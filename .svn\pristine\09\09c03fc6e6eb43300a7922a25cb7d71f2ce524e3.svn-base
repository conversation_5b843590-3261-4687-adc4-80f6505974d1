﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class MapLTECellLayerFddPropertis : MTLayerPropUserControl
    {
        private List<BandRange> rangeValues = new List<BandRange>();

        public MapLTECellLayerFddPropertis()
        {
            InitializeComponent();
        }

        private MapLTECellLayer layer = null;
        public override void Setup(object obj)
        {
            layer = obj as MapLTECellLayer;
            if (layer == null)
            {
                return;
            }
            Text = "FDD频段";

            colorLable.Color = layer.FDDSetting.ColorCellLabel;

            checkBoxDisplay.Checked = layer.FDDSetting.IsVisible;
            checkBoxDisplay.CheckedChanged += new EventHandler(checkBoxVIsible_CheckedChanged);

            colorCell.Color = layer.FDDSetting.ColorCell;
            colorCell.ColorChanged += new EventHandler(colorCell_ColorChanged);
            colorLable.ColorChanged += new EventHandler(colorLabel_ColorChanged);

            foreach (BandRange rangeValue in layer.FDDSetting.FDDRangeList)
            {
                rangeValues.Add(rangeValue);
            }
            rowCountChanged();
        }

        private void rowCountChanged()
        {
            dataGridView.RowCount = rangeValues.Count;
            dataGridView.Invalidate();
        }

        void colorCell_ColorChanged(object sender, EventArgs e)
        {
            layer.FDDSetting.ColorCell = Color.FromArgb(255, colorCell.Color);
        }

        private void buttonAdd_Click(object sender, EventArgs e)
        {
            BandRangeValueSettingBox box = new BandRangeValueSettingBox(null);
            if (box.ShowDialog() == DialogResult.OK)
            {
                rangeValues.Add(box.RangeValue);
                rowCountChanged();
                layer.FDDSetting.FDDRangeList = rangeValues;
            }
        }

        private void buttonModify_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count > 0)
            {
                BandRange rangeValue = rangeValues[dataGridView.SelectedRows[0].Index];
                BandRangeValueSettingBox box = new BandRangeValueSettingBox(rangeValue);
                if (box.ShowDialog() == DialogResult.OK)
                {
                    rangeValues.Remove(rangeValue);
                    rangeValues.Add(box.RangeValue);
                    rowCountChanged();
                    layer.FDDSetting.FDDRangeList = rangeValues;
                }
            }
        }

        private void buttonRemove_Click(object sender, EventArgs e)
        {
            rangeValues.RemoveAt(dataGridView.SelectedRows[0].Index);
            rowCountChanged();
            layer.FDDSetting.FDDRangeList = rangeValues;
        }

        private void checkBoxVIsible_CheckedChanged(object sender, EventArgs e)
        {
            layer.FDDSetting.IsVisible = checkBoxDisplay.Checked;
        }

        private void dataGridView_CellValueNeeded(object sender, DataGridViewCellValueEventArgs e)
        {
            if (e.RowIndex >= rangeValues.Count)
            {
                return;
            }
            if (e.ColumnIndex == 0)
            {
                e.Value = rangeValues[e.RowIndex].RangeDescription;
            }
        }

        private void dataGridView_SelectionChanged(object sender, EventArgs e)
        {
            checkButtonState();
        }

        private void checkButtonState()
        {
            buttonModify.Enabled = dataGridView.SelectedRows.Count == 1;
            buttonRemove.Enabled = dataGridView.SelectedRows.Count == 1;
        }

        void colorLabel_ColorChanged(object sender, EventArgs e)
        {
            layer.FDDSetting.ColorCellLabel = colorLable.Color;
        }
    }

    public class FddSetting
    {
        public List<BandRange> FDDRangeList { get; set; } = new List<BandRange>();

        public bool IsVisible { get; set; }

        public SolidBrush CellBrush
        {
            get { return new SolidBrush(ColorCell); }
        }

        public Pen CellPen
        {
            get { return new Pen(ColorCell, 2); }
        }

        /// <summary>
        /// FDD频段颜色
        /// </summary>
        public Color ColorCell{ get; set; } = Color.Aquamarine;

        public Brush BrushCellLabel
        {
            get { return new SolidBrush(ColorCellLabel); }
        }

        public Color ColorCellLabel { get; set; } = Color.Black;

        /// <summary>
        /// 根据频点判断是否是Fdd
        /// </summary>
        /// <param name="earfcn">频点</param>
        /// <returns></returns>
        public bool IsValidFdd(int earfcn)
        {
            bool result = false;
            foreach (var range in FDDRangeList)
            {
                result = range.IsValidBand(earfcn);
                if (result)
                {
                    break;
                }
            }
            return result;
        }

        //bool btsContainLTECell;
        //bool btsContainNBCell;
        //public int GetBTSCellType(LTEBTS bts)
        //{
        //    int cellType = -1;
        //    btsContainLTECell = false;
        //    btsContainNBCell = false;
        //    foreach (var cell in bts.Cells)
        //    {
        //        bool isCurNBCell = false;
        //        foreach (var range in FDDRangeList)
        //        {
        //            if (range.IsValidBand(cell.EARFCN))
        //            {
        //                isCurNBCell = true;
        //                btsContainNBCell = true;
        //                break;
        //            }
        //        }
        //        if (!isCurNBCell)
        //        {
        //            btsContainLTECell = true;
        //        }
        //    }

        //    if (btsContainLTECell && btsContainNBCell)
        //    {
        //        //同时有LTE和NB小区的站
        //        cellType = 3;
        //    }
        //    else if (btsContainNBCell)
        //    {
        //        //只有NB小区的站
        //        cellType = 1;
        //    }
        //    else if (btsContainLTECell)
        //    {
        //        //只有LTE小区的站
        //        cellType = 2;
        //    }
        //    return cellType;
        //}

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                List<object> rangeParams = new List<object>();
                param["colorCell"] = ColorCell.ToArgb();
                param["colorCellLabel"] = ColorCellLabel.ToArgb();
                param["isVisible"] = IsVisible;
                param["bandRange"] = rangeParams;
                foreach (BandRange range in FDDRangeList)
                {
                    rangeParams.Add(range.Param);
                }
                return param;
            }
            set
            {
                FDDRangeList.Clear();
                ColorCell = Color.FromArgb((int)(value["colorCell"]));
                ColorCellLabel = Color.FromArgb((int)(value["colorCellLabel"]));
                IsVisible = (bool)value["isVisible"];
                List<object> rangeParams = (List<object>)value["bandRange"];
                foreach (object o in rangeParams)
                {
                    Dictionary<string, object> rangeParam = (Dictionary<string, object>)o;
                    BandRange range = new BandRange();
                    range.Param = rangeParam;
                    FDDRangeList.Add(range);
                }
            }
        }
    }
}
