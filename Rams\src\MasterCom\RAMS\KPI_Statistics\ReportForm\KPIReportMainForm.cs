﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using GeneLau.WinFormsUI.Docking;
using MasterCom.RAMS.Stat;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    public partial class KPIReportMainForm : AMainForm
    {
        public KPIReportMainForm()
            : base()
        {
            InitializeComponent();
        }

        public override void Init()
        {
            AllEventChanged += somethingChanged;
            naviForm = CreateForm("MasterCom.RAMS.KPI_Statistics.ReportNaviForm*报表导航", false) as ReportNaviForm;
            naviForm.DockHandler.Show(this.DockPanel, DockState.DockRight);
        }

        /// <summary>
        /// 主窗体，处理“打开”、“激活”报告
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void somethingChanged(object sender, ContextEvent e)
        {
            if (sender == this)
            {
                return;
            }

            if (this.InvokeRequired)
            {
                ThreadUtil.BeginInvoke(delegate(object obj)
                {
                    somethingChanged(((object[])obj)[0], ((object[])obj)[1] as ContextEvent);
                }, new object[] { sender, e });
            }
            else if (e.Comand.Equals(AMainEventType.New))
            {
                if (e.OneParame is IList<ReportStyle>)
                {
                    foreach (ReportStyle rs in e.OneParame as IList<ReportStyle>)
                        showReportSheet(rs, true);
                }
                else if (e.OneParame is ReportStyle)
                {
                    showReportSheet(e.OneParame as ReportStyle, true);
                }
            }
            else if (e.Comand.Equals(AMainEventType.Active))
            {
                if (e.OneParame is ReportStyle)
                {
                    showReportSheet(e.OneParame as ReportStyle, false);
                }
            }
            else if (e.Comand.Equals(AMainEventType.Rename))
            {
                templateNameChanged(e.OneParame as ReportStyle);
            }
        }

        private void templateNameChanged(ReportStyle template)
        {
            foreach (IDockContent con in this.dockPanel.Documents)
            {
                KPIReportFormSheet existSheet = con as KPIReportFormSheet;
                if (existSheet != null && existSheet.OrgReport == template)
                {
                    existSheet.CurReport.name = template.name;
                    existSheet.Caption = template.name;
                    break;
                }
            }
        }

        ReportNaviForm naviForm;
        protected override DockPanel DockPanel
        {
            get { return this.dockPanel; }
        }
        private void cascadeToolStripButton_Click(object sender, EventArgs e)
        {
            Relayout(this.dockPanel, null);
        }

        private void tileVerticalToolStripButton_Click(object sender, EventArgs e)
        {
            Relayout(this.dockPanel, DockAlignment.Left);
        }

        private void tileHorizontalToolStripButton_Click(object sender, EventArgs e)
        {
            Relayout(this.dockPanel, DockAlignment.Bottom);
        }

        private void btnSaveStyle_Click(object sender, EventArgs e)
        {
            KPIReportFormSheet sheet = activeSheet;
            if (sheet == null)
            {
                return;
            }
            sheet.SaveReportTemplate(sheet.CurReport.name);
            this.naviForm.Navi(reportStyleSet, sheet.OrgReport);
        }

        private List<KPIDataGroup> dataGroup = null;
        private List<ReportStyle> reportStyleSet = null;
        internal void ShowReport(List<ReportStyle> reportSet, ReportStyle report, List<KPIDataGroup> dataGroup)
        {
            this.reportStyleSet = reportSet;
            this.dataGroup = dataGroup;
            this.naviForm.Navi(reportSet, report);
        }

        private void showReportSheet(ReportStyle report, bool create)
        {
            KPIReportFormSheet sheet = null;
            foreach (IDockContent con in this.dockPanel.Documents)
            {
                KPIReportFormSheet existSheet = con as KPIReportFormSheet;
                if (existSheet != null && existSheet.CurReport.name == report.name)
                {
                    sheet = existSheet;
                    break;
                }
            }
            if (sheet == null)
            {
                if (create)
                {
                    sheet = new KPIReportFormSheet();
                    sheet.ShowReport(report, dataGroup);
                }
                else
                {//忽略，激活未打开报告。
                    return;
                }
            }
            sheet.Show(this.dockPanel);
        }

        private ReportStyle activeReport
        {
            get
            {
                KPIReportFormSheet sheet = activeSheet;
                if (sheet == null)
                {
                    return null;
                }
                return sheet.CurReport;
            }
        }

        private KPIReportFormSheet activeSheet
        {
            get
            {
                KPIReportFormSheet sheet = dockPanel.ActiveDocument as KPIReportFormSheet;
                return sheet;
            }
        }

        private void dockPanel_ActiveDocumentChanged(object sender, EventArgs e)
        {
            ReportStyle report = activeReport;
            if (report == null)
            {
                return;
            }
            this.FireAllEventChanged(this, AMainEventType.Active, report);
        }

        private void tsBtnNewReport_Click(object sender, EventArgs e)
        {
            List<string> names = new List<string>();
            foreach (ReportStyle rpt in reportStyleSet)
            {
                names.Add(rpt.name);
            }

            NewReportDlg dlg = new NewReportDlg(names);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            ReportStyle newRpt = dlg.Report;
            reportStyleSet.Add(newRpt);
            ShowReport(reportStyleSet, newRpt, dataGroup);
        }

        private void btnSaveAs_Click(object sender, EventArgs e)
        {
            List<string> names = new List<string>();
            foreach (ReportStyle rpt in reportStyleSet)
            {
                names.Add(rpt.name);
            }
            TextInputBox namedBox = new TextInputBox("另存为", "新报表名称", activeReport.name);
            while (true)
            {
                if (namedBox.ShowDialog(this) == DialogResult.OK)
                {
                    if (!KPIReportManager.Instance.IsReportCanEdit(namedBox.TextInput))
                    {
                        continue;
                    }

                    if (!names.Contains(namedBox.TextInput))
                    {
                        ReportStyle rpt = activeReport.copyInstance();
                        rpt.name = namedBox.TextInput;
                        rpt.Save();
                        reportStyleSet.Add(rpt);
                        this.naviForm.Navi(reportStyleSet, rpt);
                        break;
                    }
                }
                else
                {
                    break;
                }
            }
        }

        private void btnDelStyle_Click(object sender, EventArgs e)
        {
            //
        }

        private void btnExport2ExcelSimple_Click(object sender, EventArgs e)
        {
            KPIReportFormSheet sheet=this.activeSheet;
            if (sheet==null)
            {
                return;
            }
            sheet.Export2Xls();
        }

        private void tsBtnXlsDetails_Click(object sender, EventArgs e)
        {
            KPIReportFormSheet sheet = this.activeSheet;
            if (sheet == null)
            {
                return;
            }
            sheet.Export2XlsDetails();
        }

        private void tsBtnAutoWidth_Click(object sender, EventArgs e)
        {
            KPIReportFormSheet sheet = this.activeSheet;
            if (sheet == null)
            {
                return;
            }
            sheet.BestFitColWidth();
        }

        private void tsBtnExcelToXml_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.InitialDirectory = Application.StartupPath;
            dlg.Filter = string.Format("{0}|{1}", FilterHelper.Xls, FilterHelper.Xlsx);
            dlg.RestoreDirectory = true;
            dlg.FilterIndex = 2;

            if (dlg.ShowDialog() == DialogResult.OK)
            {
                WaitBox.Show("正在转换格式...", excelToXml, dlg.FileName);

                if (reportStyleSet.Count > 0)
                    ShowReport(reportStyleSet, reportStyleSet[reportStyleSet.Count - 1], dataGroup);
            }
        }

        private void excelToXml(object o)
        {
            string filePath = o as string;
            ExcelOpHelper excel = ExcelOpHelper.Instance;
            try
            {
                excel.Load(filePath);

                List<string> sheets = excel.SheetNames;
                if (sheets.Count == 0)
                    return;

                ReportStyle rptStyle = new ReportStyle();
                rptStyle.name = string.Format("xls_{0}_{1:yyyyMMddHHmmss}",
                    System.IO.Path.GetFileNameWithoutExtension(filePath),
                    DateTime.Now);
                List<XlsRow> rows = excel.GetRows(sheets[0]);
                int rowCnt = 0;
                foreach (XlsRow row in rows)
                {
                    rowCnt++;
                    foreach (XlsCell cell in row.Cells)
                    {
                        RptCell rptCell = new RptCell();
                        rptCell.rowAt = row.RowIndex - 1;
                        rptCell.colAt = cell.CellIndex - 1;
                        rptCell.exp = cell.CellValue;
                        rptCell.bkColor = cell.CellBakColor;
                        rptCell.foreColor = cell.CellFontColor;

                        rptStyle.AddCell(rptCell);
                    }
                    WaitBox.ProgressPercent = (100 * rowCnt / rows.Count);
                }
                reportStyleSet.Add(rptStyle);
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(string.Format("转换失败...\r\n{0}", ex.StackTrace));
            }
            finally
            {
                excel.Release();
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        private void tsBtnXlsFormula_Click(object sender, EventArgs e)
        {
            KPIReportFormSheet sheet = this.activeSheet;
            if (sheet == null)
            {
                return;
            }
            sheet.Export2XlsFormula();
        }
    }
}
