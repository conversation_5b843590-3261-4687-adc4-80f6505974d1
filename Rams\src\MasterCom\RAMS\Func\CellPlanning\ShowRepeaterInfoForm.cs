﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class ShowRepeaterInfoForm : ShowFuncForm
    {
        public ShowRepeaterInfoForm(MainModel mm)
            : base(mm)
        { }
        FindRepeaterForm form = null;
        protected override void showForm()
        {
            if (form == null || form.IsDisposed)
            {
                form = new FindRepeaterForm(MainModel);
            }
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
            form.BringToFront();
        }

        public override string Name
        {
            get { return "呈现直放站信息窗口"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19017, this.Name);
        }
    }
}
