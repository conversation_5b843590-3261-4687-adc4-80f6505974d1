﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraGrid.Views.Grid;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Grid;
using MasterCom.MTGis;
using MapWinGIS;

namespace MasterCom.RAMS.ZTFunc.ZTTestPointOverMuchGrid
{
    public partial class TestPointOverMuchResultForm : MinCloseForm
    {
        public TestPointOverMuchResultForm()
            : base()
        {
            InitializeComponent();
            editSampleCount.ValueChanged += EditSampleCount_ValueChanged;
            numDuration.ValueChanged += numDuration_ValueChanged;
            numDistance.ValueChanged += numDistance_ValueChanged;
            gridView2.DoubleClick += GridRow_DoubleClick;
        }

        void numDistance_ValueChanged(object sender, EventArgs e)
        {
            refresh();
        }

        private void refresh()
        {
            RefreshData();
            ShowAll();
        }

        void numDuration_ValueChanged(object sender, EventArgs e)
        {
            refresh();
        }

        public void FillData(List<TestPointOverMuchFileInfo> fileInfoList)
        {
            //设置显示列表的数据
            this.fileInfoList = new List<TestPointOverMuchFileInfo>(fileInfoList);
            RefreshData();
        }

        private void RefreshData()
        {
            int minPointCount = (int)editSampleCount.Value;
            int duration = (int)numDuration.Value;
            double distance = (double)numDistance.Value;
            List<TestPointOverMuchFileInfo> tmpList = new List<TestPointOverMuchFileInfo>();
            List<TestPointOverMuchGridInfo> grids = new List<TestPointOverMuchGridInfo>();
            int idx = 1;
            foreach (TestPointOverMuchFileInfo fileInfo in this.fileInfoList)
            {
                fileInfo.SetFilter(minPointCount, duration, distance);
                if (fileInfo.FilterGridCount > 0)
                {
                    tmpList.Add(fileInfo);
                    grids.AddRange(fileInfo.FilterGridList);
                    foreach (TestPointOverMuchGridInfo g in fileInfo.FilterGridList)
                    {
                        g.SerialNm = idx++;
                    }
                }
            }

            gridControl1.DataSource = tmpList;
            gridControl1.RefreshDataSource();

            TestPointOverMuchGridLayer layer = MainModel.MainForm.GetMapForm().GetLayerBase(typeof(TestPointOverMuchGridLayer))
                as TestPointOverMuchGridLayer;
            layer.Grids = grids;
            layer.SelGrid = null;
        }

        private void EditSampleCount_ValueChanged(object sender, EventArgs e)
        {
            refresh();
        }

        private void GridRow_DoubleClick(object sender, EventArgs e)
        {
            GridView gv = sender as GridView;
            object row = gv.GetRow(gv.GetSelectedRows()[0]);
            TestPointOverMuchGridInfo grid = row as TestPointOverMuchGridInfo;
            if (grid == null)
            {
                return;
            }
            TestPointOverMuchGridLayer layer = MainModel.MainForm.GetMapForm().GetLayerBase(typeof(TestPointOverMuchGridLayer))
                 as TestPointOverMuchGridLayer;
            layer.SelGrid = grid;
            MainModel.GetInstance().MainForm.GetMapForm().GoToView(grid.CenterLng, grid.CenterLat, 2000);
        }

        private List<TestPointOverMuchFileInfo> fileInfoList;
        private void miExp2Xls_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.Filter = FilterHelper.ExcelX;
            dlg.RestoreDirectory = true;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                gridControl1.ExportToXlsx(dlg.FileName);
            }
            MessageBox.Show("导出成功!");
        }

        private void miExp2XlsLite_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("地市");
            row.AddCellValue("文件");
            row.AddCellValue("业务类型");
            row.AddCellValue("测试开始时间");
            row.AddCellValue("测试结束时间");
            row.AddCellValue("总栅格个数");
            row.AddCellValue(string.Format("满足采样点个数≥{0}且时长≥{1}且里程≥{2}的栅格个数", this.editSampleCount.Value
                , this.numDuration.Value, this.numDistance.Value));
            row.AddCellValue("满足条件栅格占比(%)");//file attribute

            row.AddCellValue("序号");
            row.AddCellValue("网格");
            row.AddCellValue("采样点个数");
            row.AddCellValue("时长(秒)");
            row.AddCellValue("里程(米)");
            row.AddCellValue("栅格左上经度");
            row.AddCellValue("栅格左上纬度");
            rows.Add(row);//title end

            List<TestPointOverMuchFileInfo> list = gridControl1.DataSource as List<TestPointOverMuchFileInfo>;
            foreach (TestPointOverMuchFileInfo file in list)
            {
                row = new NPOIRow();
                string cityName = DistrictManager.GetInstance().getDistrictName(file.FileInfo.DistrictID);
                row.AddCellValue(cityName);
                row.AddCellValue(file.FileName);
                row.AddCellValue(file.ServiceTypeDescription);
                row.AddCellValue(file.BeginTimeString);
                row.AddCellValue(file.EndTimeString);
                row.AddCellValue(file.TotalGridCount);
                row.AddCellValue(file.FilterGridCount);
                row.AddCellValue(Math.Round(file.GridRate * 100.0, 2));

                foreach (TestPointOverMuchGridInfo grid in file.FilterGridList)
                {
                    NPOIRow subRow = new NPOIRow();
                    subRow.AddCellValue(grid.SerialNm.ToString());
                    subRow.AddCellValue(GISManager.GetInstance().GetGridDesc(grid.LTLng, grid.LTLat));
                    subRow.AddCellValue(grid.TestPointCount);
                    subRow.AddCellValue(grid.Duration_Sec);
                    subRow.AddCellValue(grid.Distance);
                    subRow.AddCellValue(grid.LTLng);
                    subRow.AddCellValue(grid.LTLat);

                    row.subRows.Add(subRow);
                }
                rows.Add(row);
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        public void showAllGrid(object sender, EventArgs e)
        {
            this.ShowAll();
        }

        public void ShowAll()
        {
            if (this.fileInfoList == null || this.fileInfoList.Count <= 0)
            {
                return;
            }
            DbRect bounds = null;
            foreach (TestPointOverMuchFileInfo fi in this.fileInfoList)
            {
                foreach (TestPointOverMuchGridInfo grid in fi.FilterGridList)
                {
                    if (bounds == null)
                    {
                        bounds = grid.Bounds;
                    }
                    else
                    {
                        bounds.MergeRects(grid.Bounds);
                    }
                }
            }
            if (bounds != null)
            {
                MainModel.MainForm.GetMapForm().GoToView(bounds);
            }
        }

        private void miExportShp_Click(object sender, EventArgs e)
        {
            SaveFileDialog sfd = new SaveFileDialog();
            sfd.Filter = FilterHelper.Shp;
            if (sfd.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return;
            }
            string fileName = sfd.FileName;
            if (!ShapeHelper.DeleteShpFile(fileName))
            {
                MessageBox.Show("文件：" + fileName + " 已被其他程序占用。");
                return;
            }
            Shapefile shpFile = new Shapefile();
            if (!shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POLYGON))
            {
                MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                return;
            }

            //设置字段序号
            int tem = 0;
            int serialNo = tem++;
            int fileNameNo = tem++;
            int gridCountNo = tem++;
            int conditionGridCountNo = tem++;
            int gridRateNo = tem++;
            int svcType = tem++;
            int beginTimeNo = tem++;
            int endTimeNo = tem++;
            int testPointCountNo = tem++;
            int durationSecondNo = tem++;
            int distanceNo = tem++;
            int ltLng = tem++;
            int ltLat = tem;
            //表格字段
            ShapeHelper.InsertNewField(shpFile, "序号", FieldType.INTEGER_FIELD, 10, 30, ref serialNo);
            ShapeHelper.InsertNewField(shpFile, "文件名", FieldType.STRING_FIELD, 10, 30, ref fileNameNo);
            ShapeHelper.InsertNewField(shpFile, "总栅格数", FieldType.INTEGER_FIELD, 10, 30, ref gridCountNo);
            ShapeHelper.InsertNewField(shpFile, "条件栅格数", FieldType.INTEGER_FIELD, 10, 30, ref conditionGridCountNo);
            ShapeHelper.InsertNewField(shpFile, "栅格占比", FieldType.DOUBLE_FIELD, 10, 30, ref gridRateNo);
            ShapeHelper.InsertNewField(shpFile, "业务类型", FieldType.STRING_FIELD, 10, 30, ref svcType);
            ShapeHelper.InsertNewField(shpFile, "开始时间", FieldType.STRING_FIELD, 10, 30, ref beginTimeNo);
            ShapeHelper.InsertNewField(shpFile, "结束时间", FieldType.STRING_FIELD, 10, 30, ref endTimeNo);
            ShapeHelper.InsertNewField(shpFile, "采样点数", FieldType.INTEGER_FIELD, 10, 30, ref testPointCountNo);
            ShapeHelper.InsertNewField(shpFile, "时长（秒）", FieldType.DOUBLE_FIELD, 10, 30, ref durationSecondNo);
            ShapeHelper.InsertNewField(shpFile, "里程（米）", FieldType.DOUBLE_FIELD, 10, 30, ref distanceNo);
            ShapeHelper.InsertNewField(shpFile, "左上经度", FieldType.DOUBLE_FIELD, 10, 30, ref ltLng);
            ShapeHelper.InsertNewField(shpFile, "左上纬度", FieldType.DOUBLE_FIELD, 10, 30, ref ltLat);
            int shpIdx = -1;
            foreach (TestPointOverMuchFileInfo fi in this.fileInfoList)
            {
                foreach (TestPointOverMuchGridInfo grid in fi.FilterGridList)
                {
                    //创建几何体
                    shpIdx++;
                    MapWinGIS.Shape spBase = ShapeHelper.CreateRectShape(grid.LTLng, grid.LTLat, grid.BRLng, grid.BRLat);
                    shpFile.EditInsertShape(spBase, ref shpIdx);
                    shpFile.EditCellValue(serialNo, shpIdx, grid.SerialNm);
                    shpFile.EditCellValue(fileNameNo, shpIdx, fi.FileName);
                    shpFile.EditCellValue(gridCountNo, shpIdx, fi.TotalGridCount);
                    shpFile.EditCellValue(conditionGridCountNo, shpIdx, fi.FilterGridCount);
                    shpFile.EditCellValue(gridRateNo, shpIdx, fi.GridRate);
                    shpFile.EditCellValue(svcType, shpIdx, fi.ServiceTypeDescription);
                    shpFile.EditCellValue(beginTimeNo, shpIdx, fi.BeginTimeString);
                    shpFile.EditCellValue(endTimeNo, shpIdx, fi.EndTimeString);
                    shpFile.EditCellValue(testPointCountNo, shpIdx, grid.TestPointCount);
                    shpFile.EditCellValue(durationSecondNo, shpIdx, grid.Duration_Sec);
                    shpFile.EditCellValue(distanceNo, shpIdx, grid.Distance);
                    shpFile.EditCellValue(ltLng, shpIdx, grid.LTLng);
                    shpFile.EditCellValue(ltLat, shpIdx, grid.LTLat);
                }
            }
            try
            {
                shpFile.SaveAs(fileName, null);
            }
            catch (System.Exception ex)
            {
                MessageBox.Show("保存文件失败！" + ex.Message);
            }
            finally
            {
                shpFile.Close();
            }
        }

    }

}
