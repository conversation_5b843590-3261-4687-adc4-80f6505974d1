﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;

namespace MasterCom.RAMS.Model
{
    public static class PasswordFormatCheck
    {
        /// <summary>
        /// 检查密码格式是否有效（有效则返回空值，无效时返回原因）
        /// </summary>
        /// <param name="loginName"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        public static string CheckPwdFormatValid(string loginName, string password)
        {
#if LoginManage
            LoginValues loginCfg = MainModel.GetInstance().LoginManageCfg;
            if (loginCfg.PwdSuperLimit)
            {
                StringBuilder strbWarns = new StringBuilder();
                saveWarn(strbWarns, checkPwdComplex(password, loginCfg.PwdMinCharCount, loginCfg.PwdMinCharKindCount));//检查密码复杂度

                string passwordLower = password.ToLower();// 以下转为小写进行检查
                saveWarn(strbWarns, isContainsUserName(loginName, passwordLower));//检查密码是否与用户名有关
                saveWarn(strbWarns, isContainsTime(passwordLower));//检查密码是否包含年月日
                saveWarn(strbWarns, isContainsPhoneNum(passwordLower));//检查密码是否包含手机号
                saveWarn(strbWarns, isContainsSeriesKeyboardChars(passwordLower));//检查密码是否包含键盘上的连续字符
                saveWarn(strbWarns, isContainsSeriesChars(passwordLower));//检查密码是否包含连续字符或重复字符

                return strbWarns.ToString().Trim();
            }
            else
            {
                return checkPwdComplex(password, loginCfg.PwdMinCharCount, loginCfg.PwdMinCharKindCount);//只检查密码复杂度
            }
#else
            return string.Empty;
#endif
        }
#if LoginManage
        private static void saveWarn(StringBuilder strbWarns, string strWarn)
        {
            if (string.IsNullOrEmpty(strWarn))
            {
                return;
            }
            strbWarns.AppendLine(strWarn);
        }

        /// <summary>
        /// 检查密码复杂度（长度及包含多少种字符）
        /// </summary>
        /// <param name="password">密码</param>
        /// <param name="pwdLengthMin">密码最小字符数（长度）</param>
        /// <param name="charKindCountMin">密码字符最少种类数（最小为1，最大为4）</param>
        /// <returns></returns>
        private static string checkPwdComplex(string password, int pwdLengthMin, int charKindCountMin)
        {
            string strWarn = string.Empty;

            /* 密码字符包括：小写字母、大写字母、数字、符号等；
            这个正则会得到五个捕获组，前四个捕获组会告诉我们这个字符串包含有多少种组合（返回多少个匹配代表多少种组合）
            如果这个字符串小于 pwdLengthMin 位的话,则会得到第五个捕获组,长度为1（即强度为1），
            如果没有输入，就连捕获组5都不会得到（强度为0）*/
            int charKindCount = System.Text.RegularExpressions.Regex.Replace(password
                , "^(?:([a-z])|([A-Z])|([0-9])|(.)){" + pwdLengthMin + ",}|(.)+$", "$1$2$3$4$5").Length;

            if (charKindCount < charKindCountMin || (charKindCount == 1 && password.Length < pwdLengthMin))
            {
                if (charKindCountMin >= 4)
                {
                    strWarn = string.Format("密码需至少有{0}位字符,并同时包含大写字母、小写字母、数字和特殊符号!"
                        , pwdLengthMin);
                }
                else
                {
                    strWarn = string.Format("密码需至少有{0}位字符,并至少包含大写字母、小写字母、数字和特殊符号4类中的{1}类!"
                        , pwdLengthMin, charKindCountMin);
                }
            }
            return strWarn;
        }

        private static string isContainsUserName(string loginName, string password)
        {
            string strWarn = string.Empty;
            if (password.Contains(loginName.ToLower()))// 检查密码是否包括用户账号，暂不考虑账号反写校验
            {
                strWarn = "密码不能包含用户名或其形似变换字符串！";
            }
            else
            {
                // 近似字符替换后再检查密码是否包括用户账号
                string similarPassword = password.Replace("1", "l").Replace("0", "o").Replace("8", "b").Replace("@", "a");
                if (similarPassword.Contains(loginName.ToLower()))
                {
                    strWarn = "密码不能包含用户名或其形似变换字符串！";
                }
            }
            return strWarn;
        }

        private static string isContainsTime(string password)
        {
            string strWarn = string.Empty;
            // 检查密码是否包含生日：年月、月日、年月日，暂不考虑闰年2月29日
            if (Regex.IsMatch(password, ".*(19|20)\\d{2}(0[1-9]|1[0-2]).*") // 包含年月
                || Regex.IsMatch(password, ".*((01|03|05|07|08|10|12)([0-2][1-9]|3[0-1])|(04|06|09|11)([0-2][1-9]|30)|(02)([0-1][1-9]|2[0-8])).*") // 包含月日
                || Regex.IsMatch(password, ".*(19|20)\\d{2}((01|03|05|07|08|10|12)([0-2][1-9]|3[0-1])|(04|06|09|11)([0-2][1-9]|30)|(02)([0-1][1-9]|2[0-8])).*"))// 包含年月日
            {
                strWarn = "密码不能包含年月、月日或年月日格式的字符串！";
            }
            return strWarn;
        }

        private static string isContainsPhoneNum(string password)
        {
            string strWarn = string.Empty;
            // 检查密码是否包含手机号码
            if (password.Length >= 11 && Regex.IsMatch(password, ".*1[3|4|5|8][0-9]\\d{8}.*"))
            {
                strWarn = "密码不能包含手机号码！";
            }
            return strWarn;
        }

        private static string isContainsSeriesKeyboardChars(string password)
        {
            string strWarn = string.Empty;
            // 检查密码是否包含3位以上（含3位）键盘排序密码
            foreach (string str in keyboard)
            {
                if (password.Contains(str))
                {
                    strWarn = "密码不能包含3位以上键盘相邻字符！(错误提示：" + str + ")";
                    break;
                }
            }
            return strWarn;
        }

        /// <summary>
        /// 检查密码是否包含3位以上（含三位）连续字符或3位以上（含三位）重复字符
        /// </summary>
        /// <param name="password"></param>
        /// <returns></returns>
        private static string isContainsSeriesChars(string password)
        {
            string sequentialLetter = "abcdefghijklmnopqrstuvwxyz";
            string sequentialNumber = "01234567890";
            string sequentialChar = "~!@#$%^&*()_+[]\\{}|,./<>?";
            for (int i = 0; i < password.Length - 2; i++)
            {
                // 检查三位相同字母
                string curStr = password.Substring(i, 1);
                string strSame = curStr + curStr + curStr;
                if (password.Contains(strSame))
                {
                    return "密码不能包含3位以上重复字符或连续字符！";
                }

                // 检查连续三位字母
                string strSeries = password.Substring(i, 3);
                if (sequentialLetter.Contains(strSeries) || sequentialNumber.Contains(strSeries)
                    || sequentialChar.Contains(strSeries))
                {
                    return "密码不能包含3位以上重复字符或连续字符！";
                }
            }
            return string.Empty;
        }

        // 键盘连续字符
        static readonly string[] keyboard = new string[] { "1qa", "2ws", "3ed", "4rf", "5tg", "6yh", "7uj", "8ik", "9ol", "0p;",
                        "qaz", "wsx", "edc", "rfv", "tgb", "yhn", "ujm", "ik,", "ol.", "!QA", "@WS", "#ED", "$RF", "%TG", "^YH",
                        "&UJ", "*IK", "(OL", ")P:", "123", "234", "345", "456", "567", "678", "789", "890", "90-", "0-=", "qwe",
                        "wer", "ert", "rty", "tyu", "yui", "uio", "iop", "op[", "p[]", "asd", "sdf", "dfg", "fgh", "ghj", "hjk",
                        "jkl", "kl;", "l;\"", "zxc", "xcv", "cvb", "vbn", "bnm", "nm,", "m,.", "!@#", "@#$", "#$%", "$%^", "%^&",
                        "^&*", "&*(", "*()", "()_", ")_+"};
#endif
    }
}