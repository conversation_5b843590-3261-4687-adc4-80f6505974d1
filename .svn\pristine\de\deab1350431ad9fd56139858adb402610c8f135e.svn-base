using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Drawing;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
namespace MasterCom.RAMS.Func
{
    [System.ComponentModel.ToolboxItem(false)]
    public partial class MapWCellLayerAntennaProperties : MTLayerPropUserControl
    {
        public MapWCellLayerAntennaProperties()
        {
            InitializeComponent();
        }

        public override void Setup(Object obj)
        {
            if (obj == null)
            {
                return;
            }
            Text = "WCDMA Antenna";
            layer = (MapWCellLayer)obj;
            checkBoxDisplay.Checked = layer.DrawAntenna;
            labelColorAntenna.BackColor = layer.ColorAntenna;
            TrackBarOpacity.Value = labelColorAntenna.BackColor.A;

            cbxDrawAntennaLabel.Checked = layer.DrawAntennaLabel;
            groupBox1.Enabled = layer.DrawAntennaLabel;
            cbxAntennaGroup.Checked = layer.DrawAntennaGroup;
            cbxAntennaSN.Checked = layer.DrawAntennaSN;
            cbxAntennaLongitude.Checked = layer.DrawAntennaLongitude;
            cbxAntennaLatitude.Checked = layer.DrawAntennaLatitude;
            cbxAntennaDirectionType.Checked = layer.DrawAntennaDirectionType;
            cbxAntennaDirection.Checked = layer.DrawAntennaDirection;
            cbxAntennaDownward.Checked = layer.DrawAntennaDownward;
            cbxAntennaAltitude.Checked = layer.DrawAntennaAltitude;
            cbxAntennaDescription.Checked = layer.DrawAntennaDescription;
        }

        private void checkBoxDisplay_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntenna = checkBoxDisplay.Checked;
        }

        private void labelColor_Click(object sender, EventArgs e)
        {
            colorDialog.Color = labelColorAntenna.BackColor;
            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                labelColorAntenna.BackColor = Color.FromArgb(TrackBarOpacity.Value, colorDialog.Color);
                layer.ColorAntenna = labelColorAntenna.BackColor;
                layer.RefreshBrushes();
            }
        }

        private void TrackBarOpacity_Scroll(object sender, EventArgs e)
        {
            labelColorAntenna.BackColor = Color.FromArgb(TrackBarOpacity.Value, labelColorAntenna.BackColor);
            layer.ColorAntenna = labelColorAntenna.BackColor;
            layer.RefreshBrushes();
        }

        private void cbxDrawAntennaLabel_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaLabel = cbxDrawAntennaLabel.Checked;
            btnFont.Enabled = cbxDrawAntennaLabel.Checked;
            groupBox1.Enabled = cbxDrawAntennaLabel.Checked;
        }

        private void btnFont_Click(object sender, EventArgs e)
        {
            fontDialog.Font = layer.FontAntennaLabel;
            if (fontDialog.ShowDialog(this) == DialogResult.OK)
            {
                layer.FontAntennaLabel = fontDialog.Font;
            }
        }

        private void cbxAntennaGroup_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaGroup = cbxAntennaGroup.Checked;
        }

        private void cbxAntennaSN_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaSN = cbxAntennaSN.Checked;
        }

        private void cbxAntennaLongitude_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaLongitude = cbxAntennaLongitude.Checked;
        }

        private void cbxAntennaLatitude_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaLatitude = cbxAntennaLatitude.Checked;
        }

        private void cbxAntennaDirectionType_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaDirectionType = cbxAntennaDirectionType.Checked;
        }

        private void cbxAntennaDirection_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaDirection = cbxAntennaDirection.Checked;
        }

        private void cbxAntennaDowardE_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaDownwardE = cbxAntennaDowardE.Checked;
        }

        private void cbxAntennaDowardM_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaDownwardM = cbxAntennaDowardM.Checked;
        }

        private void cbxAntennaDownward_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaDownward = cbxAntennaDownward.Checked;
        }

        private void cbxAntennaAltitude_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaAltitude = cbxAntennaAltitude.Checked;
        }

        private void cbxAntennaDescription_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawAntennaDescription = cbxAntennaDescription.Checked;
        }

        private void InitializeComponent()
        {
            System.Windows.Forms.Label LabelOpacity;
            System.Windows.Forms.Label labelColor;
            System.Windows.Forms.Label labelColorLabelAntenna;
            System.Windows.Forms.Label label0;
            System.Windows.Forms.Label label100;
            this.TrackBarOpacity = new System.Windows.Forms.TrackBar();
            this.labelColorAntenna = new System.Windows.Forms.Label();
            this.checkBoxDisplay = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbxAntennaDescription = new System.Windows.Forms.CheckBox();
            this.cbxAntennaAltitude = new System.Windows.Forms.CheckBox();
            this.cbxAntennaDownward = new System.Windows.Forms.CheckBox();
            this.cbxAntennaDirection = new System.Windows.Forms.CheckBox();
            this.cbxAntennaDirectionType = new System.Windows.Forms.CheckBox();
            this.cbxAntennaLatitude = new System.Windows.Forms.CheckBox();
            this.cbxAntennaLongitude = new System.Windows.Forms.CheckBox();
            this.cbxAntennaSN = new System.Windows.Forms.CheckBox();
            this.cbxAntennaGroup = new System.Windows.Forms.CheckBox();
            this.btnFont = new System.Windows.Forms.Button();
            this.cbxDrawAntennaLabel = new System.Windows.Forms.CheckBox();
            this.cbxAntennaDowardM = new System.Windows.Forms.CheckBox();
            this.cbxAntennaDowardE = new System.Windows.Forms.CheckBox();
            LabelOpacity = new System.Windows.Forms.Label();
            labelColor = new System.Windows.Forms.Label();
            labelColorLabelAntenna = new System.Windows.Forms.Label();
            label0 = new System.Windows.Forms.Label();
            label100 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // LabelOpacity
            // 
            LabelOpacity.Location = new System.Drawing.Point(3, 166);
            LabelOpacity.Name = "LabelOpacity";
            LabelOpacity.Size = new System.Drawing.Size(56, 16);
            LabelOpacity.TabIndex = 8;
            LabelOpacity.Text = "Opacity: ";
            // 
            // labelColor
            // 
            labelColor.Location = new System.Drawing.Point(3, 26);
            labelColor.Name = "labelColor";
            labelColor.Size = new System.Drawing.Size(37, 20);
            labelColor.TabIndex = 5;
            labelColor.Text = "Color:";
            labelColor.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // labelColorLabelAntenna
            // 
            labelColorLabelAntenna.AutoSize = true;
            labelColorLabelAntenna.Location = new System.Drawing.Point(3, 52);
            labelColorLabelAntenna.Name = "labelColorLabelAntenna";
            labelColorLabelAntenna.Size = new System.Drawing.Size(47, 12);
            labelColorLabelAntenna.TabIndex = 36;
            labelColorLabelAntenna.Text = "Antenna";
            labelColorLabelAntenna.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label0
            // 
            label0.AutoSize = true;
            label0.Location = new System.Drawing.Point(54, 187);
            label0.Name = "label0";
            label0.Size = new System.Drawing.Size(17, 12);
            label0.TabIndex = 40;
            label0.Text = "0%";
            label0.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label100
            // 
            label100.AutoSize = true;
            label100.Location = new System.Drawing.Point(138, 187);
            label100.Name = "label100";
            label100.Size = new System.Drawing.Size(29, 12);
            label100.TabIndex = 41;
            label100.Text = "100%";
            label100.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // TrackBarOpacity
            // 
            this.TrackBarOpacity.LargeChange = 32;
            this.TrackBarOpacity.Location = new System.Drawing.Point(49, 155);
            this.TrackBarOpacity.Maximum = 255;
            this.TrackBarOpacity.Name = "TrackBarOpacity";
            this.TrackBarOpacity.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.TrackBarOpacity.Size = new System.Drawing.Size(115, 45);
            this.TrackBarOpacity.TabIndex = 9;
            this.TrackBarOpacity.TickFrequency = 32;
            this.TrackBarOpacity.Value = 255;
            this.TrackBarOpacity.Scroll += new System.EventHandler(this.TrackBarOpacity_Scroll);
            // 
            // labelColorAntenna
            // 
            this.labelColorAntenna.BackColor = System.Drawing.Color.Red;
            this.labelColorAntenna.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.labelColorAntenna.Location = new System.Drawing.Point(58, 47);
            this.labelColorAntenna.Name = "labelColorAntenna";
            this.labelColorAntenna.Size = new System.Drawing.Size(25, 25);
            this.labelColorAntenna.TabIndex = 35;
            this.labelColorAntenna.Click += new System.EventHandler(this.labelColor_Click);
            // 
            // checkBoxDisplay
            // 
            this.checkBoxDisplay.AutoSize = true;
            this.checkBoxDisplay.Location = new System.Drawing.Point(6, 7);
            this.checkBoxDisplay.Name = "checkBoxDisplay";
            this.checkBoxDisplay.Size = new System.Drawing.Size(66, 16);
            this.checkBoxDisplay.TabIndex = 42;
            this.checkBoxDisplay.Text = "Display";
            this.checkBoxDisplay.UseVisualStyleBackColor = true;
            this.checkBoxDisplay.CheckedChanged += new System.EventHandler(this.checkBoxDisplay_CheckedChanged);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.cbxAntennaDowardE);
            this.groupBox1.Controls.Add(this.cbxAntennaDowardM);
            this.groupBox1.Controls.Add(this.cbxAntennaDescription);
            this.groupBox1.Controls.Add(this.cbxAntennaAltitude);
            this.groupBox1.Controls.Add(this.cbxAntennaDownward);
            this.groupBox1.Controls.Add(this.cbxAntennaDirection);
            this.groupBox1.Controls.Add(this.cbxAntennaDirectionType);
            this.groupBox1.Controls.Add(this.cbxAntennaLatitude);
            this.groupBox1.Controls.Add(this.cbxAntennaLongitude);
            this.groupBox1.Controls.Add(this.cbxAntennaSN);
            this.groupBox1.Controls.Add(this.cbxAntennaGroup);
            this.groupBox1.Enabled = false;
            this.groupBox1.Location = new System.Drawing.Point(179, 6);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(130, 225);
            this.groupBox1.TabIndex = 43;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "Display Index";
            // 
            // cbxAntennaDescription
            // 
            this.cbxAntennaDescription.AutoSize = true;
            this.cbxAntennaDescription.Location = new System.Drawing.Point(15, 185);
            this.cbxAntennaDescription.Name = "cbxAntennaDescription";
            this.cbxAntennaDescription.Size = new System.Drawing.Size(90, 16);
            this.cbxAntennaDescription.TabIndex = 10;
            this.cbxAntennaDescription.Text = "Description";
            this.cbxAntennaDescription.UseVisualStyleBackColor = true;
            this.cbxAntennaDescription.CheckedChanged += new System.EventHandler(this.cbxAntennaDescription_CheckedChanged);
            // 
            // cbxAntennaAltitude
            // 
            this.cbxAntennaAltitude.AutoSize = true;
            this.cbxAntennaAltitude.Location = new System.Drawing.Point(15, 169);
            this.cbxAntennaAltitude.Name = "cbxAntennaAltitude";
            this.cbxAntennaAltitude.Size = new System.Drawing.Size(72, 16);
            this.cbxAntennaAltitude.TabIndex = 9;
            this.cbxAntennaAltitude.Text = "Altitude";
            this.cbxAntennaAltitude.UseVisualStyleBackColor = true;
            this.cbxAntennaAltitude.CheckedChanged += new System.EventHandler(this.cbxAntennaAltitude_CheckedChanged);
            // 
            // cbxAntennaDownward
            // 
            this.cbxAntennaDownward.AutoSize = true;
            this.cbxAntennaDownward.Location = new System.Drawing.Point(15, 153);
            this.cbxAntennaDownward.Name = "cbxAntennaDownward";
            this.cbxAntennaDownward.Size = new System.Drawing.Size(72, 16);
            this.cbxAntennaDownward.TabIndex = 8;
            this.cbxAntennaDownward.Text = "Downward";
            this.cbxAntennaDownward.UseVisualStyleBackColor = true;
            this.cbxAntennaDownward.CheckedChanged += new System.EventHandler(this.cbxAntennaDownward_CheckedChanged);
            // 
            // cbxAntennaDirection
            // 
            this.cbxAntennaDirection.AutoSize = true;
            this.cbxAntennaDirection.Location = new System.Drawing.Point(15, 105);
            this.cbxAntennaDirection.Name = "cbxAntennaDirection";
            this.cbxAntennaDirection.Size = new System.Drawing.Size(78, 16);
            this.cbxAntennaDirection.TabIndex = 5;
            this.cbxAntennaDirection.Text = "Direction";
            this.cbxAntennaDirection.UseVisualStyleBackColor = true;
            this.cbxAntennaDirection.CheckedChanged += new System.EventHandler(this.cbxAntennaDirection_CheckedChanged);
            // 
            // cbxAntennaDirectionType
            // 
            this.cbxAntennaDirectionType.AutoSize = true;
            this.cbxAntennaDirectionType.Location = new System.Drawing.Point(15, 89);
            this.cbxAntennaDirectionType.Name = "cbxAntennaDirectionType";
            this.cbxAntennaDirectionType.Size = new System.Drawing.Size(102, 16);
            this.cbxAntennaDirectionType.TabIndex = 4;
            this.cbxAntennaDirectionType.Text = "DirectionType";
            this.cbxAntennaDirectionType.UseVisualStyleBackColor = true;
            this.cbxAntennaDirectionType.CheckedChanged += new System.EventHandler(this.cbxAntennaDirectionType_CheckedChanged);
            // 
            // cbxAntennaLatitude
            // 
            this.cbxAntennaLatitude.AutoSize = true;
            this.cbxAntennaLatitude.Location = new System.Drawing.Point(15, 73);
            this.cbxAntennaLatitude.Name = "cbxAntennaLatitude";
            this.cbxAntennaLatitude.Size = new System.Drawing.Size(72, 16);
            this.cbxAntennaLatitude.TabIndex = 3;
            this.cbxAntennaLatitude.Text = "Latitude";
            this.cbxAntennaLatitude.UseVisualStyleBackColor = true;
            this.cbxAntennaLatitude.CheckedChanged += new System.EventHandler(this.cbxAntennaLatitude_CheckedChanged);
            // 
            // cbxAntennaLongitude
            // 
            this.cbxAntennaLongitude.AutoSize = true;
            this.cbxAntennaLongitude.Location = new System.Drawing.Point(15, 57);
            this.cbxAntennaLongitude.Name = "cbxAntennaLongitude";
            this.cbxAntennaLongitude.Size = new System.Drawing.Size(78, 16);
            this.cbxAntennaLongitude.TabIndex = 2;
            this.cbxAntennaLongitude.Text = "Longitude";
            this.cbxAntennaLongitude.UseVisualStyleBackColor = true;
            this.cbxAntennaLongitude.CheckedChanged += new System.EventHandler(this.cbxAntennaLongitude_CheckedChanged);
            // 
            // cbxAntennaSN
            // 
            this.cbxAntennaSN.AutoSize = true;
            this.cbxAntennaSN.Location = new System.Drawing.Point(15, 41);
            this.cbxAntennaSN.Name = "cbxAntennaSN";
            this.cbxAntennaSN.Size = new System.Drawing.Size(36, 16);
            this.cbxAntennaSN.TabIndex = 1;
            this.cbxAntennaSN.Text = "SN";
            this.cbxAntennaSN.UseVisualStyleBackColor = true;
            this.cbxAntennaSN.CheckedChanged += new System.EventHandler(this.cbxAntennaSN_CheckedChanged);
            // 
            // cbxAntennaGroup
            // 
            this.cbxAntennaGroup.AutoSize = true;
            this.cbxAntennaGroup.Location = new System.Drawing.Point(15, 25);
            this.cbxAntennaGroup.Name = "cbxAntennaGroup";
            this.cbxAntennaGroup.Size = new System.Drawing.Size(96, 16);
            this.cbxAntennaGroup.TabIndex = 0;
            this.cbxAntennaGroup.Text = "AntennaGroup";
            this.cbxAntennaGroup.UseVisualStyleBackColor = true;
            this.cbxAntennaGroup.CheckedChanged += new System.EventHandler(this.cbxAntennaGroup_CheckedChanged);
            // 
            // btnFont
            // 
            this.btnFont.Location = new System.Drawing.Point(6, 124);
            this.btnFont.Name = "btnFont";
            this.btnFont.Size = new System.Drawing.Size(53, 23);
            this.btnFont.TabIndex = 72;
            this.btnFont.Text = "Font...";
            this.btnFont.UseVisualStyleBackColor = true;
            this.btnFont.Click += new System.EventHandler(this.btnFont_Click);
            // 
            // cbxDrawAntennaLabel
            // 
            this.cbxDrawAntennaLabel.AutoSize = true;
            this.cbxDrawAntennaLabel.Location = new System.Drawing.Point(6, 101);
            this.cbxDrawAntennaLabel.Name = "cbxDrawAntennaLabel";
            this.cbxDrawAntennaLabel.Size = new System.Drawing.Size(102, 16);
            this.cbxDrawAntennaLabel.TabIndex = 71;
            this.cbxDrawAntennaLabel.Text = "Display Label";
            this.cbxDrawAntennaLabel.UseVisualStyleBackColor = true;
            this.cbxDrawAntennaLabel.CheckedChanged += new System.EventHandler(this.cbxDrawAntennaLabel_CheckedChanged);
            // 
            // cbxAntennaDowardM
            // 
            this.cbxAntennaDowardM.AutoSize = true;
            this.cbxAntennaDowardM.Location = new System.Drawing.Point(15, 137);
            this.cbxAntennaDowardM.Name = "cbxAntennaDowardM";
            this.cbxAntennaDowardM.Size = new System.Drawing.Size(66, 16);
            this.cbxAntennaDowardM.TabIndex = 7;
            this.cbxAntennaDowardM.Text = "DowardM";
            this.cbxAntennaDowardM.UseVisualStyleBackColor = true;
            this.cbxAntennaDowardM.CheckedChanged += new System.EventHandler(this.cbxAntennaDowardM_CheckedChanged);
            // 
            // cbxAntennaDowardE
            // 
            this.cbxAntennaDowardE.AutoSize = true;
            this.cbxAntennaDowardE.Location = new System.Drawing.Point(15, 121);
            this.cbxAntennaDowardE.Name = "cbxAntennaDowardE";
            this.cbxAntennaDowardE.Size = new System.Drawing.Size(66, 16);
            this.cbxAntennaDowardE.TabIndex = 6;
            this.cbxAntennaDowardE.Text = "DowardE";
            this.cbxAntennaDowardE.UseVisualStyleBackColor = true;
            this.cbxAntennaDowardE.CheckedChanged += new System.EventHandler(this.cbxAntennaDowardE_CheckedChanged);
            // 
            // MapWCellLayerAntennaProperties
            // 
            this.Controls.Add(this.btnFont);
            this.Controls.Add(this.cbxDrawAntennaLabel);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.checkBoxDisplay);
            this.Controls.Add(label100);
            this.Controls.Add(label0);
            this.Controls.Add(labelColorLabelAntenna);
            this.Controls.Add(this.labelColorAntenna);
            this.Controls.Add(this.TrackBarOpacity);
            this.Controls.Add(LabelOpacity);
            this.Controls.Add(labelColor);
            this.Name = "MapWCellLayerAntennaProperties";
            this.Size = new System.Drawing.Size(315, 234);
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private MapWCellLayer layer;

        private CheckBox checkBoxDisplay;

        private Label labelColorAntenna;

        private TrackBar TrackBarOpacity;
        private GroupBox groupBox1;
        private Button btnFont;
        private CheckBox cbxDrawAntennaLabel;
        private CheckBox cbxAntennaAltitude;
        private CheckBox cbxAntennaDownward;
        private CheckBox cbxAntennaDirection;
        private CheckBox cbxAntennaDirectionType;
        private CheckBox cbxAntennaLatitude;
        private CheckBox cbxAntennaLongitude;
        private CheckBox cbxAntennaSN;
        private CheckBox cbxAntennaGroup;
        private CheckBox cbxAntennaDescription;

        private readonly ColorDialog colorDialog = new ColorDialog();
        private CheckBox cbxAntennaDowardE;
        private CheckBox cbxAntennaDowardM;
        private readonly FontDialog fontDialog = new FontDialog();
    }
}
