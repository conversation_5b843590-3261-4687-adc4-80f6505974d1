using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class TDMSC
    {
        public TDMSC()
        {
        }

        public TDMSC(string name)
        {
            Name = name;
        }

        public string Name { get; set; }

        public List<TDRNC> BSCs
        {
            get { return bscs; }
        }

        public TDRNC AddBSC(string bscName)
        {
            if (!nameBSCMap.ContainsKey(bscName))
            {
                TDRNC bsc = new TDRNC(bscName);
                bscs.Add(bsc);
                nameBSCMap[bscName] = bsc;
                bsc.BelongMSC = this;
            }
            return nameBSCMap[bscName];
        }

        private readonly List<TDRNC> bscs = new List<TDRNC>();

        private readonly Dictionary<string, TDRNC> nameBSCMap = new Dictionary<string, TDRNC>();

        public static IComparer<TDMSC> GetCompareByName()
        {
            if (comparerByName == null)
            {
                comparerByName = new ComparerByName();
            }
            return comparerByName;
        }

        private static IComparer<TDMSC> comparerByName;

        public class ComparerByName : IComparer<TDMSC>
        {
            public int Compare(TDMSC x, TDMSC y)
            {
                return x.Name.CompareTo(y.Name);
            }
        }
    }
}