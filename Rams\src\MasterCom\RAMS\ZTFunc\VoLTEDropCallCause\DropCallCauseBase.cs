﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.VoLTEDropCallCause
{
    public abstract class DropCallCauseBase
    {
        public abstract bool IsSatisfy(CallInfo call);
        //public abstract string Name { get;set;}
        public abstract bool ShowCaption { get; set; }
    }

    public class VoiceHangupCause : DropCallCauseBase
    {
        public override bool IsSatisfy(CallInfo call)
        {//addMessage(child, new MessageInfo(0x7FFF1C43, "Voice_Hangup"));
            for (int i = call.Messages.Count - 1; i >= 0; i--)
            {
                Message msg = call.Messages[i];
                if (msg.ID == 0x7FFF1C43)
                {
                    call.DropCause = DropCallCause.VoiceHangup;
                    return true;
                }
            }
            return false;
        }

        public override bool ShowCaption
        {
            get
            {
                throw new NotImplementedException();
            }
            set
            {
                throw new NotImplementedException();
            }
        }
    }

    public class ReleasEPSBeforeOKCause : DropCallCauseBase
    {
        public override bool IsSatisfy(CallInfo call)
        {//0x416B02ce, "Deactivate EPS bearer context accept"
            for (int i = call.Messages.Count - 1; i >= 0; i--)
            {
                Message msg = call.Messages[i];
                if (msg.ID == (int)MessageManager.LTE_NAS_Deactivate_EPS_bearer_context_accept)
                {
                    call.DropCause = DropCallCause.提前释放EPS专用承载;
                    return true;
                }
            }
            return false;
        }

        public override bool ShowCaption
        {
            get
            {
                throw new NotImplementedException();
            }
            set
            {
                throw new NotImplementedException();
            }
        }
    }

    public class TwoBYECause : DropCallCauseBase
    {
        public override bool IsSatisfy(CallInfo call)
        {
            int byeNum = 0;
            for (int i = call.Messages.Count - 1; i >= 0; i--)
            {
                Message msg = call.Messages[i];
                if (msg.ID == (int)MessageManager.Msg_IMS_SIP_BYE)
                {
                    byeNum++;
                }
                else if (msg.ID == (int)MessageManager.Msg_IMS_SIP_INVITE_OK)
                {
                    break;
                }
            }
            if (byeNum > 1)
            {
                call.DropCause = DropCallCause.双BYE;
                return true;
            }
            else
            {
                return false;
            }
        }

        public override bool ShowCaption
        {
            get
            {
                throw new NotImplementedException();
            }
            set
            {
                throw new NotImplementedException();
            }
        }
    }

    public class SIPBYERequestTerminated : DropCallCauseBase
    {
        public override bool IsSatisfy(CallInfo call)
        {
            for (int i = call.Messages.Count - 1; i >= 0; i--)
            {
                Message msg = call.Messages[i];
                int id;
                if (MessageInfoManager.TryParseToSipReqMsgID(msg.ID, out id) && id == 487)
                {
                    //eIMS_SIP_Response_Status_Code.Request_Terminated
                    call.DropCause = DropCallCause.BYE_Request_Terminated;
                    return true;
                }
            }
            return false;
        }

        public override bool ShowCaption
        {
            get
            {
                throw new NotImplementedException();
            }
            set
            {
                throw new NotImplementedException();
            }
        }
    }

    public class WeakCoverCause : DropCallCauseBase
    {
        public override bool IsSatisfy(CallInfo call)
        {
            throw new NotImplementedException();
        }

        public override bool ShowCaption
        {
            get
            {
                throw new NotImplementedException();
            }
            set
            {
                throw new NotImplementedException();
            }
        }
    }

    //public class UnstabitilyCoverCause : DropCallCauseBase
    //{
    //}

    public class PoorSINRCause : DropCallCauseBase
    {
        public override bool IsSatisfy(CallInfo call)
        {
            throw new NotImplementedException();
        }


        public override bool ShowCaption
        {
            get
            {
                throw new NotImplementedException();
            }
            set
            {
                throw new NotImplementedException();
            }
        }
    }

    //public class UnreasonableHoPoorMainRSRP : DropCallCauseBase
    //{
 
    //}

    //public class UnreasonableHoGoodNbRSRP : DropCallCauseBase
    //{

    //}

}
