﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class NBIOTWeakCoverAnaByRegion : VoLteWeakCoverAnaByRegion
    {
        public NBIOTWeakCoverAnaByRegion(ServiceName serviceName)
            : base(serviceName)
        {
        }


        public override string Name
        {
            get
            {
                return "NB弱覆盖分析(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34001, this.Name);
        }
    }
}
