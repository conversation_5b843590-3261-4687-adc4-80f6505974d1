﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTNRScanGoodRsrpPoorSinr
{
    public partial class NRScanGoodRsrpPoorSinrForm : MinCloseForm
    {
        public NRScanGoodRsrpPoorSinrForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void FillData(List<NRScanGoodRsrpPoorSinrCell> cells)
        {
            gridControl.DataSource = cells;
            gridControl.RefreshDataSource();
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gridView.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }

            var cell = gridView.GetRow(info.RowHandle) as NRScanGoodRsrpPoorSinrCell;
            if (cell == null)
            {
                return;
            }
            MainModel.SetSelectedNRCell(cell.Cell);
            MainModel.ClearDTData();
            foreach (TestPoint tp in cell.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            MainModel.FireSetDefaultMapSerialTheme(NRTpHelper.NrScanTpManager.SinrFullThemeName);
            MainModel.FireDTDataChanged(this);
        }

        private void miExp2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }
    }
}
