﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTSCellNCellInfo;

namespace MasterCom.RAMS.ZTFunc
{
    public class QuerySCellNCellSignalLevDiff_TD : QuerySCellNCellSignalLevDiff
    {
        private static QuerySCellNCellSignalLevDiff_TD instance = null;
        public new static QuerySCellNCellSignalLevDiff_TD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new QuerySCellNCellSignalLevDiff_TD();
                    }
                }
            }
            return instance;
        }

        protected QuerySCellNCellSignalLevDiff_TD()
            : base()
        {
        }

        public override string Name
        {
            get
            {
                return "主服邻区场强信息查询(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13049, this.Name);
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                Dictionary<ICell, SCellInfo> sCellDic = new Dictionary<ICell, SCellInfo>();
                foreach (TestPoint tp in file.TestPoints)
                {
                    if (tp is TDTestPointDetail || tp is TDTestPointSummary)
                    {
                        if (!isValidTestPoint(tp))
                        {
                            continue;
                        }
                        ICell sCell = tp.GetMainCell();
                        if (sCell == null)
                        {
                            continue;
                        }
                        float? sLev = null;
                        if (tp[MainModel.TD_GSM_SCell_LAC] != null 
                            && tp[MainModel.TD_GSM_SCell_CI] != null)
                        {
                            sLev = (float?)(int?)tp["TD_GSM_RxlevSub"];
                        }
                        else
                        {
                            sLev = (float?)tp["TD_PCCPCH_RSCP"];
                        }
                        if (sLev == null)
                        {
                            continue;
                        }

                        SCellInfo sCellInfo;
                        if (!sCellDic.TryGetValue(sCell, out sCellInfo))
                        {
                            sCellInfo = new SCellInfo();
                            sCellInfo.Cell = sCell;
                            sCellInfo.FileName = file.FileName;
                            sCellDic[sCell] = sCellInfo;
                        }
                        for (int i = 0; i < 10; i++)
                        {
                            int? nRscp = (int?)tp["TD_NCell_PCCPCH_RSCP", i];
                            if (nRscp != null && (nRscp > sLev
                                    || Math.Abs((float)(int)nRscp - (float)sLev) <= levDiff))
                            {
                                ICell nCell = tp.GetNBCell_TD_TDCell(i);
                                if (nCell != null)
                                {
                                    sCellInfo.AddNCell(saveTp ? tp : null, (float)sLev, nCell, (float)(int)nRscp);
                                }
                            }

                            int? nRxLev = (int?)(short?)tp["TD_GSM_NCell_Rxlev", i];
                            if (nRxLev != null && (nRxLev > sLev
                                   || Math.Abs((float)(int)nRxLev - (float)sLev) <= levDiff))
                            {
                                ICell nCell = tp.GetNBCell_TD_GSMCell(i);
                                if (nCell != null)
                                {
                                    sCellInfo.AddNCell(saveTp ? tp : null, (float)sLev, nCell, (float)(int)nRxLev);
                                }
                            }

                        }
                    }
                }
                foreach (SCellInfo sCell in sCellDic.Values)
                {
                    if (sCell.NCells.Count > 0)
                    {
                        this.cells.Add(sCell);
                    }
                }
            }
        }

    }
}
