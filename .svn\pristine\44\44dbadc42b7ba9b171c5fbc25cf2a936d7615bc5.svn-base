﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using System.Threading;

namespace MasterCom.RAMS.ZTFunc
{
    public class HandoverStreetCellCfgManager
    {
        private static HandoverStreetCellCfgManager instance = null;
        private static object objLock = new object();

        public List<string> StreetNameLst { get; set; }
        public List<string> SubStreetNameLst { get; set; }
        public List<string> NetTypeLst { get; set; }
        public List<string> CoverTypeLst { get; set; }

        private List<StreetCellInfo> idealStreetCells = null;
        public List<StreetCellInfo> IdealStreetCells
        {
            get
            {
                if (idealStreetCells==null)
                {
                    SearchStreetCells query = new SearchStreetCells(MainModel.GetInstance());
                    query.Query();
                    this.idealStreetCells = query.GetResult();
                }
                return idealStreetCells;
            }
        }

        private HandoverStreetCellCfgManager() 
        {
            StreetNameLst = new List<string>();
            SubStreetNameLst = new List<string>();
            NetTypeLst = new List<string>();
            CoverTypeLst = new List<string>();

            WaitBox.Show("正在读取初始化配置...", initCfgData);
        }

        public static HandoverStreetCellCfgManager GetInstance()
        {
            if (instance == null)
            {
                lock (objLock)
                {
                    if (instance == null)
                    {
                        instance = new HandoverStreetCellCfgManager();
                    }
                }
            }
            return instance;
        }

        private void initCfgData()
        {
            try
            {
                HandoverQueryCfg query = new HandoverQueryCfg(MainModel.GetInstance());
                string sql = "select distinct streetName from tb_cfg_street_cell";
                query.SetSqlText(sql);
                query.Query();
                StreetNameLst.AddRange(query.GetResult());

                sql = "select distinct subStreetName from tb_cfg_street_cell";
                query.SetSqlText(sql);
                query.Query();
                SubStreetNameLst.AddRange(query.GetResult());

                sql = "select distinct netType from tb_cfg_street_cell";
                query.SetSqlText(sql);
                query.Query();
                NetTypeLst.AddRange(query.GetResult());

                sql = "select distinct coverType from tb_cfg_street_cell";
                query.SetSqlText(sql);
                query.Query();
                CoverTypeLst.AddRange(query.GetResult());
            }
            finally
            {
                Thread.Sleep(100);
                WaitBox.Close();
            }
        }

        public bool IsValidCfg()
        {
            return StreetNameLst.Count > 0 && NetTypeLst.Count > 0 && CoverTypeLst.Count > 0;
        }
    }
}
