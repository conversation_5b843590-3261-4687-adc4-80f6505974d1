﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.Model;
using Excel = Microsoft.Office.Interop.Excel;
using MasterCom.RAMS.Func;
using System.Drawing;
using MasterCom.RAMS.BackgroundFunc;
using System.IO;

namespace MasterCom.RAMS.ZTFunc
{
    public class NbIotStationAcceptManagerXJ
    {
        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public NBIotCellAcceptFileInfo NBIotAcceptFileInfo { get; set; }

        protected virtual List<NbIotCellAcceptKpiAnaXJ> getAcceptAnaList(LTEBTSType btsType)
        {
            List<NbIotCellAcceptKpiAnaXJ> acceptorList = null;
            if (btsType == LTEBTSType.Outdoor)
            {
                acceptorList = new List<NbIotCellAcceptKpiAnaXJ>()
                {
                   new NbIotAcpAutoAttachRateXJ(),
                   new NbIotAcpAutoInStationReSelectXJ(),
                   //new NbIotAcpAutoBetweenStationReSelectXJ(),
                   new NbIotAcpAutoPingRateXJ(),
                   new NbIotAcpAutoPingDelayXJ(),              
                   new NbIotAcpAutoULSpeedXJ(),
                   new NbIotAcpAutoDLSpeedXJ(),
                   new NbIotAcpAutoCoverPictureXJ(),
                };
            }
            else
            {
                //移动白皮书暂未写NB室分站
                acceptorList = new List<NbIotCellAcceptKpiAnaXJ>()
                {

                };
            }
            return acceptorList;
        }

        /// <summary>
        /// 分析文件,进行单验业务
        /// </summary>
        /// <param name="fileInfo"></param>
        /// <param name="fileManager"></param>
        /// <returns></returns>
        public virtual LTECell AnalyzeFile(Model.FileInfo fileInfo, DTFileDataManager fileManager)
        {
            LTECell targetCell = GetFileTestCell(fileManager);
            if (targetCell == null)
            {
                return null;
            }

            List<NbIotCellAcceptKpiAnaXJ> acceptAnaList = getAcceptAnaList(targetCell.Type);//根据小区类型获取业务分析类
            foreach (NbIotCellAcceptKpiAnaXJ acp in acceptAnaList)
            {
                if (acp.IsValidFile(fileInfo))
                {
                    doStatWithData(acp, fileInfo, fileManager, targetCell);
                    //目前没有一个文件对应多个业务的情况,执行完一个业务就返回
                    break;
                }
            }
            return targetCell;
        }

        protected void doStatWithData(NbIotCellAcceptKpiAnaXJ acp, Model.FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            if (NBIotAcceptFileInfo == null)
            {
                NBIotAcceptFileInfo = new NBIotCellAcceptFileInfo(fileInfo, targetCell);
            }
            Dictionary<NbIotKpiKeyXJ, object> kpiInfoDic = acp.GetFileKpiInfos(fileInfo, fileManager, targetCell);
            if (kpiInfoDic.Count > 0)
            {
                foreach (NbIotKpiKeyXJ key in kpiInfoDic.Keys)
                {
                    object valueObj = kpiInfoDic[key];
                    if (valueObj is double)
                    {
                        double valueDouble = (double)valueObj;
                        if (valueDouble == double.MinValue)
                        {
                            valueObj = double.NaN;
                        }
                        else
                        {
                            valueObj = Math.Round(valueDouble, 4);
                        }
                    }

                    NBIotAcceptFileInfo.AcceptKpiDic.Add((uint)key, valueObj);
                }
            }
        }

        #region 获取测试文件对应的主服小区
        /// <summary>
        /// 获取测试文件对应的小区
        /// </summary>
        /// <param name="fileManager"></param>
        /// <returns></returns>
        public LTECell GetFileTestCell(DTFileDataManager fileManager)
        {
            bool isHandoverFile = false;
            if (fileManager.FileName.Contains("重选"))
            {
                isHandoverFile = true;
            }

            LTECell targeCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = GetTpSrcCell(tp, fileManager.FileName);
                if (isSrcTestCell(cell, fileManager.FileName, isHandoverFile))
                {
                    targeCell = cell;
                    break;
                }
            }
            return targeCell;
        }

        /// <summary>
        /// 根据采样点匹配主服小区
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        public LTECell GetTpSrcCell(TestPoint tp, string fileName)
        {
            LTECell cell = null;
            if (tp is LTETestPointDetail)
            {
                cell = StationAcceptCellHelper_XJ.Instance.GetLTECellByTacEci(tp);

                int? earfcn = (int?)tp["lte_EARFCN"];
                int? pci = (int?)(short?)tp["lte_PCI"];

                if (cell == null && earfcn != null && pci != null)
                {
                    var time = StationAcceptCellHelper_XJ.Instance.GetValidDate(tp.DateTime);
                    cell = getLTECellByEARFCNPCI(time, earfcn, pci, tp.Longitude, tp.Latitude, fileName);
                }
            }
            return cell;
        }

        /// <summary>
        /// 根据频点PCI匹配小区
        /// </summary>
        /// <param name="time"></param>
        /// <param name="earfcn"></param>
        /// <param name="pci"></param>
        /// <param name="longitude"></param>
        /// <param name="latitude"></param>
        /// <returns></returns>
        private LTECell getLTECellByEARFCNPCI(DateTime time, int? earfcn, int? pci, double longitude, double latitude, string fileName)
        {
            List<LTECell> cells = CellManager.GetInstance().GetLTECellListByEARFCNPCI(time, earfcn, pci);
            if (cells != null && cells.Count > 0)
            {
                foreach (LTECell cell in cells)
                {
                    if (fileName.Contains(cell.Name) && cell.ValidPeriod.Contains(time))
                    {
                        return cell;
                    }

                    //if (cell.ValidPeriod.Contains(time))
                    //{
                    //    if (longitude > 0 && latitude > 0 && CellManager.GetInstance().SystemConfigInfo.distLimit && cell.GetDistance(longitude, latitude) > CD.MAX_COV_DISTANCE_LTE)
                    //    {
                    //        continue;
                    //    }
                    //    return cell;
                    //}
                }
                
                return CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(time, earfcn, pci, longitude, latitude);
            }
            return null;
        }

        /// <summary>
        /// 根据文件名判断是否为测试文件的主服小区(后续根据文件命名规范修改此判断)
        /// </summary>
        /// <param name="cell"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private bool isSrcTestCell(LTECell cell, string fileName, bool isHandoverFile)
        {
            if (cell == null)
            {
                return false;
            }
            string keyStr = cell.Name.ToUpper();
            if (isHandoverFile && cell.BelongBTS != null)
            {
                //获取NB基站名
                keyStr = GetBtsName(cell, fileName).ToUpper();
            }
            if (!string.IsNullOrEmpty(keyStr) && fileName.ToUpper().Contains(keyStr.Trim()))
            {
                return true;
            }
            return false;
        }
        #endregion

        public string GetBtsName(LTECell lteCell, string fileName)
        {
            string btsName = lteCell.BelongBTS.Name;
            string btsPrefix = "";
            if (btsName.Contains("-NB"))
            {
                return btsName;
            }
            else if (btsName.Contains("-FDD"))
            {
                //由于存在FDD共站,假如站名为FDD站名时,替换为NB
                btsPrefix = btsName.Substring(0, btsName.LastIndexOf('-'));
                btsName = btsPrefix + "-NB";
                return btsName;
            }
            else
            {
                return btsName + "-NB";
            }
        }
    }

    public static class NbIotStaionAcceptResultHelperXJ
    {
        /// <summary>
        /// 将基站对应的结果数据转为导出时用的格式,并判断每一项是否合格
        /// </summary>
        /// <param name="resultList"></param>
        /// <param name="btsWorkParam"></param>
        /// <returns></returns>
        public static NbIotOutDoorBtsAcceptInfoXJ GetOutDoorBtsResultByData(Dictionary<int, Dictionary<uint, object>> resultList
           , NbIotBtsWorkParam btsWorkParam)
        {
            NbIotOutDoorBtsAcceptInfoXJ btsAcceptInfo = null;
            foreach (var result in resultList)
            {
                btsAcceptInfo = addCellAcceptInfo(btsWorkParam, btsAcceptInfo, result);
            }

            //新疆报告暂未包含是否通过结论,但工参表中需记录是否通过验收,还是需要判断各项是否合格
            if (btsAcceptInfo != null)
            {
                btsAcceptInfo.CheckBtsIsAccordAccept();
            }
            return btsAcceptInfo;
        }

        private static NbIotOutDoorBtsAcceptInfoXJ addCellAcceptInfo(NbIotBtsWorkParam btsWorkParam, NbIotOutDoorBtsAcceptInfoXJ btsAcceptInfo, KeyValuePair<int, Dictionary<uint, object>> result)
        {
            DateTime bgResultTime = Convert.ToDateTime(btsWorkParam.DateTimeDes);
            int tac = 0;
            int eci = 0;
            string btsName = "";
            for (int i = 0; i < btsWorkParam.CellWorkParams.Count; i++)
            {
                NbIotCellWorkParam cell = btsWorkParam.CellWorkParams[i] as NbIotCellWorkParam;
                if (cell.CellID == result.Key)
                {
                    tac = cell.Tac;
                    eci = cell.Eci;
                }
                btsName = cell.BtsName;
            }
            LTECell nbiotCell = StationAcceptCellHelper_XJ.Instance.GetLTECellByTacEci(bgResultTime, tac, eci);
            if (nbiotCell != null && nbiotCell.BelongBTS.BTSID == btsWorkParam.ENodeBID)
            {
                if (btsAcceptInfo == null)
                {
                    //存在共站时获取的NB基站名
                    btsAcceptInfo = new NbIotOutDoorBtsAcceptInfoXJ(nbiotCell.BelongBTS, btsName);
                }

                NbIotOutDoorCellAcceptInfoXJ cellAcceptInfo;
                if (!btsAcceptInfo.CellsAcceptDic.TryGetValue(nbiotCell.CellID, out cellAcceptInfo))
                {
                    cellAcceptInfo = new NbIotOutDoorCellAcceptInfoXJ(nbiotCell);
                    btsAcceptInfo.CellsAcceptDic.Add(nbiotCell.CellID, cellAcceptInfo);
                }
                cellAcceptInfo.AddAcceptKpiInfo(result.Value);
            }

            return btsAcceptInfo;
        }
    }
}
