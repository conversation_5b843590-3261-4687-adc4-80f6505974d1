﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.Globalization;

namespace MasterCom.RAMS.ZTFunc
{
    //根据时间范围查询建议
    
    class AdviseQuery : DIYSQLBase
    {
        private readonly bool IsTDIn;
        private readonly DateTime timeStart;
        private readonly DateTime timeEnd;
        public string Advise;

        public AdviseQuery(MainModel mainModel,bool IsTDIn,DateTime timeStart,DateTime timeEnd)
            : base(mainModel)
        {
            this.IsTDIn = IsTDIn;
            this.timeStart = timeStart;
            this.timeEnd = timeEnd;
            Advise = "";
        }

        protected override string getSqlTextString()
        {
            string IDOfTmdat = timeStart.ToString("yyyyMMdd") + 
                timeEnd.ToString("yyyyMMdd") + (IsTDIn ? "1" : "0");
            string statement = "select [adviseInf] from tb_para_Advise where ID = "+IDOfTmdat;           
            return statement;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_String;
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    try
                    {
                        Advise = package.Content.GetParamString();
                    }
                    catch
                    {
                        //continue
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgress(ref index, ref progress);
            }
        }

        private static void setProgress(ref int index, ref int progress)
        {
            if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
            {
                progress++;
                if (progress > 95)
                {
                    progress = 5;
                    index = 0;
                }
                if (progress % 5 == 0)
                {
                    WaitBox.ProgressPercent = progress;
                }
            }
        }

        public override string Name
        {
            get { return "PerDetailInfQuery"; }
        }
    }
}
