﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.Util
{
    public partial class InputReasonForm : BaseDialog
    {
        public InputReasonForm()
            : base()
        {
            InitializeComponent();
        }

        public string Person
        {
            get { return this.txbPerson.Text; }
            set { this.txbPerson.Text = value; }
        }

        public string TextInput
        {
            get { return this.txbReason.Text; }
            set { this.txbReason.Text = value; }
        }
    }
}
