﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ChkMgrsLibTestResultForm : MinCloseForm
    {
        public ChkMgrsLibTestResultForm(MainModel mModel)
            : base(mModel)
        {
            InitializeComponent();
            DisposeWhenClose = true;

            miExportExcel.Click += MiExportExcel_Click;
        }

        public void FillData(object result)
        {
            gridControl1.DataSource = new List<MgrsLibTestItem>(result as List<MgrsLibTestItem>);
            gridControl1.RefreshDataSource();
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }
    }
}
