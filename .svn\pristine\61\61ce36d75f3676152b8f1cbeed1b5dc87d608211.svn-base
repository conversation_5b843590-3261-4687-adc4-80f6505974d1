﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.Voronoi;

namespace MasterCom.RAMS.ZTFunc
{
    public class GSMBSCVoiCover
    {
        public string LastErrorText { get; private set; }
        public Dictionary<BSC, List<Vertex[]>> Construct()
        {
            Init();
            WaitBox.Show("正在建立GSM泰森多边形数据映射...", PrepareData);
            DoBTSVoi();
            if (longlatPolysDict == null && LastErrorText != "")
            {
                return new Dictionary<BSC, List<Vertex[]>>();
            }
            WaitBox.Show("正在组合GSM-BSC区域...", MergeBTS);
            return bscPolysDict;
        }

        private void Init()
        {
            LastErrorText = "";
            longlatPolysDict = null;
            bscPolysDict = new Dictionary<BSC, List<Vertex[]>>();
        }

        private void DoBTSVoi()
        {
            VoronoiManager<Vertex> manager = VoronoiManager<Vertex>.GetInstance();
            longlatPolysDict = manager.Construct(vList, Filter, true);
            LastErrorText = manager.LastErrorText;
        }

        private void PrepareData()
        {
            if (btsList != null)
            {
                System.Threading.Thread.Sleep(500);
                WaitBox.Close();
                return;
            }

            if (MapCellLayer.DrawCurrent)
            {
                btsList = MainModel.GetInstance().CellManager.GetCurrentBTSs();
            }
            else
            {
                btsList = MainModel.GetInstance().CellManager.GetBTSs(MapCellLayer.CurShowTimeAt);
            }

            int loop = 0;
            foreach (BTS bts in btsList)
            {
                WaitBox.ProgressPercent = ++loop * 100 / btsList.Count;

                Vertex v = new Vertex(bts.Longitude, bts.Latitude);
                if (!longlatBtsDict.ContainsKey(v))
                {
                    longlatBtsDict.Add(v, new List<BTS>());
                    vList.Add(v);
                }
                longlatBtsDict[v].Add(bts);
            }

            System.Threading.Thread.Sleep(500);
            WaitBox.Close();
        }

        private void MergeBTS()
        {
            CPolygonClipper clipper = new CPolygonClipper();
            int loop = 0;
            foreach (Vertex v in longlatPolysDict.Keys)
            {
                WaitBox.ProgressPercent = ++loop * 100 / longlatPolysDict.Count;

                if (!longlatBtsDict.ContainsKey(v))
                {
                    continue;
                }

                List<BTS> tmpBtsList = longlatBtsDict[v];
                foreach (BTS bts in tmpBtsList)
                {
                    BSC bsc = bts.BelongBSC;
                    if (bsc == null)
                    {
                        continue;
                    }

                    List<Vertex[]> polys;
                    if (!bscPolysDict.ContainsKey(bsc))
                    {
                        polys = new List<Vertex[]>();
                        bscPolysDict.Add(bsc, polys);
                    }
                    else
                    {
                        polys = bscPolysDict[bsc];
                    }

                                        
                    List<Vertex[]> retPolys = clipper.ClipPolygon(CPolygonClipper.ClipOperation.Union,
                        longlatPolysDict[v], polys);
                    bscPolysDict[bsc] = retPolys;
                }
            }
            System.Threading.Thread.Sleep(500);
            WaitBox.Close();
        }

        private bool Filter(Vertex v, MapOperation2 mop2)
        {
            return mop2.CheckPointInRegion(v.X, v.Y);
        }

        private Dictionary<BSC, List<Vertex[]>> bscPolysDict;
        private Dictionary<Vertex, List<Vertex[]>> longlatPolysDict;
        private static Dictionary<Vertex, List<BTS>> longlatBtsDict { get; set; } = new Dictionary<Vertex, List<BTS>>();
        private static List<BTS> btsList { get; set; }
        private static List<Vertex> vList { get; set; } = new List<Vertex>();
    }

    public class TDBSCVoiCover
    {
        public string LastErrorText { get; private set; }
        public Dictionary<TDRNC, List<Vertex[]>> Construct()
        {
            Init();
            WaitBox.Show("正在建立TD泰森多边形数据映射...", PrepareData);
            DoBTSVoi();
            if (longlatPolysDict == null && LastErrorText != "")
            {
                return new Dictionary<TDRNC, List<Vertex[]>>();
            }
            WaitBox.Show("正在组合TD-RNC区域...", MergeBTS);
            return bscPolysDict;
        }

        private void Init()
        {
            LastErrorText = "";
            longlatPolysDict = null;
            bscPolysDict = new Dictionary<TDRNC, List<Vertex[]>>();
        }

        private void DoBTSVoi()
        {
            VoronoiManager<Vertex> manager = VoronoiManager<Vertex>.GetInstance();
            longlatPolysDict = manager.Construct(vList, Filter, true);
            LastErrorText = manager.LastErrorText;
        }

        private void PrepareData()
        {
            if (btsList != null)
            {
                System.Threading.Thread.Sleep(500);
                WaitBox.Close();
                return;
            }

            if (MapCellLayer.DrawCurrent)
            {
                btsList = MainModel.GetInstance().CellManager.GetCurrentTDBTSs();
            }
            else
            {
                btsList = MainModel.GetInstance().CellManager.GetTDBTSs(MapCellLayer.CurShowTimeAt);
            }

            int loop = 0;
            foreach (TDNodeB bts in btsList)
            {
                WaitBox.ProgressPercent = ++loop * 100 / btsList.Count;

                Vertex v = new Vertex(bts.Longitude, bts.Latitude);
                if (!longlatBtsDict.ContainsKey(v))
                {
                    longlatBtsDict.Add(v, new List<TDNodeB>());
                    vList.Add(v);
                }
                longlatBtsDict[v].Add(bts);
            }

            System.Threading.Thread.Sleep(500);
            WaitBox.Close();
        }

        private void MergeBTS()
        {
            CPolygonClipper clipper = new CPolygonClipper();
            int loop = 0;
            foreach (Vertex v in longlatPolysDict.Keys)
            {
                WaitBox.ProgressPercent = ++loop * 100 / longlatPolysDict.Count;

                if (!longlatBtsDict.ContainsKey(v))
                {
                    continue;
                }

                List<TDNodeB> tmpBtsList = longlatBtsDict[v];
                foreach (TDNodeB bts in tmpBtsList)
                {
                    TDRNC bsc = bts.BelongBSC;
                    if (bsc == null)
                    {
                        continue;
                    }

                    List<Vertex[]> polys;
                    if (!bscPolysDict.ContainsKey(bsc))
                    {
                        polys = new List<Vertex[]>();
                        bscPolysDict.Add(bsc, polys);
                    }
                    else
                    {
                        polys = bscPolysDict[bsc];
                    }


                    List<Vertex[]> retPolys = clipper.ClipPolygon(CPolygonClipper.ClipOperation.Union,
                        longlatPolysDict[v], polys);
                    bscPolysDict[bsc] = retPolys;
                }
            }
            System.Threading.Thread.Sleep(500);
            WaitBox.Close();
        }

        private bool Filter(Vertex v, MapOperation2 mop2)
        {
            return mop2.CheckPointInRegion(v.X, v.Y);
        }

        private Dictionary<TDRNC, List<Vertex[]>> bscPolysDict;
        private Dictionary<Vertex, List<Vertex[]>> longlatPolysDict;
        private static Dictionary<Vertex, List<TDNodeB>> longlatBtsDict { get; set; } = new Dictionary<Vertex, List<TDNodeB>>();
        private static List<TDNodeB> btsList { get; set; }
        private static List<Vertex> vList { get; set; } = new List<Vertex>();
    }
}
