﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Reflection;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    /// <summary>
    /// 导出报告
    /// </summary>
    /// <typeparam name="T"></typeparam>
    abstract class ExportReportHelper_SX<T, U> where T : ICell
    {
        protected abstract string rsrpParam { get; }
        protected abstract string sinrParam { get; }
        protected abstract string dlParam { get; }
        protected abstract string ulParam { get; }

        public virtual void ExportReportToExcel(BtsAcceptInfo_SX<T, U> btsAcceptInfo, string savePath, BtsAcceptRecordInfo_SX<U> recordInfo)
        {
            if (btsAcceptInfo == null || string.IsNullOrEmpty(savePath))
            {
                return;
            }

            if (!System.IO.Directory.Exists(savePath))
            {
                System.IO.Directory.CreateDirectory(savePath);
            }
            Application excel = new Application();
            Workbook workbook = excel.Workbooks.Add(true);
            Worksheet worksheet = (Worksheet)excel.ActiveSheet;

            try
            {
                string strReprtPath = "";
                if (btsAcceptInfo.IsOutDoor)
                {
                    strReprtPath = savePath + "\\" + btsAcceptInfo.BtsName + "_宏站单验指标";
                    addOutDoorInfoToWookSheet(btsAcceptInfo, workbook, worksheet, recordInfo);
                }
                else
                {
                    strReprtPath = savePath + "\\" + btsAcceptInfo.BtsName + "_室分单验指标";
                    addInDoorInfoToWookSheet(btsAcceptInfo, worksheet);
                }
                recordInfo.ExcelPath = strReprtPath;
                excel.DisplayAlerts = false;
                workbook.SaveAs(strReprtPath, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value
                    , XlSaveAsAccessMode.xlNoChange, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value);

                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("已导出{0}站点的报告", btsAcceptInfo.BtsName));
            }
            catch (Exception ex)
            {
                recordInfo.BtsAcceptErrorInfo += "导出报告时出错;";
                BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
            }
            finally
            {
                excel.Quit();
            }
        }
        protected virtual void addOutDoorInfoToWookSheet(BtsAcceptInfo_SX<T, U> btsAcceptInfo, Workbook workbook, Worksheet worksheet
            , BtsAcceptRecordInfo_SX<U> recordInfo)
        {
            worksheet.Name = "性能验收覆盖效果图";
            ((Range)worksheet.Columns["A", System.Type.Missing]).ColumnWidth = 15;
            Range excelRange;
            excelRange = worksheet.get_Range(worksheet.Cells[1, 1], worksheet.Cells[8, 9]);
            excelRange.Borders.Color = System.Drawing.ColorTranslator.ToOle(Color.White);

            worksheet.Cells[2, 1] = "覆盖效果图：";
            excelRange = worksheet.get_Range(worksheet.Cells[2, 1], worksheet.Cells[2, 1]);
            excelRange.RowHeight = 22;
            excelRange.Font.Size = 16;
            excelRange.Font.Bold = true;

            worksheet.Cells[5, 1] = "站名:";
            excelRange = worksheet.get_Range(worksheet.Cells[5, 1], worksheet.Cells[5, 2]);
            excelRange.Merge(excelRange.MergeCells);
            excelRange.RowHeight = 22;
            excelRange.Font.Size = 14;
            excelRange.HorizontalAlignment = XlHAlign.xlHAlignCenter;

            worksheet.Cells[5, 3] = btsAcceptInfo.BtsName;
            excelRange = worksheet.get_Range(worksheet.Cells[5, 3], worksheet.Cells[5, 9]);
            excelRange.Merge(excelRange.MergeCells);
            excelRange.Borders.LineStyle = XlLineStyle.xlDash;
            excelRange.Borders.Color = System.Drawing.ColorTranslator.ToOle(Color.Gray);
            excelRange.Borders.Weight = XlBorderWeight.xlMedium;
            excelRange.HorizontalAlignment = XlHAlign.xlHAlignCenter;

            worksheet.Cells[7, 1] = "日期:";
            excelRange = worksheet.get_Range(worksheet.Cells[7, 1], worksheet.Cells[7, 2]);
            excelRange.Merge(excelRange.MergeCells);
            excelRange.RowHeight = 22;
            excelRange.Font.Size = 14;
            excelRange.HorizontalAlignment = XlHAlign.xlHAlignCenter;

            worksheet.Cells[7, 3] = DateTime.Now.ToShortDateString();
            excelRange = worksheet.get_Range(worksheet.Cells[7, 3], worksheet.Cells[7, 9]);
            excelRange.Merge(excelRange.MergeCells);
            excelRange.Borders.LineStyle = XlLineStyle.xlDash;
            excelRange.Borders.Color = System.Drawing.ColorTranslator.ToOle(Color.Gray);
            excelRange.Borders.Weight = XlBorderWeight.xlMedium;
            excelRange.HorizontalAlignment = XlHAlign.xlHAlignCenter;

            int excelrow = 10;
            MainModel.GetInstance().DrawFlyLines = true;
            clearCellSelection();
            float topPosition = (float)(((excelrow - 3) * 13.6) + 66);//减去已设置RowHeight的几行，再加上这几行的行高之和
            excelrow += 2;

            if (btsAcceptInfo.TestPointList_Dl.Count > 0)
            {
                goToMapView(btsAcceptInfo.CellList, btsAcceptInfo.TestPointList_Dl);
                saveSerialThemePicToExcel(ref excelrow, rsrpParam, worksheet, ref topPosition, "RSRP拉线图");
                saveSerialThemePicToExcel(ref excelrow, sinrParam, worksheet, ref topPosition, "SINR拉线图");
                saveSerialThemePicToExcel(ref excelrow, dlParam, worksheet, ref topPosition, "下载速率拉线图");
            }
            else
            {
                recordInfo.BtsAcceptErrorInfo += "无环测下载采样点;";
                saveTitleToExcel(worksheet, ref excelrow, ref topPosition, "无环测下载采样点，无法呈现轨迹图");
            }

            if (btsAcceptInfo.TestPointList_Ul.Count > 0)
            {
                goToMapView(btsAcceptInfo.CellList, btsAcceptInfo.TestPointList_Ul);
                saveSerialThemePicToExcel(ref excelrow, ulParam, worksheet, ref topPosition, "上传速率拉线图");
            }
            else
            {
                recordInfo.BtsAcceptErrorInfo += "无环测上传采样点;";
                saveTitleToExcel(worksheet, ref excelrow, ref topPosition, "无环测上传采样点，无法呈现轨迹图");
            }

            workbook.Sheets.Add();
            List<NPOIRow> sumRows = btsAcceptInfo.GetKpiSumRows();
            Worksheet worksheetKpi = (Worksheet)workbook.Sheets[1];
            worksheetKpi.Name = "性能验收测试表格";
            saveNPOIRowsToWorksheet(worksheetKpi, sumRows);
        }

        protected virtual void clearCellSelection()
        {
        }

        protected virtual void goToMapView(List<T> cellList, List<TestPoint> pntLst)
        {
            MTGis.DbRect rect = getDbRect(cellList, pntLst);
            MainModel.GetInstance().MainForm.GetMapForm().GoToView(rect);
        }

        protected MTGis.DbRect getDbRect(List<T> cellList, List<TestPoint> pntLst)
        {
            double maxLongitude = 0;
            double minLongitude = 99999;
            double maxLatitude = 0;
            double minLatitude = 99999;
            if (cellList != null && cellList.Count > 0)
            {
                setRangeByCell(cellList, ref maxLongitude, ref minLongitude, ref maxLatitude, ref minLatitude);
            }

            MainModel.GetInstance().ClearDTData();
            if (pntLst != null)
            {
                setRangeByTP(pntLst, ref maxLongitude, ref minLongitude, ref maxLatitude, ref minLatitude);
            }
            MainModel.GetInstance().FireDTDataChanged(StationAcceptAna_SX.GetInstance());

            MasterCom.MTGis.DbRect rect = new MasterCom.MTGis.DbRect(minLongitude, minLatitude - 0.00015, maxLongitude, maxLatitude + 0.00015);
            return rect;
        }

        protected abstract void setRangeByCell(List<T> cellList,
            ref double maxLongitude, ref double minLongitude, ref double maxLatitude, ref double minLatitude);

        protected virtual void setRangeByTP(List<TestPoint> pntLst,
            ref double maxLongitude, ref double minLongitude, ref double maxLatitude, ref double minLatitude)
        {
            foreach (TestPoint tp in pntLst)
            {
                if (tp.Longitude == 0 || tp.Latitude == 0)
                {
                    continue;
                }

                MainModel.GetInstance().DTDataManager.Add(tp);

                getEdgeData(ref maxLongitude, ref minLongitude, ref maxLatitude, ref minLatitude, tp.Longitude, tp.Latitude);
            }
        }

        protected void getEdgeData(ref double maxLongitude, ref double minLongitude, ref double maxLatitude, ref double minLatitude, double longitude, double latitude)
        {
            if (maxLongitude < longitude)
            {
                maxLongitude = longitude;
            }
            if (minLongitude > longitude && longitude != 0)
            {
                minLongitude = longitude;
            }
            if (maxLatitude < latitude)
            {
                maxLatitude = latitude;
            }
            if (minLatitude > latitude && latitude != 0)
            {
                minLatitude = latitude;
            }
        }

        private void saveSerialThemePicToExcel(ref int excelrow, string themeName, Worksheet worksheet, ref float topPosition, string strTitle)
        {
            MainModel.GetInstance().FireSetDefaultMapSerialTheme(themeName);
            MasterCom.RAMS.Func.MapDTLayer dtLayer = MainModel.GetInstance().MainForm.GetMapForm().GetDTLayer();
            dtLayer.IsBySerials = false;
            dtLayer.FlyColorFromOrig = true;
            dtLayer.Invalidate();

            saveTitleToExcel(worksheet, ref excelrow, ref topPosition, strTitle);

            string imgPath = string.Format(System.Windows.Forms.Application.StartupPath + "/singleStation1.png");
            if (System.IO.File.Exists(imgPath))
            {
                System.IO.File.Delete(imgPath);
            }
            MainModel.GetInstance().MainForm.GetMapForm().OutputCurrentResolutionMap(imgPath);

            worksheet.Shapes.AddPicture(imgPath, Microsoft.Office.Core.MsoTriState.msoFalse
                , Microsoft.Office.Core.MsoTriState.msoCTrue, 10, topPosition + 3, 500, 240);

            int rowIndex = excelrow;
            int colIndex = 10;
            foreach (MasterCom.RAMS.Func.MapForm.SerialCbsClrInfo serialInfo in MainModel.GetInstance().MainForm.LegendPanel.SerialCbsClrInfoList)
            {
                string strRange = serialInfo.countInfoDesc + "（" + serialInfo.range.RangeDescription + ")";
                worksheet.Cells[rowIndex, colIndex] = "█";
                Range excelRange = worksheet.get_Range(worksheet.Cells[rowIndex, colIndex], worksheet.Cells[rowIndex, colIndex]);
                excelRange.Font.Bold = true;
                excelRange.HorizontalAlignment = XlHAlign.xlHAlignRight;
                try
                {
                    Color color = ((DTParameterRangeColor)serialInfo.range).Value;
                    excelRange.Font.Color = System.Drawing.Color.FromArgb(color.A, color.B, color.G, color.R).ToArgb();//winform颜色与excel颜色略有不同
                }
                catch
                {
                    //continue
                }

                worksheet.Cells[rowIndex, colIndex + 1] = strRange;
                excelRange = worksheet.get_Range(worksheet.Cells[rowIndex, colIndex + 1], worksheet.Cells[rowIndex, colIndex + 4]);
                excelRange.Font.Bold = true;
                excelRange.HorizontalAlignment = XlHAlign.xlHAlignLeft;
                excelRange.Merge(excelRange.MergeCells);
                rowIndex++;
            }
            topPosition += 270;
            excelrow += 20;
        }
        private void saveTitleToExcel(Worksheet worksheet, ref int excelrow, ref float topPosition, string strTitle)
        {
            worksheet.Cells[excelrow, 1] = strTitle;
            Range excelRange = worksheet.get_Range(worksheet.Cells[excelrow, 1], worksheet.Cells[excelrow, 1]);
            excelRange.RowHeight = 22;
            excelRange.Font.Size = 16;
            excelRange.Font.Bold = true;
            excelrow += 2;
            topPosition += 35.6F;
        }


        protected virtual void addInDoorInfoToWookSheet(BtsAcceptInfo_SX<T, U> btsAcceptInfo, Worksheet worksheet)
        {
            worksheet.Name = "性能验收测试表格";
            List<NPOIRow> sumRows = btsAcceptInfo.GetKpiSumRows();
            saveNPOIRowsToWorksheet(worksheet, sumRows);
        }

        protected virtual void saveNPOIRowsToWorksheet(Worksheet worksheet, List<NPOIRow> sumRows)
        {
            if (sumRows == null)
            {
                return;
            }
            for (int i = 0; i < sumRows.Count; i++)
            {
                NPOIRow row = sumRows[i];
                if (row.cellValues != null)
                {
                    for (int j = 0; j < row.cellValues.Count; j++)
                    {
                        object valueObj = row.cellValues[j];
                        if (valueObj == null)
                        {
                            valueObj = "";
                        }
                        else if (((valueObj is float) && (float.IsNaN(((float)valueObj)))) || ((valueObj is double) && (double.IsNaN(((double)valueObj)))))
                        {
                            valueObj = "-";
                        }
                        worksheet.Cells[i + 1, j + 1] = valueObj.ToString();
                    }
                }
            }
        }
    }

    class ExportReportHelper_SX_LTE : ExportReportHelper_SX<LTECell, string>
    {
        protected override string rsrpParam { get { return "TD_LTE_RSRP"; } }
        protected override string sinrParam { get { return "TD_LTE_SINR"; } }
        protected override string dlParam { get { return "lte_PDCP_DL_Mb"; } }
        protected override string ulParam { get { return "lte_PDCP_UL_Mb"; } }

        protected override void clearCellSelection()
        {
            MainModel.GetInstance().SelectedLTECells.Clear();
        }

        protected override void setRangeByCell(List<LTECell> cellList,
        ref double maxLongitude, ref double minLongitude, ref double maxLatitude, ref double minLatitude)
        {
            foreach (LTECell cell in cellList)
            {
                getEdgeData(ref maxLongitude, ref minLongitude, ref maxLatitude, ref minLatitude, cell.EndPointLongitude, cell.EndPointLatitude);
            }
        }
    }

    class ExportReportHelper_SX_NR : ExportReportHelper_SX<NRCell, string>
    {
        protected override string rsrpParam { get { return "NR_SS_RSRP"; } }
        protected override string sinrParam { get { return "NR_SS_SINR"; } }
        protected override string dlParam { get { return "NR_Throughput_PDCP_DL_Mb"; } }
        protected override string ulParam { get { return "NR_Throughput_PDCP_UL_Mb"; } }

        protected override void clearCellSelection()
        {
            MainModel.GetInstance().SelectedNRCells.Clear();
        }

        protected override void setRangeByCell(List<NRCell> cellList,
        ref double maxLongitude, ref double minLongitude, ref double maxLatitude, ref double minLatitude)
        {
            foreach (NRCell cell in cellList)
            {
                getEdgeData(ref maxLongitude, ref minLongitude, ref maxLatitude, ref minLatitude, cell.EndPointLongitude, cell.EndPointLatitude);
            }
        }
    }
}
