﻿using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLTERRCReleaseAnaByFile : ZTLTERRCReleaseAnaBase
    {
        public ZTLTERRCReleaseAnaByFile(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected static readonly object lockObj = new object();
        private static ZTLTERRCReleaseAnaByFile intance = null;
        public static ZTLTERRCReleaseAnaByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTLTERRCReleaseAnaByFile(MainModel.GetInstance());
                    }
                }
            }
            return intance;
        }

        public ZTLTERRCReleaseAnaByFile(bool isVoLTE)
            : base(MainModel.GetInstance())
        {
            this.isVoLTE = isVoLTE;
        }

        public override string Name
        {
            get { return "LTE异频重定向(按文件)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22050, this.Name);//////
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }

    public class ZTLTERRCReleaseAnaByFile_FDD : ZTLTERRCReleaseAnaByFile
    {
        private static ZTLTERRCReleaseAnaByFile_FDD instance = null;
        public static new ZTLTERRCReleaseAnaByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTLTERRCReleaseAnaByFile_FDD(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public ZTLTERRCReleaseAnaByFile_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get { return "LTE_FDD异频重定向(按文件)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26048, this.Name);//////
        }
    }
}