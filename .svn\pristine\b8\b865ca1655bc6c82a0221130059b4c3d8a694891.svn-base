﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.SiteCellInfo
{
    public class CellSiteInfo
    {
        public int SN
        {
            get;
            private set;
        }
        public CellSiteInfo(ICell cell, SiteInfo site, double? dis, BTSSiteInfo bts,int sn)
        {
            this.Cell = cell;
            if (site!=null)
            {
                this.SiteCellCount = site.CellCount;
                this.SiteName = site.Name;
                this.SiteType = site.SiteType;
            }
            else
            {
                this.SiteName = string.Empty;
                this.SiteType = string.Empty;
            }
            this.Site = site;
            this.SiteDis = dis;
            this.BTSInfo = bts;
            this.SN = sn;
        }
        public SiteInfo Site
        {
            get;
            private set;
        }
        public double? SiteDis
        {
            get;
            private set;
        }
        public BTSSiteInfo BTSInfo { get; private set; }
        public ICell Cell
        { get; private set; }

        public string CityName
        {
            get { return this.BTSInfo.CityName; }
        }

        public string GridName
        { get { return this.BTSInfo.GridName; } }

        public int ENodeBID
        {
            get { return this.BTSInfo.BTS.BTSID; }
        }

        public string CellName
        {
            get { return this.Cell.Name; }
        }

        public int CellCode
        {
            get { return ((LTECell)Cell).SCellID; }
        }

        public int CellCount
        {
            get { return this.BTSInfo.OtherCellCount; }
        }

        public double CellAvgDis
        {
            get { return this.BTSInfo.OtherCellAvgDis; }
        }

        public double CellMinDis
        {
            get { return this.BTSInfo.OtherCellMinDis; }
        }

        public string SiteName
        { get;private set; }

        public string SiteType
        {
            get;
            private set;
        }

        public int? SiteCellCount
        {
            get;
            private set;
        }

    }

}
