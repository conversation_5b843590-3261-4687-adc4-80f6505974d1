﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsWeakRsrpSetting : LteMgrsConditionControlBase
    {
        public LteMgrsWeakRsrpSetting()
        {
            InitializeComponent();
            initValues();
        }

        public override string Title
        {
            get { return "弱覆盖区域"; }
        }

        public override object GetCondition(out string invalidReason)
        {
            invalidReason = null;
            return new object[] {
                (double)numRsrpMax.Value,
                (int)numGridCount.Value,
                (LteMgrsRsrpBandType)EnumDescriptionAttribute.Parse(typeof(LteMgrsRsrpBandType), cbxFreqType.SelectedItem as string),
            };
        }

        public override void SaveCondititon(XmlConfigFile xcfg)
        {
            XmlElement configWeakRsrp = xcfg.AddConfig("WeakRsrp");
            xcfg.AddItem(configWeakRsrp, "RsrpMax", (double)numRsrpMax.Value);
            xcfg.AddItem(configWeakRsrp, "GridCount", (int)numGridCount.Value);
            xcfg.AddItem(configWeakRsrp, "FreqType", cbxFreqType.Text);
        }

        private void initValues()
        {
            cbxFreqType.Items.Clear();
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsRsrpBandType.Top));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsRsrpBandType.SingleD));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsRsrpBandType.SingleF));
            cbxFreqType.SelectedIndex = 0;
            XmlConfigFile configFile = new MyXmlConfigFile(LteMgrsBaseSettingManager.Instance.ConfigPath);
            if (configFile.Load())
            {
                XmlElement configWeakRsrp = configFile.GetConfig("WeakRsrp");
                object obj = configFile.GetItemValue(configWeakRsrp, "RsrpMax");
                if (obj != null)
                {
                    numRsrpMax.Value = (decimal)(double)obj;
                }

                obj = configFile.GetItemValue(configWeakRsrp, "GridCount");
                if (obj != null)
                {
                    numGridCount.Value = (int)obj;
                }

                obj = configFile.GetItemValue(configWeakRsrp, "FreqType");
                if (obj != null)
                {
                    int index = cbxFreqType.Items.IndexOf(obj.ToString());
                    if (index != -1)
                    {
                        cbxFreqType.SelectedIndex = index;
                    }
                }
            }
        }
    }
}
