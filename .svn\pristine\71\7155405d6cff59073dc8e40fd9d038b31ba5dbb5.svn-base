﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.ES.Data;
using MasterCom.Util;
using DevExpress.XtraEditors;

namespace MasterCom.ES.UI
{
    public partial class ParamSettingDlg : BaseFormStyle
    {
        public ParamSettingDlg()
        {
            InitializeComponent();
        }
        public void FillParams(Dictionary<string, DTParameter> paraDic)
        {
            this.lbxParams.Items.Clear();
            foreach (DTParameter s in paraDic.Values)
            {
                this.lbxParams.Items.Add(s);
            }
        }
        public Dictionary<string, DTParameter> GetResultParaDic()
        {
            Dictionary<string, DTParameter> retDic = new Dictionary<string, DTParameter>();
            foreach (object o in lbxParams.Items)
            {
                DTParameter p = o as DTParameter;
                retDic[p.paramKey] = p;
            }
            return retDic;
        }
        private void btnAdd_Click(object sender, EventArgs e)
        {
            DTParameter p = new DTParameter();
            string s = tbxNewParam.Text.Trim();
            if (s.Length > 0)
            {
                p.paramKey = s;
            }
            else
            {
                XtraMessageBox.Show("Please Input Parameter Name!");
                return;
            }
            long min;
            long max;
            if (!long.TryParse(tbxMin.Text, out min))
            {
                XtraMessageBox.Show("Input Min Value!");
                return;
            }
            if (!long.TryParse(tbxMax.Text, out max))
            {
                XtraMessageBox.Show("Input Max Value!");
                return;
            }
            if (min > max)
            {
                XtraMessageBox.Show("make sure Min<=Max!");
                return;
            }
            p.minValue = min;
            p.maxValue = max;
            lbxParams.Items.Add(p);
            lbxParams.SelectedIndex = lbxParams.Items.Count - 1;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnDel_Click(object sender, EventArgs e)
        {
            if (lbxParams.SelectedIndex != -1)
            {
                lbxParams.Items.RemoveAt(lbxParams.SelectedIndex);
            }
        }
    }
}