﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTReportEventMngQuery_BJ : QueryBase
    {
        public ZTReportEventMngQuery_BJ(MainModel mainModel)
            : base(mainModel)
        {
            ZTSQLReportEventPreprocessQuery_BJ query = new ZTSQLReportEventPreprocessQuery_BJ(mainModel);
            query.Query();

            isPreprocess = query.RoleList.Contains(9999);
        }

        public static bool isPreprocess { get; set; } = false;//判断是否为网优中心预处理人员

        private List<ZTReportEventInfo_BJ> reportEventInfoList = null;
        private ReportEventCondition reportEventCond = null;

        ZTReportEventStatSetForm_BJ reportEventStatSetForm = null;
        private bool getCondition()
        {
            if (reportEventStatSetForm == null)
            {
                reportEventStatSetForm = new ZTReportEventStatSetForm_BJ(this.MainModel, 1);
            }

            if (reportEventStatSetForm.ShowDialog() == DialogResult.OK)
            {
                reportEventStatSetForm.GetCondition(out reportEventCond);
                reportEventCond.UserName = MainModel.User.LoginName;     //用户权限决定能看到的信息
            }
            else
            {
                return false;
            }

            return true;
        }


        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                if (!getCondition())
                {
                    return;
                }

                MainModel.ClearDTData();
                WaitBox.Show("开始查询异常事件...", queryInThread, clientProxy);
                MainModel.FireDTDataChanged(this);
                MainModel.RefreshLegend();
                fireShowFormAfterQuery();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected void queryInThread(object o)
        {
            try
            {
                reportEventInfoList = getReportEventInfoList(reportEventCond);
            }
            catch (Exception e)
            {
                ErrorInfo += e.Message;
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private List<ZTReportEventInfo_BJ> getReportEventInfoList(ReportEventCondition reportEventCond)
        {
            ZTSQLReportEventInfoQuery_BJ query = new ZTSQLReportEventInfoQuery_BJ(MainModel, reportEventCond);
            query.Query();

            return query.GetReportEventInfoList();
        }

        private void fireShowFormAfterQuery()
        {
            ZTReportEventMngListForm_BJ reportEventMngListForm
                = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTReportEventMngListForm_BJ)) as ZTReportEventMngListForm_BJ;
            if (reportEventMngListForm == null || reportEventMngListForm.IsDisposed)
            {
                reportEventMngListForm = new ZTReportEventMngListForm_BJ(MainModel);
            }
            reportEventMngListForm.FillData(reportEventInfoList);
            reportEventMngListForm.Owner = MainModel.MainForm;
            reportEventMngListForm.Visible = true;
            reportEventMngListForm.BringToFront();
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18019, this.Name);
        }
        
        public override string Name
        {
            get { return "BJ异常事件跟踪"; }
        }

        public override string IconName
        {
            get { return "Images/cellquery.gif"; }
        }
    }

    public class ReportEventCondition
    {
        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }
        public string NetType { get; set; }
        public string UserName { get; set; }  
        public Dictionary<string, int> DeviceDic { get; set; }//设备列表  
        public Dictionary<int, string> ProjDic { get; set; }//项目列表
        public bool IsShieldOther { get; set; }
    }
}