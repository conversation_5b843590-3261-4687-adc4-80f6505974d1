﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryPoorRsrqRoad_Scan : QueryPoorRsrqRoad
    {
        protected override string themeName { get { return "LTE_SCAN:LTESCAN_TopN_CELL_Specific_RSRQ"; } }

        public QueryPoorRsrqRoad_Scan(ServiceName serviceName)
            : base(serviceName)
        {
        }

        public override string Name
        {
            get { return "低RSRQ路段_NBIOT扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33020, this.Name);
        }

        protected override float? getSinr(TestPoint tp)
        {
            return (float?)tp["LTESCAN_TopN_CELL_Specific_RSSINR", 0];
        }

        protected override float? getRsrp(TestPoint tp)
        {
            return (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", 0];
        }

        protected override float? getRsrq(TestPoint tp)
        {
            return (float?)tp["LTESCAN_TopN_CELL_Specific_RSRQ", 0];
        }
    }
}
