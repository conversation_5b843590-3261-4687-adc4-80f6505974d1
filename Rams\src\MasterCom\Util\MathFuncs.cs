﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MasterCom.MTGis;

namespace MasterCom.Util
{
    public class MathFuncs
    {
        public static double GetDistance(double x, double y, double x2, double y2)
        {
            double longitudeDistance = (Math.Sin((90 - y2) * 2 * Math.PI / 360) + Math.Sin((90 - y) * 2 * Math.PI / 360)) / 2 * (x2 - x) / 360 * 40075360;
            double latitudeDistance = (y2 - y) / 360 * 39940670;
            return Math.Sqrt(longitudeDistance * longitudeDistance + latitudeDistance * latitudeDistance);
        }
        /// <summary>
        /// 计算点到线段的距离
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <param name="x1"></param>
        /// <param name="y1"></param>
        /// <param name="x2"></param>
        /// <param name="y2"></param>
        /// <returns></returns>
        public static double PointToSegDist(double x, double y, double x1, double y1, double x2, double y2)
        {
            //----------图2--------------------   
            double a, b, c;
            a = GetPointDistance(x2,y2, x,y);
            if (a <= 0.00001)
                return 0.0f;
            b = GetPointDistance(x1,y1, x,y);
            if (b <= 0.00001)
                return 0.0f;
            c = GetPointDistance(x1,y1, x2,y2);
            if (c <= 0.00001)
                return a;//如果PA和PB坐标相同，则退出函数，并返回距离   
            //------------------------------   


            if (a * a >= b * b + c * c)//--------图3--------   
                return b;
            if (b * b >= a * a + c * c)//--------图4-------   
                return a;

            //图1   
            double l = (a + b + c) / 2;     //周长的一半   
            double s = Math.Sqrt(l * (l - a) * (l - b) * (l - c));  //海伦公式求面积   
            return 2 * s / c;
        }
        private static double GetPointDistance(double x1, double y1, double x2,double y2)
        {
            return Math.Sqrt((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2));
        }

        /// <summary>
        /// 计算两点连成的线与正北方向顺时针的夹角
        /// </summary>
        /// <param name="x1"></param>
        /// <param name="y1"></param>
        /// <param name="x2"></param>
        /// <param name="y2"></param>
        /// <returns></returns>
        public static int getAngleFromPointToPoint(double x1, double y1, double x2, double y2)
        {
            double alpha = getAngleFromPointToPoint_D(x1, y1, x2, y2);
            return (int)alpha;
        }

        private static double getAlpha(double x1, double y1, double x2, double y2, double atan)
        {
            double alpha = 0;
            if (x2 > x1 && y2 > y1)
            {
                alpha = 90 - atan;
            }
            else if (x2 > x1 && y2 < y1)
            {
                alpha = 90 - atan;
            }
            else if (x2 < x1 && y2 < y1)
            {
                alpha = 270 - atan;
            }
            else if (x2 < x1 && y2 > y1)
            {
                alpha = 270 - atan;
            }
            else if (x2 > x1)//y2 == y1
            {
                alpha = 90;
            }
            else if (x2 < x1)//y2 == y1
            {
                alpha = 270;
            }
            else if (y2 > y1)//x2 == x1
            {
                alpha = 0;
            }
            else if (y2 < y1)//x2 == x1
            {
                alpha = 180;
            }

            return alpha;
        }

        /// <summary>
        /// 计算两点连成的线与正北方向顺时针的夹角
        /// </summary>
        /// <param name="x1"></param>
        /// <param name="y1"></param>
        /// <param name="x2"></param>
        /// <param name="y2"></param>
        /// <returns></returns>
        public static double getAngleFromPointToPoint_D(double x1, double y1, double x2, double y2)
        {
            double atan = Math.Atan((y2 - y1) / (x2 - x1)) * 180 / Math.PI;
            double alpha = getAlpha(x1, y1, x2, y2, atan);
            return alpha;
        }

        /// <summary>
        /// 判断点2是否在点1覆盖方向上，默认覆盖范围为前方120度内
        /// </summary>
        /// <param name="x1">点1</param>
        /// <param name="y1">点1</param>
        /// <param name="x2">点2</param>
        /// <param name="y2">点2</param>
        /// <param name="angle_A">点1的覆盖方向</param>
        /// <returns></returns>
        public static bool JudgePoint(double x1, double y1, double x2, double y2, int angle_A)
        {
            int default_angelRange = 60;
            return JudgePoint(x1, y1, x2, y2, angle_A, default_angelRange);
        }
        /// <summary>
        /// 判断点2是否在点1覆盖方向上
        /// </summary>
        /// <param name="x1">点1</param>
        /// <param name="y1">点1</param>
        /// <param name="x2">点2</param>
        /// <param name="y2">点2</param>
        /// <param name="angle_A">点1的覆盖方向</param>
        /// <param name="angle_range">在覆盖方向上的判断角度范围</param>
        /// <returns></returns>
        public static bool JudgePoint(double x1, double y1, double x2, double y2, int angle_A, int angle_range)
        {
            double alpha = 0;
            double atan = Math.Atan((y2 - y1) / (x2 - x1)) * 180 / Math.PI;

            alpha = getAlpha(x1, y1, x2, y2, atan);

            if ((Math.Abs((alpha - angle_A)) <= angle_range) || ((360 - Math.Abs((alpha - angle_A))) <= angle_range))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 判断点2是否在点1覆盖方向上
        /// </summary>
        /// <param name="x1">点1</param>
        /// <param name="y1">点1</param>
        /// <param name="x2">点2</param>
        /// <param name="y2">点2</param>
        /// <param name="angle_A">点1的覆盖方向</param>
        /// <param name="angle_range_min">在覆盖方向上的判断角度范围最小值</param>
        /// <param name="angle_range_max">在覆盖方向上的判断角度范围最大值</param>
        /// <returns></returns>
        public static bool JudgePoint(double x1, double y1, double x2, double y2, int angle_A, int angle_range_min, int angle_range_max)
        {
            double alpha = 0;
            double atan = Math.Atan((y2 - y1) / (x2 - x1)) * 180 / Math.PI;

            alpha = getAlpha(x1, y1, x2, y2, atan);

            if ((Math.Abs(alpha - angle_A) >= angle_range_min) && (Math.Abs(alpha - angle_A) <= angle_range_max))
            {
                return true;
            }
            else if (((360 - Math.Abs(alpha - angle_A)) >= angle_range_min) && ((360 - Math.Abs(alpha - angle_A)) <= angle_range_max))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        internal static void drawTest(System.Drawing.Graphics g, Pen arcPen, PointF point1, PointF point2, PointF point3)
        {
            float x1, y1, x2, y2, y3, x3;
            x1 = point1.X;
            y1 = point1.Y;
            x2 = point2.X;
            y2 = point2.Y;
            x3 = point3.X;
            y3 = point3.Y;

            float x0, y0, r0;//圆心和半径 
            float m1, m2, mx1, mx2, my1, my2;
            if (Math.Abs(y2 - y1) < 0.0001)
            {
                m2 = -(x3 - x2) / (y3 - y2);
                mx2 = (x2 + x3) / 2.0f;
                my2 = (y2 + y3) / 2.0f;
                x0 = (x1 + x2) / 2.0f;
                y0 = m2 * (x0 - mx2) + my2;
            }
            else if (Math.Abs(y3 - y2) < 0.0001)
            {
                m1 = -(x2 - x1) / (y2 - y1);
                mx1 = (x1 + x2) / 2.0f;
                my1 = (y1 + y2) / 2.0f;
                x0 = (x2 + x3) / 2.0f;
                y0 = m1 * (x0 - mx1) + my1;
            }
            else
            {
                m1 = -(x2 - x1) / (y2 - y1);
                m2 = -(x3 - x2) / (y3 - y2);
                mx1 = (x1 + x2) / 2.0f;
                mx2 = (x2 + x3) / 2.0f;
                my1 = (y1 + y2) / 2.0f;
                my2 = (y2 + y3) / 2.0f;
                x0 = (m1 * mx1 - m2 * mx2 + my2 - my1) / (m1 - m2);
                y0 = m1 * (x0 - mx1) + my1;
            }

            r0 = Convert.ToSingle(Math.Sqrt((x0 - x1) * (x0 - x1) + (y0 - y1) * (y0 - y1)));
            //g.DrawEllipse(new Pen(Color.Red,2), new RectangleF(x1, y1, 10, 10));
            //g.DrawEllipse(new Pen(Color.Lime,2), new RectangleF(x2, y2, 10, 10));
            //g.DrawEllipse(new Pen(Color.Blue,2), new RectangleF(x3, y3, 10, 10));
            //画弧 
            float k1, k2, k3;//斜率 
            k1 = (y1 - y0) / (x1 - x0);
            k2 = (y2 - y0) / (x2 - x0);
            k3 = (y3 - y0) / (x3 - x0);

            double[] a = new double[3];//弧度 
            double[] b = new double[3];//角度 
            double[] x = new double[3];
            double v = 180.0 / 3.14159;//弧度与角度 转换 
            a[0] = Math.Atan(k1);
            a[1] = Math.Atan(k2);
            a[2] = Math.Atan(k3);

            x[0] = x1;
            x[1] = x2;
            x[2] = x3;
            setAngle(x0, a, b, x, v);
            float sweepAngle = (float)(b[2] - b[0]);
            if (sweepAngle < 0)
            {
                sweepAngle += 360;
            }
            else if (sweepAngle > 180)
            {
                sweepAngle -= 360;
            }
            g.DrawArc(arcPen, new RectangleF(x0 - r0, y0 - r0, 2 * r0, 2 * r0), (float)b[0], sweepAngle);
        }

        private static void setAngle(float x0, double[] a, double[] b, double[] x, double v)
        {
            for (int i = 0; i < 3; i++)
            {
                if (a[i] >= 0)
                {
                    if (x[i] > x0)
                    {
                        b[i] = a[i] * v;
                    }
                    else if (x[i] < x0)
                    {
                        b[i] = 180.0 + a[i] * v;
                    }
                }
                else
                {
                    a[i] = Math.Abs(a[i]);
                    if (x[i] > x0)
                    {
                        b[i] = 360.0 - a[i] * v;
                    }
                    else if (x[i] < x0)
                    {
                        b[i] = 180.0 - a[i] * v;
                    }
                }
            }
        }

        /// <summary>
        /// 根据余弦定理求两个线段夹角
        /// </summary>
        /// <param name="o">端点</param>
        /// <param name="s">第一点</param>
        /// <param name="e">第二点</param>
        /// <returns></returns>
        public static double CalAngle(PointF o, PointF s, PointF e)
        {
            double cosfi = 0, fi = 0, norm = 0;
            double dsx = s.X - o.X;
            double dsy = s.Y - o.Y;
            double dex = e.X - o.X;
            double dey = e.Y - o.Y;

            cosfi = dsx * dex + dsy * dey;
            
            norm = (dsx * dsx + dsy * dsy) * (dex * dex + dey * dey);
            if (norm==0)
                return 0;

            cosfi /= Math.Sqrt(norm);

            if (cosfi >= 1.0) 
                return 0;
            if (cosfi <= -1.0) 
                return 180;
            fi = Math.Acos(cosfi);

            if (180 * fi / Math.PI < 180)
            {
                return 180 * fi / Math.PI;
            }
            else
            {
                return 360 - 180 * fi / Math.PI;
            }
        }

        //public static double CalAngle(double sx1, double sy1, double sx2, double sy2,
        //                              double tx1, double ty1, double tx2, double ty2)
        //{
        //    double _tx2 = tx2 - (tx1 - sx1);
        //    double _ty2 = ty2 - (ty1 - sy1);
        //    return CalAngle(new PointF((float)sx1, (float)sy1), new PointF((float)sx2, (float)sy2), new PointF((float)_tx2, (float)_ty2));
        //}

        #region InPoly(PointF[] poly, DbPoint p) 判断多边形是否包含某个点
        /// <summary>
        /// 判断多边形是否包含某个点
        /// </summary>
        /// <param name="poly">多边形边框上每个角的点坐标数组</param>
        /// <param name="p">要进行判断的点</param>
        /// <returns>true:包含; false:不包含</returns>
        public static bool InPoly(PointF[] poly, DbPoint p)
        {
            int i = 0, f = 0;
            double xi = 0, a = 0, b = 0, c = 0;
            PointF ps, pe;
            int uboundValue = poly.GetLength(0);
            for (i = 0; i < uboundValue; i++)
            {
                ps = poly[i];
                if (i < uboundValue - 1)
                {
                    pe = poly[i + 1];
                }
                else
                {
                    pe = poly[0];
                }
                getStdLine(ps, pe, ref a, ref b, ref c);
                if (a != 0)
                {
                    xi = 0 - ((b * p.y + c) / a);
                    if (xi == p.x)
                    {
                        return true;
                    }
                    else if (xi < p.x)
                    {
                        f = f + sgn((int)(pe.Y - p.y)) - sgn((int)(ps.Y - p.y));
                    }
                }
            }
            return f != 0;
        }

        //根据两个点的坐标求经过两点的直线的标准方程参数A、B、C
        private static void getStdLine(PointF ps, PointF pe, ref double a, ref double b, ref double c)
        {
            double p1, p2;
            p1 = (ps.X * pe.Y);
            p2 = (pe.X * ps.Y);
            if (p1 == p2)
            {
                getSameStdLine(ps, pe, ref a, ref b, ref c);
            }
            else
            {
                getDifferentStdLine(ps, pe, out a, out b, out c, p1, p2);
            }
        }

        private static void getSameStdLine(PointF ps, PointF pe, ref double a, ref double b, ref double c)
        {
            if (ps.X == 0)
            {
                if (pe.X == 0)
                {
                    a = 1;
                    b = 0;
                    c = 0;
                }
                else if (ps.Y == 0)
                {
                    a = pe.Y;
                    b = 0 - pe.X;
                    c = 0;
                }
            }
            else if (pe.Y == 0)
            {
                if (ps.Y == 0)
                {
                    a = 0;
                    b = 1;
                    c = 0;
                }
                else if (pe.X == 0)
                {
                    a = 0 - ps.Y;
                    b = ps.X;
                    c = 0;
                }
            }
        }

        private static void getDifferentStdLine(PointF ps, PointF pe, out double a, out double b, out double c, double p1, double p2)
        {
            a = (ps.Y - pe.Y) / (p1 - p2);
            c = 1;
            if (ps.Y == 0)
            {
                if (pe.Y == 0)
                {
                    b = 1;
                    c = 0;
                }
                else
                {
                    b = 0 - ((a * pe.X + 1) / pe.Y);
                }
            }
            else
            {
                b = 0 - ((a * ps.X + 1) / ps.Y);
            }
        }

        private static int sgn(int a)
        {
            if (a == 0)
            {
                return 0;
            }
            else if (a < 0)
            {
                return -1;
            }
            else
            {
                return 1;
            }
        }
        #endregion

        /// <summary>
        /// 功能：判断点是否在多边形内 
        /// 方法：求解通过该点的水平线与多边形各边的交点 
        /// 结论：单边交点为奇数，成立! 
        /// 参数： 返回1表示肯定在多边形内；-1肯定不在多边形内；0表示在多边形的边上；
        /// Point p 指定的某个点 
        /// Point[] ptPolygon 多边形的各个顶点坐标（首末点可以不一致） 
        /// int nCount 多边形定点的个数 
        /// </summary>
        /// <param name="p"></param>
        /// <param name="ptPolygon"></param>
        /// <returns></returns>
        public static int PtInPolygon(PointF p, PointF[] ptPolygon)
        {
            int nCount = ptPolygon.Length;
            bool isBeside = false;// 记录是否在多边形的边上

            #region 矩形外区域
            if (nCount > 0)
            {
                double maxx = ptPolygon[0].X;
                double minx = ptPolygon[0].X;
                double maxy = ptPolygon[0].Y;
                double miny = ptPolygon[0].Y;

                getSideRange(ptPolygon, nCount, ref maxx, ref minx, ref maxy, ref miny);

                if ((p.X > maxx) || (p.X < minx) || (p.Y > maxy) || (p.Y < miny))
                    return -1;
            }
            #endregion

            #region 射线法
            int nCross = 0;
            judgeBeside(p, ptPolygon, nCount, ref isBeside, ref nCross);

            if (isBeside)
                return 0;//多边形边上
            else if (nCross % 2 == 1)// 单边交点为偶数，点在多边形之外 --- 
                return 1;//多边形内

            return -1;//多边形外
            #endregion
        }

        private static void judgeBeside(PointF p, PointF[] ptPolygon, int nCount, ref bool isBeside, ref int nCross)
        {
            List<double> crossList = new List<double>();    //在多边形顶点的交点坐标的X，避免重复计算
            for (int i = 0; i < nCount; i++)
            {
                PointF p1 = ptPolygon[i];
                PointF p2 = ptPolygon[(i + 1) % nCount];

                // 求解 y=p.y 与 p1p2 的交点

                if (p1.Y == p2.Y && p.Y == p1.Y && p.X >= Math.Min(p1.X, p2.X) && p.X <= Math.Max(p1.X, p2.X)) // p1p2 与 y=p0.y平行 
                {
                    isBeside = true;
                    continue;
                }

                if (p.Y < Math.Min(p1.Y, p2.Y) || p.Y > Math.Max(p1.Y, p2.Y)) // 交点在p1p2延长线上 
                    continue;


                // 求交点的 X 坐标 -------------------------------------------------------------- 
                double x = (double)(p.Y - p1.Y) * (double)(p2.X - p1.X) / (double)(p2.Y - p1.Y) + p1.X;

                if (x > p.X)
                {
                    nCross = addCross(nCross, crossList, p1, p2, x);
                }
                else if (x == p.X)
                    isBeside = true;
            }
        }

        private static int addCross(int nCross, List<double> crossList, PointF p1, PointF p2, double x)
        {
            if (x == p1.X || x == p2.X)
            {
                if (!crossList.Contains(x))
                {
                    crossList.Add(x);
                    nCross++; // 只统计单边交点 
                }
            }
            else
            {
                nCross++; // 只统计单边交点 
            }

            return nCross;
        }

        /// <summary>
        /// 利用系统函数判断点是否在多边形内
        /// </summary>
        /// <returns></returns>
        public static bool PointInBox(PointF[] p, PointF p1)
        {
            System.Drawing.Drawing2D.GraphicsPath gp = new System.Drawing.Drawing2D.GraphicsPath();
            Region r = new Region();
            gp.Reset();
            gp.AddPolygon(p);
            r.MakeEmpty();
            r.Union(gp); //把p1放入多边形,如果看不见了,就是在外;如果能看见,就是在内.
            if (r.IsVisible(p1))
                return true;
            return false;
        }

        /// <summary>
        /// 计算两条直线的交点，延伸直线
        /// </summary>
        /// <param name="p1">L1的点1坐标</param>
        /// <param name="p2">L1的点2坐标</param>
        /// <param name="p3">L2的点1坐标</param>
        /// <param name="p4">L2的点2坐标</param>
        /// <returns></returns>
        public static PointF? GetIntersectionOfTwoLine(PointF p1, PointF p2, PointF p3, PointF p4)
        {
            /*
             * L1，L2都存在斜率的情况：
             * 直线方程L1: ( y - y1 ) / ( y2 - y1 ) = ( x - x1 ) / ( x2 - x1 ) 
             * => y = [ ( y2 - y1 ) / ( x2 - x1 ) ]( x - x1 ) + y1
             * 令 a = ( y2 - y1 ) / ( x2 - x1 )
             * 有 y = a * x - a * x1 + y1   .........1
             * 直线方程L2: ( y - y3 ) / ( y4 - y3 ) = ( x - x3 ) / ( x4 - x3 )
             * 令 b = ( y4 - y3 ) / ( x4 - x3 )
             * 有 y = b * x - b * x3 + y3 ..........2
             * 
             * 如果 a = b，则两直线平等，否则， 联解方程 1,2，得:
             * x = ( a * x1 - b * x3 - y1 + y3 ) / ( a - b )
             * y = a * x - a * x1 + y1
             * 
             * L1存在斜率, L2平行Y轴的情况：
             * x = x3
             * y = a * x3 - a * x1 + y1
             * 
             * L1 平行Y轴，L2存在斜率的情况：
             * x = x1
             * y = b * x - b * x3 + y3
             * 
             * L1与L2都平行Y轴的情况：
             * 如果 x1 = x3，那么L1与L2重合，否则平等
             * 
            */
            float a = 0, b = 0;
            int state = 0;
            if (p1.X != p2.X)
            {
                a = (p2.Y - p1.Y) / (p2.X - p1.X);
                state |= 1;
            }
            if (p3.X != p4.X)
            {
                b = (p4.Y - p3.Y) / (p4.X - p3.X);
                state |= 2;
            }
            switch (state)
            {
                case 0: //L1与L2都平行Y轴
                    {
                        return null;
                        //if (p1.X == p3.X)
                        //{
                        //    throw new Exception("两条直线互相重合，且平行于Y轴，无法计算交点。");
                        //}
                        //else
                        //{
                        //    throw new Exception("两条直线互相平行，且平行于Y轴，无法计算交点。");
                        //}
                    }
                case 1: //L1存在斜率, L2平行Y轴
                    {
                        float x = p3.X;
                        float y = a * x - a * p1.X + p1.Y;
                        return new PointF(x, y);
                    }
                case 2: //L1 平行Y轴，L2存在斜率
                    {
                        float x = p1.X;
                        float y = b * x + b * p3.X + p3.Y;
                        return new PointF(x, y);
                    }
                case 3: //L1，L2都存在斜率
                    {
                        if (a == b)
                        {
                            return null;
                            //throw new Exception("两条直线平行或重合，无法计算交点。");
                        }
                        float x = (a * p1.X - b * p3.X - p1.Y + p3.Y) / (a - b);
                        float y = a * x - a * p1.X + p1.Y;
                        return new PointF(x, y);
                    }
            }
            return null;
        }

        /// <summary>
        ///  计算两条直线的交点，不延伸直线
        /// </summary>
        /// <param name="p1"></param>
        /// <param name="p2"></param>
        /// <param name="p3"></param>
        /// <param name="p4"></param>
        /// <returns></returns>
        public static PointF? GetIntersectionOfTwoLineNotExtend(PointF p1, PointF p2, PointF p3, PointF p4)
        {
            PointF? pointInter = GetIntersectionOfTwoLine(p1, p2, p3, p4);
            if (pointInter == null)
            {
                return null;
            }
            float xMinLine1 = Math.Min(p1.X, p2.X);
            float xMaxLine1 = Math.Max(p1.X, p2.X);
            float yMinLine1 = Math.Min(p1.Y, p2.Y);
            float yMaxLine1 = Math.Max(p1.Y, p2.Y);
            float xMinLine2 = Math.Min(p3.X, p4.X);
            float xMaxLine2 = Math.Max(p3.X, p4.X);
            float yMinLine2 = Math.Min(p3.Y, p4.Y);
            float yMaxLine2 = Math.Max(p3.Y, p4.Y);
            PointF p = (PointF)pointInter;
            if (p.X >= xMinLine1 && p.X <= xMaxLine1 && p.Y >= yMinLine1 && p.Y <= yMaxLine1 &&
                p.X >= xMinLine2 && p.X <= xMaxLine2 && p.Y >= yMinLine2 && p.Y <= yMaxLine2)
            {
                return pointInter;
            }
            return null;
        }

        /// <summary>
        ///  计算两条直线的交点，第二条直线不延伸
        /// </summary>
        /// <param name="p1"></param>
        /// <param name="p2"></param>
        /// <param name="p3"></param>
        /// <param name="p4"></param>
        /// <returns></returns>
        public static PointF? GetIntersectionOfTwoLineTheSecondNotExtend(PointF p1, PointF p2, PointF p3, PointF p4)
        {
            PointF? pointInter = GetIntersectionOfTwoLine(p1, p2, p3, p4);
            if (pointInter == null)
            {
                return null;
            }
            //float xMinLine1 = Math.Min(p1.X, p2.X);
            //float xMaxLine1 = Math.Max(p1.X, p2.X);
            //float yMinLine1 = Math.Min(p1.Y, p2.Y);
            //float yMaxLine1 = Math.Max(p1.Y, p2.Y);
            float xMinLine2 = Math.Min(p3.X, p4.X);
            float xMaxLine2 = Math.Max(p3.X, p4.X);
            float yMinLine2 = Math.Min(p3.Y, p4.Y);
            float yMaxLine2 = Math.Max(p3.Y, p4.Y);
            PointF p = (PointF)pointInter;
            if (p.X >= xMinLine2 && p.X <= xMaxLine2 && p.Y >= yMinLine2 && p.Y <= yMaxLine2)
            {
                return pointInter;
            }
            return null;
        }

        /// <summary>
        /// 获取p1,p2直线p1 -> p2方向上很远的一点
        /// </summary>
        /// <param name="p1"></param>
        /// <param name="p2"></param>
        /// <param name="dir">方向：1.xMin, 2.xMax, 3.yMin, 4.yMax</param>
        /// <returns></returns>
        public static PointF? GetPointOnLineFar(PointF p1, PointF p2, int dir)
        {
            /*
             * 直线方程斜截式：Y=K * X + B (K!=0)
             * B = Y - K * X;
             * y1 - K * x1 = y2 - K * x2
             * K * (x2 - x1) = y2 - y1
             * K = (y2 - y1) / (x2 - x1)
             * B = y1 - x1 * (y2 - y1) / (x2 - x1)
             * 
            */
            if (p1 == p2)
            {
                return null;
            }
            //float xMax = 100000;
            //float xMin = -100000;
            //float yMax = 100000;
            //float yMin = -100000;
            float xMax = 180;   //经纬度的时候
            float xMin = 0;
            float yMax = 180;
            float yMin = 0;
            if (p2.X == p1.X)   //斜率不存在
            {
                if (dir == 3)
                {
                    return new PointF(p1.X, yMin);
                }
                else if (dir == 4)
                {
                    return new PointF(p1.X, yMax);
                }
            }
            if (p1.Y == p2.Y)   //平行于X轴
            {
                if (dir == 1)
                {
                    return new PointF(xMin, p1.Y);
                }
                else if (dir == 2)
                {
                    return new PointF(xMax, p1.Y);
                }
            }
            if (dir == 1)
            {
                float K;
                float B;
                getSlope(p2.X >= p1.X, p1, p2, out K, out B);
                float newX = xMin;
                float newY = K * newX + B;
                return new PointF(newX, newY);
            }
            else if (dir == 2)
            {
                float K;
                float B;
                getSlope(p2.X <= p1.X, p1, p2, out K, out B);
                float newX = xMax;
                float newY = K * newX + B;
                return new PointF(newX, newY);
            }
            else if (dir == 3)
            {
                float K;
                float B;
                getSlope(p2.Y >= p1.Y, p1, p2, out K, out B);
                float newY = yMin;
                float newX = (newY - B) / K;
                return new PointF(newX, newY);
            }
            else if (dir == 4)
            {
                float K;
                float B;
                getSlope(p2.Y <= p1.Y, p1, p2, out K, out B);
                float newY = yMax;
                float newX = (newY - B) / K;
                return new PointF(newX, newY);
            }
            return null;
        }

        private static void getSlope(bool flag,PointF p1, PointF p2, out float K, out float B)
        {
            if (flag)
            {
                K = (p2.Y - p1.Y) / (p2.X - p1.X);
                B = p1.Y - p1.X * K;
            }
            else
            {
                K = (p1.Y - p2.Y) / (p1.X - p2.X);
                B = p2.Y - p2.X * K;
            }
        }

        /// <summary>
        /// 判断点相对于直线的状态
        /// </summary>
        /// <param name="dPtX"></param>
        /// <param name="dPtY"></param>
        /// <param name="dLineX1"></param>
        /// <param name="dLineY1"></param>
        /// <param name="dLineX2"></param>
        /// <param name="dLineY2"></param>
        /// <returns>0:在线上　1：在线的左边　2：在线的右边 3：在线的上面　4：在线的下面</returns>
        public static int PointToLinePosition(double ptX, double ptY, double lineX1, double lineY1, double lineX2, double lineY2)
        {
            if (lineX2 == lineX1)   //不存在斜率，即与X轴垂直
            {
                return judgeXPosition(ptX, lineX1);
            }
            double K = (lineY2 - lineY1) / (lineX2 - lineX1);
            double B = lineY1 - lineX1 * K;
            int angle = 0;
            if (lineX1 < lineX2)
            {
                angle = getAngleFromPointToPoint(lineX1, lineY1, lineX2, lineY2);
            }
            else
            {
                angle = getAngleFromPointToPoint(lineX2, lineY2, lineX1, lineY1);
            }
            if (angle <= 45 || angle > 135)
            {
                double xTmp = (ptY - B) / K;
                return judgeXPosition(ptX, xTmp);
            }
            else
            {
                double yTmp = K * ptX + B;
                return judgeYPosition(ptY, yTmp);
            }
        }

        private static int judgeYPosition(double pt, double line)
        {
            if (pt < line)
            {
                return 3;
            }
            else if (pt == line)
            {
                return 0;
            }
            else
            {
                return 4;
            }
        }

        private static int judgeXPosition(double pt, double line)
        {
            if (pt < line)
            {
                return 1;
            }
            else if (pt == line)
            {
                return 0;
            }
            else
            {
                return 2;
            }
        }

        public static bool PointOnLine(PointF point, PointF pointLine1, PointF pointLine2)
        {
            float xMin = Math.Min(pointLine1.X, pointLine2.X);
            float xMax = Math.Max(pointLine1.X, pointLine2.X);
            float yMin = Math.Min(pointLine1.Y, pointLine2.Y);
            float yMax = Math.Max(pointLine1.Y, pointLine2.Y);
            if (point.X >= xMin && point.X <= xMax && point.Y >= yMin && point.Y <= yMax)
            {
                if (pointLine1.X == pointLine2.X)
                {
                    if (point.X == pointLine1.X)
                    {
                        return true;
                    }
                }
                else
                {
                    float K = (pointLine2.Y - pointLine1.Y) / (pointLine2.X - pointLine1.X);
                    float B = pointLine1.Y - pointLine1.X * K;
                    float yTmp = K * point.X + B;
                    if (Math.Round(yTmp, 2) == Math.Round(point.Y, 2))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 约等于
        /// </summary>
        /// <param name="x1"></param>
        /// <param name="x2"></param>
        /// <param name="roundCount"></param>
        /// <returns></returns>
        public static bool EqualsApproximately(double x1, double x2, int roundCount)
        {
            if (Math.Abs(Round(x1, roundCount) * Math.Pow(10, roundCount)) - Math.Abs(Round(x2, roundCount) * Math.Pow(10, roundCount)) <= 1)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// WeilerAtherton区域裁剪法,被裁剪区域和裁剪区域的点都是顺时针排序
        /// </summary>
        /// <param name="oldRegion">被裁剪区域</param>
        /// <param name="clipRegion">裁剪区域</param>
        /// <returns>裁剪出的多个区域(如果是凹多边形，可能会有多块)</returns>
        public static List<PointF[]> GetWeilerAthertonRegion(List<PointF> oldRegion, List<PointF> clipRegion)
        {
            PointD[] oldRegions = new PointD[oldRegion.Count];
            for (int i = 0; i < oldRegion.Count; i++)
            {
                oldRegions[i] = new PointD(oldRegion[i].X, oldRegion[i].Y);
            }
            PointD[] clipRegions = new PointD[clipRegion.Count];
            for (int i = 0; i < clipRegion.Count; i++)
            {
                clipRegions[i] = new PointD(clipRegion[i].X, clipRegion[i].Y);
            }
            WeilerAthertonManager manager = new WeilerAthertonManager(oldRegions, clipRegions);
            List<PointF[]> refList = manager.GetResult();
            return refList;
        }

        /// <summary>
        /// 功能：判断点是否在多边形内(建议使用)
        /// 方法：求解通过该点的水平线与多边形各边的交点 
        /// 结论：单边交点为奇数，成立! 
        /// 参数： 返回1表示肯定在多边形内；-1肯定不在多边形内；0表示在多边形的边上；
        /// Point p 指定的某个点 
        /// Point[] ptPolygon 多边形的各个顶点坐标（首末点可以不一致） 
        /// int nCount 多边形定点的个数 
        /// </summary>
        /// <param name="p"></param>
        /// <param name="ptPolygon"></param>
        /// <returns></returns>
        public static int PointInPolygon(PointF p, PointF[] ptPolygon, int roundCount)
        {
            int nCount = ptPolygon.Length;
            bool isBeside = false;// 记录是否在多边形的边上

            #region 矩形外区域
            if (nCount > 0)
            {
                double maxx = ptPolygon[0].X;
                double minx = ptPolygon[0].X;
                double maxy = ptPolygon[0].Y;
                double miny = ptPolygon[0].Y;

                getSideRange(ptPolygon, nCount, ref maxx, ref minx, ref maxy, ref miny);

                if ((Round(p.X, roundCount) > Round(maxx, roundCount)) || (Round(p.X, roundCount) < Round(minx, roundCount)) ||
                    (Round(p.Y, roundCount) > Round(maxy, roundCount)) || (Round(p.Y, roundCount) < Round(miny, roundCount)))
                    return -1;
            }
            #endregion

            #region 射线法
            int nCross = 0;
            judgeBeside(p, ptPolygon, roundCount, nCount, ref isBeside, ref nCross);

            if (isBeside)
                return 0;//多边形边上
            else if (nCross % 2 == 1)// 单边交点为偶数，点在多边形之外 --- 
                return 1;//多边形内

            return -1;//多边形外
            #endregion
        }

        private static void getSideRange(PointF[] ptPolygon, int nCount, ref double maxx, ref double minx, ref double maxy, ref double miny)
        {
            for (int j = 1; j < nCount; j++)
            {
                if (ptPolygon[j].X >= maxx)
                    maxx = ptPolygon[j].X;
                else if (ptPolygon[j].X <= minx)
                    minx = ptPolygon[j].X;

                if (ptPolygon[j].Y >= maxy)
                    maxy = ptPolygon[j].Y;
                else if (ptPolygon[j].Y <= miny)
                    miny = ptPolygon[j].Y;
            }
        }

        private static void judgeBeside(PointF p, PointF[] ptPolygon, int roundCount
            , int nCount, ref bool isBeside, ref int nCross)
        {
            for (int i = 0; i < nCount; i++)
            {
                PointF p1 = ptPolygon[i];
                PointF p2 = ptPolygon[(i + 1) % nCount];

                // 求解 y=p.y 与 p1p2 的交点

                // p1p2 与 y=p0.y平行 
                if (p1.Y == p2.Y && Round(p.Y, roundCount) == Round(p1.Y, roundCount) &&
                    Round(p.X, roundCount) >= Math.Min(Round(p1.X, roundCount), Round(p2.X, roundCount)) &&
                    Round(p.X, roundCount) <= Math.Max(Round(p1.X, roundCount), Round(p2.X, roundCount)))
                {
                    isBeside = true;
                    continue;
                }

                //通过大于等于线段两个端点的最小Y值，小于线段两个端点的最大Y值，判断出射线与线段是否相交

                if (Round(p.Y, roundCount) < Math.Min(Round(p1.Y, roundCount), Round(p2.Y, roundCount)) ||
                    Round(p.Y, roundCount) >= Math.Max(Round(p1.Y, roundCount), Round(p2.Y, roundCount))) // 交点在p1p2延长线上 
                    continue;


                // 求交点的 X 坐标 -------------------------------------------------------------- 
                double x = (p.Y - p1.Y) * (p2.X - p1.X) / (p2.Y - p1.Y) + p1.X;

                if (Round(x, roundCount) > Round(p.X, roundCount))
                {
                    nCross++; // 只统计单边交点 
                }
                else if (Round(x, roundCount) == Round(p.X, roundCount))
                    isBeside = true;
            }
        }

        /// <summary>
        /// 给点数组顺时针排序
        /// </summary>
        /// <param name="points"></param>
        public static List<PointF> SortPointsClockwise(List<PointF> points)
        {
            if (points.Count < 3)
            {
                return points;
            }
            float xCenter = (points[0].X + points[1].X + points[2].X) / 3;
            float yCenter = (points[0].Y + points[1].Y + points[2].Y) / 3;
            List<PointF> pointList = new List<PointF>();
            List<double> atans = new List<double>();
            for (int i = 0; i < points.Count; i++)
            {
                PointF curPoint = points[i];
                double curAtan = Math.Atan2(curPoint.Y - yCenter, curPoint.X - xCenter);
                bool inserted = false;
                for (int j = 0; j < atans.Count; j++)
                {
                    if (atans[j] < curAtan)
                    {
                        atans.Insert(j, curAtan);
                        pointList.Insert(j, curPoint);
                        inserted = true;
                        break;
                    }
                }
                if (!inserted)
                {
                    atans.Add(curAtan);
                    pointList.Add(curPoint);
                }
            }
            return pointList;
        }

        /// <summary>
        /// 给点数组顺时针排序，针对Form，y轴是反的
        /// </summary>
        /// <param name="points"></param>
        public static List<PointF> SortPointsClockwiseForForm(List<PointF> points)
        {
            if (points.Count < 3)
            {
                return points;
            }
            //if (points.Count > 3)
            //{
            //    PointF? pointInter = GetIntersectionOfTwoLineNotExtend(points[0], points[1], points[points.Count - 2], points[points.Count - 1]);
            //    if (pointInter != null)
            //    {
            //        points.RemoveAt(0);
            //        points.RemoveAt(points.Count - 1);
            //        points.Add((PointF)pointInter);
            //    }
            //}
            float xCenter = (points[0].X + points[1].X + points[2].X) / 3;
            float yCenter = (points[0].Y + points[1].Y + points[2].Y) / 3;
            List<PointF> pointList = new List<PointF>();
            List<double> atans = new List<double>();
            for (int i = 0; i < points.Count; i++)
            {
                PointF curPoint = points[i];
                double curAtan = Math.Atan2(curPoint.Y - yCenter, curPoint.X - xCenter);
                bool inserted = false;
                for (int j = 0; j < atans.Count; j++)
                {
                    if (atans[j] > curAtan)
                    {
                        atans.Insert(j, curAtan);
                        pointList.Insert(j, curPoint);
                        inserted = true;
                        break;
                    }
                }
                if (!inserted)
                {
                    atans.Add(curAtan);
                    pointList.Add(curPoint);
                }
            }
            return pointList;
        }


        /// <summary>
        /// 四舍五入
        /// Math.Round(dblnum, numberprecision, MidpointRounding.AwayFromZero) is banker
        /// </summary>
        /// <param name="dblnum"></param>
        /// <param name="numberprecision"></param>
        /// <returns></returns>
        public static double Round(double dblnum, int numberprecision)
        {
            int tmpNum = dblnum > 0 ? 5 : -5;
            double dblreturn = Math.Truncate(dblnum * Math.Pow(10, numberprecision + 1)) + tmpNum;
            dblreturn = Math.Truncate(dblreturn / 10) / Math.Pow(10, numberprecision);
            return dblreturn;
        }


        #region 传入一组  List<DbPoint>  类型数据，获取最小凸包点，调用函数GetConvexNodes即可
        /////////////////传入一系列点，并返回这些点的最小凸包
        public static List<DbPoint> GetConvexNodes(List<DbPoint> node_list)
        {

            double allowance = 0.0001;   ////给定凸包容差，主要针对情况：1、只有一个点的情况。2、两个点或者所有点共线的情况。
            Stack<DbPoint> nodes_stack = GetNodesByAngle(node_list);
            int p_nodesCount = nodes_stack.Count;
            List<DbPoint> p_nodesList = Stack2List(nodes_stack);

            switch (p_nodesCount)
            {
                case 1:
                    //////////只有一个点的情况
                    DbPoint p = p_nodesList[0];
                    p_nodesList.Clear();
                    p_nodesList.Add(new DbPoint(p.x - allowance, p.y - allowance));
                    p_nodesList.Add(new DbPoint(p.x - allowance, p.y + allowance));
                    p_nodesList.Add(new DbPoint(p.x + allowance, p.y - allowance));
                    p_nodesList.Add(new DbPoint(p.x + allowance, p.y + allowance));
                    break;
                case 2:
                    //////////两个点或者所有点共线的情况
                    DbPoint p0 = p_nodesList[0];
                    DbPoint p1 = p_nodesList[1];
                    float angle = CalcAngleTwoPoint(p0, p1);
                    double xDis = Math.Sin(angle * Math.PI / 180) * allowance;
                    double yDis = Math.Cos(angle * Math.PI / 180) * allowance;
                    p_nodesList.Add(new DbPoint((p0.x + p1.x) / 2 - xDis, (p0.y + p1.y) / 2 + yDis));
                    p_nodesList.Add(new DbPoint((p0.x + p1.x) / 2 + xDis, (p0.y + p1.y) / 2 - yDis));
                    break;
                default:
                    break;
            }

            p_nodesList = Stack2List(GetNodesByAngle(p_nodesList));
            return p_nodesList;
        }
        /////////////////通过角度获取一系列点中要用来组成凸包的点
        private static Stack<DbPoint> GetNodesByAngle(List<DbPoint> nodes)
        {
            Stack<DbPoint> sortedNodesStack = new Stack<DbPoint>();

            if (nodes == null || nodes.Count <= 0)
            {
                //////////当传入0个点的情况
                return sortedNodesStack;
            }

            LinkedList<DbPoint> list_node = new LinkedList<DbPoint>();
            DbPoint p0 = GetMinYPoint(nodes);               //Y方向最小的点
            if (nodes.Count <= 0)
            {
                //////当只有一个点时的情况
                sortedNodesStack.Push(p0);
                return sortedNodesStack;
            }

            addListNode(nodes, list_node, p0);
            DbPoint[] sor_nodes = LinkedList2Array(list_node);

            sortedNodesStack.Push(p0);
            int idex = 1;
            foreach (DbPoint item in sor_nodes)
            {
                if (idex >= 3)
                {
                    break;
                }
                sortedNodesStack.Push(item);
                idex++;
            }

            for (int i = 2; i < sor_nodes.Length; i++)
            {

                DbPoint p2 = sor_nodes[i];
                DbPoint p1 = sortedNodesStack.Pop();
                DbPoint p0_sec = sortedNodesStack.Pop();
                sortedNodesStack.Push(p0_sec);
                sortedNodesStack.Push(p1);

                if (IsClockDirection(p0_sec, p1, p2) == 1)
                {
                    sortedNodesStack.Push(p2);
                    continue;
                }
                while (IsClockDirection(p0_sec, p1, p2) != 1)
                {
                    sortedNodesStack.Pop();
                    p1 = sortedNodesStack.Pop();
                    p0_sec = sortedNodesStack.Pop();
                    sortedNodesStack.Push(p0_sec);
                    sortedNodesStack.Push(p1);
                }
                sortedNodesStack.Push(p2);

            }
            return sortedNodesStack;
        }

        private static void addListNode(List<DbPoint> nodes, LinkedList<DbPoint> list_node, DbPoint p0)
        {
            LinkedListNode<DbPoint> node = new LinkedListNode<DbPoint>(nodes[0]);
            list_node.AddFirst(node);
            for (int i = 1; i < nodes.Count; i++)
            {
                int direct = IsClockDirectionOrCollinear(p0, node.Value, nodes[i]);
                if (direct == 1)
                {
                    list_node.AddLast(nodes[i]);
                    node = list_node.Last;

                }
                else if (direct == -10)
                {
                    list_node.Last.Value = nodes[i];
                }
                //else if (direct == 10)
                //{
                //    //
                //}
                else if (direct == -1)
                {
                    deallDirNode(nodes, list_node, p0, node, i);
                }
            }
        }

        private static void deallDirNode(List<DbPoint> nodes, LinkedList<DbPoint> list_node
            , DbPoint p0, LinkedListNode<DbPoint> node, int i)
        {
            LinkedListNode<DbPoint> temp = node.Previous;
            while (temp != null && IsClockDirectionOrCollinear(p0, temp.Value, nodes[i]) == -1)
            {
                temp = temp.Previous;
            }
            if (temp == null)
            {
                list_node.AddFirst(nodes[i]);
            }
            else
            {
                if (IsClockDirectionOrCollinear(p0, temp.Value, nodes[i]) == -10)
                {
                    temp.Value = nodes[i];
                }
                else if (IsClockDirectionOrCollinear(p0, temp.Value, nodes[i]) == 10)
                {
                    //
                }
                else
                {
                    list_node.AddAfter(temp, nodes[i]);
                }
            }
        }

        //////////堆栈转为list
        private static List<DbPoint> Stack2List(Stack<DbPoint> stack)
        {
            List<DbPoint> resultList = new List<DbPoint>();
            foreach (DbPoint item in stack)
            {
                resultList.Add(item);
            }
            return resultList;
        }
        ///双向链表转为数组
        private static DbPoint[] LinkedList2Array(LinkedList<DbPoint> linkList)
        {
            DbPoint[] sor_linkList = new DbPoint[linkList.Count];
            int idex = 0;
            foreach (DbPoint item in linkList)
            {
                sor_linkList[idex] = item;
                idex++;
            }
            return sor_linkList;
        }
        //判断p2点在p0、p1直线的哪一侧
        private static int IsClockDirection(DbPoint p0, DbPoint p1, DbPoint p2)
        {
            DbPoint p0_p1 = new DbPoint(p1.x - p0.x, p1.y - p0.y);
            DbPoint p0_p2 = new DbPoint(p2.x - p0.x, p2.y - p0.y);
            return (p0_p1.x * p0_p2.y - p0_p2.x * p0_p1.y) > 0 ? 1 : -1;
        }
        ///获取系列点中最下方的点（最下方有多个点时，则选择最靠左那个）
        private static DbPoint GetMinYPoint(List<DbPoint> nodes)
        {
            DbPoint nodeXYMin = nodes[0];
            foreach (DbPoint item in nodes)
            {
                if (nodeXYMin.y > item.y)
                {
                    nodeXYMin = item;
                }
            }
            foreach (DbPoint item in nodes)
            {
                if (nodeXYMin.x > item.x && item.y <= nodeXYMin.y)
                {
                    nodeXYMin = item;
                }
            }
            nodes.Remove(nodeXYMin);
            return nodeXYMin;

        }
        //判断p2点在p0、p1直线的哪一侧，或者是p0、p1、p2三点共线
        private static int IsClockDirectionOrCollinear(DbPoint p0, DbPoint p1, DbPoint p2)
        {
            DbPoint p0_p1 = new DbPoint(p1.x - p0.x, p1.y - p0.y);
            DbPoint p0_p2 = new DbPoint(p2.x - p0.x, p2.y - p0.y);
            if ((p0_p1.x * p0_p2.y - p0_p2.x * p0_p1.y) != 0)
            {
                return (p0_p1.x * p0_p2.y - p0_p2.x * p0_p1.y) > 0 ? 1 : -1;
            }
            else
            {
                return DistanceOfNodes(p0, p1) > DistanceOfNodes(p0, p2) ? 10 : -10;
            }
        }
        /////////获取两个点之间的距离
        private static double DistanceOfNodes(DbPoint p0, DbPoint p1)
        {
            if (p0 == null || p1 == null)
            {
                return 0.0;
            }
            else
            {
                return Math.Sqrt((p1.x - p0.x) * (p1.x - p0.x) + (p1.y - p0.y) * (p1.y - p0.y));
            }


        }
        ////////////获取两个点与x轴的夹角（返回结果：例如90度，则记为90，所以在运动角度函数时需要用Math.PI处理一下）
        private static float CalcAngleTwoPoint(DbPoint p1, DbPoint p2)
        {
            float angle = (float)(Math.Atan(Math.Abs(p2.y - p1.y + 0.0) / Math.Abs(p2.x - p1.x + 0.0)) * 180 / Math.PI);
            if ((p2.y - p1.y + 0.0) / (p2.x - p1.x + 0.0) < 0)
            {
                angle = 180 - angle;
            }
            return angle;
        }
        #endregion


        /// <summary>
        /// WeilerAtherton裁剪法计算类
        /// </summary>
        private class WeilerAthertonManager
        {
            readonly PointD[] oldRegion;
            readonly PointD[] clipRegion;

            readonly List<PointForWeilerA> oldRegionTmp = new List<PointForWeilerA>();   //被裁剪多边形点序列1
            readonly List<PointForWeilerA> clipRegionTmp = new List<PointForWeilerA>();  //裁剪多边形点序列2
            readonly List<PointForWeilerA> oldRegionRef = new List<PointForWeilerA>();   //被裁剪多边形点序列3，插入交点
            readonly List<PointForWeilerA> clipRegionRef = new List<PointForWeilerA>();  //裁剪多边形点序列4，插入交点
            readonly List<PointForWeilerA> intersectPoints = new List<PointForWeilerA>();    //交点数组
            /// <summary>
            /// 点比较时小数点精确位数
            /// </summary>
            readonly int roundCount = 6;
            int indexStart { get; set; } = -1;
            public WeilerAthertonManager(PointD[] oldRegion, PointD[] clipRegion)
            {
                this.oldRegion = oldRegion;
                this.clipRegion = clipRegion;
            }

            public List<PointF[]> GetResult()
            {
                initOldRegion();
                initClipRegion();
                getIntersectPoints();
                //getIntersectPoints2();
                setOldRegionWithIntersect();
                setClipRegionWithIntersect();
                List<List<PointForWeilerA>> regionList = getResult();
                List<PointF[]> refList = new List<PointF[]>();
                for (int i = 0; i < regionList.Count; i++)
                {
                    List<PointForWeilerA> region = regionList[i];
                    PointF[] pArr = new PointF[region.Count];
                    for (int j = 0; j < region.Count; j++)
                    {
                        pArr[j] = new PointF((float)region[j].point.X, (float)region[j].point.Y);
                    }
                    refList.Add(pArr);
                }
                return refList;
            }

            /// <summary>
            /// 插入被裁剪多边形序列
            /// </summary>
            private void initOldRegion()
            {
                oldRegionTmp.Clear();
                foreach (PointD p in oldRegion) //
                {
                    PointForWeilerA pointWithFlat = new PointForWeilerA(p);
                    oldRegionTmp.Add(pointWithFlat);
                }
            }

            /// <summary>
            /// 插入裁剪多边形序列
            /// </summary>
            private void initClipRegion()
            {
                clipRegionTmp.Clear();
                foreach (PointD p in clipRegion)    //
                {
                    PointForWeilerA pointWithFlat = new PointForWeilerA(p);
                    clipRegionTmp.Add(pointWithFlat);
                }
            }

            /**
            private void getIntersectPoints2()
            {
                MapWinGIS.Shapefile shpFile = new MapWinGIS.Shapefile();
                MapWinGIS.Shape oldShape = new MapWinGIS.Shape();
                oldShape.ShapeType = MapWinGIS.ShpfileType.SHP_POLYGON;
                MapWinGIS.Shape clipShape = new MapWinGIS.Shape();
                clipShape.ShapeType = MapWinGIS.ShpfileType.SHP_POLYGON;
                MapWinGIS.Shape newShape = null;
                int pIndex = 0;
                for (int i = 0; i < oldRegion.Length; ++i)
                {
                    pIndex = i;
                    MapWinGIS.Point p = new MapWinGIS.Point();
                    p.x = oldRegion[i].X;
                    p.y = oldRegion[i].Y;
                    oldShape.InsertPoint(p, ref pIndex);
                }
                for (int i = 0; i < clipRegion.Length; ++i)
                {
                    pIndex = i;
                    MapWinGIS.Point p = new MapWinGIS.Point();
                    p.x = clipRegion[i].X;
                    p.y = clipRegion[i].Y;
                    clipShape.InsertPoint(p, ref pIndex);
                }
                
                intersectPoints.Clear();
                newShape = oldShape.Clip(clipShape, MapWinGIS.tkClipOperation.clClip);
                Console.WriteLine("last error code = " + oldShape.LastErrorCode);
                if (newShape == null)
                {
                    return;
                }
                for (int i = 0; i < newShape.numPoints; ++i)
                {
                    double x = 0, y = 0;
                    newShape.get_XY(i, ref x, ref y);
                    PointForWeilerA p = new PointForWeilerA(new PointD(x, y));
                    intersectPoints.Add(p);
                }
            }
            */

            class PointForWeilerAGroup
            {
                public PointForWeilerA P1 { get; set; }
                public PointForWeilerA P2 { get; set; }
                public PointForWeilerA P3 { get; set; }

                public void Init(List<PointForWeilerA> oldRegionTmp, int i)
                {
                    P1 = oldRegionTmp[i];
                    if (i == oldRegionTmp.Count - 1)
                    {
                        P2 = oldRegionTmp[0];
                    }
                    else
                    {
                        P2 = oldRegionTmp[i + 1];
                    }
                    if (i == oldRegionTmp.Count - 1)
                    {
                        P3 = oldRegionTmp[1];
                    }
                    else if (i == oldRegionTmp.Count - 2)
                    {
                        P3 = oldRegionTmp[0];
                    }
                    else
                    {
                        P3 = oldRegionTmp[i + 2];
                    }
                }
            }

            /// <summary>
            /// 求出所有交点
            /// </summary>
            private void getIntersectPoints()
            {
                List<PointForWeilerA> pointsInter = new List<PointForWeilerA>();
                List<PointForWeilerA> pointsPass = new List<PointForWeilerA>();
                for (int i = 0; i < oldRegionTmp.Count; i++)    //
                {
                    PointForWeilerAGroup grp = new PointForWeilerAGroup();
                    grp.Init(oldRegionTmp, i);
                    for (int j = 0; j < clipRegionTmp.Count; j++)
                    {
                        PointForWeilerA p1Border, p2Border;
                        p1Border = clipRegionTmp[j];
                        if (j < clipRegionTmp.Count - 1)
                        {
                            p2Border = clipRegionTmp[j + 1];
                        }
                        else
                        {
                            p2Border = clipRegionTmp[0];
                        }
                        List<PointD> interPoints = getIntersectionOfTwoLineNotExtendForWeilerAtherton(grp.P1.point, grp.P2.point, p1Border.point, p2Border.point);
                        if (interPoints.Count > 0)
                        {
                            dealInterPoints(pointsInter, pointsPass, grp, p1Border, p2Border, interPoints);
                        }
                    }
                }
                intersectPoints.Clear();
                foreach (PointForWeilerA point in pointsInter)
                {
                    addValidPInter(intersectPoints, point);
                }
                //可能以为精度问题，存在丢失
                if (intersectPoints.Count % 2 == 1 && pointsPass.Count == 1)
                {
                    addValidPInter(intersectPoints, pointsPass[0]);
                }
                if (intersectPoints.Count % 2 == 1)
                {
                    removeApproximatelyPoint();
                }
            }

            private void dealInterPoints(List<PointForWeilerA> pointsInter, List<PointForWeilerA> pointsPass, PointForWeilerAGroup grp, PointForWeilerA p1Border, PointForWeilerA p2Border, List<PointD> interPoints)
            {
                /* 一、需要完善交点算法，分多种情况的处理，AB与切割边有交点，还要考虑下一个计算B点到C点的线，在计算AB过程中：
                 * 如果只有一个交点，且不是A,B，肯定是交点，如果A点，下次作为B点时考虑，如果是B点：
                 * 1.AB,BC是否对切割边进行了穿越，即A,C点在切割边的两边；
                 *  
                 * 二、如果返回多个点，两条直线重合了，因为成对出现，都作为交点；
                 */

                if (interPoints.Count == 1)
                {
                    PointForWeilerA pInter = new PointForWeilerA(interPoints[0]);
                    if (pInter.Equals(grp.P2))
                    {
                        if (getIntersectionOfTwoLineTheSecondNotExtend(p1Border.point, p2Border.point, grp.P1.point, grp.P3.point) != null)
                        {
                            addValidPInter(pointsInter, pInter);
                        }
                    }
                    else if (!pInter.Equals(grp.P1))
                    {
                        addValidPInter(pointsInter, pInter);
                    }
                    else
                    {
                        addValidPInter(pointsPass, pInter);
                    }
                }
                else
                {
                    foreach (PointD pf in interPoints)
                    {
                        PointForWeilerA pInter = new PointForWeilerA(pf);
                        addValidPInter(pointsInter, pInter);
                    }
                }
            }

            private void removeApproximatelyPoint()
            {
                for (int i = 0; i < intersectPoints.Count - 1; i++)
                {
                    PointForWeilerA curP = intersectPoints[i];
                    bool bFind = false;
                    for (int j = i + 1; j < intersectPoints.Count; i++)
                    {
                        PointForWeilerA nextP = intersectPoints[j];
                        if (curP.EqualsApproximatelyPoint(nextP))
                        {
                            intersectPoints.RemoveAt(j);
                            bFind = true;
                            break;
                        }
                    }
                    if (bFind)
                    {
                        break;
                    }
                }
            }

            private void addValidPInter(List<PointForWeilerA> points, PointForWeilerA pInter)
            {
                if (!points.Contains(pInter))
                {
                    points.Add(pInter);
                }
            }

            /// <summary>
            /// 交点插入数组1，得到数组3
            /// </summary>
            private void setOldRegionWithIntersect()
            {
                int flag = 0;
                List<int> indexSeted = new List<int>();
                for (int i = 0; i < oldRegionTmp.Count; i++)  //
                {
                    PointForWeilerA pOldRegion = oldRegionTmp[i];
                    PointForWeilerA pOldRegionNext;
                    if (i == oldRegionTmp.Count - 1)
                    {
                        pOldRegionNext = oldRegionTmp[0];
                    }
                    else
                    {
                        pOldRegionNext = oldRegionTmp[i + 1];
                    }
                    if (!oldRegionRef.Contains(pOldRegion))
                    {
                        oldRegionRef.Add(pOldRegion);
                    }
                    if (indexSeted.Count == intersectPoints.Count)
                    {
                        continue;
                    }
                    List<PointForWeilerA> cuInterPoints = getCuInterPoints(indexSeted, pOldRegion, pOldRegionNext);
                    flag = dealCuInterPoints(flag, i, pOldRegion, cuInterPoints);
                }
            }

            private List<PointForWeilerA> getCuInterPoints(List<int> indexSeted
                , PointForWeilerA pRegion, PointForWeilerA pRegionNext)
            {
                int k = 0;
                List<PointForWeilerA> cuInterPoints = new List<PointForWeilerA>();
                while (k < intersectPoints.Count)
                {
                    if (indexSeted.Contains(k))
                    {
                        k++;
                        continue;
                    }
                    PointForWeilerA pIntersect = intersectPoints[k];
                    if (pointOnLine(pIntersect.point, pRegion.point, pRegionNext.point, roundCount))
                    {
                        cuInterPoints.Add(pIntersect);
                        indexSeted.Add(k);
                    }
                    k++;
                }

                return cuInterPoints;
            }

            private int dealCuInterPoints(int flag, int i, PointForWeilerA pOldRegion, List<PointForWeilerA> cuInterPoints)
            {
                if (cuInterPoints.Count > 0)
                {
                    if (cuInterPoints.Count == 1)
                    {
                        insertIntoOldRegionRef(i, ref flag, cuInterPoints[0]);
                    }
                    else
                    {
                        Dictionary<double, PointForWeilerA> interPointsDic = new Dictionary<double, PointForWeilerA>();
                        List<double> distanceList = new List<double>();
                        foreach (PointForWeilerA pIntersect in cuInterPoints)
                        {
                            double dvalue = Math.Sqrt(Math.Pow(pIntersect.point.X - pOldRegion.point.X, 2) + Math.Pow(pIntersect.point.Y - pOldRegion.point.Y, 2));
                            distanceList.Add(dvalue);
                            interPointsDic[dvalue] = pIntersect;
                        }
                        distanceList.Sort();
                        foreach (double dvalue in distanceList)
                        {
                            insertIntoOldRegionRef(i, ref flag, interPointsDic[dvalue]);
                        }
                    }
                }

                return flag;
            }

            /// <summary>
            /// 交点插入到被裁剪多边形与交点合并，顺序排序的列表中
            /// </summary>
            private void insertIntoOldRegionRef(int indexOld, ref int flag, PointForWeilerA pIntersect)
            {
                PointForWeilerA pOldRegion = oldRegionTmp[indexOld];
                getIntersectFlag(ref indexOld, ref flag, pIntersect, pOldRegion);

                int index = oldRegionRef.IndexOf(pIntersect);
                if (index >= 0)
                {
                    pIntersect.indexInOld = index;
                    oldRegionRef.RemoveAt(index);
                    oldRegionRef.Insert(index, pIntersect);
                }
                else
                {
                    pIntersect.indexInOld = oldRegionRef.Count;
                    oldRegionRef.Add(pIntersect);
                }
            }

            private void getIntersectFlag(ref int indexOld, ref int flag, PointForWeilerA pIntersect, PointForWeilerA pOldRegion)
            {
                if (flag == 0)
                {
                    int inPolygon = pointInPolygon(pOldRegion.point, clipRegion, roundCount);
                    if (inPolygon == 1)
                    {
                        flag = 2;
                        pIntersect.flag = 2;
                    }
                    else if (inPolygon == 0)
                    {
                        getValidOldRegionFlag(ref indexOld, ref flag, pIntersect, pOldRegion, ref inPolygon);
                    }
                    else
                    {
                        flag = 1;
                        pIntersect.flag = 1;
                    }
                }
                else if (flag == 2)
                {
                    flag = 1;
                    pIntersect.flag = 1;
                }
                else
                {
                    flag = 2;
                    pIntersect.flag = 2;
                }
            }

            private void getValidOldRegionFlag(ref int indexOld, ref int flag, PointForWeilerA pIntersect, PointForWeilerA pOldRegion, ref int inPolygon)
            {
                if (pOldRegion.Equals(pIntersect))
                {
                    if (indexOld > 0)
                    {
                        indexOld -= 1;
                    }
                    else
                    {
                        indexOld = oldRegionTmp.Count - 1;
                    }
                    insertIntoOldRegionRef(indexOld, ref flag, pIntersect);
                }
                else
                {
                    PointD midPoint = new PointD((pOldRegion.point.X + pIntersect.point.X) / 2, (pOldRegion.point.Y + pIntersect.point.Y) / 2);
                    inPolygon = pointInPolygon(midPoint, clipRegion, roundCount);
                    if (inPolygon == 1 || inPolygon == 0)
                    {
                        flag = 2;
                        pIntersect.flag = 2;
                    }
                    else
                    {
                        flag = 1;
                        pIntersect.flag = 1;
                    }
                }
            }

            /// <summary>
            /// 交点插入数组2，得到数组4
            /// </summary>
            private void setClipRegionWithIntersect()
            {
                List<int> indexSeted = new List<int>();
                for (int i = 0; i < clipRegionTmp.Count; i++)  //
                {
                    PointForWeilerA pClipRegion = clipRegionTmp[i];
                    PointForWeilerA pClipRegionNext;
                    if (i == clipRegionTmp.Count - 1)
                    {
                        pClipRegionNext = clipRegionTmp[0];
                    }
                    else
                    {
                        pClipRegionNext = clipRegionTmp[i + 1];
                    }
                    if (!clipRegionRef.Contains(pClipRegion))
                    {
                        clipRegionRef.Add(pClipRegion);
                    }
                    if (indexSeted.Count == intersectPoints.Count)
                    {
                        continue;
                    }
                    List<PointForWeilerA> cuInterPoints = getCuInterPoints(indexSeted, pClipRegion, pClipRegionNext);
                    dealCuInterPoints(pClipRegion, cuInterPoints);
                }
            }

            private void dealCuInterPoints(PointForWeilerA pClipRegion, List<PointForWeilerA> cuInterPoints)
            {
                if (cuInterPoints.Count > 0)
                {
                    /*  如果只有1个交点，插入。
                     *  如果有多个交点，按与裁剪线的起始点的距离，由小到大排序，顺序插入。
                     */
                    if (cuInterPoints.Count == 1)
                    {
                        insertIntoClipRegionRef(cuInterPoints[0]);
                    }
                    else
                    {
                        Dictionary<double, PointForWeilerA> interPointsDic = new Dictionary<double, PointForWeilerA>();
                        List<double> distanceList = new List<double>();
                        foreach (PointForWeilerA pIntersect in cuInterPoints)
                        {
                            double dvalue = Math.Sqrt(Math.Pow(pIntersect.point.X - pClipRegion.point.X, 2) + Math.Pow(pIntersect.point.Y - pClipRegion.point.Y, 2));
                            distanceList.Add(dvalue);
                            interPointsDic[dvalue] = pIntersect;
                        }
                        distanceList.Sort();
                        foreach (double dvalue in distanceList)
                        {
                            insertIntoClipRegionRef(interPointsDic[dvalue]);
                        }
                    }
                }
            }

            /// <summary>
            /// 交点插入到裁剪多边形与交点合并，顺序排序的列表中
            /// </summary>
            /// <param name="pIntersect"></param>
            private void insertIntoClipRegionRef(PointForWeilerA pIntersect)
            {
                int index = clipRegionRef.IndexOf(pIntersect);
                if (index >= 0)
                {
                    pIntersect.indexInClip = index;
                    clipRegionRef.RemoveAt(index);
                    clipRegionRef.Insert(index, pIntersect.Clone());
                }
                else
                {
                    pIntersect.indexInClip = clipRegionRef.Count;
                    clipRegionRef.Add(pIntersect.Clone());
                }
            }

            /// <summary>
            /// 获取裁剪出的多个区域(如果是凹多边形，可能会有多块)
            /// </summary>
            /// <returns></returns>
            private List<List<PointForWeilerA>> getResult()
            {
                List<List<PointForWeilerA>> refList = new List<List<PointForWeilerA>>();
                while (true)
                {
                    List<PointForWeilerA> regionPList = getRegion();
                    if (regionPList.Count == 0)
                    {
                        break;
                    }
                    refList.Add(regionPList);
                }
                return refList;
            }

            /// <summary>
            /// 获取裁剪出的一块区域，如果是凹多边形，可能会有多块；
            /// </summary>
            /// <returns>裁剪出的一块区域顶点列表</returns>
            private List<PointForWeilerA> getRegion()
            {
                List<PointForWeilerA> pointList = new List<PointForWeilerA>();

                //从数组3中寻找“入”点,如果“入”点没找到，程序结束,如果找到“入”点，则将“入”点放入S中暂存,
                //将“入”点录入到输出数组Q中。并从数组3中将该“入”点的“入”点标 记删去。
                indexStart = findInPoint(oldRegionRef, 0);
                if (indexStart == -1)
                {
                    return pointList;
                }

                int indexBegin = indexStart;
                List<PointForWeilerA> tmpList;
                int iloop = 0;
                while (true)
                {
                    iloop++;
                    if (iloop > 5)
                    {
                        foreach (PointForWeilerA pw in oldRegionTmp)
                        {
                            System.Console.WriteLine(Math.Round(pw.point.X, 6) + ", " + Math.Round(pw.point.Y, 6));
                        }
                        foreach (PointForWeilerA pw in clipRegionTmp)
                        {
                            System.Console.WriteLine(Math.Round(pw.point.X, 6) + ", " + Math.Round(pw.point.Y, 6));
                        }
                        pointList.Clear();
                        return pointList;
                    }
                    oldRegionRef[indexBegin].flag = -1;
                    pointList.Add(oldRegionRef[indexBegin]);
                    tmpList = getPoints(ref indexBegin);
                    bool isValid = addValidPointList(pointList, indexBegin, tmpList);
                    if (!isValid)
                    {
                        break;
                    }
                }

                return pointList;
            }

            private bool addValidPointList(List<PointForWeilerA> pointList, int indexBegin, List<PointForWeilerA> tmpList)
            {
                if (tmpList.Count > 0)
                {
                    foreach (PointForWeilerA point in tmpList)
                    {
                        if (!pointList.Contains(point))
                        {
                            pointList.Add(point);
                        }
                    }
                    if (indexBegin == indexStart)
                    {
                        return false;
                    }
                }
                else
                {
                    return false;
                }
                return true;
            }

            /// <summary>
            /// 依次从3、4数组取点
            /// </summary>
            /// <param name="indexBegin">起始位置</param>
            /// <returns>下次取点在3数组中的起始位置</returns>
            private List<PointForWeilerA> getPoints(ref int indexBegin)
            {
                List<PointForWeilerA> refList = new List<PointForWeilerA>();

                //沿数组3顺序取顶点：
                //如果顶点不是“出点”，则将顶点录入到输出数组Q中,否则跳转到数组4
                int indexFrom = indexBegin + 1;
                int indexOutOld = findOutPoint(oldRegionRef, indexFrom);
                if (indexOutOld == -1)
                {
                    indexOutOld = findOutPoint(oldRegionRef, 0);
                }
                addSortOldRegionRef(refList, indexFrom, indexOutOld);

                //沿数组4顺序取顶点：
                //如果顶点不是“入点”，则将顶点录入到输出数组Q中
                indexFrom = oldRegionRef[indexOutOld].indexInClip;
                int indexInClip = findInPoint(clipRegionRef, indexFrom);
                if (indexInClip == -1)
                {
                    indexInClip = findInPoint(clipRegionRef, 0);
                }
                addSortClipRegionRef(refList, indexFrom, indexInClip);

                indexBegin = clipRegionRef[indexInClip].indexInOld;

                return refList;
            }

            private void addSortOldRegionRef(List<PointForWeilerA> refList, int indexFrom, int indexOutOld)
            {
                if (indexOutOld >= indexFrom)
                {
                    for (int i = indexFrom; i < indexOutOld; i++)
                    {
                        if (oldRegionRef[i].flag == 1)
                        {
                            oldRegionRef[i].flag = -1;
                        }
                        refList.Add(oldRegionRef[i]);
                    }
                }
                else
                {
                    for (int i = indexFrom; i < oldRegionRef.Count; i++)
                    {
                        if (oldRegionRef[i].flag == 1)
                        {
                            oldRegionRef[i].flag = -1;
                        }
                        refList.Add(oldRegionRef[i]);
                    }
                    for (int i = 0; i < indexOutOld; i++)
                    {
                        refList.Add(oldRegionRef[i]);
                    }
                }
            }

            private void addSortClipRegionRef(List<PointForWeilerA> refList, int indexFrom, int indexInClip)
            {
                if (indexInClip > indexFrom)
                {
                    for (int i = indexFrom; i < indexInClip; i++)
                    {
                        refList.Add(clipRegionRef[i]);
                    }
                }
                else
                {
                    for (int i = indexFrom; i < clipRegionRef.Count; i++)
                    {
                        refList.Add(clipRegionRef[i]);
                    }
                    for (int i = 0; i < indexInClip; i++)
                    {
                        refList.Add(clipRegionRef[i]);
                    }
                }
            }

            /// <summary>
            /// 寻找入点
            /// </summary>
            /// <param name="pointList"></param>
            /// <param name="indexFrom">起始位置</param>
            /// <returns></returns>
            private int findInPoint(List<PointForWeilerA> pointList, int indexFrom)
            {
                for (int i = indexFrom; i < pointList.Count; i++)
                {
                    PointForWeilerA point = pointList[i];
                    if (point.flag == 1)
                    {
                        return i;
                    }
                }
                return -1;
            }

            /// <summary>
            /// 寻找出点
            /// </summary>
            /// <param name="pointList"></param>
            /// <param name="indexFrom">起始位置</param>
            /// <returns></returns>
            private int findOutPoint(List<PointForWeilerA> pointList, int indexFrom)
            {
                for (int i = indexFrom; i < pointList.Count; i++)
                {
                    PointForWeilerA point = pointList[i];
                    if (point.flag == 2)
                    {
                        return i;
                    }
                }
                return -1;
            }

            /// <summary>
            /// 功能：判断点是否在多边形内 
            /// 方法：求解通过该点的水平线与多边形各边的交点 
            /// 结论：单边交点为奇数，成立! 
            /// 参数： 返回1表示肯定在多边形内；-1肯定不在多边形内；0表示在多边形的边上；
            /// Point p 指定的某个点 
            /// Point[] ptPolygon 多边形的各个顶点坐标（首末点可以不一致） 
            /// int nCount 多边形定点的个数 
            /// </summary>
            /// <param name="p"></param>
            /// <param name="ptPolygon"></param>
            /// <returns></returns>
            private int pointInPolygon(PointD p, PointD[] ptPolygon, int roundCount)
            {
                int nCount = ptPolygon.Length;
                bool isBeside = false;// 记录是否在多边形的边上

                #region 矩形外区域
                if (nCount > 0)
                {
                    double maxx = ptPolygon[0].X;
                    double minx = ptPolygon[0].X;
                    double maxy = ptPolygon[0].Y;
                    double miny = ptPolygon[0].Y;

                    getSideRangePointD(ptPolygon, nCount, ref maxx, ref maxy, ref minx, ref miny);

                    if ((MathFuncs.Round(p.X, roundCount) > MathFuncs.Round(maxx, roundCount)) || (MathFuncs.Round(p.X, roundCount) < MathFuncs.Round(minx, roundCount)) ||
                        (MathFuncs.Round(p.Y, roundCount) > MathFuncs.Round(maxy, roundCount)) || (MathFuncs.Round(p.Y, roundCount) < MathFuncs.Round(miny, roundCount)))
                        return -1;
                }


                #endregion

                #region 射线法
                int nCross = 0;
                judgeBesidePointD(p, ptPolygon, roundCount, nCount, ref isBeside, ref nCross);

                if (isBeside)
                    return 0;//多边形边上
                else if (nCross % 2 == 1)// 单边交点为偶数，点在多边形之外 --- 
                    return 1;//多边形内

                return -1;//多边形外
                #endregion
            }

            private static void judgeBesidePointD(PointD p, PointD[] ptPolygon, int roundCount
                , int nCount, ref bool isBeside, ref int nCross)
            {
                for (int i = 0; i < nCount; i++)
                {
                    PointD p1 = ptPolygon[i];
                    PointD p2 = ptPolygon[(i + 1) % nCount];

                    // 求解 y=p.y 与 p1p2 的交点

                    // p1p2 与 y=p0.y平行
                    if (p1.Y == p2.Y && MathFuncs.Round(p.Y, roundCount) == MathFuncs.Round(p1.Y, roundCount) &&
                        MathFuncs.Round(p.X, roundCount) >= Math.Min(MathFuncs.Round(p1.X, roundCount), MathFuncs.Round(p2.X, roundCount)) &&
                        MathFuncs.Round(p.X, roundCount) <= Math.Max(MathFuncs.Round(p1.X, roundCount), MathFuncs.Round(p2.X, roundCount)))
                    {
                        isBeside = true;
                        continue;
                    }

                    if (MathFuncs.Round(p.Y, roundCount) < Math.Min(MathFuncs.Round(p1.Y, roundCount), MathFuncs.Round(p2.Y, roundCount)) ||
                        MathFuncs.Round(p.Y, roundCount) >= Math.Max(MathFuncs.Round(p1.Y, roundCount), MathFuncs.Round(p2.Y, roundCount))) // 交点在p1p2延长线上 
                        continue;


                    // 求交点的 X 坐标 -------------------------------------------------------------- 
                    double x = (p.Y - p1.Y) * (p2.X - p1.X) / (p2.Y - p1.Y) + p1.X;

                    if (Round(x, roundCount) > Round(p.X, roundCount))
                    {
                        nCross++; // 只统计单边交点 
                    }
                    else if (Round(x, roundCount) == Round(p.X, roundCount))
                        isBeside = true;
                }
            }

            private static void getSideRangePointD(PointD[] ptPolygon, int nCount, ref double maxx, ref double maxy, ref double minx, ref double miny)
            {
                for (int j = 1; j < nCount; j++)
                {
                    if (ptPolygon[j].X >= maxx)
                        maxx = ptPolygon[j].X;
                    else if (ptPolygon[j].X <= minx)
                        minx = ptPolygon[j].X;

                    if (ptPolygon[j].Y >= maxy)
                        maxy = ptPolygon[j].Y;
                    else if (ptPolygon[j].Y <= miny)
                        miny = ptPolygon[j].Y;
                }
            }

            /// <summary>
            /// 计算两条直线的交点，延伸直线，WeilerAtherton裁剪法使用，重叠的两条线也要求出交点，为相交的线顶点。
            /// 这里固定p1、p2为被裁剪多边形的边，p3、p4为裁剪多边形的边
            /// </summary>
            /// <param name="p1">L1的点1坐标</param>
            /// <param name="p2">L1的点2坐标</param>
            /// <param name="p3">L2的点1坐标</param>
            /// <param name="p4">L2的点2坐标</param>
            /// <returns></returns>
            private List<PointD> getIntersectionOfTwoLineNotExtendForWeilerAtherton(PointD p1, PointD p2, PointD p3, PointD p4)
            {
                /*
                 * L1，L2都存在斜率的情况：
                 * 直线方程L1: ( y - y1 ) / ( y2 - y1 ) = ( x - x1 ) / ( x2 - x1 ) 
                 * => y = [ ( y2 - y1 ) / ( x2 - x1 ) ]( x - x1 ) + y1
                 * 令 a = ( y2 - y1 ) / ( x2 - x1 )
                 * 有 y = a * x - a * x1 + y1   .........1
                 * 直线方程L2: ( y - y3 ) / ( y4 - y3 ) = ( x - x3 ) / ( x4 - x3 )
                 * 令 b = ( y4 - y3 ) / ( x4 - x3 )
                 * 有 y = b * x - b * x3 + y3 ..........2
                 * 
                 * 如果 a = b，则两直线平等，否则， 联解方程 1,2，得:
                 * x = ( a * x1 - b * x3 - y1 + y3 ) / ( a - b )
                 * y = a * x - a * x1 + y1
                 * 
                 * L1存在斜率, L2平行Y轴的情况：
                 * x = x3
                 * y = a * x3 - a * x1 + y1
                 * 
                 * L1 平行Y轴，L2存在斜率的情况：
                 * x = x1
                 * y = b * x - b * x3 + y3
                 * 
                 * L1与L2都平行Y轴的情况：
                 * 如果 x1 = x3，那么L1与L2重合，否则平等
                 * 
                */
                List<PointD> refList = new List<PointD>();
                double a = 0, b = 0;
                int state = 0;
                if (p1.X != p2.X)
                {
                    a = (p2.Y - p1.Y) / (p2.X - p1.X);
                    state |= 1;
                }
                if (p3.X != p4.X)
                {
                    b = (p4.Y - p3.Y) / (p4.X - p3.X);
                    state |= 2;
                }
                switch (state)
                {
                    case 0: //L1与L2都平行Y轴
                        {
                            //return null;
                            dealNoSlope(p1, p2, p3, p4, refList);
                            break;
                        }
                    case 1: //L1存在斜率, L2平行Y轴
                        {
                            dealL1Slope(p1, p2, p3, p4, refList, a);
                            break;
                        }
                    case 2: //L1 平行Y轴，L2存在斜率
                        {
                            dealL2Slope(p1, p2, p3, p4, refList, b);
                            break;
                        }
                    case 3: //L1，L2都存在斜率
                        {
                            dealTwoLineSlope(p1, p2, p3, p4, refList, a, b);
                            break;
                        }
                }
                return refList;
            }

            private void dealNoSlope(PointD p1, PointD p2, PointD p3, PointD p4, List<PointD> refList)
            {
                if (p1.X == p3.X)   //两条直线互相重合，只返回被裁剪多边形顶点，p1、p2必须后判断，外层要记录为pass掉的点
                {
                    if (pointOnLine(p3, p1, p2, roundCount))
                    {
                        addValidPoint(p3, refList);
                    }
                    if (pointOnLine(p4, p1, p2, roundCount))
                    {
                        addValidPoint(p4, refList);
                    }
                    if (pointOnLine(p1, p3, p4, roundCount))
                    {
                        addValidPoint(p1, refList);
                    }
                    if (pointOnLine(p2, p3, p4, roundCount))
                    {
                        addValidPoint(p2, refList);
                    }
                }
                //else
                //{
                //    throw new Exception("两条直线互相平行，且平行于Y轴，无法计算交点。");
                //}
            }

            private void dealL1Slope(PointD p1, PointD p2, PointD p3, PointD p4, List<PointD> refList, double a)
            {
                double x = p3.X;
                double y = a * x - a * p1.X + p1.Y;
                PointD pTmp = new PointD(x, y);
                bool onLine1 = pointOnLine(pTmp, p1, p2, roundCount);
                bool onLine2 = pointOnLine(pTmp, p3, p4, roundCount);
                if (onLine1 && onLine2)
                {
                    refList.Add(pTmp);
                }
            }

            private void dealL2Slope(PointD p1, PointD p2, PointD p3, PointD p4, List<PointD> refList, double b)
            {
                double x = p1.X;
                double y = b * x - b * p3.X + p3.Y;
                PointD pTmp = new PointD(x, y);
                bool onLine1 = pointOnLine(pTmp, p1, p2, roundCount);
                bool onLine2 = pointOnLine(pTmp, p3, p4, roundCount);
                if (onLine1 && onLine2)
                {
                    refList.Add(pTmp);
                }
                //if (pointOnLine(pTmp, p1, p2, roundCount) && pointOnLine(pTmp, p3, p4, roundCount))
                //{
                //    refList.Add(pTmp);
                //}
            }

            private void dealTwoLineSlope(PointD p1, PointD p2, PointD p3, PointD p4, List<PointD> refList, double a, double b)
            {
                if (a == b) //两条直线平行或重合，重合的时候要求交点，只返回被裁剪多边形顶点，p1、p2必须后判断，外层要记录为pass掉的点
                {
                    if (pointOnLine(p3, p1, p2, roundCount))
                    {
                        addValidPoint(p3, refList);
                    }
                    if (pointOnLine(p4, p1, p2, roundCount))
                    {
                        addValidPoint(p4, refList);
                    }
                    if (pointOnLine(p1, p3, p4, roundCount))
                    {
                        addValidPoint(p1, refList);
                    }
                    if (pointOnLine(p2, p3, p4, roundCount))
                    {
                        addValidPoint(p2, refList);
                    }
                    //throw new Exception("两条直线平行或重合，无法计算交点。");
                }
                else
                {
                    //               x = ( a * x1 - b * x3 - y1 + y3 ) / ( a - b )
                    //* y = a * x - a * x1 + y1
                    double x = (a * p1.X - b * p3.X - p1.Y + p3.Y) / (a - b);
                    double y = a * ((a * p1.X - b * p3.X - p1.Y + p3.Y) / (a - b)) - a * p1.X + p1.Y;
                    PointD pTmp = new PointD(x, y);
                    bool onLine1 = pointOnLine(pTmp, p1, p2, roundCount);
                    bool onLine2 = pointOnLine(pTmp, p3, p4, roundCount);
                    if (onLine1 && onLine2)
                    {
                        refList.Add(pTmp);
                    }
                }
            }

            private void addValidPoint(PointD point, List<PointD> pointList)
            {
                if (!pointList.Contains(point))
                {
                    pointList.Add(point);
                }
            }

            /// <summary>
            /// 点是否在直线上，直线不延伸
            /// </summary>
            /// <param name="point">点</param>
            /// <param name="pointLine1">直线顶点</param>
            /// <param name="pointLine2">直线顶点</param>
            /// <param name="roundCount">点坐标比较时的小数精确位数</param>
            /// <returns></returns>
            private bool pointOnLine(PointD point, PointD pointLine1, PointD pointLine2, int roundCount)
            {
                double xMin = Math.Min(pointLine1.X, pointLine2.X);
                double xMax = Math.Max(pointLine1.X, pointLine2.X);
                double yMin = Math.Min(pointLine1.Y, pointLine2.Y);
                double yMax = Math.Max(pointLine1.Y, pointLine2.Y);
                if (Round(point.X, roundCount) >= Round(xMin, roundCount) && Round(point.X, roundCount) <= Round(xMax, roundCount) &&
                    Round(point.Y, roundCount) >= Round(yMin, roundCount) && Round(point.Y, roundCount) <= Round(yMax, roundCount))
                {
                    if (pointLine1.X == pointLine2.X)
                    {
                        if (point.X == pointLine1.X)
                        {
                            return true;
                        }
                    }
                    else
                    {
                        double K = (pointLine2.Y - pointLine1.Y) / (pointLine2.X - pointLine1.X);
                        double yTmp = (K) * point.X + (pointLine1.Y - pointLine1.X * (K));
                        return judgeOnYAxisLine(point, roundCount, yTmp);
                    }
                }
                return false;
            }

            private bool judgeOnYAxisLine(PointD point, int roundCount, double yTmp)
            {
                if (Round(yTmp, roundCount) == Round(point.Y, roundCount))
                {
                    return true;
                }
                else
                {
                    //精度问题，例如保留两位的时候yTmp=96.664999999999964，point.Y=96.66500000000002
                    for (int i = 0; i < 3; i++)
                    {
                        if (Round(yTmp, roundCount + i + 1) == Round(point.Y, roundCount + i + 1))
                        {
                            return true;
                        }
                    }
                }
                return false;
            }

            /// <summary>
            ///  计算两条直线的交点，第二条直线不延伸
            /// </summary>
            /// <param name="p1"></param>
            /// <param name="p2"></param>
            /// <param name="p3"></param>
            /// <param name="p4"></param>
            /// <returns></returns>
            public static PointD? getIntersectionOfTwoLineTheSecondNotExtend(PointD p1, PointD p2, PointD p3, PointD p4)
            {
                PointD? pointInter = getIntersectionOfTwoLine(p1, p2, p3, p4);
                if (pointInter == null)
                {
                    return null;
                }
                double xMinLine2 = Math.Min(p3.X, p4.X);
                double xMaxLine2 = Math.Max(p3.X, p4.X);
                double yMinLine2 = Math.Min(p3.Y, p4.Y);
                double yMaxLine2 = Math.Max(p3.Y, p4.Y);
                PointD p = (PointD)pointInter;
                if (p.X >= xMinLine2 && p.X <= xMaxLine2 && p.Y >= yMinLine2 && p.Y <= yMaxLine2)
                {
                    return pointInter;
                }
                return null;
            }

            /// <summary>
            /// 计算两条直线的交点，延伸直线
            /// </summary>
            /// <param name="p1">L1的点1坐标</param>
            /// <param name="p2">L1的点2坐标</param>
            /// <param name="p3">L2的点1坐标</param>
            /// <param name="p4">L2的点2坐标</param>
            /// <returns></returns>
            private static PointD? getIntersectionOfTwoLine(PointD p1, PointD p2, PointD p3, PointD p4)
            {
                /*
                 * L1，L2都存在斜率的情况：
                 * 直线方程L1: ( y - y1 ) / ( y2 - y1 ) = ( x - x1 ) / ( x2 - x1 ) 
                 * => y = [ ( y2 - y1 ) / ( x2 - x1 ) ]( x - x1 ) + y1
                 * 令 a = ( y2 - y1 ) / ( x2 - x1 )
                 * 有 y = a * x - a * x1 + y1   .........1
                 * 直线方程L2: ( y - y3 ) / ( y4 - y3 ) = ( x - x3 ) / ( x4 - x3 )
                 * 令 b = ( y4 - y3 ) / ( x4 - x3 )
                 * 有 y = b * x - b * x3 + y3 ..........2
                 * 
                 * 如果 a = b，则两直线平等，否则， 联解方程 1,2，得:
                 * x = ( a * x1 - b * x3 - y1 + y3 ) / ( a - b )
                 * y = a * x - a * x1 + y1
                 * 
                 * L1存在斜率, L2平行Y轴的情况：
                 * x = x3
                 * y = a * x3 - a * x1 + y1
                 * 
                 * L1 平行Y轴，L2存在斜率的情况：
                 * x = x1
                 * y = b * x - b * x3 + y3
                 * 
                 * L1与L2都平行Y轴的情况：
                 * 如果 x1 = x3，那么L1与L2重合，否则平等
                 * 
                */
                double a = 0, b = 0;
                int state = 0;
                if (p1.X != p2.X)
                {
                    a = (p2.Y - p1.Y) / (p2.X - p1.X);
                    state |= 1;
                }
                if (p3.X != p4.X)
                {
                    b = (p4.Y - p3.Y) / (p4.X - p3.X);
                    state |= 2;
                }
                switch (state)
                {
                    case 0: //L1与L2都平行Y轴
                        {
                            return null;
                            //if (p1.X == p3.X)
                            //{
                            //    throw new Exception("两条直线互相重合，且平行于Y轴，无法计算交点。");
                            //}
                            //else
                            //{
                            //    throw new Exception("两条直线互相平行，且平行于Y轴，无法计算交点。");
                            //}
                        }
                    case 1: //L1存在斜率, L2平行Y轴
                        {
                            double x = p3.X;
                            double y = a * x - a * p1.X + p1.Y;
                            return new PointD(x, y);
                        }
                    case 2: //L1 平行Y轴，L2存在斜率
                        {
                            double x = p1.X;
                            double y = b * x - b * p3.X + p3.Y;
                            return new PointD(x, y);
                        }
                    case 3: //L1，L2都存在斜率
                        {
                            if (a == b)
                            {
                                return null;
                                //throw new Exception("两条直线平行或重合，无法计算交点。");
                            }
                            double x = (a * p1.X - b * p3.X - p1.Y + p3.Y) / (a - b);
                            double y = a * x - a * p1.X + p1.Y;
                            return new PointD(x, y);
                        }
                }
                return null;
            }
        }

        /// <summary>
        /// WeilerAtherton裁剪法定义的顶点类
        /// </summary>
        private class PointForWeilerA
        {
            int roundCount { get; set; } = 6;
            public PointForWeilerA(PointD pointD)
            {
                this.point = pointD;
            }
            //public PointForWeilerA(PointF pointF)
            //{
            //    this.point = new PointD(pointF);
            //}
            public PointD point { get; set; }
            /// <summary>
            /// 标记：0.未标记；1.入；2.出；
            /// </summary>
            public int flag { get; set; } = 0;
            /// <summary>
            /// 如果是交点，保存在被裁剪数组中位置
            /// </summary>
            public int indexInOld { get; set; } = -1;
            /// <summary>
            /// 如果是交点，保存在裁剪数组中位置
            /// </summary>
            public int indexInClip { get; set; } = -1;

            public PointForWeilerA Clone()
            {
                PointForWeilerA refP = new PointForWeilerA(point);
                refP.flag = flag;
                refP.indexInOld = indexInOld;
                refP.indexInClip = indexInClip;
                return refP;
            }

            public override bool Equals(object obj)
            {
                PointForWeilerA other = obj as PointForWeilerA;
                return MathFuncs.Round(point.X, roundCount) == MathFuncs.Round(other.point.X, roundCount) &&
                    MathFuncs.Round(point.Y, roundCount) == MathFuncs.Round(other.point.Y, roundCount);
            }

            public bool EqualsApproximatelyPoint(PointForWeilerA other)
            {
                return MathFuncs.EqualsApproximately(point.X, other.point.X, roundCount) &&
                    MathFuncs.EqualsApproximately(point.Y, other.point.Y, roundCount);
            }

            public override int GetHashCode()
            {
                return point.X.GetHashCode() ^ point.Y.GetHashCode();
            }
        }

        private struct PointD
        {
            public PointD(double x, double y)
            {
                X = x;
                Y = y;
            }
            //public PointD(PointF pointF)
            //{
            //    X = pointF.X;
            //    Y = pointF.Y;
            //}
            public double X { get; set; }
            public double Y { get; set; }

            public override bool Equals(object obj)
            {
                PointD other = (PointD)obj;
                return MathFuncs.Round(X, 6) == MathFuncs.Round(other.X, 6) &&
                    MathFuncs.Round(Y, 6) == MathFuncs.Round(other.Y, 6);
            }

            public override int GetHashCode()
            {
                return base.GetHashCode();
            }
        }
    }
}
