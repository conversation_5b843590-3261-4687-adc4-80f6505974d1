﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Model.Interface
{
    public class InterfaceManager
    {
        private static InterfaceManager instance = null;
        public static InterfaceManager GetInstance()
        {
            if (instance == null)
            {
                instance = new InterfaceManager();
            }
            return instance;
        }

        public StatImgDefItem GetStatImgDef(string imgCode)
        {
            StatImgDefItem ret = null;
            imgCodeIDDefDic.TryGetValue(imgCode, out ret);
            return ret;
        }
       
        public ColumnDefItem GetColumnDef(int imgid, int paraid, int tableid)
        {
            ColumnDefItem ret = null;
            string strTriId = imgid + "," + paraid + "," + tableid;
            triIdsToColumnDefDic.TryGetValue(strTriId, out ret);
            return ret;
        }

        public StatImgDefItem GetStatImgDef(int imgid, int paraid, int tableid)
        {
            StatImgDefItem ret = null;
            string strTriId = imgid + "," + paraid + "," + tableid;
            triIdsToImgDefDic.TryGetValue(strTriId, out ret);
            return ret;
        }
        public List<ColumnDefItem> GetColumnDefByShowName(string showname)
        {
            List<ColumnDefItem> ret = null;
            showname = getRealParamName(showname);
            showNameToColumnDefDic.TryGetValue(showname, out ret);
            return ret;
        }

        /// <summary>
        /// 获取底层的参数名称，（如AppThroughputDL_kb对应的是AppThroughputDL）
        /// </summary>
        /// <returns></returns>
        private string getRealParamName(string showname)
        {
            if (showname.EndsWith("_kb") || showname.EndsWith("_Mb"))
            {//为了省事，如此处理
                if (showname == "TD_AppThroughputDL_kb")
                {
                    return "TD_APP_ThroughputDL";
                }
                else if (showname == "TD_AppThroughputUL_kb")
                {
                    return "TD_APP_ThroughputUL";
                }
                return showname.Substring(0, showname.Length - 3);
            }
            return showname;
        }

        //quick helpers image
        private readonly Dictionary<string, StatImgDefItem> imgCodeIDDefDic = new Dictionary<string, StatImgDefItem>();
        private readonly Dictionary<string, StatImgDefItem> triIdsToImgDefDic = new Dictionary<string, StatImgDefItem>();
        //quick helpers column
        private readonly Dictionary<string, ColumnDefItem> triIdsToColumnDefDic = new Dictionary<string, ColumnDefItem>();
        public Dictionary<string, ColumnDefItem> TridIDColDefDic
        {
            get { return triIdsToColumnDefDic; }
        }
        //quick helpers for non fix sample column 
        /// <summary>
        /// 通过表id+ imageid —> 所有nonfix paras List
        /// </summary>
        private readonly Dictionary<string, List<ColumnDefItem>> columnIdsToDefsInColumn = new Dictionary<string, List<ColumnDefItem>>();
        /// <summary>
        /// 采样点列，显示名称->配置
        /// </summary>
        private readonly Dictionary<string, List<ColumnDefItem>> showNameToColumnDefDic = new Dictionary<string, List<ColumnDefItem>>();
        
        internal List<ColumnDefItem> GetOtherDefInSameColumn(ColumnDefItem cdf)
        {
            List<ColumnDefItem> ret;
            string tbIdImgId = cdf.tableID + "," + cdf.imgID;
            columnIdsToDefsInColumn.TryGetValue(tbIdImgId, out ret);
            return ret;
        }

        /// <summary>
        /// 根据token和统计单元ID，获取修改过的三元组ID(imgID，1，tbID)
        /// </summary>
        /// <param name="tbToken"></param>
        /// <param name="defCode"></param>
        /// <returns></returns>
        public string GetRevisedStatImgTriadID(StatTbToken tbToken, string defCode)
        {
            StatImgDefItem defItem = GetStatImgDef(defCode);
            if (defItem != null)
            {
                Dictionary<StatTbToken, Dictionary<int,bool>> tokenTbIDDic = null;
                if (tbNameTokenIDDic.TryGetValue(defItem.ShortTbName, out tokenTbIDDic))
                {
                    Dictionary<int, bool> tbIDDic;
                    if (tokenTbIDDic.TryGetValue(tbToken, out tbIDDic))
                    {
                        StringBuilder ret = new StringBuilder();
                        
                        foreach (int tbID in tbIDDic.Keys)
                        {
                            ret.Append(defItem.imgID.ToString() + "," + defItem.paraID + "," + tbID.ToString() + ",");
                        }
                        return ret.ToString().TrimEnd(',');
                    }
                }
            }
            return null;
        }

        readonly Dictionary<string, Dictionary<StatTbToken, Dictionary<int, bool>>> tbNameTokenIDDic 
            = new Dictionary<string, Dictionary<StatTbToken, Dictionary<int, bool>>>();
        internal void AddStatImgDef(StatImgDefItem imgDef)
        {
            if (!imgCodeIDDefDic.ContainsKey(imgDef.itemCode))
            {
                imgCodeIDDefDic[imgDef.itemCode] = imgDef;
                if (imgDef.itemNewCode != "")
                    imgCodeIDDefDic[imgDef.itemNewCode] = imgDef;
            }
            triIdsToImgDefDic[imgDef.GetTriIdStr()] = imgDef;

            Dictionary<StatTbToken, Dictionary<int, bool>> tokenTbIDDic = null;
            if (!tbNameTokenIDDic.TryGetValue(imgDef.ShortTbName, out tokenTbIDDic))
            {
                tokenTbIDDic = new Dictionary<StatTbToken, Dictionary<int, bool>>();
                tbNameTokenIDDic[imgDef.ShortTbName] = tokenTbIDDic;
            }

            Dictionary<int, bool> tbIDDic = null;
            if (!tokenTbIDDic.TryGetValue(imgDef.Token, out tbIDDic))
            {
                tbIDDic = new Dictionary<int, bool>();
                tokenTbIDDic[imgDef.Token] = tbIDDic;
            }
            tbIDDic[imgDef.tableID] = true;
        }

        internal void AddColumnDef(ColumnDefItem itm)
        {
            triIdsToColumnDefDic[itm.GetTriIdStr()] = itm;

            if (!itm.fix)
            {
                string tbIdImgId = itm.tableID + "," + itm.imgID;
                List<ColumnDefItem> nonfixList;
                if (!columnIdsToDefsInColumn.TryGetValue(tbIdImgId, out nonfixList))
                {
                    nonfixList = new List<ColumnDefItem>();
                    columnIdsToDefsInColumn[tbIdImgId] = nonfixList;
                }
                nonfixList.Add(itm);
            }
            //

            List<ColumnDefItem> defOfNameList = null;
            if (!showNameToColumnDefDic.TryGetValue(itm.showName, out defOfNameList))
            {
                defOfNameList = new List<ColumnDefItem>();
                showNameToColumnDefDic[itm.showName] = defOfNameList;
            }
            defOfNameList.Add(itm);
        }
    }
}
