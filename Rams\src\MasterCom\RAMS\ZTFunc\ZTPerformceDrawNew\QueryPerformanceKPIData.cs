﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using DTLibrary;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;


namespace MasterCom.RAMS.ZTFunc
{
    public class QueryPerformanceKPIData : DIYSQLBase
    {
        public QueryPerformanceKPIData(MainModel mainModel, string mynetwork)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            network = mynetwork;
        }
        protected override string getSqlTextString()
        {
            throw new NotImplementedException("The method or operation is not implemented.");
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            throw new NotImplementedException("The method or operation is not implemented.");
        }          
        
        public override string Name
        {
            get { return "性能KPI渲染呈现"; }
        }
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }
        /// <summary>
        /// 涉及的变量
        /// </summary>             
        Dictionary<DtParaKey, Dictionary<String, DtParaValueItem>> firstReslut { get; set; } = new Dictionary<DtParaKey, Dictionary<string, DtParaValueItem>>();
        Dictionary<DtParaKey, Dictionary<String, DtParaValueItem>> finalReslut { get; set; } = new Dictionary<DtParaKey, Dictionary<string, DtParaValueItem>>();        
        Dictionary<string, List<DtParaColumnItem>> tdNameAndPcItemList { get; set; } = new Dictionary<string, List<DtParaColumnItem>>();
        protected List<E_VType> fieldTypeArr = new List<E_VType>();
        private List<object[]> resultSet { get; set; } = new List<object[]>();
        private List<String> itemList = new List<string>();       
        List<string> listTbName { get; set; } = new List<string>();
        List<string> strSQL { get; set; } = new List<string>();       
        protected E_VType[] fieldTypeArry { get; set; }
        List<DtParaColumnItem> KPIList { get; set; }
        string strsql { get; set; } = "";
        public string SelectKPIName { get; set; }
        public string network { get; set; }
        public DateTime SelectTime { get; set; }
        double minKPI = 0;
        double maxKPI = 0;
        private List<DtParaColumnItem> KPISplit { get; set; }
        private Dictionary<DtParaKey, float> getKpiValueList { get; set; }
        private Dictionary<Cell, double> cellKPI { get; set; }
        private Dictionary<TDCell, double> tdCellKPI { get; set; }
        private string readXmlPath { get; set; } = "userData\\DtFormulaCfg.xml";

        /// <summary>
        /// 准备查询条件
        /// </summary>
        protected override void query()
        {
            tdNameAndPcItemList.Clear();
            listTbName.Clear();
            KPISplit = new List<DtParaColumnItem>();
            ReadXML rdm = new ReadXML();
            KPISplit.AddRange(rdm.getKPISplitXml(readXmlPath, network));
            XtraPerformanceDrawSetForm xtraperformstatus = new XtraPerformanceDrawSetForm(MainModel, network, KPISplit);
            SelectKPIName = "";
            SelectTime = new DateTime();
            minKPI = 0;
            maxKPI = 3;
            if (xtraperformstatus.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            string name;
            DateTime time;
            xtraperformstatus.getSelect(out name, out time, out minKPI, out maxKPI);
            SelectKPIName = name;
            SelectTime = time;

            saveThresholdDrawToXml();
            KPIList = new List<DtParaColumnItem>();
            foreach (DtParaColumnItem kpilis in KPISplit)
            {
                if (kpilis.StrKpiName.Equals(SelectKPIName))
                {
                    KPIList.Add(kpilis);
                }
            }
            foreach (DtParaColumnItem tdname in KPIList)
            {
                if (!listTbName.Contains(tdname.StrTableName))
                {
                    listTbName.Add(tdname.StrTableName);
                }
            }            
            foreach (string tdn in listTbName)
            {
                List<DtParaColumnItem> PcItemList = new List<DtParaColumnItem>();
                foreach (DtParaColumnItem pci in KPIList)
                {
                    if (pci.StrTableName.Equals(tdn))
                        PcItemList.Add(pci);
                }
                tdNameAndPcItemList.Add(tdn, PcItemList);
            }               
            WaitBox.Text = "准备查询...";
            WaitBox.CanCancel = true;
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("开始接收数据...", queryInThread, clientProxy);
            }
            finally
            {
                WaitBox.Close();
                clientProxy.Close();
            }
        }
        /// <summary>
        /// 查询数据
        /// </summary>
        protected override void queryInThread(object o)
        {
            finalReslut.Clear();
            foreach (string tdname in tdNameAndPcItemList.Keys)
            {
                DTParaManage dtpm = new DTParaManage();
                strSQL.Clear();
                fieldTypeArr.Clear();
                resultSet.Clear();
                itemList.Clear();
                strSQL.AddRange(dtpm.getKpiNameList(SelectTime, SelectTime, tdname, tdNameAndPcItemList[tdname], ref fieldTypeArr, ref itemList));
                fieldTypeArry = new E_VType[fieldTypeArr.Count];
                for (int k = 0; k < fieldTypeArr.Count; k++)
                {
                    fieldTypeArry[k] = fieldTypeArr[k];
                }
                foreach (string sql in strSQL)
                {
                    strsql = sql;
                    try
                    {
                        query(o);
                    }
                    catch
                    {
                        //continue
                    }
                }
                firstReslut = dtpm.getParaValue(resultSet, itemList, tdNameAndPcItemList[tdname]);
                foreach (DtParaKey dtpk in firstReslut.Keys)
                {
                    addFinalReslut(dtpk);
                }
            }
            dealWithResultSet();           
            WaitBox.Close();
            getCellKPI();
        }

        private void query(object o)
        {
            ClientProxy clientProxy = (ClientProxy)o;
            Package package = clientProxy.Package;
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            if (MainDB)
            {
                package.Content.Type = MasterCom.RAMS.Net.RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
            }
            else
            {
                package.Content.Type = MasterCom.RAMS.Net.RequestType.REQTYPE_DIY_MODEL_SQL;
            }
            package.Content.PrepareAddParam();
            package.Content.AddParam(strsql);
            StringBuilder sb = new StringBuilder();
            if (fieldTypeArry != null)
            {
                for (int i = 0; i < fieldTypeArry.Length; i++)
                {
                    sb.Append((int)fieldTypeArry[i]);
                    if (i < fieldTypeArry.Length - 1)
                    {
                        sb.Append(",");
                    }
                }
            }
            package.Content.AddParam(sb.ToString());
            clientProxy.Send();
            receiveRetData(clientProxy);
        }

        private void addFinalReslut(DtParaKey dtpk)
        {
            if (!finalReslut.ContainsKey(dtpk))
            {
                finalReslut.Add(dtpk, firstReslut[dtpk]);
            }
            else
            {
                Dictionary<String, DtParaValueItem> finalValue;
                finalValue = finalReslut[dtpk];
                foreach (string sunDtKey in firstReslut[dtpk].Keys)
                {
                    if (!finalValue.ContainsKey(sunDtKey))
                    {
                        finalValue.Add(sunDtKey, firstReslut[dtpk][sunDtKey]);
                    }
                }
                finalReslut.Remove(dtpk);
                finalReslut.Add(dtpk, finalValue);
            }
        }

        /// <summary>
        /// 接收数据
        /// </summary>
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            WaitBox.Text = "正在接收数据...";
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 10;
            while (true)
            {
                if (WaitBox.CancelRequest)
                    WaitBox.Close();

                if (WaitBox.ProgressPercent >= 95)
                {
                    WaitBox.ProgressPercent = 5;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    addResultSet(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                WaitBox.ProgressPercent++;
            }
            WaitBox.ProgressPercent = 99;
        }

        private void addResultSet(Package package)
        {
            int index = 0;
            object[] resultSetArr = new object[fieldTypeArry.Length];
            for (int i = 0; i < fieldTypeArry.Length; i++)
            {
                E_VType type = fieldTypeArry[i];
                if (type == E_VType.E_String)
                {
                    resultSetArr[index++] = package.Content.GetParamString();
                }
                else if (type == E_VType.E_Byte)
                {
                    resultSetArr[index++] = package.Content.GetParamByte();
                }
                else if (type == E_VType.E_Float)
                {
                    resultSetArr[index++] = package.Content.GetParamFloat();
                }
                else if (type == E_VType.E_Int)
                {
                    resultSetArr[index++] = package.Content.GetParamInt();
                }
                else if (type == E_VType.E_IntFloat)
                {
                    resultSetArr[index++] = package.Content.GetParamFloat();
                }
                else if (type == E_VType.E_Short)
                {
                    resultSetArr[index++] = package.Content.GetParamShort();
                }
            }
            resultSet.Add(resultSetArr);
        }

        /// <summary>
        /// 获取选择渲染指标的性能值
        /// </summary>
        protected void dealWithResultSet()
        {
            getKpiValueList = new Dictionary<DtParaKey, float>();
            foreach (DtParaKey dtKey in finalReslut.Keys)
            {
                foreach (string kname in finalReslut[dtKey].Keys)
                {
                    if (kname.Equals(SelectKPIName))
                    {
                        getKpiValueList.Add(dtKey, finalReslut[dtKey][kname].FValue);
                    }
                }
            }
        }
        /// <summary>
        /// 构建小区、性能字典并且进行云图渲染
        /// </summary>
        private void getCellKPI()
        {
            cellKPI = new Dictionary<Cell, double>();
            tdCellKPI = new Dictionary<TDCell, double>();
            double dKPI = 0;
            foreach (DtParaKey dy in getKpiValueList.Keys)
            {
                Cell cell = null;
                TDCell tdCell = null;
                dKPI = double.Parse(getKpiValueList[dy].ToString());
                if (maxKPI < 0)
                    dKPI = -dKPI;
                addCellKpi(ref dKPI, dy, ref cell, ref tdCell);
            }
            MainModel.MainForm.GetMapForm().clearData();
            if (network.Equals("GSM"))
                CellWeightRegionManager.GetInstance().CreateCellWeightRegion(cellKPI, minKPI, maxKPI);
            else
                TDCellWeightRegionManager.GetInstance().CreateTDCellWeightRegion(tdCellKPI, minKPI, maxKPI);
            MainModel.MainForm.GetMapForm().FireCellWeightRegionShow();
        }

        private void addCellKpi(ref double dKPI, DtParaKey dy, ref Cell cell, ref TDCell tdCell)
        {
            if (network.Equals("GSM"))
            {
                cell = MainModel.CellManager.GetCell(dy.DTime, (ushort)dy.ILac, (ushort)dy.ICi);
                if (cell != null)
                {
                    if (dKPI > maxKPI)
                        dKPI = maxKPI;
                    else if (dKPI < minKPI)
                        dKPI = minKPI;
                    cellKPI.Add(cell, dKPI);
                }
            }
            else
            {
                tdCell = MainModel.CellManager.GetTDCell(dy.DTime, (ushort)dy.ILac, (ushort)dy.ICi);
                if (tdCell != null)
                {
                    if (dKPI > maxKPI)
                        dKPI = maxKPI;
                    else if (dKPI < minKPI)
                        dKPI = minKPI;
                    tdCellKPI.Add(tdCell, dKPI);
                }
            }
        }

        /// <summary>
        /// 保存指标阈值
        /// </summary>
        private void saveThresholdDrawToXml()
        {
            XmlDocument doc = new XmlDocument();
            try
            {
                doc.Load(readXmlPath);
                XmlNode xn;
                xn = doc.DocumentElement.SelectSingleNode("Config");                   //找到根节点
                foreach (XmlNode item2 in xn.SelectSingleNode("Item"))
                {
                    foreach (XmlNode item3 in item2.ChildNodes)
                    {
                        setSubNode(item2, item3);
                    }
                }
                doc.Save(readXmlPath);
            }
            catch
            {
                //continue       
            }
        }

        private void setSubNode(XmlNode item2, XmlNode item3)
        {
            if (((XmlElement)item3).GetAttribute("name") == "KpiName" && ((XmlElement)item3).InnerText.Equals(SelectKPIName))
            {
                foreach (XmlNode item4 in item2.ChildNodes)
                {
                    if (((XmlElement)item4).GetAttribute("name") == "ThresholdDraw")
                    {
                        ((XmlElement)item4).InnerText = "[" + minKPI.ToString() + "," + maxKPI.ToString() + "]";
                        break;
                    }
                }
            }
        }
    }
}
