<?xml version="1.0"?>
<Configs>
  <Config name="ReportSetting">
    <Item name="styles" typeName="IDictionary">
      <Item typeName="String" key="Name">中国联通2011年WCDMA评估标准（带标准）</Item>
      <Item typeName="IList" key="Cells">
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">序号</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">项目</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">内容</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">覆盖</Item>
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">良好覆盖率RSCP≥-80dBm比例
</Item>
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">上下行功率平衡  （RSCP≥-80dBm &amp; TxPower≤-10dBm采样点/RSCP≥-80dBm采样点/比例）

</Item>
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">软切换比例


</Item>
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">干扰




</Item>
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Ec/Io≥-11dB的比例





</Item>
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">导频污染点比例






</Item>
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">业务质量




</Item>
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">话音业务接通率





</Item>
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">VP业务接通率

</Item>
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">27</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">数据业务接通率


</Item>
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">话音业务掉话率



</Item>
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">VP业务掉话率




</Item>
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">HSPA掉线率





</Item>
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">CQI-MPO





</Item>
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">实测HSDPA吞吐率






</Item>
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">实测HSUPA吞吐率







</Item>
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">吞吐率低于门限值的路段比例









</Item>
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">MOS值分布










</Item>
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">MOS均值










</Item>
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">系统间切换失败比例 











</Item>
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">2/3G互操作












</Item>
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">指标结果












</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">19</Item>
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">18</Item>
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">17</Item>
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">16</Item>
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">15</Item>
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">14</Item>
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">13</Item>
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">12</Item>
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">11</Item>
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">10</Item>
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">9</Item>
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">8</Item>
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">7</Item>
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">6</Item>
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">5</Item>
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">4</Item>
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">3</Item>
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">2</Item>
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(evtIdCount[542]+evtIdCount[611])/(evtIdCount[540]+evtIdCount[609]) }%</Item>
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Wx_6D0A01+Wx_6D0A02+Wx_6D0A03+Wx_6D0A04+Wx_6D0E01+Wx_6D0E02+Wx_6D0E03+Wx_6D0E04)/(Wx_6D0A08+Wx_6D0E08) }%</Item>
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*value3[587]/(Wx_0822*1000)}%</Item>
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(evtIdCount[58]+evtIdCount[61])/(evtIdCount[56]+evtIdCount[59]) }%</Item>
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(evtIdCount[505]+evtIdCount[511]+evtIdCount[517]+evtIdCount[523])/(evtIdCount[501]+evtIdCount[507]+evtIdCount[513]+evtIdCount[519]) }%</Item>
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*evtIdCount[565]/evtIdCount[564] }%</Item>
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*evtIdCount[555]/evtIdCount[554] }%</Item>
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(evtIdCount[501]+evtIdCount[507]+evtIdCount[513]+evtIdCount[519])/(evtIdCount[500]+evtIdCount[506]+evtIdCount[512]+evtIdCount[518]) }%</Item>
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Wx_5D0A0402+Wx_5D0A0403+Wx_5D0A0404+Wx_5D0A0405+Wx_5D0A0406+Wx_5D0E0402+Wx_5D0E0403+Wx_5D0E0404+Wx_5D0E0405+Wx_5D0E0406+Wx_5D0B0402+Wx_5D0B0403+Wx_5D0B0404+Wx_5D0B0405+Wx_5D0B0406+Wx_5D0F0402+Wx_5D0F0403+Wx_5D0F0404+Wx_5D0F0405+Wx_5D0F0406+Wx_5D1C0402+Wx_5D1C0403+Wx_5D1C0404+Wx_5D1C0405+Wx_5D1C0406)/(Wx_5D0A0401+Wx_5D0A0402+Wx_5D0A0403+Wx_5D0A0404+Wx_5D0A0405+Wx_5D0A0406+Wx_5D0E0401+Wx_5D0E0402+Wx_5D0E0403+Wx_5D0E0404+Wx_5D0E0405+Wx_5D0E0406+Wx_5D0B0401+Wx_5D0B0402+Wx_5D0B0403+Wx_5D0B0404+Wx_5D0B0405+Wx_5D0B0406+Wx_5D0F0401+Wx_5D0F0402+Wx_5D0F0403+Wx_5D0F0404+Wx_5D0F0405+Wx_5D0F0406+Wx_5D1C0401+Wx_5D1C0402+Wx_5D1C0403+Wx_5D1C0404+Wx_5D1C0405+Wx_5D1C0406) }%</Item>
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Wx_6F0A03+Wx_6F0E03+Wx_6F0B03+Wx_6F0F03+Wx_6F1C03)/(Wx_6F0A02+Wx_6F0E02+Wx_6F0B02+Wx_6F0F02+Wx_6F1C02) }%</Item>
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Wx_710A33+Wx_710A34+Wx_710A35+Wx_710E33+Wx_710E34+Wx_710E35+Wx_710B33+Wx_710B34+Wx_710B35+Wx_710F33+Wx_710F34+Wx_710F35+Wx_711C33+Wx_711C34+Wx_711C35  )/(Wx_710A3C+Wx_710E3C+Wx_710B3C+Wx_710F3C+Wx_711C3C) }%</Item>
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">WCDMA</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">1</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">20</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">20</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">GSM</Item>
          <Item typeName="Int32" key="RowAt">20</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">20</Item>
          <Item typeName="Int32" key="RowAt">20</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">覆盖 
</Item>
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">干扰 

</Item>
          <Item typeName="Int32" key="RowAt">22</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">业务质量 





</Item>
          <Item typeName="Int32" key="RowAt">23</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">路测良好覆盖率 






</Item>
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">路测C/I 







</Item>
          <Item typeName="Int32" key="RowAt">22</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">接通率 







</Item>
          <Item typeName="Int32" key="RowAt">23</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">掉话率 







</Item>
          <Item typeName="Int32" key="RowAt">24</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">MOS值分布 








</Item>
          <Item typeName="Int32" key="RowAt">25</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">MOS均值 









</Item>
          <Item typeName="Int32" key="RowAt">26</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">21









</Item>
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">22









</Item>
          <Item typeName="Int32" key="RowAt">22</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">23









</Item>
          <Item typeName="Int32" key="RowAt">23</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">24









</Item>
          <Item typeName="Int32" key="RowAt">24</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">25









</Item>
          <Item typeName="Int32" key="RowAt">25</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">26









</Item>
          <Item typeName="Int32" key="RowAt">26</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">26</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">25</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">24</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">26</Item>
          <Item typeName="Int32" key="ColAt">7</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">指标标准</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">计分办法</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">分值</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">85%</Item>
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">95%</Item>
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">35%</Item>
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每低于标准值2%，扣1分；每高于标准值2.5%，加0.5分，加分不超过1分</Item>
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每低于标准值2%，扣1分；每高于标准值2.5%，加0.5分，加分不超过1分</Item>
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每高于标准值5%，扣1分</Item>
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">10</Item>
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">5











</Item>
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">5











</Item>
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">92%





</Item>
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">1%





</Item>
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每低于标准值1%，扣1分；每高于标准值1%，加0.5分





</Item>
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每高于标准值0.1%，扣1分 





</Item>
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">15





</Item>
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">5





</Item>
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">3.75‰ 




</Item>
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每高于标准值2.5‰，扣1分，扣完为止 




</Item>
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">5



</Item>
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">99%




</Item>
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">98.5%




</Item>
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">96%




</Item>
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">0.4%




</Item>
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">0.6%




</Item>
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">0.5%




</Item>
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">16




</Item>
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">2.5M 



</Item>
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">1.2M 



</Item>
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">2%



</Item>
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">9%


</Item>
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">3.5


</Item>
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">15


</Item>
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">15


</Item>
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">15


</Item>
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">15


</Item>
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">15


</Item>
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">15


</Item>
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">5


</Item>
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">5


</Item>
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">5


</Item>
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">5


</Item>
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">5


</Item>
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">5


</Item>
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每低于标准值1%，扣1分 


</Item>
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每低于标准值1%，扣1分 


</Item>
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每低于标准值1%，扣1分 


</Item>
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每高于标准值0.1%，扣2分；每低0.1%，加0.25分，最多加1分 

</Item>
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每高于标准值0.1%，扣2分；每低0.1%，加0.25分，最多加1分 

</Item>
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每高于标准值0.1%，扣2分；每低0.1%，加0.25分，最多加1分 

</Item>
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每低于标准值1，扣1分；每高于标准值1加0.5分，加分不超过2分

</Item>
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每低于标准值500Kbps，扣1分；每高于标准值500Kbps，加0.5分，加分不超过1分

</Item>
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每低于标准值200Kbps，扣1分；每高于标准值200Kbps，加0.5分，加分不超过1分

</Item>
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每高于标准值1%，扣1分，扣完为止

</Item>
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每高于标准值1%，扣0.5分，扣完为止；每低于标准值2%，加0.5分，加分不超过2分

</Item>
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每低于标准值0.2扣0.5分，扣完为止 

</Item>
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">20</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">20</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">20</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">85%






</Item>
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每低于标准值2%，扣1分；每高于标准值2.5%，加0.5分，加分不超过1分






</Item>
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">20






</Item>
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">94%






</Item>
          <Item typeName="Int32" key="RowAt">22</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每低于标准值1%，扣1分；每高于标准值2%，加0.5分




</Item>
          <Item typeName="Int32" key="RowAt">22</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">30




</Item>
          <Item typeName="Int32" key="RowAt">22</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">99%







</Item>
          <Item typeName="Int32" key="RowAt">23</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每低于标准值1%，扣1分 






</Item>
          <Item typeName="Int32" key="RowAt">23</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">14






</Item>
          <Item typeName="Int32" key="RowAt">23</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">6%






</Item>
          <Item typeName="Int32" key="RowAt">24</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每高于标准值0.05%，扣1分，每低于标准值0.05%加0.25分，最多加1分 





</Item>
          <Item typeName="Int32" key="RowAt">24</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">27





</Item>
          <Item typeName="Int32" key="RowAt">24</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">20%





</Item>
          <Item typeName="Int32" key="RowAt">25</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每高于标准值1%，扣0.5分，扣完为止；每低于标准值2%，加0.5分，加分不超过2分




</Item>
          <Item typeName="Int32" key="RowAt">25</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">4




</Item>
          <Item typeName="Int32" key="RowAt">25</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每低于标准值0.2扣0.5分，扣完为止 



</Item>
          <Item typeName="Int32" key="RowAt">26</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">3.5



</Item>
          <Item typeName="Int32" key="RowAt">26</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">5



</Item>
          <Item typeName="Int32" key="RowAt">26</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Wx_050B01050201+Wx_050F01050201+Wx_051C01050201)*8/((Wx_050B01050202+Wx_050F01050202+Wx_051C01050202)*1024) }</Item>
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(evtIdCount[558]+evtIdCount[563])/evtIdCount[555] }%</Item>
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Wx_050B02050301+Wx_050F02050301+Wx_051C02050301)*8/((Wx_050B02050302+Wx_050F02050302+Wx_051C02050302)*1024) }</Item>
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Wx_6D0E09*Wx_6D0E08+Wx_6D0A09*Wx_6D0A08)/(Wx_6D0A08+Wx_6D0E08) }</Item>
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100.0*Mx_640117/Mx_640101 }%</Item>
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Mx_5A010705+Mx_5A01070A)/Mx_5A010709 }%</Item>
          <Item typeName="Int32" key="RowAt">22</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100.0*(evtIdCount[0]-(evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87])) /evtIdCount[0] }%</Item>
          <Item typeName="Int32" key="RowAt">23</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100.0*(evtIdCount[5]+evtIdCount[6])/(evtIdCount[3]+evtIdCount[4]) }%</Item>
          <Item typeName="Int32" key="RowAt">24</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100.0*(Mx_5A010B55+Mx_5A010B56+Mx_5A010B57+Mx_5A010B58)/Mx_5A010B5C }%</Item>
          <Item typeName="Int32" key="RowAt">25</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Mx_5A010B53}</Item>
          <Item typeName="Int32" key="RowAt">26</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Wx_6A0A06+Wx_6A0A05+Wx_6A0A04+Wx_6A0E06+Wx_6A0E05+Wx_6A0E04+Wx_6A0B06+Wx_6A0B05+Wx_6A0B04+Wx_6A0F06+Wx_6A0F05+Wx_6A0F04 +Wx_6A1C06+Wx_6A1C05+Wx_6A1C04 )/(Wx_6A0A09+Wx_6A0E09+Wx_6A0B09+Wx_6A1C09+Wx_6A0F09 ) }%</Item>
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Wx_5D0A0504+Wx_5D0A0505+Wx_5D0A0506+Wx_5D0E0504+Wx_5D0E0505+Wx_5D0E0506+Wx_5D0B0504+Wx_5D0B0505+Wx_5D0B0506+Wx_5D0F0504+Wx_5D0F0505+Wx_5D0F0506+Wx_5D1C0504+Wx_5D1C0505+Wx_5D1C0506)/(Wx_5D0A051F+Wx_5D0E051F +Wx_5D0B051F+Wx_5D0F051F +Wx_5D1C051F )}%</Item>
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Wx_5D0B0202+Wx_5D0F0202+Wx_5D1C0202)/(Wx_5D0B0201+Wx_5D0F0201+Wx_5D1C0201)}</Item>
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item typeName="IList" key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
      </Item>
      <Item typeName="IList" key="Graphs" />
      <Item typeName="IList" key="ColWidth">
        <Item typeName="Int32">58</Item>
        <Item typeName="Int32">69</Item>
        <Item typeName="Int32">215</Item>
        <Item typeName="Int32">58</Item>
        <Item typeName="Int32">263</Item>
        <Item typeName="Int32">67</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
      </Item>
    </Item>
  </Config>
</Configs>