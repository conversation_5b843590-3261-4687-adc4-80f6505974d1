﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class FRFreConVerifyInfo
    {
        public int SN { get; set; }
        public string GridName 
        { 
            get 
            {
                if (ListFrItemsInfo.Count > 0)
                {
                    return ListFrItemsInfo[0].GridName;
                }
                return null;
            } 
        }

        public List<FrItemInfo> ListFrItemsInfo { get; set; } = new List<FrItemInfo>();
    }

    public class FrItemInfo
    {
        public FrItemInfo(string fileName)
        {
            this.FileName = fileName;
        }
        //文件名称
        public string FileName { get; set; }

        //网格名称
        public string GridName
        {
            get;
            set;
        }
        
        //事件时间
        public System.DateTime EventTime { get; set; }

        //事件经度
        public double EventLongitude { get; set; }

        //事件纬度
        public double EventLatitude { get; set; }

        //通话结束GSM小区名称
        public string GSM_Name { get; set; }

        //通话结束GSM小区LAC
        public int GSM_LAC { get; set; }

        //通话结束GSM小区CI
        public int GSM_CI { get; set; }

        //通话结束GSM小区与事件距离
        public double GSM_Distance { get; set; }

        //通话结束GSM信号强度均值
        public double GSMRxLev { get; set; }

        //通话结束GSM质量均值
        public double GSMQual { get; set; }

        //网络侧FR功能是否开启
        public string HasFreON { get; set; }

        //Channel Release下发的LTE频点清单
        public string ChannelReleList { get; set; }

        //LTE小区名称
        public string SCell_Name { get; set; }

        //LTE基站名称
        public string SCell_BaseName { get; set; }

        //LTE小区CI
        public double SCell_ECI { get; set; }

        //LTE小区LAC
        public double SCell_LAC { get; set; }

        //LTE小区与事件距离
        public double SCell_Distance { get; set; }
        
        //LTE小区RSRP均值
        public double SCell_RSRP { get; set; }
        					
        //LTE小区SINR均值
        public double SCell_SINR { get; set; }

        //LTE小区RSRQ均值
        public double SCell_RSRQ { get; set; }

        //LTE小区RSSI均值
        public double SCell_RSSI { get; set; }

        //回落LTE小区频点
        public double Back_SCell_EARFCN { get; set; }

        //回落LTE小区PCI
        public double Back_SCell_PCI { get; set; }

        //距离最近LTE小区CI
        public string NearLTECI { get; set; }

        //距离最近LTE小区名称
        public string NearLTEName { get; set; }

        //距离最近LTE小区频点
        public string NearLTEFre { get; set; }

        //序号
        public int SN { get; set; }
       
        public List<TestPoint> ListGSMPoints { get; set; } = new List<TestPoint>();
        public List<TestPoint> ListLTEPoints { get; set; } = new List<TestPoint>();
        public List<LTECell> ListLTECells { get; set; } = new List<LTECell>();
        public Event EvtDone { get; set; }

    }

}
