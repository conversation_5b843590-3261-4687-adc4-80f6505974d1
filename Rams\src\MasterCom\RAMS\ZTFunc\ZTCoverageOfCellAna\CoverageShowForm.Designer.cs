﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CoverageShowForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.btnExportShapefile = new DevExpress.XtraEditors.SimpleButton();
            this.checkEditMarkNum = new DevExpress.XtraEditors.CheckEdit();
            this.label1 = new System.Windows.Forms.Label();
            this.radioBtnCell = new System.Windows.Forms.RadioButton();
            this.radioBtnTestpoint = new System.Windows.Forms.RadioButton();
            this.xtraTabControl2 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage5 = new DevExpress.XtraTab.XtraTabPage();
            this.listViewMultiCellColor_Tp = new System.Windows.Forms.ListView();
            this.columnHeader10 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader12 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShowCell = new System.Windows.Forms.ToolStripMenuItem();
            this.miEditColor = new System.Windows.Forms.ToolStripMenuItem();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.xtraTabControl3 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage8 = new DevExpress.XtraTab.XtraTabPage();
            this.listViewMultiCellColor_Fl = new System.Windows.Forms.ListView();
            this.columnHeader20 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader21 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditMarkNum.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl2)).BeginInit();
            this.xtraTabControl2.SuspendLayout();
            this.xtraTabPage5.SuspendLayout();
            this.contextMenuStrip.SuspendLayout();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl3)).BeginInit();
            this.xtraTabControl3.SuspendLayout();
            this.xtraTabPage8.SuspendLayout();
            this.SuspendLayout();
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Location = new System.Drawing.Point(4, 1);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(434, 311);
            this.xtraTabControl1.TabIndex = 0;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.btnExportShapefile);
            this.xtraTabPage1.Controls.Add(this.checkEditMarkNum);
            this.xtraTabPage1.Controls.Add(this.label1);
            this.xtraTabPage1.Controls.Add(this.radioBtnCell);
            this.xtraTabPage1.Controls.Add(this.radioBtnTestpoint);
            this.xtraTabPage1.Controls.Add(this.xtraTabControl2);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(427, 281);
            this.xtraTabPage1.Text = "采样点";
            // 
            // btnExportShapefile
            // 
            this.btnExportShapefile.Location = new System.Drawing.Point(139, 3);
            this.btnExportShapefile.Name = "btnExportShapefile";
            this.btnExportShapefile.Size = new System.Drawing.Size(41, 23);
            this.btnExportShapefile.TabIndex = 2;
            this.btnExportShapefile.Text = "Shape";
            this.btnExportShapefile.ToolTip = "导出shape文件";
            this.btnExportShapefile.Click += new System.EventHandler(this.btnExportShapefile_Click);
            // 
            // checkEditMarkNum
            // 
            this.checkEditMarkNum.Location = new System.Drawing.Point(17, 7);
            this.checkEditMarkNum.Name = "checkEditMarkNum";
            this.checkEditMarkNum.Properties.Caption = "是否标记场强值";
            this.checkEditMarkNum.Size = new System.Drawing.Size(105, 19);
            this.checkEditMarkNum.TabIndex = 2;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.ForeColor = System.Drawing.Color.Maroon;
            this.label1.Location = new System.Drawing.Point(171, 38);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(247, 14);
            this.label1.TabIndex = 8;
            this.label1.Text = "注：使用场强着色则使表中的自定义颜色无效";
            // 
            // radioBtnCell
            // 
            this.radioBtnCell.AutoSize = true;
            this.radioBtnCell.Location = new System.Drawing.Point(19, 56);
            this.radioBtnCell.Name = "radioBtnCell";
            this.radioBtnCell.Size = new System.Drawing.Size(109, 18);
            this.radioBtnCell.TabIndex = 6;
            this.radioBtnCell.Text = "按占用小区着色";
            this.radioBtnCell.UseVisualStyleBackColor = true;
            this.radioBtnCell.CheckedChanged += new System.EventHandler(this.radioBtnCell_CheckedChanged);
            // 
            // radioBtnTestpoint
            // 
            this.radioBtnTestpoint.AutoSize = true;
            this.radioBtnTestpoint.Checked = true;
            this.radioBtnTestpoint.Location = new System.Drawing.Point(19, 36);
            this.radioBtnTestpoint.Name = "radioBtnTestpoint";
            this.radioBtnTestpoint.Size = new System.Drawing.Size(145, 18);
            this.radioBtnTestpoint.TabIndex = 5;
            this.radioBtnTestpoint.TabStop = true;
            this.radioBtnTestpoint.Text = "按采样点场强信息着色";
            this.radioBtnTestpoint.UseVisualStyleBackColor = true;
            this.radioBtnTestpoint.CheckedChanged += new System.EventHandler(this.radioBtnTestpoint_CheckedChanged);
            // 
            // xtraTabControl2
            // 
            this.xtraTabControl2.Location = new System.Drawing.Point(19, 77);
            this.xtraTabControl2.LookAndFeel.SkinName = "Office 2010 Silver";
            this.xtraTabControl2.LookAndFeel.UseDefaultLookAndFeel = false;
            this.xtraTabControl2.Name = "xtraTabControl2";
            this.xtraTabControl2.SelectedTabPage = this.xtraTabPage5;
            this.xtraTabControl2.Size = new System.Drawing.Size(405, 205);
            this.xtraTabControl2.TabIndex = 3;
            this.xtraTabControl2.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage5});
            // 
            // xtraTabPage5
            // 
            this.xtraTabPage5.Controls.Add(this.listViewMultiCellColor_Tp);
            this.xtraTabPage5.Name = "xtraTabPage5";
            this.xtraTabPage5.Size = new System.Drawing.Size(397, 175);
            this.xtraTabPage5.Text = "多小区";
            // 
            // listViewMultiCellColor_Tp
            // 
            this.listViewMultiCellColor_Tp.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader10,
            this.columnHeader12});
            this.listViewMultiCellColor_Tp.ContextMenuStrip = this.contextMenuStrip;
            this.listViewMultiCellColor_Tp.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewMultiCellColor_Tp.FullRowSelect = true;
            this.listViewMultiCellColor_Tp.GridLines = true;
            this.listViewMultiCellColor_Tp.Location = new System.Drawing.Point(0, 0);
            this.listViewMultiCellColor_Tp.MultiSelect = false;
            this.listViewMultiCellColor_Tp.Name = "listViewMultiCellColor_Tp";
            this.listViewMultiCellColor_Tp.Size = new System.Drawing.Size(397, 175);
            this.listViewMultiCellColor_Tp.TabIndex = 1;
            this.listViewMultiCellColor_Tp.UseCompatibleStateImageBehavior = false;
            this.listViewMultiCellColor_Tp.View = System.Windows.Forms.View.Details;
            this.listViewMultiCellColor_Tp.DoubleClick += new System.EventHandler(this.listViewMutilCellColor_Tp_DoubleClick);
            // 
            // columnHeader10
            // 
            this.columnHeader10.Text = "所选小区";
            this.columnHeader10.Width = 280;
            // 
            // columnHeader12
            // 
            this.columnHeader12.Text = "颜色";
            this.columnHeader12.Width = 45;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShowCell,
            this.miEditColor});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(149, 48);
            // 
            // miShowCell
            // 
            this.miShowCell.Name = "miShowCell";
            this.miShowCell.Size = new System.Drawing.Size(148, 22);
            this.miShowCell.Text = "地图显示小区";
            this.miShowCell.Click += new System.EventHandler(this.miShowCell_Click);
            // 
            // miEditColor
            // 
            this.miEditColor.Name = "miEditColor";
            this.miEditColor.Size = new System.Drawing.Size(148, 22);
            this.miEditColor.Text = "编辑颜色";
            this.miEditColor.Click += new System.EventHandler(this.miEditColor_Click);
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.xtraTabControl3);
            this.xtraTabPage2.Controls.Add(this.labelControl3);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(427, 281);
            this.xtraTabPage2.Text = "飞线";
            // 
            // xtraTabControl3
            // 
            this.xtraTabControl3.Location = new System.Drawing.Point(19, 43);
            this.xtraTabControl3.LookAndFeel.SkinName = "Office 2010 Silver";
            this.xtraTabControl3.LookAndFeel.UseDefaultLookAndFeel = false;
            this.xtraTabControl3.Name = "xtraTabControl3";
            this.xtraTabControl3.SelectedTabPage = this.xtraTabPage8;
            this.xtraTabControl3.Size = new System.Drawing.Size(407, 224);
            this.xtraTabControl3.TabIndex = 5;
            this.xtraTabControl3.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage8});
            // 
            // xtraTabPage8
            // 
            this.xtraTabPage8.Controls.Add(this.listViewMultiCellColor_Fl);
            this.xtraTabPage8.Name = "xtraTabPage8";
            this.xtraTabPage8.Size = new System.Drawing.Size(399, 194);
            this.xtraTabPage8.Text = "多小区";
            // 
            // listViewMultiCellColor_Fl
            // 
            this.listViewMultiCellColor_Fl.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader20,
            this.columnHeader21});
            this.listViewMultiCellColor_Fl.ContextMenuStrip = this.contextMenuStrip;
            this.listViewMultiCellColor_Fl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewMultiCellColor_Fl.FullRowSelect = true;
            this.listViewMultiCellColor_Fl.GridLines = true;
            this.listViewMultiCellColor_Fl.Location = new System.Drawing.Point(0, 0);
            this.listViewMultiCellColor_Fl.MultiSelect = false;
            this.listViewMultiCellColor_Fl.Name = "listViewMultiCellColor_Fl";
            this.listViewMultiCellColor_Fl.Size = new System.Drawing.Size(399, 194);
            this.listViewMultiCellColor_Fl.TabIndex = 1;
            this.listViewMultiCellColor_Fl.UseCompatibleStateImageBehavior = false;
            this.listViewMultiCellColor_Fl.View = System.Windows.Forms.View.Details;
            this.listViewMultiCellColor_Fl.DoubleClick += new System.EventHandler(this.listViewMutilCellColor_Fl_DoubleClick);
            // 
            // columnHeader20
            // 
            this.columnHeader20.Text = "所选小区";
            this.columnHeader20.Width = 280;
            // 
            // columnHeader21
            // 
            this.columnHeader21.Text = "颜色";
            this.columnHeader21.Width = 42;
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(19, 16);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(84, 14);
            this.labelControl3.TabIndex = 4;
            this.labelControl3.Text = "按占用小区着色";
            this.labelControl3.ToolTip = "飞线颜色";
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(242, 314);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(87, 27);
            this.btnCancel.TabIndex = 2;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(99, 314);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(87, 27);
            this.btnOK.TabIndex = 1;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // CoverageShowForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(439, 346);
            this.Controls.Add(this.xtraTabControl1);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);
            this.Name = "CoverageShowForm";
            this.Text = "小区覆盖带着色设置";
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.xtraTabPage1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditMarkNum.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl2)).EndInit();
            this.xtraTabControl2.ResumeLayout(false);
            this.xtraTabPage5.ResumeLayout(false);
            this.contextMenuStrip.ResumeLayout(false);
            this.xtraTabPage2.ResumeLayout(false);
            this.xtraTabPage2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl3)).EndInit();
            this.xtraTabControl3.ResumeLayout(false);
            this.xtraTabPage8.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl2;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage5;
        private System.Windows.Forms.ListView listViewMultiCellColor_Tp;
        private System.Windows.Forms.ColumnHeader columnHeader10;
        private System.Windows.Forms.ColumnHeader columnHeader12;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl3;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.RadioButton radioBtnTestpoint;
        private System.Windows.Forms.RadioButton radioBtnCell;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miShowCell;
        private System.Windows.Forms.ToolStripMenuItem miEditColor;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage8;
        private System.Windows.Forms.ListView listViewMultiCellColor_Fl;
        private System.Windows.Forms.ColumnHeader columnHeader20;
        private System.Windows.Forms.ColumnHeader columnHeader21;
        private DevExpress.XtraEditors.CheckEdit checkEditMarkNum;
        private DevExpress.XtraEditors.SimpleButton btnExportShapefile;

    }
}