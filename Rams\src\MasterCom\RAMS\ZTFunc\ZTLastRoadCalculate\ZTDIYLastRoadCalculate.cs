﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYLastRoadCalculate : QueryBase
    {          
        public ZTDIYLastRoadCalculate(MainModel mainModel)
            :base(mainModel)
        {
        }      
        public override string Name
        {
            get { return "道路平面里程预统计"; }
        }
        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18015, this.Name);
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true ;
        }

        #region 全局变量
        bool isCalOnlyByFour = false;
        string strCity = "";
        float dCitySpeed = 0;//地市平均速率
        List<LastDist> lastDist_list;
        List<RoadCalculateRoadpoint_all> lastroad_list;
        List<RoadCalculateRoadpoint_all> newroad_list;
        List<RoadCalculateRoadpoint_all> roadpoint_all_list;
        Dictionary<long, long> rejectRoadDic;
        public static List<RoadResult> resultList { get; set; } = new List<RoadResult>();
        Dictionary<string, MapCfg> mapCfgDic = new Dictionary<string, MapCfg>();
        Dictionary<int, List<RoundCfg>> roundCfgDic = new Dictionary<int,List<RoundCfg>>();
        List<string> rejectMapList = new List<string>();
        MapCfg mapCfg = new MapCfg();
        string strSRound { get; set; } = "";
        string strERound { get; set; } = "";
        bool isLTE = false;
        List<RoundCfg> roundCfgList { get; set; } = new List<RoundCfg>();
        List<List<NPOIRow>> nrDatasList { get; set; } = new List<List<NPOIRow>>();
        List<string> sheetNames { get; set; } = new List<string>();
        Dictionary<GridKey, RoadResult> levelOneDic { get; set; } = new Dictionary<GridKey, RoadResult>();//一级道路统计
        Dictionary<GridKey, RoadResult> levelTwoDic { get; set; } = new Dictionary<GridKey, RoadResult>();//二级道路统计
        Dictionary<GridKey, RoadResult> levelThreeDic { get; set; } = new Dictionary<GridKey, RoadResult>();//三级道路统计
        Dictionary<GridKey, RoadResult> levelFourDic { get; set; } = new Dictionary<GridKey, RoadResult>();//四级道路统计
        Dictionary<GridKey, RoadResult> levelFiveDic { get; set; } = new Dictionary<GridKey, RoadResult>();//新增道路统计
        Dictionary<GridKey, RoadResult> levelAllDic { get; set; } = new Dictionary<GridKey, RoadResult>();//全部道路统计
        Dictionary<GridKey, RoadResult> levelTotalDic { get; set; } = new Dictionary<GridKey, RoadResult>();//汇总统计
        #endregion

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void initData()
        {
            lastDist_list = new List<LastDist>();
            lastroad_list = new List<RoadCalculateRoadpoint_all>();
            newroad_list = new List<RoadCalculateRoadpoint_all>();
            roadpoint_all_list = new List<RoadCalculateRoadpoint_all>();
            rejectRoadDic = new Dictionary<long, long>();

            levelOneDic.Clear();
            levelTwoDic.Clear();
            levelThreeDic.Clear();
            levelFourDic.Clear();
            levelFiveDic.Clear();
            levelAllDic.Clear();

            nrDatasList.Clear();
            sheetNames.Clear();
        }
        /// <summary>
        /// 查询条件准备
        /// </summary>
        protected override void query()
        {
            mapCfgDic.Clear();
            roundCfgList.Clear();
            mapCfg = new MapCfg();
            resultList.Clear();

            WaitBox.Show("正在查询图层及轮次表...", getMapSetCfg);
            ZTDIYLastRoadSetTimeForm timeForm = new ZTDIYLastRoadSetTimeForm(mapCfgDic, rejectMapList, roundCfgDic, true);
            if (timeForm.ShowDialog() != DialogResult.OK)
                return;
            isCalOnlyByFour = timeForm.isOnlyCalByFour;
            string strMapCfg = timeForm.MapName;
            if (!mapCfgDic.ContainsKey(strMapCfg))
            {
                MessageBox.Show("所选择的底图有误，请重新选择！", "信息提示", MessageBoxButtons.OK);
                return;                
            }
            mapCfg = mapCfgDic[strMapCfg];
            mapCfg.StrRejectMap = timeForm.rejMapName;
            strSRound = timeForm.StrSRound;
            strERound = timeForm.StrERound;
            foreach (RoundCfg rCfg in roundCfgDic[this.mapCfgDic[strMapCfg].IMapId])
            {
                if (int.Parse(rCfg.StrRound) >= int.Parse(strSRound)
                    && int.Parse(rCfg.StrRound) <= int.Parse(strERound))
                    roundCfgList.Add(rCfg);
            }
            try
            {
                int iCurDistrictID = MainModel.DistrictID;
                if (MainModel.User.DBID == -1) //省用户执行
                {
                    foreach (int DistrictID in sortCity(condition.DistrictIDs))
                    {
                        MainModel.DistrictID = DistrictID;
                        initData();
                        strCity = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                        WaitBox.CanCancel = true;
                        WaitBox.Show("正在获取道路点...", obtainRoadPoint);
                        WaitBox.Show("处理道路平面里程...", dealLastRoad);
                        backcalculate();
                    }
                }
                else
                {
                    initData();
                    strCity = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                    WaitBox.CanCancel = true;
                    WaitBox.Show("正在获取道路点...", obtainRoadPoint);
                    WaitBox.Show("处理道路平面里程...", dealLastRoad);
                    backcalculate();
                }
                MainModel.DistrictID = iCurDistrictID;
            }
            finally
            {
                initData();//清空内存
            }
            outputExcel();//导出EXCEL
            showbackResultForm(true);
        }
        //按广东城市顺序排序
        public static List<int> sortCity(List<int> orgList)
        {
            List<int> cityList = new List<int>();
            //按特定顺序排列查询各是数据(2,3,12,6,5,4,10,13,14,16,7,8,9,11,15,17,18,19,20,21,22)
            int[] zhidingID = { 2, 3, 12, 6, 5, 4, 10, 13, 14, 16, 7, 8, 9, 11, 15, 17, 18, 19, 20, 21, 22 };
            for (int idk = 0; idk < zhidingID.Length; idk++)
            {
                if (orgList.Contains(zhidingID[idk]))
                {
                    cityList.Add(zhidingID[idk]);
                }
            }
            return cityList;
        }
        /// <summary>
        /// 查询地图配置
        /// </summary>
        private void getMapSetCfg()
        {
            WaitBox.Text = "正在查询轮次表...";
            if (roundCfgDic.Count == 0)
            {
                DiySqlRoundSetCfg sqlRoundSetCfg = new DiySqlRoundSetCfg(MainModel);
                sqlRoundSetCfg.SetQueryCondition(condition);
                sqlRoundSetCfg.Query();
                this.roundCfgDic = sqlRoundSetCfg.roundCfgDic;
            }
            WaitBox.ProgressPercent = 30;

            WaitBox.Text = "正在查询图层配置表...";
            if (mapCfgDic.Count == 0)
            {
                DiySqlMapSetCfg sqlMapSetCfg = new DiySqlMapSetCfg(MainModel, false);
                sqlMapSetCfg.SetQueryCondition(condition);
                sqlMapSetCfg.Query();
                this.mapCfgDic = sqlMapSetCfg.mapCfgDic;
            }
            WaitBox.ProgressPercent = 60;

            WaitBox.Text = "正在查询剔除图层表...";
            if (rejectMapList.Count == 0)
            {
                DiySqlRejectMap sqlMapRejCfg = new DiySqlRejectMap(MainModel);
                sqlMapRejCfg.SetQueryCondition(condition);
                sqlMapRejCfg.Query();
                rejectMapList = sqlMapRejCfg.rejectMapList;
            }
            WaitBox.ProgressPercent = 90;

            WaitBox.Close();
        }
        // 显示后台与统计处理窗口
        private void showbackResultForm(bool isMultiCells)  
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LastRoadCalculate).FullName);
            LastRoadCalculate lastbackShowForm = obj == null ? null : obj as LastRoadCalculate;
            if (lastbackShowForm == null || lastbackShowForm.IsDisposed)
            {
                lastbackShowForm = new LastRoadCalculate(MainModel);
            }
            lastbackShowForm.isMultiCells = isMultiCells;
            lastbackShowForm.FillData(nrDatasList,sheetNames);
            lastbackShowForm.Show(MainModel.MainForm);
        }

        //后台预统计处理
        public void backcalculate() 
        {
            foreach (GridKey gKey in levelAllDic.Keys)
            {
                addRoadLevel(gKey);
            }
            foreach (GridKey gKey in levelTotalDic.Keys)
            {
                RoadResult total = levelTotalDic[gKey];
                RoadResult Total = new RoadResult();
                Total.Strcity = total.Strcity;
                Total.StrType = total.StrType;
                Total.StrGridType = total.StrGridType;
                Total.StrGrid = total.StrGrid;
                Total.FRoadDist = (float)Math.Round(total.FRoadDist, 2);
                Total.FSeepDist = (float)Math.Round(total.FSeepDist, 2);
                Total.FNewDist = (float)Math.Round(total.FNewDist, 2);
                Total.FTestDist = (float)Math.Round(total.FTestDist, 2);
                Total.FTestDistSpeed = total.FTestDistSpeed;
                Total.FTestTime = total.FTestTime;
                resultList.Add(Total);
            }
        }

        private void addRoadLevel(GridKey gKey)
        {
            RoadResult levelAll = levelAllDic[gKey];
            RoadResult levelOne = new RoadResult();
            levelOne.Strcity = gKey.StrCity;
            levelOne.StrType = "城市一级道路";
            levelOne.StrGridType = gKey.StrGridType;
            levelOne.StrGrid = gKey.StrGridName;
            RoadResult levelTwo = new RoadResult();
            levelTwo.Strcity = gKey.StrCity;
            levelTwo.StrType = "城市二级道路";
            levelTwo.StrGridType = gKey.StrGridType;
            levelTwo.StrGrid = gKey.StrGridName;
            RoadResult levelThree = new RoadResult();
            levelThree.Strcity = gKey.StrCity;
            levelThree.StrType = "城市三级道路";
            levelThree.StrGridType = gKey.StrGridType;
            levelThree.StrGrid = gKey.StrGridName;
            RoadResult levelFour = new RoadResult();
            levelFour.Strcity = gKey.StrCity;
            levelFour.StrType = "城市四级道路";
            levelFour.StrGridType = gKey.StrGridType;
            levelFour.StrGrid = gKey.StrGridName;
            RoadResult levelFive = new RoadResult();
            if (!isCalOnlyByFour)
            {
                //RoadResult levelFive = new RoadResult();
                levelFive.Strcity = gKey.StrCity;
                levelFive.StrType = "城市五级道路";
                levelFive.StrGridType = gKey.StrGridType;
                levelFive.StrGrid = gKey.StrGridName;
            }

            if (levelOneDic.ContainsKey(gKey))
                levelOne = levelOneDic[gKey];
            if (levelTwoDic.ContainsKey(gKey))
                levelTwo = levelTwoDic[gKey];
            if (levelThreeDic.ContainsKey(gKey))
                levelThree = levelThreeDic[gKey];
            if (levelFourDic.ContainsKey(gKey))
                levelFour = levelFourDic[gKey];
            if (!isCalOnlyByFour && levelFiveDic.ContainsKey(gKey))
            {
                levelFive = levelFiveDic[gKey];
            }

            roadFillData(levelAll, levelOne, levelTwo, levelThree, levelFour, levelFive);
            resultList.Add(levelAll);
            resultList.Add(levelOne);
            resultList.Add(levelTwo);
            resultList.Add(levelThree);
            resultList.Add(levelFour);
            if (!isCalOnlyByFour)
            {
                resultList.Add(levelFive);
            }
        }

        /// <summary>
        /// 报表数据填值
        /// </summary>
        private void roadFillData(RoadResult levelAll, RoadResult levelOne, RoadResult levelTwo,
            RoadResult levelThree, RoadResult levelFour, RoadResult levelFive)
        {
            levelAll.FRoadDist = (float)Math.Round(levelAll.FRoadDist, 2);
            levelOne.FRoadDist = (float)Math.Round(levelOne.FRoadDist, 2);
            levelTwo.FRoadDist = (float)Math.Round(levelTwo.FRoadDist, 2);
            levelThree.FRoadDist = (float)Math.Round(levelThree.FRoadDist, 2);
            levelFour.FRoadDist = (float)Math.Round(levelFour.FRoadDist, 2);
            levelFive.FRoadDist = (float)Math.Round(levelFive.FRoadDist, 2);

            levelAll.FSeepDist = (float)Math.Round(levelAll.FSeepDist, 2);
            levelOne.FSeepDist = (float)Math.Round(levelOne.FSeepDist, 2);
            levelTwo.FSeepDist = (float)Math.Round(levelTwo.FSeepDist, 2);
            levelThree.FSeepDist = (float)Math.Round(levelThree.FSeepDist, 2);
            levelFour.FSeepDist = (float)Math.Round(levelFour.FSeepDist, 2);
            levelFive.FSeepDist = (float)Math.Round(levelFive.FSeepDist, 2);

            levelAll.FNewDist = (float)Math.Round(levelAll.FNewDist, 2);
            levelOne.FNewDist = (float)Math.Round(levelOne.FNewDist, 2);
            levelTwo.FNewDist = (float)Math.Round(levelTwo.FNewDist, 2);
            levelThree.FNewDist = (float)Math.Round(levelThree.FNewDist, 2);
            levelFour.FNewDist = (float)Math.Round(levelFour.FNewDist, 2);
            levelFive.FNewDist = (float)Math.Round(levelFive.FNewDist, 2);

            levelOne.FTestDist = (float)Math.Round(levelOne.FTestDist, 2);
            levelTwo.FTestDist = (float)Math.Round(levelTwo.FTestDist, 2);
            levelThree.FTestDist = (float)Math.Round(levelThree.FTestDist, 2);
            levelFour.FTestDist = (float)Math.Round(levelFour.FTestDist, 2);
            levelFive.FTestDist = (float)Math.Round(levelFive.FTestDist, 2);
            levelAll.FTestDist = (float)Math.Round(levelAll.FTestDist, 2);

            levelOne.FNewDist = (float)Math.Round(levelOne.FNewDist, 2);
            levelTwo.FNewDist = (float)Math.Round(levelTwo.FNewDist, 2);
            levelThree.FNewDist = (float)Math.Round(levelThree.FNewDist, 2);
            levelFour.FNewDist = (float)Math.Round(levelFour.FNewDist, 2);
            levelFive.FNewDist = (float)Math.Round(levelFive.FNewDist, 2);
            levelAll.FNewDist = (float)Math.Round(levelAll.FNewDist, 2);
        }
        /// <summary>
        /// 导出EXCEL
        /// </summary>
        private void outputExcel()
        {
            List<NPOIRow> datas = new List<NPOIRow>();
            List<NPOIRow> statCity = new List<NPOIRow>();
            List<NPOIRow> statPlan = new List<NPOIRow>();
            List<NPOIRow> statA = new List<NPOIRow>();
            List<NPOIRow> statB = new List<NPOIRow>();
            List<NPOIRow> statC = new List<NPOIRow>();
            List<NPOIRow> statLG = new List<NPOIRow>();
            List<NPOIRow> statAG = new List<NPOIRow>();
            List<NPOIRow> statBG = new List<NPOIRow>();
            List<NPOIRow> statCG = new List<NPOIRow>();
            List<NPOIRow> detailA = new List<NPOIRow>();
            List<NPOIRow> detailB = new List<NPOIRow>();
            List<NPOIRow> detailC = new List<NPOIRow>();
            List<NPOIRow> detailALTE = new List<NPOIRow>();
            List<NPOIRow> detailBLTE = new List<NPOIRow>();
            List<NPOIRow> detailCLTE = new List<NPOIRow>();
            List<object> cols = new List<object>();
            NPOIRow nr1 = new NPOIRow();

            List<RoadResult> roadStatCity = getRoadList("全市", true);
            List<RoadResult> roadStatPlan = getRoadList("TD规划区内", true);
            List<RoadResult> roadStatA = getRoadList("A类网格", true);
            List<RoadResult> roadStatB = getRoadList("B类网格", true);
            List<RoadResult> roadStatC = getRoadList("C类网格", true);
            List<RoadResult> roadStatLG = getRoadList("LTE规划区内", true);
            List<RoadResult> roadStatAG = getRoadList("A类网格LTE规划区内", true);
            List<RoadResult> roadStatBG = getRoadList("B类网格LTE规划区内", true);
            List<RoadResult> roadStatCG = getRoadList("C类网格LTE规划区内", true);
            List<RoadResult> roadDetailTmpA = getRoadList("A类网格", false);
            List<RoadResult> roadDetailTmpB = getRoadList("B类网格", false);
            List<RoadResult> roadDetailTmpC = getRoadList("C类网格", false);
            List<RoadResult> roadDetailTmpALTE = getRoadList("A类网格LTE规划区内", false);
            List<RoadResult> roadDetailTmpBLTE = getRoadList("B类网格LTE规划区内", false);
            List<RoadResult> roadDetailTmpCLTE = getRoadList("C类网格LTE规划区内", false);
            List<RoadResult> roadDetailA = updateGridName(roadDetailTmpA, "A");
            List<RoadResult> roadDetailB = updateGridName(roadDetailTmpB, "B");
            List<RoadResult> roadDetailC = updateGridName(roadDetailTmpC, "C");
            List<RoadResult> roadDetailALTE = updateGridName(roadDetailTmpALTE, "A");
            List<RoadResult> roadDetailBLTE = updateGridName(roadDetailTmpBLTE, "B");
            List<RoadResult> roadDetailCLTE = updateGridName(roadDetailTmpCLTE, "C");

            #region EXCEL-SHEET1列表构造
            cols.Add("所属城市");
            cols.Add("道路级别");
            cols.Add("网格类型");
            cols.Add("归属网格");
            cols.Add("道路平面里程");
            cols.Add("渗透平面里程");
            cols.Add("平面里程渗透率");
            cols.Add("测试新增里程");
            cols.Add("实际测试距离");
            cols.Add("道路测试重复率");
            cols.Add("道路溢出率");
            cols.Add("实际测试车速");
            nr1.cellValues = cols;
            datas.Add(nr1);
            statCity.Add(nr1);
            statPlan.Add(nr1);
            statA.Add(nr1);
            statB.Add(nr1);
            statC.Add(nr1);
            statLG.Add(nr1);
            statAG.Add(nr1);
            statBG.Add(nr1);
            statCG.Add(nr1);
            detailA.Add(nr1);
            detailB.Add(nr1);
            detailC.Add(nr1);
            detailALTE.Add(nr1);
            detailBLTE.Add(nr1);
            detailCLTE.Add(nr1);
            #endregion

            fillExcelData(resultList, ref datas);
            fillExcelData(roadStatCity, ref statCity);
            fillExcelData(roadStatPlan, ref statPlan);
            fillExcelData(roadStatA, ref statA);
            fillExcelData(roadStatB, ref statB);
            fillExcelData(roadStatC, ref statC);
            fillExcelData(roadStatLG, ref statLG);
            fillExcelData(roadStatAG, ref statAG);
            fillExcelData(roadStatBG, ref statBG);
            fillExcelData(roadStatCG, ref statCG);
            fillExcelData(roadDetailA, ref detailA);
            fillExcelData(roadDetailB, ref detailB);
            fillExcelData(roadDetailC, ref detailC);
            fillExcelData(roadDetailALTE, ref detailALTE);
            fillExcelData(roadDetailBLTE, ref detailBLTE);
            fillExcelData(roadDetailCLTE, ref detailCLTE);

            nrDatasList.Add(datas);
            nrDatasList.Add(statCity);
            
            int[] iService = {33,34,35,36,37,41,42,43,44 };
            for (int i = 0; i < iService.Length; i++)
            {
                if (condition.ServiceTypes.Contains(iService[i]))
                {
                    isLTE = true;
                }
            }
            if (!isLTE)
                nrDatasList.Add(statPlan);
            nrDatasList.Add(statA);
            nrDatasList.Add(statB);
            nrDatasList.Add(statC);
            if (isLTE)
            {
                nrDatasList.Add(statLG);
                nrDatasList.Add(statAG);
                nrDatasList.Add(statBG);
                nrDatasList.Add(statCG);
            }
            nrDatasList.Add(detailA);
            nrDatasList.Add(detailB);
            nrDatasList.Add(detailC);
            if (isLTE)
            {
                nrDatasList.Add(detailALTE);
                nrDatasList.Add(detailBLTE);
                nrDatasList.Add(detailCLTE);
            }

            sheetNames.Add("测试平面里程统计");
            sheetNames.Add("全网汇总");
            if (!isLTE)
                sheetNames.Add("TD规划区内");
            sheetNames.Add("A类网格汇总");
            sheetNames.Add("B类网格汇总");
            sheetNames.Add("C类网格汇总");
            if (isLTE)
            {
                sheetNames.Add("LTE规划区内");
                sheetNames.Add("A类网格(LTE规划区内)汇总");
                sheetNames.Add("B类网格(LTE规划区内)汇总");
                sheetNames.Add("C类网格(LTE规划区内)汇总");
            }
            sheetNames.Add("A类网格");
            sheetNames.Add("B类网格");
            sheetNames.Add("C类网格");
            if (isLTE)
            {
                sheetNames.Add("A类网格(LTE规划区内)");
                sheetNames.Add("B类网格(LTE规划区内)");
                sheetNames.Add("C类网格(LTE规划区内)");
            }
            WaitBox.Close();
        }
        /// <summary>
        /// 更新网格名称
        /// </summary>
        private List<RoadResult> updateGridName(List<RoadResult> roadDetail, string strGridType)
        {
            List<RoadResult> roadDetailNew = new List<RoadResult>();
            Dictionary<string, List<RoadResult>> tmpDic = new Dictionary<string, List<RoadResult>>();
            foreach (RoadResult rr in roadDetail)
            {
                RoadResult rrNew = new RoadResult();
                rrNew.fillValue(rr);
                setStrGrid(strGridType, rr, rrNew);
                if (tmpDic.ContainsKey(rrNew.Strcity))
                {
                    List<RoadResult> tmpList = tmpDic[rrNew.Strcity];
                    tmpList.Add(rrNew);
                }
                else
                {
                    List<RoadResult> tmpList = new List<RoadResult>();
                    tmpList.Add(rrNew);
                    tmpDic.Add(rrNew.Strcity, tmpList);
                }
            }

            foreach (string city in tmpDic.Keys)
            {
                List<RoadResult> tmpList = tmpDic[city];
                tmpList.Sort(RoadResult.GetCompareByGrid());
                roadDetailNew.AddRange(tmpList);
            }
            return roadDetailNew;
        }

        private void setStrGrid(string strGridType, RoadResult rr, RoadResult rrNew)
        {
            if (strGridType == "C")
            {
                string strTmp = rr.StrGrid.Replace("C", "").Replace("c", "");
                if (strTmp.Length == 1)
                {
                    rrNew.StrGrid = "C0" + strTmp;
                }
            }
            else
            {
                try
                {
                    string[] strPart = rr.StrGrid.Split('-');
                    if (strPart.Length != 3)
                        rrNew.StrGrid = rr.StrGrid;
                    else
                    {
                        if (strGridType == "A")
                            rrNew.StrGrid = "A" + strPart[1].Substring(strPart[1].Length - 2, 2);
                        else
                            rrNew.StrGrid = "B" + strPart[1].Substring(strPart[1].Length - 2, 2);
                    }
                }
                catch
                {
                    rrNew.StrGrid = rr.StrGrid;
                }
            }
        }

        /// <summary>
        /// EXCEL填值
        /// </summary>
        private void fillExcelData(List<RoadResult> tmpList, ref List<NPOIRow> datas)
        {
            foreach (RoadResult rr in tmpList)
            {
                NPOIRow nr2 = new NPOIRow();
                List<object> cols2 = new List<object>();
                cols2.Add(rr.Strcity);
                cols2.Add(rr.StrType);
                cols2.Add(rr.StrGridType);
                cols2.Add(rr.StrGrid);
                cols2.Add(rr.FRoadDist);
                cols2.Add(rr.FSeepDist);
                cols2.Add(rr.StrSeepDist);
                cols2.Add(rr.FNewDist);
                cols2.Add(rr.FTestDist);
                cols2.Add(rr.StrTestRepeatRate);
                cols2.Add(rr.StrTestOverRate);
                cols2.Add(rr.FTestSpeed);
                nr2.cellValues = cols2;
                datas.Add(nr2);
            }
        }
        /// <summary>
        /// 获取各类EXCEL模板数据集
        /// </summary>
        private List<RoadResult> getRoadList(string strGridType,bool isTotal)
        {
            List<RoadResult> sumRoadList = new List<RoadResult>();
            foreach (RoadResult rr in resultList)
            {
                if (rr.StrType != "城市道路汇总" || rr.StrGridType != strGridType)
                    continue;

                if (strGridType == "全市" && rr.StrGrid == "汇总")
                    dCitySpeed = rr.FTestSpeed;
                else
                    rr.FTestTmpSpeed = dCitySpeed;

                if (isTotal && rr.StrGrid == "汇总")
                    sumRoadList.Add(rr);
                else if (!isTotal && rr.StrGrid != "汇总")
                    sumRoadList.Add(rr);
            }
            return sumRoadList;
        }
        /// <summary>
        /// 查询各表数据
        /// </summary>
        private void obtainRoadPoint()
        {
            WaitBox.ProgressPercent = 20;
            lastDist_list = new List<LastDist>();
            lastroad_list = new List<RoadCalculateRoadpoint_all>();
            newroad_list = new List<RoadCalculateRoadpoint_all>();
            roadpoint_all_list = new List<RoadCalculateRoadpoint_all>();
            rejectRoadDic = new Dictionary<long, long>();

            obtainRoadPointByDataBase();
            WaitBox.Close();
        }
        /// <summary>
        /// 通过数据库获取各类表信息
        /// </summary>
        private void obtainRoadPointByDataBase()
        {
            foreach (RoundCfg rCfg in roundCfgList)
            {
                //实际测试里程统计
                WaitBox.Text = "[" + strCity + "]第" + rCfg.StrRound + "轮实际测试里程统计";
                DiySqlGetTestStat sqlRoadpointRange_test = new DiySqlGetTestStat(MainModel);
                sqlRoadpointRange_test.SetQueryCondition(condition);
                sqlRoadpointRange_test.setParam(rCfg, mapCfg);
                sqlRoadpointRange_test.Query();
                List<LastDist> lastDistTmpList = sqlRoadpointRange_test.roadpoint_new_List;
                if (lastDistTmpList != null && lastDistTmpList.Count > 0)
                {
                    lastDist_list.AddRange(lastDistTmpList);
                }
                
                WaitBox.ProgressPercent = 30;

                //平面里程统计
                WaitBox.Text = "[" + strCity + "]第" + rCfg.StrRound + "轮平面里程统计";
                DiySqlGetLastRoad sqlRoadpointRange_new_gather = new DiySqlGetLastRoad(MainModel,false);
                sqlRoadpointRange_new_gather.SetQueryCondition(condition);
                sqlRoadpointRange_new_gather.setParam(rCfg, mapCfg);
                sqlRoadpointRange_new_gather.Query();
                List<RoadCalculateRoadpoint_all> lastRoadTmpList = sqlRoadpointRange_new_gather.roadpoint_new_List;
                if (lastRoadTmpList != null && lastRoadTmpList.Count > 0)
                {
                    lastroad_list.AddRange(lastRoadTmpList);
                }
                
                WaitBox.ProgressPercent = 40;

                //新增里程统计
                WaitBox.Text = "[" + strCity + "]第" + rCfg.StrRound + "轮新增里程统计";
                DiySqlGetNewRoad sqlRoadpointRange_new = new DiySqlGetNewRoad(MainModel,false);
                sqlRoadpointRange_new.SetQueryCondition(condition);
                sqlRoadpointRange_new.setParam(rCfg, mapCfg);
                sqlRoadpointRange_new.Query();
                List<RoadCalculateRoadpoint_all> newRoadTmpList = sqlRoadpointRange_new.roadpoint_new_List;
                if (newRoadTmpList != null && newRoadTmpList.Count > 0)
                {
                    newroad_list.AddRange(newRoadTmpList);
                }
                
                WaitBox.ProgressPercent = 50;
            }

            if (mapCfg.StrRejectMap != "")
            {
                //道路剔除图层采样点
                WaitBox.Text = "[" + strCity + "]道路剔除采样点";
                DiySqlGetRejRoad sqlRejectRoad = new DiySqlGetRejRoad(MainModel);
                sqlRejectRoad.SetQueryCondition(condition);
                sqlRejectRoad.setParam(mapCfg);
                sqlRejectRoad.Query();
                rejectRoadDic = sqlRejectRoad.rejectRoadDic;
                WaitBox.ProgressPercent = 53;
            }

            //道路序列化采样点
            WaitBox.Text = "[" + strCity + "]道路序列化采样点";
            RoadCalculateDiySqlGetRoadpointAllRange sqlRoadpointRange_all = new RoadCalculateDiySqlGetRoadpointAllRange(MainModel);
            sqlRoadpointRange_all.SetQueryCondition(condition);
            sqlRoadpointRange_all.setParam(mapCfg);
            sqlRoadpointRange_all.Query();
            List<RoadCalculateRoadpoint_all> roadPointAllList = sqlRoadpointRange_all.roadpoint_all_List;
            roadpoint_all_list.AddRange(roadPointAllList);

            WaitBox.ProgressPercent = 60;
        }
        /// <summary>
        /// 处理平面道路里程
        /// </summary>
        private void dealLastRoad()
        {
            WaitBox.Text += "[" + strCity + "]";
            statLastRoad();

            statNewRoad();

            foreach (LastDist ld in lastDist_list)
            {
                if (condition.ServiceTypes.Contains(12) && (ld.StrFileName.Contains("1800)")))
                {
                    continue;
                }
                GridKey gKey = new GridKey();
                gKey.StrCity = strCity;
                gKey.StrGridType = ld.StrGridType;
                if (ld.StrGridType.Contains("LTE规划区内(A网格)"))
                    gKey.StrGridType = "A类网格LTE规划区内";
                else if (ld.StrGridType.Contains("LTE规划区内(B网格)"))
                    gKey.StrGridType = "B类网格LTE规划区内";
                else if (ld.StrGridType.Contains("LTE规划区内(C网格)"))
                    gKey.StrGridType = "C类网格LTE规划区内";
                gKey.StrGridName = ld.StrGridName;

                CalcTestDist(gKey, ld);
            }
            //修正实际测试里面--BY TEST
            List<GridKey> gkList = new List<GridKey>();
            foreach (GridKey gKey in levelAllDic.Keys)
            {
                gkList.Add(gKey);
            }
            foreach (GridKey gKey in gkList)
            {
                RoadResult rr = new RoadResult();
                if (levelAllDic.ContainsKey(gKey))
                    rr = levelAllDic[gKey];
                if (!condition.Projects.Contains(50))
                {
                    rr.FTestDist = rr.FTestDist / 2;
                    rr.FTestTime = rr.FTestTime / 2;
                }
                rr.FTestDistSpeed = rr.FTestDist;//将未修改值用于计算速率
                //if (rr.FTestDist < (rr.FSeepDist + rr.FNewDist))
                //{
                //    //rr.FTestDist = (float)(1.01 * (rr.FSeepDist + rr.FNewDist));
                //}
                levelAllDic[gKey] = rr;
            }
            //网格汇总
            combineLevelTotalDic();
            WaitBox.Close();
        }

        private void combineLevelTotalDic()
        {
            levelTotalDic.Clear();
            foreach (GridKey gKey in levelAllDic.Keys)
            {
                GridKey tKey = new GridKey();
                tKey.StrCity = gKey.StrCity;
                tKey.StrGridType = gKey.StrGridType;
                tKey.StrGridName = "汇总";

                RoadResult rr = levelAllDic[gKey];
                if (levelTotalDic.ContainsKey(tKey))
                {
                    RoadResult tmpRR = levelTotalDic[tKey];
                    tmpRR.FRoadDist += rr.FRoadDist;
                    tmpRR.FSeepDist += rr.FSeepDist;
                    tmpRR.FNewDist += rr.FNewDist;
                    tmpRR.FTestDist += rr.FTestDist;
                    tmpRR.FTestDistSpeed += rr.FTestDistSpeed;
                    tmpRR.FTestTime += rr.FTestTime;

                    levelTotalDic[tKey] = tmpRR;
                }
                else
                {
                    RoadResult tmpRR = new RoadResult();
                    tmpRR.Strcity = tKey.StrCity;
                    tmpRR.StrType = rr.StrType;
                    tmpRR.StrGridType = tKey.StrGridType;
                    //tmpRR.StrGrid = tKey.StrGridName;
                    tmpRR.StrGrid = "汇总";

                    tmpRR.FRoadDist = rr.FRoadDist;
                    tmpRR.FSeepDist = rr.FSeepDist;
                    tmpRR.FNewDist = rr.FNewDist;
                    tmpRR.FTestDist = rr.FTestDist;
                    tmpRR.FTestDistSpeed = rr.FTestDistSpeed;
                    tmpRR.FTestTime = rr.FTestTime;

                    levelTotalDic.Add(tKey, tmpRR);
                }
            }
        }

        /// <summary>
        /// 统计总里程及渗透里程
        /// </summary>
        private void statLastRoad()
        {
            Dictionary<RoadKey, RoadCalculateRoadpoint_all> lastRoadDic = new Dictionary<RoadKey, RoadCalculateRoadpoint_all>();
            //渗透道路点
            foreach (RoadCalculateRoadpoint_all rp in lastroad_list)
            {
                RoadKey rk = new RoadKey();
                rk.Isampleid = rp.isampleid;
                rk.Iroadid = rp.iroad;

                //剔除图层
                if (rejectRoadDic.ContainsKey(rk.Isampleid))
                    continue;

                if (!lastRoadDic.ContainsKey(rk))
                    lastRoadDic.Add(rk, rp);
            }

            #region 全部道路点
            dealRoadPoint(lastRoadDic);
            #endregion
        }

        private void dealRoadPoint(Dictionary<RoadKey, RoadCalculateRoadpoint_all> lastRoadDic)
        {
            foreach (RoadCalculateRoadpoint_all rp in roadpoint_all_list)
            {
                if (isCalOnlyByFour && rp.itype == 5)
                {
                    continue;
                }
                RoadKey rk = new RoadKey();
                rk.Isampleid = rp.isampleid;
                rk.Iroadid = rp.iroad;

                //剔除图层
                if (rejectRoadDic.ContainsKey(rk.Isampleid))
                    continue;

                RoadCalculateRoadpoint_all seepRp = new RoadCalculateRoadpoint_all();
                if (lastRoadDic.ContainsKey(rk))
                    seepRp = rp;

                string[] gridPart = rp.strdesc2.Split('|');
                if (gridPart.Length != 3)
                    continue;

                string[] grid2Part = rp.strdesc1.Split('|');
                if (grid2Part.Length != 3)
                    continue;

                string strGridA = gridPart[0];
                string strGridB = gridPart[1];
                string strGridC = gridPart[2];
                string strGridG = grid2Part[0];
                string strGridN = grid2Part[1];
                string strGridL = grid2Part[2];
                string strGridT = rp.strdesc3;

                addLastTd(rp, seepRp, strGridA, strGridB, strGridC, strGridG, strGridN);

                if (strGridL != "")
                    addLastRoadData("LTE" + strGridL, strGridN, rp, seepRp);
                addLastLte(rp, seepRp, strGridA, strGridB, strGridC, strGridL, strGridT);
            }
        }

        private void addLastTd(RoadCalculateRoadpoint_all rp, RoadCalculateRoadpoint_all seepRp, string strGridA, string strGridB, string strGridC, string strGridG, string strGridN)
        {
            if (strGridA != "")
                addLastRoadData("A类网格", strGridA, rp, seepRp);
            if (strGridB != "")
                addLastRoadData("B类网格", strGridB, rp, seepRp);
            if (strGridC != "")
                addLastRoadData("C类网格", strGridC, rp, seepRp);
            if (strGridG != "")
            {
                addLastRoadData("TD" + strGridG, strGridN, rp, seepRp);
                if (!condition.ServiceTypes.Contains(34) && !condition.ServiceTypes.Contains(35))
                    addLastRoadData("全市", strGridG, rp, seepRp);
            }
        }

        private void addLastLte(RoadCalculateRoadpoint_all rp, RoadCalculateRoadpoint_all seepRp, string strGridA, string strGridB, string strGridC, string strGridL, string strGridT)
        {
            if (strGridA != "" && strGridL != "")
                addLastRoadData("A类网格LTE" + strGridL, strGridA, rp, seepRp);
            if (strGridB != "" && strGridL != "")
                addLastRoadData("B类网格LTE" + strGridL, strGridB, rp, seepRp);
            if (strGridC != "" && strGridL != "")
                addLastRoadData("C类网格LTE" + strGridL, strGridC, rp, seepRp);

            if (strGridT != "")
                addLastRoadData("行政区域", strGridT, rp, seepRp);
            //规划区合并
            if (strGridL != "" && (condition.ServiceTypes.Contains(34) || condition.ServiceTypes.Contains(35)))
                addLastRoadData("全市", "LTE" + strGridL, rp, seepRp);
        }

        /// <summary>
        /// 新增里程统计
        /// </summary>
        private void statNewRoad()
        {
            #region 新增道路统计
            //新增道路主键转换
            Dictionary<string, List<RoadCalculateRoadpoint_all>> newroadDic;
            List<RoadCalculateRoadpoint_all> newroad_list2;
            getNewRoad(out newroadDic, out newroad_list2);

            dealNewRoad(newroadDic, newroad_list2);

            foreach (RoadCalculateRoadpoint_all rp in newroad_list2)
            {
                string[] gridPart = rp.strdesc2.Split('|');
                if (gridPart.Length != 3)
                    continue;

                string[] grid2Part = rp.strdesc1.Split('|');
                if (grid2Part.Length != 3)
                    continue;

                string strGridA = gridPart[0];
                string strGridB = gridPart[1];
                string strGridC = gridPart[2];
                string strGridG = grid2Part[0];
                string strGridN = grid2Part[1];
                string strGridL = grid2Part[2];
                string strGridT = rp.strdesc3;

                addTD(rp, strGridA, strGridB, strGridC, strGridG, strGridN);

                addLTE(rp, strGridA, strGridB, strGridC, strGridN, strGridL, strGridT);
            }
            #endregion
        }

        private void getNewRoad(out Dictionary<string, List<RoadCalculateRoadpoint_all>> newroadDic, out List<RoadCalculateRoadpoint_all> newroad_list2)
        {
            newroadDic = new Dictionary<string, List<RoadCalculateRoadpoint_all>>();
            newroad_list2 = new List<RoadCalculateRoadpoint_all>();
            foreach (RoadCalculateRoadpoint_all rp in newroad_list)
            {
                if (newroadDic.ContainsKey(rp.strroad))
                {
                    List<RoadCalculateRoadpoint_all> tmpList = newroadDic[rp.strroad];
                    tmpList.Add(rp);
                    newroadDic[rp.strroad] = tmpList;
                }
                else
                {
                    List<RoadCalculateRoadpoint_all> tmpList = new List<RoadCalculateRoadpoint_all>();
                    tmpList.Add(rp);
                    newroadDic.Add(rp.strroad, tmpList);
                }
            }
        }

        private static void dealNewRoad(Dictionary<string, List<RoadCalculateRoadpoint_all>> newroadDic, List<RoadCalculateRoadpoint_all> newroad_list2)
        {
            //新增道路距离计算
            foreach (string strroad in newroadDic.Keys)
            {
                List<RoadCalculateRoadpoint_all> newRoadList = newroadDic[strroad];
                newRoadList.Sort(RoadCalculateRoadpoint_all.GetCompareBySampleId());

                for (int i = 1; i < newRoadList.Count; i++)
                {
                    double long1 = (double)(newRoadList[i].ilongitude) / 10000000;
                    double lat1 = (double)(newRoadList[i].ilatitude) / 10000000;
                    double long2 = (double)(newRoadList[i - 1].ilongitude) / 10000000;
                    double lat2 = (double)(newRoadList[i - 1].ilatitude) / 10000000;
                    float fTmpDistance = ((float)MathFuncs.GetDistance(long1, lat1, long2, lat2)) / 1000;
                    if (fTmpDistance < 0.1)
                    {
                        newRoadList[i].fdistance = fTmpDistance;
                        newroad_list2.Add(newRoadList[i]);
                    }
                }
            }
        }

        private void addTD(RoadCalculateRoadpoint_all rp, string strGridA, string strGridB, string strGridC, string strGridG, string strGridN)
        {
            if (strGridA != "")
                addNewRoadData("A类网格", strGridA, rp);
            if (strGridB != "")
                addNewRoadData("B类网格", strGridB, rp);
            if (strGridC != "")
                addNewRoadData("C类网格", strGridC, rp);
            if (strGridG != "")
            {
                addNewRoadData("TD" + strGridG, strGridN, rp);
                if (!condition.ServiceTypes.Contains(34))
                    addNewRoadData("全市", strGridG, rp);
            }
        }

        private void addLTE(RoadCalculateRoadpoint_all rp, string strGridA, string strGridB, string strGridC, string strGridN, string strGridL, string strGridT)
        {
            if (strGridL != "")
                addNewRoadData("LTE" + strGridL, strGridN, rp);
            if (strGridA != "" && strGridL != "")
                addNewRoadData("A类网格LTE" + strGridL, strGridA, rp);
            if (strGridB != "" && strGridL != "")
                addNewRoadData("B类网格LTE" + strGridL, strGridB, rp);
            if (strGridC != "" && strGridL != "")
                addNewRoadData("C类网格LTE" + strGridL, strGridC, rp);

            if (strGridT != "")
                addNewRoadData("行政区域", strGridT, rp);
            //规划区合并
            if (strGridL != "" && condition.ServiceTypes.Contains(34))
                addNewRoadData("全市", "LTE" + strGridL, rp);
        }

        /// <summary>
        /// 按维度添加总里程及渗透里程
        /// </summary>
        private void addLastRoadData(string strGridType, string strGridName, RoadCalculateRoadpoint_all rp, RoadCalculateRoadpoint_all seepRp)
        {          
            GridKey gKey = new GridKey();
            gKey.StrCity = strCity;
            gKey.StrGridType = strGridType;
            gKey.StrGridName = strGridName;
            CalcRoadDist(gKey, rp);
            CalcSeepDist(gKey, seepRp);
        }

        /// <summary>
        /// 按维度添加新增里程
        /// </summary>
        private void addNewRoadData(string strGridType, string strGridName, RoadCalculateRoadpoint_all rp)
        {
            GridKey gKey = new GridKey();
            gKey.StrCity = strCity;
            gKey.StrGridType = strGridType;
            gKey.StrGridName = strGridName;
            CalcNewDist(gKey, rp);
        }

        /// <summary>
        /// 计算各级别道路里程
        /// </summary>
        private void CalcRoadDist(GridKey gridKey, RoadCalculateRoadpoint_all rp)
        {
            if (rp.itype == 1)
            {
                addLevelDicData(gridKey, rp, levelOneDic, "城市一级道路");
            }
            else if (rp.itype == 2)
            {
                addLevelDicData(gridKey, rp, levelTwoDic, "城市二级道路");
            }
            else if (rp.itype == 3)
            {
                addLevelDicData(gridKey, rp, levelThreeDic, "城市三级道路");
            }
            else if (rp.itype == 4)
            {
                addLevelDicData(gridKey, rp, levelFourDic, "城市四级道路");
            }
            else if (rp.itype == 5)
            {
                addLevelDicData(gridKey, rp, levelFiveDic, "城市五级道路");
            }

            if (levelAllDic.ContainsKey(gridKey))
            {
                RoadResult rr = levelAllDic[gridKey];
                rr.FRoadDist += rp.fdistance;
            }
            else
            {
                RoadResult rr = createResultValue(gridKey, "城市道路汇总", rp.fdistance);
                levelAllDic.Add(gridKey, rr);
            }
        }

        private void addLevelDicData(GridKey gridKey, RoadCalculateRoadpoint_all rp, Dictionary<GridKey, RoadResult> levelDic, string strType)
        {
            if (levelDic.ContainsKey(gridKey))
            {
                RoadResult rr = levelDic[gridKey];
                rr.FRoadDist += rp.fdistance;
            }
            else
            {
                RoadResult rr = createResultValue(gridKey, strType, rp.fdistance);
                levelDic.Add(gridKey, rr);
            }
        }

        /// <summary>
        /// 道路计算结果赋值
        /// </summary>
        private RoadResult createResultValue(GridKey gridKey, String strType, float fDistance)
        {
            RoadResult rr = new RoadResult();
            rr.StrGridType = gridKey.StrGridType;
            rr.StrGrid = gridKey.StrGridName;
            rr.Strcity = strCity;
            rr.StrType = strType;
            rr.FRoadDist += fDistance;
            return rr;
        }

        /// <summary>
        /// 计算各级别渗透里程
        /// </summary>
        private void CalcSeepDist(GridKey gridKey, RoadCalculateRoadpoint_all rp)
        {
            if (rp == null)
                return;

            if (rp.itype == 1)
            {
                addSeepDist(gridKey, rp, levelOneDic);
            }
            else if (rp.itype == 2)
            {
                addSeepDist(gridKey, rp, levelTwoDic);
            }
            else if (rp.itype == 3)
            {
                addSeepDist(gridKey, rp, levelThreeDic);
            }
            else if (rp.itype == 4)
            {
                addSeepDist(gridKey, rp, levelFourDic);
            }
            else if (rp.itype == 5)
            {
                addSeepDist(gridKey, rp, levelFiveDic);
            }

            if (levelAllDic.ContainsKey(gridKey))
            {
                RoadResult rr = levelAllDic[gridKey];
                rr.FSeepDist += rp.fdistance;
            }
        }

        private void addSeepDist(GridKey gridKey, RoadCalculateRoadpoint_all rp, Dictionary<GridKey, RoadResult> levelDic)
        {
            if (levelDic.ContainsKey(gridKey))
            {
                RoadResult rr = levelDic[gridKey];
                rr.FSeepDist += rp.fdistance;
            }
        }

        /// <summary>
        /// 计算实际测试里程
        /// </summary>
        private void CalcTestDist(GridKey gridKey, LastDist ld)
        {
            if (ld == null)
                return;

            if (levelAllDic.ContainsKey(gridKey))
            {
                RoadResult rr = levelAllDic[gridKey];
                rr.FTestDist += ld.FDist;
                rr.FTestTime += ld.FTime;
            }
            else
            {
                RoadResult rr = new RoadResult();
                rr.StrGridType = gridKey.StrGridType;
                rr.StrGrid = gridKey.StrGridName;
                rr.Strcity = strCity;
                rr.StrType = "城市道路汇总";

                rr.FTestDist = ld.FDist;
                rr.FTestTime = ld.FTime;
                levelAllDic.Add(gridKey, rr);
            }
        }

        /// <summary>
        /// 计算新增里程
        /// </summary>
        private void CalcNewDist(GridKey gridKey, RoadCalculateRoadpoint_all rp)
        {
            if (rp == null)
                return;

            if (levelAllDic.ContainsKey(gridKey))
            {
                RoadResult rr = levelAllDic[gridKey];
                rr.FNewDist += rp.fdistance;
            }
            else
            {
                RoadResult rr = new RoadResult();
                rr.StrGridType = gridKey.StrGridType;
                rr.StrGrid = gridKey.StrGridName;
                rr.Strcity = strCity;
                rr.StrType = "城市道路汇总";

                rr.FNewDist = rp.fdistance;
                levelAllDic.Add(gridKey, rr);
            }
        }
    }

    public class MapCfg
    {
        /// <summary>
        /// 图层序号ID
        /// </summary>
        public int IMapId { get; set; }
        /// <summary>
        /// 图层名称
        /// </summary>
        public string StrName { get; set; }
        /// <summary>
        /// 道路底图统计表
        /// </summary>
        public string StrGridRoad { get; set; }
        /// <summary>
        /// 平面里程统计表
        /// </summary>
        public string StrLastRoad { get; set; }
        /// <summary>
        /// 新增平面里程采样点表
        /// </summary>
        public string StrNewRoad { get; set; }
        /// <summary>
        /// 新增平面里程统计表
        /// </summary>
        public string StrLastNewRoad { get; set; }
        /// <summary>
        /// 文件里程统计表
        /// </summary>
        public string StrLastDist { get; set; }
        /// <summary>
        /// 自定义维度统计表
        /// </summary>
        public string StrAreaDist { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string StrComment { get; set; }
        /// <summary>
        /// 剔除图层
        /// </summary>
        public string StrRejectMap { get; set; }
    }

    public class RoadResult
    {
        /// <summary>
        /// 所属城市
        /// </summary>
        public string Strcity { get; set; }
        /// <summary>
        /// 道路类型
        /// </summary>
        public string StrType { get; set; }
        /// <summary>
        /// 网格类型
        /// </summary>
        public string StrGridType { get; set; }
        /// <summary>
        /// 归属网格
        /// </summary>
        public string StrGrid { get; set; }
        /// <summary>
        /// 实际测试距离
        /// </summary>
        public float FTestDist { get; set; }
        /// <summary>
        /// 实际测试距离(用于计算车速)
        /// </summary>
        public float FTestDistSpeed { get; set; }
        /// <summary>
        /// 单程测试距离
        /// </summary>
        public float FOneTestDist
        {
            get 
            {
                return FTestDist / 2; 
            }
        }
        /// <summary>
        /// 道路测试重复率
        /// </summary>
        public string StrTestRepeatRate
        {
            get
            {
                if (FSeepDist + FNewDist != 0 && FTestDist - (FSeepDist + FNewDist) > 0 && StrType == "城市道路汇总")
                    return Math.Round(100 * (FTestDist - (FSeepDist + FNewDist)) / (FSeepDist + FNewDist), 2).ToString("0.00") + "%";
                else
                    return "0.00%";
            }
        }
        /// <summary>
        /// 道路测试重复率(干道使用)
        /// </summary>
        public string StrMainRoadTRRate
        {
            get
            {
                if (FSeepDist + FNewDist != 0 && FTestDist - (FSeepDist + FNewDist) > 0)
                    return Math.Round(100 * (FTestDist - (FSeepDist + FNewDist)) / (FSeepDist + FNewDist), 2).ToString("0.00") + "%";
                else
                    return "0.00%";
            }
        }
        /// <summary>
        /// 道路溢出率
        /// </summary>
        public string StrTestOverRate
        {
            get
            {
                if (FRoadDist != 0)
                    return Math.Round(100 * FNewDist / FRoadDist,2).ToString("0.00") + "%";
                else
                    return "0.00%";
            }
        }
        
        /// <summary>
        /// 实际测试时长
        /// </summary>
        public float FTestTime { get; set; }
        /// <summary>
        /// 地市平均车速(当无数据时,用地市平均车速)
        /// </summary>
        public float FTestTmpSpeed { get; set; }

        /// <summary>
        /// 实际测试车速
        /// </summary>
        public float FTestSpeed
        {
            get
            {
                float fTestSpeed = 0;
                if (FTestTime != 0)
                {
                    fTestSpeed = (float)Math.Round(FTestDistSpeed / FTestTime, 2);
                }
                else if (FTestDist != 0)
                {
                    fTestSpeed = FTestTmpSpeed;
                }
                return fTestSpeed; 
            }
        }
        
        /// <summary>
        /// 实际道路里程
        /// </summary>
        public float FRoadDist { get; set; }
        /// <summary>
        /// 测试渗透距离
        /// </summary>
        public float FSeepDist { get; set; }
        /// <summary>
        /// 测试新增里程
        /// </summary>
        public float FNewDist { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string StrComment { get; set; }

        /// <summary>
        /// 中间量
        /// </summary>
        public string StrSeepDist
        {
            get
            {
                string strSeepDist;
                if (FRoadDist != 0)
                    strSeepDist = Math.Round((100 * FSeepDist / FRoadDist), 2).ToString() + "%";
                else
                    strSeepDist = "----";
                return strSeepDist;
            }
        }

        public RoadResult()
        {
            this.Strcity = "";
            this.StrType = "";
            this.StrGridType = "";
            this.StrGrid = "";
            this.FTestDist = 0;
            this.FTestDistSpeed = 0;
            this.FRoadDist = 0;
            this.FSeepDist = 0;
            this.FNewDist = 0;
            this.StrComment = "";
            this.FTestTime = 0;
            this.FTestTmpSpeed = 0;
        }

        /// <summary>
        /// 新赋值
        /// </summary>
        public void fillValue(RoadResult orgRr)
        {
            this.Strcity = orgRr.Strcity;
            this.StrType = orgRr.StrType;
            this.StrGridType = orgRr.StrGridType;
            this.StrGrid = orgRr.StrGrid;
            this.FTestDist = orgRr.FTestDist;
            this.FTestDistSpeed = orgRr.FTestDistSpeed;
            this.FTestTime = orgRr.FTestTime;
            this.FRoadDist = orgRr.FRoadDist;
            this.FSeepDist = orgRr.FSeepDist;
            this.FNewDist = orgRr.FNewDist;
            this.StrComment = orgRr.StrComment;
            this.FTestTmpSpeed = orgRr.FTestTmpSpeed;
        }

        //实现排序的接口
        public static IComparer<RoadResult> GetCompareByGrid()
        {
            if (comparerByGrid == null)
            {
                comparerByGrid = new CompareByGrid();
            }
            return comparerByGrid;
        }
        public class CompareByGrid : IComparer<RoadResult>
        {
            public int Compare(RoadResult x, RoadResult y)
            {
                return x.StrGrid.CompareTo(y.StrGrid);
            }
        }
        private static IComparer<RoadResult> comparerByGrid;
    }

    public class RoadCalculateRoadpoint
    {
        public string strroad { get; set; }
        public int itype { get; set; }
        public long isampleid { get; set; }
        public float fdistance { get; set; }
        public int ilongitude { get; set; }
        public int ilatitude { get; set; }
        public int iroad { get; set; }
        public int iareatype { get; set; }
        public int iareaid { get; set; }
        public int idir { get; set; }

        public RoadCalculateRoadpoint()
        {
            strroad = "";
            itype = 0;
            isampleid = 0;
            fdistance = 0;
            ilongitude = 0;
            ilatitude = 0;
            iroad = 0;
            iareatype = 0;
            iareaid = 0;
            idir = 0;
        }
    }

    public class RoadCalculateRoadpoint_all : RoadCalculateRoadpoint
    {
        public string strdesc1 { get; set; }
        public string strdesc2 { get; set; }
        public string strdesc3 { get; set; }
        public string strMainRoadName { get; set; }
        public string strdesc { get; set; }

        public RoadCalculateRoadpoint_all()
        {
            strdesc1 = "";
            strdesc2 = "";
            strdesc3 = "";
            strMainRoadName = "";
            strdesc = "";
        }

        //实现排序的接口
        public static IComparer<RoadCalculateRoadpoint_all> GetCompareBySampleId()
        {
            if (comparerBySampleId == null)
            {
                comparerBySampleId = new CompareBySampleId();
            }
            return comparerBySampleId;
        }
        public class CompareBySampleId : IComparer<RoadCalculateRoadpoint_all>
        {
            public int Compare(RoadCalculateRoadpoint_all x, RoadCalculateRoadpoint_all y)
            {
                return x.isampleid.CompareTo(y.isampleid);
            }
        }
        private static IComparer<RoadCalculateRoadpoint_all> comparerBySampleId;
    }

    public class RoadKey
    {
        /// <summary>
        /// 采样点ID
        /// </summary>
        public long Isampleid { get; set; }
        /// <summary>
        /// 道路ID
        /// </summary>
        public int Iroadid { get; set; }

        public override bool Equals(object obj)
        {
            RoadKey other = obj as RoadKey;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.Isampleid.Equals(other.Isampleid) &&
                    this.Iroadid.Equals(other.Iroadid));
        }

        public override int GetHashCode()
        {
            return this.Isampleid.GetHashCode();
        }
    }

    public class GridKey
    {
        /// <summary>
        /// 所属城市
        /// </summary>
        public string StrCity { get; set; }
        /// <summary>
        /// 网格类型
        /// </summary>
        public string StrGridType { get; set; }
        /// <summary>
        /// 网格名称
        /// </summary>
        public string StrGridName { get; set; }

        public override bool Equals(object obj)
        {
            GridKey other = obj as GridKey;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.StrGridType.Equals(other.StrGridType) &&
                    this.StrCity.Equals(other.StrCity) &&
                    this.StrGridName.Equals(other.StrGridName));
        }

        public override int GetHashCode()
        {
            return (this.StrGridType
                 + this.StrCity
                 + this.StrGridName).GetHashCode();
        }
    }

    public class LastDist
    {
        /// <summary>
        /// 文件对应的高速
        /// </summary>
        public string StrFileName { get; set; }
        /// <summary>
        /// 网格类别
        /// </summary>
        public string StrGridType { get; set; }
        /// <summary>
        /// 网格名称
        /// </summary>
        public string StrGridName { get; set; }
        /// <summary>
        /// 测试里程
        /// </summary>
        public float FDist { get; set; }
        /// <summary>
        /// 测试时长
        /// </summary>
        public float FTime { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string StrComment { get; set; }
    }

    public class RoundCfg
    {
        /// <summary>
        /// 轮次ID
        /// </summary>
        public int Iid { get; set; }
        /// <summary>
        /// 地图ID
        /// </summary>
        public int IMapId { get; set; }
        /// <summary>
        /// 年份
        /// </summary>
        public int IYear { get; set; }
        /// <summary>
        /// 轮次
        /// </summary>
        public int IRound { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public int IStime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public int IEtiem { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string StrComment { get; set; }

        public string StrRound
        {
            get
            {
                string strYear = (IYear - 2000).ToString();
                string strMon = IRound.ToString();
                if (strMon.Length == 1)
                {
                    strMon = "0" + strMon;
                }
                return strYear + strMon;
            }
        }

        public List<string> FileNameList
        {
            get
            {
                List<string> fileList = new List<string>();
                if (IStime != 0)
                {
                    DateTime sTime = JavaDate.GetDateTimeFromMilliseconds(IStime * 1000L);
                    DateTime eTime = JavaDate.GetDateTimeFromMilliseconds(IEtiem * 1000L);
                    DateTime sTimeTem = sTime;
                    while (sTimeTem <= eTime)
                    {
                        string strMon = string.Format("{0:yyMM}", sTimeTem);
                        string roundMon = "tb_auto_filelist_20" + strMon.Substring(0, 2) + "_" + strMon.Substring(2,2);
                        if (!fileList.Contains(roundMon))
                            fileList.Add(roundMon);
                        sTimeTem = sTimeTem.AddDays(1);

                    }
                }
                return fileList;
            }
        }

        public RoundCfg()
        {
            this.Iid = 0;
            this.IMapId = 0;
            this.IYear = 0;
            this.IRound = 0;
            this.IStime = 0;
            this.IEtiem = 0;
            this.StrComment = "";
        }

        public override bool Equals(object obj)
        {
            RoundCfg other = obj as RoundCfg;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.IMapId.Equals(other.IMapId)
                && this.IYear.Equals(other.IYear)
                && this.IRound.Equals(other.IRound));
        }

        public override int GetHashCode()
        {
            return this.IRound.GetHashCode();
        }
    }

    public class DiySqlMapSetCfg : DIYSQLBase
    {
        public DiySqlMapSetCfg(MainModel mainModel, bool isMainRoad)
            : base(mainModel)
        {
            this.isMainRoad = isMainRoad;
        }
        private bool isMainRoad { get; set; } = false;
        protected override string getSqlTextString()
        {
            string strIsHigh = "";
            string strAndOr = " or ";
            if (!isMainRoad)
            {
                strIsHigh = "not";
                strAndOr = " and ";
            }
            string strSql = "select imapid,strname,gridroad,lastroad,newroad,lastnewroad,lastdist,areadist,strcomment " +
                            "from tb_auto_cfg_mapsetting where lastroad " + strIsHigh + " like '%lastmainrd%' " + strAndOr + " strname " + strIsHigh + " like '%国道%'";
            return strSql;
        }

        public override string Name
        {
            get { return "DiySqlMapSetCfg"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[9];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_String;
            rType[7] = E_VType.E_String;
            rType[8] = E_VType.E_String;
            return rType;
        }

        public Dictionary<string,MapCfg> mapCfgDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            mapCfgDic = new Dictionary<string, MapCfg>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    MapCfg mapCfg = new MapCfg();
                    mapCfg.IMapId = package.Content.GetParamInt();
                    mapCfg.StrName = package.Content.GetParamString();
                    mapCfg.StrGridRoad = package.Content.GetParamString();
                    mapCfg.StrLastRoad = package.Content.GetParamString();

                    mapCfg.StrNewRoad = package.Content.GetParamString();
                    mapCfg.StrLastNewRoad = package.Content.GetParamString();
                    mapCfg.StrLastDist = package.Content.GetParamString();
                    mapCfg.StrAreaDist = package.Content.GetParamString();
                    mapCfg.StrComment = package.Content.GetParamString();

                    if (!mapCfgDic.ContainsKey(mapCfg.StrName))
                        mapCfgDic.Add(mapCfg.StrName,mapCfg);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DiySqlRoundSetCfg : DIYSQLBase
    {
        public DiySqlRoundSetCfg(MainModel mainModel)
            : base(mainModel)
        {
        }
        protected override string getSqlTextString()
        {
            string strSql = "select iid,imapid,iyear,iround,istime,ietime,strcomment from tb_auto_cfg_round where strprojs = '"  + condition.Projects[0]
                 + "' and strservs like '%" + condition.ServiceTypes[0] + "%'  order by iid";
            return strSql;
        }

        public override string Name
        {
            get { return "DiySqlRoundSetCfg"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[7];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_String;
            return rType;
        }

        public Dictionary<int, List<RoundCfg>> roundCfgDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            roundCfgDic = new Dictionary<int, List<RoundCfg>>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    RoundCfg rCfg = new RoundCfg();
                    rCfg.Iid = package.Content.GetParamInt();
                    rCfg.IMapId = package.Content.GetParamInt();
                    rCfg.IYear = package.Content.GetParamInt();
                    rCfg.IRound = package.Content.GetParamInt();
                    rCfg.IStime = package.Content.GetParamInt();
                    rCfg.IEtiem = package.Content.GetParamInt();
                    rCfg.StrComment = package.Content.GetParamString();

                    if (!roundCfgDic.ContainsKey(rCfg.IMapId))
                    {
                        List<RoundCfg> rCfgList = new List<RoundCfg>();
                        rCfgList.Add(rCfg);
                        roundCfgDic.Add(rCfg.IMapId, rCfgList);
                    }
                    else
                    {
                        roundCfgDic[rCfg.IMapId].Add(rCfg);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DiySqlRejectMap : DIYSQLBase
    {
        public DiySqlRejectMap(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override string getSqlTextString()
        {
            string strSql = "select distinct strname from tb_auto_cfg_mapreject";
            return strSql;
        }

        public override string Name
        {
            get { return "DiySqlRejectMap"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_String;
            return rType;
        }

        public List<string> rejectMapList { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            rejectMapList = new List<string>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    rejectMapList.Add(package.Content.GetParamString());
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DiySqlGetRejRoad : DIYSQLBase
    {
        /// <summary>
        /// SQL查询语句
        /// </summary>
        public static string strSQL { get; set; } = "";

        /// <summary>
        /// SQL查询语句构造
        /// </summary>
        public void setParam(MapCfg mapCfg)
        {
            strSQL = "select distinct isampleid from tb_auto_cfg_mapreject where strname = '" + mapCfg.StrRejectMap + "' and imapid = " + mapCfg.IMapId;
        }

        public DiySqlGetRejRoad(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override string getSqlTextString()
        {
            return strSQL;
        }

        public override string Name
        {
            get { return "DiySqlGetRejRoad"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int64;
            return rType;
        }

        public Dictionary<long, long> rejectRoadDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            rejectRoadDic = new Dictionary<long, long>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    long iSampleid = package.Content.GetParamInt64();
                    rejectRoadDic.Add(iSampleid, 0);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DiySqlGetTestStat : DIYSQLBase
    {
        private string strSQL = "";
        /// <summary>
        /// 存储过程参数
        /// </summary>
        /// <param name="timeStr">时间</param>
        public void setParam(RoundCfg rCfg, MapCfg mapCfg)
        {
            StringBuilder projsStr = new StringBuilder();
            StringBuilder servsStr = new StringBuilder();
            string strMainRoad = getMainRoad(mapCfg);

            addProjsStr(projsStr);

            addServsStr(servsStr);

            string strTbName = mapCfg.StrLastDist.Replace("{0:yyMM}", "") + rCfg.StrRound;
            StringBuilder sql = new StringBuilder(strSQL);
            foreach (string strDB in rCfg.FileNameList)
            {
                if (sql.Length == 0)
                {
                    sql.Append("select strgridtype,strgridname,sum(convert(float,idist)/1000) as fDist,sum(convert(float,itime)/(1000*3600)) as fTime ,a.strfilename " + " from " + strDB + " as a join " + strTbName + " as b on a.ifileid = b.ifileid where iprojecttype in " + projsStr.ToString() + "  and iservicetype in " + servsStr.ToString() + strMainRoad + " group by strgridtype,strgridname,strfilename");
                }
                else
                {
                    sql.Append(" union all " + "select strgridtype,strgridname,sum(convert(float,idist)/1000) as fDist,sum(convert(float,itime)/(1000*3600)) as fTime ,a.strfilename " + " from " + strDB + " as a join " + strTbName + " as b on a.ifileid = b.ifileid where iprojecttype in " + projsStr.ToString() + "  and iservicetype in " + servsStr.ToString() + strMainRoad + " group by strgridtype,strgridname,strfilename");
                }
            }
            strSQL = sql.ToString();
        }

        private string getMainRoad(MapCfg mapCfg)
        {
            string strMainRoad = "";
            if (mapCfg.StrName.Contains("高速"))
            {
                strMainRoad = "  and strfilename like '%高速%' ";
            }
            else if (mapCfg.StrName.Contains("国道"))
            {
                strMainRoad = "  and strfilename like '%国道%' ";
            }

            if (Condition.Projects.Contains(50))
            {
                strMainRoad = "  and iareatype = 1 ";
                if (mapCfg.StrName.Contains("高速"))
                {
                    strMainRoad = "  and iareatype = 2 ";
                }
                else if (mapCfg.StrName.Contains("国道"))
                {
                    strMainRoad = "  and iareatype = 3 ";
                }
            }

            return strMainRoad;
        }

        private void addProjsStr(StringBuilder projsStr)
        {
            for (int i = 0; i < Condition.Projects.Count; i++)
            {
                if (i == 0)
                    projsStr.Append(Condition.Projects[i].ToString());
                else
                {
                    projsStr.Append("," + Condition.Projects[i]);
                }
            }
            projsStr.Insert(0, "(");
            projsStr.Append(")");
        }

        private void addServsStr(StringBuilder servsStr)
        {
            for (int i = 0; i < Condition.ServiceTypes.Count; i++)
            {
                if (i == 0)
                    servsStr.Append(Condition.ServiceTypes[i].ToString());
                else
                {
                    servsStr.Append("," + Condition.ServiceTypes[i]);
                }
            }
            servsStr.Insert(0, "(");
            servsStr.Append(")");
        }

        public DiySqlGetTestStat(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override string getSqlTextString()
        {
            return strSQL;
        }
        public override string Name
        {
            get { return "DiySqlGetLastRoad"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[5];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_Float;
            rType[3] = E_VType.E_Float;
            rType[4] = E_VType.E_String;
            return rType;
        }

        public List<LastDist> roadpoint_new_List { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            roadpoint_new_List = new List<LastDist>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    LastDist rp = new LastDist();
                    rp.StrGridType = package.Content.GetParamString();
                    rp.StrGridName = package.Content.GetParamString();
                    rp.FDist = package.Content.GetParamFloat();
                    rp.FTime = package.Content.GetParamFloat();
                    rp.StrFileName = package.Content.GetParamString();
                    if (rp.StrGridType.Equals("LTE规划区内") || rp.StrGridType.Equals("LTE规划区外"))
                    {
                        LastDist rp2 = new LastDist();
                        rp2.StrGridType = "全市";
                        rp2.StrGridName = rp.StrGridType;
                        rp2.FDist = rp.FDist;
                        rp2.FTime = rp.FTime;
                        rp2.StrFileName = rp.StrFileName;
                        roadpoint_new_List.Add(rp2);
                    }
                    roadpoint_new_List.Add(rp);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DiySqlGetLastRoad : DIYSQLBase
    {
        /// <summary>
        /// SQL查询语句
        /// </summary>
        public string strSQL { get; set; } = "";

        /// <summary>
        /// 存储过程参数
        /// </summary>
        /// <param name="timeStr">时间</param>
        public void setParam(RoundCfg rCfg, MapCfg mapCfg)
        {
            StringBuilder projsStr = new StringBuilder();
            StringBuilder servsStr = new StringBuilder();
            string strMainRoad = "";
            if (mapCfg.StrName.Contains("高速"))
            {
                strMainRoad = "  and iareatype = 2 ";
            }
            else if (mapCfg.StrName.Contains("国道"))
            {
                strMainRoad = "  and iareatype = 3 ";
            }
            for (int i = 0; i < Condition.Projects.Count; i++)
            {
                if (i == 0)
                    projsStr.Append(Condition.Projects[i].ToString());
                else
                {
                    projsStr.Append("," + Condition.Projects[i]);
                }
            }
            projsStr.Insert(0, "(");
            projsStr.Append(")");

            for (int i = 0; i < Condition.ServiceTypes.Count; i++)
            {
                if (i == 0)
                    servsStr.Append(Condition.ServiceTypes[i].ToString());
                else
                {
                    servsStr.Append("," + Condition.ServiceTypes[i]);
                }
            }
            servsStr.Insert(0, "(");
            servsStr.Append(")");
            string strIsHigh = " from ";
            if (isMainRoad)
                strIsHigh = ",iareatype,iareaid,idir from ";
            string strTbName = mapCfg.StrLastRoad.Replace("{0:yyMM}", "") + rCfg.StrRound;
            strSQL = "select distinct isampleid,iroadid,ilongitude,ilatitude " + strIsHigh + strTbName +
                     " where iprojecttype in " + projsStr.ToString() + " and iservicetype in " + servsStr.ToString() + strMainRoad;
        }

        public DiySqlGetLastRoad(MainModel mainModel, bool isMainRoad)
            : base(mainModel)
        {
            this.isMainRoad = isMainRoad;
        }
        private bool isMainRoad { get; set; } = false;
        protected override string getSqlTextString()
        {
            return strSQL;
        }
        public override string Name
        {
            get { return "DiySqlGetLastRoad"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int isH = 4;
            if (isMainRoad)
                isH = 7;
            E_VType[] rType = new E_VType[isH];
            rType[0] = E_VType.E_Int64;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            if (isMainRoad)
            {
                rType[4] = E_VType.E_Int;
                rType[5] = E_VType.E_Int;
                rType[6] = E_VType.E_Int;
            }
            return rType;
        }

        public List<RoadCalculateRoadpoint_all> roadpoint_new_List { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            roadpoint_new_List = new List<RoadCalculateRoadpoint_all>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    RoadCalculateRoadpoint_all rp = new RoadCalculateRoadpoint_all();
                    rp.isampleid = package.Content.GetParamInt64();
                    rp.iroad = package.Content.GetParamInt();
                    rp.ilongitude = package.Content.GetParamInt();
                    rp.ilatitude = package.Content.GetParamInt();
                    if (isMainRoad)
                    {
                        rp.iareatype = package.Content.GetParamInt();
                        rp.iareaid = package.Content.GetParamInt();
                        rp.idir = package.Content.GetParamInt();
                    }
                    roadpoint_new_List.Add(rp);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class DiySqlGetNewRoad : DIYSQLBase
    {
        /// <summary>
        /// SQL查询语句
        /// </summary>
        public string strSQL { get; set; } = "";

        /// <summary>
        /// 存储过程参数
        /// </summary>
        /// <param name="timeStr">时间</param>
        public void setParam(RoundCfg rCfg, MapCfg mapCfg)
        {
            StringBuilder projsStr = new StringBuilder();
            StringBuilder servsStr = new StringBuilder();
            string strMainRoad = "";
            if (mapCfg.StrName.Contains("高速"))
            {
                strMainRoad = "  and iareatype = 2 ";
            }
            else if (mapCfg.StrName.Contains("国道"))
            {
                strMainRoad = "  and iareatype = 3 ";
            }
            
            for (int i = 0; i < Condition.Projects.Count; i++)
            {
                if (i == 0)
                    projsStr.Append(Condition.Projects[i].ToString());
                else
                {
                    projsStr.Append("," + Condition.Projects[i]);
                }
            }
            projsStr.Insert(0, "(");
            projsStr.Append(")");

            for (int i = 0; i < Condition.ServiceTypes.Count; i++)
            {
                if (i == 0)
                    servsStr.Append(Condition.ServiceTypes[i].ToString());
                else
                {
                    servsStr.Append("," + Condition.ServiceTypes[i]);
                }
            }
            servsStr.Insert(0, "(");
            servsStr.Append(")");

            string strIsHigh = " from ";
            if (isMainRoad)
                strIsHigh = ",iareatype,iareaid,idir from ";
            string strTbName = mapCfg.StrLastNewRoad.Replace("{0:yyMM}", "") + rCfg.StrRound;

            strSQL = "select distinct isampleid,iroadid,ilongitude,ilatitude,strdesc1,strdesc2,strdesc3 " + strIsHigh + strTbName +
                     " where iroadid<0 and iprojecttype in " + projsStr.ToString() + " and iservicetype in " + servsStr.ToString() + strMainRoad + " order by iroadid,isampleid desc";
        }

        public DiySqlGetNewRoad(MainModel mainModel, bool isMainRoad)
            : base(mainModel)
        {
            this.isMainRoad = isMainRoad;
        }
        private bool isMainRoad { get; set; } = false;
        protected override string getSqlTextString()
        {
            return strSQL;
        }
        public override string Name
        {
            get { return "DiySqlGetNewRoad"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int isH = 7;
            if (isMainRoad)
                isH = 10;
            E_VType[] rType = new E_VType[isH];
            rType[0] = E_VType.E_Int64;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_String;
            if (isMainRoad)
            {
                rType[7] = E_VType.E_Int;
                rType[8] = E_VType.E_Int;
                rType[9] = E_VType.E_Int;
            }
            return rType;
        }

        public List<RoadCalculateRoadpoint_all> roadpoint_new_List { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            roadpoint_new_List = new List<RoadCalculateRoadpoint_all>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    RoadCalculateRoadpoint_all rp = new RoadCalculateRoadpoint_all();
                    rp.isampleid = package.Content.GetParamInt64();
                    rp.strroad = package.Content.GetParamInt().ToString();
                    rp.ilongitude = package.Content.GetParamInt();
                    rp.ilatitude = package.Content.GetParamInt();
                    rp.strdesc1 = package.Content.GetParamString();
                    rp.strdesc2 = package.Content.GetParamString();
                    rp.strdesc3 = package.Content.GetParamString();
                    if (isMainRoad)
                    {
                        rp.iareatype = package.Content.GetParamInt();
                        rp.iareaid = package.Content.GetParamInt();
                        rp.idir = package.Content.GetParamInt();
                    }
                    roadpoint_new_List.Add(rp);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class RoadCalculateDiySqlGetRoadpointAllRange : DIYSQLBase
    {

        public RoadCalculateDiySqlGetRoadpointAllRange(MainModel mainModel)
            : base(mainModel)
        {
        }

        string strSql = "";

        /// <summary>
        /// 存储过程参数
        /// </summary>
        public void setParam(MapCfg mapCfg)
        {
            string strTbName = mapCfg.StrGridRoad;
            strSql = "select strroad,itype,fdistance*1000 as fdistance,isampleid,ilongitude,ilatitude,strdesc1,strdesc2,strdesc3,iroad,strdesc from " + strTbName;
        }

        protected override string getSqlTextString()
        {
            return strSql;
        }
        public override string Name
        {
            get { return "DiySqlGetRoadpointAllRange"; }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[11];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Float;
            rType[3] = E_VType.E_Int64;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_String;
            rType[7] = E_VType.E_String;
            rType[8] = E_VType.E_String;
            rType[9] = E_VType.E_Int;
            rType[10] = E_VType.E_String;
            return rType;
        }

        public List<RoadCalculateRoadpoint_all> roadpoint_all_List { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            roadpoint_all_List = new List<RoadCalculateRoadpoint_all>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    RoadCalculateRoadpoint_all rpt = new RoadCalculateRoadpoint_all();
                    rpt.strroad = package.Content.GetParamString();
                    rpt.itype = package.Content.GetParamInt();
                    rpt.fdistance = package.Content.GetParamFloat() / 1000000;
                    rpt.isampleid = package.Content.GetParamInt64();
                    rpt.ilongitude = package.Content.GetParamInt();
                    rpt.ilatitude = package.Content.GetParamInt();
                    rpt.strdesc1 = package.Content.GetParamString();
                    rpt.strdesc2 = package.Content.GetParamString();
                    rpt.strdesc3 = package.Content.GetParamString();
                    rpt.iroad = package.Content.GetParamInt();
                    rpt.strdesc = package.Content.GetParamString();

                    roadpoint_all_List.Add(rpt);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class MapAreaInfo
    {
        public string strCityName { get; set; } = "";
        public int iID { get; set; } = 0;
        public string strAreaTbName { get; set; } = "";
        public string strGridName { get; set; } = "";
        public double fArea { get; set; } = 0;
        public string strComment { get; set; } = "";
    }
}
