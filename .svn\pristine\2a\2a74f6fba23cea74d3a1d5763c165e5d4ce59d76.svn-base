﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.Util.UiEx;
using MasterCom.MTGis;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc.LteSignalImsi;

namespace MasterCom.RAMS.ZTFunc
{
    public class KpiGridRenderByRegion : QueryKPIStatByRegion
    {
        public KpiGridRenderByRegion()
        {
            IsShowResultForm = false;
        }

        public override string Name
        {
            get { return "区域内栅格渲染"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11019, this.Name);
        }

        protected override bool getConditionBeforeQuery()
        {
            KpiGridRenderSetForm setForm = new KpiGridRenderSetForm(selectedColorMode);
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            selectedColorMode = setForm.GetSelectedItem();

            curReportStyle = null;
            isQueryAllParams = false;
            gridMatrix = new GridMatrix<GridDataHub>();
            return true;
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> formulaSet = new List<string>() { selectedColorMode.formula };
            return getTriadIDIgnoreServiceType(formulaSet);
        }

        protected override void recieveAndHandleSpecificStatData(Package package,
            List<StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            if (!condition.Geometorys.GeoOp.Contains(grid.CenterLng, grid.CenterLat))
            {
                return;
            }

            fillStatData(package, curImgColumnDef, singleStatData);
            saveGridData(singleStatData, false);
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            KpiGridRenderLayer layer = MainModel.MainForm.GetMapForm().GetLayerBase(typeof(KpiGridRenderLayer)) as KpiGridRenderLayer;
            if (layer == null)
            {
                layer = new KpiGridRenderLayer();
                MainModel.MainForm.GetMapForm().AddLayerBase(layer);
            }
            layer.SetMatrixAndColor(this.gridMatrix, this.selectedColorMode);

            MainModel.MainForm.GetMapForm().GetGridShowLayer().CurUsingColorMode = this.selectedColorMode;
            MainModel.FireDTDataChanged(MainModel.MainForm);
            MainModel.MainForm.LegendPanel.RefreshLegend();

            this.gridMatrix = null;
        }

        protected void saveGridData(KPIStatDataBase data, bool saveAsGrid)
        {
            DbRect rect = GridHelper.GetDefaultSizeGridBoundsByLeftTopPoint(data.LTLng, data.LTLat);
            int rowIdx;
            int colIdx;
            GridHelper.GetIndexOfDefaultSizeGrid(rect.Center().x, rect.Center().y, out rowIdx, out colIdx);
            GridDataHub hub = gridMatrix[rowIdx, colIdx];
            if (hub == null)
            {
                hub = new GridDataHub();
                hub.LTLng = rect.x1;
                hub.LTLat = rect.y2;
                gridMatrix[rowIdx, colIdx] = hub;
            }
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(data.FileID);
            hub.DataHub.AddStatData(fi, data, saveAsGrid);
        }

        protected GridMatrix<GridDataHub> gridMatrix = null;

        protected GridColorModeItem selectedColorMode = null;

        public class GridDataHub : GridUnitBase
        {
            public KPIDataGroup DataHub { get; set; } = new KPIDataGroup(null);

            public object Tag { get; set; }
        }
    }
}
