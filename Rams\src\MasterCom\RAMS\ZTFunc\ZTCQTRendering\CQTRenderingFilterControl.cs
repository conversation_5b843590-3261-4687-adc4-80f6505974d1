﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Stat;
using System.Collections;

namespace MasterCom.RAMS.Func
{
    public partial class CQTRenderingFilterControl : UserControl
    {
        /// <summary>
        /// 原始数据
        /// </summary>
        List<CqtFilterInfo> listOriginalData = new List<CqtFilterInfo>();

        public CQTRenderingFilterControl()
        {
            InitializeComponent();
        }

        public void SetInitData(string filter, List<CqtFilterInfo> cqtFilterInfos)
        {
            listOriginalData.Clear();
            listViewData.Items.Clear();
            listViewData.Columns.Clear();
            listViewData.Columns.Add("楼层名", 200);
            listViewData.Columns.Add("日期", 100);
            listViewData.HideSelection = false;
            //避免相同条件多选
            bool isSelected = false;
            foreach (CqtFilterInfo info in cqtFilterInfos)
            {
                ListViewItem lvi = new ListViewItem();
                lvi.Tag = info;
                lvi.Text = info.FloorName;
                lvi.SubItems.Add(info.TestDate);
                if (!isSelected && info.FloorName == filter)
                {
                    lvi.Selected = true;
                    isSelected = true;
                    popupCntEdit.Text = info.FloorName;
                }
                listViewData.Items.Add(lvi);
                listOriginalData.Add(info);
            }
            listViewData_ColumnClick(null, null);
        }

        public string GetData()
        {
            return popupCntEdit.Text;
        }

        /// <summary>
        /// 2个筛选用的控件宽度显示效果对齐
        /// </summary>
        private void popupContainerEdit_QueryPopUp(object sender, CancelEventArgs e)
        {
            PopupContainerEdit popupedit = (PopupContainerEdit)sender;
            popupContainerCtr.Width = popupedit.Width - 4;
        }

        /// <summary>
        /// 筛选条件改变时对应listbox数据改变
        /// </summary>
        private void txtFilter_TextChanged(object sender, EventArgs e)
        {
            List<CqtFilterInfo> listInfo = new List<CqtFilterInfo>();
            foreach (CqtFilterInfo item in listOriginalData)
            {
                if (string.IsNullOrEmpty(txtFilter.Text) || item.FloorName.ToUpper().IndexOf(txtFilter.Text.ToUpper()) >= 0)
                {
                    listInfo.Add(item);
                }
            }
            listViewData.Items.Clear();
            foreach (CqtFilterInfo info in listInfo)
            {
                ListViewItem lvi = new ListViewItem();
                lvi.Tag = info;
                lvi.Text = info.FloorName;
                lvi.SubItems.Add(info.TestDate);
                listViewData.Items.Add(lvi);
            }
        }

        private void listViewData_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listViewData.SelectedItems != null && listViewData.SelectedItems.Count > 0)
            {
                StringBuilder floorName = new StringBuilder();
                foreach (ListViewItem item in listViewData.SelectedItems)
                {
                    floorName.Append((item.Tag as CqtFilterInfo).FloorName + ",");
                }
                popupCntEdit.Text = floorName.ToString().TrimEnd(',');
            }
            else
            {
                popupCntEdit.Text = "";
            }
        }

        /// <summary>
        /// 双击关闭选择框
        /// </summary>
        private void listViewData_DoubleClick(object sender, EventArgs e)
        {
            popupCntEdit.ClosePopup();
        }

        private void listViewData_ColumnClick(object sender, ColumnClickEventArgs e)
        {
            int column = 0;
            if (e != null)
            {
                column = e.Column;
            }
            //使用列Tag标记是否为倒序
            if (this.listViewData.Columns[column].Tag == null)
            {
                this.listViewData.Columns[column].Tag = true;
            }
            bool flag = (bool)this.listViewData.Columns[column].Tag;
            //每次点击正序倒序切换
            if (flag)
            {
                //第一次点击为正序排序
                this.listViewData.Columns[column].Tag = false;
            }
            else
            {
                this.listViewData.Columns[column].Tag = true;
            }

            this.listViewData.ListViewItemSorter = new ListViewSort(column, this.listViewData.Columns[column].Tag);
            this.listViewData.Sort();
        }
    }

    /**
    class ListViewSort : IComparer
    {
        private int col;
        private bool desc;
        public ListViewSort()
        {
            col = 0;
        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="column">列index</param>
        /// <param name="Desc">是否倒序</param>
        public ListViewSort(int column, object Desc)
        {
            desc = (bool)Desc;
            col = column;
        }

        public int Compare(object x, object y)
        {
            int tempInt = String.Compare(((ListViewItem)x).SubItems[col].Text, ((ListViewItem)y).SubItems[col].Text);
            if (desc)
            {
                //如果倒序则取负数
                return -tempInt;
            }
            else
            {
                //正序排序
                return tempInt;
            }
        }
    }
    */
}
