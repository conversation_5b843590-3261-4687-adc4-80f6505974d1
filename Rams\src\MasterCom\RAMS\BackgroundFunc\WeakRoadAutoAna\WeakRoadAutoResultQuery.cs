﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraEditors;
using System.Windows.Forms;
using System.IO;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class WeakRoadAutoResultQuery : QueryBase
    {
        List<BackgroundResult> resultList = new List<BackgroundResult>();
        readonly WeakRoadReport curRpt ;
        public WeakRoadAutoResultQuery(WeakRoadReport rpt, MainModel mainModel)
            : base(mainModel)
        {
            curRpt = rpt;
        }
        public override string Name
        {
            get { return "自动统计结果导出"; }
        }

        public override string IconName
        {
            get { return ""; }
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11051, this.Name);
        }

        TimePeriod curTimePeriod = new TimePeriod();
        protected override void query()
        {
            if (MainModel.BackgroundStarted)
            {
                return;
            }
            if (MainModel.IsBackground)
            {
                MainModel.SystemConfigInfo.isBackground = false;
                MainModel.SystemConfigInfo.Save();
            }
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询自动统计结果...";

            for (int i = 0; i < curRpt.RecentMonths; i++)
            {
                DateTime startTime = new DateTime(DateTime.Now.Date.Year, DateTime.Now.Date.Month, 1).AddMonths(-1 * i);
                DateTime endTime = startTime.AddMonths(1);
                curTimePeriod = new TimePeriod(startTime, endTime);
                MainModel.ClearDTData();
                WaitBox.Show("正在查询" + curRpt.Name + "自动统计结果...", queryInThread);
                WaitBox.Show("正在导出...", doExport);
            }
        }
        protected virtual void queryInThread()
        {
            try
            {
                MainModel.QueryFromBackground = true;
                WeakRoadAutoQuery queryFunc = new WeakRoadAutoQuery(curRpt);

                QueryCondition cond = new QueryCondition();
                cond.Periods.Add(curTimePeriod);

                queryFunc.SetQueryCondition(cond);
                queryFunc.Query();
                resultList = queryFunc.BackgroundResultList;
            }
            catch
            {
                //continue
            }
            finally
            {
                MainModel.QueryFromBackground = false;
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        private void doExport()
        {
            DateTime time = curTimePeriod.BeginTime;
            string year = time.Year.ToString();
            string month = time.Month.ToString();
            if (month.Length == 1)
            {
                month = "0" + month;
            }
            string yearMonth = year + month;
            string strPath = string.Format(curRpt.FilePath + "\\" + yearMonth + curRpt.FileName);

            try
            {
                if (curRpt.FileFilterIndex == 1)
                {
                    string strFileSubName = strPath + ".csv";
                    outToCsvFile(strFileSubName, yearMonth);
                }
                else
                {
                    //文件路径
                    string strFileSubName = strPath + ".txt";
                    outToTxtFile(strFileSubName);
                }
            }
            catch
            { 
                //continue
            }
            WaitBox.Close();
            resultList.Clear();
        }
        private void outToTxtFile(string strFileSubName)
        {
            if (File.Exists(strFileSubName))
            {
                File.Delete(strFileSubName);
            }
            //字符流写出器
            StreamWriter sw = new StreamWriter(strFileSubName, true, Encoding.GetEncoding("gb2312"));

            try
            {
                if (resultList.Count == 0)
                {
                    sw.WriteLine("没有符合条件的信息！");
                    return;
                }
                StringBuilder sb = new StringBuilder();

                sb.Append("序号\t");
                if (curRpt.DistrictIDSet != null && curRpt.DistrictIDSet.Count > 1)
                {
                    sb.Append("地市\t");
                }
                sb.Append("经度\t\t");
                sb.Append("纬度\t\t");
                sb.Append("路段长度\t");
                sb.Append("采样点数\t");
                sb.Append("平均RSRP\t");

                sw.WriteLine(sb.ToString());
                sb.Remove(0, sb.Length);
                int iLoop = 1;
                foreach (BackgroundResult item in resultList)
                {
                    sb.Append(item.SN + "；\t");
                    if (curRpt.DistrictIDSet != null && curRpt.DistrictIDSet.Count > 1)
                    {
                        sb.Append(item.DistrictDesc + "；\t");
                    }
                    sb.Append(item.LongitudeMid + "；\t");
                    sb.Append(item.LatitudeMid + "；\t");
                    sb.Append(Math.Round(item.DistanceLast, 2) + "；    \t");
                    sb.Append(item.SampleCount + "；     \t");
                    sb.Append(item.RxLevMean + "；\t");

                    sw.WriteLine(sb.ToString());
                    sb.Remove(0, sb.Length);

                    WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / resultList.Count);
                }
            }
            catch (Exception e)
            {
                sw.WriteLine("导出到Txt出错" + e.Message);
            }
            finally
            {
                sw.Flush();
                sw.Close();
            }
        }

        private void outToCsvFile(string strFileSubName, string yearMonth)
        {
            if (System.IO.File.Exists(strFileSubName))
                System.IO.File.Delete(strFileSubName);

            System.IO.FileStream fileStream = new System.IO.FileStream(strFileSubName, System.IO.FileMode.CreateNew, System.IO.FileAccess.Write, System.IO.FileShare.Read);
            System.IO.StreamWriter streamWriter = new System.IO.StreamWriter(fileStream, Encoding.Default);

            try
            {
                StringBuilder sb = new StringBuilder();
                sb.Append("序号,");
                sb.Append("日期,");
                sb.Append("地市,");
                sb.Append("道路,");
                sb.Append("中心经度,");
                sb.Append("中心纬度,");
                sb.Append("弱覆盖点占比,");
                sb.Append("路段长度,");
                sb.Append("开始时间,");
                sb.Append("持续时间,");
                sb.Append("采样点数,");
                sb.Append("优化问题比例,");
                sb.Append("规划问题比例,");
                sb.Append("最大场强,");
                sb.Append("最小场强,");
                sb.Append("平均场强,");
                sb.Append("平均下载速率,");
                sb.Append("最强邻区最大场强,");
                sb.Append("最强邻区最小场强,");
                sb.Append("最强邻区平均场强,");
                sb.Append("最大SINR值,");
                sb.Append("最小SINR值,");
                sb.Append("平均SINR值,");
                sb.Append("TAC_CI,");
                sb.Append("问题路段关联小区,");
                streamWriter.WriteLine(sb.ToString());

                foreach (BackgroundResult item in resultList)
                {
                    float fSampleCountOp = item.GetImageValueFloat();
                    item.GetImageValueString();//strLacCi_opt
                    item.GetImageValueString();//strCellName_opt
                    float fSampleCountPlan = item.GetImageValueFloat();
                    string strLacCi_plan = item.GetImageValueString();
                    string strCellName_plan = item.GetImageValueString();
                    item.GetImageValueInt();//IMaxNBCellCount
                    item.GetImageValueString();//strMaxRsrpCellName
                    item.GetImageValueString();//strMaxNBCellMinDistince
                    float MaxNbRsrp = item.GetImageValueFloat();
                    float MinNbRsrp = item.GetImageValueFloat();
                    float AvgNbRsrp = item.GetImageValueFloat();
                    float MaxSINR = item.GetImageValueFloat();
                    float MinSINR = item.GetImageValueFloat();
                    float AvgSINR = item.GetImageValueFloat();
                    float AvgSpeed = item.GetImageValueFloat();
                    float WeakPointPercent = item.GetImageValueFloat();

                    sb = new StringBuilder();
                    sb.Append(item.SN + ",");
                    sb.Append(yearMonth + ",");
                    sb.Append(item.DistrictDesc + ",");
                    sb.Append(item.RoadDesc + ",");
                    sb.Append(item.LongitudeMid + ",");
                    sb.Append(item.LatitudeMid + ",");
                    sb.Append(WeakPointPercent + ",");
                    sb.Append(Math.Round(item.DistanceLast, 2) + ",");
                    sb.Append(item.DateTimeBeginString + ",");
                    sb.Append(item.TimeLast + ",");
                    sb.Append(item.SampleCount + ",");
                    sb.Append(fSampleCountOp + ",");
                    sb.Append(fSampleCountPlan + ",");
                    sb.Append(item.RxLevMax + ",");
                    sb.Append(item.RxLevMin + ",");
                    sb.Append(item.RxLevMean + ",");
                    sb.Append(AvgSpeed + ",");
                    sb.Append(MaxNbRsrp + ",");
                    sb.Append(MinNbRsrp + ",");
                    sb.Append(AvgNbRsrp + ",");
                    sb.Append(MaxSINR + ",");
                    sb.Append(MinSINR + ",");
                    sb.Append(AvgSINR + ",");
                    sb.Append(strLacCi_plan + ",");
                    sb.Append(strCellName_plan + ",");
                    streamWriter.WriteLine(sb.ToString());
                }
            }
            catch (Exception e)
            {
                streamWriter.WriteLine("导出到Csv出错" + e.Message);
            }
            finally
            {
                if (streamWriter != null)
                {
                    streamWriter.Close();
                }
                if (fileStream != null)
                {
                    fileStream.Close();
                }
            }
        }
    }
}
