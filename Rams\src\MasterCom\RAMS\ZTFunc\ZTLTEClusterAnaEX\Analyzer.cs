﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Windows.Forms;
using System.Reflection;
using System.Drawing;
using System.Drawing.Imaging;
using MasterCom.Util.UiEx;

using Excel = Microsoft.Office.Interop.Excel;
using MasterCom.RAMS.Model;
using MasterCom.Util;
namespace MasterCom.RAMS.ZTFunc.ZTLTEClusterAnaEX
{
    public class Analyzer
    {
        private readonly Dictionary<string, string> Path = null;

        private Dictionary<string, int> SINRDic = null;
        private Dictionary<string, int> RSRPDic = null;
        private Dictionary<string, int> LongSINRDic = null;
        private Dictionary<string, int> LongRSRPDic = null;
        private Dictionary<string, int> ULSpeedDic = null;
        private Dictionary<string, int> DLSpeedDic = null;

        string filePath = "";

        public Analyzer()
        {
            Path = new Dictionary<string, string>();
            SINRDic = new Dictionary<string, int>();
            RSRPDic = new Dictionary<string, int>();
            ULSpeedDic = new Dictionary<string, int>();
            DLSpeedDic = new Dictionary<string, int>();
            LongSINRDic = new Dictionary<string, int>();
            LongRSRPDic = new Dictionary<string, int>();

            init();
        }

        public void Analyze(DTFileDataManager file)
        {
            if (file.FileName.Contains("空扰") || file.FileName.Contains("短呼"))
            {
                dealIdel(file);
            }
            else if (file.FileName.Contains("下载") && file.FileName.Contains("加扰"))
            {
                dealDownLord(file);
            }
            else if (file.FileName.Contains("上传") && file.FileName.Contains("加扰"))
            {
                dealUpLord(file);
            }
        }

        private void dealIdel(DTFileDataManager file)
        {
            double lngMin = file.TestPoints[0].Longitude;
            double lngMax = file.TestPoints[0].Longitude;
            double latMin = file.TestPoints[0].Latitude;
            double latMax = file.TestPoints[0].Latitude;
            foreach (TestPoint tp in file.TestPoints)
            {
                if (tp.Longitude > 70 && tp.Latitude > 3)
                {
                    lngMin = Math.Min(lngMin, tp.Longitude);
                    lngMax = Math.Max(lngMax, tp.Longitude);
                    latMin = Math.Min(latMin, tp.Latitude);
                    latMax = Math.Max(latMax, tp.Latitude);
                }
                if ((float?)tp["lte_SINR"] != null)
                {
                    float sinr = (float)tp["lte_SINR"];
                    SINRAnaly(ref SINRDic, sinr);
                }
                if ((float?)tp["lte_RSRP"] != null)
                {
                    float rsrp = (float)tp["lte_RSRP"];
                    RSRPAnaly(ref RSRPDic, rsrp);
                }
            }

            MasterCom.MTGis.DbRect bounds = new MTGis.DbRect(lngMin - 0.001, latMin - 0.001
        , lngMax + 0.001, latMax + 0.001);
            MainModel mModel = MainModel.GetInstance();
            string picPath = null;

            mModel.MainForm.GetMapForm().GoToView(bounds);
            mModel.FireSetDefaultMapSerialTheme("LTE_TDD", "RSRP");
            mModel.FireDTDataChanged(mModel.MainForm);
            picPath = TakePicture("簇评估", "RSRP");
            Path["RSRP"] = picPath;

            mModel.MainForm.GetMapForm().GoToView(bounds);
            mModel.FireSetDefaultMapSerialTheme("LTE_TDD", "SINR");
            mModel.FireDTDataChanged(mModel.MainForm);

            picPath = TakePicture("簇评估", "SINR");
            Path["SINR"] = picPath;
        }

        private void dealDownLord(DTFileDataManager file)
        {
            double lngMin = file.TestPoints[0].Longitude;
            double lngMax = file.TestPoints[0].Longitude;
            double latMin = file.TestPoints[0].Latitude;
            double latMax = file.TestPoints[0].Latitude;
            foreach (TestPoint tp in file.TestPoints)
            {
                if ((int?)tp["lte_PDCP_DL"] != null)
                {
                    int speed = (int)tp["lte_PDCP_DL"] / 1024;
                    DownSpeedAnaly(ref DLSpeedDic, speed);
                }
                if ((float?)tp["lte_SINR"] != null)
                {
                    float sinr = (float)tp["lte_SINR"];
                    SINRAnaly(ref LongSINRDic, sinr);
                }
                if ((float?)tp["lte_RSRP"] != null)
                {
                    float rsrp = (float)tp["lte_RSRP"];
                    RSRPAnaly(ref LongRSRPDic, rsrp);
                }
                if (tp.Longitude > 70 && tp.Latitude > 3)
                {
                    lngMin = Math.Min(lngMin, tp.Longitude);
                    lngMax = Math.Max(lngMax, tp.Longitude);
                    latMin = Math.Min(latMin, tp.Latitude);
                    latMax = Math.Max(latMax, tp.Latitude);
                }

            }
            MasterCom.MTGis.DbRect bounds = new MTGis.DbRect(lngMin - 0.001, latMin - 0.001
        , lngMax + 0.001, latMax + 0.001);
            MainModel mModel = MainModel.GetInstance();
            string picPath = null;

            mModel.MainForm.GetMapForm().GoToView(bounds);
            mModel.FireSetDefaultMapSerialTheme("LTE_TDD", "PDCP_DL");
            mModel.FireDTDataChanged(mModel.MainForm);

            picPath = TakePicture("簇评估", "下载速率");
            Path["下载速率"] = picPath;

            mModel.MainForm.GetMapForm().GoToView(bounds);
            mModel.FireSetDefaultMapSerialTheme("LTE_TDD", "RSRP");
            mModel.FireDTDataChanged(mModel.MainForm);
            picPath = TakePicture("簇评估", "LongRSRP");
            Path["LongRSRP"] = picPath;

            mModel.MainForm.GetMapForm().GoToView(bounds);
            mModel.FireSetDefaultMapSerialTheme("LTE_TDD", "SINR");
            mModel.FireDTDataChanged(mModel.MainForm);

            picPath = TakePicture("簇评估", "LongSINR");
            Path["LongSINR"] = picPath;
        }

        private void dealUpLord(DTFileDataManager file)
        {
            double lngMin = file.TestPoints[0].Longitude;
            double lngMax = file.TestPoints[0].Longitude;
            double latMin = file.TestPoints[0].Latitude;
            double latMax = file.TestPoints[0].Latitude;
            foreach (TestPoint tp in file.TestPoints)
            {
                if ((int?)tp["lte_PDCP_UL"] != null)
                {
                    int speed = (int)tp["lte_PDCP_UL"] / 1024;
                    UpSpeedAnaly(ref ULSpeedDic, speed);
                }
                if (tp.Longitude > 70 && tp.Latitude > 3)
                {
                    lngMin = Math.Min(lngMin, tp.Longitude);
                    lngMax = Math.Max(lngMax, tp.Longitude);
                    latMin = Math.Min(latMin, tp.Latitude);
                    latMax = Math.Max(latMax, tp.Latitude);
                }
            }
            MasterCom.MTGis.DbRect bounds = new MTGis.DbRect(lngMin - 0.001, latMin - 0.001
    , lngMax + 0.001, latMax + 0.001);
            MainModel mModel = MainModel.GetInstance();
            string picPath = null;

            mModel.MainForm.GetMapForm().GoToView(bounds);
            mModel.FireSetDefaultMapSerialTheme("LTE_TDD", "PDCP_UL");
            mModel.FireDTDataChanged(mModel.MainForm);

            picPath = TakePicture("簇评估", "上传速率");
            Path["上传速率"] = picPath;
        }

        public void Export(string fileName)
        {
            filePath = fileName;
            WaitTextBox.Show("正在导出Excel文件...", export);
        }

        private string TakePicture(string btsName, string postfix)
        {
            string fileName = btsName + "_" + postfix + ".png";
            string path = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/LteStationAcceptance");
            path = System.IO.Path.Combine(path, fileName);

            Bitmap bitMap = MainModel.GetInstance().MainForm.GetMapForm().DrawToBitmapDIY();
            bitMap.Save(path, ImageFormat.Png);
            bitMap.Dispose();
            return path;
        }

        private void InsertExcelPicture(Excel.Workbook eBook, string startCell, string endCell, string picPath)
        {
            Excel.Worksheet eSheet = (Excel.Worksheet)eBook.Sheets[1];
            Excel.Range rng = eSheet.get_Range(startCell, endCell);

            double width = eBook.Application.CentimetersToPoints(17.36);
            double height = eBook.Application.CentimetersToPoints(10.11);
            eSheet.Shapes.AddPicture(picPath,
                Microsoft.Office.Core.MsoTriState.msoFalse,
                Microsoft.Office.Core.MsoTriState.msoCTrue,
                (float)(double)rng.Left, (float)(double)rng.Top,
                (float)width, (float)height);
        }

        private void export()
        {
            if (SINRDic == null && RSRPDic == null && DLSpeedDic == null && ULSpeedDic == null)
            {
                return;
            }
            //Excel.Application excel = null;
            //Excel.Workbook workbook = null;
            //Excel.Worksheet worksheet = null;


            Excel.Application excel = new Excel.Application();
            excel.Visible = false;
            Excel.Workbook workbook = excel.Workbooks.Open(filePath,
            Type.Missing, Type.Missing, Type.Missing, Type.Missing,
            Type.Missing, Type.Missing, Type.Missing, Type.Missing,
            Type.Missing, Type.Missing, Type.Missing, Type.Missing,
            Type.Missing, Type.Missing);


            Excel.Worksheet worksheet = (Excel.Worksheet)excel.ActiveSheet;
            excel.Visible = true;
            worksheet.Name = "簇评估";
            try
            {
                #region SINR
                if (SINRDic != null)
                {
                    int totalCount = 0;
                    int count1 = 0;
                    int count2 = 0;
                    int count3 = 0;
                    int count4 = 0;
                    int count5 = 0;
                    int count6 = 0;
                    int count7 = 0;

                    if (SINRDic.ContainsKey("[-20,-3)"))
                    {
                        count1 = SINRDic["[-20,-3)"];
                        totalCount += count1;
                    }
                    if (SINRDic.ContainsKey("[-3,0)"))
                    {
                        count2 = SINRDic["[-3,0)"];
                        totalCount += count2;
                    }
                    if (SINRDic.ContainsKey("[0,5)"))
                    {
                        count3 = SINRDic["[0,5)"];
                        totalCount += count3;
                    }
                    if (SINRDic.ContainsKey("[5,10)"))
                    {
                        count4 = SINRDic["[5,10)"];
                        totalCount += count4;
                    }
                    if (SINRDic.ContainsKey("[10,20)"))
                    {
                        count5 = SINRDic["[10,20)"];
                        totalCount += count5;
                    }
                    if (SINRDic.ContainsKey("[20,28)"))
                    {
                        count6 = SINRDic["[20,28)"];
                        totalCount += count6;
                    }
                    if (SINRDic.ContainsKey("[28,50)"))
                    {
                        count7 = SINRDic["[28,50)"];
                        totalCount += count7;
                    }

                    if (totalCount == 0)
                    {
                        totalCount = 1;
                    }
                    worksheet.Cells[30, 1] = "SINR值";
                    worksheet.Cells[30, 2] = "比例";

                    worksheet.Cells[31, 1] = "[-20,-3)";
                    worksheet.Cells[31, 2] = Math.Round((double)count1 / totalCount, 2);

                    worksheet.Cells[32, 1] = "[-3,0)";
                    worksheet.Cells[32, 2] = Math.Round((double)count2 / totalCount, 2);

                    worksheet.Cells[33, 1] = "[0,5)";
                    worksheet.Cells[33, 2] = Math.Round((double)count3 / totalCount, 2);

                    worksheet.Cells[34, 1] = "[5,10)";
                    worksheet.Cells[34, 2] = Math.Round((double)count4 / totalCount, 2);

                    worksheet.Cells[35, 1] = "[10,20)";
                    worksheet.Cells[35, 2] = Math.Round((double)count5 / totalCount, 2);

                    worksheet.Cells[36, 1] = "[20,28)";
                    worksheet.Cells[36, 2] = Math.Round((double)count6 / totalCount, 2);

                    worksheet.Cells[37, 1] = "[28,50)";
                    worksheet.Cells[37, 2] = Math.Round((double)count7 / totalCount, 2);
                }
                #endregion

                #region RSRP
                if (RSRPDic != null)
                {
                    int totalCount = 0;
                    int count1 = 0;
                    int count2 = 0;
                    int count3 = 0;
                    int count4 = 0;
                    int count5 = 0;
                    int count6 = 0;

                    if (RSRPDic.ContainsKey("[-150,-110)"))
                    {
                        count1 = RSRPDic["[-150,-110)"];
                        totalCount += count1;
                    }
                    if (RSRPDic.ContainsKey("[-110,-100)"))
                    {
                        count2 = RSRPDic["[-110,-100)"];
                        totalCount += count2;
                    }
                    if (RSRPDic.ContainsKey("[-100,-90)"))
                    {
                        count3 = RSRPDic["[-100,-90)"];
                        totalCount += count3;
                    }
                    if (RSRPDic.ContainsKey("[-90,-80)"))
                    {
                        count4 = RSRPDic["[-90,-80)"];
                        totalCount += count4;
                    }
                    if (RSRPDic.ContainsKey("[-80,-70)"))
                    {
                        count5 = RSRPDic["[-80,-70)"];
                        totalCount += count5;
                    }
                    if (RSRPDic.ContainsKey("[-70,0)"))
                    {
                        count6 = RSRPDic["[-70,0)"];
                        totalCount += count6;
                    }

                    if (totalCount == 0)
                    {
                        totalCount = 1;
                    }
                    worksheet.Cells[39, 1] = "RSRP值";
                    worksheet.Cells[39, 2] = "比例";

                    worksheet.Cells[40, 1] = "[-150,-110)";
                    worksheet.Cells[40, 2] = Math.Round((double)count1 / totalCount, 2);

                    worksheet.Cells[41, 1] = "[-110,-100)";
                    worksheet.Cells[41, 2] = Math.Round((double)count2 / totalCount, 2);

                    worksheet.Cells[42, 1] = "[-100,-90)";
                    worksheet.Cells[42, 2] = Math.Round((double)count3 / totalCount, 2);

                    worksheet.Cells[43, 1] = "[-90,-80)";
                    worksheet.Cells[43, 2] = Math.Round((double)count4 / totalCount, 2);

                    worksheet.Cells[44, 1] = "[-80,-70)";
                    worksheet.Cells[44, 2] = Math.Round((double)count5 / totalCount, 2);

                    worksheet.Cells[45, 1] = "[-70,0)";
                    worksheet.Cells[45, 2] = Math.Round((double)count6 / totalCount, 2);
                }
                #endregion

                #region 下行吞吐率
                if (DLSpeedDic != null)
                {
                    int totalCount = 0;
                    int count1 = 0;
                    int count2 = 0;
                    int count3 = 0;
                    int count4 = 0;
                    int count5 = 0;
                    int count6 = 0;
                    int count7 = 0;
                    int count8 = 0;
                    int count9 = 0;

                    if (DLSpeedDic.ContainsKey("[0,500)"))
                    {
                        count1 = DLSpeedDic["[0,500)"];
                        totalCount += count1;
                    }
                    if (DLSpeedDic.ContainsKey("[500,5000)"))
                    {
                        count2 = DLSpeedDic["[500,5000)"];
                        totalCount += count2;
                    }
                    if (DLSpeedDic.ContainsKey("[5000,10000)"))
                    {
                        count3 = DLSpeedDic["[5000,10000)"];
                        totalCount += count3;
                    }
                    if (DLSpeedDic.ContainsKey("[10000,15000)"))
                    {
                        count4 = DLSpeedDic["[10000,15000)"];
                        totalCount += count4;
                    }
                    if (DLSpeedDic.ContainsKey("[15000,20000)"))
                    {
                        count5 = DLSpeedDic["[15000,20000)"];
                        totalCount += count5;
                    }
                    if (DLSpeedDic.ContainsKey("[20000,25000)"))
                    {
                        count6 = DLSpeedDic["[20000,25000)"];
                        totalCount += count6;
                    }
                    if (DLSpeedDic.ContainsKey("[25000,30000)"))
                    {
                        count7 = DLSpeedDic["[25000,30000)"];
                        totalCount += count7;
                    }
                    if (DLSpeedDic.ContainsKey("[30000,40000)"))
                    {
                        count8 = DLSpeedDic["[30000,40000)"];
                        totalCount += count8;
                    }
                    if (DLSpeedDic.ContainsKey("[40000,90000)"))
                    {
                        count9 = DLSpeedDic["[40000,90000)"];
                        totalCount += count9;
                    }
                    if (totalCount == 0)
                    {
                        totalCount = 1;
                    }

                    worksheet.Cells[47, 1] = "下行吞吐率";
                    worksheet.Cells[47, 2] = "比例";

                    worksheet.Cells[48, 1] = "[0,500)";
                    worksheet.Cells[48, 2] = Math.Round((double)count1 / totalCount, 2);

                    worksheet.Cells[49, 1] = "[500,5000)";
                    worksheet.Cells[49, 2] = Math.Round((double)count2 / totalCount, 2);

                    worksheet.Cells[50, 1] = "[5000,10000)";
                    worksheet.Cells[50, 2] = Math.Round((double)count3 / totalCount, 2);

                    worksheet.Cells[51, 1] = "[10000,15000)";
                    worksheet.Cells[51, 2] = Math.Round((double)count4 / totalCount, 2);

                    worksheet.Cells[52, 1] = "[15000,20000)";
                    worksheet.Cells[52, 2] = Math.Round((double)count5 / totalCount, 2);

                    worksheet.Cells[53, 1] = "[20000,25000)";
                    worksheet.Cells[53, 2] = Math.Round((double)count6 / totalCount, 2);

                    worksheet.Cells[54, 1] = "[25000,30000)";
                    worksheet.Cells[54, 2] = Math.Round((double)count7 / totalCount, 2);

                    worksheet.Cells[55, 1] = "[30000,40000)";
                    worksheet.Cells[55, 2] = Math.Round((double)count8 / totalCount, 2);

                    worksheet.Cells[56, 1] = "[40000,90000)";
                    worksheet.Cells[56, 2] = Math.Round((double)count9 / totalCount, 2);
                }
                #endregion

                #region 上行吞吐率
                if (ULSpeedDic != null)
                {
                    int totalCount = 0;
                    int count1 = 0;
                    int count2 = 0;
                    int count3 = 0;
                    int count4 = 0;
                    int count5 = 0;
                    int count6 = 0;
                    int count7 = 0;
                    int count8 = 0;
                    int count9 = 0;

                    if (ULSpeedDic.ContainsKey("[0,500)"))
                    {
                        count1 = ULSpeedDic["[0,500)"];
                        totalCount += count1;
                    }
                    if (ULSpeedDic.ContainsKey("[500,1000)"))
                    {
                        count2 = ULSpeedDic["[500,1000)"];
                        totalCount += count2;
                    }
                    if (ULSpeedDic.ContainsKey("[1000,2000)"))
                    {
                        count3 = ULSpeedDic["[1000,2000)"];
                        totalCount += count3;
                    }
                    if (ULSpeedDic.ContainsKey("[2000,4000)"))
                    {
                        count4 = ULSpeedDic["[2000,4000)"];
                        totalCount += count4;
                    }
                    if (ULSpeedDic.ContainsKey("[4000,6000)"))
                    {
                        count5 = ULSpeedDic["[4000,6000)"];
                        totalCount += count5;
                    }
                    if (ULSpeedDic.ContainsKey("[6000,10000)"))
                    {
                        count6 = ULSpeedDic["[6000,10000)"];
                        totalCount += count6;
                    }
                    if (ULSpeedDic.ContainsKey("[10000,14000)"))
                    {
                        count7 = ULSpeedDic["[10000,14000)"];
                        totalCount += count7;
                    }
                    if (ULSpeedDic.ContainsKey("[14000,18000)"))
                    {
                        count8 = ULSpeedDic["[14000,18000)"];
                        totalCount += count8;
                    }
                    if (ULSpeedDic.ContainsKey("[18000,22000)"))
                    {
                        count9 = ULSpeedDic["[18000,22000)"];
                        totalCount += count9;
                    }

                    if (totalCount == 0)
                    {
                        totalCount = 1;
                    }
                    worksheet.Cells[58, 1] = "上行吞吐率";
                    worksheet.Cells[58, 2] = "比例";

                    worksheet.Cells[59, 1] = "[0,500)";
                    worksheet.Cells[59, 2] = Math.Round((double)count1 / totalCount, 2);

                    worksheet.Cells[60, 1] = "[500,1000)";
                    worksheet.Cells[60, 2] = Math.Round((double)count2 / totalCount, 2);

                    worksheet.Cells[61, 1] = "[1000,2000)";
                    worksheet.Cells[61, 2] = Math.Round((double)count3 / totalCount, 2);

                    worksheet.Cells[62, 1] = "[2000,4000)";
                    worksheet.Cells[62, 2] = Math.Round((double)count4 / totalCount, 2);

                    worksheet.Cells[63, 1] = "[4000,6000)";
                    worksheet.Cells[63, 2] = Math.Round((double)count5 / totalCount, 2);

                    worksheet.Cells[64, 1] = "[6000,10000)";
                    worksheet.Cells[64, 2] = Math.Round((double)count6 / totalCount, 2);

                    worksheet.Cells[65, 1] = "[10000,14000)";
                    worksheet.Cells[65, 2] = Math.Round((double)count7 / totalCount, 2);

                    worksheet.Cells[66, 1] = "[14000,18000)";
                    worksheet.Cells[66, 2] = Math.Round((double)count8 / totalCount, 2);

                    worksheet.Cells[67, 1] = "[18000,22000)";
                    worksheet.Cells[67, 2] = Math.Round((double)count9 / totalCount, 2);
                }
                #endregion

                #region LongSINR
                if (LongSINRDic != null)
                {
                    int totalCount = 0;
                    int count1 = 0;
                    int count2 = 0;
                    int count3 = 0;
                    int count4 = 0;
                    int count5 = 0;
                    int count6 = 0;
                    int count7 = 0;

                    if (LongSINRDic.ContainsKey("[-20,-3)"))
                    {
                        count1 = LongSINRDic["[-20,-3)"];
                        totalCount += count1;
                    }
                    if (LongSINRDic.ContainsKey("[-3,0)"))
                    {
                        count2 = LongSINRDic["[-3,0)"];
                        totalCount += count2;
                    }
                    if (LongSINRDic.ContainsKey("[0,5)"))
                    {
                        count3 = LongSINRDic["[0,5)"];
                        totalCount += count3;
                    }
                    if (LongSINRDic.ContainsKey("[5,10)"))
                    {
                        count4 = LongSINRDic["[5,10)"];
                        totalCount += count4;
                    }
                    if (LongSINRDic.ContainsKey("[10,20)"))
                    {
                        count5 = LongSINRDic["[10,20)"];
                        totalCount += count5;
                    }
                    if (LongSINRDic.ContainsKey("[20,28)"))
                    {
                        count6 = LongSINRDic["[20,28)"];
                        totalCount += count6;
                    }
                    if (LongSINRDic.ContainsKey("[28,50)"))
                    {
                        count7 = LongSINRDic["[28,50)"];
                        totalCount += count7;
                    }

                    if (totalCount == 0)
                    {
                        totalCount = 1;
                    }
                    worksheet.Cells[69, 1] = "加扰SINR值";
                    worksheet.Cells[69, 2] = "比例";

                    worksheet.Cells[70, 1] = "[-20,-3)";
                    worksheet.Cells[70, 2] = Math.Round((double)count1 / totalCount, 2);

                    worksheet.Cells[71, 1] = "[-3,0)";
                    worksheet.Cells[71, 2] = Math.Round((double)count2 / totalCount, 2);

                    worksheet.Cells[72, 1] = "[0,5)";
                    worksheet.Cells[72, 2] = Math.Round((double)count3 / totalCount, 2);

                    worksheet.Cells[73, 1] = "[5,10)";
                    worksheet.Cells[73, 2] = Math.Round((double)count4 / totalCount, 2);

                    worksheet.Cells[74, 1] = "[10,20)";
                    worksheet.Cells[74, 2] = Math.Round((double)count5 / totalCount, 2);

                    worksheet.Cells[75, 1] = "[20,28)";
                    worksheet.Cells[75, 2] = Math.Round((double)count6 / totalCount, 2);

                    worksheet.Cells[76, 1] = "[28,50)";
                    worksheet.Cells[76, 2] = Math.Round((double)count7 / totalCount, 2);
                }
                #endregion

                #region RSRP
                if (LongRSRPDic != null)
                {
                    int totalCount = 0;
                    int count1 = 0;
                    int count2 = 0;
                    int count3 = 0;
                    int count4 = 0;
                    int count5 = 0;
                    int count6 = 0;

                    if (LongRSRPDic.ContainsKey("[-150,-110)"))
                    {
                        count1 = LongRSRPDic["[-150,-110)"];
                        totalCount += count1;
                    }
                    if (LongRSRPDic.ContainsKey("[-110,-100)"))
                    {
                        count2 = LongRSRPDic["[-110,-100)"];
                        totalCount += count2;
                    }
                    if (LongRSRPDic.ContainsKey("[-100,-90)"))
                    {
                        count3 = LongRSRPDic["[-100,-90)"];
                        totalCount += count3;
                    }
                    if (LongRSRPDic.ContainsKey("[-90,-80)"))
                    {
                        count4 = LongRSRPDic["[-90,-80)"];
                        totalCount += count4;
                    }
                    if (LongRSRPDic.ContainsKey("[-80,-70)"))
                    {
                        count5 = LongRSRPDic["[-80,-70)"];
                        totalCount += count5;
                    }
                    if (LongRSRPDic.ContainsKey("[-70,0)"))
                    {
                        count6 = LongRSRPDic["[-70,0)"];
                        totalCount += count6;
                    }

                    if (totalCount == 0)
                    {
                        totalCount = 1;
                    }
                    worksheet.Cells[78, 1] = "加扰RSRP值";
                    worksheet.Cells[78, 2] = "比例";

                    worksheet.Cells[79, 1] = "[-150,-110)";
                    worksheet.Cells[79, 2] = Math.Round((double)count1 / totalCount, 2);

                    worksheet.Cells[80, 1] = "[-110,-100)";
                    worksheet.Cells[80, 2] = Math.Round((double)count2 / totalCount, 2);

                    worksheet.Cells[81, 1] = "[-100,-90)";
                    worksheet.Cells[81, 2] = Math.Round((double)count3 / totalCount, 2);

                    worksheet.Cells[82, 1] = "[-90,-80)";
                    worksheet.Cells[82, 2] = Math.Round((double)count4 / totalCount, 2);

                    worksheet.Cells[83, 1] = "[-80,-70)";
                    worksheet.Cells[83, 2] = Math.Round((double)count5 / totalCount, 2);

                    worksheet.Cells[84, 1] = "[-70,0)";
                    worksheet.Cells[84, 2] = Math.Round((double)count6 / totalCount, 2);
                }
                #endregion

                //创建图表
                CreateChart(ref workbook, ref worksheet);

                worksheet.Cells[86, 1] = "SINR";
                worksheet.Cells[127, 1] = "RSRP";
                worksheet.Cells[167, 1] = "下载速率";
                worksheet.Cells[208, 1] = "上传速率";
                worksheet.Cells[249, 1] = "加扰SINR";
                worksheet.Cells[290, 1] = "加扰RSRP";

                foreach (var KeyAndValue in Path)
                {
                    if (KeyAndValue.Key == "SINR")
                    {
                        InsertExcelPicture(workbook, "A87", "I106", KeyAndValue.Value);
                    }
                    else if (KeyAndValue.Key == "RSRP")
                    {
                        InsertExcelPicture(workbook, "A128", "I147", KeyAndValue.Value);
                    }
                    else if (KeyAndValue.Key == "下载速率")
                    {
                        InsertExcelPicture(workbook, "A168", "I187", KeyAndValue.Value);
                    }
                    else if (KeyAndValue.Key == "上传速率")
                    {
                        InsertExcelPicture(workbook, "A209", "I228", KeyAndValue.Value);
                    }
                    else if (KeyAndValue.Key == "LongSINR")
                    {
                        InsertExcelPicture(workbook, "A250", "I269", KeyAndValue.Value);
                    }
                    else if (KeyAndValue.Key == "LongRSRP")
                    {
                        InsertExcelPicture(workbook, "A291", "I310", KeyAndValue.Value);
                    }
                }
                //保存文件
                excel.DisplayAlerts = false;
                workbook.SaveAs(filePath, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Excel.XlSaveAsAccessMode.xlNoChange, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value);

                DevExpress.XtraEditors.XtraMessageBox.Show(" 导出成功！ ", " 提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            finally
            {
                if (excel != null)
                {
                    excel.Quit();
                }
                WaitTextBox.Close();
            }

        }

        private void CreateChart(ref Excel.Workbook workbook, ref Excel.Worksheet worksheet)
        {

            //sinr
            Excel.Chart chart1 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange1 = worksheet.get_Range("B30", "B37");
            chart1.ChartWizard(chartrange1, Excel.XlChartType.xlColumnClustered, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "SINR", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries1 = (Excel.Series)chart1.SeriesCollection(1);
            // exseries1.BarShape = Excel.XlBarShape.xlCylinder;
            exseries1.XValues = worksheet.get_Range("A31", "A38");
            exseries1.HasDataLabels = true;

            Excel.Axis yAxis1 = (Excel.Axis)chart1.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis1.MinimumScale = 0.0;
            yAxis1.MajorUnit = 1.0;
            yAxis1.HasMajorGridlines = true;
            yAxis1.MajorGridlines.Border.ColorIndex = 15;
            yAxis1.TickLabels.NumberFormat = "0.00";

            chart1.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(1).Top = (float)(double)worksheet.get_Range("A107", "G126").Top;
            worksheet.Shapes.Item(1).Left = (float)(double)worksheet.get_Range("A107", "G126").Left;

            //RSRP
            Excel.Chart chart2 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange2 = worksheet.get_Range("B39", "B45");
            chart2.ChartWizard(chartrange2, Excel.XlChartType.xlColumnClustered, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "RSRP", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries2 = (Excel.Series)chart2.SeriesCollection(1);
            // exseries2.BarShape = Excel.XlBarShape.xlCylinder;
            exseries2.XValues = worksheet.get_Range("A40", "A46");
            exseries2.HasDataLabels = true;

            Excel.Axis yAxis2 = (Excel.Axis)chart2.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis2.MinimumScale = 0.0;
            yAxis2.MajorUnit = 1.0;
            yAxis2.HasMajorGridlines = true;
            yAxis2.MajorGridlines.Border.ColorIndex = 15;
            yAxis2.TickLabels.NumberFormat = "0.00";

            chart2.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(2).Top = (float)(double)worksheet.get_Range("A148", "G166").Top;
            worksheet.Shapes.Item(2).Left = (float)(double)worksheet.get_Range("A148", "G166").Left;

            //下行吞吐率
            Excel.Chart chart3 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange3 = worksheet.get_Range("B47", "B56");
            chart3.ChartWizard(chartrange3, Excel.XlChartType.xlColumnClustered, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "下行吞吐率", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries3 = (Excel.Series)chart3.SeriesCollection(1);
            //exseries3.BarShape = Excel.XlBarShape.xlCylinder;
            exseries3.XValues = worksheet.get_Range("A48", "A57");
            exseries3.HasDataLabels = true;

            Excel.Axis yAxis3 = (Excel.Axis)chart3.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis3.MinimumScale = 0.0;
            yAxis3.MajorUnit = 1.0;
            yAxis3.HasMajorGridlines = true;
            yAxis3.MajorGridlines.Border.ColorIndex = 15;
            yAxis3.TickLabels.NumberFormat = "0.00";

            chart3.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(3).Top = (float)(double)worksheet.get_Range("A188", "G207").Top;
            worksheet.Shapes.Item(3).Left = (float)(double)worksheet.get_Range("A188", "G207").Left;

            //上行吞吐率
            Excel.Chart chart4 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange4 = worksheet.get_Range("B58", "B67");
            chart4.ChartWizard(chartrange4, Excel.XlChartType.xlColumnClustered, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "上行吞吐率", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries4 = (Excel.Series)chart4.SeriesCollection(1);
            // exseries4.BarShape = Excel.XlBarShape.xlCylinder;
            exseries4.XValues = worksheet.get_Range("A59", "A68");
            exseries4.HasDataLabels = true;

            Excel.Axis yAxis4 = (Excel.Axis)chart4.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis4.MinimumScale = 0.0;
            yAxis4.MajorUnit = 1.0;
            yAxis4.HasMajorGridlines = true;
            yAxis4.MajorGridlines.Border.ColorIndex = 15;
            yAxis4.TickLabels.NumberFormat = "0.00";

            chart4.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(4).Top = (float)(double)worksheet.get_Range("A229", "G248").Top;
            worksheet.Shapes.Item(4).Left = (float)(double)worksheet.get_Range("A229", "G248").Left;

            //Longsinr
            Excel.Chart chart5 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange5 = worksheet.get_Range("B69", "B76");
            chart5.ChartWizard(chartrange5, Excel.XlChartType.xlColumnClustered, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "加扰SINR", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries5 = (Excel.Series)chart5.SeriesCollection(1);
            // exseries1.BarShape = Excel.XlBarShape.xlCylinder;
            exseries5.XValues = worksheet.get_Range("A69", "A76");
            exseries5.HasDataLabels = true;

            Excel.Axis yAxis5 = (Excel.Axis)chart5.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis5.MinimumScale = 0.0;
            yAxis5.MajorUnit = 1.0;
            yAxis5.HasMajorGridlines = true;
            yAxis5.MajorGridlines.Border.ColorIndex = 15;
            yAxis5.TickLabels.NumberFormat = "0.00";

            chart5.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(5).Top = (float)(double)worksheet.get_Range("A270", "G289").Top;
            worksheet.Shapes.Item(5).Left = (float)(double)worksheet.get_Range("A270", "G289").Left;

            //LongRSRP
            Excel.Chart chart6 = (Excel.Chart)workbook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange6 = worksheet.get_Range("B78", "B84");
            chart6.ChartWizard(chartrange6, Excel.XlChartType.xlColumnClustered, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "加扰RSRP", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries6 = (Excel.Series)chart6.SeriesCollection(1);
            // exseries2.BarShape = Excel.XlBarShape.xlCylinder;
            exseries6.XValues = worksheet.get_Range("A78", "A84");
            exseries6.HasDataLabels = true;

            Excel.Axis yAxis6 = (Excel.Axis)chart6.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis6.MinimumScale = 0.0;
            yAxis6.MajorUnit = 1.0;
            yAxis6.HasMajorGridlines = true;
            yAxis6.MajorGridlines.Border.ColorIndex = 15;
            yAxis6.TickLabels.NumberFormat = "0.00";

            chart6.Location(Excel.XlChartLocation.xlLocationAsObject, worksheet.Name);
            worksheet.Shapes.Item(6).Top = (float)(double)worksheet.get_Range("A311", "G330").Top;
            worksheet.Shapes.Item(6).Left = (float)(double)worksheet.get_Range("A311", "G330").Left;
        }

        private void SINRAnaly(ref Dictionary<string, int> SINRDic, float sinr)
        {
            analy(sinrRangeList, SINRDic, sinr);
        }

        private void RSRPAnaly(ref Dictionary<string, int> Dic, float KPI)
        {
            analy(rsrpRangeList, Dic, KPI);
        }

        private void UpSpeedAnaly(ref Dictionary<string, int> Dic, int KPI)
        {
            analy(upSpeedRangeList, Dic, KPI);
        }

        private void DownSpeedAnaly(ref Dictionary<string, int> Dic, int KPI)
        {
            analy(downSpeedRangeList, Dic, KPI);
        }

        private void analy(List<KPIRange> rangeList, Dictionary<string, int> Dic, float KPI)
        {
            foreach (var range in rangeList)
            {
                bool isAdded = addDic(range, Dic, KPI);
                if (isAdded)
                {
                    break;
                }
            }
        }

        List<KPIRange> sinrRangeList;
        List<KPIRange> rsrpRangeList;
        List<KPIRange> upSpeedRangeList;
        List<KPIRange> downSpeedRangeList;

        private void init()
        {
            sinrRangeList = new List<KPIRange>();
            sinrRangeList.Add(new KPIRange(-20, -3));
            sinrRangeList.Add(new KPIRange(-3, 0));
            sinrRangeList.Add(new KPIRange(0, 5));
            sinrRangeList.Add(new KPIRange(5, 10));
            sinrRangeList.Add(new KPIRange(10, 20));
            sinrRangeList.Add(new KPIRange(20, 28));
            sinrRangeList.Add(new KPIRange(28, 50));

            rsrpRangeList = new List<KPIRange>();
            rsrpRangeList.Add(new KPIRange(-150, -110));
            rsrpRangeList.Add(new KPIRange(-110, -100));
            rsrpRangeList.Add(new KPIRange(-100, -90));
            rsrpRangeList.Add(new KPIRange(-90, -80));
            rsrpRangeList.Add(new KPIRange(-80, -70));
            rsrpRangeList.Add(new KPIRange(-70, 0));

            upSpeedRangeList = new List<KPIRange>();
            upSpeedRangeList.Add(new KPIRange(0, 500));
            upSpeedRangeList.Add(new KPIRange(500, 1000));
            upSpeedRangeList.Add(new KPIRange(1000, 2000));
            upSpeedRangeList.Add(new KPIRange(2000, 4000));
            upSpeedRangeList.Add(new KPIRange(4000, 6000));
            upSpeedRangeList.Add(new KPIRange(6000, 10000));
            upSpeedRangeList.Add(new KPIRange(10000, 14000));
            upSpeedRangeList.Add(new KPIRange(14000, 18000));
            upSpeedRangeList.Add(new KPIRange(18000, 22000));

            downSpeedRangeList = new List<KPIRange>();
            downSpeedRangeList.Add(new KPIRange(0, 500));
            downSpeedRangeList.Add(new KPIRange(500, 5000));
            downSpeedRangeList.Add(new KPIRange(5000, 10000));
            downSpeedRangeList.Add(new KPIRange(10000, 15000));
            downSpeedRangeList.Add(new KPIRange(15000, 20000));
            downSpeedRangeList.Add(new KPIRange(20000, 25000));
            downSpeedRangeList.Add(new KPIRange(25000, 30000));
            downSpeedRangeList.Add(new KPIRange(30000, 35000));
            downSpeedRangeList.Add(new KPIRange(35000, 40000));
            downSpeedRangeList.Add(new KPIRange(40000, 90000));
        }

        private bool addDic(KPIRange kpiRange, Dictionary<string, int> Dic, float KPI)
        {
            if (KPI >= kpiRange.Min && KPI < kpiRange.Max)
            {
                int count;
                if (!Dic.TryGetValue(kpiRange.Name, out count))
                {
                    Dic[kpiRange.Name] = 0;
                }
                Dic[kpiRange.Name]++;
                return true;
            }
            return false;
        }

        class KPIRange
        {
            public KPIRange(double min, double max)
            {
                Max = max;
                Min = min;
                Name = "[" + min.ToString() + "," + max.ToString() + ")";
            }

            public string Name { get; set; }
            public double Min { get; set; }
            public double Max { get; set; }
        }
    }
}
