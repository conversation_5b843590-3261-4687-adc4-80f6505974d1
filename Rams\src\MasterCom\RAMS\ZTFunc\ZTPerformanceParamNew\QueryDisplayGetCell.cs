﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.CQT;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryDisplayGetCell : DIYReplayFileQuery
    {
        public QueryDisplayGetCell(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            IsAddSampleToDTDataManager = false;
            getCiList = new List<int>();
        }
        public override string Name
        {
            get { return "获取回放文件中小区集"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        public static List<int> getCiList { get; set; }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                if (tp != null)
                {
                    if (tp is TestPointDetail)
                    {
                        doWithGSMData(tp);
                    }
                    else if (tp is TDTestPointDetail)
                    {
                        doWithTDData(tp);
                    }
                }
            }
            catch (System.Exception ex)
            {
                log.Error(ex.Message);
            }
        }

        private bool isValidGSMData(TestPoint tpPoint)
        {
            float rxLevSub = (float)(short)tpPoint["RxLevSub"];
            byte rxQualSub = (byte)tpPoint["RxQualSub"];

            if (rxLevSub < -120 || rxLevSub > -10 || rxQualSub < 0 || rxQualSub > 7)
            {
                return false;
            }
            else
            {
                return true;
            }
        }

        private bool isValidTDData(TestPoint tpPoint)
        {
            int? rscp = null;
            if (tpPoint["TD_PCCPCH_RSCP"] is float)
            {
                rscp = (int?)(float?)tpPoint["TD_PCCPCH_RSCP"];
            }
            else
            {
                rscp = (int?)tpPoint["TD_PCCPCH_RSCP"];
            }
            float? c2i = (float?)(int?)tpPoint["TD_PCCPCH_C2I"];

            if (c2i == null || rscp == null || c2i < -20 || c2i > 25 || rscp < -140 || rscp > -10)
            {
                return false;
            }
            return true;
        }

        private bool isValidTDGSMData(TestPoint tpPoint)
        {
            float rxLevSub = (float)(int?)tpPoint["TD_GSM_RxlevSub"];
            byte? rxQualSub = (byte?)(int?)tpPoint["TD_GSM_RxQualSub"];
            float? c2i = (float?)(short?)tpPoint["TD_GSM_C2I_C2I", 0];

            if (rxLevSub < -120 || rxLevSub > -10 || rxQualSub < 0 || rxQualSub > 7 || rxQualSub == null || c2i < -30 || c2i > 30)
            {
                return false;
            }
            return true;
        }

        private void doWithGSMData(TestPoint tpPoint)
        {
            //================== 判断邻区 ==================
            for (int i = 0; i < 6; i++)
            {
                short? nBcch = (short?)tpPoint["N_BCCH", i];
                byte? nBsic = (byte?)tpPoint["N_BSIC", i];
                int? nRxLev = (int?)(short?)tpPoint["N_RxLev", i];
                if (nBcch == null || nBsic == null || nRxLev < -120 || nRxLev > -10)
                {
                    break;
                }
                Cell nbCell = CellManager.GetInstance().GetNearestCell(tpPoint.DateTime, (short)nBcch, (byte)nBsic, tpPoint.Longitude, tpPoint.Latitude);
                if (!getCiList.Contains(nbCell.CI))
                    getCiList.Add(nbCell.CI);
            }

            //================== 判断主服 ==================
            if (!isValidGSMData(tpPoint))
            {
                return;
            }
            Cell cell = CellManager.GetInstance().GetNearestCell(tpPoint.DateTime, (ushort?)(int?)tpPoint["LAC"], (ushort?)(int?)tpPoint["CI"], tpPoint.Longitude, tpPoint.Latitude);
            if (!getCiList.Contains(cell.CI))
                getCiList.Add(cell.CI);
        }

        private void doWithTDData(TestPoint tpPoint)
        {
            //================== 首先判断是否占用到GSM网络 ==================
            if ((int)tpPoint[MainModel.TD_GSM_SCell_LAC] != -255 && (int)tpPoint[MainModel.TD_GSM_SCell_CI] != -255)
            {
                doWithTDGSMData(tpPoint);
                return;
            }

            //================== 判断邻区 ==================
            for (int i = 0; i < 6; i++)
            {
                int? nArfcn = (int?)tpPoint["TD_NCell_UARFCN", i];
                int? nCpi = (int?)tpPoint["TD_NCell_CPI", i];
                if (nArfcn == null || nCpi == null)
                {
                    break;
                }

                TDCell nbCell = CellManager.GetInstance().GetNearestTDCell(tpPoint.DateTime, (short)nArfcn, (short)nCpi, tpPoint.Longitude, tpPoint.Latitude);
                if (!getCiList.Contains(nbCell.CI))
                    getCiList.Add(nbCell.CI);
            }

            //================== 判断主服 ==================
            if (!isValidTDData(tpPoint))
            {
                return;
            }
            TDCell tdcell = CellManager.GetInstance().GetNearestTDCell(tpPoint.DateTime, (int?)tpPoint[MainModel.TD_SCell_LAC], (int?)tpPoint[MainModel.TD_SCell_CI], tpPoint.Longitude, tpPoint.Latitude);
            if (!getCiList.Contains(tdcell.CI))
                getCiList.Add(tdcell.CI);
        }

        private void doWithTDGSMData(TestPoint tpPoint)
        {
            //================== 判断邻区 ==================
            for (int i = 0; i < 6; i++)
            {
                short? nBcch = (short?)tpPoint["TD_GSM_NCell_ARFCN", i];
                byte? nBsic = (byte?)tpPoint["TD_GSM_NCell_BSIC", i];
                if (nBcch == null || nBsic == null)
                {
                    break;
                }
                Cell nbCell = CellManager.GetInstance().GetNearestCell(tpPoint.DateTime, (short)nBcch, (byte)nBsic, tpPoint.Longitude, tpPoint.Latitude);
                if (!getCiList.Contains(nbCell.CI))
                    getCiList.Add(nbCell.CI);
            }

            //================== 判断主服 ==================
            if (!isValidTDGSMData(tpPoint))
            {
                return;
            }
            Cell cell = CellManager.GetInstance().GetNearestCell(tpPoint.DateTime, (ushort?)(int?)tpPoint["TD_GSM_SCell_LAC"], (ushort?)(int?)tpPoint["TD_GSM_SCell_CI"], tpPoint.Longitude, tpPoint.Latitude);
            if (!getCiList.Contains(cell.CI))
                getCiList.Add(cell.CI);
        }
    }
}

