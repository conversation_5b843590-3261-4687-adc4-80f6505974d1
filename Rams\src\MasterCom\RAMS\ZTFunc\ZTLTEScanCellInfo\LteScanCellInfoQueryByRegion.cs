﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteScanCellInfoQueryByRegion : DIYSampleByRegion
    {
        private LteScanCellInfoStater stater;
        public LteScanCellInfoQueryByRegion(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        public override string Name
        {
            get { return "LTE扫频小区集(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23026, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            stater = new LteScanCellInfoStater();
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSSINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LTE_SCAN");
            tmpDic.Add("themeName", (object)"TopN_CELL_Specific_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            stater.StatTestPoint(tp);
        }

        protected override void FireShowFormAfterQuery()
        {
            List<LteScanCellInfo> cellInfoList = stater.GetStatResult();
            LteScanCellInfoResultForm frm = MainModel.GetInstance().CreateResultForm(typeof(LteScanCellInfoResultForm)) as LteScanCellInfoResultForm;
            frm.FillData(cellInfoList);
            frm.Visible = true;
            frm.BringToFront();
        }
    }
}
