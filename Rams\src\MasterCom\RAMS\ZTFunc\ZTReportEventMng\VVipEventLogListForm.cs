﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraGrid.Views.Grid;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTReportEventMng
{
    public partial class VVipEventLogListForm : MinCloseForm
    {
        public VVipEventLogListForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
        }

        private List<EventLogGSM> gsmLogs;
        private List<EventLogTD> tdLogs;
        public void FillData(List<EventLogGSM> gsmLogs,List<EventLogTD> tdLogs)
        {
            this.gsmLogs = gsmLogs;
            this.tdLogs = tdLogs;
            gridControlGSM.DataSource = gsmLogs;
            gridControlGSM.RefreshDataSource();
            gridControlTD.DataSource = tdLogs;
            gridControlTD.RefreshDataSource();
        }
        VVipEventLogEditForm editDlg = null;
        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxE = e as DevExpress.Utils.DXMouseEventArgs;
            GridView view = sender as GridView;
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = view.CalcHitInfo(dxE.Location);
            if (info.InRow)
            {
                EventLog log = view.GetRow(info.RowHandle) as EventLog;
                if (editDlg == null || editDlg.IsDisposed)
                {
                    editDlg = new VVipEventLogEditForm();
                }
                editDlg.FillData(log);
                editDlg.ShowDialog(this);
            }
        }

    }
}
