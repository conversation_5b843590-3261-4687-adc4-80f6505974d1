﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class EndToEndQuery : DIYAnalyseByFileBackgroundBase
    {
        List<EndToEndInfo> resultList = null;

        protected static readonly object lockObj = new object();
        private static EndToEndQuery instance = null;
        public static EndToEndQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new EndToEndQuery();
                    }
                }
            }
            return instance;
        }

        protected EndToEndQuery()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
        }

        public override string Name
        {
            get
            {
                return "端对端问题分析(按区域)";
            }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isValidTestPoint(MasterCom.RAMS.Model.TestPoint testPoint)
        {
            return true;
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 27000, 27014, this.Name);
        }

        EndToEndCondition hocondition = null;
        EndToEndSettingForm setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new EndToEndSettingForm();
            }
            if (setForm.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                resultList = new List<EndToEndInfo>();
                setForm.GetCondition(out hocondition);
                return true;
            }
            return false;
        }

        protected override void fireShowForm()
        {
            if (resultList == null || resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的数据");
                return;
            }
            EndToEndResultListForm frm = MainModel.GetInstance().CreateResultForm(typeof(EndToEndResultListForm)) as EndToEndResultListForm;
            frm.FillData(resultList);
            frm.Visible = true;
            frm.BringToFront();
            resultList = null;
        }

        protected override void analyseFiles()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = getMoMTPair();

            try
            {
                clearDataBeforeAnalyseFiles();
                int iloop = 0;
                foreach (KeyValuePair<FileInfo, FileInfo> pair in moMtPair)
                {
                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + moMtPair.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / moMtPair.Count);

                    condition.FileInfos.Clear();
                    if (pair.Key != null)
                    {
                        condition.FileInfos.Add(pair.Key);
                    }
                    if (pair.Value != null)
                    {
                        condition.FileInfos.Add(pair.Value);
                    }
                    replay();
                    condition.FileInfos.Clear();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        private Dictionary<FileInfo, FileInfo> getMoMTPair()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = new Dictionary<FileInfo, FileInfo>();
            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (ServiceTypes.Count > 0 && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                {
                    continue;
                }
                if (fileInfo.Momt == 1)
                {
                    FileInfo mtFile = MainModel.FileInfos.Find(delegate (FileInfo x) { return x.ID == fileInfo.EventCount; });
                    moMtPair[fileInfo] = mtFile;
                }
                else if (fileInfo.Momt == 2)
                {
                    FileInfo moFile = MainModel.FileInfos.Find(delegate (FileInfo x) { return x.ID == fileInfo.EventCount; });
                    if (moFile == null)
                    {
                        moMtPair[fileInfo] = null;
                    }
                }
            }

            return moMtPair;
        }

        EndToEndInfo info = null;

        public EndToEndInfo Copy(EndToEndInfo info)
        {
            EndToEndInfo info2 = new EndToEndInfo(info.MoFile, info.MtFile, info.BeginTime);
            Type t = typeof(EndToEndInfo);
            for (int i = 0; i < t.GetProperties().Length; i++)
            {
                t.GetProperties()[i].SetValue(info2, t.GetProperties()[i].GetValue(info, null), null);
            }
            return info2;
        }
        protected override void doStatWithQuery()
        {
            DTFileDataManager moFile = null;
            DTFileDataManager mtFile = null;
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file.MoMtFlag == 1)
                {
                    moFile = file;
                }
                else if (file.MoMtFlag == 2)
                {
                    mtFile = file;
                }
            }
            if (moFile != null)
            {
                try
                {
                    foreach (Message msg in moFile.Messages)
                    {
                        if (msg.ID == 1107689472)     //IMS_SIP_INVITE
                        {
                            if (info == null)
                            {
                                info = new EndToEndInfo(moFile, mtFile, msg.DateTime);
                            }
                            info.ImsSipInvite = msg;
                            if (mtFile != null)
                            {
                                bool isPaging = true;
                                foreach (Message mtmsg in mtFile.Messages)
                                {
                                    if (mtmsg.DateTime > msg.DateTime 
                                        && (mtmsg.DateTime - msg.DateTime).TotalSeconds <= hocondition.Imssipinvite2Paging
                                        && mtmsg.ID == msg.ID)
                                    {
                                            isPaging = false;
                                            break;
                                    }
                                }
                                if (isPaging)
                                {
                                    EndToEndInfo info1 = Copy(info);
                                    info1.BeginTime = msg.DateTime;
                                    info1.MessageNames = "MoIMS_SIP_INVITE => MtPaging";
                                    info1.MtFile = mtFile.GetFileInfo();
                                    info1.SN = resultList.Count + 1;
                                    resultList.Add(info1);
                                }
                            }
                        }
                        else if (msg.ID == 1097532102)   //Activate dedicated EPS bearer context accept
                        {
                            if (info == null)
                            {
                                info = new EndToEndInfo(moFile, mtFile, msg.DateTime);
                            }
                            info.ActivatededicatedEPSbearerAccept = msg;
                        }
                        else if (msg.ID == 1107427328)    //IMS_SIP_BYE
                        {
                            if (info == null)
                            {
                                info = new EndToEndInfo(moFile, mtFile, msg.DateTime);
                            }
                            info.ImsSipBye = msg;
                            if (mtFile != null)
                            {
                                foreach (DTData mtdata in mtFile.DTDatas)
                                {
                                    if (mtdata.DateTime >= msg.DateTime
                                        && mtdata is Message)
                                    {
                                            Message mtmsg = mtdata as Message;
                                            if (mtmsg.ID == msg.ID && (mtmsg.DateTime - msg.DateTime).TotalSeconds < hocondition.MoImssipbye2MtImssipbye)
                                            {
                                                EndToEndInfo info1 = Copy(info);
                                                info1.BeginTime = msg.DateTime;
                                                info1.MessageNames = "MoIMS_SIP_BYE => MTIMS_SIP_BYE";
                                                info1.MtFile = mtFile.GetFileInfo();
                                                info1.SN = resultList.Count + 1;
                                                resultList.Add(info1);
                                                break;
                                            }
                                    }
                                }
                            }
                        }
                        else if (msg.ID == 1107705956)    //IMS_SIP_INVITE->Trying(100)
                        {
                            if (info == null)
                            {
                                info = new EndToEndInfo(moFile, mtFile, msg.DateTime);
                            }
                            info.ImsSipInvite2Trying100 = msg;
                        }
                        else if (msg.ID == 1108344832)    //IMS_SIP_UPDATE
                        {
                            if (info == null)
                            {
                                info = new EndToEndInfo(moFile, mtFile, msg.DateTime);
                            }
                            info.ImsSipUpdate = msg;
                        }
                        if (info != null)
                        {
                            if (msg.ID == 1097532101 && info.ImsSipInvite != null)       //Activate dedicated EPS bearer context request
                            {
                                if ((msg.DateTime - info.ImsSipInvite.DateTime).TotalSeconds >= hocondition.Imssipinvite2Activatededicatedepsrequest)
                                {
                                    EndToEndInfo info1 = Copy(info);
                                    info1.MessageNames = "IMS_SIP_INVITE => Activate dedicated EPS bearer context request";
                                    info1.BeginTime = info1.ImsSipInvite.DateTime;
                                    info1.SN = resultList.Count + 1;
                                    resultList.Add(info1);
                                }
                            }
                            else if (msg.ID == 1097532109)         //Deactivate EPS bearer context request
                            {
                                    if (info.ActivatededicatedEPSbearerAccept != null
                                        && (msg.DateTime - info.ActivatededicatedEPSbearerAccept.DateTime).TotalSeconds <= hocondition.Activateddedicatedepsaccept2Deactivateepsrequest)
                                    {
                                        EndToEndInfo info1 = Copy(info);
                                        info1.MessageNames = "Activate dedicater EPS bearer context accept => Deactivate EPS bearer context request";
                                        info1.BeginTime = info1.ActivatededicatedEPSbearerAccept.DateTime;
                                        info1.SN = resultList.Count + 1;
                                        resultList.Add(info1);
                                    }
                                    if (info.ImsSipInvite2Trying100 != null
                                        && (msg.DateTime - info.ImsSipInvite2Trying100.DateTime).TotalSeconds >= hocondition.Imssipinvitetrying1002DErequest2ISIServiceunavailable503)
                                    {
                                        info.BeginTime = info.ImsSipInvite2Trying100.DateTime;
                                        info.MessageNames = "IMS_SIP_INVITE->Trying(100) => Deactivate EPS bearer context request";
                                        info.ISITry1002DEbcr = msg;
                                    }
                                info.ActivatededicatedEPSbearerAccept = null;
                            }
                            else if (msg.ID == 1097532102 && info.ImsSipInvite != null)         //Activate dedicated EPS bearer context accept
                            {
                                if ((msg.DateTime - info.ImsSipInvite.DateTime).TotalSeconds <= hocondition.Imssipinvite2Activatededicatedepsaccept)
                                {
                                    info.BeginTime = info.ImsSipInvite.DateTime;
                                    info.MessageNames = "IMS_SIP_INVITE => Activate dedicated EPS bearer context accept ";
                                    info.ISIAdEbca2183 = msg;
                                }
                                info.ImsSipInvite = null;
                            }
                            else if (msg.ID == 1107706039 && info.ISIAdEbca2183 != null)     //IMS_SIP_INVITE->Session_Progress(183)
                            {
                                if ((msg.DateTime - info.ISIAdEbca2183.DateTime).TotalSeconds >= hocondition.Activate2Sessionprogress183)
                                {
                                    EndToEndInfo info1 = Copy(info);
                                    StringBuilder sb = new StringBuilder(info1.MessageNames);
                                    sb.Append("=> IMS_SIP_INVITE->Session_Progress(183)");
                                    info1.MessageNames = sb.ToString();
                                    info1.SN = resultList.Count + 1;
                                    resultList.Add(info1);
                                }
                                info.ISIAdEbca2183 = null;
                            }
                            else if (msg.ID == 1107706359)     //IMS_SIP_INVITE->Service_Unavailable(503)
                            {
                                    if (info.ImsSipInvite2Trying100 != null
                                        && (msg.DateTime - info.ImsSipInvite2Trying100.DateTime).TotalSeconds <= hocondition.Imssipinvitetrying1002ISIServiceunavailable503)
                                    {
                                        EndToEndInfo info1 = Copy(info);
                                        info1.MessageNames = "IMS_SIP_INVITE->Trying(100) => IMS_SIP_INVITE->Service_Unavailable(503)";
                                        info1.BeginTime = info1.ImsSipInvite2Trying100.DateTime;
                                        info1.SN = resultList.Count + 1;
                                        resultList.Add(info1);
                                        info.ImsSipInvite2Trying100 = null;
                                    }
                                if (info.ISITry1002DEbcr != null && info.ImsSipInvite2Trying100 != null)
                                {
                                    if ((msg.DateTime - info.ISITry1002DEbcr.DateTime).TotalSeconds < 1)
                                    {
                                        EndToEndInfo info1 = Copy(info);
                                        StringBuilder sb = new StringBuilder(info1.MessageNames);
                                        sb.Append(" => IMS_SIP_INVITE->Service_Unavailable(503)");
                                        info1.MessageNames = sb.ToString();
                                        info1.BeginTime = info1.ImsSipInvite2Trying100.DateTime;
                                        info1.SN = resultList.Count + 1;
                                        resultList.Add(info1);
                                    }
                                    info.ISITry1002DEbcr = null;
                                }
                                info.ImsSipInvite2Trying100 = null;
                            }
                            else if (msg.ID == 1108361716 && info.ImsSipUpdate != null)     //IMS_SIP_UPDATE->Server_Internal_Error(500)
                            {
                                if ((msg.DateTime - info.ImsSipUpdate.DateTime).TotalSeconds >= hocondition.Imssipupdate2ISUServerinternalerror5002Cancel)
                                {
                                    info.BeginTime = info.ImsSipUpdate.DateTime;
                                    info.MessageNames = "IMS_SIP_UPDATE => IMS_SIP_UPDATE->Server_Internal_Error(500)";
                                    info.ISU2SIE5002ISC = msg;
                                }
                                info.ImsSipUpdate = null;
                            }
                            else if (msg.ID == 1107492864 && info.ISU2SIE5002ISC != null)     //IMS_SIP_CANCEL
                            {
                                EndToEndInfo info1 = Copy(info);
                                StringBuilder sb = new StringBuilder(info1.MessageNames);
                                sb.Append(" => IMS_SIP_CANCEL");
                                info1.MessageNames = sb.ToString();
                                info1.SN = resultList.Count + 1;
                                resultList.Add(info1);
                                info.ISU2SIE5002ISC = null;
                            }
                        }
                    }
                }
                catch
                {
                    log.Debug("主叫关联过程出错");
                }
            }
            info = null;
            if (mtFile != null)
            {
                try
                {
                    foreach (Message msg in mtFile.Messages)
                    {
                        if (msg.ID == 1097533253)       //Detach request
                        {
                            if (info == null)
                            {
                                info = new EndToEndInfo(moFile, mtFile, msg.DateTime);
                            }
                            info.DetachRequest = msg;
                        }
                        else if (msg.ID == 1107706039)     //IMS_SIP_INVITE->Session_Progress(183)
                        {
                            if (info == null)
                            {
                                info = new EndToEndInfo(moFile, mtFile, msg.DateTime);
                            }
                            info.ImsSipInvite2SessionProgress183 = msg;
                        }
                        else if (msg.ID == 1107427328)      //IMS_SIP_BYE
                        {
                            if (info == null)
                            {
                                info = new EndToEndInfo(moFile, mtFile, msg.DateTime);
                            }
                            info.ImsSipBye = msg;
                        }

                        if (info != null)
                        {
                            bool isClear = true;
                            if (msg.ID == 1097533254 && info.DetachRequest != null)      //Detach accept
                            {
                                isClear = false;
                                if ((msg.DateTime - info.DetachRequest.DateTime).TotalSeconds < hocondition.Detachrequest2DetachAccept)
                                {
                                    EndToEndInfo info1 = Copy(info);
                                    info1.MessageNames = "Detach request => Detach accept";
                                    info1.BeginTime = info1.DetachRequest.DateTime;
                                    info1.SN = resultList.Count + 1;
                                    resultList.Add(info1);
                                    isClear = true;
                                }
                                info.DetachRequest = null;
                            }
                            else if (msg.ID == 1107492864 && info.ImsSipInvite2SessionProgress183 != null)      //IMS_SIP_CANCEL
                            {
                                isClear = false;
                                if ((msg.DateTime - info.ImsSipInvite2SessionProgress183.DateTime).TotalSeconds >= hocondition.ISISesseionprogress1832ISCancel)
                                {
                                    EndToEndInfo info1 = Copy(info);
                                    info1.MessageNames = "IMS_SIP_INVITE->Session_Progress(183) => IMS_SIP_CANCEL";
                                    info1.BeginTime = info1.ImsSipInvite2SessionProgress183.DateTime;
                                    info1.SN = resultList.Count + 1;
                                    resultList.Add(info1);
                                    isClear = true;
                                }
                                info.ImsSipInvite2SessionProgress183 = null;
                            }
                            else if (msg.ID == 1107427328 && info.ImsSipBye != null)       //IMS_SIP_BYE
                            {
                                if ((msg.DateTime - info.ImsSipBye.DateTime).TotalSeconds <= hocondition.Imssipbye2Requestterminated487)
                                {
                                    info.BeginTime = info.ImsSipBye.DateTime;
                                    info.MessageNames = "(上)IMS_SIP_BYE => (下)IMS_SIP_BYE";
                                    info.ISB = msg;
                                }
                                info.ImsSipBye = null;
                            }
                            else if (msg.ID == 1107443912 && info.ISB != null)      //IMS_SIP_BYE->OK(200)
                            {
                                if ((msg.DateTime - info.ISB.DateTime).TotalSeconds <= hocondition.Imssipbye2Requestterminated487)
                                {
                                    StringBuilder sb = new StringBuilder(info.MessageNames);
                                    sb.Append(" => IMS_SIP_BYE->OK(200)");
                                    info.MessageNames = sb.ToString();
                                    info.ISB2OK = msg;
                                }
                                info.ISB = null;
                            }
                            else if (msg.ID == 1107444199 && info.ISB2OK != null)       //IMS_SIP_BYE->Request_Terminated(487)
                            {
                                if ((msg.DateTime - info.ISB2OK.DateTime).TotalSeconds <= hocondition.Imssipbye2Requestterminated487)
                                {
                                    EndToEndInfo info1 = Copy(info);
                                    StringBuilder sb = new StringBuilder(info1.MessageNames);
                                    sb.Append(" => IMS_SIP_BYE->Request_Terminated(487)");
                                    info1.MessageNames = sb.ToString();
                                    info1.SN = resultList.Count + 1;
                                    resultList.Add(info1);

                                }
                                info.ISB2OK = null;
                            }
                            if (!isClear)
                            {
                                info = null;
                            }
                        }
                    }
                }
                catch
                {
                    log.Debug("被叫关联过程出错");
                }
            }
        }
    }

    public class EndToEndQuery_FDD : EndToEndQuery
    {
        private static EndToEndQuery_FDD instance = null;
        public static new EndToEndQuery_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new EndToEndQuery_FDD();
                    }
                }
            }
            return instance;
        }
        protected EndToEndQuery_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
            carrierID = CarrierType.ChinaUnicom;
        }
        public override string Name
        {
            get
            {
                return "VOLTE_FDD端对端问题分析(按区域)";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 30000, 30037, this.Name);
        }
    }

    public class EndToEndInfo
    {
        public EndToEndInfo(DTFileDataManager moFile, DTFileDataManager mtFile,DateTime dt)
        {
            if (moFile != null)
            {
                this.MoFile = moFile.GetFileInfo();
            }
            if (mtFile != null)
            {
                this.MtFile = mtFile.GetFileInfo();
            }
            this.BeginTime = dt;
        }

        public EndToEndInfo(FileInfo moFile, FileInfo mtFile, DateTime dt)
        {
            if (moFile != null)
            {
                this.MoFile = moFile;
            }
            if (mtFile != null)
            {
                this.MtFile = mtFile;
            }
            this.BeginTime = dt;
        }

        public int SN { get; set; }
        public FileInfo MoFile { get; set; }
        public FileInfo MtFile { get; set; }
        public DateTime BeginTime { get; set; }
        public Message ImsSipInvite { get; set; }
        public Message ISIAdEbca2183 { get; set; }
        public Message ActivatededicatedEPSbearerAccept { get; set; }
        public Message DetachRequest { get; set; }
        public Message ImsSipInvite2SessionProgress183 { get; set; }
        public Message ImsSipBye { get; set; }
        public Message ImsSipInvite2Trying100 { get; set; }
        public Message ImsSipUpdate { get; set; }
        public Message ISITry1002DEbcr { get; set; }
        public Message ISU2SIE5002ISC { get; set; }
        public Message ISB { get; set; }
        public Message ISB2OK { get; set; }
        public string MessageNames { get; set; }
    }

    public class EndToEndCondition
    {
        public int Imssipinvite2Activatededicatedepsrequest { get; set; }
        public int Activateddedicatedepsaccept2Deactivateepsrequest { get; set; }
        public int Imssipinvite2Activatededicatedepsaccept { get; set; }
        public int Activate2Sessionprogress183 { get; set; }
        public int Detachrequest2DetachAccept { get; set; }
        public int MoImssipbye2MtImssipbye { get; set; }
        public int ISISesseionprogress1832ISCancel { get; set; }
        public int Imssipbye2Requestterminated487 { get; set; }
        public int Imssipinvitetrying1002ISIServiceunavailable503 { get; set; }
        public int Imssipinvitetrying1002DErequest2ISIServiceunavailable503 { get; set; }
        public int Imssipinvite2Paging { get; set; }
        public int Imssipupdate2ISUServerinternalerror5002Cancel { get; set; }
        public EndToEndCondition()
        {
            Imssipinvite2Activatededicatedepsrequest = 5;
            Activateddedicatedepsaccept2Deactivateepsrequest = 3;
            Imssipinvite2Activatededicatedepsaccept = 3;
            Activate2Sessionprogress183 = 10;
            Detachrequest2DetachAccept = 0;
            MoImssipbye2MtImssipbye = 1;
            ISISesseionprogress1832ISCancel = 4;
            Imssipbye2Requestterminated487 = 1;
            Imssipinvitetrying1002ISIServiceunavailable503 = 1;
            Imssipinvite2Paging = 5;
            Imssipupdate2ISUServerinternalerror5002Cancel = 6;
        }
    }
}
