﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using DevExpress.XtraCharts;
using DevExpress.Utils;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Util;
using DevExpress.XtraEditors.Controls;
using MasterCom.RAMS.NewBlackBlock;

namespace MasterCom.RAMS.Func.PopShow
{
    public partial class NewBlackBlockInfoPanel_ng : UserControl, PopShowPanelInterface
    {
        public const byte REQTYPE_TABLE_DATA_INFO = 0x83; //REQUEST 
        public const byte RESTYPE_TABLE_DATA_INFO = 0x83;

        ToolTipController toolTipController = new ToolTipController();
        private MainModel MainModel;
        private WelcomForm mWelcomForm;
        private Dictionary<string, BlackBlockToken> tokenDic = new Dictionary<string, BlackBlockToken>();

        public NewBlackBlockInfoPanel_ng()
        {
            InitializeComponent();
            initShowInfo();
        }

        private void initShowInfo()
        {
            DIYSQLNewBlackBlockToken query = new DIYSQLNewBlackBlockToken(MainModel.GetInstance());
            query.Query();
            tokenDic = query.GetTokenDic();

            foreach (string token in tokenDic.Keys)
            {
                cbxTypeSelBB.Items.Add(tokenDic[token].Name);
            }
            if (cbxTypeSelBB.Items.Count > 0)
            {
                cbxTypeSelBB.SelectedIndex = 0;
            }

            cbxShowType.Items.Add("当前情况");
            cbxShowType.Items.Add("最近一周");
            cbxShowType.Items.Add("最近一月");
            cbxShowType.Items.Add("按周");
            cbxShowType.Items.Add("按月");
            cbxShowType.Items.Add("按天");
            cbxShowType.SelectedIndex = 0;

            toolStripDropDownCompBench.Items.Clear();
            toolStripDropDownCompBench.Items.Add(new ToolStripControlHost(compBenchControl));
            
            curStartTime = DateTime.Now.AddMonths(-1);
            curEndTime = DateTime.Now;
            dateStart.Value = curStartTime;
            dateEnd.Value = curEndTime;

            string[] cityNames = DistrictManager.GetInstance().DistrictNames;
            foreach (string cityName in cityNames)
            {
                if (cityName != "")
                {
                    curCityNames.Add(cityName);
                }
            }

            curProjectIDs.Clear();
            CategoryEnumItem[] projItems = ((CategoryEnum)CategoryManager.GetInstance()["Project"]).Items;
            foreach (CategoryEnumItem item in projItems)
            {
                curProjectIDs.Add(item.ID);
            }

        }
        List<PopESEvent> popEventsAll = new List<PopESEvent>();
        public void RunQuery(BackgroundWorker worker, TaskInfo task)
        {
            isProvUser = MainModel.User.DBID == -1;
            //===道路黑点
            List<NewBlockEntryItem> blockList = queryBlackBlockEntryFrom(worker, MainModel.User.DBID);
            Dictionary<string, List<NewBlockEntryItem>> blockTypeDic = new Dictionary<string, List<NewBlockEntryItem>>();

            foreach (object item in cbxTypeSelBB.Items)
            {
                blockTypeDic[item.ToString()] = new List<NewBlockEntryItem>();//gsm

            }
            foreach (NewBlockEntryItem block in blockList)
            {
                string key = tokenDic[block.type].Name;

                List<NewBlockEntryItem> typeList = null;
                if (blockTypeDic.TryGetValue(key, out typeList))
                {
                    typeList.Add(block);
                }
            }
            //Block type -> cityDic
            Dictionary<string, Dictionary<string, CityBlackBlockInfo>> blockTypeCityBlockInfoDic = new Dictionary<string, Dictionary<string, CityBlackBlockInfo>>();
            foreach (string typeKey in blockTypeDic.Keys)
            {
                Dictionary<string, CityBlackBlockInfo> blockCitysDic = buildCityStructFrom_BlackBlock(blockTypeDic[typeKey]);
                blockTypeCityBlockInfoDic[typeKey] = blockCitysDic;
            }
            BlockResultForShow ret = new BlockResultForShow();
            ret.blockTypeCityBlockInfoDic = blockTypeCityBlockInfoDic;
            //====异常事件点
            popEventsAll = queryPopESEventsFrom(worker, MainModel.User.DBID);

            Dictionary<int, bool> inNeedProjIds = parseNeededProjDic(popEventsAll);
            CategoryEnumItem[] projItems = ((CategoryEnum)CategoryManager.GetInstance()["Project"]).Items;
            for (int x = 0; x < projItems.Length; x++)
            {
                if (inNeedProjIds.ContainsKey(projItems[x].ID))
                {
                    CategoryEnumItem itm = projItems[x];
                    projESList.Add(new IDNamePair(itm.ID, itm.Name));
                }

            }
#if Guangdong
            cityESList.Add("广州");
            cityESList.Add("深圳");
            cityESList.Add("东莞");
            cityESList.Add("佛山");
            cityESList.Add("汕头");
            cityESList.Add("珠海");
            cityESList.Add("中山");
            cityESList.Add("惠州");
            cityESList.Add("江门");
            cityESList.Add("湛江");
            cityESList.Add("茂名");
            cityESList.Add("清远");
            cityESList.Add("肇庆");
            cityESList.Add("揭阳");
            cityESList.Add("韶关");
            cityESList.Add("潮州");
            cityESList.Add("阳江");
            cityESList.Add("梅州");
            cityESList.Add("河源");
            cityESList.Add("汕尾");
            cityESList.Add("云浮");
#else
            Dictionary<string, bool> inNeedCityNames = parseNeededCityDic(popEventsAll);
            string[] cityNames = DistrictManager.GetInstance().DistrictNames;
            for (int x = 0; x < cityNames.Length; x++)
            {
                if (inNeedCityNames.ContainsKey(cityNames[x]))
                {
                    cityESList.Add(cityNames[x]);
                }
            }
#endif

            List<PopESEvent> popEvents = filterESFromAll(popEventsAll);
            Dictionary<string, ESCityInfo> esCityDic = buildESCityStructFrom_ES(popEvents);
            ret.esCityInfoDic = esCityDic;

#if PopShow_CompBench
            //====竞争对比
            List<CompBenchStatusEntryItem> compBenchStatusItemList = queryCompBenchStatusFrom(worker, MainModel.User.DBID);
            List<CompBenchUnitEntryItem> compBenchUnitItemList = queryCompBenchUnitFrom(worker, MainModel.User.DBID);
            CompBenchCityInfo compBenchCityInfo = new CompBenchCityInfo();
            foreach (CompBenchStatusEntryItem cbsei in compBenchStatusItemList)
            {
                compBenchCityInfo.AddCompBenchStatus(cbsei);
            }
            foreach (CompBenchUnitEntryItem cbuei in compBenchUnitItemList)
            {
                compBenchCityInfo.AddCompBenchUnit(cbuei);
            }
            ret.compBenchCityInfo = compBenchCityInfo;
#else
#if Guangdong
            tabMain.TabPages.Remove(tabPage1);   
#endif
            tabMain.TabPages.Remove(tabPageCompBench);
#endif

            //======
            task.retResultInfo = ret;
        }

        private Dictionary<int, bool> parseNeededProjDic(List<PopESEvent> popEventsAll)
        {
            Dictionary<int, bool> rdic = new Dictionary<int, bool>();
            foreach (PopESEvent evt in popEventsAll)
            {
                rdic[evt.iprojecttype] = true;
            }
            return rdic;
        }

        private Dictionary<string, bool> parseNeededCityDic(List<PopESEvent> popEventsAll)
        {
            Dictionary<string, bool> rdic = new Dictionary<string, bool>();
            foreach (PopESEvent evt in popEventsAll)
            {
                string name = DistrictManager.GetInstance().getDistrictName(evt.dbid);
                if (name != null && name != "" && name != "未知")
                {
                    rdic[name] = true;
                }
            }
            return rdic;
        }

        List<IDNamePair> projESList = new List<IDNamePair>();
        DateTime curStartTime = DateTime.Now.AddMonths(-1);
        DateTime curEndTime = DateTime.Now;

        List<int> curProjectIDs = new List<int>();//已选择的项目ID

        List<string> cityESList = new List<string>();
        List<string> curCityNames = new List<string>();//已选择的地市名称

        string curShowType = "";
        List<string> curSelDates = new List<string>();//在KPIInfoPanel_ng_total已选择的日期列表，用于按天、按周、按月

        private List<PopESEvent> filterESFromAll(List<PopESEvent> popEventsAll)
        {
            int stimeValue = (int)(JavaDate.GetMilliseconds(curStartTime) / 1000L);
            int endValue = (int)(JavaDate.GetMilliseconds(curEndTime) / 1000L);
            List<PopESEvent> newList = new List<PopESEvent>();
            foreach (PopESEvent evt in popEventsAll)
            {
                if (curProjectIDs.Contains(evt.iprojecttype))
                {
                    string cityName = DistrictManager.GetInstance().getDistrictName(evt.dbid);
                    if (curCityNames.Contains(cityName) && evt.itime >= stimeValue && evt.itime < endValue)
                    {
                        addPopESEventList(newList, evt);
                    }
                }
            }
            return newList;
        }

        private void addPopESEventList(List<PopESEvent> newList, PopESEvent evt)
        {
            if (curShowType == "按天")
            {
                string dateStr = getDayStr(evt.itime);
                if (curSelDates.Contains(dateStr))
                {
                    newList.Add(evt);
                }
            }
            else if (curShowType == "按周")
            {
                string dateStr = getWeekStr(evt.itime);
                if (curSelDates.Contains(dateStr))
                {
                    newList.Add(evt);
                }
            }
            else if (curShowType == "按月")
            {
                string dateStr = getMonthStr(evt.itime);
                if (curSelDates.Contains(dateStr))
                {
                    newList.Add(evt);
                }
            }
            else
            {
                newList.Add(evt);
            }
        }

        private string getWeekStr(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DayOfWeek dweek = dt.DayOfWeek;
            int dayToMondy = 0;
            switch (dweek)
            {
                case DayOfWeek.Monday:
                    dayToMondy = 0;
                    break;
                case DayOfWeek.Tuesday:
                    dayToMondy = 1;
                    break;
                case DayOfWeek.Wednesday:
                    dayToMondy = 2;
                    break;
                case DayOfWeek.Thursday:
                    dayToMondy = 3;
                    break;
                case DayOfWeek.Friday:
                    dayToMondy = 4;
                    break;
                case DayOfWeek.Saturday:
                    dayToMondy = 5;
                    break;
                case DayOfWeek.Sunday:
                    dayToMondy = 6;
                    break;

            }
            return dt.AddDays(-dayToMondy).ToString("yyyy.MM.dd_") + dt.AddDays(6 - dayToMondy).ToString("MM.dd");
        }

        private string getMonthStr(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            return dt.ToString("yyyy-MM");
        }

        private string getDayStr(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            return dt.ToString("yyyy.MM.dd");
        }


        private void btnESFresh_Click(object sender, EventArgs e)
        {
            curProjectIDs.Clear();
            curStartTime = dateStart.Value.Date;
            curEndTime = dateEnd.Value.AddDays(1).Date;
            foreach (CheckedListBoxItem item in cbxProjFilter.Properties.Items)
            {
                if (item.CheckState == CheckState.Checked)
                {
                    IDNamePair proj = item.Value as IDNamePair;
                    if (proj != null)
                    {
                        curProjectIDs.Add(proj.id);
                    }
                }
            }

            curCityNames.Clear();
            foreach (CheckedListBoxItem item in cbxCityName.Properties.Items)
            {
                if (item.CheckState == CheckState.Checked)
                {
                    curCityNames.Add(item.Value.ToString());
                }
            }

            List<PopESEvent> popEvents = filterESFromAll(popEventsAll);
            Dictionary<string, ESCityInfo> esCityDic = buildESCityStructFrom_ES(popEvents);
            this.curESAllCitysDic = esCityDic;
            PrepareForDicShow_ES();
            refreshShowReport_ES();

            curShowType = "";
            curSelDates.Clear();
        }

        public void freshDateAndProject(DateTime startTime, DateTime endTime, List<int> projectIDs, List<string> cityNames, string showType, List<string> dateStrs) //刷新时间,项目id列表,地市列表,时间显示方式,已选时间段,给KPIInfoPanel_ng_total调用
        {
            curShowType = showType;
            curSelDates = dateStrs;

            this.dateStart.Value = startTime;
            this.dateEnd.Value = endTime;

            foreach (CheckedListBoxItem item in cbxProjFilter.Properties.Items)
            {
                item.CheckState = CheckState.Unchecked;
            }
            foreach (CheckedListBoxItem item in cbxProjFilter.Properties.Items)
            {
                IDNamePair proj = item.Value as IDNamePair;
                foreach (int projID in projectIDs)
                {
                    if (proj.id == projID)
                    {
                        item.CheckState = CheckState.Checked;
                        break;
                    }
                }
            }

            foreach (CheckedListBoxItem item in cbxCityName.Properties.Items)
            {
                item.CheckState = CheckState.Unchecked;
            }
            foreach (CheckedListBoxItem item in cbxCityName.Properties.Items)
            {
                string name = item.Value.ToString();
                if (cityNames.Contains(name))
                {
                    item.CheckState = CheckState.Checked;
                }
            }


            if (cbxProjFilter.Text != null || cbxProjFilter.Text != "")
            {
                this.btnESFresh.PerformClick();
            }
        }

        private Dictionary<string, ESCityInfo> buildESCityStructFrom_ES(List<PopESEvent> popEvents)
        {
            Dictionary<string, ESCityInfo> cityDicRet = new Dictionary<string, ESCityInfo>();
            foreach (PopESEvent esEvt in popEvents)
            {
                string cityName = DistrictManager.GetInstance().getDistrictName(esEvt.dbid);
                ESCityInfo cityInfo = null;
                if (!cityDicRet.TryGetValue(cityName, out cityInfo))
                {
                    cityInfo = new ESCityInfo();
                    cityInfo.cityName = cityName;
                    cityDicRet[cityName] = cityInfo;
                }
                cityInfo.AddPopEsEvent(esEvt);
            }
#if Guangdong
            Dictionary<string, ESCityInfo> citySeqDicRet = new Dictionary<string, ESCityInfo>();   //按地市优先顺序  
            foreach (string cityName in cityESList)
            {
                if (cityDicRet.ContainsKey(cityName))
                {
                    citySeqDicRet.Add(cityName, cityDicRet[cityName]);
                }
            }
            return citySeqDicRet;
#else
            return cityDicRet;
#endif

        }

        private Dictionary<string, CityBlackBlockInfo> buildCityStructFrom_BlackBlock(List<NewBlockEntryItem> blockEntryList)
        {
            Dictionary<string, CityBlackBlockInfo> cityDic = new Dictionary<string, CityBlackBlockInfo>();
            foreach (NewBlockEntryItem bbEntry in blockEntryList)
            {
                string cityName = DistrictManager.GetInstance().getDistrictName(bbEntry.dbid);
                CityBlackBlockInfo cityBlockInfo = null;
                if (!cityDic.TryGetValue(cityName, out cityBlockInfo))
                {
                    cityBlockInfo = new CityBlackBlockInfo();
                    cityBlockInfo.cityName = cityName;
                    cityDic[cityName] = cityBlockInfo;
                }
                List<NewBlockEntryItem> blockList;
                if (!cityBlockInfo.areaBlocksDic.TryGetValue(bbEntry.area_names, out blockList))
                {
                    blockList = new List<NewBlockEntryItem>();
                    cityBlockInfo.areaBlocksDic[bbEntry.area_names] = blockList;
                }
                blockList.Add(bbEntry);
            }
            return cityDic;
        }

        private List<PopESEvent> queryPopESEventsFrom(BackgroundWorker worker, int dbid)
        {
            List<PopESEvent> retList = new List<PopESEvent>();
            ClientProxy clientProxy = new ClientProxy();

            string username = MainModel.User.LoginName;
            string password = MainModel.User.Password;
            int districtID = MainModel.DistrictID;
#if Guangdong
            //if (districtID == 14 || districtID == 15 || districtID == 22)
            {
                username = MainModel.MainDbUser.LoginName;
                password = MainModel.MainDbUser.Password;
                districtID = 2;
            }
#endif
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, username, password, districtID) != ConnectResult.Success)
            {
                worker.ReportProgress(99, "连接服务器端出错！");
                return retList;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.InfoQuery;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_TABLE_DATA_INFO;
                package.Content.PrepareAddParam();
                package.Content.AddParam(dbid);
                package.Content.AddParam("tb_pop_esinsight");
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_TABLE_DATA_INFO)
                    {
                        package.Content.PrepareGetParam();
                        PopESEvent retItem = PopESEvent.ReadResultItemFrom(package.Content);
                        retList.Add(retItem);
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                }
                return retList;
            }

            finally
            {
                clientProxy.Close();
            }
        }
        private List<NewBlockEntryItem> queryBlackBlockEntryFrom(BackgroundWorker worker, int dbid)
        {
            List<NewBlockEntryItem> retList = new List<NewBlockEntryItem>();
            ClientProxy clientProxy = new ClientProxy();

            string username = MainModel.User.LoginName;
            string password = MainModel.User.Password;
            int districtID = MainModel.DistrictID;
#if Guangdong
            //if (districtID == 14 || districtID == 15 || districtID == 22)
            {
                username = MainModel.MainDbUser.LoginName;
                password = MainModel.MainDbUser.Password;
                districtID = 2;
            }
#endif
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, username, password, districtID) != ConnectResult.Success)
            {
                worker.ReportProgress(99, "连接服务器端出错！");
                return retList;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.InfoQuery;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_TABLE_DATA_INFO;
                package.Content.PrepareAddParam();
                package.Content.AddParam(dbid);
                package.Content.AddParam("tb_popblackblock");
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_TABLE_DATA_INFO)
                    {
                        package.Content.PrepareGetParam();
                        NewBlockEntryItem retItem = NewBlockEntryItem.ReadResultItemFrom(package.Content);
                        List<NewBlockEntryItem> retItemSplitList = splitByAreaName(retItem);
                        retList.AddRange(retItemSplitList);
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                }
                return retList;
            }

            finally
            {
                clientProxy.Close();
            }
        }

        private List<NewBlockEntryItem> splitByAreaName(NewBlockEntryItem retItem)
        {
            List<NewBlockEntryItem> retList = new List<NewBlockEntryItem>();
            string[] names = retItem.area_names.Split('|');
            for (int i = 0; i < names.Length; i++)
            {
                if (names[i] != "" && !names[i].Contains("中心点"))
                {
                    NewBlockEntryItem splitItem = retItem.cloneInstance();
                    splitItem.area_names = names[i];
                    retList.Add(splitItem);
                }
            }
            return retList;
        }

        public void setInitCondition(int beginDateTime, int endDateTime, List<int> selectedProjects, List<string> selectedCityNames, string showType, List<string> dateStrs)
        {
            curCityNames = new List<string>(selectedCityNames);
            curProjectIDs = new List<int>(selectedProjects);
            curStartTime = JavaDate.GetDateTimeFromMilliseconds(beginDateTime * 1000L);
            curEndTime = JavaDate.GetDateTimeFromMilliseconds(endDateTime * 1000L);
            curShowType = showType;
            curSelDates = dateStrs;
        }

        Dictionary<string, Dictionary<string, CityBlackBlockInfo>> curRetBlackBlockDataDic = new Dictionary<string, Dictionary<string, CityBlackBlockInfo>>();
        public void FireFreshShowData(TaskInfo task)
        {

            if (!(task.retResultInfo is BlockResultForShow))
            {
                curRetBlackBlockDataDic.Clear();
            }
            else
            {
                curRetBlackBlockDataDic = (task.retResultInfo as BlockResultForShow).blockTypeCityBlockInfoDic;
                curESAllCitysDic = (task.retResultInfo as BlockResultForShow).esCityInfoDic;
                curCompBenchCityInfo = (task.retResultInfo as BlockResultForShow).compBenchCityInfo;
            }
            freshESAvailableProjects();
            freshESAvailableCities();
            cbxShowType.SelectedIndex = 0;
            PrepareForDicShow_BlackBlock();
            refreshShowReport_BlackBlock();
            PrepareForDicShow_ES();
            refreshShowReport_ES();

#if PopShow_CompBench
            PrepareForDicShow_CompBench();
            refreshShowReport_CompBench();
#endif

        }

        private void freshESAvailableProjects()
        {
            cbxProjFilter.Properties.Items.Clear();
            foreach (IDNamePair namePair in projESList)
            {
                if (curProjectIDs.Contains(namePair.id))
                {
                    cbxProjFilter.Properties.Items.Add(namePair, true);
                }
                else
                {
                    cbxProjFilter.Properties.Items.Add(namePair, false);
                }
            }
        }

        private void freshESAvailableCities()
        {
            if (MainModel.User.DBID == -1)
            {
                cbxCityName.Properties.Items.Clear();
                foreach (string name in cityESList)
                {
                    if (curCityNames.Contains(name))
                    {
                        cbxCityName.Properties.Items.Add(name, true);
                    }
                    else
                    {
                        cbxCityName.Properties.Items.Add(name, false);
                    }
                }
            }
            else
            {
                string cityName = DistrictManager.GetInstance().getDistrictName(MainModel.User.DBID);
                cbxCityName.Properties.Items.Clear();
                cbxCityName.Properties.Items.Add(cityName, true);
            }
        }

        public void SetMainModal(MasterCom.RAMS.Model.MainModel mm, WelcomForm welcomform)
        {
            this.MainModel = mm;
            this.mWelcomForm = welcomform;

        }

        private void cbxTypeSel_SelectedIndexChanged(object sender, EventArgs e)
        {
            PrepareForDicShow_BlackBlock();
            refreshShowReport_BlackBlock();
        }

        private void cbxShowType_SelectedIndexChanged(object sender, EventArgs e)
        {
            refreshShowReport_BlackBlock();
        }

        private void cbxContentType_SelectedIndexChanged(object sender, EventArgs e)
        {
            refreshShowReport_BlackBlock();
        }
        /// <summary>
        /// 是否当前登录的是省用户/多地市用户
        /// </summary>
        private bool isProvUser = false;
        //==================================================BlackBlock
        /// <summary>
        /// 若为省用户则可能为0（查看多地市分布情况）  1 (看单地市情况)     若为地市用户只可能为1（查看地市情况）
        /// </summary>
        private int curGraphShowLevel_BlackBlock = 1;
        /// <summary>
        /// 当前所选中显示的地市情况
        /// </summary>
        private CityBlackBlockInfo curShowCityInfo_BlackBlock = null;
        /// <summary>
        /// 当前多地市情况
        /// </summary>
        private Dictionary<string, CityBlackBlockInfo> curShowMultiCityInfo_BlackBlock = null;
        //==============end
        //==================================================ES
        /// <summary>
        /// 若为省用户则可能为0：查看多地市分布情况  1 ：查看网格地区  2：查看各网格问题类型Task   3：查看任务类型的各个问题分支Branch   
        /// 若为地市用户只可能为 1，2，3，4
        /// </summary>
        private int curGraphShowLevel_ES = 1;
        /// <summary>
        /// 当前所选中地市 level=1所指
        /// </summary>
        private ESCityInfo curESCity = null;
        /// <summary>
        /// 当前所选中网格 level=2所指
        /// </summary>
        private ESAreaInfo curESArea = null;
        /// <summary>
        /// 当前所选中任务Task level=3所指
        /// </summary>
        private ESTaskInfo curESTask = null;
        /// <summary>
        /// 多地市情况
        /// </summary>
        private Dictionary<string, ESCityInfo> curESAllCitysDic = null;
        //==============end


        //=================================================CompBench
        /// <summary>
        /// 用于欢迎界面，竞对
        /// <returns>返回结果</returns>
        private CompBenchCityInfo curCompBenchCityInfo = null;

        private int curGraphShowLevel_CompBench = 1;
        /// <summary>
        /// 当前所选中地市 level=1所指
        /// </summary>
        private CompBenchTimePeriodInfo curCompBenchCity = null;
        /// <summary>
        /// 当前所选中网格 level=2所指
        /// </summary>
        private CompBenchTaskInfo curCompBenchTaskInfo = null;
        /// <summary>
        /// 当前所选中任务Task level=3所指
        /// </summary>
        private CompBenchBranchInfo curCompBenchBranchInfo = null;


        //==============end


        private void PrepareForDicShow_BlackBlock()
        {
            string seltype = cbxTypeSelBB.SelectedItem as string;
            if (seltype == null)
            {
                return;
            }
            Dictionary<string, CityBlackBlockInfo> multiCity;
            if (curRetBlackBlockDataDic.TryGetValue(seltype, out multiCity))
            {
                curShowMultiCityInfo_BlackBlock = multiCity;
                if (isProvUser)
                {
                    curGraphShowLevel_BlackBlock = 0;//default
                }
                else
                {
                    setBlackBlock();
                }
            }
        }

        private void setBlackBlock()
        {
            if (curShowMultiCityInfo_BlackBlock.Values.Count > 0)
            {
                curGraphShowLevel_BlackBlock = 1;
                foreach (CityBlackBlockInfo cityInfo in curShowMultiCityInfo_BlackBlock.Values)
                {
                    curShowCityInfo_BlackBlock = cityInfo;
                    if (true)
                    {
                        break;
                    }
                }
            }
            else
            {
                curShowCityInfo_BlackBlock = new CityBlackBlockInfo();
            }
        }

        private void PrepareForDicShow_ES()
        {
            if (isProvUser)
            {
                curGraphShowLevel_ES = 0;//default
            }
            else
            {
                if (curESAllCitysDic.Count > 0)
                {
                    curGraphShowLevel_ES = 1;
                    foreach (ESCityInfo cityInfo in curESAllCitysDic.Values)
                    {
                        curESCity = cityInfo;
                        if (true)
                        {
                            break;
                        }
                    }
                }
                else
                {
                    curESCity = new ESCityInfo();
                }
            }
        }

        private void refreshShowReport_ES()
        {
            if (curGraphShowLevel_ES == 0)
            {
                refreshShowLevel0();
            }
            else if (curGraphShowLevel_ES == 1)
            {
                if (curESCity == null)
                {
                    return;
                }
                refreshShowLevel1();
            }
            else if (curGraphShowLevel_ES == 2)
            {
                if (curESArea == null)
                {
                    return;
                }
                refreshShowLevel2();
            }
            else if (curGraphShowLevel_ES == 3)
            {
                if (curESTask == null)
                {
                    return;
                }
                refreshShowLevel3();
            }
        }

        private void refreshShowLevel0()
        {
            //==========chart
            chartControlNProblem.Series.Clear();
            chartControlNProblem.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = "全省异常事件情况";
            chartControlNProblem.Titles.Add(title);
            Series serial = new Series("全省异常事件情况", ViewType.Pie);
            ((PieSeriesView)serial.View).ExplodeMode = PieExplodeMode.All;
            ((PiePointOptions)serial.PointOptions).PointView = PointView.ArgumentAndValues;
            ((PiePointOptions)serial.PointOptions).PercentOptions.ValueAsPercent = false;
            ((PiePointOptions)serial.PointOptions).ValueNumericOptions.Format = NumericFormat.General;
            DataTable datInfo = new DataTable();
            datInfo.Columns.Add("地市", typeof(String));
            datInfo.Columns.Add("异常事件数量", typeof(Int32));
            foreach (ESCityInfo cityInfo in curESAllCitysDic.Values)
            {
                string cityName = cityInfo.cityName;
                int bbNum = cityInfo.CalcCurrentProblemNum();
                datInfo.Rows.Add(new object[] { cityName, bbNum });
            }
            serial.ArgumentDataMember = "地市";
            serial.ValueScaleType = ScaleType.Numerical;
            serial.ValueDataMembers.AddRange(new string[] { "异常事件数量" });
            serial.PointOptions.PointView = DevExpress.XtraCharts.PointView.ArgumentAndValues;
            //serial.PointOptions.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            serial.DataSource = datInfo;
            chartControlNProblem.Series.Add(serial);
            //=======grid===
            dataGridViewProblemNumInfo.Visible = true;
            spliterNProblemDetail.Visible = false;
            dataGridViewProblemNumInfo.Rows.Clear();
            dataGridViewProblemNumInfo.Columns.Clear();
            //prepare for问题类型用以构建列
            Dictionary<string, int> taskProblemCount = new Dictionary<string, int>();//问题点类型（task）->事件数量
            foreach (ESCityInfo cityInfo in curESAllCitysDic.Values)
            {
                Dictionary<string, int> taskProblemCountCity = cityInfo.CalcProblemCountByTask();
                foreach (string taskname in taskProblemCountCity.Keys)
                {
                    int ct = 0;
                    if (!taskProblemCount.TryGetValue(taskname, out ct))
                    {
                        taskProblemCount[taskname] = taskProblemCountCity[taskname];
                    }
                    else
                    {
                        taskProblemCount[taskname] = taskProblemCountCity[taskname] + ct;
                    }
                }
            }
            Dictionary<string, int> taskNameAtColumnIndexDic = new Dictionary<string, int>();
            dataGridViewProblemNumInfo.Columns.Add("ProbNumColumn0", "地市");
            dataGridViewProblemNumInfo.Columns.Add("ProbNumColumn1", "总异常事件数");
            int cidx = 2;
            foreach (string taskname in taskProblemCount.Keys)
            {
                dataGridViewProblemNumInfo.Columns.Add("ProbNumColumn" + cidx, taskname);
                taskNameAtColumnIndexDic[taskname] = cidx;
                cidx++;
            }
            int indexRowAt = 0;
            foreach (ESCityInfo cityInfo in curESAllCitysDic.Values)
            {
                int bbNum = cityInfo.CalcCurrentProblemNum();
                Dictionary<string, int> taskProblemCountCity = cityInfo.CalcProblemCountByTask();
                dataGridViewProblemNumInfo.Rows.Add(1);
                dataGridViewProblemNumInfo.Rows[indexRowAt].Cells[0].Value = cityInfo.cityName;
                dataGridViewProblemNumInfo.Rows[indexRowAt].Cells[1].Value = bbNum;
                foreach (string tname in taskProblemCountCity.Keys)
                {
                    dataGridViewProblemNumInfo.Rows[indexRowAt].Cells[taskNameAtColumnIndexDic[tname]].Value = taskProblemCountCity[tname];
                }
                indexRowAt++;
            }
        }

        private void refreshShowLevel1()
        {
            //==========chart
            chartControlNProblem.Series.Clear();
            chartControlNProblem.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = curESCity.cityName + "异常事件情况";
            chartControlNProblem.Titles.Add(title);
            Series series1 = new Series("异常事件数量", ViewType.Bar);
            foreach (ESAreaInfo areaInfo in curESCity.areaOfCityDic.Values)
            {
                int blockNum = areaInfo.CalcCurrentProblemNum();
                series1.Points.Add(new SeriesPoint(areaInfo.areaName, new double[] { blockNum }));
            }
            series1.Visible = true;
            chartControlNProblem.Series.Add(series1);
            if (series1.Points.Count > 3)
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControlNProblem.Diagram)).AxisX.Label.Angle = 270;
            }
            else
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControlNProblem.Diagram)).AxisX.Label.Angle = 0;
            }
            //=======grid===
            dataGridViewProblemNumInfo.Visible = true;
            spliterNProblemDetail.Visible = false;
            dataGridViewProblemNumInfo.Rows.Clear();
            dataGridViewProblemNumInfo.Columns.Clear();
            //prepare for问题类型用以构建列
            Dictionary<string, int> taskProblemCount = new Dictionary<string, int>();//问题点类型（task）->事件数量
            foreach (ESAreaInfo areaInfo in curESCity.areaOfCityDic.Values)
            {
                Dictionary<string, int> taskProblemCountArea = areaInfo.CalcProblemCountByTask();
                foreach (string taskname in taskProblemCountArea.Keys)
                {
                    int ct = 0;
                    if (!taskProblemCount.TryGetValue(taskname, out ct))
                    {
                        taskProblemCount[taskname] = taskProblemCountArea[taskname];
                    }
                    else
                    {
                        taskProblemCount[taskname] = taskProblemCountArea[taskname] + ct;
                    }
                }
            }
            Dictionary<string, int> taskNameAtColumnIndexDic = new Dictionary<string, int>();
            dataGridViewProblemNumInfo.Columns.Add("ProbNumColumn0", "网格名");
            dataGridViewProblemNumInfo.Columns.Add("ProbNumColumn1", "总异常事件数");
            int cidx = 2;
            foreach (string taskname in taskProblemCount.Keys)
            {
                dataGridViewProblemNumInfo.Columns.Add("ProbNumColumn" + cidx, taskname);
                taskNameAtColumnIndexDic[taskname] = cidx;
                cidx++;
            }
            int indexRowAt = 0;
            foreach (ESAreaInfo areaInfo in curESCity.areaOfCityDic.Values)
            {
                int bbNum = areaInfo.CalcCurrentProblemNum();
                Dictionary<string, int> taskProblemCountArea = areaInfo.CalcProblemCountByTask();
                dataGridViewProblemNumInfo.Rows.Add(1);
                dataGridViewProblemNumInfo.Rows[indexRowAt].Cells[0].Value = areaInfo.areaName;
                dataGridViewProblemNumInfo.Rows[indexRowAt].Cells[1].Value = bbNum;
                foreach (string tname in taskProblemCountArea.Keys)
                {
                    dataGridViewProblemNumInfo.Rows[indexRowAt].Cells[taskNameAtColumnIndexDic[tname]].Value = taskProblemCountArea[tname];
                }
                indexRowAt++;
            }
        }

        private void refreshShowLevel2()
        {
            //==========chart
            chartControlNProblem.Series.Clear();
            chartControlNProblem.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = curESCity.cityName + " " + curESArea.areaName + "异常事件情况";
            chartControlNProblem.Titles.Add(title);
            Series series1 = new Series("异常事件数量", ViewType.Bar);
            foreach (ESTaskInfo taskInfo in curESArea.taskOfAreaDic.Values)
            {
                int blockNum = taskInfo.CalcCurrentProblemNum();
                series1.Points.Add(new SeriesPoint(taskInfo.taskName, new double[] { blockNum }));
            }
            series1.Visible = true;
            chartControlNProblem.Series.Add(series1);
            if (series1.Points.Count > 3)
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControlNProblem.Diagram)).AxisX.Label.Angle = 270;
            }
            else
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControlNProblem.Diagram)).AxisX.Label.Angle = 0;
            }
            //=======grid===
            dataGridViewProblemNumInfo.Visible = true;
            spliterNProblemDetail.Visible = false;
            dataGridViewProblemNumInfo.Rows.Clear();
            dataGridViewProblemNumInfo.Columns.Clear();
            //prepare for问题类型用以构建列
            Dictionary<string, int> branchProblemCount = new Dictionary<string, int>();//问题点类型分支（branch）->事件数量
            foreach (ESTaskInfo taskInfo in curESArea.taskOfAreaDic.Values)
            {
                Dictionary<string, int> branchProblemCountTask = taskInfo.CalcProblemCountByBranch();
                foreach (string branchname in branchProblemCountTask.Keys)
                {
                    int ct = 0;
                    if (!branchProblemCount.TryGetValue(branchname, out ct))
                    {
                        branchProblemCount[branchname] = branchProblemCountTask[branchname];
                    }
                    else
                    {
                        branchProblemCount[branchname] = branchProblemCountTask[branchname] + ct;
                    }
                }
            }
            Dictionary<string, int> taskNameAtColumnIndexDic = new Dictionary<string, int>();
            dataGridViewProblemNumInfo.Columns.Add("ProbNumColumn0", "异常事件类型");
            dataGridViewProblemNumInfo.Columns.Add("ProbNumColumn1", "总异常事件数");
            int cidx = 2;
            foreach (string branchname in branchProblemCount.Keys)
            {
                dataGridViewProblemNumInfo.Columns.Add("ProbNumColumn" + cidx, branchname);
                taskNameAtColumnIndexDic[branchname] = cidx;
                cidx++;
            }
            int indexRowAt = 0;
            foreach (ESTaskInfo taskInfo in curESArea.taskOfAreaDic.Values)
            {
                int bbNum = taskInfo.CalcCurrentProblemNum();
                Dictionary<string, int> taskProblemCountArea = taskInfo.CalcProblemCountByBranch();
                dataGridViewProblemNumInfo.Rows.Add(1);
                dataGridViewProblemNumInfo.Rows[indexRowAt].Cells[0].Value = taskInfo.taskName;
                dataGridViewProblemNumInfo.Rows[indexRowAt].Cells[1].Value = bbNum;
                foreach (string tname in taskProblemCountArea.Keys)
                {
                    dataGridViewProblemNumInfo.Rows[indexRowAt].Cells[taskNameAtColumnIndexDic[tname]].Value = taskProblemCountArea[tname];
                }
                indexRowAt++;
            }
        }

        private void refreshShowLevel3()
        {
            //==========chart
            chartControlNProblem.Series.Clear();
            chartControlNProblem.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = curESCity.cityName + " " + curESArea.areaName + " " + curESTask.taskName + "问题原因分类";
            chartControlNProblem.Titles.Add(title);
            Series series1 = new Series("异常事件数量", ViewType.Bar);
            foreach (ESBranchInfo branchInfo in curESTask.branchOfTaskDic.Values)
            {
                int blockNum = branchInfo.CalcCurrentProblemNum();
                series1.Points.Add(new SeriesPoint(branchInfo.branchName, new double[] { blockNum }));
            }
            series1.Visible = true;
            chartControlNProblem.Series.Add(series1);
            if (series1.Points.Count > 3)
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControlNProblem.Diagram)).AxisX.Label.Angle = 270;
            }
            else
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControlNProblem.Diagram)).AxisX.Label.Angle = 0;
            }
            //=======grid===
            dataGridViewProblemNumInfo.Visible = true;
            spliterNProblemDetail.Visible = false;
            dataGridViewProblemNumInfo.Rows.Clear();
            dataGridViewProblemNumInfo.Columns.Clear();

            dataGridViewProblemNumInfo.Columns.Add("ProbNumColumn0", "异常事件原因分类");
            dataGridViewProblemNumInfo.Columns.Add("ProbNumColumn1", "总异常事件数");
            int indexRowAt = 0;
            foreach (ESBranchInfo branchInfo in curESTask.branchOfTaskDic.Values)
            {
                int bbNum = branchInfo.CalcCurrentProblemNum();
                dataGridViewProblemNumInfo.Rows.Add(1);
                dataGridViewProblemNumInfo.Rows[indexRowAt].Cells[0].Value = branchInfo.branchName;
                dataGridViewProblemNumInfo.Rows[indexRowAt].Cells[1].Value = bbNum;
                indexRowAt++;
            }
        }

        private void refreshShowReport_BlackBlock()
        {
            //
            if (curGraphShowLevel_BlackBlock == 0)
            {
                //curShowMultiCityInfo;
                chartControlMain.Series.Clear();
                chartControlMain.Titles.Clear();
                ChartTitle title = new ChartTitle();
                title.Text = "全省黑点情况";
                chartControlMain.Titles.Add(title);
                Series serial = new Series("全省黑点情况", ViewType.Pie);
                ((PieSeriesView)serial.View).ExplodeMode = PieExplodeMode.All;
                ((PiePointOptions)serial.PointOptions).PointView = PointView.ArgumentAndValues;
                ((PiePointOptions)serial.PointOptions).PercentOptions.ValueAsPercent = false;
                ((PiePointOptions)serial.PointOptions).ValueNumericOptions.Format = NumericFormat.General;
                DataTable datInfo = new DataTable();
                datInfo.Columns.Add("地市", typeof(String));
                datInfo.Columns.Add("黑点数量", typeof(Int32));
                foreach (CityBlackBlockInfo cityInfo in curShowMultiCityInfo_BlackBlock.Values)
                {
                    string cityName = cityInfo.cityName;
                    int bbNum = cityInfo.CalcCurrentBlockNum();
                    datInfo.Rows.Add(new object[] { cityName, bbNum });
                }
                serial.ArgumentDataMember = "地市";
                serial.ValueScaleType = ScaleType.Numerical;
                serial.ValueDataMembers.AddRange(new string[] { "黑点数量" });
                serial.PointOptions.PointView = DevExpress.XtraCharts.PointView.ArgumentAndValues;
                //serial.PointOptions.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
                serial.DataSource = datInfo;
                chartControlMain.Series.Add(serial);
                //================GRID=============================
                dataGridView.Visible = true;
                dataGridViewBlockDetail.Visible = false;
                dataGridView.Rows.Clear();
                fillDataGridViewRow(); 
                ColumnTime.Visible = false;
                ColumnClosed.Visible = false;
                ColumnCreated.Visible = false;
            }
            else if (curGraphShowLevel_BlackBlock == 1)//查看地市各个区域
            {
                if (curShowCityInfo_BlackBlock == null)
                {
                    return;
                }
                string selShowType = cbxShowType.SelectedItem as string;
                if (selShowType == "当前情况")
                {
                    refreshShowReportByCurrent();
                }
                else if (selShowType == "最近一周" || selShowType == "最近一月")
                {
                    refreshShowReportByRecent(selShowType);
                }
                else if (selShowType == "按周" || selShowType == "按月" || selShowType == "按天")
                {
                    refreshShowReportByPeriod(selShowType);
                }
            }
        }

        private void fillDataGridViewRow()
        {
            int indexRowAt = 0;
#if !PopShow_BBLst3Month
            foreach (CityBlackBlockInfo cityInfo in curShowMultiCityInfo_BlackBlock.Values)
            {
                int bbNum = cityInfo.CalcCurrentBlockNum();
                dataGridView.Rows.Add(1);
                dataGridView.Rows[indexRowAt].Cells[0].Value = 1;
                dataGridView.Rows[indexRowAt].Cells[1].Value = cityInfo.cityName;
                dataGridView.Rows[indexRowAt].Cells[2].Value = 1;
                dataGridView.Rows[indexRowAt].Cells[3].Value = 1;
                dataGridView.Rows[indexRowAt].Cells[4].Value = bbNum;
                indexRowAt++;
            }
#else
            int month = 0;
            foreach (CityBlackBlockInfo cityInfo in curShowMultiCityInfo_BlackBlock.Values)
            {
                List<MonthResult> results = NewCountMonthHelper.CalcBlockInfoByExValidate(cityInfo.areaBlocksDic);
                dataGridView.Rows.Add(1);
                dataGridView.Rows[indexRowAt].Cells[0].Value = 1;
                dataGridView.Rows[indexRowAt].Cells[1].Value = cityInfo.cityName;
                dataGridView.Rows[indexRowAt].Cells[2].Value = 1;
                dataGridView.Rows[indexRowAt].Cells[3].Value = 1;
                dataGridView.Rows[indexRowAt].Cells[4].Value = 0;
                int indexColAt = 5;
                foreach (MonthResult result in results)
                {
                    month = result.month;
                    dataGridView.Columns[indexColAt].HeaderText = month + "月剩余数量";
                    dataGridView.Rows[indexRowAt].Cells[indexColAt++].Value = result.count;
                    dataGridView.Columns[indexColAt].HeaderText = month + "月新增率";
                    dataGridView.Rows[indexRowAt].Cells[indexColAt++].Value = result.IncreaseRateString;
                    dataGridView.Columns[indexColAt].HeaderText = month + "月消除率";
                    dataGridView.Rows[indexRowAt].Cells[indexColAt++].Value = result.EliminateRateString;
                }
                indexRowAt++;
            }
            setLast3MonthVisible(true);
#endif
        }

        private void refreshShowReportByCurrent()
        {
            chartControlMain.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = curShowCityInfo_BlackBlock.cityName + "黑点分布情况";
            chartControlMain.Titles.Add(title);

            Series series1 = new Series("黑点数量", ViewType.Bar);
            foreach (string areaname in curShowCityInfo_BlackBlock.areaBlocksDic.Keys)
            {
                int blockNum = calcBlockNumFromList(curShowCityInfo_BlackBlock.areaBlocksDic[areaname]);
                series1.Points.Add(new SeriesPoint(areaname, new double[] { blockNum }));
            }
            series1.Visible = true;
            chartControlMain.Series.Clear();
            chartControlMain.Series.Add(series1);
            if (series1.Points.Count > 5)
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControlMain.Diagram)).AxisX.Label.Angle = 270;
                //((DevExpress.XtraCharts.XYDiagram)(chartControlMain.Diagram)).EnableAxisXScrolling = false;
                //((DevExpress.XtraCharts.XYDiagram)(chartControlMain.Diagram)).EnableAxisXZooming = false;
            }
            else if (series1.Points.Count > 0)
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControlMain.Diagram)).AxisX.Label.Angle = 0;
            }
            //================GRID=============================
            dataGridView.Visible = true;
            dataGridViewBlockDetail.Visible = false;
            dataGridView.Rows.Clear();
            int indexRowAt = 0;
#if !PopShow_BBLst3Month
            foreach (string areaname in curShowCityInfo_BlackBlock.areaBlocksDic.Keys)
            {
                int blockNum = calcBlockNumFromList(curShowCityInfo_BlackBlock.areaBlocksDic[areaname]);
                dataGridView.Rows.Add(1);
                dataGridView.Rows[indexRowAt].Cells[0].Value = 1;
                dataGridView.Rows[indexRowAt].Cells[1].Value = areaname;
                dataGridView.Rows[indexRowAt].Cells[2].Value = 1;
                dataGridView.Rows[indexRowAt].Cells[3].Value = 1;
                dataGridView.Rows[indexRowAt].Cells[4].Value = blockNum;
                indexRowAt++;
            }
#else
            int month = 0;
            foreach (string areaname in curShowCityInfo_BlackBlock.areaBlocksDic.Keys)
            {
                Dictionary<string, List<NewBlockEntryItem>> dic = new Dictionary<string, List<NewBlockEntryItem>>()
                {
                    { "",curShowCityInfo_BlackBlock.areaBlocksDic[areaname]}
                };
                List<MonthResult> results = NewCountMonthHelper.CalcBlockInfoByExValidate(dic);
                dataGridView.Rows.Add(1);
                dataGridView.Rows[indexRowAt].Cells[0].Value = 1;
                dataGridView.Rows[indexRowAt].Cells[1].Value = areaname;
                dataGridView.Rows[indexRowAt].Cells[2].Value = 1;
                dataGridView.Rows[indexRowAt].Cells[3].Value = 1;
                dataGridView.Rows[indexRowAt].Cells[4].Value = 0;
                int indexColAt = 5;
                foreach (MonthResult result in results)
                {
                    month = result.month;
                    dataGridView.Columns[indexColAt].HeaderText = month + "月剩余数量";
                    dataGridView.Rows[indexRowAt].Cells[indexColAt++].Value = result.count;
                    dataGridView.Columns[indexColAt].HeaderText = month + "月新增率";
                    dataGridView.Rows[indexRowAt].Cells[indexColAt++].Value = result.IncreaseRateString;
                    dataGridView.Columns[indexColAt].HeaderText = month + "月消除率";
                    dataGridView.Rows[indexRowAt].Cells[indexColAt++].Value = result.EliminateRateString;
                }
                indexRowAt++;
            }
            setLast3MonthVisible(true);
#endif
            ColumnTime.Visible = false;
            ColumnClosed.Visible = false;
            ColumnCreated.Visible = false;
            //endgrid
        }

        private void refreshShowReportByRecent(string selShowType)
        {
            //==GRID===
            dataGridView.Visible = true;
            dataGridViewBlockDetail.Visible = false;
            ColumnTime.Visible = false;
            ColumnClosed.Visible = true;
            ColumnCreated.Visible = true;
            setLast3MonthVisible(false);
            dataGridView.Rows.Clear();
            int indexRowAt = 0;
            //===
            chartControlMain.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = curShowCityInfo_BlackBlock.cityName + selShowType + "黑点情况";
            chartControlMain.Titles.Add(title);
            Series series1 = new Series("新增", ViewType.Bar);
            Series series2 = new Series("关闭", ViewType.Bar);
            Series series3 = new Series("剩余", ViewType.Bar);
            foreach (string areaname in curShowCityInfo_BlackBlock.areaBlocksDic.Keys)
            {
                int newNum = 0;
                int closeNum = 0;
                int curNum = 0;
                List<NewBlockEntryItem> listOfArea = curShowCityInfo_BlackBlock.areaBlocksDic[areaname];
                getNumByLastPeriod(listOfArea, selShowType, out newNum, out closeNum, out curNum);
                series1.Points.Add(new SeriesPoint(areaname, new double[] { newNum }));
                series2.Points.Add(new SeriesPoint(areaname, new double[] { closeNum }));
                series3.Points.Add(new SeriesPoint(areaname, new double[] { curNum }));
                //===GRID=====
                dataGridView.Rows.Add(1);
                dataGridView.Rows[indexRowAt].Cells[0].Value = 1;
                dataGridView.Rows[indexRowAt].Cells[1].Value = areaname;
                dataGridView.Rows[indexRowAt].Cells[2].Value = newNum;
                dataGridView.Rows[indexRowAt].Cells[3].Value = closeNum;
                dataGridView.Rows[indexRowAt].Cells[4].Value = curNum;
                indexRowAt++;
                //=endgrid
            }
            series1.Visible = true;
            series2.Visible = true;
            series3.Visible = true;
            chartControlMain.Series.Clear();
            chartControlMain.Series.Add(series1);
            chartControlMain.Series.Add(series2);
            chartControlMain.Series.Add(series3);
            if (series1.Points.Count > 5)
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControlMain.Diagram)).AxisX.Label.Angle = 270;
            }
            else if (series1.Points.Count > 0)
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControlMain.Diagram)).AxisX.Label.Angle = 0;
            }
        }

        private void refreshShowReportByPeriod(string selShowType)
        {
            chartControlMain.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = curShowCityInfo_BlackBlock.cityName + selShowType + "黑点情况";
            chartControlMain.Titles.Add(title);
            chartControlMain.Series.Clear();
            foreach (string areaname in curShowCityInfo_BlackBlock.areaBlocksDic.Keys)
            {
                List<NewBlockEntryItem> listOfArea = curShowCityInfo_BlackBlock.areaBlocksDic[areaname];
                BlackBlockInfoHelper helper = new BlackBlockInfoHelper();
                List<BlockNumResult> numList = helper.PrepareShowByPeriod(listOfArea, selShowType);
                Series series = new Series(areaname, ViewType.Line);
                foreach (BlockNumResult bn in numList)
                {
                    series.Points.Add(new SeriesPoint(bn.timeStr, new double[] { bn.remainNum }));
                }
                series.Visible = true;
                ((LineSeriesView)series.View).LineMarkerOptions.Size = 5;
                chartControlMain.Series.Add(series);
            }
            if (chartControlMain.Series.Count > 3)
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControlMain.Diagram)).AxisX.Label.Angle = 270;
                //   ((DevExpress.XtraCharts.XYDiagram)(chartControlMain.Diagram)).EnableAxisXScrolling = true;
                //   ((DevExpress.XtraCharts.XYDiagram)(chartControlMain.Diagram)).EnableAxisXZooming = true;
                //   ((DevExpress.XtraCharts.XYDiagram)(chartControlMain.Diagram)).PaneDistance = 5;
            }
        }

        private void setLast3MonthVisible(bool visible)
        {
            ColumnRemainCount.Visible = !visible;
            ColumnCountThisMonth.Visible = visible;
            ColumnIncreaseRateThisMonth.Visible = visible;
            ColumnEliminateRateThisMonth.Visible = visible;
            ColumnCountLastMonth.Visible = visible;
            ColumnIncreaseRateLastOneMonth.Visible = visible;
            ColumnEliminateRateLastOneMonth.Visible = visible;
            ColumnCountLastTwoMonth.Visible = visible;
            ColumnIncreaseRateLastTwoMonth.Visible = visible;
            ColumnEliminateRateLastTwoMonth.Visible = visible;
        }

        private void getNumByLastPeriod(List<NewBlockEntryItem> blockList, string selShowType, List<NewBlockEntryItem> newList, List<NewBlockEntryItem> closeList, List<NewBlockEntryItem> curList)
        {
            newList.Clear();
            closeList.Clear();
            curList.Clear();
            DateTime dtRef = DateTime.Now;
            int dateSpliter = 0;
            if (selShowType == "最近一月")
            {
                dateSpliter = (int)(JavaDate.GetMilliseconds(dtRef.AddDays(-30)) / 1000L);
            }
            else if (selShowType == "最近一周")
            {
                dateSpliter = (int)(JavaDate.GetMilliseconds(dtRef.AddDays(-7)) / 1000L);
            }
            foreach (NewBlockEntryItem block in blockList)
            {
                if (block.status == 4)//关闭的
                {
                    if (block.closed_date >= dateSpliter)
                    {
                        closeList.Add(block);
                    }
                    if (block.created_date >= dateSpliter)
                    {
                        newList.Add(block);
                    }
                }
                else
                {
                    if (block.created_date >= dateSpliter)
                    {
                        newList.Add(block);
                    }
                    curList.Add(block);
                }
            }
        }
        private void getNumByLastPeriod(List<NewBlockEntryItem> blockList, string selShowType, out int newNum, out int closeNum, out int curNum)
        {
            DateTime dtRef = DateTime.Now;
            int dateSpliter = 0;
            if (selShowType == "最近一月")
            {
                dateSpliter = (int)(JavaDate.GetMilliseconds(dtRef.AddDays(-30)) / 1000L);
            }
            else if (selShowType == "最近一周")
            {
                dateSpliter = (int)(JavaDate.GetMilliseconds(dtRef.AddDays(-7)) / 1000L);
            }
            newNum = 0;
            closeNum = 0;
            curNum = 0;
            foreach (NewBlockEntryItem block in blockList)
            {
                if (block.status == 4)//关闭的
                {
                    if (block.closed_date >= dateSpliter)
                    {
                        closeNum++;
                    }
                    if (block.created_date >= dateSpliter)
                    {
                        newNum++;
                    }
                }
                else
                {
                    if (block.created_date >= dateSpliter)
                    {
                        newNum++;
                    }
                    curNum++;
                }
            }
        }

        private int calcBlockNumFromList(List<NewBlockEntryItem> blocklist)
        {
            int count = 0;
            foreach (NewBlockEntryItem bbItem in blocklist)
            {
                if (bbItem.status != 4)
                {
                    count++;
                }
            }
            return count;
        }

        internal void ReturnTChartControl(Steema.TeeChart.TChart tchart)
        {
            tchart.Parent = null;
            splitMainBlackBlock.Panel2.Controls.Add(tchart);
            splitMainBlackBlock.Panel2Collapsed = false;
            tchart.Dock = DockStyle.Fill;
        }

        private void btnGoBlackBlockQuery_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            MapForm mapform = MainModel.GetInstance().MainForm.GetMapForm();
            if (mapform != null)
            {
                WelcomForm welcomForm = this.Parent.Parent.Parent as WelcomForm;
                welcomForm.WindowState = FormWindowState.Minimized;
                CondSaverItem csi = new CondSaverItem();
                csi.autoRun = 1;
                csi.timeType = -1;
                csi.targetType = -1;
                csi.taskName = "问题黑点查询";
            }
        }

        private void miViewBlockInfo_Click(object sender, EventArgs e)
        {
            if (curRowSel_BlackBlock >= 0 && dataGridViewBlockDetail.Rows.Count > curRowSel_BlackBlock)
            {
                NewBlockEntryItem selEntry = dataGridViewBlockDetail.Rows[curRowSel_BlackBlock].Tag as NewBlockEntryItem;
                if (selEntry != null)
                {
                    if (MainModel.DistrictID == selEntry.dbid)//当前登录地市的黑点，可以直接回放
                    {
                        GetNewBlackBlockOne query = new GetNewBlackBlockOne(MainModel);
                        query.FillCondition(selEntry.type, selEntry.block_id);
                        query.Query();
                    }
                    else
                    {
                        MessageBox.Show(this, "所选黑点非当前登录地市黑点，请先切换登录地市！");
                    }
                }
            }
        }
        private int curRowSel_BlackBlock = -1;
        private int curColSel_BlackBlock = -1;
        private void dataGridViewBlockDetail_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            curRowSel_BlackBlock = e.RowIndex;
            curColSel_BlackBlock = e.ColumnIndex;
            ctxDetail.Items[0].Enabled = true;
        }

        private void chartControlMain_MouseMove(object sender, MouseEventArgs e)
        {
            ChartHitInfo hitInfo = chartControlMain.CalcHitInfo(e.Location);
            if (curGraphShowLevel_BlackBlock == 0)
            {
                bool isHint = hitInfo.InSeries && hitInfo.SeriesPoint != null;
                if (isHint)
                {
                    string cityname = hitInfo.SeriesPoint.Argument;
                    toolTipController.ShowHint("单击进入地市：" + cityname, chartControlMain.PointToScreen(e.Location));
                    return;
                }
            }
            else if (curGraphShowLevel_BlackBlock == 1)
            {
                bool isHint = hitInfo.InChartTitle && MainModel.User.DBID == -1;
                if (isHint)
                {
                    toolTipController.ShowHint("单击返回全省情况查看", chartControlMain.PointToScreen(e.Location));
                    return;
                }
            }
            toolTipController.HideHint();
        }

        private void chartControlNProblem_MouseClick(object sender, MouseEventArgs e)
        {
            ChartHitInfo hitInfo = chartControlNProblem.CalcHitInfo(e.Location);
            if (hitInfo.InSeries)
            {
                SeriesPoint selPoint = hitInfo.SeriesPoint;
                if (selPoint == null)
                {
                    return;
                }
                refreshInSeries_ES(selPoint);
            }
            else if (hitInfo.InChartTitle)
            {
                refreshInChartTitle_ES(hitInfo);
            }
        }

        private void refreshInSeries_ES(SeriesPoint selPoint)
        {
            if (curGraphShowLevel_ES == 0)
            {
                string cityName = selPoint.Argument;
                ESCityInfo cityinfo = null;
                if (curESAllCitysDic.TryGetValue(cityName, out cityinfo))
                {
                    curGraphShowLevel_ES = 1;
                    curESCity = cityinfo;
                    refreshShowReport_ES();
                }
            }
            else if (curGraphShowLevel_ES == 1)
            {
                string areaName = selPoint.Argument;
                ESAreaInfo areainfo = null;
                if (curESCity.areaOfCityDic.TryGetValue(areaName, out areainfo))
                {
                    curGraphShowLevel_ES = 2;
                    curESArea = areainfo;
                    refreshShowReport_ES();
                }
            }
            else if (curGraphShowLevel_ES == 2)
            {
                string taskName = selPoint.Argument;
                ESTaskInfo taskinfo = null;
                if (curESArea.taskOfAreaDic.TryGetValue(taskName, out taskinfo))
                {
                    curGraphShowLevel_ES = 3;
                    curESTask = taskinfo;
                    refreshShowReport_ES();
                }
            }
            else if (curGraphShowLevel_ES == 3)
            {
                string branchName = selPoint.Argument;
                ESBranchInfo branchInfo = null;
                if (curESTask.branchOfTaskDic.TryGetValue(branchName, out branchInfo))
                {
                    dataGridViewProblemNumInfo.Visible = false;
                    spliterNProblemDetail.Visible = true;
                    dataGridViewProblemDetail.Rows.Clear();
                    int indexRowAt = 0;
                    foreach (PopESEvent evt in branchInfo.esDetailList)
                    {
                        dataGridViewProblemDetail.Rows.Add(1);
                        dataGridViewProblemDetail.Rows[indexRowAt].Tag = evt;
                        dataGridViewProblemDetail.Rows[indexRowAt].Cells[0].Value = JavaDate.GetDateTimeFromMilliseconds(1000L * evt.itime).ToString("yyyy-MM-dd HH:mm:ss");
                        dataGridViewProblemDetail.Rows[indexRowAt].Cells[1].Value = evt.pretype_desc;
                        dataGridViewProblemDetail.Rows[indexRowAt].Cells[2].Value = evt.filaname;
                        indexRowAt++;
                    }
                    tbxNProblemDetailDesc.Text = "";
                }
            }
        }

        private void refreshInChartTitle_ES(ChartHitInfo hitInfo)
        {
            DockableTitle dockTitle = hitInfo.ChartTitle as DockableTitle;
            if (dockTitle != null)
            {
                if (curGraphShowLevel_ES == 3)
                {
                    curGraphShowLevel_ES = 2;
                    refreshShowReport_ES();
                }
                else if (curGraphShowLevel_ES == 2)
                {
                    curGraphShowLevel_ES = 1;
                    refreshShowReport_ES();
                }
                else if (curGraphShowLevel_ES == 1)
                {
                    if (MainModel.User.DBID == -1)
                    {
                        curGraphShowLevel_ES = 0;
                        refreshShowReport_ES();
                    }
                }
                else
                {
                    //
                }
            }
        }

        private void chartControlMain_MouseClick(object sender, MouseEventArgs e)
        {
            ChartHitInfo hitInfo = chartControlMain.CalcHitInfo(e.Location);
            if (hitInfo.InSeries)
            {
                Series curSelSeries = hitInfo.Series as Series;
                SeriesPoint selPoint = hitInfo.SeriesPoint;
                if (selPoint == null)
                {
                    return;
                }
                if (curGraphShowLevel_BlackBlock == 0)
                {
                    string cityName = selPoint.Argument;
                    CityBlackBlockInfo cityblockInfo = null;
                    if (curShowMultiCityInfo_BlackBlock.TryGetValue(cityName, out cityblockInfo))
                    {
                        curGraphShowLevel_BlackBlock = 1;
                        curShowCityInfo_BlackBlock = cityblockInfo;
                        refreshShowReport_BlackBlock();
                    }
                }
                else if (curGraphShowLevel_BlackBlock == 1)
                {
                    dealBlockDetailLevel1(curSelSeries, selPoint);
                }
            }
            else if (hitInfo.InChartTitle)
            {
                DockableTitle dockTitle = hitInfo.ChartTitle as DockableTitle;
                if (dockTitle != null && MainModel.User.DBID == -1)
                {
                    curGraphShowLevel_BlackBlock = 0;
                    refreshShowReport_BlackBlock();
                }
            }
        }

        private void dealBlockDetailLevel1(Series curSelSeries, SeriesPoint selPoint)
        {
            string selShowType = cbxShowType.SelectedItem as string;
            if (selShowType == "当前情况")
            {
                string areaname = selPoint.Argument;
                setCurBlockDetail(areaname);
            }
            else if (selShowType == "最近一周" || selShowType == "最近一月")
            {
                string areaname = selPoint.Argument;
                List<NewBlockEntryItem> blockList = null;
                if (curShowCityInfo_BlackBlock.areaBlocksDic.TryGetValue(areaname, out blockList))
                {
                    dataGridView.Visible = false;
                    dataGridViewBlockDetail.Visible = true;
                    dataGridViewBlockDetail.Rows.Clear();
                    //===
                    List<NewBlockEntryItem> newList = new List<NewBlockEntryItem>();
                    List<NewBlockEntryItem> closeList = new List<NewBlockEntryItem>();
                    List<NewBlockEntryItem> curList = new List<NewBlockEntryItem>();
                    getNumByLastPeriod(blockList, selShowType, newList, closeList, curList);
                    if (curSelSeries == null)
                    {
                        return;
                    }
                    if (curSelSeries.Name == "剩余")
                    {
                        dColumnCloseTime.Visible = false;
                        addDataGridViewBlockDetail(newList, setBlockDescCreate);
                    }
                    else if (curSelSeries.Name == "新增")
                    {
                        dColumnCloseTime.Visible = true;
                        addDataGridViewBlockDetail(newList, setBlockDescAdd);
                    }
                    else if (curSelSeries.Name == "关闭")
                    {
                        dColumnCloseTime.Visible = true;
                        addDataGridViewBlockDetail(newList, setBlockDescClose);
                    }
                }
            }
        }

        private void setCurBlockDetail(string areaname)
        {
            List<NewBlockEntryItem> blockList = null;
            if (curShowCityInfo_BlackBlock.areaBlocksDic.TryGetValue(areaname, out blockList))
            {
                dataGridView.Visible = false;
                dataGridViewBlockDetail.Visible = true;
                dColumnCloseTime.Visible = false;
                dataGridViewBlockDetail.Rows.Clear();
                //===
                int indexRowAt = 0;
                foreach (NewBlockEntryItem block in blockList)
                {
                    if (block.status != 4)
                    {
                        dataGridViewBlockDetail.Rows.Add(1);
                        dataGridViewBlockDetail.Rows[indexRowAt].Tag = block;
                        dataGridViewBlockDetail.Rows[indexRowAt].Cells[0].Value = block.block_id;
                        dataGridViewBlockDetail.Rows[indexRowAt].Cells[1].Value = JavaDate.GetDateTimeFromMilliseconds(1000L * block.created_date).ToString("yyyy-MM-dd");
                        dataGridViewBlockDetail.Rows[indexRowAt].Cells[2].Value = "";
                        dataGridViewBlockDetail.Rows[indexRowAt].Cells[3].Value = "已创建";
                        dataGridViewBlockDetail.Rows[indexRowAt].Cells[4].Value = block.evtnum;
                        dataGridViewBlockDetail.Rows[indexRowAt].Cells[5].Value = block.area_names;
                        indexRowAt++;
                    }
                }
            }
        }

        private void addDataGridViewBlockDetail(List<NewBlockEntryItem> newList, BlockFunc func)
        {
            int indexRowAt = 0;
            foreach (NewBlockEntryItem block in newList)
            {
                dataGridViewBlockDetail.Rows.Add(1);
                dataGridViewBlockDetail.Rows[indexRowAt].Tag = block;
                dataGridViewBlockDetail.Rows[indexRowAt].Cells[0].Value = block.block_id;
                dataGridViewBlockDetail.Rows[indexRowAt].Cells[1].Value = JavaDate.GetDateTimeFromMilliseconds(1000L * block.created_date).ToString("yyyy-MM-dd");
                func(indexRowAt, block);
                dataGridViewBlockDetail.Rows[indexRowAt].Cells[4].Value = block.evtnum;
                dataGridViewBlockDetail.Rows[indexRowAt].Cells[5].Value = block.area_names;
                indexRowAt++;
            }
        }

        private void setBlockDescCreate(int indexRowAt, NewBlockEntryItem block)
        {
            dataGridViewBlockDetail.Rows[indexRowAt].Cells[2].Value = "";
            dataGridViewBlockDetail.Rows[indexRowAt].Cells[3].Value = "已创建";
        }

        private void setBlockDescAdd(int indexRowAt, NewBlockEntryItem block)
        {
            dataGridViewBlockDetail.Rows[indexRowAt].Cells[2].Value = JavaDate.GetDateTimeFromMilliseconds(1000L * block.closed_date).ToString("yyyy-MM-dd");
            dataGridViewBlockDetail.Rows[indexRowAt].Cells[3].Value = block.status == 4 ? "已关闭" : "已创建";
        }

        private void setBlockDescClose(int indexRowAt, NewBlockEntryItem block)
        {
            dataGridViewBlockDetail.Rows[indexRowAt].Cells[2].Value = JavaDate.GetDateTimeFromMilliseconds(1000L * block.closed_date).ToString("yyyy-MM-dd");
            dataGridViewBlockDetail.Rows[indexRowAt].Cells[3].Value = "已关闭";
        }

        delegate void BlockFunc(int indexRowAt, NewBlockEntryItem block);

        private void chartControlNProblem_MouseMove(object sender, MouseEventArgs e)
        {
            ChartHitInfo hitInfo = chartControlNProblem.CalcHitInfo(e.Location);
            bool hide = true;
            if (curGraphShowLevel_ES == 0)
            {
                judgeSeries(e, hitInfo, "单击进入地市：", ref hide);
            }
            else if (curGraphShowLevel_ES == 1)
            {
                judgeSeries(e, hitInfo, "单击进入异常事件点：", ref hide);
                if (MainModel.User.DBID == -1)
                {
                    judgeChartTitle(e, hitInfo, "单击返回全省情况查看", ref hide);
                }
            }
            else if (curGraphShowLevel_ES == 2)
            {
                judgeSeries(e, hitInfo, "单击进入异常事件类型：", ref hide);
                judgeChartTitle(e, hitInfo, "单击返回异常事件点情况查看", ref hide);
            }
            else if (curGraphShowLevel_ES == 3)
            {
                judgeSeries(e, hitInfo, "单击查看详细事件列表：", ref hide);
                judgeChartTitle(e, hitInfo, "单击返回异常事件类型情况查看", ref hide);
            }

            if (hide)
            {
                toolTipController.HideHint();
            }
        }

        private void judgeSeries(MouseEventArgs e, ChartHitInfo hitInfo, string desc, ref bool isHide)
        {
            if (hitInfo.InSeries && hitInfo.SeriesPoint != null)
            {
                string name = hitInfo.SeriesPoint.Argument;
                toolTipController.ShowHint(desc + name, chartControlNProblem.PointToScreen(e.Location));
                isHide = false;
            }
        }

        private void judgeChartTitle(MouseEventArgs e, ChartHitInfo hitInfo, string desc, ref bool isHide)
        {
            if (!isHide)
            {
                return;
            }
            if (hitInfo.InChartTitle)
            {
                toolTipController.ShowHint(desc, chartControlNProblem.PointToScreen(e.Location));
                isHide = false;
            }
        }

        private int curRowSel_ES = -1;
        private int curColSel_ES = -1;
        private void dataGridViewProblemDetail_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            curRowSel_ES = e.RowIndex;
            curColSel_ES = e.ColumnIndex;

            tbxNProblemDetailDesc.Text = "";
            //
            if (curRowSel_ES >= 0 && dataGridViewProblemDetail.Rows.Count > curRowSel_ES)
            {
                PopESEvent selEvt = dataGridViewProblemDetail.Rows[curRowSel_ES].Tag as PopESEvent;
                if (selEvt != null)
                {
                    tbxNProblemDetailDesc.Text = selEvt.reason_desc;
                }
            }

        }

        private void miReplayEvent_Click(object sender, EventArgs e)
        {
            if (curRowSel_ES >= 0 && dataGridViewProblemDetail.Rows.Count > curRowSel_ES)
            {
                PopESEvent selEvt = dataGridViewProblemDetail.Rows[curRowSel_ES].Tag as PopESEvent;
                if (selEvt != null)
                {
                    if (MainModel.DistrictID == selEvt.dbid)//当前登录地市的事件，可以直接回放
                    {
                        PreNextMinutesForm preNextMinutesForm = new PreNextMinutesForm(false);
                        DIYReplayFileWithinPeriodQuery qb = new DIYReplayFileWithinPeriodQuery(MainModel.GetInstance());
                        if (preNextMinutesForm.ShowDialog() == DialogResult.OK)
                        {
                            ReplayEvent(preNextMinutesForm, selEvt, qb);
                        }
                    }
                    else
                    {
                        MessageBox.Show(this, "所选问题点非当前登录地市问题，请先切换登录地市！");
                    }
                }
            }

        }
        private void ReplayEvent(PreNextMinutesForm preNextMinutesForm, PopESEvent dtData, QueryBase qb)
        {
            QueryCondition condition = new QueryCondition();
            condition.QueryType = 2;//depth
            FileInfo fileInfo = new FileInfo();
            fileInfo.Name = dtData.filaname;
            fileInfo.ProjectID = dtData.iprojecttype;
            fileInfo.ID = dtData.ifileid;
            fileInfo.ServiceType = dtData.iservice;
            fileInfo.SampleTbName = dtData.sampletbname;
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(1000L * dtData.itime);
            fileInfo.LogTable = string.Format("tb_log_file_{0}_{1:D2}", dt.Year, dt.Month);
            condition.FileInfos.Add(fileInfo);
            int pre = preNextMinutesForm.Pre;
            int next = preNextMinutesForm.Next;
            condition.Periods.Add(new TimePeriod(dt.AddMinutes(-pre), dt.AddMinutes(next)));
            qb.SetQueryCondition(condition);
            qb.Query();
        }

        public void ExportAllTabInfo(ExcelControl excel, string path)
        {
            try
            {
                System.IO.Directory.CreateDirectory(System.IO.Path.Combine(Application.StartupPath, "BbExpInfoPict"));
            }
            catch (Exception)
            {
                //continue
            }
            int index = tabMain.SelectedIndex;
            for (int i = 0; i < tabMain.TabCount; i++)
            {
                tabMain.SelectedIndex = i;
                ExportInfo(excel);
            }
            tabMain.SelectedIndex = index;
        }

        private void ExportInfo(ExcelControl excel)
        {
            BbExport2XlsParam wordParam = new BbExport2XlsParam();
            wordParam.excelApp = excel;
            wordParam.pictPath = System.IO.Path.Combine(Application.StartupPath, "BbExpInfoPict") + "\\temp.jpg";
            switch (tabMain.SelectedTab.Text)
            {
                case "道路问题黑点":
                    wordParam.chartCtrl = chartControlMain;
                    wordParam.pictWidth = chartControlMain.Width / 96f * 72f;
                    wordParam.pictHeight = chartControlMain.Height / 96f * 72f;
                    wordParam.dgv = dataGridViewBlockDetail.Visible ? dataGridViewBlockDetail : dataGridView;
                    wordParam.excelApp.CreateSheet();
                    wordParam.excelApp.Sheet.Name = "道路问题黑点";
                    WaitBox.Show(this, printPict, wordParam);
                    break;
                case "异常事件点":
                    wordParam.excelApp.CreateSheet();
                    wordParam.excelApp.Sheet.Name = "异常事件点";
                    wordParam.chartCtrl = chartControlNProblem;
                    wordParam.pictWidth = chartControlNProblem.Width / 96f * 72f;
                    wordParam.pictHeight = chartControlNProblem.Height / 96f * 72f;
                    wordParam.dgv = dataGridViewProblemNumInfo.Visible ? dataGridViewProblemNumInfo : dataGridViewProblemDetail;
                    WaitBox.Show(this, printPict, wordParam);
                    break;
                case "竞争对比分析":
                    wordParam.excelApp.CreateSheet();
                    wordParam.excelApp.Sheet.Name = "竞争对比分析";
                    wordParam.chartCtrl = chartControlCompBench;
                    wordParam.pictWidth = chartControlCompBench.Width / 96f * 72f;
                    wordParam.pictHeight = chartControlCompBench.Height / 96f * 72f;
                    wordParam.dgv = dataGridViewCompBenchInfo.Visible ? dataGridViewCompBenchInfo : dataGridView2;
                    WaitBox.Show(this, printPict, wordParam);
                    break;
                default:
                    break;
            }
            WaitBox.Close();
        }

        private void ctxExp2WordToolStripMenuItem_Click(object sender, EventArgs e)
        {
            SelectSavePath();
        }
        private void SelectSavePath()
        {
            SaveFileDialog saveFileDlg = new SaveFileDialog();
            saveFileDlg.Title = "选择要保存文档的路径";
            saveFileDlg.RestoreDirectory = true;
            saveFileDlg.Filter = FilterHelper.Excel;
            if (saveFileDlg.ShowDialog() == DialogResult.OK)
            {
                ExcelControl excel = new ExcelControl();
                try
                {
                    System.IO.Directory.CreateDirectory(System.IO.Path.Combine(Application.StartupPath, "BbExpInfoPict"));
                    ExportInfo(excel);
                    excel.SaveFile(saveFileDlg.FileName);
                }
                catch (Exception e)
                {
                    MessageBox.Show(e.Message);
                    return;
                }
                finally
                {
                    excel.CloseExcel();
                }
                if (DialogResult.Yes == MessageBox.Show(this, "Excel文件保存完毕，是否现在打开?", "打开文件", MessageBoxButtons.YesNo))
                {
                    try
                    {
                        System.Diagnostics.Process.Start(saveFileDlg.FileName);
                    }
                    catch
                    {
                        MessageBox.Show("打开失败!\r\n文件名:" + saveFileDlg.FileName);
                    }
                }
            }
        }

        private void miExp2Word_Dtl_Click(object sender, EventArgs e)
        {
            if (dataGridViewBlockDetail.RowCount <= 0)
            {
                MessageBox.Show("当前无数据可导出！");
                return;
            }
            SelectSavePath();
        }
        private void printPict(object param)
        {
            BbExport2XlsParam wordParam = param as BbExport2XlsParam;
            wordParam.chartCtrl.ExportToImage(wordParam.pictPath, System.Drawing.Imaging.ImageFormat.Jpeg);
            fireExp2Xls(wordParam);
        }

        private void fireExp2Xls(object param)
        {
            BbExport2XlsParam wordParam = param as BbExport2XlsParam;
            wordParam.excelApp.Sheet.Name = tabMain.SelectedTab.Text;
            wordParam.excelApp.ExportExcel(wordParam.excelApp.Sheet, wordParam.dgv);
            wordParam.excelApp.InsertPicture(wordParam.dgv.RowCount + 2, wordParam.pictPath, wordParam.pictWidth, wordParam.pictHeight);
        }

        private void miExp2Word_Evt_Click(object sender, EventArgs e)
        {
            if (dataGridViewProblemDetail.RowCount <= 0)
            {
                MessageBox.Show("当前无数据可导出！");
                return;
            }
            SelectSavePath();
        }

        private void miExp2Word_CompBench_Click(object sender, EventArgs e)
        {
            if (dataGridViewCompBenchInfo.RowCount <= 0)
            {
                MessageBox.Show("当前无数据可导出！");
                return;
            }
            SelectSavePath();
        }

#region 竞对分析


#if PopShow_CompBench
        private List<CompBenchStatusEntryItem> queryCompBenchStatusFrom(BackgroundWorker worker, int dbid)
        {
            List<CompBenchStatusEntryItem> retList = new List<CompBenchStatusEntryItem>();
            ClientProxy clientProxy = new ClientProxy();

            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                worker.ReportProgress(99, "连接服务器端出错！");
                return retList;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.InfoQuery;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_TABLE_DATA_INFO;
                package.Content.PrepareAddParam();
                package.Content.AddParam(dbid);
                package.Content.AddParam("tb_pop_compbench_status");
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_TABLE_DATA_INFO)
                    {
                        package.Content.PrepareGetParam();
                        CompBenchStatusEntryItem retItem = CompBenchStatusEntryItem.ReadResultItemFrom(package.Content);
                        retList.Add(retItem);
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                }
                return retList;
            }

            finally
            {
                clientProxy.Close();
            }
        }

        private List<CompBenchUnitEntryItem> queryCompBenchUnitFrom(BackgroundWorker worker, int dbid)
        {
            List<CompBenchUnitEntryItem> retList = new List<CompBenchUnitEntryItem>();
            ClientProxy clientProxy = new ClientProxy();

            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                worker.ReportProgress(99, "连接服务器端出错！");
                return retList;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.InfoQuery;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_TABLE_DATA_INFO;
                package.Content.PrepareAddParam();
                package.Content.AddParam(dbid);
                package.Content.AddParam("tb_pop_compbench_unit");
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_TABLE_DATA_INFO)
                    {
                        package.Content.PrepareGetParam();
                        CompBenchUnitEntryItem retItem = CompBenchUnitEntryItem.ReadResultItemFrom(package.Content);
                        retList.Add(retItem);
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                }
                return retList;
            }

            finally
            {
                clientProxy.Close();
            }
        }

        /**
        private Dictionary<string, CityBlackBlockInfo> buildCityStructFrom_CompBench(string typeName, List<NewBlockEntryItem> blockEntryList)
        {
            Dictionary<string, CityBlackBlockInfo> cityDic = new Dictionary<string, CityBlackBlockInfo>();
            foreach (NewBlockEntryItem bbEntry in blockEntryList)
            {
                string cityName = DistrictManager.GetInstance().getDistrictName(bbEntry.dbid);
                CityBlackBlockInfo cityBlockInfo = null;
                if (!cityDic.TryGetValue(cityName, out cityBlockInfo))
                {
                    cityBlockInfo = new CityBlackBlockInfo();
                    cityBlockInfo.cityName = cityName;
                    cityDic[cityName] = cityBlockInfo;
                }
                List<NewBlockEntryItem> blockList;
                if (!cityBlockInfo.areaBlocksDic.TryGetValue(bbEntry.area_names, out blockList))
                {
                    blockList = new List<NewBlockEntryItem>();
                    cityBlockInfo.areaBlocksDic[bbEntry.area_names] = blockList;
                }
                blockList.Add(bbEntry);
            }
            return cityDic;
        }
        */
#endif

        ContentCompBenchControl compBenchControl = new ContentCompBenchControl();
        private void linkLabelSetting_Click(object sender, EventArgs e)
        {
            compBenchControl.FillShow(toolStripDropDownCompBench);
            System.Drawing.Point pt = new System.Drawing.Point(linkLabelSetting.Width, linkLabelSetting.Height);
            toolStripDropDownCompBench.Show(linkLabelSetting, pt, ToolStripDropDownDirection.BelowRight);
        }

        private void PrepareForDicShow_CompBench()
        {
            if (isProvUser)
            {
                curGraphShowLevel_CompBench = 0;//default
            }
            else
            {
                if (curCompBenchCityInfo.compBenchOfCityDic.Count > 0)
                {
                    curGraphShowLevel_CompBench = 1;
                    foreach (CompBenchTimePeriodInfo cityInfo in curCompBenchCityInfo.compBenchOfCityDic.Values)
                    {
                        curCompBenchCity = cityInfo;
                        if (true)
                        {
                            break;
                        }
                    }
                }
                else
                {
                    curCompBenchCity = new CompBenchTimePeriodInfo();
                }
            }
        }
        private void refreshShowReport_CompBench()
        {
            if (curGraphShowLevel_CompBench == 0)
            {
                //==========chart
                chartControlCompBench.Series.Clear();
                chartControlCompBench.Titles.Clear();
                ChartTitle title = new ChartTitle();
                title.Text = "全省竞对分析情况";
                chartControlCompBench.Titles.Add(title);
                Series serial = new Series("全省竞对分析情况", ViewType.Pie);
                ((PieSeriesView)serial.View).ExplodeMode = PieExplodeMode.All;
                ((PiePointOptions)serial.PointOptions).PointView = PointView.ArgumentAndValues;
                ((PiePointOptions)serial.PointOptions).PercentOptions.ValueAsPercent = false;
                ((PiePointOptions)serial.PointOptions).ValueNumericOptions.Format = NumericFormat.General;
                DataTable datInfo = new DataTable();
                datInfo.Columns.Add("地市", typeof(String));
                datInfo.Columns.Add("劣势区域数量", typeof(Int32));

                if (curCompBenchCityInfo == null)
                {
                    return;
                }
                setChartControlCompBench0(serial, datInfo);
                setDataGridViewCompBenchInfo0();
            }
            else if (curGraphShowLevel_CompBench == 1)
            {
                if (curCompBenchCity == null)
                {
                    return;
                }
                setChartControlCompBench1();
                setDataGridViewCompBenchInfo1();
            }
            else if (curGraphShowLevel_CompBench == 2)
            {
                if (curCompBenchTaskInfo == null)
                {
                    return;
                }
                setChartControlCompBench2();
                setDataGridViewCompBenchInfo2();
            }
            else if (curGraphShowLevel_CompBench == 3)
            {
                if (curCompBenchBranchInfo == null)
                {
                    return;
                }
                //==========chart
                chartControlCompBench.Titles.Clear();
                ChartTitle title = new ChartTitle();
                title.Text = curCompBenchCity.cityName + " " + "竞争对比情况";
                chartControlCompBench.Titles.Add(title);
            }
        }

        private void setChartControlCompBench0(Series serial, DataTable datInfo)
        {
            foreach (CompBenchTimePeriodInfo cityInfo in curCompBenchCityInfo.compBenchOfCityDic.Values)
            {
                string cityName = cityInfo.cityName;
                CompBenchBranchInfo compBenchBranchInfo = cityInfo.GetCompBenchBranchInfoHaveData(compBenchControl);
                if (compBenchBranchInfo == null)
                {
                    continue;
                }
                int bbNum = compBenchBranchInfo.CalcCurrentProblemNum();
                if (bbNum >= 0)
                {
                    datInfo.Rows.Add(new object[] { cityName, bbNum });
                }
            }

            serial.Visible = true;
            serial.ArgumentDataMember = "地市";
            serial.ValueScaleType = ScaleType.Numerical;
            serial.ValueDataMembers.AddRange(new string[] { "劣势区域数量" });
            serial.PointOptions.PointView = DevExpress.XtraCharts.PointView.ArgumentAndValues;
            serial.DataSource = datInfo;
            chartControlCompBench.Series.Add(serial);
        }

        private void setDataGridViewCompBenchInfo0()
        {
            //=======grid===
            dataGridViewCompBenchInfo.Visible = true;
            dataGridViewCompBenchInfo.Rows.Clear();
            dataGridViewCompBenchInfo.Columns.Clear();
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn0", "地市");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn1", "竞对分析时间");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn2", "劣势区域数量");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn3", "优势点个数");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn4", "优势点比例");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn5", "劣势点个数");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn6", "劣势点比例");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn7", "相当点个数");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn8", "相当点比例");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn9", "同优点个数");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn10", "同优点比例");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn11", "同差点个数");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn12", "同差点比例");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn13", "参考点个数");

            int indexRowAt = 0;
            foreach (CompBenchTimePeriodInfo cityInfo in curCompBenchCityInfo.compBenchOfCityDic.Values)
            {
                CompBenchBranchInfo compBenchBranchInfo = cityInfo.GetCompBenchBranchInfoHaveData(compBenchControl);
                if (compBenchBranchInfo == null)
                {
                    continue;
                }
                int bbNum = compBenchBranchInfo.CalcCurrentProblemNum();
                if (bbNum < 0)
                {
                    continue;
                }

                int index = 0;
                dataGridViewCompBenchInfo.Rows.Add(1);
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[index++].Value = cityInfo.cityName;
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[index++].Value = compBenchBranchInfo.StatTime.ToShortDateString();
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[index++].Value = bbNum;

                for (int i = 1; i <= compBenchBranchInfo.compBenchStatusDic.Count; i++)
                {
                    dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[index++].Value = compBenchBranchInfo.compBenchStatusDic[i].iGridCount;

                    if (compBenchBranchInfo.compBenchStatusDic[i].GetPercent() > 0)
                    {
                        dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[index++].Value = compBenchBranchInfo.compBenchStatusDic[i].GetPercent();
                    }
                    else
                    {
                        dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[index++].Value = "-";
                    }
                }

                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[index].Value = compBenchBranchInfo.compBenchStatusDic[1].iTotalGridCount;

                indexRowAt++;
            }
        }

        private void setChartControlCompBench1()
        {
            //==========chart
            chartControlCompBench.Series.Clear();
            chartControlCompBench.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = curCompBenchCity.cityName + "竞争分析历史情况";
            chartControlCompBench.Titles.Add(title);

            Series series1 = new Series("劣势区个数", ViewType.Line);
            //定义X轴的数据的类型。质量，数字，时间
            series1.ArgumentScaleType = ScaleType.Numerical;
            //定义线条上点的标识形状
            ((LineSeriesView)series1.View).LineMarkerOptions.Kind = MarkerKind.Triangle;
            //线条的类型，虚线，实线
            ((LineSeriesView)series1.View).LineStyle.DashStyle = DashStyle.Solid;
            series1.ArgumentScaleType = ScaleType.DateTime;

            try
            {
                foreach (int time in curCompBenchCity.taskOfTimePeriodDic.Keys)
                {
                    int bbNum = curCompBenchCity.CalcCurrentProblemNum(compBenchControl.CarrierType, compBenchControl.ServiceType, compBenchControl.KpiVsTypeName, compBenchControl.StatMathodName, compBenchControl.WindowSize, compBenchControl.AreaSize);
                    series1.Points.Add(new SeriesPoint(curCompBenchCity.taskOfTimePeriodDic[time].StatTime, new double[] { bbNum }));
                }
            }
            catch
            {
                //continue
            }
            series1.Visible = true;
            chartControlCompBench.Series.Add(series1);
        }

        private void setDataGridViewCompBenchInfo1()
        {
            //=======grid===
            dataGridViewCompBenchInfo.Visible = true;
            dataGridViewCompBenchInfo.Rows.Clear();
            dataGridViewCompBenchInfo.Columns.Clear();
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn0", "地市");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn1", "竞对分析时间");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn2", "劣势区域数量");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn3", "优势点个数");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn4", "优势点比例（%）");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn5", "劣势点个数");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn6", "劣势点比例（%）");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn7", "相当点个数");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn8", "相当点比例（%）");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn9", "同优点个数");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn10", "同优点比例（%）");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn11", "同差点个数");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn12", "同差点比例（%）");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn13", "参考点个数");

            int indexRowAt = 0;
            foreach (int time in curCompBenchCity.taskOfTimePeriodDic.Keys)
            {
                CompBenchTaskInfo compBenchTaskInfo = curCompBenchCity.taskOfTimePeriodDic[time];

                CompBenchBranchInfo compBenchBranchInfo = compBenchTaskInfo.GetCompBenchBranchInfo(compBenchControl.CarrierType, compBenchControl.ServiceType, compBenchControl.KpiVsTypeName, compBenchControl.StatMathodName, compBenchControl.WindowSize, compBenchControl.AreaSize);
                if (compBenchBranchInfo == null)
                {
                    continue;
                }
                int bbNum = curCompBenchCity.CalcCurrentProblemNum(compBenchControl.CarrierType, compBenchControl.ServiceType, compBenchControl.KpiVsTypeName, compBenchControl.StatMathodName, compBenchControl.WindowSize, compBenchControl.AreaSize);

                int index = 0;
                dataGridViewCompBenchInfo.Rows.Add(1);
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[index++].Value = curCompBenchCity.cityName;
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[index++].Value = compBenchTaskInfo.StatTime.ToShortDateString();
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[index++].Value = bbNum;

                for (int i = 1; i <= compBenchBranchInfo.compBenchStatusDic.Count; i++)
                {
                    dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[index++].Value = compBenchBranchInfo.compBenchStatusDic[i].iGridCount;

                    if (compBenchBranchInfo.compBenchStatusDic[i].GetPercent() > 0)
                    {
                        dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[index++].Value = compBenchBranchInfo.compBenchStatusDic[i].GetPercent();
                    }
                    else
                    {
                        dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[index++].Value = "-";
                    }
                }

                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[index].Value = compBenchBranchInfo.compBenchStatusDic[1].iTotalGridCount;

                indexRowAt++;
            }
        }

        private void setChartControlCompBench2()
        {
            //==========chart
            chartControlCompBench.Series.Clear();
            chartControlCompBench.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = string.Format("{0} {1} 竞争对比情况", curCompBenchCity.cityName, curCompBenchTaskInfo.StatTime.ToShortDateString());
            chartControlCompBench.Titles.Add(title);
            Series series1 = new Series("劣势区域数量", ViewType.Bar);
            foreach (CompBenchBranchInfo branchInfo in curCompBenchTaskInfo.branchOfTaskDic.Values)
            {
                int blockNum = branchInfo.CalcCurrentProblemNum();
                string showInfo = string.Format("{0} {1}", branchInfo.strServVsType, branchInfo.strValueVsType);
                SeriesPoint sp = new SeriesPoint(showInfo, new double[] { blockNum });
                sp.Tag = branchInfo;
                series1.Points.Add(sp);
            }
            series1.Visible = true;
            chartControlCompBench.Series.Add(series1);
            if (series1.Points.Count > 3)
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControlCompBench.Diagram)).AxisX.Label.Angle = 270;
            }
            else
            {
                ((DevExpress.XtraCharts.XYDiagram)(chartControlCompBench.Diagram)).AxisX.Label.Angle = 0;
            }
        }

        private void setDataGridViewCompBenchInfo2()
        {
            //=======grid===
            dataGridViewCompBenchInfo.Visible = true;
            dataGridViewCompBenchInfo.Rows.Clear();
            dataGridViewCompBenchInfo.Columns.Clear();
            //prepare for问题类型用以构建列

            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn0", "运营商对比类型");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn1", "业务对比类型");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn1", "参数对比类型");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn1", "对比模式");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn1", "窗口大小（平方米）");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn1", "区域大小（平方米）");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn1", "劣势区域个数");

            int indexRowAt = 0;
            foreach (CompBenchBranchInfo branchInfo in curCompBenchTaskInfo.branchOfTaskDic.Values)
            {
                dataGridViewCompBenchInfo.Rows.Add(1);
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[0].Value = branchInfo.strCarrierVsType;
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[1].Value = branchInfo.strServVsType;
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[2].Value = branchInfo.strValueVsType;
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[3].Value = branchInfo.strMethodVsType;
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[4].Value = branchInfo.iWindowSize;
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[5].Value = branchInfo.iAreaSize;
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[6].Value = branchInfo.CalcCurrentProblemNum();

                indexRowAt++;
            }
        }

        private void chartControlCompBench_MouseClick(object sender, MouseEventArgs e)
        {
            ChartHitInfo hitInfo = chartControlCompBench.CalcHitInfo(e.Location);
            if (hitInfo.InSeries)
            {
                SeriesPoint selPoint = hitInfo.SeriesPoint;
                if (selPoint == null)
                {
                    return;
                }

                refreshInSeries_CompBench(selPoint);
            }
            else if (hitInfo.InChartTitle)
            {
                refreshInChartTitle_CompBench(hitInfo);
            }
        }

        private void refreshInSeries_CompBench(SeriesPoint selPoint)
        {
            if (curGraphShowLevel_CompBench == 0)
            {
                string cityName = selPoint.Argument;
                CompBenchTimePeriodInfo cityinfo = null;
                if (curCompBenchCityInfo.compBenchOfCityDic.TryGetValue(cityName, out cityinfo))
                {
                    curGraphShowLevel_CompBench = 1;
                    curCompBenchCity = cityinfo;
                    refreshShowReport_CompBench();
                }
            }
            else if (curGraphShowLevel_CompBench == 1)
            {
                int itime = (int)(JavaDate.GetMilliseconds(Convert.ToDateTime(selPoint.Argument)) / 1000L);
                CompBenchTaskInfo taskInfo = null;
                if (curCompBenchCity.taskOfTimePeriodDic.TryGetValue(itime, out taskInfo))
                {
                    curGraphShowLevel_CompBench = 2;
                    curCompBenchTaskInfo = taskInfo;
                    refreshShowReport_CompBench();
                }
            }
            else if (curGraphShowLevel_CompBench == 2)
            {
                CompBenchBranchInfo branchInfo = selPoint.Tag as CompBenchBranchInfo;

                if (branchInfo != null)
                {
                    curGraphShowLevel_CompBench = 3;
                    curCompBenchBranchInfo = branchInfo;
                    refreshShowReport_CompBench();

                    addCompBenchInfo(branchInfo);
                }


                //string compBenchStr = selPoint.Argument;
                //CompBenchBranchInfo branchInfo = null;
                //if (curCompBenchTaskInfo.branchOfTaskDic.TryGetValue(compBenchStr, out branchInfo))
                //{
                //    curGraphShowLevel_CompBench = 3;
                //    curCompBenchBranchInfo = branchInfo;
                //    refreshShowReport_CompBench();
                //}
                //string branchName = selPoint.Argument;
                //if (curCompBenchTaskInfo.branchOfTaskDic.TryGetValue(branchName, out branchInfo))
                //{
                //    dataGridViewCompBenchInfo.Visible = true;
                //    dataGridViewCompBenchInfo.Rows.Clear();
                //    dataGridViewCompBenchInfo.Columns.Clear();
                //    dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn0", "节点ID");
                //    dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn1", "区域面积（平方米）");
                //    dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn2", "区域经度（左上）");
                //    dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn3", "区域纬度（左上）");
                //    dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn4", "区域经度（右下）");
                //    dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn5", "区域纬度（右下）");
                //    dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn6", "区域覆盖街道");

                //    int indexRowAt = 0;
                //    foreach (CompBenchUnit item in branchInfo.compBenchUnitDic.Values)
                //    {
                //        dataGridViewCompBenchInfo.Rows.Add(1);
                //        dataGridViewCompBenchInfo.Rows[indexRowAt].Tag = item;
                //        dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[0].Value = item.iCompUnitID;
                //        dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[1].Value = item.compBenchUnitList.Count * 6400;
                //        dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[2].Value = (item.GetUnitArea())[0];
                //        dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[3].Value = (item.GetUnitArea())[1];
                //        dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[4].Value = (item.GetUnitArea())[2];
                //        dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[5].Value = (item.GetUnitArea())[3];
                //        string streets = "";
                //        foreach (string street in item.GetStreet())
                //        {
                //            streets += street + "|";
                //        }
                //        streets = streets.Substring(0, streets.Length - 1);
                //        dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[6].Value = streets;
                //        indexRowAt++;
                //    }
                //}
            }
        }

        private void addCompBenchInfo(CompBenchBranchInfo branchInfo)
        {
            dataGridViewCompBenchInfo.Visible = true;
            dataGridViewCompBenchInfo.Rows.Clear();
            dataGridViewCompBenchInfo.Columns.Clear();
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn0", "节点ID");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn1", "区域面积（平方米）");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn2", "区域经度（左上）");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn3", "区域纬度（左上）");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn4", "区域经度（右下）");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn5", "区域纬度（右下）");
            dataGridViewCompBenchInfo.Columns.Add("ProbNumColumn6", "区域覆盖街道");

            int indexRowAt = 0;
            foreach (CompBenchUnit item in branchInfo.compBenchUnitDic.Values)
            {
                dataGridViewCompBenchInfo.Rows.Add(1);
                dataGridViewCompBenchInfo.Rows[indexRowAt].Tag = item;
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[0].Value = item.iCompUnitID;
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[1].Value = item.compBenchUnitList.Count * 6400;
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[2].Value = (item.GetUnitArea())[0];
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[3].Value = (item.GetUnitArea())[1];
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[4].Value = (item.GetUnitArea())[2];
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[5].Value = (item.GetUnitArea())[3];
                StringBuilder sb = new StringBuilder();
                foreach (string street in item.GetStreet())
                {
                    sb.Append(street + "|");
                }
                string streets = sb.ToString();
                if (streets.Length > 0)
                {
                    streets = streets.Substring(0, streets.Length - 1);
                }
                dataGridViewCompBenchInfo.Rows[indexRowAt].Cells[6].Value = streets;
                indexRowAt++;
            }
        }

        private void refreshInChartTitle_CompBench(ChartHitInfo hitInfo)
        {
            DockableTitle dockTitle = hitInfo.ChartTitle as DockableTitle;
            if (dockTitle != null)
            {
                if (curGraphShowLevel_CompBench == 3)
                {
                    curGraphShowLevel_CompBench = 2;
                    refreshShowReport_CompBench();
                }
                else if (curGraphShowLevel_CompBench == 2)
                {
                    curGraphShowLevel_CompBench = 1;
                    refreshShowReport_CompBench();
                }
                else if (curGraphShowLevel_CompBench == 1)
                {
                    if (MainModel.User.DBID == -1)
                    {
                        curGraphShowLevel_CompBench = 0;
                        refreshShowReport_CompBench();
                    }
                }
                else
                {
                    //
                }
            }
        }

        private void chartControlCompBench_MouseMove(object sender, MouseEventArgs e)
        {
            ChartHitInfo hitInfo = chartControlCompBench.CalcHitInfo(e.Location);

            bool hide = true;
            if (curGraphShowLevel_CompBench == 0)
            {
                if (hitInfo.InSeries && hitInfo.SeriesPoint != null)
                {
                    string cityname = hitInfo.SeriesPoint.Argument;
                    toolTipController.ShowHint("单击进入地市：" + cityname, chartControlMain.PointToScreen(e.Location));
                    hide = false;
                }
            }
            else if (curGraphShowLevel_CompBench == 1)
            {
                if (hitInfo.InChartTitle && MainModel.User.DBID == -1)
                {
                    toolTipController.ShowHint("单击返回全省情况查看", chartControlMain.PointToScreen(e.Location));
                    hide = false;
                }
            }
            else if (curGraphShowLevel_CompBench == 2)
            {
                hide = judgeMainSeries(e, hitInfo, hide);
            }
            else if (curGraphShowLevel_CompBench == 3)
            {
                hide = judgeMainSeries(e, hitInfo, hide);
            }
            else
            {
                //
            }
            if (hide)
            {
                toolTipController.HideHint();
            }
        }

        private bool judgeMainSeries(MouseEventArgs e, ChartHitInfo hitInfo, bool hide)
        {
            if (hitInfo.InSeries && hitInfo.SeriesPoint != null)
            {
                CompBenchBranchInfo cbbi = hitInfo.SeriesPoint.Tag as CompBenchBranchInfo;
                if (cbbi != null)
                {
                    string info = string.Format("分析时间：{0}\n竞对运营商：{1}\n竞对业务类型：{2}\n竞对指标：{3}\n竞对方法：{4}\n竞对窗口大小（平方米）：{5}\n竞对区域大小（平方米）：{6}", cbbi.StatTime.ToShortDateString(), cbbi.strCarrierVsType, cbbi.strServVsType, cbbi.strValueVsType, cbbi.strMethodVsType, cbbi.iWindowSize, cbbi.iAreaSize);
                    toolTipController.ShowHint("竞争对比条件： \n" + info, chartControlMain.PointToScreen(e.Location));
                    hide = false;
                }
            }

            return hide;
        }

        private void toolStripDropDown_Closed(object sender, ToolStripDropDownClosedEventArgs e)
        {
            PrepareForDicShow_CompBench();
            refreshShowReport_CompBench();
        }
#endregion

        private void expAll2Xls_Click(object sender, EventArgs e)
        {
            WelcomForm welFrm = this.Parent.Parent.Parent as WelcomForm;
            if (welFrm != null)
            {
                welFrm.ExpAll2Xls();
            }
        }

        private void exp2Xls_Click(object sender, EventArgs e)
        {
            SelectSavePath();
        }

        public class BbExport2XlsParam
        {
            public ExcelControl excelApp { get; set; }
            public string pictPath { get; set; }
            public DataGridView dgv { get; set; }
            public ChartControl chartCtrl { get; set; }
            public float pictWidth { get; set; }
            public float pictHeight { get; set; }
        }

        static class NewCountMonthHelper
        {
            class CountMonthInfo
            {
                public int CountMonth { get; set; }
                public int CreatedCountMonth { get; set; }
                public int ClosedCountMonth { get; set; }
                public int DateValueMonth { get; set; }
                public DateTime Time { get; set; }

                public CountMonthInfo(DateTime dt, int dateValueMonth)
                {
                    Time = dt;
                    DateValueMonth = dateValueMonth;
                }
            }

            internal static List<MonthResult> CalcBlockInfoByExValidate(Dictionary<string, List<NewBlockEntryItem>> areaBlocksDic)
            {
                List<MonthResult> results = new List<MonthResult>();
                DateTime dt = DateTime.Now;
                DateTime dtLastOneMonth = getLastMonth(dt);
                DateTime dtLastTwoMonth = getLastMonth(dtLastOneMonth);
                DateTime dtLastThreeMonth = getLastMonth(dtLastTwoMonth);
                int dateValueThisMonth = (int)(JavaDate.GetMilliseconds(new DateTime(dt.Year, dt.Month, 1)) / 1000L);
                int dateValueLastOneMonth = (int)(JavaDate.GetMilliseconds(dtLastOneMonth) / 1000L);
                int dateValueLastTwoMonth = (int)(JavaDate.GetMilliseconds(dtLastTwoMonth) / 1000L);
                int dateValueLastThreeMonth = (int)(JavaDate.GetMilliseconds(dtLastThreeMonth) / 1000L);

                CountMonthInfo ThisMonth = new CountMonthInfo(dt, dateValueThisMonth);
                CountMonthInfo OneMonth = new CountMonthInfo(dtLastOneMonth, dateValueLastOneMonth);
                CountMonthInfo TwoMonth = new CountMonthInfo(dtLastTwoMonth, dateValueLastTwoMonth);
                CountMonthInfo ThreeMonth = new CountMonthInfo(dtLastThreeMonth, dateValueLastThreeMonth);
                foreach (List<NewBlockEntryItem> blockList in areaBlocksDic.Values)
                {
                    foreach (NewBlockEntryItem bbItem in blockList)
                    {
                        addCreatedCount(ThisMonth, OneMonth, TwoMonth, ThreeMonth, bbItem);

                        if (bbItem.status == 4)
                        {
                            addClosedCount(ThisMonth, OneMonth, TwoMonth, ThreeMonth, bbItem);

                            addCount(ThisMonth, OneMonth, TwoMonth, ThreeMonth, bbItem);
                        }
                        else
                        {
                            addOtherStatusCount(ThisMonth, OneMonth, TwoMonth, ThreeMonth, bbItem);
                        }
                    }
                }
                //原公式：当月黑点新增率=（本月创建黑点 - 本月关闭黑点）/上月遗留黑点（已创建）
                //修改为：当月黑点新增率=（本月创建黑点 ）/(上月遗留黑点（已创建）+本月创建黑点)
                MonthResult thisMonth = new MonthResult(ThisMonth.Time, ThisMonth.CountMonth, 1.0 * (ThisMonth.CreatedCountMonth) / (OneMonth.CountMonth + ThisMonth.CreatedCountMonth),
                    1.0 * ThisMonth.ClosedCountMonth / (OneMonth.CountMonth + ThisMonth.CreatedCountMonth));
                results.Add(thisMonth);
                MonthResult lastOneMonth = new MonthResult(OneMonth.Time, OneMonth.CountMonth, 1.0 * (OneMonth.CreatedCountMonth) / (TwoMonth.CountMonth + OneMonth.CreatedCountMonth),
                    1.0 * OneMonth.ClosedCountMonth / (TwoMonth.CountMonth + OneMonth.CreatedCountMonth));
                results.Add(lastOneMonth);
                MonthResult lastTwoMonth = new MonthResult(TwoMonth.Time, TwoMonth.CountMonth, 1.0 * (TwoMonth.CreatedCountMonth) / (ThreeMonth.CountMonth + TwoMonth.CreatedCountMonth),
                    1.0 * TwoMonth.ClosedCountMonth / (ThreeMonth.CountMonth + TwoMonth.CreatedCountMonth));
                results.Add(lastTwoMonth);
                return results;
            }

            private static void addCreatedCount(CountMonthInfo ThisMonth, CountMonthInfo OneMonth, CountMonthInfo TwoMonth, CountMonthInfo ThreeMonth, NewBlockEntryItem bbItem)
            {
                if (bbItem.created_date >= ThisMonth.DateValueMonth)
                {
                    ThisMonth.CreatedCountMonth++;
                }
                else if (bbItem.created_date < ThisMonth.DateValueMonth && bbItem.created_date >= OneMonth.DateValueMonth)
                {
                    OneMonth.CreatedCountMonth++;
                }
                else if (bbItem.created_date < OneMonth.DateValueMonth && bbItem.created_date >= TwoMonth.DateValueMonth)
                {
                    TwoMonth.CreatedCountMonth++;
                }
                else if (bbItem.created_date < TwoMonth.DateValueMonth && bbItem.created_date >= ThreeMonth.DateValueMonth)
                {
                    ThreeMonth.CreatedCountMonth++;
                }
            }

            private static void addClosedCount(CountMonthInfo ThisMonth, CountMonthInfo OneMonth, CountMonthInfo TwoMonth, CountMonthInfo ThreeMonth, NewBlockEntryItem bbItem)
            {
                if (bbItem.closed_date >= ThisMonth.DateValueMonth)
                {
                    ThisMonth.ClosedCountMonth++;
                }
                else if (bbItem.closed_date < ThisMonth.DateValueMonth && bbItem.closed_date >= OneMonth.DateValueMonth)
                {
                    OneMonth.ClosedCountMonth++;
                }
                else if (bbItem.closed_date < OneMonth.DateValueMonth && bbItem.closed_date >= TwoMonth.DateValueMonth)
                {
                    TwoMonth.ClosedCountMonth++;
                }
                else if (bbItem.closed_date < TwoMonth.DateValueMonth && bbItem.closed_date >= ThreeMonth.DateValueMonth)
                {
                    ThreeMonth.ClosedCountMonth++;
                }
            }

            private static void addCount(CountMonthInfo ThisMonth, CountMonthInfo OneMonth, CountMonthInfo TwoMonth, CountMonthInfo ThreeMonth, NewBlockEntryItem bbItem)
            {
                if (bbItem.closed_date >= ThisMonth.DateValueMonth)
                {
                    if (bbItem.created_date < ThisMonth.DateValueMonth)
                    {
                        OneMonth.CountMonth++;
                    }
                    if (bbItem.created_date < OneMonth.DateValueMonth)
                    {
                        TwoMonth.CountMonth++;
                    }
                    if (bbItem.created_date < TwoMonth.DateValueMonth)
                    {
                        ThreeMonth.CountMonth++;
                    }
                }
                else if (bbItem.closed_date < ThisMonth.DateValueMonth && bbItem.closed_date >= OneMonth.DateValueMonth)
                {
                    if (bbItem.created_date < OneMonth.DateValueMonth)
                    {
                        TwoMonth.CountMonth++;
                    }
                    if (bbItem.created_date < TwoMonth.DateValueMonth)
                    {
                        ThreeMonth.CountMonth++;
                    }
                }
                else if (bbItem.closed_date < OneMonth.DateValueMonth && bbItem.closed_date >= TwoMonth.DateValueMonth
                    && bbItem.created_date < TwoMonth.DateValueMonth)
                {
                    ThreeMonth.CountMonth++;
                }
            }

            private static void addOtherStatusCount(CountMonthInfo ThisMonth, CountMonthInfo OneMonth, CountMonthInfo TwoMonth, CountMonthInfo ThreeMonth, NewBlockEntryItem bbItem)
            {
                if (bbItem.created_date >= ThisMonth.DateValueMonth)
                {
                    ThisMonth.CountMonth++;
                }
                else if (bbItem.created_date < ThisMonth.DateValueMonth && bbItem.created_date >= OneMonth.DateValueMonth)
                {
                    ThisMonth.CountMonth++;
                    OneMonth.CountMonth++;
                }
                else if (bbItem.created_date < OneMonth.DateValueMonth && bbItem.created_date >= TwoMonth.DateValueMonth)
                {
                    ThisMonth.CountMonth++;
                    OneMonth.CountMonth++;
                    TwoMonth.CountMonth++;
                }
                else
                {
                    ThisMonth.CountMonth++;
                    OneMonth.CountMonth++;
                    TwoMonth.CountMonth++;
                    ThreeMonth.CountMonth++;
                }
            }

            private static DateTime getLastMonth(DateTime dt)
            {
                int year = dt.Year;
                int month = dt.Month;
                if (month == 1)
                {
                    return new DateTime(year - 1, 12, 1);
                }
                else
                {
                    return new DateTime(year, month - 1, 1);
                }
            }
        }

        public class CityBlackBlockInfo
        {
            public string cityName { get; set; }
            /// <summary>
            /// 各个片区名->黑点详细列表
            /// </summary>
            internal Dictionary<string, List<NewBlockEntryItem>> areaBlocksDic = new Dictionary<string, List<NewBlockEntryItem>>();

            internal int CalcCurrentBlockNum()
            {
                int count = 0;
                foreach (List<NewBlockEntryItem> blockList in areaBlocksDic.Values)
                {
                    foreach (NewBlockEntryItem block in blockList)
                    {
                        if (block.status != 4)
                        {
                            count++;
                        }
                    }
                }
                return count;
            }
        }

        internal class ESCityInfo
        {
            internal string cityName;
            internal Dictionary<string, ESAreaInfo> areaOfCityDic = new Dictionary<string, ESAreaInfo>();

            internal void AddPopEsEvent(PopESEvent esEvt)
            {
                ESAreaInfo areaInfo = null;
                if (!areaOfCityDic.TryGetValue(esEvt.strname, out areaInfo))
                {
                    areaInfo = new ESAreaInfo();
                    areaInfo.areaName = esEvt.strname;
                    areaOfCityDic[esEvt.strname] = areaInfo;
                }
                areaInfo.AddPopEsEvent(esEvt);
            }

            internal int CalcCurrentProblemNum()
            {
                int count = 0;
                foreach (ESAreaInfo area in areaOfCityDic.Values)
                {
                    count += area.CalcCurrentProblemNum();
                }
                return count;
            }

            internal Dictionary<string, int> CalcProblemCountByTask()
            {
                Dictionary<string, int> dic = new Dictionary<string, int>();
                foreach (ESAreaInfo area in areaOfCityDic.Values)
                {
                    Dictionary<string, int> dicOfArea = area.CalcProblemCountByTask();
                    foreach (string taskName in dicOfArea.Keys)
                    {
                        int count = 0;
                        if (!dic.TryGetValue(taskName, out count))
                        {
                            dic[taskName] = dicOfArea[taskName];
                        }
                        else
                        {
                            dic[taskName] = (count + dicOfArea[taskName]);
                        }
                    }
                }
                return dic;
            }
        };
        internal class ESAreaInfo
        {
            internal string areaName;
            internal Dictionary<string, ESTaskInfo> taskOfAreaDic = new Dictionary<string, ESTaskInfo>();

            internal void AddPopEsEvent(PopESEvent esEvt)
            {
                ESTaskInfo taskInfo = null;
                if (!taskOfAreaDic.TryGetValue(esEvt.strtaskname, out taskInfo))
                {
                    taskInfo = new ESTaskInfo();
                    taskInfo.taskName = esEvt.strtaskname;
                    taskOfAreaDic[esEvt.strtaskname] = taskInfo;
                }
                taskInfo.AddPopEsEvent(esEvt);
            }

            internal int CalcCurrentProblemNum()
            {
                int count = 0;
                foreach (ESTaskInfo task in taskOfAreaDic.Values)
                {
                    count += task.CalcCurrentProblemNum();
                }
                return count;
            }

            internal Dictionary<string, int> CalcProblemCountByTask()
            {
                Dictionary<string, int> dic = new Dictionary<string, int>();
                foreach (ESTaskInfo area in taskOfAreaDic.Values)
                {
                    dic[area.taskName] = area.CalcCurrentProblemNum();
                }
                return dic;
            }
        };
        internal class ESTaskInfo
        {
            internal string taskName;
            internal Dictionary<string, ESBranchInfo> branchOfTaskDic = new Dictionary<string, ESBranchInfo>();

            internal void AddPopEsEvent(PopESEvent esEvt)
            {
                ESBranchInfo branchInfo = null;
                if (!branchOfTaskDic.TryGetValue(esEvt.strbranch, out branchInfo))
                {
                    branchInfo = new ESBranchInfo();
                    branchInfo.branchName = esEvt.strbranch;
                    branchOfTaskDic[esEvt.strbranch] = branchInfo;
                }
                branchInfo.AddPopEsEvent(esEvt);
            }

            internal int CalcCurrentProblemNum()
            {
                int count = 0;
                foreach (ESBranchInfo branch in branchOfTaskDic.Values)
                {
                    count += branch.CalcCurrentProblemNum();
                }
                return count;
            }

            internal Dictionary<string, int> CalcProblemCountByBranch()
            {
                Dictionary<string, int> dic = new Dictionary<string, int>();
                foreach (ESBranchInfo branch in branchOfTaskDic.Values)
                {
                    dic[branch.branchName] = branch.CalcCurrentProblemNum();
                }
                return dic;
            }
        };
        internal class ESBranchInfo
        {
            internal string branchName;
            internal List<PopESEvent> esDetailList = new List<PopESEvent>();

            internal void AddPopEsEvent(PopESEvent esEvt)
            {
                esDetailList.Add(esEvt);
            }

            internal int CalcCurrentProblemNum()
            {
                return esDetailList.Count;
            }
        };

        internal class BlockResultForShow
        {
            internal Dictionary<string, Dictionary<string, CityBlackBlockInfo>> blockTypeCityBlockInfoDic;
            internal Dictionary<string, ESCityInfo> esCityInfoDic;
            internal CompBenchCityInfo compBenchCityInfo { get; set; }
        };

#region 竞对分析

        internal class CompBenchStatusEntryItem
        {
            public int iDBid;
            public int iDate;
            public string strCarrierVsType;
            public string strServVsType;
            public string strValueVsType;
            public string strMethodVsType;
            public int iWindowSize;
            public int iAreaSize;
            public int iCompStatus;
            public int iGridCount;
            public int iTotalGridCount;

            public string dbName;

            internal static CompBenchStatusEntryItem ReadResultItemFrom(Content content)
            {
                CompBenchStatusEntryItem item = new CompBenchStatusEntryItem();
                item.iDBid = content.GetParamInt();
                item.iDate = content.GetParamInt();
                item.strCarrierVsType = content.GetParamString();
                item.strServVsType = content.GetParamString();
                item.strValueVsType = content.GetParamString();
                item.strMethodVsType = content.GetParamString();
                item.iWindowSize = content.GetParamInt();
                item.iAreaSize = content.GetParamInt();
                item.iCompStatus = content.GetParamInt();
                item.iGridCount = content.GetParamInt();
                item.iTotalGridCount = content.GetParamInt();

                item.dbName = DistrictManager.GetInstance().getDistrictName(item.iDBid);

                return item;
            }

            internal CompBenchStatusEntryItem cloneInstance()
            {
                CompBenchStatusEntryItem clone = new CompBenchStatusEntryItem();
                clone.iDBid = iDBid;
                clone.iDate = iDate;
                clone.strCarrierVsType = strCarrierVsType;
                clone.strServVsType = strServVsType;
                clone.strValueVsType = strValueVsType;
                clone.strMethodVsType = strMethodVsType;
                clone.iWindowSize = iWindowSize;
                clone.iAreaSize = iAreaSize;
                clone.iCompStatus = iCompStatus;
                clone.iGridCount = iGridCount;
                clone.iTotalGridCount = iTotalGridCount;
                clone.dbName = dbName;

                return clone;
            }
        };

        internal class CompBenchUnitEntryItem
        {
            public int iDBid;
            public int iDate;
            public string strCarrierVsType;
            public string strServVsType;
            public string strValueVsType;
            public string strMethodVsType;
            public int iWindowSize;
            public int iAreaSize;
            public int iCompUnitID;
            public int iUnitID;
            public int iUnitStatus;
            public int iGoodBadStatus;
            public int iltlongitude;
            public int iltlatitude;
            public int ibrlongitude;
            public int ibrlatitude;
            public int ibetterdays;
            public int iworsedays;
            public string strRoadName;

            public string dbName;


            internal static CompBenchUnitEntryItem ReadResultItemFrom(Content content)
            {
                CompBenchUnitEntryItem item = new CompBenchUnitEntryItem();
                item.iDBid = content.GetParamInt();
                item.iDate = content.GetParamInt();
                item.strCarrierVsType = content.GetParamString();
                item.strServVsType = content.GetParamString();
                item.strValueVsType = content.GetParamString();
                item.strMethodVsType = content.GetParamString();
                item.iWindowSize = content.GetParamInt();
                item.iAreaSize = content.GetParamInt();
                item.iCompUnitID = content.GetParamInt();
                item.iUnitID = content.GetParamInt();
                item.iUnitStatus = content.GetParamInt();
                item.iGoodBadStatus = content.GetParamInt();
                item.iltlongitude = content.GetParamInt();
                item.iltlatitude = content.GetParamInt();
                item.ibrlongitude = content.GetParamInt();
                item.ibrlatitude = content.GetParamInt();
                item.ibetterdays = content.GetParamInt();
                item.iworsedays = content.GetParamInt();
                item.strRoadName = content.GetParamString();

                item.dbName = DistrictManager.GetInstance().getDistrictName(item.iDBid);

                return item;
            }

            internal CompBenchUnitEntryItem cloneInstance()
            {
                CompBenchUnitEntryItem clone = new CompBenchUnitEntryItem();
                clone.iDBid = iDBid;
                clone.iDate = iDate;
                clone.strCarrierVsType = strCarrierVsType;
                clone.strServVsType = strServVsType;
                clone.strValueVsType = strValueVsType;
                clone.strMethodVsType = strMethodVsType;
                clone.iWindowSize = iWindowSize;
                clone.iAreaSize = iAreaSize;
                clone.iCompUnitID = iCompUnitID;
                clone.iUnitID = iUnitID;
                clone.iUnitStatus = iUnitStatus;
                clone.iGoodBadStatus = iGoodBadStatus;
                clone.iltlongitude = iltlongitude;
                clone.iltlatitude = iltlatitude;
                clone.ibrlongitude = ibrlongitude;
                clone.ibrlatitude = ibrlatitude;
                clone.ibetterdays = ibetterdays;
                clone.iworsedays = iworsedays;
                clone.strRoadName = strRoadName;
                clone.dbName = dbName;

                return clone;
            }
        };

        internal class CompBenchCityInfo
        {
            internal Dictionary<string, CompBenchTimePeriodInfo> compBenchOfCityDic = new Dictionary<string, CompBenchTimePeriodInfo>();

            internal void AddCompBenchStatus(CompBenchStatusEntryItem item)
            {
                CompBenchTimePeriodInfo info = null;
                if (!compBenchOfCityDic.TryGetValue(item.dbName, out info))
                {
                    info = new CompBenchTimePeriodInfo();
                    info.cityName = item.dbName;
                    compBenchOfCityDic[item.dbName] = info;
                }
                info.AddCompBenchStatus(item);
            }

            internal void AddCompBenchUnit(CompBenchUnitEntryItem item)
            {
                CompBenchTimePeriodInfo info = null;
                if (compBenchOfCityDic.TryGetValue(item.dbName, out info))
                {
                    info.AddCompBenchUnit(item);
                }
            }
        };

        internal class CompBenchTimePeriodInfo
        {
            internal string cityName;
            internal Dictionary<int, CompBenchTaskInfo> taskOfTimePeriodDic = new Dictionary<int, CompBenchTaskInfo>();

            internal void AddCompBenchStatus(CompBenchStatusEntryItem item)
            {
                CompBenchTaskInfo taskInfo = null;
                if (!taskOfTimePeriodDic.TryGetValue(item.iDate, out taskInfo))
                {
                    taskInfo = new CompBenchTaskInfo();
                    taskInfo.itime = item.iDate;
                    taskOfTimePeriodDic[taskInfo.itime] = taskInfo;
                }
                taskInfo.AddCompBenchStatus(item);
            }

            internal void AddCompBenchUnit(CompBenchUnitEntryItem item)
            {
                CompBenchTaskInfo info = null;
                if (taskOfTimePeriodDic.TryGetValue(item.iDate, out info))
                {
                    info.AddCompBenchUnit(item);
                }
            }

            internal CompBenchTaskInfo CurCompBenchTaskInfo
            {
                get
                {
                    int maxkey = -1;
                    foreach (int key in taskOfTimePeriodDic.Keys)
                    {
                        maxkey = Math.Max(maxkey, key);
                    }
                    if (maxkey != -1)
                    {
                        return taskOfTimePeriodDic[maxkey];
                    }
                    else
                    {
                        return null;
                    }
                }
            }

            internal CompBenchBranchInfo GetCompBenchBranchInfoHaveData(ContentCompBenchControl ccbc)
            {
                string key = CompBenchTaskInfo.GetKey(ccbc);
                int[] keys = new int[taskOfTimePeriodDic.Keys.Count];
                taskOfTimePeriodDic.Keys.CopyTo(keys, 0);
                Array.Sort(keys);

                for (int i = keys.Length - 1; i >= 0; i--)
                {
                    CompBenchTaskInfo compBenchTaskInfo = taskOfTimePeriodDic[keys[i]];
                    CompBenchBranchInfo cbbi = null;
                    if (compBenchTaskInfo.branchOfTaskDic.TryGetValue(key, out cbbi))
                    {
                        return cbbi;
                    }
                }
                return null;
            }

            internal int CalcCurrentProblemNum(ContentCompBenchControl compBenchControl)
            {
                return CalcCurrentProblemNum(compBenchControl.CarrierType, compBenchControl.ServiceType, compBenchControl.KpiVsTypeName, compBenchControl.StatMathodName, compBenchControl.WindowSize, compBenchControl.AreaSize);
            }

            internal int CalcCurrentProblemNum(string strCarrierVsType, string strServVsType, string strValueVsType, string strMethodVsType, int iWindowSize, int iAreaSize)
            {
                CompBenchTaskInfo curCBTI = CurCompBenchTaskInfo;
                if (curCBTI == null)
                {
                    return -1;
                }
                return curCBTI.CalcCurrentProblemNum(strCarrierVsType, strServVsType, strValueVsType, strMethodVsType, iWindowSize, iAreaSize);
            }
        };

        internal class CompBenchTaskInfo
        {
            internal int itime;
            internal Dictionary<string, CompBenchBranchInfo> branchOfTaskDic = new Dictionary<string, CompBenchBranchInfo>();

            internal void AddCompBenchStatus(CompBenchStatusEntryItem item)
            {
                CompBenchBranchInfo branchInfo = null;
                string key = GetKey(item);
                if (!branchOfTaskDic.TryGetValue(key, out branchInfo))
                {
                    branchInfo = new CompBenchBranchInfo();
                    branchInfo.strCarrierVsType = item.strCarrierVsType;
                    branchInfo.strServVsType = item.strServVsType;
                    branchInfo.strValueVsType = item.strValueVsType;
                    branchInfo.strMethodVsType = item.strMethodVsType;
                    branchInfo.iWindowSize = item.iWindowSize;
                    branchInfo.iAreaSize = item.iAreaSize;
                    branchInfo.iDate = item.iDate;

                    branchOfTaskDic[key] = branchInfo;
                }
                branchInfo.AddCompBenchStatus(item);
            }

            internal void AddCompBenchUnit(CompBenchUnitEntryItem item)
            {
                CompBenchBranchInfo info = null;
                string key = GetKey(item);
                if (branchOfTaskDic.TryGetValue(key, out info))
                {
                    info.AddCompBenchUnit(item);
                }
            }

            internal DateTime StatTime
            {
                get
                {
                    return JavaDate.GetDateTimeFromMilliseconds(1000L * itime);
                }
            }

            internal static string GetKey(ContentCompBenchControl item)
            {
                return GetKey(item.CarrierType, item.ServiceType, item.KpiVsTypeName, item.StatMathodName, item.WindowSize, item.AreaSize);
            }

            internal static string GetKey(CompBenchStatusEntryItem item)
            {
                return GetKey(item.strCarrierVsType, item.strServVsType, item.strValueVsType, item.strMethodVsType, item.iWindowSize, item.iAreaSize);
            }

            internal static string GetKey(CompBenchUnitEntryItem item)
            {
                return GetKey(item.strCarrierVsType, item.strServVsType, item.strValueVsType, item.strMethodVsType, item.iWindowSize, item.iAreaSize);
            }

            internal static string GetKey(string strCarrierVsType, string strServVsType, string strValueVsType, string strMethodVsType, int iWindowSize, int iAreaSize)
            {
                return strCarrierVsType + "," + strServVsType + "," + strValueVsType + "," + strMethodVsType + "," + iWindowSize + "," + iAreaSize;
            }

            internal int CalcCurrentProblemNum(string strCarrierVsType, string strServVsType, string strValueVsType, string strMethodVsType, int iWindowSize, int iAreaSize)
            {
                string key = GetKey(strCarrierVsType, strServVsType, strValueVsType, strMethodVsType, iWindowSize, iAreaSize);
                CompBenchBranchInfo compBenchBrachInfo = null;
                if (branchOfTaskDic.TryGetValue(key, out compBenchBrachInfo))
                {
                    return compBenchBrachInfo.CalcCurrentProblemNum();
                }
                return -1;
            }

            internal int CalcCurrentProblemNum(ContentCompBenchControl item)
            {
                string key = GetKey(item);
                CompBenchBranchInfo compBenchBrachInfo = null;
                if (branchOfTaskDic.TryGetValue(key, out compBenchBrachInfo))
                {
                    return compBenchBrachInfo.CalcCurrentProblemNum();
                }
                return -1;
            }

            internal CompBenchBranchInfo GetCompBenchBranchInfo(string strCarrierVsType, string strServVsType, string strValueVsType, string strMethodVsType, int iWindowSize, int iAreaSize)
            {
                string key = GetKey(strCarrierVsType, strServVsType, strValueVsType, strMethodVsType, iWindowSize, iAreaSize);
                CompBenchBranchInfo compBenchBrachInfo = null;
                if (branchOfTaskDic.TryGetValue(key, out compBenchBrachInfo))
                {
                    return compBenchBrachInfo;
                }
                return null;
            }

            internal CompBenchBranchInfo GetCompBenchBranchInfo(ContentCompBenchControl item)
            {
                string key = GetKey(item);
                CompBenchBranchInfo compBenchBrachInfo = null;
                if (branchOfTaskDic.TryGetValue(key, out compBenchBrachInfo))
                {
                    return compBenchBrachInfo;
                }
                return null;
            }


        };


        internal class CompBenchBranchInfo
        {
            internal int iDate;

            internal string strCarrierVsType;
            internal string strServVsType;
            internal string strValueVsType;
            internal string strMethodVsType;
            internal int iWindowSize;
            internal int iAreaSize;

            internal Dictionary<int, CompBenchStatus> compBenchStatusDic = new Dictionary<int, CompBenchStatus>();

            internal Dictionary<int, CompBenchUnit> compBenchUnitDic = new Dictionary<int, CompBenchUnit>();

            internal void AddCompBenchStatus(CompBenchStatusEntryItem item)
            {
                CompBenchStatus compBenchStatus = null;
                if (!compBenchStatusDic.TryGetValue(item.iCompStatus, out compBenchStatus))
                {
                    compBenchStatus = new CompBenchStatus();
                    compBenchStatus.iCompStatus = item.iCompStatus;
                    compBenchStatusDic[item.iCompStatus] = compBenchStatus;
                }
                compBenchStatus.AddCompBenchStatus(item);
            }

            internal void AddCompBenchUnit(CompBenchUnitEntryItem item)
            {
                CompBenchUnit info = null;
                if (!compBenchUnitDic.TryGetValue(item.iCompUnitID, out info))
                {
                    info = new CompBenchUnit();
                    info.iCompUnitID = item.iCompUnitID;
                    compBenchUnitDic[info.iCompUnitID] = info;
                }
                info.AddCompBenchUnit(item);
            }

            public int CalcCurrentProblemNum()
            {
                return compBenchUnitDic.Count;
            }

            internal string GetCompBenchBranchName()
            {
                return strCarrierVsType + "," + strServVsType + "," + strValueVsType + "," + strMethodVsType + "," + iWindowSize + "," + iAreaSize;
            }

            internal DateTime StatTime
            {
                get
                {
                    return JavaDate.GetDateTimeFromMilliseconds(1000L * iDate);
                }
            }
        };

        internal class CompBenchUnit
        {
            internal int iCompUnitID;
            internal List<CompBenchUnitEntryItem> compBenchUnitList = new List<CompBenchUnitEntryItem>();

            internal void AddCompBenchUnit(CompBenchUnitEntryItem item)
            {
                compBenchUnitList.Add(item);
            }

            int[] res = null;
            internal int[] GetUnitArea()
            {
                if (res != null)
                {
                    return res;
                }

                int tllongitude = int.MaxValue;
                int tllatitude = int.MinValue;
                int brlongitude = int.MinValue;
                int brlatitude = int.MaxValue;
                foreach (CompBenchUnitEntryItem item in compBenchUnitList)
                {
                    tllongitude = item.iltlongitude < tllongitude ? item.iltlongitude : tllongitude;
                    tllatitude = item.iltlatitude > tllatitude ? item.iltlatitude : tllatitude;
                    brlongitude = item.ibrlongitude > brlongitude ? item.ibrlongitude : brlongitude;
                    brlatitude = item.ibrlatitude < brlatitude ? item.ibrlatitude : brlatitude;
                }

                res = new int[4];
                res[0] = tllongitude;
                res[1] = tllatitude;
                res[2] = brlongitude;
                res[3] = brlatitude;
                return res;
            }

            List<string> streetList = null;
            internal List<string> GetStreet()
            {
                if (streetList != null)
                {
                    return streetList;
                }

                Dictionary<string, int> streetDic = new Dictionary<string, int>();
                foreach (CompBenchUnitEntryItem item in compBenchUnitList)
                {
                    string[] streets = item.strRoadName.Split('|');
                    foreach (string street in streets)
                    {
                        if (street.Length == 0)
                        {
                            continue;
                        }
                        if (!streetDic.ContainsKey(street))
                        {
                            streetDic[street] = 1;
                        }
                    }
                }

                streetList = new List<string>();
                foreach (string street in streetDic.Keys)
                {
                    streetList.Add(street);
                }
                return streetList;
            }
        }

        internal class CompBenchStatus
        {
            internal int iCompStatus;
            internal int iGridCount;
            internal int iTotalGridCount;

            public CompBenchStatus()
            {
                iGridCount = 0;
                iTotalGridCount = 0;
            }

            internal void AddCompBenchStatus(CompBenchStatusEntryItem item)
            {
                iGridCount += item.iGridCount;
                iTotalGridCount += item.iTotalGridCount;
            }

            internal float GetPercent()
            {
                return iTotalGridCount > 0 ? (float)iGridCount / iTotalGridCount * 100 : -1;
            }
        }

#endregion

        internal class MonthResult
        {
            public MonthResult(DateTime dateTime, int count, double increaseRate, double eliminateRate)
            {
                this.year = dateTime.Year;
                this.month = dateTime.Month;
                this.count = count;
                this.increaseRate = increaseRate;
                this.eliminateRate = eliminateRate;
            }

            public int year;
            public int month;
            public int count;
            private readonly double increaseRate;
            public string IncreaseRateString
            {
                get { return (Math.Round(increaseRate * 100, 2)).ToString() + "%"; }
            }
            private readonly double eliminateRate;
            public string EliminateRateString
            {
                get { return (Math.Round(eliminateRate * 100, 2)).ToString() + '%'; }
            }
        }

        internal class NewBlockEntryItem
        {
            public int dbid;
            public string type;
            public int block_id;
            public int status;
            public int created_date;
            public int closed_date;
            public string area_names;
            public int evtnum;

            internal static NewBlockEntryItem ReadResultItemFrom(Content content)
            {
                NewBlockEntryItem blockRet = new NewBlockEntryItem();
                blockRet.dbid = content.GetParamInt();
                blockRet.type = content.GetParamString();
                blockRet.block_id = content.GetParamInt();
                blockRet.status = content.GetParamInt();
                blockRet.created_date = content.GetParamInt();
                blockRet.closed_date = content.GetParamInt();
                blockRet.area_names = content.GetParamString();
                blockRet.evtnum = content.GetParamInt();
                return blockRet;
            }

            internal NewBlockEntryItem cloneInstance()
            {
                NewBlockEntryItem clone = new NewBlockEntryItem();
                clone.dbid = dbid;
                clone.type = type;
                clone.block_id = block_id;
                clone.status = status;
                clone.created_date = created_date;
                clone.closed_date = closed_date;
                clone.area_names = area_names;
                clone.evtnum = evtnum;
                return clone;
            }
        };
    }
}
