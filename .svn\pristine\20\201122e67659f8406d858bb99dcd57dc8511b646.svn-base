﻿namespace MasterCom.RAMS.Func
{
    partial class TDCellNeighbourInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.Label label1;
            System.Windows.Forms.Label label12;
            System.Windows.Forms.Label label3;
            this.listView = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnIndex = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRelation = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCode = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFREQ = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCPI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnExternal = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport2xls = new System.Windows.Forms.ToolStripMenuItem();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.labelColorNeighbourEachOther = new System.Windows.Forms.Label();
            this.labelColorNeighbour2G = new System.Windows.Forms.Label();
            this.labelColorNeighbour = new System.Windows.Forms.Label();
            label1 = new System.Windows.Forms.Label();
            label12 = new System.Windows.Forms.Label();
            label3 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.listView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(155, 35);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(47, 14);
            label1.TabIndex = 78;
            label1.Text = "TD双向";
            label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Location = new System.Drawing.Point(21, 35);
            label12.Name = "label12";
            label12.Size = new System.Drawing.Size(47, 14);
            label12.TabIndex = 76;
            label12.Text = "TD单向";
            label12.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new System.Drawing.Point(291, 35);
            label3.Name = "label3";
            label3.Size = new System.Drawing.Size(46, 14);
            label3.TabIndex = 76;
            label3.Text = "2G单向";
            label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // listView
            // 
            this.listView.AllColumns.Add(this.olvColumnIndex);
            this.listView.AllColumns.Add(this.olvColumnRelation);
            this.listView.AllColumns.Add(this.olvColumnCode);
            this.listView.AllColumns.Add(this.olvColumnName);
            this.listView.AllColumns.Add(this.olvColumnLAC);
            this.listView.AllColumns.Add(this.olvColumnCI);
            this.listView.AllColumns.Add(this.olvColumnFREQ);
            this.listView.AllColumns.Add(this.olvColumnCPI);
            this.listView.AllColumns.Add(this.olvColumnLongitude);
            this.listView.AllColumns.Add(this.olvColumnLatitude);
            this.listView.AllColumns.Add(this.olvColumnExternal);
            this.listView.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.listView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnIndex,
            this.olvColumnRelation,
            this.olvColumnCode,
            this.olvColumnName,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnFREQ,
            this.olvColumnCPI,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnExternal});
            this.listView.ContextMenuStrip = this.contextMenuStrip;
            this.listView.Cursor = System.Windows.Forms.Cursors.Default;
            this.listView.FullRowSelect = true;
            this.listView.GridLines = true;
            this.listView.HeaderWordWrap = true;
            this.listView.Location = new System.Drawing.Point(2, 3);
            this.listView.Name = "listView";
            this.listView.ShowGroups = false;
            this.listView.Size = new System.Drawing.Size(769, 334);
            this.listView.TabIndex = 3;
            this.listView.UseCompatibleStateImageBehavior = false;
            this.listView.View = System.Windows.Forms.View.Details;
            this.listView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listView_MouseDoubleClick);
            // 
            // olvColumnIndex
            // 
            this.olvColumnIndex.HeaderFont = null;
            this.olvColumnIndex.Text = "序号";
            // 
            // olvColumnRelation
            // 
            this.olvColumnRelation.HeaderFont = null;
            this.olvColumnRelation.Text = "单双向";
            // 
            // olvColumnCode
            // 
            this.olvColumnCode.AspectName = "";
            this.olvColumnCode.HeaderFont = null;
            this.olvColumnCode.Text = "小区号";
            this.olvColumnCode.Width = 80;
            // 
            // olvColumnName
            // 
            this.olvColumnName.AspectName = "";
            this.olvColumnName.HeaderFont = null;
            this.olvColumnName.Text = "小区名";
            this.olvColumnName.Width = 100;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.AspectName = "";
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.AspectName = "";
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // olvColumnFREQ
            // 
            this.olvColumnFREQ.AspectName = "";
            this.olvColumnFREQ.HeaderFont = null;
            this.olvColumnFREQ.Text = "FREQ";
            // 
            // olvColumnCPI
            // 
            this.olvColumnCPI.AspectName = "";
            this.olvColumnCPI.HeaderFont = null;
            this.olvColumnCPI.Text = "CPI";
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.AspectName = "";
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 80;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.AspectName = "";
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 80;
            // 
            // olvColumnExternal
            // 
            this.olvColumnExternal.HeaderFont = null;
            this.olvColumnExternal.Text = "特性";
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport2xls});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(137, 26);
            // 
            // miExport2xls
            // 
            this.miExport2xls.Name = "miExport2xls";
            this.miExport2xls.Size = new System.Drawing.Size(136, 22);
            this.miExport2xls.Text = "导出到Excel";
            this.miExport2xls.Click += new System.EventHandler(this.miExport2xls_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(label1);
            this.groupBox1.Controls.Add(this.labelColorNeighbourEachOther);
            this.groupBox1.Controls.Add(label3);
            this.groupBox1.Controls.Add(label12);
            this.groupBox1.Controls.Add(this.labelColorNeighbour2G);
            this.groupBox1.Controls.Add(this.labelColorNeighbour);
            this.groupBox1.Location = new System.Drawing.Point(2, 343);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(769, 71);
            this.groupBox1.TabIndex = 4;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "图例说明";
            // 
            // labelColorNeighbourEachOther
            // 
            this.labelColorNeighbourEachOther.BackColor = System.Drawing.Color.Red;
            this.labelColorNeighbourEachOther.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.labelColorNeighbourEachOther.Location = new System.Drawing.Point(203, 28);
            this.labelColorNeighbourEachOther.Name = "labelColorNeighbourEachOther";
            this.labelColorNeighbourEachOther.Size = new System.Drawing.Size(29, 29);
            this.labelColorNeighbourEachOther.TabIndex = 77;
            // 
            // labelColorNeighbour2G
            // 
            this.labelColorNeighbour2G.BackColor = System.Drawing.Color.Red;
            this.labelColorNeighbour2G.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.labelColorNeighbour2G.Location = new System.Drawing.Point(338, 28);
            this.labelColorNeighbour2G.Name = "labelColorNeighbour2G";
            this.labelColorNeighbour2G.Size = new System.Drawing.Size(29, 29);
            this.labelColorNeighbour2G.TabIndex = 75;
            // 
            // labelColorNeighbour
            // 
            this.labelColorNeighbour.BackColor = System.Drawing.Color.Red;
            this.labelColorNeighbour.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.labelColorNeighbour.Location = new System.Drawing.Point(69, 28);
            this.labelColorNeighbour.Name = "labelColorNeighbour";
            this.labelColorNeighbour.Size = new System.Drawing.Size(29, 29);
            this.labelColorNeighbour.TabIndex = 75;
            // 
            // TDCellNeighbourInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(775, 418);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.listView);
            this.Name = "TDCellNeighbourInfoForm";
            this.Text = "小区及邻区信息";
            this.Activated += new System.EventHandler(this.CellNeighbourInfoForm_Activated);
            ((System.ComponentModel.ISupportInitialize)(this.listView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.ObjectListView listView;
        private BrightIdeasSoftware.OLVColumn olvColumnIndex;
        private BrightIdeasSoftware.OLVColumn olvColumnRelation;
        private BrightIdeasSoftware.OLVColumn olvColumnCode;
        private BrightIdeasSoftware.OLVColumn olvColumnName;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnFREQ;
        private BrightIdeasSoftware.OLVColumn olvColumnCPI;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label labelColorNeighbourEachOther;
        private System.Windows.Forms.Label labelColorNeighbour;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExport2xls;
        private BrightIdeasSoftware.OLVColumn olvColumnExternal;
        private System.Windows.Forms.Label labelColorNeighbour2G;
    }
}