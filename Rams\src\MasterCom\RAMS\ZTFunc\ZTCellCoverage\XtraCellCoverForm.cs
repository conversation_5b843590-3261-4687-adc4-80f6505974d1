﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class XtraCellCoverForm : DevExpress.XtraEditors.XtraForm
    {
        MainModel mainModel;
        private bool IsTDIn = false;
        public XtraCellCoverForm(bool IsTDIn)
        {
            InitializeComponent();
            this.IsTDIn = IsTDIn;
        }

        public XtraCellCoverForm(MainModel mainmodel,bool IsTDIn)
        {
            mainModel = mainmodel;
            InitializeComponent();
            if (IsTDIn)
            {
                this.Text += "(TD)";
            }
            else
            {
                this.Text += "(GSM)";
            }
            this.IsTDIn = IsTDIn;
        }
        
        public List<CellProblem> CellcoverList { get; set; } = new List<CellProblem>();
        public Dictionary<string, List<CellProblemTest>> CellDicAll { get; set; } = new Dictionary<string, List<CellProblemTest>>();

        public void setData(List<CellProblem> cellitem, Dictionary<string, List<CellProblemTest>> cellDic)
        {
            gridControlShow.DataSource = cellitem;
            CellcoverList = cellitem;
            CellDicAll = cellDic;

            int countNum=0;

            List<CellProblemTest> listcell = new List<CellProblemTest>();
            List<CellProblem> listcellpro = new List<CellProblem>();
            foreach (string item in cellDic.Keys)
            {
                listcell.AddRange(CellDicAll[item]);
                countNum+=cellDic[item].Count;
                foreach (CellProblemTest item2 in CellDicAll[item])
                {
                    listcellpro.AddRange(item2.CellList);
                }
            }
            gridControlTestAll.DataSource = listcell;
            gridControlPerAll.DataSource = listcellpro;

            button1.Text += " - " + cellDic["1"].Count.ToString() + " ("+calc(cellDic["1"].Count,countNum)+")";
            button2.Text += " - " + cellDic["2"].Count.ToString() + " (" + calc(cellDic["2"].Count, countNum) + ")";
            button3.Text += " - " + cellDic["3"].Count.ToString() + " (" + calc(cellDic["3"].Count, countNum) + ")";
            button4.Text += " - " + cellDic["4"].Count.ToString() + " (" + calc(cellDic["4"].Count, countNum) + ")";
            button5.Text += " - " + cellDic["5"].Count.ToString() + " (" + calc(cellDic["5"].Count, countNum) + ")";
            button6.Text += " - " + cellDic["6"].Count.ToString() + " (" + calc(cellDic["6"].Count, countNum) + ")";
            button7.Text += " - " + cellDic["7"].Count.ToString() + " (" + calc(cellDic["7"].Count, countNum) + ")";
            button8.Text += " - " + cellDic["8"].Count.ToString() + " (" + calc(cellDic["8"].Count, countNum) + ")";
            button9.Text += " - " + cellDic["9"].Count.ToString() + " (" + calc(cellDic["9"].Count, countNum) + ")";
        }

        //计算百分比
        private string calc(int num,int maxnum)
        {
            try
            {
                if (maxnum == 0)
                {
                    return "0.00%";
                }
                else
                {
                    string ss=((double)num / (double)maxnum).ToString("0.00%");
                    return ss;
                }
            }
            catch (Exception)
            {
                return "0.00%";
            }
        }

        //隐藏至右下角
        private void XtraClusterForm_Deactivate(object sender, EventArgs e)
        {
            if (this.WindowState == FormWindowState.Minimized)
            {
                this.Visible = false;
                mainModel.AddQuickWindowItem(this.GetType().Name, this.Text, "images\\cellquery.gif");
            }
        }

        //单元格高亮
        private void gridViewInfo_RowCellStyle(object sender, RowCellStyleEventArgs e)
        {
            double value;
            bool isValid = getValidValue(e, "忙时SD可用数目", out value);
            judge(e, value - Convert.ToInt32(value) != 0, isValid);
            isValid = getValidValue(e, "信令信道拥塞率(不含切换)", out value);
            judge(e, value > 1, isValid);
            isValid = getValidValue(e, "SD总掉话率", out value);
            judge(e, value > 2, isValid);
            isValid = getValidValue(e, "每SD信道话务量", out value);
            judge(e, value > 0.3, isValid);
            isValid = getValidValue(e, "立即指配命令丢弃次数", out value);
            judge(e, value > 100, isValid);
            isValid = getValidValue(e, "SDCCH分配成功率", out value);
            judge(e, value < 97, isValid);
            isValid = getValidValue(e, "无线接入性", out value);
            judge(e, value < 95, isValid);
            isValid = getValidValue(e, "忙时话音信道总话务量", out value);
            judge(e, value > 20, isValid);
            isValid = getValidValue(e, "每TCH信道话务量", out value);
            judge(e, value > 0.8, isValid);
            isValid = getValidValue(e, "TCH分配成功率", out value);
            judge(e, value < 95, isValid);
            isValid = getValidValue(e, "话音信道拥塞率(含切换)", out value);
            judge(e, value > 10, isValid);
            isValid = getValidValue(e, "BAND4_5", out value);
            judge(e, value > 30, isValid);
            isValid = getValidValue(e, "话音信道掉话率(含切换)", out value);
            judge(e, value > 2, isValid);
            isValid = getValidValue(e, "话音信道掉话率(不含切换)", out value);
            judge(e, value > 4, isValid);
            double value2;
            isValid = getValidValue(e, "半速率话务量", "全速率话务量", out value, out value2);
            judge(e, (value / (value + value2)) >= 50, isValid);
            isValid = getValidValue(e, "忙时话音信道可用总数", out value);
            judge(e, value - Convert.ToInt32(value) != 0, isValid);
            isValid = getValidValue(e, "切换成功率", out value);
            judge(e, value < 80, isValid);
            isValid = getValidValue(e, "BSC间出切换请求成功率", out value);
            judge(e, value < 90, isValid);
            isValid = getValidValue(e, "BSC间入切换请求成功率", out value);
            judge(e, value < 90, isValid);
        }

        private void judge(RowCellStyleEventArgs e, bool isDataValid, bool isValid)
        {
            if (isValid && isDataValid)
            {
                setBackColor(e);
            }
        }

        private static void setBackColor(RowCellStyleEventArgs e)
        {
            e.Appearance.BackColor = Color.Red;
            e.Appearance.BackColor2 = Color.Red;
        }

        private bool getValidValue(RowCellStyleEventArgs e,string name, out double value)
        {
            value = 0;
            if (e.Column.FieldName == name)
            {
                try
                {
                    value = Convert.ToDouble(gridViewInfo.GetRowCellDisplayText(e.RowHandle, gridViewInfo.Columns[name]));
                    return true;
                }
                catch
                {
                    return false;
                }
            }
            return false;
        }

        private bool getValidValue(RowCellStyleEventArgs e, string name, string name2, out double value, out double value2)
        {
            value = 0;
            value2 = 0;
            if (e.Column.FieldName == name)
            {
                try
                {
                    value = Convert.ToDouble(gridViewInfo.GetRowCellDisplayText(e.RowHandle, gridViewInfo.Columns[name]));
                    value2 = Convert.ToDouble(gridViewInfo.GetRowCellDisplayText(e.RowHandle, gridViewInfo.Columns[name2]));
                    return true;
                }
                catch
                {
                    return false;
                }
            }
            return false;
        }


        //点击显示簇信息
        private void gridControlShow_Click(object sender, EventArgs e)
        {
            // Method intentionally left empty.
        }

        //所选簇信息变动 显示小区集信息
        private void gridControlInfo_DataSourceChanged(object sender, EventArgs e)
        {
            // Method intentionally left empty.
        }

        //双击切换到所选簇信息
        private void gridControlShow_DoubleClick(object sender, EventArgs e)
        {
            int ilac = Convert.ToInt32(gridViewShow.GetFocusedRowCellValue("Lac").ToString());
            int ici = Convert.ToInt32(gridViewShow.GetFocusedRowCellValue("Ci").ToString());
            DateTime stime = DateTime.Parse(gridViewShow.GetFocusedRowCellValue("Sdate").ToString());
            DateTime etime = DateTime.Parse(gridViewShow.GetFocusedRowCellValue("Edate").ToString());

            DiySqlQueryCounterDetail diysqlcell = new DiySqlQueryCounterDetail(mainModel, ilac, ici, stime, etime,IsTDIn);
            diysqlcell.Query();

            gridControlInfo.DataSource = diysqlcell.CounterdetailList;
            xtraTabControl1.SelectedTabPageIndex = 1;

            //定位GIS
            Cell cell = null;
            cell = mainModel.CellManager.GetCell(stime, (ushort)ilac, (ushort)ici);
            TDCell tdCell = null;
            tdCell = mainModel.CellManager.GetTDCell(stime, (ushort)ilac, (ushort)ici);
            if (cell!=null)
            {
                mainModel.MainForm.GetMapForm().GoToView(cell.Longitude, cell.Latitude);
            }
            else if (tdCell != null)
            {
                mainModel.MainForm.GetMapForm().GoToView(tdCell.Longitude, tdCell.Latitude);
            }
        }

        //频率筛选变化
        private void CheckBoxChanged(object sender, EventArgs e)
        {
            // Method intentionally left empty.
        }

        //高危
        private void button1_Click(object sender, EventArgs e)
        {
            gridControlTest.DataSource = CellDicAll["1"];

            List<CellProblem> list=new List<CellProblem>();
            foreach (CellProblemTest item in CellDicAll["1"])
            {
                list.AddRange(item.CellList);
            }
            gridControlPer.DataSource = list;
            xtraTabControl1.SelectedTabPageIndex = 3;
        }

        //紧急
        private void button2_Click(object sender, EventArgs e)
        {
            if (CellDicAll == null || CellDicAll.Count == 0)
            {
                return;
            }
            gridControlTest.DataSource = CellDicAll["2"];

            List<CellProblem> list = new List<CellProblem>();
            foreach (CellProblemTest item in CellDicAll["2"])
            {
                list.AddRange(item.CellList);
            }

            gridControlPer.DataSource = list;
            xtraTabControl1.SelectedTabPageIndex = 3;
        }

        //重要
        private void button3_Click(object sender, EventArgs e)
        {
            if (CellDicAll == null || CellDicAll.Count == 0)
            {
                return;
            }
            gridControlTest.DataSource = CellDicAll["3"];

            List<CellProblem> list = new List<CellProblem>();
            foreach (CellProblemTest item in CellDicAll["3"])
            {
                list.AddRange(item.CellList);
            }

            gridControlPer.DataSource = list;
            xtraTabControl1.SelectedTabPageIndex = 3;
        }


        //紧急
        private void button4_Click(object sender, EventArgs e)
        {

            if (CellDicAll == null || CellDicAll.Count == 0)
            {
                return;
            }
            gridControlTest.DataSource = CellDicAll["4"];

            List<CellProblem> list = new List<CellProblem>();
            foreach (CellProblemTest item in CellDicAll["4"])
            {
                list.AddRange(item.CellList);
            }

            gridControlPer.DataSource = list;
            xtraTabControl1.SelectedTabPageIndex = 3;
        }

        //重要
        private void button5_Click(object sender, EventArgs e)
        {
            if (CellDicAll == null || CellDicAll.Count == 0)
            {
                return;
            }
            gridControlTest.DataSource = CellDicAll["5"];

            List<CellProblem> list = new List<CellProblem>();
            foreach (CellProblemTest item in CellDicAll["5"])
            {
                list.AddRange(item.CellList);

            }

            gridControlPer.DataSource = list;
            xtraTabControl1.SelectedTabPageIndex = 3;
        }

        //一般
        private void button6_Click(object sender, EventArgs e)
        {
            if (CellDicAll == null || CellDicAll.Count == 0)
            {
                return;
            }
            gridControlTest.DataSource = CellDicAll["6"];

            List<CellProblem> list = new List<CellProblem>();
            foreach (CellProblemTest item in CellDicAll["6"])
            {
                list.AddRange(item.CellList);
            }

            gridControlPer.DataSource = list;
            xtraTabControl1.SelectedTabPageIndex = 3;
        }

        //重要
        private void button7_Click(object sender, EventArgs e)
        {
            if (CellDicAll == null || CellDicAll.Count == 0)
            {
                return;
            }
            gridControlTest.DataSource = CellDicAll["7"];

            List<CellProblem> list = new List<CellProblem>();
            foreach (CellProblemTest item in CellDicAll["7"])
            {
                list.AddRange(item.CellList);
            }

            gridControlPer.DataSource = list;
            xtraTabControl1.SelectedTabPageIndex = 3;
        }

        //一般
        private void button8_Click(object sender, EventArgs e)
        {
            if (CellDicAll == null || CellDicAll.Count == 0)
            {
                return;
            }
            gridControlTest.DataSource = CellDicAll["8"];

            List<CellProblem> list = new List<CellProblem>();
            foreach (CellProblemTest item in CellDicAll["8"])
            {
                list.AddRange(item.CellList);
            }

            gridControlPer.DataSource = list;
            xtraTabControl1.SelectedTabPageIndex = 3;
        }


        //低级
        private void button9_Click(object sender, EventArgs e)
        {
            if (CellDicAll == null || CellDicAll.Count == 0)
            {
                return;
            }
            gridControlTest.DataSource = CellDicAll["9"];

            List<CellProblem> list = new List<CellProblem>();
            foreach (CellProblemTest item in CellDicAll["9"])
            {
                list.AddRange(item.CellList);
            }

            gridControlPer.DataSource = list;
            xtraTabControl1.SelectedTabPageIndex = 3;
        }

        //选项卡变动事件
        private void xtraTabControl1_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            if (xtraTabControl1.SelectedTabPageIndex != 0)
            {
                mainModel.IsCloud = false;
            }
            else
            {
                mainModel.IsCloud = true;
            }
            mainModel.FireDTDataChanged(this);  //刷新GIS
        }

        //点击联动
        private void gridControl_Click(object sender, EventArgs e)
        {
            string cellname ="";
            try
            {
                DevExpress.XtraGrid.GridControl gridcontrol = sender as DevExpress.XtraGrid.GridControl;
                GridView view = gridcontrol.MainView as GridView;

                if (view.Columns.Count == 12)
                {
                    cellname = view.GetFocusedRowCellValue("Cellname").ToString();
                }
                else
                {
                    cellname = view.GetFocusedRowCellValue("StrCellName").ToString();
                }

                

            }
            catch (Exception)
            {
                
                return;
            }

            int rowid = GetRowHandleByColumnValue(bandedGridView1, bandedGridView1.Columns[1], cellname);
            bandedGridView1.MoveFirst();
            bandedGridView1.MoveBy(rowid);

            int rowid2 = GetRowHandleByColumnValue(bandedGridView2, bandedGridView2.Columns[1], cellname);
            bandedGridView2.MoveFirst();
            bandedGridView2.MoveBy(rowid2);

            int rowid3 = GetRowHandleByColumnValue(bandedGridView3, bandedGridView3.Columns[1], cellname);
            bandedGridView3.MoveFirst();
            bandedGridView3.MoveBy(rowid3);

            int rowid4 = GetRowHandleByColumnValue(bandedGridView4, bandedGridView4.Columns[1], cellname);
            bandedGridView4.MoveFirst();
            bandedGridView4.MoveBy(rowid4);
            
            //高亮小区
            Cell cell=mainModel.CellManager.GetCellByName(cellname);
            TDCell tdCell = mainModel.CellManager.GetTDCellByName(cellname);
            if(cell!=null)
            {
                mainModel.SelectedCell = cell;
                mainModel.MainForm.GetMapForm().GoToView(cell.Longitude,cell.Latitude);
            }
            else if (tdCell != null)
            {
                mainModel.SelectedTDCell = tdCell;
                mainModel.MainForm.GetMapForm().GoToView(tdCell.Longitude, tdCell.Latitude);
            }
        }

        private int GetRowHandleByColumnValue(GridView view, GridColumn column, object value)
        {
            int result = GridControl.InvalidRowHandle;
            try
            {
                
                for (int i = 0; i < view.RowCount; i++)
                    if (view.GetRowCellValue(i, column).Equals(value))
                        return i;
                return result;
            }
            catch (Exception)
            {

                return result;
            }
            
        }

        //级别过滤器
        private void bandedGridView_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            if (e.Column.FieldName == "ILevel")
            {
                int value = 0;
                try
                {
                    value = Convert.ToInt32(e.CellValue.ToString());
                }
                catch (Exception)
                {
                    value = 0;
                }
                e.DisplayText = statusTransformString(value);
            }
        }

        private string statusTransformString(int status)
        {
            if (status == 1)
            {
                return "很低";
            }
            if (status == 2)
            {
                return "较低";
            }
            if (status == 3)
            {
                return "普通";
            }
            if (status == 4)
            {
                return "较高";
            }
            if (status == 5)
            {
                return "很高";
            }
            return "未知";
        }


        private void 导出ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(bandedGridView1);
        }

        private void toolStripMenuItem1_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(bandedGridView2);
        }

        private void toolStripMenuItem2_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(bandedGridView3);
        }

        private void toolStripMenuItem3_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(bandedGridView4);
        }

        //查询小区性能接口
        private void queryIPerformance(string lac, string ci, DateTime stime, DateTime etime)
        {
            if (IsTDIn)
            {
                QueryIPerformanceTD iper = new QueryIPerformanceTD(mainModel, lac, ci, stime, etime);
                iper.Query();
            }
            else
            {
                QueryIPerformance iper = new QueryIPerformance(mainModel, lac, ci, stime, etime);
                iper.Query();
            }
        }

        private void 查看性能ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            string lac = bandedGridView3.GetFocusedRowCellValue("Lac").ToString();
            string ci = bandedGridView3.GetFocusedRowCellValue("Ci").ToString();
            string stime = bandedGridView3.GetFocusedRowCellValue("Sdate").ToString();
            string etime = bandedGridView3.GetFocusedRowCellValue("Edate").ToString();

            queryIPerformance(lac, ci, DateTime.Parse(stime), DateTime.Parse(etime));
        }

        private void 查看性能ToolStripMenuItem1_Click(object sender, EventArgs e)
        {
            try
            {
                string lac = bandedGridView2.GetFocusedRowCellValue("ILAC").ToString();
                string ci = bandedGridView2.GetFocusedRowCellValue("ICI").ToString();
                testQuery = new TestDetailQuery(mainModel, IsTDIn, int.Parse(lac), int.Parse(ci));
                WaitBox.Show(QueryTestDetail);
                if (cellcoverinfoList != null && cellcoverinfoList.Count != 0)
                {
                    TestDetailShow detailShow = new TestDetailShow();
                    detailShow.AllShowInfs = cellcoverinfoList;
                    detailShow.ShowDialog();
                }
            }
            catch
            {
                //continue
            }
        }
        TestDetailQuery testQuery;
        private void 查看性能ToolStripMenuItem2_Click(object sender, EventArgs e)
        {
            try
            {
                string lac = bandedGridView2.GetFocusedRowCellValue("ILAC").ToString();
                string ci = bandedGridView2.GetFocusedRowCellValue("ICI").ToString();
                testQuery = new TestDetailQuery(mainModel, IsTDIn, int.Parse(lac), int.Parse(ci));
                WaitBox.Show(QueryTestDetail);
                if (cellcoverinfoList != null && cellcoverinfoList.Count != 0)
                {
                    TestDetailShow detailShow = new TestDetailShow();
                    detailShow.AllShowInfs = cellcoverinfoList;
                    detailShow.ShowDialog();
                }
            }
            catch
            {
                //continue
            }
        }
        private List<TestDetailInf> cellcoverinfoList;
        private void QueryTestDetail()
        {
            cellcoverinfoList = new List<TestDetailInf>();
            WaitBox.ProgressPercent = 25;
            if (testQuery != null)
            {
                testQuery.Query();
                cellcoverinfoList = testQuery.CellcoverinfoList;
            }
            WaitBox.ProgressPercent = 100;
            WaitBox.Close();
        }
        private void 查看性能ToolStripMenuItem3_Click(object sender, EventArgs e)
        {
            string lac = bandedGridView1.GetFocusedRowCellValue("Lac").ToString();
            string ci = bandedGridView1.GetFocusedRowCellValue("Ci").ToString();
            string stime = bandedGridView1.GetFocusedRowCellValue("Sdate").ToString();
            string etime = bandedGridView1.GetFocusedRowCellValue("Edate").ToString();

            queryIPerformance(lac, ci, DateTime.Parse(stime), DateTime.Parse(etime));
        }
    }
}