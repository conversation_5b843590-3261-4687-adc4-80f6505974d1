﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteVoiceAnaByFreqBand : DIYAnalyseByFileBackgroundBase
    {
        LteVoiceAnaByFreqBandHelper helper;
        List<LteVoiceAnaByFreqBandFileResult> resList;

        protected static readonly object lockObj = new object();
        protected LteVoiceAnaByFreqBand()
            : base(MainModel.GetInstance())
        {
            //FilterSampleByRegion = true;

            Columns = new List<string>();
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_PESQScore");
            Columns.Add("lte_PESQLQ");
            Columns.Add("lte_PESQMos");
            Columns.Add("lte_POLQA_Score_SWB");

            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.VoLTE));
        }
        public override string Name
        {
            get { return "分频段指标统计"; }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 27000, 27020, this.Name);
        }

        protected override bool getCondition()
        {
            return true;
        }

        protected override void getReadyBeforeQuery()
        {
            helper = new LteVoiceAnaByFreqBandHelper();
            resList = new List<LteVoiceAnaByFreqBandFileResult>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                helper.DealWithData(file);
                LteVoiceAnaByFreqBandFileResult fileRes = helper.GetResult(file.FileName);
                if (fileRes != null)
                {
                    resList.Add(fileRes);
                }
            }
        }

        protected override void getResultsAfterQuery()
        {
            LteVoiceAnaByFreqBandFileResult totalRes = helper.StatTotalResult(resList);
            resList.Add(totalRes);
        }

        protected override void fireShowForm()
        {
            LteVoiceAnaByFreqBandForm frm = MainModel.CreateResultForm(typeof(LteVoiceAnaByFreqBandForm)) as LteVoiceAnaByFreqBandForm;
            frm.FillData(resList);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class LteVoiceAnaByFreqBandByFile : LteVoiceAnaByFreqBand
    {
        private LteVoiceAnaByFreqBandByFile()
            : base()
        {
        }

        private static LteVoiceAnaByFreqBandByFile instance = null;
        public static LteVoiceAnaByFreqBandByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteVoiceAnaByFreqBandByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "分频段指标统计(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }

    public class LteVoiceAnaByFreqBandByRegion : LteVoiceAnaByFreqBand
    {
        protected LteVoiceAnaByFreqBandByRegion()
            : base()
        {
        }

        private static LteVoiceAnaByFreqBandByRegion instance = null;
        public static LteVoiceAnaByFreqBandByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteVoiceAnaByFreqBandByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "分频段指标统计(按区域)"; }
        }
    }
}
