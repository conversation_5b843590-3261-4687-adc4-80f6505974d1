﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;

using MasterCom.MControls;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class ScanGridAnaLayer : CustomDrawLayer
    {
        public static List<ScanGridAnaGridInfo> GridList
        {
            get;
            set;
        }
        public static List<ScanGridAnaGridInfo[]> GridBlock
        {
            get;
            set;
        }

        public static ScanGridAnaGridInfo SelectedGrid
        {
            get;
            set;
        }

        public ScanGridAnaLayer(MapOperation mp, string name)
            : base(mp, name)
        {
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible || GridList == null || GridList.Count == 0)
            {
                return;
            }

            if (SelectedGrid != null)
            {
                DrawSelectedGridFlyLine(graphics);
            }

            ScanGridAnaColorRanger colorRanger = ScanGridAnaColorRanger.Instance;
            if (colorRanger.CurRangeType == ScanGridAnaRangeType.WeakRxlev || colorRanger.CurRangeType == ScanGridAnaRangeType.HighCoverage)
            {
                DrawGridLine(updateRect, graphics);
                return;
            }

            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            foreach (ScanGridAnaGridInfo grid in GridList)
            {
                if (!grid.Within(dRect))
                {
                    continue;
                }

                Color color = colorRanger.GetColor(grid);
                if (color == Color.Empty)
                {
                    continue;
                }
                DrawGrid(grid, color, graphics);
            }
        }

        private void DrawGrid(ScanGridAnaGridInfo grid, Color color, Graphics graphics)
        {
            DbPoint ltPoint = new DbPoint(grid.TLLongitude, grid.TLLatitude);
            PointF pointLt;
            this.Map.ToDisplay(ltPoint, out pointLt);
            DbPoint brPoint = new DbPoint(grid.BRLongitude, grid.BRLatitude);
            PointF pointBr;
            this.Map.ToDisplay(brPoint, out pointBr);
            Brush brush = new SolidBrush(color);
            graphics.FillRectangle(brush, pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
        }

        //连续栅格块外围线
        private void DrawGridLine(System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (GridBlock == null)
            {
                return;
            }

            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            ScanGridAnaColorRanger colorRanger = ScanGridAnaColorRanger.Instance;

            Color color = Color.Empty;
            foreach (ScanGridAnaGridInfo[] grids in GridBlock)
            {
                if (grids.Length > 0)
                {
                    color = colorRanger.GetColor(grids[0]);
                }
                foreach (ScanGridAnaGridInfo grid in grids)
                {
                    if (!grid.Within(dRect))
                    {
                        continue;
                    }
                    DrawGrid(grid, color, graphics);
                }
            }
        }

        protected override void mapForm_MapFeatureSelecting(object sender, EventArgs e)
        {
            MapForm.MapEventArgs ea = e as MapForm.MapEventArgs;
            if (ea == null)
            {
                return;
            }
            DbPoint center = ea.MapOp2.GetRegion().Bounds.Center();

            SelectedGrid = null;
            if (GridBlock != null) // 优先从GridBlock查找
            {
                getSelGridFromGridBlock(center);
                return;
            }

            if (GridList != null)
            {
                foreach (ScanGridAnaGridInfo grid in GridList)
                {
                    if (grid.TLLongitude < center.x && grid.BRLongitude > center.x
                        && grid.TLLatitude > center.y && grid.BRLatitude < center.y)
                    {
                        SelectedGrid = grid;
                        return;
                    }
                }
            }
        }

        private void getSelGridFromGridBlock(DbPoint center)
        {
            foreach (ScanGridAnaGridInfo[] grids in GridBlock)
            {
                foreach (ScanGridAnaGridInfo grid in grids)
                {
                    if (grid.TLLongitude < center.x && grid.BRLongitude > center.x
                        && grid.TLLatitude > center.y && grid.BRLatitude < center.y)
                    {
                        SelectedGrid = grid;
                        return;
                    }
                }
            }
        }

        private void DrawSelectedGridFlyLine(Graphics graphics)
        {
            if (SelectedGrid == null)
            {
                return;
            }

            int i = 0;
            PointF p1;
            Map.ToDisplay(new DbPoint(SelectedGrid.CentLng, SelectedGrid.CentLat), out p1);

            foreach (ScanGridAnaCellInfo cellInfo in SelectedGrid.CellInfoDic.Values)
            {
                Pen pen = new Pen(colors[i++ % colors.Length], 2);
                PointF p2;
                Map.ToDisplay(new DbPoint(cellInfo.Cell.Longitude, cellInfo.Cell.Latitude), out p2);
                graphics.DrawLine(pen, p1, p2);
            }
        }

        private readonly Color[] colors = new Color[]
        {
            Color.Yellow,
            Color.Green, 
            Color.Purple,
            Color.YellowGreen,
        };
    }

    public class ScanGridAnaGisColoring
    {
        private readonly ScanGridAnaResult anaResult;
        private readonly ScanGridAnaResult cmpResult;
        private readonly MainModel mainModel;
        private readonly List<ScanGridAnaGridInfo>[] netGridList;
        private readonly List<ScanGridAnaGridInfo>[] netGridCompare;
        private readonly ScanGridAnaColorRanger colorRanger;
        private readonly ScanGridAnaGridBlockStater blockStater;

        public ScanGridAnaGisColoring(MainModel mainModel, ScanGridAnaResult anaResult, ScanGridAnaResult cmpResult)
        {
            this.anaResult = anaResult;
            this.cmpResult = cmpResult;
            this.mainModel = mainModel;
            this.blockStater = new ScanGridAnaGridBlockStater(anaResult);
            netGridList = new List<ScanGridAnaGridInfo>[4];
            netGridCompare = new List<ScanGridAnaGridInfo>[4];
            for (int i = 0; i < 4; ++i)
            {
                netGridList[i] = new List<ScanGridAnaGridInfo>();
                netGridCompare[i] = new List<ScanGridAnaGridInfo>();
            }
            Fill();
            mainModel.MainForm.GetMapForm().FireAddScanGridAnaLayer();
            colorRanger = ScanGridAnaColorRanger.Instance;
        }

        public void Refresh(ScanGridAnaGridType netType)
        {
            ScanGridAnaLayer.SelectedGrid = null;
            ScanGridAnaLayer.GridBlock = null;
            if (colorRanger.CurRangeType == ScanGridAnaRangeType.Compare)
            {
                ScanGridAnaLayer.GridList = netGridCompare[(int)netType];
            }
            else
            {
                ScanGridAnaLayer.GridList = netGridList[(int)netType];
            }
            if (colorRanger.CurRangeType == ScanGridAnaRangeType.HighCoverage || colorRanger.CurRangeType == ScanGridAnaRangeType.WeakRxlev)
            {
                ScanGridAnaLayer.GridBlock = blockStater.GetResult(netType, colorRanger.CurRangeType);
            }
            
            double minX = double.MaxValue;
            double minY = double.MaxValue;
            double maxX = double.MinValue;
            double maxY = double.MinValue;
            foreach (ScanGridAnaGridInfo grid in ScanGridAnaLayer.GridList)
            {
                minX = Math.Min(minX, grid.TLLongitude);
                minY = Math.Min(minY, grid.BRLatitude);
                maxX = Math.Max(maxX, grid.BRLongitude);
                maxY = Math.Max(maxY, grid.TLLatitude);
            }
            if (minX < maxX && minY < maxY)
            {
                mainModel.MainForm.GetMapForm().GoToView(new DbRect(minX, minY, maxX, maxY));
            }

            //mainModel.MainForm.GetMapForm().scanGridAnaLayer.Invalidate();
            mainModel.RefreshLegend();
        }

        private void Fill()
        {
            foreach (ScanGridAnaGridInfo grid in anaResult.GridList)
            {
                netGridList[(int)grid.GridType].Add(grid);
            }

            if (cmpResult != null)
            {
                ScanGridCompareColoring coloring = new ScanGridCompareColoring();
                coloring.Stat(anaResult, cmpResult);
                for (int i = 0; i < 4; ++i)
                {
                    netGridCompare[i] = coloring.GetCompareInfo((ScanGridAnaGridType)i);
                }
            }

            blockStater.Stat();
        }
    }

    /// <summary>
    /// 用于GIS渲染的对比栅格解析类
    /// </summary>
    public class ScanGridCompareColoring
    {
        public ScanGridCompareColoring()
        {
            netTypeGridCompareDic = new Dictionary<int, List<ScanGridAnaGridInfo>>();
        }

        /// <summary>
        /// 获取网络类别指定的渲染栅格
        /// </summary>
        /// <param name="netType"></param>
        /// <returns></returns>
        public List<ScanGridAnaGridInfo> GetCompareInfo(ScanGridAnaGridType netType)
        {
            return netTypeGridCompareDic[(int)netType];
        }

        /// <summary>
        /// 对比两个查询结果中的栅格存在情况
        /// </summary>
        /// <param name="anaResult"></param>
        /// <param name="cmpResult"></param>
        public void Stat(ScanGridAnaResult anaResult, ScanGridAnaResult cmpResult)
        {
            // 按网络类别归类查询结果中已存在的栅格
            Dictionary<int, Dictionary<string, ScanGridAnaGridInfo>> anaNetTypeExistedGrid;
            Dictionary<int, Dictionary<string, ScanGridAnaGridInfo>> cmpNetTypeEixstedGrid;
            FillNetTypeExistedGrid(anaResult, out anaNetTypeExistedGrid);
            FillNetTypeExistedGrid(cmpResult, out cmpNetTypeEixstedGrid);

            // 遍历网络类型
            for (int i = 0; i < 4; ++i)
            {
                // 对比该网络类型中，两种查询结果中的栅格
                Dictionary<string, ScanGridAnaGridInfo> anaDic = anaNetTypeExistedGrid[i];
                Dictionary<string, ScanGridAnaGridInfo> cmpDic = cmpNetTypeEixstedGrid[i];
                Dictionary<string, int> tmpDic = new Dictionary<string, int>();

                netTypeGridCompareDic.Add(i, new List<ScanGridAnaGridInfo>());
                foreach (string key in anaDic.Keys)
                {
                    ScanGridAnaGridInfo grid = anaDic[key];
                    if (cmpDic.ContainsKey(key)) // 同时存在            
                    {
                        grid.Existence = 0;
                        netTypeGridCompareDic[i].Add(grid);
                        tmpDic.Add(key, 0);      // 记录时段二中跟时段一同时出现
                    }
                    else // 只存在时间段一
                    {
                        grid.Existence = 1;
                        netTypeGridCompareDic[i].Add(grid);
                    }
                }

                // 单独出现在时段二的
                foreach (string key in cmpDic.Keys)
                {
                    if (tmpDic.ContainsKey(key))
                    {
                        continue;
                    }
                    ScanGridAnaGridInfo grid = cmpDic[key];
                    grid.Existence = 2;
                    netTypeGridCompareDic[i].Add(grid);
                }
            }
        }

        /// <summary>
        /// 归类四种网络对应的已经存在的栅格
        /// </summary>
        /// <param name="result"></param>
        /// <param name="gridDic"></param>
        private void FillNetTypeExistedGrid(ScanGridAnaResult result, out Dictionary<int, Dictionary<string, ScanGridAnaGridInfo>> gridDic)
        {
            gridDic = new Dictionary<int, Dictionary<string, ScanGridAnaGridInfo>>();
            for (int i = 0; i < 4; ++i)
            {
                gridDic.Add(i, new Dictionary<string, ScanGridAnaGridInfo>());
            }

            foreach (ScanGridAnaGridInfo grid in result.GridList)
            {
                if (!gridDic[(int)grid.GridType].ContainsKey(grid.MGRSGridString))
                {
                    gridDic[(int)grid.GridType].Add(grid.MGRSGridString, grid);
                }
            }
        }

        /// <summary>
        /// key: 0-3四种网络类别；网络类别对应的栅格
        /// </summary>
        private readonly Dictionary<int, List<ScanGridAnaGridInfo>> netTypeGridCompareDic;
    }

}
