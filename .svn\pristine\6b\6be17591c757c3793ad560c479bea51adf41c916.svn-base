﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using DevExpress.XtraTab;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using System.IO;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTLteMgrsCoveForm : MinCloseForm
    {
        public ZTLteMgrsCoveForm(MainModel mainModel) : base(mainModel)
        {
            InitializeComponent();
            this.mapForm = MainModel.MainForm.GetMapForm();
            gridViewDetail.OptionsBehavior.EditorShowMode = DevExpress.Utils.EditorShowMode.Click;
            gridViewDetail.OptionsBehavior.Editable = false;
            gridViewDetail.OptionsView.ColumnAutoWidth = false;
            gridView2.OptionsBehavior.EditorShowMode = DevExpress.Utils.EditorShowMode.Click;
            gridView2.OptionsBehavior.Editable = false;
        }
        Dictionary<string, LTEMgrsGridInfo> dicResult = null;
        LTEMgrsCondition Condition = null;
        MapForm mapForm;
        public static LteMgrsCoverageColorRange Ranges { get; set; } = new LteMgrsCoverageColorRange();

        private void addColumns(DataTable tblDatas, int iflage)
        {
            if (iflage == 1)
            {
                tblDatas.Columns.Add("序号");
                tblDatas.Columns.Add("栅格号");
                tblDatas.Columns.Add("小区数");
                tblDatas.Columns.Add("重叠度");
                tblDatas.Columns.Add("重叠小区");
                tblDatas.Columns.Add("中心经度");
                tblDatas.Columns.Add("中心纬度");
                tblDatas.Columns.Add("栅格平均SINR");
                //前5强
                for (int i = 1; i <= 5; i++)
                {
                    tblDatas.Columns.Add(i.ToString() + "强小区名");
                    tblDatas.Columns.Add(i.ToString() + "强小区RSRP");
                }
            }
            if (iflage == 2)
            {
                tblDatas.Columns.Add("网格");
                tblDatas.Columns.Add("频段");
                tblDatas.Columns.Add(string.Format("(-∞,{0})", 1));
                for (int i = 1; i < 10; i++)
                {
                    tblDatas.Columns.Add(string.Format("[{0},{1})", i, i + 1));
                }
                tblDatas.Columns.Add(string.Format("[{0},+∞)", 10));
                tblDatas.Columns.Add("无效栅格");
                tblDatas.Columns.Add("总栅格数");
                tblDatas.Columns.Add("重叠覆盖度");
            }
        }
        public void iniData(Dictionary<string, LTEMgrsGridInfo> dicResult, LTEMgrsCondition Condition)
        {
            this.dicResult = dicResult;
            this.Condition = Condition;
            Dictionary<string, Dictionary<int, int>> dicCov = new Dictionary<string, Dictionary<int, int>>();
            this.Text += (" "+Condition.cityName + " 【 频段：" + getFreqBandType() + "】");
            DataTable dt = new DataTable("重叠覆盖栅格信息");
            DataTable dtCov = new DataTable("重叠覆盖度");
            addColumns(dt, 1);
            addColumns(dtCov, 2);
            int i = 0;
            foreach (string mgrsKey in dicResult.Keys)
            {
                List<object> secValue1 = new List<object>();
                secValue1.Add(++i);
                secValue1.Add(dicResult[mgrsKey].MgrsString);
                secValue1.Add(dicResult[mgrsKey].newCellList.Count);
                secValue1.Add(dicResult[mgrsKey].iCoverage);
                secValue1.Add(dicResult[mgrsKey].strCoverageCell);
                secValue1.Add(dicResult[mgrsKey].CentLng);
                secValue1.Add(dicResult[mgrsKey].CentLat);
                secValue1.Add(dicResult[mgrsKey].gridAvgSINR);
                List<LteMgrsCell> ListCell = dicResult[mgrsKey].CoverageCellList;
                for (int index = 0; index < 5; index++)
                {
                    if (ListCell != null && ListCell.Count - 1 >= index)
                    {
                        secValue1.Add(ListCell[index].CellName);
                        secValue1.Add(Math.Round(ListCell[index].AvgRsrp, 2));
                    }
                    else
                    {
                        addDefault(secValue1, 2);
                    }
                }
                dt.Rows.Add(secValue1.ToArray());
                setDicCov(dicResult, dicCov, mgrsKey);
            }
            addRows(dicCov, dtCov);
            FillData(dt, dtCov);
        }

        /// <summary>
        /// 得到频段类型
        /// </summary>
        /// <returns></returns>
        private string getFreqBandType()
        {
            StringBuilder sb = new StringBuilder();
            if (Condition.IsNotBand)
            {
                sb.Append("不分频段");
            }
            else if (Condition.IsStrongRange)
            {
                sb.Append("最强归属段");
            }
            else if (Condition.ListFreqPoint.Count > 0)
            {
                List<string> listFreqBand = new List<string>();
                foreach (FreqPoint fp in Condition.ListFreqPoint)
                {
                    if (!listFreqBand.Contains(fp.FreqBandName))
                    {
                        listFreqBand.Add(fp.FreqBandName);
                    }
                }
                foreach (string fb in listFreqBand)
                {
                    sb.Append(fb).Append(",");
                }
                sb.Remove(sb.Length - 1, 1);
            }
            return sb.ToString();
        }

        private void setDicCov(Dictionary<string, LTEMgrsGridInfo> dicResult, Dictionary<string, Dictionary<int, int>> dicCov, string mgrsKey)
        {
            string strGrid = dicResult[mgrsKey].strGrid;
            int iCoverage = dicResult[mgrsKey].iCoverage > 10 ? 10 : dicResult[mgrsKey].iCoverage;
            if (!dicCov.ContainsKey(strGrid))
            {
                dicCov[strGrid] = new Dictionary<int, int>();
            }
            if (!dicCov[strGrid].ContainsKey(iCoverage))
            {
                Dictionary<int, int> dicTmp = new Dictionary<int, int>();
                for (int idx = 0; idx <= 10; idx++)
                {
                    dicTmp.Add(idx, 0);
                }
                dicCov[strGrid] = dicTmp;
            }
            dicCov[strGrid][iCoverage]++;
        }

        private void addDefault(List<object> secValue1, int count)
        {
            for (int i = 0; i < count; i++)
            {
                secValue1.Add(" ");
            }
        }

        private void addRows(Dictionary<string, Dictionary<int, int>> dicCov, DataTable dtCov)
        {
            foreach (string strGrid in dicCov.Keys)
            {
                List<object> secValue1 = new List<object>();
                secValue1.Add(strGrid);
                secValue1.Add(getFreqBandType());
                secValue1.Add(0);
                int iGridSum = 0;
                int iCovCount = 0;
                for (int idx = 1; idx <= 10; idx++)
                {
                    secValue1.Add(dicCov[strGrid][idx]);
                    iGridSum += dicCov[strGrid][idx];
                    if (idx >= 4)
                    {
                        iCovCount += dicCov[strGrid][idx];
                    }
                }
                secValue1.Add(dicCov[strGrid][0]);
                secValue1.Add(iGridSum);
                secValue1.Add(string.Format("{0}%", Math.Round(iCovCount * 1.0 / iGridSum * 100, 2)));
                dtCov.Rows.Add(secValue1.ToArray());
            }
        }

        private void FillData(DataTable dt, DataTable dtCov)
        {
            if (dt != null)
            {
                gridControl1.DataSource = dt;
                gridControl1.RefreshDataSource();
            }
            if (dtCov != null)
            {
                gridControl2.DataSource = dtCov;
                gridControl2.RefreshDataSource();
            }
            RefreshResult();
        }

        private void ExportExcelItem_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveDialog = new SaveFileDialog();
            saveDialog.Filter = FilterHelper.Csv;
            saveDialog.RestoreDirectory = true;
            if (saveDialog.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            WaitBox.Show("正在导出表格...", OutputCsvFile, saveDialog.FileName);
            string strFile = saveDialog.FileName.Replace(".csv", "");
            string strFileName = string.Format(@"{0}_{1}_{2:yyyyMMddHHmmss}.xlsx", strFile, "重叠覆盖度", DateTime.Now);
            ExcelNPOIManager.ExportToExcel(gridView2, strFileName, "重叠覆盖度");
            MessageBox.Show("导出完成");
        }

        private void OutputCsvFile(object filePath)
        {
            try
            {
                string strFileName = filePath as string;
                StreamWriter comSr = new StreamWriter(strFileName, true, Encoding.Default);
                StringBuilder colName = new StringBuilder();
                List<LTEMgrsGridInfo> listTmp = new List<LTEMgrsGridInfo>();
                listTmp.AddRange(dicResult.Values);
                listTmp.Sort(new Comparison<LTEMgrsGridInfo>((x, y) => { return y.iCoverage.CompareTo(x.iCoverage); }));
                int iMaxCoverage = listTmp[0].iCoverage;
                for (int c = 0; c < gridViewDetail.Columns.Count; c++)
                {
                    if (c > 0)
                        colName.Append(",");
                    colName.Append(gridViewDetail.Columns[c].Caption == "" ? gridViewDetail.Columns[c].FieldName : gridViewDetail.Columns[c].Caption);
                }
                if (iMaxCoverage > 5)
                {
                    for (int c = 6; c <= iMaxCoverage; c++)
                    {
                        colName.Append(",");
                        colName.Append(c.ToString() + "强小区名");
                        colName.Append(",");
                        colName.Append(c.ToString() + "强小区RSRP");
                    }
                }
                bool exportlongLat = colName.ToString().Contains("经度");
                comSr.WriteLine(colName.ToString());
                int idx = 1;
                foreach (LTEMgrsGridInfo mgrs in listTmp)
                {
                    StringBuilder rows = getRows(iMaxCoverage, exportlongLat, ref idx, mgrs);
                    comSr.WriteLine(rows.ToString());
                }

                comSr.Flush();
                comSr.Close();
                WaitBox.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出失败，error：" + ex.Message);
                WaitBox.Close();
            }
        }

        private static StringBuilder getRows(int iMaxCoverage, bool exportlongLat, ref int idx, LTEMgrsGridInfo mgrs)
        {
            StringBuilder rows = new StringBuilder();
            rows.Append(idx++ + ",");
            rows.Append(mgrs.MgrsString + ",");
            rows.Append(mgrs.newCellList.Count + ",");
            rows.Append(mgrs.iCoverage + ",");
            rows.Append(mgrs.strCoverageCell + ",");
            if (exportlongLat)
            {
                rows.Append(mgrs.CentLng + ",");
                rows.Append(mgrs.CentLat + ",");
            }
            rows.Append(mgrs.gridAvgSINR + ",");
            List<LteMgrsCell> ListCell = mgrs.CoverageCellList;
            for (int index = 0; index < iMaxCoverage; index++)
            {
                if (index > 0)
                    rows.Append(",");
                if (ListCell.Count - 1 >= index)
                {
                    rows.Append(ListCell[index].CellName + ",");
                    rows.Append(Math.Round(ListCell[index].AvgRsrp, 2));
                    continue;
                }
                rows.Append(" " + ",");
                rows.Append(" ");
            }

            return rows;
        }

        Dictionary<string, LTEMgrsGridInfo> dicGisData = null;
        private void setRectangleColor()
        {
            if (dicGisData == null)
                dicGisData = new Dictionary<string, LTEMgrsGridInfo>();
            for (int r = 0; r < gridViewDetail.RowCount; r++)
            {
                string strName = gridViewDetail.GetRowCellValue(r, gridViewDetail.Columns["栅格号"]).ToString();
                int iCoverage = Convert.ToInt32(gridViewDetail.GetRowCellValue(r, gridViewDetail.Columns["重叠度"]).ToString());
                Color c = Ranges.GetColor(iCoverage);
                dicGisData[strName] = dicResult[strName];
                dicGisData[strName].GridColor = c;
            }
        }

        private void RefreshResult()
        {
            setRectangleColor();
            LTEMgrsCoveLayer gLayer = mapForm.GetLayerBase(typeof(LTEMgrsCoveLayer)) as LTEMgrsCoveLayer;
            if (gLayer != null)
            {
                gLayer.iniData(dicGisData);
            }
            MainModel.FireDTDataChanged(MainModel.MainForm);
        }
        private void GisShowItem_Click(object sender, EventArgs e)
        {
            RefreshResult();
        }

        private void 重叠覆盖度区间修改ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            LteMgrsColorRangeSettingDlg dlg = new LteMgrsColorRangeSettingDlg();
            dlg.FixMinMax(Ranges.Minimum, Ranges.Maximum);
            dlg.MakeRangeModeOnly();
            dlg.FillColorRanges(Ranges.ColorRanges);
            dlg.InvalidatePointColor = Ranges.InvalidColor;

            if (DialogResult.OK != dlg.ShowDialog(this))
            {
                return;
            }

            Ranges.ColorRanges = new List<MasterCom.MControls.ColorRange>(dlg.ColorRanges);
            Ranges.InvalidColor = dlg.InvalidatePointColor;

            RefreshResult();
        }

        private void ExportLayer_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Filter = FileSimpleTypeHelper.Shp.FilterStr;
            dlg.FilterIndex = FileSimpleTypeHelper.Shp.FilterIndex + 1;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                LTEMgrsCoveLayer gLayer = mapForm.GetLayerBase(typeof(LTEMgrsCoveLayer)) as LTEMgrsCoveLayer;
                if (gLayer != null)
                {
                    //导出栅格图层
                    int res = gLayer.MakeShpFile(dlg.FileName, false, Color.Empty, false);
                    if (res > 0)
                    {
                        MessageBox.Show("导出图层成功");
                    }
                }
            }
        }

        private void gridViewDetail_DoubleClick(object sender, EventArgs e)
        {
            DataRowView dtView = gridViewDetail.GetFocusedRow() as DataRowView;
            if (dtView != null)
            {
                double longitude = Convert.ToDouble(dtView["中心经度"]);
                double latitude = Convert.ToDouble(dtView["中心纬度"]);

                mapForm.GoToView(longitude, latitude, 2000);
            }
        }
    }
}
