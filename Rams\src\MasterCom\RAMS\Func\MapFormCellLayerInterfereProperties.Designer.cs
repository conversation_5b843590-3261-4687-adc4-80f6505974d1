﻿namespace MasterCom.RAMS.Func
{
    partial class MapFormCellLayerInterfereProperties
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.Label label1;
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbxComment = new System.Windows.Forms.CheckBox();
            this.cbxAddress = new System.Windows.Forms.CheckBox();
            this.cbxProgress = new System.Windows.Forms.CheckBox();
            this.cbxReason = new System.Windows.Forms.CheckBox();
            this.cbxType = new System.Windows.Forms.CheckBox();
            this.cbxLatitude = new System.Windows.Forms.CheckBox();
            this.cbxLongitude = new System.Windows.Forms.CheckBox();
            this.cbxName = new System.Windows.Forms.CheckBox();
            this.cbxDisplay = new System.Windows.Forms.CheckBox();
            this.cbxDrawBTSLabel = new System.Windows.Forms.CheckBox();
            this.buttonFont = new System.Windows.Forms.Button();
            this.labelDisplayColor = new System.Windows.Forms.Label();
            this.labelColor = new System.Windows.Forms.Label();
            this.numericUpDownSize = new System.Windows.Forms.NumericUpDown();
            this.trackBarOpacity = new System.Windows.Forms.TrackBar();
            this.LabelOpacity = new System.Windows.Forms.Label();
            label1 = new System.Windows.Forms.Label();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownSize)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarOpacity)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(12, 91);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(35, 12);
            label1.TabIndex = 73;
            label1.Text = "Size:";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.cbxComment);
            this.groupBox1.Controls.Add(this.cbxAddress);
            this.groupBox1.Controls.Add(this.cbxProgress);
            this.groupBox1.Controls.Add(this.cbxReason);
            this.groupBox1.Controls.Add(this.cbxType);
            this.groupBox1.Controls.Add(this.cbxLatitude);
            this.groupBox1.Controls.Add(this.cbxLongitude);
            this.groupBox1.Controls.Add(this.cbxName);
            this.groupBox1.Location = new System.Drawing.Point(214, 4);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(167, 224);
            this.groupBox1.TabIndex = 1;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "Label";
            // 
            // cbxComment
            // 
            this.cbxComment.AutoSize = true;
            this.cbxComment.Location = new System.Drawing.Point(24, 198);
            this.cbxComment.Name = "cbxComment";
            this.cbxComment.Size = new System.Drawing.Size(66, 16);
            this.cbxComment.TabIndex = 0;
            this.cbxComment.Text = "Comment";
            this.cbxComment.UseVisualStyleBackColor = true;
            this.cbxComment.Visible = false;
            this.cbxComment.CheckedChanged += new System.EventHandler(this.cbxComment_CheckedChanged);
            // 
            // cbxAddress
            // 
            this.cbxAddress.AutoSize = true;
            this.cbxAddress.Location = new System.Drawing.Point(24, 176);
            this.cbxAddress.Name = "cbxAddress";
            this.cbxAddress.Size = new System.Drawing.Size(66, 16);
            this.cbxAddress.TabIndex = 0;
            this.cbxAddress.Text = "Address";
            this.cbxAddress.UseVisualStyleBackColor = true;
            this.cbxAddress.Visible = false;
            this.cbxAddress.CheckedChanged += new System.EventHandler(this.cbxAddress_CheckedChanged);
            // 
            // cbxProgress
            // 
            this.cbxProgress.AutoSize = true;
            this.cbxProgress.Location = new System.Drawing.Point(24, 154);
            this.cbxProgress.Name = "cbxProgress";
            this.cbxProgress.Size = new System.Drawing.Size(72, 16);
            this.cbxProgress.TabIndex = 0;
            this.cbxProgress.Text = "Progress";
            this.cbxProgress.UseVisualStyleBackColor = true;
            this.cbxProgress.Visible = false;
            this.cbxProgress.CheckedChanged += new System.EventHandler(this.cbxProgress_CheckedChanged);
            // 
            // cbxReason
            // 
            this.cbxReason.AutoSize = true;
            this.cbxReason.Location = new System.Drawing.Point(24, 132);
            this.cbxReason.Name = "cbxReason";
            this.cbxReason.Size = new System.Drawing.Size(60, 16);
            this.cbxReason.TabIndex = 0;
            this.cbxReason.Text = "Reason";
            this.cbxReason.UseVisualStyleBackColor = true;
            this.cbxReason.Visible = false;
            this.cbxReason.CheckedChanged += new System.EventHandler(this.checkReason_CheckedChanged);
            // 
            // cbxType
            // 
            this.cbxType.AutoSize = true;
            this.cbxType.Location = new System.Drawing.Point(24, 110);
            this.cbxType.Name = "cbxType";
            this.cbxType.Size = new System.Drawing.Size(48, 16);
            this.cbxType.TabIndex = 0;
            this.cbxType.Text = "Type";
            this.cbxType.UseVisualStyleBackColor = true;
            this.cbxType.Visible = false;
            this.cbxType.CheckedChanged += new System.EventHandler(this.cbxType_CheckedChanged);
            // 
            // cbxLatitude
            // 
            this.cbxLatitude.AutoSize = true;
            this.cbxLatitude.Location = new System.Drawing.Point(24, 87);
            this.cbxLatitude.Name = "cbxLatitude";
            this.cbxLatitude.Size = new System.Drawing.Size(72, 16);
            this.cbxLatitude.TabIndex = 0;
            this.cbxLatitude.Text = "Latitude";
            this.cbxLatitude.UseVisualStyleBackColor = true;
            this.cbxLatitude.Visible = false;
            this.cbxLatitude.CheckedChanged += new System.EventHandler(this.cbxLatitude_CheckedChanged);
            // 
            // cbxLongitude
            // 
            this.cbxLongitude.AutoSize = true;
            this.cbxLongitude.Location = new System.Drawing.Point(24, 65);
            this.cbxLongitude.Name = "cbxLongitude";
            this.cbxLongitude.Size = new System.Drawing.Size(78, 16);
            this.cbxLongitude.TabIndex = 0;
            this.cbxLongitude.Text = "Longitude";
            this.cbxLongitude.UseVisualStyleBackColor = true;
            this.cbxLongitude.Visible = false;
            this.cbxLongitude.CheckedChanged += new System.EventHandler(this.cbxLongitude_CheckedChanged);
            // 
            // cbxName
            // 
            this.cbxName.AutoSize = true;
            this.cbxName.Location = new System.Drawing.Point(24, 43);
            this.cbxName.Name = "cbxName";
            this.cbxName.Size = new System.Drawing.Size(48, 16);
            this.cbxName.TabIndex = 0;
            this.cbxName.Text = "Name";
            this.cbxName.UseVisualStyleBackColor = true;
            this.cbxName.CheckedChanged += new System.EventHandler(this.cbxName_CheckedChanged);
            // 
            // cbxDisplay
            // 
            this.cbxDisplay.AutoSize = true;
            this.cbxDisplay.Location = new System.Drawing.Point(14, 4);
            this.cbxDisplay.Name = "cbxDisplay";
            this.cbxDisplay.Size = new System.Drawing.Size(66, 16);
            this.cbxDisplay.TabIndex = 0;
            this.cbxDisplay.Text = "Display";
            this.cbxDisplay.UseVisualStyleBackColor = true;
            this.cbxDisplay.CheckedChanged += new System.EventHandler(this.cbxDisplay_CheckedChanged);
            // 
            // cbxDrawBTSLabel
            // 
            this.cbxDrawBTSLabel.AutoSize = true;
            this.cbxDrawBTSLabel.Location = new System.Drawing.Point(14, 136);
            this.cbxDrawBTSLabel.Name = "cbxDrawBTSLabel";
            this.cbxDrawBTSLabel.Size = new System.Drawing.Size(102, 16);
            this.cbxDrawBTSLabel.TabIndex = 51;
            this.cbxDrawBTSLabel.Text = "Display Label";
            this.cbxDrawBTSLabel.UseVisualStyleBackColor = true;
            this.cbxDrawBTSLabel.CheckedChanged += new System.EventHandler(this.checkBoxDrawBTSLabel_CheckedChanged);
            // 
            // buttonFont
            // 
            this.buttonFont.Location = new System.Drawing.Point(131, 132);
            this.buttonFont.Name = "buttonFont";
            this.buttonFont.Size = new System.Drawing.Size(53, 23);
            this.buttonFont.TabIndex = 72;
            this.buttonFont.Text = "Font...";
            this.buttonFont.UseVisualStyleBackColor = true;
            this.buttonFont.Click += new System.EventHandler(this.buttonFont_Click);
            // 
            // labelDisplayColor
            // 
            this.labelDisplayColor.BackColor = System.Drawing.Color.Orange;
            this.labelDisplayColor.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.labelDisplayColor.Location = new System.Drawing.Point(56, 43);
            this.labelDisplayColor.Name = "labelDisplayColor";
            this.labelDisplayColor.Size = new System.Drawing.Size(25, 25);
            this.labelDisplayColor.TabIndex = 71;
            this.labelDisplayColor.Click += new System.EventHandler(this.labelDisplayColor_Click);
            // 
            // labelColor
            // 
            this.labelColor.Location = new System.Drawing.Point(12, 43);
            this.labelColor.Name = "labelColor";
            this.labelColor.Size = new System.Drawing.Size(47, 20);
            this.labelColor.TabIndex = 68;
            this.labelColor.Text = "Color:";
            this.labelColor.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // numericUpDownSize
            // 
            this.numericUpDownSize.Location = new System.Drawing.Point(56, 87);
            this.numericUpDownSize.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numericUpDownSize.Minimum = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.numericUpDownSize.Name = "numericUpDownSize";
            this.numericUpDownSize.Size = new System.Drawing.Size(34, 21);
            this.numericUpDownSize.TabIndex = 74;
            this.numericUpDownSize.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.numericUpDownSize.ValueChanged += new System.EventHandler(this.numericUpDownSize_ValueChanged);
            // 
            // trackBarOpacity
            // 
            this.trackBarOpacity.LargeChange = 32;
            this.trackBarOpacity.Location = new System.Drawing.Point(65, 173);
            this.trackBarOpacity.Maximum = 255;
            this.trackBarOpacity.Name = "trackBarOpacity";
            this.trackBarOpacity.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.trackBarOpacity.Size = new System.Drawing.Size(143, 45);
            this.trackBarOpacity.TabIndex = 76;
            this.trackBarOpacity.TickFrequency = 32;
            this.trackBarOpacity.Value = 255;
            // 
            // LabelOpacity
            // 
            this.LabelOpacity.Location = new System.Drawing.Point(12, 184);
            this.LabelOpacity.Name = "LabelOpacity";
            this.LabelOpacity.Size = new System.Drawing.Size(56, 16);
            this.LabelOpacity.TabIndex = 75;
            this.LabelOpacity.Text = "Opacity: ";
            // 
            // MapFormCellLayerInterfereProperties
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.trackBarOpacity);
            this.Controls.Add(this.LabelOpacity);
            this.Controls.Add(this.numericUpDownSize);
            this.Controls.Add(label1);
            this.Controls.Add(this.buttonFont);
            this.Controls.Add(this.labelDisplayColor);
            this.Controls.Add(this.labelColor);
            this.Controls.Add(this.cbxDrawBTSLabel);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.cbxDisplay);
            this.Name = "MapFormCellLayerInterfereProperties";
            this.Size = new System.Drawing.Size(394, 239);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownSize)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarOpacity)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox cbxReason;
        private System.Windows.Forms.CheckBox cbxType;
        private System.Windows.Forms.CheckBox cbxLatitude;
        private System.Windows.Forms.CheckBox cbxLongitude;
        private System.Windows.Forms.CheckBox cbxName;
        private System.Windows.Forms.CheckBox cbxDisplay;
        private System.Windows.Forms.CheckBox cbxDrawBTSLabel;
        private System.Windows.Forms.Button buttonFont;
        private System.Windows.Forms.Label labelDisplayColor;
        private System.Windows.Forms.Label labelColor;
        private System.Windows.Forms.CheckBox cbxComment;
        private System.Windows.Forms.CheckBox cbxAddress;
        private System.Windows.Forms.CheckBox cbxProgress;
        private System.Windows.Forms.NumericUpDown numericUpDownSize;
        private System.Windows.Forms.TrackBar trackBarOpacity;
        private System.Windows.Forms.Label LabelOpacity;
    }
}
