﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Stat
{
    /// <summary>
    /// 采样点过滤预存公式
    /// </summary>
    public class TestpointFilterExpression
    {
        public string groupName { get; set; } //公式组名
        public TestpointFilterExpression()
        {
        }
        public TestpointFilterExpression(string groupName)
        {
            this.groupName = groupName;
        }

        public override string ToString()
        {
            return cexpresses.Count + "条" + " - " + groupName;
        }

        public List<CExpress> cexpresses { get; set; } = new List<CExpress>();

        public Dictionary<string, object> Param
        {
            get 
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["groupName"] = groupName;
                List<object> ces = new List<object>();
                foreach (CExpress ce in cexpresses)
                {
                    ces.Add(ce.Param);
                }
                param["cexpresses"] = ces;
                return param;
            }
            set 
            {
                groupName = (string)value["groupName"];
                cexpresses.Clear();
                List<object> ces = (List<object>)value["cexpresses"];
                foreach (object o in ces)
                {
                    Dictionary<string, object> ceParam = (Dictionary<string, object>)o;
                    CExpress ce = new CExpress();
                    ce.Param = ceParam;
                    cexpresses.Add(ce);
                }

            }
        }


    }

}
