﻿namespace MasterCom.RAMS.NewBlackBlock
{
    partial class BlackBlockForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(BlackBlockForm));
            this.pnlInfo = new System.Windows.Forms.Panel();
            this.label6 = new System.Windows.Forms.Label();
            this.lblInstructionCreateAndClose = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.lblInstructionClose = new System.Windows.Forms.Label();
            this.lblColor = new System.Windows.Forms.Label();
            this.lblInstructionCreate = new System.Windows.Forms.Label();
            this.txtInfo = new System.Windows.Forms.TextBox();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl4 = new DevExpress.XtraEditors.GroupControl();
            this.radioByTime = new System.Windows.Forms.RadioButton();
            this.colorClose = new DevExpress.XtraEditors.ColorEdit();
            this.radioByCnt = new System.Windows.Forms.RadioButton();
            this.radioUnify = new System.Windows.Forms.RadioButton();
            this.chkBbIDVisible = new System.Windows.Forms.CheckBox();
            this.chbShowClose = new System.Windows.Forms.CheckBox();
            this.treeList1 = new DevExpress.XtraTreeList.TreeList();
            this.treeListColumnSN = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnCity = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnID = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnBranch = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnGroup = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnGrid = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnName = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnGridDesc = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnAreaName = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnRoadName = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnHandleUser = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnVIP = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnStaticWeight = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnProcPer = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnAbDays = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnNormalDays = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnAbEvents = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnAbEventDate = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnAbEventLon = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnAbEventLat = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnAbEventName = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnStatus = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnStatusByFiles = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnCenterLong = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnCenterLat = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnPlaceDes = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnBelongGrid = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnReason = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnAttachFileDesc = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnCreateDate = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnCloseDate = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnFirstAbDate = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnLastAbDate = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnLastTestDate = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnAbEventDes = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnNoStat = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnGoodDays = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnValidateDay = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnValidStatus = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnLastEventDesc = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnCellNames = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnNetType = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnCauseMain = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnCauseSub = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnCauseDetail = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnProblem = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnSolution = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnDealTime = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnDeadline = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnPlan2CloseTime = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnDealPerson = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnNearestBuilding = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tsmItemProcessBlock = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmItemViewDetail = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmItemShowESResult = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmItemExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmItemExportBBReport = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmItemSelectedBBReport = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmItemGeneralBBInfo = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmItemShapefile = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmItemDownload = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmItemUpload = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmItemDelete = new System.Windows.Forms.ToolStripMenuItem();
            this.pnlInfo.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).BeginInit();
            this.groupControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorClose.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeList1)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlInfo
            // 
            this.pnlInfo.Controls.Add(this.label6);
            this.pnlInfo.Controls.Add(this.lblInstructionCreateAndClose);
            this.pnlInfo.Controls.Add(this.label1);
            this.pnlInfo.Controls.Add(this.lblInstructionClose);
            this.pnlInfo.Controls.Add(this.lblColor);
            this.pnlInfo.Controls.Add(this.lblInstructionCreate);
            this.pnlInfo.Controls.Add(this.txtInfo);
            this.pnlInfo.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.pnlInfo.Location = new System.Drawing.Point(0, 408);
            this.pnlInfo.Name = "pnlInfo";
            this.pnlInfo.Size = new System.Drawing.Size(1096, 73);
            this.pnlInfo.TabIndex = 2;
            this.pnlInfo.Visible = false;
            // 
            // label6
            // 
            this.label6.BackColor = System.Drawing.Color.Turquoise;
            this.label6.Location = new System.Drawing.Point(1023, 34);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(29, 29);
            this.label6.TabIndex = 6;
            // 
            // lblInstructionCreateAndClose
            // 
            this.lblInstructionCreateAndClose.AutoSize = true;
            this.lblInstructionCreateAndClose.Location = new System.Drawing.Point(702, 41);
            this.lblInstructionCreateAndClose.Name = "lblInstructionCreateAndClose";
            this.lblInstructionCreateAndClose.Size = new System.Drawing.Size(279, 14);
            this.lblInstructionCreateAndClose.TabIndex = 5;
            this.lblInstructionCreateAndClose.Text = "2011-07-01至2011-07-05创建并关闭的黑点颜色：";
            // 
            // label1
            // 
            this.label1.BackColor = System.Drawing.Color.Chartreuse;
            this.label1.Location = new System.Drawing.Point(636, 34);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(29, 29);
            this.label1.TabIndex = 4;
            // 
            // lblInstructionClose
            // 
            this.lblInstructionClose.AutoSize = true;
            this.lblInstructionClose.Location = new System.Drawing.Point(357, 41);
            this.lblInstructionClose.Name = "lblInstructionClose";
            this.lblInstructionClose.Size = new System.Drawing.Size(243, 14);
            this.lblInstructionClose.TabIndex = 3;
            this.lblInstructionClose.Text = "2011-07-01至2011-07-05关闭的黑点颜色：";
            // 
            // lblColor
            // 
            this.lblColor.BackColor = System.Drawing.Color.DodgerBlue;
            this.lblColor.Location = new System.Drawing.Point(293, 34);
            this.lblColor.Name = "lblColor";
            this.lblColor.Size = new System.Drawing.Size(29, 29);
            this.lblColor.TabIndex = 2;
            // 
            // lblInstructionCreate
            // 
            this.lblInstructionCreate.AutoSize = true;
            this.lblInstructionCreate.Location = new System.Drawing.Point(14, 41);
            this.lblInstructionCreate.Name = "lblInstructionCreate";
            this.lblInstructionCreate.Size = new System.Drawing.Size(243, 14);
            this.lblInstructionCreate.TabIndex = 1;
            this.lblInstructionCreate.Text = "2011-07-01至2011-07-05创建的黑点颜色：";
            // 
            // txtInfo
            // 
            this.txtInfo.Dock = System.Windows.Forms.DockStyle.Top;
            this.txtInfo.Location = new System.Drawing.Point(0, 0);
            this.txtInfo.Name = "txtInfo";
            this.txtInfo.ReadOnly = true;
            this.txtInfo.Size = new System.Drawing.Size(1096, 22);
            this.txtInfo.TabIndex = 0;
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Default;
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.groupControl4);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.AutoScroll = true;
            this.splitContainerControl1.Panel2.Controls.Add(this.treeList1);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1096, 408);
            this.splitContainerControl1.SplitterPosition = 67;
            this.splitContainerControl1.TabIndex = 10;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // groupControl4
            // 
            this.groupControl4.Controls.Add(this.radioByTime);
            this.groupControl4.Controls.Add(this.colorClose);
            this.groupControl4.Controls.Add(this.radioByCnt);
            this.groupControl4.Controls.Add(this.radioUnify);
            this.groupControl4.Controls.Add(this.chkBbIDVisible);
            this.groupControl4.Controls.Add(this.chbShowClose);
            this.groupControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl4.Location = new System.Drawing.Point(0, 0);
            this.groupControl4.Name = "groupControl4";
            this.groupControl4.Size = new System.Drawing.Size(1092, 67);
            this.groupControl4.TabIndex = 7;
            this.groupControl4.Text = "GIS设置";
            // 
            // radioByTime
            // 
            this.radioByTime.AutoSize = true;
            this.radioByTime.Location = new System.Drawing.Point(401, 35);
            this.radioByTime.Name = "radioByTime";
            this.radioByTime.Size = new System.Drawing.Size(138, 18);
            this.radioByTime.TabIndex = 8;
            this.radioByTime.Text = "按创建/关闭时间着色";
            this.radioByTime.UseVisualStyleBackColor = true;
            this.radioByTime.Visible = false;
            this.radioByTime.CheckedChanged += new System.EventHandler(this.radioByTime_CheckedChanged);
            // 
            // colorClose
            // 
            this.colorClose.EditValue = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            this.colorClose.Location = new System.Drawing.Point(226, 34);
            this.colorClose.Name = "colorClose";
            this.colorClose.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorClose.Size = new System.Drawing.Size(51, 21);
            this.colorClose.TabIndex = 7;
            this.colorClose.EditValueChanged += new System.EventHandler(this.colorClose_EditValueChanged);
            // 
            // radioByCnt
            // 
            this.radioByCnt.AutoSize = true;
            this.radioByCnt.Location = new System.Drawing.Point(294, 35);
            this.radioByCnt.Name = "radioByCnt";
            this.radioByCnt.Size = new System.Drawing.Size(97, 18);
            this.radioByCnt.TabIndex = 6;
            this.radioByCnt.Text = "按事件数着色";
            this.radioByCnt.UseVisualStyleBackColor = true;
            this.radioByCnt.CheckedChanged += new System.EventHandler(this.radioByCnt_CheckedChanged);
            // 
            // radioUnify
            // 
            this.radioUnify.AutoSize = true;
            this.radioUnify.Checked = true;
            this.radioUnify.Location = new System.Drawing.Point(156, 35);
            this.radioUnify.Name = "radioUnify";
            this.radioUnify.Size = new System.Drawing.Size(73, 18);
            this.radioUnify.TabIndex = 6;
            this.radioUnify.TabStop = true;
            this.radioUnify.Text = "统一着色";
            this.radioUnify.UseVisualStyleBackColor = true;
            this.radioUnify.CheckedChanged += new System.EventHandler(this.radioUnify_CheckedChanged);
            // 
            // chkBbIDVisible
            // 
            this.chkBbIDVisible.AutoSize = true;
            this.chkBbIDVisible.Checked = true;
            this.chkBbIDVisible.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkBbIDVisible.Location = new System.Drawing.Point(575, 34);
            this.chkBbIDVisible.Name = "chkBbIDVisible";
            this.chkBbIDVisible.Size = new System.Drawing.Size(86, 18);
            this.chkBbIDVisible.TabIndex = 5;
            this.chkBbIDVisible.Text = "显示黑点ID";
            this.chkBbIDVisible.UseVisualStyleBackColor = true;
            this.chkBbIDVisible.CheckedChanged += new System.EventHandler(this.chkBbIDVisible_CheckedChanged);
            // 
            // chbShowClose
            // 
            this.chbShowClose.AutoSize = true;
            this.chbShowClose.Checked = true;
            this.chbShowClose.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chbShowClose.Location = new System.Drawing.Point(15, 34);
            this.chbShowClose.Name = "chbShowClose";
            this.chbShowClose.Size = new System.Drawing.Size(122, 18);
            this.chbShowClose.TabIndex = 5;
            this.chbShowClose.Text = "显示已经关闭黑点";
            this.chbShowClose.UseVisualStyleBackColor = true;
            this.chbShowClose.CheckedChanged += new System.EventHandler(this.chbShowClose_CheckedChanged);
            // 
            // treeList1
            // 
            this.treeList1.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.treeListColumnSN,
            this.treeListColumnCity,
            this.treeListColumnID,
            this.treeListColumnBranch,
            this.treeListColumnGroup,
            this.treeListColumnGrid,
            this.treeListColumnName,
            this.treeListColumnGridDesc,
            this.treeListColumnAreaName,
            this.treeListColumnRoadName,
            this.treeListColumnHandleUser,
            this.treeListColumnVIP,
            this.treeListColumnStaticWeight,
            this.treeListColumnProcPer,
            this.treeListColumnAbDays,
            this.treeListColumnNormalDays,
            this.treeListColumnAbEvents,
            this.treeListColumnAbEventDate,
            this.treeListColumnAbEventLon,
            this.treeListColumnAbEventLat,
            this.treeListColumnAbEventName,
            this.treeListColumnStatus,
            this.treeListColumnStatusByFiles,
            this.treeListColumnCenterLong,
            this.treeListColumnCenterLat,
            this.treeListColumnPlaceDes,
            this.treeListColumnBelongGrid,
            this.treeListColumnReason,
            this.treeListColumnAttachFileDesc,
            this.treeListColumnCreateDate,
            this.treeListColumnCloseDate,
            this.treeListColumnFirstAbDate,
            this.treeListColumnLastAbDate,
            this.treeListColumnLastTestDate,
            this.treeListColumnAbEventDes,
            this.treeListColumnNoStat,
            this.treeListColumnGoodDays,
            this.treeListColumnValidateDay,
            this.treeListColumnValidStatus,
            this.treeListColumnLastEventDesc,
            this.treeListColumnCellNames,
            this.treeListColumnNetType,
            this.treeListColumnCauseMain,
            this.treeListColumnCauseSub,
            this.treeListColumnCauseDetail,
            this.treeListColumnProblem,
            this.treeListColumnSolution,
            this.treeListColumnDealTime,
            this.treeListColumnDeadline,
            this.treeListColumnPlan2CloseTime,
            this.treeListColumnDealPerson,
            this.treeListColumnNearestBuilding});
            this.treeList1.ContextMenuStrip = this.contextMenuStrip;
            this.treeList1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeList1.Location = new System.Drawing.Point(0, 0);
            this.treeList1.Name = "treeList1";
            this.treeList1.OptionsBehavior.Editable = false;
            this.treeList1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.treeList1.OptionsSelection.MultiSelect = true;
            this.treeList1.OptionsView.AutoWidth = false;
            this.treeList1.OptionsView.EnableAppearanceEvenRow = true;
            this.treeList1.OptionsView.ShowIndicator = false;
            this.treeList1.Size = new System.Drawing.Size(1092, 333);
            this.treeList1.TabIndex = 0;
            this.treeList1.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeList1_MouseDoubleClick);
            this.treeList1.MouseDown += new System.Windows.Forms.MouseEventHandler(this.treeList1_MouseDown);
            // 
            // treeListColumnSN
            // 
            this.treeListColumnSN.Caption = "序号";
            this.treeListColumnSN.FieldName = "city";
            this.treeListColumnSN.Name = "treeListColumnSN";
            this.treeListColumnSN.Visible = true;
            this.treeListColumnSN.VisibleIndex = 1;
            // 
            // treeListColumnCity
            // 
            this.treeListColumnCity.Caption = "地市";
            this.treeListColumnCity.FieldName = "地市";
            this.treeListColumnCity.Name = "treeListColumnCity";
            this.treeListColumnCity.Visible = true;
            this.treeListColumnCity.VisibleIndex = 0;
            // 
            // treeListColumnID
            // 
            this.treeListColumnID.Caption = "黑点ID";
            this.treeListColumnID.FieldName = "treeListColumn3";
            this.treeListColumnID.Name = "treeListColumnID";
            this.treeListColumnID.Visible = true;
            this.treeListColumnID.VisibleIndex = 2;
            // 
            // treeListColumnBranch
            // 
            this.treeListColumnBranch.Caption = "分公司";
            this.treeListColumnBranch.FieldName = "treeListColumn4";
            this.treeListColumnBranch.Name = "treeListColumnBranch";
            // 
            // treeListColumnGroup
            // 
            this.treeListColumnGroup.Caption = "小组";
            this.treeListColumnGroup.FieldName = "treeListColumn5";
            this.treeListColumnGroup.Name = "treeListColumnGroup";
            // 
            // treeListColumnGrid
            // 
            this.treeListColumnGrid.Caption = "网格";
            this.treeListColumnGrid.FieldName = "treeListColumn6";
            this.treeListColumnGrid.Name = "treeListColumnGrid";
            // 
            // treeListColumnName
            // 
            this.treeListColumnName.Caption = "名称";
            this.treeListColumnName.FieldName = "treeListColumn7";
            this.treeListColumnName.Name = "treeListColumnName";
            this.treeListColumnName.Visible = true;
            this.treeListColumnName.VisibleIndex = 3;
            // 
            // treeListColumnGridDesc
            // 
            this.treeListColumnGridDesc.Caption = "网格";
            this.treeListColumnGridDesc.FieldName = "treeListColumn8";
            this.treeListColumnGridDesc.Name = "treeListColumnGridDesc";
            // 
            // treeListColumnAreaName
            // 
            this.treeListColumnAreaName.Caption = "所属区域";
            this.treeListColumnAreaName.FieldName = "所属区域";
            this.treeListColumnAreaName.Name = "treeListColumnAreaName";
            this.treeListColumnAreaName.Visible = true;
            this.treeListColumnAreaName.VisibleIndex = 4;
            // 
            // treeListColumnRoadName
            // 
            this.treeListColumnRoadName.Caption = "所属道路";
            this.treeListColumnRoadName.FieldName = "treeListColumn10";
            this.treeListColumnRoadName.Name = "treeListColumnRoadName";
            this.treeListColumnRoadName.Visible = true;
            this.treeListColumnRoadName.VisibleIndex = 5;
            // 
            // treeListColumnHandleUser
            // 
            this.treeListColumnHandleUser.Caption = "处理人";
            this.treeListColumnHandleUser.FieldName = "treeListColumn11";
            this.treeListColumnHandleUser.Name = "treeListColumnHandleUser";
            // 
            // treeListColumnVIP
            // 
            this.treeListColumnVIP.Caption = "权重值";
            this.treeListColumnVIP.FieldName = "treeListColumn12";
            this.treeListColumnVIP.Name = "treeListColumnVIP";
            // 
            // treeListColumnStaticWeight
            // 
            this.treeListColumnStaticWeight.Caption = "权重";
            this.treeListColumnStaticWeight.FieldName = "treeListColumn13";
            this.treeListColumnStaticWeight.Name = "treeListColumnStaticWeight";
            // 
            // treeListColumnProcPer
            // 
            this.treeListColumnProcPer.Caption = "处理进度";
            this.treeListColumnProcPer.FieldName = "treeListColumn14";
            this.treeListColumnProcPer.Name = "treeListColumnProcPer";
            // 
            // treeListColumnAbDays
            // 
            this.treeListColumnAbDays.Caption = "问题天数";
            this.treeListColumnAbDays.FieldName = "treeListColumn15";
            this.treeListColumnAbDays.Name = "treeListColumnAbDays";
            this.treeListColumnAbDays.Visible = true;
            this.treeListColumnAbDays.VisibleIndex = 6;
            // 
            // treeListColumnNormalDays
            // 
            this.treeListColumnNormalDays.Caption = "正常天数";
            this.treeListColumnNormalDays.FieldName = "treeListColumn16";
            this.treeListColumnNormalDays.Name = "treeListColumnNormalDays";
            this.treeListColumnNormalDays.Visible = true;
            this.treeListColumnNormalDays.VisibleIndex = 7;
            // 
            // treeListColumnAbEvents
            // 
            this.treeListColumnAbEvents.Caption = "异常事件个数";
            this.treeListColumnAbEvents.FieldName = "treeListColumn17";
            this.treeListColumnAbEvents.Name = "treeListColumnAbEvents";
            this.treeListColumnAbEvents.Visible = true;
            this.treeListColumnAbEvents.VisibleIndex = 8;
            this.treeListColumnAbEvents.Width = 120;
            // 
            // treeListColumnAbEventDate
            // 
            this.treeListColumnAbEventDate.Caption = "异常事件时间";
            this.treeListColumnAbEventDate.FieldName = "treeListColumn18";
            this.treeListColumnAbEventDate.Name = "treeListColumnAbEventDate";
            this.treeListColumnAbEventDate.Visible = true;
            this.treeListColumnAbEventDate.VisibleIndex = 9;
            this.treeListColumnAbEventDate.Width = 120;
            // 
            // treeListColumnAbEventLon
            // 
            this.treeListColumnAbEventLon.Caption = "异常事件经度";
            this.treeListColumnAbEventLon.FieldName = "treeListColumn19";
            this.treeListColumnAbEventLon.Name = "treeListColumnAbEventLon";
            this.treeListColumnAbEventLon.Visible = true;
            this.treeListColumnAbEventLon.VisibleIndex = 10;
            this.treeListColumnAbEventLon.Width = 120;
            // 
            // treeListColumnAbEventLat
            // 
            this.treeListColumnAbEventLat.Caption = "异常事件纬度";
            this.treeListColumnAbEventLat.FieldName = "treeListColumn20";
            this.treeListColumnAbEventLat.Name = "treeListColumnAbEventLat";
            this.treeListColumnAbEventLat.Visible = true;
            this.treeListColumnAbEventLat.VisibleIndex = 11;
            this.treeListColumnAbEventLat.Width = 120;
            // 
            // treeListColumnAbEventName
            // 
            this.treeListColumnAbEventName.Caption = "异常事件名称";
            this.treeListColumnAbEventName.FieldName = "treeListColumn21";
            this.treeListColumnAbEventName.Name = "treeListColumnAbEventName";
            this.treeListColumnAbEventName.Visible = true;
            this.treeListColumnAbEventName.VisibleIndex = 12;
            this.treeListColumnAbEventName.Width = 120;
            // 
            // treeListColumnStatus
            // 
            this.treeListColumnStatus.Caption = "黑点状态";
            this.treeListColumnStatus.FieldName = "treeListColumn22";
            this.treeListColumnStatus.Name = "treeListColumnStatus";
            this.treeListColumnStatus.Visible = true;
            this.treeListColumnStatus.VisibleIndex = 13;
            // 
            // treeListColumnStatusByFiles
            // 
            this.treeListColumnStatusByFiles.Caption = "预状态";
            this.treeListColumnStatusByFiles.FieldName = "预状态";
            this.treeListColumnStatusByFiles.Name = "treeListColumnStatusByFiles";
            // 
            // treeListColumnCenterLong
            // 
            this.treeListColumnCenterLong.Caption = "中心点经度";
            this.treeListColumnCenterLong.FieldName = "treeListColumn24";
            this.treeListColumnCenterLong.Name = "treeListColumnCenterLong";
            this.treeListColumnCenterLong.Visible = true;
            this.treeListColumnCenterLong.VisibleIndex = 14;
            this.treeListColumnCenterLong.Width = 85;
            // 
            // treeListColumnCenterLat
            // 
            this.treeListColumnCenterLat.Caption = "中心点纬度";
            this.treeListColumnCenterLat.FieldName = "treeListColumn25";
            this.treeListColumnCenterLat.Name = "treeListColumnCenterLat";
            this.treeListColumnCenterLat.Visible = true;
            this.treeListColumnCenterLat.VisibleIndex = 15;
            this.treeListColumnCenterLat.Width = 85;
            // 
            // treeListColumnPlaceDes
            // 
            this.treeListColumnPlaceDes.Caption = "位置描述";
            this.treeListColumnPlaceDes.FieldName = "treeListColumn26";
            this.treeListColumnPlaceDes.Name = "treeListColumnPlaceDes";
            this.treeListColumnPlaceDes.Visible = true;
            this.treeListColumnPlaceDes.VisibleIndex = 16;
            // 
            // treeListColumnBelongGrid
            // 
            this.treeListColumnBelongGrid.Caption = "归属网格";
            this.treeListColumnBelongGrid.FieldName = "treeListColumn27";
            this.treeListColumnBelongGrid.Name = "treeListColumnBelongGrid";
            this.treeListColumnBelongGrid.Visible = true;
            this.treeListColumnBelongGrid.VisibleIndex = 17;
            // 
            // treeListColumnReason
            // 
            this.treeListColumnReason.Caption = "问题原因";
            this.treeListColumnReason.FieldName = "treeListColumn28";
            this.treeListColumnReason.Name = "treeListColumnReason";
            this.treeListColumnReason.Visible = true;
            this.treeListColumnReason.VisibleIndex = 18;
            // 
            // treeListColumnAttachFileDesc
            // 
            this.treeListColumnAttachFileDesc.Caption = "解决方案附件";
            this.treeListColumnAttachFileDesc.FieldName = "treeListColumn1";
            this.treeListColumnAttachFileDesc.Name = "treeListColumnAttachFileDesc";
            this.treeListColumnAttachFileDesc.Width = 120;
            // 
            // treeListColumnCreateDate
            // 
            this.treeListColumnCreateDate.Caption = "黑点创建时间";
            this.treeListColumnCreateDate.FieldName = "treeListColumn2";
            this.treeListColumnCreateDate.Name = "treeListColumnCreateDate";
            this.treeListColumnCreateDate.Visible = true;
            this.treeListColumnCreateDate.VisibleIndex = 19;
            this.treeListColumnCreateDate.Width = 120;
            // 
            // treeListColumnCloseDate
            // 
            this.treeListColumnCloseDate.Caption = "黑点关闭时间";
            this.treeListColumnCloseDate.FieldName = "treeListColumn3";
            this.treeListColumnCloseDate.Name = "treeListColumnCloseDate";
            this.treeListColumnCloseDate.Visible = true;
            this.treeListColumnCloseDate.VisibleIndex = 20;
            this.treeListColumnCloseDate.Width = 120;
            // 
            // treeListColumnFirstAbDate
            // 
            this.treeListColumnFirstAbDate.Caption = "第一个异常时间";
            this.treeListColumnFirstAbDate.FieldName = "treeListColumn4";
            this.treeListColumnFirstAbDate.Name = "treeListColumnFirstAbDate";
            this.treeListColumnFirstAbDate.Visible = true;
            this.treeListColumnFirstAbDate.VisibleIndex = 21;
            this.treeListColumnFirstAbDate.Width = 120;
            // 
            // treeListColumnLastAbDate
            // 
            this.treeListColumnLastAbDate.Caption = "最后异常时间";
            this.treeListColumnLastAbDate.FieldName = "treeListColumn5";
            this.treeListColumnLastAbDate.Name = "treeListColumnLastAbDate";
            this.treeListColumnLastAbDate.Visible = true;
            this.treeListColumnLastAbDate.VisibleIndex = 22;
            this.treeListColumnLastAbDate.Width = 120;
            // 
            // treeListColumnLastTestDate
            // 
            this.treeListColumnLastTestDate.Caption = "最后测试时间";
            this.treeListColumnLastTestDate.FieldName = "treeListColumn6";
            this.treeListColumnLastTestDate.Name = "treeListColumnLastTestDate";
            this.treeListColumnLastTestDate.Visible = true;
            this.treeListColumnLastTestDate.VisibleIndex = 23;
            this.treeListColumnLastTestDate.Width = 120;
            // 
            // treeListColumnAbEventDes
            // 
            this.treeListColumnAbEventDes.Caption = "事件详情";
            this.treeListColumnAbEventDes.FieldName = "treeListColumn7";
            this.treeListColumnAbEventDes.Name = "treeListColumnAbEventDes";
            this.treeListColumnAbEventDes.Visible = true;
            this.treeListColumnAbEventDes.VisibleIndex = 24;
            // 
            // treeListColumnNoStat
            // 
            this.treeListColumnNoStat.Caption = "是否考核";
            this.treeListColumnNoStat.FieldName = "treeListColumn8";
            this.treeListColumnNoStat.Name = "treeListColumnNoStat";
            this.treeListColumnNoStat.Visible = true;
            this.treeListColumnNoStat.VisibleIndex = 25;
            // 
            // treeListColumnGoodDays
            // 
            this.treeListColumnGoodDays.Caption = "最近正常测试天数";
            this.treeListColumnGoodDays.FieldName = "treeListColumn9";
            this.treeListColumnGoodDays.Name = "treeListColumnGoodDays";
            this.treeListColumnGoodDays.Width = 120;
            // 
            // treeListColumnValidateDay
            // 
            this.treeListColumnValidateDay.Caption = "最近验证日期";
            this.treeListColumnValidateDay.FieldName = "treeListColumn1";
            this.treeListColumnValidateDay.Name = "treeListColumnValidateDay";
            this.treeListColumnValidateDay.Width = 120;
            // 
            // treeListColumnValidStatus
            // 
            this.treeListColumnValidStatus.Caption = "验证情况";
            this.treeListColumnValidStatus.FieldName = "treeListColumn2";
            this.treeListColumnValidStatus.Name = "treeListColumnValidStatus";
            // 
            // treeListColumnLastEventDesc
            // 
            this.treeListColumnLastEventDesc.Caption = "最近异常事件";
            this.treeListColumnLastEventDesc.FieldName = "treeListColumn3";
            this.treeListColumnLastEventDesc.Name = "treeListColumnLastEventDesc";
            this.treeListColumnLastEventDesc.Visible = true;
            this.treeListColumnLastEventDesc.VisibleIndex = 26;
            this.treeListColumnLastEventDesc.Width = 120;
            // 
            // treeListColumnCellNames
            // 
            this.treeListColumnCellNames.Caption = "相关小区";
            this.treeListColumnCellNames.FieldName = "treeListColumn4";
            this.treeListColumnCellNames.Name = "treeListColumnCellNames";
            this.treeListColumnCellNames.Visible = true;
            this.treeListColumnCellNames.VisibleIndex = 27;
            // 
            // treeListColumnNetType
            // 
            this.treeListColumnNetType.Caption = "网络类型";
            this.treeListColumnNetType.FieldName = "treeListColumn5";
            this.treeListColumnNetType.Name = "treeListColumnNetType";
            // 
            // treeListColumnCauseMain
            // 
            this.treeListColumnCauseMain.Caption = "主因";
            this.treeListColumnCauseMain.FieldName = "treeListColumn6";
            this.treeListColumnCauseMain.Name = "treeListColumnCauseMain";
            // 
            // treeListColumnCauseSub
            // 
            this.treeListColumnCauseSub.Caption = "问题原因";
            this.treeListColumnCauseSub.FieldName = "treeListColumn7";
            this.treeListColumnCauseSub.Name = "treeListColumnCauseSub";
            // 
            // treeListColumnCauseDetail
            // 
            this.treeListColumnCauseDetail.Caption = "原因分析";
            this.treeListColumnCauseDetail.FieldName = "treeListColumn8";
            this.treeListColumnCauseDetail.Name = "treeListColumnCauseDetail";
            // 
            // treeListColumnProblem
            // 
            this.treeListColumnProblem.Caption = "问题分析";
            this.treeListColumnProblem.FieldName = "treeListColumn1";
            this.treeListColumnProblem.Name = "treeListColumnProblem";
            // 
            // treeListColumnSolution
            // 
            this.treeListColumnSolution.Caption = "解决办法";
            this.treeListColumnSolution.FieldName = "treeListColumn2";
            this.treeListColumnSolution.Name = "treeListColumnSolution";
            // 
            // treeListColumnDealTime
            // 
            this.treeListColumnDealTime.Caption = "最近处理时间";
            this.treeListColumnDealTime.FieldName = "treeListColumn3";
            this.treeListColumnDealTime.Name = "treeListColumnDealTime";
            this.treeListColumnDealTime.Width = 120;
            // 
            // treeListColumnDeadline
            // 
            this.treeListColumnDeadline.Caption = "最迟反馈日期";
            this.treeListColumnDeadline.FieldName = "treeListColumn4";
            this.treeListColumnDeadline.Name = "treeListColumnDeadline";
            this.treeListColumnDeadline.Width = 120;
            // 
            // treeListColumnPlan2CloseTime
            // 
            this.treeListColumnPlan2CloseTime.Caption = "计划关闭时间";
            this.treeListColumnPlan2CloseTime.FieldName = "treeListColumn5";
            this.treeListColumnPlan2CloseTime.Name = "treeListColumnPlan2CloseTime";
            this.treeListColumnPlan2CloseTime.Width = 120;
            // 
            // treeListColumnDealPerson
            // 
            this.treeListColumnDealPerson.Caption = "处理人";
            this.treeListColumnDealPerson.FieldName = "treeListColumn6";
            this.treeListColumnDealPerson.Name = "treeListColumnDealPerson";
            // 
            // treeListColumnNearestBuilding
            // 
            this.treeListColumnNearestBuilding.Caption = "周边建筑";
            this.treeListColumnNearestBuilding.FieldName = "treeListColumn1";
            this.treeListColumnNearestBuilding.Name = "treeListColumnNearestBuilding";
            this.treeListColumnNearestBuilding.Visible = true;
            this.treeListColumnNearestBuilding.VisibleIndex = 28;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmItemProcessBlock,
            this.tsmItemViewDetail,
            this.tsmItemShowESResult,
            this.toolStripSeparator2,
            this.tsmItemExportExcel,
            this.tsmItemExportBBReport,
            this.tsmItemSelectedBBReport,
            this.tsmItemGeneralBBInfo,
            this.tsmItemShapefile,
            this.toolStripSeparator3,
            this.tsmItemDownload,
            this.tsmItemUpload,
            this.tsmItemDelete});
            this.contextMenuStrip.Name = "ctxMenu";
            this.contextMenuStrip.Size = new System.Drawing.Size(230, 258);
            this.contextMenuStrip.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip_Opening);
            // 
            // tsmItemProcessBlock
            // 
            this.tsmItemProcessBlock.Name = "tsmItemProcessBlock";
            this.tsmItemProcessBlock.Size = new System.Drawing.Size(229, 22);
            this.tsmItemProcessBlock.Text = "黑点处理信息...";
            this.tsmItemProcessBlock.Visible = false;
            this.tsmItemProcessBlock.Click += new System.EventHandler(this.tsmItemProcessBlock_Click);
            // 
            // tsmItemViewDetail
            // 
            this.tsmItemViewDetail.Name = "tsmItemViewDetail";
            this.tsmItemViewDetail.Size = new System.Drawing.Size(229, 22);
            this.tsmItemViewDetail.Text = "黑点信息详情...";
            this.tsmItemViewDetail.Click += new System.EventHandler(this.tsmItemViewDetail_Click);
            // 
            // tsmItemShowESResult
            // 
            this.tsmItemShowESResult.Name = "tsmItemShowESResult";
            this.tsmItemShowESResult.Size = new System.Drawing.Size(229, 22);
            this.tsmItemShowESResult.Text = "黑点智能预判结果";
            this.tsmItemShowESResult.Click += new System.EventHandler(this.tsmItemShowESResult_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(226, 6);
            // 
            // tsmItemExportExcel
            // 
            this.tsmItemExportExcel.Name = "tsmItemExportExcel";
            this.tsmItemExportExcel.Size = new System.Drawing.Size(229, 22);
            this.tsmItemExportExcel.Text = "导出Excel";
            this.tsmItemExportExcel.Click += new System.EventHandler(this.tsmItemExportExcel_Click);
            // 
            // tsmItemExportBBReport
            // 
            this.tsmItemExportBBReport.Name = "tsmItemExportBBReport";
            this.tsmItemExportBBReport.Size = new System.Drawing.Size(229, 22);
            this.tsmItemExportBBReport.Text = "导出全部黑点报告...";
            this.tsmItemExportBBReport.Click += new System.EventHandler(this.tsmItemExportBBReport_Click);
            // 
            // tsmItemSelectedBBReport
            // 
            this.tsmItemSelectedBBReport.Name = "tsmItemSelectedBBReport";
            this.tsmItemSelectedBBReport.Size = new System.Drawing.Size(229, 22);
            this.tsmItemSelectedBBReport.Text = "导出选中黑点报告...";
            this.tsmItemSelectedBBReport.Click += new System.EventHandler(this.tsmItemSelectedBBReport_Click);
            // 
            // tsmItemGeneralBBInfo
            // 
            this.tsmItemGeneralBBInfo.Name = "tsmItemGeneralBBInfo";
            this.tsmItemGeneralBBInfo.Size = new System.Drawing.Size(229, 22);
            this.tsmItemGeneralBBInfo.Text = "导出黑点概要信息…";
            this.tsmItemGeneralBBInfo.Click += new System.EventHandler(this.tsmItemGeneralBBInfo_Click);
            // 
            // tsmItemShapefile
            // 
            this.tsmItemShapefile.Name = "tsmItemShapefile";
            this.tsmItemShapefile.Size = new System.Drawing.Size(229, 22);
            this.tsmItemShapefile.Text = "导出黑点图层";
            this.tsmItemShapefile.Click += new System.EventHandler(this.tsmItemShapefile_Click);
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(226, 6);
            this.toolStripSeparator3.Visible = false;
            // 
            // tsmItemDownload
            // 
            this.tsmItemDownload.Enabled = false;
            this.tsmItemDownload.Image = global::MasterCom.RAMS.Properties.Resources.download;
            this.tsmItemDownload.Name = "tsmItemDownload";
            this.tsmItemDownload.Size = new System.Drawing.Size(229, 22);
            this.tsmItemDownload.Text = "下载解决方案附件到本地...";
            this.tsmItemDownload.Visible = false;
            this.tsmItemDownload.Click += new System.EventHandler(this.tsmItemDownload_Click);
            // 
            // tsmItemUpload
            // 
            this.tsmItemUpload.Image = global::MasterCom.RAMS.Properties.Resources.upload;
            this.tsmItemUpload.Name = "tsmItemUpload";
            this.tsmItemUpload.Size = new System.Drawing.Size(229, 22);
            this.tsmItemUpload.Text = "上传解决方案文件到服务端...";
            this.tsmItemUpload.Visible = false;
            this.tsmItemUpload.Click += new System.EventHandler(this.tsmItemUpload_Click);
            // 
            // tsmItemDelete
            // 
            this.tsmItemDelete.Enabled = false;
            this.tsmItemDelete.Name = "tsmItemDelete";
            this.tsmItemDelete.Size = new System.Drawing.Size(229, 22);
            this.tsmItemDelete.Text = "删除解决方案附件...";
            this.tsmItemDelete.Visible = false;
            this.tsmItemDelete.Click += new System.EventHandler(this.tsmItemDelete_Click);
            // 
            // BlackBlockForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1096, 481);
            this.Controls.Add(this.splitContainerControl1);
            this.Controls.Add(this.pnlInfo);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "BlackBlockForm";
            this.Text = "问题黑点列表";
            this.pnlInfo.ResumeLayout(false);
            this.pnlInfo.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).EndInit();
            this.groupControl4.ResumeLayout(false);
            this.groupControl4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorClose.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeList1)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion
        private System.Windows.Forms.Panel pnlInfo;
        private System.Windows.Forms.TextBox txtInfo;
        private System.Windows.Forms.Label lblColor;
        private System.Windows.Forms.Label lblInstructionCreate;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label lblInstructionClose;

        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label lblInstructionCreateAndClose;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.GroupControl groupControl4;
        private System.Windows.Forms.RadioButton radioByCnt;
        private System.Windows.Forms.RadioButton radioUnify;
        private System.Windows.Forms.CheckBox chbShowClose;
        private DevExpress.XtraEditors.ColorEdit colorClose;
        private System.Windows.Forms.RadioButton radioByTime;
        private System.Windows.Forms.CheckBox chkBbIDVisible;
        private DevExpress.XtraTreeList.TreeList treeList1;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnSN;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnCity;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnID;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnBranch;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnGroup;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnGrid;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnName;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnGridDesc;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnAreaName;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnRoadName;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnHandleUser;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnVIP;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnStaticWeight;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnProcPer;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnAbDays;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnNormalDays;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnAbEvents;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnAbEventDate;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnAbEventLon;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnAbEventLat;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnAbEventName;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnStatus;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnStatusByFiles;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnCenterLong;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnCenterLat;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnPlaceDes;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnBelongGrid;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnReason;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnAttachFileDesc;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnCreateDate;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnCloseDate;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnFirstAbDate;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnLastAbDate;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnLastTestDate;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnAbEventDes;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnNoStat;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnGoodDays;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnValidateDay;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnValidStatus;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnLastEventDesc;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnCellNames;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnNetType;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnCauseMain;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnCauseSub;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnCauseDetail;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnProblem;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnSolution;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnDealTime;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnDeadline;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnPlan2CloseTime;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnDealPerson;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnNearestBuilding;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem tsmItemProcessBlock;
        private System.Windows.Forms.ToolStripMenuItem tsmItemViewDetail;
        private System.Windows.Forms.ToolStripMenuItem tsmItemShowESResult;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem tsmItemExportExcel;
        private System.Windows.Forms.ToolStripMenuItem tsmItemExportBBReport;
        private System.Windows.Forms.ToolStripMenuItem tsmItemSelectedBBReport;
        private System.Windows.Forms.ToolStripMenuItem tsmItemGeneralBBInfo;
        private System.Windows.Forms.ToolStripMenuItem tsmItemShapefile;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private System.Windows.Forms.ToolStripMenuItem tsmItemDownload;
        private System.Windows.Forms.ToolStripMenuItem tsmItemUpload;
        private System.Windows.Forms.ToolStripMenuItem tsmItemDelete;
    }
}