﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class FrequencyShortageDlg_GSM
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.numRxLevDValue = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.numSecondLast = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.numRxLevDValueOther = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.numFreqCountRateThreshold = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSecondLast.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValueOther.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFreqCountRateThreshold.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.numRxLevDValue);
            this.groupControl1.Controls.Add(this.labelControl4);
            this.groupControl1.Controls.Add(this.numSecondLast);
            this.groupControl1.Controls.Add(this.labelControl5);
            this.groupControl1.Controls.Add(this.labelControl3);
            this.groupControl1.Controls.Add(this.labelControl2);
            this.groupControl1.Location = new System.Drawing.Point(44, 24);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(231, 79);
            this.groupControl1.TabIndex = 2;
            this.groupControl1.Text = "对象小区条件";
            // 
            // numRxLevDValue
            // 
            this.numRxLevDValue.EditValue = new decimal(new int[] {
            8,
            0,
            0,
            0});
            this.numRxLevDValue.Location = new System.Drawing.Point(98, 26);
            this.numRxLevDValue.Name = "numRxLevDValue";
            this.numRxLevDValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevDValue.Properties.IsFloatValue = false;
            this.numRxLevDValue.Properties.Mask.EditMask = "N00";
            this.numRxLevDValue.Size = new System.Drawing.Size(83, 21);
            this.numRxLevDValue.TabIndex = 0;
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(197, 56);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(12, 14);
            this.labelControl4.TabIndex = 6;
            this.labelControl4.Text = "秒";
            // 
            // numSecondLast
            // 
            this.numSecondLast.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numSecondLast.Location = new System.Drawing.Point(98, 53);
            this.numSecondLast.Name = "numSecondLast";
            this.numSecondLast.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSecondLast.Properties.IsFloatValue = false;
            this.numSecondLast.Properties.Mask.EditMask = "N00";
            this.numSecondLast.Size = new System.Drawing.Size(83, 21);
            this.numSecondLast.TabIndex = 1;
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(32, 56);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(60, 14);
            this.labelControl5.TabIndex = 4;
            this.labelControl5.Text = "持续时间：";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(197, 29);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(14, 14);
            this.labelControl3.TabIndex = 3;
            this.labelControl3.Text = "db";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(20, 29);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(72, 14);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "相对主服差：";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.labelControl6);
            this.groupControl2.Controls.Add(this.numRxLevDValueOther);
            this.groupControl2.Controls.Add(this.labelControl7);
            this.groupControl2.Controls.Add(this.labelControl8);
            this.groupControl2.Controls.Add(this.numFreqCountRateThreshold);
            this.groupControl2.Controls.Add(this.labelControl9);
            this.groupControl2.Location = new System.Drawing.Point(44, 109);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(231, 80);
            this.groupControl2.TabIndex = 3;
            this.groupControl2.Text = "频率资源不足判定门限";
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(197, 29);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(14, 14);
            this.labelControl6.TabIndex = 12;
            this.labelControl6.Text = "db";
            // 
            // numRxLevDValueOther
            // 
            this.numRxLevDValueOther.EditValue = new decimal(new int[] {
            12,
            0,
            0,
            0});
            this.numRxLevDValueOther.Location = new System.Drawing.Point(98, 26);
            this.numRxLevDValueOther.Name = "numRxLevDValueOther";
            this.numRxLevDValueOther.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevDValueOther.Properties.IsFloatValue = false;
            this.numRxLevDValueOther.Properties.Mask.EditMask = "N00";
            this.numRxLevDValueOther.Size = new System.Drawing.Size(83, 21);
            this.numRxLevDValueOther.TabIndex = 0;
            // 
            // labelControl7
            // 
            this.labelControl7.Location = new System.Drawing.Point(8, 29);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(81, 14);
            this.labelControl7.TabIndex = 10;
            this.labelControl7.Text = "与对象小区差≤";
            // 
            // labelControl8
            // 
            this.labelControl8.Location = new System.Drawing.Point(197, 57);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(12, 14);
            this.labelControl8.TabIndex = 9;
            this.labelControl8.Text = "%";
            // 
            // numFreqCountRateThreshold
            // 
            this.numFreqCountRateThreshold.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numFreqCountRateThreshold.Location = new System.Drawing.Point(98, 54);
            this.numFreqCountRateThreshold.Name = "numFreqCountRateThreshold";
            this.numFreqCountRateThreshold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFreqCountRateThreshold.Properties.IsFloatValue = false;
            this.numFreqCountRateThreshold.Properties.Mask.EditMask = "N00";
            this.numFreqCountRateThreshold.Size = new System.Drawing.Size(83, 21);
            this.numFreqCountRateThreshold.TabIndex = 1;
            // 
            // labelControl9
            // 
            this.labelControl9.Location = new System.Drawing.Point(32, 57);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(57, 14);
            this.labelControl9.TabIndex = 7;
            this.labelControl9.Text = "频点占比≥";
            // 
            // btnOK
            // 
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(64, 206);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(180, 206);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            // 
            // FrequencyShortageDlg_GSM
            // 
            this.AcceptButton = this.btnOK;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(319, 248);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.groupControl1);
            this.Name = "FrequencyShortageDlg_GSM";
            this.Text = "频率资源不足条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSecondLast.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValueOther.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFreqCountRateThreshold.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SpinEdit numSecondLast;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.SpinEdit numRxLevDValueOther;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.SpinEdit numFreqCountRateThreshold;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.SpinEdit numRxLevDValue;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;

    }
}