﻿using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTFocusAnalysisResultBase : DIYSQLBase
    {
        //模板表前缀
        protected const string modelTableprefix = "tb_focus_event_result_";

        /// <summary>
        /// 结果集
        /// </summary>
        protected List<FocusResultInfo> eventResultInfoList = new List<FocusResultInfo>();
        /// <summary>
        /// 当前测试文件信息
        /// </summary>
        protected FileInfo curFile;
        /// <summary>
        /// 序号
        /// </summary>
        protected int sn = 1;

        public ZTFocusAnalysisResultBase(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override string getSqlTextString()
        {
            return "";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            return new E_VType[0];
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
        }

        protected override void query()
        {
            //
        }

        protected virtual bool isValidEvent(FocusResultInfo eventInfo)
        {
            return true;
        }

        public List<FocusResultInfo> GetEventResultInfos()
        {
            return eventResultInfoList;
        }
    }

    public class ZTFocusAnalysisResultByFile : ZTFocusAnalysisResultBase
    {
        public ZTFocusAnalysisResultByFile(MainModel mainModel)
            : base(mainModel)
        {
        }

        #region 基础数据重写
        public override string Name
        {
            get { return "问题点自动分析(按文件)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20053, this.Name);
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                mainModel.FileInfos = Condition.FileInfos;
                return true;
            }
            return false;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        #endregion

        #region 查询流程

        protected override void query()
        {
            //根据选择的文件,查询已录入文件对应的栅格表
            if (mainModel.FileInfos.Count <= 0)
            {
                return;
            }

            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                WaitBox.Show("正在进行问题点自动分析...", queryInThread, clientProxy);

                ChooseEvent();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                eventResultInfoList = new List<FocusResultInfo>();
                foreach (var file in mainModel.FileInfos)
                {
                    curFile = file;
                    Package package = clientProxy.Package;
                    string strsql = getSqlTextString();

                    E_VType[] retArrDef = getSqlRetTypeArr();//获得枚举类型数组
                    package.Command = Command.DIYSearch;//枚举类型：DIY接口
                    package.SubCommand = SubCommand.Request;//枚举类型：请求
                    if (MainDB)
                    {
                        package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                    }
                    else
                    {
                        package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                    }
                    package.Content.PrepareAddParam();
                    package.Content.AddParam(strsql);
                    StringBuilder sb = new StringBuilder();
                    if (retArrDef != null)
                    {
                        for (int i = 0; i < retArrDef.Length; i++)
                        {
                            sb.Append((int)retArrDef[i]);
                            sb.Append(",");
                        }
                    }

                    package.Content.AddParam(sb.ToString().TrimEnd(','));
                    clientProxy.Send();
                    receiveRetData(clientProxy);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder strbSql = new StringBuilder();
            string tableName = modelTableprefix + curFile.LogTable.Substring(14, 2) + curFile.LogTable.Substring(17, 2);
            strbSql.AppendFormat(@"select a.evtid,a.evttime,a.midlongitude,a.midlatitude,a.primarytype,a.specifictype,a.detail,a.suggest,a.extend,b.strEventName from {0} a left join tb_cfg_static_event b on (a.evtid = b.iEventID and b.strEventType = '') where fileid = {1} ",
              tableName, curFile.ID);

            string sql = strbSql.ToString();
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[10];          
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_Float;
            rType[3] = E_VType.E_Float;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_String;
            rType[7] = E_VType.E_String;
            rType[8] = E_VType.E_VARYBIN;
            rType[9] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        private void fillData(Package package)
        {
            FocusResultInfo info = FocusResultInfo.Fill(package.Content);
            info.FillOtherEventResultInfo(sn, curFile.Name, curFile.ServiceType);
            if (isValidEvent(info))
            {
                eventResultInfoList.Add(info);
                sn++;
            }
        }
        #endregion

        protected virtual void ChooseEvent()
        {
            List<int> eventResultIdList = new List<int>();
            foreach (var evtResultInfo in eventResultInfoList)
            {
                eventResultIdList.Add(evtResultInfo.Evtid);
            }

            EventChooserSimpleForm eventChooser = EventChooserSimpleForm.GetInstance(MainModel);
            eventChooser.SetEventList(eventResultIdList);

            if (eventChooser.ShowDialog() == DialogResult.OK)
            {
                //Condition.FilterOffValue9 = eventChooser.FilterOffValue9;
                if (eventChooser.SelectedEventIDs.Count > 0)
                {
                    Condition.EventIDs = eventChooser.SelectedEventIDs;

                    List<FocusResultInfo> evtResultInfoList = new List<FocusResultInfo>();
                    foreach (var evtResult in eventResultInfoList)
                    {
                        if (eventChooser.SelectedEventIDs.Contains(evtResult.Evtid))
                        {
                            evtResultInfoList.Add(evtResult);
                        }
                    }
                    eventResultInfoList = evtResultInfoList;

                    fireShowForm();
                }
            }
        }

        /// <summary>
        /// 显示结果窗体
        /// </summary>
        protected virtual void fireShowForm()
        {
            ZTFocusAnalysisForm frm = MainModel.GetInstance().CreateResultForm(typeof(ZTFocusAnalysisForm)) as ZTFocusAnalysisForm;
            frm.FillData(eventResultInfoList);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class ZTFocusAnalysisByRegion : ZTFocusAnalysisResultByFile
    {
        public ZTFocusAnalysisByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }

        /// <summary>
        /// 地图框选的区域
        /// </summary>
        protected DbRect selectRect;

        #region 基础数据重写
        public override string Name
        {
            get { return "问题点自动分析(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20054, this.Name);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
        #endregion

        protected override void query()
        {
            selectRect = condition.Geometorys.RegionBounds;
            if (selectRect == null)
            {
                return;
            }

            //查询区域内文件
            DIYQueryFileInfoByRegion queryFileByReg = new DIYQueryFileInfoByRegion(MainModel);
            queryFileByReg.IsShowFileInfoForm = false;
            queryFileByReg.SetQueryCondition(condition);
            queryFileByReg.Query();

            //循环所有已录入文件,查询栅格信息并汇聚
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("正在进行问题点自动分析...", queryInThread, clientProxy);
                fireShowForm();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override bool isValidEvent(FocusResultInfo eventInfo)
        {
            //过滤不在框选区域内的栅格
            if (eventInfo.Within(selectRect))
            {
                return true;
            }
            return false;
        }
    }

    public class FocusResultInfo
    {
        private int sn;
        public int SN
        {
            get { return sn; }
        }
        
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        private string evtName;
        /// <summary>
        /// 事件名
        /// </summary>
        public string EvtName
        {
            get 
            { 
                return evtName;
            }
        }
        
        /// <summary>
        /// 小区名(根据extend解析获得)
        /// </summary>
        public string CellName { get; set; }

        private string netType;
        public string NetType
        {
            get
            {
                return netType;
            }
        }

        private int evtid;
        /// <summary>
        /// 事件ID
        /// </summary>
        public int Evtid
        {
            get { return evtid; }
        }
        private string evttime;
        /// <summary>
        /// 事件时间
        /// </summary>
        public string Evttime
        {
            get { return evttime; }
        }
        private float midlongitude;
        /// <summary>
        /// 事件经度
        /// </summary>
        public float Midlongitude
        {
            get { return midlongitude; }
        }
        private float midlatitude;
        /// <summary>
        /// 事件纬度
        /// </summary>
        public float Midlatitude
        {
            get { return midlatitude; }
        }
        private string primarytype;
        /// <summary>
        /// 原因分类
        /// </summary>
        public string Primarytype
        {
            get { return primarytype; }
        }
        private string specifictype;
        /// <summary>
        /// 原因子类
        /// </summary>
        public string Specifictype
        {
            get { return specifictype; }
        }
        private string detail;
        /// <summary>
        /// 问题分析
        /// </summary>
        public string Detail
        {
            get { return detail; }
        }
        private string suggest;
        /// <summary>
        /// 解决方案
        /// </summary>
        public string Suggest
        {
            get { return suggest; }
        }
        private byte[] extend;
        /// <summary>
        /// 扩展信息
        /// </summary>
        public byte[] Extend
        {
            get { return extend; }
        }

        private Dictionary<uint, object> extendParamDic = new Dictionary<uint, object>();

        public static FocusResultInfo Fill(Content content)
        {
            FocusResultInfo info = new FocusResultInfo();
            info.evtid = content.GetParamInt();
            info.evttime = content.GetParamString();
            info.midlongitude = content.GetParamFloat();
            info.midlatitude = content.GetParamFloat();
            info.primarytype = content.GetParamString();
            info.specifictype = content.GetParamString();
            info.detail = content.GetParamString();
            info.suggest = content.GetParamString();
            info.extend = content.GetParamBytes();
            info.evtName = content.GetParamString();

            if (info.Extend != null)
            {
                info.extendParamDic = MasterCom.RAMS.NewBlackBlock.KeyValueImageParserV1.FromImage(info.extend);
            }

            return info;
        }

        public void FillOtherEventResultInfo(int sn, string fileName, int serviceType)
        {
            this.sn = sn;
            this.CellName = GetExtendValue(FocusResultInfo.ExtendParamID.CellNames) as string;
            this.FileName = fileName;
            this.netType = getNetType(serviceType);
        }

        private string getNetType(int serviceType)
        {
            ServiceName serviceName = ServiceTypeManager.getServiceNameFromTypeID(serviceType);
            switch (serviceName)
            {
                case ServiceName.NR:
                case ServiceName.NRVoice:
                    return "NR";
                case ServiceName.VoLTE:
                case ServiceName.LTE:
                    return "LTE";
                case ServiceName.GSM:
                    return "GSM";
                default:
                    return "";
            }
        }

        public object GetExtendValue(ExtendParamID paramID)
        {
            if (extendParamDic.ContainsKey((uint)paramID))
            {
                return extendParamDic[(uint)paramID];
            }
            return null;
        }

        public bool Within(DbRect rect)
        {
            if (midlongitude < rect.x1 || midlongitude > rect.x2 || midlatitude < rect.y1 || midlatitude > rect.y2)
            {
                return false;
            }
            return true;
        }

        public enum ExtendParamID
        {
            Distance = 1,
            TAC_CI = 2,
            CellNames = 3,

            测试数据 = 4,
            告警数据 = 5,
            性能数据 = 6,
            参数数据 = 7,
            MR数据 = 8,
            扫频数据 = 9,

            告警数据列表 = 10,
            性能数据列表 = 11,
            参数数据列表 = 12,

            平均速率 = 13,
            平均RSRP = 14,
            平均SINR = 15,
            采样点总数 = 16,
            问题点个数 = 17,
            平均mos = 18,
            enb_ci = 19,

            AreaName = 20,
            Top1Cell_TAC,
            Top1Cell_EARFCN,
            Top1Cell_CGI,
            Top1Cell_PCI,
            Top1Cell_RsrpAvg = 25,
            Top1Cell_SinrAvg,

            CoverBestNCell_CGI = 27,
            CoverBestNCell_EARFCN,
            CoverBestNCell_PCI,
            CoverBestNCell_RsrpAvg = 30,
            Duration,
            RsrpMax,
            RsrpMin,
            SinrMax,
            SinrMin = 35,

            CoverBestNCell_RsrpMax,
            CoverBestNCell_RsrpMin
        }
    }
}
