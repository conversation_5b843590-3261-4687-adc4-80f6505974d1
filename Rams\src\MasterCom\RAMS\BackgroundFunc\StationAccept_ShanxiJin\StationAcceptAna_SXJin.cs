﻿using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Stat;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class StationAcceptAna_SXJin : SingleStationAcceptBase
    {
        ReporterTemplate selectRpt = new ReporterTemplate();
        readonly List<CellAcceptInfo_SXJin> sumCellAcceptInfoList = new List<CellAcceptInfo_SXJin>();
        StationAcceptInfo_SXJin curBtsAcceptInfo { get; set; }
        CellAcceptInfo_SXJin curCellInfo { get; set; }

        private static StationAcceptAna_SXJin instance = null;
        public static StationAcceptAna_SXJin GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new StationAcceptAna_SXJin();
                    }
                }
            }
            return instance;
        }
        protected StationAcceptAna_SXJin()
            : base()
        {
        }

        public override string Name
        {
            get
            {
                return "山西单站验收";
            }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 22000, 22104, "查询");
        }

        public override void DealBeforeBackgroundQueryByCity()
        {
            strWorkParamTime = "";
            if (!getWorkParams(FuncSet.IsAutoWorkParamFilePath, FuncSet.ExcelPath))
            {
                return;
            }
            if (workParamSumDic.Count <= 0)
            {
                folderPath = FuncSet.FilePath;
            }
            folderPath = FuncSet.FilePath + "\\" + strWorkParamTime;

            sumCellAcceptInfoList.Clear();
            sumRows.Clear();
            NPOIRow row = new NPOIRow();
            selectRpt = ReporterTemplateManager.LoadSingleReportFromFile(StationAcceptProperties_LTE.StrRptTitleHead);
            if (selectRpt == null)
            {
                selectRpt = new ReporterTemplate();
            }
            sumRows.Add(row);
            row.AddCellValue("Id");
            row.AddCellValue("地市");
            row.AddCellValue("基站名称");
            row.AddCellValue("ENodeBID");
            row.AddCellValue("经度");
            row.AddCellValue("纬度");
            row.AddCellValue("地址");
            row.AddCellValue("小区名称");
            row.AddCellValue("TAC");
            row.AddCellValue("CellID");
            row.AddCellValue("ARFCN");
            row.AddCellValue("PCI");
            row.AddCellValue("是否找到文件");
            //标题
            for (int i = 0; i < selectRpt.Columns.Count; i++)
            {
                ColumnSet column = selectRpt.Columns[i];
                row.AddCellValue(column.Title);
            }
        }
        protected override bool getCondition()
        {
            curDistrictName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);

            if (workParamSumDic.Count <= 0 || !workParamSumDic.ContainsKey(curDistrictName.Replace("市", "")))
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo("未读取到" + curDistrictName + "的待验收小区工参");
                return false;
            }

            condition = new QueryCondition();
            DateTime startTime = BackgroundFuncConfigManager.GetInstance().StartTime;
            DateTime endTime = BackgroundFuncConfigManager.GetInstance().EndTime;
            condition.Periods.Add(new TimePeriod(startTime, endTime));
            condition.Projects = BackgroundFuncConfigManager.GetInstance().ProjectTypeList;
            return true;
        }
        protected override void queryInThread(ClientProxy clientProxy, Dictionary<int, Dictionary<int, CellWorkParam>> curDistrictWorkParamDic)
        {
            foreach (var btsValuePair in curDistrictWorkParamDic)
            {
                Dictionary<int, CellWorkParam> cellParamDic = btsValuePair.Value;
                curBtsAcceptInfo = new StationAcceptInfo_SXJin();

                if (cellParamDic != null)
                {
                    SingleCellDiyKPIQuery curQuery = null;
                    foreach (var cellValuePair in btsValuePair.Value)
                    {
                        if (MainModel.BackgroundStopRequest)
                        {
                            return;
                        }

                        CellWorkParam cellParam = cellValuePair.Value;
                        curCellInfo = new CellAcceptInfo_SXJin(cellParam, FuncSet.FileNameEnodType);
                        curBtsAcceptInfo.CellAcceptInfoDic[cellParam.CellId] = curCellInfo;

                        BackgroundFuncManager.GetInstance().ReportBackgroundInfo("开始分析" + curCellInfo.CellParamInfo.CellName + "的文件");

                        string StrENodeBID = curCellInfo.StrENodeBID;
                        string strFilter = string.Format("{0}_环测 or {1}_UL or {2}_DL or {3}_CSFB or {4}_VOLTE or {5}_{6}"
                            , StrENodeBID, StrENodeBID, StrENodeBID, StrENodeBID, StrENodeBID, StrENodeBID, curCellInfo.CellParamInfo.CellId);

                        condition.NameFilterType = FileFilterType.ByFileName;
                        int orNum = 1;
                        condition.FileName = QueryCondition.MakeFileFilterString(strFilter, ref orNum).Replace("[_]", "_");
                        condition.FileNameOrNum = orNum;

                        curQuery = new SingleCellDiyKPIQuery(selectRpt, curCellInfo.StrENodeBID, curCellInfo.CellParamInfo.ENodeBID
                            , curCellInfo.CellParamInfo.CellId, curCellInfo.CellParamInfo.Tac);
                        curQuery.SetQueryCondition(condition);
                        curQuery.Query();

                        List<SingleCellStatInfo> infoList = new List<SingleCellStatInfo>(curQuery.CellFileKeyDataDic.Values);
                        if (infoList.Count > 0)
                        {
                            SingleCellStatInfo statInfoMerge = getSingleCellStatInfo(infoList);
                            curCellInfo.HasFoundFile = true;
                            curCellInfo.StatInfo = statInfoMerge;
                        }
                        sumCellAcceptInfoList.Add(curCellInfo);
                    }
                }
            }
        }

        private SingleCellStatInfo getSingleCellStatInfo(List<SingleCellStatInfo> infoList)
        {
            SingleCellStatInfo statInfoMerge = new SingleCellStatInfo();
            if (infoList.Count > 1)
            {
                infoList.Sort();
                int recentSeconds = FuncSet.DiyStatsRecentDays * 86400;
                foreach (SingleCellStatInfo item in infoList)
                {
                    if (item.FileHeader != null && infoList[0].FileHeader != null
                        && infoList[0].FileHeader.BeginTime - item.FileHeader.BeginTime <= recentSeconds)
                    {
                        statInfoMerge.KPIData.Merge(item.KPIData);
                    }
                }
            }
            else
            {
                statInfoMerge = infoList[0];
            }
            if (statInfoMerge.KPIData != null)
            {
                statInfoMerge.KPIData.FinalMtMoGroup();
            }

            return statInfoMerge;
        }

        protected override void doSomethingAfterQueryThread()
        {
            curBtsAcceptInfo = null;
            curCellInfo = null;
        }
        public override void DealAfterBackgroundQueryByCity()
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo("开始导出单站验收报表...");
            SingleCellDiyKPIQuery curQuery = new SingleCellDiyKPIQuery(selectRpt);
            sumRows.AddRange(curQuery.CreateReport(selectRpt, sumCellAcceptInfoList));
            workParamSumDic.Clear();
            sumCellAcceptInfoList.Clear();
            CSVWriter.ExportToCsv(sumRows, folderPath + "单站验收统计报表.csv");

            sumRows.Clear();
            selectRpt = null;
        }
    }
}
