﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;

namespace MasterCom.RAMS.ZTFunc
{
    public class ShowAreaListForm : QueryBase
    {
        public ShowAreaListForm()
            : base(MainModel.GetInstance())
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "查看行政区域信息"; }
        }

        protected override bool isValidCondition()
        {
            ZTAreaManager instance = ZTAreaManager.Instance;
#if DEBUG
            Console.Write(instance.ToString());
#endif
            return true;
        }


        protected override void query()
        {
            AreaListForm form = MainModel.GetObjectFromBlackboard(typeof(AreaListForm)) as AreaListForm;
            if (form == null || form.IsDisposed)
            {
                form = new AreaListForm();
                form.FillData();
            }
            form.Visible = true;
            form.Owner = MainModel.MainForm;
            form.BringToFront();
        }

    }
}
