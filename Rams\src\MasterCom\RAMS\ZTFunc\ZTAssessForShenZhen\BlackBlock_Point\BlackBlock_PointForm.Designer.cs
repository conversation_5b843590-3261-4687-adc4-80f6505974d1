﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class BlackBlock_PointForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miDetail = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnBlockID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnWeight = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnStatus = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCreatedDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLastAbnormalDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGoodDaysCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnATUValidateCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLastValidateDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnValidateStatus = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLongitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLatitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAreaNames = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDWAreaNames = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGridNames = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRoadNames = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellNames = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnClosedDate = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.contextMenuStrip;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(811, 437);
            this.gridControl.TabIndex = 2;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            this.gridControl.DoubleClick += new System.EventHandler(this.gridControl_DoubleClick);
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miDetail,
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(158, 48);
            // 
            // miDetail
            // 
            this.miDetail.Name = "miDetail";
            this.miDetail.Size = new System.Drawing.Size(157, 22);
            this.miDetail.Text = "查看详细情况...";
            this.miDetail.Click += new System.EventHandler(this.miDetail_Click);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(157, 22);
            this.miExportToExcel.Text = "导出Excel...";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnBlockID,
            this.gridColumnName,
            this.gridColumnWeight,
            this.gridColumnStatus,
            this.gridColumnCreatedDate,
            this.gridColumnLastAbnormalDate,
            this.gridColumnClosedDate,
            this.gridColumnGoodDaysCount,
            this.gridColumnATUValidateCount,
            this.gridColumnLastValidateDate,
            this.gridColumnValidateStatus,
            this.gridColumnLAC,
            this.gridColumnCI,
            this.gridColumnLongitude,
            this.gridColumnLatitude,
            this.gridColumnAreaNames,
            this.gridColumnDWAreaNames,
            this.gridColumnGridNames,
            this.gridColumnRoadNames,
            this.gridColumnCellNames});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnBlockID
            // 
            this.gridColumnBlockID.Caption = "问题点编号";
            this.gridColumnBlockID.FieldName = "ID";
            this.gridColumnBlockID.Name = "gridColumnBlockID";
            this.gridColumnBlockID.Visible = true;
            this.gridColumnBlockID.VisibleIndex = 0;
            // 
            // gridColumnName
            // 
            this.gridColumnName.Caption = "名称";
            this.gridColumnName.FieldName = "Name";
            this.gridColumnName.Name = "gridColumnName";
            this.gridColumnName.Visible = true;
            this.gridColumnName.VisibleIndex = 1;
            this.gridColumnName.Width = 91;
            // 
            // gridColumnWeight
            // 
            this.gridColumnWeight.Caption = "权重";
            this.gridColumnWeight.FieldName = "Weight";
            this.gridColumnWeight.Name = "gridColumnWeight";
            this.gridColumnWeight.Visible = true;
            this.gridColumnWeight.VisibleIndex = 2;
            this.gridColumnWeight.Width = 48;
            // 
            // gridColumnStatus
            // 
            this.gridColumnStatus.Caption = "状态";
            this.gridColumnStatus.FieldName = "StatusString";
            this.gridColumnStatus.Name = "gridColumnStatus";
            this.gridColumnStatus.Visible = true;
            this.gridColumnStatus.VisibleIndex = 3;
            this.gridColumnStatus.Width = 58;
            // 
            // gridColumnCreatedDate
            // 
            this.gridColumnCreatedDate.Caption = "创建日期";
            this.gridColumnCreatedDate.FieldName = "CreatedDateString";
            this.gridColumnCreatedDate.Name = "gridColumnCreatedDate";
            this.gridColumnCreatedDate.Visible = true;
            this.gridColumnCreatedDate.VisibleIndex = 4;
            this.gridColumnCreatedDate.Width = 82;
            // 
            // gridColumnLastAbnormalDate
            // 
            this.gridColumnLastAbnormalDate.Caption = "最后异常日期";
            this.gridColumnLastAbnormalDate.FieldName = "LastAbnormalDateString";
            this.gridColumnLastAbnormalDate.Name = "gridColumnLastAbnormalDate";
            this.gridColumnLastAbnormalDate.Visible = true;
            this.gridColumnLastAbnormalDate.VisibleIndex = 5;
            this.gridColumnLastAbnormalDate.Width = 90;
            // 
            // gridColumnGoodDaysCount
            // 
            this.gridColumnGoodDaysCount.Caption = "验证正常次数";
            this.gridColumnGoodDaysCount.FieldName = "GoodDaysCount";
            this.gridColumnGoodDaysCount.Name = "gridColumnGoodDaysCount";
            this.gridColumnGoodDaysCount.Visible = true;
            this.gridColumnGoodDaysCount.VisibleIndex = 6;
            this.gridColumnGoodDaysCount.Width = 92;
            // 
            // gridColumnATUValidateCount
            // 
            this.gridColumnATUValidateCount.Caption = "ATU验证次数";
            this.gridColumnATUValidateCount.FieldName = "ATUValidateCount";
            this.gridColumnATUValidateCount.Name = "gridColumnATUValidateCount";
            this.gridColumnATUValidateCount.Visible = true;
            this.gridColumnATUValidateCount.VisibleIndex = 7;
            this.gridColumnATUValidateCount.Width = 91;
            // 
            // gridColumnLastValidateDate
            // 
            this.gridColumnLastValidateDate.Caption = "最后验证日期";
            this.gridColumnLastValidateDate.FieldName = "LastValidateDateString";
            this.gridColumnLastValidateDate.Name = "gridColumnLastValidateDate";
            this.gridColumnLastValidateDate.Visible = true;
            this.gridColumnLastValidateDate.VisibleIndex = 8;
            this.gridColumnLastValidateDate.Width = 93;
            // 
            // gridColumnValidateStatus
            // 
            this.gridColumnValidateStatus.Caption = "验证状态";
            this.gridColumnValidateStatus.FieldName = "ValidateStatusString";
            this.gridColumnValidateStatus.Name = "gridColumnValidateStatus";
            this.gridColumnValidateStatus.Visible = true;
            this.gridColumnValidateStatus.VisibleIndex = 9;
            this.gridColumnValidateStatus.Width = 82;
            // 
            // gridColumnLAC
            // 
            this.gridColumnLAC.Caption = "LAC";
            this.gridColumnLAC.FieldName = "LAC";
            this.gridColumnLAC.Name = "gridColumnLAC";
            this.gridColumnLAC.Visible = true;
            this.gridColumnLAC.VisibleIndex = 10;
            this.gridColumnLAC.Width = 56;
            // 
            // gridColumnCI
            // 
            this.gridColumnCI.Caption = "CI";
            this.gridColumnCI.FieldName = "CI";
            this.gridColumnCI.Name = "gridColumnCI";
            this.gridColumnCI.Visible = true;
            this.gridColumnCI.VisibleIndex = 11;
            this.gridColumnCI.Width = 54;
            // 
            // gridColumnLongitude
            // 
            this.gridColumnLongitude.Caption = "经度";
            this.gridColumnLongitude.FieldName = "Longitude";
            this.gridColumnLongitude.Name = "gridColumnLongitude";
            this.gridColumnLongitude.Visible = true;
            this.gridColumnLongitude.VisibleIndex = 12;
            this.gridColumnLongitude.Width = 73;
            // 
            // gridColumnLatitude
            // 
            this.gridColumnLatitude.Caption = "纬度";
            this.gridColumnLatitude.FieldName = "Latitude";
            this.gridColumnLatitude.Name = "gridColumnLatitude";
            this.gridColumnLatitude.Visible = true;
            this.gridColumnLatitude.VisibleIndex = 13;
            // 
            // gridColumnAreaNames
            // 
            this.gridColumnAreaNames.Caption = "片区";
            this.gridColumnAreaNames.FieldName = "AreaNames";
            this.gridColumnAreaNames.Name = "gridColumnAreaNames";
            this.gridColumnAreaNames.Visible = true;
            this.gridColumnAreaNames.VisibleIndex = 14;
            // 
            // gridColumnDWAreaNames
            // 
            this.gridColumnDWAreaNames.Caption = "代维片区";
            this.gridColumnDWAreaNames.FieldName = "DWAreaNames";
            this.gridColumnDWAreaNames.Name = "gridColumnDWAreaNames";
            this.gridColumnDWAreaNames.Visible = true;
            this.gridColumnDWAreaNames.VisibleIndex = 15;
            // 
            // gridColumnGridNames
            // 
            this.gridColumnGridNames.Caption = "网格";
            this.gridColumnGridNames.FieldName = "GridNames";
            this.gridColumnGridNames.Name = "gridColumnGridNames";
            this.gridColumnGridNames.Visible = true;
            this.gridColumnGridNames.VisibleIndex = 16;
            // 
            // gridColumnRoadNames
            // 
            this.gridColumnRoadNames.Caption = "道路";
            this.gridColumnRoadNames.FieldName = "RoadNames";
            this.gridColumnRoadNames.Name = "gridColumnRoadNames";
            this.gridColumnRoadNames.Visible = true;
            this.gridColumnRoadNames.VisibleIndex = 17;
            // 
            // gridColumnCellNames
            // 
            this.gridColumnCellNames.Caption = "小区";
            this.gridColumnCellNames.FieldName = "CellNames";
            this.gridColumnCellNames.Name = "gridColumnCellNames";
            this.gridColumnCellNames.Visible = true;
            this.gridColumnCellNames.VisibleIndex = 18;
            // 
            // gridColumnClosedDate
            // 
            this.gridColumnClosedDate.Caption = "关闭日期";
            this.gridColumnClosedDate.FieldName = "ClosedDateString";
            this.gridColumnClosedDate.Name = "gridColumnClosedDate";
            this.gridColumnClosedDate.Visible = true;
            this.gridColumnClosedDate.VisibleIndex = 6;
            // 
            // BlackBlock_PointForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(811, 437);
            this.Controls.Add(this.gridControl);
            this.Name = "BlackBlock_PointForm";
            this.Text = "重复问题点";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBlockID;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnWeight;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnStatus;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCreatedDate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLastAbnormalDate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGoodDaysCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnATUValidateCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLastValidateDate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnValidateStatus;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLAC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLongitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLatitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAreaNames;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDWAreaNames;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGridNames;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRoadNames;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellNames;
        private System.Windows.Forms.ToolStripMenuItem miDetail;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnClosedDate;
    }
}