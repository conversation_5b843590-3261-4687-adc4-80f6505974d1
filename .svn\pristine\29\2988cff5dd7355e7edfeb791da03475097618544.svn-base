﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class VolteMosAndRtpItem
    {
        public VolteMosAndRtpItem(VolteMosTp mosTp)
        {
            RtpEvts = new List<RtpEvent>();

            this.MosTestpoint = mosTp;
        }

        public int SN { get; set; }
        public float MosValue { get { return MosTestpoint.MosValue; } }
        public double MosDuration { get { return MosTestpoint.MosDuration; } }
        public DateTime MosBeginTime { get { return MosTestpoint.BeginTime; } }
        public DateTime MosEndTime { get { return MosTestpoint.EndTime; } }
        public VolteMosTp MosTestpoint { get; set; }
        public string FileName { get { return MosTestpoint.Testpoint.FileName; } }

        public List<RtpEvent> RtpEvts { get; private set; }
        public int RtpEvtsCount { get { return RtpEvts.Count; } }
        public double RtpEvtDuration { get; private set; }
        public string RtpEvtDetail { get; set; }
        public void AddRtpEvt(RtpEvent evt)
        {
            RtpEvts.Add(evt);

            TimePeriod mosPeriod = MosTestpoint.MosPeriod;
            DateTime beginTime;
            DateTime endTime;
            if (evt.BeginTime >= mosPeriod.BeginTime)
            {
                beginTime = evt.BeginTime;
            }
            else
            {
                beginTime = mosPeriod.BeginTime;
            }

            if (evt.EndTime <= mosPeriod.EndTime)
            {
                endTime = evt.EndTime;
            }
            else
            {
                endTime = mosPeriod.EndTime;
            }

            double rtpDuration = (endTime - beginTime).TotalSeconds;
            this.RtpEvtDuration += rtpDuration;
        }
        public void GetSumInfo()
        {
            StringBuilder strb = new StringBuilder();
            foreach (RtpEvent evt in RtpEvts)
            {
                strb.AppendLine(string.Format("{0}({1}s)", evt.Evt.Name, evt.RtpEvtDuration));
            }
            RtpEvtDetail = strb.ToString();
        }
    }
    public class VolteMosAndRtpSum
    {
        public VolteMosAndRtpSum(float mosMin, float mosMax)
        {
            this.MosMax = mosMax;
            this.MosMin = mosMin;
            this.RtpEvts = new List<RtpEvent>();
        }
        public float MosMin { get; set; }
        public float MosMax { get; set; }
        public string MosValueDes 
        {
            get { return GetMosValueDes(MosMin, MosMax); }
        }
        public static string GetMosValueDes(float mosMin, float mosMax)
        {
            return string.Format("{0}~{1}", mosMin, mosMax); 
        }
        public double MosDuration { get; set; }
        public double RtpDuration { get; set; }
        public int RtpEvtCount { get { return RtpEvts.Count; } }
        public int MosTpCount { get; set; }
        public List<RtpEvent> RtpEvts { get; private set; }
        public double Rate
        {
            get
            {
                if (MosDuration > 0)
                {
                    return Math.Round(RtpDuration / MosDuration, 2);
                }
                return 0;
            }
        }
        public void AddInfo(VolteMosAndRtpItem info)
        {
            this.MosTpCount++;
            this.MosDuration += info.MosDuration;
            this.RtpDuration += info.RtpEvtDuration;
            foreach (RtpEvent rtpEvt in info.RtpEvts)
            {
                if (!this.RtpEvts.Contains(rtpEvt))
                {
                    this.RtpEvts.Add(rtpEvt);
                }
            }
        }
        public void Merge(VolteMosAndRtpSum info)
        {
            this.MosDuration += info.MosDuration;
            this.RtpDuration += info.RtpDuration;
            this.MosTpCount += info.MosTpCount;
            foreach (RtpEvent rtpEvt in info.RtpEvts)
            {
                if (!this.RtpEvts.Contains(rtpEvt))
                {
                    this.RtpEvts.Add(rtpEvt);
                }
            }
        }
    }
    public class RtpEvent
    {
        public RtpEvent(Event evt)
        {
            this.Evt = evt;
            long? bgITime = (long?)evt["Value1"];
            long? edITime = (long?)evt["Value2"];

            if (bgITime != null && edITime != null)
            {
                this.BeginTime = JavaDate.GetDateTimeFromMilliseconds((long)bgITime);
                this.EndTime = JavaDate.GetDateTimeFromMilliseconds((long)edITime);
                this.EvtPeriod = new TimePeriod(BeginTime, EndTime);

                this.RtpEvtDuration = (EndTime - BeginTime).TotalSeconds;
            }
        }
        public Event Evt { get; set; }
        public string EvtName { get { return Evt.Name; } }
        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimePeriod EvtPeriod { get; set; }
        public double RtpEvtDuration { get; private set; }
    }

    public class VolteMosTp
    {
        public VolteMosTp(TestPoint tp, float mosValue)
        {
            this.MosValue = mosValue;
            this.BeginTime = tp.DateTime.AddSeconds(-10);
            this.EndTime = tp.DateTime.AddSeconds(-2);
            this.MosPeriod = new TimePeriod(BeginTime, EndTime);
            this.Testpoint = tp;
        }
        public TestPoint Testpoint { get; set; }
        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }
        public double MosDuration { get; set; }

        private TimePeriod mosPeriod;
        public TimePeriod MosPeriod
        {
            get { return mosPeriod; }
            set
            {
                mosPeriod = value;
                MosDuration = (mosPeriod.EndTime - mosPeriod.BeginTime).TotalSeconds;
            }
        }
        public float MosValue { get; set; }
    }
    public class VolteRtpAndMosItem
    {
        public VolteRtpAndMosItem(RtpEvent evt)
        {
            this.RtpEvt = evt;
            VolteTps = new List<VolteMosTp>();
        }
        public int SN { get; set; }
        public RtpEvent RtpEvt { get; set; }
        public string RtpEvtName { get { return RtpEvt.Evt.Name; } }
        public string FileName { get { return RtpEvt.Evt.FileName; } }
        public double RtpEvtDuration { get { return RtpEvt.RtpEvtDuration; } }
        public DateTime RtpBeginTime { get { return RtpEvt.BeginTime; } }
        public DateTime RtpEndTime { get { return RtpEvt.EndTime; } }
        public List<VolteMosTp> VolteTps { get; set; }
        public int VolteTpCount { get { return VolteTps.Count; } }

        public string MosValuesDes { get; set; }
        public float? MosValueAvg { get; set; }
        public double MosDuration { get; private set; }
        public void AddVolteTp(VolteMosTp tp)
        {
            VolteTps.Add(tp);

            TimePeriod mosPeriod = RtpEvt.EvtPeriod;
            DateTime beginTime;
            DateTime endTime;
            if (tp.BeginTime >= mosPeriod.BeginTime)
            {
                beginTime = tp.BeginTime;
            }
            else
            {
                beginTime = mosPeriod.BeginTime;
            }

            if (tp.EndTime <= mosPeriod.EndTime)
            {
                endTime = tp.EndTime;
            }
            else
            {
                endTime = mosPeriod.EndTime;
            }

            double rtpDuration = (endTime - beginTime).TotalSeconds;
            this.MosDuration += rtpDuration;
        }
        public void GetSumInfo()
        {
            if (VolteTps.Count > 0)
            {
                float mosSum = 0;
                StringBuilder strbMos = new StringBuilder();
                foreach (VolteMosTp tp in VolteTps)
                {
                    strbMos.AppendLine(string.Format("{0}({1}s)", tp.MosValue, tp.MosDuration));
                    mosSum += tp.MosValue;
                }
                MosValuesDes = strbMos.ToString();
                MosValueAvg = (float)Math.Round(mosSum / VolteTps.Count, 2);
            }
        }
    }
}
