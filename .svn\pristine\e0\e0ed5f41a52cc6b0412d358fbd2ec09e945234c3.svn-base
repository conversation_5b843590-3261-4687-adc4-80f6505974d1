﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class MoBlockCallByFile : MoBlockCallQuery
    {
        private static MoBlockCallByFile instance=null;
        public static new MoBlockCallByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new MoBlockCallByFile();
                    }
                }
            }
            return instance;
        }

        protected MoBlockCallByFile()
            : base()
        {

        }

        public override string Name
        {
            get
            {
                return "VOLTE主叫未接通(按文件)";
            }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
    }

    public class MoBlockCallByFile_FDD : MoBlockCallQuery_FDD
    {
        private static MoBlockCallByFile_FDD instance = null;
        public static new MoBlockCallByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new MoBlockCallByFile_FDD();
                    }
                }
            }
            return instance;
        }
        protected MoBlockCallByFile_FDD()
            : base()
        {

        }
        public override string Name
        {
            get
            {
                return "VOLTE_FDD主叫未接通(按文件)";
            }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
    }
}
