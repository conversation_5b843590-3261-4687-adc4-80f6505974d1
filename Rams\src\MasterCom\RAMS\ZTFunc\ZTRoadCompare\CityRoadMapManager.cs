﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class CityRoadMapManager
    {
        private static CityRoadMapManager instance = null;
        private CityRoadMapManager()
        {
            cfgFileName = System.Windows.Forms.Application.StartupPath + "/config/RoadCompare.xml";
            LoadCfg();
        }
        public static CityRoadMapManager GetInstance()
        {
            if (instance==null)
            {
                instance = new CityRoadMapManager();
            }
            return instance;
        }
        
        public List<CityRoadMapCfg> CityRoadMapList { get; set; } = new List<CityRoadMapCfg>();
        private List<object> cfgParam
        {
            get
            {
                List<object> rpts = new List<object>();
                foreach (CityRoadMapCfg rpt in CityRoadMapList)
                {
                    rpts.Add(rpt.Param);
                }
                return rpts;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                CityRoadMapList.Clear();
                foreach (object obj in value)
                {
                    CityRoadMapCfg rpt = new CityRoadMapCfg();
                    rpt.Param = obj as Dictionary<string, object>;
                    CityRoadMapList.Add(rpt);
                }
            }
        }

        private readonly string cfgFileName;
        public void LoadCfg()
        {
            if (File.Exists(cfgFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(cfgFileName);
                cfgParam = configFile.GetItemValue("CityRoadMap", "Setting") as List<object>;
            }
        }
        public void Save()
        {
            MasterCom.Util.XmlConfigFile xmlFile = new MasterCom.Util.XmlConfigFile();
            XmlElement cfgE = xmlFile.AddConfig("CityRoadMap");
            xmlFile.AddItem(cfgE, "Setting", this.cfgParam);
            xmlFile.Save(cfgFileName);
        }
    }

    public class CityRoadMapCfg
    {
        public CityRoadMapCfg()
        { 
        }
        public CityRoadMapCfg(string name)
            : this()
        {
            this.Name = name;
        }
        public override string ToString()
        {
            return this.Name;
        }
        public string Name { get; set; }
        public string GridMapPath { get; set; }
        public string GridNameColumn { get; set; }
        
        public List<StreetInjectTableInfo> StreetInjectTablesList { get; set; } = new List<StreetInjectTableInfo>();
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Name"] = this.Name;
                param["GridMapPath"] = this.GridMapPath;
                param["GridNameColumn"] = this.GridNameColumn;

                List<object> streetInjectTables = new List<object>();
                foreach (StreetInjectTableInfo streetInfo in this.StreetInjectTablesList)
                {
                    streetInjectTables.Add(streetInfo.Param);
                }
                param["StreetInjectTables"] = streetInjectTables;

                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;

                if (param.ContainsKey("Name"))
                {
                    this.Name = (string)param["Name"];
                }
                if (param.ContainsKey("GridMapPath"))
                {
                    this.GridMapPath = (string)param["GridMapPath"];
                }
                if (param.ContainsKey("GridNameColumn"))
                {
                    this.GridNameColumn = (string)param["GridNameColumn"];
                }

                if (param.ContainsKey("StreetInjectTables"))
                {
                    this.StreetInjectTablesList.Clear();
                    List<object> tpParams = (List<object>)value["StreetInjectTables"];
                    foreach (object o in tpParams)
                    {
                        Dictionary<string, object> cfgParam = (Dictionary<string, object>)o;
                        StreetInjectTableInfo item = new StreetInjectTableInfo();
                        item.Param = cfgParam;
                        this.StreetInjectTablesList.Add(item);
                    }
                }

            }
        }
    }
}
