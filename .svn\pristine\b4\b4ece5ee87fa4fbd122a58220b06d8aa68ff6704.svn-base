﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTNRCheckCellOccupation;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRCheckCellOccupationBase : DIYAnalyseByFileBackgroundBase
    {
        NRCellTpInfo cellTpList = null;
        NRCheckCellOccupationCond con = new NRCheckCellOccupationCond(-110, 1000, -90, 3, 1.6);//设置参数
        NRCheckCellOccupationDlg condForm;
        protected static readonly object lockObj = new object();

        public NRCheckCellOccupationBase()
            : base(MainModel.GetInstance())
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(false, false);

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        public override string Name
        {
            get { return "小区占用核查"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35035, this.Name);
        }

        protected override bool getCondition()
        {
            if (condForm == null)
            {
                condForm = new NRCheckCellOccupationDlg();
            }
            condForm.SetCondition(con);
            if (condForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            condForm.GetCondition(ref this.con);

            return true;
        }

        protected override void fireShowForm()
        {
            NRCheckCellOccupationForm resultFrom = MainModel.CreateResultForm(typeof(NRCheckCellOccupationForm)) as NRCheckCellOccupationForm;
            this.cellTpList.GetResult(this.con);
            List<NRCheckCellOccupationResult> listResult = this.cellTpList.listResult;
            resultFrom.FillData(listResult);
            resultFrom.Visible = true;
            resultFrom.BringToFront();
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            this.cellTpList = new NRCellTpInfo();
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
        
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                List<TestPoint> listTp = fileDataManager.TestPoints;
                foreach (TestPoint tp in listTp)
                {
                    this.cellTpList.AddTestPoint(tp);
                }
            }
        }

        protected override void getResultsAfterQuery()
        {
            //
        }
    }

    public class NRCheckCellOccupyByRegion : NRCheckCellOccupationBase
    {
        private static NRCheckCellOccupyByRegion instance = null;
        public static NRCheckCellOccupyByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRCheckCellOccupyByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get
            {
                return "占用核查(按区域)";
            }
        }
        protected NRCheckCellOccupyByRegion()
            : base()
        {
            FilterSampleByRegion = true;
        }
    }

    public class NRCheckCellOccupyByFile : NRCheckCellOccupationBase
    {
        private static NRCheckCellOccupyByFile intance = null;
        public static NRCheckCellOccupyByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new NRCheckCellOccupyByFile();
                    }
                }
            }
            return intance;

        }

        public override string Name
        {
            get
            {
                return "占用核查(按文件)";
            }
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;

        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }


        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
