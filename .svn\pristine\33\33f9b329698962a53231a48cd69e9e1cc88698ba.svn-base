﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    [Serializable]
    public abstract class CauseBase
    {


        public override string ToString()
        {
            return this.Name;
        }
        public abstract string Name
        {
            get;
        }

        protected void AddSubReason(CauseBase r)
        {
            if (SubCauses==null)
            {
                SubCauses = new List<CauseBase>();
            }
            r.Parent = this;
            SubCauses.Add(r);
        }
        [NonSerialized]
        private CauseBase parent = null;
        public CauseBase Parent
        {
            get { return parent; }
            set { parent = value; }
        }

        public CauseBase Ancestor
        {
            get
            {
                CauseBase ret = this;
                while (ret != null)
                {
                    if (ret.parent == null)
                    {
                        break;
                    }
                    ret = ret.parent;
                }
                return ret;
            }
        }

        public virtual List<CauseBase> SubCauses
        {
            get;
            set;
        }

        public abstract string Desc
        {
            get;
        }

        public abstract string Suggestion
        {
            get;
        }

        public virtual CauseBase Clone()
        {
            using (MemoryStream objectStream = new MemoryStream())
            {
                IFormatter formatter = new BinaryFormatter();
                formatter.Serialize(objectStream, this);
                objectStream.Seek(0, SeekOrigin.Begin);
                CauseBase r = formatter.Deserialize(objectStream) as CauseBase;
                r.parent = this.parent;
                return r;
            }
        }

        public virtual void Judge(LowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP)
        { }

        public virtual void JudgeSinglePoint(LowSpeedSeg segItem, TestPoint testPoint)
        { }


        public abstract Dictionary<string, object> CfgParam
        {
            get;
            set;
        }
    }

    public class UnknowReason : CauseBase
    {
        public override string Name
        {
            get { return "未知原因"; }
        }

        public override string Desc
        {
            get { return Name; }
        }

        public override string Suggestion
        {
            get { return string.Empty; }
        }


        public override void Judge(LowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP)
        {
            //
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                throw new NotImplementedException();
            }
            set
            {
                throw new NotImplementedException();
            }
        }
    }
}
