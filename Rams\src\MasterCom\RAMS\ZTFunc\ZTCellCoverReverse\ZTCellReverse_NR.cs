﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCellReverse_NR : DIYAnalyseByCellBackgroundBaseByPeriod
    {
        protected static readonly object lockObj = new object();

        protected CellReverseDirSettingDlg_CP conditionDlg;
        public CellWrongDirCondition cellWrongCond { get; set; }

        protected Dictionary<ICell, NRCellWrongDir> cellDic;

        protected CellWrongBatch cellWrongBatch;

        protected ZTCellReverse_NR(MainModel mainModel)
            : base(mainModel)
        {
            DateTime dtDay = DateTime.Now.Date.AddDays(-1);
            TimePeriod tPeriod = new TimePeriod(dtDay, dtDay.AddDays(2).AddMilliseconds(-1));
            cellWrongCond = new CellWrongDirCondition(-100, 100, 50, 50, 30, false, null, null);
            cellDic = new Dictionary<ICell, NRCellWrongDir>();
        }


        public string themeName { get; set; } = "";

        private static ZTCellReverse_NR instance = null;
        public static ZTCellReverse_NR GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCellReverse_NR();
                    }
                }
            }
            return instance;
        }

        protected ZTCellReverse_NR()
            : this(MainModel.GetInstance())
        {
            init();
        }

        protected void init()
        {
            themeName = "NR:SS_RSRP";
            isAddSampleToDTDataManager = false;
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "NR天线接反"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35029, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = NRTpHelper.InitBaseReplayParamSample(false, true);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void FireShowFormAfterQuery()
        {
            if (cellDic.Count == 0)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("没有符合条件的数据！");
                return;
            }

            getResultAfterQuery();

            // 以基站为中心，进行汇聚
            var btsCellDic = new Dictionary<string, List<NRCellWrongDir>>();
            List<NRCellWrongDir> cellsWrong;
            foreach (var nrcell in cellDic)
            {
                var isNrCell = nrcell.Key is NRCell;
                if (!isNrCell)
                {
                    continue;
                }

                var nrBtsName = ((NRCell)nrcell.Key).BTSName;

                if (!btsCellDic.TryGetValue(nrBtsName, out cellsWrong))
                {
                    cellsWrong = new List<NRCellWrongDir>();
                    btsCellDic.Add(nrBtsName, cellsWrong);
                }
                cellsWrong.Add(nrcell.Value);
            }

            var frm = MainModel.CreateResultForm(typeof(NRCellReverseResultWrongForm)) as NRCellReverseResultWrongForm;
            frm.FillData(btsCellDic, false);
            frm.Visible = true;
            frm.BringToFront();
            MainModel.FireSetDefaultMapSerialTheme(themeName);
        }

        protected override void doWithDTData(TestPoint tp)
        {
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            if (rsrp != null && rsrp >= -141 && rsrp <= 25 && rsrp >= cellWrongCond.RxLevMin)
            {
                NRCell cell = tp.GetMainCell_NR();
                if (cell == null)
                    return;

                double dis = cell.GetDistance(tp.Longitude, tp.Latitude);
                if (dis >= cellWrongCond.DistanceMin)
                {
                    NRCellWrongDir cellWrong;
                    if (!cellDic.TryGetValue(cell, out cellWrong))
                    {
                        cellWrong = new NRCellWrongDir(cell);
                        cellDic.Add(cell, cellWrong);
                    }
                    cellWrong.AddTestPoint(tp, cellWrongCond.AngleMin, cellWrongBatch);
                }
            }
        }

        protected override void getResultAfterQuery()
        {
            List<object> cells = new List<object>();
            foreach (ICell cell in cellDic.Keys)
            {
                if (cellDic[cell].WrongPercentage < cellWrongCond.WrongRateMin ||
                    cellDic[cell].WrongTestPointCount < cellWrongCond.WrongSampleNum)
                {
                    cells.Add(cell);
                }
            }

            foreach (ICell cell in cells)
            {
                cellDic.Remove(cell);
            }
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            cellDic.Clear();
            if (cellWrongCond.IsTwiceBatch)
            {
                condition.Periods = cellWrongCond.GetTimePeriods;
            }
            cellWrongBatch = CellWrongBatch.First;
        }

        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            if (conditionDlg == null)
            {
                conditionDlg = new CellReverseDirSettingDlg_CP();
            }

            conditionDlg.SetCondition(cellWrongCond);
            if (conditionDlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                cellWrongCond = conditionDlg.GetConditon();
                return true;
            }
            return false;
        }

        protected override void queryInThread(object o)
        {
            try
            {
                doSomethingBeforeQueryInThread();
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = index / periodCount + 10;
                        queryPeriodInfo(clientProxy, package, period, false);

                        cellWrongBatch = CellWrongBatch.Second;
                    }
                }
                getResultAfterQuery();
            }
            catch (Exception ex)
            {
                log.Error(ex.Message + Environment.NewLine + ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }

    }

}
