﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class DropCallReasonResultDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel1 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PiePointOptions piePointOptions1 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PiePointOptions piePointOptions2 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView1 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel2 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView2 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.ChartTitle chartTitle1 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.Series series2 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel3 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PiePointOptions piePointOptions3 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PiePointOptions piePointOptions4 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView3 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel4 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView4 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.ChartTitle chartTitle2 = new DevExpress.XtraCharts.ChartTitle();
            this.listViewMo = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMoFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeginTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAttemptTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDropTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSpanTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnReason = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.cStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ExportItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ReplayItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tabC = new System.Windows.Forms.TabControl();
            this.tabTotal = new System.Windows.Forms.TabPage();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gCMo = new DevExpress.XtraGrid.GridControl();
            this.gVMo = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnSN = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnReason = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNum = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPer = new DevExpress.XtraGrid.Columns.GridColumn();
            this.chartControlReason = new DevExpress.XtraCharts.ChartControl();
            this.splitContainerControl3 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gCMt = new DevExpress.XtraGrid.GridControl();
            this.gVMt = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnMtSN = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnMtReason = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnMtNum = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnMtPer = new DevExpress.XtraGrid.Columns.GridColumn();
            this.chartControlMtReason = new DevExpress.XtraCharts.ChartControl();
            this.tabMo = new System.Windows.Forms.TabPage();
            this.tabMt = new System.Windows.Forms.TabPage();
            this.ListViewMt = new BrightIdeasSoftware.TreeListView();
            this.olvColumnMtSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtMoFile = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtMtFile = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtBeginTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtAttemptTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtDropTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtSpanTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtReason = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            ((System.ComponentModel.ISupportInitialize)(this.listViewMo)).BeginInit();
            this.cStrip.SuspendLayout();
            this.tabC.SuspendLayout();
            this.tabTotal.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gCMo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gVMo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).BeginInit();
            this.splitContainerControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gCMt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gVMt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlMtReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView4)).BeginInit();
            this.tabMo.SuspendLayout();
            this.tabMt.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewMt)).BeginInit();
            this.SuspendLayout();
            // 
            // listViewMo
            // 
            this.listViewMo.AllColumns.Add(this.olvColumnSN);
            this.listViewMo.AllColumns.Add(this.olvColumnMoFileName);
            this.listViewMo.AllColumns.Add(this.olvColumnMtFileName);
            this.listViewMo.AllColumns.Add(this.olvColumnBeginTime);
            this.listViewMo.AllColumns.Add(this.olvColumnAttemptTime);
            this.listViewMo.AllColumns.Add(this.olvColumnDropTime);
            this.listViewMo.AllColumns.Add(this.olvColumnSpanTime);
            this.listViewMo.AllColumns.Add(this.olvColumnReason);
            this.listViewMo.AllColumns.Add(this.olvColumnDesc);
            this.listViewMo.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnMoFileName,
            this.olvColumnMtFileName,
            this.olvColumnBeginTime,
            this.olvColumnAttemptTime,
            this.olvColumnDropTime,
            this.olvColumnSpanTime,
            this.olvColumnReason,
            this.olvColumnDesc});
            this.listViewMo.ContextMenuStrip = this.cStrip;
            this.listViewMo.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewMo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewMo.FullRowSelect = true;
            this.listViewMo.GridLines = true;
            this.listViewMo.HeaderWordWrap = true;
            this.listViewMo.IsNeedShowOverlay = false;
            this.listViewMo.Location = new System.Drawing.Point(3, 3);
            this.listViewMo.Name = "listViewMo";
            this.listViewMo.OwnerDraw = true;
            this.listViewMo.ShowGroups = false;
            this.listViewMo.Size = new System.Drawing.Size(913, 526);
            this.listViewMo.TabIndex = 13;
            this.listViewMo.UseCompatibleStateImageBehavior = false;
            this.listViewMo.View = System.Windows.Forms.View.Details;
            this.listViewMo.VirtualMode = true;
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnMoFileName
            // 
            this.olvColumnMoFileName.HeaderFont = null;
            this.olvColumnMoFileName.Text = "主叫文件";
            this.olvColumnMoFileName.Width = 180;
            // 
            // olvColumnMtFileName
            // 
            this.olvColumnMtFileName.HeaderFont = null;
            this.olvColumnMtFileName.Text = "被叫文件";
            this.olvColumnMtFileName.Width = 180;
            // 
            // olvColumnBeginTime
            // 
            this.olvColumnBeginTime.HeaderFont = null;
            this.olvColumnBeginTime.Text = "ACK时间";
            this.olvColumnBeginTime.Width = 120;
            // 
            // olvColumnAttemptTime
            // 
            this.olvColumnAttemptTime.HeaderFont = null;
            this.olvColumnAttemptTime.Text = "CallAttempt时间";
            this.olvColumnAttemptTime.Width = 120;
            // 
            // olvColumnDropTime
            // 
            this.olvColumnDropTime.HeaderFont = null;
            this.olvColumnDropTime.Text = "DropCall时间";
            this.olvColumnDropTime.Width = 120;
            // 
            // olvColumnSpanTime
            // 
            this.olvColumnSpanTime.HeaderFont = null;
            this.olvColumnSpanTime.Text = "通话时长(s)";
            // 
            // olvColumnReason
            // 
            this.olvColumnReason.HeaderFont = null;
            this.olvColumnReason.Text = "原因";
            this.olvColumnReason.Width = 120;
            // 
            // olvColumnDesc
            // 
            this.olvColumnDesc.HeaderFont = null;
            this.olvColumnDesc.Text = "具体描述";
            this.olvColumnDesc.Width = 120;
            // 
            // cStrip
            // 
            this.cStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ExportItem,
            this.ReplayItem});
            this.cStrip.Name = "cStrip";
            this.cStrip.Size = new System.Drawing.Size(142, 48);
            // 
            // ExportItem
            // 
            this.ExportItem.Name = "ExportItem";
            this.ExportItem.Size = new System.Drawing.Size(141, 22);
            this.ExportItem.Text = "导出到Excel";
            this.ExportItem.Click += new System.EventHandler(this.ExportItem_Click);
            // 
            // ReplayItem
            // 
            this.ReplayItem.Name = "ReplayItem";
            this.ReplayItem.Size = new System.Drawing.Size(141, 22);
            this.ReplayItem.Text = "回放文件";
            this.ReplayItem.Click += new System.EventHandler(this.ReplayItem_Click);
            // 
            // tabC
            // 
            this.tabC.Controls.Add(this.tabTotal);
            this.tabC.Controls.Add(this.tabMo);
            this.tabC.Controls.Add(this.tabMt);
            this.tabC.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabC.Location = new System.Drawing.Point(0, 0);
            this.tabC.Name = "tabC";
            this.tabC.SelectedIndex = 0;
            this.tabC.Size = new System.Drawing.Size(927, 559);
            this.tabC.TabIndex = 14;
            // 
            // tabTotal
            // 
            this.tabTotal.Controls.Add(this.splitContainerControl1);
            this.tabTotal.Location = new System.Drawing.Point(4, 23);
            this.tabTotal.Name = "tabTotal";
            this.tabTotal.Size = new System.Drawing.Size(919, 532);
            this.tabTotal.TabIndex = 2;
            this.tabTotal.Text = "汇总";
            this.tabTotal.UseVisualStyleBackColor = true;
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.splitContainerControl2);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.splitContainerControl3);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(919, 532);
            this.splitContainerControl1.SplitterPosition = 460;
            this.splitContainerControl1.TabIndex = 0;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl2.Horizontal = false;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.chartControlReason);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.gCMo);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(460, 532);
            this.splitContainerControl2.SplitterPosition = 297;
            this.splitContainerControl2.TabIndex = 3;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // gCMo
            // 
            this.gCMo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gCMo.Location = new System.Drawing.Point(0, 0);
            this.gCMo.MainView = this.gVMo;
            this.gCMo.Name = "gCMo";
            this.gCMo.Size = new System.Drawing.Size(460, 229);
            this.gCMo.TabIndex = 0;
            this.gCMo.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gVMo});
            // 
            // gVMo
            // 
            this.gVMo.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnSN,
            this.gridColumnReason,
            this.gridColumnNum,
            this.gridColumnPer});
            this.gVMo.GridControl = this.gCMo;
            this.gVMo.Name = "gVMo";
            this.gVMo.OptionsBehavior.Editable = false;
            // 
            // gridColumnSN
            // 
            this.gridColumnSN.Caption = "序号";
            this.gridColumnSN.FieldName = "SN";
            this.gridColumnSN.Name = "gridColumnSN";
            this.gridColumnSN.Visible = true;
            this.gridColumnSN.VisibleIndex = 0;
            // 
            // gridColumnReason
            // 
            this.gridColumnReason.Caption = "原因";
            this.gridColumnReason.FieldName = "Reason";
            this.gridColumnReason.Name = "gridColumnReason";
            this.gridColumnReason.Visible = true;
            this.gridColumnReason.VisibleIndex = 1;
            // 
            // gridColumnNum
            // 
            this.gridColumnNum.Caption = "次数";
            this.gridColumnNum.FieldName = "Num";
            this.gridColumnNum.Name = "gridColumnNum";
            this.gridColumnNum.Visible = true;
            this.gridColumnNum.VisibleIndex = 2;
            // 
            // gridColumnPer
            // 
            this.gridColumnPer.Caption = "占比";
            this.gridColumnPer.FieldName = "Per";
            this.gridColumnPer.Name = "gridColumnPer";
            this.gridColumnPer.Visible = true;
            this.gridColumnPer.VisibleIndex = 3;
            // 
            // chartControlReason
            // 
            this.chartControlReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlReason.Location = new System.Drawing.Point(0, 0);
            this.chartControlReason.Name = "chartControlReason";
            this.chartControlReason.RuntimeSelection = true;
            this.chartControlReason.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            pieSeriesLabel1.Border.Visible = false;
            pieSeriesLabel1.LineVisible = true;
            series1.Label = pieSeriesLabel1;
            piePointOptions1.PointView = DevExpress.XtraCharts.PointView.Argument;
            series1.LegendPointOptions = piePointOptions1;
            series1.Name = "Series1";
            piePointOptions2.PercentOptions.PercentageAccuracy = 4;
            piePointOptions2.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series1.PointOptions = piePointOptions2;
            series1.SynchronizePointOptions = false;
            pieSeriesView1.RuntimeExploding = false;
            series1.View = pieSeriesView1;
            this.chartControlReason.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1};
            pieSeriesLabel2.LineVisible = true;
            this.chartControlReason.SeriesTemplate.Label = pieSeriesLabel2;
            pieSeriesView2.RuntimeExploding = false;
            this.chartControlReason.SeriesTemplate.View = pieSeriesView2;
            this.chartControlReason.Size = new System.Drawing.Size(460, 297);
            this.chartControlReason.TabIndex = 2;
            chartTitle1.Text = "主叫掉话原因占比";
            this.chartControlReason.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle1});
            // 
            // splitContainerControl3
            // 
            this.splitContainerControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl3.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl3.Horizontal = false;
            this.splitContainerControl3.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl3.Name = "splitContainerControl3";
            this.splitContainerControl3.Panel1.Controls.Add(this.chartControlMtReason);
            this.splitContainerControl3.Panel1.Text = "Panel1";
            this.splitContainerControl3.Panel2.Controls.Add(this.gCMt);
            this.splitContainerControl3.Panel2.Text = "Panel2";
            this.splitContainerControl3.Size = new System.Drawing.Size(453, 532);
            this.splitContainerControl3.SplitterPosition = 298;
            this.splitContainerControl3.TabIndex = 4;
            this.splitContainerControl3.Text = "splitContainerControl3";
            // 
            // gCMt
            // 
            this.gCMt.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gCMt.Location = new System.Drawing.Point(0, 0);
            this.gCMt.MainView = this.gVMt;
            this.gCMt.Name = "gCMt";
            this.gCMt.Size = new System.Drawing.Size(453, 228);
            this.gCMt.TabIndex = 0;
            this.gCMt.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gVMt});
            // 
            // gVMt
            // 
            this.gVMt.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnMtSN,
            this.gridColumnMtReason,
            this.gridColumnMtNum,
            this.gridColumnMtPer});
            this.gVMt.GridControl = this.gCMt;
            this.gVMt.Name = "gVMt";
            this.gVMt.OptionsBehavior.Editable = false;
            // 
            // gridColumnMtSN
            // 
            this.gridColumnMtSN.Caption = "序号";
            this.gridColumnMtSN.FieldName = "SN";
            this.gridColumnMtSN.Name = "gridColumnMtSN";
            this.gridColumnMtSN.Visible = true;
            this.gridColumnMtSN.VisibleIndex = 0;
            // 
            // gridColumnMtReason
            // 
            this.gridColumnMtReason.Caption = "原因";
            this.gridColumnMtReason.FieldName = "Reason";
            this.gridColumnMtReason.Name = "gridColumnMtReason";
            this.gridColumnMtReason.Visible = true;
            this.gridColumnMtReason.VisibleIndex = 1;
            // 
            // gridColumnMtNum
            // 
            this.gridColumnMtNum.Caption = "次数";
            this.gridColumnMtNum.FieldName = "Num";
            this.gridColumnMtNum.Name = "gridColumnMtNum";
            this.gridColumnMtNum.Visible = true;
            this.gridColumnMtNum.VisibleIndex = 2;
            // 
            // gridColumnMtPer
            // 
            this.gridColumnMtPer.Caption = "占比";
            this.gridColumnMtPer.FieldName = "Per";
            this.gridColumnMtPer.Name = "gridColumnMtPer";
            this.gridColumnMtPer.Visible = true;
            this.gridColumnMtPer.VisibleIndex = 3;
            // 
            // chartControlMtReason
            // 
            this.chartControlMtReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlMtReason.Location = new System.Drawing.Point(0, 0);
            this.chartControlMtReason.Name = "chartControlMtReason";
            this.chartControlMtReason.RuntimeSelection = true;
            this.chartControlMtReason.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            pieSeriesLabel3.Border.Visible = false;
            pieSeriesLabel3.LineVisible = true;
            series2.Label = pieSeriesLabel3;
            piePointOptions3.PointView = DevExpress.XtraCharts.PointView.Argument;
            series2.LegendPointOptions = piePointOptions3;
            series2.Name = "Series 1";
            piePointOptions4.PercentOptions.PercentageAccuracy = 4;
            piePointOptions4.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series2.PointOptions = piePointOptions4;
            series2.SynchronizePointOptions = false;
            pieSeriesView3.RuntimeExploding = false;
            series2.View = pieSeriesView3;
            this.chartControlMtReason.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series2};
            pieSeriesLabel4.LineVisible = true;
            this.chartControlMtReason.SeriesTemplate.Label = pieSeriesLabel4;
            pieSeriesView4.RuntimeExploding = false;
            this.chartControlMtReason.SeriesTemplate.View = pieSeriesView4;
            this.chartControlMtReason.Size = new System.Drawing.Size(453, 298);
            this.chartControlMtReason.TabIndex = 3;
            chartTitle2.Text = "被叫掉话原因占比";
            this.chartControlMtReason.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle2});
            // 
            // tabMo
            // 
            this.tabMo.Controls.Add(this.listViewMo);
            this.tabMo.Location = new System.Drawing.Point(4, 23);
            this.tabMo.Name = "tabMo";
            this.tabMo.Padding = new System.Windows.Forms.Padding(3);
            this.tabMo.Size = new System.Drawing.Size(919, 532);
            this.tabMo.TabIndex = 0;
            this.tabMo.Text = "主叫掉话原因分析";
            this.tabMo.UseVisualStyleBackColor = true;
            // 
            // tabMt
            // 
            this.tabMt.Controls.Add(this.ListViewMt);
            this.tabMt.Location = new System.Drawing.Point(4, 23);
            this.tabMt.Name = "tabMt";
            this.tabMt.Padding = new System.Windows.Forms.Padding(3);
            this.tabMt.Size = new System.Drawing.Size(919, 532);
            this.tabMt.TabIndex = 1;
            this.tabMt.Text = "被叫掉话原因分析";
            this.tabMt.UseVisualStyleBackColor = true;
            // 
            // ListViewMt
            // 
            this.ListViewMt.AllColumns.Add(this.olvColumnMtSN);
            this.ListViewMt.AllColumns.Add(this.olvColumnMtMoFile);
            this.ListViewMt.AllColumns.Add(this.olvColumnMtMtFile);
            this.ListViewMt.AllColumns.Add(this.olvColumnMtBeginTime);
            this.ListViewMt.AllColumns.Add(this.olvColumnMtAttemptTime);
            this.ListViewMt.AllColumns.Add(this.olvColumnMtDropTime);
            this.ListViewMt.AllColumns.Add(this.olvColumnMtSpanTime);
            this.ListViewMt.AllColumns.Add(this.olvColumnMtReason);
            this.ListViewMt.AllColumns.Add(this.olvColumnMtDesc);
            this.ListViewMt.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnMtSN,
            this.olvColumnMtMoFile,
            this.olvColumnMtMtFile,
            this.olvColumnMtBeginTime,
            this.olvColumnMtAttemptTime,
            this.olvColumnMtDropTime,
            this.olvColumnMtSpanTime,
            this.olvColumnMtReason,
            this.olvColumnMtDesc});
            this.ListViewMt.ContextMenuStrip = this.cStrip;
            this.ListViewMt.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewMt.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewMt.FullRowSelect = true;
            this.ListViewMt.GridLines = true;
            this.ListViewMt.HeaderWordWrap = true;
            this.ListViewMt.IsNeedShowOverlay = false;
            this.ListViewMt.Location = new System.Drawing.Point(3, 3);
            this.ListViewMt.Name = "ListViewMt";
            this.ListViewMt.OwnerDraw = true;
            this.ListViewMt.ShowGroups = false;
            this.ListViewMt.Size = new System.Drawing.Size(913, 526);
            this.ListViewMt.TabIndex = 14;
            this.ListViewMt.UseCompatibleStateImageBehavior = false;
            this.ListViewMt.View = System.Windows.Forms.View.Details;
            this.ListViewMt.VirtualMode = true;
            // 
            // olvColumnMtSN
            // 
            this.olvColumnMtSN.HeaderFont = null;
            this.olvColumnMtSN.Text = "序号";
            // 
            // olvColumnMtMoFile
            // 
            this.olvColumnMtMoFile.HeaderFont = null;
            this.olvColumnMtMoFile.Text = "主叫文件";
            this.olvColumnMtMoFile.Width = 180;
            // 
            // olvColumnMtMtFile
            // 
            this.olvColumnMtMtFile.HeaderFont = null;
            this.olvColumnMtMtFile.Text = "被叫文件";
            this.olvColumnMtMtFile.Width = 180;
            // 
            // olvColumnMtBeginTime
            // 
            this.olvColumnMtBeginTime.HeaderFont = null;
            this.olvColumnMtBeginTime.Text = "ACK时间";
            this.olvColumnMtBeginTime.Width = 120;
            // 
            // olvColumnMtAttemptTime
            // 
            this.olvColumnMtAttemptTime.HeaderFont = null;
            this.olvColumnMtAttemptTime.Text = "CallAttempt时间";
            this.olvColumnMtAttemptTime.Width = 120;
            // 
            // olvColumnMtDropTime
            // 
            this.olvColumnMtDropTime.HeaderFont = null;
            this.olvColumnMtDropTime.Text = "DropCall时间";
            this.olvColumnMtDropTime.Width = 120;
            // 
            // olvColumnMtSpanTime
            // 
            this.olvColumnMtSpanTime.HeaderFont = null;
            this.olvColumnMtSpanTime.Text = "通话时长(s)";
            // 
            // olvColumnMtReason
            // 
            this.olvColumnMtReason.HeaderFont = null;
            this.olvColumnMtReason.Text = "原因";
            this.olvColumnMtReason.Width = 120;
            // 
            // olvColumnMtDesc
            // 
            this.olvColumnMtDesc.HeaderFont = null;
            this.olvColumnMtDesc.Text = "具体描述";
            this.olvColumnMtDesc.Width = 120;
            // 
            // DropCallReasonResultDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(927, 559);
            this.Controls.Add(this.tabC);
            this.Name = "DropCallReasonResultDlg";
            this.Text = "掉话原因分析";
            ((System.ComponentModel.ISupportInitialize)(this.listViewMo)).EndInit();
            this.cStrip.ResumeLayout(false);
            this.tabC.ResumeLayout(false);
            this.tabTotal.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gCMo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gVMo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).EndInit();
            this.splitContainerControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gCMt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gVMt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlMtReason)).EndInit();
            this.tabMo.ResumeLayout(false);
            this.tabMt.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewMt)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView listViewMo;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnMoFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnMtFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnBeginTime;
        private System.Windows.Forms.TabControl tabC;
        private System.Windows.Forms.TabPage tabTotal;
        private System.Windows.Forms.TabPage tabMo;
        private System.Windows.Forms.TabPage tabMt;
        private BrightIdeasSoftware.TreeListView ListViewMt;
        private BrightIdeasSoftware.OLVColumn olvColumnMtSN;
        private BrightIdeasSoftware.OLVColumn olvColumnMtMoFile;
        private BrightIdeasSoftware.OLVColumn olvColumnMtMtFile;
        private BrightIdeasSoftware.OLVColumn olvColumnMtBeginTime;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraCharts.ChartControl chartControlReason;
        private DevExpress.XtraCharts.ChartControl chartControlMtReason;
        private System.Windows.Forms.ContextMenuStrip cStrip;
        private System.Windows.Forms.ToolStripMenuItem ExportItem;
        private System.Windows.Forms.ToolStripMenuItem ReplayItem;
        private BrightIdeasSoftware.OLVColumn olvColumnDesc;
        private BrightIdeasSoftware.OLVColumn olvColumnMtDesc;
        private BrightIdeasSoftware.OLVColumn olvColumnReason;
        private BrightIdeasSoftware.OLVColumn olvColumnMtReason;
        private BrightIdeasSoftware.OLVColumn olvColumnAttemptTime;
        private BrightIdeasSoftware.OLVColumn olvColumnDropTime;
        private BrightIdeasSoftware.OLVColumn olvColumnSpanTime;
        private BrightIdeasSoftware.OLVColumn olvColumnMtAttemptTime;
        private BrightIdeasSoftware.OLVColumn olvColumnMtDropTime;
        private BrightIdeasSoftware.OLVColumn olvColumnMtSpanTime;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl3;
        private DevExpress.XtraGrid.GridControl gCMo;
        private DevExpress.XtraGrid.Views.Grid.GridView gVMo;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSN;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnReason;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNum;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPer;
        private DevExpress.XtraGrid.GridControl gCMt;
        private DevExpress.XtraGrid.Views.Grid.GridView gVMt;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnMtSN;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnMtReason;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnMtNum;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnMtPer;


    }
}