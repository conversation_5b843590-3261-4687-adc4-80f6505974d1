﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEFddCSFBDelayAnaBase : ZTLTECSFBDelayAnaBase
    {
        public LTEFddCSFBDelayAnaBase(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26020, this.Name);
        }
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                ZTLTECSFBDelayFileItem fileItem = new ZTLTECSFBDelayFileItem(fileMng.FileName, fileMng.MoMtFlag);

                List<DTData> dtDataList = getDTDataList(fileMng);

                TestPoint lastTp = null;
                Message lastMsg = null;
                ZTLTECSFBDelayAnaItem csfbItem = new ZTLTECSFBDelayAnaItem();

                for (int i = 0; i < dtDataList.Count; i++)
                {
                    dealDTData(fileItem, dtDataList, ref lastTp, ref lastMsg, ref csfbItem, i);
                }

                addToResultList(csfbItem, fileItem); //最后一个

                if (fileItem.csfbList.Count > 1)
                {
                    fileItem.SN = resultList.Count + 1;
                    resultList.Add(fileItem);
                }
            }
        }

        private List<DTData> getDTDataList(DTFileDataManager fileMng)
        {
            List<DTData> dtDataList = new List<DTData>();

            foreach (TestPoint tp in fileMng.TestPoints)
            {
                dtDataList.Add((DTData)tp);
            }

            foreach (MasterCom.RAMS.Model.Message msg in fileMng.Messages)
            {
                if (msg.ID == (int)EnumFddLteNBCheckMsg.RRCConnectionRelease || msg.ID == (int)EnumFddLteNBCheckMsg.ServiceRequest_W
                    || msg.ID == (int)EnumFddLteNBCheckMsg.PagingResponse || msg.ID == (int)EnumFddLteNBCheckMsg.Setup_W
                    || msg.ID == (int)EnumFddLteNBCheckMsg.ServiceRequest_Gsm || msg.ID == (int)EnumFddLteNBCheckMsg.Setup_Gsm)
                {
                    dtDataList.Add((DTData)msg);
                }
            }
            foreach (Event evt in fileMng.Events)
            {
                if (evt.ID == (int)EnumFddCsfbEvent.MOCSFBRequest || evt.ID == (int)EnumFddCsfbEvent.MOCSFBLTERelease
                    || evt.ID == (int)EnumFddCsfbEvent.MOCSFBProceeding || evt.ID == (int)EnumFddCsfbEvent.MTCSFBRequest
                    || evt.ID == (int)EnumFddCsfbEvent.MTCSFBLTERelease || evt.ID == (int)EnumFddCsfbEvent.MTCSFBProceeding
                    || evt.ID == (int)EnumFddCsfbEvent.TrackAreaUpdateAttempt || evt.ID == (int)EnumFddCsfbEvent.TrackAreaUpdateSuccess
                    || evt.ID == (int)EnumFddCsfbEvent.TrackAreaUpdateFail || evt.ID == (int)EnumFddCsfbEvent.LocationUpdateRequestLte_W
                    || evt.ID == (int)EnumFddCsfbEvent.LocationUpdateRequestLte_Gsm
                    || evt.ID == (int)EnumFddCsfbEvent.CellReselection_L2W || evt.ID == (int)EnumFddCsfbEvent.CellReselection_L2G)
                {
                    dtDataList.Add((DTData)evt);
                }
            }

            dtDataList.Sort(comparer);
            return dtDataList;
        }

        private void dealDTData(ZTLTECSFBDelayFileItem fileItem, List<DTData> dtDataList, ref TestPoint lastTp, 
            ref Message lastMsg, ref ZTLTECSFBDelayAnaItem csfbItem, int i)
        {
            if (dtDataList[i] is TestPoint)
            {
                lastTp = dtDataList[i] as TestPoint;                  //用最近的采样点经纬度来填充
            }
            else if (dtDataList[i] is Event)
            {
                if (lastMsg != null && lastMsg.ID == (int)EnumFddLteNBCheckMsg.RRCConnectionRelease)
                {
                    Event evt = dtDataList[i] as Event;
                    csfbItem.Latitude = evt.Latitude;
                    csfbItem.Longitude = evt.Longitude;
                }
                if (((dtDataList[i] as Event).ID == (int)EnumFddCsfbEvent.MOCSFBRequest
                        || (dtDataList[i] as Event).ID == (int)EnumFddCsfbEvent.MTCSFBRequest)
                    && csfbItem.IsGotBegin)
                {
                    //前一个事件
                    addToResultList(csfbItem, fileItem);
                    csfbItem = new ZTLTECSFBDelayAnaItem();
                }
                doWithEvent(dtDataList[i], lastTp, ref csfbItem);
            }
            else if (dtDataList[i] is MasterCom.RAMS.Model.Message)
            {
                lastMsg = (Message)dtDataList[i];
                doWithMsg(dtDataList[i], lastTp, ref csfbItem);
            }
        }

        private void doWithEvent(DTData dtData, TestPoint lastTp, ref ZTLTECSFBDelayAnaItem curItem)
        {
            Event evt = (Event)dtData;
            switch (evt.ID)
            {
                case (int)EnumFddCsfbEvent.MOCSFBRequest:
                case (int)EnumFddCsfbEvent.MTCSFBRequest:
                    //开始
                    curItem.AddBeginEvtInfo(lastTp, evt);
                    break;
                case (int)EnumFddCsfbEvent.MOCSFBProceeding:
                case (int)EnumFddCsfbEvent.MTCSFBProceeding:
                    //结束
                    if (curItem.IsGotBegin && (long)curItem.EvtCsfbRequest["Value7"] == (long)evt["Value7"]) //已经开始，并且value7相等
                    {
                        curItem.AddEndEvtInfo(lastTp, evt);
                    }
                    break;
                case (int)EnumFddCsfbEvent.MOCSFBLTERelease:
                case (int)EnumFddCsfbEvent.MTCSFBLTERelease:
                    //中间事件
                    if (curItem.IsGotBegin && (long)curItem.EvtCsfbRequest["Value7"] == (long)evt["Value7"]) //已经开始，并且value7相等
                    {
                        curItem.AddMidEvtInfo(evt);
                    }
                    break;
                case (int)EnumFddCsfbEvent.TrackAreaUpdateAttempt:
                    //TAC更新请求
                    setUpdateRequest(curItem, evt, "TAC");
                    break;
                case (int)EnumFddCsfbEvent.LocationUpdateRequestLte_W:
                case (int)EnumFddCsfbEvent.LocationUpdateRequestLte_Gsm:
                    //LAC更新请求
                    setUpdateRequest(curItem, evt, "LAC");
                    break;
                case (int)EnumFddCsfbEvent.CellReselection_L2W:
                case (int)EnumFddCsfbEvent.CellReselection_L2G:
                    if (curItem.IsGotBegin && curItem.EvtCsfbProceeding != null)
                    {
                        curItem.TpEnd = lastTp;
                    }
                    break;
            }
        }

        private static void setUpdateRequest(ZTLTECSFBDelayAnaItem curItem, Event evt, string name)
        {
            if (curItem.IsGotBegin && curItem.EvtCsfbProceeding == null)  //有回落请求开始，尚未匹配回落结束事件
            {
                curItem.IsTAUpdate = name;
                curItem.Latitude = evt.Latitude;
                curItem.Longitude = evt.Longitude;
            }
        }

        private void doWithMsg(DTData dtData, TestPoint lastTp, ref ZTLTECSFBDelayAnaItem curItem)
        {
            MasterCom.RAMS.Model.Message msg = (MasterCom.RAMS.Model.Message)dtData;

            if (curItem.IsGotBegin) //已经有开始信息
            {
                curItem.msgList.Add(msg);
                if (msg.ID == (int)EnumFddLteNBCheckMsg.Setup_W || msg.ID == (int)EnumFddLteNBCheckMsg.Setup_Gsm)
                {
                    curItem.TpBeforeSetup = lastTp;
                }
            }
        }
        private void addToResultList(ZTLTECSFBDelayAnaItem csfbItem, ZTLTECSFBDelayFileItem fileItem)
        {
            //没有回落开始，不处理
            if (!csfbItem.IsGotBegin)
            {
                return;
            }

            csfbItem.SN = fileItem.csfbList.Count + 1;

            //开始填充回落前信息
            csfbItem.CellNameBegin = getLTECellNameByEvt(csfbItem.EvtCsfbRequest);

            if (csfbItem.msgList.Count > 0
                && csfbItem.msgList[0].ID == (int)EnumFddLteNBCheckMsg.RRCConnectionRelease)
            {
                csfbItem.MsgNameBegin = "RRCConnectionRelease";
                csfbItem.MsgTimeBegin = csfbItem.msgList[0].TimeStringWithMillisecond;
                getRRCConnReleaseInfo(ref csfbItem);
            }

            //针对回落失败
            if (csfbItem.EvtCsfbProceeding == null)  //没有结束信息
            {
                csfbItem.CsfbResult = "失败";
                csfbItem.CsfbType = "";
                csfbItem.IsTAUpdate = "";

                fileItem.csfbList.Add(csfbItem);
                return;
            }

            //回落成功
            csfbItem.CsfbResult = "成功";
            getCsfbCellInfo(ref csfbItem);

            //没有release信令
            if (csfbItem.EvtCsfbConnRelease == null)
            {
                csfbItem.MsgNameBegin = "无RRCConnectionRelease信令";
                fileItem.csfbList.Add(csfbItem);
                return;
            }

            csfbItem.CsfbDelayTime = (csfbItem.EvtCsfbProceeding.lTimeWithMillsecond - csfbItem.EvtCsfbConnRelease.lTimeWithMillsecond).ToString();

            if (csfbItem.msgList.Count >= 2)
            {
                if (csfbItem.msgList[1].ID == (int)EnumFddLteNBCheckMsg.ServiceRequest_W || csfbItem.msgList[1].ID == (int)EnumFddLteNBCheckMsg.ServiceRequest_Gsm)
                {
                    csfbItem.MsgNameEnd = "CMServiceRequest";
                }
                else if (csfbItem.msgList[1].ID == (int)EnumFddLteNBCheckMsg.PagingResponse)
                {
                    csfbItem.MsgNameEnd = "PagingResponse";
                }
                else if (csfbItem.msgList[1].ID == (int)EnumFddLteNBCheckMsg.Setup_W || csfbItem.msgList[1].ID == (int)EnumFddLteNBCheckMsg.Setup_Gsm)
                {
                    csfbItem.MsgNameEnd = "Setup";
                }

                csfbItem.MsgTimeEnd = csfbItem.msgList[1].TimeStringWithMillisecond;
            }

            fileItem.csfbList.Add(csfbItem);
        }
        private void getCsfbCellInfo(ref ZTLTECSFBDelayAnaItem csfbItem)
        {
            if (csfbItem.TpEnd["lte_fdd_wcdma_frequency"] != null)
            {
                csfbItem.CsfbType = "LTE-W";
                getWCellInfo(ref csfbItem);
            }
            else if (csfbItem.TpEnd["lte_fdd_gsm_SC_BCCH"] != null)  //判断回落后的网络
            {
                csfbItem.CsfbType = "LTE-GSM";
                csfbItem.CellNameEnd = getGSmCellNameByEvt(csfbItem.EvtCsfbProceeding);
            }
        }
        private string getGSmCellNameByEvt(Event evt)
        {
            Cell curCell = CellManager.GetInstance().GetCell(evt.DateTime, (ushort)(int)evt["LAC"], (ushort)(int)evt["CI"]);
            if (curCell != null)
            {
                return curCell.Name;
            }
            else
            {
                if ((int)evt["LAC"] == -1)
                {
                    return "";
                }
                return evt["LAC"].ToString() + "_" + evt["CI"].ToString();
            }
        }
        private void getWCellInfo(ref ZTLTECSFBDelayAnaItem csfbItem)
        {
            Event evt = csfbItem.EvtCsfbProceeding;
            WCell curCell = CellManager.GetInstance().GetWCell(evt.DateTime, (int)evt["LAC"], (int)evt["CI"]);
            if (curCell != null)
            {
                csfbItem.CellNameEnd = curCell.Name;
            }
            else
            {
                if ((int)evt["LAC"] == -1)
                {
                    csfbItem.CellNameEnd = "";
                }
                csfbItem.CellNameEnd = evt["LAC"].ToString() + "_" + evt["CI"].ToString();
            }

            TestPoint tp = csfbItem.TpBeforeSetup;
            if (tp != null)
            {
                csfbItem.CellEndBcch = (int?)tp["lte_fdd_wcdma_frequency"];
                csfbItem.CellEndRxlev = (float?)tp["lte_fdd_wcdma_TotalRSCP"];
                csfbItem.CellEndRxqual = (float?)tp["lte_fdd_wcdma_TotalEc_Io"];
                if (csfbItem.CellEndRxlev != null)
                {
                    csfbItem.IsBackOnBestBcch = "是";

                    float? nRxlevMax = (float?)tp["lte_fdd_wcdma_SNeiRSCP", 0];
                    int? nArfcnMax = (int?)tp["lte_fdd_wcdma_SNeiFreq", 0];
                    getTPInfo(tp, ref nRxlevMax, ref nArfcnMax);

                    setCsfbItemRes(csfbItem, nRxlevMax, nArfcnMax);
                }
            }
        }

        private static void getTPInfo(TestPoint tp, ref float? nRxlevMax, ref int? nArfcnMax)
        {
            if (nRxlevMax != null)
            {
                for (int i = 1; i < 10; i++)
                {
                    float? nRxlevCur = (float?)tp["lte_fdd_wcdma_SNeiRSCP", i];
                    if (nRxlevCur == null)
                    {
                        break;
                    }
                    if (nRxlevCur > nRxlevMax)
                    {
                        nRxlevMax = nRxlevCur;
                        nArfcnMax = (int?)tp["lte_fdd_wcdma_SNeiFreq", i];
                    }
                }
            }
        }

        private void setCsfbItemRes(ZTLTECSFBDelayAnaItem csfbItem, float? nRxlevMax, int? nArfcnMax)
        {
            if (nRxlevMax != null && nRxlevMax > csfbItem.CellEndRxlev)
            {
                csfbItem.IsBackOnBestBcch = "否";
                csfbItem.CellEndRxlevMax = nRxlevMax;
                csfbItem.CellEndBcchBest = nArfcnMax;

                if (nArfcnMax != null)
                {
                    if (csfbItem.ARFCNList.Contains((uint)nArfcnMax))
                    {
                        csfbItem.HasSetBestBcch = "是";
                    }
                    else
                    {
                        csfbItem.HasSetBestBcch = "否";
                    }
                }
            }
            else
            {
                csfbItem.HasSetBestBcch = "是";
                csfbItem.CellEndRxlevMax = csfbItem.CellEndRxlev;
                csfbItem.CellEndBcchBest = csfbItem.CellEndBcch;
            }
        }

        private void getRRCConnReleaseInfo(ref ZTLTECSFBDelayAnaItem csfbItem)
        {
            csfbItem.ARFCNList = new List<uint>();

            MessageWithSource msg = ((MessageWithSource)csfbItem.msgList[0]);
            MessageDecodeHelper.StartDissect(msg.Direction, msg.Source, msg.Source.Length, msg.ID);

            uint redirectedCarrierInfo = 0;
            if (MessageDecodeHelper.GetSingleUInt("lte-rrc.redirectedCarrierInfo", ref redirectedCarrierInfo))
            {
                if ((int)redirectedCarrierInfo == (int)ECarrierInfo.utra_FDD)
                {
                    csfbItem.ReDirectCarrierInfo = "utra_FDD";
                }
                else
                {
                    csfbItem.ReDirectCarrierInfo = redirectedCarrierInfo.ToString();
                }
            }
        }

    }
    enum EnumFddCsfbEvent
    {
        MOCSFBRequest = 3070,
        MOCSFBLTERelease = 3072,
        MOCSFBProceeding = 3076,
        MOCSFBSuccess = 3078,
        MOCSFBFailure = 3080,

        MTCSFBRequest = 3071,
        MTCSFBLTERelease = 3073,
        MTCSFBProceeding = 3077,
        MTCSFBSuccess = 3079,
        MTCSFBFailure = 3081,

        TrackAreaUpdateAttempt = 3171,
        TrackAreaUpdateSuccess = 3172,
        TrackAreaUpdateFail = 3173,

        LocationUpdateRequestLte_W = 3113,
        LocationUpdateRequestLte_Gsm = 3174,
        CellReselection_L2W = 3302,
        CellReselection_L2G = 3300,
    }

    enum EnumFddLteNBCheckMsg
    {
        RRCConnectionReconfigurationComplete = 1093626370,  //0x412f6a02, "RRC Connection Reconfiguration Complete"
        RRCConnectionReconfiguration = 1093625860,          //0x412f6804, "RRC Connection Reconfiguration"
        SystemInformationBlockType1 = 1093625089,           //0x412f6501, "System Information Block Type 1"

        RRCConnectionRelease = MessageManager.LTE_RRC_RRC_Connection_Release,

        PagingResponse = 1575,      //W、TD和GSM相同
        ServiceRequest_W = 1899627812,
        Setup_W = 1899627269,
        ServiceRequest_Gsm = 1899627812,
        Setup_Gsm = 1899627269,
    }
}
