﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.Model.Interface
{
    public partial class SelectReportDlg : BaseForm
    {
        public SelectReportDlg()
        {
            InitializeComponent();

            List<EventStatFilter> evtFiltersList = EventStatFilterFactory.GetEventStatFilters();
            cbxEventFilterSelList.Items.Clear();
            foreach(EventStatFilter filter in evtFiltersList)
            {
                cbxEventFilterSelList.Items.Add(filter);
            }
            if(cbxEventFilterSelList.Items.Count>0)
            {
                cbxEventFilterSelList.SelectedIndex = 0;
            }
            cbxAdvEvtFilter.Checked = false;
            cbxEventFilterSelList.Enabled = false;
            chkCaleGridPercent.Checked = false;

            toolTip1.SetToolTip(chkBlanceCale, "匀化模式是为减小部分栅格数据对最终查询结果造成的过大影响，而设立的一处理过程。"
            +"\n 因为可能有部分栅格加载了多次相似的数据，不能很好地反应实际的总体数据情况。\n例如：在计算车速时，有车多次重复经过某几个栅格上，而且速度很快，会拉高总体平均车速。");

            cbxMomt.Visible = true;
        }

        private List<ReportStyle> rptStyleList = null;
        internal void FillCurrentReports(ref List<ReportStyle> rptStyleList)
        {
            this.rptStyleList = rptStyleList;
            reportFilter.initReportPicker(rptStyleList);
        }

        internal EventStatFilter GetEventStatFilter()
        {
            if(cbxAdvEvtFilter.Checked)
            {
                return cbxEventFilterSelList.SelectedItem as EventStatFilter;
            }
            return null;
        }

        internal ReportStyle GetSelectedReport(out bool allContent)
        {
            allContent = cbxAllParam.Checked;
            return reportFilter.SelectedReport; 
        }

        internal bool GetCaleGridPercent()
        {
            if (chkCaleGridPercent.Checked)
            {
                return true;
            }
            return false;
        }

        internal bool GetBlanceCale()
        {
            if (chkBlanceCale.Checked)
            {
                return true;
            }
            return false;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            bool tmp;
            if(GetSelectedReport(out tmp)!=null)
            {
                this.DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show(this, "请选择报表！", "选择");
            }
        }

        private void cbxAllParam_CheckedChanged(object sender, EventArgs e)
        {
            lbNoteAll.Visible = cbxAllParam.Checked;
        }

        private void cbxAdvEvtFilter_CheckedChanged(object sender, EventArgs e)
        {
            cbxEventFilterSelList.Enabled = cbxAdvEvtFilter.Checked;
        }

        private void chkCaleGridPercent_CheckedChanged(object sender, EventArgs e)
        {
            lblNoteGridPercent.Visible = chkCaleGridPercent.Checked;
        }

        public bool GetMomtFlag()
        {
            return cbxMomt.Checked;
        }

        private void cbxMomt_CheckedChanged(object sender, EventArgs e)
        {
            label2.Visible = cbxMomt.Checked;
        }

        private void chkBlanceCale_CheckedChanged(object sender, EventArgs e)
        {
            label3.Visible = chkBlanceCale.Checked;
        }

        private void lnkReloadRpt_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            reportFilter.initReportPicker(rptStyleList);
        }
    }
}