﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTRegionKQISQLQuery : DIYSQLBase
    {
        public ZTRegionKQISQLQuery(MainModel mainModel)
            : base(mainModel)
        { 
            
        }
        public string ReportName { get; set; }
        public string GridName { get; set; }
        public string BeginDate { get; set; }
        public string EndDate { get; set; }

        private readonly List<RegionKQIItem> regionkqis = new List<RegionKQIItem>();
        public List<RegionKQIItem> RegionKQIs
        {
            get { return regionkqis; }
        }
        protected override string getSqlTextString()
        {
            return "select KQIName,KQIScore from Complain_Sys..tb_QoE_area_KQI_score where keyName='" + GridName + "' and beginTime='" + BeginDate + "' and endTime='" + EndDate + "' and reportName='" + ReportName + "'";
        }

        protected override MasterCom.RAMS.Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            E_VType[] vtypes = new E_VType[2];
            vtypes[0] = E_VType.E_String;
            vtypes[1] = E_VType.E_Float;
            return vtypes;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            regionkqis.Clear();
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    RegionKPIItem kqiitem = new RegionKPIItem();
                    kqiitem.IndicatorName = package.Content.GetParamString();
                    kqiitem.Score = package.Content.GetParamFloat().ToString();
                    this.regionkqis.Add(kqiitem);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    break;
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    break;
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public override string Name
        {
            get { return ""; }
        }
    }
}
