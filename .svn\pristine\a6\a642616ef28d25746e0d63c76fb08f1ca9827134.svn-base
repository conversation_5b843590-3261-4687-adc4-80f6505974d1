﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;

using MasterCom.Util.UiEx;
using MasterCom.RAMS.Model;
using Excel = Microsoft.Office.Interop.Excel;
using MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ;

namespace MasterCom.RAMS.ZTFunc.ZTLteTestAcceptance
{
    class LteTestAcceptManager
    {
        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private Dictionary<string, int> sectorIDDic = null;

        private string btsNameByFile;

        protected string GetBtsName(string fileName)
        {
            string btsNameTmp = "";
            string[] names = fileName.Split('_');
            if (names.Length >3)
            {
                btsNameTmp = names[2];
            }
            return btsNameTmp;
        }

        public void SetAcceptCond(LteTestAcceptCondition cond)
        {
            this.acceptCond = cond;

            this.acceptorList = new List<LteTestAcceptBase>()
            {
                //锁频测试文件一定存在,故优先处理
                new AcpDivulgeRate(),
                new AcpFtpDownload(),
                new AcpFtpUpload(),
                new AcpAccRate(),
                new AcpRrcRate(),
                new AcpCsfbRate(),
                new AcpHandoverRate(),
                new AcpErabRate(),
                new AcpDivulge2Rate(),
                new AcpLeveling(),
                new AcpHomePage(),
                //new AcpLostFloor(),
                new AcpHomePCI(),
                new AcpCoverePicByApp(),
                new AcpLevelingByAppSP(),
                new AcpPerformanceByApp(),
                new AcpVolteVoiceMo(),
                new AcpVolteVoiceMt()
            };
            sectorIDDic = new Dictionary<string, int>();

            DiyQueryFddDBSetting.GetInstance().Query();
        }

        protected  string GetPCI(string fileName)
        {
            int index = fileName.IndexOf("PCI") + 3;
            int endIndex = -1;
            string PCI = "";
            for (int i = index; i < fileName.Length; i++)
            {
                if (fileName[i] > '9' || fileName[i] < '0')
                {
                    endIndex = i;
                    break;
                }
            }

            int length = -1;
            if (endIndex > index)
            {
                length = endIndex - index;
            }
            if (length > 0)
            {
                PCI = fileName.Substring(index, length);
            }
            return PCI;
        }

        /// <summary>
        /// 当前分析文件对应的的小区
        /// </summary>
        public LTECell CurLteCell { get; set; }
        /// <summary>
        /// 当前小区对应的基站
        /// </summary>
        public LTEBTS CurLteBts { get; set; }
        /// <summary>
        /// 当前分析基站的小区数
        /// </summary>
        public int CurLteCellCount { get; set; }
        public void AnalyzeFile(MasterCom.RAMS.Model.FileInfo fileInfo, DTFileDataManager fileManager)
        {
            try
            {
                btsNameByFile = GetBtsName(fileInfo.Name);
                string PCI = GetPCI(fileInfo.Name);
                if (!sectorIDDic.ContainsKey(PCI))
                {
                    int sectorID = sectorIDDic.Count;
                    sectorIDDic[PCI] = sectorID;
                }

                CurLteCell = GetTargetCell(fileManager, PCI);
                if (CurLteCell == null && !fileInfo.Name.Contains("扫频"))
                {
                    log.Error(string.Format("文件[{0}]没有找到小区", fileInfo.Name));
                    return;
                }
                else if (CurLteCell != null)//扫频泄露文件关联不到小区
                {
                    CurLteBts = CurLteCell.BelongBTS;
                    CurLteCellCount = CurLteBts.Cells.Count;
                }

                foreach (LteTestAcceptBase acp in acceptorList)
                {
                    if (acp.BtsNames.Count > 1)
                    {
                        return;
                    }
                    acp.Init(CurLteCell, btsNameByFile);
                    acp.AnalyzeFile(fileInfo, fileManager, PCI, sectorIDDic[PCI]);
                }
                //AcpLeveling acpLeveling = (AcpLeveling)acceptorList[9];
                //AcpHomePage acpHome = (AcpHomePage)acceptorList[10];
                //AcpLostFloor lostFloor = (AcpLostFloor)acceptorList[11];
                //lostFloor.SetValue(acpHome.FloorList, acpLeveling.FileCount, acpHome.MaxFloor, acpHome.MinFloor, fileInfo);
                AcpHomePCI homePCI = (AcpHomePCI)acceptorList[11];
                homePCI.SetValue(sectorIDDic, fileInfo);
            }
            catch (Exception ex)
            {
                log.Error(string.Format("文件[{0}]分析异常", fileInfo.Name), ex);
                Clear();
                throw;
            }
        }

        public void DoWorkAfterAnalyze()
        {
            try
            {
                dealFinalAppData();

                CreateFileForBts(sectorIDDic);
            }
            finally
            {
                Clear();
            }
        }

        private void dealFinalAppData()
        {
            //1.判断所有性能测试指标是否有结果
            AcpPerformanceByApp performanceAcpApp = (AcpPerformanceByApp)acceptorList[14];
            if (performanceAcpApp.AllCellPerformanceResult != null)
            {
                foreach (LteTestAcceptBase acp in acceptorList)
                {
                    bool haveRes = acp.JudgeHaveResult();
                    if (!haveRes)
                    {
                        //对应的性能测试指标使用App数据填充
                        acp.ResetResult(performanceAcpApp, sectorIDDic);
                    }
                }
            }

            //2.平层测试为log分析数据加上App数据
            AcpLeveling levelingAcp = (AcpLeveling)acceptorList[9];
            AcpLevelingByAppSP levelingAcpApp = (AcpLevelingByAppSP)acceptorList[13];
            if (levelingAcpApp.LevelingResultList != null)
            {
                levelingAcp.ResetResult(levelingAcpApp, sectorIDDic);
            }
        }

        private void CreateFileForBts(Dictionary<string, int> btsDic)
        {
            bool hasValidRes = false;
            foreach (LteTestAcceptBase acp in acceptorList)
            {
                if (acp.BtsNames.Count > 1)
                {
                    throw (new Exception("所选文件列表中包含多个基站文件,只能分析一个基站文件!"));
                }
                else if (acp.BtsNames.Count == 1)
                {
                    hasValidRes = true;
                }
            }

            if (!hasValidRes)
            {
                throw (new Exception("没有数据可以导出,请检查工参和文件信息!"));
            }

            int sectorCount = btsDic.Count;
            string targetFile = GetTargetFile(btsNameByFile, sectorCount, acceptCond.SaveFolder);
            if (!string.IsNullOrEmpty(targetFile))
            {
                exportFile(btsDic, targetFile);
            }

            saveCurReport(btsDic, btsNameByFile, sectorCount);
        }

        private void exportFile(Dictionary<string, int> btsDic, string targetFile)
        {
            Excel.Application xlApp = null;
            try
            {
                WaitTextBox.Text = "正在导出Excel...";
                xlApp = new Excel.Application();
                xlApp.Visible = false;
                Excel.Workbook eBook = xlApp.Workbooks.Open(targetFile,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing);

                foreach (LteTestAcceptBase acp in acceptorList)
                {
                    acp.FillResult(btsNameByFile, eBook, btsDic);
                }

                eBook.Save();
                eBook.Close(Type.Missing, Type.Missing, Type.Missing);
            }
            finally
            {
                if (xlApp != null)
                {
                    xlApp.Quit();
                }
            }
        }

        protected virtual void saveCurReport(Dictionary<string, int> btsDic, string btsName, int cellCount)
        {
            string path = getPath();
            if (string.IsNullOrEmpty(path))
            {
                return;
            }

            string targetFile = GetTargetFile(btsName, cellCount, path);
            if (!string.IsNullOrEmpty(targetFile))
            {
                exportFile(btsDic, targetFile);
            }
        }

        protected virtual string getPath()
        {
            return Singleton<TddIndoorStationAcceptConfigHelper>.Instance.GetCurSavePath();
        }

        private void Clear()
        {
            foreach (LteTestAcceptBase acp in acceptorList)
            {
                acp.Clear();
            }
        }

        private LTECell GetTargetCell(DTFileDataManager fileManager, string pci)
        {
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                List<LTECell> cells = StationAcceptCellHelper_XJ.Instance.GetLTECellList(tp);
                foreach (LTECell lteCell in cells)
                {
                    if (lteCell != null && lteCell.BelongBTS.Type == LTEBTSType.Indoor 
                        && lteCell.Name.Contains(btsNameByFile) && lteCell.PCI.ToString() == pci)
                    {
                        return lteCell;
                    }
                }
            }
            return null;
        }

        public static string GetTargetFile(string btsName, int sectorCount, string saveFolder)
        {
            if (sectorCount >= 9)
            {
                throw (new Exception(string.Format("基站{0}PCI小区超过9个，不支持报告导出", btsName)));
            }
            
            string templateFile = "LTE室分单站验证模板表.xlsx";
            templateFile = Path.Combine(workDir, templateFile);

            string targetFile = string.Format("LTE室分单站验证_{0}.xlsx", btsName);
            targetFile = Path.Combine(saveFolder, targetFile);
            if (File.Exists(targetFile))
            {
                File.Delete(targetFile);
            }
            File.Copy(templateFile, targetFile);
            return targetFile;
        }

        static string workDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/LteTestAcceptance");
        private LteTestAcceptCondition acceptCond = null;
        private List<LteTestAcceptBase> acceptorList = null;
    }
}
