﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class HandoverSequenceInfoForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.treeViewHOSQFiles = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFileName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnIdealHOSequence = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnIdealHOLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnIdealHOCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnIdealHOLastDuration = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHOSequence = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHOLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHOCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHOLastDuration = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemCollapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.treeViewHOSQFiles)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeViewHOSQFiles
            // 
            this.treeViewHOSQFiles.AllColumns.Add(this.olvColumnStatSN);
            this.treeViewHOSQFiles.AllColumns.Add(this.olvColumnFileName);
            this.treeViewHOSQFiles.AllColumns.Add(this.olvColumnIdealHOSequence);
            this.treeViewHOSQFiles.AllColumns.Add(this.olvColumnIdealHOLAC);
            this.treeViewHOSQFiles.AllColumns.Add(this.olvColumnIdealHOCI);
            this.treeViewHOSQFiles.AllColumns.Add(this.olvColumnIdealHOLastDuration);
            this.treeViewHOSQFiles.AllColumns.Add(this.olvColumnHOSequence);
            this.treeViewHOSQFiles.AllColumns.Add(this.olvColumnHOLAC);
            this.treeViewHOSQFiles.AllColumns.Add(this.olvColumnHOCI);
            this.treeViewHOSQFiles.AllColumns.Add(this.olvColumnHOLastDuration);
            this.treeViewHOSQFiles.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnFileName,
            this.olvColumnIdealHOSequence,
            this.olvColumnIdealHOLAC,
            this.olvColumnIdealHOCI,
            this.olvColumnIdealHOLastDuration,
            this.olvColumnHOSequence,
            this.olvColumnHOLAC,
            this.olvColumnHOCI,
            this.olvColumnHOLastDuration});
            this.treeViewHOSQFiles.ContextMenuStrip = this.contextMenuStrip1;
            this.treeViewHOSQFiles.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeViewHOSQFiles.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeViewHOSQFiles.FullRowSelect = true;
            this.treeViewHOSQFiles.GridLines = true;
            this.treeViewHOSQFiles.HeaderWordWrap = true;
            this.treeViewHOSQFiles.IsNeedShowOverlay = false;
            this.treeViewHOSQFiles.Location = new System.Drawing.Point(0, 0);
            this.treeViewHOSQFiles.Name = "treeViewHOSQFiles";
            this.treeViewHOSQFiles.OwnerDraw = true;
            this.treeViewHOSQFiles.ShowGroups = false;
            this.treeViewHOSQFiles.Size = new System.Drawing.Size(1199, 499);
            this.treeViewHOSQFiles.TabIndex = 9;
            this.treeViewHOSQFiles.UseCompatibleStateImageBehavior = false;
            this.treeViewHOSQFiles.View = System.Windows.Forms.View.Details;
            this.treeViewHOSQFiles.VirtualMode = true;
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 250;
            // 
            // olvColumnIdealHOSequence
            // 
            this.olvColumnIdealHOSequence.HeaderFont = null;
            this.olvColumnIdealHOSequence.HeaderForeColor = System.Drawing.Color.Black;
            this.olvColumnIdealHOSequence.Text = "理想序列小区名称";
            this.olvColumnIdealHOSequence.Width = 200;
            // 
            // olvColumnIdealHOLAC
            // 
            this.olvColumnIdealHOLAC.HeaderFont = null;
            this.olvColumnIdealHOLAC.HeaderForeColor = System.Drawing.Color.Black;
            this.olvColumnIdealHOLAC.Text = "理想序列LAC";
            this.olvColumnIdealHOLAC.Width = 80;
            // 
            // olvColumnIdealHOCI
            // 
            this.olvColumnIdealHOCI.HeaderFont = null;
            this.olvColumnIdealHOCI.HeaderForeColor = System.Drawing.Color.Black;
            this.olvColumnIdealHOCI.Text = "理想序列CI";
            this.olvColumnIdealHOCI.Width = 80;
            // 
            // olvColumnIdealHOLastDuration
            // 
            this.olvColumnIdealHOLastDuration.HeaderFont = null;
            this.olvColumnIdealHOLastDuration.Text = "理想序列时长";
            this.olvColumnIdealHOLastDuration.Width = 80;
            // 
            // olvColumnHOSequence
            // 
            this.olvColumnHOSequence.HeaderFont = null;
            this.olvColumnHOSequence.HeaderForeColor = System.Drawing.Color.Black;
            this.olvColumnHOSequence.Text = "实际序列小区名称";
            this.olvColumnHOSequence.Width = 200;
            // 
            // olvColumnHOLAC
            // 
            this.olvColumnHOLAC.HeaderFont = null;
            this.olvColumnHOLAC.HeaderForeColor = System.Drawing.Color.Black;
            this.olvColumnHOLAC.Text = "实际序列LAC";
            this.olvColumnHOLAC.Width = 80;
            // 
            // olvColumnHOCI
            // 
            this.olvColumnHOCI.HeaderFont = null;
            this.olvColumnHOCI.HeaderForeColor = System.Drawing.Color.Black;
            this.olvColumnHOCI.Text = "实际序列CI";
            this.olvColumnHOCI.Width = 80;
            // 
            // olvColumnHOLastDuration
            // 
            this.olvColumnHOLastDuration.HeaderFont = null;
            this.olvColumnHOLastDuration.Text = "实际序列时长";
            this.olvColumnHOLastDuration.Width = 80;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExpandAll,
            this.ToolStripMenuItemCollapsAll,
            this.ToolStripMenuItemExport});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(137, 70);
            // 
            // ToolStripMenuItemExpandAll
            // 
            this.ToolStripMenuItemExpandAll.Name = "ToolStripMenuItemExpandAll";
            this.ToolStripMenuItemExpandAll.Size = new System.Drawing.Size(136, 22);
            this.ToolStripMenuItemExpandAll.Text = "全部展开";
            this.ToolStripMenuItemExpandAll.Click += new System.EventHandler(this.ToolStripMenuItemExpandAll_Click);
            // 
            // ToolStripMenuItemCollapsAll
            // 
            this.ToolStripMenuItemCollapsAll.Name = "ToolStripMenuItemCollapsAll";
            this.ToolStripMenuItemCollapsAll.Size = new System.Drawing.Size(136, 22);
            this.ToolStripMenuItemCollapsAll.Text = "全部收缩";
            this.ToolStripMenuItemCollapsAll.Click += new System.EventHandler(this.ToolStripMenuItemCollapsAll_Click);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(136, 22);
            this.ToolStripMenuItemExport.Text = "导出到xls...";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // HandoverSequenceInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1199, 499);
            this.Controls.Add(this.treeViewHOSQFiles);
            this.Name = "HandoverSequenceInfoForm";
            this.Text = "切换序列对比列表";
            ((System.ComponentModel.ISupportInitialize)(this.treeViewHOSQFiles)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeViewHOSQFiles;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnHOSequence;
        private BrightIdeasSoftware.OLVColumn olvColumnIdealHOSequence;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExpandAll;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemCollapsAll;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private BrightIdeasSoftware.OLVColumn olvColumnHOLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnHOCI;
        private BrightIdeasSoftware.OLVColumn olvColumnIdealHOLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnIdealHOCI;
        private BrightIdeasSoftware.OLVColumn olvColumnIdealHOLastDuration;
        private BrightIdeasSoftware.OLVColumn olvColumnHOLastDuration;
    }
}