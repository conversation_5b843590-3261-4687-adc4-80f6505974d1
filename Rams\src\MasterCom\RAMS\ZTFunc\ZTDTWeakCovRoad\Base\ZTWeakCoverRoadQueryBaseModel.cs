﻿using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class ZTWeakCoverRoadQueryBaseModel<T> : DIYAnalyseByFileBackgroundBase where T : new()
    {
        //基础配置属性
        protected abstract string name { get; }
        protected abstract int type { get; }
        protected abstract int funcId { get; }
        protected abstract int subfuncId { get; }
        //渲染指标
        public abstract string themeName { get; }

        //参数类型
        public string tpRSRP { get; set; }
        public string tpSINR { get; set; }
        public string tpTac { get; set; }
        public string tpSpeed { get; set; }
        public string tpAppType { get; set; }
        public string tpStr { get; set; }
        public string tpNCell_EARFCN { get; set; }
        public string tpNCell_PCI { get; set; }

        //后台配置
        protected bool saveTestPoints = true;

        private static T instance { get; set; }
        public static T GetInstance()
        {
            if (instance == null)
            {
                instance = new T();
            }
            return instance;
        }

        public override string Name
        {
            get { return name; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(type, funcId, subfuncId, name);
        }

        ////存放弱覆盖采样点的矩形区域，用于对区域内的小区进行筛选，避免遍历全部小区
        protected DbRect sampleRect = new DbRect(-999, -999, -999, -999);

        protected abstract ICell getNBCell(TestPoint testPoint, int index);
        protected abstract ICell getMainCell(TestPoint testPoint);

        protected override bool getCondition()
        {
            setRoadCond();
            return true;
        }

        protected WeakCoverRoadConditionBase weakCondition = new WeakCoverRoadConditionBase();

        public virtual void setRoadCond()
        {
            PercentRoadCondition roadCond = new PercentRoadCondition(weakCondition.MinWeakPointPercent / 100, OnOneRoadComplete);
            roadCond.MinLength = weakCondition.MinCoverRoadDistance;
            roadCond.MaxDistanceGap = weakCondition.MaxTPDistance;

            this.roadBuilder = new PercentRoadBuilder(roadCond);
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            weakCoverList = new List<WeakCoverRoadResultBase>();
        }

        private double curWeakPercent = 0;
        private double duration = 0;
        protected void OnOneRoadComplete(object sender, PercentRoadItem roadItem)
        {
            curWeakPercent = Math.Round(roadItem.ValidPercent * 100, 2);
            duration = roadItem.Duration;
            if (curWeakPercent < this.weakCondition.MinWeakPointPercent)
            {
                return;
            }
            tps = roadItem.TestPoints;
            addToReportInfo(tps);
        }

        public virtual void addToReportInfo(List<TestPoint> testPointList)
        {
            if (testPointList.Count == 0)
            {
                return;
            }

            WeakCoverRoadResultBase weakCover = null;
            TestPoint prePoint = null;//前一点
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];

                int inNbCellIndex = -1;
                Dictionary<float, ICell> nbCell = getNbMaxRSRPCellDic(testPoint, ref inNbCellIndex);
                double dis = 0;
                if (weakCover == null)
                {
                    //弱覆盖开始
                    weakCover = initResult();
                }
                else
                {
                    //上一点为弱覆盖点
                    dis = prePoint.Distance2(testPoint);
                }

                setWeakCoverRoadResult(testPoint, weakCover, dis, inNbCellIndex, nbCell);
                prePoint = testPoint;
            }

            if (weakCover != null)
            {
                weakCover.CityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                weakCover.Duration = duration;
                weakCover.CalulateFinalRes();
                saveWeakCoverInfo(ref weakCover);
            }
        }

        protected virtual WeakCoverRoadResultBase initResult()
        {
            return new WeakCoverRoadResultBase();
        }

        protected virtual void setWeakCoverRoadResult(TestPoint testPoint, WeakCoverRoadResultBase weakCover, double dis, int inNbCellIndex, Dictionary<float, ICell> nbCellDic)
        {
            float? nbRSRP = getNbMaxRSRP(testPoint);
            float? sinr = getSinr(testPoint);
            float? rsrp = getRsrp(testPoint);
            short? appType = getAppType(testPoint);
            double? speed = getSpeed(testPoint);

            int? tac = getTac(testPoint);
            ICell cell = getMainCell(testPoint);

            weakCover.Add(rsrp, nbRSRP, sinr, dis, testPoint);
            weakCover.AddSpeed(appType, speed);
            weakCover.AddOtherTPInfo(testPoint);
            weakCover.AddProblemInfo(tac, cell, weakCover.LacciList_plan, weakCover.CellNames_plan);
            weakCover.JudeNbMaxRsrpCelll(nbCellDic, testPoint, tpNCell_EARFCN, tpNCell_PCI, inNbCellIndex);
        }

        protected List<WeakCoverRoadResultBase> weakCoverList = null;

        protected virtual void saveWeakCoverInfo(ref WeakCoverRoadResultBase info)
        {
            info.CityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            info.Duration = duration;

            if (!weakCondition.MatchMinWeakCoverDuration(info.Duration) 
                || !weakCondition.MatchMinWeakCoverDistance(info.Distance))
            {
                info = null;
                return;
            }
            if (weakCoverList == null)
            {
                weakCoverList = new List<WeakCoverRoadResultBase>();
            }
            if (!weakCoverList.Contains(info))
            {
                info.SN = weakCoverList.Count + 1;
                info.WeakPointPercent = curWeakPercent;
                info.SetFinalData(areaID, areaTypeID);
                weakCoverList.Add(info);
            }

            expandSampleRect(info.TestPoints);

            info = null;
        }

        protected Dictionary<float, ICell> getNbMaxRSRPCellDic(TestPoint testPoint, ref int index)
        {
            Dictionary<float, ICell> nbCellDic = new Dictionary<float, ICell>();
            ICell nbCell;
            float? max = -999;
            for (int i = 0; i < 10; i++)
            {
                float? n = (float?)testPoint[tpStr, i];
                if (n == null || n < -141 || n > 25)
                {
                    continue;
                }
                if (n > max)
                {
                    max = n;
                    index = i;
                }
            }
            if (max > -999)
            {
                nbCell = getNBCell(testPoint, index);
                if (nbCell == null)
                {
                    nbCell = new LTECell();
                }
                nbCellDic.Add((float)max, nbCell);
            }
            return nbCellDic;
        }

        protected virtual float? getNbMaxRSRP(TestPoint testPoint)
        {
            float? max = null;
            for (int i = 0; i < 10; i++)
            {
                float? n = (float?)testPoint[tpStr, i];
                if (n == null || n < -141 || n > 25)
                {
                    continue;
                }
                max = max == null ? n : Math.Max((float)max, (float)n);
            }
            return max;
        }

        protected virtual float? getRsrp(TestPoint tp)
        {
            float? rsrp = (float?)tp[tpRSRP];
            if (rsrp > -200 && rsrp < 100)
            {
                return rsrp;
            }
            return null;
        }

        protected virtual float? getSinr(TestPoint tp)
        {
            float? sinr = (float?)tp[tpSINR];
            if (sinr > -50 && sinr < 50)
            {
                return sinr;
            }
            return null;
        }

        protected virtual short? getAppType(TestPoint tp)
        {
            return (short?)tp[tpAppType];
        }

        protected virtual double? getSpeed(TestPoint tp)
        {
            return (double?)tp[tpSpeed];
        }

        protected virtual int? getTac(TestPoint tp)
        {
            return (int?)(ushort?)tp[tpTac];
        }

        protected void expandSampleRect(List<TestPoint> sampleList)
        {
            foreach (TestPoint tp in sampleList)
            {
                if (sampleRect.x2 == -999 || tp.Longitude > sampleRect.x2)
                {
                    sampleRect.x2 = tp.Longitude;
                }
                if (sampleRect.x1 == -999 || tp.Longitude < sampleRect.x1)
                {
                    sampleRect.x1 = tp.Longitude;
                }
                if (sampleRect.y2 == -999 || tp.Latitude > sampleRect.y2)
                {
                    sampleRect.y2 = tp.Latitude;
                }
                if (sampleRect.y1 == -999 || tp.Latitude < sampleRect.y1)
                {
                    sampleRect.y1 = tp.Latitude;
                }
            }
        }

        public List<TestPoint> tps { get; set; } = new List<TestPoint>();
        protected PercentRoadBuilder roadBuilder;

        protected int areaID = -1;
        protected int areaTypeID = -1;
        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    tps = new List<TestPoint>();
                    areaID = fileDataManager.GetFileInfo().AreaID;
                    areaTypeID = fileDataManager.GetFileInfo().AreaTypeID;

                    foreach (TestPoint tp in fileDataManager.TestPoints)
                    {
                        if (isTPInRegion(tp))
                        {
                            roadBuilder.AddPoint(tp, this.isValidTestPoint(tp));
                        }
                    }
                    this.roadBuilder.StopRoading();
                }
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        protected virtual bool isTPInRegion(TestPoint tp)
        {
            if (condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude))
            {
                return true;
            }
            return false;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            bool ret = false;
            try
            {
                ret = JudgeWeakTP(testPoint);
            }
            catch
            {
                //continue
            }
            return ret;
        }

        protected virtual bool JudgeWeakTP(TestPoint tp)
        {
            return true;
        }

        #region 处理结果,判断问题是优化问题还是规划问题
        protected override void getResultsAfterQuery()
        {
            doSomethingAfterAna();
        }

        protected virtual void doSomethingAfterAna()
        {
            List<ICell> cellInLst = getCellInGeometry();

            foreach (WeakCoverRoadResultBase info in weakCoverList)
            {
                foreach (TestPoint testPoint in info.TestPoints)
                {
                    bool isPlanProblem = judgePlanProblem(cellInLst, testPoint);

                    int? lac = (int?)(ushort?)testPoint[tpTac];
                    ICell mainCell = getMainCell(testPoint);
                    if (isPlanProblem)
                    {
                        info.TestPointCount_Plan++;
                    }
                    else
                    {
                        info.TestPointCount_Opt++;
                        info.AddProblemInfo(lac, mainCell, info.LacciList_opt, info.CellNames_opt);
                    }

                    if (saveTestPoints)
                    {
                        MainModel.DTDataManager.Add(testPoint);
                    }
                }
                info.CalulateFinalRes();
            }
        }

        protected bool judgePlanProblem(List<ICell> cellInLst, TestPoint testPoint)
        {
            bool isPlanProblem = true;   //判断是优化问题，还是规划问题
            foreach (ICell cell in cellInLst)
            {
                double dis = getDistance(testPoint, cell);
                bool inRange = MathFuncs.JudgePoint(cell.Longitude, cell.Latitude, testPoint.Longitude, testPoint.Latitude, (int)cell.Direction, weakCondition.MaxSampleCellAngle);

                if (dis <= weakCondition.MaxSampleCellDistance && inRange)
                {
                    isPlanProblem = false;  //采样点在小区的方向角内，是优化问题
                    break;
                }
            }

            return isPlanProblem;
        }

        protected List<ICell> getCellInGeometry()
        {
            ////外扩sampleRect
            double offset = (double)weakCondition.MaxSampleCellDistance / 100000;
            sampleRect.x1 -= offset;
            sampleRect.x2 += offset;
            sampleRect.y1 -= offset;
            sampleRect.y2 += offset;

            List<ICell> cellInLst = getCellInThisRect();
            return cellInLst;
        }

        protected virtual List<ICell> getCellInThisRect()
        {
            return new List<ICell>();
        }

        protected virtual double getDistance(TestPoint testPoint, ICell cell)
        {
            return 0;
        }
        #endregion

        protected override void fireShowForm()
        {
            throw (new Exception("需要实现结果界面"));
        }
    }

    public class WeakCoverRoadConditionBase
    {
        public float MaxRSRP { get; set; } = -105;
        public double MaxTPDistance { get; set; } = 50;
        public double MinCoverRoadDistance { get; set; } = 50;
        public int MaxSampleCellDistance { get; set; } = 300;
        public int MaxSampleCellAngle { get; set; } = 60;

        public double MinWeakPointPercent { get; set; } = 100;

        public virtual bool MatchMinWeakCoverDistance(double distance)
        {
            return distance >= MinCoverRoadDistance;
        }

        public virtual bool MatchMinWeakCoverDuration(double duration)
        {
            return true;
        }

        public virtual bool JudgeValid(TestPoint tp)
        {
            return true;
        }
    }

    public class WeakCoverRoadResultBase
    {
        public List<TestPoint> TestPoints { get; protected set; } = new List<TestPoint>();

        public int SN { get; set; }
        public string CityName { get; set; }
        public string GridName { get; set; } = string.Empty;
        public string FileName { get; protected set; } = string.Empty;
        public string StartTime { get; protected set; }
        public double MidLng { get; protected set; }
        public double MidLat { get; protected set; }
        public string RoadName { get; set; } = string.Empty;
        public double WeakPointPercent { get; set; }
        public double Distance { get; set; }
        public double Duration { get; set; }
        public int TestPointCount { get; protected set; }

        public DataInfo RsrpData { get; set; } = new DataInfo();
        public DataInfo SinrData { get; set; } = new DataInfo();
        public DataInfo NbRsrpData { get; set; } = new DataInfo();
        public SpeedInfo SpeedData { get; set; } = new SpeedInfo();

        public string MotorWay { get; set; } = "";
        public string AreaName { get; set; } = string.Empty;
        public string AreaAgentName { get; set; } = string.Empty;

        private readonly List<ICell> nbCellList = new List<ICell>();

        public int IMaxNBCellCount
        {
            get { return nbCellList.Count; }
        }

        /// <summary>
        /// 最强小区名
        /// </summary>
        public string StrMaxRsrpCellName { get; set; } = "";

        private string strMaxNBCellMinDistince;
        /// <summary>
        /// 最强邻区与中心的距离
        /// </summary>
        public string StrMaxNBCellMinDistince
        {
            get
            {
                if (strMaxNBCellMinDistince == null)
                {
                    return "-";
                }
                return strMaxNBCellMinDistince;
            }
            set { strMaxNBCellMinDistince = value; }
        }

        public int TestPointCount_Opt { get; set; }
        public float TestPointRate_Opt { get; protected set; }

        public int TestPointCount_Plan { get; set; }
        public float TestPointRate_Plan { get; protected set; }

        public List<string> CellNames_opt { get; set; } = new List<string>();
        public string CellName_opt { get; protected set; }

        public List<string> LacciList_opt { get; set; } = new List<string>();
        public string LACCIs_opt { get; protected set; }

        public List<string> CellNames_plan { get; set; } = new List<string>();
        public string CellName_plan { get; protected set; }

        public List<string> LacciList_plan { get; set; } = new List<string>();
        public string LACCIs_plan { get; protected set; }

        protected string setListTostr(List<string> listString)
        {
            StringBuilder sb = new StringBuilder();
            foreach (string str in listString)
            {
                if (sb.Length > 0)
                {
                    sb.Append(" | ");
                }
                sb.Append(str);
            }
            return sb.ToString();
        }

        internal void Add(float? rsrp, float? nbMaxRsrp, float? sinr, double distance, TestPoint testPoint)
        {
            if (rsrp != null)
            {
                RsrpData.Total += (float)rsrp;
                RsrpData.Min = Math.Min((float)rsrp, RsrpData.Min);
                RsrpData.Max = Math.Max((float)rsrp, RsrpData.Max);
            }
            if (sinr != null)
            {
                SinrData.ValidCnt++;
                SinrData.Total += (float)sinr;
                SinrData.Min = Math.Min((float)sinr, SinrData.Min);
                SinrData.Max = Math.Max((float)sinr, SinrData.Max);
            }
            if (nbMaxRsrp != null)
            {
                NbRsrpData.ValidCnt++;
                NbRsrpData.Total += (float)nbMaxRsrp;
                NbRsrpData.Min = Math.Min((float)nbMaxRsrp, NbRsrpData.Min);
                NbRsrpData.Max = Math.Max((float)nbMaxRsrp, NbRsrpData.Max);
            }

            Distance += distance;
            TestPoints.Add(testPoint);
        }

        public void AddSpeed(short? type, double? speed)
        {
            if (speed != null)
            {
                SpeedData.ValidCnt++;
                SpeedData.Total += (double)speed;
            }
        }

        public virtual void AddOtherTPInfo(TestPoint testPoint)
        { 
        
        }

        public virtual void AddProblemInfo(int? lac, ICell cell, List<string> lacciList, List<string> cellNames)
        {
            if (cell != null && lac != null)
            {
                string ci = getCellCI(cell);
                string lacci = lac.ToString() + "_" + ci;
                if (!lacciList.Contains(lacci))
                {
                    lacciList.Add(lacci);
                }
                if (!cellNames.Contains(cell.Name))
                {
                    cellNames.Add(cell.Name);
                }
            }
        }

        public virtual string getCellCI(ICell cell)
        {
            return "";
        }

        internal void JudeNbMaxRsrpCelll(Dictionary<float, ICell> nbCell, TestPoint testPoint, string tpNCell_EARFCN, string tpNCell_PCI, int index)
        {
            foreach (float fRsrp in nbCell.Keys)
            {
                if (fRsrp >= NbRsrpData.Max)
                {
                    addNbCellList(nbCell, testPoint, tpNCell_EARFCN, tpNCell_PCI, index, fRsrp);
                }
            }
        }

        private void addNbCellList(Dictionary<float, ICell> nbCell, TestPoint testPoint,
            string tpNCell_EARFCN, string tpNCell_PCI, int index, float fRsrp)
        {
            strMaxNBCellMinDistince = "";
            if (nbCell[fRsrp].Name != null && nbCell[fRsrp].Name != "")
            {
                StrMaxRsrpCellName = nbCell[fRsrp].Name;
                if (!nbCellList.Contains(nbCell[fRsrp]))
                {
                    nbCellList.Add(nbCell[fRsrp]);
                }
                strMaxNBCellMinDistince = MathFuncs.GetDistance(nbCell[fRsrp].Longitude, nbCell[fRsrp].Latitude, MidLng, MidLat).ToString("0.00");
            }
            else
            {
                if (testPoint[tpNCell_EARFCN, index] != null
                    && testPoint[tpNCell_PCI, index] != null)
                {
                    StrMaxRsrpCellName = (int?)testPoint[tpNCell_EARFCN, index] + "_" + (int?)testPoint[tpNCell_PCI, index];
                }
                nbCellList.Add(nbCell[fRsrp]);
            }
        }

        public virtual void SetFinalData(int areaID, int areaTypeID)
        {
            if (TestPoints.Count > 0)
            {
                MidLng = TestPoints[TestPoints.Count / 2].Longitude;
                MidLat = TestPoints[TestPoints.Count / 2].Latitude;

                FileName = TestPoints[0].FileName;
                StartTime = TestPoints[0].DateTime.ToString();

                RsrpData.ValidCnt = TestPoints.Count;
                RsrpData.SetResult();
                SinrData.SetResult();
                NbRsrpData.SetResult();

                SpeedData.SetResult();
            }
            TestPointCount = TestPoints.Count;

            setMotorWay(areaID, areaTypeID);
            findRoadName();
            findAreaName();
            findGridName();
            findAgentName();
        }

        public virtual void CalulateFinalRes()
        {
            LACCIs_plan = setListTostr(LacciList_plan);
            CellName_plan = setListTostr(CellNames_plan);
            LACCIs_opt = setListTostr(LacciList_opt);
            CellName_opt = setListTostr(CellNames_opt);

            TestPointRate_Plan = (float)Math.Round((float)100 * TestPointCount_Plan / (float)TestPoints.Count, 2);
            TestPointRate_Opt = (float)Math.Round((float)100 * TestPointCount_Opt / (float)TestPoints.Count, 2);
        }

        protected virtual void setMotorWay(int areaID, int areaTypeID)
        {
            MotorWay = AreaManager.GetInstance().GetAreaDesc(areaTypeID, areaID);
        }

        protected virtual void findRoadName()
        {
            RoadName = GISManager.GetInstance().GetRoadPlaceDesc(MidLng, MidLat);
        }

        protected virtual void findAreaName()
        {
            string strAreaName = GISManager.GetInstance().GetAreaPlaceDesc(MidLng, MidLat);
            AreaName = getValidData(strAreaName, AreaName);
        }

        protected virtual void findGridName()
        {
            string strGridName = GISManager.GetInstance().GetGridDesc(MidLng, MidLat);
            GridName = getValidData(strGridName, GridName);
        }

        protected virtual void findAgentName()
        {
            string strAreaAgentName = GISManager.GetInstance().GetAreaAgentDesc(MidLng, MidLat);
            AreaAgentName = getValidData(strAreaAgentName, AreaAgentName);
        }

        private string getValidData(string strName, string dataName)
        {
            string name = dataName;
            if (strName != null)
            {
                if (string.IsNullOrEmpty(name))
                {
                    name = strName;
                }
                else
                {
                    if (!name.Contains(strName) && strName != "")
                    {
                        name += "," + strName;
                    }
                }
            }
            return name;
        }

        public class DataInfo
        {
            public int ValidCnt { get; set; }
            public float Total { get; set; }
            public float Min { get; set; } = 1000;
            public float Max { get; set; } = -1000;
            public float Avg { get; set; }
            public void SetResult()
            {
                if (ValidCnt > 0)
                {
                    Avg = (float)Math.Round(Total / ValidCnt, 2);
                }
                if (Min == 1000)
                {
                    Min = 0;
                }
                if (Max == -1000)
                {
                    Max = 0;
                }
            }
        }

        public class SpeedInfo
        {
            public int ValidCnt { get; set; }
            public double Total { get; set; }
            public float Avg { get; set; }
            public float LowSpeedCnt { get; set; }
            public float LowSpeedPercent { get; set; }

            public void SetResult()
            {
                if (ValidCnt > 0)
                {
                    Avg = (float)Math.Round(Total / ValidCnt, 2);
                    LowSpeedPercent = (float)Math.Round(100 * LowSpeedCnt / ValidCnt, 2);
                }
            }
        }
    }
}
