﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using DevExpress.XtraGrid.Columns;
using MasterCom.NOP.DataSet;
using MasterCom.NOP.Report;
using MasterCom.NOP.WF.Core;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.NOP.Stat;

namespace MasterCom.RAMS.NOP
{
    public class StatRow
    {
        public StatRow(string cityName)
        {
            地市名称 = cityName;
            Events = new List<Event>();
        }

        public void ProcessOrder(Task task)
        {
            工单总数 += 1;
            switch (task.CurrentStatusName)
            {
                case "预处理":
                    待派发工单 += 1;
                    break;
                case "已派单":
                    待接单工单 += 1;
                    待回单工单 += 1;
                    break;
                case "已接单":
                    待回单工单 += 1;
                    break;
                case "已回单":
                    待验证工单 += 1;
                    break;
                case "已完成":
                    已解决工单 += 1;
                    break;
            }
            未解决工单 = 工单总数 - 已解决工单;
            问题解决率 = 已解决工单 / 工单总数;
        }

        public string 地市名称
        {
            get;
            private set;
        }

        public double 工单总数
        {
            get;
            private set;
        }

        public double 待派发工单
        {
            get;
            private set;
        }

        public double 待接单工单
        {
            get;
            private set;
        }

        public double 待回单工单
        {
            get;
            private set;
        }

        public double 待验证工单
        {
            get;
            private set;
        }

        public double 已归档工单
        {
            get { return 0; }
        }

        public double 处理超时工单
        {
            get { return 0; }
        }

        public double 工单超时率
        {
            get { return 0; }
        }

        public double 已解决工单
        {
            get;
            private set;
        }

        public double 未解决工单
        {
            get;
            private set;
        }

        public double 问题解决率
        {
            get;
            private set;
        }

        public List<Event> Events
        { get; set; }
        public int 事件个数
        {
            get { return Events.Count; }
        }
    }

    public class TaskOrderStater
    {
        public TaskOrderStater()
        {
            cityRowDic = new Dictionary<string, StatRow>();
            string[] cityNames = DistrictManager.GetInstance().DistrictNames;

            foreach (string city in cityNames)
            {
                if (string.IsNullOrEmpty(city))
                {
                    continue;
                }
                if (summaryRow == null)
                {
                    summaryRow = new StatRow("汇总");
                    continue;
                }

                string key = city.TrimEnd('市');
                cityRowDic[key] = new StatRow(key);
            }
        }

        MasterCom.NOP.DataSet.Schema schema = null;
        private void initSchema()
        {
            if (schema == null)
            {
                DataSetNetAdapter adapter = new DataSetNetAdapter(NopCfgMngr.Instance.NopServer.Ip
                    , NopCfgMngr.Instance.NopServer.Port, MainModel.GetInstance().User.LoginName, "mastercom");
                schema = adapter.GetSchemaByID(1);
            }
        }

        private ResultSet createResultSet(byte[] byteArr)
        {
            initSchema();
            MasterCom.NOP.Report.TemplateInfo info = new MasterCom.NOP.Report.TemplateInfo(schema);
            return ReportNetHelper.BytesToResultSet(byteArr, ref schema, ref info);
        }

        public List<StatRow> GetResult(DateTime sTime, DateTime eTime)
        {
            // 工单查询
            TaskOrderQuerier querier = new TaskOrderQuerier(MainModel.GetInstance());
              ICollection<Task> tasks = querier.Query(sTime, eTime);
            if (tasks == null)
            {
                throw (new Exception("工单查询失败"));
            }

            // 工单处理
            foreach (Task task in tasks)
            {
                string city = task.GetValue("地市") as string;
                ResultSet evtResultSet = createResultSet((byte[])task.GetValue("汇聚事件列表"));
                if (evtResultSet == null || evtResultSet.Rows.Count <= 0)
                {
                    continue;
                }
                List<Event> events = getEvents(evtResultSet);
                if (string.IsNullOrEmpty(city))
                {
                    continue;
                }

                if (summaryRow != null)
                {
                    summaryRow.ProcessOrder(task);
                    summaryRow.Events.AddRange(events);
                }
                if (cityRowDic.Keys.Count <= 0)
                {
                    return new List<StatRow>();
                }

                string key = city.TrimEnd('市');
                if (cityRowDic.ContainsKey(key))
                {
                    cityRowDic[key].ProcessOrder(task);
                    cityRowDic[key].Events.AddRange(events);
                }
            }

            // 排序返回
            System.Globalization.CultureInfo cultureInfo = new System.Globalization.CultureInfo("zh-CN");
            
            List<string> citys = new List<string>(cityRowDic.Keys);
            citys.Sort(delegate(string t1, string t2) {
                return string.Compare(t1, t2, false, cultureInfo);
            });

            List<StatRow> retRows = new List<StatRow>();
            foreach (string c in citys)
            {
                retRows.Add(cityRowDic[c]);
            }
            retRows.Add(summaryRow);

            return retRows;
        }

        private static List<Event> getEvents(ResultSet evtResultSet)
        {
            List<Event> events = new List<Event>();
            foreach (Row row in evtResultSet.Rows)
            {
                double? lng = (double?)row.GetValue("问题点经度");
                double? lat = (double?)row.GetValue("问题点纬度");
                if (lng != null && lat != null)
                {
                    Event evt = new Event();
                    evt.Longitude = (double)lng;
                    evt.Latitude = (double)lat;
                    events.Add(evt);
                }
            }

            return events;
        }

        private readonly Dictionary<string, StatRow> cityRowDic;

        private readonly StatRow summaryRow;
    }
}
