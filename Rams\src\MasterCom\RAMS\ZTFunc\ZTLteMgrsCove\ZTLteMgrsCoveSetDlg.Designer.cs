﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLteMgrsCoveSetDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBoxRemove = new System.Windows.Forms.GroupBox();
            this.chkStrongBand = new DevExpress.XtraEditors.CheckEdit();
            this.chkNotBand = new DevExpress.XtraEditors.CheckEdit();
            this.chkTwoEarfcn = new DevExpress.XtraEditors.CheckEdit();
            this.freqBandControl1 = new MasterCom.RAMS.ZTFunc.FreqBandControl();
            this.label8 = new System.Windows.Forms.Label();
            this.numOptionalRsrp = new System.Windows.Forms.NumericUpDown();
            this.chkOptionalRsrp = new System.Windows.Forms.CheckBox();
            this.label5 = new System.Windows.Forms.Label();
            this.numRsrpDiff = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.numRsrpMin = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.toolStripDropDownFreq = new System.Windows.Forms.ToolStripDropDown();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBoxRemove.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkStrongBand.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkNotBand.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkTwoEarfcn.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOptionalRsrp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpDiff)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMin)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBoxRemove
            // 
            this.groupBoxRemove.Controls.Add(this.chkStrongBand);
            this.groupBoxRemove.Controls.Add(this.chkNotBand);
            this.groupBoxRemove.Controls.Add(this.chkTwoEarfcn);
            this.groupBoxRemove.Controls.Add(this.freqBandControl1);
            this.groupBoxRemove.Location = new System.Drawing.Point(284, 12);
            this.groupBoxRemove.Name = "groupBoxRemove";
            this.groupBoxRemove.Size = new System.Drawing.Size(305, 159);
            this.groupBoxRemove.TabIndex = 52;
            this.groupBoxRemove.TabStop = false;
            // 
            // chkStrongBand
            // 
            this.chkStrongBand.Enabled = false;
            this.chkStrongBand.Location = new System.Drawing.Point(15, 106);
            this.chkStrongBand.Name = "chkStrongBand";
            this.chkStrongBand.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkStrongBand.Properties.Appearance.Options.UseFont = true;
            this.chkStrongBand.Properties.Caption = "最强归属段";
            this.chkStrongBand.Size = new System.Drawing.Size(84, 19);
            this.chkStrongBand.TabIndex = 20;
            this.chkStrongBand.CheckedChanged += new System.EventHandler(this.chkStrongBand_CheckedChanged);
            // 
            // chkNotBand
            // 
            this.chkNotBand.EditValue = true;
            this.chkNotBand.Location = new System.Drawing.Point(16, 39);
            this.chkNotBand.Name = "chkNotBand";
            this.chkNotBand.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkNotBand.Properties.Appearance.Options.UseFont = true;
            this.chkNotBand.Properties.Caption = "不分段";
            this.chkNotBand.Size = new System.Drawing.Size(63, 19);
            this.chkNotBand.TabIndex = 19;
            this.chkNotBand.CheckedChanged += new System.EventHandler(this.chkNotBand_CheckedChanged);
            // 
            // chkTwoEarfcn
            // 
            this.chkTwoEarfcn.Location = new System.Drawing.Point(6, -2);
            this.chkTwoEarfcn.Name = "chkTwoEarfcn";
            this.chkTwoEarfcn.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkTwoEarfcn.Properties.Appearance.Options.UseFont = true;
            this.chkTwoEarfcn.Properties.Caption = "多层网剔除异频";
            this.chkTwoEarfcn.Size = new System.Drawing.Size(115, 19);
            this.chkTwoEarfcn.TabIndex = 3;
            this.chkTwoEarfcn.CheckedChanged += new System.EventHandler(this.chkTwoEarfcn_CheckedChanged);
            // 
            // freqBandControl1
            // 
            this.freqBandControl1.ChkFreqBandChange_click = null;
            this.freqBandControl1.Location = new System.Drawing.Point(95, 23);
            this.freqBandControl1.Name = "freqBandControl1";
            this.freqBandControl1.Size = new System.Drawing.Size(204, 121);
            this.freqBandControl1.TabIndex = 21;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(235, 117);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(23, 12);
            this.label8.TabIndex = 51;
            this.label8.Text = "dBm";
            // 
            // numOptionalRsrp
            // 
            this.numOptionalRsrp.Enabled = false;
            this.numOptionalRsrp.Location = new System.Drawing.Point(109, 113);
            this.numOptionalRsrp.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numOptionalRsrp.Minimum = new decimal(new int[] {
            10000,
            0,
            0,
            -2147483648});
            this.numOptionalRsrp.Name = "numOptionalRsrp";
            this.numOptionalRsrp.Size = new System.Drawing.Size(120, 21);
            this.numOptionalRsrp.TabIndex = 50;
            this.numOptionalRsrp.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOptionalRsrp.Value = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            // 
            // chkOptionalRsrp
            // 
            this.chkOptionalRsrp.AutoSize = true;
            this.chkOptionalRsrp.Location = new System.Drawing.Point(19, 116);
            this.chkOptionalRsrp.Name = "chkOptionalRsrp";
            this.chkOptionalRsrp.Size = new System.Drawing.Size(84, 16);
            this.chkOptionalRsrp.TabIndex = 49;
            this.chkOptionalRsrp.Text = "信号强度≥";
            this.chkOptionalRsrp.UseVisualStyleBackColor = true;
            this.chkOptionalRsrp.CheckedChanged += new System.EventHandler(this.chkOptionalRsrp_CheckedChanged);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(235, 78);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(17, 12);
            this.label5.TabIndex = 46;
            this.label5.Text = "dB";
            // 
            // numRsrpDiff
            // 
            this.numRsrpDiff.Location = new System.Drawing.Point(109, 76);
            this.numRsrpDiff.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numRsrpDiff.Name = "numRsrpDiff";
            this.numRsrpDiff.Size = new System.Drawing.Size(120, 21);
            this.numRsrpDiff.TabIndex = 45;
            this.numRsrpDiff.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRsrpDiff.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(27, 79);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(77, 12);
            this.label3.TabIndex = 44;
            this.label3.Text = "相对覆盖带≤";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(235, 41);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(23, 12);
            this.label2.TabIndex = 41;
            this.label2.Text = "dBm";
            // 
            // numRsrpMin
            // 
            this.numRsrpMin.Location = new System.Drawing.Point(109, 38);
            this.numRsrpMin.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numRsrpMin.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numRsrpMin.Name = "numRsrpMin";
            this.numRsrpMin.Size = new System.Drawing.Size(120, 21);
            this.numRsrpMin.TabIndex = 40;
            this.numRsrpMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRsrpMin.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(39, 41);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 39;
            this.label1.Text = "最强信号≥";
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(388, 181);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 53;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Location = new System.Drawing.Point(496, 181);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 54;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // toolStripDropDownFreq
            // 
            this.toolStripDropDownFreq.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStripDropDownFreq.Name = "toolStripDropDown1";
            this.toolStripDropDownFreq.Size = new System.Drawing.Size(2, 4);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.numRsrpDiff);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.numRsrpMin);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.numOptionalRsrp);
            this.groupBox1.Controls.Add(this.chkOptionalRsrp);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(266, 159);
            this.groupBox1.TabIndex = 56;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "指标限定";
            // 
            // ZTLteMgrsCoveSetDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(603, 214);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupBoxRemove);
            this.Name = "ZTLteMgrsCoveSetDlg";
            this.Text = "条件设置窗口";
            this.Load += new System.EventHandler(this.ZTLteMgrsCoveSetDlg_Load);
            this.groupBoxRemove.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkStrongBand.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkNotBand.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkTwoEarfcn.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOptionalRsrp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpDiff)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMin)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBoxRemove;
        private DevExpress.XtraEditors.CheckEdit chkTwoEarfcn;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown numOptionalRsrp;
        private System.Windows.Forms.CheckBox chkOptionalRsrp;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown numRsrpDiff;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numRsrpMin;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.ToolStripDropDown toolStripDropDownFreq;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.CheckEdit chkStrongBand;
        private DevExpress.XtraEditors.CheckEdit chkNotBand;
        private FreqBandControl freqBandControl1;
    }
}