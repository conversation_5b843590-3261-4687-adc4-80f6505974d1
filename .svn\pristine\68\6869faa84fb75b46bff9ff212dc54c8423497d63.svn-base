﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Util;
using MasterCom.RAMS.Compare;
using MasterCom.Util;

namespace MasterCom.RAMS.Func
{
    public partial class CPHisGridForm : Form
    {
        public CPHisGridForm()
        {
            InitializeComponent();
        }
        public void FillDatas(CompUnit cu, string carrierPair, string servPair, int kpiPos)
        {
            this.listView.Items.Clear();

            foreach(DateResult dr in cu.testDates)
            {
                listView.Items.Add(makeLVI(dr, carrierPair, servPair, kpiPos));
            }
        }
        private ListViewItem makeLVI(DateResult dr, string carrierPair, string servPair, int kpiPos)
        {
            ListViewItem lvi = new ListViewItem();
            lvi.Text = JavaDate.GetDateTimeFromMilliseconds(dr.dateValue * 1000L).ToString("yyyy-MM-dd");
            TestResult tr = dr.getTestResult(carrierPair, servPair, kpiPos);
            lvi.SubItems.Add(getDescription(tr));
            lvi.SubItems[1].Tag = tr;
            lvi.SubItems[1].BackColor = getColor(tr);
            lvi.UseItemStyleForSubItems = false;
            return lvi;
        }

        private Color getColor(TestResult tr)
        {
            switch (tr)
            {
                case TestResult.Better:
                    return Color.FromArgb(0, 255, 0);
                case TestResult.Worse:
                    return Color.FromArgb(255, 0, 0);
                case TestResult.Middle:
                    return Color.FromArgb(255, 255, 0);
                case TestResult.AllGood:
                    return Color.Cyan;
                case TestResult.AllBad:
                    return Color.FromArgb(100, 100, 100);
                case TestResult.Unknown:
                    return Color.Transparent;
            }
            return Color.Transparent;
        }

        private string getDescription(TestResult tr)
        {
            switch(tr)
            {
                case TestResult.Better:
                    return "优势点";
                case TestResult.Worse:
                    return "劣势点";
                case TestResult.Middle:
                    return "相当点";
                case TestResult.AllGood:
                    return "同优点";
                case TestResult.AllBad:
                    return "同差点";
                case TestResult.Unknown:
                    return "未知点";
            }
            return "";
        }

        //private string getPercentDesc(int sub, int totle)
        //{
        //    if(totle==0)
        //    {
        //        return "-";
        //    }
        //    else
        //    {
        //        return string.Format("{0:F2}%", 100.0*sub / totle);
        //    }
        //}
        //private float getPercentFloat(int sub,int totle)
        //{
        //    if(totle==0)
        //    {
        //        return 0;
        //    }
        //    else
        //    {
        //        return (float)sub / totle;
        //    }
        //}

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(this.listView);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

        private void CPHisGridForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                e.Cancel = true;
                this.Visible = false;
            }
        }

        internal void ClearDatas()
        {
            this.listView.Items.Clear();
        }
    }
   
}