﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRBlockCallAnaBase : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        protected NRBlockCallAnaBase()
            : base(MainModel.GetInstance())
        {
            FilterSampleByRegion = true;
            IncludeMessage = true;

            Columns = NRTpHelper.InitBaseReplayParamBackground(false, false);
            Columns.Add("NR_lte_RSRP");
            Columns.Add("NR_lte_SINR");

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NRVoice);
        }

        readonly NRBlockCallAnaHelper helper = new NRBlockCallAnaHelper();

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35040, Name);
        }

        protected override bool getCondition()
        {
            NRBlockCallAnaDlg dlg = new NRBlockCallAnaDlg();
            dlg.SetCondition(blockCallCond);
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            blockCallCond = dlg.GetCondition();
            blockCalls = new List<NRBlockCallInfo>();
            return true;
        }

        protected override void queryTimePeriodInfo(ClientProxy clientProxy, Package package, TimePeriod period, bool byRound)
        {//只查询含Block Call事件的文件
            condition.EventIDs = new List<int>();
            condition.EventIDs.AddRange(helper.MoCallBlockEventIds);
            condition.EventIDs.AddRange(helper.MtCallBlockEventIds);
            condition.FilterOffValue9 = true;
            base.queryTimePeriodInfo(clientProxy, package, period, byRound);
        }

        #region
        /// <summary>
        /// 先进行主被叫关联
        /// </summary>
        protected override void analyseFiles()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = getMoMtPair();

            try
            {
                clearDataBeforeAnalyseFiles();
                int iloop = 0;
                foreach (KeyValuePair<FileInfo, FileInfo> pair in moMtPair)
                {
                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + moMtPair.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / moMtPair.Count);

                    condition.FileInfos.Clear();
                    if (pair.Key != null)
                    {
                        condition.FileInfos.Add(pair.Key);
                    }
                    if (pair.Value != null)
                    {
                        condition.FileInfos.Add(pair.Value);
                    }
                    replay();
                    condition.FileInfos.Clear();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        private Dictionary<FileInfo, FileInfo> getMoMtPair()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = new Dictionary<FileInfo, FileInfo>();
            Dictionary<int, bool> fileAdded = new Dictionary<int, bool>();
            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0
                   && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                {
                    continue;
                }
                if (fileAdded.ContainsKey(fileInfo.ID))
                {
                    continue;
                }
                fileAdded[fileInfo.ID] = true;
                if (fileInfo.EventCount == 0)
                {
                    moMtPair[fileInfo] = null;
                }
                else
                {
                    FileInfo mtFile = MainModel.FileInfos.Find(x => x.ID == fileInfo.EventCount);
                    if (mtFile == null)
                    {
                        mtFile = new FileInfo();
                        mtFile.ID = fileInfo.EventCount;
                        mtFile.LogTable = fileInfo.LogTable;
                    }
                    fileAdded[mtFile.ID] = true;
                    moMtPair[fileInfo] = mtFile;
                }
            }

            return moMtPair;
        }

        protected override void doStatWithQuery()
        {
            DTFileDataManager moFile = null;
            DTFileDataManager mtFile = null;
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file.MoMtFlag == (int)MoMtFile.MoFlag)
                {
                    moFile = file;
                }
                else if (file.MoMtFlag == (int)MoMtFile.MtFlag)
                {
                    mtFile = file;
                }
            }

            List<NRBlockCallAnaInfo> moCalls = getNRBlockCallAnaInfos(moFile, helper.MoCallAttemptEventIds
                , helper.MoCallOverEventIds, helper.MoCallBlockEventIds);

            List<NRBlockCallAnaInfo> mtCalls = getNRBlockCallAnaInfos(mtFile, helper.MtCallAttemptEventIds
                , helper.MtCallOverEventIds, helper.MtCallBlockEventIds);

            anaBothSideFiles(moCalls, mtCalls);
        }

        private List<NRBlockCallAnaInfo> getNRBlockCallAnaInfos(DTFileDataManager dtFile, List<int> callAttemptEventIds
            , List<int> callOverEventIds, List<int> callBlockEventIds)
        {
            List<NRBlockCallAnaInfo> NRBlockCallAnaInfoList = new List<NRBlockCallAnaInfo>();
            if (dtFile == null)
            {
                return NRBlockCallAnaInfoList;
            }

            string strMoMtDesc = dtFile.GetMoMtDesc();
            NRBlockCallAnaInfo curCall = null;
            NRBlockCallAnaInfo lastCall = null;
            foreach (DTData data in dtFile.DTDatas)
            {
                if (data is TestPoint)
                {
                    doWithTestPoint(curCall, data);
                }
                else if (data is Message)
                {
                    doWithMessage(curCall, data);
                }
                else if (data is Event)
                {
                    Event evt = data as Event;
                    if (callAttemptEventIds.Contains(evt.ID))
                    {
                        doWithCallAttemptEvent(NRBlockCallAnaInfoList, strMoMtDesc, evt, ref curCall, ref lastCall);
                    }
                    else if (callOverEventIds.Contains(evt.ID))
                    {
                        doWithCallOverEvent(callBlockEventIds, evt, ref curCall, lastCall);
                    }
                    else if (curCall != null)
                    {
                        curCall.Events.Add(evt);
                    }
                }
            }

            return NRBlockCallAnaInfoList;
        }

        private void doWithCallOverEvent(List<int> callBlockEventIds, Event evt
            , ref NRBlockCallAnaInfo curCall, NRBlockCallAnaInfo lastCall)
        {
            if (curCall != null)
            {
                bool needAdd = true;
                if (callBlockEventIds.Contains(evt.ID))
                {
                    bool filter = int.Parse(evt["Value9"].ToString()) == -1;
                    if (lastCall != null && evt.DateTime == curCall.BeginTime)
                    {
                        //block time与attempt time一致，则该block call属于上一次通话结果
                        needAdd = false;
                        lastCall.IsBlockCall = true;
                        lastCall.BlockEvt = evt;
                        lastCall.BlockTime = evt.DateTime;
                        lastCall.BlockTimeDesc = evt.DateTimeStringWithMillisecond;
                        lastCall.IsFilter = filter;
                    }
                    else
                    {
                        curCall.BlockEvt = evt;
                        curCall.IsBlockCall = true;
                        curCall.BlockTime = evt.DateTime;
                        curCall.BlockTimeDesc = evt.DateTimeStringWithMillisecond;
                        curCall.IsFilter = filter;
                    }
                }
                if (needAdd)
                {
                    curCall.Events.Add(evt);
                    curCall.EndTime = evt.DateTime;
                    curCall = null;
                }
            }
        }

        private void doWithCallAttemptEvent(List<NRBlockCallAnaInfo> NRBlockCallAnaInfoList, string strMoMtDesc, Event evt
            , ref NRBlockCallAnaInfo curCall, ref NRBlockCallAnaInfo lastCall)
        {
            lastCall = curCall;
            if (curCall != null)
            {
                curCall.EndTime = evt.DateTime;
            }
            curCall = new NRBlockCallAnaInfo();
            curCall.FileName = evt.FileName;
            curCall.MoMtDesc = strMoMtDesc;
            curCall.BeginTime = evt.DateTime;
            curCall.Events.Add(evt);
            NRBlockCallAnaInfoList.Add(curCall);
        }

        private void doWithTestPoint(NRBlockCallAnaInfo curCall, DTData data)
        {
            TestPoint tp = data as TestPoint;
            if (tp != null && curCall != null)
            {
                curCall.TestPoints.Add(tp);
            }
        }

        private void doWithMessage(NRBlockCallAnaInfo curCall, DTData data)
        {
            Message msg = data as Message;
            if (msg != null && curCall != null)
            {
                curCall.Messages.Add(msg);
            }
        }
        
        private void anaBothSideFiles(List<NRBlockCallAnaInfo> moCalls
             , List<NRBlockCallAnaInfo> mtCalls)
        {
            int lastCallIdx = 0;
            for (int i = 0; i < moCalls.Count; i++)
            {
                NRBlockCallAnaInfo moCall = moCalls[i];
                if (moCall.IsBlockCall)
                {
                    lastCallIdx = setOtherSideCall(lastCallIdx, moCall, mtCalls);
                    saveBlockCall(moCall);
                }
            }

            lastCallIdx = 0;
            for (int i = 0; i < mtCalls.Count; i++)
            {
                NRBlockCallAnaInfo mtCall = mtCalls[i];
                if (mtCall.IsBlockCall && mtCall.OtherSideCall == null)
                {
                    lastCallIdx = setOtherSideCall(lastCallIdx, mtCall, moCalls);
                    saveBlockCall(mtCall);
                }
            }
        }

        private int setOtherSideCall(int lastCallIdx, NRBlockCallAnaInfo call, List<NRBlockCallAnaInfo> otherSideCalls)
        {
            for (int j = lastCallIdx; j < otherSideCalls.Count; j++)
            {
                NRBlockCallAnaInfo otherSideCall = otherSideCalls[j];
                if (call.BeginTime > otherSideCall.BeginTime
                    && (call.BeginTime - otherSideCall.BeginTime).TotalSeconds < 30)
                {
                    call.OtherSideCall = otherSideCall;
                    //otherSideCall.OtherSideCall = call.OtherSideCall;
                    lastCallIdx = j;
                    if (!otherSideCall.IsBlockCall)
                    {
                        otherSideCall.BlockTime = call.BlockTime;
                        otherSideCall.BlockTimeDesc = call.BlockTimeDesc;
                    }
                    break;
                }
            }

            return lastCallIdx;
        }

        NRBlockCallAnaCondtion blockCallCond;
        private List<NRBlockCallInfo> blockCalls = new List<NRBlockCallInfo>();
        private void saveBlockCall(NRBlockCallAnaInfo call)
        {
            NRBlockCallInfo existsInfo = blockCalls.Find(x =>
            {
                return call.OtherSideCall != null && x.MoMtCalls?[0].BlockEvt == call.OtherSideCall.BlockEvt;
            });
            int sn;
            if (existsInfo != null)
            {
                sn = existsInfo.SN;
                blockCalls.Remove(existsInfo);
            }
            else
            {
                sn = blockCalls.Count + 1;
            }

            call.Evaluate(blockCallCond, true);
            NRBlockCallInfo blockCall = new NRBlockCallInfo(sn, call);
            blockCalls.Insert(sn - 1, blockCall);
            //blockCalls.Add(blockCall);
        }
        #endregion

        protected override void fireShowForm()
        {
            if (blockCalls == null || blockCalls.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有未接通事件。");
                return;
            }

            NRBlockCallAnaForm frm = MainModel.CreateResultForm(typeof(NRBlockCallAnaForm)) as NRBlockCallAnaForm;
            frm.FillData(blockCalls, blockCallCond);
            frm.Visible = true;
            frm.BringToFront();
            blockCalls = null;
        }
    }

    public class NRBlockCallAnaByFile : NRBlockCallAnaBase
    {
        private NRBlockCallAnaByFile()
            : base()
        {
        }

        private static NRBlockCallAnaByFile instance = null;
        public static NRBlockCallAnaByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRBlockCallAnaByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "未接通分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }

    public class NRBlockCallAnaByRegion : NRBlockCallAnaBase
    {
        protected NRBlockCallAnaByRegion()
            : base()
        {
        }

        private static NRBlockCallAnaByRegion instance = null;
        public static NRBlockCallAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRBlockCallAnaByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "未接通分析(按区域)"; }
        }
    }
}
