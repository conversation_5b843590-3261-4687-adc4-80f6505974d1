﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public enum StationAcceptType
    {
        GSM_OutDoor,
        GSM_Indoor,
        TDD_OutDoor,
        TDD_Indoor,
        TDD_Small,
        FDD_OutDoor,
        FDD_Indoor,
        FDD_Small,
        NBIOT,
        NR_OutDoor,
        NR_Indoor,
        NR_Small,
        NR_700M,
    }

    public class StationAcceptConfigInfo
    {
        public string ServerCoverPicPath { get; set; }
        public string LocalCoverPicPath { get; set; }

        public string BtsServerCoverPicPath { get; set; }
        public string BtsLocalCoverPicPath { get; set; }
    }

    public abstract class StationAcceptConfigHelper
    {
        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public abstract StationAcceptType Type { get; protected set; }
        /// <summary>
        /// 配置文件路径,一种单验确保只维护一个配置文件,目前的配置文件中基本只用于从服务端下载图片
        /// </summary>
        public abstract string ConfigPath { get; protected set; }
        /// <summary>
        /// 报告模板路径
        /// </summary>
        //public virtual string ModelPath { get; protected set; }
        //    = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"\config\StationAceppt\Model");
        public StationAcceptConfigInfo ConfigInfo { get; set; }

        /// <summary>
        /// 局方要求每次都出后在本地也生成一份报告
        /// </summary>
        public abstract string CurSavePath { get; protected set; }
        public virtual bool IsExportSavePath { get; protected set; } = true;

        public bool LoadConfig()
        {
            try
            {
                string startPath = System.Windows.Forms.Application.StartupPath;
                string xmlPath = startPath + ConfigPath;
                if (!File.Exists(xmlPath))
                {
                    XmlConfigFile newConfig = new XmlConfigFile();
                    System.Xml.XmlElement cfg = newConfig.AddConfig("Configs");
                    string serverPath = $@"{startPath}\userData\StationAceppt\{Type}";
                    newConfig.AddItem(cfg, "ServerCoverPicPath", serverPath);
                    string localPath = $@"{startPath}\userData\StationAceppt\{Type}";
                    newConfig.AddItem(cfg, "LocalCoverPicPath", localPath);
                    newConfig.Save(xmlPath);
                }

                ConfigInfo = new StationAcceptConfigInfo();
                XmlConfigFile xcfg = new XmlConfigFile(xmlPath);
                if (xcfg.Load())
                {
                    System.Xml.XmlElement configCondition = xcfg.GetConfig("Configs");
                    object obj = xcfg.GetItemValue(configCondition, "ServerCoverPicPath");
                    if (obj != null)
                    {
                        ConfigInfo.ServerCoverPicPath = obj.ToString();
                    }
                    obj = xcfg.GetItemValue(configCondition, "LocalCoverPicPath");
                    if (obj != null)
                    {
                        ConfigInfo.LocalCoverPicPath = obj.ToString();
                        if (!Directory.Exists(ConfigInfo.LocalCoverPicPath))
                        {
                            Directory.CreateDirectory(ConfigInfo.LocalCoverPicPath);
                        }
                    }
                }
                return true;
            }
            catch(Exception ex)
            {
                ConfigInfo = null;
                log.Error($"读取配置文件[{ConfigPath}]失败[{ex.Message}]");
                return false;
            }
        }

        public string GetCurSavePath()
        {
            string path = "";
            if (IsExportSavePath)
            {
                path = $@"{AppDomain.CurrentDomain.BaseDirectory}\{CurSavePath}\{DateTime.Now:yyyyMMdd}";
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
            }
            return path;
        }
    }

    public class NRStationAcceptConfigHelper : StationAcceptConfigHelper
    {
        public override StationAcceptType Type { get; protected set; } = StationAcceptType.NR_OutDoor;
        //NR宏站
        public override string ConfigPath { get; protected set; } 
            = @"\config\StationAceppt\NRCoverPicConfig.xml";
        //public override string ModelPath { get; protected set; }
        //   = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"\config\StationAceppt\Model");

        public override string CurSavePath { get; protected set; }
            = @"\StaionAccept\NR宏站验收";
    }

    public class NRIndoorStationAcceptConfigHelper : StationAcceptConfigHelper
    {
        public override StationAcceptType Type { get; protected set; } = StationAcceptType.NR_Indoor;
        //NR室分
        public override string ConfigPath { get; protected set; } 
            = @"\config\StationAceppt\NRIndoorCoverPicConfig.xml";
        public override string CurSavePath { get; protected set; }
            = @"\StaionAccept\NR室分验收";
    }

    public class NRSmallStationAcceptConfigHelper : StationAcceptConfigHelper
    {
        public override StationAcceptType Type { get; protected set; } = StationAcceptType.NR_Small;
        //NR小站
        public override string ConfigPath { get; protected set; } = "";

        public override string CurSavePath { get; protected set; }
            = @"\StaionAccept\NR小站验收";
    }

    public class NR700MStationAcceptConfigHelper : StationAcceptConfigHelper
    {
        public override StationAcceptType Type { get; protected set; } = StationAcceptType.NR_700M;
        //NR700M
        public override string ConfigPath { get; protected set; }
            = @"\config\StationAceppt\NR700MCoverPicConfig.xml";

        public override string CurSavePath { get; protected set; }
            = @"\StaionAccept\NR700M验收";
    }

    public class TddStationAcceptConfigHelper : StationAcceptConfigHelper
    {
        public override StationAcceptType Type { get; protected set; } = StationAcceptType.TDD_OutDoor;
        //TDD室分
        public override string ConfigPath { get; protected set; }
            = @"\config\StationAceppt\TddIndoorCoverPicConfig.xml";

        public override string CurSavePath { get; protected set; }
           = @"\StaionAccept\TDD宏站验收";
    }

    public class TddIndoorStationAcceptConfigHelper : StationAcceptConfigHelper
    {
        public override StationAcceptType Type { get; protected set; } = StationAcceptType.TDD_Indoor;
        //TDD室分
        public override string ConfigPath { get; protected set; } 
            = @"\config\StationAceppt\TddIndoorCoverPicConfig.xml";

        public override string CurSavePath { get; protected set; }
           = @"\StaionAccept\TDD室分验收";
    }

    public class TddSmallStationAcceptConfigHelper : StationAcceptConfigHelper
    {
        public override StationAcceptType Type { get; protected set; } = StationAcceptType.TDD_Indoor;
        //TDD室分
        public override string ConfigPath { get; protected set; }
            = @"\config\StationAceppt\TddIndoorCoverPicConfig.xml";

        public override string CurSavePath { get; protected set; }
           = @"\StaionAccept\TDD小站验收";
    }

    public class FddStationAcceptConfigHelper : StationAcceptConfigHelper
    {
        public override StationAcceptType Type { get; protected set; } = StationAcceptType.FDD_OutDoor;
        //FDD宏站
        public override string ConfigPath { get; protected set; } 
            = @"\config\StationAceppt\FddCoverPicConfig.xml";

        public override string CurSavePath { get; protected set; }
            = @"\StaionAccept\FDD宏站验收";
    }

    public class FddIndoorStationAcceptConfigHelper : StationAcceptConfigHelper
    {
        public override StationAcceptType Type { get; protected set; } = StationAcceptType.FDD_Indoor;
        //FDD室分
        public override string ConfigPath { get; protected set; } 
            = @"\config\StationAceppt\FddIndoorCoverPicConfig.xml";

        public override string CurSavePath { get; protected set; }
          = @"\StaionAccept\FDD室分验收";
    }

    public class FddSmallStationAcceptConfigHelper : StationAcceptConfigHelper
    {
        public override StationAcceptType Type { get; protected set; } = StationAcceptType.FDD_Small;
        //FDD室分
        public override string ConfigPath { get; protected set; }
            = @"\config\StationAceppt\FddIndoorCoverPicConfig.xml";

        public override string CurSavePath { get; protected set; }
          = @"\StaionAccept\FDD小站验收";
    }

    public class NbiotStationAcceptConfigHelper : StationAcceptConfigHelper
    {
        public override StationAcceptType Type { get; protected set; } = StationAcceptType.NBIOT;
        //FDD室分
        public override string ConfigPath { get; protected set; }
            = @"\config\StationAceppt\NbiotCoverPicConfig.xml";

        public override string CurSavePath { get; protected set; }
          = @"\StaionAccept\Nbiot验收";
    }
}
