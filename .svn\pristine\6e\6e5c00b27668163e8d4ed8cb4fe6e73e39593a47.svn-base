﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDiyQueryLteEdgeSpeedAnaDataForm : MinCloseForm
    {
        public ZTDiyQueryLteEdgeSpeedAnaDataForm(MainModel mainModel,string strColText)
            : base(mainModel)
        {
            InitializeComponent();
            this.gridColumn4.Caption = strColText;
        }
        public void FillData(List<LteEdgeSpeedInfo> lteEdgeSpeedInfoList)
        {
            BindingSource source = new BindingSource();
            source.DataSource = lteEdgeSpeedInfoList;
            dataGrid.DataSource = source;
            dataGrid.RefreshDataSource();
        }

        private void outPutData_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.gridView1);
        }
            
    }
}
