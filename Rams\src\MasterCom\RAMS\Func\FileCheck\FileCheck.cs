﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.src.MasterCom.RAMS.Func
{
    public class FileCheck : IComparable<FileCheck>
    {
        public string Name    //网格名称
        {
            get;
            set;
        }
        public string Port    //端口名称
        {
            get;
            set;
        }
        public int Number      //个数
        {
            get;
            set;
        }
        public bool IsRepeate  //是否有重复log
        {
            get;
            set;
        }
        public bool IsInclude    //文件中是否包含该网格
        {
            get;
            set;
        }
        public bool IsEquale    //不同端口测试时长是否相等
        {
            get;
            set;
        }
        public bool IsInExit     //五环内时长大于3小时
        {
            get;
            set;
        }
        public bool IsOutExit    //五环外时长小于2小时
        {
            get;
            set;
        }

        public string FileName    //记录文件名
        {
            get;
            set;
        }
        public string BeginTime   //记录log开始时间
        {
            get;
            set;
        }

        public int CompareTo(FileCheck other)   //对list进行排序
        {
            int result;
            if (this.IsInclude == other.IsInclude && this.Name == other.Name && this.Port == other.Port)
            {
                result = 0;
            }
            else
            {
                if (this.IsInclude.CompareTo(other.IsInclude) < 0)
                {
                    result = 1;
                }
                else if (this.IsInclude == other.IsInclude && this.Name.CompareTo(other.Name) > 0)
                {
                    result = 1;
                }
                else if (this.IsInclude == other.IsInclude && this.Name == other.Name 
                    && string.Compare(this.Port, other.Port) > 0)
                {
                    result = 1;
                }
                else
                {
                    result = -1;
                }
            }
            return result;
        }

    }
}
