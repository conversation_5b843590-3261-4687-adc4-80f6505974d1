﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using Microsoft.CSharp;
using System.CodeDom.Compiler;
using MasterCom.RAMS.Func.OwnSampleAnalyse;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Net
{
    public class CommonSampleAnalyserDIYByRegion : DIYSampleQuery
    {
        public CommonSampleAnalyserDIYByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "自定义扩展分析(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 1100, 1119, this.Name);
        }
        protected override void prepareStatPackage_Sample_FileFilter(Package package, TimePeriod period,bool byRound)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            if (!isPreSetted)
            {
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_COVER_SAMPLE;
            }
            else
            {
                package.Content.Type = this.preSettedCommandType;
            }
            package.Content.PrepareAddParam();
            if (byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            AddDIYRegion_Intersect(package);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);

            AddDIYStatStatus(package);
            //
            AddDIYEndOpFlag(package);
            
        }
        protected override void prepareStatPackage_Sample_SampleFilter(Package package, TimePeriod period)
        {
            AddDIYRegion_Sample(package);
            AddDIYEndOpFlag(package);
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }
        private readonly CommonAnalyserCommander commander = null;
        private readonly string ownfuncParam ="";
        private object resInfoStater = null;
        private bool resaverPrepared = false;
        protected override bool isValidTestPoint(TestPoint tp)
        {
            if(!DebugMode)
            {
                if (!commander._classReady && !commander._hasError)
                {
                    commander.initFuncClass_TestPoint();
                }
                if (commander._classReady)
                {
                    if (resInfoStater == null && !resaverPrepared)
                    {
                        object[] paramObj = new object[0];
                        object objClass = commander.clzzInst;
                        resInfoStater = objClass.GetType().InvokeMember("PrepareResultSaver", System.Reflection.BindingFlags.InvokeMethod, null, objClass, paramObj);
                        resaverPrepared = true;
                    }
                    bool inRegion = Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude); 
                    if (inRegion)
                    {
                        return getbResult(tp);
                    }
                }
                return false;
            }
            else//DEBUG MODE
            {
                return debugClass.CheckValidFunc(tp, ownfuncParam, resInfoStater);
            }
            
        }

        private bool getbResult(TestPoint tp)
        {
            object[] paramObj = new object[3];
            paramObj[0] = tp;
            paramObj[1] = ownfuncParam;
            paramObj[2] = resInfoStater;
            object objClass = commander.clzzInst;
            try
            {
                bool bResult = (bool)objClass.GetType().InvokeMember("CheckValidFunc", System.Reflection.BindingFlags.InvokeMethod, null, objClass, paramObj);
                return bResult;
            }
            catch
            {
                return false;
            }
        }

        SampleFilterSelectorDlg dlgFilter = null;
        private bool DebugMode = false;
        readonly SampleAnaDebugClassHerePlease debugClass = null;
        protected override void query()
        {
            curSelDIYSampleGroup = getCurSelDIYSampleGroupPrepare();
            if (curSelDIYSampleGroup == null)
            {
                return;
            }
            if(dlgFilter == null)
            {
                dlgFilter = new SampleFilterSelectorDlg();
            }
            if(dlgFilter.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            if(commander==null)
            {
                return;
            }
            DebugMode = false;
            commander._classReady = false;
            commander._hasError = false;
            resInfoStater = null;
            resaverPrepared = false;


            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                if (curSelDIYSampleGroup.ThemeName != null)
                {
                    MainModel.FireSetDefaultMapSerialTheme(curSelDIYSampleGroup.ThemeName);
                }
                MainModel.FireDTDataChanged(this);
                if(resInfoStater!=null)
                {
                    setResForm();
                }
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void setResForm()
        {
            SampleAnalyserResultForm form = new SampleAnalyserResultForm();
            if (!DebugMode)
            {
                form.Text = commander.desc;
            }
            else
            {
                form.Text = "自定义调试模式";
            }
            ListView lview = form.GetListView();
            if (!DebugMode)
            {
                object[] paramObj = new object[2];
                paramObj[0] = resInfoStater;
                paramObj[1] = lview;
                object objClass = commander.clzzInst;
                try
                {
                    objClass.GetType().InvokeMember( "ShowInListView", System.Reflection.BindingFlags.InvokeMethod, null, objClass,paramObj);
                    form.Owner = MainModel.MainForm;
                    form.Visible = true;
                }
                catch
                {
                    //continue
                }
            }
            else//Debug Mode
            {
                try
                {
                    debugClass.ShowInListView(resInfoStater, lview);
                    form.Owner = MainModel.MainForm;
                    form.Visible = true;
                }
                catch (Exception exxxs)
                {
                    MessageBox.Show("出错：" + exxxs);
                }
            }
        }
    }

    

}
