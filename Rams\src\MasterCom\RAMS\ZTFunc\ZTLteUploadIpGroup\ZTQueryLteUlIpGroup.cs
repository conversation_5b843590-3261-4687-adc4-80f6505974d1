﻿using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTLteUploadIpGroup;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTQueryLteUlIpGroup : DIYAnalyseByFileBackgroundBase
    {
        protected ZTQueryLteUlIpGroup()
            : base(MainModel.GetInstance())
        {
            this.IncludeEvent = true;
            this.IncludeMessage = true;
            this.Columns = new List<string>();
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_APP_Speed");
            Columns.Add("lte_PDCCH_UL_Grant_Count");
            Columns.Add("lte_PDSCH_PRb_Num_s");
            Columns.Add("lte_Rank_Indicator");
            Columns.Add("lte_Times_QAM64_UL");
            Columns.Add("lte_Times_QPSK_UL");
            Columns.Add("lte_Times_QAM16_UL");
            Columns.Add("lte_Count_UL_HARQ_ACK");
            Columns.Add("lte_Count_UL_HARQ_NACK");


            Columns.Add("lte_Wideband_CQI_for_CW0");
            Columns.Add("lte_Wideband_CQI_for_CW1");
            Columns.Add("lte_Ratio_UL_HARQ_ACK");
            Columns.Add("lte_Ratio_UL_HARQ_NACK");
            Columns.Add("lte_MCS_UL");
            Columns.Add("lte_PDSCH_BLER");
        }

        protected readonly static object lockObj = new object();
        private static ZTQueryLteUlIpGroup intance = null;
        public static ZTQueryLteUlIpGroup GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTQueryLteUlIpGroup();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get
            {
                return "按IP统计上传信息(按文件)";
            }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22092, this.Name);
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (condition.FileInfos.Count > 0)
            {
                ulInfoSet = new List<UpLoadInfo>();
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

        protected override void fireShowForm()
        {
            UploadIpGroupForm frm = MainModel.GetObjectFromBlackboard(typeof(UploadIpGroupForm)) as UploadIpGroupForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new UploadIpGroupForm();
            }
            frm.Owner = MainModel.MainForm;
            frm.FillData(ulInfoSet);
            frm.Visible = true;
            frm.BringToFront();
            ulInfoSet = null;
        }
        protected List<UpLoadInfo> ulInfoSet = null;

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                UpLoadInfo curInfo = null;
                bool isUling = false;
                List<TestPoint> ulingTpSet = new List<TestPoint>();//上传过程中的采样点
                foreach (DTData data in file.DTDatas)
                {
                    dealDataInfo(file, ref curInfo, ref isUling, ulingTpSet, data);
                }
                if (curInfo != null && curInfo.UploadResultEvents.Count > 0 && !ulInfoSet.Contains(curInfo))
                {
                    curInfo.AddTestPoints(ulingTpSet);
                    curInfo.MakeSummary();
                    ulInfoSet.Add(curInfo);
                }
            }
        }

        private void dealDataInfo(DTFileDataManager file, ref UpLoadInfo curInfo, ref bool isUling, List<TestPoint> ulingTpSet, DTData data)
        {
            if (data is MessageWithSource)
            {
                setMsgInfo(file, ref curInfo, ref isUling, ulingTpSet, data);
            }
            else if (curInfo != null)
            {
                if (data is Event)
                {
                    setEvtInfo(curInfo, ref isUling, ulingTpSet, data);
                }
                else if (isUling && data is TestPoint)
                {
                    ulingTpSet.Add(data as TestPoint);
                }
            }
        }

        private void setEvtInfo(UpLoadInfo curInfo, ref bool isUling, List<TestPoint> ulingTpSet, DTData data)
        {
            Event evt = data as Event;
            if (evt.ID == 60)
            {//ftp begin
                isUling = true;
                if (curInfo.BeginTime == DateTime.MinValue)
                {
                    curInfo.BeginTime = evt.DateTime;
                }
            }
            else if ((evt.ID == 61 || evt.ID == 62) && curInfo.BeginTime != DateTime.MinValue)
            {//结束
                isUling = false;
                curInfo.AddTestPoints(ulingTpSet);
                ulingTpSet.Clear();
                curInfo.UploadResultEvents.Add(evt);
            }
        }

        private void setMsgInfo(DTFileDataManager file, ref UpLoadInfo curInfo, ref bool isUling, List<TestPoint> ulingTpSet, DTData data)
        {
            MessageWithSource msg = data as MessageWithSource;
            if (msg.ID == 1097533253 || msg.ID == 1097533249)//detach attch 
            {
                isUling = false;
                if (curInfo != null && curInfo.UploadResultEvents.Count > 0)
                {
                    curInfo.MakeSummary();
                    ulInfoSet.Add(curInfo);
                }
                ulingTpSet.Clear();
                curInfo = null;
            }
            else if (msg.ID == 0x416b02c1)
            {
                isUling = false;
                if (curInfo != null && curInfo.UploadResultEvents.Count > 0)
                {
                    curInfo.MakeSummary();
                    ulInfoSet.Add(curInfo);
                }
                ulingTpSet.Clear();
                curInfo = null;
                int hexIp = 0;
                MessageDecodeHelper.StartDissect(msg.Direction, msg.Source, msg.Length, msg.ID);
                MessageDecodeHelper.GetSingleSInt("nas_eps.esm.pdn_ipv4_int", ref hexIp);
                string strIp = hexIp.ToString("X8");
                strIp = string.Format("{0}.{1}.{2}.{3}"
                    , Convert.ToInt32(strIp.Substring(0, 2), 16)
                    , Convert.ToInt32(strIp.Substring(2, 2), 16)
                    , Convert.ToInt32(strIp.Substring(4, 2), 16)
                    , Convert.ToInt32(strIp.Substring(6, 2), 16));
                curInfo = new UpLoadInfo(file.FileName, strIp);
            }
        }
    }

    public class ZTQueryLteUlIpGroup_FDD : ZTQueryLteUlIpGroup
    {
        private static ZTQueryLteUlIpGroup_FDD instance = null;
        public static new ZTQueryLteUlIpGroup_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTQueryLteUlIpGroup_FDD();
                    }
                }
            }
            return instance;
        }
        protected ZTQueryLteUlIpGroup_FDD()
            : base()
        {
            this.IncludeEvent = true;
            this.IncludeMessage = true;
            this.Columns = new List<string>();
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_SINR");
            Columns.Add("lte_fdd_APP_Speed");
            Columns.Add("lte_fdd_PDCCH_UL_Grant_Count");
            Columns.Add("lte_fdd_PDSCH_PRb_Num_s");
            Columns.Add("lte_fdd_Rank_Indicator");
            Columns.Add("lte_fdd_Times_QAM64_UL");
            Columns.Add("lte_fdd_Times_QPSK_UL");
            Columns.Add("lte_fdd_Times_QAM16_UL");
            Columns.Add("lte_fdd_Count_UL_HARQ_ACK");
            Columns.Add("lte_fdd_Count_UL_HARQ_NACK");


            Columns.Add("lte_fdd_Wideband_CQI_for_CW0");
            Columns.Add("lte_fdd_Wideband_CQI_for_CW1");
            Columns.Add("lte_fdd_Ratio_UL_HARQ_ACK");
            Columns.Add("lte_fdd_Ratio_UL_HARQ_NACK");
            Columns.Add("lte_fdd_MCS_UL");
            Columns.Add("lte_fdd_PDSCH_BLER");
        }
        public override string Name
        {
            get
            {
                return "按IP统计上传信息LTE_FDD(按文件)";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26062, this.Name);
        }
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                UpLoadInfo curInfo = null;
                bool isUling = false;
                List<TestPoint> ulingTpSet = new List<TestPoint>();//上传过程中的采样点
                foreach (DTData data in file.DTDatas)
                {
                    dealDataInfo(file, ref curInfo, ref isUling, ulingTpSet, data);
                }
                if (curInfo != null && curInfo.UploadResultEvents.Count > 0 && !ulInfoSet.Contains(curInfo))
                {
                    curInfo.AddTestPoints(ulingTpSet);
                    curInfo.MakeSummary();
                    ulInfoSet.Add(curInfo);
                }
            }
        }

        private void dealDataInfo(DTFileDataManager file, ref UpLoadInfo curInfo, ref bool isUling, List<TestPoint> ulingTpSet, DTData data)
        {
            if (data is MessageWithSource)
            {
                setMsgInfo(file, ref curInfo, ref isUling, ulingTpSet, data);
            }
            else if (curInfo != null)
            {
                if (data is Event)
                {
                    setEvtInfo(curInfo, ref isUling, ulingTpSet, data);
                }
                else if (isUling && data is TestPoint)
                {
                    ulingTpSet.Add(data as TestPoint);
                }
            }
        }

        private void setEvtInfo(UpLoadInfo curInfo, ref bool isUling, List<TestPoint> ulingTpSet, DTData data)
        {
            Event evt = data as Event;
            if (evt.ID == 3560)
            {//ftp begin
                isUling = true;
                if (curInfo.BeginTime == DateTime.MinValue)
                {
                    curInfo.BeginTime = evt.DateTime;
                }
            }
            else if ((evt.ID == 3561 || evt.ID == 3562) && curInfo.BeginTime != DateTime.MinValue)
            {//结束
                isUling = false;
                curInfo.AddTestPoints(ulingTpSet);
                ulingTpSet.Clear();
                curInfo.UploadResultEvents.Add(evt);
            }
        }

        private void setMsgInfo(DTFileDataManager file, ref UpLoadInfo curInfo, ref bool isUling, List<TestPoint> ulingTpSet, DTData data)
        {
            MessageWithSource msg = data as MessageWithSource;
            if (msg.ID == 1097533253 || msg.ID == 1097533249)//detach attch 
            {
                isUling = false;
                if (curInfo != null && curInfo.UploadResultEvents.Count > 0)
                {
                    curInfo.MakeSummary();
                    ulInfoSet.Add(curInfo);
                }
                ulingTpSet.Clear();
                curInfo = null;
            }
            else if (msg.ID == 0x416b02c1)
            {
                isUling = false;
                if (curInfo != null && curInfo.UploadResultEvents.Count > 0)
                {
                    curInfo.MakeSummary();
                    ulInfoSet.Add(curInfo);
                }
                ulingTpSet.Clear();
                curInfo = null;
                int hexIp = 0;
                MessageDecodeHelper.StartDissect(msg.Direction, msg.Source, msg.Length, msg.ID);
                MessageDecodeHelper.GetSingleSInt("nas_eps.esm.pdn_ipv4_int", ref hexIp);
                string strIp = hexIp.ToString("X8");
                strIp = string.Format("{0}.{1}.{2}.{3}"
                    , Convert.ToInt32(strIp.Substring(0, 2), 16)
                    , Convert.ToInt32(strIp.Substring(2, 2), 16)
                    , Convert.ToInt32(strIp.Substring(4, 2), 16)
                    , Convert.ToInt32(strIp.Substring(6, 2), 16));
                curInfo = new UpLoadInfo(file.FileName, strIp);
            }
        }
    }
}
