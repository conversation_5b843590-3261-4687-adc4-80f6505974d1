﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TDPoorBlerRoadListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(TDPoorBlerRoadListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewRoad = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDitance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSample = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxBler = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinBler = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgBler = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxPCCPCHC2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinPCCPCHC2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgPCCPCHC2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxDPCHC2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinDPCHC2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgDPCHC2I = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitudeMid = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitudeMid = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFirstTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLastTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCells = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellNames = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewRoad)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(153, 98);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(152, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(149, 6);
            this.toolStripMenuItem1.Visible = false;
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(152, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Visible = false;
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(152, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Visible = false;
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // ListViewRoad
            // 
            this.ListViewRoad.AllColumns.Add(this.olvColumnSN);
            this.ListViewRoad.AllColumns.Add(this.olvColumnRoadName);
            this.ListViewRoad.AllColumns.Add(this.olvColumnDitance);
            this.ListViewRoad.AllColumns.Add(this.olvColumnSample);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMaxBler);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMinBler);
            this.ListViewRoad.AllColumns.Add(this.olvColumnAvgBler);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMaxRscp);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMinRscp);
            this.ListViewRoad.AllColumns.Add(this.olvColumnAvgRscp);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMaxPCCPCHC2I);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMinPCCPCHC2I);
            this.ListViewRoad.AllColumns.Add(this.olvColumnAvgPCCPCHC2I);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMaxDPCHC2I);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMinDPCHC2I);
            this.ListViewRoad.AllColumns.Add(this.olvColumnAvgDPCHC2I);
            this.ListViewRoad.AllColumns.Add(this.olvColumnLongitudeMid);
            this.ListViewRoad.AllColumns.Add(this.olvColumnLatitudeMid);
            this.ListViewRoad.AllColumns.Add(this.olvColumnFileName);
            this.ListViewRoad.AllColumns.Add(this.olvColumnFirstTime);
            this.ListViewRoad.AllColumns.Add(this.olvColumnLastTime);
            this.ListViewRoad.AllColumns.Add(this.olvColumnCells);
            this.ListViewRoad.AllColumns.Add(this.olvColumnCellNames);
            this.ListViewRoad.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnRoadName,
            this.olvColumnDitance,
            this.olvColumnSample,
            this.olvColumnMaxBler,
            this.olvColumnMinBler,
            this.olvColumnAvgBler,
            this.olvColumnMaxRscp,
            this.olvColumnMinRscp,
            this.olvColumnAvgRscp,
            this.olvColumnMaxPCCPCHC2I,
            this.olvColumnMinPCCPCHC2I,
            this.olvColumnAvgPCCPCHC2I,
            this.olvColumnMaxDPCHC2I,
            this.olvColumnMinDPCHC2I,
            this.olvColumnAvgDPCHC2I,
            this.olvColumnLongitudeMid,
            this.olvColumnLatitudeMid,
            this.olvColumnFileName,
            this.olvColumnFirstTime,
            this.olvColumnLastTime,
            this.olvColumnCells,
            this.olvColumnCellNames});
            this.ListViewRoad.ContextMenuStrip = this.ctxMenu;
            this.ListViewRoad.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewRoad.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewRoad.FullRowSelect = true;
            this.ListViewRoad.GridLines = true;
            this.ListViewRoad.HeaderWordWrap = true;
            this.ListViewRoad.IsNeedShowOverlay = false;
            this.ListViewRoad.Location = new System.Drawing.Point(0, 0);
            this.ListViewRoad.Name = "ListViewRoad";
            this.ListViewRoad.OwnerDraw = true;
            this.ListViewRoad.ShowGroups = false;
            this.ListViewRoad.Size = new System.Drawing.Size(1222, 502);
            this.ListViewRoad.TabIndex = 5;
            this.ListViewRoad.UseCompatibleStateImageBehavior = false;
            this.ListViewRoad.View = System.Windows.Forms.View.Details;
            this.ListViewRoad.VirtualMode = true;
            this.ListViewRoad.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 40;
            // 
            // olvColumnRoadName
            // 
            this.olvColumnRoadName.HeaderFont = null;
            this.olvColumnRoadName.Text = "道路名称";
            this.olvColumnRoadName.Width = 120;
            // 
            // olvColumnDitance
            // 
            this.olvColumnDitance.HeaderFont = null;
            this.olvColumnDitance.Text = "距离(米)";
            // 
            // olvColumnSample
            // 
            this.olvColumnSample.HeaderFont = null;
            this.olvColumnSample.Text = "采样点数";
            // 
            // olvColumnMaxBler
            // 
            this.olvColumnMaxBler.HeaderFont = null;
            this.olvColumnMaxBler.Text = "最大BLER";
            // 
            // olvColumnMinBler
            // 
            this.olvColumnMinBler.HeaderFont = null;
            this.olvColumnMinBler.Text = "最小BLER";
            // 
            // olvColumnAvgBler
            // 
            this.olvColumnAvgBler.HeaderFont = null;
            this.olvColumnAvgBler.Text = "平均BLER";
            // 
            // olvColumnMaxRscp
            // 
            this.olvColumnMaxRscp.HeaderFont = null;
            this.olvColumnMaxRscp.Text = "最大PCCPCH_RSCP";
            this.olvColumnMaxRscp.Width = 80;
            // 
            // olvColumnMinRscp
            // 
            this.olvColumnMinRscp.HeaderFont = null;
            this.olvColumnMinRscp.Text = "最小PCCPCH_RSCP";
            this.olvColumnMinRscp.Width = 80;
            // 
            // olvColumnAvgRscp
            // 
            this.olvColumnAvgRscp.HeaderFont = null;
            this.olvColumnAvgRscp.Text = "平均PCCPCH_RSCP";
            this.olvColumnAvgRscp.Width = 80;
            // 
            // olvColumnMaxPCCPCHC2I
            // 
            this.olvColumnMaxPCCPCHC2I.HeaderFont = null;
            this.olvColumnMaxPCCPCHC2I.Text = "最大PCCPCH_C/I";
            this.olvColumnMaxPCCPCHC2I.Width = 80;
            // 
            // olvColumnMinPCCPCHC2I
            // 
            this.olvColumnMinPCCPCHC2I.HeaderFont = null;
            this.olvColumnMinPCCPCHC2I.Text = "最小PCCPCH_C/I";
            this.olvColumnMinPCCPCHC2I.Width = 80;
            // 
            // olvColumnAvgPCCPCHC2I
            // 
            this.olvColumnAvgPCCPCHC2I.HeaderFont = null;
            this.olvColumnAvgPCCPCHC2I.Text = "平均PCCPCH_C/I";
            this.olvColumnAvgPCCPCHC2I.Width = 80;
            // 
            // olvColumnMaxDPCHC2I
            // 
            this.olvColumnMaxDPCHC2I.HeaderFont = null;
            this.olvColumnMaxDPCHC2I.Text = "最大DPCH_C/I";
            // 
            // olvColumnMinDPCHC2I
            // 
            this.olvColumnMinDPCHC2I.HeaderFont = null;
            this.olvColumnMinDPCHC2I.Text = "最小DPCH_C/I";
            // 
            // olvColumnAvgDPCHC2I
            // 
            this.olvColumnAvgDPCHC2I.HeaderFont = null;
            this.olvColumnAvgDPCHC2I.Text = "平均DPCH_C/I";
            // 
            // olvColumnLongitudeMid
            // 
            this.olvColumnLongitudeMid.HeaderFont = null;
            this.olvColumnLongitudeMid.Text = "中心经度";
            this.olvColumnLongitudeMid.Width = 80;
            // 
            // olvColumnLatitudeMid
            // 
            this.olvColumnLatitudeMid.HeaderFont = null;
            this.olvColumnLatitudeMid.Text = "中心纬度";
            this.olvColumnLatitudeMid.Width = 80;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名";
            // 
            // olvColumnFirstTime
            // 
            this.olvColumnFirstTime.HeaderFont = null;
            this.olvColumnFirstTime.Text = "开始时间";
            this.olvColumnFirstTime.Width = 100;
            // 
            // olvColumnLastTime
            // 
            this.olvColumnLastTime.HeaderFont = null;
            this.olvColumnLastTime.Text = "结束时间";
            this.olvColumnLastTime.Width = 100;
            // 
            // olvColumnCells
            // 
            this.olvColumnCells.HeaderFont = null;
            this.olvColumnCells.Text = "占用小区";
            // 
            // olvColumnCellNames
            // 
            this.olvColumnCellNames.HeaderFont = null;
            this.olvColumnCellNames.Text = "占用小区名称";
            // 
            // TDPoorBlerRoadListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1222, 502);
            this.Controls.Add(this.ListViewRoad);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "TDPoorBlerRoadListForm";
            this.Text = "高BLER路段";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewRoad)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private BrightIdeasSoftware.TreeListView ListViewRoad;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnDitance;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadName;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnSample;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnMinRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitudeMid;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitudeMid;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxPCCPCHC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnMinPCCPCHC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgPCCPCHC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxBler;
        private BrightIdeasSoftware.OLVColumn olvColumnMinBler;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgBler;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxDPCHC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnMinDPCHC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgDPCHC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnFirstTime;
        private BrightIdeasSoftware.OLVColumn olvColumnLastTime;
        private BrightIdeasSoftware.OLVColumn olvColumnCells;
        private BrightIdeasSoftware.OLVColumn olvColumnCellNames;

    }
}