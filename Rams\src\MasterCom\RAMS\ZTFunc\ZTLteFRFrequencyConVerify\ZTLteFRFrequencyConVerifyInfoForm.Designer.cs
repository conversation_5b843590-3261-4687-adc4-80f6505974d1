﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLteFRFrequencyConVerifyInfoForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTLteFRFrequencyConVerifyInfoForm));
            this.ListViewFRVerify = new BrightIdeasSoftware.TreeListView();
            this.olvSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvGridName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvEventTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvEventLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvEventLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvGSM_Name = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvGSM_LAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvGSM_CI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvGSM_Distance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvGSMAvgQual = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvGSMAvgRxLev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvHasFreON = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvChannelReleList = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvSCell_Name = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvSCell_BaseName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvSCell_LAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvSCell_ECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvBackEspri = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvbACKpci = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvSCell_Distance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvSCell_RSRP = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvSCell_SINR = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvSCell_RSRQ = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvSCell_RSSI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvNearLTECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvNearLTEName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvNearLTEFre = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCloseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewFRVerify)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // ListViewFRVerify
            // 
            this.ListViewFRVerify.AllColumns.Add(this.olvSN);
            this.ListViewFRVerify.AllColumns.Add(this.olvFileName);
            this.ListViewFRVerify.AllColumns.Add(this.olvGridName);
            this.ListViewFRVerify.AllColumns.Add(this.olvEventTime);
            this.ListViewFRVerify.AllColumns.Add(this.olvEventLongitude);
            this.ListViewFRVerify.AllColumns.Add(this.olvEventLatitude);
            this.ListViewFRVerify.AllColumns.Add(this.olvGSM_Name);
            this.ListViewFRVerify.AllColumns.Add(this.olvGSM_LAC);
            this.ListViewFRVerify.AllColumns.Add(this.olvGSM_CI);
            this.ListViewFRVerify.AllColumns.Add(this.olvGSM_Distance);
            this.ListViewFRVerify.AllColumns.Add(this.olvGSMAvgQual);
            this.ListViewFRVerify.AllColumns.Add(this.olvGSMAvgRxLev);
            this.ListViewFRVerify.AllColumns.Add(this.olvHasFreON);
            this.ListViewFRVerify.AllColumns.Add(this.olvChannelReleList);
            this.ListViewFRVerify.AllColumns.Add(this.olvSCell_Name);
            this.ListViewFRVerify.AllColumns.Add(this.olvSCell_BaseName);
            this.ListViewFRVerify.AllColumns.Add(this.olvSCell_LAC);
            this.ListViewFRVerify.AllColumns.Add(this.olvSCell_ECI);
            this.ListViewFRVerify.AllColumns.Add(this.olvBackEspri);
            this.ListViewFRVerify.AllColumns.Add(this.olvbACKpci);
            this.ListViewFRVerify.AllColumns.Add(this.olvSCell_Distance);
            this.ListViewFRVerify.AllColumns.Add(this.olvSCell_RSRP);
            this.ListViewFRVerify.AllColumns.Add(this.olvSCell_SINR);
            this.ListViewFRVerify.AllColumns.Add(this.olvSCell_RSRQ);
            this.ListViewFRVerify.AllColumns.Add(this.olvSCell_RSSI);
            this.ListViewFRVerify.AllColumns.Add(this.olvNearLTECI);
            this.ListViewFRVerify.AllColumns.Add(this.olvNearLTEName);
            this.ListViewFRVerify.AllColumns.Add(this.olvNearLTEFre);
            this.ListViewFRVerify.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvSN,
            this.olvFileName,
            this.olvGridName,
            this.olvEventTime,
            this.olvEventLongitude,
            this.olvEventLatitude,
            this.olvGSM_Name,
            this.olvGSM_LAC,
            this.olvGSM_CI,
            this.olvGSM_Distance,
            this.olvGSMAvgQual,
            this.olvGSMAvgRxLev,
            this.olvHasFreON,
            this.olvChannelReleList,
            this.olvSCell_Name,
            this.olvSCell_BaseName,
            this.olvSCell_LAC,
            this.olvSCell_ECI,
            this.olvBackEspri,
            this.olvbACKpci,
            this.olvSCell_Distance,
            this.olvSCell_RSRP,
            this.olvSCell_SINR,
            this.olvSCell_RSRQ,
            this.olvSCell_RSSI,
            this.olvNearLTECI,
            this.olvNearLTEName,
            this.olvNearLTEFre});
            this.ListViewFRVerify.ContextMenuStrip = this.contextMenuStrip;
            this.ListViewFRVerify.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewFRVerify.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewFRVerify.FullRowSelect = true;
            this.ListViewFRVerify.GridLines = true;
            this.ListViewFRVerify.HeaderWordWrap = true;
            this.ListViewFRVerify.IsNeedShowOverlay = false;
            this.ListViewFRVerify.Location = new System.Drawing.Point(0, 0);
            this.ListViewFRVerify.Name = "ListViewFRVerify";
            this.ListViewFRVerify.OwnerDraw = true;
            this.ListViewFRVerify.ShowGroups = false;
            this.ListViewFRVerify.Size = new System.Drawing.Size(1021, 502);
            this.ListViewFRVerify.TabIndex = 8;
            this.ListViewFRVerify.UseCompatibleStateImageBehavior = false;
            this.ListViewFRVerify.View = System.Windows.Forms.View.Details;
            this.ListViewFRVerify.VirtualMode = true;
            // 
            // olvSN
            // 
            this.olvSN.AspectName = "";
            this.olvSN.HeaderFont = null;
            this.olvSN.Text = "序号";
            this.olvSN.Width = 40;
            // 
            // olvFileName
            // 
            this.olvFileName.HeaderFont = null;
            this.olvFileName.Text = "文件名称";
            this.olvFileName.Width = 120;
            // 
            // olvGridName
            // 
            this.olvGridName.HeaderFont = null;
            this.olvGridName.Text = "网格名称";
            // 
            // olvEventTime
            // 
            this.olvEventTime.HeaderFont = null;
            this.olvEventTime.Text = "事件时间";
            this.olvEventTime.Width = 80;
            // 
            // olvEventLongitude
            // 
            this.olvEventLongitude.HeaderFont = null;
            this.olvEventLongitude.Text = "事件经度";
            // 
            // olvEventLatitude
            // 
            this.olvEventLatitude.HeaderFont = null;
            this.olvEventLatitude.Text = "事件纬度";
            // 
            // olvGSM_Name
            // 
            this.olvGSM_Name.HeaderFont = null;
            this.olvGSM_Name.Text = "通话结束GSM小区名称";
            // 
            // olvGSM_LAC
            // 
            this.olvGSM_LAC.HeaderFont = null;
            this.olvGSM_LAC.Text = "通话结束GSM小区LAC";
            // 
            // olvGSM_CI
            // 
            this.olvGSM_CI.HeaderFont = null;
            this.olvGSM_CI.Text = "通话结束GSM小区CI";
            // 
            // olvGSM_Distance
            // 
            this.olvGSM_Distance.HeaderFont = null;
            this.olvGSM_Distance.Text = "通话结束GSM小区与事件距离";
            // 
            // olvGSMAvgQual
            // 
            this.olvGSMAvgQual.HeaderFont = null;
            this.olvGSMAvgQual.Text = "通话结束GSM信号强度均值";
            // 
            // olvGSMAvgRxLev
            // 
            this.olvGSMAvgRxLev.HeaderFont = null;
            this.olvGSMAvgRxLev.Text = "通话结束GSM质量均值";
            // 
            // olvHasFreON
            // 
            this.olvHasFreON.HeaderFont = null;
            this.olvHasFreON.Text = "网络侧FR功能是否开启";
            // 
            // olvChannelReleList
            // 
            this.olvChannelReleList.HeaderFont = null;
            this.olvChannelReleList.Text = "Channel Release下发的LTE频点清单";
            // 
            // olvSCell_Name
            // 
            this.olvSCell_Name.HeaderFont = null;
            this.olvSCell_Name.Text = "LTE小区名称";
            // 
            // olvSCell_BaseName
            // 
            this.olvSCell_BaseName.HeaderFont = null;
            this.olvSCell_BaseName.Text = "LTE基站名称";
            // 
            // olvSCell_LAC
            // 
            this.olvSCell_LAC.HeaderFont = null;
            this.olvSCell_LAC.Text = "LTE小区LAC";
            // 
            // olvSCell_ECI
            // 
            this.olvSCell_ECI.HeaderFont = null;
            this.olvSCell_ECI.Text = "LTE小区CI";
            // 
            // olvBackEspri
            // 
            this.olvBackEspri.HeaderFont = null;
            this.olvBackEspri.Text = "LTE小区EARFCN";
            // 
            // olvbACKpci
            // 
            this.olvbACKpci.HeaderFont = null;
            this.olvbACKpci.Text = "LTE小区PCI";
            // 
            // olvSCell_Distance
            // 
            this.olvSCell_Distance.HeaderFont = null;
            this.olvSCell_Distance.Text = "LTE小区与事件距离";
            // 
            // olvSCell_RSRP
            // 
            this.olvSCell_RSRP.HeaderFont = null;
            this.olvSCell_RSRP.Text = "LTE小区RSRP均值";
            // 
            // olvSCell_SINR
            // 
            this.olvSCell_SINR.HeaderFont = null;
            this.olvSCell_SINR.Text = "LTE小区SINR均值";
            // 
            // olvSCell_RSRQ
            // 
            this.olvSCell_RSRQ.HeaderFont = null;
            this.olvSCell_RSRQ.Text = "LTE小区RSRQ均值";
            // 
            // olvSCell_RSSI
            // 
            this.olvSCell_RSSI.HeaderFont = null;
            this.olvSCell_RSSI.Text = "LTE小区RSSI均值";
            // 
            // olvNearLTECI
            // 
            this.olvNearLTECI.HeaderFont = null;
            this.olvNearLTECI.Text = "距离最近LTE小区CI";
            // 
            // olvNearLTEName
            // 
            this.olvNearLTEName.HeaderFont = null;
            this.olvNearLTEName.Text = "距离最近LTE小区名称";
            // 
            // olvNearLTEFre
            // 
            this.olvNearLTEFre.HeaderFont = null;
            this.olvNearLTEFre.Text = "距离最近LTE小区频点";
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCloseAll,
            this.toolStripMenuItem1,
            this.miExport2Xls});
            this.contextMenuStrip.Name = "contextMenuStrip1";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 76);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCloseAll
            // 
            this.miCloseAll.Name = "miCloseAll";
            this.miCloseAll.Size = new System.Drawing.Size(129, 22);
            this.miCloseAll.Text = "全部合并";
            this.miCloseAll.Click += new System.EventHandler(this.miCloseAll_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(126, 6);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(129, 22);
            this.miExport2Xls.Text = "导出Excel";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // ZTLteFRFrequencyConVerifyInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1021, 502);
            this.Controls.Add(this.ListViewFRVerify);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTLteFRFrequencyConVerifyInfoForm";
            this.Text = "FR频点配置核查分析结果";
            ((System.ComponentModel.ISupportInitialize)(this.ListViewFRVerify)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView ListViewFRVerify;

        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCloseAll;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;

        private BrightIdeasSoftware.OLVColumn olvSN;
        private BrightIdeasSoftware.OLVColumn olvFileName;
        private BrightIdeasSoftware.OLVColumn olvEventTime;
        private BrightIdeasSoftware.OLVColumn olvEventLongitude;
        private BrightIdeasSoftware.OLVColumn olvEventLatitude;
        private BrightIdeasSoftware.OLVColumn olvGSM_Name;
        private BrightIdeasSoftware.OLVColumn olvGSM_LAC;
        private BrightIdeasSoftware.OLVColumn olvGSM_CI;
        private BrightIdeasSoftware.OLVColumn olvGSM_Distance;
        private BrightIdeasSoftware.OLVColumn olvGSMAvgQual;
        private BrightIdeasSoftware.OLVColumn olvGSMAvgRxLev;
        private BrightIdeasSoftware.OLVColumn olvHasFreON;
        private BrightIdeasSoftware.OLVColumn olvChannelReleList;
        private BrightIdeasSoftware.OLVColumn olvSCell_Name;
        private BrightIdeasSoftware.OLVColumn olvSCell_BaseName;
        private BrightIdeasSoftware.OLVColumn olvSCell_LAC;
        private BrightIdeasSoftware.OLVColumn olvSCell_ECI;
        private BrightIdeasSoftware.OLVColumn olvSCell_Distance;
        private BrightIdeasSoftware.OLVColumn olvSCell_RSRP;
        private BrightIdeasSoftware.OLVColumn olvSCell_SINR;
        private BrightIdeasSoftware.OLVColumn olvSCell_RSRQ;
        private BrightIdeasSoftware.OLVColumn olvSCell_RSSI;
        private BrightIdeasSoftware.OLVColumn olvNearLTECI;
        private BrightIdeasSoftware.OLVColumn olvNearLTEName;
        private BrightIdeasSoftware.OLVColumn olvNearLTEFre;
        private BrightIdeasSoftware.OLVColumn olvGridName;
        private BrightIdeasSoftware.OLVColumn olvBackEspri;
        private BrightIdeasSoftware.OLVColumn olvbACKpci;
    }
}