﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTCellSetBriefSetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.radBtnMaiCell = new System.Windows.Forms.CheckBox();
            this.radBtnNearCell = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbContainNotGrid = new System.Windows.Forms.CheckBox();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(219, 118);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(311, 118);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 4;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // radBtnMaiCell
            // 
            this.radBtnMaiCell.AutoSize = true;
            this.radBtnMaiCell.Checked = true;
            this.radBtnMaiCell.CheckState = System.Windows.Forms.CheckState.Checked;
            this.radBtnMaiCell.Location = new System.Drawing.Point(28, 39);
            this.radBtnMaiCell.Name = "radBtnMaiCell";
            this.radBtnMaiCell.Size = new System.Drawing.Size(74, 18);
            this.radBtnMaiCell.TabIndex = 5;
            this.radBtnMaiCell.Text = "主服小区";
            this.radBtnMaiCell.UseVisualStyleBackColor = true;
            // 
            // radBtnNearCell
            // 
            this.radBtnNearCell.AutoSize = true;
            this.radBtnNearCell.Location = new System.Drawing.Point(117, 39);
            this.radBtnNearCell.Name = "radBtnNearCell";
            this.radBtnNearCell.Size = new System.Drawing.Size(62, 18);
            this.radBtnNearCell.TabIndex = 6;
            this.radBtnNearCell.Text = "邻小区";
            this.radBtnNearCell.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.cbContainNotGrid);
            this.groupBox1.Controls.Add(this.radBtnMaiCell);
            this.groupBox1.Controls.Add(this.radBtnNearCell);
            this.groupBox1.Location = new System.Drawing.Point(22, 25);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(364, 75);
            this.groupBox1.TabIndex = 7;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "请选择查询类型，无法定位网格小区可不选";
            // 
            // cbContainNotGrid
            // 
            this.cbContainNotGrid.AutoSize = true;
            this.cbContainNotGrid.Location = new System.Drawing.Point(197, 39);
            this.cbContainNotGrid.Name = "cbContainNotGrid";
            this.cbContainNotGrid.Size = new System.Drawing.Size(146, 18);
            this.cbContainNotGrid.TabIndex = 7;
            this.cbContainNotGrid.Text = "包含无法定位网格小区";
            this.cbContainNotGrid.UseVisualStyleBackColor = true;
            // 
            // IsQueryNearCellForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(410, 153);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "IsQueryNearCellForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "选择查询的小区类型";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.CheckBox radBtnMaiCell;
        private System.Windows.Forms.CheckBox radBtnNearCell;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox cbContainNotGrid;
    }
}