﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Drawing2D;

using MasterCom.MTGis;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.LteSignalImsi
{
    class ReplayEventByImsiLayer
    {
        public ReplayEventByImsiLayer()
        {
        }

        public bool IsGisIntervalLimit
        {
            get;
            set;
        }

        public int GisIntervalMinutes
        {
            get;
            set;
        }

        public void DrawArrow(bool isDraw)
        {
            if (!isDraw)
            {
                //TempLayer.Instance.Clear();
                evtList = null;
            }
            else
            {
                evtList = new List<Event>();
                MainModel mainModel = MainModel.GetInstance();
                foreach (DTFileDataManager fileManager in mainModel.DTDataManager.FileDataManagers)
                {
                    foreach (Event evt in fileManager.Events)
                    {
                        if (evt.Longitude < 1 || evt.Latitude < 1)
                        {
                            continue;
                        }
                        evtList.Add(evt);
                    }
                }
                TempLayer.Instance.Draw(DrawHandler, true);
            }
        }

        private void DrawHandler(Rectangle clientRect, Rectangle updateRect, Graphics graphics, MapOperation mop)
        {
            if (evtList == null || evtList.Count == 0)
            {
                return;
            }

            AdjustableArrowCap lineCap = new AdjustableArrowCap(4, 4);
            lineCap.Filled = false;
            Pen oddPen = new Pen(Color.Blue, 2.5F);
            oddPen.CustomEndCap = lineCap;
            Pen evenPen = new Pen(Color.Red, 2.5F);
            evenPen.CustomEndCap = lineCap;

            int lineCnt = 0;
            for (int i = 0; i < evtList.Count - 1; ++i)
            {
                Event sEvt = evtList[i];
                Event eEvt = evtList[i + 1];
                if (sEvt.Longitude == eEvt.Longitude && sEvt.Latitude == eEvt.Latitude)
                {
                    continue;
                }

                if (IsGisIntervalLimit && (eEvt.DateTime - sEvt.DateTime).TotalMinutes > GisIntervalMinutes)
                {
                    lineCnt = 0;
                    continue;
                }

                PointF sp, ep;
                DbPoint startPoint = new DbPoint(sEvt.Longitude, sEvt.Latitude);
                DbPoint endPoint = new DbPoint(eEvt.Longitude, eEvt.Latitude);
                mop.ToDisplay(startPoint, out sp);
                mop.ToDisplay(endPoint, out ep);

                graphics.DrawLine(++lineCnt % 2 == 1 ? oddPen : evenPen,
                    sp, ep);
            }
        }

        private List<Event> evtList;
    }
}
