using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    partial class CellInfoForm : Form
    {
        public CellInfoForm(MainModel mainModel, Cell cell)
        {
            InitializeComponent();
            this.mainModel = mainModel;
            this.cell = cell;
            textBoxID.Text = cell.ID.ToString();
            textBoxName.Text = cell.Name;
            textBoxCode.Text = cell.Code;
            textBoxLAC.Text = cell.LAC.ToString();
            textBoxCI.Text = cell.CI.ToString();
            textBoxBCCH.Text = cell.BCCH.ToString();
            textBoxBSIC.Text = cell.BSIC.ToString();
            textBoxHOP.Text = cell.HOP.ToString();
            textBoxTCH.Text = cell.TCHDescription;
            textBoxType.Text = cell.Type.ToString();
            textBoxBand.Text = cell.BandType.ToString();
            textBoxDirection.Text = cell.DirectionType.ToString();
            textBoxMSC.Text = cell.BelongBTS.BelongBSC.BelongMSC.Name;
            textBoxBSC.Text = cell.BelongBTS.BelongBSC.Name;
            textBoxBTS.Text = cell.BelongBTS.Name;
            textBoxLongitude.Text = cell.Longitude.ToString();
            textBoxLatitude.Text = cell.Latitude.ToString();
            List<Antenna> antennas = new List<Antenna>();
            antennas.AddRange(cell.Antennas);
            listBoxAntenna.DataSource = antennas;
            listBoxAntenna.DisplayMember = "SimpleInfo";
            List<Repeater> repeaters = new List<Repeater>();
            foreach (Repeater repeater in cell.Repeaters)
            {
                if (repeater.Current == repeater)
                {
                    repeaters.Add(repeater);
                }
            }
            listBoxRepeater.DataSource = repeaters;
            listBoxRepeater.DisplayMember = "Name";
            checkButtonState();
        }

        private void listBoxRepeater_SelectedIndexChanged(object sender, EventArgs e)
        {
            checkButtonState();
        }

        private void buttonBTSInfo_Click(object sender, EventArgs e)
        {
            new BTSInfoForm(mainModel, cell.BelongBTS).Show(Owner);
        }

        private void buttonNeighbours_Click(object sender, EventArgs e)
        {
            new NeighboursForm(mainModel, cell).Show(Owner);
        }

        private void buttonInterference_Click(object sender, EventArgs e)
        {
            int mode = 0;
            if (radioButtonBCCHTCH.Checked)
            {
                mode = 0;
            }
            else if (radioButtonBCCH.Checked)
            {
                mode = 1;
            }
            else if (radioButtonTCH.Checked)
            {
                mode = 2;
            }
            new InterferenceForm(mainModel, cell, mode).Show(Owner);
        }

        private void buttonCoBSIC_Click(object sender, EventArgs e)
        {
            new CoBSICForm(mainModel, cell).Show(Owner);
        }

        private void buttonLocation_Click(object sender, EventArgs e)
        {
            mainModel.SelectedCell = cell;
            mainModel.FireSelectedCellChanged(this);
        }

        private void buttonRepeaterInfo_Click(object sender, EventArgs e)
        {
            new RepeaterInfoForm(mainModel, (Repeater)listBoxRepeater.SelectedItem).Show(Owner);
        }

        private void buttonLocationRepeater_Click(object sender, EventArgs e)
        {
            mainModel.SelectedRepeater = (Repeater)listBoxRepeater.SelectedItem;
            mainModel.FireSelectedRepeaterChanged(this);
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void checkButtonState()
        {
            bool repeaterSelected = listBoxRepeater.SelectedItem != null;
            buttonRepeaterInfo.Enabled = repeaterSelected;
            buttonLocationRepeater.Enabled = repeaterSelected;
        }

        private void InitializeComponent()
        {
            System.Windows.Forms.Label labelID;
            System.Windows.Forms.Label labelCI;
            System.Windows.Forms.Label labelBCCH;
            System.Windows.Forms.Label labelBSIC;
            System.Windows.Forms.Label labelTCH;
            System.Windows.Forms.Label labelHOP;
            System.Windows.Forms.Label label1;
            System.Windows.Forms.Label label3;
            System.Windows.Forms.Label label2;
            System.Windows.Forms.Label label4;
            System.Windows.Forms.Label label5;
            System.Windows.Forms.Label label6;
            System.Windows.Forms.Label label7;
            System.Windows.Forms.GroupBox groupBox2;
            System.Windows.Forms.GroupBox groupBox1;
            System.Windows.Forms.Label label13;
            System.Windows.Forms.Label label12;
            System.Windows.Forms.Label label10;
            System.Windows.Forms.Label label11;
            System.Windows.Forms.Label label9;
            System.Windows.Forms.Label label8;
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CellInfoForm));
            this.buttonRepeaterInfo = new System.Windows.Forms.Button();
            this.buttonLocationRepeater = new System.Windows.Forms.Button();
            this.radioButtonTCH = new System.Windows.Forms.RadioButton();
            this.radioButtonBCCH = new System.Windows.Forms.RadioButton();
            this.radioButtonBCCHTCH = new System.Windows.Forms.RadioButton();
            this.buttonCoBSIC = new System.Windows.Forms.Button();
            this.buttonInterference = new System.Windows.Forms.Button();
            this.buttonNeighbours = new System.Windows.Forms.Button();
            this.buttonLocation = new System.Windows.Forms.Button();
            this.listBoxRepeater = new System.Windows.Forms.ListBox();
            this.listBoxAntenna = new System.Windows.Forms.ListBox();
            this.textBoxBTS = new System.Windows.Forms.TextBox();
            this.textBoxLatitude = new System.Windows.Forms.TextBox();
            this.textBoxDirection = new System.Windows.Forms.TextBox();
            this.buttonBTSInfo = new System.Windows.Forms.Button();
            this.textBoxBand = new System.Windows.Forms.TextBox();
            this.textBoxType = new System.Windows.Forms.TextBox();
            this.textBoxLongitude = new System.Windows.Forms.TextBox();
            this.textBoxMSC = new System.Windows.Forms.TextBox();
            this.textBoxBSC = new System.Windows.Forms.TextBox();
            this.textBoxCode = new System.Windows.Forms.TextBox();
            this.textBoxName = new System.Windows.Forms.TextBox();
            this.textBoxLAC = new System.Windows.Forms.TextBox();
            this.textBoxHOP = new System.Windows.Forms.TextBox();
            this.textBoxTCH = new System.Windows.Forms.TextBox();
            this.textBoxBSIC = new System.Windows.Forms.TextBox();
            this.textBoxBCCH = new System.Windows.Forms.TextBox();
            this.textBoxCI = new System.Windows.Forms.TextBox();
            this.textBoxID = new System.Windows.Forms.TextBox();
            this.buttonOK = new System.Windows.Forms.Button();
            labelID = new System.Windows.Forms.Label();
            labelCI = new System.Windows.Forms.Label();
            labelBCCH = new System.Windows.Forms.Label();
            labelBSIC = new System.Windows.Forms.Label();
            labelTCH = new System.Windows.Forms.Label();
            labelHOP = new System.Windows.Forms.Label();
            label1 = new System.Windows.Forms.Label();
            label3 = new System.Windows.Forms.Label();
            label2 = new System.Windows.Forms.Label();
            label4 = new System.Windows.Forms.Label();
            label5 = new System.Windows.Forms.Label();
            label6 = new System.Windows.Forms.Label();
            label7 = new System.Windows.Forms.Label();
            groupBox2 = new System.Windows.Forms.GroupBox();
            groupBox1 = new System.Windows.Forms.GroupBox();
            label13 = new System.Windows.Forms.Label();
            label12 = new System.Windows.Forms.Label();
            label10 = new System.Windows.Forms.Label();
            label11 = new System.Windows.Forms.Label();
            label9 = new System.Windows.Forms.Label();
            label8 = new System.Windows.Forms.Label();
            groupBox2.SuspendLayout();
            groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelID
            // 
            labelID.AutoSize = true;
            labelID.Location = new System.Drawing.Point(6, 22);
            labelID.Name = "labelID";
            labelID.Size = new System.Drawing.Size(21, 13);
            labelID.TabIndex = 0;
            labelID.Text = "&ID:";
            // 
            // labelCI
            // 
            labelCI.AutoSize = true;
            labelCI.Location = new System.Drawing.Point(137, 48);
            labelCI.Name = "labelCI";
            labelCI.Size = new System.Drawing.Size(20, 13);
            labelCI.TabIndex = 8;
            labelCI.Text = "&CI:";
            // 
            // labelBCCH
            // 
            labelBCCH.AutoSize = true;
            labelBCCH.Location = new System.Drawing.Point(6, 75);
            labelBCCH.Name = "labelBCCH";
            labelBCCH.Size = new System.Drawing.Size(39, 13);
            labelBCCH.TabIndex = 10;
            labelBCCH.Text = "&BCCH:";
            // 
            // labelBSIC
            // 
            labelBSIC.AutoSize = true;
            labelBSIC.Location = new System.Drawing.Point(137, 75);
            labelBSIC.Name = "labelBSIC";
            labelBSIC.Size = new System.Drawing.Size(34, 13);
            labelBSIC.TabIndex = 12;
            labelBSIC.Text = "&BSIC:";
            // 
            // labelTCH
            // 
            labelTCH.AutoSize = true;
            labelTCH.Location = new System.Drawing.Point(6, 100);
            labelTCH.Name = "labelTCH";
            labelTCH.Size = new System.Drawing.Size(32, 13);
            labelTCH.TabIndex = 16;
            labelTCH.Text = "&TCH:";
            // 
            // labelHOP
            // 
            labelHOP.AutoSize = true;
            labelHOP.Location = new System.Drawing.Point(267, 75);
            labelHOP.Name = "labelHOP";
            labelHOP.Size = new System.Drawing.Size(33, 13);
            labelHOP.TabIndex = 14;
            labelHOP.Text = "&HOP:";
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(6, 48);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(30, 13);
            label1.TabIndex = 6;
            label1.Text = "&LAC:";
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new System.Drawing.Point(267, 126);
            label3.Name = "label3";
            label3.Size = new System.Drawing.Size(23, 13);
            label3.TabIndex = 22;
            label3.Text = "&Dir:";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new System.Drawing.Point(137, 126);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(35, 13);
            label2.TabIndex = 20;
            label2.Text = "&Band:";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new System.Drawing.Point(6, 126);
            label4.Name = "label4";
            label4.Size = new System.Drawing.Size(34, 13);
            label4.TabIndex = 18;
            label4.Text = "&Type:";
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new System.Drawing.Point(6, 204);
            label5.Name = "label5";
            label5.Size = new System.Drawing.Size(34, 13);
            label5.TabIndex = 31;
            label5.Text = "&Long:";
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new System.Drawing.Point(137, 204);
            label6.Name = "label6";
            label6.Size = new System.Drawing.Size(25, 13);
            label6.TabIndex = 33;
            label6.Text = "&Lat:";
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Location = new System.Drawing.Point(6, 178);
            label7.Name = "label7";
            label7.Size = new System.Drawing.Size(31, 13);
            label7.TabIndex = 28;
            label7.Text = "&BTS:";
            // 
            // groupBox2
            // 
            groupBox2.Controls.Add(this.buttonRepeaterInfo);
            groupBox2.Controls.Add(this.buttonLocationRepeater);
            groupBox2.Controls.Add(this.radioButtonTCH);
            groupBox2.Controls.Add(this.radioButtonBCCH);
            groupBox2.Controls.Add(this.radioButtonBCCHTCH);
            groupBox2.Controls.Add(this.buttonCoBSIC);
            groupBox2.Controls.Add(this.buttonInterference);
            groupBox2.Controls.Add(this.buttonNeighbours);
            groupBox2.Controls.Add(this.buttonLocation);
            groupBox2.Location = new System.Drawing.Point(412, 12);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new System.Drawing.Size(121, 276);
            groupBox2.TabIndex = 1;
            groupBox2.TabStop = false;
            groupBox2.Text = "Operation";
            // 
            // buttonRepeaterInfo
            // 
            this.buttonRepeaterInfo.Location = new System.Drawing.Point(6, 204);
            this.buttonRepeaterInfo.Name = "buttonRepeaterInfo";
            this.buttonRepeaterInfo.Size = new System.Drawing.Size(109, 23);
            this.buttonRepeaterInfo.TabIndex = 8;
            this.buttonRepeaterInfo.Text = "&Repeater Info";
            this.buttonRepeaterInfo.UseVisualStyleBackColor = true;
            this.buttonRepeaterInfo.Click += new System.EventHandler(this.buttonRepeaterInfo_Click);
            // 
            // buttonLocationRepeater
            // 
            this.buttonLocationRepeater.Location = new System.Drawing.Point(6, 233);
            this.buttonLocationRepeater.Name = "buttonLocationRepeater";
            this.buttonLocationRepeater.Size = new System.Drawing.Size(109, 23);
            this.buttonLocationRepeater.TabIndex = 7;
            this.buttonLocationRepeater.Text = "&Location Repeater";
            this.buttonLocationRepeater.UseVisualStyleBackColor = true;
            this.buttonLocationRepeater.Click += new System.EventHandler(this.buttonLocationRepeater_Click);
            // 
            // radioButtonTCH
            // 
            this.radioButtonTCH.AutoSize = true;
            this.radioButtonTCH.Location = new System.Drawing.Point(9, 94);
            this.radioButtonTCH.Name = "radioButtonTCH";
            this.radioButtonTCH.Size = new System.Drawing.Size(71, 17);
            this.radioButtonTCH.TabIndex = 3;
            this.radioButtonTCH.Text = "&TCH Only";
            this.radioButtonTCH.UseVisualStyleBackColor = true;
            // 
            // radioButtonBCCH
            // 
            this.radioButtonBCCH.AutoSize = true;
            this.radioButtonBCCH.Location = new System.Drawing.Point(9, 71);
            this.radioButtonBCCH.Name = "radioButtonBCCH";
            this.radioButtonBCCH.Size = new System.Drawing.Size(78, 17);
            this.radioButtonBCCH.TabIndex = 2;
            this.radioButtonBCCH.Text = "&BCCH Only";
            this.radioButtonBCCH.UseVisualStyleBackColor = true;
            // 
            // radioButtonBCCHTCH
            // 
            this.radioButtonBCCHTCH.AutoSize = true;
            this.radioButtonBCCHTCH.Checked = true;
            this.radioButtonBCCHTCH.Location = new System.Drawing.Point(9, 48);
            this.radioButtonBCCHTCH.Name = "radioButtonBCCHTCH";
            this.radioButtonBCCHTCH.Size = new System.Drawing.Size(88, 17);
            this.radioButtonBCCHTCH.TabIndex = 1;
            this.radioButtonBCCHTCH.TabStop = true;
            this.radioButtonBCCHTCH.Text = "&BCCH && TCH";
            this.radioButtonBCCHTCH.UseVisualStyleBackColor = true;
            // 
            // buttonCoBSIC
            // 
            this.buttonCoBSIC.Location = new System.Drawing.Point(6, 146);
            this.buttonCoBSIC.Name = "buttonCoBSIC";
            this.buttonCoBSIC.Size = new System.Drawing.Size(109, 23);
            this.buttonCoBSIC.TabIndex = 5;
            this.buttonCoBSIC.Text = "&Co-BSIC...";
            this.buttonCoBSIC.UseVisualStyleBackColor = true;
            this.buttonCoBSIC.Click += new System.EventHandler(this.buttonCoBSIC_Click);
            // 
            // buttonInterference
            // 
            this.buttonInterference.Location = new System.Drawing.Point(6, 117);
            this.buttonInterference.Name = "buttonInterference";
            this.buttonInterference.Size = new System.Drawing.Size(109, 23);
            this.buttonInterference.TabIndex = 4;
            this.buttonInterference.Text = "&Interference...";
            this.buttonInterference.UseVisualStyleBackColor = true;
            this.buttonInterference.Click += new System.EventHandler(this.buttonInterference_Click);
            // 
            // buttonNeighbours
            // 
            this.buttonNeighbours.Location = new System.Drawing.Point(6, 19);
            this.buttonNeighbours.Name = "buttonNeighbours";
            this.buttonNeighbours.Size = new System.Drawing.Size(109, 23);
            this.buttonNeighbours.TabIndex = 0;
            this.buttonNeighbours.Text = "&Neighbours...";
            this.buttonNeighbours.UseVisualStyleBackColor = true;
            this.buttonNeighbours.Click += new System.EventHandler(this.buttonNeighbours_Click);
            // 
            // buttonLocation
            // 
            this.buttonLocation.Location = new System.Drawing.Point(6, 175);
            this.buttonLocation.Name = "buttonLocation";
            this.buttonLocation.Size = new System.Drawing.Size(109, 23);
            this.buttonLocation.TabIndex = 6;
            this.buttonLocation.Text = "&Location";
            this.buttonLocation.UseVisualStyleBackColor = true;
            this.buttonLocation.Click += new System.EventHandler(this.buttonLocation_Click);
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(label13);
            groupBox1.Controls.Add(this.listBoxRepeater);
            groupBox1.Controls.Add(label12);
            groupBox1.Controls.Add(this.listBoxAntenna);
            groupBox1.Controls.Add(this.textBoxBTS);
            groupBox1.Controls.Add(label10);
            groupBox1.Controls.Add(this.textBoxLatitude);
            groupBox1.Controls.Add(this.textBoxDirection);
            groupBox1.Controls.Add(label11);
            groupBox1.Controls.Add(label3);
            groupBox1.Controls.Add(this.buttonBTSInfo);
            groupBox1.Controls.Add(this.textBoxBand);
            groupBox1.Controls.Add(label9);
            groupBox1.Controls.Add(this.textBoxType);
            groupBox1.Controls.Add(label2);
            groupBox1.Controls.Add(label8);
            groupBox1.Controls.Add(this.textBoxLongitude);
            groupBox1.Controls.Add(label7);
            groupBox1.Controls.Add(label4);
            groupBox1.Controls.Add(this.textBoxMSC);
            groupBox1.Controls.Add(label5);
            groupBox1.Controls.Add(this.textBoxBSC);
            groupBox1.Controls.Add(label6);
            groupBox1.Controls.Add(this.textBoxCode);
            groupBox1.Controls.Add(this.textBoxName);
            groupBox1.Controls.Add(this.textBoxLAC);
            groupBox1.Controls.Add(label1);
            groupBox1.Controls.Add(this.textBoxHOP);
            groupBox1.Controls.Add(this.textBoxTCH);
            groupBox1.Controls.Add(this.textBoxBSIC);
            groupBox1.Controls.Add(this.textBoxBCCH);
            groupBox1.Controls.Add(this.textBoxCI);
            groupBox1.Controls.Add(labelHOP);
            groupBox1.Controls.Add(labelTCH);
            groupBox1.Controls.Add(labelBSIC);
            groupBox1.Controls.Add(labelBCCH);
            groupBox1.Controls.Add(labelCI);
            groupBox1.Controls.Add(this.textBoxID);
            groupBox1.Controls.Add(labelID);
            groupBox1.Location = new System.Drawing.Point(12, 12);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new System.Drawing.Size(394, 276);
            groupBox1.TabIndex = 0;
            groupBox1.TabStop = false;
            groupBox1.Text = "Info";
            // 
            // label13
            // 
            label13.AutoSize = true;
            label13.Location = new System.Drawing.Point(267, 152);
            label13.Name = "label13";
            label13.Size = new System.Drawing.Size(54, 13);
            label13.TabIndex = 37;
            label13.Text = "&Repeater:";
            // 
            // listBoxRepeater
            // 
            this.listBoxRepeater.FormattingEnabled = true;
            this.listBoxRepeater.HorizontalScrollbar = true;
            this.listBoxRepeater.Location = new System.Drawing.Point(270, 175);
            this.listBoxRepeater.Name = "listBoxRepeater";
            this.listBoxRepeater.Size = new System.Drawing.Size(118, 95);
            this.listBoxRepeater.TabIndex = 38;
            this.listBoxRepeater.SelectedIndexChanged += new System.EventHandler(this.listBoxRepeater_SelectedIndexChanged);
            this.listBoxRepeater.DoubleClick += new System.EventHandler(this.buttonLocationRepeater_Click);
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Location = new System.Drawing.Point(8, 231);
            label12.Name = "label12";
            label12.Size = new System.Drawing.Size(32, 13);
            label12.TabIndex = 35;
            label12.Text = "&Ante:";
            // 
            // listBoxAntenna
            // 
            this.listBoxAntenna.FormattingEnabled = true;
            this.listBoxAntenna.HorizontalScrollbar = true;
            this.listBoxAntenna.Location = new System.Drawing.Point(51, 227);
            this.listBoxAntenna.Name = "listBoxAntenna";
            this.listBoxAntenna.Size = new System.Drawing.Size(210, 43);
            this.listBoxAntenna.TabIndex = 36;
            // 
            // textBoxBTS
            // 
            this.textBoxBTS.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxBTS.Location = new System.Drawing.Point(51, 175);
            this.textBoxBTS.Name = "textBoxBTS";
            this.textBoxBTS.ReadOnly = true;
            this.textBoxBTS.Size = new System.Drawing.Size(80, 20);
            this.textBoxBTS.TabIndex = 29;
            // 
            // label10
            // 
            label10.AutoSize = true;
            label10.Location = new System.Drawing.Point(6, 152);
            label10.Name = "label10";
            label10.Size = new System.Drawing.Size(33, 13);
            label10.TabIndex = 24;
            label10.Text = "&MSC:";
            // 
            // textBoxLatitude
            // 
            this.textBoxLatitude.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxLatitude.Location = new System.Drawing.Point(181, 201);
            this.textBoxLatitude.Name = "textBoxLatitude";
            this.textBoxLatitude.ReadOnly = true;
            this.textBoxLatitude.Size = new System.Drawing.Size(80, 20);
            this.textBoxLatitude.TabIndex = 34;
            // 
            // textBoxDirection
            // 
            this.textBoxDirection.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxDirection.Location = new System.Drawing.Point(308, 123);
            this.textBoxDirection.Name = "textBoxDirection";
            this.textBoxDirection.ReadOnly = true;
            this.textBoxDirection.Size = new System.Drawing.Size(80, 20);
            this.textBoxDirection.TabIndex = 23;
            // 
            // label11
            // 
            label11.AutoSize = true;
            label11.Location = new System.Drawing.Point(137, 152);
            label11.Name = "label11";
            label11.Size = new System.Drawing.Size(31, 13);
            label11.TabIndex = 26;
            label11.Text = "&BSC:";
            // 
            // buttonBTSInfo
            // 
            this.buttonBTSInfo.Location = new System.Drawing.Point(137, 175);
            this.buttonBTSInfo.Name = "buttonBTSInfo";
            this.buttonBTSInfo.Size = new System.Drawing.Size(42, 20);
            this.buttonBTSInfo.TabIndex = 30;
            this.buttonBTSInfo.Text = "&Info...";
            this.buttonBTSInfo.UseVisualStyleBackColor = true;
            this.buttonBTSInfo.Click += new System.EventHandler(this.buttonBTSInfo_Click);
            // 
            // textBoxBand
            // 
            this.textBoxBand.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxBand.Location = new System.Drawing.Point(181, 123);
            this.textBoxBand.Name = "textBoxBand";
            this.textBoxBand.ReadOnly = true;
            this.textBoxBand.Size = new System.Drawing.Size(80, 20);
            this.textBoxBand.TabIndex = 21;
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Location = new System.Drawing.Point(267, 22);
            label9.Name = "label9";
            label9.Size = new System.Drawing.Size(35, 13);
            label9.TabIndex = 4;
            label9.Text = "&Code:";
            // 
            // textBoxType
            // 
            this.textBoxType.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxType.Location = new System.Drawing.Point(51, 123);
            this.textBoxType.Name = "textBoxType";
            this.textBoxType.ReadOnly = true;
            this.textBoxType.Size = new System.Drawing.Size(80, 20);
            this.textBoxType.TabIndex = 19;
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Location = new System.Drawing.Point(137, 22);
            label8.Name = "label8";
            label8.Size = new System.Drawing.Size(38, 13);
            label8.TabIndex = 2;
            label8.Text = "&Name:";
            // 
            // textBoxLongitude
            // 
            this.textBoxLongitude.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxLongitude.Location = new System.Drawing.Point(51, 201);
            this.textBoxLongitude.Name = "textBoxLongitude";
            this.textBoxLongitude.ReadOnly = true;
            this.textBoxLongitude.Size = new System.Drawing.Size(80, 20);
            this.textBoxLongitude.TabIndex = 32;
            // 
            // textBoxMSC
            // 
            this.textBoxMSC.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxMSC.Location = new System.Drawing.Point(51, 149);
            this.textBoxMSC.Name = "textBoxMSC";
            this.textBoxMSC.ReadOnly = true;
            this.textBoxMSC.Size = new System.Drawing.Size(80, 20);
            this.textBoxMSC.TabIndex = 25;
            // 
            // textBoxBSC
            // 
            this.textBoxBSC.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxBSC.Location = new System.Drawing.Point(181, 149);
            this.textBoxBSC.Name = "textBoxBSC";
            this.textBoxBSC.ReadOnly = true;
            this.textBoxBSC.Size = new System.Drawing.Size(80, 20);
            this.textBoxBSC.TabIndex = 27;
            // 
            // textBoxCode
            // 
            this.textBoxCode.AcceptsReturn = true;
            this.textBoxCode.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxCode.Location = new System.Drawing.Point(308, 19);
            this.textBoxCode.Name = "textBoxCode";
            this.textBoxCode.ReadOnly = true;
            this.textBoxCode.Size = new System.Drawing.Size(80, 20);
            this.textBoxCode.TabIndex = 5;
            this.textBoxCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // textBoxName
            // 
            this.textBoxName.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxName.Location = new System.Drawing.Point(181, 19);
            this.textBoxName.Name = "textBoxName";
            this.textBoxName.ReadOnly = true;
            this.textBoxName.Size = new System.Drawing.Size(80, 20);
            this.textBoxName.TabIndex = 3;
            this.textBoxName.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // textBoxLAC
            // 
            this.textBoxLAC.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxLAC.Location = new System.Drawing.Point(51, 45);
            this.textBoxLAC.Name = "textBoxLAC";
            this.textBoxLAC.ReadOnly = true;
            this.textBoxLAC.Size = new System.Drawing.Size(80, 20);
            this.textBoxLAC.TabIndex = 7;
            // 
            // textBoxHOP
            // 
            this.textBoxHOP.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxHOP.Location = new System.Drawing.Point(308, 71);
            this.textBoxHOP.Name = "textBoxHOP";
            this.textBoxHOP.ReadOnly = true;
            this.textBoxHOP.Size = new System.Drawing.Size(80, 20);
            this.textBoxHOP.TabIndex = 15;
            // 
            // textBoxTCH
            // 
            this.textBoxTCH.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxTCH.Location = new System.Drawing.Point(51, 97);
            this.textBoxTCH.Name = "textBoxTCH";
            this.textBoxTCH.ReadOnly = true;
            this.textBoxTCH.Size = new System.Drawing.Size(337, 20);
            this.textBoxTCH.TabIndex = 17;
            // 
            // textBoxBSIC
            // 
            this.textBoxBSIC.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxBSIC.Location = new System.Drawing.Point(181, 71);
            this.textBoxBSIC.Name = "textBoxBSIC";
            this.textBoxBSIC.ReadOnly = true;
            this.textBoxBSIC.Size = new System.Drawing.Size(80, 20);
            this.textBoxBSIC.TabIndex = 13;
            // 
            // textBoxBCCH
            // 
            this.textBoxBCCH.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxBCCH.Location = new System.Drawing.Point(51, 71);
            this.textBoxBCCH.Name = "textBoxBCCH";
            this.textBoxBCCH.ReadOnly = true;
            this.textBoxBCCH.Size = new System.Drawing.Size(80, 20);
            this.textBoxBCCH.TabIndex = 11;
            // 
            // textBoxCI
            // 
            this.textBoxCI.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxCI.Location = new System.Drawing.Point(181, 45);
            this.textBoxCI.Name = "textBoxCI";
            this.textBoxCI.ReadOnly = true;
            this.textBoxCI.Size = new System.Drawing.Size(80, 20);
            this.textBoxCI.TabIndex = 9;
            // 
            // textBoxID
            // 
            this.textBoxID.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxID.Location = new System.Drawing.Point(51, 19);
            this.textBoxID.Name = "textBoxID";
            this.textBoxID.ReadOnly = true;
            this.textBoxID.Size = new System.Drawing.Size(80, 20);
            this.textBoxID.TabIndex = 1;
            // 
            // buttonOK
            // 
            this.buttonOK.Anchor = (System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right);
            this.buttonOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.buttonOK.Location = new System.Drawing.Point(446, 294);
            this.buttonOK.Name = "buttonOK";
            this.buttonOK.Size = new System.Drawing.Size(87, 23);
            this.buttonOK.TabIndex = 2;
            this.buttonOK.Text = "&OK";
            this.buttonOK.UseVisualStyleBackColor = true;
            this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
            // 
            // CellInfoForm
            // 
            this.AcceptButton = this.buttonOK;
            this.CancelButton = this.buttonOK;
            this.ClientSize = new System.Drawing.Size(545, 329);
            this.Controls.Add(groupBox1);
            this.Controls.Add(groupBox2);
            this.Controls.Add(this.buttonOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "CellInfoForm";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Cell Info";
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        private readonly MainModel mainModel;

        private readonly Cell cell;

        private TextBox textBoxID;

        private TextBox textBoxName;

        private TextBox textBoxCode;

        private TextBox textBoxLAC;

        private TextBox textBoxCI;

        private TextBox textBoxBCCH;

        private TextBox textBoxBSIC;

        private TextBox textBoxTCH;

        private TextBox textBoxHOP;

        private TextBox textBoxType;

        private TextBox textBoxBand;

        private TextBox textBoxDirection;

        private TextBox textBoxMSC;

        private TextBox textBoxBSC;

        private TextBox textBoxBTS;

        private Button buttonBTSInfo;

        private TextBox textBoxLongitude;

        private TextBox textBoxLatitude;

        private ListBox listBoxAntenna;

        private ListBox listBoxRepeater;

        private Button buttonNeighbours;

        private RadioButton radioButtonBCCHTCH;

        private RadioButton radioButtonBCCH;

        private RadioButton radioButtonTCH;

        private Button buttonInterference;

        private Button buttonCoBSIC;

        private Button buttonLocation;

        private Button buttonRepeaterInfo;

        private Button buttonLocationRepeater;

        private Button buttonOK;
    }
}
