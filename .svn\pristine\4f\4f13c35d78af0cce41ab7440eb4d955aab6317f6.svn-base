﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRLTECollaborativeAnaByGridDlg : BaseDialog
    {
        public NRLTECollaborativeAnaByGridDlg()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }
        
        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        public void SetCondition(NRLTECollaborativeAnaByGridCondition condition)
        {
            if (condition == null)
            {
                return;
            }
            num4GBetterThan5G.Value = condition.Num4GBetterThan5G;
            num5GBetterThan4G.Value = condition.Num5GBetterThan4G;
            num4GWeakCover.Value = condition.Num4GWeakCover;
            num5GWeakCover.Value = condition.Num5GWeakCover;
            chkContainsNRLTE.Checked = condition.ContainsNRLTE;
            chkContiansNone.Checked = condition.ContiansNone;
        }

        public NRLTECollaborativeAnaByGridCondition GetCondition()
        {
            NRLTECollaborativeAnaByGridCondition condition = new NRLTECollaborativeAnaByGridCondition();
            condition.Num4GBetterThan5G = (int)num4GBetterThan5G.Value;
            condition.Num5GBetterThan4G = (int)num5GBetterThan4G.Value;
            condition.Num4GWeakCover = (int)num4GWeakCover.Value;
            condition.Num5GWeakCover = (int)num5GWeakCover.Value;
            condition.ContainsNRLTE = chkContainsNRLTE.Checked;
            condition.ContiansNone = chkContiansNone.Checked;
            return condition;
        }
    }

    public class NRLTECollaborativeAnaByGridCondition : ZTNRLTECollaborativeAnaCondition
    {
        public bool ContainsNRLTE { get; set; }
        public bool ContiansNone { get; set; }
    }
}
