﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    [Serializable]
    public class WeakCoverCause : CauseBase
    {
        public WeakCoverCause()
        {
            AddSubReason(new WeakCoverNoSite());
            AddSubReason(new WeakCoverLackNbCell());
            AddSubReason(new WeakCoverSCell());
            AddSubReason(new WeakCoverNCell());
        }
        public override string Name
        {
            get
            {
                return "弱覆盖";
            }
        }
        
        public float RSRPMax { get; set; } = -95;

        public override string Desc
        {
            get
            {
                return string.Format("主服和邻区信号强度都≤{0}", RSRPMax);
            }
        }

        public override string Suggestion
        {
            get { return null; }
        }

        public override void Judge(LowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP)
        {
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                if (!segItem.IsNeedJudge(pnt))
                {
                    continue;
                }
                float? rsrp = (float?)GetRSRP(pnt);
                if (rsrp <= RSRPMax)
                {
                    bool nGreater = getGreater(pnt, ref rsrp);
                    if (nGreater)
                    {
                        continue;
                    }
                    dealLowSpeedSeg(segItem, pnt);
                    if (!segItem.NeedJudge)
                    {
                        return;
                    }
                }
            }
        }

        private bool getGreater(TestPoint pnt, ref float? rsrp)
        {
            bool nGreater = false;
            for (int i = 0; i < 10; i++)
            {
                object objV = GetNRSRP(pnt, i);
                if (objV == null)
                {
                    continue;
                }
                rsrp = float.Parse(objV.ToString());
                if (rsrp > RSRPMax)
                {
                    nGreater = true;
                    break;
                }
            }

            return nGreater;
        }

        private void dealLowSpeedSeg(LowSpeedSeg segItem, TestPoint pnt)
        {
            foreach (CauseBase subReason in SubCauses)
            {
                if (!segItem.IsNeedJudge(pnt))
                {
                    break;
                }
                subReason.JudgeSinglePoint(segItem, pnt);
            }
            if (segItem.IsNeedJudge(pnt))
            {
                UnknowReason r = new UnknowReason();
                r.Parent = this;
                segItem.SetReason(new LowSpeedPointDetail(pnt, r));
            }
        }

        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            return tp["lte_RSRP"];
        }
        protected object GetNRSRP(TestPoint tp, int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_NCell_RSRP", index];
            }
            return tp["lte_NCell_RSRP", index];
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;

                paramDic["rsrpMax"] = this.RSRPMax;

                List<object> list = new List<object>();
                foreach (CauseBase cause in SubCauses)
                {
                    list.Add(cause.CfgParam);
                }
                paramDic["SubCauseSet"] = list;

                return paramDic;

            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RSRPMax = (float)value["rsrpMax"];

                SubCauses = new List<CauseBase>();
                List<object> list = value["SubCauseSet"] as List<object>;
                foreach (object item in list)
                {
                    Dictionary<string, object> dic = item as Dictionary<string, object>;
                    string typeName = dic["TypeName"].ToString();
                    System.Reflection.Assembly assembly = System.Reflection.Assembly.GetExecutingAssembly();
                    CauseBase cause = (CauseBase)assembly.CreateInstance(typeName);
                    cause.CfgParam = dic;
                    AddSubReason(cause);
                }
            }
        }

    }

    [Serializable]
    public class WeakCoverNoSite : CauseBase
    {


        public override string Name
        {
            get { return "缺站"; }
        }
        
        public float RadiusMin { get; set; } = 500;
        public override string Desc
        {
            get { return string.Format("主服和邻区距离都≥{0}米，并且周围{0}米内，没有其它LTE基站", RadiusMin); }
        }

        public override string Suggestion
        {
            get { return "合理规划站点"; }
        }

        public override void JudgeSinglePoint(LowSpeedSeg segItem, TestPoint testPoint)
        {
            LTECell sCell = testPoint.GetMainLTECell_TdOrFdd();
            if (sCell == null)
            {
                return;
            }
            if (testPoint.Distance2(sCell.Longitude, sCell.Latitude) < RadiusMin)
            {//主服小区在x米内
                return;
            }
            for (int i = 0; i < 10; i++)
            {
                LTECell nCell = testPoint.GetNBLTECell_TdOrFdd(i);
                if (nCell == null)
                {
                    continue;
                }
                if (testPoint.Distance2(nCell.Longitude, nCell.Latitude) < RadiusMin)
                {//存在邻区小区在x米内
                    return;
                }
            }
            double distanceX = RadiusMin * 0.00001;
            List<LTECell> cells = CellManager.GetInstance().GetLTECells(testPoint.DateTime);
            foreach (LTECell cell in cells)
            {
                if (Math.Abs(cell.Longitude - testPoint.Longitude) > distanceX
                    || Math.Abs(cell.Latitude - testPoint.Latitude) > distanceX)
                {//粗略计算
                    continue;
                }
                if (testPoint.Distance2(cell.Longitude, cell.Latitude) <= RadiusMin)
                {
                    return;
                }
            }
            WeakCoverNoSite cln = this.Clone() as WeakCoverNoSite;
            segItem.SetReason(new LowSpeedPointDetail(testPoint, cln));
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["radiusMin"] = this.RadiusMin;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RadiusMin = (float)value["radiusMin"];
            }
        }
    }

    [Serializable]
    public class WeakCoverLackNbCell : CauseBase
    {
        public override string Name
        {
            get { return "漏配邻区关系"; }
        }
        [NonSerialized]
        private ICell serverCell;
        [NonSerialized]
        private ICell nbCell;
        public float RadiusMin { get; set; } = 500;
        public override string Desc
        {
            get { return string.Format("主服和邻区距离都≥{0}米，周围{0}米内有LTE基站，但基站中的小区都没有出现在邻区中"
                , RadiusMin); }
        }

        public override string Suggestion
        {
            get
            {
                return string.Format("核查邻区关系（核查主服{0}和{1}邻区关系）"
                    , serverCell != null ? serverCell.Name : "", nbCell != null ? nbCell.Name : "");
            }
        }

        public override void JudgeSinglePoint(LowSpeedSeg segItem, TestPoint testPoint)
        {
            LTECell sCell = testPoint.GetMainLTECell_TdOrFdd();
            if (sCell == null)
            {
                return;
            }
            if (testPoint.Distance2(sCell.Longitude, sCell.Latitude) < RadiusMin)
            {//主服小区在x米内
                return;
            }
            bool allCellFar = true;
            for (int i = 0; i < 10; i++)
            {
                LTECell nCell = testPoint.GetNBLTECell_TdOrFdd(i);
                if (nCell == null)
                {
                    continue;
                }
                if (testPoint.Distance2(nCell.Longitude, nCell.Latitude) < RadiusMin)
                {//存在邻区小区在x米内
                    allCellFar = false;
                    break;
                }
            }
            if (!allCellFar)
            {//存在邻区小区在x米内
                return;
            }
            double distanceX = RadiusMin * 0.00001;
            List<LTECell> cells = CellManager.GetInstance().GetLTECells(testPoint.DateTime);
            foreach (LTECell cell in cells)
            {
                if (Math.Abs(cell.Longitude - testPoint.Longitude) > distanceX
                    || Math.Abs(cell.Latitude - testPoint.Latitude) > distanceX)
                {//粗略计算
                    continue;
                }
                if (testPoint.Distance2(cell.Longitude, cell.Latitude) < RadiusMin)
                {
                    WeakCoverLackNbCell cln = this.Clone() as WeakCoverLackNbCell;
                    cln.serverCell = sCell;
                    cln.nbCell = cell;
                    segItem.SetReason(new LowSpeedPointDetail(testPoint, cln));
                    break;
                }
            }
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["radiusMin"] = this.RadiusMin;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RadiusMin = (float)value["radiusMin"];
            }
        }
    }

    [Serializable]
    public class WeakCoverSCell : CauseBase
    {
        public override string Name
        {
            get { return "主服覆盖不足"; }
        }
        [NonSerialized]
        private ICell serverCell;
        public float RadiusMin { get; set; } = 500;
        public override string Desc
        {
            get { return string.Format("主服和邻区距离采样点都在{0}米内，主服比邻区距离采样点更近", RadiusMin); }
        }

        public override string Suggestion
        {
            get
            {
                return string.Format("调整主服{0}的覆盖）"
                    , serverCell != null ? serverCell.Name : "");
            }
        }

        public override void JudgeSinglePoint(LowSpeedSeg segItem, TestPoint testPoint)
        {
            LTECell sCell = testPoint.GetMainLTECell_TdOrFdd();
            if (sCell == null)
            {
                return;
            }
            double mainDistance = testPoint.Distance2(sCell.Longitude, sCell.Latitude);
            if (mainDistance > RadiusMin)
            {//主服小区超过条件距离
                return;
            }
            double minNCellDistance = double.MaxValue;
            for (int i = 0; i < 10; i++)
            {
                LTECell nCell = testPoint.GetNBLTECell_TdOrFdd(i);
                if (nCell == null)
                {
                    continue;
                }
                double dis = testPoint.Distance2(nCell.Longitude, nCell.Latitude);
                if (dis > RadiusMin)
                {
                    return;
                }
                if (dis < minNCellDistance)
                {
                    minNCellDistance = dis;
                }
            }
            if (mainDistance <= minNCellDistance)
            {
                WeakCoverSCell cln = this.Clone() as WeakCoverSCell;
                cln.serverCell = sCell;
                segItem.SetReason(new LowSpeedPointDetail(testPoint, cln));
            }
        }


        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["radiusMin"] = this.RadiusMin;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RadiusMin = (float)value["radiusMin"];
            }
        }
    }

    [Serializable]
    public class WeakCoverNCell : CauseBase
    {
        public override string Name
        {
            get { return "邻区覆盖不足"; }
        }
        [NonSerialized]
        private ICell nCell;
        public float RadiusMin { get; set; } = 500;
        public override string Desc
        {
            get { return "主服和邻区距离采样点都在X米内，邻区比主服距离采样点更近"; }
        }

        public override string Suggestion
        {
            get
            {
                return string.Format("调整邻区{0}的覆盖）"
                    , nCell != null ? nCell.Name : "");
            }
        }

        public override void JudgeSinglePoint(LowSpeedSeg segItem, TestPoint testPoint)
        {
            LTECell sCell = testPoint.GetMainLTECell_TdOrFdd();
            if (sCell == null)
            {
                return;
            }
            double mainDistance = testPoint.Distance2(sCell.Longitude, sCell.Latitude);
            if (mainDistance > RadiusMin)
            {//主服小区超过条件距离
                return;
            }
            double minNCellDistance = double.MaxValue;
            LTECell closestNCell = null;
            for (int i = 0; i < 10; i++)
            {
                LTECell curNCell = testPoint.GetNBLTECell_TdOrFdd(i);
                if (curNCell == null)
                {
                    continue;
                }
                double dis = testPoint.Distance2(curNCell.Longitude, curNCell.Latitude);
                if (dis > RadiusMin)
                {//邻区超过条件距离
                    return;
                }
                if (dis < minNCellDistance)
                {
                    minNCellDistance = dis;
                    closestNCell = curNCell;
                }
            }
            if (minNCellDistance != double.MaxValue
                && mainDistance > minNCellDistance)
            {
                WeakCoverNCell cln = this.Clone() as WeakCoverNCell;
                cln.nCell = closestNCell;
                segItem.SetReason(new LowSpeedPointDetail(testPoint, cln));
            }
        }


        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["radiusMin"] = this.RadiusMin;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RadiusMin = (float)value["radiusMin"];
            }
        }
    }

}
