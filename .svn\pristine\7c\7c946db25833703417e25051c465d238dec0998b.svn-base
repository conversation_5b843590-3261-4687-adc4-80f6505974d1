﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;

namespace MasterCom.RAMS.ZTFunc.ZTReportEventMng
{
    public class EventLog
    {
        [Description("事件编号")]
        public int SN { get; set; }
        
        [Description("基站类型")]
        public string BTSType { get; set; } = string.Empty;
        
        [Description("时间")]
        public DateTime DateTime { get; set; } = DateTime.MinValue;

        public DateTime Date
        {
            get { return DateTime.Date; }
            set { DateTime = value.Date; }
        }

        public TimeSpan Time
        {
            get { return DateTime.TimeOfDay; }
            set
            {
                DateTime = new DateTime(DateTime.Date.Ticks + value.Ticks);
            }
        }
        
        [Description("LOG号")]
        public string LogNumber { get; set; } = string.Empty;
        
        [Description("CI")]
        public string CI { get; set; } = string.Empty;
        
        [Description("主/被叫")]
        public string MoMt { get; set; } = string.Empty;
        
        [Description("事件")]
        public string EventName { get; set; } = string.Empty;
        
        [Description("事件产生地点")]
        public string Place { get; set; } = string.Empty;
        
        [Description("经度")]
        public double Longitude { get; set; }

        public int Lng
        {
            get { return (int)(Longitude * 10000000); }
        }
        public int Lat
        {
            get { return (int)(Latitude * 10000000); }
        }
        
        [Description("纬度")]
        public double Latitude { get; set; }
        
        [Description("事件分析")]
        public string Analytics { get; set; } = string.Empty;
        
        [Description("解决建议")]
        public string Suggestion { get; set; } = string.Empty;

        protected string errorType = string.Empty;
        [Description("分类原因")]
        public string ErrorType
        {
            get { return errorType; }
            set
            {
                if (Regex.IsMatch(value, @"弱覆盖|越区覆盖|室分泄露|设备故障|终端问题|参数问题|干扰问题|小区拥塞|其他问题"))
                {
                    errorType = value;
                }
            }
        }
        
        [Description("所属片")]
        public string OwnRegion { get; set; } = string.Empty;
        
        [Description("是否闭环")]
        public string IsCloseLoop { get; set; } = string.Empty;
        
        [Description("是否有traffic记录")]
        public string HasTrafficLog { get; set; } = string.Empty;
        
        [Description("手机号")]
        public string PhoneNumber { get; set; } = string.Empty;

        #region check
        public virtual bool CheckField(string fieldName, ref string errorInfo)
        {
            switch (fieldName)
            {
                case "SN":
                    return checkSN(ref errorInfo);
                case "Date":
                    return checkDate(ref errorInfo);
                case "Time":
                    return checkTime(ref errorInfo);
                case "LogNumber":
                    return checkLogNumber(ref errorInfo);
                case "CI":
                    return checkCI(ref errorInfo);
                case "MoMt":
                    return checkMoMt(ref errorInfo);
                case "EventName":
                    return checkEventName(ref errorInfo);
                case "IsClosedLoop":
                    return checkIsClosedLoop(ref errorInfo);
                case "HasTrafficLog":
                    return checkHasTrafficLog(ref errorInfo);
                case "PhoneNumber":
                    return checkPhoneNumber(ref errorInfo);
                case "Longitude":
                    return checkLongitude(ref errorInfo);
                case "Latitude":
                    return checkLatitude(ref errorInfo);
                default:
                    break;
            }
            return true;
        }

        private bool checkSN(ref string errorInfo)
        {
            if (SN <= 0)
            {
                errorInfo += "记录序号必须为大于0的整数\n";
                return false;
            }
            return true;
        }

        private bool checkDate(ref string errorInfo)
        {
            if (Date.Equals(DateTime.MinValue.Date))
            {
                errorInfo += "请填写测试时间\n";
                return false;
            }
            return true;
        }

        private bool checkTime(ref string errorInfo)
        {
            if (Date.Equals(DateTime.MinValue.Date))
            {
                errorInfo += "请填写事件产生时间（格式为24小时制，如14:30）\n";
                return false;
            }
            return true;
        }

        private bool checkLogNumber(ref string errorInfo)
        {
            if (string.IsNullOrEmpty(LogNumber.Trim()))
            {
                errorInfo += "请填写LOG号\n";
                return false;
            }
            return true;
        }

        private bool checkCI(ref string errorInfo)
        {
            if (string.IsNullOrEmpty(CI.Trim()))
            {
                errorInfo += "请填写CI\n";
                return false;
            }
            return true;
        }

        private bool checkMoMt(ref string errorInfo)
        {
            if (string.IsNullOrEmpty(MoMt.Trim()))
            {
                errorInfo += "请填写主/被叫，内容只能为“主叫”或者“被叫”\n";
                return false;
            }
            else if (!Regex.IsMatch(MoMt, @"主叫|被叫"))
            {
                errorInfo += "主/被叫内容填写错误，内容只能为“主叫”或者“被叫”\n";
                return false;
            }
            return true;
        }

        private bool checkEventName(ref string errorInfo)
        {
            if (string.IsNullOrEmpty(EventName.Trim()))
            {
                errorInfo += "请填写事件\n";
                return false;
            }
            return true;
        }

        private bool checkIsClosedLoop(ref string errorInfo)
        {
            if (!string.IsNullOrEmpty(IsCloseLoop) && !Regex.IsMatch(IsCloseLoop, @"是|否"))
            {
                errorInfo += "是否闭环内容填写错误，内容只能为“是”或者“否”\n";
                return false;
            }
            return true;
        }

        private bool checkHasTrafficLog(ref string errorInfo)
        {
            if (!string.IsNullOrEmpty(HasTrafficLog) && !Regex.IsMatch(HasTrafficLog, @"是|否"))
            {
                errorInfo += "是否有traffic记录内容填写错误，内容只能为“是”或者“否”\n";
                return false;
            }
            return true;
        }

        private bool checkPhoneNumber(ref string errorInfo)
        {
            if (string.IsNullOrEmpty(PhoneNumber.Trim()))
            {
                errorInfo += "请填写手机号\n";
                return false;
            }
            return true;
        }

        private bool checkLongitude(ref string errorInfo)
        {
            if (Longitude != 0 && (Longitude < 70 || Longitude > 136))
            {
                errorInfo += "经度填写错误\n";
                return false;
            }
            return true;
        }

        private bool checkLatitude(ref string errorInfo)
        {
            if (Latitude != 0 && (Latitude < 3 || Latitude > 54))
            {
                errorInfo += "纬度填写错误\n";
                return false;
            }
            return true;
        }

        public virtual bool CheckData()
        {
            string info;
            return CheckData(out info);
        }

        public virtual bool CheckData(out string errorInfo)
        {
            bool correct = true;
            errorInfo = string.Empty;
            correct &= CheckField("SN", ref errorInfo);
            correct &= CheckField("Date", ref errorInfo);
            correct &= CheckField("Time", ref errorInfo);
            correct &= CheckField("LogNumber", ref errorInfo);
            correct &= CheckField("CI", ref errorInfo);
            correct &= CheckField("MoMt", ref errorInfo);
            correct &= CheckField("SN", ref errorInfo);
            correct &= CheckField("EventName", ref errorInfo);
            correct &= CheckField("Longitude", ref errorInfo);
            correct &= CheckField("Latitude", ref errorInfo);
            correct &= CheckField("PhoneNumber", ref errorInfo);
            return correct;
        }
        #endregion

        public virtual void FillFrom(DataRow row)
        {
            PropertyInfo[] props = this.GetType().GetProperties();
            for (int p = 0; p < props.Length; p++)
            {
                PropertyInfo prop = props[p];
                DescriptionAttribute desc = Attribute.GetCustomAttribute(prop, typeof(DescriptionAttribute)) as DescriptionAttribute;
                if (desc != null && !string.IsNullOrEmpty(desc.Description))
                {
                    try
                    {
                        if (desc.Description.Equals("时间"))
                        {
                            DateTime date = (DateTime)row["测试时间"];
                            TimeSpan time = (TimeSpan)row["事件产生时间"];
                            DateTime = new DateTime(date.Year, date.Month, date.Day, time.Hours, time.Minutes, time.Seconds, time.Milliseconds);
                        }
                        else if (row[desc.Description] != null && row[desc.Description].ToString().Trim().Length != 0)
                        {
                            object o = Convert.ChangeType(row[desc.Description], prop.PropertyType);
                            prop.SetValue(this, o, null);
                        }
                    }
                    catch
                    {
                        //continue
                    }
                }
            }
        }

    }
}
