﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FrequencyShortageDlg_GSM : BaseDialog
    {
        public FrequencyShortageDlg_GSM()
        {
            InitializeComponent();
        }

        public void GetFilterCondition(out int rxLevDValue, out int secondLast, out int rxLevDValueOther, out int freqCountRateThreshold)
        {
            rxLevDValue = (int)numRxLevDValue.Value;
            secondLast = (int)numSecondLast.Value;
            rxLevDValueOther = (int)numRxLevDValueOther.Value;
            freqCountRateThreshold = (int)numFreqCountRateThreshold.Value;
        }
    }
}
