﻿using Microsoft.Office.Interop.Excel;
using MasterCom.ES.Core;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public partial class EventInfoForm : MinCloseForm
    {
        public EventInfoForm()
            :base()
        {
            InitializeComponent();
            this.mapForm = MainModel.MainForm.GetMapForm();
#if DEBUG
            btnInputEvents.Visible = true;
#endif
        }

        private List<Event> events = new List<Event>();
        private MapForm mapForm;
        public void FillData()
        {
            events.Clear();
            foreach (DTFileDataManager fileDataManager in this.MainModel.DTDataManager.FileDataManagers)
            {
                foreach (Event e in fileDataManager.Events)
                {
                    if (e.EventInfo != null)
                    {
                        events.Add(e);
                    }
                }
            }
            this.gridCtrl.DataSource = events;
            this.gridColumnESType.Visible = false;
            this.gridColumnESAnalysis.Visible = false;
            this.gridCtrl.RefreshDataSource();   
        }

        private void getTimeCondition(out DateTime startTime, out DateTime endTime)
        {
            long startSeconds = int.MaxValue;
            long endSeconds = int.MinValue;
            List<Event> eventList = events;
            foreach (Event evt in eventList)
            {
                if (startSeconds > evt.Time)
                {
                    startSeconds = evt.Time;
                }
                if (endSeconds < evt.Time)
                {
                    endSeconds = evt.Time;
                }
            }
            startTime = JavaDate.GetMonthFirstDate(startSeconds);
            endTime = JavaDate.GetMonthFirstDate(endSeconds);
        }

        Dictionary<string, ESResult> resultDic;
        private void getESResult()
        {
            DateTime startTime;
            DateTime endTime;
            MainModel mainModel = MainModel.GetInstance();
            getTimeCondition(out startTime, out endTime);
            DiySqlQueryESResult sqlQuery = new DiySqlQueryESResult(mainModel, startTime, endTime);
            sqlQuery.Query();
            resultDic = sqlQuery.ResultDictionary;
            WaitBox.Close();
        }

        private void bindESResult()
        {
            WaitBox.Show("正在查询智能预判信息", getESResult);
            foreach (Event e in events)
            {
                string key = string.Format("{0}_{1}", e.FileID, e.SN);
                if (resultDic.ContainsKey(key))
                {
                    e.EsResult = resultDic[key];
                }
            }
        }

        private void showESResult_Click(object sender, EventArgs e)
        {
            bindESResult();
            this.gridColumnESType.Visible = true;
            this.gridColumnESAnalysis.Visible = true;
            gridCtrl.RefreshDataSource();
        }

        private void exportToExcel()
        {
            List<List<object>> datas = new List<List<object>>();
            List<object> rowTitle = new List<object>();
            rowTitle.Add("名称");
            rowTitle.Add("时间");
            rowTitle.Add("文件");
            rowTitle.Add("小区名");
            rowTitle.Add("Code");
            rowTitle.Add("LAC/TAC");
            rowTitle.Add("CI/ECI");
            rowTitle.Add("BCCH/EARCFN");
            rowTitle.Add("BSIC/PCI");
            rowTitle.Add("RAC");
            rowTitle.Add("目标小区名");
            rowTitle.Add("目标小区Code");
            rowTitle.Add("目标小区LAC/TAC");
            rowTitle.Add("目标小区CI/ECI");
            rowTitle.Add("目标小区BCCH/EARCFN");
            rowTitle.Add("目标小区BSIC/PCI");
            rowTitle.Add("目标小区RAC");
            rowTitle.Add("经度");
            rowTitle.Add("纬度");
            rowTitle.Add("Info");
            rowTitle.Add("StartTime");
            rowTitle.Add("道路名称");
            rowTitle.Add("区域名称");
            rowTitle.Add("区域信息");
            rowTitle.Add("Value1");
            rowTitle.Add("Value2");
            rowTitle.Add("Value3");
            rowTitle.Add("Value4");
            rowTitle.Add("Value5");
            rowTitle.Add("Value6");
            rowTitle.Add("Value7");
            rowTitle.Add("Value8");
            rowTitle.Add("Value9");
            rowTitle.Add("Value10");
            rowTitle.Add("是否在道路黑点");
            rowTitle.Add("片区");
            rowTitle.Add("网格名");
            rowTitle.Add("代维分区");
            rowTitle.Add("端口号");
            rowTitle.Add("PCI");
            rowTitle.Add("预判类型");
            rowTitle.Add("判断原因分析");

            datas.Add(rowTitle);
            foreach (Event e in events)
            {
                List<object> row = new List<object>();
                row.Add(e.Name);
                row.Add(e.DateTimeStringWithMillisecond);
                row.Add(e.FileName == null ? "" : e.FileName);
                row.Add(e.CellNameSrc);
                row.Add(e.CellCodeSrc);
                row.Add(e["LAC"]);
                row.Add(e["CI"]);
                row.Add(e.CellBCCHSrc);
                row.Add(e.CellBSICSrc);
                row.Add(e["RAC"]);
                row.Add(e.CellNameTarget);
                row.Add(e.CellCodeTarget);
                row.Add(e["TargetLAC"]);
                row.Add(e["TargetCI"]);
                row.Add(e.CellBCCHTarget);
                row.Add(e.CellBSICTarget);
                row.Add(e["TargetRAC"]);
                row.Add(e.Longitude);
                row.Add(e.Latitude);
                row.Add(e.Info);
                row.Add(e.StartTime);
                row.Add(e.RoadPlaceDesc);

                row.Add(e.AreaPlaceDesc);
                row.Add(e.StrAreaInfo);
                row.Add(e["Value1"]);
                row.Add(e["Value2"]);
                row.Add(e["Value3"]);
                row.Add(e["Value4"]);
                row.Add(e["Value5"]);
                row.Add(e["Value6"]);
                row.Add(e["Value7"]);
                row.Add(e["Value8"]);
                row.Add(e["Value9"]);
                row.Add(e["Value10"]);
                row.Add(e.InterSectBlackBlock);

                row.Add(e.AreaPlaceDesc);
                row.Add(e.GridDesc);
                row.Add(e.AgentAreaName);
                row.Add(e.MS);
                row.Add(e.CellBSICSrc);
                row.Add(e.EsResultType);
                row.Add(e.EsResultAnalyis);

                datas.Add(row);
            }
            ExcelNPOIManager.ExportToExcel(datas);
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            exportToExcel();
        }

        private void contextMenuStrip_Opening(object sender, CancelEventArgs e)
        {
            miShowInterference.Enabled = grid1View.SelectedRowsCount > 0;
            miExportExcel.Enabled = grid1View.SelectedRowsCount > 0;
            miShowInterference.Checked = MainModel.ShowEventInterference;
        }

        private void btnBlackBlock_Click(object sender, EventArgs e)
        {
            QueryBlackBlock qBlockBlock = new QueryBlackBlock(mModel);
            qBlockBlock.Query();
        }

        private void miShowInterference_Click(object sender, EventArgs e)
        {
            if (grid1View.SelectedRowsCount > 0)
            {
                if (miShowInterference.Checked)
                {
                    int index = grid1View.GetDataSourceRowIndex(grid1View.FocusedRowHandle);
                    Event curEvent = events[index];
                    foreach (Event eve in MainModel.SelectedEvents)
                    {
                        eve.Selected = false;
                    }
                    MainModel.SelectedEvents.Clear();
                    MainModel.SelectedEvents.Add(curEvent);
                    curEvent.Selected = true;
                    mapForm.SetShowEventInterference(true);
                }
                else
                {
                    mapForm.SetShowEventInterference(false);
                }
            }
        }

        private void miReplayEvent_Click(object sender, EventArgs e)
        {
            int index = grid1View.GetDataSourceRowIndex(grid1View.FocusedRowHandle);
            MasterCom.RAMS.Model.Interface.FileReplayer.ReplayOnePart(events[index]);
        }

        private void btnInputEvents_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = "Excel file (*.xls)|*.xls";
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                WaitBox.Show("正在导入事件...", inputEventsFromExcel, dlg.FileName);
                gridCtrl.RefreshDataSource();
            }
        }

        private void inputEventsFromExcel(object nameObj)
        {
            try
            {
                string fileName = nameObj.ToString();
                events.Clear();
                Microsoft.Office.Interop.Excel.Application xApp = new Microsoft.Office.Interop.Excel.ApplicationClass();
                xApp.Visible = false;
                object MissingValue = Type.Missing;
                xApp.Workbooks._Open(fileName,
                MissingValue, MissingValue, MissingValue, MissingValue, MissingValue, MissingValue, MissingValue, MissingValue,
                MissingValue, MissingValue, MissingValue, MissingValue);
                Microsoft.Office.Interop.Excel.Worksheet xSheet = (Microsoft.Office.Interop.Excel.Worksheet)xApp.ActiveSheet;
                try
                {
                    addValidEvt(xSheet);
                }
                catch
                {
                    //
                }
                finally
                {
                    //xSheet = null;
                    xApp.Quit();
                    //xApp = null;
                }
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void addValidEvt(Worksheet xSheet)
        {
            int index = 0;
            int rowCount = xSheet.UsedRange.Rows.Count;
            for (int i = 2; i <= rowCount; i++)
            {
                index++;
                Range rg1 = (Range)xSheet.Cells[i, 6];
                if (rg1 == null || rg1.Value2 == null)
                {
                    continue;
                }
                string sLAC = rg1.Value2.ToString();
                int lac = int.Parse(sLAC);

                Range rg4 = (Range)xSheet.Cells[i, 7];
                if (rg4 == null || rg4.Value2 == null)
                {
                    continue;
                }
                string sCI = rg4.Value2.ToString();
                int ci = int.Parse(sCI);

                Range rg2 = (Range)xSheet.Cells[i, 14];
                if (rg2.Value2 == null)
                {
                    continue;
                }
                string sLongitude = rg2.Value2.ToString().Trim();
                if (string.IsNullOrEmpty(sLongitude) || sLongitude.Equals("NULL"))
                {
                    continue;
                }
                double longitude = double.Parse(sLongitude);

                Range rg3 = (Range)xSheet.Cells[i, 15];
                string sLatitude = rg3.Value2.ToString().Trim();
                if (string.IsNullOrEmpty(sLatitude) || sLatitude.Equals("NULL"))
                {
                    continue;
                }
                double latitude = double.Parse(sLatitude);

                Event e = new Event();
                e.ID = 17;
                e.Longitude = longitude;
                e.Latitude = latitude;
                e["LAC"] = lac;
                e["RAC"] = 0;
                e["CI"] = ci;
                e["TargetLAC"] = 0;
                e["TargetRAC"] = 0;
                e["TargetCI"] = 0;
                e["Value1"] = 0;
                e["Value2"] = 0;
                e["Value3"] = 0;
                e["Value4"] = 0;
                e["Value5"] = 0;
                e["Value6"] = 0;
                e["Value7"] = 0;
                e["Value8"] = 0;
                e["Value9"] = 0;
                e["Value10"] = 0;
                events.Add(e);

                MasterCom.Util.WaitBox.ProgressPercent = (int)(i * 100.0 / rowCount);
            }
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if(grid1View.SelectedRowsCount <= 0)
            {
                return;
            }
            int index = grid1View.GetDataSourceRowIndex(grid1View.FocusedRowHandle);
            Event curEvent = events[index];
            foreach (Event eve in MainModel.SelectedEvents)
            {
                eve.Selected = false;
            }
            MainModel.SelectedEvents.Clear();
            MainModel.SelectedEvents.Add(curEvent);
            curEvent.Selected = true;
            if (MainModel.ShowEventInterference)
            {
                mapForm.ShowEventInterference(curEvent);
            }
            else
            {
                mapForm.GoToView(curEvent.Longitude, curEvent.Latitude);
            }
        }

        private void gridView1_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            if(e.Column == this.gridColumnID)
            {
                e.DisplayText = (e.RowHandle + 1).ToString();
            }
        }
    }
}
