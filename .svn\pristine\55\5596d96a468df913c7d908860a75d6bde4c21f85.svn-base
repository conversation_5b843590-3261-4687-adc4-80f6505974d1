﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using Excel = Microsoft.Office.Interop.Excel;
using System.Reflection;
using MasterCom.RAMS.Model;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.Utils;
using MasterCom.RAMS.Func;
using System.Windows.Forms;
using DevExpress.XtraCharts;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public static class ExcelExporter
    {
        public static List<DataTable> TableList { get; set; } 
        public static string[] Tips { get; set; }
        public static string MosTitle { get; set; }
        public static DataTable FactorTable { get; set; }

        private static Excel.Application excelApp { get; set; }
        private static Excel.Workbook workBook { get; set; }
        private static Excel.Worksheet workSheet { get; set; }
        private static List<int> firstRow { get; set; }
        private static int chartRowHeight { get; set; } = 15;

        public static void Export()
        {
            SaveFileDialog saveFileDialog = new SaveFileDialog();
            saveFileDialog.Filter = FilterHelper.Excel;
            saveFileDialog.RestoreDirectory = true;
            if (saveFileDialog.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            excelApp = new Excel.Application();
            workBook = excelApp.Workbooks.Add(true);
            workSheet = (Excel.Worksheet)excelApp.ActiveSheet;
            excelApp.Visible = true;
            workSheet.Name = "TD-MOS统计分析";

            bool isSucceed = true;
            firstRow = new List<int>();
            try
            {
                int curRow = 1;
                for (int i = 0; i < TableList.Count; ++i)
                {
                    firstRow.Add(curRow);
                    DataTable tb = TableList[i];

                    // column name
                    for (int j = 0; j < tb.Columns.Count; ++j)
                    {
                        workSheet.Cells[curRow, j + 1] = tb.Columns[j].ColumnName;
                    }
                    ++curRow;

                    // column value
                    for (int row = 0; row < tb.Rows.Count; ++row)
                    {
                        for (int col = 0; col < tb.Columns.Count; ++col)
                        {
                            workSheet.Cells[curRow, col + 1] = tb.Rows[row][col];
                        }
                        ++curRow;
                    }

                    // add tips
                    AddTableTips(i, tb.Rows.Count, ref curRow);

                    // do next table
                    ++curRow;
                }

                // 插入影响因子
                InsertFactorTable(ref curRow);

                // 修正标题
                CorrectTitle();

                // 数值格式化
                FormatCells();

                // 创建图表
                CreateChart();

                //保存文件
                excelApp.DisplayAlerts = false;
                workBook.SaveAs(saveFileDialog.FileName, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Excel.XlSaveAsAccessMode.xlNoChange, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            }
            catch
            {
                MessageBox.Show("文件保存失败!");
                isSucceed = false;
            }
            finally
            {
                workBook = null;
                workSheet = null;
                TableList = null;
            }

            if (isSucceed)
            {
                MessageBox.Show("保存文件成功!");
            }
        }

        private static void InsertFactorTable(ref int curRow)
        {
            // 影响因素？
            ++curRow;
            for (int j = 0; j < FactorTable.Columns.Count; ++j)
            {
                workSheet.Cells[curRow, j + 1] = FactorTable.Columns[j].ColumnName;
            }

            ++curRow;
            int startRow = curRow;
            for (int row = 0; row < FactorTable.Rows.Count; ++row)
            {
                for (int col = 0; col < FactorTable.Columns.Count; ++col)
                {
                    workSheet.Cells[curRow, col + 1] = FactorTable.Rows[row][col];
                }
                ++curRow;
            }

            workSheet.get_Range("C" + startRow, "C" + (startRow + FactorTable.Rows.Count - 1)).NumberFormat = "0.00";
            workSheet.get_Range("D" + startRow, "G" + (startRow + FactorTable.Rows.Count - 1)).NumberFormat = "0.00%";
        }

        private static void FormatCells()
        {
            int idx = 0;
            workSheet.get_Range("B" + firstRow[idx], "B" + (firstRow[idx] + TableList[idx].Rows.Count - 1)).NumberFormat = "0.00%";

            for (++idx; idx < TableList.Count; ++idx)
            {
                if (idx % 3 == 1)
                    workSheet.get_Range("B" + firstRow[idx], "B" + (firstRow[idx] + TableList[idx].Rows.Count)).NumberFormat = "0.00";
                else
                    workSheet.get_Range("B" + firstRow[idx], "B" + (firstRow[idx] + TableList[idx].Rows.Count)).NumberFormat = "0.00%";
            }
        }

        private static void CorrectTitle()
        {
            int idx = 2;
            workSheet.Cells[firstRow[idx], 2] = MosTitle;
            idx += 3;
            workSheet.Cells[firstRow[idx], 2] = MosTitle;
            idx += 3;
            workSheet.Cells[firstRow[idx], 2] = MosTitle;
        }

        private static void AddTableTips(int tbIndex, int tbRows, ref int curRow)
        {
            if (tbIndex == 0)
            {
                curRow += 1;
                return;
            }

            if (tbRows < chartRowHeight)
            {
                curRow += chartRowHeight - tbRows;
            }

            string tip = null;
            switch (tbIndex)
            {
                case 1:
                case 2:
                    tip = Tips[0];
                    break;
                case 4:
                case 5:
                    tip = Tips[1];
                    break;
                case 7:
                case 8:
                    tip = Tips[2];
                    break;
                default:
                    tip = Tips[3];
                    break;
            }
            workSheet.Cells[curRow, 1] = tip;
            curRow += 2;
        }

        private static void CreateChart()
        {
            //MOS比例图表
            int tbIdx = 0;
            int mosCount = TableList[tbIdx].Rows.Count;
            Excel.Chart chart1 = (Excel.Chart)workBook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange1 = workSheet.get_Range("B1", "B" + (mosCount + 1 - 1));

            chart1.ChartWizard(chartrange1, Excel.XlChartType.xlLine, Missing.Value, Excel.XlRowCol.xlColumns, 0, 1, false,
                Missing.Value, Missing.Value, Missing.Value, Missing.Value);

            chart1.HasTitle = false;
            Excel.Series exseries1 = (Excel.Series)chart1.SeriesCollection(1);
            exseries1.XValues = workSheet.get_Range("A2", "A" + (mosCount + 1 - 1));
            exseries1.HasDataLabels = false;

            Excel.Axis yAxis1 = (Excel.Axis)chart1.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis1.MinimumScale = 0;
            yAxis1.HasMajorGridlines = true;
            yAxis1.MajorGridlines.Border.ColorIndex = 15;

            Excel.Axis xAxis1 = (Excel.Axis)chart1.Axes(Excel.XlAxisType.xlCategory, Excel.XlAxisGroup.xlPrimary);
            xAxis1.TickLabels.Font.Size = 10;

            chart1.Location(Excel.XlChartLocation.xlLocationAsObject, workSheet.Name);
            SetTopLeft(tbIdx);


            //切换次数与MOS均值
            ++tbIdx;
            int handoverCount = TableList[tbIdx].Rows.Count;
            Excel.Chart chart2 = (Excel.Chart)workBook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange2 = workSheet.get_Range("B" + firstRow[tbIdx], "B" + (firstRow[tbIdx] + handoverCount));
            chart2.ChartWizard(chartrange2, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS均值", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries2 = (Excel.Series)chart2.SeriesCollection(1);
            exseries2.BarShape = Excel.XlBarShape.xlCylinder;
            exseries2.XValues = workSheet.get_Range("A" + (firstRow[tbIdx] + 1), "A" + (firstRow[tbIdx] + handoverCount));
            exseries2.HasDataLabels = true;

            Excel.Axis yAxis2 = (Excel.Axis)chart2.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis2.MinimumScale = 0.0;
            yAxis2.MajorUnit = 1.0;
            yAxis2.HasMajorGridlines = true;
            yAxis2.MajorGridlines.Border.ColorIndex = 15;
            yAxis2.TickLabels.NumberFormat = "0.00";

            chart2.Location(Excel.XlChartLocation.xlLocationAsObject, workSheet.Name);
            SetTopLeft(tbIdx);


            ////切换次数与MOS>=X
            ++tbIdx;
            Excel.Chart chart3 = (Excel.Chart)workBook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange3 = workSheet.get_Range("B" + firstRow[tbIdx], "B" + (firstRow[tbIdx] + handoverCount));

            chart3.ChartWizard(chartrange3, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, MosTitle, Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries3 = (Excel.Series)chart3.SeriesCollection(1);
            exseries3.BarShape = Excel.XlBarShape.xlCylinder;
            exseries3.XValues = workSheet.get_Range("A" + (firstRow[tbIdx] + 1), "A" + (firstRow[tbIdx] + handoverCount));
            exseries3.HasDataLabels = true;

            Excel.Axis yAxis3 = (Excel.Axis)chart3.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis3.MinimumScale = 0;
            yAxis3.HasMajorGridlines = true;
            yAxis3.MajorGridlines.Border.ColorIndex = 20;

            chart3.Location(Excel.XlChartLocation.xlLocationAsObject, workSheet.Name);
            SetTopLeft(tbIdx);


            ////切换次数与MOS比例
            ++tbIdx;
            Excel.Chart chart4 = (Excel.Chart)workBook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange4 = workSheet.get_Range("B" + firstRow[tbIdx], "B" + (firstRow[tbIdx] + handoverCount));

            chart4.ChartWizard(chartrange4, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries4 = (Excel.Series)chart4.SeriesCollection(1);
            exseries4.BarShape = Excel.XlBarShape.xlCylinder;
            exseries4.XValues = workSheet.get_Range("A" + (firstRow[tbIdx] + 1), "A" + (firstRow[tbIdx] + handoverCount));
            exseries4.HasDataLabels = true;

            Excel.Axis yAxis4 = (Excel.Axis)chart4.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis4.MinimumScale = 0;
            yAxis4.HasMajorGridlines = true;
            yAxis4.MajorGridlines.Border.ColorIndex = 15;

            chart4.Location(Excel.XlChartLocation.xlLocationAsObject, workSheet.Name);
            SetTopLeft(tbIdx);


            /////////C2I与MOS均值/////////////////////
            ++tbIdx;
            int c2iCount = TableList[tbIdx].Rows.Count;
            Excel.Chart chart5 = (Excel.Chart)workBook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange5 = workSheet.get_Range("B" + firstRow[tbIdx], "B" + (firstRow[tbIdx] + c2iCount));
            chart5.ChartWizard(chartrange5, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS均值", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries5 = (Excel.Series)chart5.SeriesCollection(1);
            exseries5.BarShape = Excel.XlBarShape.xlCylinder;
            exseries5.XValues = workSheet.get_Range("A" + (firstRow[tbIdx] + 1), "A" + (firstRow[tbIdx] + c2iCount));
            exseries5.HasDataLabels = true;

            Excel.Axis yAxis5 = (Excel.Axis)chart5.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis5.MinimumScale = 0;
            yAxis5.MajorUnit = 1.0;
            yAxis5.HasMajorGridlines = true;
            yAxis5.MajorGridlines.Border.ColorIndex = 15;
            yAxis5.TickLabels.NumberFormat = "0.00";

            chart5.Location(Excel.XlChartLocation.xlLocationAsObject, workSheet.Name);
            SetTopLeft(tbIdx);


            ////C2I与MOS>=3
            ++tbIdx;
            Excel.Chart chart6 = (Excel.Chart)workBook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange6 = workSheet.get_Range("B" + firstRow[tbIdx], "B" + (firstRow[tbIdx] + c2iCount));

            chart6.ChartWizard(chartrange6, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, MosTitle, Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries6 = (Excel.Series)chart6.SeriesCollection(1);
            exseries6.BarShape = Excel.XlBarShape.xlCylinder;
            exseries6.XValues = workSheet.get_Range("A" + (firstRow[tbIdx] + 1), "A" + (firstRow[tbIdx] + c2iCount));
            exseries6.HasDataLabels = true;

            Excel.Axis yAxis6 = (Excel.Axis)chart6.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis6.MinimumScale = 0;
            yAxis6.HasMajorGridlines = true;
            yAxis6.MajorGridlines.Border.ColorIndex = 15;

            chart6.Location(Excel.XlChartLocation.xlLocationAsObject, workSheet.Name);
            SetTopLeft(tbIdx);


            ////C2I与MOS比例
            ++tbIdx;
            Excel.Chart chart7 = (Excel.Chart)workBook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange7 = workSheet.get_Range("B" + firstRow[tbIdx], "B" + (firstRow[tbIdx] + c2iCount));

            chart7.ChartWizard(chartrange7, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries7 = (Excel.Series)chart7.SeriesCollection(1);
            exseries7.BarShape = Excel.XlBarShape.xlCylinder;
            exseries7.XValues = workSheet.get_Range("A" + (firstRow[tbIdx] + 1), "A" + (firstRow[tbIdx] + c2iCount));
            exseries7.HasDataLabels = true;

            Excel.Axis yAxis7 = (Excel.Axis)chart7.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis7.MinimumScale = 0;
            yAxis7.HasMajorGridlines = true;
            yAxis7.MajorGridlines.Border.ColorIndex = 15;

            chart7.Location(Excel.XlChartLocation.xlLocationAsObject, workSheet.Name);
            SetTopLeft(tbIdx);


            //BLER与MOS均值
            ++tbIdx;
            int blerCount = TableList[tbIdx].Rows.Count;
            Excel.Chart chart8 = (Excel.Chart)workBook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange8 = workSheet.get_Range("B" + firstRow[tbIdx], "B" + (firstRow[tbIdx] + blerCount));
            chart8.ChartWizard(chartrange8, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "MOS均值", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries8 = (Excel.Series)chart8.SeriesCollection(1);
            exseries8.BarShape = Excel.XlBarShape.xlCylinder;
            exseries8.XValues = workSheet.get_Range("A" + (firstRow[tbIdx] + 1), "A" + (firstRow[tbIdx] + blerCount));
            exseries8.HasDataLabels = true;

            Excel.Axis yAxis8 = (Excel.Axis)chart8.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis8.MinimumScale = 0;
            yAxis8.MajorUnit = 1.0;
            yAxis8.HasMajorGridlines = true;
            yAxis8.MajorGridlines.Border.ColorIndex = 15;
            yAxis8.TickLabels.NumberFormat = "0.00";

            chart8.Location(Excel.XlChartLocation.xlLocationAsObject, workSheet.Name);
            SetTopLeft(tbIdx);


            ////编码速率与MOS>=3
            ++tbIdx;
            Excel.Chart chart9 = (Excel.Chart)workBook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange9 = workSheet.get_Range("B" + firstRow[tbIdx], "B" + (firstRow[tbIdx] + blerCount));

            chart9.ChartWizard(chartrange9, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, MosTitle, Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries9 = (Excel.Series)chart9.SeriesCollection(1);
            exseries9.BarShape = Excel.XlBarShape.xlCylinder;
            exseries9.XValues = workSheet.get_Range("A" + (firstRow[tbIdx] + 1), "A" + (firstRow[tbIdx] + blerCount));
            exseries9.HasDataLabels = true;

            Excel.Axis yAxis9 = (Excel.Axis)chart9.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis9.MinimumScale = 0;
            yAxis9.HasMajorGridlines = true;
            yAxis9.MajorGridlines.Border.ColorIndex = 15;

            chart9.Location(Excel.XlChartLocation.xlLocationAsObject, workSheet.Name);
            SetTopLeft(tbIdx);


            ////编码速率与MOS比例
            ++tbIdx;
            Excel.Chart chart10 = (Excel.Chart)workBook.Charts.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
            Excel.Range chartrange10 = workSheet.get_Range("B" + firstRow[tbIdx], "B" + (firstRow[tbIdx] + blerCount));

            chart10.ChartWizard(chartrange10, Excel.XlChartType.xl3DColumn, Missing.Value,
                Excel.XlRowCol.xlColumns, 0, 1, false, "比例", Missing.Value, Missing.Value, Missing.Value);

            Excel.Series exseries10 = (Excel.Series)chart10.SeriesCollection(1);
            exseries10.BarShape = Excel.XlBarShape.xlCylinder;
            exseries10.XValues = workSheet.get_Range("A" + (firstRow[tbIdx] + 1), "A" + (firstRow[tbIdx] + blerCount));
            exseries10.HasDataLabels = true;

            Excel.Axis yAxis10 = (Excel.Axis)chart10.Axes(Excel.XlAxisType.xlValue, Excel.XlAxisGroup.xlPrimary);
            yAxis10.MinimumScale = 0;
            yAxis10.HasMajorGridlines = true;
            yAxis10.MajorGridlines.Border.ColorIndex = 15;

            chart10.Location(Excel.XlChartLocation.xlLocationAsObject, workSheet.Name);
            SetTopLeft(tbIdx);
        }

        private static void SetTopLeft(int tbIdx)
        {
            workSheet.Shapes.Item(tbIdx + 1).Top = (float)(double)workSheet.get_Range("G" + firstRow[tbIdx], "L" + (firstRow[tbIdx] + chartRowHeight)).Top;
            workSheet.Shapes.Item(tbIdx + 1).Left = (float)(double)workSheet.get_Range("G" + firstRow[tbIdx], "L" + (firstRow[tbIdx] + chartRowHeight)).Left;
        }
    }
}
