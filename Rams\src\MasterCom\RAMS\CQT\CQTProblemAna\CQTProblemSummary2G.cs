﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using Microsoft.Office.Interop.Excel;
using System.Reflection;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTProblemSummary2G : MinCloseForm
    {
        public CQTProblemSummary2G(MainModel mainModel, string strCqtProblemType)
            :base(mainModel)
        {
            this.mainModel = mainModel;
            InitializeComponent();
            strProblemType = strCqtProblemType;
            xtraTabPage2.PageVisible = false;
            xtraTabPage3.PageVisible = false;
            xtraTabPage4.PageVisible = false;
            if (strCqtProblemType == "派单问题点")
                xtraTabPage4.PageVisible = true;
            else
            {
                if (CQTProblemSummaryAnalysGSM.cityLevel(mainModel.DistrictID) != ""
                    && mainModel.User.DBID == -1)
                    xtraTabPage2.PageVisible = true;
                else
                    xtraTabPage3.PageVisible = true;
            }
            problemPointListTem = new List<ProblemPont2G>();
        }
        MainModel mainModel;
        string strProblemType;
        public List<ProblemPont2G> problemPointListTem { get; set; }
        public void filldataSummary2G(int type)
        {
            problemPointListTem.Clear();
            foreach (ProblemPont2G pr in CQTProblemSummaryAnalysGSM.problemPointList)
            {
                if (pr.SMainType == "" && pr.SSecondType == "")
                    continue;
                pr.IID = problemPointListTem.Count + 1;
                problemPointListTem.Add(pr);
            }
            BindingSource bindingSource = new BindingSource();
            if (type == 0)
                bindingSource.DataSource = CQTProblemSummaryAnalys2G.problemPointSummarryList;
            else if (type == 1)
                bindingSource.DataSource = CQTProblemSummaryAnalysGSM.problemPointSummarryList;

            summarryGD.DataSource = bindingSource;
            summarryGD.RefreshDataSource();
            summarry.DataSource = bindingSource;
            summarry.RefreshDataSource();
            summarryOrderGrid.DataSource = bindingSource;
            summarryOrderGrid.RefreshDataSource();
            BindingSource bindingSourcexq = new BindingSource();
            if (type == 0)
                bindingSourcexq.DataSource = CQTProblemSummaryAnalys2G.problemPointList;
            else if (type == 1)
                bindingSourcexq.DataSource = problemPointListTem;
            xqGrid.DataSource = bindingSourcexq;
            xqGrid.RefreshDataSource();
        }

        private void ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (strProblemType == "派单问题点")
                ExportExcelOrer();
            else
            {
                if (CQTProblemSummaryAnalysGSM.cityLevel(mainModel.DistrictID) != "")
                    ExportExcelGD();
                else
                    ExportExcel();
            }
        }

        private void ExportExcelGD()
        {
            Microsoft.Office.Interop.Excel.Application app = null;
            try
            {
                app = new Microsoft.Office.Interop.Excel.Application();
                if (app == null)
                    throw (new Exception("ERROR: EXCEL couldn't be started!"));
                app.Visible = false;
                app.UserControl = false;
                Workbooks workbooks = app.Workbooks;
                _Workbook workbook = workbooks.Add(XlWBATemplate.xlWBATWorksheet);
                Sheets sheets = workbook.Worksheets;
                app.Worksheets.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
                if (mainModel.User.DBID == -1)
                {
                    _Worksheet worksheet = (_Worksheet)sheets.get_Item(2);
                    if (worksheet == null)
                        throw (new Exception("ERROR: worksheet == null"));
                    worksheet.Name = "2G汇总表";
                    int idx = 4;
                    makeTitle(worksheet, 1, 1, "项目", 6, false);
                    makeTitle(worksheet, 1, 2, "城市类别", 8, false);
                    makeTitle(worksheet, 1, 3, "城市", 4, false);
                    makeTitle(worksheet, 1, 11, "地市总问题点数", 7, true);
                    makeTitle(worksheet, 1, 12, "测试总数", 8, false);
                    makeTitle(worksheet, 1, 13, "测试通过率", 7, true);

                    for (int i = 0; i < 5; i++)
                    {
                        makeTitle(worksheet, 1, idx++, "", 15, false);
                    }
                    worksheet.get_Range(worksheet.Cells[1, 4], worksheet.Cells[1, 8]).MergeCells = true;
                    Range firstGroupRge_1 = worksheet.Cells[1, 4] as Range;
                    firstGroupRge_1.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                    firstGroupRge_1.Value2 = "问题点统计";

                    for (int i = 0; i < 2; i++)
                    {
                        makeTitle(worksheet, 1, idx++, "", 10, false);
                    }
                    worksheet.get_Range(worksheet.Cells[1, 9], worksheet.Cells[1, 10]).MergeCells = true;
                    Range firstGroupRge_2 = worksheet.Cells[1, 9] as Range;
                    firstGroupRge_2.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                    firstGroupRge_2.Value2 = "劣势点统计";

                    worksheet.get_Range(worksheet.Cells[1, 1], worksheet.Cells[2, 1]).MergeCells = true;
                    Range firstGroupRge_4 = worksheet.Cells[1, 1] as Range;
                    firstGroupRge_4.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                    //firstGroupRge_4.Value2 = " 项目"

                    worksheet.get_Range(worksheet.Cells[1, 2], worksheet.Cells[2, 2]).MergeCells = true;
                    Range firstGroupRge_5 = worksheet.Cells[1, 2] as Range;
                    firstGroupRge_5.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                    //firstGroupRge_5.Value2 = " 城市类别"

                    worksheet.get_Range(worksheet.Cells[1, 3], worksheet.Cells[2, 3]).MergeCells = true;
                    Range firstGroupRge_6 = worksheet.Cells[1, 3] as Range;
                    firstGroupRge_6.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                    //firstGroupRge_6.Value2 = " 城市"

                    worksheet.get_Range(worksheet.Cells[1, 11], worksheet.Cells[2, 11]).MergeCells = true;
                    Range firstGroupRge_7 = worksheet.Cells[1, 11] as Range;
                    firstGroupRge_7.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                    //firstGroupRge_7.Value2 = " 地市总问题点数"

                    worksheet.get_Range(worksheet.Cells[1, 12], worksheet.Cells[2, 12]).MergeCells = true;
                    Range firstGroupRge_8 = worksheet.Cells[1, 12] as Range;
                    firstGroupRge_8.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                    //firstGroupRge_8.Value2 = " 测试总数"

                    worksheet.get_Range(worksheet.Cells[1, 13], worksheet.Cells[2, 13]).MergeCells = true;
                    Range firstGroupRge_9 = worksheet.Cells[1, 13] as Range;
                    firstGroupRge_9.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                    //firstGroupRge_9.Value2 = " 测试通过率"

                    idx = 4;
                    makeTitle(worksheet, 2, idx++, "未接通", 6, false);
                    makeTitle(worksheet, 2, idx++, "掉话", 6, true);
                    makeTitle(worksheet, 2, idx++, "GSM深度覆盖不足", 8, true);
                    makeTitle(worksheet, 2, idx++, "下载速率低", 7, true);
                    makeTitle(worksheet, 2, idx++, "问题点汇总", 7, true);
                    makeTitle(worksheet, 2, idx++, "G劣", 4, false);
                    makeTitle(worksheet, 2, idx, "劣势点汇总", 7, true);

                    int rowAt = 3;
                    foreach (ProblemSummaryPont2G ti in CQTProblemSummaryAnalysGSM.problemPointSummarryList)
                    {
                        int xx = 1;
                        makeItemRow(worksheet, rowAt, xx++, ti.SProject);
                        makeItemRow(worksheet, rowAt, xx++, ti.SCityType);
                        makeItemRow(worksheet, rowAt, xx++, ti.SCity);
                        makeItemRow(worksheet, rowAt, xx++, ti.INotConnected.ToString());
                        makeItemRow(worksheet, rowAt, xx++, ti.IDroppedCalls.ToString());
                        makeItemRow(worksheet, rowAt, xx++, ti.ILessthendeep.ToString());
                        makeItemRow(worksheet, rowAt, xx++, ti.IDownLess.ToString());
                        makeItemRow(worksheet, rowAt, xx++, ti.ILessThenCole.ToString());
                        makeItemRow(worksheet, rowAt, xx++, ti.ILessGHG.ToString());
                        makeItemRow(worksheet, rowAt, xx++, ti.ILessG.ToString());
                        makeItemRow(worksheet, rowAt, xx++, ti.ICityColePoint.ToString());
                        makeItemRow(worksheet, rowAt, xx++, ti.ITestCole.ToString());
                        makeItemRow(worksheet, rowAt, xx, ti.ITestSucess);

                        rowAt++;
                    }
                }
                _Worksheet worksheetxq = (_Worksheet)sheets.get_Item(1);
                if (worksheetxq == null)
                    throw (new Exception("ERROR: worksheetxq == null"));
                worksheetxq.Name = "2G详情表";
                int idxxq = 1;
                makeTitle(worksheetxq, 1, idxxq++, "序号", 4, false);
                makeTitle(worksheetxq, 1, idxxq++, "轮次", 6, false);
                makeTitle(worksheetxq, 1, idxxq++, "城市", 4, false);
                makeTitle(worksheetxq, 1, idxxq++, "测试日期", 12, false);
                makeTitle(worksheetxq, 1, idxxq++, "项目类型", 12, false);
                makeTitle(worksheetxq, 1, idxxq++, "测试点名称", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "测试点经度", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "测试点纬度", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "测试点属性", 7, false);
                makeTitle(worksheetxq, 1, idxxq++, "场所属性", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "覆盖属性", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "匹配站点信息", 9, true);
                makeTitle(worksheetxq, 1, idxxq++, "重要等级", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "主问题类别", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "次问题类别", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "问题点位置", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "测试文件存放路径", 9, true);
                makeTitle(worksheetxq, 1, idxxq++, "原因分析", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "原因类别", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "初步分析", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "是否已解决", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "主要问题点原因分析", 9, true);
                makeTitle(worksheetxq, 1, idxxq++, "次要问题点原因分析", 9, true);
                makeTitle(worksheetxq, 1, idxxq++, "问题点类型", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "解决方案", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "预计解决时间", 7, true);
                makeTitle(worksheetxq, 1, idxxq, "如因为物业或工程建设原因，请写明具体理由", 11, true);

                addRowInfoGD(worksheetxq);
                app.Visible = true;
                app.UserControl = true;
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(this, "导出 Excel出错：" + ex.Message);
            }
        }

        private void addRowInfoGD(_Worksheet worksheetxq)
        {
            int rowAtxq = 2;
            foreach (ProblemPont2G ti in problemPointListTem)
            {
                int xx = 1;
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.IID.ToString());
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.STurn);
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.SCity);
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.STestTime.ToString("yyyy/MM/dd"));
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.SProject);
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.STestPoint);
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.DLongitude.ToString());
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.DLatitude.ToString());
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.SSitePro);
                makeItemRow(worksheetxq, rowAtxq, xx++, "-");
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.StrCoverType);
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.StrValue8);
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.SImportLeve);
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.SMainType);
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.SSecondType);
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.SPointPosition);
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.STestFilePosition);
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.SReasonAna);
                if (ti.SReasonType == "")
                    makeItemRow(worksheetxq, rowAtxq, xx++, "  ");
                else
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SReasonType);
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.SPreliminaryAna);
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.SSolve);
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.SPointAna);
                makeItemRow(worksheetxq, rowAtxq, xx++, "-");
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.SPointType);
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.SSolveScheme);
                makeItemRow(worksheetxq, rowAtxq, xx++, ti.SExceptSolveTime);
                makeItemRow(worksheetxq, rowAtxq, xx, "-");

                rowAtxq++;
            }
        }

        private void ExportExcel()
        {
            Microsoft.Office.Interop.Excel.Application app = null;
            try
            {
                app = new Microsoft.Office.Interop.Excel.Application();
                if (app == null)
                    throw (new Exception("ERROR: EXCEL couldn't be started!"));
                app.Visible = false;
                app.UserControl = false;
                Workbooks workbooks = app.Workbooks;
                _Workbook workbook = workbooks.Add(XlWBATemplate.xlWBATWorksheet);
                Sheets sheets = workbook.Worksheets;
                app.Worksheets.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
                _Worksheet worksheet = (_Worksheet)sheets.get_Item(2);
                if (worksheet == null)
                    throw (new Exception("ERROR: worksheet == null"));
                worksheet.Name = "2G汇总表";
                int iless = 3;
#if CQTProblemAna
                iless = 2;
#endif
                int idx = 4;
                makeTitle(worksheet, 1, 1, "项目", 6, false);
                makeTitle(worksheet, 1, 2, "城市类别", 8, false);
                makeTitle(worksheet, 1, 3, "城市", 4, false);
                makeTitle(worksheet, 1, 15 + iless, "地市总问题点数", 7, true);
                makeTitle(worksheet, 1, 16 + iless, "测试总数", 8, false);
                makeTitle(worksheet, 1, 17 + iless, "测试通过率", 7, true);

                for (int i = 0; i < 9; i++)
                {
                    makeTitle(worksheet, 1, idx++, "", 15, false);
                }
                worksheet.get_Range(worksheet.Cells[1, 4], worksheet.Cells[1, 12]).MergeCells = true;
                Range firstGroupRge_1 = worksheet.Cells[1, 4] as Range;
                firstGroupRge_1.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                firstGroupRge_1.Value2 = "问题点统计";

                for (int i = 0; i < 2; i++)
                {
                    makeTitle(worksheet, 1, idx++, "", 10, false);
                }
                worksheet.get_Range(worksheet.Cells[1, 13], worksheet.Cells[1, 14]).MergeCells = true;
                Range firstGroupRge_2 = worksheet.Cells[1, 13] as Range;
                firstGroupRge_2.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                firstGroupRge_2.Value2 = "不达标点统计";

                for (int i = 0; i < iless; i++)
                {
                    makeTitle(worksheet, 1, idx++, "", 10, false);
                }
                worksheet.get_Range(worksheet.Cells[1, 15], worksheet.Cells[1, 14 + iless]).MergeCells = true;
                Range firstGroupRge_3 = worksheet.Cells[1, 15] as Range;
                firstGroupRge_3.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                firstGroupRge_3.Value2 = "劣势点统计";


                worksheet.get_Range(worksheet.Cells[1, 1], worksheet.Cells[2, 1]).MergeCells = true;
                Range firstGroupRge_4 = worksheet.Cells[1, 1] as Range;
                firstGroupRge_4.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                //firstGroupRge_4.Value2 = " 项目"

                worksheet.get_Range(worksheet.Cells[1, 2], worksheet.Cells[2, 2]).MergeCells = true;
                Range firstGroupRge_5 = worksheet.Cells[1, 2] as Range;
                firstGroupRge_5.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                //firstGroupRge_5.Value2 = " 城市类别"

                worksheet.get_Range(worksheet.Cells[1, 3], worksheet.Cells[2, 3]).MergeCells = true;
                Range firstGroupRge_6 = worksheet.Cells[1, 3] as Range;
                firstGroupRge_6.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                //firstGroupRge_6.Value2 = " 城市"

                worksheet.get_Range(worksheet.Cells[1, 15 + iless], worksheet.Cells[2, 15 + iless]).MergeCells = true;
                Range firstGroupRge_7 = worksheet.Cells[1, 15 + iless] as Range;
                firstGroupRge_7.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                //firstGroupRge_7.Value2 = " 地市总问题点数"

                worksheet.get_Range(worksheet.Cells[1, 16 + iless], worksheet.Cells[2, 16 + iless]).MergeCells = true;
                Range firstGroupRge_8 = worksheet.Cells[1, 16 + iless] as Range;
                firstGroupRge_8.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                //firstGroupRge_8.Value2 = " 测试总数"

                worksheet.get_Range(worksheet.Cells[1, 17 + iless], worksheet.Cells[2, 17 + iless]).MergeCells = true;
                Range firstGroupRge_9 = worksheet.Cells[1, 17 + iless] as Range;
                firstGroupRge_9.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                //firstGroupRge_9.Value2 = " 测试通过率"

                idx = 4;
                makeTitle(worksheet, 2, idx++, "未接通", 6, false);
                makeTitle(worksheet, 2, idx++, "掉话", 6, true);
                makeTitle(worksheet, 2, idx++, "弱覆盖", 6, false);
                makeTitle(worksheet, 2, idx++, "下载速率低", 7, true);
                makeTitle(worksheet, 2, idx++, "感知掉话", 6, true);
                makeTitle(worksheet, 2, idx++, "话质差", 7, true);
                makeTitle(worksheet, 2, idx++, "下载掉线", 6, true);
                makeTitle(worksheet, 2, idx++, "下载超时", 6, true);
                makeTitle(worksheet, 2, idx++, "问题点汇总", 7, true);
                makeTitle(worksheet, 2, idx++, "GSM深度覆盖不达标", 8, true);
                makeTitle(worksheet, 2, idx++, "不达标点汇总", 7, true);
#if CQTProblemAna
                makeTitle(worksheet, 2, idx++, "G劣", 8, true);
#else
                makeTitle(worksheet, 2, idx++, "劣于1家竞争对手", 8, true);
                makeTitle(worksheet, 2, idx++, "劣于2家竞争对手", 8, true);
#endif
                makeTitle(worksheet, 2, idx, "劣势点汇总", 7, true);

                int rowAt = 3;
#if MYCQT_Para
                foreach (ProblemSummaryPont2G ti in CQTProblemSummaryAnalys2G.problemPointSummarryList)
#else
                foreach (ProblemSummaryPont2G ti in CQTProblemSummaryAnalysGSM.problemPointSummarryList)
#endif
                {
                    int xx = 1;
                    makeItemRow(worksheet, rowAt, xx++, ti.SProject);
                    makeItemRow(worksheet, rowAt, xx++, ti.SCityType);
                    makeItemRow(worksheet, rowAt, xx++, ti.SCity);
                    makeItemRow(worksheet, rowAt, xx++, ti.INotConnected.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.IDroppedCalls.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.IWeakCoverage.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.IDownLess.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.IFillDropedCall.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.IPoorVoice.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.IDownDrop.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.IDownTimeOut.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.ILessThenCole.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.ILessThenDeepGSM.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.ILessThenDeepCole.ToString());
#if CQTProblemAna
                    makeItemRow(worksheet, rowAt, xx++, ti.ILessGHG.ToString());
#else
                    makeItemRow(worksheet, rowAt, xx++, ti.ILessG1.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.ILessG2.ToString());
#endif
                    makeItemRow(worksheet, rowAt, xx++, ti.ILessG.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.ICityColePoint.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ti.ITestCole.ToString());
                    makeItemRow(worksheet, rowAt, xx, ti.ITestSucess);

                    rowAt++;
                }
                _Worksheet worksheetxq = (_Worksheet)sheets.get_Item(1);
                if (worksheetxq == null)
                    throw (new Exception("ERROR: worksheetxq == null"));
                worksheetxq.Name = "2G详情表";
                int idxxq = 1;
                makeTitle(worksheetxq, 1, idxxq++, "序号", 4, false);
                makeTitle(worksheetxq, 1, idxxq++, "轮次", 6, false);
                makeTitle(worksheetxq, 1, idxxq++, "城市", 4, false);
                makeTitle(worksheetxq, 1, idxxq++, "测试日期", 12, false);
                makeTitle(worksheetxq, 1, idxxq++, "项目类型", 12, false);
                makeTitle(worksheetxq, 1, idxxq++, "测试点名称", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "测试点属性", 7, false);
                makeTitle(worksheetxq, 1, idxxq++, "场所属性", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "覆盖属性", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "匹配站点信息", 9, true);
                makeTitle(worksheetxq, 1, idxxq++, "重要等级", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "主问题类别", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "次问题类别", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "问题点位置", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "测试文件存放路径", 9, true);
                makeTitle(worksheetxq, 1, idxxq++, "原因分析", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "原因类别", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "初步分析", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "是否已解决", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "主要问题点原因分析", 9, true);
                makeTitle(worksheetxq, 1, idxxq++, "次要问题点原因分析", 9, true);
                makeTitle(worksheetxq, 1, idxxq++, "问题点类型", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "解决方案", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "预计解决时间", 7, true);
                makeTitle(worksheetxq, 1, idxxq, "如因为物业或工程建设原因，请写明具体理由", 11, true);

                int rowAtxq = 2;
#if MYCQT_Para
                foreach (ProblemPont2G ti in CQTProblemSummaryAnalys2G.problemPointList)
#else
                foreach (ProblemPont2G ti in problemPointListTem)
#endif
                {
                    int xx = 1;
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.IID.ToString());
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.STurn);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SCity);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.STestTime.ToString("yyyy/MM/dd"));
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SProject);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.STestPoint);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SSitePro);
                    makeItemRow(worksheetxq, rowAtxq, xx++, "-");
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.StrCoverType);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.StrValue8);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SImportLeve);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SMainType);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SSecondType);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SPointPosition);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.STestFilePosition);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SReasonAna);
                    if (ti.SReasonType == "")
                        makeItemRow(worksheetxq, rowAtxq, xx++, "  ");
                    else
                        makeItemRow(worksheetxq, rowAtxq, xx++, ti.SReasonType);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SPreliminaryAna);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SSolve);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SPointAna);
                    makeItemRow(worksheetxq, rowAtxq, xx++, "-");
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SPointType);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SSolveScheme);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SExceptSolveTime);
                    makeItemRow(worksheetxq, rowAtxq, xx, "-");

                    rowAtxq++;
                }
                app.Visible = true;
                app.UserControl = true;
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(this, "导出 Excel出错：" + ex.Message);
            }
        }
        private void ExportExcelOrer()
        {
            Microsoft.Office.Interop.Excel.Application app = null;
            try
            {
                app = new Microsoft.Office.Interop.Excel.Application();
                if (app == null)
                    throw (new Exception("ERROR: EXCEL couldn't be started!"));
                app.Visible = false;
                app.UserControl = false;
                Workbooks workbooks = app.Workbooks;
                _Workbook workbook = workbooks.Add(XlWBATemplate.xlWBATWorksheet);
                Sheets sheets = workbook.Worksheets;
                app.Worksheets.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);
                if (mainModel.User.DBID == -1)
                {
                    _Worksheet worksheet = (_Worksheet)sheets.get_Item(2);
                    if (worksheet == null)
                        throw (new Exception("ERROR: worksheet == null"));
                    worksheet.Name = "2G汇总表";
                    int idx = 4;
                    for (int i = 0; i < 5; i++)
                    {
                        makeTitle(worksheet, 1, idx++, "", 15, false);
                    }
                    worksheet.get_Range(worksheet.Cells[1, 4], worksheet.Cells[1, 8]).MergeCells = true;
                    Range firstGroupRge_1 = worksheet.Cells[1, 4] as Range;
                    firstGroupRge_1.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                    firstGroupRge_1.Value2 = "问题点统计";

                    worksheet.get_Range(worksheet.Cells[1, 1], worksheet.Cells[2, 1]).MergeCells = true;
                    Range firstGroupRge_4 = worksheet.Cells[1, 1] as Range;
                    firstGroupRge_4.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                    firstGroupRge_4.Value2 = " 项目";

                    worksheet.get_Range(worksheet.Cells[1, 2], worksheet.Cells[2, 2]).MergeCells = true;
                    Range firstGroupRge_5 = worksheet.Cells[1, 2] as Range;
                    firstGroupRge_5.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                    firstGroupRge_5.Value2 = " 城市类别";

                    worksheet.get_Range(worksheet.Cells[1, 3], worksheet.Cells[2, 3]).MergeCells = true;
                    Range firstGroupRge_6 = worksheet.Cells[1, 3] as Range;
                    firstGroupRge_6.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                    firstGroupRge_6.Value2 = " 城市";

                    worksheet.get_Range(worksheet.Cells[1, 9], worksheet.Cells[2, 9]).MergeCells = true;
                    Range firstGroupRge_7 = worksheet.Cells[1, 9] as Range;
                    firstGroupRge_7.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                    firstGroupRge_7.Value2 = "地市总问题点数";

                    worksheet.get_Range(worksheet.Cells[1, 10], worksheet.Cells[2, 10]).MergeCells = true;
                    Range firstGroupRge_8 = worksheet.Cells[1, 10] as Range;
                    firstGroupRge_8.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                    firstGroupRge_8.Value2 = "测试总数";

                    worksheet.get_Range(worksheet.Cells[1, 11], worksheet.Cells[2, 11]).MergeCells = true;
                    Range firstGroupRge_9 = worksheet.Cells[1, 11] as Range;
                    firstGroupRge_9.HorizontalAlignment = XlHAlign.xlHAlignCenter;
                    firstGroupRge_9.Value2 = "测试通过率";

                    idx = 4;
                    makeTitle(worksheet, 2, idx++, "未接通", 6, false);
                    makeTitle(worksheet, 2, idx++, "掉话", 6, true);
                    makeTitle(worksheet, 2, idx++, "下载掉线", 8, true);
                    makeTitle(worksheet, 2, idx++, "下载速率低", 7, true);
                    makeTitle(worksheet, 2, idx, "问题点汇总", 7, true);

                    int rowAt = 3;
                    foreach (ProblemSummaryPont2G ti in CQTProblemSummaryAnalysGSM.problemPointSummarryList)
                    {
                        int xx = 1;
                        makeItemRow(worksheet, rowAt, xx++, ti.SProject);
                        makeItemRow(worksheet, rowAt, xx++, ti.SCityType);
                        makeItemRow(worksheet, rowAt, xx++, ti.SCity);
                        makeItemRow(worksheet, rowAt, xx++, ti.INotConnected.ToString());
                        makeItemRow(worksheet, rowAt, xx++, ti.IDroppedCalls.ToString());
                        makeItemRow(worksheet, rowAt, xx++, ti.IDownDrop.ToString());
                        makeItemRow(worksheet, rowAt, xx++, ti.IDownLess.ToString());
                        makeItemRow(worksheet, rowAt, xx++, ti.ILessThenCole.ToString());
                        makeItemRow(worksheet, rowAt, xx++, ti.ICityColePoint.ToString());
                        makeItemRow(worksheet, rowAt, xx++, ti.ITestCole.ToString());
                        makeItemRow(worksheet, rowAt, xx, ti.ITestSucess);

                        rowAt++;
                    }
                }
                _Worksheet worksheetxq = (_Worksheet)sheets.get_Item(1);
                if (worksheetxq == null)
                    throw (new Exception("ERROR: worksheetxq == null"));
                worksheetxq.Name = "2G详情表";
                int idxxq = 1;
                makeTitle(worksheetxq, 1, idxxq++, "序号", 4, false);
                makeTitle(worksheetxq, 1, idxxq++, "轮次", 6, false);
                makeTitle(worksheetxq, 1, idxxq++, "城市", 4, false);
                makeTitle(worksheetxq, 1, idxxq++, "测试日期", 12, false);
                makeTitle(worksheetxq, 1, idxxq++, "项目类型", 12, false);
                makeTitle(worksheetxq, 1, idxxq++, "测试点名称", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "测试点经度", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "测试点纬度", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "测试点属性", 7, false);
                makeTitle(worksheetxq, 1, idxxq++, "场所属性", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "覆盖属性", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "匹配站点信息", 9, true);
                makeTitle(worksheetxq, 1, idxxq++, "重要等级", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "主问题类别", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "次问题类别", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "问题点位置", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "测试文件存放路径", 9, true);
                makeTitle(worksheetxq, 1, idxxq++, "原因分析", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "原因类别", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "初步分析", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "是否已解决", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "主要问题点原因分析", 9, true);
                makeTitle(worksheetxq, 1, idxxq++, "次要问题点原因分析", 9, true);
                makeTitle(worksheetxq, 1, idxxq++, "问题点类型", 7, true);
                makeTitle(worksheetxq, 1, idxxq++, "解决方案", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "预计解决时间", 7, true);
                makeTitle(worksheetxq, 1, idxxq, "如因为物业或工程建设原因，请写明具体理由", 11, true);

                int rowAtxq = 2;
                foreach (ProblemPont2G ti in problemPointListTem)
                {
                    int xx = 1;
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.IID.ToString());
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.STurn);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SCity);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.STestTime.ToString("yyyy/MM/dd"));
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SProject);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.STestPoint);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.DLongitude.ToString());
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.DLatitude.ToString());
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SSitePro);
                    makeItemRow(worksheetxq, rowAtxq, xx++, "-");
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.StrCoverType);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.StrValue8);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SImportLeve);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SMainType);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SSecondType);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SPointPosition);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.STestFilePosition);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SReasonAna);
                    if (ti.SReasonType == "")
                        makeItemRow(worksheetxq, rowAtxq, xx++, "  ");
                    else
                        makeItemRow(worksheetxq, rowAtxq, xx++, ti.SReasonType);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SPreliminaryAna);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SSolve);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SPointAna);
                    makeItemRow(worksheetxq, rowAtxq, xx++, "-");
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SPointType);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SSolveScheme);
                    makeItemRow(worksheetxq, rowAtxq, xx++, ti.SExceptSolveTime);
                    makeItemRow(worksheetxq, rowAtxq, xx, "-");

                    rowAtxq++;
                }
                app.Visible = true;
                app.UserControl = true;
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(this, "导出 Excel出错：" + ex.Message);
            }
        }

        public static void makeTitle(_Worksheet worksheet, int row, int col, string title, int width, bool wraptext)
        {
            Range range = worksheet.Cells[row, col] as Range;
            range.Value2 = title;
            range.ColumnWidth = width;
            range.WrapText = wraptext;
        }

        public static void makeItemRow(_Worksheet worksheet, int row, int column, string str)
        {
            Range range = worksheet.Cells[row, column] as Range;
            range.Value2 = str;
        }

        private void GExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ExportExcel();
        }

        public static void MergeCell(_Worksheet worksheet, int rowlt, int collt, int rowrb, int colrb, string text)
        {
            worksheet.get_Range(worksheet.Cells[rowlt, collt], worksheet.Cells[rowrb, colrb]).MergeCells = true;
            Range firstGroupRge = worksheet.Cells[rowlt, collt] as Range;
            firstGroupRge.HorizontalAlignment = XlHAlign.xlHAlignCenter;
            firstGroupRge.Value2 = text;
        }
    }
}