﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using System.Drawing;
using MapWinGIS;
using MasterCom.MTGis;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.Net
{
	public class ZTDIYWeakQualAnaByRegion : DIYSampleByRegion
	{
		int coverLapThreshold = -90;
		double coverLapDistanceTimes = 1.6;
		int weakCovMainCellThreshold = -85;
		int weakCovNbCell1Threshold = -85;
		int noMainCellDifferRxlev = 6;
		int noMainCellCellCount = 3;
		int c2iThreshold = 12;
		int handoverProblemSecond = 3;
		int handoverFreqSecond = 20;
		int backCovDegree = 90;
		int backCovDistance = 500;
		/// <summary>
		/// 判断原因的先后顺序
		/// </summary>
		List<string> reasonsOrder = null;

		public ZTDIYWeakQualAnaByRegion(MainModel mainmodel)
			: base(mainmodel)
		{
			isAddSampleToDTDataManager = false;
		}

		Dictionary<string, Dictionary<string, ReasonClassifyInfo>> regionReasonClassifyInfoDicDic = null;
		/// <summary>
		/// 汇总值
		/// </summary>
		Dictionary<string, ReasonClassifyInfo> gatherReasonClassifyInfoDic = null;

		/// <summary>
		/// 小区信息字典
		/// </summary>
		Dictionary<CellSub, WeakQualCellInfo> CellWeakQualCellInfoDic = null;

		/// <summary>
		/// 小区与其总采样点数字典
		/// </summary>
		Dictionary<string, int> cellTestpointAllCountDic = null;
        
		//多重区域分开处理
		List<ResvRegion> resvRegions { get; set; }
		Shape gmt;

		public override string Name
		{
			get { return "质差采样点原因分析(按区域)"; }
		}

		protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
		{
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12056, this.Name);
		}

		protected void getReadyBeforeQuery()
		{
			//先清除数据
			btsList.Clear();
			condEventsDic.Clear();
			atuIds.Clear();
			idCommentDic.Clear();
			regionEquipInfoDicDic.Clear();
			regionTestpointWeakRxQualCountDic.Clear();
			regionTestpointCountDic.Clear();
			testpointCount = 0;
			testpointWeakRxQualCount = 0;
			fileTimeList.Clear();
			tpList.Clear();
			modulusParamList.Clear();
			regionMapOp2Dic.Clear();
			isCurShapeFillPolygon = false;

			PrepareEvents();
			PrepareEquipmentForQualBurr();

#if Guangdong
			PrepareNetworkModulus();
#endif

			regionReasonClassifyInfoDicDic = new Dictionary<string, Dictionary<string, ReasonClassifyInfo>>();

			if (regionCount > 1)
				gatherReasonClassifyInfoDic = buildInfo();

			CellWeakQualCellInfoDic = new Dictionary<CellSub, WeakQualCellInfo>();
			cellTestpointAllCountDic = new Dictionary<string, int>();
		}


		private Dictionary<string, ReasonClassifyInfo> buildInfo()
		{
			Dictionary<string, ReasonClassifyInfo> reasonClassifyInfoDic = new Dictionary<string, ReasonClassifyInfo>();
			foreach (string reason in reasonsOrder)
			{
				ReasonClassifyInfo info = new ReasonClassifyInfo();
				info.Reason = reason;
				reasonClassifyInfoDic.Add(reason, info);
			}

			return reasonClassifyInfoDic;
		}

		protected override void query()
		{
			WeakQualReasonSettingDlg wDlg = WeakQualReasonSettingDlg.GetInstance();
			if (wDlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
			{
				return;
			}
			coverLapThreshold = wDlg.CoverLapThreshold;
			coverLapDistanceTimes = wDlg.CoverLapDistanceTimes;
			weakCovMainCellThreshold = wDlg.WeakCovMainCellThreshold;
			weakCovNbCell1Threshold = wDlg.WeakCovNbCell1Threshold;
			noMainCellDifferRxlev = wDlg.NoMainCellDifferRxlev;
			noMainCellCellCount = wDlg.NoMainCellCellCount;
			c2iThreshold = wDlg.C2IThreshold;
			handoverProblemSecond = wDlg.HandoverProblemSecond;
			handoverFreqSecond = wDlg.HandoverFreqSecond;
			backCovDegree = wDlg.BackCovDegree;
			backCovDistance = wDlg.BackCovDistance;
			reasonsOrder = wDlg.ReasonsOrder;

			ClientProxy clientProxy = new ClientProxy();
			try
			{
				SelectRegion();
				getReadyBeforeQuery();


				curSelDIYSampleGroup = getCurSelDIYSampleGroupPrepare();
				if (curSelDIYSampleGroup == null)
					return;

				if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
				{
					ErrorInfo = "连接服务器失败!";
					return;
				}

				WaitBox.CanCancel = true;
				WaitBox.Text = "正在查询...";


				WaitBox.Show("开始统计数据...", queryInThread, clientProxy);

			}
			catch
			{
				clientProxy.Close();
			}
			finally
			{
				MainModel.ClearDTData();
			}

			if (regionCount > 1)
				regionReasonClassifyInfoDicDic.Add("全网（汇总）", gatherReasonClassifyInfoDic);

			foreach (WeakQualCellInfo cellinfo in CellWeakQualCellInfoDic.Values)
			{
				if (cellinfo.CellName == "未知小区")
					cellinfo.ProblemRoad = "未知";
				else
					cellinfo.ProblemRoad = GISManager.GetInstance().GetRoadPlaceDesc(cellinfo.Longitude, cellinfo.Latitude);
			}

			if (regionReasonClassifyInfoDicDic.Count==0)
			{
				XtraMessageBox.Show("查询区域不存在质差点");
				return;
			}
			MainModel.WeakQualReasonInfo = regionReasonClassifyInfoDicDic;
			MainModel.CellWeakQualCellInfoDic = CellWeakQualCellInfoDic;
            fireShowForm();
		}

        private void fireShowForm()
        {
            WeakQualAnaForm frm = MainModel.GetInstance().CreateResultForm(typeof(WeakQualAnaForm)) as WeakQualAnaForm;
            frm.FillData();
            frm.Visible = true;
            frm.BringToFront();
        }

		protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
		{
			List<object> columnsDef = new List<object>();
			Dictionary<string, object> param = new Dictionary<string, object>();
			param["param_name"] = "RxLevSub";
			param["param_arg"] = 0;
			columnsDef.Add((object)param);

			param = new Dictionary<string, object>();
			param["param_name"] = "LAC";
			param["param_arg"] = 0;
			columnsDef.Add((object)param);

			param = new Dictionary<string, object>();
			param["param_name"] = "CI";
			param["param_arg"] = 0;
			columnsDef.Add((object)param);

			param = new Dictionary<string, object>();
			param["param_name"] = "BCCH";
			param["param_arg"] = 0;
			columnsDef.Add((object)param);

			param = new Dictionary<string, object>();
			param["param_name"] = "N_TCH";
			param["param_arg"] = 0;
			columnsDef.Add((object)param);

			param = new Dictionary<string, object>();
			param["param_name"] = "TCH";
			param["param_arg"] = 0;
			columnsDef.Add((object)param);

			param = new Dictionary<string, object>();
			param["param_name"] = "BSIC";
			param["param_arg"] = 0;
			columnsDef.Add((object)param);

			param = new Dictionary<string, object>();
			param["param_name"] = "RxQualSub";
			param["param_arg"] = 0;
			columnsDef.Add((object)param);

			param = new Dictionary<string, object>();
			param["param_name"] = "C_I";
			param["param_arg"] = 0;
			columnsDef.Add((object)param);

			param = new Dictionary<string, object>();
			param["param_name"] = "N_RxLev";
			param["param_arg"] = 0;
			columnsDef.Add((object)param);

			param = new Dictionary<string, object>();
			param["param_name"] = "SpeechCodec";
			param["param_arg"] = 0;
			columnsDef.Add((object)param);

			Dictionary<string, object> tmpDic = new Dictionary<string, object>();
			tmpDic.Add("name", (object)"GSM NoMainCell");
			tmpDic.Add("themeName", (object)"GSM RxLevSub");
			tmpDic.Add("columnsDef", (object)columnsDef);

			DIYSampleGroup group = new DIYSampleGroup();
			group.Param = tmpDic;
			return group;
		}


		//1.预存区域;2.多个矩形区域;3.单个矩形区域
		int iRegInit = 0;
		Dictionary<string, Shape> regionDic { get; set; } = new Dictionary<string, Shape>();
		Dictionary<string, Shape> polygonDic { get; set; } = new Dictionary<string, Shape>();
		Shape curShape;
		int regionCount = 0;
		private void SelectRegion()
		{
			regionDic.Clear();
			polygonDic.Clear();
			curShape = null;
			regionCount = 0;
			resvRegions = MainModel.SearchGeometrys.SelectedResvRegions;
			gmt = MainModel.SearchGeometrys.Region;
			if (resvRegions != null && resvRegions.Count > 0)  //预存区域
			{
				iRegInit = 1;
				foreach (ResvRegion region in resvRegions)
				{
					if (!regionDic.ContainsKey(region.RegionName))
					{
						regionDic[region.RegionName] = region.Shape;
						regionCount++;
					}
				}
			}
			else if (gmt != null)//单个区域
			{
				iRegInit = 3;
				curShape = gmt;
				regionCount = 1;
			}
		}

		Dictionary<string, MapOperation2> regionMapOp2Dic { get; set; } = new Dictionary<string, MapOperation2>();
		bool isCurShapeFillPolygon = false;
		MapOperation2 mapOp2;
		/// <summary>
		/// 定位是否为包括的采样点
		/// </summary>
		/// <param name="iType"></param>
		/// <returns>返回区域名称</returns>
		private string isContainPoint(DbPoint dPoint)
		{
            if (iRegInit == 1)
            {
                foreach (string strKey in regionDic.Keys)
                {
                    getRegionMapOp2Dic(strKey);

                    if (mapOp2.CheckPointInRegion(dPoint.x, dPoint.y))
                    {
                        return strKey;
                    }
                }
            }
            else if (iRegInit == 3 && curShape != null)
            {
                if (!isCurShapeFillPolygon)
                {
                    mapOp2 = new MapOperation2();
                    mapOp2.FillPolygon(curShape);
                    isCurShapeFillPolygon = true;
                }

                if (mapOp2.CheckPointInRegion(dPoint.x, dPoint.y))
                {
                    return "当前区域";
                }
            }

			return null;
		}

        private void getRegionMapOp2Dic(string strKey)
        {
            if (regionMapOp2Dic.ContainsKey(strKey))
            {
                mapOp2 = regionMapOp2Dic[strKey];
            }
            else
            {
                mapOp2 = new MapOperation2();
                Shape fgeo = regionDic[strKey];
                mapOp2.FillPolygon(fgeo);
                regionMapOp2Dic.Add(strKey, mapOp2);
            }
        }

        /// <summary>
        /// 网格下的质差采样点数目字典
        /// </summary>
        Dictionary<string, int> regionTestpointWeakRxQualCountDic { get; set; } = new Dictionary<string, int>();

		/// <summary>
		/// 网格下的总采样点数目字典
		/// </summary>
		Dictionary<string, int> regionTestpointCountDic { get; set; } = new Dictionary<string, int>();

		/// <summary>
		/// 汇总的总采样点数目
		/// </summary>
		int testpointCount = 0;

		/// <summary>
		/// 汇总的全部质差采样点数目
		/// </summary>
		int testpointWeakRxQualCount = 0;

		List<TestPoint> tpList { get; set; } = new List<TestPoint>();
		List<FileTimeKey> fileTimeList { get; set; } = new List<FileTimeKey>();

        protected override void doWithDTData(TestPoint tp)
        {
            string strRegionName = isContainPoint(new DbPoint(tp.Longitude, tp.Latitude));
            if (strRegionName == null)
                return;

            Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic;
            if (regionReasonClassifyInfoDicDic.ContainsKey(strRegionName))
            {
                curReasonClassifyInfoDic = regionReasonClassifyInfoDicDic[strRegionName];
            }
            else
            {
                curReasonClassifyInfoDic = buildInfo();
                regionReasonClassifyInfoDicDic[strRegionName] = curReasonClassifyInfoDic;
            }

            ///获取当前网格区域下的质差采样点数目（独立统计的道路黑点，室分占比除外）
            if (!regionTestpointWeakRxQualCountDic.ContainsKey(strRegionName))
            {
                regionTestpointWeakRxQualCountDic.Add(strRegionName, 0);
            }

            ///获取当前网格区域下的总采样点数目
            if (!regionTestpointCountDic.ContainsKey(strRegionName))
            {
                regionTestpointCountDic.Add(strRegionName, 0);
            }

            //进行质差采样点原因分析
            dealTPInfo(tp, strRegionName, curReasonClassifyInfoDic);
        }

        private void dealTPInfo(TestPoint tp, string strRegionName, Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic)
        {
            if (tp is TestPointDetail)
            {
                try
                {
                    dealTestPointDetail(tp, strRegionName, curReasonClassifyInfoDic);
                }
                catch
                {
                    //continue
                }
            }
        }

        private void dealTestPointDetail(TestPoint tp, string strRegionName, Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic)
        {
            bool isAnaCell = true;

            int? rxQual = (int?)(byte?)tp["RxQualSub"];
            if (rxQual != null)
            {
                testpointCount++;
                regionTestpointCountDic[strRegionName]++;

                Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (ushort?)(int?)tp["LAC"], (ushort?)(int?)tp["CI"], (short?)tp["BCCH"], (byte?)tp["BSIC"], tp.Longitude, tp.Latitude);
                CellSub cellSub;
                addCellTestpointAllCountDic(tp, cell, out cell, out cellSub);
                //采样点的RxQualSub在[5,7]范围内
                if (IsValidRxlev(tp))
                {
                    #region 原因分析概况
                    testpointWeakRxQualCount++;
                    regionTestpointWeakRxQualCountDic[strRegionName]++;

                    //当前采样点的原因
                    //质差原因分类(优先级别按前后顺序)：
                    WeakQualCellInfo.reasonTestpoint curReasonTp = new WeakQualCellInfo.reasonTestpoint("", tp);

                    anaFileTimeTP(tp, strRegionName, ref curReasonClassifyInfoDic, cell, ref curReasonTp);

                    bool dealt = getDealt(tp, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp);

                    judgeIsAnaCell(tp, strRegionName, ref curReasonClassifyInfoDic, ref isAnaCell, ref curReasonTp, dealt);
                    #endregion

                    if (curReasonTp.reason != "" && isAnaCell)
                    {
                        anaRxqualProblemByCell(cell, cellSub, strRegionName, curReasonTp);
                    }
                }
            }
        }

        private void anaFileTimeTP(TestPoint tp, string strRegionName, ref Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, Cell cell, ref WeakQualCellInfo.reasonTestpoint curReasonTp)
        {
            if (fileTimeList.Count > 0)
            {
                FileTimeKey file = fileTimeList[fileTimeList.Count - 1];
                if (tp.FileID != file.ifileid || (int)(tp["CI"]) != file.ici)
                {
                    anaTPCellInfo(tp, strRegionName, ref curReasonClassifyInfoDic, cell, ref curReasonTp);
                    fileTimeList = new List<FileTimeKey>();
                    tpList.Clear();
                }
            }
        }

        private void anaTPCellInfo(TestPoint tp, string strRegionName, ref Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, Cell cell, ref WeakQualCellInfo.reasonTestpoint curReasonTp)
        {
            foreach (TestPoint tpSec in tpList)
            {
                if (tpSec.DateTime <= fileTimeList[0].dTime && tpSec.DateTime >= fileTimeList[0].dTime.AddSeconds(-15))
                {
                    testPointDeal(fileTimeList[0].strreason, tpSec, fileTimeList[0].strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp);
                }
                else
                {
                    Add(strRegionName, curReasonClassifyInfoDic, regionTestpointWeakRxQualCountDic[fileTimeList[0].strRegionName], "其它", tpSec);
                    if (regionCount > 1)
                        Add("其它", testpointWeakRxQualCount, tpSec);
                    curReasonTp = new WeakQualCellInfo.reasonTestpoint("其它", tpSec);
                }

                Cell cellSec;
                CellSub cellSubSec;
                addCellTestpointAllCountDic(tp, cell, out cellSec, out cellSubSec);

                anaRxqualProblemByCell(cellSec, cellSubSec, strRegionName, curReasonTp);
            }
        }

        private bool getDealt(TestPoint tp, string strRegionName, ref Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, ref WeakQualCellInfo.reasonTestpoint curReasonTp)
        {
            bool dealt = false;
            for (int i = 0; i < 10; i++)
            {
                switch (reasonsOrder[i])
                {
                    case "室分泄漏":
                        IsIndoor(tp, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp, ref dealt);
                        break;
                    case "占用不合理":
                        IsCoverLap(tp, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp, ref dealt);
                        break;
                    case "弱覆盖":
                        IsWeakCover(tp, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp, ref dealt);
                        break;
                    case "重选问题":
                        IsReselectProblem(tp, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp, ref dealt);
                        break;
                    case "覆盖杂乱":
                        IsNoMainCell(tp, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp, ref dealt);
                        break;
                    case "背向覆盖":
                        IsBackCover(tp, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp, ref dealt);
                        break;
                    case "切换不合理":
                        IsHandoverProblem(tp, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp, ref dealt);
                        break;
                    case "切换不及时":
                        IsHandoverNotInTime(tp, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp, ref dealt);
                        break;
                    case "质量毛刺":
                        IsQualBurr(tp, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp, ref dealt);
                        break;
                    case "频率干扰或故障":
                        IsInterfere_C_I(tp, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp, ref dealt);
                        break;
                    default:
                        break;
                }

                if (dealt)
                {
                    break;
                }
            }

            return dealt;
        }

        private void judgeIsAnaCell(TestPoint tp, string strRegionName, ref Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, ref bool isAnaCell, ref WeakQualCellInfo.reasonTestpoint curReasonTp, bool dealt)
        {
            if (!dealt)
            {
                if (fileTimeList.Count > 0)
                {
                    if (tp.FileID == fileTimeList[fileTimeList.Count - 1].ifileid && (int)(tp["CI"]) == fileTimeList[fileTimeList.Count - 1].ici &&
                        tp.DateTime >= fileTimeList[fileTimeList.Count - 1].dTime && tp.DateTime <= fileTimeList[fileTimeList.Count - 1].dTime.AddSeconds(15))
                    {
                        testPointDeal(fileTimeList[fileTimeList.Count - 1].strreason, tp, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp);
                    }
                    else
                    {
                        tpList.Add(tp);
                        isAnaCell = false;
                    }
                }
                else
                {
                    tpList.Add(tp);
                    isAnaCell = false;
                }
            }
        }

        private void addCellTestpointAllCountDic(TestPoint tp, Cell curCell, out Cell cell, out CellSub cellSub)
        {
            cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (ushort?)(int?)tp["LAC"], (ushort?)(int?)tp["CI"], (short?)tp["BCCH"], (byte?)tp["BSIC"], tp.Longitude, tp.Latitude);
            cellSub = new CellSub();

            if (curCell != null)
            {
                cellSub.Name = cell.Name;
                cellSub.LAC = cell.LAC;
                cellSub.CI = cell.CI;
                cellSub.Longitude = cell.Longitude;
                cellSub.Latitude = cell.Latitude;
            }
            else
            {
                cellSub.Name = "未知小区";
                cellSub.LAC = (int)tp["LAC"];
                cellSub.CI = (int)tp["CI"];
                cellSub.Longitude = 0;
                cellSub.Latitude = 0;
            }

            string cellkey = cellSub.Name + cellSub.LAC + cellSub.CI;
            if (cellTestpointAllCountDic.ContainsKey(cellkey)) //统计小区下的全部采样点数目
                cellTestpointAllCountDic[cellkey]++;
            else
                cellTestpointAllCountDic[cellkey] = 1;
        }

        private void testPointDealQualBurr(TestPoint tp, string strRegionName, ref Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, ref WeakQualCellInfo.reasonTestpoint curReasonTp)
        {
            Add(strRegionName, curReasonClassifyInfoDic, regionTestpointWeakRxQualCountDic[strRegionName], "质量毛刺", tp);
            if (regionCount > 1)
                Add("质量毛刺", testpointWeakRxQualCount, tp);
            curReasonTp = new WeakQualCellInfo.reasonTestpoint("质量毛刺", tp);

            if (curReasonClassifyInfoDic["质量毛刺"].qualBurrInfoList == null)
            {
                curReasonClassifyInfoDic["质量毛刺"].qualBurrInfoList = new List<QualBurrInfo>();
            }

            string equipmentName = AnalyseQualBurr(tp);
            Dictionary<string, QualBurrInfo> equipInfoDic = null;
            if (regionEquipInfoDicDic.ContainsKey(strRegionName))
            {
                equipInfoDic = regionEquipInfoDicDic[strRegionName];
            }
            else
            {
                equipInfoDic = new Dictionary<string, QualBurrInfo>();
                regionEquipInfoDicDic[strRegionName] = equipInfoDic;
            }

            QualBurrInfo qi;
            if (equipInfoDic.ContainsKey(equipmentName))
            {
                qi = equipInfoDic[equipmentName];
            }
            else
            {
                qi = new QualBurrInfo();
                equipInfoDic[equipmentName] = qi;
                qi.RegionType = strRegionName; //添加设备所在网格
                qi.EquipmentName = equipmentName; //设备名
            }
            setQualBurrInfo(tp, qi);

            addQualBurrInfoList(curReasonClassifyInfoDic, equipInfoDic);
        }

        private void setQualBurrInfo(TestPoint tp, QualBurrInfo qi)
        {
            if (IsHandoverSuccess(tp))
            {
                qi.IsHandover++;
            }
            else
            {
                qi.IsNotHandover++;
            }

            int? hpTch = (int?)tp["N_TCH", 0];
            int? tch = (int?)(short?)tp["TCH"];
            short? bcch = (short?)tp["BCCH"];
            BandType bandType = judgeFreqType(hpTch, tch, bcch);
            if (bandType == BandType.GSM900)
            {
                qi.Is900++;
            }
            else if (bandType == BandType.DCS1800)
            {
                qi.Is1800++;
            }

            if (IsFastFailure(tp))
            {
                qi.IsFastFailure++;
            }
            else
            {
                qi.IsOther++;
            }
        }

        private void addQualBurrInfoList(Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, Dictionary<string, QualBurrInfo> equipInfoDic)
        {
            foreach (QualBurrInfo qi in equipInfoDic.Values)
            {
                int index = getDataIndex(curReasonClassifyInfoDic, qi);

                if (index != -1)
                {
                    curReasonClassifyInfoDic["质量毛刺"].qualBurrInfoList[index].RegionType = qi.RegionType;
                    curReasonClassifyInfoDic["质量毛刺"].qualBurrInfoList[index].IsHandover = qi.IsHandover;
                    curReasonClassifyInfoDic["质量毛刺"].qualBurrInfoList[index].IsNotHandover = qi.IsNotHandover;
                    curReasonClassifyInfoDic["质量毛刺"].qualBurrInfoList[index].Is900 = qi.Is900;
                    curReasonClassifyInfoDic["质量毛刺"].qualBurrInfoList[index].Is1800 = qi.Is1800;
                    curReasonClassifyInfoDic["质量毛刺"].qualBurrInfoList[index].IsFastFailure = qi.IsFastFailure;
                    curReasonClassifyInfoDic["质量毛刺"].qualBurrInfoList[index].IsOther = qi.IsOther;
                }
                else
                {
                    curReasonClassifyInfoDic["质量毛刺"].qualBurrInfoList.Add(qi);
                }
            }
        }

        private int getDataIndex(Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, QualBurrInfo qi)
        {
            return curReasonClassifyInfoDic["质量毛刺"].qualBurrInfoList.FindIndex(
                delegate (QualBurrInfo q)
                {
                    if (q.EquipmentName == qi.EquipmentName)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
            );
            //按设备名，检索当前毛刺信息的设备在现有的设备字典中的索引
        }

        private  BandType judgeFreqType(int? hpTch, int? tch, int? bcch)
		{
			BandType type;

			//==== 用跳频列表的tch
			type = getFreqType(hpTch);

			//==== 无法判断,用tch去判断，用tch去判断
			if (type == BandType.BandUnknown)   //
			{
				type = getFreqType(tch);
			}
			else
			{
				return type;
			}

			//==== 无法判断,无法判断，用bcch去判断
			if (type == BandType.BandUnknown)   //无法判断，用bcch去判断
			{
				type = getFreqType(bcch);
			}
			else
			{
				return type;
			}

			return type;
		}

		private BandType getFreqType(int? Freq)
		{
			if (Freq == null)
			{
				return BandType.BandUnknown;   //无法判断
			}

			if ((Freq >= 0 && Freq <= 124) || (Freq >= 976 && Freq <= 1024))
			{
				return BandType.GSM900; //900
			}
			else if (Freq >= 512 && Freq <= 975)
			{
				return BandType.DCS1800;   //  1800
			}
			else
			{
				return BandType.BandUnknown;   //无法判断
			}
		}

		/// <summary>
		/// 按小区归分问题详情
		/// </summary>
		public void anaRxqualProblemByCell(Cell cell, CellSub cellSub, string strRegionName, WeakQualCellInfo.reasonTestpoint curReasonTp)
		{
			WeakQualCellInfo curCellInfo;

  #region
			if (CellWeakQualCellInfoDic.ContainsKey(cellSub))
			{
				curCellInfo = CellWeakQualCellInfoDic[cellSub];
			}
			else
			{
				curCellInfo = new WeakQualCellInfo();
				CellWeakQualCellInfoDic[cellSub] = curCellInfo;
				curCellInfo.SN = CellWeakQualCellInfoDic.Count;
			}

			curCellInfo.CellName = cellSub.Name;
			if (!curCellInfo.RegionType.Contains(strRegionName) && curCellInfo.RegionType != "")
			{
				curCellInfo.RegionType += ";" + strRegionName;
			}
			else
			{
				curCellInfo.RegionType = strRegionName;
			}

			curCellInfo.ReasonTpList.Add(curReasonTp);
			curCellInfo.Lac = cellSub.LAC;
			curCellInfo.Ci = cellSub.CI;
			curCellInfo.Longitude = cellSub.Longitude;
			curCellInfo.Latitude = cellSub.Latitude;

			if(cell==null)
			{
				curCellInfo.SumTpDistance = 0;
			}
			else
			{
				curCellInfo.SumTpDistance += cell.GetDistance(curReasonTp.tp.Longitude, curReasonTp.tp.Latitude);
			}

			switch (curReasonTp.reason)
			{
				case "室分泄漏":
					curCellInfo.Indoor++;
					break;
				case "占用不合理":
					curCellInfo.CoverLap++;
					break;
				case "弱覆盖":
					curCellInfo.WeakCover++;
					break;      
				case "重选问题":
					curCellInfo.ReselectProblem++;
					break;
				case "覆盖杂乱":
					curCellInfo.NoMainCell++;
					break;
				case "背向覆盖":
					curCellInfo.BackCover++;
					break;
				case "切换不及时":
					curCellInfo.HandoverNotInTime++;
					break;
				case "切换不合理":
					curCellInfo.HandoverProblem++;
					break;
				case "频率干扰或故障":
					curCellInfo.Interfere_C_I++;
					break;
				case "质量毛刺":
					curCellInfo.QualBurr++;
					break;
				case "其它":
					curCellInfo.Other++;
					break;
				default:
					break;
			}

			string cellSubkey = cellSub.Name + cellSub.LAC + cellSub.CI;
			curCellInfo.TpCountAll = cellTestpointAllCountDic[cellSubkey];
			curCellInfo.WeakQualTpCount = curCellInfo.Indoor + curCellInfo.CoverLap + curCellInfo.WeakCover + curCellInfo.ReselectProblem + curCellInfo.NoMainCell + curCellInfo.BackCover
				+ curCellInfo.HandoverNotInTime + curCellInfo.HandoverProblem + curCellInfo.Interfere_C_I + curCellInfo.QualBurr + curCellInfo.Other;
			curCellInfo.RxQualSub0_4Percent = Math.Round((double)(cellTestpointAllCountDic[cellSubkey] - curCellInfo.WeakQualTpCount) * 100 / (double)cellTestpointAllCountDic[cellSubkey], 2) + "%";

			#endregion

#if Guangdong //广东专用网络评估系数
			DIYSQLQueryNetworkModulus.modulusParam modulusParam = modulusParamList.Find(
				delegate(DIYSQLQueryNetworkModulus.modulusParam mp)
				{
					return (mp.lac == curCellInfo.Lac && mp.ci == curCellInfo.Ci);
				}
			);
			if (modulusParam != null)
			{
				curCellInfo.InterferenceFactor = modulusParam.interferenceFactor;
				curCellInfo.NetworkStructureIndex = modulusParam.networkStructureIndex;
				curCellInfo.RedundantCoverageIndex = modulusParam.redundantCoverageIndex;
				curCellInfo.MutilCovIndex = modulusParam.mutilCovIndex;
				curCellInfo.InterferenceSourcesFactor = modulusParam.interferenceSourcesFactor;
				curCellInfo.OverlapCovIndex = modulusParam.overlapCovIndex;
			}
#endif

		}

		/// <summary>
		/// 各类采样点累计处理
		/// </summary>
		private void testPointDeal(string strReason, TestPoint tp, string strRegionName, ref Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, ref WeakQualCellInfo.reasonTestpoint curReasonTp)
		{
			if (strReason == "质量毛刺")
			{
				testPointDealQualBurr(tp, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp);
			}
			else
			{
				Add(strRegionName, curReasonClassifyInfoDic, regionTestpointWeakRxQualCountDic[strRegionName], strReason, tp);
				if (regionCount > 1)
				{
					Add(strReason, testpointWeakRxQualCount, tp);
				}
				curReasonTp = new WeakQualCellInfo.reasonTestpoint(strReason, tp);
			}

			if (strReason != "质量毛刺" && strReason != "室分泄漏")
			{
				FileTimeKey ftk = new FileTimeKey();
				ftk.strreason = strReason;
				ftk.ifileid = tp.FileID;
				ftk.ici = (int)tp["CI"];
				ftk.dTime = tp.DateTime;
				ftk.strRegionName = strRegionName;
				fileTimeList.Add(ftk);
			}
		}

		/// <summary>
		/// 质差采样点（汇总）
		/// </summary>
		private void Add(string key, int testpointWeakRxQualCount, TestPoint testpoint)
		{
			if (gatherReasonClassifyInfoDic.ContainsKey(key))
            {
                gatherReasonClassifyInfoDic[key].TpCount++;
                gatherReasonClassifyInfoDic[key].reasonTestpointList.Add(new WeakQualCellInfo.reasonTestpoint(key, testpoint));
                int? rxQual = (int?)(byte?)testpoint["RxQualSub"];
                if (rxQual != null)
                {
                    gatherReasonClassifyInfoDic[key].AddQual((int)rxQual);
                }
                
                addGatherReasonClassifyInfoDic(testpointWeakRxQualCount, "室分泄漏");
                addGatherReasonClassifyInfoDic(testpointWeakRxQualCount, "占用不合理");
                addGatherReasonClassifyInfoDic(testpointWeakRxQualCount, "弱覆盖");
                addGatherReasonClassifyInfoDic(testpointWeakRxQualCount, "重选问题");
                addGatherReasonClassifyInfoDic(testpointWeakRxQualCount, "覆盖杂乱");
                addGatherReasonClassifyInfoDic(testpointWeakRxQualCount, "背向覆盖");
                addGatherReasonClassifyInfoDic(testpointWeakRxQualCount, "切换不及时");
                addGatherReasonClassifyInfoDic(testpointWeakRxQualCount, "切换不合理");
                addGatherReasonClassifyInfoDic(testpointWeakRxQualCount, "频率干扰或故障");
                addGatherReasonClassifyInfoDic(testpointWeakRxQualCount, "质量毛刺");
                addGatherReasonClassifyInfoDic(testpointWeakRxQualCount, "其它");
            }
        }

        private void addGatherReasonClassifyInfoDic(int testpointWeakRxQualCount, string reasonName)
        {
            if (gatherReasonClassifyInfoDic.ContainsKey(reasonName))
            {
                gatherReasonClassifyInfoDic[reasonName].tpTotalCount = testpointWeakRxQualCount;
                gatherReasonClassifyInfoDic[reasonName].TpAllCount = testpointCount;
            }
        }

        /// <summary>
        /// 质差采样点
        /// </summary>
        private void Add(string regionName, Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, int testpointWeakRxQualCount, string key, TestPoint testpoint)
        {
            if (curReasonClassifyInfoDic.ContainsKey(key))
            {
                curReasonClassifyInfoDic[key].TpCount++;
                curReasonClassifyInfoDic[key].reasonTestpointList.Add(new WeakQualCellInfo.reasonTestpoint(key, testpoint));
                int? rxQual = (int?)(byte?)testpoint["RxQualSub"];
                if (rxQual != null)
                {
                    curReasonClassifyInfoDic[key].AddQual((int)rxQual);
                }
            }
            else
            {
                ReasonClassifyInfo info = new ReasonClassifyInfo();
                info.TpCount++;
                info.reasonTestpointList.Add(new WeakQualCellInfo.reasonTestpoint(key, testpoint));
                int? rxQual = (int?)(byte?)testpoint["RxQualSub"];
                if (rxQual != null)
                {
                    info.AddQual((int)rxQual);
                }
                curReasonClassifyInfoDic[key] = info;
            }
            addCurReasonClassifyInfoDic(regionName, "室分泄漏", curReasonClassifyInfoDic, testpointWeakRxQualCount);
            addCurReasonClassifyInfoDic(regionName, "占用不合理", curReasonClassifyInfoDic, testpointWeakRxQualCount);
            addCurReasonClassifyInfoDic(regionName, "弱覆盖", curReasonClassifyInfoDic, testpointWeakRxQualCount);
            addCurReasonClassifyInfoDic(regionName, "覆盖杂乱", curReasonClassifyInfoDic, testpointWeakRxQualCount);
            addCurReasonClassifyInfoDic(regionName, "背向覆盖", curReasonClassifyInfoDic, testpointWeakRxQualCount);
            addCurReasonClassifyInfoDic(regionName, "重选问题", curReasonClassifyInfoDic, testpointWeakRxQualCount);
            addCurReasonClassifyInfoDic(regionName, "切换不及时", curReasonClassifyInfoDic, testpointWeakRxQualCount);
            addCurReasonClassifyInfoDic(regionName, "切换不合理", curReasonClassifyInfoDic, testpointWeakRxQualCount);
            addCurReasonClassifyInfoDic(regionName, "频率干扰或故障", curReasonClassifyInfoDic, testpointWeakRxQualCount);
            addCurReasonClassifyInfoDic(regionName, "质量毛刺", curReasonClassifyInfoDic, testpointWeakRxQualCount);
            addCurReasonClassifyInfoDic(regionName, "其它", curReasonClassifyInfoDic, testpointWeakRxQualCount);
        }

        private void addCurReasonClassifyInfoDic(string regionName, string reasonName,
            Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, int testpointWeakRxQualCount)
        {
            if (curReasonClassifyInfoDic.ContainsKey(reasonName))
            {
                curReasonClassifyInfoDic[reasonName].tpTotalCount = testpointWeakRxQualCount;
                curReasonClassifyInfoDic[reasonName].TpAllCount = regionTestpointCountDic[regionName];
            }
        }

        /// <summary>
        /// 是否有效质量值
        /// </summary>
        private bool IsValidRxlev(TestPoint testpoint)
		{
			int? rxQual = (int?)(byte?)testpoint["RxQualSub"];
			if (rxQual != null)
			{
				if (rxQual >= 5 && rxQual <= 7)
				{
					return true;
				}
				else
					return false;
			}
			return false;
		}

		/// <summary>
		/// 是否占用不合理(过覆盖)
		/// </summary>
		private void IsCoverLap(TestPoint testpoint, string strRegionName, ref Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, ref WeakQualCellInfo.reasonTestpoint curReasonTp, ref bool dealt)
		{
			Cell mainCell = CellManager.GetInstance().GetNearestCell(testpoint.DateTime, (ushort?)(int?)testpoint["LAC"], (ushort?)(int?)testpoint["CI"], (short?)testpoint["BCCH"], (byte?)testpoint["BSIC"], testpoint.Longitude, testpoint.Latitude);
            if (mainCell != null)
            {
                double cellDistance = mainCell.GetDistance((testpoint.Longitude), (testpoint.Latitude));
                double maxDistance = coverLapDistanceTimes * CalcRadius(testpoint.Longitude, testpoint.Latitude, 3);
                int? rxLevSub = (int?)(short?)testpoint["RxLevSub"];

                if (rxLevSub != null && cellDistance > maxDistance && cellDistance > 400 && rxLevSub > coverLapThreshold)
                {
                    testPointDeal("占用不合理", testpoint, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp);
                    dealt = true;
                }
            }
		}

		/// <summary>
		/// 获得某采样点的理想覆盖半径
		/// </summary>
		/// <param name="flongitude">采样点经度</param>
		/// <param name="flatitude">采样点纬度</param>
		/// <param name="nearestCellCount">附近的小区数</param>
		public double CalcRadius(double flongitude, double flatitude, int nearestCellCount)
        {
            if (btsList.Count == 0)
            {
                btsList = CellManager.GetInstance().GetCurrentBTSs();
            }

            List<double> distList = new List<double>();
            double minLong = flongitude - 0.02;
            double maxLong = flongitude + 0.02;
            double minLat = flatitude - 0.02;
            double maxLat = flatitude + 0.02;

            foreach (BTS bts in btsList)
            {
                if (bts.Type == BTSType.Indoor)
                {
                    continue;
                }
                if (bts.Cells.Count == 0)
                {
                    continue;
                }
                if ((bts.Longitude >= minLong) && (bts.Longitude <= maxLong) && (bts.Latitude <= maxLat) && (bts.Latitude >= minLat))
                {
                    double distance = MathFuncs.GetDistance(flongitude, flatitude, bts.Longitude, bts.Latitude);
                    if ((distance < CD.MAX_COV_DISTANCE_TD) && (distance > 20))
                    {
                        distList.Add(distance);
                    }
                }
            }

            double meanDistance = getMeanDistance(nearestCellCount, distList);

            return meanDistance;
        }

        private double getMeanDistance(int nearestCellCount, List<double> distList)
        {
            double meanDistance = 0;
            if (distList.Count > 0)
            {
                distList.Sort();
                int counter = 0;
                for (int i = 0; i < distList.Count; i++)
                {
                    counter++;
                    meanDistance += distList[i];

                    if (counter >= nearestCellCount)
                    {
                        break;
                    }
                }
                meanDistance = meanDistance / counter;
            }
            else
            {
                meanDistance = 0;
            }

            return meanDistance;
        }

        /// <summary>
        /// 是否弱覆盖
        /// </summary>
        private void IsWeakCover(TestPoint testpoint, string strRegionName, ref Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, ref WeakQualCellInfo.reasonTestpoint curReasonTp, ref bool dealt)
		{
			int? rxLevSub = (int?)(short?)testpoint["RxLevSub"];
			int? nbCell1_rxlev = (int?)(short?)testpoint["N_RxLev", 0];
            if (rxLevSub != null && nbCell1_rxlev != null && rxLevSub < weakCovMainCellThreshold && nbCell1_rxlev < weakCovNbCell1Threshold)
            {
                testPointDeal("弱覆盖", testpoint, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp);
                dealt = true;
            }
		}

		/// <summary>
		/// 是否室分小区
		/// </summary>
		private void IsIndoor(TestPoint testpoint, string strRegionName, ref Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, ref WeakQualCellInfo.reasonTestpoint curReasonTp, ref bool dealt)
		{
			Cell mainCell = CellManager.GetInstance().GetNearestCell(testpoint.DateTime, (ushort?)(int?)testpoint["LAC"], (ushort?)(int?)testpoint["CI"], (short?)testpoint["BCCH"], (byte?)testpoint["BSIC"], testpoint.Longitude, testpoint.Latitude);
            if (mainCell != null && mainCell.Type == BTSType.Indoor)
            {
                testPointDeal("室分泄漏", testpoint, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp);
                dealt = true;
            }
		}

		/// <summary>
		/// 是否覆盖杂乱(无主导)
		/// </summary>
		private void IsNoMainCell(TestPoint testpoint, string strRegionName, ref Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, ref WeakQualCellInfo.reasonTestpoint curReasonTp, ref bool dealt)
        {
            List<int> relevList = new List<int>();

            int? rxlevSub = (int?)(short?)testpoint["RxLevSub"];
            if (rxlevSub == null || rxlevSub < -85)
            {
                return;
            }
            else
            {
                relevList.Add((int)(rxlevSub));
            }

            addNRxLev(testpoint, relevList);

            int maxRelev;
            int icount = 0;
            relevList.Sort();

            if (relevList.Count != 0)
            {
                maxRelev = relevList[relevList.Count - 1];
                icount = getResCount(relevList, maxRelev, icount);
            }
            else
            {
                return;
            }

            if (icount >= noMainCellCellCount)
            {
                testPointDeal("覆盖杂乱", testpoint, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp);
                dealt = true;
            }
        }

        private void addNRxLev(TestPoint testpoint, List<int> relevList)
        {
            for (int i = 0; i < 6; i++)
            {
                int? rxlevSubN = (int?)(short?)testpoint["N_RxLev", i];
                if (rxlevSubN != null)
                {
                    if (rxlevSubN >= -85)
                    {
                        relevList.Add((int)(rxlevSubN));
                    }
                }
                else
                {
                    break;
                }
            }
        }

        private int getResCount(List<int> relevList, int maxRelev, int icount)
        {
            icount++;
            for (int i = relevList.Count - 2; i >= 0; i--)
            {
                if ((maxRelev - relevList[i]) <= noMainCellDifferRxlev)
                {
                    icount++;
                }
                else
                {
                    break;
                }
            }

            return icount;
        }

        /// <summary>
        /// 是否为载干比差
        /// </summary>
        private void IsInterfere_C_I(TestPoint testpoint, string strRegionName, ref Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, ref WeakQualCellInfo.reasonTestpoint curReasonTp, ref bool dealt)
		{
			float? c2i = (float?)(short?)testpoint["C_I", 0];
            if (c2i != null && c2i <= c2iThreshold)
            {
                testPointDeal("频率干扰或故障", testpoint, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp);
                dealt = true;
            }
		}

		private void IsReselectProblem(TestPoint testpoint, string strRegionName, ref Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, ref WeakQualCellInfo.reasonTestpoint curReasonTp, ref bool dealt)
		{
			if (condEventsDic.ContainsKey(testpoint.FileID))
			{
				List<Event> evtList = condEventsDic[testpoint.FileID];
				foreach (Event evt in evtList)
				{
					if (evt.ID == 429 && testpoint.DateTime > evt.DateTime && testpoint.DateTime < evt.DateTime.AddSeconds(10))
                    {
                        bool hasProblem = judgeReselectProblem(evtList, evt);
                        if (hasProblem)
                        {
                            testPointDeal("重选问题", testpoint, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp);
                            dealt = true;
                        }
                    }
                }
			}
		}

        private bool judgeReselectProblem(List<Event> evtList, Event evt)
        {
            foreach (Event evt1 in evtList)
            {
                if (evt1.ID == 17 || evt1.ID == 4 || evt1.ID == 8 || evt1.ID == 10 || evt1.ID == 82
                    && evt1.DateTime > evt.DateTime && evt1.DateTime < evt.DateTime.AddSeconds(10)) //在产生需更换主服小区的事件的发生时间到后10秒这段时间内
                                                                                                    //，如没有发生小区切换成功，接通或者未接通事件，则判断为重选问题
                {
                    return false;
                }
            }
            return true;
        }

        List<BTS> btsList = new List<BTS>();
		Dictionary<int, List<Event>> condEventsDic = new Dictionary<int, List<Event>>();
		List<int> atuIds = new List<int>();
		Dictionary<int, string> idCommentDic = new Dictionary<int, string>();
		Dictionary<string, Dictionary<string, QualBurrInfo>> regionEquipInfoDicDic { get; set; } = new Dictionary<string, Dictionary<string, QualBurrInfo>>(); //网格区域下对应设备信息字典

		/// <summary>
		/// 是否为质量毛刺
		/// </summary>
		private void IsQualBurr(TestPoint testpoint, string strRegionName, ref Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, ref WeakQualCellInfo.reasonTestpoint curReasonTp, ref bool dealt)
		{
			if (condEventsDic.ContainsKey(testpoint.FileID))
			{
				List<Event> evtList = condEventsDic[testpoint.FileID];
				foreach (Event evt in evtList)
				{
					if (evt.ID != 405)
					{
						continue;
					}
					if (testpoint.DateTime >= evt.DateTime.AddSeconds(-3) && testpoint.DateTime <= evt.DateTime.AddSeconds(3))
					{
                        testPointDeal("质量毛刺", testpoint, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp);
                        dealt = true;
                    }
				}
			}
		}

		/// <summary>
		/// 详细分析质量毛刺
		/// </summary>
		private string AnalyseQualBurr(TestPoint tp)
		{
			if (atuIds.Contains(tp.FileType))
			{
				if (idCommentDic.ContainsKey(tp.DeviceType))
				{
					return idCommentDic[tp.DeviceType] + "(" + tp.FileName.Substring(0, 8) + ")";
				}
				return tp.FileName.Substring(0, 8);
			}
			else
			{
				if (idCommentDic.ContainsKey(tp.DeviceType))
				{
					return idCommentDic[tp.DeviceType];
				}
			}
			return null;
		}

		/// <summary>
		///准备查询质差原因所需的事件
		/// </summary>
		private void PrepareEvents()
		{
			DIYEventByRegion queryEvent = new DIYEventByRegion(MainModel);
			queryEvent.SetIsAddEventToDTDataManager(false);
			queryEvent.SetSaveAsFileEventsDic(true);
			queryEvent.showEventChooser = false;
			queryEvent.IsQueryAllEvents = false;
			List<int> eventIds = new List<int>();
			eventIds.Add(405);//突然高质差
			eventIds.Add(964);//切换不合理
			eventIds.Add(17);//切换成功
			eventIds.Add(84);//覆盖快衰
			eventIds.Add(425);//切换不及时
			eventIds.Add(428);//切换频繁
			eventIds.Add(429);//邻小区比主服小区高6db
			eventIds.Add(4); //主叫接通
			eventIds.Add(8);//主叫未接通
			eventIds.Add(10);//主叫未接通
			eventIds.Add(82);//主叫未接通
			condition.EventIDs = eventIds;
			queryEvent.SetQueryCondition(condition);
			queryEvent.Query();
			condEventsDic = queryEvent.fileEventsDic;
		}

		/// <summary>
		/// 准备查询质量毛刺所需的设备信息
		/// </summary>
		private void PrepareEquipmentForQualBurr()
		{
			DIYSQLQueryATUIds diySqlQueryAtuIds = new DIYSQLQueryATUIds(MainModel);
			diySqlQueryAtuIds.Query();
			atuIds = diySqlQueryAtuIds.ids;

			DIYSQLQueryStrComments diySqlQueryStrComments = new DIYSQLQueryStrComments(MainModel);
			diySqlQueryStrComments.Query();
			idCommentDic = diySqlQueryStrComments.idStrcommentDic;
		}

		/// <summary>
		/// 是否背向覆盖
		/// </summary>
		private void IsBackCover(TestPoint testpoint, string strRegionName, ref Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, ref WeakQualCellInfo.reasonTestpoint curReasonTp, ref bool dealt)
		{
			Cell mainCell = CellManager.GetInstance().GetNearestCell(testpoint.DateTime, (ushort?)(int?)testpoint["LAC"], (ushort?)(int?)testpoint["CI"], (short?)testpoint["BCCH"], (byte?)testpoint["BSIC"], testpoint.Longitude, testpoint.Latitude);

			if (mainCell == null)
			{
				return;
			}
			if (mainCell.Direction > 360)
			{
				return;
			}
			if (!isValidAngle(mainCell, testpoint.Longitude, testpoint.Latitude))
			{
                testPointDeal("背向覆盖", testpoint, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp);
                dealt = true;
            }
		}

		/// <summary>
		/// 采样点到小区的夹角是否属于正常角度
		/// </summary>
		private bool isValidAngle(Cell cell, double longitude, double latitude)
		{
			double angleDiff = 0;
			double distance = cell.GetDistance(longitude, latitude);
			if (distance > backCovDistance )
			{
				///所有角度按正北方向算起始，顺时针算夹角，正北为0度
				double angle;
				double ygap = cell.GetDistance(cell.Longitude, latitude);
				double angleV = Math.Acos(ygap / distance);
				if (longitude >= cell.Longitude && latitude >= cell.Latitude)//1象限
				{
					angle = angleV * 180 / Math.PI;
				}
				else if (longitude <= cell.Longitude && latitude >= cell.Latitude)//2象限
				{
					angle = 360 - angleV * 180 / Math.PI;
				}
				else if (longitude <= cell.Longitude && latitude <= cell.Latitude)//3象限
				{
					angle = 180 + angleV * 180 / Math.PI;
				}
				else//4象限
				{
					angle = 180 - angleV * 180 / Math.PI;
				}

				angleDiff = Math.Abs(angle - cell.Direction);
				if (angleDiff > 180)
				{
					angleDiff = 360 - angleDiff;
				}
				if (angleDiff > backCovDegree)
				{
					return false;
				}
			}
			return true;
		}

		/// <summary>
		/// 是否为切换不及时
		/// </summary>
		private void IsHandoverNotInTime(TestPoint testpoint, string strRegionName, ref Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, ref WeakQualCellInfo.reasonTestpoint curReasonTp, ref bool dealt)
		{
			if (condEventsDic.ContainsKey(testpoint.FileID))
			{
				List<Event> evtList = condEventsDic[testpoint.FileID];
				foreach (Event evt in evtList)
				{
					if (evt.ID != 425)
					{
						continue;
					}
					int iTime = int.Parse(evt["Value3"].ToString()) / 1000 + 1;//切换不及时持续时间
					if (testpoint.DateTime >= evt.DateTime.AddSeconds(0 - iTime) && testpoint.DateTime <= evt.DateTime)
					{
                        testPointDeal("切换不及时", testpoint, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp);
                        dealt = true;
                    }
				}
			}
		}

		/// <summary>
		/// 是否为切换不合理
		/// </summary>
		private void IsHandoverProblem(TestPoint testpoint, string strRegionName, ref Dictionary<string, ReasonClassifyInfo> curReasonClassifyInfoDic, ref WeakQualCellInfo.reasonTestpoint curReasonTp, ref bool dealt)
		{
			if (condEventsDic.ContainsKey(testpoint.FileID))
            {
                List<Event> evtList = condEventsDic[testpoint.FileID];
                List<Event> hoEvtList = new List<Event>();

                foreach (Event evt in evtList)
                {
                    if (evt.ID == 17)
                    {
                        hoEvtList.Add(evt);
                    }
                }

                bool hasProblem = judgeHandoverEvt(testpoint, hoEvtList);
                if (hasProblem)
                {
                    testPointDeal("切换不合理", testpoint, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp);
                    dealt = true;
                    return;
                }

                hasProblem = judgeHasProoblemEvt(testpoint, evtList);
                if (hasProblem)
                {
                    testPointDeal("切换不合理", testpoint, strRegionName, ref curReasonClassifyInfoDic, ref curReasonTp);
                    dealt = true;
                    return;
                }
            }

            IsHandoverFrequently(testpoint);  //切换频繁归类到切换不合理
		}

        private bool judgeHandoverEvt(TestPoint testpoint, List<Event> hoEvtList)
        {
            int i = 0;
            foreach (Event evt in hoEvtList)
            {
                i++;
                DateTime dCurTime = evt.DateTime;
                DateTime dNextTime;
                if (hoEvtList.Count <= i) //最后一个事件
                {
                    dNextTime = dCurTime.AddSeconds(10);
                }
                else
                {
                    DateTime tmpTime = hoEvtList[i].DateTime;//下一事件时间
                    if (tmpTime >= dCurTime.AddSeconds(10))
                    {
                        dNextTime = dCurTime.AddSeconds(10);
                    }
                    else
                    {
                        dNextTime = tmpTime;
                    }
                }

                //切换前后2秒比较
                int ibeforeHoBcch = int.Parse(evt["Value1"].ToString());
                int iafterHoBcch = int.Parse(evt["Value3"].ToString());
                if (ibeforeHoBcch >= 128 || iafterHoBcch < 128)
                {
                    int ibeforeHoRelev = int.Parse(evt["Value6"].ToString());
                    int iafterHoRelev = int.Parse(evt["Value8"].ToString());
                    if (ibeforeHoRelev > iafterHoRelev
                        && testpoint.DateTime >= dCurTime && testpoint.DateTime <= dNextTime)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private bool judgeHasProoblemEvt(TestPoint testpoint, List<Event> evtList)
        {
            foreach (Event evt in evtList)
            {
                //与第一邻区比较
                if (evt.ID == 964 && testpoint.DateTime >= evt.DateTime.AddSeconds(-handoverProblemSecond)
                    && testpoint.DateTime <= evt.DateTime.AddSeconds(handoverProblemSecond))
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 是否切换频繁
        /// </summary>
        private void IsHandoverFrequently(TestPoint testpoint)
		{
			if (condEventsDic.ContainsKey(testpoint.FileID))
			{
				List<Event> evtList = condEventsDic[testpoint.FileID];
				foreach (Event evt in evtList)
				{
					if (evt.ID != 428)
					{
						continue;
					}
					//与第一邻区比较
					if (testpoint.DateTime >= evt.DateTime.AddSeconds(-handoverFreqSecond) && testpoint.DateTime <= evt.DateTime)
					{
						//return true
					}
				}
			}
			//return false
		}

		/// <summary>
		/// 是否切换成功
		/// </summary>
		private bool IsHandoverSuccess(TestPoint testpoint)
		{
			if (condEventsDic.ContainsKey(testpoint.FileID))
			{
				List<Event> evtList = condEventsDic[testpoint.FileID];
				foreach (Event evt in evtList)
				{
					if (evt.ID != 17)
					{
						continue;
					}
					if (testpoint.DateTime >= evt.DateTime.AddSeconds(-5) && testpoint.DateTime <= evt.DateTime.AddSeconds(5))
					{
						return true;
					}
				}
			}
			return false;
		}

		/// <summary>
		/// 是否覆盖快衰
		/// </summary>
		private bool IsFastFailure(TestPoint testpoint)
		{
			if (condEventsDic.ContainsKey(testpoint.FileID))
			{
				List<Event> evtList = condEventsDic[testpoint.FileID];
				foreach (Event evt in evtList)
				{
					if (evt.ID != 84)
					{
						continue;
					}
					if (testpoint.DateTime >= evt.DateTime.AddSeconds(-5) && testpoint.DateTime <= evt.DateTime.AddSeconds(5))
					{
						return true;
					}
				}
			}
			return false;
		}

        List<DIYSQLQueryNetworkModulus.modulusParam> modulusParamList { get; set; } = new List<DIYSQLQueryNetworkModulus.modulusParam>();
#if Guangdong
		/// <summary>
		/// 预查询网络评估系数
		/// </summary>
		private void PrepareNetworkModulus()
		{
			DIYSQLQueryNetworkModulus queryNetworkModulus = new DIYSQLQueryNetworkModulus(MainModel);
			queryNetworkModulus.Query();
			modulusParamList = queryNetworkModulus.modulusParamList;
		}
#endif

        /// <summary>
        /// 采样点原因信息保存
        /// </summary>
        public class FileTimeKey
		{
			public int ifileid { get; set; }
			public DateTime dTime { get; set; }
			public int ici { get; set; }
			public string strreason { get; set; }
			public string strRegionName { get; set; }

			public FileTimeKey()
			{
				ifileid = 0;
				dTime = Convert.ToDateTime("1970-1-1 8:00:00");
				ici = 0;
				strreason = "";
				strRegionName = "";
			}
		}

		/// <summary>
		/// 小区基础信息子类
		/// </summary>
		public class CellSub
		{
			public string Name { get; set; }
			public int LAC { get; set; }
			public int CI { get; set; }
			public double Longitude { get; set; }
			public double Latitude { get; set; }

			public CellSub()
			{
				Name = "";
				LAC = 0;
				CI = 0;
				Longitude = 0;
				Latitude = 0;
			}

			public override bool Equals(object obj)
			{
				CellSub other = obj as CellSub;

				if (other == null)
					return false;

				if (!base.GetType().Equals(obj.GetType()))
					return false;

				return (this.Name.Equals(other.Name)
						&& this.LAC.Equals(other.LAC)
						&& this.CI.Equals(other.CI));
			}

			public override int GetHashCode()
			{
				return this.CI.GetHashCode();
			}
		}

		public class ReasonClassifyInfo
		{
			/// <summary>
			/// 质差类型
			/// </summary>
			public string Reason { get; set; } = "";
            /// <summary>
            /// 全部的采样点数目（正常采样点+质差采样点）
            /// </summary>
            public int TpAllCount { get; set; }
            /// <summary>
            /// 此类原因的质差采样点数目
            /// </summary>
            public int TpCount { get; set; }
            double qualSum = 0;
			/// <summary>
			/// 全部原因的质差采样点数目
			/// </summary>
			public int tpTotalCount { get; set; } = 0;
			private string avgQual = null;
			public string AvgQual //平均质量
			{
				get
				{
					if (avgQual != null)
					{
						return avgQual;
					}
					else
					{
						if (TpCount == 0)
						{
							return "-";
						}
						else
						{
							return Math.Round(qualSum / TpCount, 2).ToString();
						}
					}
				}
				set { avgQual = value; }
			}
			private string weakQualTpPct = null;
			public string WeakQualTpPct //质差采样点占比
			{
				get
				{
					if (weakQualTpPct != null)
					{
						return weakQualTpPct;
					}
					else
					{
						if (tpTotalCount == 0)
						{
							return "-";
						}
						else
						{
							return Math.Round(100 * (double)TpCount / (double)tpTotalCount, 2) + "%";
						}
					}
				}
				set { weakQualTpPct = value; }
			}

			public List<QualBurrInfo> qualBurrInfoList { get; set; }

            public void AddQual(double qual)
			{
				qualSum += qual;
			}

			public List<WeakQualCellInfo.reasonTestpoint> reasonTestpointList { get; set; } = new List<WeakQualCellInfo.reasonTestpoint>();
		}

		public class QualBurrInfo
		{
			public string RegionType { get; set; } = "";
            public string EquipmentName { get; set; } = "";
            public int IsHandover { get; set; }
            public int IsNotHandover { get; set; }
            public int Is900 { get; set; }
            public int Is1800 { get; set; }
            public int IsFastFailure { get; set; }
            public int IsOther { get; set; }
        }

		public class WeakQualCellInfo
		{
			public int SN { get; set; }
            public string CellName { get; set; }
            public string RegionType { get; set; } = "";
            public int Lac { get; set; }
            public int Ci { get; set; }
            public double Longitude { get; set; }
            public double Latitude { get; set; }
            /// <summary>
            /// 问题路段
            /// </summary>
            public string ProblemRoad { get; set; }
            /// <summary>
            /// 全部采样点数目
            /// </summary>
            public int TpCountAll { get; set; }
            /// <summary>
            /// 质差点数目
            /// </summary>
            public int WeakQualTpCount { get; set; }

            public List<reasonTestpoint> ReasonTpList { get; set; } = new List<reasonTestpoint>();
            
			/// <summary>
			///RxQualSub[0,4]占比
			///公式：100*(采样点总数-质差点数目)/采样点总数
			/// </summary>
			public string RxQualSub0_4Percent { get; set; }
            public double SumTpDistance { get; set; }

            /// <summary>
            /// 质差采样点平均距离
            /// </summary>
            public double AvgTpDistance
			{
				get { return Math.Round(SumTpDistance / WeakQualTpCount, 2); }
			}
            
			/// <summary>
			/// 室内泄漏数目
			/// </summary>
			public int Indoor { get; set; }
            /// <summary>
            /// 占用不合理数目
            /// </summary>
            public int CoverLap { get; set; }
            /// <summary>
            /// 弱覆盖数目
            /// </summary>
            public int WeakCover { get; set; }
            /// <summary>
            /// 覆盖杂乱数目
            /// </summary>
            public int NoMainCell { get; set; }
            /// <summary>
            /// 背向覆盖数目
            /// </summary>
            public int BackCover { get; set; }
            /// <summary>
            /// 重选问题
            /// </summary>
            public int ReselectProblem { get; set; }
            /// <summary>
            /// 切换不及时数目
            /// </summary>
            public int HandoverNotInTime { get; set; }
            /// <summary>
            /// 切换不合理数目
            /// </summary>
            public int HandoverProblem { get; set; }
            /// <summary>
            /// C/I干扰数目
            /// </summary>
            public int Interfere_C_I { get; set; }
            /// <summary>
            /// 半速率数目
            /// </summary>
            public int SpeechCodecHR { get; set; }
            /// <summary>
            /// 质量毛刺数目
            /// </summary>
            public int QualBurr { get; set; }
            /// <summary>
            /// 其它数目
            /// </summary>
            public int Other { get; set; }
            /// <summary>
            /// 干扰系数
            /// </summary>
            public string InterferenceFactor { get; set; }
            /// <summary>
            /// 网络结构
            /// </summary>
            public string NetworkStructureIndex { get; set; }
            /// <summary>
            /// 冗余覆盖指数
            /// </summary>
            public string RedundantCoverageIndex { get; set; }
            /// <summary>
            /// 重叠覆盖度
            /// </summary>
            public string MutilCovIndex { get; set; }
            /// <summary>
            /// 干扰源系数
            /// </summary>
            public string InterferenceSourcesFactor { get; set; }
            /// <summary>
            /// 过覆盖系数
            /// </summary>
            public string OverlapCovIndex { get; set; }

            public class reasonTestpoint
			{
				public string reason { get; set; }
				public TestPoint tp { get; set; }

				public reasonTestpoint(string reason, TestPoint tp)
				{
					this.reason = reason;
					this.tp = tp;
				}

				public Color color { get; set; }
            }
		}

		public class DIYSQLQueryATUIds : DIYSQLBase
		{
			public DIYSQLQueryATUIds(MainModel mainModel)
				: base(mainModel)
			{
			}

			public override string Name
			{
				get { return "DIYSQLQueryATUIds"; }
			}

			protected override void query()
			{
				ClientProxy clientProxy = new ClientProxy();
				try
				{
					if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
					{
						ErrorInfo = "连接服务器失败!";
						return;
					}
					queryInThread(clientProxy);
				}
				finally
				{
					clientProxy.Close();
				}

			}

			public List<int> ids { get; set; } = new List<int>();
			protected override string getSqlTextString()
			{
				string sql = "select iid from tb_cfg_static_file where strname like 'FILE_STD%'";
				return sql;
			}

			protected override E_VType[] getSqlRetTypeArr()
			{
				E_VType[] rType = new E_VType[1];
				rType[0] = E_VType.E_Int;
				return rType;
			}


			protected override void receiveRetData(ClientProxy clientProxy)
			{
				ids.Clear();
				Package package = clientProxy.Package;
				while (true)
				{
					clientProxy.Recieve();
					package.Content.PrepareGetParam();
					if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
					{
						int id = package.Content.GetParamInt();
						ids.Add(id);
					}
					else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
					{
						break;
					}
					else
					{
						log.Error("Unexpected type: " + package.Content.Type);
						break;
					}
				}
			}

		}

		public class DIYSQLQueryStrComments : DIYSQLBase
		{
			public DIYSQLQueryStrComments(MainModel mainModel)
				: base(mainModel)
			{
			}

			public override string Name
			{
				get { return "DIYSQLQueryStrComments"; }
			}

			protected override void query()
			{
				ClientProxy clientProxy = new ClientProxy();
				try
				{
					if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
					{
						ErrorInfo = "连接服务器失败!";
						return;
					}
					queryInThread(clientProxy);
				}
				finally
				{
					clientProxy.Close();
				}

			}

			public Dictionary<int, string> idStrcommentDic { get; set; } = new Dictionary<int, string>();
			protected override string getSqlTextString()
			{
				string sql = "select iid,strcomment from tb_cfg_static_device";
				return sql;
			}

			protected override E_VType[] getSqlRetTypeArr()
			{
				E_VType[] rType = new E_VType[2];
				rType[0] = E_VType.E_Int;
				rType[1] = E_VType.E_String;
				return rType;
			}


			protected override void receiveRetData(ClientProxy clientProxy)
			{
				idStrcommentDic.Clear();
				Package package = clientProxy.Package;
				while (true)
				{
					clientProxy.Recieve();
					package.Content.PrepareGetParam();
					if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
					{
						int iid = package.Content.GetParamInt();
						string cme = package.Content.GetParamString();
						idStrcommentDic.Add(iid, cme);
					}
					else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
					{
						break;
					}
					else
					{
						log.Error("Unexpected type: " + package.Content.Type);
						break;
					}
				}
			}

		}

		public class DIYSQLQueryNetworkModulus : DIYSQLBase
		{
			public DIYSQLQueryNetworkModulus(MainModel mainModel)
				: base(mainModel)
			{
			}

			public List<modulusParam> modulusParamList { get; set; } = new List<modulusParam>();
			public override string Name
			{
				get { return "DIYSQLQueryNetworkModulus"; }
			}

			protected override void query()
			{
				ClientProxy clientProxy = new ClientProxy();
				try
				{
					if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
					{
						ErrorInfo = "连接服务器失败!";
						return;
					}
					queryInThread(clientProxy);
				}
				finally
				{
					clientProxy.Close();
				}

			}

			protected override string getSqlTextString()
			{
				string sql = "select LAC,CI,干扰系数,网络结构指数,冗余覆盖指数,重叠覆盖度,干扰源系数,过覆盖系数 from DTASYSTEM.dbo.tb_cfg_cell_exponent";
				return sql;
			}

			protected override E_VType[] getSqlRetTypeArr()
			{
				E_VType[] rType = new E_VType[8];
				rType[0] = E_VType.E_Int;
				rType[1] = E_VType.E_Int;
				rType[2] = E_VType.E_String;
				rType[3] = E_VType.E_String;
				rType[4] = E_VType.E_String;
				rType[5] = E_VType.E_String;
				rType[6] = E_VType.E_String;
				rType[7] = E_VType.E_String;
				return rType;
			}


			protected override void receiveRetData(ClientProxy clientProxy)
			{
				modulusParamList.Clear();
				Package package = clientProxy.Package;
				while (true)
				{
					clientProxy.Recieve();
					package.Content.PrepareGetParam();
					if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
					{
						modulusParam modulus = new modulusParam();
						modulus.fillForm(package);
						modulusParamList.Add(modulus);
					}
					else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
					{
						break;
					}
					else
					{
						log.Error("Unexpected type: " + package.Content.Type);
						break;
					}
				}
			}

			public class modulusParam
			{
				public int lac { get; set; }
				public int ci { get; set; }
				public string interferenceFactor { get; set; }
				public string networkStructureIndex { get; set; }
				public string redundantCoverageIndex { get; set; }
				public string mutilCovIndex { get; set; }
				public string interferenceSourcesFactor { get; set; }
				public string overlapCovIndex { get; set; }

				public void fillForm(Package package)
				{
					lac = package.Content.GetParamInt();
					ci = package.Content.GetParamInt();
					interferenceFactor = package.Content.GetParamString();
					networkStructureIndex = package.Content.GetParamString();
					redundantCoverageIndex = package.Content.GetParamString();
					mutilCovIndex = package.Content.GetParamString();
					interferenceSourcesFactor = package.Content.GetParamString();
					overlapCovIndex = package.Content.GetParamString();
				}

			}
		}
	}
}