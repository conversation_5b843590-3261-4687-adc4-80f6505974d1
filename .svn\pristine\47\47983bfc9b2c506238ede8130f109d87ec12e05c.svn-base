﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CellWrongDirForm_TD : MinCloseForm
    {
        public CellWrongDirForm_TD()
            : base()
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void FillData(List<TDCellWrongDir> wrongList, bool isTwiceBatch)
        {
            gcLAC.Caption = "LAC/TAC";
            gcCI.Caption = "CI/ECI";
            gcCompetedResult.Visible = isTwiceBatch;
            
            bindData(wrongList, isTwiceBatch);

            refreshTestPoint(wrongList);
        }

        private void bindData(List<TDCellWrongDir> wrongList, bool isTwiceBatch)
        {
            List<TDCellWrongDir> lstWrongBind = new List<TDCellWrongDir>();

            foreach (TDCellWrongDir cellWrong in wrongList)
            {
                TDCellWrongDir firstBatch = cellWrong.Clone();
                firstBatch.cellWrongBatch = CellWrongBatch.First;
                lstWrongBind.Add(firstBatch);

                if (isTwiceBatch)
                {
                    TDCellWrongDir secondBatch = cellWrong.Clone();
                    secondBatch.cellWrongBatch = CellWrongBatch.Second;
                    lstWrongBind.Add(secondBatch);
                }
            }
            gridControl.DataSource = lstWrongBind;
            gridControl.RefreshDataSource();
        }

        private void refreshTestPoint(List<TDCellWrongDir> wrongList)
        {
            MainModel.DTDataManager.Clear();
            foreach (TDCellWrongDir wrongCell in wrongList)
            {
                foreach (TestPoint tp in wrongCell.resultFirstBatch.WrongPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                foreach (TestPoint tp in wrongCell.resultSecondBatch.WrongPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.FireDTDataChanged(this);
        }

        private void refreshTestPoint(TDCellWrongDir cellWrong)
        {
            MainModel.DTDataManager.Clear();

            foreach (TestPoint tp in cellWrong.resultFirstBatch.WrongPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            foreach (TestPoint tp in cellWrong.resultSecondBatch.WrongPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }

            MainModel.FireDTDataChanged(this);
        }
        /*
        public void FillData(List<WCDMACellWrongDir> wrongList)
        {
            gcLAC.Caption = "LAC";
            gcCI.Caption = "CI";
            gridControl.DataSource = wrongList;
            gridControl.RefreshDataSource();
            MainModel.DTDataManager.Clear();
            foreach (WCDMACellWrongDir wrongCell in wrongList)
            {
                foreach (TestPoint tp in wrongCell.WrongPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.FireDTDataChanged(this);
        }

        public void FillData(List<LTECellWrongDir> wrongList)
        {
            gcLAC.Caption = "TAC";
            gcCI.Caption = "ECI";
            gridControl.DataSource = wrongList;
            gridControl.RefreshDataSource();
            MainModel.DTDataManager.Clear();
            foreach (LTECellWrongDir wrongCell in wrongList)
            {
                foreach (TestPoint tp in wrongCell.WrongPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.FireDTDataChanged(this);
        }

        public void FillData(List<GSMCellWrongDir> wrongList)
        {
            gcLAC.Caption = "LAC";
            gcCI.Caption = "CI";
            gridControl.DataSource = wrongList;
            gridControl.RefreshDataSource();
            MainModel.DTDataManager.Clear();
            foreach (GSMCellWrongDir wrongCell in wrongList)
            {
                foreach (TestPoint tp in wrongCell.WrongPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.FireDTDataChanged(this);
        }
        */
        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            MainModel.SelectedTDCell = null;
            int[] hds = gridView.GetSelectedRows();
            object row = null;
            if (hds.Length > 0)
            {
                row = gridView.GetRow(hds[0]);
            }
            else
            {
                return;
            }
            TDCellWrongDir wrongCell = null;
            if (row is LTECellWrongDir)
            {
                LTECellWrongDir wrongLTECell = row as LTECellWrongDir;
                wrongCell = wrongLTECell;
                MainModel.SelectedLTECell = wrongLTECell.Cell;
            }
            else if (row is GSMCellWrongDir)
            {
                GSMCellWrongDir wrongGsmCell = row as GSMCellWrongDir;
                wrongCell = wrongGsmCell;
                MainModel.SelectedCell = wrongGsmCell.Cell;
            }
            else if (row is WCDMACellWrongDir)
            {
                WCDMACellWrongDir wrongWCell = row as WCDMACellWrongDir;
                wrongCell = wrongWCell;
                MainModel.SelectedWCell = wrongWCell.Cell;
            }
            else if (row is TDCellWrongDir)
            {
                wrongCell = row as TDCellWrongDir;
                MainModel.SelectedTDCell = wrongCell.Cell;
            }
            else
            {
                return;
            }

            refreshTestPoint(wrongCell);

            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GoToView(wrongCell.Longitude, wrongCell.Latitude);
            }
        }
    }
}
