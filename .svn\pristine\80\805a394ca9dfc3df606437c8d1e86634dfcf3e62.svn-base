﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRScanHighCoverateRoad : DIYAnalyseByFileBackgroundBase
    {
        private List<NRScanHighCoverateRoadInfo> relRoadCoverageList = null;
        private List<NRScanHighCoverateRoadInfo> absRoadCoverageList = null;
        private NRScanHighCoverateRoadCond condHighCover = new NRScanHighCoverateRoadCond();
        private NRScanHighCoverateRoadDlg setForm;

        private static NRScanHighCoverateRoad intance = null;
        protected static readonly object LockObj = new object();
        public static NRScanHighCoverateRoad GetInstance()
        {
            if (intance == null)
            {
                lock (LockObj)
                {
                    if (intance == null)
                    {
                        intance = new NRScanHighCoverateRoad();
                    }
                }
            }
            return intance;
        }

        protected NRScanHighCoverateRoad()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NRScan);
            Columns = NRTpHelper.InitNrScanParamBackground();
        }

        public override string Name
        {
            get { return "高重叠覆盖路段_NR扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 36000, 36006, this.Name);
        }

        #region 设置条件
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                initData();
                return true;
            }
            if (showFuncCondSetDlg())
            {
                initData();
                return true;
            }
            return false;
        }
        protected void initData()
        {
            relRoadCoverageList = new List<NRScanHighCoverateRoadInfo>();
            absRoadCoverageList = new List<NRScanHighCoverateRoadInfo>();
            setRoadCond();
        }
        protected bool showFuncCondSetDlg()
        {
            if (setForm == null || setForm.IsDisposed)
            {
                setForm = new NRScanHighCoverateRoadDlg();
            }
            setForm.SetCondition(condHighCover);
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                condHighCover = setForm.GetCondition();
                setRoadCond();
                return true;
            }
            return false;
        }
        #endregion

        protected PercentRoadBuilder roadBuilder_Rel;
        protected PercentRoadBuilder roadBuilder_Abs;

        public void setRoadCond()
        {
            PercentRoadCondition roadCond = new PercentRoadCondition(condHighCover.RoadMinPercent / 100, OnOneRoadComplete);
            roadCond.IsCheckDuration = false;
            roadCond.IsCheckMinLength = true;
            roadCond.MinLength = condHighCover.RoadDistance;
            roadCond.IsCheckDistanceGap = true;
            roadCond.MaxDistanceGap = condHighCover.SampleDistance;

            this.roadBuilder_Rel = new PercentRoadBuilder(roadCond);
            this.roadBuilder_Abs = new PercentRoadBuilder(roadCond);
        }


        private double curPercent = 0;
        public List<TestPoint> tps { get; set; } = new List<TestPoint>();

        protected void OnOneRoadComplete(object sender, PercentRoadItem roadItem)
        {
            curPercent = Math.Round(roadItem.ValidPercent * 100, 2);
            if (curPercent < this.condHighCover.RoadMinPercent)
            {
                return;
            }
            tps = roadItem.TestPoints;
            if (roadItem.NetType == ShowCoverage.Absolute.ToString())
            {
                addToReportInfo(tps, true);
            }
            else
            {
                addToReportInfo(tps, false);
            }
        }

        public virtual void addToReportInfo(List<TestPoint> testPointList, bool isAbs)
        {
            if (testPointList.Count == 0)
            {
                return;
            }
            double tmpLongitude = testPointList[0].Longitude;
            double tmpLatitude = testPointList[0].Latitude;

            NRScanHighCoverateRoadInfo relInfo = new NRScanHighCoverateRoadInfo();
            NRScanHighCoverateRoadInfo absInfo = new NRScanHighCoverateRoadInfo();

            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];

                double distance = MathFuncs.GetDistance(testPoint.Longitude, testPoint.Latitude, tmpLongitude, tmpLatitude);

                doWithDTData(ref relInfo, ref absInfo, testPoint, distance, isAbs);

                tmpLongitude = testPointList[i].Longitude;
                tmpLatitude = testPointList[i].Latitude;
            }

            if (isAbs)
            {
                saveResult(absRoadCoverageList, absInfo);
            }
            else
            {
                saveResult(relRoadCoverageList, relInfo);
            }
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    tps = new List<TestPoint>();

                    foreach (TestPoint tp in fileDataManager.TestPoints)
                    {
                        if (condHighCover.IsRelativeCheck)
                        {
                            roadBuilder_Rel.AddPoint(tp, isValidTestPoint(tp, false)
                                , ShowCoverage.Relative.ToString());
                        }
                        if (condHighCover.IsAbsCheck)
                        {
                            roadBuilder_Abs.AddPoint(tp, isValidTestPoint(tp, true)
                                , ShowCoverage.Absolute.ToString());
                        }
                    }
                    this.roadBuilder_Rel.StopRoading();
                    this.roadBuilder_Abs.StopRoading();
                }
            }
            catch (Exception ee)
            {
                showException(ee);
            }
        }

        protected bool isValidTestPoint(TestPoint tp, bool isAbs)
        {
            bool ret = false;
            try
            {
                bool ignore = isIgnoreTestPoint(tp, isAbs);
                if (!ignore)
                {
                    ret = condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
                }
            }
            catch
            {
                //continue
            }
            return ret;
        }

        public bool isIgnoreTestPoint(TestPoint testPoint, bool isAbs)
        {
            float? maxRsrp = null;
            float? rsrp = null;
            List<NRScanHighCoverateRoadCellInfo> relCellList = new List<NRScanHighCoverateRoadCellInfo>();
            List<NRScanHighCoverateRoadCellInfo> absCellList = new List<NRScanHighCoverateRoadCellInfo>();

            Dictionary<int, int> groupDic = NRTpHelper.NrScanTpManager.GetCellMaxBeam(testPoint);
            foreach (var index in groupDic.Values)
            {
                bool isValidMaxRsrp = setRsrp(testPoint, ref rsrp, ref maxRsrp, index);
                if (!isValidMaxRsrp)
                {
                    return true;
                }
                var cellInfo = new NRScanHighCoverateRoadCellInfo(testPoint, index);
                bool isValid = addCellList(isAbs, rsrp, maxRsrp, relCellList, absCellList, cellInfo);
                if (!isValid)
                {
                    break;
                }
            }

            if (!isAbs && relCellList.Count < condHighCover.RelCoverate)  //不符合相对覆盖带要求
            {
                return true;
            }
            if (isAbs && absCellList.Count < condHighCover.AbsCoverate)   //不符合绝对覆盖带要求
            {
                return true;
            }
            return false;
        }

        private bool setRsrp(TestPoint testPoint, ref float? rsrp, ref float? maxRsrp, int index)
        {
            rsrp = NRTpHelper.NrScanTpManager.GetCellRsrp(testPoint, index);
            if (maxRsrp == null)
            {
                maxRsrp = rsrp;
                if (maxRsrp == null || maxRsrp <= condHighCover.RsrpMin)
                {
                    return false;
                }
            }

            return true;
        }

        private bool addCellList(bool isAbs, float? rsrp, float? maxRsrp
            , List<NRScanHighCoverateRoadCellInfo> relCellList
            , List<NRScanHighCoverateRoadCellInfo> absCellList
            , NRScanHighCoverateRoadCellInfo cellInfo)
        {
            if (rsrp == null)    //无效值
            {
                return false;
            }

            if (!isAbs && condHighCover.IsRelativeCheck && (maxRsrp - rsrp) < condHighCover.RsrpMaxDiff)//相对覆盖带
            {
                relCellList.Add(cellInfo);
            }

            if (isAbs && condHighCover.IsAbsCheck && rsrp > condHighCover.AbsValue)//绝对覆盖带
            {
                absCellList.Add(cellInfo);
            }
            return true;
        }

        private void prevDealBandType(TestPoint testPoint, out List<NRScanHighCoverateRoadCellInfo> lstHightCoverCells)
        {
            lstHightCoverCells = new List<NRScanHighCoverateRoadCellInfo>();
            NRScanHighCoverateRoadCellInfo maxCell = null;
            for (int index = 0; index < 50; index++) //扫频数据，入库时已按rxlev从大到小排序，无需再排序
            {
                NRScanHighCoverateRoadCellInfo hcCell = new NRScanHighCoverateRoadCellInfo(testPoint, index);

                if (!hcCell.IsValid) continue;

                if (maxCell == null)
                {
                    maxCell = hcCell;
                    if (maxCell.Rsrp < condHighCover.RsrpMin)
                    {
                        break;
                    }
                }
                lstHightCoverCells.Add(hcCell);
            }
        }
        private void doWithDTData(ref NRScanHighCoverateRoadInfo relInfo, ref NRScanHighCoverateRoadInfo absInfo
            , TestPoint testPoint, double distance, bool isAbs)
        {
            float? maxRsrp = null;

            List<NRScanHighCoverateRoadCellInfo> relCellList = new List<NRScanHighCoverateRoadCellInfo>();
            List<NRScanHighCoverateRoadCellInfo> absCellList = new List<NRScanHighCoverateRoadCellInfo>();

            List<NRScanHighCoverateRoadCellInfo> lstHightCoverCells;

            prevDealBandType(testPoint, out lstHightCoverCells);

            getCellList(isAbs, ref maxRsrp, relCellList, absCellList, lstHightCoverCells);

            if (maxRsrp == null)
                return;

            if (!isAbs && relCellList.Count < condHighCover.RelCoverate)  //不符合相对覆盖带要求
            {
                return;
            }
            if (isAbs && absCellList.Count < condHighCover.AbsCoverate)   //不符合绝对覆盖带要求
            {
                return;
            }

            if (isAbs)
            {
                absInfo.Distance += distance;
                absInfo.Rsrp.Add((float)maxRsrp);
                NRScanHighCoverateRoadPointInfo absPointInfo = new NRScanHighCoverateRoadPointInfo(absInfo.SampleList.Count + 1, testPoint, absCellList);
                absInfo.SampleList.Add(absPointInfo);
            }
            else
            {
                relInfo.Distance += distance;
                relInfo.Rsrp.Add((float)maxRsrp);
                NRScanHighCoverateRoadPointInfo relPointInfo = new NRScanHighCoverateRoadPointInfo(relInfo.SampleList.Count + 1, testPoint, relCellList);
                relInfo.SampleList.Add(relPointInfo);
            }
        }

        private void getCellList(bool isAbs, ref float? maxRsrp, List<NRScanHighCoverateRoadCellInfo> relCellList, List<NRScanHighCoverateRoadCellInfo> absCellList, List<NRScanHighCoverateRoadCellInfo> lstHightCoverCells)
        {
            foreach (NRScanHighCoverateRoadCellInfo cellInfo in lstHightCoverCells)
            {
                if (maxRsrp == null)
                {
                    maxRsrp = cellInfo.Rsrp;
                }

                addCellList(isAbs, maxRsrp, relCellList, absCellList, cellInfo);
            }
        }

        private void addCellList(bool isAbs, float? maxRsrp, List<NRScanHighCoverateRoadCellInfo> relCellList, List<NRScanHighCoverateRoadCellInfo> absCellList, NRScanHighCoverateRoadCellInfo cellInfo)
        {
            if (!isAbs && ((maxRsrp - cellInfo.Rsrp) < condHighCover.RsrpMaxDiff)) //超出覆盖带
            {
                relCellList.Add(cellInfo);
                cellInfo.SN = relCellList.Count;
            }

            if (isAbs && cellInfo.Rsrp > condHighCover.AbsValue)
            {
                absCellList.Add(cellInfo);
                cellInfo.SN = absCellList.Count;
            }
        }

        private void saveResult(List<NRScanHighCoverateRoadInfo> roadCoverageList
            , NRScanHighCoverateRoadInfo info)
        {
            if (info.Distance >= condHighCover.RoadDistance
                && info.SampleList.Count > 1)  //超过距离门限
            {
                info.Percent = curPercent;
                info.SN = roadCoverageList.Count + 1;
                roadCoverageList.Add(info);
            }
        }

        protected override void getResultsAfterQuery()
        {
            if (condHighCover.IsRelativeCheck)
            {
                foreach (var info in relRoadCoverageList)
                {
                    info.Calculate();
                    foreach (var pointInfo in info.SampleList)
                    {
                        MainModel.DTDataManager.Add(pointInfo.Tp);
                    }
                }
            }

            if (condHighCover.IsAbsCheck)
            {
                foreach (var info in absRoadCoverageList)
                {
                    info.Calculate();
                    foreach (var pointInfo in info.SampleList)
                    {
                        MainModel.DTDataManager.Add(pointInfo.Tp);
                    }
                }
            }
            MainModel.FireSetDefaultMapSerialTheme(NRTpHelper.NrScanTpManager.RsrpFullThemeName);
        }

        protected override void fireShowForm()
        {
            NRScanHighCoverateRoadForm form = MainModel.CreateResultForm(typeof(NRScanHighCoverateRoadForm)) as NRScanHighCoverateRoadForm;
            form.FillData(new List<NRScanHighCoverateRoadInfo>(relRoadCoverageList), 
                            new List<NRScanHighCoverateRoadInfo>(absRoadCoverageList));
            form.Owner = MainModel.MainForm;
            form.Visible = true;
            form.BringToFront();

            relRoadCoverageList = null;
            absRoadCoverageList = null;
        }
    }
}
