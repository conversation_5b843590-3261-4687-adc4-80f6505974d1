﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.NOP
{
    public partial class RepeatSummaryResultForm : MinCloseForm
    {
        public RepeatSummaryResultForm(MainModel mModel) : base(mModel)
        {
            InitializeComponent();
            this.miExportExcel.Click += MiExportExcel_Click;
            this.miViewDetail.Click += MiViewDetail_Click;
        }

        public void FillData(object result)
        {
            this.gridControl1.DataSource = result;
            this.gridControl1.RefreshDataSource();
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }

        private void MiViewDetail_Click(object sender, EventArgs e)
        {
            // DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            DevExpress.XtraGrid.Views.Grid.GridView gv = gridView1;
            RepeatSummaryTask sTask = gv.GetRow(gv.GetSelectedRows()[0]) as RepeatSummaryTask;
            if (sTask == null)
            {
                return;
            }

            RepeatDetailResultForm resultForm = MainModel.GetObjectFromBlackboard(
                typeof(RepeatDetailResultForm).FullName) as RepeatDetailResultForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new RepeatDetailResultForm(MainModel);
            }
            resultForm.FillData(sTask.HistoryTasks);
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }
            resultForm.BringToFront();
        }
    }
}
