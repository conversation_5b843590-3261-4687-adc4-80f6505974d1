﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.CheckCellOccupation;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTNRCheckCellOccupation
{
    class NRCellTpInfo
    {
        public Dictionary<int, NRCellData> dicCellData { get; set; }
        public List<NRCheckCellOccupationResult> listResult { get; set; }

        public NRCellTpInfo()
        {
            dicCellData = new Dictionary<int, NRCellData>();
        }

        public void AddTestPoint(TestPoint tp)
        {
            NRCell cell = tp.GetMainCell_NR();
            if (cell == null)
            {
                return;
            }

            int id = cell.ID;
            NRCellData cellData = null;
            if (!this.dicCellData.TryGetValue(id, out cellData))
            {
                cellData = new NRCellData();
                this.dicCellData[id] = cellData;
            }
            cellData.AddTestPoint(tp);
        }

        public void GetResult(object o)
        {
            NRCheckCellOccupationCond condition = o as NRCheckCellOccupationCond;
            int sn = 1;
            this.listResult = new List<NRCheckCellOccupationResult>();
            NRCheckCellOccupationResult result = null;
            List<NRBTS> btsList = getCurrentNRBTSs();
            if (btsList == null)
            {
                btsList = new List<NRBTS>();
            }

            foreach (NRCellData cellData in this.dicCellData.Values)
            {
                if (!cellData.Calculator(condition, btsList))
                {
                    continue;
                }
                result = new NRCheckCellOccupationResult();
                listResult.Add(result);

                result.cellData = cellData;
                NRCell cell = cellData.mainCell;

                result.SN = sn++;
                result.CellName = cell.Name;
                result.NodeBID = cell.BelongBTS.BTSID;
                result.EARFCN = cell.SSBARFCN;
                result.PCI = cell.PCI;
                result.TAC = cell.TAC;
                result.NCI = cell.NCI;
                result.ArgRSRP = cellData.argRSRP;
                result.ArgSINR = cellData.argSINR;
                result.TestPointSum = cellData.listTp.Count;
                result.ArgLon = Math.Round(cellData.argLon, 5);
                result.ArgLat = Math.Round(cellData.argLat, 5);
                result.DistanceMainCell = cellData.distanceArgLonlat2Cell;
                NRBTS bts = cellData.nearestBTS;
                if (bts != null)
                {
                    result.NearstestBTS_Name = bts.Name;
                    result.NearstestBTS_NodeBID = bts.BTSID;
                    result.NearstestBTS_Distance = cellData.distance2NearestBTS;
                    result.IsOccupationUnusual = cellData.isOccupationUnusual;
                    result.DistanceDifference = Math.Round(cellData.distance2NearestBTS - cellData.distanceArgLonlat2Cell, 2);
                    result.IsOverCover = cellData.isOverCover;
                }
                else
                {
                    result.NearstestBTS_Name = "-";
                    result.NearstestBTS_NodeBID = 0;
                    result.NearstestBTS_Distance = 0;
                    result.IsOccupationUnusual = "-";
                    result.DistanceDifference = 0;
                    result.IsOverCover = "-";
                }
            }
        }

        private List<NRBTS> getCurrentNRBTSs()
        {
            MainModel mainmodel = MainModel.GetInstance();
            List<NRBTS> validBts = CellManager.GetInstance().GetCurrentNRBTSs();
            if (mainmodel.SearchGeometrys != null && mainmodel.SearchGeometrys.IsSelectRegion())
            {
                DbRect selectRect = mainmodel.SearchGeometrys.RegionBounds;
                DbRect validRect = new DbRect(selectRect.x1 - 0.1, selectRect.y1 - 0.1, selectRect.x2 + 0.1, selectRect.y2 + 0.1);

                for (int i = 0; i < validBts.Count; i++)
                {
                    ISite site = validBts[i];
                    if (!validRect.IsPointInThisRect(site.Longitude, site.Latitude))
                    {
                        validBts.RemoveAt(i);
                        i--;
                    }
                }
            }
            return validBts;
        }
    }

    public class NRCellData : CellDataBase
    {
        public NRCell mainCell { get; set; }
        public List<TestPoint> listTp { get; set; }
        public List<TestPoint> listValidTp { get; set; }
        public double argRSRP { get; set; } = 0;
        public double argSINR { get; set; } = 0;
        public double distanceArgLonlat2Cell { get; set; } = 0;//米
        public NRBTS nearestBTS { get; set; }
        public double distance2NearestBTS { get; set; } = 0;//米
        public string isOccupationUnusual { get; set; }
        public string isOverCover { get; set; }

        public void AddTestPoint(TestPoint tp)
        {
            if (this.listTp == null)
            {
                this.listTp = new List<TestPoint>();
                this.mainCell = tp.GetMainCell_NR();
                Cell = mainCell;
            }
            this.listTp.Add(tp);
        }

        /// <summary>
        /// 根据原始数据计算其他所有结果
        /// </summary>
        public bool Calculator(NRCheckCellOccupationCond condition, List<NRBTS> btsList)
        {
            //计算平均经纬度、平均RSRP、平均SINR
            float? f_rsrp = null;
            float? f_sinr = null;
            double lon = 0;
            double lat = 0;
            double rsrp = 0;
            double sinr = 0;
            int counter = 0;
            this.listValidTp = new List<TestPoint>();
            foreach (TestPoint tp in this.listTp)
            {
                f_rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
                f_sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);
                if (f_rsrp == null || f_sinr == null) continue;
                float data = (float)f_rsrp;
                if (data <= condition.rsrp) continue;
                counter++;
                lon += tp.Longitude;
                lat += tp.Latitude;
                rsrp += data;
                sinr += (float)f_sinr;
                this.listValidTp.Add(tp);
            }
            if (counter == 0)
            {
                return false;
            }
            this.argLon = lon / counter;
            this.argLat = lat / counter;
            this.argRSRP = Math.Round(rsrp / counter, 2);
            this.argSINR = Math.Round(sinr / counter, 2);
            //平均经纬度到主服小区的距离
            this.distanceArgLonlat2Cell = MathFuncs.GetDistance(this.argLon, this.argLat, this.mainCell.Longitude, this.mainCell.Latitude);
            this.distanceArgLonlat2Cell = Math.Round(this.distanceArgLonlat2Cell, 2);
            //计算N米内最近基站
            this.distance2NearestBTS = double.MaxValue;
            setNearestBTS(btsList);
            this.distance2NearestBTS = Math.Round(this.distance2NearestBTS, 2);
            if (this.distance2NearestBTS > condition.radius)
            {
                this.distance2NearestBTS = double.MaxValue;
                this.nearestBTS = null;
                return true;
            }
            //是否占用异常
            judgeIsOccupationUnusual();
            //判断是否过覆盖
            return judgeIsOverCover(condition);
        }

        private void setNearestBTS(List<NRBTS> listBTS)
        {
            foreach (NRBTS bts in listBTS)
            {
                if (bts.BTSID == this.mainCell.BelongBTS.BTSID)
                {
                    //不包括主服小区所属基站
                    continue;
                }
                if (bts.Type == NRBTSType.Indoor)
                {
                    //不包括室分小区
                    continue;
                }
                double d = MathFuncs.GetDistance(this.argLon, this.argLat, bts.Longitude, bts.Latitude);
                if (d < this.distance2NearestBTS)
                {
                    this.distance2NearestBTS = d;
                    this.nearestBTS = bts;
                    BTS = bts;
                }
            }
        }

        private bool judgeIsOverCover(NRCheckCellOccupationCond condition)
        {
            if (this.isOccupationUnusual.Equals("异常"))
            {
                if (this.checkOverCover(condition) == 1)
                {
                    this.isOverCover = "是";
                }
                else if (this.checkOverCover(condition) == 0)
                {
                    this.isOverCover = "否";
                }
                else
                {
                    return false;
                }
            }
            return true;
        }

        private void judgeIsOccupationUnusual()
        {
            if (this.distanceArgLonlat2Cell < this.distance2NearestBTS)
            {
                this.isOccupationUnusual = "正常";
            }
            else if (this.distanceArgLonlat2Cell == this.distance2NearestBTS)
            {
                this.isOccupationUnusual = "";
            }
            else if (this.distanceArgLonlat2Cell > this.distance2NearestBTS)
            {
                this.isOccupationUnusual = "异常";
            }
        }

        private int checkOverCover(NRCheckCellOccupationCond condition)
        {
            if (this.mainCell.Antennas == null || this.mainCell.Antennas.Count == 0)//避免工参不对Antennas为空时CalculateRadius()中报错
            {
                return -1;
            }
            double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(this.mainCell, condition.btsCount);
            foreach (TestPoint tp in this.listTp)
            {
                float? pccpchRSCP = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
                if (pccpchRSCP == null)
                {
                    continue;
                }
                if (pccpchRSCP < condition.rxlev)
                {
                    continue;
                }
                if (this.distanceArgLonlat2Cell > (radiusOfCell * condition.radiusFactor))
                {
                    return 1;
                }
            }
            return 0;
        }
    }

    public class NRCheckCellOccupationCond
    {
        public float rsrp { get; set; }
        public double radius { get; set; }
        public int rxlev { get; set; }
        public int btsCount { get; set; }
        public double radiusFactor { get; set; }
        public NRCheckCellOccupationCond(float parRsrp, double parRadisus, int parRxlev, int parBtsCount, double parRadiusFactor)
        {
            this.rsrp = parRsrp;
            this.radius = parRadisus;
            this.rxlev = parRxlev;
            this.btsCount = parBtsCount;
            this.radiusFactor = parRadiusFactor;
        }
    }

    public class NRCheckCellOccupationResult
    {
        public int SN { set; get; }
        public string CellName { set; get; }
        public int NodeBID { set; get; }
        public int EARFCN { set; get; }
        public int PCI { set; get; }
        public int TAC { get; set; }
        public long NCI { get; set; }
        public double ArgRSRP { set; get; }
        public double ArgSINR { set; get; }
        public int TestPointSum { set; get; }
        public double ArgLon { set; get; }
        public double ArgLat { set; get; }
        public double DistanceMainCell { set; get; }
        public string NearstestBTS_Name { set; get; }
        public int NearstestBTS_NodeBID { set; get; }
        public double NearstestBTS_Distance { set; get; }
        public string IsOccupationUnusual { set; get; }
        public string IsOverCover { set; get; }
        public double DistanceDifference { set; get; }

        //显示图层时会用到的数据
        public NRCellData cellData { get; set; }
    }
}
