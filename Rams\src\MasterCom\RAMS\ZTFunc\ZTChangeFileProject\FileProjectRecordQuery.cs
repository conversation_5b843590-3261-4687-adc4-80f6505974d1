﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class FileProjectRecordQuery : QueryBase
    {
        public FileProjectRecordQuery(MainModel mModel) : base(mModel)
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "文件项目类型修改记录"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18038, this.Name);
        }
        protected override bool isValidCondition()
        {
            if (setForm == null || setForm.IsDisposed)
            {
                setForm = new FileProjectRecordSetForm();
            }
            return setForm.ShowDialog() == System.Windows.Forms.DialogResult.OK;
        }

        protected override void query()
        {
            WaitTextBox.Show("正在查询修改记录...", DoStatInThread);
            FireShowResult();
        }

        private void DoStatInThread()
        {
            try
            {
                SqlProjectChangedRecord sqlQuery = new SqlProjectChangedRecord(MainModel, setForm.GetCondition());
                sqlQuery.Query();
                result = sqlQuery.RecordList;
                this.error = null;
            }
            catch (Exception ex)
            {
                this.error = ex;
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        private void FireShowResult()
        {
            if (error != null)
            {
                log.Error(error.Message + Environment.NewLine + error.StackTrace);
                MessageBox.Show("查询失败!" + error.Message, this.Name, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            else
            {
                FileProjectRecordResultForm resultForm = MainModel.GetObjectFromBlackboard(
                    typeof(FileProjectRecordResultForm).FullName) as FileProjectRecordResultForm;
                if (resultForm == null || resultForm.IsDisposed)
                {
                    resultForm = new FileProjectRecordResultForm(MainModel);
                }
                resultForm.FillData(result);
                if (!resultForm.Visible)
                {
                    resultForm.Show(MainModel.MainForm);
                }
            }

            error = null;
            result = null;
        }

        Exception error = null;
        object result = null;
        FileProjectRecordSetForm setForm = null;
    }

    public class FileProjectRecordItem
    {
        public string LoginUser
        {
            get;
            set;
        }

        public string Operator
        {
            get;
            set;
        }

        public string OperateTime
        {
            get;
            set;
        }

        public string Reason
        {
            get;
            set;
        }

        public string SrcProjectName
        {
            get;
            set;
        }

        public string TarProjectName
        {
            get;
            set;
        }

        public string FileName
        {
            get;
            set;
        }
    }

    public class SqlProjectChangedRecord : DIYSQLBase
    {
        public SqlProjectChangedRecord(MainModel mModel, TimePeriod tp) : base(mModel)
        {
            this.tp = tp;
            this.RecordList = new List<FileProjectRecordItem>();
        }

        public List<FileProjectRecordItem> RecordList
        {
            get;
            private set;
        }

        protected override string getSqlTextString()
        {
            string sql = string.Format(
                "select"
                + " strusername, stroperateuser, operatetime, strreason,"
                + " ioldprojectid, inewprojectid, strfilename"
                + " from tb_moveproject_file"
                + " where operatetime >= '{0}' and operatetime <= '{1}'",
                tp.BeginTime.ToString("yyyy-MM-dd HH:mm:ss"),
                tp.EndTime.ToString("yyyy-MM-dd HH:mm:ss"));
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[7];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            CategoryEnum projectEnum = (CategoryEnum)CategoryManager.GetInstance()["Project"];
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    FileProjectRecordItem record = new FileProjectRecordItem();
                    record.LoginUser = package.Content.GetParamString();
                    record.Operator = package.Content.GetParamString();
                    record.OperateTime = DateTime.Parse(package.Content.GetParamString()).ToString("yy-MM-dd HH:mm:ss");
                    record.Reason = package.Content.GetParamString();

                    int oldProID = package.Content.GetParamInt();
                    int newProID = package.Content.GetParamInt();
                    CategoryEnumItem oldProItem = projectEnum[oldProID];
                    CategoryEnumItem newProItem = projectEnum[newProID];
                    record.SrcProjectName = oldProItem == null ? oldProID.ToString() : oldProItem.Name;
                    record.TarProjectName = newProItem == null ? newProID.ToString() : newProItem.Name;

                    record.FileName = package.Content.GetParamString();
                    RecordList.Add(record);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private readonly TimePeriod tp;
    }
}
