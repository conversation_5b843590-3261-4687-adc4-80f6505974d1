﻿using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class MultiStationAcceptAna : DIYAnalyseByCellBackgroundBaseByFile
    {
        protected MultiStationAutoAcceptManager manager;

        protected static readonly object lockObj = new object();
        private static MultiStationAcceptAna instance = null;
        public static MultiStationAcceptAna GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new MultiStationAcceptAna();
                    }
                }
            }
            return instance;
        }

        protected MultiStationAcceptAna()
            : base(MainModel.GetInstance())
        {
            if (instance != null)
            {
                return;
            }
            funcVersion = 1.2;
        }
        public override string Name
        {
            get
            {
                return "新疆单站总体验收";
            }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 22000, 22088, "查询");
        }

        protected override void getReadyBeforeQuery()
        {
            //
        }
        protected override bool getCondition()
        {
            FilterSampleByRegion = false;
            FilterEventByRegion = false;

            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.LTE));
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.VoLTE));

            this.Columns = new List<string>();
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_RSRQ");
            Columns.Add("lte_RSSI");
            Columns.Add("lte_SCell_LAC");
            Columns.Add("lte_SCell_CI");

            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_PDCP_UL_Mb");
            Columns.Add("lte_PDCP_DL_Mb");
            Columns.Add("lte_RSRP_Rx0");
            Columns.Add("lte_RSRP_Rx1");
            Columns.Add("lte_NCell_EARFCN");
            Columns.Add("lte_NCell_PCI");
            Columns.Add("lte_NCell_RSRP");
            return true;
        }

        protected override void doStatWithQuery()
        {
            if (curAnaFileInfo == null || MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }
            manager = new MultiStationAutoAcceptManager();
            manager.AnalyzeFile(curAnaFileInfo, MainModel.DTDataManager.FileDataManagers[0]);
            MainModel.DTDataManager.Clear();
        }
        #region Background
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.单站验收; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
            }
        }
        public override PropertiesControl Properties
        {
            get
            {
                return new CommonNoCondProperties(this);
            }
        }

        protected override void getFilesForAnalyse()
        {
            reportBackgroundInfo("开始读取所有站点的未分析文件...");
            BackgroundFuncQueryManager.GetInstance().GetFile_Cell(GetSubFuncID(), ServiceTypeString, ((int)carrierID).ToString());
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> resultList = new List<BackgroundResult>();
            int funcId = GetSubFuncID();
            if (manager.AcceptFileInfo != null && manager.AcceptFileInfo.AcceptKpiDic.Count > 0)
            {
                BackgroundResult result = manager.AcceptFileInfo.ConvertToBackgroundResult(funcId, BackgroundFuncConfigManager.GetInstance().ProjectType);
                resultList.Add(result);
                manager.AcceptFileInfo = null;

                //未匹配到目标小区或未获取到指标信息的文件信息暂不保留
                //（有可能是未更新工参信息导致的,或者DT上传下载文件是截取覆盖图用的，每次出报告都要重新查询回放）
                BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(funcId, curAnaFileInfo, resultList);
            }
        }
        protected override void initBackgroundImageDesc()
        {
            if (BackgroundFuncConfigManager.GetInstance().AutoExportResult)
            {
                this.BackgroundNPOIRowResultDic.Clear();

                Dictionary<int, InDoorBtsAcceptInfo> inDoorBtsAcceptInfoDic = new Dictionary<int, InDoorBtsAcceptInfo>();
                MultiInStaionAcceptResultQuery.AddBackgroundInfoToResults(BackgroundResultList, ref inDoorBtsAcceptInfoDic);
                this.BackgroundNPOIRowResultDic["室分站"] = MultiInStaionAcceptResultForm.GetNPOIRows(
                    new List<InDoorBtsAcceptInfo>(inDoorBtsAcceptInfoDic.Values));

                Dictionary<int, OutDoorBtsAcceptInfo> outDoorBtsAcceptInfoDic = new Dictionary<int, OutDoorBtsAcceptInfo>();
                MultiOutStaionAcceptResultQuery.AddBackgroundInfoToResults(BackgroundResultList, ref outDoorBtsAcceptInfoDic);
                this.BackgroundNPOIRowResultDic["宏站"] = MultiOutStaionAcceptResultForm.GetNPOIRows(
                    new List<OutDoorBtsAcceptInfo>(outDoorBtsAcceptInfoDic.Values));
            }
        }
        #endregion
    }
}
