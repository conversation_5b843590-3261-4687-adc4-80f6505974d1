﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCellKPISQLQuery : ZTRegionKPISQLQuery
    {
        public ZTCellKPISQLQuery(MainModel mainModel)
            : base(mainModel)
        { 
            
        }

        protected override string getSqlTextString()
        {
            return "select KPIName,KPIValue,KPIScore from Complain_Sys..tb_QoE_cell_KPI_score where keyName='" + GridName + "' and beginTime='" + BeginDate + "' and endTime='" + EndDate + "' and reportName='" + ReportName + "'";
        }

        public override string Name
        {
            get { return ""; }
        }
    }
}
