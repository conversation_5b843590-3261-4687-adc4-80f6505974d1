﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using DBDataViewer;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.Net
{
    public class DIYWorkStatQuery : DIYStatQuery
    {
        private List<FileStatInfo> retFileStatInfoList = null;

        public DIYWorkStatQuery(MainModel mainModel,ReporterTemplate template)
            : base(mainModel)
        {
            this.Template = template;
        }
        public List<FileStatInfo> ResultStatList
        {
            get
            {
                return retFileStatInfoList;
            }
        }
        public override string Name
        {
            get { return "工作量统计"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 1300, 1301, this.Name);
        }
        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.log;
        }
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, condition.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                retFileStatInfoList = new List<FileStatInfo>();
                queryLogKPIStat(clientProxy,retFileStatInfoList);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void queryLogKPIStat(ClientProxy clientProxy, List<FileStatInfo> retFileStatInfoList)
        {
            Dictionary<int, FileStatInfo> dicOfResult = new Dictionary<int, FileStatInfo>();
            Package package = clientProxy.Package;
            prepareStatLogKPI_ImgGrid_FileFilter(package);
            fillContentNeeded_ImgGrid(package);
            clientProxy.Send();
            recieveInfo_ImgGrid(clientProxy,dicOfResult);
            prepareStatLogKPI_Event_FileFilter(package);
            AddDIYEndOpFlag(package);
            fillContentNeeded_Event(package);
            clientProxy.Send();
            recieveInfo_Event(clientProxy, dicOfResult);
            foreach(FileStatInfo fsi in dicOfResult.Values)
            {
                retFileStatInfoList.Add(fsi);
            }
        }

        private void prepareStatLogKPI_ImgGrid_FileFilter(Package package)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_LOG_KPI;
            package.Content.PrepareAddParam();
            AddDIYPeriod(package, condition.Periods[0]);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);
            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            //
            AddDIYEndOpFlag(package);
        }

        private void fillContentNeeded_ImgGrid(Package package)
        {
            package.Content.AddParam("-1,-1,-1");
        }

        private void prepareStatLogKPI_Event_FileFilter(Package package)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
            package.Content.PrepareAddParam();
            AddDIYPeriod(package, condition.Periods[0]);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);
            AddDIYStatStatus(package);
            //AddDIYAreaTypeAndID(package, condition.Areas);
            //
            AddDIYEndOpFlag(package);
        }
        protected override void prepareStatPackage_Event_EventFilter(Package package, TimePeriod period)
        {
            AddDIYEndOpFlag(package);
        }
        private void recieveInfo_ImgGrid(ClientProxy clientProxy, Dictionary<int, FileStatInfo> dicOfResult)
        {
            DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                PartialData data = null;
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader header = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    headerManager.AddDTDataHeader(header);
                }
                else if (package.Content.Type == ResponseType.COLUMN_DEFINE)
                {
                    curImgColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurImgColumnDef(idpairs, curImgColumnDef);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else if (package.Content.Type == ResponseType.RESTYPE_SEARCHERROR)
                {
                    break;
                }
                data = getData(package, data);
                if (data != null)
                {
                    dealKPI(clientProxy, dicOfResult, headerManager, curImgColumnDef, package, data);
                }
                #endregion
                setProgressPercent(ref index, ref progress);
            }
        }

        private PartialData getData(Package package, PartialData data)
        {
            if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_GSM
                || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_GPRS)
            {
                data = new DataGSM_NewImg();
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_GSM)
            {
                data = new DataScan_GSM();
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_AMR
                || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_PS
                || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_VP)
            {
                data = new DataTDSCDMA_NewImg();
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_TD)
            {
                data = new DataScan_TD();
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_AMR
                || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_PS
                || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_VP
                || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_PSHS)
            {
                data = new DataWCDMA_AMR();
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_CDMA_V
                || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_CDMA_D)
            {
                data = new DataCDMA_Voice();
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_CDMA2000_D)
            {
                data = new DataEVDO_Data();
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_LTE_AMR)
            {
                data = new DataLTE();
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_LTETOPN
                || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_LTE_FREQSPECTRUM)
            {
                data = new DataScan_LTE();
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_NR
                || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_NR_FREQSPECTRUM)
            {
                data = new DataScan_NR();
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_LTE_FDD_AMR)
            {
                data = new DataLTE_FDD();
            }

            return data;
        }

        private void dealKPI(ClientProxy clientProxy, Dictionary<int, FileStatInfo> dicOfResult, DTDataHeaderManager headerManager, List<StatImgDefItem> curImgColumnDef, Package package, PartialData data)
        {
            foreach (StatImgDefItem cdf in curImgColumnDef)
            {
                byte[] imgBytes = package.Content.GetParamBytes();
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    data.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
            double fileIDDouble = 0;
            if (data.wInfoDic.TryGetValue("0801", out fileIDDouble))
            {
                int fileID = (int)fileIDDouble;
                DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                if (header != null)
                {
                    FileStatInfo fsi = null;
                    if (!dicOfResult.TryGetValue(fileID, out fsi))
                    {
                        DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                        fsi = new FileStatInfo();
                        fsi.DistrictID = clientProxy.DbID;
                        fsi.fileHeader = header;
                        fsi.kpiData = retResult;
                        dicOfResult[fileID] = fsi;
                    }
                    fsi.kpiData.AddStatData(data);
                }
            }
        }

        private void recieveInfo_Event(ClientProxy clientProxy, Dictionary<int, FileStatInfo> dicOfResult)
        {
            List<ColumnDefItem> curDefColumnDef = new List<ColumnDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            bool isSeparateEvtByServiceID = this.Template.IsSeparateByServiceID;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                switch (package.Content.Type)
                {
                    case ResponseType.RESTYPE_COLUMN_FILE_INFO:
                    case ResponseType.RESTYPE_DIY_FILE_INFO:
                        break;
                    case ResponseType.COLUMN_DEFINE:
                        curDefColumnDef.Clear();
                        string idpairs = package.Content.GetParamString();
                        parseToCurColumnDef(idpairs, curDefColumnDef);
                        break;
                    case ResponseType.RESTYPE_DIY_AREA_EVENT:
                        NREventHelper.ReSetIntCI(curDefColumnDef);
                        DataEvent data = DataEvent.Create(package.Content, curDefColumnDef);
                        FileStatInfo fsi = null;
                        if (dicOfResult.TryGetValue(data.filebase.fileId, out fsi))
                        {
                            fsi.kpiData.addStatData(data, isSeparateEvtByServiceID);
                        }
                        break;
                    case ResponseType.RESTYPE_DIY_AREA_EVENT_NR:
                        NREventHelper.SetLongCI(curDefColumnDef);
                        DataEvent dataNR = DataEvent.Create(package.Content, curDefColumnDef);
                        FileStatInfo fsiNR = null;
                        if (dicOfResult.TryGetValue(dataNR.filebase.fileId, out fsiNR))
                        {
                            fsiNR.kpiData.addStatData(dataNR, isSeparateEvtByServiceID);
                        }
                        break;
                    case ResponseType.END:
                        return;
                    default:
                        log.Error("Unexpected type: " + package.Content.Type);
                        return;
                }
                #endregion

                setProgressPercent(ref index, ref progress);
            }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }


        public ReporterTemplate Template { get; set; }
    }
    public class FileStatInfo
    {
        public DTDataHeader fileHeader { get; set; }
        public DataUnitAreaKPIQuery kpiData { get; set; }
        public int DistrictID { get; set; }

        public static IComparer<FileStatInfo> GetCompareByDistrictId()
        {
            if (comparerByDistrictId == null)
            {
                comparerByDistrictId = new ComparerByDistance();
            }
            return comparerByDistrictId;
        }
        public class ComparerByDistance : IComparer<FileStatInfo>
        {
            public int Compare(FileStatInfo x, FileStatInfo y)
            {
                return (x.DistrictID - y.DistrictID);
            }
        }
        private static IComparer<FileStatInfo> comparerByDistrictId;
    };

    public class LogKpiStatItem
    {
        public LogKpiStatItem()
        {
            keyColumnValues = new List<object>();
        }

        public List<object> keyColumnValues { get; set; }
        public DataUnitAreaKPIQuery ydData { get; set; }
        public DataUnitAreaKPIQuery ltData { get; set; }
        public DataUnitAreaKPIQuery dxData { get; set; }

        public DataUnitAreaKPIQuery ydDataMo { get; set; }
        public DataUnitAreaKPIQuery ltDataMo { get; set; }
        public DataUnitAreaKPIQuery dxDataMo { get; set; }

        public DataUnitAreaKPIQuery ydDataMt { get; set; }
        public DataUnitAreaKPIQuery ltDataMt { get; set; }
        public DataUnitAreaKPIQuery dxDataMt { get; set; }

        internal void addStatData(DataUnitAreaKPIQuery dataUnitAreaKPIQuery, int carrier, int momt)
        {
            if(carrier == 1)
            {
                addYDData(dataUnitAreaKPIQuery, momt);
            }
            else if (carrier == 2)
            {
                addLTData(dataUnitAreaKPIQuery, momt);
            }
            else if (carrier == 3)
            {
                addDXData(dataUnitAreaKPIQuery, momt);
            }
        }

        private void addYDData(DataUnitAreaKPIQuery dataUnitAreaKPIQuery, int momt)
        {
            if (ydData == null)
            {
                ydData = new DataUnitAreaKPIQuery();
            }
            ydData.addStatData(dataUnitAreaKPIQuery);
            if (momt == (int)MoMtFile.MoFlag)
            {
                if (ydDataMo == null)
                {
                    ydDataMo = new DataUnitAreaKPIQuery();
                }
                ydDataMo.addStatData(dataUnitAreaKPIQuery);
            }
            else if (momt == (int)MoMtFile.MtFlag)
            {
                if (ydDataMt == null)
                {
                    ydDataMt = new DataUnitAreaKPIQuery();
                }
                ydDataMt.addStatData(dataUnitAreaKPIQuery);
            }
        }

        private void addLTData(DataUnitAreaKPIQuery dataUnitAreaKPIQuery, int momt)
        {
            if (ltData == null)
            {
                ltData = new DataUnitAreaKPIQuery();
            }
            ltData.addStatData(dataUnitAreaKPIQuery);

            if (momt == (int)MoMtFile.MoFlag)
            {
                if (ltDataMo == null)
                {
                    ltDataMo = new DataUnitAreaKPIQuery();
                }
                ltDataMo.addStatData(dataUnitAreaKPIQuery);
            }
            else if (momt == (int)MoMtFile.MtFlag)
            {
                if (ltDataMt == null)
                {
                    ltDataMt = new DataUnitAreaKPIQuery();
                }
                ltDataMt.addStatData(dataUnitAreaKPIQuery);
            }
        }

        private void addDXData(DataUnitAreaKPIQuery dataUnitAreaKPIQuery, int momt)
        {
            if (dxData == null)
            {
                dxData = new DataUnitAreaKPIQuery();
            }
            dxData.addStatData(dataUnitAreaKPIQuery);
            if (momt == (int)MoMtFile.MoFlag)
            {
                if (dxDataMo == null)
                {
                    dxDataMo = new DataUnitAreaKPIQuery();
                }
                dxDataMo.addStatData(dataUnitAreaKPIQuery);
            }
            else if (momt == (int)MoMtFile.MtFlag)
            {
                if (dxDataMt == null)
                {
                    dxDataMt = new DataUnitAreaKPIQuery();
                }
                dxDataMt.addStatData(dataUnitAreaKPIQuery);
            }
        }

        internal bool IsGood()
        {
            bool isGood = true;
            return isGood;
        }
    };
}
