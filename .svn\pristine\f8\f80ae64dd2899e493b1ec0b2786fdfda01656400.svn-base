﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Model.Interface
{
    public class QueryOtherSideFile : QueryBase
    {
        public FileInfo CurFile { get; set; }
        public FileInfo OtherFile { get; set; }
        ClientProxy clientProxy = null;
        public QueryOtherSideFile(FileInfo curFile, ClientProxy connectedProxy)
            : base(MainModel.GetInstance())
        {
            this.CurFile = curFile;
            this.clientProxy = connectedProxy;
        }

        public override string Name
        {
            get { return "查询对端文件信息"; }
        }

        public override string IconName
        {
            get { return null; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            if (CurFile.EventCount == CurFile.ID)
            {
                OtherFile = CurFile;
            }
            if (clientProxy == null)
            {
                clientProxy = new ClientProxy();
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName
                   , MainModel.User.Password, CurFile.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                try
                {
                    queryInThread(clientProxy);
                }
                finally
                {
                    clientProxy.Close();
                }
            }
            else
            {
                queryInThread(clientProxy);
            }
        }

        protected void queryInThread(ClientProxy clientProxy)
        {
            try
            {
                Package package = clientProxy.Package;
                prepareInfoQueryPackage(package, CurFile.LogTable, CurFile.EventCount);
                clientProxy.Send();
                this.OtherFile = reciveInfo(clientProxy);
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
        }

        protected FileInfo reciveInfo(ClientProxy clientProxy)
        {
            FileInfo fi = null;
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_LOG_SEARCH_BYFILEID)
                {
                    fi = new FileInfo();
                    fi.Fill(package.Content);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
            }
            return fi;
        }

        protected virtual void prepareInfoQueryPackage(Package package, string LogTable, int fileID)
        {
            package.Command = Command.InfoQuery;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_LOG_SEARCH_BYFILEID;
            package.Content.PrepareAddParam();
            package.Content.AddParam(fileID);
            package.Content.AddParam(LogTable);
        }

    }
}
