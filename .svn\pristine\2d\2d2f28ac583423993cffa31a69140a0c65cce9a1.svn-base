﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class DIYMRDataSettingForm : BaseForm
    {
        public DIYMRDataSettingForm()
        {
            InitializeComponent();
            btnCancel.Click += BtnCancel_Clicked;
            btnOK.Click += BtnOK_Clicked;
            dtStartTime.Value = DateTime.Now.Date.AddDays(-1);
            dtEndTime.Value = DateTime.Now.Date;
        }

        public bool GetCondition(ref MRDataCondition condition)
        {
            condition.StartTime = dtStartTime.Value.Date;
            condition.EndTime = dtEndTime.Value.Date;
            if (condition.EndTime == condition.StartTime)
            {
                condition.EndTime = condition.EndTime.AddDays(1);
            }
            condition.UL90Cover = (double)numUL90RxLev.Value / 100;
            condition.DL90Cover = (double)numDL90RxLev.Value / 100;
            condition.CoverImbalance = (int)numRxLevDiff.Value;
            condition.ULQual = (double)numULQual.Value / 100;
            condition.DLQual = (double)numDLQual.Value / 100;
            condition.TA = (double)numTA.Value / 100;
            condition.PathLossImbalance = (double)numPL.Value / 100;
            return true;
        }

        private void BtnCancel_Clicked(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void BtnOK_Clicked(object sender, EventArgs e)
        {
            if (dtEndTime.Value.Date < dtStartTime.Value.Date)
            {
                MessageBox.Show("结束时间早于开始时间，请重选设置", "提示");
                return;
            }
            DialogResult = DialogResult.OK;
        }
    }
}
