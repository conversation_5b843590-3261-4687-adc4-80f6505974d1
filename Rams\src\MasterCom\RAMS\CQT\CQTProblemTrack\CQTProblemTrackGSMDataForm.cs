﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using Microsoft.Office.Interop.Excel;
using System.Reflection;
using DevExpress.XtraCharts;
using MasterCom.MTGis;


namespace MasterCom.RAMS.CQT
{
    public partial class CQTProblemTrackGSMDataForm : MinCloseForm
    {
        MapForm mapForm;
        public CQTProblemTrackGSMDataForm(MapForm mf)
            :
            base(mf.MainModel)
        {
            InitializeComponent();
            this.mapForm = mf;
            this.mModel = mf.MainModel;
        }
        TabProblemInfo tbItemTem = null;
        List<TabProblemInfo> tabProblemInfoList = new List<TabProblemInfo>();
        /// <summary>
        /// 绑定数据源
        /// </summary>
        /// <param name="tabProblemInfo"></param>
        public void fillData(List<TabProblemInfo> tabProblemInfo)
        {
            tabProblemInfoList.Clear();
            tabProblemInfoList.AddRange(tabProblemInfo);
            BindingSource bindingSource = new BindingSource();
            bindingSource.DataSource = tabProblemInfo;
            gridControl1.DataSource = bindingSource;
            gridControl1.RefreshDataSource();
        }
        /// <summary>
        /// 查看问题点详情
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void viewXQMenuItem_Click(object sender, EventArgs e)
        {
            viewXQ();
        }
        /// <summary>
        /// 选择第一个概况sheet的时候，隐藏详情sheet
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void xtraTabControl1_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            if (xtraTabControl1.SelectedTabPage == xtraTabPage1)
            {
                xtraTabPage2.PageVisible = false;
            }
        }

        private void gridControl1_DoubleClick(object sender, EventArgs e)
        {
            int[] rows = gridView1.GetSelectedRows();
            if (rows.Length == 0)
                return;
            object o = gridView1.GetRow(rows[0]);
            TabProblemInfo evaluate = o as TabProblemInfo;
            if (evaluate != null && evaluate.DLongitude != 0 && evaluate.DLatitude != 0)
            {
                mapForm.CurSelProblemTrackID = evaluate.ITurn;
                DbPoint center = new DbPoint(evaluate.DLongitude, evaluate.DLatitude);
                mapForm.GoToView(center.x, center.y,8000);
            }
        }

        public void viewXQ()
        {
            int[] rows = this.gridView1.GetSelectedRows();
            if (rows.Length == 0 || rows.Length > 1)
            {
                MessageBox.Show("没有选择项，或者选择了多项，请选择一项");
                return;
            }
            foreach (int i in rows)
            {
                List<TabProblemInfo> subTabProblemInfo = new List<TabProblemInfo>();

                TabProblemInfo tbItem = this.gridView1.GetRow(i) as TabProblemInfo;
                tbItemTem = tbItem;
                subTabProblemInfo.Add(tbItem);
                BindingSource bindingSource = new BindingSource();
                bindingSource.DataSource = subTabProblemInfo;
                gridControl2.DataSource = bindingSource;
                gridControl2.RefreshDataSource();

                xtraTabPage2.PageVisible = true;
                xtraTabControl1.SelectedTabPage = xtraTabPage2;

                labCity.Text = tbItem.StrCityName;
                labTestName.Text = tbItem.StrTestName;
                labTestComment.Text = tbItem.StrTestComment;
                labLo.Text = tbItem.DLongitude.ToString("0.0000000");
                laLa.Text = tbItem.DLatitude.ToString("0.0000000");
                laSate.Text = tbItem.StrSateName;
               
                lisEvent.Items.Clear();              
                foreach (ProblemTime prItem in tbItem.eventTimeInfo)
                {
                    string strItem = "-" + prItem.DSTime.ToString("yyyyMMdd");
                    if (prItem.Iresult == 0)
                        strItem += "-正常";
                    else
                        strItem += "-异常";
                    lisEvent.Items.Add(strItem);                   
                }
                if (lisEvent.Items.Count > 0)
                    lisEvent.SelectedIndex = 0;
                showChar();
            }
        }
        /// 显示饼图
        /// </summary>
        private void showChar()
        {
            Dictionary<string, int> proNumDic = new Dictionary<string, int>();
            foreach (ProblemInfoTem pr in tbItemTem.problemInfo)
            {
                addProNumDic(proNumDic, pr.StrMainProblem1);
                addProNumDic(proNumDic, pr.StrMainProblem2);
                addProNumDic(proNumDic, pr.StrMainProblem3);
                if (pr.StrSecProblem1 != "")
                {
                    string[] str = pr.StrSecProblem1.Split(',');
                    for (int i = 0; i < str.Length; i++)
                    {
                        if (!proNumDic.ContainsKey(str[i]))
                        {
                            proNumDic.Add(str[i], 1);
                        }
                        else
                        {
                            proNumDic[str[i]] += 1;
                        }
                    }
                }
                addProNumDic(proNumDic, pr.StrSecProblem2);
                addProNumDic(proNumDic, pr.StrSecProblem3);
            }

            System.Data.DataTable dt = new System.Data.DataTable();
            dt.Columns.Add("项", typeof(string));
            dt.Columns.Add("值", typeof(int));
            foreach (string strName in proNumDic.Keys)
            {
                DataRow dr = dt.NewRow();
                dr["项"] = strName;
                dr["值"] = proNumDic[strName];
                dt.Rows.Add(dr);
            }
            FillData("测试问题汇总分布", dt);
        }

        private static void addProNumDic(Dictionary<string, int> proNumDic, string data)
        {
            if (data != "")
            {
                if (!proNumDic.ContainsKey(data))
                {
                    proNumDic.Add(data, 1);
                }
                else
                {
                    proNumDic[data] += 1;
                }
            }
        }



        public delegate void FillDataCallback(string title, System.Data.DataTable table);
        public void FillData(string title, System.Data.DataTable table)
        {
            if (this.chartControl1.InvokeRequired)
            {
                this.chartControl1.Invoke(new FillDataCallback(this.FillData), title, table);
                return;
            }
            DevExpress.XtraCharts.Series serialdt = chartControl1.Series[0];
            chartControl1.Titles[0].Text = title;
            serialdt.DataSource = table;
            serialdt.ArgumentScaleType = ScaleType.Qualitative;
            serialdt.ArgumentDataMember = "项";
            serialdt.ValueScaleType = ScaleType.Numerical;
            serialdt.ValueDataMembers.AddRange(new string[] { "值" });
            serialdt.PointOptions.PointView = PointView.ArgumentAndValues;
            serialdt.PointOptions.ValueNumericOptions.Format = NumericFormat.Percent;
        }

        private void lisEvent_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (lisEvent.SelectedItem == null)
            {
                MessageBox.Show("没有选中项");
                return;
            }
            if (lisEvent.SelectedItem.ToString() == "")
            {
                return;
            }
            txtProInfo.Text = "";
            StringBuilder sb = new StringBuilder();
            if (lisEvent.SelectedItem.ToString().Contains("正常"))
            {
                sb.Append("\r\n");
                sb.Append("\r\n");
                sb.Append("\r\n");
                sb.Append("        该次测试结果：正常！");
            }
            else if (lisEvent.SelectedItem.ToString().Contains("异常"))
            {
                string strTime = lisEvent.SelectedItem.ToString().Split('-')[1];
                foreach(ProblemInfoTem pr in tbItemTem.problemInfo)
                {
                    if (pr.Dtime.ToString("yyyyMMdd") == strTime)
                    {
                        if (pr.SSecondType == "")
                            pr.SSecondType = "无";
                        sb.Append("1、问题点位置：" + pr.SPointPosition + "， 主要问题：" + pr.SMainType + "， 次要问题：" + pr.SSecondType + "；\r\n");
                        sb.Append("2、小区详情：" + pr.SReasonAna + "\r\n");
                        sb.Append("3、文件信息：" + pr.STestFilePosition + "\r\n");
                    }
                }
            }
            txtProInfo.Text = sb.ToString();
        }
        /// <summary>
        /// 数据导出Excel
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ExportExcel();
        }
        private void makeTitle(_Worksheet worksheet, int row, int col, string title, int width, bool wraptext)
        {
            Range range = worksheet.Cells[row, col] as Range;
            range.Value2 = title;
            range.ColumnWidth = width;
            range.WrapText = wraptext;
        }

        private void makeItemRow(_Worksheet worksheet, int row, int column, string str)
        {
            Range range = worksheet.Cells[row, column] as Range;
            range.Value2 = str;
        }
        private void ExportExcel()
        {
            Microsoft.Office.Interop.Excel.Application app = null;
            try
            {
                app = new Microsoft.Office.Interop.Excel.Application();
                app.Visible = false;
                app.UserControl = false;
                Workbooks workbooks = app.Workbooks;
                _Workbook workbook = workbooks.Add(XlWBATemplate.xlWBATWorksheet);
                Sheets sheets = workbook.Worksheets;
                app.Worksheets.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);

                _Worksheet worksheetxq = (_Worksheet)sheets.get_Item(1);
                if (worksheetxq == null)
                {
                    throw (new Exception("ERROR: worksheetxq == null"));
                }
                worksheetxq.Name = "问题点跟踪数据列表";
                int idxxq = 1;
                makeTitle(worksheetxq, 1, idxxq++, "序号", 4, false);
                makeTitle(worksheetxq, 1, idxxq++, "城市", 4, false);
                makeTitle(worksheetxq, 1, idxxq++, "测试地点", 10, false);
                makeTitle(worksheetxq, 1, idxxq++, "测试点场景", 14, true);
                makeTitle(worksheetxq, 1, idxxq++, "精度", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "纬度", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "状态", 8, false);
                makeTitle(worksheetxq, 1, idxxq++, "问题点位置", 17, true);
                makeTitle(worksheetxq, 1, idxxq++, "主要问题", 12, false);
                makeTitle(worksheetxq, 1, idxxq++, "次要问题", 12, false);
                makeTitle(worksheetxq, 1, idxxq++, "测试文件存放路径", 9, true);
                makeTitle(worksheetxq, 1, idxxq, "原因分析", 12, false);

                int rowAtxq = 2;
                foreach (TabProblemInfo ti in tabProblemInfoList)
                {
                    foreach (ProblemInfoTem pr in ti.problemInfo)
                    {
                        int xx = 1;
                        makeItemRow(worksheetxq, rowAtxq, xx++, (ti.ITurn + 1).ToString());
                        makeItemRow(worksheetxq, rowAtxq, xx++, ti.StrCityName);
                        makeItemRow(worksheetxq, rowAtxq, xx++, ti.StrTestName);
                        makeItemRow(worksheetxq, rowAtxq, xx++, ti.StrTestComment.ToString());
                        makeItemRow(worksheetxq, rowAtxq, xx++, ti.DLongitude.ToString("0.0000000"));
                        makeItemRow(worksheetxq, rowAtxq, xx++, ti.DLatitude.ToString("0.0000000"));
                        makeItemRow(worksheetxq, rowAtxq, xx++, ti.StrSateName);
                        makeItemRow(worksheetxq, rowAtxq, xx++, pr.SPointPosition);
                        makeItemRow(worksheetxq, rowAtxq, xx++, pr.SMainType);
                        makeItemRow(worksheetxq, rowAtxq, xx++, pr.SSecondType);
                        makeItemRow(worksheetxq, rowAtxq, xx++, pr.STestFilePosition);
                        makeItemRow(worksheetxq, rowAtxq, xx, pr.SReasonAna);
                    }

                    rowAtxq++;
                }

                app.Visible = true;
                app.UserControl = true;
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(this, "导出 Excel出错：" + ex.Message);
            }
        }
    }
}
