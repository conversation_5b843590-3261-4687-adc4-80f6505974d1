﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.CellCoverage
{
    public partial class CellCoverTemplateOptionForm : BaseForm
    {
        public CellCoverTemplateOptionForm()
        {
            InitializeComponent();
            init();
        }

        private CellCoverRptTemplate curTemplate;
        public CellCoverRptTemplate CurTemplate
        {
            get
            {
                return curTemplate;
            }
        }

        public void SetTemplate(CellCoverRptTemplate value)
        {
            cbxTemplate.SelectedItem = value;
        }

        TemplateColumn curColumn = null;

        private void init()
        {
            numRngMin.Maximum = numRngMax.Maximum = decimal.MaxValue;
            numRngMin.Minimum = numRngMax.Minimum = decimal.MinValue;

            rngColorPnl.SetScoreColumnCaption("指标值");
            cbxTemplate.SelectedIndexChanged += cbxTemplate_SelectedIndexChanged;
            gvCol.FocusedRowChanged += gvCol_FocusedRowChanged;
            FillTempate(null);
        }

        void cbxTemplate_SelectedIndexChanged(object sender, EventArgs e)
        {
            fillColumnView(cbxTemplate.SelectedItem as CellCoverRptTemplate, null);
        }

        void gvCol_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            btnUp.Enabled = btnDown.Enabled = true;
            if (gvCol.FocusedRowHandle == int.MinValue)
            {
                btnUp.Enabled = btnDown.Enabled = false;
            }
            else
            {
                if (gvCol.FocusedRowHandle == 0)
                {
                    btnUp.Enabled = false;
                }
                if (gvCol.FocusedRowHandle == curTemplate.Columns.Count - 1)
                {
                    btnDown.Enabled = false;
                }
            }

            fillColDetailView(gvCol.GetFocusedRow() as TemplateColumn);
        }

        private void fillColDetailView(TemplateColumn col)
        {
            curColumn = col;
            if (curColumn == null)
            {
                return;
            }
            this.tbxTitle.Text = curColumn.Caption;
            this.carrierID = curColumn.CarrierID;
            this.momtFlag = curColumn.MoMtFlag;
            this.kpiExpEditor.Formula = curColumn.Expression;
            this.chkRate.Checked = curColumn.IsCalcProportion;

            rBtnDynamic.CheckedChanged -= rBtnDynamic_CheckedChanged;
            rBtnDynamic.CheckedChanged += rBtnDynamic_CheckedChanged;
            this.rBtnDynamic.Checked = curColumn.IsDynamicBKColor;
            this.clrCellBkStatic.Color = curColumn.StaticColor;

            numRngMin.ValueChanged -= numRngMin_ValueChanged;
            numRngMax.ValueChanged -= numRngMax_ValueChanged;
            numRngMin.Value = (decimal)curColumn.ValueRangeMin;
            numRngMax.Value = (decimal)curColumn.ValueRangeMax;
            rngColorPnl.SetScoreColorRanges(curColumn.DynamicBKColorRanges
                , curColumn.ValueRangeMin, curColumn.ValueRangeMax);
            numRngMin.ValueChanged += numRngMin_ValueChanged;
            numRngMax.ValueChanged += numRngMax_ValueChanged;
        }

        void rBtnDynamic_CheckedChanged(object sender, EventArgs e)
        {
            numRngMin.Enabled = numRngMax.Enabled = rngColorPnl.Enabled = rBtnDynamic.Checked;
        }

        void numRngMax_ValueChanged(object sender, EventArgs e)
        {
            rngColorPnl.UpdateRange((double)numRngMin.Value, (double)numRngMax.Value);
        }

        void numRngMin_ValueChanged(object sender, EventArgs e)
        {
            rngColorPnl.UpdateRange((double)numRngMin.Value, (double)numRngMax.Value);
        }

        private byte carrierID
        {
            get
            {
                if (rbtnChinaMobile.Checked)
                {
                    return (byte)Model.CarrierType.ChinaMobile;
                }
                else if (rbtnChinaUnicom.Checked)
                {
                    return (byte)Model.CarrierType.ChinaUnicom;
                }
                else
                {
                    return (byte)Model.CarrierType.ChinaTelecom;
                }
            }
            set
            {
                CarrierType type = (CarrierType)value;
                switch (type)
                {
                    case CarrierType.ChinaMobile:
                        rbtnChinaMobile.Checked = true;
                        break;
                    case CarrierType.ChinaUnicom:
                        rbtnChinaUnicom.Checked = true;
                        break;
                    case CarrierType.ChinaTelecom:
                        rbtnChinaTelecom.Checked = true;
                        break;
                    default:
                        break;
                }
            }
        }
        private byte momtFlag
        {
            get
            {
                if (rbtnMo.Checked)
                {
                    return 1;
                }
                else if (rbtnMt.Checked)
                {
                    return 2;
                }
                else
                {
                    return 0;
                }
            }
            set
            {
                if (value == 1)
                {
                    rbtnMo.Checked = true;
                }
                else if (value == 2)
                {
                    rbtnMt.Checked = true;
                }
                else if (value == 0)
                {
                    rbtnAll.Checked = true;
                }
            }
        }

        private void btnApply_Click(object sender, EventArgs e)
        {
            if (curColumn == null)
            {
                return;
            }
            curColumn.Caption = this.tbxTitle.Text;
            curColumn.CarrierID = this.carrierID;
            curColumn.MoMtFlag = this.momtFlag;
            curColumn.Expression = this.kpiExpEditor.Formula;
            curColumn.IsCalcProportion = this.chkRate.Checked;
            curColumn.StaticColor = this.clrCellBkStatic.Color;

            curColumn.IsDynamicBKColor = this.rBtnDynamic.Checked;
            curColumn.ValueRangeMin = (float)numRngMin.Value;
            curColumn.ValueRangeMax = (float)numRngMax.Value;
            if (rngColorPnl.Ranges != null && rngColorPnl.Ranges.Count > 0)
            {
                curColumn.DynamicBKColorRanges = rngColorPnl.Ranges;
            }
        }

        private void btnNewT_Click(object sender, EventArgs e)
        {
            TextInputBox box = new TextInputBox("新建模板", "模板名称", "请输入名称");
            while (box.ShowDialog() == DialogResult.OK)
            {
                string newName = box.TextInput;
                CellCoverRptTemplate template = getExistRptTemplate(newName);
                if (template != null)
                {
                    MessageBox.Show("已存在相同名称的档案模板，请重新命名。");
                }
                else
                {
                    template = new CellCoverRptTemplate();
                    template.Name = newName;
                    CellCoverTemplateMngr.Instance.ReportTemplates.Add(template);
                    FillTempate(template);
                    break;
                }
            }
        }

        private CellCoverRptTemplate getExistRptTemplate(string newName)
        {
            return CellCoverTemplateMngr.Instance.ReportTemplates.Find(delegate (CellCoverRptTemplate x) 
            { return x.Name == newName; });
        }

        private void FillTempate(CellCoverRptTemplate template)
        {
            cbxTemplate.Items.Clear();
            foreach (CellCoverRptTemplate item in CellCoverTemplateMngr.Instance.ReportTemplates)
            {
                cbxTemplate.Items.Add(item);
            }
            if (template != null)
            {
                cbxTemplate.SelectedItem = template;
            }
            else if (cbxTemplate.Items.Count > 0)
            {
                cbxTemplate.SelectedIndex = 0;
            }
        }

        private void btnDeleteT_Click(object sender, EventArgs e)
        {
            if (curTemplate == null)
            {
                return;
            }
            if (MessageBox.Show("确定删除当前模板？", "提醒", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                CellCoverTemplateMngr.Instance.ReportTemplates.Remove(curTemplate);
                FillTempate(null);
            }
        }

        private void btnSaveT_Click(object sender, EventArgs e)
        {
            if (curTemplate == null)
            {
                return;
            }
            curTemplate.Save();
        }

        private void btnNewCol_Click(object sender, EventArgs e)
        {
            if (curTemplate == null)
            {
                return;
            }
            TextInputBox box = new TextInputBox("添加模板列", "列标题", "请输入标题");
            if (box.ShowDialog() == DialogResult.OK)
            {
                TemplateColumn col = new TemplateColumn();
                col.Caption = box.TextInput;
                curTemplate.Columns.Add(col);
                fillColumnView(curTemplate, col);
            }
        }

        private void fillColumnView(CellCoverRptTemplate template, TemplateColumn col)
        {
            this.curTemplate = template;
            if (curTemplate == null)
            {
                curColumn = null;
                this.gridCtrlCol.DataSource = null;
                this.gridCtrlCol.RefreshDataSource();
                return;
            }
            gridCtrlCol.DataSource = template.Columns;
            gridCtrlCol.RefreshDataSource();
            if (col == null)
            {
                gvCol.FocusedRowHandle = -1;
            }
            else
            {
                gvCol.FocusedRowHandle = curTemplate.Columns.IndexOf(col);
            }
        }


        private void btnDeleteCol_Click(object sender, EventArgs e)
        {
            if (curTemplate == null || curColumn == null)
            {
                return;
            }
            curTemplate.Columns.Remove(curColumn);
            fillColumnView(curTemplate, null);
        }

        private void btnUp_Click(object sender, EventArgs e)
        {
            int idx = curTemplate.Columns.IndexOf(curColumn);
            TemplateColumn preCol = curTemplate.Columns[idx - 1];
            curTemplate.Columns[idx - 1] = curColumn;
            curTemplate.Columns[idx] = preCol;
            fillColumnView(curTemplate, curColumn);
        }

        private void btnDown_Click(object sender, EventArgs e)
        {
            int idx = curTemplate.Columns.IndexOf(curColumn);
            TemplateColumn preCol = curTemplate.Columns[idx + 1];
            curTemplate.Columns[idx + 1] = curColumn;
            curTemplate.Columns[idx] = preCol;
            fillColumnView(curTemplate, curColumn);
        }
    }
}
