﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
namespace MasterCom.RAMS.ZTFunc
{
    public partial class LastMainRoadCalculate : MinCloseForm
    {
        public LastMainRoadCalculate()
        {
            InitializeComponent();
        }
        List<MainRoadResult> resultListN;
        List<MainRoadResult> mainResultListN;
        List<List<NPOIRow>> nrDatasList;
        List<string> sheetNames;
        public void FillData(List<MainRoadResult> resultList, List<MainRoadResult> mainResultList)
        {           
            BindingSource bindingSource = new BindingSource();
            bindingSource.DataSource = resultList;
            summarryGrid.DataSource = bindingSource;
            summarryGrid.RefreshDataSource();

            resultListN = new List<MainRoadResult>();
            resultListN.AddRange(resultList);

            BindingSource bindingSourceXQ = new BindingSource();
            bindingSourceXQ.DataSource = mainResultList;
            highGird.DataSource = bindingSourceXQ;
            highGird.RefreshDataSource();

            mainResultListN = new List<MainRoadResult>();
            mainResultListN.AddRange(mainResultList);
        }

        private void OutPutExcel()
        {
            List<NPOIRow> datas = new List<NPOIRow>();
            List<object> cols = new List<object>();
            NPOIRow nr1 = new NPOIRow();

            List<NPOIRow> dataXQ = new List<NPOIRow>();
            List<object> col2XQ = new List<object>();
            NPOIRow nrXQ = new NPOIRow();

            #region EXCEL-SHEET1列表构造
            cols.Add("所属城市");
            cols.Add("道路级别");
            cols.Add("网格类型");
            cols.Add("道路平面里程");
            cols.Add("渗透平面里程");
            cols.Add("平面渗透率");
            cols.Add("测试新增里程");
            cols.Add("实际测试距离");
            cols.Add("道路测试重复率");
            cols.Add("道路溢出率");
            cols.Add("实际测试车速");

            nr1.cellValues = cols;
            datas.Add(nr1);
            #endregion

            #region EXCEL-SHEET2列表构造
            col2XQ.Add("道路名称");
            col2XQ.Add("所属城市");
            col2XQ.Add("方向");
            col2XQ.Add("道路平面里程");
            col2XQ.Add("渗透平面里程");
            col2XQ.Add("平面渗透率");
            col2XQ.Add("测试新增里程");
            col2XQ.Add("实际测试距离");
            col2XQ.Add("道路测试重复率");
            col2XQ.Add("道路溢出率");
            col2XQ.Add("实际测试车速");

            nrXQ.cellValues = col2XQ;
            dataXQ.Add(nrXQ);
            #endregion


            int idx = 1;
            foreach (MainRoadResult deInfo in resultListN)
            {
                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();

                objs.Add(deInfo.Strcity);
                objs.Add(deInfo.StrType);
                objs.Add(deInfo.StrGridType);
                objs.Add(deInfo.FRoadDist.ToString());
                objs.Add(deInfo.FSeepDist.ToString());
                objs.Add(deInfo.StrSeepDist.ToString());
                objs.Add(deInfo.FNewDist.ToString());
                objs.Add(deInfo.FTestDist.ToString());
                objs.Add(deInfo.StrMainRoadTRRate);
                objs.Add(deInfo.StrTestOverRate);
                objs.Add(deInfo.FTestSpeed.ToString());

                nr.cellValues = objs;
                datas.Add(nr);

                idx++;
            }

            idx = 1;
            foreach (MainRoadResult deInfo in mainResultListN)
            {
                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();

                objs.Add(deInfo.StrMainRoadName);
                objs.Add(deInfo.Strcity);
                objs.Add(deInfo.StrDir);
                objs.Add(deInfo.FRoadDist.ToString());
                objs.Add(deInfo.FSeepDist.ToString());
                objs.Add(deInfo.StrSeepDist.ToString());
                objs.Add(deInfo.FNewDist.ToString());
                objs.Add(deInfo.FTestDist.ToString());
                objs.Add(deInfo.StrMainRoadTRRate);
                objs.Add(deInfo.StrTestOverRate);
                objs.Add(deInfo.FTestSpeed.ToString());

                nr.cellValues = objs;
                dataXQ.Add(nr);

                idx++;
            }

            nrDatasList = new List<List<NPOIRow>>();
            nrDatasList.Add(datas);
            nrDatasList.Add(dataXQ);

            sheetNames = new List<string>();
            sheetNames.Add("汇总表");
            sheetNames.Add("详情表");
        }

        private void ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            OutPutExcel();
            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }
    }
}
