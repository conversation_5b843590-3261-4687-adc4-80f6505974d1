﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class HighSpeedRailPrivateNetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridControlWeakCover = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportAllXls = new System.Windows.Forms.ToolStripMenuItem();
            this.gvWeakCover = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColProvinceCover = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xTabCtrHighSpeedRail = new DevExpress.XtraTab.XtraTabControl();
            this.xTabPageWeakCover = new DevExpress.XtraTab.XtraTabPage();
            this.xTabPageWeakSINR = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlWeakSINR = new DevExpress.XtraGrid.GridControl();
            this.gvWeakSINR = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColProvinceSINR = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xTabPageOutPrivateNet = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlOutPrivateNet = new DevExpress.XtraGrid.GridControl();
            this.gvOutPrivateNet = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColProvincePNet = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlWeakCover)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvWeakCover)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xTabCtrHighSpeedRail)).BeginInit();
            this.xTabCtrHighSpeedRail.SuspendLayout();
            this.xTabPageWeakCover.SuspendLayout();
            this.xTabPageWeakSINR.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlWeakSINR)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvWeakSINR)).BeginInit();
            this.xTabPageOutPrivateNet.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlOutPrivateNet)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvOutPrivateNet)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControlWeakCover
            // 
            this.gridControlWeakCover.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlWeakCover.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlWeakCover.Location = new System.Drawing.Point(0, 0);
            this.gridControlWeakCover.LookAndFeel.SkinName = "Office 2010 Blue";
            this.gridControlWeakCover.MainView = this.gvWeakCover;
            this.gridControlWeakCover.Name = "gridControlWeakCover";
            this.gridControlWeakCover.Size = new System.Drawing.Size(1031, 545);
            this.gridControlWeakCover.TabIndex = 2;
            this.gridControlWeakCover.UseEmbeddedNavigator = true;
            this.gridControlWeakCover.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvWeakCover});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport2Xls,
            this.miExportAllXls});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(154, 48);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(153, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // miExportAllXls
            // 
            this.miExportAllXls.Name = "miExportAllXls";
            this.miExportAllXls.Size = new System.Drawing.Size(153, 22);
            this.miExportAllXls.Text = "全部导出Excel";
            this.miExportAllXls.Click += new System.EventHandler(this.miExportAllXls_Click);
            // 
            // gvWeakCover
            // 
            this.gvWeakCover.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gvWeakCover.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gvWeakCover.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gvWeakCover.ColumnPanelRowHeight = 50;
            this.gvWeakCover.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColProvinceCover,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10});
            this.gvWeakCover.GridControl = this.gridControlWeakCover;
            this.gvWeakCover.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gvWeakCover.Name = "gvWeakCover";
            this.gvWeakCover.OptionsBehavior.Editable = false;
            this.gvWeakCover.OptionsDetail.EnableMasterViewMode = false;
            this.gvWeakCover.OptionsDetail.ShowDetailTabs = false;
            this.gvWeakCover.OptionsView.ColumnAutoWidth = false;
            this.gvWeakCover.OptionsView.ShowGroupPanel = false;
            this.gvWeakCover.DoubleClick += new System.EventHandler(this.gvWeakCover_DoubleClick);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "线路";
            this.gridColumn1.FieldName = "LineName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 120;
            // 
            // gridColProvinceCover
            // 
            this.gridColProvinceCover.Caption = "省份";
            this.gridColProvinceCover.FieldName = "ProvinceName";
            this.gridColProvinceCover.Name = "gridColProvinceCover";
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "地市";
            this.gridColumn3.FieldName = "CityName";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 1;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "事件类型";
            this.gridColumn4.FieldName = "EventType";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 2;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "开始时间";
            this.gridColumn5.FieldName = "StartTime";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 3;
            this.gridColumn5.Width = 120;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "文件名称";
            this.gridColumn6.FieldName = "FileName";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 4;
            this.gridColumn6.Width = 160;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "经度";
            this.gridColumn7.FieldName = "Longitude";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 5;
            this.gridColumn7.Width = 110;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "纬度";
            this.gridColumn8.FieldName = "Latitude";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 6;
            this.gridColumn8.Width = 110;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "发生小区ECI";
            this.gridColumn9.FieldName = "ECI";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 7;
            this.gridColumn9.Width = 120;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "原因归类";
            this.gridColumn10.FieldName = "Reason";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 8;
            this.gridColumn10.Width = 85;
            // 
            // xTabCtrHighSpeedRail
            // 
            this.xTabCtrHighSpeedRail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xTabCtrHighSpeedRail.Location = new System.Drawing.Point(0, 0);
            this.xTabCtrHighSpeedRail.Name = "xTabCtrHighSpeedRail";
            this.xTabCtrHighSpeedRail.SelectedTabPage = this.xTabPageWeakCover;
            this.xTabCtrHighSpeedRail.Size = new System.Drawing.Size(1039, 575);
            this.xTabCtrHighSpeedRail.TabIndex = 4;
            this.xTabCtrHighSpeedRail.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xTabPageWeakCover,
            this.xTabPageWeakSINR,
            this.xTabPageOutPrivateNet});
            this.xTabCtrHighSpeedRail.SelectedPageChanged += new DevExpress.XtraTab.TabPageChangedEventHandler(this.xTabCtrHighSpeedRail_SelectedPageChanged);
            // 
            // xTabPageWeakCover
            // 
            this.xTabPageWeakCover.Controls.Add(this.gridControlWeakCover);
            this.xTabPageWeakCover.Name = "xTabPageWeakCover";
            this.xTabPageWeakCover.Size = new System.Drawing.Size(1031, 545);
            this.xTabPageWeakCover.Text = "连续弱覆盖详表";
            // 
            // xTabPageWeakSINR
            // 
            this.xTabPageWeakSINR.Controls.Add(this.gridControlWeakSINR);
            this.xTabPageWeakSINR.Name = "xTabPageWeakSINR";
            this.xTabPageWeakSINR.Size = new System.Drawing.Size(1032, 545);
            this.xTabPageWeakSINR.Text = "连续质差详表";
            // 
            // gridControlWeakSINR
            // 
            this.gridControlWeakSINR.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlWeakSINR.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlWeakSINR.Location = new System.Drawing.Point(0, 0);
            this.gridControlWeakSINR.LookAndFeel.SkinName = "Office 2010 Blue";
            this.gridControlWeakSINR.MainView = this.gvWeakSINR;
            this.gridControlWeakSINR.Name = "gridControlWeakSINR";
            this.gridControlWeakSINR.Size = new System.Drawing.Size(1032, 545);
            this.gridControlWeakSINR.TabIndex = 3;
            this.gridControlWeakSINR.UseEmbeddedNavigator = true;
            this.gridControlWeakSINR.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvWeakSINR});
            // 
            // gvWeakSINR
            // 
            this.gvWeakSINR.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gvWeakSINR.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gvWeakSINR.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gvWeakSINR.ColumnPanelRowHeight = 50;
            this.gvWeakSINR.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn11,
            this.gridColProvinceSINR,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20});
            this.gvWeakSINR.GridControl = this.gridControlWeakSINR;
            this.gvWeakSINR.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gvWeakSINR.Name = "gvWeakSINR";
            this.gvWeakSINR.OptionsBehavior.Editable = false;
            this.gvWeakSINR.OptionsDetail.EnableMasterViewMode = false;
            this.gvWeakSINR.OptionsDetail.ShowDetailTabs = false;
            this.gvWeakSINR.OptionsView.ColumnAutoWidth = false;
            this.gvWeakSINR.OptionsView.ShowGroupPanel = false;
            this.gvWeakSINR.DoubleClick += new System.EventHandler(this.gvWeakSINR_DoubleClick);
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "线路";
            this.gridColumn11.FieldName = "LineName";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 0;
            this.gridColumn11.Width = 120;
            // 
            // gridColProvinceSINR
            // 
            this.gridColProvinceSINR.Caption = "省份";
            this.gridColProvinceSINR.FieldName = "ProvinceName";
            this.gridColProvinceSINR.Name = "gridColProvinceSINR";
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "地市";
            this.gridColumn13.FieldName = "CityName";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 1;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "事件类型";
            this.gridColumn14.FieldName = "EventType";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 2;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "开始时间";
            this.gridColumn15.FieldName = "StartTime";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 3;
            this.gridColumn15.Width = 120;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "文件名称";
            this.gridColumn16.FieldName = "FileName";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 4;
            this.gridColumn16.Width = 160;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "经度";
            this.gridColumn17.FieldName = "Longitude";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 5;
            this.gridColumn17.Width = 110;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "纬度";
            this.gridColumn18.FieldName = "Latitude";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 6;
            this.gridColumn18.Width = 110;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "发生小区ECI";
            this.gridColumn19.FieldName = "ECI";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 7;
            this.gridColumn19.Width = 120;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "原因归类";
            this.gridColumn20.FieldName = "Reason";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 8;
            this.gridColumn20.Width = 85;
            // 
            // xTabPageOutPrivateNet
            // 
            this.xTabPageOutPrivateNet.Controls.Add(this.gridControlOutPrivateNet);
            this.xTabPageOutPrivateNet.Name = "xTabPageOutPrivateNet";
            this.xTabPageOutPrivateNet.Size = new System.Drawing.Size(1032, 545);
            this.xTabPageOutPrivateNet.Text = "出专网详表";
            // 
            // gridControlOutPrivateNet
            // 
            this.gridControlOutPrivateNet.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlOutPrivateNet.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlOutPrivateNet.Location = new System.Drawing.Point(0, 0);
            this.gridControlOutPrivateNet.LookAndFeel.SkinName = "Office 2010 Blue";
            this.gridControlOutPrivateNet.MainView = this.gvOutPrivateNet;
            this.gridControlOutPrivateNet.Name = "gridControlOutPrivateNet";
            this.gridControlOutPrivateNet.Size = new System.Drawing.Size(1032, 545);
            this.gridControlOutPrivateNet.TabIndex = 4;
            this.gridControlOutPrivateNet.UseEmbeddedNavigator = true;
            this.gridControlOutPrivateNet.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvOutPrivateNet});
            // 
            // gvOutPrivateNet
            // 
            this.gvOutPrivateNet.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gvOutPrivateNet.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gvOutPrivateNet.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gvOutPrivateNet.ColumnPanelRowHeight = 50;
            this.gvOutPrivateNet.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn21,
            this.gridColProvincePNet,
            this.gridColumn23,
            this.gridColumn26,
            this.gridColumn25,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn31,
            this.gridColumn36,
            this.gridColumn35,
            this.gridColumn30,
            this.gridColumn34,
            this.gridColumn33});
            this.gvOutPrivateNet.GridControl = this.gridControlOutPrivateNet;
            this.gvOutPrivateNet.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gvOutPrivateNet.Name = "gvOutPrivateNet";
            this.gvOutPrivateNet.OptionsBehavior.Editable = false;
            this.gvOutPrivateNet.OptionsDetail.EnableMasterViewMode = false;
            this.gvOutPrivateNet.OptionsDetail.ShowDetailTabs = false;
            this.gvOutPrivateNet.OptionsView.ColumnAutoWidth = false;
            this.gvOutPrivateNet.OptionsView.ShowGroupPanel = false;
            this.gvOutPrivateNet.DoubleClick += new System.EventHandler(this.gvOutPrivateNet_DoubleClick);
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "线路";
            this.gridColumn21.FieldName = "LineName";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 0;
            this.gridColumn21.Width = 120;
            // 
            // gridColProvincePNet
            // 
            this.gridColProvincePNet.Caption = "省份";
            this.gridColProvincePNet.FieldName = "ProvinceName";
            this.gridColProvincePNet.Name = "gridColProvincePNet";
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "地市";
            this.gridColumn23.FieldName = "CityName";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 1;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "文件名称";
            this.gridColumn26.FieldName = "FileName";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 2;
            this.gridColumn26.Width = 160;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "出专网时间";
            this.gridColumn25.FieldName = "StartTime";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 3;
            this.gridColumn25.Width = 120;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "出专网经度";
            this.gridColumn27.FieldName = "Longitude";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 4;
            this.gridColumn27.Width = 110;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "出专网纬度";
            this.gridColumn28.FieldName = "Latitude";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 5;
            this.gridColumn28.Width = 110;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "出专网前ECI";
            this.gridColumn29.FieldName = "ECI";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 6;
            this.gridColumn29.Width = 100;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "出专网前TAC";
            this.gridColumn31.FieldName = "TAC";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 7;
            this.gridColumn31.Width = 100;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "出专网后的ECI(4G)\n或CI(2\\3G)";
            this.gridColumn36.FieldName = "AfterECI";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 8;
            this.gridColumn36.Width = 120;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "出专网后的TAC/LAC";
            this.gridColumn35.FieldName = "AfterTAC";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 9;
            this.gridColumn35.Width = 120;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "原因归类";
            this.gridColumn30.FieldName = "Reason";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 10;
            this.gridColumn30.Width = 85;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "再返回到专网的时间";
            this.gridColumn34.FieldName = "BackTime";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 11;
            this.gridColumn34.Width = 120;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "持续时间（秒）";
            this.gridColumn33.FieldName = "Duration";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 12;
            // 
            // HighSpeedRailPrivateNetForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1039, 575);
            this.Controls.Add(this.xTabCtrHighSpeedRail);
            this.IsMdiContainer = true;
            this.Name = "HighSpeedRailPrivateNetForm";
            this.Text = "高铁专网分析";
            ((System.ComponentModel.ISupportInitialize)(this.gridControlWeakCover)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gvWeakCover)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xTabCtrHighSpeedRail)).EndInit();
            this.xTabCtrHighSpeedRail.ResumeLayout(false);
            this.xTabPageWeakCover.ResumeLayout(false);
            this.xTabPageWeakSINR.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlWeakSINR)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvWeakSINR)).EndInit();
            this.xTabPageOutPrivateNet.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlOutPrivateNet)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvOutPrivateNet)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControlWeakCover;
        private DevExpress.XtraGrid.Views.Grid.GridView gvWeakCover;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColProvinceCover;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraTab.XtraTabControl xTabCtrHighSpeedRail;
        private DevExpress.XtraTab.XtraTabPage xTabPageWeakCover;
        private DevExpress.XtraTab.XtraTabPage xTabPageWeakSINR;
        private DevExpress.XtraTab.XtraTabPage xTabPageOutPrivateNet;
        private DevExpress.XtraGrid.GridControl gridControlWeakSINR;
        private DevExpress.XtraGrid.Views.Grid.GridView gvWeakSINR;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColProvinceSINR;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.GridControl gridControlOutPrivateNet;
        private DevExpress.XtraGrid.Views.Grid.GridView gvOutPrivateNet;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColProvincePNet;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private System.Windows.Forms.ToolStripMenuItem miExportAllXls;
    }
}