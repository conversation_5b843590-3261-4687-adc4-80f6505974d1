﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.NOP
{
    public class NopCfgMngr
    {
        private readonly string nopServerCfgName = Application.StartupPath + "\\config\\nopserver.xml";
        private static NopCfgMngr instance = null;

        public static NopCfgMngr Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new NopCfgMngr();
                }
                return instance;
            }
        }

        private NopCfgMngr()
        {
            TaskDBServerName = "FusionAna_DB";
            load();
        }

        public string TaskDBServerName
        {
            get;
            set;
        }

        private NopServer nopServer = null;

        public NopServer NopServer
        {
            get
            {
                return nopServer;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                if (nopServer != value)
                {
                    nopServer = value;
                    save();
                }
            }
        }

        public string Password
        {
            get { return "mastercom"; }
        }

        private void load()
        {
            if (!File.Exists(nopServerCfgName))
            {
                return;
            }

            XmlConfigFile configFile = new XmlConfigFile(nopServerCfgName);
            object obj = configFile.GetItemValue("Config", "NopServer");
            if (obj != null && obj is Dictionary<string, object>)
            {
                this.nopServer = new NopServer();
                this.nopServer.Param = obj as Dictionary<string, object>;
            }
            obj = configFile.GetItemValue("Config", "TaskDBServerName");
            if (obj!=null)
            {
                this.TaskDBServerName = obj.ToString();
            }
        }

        private void save()
        {
            XmlConfigFile configFile = new XmlConfigFile();
            XmlElement config = configFile.AddConfig("Config");
            configFile.AddItem(config, "NopServer", this.nopServer.Param);
            configFile.AddItem(config, "TaskDBServerName", TaskDBServerName);
            configFile.Save(nopServerCfgName);
        }

    }
}
