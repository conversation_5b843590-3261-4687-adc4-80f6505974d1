﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;
using DevExpress.XtraEditors.Controls;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTSiteRoadDistanceSetForm : BaseDialog
    {
        public string curTableName { get; set; }
        public Dictionary<string, CheckedListBoxItem> checkedListBoxItemDic { get; set; } = new Dictionary<string, CheckedListBoxItem>();

        public ZTSiteRoadDistanceSetForm()
        {
            InitializeComponent();

            this.edtMap.ButtonClick += edtMap_ButtonClick;
        }

        void edtMap_ButtonClick(object sender, ButtonPressedEventArgs e)
        {
            this.edtMap_Click(null,null);
        }

        public ZTSiteRoadDistanceCondition GetCondition()
        {
            ZTSiteRoadDistanceCondition condition = new ZTSiteRoadDistanceCondition();
            condition.SiteFileName = this.tbxSiteFileName.Text;
            condition.Distance = (int)this.numDistance.Value;

            foreach (string tbName in checkedListBoxItemDic.Keys)
            {
                if (checkedListBoxItemDic[tbName].CheckState == CheckState.Checked)
                {
                    condition.ShpFileList.Add((StreetInjectTableInfo)checkedListBoxItemDic[tbName].Value);
                }
            }
            return condition;
        }

        private void btnOpenSite_Click(object sender, EventArgs e)
        {
            OpenFileDialog openFileDlg = new OpenFileDialog();
            openFileDlg.Multiselect = false;
            openFileDlg.Filter = "表格文件|*.xls;*.xlsx";
            if (openFileDlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return;
            }

            this.tbxSiteFileName.Text = openFileDlg.FileName;
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (checkMap(edtMap.Text.Trim()))
            {
                StreetInjectTableInfo streetInfo = new StreetInjectTableInfo();
                streetInfo.FilePath = edtMap.Text.Trim();
                streetInfo.ColumnName = this.cbxColumn.Text;
                if (checkedListBoxItemDic.ContainsKey(curTableName))
                {
                    int index = clbMap.FindString(curTableName);
                    clbMap.SetItemValue(streetInfo, index);
                    checkedListBoxItemDic[curTableName].Value = streetInfo;
                }
                else
                {
                    CheckedListBoxItem item = new CheckedListBoxItem();
                    item.Description = curTableName;
                    item.CheckState = CheckState.Checked;
                    item.Value = streetInfo;
                    clbMap.Items.Add(item);
                    checkedListBoxItemDic[curTableName] = item;
                }
            }
        }

        private bool checkMap(string filePath)
        {
            if (File.Exists(filePath))
            {
                return true;
            }
            return false;
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (clbMap.SelectedIndex >= 0)
            {
                checkedListBoxItemDic.Remove(clbMap.Items[clbMap.SelectedIndex].ToString());
                clbMap.Items.RemoveAt(clbMap.SelectedIndex);
            }
        }

        private void btnSelectAll_Click(object sender, EventArgs e)
        {
            clbMap.CheckAll();
        }

        private void btnSelectNone_Click(object sender, EventArgs e)
        {
            clbMap.UnCheckAll();
        }

        private void edtMap_Click(object sender, EventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.Multiselect = false;
            openFileDialog.CheckFileExists = true;
            openFileDialog.DefaultExt = "SHP";
            openFileDialog.Filter = FilterHelper.Shp;
            openFileDialog.InitialDirectory = Application.StartupPath + @"\GEOGRAPHIC";

            if (openFileDialog.ShowDialog(this) == DialogResult.OK)
            {
                string typeName = null;
                MapWinGIS.Shapefile table = new MapWinGIS.Shapefile();
                try
                {
                    System.IO.FileInfo fileInfo = new System.IO.FileInfo(openFileDialog.FileName);
                    typeName = fileInfo.Name;
                    if (typeName.IndexOf('.') >= 0)
                    {
                        typeName = typeName.Substring(0, typeName.IndexOf('.'));
                    }

                    if (table.Open(openFileDialog.FileName, null))
                    {
                        cbxColumn.Properties.Items.Clear();
                        int numFields = table.NumFields;
                        for (int x = 0; x < numFields; x++)
                        {
                            MapWinGIS.Field field = table.get_Field(x);
                            cbxColumn.Properties.Items.Add(field.Name);
                        }
                        cbxColumn.SelectedIndex = 0;
                    }
                    this.edtMap.Text = openFileDialog.FileName;
                    curTableName = typeName;
                }
                catch
                {
                    //continue
                }
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (this.tbxSiteFileName.Text == "")
            {
                MessageBox.Show("请设置基站模板。");
                DialogResult = DialogResult.Retry;
                return;
            }
            if (clbMap.Items.Count <= 0)
            {
                MessageBox.Show("请添加道路图层。");
                DialogResult = DialogResult.Retry;
                return;
            }
            if (clbMap.CheckedIndices.Count <= 0)
            {
                MessageBox.Show("请选择道路图层。");
                DialogResult = DialogResult.Retry;
                return;
            }

            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
