﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRDropCallAnaInfo
    {
        public NRDropCallAnaInfo()
        { 
        
        }

        private NRDropCallAnaInfo otherSideCall;
        public NRDropCallAnaInfo OtherSideCall
        {
            get { return otherSideCall; }
            set
            {
                otherSideCall = value;
                if (otherSideCall != null)
                {
                    otherSideCall.otherSideCall = this;
                }
            }
        }

        public string FileName { get; set; }
        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }

        public Event DropEvt { get; set; }
        public DateTime DropTime { get; set; }
        public string DropTimeDesc { get; set; }
        public ENRDropCallCause DropCause { get; set; } = ENRDropCallCause.其它;
        public string Suggest { get; set; }
        public bool IsFilter { get; set; }

        public List<TestPoint> TestPoints { get; protected set; } = new List<TestPoint>();
        public List<Event> Events { get; protected set; } = new List<Event>();
        public List<Message> Messages { get; protected set; } = new List<Message>();

        public bool IsDropCall { get; set; }
        public string IsDropCallDesc { get; set; }

        public string MoMtDesc { get; set; }
        public bool IsMultiCvr { get; set; }

        public DataInfo RsrpInfo { get; set; } = new DataInfo();
        public DataInfo SinrInfo { get; set; } = new DataInfo();
        public DataInfo LteRsrpInfo { get; set; } = new DataInfo();
        public DataInfo LteSinrInfo { get; set; } = new DataInfo();
        public DataInfo MultiCvrInfo { get; set; } = new DataInfo();

        public int HoNum { get; set; }

        readonly NRDropCallAnaHelper helper = new NRDropCallAnaHelper();

        public void Evaluate(NRDropCallAnaCondtion dropCallCond, bool both)
        {
            foreach (TestPoint tp in TestPoints)
            {
                double totalSeconds = (DropTime - DateTime.Parse(tp.DateTimeStringWithMillisecond)).TotalSeconds;
                if (totalSeconds <= dropCallCond.WeakRsrpSec)
                {
                    float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
                    RsrpInfo.Add(rsrp);

                    float? lteRsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(tp);
                    LteRsrpInfo.Add(lteRsrp);
                }
                if (totalSeconds <= dropCallCond.PoorSinrSec)
                {
                    float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);
                    SinrInfo.Add(sinr);

                    float? lteSinr = NRTpHelper.NrLteTpManager.GetSCellSinr(tp);
                    LteSinrInfo.Add(lteSinr);
                }
                if (totalSeconds <= dropCallCond.MultiSec)
                {
                    dealMultiCvr(dropCallCond, MultiCvrInfo, tp);
                }
            }

            if (IsDropCall)
            {
                IsDropCallDesc = "是";
            }
            else
            {
                IsDropCallDesc = "否";
            }

            RsrpInfo.Calculate();
            SinrInfo.Calculate();
            LteRsrpInfo.Calculate();
            LteSinrInfo.Calculate();
            MultiCvrInfo.Calculate();
            MultiCvrInfo.Avg *= 100d;

            dealHandoverEvt(dropCallCond);

            dealDropCause(dropCallCond);

            if (both && this.otherSideCall != null)
            {
                this.otherSideCall.Evaluate(dropCallCond, false);
            }
        }

        private void dealMultiCvr(NRDropCallAnaCondtion dropCallCond, DataInfo multiCvrInfo, TestPoint tp)
        {
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            if (rsrp != null)
            {
                multiCvrInfo.Count++;
                int num = 1;
                for (int i = 0; i < 16; i++)
                {
                    bool isNCell = NRTpHelper.NrTpManager.JudgeIsNCell(tp, i);
                    if (!isNCell)
                    {
                        continue;
                    }
                    float? rsrpN = NRTpHelper.NrTpManager.GetNCellRsrp(tp, i);
                    if (rsrpN == null)
                    {
                        break;
                    }
                    if (Math.Abs((float)(rsrpN - rsrp)) <= dropCallCond.MultiBand)
                    {
                        num++;
                    }
                }
                if (num >= dropCallCond.MultiValue)
                {
                    multiCvrInfo.Sum++;
                }
            }
        }

        private void dealHandoverEvt(NRDropCallAnaCondtion dropCallCond)
        {
            foreach (Event evt in Events)
            {
                if (evt.DateTime > this.DropTime)
                {
                    break;
                }
                if (helper.HandOverEvtIdList.Contains(evt.ID))
                {
                    double totalSeconds = (DropTime - evt.DateTime).TotalSeconds;
                    if (totalSeconds <= dropCallCond.HoSec)
                    {
                        HoNum++;
                    }
                }
            }
        }

        private void dealDropCause(NRDropCallAnaCondtion dropCallCond)
        {
            bool netProb = false;
            foreach (ZTNRDropCallAna.NRDropCallAnaCauseBase cause in dropCallCond.CauseSet)
            {
                if (cause.IsSatisfy(this))
                {
                    netProb = true;
                }
            }

            if (!netProb)
            {
                if (this.RsrpInfo.Avg <= dropCallCond.WeakRsrp)
                {
                    DropCause = ENRDropCallCause.弱覆盖;
                }
                else if (this.SinrInfo.Avg <= dropCallCond.PoorSinr)
                {
                    DropCause = ENRDropCallCause.质差;
                }
                else if (this.MultiCvrInfo.Avg >= dropCallCond.MultiPer)
                {
                    DropCause = ENRDropCallCause.高重叠覆盖;
                    IsMultiCvr = true;
                }
                else if (this.HoNum >= dropCallCond.HoNum)
                {
                    DropCause = ENRDropCallCause.频繁切换;
                }
            }
        }

        public class DataInfo
        {
            public int Count { get; set; }
            public double Sum { get; set; }
            public double Avg { get; set; }

            public void Add(float? data)
            {
                if (data != null)
                {
                    Sum += (float)data;
                    Count++;
                }
            }

            public void Calculate()
            {
                if (Count != 0)
                {
                    Avg = Math.Round(Sum / Count, 2);
                }
                else
                {
                    Avg = 0;
                }
            }
        }
    }

    public enum ENRDropCallCause
    {
        VoiceHangup,
        双BYE,
        BYE_Request_Terminated,
        提前释放EPS专用承载,
        弱覆盖,
        质差,
        高重叠覆盖,
        频繁切换,
        其它
    }
}
