﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTDIYQuerLteHighirskAnaSetForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label14 = new System.Windows.Forms.Label();
            this.numWeakCoverSampleCount = new System.Windows.Forms.NumericUpDown();
            this.label24 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.numWeakCoverMeanRSRP = new System.Windows.Forms.NumericUpDown();
            this.label23 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.numWeakCoverRate = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.numWeakCoverRsrp = new System.Windows.Forms.NumericUpDown();
            this.label9 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.numOverCoverSampleCount = new System.Windows.Forms.NumericUpDown();
            this.numOverCoverRadiusRatio = new System.Windows.Forms.NumericUpDown();
            this.label18 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.numOverCoverRate = new System.Windows.Forms.NumericUpDown();
            this.label12 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.numOverCoverDistanceMax = new System.Windows.Forms.NumericUpDown();
            this.numOverCoverDistanceMin = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.numOverCoverFilter = new System.Windows.Forms.NumericUpDown();
            this.label16 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.numOverCoverInterfereRSRP = new System.Windows.Forms.NumericUpDown();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverSampleCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverMeanRSRP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverRsrp)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverSampleCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverRadiusRatio)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverDistanceMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverDistanceMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverFilter)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverInterfereRSRP)).BeginInit();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.btnOK.Location = new System.Drawing.Point(341, 261);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.btnCancel.Location = new System.Drawing.Point(434, 261);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 0;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label14);
            this.groupBox1.Controls.Add(this.numWeakCoverSampleCount);
            this.groupBox1.Controls.Add(this.label24);
            this.groupBox1.Controls.Add(this.label15);
            this.groupBox1.Controls.Add(this.numWeakCoverMeanRSRP);
            this.groupBox1.Controls.Add(this.label23);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.numWeakCoverRate);
            this.groupBox1.Controls.Add(this.label7);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.numWeakCoverRsrp);
            this.groupBox1.Controls.Add(this.label9);
            this.groupBox1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox1.Location = new System.Drawing.Point(18, 153);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(497, 93);
            this.groupBox1.TabIndex = 4;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "弱覆盖设置";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(180, 61);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(17, 12);
            this.label14.TabIndex = 51;
            this.label14.Text = "个";
            // 
            // numWeakCoverSampleCount
            // 
            this.numWeakCoverSampleCount.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakCoverSampleCount.Location = new System.Drawing.Point(94, 58);
            this.numWeakCoverSampleCount.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numWeakCoverSampleCount.Name = "numWeakCoverSampleCount";
            this.numWeakCoverSampleCount.Size = new System.Drawing.Size(80, 21);
            this.numWeakCoverSampleCount.TabIndex = 50;
            this.numWeakCoverSampleCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakCoverSampleCount.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label24.Location = new System.Drawing.Point(21, 62);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(65, 12);
            this.label24.TabIndex = 49;
            this.label24.Text = "采样点数≥";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label15.Location = new System.Drawing.Point(387, 28);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(23, 12);
            this.label15.TabIndex = 48;
            this.label15.Text = "dBm";
            // 
            // numWeakCoverMeanRSRP
            // 
            this.numWeakCoverMeanRSRP.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakCoverMeanRSRP.Location = new System.Drawing.Point(301, 23);
            this.numWeakCoverMeanRSRP.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numWeakCoverMeanRSRP.Minimum = new decimal(new int[] {
            10000,
            0,
            0,
            -2147483648});
            this.numWeakCoverMeanRSRP.Name = "numWeakCoverMeanRSRP";
            this.numWeakCoverMeanRSRP.Size = new System.Drawing.Size(80, 21);
            this.numWeakCoverMeanRSRP.TabIndex = 47;
            this.numWeakCoverMeanRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakCoverMeanRSRP.Value = new decimal(new int[] {
            115,
            0,
            0,
            -2147483648});
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label23.Location = new System.Drawing.Point(229, 27);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(65, 12);
            this.label23.TabIndex = 46;
            this.label23.Text = "平均RSRP≤";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label8.Location = new System.Drawing.Point(370, 65);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(11, 12);
            this.label8.TabIndex = 44;
            this.label8.Text = "%";
            // 
            // numWeakCoverRate
            // 
            this.numWeakCoverRate.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakCoverRate.Location = new System.Drawing.Point(282, 58);
            this.numWeakCoverRate.Name = "numWeakCoverRate";
            this.numWeakCoverRate.Size = new System.Drawing.Size(80, 21);
            this.numWeakCoverRate.TabIndex = 43;
            this.numWeakCoverRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakCoverRate.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label7.Location = new System.Drawing.Point(232, 62);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(41, 12);
            this.label7.TabIndex = 42;
            this.label7.Text = "比例≥";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.Location = new System.Drawing.Point(179, 24);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(23, 12);
            this.label5.TabIndex = 30;
            this.label5.Text = "dBm";
            // 
            // numWeakCoverRsrp
            // 
            this.numWeakCoverRsrp.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakCoverRsrp.Location = new System.Drawing.Point(93, 20);
            this.numWeakCoverRsrp.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numWeakCoverRsrp.Name = "numWeakCoverRsrp";
            this.numWeakCoverRsrp.Size = new System.Drawing.Size(80, 21);
            this.numWeakCoverRsrp.TabIndex = 29;
            this.numWeakCoverRsrp.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakCoverRsrp.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label9.Location = new System.Drawing.Point(21, 25);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(65, 12);
            this.label9.TabIndex = 28;
            this.label9.Text = "最强RSRP≤";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label16);
            this.groupBox2.Controls.Add(this.label17);
            this.groupBox2.Controls.Add(this.numOverCoverInterfereRSRP);
            this.groupBox2.Controls.Add(this.numOverCoverSampleCount);
            this.groupBox2.Controls.Add(this.numOverCoverRadiusRatio);
            this.groupBox2.Controls.Add(this.label18);
            this.groupBox2.Controls.Add(this.label6);
            this.groupBox2.Controls.Add(this.numOverCoverRate);
            this.groupBox2.Controls.Add(this.label12);
            this.groupBox2.Controls.Add(this.label4);
            this.groupBox2.Controls.Add(this.label2);
            this.groupBox2.Controls.Add(this.label10);
            this.groupBox2.Controls.Add(this.numOverCoverDistanceMax);
            this.groupBox2.Controls.Add(this.numOverCoverDistanceMin);
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Controls.Add(this.label13);
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Controls.Add(this.numOverCoverFilter);
            this.groupBox2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox2.Location = new System.Drawing.Point(18, 12);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(497, 135);
            this.groupBox2.TabIndex = 15;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "过覆盖设置";
            // 
            // numOverCoverSampleCount
            // 
            this.numOverCoverSampleCount.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numOverCoverSampleCount.Location = new System.Drawing.Point(318, 25);
            this.numOverCoverSampleCount.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numOverCoverSampleCount.Name = "numOverCoverSampleCount";
            this.numOverCoverSampleCount.Size = new System.Drawing.Size(80, 21);
            this.numOverCoverSampleCount.TabIndex = 51;
            this.numOverCoverSampleCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOverCoverSampleCount.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // numOverCoverRadiusRatio
            // 
            this.numOverCoverRadiusRatio.DecimalPlaces = 1;
            this.numOverCoverRadiusRatio.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numOverCoverRadiusRatio.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numOverCoverRadiusRatio.Location = new System.Drawing.Point(109, 97);
            this.numOverCoverRadiusRatio.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numOverCoverRadiusRatio.Name = "numOverCoverRadiusRatio";
            this.numOverCoverRadiusRatio.Size = new System.Drawing.Size(80, 21);
            this.numOverCoverRadiusRatio.TabIndex = 49;
            this.numOverCoverRadiusRatio.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOverCoverRadiusRatio.Value = new decimal(new int[] {
            16,
            0,
            0,
            65536});
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label18.Location = new System.Drawing.Point(12, 102);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(83, 12);
            this.label18.TabIndex = 48;
            this.label18.Text = "理想半径系数=";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label6.Location = new System.Drawing.Point(458, 60);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(11, 12);
            this.label6.TabIndex = 47;
            this.label6.Text = "%";
            // 
            // numOverCoverRate
            // 
            this.numOverCoverRate.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numOverCoverRate.Location = new System.Drawing.Point(372, 56);
            this.numOverCoverRate.Name = "numOverCoverRate";
            this.numOverCoverRate.Size = new System.Drawing.Size(80, 21);
            this.numOverCoverRate.TabIndex = 46;
            this.numOverCoverRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOverCoverRate.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label12.Location = new System.Drawing.Point(307, 60);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(59, 12);
            this.label12.TabIndex = 45;
            this.label12.Text = "米 比例≥";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(12, 69);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 41;
            this.label4.Text = "过覆盖距离";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.Location = new System.Drawing.Point(238, 29);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 40;
            this.label2.Text = "采样点数≥";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(414, 29);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(17, 12);
            this.label10.TabIndex = 38;
            this.label10.Text = "个";
            // 
            // numOverCoverDistanceMax
            // 
            this.numOverCoverDistanceMax.Increment = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numOverCoverDistanceMax.Location = new System.Drawing.Point(234, 56);
            this.numOverCoverDistanceMax.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numOverCoverDistanceMax.Name = "numOverCoverDistanceMax";
            this.numOverCoverDistanceMax.Size = new System.Drawing.Size(67, 21);
            this.numOverCoverDistanceMax.TabIndex = 36;
            this.numOverCoverDistanceMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOverCoverDistanceMax.Value = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            // 
            // numOverCoverDistanceMin
            // 
            this.numOverCoverDistanceMin.Increment = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numOverCoverDistanceMin.Location = new System.Drawing.Point(98, 60);
            this.numOverCoverDistanceMin.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numOverCoverDistanceMin.Name = "numOverCoverDistanceMin";
            this.numOverCoverDistanceMin.Size = new System.Drawing.Size(96, 21);
            this.numOverCoverDistanceMin.TabIndex = 35;
            this.numOverCoverDistanceMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOverCoverDistanceMin.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(202, 62);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(29, 12);
            this.label3.TabIndex = 32;
            this.label3.Text = "米至";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label13.Location = new System.Drawing.Point(15, 27);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(77, 12);
            this.label13.TabIndex = 27;
            this.label13.Text = "采样点场强≥";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(209, 29);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(23, 12);
            this.label1.TabIndex = 17;
            this.label1.Text = "dBm";
            // 
            // numOverCoverFilter
            // 
            this.numOverCoverFilter.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numOverCoverFilter.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numOverCoverFilter.Location = new System.Drawing.Point(98, 25);
            this.numOverCoverFilter.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numOverCoverFilter.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numOverCoverFilter.Name = "numOverCoverFilter";
            this.numOverCoverFilter.Size = new System.Drawing.Size(96, 21);
            this.numOverCoverFilter.TabIndex = 16;
            this.numOverCoverFilter.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOverCoverFilter.Value = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label16.Location = new System.Drawing.Point(209, 101);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(113, 12);
            this.label16.TabIndex = 54;
            this.label16.Text = "干扰小区RSCP差值≤";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label17.Location = new System.Drawing.Point(432, 101);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(23, 12);
            this.label17.TabIndex = 53;
            this.label17.Text = "dBm";
            // 
            // numOverCoverInterfereRSRP
            // 
            this.numOverCoverInterfereRSRP.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numOverCoverInterfereRSRP.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numOverCoverInterfereRSRP.Location = new System.Drawing.Point(328, 93);
            this.numOverCoverInterfereRSRP.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numOverCoverInterfereRSRP.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numOverCoverInterfereRSRP.Name = "numOverCoverInterfereRSRP";
            this.numOverCoverInterfereRSRP.Size = new System.Drawing.Size(96, 21);
            this.numOverCoverInterfereRSRP.TabIndex = 52;
            this.numOverCoverInterfereRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOverCoverInterfereRSRP.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // ZTDIYQuerLteHighirskAnaSetForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(542, 296);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.Name = "ZTDIYQuerLteHighirskAnaSetForm";
            this.Text = "高风险小区分析条件设置";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverSampleCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverMeanRSRP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverRsrp)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverSampleCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverRadiusRatio)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverDistanceMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverDistanceMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverFilter)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverInterfereRSRP)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.NumericUpDown numOverCoverDistanceMax;
        private System.Windows.Forms.NumericUpDown numOverCoverDistanceMin;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numOverCoverFilter;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown numWeakCoverRsrp;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown numWeakCoverRate;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown numOverCoverRate;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.NumericUpDown numOverCoverRadiusRatio;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.NumericUpDown numWeakCoverSampleCount;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.NumericUpDown numWeakCoverMeanRSRP;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.NumericUpDown numOverCoverSampleCount;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.NumericUpDown numOverCoverInterfereRSRP;
    }
}