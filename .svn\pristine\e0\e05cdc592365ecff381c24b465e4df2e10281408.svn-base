﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class NRUrlAnaByFile : DIYAnalyseFilesOneByOneByRegion
    {
        public NRUrlAnaByFile(MainModel mainModel)
            : base(mainModel)
        {
            this.FilterEventByRegion = false;
            this.IncludeMessage = true;
            this.IncludeTestPoint = false;
            analyzer = new NRUrlAnalyzerByFile();
        }

        private readonly NRUrlAnalyzerByFile analyzer;
        protected List<DTFileDataManager> fileManagers = null;
        protected Dictionary<int, string> districtName = null;
        private static NRUrlAnaByFile instance;

        public static NRUrlAnaByFile GetInstance()
        {
            if (instance == null)
            {
                instance = new NRUrlAnaByFile(MainModel.GetInstance());
            }
            return instance;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                if (districtName.ContainsKey(fileMng.FileID))
                {
                    continue;
                }
                DTFileDataManager file = new DTFileDataManager(fileMng.FileID, 
                    fileMng.FileName, 
                    fileMng.ProjectType, 
                    fileMng.TestType,
                    fileMng.CarrierType, 
                    fileMng.LogTable, 
                    fileMng.SampleTableName, 
                    fileMng.ServiceType, 
                    fileMng.MoMtFlag);
                addFileDTDatas(fileMng, file);
                foreach (FileInfo fileInfo in condition.FileInfos)
                {
                    if (fileMng.FileID == fileInfo.ID)
                    {
                        districtName.Add(fileMng.FileID, fileInfo.DistrictName);
                        break;
                    }
                }
                fileManagers.Add(file);
            }
        }

        private void addFileDTDatas(DTFileDataManager fileMng, DTFileDataManager file)
        {
            foreach (Event evt in fileMng.Events)
            {
                if (ZTUrlAnalyzer.HttpIDList.Contains(evt.ID)
                    || ZTUrlAnalyzer.DownloadIDList.Contains(evt.ID)
                    || ZTUrlAnalyzer.VideoIDList.Contains(evt.ID))
                {
                    file.Add(evt);
                }
            }
            foreach (MasterCom.RAMS.Model.Message msg in fileMng.Messages)
            {
                if (msg.ID == (int)NRMsgManager.Http_Page_Request
                    || msg.ID == (int)NRMsgManager.Http_Download_Begin
                    || msg.ID == (int)NRMsgManager.Video_Play_Request
                    || msg.ID == (int)NRMsgManager.Http_Page_Start) 
                {
                    file.Add(msg);
                }
            }
        }

        protected override void getResultsAfterQuery()
        {
            analyzer.Analyze(fileManagers, districtName);
        }

        protected override void fireShowForm()
        {
            NRUrlAnaForm resultForm = MainModel.CreateResultForm(typeof(NRUrlAnaForm)) as NRUrlAnaForm;
            resultForm.FillData(analyzer.Results, false);
            resultForm.Visible = true;
            resultForm.BringToFront();
        }

        protected override void getReadyBeforeQuery()
        {
            if (fileManagers == null)
            {
                fileManagers = new List<DTFileDataManager>();
            }
            else
            {
                fileManagers.Clear();
            }
            if (districtName == null)
            {
                districtName = new Dictionary<int, string>();
            }
            else
            {
                districtName.Clear();
            }
        }

        public override string Name
        {
            get { return "URL统计分析(按文件)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22059, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }
    }
}
