﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;

namespace MasterCom.RAMS.KPI_Statistics.PK
{
    public class PkGridLayer : LayerBase
    {
        public PkGridLayer()
            : base("栅格竞比")
        { }

        public Dictionary<PkGridDataHub, CheckAlthm> GridDic
        {
            get;
            set;
        }
        public List<PkGridDataHub> CurSelGridList { get; set; }
        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible || GridDic == null || GridDic.Count == 0)
            {
                return;
            }
            Rectangle inflatedRect = new Rectangle(updateRect.X, updateRect.Y, updateRect.Width, updateRect.Height);
            inflatedRect.Inflate(50, 50);
            DbRect dRect;
            gisAdapter.FromDisplay(updateRect, out dRect);

            RectangleF? size = null;
            foreach (PkGridDataHub grid in GridDic.Keys)
            {
                if (grid.Bounds.Within(dRect))
                {
                    drawGrid(graphics, grid, GridDic[grid], ref size);
                }
            }
        }

        private void drawGrid(Graphics graphics, PkGridDataHub grid, CheckAlthm chkItem, ref RectangleF? size)
        {
            if (!chkItem.BCheck)
            {
                return;
            }
            PointF pointLt;
            gisAdapter.ToDisplay(new DbPoint(grid.LTLng, grid.LTLat), out pointLt);
            if (size == null)
            {
                DbPoint brPoint = new DbPoint(grid.BRLng, grid.BRLat);
                PointF pointBr;
                gisAdapter.ToDisplay(brPoint, out pointBr);
                size = new RectangleF(pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
            }
            RectangleF rect = new RectangleF(pointLt.X, pointLt.Y, ((RectangleF)size).Width, ((RectangleF)size).Height);
            graphics.FillRectangle(new SolidBrush(chkItem.Althm.Color), rect);
            if (CurSelGridList != null && CurSelGridList.Contains(grid))
            {
                rect.Inflate(1.0f, 1.0f);
                graphics.DrawRectangle(new Pen(Color.Red), rect.X, rect.Y, rect.Width, rect.Height);
            }
        }


    }
}
