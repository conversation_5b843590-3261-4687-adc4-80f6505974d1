﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using System.Drawing;
using MasterCom.RAMS.Func.CoverageCheck;

namespace MasterCom.RAMS.ZTFunc
{
    public class PCIOptimizeLayer : CustomDrawLayer
    {
        private object obj;
        public object Obj
        {
            get { return obj; }
        }
        private List<SINRToPCI> allPntList;
        public List<SINRToPCI> AllPntList
        {
            get { return allPntList; }
        }

        public PCIOptimizeLayer(MapOperation mp, string name)
            : base(mp, name)
        {
            ColorRangeDic = new Dictionary<string, TextColorRange>();
        }

        public void Fill(object obj)
        {
            this.obj = obj;
        }

        public void FillPnts(List<SINRToPCI> sinrList)
        {
            this.allPntList = sinrList;
            fillLengend();
        }

        private readonly Dictionary<string, SolidBrush> brushDic = new Dictionary<string, SolidBrush>();
        private void fillLengend()
        {
            ColorRangeDic.Clear();
            brushDic.Clear();
            if (allPntList != null && allPntList.Count > 0)
            {
                foreach (SINRToPCI sinr in allPntList)
                {
                    ColorRangeDic[sinr.Stage] = new TextColorRange(((SolidBrush)sinr.GetBrush()).Color, sinr.Stage);
                    brushDic[sinr.Stage] = (SolidBrush)sinr.GetBrush();
                }
            }
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, System.Drawing.Graphics graphics)
        {
            if (obj != null)
            {
                bool isValid = drawResultPntWithLine(graphics);
                if (!isValid)
                {
                    return;
                }
            }
            foreach (SINRToPCI sinr in allPntList)
            {
                drawPnt(graphics, sinr);
            }
        }

        private bool drawResultPntWithLine(Graphics graphics)
        {
            if (obj is ArrangeResult)
            {
                ArrangeResult aResult = obj as ArrangeResult;
                if (aResult == null || aResult.mainCell == null)
                {
                    return false;
                }

                DbPoint pntMCell = new DbPoint(aResult.mainCell.Longitude, aResult.mainCell.Latitude);
                PointF pntFMCell;
                this.Map.ToDisplay(pntMCell, out pntFMCell);

                foreach (SINRToPCI sinr in aResult.SinrList)
                {
                    if (sinr.GetNBCell() != null)
                    {
                        drawPntWithLine(graphics, sinr);
                    }
                }
            }
            else if (obj is SINRToPCI)
            {
                SINRToPCI sinr = obj as SINRToPCI;
                drawPntWithLine(graphics, sinr);
            }
            else if (obj is List<SINRToPCI>)
            {
                List<SINRToPCI> sinrList = obj as List<SINRToPCI>;
                if (sinrList == null)
                {
                    return false;
                }

                foreach (SINRToPCI sinr in sinrList)
                {
                    drawPntWithLine(graphics, sinr);
                }
            }
            return true;
        }

        private void drawPnt(System.Drawing.Graphics graphics, SINRToPCI sinr)
        {
            if (!ColorRangeDic[sinr.Stage].Visible) return;

            DbPoint pntSinr = new DbPoint(sinr.longitude, sinr.latitude);
            PointF pntFSinr;
            this.Map.ToDisplay(pntSinr, out pntFSinr);

            graphics.FillEllipse(sinr.GetBrush(), pntFSinr.X - 6, pntFSinr.Y - 6, 12, 12);
        }

        private void drawPntWithLine(System.Drawing.Graphics graphics, SINRToPCI sinr)
        {
            if (!ColorRangeDic[sinr.Stage].Visible) return;

            if (sinr.mainCell == null || sinr.GetNBCell() == null) return;

            DbPoint pntMCell = sinr.GetLTEAntennaEndPoint(sinr.mainCell);
            PointF pntFMCell;
            this.Map.ToDisplay(pntMCell, out pntFMCell);

            DbPoint pntNBCell = sinr.GetLTEAntennaEndPoint(sinr.GetNBCell());
            PointF pntFNBCell;
            this.Map.ToDisplay(pntNBCell, out pntFNBCell);

            DbPoint pntSinr = new DbPoint(sinr.longitude, sinr.latitude);
            PointF pntFSinr;
            this.Map.ToDisplay(pntSinr, out pntFSinr);

            graphics.DrawLine(new Pen(Color.Blue, 1), pntFSinr, pntFMCell);
            graphics.DrawLine(new Pen(Color.Yellow, 1), pntFSinr, pntFNBCell);

            drawPnt(graphics, sinr);
        }
        
        public Dictionary<string, TextColorRange> ColorRangeDic { get; set; }
    }
}
