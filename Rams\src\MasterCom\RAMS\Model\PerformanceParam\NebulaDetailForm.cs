﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Frame;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Columns;
using MasterCom.RAMS.Model.PerformanceParam.TD切换分析;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.PerformanceParam
{
    public partial class NebulaDetailForm:XtraForm
    {
        MainModel mainModel;
        private DataTable tableMain;
        private DataTable tableChild;
        private bool IsTDAnalysis = false;
        public NebulaDetailForm(MainModel mainModel, bool IsTDAnalysis)
        {
            InitializeComponent();
            this.mainModel = mainModel;
            this.IsTDAnalysis = IsTDAnalysis;
            //ydd修改的关于两级显示的方法
            IniTableOfResult();
        }
        private void IniTableOfResult()
        {
            tableMain = new DataTable();
            tableChild = new DataTable();

            tableMain.Columns.Add("IMon");
            tableMain.Columns.Add("ILAC");
            tableMain.Columns.Add("ICI");
            tableMain.Columns.Add("LAC_CI");
            tableMain.Columns.Add("MDTChangeCount");
            tableMain.Columns.Add("ChangeSuccessCount");
            tableMain.Columns.Add("ChangeCount");
            tableMain.Columns.Add("ChangeSuccessRadio");

            tableChild.Columns.Add("targetLAC");
            tableChild.Columns.Add("targetCI");

            if (IsTDAnalysis)
            {
                GridColumn addedColumn = new DevExpress.XtraGrid.Columns.GridColumn();
                addedColumn.Caption = "网络切换类型";
                addedColumn.FieldName = "INet";
                addedColumn.Width = 70;
                addedColumn.VisibleIndex = 3;
                gridViewDetail.Columns.Insert(3, addedColumn);
                tableChild.Columns.Add("INet");
            }

            tableChild.Columns.Add("MDTChangeCount");
            tableChild.Columns.Add("outRadio");

            tableChild.Columns.Add("targetOutRadio");
            tableChild.Columns.Add("requestCout");

            tableChild.Columns.Add("successCount");
            tableChild.Columns.Add("successRadio");

            tableChild.Columns.Add("questionIndex");
            tableChild.Columns.Add("suggestInf");

            gridControlRes.DataSource =tableMain;
            gridControlRes.RefreshDataSource();
            gridControlDetail.DataSource = tableChild;
            gridControlDetail.RefreshDataSource();
        }
        private Dictionary<string, List<JoinHandoverAnalysis>> allDetailInfs;
        public void SetDataSource(List<JoinHandoverAnalysis> value)
        {
            iLevel = 0;
            if (value != null)
            {
                allDetailInfs = new Dictionary<string, List<JoinHandoverAnalysis>>();
                tableChild.Rows.Clear();
                tableMain.Rows.Clear();
                string keyInfFirst = "";
                foreach (JoinHandoverAnalysis jhAnalysisData in value)
                {
                    if (iLevel == 0)
                    {
                        iLevel = jhAnalysisData.ILevel;
                    }
                    string Lac_Ci = jhAnalysisData.LAC_CI;
                    if (!allDetailInfs.ContainsKey(Lac_Ci))
                    {
                        List<JoinHandoverAnalysis> detailInf = new List<JoinHandoverAnalysis>();
                        detailInf.Add(jhAnalysisData);
                        allDetailInfs.Add(Lac_Ci, detailInf);
                        if (keyInfFirst == "")
                        {
                            keyInfFirst = Lac_Ci;
                        }
                    }
                    else
                    {
                        allDetailInfs[Lac_Ci].Add(jhAnalysisData);
                    }
                }
                IniDetailGridControlShow(allDetailInfs[keyInfFirst]);
                IniMainTableShow();
            }
        }
        
        private void IniMainTableShow()
        {
            foreach (string keyInf in allDetailInfs.Keys)
            {
                List<object> rowInf = new List<object>();
                int MDTChangeCount = 0;
                int changeSuccesscount = 0;
                int changeSumCount = 0;
                foreach (JoinHandoverAnalysis jhAnalysis in allDetailInfs[keyInf])
                {
                    if (rowInf.Count == 0)
                    {
                        rowInf.Add(jhAnalysis.IMon);
                        rowInf.Add(jhAnalysis.ILac);
                        rowInf.Add(jhAnalysis.ICi);
                        rowInf.Add(jhAnalysis.LAC_CI);
                    }
                    MDTChangeCount += jhAnalysis.IHoNum;
                    changeSuccesscount += jhAnalysis.ISucc;
                    changeSumCount += jhAnalysis.IReq;
                }
                rowInf.Add(MDTChangeCount);
                rowInf.Add(changeSuccesscount);
                rowInf.Add(changeSumCount);
                rowInf.Add(((((double)(changeSuccesscount)) / ((double)(changeSumCount)))*100).ToString("0.0")+"%");

                tableMain.Rows.Add(rowInf.ToArray());
            }
        }
        private List<JoinHandoverAnalysis> detailInfSave;
        private void IniDetailGridControlShow(List<JoinHandoverAnalysis> detailInf)
        {
            List<object> rowInf;
            tableChild.Rows.Clear();
            foreach (JoinHandoverAnalysis jhAnalysis in detailInf)
            {
                rowInf = new List<object>();
                rowInf.Add( jhAnalysis.ITargetLac);
                rowInf.Add(jhAnalysis.ITargetCi);
                if (IsTDAnalysis)
                {
                    if (((JoinHandoverAnalysisTD)jhAnalysis).INet == 1)
                    {
                        rowInf.Add("TD网内切换");
                    }
                    else
                    {
                        rowInf.Add("TD网间切换");
                    }
                }
                rowInf.Add(jhAnalysis.IHoNum);
                rowInf.Add((jhAnalysis.FoutRate*100).ToString("0.0")+"%");
                rowInf.Add((jhAnalysis.FintRate*100).ToString("0.0")+"%");
                rowInf.Add(jhAnalysis.IReq);
                rowInf.Add(jhAnalysis.ISucc);
                rowInf.Add((jhAnalysis.FSuccRate*100).ToString("0.0")+"%");
                switch (jhAnalysis.ILevel)
                {
                    case 1:
                        rowInf.Add("正常小区");
                        break;
                    case 2:
                        rowInf.Add("冗余小区");
                        break;
                    case 3:
                        rowInf.Add("低质小区");
                        break;
                    case 4:
                        rowInf.Add("规避小区");
                        break;
                    default:
                        break;
                }
                rowInf.Add(jhAnalysis.Strcomment);
                tableChild.Rows.Add(rowInf.ToArray());
            }
            detailInfSave = detailInf;
        }      

        private int iLevel = 0;

       

        private void gvTest1_RowStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowStyleEventArgs e)
        {
            if (iLevel == 1)
            {
                e.Appearance.BackColor = Color.FromArgb(100,0,255,0);//  .MediumSeaGreen
            }
            else if (iLevel == 2)
            {
                e.Appearance.BackColor = Color.FromArgb(100, 0, 0, 255);//  .DodgerBlue
            }
            else if (iLevel == 3)
            {
                e.Appearance.BackColor = Color.DarkOrange;
            }
            else if (iLevel == 4)
            {
                e.Appearance.BackColor = Color.FromArgb(100, 255, 0, 0);// .Red
            }
        }



        private void gvMain_RowStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowStyleEventArgs e)
        {
            if (iLevel == 1)
            {
                e.Appearance.BackColor = Color.FromArgb(255, 200, 255, 200);//  .MediumSeaGreen
            }
            else if (iLevel == 2)
            {
                e.Appearance.BackColor = Color.FromArgb(255, 230, 230, 255);//  .DodgerBlue
            }
            else if (iLevel == 3)
            {
                //rgb 233 150 122
                //e.Appearance.BackColor = Color.DarkOrange
                e.Appearance.BackColor = Color.FromArgb(255, 233, 150, 122);
            }
            else if (iLevel == 4)
            {
                e.Appearance.BackColor = Color.FromArgb(255, 255, 230, 230);// .Red
            }
        }

        private void gvMain_FocusedRowChanged(object sender, FocusedRowChangedEventArgs e)
        {
            Cell cell = null;
            TDCell tdCell = null;
            string lac_ci = gvMain.GetFocusedRowCellDisplayText(gcLAC_CI);//
            if (allDetailInfs==null||!allDetailInfs.ContainsKey(lac_ci))
            {
                return;
            }
            List<JoinHandoverAnalysis> witchingList = allDetailInfs[lac_ci];

            if (witchingList != null)
            {
                mainModel.HandoverAnalysisList.Clear();
                foreach (JoinHandoverAnalysis handoverAnalysis in witchingList)
                {
                    mainModel.HandoverAnalysisList.Add(handoverAnalysis);
                    string date = handoverAnalysis.IMon.ToString().Substring(0, 4) +
                        "-" + handoverAnalysis.IMon.ToString().Substring(4, 2);
                    gotoView(ref cell, ref tdCell, handoverAnalysis.ILac, handoverAnalysis.ICi, date);
                }
                IniDetailGridControlShow(witchingList);
            }
        }

        private void gridViewDetail_FocusedRowChanged(object sender, FocusedRowChangedEventArgs e)
        {
            /**
            Cell cell = null;
            TDCell tdCell = null;
            string sLACDate = gridViewDetail.GetFocusedRowCellDisplayText(gcTargetLAC);
            string sCIDate = gridViewDetail.GetFocusedRowCellDisplayText(gcTargetCI);
            if (sLACDate == "" || sCIDate == "")
            {
                return;
            }
            if (detailInfSave != null)
            {
                foreach (JoinHandoverAnalysis jhAnalysis in detailInfSave)
                {
                    if (jhAnalysis.ITargetLac == int.Parse(sLACDate) &&
                        jhAnalysis.ITargetCi == int.Parse(sCIDate.ToString()))
                    {
                        mainModel.HandoverAnalysisList.Clear();
                        mainModel.HandoverAnalysisList.Add(jhAnalysis);
                        string date = jhAnalysis.IMon.ToString().Substring(0, 4) +
                            "-" + jhAnalysis.IMon.ToString().Substring(4, 2);
                        DateTime dateTime = Convert.ToDateTime(date);
                        if (IsTDAnalysis)
                        {
                            tdCell = mainModel.CellManager.GetTDCell(dateTime, jhAnalysis.ILac, jhAnalysis.ICi);
                        }
                        else
                        {
                            cell = mainModel.CellManager.GetCell(dateTime, (ushort)jhAnalysis.ILac, (ushort)jhAnalysis.ICi);
                        }
                        if ((!IsTDAnalysis) && cell != null)
                        {
                            mainModel.MainForm.GetMapForm().GoToView(cell.EndPointLongitude, cell.EndPointLatitude);
                        }
                        if (IsTDAnalysis && tdCell != null)
                        {
                            mainModel.MainForm.GetMapForm().GoToView(tdCell.EndPointLongitude, tdCell.EndPointLatitude);
                        }
                        System.Threading.Thread.Sleep(0);
                    }
                }
            }
            *///
        }

        private void 显示所有ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            string lac_ci = gvMain.GetFocusedRowCellDisplayText(gcLAC_CI);//
            if (!allDetailInfs.ContainsKey(lac_ci))
            {
                return;
            }
            if (ShowEleDetail != null)
            {
                ShowEleDetail(lac_ci);
            }
        }
        public delegate void ShowElementDetailInf(string lac_ci);
        public ShowElementDetailInf ShowEleDetail { get; set; }

        private void gvMain_Click(object sender, EventArgs e)
        {
            gvMain_FocusedRowChanged(null, null);
        }

        private void gridViewDetail_Click(object sender, EventArgs e)
        {
            Cell cell = null;
            TDCell tdCell = null;
            string sLACDate = gridViewDetail.GetFocusedRowCellDisplayText(gcTargetLAC);
            string sCIDate = gridViewDetail.GetFocusedRowCellDisplayText(gcTargetCI);
            if (sLACDate == "" || sCIDate == "")
            {
                return;
            }
            if (detailInfSave != null)
            {
                foreach (JoinHandoverAnalysis jhAnalysis in detailInfSave)
                {
                    if (jhAnalysis.ITargetLac == int.Parse(sLACDate) &&
                        jhAnalysis.ITargetCi == int.Parse(sCIDate.ToString()))
                    {
                        mainModel.HandoverAnalysisList.Clear();
                        mainModel.HandoverAnalysisList.Add(jhAnalysis);
                        string date = jhAnalysis.IMon.ToString().Substring(0, 4) +
                            "-" + jhAnalysis.IMon.ToString().Substring(4, 2);
                        gotoView(ref cell, ref tdCell, jhAnalysis.ILac, jhAnalysis.ICi, date);
                    }
                }
            }
        }

        private void gotoView(ref Cell cell, ref TDCell tdCell, int lac, int ci, string date)
        {
            DateTime dateTime = Convert.ToDateTime(date);
            if (IsTDAnalysis)
            {
                tdCell = mainModel.CellManager.GetTDCell(dateTime, lac, ci);
            }
            else
            {
                cell = mainModel.CellManager.GetCell(dateTime, (ushort)lac, (ushort)ci);
            }
            if ((!IsTDAnalysis) && cell != null)
            {
                mainModel.MainForm.GetMapForm().GoToView(cell.EndPointLongitude, cell.EndPointLatitude);
            }
            if (IsTDAnalysis && tdCell != null)
            {
                mainModel.MainForm.GetMapForm().GoToView(tdCell.EndPointLongitude, tdCell.EndPointLatitude);
            }
            System.Threading.Thread.Sleep(0);
        }

        private DataTable tableSave;
        private void 导出数据ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //设置需要保存的表结构
            IniTableSaveStructure();

            //设置保存表的数据
            WaitBox.Show(IniTableSaveData);
            ExcelNPOIManager.ExportToExcel(tableSave);
        }

        private void IniTableSaveStructure()
        {
            tableSave = new DataTable();
            tableSave.Columns.Add("轮次");
            tableSave.Columns.Add("源LAC");
            tableSave.Columns.Add("源CI");

            tableSave.Columns.Add("路测切换总次数");
            tableSave.Columns.Add("网络侧切换成功次数");
            tableSave.Columns.Add("网络侧切换总次数");
            tableSave.Columns.Add("网络侧切换成功率");


            tableSave.Columns.Add("目标LAC");
            tableSave.Columns.Add("目标CI");
            tableSave.Columns.Add("路测切换次数");
            if (IsTDAnalysis)
            {
                tableSave.Columns.Add("网络切换类型");
            }
            tableSave.Columns.Add("源小区切出占比");
            tableSave.Columns.Add("目标小区切出占比");
            tableSave.Columns.Add("性能侧切换请求次数");
            tableSave.Columns.Add("性能侧切换成功次数");
            tableSave.Columns.Add("性能侧切换成功率");
            tableSave.Columns.Add("问题级别");
            tableSave.Columns.Add("建议方案");
        }

        private void IniTableSaveData()
        {
            WaitBox.ProgressPercent = 25;
            if (tableSave != null && tableSave.Columns.Count != 0)
            {
                foreach (string keyInf in allDetailInfs.Keys)
                {
                    List<object> rowInf = new List<object>();
                    int MDTChangeCount = 0;
                    int changeSuccesscount = 0;
                    int changeSumCount = 0;
                    foreach (JoinHandoverAnalysis jhAnalysis in allDetailInfs[keyInf])
                    {
                        if (rowInf.Count == 0)
                        {
                            rowInf.Add(jhAnalysis.IMon);
                            rowInf.Add(jhAnalysis.ILac);
                            rowInf.Add(jhAnalysis.ICi);
                        }
                        MDTChangeCount += jhAnalysis.IHoNum;
                        changeSuccesscount += jhAnalysis.ISucc;
                        changeSumCount += jhAnalysis.IReq;
                    }
                    rowInf.Add(MDTChangeCount);
                    rowInf.Add(changeSuccesscount);
                    rowInf.Add(changeSumCount);
                    rowInf.Add((((double)(changeSuccesscount)) / ((double)(changeSumCount))).ToString("0.000"));
                    addJoinHandoverAnalysisData(keyInf, rowInf);
                }
            }
            WaitBox.ProgressPercent = 100;
            WaitBox.Close();
        }

        private void addJoinHandoverAnalysisData(string keyInf, List<object> rowInf)
        {
            foreach (JoinHandoverAnalysis jhAnalysis in allDetailInfs[keyInf])
            {
                List<object> rowInfSave = new List<object>(rowInf);
                rowInfSave.Add(jhAnalysis.ITargetLac);
                rowInfSave.Add(jhAnalysis.ITargetCi);

                if (IsTDAnalysis)
                {
                    if (((JoinHandoverAnalysisTD)jhAnalysis).INet == 1)
                    {
                        rowInfSave.Add("TD网内切换");
                    }
                    else
                    {
                        rowInfSave.Add("TD网间切换");
                    }
                }

                rowInfSave.Add(jhAnalysis.IHoNum);
                rowInfSave.Add((jhAnalysis.FoutRate * 100).ToString("0.0") + "%");
                rowInfSave.Add((jhAnalysis.FintRate * 100).ToString("0.0") + "%");
                rowInfSave.Add(jhAnalysis.IReq);
                rowInfSave.Add(jhAnalysis.ISucc);
                rowInfSave.Add(jhAnalysis.FSuccRate.ToString("0.000"));
                switch (jhAnalysis.ILevel)
                {
                    case 1:
                        rowInfSave.Add("正常小区");
                        break;
                    case 2:
                        rowInfSave.Add("冗余小区");
                        break;
                    case 3:
                        rowInfSave.Add("低质小区");
                        break;
                    case 4:
                        rowInfSave.Add("规避小区");
                        break;
                    default:
                        break;
                }
                rowInfSave.Add(jhAnalysis.Strcomment);
                tableSave.Rows.Add(rowInfSave.ToArray());
            }
        }

        private void toolStripMenuItem1_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridViewDetail);
        }
    }
}