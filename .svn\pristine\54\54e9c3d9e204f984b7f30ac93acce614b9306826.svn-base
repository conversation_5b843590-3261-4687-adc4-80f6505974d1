﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func.OwnSampleAnalyse;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.RAMS.GeneralFuncDef.OwnSampleAnalyse
{
    public class SampleAnalyserDef : BaseSubAnalyser
    {
        public CommonAnalyserCommander commander { get; set; }
        public SampleAnalyserDef()
        {
            this.name = "采样点分析";
        }
        public override string GetDescShow()
        {
            if(commander==null)
            {
                return "未设置";
            }
            else
            {
                return commander.desc;
            }
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["NodeType"] = 1;
                param["CommanderName"] = commander == null ? "" : commander.funcName;
                return param;
            }
        }
        private static Dictionary<string, CommonAnalyserCommander> commanderDic = null;
        internal static CommonAnalyserCommander GetSampleCommander(string commanderName)
        {
            if(commanderDic==null)
            {
                commanderDic = loadOptionSettings();
            }
            CommonAnalyserCommander cmder=null;
            commanderDic.TryGetValue(commanderName, out cmder);
            return cmder;
        }

        private static Dictionary<string, CommonAnalyserCommander> loadOptionSettings()
        {
            Dictionary<string, CommonAnalyserCommander> optionDic = new Dictionary<string, CommonAnalyserCommander>();
            try
            {
                XmlConfigFile configFile = new XmlConfigFile(string.Format(Application.StartupPath + "/config/samplefilter.xml"));
                List<Object> list = configFile.GetItemValue("SampleAnaCommanderOptions", "options") as List<Object>;
                if (list != null)
                {
                    foreach (object value in list)
                    {
                        CommonAnalyserCommander option = new CommonAnalyserCommander();
                        option.Param = value as Dictionary<string, object>;
                        optionDic[option.funcName] = option;
                    }
                }
            }
            catch
            {
                //continue
            }
            return optionDic;
        }
    }
}
