using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.Net.Sockets;
using System.IO;
using MasterCom.RAMS.Model;
using System.Windows.Forms;

namespace MasterCom.RAMS.Net
{
	/// <summary>
	/// 命令字
	/// </summary>
	public enum Command
	{
		/// <summary>
		/// 用户鉴权
		/// </summary>
		Authenticate = 1,
		/// <summary>
		/// 测试数据维护
		/// </summary>
		DataManage = 2,
		/// <summary>
		/// 小区配置管理
		/// </summary>
		CellConfigManage = 3,
		/// <summary>
		/// 数据库管理
		/// </summary>
		DBManage = 4,
		/// <summary>
		/// 用户子系统
		/// </summary>
		UserManage = 5,
		/// <summary>
		/// 信息查询
		/// </summary>
		InfoQuery = 6,
		/// <summary>
		/// 统计查询
		/// </summary>
		StatQuery = 7,
		/// <summary>
		/// PC信息查询
		/// </summary>
		PCInfoQuery = 8,
		/// <summary>
		/// CQT信息查询
		/// </summary>
		CQTInfoQuery = 9,
		///<summary>
		///覆盖管理
		///</summary>
		CoverageManage=11,
		/// <summary>
		/// DIY接口
		/// </summary>
		DIYSearch = 12,
		/// <summary>
		/// 事件属性设置
		/// </summary>
		EventPropertySetting = 13
	}

	/// <summary>
	/// 子命令
	/// </summary>
	public enum SubCommand
	{
		/// <summary>
		/// 请求
		/// </summary>
		Request = 0,
		/// <summary>
		/// 响应
		/// </summary>
		Response = 1
	}

	public static partial class RequestType
	{
		public static byte LoginCheckUser = 0x30;
		public static byte LoginCheckPassword = 0x31;
		public static byte LoginCheckUser_WYZJ = 0x32;
		public static byte LoginCheckPassword_WYZJ = 0x33;
		public static byte QueryRole = 0xa2;
		public static byte QueryRolePermission = 0xa3;
	}

	public static partial class ResponseType
	{
		public static int LoginRand = 0x70;
		public static int LoginResult = 0x71;
		public static int Role = 0xa2;
		public static int RolePermission = 0xa3;
	}

	public enum LoginType
	{
		Login = 1,
		Other = 2
	}

	/// <summary>
	/// 鉴权结果
	/// </summary>
	public enum ConnectResult
	{
		/// <summary>
		/// 鉴权成功
		/// </summary>
		Success = 0,
		/// <summary>
		/// 不允许连接
		/// </summary>
		Forbidden = 1,
		/// <summary>
		/// 未知用户名
		/// </summary>
		UserNotFound = 2,
		/// <summary>
		/// 密码错误
		/// </summary>
		WrongPassword = 3,
		/// <summary>
		/// 达到最大连接数
		/// </summary>
		ReachMaxConnectionCount = 4,
		/// <summary>
		/// 服务器注册失败
		/// </summary>
		RegisterFail = 5,
		/// <summary>
		/// 服务器无法连接数据库
		/// </summary>
        CannotConnectDB = 6,
        /// <summary>
        /// 客户端版本过低
        /// </summary>
        VersionLower = 7,
        /// <summary>
        /// 用户账号异常
        /// </summary>
        UserUnNormal = 8,
	}

	public static partial class DescriptionFactory
	{
		public static string getDescription(ConnectResult connectResult)
		{
			switch (connectResult)
			{
				case ConnectResult.Success:
				{
					return "鉴权成功";
				}
				case ConnectResult.Forbidden:
				{
					return "不允许连接";
				}
				case ConnectResult.UserNotFound:
				{
					return "错误的用户名或密码";
				}
				case ConnectResult.WrongPassword:
				{
					return "错误的用户名或密码";
				}
				case ConnectResult.ReachMaxConnectionCount:
				{
					return "达到最大连接数";
				}
				case ConnectResult.RegisterFail:
				{
					return "服务器注册失败";
				}
				case ConnectResult.CannotConnectDB:
				{
					return "服务器无法连接数据库";
                }
                case ConnectResult.VersionLower:
                {
                    return "版本过低，请更新";
                }
                case ConnectResult.UserUnNormal:
                {
                    return "账号状态异常:" + MainModel.GetInstance().User.UserStatusDes;
                }
				default:
				{
					return "未知原因";
				}
			}
		}
	}

	/// <summary>
	/// 包
	/// </summary>
	public class Package
	{
		public Command Command
		{
			get { return command; }
			set { command = value; }
		}

		public SubCommand SubCommand
		{
			get { return subCommand; }
			set { subCommand = value; }
		}

		public Content Content
		{
			get { return content; }
			set { if (value != null) content = value; }
		}

		internal void Send(BinaryWriter writer)
		{
            if (Length > 8000)
            {
                throw (new Exception(string.Format("待发送的数据包长度为{0}，超过了限定长度，请咨询管理员。", Length)));
            }
			writer.Write(header);
			writer.Write(IPAddress.HostToNetworkOrder((short)this.Length));
			writer.Write((byte)this.command);
			writer.Write((byte)this.subCommand);
			this.content.Write(writer);
		}

		internal void Recieve(BinaryReader reader)//接收读取的字节
		{
			byte readByte = 0;
			readByte = reader.ReadByte();
			//Assert(readByte == Header1);
			readByte = reader.ReadByte();
			//assert(readByte == Header2);
			ushort len=reader.ReadUInt16();
			short sLen=(short)len;
			ushort length = (ushort)IPAddress.NetworkToHostOrder(sLen);
			reader.ReadByte();
			length--;
			reader.ReadByte();
			length--;
			content.Read(reader, length);
		}
		internal byte RecieveMainCmd(BinaryReader reader)
		{
			byte readByte = 0;
			readByte = reader.ReadByte();
			//Assert(readByte == Header1);
			readByte = reader.ReadByte();
			//assert(readByte == Header2);
			ushort length = (ushort)IPAddress.NetworkToHostOrder((short)reader.ReadUInt16());
			byte cmd1 = reader.ReadByte();
			length--;
			content.Read(reader, length);
			return cmd1;
		}

		private ushort Length
		{
			get { return (ushort)(2 + content.Length); }
		}

		private const byte Header1 = 0xAA;

		private const byte Header2 = 0x55;

		private static readonly byte[] header = { Header1, Header2 };

		private Command command;

		private SubCommand subCommand;

		private Content content = new Content();
	}

	/// <summary>
	/// 内容
	/// </summary>
	public class Content
	{
		public byte Type
		{
			get { return type; }
			set { type = value; }
		}
		public byte[] Buff
		{
			get { return paramsBuffer; }
		}
		public int CurOffset
		{
			get { return getOffset; }
		}

		public void PrepareAddParam()
		{
			this.paramsBufferSize = 0;
			PrepareParamsBuffer(0);
		}

		public void AddParam(byte param)
		{
			this.PrepareParamsBuffer(1);
			this.paramsBuffer[this.paramsBufferSize] = param;
			this.paramsBufferSize++;
		}

		public void AddParam(short param)
		{
			this.PrepareParamsBuffer(2);
			BitConverter.GetBytes(IPAddress.HostToNetworkOrder(param)).CopyTo(this.paramsBuffer, this.paramsBufferSize);
			this.paramsBufferSize += 2;
		}

		public void AddParam(int param)
		{
			this.PrepareParamsBuffer(4);
			BitConverter.GetBytes(IPAddress.HostToNetworkOrder(param)).CopyTo(this.paramsBuffer, this.paramsBufferSize);
			this.paramsBufferSize += 4;
		}

		public void AddParam(float param)
		{
			AddParam((int)(param * 1000));
		}

		public void AddParam(double param)
		{
			AddParam((int)(param * 10000000));
		}

		public void AddParam(DateTime dt)
		{
			long time = MasterCom.Util.JavaDate.GetMilliseconds(dt);
			AddParam((int)(time / 1000L));
			AddParam((int)(time % 1000L));
		}

		public void AddParamDateTime8(DateTime dt)
		{
			AddParam(dt);
		}

		public void AddParamDateTime4(DateTime dt)
		{
			long time = MasterCom.Util.JavaDate.GetMilliseconds(dt);
			AddParam((int)(time / 1000L));
		}

		public void AddParam(String param)
		{
			if (param == null)
			{
				AddParam((short)0);
			}
			else
			{
				byte[] buffer = Encoding.Default.GetBytes(param);
				AddParam((short)buffer.Length);
				PrepareParamsBuffer(buffer.Length);
				buffer.CopyTo(paramsBuffer, paramsBufferSize);
				paramsBufferSize += buffer.Length;
			}
		}

		public void AddParam(byte[] buffer, int length)
		{
			PrepareParamsBuffer(buffer.Length);
			buffer.CopyTo(paramsBuffer, paramsBufferSize);
			paramsBufferSize += length;
		}

		public void PrepareGetParam()
		{
			this.getOffset = 0;
		}

        public int GetOddCntAfterIgnoreAnyParam(int gnore)
        {
            this.getOffset += gnore;
            return this.paramsBuffer.Length - this.getOffset;
        }

		public bool HasNextParam()
		{
			return this.getOffset < paramsBufferSize - 1;
		}

		public byte GetParamByte()
		{
			return this.paramsBuffer[this.getOffset++];
		}
		public byte[] GetParamBytes()
		{
			ushort length = GetParamUShort();
			byte[] retBytes = new byte[length];
			if (length > 0)
			{
				GetParamBytesNoConvert(retBytes, 0, length);
			}
			return retBytes;
		}
		public short GetParamShort()
		{
			short param = BitConverter.ToInt16(this.paramsBuffer, this.getOffset);
			this.getOffset += 2;
			return IPAddress.NetworkToHostOrder(param);
		}
        public short? TryGetParamShort()
        {
            if (this.paramsBuffer.Length >= this.getOffset + 2)
            {
                return GetParamShort();
            }
            return null;
        }
		public ushort GetParamUShort()
		{
			return (ushort)GetParamShort();
		}

		public int GetParamInt()
		{
			int param = BitConverter.ToInt32(this.paramsBuffer, this.getOffset);
			this.getOffset += 4;
			return IPAddress.NetworkToHostOrder(param);
		}
        public int? TryGetParamInt()
        {
            if (this.paramsBuffer.Length >= this.getOffset + 4)
            {
                return GetParamInt();
            }
            return null;
        }

		public UInt64 GetParamUInt64()
		{
			UInt64 param = BitConverter.ToUInt64(this.paramsBuffer, this.getOffset);
			this.getOffset += 8;
			return (UInt64)IPAddress.NetworkToHostOrder((long)param);
		}

		public Int64 GetParamInt64()
		{
			Int64 param = BitConverter.ToInt64(this.paramsBuffer, this.getOffset);
			this.getOffset += 8;
			return (Int64)IPAddress.NetworkToHostOrder((long)param);
		}

		public Int64 GetParamInt64NoTransfer()
		{
			Int64 param = BitConverter.ToInt64(this.paramsBuffer, this.getOffset);
			this.getOffset += 8;
			return param;
		}

		public void ResetOffset(int offset)
		{
			this.getOffset -= offset;
		}

		public DateTime GetParameDateTimeByLong()
		{ 
			return MasterCom.Util.JavaDate.GetDateTimeFromMilliseconds(GetParamInt() * 1000L + GetParamInt());
		}

		public DateTime GetParameDateTimeByInt()
		{
			return MasterCom.Util.JavaDate.GetDateTimeFromMilliseconds(GetParamInt() * 1000L);
		}
		public float GetParamKFloat()
		{
			return GetParamInt() / 1024.0f;
		}
		public float GetParamFloat()
		{
			int i=GetParamInt();
			return i / 1000.0f;
		}

		public double GetParamDouble()
		{
			return GetParamInt() / 10000000.0d;
		}

		public string GetParamString()
		{
			short length = this.GetParamShort();
			string param = Encoding.Default.GetString(this.paramsBuffer, this.getOffset, length);
			this.getOffset += length;
			return param;
		}
        public string TryGetParamString()
        {
            short? length = this.TryGetParamShort();
            if (length != null && this.paramsBuffer.Length >= this.getOffset + (short)length)
            {
                string param = Encoding.Default.GetString(this.paramsBuffer, this.getOffset, (short)length);
                this.getOffset += (short)length;
                return param;
            }
            return null;
        }
		public bool BCanGetString()
		{
			if (this.paramsBuffer.Length < this.getOffset + 2)
				return false;
			short param = BitConverter.ToInt16(this.paramsBuffer, this.getOffset);
			short length = IPAddress.NetworkToHostOrder(param);
            return this.paramsBuffer.Length >= this.getOffset + 2 + length;
		}

		Boolean isValidHexNum(byte ch1)
		{
			if(( ch1 >= '0' && ch1 <= '9')  ||
			   ( ch1 >= 'a' && ch1 <= 'f')  || 
			   ( ch1 >= 'A' && ch1 <= 'F')
			  )
			{
				return true;
			}
			return false;
		}

		byte toHex(byte ch1)
		{
			if (ch1 >= '0' && ch1 <= '9')
			{
				return (byte)(ch1 - '0');
			}
			else if (ch1 >= 'a' && ch1 <= 'f')
			{
				return (byte)(ch1 - 'a' + 10);
			}
			else if (ch1 >= 'A' && ch1 <= 'F')
			{
				return (byte)(ch1 - 'A' + 10);
			}
			else
			{
				return 0;
			}
		}

		int atox(byte[] toHexChar, byte[] fromString)
		{
			int nIndex = 0;
			for (int i=0; i<fromString.Length; i++)
			{
				byte ch1 = 0, ch2 = 0;
				ch1 = fromString[i];
				while (!isValidHexNum(ch1) && (i < fromString.Length))
				{
					if (i == (fromString.Length - 1))
					{
						return nIndex;
					}

					i++;
					ch1 = fromString[i];
				}

				i++;
				if (i == (fromString.Length))
				{
					return nIndex;
				}

				ch2 = fromString[i];
				while (!isValidHexNum(ch2) && (i < fromString.Length))
				{
					if (i == (fromString.Length- 1))
					{
						return nIndex;
					}

					i++;
					ch2 = fromString[i];
				}
				toHexChar[nIndex++] = (byte)(toHex(ch1) * 16 + toHex(ch2));
			}
			return nIndex;
		}
		public void GetParamBytesNoConvert(byte[] dest, int offset, int length)
		{
			Array.Copy(this.paramsBuffer, this.getOffset, dest, offset, length);
			this.getOffset += length;
		}
		public void GetParamBytes(byte[] dest, int offset, int length)
		{
			byte[] tmp = new byte[length];
			Array.Copy(this.paramsBuffer, this.getOffset, tmp, offset, length);
			atox(dest, tmp);
			this.getOffset += length;
		}

		internal void Write(BinaryWriter writer)
		{
			writer.Write(this.type);
			if (paramsBufferSize > 0)
			{
				writer.Write(this.paramsBuffer, 0, this.paramsBufferSize);
			}
		}

		internal void Read(BinaryReader reader, int length)
		{
			this.type = reader.ReadByte();
			length--;
			if (length > 0)
			{
				this.paramsBuffer = reader.ReadBytes(length);
				this.paramsBufferSize = this.paramsBuffer.Length;
			}
		}

		internal void CopyBuffFrom(byte[] arrByte)
		{
			this.paramsBuffer = arrByte;
			this.paramsBufferSize = this.paramsBuffer.Length;
		}

		internal ushort Length
		{
			get
			{
				return (ushort)(1 + this.paramsBufferSize);
			}
		}

		private void PrepareParamsBuffer(int addLength)
		{
			if (this.paramsBuffer == null)
			{
				this.paramsBuffer = new byte[(addLength / bufferAddSize + 1) * bufferAddSize];
			}
			else if (this.paramsBuffer.Length - this.paramsBufferSize < addLength)
			{
				byte[] tempBuffer = new byte[((this.paramsBufferSize + addLength) / bufferAddSize + 1) * bufferAddSize];
				this.paramsBuffer.CopyTo(tempBuffer, 0);
				this.paramsBuffer = tempBuffer;
			}
		}

		private static int bufferAddSize = 1024;

		private byte type;

		private byte[] paramsBuffer;

		private int paramsBufferSize;

		private int getOffset;

		public string GetBuffBase64Str()
		{
			return System.Convert.ToBase64String(this.paramsBuffer);
		}

		public static Content Create(byte type, string base64Str)
		{
			Content c = new Content();
			c.type = type;
			c.paramsBuffer = System.Convert.FromBase64String(base64Str);
			c.paramsBufferSize = c.paramsBuffer.Length - 1;
			return c;
		}
	}

	public class ClientProxy
	{
		public static void SetLoginStatus(bool logining)
		{
			isLogining = logining;
		}
		private static bool isLogining = false;
		public static bool IsLogining
		{
			get { return isLogining; }
		}

        public ClientProxy()
        {
            //根据不同的IP类型初始化不同的TcpClient
            if (MainModel.GetInstance().IPTypeInfo == IPType.IPV6)
            {
                client = new TcpClient(AddressFamily.InterNetworkV6);
            }

            client.ReceiveTimeout = 180000;
            client.SendTimeout = 180000;
            connectedTimeout = 180000;
        }

        public ClientProxy(string ip, int port)
			: this()
		{
			this.ip = ip;
			this.port = port;
		}

		public ClientProxy(string ip, int port, string userName, string password, int dbID)
			: this(ip, port)
		{
			this.userName = userName;
			this.password = password;
			this.dbID = dbID;
		}

		public ConnectResult connect()
		{
			bool versionValid;
            return connectAutoCheck(false, null, ref dbID, out versionValid, null);
		}
		public Dictionary<int,bool> requestUserCitys(string userName)
		{
			Dictionary<int, bool> dicRet = new Dictionary<int, bool>();
			try
			{
                connectWithRetry();
                if (isConnected)
				{
					this.stream = client.GetStream();
					this.writer = new BinaryWriter(stream);
					this.reader = new BinaryReader(stream);
					this.package.Command = Command.CellConfigManage;
					this.package.SubCommand = SubCommand.Request;
					this.package.Content.Type = 0xa4;
					this.package.Content.PrepareAddParam();
					this.package.Content.AddParam(userName);
					this.Send();
					while (true)
					{
						this.Recieve();
						package.Content.PrepareGetParam();
						if (package.Content.Type == 0xa4)
						{
							int cid = this.package.Content.GetParamInt();
							dicRet[cid] = true;
						}
						else if (package.Content.Type == ResponseType.END)
						{
							break;
						}
					}
					return dicRet;
				}
				else
				{
					return dicRet;
				}

			}
			catch
			{
				return dicRet;
			}
		}

		public bool RequestVersionID(out int versionID)
		{
			versionID = -1;
			try
			{
                connectWithRetry();
                if (isConnected)
				{
					this.stream = client.GetStream();
					this.writer = new BinaryWriter(stream);
					this.reader = new BinaryReader(stream);
					this.package.Command = Command.CellConfigManage;
					this.package.SubCommand = SubCommand.Request;
					this.package.Content.Type = 0xa0;
					this.package.Content.PrepareAddParam();
					this.package.Content.AddParam(0);
					this.Send();
					this.Recieve();
					this.package.Content.PrepareGetParam();
					int verId = this.package.Content.GetParamInt();
					MainModel.GetInstance().LatestVersionID = verId;
					return true;
				}
			}
			catch
			{
                //continue
			}
			return false;
		}

		public List<UpdateNote> requestUpdateDescList(int curVerId)
		{
			try
			{
                connectWithRetry();
                if (isConnected)
				{
					this.stream = client.GetStream();
					this.writer = new BinaryWriter(stream);
					this.reader = new BinaryReader(stream);
					this.package.Command = Command.CellConfigManage;
					this.package.SubCommand = SubCommand.Request;
					this.package.Content.Type = 0xa1;
					this.package.Content.PrepareAddParam();
					this.package.Content.AddParam((int)curVerId);
					this.Send();
					List<UpdateNote> retList = new List<UpdateNote>();
					while (true)
					{
						this.Recieve();
						package.Content.PrepareGetParam();
						if (package.Content.Type == 0xa1)
						{
							UpdateNote note = new UpdateNote();
							note.Fill(package.Content);
							retList.Add(note);
						}
						else if (package.Content.Type == ResponseType.END)
						{
							break;
						}
					}
					return retList;
				}
				else
				{
					return null;
				}

			}
			catch
			{
				return null;
			}

		}
#if MaxTryCount2
		int Max_TRY_Check = 2;
#else
		int Max_TRY_Check = 1;
#endif
		/// <summary>
		/// 设置连接超时
		/// </summary>
		/// <param name="receiveTimeout">发送超时值（以毫秒为单位）。默认值为 0。</param>
		/// <param name="sendTimeout">连接的超时值（以毫秒为单位）。默认值为 0。</param>
		public void setTimeout(int receiveTimeout, int sendTimeout)
        {
            client.SendTimeout = sendTimeout;
            client.ReceiveTimeout = receiveTimeout;
		}
        private bool isNeedConnectToOtherServer(ConnectResult connectResult)
        {
            if (connectResult == ConnectResult.ReachMaxConnectionCount ||
                connectResult == ConnectResult.RegisterFail)
            {
                return true;
            }
            return false;
        }
        private ConnectResult connectToOtherServer(Server newServer, bool isCheckMaxConnect, int curDbid,
            bool login, string version, ref int dbId, out bool versionValid, List<int> permissions)
        {
            int lastSendTimeout = client.SendTimeout;
            int lastReceiveTimeout = client.ReceiveTimeout;
            this.client.Close();

            if (newServer.IP.Contains(":"))
            {
                this.client = new TcpClient(AddressFamily.InterNetworkV6);
            }
            else
            {
                this.client = new TcpClient();
            }
            this.client.SendTimeout = lastSendTimeout;
            this.client.ReceiveTimeout = lastReceiveTimeout;
            this.ip = newServer.IP;
            this.port = newServer.Port;
            this.dbID = curDbid;

            MainModel model = MainModel.GetInstance();
            model.Server = newServer;
            model.MainForm.SetServerIpLable();

            return connect(login, version, ref dbId, out versionValid, permissions, isCheckMaxConnect);
        }

        private ComparerServer comparerCurConnectCount = new ComparerServer();
        private class ComparerServer : IComparer<Server>
        {
            public int Compare(Server x, Server y)
            {
                return x.CurConnectCount - y.CurConnectCount;
            }
        }
        public ConnectResult connectAutoCheck(bool login, string version, ref int dbId
            , out bool versionValid, List<int> permissions)
        {
            int curDbid = dbId;
            this.result = connect(login, version, ref dbId, out versionValid, permissions, true);

#if SeverLimitMaxConnect

            if (isNeedConnectToOtherServer(result))
            {
                MainModel model = MainModel.GetInstance();
                if (model.ServerOfUsableList.Count > 0)
                {
                    int lastServerConnectCont = model.Server.CurConnectCount;
                    string lastServerToken = model.Server.Token;
                    string lastServerNetType = model.Server.ServerType;

                    List<Server> serverList = new List<Server>();
                    foreach (Server serverInfo in model.ServerOfUsableList)
                    {
                        if (serverInfo.ServerType != lastServerNetType)
                        {
                            continue;
                        }

                        serverList.Add(serverInfo);
                        if (serverInfo.Token == lastServerToken)
                        {
                            serverInfo.CurConnectCount = lastServerConnectCont;
                            continue;
                        }

                        result = connectToOtherServer(serverInfo, true, curDbid, login, version, ref dbId, out versionValid, permissions);

                        if (!(isNeedConnectToOtherServer(result)))
                        {
                            return result;
                        }
                    }

                    if (serverList.Count > 0)
                    {
                        serverList.Sort(comparerCurConnectCount);
                        result = connectToOtherServer(serverList[0], false, curDbid, login, version, ref dbId
                            , out versionValid, permissions);
                    }
                }

                if (result == ConnectResult.ReachMaxConnectionCount)
                {
                    MessageBox.Show("服务端达到最大连接限制，请稍后再试或联系管理员！");
                }
            }
#endif
            return result;
        }

        #region 连接超时
        private bool isConnected;
        private int connectedTimeout;
        public void SetConnectTimeout(int timeout)
        {
            connectedTimeout = timeout;
        }

        private void connectWithRetry()
        {
            //方案3:异步回调
            client.BeginConnect(ip, port,new AsyncCallback(CallBackMethod), client);
			isConnected = TimeoutObject.WaitOne(connectedTimeout);

            ////方案2:BeginConnect+WaitOne超时不生效
            //IAsyncResult ar = client.BeginConnect(ip,port,null,null);
            //bool success = ar.AsyncWaitHandle.WaitOne(10000);
            //if (success)
            //{
            //    isConnected = true;
            //}

            ////方案1:类似timer,超时时间会大于设置的时间
            //int time = 0;
            //int timeoutSecond = 180;
            //while (time++ < timeoutSecond)
            //{
            //    try
            //    {
            //        client.Connect(ip, port);
            //        isConnected = client.Connected;
            //        if (isConnected)
            //        {
            //            break;
            //        }
            //    }
            //    catch
            //    {
            //        System.Threading.Thread.Sleep(1000);
            //    }
            //}
        }

        private readonly System.Threading.ManualResetEvent TimeoutObject = new System.Threading.ManualResetEvent(false);
        private void CallBackMethod(IAsyncResult asyncresult)
        {
            try
            {
                //isConnected = false;
                TcpClient tcpclient = asyncresult.AsyncState as TcpClient;

                if (tcpclient.Client != null && tcpclient.Connected)
                {
                    tcpclient.EndConnect(asyncresult);
                    //isConnected = true;
                    TimeoutObject.Set();
                }
            }
            catch
            {
                //isConnected = false;
            }
        }
        #endregion

        public ConnectResult connect(bool login, string version, ref int dbId, out bool versionValid
            , List<int> permissions, bool isCheckMaxConnect)
		{
            ConnectResult connectResult = ConnectResult.RegisterFail;
            User user = MainModel.GetInstance().User;
			versionValid = false;
			try
			{
				if(login)
                {
                    user.UserStatus = (int)EnumUserStatus.None;
                    user.UserStatusDes = "未知";
					masterMD5Check = true;//初始默认采用MD5鉴权
				}
				if (isLogining)
				{//加载完地图前，一直进行Connect，直到成功为止。
					int maxTryTimes = 3;
					int curTimes = 0;
					while (!isConnected)
					{
                        connectWithRetry();
                        curTimes++;
						if (curTimes >= maxTryTimes)
						{
                            break;
                            //string txt = string.Format("已请求连接服务器“{0}:{1}” {2} 次，仍无法连通。\r\n是否继续连接？是：继续等待连接；否：退出系统。"
                            //    , ip, port, curTimes);
                            //txt = txt + Environment.NewLine + "(若通过VPN或其它非局域网形式连接服务器，建议先退出系统，重新登录VPN或检查网络后再登录系统。)";
                            //DialogResult dlgResult = MessageBox.Show(txt, "问题", MessageBoxButtons.YesNo
                            //                                  , MessageBoxIcon.Question, MessageBoxDefaultButton.Button2);
                            //if (dlgResult == DialogResult.Yes)
                            //{
                            //    curTimes = 0;
                            //}
                            //else
                            //{
                            //    Environment.Exit(0);
                            //}
						}
					}
				}
				else
				{
                    connectWithRetry();
                }
                if(isConnected)
				{
					this.stream = client.GetStream();
					this.writer = new BinaryWriter(stream);
					this.reader = new BinaryReader(stream);
					for (int TRY = 0; TRY < Max_TRY_Check; TRY++)
					{
						// The first send user name;
						this.package.Command = Command.Authenticate;
						this.package.SubCommand = SubCommand.Request;
						this.package.Content.Type = masterMD5Check?RequestType.LoginCheckUser:RequestType.LoginCheckUser_WYZJ;
						this.package.Content.PrepareAddParam();
						this.package.Content.AddParam(login ? (byte)LoginType.Login : (byte)LoginType.Other);
						this.package.Content.AddParam(this.userName);
						this.package.Content.AddParam(dbID);
                        this.package.Content.AddParam(MainModel.GetInstance().VersionID);
						this.Send();
						// The first recieve package
						this.Recieve();
						if (this.package.Content.Type == ResponseType.LoginRand)
						{
							this.package.Content.PrepareGetParam();
							string randomString = this.package.Content.GetParamString();
							string returnVersion = this.package.Content.GetParamString();
							dbID = this.package.Content.GetParamInt();
							//打开权限控制开关，登录时，需要获取对应的userid
							if (login)
							{
								user.ID = package.Content.GetParamInt();
							}
							if (login && returnVersion.Equals(version))
							{
								versionValid = true;
							}

							string passwordMD5 = "";
							passwordMD5 = masterMD5Check ? getMD5String(Encoding.Default.GetBytes(this.password)) 
								: EncryptByDelphi("uniware"+this.password,(UInt16)userId);
							passwordMD5 = getMD5String(Encoding.Default.GetBytes(passwordMD5 + randomString));
							// The seconde send password
							this.package.Command = Command.Authenticate;
							this.package.SubCommand = SubCommand.Request;
							this.package.Content.Type = masterMD5Check?RequestType.LoginCheckPassword:RequestType.LoginCheckPassword_WYZJ;
							this.package.Content.PrepareAddParam();
							this.package.Content.AddParam(passwordMD5);
							this.Send();
							// The second receive result
							this.Recieve();
							this.package.Content.PrepareGetParam();
							connectResult = (ConnectResult)this.package.Content.GetParamByte();
							if(login)//初次登陆
							{
								if (connectResult == ConnectResult.Success && permissions != null)//鉴权成功
								{
									permissions.Clear();
									int count = this.package.Content.GetParamShort();
									for (int i = 0; i < count; i++)
									{
										permissions.Add(this.package.Content.GetParamInt());
                                    }

                                    int? curConnectCount = this.package.Content.TryGetParamInt();

                                    #region 登陆成功后尝试获取用户状态
                                    int? status = this.package.Content.TryGetParamInt();
                                    string statusDes = this.package.Content.TryGetParamString();
                                    if (status != null && !string.IsNullOrEmpty(statusDes))
                                    {
                                        user.UserStatus = (int)status;
                                        user.UserStatusDes = statusDes;
                                    }
                                    else
                                    {
                                        user.UserStatus = (int)EnumUserStatus.Normal;
                                        user.UserStatusDes = "正常";
                                    }
                                    #endregion

                                    break;
								}
                                else if (connectResult == ConnectResult.UserUnNormal)
                                {
                                    user.UserStatus = this.package.Content.GetParamInt();
                                    user.UserStatusDes = this.package.Content.GetParamString();
                                    return ConnectResult.UserUnNormal;
                                }
                                else//密码错误
                                {
                                    masterMD5Check = !masterMD5Check;
                                    break;
                                }
							}
							else//后续操作鉴权
							{
								if (connectResult == ConnectResult.Success)
                                {
#if SeverLimitMaxConnect
                                    try
                                    {
                                        if (isCheckMaxConnect)
                                        {
                                            int countPermissions = this.package.Content.GetParamShort();
                                            int countOdd = this.package.Content.GetOddCntAfterIgnoreAnyParam(4 * countPermissions);
                                            if (countOdd >= 4)
                                            {
                                                Server curServer = MainModel.GetInstance().Server;
                                                curServer.CurConnectCount = this.package.Content.GetParamInt();
                                                if (curServer.IsConnectReachMax())
                                                {
                                                    connectResult = ConnectResult.ReachMaxConnectionCount;
                                                }
                                            }
                                        }
                                    }
                                    catch
                                    { 
                                    }
#endif
									break;
								}
							}
						}
						else if (this.package.Content.Type == ResponseType.LoginResult)//错误的用户名
						{
							this.package.Content.PrepareGetParam();
							connectResult = (ConnectResult)this.package.Content.GetParamByte();
							masterMD5Check = !masterMD5Check;
						}
					}
				}
			}
			catch (Exception e)
			{
                MessageBox.Show("连接服务端异常！" + Environment.NewLine + e.ToString());
            }
			dbId = this.dbID;
			this.result = connectResult;
			return connectResult;
		}

		private ConnectResult result = ConnectResult.RegisterFail;
		public ConnectResult ConnectResult
		{
			get { return result; }
		}

		public ConnectResult connect(string userName, string password, int dbID)
		{
			this.userName = userName;
			this.password = password;
			this.dbID = dbID;
			return connect();
		}

		public ConnectResult connect(string userName, string password, ref int dbID, bool login, string version, out bool versionValid, List<int> permissions)
		{
			this.userName = userName;
			this.password = password;
			this.dbID = dbID;
            return connectAutoCheck(login, version, ref dbID, out versionValid, permissions);
		}

		public ConnectResult connect(string ip, int port, string userName, string password, int dbID)
		{
			this.ip = ip;
			this.port = port;
			connectedbID = dbID;
			return connect(userName, password, dbID);
		}

		public ConnectResult connect(string ip, int port, string userName, string password, ref int dbID, bool login, string version, out bool versionValid, List<int> permissions)
		{
			this.ip = ip;
			this.port = port;
			return connect(userName, password, ref dbID, login, version, out versionValid, permissions);
		}

        public void RequestUsableServers()
        {
            try
            {
                MainModel model = MainModel.GetInstance();
                List<Server> serverList = new List<Server>();

                this.package.Command = Command.CellConfigManage;
                this.package.SubCommand = SubCommand.Request;
                this.package.Content.Type = 0xa5;
                this.package.Content.PrepareAddParam();
                this.Send();
                while (true)
                {
                    this.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == 0xa5)
                    {
                        Server serverInfo = new Server();
                        serverInfo.Fill(package.Content);

                        serverList.Add(serverInfo);
                        if (serverInfo.Token == model.Server.Token)
                        {
                            model.Server.MaxConnectCount = serverInfo.MaxConnectCount;
                        }

                        foreach (Server serverDiy in model.Servers)
                        {
                            if (serverInfo.Token == serverDiy.Token)
                            {
                                serverDiy.MaxConnectCount = serverInfo.MaxConnectCount;
                                serverDiy.ServerType = serverInfo.ServerType;

                                serverInfo.TestPageUrl = serverDiy.TestPageUrl;
                                break;
                            }
                        }
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        break;
                    }
                }

                if (serverList.Count > 0)
                {
                    model.ServerOfUsableList = serverList;
                }
            }
            catch
            {
            }
        }

		public void Close()
		{
			this.client.Close();
			if (this.stream != null)
			{
				this.stream.Close();
				this.stream = null;
			}
			if (this.reader != null)
			{
				this.reader.Close();
				this.reader = null;
			}
			if (this.writer != null)
			{
				this.writer.Close();
				this.writer = null;
			}
		}

		public void Send()
		{
			this.package.Send(writer);
			writer.Flush();
		}

		public void Recieve()
		{
			this.package.Recieve(reader);
        }

		public Package Package
		{
			get { return package; }
			set { if (value != null) this.package = value; }
		}

		public static string getMD5String(byte[] message)
		{
			System.Security.Cryptography.MD5 md5 = System.Security.Cryptography.MD5.Create();
			byte[] digest = md5.ComputeHash(message);
			byte[] complexMatrix = { 1, 7, 0, 6, 1, 0, 0, 3, 8, 6, 0, 0, 4, 5, 0, 0 };
			StringBuilder md5String = new StringBuilder();
			for (int i = 0; i < 16; i++)
			{
				digest[i] += complexMatrix[i];
				md5String.AppendFormat("{0:x2}", digest[i]);
			}
			return md5String.ToString();
		}

		private string EncryptByDelphi(String S, UInt16 Key)
		{
			ushort C1 = 52845; //字符串加密算法的公匙 
			ushort C2 = 22719; //字符串加密算法的公匙 

			char[] Value = new char[S.Length];
			Byte[] charArray = new Byte[S.Length];

			for (int c = 0; c < charArray.Length; c++)
			{
				Value[c] = Convert.ToChar(Convert.ToByte(S[c]) ^ (Key >> 8));
				Key = (ushort)((Convert.ToByte(Value[c]) + Key) * C1 + C2);
			}
			int j = 0;
			StringBuilder sb = new StringBuilder();
			for (int k = 0; k < charArray.Length; k++)
			{
				j = (int)Value[k];
				sb.Append(((decimal)(65 + (decimal)j / 26)).ToString("#.#############") + ((decimal)(65 + j % 26)).ToString("#.#############"));
			}
			return sb.ToString();
		}

		private string ip;

		private int port;

		private string userName;

		private string password;

		private static bool masterMD5Check = true;

		private static int userId = 0;

		private int dbID;
		private int connectedbID;

		public int DbID
		{
			get {
                return connectedbID;
			}
		}

		private TcpClient client = new TcpClient();

		private NetworkStream stream;

		private BinaryWriter writer;

		private BinaryReader reader;

		private Package package = new Package();
	}

    public enum IPType
    { 
        IPV4,
        IPV6
    }
     
}
