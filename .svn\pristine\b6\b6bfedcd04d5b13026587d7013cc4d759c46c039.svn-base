using System;
using System.Collections.Generic;
using System.Text;

using System.Drawing;
using System.Drawing.Drawing2D;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func.AssistLayer
{
    class LineElementEx :BaseElementEx
    {
        private readonly DbPoint pointA;
        private readonly DbPoint pointB;

        public LineElementEx(DbPoint pointA, DbPoint pointB)
        {
            this.pointA = pointA;
            this.pointB = pointB;
        }

        public override GraphicsPath GraphicsPath
        {
            get
            {
                this.graphicsPath = new GraphicsPath();
                PointF pf,pf2;
                this.GisAdapter.ToDisplay(pointA, out pf);
                this.GisAdapter.ToDisplay(pointB, out pf2);
                graphicsPath.AddLine(pf, pf2);
                return graphicsPath;
            }
            set
            {
                this.graphicsPath = value;
            }
        }
    }
}
