﻿using DevExpress.XtraEditors.Controls;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Stat
{
    public partial class ProjectExtendConfigDlg : BaseDialog
    {
        public ProjectExtendConfigDlg()
        {
            InitializeComponent();
            initAreaTreeView();
            initChkListBox();
        }

        private StringBuilder sqlStrBuilder = new StringBuilder();

        private void initAreaTreeView()
        {
            foreach(CategoryEnumItem item in ((CategoryEnum)MainModel.CategoryManager["AreaType"]).Items)
            {
                List<CategoryEnumItem> projectList = MainModel.ProjectManager.GetProjectsByAreaType(item.ID);
                if(projectList.Count>0)
                {
                    TreeNode areaNode = new TreeNode(item.Description);
                    areaNode.Tag = item;
                    foreach(CategoryEnumItem projectItem in projectList)
                    {
                        TreeNode proNode = new TreeNode(projectItem.Description);
                        proNode.Tag = projectItem;
                        areaNode.Nodes.Add(proNode);
                    }
                    treeViewArea.Nodes.Add(areaNode);
                }
            }
        }

        private void initChkListBox()
        {
            foreach(CategoryEnumItem proItem in ((CategoryEnum)MainModel.CategoryManager["Project"]).Items)
            {
                CheckedListBoxItem boxItem = new CheckedListBoxItem(proItem);
                chkListBox.Items.Add(boxItem);
            }
        }

        private void treeViewArea_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node.Parent != null)
            {
                treeViewArea.SelectedNode = e.Node.Parent;
            }
            chkListBox.Enabled = true;
            txtNowArea.Text = (treeViewArea.SelectedNode.Tag as CategoryEnumItem).Description;
            chkListBox.UnCheckAll();
            foreach(TreeNode node in treeViewArea.SelectedNode.Nodes)
            {
                foreach (CheckedListBoxItem boxItem in chkListBox.Items)
                {
                    if(boxItem.Value == node.Tag)
                    {
                        boxItem.CheckState = CheckState.Checked;
                        break;
                    }
                }
            }
        }

        private void btnDeleteArea_Click(object sender, EventArgs e)
        {
            TreeNode node = treeViewArea.SelectedNode;
            if (node == null)
            {
                return;
            }
            sqlStrBuilder.Append(string.Format("delete from tb_cfg_project_extend where isubid={0};"
                , (node.Tag as CategoryEnumItem).ID));
            node.Remove();

            treeViewArea.SelectedNode = null;
            txtNowArea.Text = "";
            chkListBox.UnCheckAll();
            chkListBox.Enabled = false;
        }

        private void excuteSql()
        {
            if (sqlStrBuilder.Length <= 0)
            {
                return;
            }
            string sqlCommandStr = sqlStrBuilder.ToString();
            ProjectExtendDBOperator sqlOper = new ProjectExtendDBOperator(sqlCommandStr);
            sqlOper.Query();
            QueryProjectProperty queryProjectProperty = new QueryProjectProperty(MainModel);
            queryProjectProperty.Query();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            excuteSql();
            DialogResult = DialogResult.OK;
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            AreaSelectBox areaBox = new AreaSelectBox();
            if (areaBox.ShowDialog()==DialogResult.OK)
            {
                addAreaEvent(areaBox.AreaItem);
            }
        }

        private void addAreaEvent(CategoryEnumItem item)
        {
            foreach (TreeNode node in treeViewArea.Nodes)
            {
                if(node.Tag == item)
                {
                    treeViewArea.SelectedNode = node;
                    return;
                }
            }
            TreeNode areaNode = new TreeNode(item.Description);
            areaNode.Tag = item;
            treeViewArea.Nodes.Add(areaNode);
            treeViewArea.SelectedNode = areaNode;
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            TreeNode areaNode = treeViewArea.SelectedNode;
            if (areaNode == null)
            {
                return;
            }
            areaNode.Nodes.Clear();
            sqlStrBuilder.Append(string.Format("delete from tb_cfg_project_extend where isubid={0};"
                , (areaNode.Tag as CategoryEnumItem).ID));
            foreach(CheckedListBoxItem checkItem in chkListBox.CheckedItems)
            {
                areaNode.Nodes.Add(new TreeNode(checkItem.ToString()));
                sqlStrBuilder.Append(string.Format(
                    "insert into tb_cfg_project_extend (iid,itypeid,isubid,strcomment) values({0},1,{1},'')"
                    , (checkItem.Value as CategoryEnumItem).ID, (areaNode.Tag as CategoryEnumItem).ID)); 
            }
        }
    }
}
