﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LteAntennaForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            DevExpress.XtraCharts.XYDiagram xyDiagram1 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.SecondaryAxisY secondaryAxisY1 = new DevExpress.XtraCharts.SecondaryAxisY();
            DevExpress.XtraCharts.SecondaryAxisY secondaryAxisY2 = new DevExpress.XtraCharts.SecondaryAxisY();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel1 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesView sideBySideBarSeriesView1 = new DevExpress.XtraCharts.SideBySideBarSeriesView();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel2 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram2 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series2 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.StackedBarSeriesLabel stackedBarSeriesLabel1 = new DevExpress.XtraCharts.StackedBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideStackedBarSeriesView sideBySideStackedBarSeriesView1 = new DevExpress.XtraCharts.SideBySideStackedBarSeriesView();
            DevExpress.XtraCharts.StackedBarSeriesLabel stackedBarSeriesLabel2 = new DevExpress.XtraCharts.StackedBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideStackedBarSeriesView sideBySideStackedBarSeriesView2 = new DevExpress.XtraCharts.SideBySideStackedBarSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram3 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series3 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel1 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView1 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            DevExpress.XtraCharts.Series series4 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel2 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView2 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel3 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView3 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            DevExpress.XtraCharts.RadarDiagram radarDiagram1 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series5 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel1 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint1 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint2 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint3 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint4 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint5 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView1 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.Series series6 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel2 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView2 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel3 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView3 = new DevExpress.XtraCharts.RadarLineSeriesView();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage4 = new DevExpress.XtraTab.XtraTabPage();
            this.btnNextpage = new System.Windows.Forms.Button();
            this.btnPrevpage = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.txtPage = new System.Windows.Forms.TextBox();
            this.labPage = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.btnGo = new System.Windows.Forms.Button();
            this.labNum = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.btnSearch = new System.Windows.Forms.Button();
            this.txtCellName = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.dataGridViewCell = new System.Windows.Forms.DataGridView();
            this.Column10 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column11 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column14 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column12 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column13 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column15 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column16 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column17 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column18 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column26 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column27 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column28 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column29 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column30 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column19 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column20 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column21 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column22 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column23 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column24 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column31 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column32 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column33 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column34 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column35 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column36 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column37 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column38 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column39 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column40 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column41 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column49 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column50 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column51 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column52 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column53 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column54 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column55 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column56 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column57 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column58 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column59 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colvender = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colcgi = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colbeamwidth = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colcovertype = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colgmax = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col3db = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col6db = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colrange8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colphase8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col1ob = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col2ob = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col3ob = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colaltitude = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col1para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col2para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col3para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col4para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col5para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col6para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.col7para = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column45 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column46 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column47 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column48 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShwoChart = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowGis = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowSimulation = new System.Windows.Forms.ToolStripMenuItem();
            this.拆分导出CSVToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportWholeExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.dataGridView = new System.Windows.Forms.DataGridView();
            this.colCellname = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSection = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colRxlevSampleNum = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSamplePect = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAvgPccpch = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column42 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column43 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column44 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colMaxPccpch = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colDpchAvgRscp = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colDpchAvgC2I = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAvgTdBler = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAvgTA = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAvgSampleDistance = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAvgCellNum = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.coldMaxPcc0_30_150_180 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colBcch = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cellChanel = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSampleTotal = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAnaType = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colIangle_ob = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colIaltitude = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colIangle_dir = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellPccpAvgRscp = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellDpchAvgRscp = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellDpchAvgC2I = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellAvgTdBler = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellAvgSampleDistance = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellOverNum = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellCellNum = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellAvgTA = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.dataGridViewAngle = new System.Windows.Forms.DataGridView();
            this.cluCellName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cluTarget = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.panel1 = new System.Windows.Forms.Panel();
            this.chartControl1 = new DevExpress.XtraCharts.ChartControl();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbbxSeries2 = new System.Windows.Forms.ComboBox();
            this.cbbxSeries1 = new System.Windows.Forms.ComboBox();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.xtraTabPage5 = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.chartControl5 = new DevExpress.XtraCharts.ChartControl();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.chartControl3 = new DevExpress.XtraCharts.ChartControl();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.chartControl2 = new DevExpress.XtraCharts.ChartControl();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewCell)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAngle)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.xtraTabPage5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            this.groupBox6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(stackedBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideStackedBarSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(stackedBarSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideStackedBarSeriesView2)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView3)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView3)).BeginInit();
            this.SuspendLayout();
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage4;
            this.xtraTabControl1.Size = new System.Drawing.Size(1184, 650);
            this.xtraTabControl1.TabIndex = 1;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage4,
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage5});
            this.xtraTabControl1.SelectedPageChanged += new DevExpress.XtraTab.TabPageChangedEventHandler(this.xtraTabControl1_SelectedPageChanged);
            // 
            // xtraTabPage4
            // 
            this.xtraTabPage4.Controls.Add(this.btnNextpage);
            this.xtraTabPage4.Controls.Add(this.btnPrevpage);
            this.xtraTabPage4.Controls.Add(this.label5);
            this.xtraTabPage4.Controls.Add(this.txtPage);
            this.xtraTabPage4.Controls.Add(this.labPage);
            this.xtraTabPage4.Controls.Add(this.label4);
            this.xtraTabPage4.Controls.Add(this.btnGo);
            this.xtraTabPage4.Controls.Add(this.labNum);
            this.xtraTabPage4.Controls.Add(this.label3);
            this.xtraTabPage4.Controls.Add(this.label2);
            this.xtraTabPage4.Controls.Add(this.btnSearch);
            this.xtraTabPage4.Controls.Add(this.txtCellName);
            this.xtraTabPage4.Controls.Add(this.label1);
            this.xtraTabPage4.Controls.Add(this.dataGridViewCell);
            this.xtraTabPage4.Name = "xtraTabPage4";
            this.xtraTabPage4.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage4.Text = "小区权值设置及覆盖统计";
            // 
            // btnNextpage
            // 
            this.btnNextpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNextpage.Location = new System.Drawing.Point(862, 594);
            this.btnNextpage.Name = "btnNextpage";
            this.btnNextpage.Size = new System.Drawing.Size(33, 23);
            this.btnNextpage.TabIndex = 55;
            this.btnNextpage.Text = ">>";
            this.btnNextpage.UseVisualStyleBackColor = true;
            this.btnNextpage.Click += new System.EventHandler(this.btnNextpage_Click);
            // 
            // btnPrevpage
            // 
            this.btnPrevpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPrevpage.Location = new System.Drawing.Point(823, 594);
            this.btnPrevpage.Name = "btnPrevpage";
            this.btnPrevpage.Size = new System.Drawing.Size(33, 23);
            this.btnPrevpage.TabIndex = 54;
            this.btnPrevpage.Text = "<<";
            this.btnPrevpage.UseVisualStyleBackColor = true;
            this.btnPrevpage.Click += new System.EventHandler(this.btnPrevpage_Click);
            // 
            // label5
            // 
            this.label5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(765, 596);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(19, 14);
            this.label5.TabIndex = 53;
            this.label5.Text = "页";
            // 
            // txtPage
            // 
            this.txtPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtPage.Location = new System.Drawing.Point(700, 593);
            this.txtPage.Name = "txtPage";
            this.txtPage.Size = new System.Drawing.Size(63, 22);
            this.txtPage.TabIndex = 52;
            this.txtPage.Text = "1";
            // 
            // labPage
            // 
            this.labPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labPage.AutoSize = true;
            this.labPage.Location = new System.Drawing.Point(600, 598);
            this.labPage.Name = "labPage";
            this.labPage.Size = new System.Drawing.Size(14, 14);
            this.labPage.TabIndex = 51;
            this.labPage.Text = "0";
            // 
            // label4
            // 
            this.label4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(557, 597);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(43, 14);
            this.label4.TabIndex = 50;
            this.label4.Text = "个，共";
            // 
            // btnGo
            // 
            this.btnGo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnGo.Location = new System.Drawing.Point(784, 593);
            this.btnGo.Name = "btnGo";
            this.btnGo.Size = new System.Drawing.Size(33, 23);
            this.btnGo.TabIndex = 49;
            this.btnGo.Text = "GO";
            this.btnGo.UseVisualStyleBackColor = true;
            this.btnGo.Click += new System.EventHandler(this.btnGo_Click);
            // 
            // labNum
            // 
            this.labNum.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labNum.AutoSize = true;
            this.labNum.Location = new System.Drawing.Point(512, 598);
            this.labNum.Name = "labNum";
            this.labNum.Size = new System.Drawing.Size(14, 14);
            this.labNum.TabIndex = 48;
            this.labNum.Text = "0";
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(634, 597);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(67, 14);
            this.label3.TabIndex = 47;
            this.label3.Text = "页，跳转至";
            // 
            // label2
            // 
            this.label2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(436, 597);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(79, 14);
            this.label2.TabIndex = 46;
            this.label2.Text = "总计小区共：";
            // 
            // btnSearch
            // 
            this.btnSearch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSearch.Location = new System.Drawing.Point(1130, 593);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(42, 23);
            this.btnSearch.TabIndex = 45;
            this.btnSearch.Text = "查找";
            this.btnSearch.UseVisualStyleBackColor = true;
            this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
            // 
            // txtCellName
            // 
            this.txtCellName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtCellName.Location = new System.Drawing.Point(968, 594);
            this.txtCellName.Name = "txtCellName";
            this.txtCellName.Size = new System.Drawing.Size(157, 22);
            this.txtCellName.TabIndex = 44;
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(907, 598);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(67, 14);
            this.label1.TabIndex = 43;
            this.label1.Text = "小区名称：";
            // 
            // dataGridViewCell
            // 
            this.dataGridViewCell.AllowUserToAddRows = false;
            this.dataGridViewCell.AllowUserToDeleteRows = false;
            this.dataGridViewCell.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridViewCell.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridViewCell.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewCell.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.Column10,
            this.Column11,
            this.Column14,
            this.Column12,
            this.Column13,
            this.Column15,
            this.Column16,
            this.Column17,
            this.Column18,
            this.Column26,
            this.Column27,
            this.Column28,
            this.Column29,
            this.Column30,
            this.Column19,
            this.Column20,
            this.Column21,
            this.Column22,
            this.Column23,
            this.Column24,
            this.Column31,
            this.Column32,
            this.Column33,
            this.Column34,
            this.Column35,
            this.Column36,
            this.Column37,
            this.Column38,
            this.Column39,
            this.Column40,
            this.Column41,
            this.Column49,
            this.Column50,
            this.Column51,
            this.Column52,
            this.Column53,
            this.Column54,
            this.Column55,
            this.Column56,
            this.Column57,
            this.Column58,
            this.Column59,
            this.colvender,
            this.colcgi,
            this.colbeamwidth,
            this.colcovertype,
            this.colgmax,
            this.col3db,
            this.col6db,
            this.colrange1,
            this.colrange2,
            this.colrange3,
            this.colrange4,
            this.colrange5,
            this.colrange6,
            this.colrange7,
            this.colrange8,
            this.colphase1,
            this.colphase2,
            this.colphase3,
            this.colphase4,
            this.colphase5,
            this.colphase6,
            this.colphase7,
            this.colphase8,
            this.col1ob,
            this.col2ob,
            this.col3ob,
            this.colaltitude,
            this.col1para,
            this.col2para,
            this.col3para,
            this.col4para,
            this.col5para,
            this.col6para,
            this.col7para,
            this.Column45,
            this.Column46,
            this.Column47,
            this.Column48});
            this.dataGridViewCell.ContextMenuStrip = this.contextMenuStrip;
            this.dataGridViewCell.Location = new System.Drawing.Point(0, 0);
            this.dataGridViewCell.Name = "dataGridViewCell";
            this.dataGridViewCell.RowTemplate.Height = 23;
            this.dataGridViewCell.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewCell.Size = new System.Drawing.Size(1177, 588);
            this.dataGridViewCell.TabIndex = 2;
            // 
            // Column10
            // 
            this.Column10.Frozen = true;
            this.Column10.HeaderText = "时间";
            this.Column10.Name = "Column10";
            // 
            // Column11
            // 
            this.Column11.Frozen = true;
            this.Column11.HeaderText = "地市名称";
            this.Column11.Name = "Column11";
            // 
            // Column14
            // 
            this.Column14.Frozen = true;
            this.Column14.HeaderText = "小区名";
            this.Column14.Name = "Column14";
            // 
            // Column12
            // 
            this.Column12.HeaderText = "网格号";
            this.Column12.Name = "Column12";
            // 
            // Column13
            // 
            this.Column13.HeaderText = "BSC名";
            this.Column13.Name = "Column13";
            // 
            // Column15
            // 
            this.Column15.HeaderText = "频点标示";
            this.Column15.Name = "Column15";
            // 
            // Column16
            // 
            this.Column16.HeaderText = "分析结果";
            this.Column16.Name = "Column16";
            // 
            // Column17
            // 
            this.Column17.HeaderText = "采样点总数";
            this.Column17.Name = "Column17";
            // 
            // Column18
            // 
            this.Column18.HeaderText = "小区RSRP均值";
            this.Column18.Name = "Column18";
            // 
            // Column26
            // 
            this.Column26.HeaderText = "RSRP0";
            this.Column26.Name = "Column26";
            // 
            // Column27
            // 
            this.Column27.HeaderText = "RSRP1";
            this.Column27.Name = "Column27";
            // 
            // Column28
            // 
            this.Column28.HeaderText = "△RSRP";
            this.Column28.Name = "Column28";
            // 
            // Column29
            // 
            this.Column29.HeaderText = "PathLoss";
            this.Column29.Name = "Column29";
            // 
            // Column30
            // 
            this.Column30.HeaderText = "小区路损";
            this.Column30.Name = "Column30";
            // 
            // Column19
            // 
            this.Column19.HeaderText = "小区SINR<=-3占比(%)";
            this.Column19.Name = "Column19";
            // 
            // Column20
            // 
            this.Column20.HeaderText = "小区C/I平均";
            this.Column20.Name = "Column20";
            // 
            // Column21
            // 
            this.Column21.HeaderText = "小区平均通信距离";
            this.Column21.Name = "Column21";
            // 
            // Column22
            // 
            this.Column22.HeaderText = "小区过覆盖指数";
            this.Column22.Name = "Column22";
            // 
            // Column23
            // 
            this.Column23.HeaderText = "小区区域站点密集指数";
            this.Column23.Name = "Column23";
            // 
            // Column24
            // 
            this.Column24.HeaderText = "小区平均TA";
            this.Column24.Name = "Column24";
            // 
            // Column31
            // 
            this.Column31.HeaderText = "传输模式(TM=1)时长占比";
            this.Column31.Name = "Column31";
            // 
            // Column32
            // 
            this.Column32.HeaderText = "传输模式(TM=2)时长占比";
            this.Column32.Name = "Column32";
            // 
            // Column33
            // 
            this.Column33.HeaderText = "传输模式(TM=3)时长占比";
            this.Column33.Name = "Column33";
            // 
            // Column34
            // 
            this.Column34.HeaderText = "传输模式(TM=4)时长占比";
            this.Column34.Name = "Column34";
            // 
            // Column35
            // 
            this.Column35.HeaderText = "传输模式(TM=5)时长占比";
            this.Column35.Name = "Column35";
            // 
            // Column36
            // 
            this.Column36.HeaderText = "传输模式(TM=6)时长占比";
            this.Column36.Name = "Column36";
            // 
            // Column37
            // 
            this.Column37.HeaderText = "传输模式(TM=7)时长占比";
            this.Column37.Name = "Column37";
            // 
            // Column38
            // 
            this.Column38.HeaderText = "传输模式(TM=8)时长占比";
            this.Column38.Name = "Column38";
            // 
            // Column39
            // 
            this.Column39.HeaderText = "单流时长占比";
            this.Column39.Name = "Column39";
            // 
            // Column40
            // 
            this.Column40.HeaderText = "双流时长占比";
            this.Column40.Name = "Column40";
            // 
            // Column41
            // 
            this.Column41.HeaderText = "总时长";
            this.Column41.Name = "Column41";
            // 
            // Column49
            // 
            this.Column49.HeaderText = "覆盖率(RSRP≥-110&SINR>=-3)";
            this.Column49.Name = "Column49";
            // 
            // Column50
            // 
            this.Column50.HeaderText = "LTE覆盖率(RSRP≥-110)";
            this.Column50.Name = "Column50";
            // 
            // Column51
            // 
            this.Column51.HeaderText = "LTE覆盖率(SINR>=-3)";
            this.Column51.Name = "Column51";
            // 
            // Column52
            // 
            this.Column52.HeaderText = "±(0,60°)范围内采样点比例";
            this.Column52.Name = "Column52";
            // 
            // Column53
            // 
            this.Column53.HeaderText = "±(0,60°)范围内小区平均RSRP";
            this.Column53.Name = "Column53";
            // 
            // Column54
            // 
            this.Column54.HeaderText = "±(0,60°)范围内覆盖率(RSRP≥-110&SINR>=-3)";
            this.Column54.Name = "Column54";
            // 
            // Column55
            // 
            this.Column55.HeaderText = "±(0,60°)范围内LTE覆盖率(RSRP≥-110)";
            this.Column55.Name = "Column55";
            // 
            // Column56
            // 
            this.Column56.HeaderText = "±(0,60°)范围内LTE覆盖率(SINR>=-3)";
            this.Column56.Name = "Column56";
            // 
            // Column57
            // 
            this.Column57.HeaderText = "±(60,150°)范围内采样点比例";
            this.Column57.Name = "Column57";
            // 
            // Column58
            // 
            this.Column58.HeaderText = "±(150,180°)范围内采样点比例";
            this.Column58.Name = "Column58";
            // 
            // Column59
            // 
            this.Column59.HeaderText = "前后比";
            this.Column59.Name = "Column59";
            // 
            // colvender
            // 
            this.colvender.HeaderText = "主设备厂家";
            this.colvender.Name = "colvender";
            // 
            // colcgi
            // 
            this.colcgi.HeaderText = "CGI";
            this.colcgi.Name = "colcgi";
            // 
            // colbeamwidth
            // 
            this.colbeamwidth.HeaderText = "波束宽度";
            this.colbeamwidth.Name = "colbeamwidth";
            // 
            // colcovertype
            // 
            this.colcovertype.HeaderText = "覆盖类型";
            this.colcovertype.Name = "colcovertype";
            // 
            // colgmax
            // 
            this.colgmax.HeaderText = "Gmax";
            this.colgmax.Name = "colgmax";
            // 
            // col3db
            // 
            this.col3db.HeaderText = "3dB功率角";
            this.col3db.Name = "col3db";
            // 
            // col6db
            // 
            this.col6db.HeaderText = "6dB功率角";
            this.col6db.Name = "col6db";
            // 
            // colrange1
            // 
            this.colrange1.HeaderText = "端口1幅度";
            this.colrange1.Name = "colrange1";
            // 
            // colrange2
            // 
            this.colrange2.HeaderText = "端口2幅度";
            this.colrange2.Name = "colrange2";
            // 
            // colrange3
            // 
            this.colrange3.HeaderText = "端口3幅度";
            this.colrange3.Name = "colrange3";
            // 
            // colrange4
            // 
            this.colrange4.HeaderText = "端口4幅度";
            this.colrange4.Name = "colrange4";
            // 
            // colrange5
            // 
            this.colrange5.HeaderText = "端口5幅度";
            this.colrange5.Name = "colrange5";
            // 
            // colrange6
            // 
            this.colrange6.HeaderText = "端口6幅度";
            this.colrange6.Name = "colrange6";
            // 
            // colrange7
            // 
            this.colrange7.HeaderText = "端口7幅度";
            this.colrange7.Name = "colrange7";
            // 
            // colrange8
            // 
            this.colrange8.HeaderText = "端口8幅度";
            this.colrange8.Name = "colrange8";
            // 
            // colphase1
            // 
            this.colphase1.HeaderText = "端口1相位";
            this.colphase1.Name = "colphase1";
            // 
            // colphase2
            // 
            this.colphase2.HeaderText = "端口2相位";
            this.colphase2.Name = "colphase2";
            // 
            // colphase3
            // 
            this.colphase3.HeaderText = "端口3相位";
            this.colphase3.Name = "colphase3";
            // 
            // colphase4
            // 
            this.colphase4.HeaderText = "端口4相位";
            this.colphase4.Name = "colphase4";
            // 
            // colphase5
            // 
            this.colphase5.HeaderText = "端口5相位";
            this.colphase5.Name = "colphase5";
            // 
            // colphase6
            // 
            this.colphase6.HeaderText = "端口6相位";
            this.colphase6.Name = "colphase6";
            // 
            // colphase7
            // 
            this.colphase7.HeaderText = "端口7相位";
            this.colphase7.Name = "colphase7";
            // 
            // colphase8
            // 
            this.colphase8.HeaderText = "端口8相位";
            this.colphase8.Name = "colphase8";
            // 
            // col1ob
            // 
            this.col1ob.HeaderText = "预置下倾角";
            this.col1ob.Name = "col1ob";
            // 
            // col2ob
            // 
            this.col2ob.HeaderText = "机械下倾角";
            this.col2ob.Name = "col2ob";
            // 
            // col3ob
            // 
            this.col3ob.HeaderText = "电调下倾角";
            this.col3ob.Name = "col3ob";
            // 
            // colaltitude
            // 
            this.colaltitude.HeaderText = "挂高";
            this.colaltitude.Name = "colaltitude";
            // 
            // col1para
            // 
            this.col1para.HeaderText = "上行吞吐量(MB)";
            this.col1para.Name = "col1para";
            // 
            // col2para
            // 
            this.col2para.HeaderText = "下行吞吐量(MB)";
            this.col2para.Name = "col2para";
            // 
            // col3para
            // 
            this.col3para.HeaderText = "无线接通率(%)";
            this.col3para.Name = "col3para";
            // 
            // col4para
            // 
            this.col4para.HeaderText = "无线掉线率(%)";
            this.col4para.Name = "col4para";
            // 
            // col5para
            // 
            this.col5para.HeaderText = "切换成功率(%)";
            this.col5para.Name = "col5para";
            // 
            // col6para
            // 
            this.col6para.HeaderText = "ERAB建立成功率(%)";
            this.col6para.Name = "col6para";
            // 
            // col7para
            // 
            this.col7para.HeaderText = "ERAB掉线率(%)";
            this.col7para.Name = "col7para";
            // 
            // Column45
            // 
            this.Column45.HeaderText = "RSRP均值";
            this.Column45.Name = "Column45";
            // 
            // Column46
            // 
            this.Column46.HeaderText = "SINR均值";
            this.Column46.Name = "Column46";
            // 
            // Column47
            // 
            this.Column47.HeaderText = "95覆盖率";
            this.Column47.Name = "Column47";
            // 
            // Column48
            // 
            this.Column48.HeaderText = "110覆盖率";
            this.Column48.Name = "Column48";
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShwoChart,
            this.miShowGis,
            this.miShowSimulation,
            this.拆分导出CSVToolStripMenuItem,
            this.miExportWholeExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(209, 114);
            // 
            // miShwoChart
            // 
            this.miShwoChart.Name = "miShwoChart";
            this.miShwoChart.Size = new System.Drawing.Size(208, 22);
            this.miShwoChart.Text = "显示天线辐射波形重建";
            this.miShwoChart.Click += new System.EventHandler(this.miShwoChart_Click);
            // 
            // miShowGis
            // 
            this.miShowGis.Name = "miShowGis";
            this.miShowGis.Size = new System.Drawing.Size(208, 22);
            this.miShowGis.Text = "显示选择小区及其采样点";
            this.miShowGis.Click += new System.EventHandler(this.miShowGis_Click);
            // 
            // miShowSimulation
            // 
            this.miShowSimulation.Name = "miShowSimulation";
            this.miShowSimulation.Size = new System.Drawing.Size(208, 22);
            this.miShowSimulation.Text = "显示天线覆盖仿真";
            this.miShowSimulation.Click += new System.EventHandler(this.miShowSimulation_Click);
            // 
            // 拆分导出CSVToolStripMenuItem
            // 
            this.拆分导出CSVToolStripMenuItem.Name = "拆分导出CSVToolStripMenuItem";
            this.拆分导出CSVToolStripMenuItem.Size = new System.Drawing.Size(208, 22);
            this.拆分导出CSVToolStripMenuItem.Text = "拆分导出CSV";
            this.拆分导出CSVToolStripMenuItem.Click += new System.EventHandler(this.拆分导出CSVToolStripMenuItem_Click);
            // 
            // miExportWholeExcel
            // 
            this.miExportWholeExcel.Name = "miExportWholeExcel";
            this.miExportWholeExcel.Size = new System.Drawing.Size(208, 22);
            this.miExportWholeExcel.Text = "导出Excel";
            this.miExportWholeExcel.Click += new System.EventHandler(this.miExportWholeExcel_Click);
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.dataGridView);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage1.Text = "角度区间覆盖统计";
            // 
            // dataGridView
            // 
            this.dataGridView.AllowUserToAddRows = false;
            this.dataGridView.AllowUserToDeleteRows = false;
            this.dataGridView.BackgroundColor = System.Drawing.Color.White;
            this.dataGridView.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.colCellname,
            this.colSection,
            this.colRxlevSampleNum,
            this.colSamplePect,
            this.colAvgPccpch,
            this.Column42,
            this.Column43,
            this.Column44,
            this.colMaxPccpch,
            this.colDpchAvgRscp,
            this.colDpchAvgC2I,
            this.colAvgTdBler,
            this.colAvgTA,
            this.colAvgSampleDistance,
            this.colAvgCellNum,
            this.coldMaxPcc0_30_150_180,
            this.colBcch,
            this.cellChanel,
            this.colSampleTotal,
            this.colAnaType,
            this.colIangle_ob,
            this.colIaltitude,
            this.colIangle_dir,
            this.colCellPccpAvgRscp,
            this.colCellDpchAvgRscp,
            this.colCellDpchAvgC2I,
            this.colCellAvgTdBler,
            this.colCellAvgSampleDistance,
            this.colCellOverNum,
            this.colCellCellNum,
            this.colCellAvgTA});
            this.dataGridView.ContextMenuStrip = this.contextMenuStrip;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridView.DefaultCellStyle = dataGridViewCellStyle1;
            this.dataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridView.Location = new System.Drawing.Point(0, 0);
            this.dataGridView.Name = "dataGridView";
            this.dataGridView.RowTemplate.Height = 23;
            this.dataGridView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridView.Size = new System.Drawing.Size(1177, 620);
            this.dataGridView.TabIndex = 0;
            // 
            // colCellname
            // 
            this.colCellname.HeaderText = "小区名称";
            this.colCellname.Name = "colCellname";
            this.colCellname.ReadOnly = true;
            this.colCellname.Width = 250;
            // 
            // colSection
            // 
            this.colSection.HeaderText = "方向角偏差区间";
            this.colSection.Name = "colSection";
            this.colSection.ReadOnly = true;
            // 
            // colRxlevSampleNum
            // 
            this.colRxlevSampleNum.HeaderText = "区间采样点数";
            this.colRxlevSampleNum.Name = "colRxlevSampleNum";
            this.colRxlevSampleNum.ReadOnly = true;
            // 
            // colSamplePect
            // 
            this.colSamplePect.HeaderText = "采样点占比";
            this.colSamplePect.Name = "colSamplePect";
            this.colSamplePect.ReadOnly = true;
            // 
            // colAvgPccpch
            // 
            this.colAvgPccpch.HeaderText = "区间平均RSRP";
            this.colAvgPccpch.Name = "colAvgPccpch";
            this.colAvgPccpch.ReadOnly = true;
            // 
            // Column42
            // 
            this.Column42.HeaderText = "区间平均RSRP0";
            this.Column42.Name = "Column42";
            // 
            // Column43
            // 
            this.Column43.HeaderText = "区间平均RSRP1";
            this.Column43.Name = "Column43";
            // 
            // Column44
            // 
            this.Column44.HeaderText = "区间△RSRP";
            this.Column44.Name = "Column44";
            // 
            // colMaxPccpch
            // 
            this.colMaxPccpch.HeaderText = "区间最大RSRP";
            this.colMaxPccpch.Name = "colMaxPccpch";
            this.colMaxPccpch.ReadOnly = true;
            // 
            // colDpchAvgRscp
            // 
            this.colDpchAvgRscp.HeaderText = "区间RSSI平均";
            this.colDpchAvgRscp.Name = "colDpchAvgRscp";
            this.colDpchAvgRscp.ReadOnly = true;
            this.colDpchAvgRscp.Width = 120;
            // 
            // colDpchAvgC2I
            // 
            this.colDpchAvgC2I.HeaderText = "区间SINR平均";
            this.colDpchAvgC2I.Name = "colDpchAvgC2I";
            this.colDpchAvgC2I.ReadOnly = true;
            // 
            // colAvgTdBler
            // 
            this.colAvgTdBler.HeaderText = "区间RSRQ平均";
            this.colAvgTdBler.Name = "colAvgTdBler";
            this.colAvgTdBler.ReadOnly = true;
            // 
            // colAvgTA
            // 
            this.colAvgTA.HeaderText = "区间平均TA";
            this.colAvgTA.Name = "colAvgTA";
            // 
            // colAvgSampleDistance
            // 
            this.colAvgSampleDistance.HeaderText = "区间平均通信距离";
            this.colAvgSampleDistance.Name = "colAvgSampleDistance";
            this.colAvgSampleDistance.ReadOnly = true;
            // 
            // colAvgCellNum
            // 
            this.colAvgCellNum.HeaderText = "区间过覆盖指数";
            this.colAvgCellNum.Name = "colAvgCellNum";
            this.colAvgCellNum.ReadOnly = true;
            // 
            // coldMaxPcc0_30_150_180
            // 
            this.coldMaxPcc0_30_150_180.HeaderText = "[0,30]区间与(150,180]区间最强PCCPCH的差值";
            this.coldMaxPcc0_30_150_180.Name = "coldMaxPcc0_30_150_180";
            this.coldMaxPcc0_30_150_180.ReadOnly = true;
            this.coldMaxPcc0_30_150_180.Width = 200;
            // 
            // colBcch
            // 
            this.colBcch.HeaderText = "频点";
            this.colBcch.Name = "colBcch";
            this.colBcch.ReadOnly = true;
            // 
            // cellChanel
            // 
            this.cellChanel.HeaderText = "频段";
            this.cellChanel.Name = "cellChanel";
            // 
            // colSampleTotal
            // 
            this.colSampleTotal.HeaderText = "总采样点数";
            this.colSampleTotal.Name = "colSampleTotal";
            this.colSampleTotal.ReadOnly = true;
            // 
            // colAnaType
            // 
            this.colAnaType.HeaderText = "天线类型";
            this.colAnaType.Name = "colAnaType";
            this.colAnaType.ReadOnly = true;
            // 
            // colIangle_ob
            // 
            this.colIangle_ob.HeaderText = "下倾角";
            this.colIangle_ob.Name = "colIangle_ob";
            this.colIangle_ob.ReadOnly = true;
            // 
            // colIaltitude
            // 
            this.colIaltitude.HeaderText = "挂高";
            this.colIaltitude.Name = "colIaltitude";
            this.colIaltitude.ReadOnly = true;
            // 
            // colIangle_dir
            // 
            this.colIangle_dir.HeaderText = "方向角";
            this.colIangle_dir.Name = "colIangle_dir";
            this.colIangle_dir.ReadOnly = true;
            // 
            // colCellPccpAvgRscp
            // 
            this.colCellPccpAvgRscp.HeaderText = "小区RSRP平均";
            this.colCellPccpAvgRscp.Name = "colCellPccpAvgRscp";
            this.colCellPccpAvgRscp.ReadOnly = true;
            // 
            // colCellDpchAvgRscp
            // 
            this.colCellDpchAvgRscp.HeaderText = "小区RSSI平均";
            this.colCellDpchAvgRscp.Name = "colCellDpchAvgRscp";
            this.colCellDpchAvgRscp.ReadOnly = true;
            // 
            // colCellDpchAvgC2I
            // 
            this.colCellDpchAvgC2I.HeaderText = "小区SINR平均";
            this.colCellDpchAvgC2I.Name = "colCellDpchAvgC2I";
            this.colCellDpchAvgC2I.ReadOnly = true;
            // 
            // colCellAvgTdBler
            // 
            this.colCellAvgTdBler.HeaderText = "小区RSRQ平均";
            this.colCellAvgTdBler.Name = "colCellAvgTdBler";
            this.colCellAvgTdBler.ReadOnly = true;
            // 
            // colCellAvgSampleDistance
            // 
            this.colCellAvgSampleDistance.HeaderText = "小区平均通信距离";
            this.colCellAvgSampleDistance.Name = "colCellAvgSampleDistance";
            this.colCellAvgSampleDistance.ReadOnly = true;
            // 
            // colCellOverNum
            // 
            this.colCellOverNum.HeaderText = "小区过覆盖指数";
            this.colCellOverNum.Name = "colCellOverNum";
            this.colCellOverNum.ReadOnly = true;
            // 
            // colCellCellNum
            // 
            this.colCellCellNum.HeaderText = "小区区域站点密集指数";
            this.colCellCellNum.Name = "colCellCellNum";
            this.colCellCellNum.ReadOnly = true;
            // 
            // colCellAvgTA
            // 
            this.colCellAvgTA.HeaderText = "小区平均TA";
            this.colCellAvgTA.Name = "colCellAvgTA";
            this.colCellAvgTA.ReadOnly = true;
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.groupControl2);
            this.xtraTabPage2.Controls.Add(this.groupControl1);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage2.Text = "天线角度及采样数据分析";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.dataGridViewAngle);
            this.groupControl2.Location = new System.Drawing.Point(10, 390);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(1142, 218);
            this.groupControl2.TabIndex = 1;
            this.groupControl2.Text = "天线角度";
            // 
            // dataGridViewAngle
            // 
            this.dataGridViewAngle.AllowUserToAddRows = false;
            this.dataGridViewAngle.AllowUserToDeleteRows = false;
            this.dataGridViewAngle.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewAngle.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.cluCellName,
            this.cluTarget});
            this.dataGridViewAngle.Location = new System.Drawing.Point(12, 32);
            this.dataGridViewAngle.Name = "dataGridViewAngle";
            this.dataGridViewAngle.RowTemplate.Height = 23;
            this.dataGridViewAngle.Size = new System.Drawing.Size(1121, 181);
            this.dataGridViewAngle.TabIndex = 0;
            // 
            // cluCellName
            // 
            this.cluCellName.HeaderText = "小区名称";
            this.cluCellName.Name = "cluCellName";
            this.cluCellName.Width = 150;
            // 
            // cluTarget
            // 
            this.cluTarget.HeaderText = "指标项";
            this.cluTarget.Name = "cluTarget";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.groupBox2);
            this.groupControl1.Controls.Add(this.groupBox1);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1177, 620);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "图表";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.panel1);
            this.groupBox2.Controls.Add(this.chartControl1);
            this.groupBox2.Location = new System.Drawing.Point(10, 75);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(1119, 265);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            // 
            // panel1
            // 
            this.panel1.Location = new System.Drawing.Point(1082, 21);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(33, 223);
            this.panel1.TabIndex = 3;
            // 
            // chartControl1
            // 
            xyDiagram1.AxisX.MinorCount = 1;
            xyDiagram1.AxisX.Range.Auto = false;
            xyDiagram1.AxisX.Range.MaxValueInternal = 3.4999999999999991D;
            xyDiagram1.AxisX.Range.MinValueInternal = -0.5D;
            xyDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Tickmarks.MinorLength = 1;
            xyDiagram1.AxisX.Title.Alignment = System.Drawing.StringAlignment.Near;
            xyDiagram1.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram1.AxisY.Color = System.Drawing.Color.DarkOrange;
            xyDiagram1.AxisY.Interlaced = true;
            xyDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Thickness = 2;
            xyDiagram1.AxisY.VisibleInPanesSerializable = "-1";
            xyDiagram1.DefaultPane.BackColor = System.Drawing.Color.Transparent;
            xyDiagram1.EnableAxisXScrolling = true;
            xyDiagram1.EnableAxisXZooming = true;
            xyDiagram1.EnableAxisYScrolling = true;
            xyDiagram1.Margins.Bottom = 1;
            xyDiagram1.Margins.Left = 1;
            xyDiagram1.Margins.Right = 1;
            xyDiagram1.Margins.Top = 1;
            xyDiagram1.PaneDistance = 5;
            secondaryAxisY1.AxisID = 0;
            secondaryAxisY1.Color = System.Drawing.Color.Blue;
            secondaryAxisY1.GridLines.Color = System.Drawing.Color.White;
            secondaryAxisY1.Interlaced = true;
            secondaryAxisY1.Name = "Secondary AxisY 1";
            secondaryAxisY1.Range.Auto = false;
            secondaryAxisY1.Range.MaxValueSerializable = "1";
            secondaryAxisY1.Range.MinValueSerializable = "0";
            secondaryAxisY1.Range.ScrollingRange.SideMarginsEnabled = true;
            secondaryAxisY1.Range.SideMarginsEnabled = true;
            secondaryAxisY1.Thickness = 2;
            secondaryAxisY1.VisibleInPanesSerializable = "-1";
            secondaryAxisY2.AxisID = 1;
            secondaryAxisY2.Name = "Secondary AxisY 2";
            secondaryAxisY2.Range.ScrollingRange.SideMarginsEnabled = true;
            secondaryAxisY2.Range.SideMarginsEnabled = true;
            secondaryAxisY2.VisibleInPanesSerializable = "-1";
            xyDiagram1.SecondaryAxesY.AddRange(new DevExpress.XtraCharts.SecondaryAxisY[] {
            secondaryAxisY1,
            secondaryAxisY2});
            this.chartControl1.Diagram = xyDiagram1;
            this.chartControl1.Location = new System.Drawing.Point(9, 21);
            this.chartControl1.Name = "chartControl1";
            this.chartControl1.RefreshDataOnRepaint = false;
            this.chartControl1.RuntimeHitTesting = false;
            sideBySideBarSeriesLabel1.LineVisible = false;
            sideBySideBarSeriesLabel1.Visible = false;
            series1.Label = sideBySideBarSeriesLabel1;
            series1.Name = "Series 3";
            series1.ShowInLegend = false;
            sideBySideBarSeriesView1.AxisYName = "Secondary AxisY 2";
            sideBySideBarSeriesView1.BarWidth = 1D;
            sideBySideBarSeriesView1.Color = System.Drawing.Color.Red;
            series1.View = sideBySideBarSeriesView1;
            this.chartControl1.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1};
            sideBySideBarSeriesLabel2.LineVisible = true;
            this.chartControl1.SeriesTemplate.Label = sideBySideBarSeriesLabel2;
            this.chartControl1.SideBySideBarDistanceVariable = 0.01D;
            this.chartControl1.Size = new System.Drawing.Size(1104, 223);
            this.chartControl1.TabIndex = 2;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.cbbxSeries2);
            this.groupBox1.Controls.Add(this.cbbxSeries1);
            this.groupBox1.Controls.Add(this.labelControl2);
            this.groupBox1.Controls.Add(this.labelControl1);
            this.groupBox1.Location = new System.Drawing.Point(10, 29);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(1121, 50);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "图例";
            // 
            // cbbxSeries2
            // 
            this.cbbxSeries2.FormattingEnabled = true;
            this.cbbxSeries2.Location = new System.Drawing.Point(365, 18);
            this.cbbxSeries2.Name = "cbbxSeries2";
            this.cbbxSeries2.Size = new System.Drawing.Size(112, 22);
            this.cbbxSeries2.TabIndex = 3;
            this.cbbxSeries2.SelectedIndexChanged += new System.EventHandler(this.cbbxSeries2_SelectedIndexChanged);
            // 
            // cbbxSeries1
            // 
            this.cbbxSeries1.FormattingEnabled = true;
            this.cbbxSeries1.Location = new System.Drawing.Point(133, 18);
            this.cbbxSeries1.Name = "cbbxSeries1";
            this.cbbxSeries1.Size = new System.Drawing.Size(116, 22);
            this.cbbxSeries1.TabIndex = 2;
            this.cbbxSeries1.SelectedIndexChanged += new System.EventHandler(this.cbbxSeries1_SelectedIndexChanged);
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(299, 21);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(60, 14);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "折线图指标";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(67, 21);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(60, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "柱形图指标";
            // 
            // xtraTabPage5
            // 
            this.xtraTabPage5.Controls.Add(this.groupControl3);
            this.xtraTabPage5.Name = "xtraTabPage5";
            this.xtraTabPage5.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage5.Text = "天线辐射波形重构";
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.groupBox6);
            this.groupControl3.Controls.Add(this.groupBox4);
            this.groupControl3.Controls.Add(this.groupBox3);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl3.Location = new System.Drawing.Point(0, 0);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(1177, 620);
            this.groupControl3.TabIndex = 1;
            this.groupControl3.Text = "全向分析";
            // 
            // groupBox6
            // 
            this.groupBox6.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox6.Controls.Add(this.chartControl5);
            this.groupBox6.Location = new System.Drawing.Point(605, 26);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(565, 272);
            this.groupBox6.TabIndex = 2;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "权值理想波形图";
            // 
            // chartControl5
            // 
            this.chartControl5.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            xyDiagram2.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram2.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl5.Diagram = xyDiagram2;
            this.chartControl5.Legend.Visible = false;
            this.chartControl5.Location = new System.Drawing.Point(6, 21);
            this.chartControl5.Name = "chartControl5";
            series2.Label = stackedBarSeriesLabel1;
            series2.Name = "Series 1";
            series2.View = sideBySideStackedBarSeriesView1;
            this.chartControl5.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series2};
            this.chartControl5.SeriesTemplate.Label = stackedBarSeriesLabel2;
            this.chartControl5.SeriesTemplate.View = sideBySideStackedBarSeriesView2;
            this.chartControl5.Size = new System.Drawing.Size(553, 245);
            this.chartControl5.TabIndex = 0;
            this.chartControl5.CustomDrawSeriesPoint += new DevExpress.XtraCharts.CustomDrawSeriesPointEventHandler(this.chartControl5_CustomDrawSeriesPoint);
            // 
            // groupBox4
            // 
            this.groupBox4.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox4.Controls.Add(this.chartControl3);
            this.groupBox4.Location = new System.Drawing.Point(599, 304);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(571, 309);
            this.groupBox4.TabIndex = 1;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "垂直面覆盖图";
            // 
            // chartControl3
            // 
            this.chartControl3.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            xyDiagram3.AxisX.Range.ScrollingRange.SideMarginsEnabled = false;
            xyDiagram3.AxisX.Range.SideMarginsEnabled = false;
            xyDiagram3.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram3.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram3.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl3.Diagram = xyDiagram3;
            this.chartControl3.Location = new System.Drawing.Point(6, 21);
            this.chartControl3.Name = "chartControl3";
            pointSeriesLabel1.LineVisible = true;
            series3.Label = pointSeriesLabel1;
            series3.Name = "AntSeries";
            series3.ShowInLegend = false;
            series3.View = splineAreaSeriesView1;
            pointSeriesLabel2.LineVisible = true;
            series4.Label = pointSeriesLabel2;
            series4.Name = "StandardSeries";
            series4.ShowInLegend = false;
            series4.View = splineAreaSeriesView2;
            this.chartControl3.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series3,
        series4};
            pointSeriesLabel3.LineVisible = true;
            this.chartControl3.SeriesTemplate.Label = pointSeriesLabel3;
            splineAreaSeriesView3.Transparency = ((byte)(0));
            this.chartControl3.SeriesTemplate.View = splineAreaSeriesView3;
            this.chartControl3.Size = new System.Drawing.Size(559, 282);
            this.chartControl3.TabIndex = 0;
            // 
            // groupBox3
            // 
            this.groupBox3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox3.Controls.Add(this.chartControl2);
            this.groupBox3.Location = new System.Drawing.Point(10, 26);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(583, 587);
            this.groupBox3.TabIndex = 0;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "俯视面辐射图";
            // 
            // chartControl2
            // 
            this.chartControl2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.chartControl2.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram1.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartControl2.Diagram = radarDiagram1;
            this.chartControl2.Location = new System.Drawing.Point(6, 21);
            this.chartControl2.Name = "chartControl2";
            radarPointSeriesLabel1.LineVisible = true;
            series5.Label = radarPointSeriesLabel1;
            series5.Name = "AntSeries";
            series5.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint1,
            seriesPoint2,
            seriesPoint3,
            seriesPoint4,
            seriesPoint5});
            series5.ShowInLegend = false;
            series5.View = radarLineSeriesView1;
            radarPointSeriesLabel2.LineVisible = true;
            series6.Label = radarPointSeriesLabel2;
            series6.Name = "StandardSeries";
            series6.ShowInLegend = false;
            series6.View = radarLineSeriesView2;
            this.chartControl2.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series5,
        series6};
            radarPointSeriesLabel3.LineVisible = true;
            this.chartControl2.SeriesTemplate.Label = radarPointSeriesLabel3;
            this.chartControl2.SeriesTemplate.View = radarLineSeriesView3;
            this.chartControl2.Size = new System.Drawing.Size(571, 560);
            this.chartControl2.TabIndex = 0;
            this.chartControl2.SizeChanged += new System.EventHandler(this.chartControl2_SizeChanged);
            // 
            // LteAntennaForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1184, 650);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "LteAntennaForm";
            this.Text = "天线分析结果";
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage4.ResumeLayout(false);
            this.xtraTabPage4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewCell)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAngle)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.xtraTabPage5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.groupBox6.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(stackedBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideStackedBarSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(stackedBarSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideStackedBarSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl5)).EndInit();
            this.groupBox4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl3)).EndInit();
            this.groupBox3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl2)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private System.Windows.Forms.DataGridView dataGridView;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private System.Windows.Forms.DataGridView dataGridViewAngle;
        private System.Windows.Forms.DataGridViewTextBoxColumn cluCellName;
        private System.Windows.Forms.DataGridViewTextBoxColumn cluTarget;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Panel panel1;
        private DevExpress.XtraCharts.ChartControl chartControl1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.ComboBox cbbxSeries2;
        private System.Windows.Forms.ComboBox cbbxSeries1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miShwoChart;
        private System.Windows.Forms.ToolStripMenuItem miShowGis;
        private System.Windows.Forms.ToolStripMenuItem miShowSimulation;
        private System.Windows.Forms.ToolStripMenuItem miExportWholeExcel;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage4;
        private System.Windows.Forms.DataGridView dataGridViewCell;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage5;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private System.Windows.Forms.GroupBox groupBox6;
        private DevExpress.XtraCharts.ChartControl chartControl5;
        private System.Windows.Forms.GroupBox groupBox4;
        private DevExpress.XtraCharts.ChartControl chartControl3;
        private System.Windows.Forms.GroupBox groupBox3;
        private DevExpress.XtraCharts.ChartControl chartControl2;
        private System.Windows.Forms.ToolStripMenuItem 拆分导出CSVToolStripMenuItem;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column10;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column11;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column14;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column12;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column13;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column15;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column16;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column17;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column18;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column26;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column27;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column28;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column29;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column30;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column19;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column20;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column21;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column22;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column23;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column24;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column31;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column32;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column33;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column34;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column35;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column36;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column37;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column38;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column39;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column40;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column41;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column49;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column50;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column51;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column52;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column53;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column54;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column55;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column56;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column57;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column58;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column59;
        private System.Windows.Forms.DataGridViewTextBoxColumn colvender;
        private System.Windows.Forms.DataGridViewTextBoxColumn colcgi;
        private System.Windows.Forms.DataGridViewTextBoxColumn colbeamwidth;
        private System.Windows.Forms.DataGridViewTextBoxColumn colcovertype;
        private System.Windows.Forms.DataGridViewTextBoxColumn colgmax;
        private System.Windows.Forms.DataGridViewTextBoxColumn col3db;
        private System.Windows.Forms.DataGridViewTextBoxColumn col6db;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange1;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange2;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange3;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange4;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange5;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange6;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange7;
        private System.Windows.Forms.DataGridViewTextBoxColumn colrange8;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase1;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase2;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase3;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase4;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase5;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase6;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase7;
        private System.Windows.Forms.DataGridViewTextBoxColumn colphase8;
        private System.Windows.Forms.DataGridViewTextBoxColumn col1ob;
        private System.Windows.Forms.DataGridViewTextBoxColumn col2ob;
        private System.Windows.Forms.DataGridViewTextBoxColumn col3ob;
        private System.Windows.Forms.DataGridViewTextBoxColumn colaltitude;
        private System.Windows.Forms.DataGridViewTextBoxColumn col1para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col2para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col3para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col4para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col5para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col6para;
        private System.Windows.Forms.DataGridViewTextBoxColumn col7para;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column45;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column46;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column47;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column48;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellname;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSection;
        private System.Windows.Forms.DataGridViewTextBoxColumn colRxlevSampleNum;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSamplePect;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAvgPccpch;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column42;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column43;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column44;
        private System.Windows.Forms.DataGridViewTextBoxColumn colMaxPccpch;
        private System.Windows.Forms.DataGridViewTextBoxColumn colDpchAvgRscp;
        private System.Windows.Forms.DataGridViewTextBoxColumn colDpchAvgC2I;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAvgTdBler;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAvgTA;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAvgSampleDistance;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAvgCellNum;
        private System.Windows.Forms.DataGridViewTextBoxColumn coldMaxPcc0_30_150_180;
        private System.Windows.Forms.DataGridViewTextBoxColumn colBcch;
        private System.Windows.Forms.DataGridViewTextBoxColumn cellChanel;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSampleTotal;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAnaType;
        private System.Windows.Forms.DataGridViewTextBoxColumn colIangle_ob;
        private System.Windows.Forms.DataGridViewTextBoxColumn colIaltitude;
        private System.Windows.Forms.DataGridViewTextBoxColumn colIangle_dir;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellPccpAvgRscp;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellDpchAvgRscp;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellDpchAvgC2I;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellAvgTdBler;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellAvgSampleDistance;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellOverNum;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellCellNum;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellAvgTA;
        private System.Windows.Forms.Button btnNextpage;
        private System.Windows.Forms.Button btnPrevpage;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtPage;
        private System.Windows.Forms.Label labPage;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Button btnGo;
        private System.Windows.Forms.Label labNum;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnSearch;
        private System.Windows.Forms.TextBox txtCellName;
        private System.Windows.Forms.Label label1;
    }
}