﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS
{
    public partial class CPTextColorEditDlg : Form
    {
        public CPModeColorItem cpColorItem { get; set; }

        public CPTextColorEditDlg()
        {
            InitializeComponent();
            cpColorItem = new CPModeColorItem();

            rangeSetting.NumericUpDownMin.Minimum = int.MinValue;
            rangeSetting.NumericUpDownMin.Maximum = int.MaxValue;
            rangeSetting.NumericUpDownMin.Value = -100;
            rangeSetting.NumericUpDownMin.Increment = 0.1M;
            rangeSetting.NumericUpDownMin.DecimalPlaces = 1;
            rangeSetting.NumericUpDownMax.Minimum = int.MinValue;
            rangeSetting.NumericUpDownMax.Maximum = int.MaxValue;
            rangeSetting.NumericUpDownMax.Value = -55;
            rangeSetting.NumericUpDownMax.Increment = 0.1M;
            rangeSetting.NumericUpDownMax.DecimalPlaces = 1;

            comboBoxCmpType.Items.Clear();
            comboBoxCmpType.Items.Add(CPModeEditForm.HOST_SUB_GUEST);
            comboBoxCmpType.Items.Add(CPModeEditForm.BOTH_STANDARD);
            comboBoxCmpType.SelectedIndex = 0;
            comboBoxHost.Items.Clear();
            comboBoxHost.Items.Add("＞");
            comboBoxHost.Items.Add("≥");
            comboBoxHost.SelectedIndex = 1;
            comboBoxGuest.Items.Clear();
            comboBoxGuest.Items.Add("＞");
            comboBoxGuest.Items.Add("≥");
            comboBoxGuest.SelectedIndex = 1;

            numericUpDownHost.Value = 0;
            numericUpDownGuest.Value = 0;

            setType0Visible(true);
            setType1Visible(false);
        }

        private void setType0Visible(bool bVisible)
        {
            rangeSetting.Visible = bVisible;
        }

        private void setType1Visible(bool bVisible)
        {
            labelHost.Visible = bVisible;
            labelGuest.Visible = bVisible;
            comboBoxHost.Visible = bVisible;
            comboBoxGuest.Visible = bVisible;
            numericUpDownHost.Visible = bVisible;
            numericUpDownGuest.Visible = bVisible;
        }

        public void SetColorItem(CPModeColorItem cpColorItem)
        {
            this.cpColorItem = cpColorItem;
            if (this.cpColorItem != null)
            {
                switch (cpColorItem.CmpType)
                {
                    case (int)CPModeEditForm.ECmpType.HOST_SUB_GUEST:
                        {
                            fillColorEdit0();
                        }
                        break;
                    case (int)CPModeEditForm.ECmpType.BOTH_STANDARD:
                        {
                            fillColorEdit1();
                        }
                        break;
                    default: break;
                }
            }
        }

        private void fillColorEdit0()
        {
            richTextBoxAlgorithmName.Text = cpColorItem.colorRange.description;
            lbColor.BackColor = cpColorItem.colorRange.color;
            rangeSetting.Range = cpColorItem.range;

            comboBoxCmpType.SelectedIndex = cpColorItem.CmpType;
            groupBoxType.Text = CPModeEditForm.HOST_SUB_GUEST;
            setType0Visible(true);
            setType1Visible(false);
        }

        private void fillColorEdit1()
        {
            comboBoxHost.SelectedIndex = cpColorItem.bHostMinInclude ? 1 : 0;
            comboBoxGuest.SelectedIndex = cpColorItem.bGuestMinInclude ? 1 : 0;
            numericUpDownHost.Value = (decimal)cpColorItem.fHostMin;
            numericUpDownGuest.Value = (decimal)cpColorItem.fGuestMin;
            richTextBoxAlgorithmName.Text = cpColorItem.colorRange.description;
            lbColor.BackColor = cpColorItem.colorRange.color;

            comboBoxCmpType.SelectedIndex = cpColorItem.CmpType;
            groupBoxType.Text = CPModeEditForm.BOTH_STANDARD;
            setType0Visible(false);
            setType1Visible(true);
        }

        private ColorDialog dialog = new ColorDialog();
        private void lbColor_Click(object sender, EventArgs e)
        {
            dialog.Color = lbColor.BackColor;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                lbColor.BackColor = dialog.Color;
            }
        }

        private bool check()
        {
            if (richTextBoxAlgorithmName.Text.Trim() == "")
            {
                MessageBox.Show("请输入此段算法名称", "提示");
                return false;
            }
            else if (lbColor.BackColor == Color.Empty)
            {
                MessageBox.Show("请设置此段算法所用颜色", "提示");
                return false;
            }
            if (comboBoxCmpType.SelectedIndex == 0 && rangeSetting.Range.Max < rangeSetting.Range.Min)
            {
                MessageBox.Show("值域设置有问题", "提示");
                return false;
            }
            return true;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (check())
            {
                cpColorItem.colorRange.color = lbColor.BackColor;
                cpColorItem.colorRange.description = richTextBoxAlgorithmName.Text.Trim();
                cpColorItem.range = rangeSetting.Range;

                cpColorItem.CmpType = comboBoxCmpType.SelectedIndex;
                cpColorItem.fHostMin = (float)numericUpDownHost.Value;
                cpColorItem.fGuestMin = (float)numericUpDownGuest.Value;
                cpColorItem.bHostMinInclude = comboBoxHost.SelectedIndex != 0;
                cpColorItem.bGuestMinInclude = comboBoxGuest.SelectedIndex != 0;
                this.DialogResult = DialogResult.OK;
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void comboBoxCmpType_SelectedIndexChanged(object sender, EventArgs e)
        {
            switch (comboBoxCmpType.SelectedIndex)
            {
                case (int)CPModeEditForm.ECmpType.HOST_SUB_GUEST:
                    setType0Visible(true);
                    setType1Visible(false);
                    break;
                case (int)CPModeEditForm.ECmpType.BOTH_STANDARD:
                    setType0Visible(false);
                    setType1Visible(true);
                    break;
                default:
                    break;
            }
        }
    }
}
