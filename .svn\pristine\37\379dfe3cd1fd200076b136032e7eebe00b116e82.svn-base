﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CreatePolygonLogForm : MinCloseForm
    {
        public CreatePolygonLogForm(MainModel mm, List<string> logs) : base(mm) 
        {
            InitializeComponent();
            DisposeWhenClose = true;
            btnOK.Click += BtnOK_Click;

            StringBuilder sb = new StringBuilder();
            foreach (string line in logs)
            {
                sb.Append(line + Environment.NewLine);
            }
            memoEdit.Text = sb.ToString();
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
