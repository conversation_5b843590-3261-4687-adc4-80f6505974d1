﻿namespace MasterCom.RAMS.CQT
{
    partial class CQTPointInfoDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gcAddrInfo = new DevExpress.XtraEditors.GroupControl();
            this.txtLatitude = new System.Windows.Forms.TextBox();
            this.txtSpaceType = new System.Windows.Forms.TextBox();
            this.txtDensityType = new System.Windows.Forms.TextBox();
            this.txtCoverType = new System.Windows.Forms.TextBox();
            this.txtNetType = new System.Windows.Forms.TextBox();
            this.txtPointType = new System.Windows.Forms.TextBox();
            this.txtLongitude = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.navBarControl = new DevExpress.XtraNavBar.NavBarControl();
            this.panel1 = new System.Windows.Forms.Panel();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupControl4 = new DevExpress.XtraEditors.GroupControl();
            this.dataGridViewAbove = new System.Windows.Forms.DataGridView();
            this.colAboveFileName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAboveKpiValue = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAboveFileScore = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.menuStripAbove = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayAboveFiles = new System.Windows.Forms.ToolStripMenuItem();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.dataGridViewBelow = new System.Windows.Forms.DataGridView();
            this.colBelowFileName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colBelowFileKpiValue = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colBelowScore = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.menuStripBelow = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayBeloveFiles = new System.Windows.Forms.ToolStripMenuItem();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.dataGridViewMain = new System.Windows.Forms.DataGridView();
            this.colKPIValue = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colScore = new System.Windows.Forms.DataGridViewTextBoxColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gcAddrInfo)).BeginInit();
            this.gcAddrInfo.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.navBarControl)).BeginInit();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).BeginInit();
            this.groupControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAbove)).BeginInit();
            this.menuStripAbove.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewBelow)).BeginInit();
            this.menuStripBelow.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewMain)).BeginInit();
            this.SuspendLayout();
            // 
            // gcAddrInfo
            // 
            this.gcAddrInfo.Controls.Add(this.txtLatitude);
            this.gcAddrInfo.Controls.Add(this.txtSpaceType);
            this.gcAddrInfo.Controls.Add(this.txtDensityType);
            this.gcAddrInfo.Controls.Add(this.txtCoverType);
            this.gcAddrInfo.Controls.Add(this.txtNetType);
            this.gcAddrInfo.Controls.Add(this.txtPointType);
            this.gcAddrInfo.Controls.Add(this.txtLongitude);
            this.gcAddrInfo.Controls.Add(this.label5);
            this.gcAddrInfo.Controls.Add(this.label7);
            this.gcAddrInfo.Controls.Add(this.label1);
            this.gcAddrInfo.Controls.Add(this.label6);
            this.gcAddrInfo.Controls.Add(this.label3);
            this.gcAddrInfo.Controls.Add(this.label2);
            this.gcAddrInfo.Controls.Add(this.label4);
            this.gcAddrInfo.Dock = System.Windows.Forms.DockStyle.Top;
            this.gcAddrInfo.Location = new System.Drawing.Point(0, 0);
            this.gcAddrInfo.Name = "gcAddrInfo";
            this.gcAddrInfo.Size = new System.Drawing.Size(600, 126);
            this.gcAddrInfo.TabIndex = 1;
            this.gcAddrInfo.Text = "地点信息";
            // 
            // txtLatitude
            // 
            this.txtLatitude.Location = new System.Drawing.Point(279, 33);
            this.txtLatitude.Name = "txtLatitude";
            this.txtLatitude.ReadOnly = true;
            this.txtLatitude.Size = new System.Drawing.Size(116, 21);
            this.txtLatitude.TabIndex = 1;
            // 
            // txtSpaceType
            // 
            this.txtSpaceType.Location = new System.Drawing.Point(476, 96);
            this.txtSpaceType.Name = "txtSpaceType";
            this.txtSpaceType.ReadOnly = true;
            this.txtSpaceType.Size = new System.Drawing.Size(116, 21);
            this.txtSpaceType.TabIndex = 6;
            // 
            // txtDensityType
            // 
            this.txtDensityType.Location = new System.Drawing.Point(279, 64);
            this.txtDensityType.Name = "txtDensityType";
            this.txtDensityType.ReadOnly = true;
            this.txtDensityType.Size = new System.Drawing.Size(116, 21);
            this.txtDensityType.TabIndex = 3;
            // 
            // txtCoverType
            // 
            this.txtCoverType.Location = new System.Drawing.Point(279, 96);
            this.txtCoverType.Name = "txtCoverType";
            this.txtCoverType.ReadOnly = true;
            this.txtCoverType.Size = new System.Drawing.Size(116, 21);
            this.txtCoverType.TabIndex = 5;
            // 
            // txtNetType
            // 
            this.txtNetType.Location = new System.Drawing.Point(85, 96);
            this.txtNetType.Name = "txtNetType";
            this.txtNetType.ReadOnly = true;
            this.txtNetType.Size = new System.Drawing.Size(116, 21);
            this.txtNetType.TabIndex = 4;
            // 
            // txtPointType
            // 
            this.txtPointType.Location = new System.Drawing.Point(85, 64);
            this.txtPointType.Name = "txtPointType";
            this.txtPointType.ReadOnly = true;
            this.txtPointType.Size = new System.Drawing.Size(116, 21);
            this.txtPointType.TabIndex = 2;
            // 
            // txtLongitude
            // 
            this.txtLongitude.Location = new System.Drawing.Point(85, 33);
            this.txtLongitude.Name = "txtLongitude";
            this.txtLongitude.ReadOnly = true;
            this.txtLongitude.Size = new System.Drawing.Size(116, 21);
            this.txtLongitude.TabIndex = 0;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(407, 103);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(65, 12);
            this.label5.TabIndex = 2;
            this.label5.Text = "建筑类型：";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(209, 103);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(65, 12);
            this.label7.TabIndex = 2;
            this.label7.Text = "覆盖类型：";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(236, 36);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 2;
            this.label1.Text = "纬度：";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(14, 99);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(65, 12);
            this.label6.TabIndex = 2;
            this.label6.Text = "网络类型：";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(209, 71);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 2;
            this.label3.Text = "密度类型：";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(14, 68);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 2;
            this.label2.Text = "地点类型：";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(42, 36);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(41, 12);
            this.label4.TabIndex = 2;
            this.label4.Text = "经度：";
            // 
            // navBarControl
            // 
            this.navBarControl.ActiveGroup = null;
            this.navBarControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.navBarControl.Location = new System.Drawing.Point(0, 126);
            this.navBarControl.Name = "navBarControl";
            this.navBarControl.OptionsNavPane.ExpandedWidth = 513;
            this.navBarControl.Size = new System.Drawing.Size(600, 360);
            this.navBarControl.TabIndex = 0;
            this.navBarControl.Text = "navBarControl1";
            // 
            // panel1
            // 
            this.panel1.AutoScroll = true;
            this.panel1.Controls.Add(this.groupControl1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(0, 126);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(600, 360);
            this.panel1.TabIndex = 2;
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.groupControl4);
            this.groupControl1.Controls.Add(this.groupControl3);
            this.groupControl1.Controls.Add(this.groupControl2);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(583, 484);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "指标信息";
            // 
            // groupControl4
            // 
            this.groupControl4.Controls.Add(this.dataGridViewAbove);
            this.groupControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl4.Location = new System.Drawing.Point(2, 292);
            this.groupControl4.Name = "groupControl4";
            this.groupControl4.Size = new System.Drawing.Size(579, 190);
            this.groupControl4.TabIndex = 3;
            this.groupControl4.Text = "高于等于总体水平指标信息";
            // 
            // dataGridViewAbove
            // 
            this.dataGridViewAbove.AllowUserToAddRows = false;
            this.dataGridViewAbove.AllowUserToDeleteRows = false;
            this.dataGridViewAbove.BackgroundColor = System.Drawing.SystemColors.ButtonHighlight;
            this.dataGridViewAbove.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewAbove.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.colAboveFileName,
            this.colAboveKpiValue,
            this.colAboveFileScore});
            this.dataGridViewAbove.ContextMenuStrip = this.menuStripAbove;
            this.dataGridViewAbove.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewAbove.Location = new System.Drawing.Point(2, 23);
            this.dataGridViewAbove.Name = "dataGridViewAbove";
            this.dataGridViewAbove.RowHeadersVisible = false;
            this.dataGridViewAbove.RowTemplate.Height = 23;
            this.dataGridViewAbove.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewAbove.Size = new System.Drawing.Size(575, 165);
            this.dataGridViewAbove.TabIndex = 2;
            // 
            // colAboveFileName
            // 
            this.colAboveFileName.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.colAboveFileName.HeaderText = "文件名";
            this.colAboveFileName.Name = "colAboveFileName";
            // 
            // colAboveKpiValue
            // 
            this.colAboveKpiValue.HeaderText = "指标";
            this.colAboveKpiValue.Name = "colAboveKpiValue";
            // 
            // colAboveFileScore
            // 
            this.colAboveFileScore.HeaderText = "评分";
            this.colAboveFileScore.Name = "colAboveFileScore";
            // 
            // menuStripAbove
            // 
            this.menuStripAbove.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayAboveFiles});
            this.menuStripAbove.Name = "menuStripAbove";
            this.menuStripAbove.Size = new System.Drawing.Size(134, 26);
            // 
            // miReplayAboveFiles
            // 
            this.miReplayAboveFiles.Name = "miReplayAboveFiles";
            this.miReplayAboveFiles.Size = new System.Drawing.Size(133, 22);
            this.miReplayAboveFiles.Text = "回放文件...";
            this.miReplayAboveFiles.Click += new System.EventHandler(this.miReplayAboveFiles_Click);
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.dataGridViewBelow);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl3.Location = new System.Drawing.Point(2, 105);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(579, 187);
            this.groupControl3.TabIndex = 2;
            this.groupControl3.Text = "低于总体水平指标信息";
            // 
            // dataGridViewBelow
            // 
            this.dataGridViewBelow.AllowUserToAddRows = false;
            this.dataGridViewBelow.AllowUserToDeleteRows = false;
            this.dataGridViewBelow.BackgroundColor = System.Drawing.SystemColors.ButtonHighlight;
            this.dataGridViewBelow.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewBelow.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.colBelowFileName,
            this.colBelowFileKpiValue,
            this.colBelowScore});
            this.dataGridViewBelow.ContextMenuStrip = this.menuStripBelow;
            this.dataGridViewBelow.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewBelow.Location = new System.Drawing.Point(2, 23);
            this.dataGridViewBelow.Name = "dataGridViewBelow";
            this.dataGridViewBelow.RowHeadersVisible = false;
            this.dataGridViewBelow.RowTemplate.Height = 23;
            this.dataGridViewBelow.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewBelow.Size = new System.Drawing.Size(575, 162);
            this.dataGridViewBelow.TabIndex = 1;
            // 
            // colBelowFileName
            // 
            this.colBelowFileName.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.colBelowFileName.HeaderText = "文件名";
            this.colBelowFileName.Name = "colBelowFileName";
            // 
            // colBelowFileKpiValue
            // 
            this.colBelowFileKpiValue.HeaderText = "指标";
            this.colBelowFileKpiValue.Name = "colBelowFileKpiValue";
            // 
            // colBelowScore
            // 
            this.colBelowScore.HeaderText = "评分";
            this.colBelowScore.Name = "colBelowScore";
            // 
            // menuStripBelow
            // 
            this.menuStripBelow.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayBeloveFiles});
            this.menuStripBelow.Name = "menuStripBelow";
            this.menuStripBelow.Size = new System.Drawing.Size(134, 26);
            // 
            // miReplayBeloveFiles
            // 
            this.miReplayBeloveFiles.Name = "miReplayBeloveFiles";
            this.miReplayBeloveFiles.Size = new System.Drawing.Size(133, 22);
            this.miReplayBeloveFiles.Text = "回放文件...";
            this.miReplayBeloveFiles.Click += new System.EventHandler(this.miReplayBeloveFiles_Click);
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.dataGridViewMain);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl2.Location = new System.Drawing.Point(2, 23);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(579, 82);
            this.groupControl2.TabIndex = 1;
            this.groupControl2.Text = "总体指标";
            // 
            // dataGridViewMain
            // 
            this.dataGridViewMain.AllowUserToAddRows = false;
            this.dataGridViewMain.AllowUserToDeleteRows = false;
            this.dataGridViewMain.BackgroundColor = System.Drawing.SystemColors.ButtonHighlight;
            this.dataGridViewMain.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewMain.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.colKPIValue,
            this.colScore});
            this.dataGridViewMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewMain.Location = new System.Drawing.Point(2, 23);
            this.dataGridViewMain.Name = "dataGridViewMain";
            this.dataGridViewMain.RowHeadersVisible = false;
            this.dataGridViewMain.RowTemplate.Height = 23;
            this.dataGridViewMain.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewMain.Size = new System.Drawing.Size(575, 57);
            this.dataGridViewMain.TabIndex = 0;
            // 
            // colKPIValue
            // 
            this.colKPIValue.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.colKPIValue.HeaderText = "指标";
            this.colKPIValue.Name = "colKPIValue";
            // 
            // colScore
            // 
            this.colScore.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.colScore.HeaderText = "评分";
            this.colScore.Name = "colScore";
            // 
            // CQTPointInfoDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(600, 486);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.navBarControl);
            this.Controls.Add(this.gcAddrInfo);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.Name = "CQTPointInfoDlg";
            this.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
            this.Text = "CQTPointInfoDlg";
            this.Deactivate += new System.EventHandler(this.CQTPointInfoDlg_Deactivate);
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.CQTPointInfoDlg_FormClosing);
            ((System.ComponentModel.ISupportInitialize)(this.gcAddrInfo)).EndInit();
            this.gcAddrInfo.ResumeLayout(false);
            this.gcAddrInfo.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.navBarControl)).EndInit();
            this.panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).EndInit();
            this.groupControl4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAbove)).EndInit();
            this.menuStripAbove.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewBelow)).EndInit();
            this.menuStripBelow.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewMain)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl gcAddrInfo;
        private System.Windows.Forms.TextBox txtLatitude;
        private System.Windows.Forms.TextBox txtLongitude;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox txtDensityType;
        private System.Windows.Forms.TextBox txtPointType;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox txtSpaceType;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtCoverType;
        private System.Windows.Forms.TextBox txtNetType;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private DevExpress.XtraNavBar.NavBarControl navBarControl;
        private System.Windows.Forms.Panel panel1;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private DevExpress.XtraEditors.GroupControl groupControl4;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private System.Windows.Forms.DataGridView dataGridViewAbove;
        private System.Windows.Forms.DataGridView dataGridViewBelow;
        private System.Windows.Forms.DataGridView dataGridViewMain;
        private System.Windows.Forms.DataGridViewTextBoxColumn colBelowFileName;
        private System.Windows.Forms.DataGridViewTextBoxColumn colBelowFileKpiValue;
        private System.Windows.Forms.DataGridViewTextBoxColumn colBelowScore;
        private System.Windows.Forms.DataGridViewTextBoxColumn colKPIValue;
        private System.Windows.Forms.DataGridViewTextBoxColumn colScore;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAboveFileName;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAboveKpiValue;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAboveFileScore;
        private System.Windows.Forms.ContextMenuStrip menuStripBelow;
        private System.Windows.Forms.ToolStripMenuItem miReplayBeloveFiles;
        private System.Windows.Forms.ContextMenuStrip menuStripAbove;
        private System.Windows.Forms.ToolStripMenuItem miReplayAboveFiles;
    }
}