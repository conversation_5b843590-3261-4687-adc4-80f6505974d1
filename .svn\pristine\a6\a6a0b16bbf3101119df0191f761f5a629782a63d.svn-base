﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ExMap
{
    public static class ExMapUtil
    {
        class ExMapZoomLevelScale
        { 
            public double MaxScale { get; set; }
            public int Level { get; set; }

            public ExMapZoomLevelScale(double maxScale, int level)
            {
                MaxScale = maxScale;
                Level = level;
            }
        }

        private static List<ExMapZoomLevelScale> levelScaleList = init();

        private static List<ExMapZoomLevelScale> init()
        {
            List<ExMapZoomLevelScale> list = new List<ExMapZoomLevelScale>();
            list.Add(new ExMapZoomLevelScale(1580, 18));
            list.Add(new ExMapZoomLevelScale(3780, 17));
            list.Add(new ExMapZoomLevelScale(7839, 16));
            list.Add(new ExMapZoomLevelScale(19508, 15));
            list.Add(new ExMapZoomLevelScale(33713, 14));
            list.Add(new ExMapZoomLevelScale(208700, 13));
            list.Add(new ExMapZoomLevelScale(360000, 12));
            list.Add(new ExMapZoomLevelScale(390909, 11));
            list.Add(new ExMapZoomLevelScale(421818, 10));
            list.Add(new ExMapZoomLevelScale(452727, 9));
            list.Add(new ExMapZoomLevelScale(483636, 8));
            list.Add(new ExMapZoomLevelScale(514545, 7));
            list.Add(new ExMapZoomLevelScale(545454, 6));
            list.Add(new ExMapZoomLevelScale(576363, 5));
            list.Add(new ExMapZoomLevelScale(607272, 4));
            list.Add(new ExMapZoomLevelScale(638181, 3));
            list.Add(new ExMapZoomLevelScale(700000, 2));
            return list;
        }

        public static int parseNearZoomLevelByScale(double scale)
        {
            foreach (var item in levelScaleList)
            {
                if (scale <= item.MaxScale)
                {
                    return item.Level;
                }
            }
            return 2;
        }

        public static double parseScaleFromZoomLevel(double zoomLevel)
        {
            int zLevel = (int)zoomLevel;
            switch (zLevel)
            {
                case 1:
                    return 700000;
                case 2:
                    return 700000;
                case 3:
                    return 638181;
                case 4:
                    return 607272;
                case 5:
                    return 576363;
                case 6:
                    return 545454;
                case 7:
                    return 514545;
                case 8:
                    return 483636;
                case 9:
                    return 452727;
                case 10:
                    return 421818;
                case 11:
                    return 390909;
                case 12:
                    return 360000;
                case 13:
                    return 208700;
                case 14:
                    return 33713;
                case 15:
                    return 19508;
                case 16:
                    return 7839;
                case 17:
                    return 3780;
                case 18:
                    return 1580;
                case 19:
                    return 1580;
                case 20:
                    return 1580;
                default:
                    return 1580;
            }
        }
    }
}
