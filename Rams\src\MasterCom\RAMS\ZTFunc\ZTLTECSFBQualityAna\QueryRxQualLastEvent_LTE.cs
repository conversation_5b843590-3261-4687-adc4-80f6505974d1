﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.MTGis;
using DevExpress.XtraEditors;
namespace MasterCom.RAMS.ZTFunc
{
    public class QueryRxQualLastEvent_LTE : QueryRxQualLastEvent
    {
        protected RxQualLastEventInfoForm_LTE resultForm = null;
        public QueryRxQualLastEvent_LTE(MainModel mainModel, bool isNewStandard)
            : base(mainModel, isNewStandard)
        {
        }

        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }
        protected override DIYReplaySampleWithinPeriodQuery getSampleQueryer(MainModel md)
        {
            return new DIYReplaySampleWithinPeriodQuery_LTE(md);
        }
        protected override QueryRxQualLastEventByRegion getQueryerByRegion()
        {
            return new QueryRxQualLastEventByRegion_LTE(MainModel, isNewStandard);
        }
        protected override QueryRxQualLastEventByAllRegion getQueryerByAllRegion()
        {
            return new QueryRxQualLastEventByAllRegion_LTE(MainModel, isNewStandard);
        }
        protected override void showResult(List<Event> events, Dictionary<Event, List<TestPoint>> ePointDic)
        {
            if (this.resultForm == null || this.resultForm.IsDisposed)
            {
                this.resultForm = new RxQualLastEventInfoForm_LTE(MainModel.GetInstance());
            }
            resultForm.showEventInfo(events, ePointDic, this.Condition);
            resultForm.Owner = MainModel.MainForm;
            resultForm.Show();
            resultForm.Visible = true;
        }
    }

    public class QueryRxQualLastEvent_LTE_FDD : QueryRxQualLastEvent_LTE
    {
        public QueryRxQualLastEvent_LTE_FDD()
            : base(MainModel.GetInstance(), true)
        {

        }
        protected override void showResult(List<Event> events, Dictionary<Event, List<TestPoint>> ePointDic)
        {
            if (this.resultForm == null || this.resultForm.IsDisposed)
            {
                this.resultForm = new RxQualLastEventInfoForm_LTE_FDD(MainModel.GetInstance());
            }
            resultForm.showEventInfo(events, ePointDic, this.Condition);
            resultForm.Owner = MainModel.MainForm;
            resultForm.Show();
            resultForm.Visible = true;
        }
    }
    class QueryRxQualLastEventByRegion_LTE : QueryRxQualLastEventByRegion
    {
        public QueryRxQualLastEventByRegion_LTE(MainModel mainModel, bool isNewStandard)
            : base(mainModel,isNewStandard)
        {
        }
        public override string Name
        {
            get { return "按区域查询持续质差"; }
        }

        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }
        protected override bool prepareAskWhatEvent()
        {
            if (Condition.EventIDs == null)
            {
                Condition.EventIDs = new List<int>();
            }
            Condition.EventIDs.Add(1191);//质差
            return true;
        }
    }


    public class QueryRxQualLastEventByAllRegion_LTE : QueryRxQualLastEventByAllRegion
    {
        public QueryRxQualLastEventByAllRegion_LTE(MainModel mainModel, bool isNewStandard)
            : base(mainModel, isNewStandard)
        {
             
        }

        protected override bool prepareAskWhatEvent()
        {
            if (Condition.EventIDs == null)
            {
                Condition.EventIDs = new List<int>();
            }
            Condition.EventIDs.Add(1191);//质差
            return true;
        }
         
    }

    public class RxQualLastEventInfoForm_LTE : RxQualLastEventInfoForm
    {
        public RxQualLastEventInfoForm_LTE(MainModel mainModel)
            : base()
        {
            this.initForLTE();
        }
        protected override void getParam(TestPoint tp, out short? rxlev, out short? ci, out byte? rxqual)
        {
            rxlev = (short?)tp["lte_gsm_DM_RxLevSub"];
            ci = null;
            rxqual = (byte?)tp["lte_gsm_DM_RxQualSub"];
        }
    }

    public class RxQualLastEventInfoForm_LTE_FDD : RxQualLastEventInfoForm_LTE
    {
        public RxQualLastEventInfoForm_LTE_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }

        protected void getParamFdd(TestPoint tp, out float? rxlev, out short? ci, out float? rxqual)
        {
            rxlev = (float?)tp["lte_fdd_wcdma_TotalRSCP"];
            ci = null;
            rxqual = (float?)tp["lte_fdd_wcdma_TotalEc_Io"];
        }
        protected override void getExtInfo(List<TestPoint> tpList, TPInfo tpInfo)
        {
            if (tpList.Count <= 0)
            {
                return;
            }
            try
            {
                foreach (TestPoint tp in tpList)
                {
                    float? rxlev, rxqual;
                    short? ci;
                    this.getParamFdd(tp, out rxlev, out ci, out rxqual);
                    if (rxlev != null && (rxlev <= -10 && rxlev >= -140))
                    {
                        tpInfo.rxlevMean += (float)rxlev;
                        tpInfo.rxlevMax = getMaxData((float)rxlev, tpInfo.rxlevMax);
                        tpInfo.rxlevMin = getMinData((float)rxlev, tpInfo.rxlevMin);
                    }
                    if (ci != null && (ci >= -25 && ci <= 25))
                    {
                        tpInfo.ciMean += (float)ci;
                        tpInfo.ciMax = getMaxData((float)ci, tpInfo.ciMax);
                        tpInfo.ciMin = getMinData((float)ci, tpInfo.ciMin);
                    }
                    if (rxqual != 0 && (rxqual >= 0 && rxqual <= 7))
                    {
                        tpInfo.rxqualMean += (float)rxqual;
                        tpInfo.rxqualMax = getMaxData((float)rxqual, tpInfo.rxqualMax);
                        tpInfo.rxqualMin = getMinData((float)rxqual, tpInfo.rxqualMax);
                    }
                }

                tpInfo.rxlevMean /= tpList.Count;
                tpInfo.ciMean /= tpList.Count;
                tpInfo.rxqualMean /= tpList.Count;
            }
            catch
            {
                //continue
            }
        }
    }

    public class DIYReplaySampleWithinPeriodQuery_LTE : DIYReplaySampleWithinPeriodQuery
    {
        public DIYReplaySampleWithinPeriodQuery_LTE(MainModel mainModel)
            : base(mainModel)
        {
        }
        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = base.getDIYReplayContent();

            //option.DefaultSerialThemeName = "GSM RxQual"; //要求指标名叫GSM RxQual才能显示出采样点    **不知道什么意思
            ColumnDefItem item = InterfaceManager.GetInstance().GetColumnDef(31, 8, 53);//GsmBaseInfo : lte_gsm_DM_RxLevSub
            option.SampleColumns.Add(item);
            item = InterfaceManager.GetInstance().GetColumnDef(31, 12,53);//GsmBaseInfo  : lte_gsm_DM_RxQualSub
            option.SampleColumns.Add(item);
            return option;
        }
    }
}
