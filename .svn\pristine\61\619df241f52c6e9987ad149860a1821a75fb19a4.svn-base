﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    //CustomDefine
    public static class CD
    {
#if GridSize_30
        /// <summary>
        /// 30*30米大小栅格对应的经度跨度为0.0003
        /// </summary>
        public static double ATOM_SPAN_LONG { get; set; } = 0.0003;
        /// <summary>
        /// 30*30米大小栅格对应的经度跨度为0.00027
        /// </summary>
        public static double ATOM_SPAN_LAT { get; set; } = 0.00027;
#elif GridSize_100
        /// <summary>
        /// 100*100米大小栅格对应的经度跨度为0.001
        /// </summary>
        public static double ATOM_SPAN_LONG { get; set; } = 0.001;
        /// <summary>
        /// 100*100米大小栅格对应的经度跨度为0.0009
        /// </summary>
        public static double ATOM_SPAN_LAT { get; set; } = 0.0009;
#else
        /// <summary>
        /// 40*40米大小栅格对应的经度跨度为0.0004
        /// </summary>
        public static double ATOM_SPAN_LONG { get; set; } = 0.0004;
        /// <summary>
        /// 40*40米大小栅格对应的纬度度跨度为0.00036
        /// </summary>
        public static double ATOM_SPAN_LAT { get; set; } = 0.00036;
#endif

#if NMG
        public static int MAX_COV_DISTANCE_GSM { get; set; } = 32000;
        public static int MAX_COV_DISTANCE_TD { get; set; } = 300000;
        public static int MAX_COV_DISTANCE_W { get; set; } = 300000;
        public static int MAX_COV_DISTANCE_CD { get; set; } = 300000;
#elif Dongguan || Guangzhou
        public static int MAX_COV_DISTANCE_GSM { get; set; } = 5000;
        public static int MAX_COV_DISTANCE_TD { get; set; } = 5000;
        public static int MAX_COV_DISTANCE_W { get; set; } = 5000;
        public static int MAX_COV_DISTANCE_CD { get; set; } = 5000;
#elif ShanxiJin
        public static int MAX_COV_DISTANCE_GSM { get; set; } = 8000;
        public static int MAX_COV_DISTANCE_TD { get; set; } = 8000;
        public static int MAX_COV_DISTANCE_W { get; set; } = 300000;
        public static int MAX_COV_DISTANCE_CD { get; set; } = 300000;
#elif XJLT||ZJLT||SZLT
        public static int MAX_COV_DISTANCE_GSM { get; set; } = 5000;
        public static int MAX_COV_DISTANCE_TD { get; set; } = 5000;
        public static int MAX_COV_DISTANCE_W { get; set; } = 10000;
        public static int MAX_COV_DISTANCE_CD { get; set; } = 5000;

#elif Shanxi
        public static int MAX_COV_DISTANCE_GSM { get; set; } = 3000;
        public static int MAX_COV_DISTANCE_TD { get; set; } = 3000;
        public static int MAX_COV_DISTANCE_W { get; set; } = 300000;
        public static int MAX_COV_DISTANCE_CD { get; set; } = 300000;
#else
        public static int MAX_COV_DISTANCE_GSM { get; set; } = 3000;
        public static int MAX_COV_DISTANCE_TD { get; set; } = 5000;
        public static int MAX_COV_DISTANCE_W { get; set; } = 300000;
        public static int MAX_COV_DISTANCE_CD { get; set; } = 300000;
#endif

        public static int MAX_COV_DISTANCE_LTE { get; set; } = 3000;

        public static int MAX_COV_DISTANCE_NR { get; set; } = 2000;
        /// <summary>
        /// 小区箭头长度显示比例。
        /// </summary>
        public static float CellLengthRadio { get; set; } = 1.0f;

        /// <summary>
        /// TD小区箭头长度显示比例。
        /// </summary>
        public static float CellLengthRadio_TD { get; set; } = 1.0f;

        /// <summary>
        /// W小区箭头长度显示比例。
        /// </summary>
        public static float CellLengthRadio_W { get; set; } = 1.0f;

        /// <summary>
        /// 道路栅格，默认为40*40米大小
        /// </summary>
        public static double RoadGridLngSpan { get; set; } = 0.0004;

        /// <summary>
        /// 道路栅格，默认为40*40米大小
        /// </summary>
        public static double RoadGridLatSpan { get; set; } = 0.00036;

    }
}
