﻿namespace MasterCom.ES.UI
{
    partial class ESProcessForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ESProcessForm));
            this.rptRichBox = new System.Windows.Forms.RichTextBox();
            this.ctxConfigMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miOpenConfig = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miContinue = new System.Windows.Forms.ToolStripMenuItem();
            this.bgWorker = new System.ComponentModel.BackgroundWorker();
            this.bgContinueWorker = new System.ComponentModel.BackgroundWorker();
            this.bgStepOverWorker = new System.ComponentModel.BackgroundWorker();
            this.bgStepInWorker = new System.ComponentModel.BackgroundWorker();
            this.tabControlMain = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.panelPreType = new System.Windows.Forms.Panel();
            this.label1 = new System.Windows.Forms.Label();
            this.dataGridPreType = new System.Windows.Forms.DataGridView();
            this.Column1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.panelReason = new System.Windows.Forms.Panel();
            this.label2 = new System.Windows.Forms.Label();
            this.dataGridReason = new System.Windows.Forms.DataGridView();
            this.dataGridViewTextBoxColumn1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.panel1 = new System.Windows.Forms.Panel();
            this.label4 = new System.Windows.Forms.Label();
            this.dataGridPerformance = new System.Windows.Forms.DataGridView();
            this.dataGridViewTextBoxColumn3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.panelSolution = new System.Windows.Forms.Panel();
            this.label3 = new System.Windows.Forms.Label();
            this.dataGridSolution = new System.Windows.Forms.DataGridView();
            this.dataGridViewTextBoxColumn2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.dataGridView1 = new System.Windows.Forms.DataGridView();
            this.bgPerformanceWorker = new System.ComponentModel.BackgroundWorker();
            this.ctxConfigMenu.SuspendLayout();
            this.tabControlMain.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tableLayoutPanel1.SuspendLayout();
            this.panelPreType.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridPreType)).BeginInit();
            this.panelReason.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridReason)).BeginInit();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridPerformance)).BeginInit();
            this.panelSolution.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridSolution)).BeginInit();
            this.tabPage2.SuspendLayout();
            this.tabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // rptRichBox
            // 
            this.rptRichBox.ContextMenuStrip = this.ctxConfigMenu;
            this.rptRichBox.Dock = System.Windows.Forms.DockStyle.Fill;
            this.rptRichBox.Location = new System.Drawing.Point(3, 3);
            this.rptRichBox.Name = "rptRichBox";
            this.rptRichBox.Size = new System.Drawing.Size(601, 542);
            this.rptRichBox.TabIndex = 0;
            this.rptRichBox.Text = "";
            // 
            // ctxConfigMenu
            // 
            this.ctxConfigMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miOpenConfig,
            this.toolStripMenuItem1,
            this.miContinue});
            this.ctxConfigMenu.Name = "ctxConfigMenu";
            this.ctxConfigMenu.Size = new System.Drawing.Size(182, 54);
            // 
            // miOpenConfig
            // 
            this.miOpenConfig.Name = "miOpenConfig";
            this.miOpenConfig.Size = new System.Drawing.Size(181, 22);
            this.miOpenConfig.Text = "打开流程配置界面...";
            this.miOpenConfig.Click += new System.EventHandler(this.miOpenConfig_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(178, 6);
            // 
            // miContinue
            // 
            this.miContinue.Name = "miContinue";
            this.miContinue.Size = new System.Drawing.Size(181, 22);
            this.miContinue.Text = "继续运行流程";
            this.miContinue.Click += new System.EventHandler(this.miContinue_Click);
            // 
            // bgWorker
            // 
            this.bgWorker.WorkerReportsProgress = true;
            this.bgWorker.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgWorker_DoWork);
            this.bgWorker.ProgressChanged += new System.ComponentModel.ProgressChangedEventHandler(this.bgWorker_ProgressChanged);
            this.bgWorker.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bgWorker_RunWorkerCompleted);
            // 
            // bgContinueWorker
            // 
            this.bgContinueWorker.WorkerReportsProgress = true;
            this.bgContinueWorker.WorkerSupportsCancellation = true;
            this.bgContinueWorker.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgContinueWorker_DoWork);
            this.bgContinueWorker.ProgressChanged += new System.ComponentModel.ProgressChangedEventHandler(this.bgContinueWorker_ProgressChanged);
            this.bgContinueWorker.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bgContinueWorker_RunWorkerCompleted);
            // 
            // bgStepOverWorker
            // 
            this.bgStepOverWorker.WorkerReportsProgress = true;
            this.bgStepOverWorker.WorkerSupportsCancellation = true;
            this.bgStepOverWorker.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgStepOverWorker_DoWork);
            this.bgStepOverWorker.ProgressChanged += new System.ComponentModel.ProgressChangedEventHandler(this.bgStepOverWorker_ProgressChanged);
            this.bgStepOverWorker.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bgStepOverWorker_RunWorkerCompleted);
            // 
            // bgStepInWorker
            // 
            this.bgStepInWorker.WorkerReportsProgress = true;
            this.bgStepInWorker.WorkerSupportsCancellation = true;
            this.bgStepInWorker.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgStepInWorker_DoWork);
            this.bgStepInWorker.ProgressChanged += new System.ComponentModel.ProgressChangedEventHandler(this.bgStepInWorker_ProgressChanged);
            this.bgStepInWorker.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bgStepInWorker_RunWorkerCompleted);
            // 
            // tabControlMain
            // 
            this.tabControlMain.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tabControlMain.Controls.Add(this.tabPage1);
            this.tabControlMain.Controls.Add(this.tabPage2);
            this.tabControlMain.Controls.Add(this.tabPage3);
            this.tabControlMain.Location = new System.Drawing.Point(3, 3);
            this.tabControlMain.Name = "tabControlMain";
            this.tabControlMain.SelectedIndex = 0;
            this.tabControlMain.Size = new System.Drawing.Size(615, 575);
            this.tabControlMain.TabIndex = 2;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.tableLayoutPanel1);
            this.tabPage1.Location = new System.Drawing.Point(4, 23);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(607, 548);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "判断结果";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 1;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Controls.Add(this.panelPreType, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.panelReason, 0, 1);
            this.tableLayoutPanel1.Controls.Add(this.panel1, 0, 2);
            this.tableLayoutPanel1.Controls.Add(this.panelSolution, 0, 3);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Location = new System.Drawing.Point(3, 3);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 4;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.Size = new System.Drawing.Size(601, 542);
            this.tableLayoutPanel1.TabIndex = 4;
            // 
            // panelPreType
            // 
            this.panelPreType.Controls.Add(this.label1);
            this.panelPreType.Controls.Add(this.dataGridPreType);
            this.panelPreType.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelPreType.Location = new System.Drawing.Point(3, 3);
            this.panelPreType.Name = "panelPreType";
            this.panelPreType.Size = new System.Drawing.Size(595, 127);
            this.panelPreType.TabIndex = 1;
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.label1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(192)))));
            this.label1.Location = new System.Drawing.Point(2, 3);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(28, 124);
            this.label1.TabIndex = 2;
            this.label1.Text = "预\r\n判\r\n类\r\n型";
            this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // dataGridPreType
            // 
            this.dataGridPreType.AllowUserToAddRows = false;
            this.dataGridPreType.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridPreType.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridPreType.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridPreType.ColumnHeadersVisible = false;
            this.dataGridPreType.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.Column1});
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.GradientActiveCaption;
            dataGridViewCellStyle1.SelectionForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridPreType.DefaultCellStyle = dataGridViewCellStyle1;
            this.dataGridPreType.EditMode = System.Windows.Forms.DataGridViewEditMode.EditProgrammatically;
            this.dataGridPreType.Location = new System.Drawing.Point(31, 3);
            this.dataGridPreType.Name = "dataGridPreType";
            this.dataGridPreType.RowHeadersVisible = false;
            this.dataGridPreType.RowTemplate.DefaultCellStyle.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridPreType.RowTemplate.Height = 23;
            this.dataGridPreType.Size = new System.Drawing.Size(560, 124);
            this.dataGridPreType.TabIndex = 0;
            // 
            // Column1
            // 
            this.Column1.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.Column1.HeaderText = "预判名称";
            this.Column1.Name = "Column1";
            // 
            // panelReason
            // 
            this.panelReason.Controls.Add(this.label2);
            this.panelReason.Controls.Add(this.dataGridReason);
            this.panelReason.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelReason.Location = new System.Drawing.Point(3, 136);
            this.panelReason.Name = "panelReason";
            this.panelReason.Size = new System.Drawing.Size(595, 127);
            this.panelReason.TabIndex = 2;
            // 
            // label2
            // 
            this.label2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.label2.BackColor = System.Drawing.Color.Thistle;
            this.label2.Location = new System.Drawing.Point(2, 3);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(28, 124);
            this.label2.TabIndex = 2;
            this.label2.Text = "判\r\n断\r\n原\r\n因\r\n分\r\n析\r\n";
            this.label2.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // dataGridReason
            // 
            this.dataGridReason.AllowUserToAddRows = false;
            this.dataGridReason.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridReason.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridReason.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridReason.ColumnHeadersVisible = false;
            this.dataGridReason.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.dataGridViewTextBoxColumn1});
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.GradientActiveCaption;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridReason.DefaultCellStyle = dataGridViewCellStyle2;
            this.dataGridReason.EditMode = System.Windows.Forms.DataGridViewEditMode.EditProgrammatically;
            this.dataGridReason.Location = new System.Drawing.Point(31, 3);
            this.dataGridReason.Name = "dataGridReason";
            this.dataGridReason.RowHeadersVisible = false;
            this.dataGridReason.RowTemplate.DefaultCellStyle.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridReason.RowTemplate.Height = 30;
            this.dataGridReason.RowTemplate.Resizable = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridReason.Size = new System.Drawing.Size(560, 124);
            this.dataGridReason.TabIndex = 0;
            // 
            // dataGridViewTextBoxColumn1
            // 
            this.dataGridViewTextBoxColumn1.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.dataGridViewTextBoxColumn1.HeaderText = "原因描述";
            this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.label4);
            this.panel1.Controls.Add(this.dataGridPerformance);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(3, 269);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(595, 127);
            this.panel1.TabIndex = 3;
            // 
            // label4
            // 
            this.label4.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.label4.BackColor = System.Drawing.Color.PowderBlue;
            this.label4.Location = new System.Drawing.Point(2, 3);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(28, 124);
            this.label4.TabIndex = 2;
            this.label4.Text = "性能关联分析";
            this.label4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // dataGridPerformance
            // 
            this.dataGridPerformance.AllowUserToAddRows = false;
            this.dataGridPerformance.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridPerformance.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridPerformance.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridPerformance.ColumnHeadersVisible = false;
            this.dataGridPerformance.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.dataGridViewTextBoxColumn3});
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle3.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.SystemColors.GradientActiveCaption;
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridPerformance.DefaultCellStyle = dataGridViewCellStyle3;
            this.dataGridPerformance.EditMode = System.Windows.Forms.DataGridViewEditMode.EditProgrammatically;
            this.dataGridPerformance.Location = new System.Drawing.Point(31, 3);
            this.dataGridPerformance.Name = "dataGridPerformance";
            this.dataGridPerformance.RowHeadersVisible = false;
            this.dataGridPerformance.RowTemplate.DefaultCellStyle.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridPerformance.RowTemplate.Height = 23;
            this.dataGridPerformance.Size = new System.Drawing.Size(560, 124);
            this.dataGridPerformance.TabIndex = 0;
            // 
            // dataGridViewTextBoxColumn3
            // 
            this.dataGridViewTextBoxColumn3.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.dataGridViewTextBoxColumn3.HeaderText = "预判名称";
            this.dataGridViewTextBoxColumn3.Name = "dataGridViewTextBoxColumn3";
            // 
            // panelSolution
            // 
            this.panelSolution.Controls.Add(this.label3);
            this.panelSolution.Controls.Add(this.dataGridSolution);
            this.panelSolution.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelSolution.Location = new System.Drawing.Point(3, 402);
            this.panelSolution.Name = "panelSolution";
            this.panelSolution.Size = new System.Drawing.Size(595, 137);
            this.panelSolution.TabIndex = 3;
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.label3.BackColor = System.Drawing.Color.PaleGreen;
            this.label3.Location = new System.Drawing.Point(2, 3);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(28, 134);
            this.label3.TabIndex = 2;
            this.label3.Text = "解决方案";
            this.label3.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // dataGridSolution
            // 
            this.dataGridSolution.AllowUserToAddRows = false;
            this.dataGridSolution.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridSolution.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridSolution.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridSolution.ColumnHeadersVisible = false;
            this.dataGridSolution.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.dataGridViewTextBoxColumn2});
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle4.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.SystemColors.GradientActiveCaption;
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.Black;
            dataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridSolution.DefaultCellStyle = dataGridViewCellStyle4;
            this.dataGridSolution.EditMode = System.Windows.Forms.DataGridViewEditMode.EditProgrammatically;
            this.dataGridSolution.Location = new System.Drawing.Point(31, 3);
            this.dataGridSolution.Name = "dataGridSolution";
            this.dataGridSolution.RowHeadersVisible = false;
            this.dataGridSolution.RowTemplate.DefaultCellStyle.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridSolution.RowTemplate.Height = 30;
            this.dataGridSolution.Size = new System.Drawing.Size(560, 134);
            this.dataGridSolution.TabIndex = 0;
            // 
            // dataGridViewTextBoxColumn2
            // 
            this.dataGridViewTextBoxColumn2.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.dataGridViewTextBoxColumn2.HeaderText = "描述";
            this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.rptRichBox);
            this.tabPage2.Location = new System.Drawing.Point(4, 23);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(607, 548);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "详细过程";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.dataGridView1);
            this.tabPage3.Location = new System.Drawing.Point(4, 23);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Size = new System.Drawing.Size(607, 548);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "性能详情";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // dataGridView1
            // 
            this.dataGridView1.AllowUserToAddRows = false;
            this.dataGridView1.AllowUserToDeleteRows = false;
            this.dataGridView1.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridView1.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridView1.Location = new System.Drawing.Point(0, 0);
            this.dataGridView1.Name = "dataGridView1";
            this.dataGridView1.ReadOnly = true;
            this.dataGridView1.RowHeadersVisible = false;
            this.dataGridView1.RowTemplate.Height = 23;
            this.dataGridView1.Size = new System.Drawing.Size(607, 548);
            this.dataGridView1.TabIndex = 0;
            // 
            // bgPerformanceWorker
            // 
            this.bgPerformanceWorker.WorkerReportsProgress = true;
            this.bgPerformanceWorker.WorkerSupportsCancellation = true;
            this.bgPerformanceWorker.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgPerformanceWorker_DoWork);
            this.bgPerformanceWorker.ProgressChanged += new System.ComponentModel.ProgressChangedEventHandler(this.bgPerformanceWorker_ProgressChanged);
            this.bgPerformanceWorker.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bgPerformanceWorker_RunWorkerCompleted);
            // 
            // ESProcessForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("ESProcessForm.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(621, 579);
            this.Controls.Add(this.tabControlMain);
            this.Name = "ESProcessForm";
            this.Text = "智能分析";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.ESProcessForm_FormClosing);
            this.ctxConfigMenu.ResumeLayout(false);
            this.tabControlMain.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.panelPreType.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridPreType)).EndInit();
            this.panelReason.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridReason)).EndInit();
            this.panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridPerformance)).EndInit();
            this.panelSolution.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridSolution)).EndInit();
            this.tabPage2.ResumeLayout(false);
            this.tabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.RichTextBox rptRichBox;
        private System.ComponentModel.BackgroundWorker bgWorker;
        private System.Windows.Forms.ContextMenuStrip ctxConfigMenu;
        private System.Windows.Forms.ToolStripMenuItem miOpenConfig;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miContinue;
        private System.ComponentModel.BackgroundWorker bgContinueWorker;
        private System.ComponentModel.BackgroundWorker bgStepOverWorker;
        private System.ComponentModel.BackgroundWorker bgStepInWorker;
        private System.Windows.Forms.TabControl tabControlMain;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.DataGridView dataGridPreType;
        private System.Windows.Forms.Panel panelReason;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.DataGridView dataGridReason;
        private System.Windows.Forms.Panel panelPreType;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Panel panelSolution;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.DataGridView dataGridSolution;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column1;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
        private System.Windows.Forms.TabPage tabPage3;
        private System.ComponentModel.BackgroundWorker bgPerformanceWorker;
        private System.Windows.Forms.DataGridView dataGridView1;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.DataGridView dataGridPerformance;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn3;
    }
}