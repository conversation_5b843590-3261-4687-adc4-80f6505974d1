﻿namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    partial class HoUpdatePnl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.grpOutdoorWC = new System.Windows.Forms.GroupBox();
            this.label11 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.numPoorAfterHoBeforeSec = new DevExpress.XtraEditors.SpinEdit();
            this.numPoorAfterHoAfterSec = new DevExpress.XtraEditors.SpinEdit();
            this.numPoorAfterHoScond = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.numGoodNbAfterHoAfterSec = new DevExpress.XtraEditors.SpinEdit();
            this.numGoodNbAfterHoSec = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.label17 = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.label21 = new System.Windows.Forms.Label();
            this.numBehindSecond = new DevExpress.XtraEditors.SpinEdit();
            this.numBehindDiff = new DevExpress.XtraEditors.SpinEdit();
            this.numBehindRSRP = new DevExpress.XtraEditors.SpinEdit();
            this.grpOverCover = new System.Windows.Forms.GroupBox();
            this.label3 = new System.Windows.Forms.Label();
            this.numOverHoSecond = new DevExpress.XtraEditors.SpinEdit();
            this.label5 = new System.Windows.Forms.Label();
            this.numOverHoNum = new DevExpress.XtraEditors.SpinEdit();
            this.grpHO = new System.Windows.Forms.GroupBox();
            this.label2 = new System.Windows.Forms.Label();
            this.numOverUpdateNum = new DevExpress.XtraEditors.SpinEdit();
            this.numOverUpdateSecond = new DevExpress.XtraEditors.SpinEdit();
            this.label1 = new System.Windows.Forms.Label();
            this.cellErrorPnl1 = new MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.CellErrorPnl();
            this.ifhoChangedPnl1 = new MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.IFHOChangedPnl();
            this.prbLowSchedulingPnl1 = new MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.PRBLowSchedulingPnl();
            this.trackAreaUpdatePnl1 = new MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.TrackAreaUpdatePnl();
            this.rrcSetupFailPnl1 = new MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.RRCSetupFailPnl();
            this.rrcReEstablishFailPnl1 = new MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.RRCReEstablishFailPnl();
            this.groupBox1.SuspendLayout();
            this.groupBox4.SuspendLayout();
            this.grpOutdoorWC.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numPoorAfterHoBeforeSec.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPoorAfterHoAfterSec.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPoorAfterHoScond.Properties)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numGoodNbAfterHoAfterSec.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numGoodNbAfterHoSec.Properties)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numBehindSecond.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBehindDiff.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBehindRSRP.Properties)).BeginInit();
            this.grpOverCover.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numOverHoSecond.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverHoNum.Properties)).BeginInit();
            this.grpHO.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numOverUpdateNum.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverUpdateSecond.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.groupBox1.Controls.Add(this.groupBox4);
            this.groupBox1.Controls.Add(this.groupBox3);
            this.groupBox1.Controls.Add(this.grpOverCover);
            this.groupBox1.Controls.Add(this.grpHO);
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(771, 360);
            this.groupBox1.TabIndex = 3;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "切换与更新";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.grpOutdoorWC);
            this.groupBox4.Controls.Add(this.groupBox2);
            this.groupBox4.Location = new System.Drawing.Point(33, 111);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(716, 132);
            this.groupBox4.TabIndex = 2;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "切换不合理";
            // 
            // grpOutdoorWC
            // 
            this.grpOutdoorWC.Controls.Add(this.label11);
            this.grpOutdoorWC.Controls.Add(this.label9);
            this.grpOutdoorWC.Controls.Add(this.label10);
            this.grpOutdoorWC.Controls.Add(this.label8);
            this.grpOutdoorWC.Controls.Add(this.label7);
            this.grpOutdoorWC.Controls.Add(this.label6);
            this.grpOutdoorWC.Controls.Add(this.numPoorAfterHoBeforeSec);
            this.grpOutdoorWC.Controls.Add(this.numPoorAfterHoAfterSec);
            this.grpOutdoorWC.Controls.Add(this.numPoorAfterHoScond);
            this.grpOutdoorWC.Location = new System.Drawing.Point(6, 20);
            this.grpOutdoorWC.Name = "grpOutdoorWC";
            this.grpOutdoorWC.Size = new System.Drawing.Size(349, 105);
            this.grpOutdoorWC.TabIndex = 2;
            this.grpOutdoorWC.TabStop = false;
            this.grpOutdoorWC.Text = "切换后主服变差";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(142, 80);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(101, 12);
            this.label11.TabIndex = 0;
            this.label11.Text = "秒的电平平均值差";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(142, 52);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(101, 12);
            this.label9.TabIndex = 0;
            this.label9.Text = "秒的电平平均值比";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(29, 79);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(41, 12);
            this.label10.TabIndex = 0;
            this.label10.Text = "切换前";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(29, 52);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(41, 12);
            this.label8.TabIndex = 0;
            this.label8.Text = "切换后";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(178, 25);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(149, 12);
            this.label7.TabIndex = 0;
            this.label7.Text = "秒内(含等于)发生过切换，";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(29, 25);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(77, 12);
            this.label6.TabIndex = 0;
            this.label6.Text = "低速率点最近";
            // 
            // numPoorAfterHoBeforeSec
            // 
            this.numPoorAfterHoBeforeSec.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numPoorAfterHoBeforeSec.Location = new System.Drawing.Point(76, 74);
            this.numPoorAfterHoBeforeSec.Name = "numPoorAfterHoBeforeSec";
            this.numPoorAfterHoBeforeSec.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numPoorAfterHoBeforeSec.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numPoorAfterHoBeforeSec.Size = new System.Drawing.Size(60, 21);
            this.numPoorAfterHoBeforeSec.TabIndex = 0;
            // 
            // numPoorAfterHoAfterSec
            // 
            this.numPoorAfterHoAfterSec.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numPoorAfterHoAfterSec.Location = new System.Drawing.Point(76, 47);
            this.numPoorAfterHoAfterSec.Name = "numPoorAfterHoAfterSec";
            this.numPoorAfterHoAfterSec.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numPoorAfterHoAfterSec.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numPoorAfterHoAfterSec.Size = new System.Drawing.Size(60, 21);
            this.numPoorAfterHoAfterSec.TabIndex = 0;
            // 
            // numPoorAfterHoScond
            // 
            this.numPoorAfterHoScond.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numPoorAfterHoScond.Location = new System.Drawing.Point(112, 20);
            this.numPoorAfterHoScond.Name = "numPoorAfterHoScond";
            this.numPoorAfterHoScond.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numPoorAfterHoScond.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numPoorAfterHoScond.Size = new System.Drawing.Size(60, 21);
            this.numPoorAfterHoScond.TabIndex = 0;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label4);
            this.groupBox2.Controls.Add(this.label12);
            this.groupBox2.Controls.Add(this.label14);
            this.groupBox2.Controls.Add(this.label15);
            this.groupBox2.Controls.Add(this.label16);
            this.groupBox2.Controls.Add(this.numGoodNbAfterHoAfterSec);
            this.groupBox2.Controls.Add(this.numGoodNbAfterHoSec);
            this.groupBox2.Location = new System.Drawing.Point(361, 21);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(349, 105);
            this.groupBox2.TabIndex = 2;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "切换后主服比邻区差";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(29, 80);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(113, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "比邻区电平平均值差";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(142, 52);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(185, 12);
            this.label12.TabIndex = 0;
            this.label12.Text = "秒内(含等于)主服小区电平平均值";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(29, 52);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(41, 12);
            this.label14.TabIndex = 0;
            this.label14.Text = "切换后";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(178, 25);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(149, 12);
            this.label15.TabIndex = 0;
            this.label15.Text = "秒内(含等于)发生过切换，";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(29, 25);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(77, 12);
            this.label16.TabIndex = 0;
            this.label16.Text = "低速率点最近";
            // 
            // numGoodNbAfterHoAfterSec
            // 
            this.numGoodNbAfterHoAfterSec.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numGoodNbAfterHoAfterSec.Location = new System.Drawing.Point(76, 47);
            this.numGoodNbAfterHoAfterSec.Name = "numGoodNbAfterHoAfterSec";
            this.numGoodNbAfterHoAfterSec.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numGoodNbAfterHoAfterSec.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numGoodNbAfterHoAfterSec.Size = new System.Drawing.Size(60, 21);
            this.numGoodNbAfterHoAfterSec.TabIndex = 0;
            // 
            // numGoodNbAfterHoSec
            // 
            this.numGoodNbAfterHoSec.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numGoodNbAfterHoSec.Location = new System.Drawing.Point(112, 20);
            this.numGoodNbAfterHoSec.Name = "numGoodNbAfterHoSec";
            this.numGoodNbAfterHoSec.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numGoodNbAfterHoSec.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numGoodNbAfterHoSec.Size = new System.Drawing.Size(60, 21);
            this.numGoodNbAfterHoSec.TabIndex = 0;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.label17);
            this.groupBox3.Controls.Add(this.label18);
            this.groupBox3.Controls.Add(this.label19);
            this.groupBox3.Controls.Add(this.label13);
            this.groupBox3.Controls.Add(this.label20);
            this.groupBox3.Controls.Add(this.label21);
            this.groupBox3.Controls.Add(this.numBehindSecond);
            this.groupBox3.Controls.Add(this.numBehindDiff);
            this.groupBox3.Controls.Add(this.numBehindRSRP);
            this.groupBox3.Location = new System.Drawing.Point(33, 249);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(355, 105);
            this.groupBox3.TabIndex = 2;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "切换不及时";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(130, 79);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(53, 12);
            this.label17.TabIndex = 0;
            this.label17.Text = "秒及以上";
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(29, 79);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(29, 12);
            this.label18.TabIndex = 0;
            this.label18.Text = "持续";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(29, 52);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(113, 12);
            this.label19.TabIndex = 0;
            this.label19.Text = "邻区与主服电平差＞";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(208, 52);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(29, 12);
            this.label13.TabIndex = 0;
            this.label13.Text = "dB，";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(165, 25);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(35, 12);
            this.label20.TabIndex = 0;
            this.label20.Text = "dBm，";
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(29, 25);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(65, 12);
            this.label21.TabIndex = 0;
            this.label21.Text = "主服电平＜";
            // 
            // numBehindSecond
            // 
            this.numBehindSecond.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numBehindSecond.Location = new System.Drawing.Point(64, 74);
            this.numBehindSecond.Name = "numBehindSecond";
            this.numBehindSecond.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numBehindSecond.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numBehindSecond.Size = new System.Drawing.Size(60, 21);
            this.numBehindSecond.TabIndex = 0;
            // 
            // numBehindDiff
            // 
            this.numBehindDiff.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numBehindDiff.Location = new System.Drawing.Point(144, 49);
            this.numBehindDiff.Name = "numBehindDiff";
            this.numBehindDiff.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numBehindDiff.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numBehindDiff.Size = new System.Drawing.Size(60, 21);
            this.numBehindDiff.TabIndex = 0;
            // 
            // numBehindRSRP
            // 
            this.numBehindRSRP.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numBehindRSRP.Location = new System.Drawing.Point(99, 20);
            this.numBehindRSRP.Name = "numBehindRSRP";
            this.numBehindRSRP.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numBehindRSRP.Properties.MaxValue = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.numBehindRSRP.Properties.MinValue = new decimal(new int[] {
            150,
            0,
            0,
            -2147483648});
            this.numBehindRSRP.Size = new System.Drawing.Size(60, 21);
            this.numBehindRSRP.TabIndex = 0;
            // 
            // grpOverCover
            // 
            this.grpOverCover.Controls.Add(this.label3);
            this.grpOverCover.Controls.Add(this.numOverHoSecond);
            this.grpOverCover.Controls.Add(this.label5);
            this.grpOverCover.Controls.Add(this.numOverHoNum);
            this.grpOverCover.ForeColor = System.Drawing.SystemColors.ControlText;
            this.grpOverCover.Location = new System.Drawing.Point(33, 31);
            this.grpOverCover.Name = "grpOverCover";
            this.grpOverCover.Size = new System.Drawing.Size(355, 74);
            this.grpOverCover.TabIndex = 0;
            this.grpOverCover.TabStop = false;
            this.grpOverCover.Text = "频繁切换";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(97, 19);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(101, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "秒内(含等于)发生";
            // 
            // numOverHoSecond
            // 
            this.numOverHoSecond.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numOverHoSecond.Location = new System.Drawing.Point(31, 16);
            this.numOverHoSecond.Name = "numOverHoSecond";
            this.numOverHoSecond.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numOverHoSecond.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numOverHoSecond.Size = new System.Drawing.Size(60, 21);
            this.numOverHoSecond.TabIndex = 0;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(97, 48);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(77, 12);
            this.label5.TabIndex = 0;
            this.label5.Text = "次及以上切换";
            // 
            // numOverHoNum
            // 
            this.numOverHoNum.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numOverHoNum.Location = new System.Drawing.Point(31, 43);
            this.numOverHoNum.Name = "numOverHoNum";
            this.numOverHoNum.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numOverHoNum.Size = new System.Drawing.Size(60, 21);
            this.numOverHoNum.TabIndex = 1;
            // 
            // grpHO
            // 
            this.grpHO.Controls.Add(this.label2);
            this.grpHO.Controls.Add(this.numOverUpdateNum);
            this.grpHO.Controls.Add(this.numOverUpdateSecond);
            this.grpHO.Controls.Add(this.label1);
            this.grpHO.ForeColor = System.Drawing.SystemColors.ControlText;
            this.grpHO.Location = new System.Drawing.Point(394, 31);
            this.grpHO.Name = "grpHO";
            this.grpHO.Size = new System.Drawing.Size(355, 74);
            this.grpHO.TabIndex = 1;
            this.grpHO.TabStop = false;
            this.grpHO.Text = "频繁更新";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(97, 25);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(101, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "秒内(含等于)发生";
            // 
            // numOverUpdateNum
            // 
            this.numOverUpdateNum.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numOverUpdateNum.Location = new System.Drawing.Point(31, 47);
            this.numOverUpdateNum.Name = "numOverUpdateNum";
            this.numOverUpdateNum.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numOverUpdateNum.Size = new System.Drawing.Size(60, 21);
            this.numOverUpdateNum.TabIndex = 1;
            // 
            // numOverUpdateSecond
            // 
            this.numOverUpdateSecond.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numOverUpdateSecond.Location = new System.Drawing.Point(31, 20);
            this.numOverUpdateSecond.Name = "numOverUpdateSecond";
            this.numOverUpdateSecond.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numOverUpdateSecond.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numOverUpdateSecond.Size = new System.Drawing.Size(60, 21);
            this.numOverUpdateSecond.TabIndex = 0;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(97, 52);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "次及以上更新";
            // 
            // cellErrorPnl1
            // 
            this.cellErrorPnl1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.cellErrorPnl1.Location = new System.Drawing.Point(394, 555);
            this.cellErrorPnl1.Name = "cellErrorPnl1";
            this.cellErrorPnl1.Size = new System.Drawing.Size(377, 149);
            this.cellErrorPnl1.TabIndex = 4;
            this.cellErrorPnl1.Visible = false;
            // 
            // ifhoChangedPnl1
            // 
            this.ifhoChangedPnl1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.ifhoChangedPnl1.Location = new System.Drawing.Point(0, 555);
            this.ifhoChangedPnl1.Name = "ifhoChangedPnl1";
            this.ifhoChangedPnl1.Size = new System.Drawing.Size(385, 149);
            this.ifhoChangedPnl1.TabIndex = 8;
            // 
            // prbLowSchedulingPnl1
            // 
            this.prbLowSchedulingPnl1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.prbLowSchedulingPnl1.Location = new System.Drawing.Point(394, 427);
            this.prbLowSchedulingPnl1.Name = "prbLowSchedulingPnl1";
            this.prbLowSchedulingPnl1.Size = new System.Drawing.Size(377, 56);
            this.prbLowSchedulingPnl1.TabIndex = 7;
            // 
            // trackAreaUpdatePnl1
            // 
            this.trackAreaUpdatePnl1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.trackAreaUpdatePnl1.Location = new System.Drawing.Point(0, 487);
            this.trackAreaUpdatePnl1.Name = "trackAreaUpdatePnl1";
            this.trackAreaUpdatePnl1.Size = new System.Drawing.Size(771, 61);
            this.trackAreaUpdatePnl1.TabIndex = 6;
            // 
            // rrcSetupFailPnl1
            // 
            this.rrcSetupFailPnl1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.rrcSetupFailPnl1.Location = new System.Drawing.Point(0, 427);
            this.rrcSetupFailPnl1.Name = "rrcSetupFailPnl1";
            this.rrcSetupFailPnl1.Size = new System.Drawing.Size(388, 56);
            this.rrcSetupFailPnl1.TabIndex = 5;
            // 
            // rrcReEstablishFailPnl1
            // 
            this.rrcReEstablishFailPnl1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.rrcReEstablishFailPnl1.Location = new System.Drawing.Point(0, 363);
            this.rrcReEstablishFailPnl1.Name = "rrcReEstablishFailPnl1";
            this.rrcReEstablishFailPnl1.Size = new System.Drawing.Size(771, 61);
            this.rrcReEstablishFailPnl1.TabIndex = 4;
            // 
            // HoUpdatePnl
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.AutoScroll = true;
            this.Controls.Add(this.cellErrorPnl1);
            this.Controls.Add(this.ifhoChangedPnl1);
            this.Controls.Add(this.prbLowSchedulingPnl1);
            this.Controls.Add(this.trackAreaUpdatePnl1);
            this.Controls.Add(this.rrcSetupFailPnl1);
            this.Controls.Add(this.rrcReEstablishFailPnl1);
            this.Controls.Add(this.groupBox1);
            this.Name = "HoUpdatePnl";
            this.Size = new System.Drawing.Size(669, 285);
            this.groupBox1.ResumeLayout(false);
            this.groupBox4.ResumeLayout(false);
            this.grpOutdoorWC.ResumeLayout(false);
            this.grpOutdoorWC.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numPoorAfterHoBeforeSec.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPoorAfterHoAfterSec.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPoorAfterHoScond.Properties)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numGoodNbAfterHoAfterSec.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numGoodNbAfterHoSec.Properties)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numBehindSecond.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBehindDiff.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBehindRSRP.Properties)).EndInit();
            this.grpOverCover.ResumeLayout(false);
            this.grpOverCover.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numOverHoSecond.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverHoNum.Properties)).EndInit();
            this.grpHO.ResumeLayout(false);
            this.grpHO.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numOverUpdateNum.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverUpdateSecond.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox grpOutdoorWC;
        private System.Windows.Forms.GroupBox grpOverCover;
        private System.Windows.Forms.Label label3;
        private DevExpress.XtraEditors.SpinEdit numOverHoSecond;
        private System.Windows.Forms.Label label5;
        private DevExpress.XtraEditors.SpinEdit numOverHoNum;
        private System.Windows.Forms.GroupBox grpHO;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private DevExpress.XtraEditors.SpinEdit numPoorAfterHoScond;
        private System.Windows.Forms.Label label2;
        private DevExpress.XtraEditors.SpinEdit numOverUpdateNum;
        private DevExpress.XtraEditors.SpinEdit numOverUpdateSecond;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label8;
        private DevExpress.XtraEditors.SpinEdit numPoorAfterHoBeforeSec;
        private DevExpress.XtraEditors.SpinEdit numPoorAfterHoAfterSec;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label16;
        private DevExpress.XtraEditors.SpinEdit numGoodNbAfterHoAfterSec;
        private DevExpress.XtraEditors.SpinEdit numGoodNbAfterHoSec;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.Label label21;
        private DevExpress.XtraEditors.SpinEdit numBehindSecond;
        private DevExpress.XtraEditors.SpinEdit numBehindDiff;
        private DevExpress.XtraEditors.SpinEdit numBehindRSRP;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.GroupBox groupBox4;
        private CellErrorPnl cellErrorPnl1;
        private RRCReEstablishFailPnl rrcReEstablishFailPnl1;
        private RRCSetupFailPnl rrcSetupFailPnl1;
        private TrackAreaUpdatePnl trackAreaUpdatePnl1;
        private PRBLowSchedulingPnl prbLowSchedulingPnl1;
        private IFHOChangedPnl ifhoChangedPnl1;

    }
}
