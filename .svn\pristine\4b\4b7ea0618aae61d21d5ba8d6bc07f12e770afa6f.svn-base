﻿using System;
using System.Collections.Generic;
using System.Text;


using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Model.Interface;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Net
{
    public class DIYSampleByCellSingle : DIYSampleQuery
    {
        public DIYSampleByCellSingle(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "采样点查询(按小区)" + Environment.NewLine + "主服（第一强）小区或者第一邻区"; }
        }
        public override string IconName
        {
            get { return "Images/cellq.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11012, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Cell;
        }

        protected void addColDefParam(string paramName)
        {
            DIYSampleParamDef def = new DIYSampleParamDef();
            def.parameter = DTParameterManager.GetInstance().GetParameter(paramName);
            if (!curSelDIYSampleGroup.ColumnsDefSet.Contains(def))
            {
                curSelDIYSampleGroup.ColumnsDefSet.Add(def);
            }
        }

        private ICell selectedCell;
        private double maxDistance = 0.005;
        protected void prepareParamBeforeQuery()
        {
            if (condition.Geometorys.SelectedCell != null)
            {
                addGsmParam();
                selectedCell = condition.Geometorys.SelectedCell;
                maxDistance = CD.MAX_COV_DISTANCE_GSM / 1000000d;
            }
            else if (condition.Geometorys.SelectedTDCell != null)
            {
                addTdParam();
                selectedCell = condition.Geometorys.SelectedTDCell;
                maxDistance = CD.MAX_COV_DISTANCE_TD / 1000000d;
            }
            else if (condition.Geometorys.SelectedLTECell != null)
            {
                addLteParam();
                selectedCell = condition.Geometorys.SelectedLTECell;
                maxDistance = CD.MAX_COV_DISTANCE_LTE / 1000000d;
            }
            else if (condition.Geometorys.SelectedNRCell != null)
            {
                addNrParam();
                selectedCell = condition.Geometorys.SelectedNRCell;
                maxDistance = CD.MAX_COV_DISTANCE_NR / 1000000d;
            }
            else
            {
                selectedCell = null;
            }
        }

        private void addGsmParam()
        {
            addColDefParam("LAC");
            addColDefParam("CI");
            addColDefParam("BCCH");
            addColDefParam("BSIC");
            addColDefParam("N_BCCH");
            addColDefParam("N_BSIC");
            if (condition.ServiceTypes.Contains((int)ServiceType.GSM_SCAN))
            {
                addColDefParam("GSCAN_BCCH");
                addColDefParam("GSCAN_BSIC");
            }
            if (condition.ServiceTypes.Contains((int)ServiceType.TDSCDMA_VOICE)
                || condition.ServiceTypes.Contains((int)ServiceType.TDSCDMA_DATA)
                || condition.ServiceTypes.Contains((int)ServiceType.TDSCDMA_HSDPA)
                || condition.ServiceTypes.Contains((int)ServiceType.TDSCDMA_HSUPA)
                || condition.ServiceTypes.Contains((int)ServiceType.TDSCDMA_IDLE))
            {
                addColDefParam(MainModel.TD_GSM_SCell_LAC);
                addColDefParam(MainModel.TD_GSM_SCell_CI);
                addColDefParam(MainModel.TD_GSM_SCell_ARFCN);
                addColDefParam(MainModel.TD_GSM_SCell_BSIC);
                addColDefParam(MainModel.TD_GSM_NCell_ARFCN);
                addColDefParam(MainModel.TD_GSM_NCell_BSIC);
            }
        }

        private void addTdParam()
        {
            addColDefParam(MainModel.TD_SCell_LAC);
            addColDefParam(MainModel.TD_SCell_CI);
            addColDefParam(MainModel.TD_SCell_UARFCN);
            addColDefParam(MainModel.TD_SCell_CPI);
            addColDefParam("TD_NCell_UARFCN");
            addColDefParam("TD_NCell_CPI");
            if (condition.ServiceTypes.Contains((int)ServiceType.TD_SCAN))
            {
                addColDefParam("TDS_PCCPCH_Channel");
                addColDefParam("TDS_PCCPCH_CPI");
            }
        }

        private void addLteParam()
        {
            addColDefParam("lte_TAC");
            addColDefParam("lte_ECI");
            addColDefParam("lte_EARFCN");
            addColDefParam("lte_PCI");
            addColDefParam("lte_NCell_EARFCN");
            addColDefParam("lte_NCell_PCI");
            if (condition.ServiceTypes.Contains((int)ServiceType.LTE_SCAN_CW)
                || condition.ServiceTypes.Contains((int)ServiceType.LTE_SCAN_TOPN))
            {
                addColDefParam("LTESCAN_TopN_EARFCN");
                addColDefParam("LTESCAN_TopN_PCI");
            }
            if (condition.ServiceTypes.Contains((int)ServiceType.LTE_SIGNAL))
            {
                addColDefParam("signal_LAC");
                addColDefParam("signal_CI");
                addColDefParam("signal_LteScEarfcn");
                addColDefParam("signal_LteScPci");
            }
        }

        private void addNrParam()
        {
            addColDefParam("NR_TAC");
            addColDefParam("NR_NCI");
            addColDefParam("NR_SSB_ARFCN");
            addColDefParam("NR_PCI");
            addColDefParam("NR_NCell_ARFCN");
            addColDefParam("NR_NCell_PCI");
        }

        protected override void queryInThread(object o)
        {
            try
            {
                prepareParamBeforeQuery();
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                foreach (TimePeriod period in Condition.Periods)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                    WaitBox.ProgressPercent = index / periodCount + 10;
                    queryPeriodInfo(clientProxy, package, period, false);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected string getTDParamName(string tdDisplayName) //通过TD显示名称转回参数名称
        {
            if (tdDisplayName.StartsWith("TD_"))
            {
                return tdDisplayName.Substring(3, tdDisplayName.Length - 3);
            }
            else
            {
                return tdDisplayName;
            }
        }

        /**
        private void parseRectByLongLatDistance(double longitude, double latitude, int meter, out double ltlong, out double ltlat, out double brlong, out double brlat)//通过中心经纬度及其到边框的距离求经纬度（模糊值）
        {
            double longPerMeter = 0.00001;
            double latPerMeter = 0.000009;
            ltlong = longitude - meter * longPerMeter;
            ltlat = latitude + meter * latPerMeter;
            brlong = longitude + meter * longPerMeter;
            brlat = latitude - meter * latPerMeter;
        }
        */
        protected override void queryPeriodInfo(ClientProxy clientProxy, Package package, TimePeriod period, bool byround)
        {
            prepareStatPackage_Sample_FileFilter(package, period, byround);
            prepareStatPackage_Sample_SampleFilter(package, period);
            fillContentNeeded_Sample(package);
            clientProxy.Send();
            recieveInfo_Sample(clientProxy);
        }
        protected override void prepareStatPackage_Sample_SampleFilter(Package package, TimePeriod period)
        {
            AddDIYRegion_Sample(package);
            AddDIYEndOpFlag(package);
        }
        protected override void prepareStatPackage_Sample_FileFilter(Package package, TimePeriod period, bool byRound)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            if (!isPreSetted)
            {
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_COVER_SAMPLE;
            }
            else
            {
                package.Content.Type = this.preSettedCommandType;
            }
            package.Content.PrepareAddParam();
            if (byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            AddDIYRegion_Intersect(package);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);
            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            //
            AddDIYEndOpFlag(package);
        }
        protected override void AddDIYRegion_Intersect(Package package)
        {
            if (condition.Geometorys.Region != null)
            {
                DbRect rect = MapOperation.GetShapeBounds(condition.Geometorys.Region);
                package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
                package.Content.AddParam(rect.x1 - maxDistance);
                package.Content.AddParam(rect.y2 + maxDistance);
                package.Content.AddParam(rect.x2 + maxDistance);
                package.Content.AddParam(rect.y1 - maxDistance);
            }
            else if (selectedCell != null)
            {
                package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
                package.Content.AddParam(selectedCell.Longitude - maxDistance);
                package.Content.AddParam(selectedCell.Latitude + maxDistance);
                package.Content.AddParam(selectedCell.Longitude + maxDistance);
                package.Content.AddParam(selectedCell.Latitude - maxDistance);
            }
        }
        protected override void AddDIYRegion_Sample(Package package)
        {
            if (condition.Geometorys.Region != null)
            {
                DbRect rect = MapOperation.GetShapeBounds(condition.Geometorys.Region);
                package.Content.AddParam((byte)OpOptionDef.AreaSelectSample);
                package.Content.AddParam(rect.x1 - maxDistance);
                package.Content.AddParam(rect.y2 + maxDistance);
                package.Content.AddParam(rect.x2 + maxDistance);
                package.Content.AddParam(rect.y1 - maxDistance);
            }
            else if (selectedCell != null)
            {
                package.Content.AddParam((byte)OpOptionDef.AreaSelectSample);
                package.Content.AddParam(selectedCell.Longitude - maxDistance);
                package.Content.AddParam(selectedCell.Latitude + maxDistance);
                package.Content.AddParam(selectedCell.Longitude + maxDistance);
                package.Content.AddParam(selectedCell.Latitude - maxDistance);
            }
        }

        /**
        private void AddDIYRegion_Sample_Cell(Package package, CellQueryCondUnit cond)
        {
            package.Content.AddParam((byte)OpOptionDef.AreaSelectSample);
            package.Content.AddParam(cond.ltLongitude);
            package.Content.AddParam(cond.ltLatitude);
            package.Content.AddParam(cond.brLongitude);
            package.Content.AddParam(cond.brLatitude);
        }
        private void prepareStatPackage_Sample_FileFilter_Cell(Package package, CellQueryCondUnit cond)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_CELL_SAMPLE;
            package.Content.PrepareAddParam();
            AddDIYPeriod(package, cond.period);
            AddDIYRegion_Intersect(package, cond);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);
            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            //
            AddDIYEndOpFlag(package);

        }
        */

        protected void AddDIYLacCIOfCell(Package package, CellQueryCondUnit cond)
        {
            package.Content.AddParam((byte)OpOptionDef.CellSelect);
            package.Content.AddParam(cond.LAC);
            package.Content.AddParam(cond.CI);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.SelectedCell == null
                && searchGeometrys.SelectedTDCell == null
                && searchGeometrys.SelectedLTECell == null
                && searchGeometrys.SelectedNRCell == null
                )
            {
                return false;
            }
            return true;
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            ICell cellObj = tp.GetMainCell();
            if (cellObj == null)
            {
                cellObj = tp.GetNBCell(0);
            }
            if (cellObj == null)
            {
                return false;
            }
            if (condition.Geometorys.SelectedCell != null)
            {
                return (cellObj is Cell) && cellObj.ID == condition.Geometorys.SelectedCell.ID;
            }
            else if (condition.Geometorys.SelectedTDCell != null)
            {
                return (cellObj is TDCell) && cellObj.ID == condition.Geometorys.SelectedTDCell.ID;
            }
            else if (condition.Geometorys.SelectedLTECell != null)
            {
                return (cellObj is LTECell) && cellObj.ID == condition.Geometorys.SelectedLTECell.ID;
            }
            else if (condition.Geometorys.SelectedNRCell != null)
            {
                return (cellObj is NRCell) && cellObj.ID == condition.Geometorys.SelectedNRCell.ID;
            }
            return false;
        }
    }
}
