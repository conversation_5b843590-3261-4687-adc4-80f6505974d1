﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRSinrResultForm : MinCloseForm
    {
        public NRSinrResultForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = true;
            miExportExcel.Click += MiExportExcel_Click;
        }

        private delegate void setGCDataSource(DevExpress.XtraGrid.GridControl gc, DataTable dtTable);
    
        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            if (resultFiles == null)
            {
                return;
            }
            List<NPOIRow> table = new List<NPOIRow>();
            int columnsCount = resultFiles[0].NRSinrModel.Items.Count;
            NPOIRow titleRow = new NPOIRow();
            titleRow.AddCellValue("运营商");
            titleRow.AddCellValue("电平分布");
            foreach (NRSinrItemModel item in resultFiles[0].NRSinrModel.Items)
            {
                titleRow.AddCellValue(item.Name);
            }
            titleRow.AddCellValue("总采样点");
            table.Add(titleRow);
            foreach (NRSinrResultModel res in resultFiles)
            {
                NPOIRow row = new NPOIRow();
                NPOIRow totalRow = new NPOIRow();
                row.AddCellValue(res.CarrierName);
                row.AddCellValue(res.NRSinrModel.Name);
                totalRow.AddCellValue(res.CarrierName);
                totalRow.AddCellValue(res.totalModel.Name);
                int pointTotal = 0;
                int allTotal = 0;
                for (int i = 0; i < columnsCount; i++)
                {
                    pointTotal += res.NRSinrModel.Items[i].PointCount;
                    allTotal += res.totalModel.Items[i].PointCount;
                    row.AddCellValue(res.NRSinrModel.Items[i].PointCount);
                    totalRow.AddCellValue(res.totalModel.Items[i].PointCount);
                }
                row.AddCellValue(pointTotal);
                totalRow.AddCellValue(allTotal);
                table.Add(row);
                table.Add(totalRow);
            }
            ExcelNPOIManager.ExportToExcel(new List<List<NPOIRow>>() { table  },
                new List<string>() { resultFiles[0].NRSinrModel.Name});
        }

        private List<NRSinrResultModel> resultFiles = null;

        public void FillData(object result)
        {
            resultFiles = new List<NRSinrResultModel>();
            resultFiles = result as List<NRSinrResultModel>;
            int columnsCount = resultFiles[0].NRSinrModel.Items.Count ;
            try
            {
                DataTable dtTDResult = new DataTable();
                dtTDResult.Columns.Add("运营商");
                dtTDResult.Columns.Add("电平分布");
                for (int i = 0; i < columnsCount; i++)
                {
                    dtTDResult.Columns.Add(resultFiles[0].NRSinrModel.Items[i].Name);
                }
                dtTDResult.Columns.Add("总采样点");
                foreach (NRSinrResultModel res in resultFiles)
                {
                    List<object> points = new List<object>() { res.CarrierName, res.NRSinrModel.Name };
                    List<object> all = new List<object>() { res.CarrierName, "SINR总采样点" };
                    int pointTotal = 0;
                    int allTotal = 0;
                    for (int i = 0; i < columnsCount; i++)
                    {
                        pointTotal += res.NRSinrModel.Items[i].PointCount;
                        allTotal += res.totalModel.Items[i].PointCount;
                        points.Add(res.NRSinrModel.Items[i].PointCount);
                        all.Add(res.totalModel.Items[i].PointCount);
                    }
                    points.Add(pointTotal);
                    all.Add(allTotal);
                    dtTDResult.Rows.Add(points.ToArray());
                    dtTDResult.Rows.Add(all.ToArray());
                    CheckForIllegalCrossThreadCalls = false;
                    SetGCDataSource(gcSINR, dtTDResult);                   
                }
                gvResult.BestFitColumns();
            }
            catch
            {
                //continue
            }
        }

        private void SetGCDataSource(DevExpress.XtraGrid.GridControl gc, DataTable dtTable)
        {
            if (gc.InvokeRequired)
            {
                setGCDataSource set = new setGCDataSource(delegate(DevExpress.XtraGrid.GridControl _gc,
                   DataTable _dtTable)
                {
                    _gc.DataSource = _dtTable;
                });
                this.Invoke(set, gc, dtTable);
            }
            else
            {
                gc.DataSource = dtTable;
            }
        }
    }
}
