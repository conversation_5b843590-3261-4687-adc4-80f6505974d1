﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.GridOrderCommon
{
    public class OrderTokenMng
    {
        private static OrderTokenMng instance = null;
        public static OrderTokenMng Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new OrderTokenMng();
                }
                return instance;
            }
        }

        private OrderTokenMng()
        {
            queryFormDB();
        }

        public List<GridOrderToken> TokenSet
        {
            get;
            set;
        }

        public GridOrderToken GetToken(int id)
        {
            foreach (GridOrderToken item in TokenSet)
            {
                if (item.ID == id)
                {
                    return item;
                }
            }
            return null;
        }

        private void queryFormDB()
        {
            QueryGridOrderToken query = new QueryGridOrderToken();
            query.Query();
            TokenSet = query.TokenSet;
        }

    }
}
