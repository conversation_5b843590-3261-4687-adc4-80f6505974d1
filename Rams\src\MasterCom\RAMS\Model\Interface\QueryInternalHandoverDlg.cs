using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Model.Interface
{
    public partial class QueryInternalHandoverDlg : Form
    {
        public QueryInternalHandoverDlg()
        {
            InitializeComponent();
        }

        public int FrequencyRangeFilter
        {
            get 
            {
                if (rbAll.Checked)
                {
                    return 1;
                }
                else if (rb900.Checked)
                {
                    return 2;
                }
                else
                {
                    return 3;
                }
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }
    }
}