﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Web.UI.WebControls;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryLteRRCAna : DIYAnalyseByFileBackgroundBase
    {
        public QueryLteRRCAna()
            : base(MainModel.GetInstance())
        {
        }

        public override string Name
        {
            get { return "参考信号功率参数分析"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22108, this.Name);
        }

        Dictionary<string, LteRRCInfo> cellRRCInfoDic = null;

        protected override void clearDataBeforeAnalyseFiles()
        {
            cellRRCInfoDic = new Dictionary<string, LteRRCInfo>();
            this.IncludeMessage = true;
        }

        protected override void query()
        {
            if (MainModel.IsBackground && !MainModel.BackgroundStarted)
            {
                return;
            }
            if (!getCondition())
            {
                return;
            }
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            queryFilesAna();
            WaitBox.Show("查找相关文件的信令...", queryRRCMsgByFile);
            fireShowForm();
        }

        private void queryFilesAna()
        {
            try
            {
                WaitBox.ProgressPercent = 25;
                DIYQueryFileInfoByRegion queryFiles = new DIYQueryFileInfoByRegion(MainModel);
                condition.EventIDs = null;
                queryFiles.SetQueryCondition(condition);
                queryFiles.IsShowFileInfoForm = false;
                queryFiles.Query();
                Thread.Sleep(20);
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }
        private void queryRRCMsgByFile()
        {
            try
            {
                WaitBox.ProgressPercent = 25;
                Dictionary<int, int> needQueryFileDic = new Dictionary<int, int>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (!needQueryFileDic.ContainsKey(fileInfo.ID))
                        needQueryFileDic[fileInfo.ID] = 0;
                }
                WaitBox.ProgressPercent = 45;
                string strMonth = "";
                DateTime time = condition.Periods[0].BeginTime;
                while(time < condition.Periods[0].EndTime)
                {
                    if(!strMonth.Contains(time.ToString("yyyyMM")))
                    {
                        strMonth = string.Format(strMonth + time.ToString("yyyyMM") + ",");
                    }
                    time = time.AddDays(1);
                }
                strMonth= strMonth.TrimEnd(',');
                DIYQueryLteRRCInfo queryMsg = new DIYQueryLteRRCInfo(MainModel);
                condition.EventIDs = null;
                queryMsg.SetQueryCondition(condition);
                queryMsg.SetCondition(strMonth , needQueryFileDic);
                queryMsg.Query();
                this.cellRRCInfoDic = queryMsg.cellRRCInfoDic;
                Thread.Sleep(20);
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected override void fireShowForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LteRRCAnaForm).FullName);
            LteRRCAnaForm resultForm = obj == null ? null : obj as LteRRCAnaForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new LteRRCAnaForm(MainModel);
            }
            resultForm.FillData(cellRRCInfoDic);
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }
        }
    }
}
