﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util.UiEx;
using MasterCom.Util;
using DevExpress.XtraGrid.Columns;
using MasterCom.NOP.WF.Core;
using MasterCom.RAMS.NOP.Stat;
using System.IO;
using DevExpress.XtraCharts;

namespace MasterCom.RAMS.NOP
{
    public partial class TaskOrderStatForm : MinCloseForm
    {
        public TaskOrderStatForm()
        {
            InitializeComponent();
            this.dtPickerBegin.Value = DateTime.Now.AddMonths(-1);
            gisPanel.Init();
            miExportExcel.Click += MiExportExcel_Click;
            btnQuery.Click += BtnQuery_Click;
            gv.RowCellStyle += gv_RowCellStyle;
            initGisColCfg();
        }

        void gv_RowCellStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
        {
            ColumnRender render = colRenderSet.Find(delegate(ColumnRender x) { return x.Caption == e.Column.Caption; });
            if (render == null)
            {
                return;
            }
            if (e.CellValue == null)
            {
                return;
            }
            float value = float.Parse(e.CellValue.ToString());
            foreach (DTParameterRangeColor rng in render.RangeColorSet)
            {
                if (rng.Within(value))
                {
                    e.Appearance.BackColor = rng.Value;
                    break;
                }
            }
        }

        List<ColumnRender> colRenderSet = null;
        private void initGisColCfg()
        {
            if (!loadRenderCfg())
            {
                colRenderSet = new List<ColumnRender>();
                colRenderSet.Add(new ColumnRender("工单总数"));
                colRenderSet.Add(new ColumnRender("待派发工单"));
                colRenderSet.Add(new ColumnRender("待接单工单"));
                colRenderSet.Add(new ColumnRender("待回单工单"));
                colRenderSet.Add(new ColumnRender("待验证工单"));
                colRenderSet.Add(new ColumnRender("已归档工单"));
                colRenderSet.Add(new ColumnRender("已解决工单"));
                colRenderSet.Add(new ColumnRender("未解决工单"));
                colRenderSet.Add(new ColumnRender("问题解决率"));
                colRenderSet.Add(new ColumnRender("事件个数"));
            }
            if (colRenderSet.Count == 9)
            {
                colRenderSet.Add(new ColumnRender("事件个数"));
            }
            cbxGisCol.Properties.Items.Clear();
            cbxGisCol.SelectedIndexChanged += cbxGisCol_SelectedIndexChanged;
            foreach (ColumnRender item in colRenderSet)
            {
                cbxGisCol.Properties.Items.Add(item);
            }
            cbxGisCol.SelectedIndex = 0;
        }

        private static readonly string CfgPath = Application.StartupPath + "\\config\\ztfunc\\工单数量统计配置.xml";
        private void saveRenderCfg()
        {
            XmlConfigFile configFile = new XmlConfigFile();
            System.Xml.XmlElement cfg = configFile.AddConfig("ColRenderCfg");
            List<object> param = new List<object>();
            foreach (ColumnRender render in colRenderSet)
            {
                param.Add(render.Param);
            }
            configFile.AddItem(cfg, "Options", param);
            configFile.AddItem(cfg, "EventColor", colorEvt.Color.ToArgb());
            try
            {
                configFile.Save(CfgPath);
            }
            catch (Exception ex)
            {
                MessageBox.Show("保存配置失败！" + Environment.NewLine + ex.Message);
            }
        }

        private bool loadRenderCfg()
        {
            if (!File.Exists(CfgPath))
            {
                return false;
            }
            XmlConfigFile configFile = new XmlConfigFile(CfgPath);
            try
            {
                List<object> list = configFile.GetItemValue("ColRenderCfg", "Options") as List<object>;
                colRenderSet = new List<ColumnRender>();
                foreach (Dictionary<string,object> item in list)
                {
                    ColumnRender render = new ColumnRender();
                    render.Param = item;
                    colRenderSet.Add(render);
                }
                object colorInt = configFile.GetItemValue("ColRenderCfg", "EventColor");
                if (colorInt != null)
                {
                    colorEvt.Color=Color.FromArgb((int)colorInt);
                }
            }
            catch (Exception)
            {
                return false;
            }
            return true;
        }

        void cbxGisCol_SelectedIndexChanged(object sender, EventArgs e)
        {
            ColumnRender render = cbxGisCol.Properties.Items[cbxGisCol.SelectedIndex] as ColumnRender;
            gisPanel.CurColRender = render;

            List<GISShapeRenderOption> gisRenders = new List<GISShapeRenderOption>();
            if (rows == null)
            {
                return;
            }
            foreach (StatRow row in rows)
            {
                if (row.地市名称 == "汇总")
                {
                    continue;
                }

                Type t = row.GetType();
                System.Reflection.PropertyInfo[] arr = t.GetProperties();
                foreach (System.Reflection.PropertyInfo item in arr)
                {
                    if (item.Name == render.Caption)
                    {
                        addGisRenders(render, gisRenders, row, item);
                        break;
                    }
                }
            }
            gisPanel.Renders = gisRenders;

        }

        private static void addGisRenders(ColumnRender render, List<GISShapeRenderOption> gisRenders, StatRow row, System.Reflection.PropertyInfo item)
        {
            object value = item.GetValue(row, null);
            float x = float.Parse(value.ToString());
            foreach (DTParameterRangeColor rng in render.RangeColorSet)
            {
                if (rng.Within(x))
                {
                    GISShapeRenderOption gisRnd = new GISShapeRenderOption();
                    gisRnd.CityName = row.地市名称;
                    gisRnd.Color = rng.Value;
                    gisRnd.Value = x;
                    gisRenders.Add(gisRnd);
                    break;
                }
            }
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gv);
        }

        private void BtnQuery_Click(object sender, EventArgs e)
        {
            DateTime sTime = dtPickerBegin.Value.Date;
            DateTime eTime = dtPickerEnd.Value.Date;

            TaskOrderStater stater = new TaskOrderStater();
            WaitTextBox.Show("正在查询工单...", DoStatInThread, new object[] { sTime, eTime, stater });

            gridCtrl.DataSource = rows;
            gridCtrl.RefreshDataSource();
            cbxGisCol_SelectedIndexChanged(null, null);
            displayEvents();
            refreashChart();
        }

        private void refreashChart()
        {
            chart.Legend.Visible = false;
            chart.Titles.Clear();
            chart.Series.Clear();
            string propName = null;
            if (gv.FocusedColumn == null || gv.FocusedColumn == gridColumn1)
            {
                propName = gridColumn2.Caption;
            }
            else
            {
                propName = gv.FocusedColumn.Caption;
            }
            Series series = new Series(propName, DevExpress.XtraCharts.ViewType.Bar);
            ChartTitle title = new ChartTitle();
            title.Text = propName;
            title.Font = new Font("宋体", 12f, FontStyle.Bold);
            if (rows == null || rows.Count > 0)
            {
                return;
            }
            foreach (StatRow row in rows)
            {
                if (row.地市名称 == "汇总")
                {
                    continue;
                }

                Type t = row.GetType();
                System.Reflection.PropertyInfo[] arr = t.GetProperties();
                float value = 0;
                foreach (System.Reflection.PropertyInfo item in arr)
                {
                    if (item.Name == propName)
                    {
                        object obj = item.GetValue(row, null);
                        value = float.Parse(obj.ToString());
                        break;
                    }
                }
                series.Points.Add(new SeriesPoint(row.地市名称, (double)value));
            }
            series.View.Color = Color.DodgerBlue;
            ((SideBySideBarSeriesView)series.View).FillStyle.FillMode = FillMode.Solid;
            ((SideBySideBarSeriesLabel)series.Label).ShowForZeroValues = true;
            chart.Series.Add(series);
            chart.Titles.Add(title);
        }

        private void DoStatInThread(object o)
        {
            try
            {
                object[] args = o as object[];
                DateTime sTime = (DateTime)args[0];
                DateTime eTime = (DateTime)args[1];
                TaskOrderStater stater = (TaskOrderStater)args[2];
                rows = stater.GetResult(sTime, eTime);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + Environment.NewLine + ex.StackTrace, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                WaitTextBox.Close();
            }
        }

        //private void RebuildColumns(GridColumn[] columns)
        //{
        //    if (gv.Columns.Count > 0)
        //    {
        //        return;
        //    }

        //    gv.Columns.Clear();
        //    for (int i = 0; i < columns.Length; ++i)
        //    {
        //        columns[i].Visible = true;
        //        columns[i].VisibleIndex = i;
        //    }
        //    gv.Columns.AddRange(columns);
        //}

        private List<StatRow> rows;

        private void btnRenderOption_Click(object sender, EventArgs e)
        {
            if (cbxGisCol.SelectedIndex == -1)
            {
                return;
            }
            ColumnRender render = cbxGisCol.Properties.Items[cbxGisCol.SelectedIndex] as ColumnRender;
            List<DTParameterRange> rangeValues = new List<DTParameterRange>();
            foreach (DTParameterRange rangeValue in render.RangeColorSet)
            {
                rangeValues.Add(rangeValue);
            }
            MasterCom.RAMS.Frame.RangeColorsSettingBox box = new Frame.RangeColorsSettingBox(rangeValues, 0, 9999);
            box.ShowRangeOption = true;
            if (box.ShowDialog() == DialogResult.OK)
            {
                render.RangeColorSet = new List<DTParameterRangeColor>();
                foreach (DTParameterRange item in box.RangeValues)
                {
                    render.RangeColorSet.Add(item as DTParameterRangeColor);
                }
                saveRenderCfg();
                cbxGisCol_SelectedIndexChanged(null, null);
                gv.RefreshData();
            }
        }

        private void displayEvents()
        {
            if (chkDisplayEvent.Checked)
            {
                if (rows == null || rows.Count <1)
                {
                    return;
                }
                gisPanel.SetEvents(rows[rows.Count - 1].Events);
            }
            else
            {
                gisPanel.SetEvents(null);
            }
        }

        private void chkDisplayEvent_CheckedChanged(object sender, EventArgs e)
        {
            displayEvents();
            colorEvt.Enabled = chkDisplayEvent.Checked;
        }

        private void colorEvt_EditValueChanged(object sender, EventArgs e)
        {
            gisPanel.PointColor = colorEvt.Color;
        }

        private void gv_FocusedColumnChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventArgs e)
        {
            if (gv.FocusedColumn == null || gv.FocusedColumn == gridColumn1)
            {
                return;
            }
            refreashChart();
        }
    }
}
