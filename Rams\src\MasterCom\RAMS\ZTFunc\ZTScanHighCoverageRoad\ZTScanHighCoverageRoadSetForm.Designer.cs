﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTScanHighCoverageRoadSetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControlBand = new DevExpress.XtraEditors.LabelControl();
            this.spinEditMaxDiff = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditRxlevMin = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditRoadDistance = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditCoverage = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditSampleDistance = new DevExpress.XtraEditors.SpinEdit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMaxDiff.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxlevMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRoadDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCoverage.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSampleDistance.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControlBand
            // 
            this.labelControlBand.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControlBand.Appearance.Options.UseFont = true;
            this.labelControlBand.Location = new System.Drawing.Point(36, 26);
            this.labelControlBand.Name = "labelControlBand";
            this.labelControlBand.Size = new System.Drawing.Size(96, 12);
            this.labelControlBand.TabIndex = 3;
            this.labelControlBand.Text = "与最强信号差异 <";
            // 
            // spinEditMaxDiff
            // 
            this.spinEditMaxDiff.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.spinEditMaxDiff.Location = new System.Drawing.Point(137, 22);
            this.spinEditMaxDiff.Name = "spinEditMaxDiff";
            this.spinEditMaxDiff.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditMaxDiff.Properties.Appearance.Options.UseFont = true;
            this.spinEditMaxDiff.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditMaxDiff.Properties.IsFloatValue = false;
            this.spinEditMaxDiff.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.spinEditMaxDiff.Properties.Mask.EditMask = "N00";
            this.spinEditMaxDiff.Properties.MaxValue = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.spinEditMaxDiff.Size = new System.Drawing.Size(82, 20);
            this.spinEditMaxDiff.TabIndex = 0;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(223, 27);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(12, 12);
            this.labelControl1.TabIndex = 5;
            this.labelControl1.Text = "dB";
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(67, 213);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 5;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(168, 213);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 6;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(67, 137);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(66, 12);
            this.labelControl6.TabIndex = 3;
            this.labelControl6.Text = "持续距离 ≥";
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(226, 137);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(12, 12);
            this.labelControl7.TabIndex = 12;
            this.labelControl7.Text = "米";
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(29, 167);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(102, 12);
            this.labelControl8.TabIndex = 3;
            this.labelControl8.Text = "相邻采样点距离 ≤";
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Location = new System.Drawing.Point(226, 168);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(12, 12);
            this.labelControl9.TabIndex = 12;
            this.labelControl9.Text = "米";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(54, 107);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(78, 12);
            this.labelControl3.TabIndex = 3;
            this.labelControl3.Text = "重叠覆盖度 ≥";
            // 
            // spinEditRxlevMin
            // 
            this.spinEditRxlevMin.EditValue = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            this.spinEditRxlevMin.Location = new System.Drawing.Point(137, 51);
            this.spinEditRxlevMin.Name = "spinEditRxlevMin";
            this.spinEditRxlevMin.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditRxlevMin.Properties.Appearance.Options.UseFont = true;
            this.spinEditRxlevMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRxlevMin.Properties.IsFloatValue = false;
            this.spinEditRxlevMin.Properties.Mask.EditMask = "N00";
            this.spinEditRxlevMin.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.spinEditRxlevMin.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.spinEditRxlevMin.Size = new System.Drawing.Size(82, 20);
            this.spinEditRxlevMin.TabIndex = 1;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(72, 54);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(60, 12);
            this.labelControl4.TabIndex = 3;
            this.labelControl4.Text = "最强信号 >";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(225, 54);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(18, 12);
            this.labelControl5.TabIndex = 12;
            this.labelControl5.Text = "dBm";
            // 
            // spinEditRoadDistance
            // 
            this.spinEditRoadDistance.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditRoadDistance.Location = new System.Drawing.Point(137, 134);
            this.spinEditRoadDistance.Name = "spinEditRoadDistance";
            this.spinEditRoadDistance.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditRoadDistance.Properties.Appearance.Options.UseFont = true;
            this.spinEditRoadDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRoadDistance.Properties.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditRoadDistance.Properties.IsFloatValue = false;
            this.spinEditRoadDistance.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.spinEditRoadDistance.Properties.Mask.EditMask = "N00";
            this.spinEditRoadDistance.Properties.MaxValue = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.spinEditRoadDistance.Size = new System.Drawing.Size(82, 20);
            this.spinEditRoadDistance.TabIndex = 3;
            // 
            // spinEditCoverage
            // 
            this.spinEditCoverage.EditValue = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.spinEditCoverage.Location = new System.Drawing.Point(137, 104);
            this.spinEditCoverage.Name = "spinEditCoverage";
            this.spinEditCoverage.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditCoverage.Properties.Appearance.Options.UseFont = true;
            this.spinEditCoverage.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditCoverage.Properties.IsFloatValue = false;
            this.spinEditCoverage.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.spinEditCoverage.Properties.Mask.EditMask = "N00";
            this.spinEditCoverage.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditCoverage.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.spinEditCoverage.Size = new System.Drawing.Size(82, 20);
            this.spinEditCoverage.TabIndex = 2;
            // 
            // spinEditSampleDistance
            // 
            this.spinEditSampleDistance.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.spinEditSampleDistance.Location = new System.Drawing.Point(137, 163);
            this.spinEditSampleDistance.Name = "spinEditSampleDistance";
            this.spinEditSampleDistance.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditSampleDistance.Properties.Appearance.Options.UseFont = true;
            this.spinEditSampleDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditSampleDistance.Properties.IsFloatValue = false;
            this.spinEditSampleDistance.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.spinEditSampleDistance.Properties.Mask.EditMask = "N00";
            this.spinEditSampleDistance.Properties.MaxValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditSampleDistance.Properties.MinValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditSampleDistance.Size = new System.Drawing.Size(82, 20);
            this.spinEditSampleDistance.TabIndex = 4;
            // 
            // ZTScanHighCoverageRoadSetForm
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(276, 259);
            this.Controls.Add(this.labelControl9);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.spinEditRxlevMin);
            this.Controls.Add(this.labelControlBand);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.spinEditCoverage);
            this.Controls.Add(this.spinEditSampleDistance);
            this.Controls.Add(this.spinEditRoadDistance);
            this.Controls.Add(this.spinEditMaxDiff);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl6);
            this.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.Name = "ZTScanHighCoverageRoadSetForm";
            this.Text = "高重叠覆盖度路段条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMaxDiff.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxlevMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRoadDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCoverage.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSampleDistance.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControlBand;
        private DevExpress.XtraEditors.SpinEdit spinEditMaxDiff;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit spinEditRxlevMin;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit spinEditRoadDistance;
        private DevExpress.XtraEditors.SpinEdit spinEditCoverage;
        private DevExpress.XtraEditors.SpinEdit spinEditSampleDistance;
    }
}