﻿using System;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NbIotMgrsResultStatisticsResult : NbIotMgrsResultControlBase
    {
        public NbIotMgrsResultStatisticsResult()
        {
            InitializeComponent();
            miExportExcel.Click += MiExportExcel_Click;
            miExportAllExcel.Click += base.MiExportExcelAll_Click;
            miExportAllShp.Click += base.MiExportShpAll_Click;
        }

        public override string Desc
        {
            get { return "统计结果"; }
        }

        public void FillData(object data)
        {
            gridControl1.DataSource = data;
            gridView1.BestFitColumns();
        }

        protected override void ExportAllExcel(string savePath)
        {
            string sheetName = "统计结果";
            string fileName = System.IO.Path.Combine(savePath, sheetName + ".xlsx");
            ExcelNPOIManager.ExportToExcel(gridView1, fileName, sheetName);
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }
    }
}
