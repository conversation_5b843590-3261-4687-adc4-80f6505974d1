﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class GSMMemoryProblemListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.lv_ProblemCells = new BrightIdeasSoftware.TreeListView();
            this.olvFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvOCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvOLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvOCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvOBCCH = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvOBSIC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvORxlev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvPCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvPCellID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvPCellLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvPCellCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvPBCCH = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvPBSIC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvPRxlev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvSixScale = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctMS_lv = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportTxt = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.miShowTestPoint = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowTestPointLink = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.lv_ProblemCells)).BeginInit();
            this.ctMS_lv.SuspendLayout();
            this.SuspendLayout();
            // 
            // lv_ProblemCells
            // 
            this.lv_ProblemCells.AllColumns.Add(this.olvFileName);
            this.lv_ProblemCells.AllColumns.Add(this.olvTime);
            this.lv_ProblemCells.AllColumns.Add(this.olvLongitude);
            this.lv_ProblemCells.AllColumns.Add(this.olvLatitude);
            this.lv_ProblemCells.AllColumns.Add(this.olvOCellName);
            this.lv_ProblemCells.AllColumns.Add(this.olvOLAC);
            this.lv_ProblemCells.AllColumns.Add(this.olvOCI);
            this.lv_ProblemCells.AllColumns.Add(this.olvOBCCH);
            this.lv_ProblemCells.AllColumns.Add(this.olvOBSIC);
            this.lv_ProblemCells.AllColumns.Add(this.olvORxlev);
            this.lv_ProblemCells.AllColumns.Add(this.olvDistance);
            this.lv_ProblemCells.AllColumns.Add(this.olvPCellName);
            this.lv_ProblemCells.AllColumns.Add(this.olvPCellID);
            this.lv_ProblemCells.AllColumns.Add(this.olvPCellLAC);
            this.lv_ProblemCells.AllColumns.Add(this.olvPCellCI);
            this.lv_ProblemCells.AllColumns.Add(this.olvPBCCH);
            this.lv_ProblemCells.AllColumns.Add(this.olvPBSIC);
            this.lv_ProblemCells.AllColumns.Add(this.olvPRxlev);
            this.lv_ProblemCells.AllColumns.Add(this.olvSixScale);
            this.lv_ProblemCells.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvFileName,
            this.olvTime,
            this.olvLongitude,
            this.olvLatitude,
            this.olvOCellName,
            this.olvOLAC,
            this.olvOCI,
            this.olvOBCCH,
            this.olvOBSIC,
            this.olvORxlev,
            this.olvDistance,
            this.olvPCellName,
            this.olvPCellID,
            this.olvPCellLAC,
            this.olvPCellCI,
            this.olvPBCCH,
            this.olvPBSIC,
            this.olvPRxlev,
            this.olvSixScale});
            this.lv_ProblemCells.ContextMenuStrip = this.ctMS_lv;
            this.lv_ProblemCells.Cursor = System.Windows.Forms.Cursors.Default;
            this.lv_ProblemCells.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lv_ProblemCells.FullRowSelect = true;
            this.lv_ProblemCells.GridLines = true;
            this.lv_ProblemCells.HeaderWordWrap = true;
            this.lv_ProblemCells.IsNeedShowOverlay = false;
            this.lv_ProblemCells.Location = new System.Drawing.Point(0, 0);
            this.lv_ProblemCells.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.lv_ProblemCells.Name = "lv_ProblemCells";
            this.lv_ProblemCells.OwnerDraw = true;
            this.lv_ProblemCells.ShowGroups = false;
            this.lv_ProblemCells.Size = new System.Drawing.Size(1047, 658);
            this.lv_ProblemCells.TabIndex = 6;
            this.lv_ProblemCells.UseCompatibleStateImageBehavior = false;
            this.lv_ProblemCells.View = System.Windows.Forms.View.Details;
            this.lv_ProblemCells.VirtualMode = true;
            this.lv_ProblemCells.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.lv_ProblemCells_MouseDoubleClick);
            // 
            // olvFileName
            // 
            this.olvFileName.HeaderFont = null;
            this.olvFileName.Text = "文件名";
            this.olvFileName.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.olvFileName.Width = 120;
            // 
            // olvTime
            // 
            this.olvTime.HeaderFont = null;
            this.olvTime.Text = "时间";
            this.olvTime.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.olvTime.Width = 100;
            // 
            // olvLongitude
            // 
            this.olvLongitude.HeaderFont = null;
            this.olvLongitude.Text = "经度";
            this.olvLongitude.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // olvLatitude
            // 
            this.olvLatitude.HeaderFont = null;
            this.olvLatitude.Text = "纬度";
            this.olvLatitude.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // olvOCellName
            // 
            this.olvOCellName.HeaderFont = null;
            this.olvOCellName.Text = "原小区名";
            this.olvOCellName.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.olvOCellName.Width = 90;
            // 
            // olvOLAC
            // 
            this.olvOLAC.HeaderFont = null;
            this.olvOLAC.Text = "LAC";
            this.olvOLAC.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // olvOCI
            // 
            this.olvOCI.HeaderFont = null;
            this.olvOCI.Text = "CI";
            this.olvOCI.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // olvOBCCH
            // 
            this.olvOBCCH.HeaderFont = null;
            this.olvOBCCH.Text = "BCCH";
            this.olvOBCCH.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // olvOBSIC
            // 
            this.olvOBSIC.HeaderFont = null;
            this.olvOBSIC.Text = "BSIC";
            this.olvOBSIC.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // olvORxlev
            // 
            this.olvORxlev.HeaderFont = null;
            this.olvORxlev.Text = "场强";
            this.olvORxlev.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // olvDistance
            // 
            this.olvDistance.HeaderFont = null;
            this.olvDistance.Text = "隐患小区与原小区距离(M)";
            this.olvDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.olvDistance.Width = 100;
            // 
            // olvPCellName
            // 
            this.olvPCellName.HeaderFont = null;
            this.olvPCellName.Text = "隐患小区名";
            this.olvPCellName.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.olvPCellName.Width = 96;
            // 
            // olvPCellID
            // 
            this.olvPCellID.HeaderFont = null;
            this.olvPCellID.Text = "隐患小区ID";
            this.olvPCellID.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.olvPCellID.Width = 113;
            // 
            // olvPCellLAC
            // 
            this.olvPCellLAC.HeaderFont = null;
            this.olvPCellLAC.Text = "LAC";
            this.olvPCellLAC.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // olvPCellCI
            // 
            this.olvPCellCI.HeaderFont = null;
            this.olvPCellCI.Text = "CI";
            this.olvPCellCI.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // olvPBCCH
            // 
            this.olvPBCCH.HeaderFont = null;
            this.olvPBCCH.Text = "BCCH";
            this.olvPBCCH.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // olvPBSIC
            // 
            this.olvPBSIC.HeaderFont = null;
            this.olvPBSIC.Text = "BSIC";
            this.olvPBSIC.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // olvPRxlev
            // 
            this.olvPRxlev.HeaderFont = null;
            this.olvPRxlev.Text = "场强";
            this.olvPRxlev.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // olvSixScale
            // 
            this.olvSixScale.HeaderFont = null;
            this.olvSixScale.Text = "六强比例";
            this.olvSixScale.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.olvSixScale.Width = 67;
            // 
            // ctMS_lv
            // 
            this.ctMS_lv.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.ctMS_lv.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.toolStripSeparator1,
            this.miExportExcel,
            this.miExportTxt,
            this.toolStripSeparator2,
            this.miShowTestPoint,
            this.miShowTestPointLink});
            this.ctMS_lv.Name = "ctMS_lv";
            this.ctMS_lv.Size = new System.Drawing.Size(365, 172);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(364, 26);
            this.miExpandAll.Text = "展开所有节点";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(364, 26);
            this.miCollapseAll.Text = "收缩所有节点";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(361, 6);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(364, 26);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // miExportTxt
            // 
            this.miExportTxt.Name = "miExportTxt";
            this.miExportTxt.Size = new System.Drawing.Size(364, 26);
            this.miExportTxt.Text = "导出TXT";
            this.miExportTxt.Click += new System.EventHandler(this.miExportTxt_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(361, 6);
            // 
            // miShowTestPoint
            // 
            this.miShowTestPoint.Name = "miShowTestPoint";
            this.miShowTestPoint.Size = new System.Drawing.Size(364, 26);
            this.miShowTestPoint.Text = "显示问题采样点";
            this.miShowTestPoint.Click += new System.EventHandler(this.miShowTestPoint_Click);
            // 
            // miShowTestPointLink
            // 
            this.miShowTestPointLink.Name = "miShowTestPointLink";
            this.miShowTestPointLink.Size = new System.Drawing.Size(364, 26);
            this.miShowTestPointLink.Text = "显示问题采样点(连接同隐患小区的采样点)";
            this.miShowTestPointLink.Click += new System.EventHandler(this.miShowTestPointLink_Click);
            // 
            // GSMMemoryProblemListForm
            // 
            this.ClientSize = new System.Drawing.Size(1047, 658);
            this.Controls.Add(this.lv_ProblemCells);
            this.Name = "GSMMemoryProblemListForm";
            this.Text = "GSM手机记忆效应问题排查";
            ((System.ComponentModel.ISupportInitialize)(this.lv_ProblemCells)).EndInit();
            this.ctMS_lv.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView lv_ProblemCells;
        private BrightIdeasSoftware.OLVColumn olvFileName;
        private BrightIdeasSoftware.OLVColumn olvTime;
        private BrightIdeasSoftware.OLVColumn olvLongitude;
        private BrightIdeasSoftware.OLVColumn olvLatitude;
        private BrightIdeasSoftware.OLVColumn olvOCellName;
        private BrightIdeasSoftware.OLVColumn olvOLAC;
        private BrightIdeasSoftware.OLVColumn olvOCI;
        private BrightIdeasSoftware.OLVColumn olvOBCCH;
        private BrightIdeasSoftware.OLVColumn olvOBSIC;
        private BrightIdeasSoftware.OLVColumn olvORxlev;
        private BrightIdeasSoftware.OLVColumn olvDistance;
        private BrightIdeasSoftware.OLVColumn olvPCellName;
        private BrightIdeasSoftware.OLVColumn olvPCellID;
        private BrightIdeasSoftware.OLVColumn olvPCellLAC;
        private BrightIdeasSoftware.OLVColumn olvPCellCI;
        private BrightIdeasSoftware.OLVColumn olvPBCCH;
        private BrightIdeasSoftware.OLVColumn olvPBSIC;
        private BrightIdeasSoftware.OLVColumn olvPRxlev;
        private BrightIdeasSoftware.OLVColumn olvSixScale;
        private System.Windows.Forms.ContextMenuStrip ctMS_lv;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miExportTxt;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem miShowTestPoint;
        private System.Windows.Forms.ToolStripMenuItem miShowTestPointLink;
    }
}
