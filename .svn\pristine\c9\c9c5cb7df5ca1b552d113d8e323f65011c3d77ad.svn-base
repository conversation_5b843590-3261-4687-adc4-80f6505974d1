<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="panel1.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAq0AAAH3CAYAAACRoWanAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0w0hl6ky4wgPQuIB0EURhmBhjKAMMMTWyIqEBEEREBRZCggAGjoUis
        iGIhKKhgD0gQUGIwiqioZEbWSnx5ee/l5ffHvd/aZ+9z99l7n7UuACRPHy4vBZYCIJkn4Ad6ONNXhUfQ
        sf0ABniAAaYAMFnpqb5B7sFAJC83F3q6yAn8i94MAUj8vmXo6U+ng/9P0qxUvgAAyF/E5mxOOkvE+SJO
        yhSkiu0zIqbGJIoZRomZL0pQxHJijlvkpZ99FtlRzOxkHlvE4pxT2clsMfeIeHuGkCNixEfEBRlcTqaI
        b4tYM0mYzBXxW3FsMoeZDgCKJLYLOKx4EZuImMQPDnQR8XIAcKS4LzjmCxZwsgTiQ7mkpGbzuXHxArou
        S49uam3NoHtyMpM4AoGhP5OVyOSz6S4pyalMXjYAi2f+LBlxbemiIluaWltaGpoZmX5RqP+6+Dcl7u0i
        vQr43DOI1veH7a/8UuoAYMyKarPrD1vMfgA6tgIgd/8Pm+YhACRFfWu/8cV5aOJ5iRcIUm2MjTMzM424
        HJaRuKC/6386/A198T0j8Xa/l4fuyollCpMEdHHdWClJKUI+PT2VyeLQDf88xP848K/zWBrIieXwOTxR
        RKhoyri8OFG7eWyugJvCo3N5/6mJ/zDsT1qca5Eo9Z8ANcoISN2gAuTnPoCiEAESeVDc9d/75oMPBeKb
        F6Y6sTj3nwX9+65wifiRzo37HOcSGExnCfkZi2viawnQgAAkARXIAxWgAXSBITADVsAWOAI3sAL4gWAQ
        DtYCFogHyYAPMkEu2AwKQBHYBfaCSlAD6kEjaAEnQAc4DS6Ay+A6uAnugAdgBIyD52AGvAHzEARhITJE
        geQhVUgLMoDMIAZkD7lBPlAgFA5FQ3EQDxJCudAWqAgqhSqhWqgR+hY6BV2ArkID0D1oFJqCfoXewwhM
        gqmwMqwNG8MM2An2hoPhNXAcnAbnwPnwTrgCroOPwe3wBfg6fAcegZ/DswhAiAgNUUMMEQbigvghEUgs
        wkc2IIVIOVKHtCBdSC9yCxlBppF3KAyKgqKjDFG2KE9UCIqFSkNtQBWjKlFHUe2oHtQt1ChqBvUJTUYr
        oQ3QNmgv9Cp0HDoTXYAuRzeg29CX0HfQ4+g3GAyGhtHBWGE8MeGYBMw6TDHmAKYVcx4zgBnDzGKxWHms
        AdYO64dlYgXYAux+7DHsOewgdhz7FkfEqeLMcO64CBwPl4crxzXhzuIGcRO4ebwUXgtvg/fDs/HZ+BJ8
        Pb4LfwM/jp8nSBN0CHaEYEICYTOhgtBCuER4SHhFJBLVidbEACKXuIlYQTxOvEIcJb4jyZD0SS6kSJKQ
        tJN0hHSedI/0ikwma5MdyRFkAXknuZF8kfyY/FaCImEk4SXBltgoUSXRLjEo8UISL6kl6SS5VjJHslzy
        pOQNyWkpvJS2lIsUU2qDVJXUKalhqVlpirSptJ90snSxdJP0VelJGayMtoybDFsmX+awzEWZMQpC0aC4
        UFiULZR6yiXKOBVD1aF6UROoRdRvqP3UGVkZ2WWyobJZslWyZ2RHaAhNm+ZFS6KV0E7QhmjvlygvcVrC
        WbJjScuSwSVzcopyjnIcuUK5Vrk7cu/l6fJu8onyu+U75B8poBT0FQIUMhUOKlxSmFakKtoqshQLFU8o
        3leClfSVApXWKR1W6lOaVVZR9lBOVd6vfFF5WoWm4qiSoFKmclZlSpWiaq/KVS1TPaf6jC5Ld6In0Svo
        PfQZNSU1TzWhWq1av9q8uo56iHqeeqv6Iw2CBkMjVqNMo1tjRlNV01czV7NZ874WXouhFa+1T6tXa05b
        RztMe5t2h/akjpyOl06OTrPOQ12yroNumm6d7m09jB5DL1HvgN5NfVjfQj9ev0r/hgFsYGnANThgMLAU
        vdR6KW9p3dJhQ5Khk2GGYbPhqBHNyMcoz6jD6IWxpnGE8W7jXuNPJhYmSSb1Jg9MZUxXmOaZdpn+aqZv
        xjKrMrttTjZ3N99o3mn+cpnBMs6yg8vuWlAsfC22WXRbfLS0suRbtlhOWWlaRVtVWw0zqAx/RjHjijXa
        2tl6o/Vp63c2ljYCmxM2v9ga2ibaNtlOLtdZzllev3zMTt2OaVdrN2JPt4+2P2Q/4qDmwHSoc3jiqOHI
        dmxwnHDSc0pwOub0wtnEme/c5jznYuOy3uW8K+Lq4Vro2u8m4xbiVun22F3dPc692X3Gw8Jjncd5T7Sn
        t+duz2EvZS+WV6PXzAqrFetX9HiTvIO8K72f+Oj78H26fGHfFb57fB+u1FrJW9nhB/y8/Pb4PfLX8U/z
        /z4AE+AfUBXwNNA0MDewN4gSFBXUFPQm2Dm4JPhBiG6IMKQ7VDI0MrQxdC7MNaw0bGSV8ar1q66HK4Rz
        wzsjsBGhEQ0Rs6vdVu9dPR5pEVkQObRGZ03WmqtrFdYmrT0TJRnFjDoZjY4Oi26K/sD0Y9YxZ2O8Yqpj
        ZlgurH2s52xHdhl7imPHKeVMxNrFlsZOxtnF7YmbineIL4+f5rpwK7kvEzwTahLmEv0SjyQuJIUltSbj
        kqOTT/FkeIm8nhSVlKyUgVSD1ILUkTSbtL1pM3xvfkM6lL4mvVNAFf1M9Ql1hVuFoxn2GVUZbzNDM09m
        SWfxsvqy9bN3ZE/kuOd8vQ61jrWuO1ctd3Pu6Hqn9bUboA0xG7o3amzM3zi+yWPT0c2EzYmbf8gzySvN
        e70lbEtXvnL+pvyxrR5bmwskCvgFw9tst9VsR23nbu/fYb5j/45PhezCa0UmReVFH4pZxde+Mv2q4quF
        nbE7+0ssSw7uwuzi7Rra7bD7aKl0aU7p2B7fPe1l9LLCstd7o/ZeLV9WXrOPsE+4b6TCp6Jzv+b+Xfs/
        VMZX3qlyrmqtVqreUT13gH1g8KDjwZYa5ZqimveHuIfu1nrUttdp15UfxhzOOPy0PrS+92vG140NCg1F
        DR+P8I6MHA082tNo1djYpNRU0gw3C5unjkUeu/mN6zedLYYtta201qLj4Ljw+LNvo78dOuF9ovsk42TL
        d1rfVbdR2grbofbs9pmO+I6RzvDOgVMrTnV32Xa1fW/0/ZHTaqerzsieKTlLOJt/duFczrnZ86nnpy/E
        XRjrjup+cHHVxds9AT39l7wvXbnsfvlir1PvuSt2V05ftbl66hrjWsd1y+vtfRZ9bT9Y/NDWb9nffsPq
        RudN65tdA8sHzg46DF645Xrr8m2v29fvrLwzMBQydHc4cnjkLvvu5L2key/vZ9yff7DpIfph4SOpR+WP
        lR7X/aj3Y+uI5ciZUdfRvidBTx6Mscae/5T+04fx/Kfkp+UTqhONk2aTp6fcp24+W/1s/Hnq8/npgp+l
        f65+ofviu18cf+mbWTUz/pL/cuHX4lfyr468Xva6e9Z/9vGb5Dfzc4Vv5d8efcd41/s+7P3EfOYH7IeK
        j3ofuz55f3q4kLyw8Bv3hPP74uYdwgAAAAlwSFlzAAALDAAACwwBP0AiyAAAN/NJREFUeF7t3QusZWdB
        9/9egE6n7bQznUJJvLyB2BjFKKJEo8FQr/hCizLtVEBoLQGkEg1gNGU6WHAqUqZFgValGEyKQIlQEGFa
        4KWQNOpfNJaLF7Rz6QVakFsptHPpPH9++z1P39XF3vvsZ+bss07nfD7Jk7P3WmuvvffMWnt/zzrr7HNU
        KeUrhmEYhmEYxsNy3Pvtsffb41PfHke0RCsAAA9vuxa+HrFEKwDAw9/nFr4esUQrAMDDn2gFAGDFE60A
        AKx4ohUAAIYmWgEAWPFEKwAAK55oBQBgxROtAACseKIVAIAVT7QCALDiiVYAAFY80QoAwIonWgEAWPFE
        KwAAK55oBQBgxROtAACseKIVAIAVT7QCALDiiVYAAFY80QoAwIonWgEAWPFEKwAAK55oBQBgxROtAACs
        eKIVAIAVT7QCALDiiVYAAFY80QoAwIonWgEAWPFEKwAAK55oBQBgxROtAACseKIVAIAVT7QCALDiiVYA
        AFY80QoAwIonWgEAWPFEKwAAK55oBQBgxTsionXfrbvLl877rdHXr23/s/L5o374IeObO/7PwpKl3H3m
        s0fX773u/aNlJ8lymZ+vWS8AAMM5Yo60JkQTmF+95HWjIK2+fNGW0bz6tUZrJHQnhWtdLiPLAQAwnCPu
        9IBEaD2Kmq/TovWBe79Z7rnmr0fL9o/O9kc3hAEAWF5HRLTm6GqiNBHaEq3T5OjqLMsBADB/R0S0Hrj7
        Sw/+qH+porUuDwDA8Fbl6QHfeOf1D/mxf0b3VICMRHCmAwAwvCMmWmuo1lit0TntSGumdcO03jbqua4A
        AAzviInWnNdaQzNfc7T0/ls+O1O05rbd82Ejy/jUAACAleGIida7zviFct/N/9/ocuIzcZrR/4zVfrTm
        djVOu9Ga82QTvvmaaQlhAACGcUREa46oJj4TmAnRhGkuJ04zPfOrfrTmeo6y1l/mysi0BGzmJ1b7n/0K
        AMDyOmKitcZlQjMRWiVQMy0xmiOnNW6zTKblcm6b6QnVjHp0Nl8zvZ5qAADAMI6Y0wNWooMHDy5cAgDg
        cIjWOdi7d2+57bbbyp49e0aXAQA4PKJ1Dr761a+Wd7zjHeWKK64on/nMZxamAgBwqETrHNx1113lpS99
        aXniE59Y3v72tztNAADgMInWOcjR1Z/92Z8tRx11VLn44ovLN7/5/34xDACAdqJ1id13333lbW97W3n8
        4x8/itZnPetZ5dOf/vTCXAAADoVoXWJ33nlnedGLXlQe8YhHjKL1e7/3e8tb3vKWcuDAgYUlAABoJVqX
        2Cc+8Ynykz/5k6NgzUi8XnjhhaOYBQDg0IjWJfQ///M/ZevWrWX9+vUPRmvGD/7gD5Z3vetd5YEHHlhY
        EgCAFqJ1CX3yk58sT3nKUx4SrBmPfOQjR0dbb7/99oUlAQBoIVqXyJe//OVy6aWXlo0bN35HtGZ8//d/
        f7n22mud2woAcAhE6xL52Mc+Vn78x398bLDW8cxnPrN89rOfXbgFAACzEq1LIH+y9aKLLionnnji2Fit
        49GPfnR59atfPTr3FQCA2YnWw7R3797RR1rlo60SpkcffXQ55phjRl8zjj322NGo4fqEJzyhvPe97/VL
        WQAADUTrYfq7v/u78mM/9mOjIF2zZk35ru/6rtER1YRrPu7qSU96UjnrrLPK4x73uAfD9alPferoo7EA
        AJiNaD0MCc9f/uVfHoXoySefXH7rt36rvPWtby0/93M/N5p2/PHHl1e+8pXli1/8Ynn/+99fnva0p5UT
        TjihrF27tmzevLn8y7/8y8KaAACYRrQeot27d5eLL764PPnJTy6/8Ru/UXbs2FH2798/mn722Wc/GK2v
        etWrHvzEgLvuuqtcf/31o+V/4Rd+ofzFX/xF+cpXvjKaBwDAZKL1EH3rW98qd9xxR9mzZ0/5xje+UQ4e
        PDiavnPnzodE65YtW0bnvVZZ7t577x39hayvf/3rzm0FAJiBaD0MCdAaq9Wtt95anvGMZ4yiNee45vSA
        +++/f2Hu/+PzWgEAZjdztObH33fffffCtUOXo4wrWZ7j4TzPWaMVAIDZzRyt11133eizSA/H9u3by3nn
        nfdgGHbHLDGbx3DmmWfOPBLa02R9GV15jBldeWx53LM8xm60jjs9AACAdg+J1muuuebBaBs3EmLjpteR
        20+SOMztJ40zzjjjOwKyK8GYZW6++eax0dsfNXCnyXJZJuus6nPpyvOaNdjzi1jdaN26devoF7QAAObo
        cwtfj1hN0TrLGHc0MgGZ4MxRyH5cZtxyyy2LHhnNcgnBfJ1F1jUpWhOpmZ+Rx5zHVq8nTjPq9frY82+T
        693AHSe/nNWN1m3btjl/FQCYt9UVrV0JzFkidrGIq9GXMB0n0/Oj92lHWaNG67jHMG4kPLPevvp4xt1m
        lpHbTnus+VirbrTmNj4hAACYs9UZrfVH8Zdccsl3RFt/ZLlJRz8TdznamYAcd9tMT9xNmp9wrlqjNSO3
        r0dMqzqv6h51nTaqXO7evu/LX/7yKJZrtL75zW8WrQDAvK3OaG35UXyidNxyCdbEWwKv++P2ltHV8pi6
        +pHavT7rUdc8j3q+bkK+Xh7nnnvuKS984QsfjNa3vOUtohUAmLfVfaQ1UTcuJrtj2pHWqOF3uFoeU3f0
        f9Eqt8+IWR9b1lOXS4BPe7733XdfecUrXvFgtL7tbW/7js9yBQBYYqv3nNYaaouNGoCT1OWqHKlMSE4a
        mT9JfUyLnbbQnT/t8dVlEqHTRtaR5Waxb9++0ScG1Gi99tprRSsAMG+rL1q754G2jMTdODUMI0c8c7R0
        0qcIZPpiR24j65v0I/rcNvE7i/wSWO4vgbnYWCzOq3wmaz6bNbc58cQTyzvf+U7RCgDM2+qK1pZfwOqO
        LD8pNusykbhdLCgzf5Zorevsa4nWLJtQTrzWkeeS0Z2WkWXHfZxXX/761e///u+PovWEE04o7373u0Ur
        ADBvqyta+8GX6wnNGondkSOPCb4qt8uyfXX5OJRorY+pOxLIGf3pdYybl3NRu/KYukdSZxlZb/c5j5No
        vfjii0fL50jr9ddf7xexAIB5W93RmtCb9HFU9ehqPfqY242L1vrJAdGP1tw28+sH9ycIM79Ga113rndH
        7j+36U/P6K6jO7pHSXM9UVmPoHZHfX796Rn1CPQ0OT3gla985Wj9p5xyyiha/XEBAGDOVne0TgrRyLIJ
        s3yNSctmeuIw+tEa9Zec8rFSmVejcNovP2XZaY+rfx99/cfelfucdL/T5lX9aH3f+94nWgGAeROthxOt
        idXu+sZF6yRZdlwgZp3dI7x9/ecwTv+xdy1ltK5fv7584AMfcHoAADBvqzta6ykAmTZu5Ihnlev9aM2P
        /ru/dd8Srfnxfz8Q6+Ob9pv8SxmtCeNczuPOqKcyTJOPvHrVq14lWgGA5bS6ozVyZLNGW3fk46sSjxm5
        nritpwFEpnejNrL+LFfPDZ02slz/jwJk2rRgjVmitZ5LO+5obb3/KveX61lnHve40O36+te/Xi677LJR
        tG7YsEG0AgDLYXVFa425WeWXnmrk9WMy88YFXqb3A3jc6AZr5AhnN4qnWSxsp6mRfqj60frBD35QtAIA
        87a6opXD143WU089VbQCAMtBtNKmf6T1Qx/6kGgFAOZNtNKmf6T1xhtv9BexAIB5E6206Ubrxo0by0c+
        8hHRCgDMm2iljWgFAAYgWmnTPz3gwx/+sGgFAOZNtNKmH61+EQsAWAailTaiFQAYgGilzde+9rWybdu2
        B6M1f6hAtAIAcyZaadM/0ipaAYBlIFpp841vfKNcfvnlo2jNHxcQrQDAMhCttEm0vu51r3OkFQBYTqKV
        Nnv27CnPfvazHxKtPvIKAJgz0UqbW2+9tTzjGc8QrQDAchKttBGtAMAARCttRCsAMADRShvRCgAMQLTS
        RrQCAAMQrbT57//+7/L0pz9dtAIAy0m00mbnzp3lV37lV0bR6o8LAADLRLTS5vbbby8XXHDBKFrXr19f
        brjhBkdaAYB5E620ueuuu8rv/M7vlEc+8pGjI6033XTTwhwAgLkRrbS5++67y8tf/vJy3HHHlZNPPrlc
        f/31Tg8AAOZNtNKmG62PetSjyiWXXFL27t27MBcAYC5EK2360bplyxbRCgDMm2iljWgFAAYgWmkjWgGA
        AYhW2ohWAGAAopU2ohUAGIBopY1oBQAGIFppI1oBgAGIVtqIVgBgAKKVNqIVABiAaKWNaAUABiBaaZNo
        fcUrXiFaAYDlJFppk2h92cteNgpW0QoALBPRSpsvfOEL5aUvfWk59thjR0dbt27dKloBgHkTrbT5/Oc/
        X17ykpeUo446qpxwwgll27ZtZd++fQtzAQDmQrTSRrQCAAMQrbQRrQDAAEQrbUQrADAA0Uob0QoADEC0
        0ka0AgADEK20ueOOO8oLXvCCUbSeeOKJohUAWA6ilTa33357ueCCC0QrALCcRCttEq3nn3++aAUAlpNo
        pc1tt91Wnvvc54pWAGA5iVba7Nq1q2zatGkUrSeddJJoBQCWg2ilze7du8u55547itZ169aVyy67TLQC
        APMmWmlz5513lhe/+MWjaF27dm259NJLRSsAMG+ilTZf/OIXy+/+7u+WNWvWlEc96lFly5YtZe/evQtz
        AQDmQrTS5u677y4vf/nLy3HHHSdaAYDlIlppI1oBgAGIVtqIVgBgAKKVNqIVABiAaKWNaAUABiBaaSNa
        AYABiFbaiFYAYACilTaiFQAYgGiljWgFAAYgWmkjWgGAAYhW2ohWAGAAopU2ohUAGIBopU2i9RWveIVo
        BQCWk2ilTaL1ZS972ShYRSsAsExEK22+8IUvlJe+9KXl2GOPHR1tFa0AwDIQrbT5/Oc/X17ykpeUo48+
        WrQCAMtFtNJGtAIAAxCttBGtAMAARCttRCsAMADRShvRCgAMQLTSRrQCAAMQrbS54447ygte8IJy1FFH
        iVYAYLmIVtrcfvvt5fzzzxetAMByEq20Ea0AwABEK21uu+228tznPncUrWvWrBGtAMByEK202bVrV9m0
        adMoWk8++eRy7bXXlgMHDizMBQCYC9FKm0Tr5s2bR9G6cePG8vGPf3xhDgDA3IhW2uTTAy688MJRtK5f
        v77ceOON5eDBgwtzAQDmQrTSJp/TetFFF5VjjjmmnHLKKWXHjh3lgQceWJgLADAXopU29Y8LJFpzpDXR
        6kgrADBnopU2ohUAGIBopY1oBQAGIFpp041W57QCAMtEtNLGkVYAYACilTY1Wo8++uiyYcOG8tGPflS0
        AgDzJlpp043WHGm94YYbRCsAMG+ilTbdaD3++OPLtm3byr59+xbmAgDMhWilTTdajzvuuLJly5ayd+/e
        hbkAAHMhWmkjWgGAAYhW2ohWAGAAopU2ohUAGIBopY1oBQAGIFppI1oBgAGIVtqIVgBgAKKVNqIVABiA
        aKVNovWiiy4qxxxzjGgFAJaLaKXNnXfeWV784hc70goALCfRSps77rijXHjhheWoo44SrQDAchGttLn9
        9tvL+eefL1oBgOUkWmkjWgGAAYhW2ohWAGAAopU2e/bsKc9+9rNH0bpmzRrRCgAsB9FKm127dpVNmzaJ
        VgBgOYlW2uzevbv82q/9mmgFAJaTaKVNPvLqhS98oXNaAYDlJFpp0/2LWOvWrSt//dd/XQ4cOLAwFwBg
        LkQrbRKtL3nJS0Z/EevUU08tH//4xxfmAADMjWilTY3WHGldv359ufHGG8vBgwcX5gIAzIVopU03Wk85
        5ZSyY8eO8sADDyzMBQCYC9FKm/6R1kSrI60AwJyJVtqIVgBgAKKVNk4PAAAGIFpp04/WG264wZFWAGDe
        RCttarTmI682bNhQPvaxjy3MAQCYG9FKm2605pzWD37wg460AgDzJlpp043WNWvWlEsvvdSfcQUA5k20
        0qYbrccdd1zZsmWLaAUA5k200ka0AgADEK20Ea0AwABEK21EKwAwANFKG9EKAAxAtNJGtAIAAxCttBGt
        AMAARCttRCsAMADRShvRCgAMQLTSRrQCAAMQrbQRrQDAAEQrbRKtF110UTnmmGNEKwCwXEQrbRKtv/mb
        v+lIKwCwnEQrbe64445y4YUXlqOOOkq0AgDLRbTS5vbbby/nn3++aAUAlpNopY1oBQAGIFppI1oBgAGI
        Vtrs2bOnPOc5zxlF65o1a0QrALAcRCttdu3aVTZt2jSK1lNOOaW8+93vLg888MDCXACAuRCttEm0nnPO
        OaNo3bhxY7npppsW5gAAzI1opc1tt91Wnve8542idcOGDeWGG24oBw8eXJgLADAXopU2+UWsCy64YBSt
        69evLzt27HB6AAAwb6KVNt1PD6jR6kgrADBnopU2ohUAGIBopc24aHV6AAAwZ6KVNt1o9YtYAMAyEa1d
        t95668Klh5o0veuWW25ZuPSdcjRyJbn77rtH41B0o/XUU0/1kVcAwHJYfdF60UUXjQ3MhOWZZ565cO2h
        Mn1aeCYAJ922u97c93XXXTe6PEnmZ/lZx2JBnPX173P79u2j0XXvvfeW8847b/R1mm605o8LXH/99U4P
        AADmbfVFa4L1jDPOeDBcE30JuARlpudyIi/xVuMu0zM/l8cddc06EnzjdKM195nL11xzzeh6X+4z93Xz
        zTc/eDR02qiBO02WyzJZZ1WfV1ceU57jYrrRetxxx5VLLrnEn3EFAOZtdZ4e0A3XRGjCMtFWj1zWwMvl
        Gp2Zn8uJwL7M60dgVW9f5fYJ3MRe/6hm5iUGx93HOP11d+U51Mdfw7teT5xm1OuJ38yvz7EbuH3daF2z
        Zo1oBQCWw+o9pzXBmnBLIGYk3BJ39XrUyzVmu/O6EqGZP06mjwvLxF4/dLPuxGCmzzLy+Mcd4a0ROu42
        s4zcNusYpxutJ554Ytm2bVvZt2/fwlwAgLlY3b+IVX9cn5FQy6jX65HXjARad15uV+VIbT06Wo/adkf3
        CG5/JAy7RzVbozWje59VnVd1j7pOG1Uud2/f1Y3Wk046SbQCAMthdUVrIjFHJicdLR13hLGeSpDwHHe7
        LF+jtcZed9T19qfX0b2/Gq3j7meauq6qez3rn3b/deRxJrAjR4Hr5T5HWgGAAay+I60JtH6AJjYTbTlq
        mcDrHknNj+ATfhm5Xfc81PqLU9NCMwGY+d11TlLXl/uqRz9nGXlc3SO29fFGjdLFZD11uXraxDiiFQAY
        wOo8PaAboPVIaoI1cr3Oy3KJ2SoBmlGDrh6hnBatWVf3KOZiajzWc14nje78Gqjj1GXy+KaNrCPLLUa0
        AgADWL3ntCYiE6o5qphQTaTWgMv0jMRm5tXpkVhMWGZ+gjTzJ0VrlkkQ5yhovuY+ZpWAnBS6ua/c9yxq
        lOcxLjamxW8lWgGAAazeaK1xGom1WcIuwdpVQzfzxkVr1psojgTwLFFY1SOk47REa32eidc68jwyutMy
        suxiYS1aAYABrO4jrTUiE4C5nGibNrJcvnbl+rhoTfzVo6yRo7O53l8u6rq7I8tm9KfXMW5eDeQq0VuD
        e9aR9daYH0e0AgADWJ3RWoOyxlmCb6mjNVHcj8h6hHOceh911NMD+tMz8rjrY+mO7lHSXM/jqkdQu6Me
        xe1Pz8jjm3SEN0QrADCA1RmtOerZ/QWrBGAibJaRsOvK9f70hGKieNyymT7LaQJ5fHmc42Q9eczTjHtc
        VY3WcabNi0TrBRdcMFq3aAUAlsnqjNZ+EC7lkda63KTgrEGbr5PUZSadX1rvY5r+4+o63Gh93vOeJ1oB
        gOW0+qK1f5Q1pkVm12LRmpFlFjuSWqN03H3Oso66zDTdx9XXDdOEcS7nsWTklIZJn1oQu3fvLps3bx6t
        21/EAgCWyeqK1gRaYq/7QfyRaEtEZt5io3/0s8Zh1tnyCQEJ16yvG4i57SynD8wSrXmcCdBxR2u70Rq5
        v1zPOnNO67jQrUQrADCA1XekddKP5RNqi41xARiJvqx30ronyTq7gZqAnXUdi4XtNLmPWY4sj5NoPffc
        c0UrALCcVuc5rRy6nTt3lrPPPnsUrevWrROtAMByEK202bVrV9m0aZMjrQDAchKttMmnBzz/+c8fRatP
        DwAAlolopY3PaQUABiBaaeMvYgEAAxCttBGtAMAARCttRCsAMADRShvRCgAMQLTSRrQCAAMQrbTpRqvP
        aQUAlolopU03WteuXVsuvfRS0QoAzJtopU03Wo877rjyyle+stx///0LcwEA5kK00qYbrWvWrClbt24t
        e/fuXZgLADAXopU2fhELABiAaKWNaAUABiBaadON1nx6wGWXXSZaAYB5E620SbQ+//nPH0Xr+vXry+tf
        //qyf//+hbkAAHMhWmnTj9bt27eLVgBg3kQrbUQrADAA0Uob0QoADEC00ka0AgADEK206Ubrhg0byhVX
        XCFaAYB5E6206UbraaedVt74xjeWAwcOLMwFAJgL0UqbbrQ+5jGPKVdddZVoBQDmTbTSRrQCAAMQrbQR
        rQDAAEQrbUQrADAA0Uob0QoADEC00ka0AgADEK20Ea0AwABEK21EKwAwANFKG9EKAAxAtNJGtAIAAxCt
        tBGtAMAARCttRCsAMADRShvRCgAMQLTSZs+ePeXZz362aAUAlpNopc3u3bvLOeecM4rW008/XbQCAMtB
        tNKmG62Pfexjy5/92Z+JVgBg3kQrbbrRunHjxvKGN7yh7N+/f2EuAMBciFbaJFo3b948itYTTjihXHrp
        pWXfvn0LcwEA5kK00ibRet55542i9fjjjy9bt24te/fuXZgLADAXopU2u3btKps2bRpF60knnVS2bdvm
        SCsAMG+ilTa33nprOeuss0bRum7dOtEKACwH0UqbnTt3lrPPPnsUraecckp57Wtf6xexAIB5E620yTmt
        55577ihaN2zYULZv3y5aAYB5E620Ea0AwABEK21EKwAwANFKG9EKAAxAtNJGtAIAAxCttOlG66mnnlqu
        vPJK0QoAzJtopU2i9ZxzzhlF66Mf/ejypje9SbQCAPMmWmnTjdbTTz+9XH311eXAgQMLcwEA5kK00qYf
        rVdddZVoBQDmTbTSRrQCAAMQrbQRrQDAAEQrbUQrADAA0Uob0QoADEC00ka0AgADEK20Ea0AwABEK21E
        KwAwANFKG9EKAAxAtNJGtAIAAxCttBGtAMAARCttRCsAMADRShvRCgAMQLTSph+tV199tWgFAOZNtNIm
        0XruueeOonXDhg1l+/btZf/+/QtzAQDmQrTSJtG6efPmUbQef/zx5VWvelXZu3fvwlwAgLkQrbTpRuva
        tWvLH/zBH5R9+/YtzAUAmAvRSptutJ500kll27ZtohUAmDfRShvRCgAMQLTSZteuXWXTpk2jaD355JPL
        a1/7Wr+IBQDMm2ilza233lqe8YxnjKJ148aN5YorrhCtAMC8iVbaJFrPOuusUbSeeuqpohUAWA6ilTY+
        pxUAGIBopY1oBQAGIFppk2itf8Y157ReeeWVohUAmDfRSptutD7mMY8pV111VTlw4MDCXACAuRCttNm5
        c2c5++yzR9F6+umni1YAYDmIVtp0P/JKtAIAy0S00ka0AgADEK20Ea0AwABEK21EKwAwANFKG9EKAAxA
        tNJGtAIAAxCttBGtAMAARCttRCsAMADRShvRCgAMQLTSRrQCAAMQrbQRrQDAAEQrbUQrADAA0Uob0QoA
        DEC00ka0AgADEK20Ea0AwABEK2260bphw4Zy+eWXl3379i3MBQCYC9FKm260rl27tmzdurXs3bt3YS4A
        wFyIVtp0o/XEE08sr371qx1pBQDmTbTSphut69atK5dddploBQDmTbTSph+t27ZtE60AwLyJVtp0o3X9
        +vXlda97nWgFAOZNtNKmG60bN24sV1xxRdm/f//CXACAuRCttOlG66mnnipaAYDlIFppI1oBgAGIVtqI
        VgBgAKKVNonWs846S7QCAMtJtNJm9+7d5dxzzx1Fa/6M6/bt20UrADBvopU2ohUAGIBopU2i9ZxzzhlF
        az7y6sorrxStAMC8iVbadKP1MY95TLnqqqvKgQMHFuYCAMyFaKVNN1pPP/30cvXVV4tWAGDeRCttdu7c
        Wc4+++wHo9WRVgBgGYhW2nQ/p1W0AgDLRLTSRrQCAAMQrbQRrQDAAEQrbUQrADAA0Uob0QoADEC00ka0
        AgADEK20Ea0AwABEK21EKwAwANFKG9EKAAxAtNJGtAIAAxCttBGtAMAARCttutF62mmnlTe84Q1l//79
        C3MBAOZCtNKmG60nn3xy+aM/+qOyb9++hbkAAHMhWmnTjda1a9eWrVu3lr179y7MBQCYC9FKm260Hn/8
        8WXLli2iFQCYN9FKG9EKAAxAtNJGtAIAAxCttBGtAMAARCttRCsAMADRSptutJ5wwgnl0ksv9ZFXAMC8
        iVbadKN13bp15bLLLhOtAMC8iVba9KN127ZtohUAmDfRSpudO3eWZz7zmaIVAFhOopU2u3fvLps3bx5F
        60knnSRaAYDlIFppI1oBgAGIVtqIVgBgAKKVNqIVABiAaKWNaAUABiBaaSNaAYABiFba+MgrAGAAopU2
        /rgAADAA0Uob0QoADEC00ka0AgADEK20Ea0AwABEK21EKwAwANFKG9EKAAxAtNJGtAIAAxCttBGtAMAA
        RCttRCsAMADRShvRCgAMQLTSRrQCAAMQrbQRrQDAAEQrbUQrADAA0UqbbrSuXbu2bN26tezdu3dhLgDA
        XIhW2nSjdcOGDeU973lPeeCBBxbmAgDMhWilTTdaTzvttHLTTTctzAEAmBvRSptutJ566qnlhhtuKAcP
        HlyYCwAwF6KVNv3TA3bs2OH0AABg3kQrbfpHWhOtjrQCAHMmWudl+/bto8BbiQ7nce3cubM861nPesiR
        VtEKAMzZ6ovWu+++exRa3TFOlkvcZf4111xTLrrootGYVW5z3nnnLVyb3SWXXFLOPPPMmUbWv1iAJp5v
        ueWWhWv/V27bf95ZzyzPb/fu3aP7Fa0AwDJafdFa4zMxl1EDrl6uI1F2xhlnjC5n3nXXXTcK2eree+8d
        xVv3Nv2RdYybXkc/Em+++ebRfSYgc1+Ljfo8pkmw5r6yfJXr/WjNuvIcF5No3bx5s2gFAJbT6ovWfrAl
        +nK9HlXNSODV6dN0AzIj60j41UisI9czvR+jCd+u3F+WnVUe46Rorc8lo4Z6vZ77yJHgej2XE8t5jLne
        PzLbJVoBgAGI1klxOku0diX86m364VnvM0dmpx3NzDKJxxqji42sL/fbl1MMMm/cbRYbuW0ew6RwFa0A
        wABEa0Kte72aNH2c7tHV3GZStNajrgnDcbJMS7QmWLPOehS36j7HHM3N5cVGTk2osu5MG8c5rQDAAJzT
        msDrBls1Ldy6Eo5ZR+Ixcptc7+pHZKJ1XLiOu+0suuuP7vU811mOuiaW679Dbj/u3yR27drlSCsAsNxW
        X7TWo6GJzQRd4iw/Cu9HXEKvG7d1dH8cn8uJzxqsMS48+1EZuV1/Wh5H4jHTZx05yprbdM+PzeOsR17H
        3fc4uU2Wy3PJ854k633mM58pWgGA5bT6orVKbCXUIiFWo7SOhOC4I5Tdc1L7v0gVdV1dud798f00WX+W
        T9TW+xw3uvOnRWmiNetMjE4bi62nyvPwxwUAgGW2+qI1R0czarQm2PrxmeuJ1v4R03FypDbrahnTfju/
        yn1P+hF91pHHPosEawJzsZHnO0tYi1YAYACrL1q7RydrsPUDMPNylDVjscDMj9ITmIm+GrqLjdxn9zSD
        cbJcHsc4LdGaKM9z6I6sOzHbnZYYzbKLEa0AwABWd7Tm67gjrYnVRF3GtPM7uxJzidZJoRm5n/xYP9HY
        DcTcpgZtHYsF8Lj5ebxV7ivPo4b5rCO36f97dIlWAGAAorUv0xOECbeEZS7nSOQsukcx+2qYJlrHRWHu
        qzuybG7Tn56R9dfg7o7ueuv99ZfJqI9x0rzcdhLRCgAMQLR21WjrRmd+jJ+jj7PKOrJ8jtAm8HJeai5n
        2rQY7Ep8JpazrnHGPfa+LJPnMs60MBWtAMAKtLqjNUc9E6j1/NJcz+hKQCbkFjsHtSu3Sagm7BKfLbeN
        PKZpoZzHLloBgFVkdUZrgjAjQZlIzdHQccFajTsC25dlEnBZR5atyydeu/cz7tSArpxisNgpCbmfpYrW
        RGiiOtczct+TPrUgRCsAMIDVFa2JyH5A1h/nTwrWqoZrd7msr0Zq1pv1JAD7wVnDsIZylk909iM40xYL
        1kgoLhatuc9Jy3SjNc+r+zwWOyosWgGAAayuaE2k9o901iOks6iBV+V2GYnMxY6gVvX+sp5EZa7X6QnG
        en2auo5DlftOfB4K0QoADGD1nR7A4RGtAMAARCttRCsAMADRShvRCgAMQLTSph+tH/3oR0UrADBvopU2
        /Wj90Ic+JFoBgHkTrbTpRuvatWvLa17zmrJ3796FuQAAcyFaadON1pNOOmkUrfv27VuYCwAwF6KVNt1o
        XbduXbnssstEKwAwb6KVNv1o3bZtm2gFAOZNtNJm165dZdOmTQ+eHiBaAYBlIFpps3PnzvKrv/qro2hd
        v359ufzyy0UrADBvopU23dMDNm7cWK644oqyf//+hbkAAHMhWmnTjdZ8TqtoBQCWgWiljWgFAAYgWmkj
        WgGAAYhW2ohWAGAAopU2ohUAGIBopY1oBQAGIFppI1oBgAEsSbTed9995ROf+ER561vfWj75yU8uTF0Z
        ROsSE60AwMPVgQMHyo033lie85znlN/+7d8un/rUp0bTVgLRusREKwDwcJZQ/cVf/MVy9NFHl6c85Snl
        yiuvLJ/97GcHj1fRusREKwDwcHbvvfeWP//zPy/f933fN+qZE044ofz8z/98+ZM/+ZPyz//8z6P5QxCt
        S0y0AgAPd+mZ888/vxxzzDGjpsk47rjjyk/8xE+U17zmNeXDH/5wuf3228vBgwcXbjF/onWJiVYA4OHu
        gQceKB/72MfK0572tIeEax2nn356+fVf//Xytre9bfQLW1/4whfmfvqAaF1iohUAOFJ89KMfLT/90z/9
        HdFaR4L2cY97XLngggvKX/3VX41OH8gR2JxCsNRHYVd9tOYfdNeuXeXf/u3flmTccMMN5alPferoP/KU
        U04pv/d7v1c+/elPj13WMAzDMIyH/8h7/wc+8IEHx9/+7d8+ZLz//e8v73vf+0Zf6+Xrr79+dLm/bB2Z
        /573vKf8zd/8TdmxY8foaGZ+Gare52c+85nvGN3HlGXr8v/5n/9Zdu7cWW677bbvGHv27Cn/9V//9eA6
        6+3/4z/+Y3QgLp8ksHnz5tGpAf1gHTce/ehHl1/6pV8qr371q8sHP/jB8u///u/lzjvvLF/5ylfK/fff
        PzqCe6gcaf22TZs2lZNOOqmsWbPmsEf+U+th9PzW3SMe8YixyxmGYRiGcWSMvN/3422pR+4jI40x66i3
        SeM8/vGPLz/yIz9SfvRHf7Q86UlPGo1c/qEf+qHy2Mc+tjzqUY+auP5jjz12NG/c41ps5ABePoHg4osv
        HkVsIvlrX/vaQoG1Ea3f9tznPrc88pGPHPuPbRiGYRiGMeRIMCYccyAsY1xYThu5baI0B9bytTvSP5Oi
        NLfN/R1qtOb2xx9//OhTCM4777zyp3/6p6PTDXIk91CI1m/Lb8edeOKJo//Mwx3ZAPKflP+s/AfnP3vc
        coZhGIZhHBkj4TePUXvih3/4h8sf//Efl3e9613l2muvLddcc83oI6lax5vf/Oby+te/vrz2ta99cGS9
        Ga973eseMrLcm970pvKHf/iH5Wd+5mdG4doN0nEjjzc99cQnPrG88IUvLG95y1vKP/zDP4yOrn7xi18s
        X/3qV0fnuuY0gUMhWr8tJwznnIt6HsfhDOe0GoZhGIax2Mj5pv1zUvsj/ZAP+s/v3nzzm98cnQ+akV/w
        3rdvX/PYu3dv+da3vjXTyP3cc8895S//8i/LD/zAD3xHoNaRA3Tf8z3fMzqSevXVV5d//Md/HEVqzmHN
        fS4l0brEup8esHHjRp8eAAA87CRaP/KRjzx4IK4/vvu7v3v0k+q3v/3t5V//9V/LXXfdNYrieRKtS6wb
        rRs2bCiXX375kn+nAQAwTznK+6xnPeshoZpf6Mrntr7xjW8sf//3fz/6bNbD+TSAVqJ1iXWjNed15CMf
        RCsA8HCRH+1fdtlloz8gkJ7Jx1g973nPK+94xzvK5z73ubkfUZ1EtC6xbrTmN+a2bNky2H8uAECrT3zi
        E+XJT37y6OO88hmt73znO0eftTo00brE+tF6ySWXiFYA4GEhv/CVTyl4/vOfP/pUgTvuuGNhzvBE6xLr
        RmvO/di2bZvTAwCAh4V8LFX+CED+JGs+WWklEa1LrBut69atE60AAEtAtC6xnPPxohe9aBSt+UUs0QoA
        cPhE6xL7+te/PvqYq9NOO210AvNrXvMa0QoAcJhE6xK77777Rn894n/9r/81+pOuPvIKjmw5JWicSdO7
        brnlloVL32nHjh0Ll1aGu+++ezRgOWT/mbZ/zCp/MnQlO9L2qwe+9JVy8JvfWrj2f2XaUhGtS+zAgQPl
        ve99b3nCE55Q1q5dO/pMs+X84N2VaPv27TO9gQ9hpT4uVpaLLrpo7BtowvLMM89cuPZQmT4tPPNGNem2
        3fXmvq+77rrR5UkyP8vPOhYL4qyvf5/ZjzO6EgT5040rPQx4+Mn+dsYZZxzWtpXtNdtnDcPumGW9eQzj
        9p9J45prrlm45XjLsF99buHrYUtofvnHnzM2OCdNz7Sv/O+XlL3/9KmFKd+5nny9/2P/MLp8KETrHOSv
        RPzUT/3U6JzWm266aWHqypMdN29e3TFOlkvcZX52yryJZswqt8kO2CofFzbuhWHcyPoXC9C8MPTDI7ft
        P++sp+X5ceSrb6B1+8k2k+0p20mm53LejPImU9+EMj3zc3nctpl1TNovMi/bZtQ3zklviLnP3NfNN9/8
        kDflSSOPs657kiyXZbLOqj6vrvp6AIci22LdrsaN7j40aWRbHSfrzu+WTBpZdz8g+7J/Zhvv7j+TRvaV
        rDeXJ8m8Oe9Xc4vWhGaCNCZF6z1b/7Tc94H/M7r89d/eVvb92389uJ6E7De2v3W0jnv/4l2jZQ6FaJ2D
        nTt3lqc//enlhBNOKDfccEM5ePDgwpyVJTtF9wUhO1N9M87lOuoOnsuZlx29u2PW7wq7t+mPrGPc9Dr6
        O2h26txn3uzri8K0UZ/HNPXNP8tXud6P1qxrsRczVp9uuHa/iavbUH0jyuWMTM/8XO5uc1XmTdpm6+2r
        3D77WL6R6x99ybzF3iy7+uvuynOojz+PLc+3Xq+vF/V69pHMr8+x+0YMs8g2lO3scMa4bwjrtjnp/aO+
        F2S7nWaWZaqsd9J+uIz71ZJHayIzP+5PkNbYHBetCdKvPu/3R3GbcE205nrCta4n07OuLDMuemchWufg
        S1/6UrngggtG0ZrPOluppwf0d8jsTLle35AzsgPW6dPUF4M6so7sfLmP7vRcz/T+i0n/jTj3l2VnVV/A
        xqnPJaP/ApH7qC8OGbmcF4w8xlzPixtU2R6y/dTtNttJtrt6Perlun9153UlQjN/nEwft/0nWvvbedad
        N8u6Dyw28vjHHeGtb5bjbjPLqPsNHIpsx3V/mjYW28bqdjzptTvTs/3Psq1mH8z+Mu5xjBu53/57WX08
        45afZTTuV0sSrYnLRGZi85vv+NtRfObypJGYTdRmuVyuI9MSs1mmG6lZrnsKQQvROgf5axL5m735Zaz3
        ve99D7to7Zs0fZJ6BGncG2+9z8VeNLJMy46e9eV++/Imn3njbrPYyG2nvfixOmV7yHacke0jo17PNlgv
        d39CkdHdjvJNWz0q0/0msY66nv70jOw33aMvrdGa0b3Pqs6rukeHpo0ql7u3hxb1gELdDieNul+Mk30j
        8yetJ9Ozr0yan/2ia9q6xo08rqyj+95W51Vz3q+WJFoTmt3Y7B5ljX6Exv49dzwkWDMSvJmeAM76Mi1f
        69HbQyFa5+Sf/umfypYtW0ZfV+rpAf2dv+50fZOmj1NfNPJGmtvkcle9z8zP5YThOFmmJVrzJl+/U+++
        8HSfY74DzuXFRjcIsu5MY/XKNpVvfLJ99WV6ttPum1QkUGuwjrtdlq/Rmu2rvz3X9fan19G9v6yjrqtF
        XVfVvZ71z7L/db9ZzL487htHmEX3tXqabHfjlqv7aeYnNLNM6+ib9TF1Zfncrqr7SizDfrUk0Vp/lD8u
        TmPc9PzIv54ekFGP0EbWVadnJGQPlWidk3vuuWfh0sqVHbv7XWR2tHHnz2TeLDtudqzum3R/543ui0Ai
        MjvkuHAdd9tZdNcf3et5rvVFbdrIi0r9d8jtx/2bsLpku8i20A3DbFfZnvJNUraZ7pHUbGt5g8rI7bo/
        MszlLD8tNLNPZP4sR/nr+nJfeUyzjv62XR9v1H1hMVlPXS7PuTWcoaqvzf3ttD+yneXrJLNuu7Oo74/9
        xzBt9CNzmferufwiVg3YSaPGaGI3odod3fVk1JjN5UMhWudopX/UVXaE7BzZwbJj5A0sb5J1x6ojLyZ1
        5+2O7o6Zy9lZ+2/qeWPsyvVM78rt+tPqkapMn3XkhSG36QZCHmc98jruvsfJbbJcnkueN0S2rxqgdfus
        21au13lZLvtMle27u29k+8r8adGadWWZ7j42TbbXrDf3k6+TRnd+fSMdpy6TxzdtZB1ZDg5X9qW63U0b
        2Sey7U1Sl6uyzWd/mjQyf5LuY5q2b3XnTXt8dZnuPjRuHMZ+NZdo7Rs3PdFaYzQjoduN1pxikKjNKQL5
        5axDJVp58A0vxr1w5M25fhfcHd03vW4oVnVdXble3+gXU3fcaS8WGd3506I0L1BZ57gXie5YbD2sXnlD
        yvabb2YSqtnu63aT6RnZVzKvTo9so9mmMj/bYeZPitYsk30u30T2vwlbTLbdSaGb+8p9z6JGeR7jYqP7
        OgCtsl1m32gd2UbHqe8FUfeh7FN1f+yOuq/l8jS5v+43on3ZrxZbRyzDfjVotE470pqIrbfJsuPWOwvR
        uorlzS0jO2R28ux0/TfIXM9ONsubXV4gui8qs4xJLzxdue/ujzG7so76ArWYvAiMe3Hoj/oiB331jS6y
        Pc3yBpRg7aqhm3nj3uiy3nqEP2+ULW9e3TfsvtzXrNFan2f2zzryPDK60zKybEtYQ1e28XE/yVtsZN/L
        639fnR+Zv9g2P0twLraeWdYRy7Bfrdgjrd3lM92nB9Cs7tzZIesbbN3Zq8zLi0o9ejRNXniy8+bFpIbu
        YiP3OenIUJXlxr04Rab3H/MkeRHovzBk3YmC7rS8qMzyAsTqk221RmTddrKtTBtZLl+7cn1ctNZvEus3
        adm+c72/XNR1d8di+964ef1TYLI/1deDWUfW6xs9DkW2we72nW0++1l9f6oj+1r34EWd3tednnVl/dP0
        7z/qe1l3tO5XGd3ozGMat+9MG4ewXy15tNbPWV1s1GjNpwLkFICcDpDPas31HIH16QEctrpz1/DLjtv/
        zq4e6cnov7lNkp0sO1vWO0nuJ99dZsfuv2D1d/xpLxYZ4+bXsIjcV57HuBeFaSO36f97sHplW+i+idTt
        LNvvtJHl8rUr17ON9afnzbq/n9UjMePU+6gj+3HW0Z+ekcddH0t3dLfxXM/jyjdv/eXq60V/ekYeX+ZB
        q+7+kf0p1+u21h/Z/7JtRp3Wl/2nvvf0ozXbeuZnH8m87j5R59ev3e07jyvvB91p3ZF1ZF396VUuL9N+
        tWTR2iqRW4+e5mv3FIB8zfU6ukddW4nWVazuLNl5x+0YmZ4XiboDd18wFpPlsiN347GqLyTZIbtvmFV/
        x82yuU1/ekbWP26H76633l9/mYz6GCfNy20hsi3kjavK9lG/wVlsZHvqyvX+9Owz2cfGLZvp4/alvjy+
        Sdts1pPHPM24x1XV14txps2Daeprcyy2HXVfkyctm2Xq+1R97e+q7xnZVzKvrqNOH6ce+Z2k+xzGWcb9
        arBoXS6idRWrO0R27P6OkZ0rO2L3jTI7bvdNezFZR5bPd7b5LjQ/2snlaW+sfYnPcW/k1bjH3jfuhavK
        9EmPZdo8Vp/+dlv3j2yb00aWy9euXO++idXlJm1vNWinfdNYlxn3jWDU+5im/7i66uvFONPmwTTd/WOx
        7ai7j4xbNvtAdxvPsott81WWHXff9T1o0u9VRPc5jLOM+5Vo5chVd4jsrDnqmTfghGnkekZXdt7snHWZ
        WeQ2CdXssNnxW24beUzTQnnSC03XtBeuTM/8cabNY3XJdtDfDmfdPrJc/82q+yaWkWW63yCOU6N03H3O
        so66zDTdx9XXfQPNfl1fOzLqj1yhVXf/yPabbTzTxo3Mq8t2t8cq22F3H8i2mdvNIttvf30x7r2wL/cx
        bp+plnG/Eq0cubJz5I04Iy8G2THz3eS0nTQ7XXbQxd4cs8NlHfXFJstnB+zeT3bQaWY5upT7GfdC0zXt
        hSvTMz9yNDgvELmekfue9t01q0P9Zq2/LWS7yzZSt/Fpo7+t1zexrDP737T9qaseSeq+kdU3+sXWUffd
        afI4s5+O2zfzfLv7Wu4v17PO7NPj3pBhMdl+uttOXofra3B/ZH+pr9H9QM3l/jeWWW99z6nb76TRf73P
        vpb1TXov7Oo/h75l3K9EK0em7Bh1Z86Omp0pO8csO2mWqztUlfXleqZnvVlPXlz6wVnDMPOzXJbPDtp9
        8Yn6IjItWCMvXt0dfpzc56Rlcv9ZR+R5dZ9HNwxY3SZth9lmFhvj3qgi21rWu9g23pd1dveXcfvZJP39
        rEXuo+4rsFQmxdw42fazvef1vP/6nNf5zO+bFsHd0f+mNPtKps8iy876HPqWeL8SrRyZsoP1d7Ls8LPu
        PP03zrrjZwdseQHKbbKevAjVF5x8zQtSvT5NXcehyn3nRQ0AHuZEKwAAK55oBQBgxROtAACseKIVAIAV
        T7QCALDiiVYAAFY80QoAwIonWgEAWPFEKwAAK55oBQBgxROtAACseKIVAIAVT7QCALDiiVYAAFa8XQtf
        j1iJ1q8YhmEYhmEYD8tx77fH3m+PT317HMFK+f8BWdv/w9XaeQMAAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="contextMenuStrip2.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>182, 17</value>
  </metadata>
  <metadata name="contextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="contextMenuStrip4.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>512, 17</value>
  </metadata>
  <metadata name="contextMenuStrip3.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>347, 17</value>
  </metadata>
</root>