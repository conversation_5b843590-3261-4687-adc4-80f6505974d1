﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class NRIndoorBtsInfo : BtsInfoBase
    {
        /// <summary>
        /// 由于存在拉远站,使用拉远小区名中的部分作为基站名
        /// </summary>
        public string FileBtsName { get; set; }

        public NRIndoorBtsServiceInfo BtsInfo { get; set; }

        public List<NRAlarmInfo> BtsAlarmList { get; set; }

        public NRIndoorBtsInfo(NRBTS bts, string fileBtsName)
            : base(bts)
        {
            BtsName = bts.Name;
            BtsID = bts.BTSID;
            FileBtsName = fileBtsName;
        }

        public void Init(NRServiceName serviceType)
        {
           //
        }
    }

    public class NRIndoorBtsServiceInfo
    {
        public PicKpiInfo RsrpPicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo SinrPicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo PciPicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo DLPicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo ULPicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo LeakOutPicInfo { get; set; } = new PicKpiInfo();
    }

    public class NRIndoorBtsParameters : BtsParameters
    {
        /// <summary>
        /// 站名
        /// </summary>
        public string BtsName { get; set; }
        /// <summary>
        /// 站号
        /// </summary>
        public int ENodeBID { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 区县
        /// </summary>
        public string Country { get; set; }

        public ParamInfo<double?> BtsLongutide { get; set; } = new ParamInfo<double?>();
        public ParamInfo<double?> BtsLatitude { get; set; } = new ParamInfo<double?>();
        public ParamInfo<int?> CellCount { get; set; } = new ParamInfo<int?>();
        public ParamInfo<int?> TAC { get; set; } = new ParamInfo<int?>();

        public ParamInfo<int?> BBU { get; set; } = new ParamInfo<int?>();
        public ParamInfo<int?> RRU { get; set; } = new ParamInfo<int?>();

        public Dictionary<string, NRIndoorCellParameters> CellDic { get; set; } = new Dictionary<string, NRIndoorCellParameters>();

        public void Caluculate()
        {
            BtsLongutide.JudgeValidLongitude(50);
            BtsLatitude.JudgeValidLatitude(50);
            CellCount.JudgeValid();
            TAC.JudgeValid();
            BBU.JudgeValid();
            RRU.JudgeValid();

            foreach (var cell in CellDic.Values)
            {
                cell.Caluculate();
            }
        }
    }
}
