using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func.IntervalGraph
{
    public partial class AutoSettingParameterForm : Form
    {
        public AutoSettingParameterForm(float min,float max)
        {

            InitializeComponent();
            this.min = min;
            this.max = max;
            for (int i = 0; i < SymbolManager.GetInstance().Count; i++)
            {
                comboBoxRangeCount.Items.Add(i + 1);
            }
            comboBoxRangeCount.SelectedIndex = 3;
        }

        private float min;
        private float max;

        private void labelColorBegin_Click(object sender, EventArgs e)
        {
            ColorDialog dialog = new ColorDialog();
            dialog.Color = labelColorBegin.BackColor;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                labelColorBegin.BackColor = dialog.Color;
            }
        }

        private void labelColorVia_Click(object sender, EventArgs e)
        {
            ColorDialog dialog = new ColorDialog();
            dialog.Color = labelColorVia.BackColor;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                labelColorVia.BackColor = dialog.Color;
            }
        }

        private void labelColorEnd_Click(object sender, EventArgs e)
        {
            ColorDialog dialog = new ColorDialog();
            dialog.Color = labelColorEnd.BackColor;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                labelColorEnd.BackColor = dialog.Color;
            }
        }

        private void checkBoxColoVia_CheckedChanged(object sender, EventArgs e)
        {
            labelColorVia.Enabled = checkBoxColorVia.Checked;
        }
        
        public List<DTParameterRange> RangeValues { get; set; }

        public int RangeCount
        {
            get { return comboBoxRangeCount.SelectedIndex + 1; }
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            List<DTParameterRange> rangeValues = new List<DTParameterRange>(RangeCount);
            for (int i = 0; i < RangeCount; i++)
            {
                rangeValues.Add(new DTParameterRangeColor());
            }
            RangeValues = rangeValues;
            MakeRange();
            makeRangeColor();
            DialogResult = DialogResult.OK;
        }

        protected void MakeRange()
        {
            for (int i = 0; i < RangeCount; i++)
            {
                DTParameterRange rangeValue = RangeValues[i];
                float range = max - min;
                rangeValue.Min = (int)((min + range * i / RangeCount) * 10) / 10.0F;
                rangeValue.Max = (int)((min + range * (i + 1) / RangeCount) * 10) / 10.0F;
            }
        }

        private void makeRangeColor()
        {
            for (int i = 0; i < RangeCount; i++)
            {
                DTParameterRangeColor rangeColor = (DTParameterRangeColor)RangeValues[i];
                float percent = RangeCount == 1 ? 0.5F : (float)i / (RangeCount - 1);
                Color beginColor;
                Color endColor;
                if (checkBoxColorVia.Checked)
                {
                    percent = percent * 2;
                    if (percent < 1)
                    {
                        beginColor = labelColorBegin.BackColor;
                        endColor = labelColorVia.BackColor;
                    }
                    else
                    {
                        percent -= 1;
                        beginColor = labelColorVia.BackColor;
                        endColor = labelColorEnd.BackColor;
                    }
                }
                else
                {
                    beginColor = labelColorBegin.BackColor;
                    endColor = labelColorEnd.BackColor;
                }
                rangeColor.Value = Color.FromArgb(
                    (int)(beginColor.R + (endColor.R - beginColor.R) * percent),
                    (int)(beginColor.G + (endColor.G - beginColor.G) * percent),
                    (int)(beginColor.B + (endColor.B - beginColor.B) * percent)
                    );
            }
        }

    }
}