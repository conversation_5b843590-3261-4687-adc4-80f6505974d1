﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRLowSpeedCauseForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode2 = new DevExpress.XtraGrid.GridLevelNode();
            DevExpress.XtraCharts.Series series3 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel5 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PiePointOptions piePointOptions5 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PiePointOptions piePointOptions6 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView5 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel6 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView6 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.ChartTitle chartTitle3 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.Series series4 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel7 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PiePointOptions piePointOptions7 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PiePointOptions piePointOptions8 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView7 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel8 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView8 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.ChartTitle chartTitle4 = new DevExpress.XtraCharts.ChartTitle();
            this.viewDetail = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridDetail = new DevExpress.XtraGrid.GridControl();
            this.viewMain = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tabCtrl = new DevExpress.XtraTab.XtraTabControl();
            this.pageSummary = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridSummary = new DevExpress.XtraGrid.GridControl();
            this.viewSummary = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.chartMain = new DevExpress.XtraCharts.ChartControl();
            this.chartSub = new DevExpress.XtraCharts.ChartControl();
            this.pageDetail = new DevExpress.XtraTab.XtraTabPage();
            this.ctxSummary = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miSummaryExport = new System.Windows.Forms.ToolStripMenuItem();
            this.ctxDetail = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miDetailExport = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.viewDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewMain)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabCtrl)).BeginInit();
            this.tabCtrl.SuspendLayout();
            this.pageSummary.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridSummary)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewSummary)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartMain)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartSub)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView8)).BeginInit();
            this.pageDetail.SuspendLayout();
            this.ctxSummary.SuspendLayout();
            this.ctxDetail.SuspendLayout();
            this.SuspendLayout();
            // 
            // viewDetail
            // 
            this.viewDetail.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn5,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn7,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn6,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn12});
            this.viewDetail.GridControl = this.gridDetail;
            this.viewDetail.Name = "viewDetail";
            this.viewDetail.OptionsBehavior.AutoPopulateColumns = false;
            this.viewDetail.OptionsBehavior.Editable = false;
            this.viewDetail.OptionsDetail.ShowDetailTabs = false;
            this.viewDetail.OptionsView.EnableAppearanceEvenRow = true;
            this.viewDetail.OptionsView.ShowDetailButtons = false;
            this.viewDetail.OptionsView.ShowGroupPanel = false;
            this.viewDetail.DoubleClick += new System.EventHandler(this.viewDetail_DoubleClick);
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "主服小区";
            this.gridColumn5.FieldName = "SCellName";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 0;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "原因类别";
            this.gridColumn10.FieldName = "CauseType";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 1;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "原因场景";
            this.gridColumn11.FieldName = "CauseScene";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 2;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "详细原因";
            this.gridColumn7.FieldName = "CauseDetailName";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 3;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "经度";
            this.gridColumn19.FieldName = "Longitude";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 4;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "纬度";
            this.gridColumn20.FieldName = "Latitude";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 5;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "下载速率(Mbps)";
            this.gridColumn6.FieldName = "Speed";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 6;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "RSRP";
            this.gridColumn8.FieldName = "RSRP";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 7;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "SINR";
            this.gridColumn9.FieldName = "SINR";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 8;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "时间";
            this.gridColumn12.DisplayFormat.FormatString = "yy/MM/dd HH:mm:ss";
            this.gridColumn12.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn12.FieldName = "Time";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 9;
            // 
            // gridDetail
            // 
            this.gridDetail.ContextMenuStrip = this.ctxDetail;
            this.gridDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode2.LevelTemplate = this.viewDetail;
            gridLevelNode2.RelationName = "PointDetails";
            this.gridDetail.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode2});
            this.gridDetail.Location = new System.Drawing.Point(0, 0);
            this.gridDetail.MainView = this.viewMain;
            this.gridDetail.Name = "gridDetail";
            this.gridDetail.ShowOnlyPredefinedDetails = true;
            this.gridDetail.Size = new System.Drawing.Size(1245, 715);
            this.gridDetail.TabIndex = 0;
            this.gridDetail.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.viewMain,
            this.viewDetail});
            // 
            // viewMain
            // 
            this.viewMain.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn14,
            this.gridColumn13,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn4,
            this.gridColumn15,
            this.gridColumn16});
            this.viewMain.GridControl = this.gridDetail;
            this.viewMain.Name = "viewMain";
            this.viewMain.OptionsBehavior.AutoPopulateColumns = false;
            this.viewMain.OptionsBehavior.Editable = false;
            this.viewMain.OptionsDetail.ShowDetailTabs = false;
            this.viewMain.OptionsPrint.ExpandAllDetails = true;
            this.viewMain.OptionsPrint.PrintDetails = true;
            this.viewMain.OptionsView.EnableAppearanceEvenRow = true;
            this.viewMain.OptionsView.ShowGroupPanel = false;
            this.viewMain.DoubleClick += new System.EventHandler(this.viewMain_DoubleClick);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "道路";
            this.gridColumn1.FieldName = "RoadName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 101;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "平均速率(Mbps)";
            this.gridColumn2.FieldName = "AvgSpeed";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 101;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "采样点个数";
            this.gridColumn3.FieldName = "SampleCount";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            this.gridColumn3.Width = 76;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "持续时间(秒)";
            this.gridColumn14.FieldName = "Second";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 3;
            this.gridColumn14.Width = 88;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "持续距离(米)";
            this.gridColumn13.FieldName = "Distance";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 4;
            this.gridColumn13.Width = 85;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "主要原因";
            this.gridColumn17.FieldName = "MainCause";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 5;
            this.gridColumn17.Width = 110;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "主要原因占比(%)";
            this.gridColumn18.FieldName = "MainCausePer";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 6;
            this.gridColumn18.Width = 110;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "文件";
            this.gridColumn4.FieldName = "FileName";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 9;
            this.gridColumn4.Width = 127;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "中心经度";
            this.gridColumn15.FieldName = "MidLng";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 7;
            this.gridColumn15.Width = 110;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "中心纬度";
            this.gridColumn16.FieldName = "MidLat";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 8;
            this.gridColumn16.Width = 110;
            // 
            // tabCtrl
            // 
            this.tabCtrl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabCtrl.Location = new System.Drawing.Point(0, 0);
            this.tabCtrl.Name = "tabCtrl";
            this.tabCtrl.SelectedTabPage = this.pageSummary;
            this.tabCtrl.Size = new System.Drawing.Size(1252, 745);
            this.tabCtrl.TabIndex = 2;
            this.tabCtrl.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.pageSummary,
            this.pageDetail});
            // 
            // pageSummary
            // 
            this.pageSummary.Controls.Add(this.splitContainerControl1);
            this.pageSummary.Name = "pageSummary";
            this.pageSummary.Size = new System.Drawing.Size(1245, 715);
            this.pageSummary.Text = "汇总信息";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.gridSummary);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.splitContainerControl2);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1245, 715);
            this.splitContainerControl1.SplitterPosition = 347;
            this.splitContainerControl1.TabIndex = 1;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // gridSummary
            // 
            this.gridSummary.ContextMenuStrip = this.ctxSummary;
            this.gridSummary.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridSummary.Location = new System.Drawing.Point(0, 0);
            this.gridSummary.MainView = this.viewSummary;
            this.gridSummary.Name = "gridSummary";
            this.gridSummary.Size = new System.Drawing.Size(1245, 347);
            this.gridSummary.TabIndex = 0;
            this.gridSummary.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.viewSummary});
            // 
            // viewSummary
            // 
            this.viewSummary.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.viewSummary.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.viewSummary.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.viewSummary.ColumnPanelRowHeight = 30;
            this.viewSummary.GridControl = this.gridSummary;
            this.viewSummary.Name = "viewSummary";
            this.viewSummary.OptionsBehavior.Editable = false;
            this.viewSummary.OptionsView.AllowCellMerge = true;
            this.viewSummary.OptionsView.ColumnAutoWidth = false;
            this.viewSummary.OptionsView.EnableAppearanceEvenRow = true;
            this.viewSummary.OptionsView.ShowGroupPanel = false;
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.chartMain);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.chartSub);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(1245, 362);
            this.splitContainerControl2.SplitterPosition = 621;
            this.splitContainerControl2.TabIndex = 1;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // chartMain
            // 
            this.chartMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartMain.EmptyChartText.Text = "无数据";
            this.chartMain.Location = new System.Drawing.Point(0, 0);
            this.chartMain.Name = "chartMain";
            this.chartMain.PaletteName = "Metro";
            this.chartMain.RuntimeSelection = true;
            this.chartMain.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            pieSeriesLabel5.Border.Visible = false;
            pieSeriesLabel5.LineVisible = true;
            series3.Label = pieSeriesLabel5;
            piePointOptions5.PercentOptions.PercentageAccuracy = 4;
            piePointOptions5.PointView = DevExpress.XtraCharts.PointView.Argument;
            piePointOptions5.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            piePointOptions5.ValueNumericOptions.Precision = 4;
            series3.LegendPointOptions = piePointOptions5;
            series3.Name = "Series 1";
            piePointOptions6.PercentOptions.PercentageAccuracy = 4;
            piePointOptions6.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series3.PointOptions = piePointOptions6;
            series3.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            series3.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            series3.SynchronizePointOptions = false;
            pieSeriesView5.Border.Visible = false;
            pieSeriesView5.ExplodedDistancePercentage = 2D;
            pieSeriesView5.FillStyle.FillMode = DevExpress.XtraCharts.FillMode.Solid;
            pieSeriesView5.Rotation = 1;
            pieSeriesView5.RuntimeExploding = false;
            series3.View = pieSeriesView5;
            this.chartMain.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series3};
            pieSeriesLabel6.LineVisible = true;
            this.chartMain.SeriesTemplate.Label = pieSeriesLabel6;
            pieSeriesView6.RuntimeExploding = false;
            this.chartMain.SeriesTemplate.View = pieSeriesView6;
            this.chartMain.Size = new System.Drawing.Size(621, 362);
            this.chartMain.SmallChartText.Text = "请拉伸图表大小以便显示";
            this.chartMain.TabIndex = 0;
            chartTitle3.Alignment = System.Drawing.StringAlignment.Near;
            chartTitle3.Font = new System.Drawing.Font("宋体", 16F);
            chartTitle3.Text = "类别占比";
            this.chartMain.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle3});
            // 
            // chartSub
            // 
            this.chartSub.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartSub.Location = new System.Drawing.Point(0, 0);
            this.chartSub.Name = "chartSub";
            this.chartSub.PaletteName = "Metro";
            this.chartSub.RuntimeSelection = true;
            this.chartSub.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            pieSeriesLabel7.Border.Visible = false;
            pieSeriesLabel7.LineVisible = true;
            pieSeriesLabel7.ResolveOverlappingMode = DevExpress.XtraCharts.ResolveOverlappingMode.Default;
            series4.Label = pieSeriesLabel7;
            piePointOptions7.PercentOptions.PercentageAccuracy = 4;
            piePointOptions7.PointView = DevExpress.XtraCharts.PointView.Argument;
            piePointOptions7.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            piePointOptions7.ValueNumericOptions.Precision = 4;
            series4.LegendPointOptions = piePointOptions7;
            series4.Name = "Series 1";
            piePointOptions8.PercentOptions.PercentageAccuracy = 4;
            piePointOptions8.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series4.PointOptions = piePointOptions8;
            series4.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            series4.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            series4.SynchronizePointOptions = false;
            pieSeriesView7.ExplodedDistancePercentage = 2D;
            pieSeriesView7.RuntimeExploding = false;
            series4.View = pieSeriesView7;
            this.chartSub.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series4};
            pieSeriesLabel8.LineVisible = true;
            this.chartSub.SeriesTemplate.Label = pieSeriesLabel8;
            pieSeriesView8.RuntimeExploding = false;
            this.chartSub.SeriesTemplate.View = pieSeriesView8;
            this.chartSub.Size = new System.Drawing.Size(618, 362);
            this.chartSub.SmallChartText.Text = "请拉伸图表大小以便显示";
            this.chartSub.TabIndex = 1;
            chartTitle4.Alignment = System.Drawing.StringAlignment.Near;
            chartTitle4.Font = new System.Drawing.Font("宋体", 16F);
            chartTitle4.Text = "场景占比";
            this.chartSub.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle4});
            // 
            // pageDetail
            // 
            this.pageDetail.Controls.Add(this.gridDetail);
            this.pageDetail.Name = "pageDetail";
            this.pageDetail.Size = new System.Drawing.Size(1245, 715);
            this.pageDetail.Text = "详细信息";
            // 
            // ctxSummary
            // 
            this.ctxSummary.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miSummaryExport});
            this.ctxSummary.Name = "ctxSummary";
            this.ctxSummary.Size = new System.Drawing.Size(181, 48);
            // 
            // miSummaryExport
            // 
            this.miSummaryExport.Name = "miSummaryExport";
            this.miSummaryExport.Size = new System.Drawing.Size(180, 22);
            this.miSummaryExport.Text = "导出Excel...";
            this.miSummaryExport.Click += new System.EventHandler(this.miSummaryExport_Click);
            // 
            // ctxDetail
            // 
            this.ctxDetail.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miDetailExport});
            this.ctxDetail.Name = "ctxSummary";
            this.ctxDetail.Size = new System.Drawing.Size(139, 26);
            // 
            // miDetailExport
            // 
            this.miDetailExport.Name = "miDetailExport";
            this.miDetailExport.Size = new System.Drawing.Size(180, 22);
            this.miDetailExport.Text = "导出Excel...";
            this.miDetailExport.Click += new System.EventHandler(this.miDetailExport_Click);
            // 
            // NRLowSpeedCauseForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1252, 745);
            this.Controls.Add(this.tabCtrl);
            this.Name = "NRLowSpeedCauseForm";
            this.Text = "低速率原因分析列表";
            ((System.ComponentModel.ISupportInitialize)(this.viewDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewMain)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabCtrl)).EndInit();
            this.tabCtrl.ResumeLayout(false);
            this.pageSummary.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridSummary)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewSummary)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartMain)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartSub)).EndInit();
            this.pageDetail.ResumeLayout(false);
            this.ctxSummary.ResumeLayout(false);
            this.ctxDetail.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl tabCtrl;
        private DevExpress.XtraTab.XtraTabPage pageSummary;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraGrid.GridControl gridSummary;
        private DevExpress.XtraGrid.Views.Grid.GridView viewSummary;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraCharts.ChartControl chartMain;
        private DevExpress.XtraCharts.ChartControl chartSub;
        private DevExpress.XtraTab.XtraTabPage pageDetail;
        private DevExpress.XtraGrid.GridControl gridDetail;
        private DevExpress.XtraGrid.Views.Grid.GridView viewDetail;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Views.Grid.GridView viewMain;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private System.Windows.Forms.ContextMenuStrip ctxDetail;
        private System.Windows.Forms.ToolStripMenuItem miDetailExport;
        private System.Windows.Forms.ContextMenuStrip ctxSummary;
        private System.Windows.Forms.ToolStripMenuItem miSummaryExport;
    }
}