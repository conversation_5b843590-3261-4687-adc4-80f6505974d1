﻿namespace MasterCom.RAMS.ZTFunc.ZTCluster
{
    partial class XtraClusterForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.comboBox1 = new System.Windows.Forms.ComboBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.panel3 = new System.Windows.Forms.Panel();
            this.panel2 = new System.Windows.Forms.Panel();
            this.label1 = new System.Windows.Forms.Label();
            this.panel1 = new System.Windows.Forms.Panel();
            this.ckb1800 = new System.Windows.Forms.CheckBox();
            this.ckb900 = new System.Windows.Forms.CheckBox();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlShow = new DevExpress.XtraGrid.GridControl();
            this.gridViewShow = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.gridControlAna = new DevExpress.XtraGrid.GridControl();
            this.gridViewAna = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView8 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridControlInfo = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemShield = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewInfo = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlInter = new DevExpress.XtraGrid.GridControl();
            this.gridViewInter = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.tableLayoutPanel1.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlShow)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewShow)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            this.tableLayoutPanel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlAna)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewAna)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlInfo)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewInfo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            this.xtraTabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlInter)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewInter)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            this.SuspendLayout();
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 1;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel1.Controls.Add(this.groupBox1, 0, 1);
            this.tableLayoutPanel1.Controls.Add(this.xtraTabControl1, 0, 0);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 2;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 87.36264F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 12.63736F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(914, 546);
            this.tableLayoutPanel1.TabIndex = 2;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.comboBox1);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.panel3);
            this.groupBox1.Controls.Add(this.panel2);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.panel1);
            this.groupBox1.Controls.Add(this.ckb1800);
            this.groupBox1.Controls.Add(this.ckb900);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Location = new System.Drawing.Point(3, 480);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(908, 63);
            this.groupBox1.TabIndex = 2;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "显示选项";
            // 
            // comboBox1
            // 
            this.comboBox1.DropDownWidth = 200;
            this.comboBox1.FormattingEnabled = true;
            this.comboBox1.Items.AddRange(new object[] {
            "无"});
            this.comboBox1.Location = new System.Drawing.Point(745, 38);
            this.comboBox1.Name = "comboBox1";
            this.comboBox1.Size = new System.Drawing.Size(154, 22);
            this.comboBox1.TabIndex = 7;
            // 
            // label4
            // 
            this.label4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(668, 43);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(71, 14);
            this.label4.TabIndex = 6;
            this.label4.Text = "已屏蔽小区:";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(528, 29);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(35, 14);
            this.label3.TabIndex = 5;
            this.label3.Text = "Top3";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(452, 29);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(35, 14);
            this.label2.TabIndex = 5;
            this.label2.Text = "Top2";
            // 
            // panel3
            // 
            this.panel3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(177)))), ((int)(((byte)(61)))));
            this.panel3.Location = new System.Drawing.Point(499, 26);
            this.panel3.Name = "panel3";
            this.panel3.Size = new System.Drawing.Size(23, 21);
            this.panel3.TabIndex = 4;
            // 
            // panel2
            // 
            this.panel2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(61)))), ((int)(((byte)(255)))));
            this.panel2.Location = new System.Drawing.Point(423, 26);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(23, 21);
            this.panel2.TabIndex = 4;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(372, 29);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(35, 14);
            this.label1.TabIndex = 3;
            this.label1.Text = "Top1";
            // 
            // panel1
            // 
            this.panel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(26)))), ((int)(((byte)(26)))));
            this.panel1.Location = new System.Drawing.Point(343, 26);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(23, 21);
            this.panel1.TabIndex = 2;
            // 
            // ckb1800
            // 
            this.ckb1800.AutoSize = true;
            this.ckb1800.Checked = true;
            this.ckb1800.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ckb1800.Location = new System.Drawing.Point(158, 26);
            this.ckb1800.Name = "ckb1800";
            this.ckb1800.Size = new System.Drawing.Size(78, 18);
            this.ckb1800.TabIndex = 1;
            this.ckb1800.Text = "1800频段";
            this.ckb1800.UseVisualStyleBackColor = true;
            this.ckb1800.CheckedChanged += new System.EventHandler(this.CheckBoxChanged);
            // 
            // ckb900
            // 
            this.ckb900.AutoSize = true;
            this.ckb900.Checked = true;
            this.ckb900.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ckb900.Location = new System.Drawing.Point(65, 26);
            this.ckb900.Name = "ckb900";
            this.ckb900.Size = new System.Drawing.Size(71, 18);
            this.ckb900.TabIndex = 0;
            this.ckb900.Text = "900频段";
            this.ckb900.UseVisualStyleBackColor = true;
            this.ckb900.CheckedChanged += new System.EventHandler(this.CheckBoxChanged);
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(3, 3);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(908, 471);
            this.xtraTabControl1.TabIndex = 3;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.gridControlShow);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(900, 441);
            this.xtraTabPage1.Text = "全部簇";
            // 
            // gridControlShow
            // 
            this.gridControlShow.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlShow.Location = new System.Drawing.Point(0, 0);
            this.gridControlShow.MainView = this.gridViewShow;
            this.gridControlShow.Name = "gridControlShow";
            this.gridControlShow.Size = new System.Drawing.Size(900, 441);
            this.gridControlShow.TabIndex = 2;
            this.gridControlShow.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewShow,
            this.gridView2});
            this.gridControlShow.DoubleClick += new System.EventHandler(this.gridControlShow_DoubleClick);
            this.gridControlShow.Click += new System.EventHandler(this.gridControlShow_Click);
            // 
            // gridViewShow
            // 
            this.gridViewShow.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn45,
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6});
            this.gridViewShow.GridControl = this.gridControlShow;
            this.gridViewShow.GroupCount = 1;
            this.gridViewShow.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "GroupId", null, "")});
            this.gridViewShow.Name = "gridViewShow";
            this.gridViewShow.OptionsBehavior.Editable = false;
            this.gridViewShow.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewShow.OptionsView.ShowGroupPanel = false;
            this.gridViewShow.SortInfo.AddRange(new DevExpress.XtraGrid.Columns.GridColumnSortInfo[] {
            new DevExpress.XtraGrid.Columns.GridColumnSortInfo(this.gridColumn45, DevExpress.Data.ColumnSortOrder.Ascending)});
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "簇组";
            this.gridColumn45.FieldName = "GroupId";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 0;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "簇ID";
            this.gridColumn1.FieldName = "Iclusterid";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "簇内小区数";
            this.gridColumn2.FieldName = "CellNum";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "载波总数";
            this.gridColumn3.FieldName = "Tchnum";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "载波均值";
            this.gridColumn4.FieldName = "Avgtchnum";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "均衡度";
            this.gridColumn5.FieldName = "Junhengdu";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "gridColumn6";
            this.gridColumn6.FieldName = "Strcells";
            this.gridColumn6.Name = "gridColumn6";
            // 
            // gridView2
            // 
            this.gridView2.GridControl = this.gridControlShow;
            this.gridView2.Name = "gridView2";
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.tableLayoutPanel2);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(901, 441);
            this.xtraTabPage2.Text = "所选簇";
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.ColumnCount = 1;
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel2.Controls.Add(this.gridControlAna, 0, 1);
            this.tableLayoutPanel2.Controls.Add(this.gridControlInfo, 0, 0);
            this.tableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel2.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.RowCount = 2;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(901, 441);
            this.tableLayoutPanel2.TabIndex = 4;
            // 
            // gridControlAna
            // 
            this.gridControlAna.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlAna.Location = new System.Drawing.Point(3, 223);
            this.gridControlAna.MainView = this.gridViewAna;
            this.gridControlAna.Name = "gridControlAna";
            this.gridControlAna.Size = new System.Drawing.Size(895, 215);
            this.gridControlAna.TabIndex = 4;
            this.gridControlAna.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewAna,
            this.gridView8});
            // 
            // gridViewAna
            // 
            this.gridViewAna.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn36,
            this.gridColumn35,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn34});
            this.gridViewAna.GridControl = this.gridControlAna;
            this.gridViewAna.Name = "gridViewAna";
            this.gridViewAna.OptionsBehavior.Editable = false;
            this.gridViewAna.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewAna.OptionsView.ColumnAutoWidth = false;
            this.gridViewAna.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "频带ID";
            this.gridColumn36.FieldName = "Ibandtype";
            this.gridColumn36.Name = "gridColumn36";
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "轮次";
            this.gridColumn35.FieldName = "Iroundid";
            this.gridColumn35.Name = "gridColumn35";
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "小区名";
            this.gridColumn25.FieldName = "Strcellname";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 0;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "小区标识";
            this.gridColumn26.FieldName = "Strcellcode";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 1;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "BCCH";
            this.gridColumn27.FieldName = "Ibcch";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 2;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "BSIC";
            this.gridColumn28.FieldName = "Ibsic";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 3;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "载波数";
            this.gridColumn29.FieldName = "Itchnum";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 4;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "无线利用率";
            this.gridColumn30.FieldName = "Radiorate";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 5;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "同邻频小区数";
            this.gridColumn31.FieldName = "Inum";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 6;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "干扰类型";
            this.gridColumn32.FieldName = "Strtype";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 7;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "小区集";
            this.gridColumn33.FieldName = "Cells";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 8;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "建议方案";
            this.gridColumn34.FieldName = "Strldea";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 9;
            // 
            // gridView8
            // 
            this.gridView8.GridControl = this.gridControlAna;
            this.gridView8.Name = "gridView8";
            // 
            // gridControlInfo
            // 
            this.gridControlInfo.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControlInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlInfo.Location = new System.Drawing.Point(3, 3);
            this.gridControlInfo.MainView = this.gridViewInfo;
            this.gridControlInfo.Name = "gridControlInfo";
            this.gridControlInfo.Size = new System.Drawing.Size(895, 214);
            this.gridControlInfo.TabIndex = 3;
            this.gridControlInfo.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewInfo,
            this.gridView4});
            this.gridControlInfo.DataSourceChanged += new System.EventHandler(this.gridControlInfo_DataSourceChanged);
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemShield});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(123, 26);
            // 
            // ToolStripMenuItemShield
            // 
            this.ToolStripMenuItemShield.Name = "ToolStripMenuItemShield";
            this.ToolStripMenuItemShield.Size = new System.Drawing.Size(122, 22);
            this.ToolStripMenuItemShield.Text = "屏蔽小区";
            this.ToolStripMenuItemShield.Click += new System.EventHandler(this.ToolStripMenuItemShield_Click);
            // 
            // gridViewInfo
            // 
            this.gridViewInfo.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24});
            this.gridViewInfo.GridControl = this.gridControlInfo;
            this.gridViewInfo.Name = "gridViewInfo";
            this.gridViewInfo.OptionsBehavior.Editable = false;
            this.gridViewInfo.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewInfo.OptionsView.ColumnAutoWidth = false;
            this.gridViewInfo.OptionsView.ShowGroupPanel = false;
            this.gridViewInfo.RowCellStyle += new DevExpress.XtraGrid.Views.Grid.RowCellStyleEventHandler(this.gridViewInfo_RowCellStyle);
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "频带ID";
            this.gridColumn7.FieldName = "Iroundid";
            this.gridColumn7.Name = "gridColumn7";
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "簇ID";
            this.gridColumn8.FieldName = "Iclusterid";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 0;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "小区序号";
            this.gridColumn9.FieldName = "Icellid";
            this.gridColumn9.Name = "gridColumn9";
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "小区名";
            this.gridColumn10.FieldName = "Strcellname";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 1;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "小区标识";
            this.gridColumn11.FieldName = "Strcellcode";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 2;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "BCCH";
            this.gridColumn12.FieldName = "Ibcch";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 3;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "BSIC";
            this.gridColumn19.FieldName = "Ibsic";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 4;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "载波数";
            this.gridColumn20.FieldName = "ItchNum";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 5;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "经度";
            this.gridColumn21.FieldName = "Flongitude";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 6;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "纬度";
            this.gridColumn22.FieldName = "Flatitude";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 7;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "方位角";
            this.gridColumn23.FieldName = "Idir";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 8;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "频带类型";
            this.gridColumn24.FieldName = "Strbandtype";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 9;
            // 
            // gridView4
            // 
            this.gridView4.GridControl = this.gridControlInfo;
            this.gridView4.Name = "gridView4";
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.gridControlInter);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(901, 441);
            this.xtraTabPage3.Text = "同邻频小区";
            // 
            // gridControlInter
            // 
            this.gridControlInter.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlInter.Location = new System.Drawing.Point(0, 0);
            this.gridControlInter.MainView = this.gridViewInter;
            this.gridControlInter.Name = "gridControlInter";
            this.gridControlInter.Size = new System.Drawing.Size(901, 441);
            this.gridControlInter.TabIndex = 3;
            this.gridControlInter.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewInter,
            this.gridView6});
            // 
            // gridViewInter
            // 
            this.gridViewInter.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn37,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18});
            this.gridViewInter.GridControl = this.gridControlInter;
            this.gridViewInter.Name = "gridViewInter";
            this.gridViewInter.OptionsBehavior.Editable = false;
            this.gridViewInter.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewInter.OptionsView.ColumnAutoWidth = false;
            this.gridViewInter.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "轮次";
            this.gridColumn37.FieldName = "Iroundid";
            this.gridColumn37.Name = "gridColumn37";
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "小区名";
            this.gridColumn13.FieldName = "Strcell";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 0;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "BCCH";
            this.gridColumn14.FieldName = "Ibcch";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 1;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "BSIC";
            this.gridColumn15.FieldName = "Ibsic";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 2;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "干扰类型";
            this.gridColumn16.FieldName = "Strtype";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 3;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "干扰小区集";
            this.gridColumn17.FieldName = "Cells";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 4;
            this.gridColumn17.Width = 431;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "所属簇信息";
            this.gridColumn18.FieldName = "Iscluster";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 5;
            // 
            // gridView6
            // 
            this.gridView6.GridControl = this.gridControlInter;
            this.gridView6.Name = "gridView6";
            // 
            // XtraClusterForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(914, 546);
            this.Controls.Add(this.tableLayoutPanel1);
            this.Name = "XtraClusterForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "簇优化分析(MR)";
            this.Deactivate += new System.EventHandler(this.XtraClusterForm_Deactivate);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlShow)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewShow)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            this.tableLayoutPanel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlAna)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewAna)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlInfo)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewInfo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            this.xtraTabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlInter)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewInter)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox ckb1800;
        private System.Windows.Forms.CheckBox ckb900;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraGrid.GridControl gridControlShow;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewShow;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private DevExpress.XtraGrid.GridControl gridControlInfo;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewInfo;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.GridControl gridControlInter;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewInter;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel2;
        private DevExpress.XtraGrid.GridControl gridControlAna;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewAna;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemShield;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Panel panel3;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.ComboBox comboBox1;
    }
}