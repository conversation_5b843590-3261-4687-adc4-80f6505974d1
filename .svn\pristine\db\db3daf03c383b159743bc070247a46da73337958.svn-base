﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;

using MasterCom.MControls;

namespace MasterCom.RAMS.Func
{
    public class ScanGridAnaCoverageStater
    {
        private ScanGridAnaResult anaResult { get; set; }
        private ScanGridAnaResult cmpResult { get; set; }
        private List<ColorRange> colorRanges { get; set; }
        private readonly ScanGridAnaCoverageSingleStater anaStater;
        private readonly ScanGridAnaCoverageSingleStater cmpStater;

        public ScanGridAnaCoverageStater(ScanGridAnaResult anaResult, ScanGridAnaResult cmpResult, List<ColorRange> colorRanges)
        {
            this.anaResult = anaResult;
            this.cmpResult = cmpResult;
            this.colorRanges = colorRanges;

            anaStater = cmpStater = null;
            anaStater = new ScanGridAnaCoverageSingleStater(anaResult, colorRanges);
            if (cmpResult != null)
            {
                cmpStater = new ScanGridAnaCoverageSingleStater(cmpResult, colorRanges);
            }
        }

        public void Stat()
        {
            anaStater.Stat();
            if (cmpStater != null)
            {
                cmpStater.Stat();
            }
        }

        public List<DataTable> GetResult(ScanGridAnaGridType netType, int covType)
        {
            List<DataTable> retList = anaStater.GetResult(netType, covType);
            if (cmpStater != null)
            {
                retList.AddRange(cmpStater.GetResult(netType, covType));
            }
            return retList;
        }
    }

    public class ScanGridAnaCoverageSingleStater
    {
        private readonly ScanGridAnaResult result;
        private readonly List<ColorRange> colorRanges;
        private readonly DataTable[][][] netCovDt;
        private List<RegionCoverageInfo> regionInfoList;

        public ScanGridAnaCoverageSingleStater(ScanGridAnaResult result, List<ColorRange> colorRanges)
        {
            this.result = result;
            this.colorRanges = colorRanges;

            netCovDt = new DataTable[4][][];
            for (int i = 0; i < 4; ++i)
            {
                netCovDt[i] = new DataTable[3][];
                for (int j = 0; j < 3; ++j)
                {
                    netCovDt[i][j] = new DataTable[4];
                    for (int k = 0; k < 4; ++k)
                    {
                        netCovDt[i][j][k] = new DataTable();
                    }
                }
            }
        }

        public void Stat()
        {
            regionInfoList = new List<RegionCoverageInfo>();
            foreach (ScanGridAnaRegionInfo region in result.RegionList)
            {
                RegionCoverageInfo rcInfo = new RegionCoverageInfo(region.RegionName);
                rcInfo.Stat(region, colorRanges);
                regionInfoList.Add(rcInfo);
            }

            for (int i = 0; i < 4; ++i)
            {
                for (int j = 0; j < 3; ++j)
                {
                    PrepareDataTable(i, j);
                }
            }
        }

        public List<DataTable> GetResult(ScanGridAnaGridType netType, int covType)
        {
            List<DataTable> retList = new List<DataTable>();
            foreach (DataTable dt in netCovDt[(int)netType][covType])
            {
                retList.Add(dt);
            }
            return retList;
        }

        private void PrepareDataTable(int netType, int covType)
        {
            DataTable[] dts = netCovDt[netType][covType];

            PrepareColumnForChartControlCount(dts[1]);
            PrepareColumnForChartControlRate(dts[3]);
            foreach (RegionCoverageInfo rcInfo in regionInfoList)
            {
                dts[1].Rows.Add(rcInfo.GetObjectRow(netType, covType, 0));
                dts[3].Rows.Add(rcInfo.GetObjectRow(netType, covType, 1));            
            }

            dts[0] = SummaryCount(dts[1]);
            dts[2] = SummaryRate(dts[0], dts[3]);

            dts[1] = dts[0].Copy();
            dts[1].Columns.Remove("合计");
            dts[3] = dts[2].Copy();
            dts[3].Columns.Remove("合计");
        }

        private void PrepareColumnForChartControlCount(DataTable dt)
        {
            dt.Columns.Add("网格ID", typeof(string));
            foreach (ColorRange range in colorRanges)
            {
                dt.Columns.Add(string.Format("[{0},{1})", range.minValue, range.maxValue), typeof(int));
            }
            dt.Columns.Add("无效栅格", typeof(int));
        }

        private void PrepareColumnForChartControlRate(DataTable dt)
        {
            dt.Columns.Add("网格ID", typeof(string));
            foreach (ColorRange range in colorRanges)
            {
                dt.Columns.Add(string.Format("[{0},{1})", range.minValue, range.maxValue), typeof(double));
            }
            dt.Columns.Add("无效栅格", typeof(double));
        }

        private DataTable SummaryCount(DataTable dt)
        {
            object[] newRow = new object[dt.Columns.Count];
            newRow[0] = "汇总(网格内)";
            for (int i = 1; i < dt.Columns.Count; ++i)
            {
                float sum = 0;
                for (int j = 0; j < dt.Rows.Count; ++j)
                {
                    if (dt.Rows[j][0].ToString() == "网格外")
                    {
                        continue;
                    }
                    sum += (int)dt.Rows[j][i];
                }
                newRow[i] = sum;
            }
            DataTable tmpDt = dt.Copy();
            tmpDt.Rows.Add(newRow);

            tmpDt.Columns.Add(new DataColumn("合计", typeof(int)));
            for (int i = 0; i < tmpDt.Rows.Count; ++i)
            {
                int sum = 0;
                DataRow dr = tmpDt.Rows[i];
                dr.BeginEdit();
                for (int j = 1; j < tmpDt.Columns.Count - 1; ++j)
                {
                    sum += (int)dr[j];
                }
                object[] row = new object[tmpDt.Columns.Count];
                dr.ItemArray.CopyTo(row, 0);
                row[tmpDt.Columns.Count - 1] = sum;
                dr.ItemArray = row;
                dr.EndEdit();
            }
            return tmpDt;
        }

        public DataTable SummaryRate(DataTable sumDt, DataTable rateDt)
        {
            DataTable tmpRateDt = rateDt.Copy();

            int sumDtColCnt = sumDt.Columns.Count;
            int sumDtRowCnt = sumDt.Rows.Count;
            float sum = (int)sumDt.Rows[sumDtRowCnt - 1][sumDtColCnt - 1];

            object[] newRow = new object[tmpRateDt.Columns.Count];
            newRow[0] = "汇总(网格内)";
            for (int i = 1; i < sumDtColCnt - 1; ++i)
            {
                newRow[i] = sum == 0 ? 0 : (int)sumDt.Rows[sumDtRowCnt - 1][i] / sum;
            }
            tmpRateDt.Rows.Add(newRow);

            // sum 为 网格内外的综合；用于计算‘合计’列
            for (int i = 0; i < sumDtRowCnt; ++i)
            {
                if (sumDt.Rows[i][0].ToString() == "网格外")
                {
                    sum += (int)sumDt.Rows[i][sumDtColCnt - 1];
                }
            }

            tmpRateDt.Columns.Add(new DataColumn("合计", typeof(float)));
            for (int i = 0; i < sumDtRowCnt; ++i)
            {
                DataRow dr = tmpRateDt.Rows[i];
                dr.BeginEdit();
                object[] row = new object[tmpRateDt.Columns.Count];
                dr.ItemArray.CopyTo(row, 0);
                row[tmpRateDt.Columns.Count - 1] = sum == 0 ? 0 : (int)sumDt.Rows[i][sumDtColCnt - 1] / sum;
                dr.ItemArray = row;
                dr.EndEdit();
            }

            return tmpRateDt;
        }
    }

    /// <summary>
    /// 网格覆盖度信息
    /// </summary>
    public class RegionCoverageInfo
    {
        public string RegionName { get; set; }
        public double[, , ,] netCovStatValue { get; set; }

        public RegionCoverageInfo(string name)
        {
            RegionName = name;
        }

        /// <summary>
        /// 分析网格中的信息
        /// </summary>
        /// <param name="regionInfo">查询网格</param>
        /// <param name="colorRanges">覆盖值分段</param>
        public void Stat(ScanGridAnaRegionInfo regionInfo, List<ColorRange> colorRanges)
        {
            // 900,1800,共站,td四种网络类型
            // 相对,绝对,综合三种覆盖类型,值域加无效栅格一种
            // 个数,占比两种统计类型
            netCovStatValue = new double[4, 3, 2, colorRanges.Count + 1];
            StatGridList(regionInfo, colorRanges);
        }

        /// <summary>
        /// 获取网格的特定信息
        /// </summary>
        /// <param name="netType">网络类别</param>
        /// <param name="covType">覆盖类别</param>
        /// <param name="statType">分析类型</param>
        /// <returns></returns>
        public object[] GetObjectRow(int netType, int covType, int statType)
        {
            List<object> objList = new List<object>();
            objList.Add(RegionName);
            for (int i = 0; i < netCovStatValue.GetLength(3); ++i)
            {
                objList.Add(netCovStatValue[netType, covType, statType, i]);
            }
            return objList.ToArray();
        }

        /// <summary>
        /// 分析网格中的覆盖信息
        /// </summary>
        /// <param name="regionInfo"></param>
        /// <param name="colorRanges"></param>
        /// <param name="maxRange"></param>
        private void StatGridList(ScanGridAnaRegionInfo regionInfo, List<ColorRange> colorRanges)
        {
            // 覆盖值无分段
            int rangeCnt = colorRanges.Count;
            if (rangeCnt == 0)
            {
                return;
            }
            double maxRange = colorRanges[colorRanges.Count - 1].maxValue;

            // 四种网络类型中出现的栅格个数
            int[] netTypeGridCount = new int[4];

            // 遍历网格中的所有栅格进行个数统计
            foreach (ScanGridAnaGridInfo grid in regionInfo.GridList)
            {
                int netType = (int)grid.GridType;
                ++netTypeGridCount[netType];

                // 无效栅格
                if (!grid.IsValidGrid)
                {
                    ++netCovStatValue[netType, 0, 0, rangeCnt];
                    ++netCovStatValue[netType, 1, 0, rangeCnt];
                    ++netCovStatValue[netType, 2, 0, rangeCnt];
                }
                else
                {
                    addNetCovStatValue(colorRanges, rangeCnt, maxRange, grid, netType);
                }
            }

            calculateProportion(rangeCnt, netTypeGridCount);
        }

        private void addNetCovStatValue(List<ColorRange> colorRanges, int rangeCnt, double maxRange, ScanGridAnaGridInfo grid, int netType)
        {
            // 覆盖值分段统计
            for (int i = 0; i < rangeCnt; ++i)
            {
                ColorRange curRange = colorRanges[i];
                if (grid.RelLevel >= curRange.minValue && grid.RelLevel < curRange.maxValue)
                {
                    ++netCovStatValue[netType, 0, 0, i];
                }
                if (grid.AbsLevel >= curRange.minValue && grid.AbsLevel < curRange.maxValue)
                {
                    ++netCovStatValue[netType, 1, 0, i];
                }
                if (grid.RelAndAbsLevel >= curRange.minValue && grid.RelAndAbsLevel < curRange.maxValue)
                {
                    ++netCovStatValue[netType, 2, 0, i];
                }
            }

            // 大于等于无穷值，默认为加入到最后一个分段值
            if (grid.RelLevel >= maxRange)
            {
                ++netCovStatValue[netType, 0, 0, rangeCnt - 1];
            }
            if (grid.AbsLevel >= maxRange)
            {
                ++netCovStatValue[netType, 1, 0, rangeCnt - 1];
            }
            if (grid.RelAndAbsLevel >= maxRange)
            {
                ++netCovStatValue[netType, 2, 0, rangeCnt - 1];
            }
        }

        private void calculateProportion(int rangeCnt, int[] netTypeGridCount)
        {
            // 占比统计
            for (int i = 0; i < 4; ++i)
            {
                for (int j = 0; j < 3; ++j)
                {
                    for (int k = 0; k < rangeCnt + 1; ++k)
                    {
                        netCovStatValue[i, j, 1, k] = netTypeGridCount[i] == 0 ? 0 : netCovStatValue[i, j, 0, k] / netTypeGridCount[i];
                    }
                }
            }
        }
    }
}
