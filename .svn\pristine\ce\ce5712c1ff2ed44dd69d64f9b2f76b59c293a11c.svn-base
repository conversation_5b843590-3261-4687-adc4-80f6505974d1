﻿using System;
using System.Collections.Generic;
using System.Text;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Func
{
    public class ZTDIYQueryLteHighriskCellAna : DIYAnalyseByPeriodBackgroundBase_Sample
    {
        public ZTDIYQueryLteHighriskCellAna(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }
        public override string Name
        {
            get { return "高风险小区_LTE(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(3, 13011, 13332, this.Name);
        }

        #region  全局变量
        protected Dictionary<string, CellCoverLap_LTE> cellLapRetDic = null;
        protected Dictionary<string, LTEWeakCoverCellInfo> lteCellWeakCoverDic = null;
        protected Dictionary<string, List<string>> overCoverCellInterfereDic = null;
        protected Dictionary<string, List<LTEHighriskInterfereCellInfo>> overCoverCellInterfereInfoDic = null;
        protected ZTDIYQuerLteHighirskAnaSetForm ztHighHirskAnaSetForm = null;
        protected HighriskCond highHriskCond = null;
        protected List<LTEHighriskItemInfo> lteHighHrisItemInfoList = null;
        #endregion

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup highriskGroup = new DIYSampleGroup();
            highriskGroup.ThemeName = "TD_LTE_RSRP";

            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("lte_TAC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                highriskGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_ECI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                highriskGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_EARFCN");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                highriskGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_PCI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                highriskGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_RSRP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                highriskGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_NCell_RSRP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                highriskGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_NCell_EARFCN");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                highriskGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_NCell_PCI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                highriskGroup.ColumnsDefSet.Add(pDef);
            }

            return highriskGroup;
        }

        protected override bool getConditionBeforeQuery()
        {
            if (ztHighHirskAnaSetForm == null)
            {
                ztHighHirskAnaSetForm = new ZTDIYQuerLteHighirskAnaSetForm();
            }
            if (ztHighHirskAnaSetForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            highHriskCond = new HighriskCond();
            ztHighHirskAnaSetForm.GetSettingFilterRet(ref highHriskCond);
            cellLapRetDic = new Dictionary<string, CellCoverLap_LTE>();
            overCoverCellInterfereDic = new Dictionary<string, List<string>>();
            lteCellWeakCoverDic = new Dictionary<string, LTEWeakCoverCellInfo>();
            overCoverCellInterfereInfoDic = new Dictionary<string, List<LTEHighriskInterfereCellInfo>>();
            lteHighHrisItemInfoList = new List<LTEHighriskItemInfo>();
            return true;
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                bool isOverCoverCell = false;
                bool inRegion = Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
                if (inRegion && (tp is LTETestPointDetail || tp is LTEUepTestPoint))
                {
                    float? RSCP = getRsrp(tp);
                    LTECell cell = tp.GetMainCell_LTE();
                    if (RSCP == null || cell == null)
                    {
                        return false;
                    }
                    if (RSCP >= highHriskCond.curOverCoverRxlev)//过覆盖算法
                    {
                        isOverCoverCell = judgeOverCoverCell(tp, isOverCoverCell, RSCP, cell);
                        List<string> interfereCell = getOverCoverCellInterfere(tp, (float)RSCP);
                        addOverCoverCellInterfereDic(cell, interfereCell);
                    }
                    addLteCellWeakCoverDic(tp, RSCP, cell);
                }
                return isOverCoverCell;
            }
            catch
            {
                return false;
            }
        }

        private bool judgeOverCoverCell(TestPoint tp, bool isOverCoverCell, float? RSCP, LTECell cell)
        {
            if (cell.Type == LTEBTSType.Outdoor)
            {
                CellCoverLap_LTE covLap = null;
                CellCoverLap_LTE clTmp = null;
                if (!cellLapRetDic.TryGetValue(cell.Name, out clTmp))
                {
                    double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(cell, 0);
                    covLap = new CellCoverLap_LTE(cell, radiusOfCell);
                    covLap.rationalDistance = radiusOfCell * highHriskCond.curOverCoverDisFactor;
                    covLap.nearestBTSs = MasterCom.ES.Data.CfgDataProvider.GetNearestBTSs(cell, 0);
                    covLap.mnger = new DTDataManager(MainModel.GetInstance());
                    cellLapRetDic[cell.Name] = covLap;
                }
                else
                {
                    covLap = clTmp;
                }
                double distanceToCell = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, cell.Longitude, cell.Latitude);
                bool isBadCheck = distanceToCell > covLap.rationalDistance;
                if (isBadCheck)
                {
                    covLap.AddBadSample(tp, distanceToCell, (float)RSCP);
                    isOverCoverCell = true;
                }
                else
                {
                    covLap.goodSampleCount++;
                    isOverCoverCell = false;
                }
            }

            return isOverCoverCell;
        }

        private void addOverCoverCellInterfereDic(LTECell cell, List<string> interfereCell)
        {
            if (interfereCell != null && interfereCell.Count > 0)
            {
                if (!overCoverCellInterfereDic.ContainsKey(cell.Name))
                {
                    overCoverCellInterfereDic.Add(cell.Name, interfereCell);
                }
                else
                {
                    List<string> interfereCellTmp = overCoverCellInterfereDic[cell.Name];
                    foreach (string cellName in interfereCell)
                    {
                        if (!interfereCellTmp.Contains(cellName))
                        {
                            overCoverCellInterfereDic[cell.Name].Add(cellName);
                        }
                    }
                }
            }
        }

        private void addLteCellWeakCoverDic(TestPoint tp, float? RSCP, LTECell cell)
        {
            if (RSCP < highHriskCond.curWeakCoverRxlev)
            {
                if (!lteCellWeakCoverDic.ContainsKey(cell.Name))
                {
                    LTEWeakCoverCellInfo cellWeakCover = new LTEWeakCoverCellInfo(cell, tp, (float)RSCP);
                    lteCellWeakCoverDic[cell.Name] = cellWeakCover;
                }
                else
                {
                    lteCellWeakCoverDic[cell.Name].AddTestPoint(tp, (float)RSCP);
                }
            }
        }

        protected virtual float? getRsrp(TestPoint tp)
        {
            return (float?)tp["lte_RSRP"];
        }

        protected List<string> getOverCoverCellInterfere(TestPoint tp,float curMainRsrp)
        {
            List<string> interfereCellList = new List<string>();
            for (int i = 0; i < 6; i++)
            {
                float? nRsrp = (float?)tp["lte_NCell_RSRP", i];
                int? nEarfcn = (int?)tp["lte_NCell_EARFCN", i];
                short? nPci = (short?)tp["lte_NCell_PCI", i];

                if (nRsrp == null || nEarfcn == null || nPci == null || (curMainRsrp - nRsrp < highHriskCond.curOverCoverInterfereRSRP))
                {
                    continue;
                }
                LTECell nbCell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(tp.DateTime, nEarfcn, (int?)nPci, tp.Longitude, tp.Latitude);
                if (nbCell == null || interfereCellList.Contains(nbCell.Name))
                {
                    continue;
                }
                interfereCellList.Add(nbCell.Name);
            }

            return interfereCellList;
        }

        protected override void getResultAfterQuery()
        {
            FilterOverCoverCell();
            FilterWeakCoverCell();
            ChangeOverAndWeakToHihgHrisk();
            mainNearstCellInterefere();
        }

        protected void FilterOverCoverCell()
        {
            List<string> filterCells = new List<string>();
            foreach (KeyValuePair<string, CellCoverLap_LTE> keyValue in cellLapRetDic)
            {
                CellCoverLap cellLap = keyValue.Value;
                cellLap.GetResult();

                if (!(cellLap.badSampleCount > 0 && cellLap.TotalSampleCount >= highHriskCond.curOverCoverSampleCount
                    && 100.0 * cellLap.BadSamplePercent >= highHriskCond.curOverCoverPercent
                    && cellLap.MeanBadDistance >= highHriskCond.curOverCoverMinDistance
                    && cellLap.MeanBadDistance <= highHriskCond.curOverCoverMaxDistance))
                {
                    filterCells.Add(keyValue.Key);
                }
            }
            foreach (string key in filterCells)
            {
                cellLapRetDic.Remove(key);
            }
            MainModel.DTDataManager.Clear();
        }

        protected void FilterWeakCoverCell()
        {
            foreach (string cellName in lteCellWeakCoverDic.Keys)
            {
                if (lteCellWeakCoverDic[cellName].RxLevMean < highHriskCond.curWeakCoverMeanRSRP
                    && lteCellWeakCoverDic[cellName].TotalTestPointCount > highHriskCond.curWeakCoverSampleCount)
                {
                    lteCellWeakCoverDic[cellName].SetWeakTestPointCount(highHriskCond.curWeakCoverMeanRSRP);
                }
            }
        }

        protected void ChangeOverAndWeakToHihgHrisk()
        {
            foreach (string strCell in cellLapRetDic.Keys)
            {
                if (lteCellWeakCoverDic.ContainsKey(strCell))
                {
                    LTEHighriskItemInfo lteHighHriskInfo = new LTEHighriskItemInfo();
                    lteHighHriskInfo.StrCellName = strCell;
                    lteHighHriskInfo.StrTAC = cellLapRetDic[strCell].lteCell.TAC.ToString();
                    lteHighHriskInfo.StrECI = cellLapRetDic[strCell].lteCell.ECI.ToString();
                    lteHighHriskInfo.IOverCoverSampleCount = cellLapRetDic[strCell].badSampleCount;
                    lteHighHriskInfo.IWeakCoverSampleCount = lteCellWeakCoverDic[strCell].WeakTestPointCount;
                    if (cellLapRetDic[strCell].TotalSampleCount <= 0 || lteCellWeakCoverDic[strCell].TotalTestPointCount <= 0)
                    {
                        continue;
                    }
                    lteHighHriskInfo.ISampleCount = cellLapRetDic[strCell].TotalSampleCount >= lteCellWeakCoverDic[strCell].TotalTestPointCount ?
                        cellLapRetDic[strCell].TotalSampleCount : lteCellWeakCoverDic[strCell].TotalTestPointCount;
                    if (100.0 * lteHighHriskInfo.IOverCoverSampleCount / lteHighHriskInfo.ISampleCount >= highHriskCond.curOverCoverPercent
                        || 100.0 * lteHighHriskInfo.IWeakCoverSampleCount / lteHighHriskInfo.ISampleCount >= highHriskCond.curWeakCoverPercent)
                    {
                        lteHighHrisItemInfoList.Add(lteHighHriskInfo);
                    }
                }  
            }
        }

        protected void mainNearstCellInterefere()
        {
            foreach (string strCell in cellLapRetDic.Keys)
            {
                List<LTEHighriskInterfereCellInfo> lteInterfereCellList = new List<LTEHighriskInterfereCellInfo>();
                if (overCoverCellInterfereDic.ContainsKey(strCell))
                {
                    addLteInterfereCellList(strCell, lteInterfereCellList);
                }
                if (!overCoverCellInterfereInfoDic.ContainsKey(strCell))
                {
                    overCoverCellInterfereInfoDic.Add(strCell, lteInterfereCellList);
                }
            }
        }

        private void addLteInterfereCellList(string strCell, List<LTEHighriskInterfereCellInfo> lteInterfereCellList)
        {
            foreach (string strInterfereCell in overCoverCellInterfereDic[strCell])
            {
                if (cellLapRetDic.ContainsKey(strInterfereCell))
                {
                    LTEHighriskInterfereCellInfo lteInterfereIno = new LTEHighriskInterfereCellInfo();
                    lteInterfereIno.StrMainCellName = strCell;
                    lteInterfereIno.StrInterfereCellName = strInterfereCell;
                    lteInterfereIno.IInterfereCellSampleCount = cellLapRetDic[strInterfereCell].badSampleCount;
                    lteInterfereIno.IInterfereCellTotalSampleCount = cellLapRetDic[strInterfereCell].TotalSampleCount;
                    if (lteInterfereIno.IInterfereCellTotalSampleCount <= 0)
                    {
                        continue;
                    }
                    lteInterfereIno.FInterfereOverCoverRate = 100.0 * cellLapRetDic[strInterfereCell].badSampleCount / cellLapRetDic[strInterfereCell].TotalSampleCount;
                    lteInterfereCellList.Add(lteInterfereIno);
                }
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            ZTDIYQueryLteHighriskDataForm showForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(ZTDIYQueryLteHighriskDataForm).FullName);
            showForm = obj == null ? null : obj as ZTDIYQueryLteHighriskDataForm;
            if (showForm == null || showForm.IsDisposed)
            {
                showForm = new ZTDIYQueryLteHighriskDataForm(MainModel);
            }
            showForm.FillData(lteHighHrisItemInfoList, overCoverCellInterfereInfoDic);
            showForm.Show(MainModel.MainForm);
        }       

    }
    public class HighriskCond
    {
        public int curOverCoverRxlev { get; set; } = -90;
        public int curOverCoverSampleCount { get; set; } = 10;
        public float curOverCoverPercent { get; set; } = 10;
        public int curOverCoverMinDistance { get; set; } = 300;
        public int curOverCoverMaxDistance { get; set; } = 5000;
        public float curOverCoverDisFactor { get; set; } = 1.6f;
        public int curOverCoverInterfereRSRP { get; set; } = 6;

        public int curWeakCoverRxlev { get; set; } = -105;
        public int curWeakCoverSampleCount { get; set; } = 10;
        public int curWeakCoverMeanRSRP { get; set; } = -115;
        public float curWeakCoverPercent { get; set; } = 10;
    }

    public class LTEWeakCoverCellInfo
    {
        public LTECell lteCell { get; set; }
        public string StrCellName
        {
            get 
            {
                string strCell = "";
                if (lteCell != null)
                {
                    strCell = lteCell.Name;
                }
                return strCell;
            }
        }
        public List<TestPoint> tpList { get; set; } = new List<TestPoint>();
        public List<float> rxLevList { get; set; } = new List<float>();
        public double rxLevTotal { get; set; } = 0;
        private double rxLevMax = 0;
        private double rxLevMin = 0;

        public LTEWeakCoverCellInfo(LTECell cell, TestPoint tp, float rxlev)
        {
            this.lteCell = cell;
            Init(tp, rxlev);
        }

        private void Init(TestPoint tp, float rxlev)
        {
            tpList.Add(tp);
            rxLevList.Add(rxlev);
            rxLevTotal += rxlev;
            rxLevMax = rxlev;
            rxLevMin = rxlev;
        }

        public void AddTestPoint(TestPoint tp, float rxLev)
        {
            tpList.Add(tp);
            rxLevList.Add(rxLev);
            rxLevTotal += rxLev;
            if (rxLevMax < rxLev)
            {
                rxLevMax = rxLev;
            }
            if (rxLevMin > rxLev)
            {
                rxLevMin = rxLev;
            }
        }

        public void SetWeakTestPointCount(float meanRxLev)
        {
            WeakTestPointCount = 0;
            foreach (float f in rxLevList)
            {
                if (f < meanRxLev)
                {
                    WeakTestPointCount += 1;
                }
            }
        }

        public string LAC
        {
            get
            {
                if (lteCell != null)
                {
                    return lteCell.TAC.ToString();
                }
                return "";
            }
        }

        public string ECI
        {
            get
            {
                if (lteCell != null)
                {
                    return lteCell.ECI.ToString();
                }
                return "";
            }
        }

        public double RxLevMean
        {
            get { return Math.Round(rxLevTotal / tpList.Count, 2); }
        }

        public double RxLevMax
        {
            get { return Math.Round(rxLevMax, 2); }
        }

        public double RxLevMin
        {
            get { return Math.Round(rxLevMin, 2); }
        }

        public int TotalTestPointCount
        {
            get { return tpList.Count; }
        }

        public int WeakTestPointCount
        {
            get;
            private set;
        }

        public double WeakRatio 
        { 
            get 
            { 
                return Math.Round(WeakTestPointCount * 100d / TotalTestPointCount, 2); 
            } 
        }
    }

    public class LTEHighriskItemInfo
    {
        public string StrCellName { get; set; }
        public string StrTAC { get; set; }
        public string StrECI { get; set; }
        public int IOverCoverSampleCount { get; set; }
        public int IWeakCoverSampleCount { get; set; }
        public int ISampleCount { get; set; }

        public string StrOverCoverRate
        {
            get
            {
                if (ISampleCount > 0)
                {
                    return Math.Round(100.0 * IOverCoverSampleCount / ISampleCount, 2) + "%";
                } 
                else
                {
                    return "-";
                }
            }
        }

        public string StrWeakCoverRate
        {
            get
            {
                if (ISampleCount > 0)
                {
                    return Math.Round(100.0 * IWeakCoverSampleCount / ISampleCount, 2) + "%";
                }
                else
                {
                    return "-";
                }
            }
        }
    }

    public class LTEHighriskInterfereCellInfo
    {
        public string StrMainCellName { get; set; }
        public string StrInterfereCellName { get; set; }
        public int IInterfereCellSampleCount { get; set; }
        public int IInterfereCellTotalSampleCount { get; set; }
        public double FInterfereOverCoverRate { get; set; }
        public string StrIInterfereOverCoverRate
        {
            get
            {
                return FInterfereOverCoverRate.ToString("0.00") + "%";
            }
        }
    }
}
