﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTGSMRxQualAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode2 = new DevExpress.XtraGrid.GridLevelNode();
            this.gridViewFcnC_I = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControlCellFcnCI = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuFcnCI = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripCIExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewC_I = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ctxMenuRegion = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripRegionExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridControlRegion = new DevExpress.XtraGrid.GridControl();
            this.gridViewRegion = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlCell = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuCell = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripCellExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewCell = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand4 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand5 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn14 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.girdColumnTotalBler = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumnBlerRate = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.comboBoxEditFile = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.gridControlTP = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuTP = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripTPReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripTPReplayCompare = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripTPLineChartLocate = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripTPExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewTP = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewFcnC_I)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCellFcnCI)).BeginInit();
            this.ctxMenuFcnCI.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewC_I)).BeginInit();
            this.ctxMenuRegion.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRegion)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRegion)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCell)).BeginInit();
            this.ctxMenuCell.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCell)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEditFile.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTP)).BeginInit();
            this.ctxMenuTP.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTP)).BeginInit();
            this.xtraTabPage3.SuspendLayout();
            this.SuspendLayout();
            // 
            // gridViewFcnC_I
            // 
            this.gridViewFcnC_I.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridViewFcnC_I.Appearance.Row.Options.UseTextOptions = true;
            this.gridViewFcnC_I.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn44,
            this.gridColumn45,
            this.gridColumn46,
            this.gridColumn47,
            this.gridColumn48});
            this.gridViewFcnC_I.GridControl = this.gridControlCellFcnCI;
            this.gridViewFcnC_I.Name = "gridViewFcnC_I";
            this.gridViewFcnC_I.OptionsBehavior.Editable = false;
            this.gridViewFcnC_I.OptionsDetail.ShowDetailTabs = false;
            this.gridViewFcnC_I.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "频点";
            this.gridColumn41.FieldName = "Fcn";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 0;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "C/I均值";
            this.gridColumn42.FieldName = "C_I_Mean";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 1;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "C/I(0_6)";
            this.gridColumn43.FieldName = "C_I_0_6";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 2;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "C/I[6_9)";
            this.gridColumn44.FieldName = "C_I_6_9";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 3;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "C/I[9_12)";
            this.gridColumn45.FieldName = "C_I_9_12";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 4;
            // 
            // gridColumn46
            // 
            this.gridColumn46.Caption = "C/I[12_18)";
            this.gridColumn46.FieldName = "C_I_12_18";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 5;
            // 
            // gridColumn47
            // 
            this.gridColumn47.Caption = "C/I(>=18)";
            this.gridColumn47.FieldName = "C_I_18";
            this.gridColumn47.Name = "gridColumn47";
            this.gridColumn47.Visible = true;
            this.gridColumn47.VisibleIndex = 6;
            // 
            // gridColumn48
            // 
            this.gridColumn48.Caption = "采样点数";
            this.gridColumn48.FieldName = "SampleNumber";
            this.gridColumn48.Name = "gridColumn48";
            this.gridColumn48.Visible = true;
            this.gridColumn48.VisibleIndex = 7;
            // 
            // gridControlCellFcnCI
            // 
            this.gridControlCellFcnCI.ContextMenuStrip = this.ctxMenuFcnCI;
            this.gridControlCellFcnCI.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode1.LevelTemplate = this.gridViewFcnC_I;
            gridLevelNode1.RelationName = "FcnC_I";
            this.gridControlCellFcnCI.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControlCellFcnCI.Location = new System.Drawing.Point(0, 0);
            this.gridControlCellFcnCI.MainView = this.gridViewC_I;
            this.gridControlCellFcnCI.Name = "gridControlCellFcnCI";
            this.gridControlCellFcnCI.Size = new System.Drawing.Size(909, 439);
            this.gridControlCellFcnCI.TabIndex = 1;
            this.gridControlCellFcnCI.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewC_I,
            this.gridViewFcnC_I});
            // 
            // ctxMenuFcnCI
            // 
            this.ctxMenuFcnCI.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripCIExportExcel});
            this.ctxMenuFcnCI.Name = "contextMenuStripProblem";
            this.ctxMenuFcnCI.Size = new System.Drawing.Size(151, 26);
            // 
            // toolStripCIExportExcel
            // 
            this.toolStripCIExportExcel.Name = "toolStripCIExportExcel";
            this.toolStripCIExportExcel.Size = new System.Drawing.Size(150, 22);
            this.toolStripCIExportExcel.Text = "导出到Excel...";
            this.toolStripCIExportExcel.Click += new System.EventHandler(this.toolStripCIExportExcel_Click);
            // 
            // gridViewC_I
            // 
            this.gridViewC_I.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridViewC_I.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewC_I.Appearance.Row.Options.UseTextOptions = true;
            this.gridViewC_I.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewC_I.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn17,
            this.gridColumn28,
            this.gridColumn29});
            this.gridViewC_I.GridControl = this.gridControlCellFcnCI;
            this.gridViewC_I.Name = "gridViewC_I";
            this.gridViewC_I.OptionsBehavior.Editable = false;
            this.gridViewC_I.OptionsDetail.ShowDetailTabs = false;
            this.gridViewC_I.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "小区名";
            this.gridColumn17.FieldName = "CellName";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 0;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "测试频点";
            this.gridColumn28.FieldName = "Fcn";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 1;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "小区TCH频点";
            this.gridColumn29.FieldName = "TCH";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 2;
            // 
            // ctxMenuRegion
            // 
            this.ctxMenuRegion.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripRegionExportExcel});
            this.ctxMenuRegion.Name = "contextMenuStrip";
            this.ctxMenuRegion.Size = new System.Drawing.Size(151, 26);
            // 
            // toolStripRegionExportExcel
            // 
            this.toolStripRegionExportExcel.Name = "toolStripRegionExportExcel";
            this.toolStripRegionExportExcel.Size = new System.Drawing.Size(150, 22);
            this.toolStripRegionExportExcel.Text = "导出到Excel...";
            this.toolStripRegionExportExcel.Click += new System.EventHandler(this.ToolStripMenuRegionExportExcel_Click);
            // 
            // gridControlRegion
            // 
            this.gridControlRegion.ContextMenuStrip = this.ctxMenuRegion;
            this.gridControlRegion.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode2.RelationName = "Level1";
            this.gridControlRegion.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode2});
            this.gridControlRegion.Location = new System.Drawing.Point(0, 0);
            this.gridControlRegion.MainView = this.gridViewRegion;
            this.gridControlRegion.Name = "gridControlRegion";
            this.gridControlRegion.Size = new System.Drawing.Size(909, 439);
            this.gridControlRegion.TabIndex = 0;
            this.gridControlRegion.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewRegion});
            // 
            // gridViewRegion
            // 
            this.gridViewRegion.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridViewRegion.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewRegion.Appearance.Row.Options.UseTextOptions = true;
            this.gridViewRegion.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewRegion.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn37,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5});
            this.gridViewRegion.GridControl = this.gridControlRegion;
            this.gridViewRegion.Name = "gridViewRegion";
            this.gridViewRegion.OptionsBehavior.Editable = false;
            this.gridViewRegion.OptionsView.AllowCellMerge = true;
            this.gridViewRegion.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "区域名称";
            this.gridColumn1.FieldName = "regionName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "总采样点";
            this.gridColumn37.FieldName = "tpTotal";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 1;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "质差总数";
            this.gridColumn2.FieldName = "rxQualTotal";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 2;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "原因分类";
            this.gridColumn3.FieldName = "reason";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 3;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "质差采样点数";
            this.gridColumn4.FieldName = "reasonRxQualTotal";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 4;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "百分比(%)";
            this.gridColumn5.FieldName = "reasonRxQualRate";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 5;
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(916, 469);
            this.xtraTabControl1.TabIndex = 1;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.gridControlRegion);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(909, 439);
            this.xtraTabPage1.Text = "原因分析概况";
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.splitContainerControl1);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(909, 439);
            this.xtraTabPage2.Text = "问题详情";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.gridControlCell);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.labelControl2);
            this.splitContainerControl1.Panel2.Controls.Add(this.comboBoxEditFile);
            this.splitContainerControl1.Panel2.Controls.Add(this.labelControl1);
            this.splitContainerControl1.Panel2.Controls.Add(this.gridControlTP);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(909, 439);
            this.splitContainerControl1.SplitterPosition = 220;
            this.splitContainerControl1.TabIndex = 1;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // gridControlCell
            // 
            this.gridControlCell.ContextMenuStrip = this.ctxMenuCell;
            this.gridControlCell.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlCell.Location = new System.Drawing.Point(0, 0);
            this.gridControlCell.MainView = this.gridViewCell;
            this.gridControlCell.Name = "gridControlCell";
            this.gridControlCell.Size = new System.Drawing.Size(909, 220);
            this.gridControlCell.TabIndex = 0;
            this.gridControlCell.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewCell});
            // 
            // ctxMenuCell
            // 
            this.ctxMenuCell.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripCellExportExcel});
            this.ctxMenuCell.Name = "contextMenuStripProblem";
            this.ctxMenuCell.Size = new System.Drawing.Size(151, 26);
            // 
            // toolStripCellExportExcel
            // 
            this.toolStripCellExportExcel.Name = "toolStripCellExportExcel";
            this.toolStripCellExportExcel.Size = new System.Drawing.Size(150, 22);
            this.toolStripCellExportExcel.Text = "导出到Excel...";
            this.toolStripCellExportExcel.Click += new System.EventHandler(this.ToolStripMenuCellExportExcel_Click);
            // 
            // gridViewCell
            // 
            this.gridViewCell.Appearance.BandPanel.Options.UseTextOptions = true;
            this.gridViewCell.Appearance.BandPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewCell.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridViewCell.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewCell.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridViewCell.Appearance.Row.Options.UseTextOptions = true;
            this.gridViewCell.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewCell.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand1,
            this.gridBand3,
            this.gridBand4,
            this.gridBand5,
            this.gridBand2});
            this.gridViewCell.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.girdColumnTotalBler,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn32,
            this.gridColumnBlerRate});
            this.gridViewCell.GridControl = this.gridControlCell;
            this.gridViewCell.Name = "gridViewCell";
            this.gridViewCell.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridViewCell.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridViewCell.OptionsBehavior.Editable = false;
            this.gridViewCell.OptionsCustomization.AllowBandMoving = false;
            this.gridViewCell.OptionsCustomization.AllowBandResizing = false;
            this.gridViewCell.OptionsCustomization.AllowGroup = false;
            this.gridViewCell.OptionsFilter.AllowColumnMRUFilterList = false;
            this.gridViewCell.OptionsFilter.AllowFilterEditor = false;
            this.gridViewCell.OptionsFilter.AllowMRUFilterList = false;
            this.gridViewCell.OptionsFilter.ShowAllTableValuesInFilterPopup = true;
            this.gridViewCell.OptionsFilter.UseNewCustomFilterDialog = true;
            this.gridViewCell.OptionsSelection.MultiSelect = true;
            this.gridViewCell.OptionsView.ColumnAutoWidth = false;
            this.gridViewCell.OptionsView.EnableAppearanceEvenRow = true;
            this.gridViewCell.OptionsView.EnableAppearanceOddRow = true;
            this.gridViewCell.OptionsView.ShowGroupPanel = false;
            this.gridViewCell.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gridViewCell_FocusedRowChanged);
            this.gridViewCell.DoubleClick += new System.EventHandler(this.gridViewCell_DoubleClick);
            // 
            // gridBand1
            // 
            this.gridBand1.Columns.Add(this.gridColumn6);
            this.gridBand1.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.Width = 44;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "序号";
            this.gridColumn6.FieldName = "sn";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.Width = 44;
            // 
            // gridBand3
            // 
            this.gridBand3.Caption = "小区信息";
            this.gridBand3.Columns.Add(this.gridColumn7);
            this.gridBand3.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridBand3.Name = "gridBand3";
            this.gridBand3.Width = 100;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "小区名";
            this.gridColumn7.FieldName = "cellName";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.Width = 100;
            // 
            // gridBand4
            // 
            this.gridBand4.Caption = "基本信息";
            this.gridBand4.Columns.Add(this.gridColumn8);
            this.gridBand4.Columns.Add(this.gridColumn9);
            this.gridBand4.Columns.Add(this.gridColumn10);
            this.gridBand4.Columns.Add(this.gridColumn11);
            this.gridBand4.Columns.Add(this.gridColumn12);
            this.gridBand4.Columns.Add(this.gridColumn13);
            this.gridBand4.MinWidth = 20;
            this.gridBand4.Name = "gridBand4";
            this.gridBand4.Width = 440;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "LAC";
            this.gridColumn8.FieldName = "LAC";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.Width = 60;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "CI";
            this.gridColumn9.FieldName = "CI";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.Width = 60;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "经度";
            this.gridColumn10.FieldName = "longitude";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.Width = 80;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "纬度";
            this.gridColumn11.FieldName = "latitude";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.Width = 80;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "所属网格";
            this.gridColumn12.FieldName = "areaName";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.Width = 80;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "所属路段";
            this.gridColumn13.FieldName = "roadName";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.Width = 80;
            // 
            // gridBand5
            // 
            this.gridBand5.Caption = "采样点信息";
            this.gridBand5.Columns.Add(this.gridColumn14);
            this.gridBand5.Columns.Add(this.girdColumnTotalBler);
            this.gridBand5.Columns.Add(this.gridColumnBlerRate);
            this.gridBand5.Name = "gridBand5";
            this.gridBand5.Width = 260;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "总采样点数";
            this.gridColumn14.FieldName = "tpTotal";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.Width = 80;
            // 
            // girdColumnTotalBler
            // 
            this.girdColumnTotalBler.Caption = "质差总数";
            this.girdColumnTotalBler.FieldName = "rxQualTotal";
            this.girdColumnTotalBler.Name = "girdColumnTotalBler";
            this.girdColumnTotalBler.Visible = true;
            this.girdColumnTotalBler.Width = 80;
            // 
            // gridColumnBlerRate
            // 
            this.gridColumnBlerRate.Caption = "质差比例";
            this.gridColumnBlerRate.FieldName = "rxQualRate";
            this.gridColumnBlerRate.Name = "gridColumnBlerRate";
            this.gridColumnBlerRate.Visible = true;
            this.gridColumnBlerRate.Width = 100;
            // 
            // gridBand2
            // 
            this.gridBand2.Caption = "原因分类";
            this.gridBand2.Columns.Add(this.gridColumn18);
            this.gridBand2.Columns.Add(this.gridColumn19);
            this.gridBand2.Columns.Add(this.gridColumn20);
            this.gridBand2.Columns.Add(this.gridColumn21);
            this.gridBand2.Columns.Add(this.gridColumn22);
            this.gridBand2.Columns.Add(this.gridColumn23);
            this.gridBand2.Columns.Add(this.gridColumn24);
            this.gridBand2.Columns.Add(this.gridColumn25);
            this.gridBand2.Columns.Add(this.gridColumn26);
            this.gridBand2.Columns.Add(this.gridColumn27);
            this.gridBand2.Columns.Add(this.gridColumn32);
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 880;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "弱覆盖";
            this.gridColumn18.FieldName = "poorCover";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.Width = 80;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "占用不合理";
            this.gridColumn19.FieldName = "lapCover";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.Width = 80;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "背向覆盖";
            this.gridColumn20.FieldName = "backCover";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.Width = 80;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "覆盖杂乱";
            this.gridColumn21.FieldName = "noMainCell";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.Width = 80;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "室分泄漏";
            this.gridColumn22.FieldName = "inDoor";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.Width = 80;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "切换不及时";
            this.gridColumn23.FieldName = "hoDelay";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.Width = 80;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "切换不合理";
            this.gridColumn24.FieldName = "hoAbnormal";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.Width = 80;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "重选问题";
            this.gridColumn25.FieldName = "reselProb";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.Width = 80;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "频率干扰或故障";
            this.gridColumn26.FieldName = "freqInterference";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.Width = 80;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "质量毛刺";
            this.gridColumn27.FieldName = "burr";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.Width = 80;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "其它";
            this.gridColumn32.FieldName = "other";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.Width = 80;
            // 
            // labelControl2
            // 
            this.labelControl2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.labelControl2.Location = new System.Drawing.Point(723, 4);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(164, 14);
            this.labelControl2.TabIndex = 3;
            this.labelControl2.Text = "文件共 个，当前文件有 条记录";
            // 
            // comboBoxEditFile
            // 
            this.comboBoxEditFile.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.comboBoxEditFile.Location = new System.Drawing.Point(61, 1);
            this.comboBoxEditFile.Name = "comboBoxEditFile";
            this.comboBoxEditFile.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxEditFile.Size = new System.Drawing.Size(627, 21);
            this.comboBoxEditFile.TabIndex = 2;
            this.comboBoxEditFile.SelectedIndexChanged += new System.EventHandler(this.comboBoxEditFile_SelectedIndexChanged);
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(3, 4);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(52, 14);
            this.labelControl1.TabIndex = 1;
            this.labelControl1.Text = "文件列表:";
            // 
            // gridControlTP
            // 
            this.gridControlTP.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gridControlTP.ContextMenuStrip = this.ctxMenuTP;
            this.gridControlTP.Location = new System.Drawing.Point(0, 28);
            this.gridControlTP.MainView = this.gridViewTP;
            this.gridControlTP.Name = "gridControlTP";
            this.gridControlTP.Size = new System.Drawing.Size(909, 185);
            this.gridControlTP.TabIndex = 0;
            this.gridControlTP.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewTP});
            // 
            // ctxMenuTP
            // 
            this.ctxMenuTP.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripTPReplay,
            this.toolStripTPReplayCompare,
            this.toolStripTPLineChartLocate,
            this.toolStripTPExportExcel});
            this.ctxMenuTP.Name = "contextMenuStripTp";
            this.ctxMenuTP.Size = new System.Drawing.Size(151, 92);
            // 
            // toolStripTPReplay
            // 
            this.toolStripTPReplay.Image = global::MasterCom.RAMS.Properties.Resources.replay;
            this.toolStripTPReplay.Name = "toolStripTPReplay";
            this.toolStripTPReplay.Size = new System.Drawing.Size(150, 22);
            this.toolStripTPReplay.Text = "回放采样点";
            this.toolStripTPReplay.Click += new System.EventHandler(this.miReplayTestpoint_Click);
            // 
            // toolStripTPReplayCompare
            // 
            this.toolStripTPReplayCompare.Name = "toolStripTPReplayCompare";
            this.toolStripTPReplayCompare.Size = new System.Drawing.Size(150, 22);
            this.toolStripTPReplayCompare.Text = "对比回放文件";
            this.toolStripTPReplayCompare.Click += new System.EventHandler(this.miReplayTestpointCompare_Click);
            // 
            // toolStripTPLineChartLocate
            // 
            this.toolStripTPLineChartLocate.Name = "toolStripTPLineChartLocate";
            this.toolStripTPLineChartLocate.Size = new System.Drawing.Size(150, 22);
            this.toolStripTPLineChartLocate.Text = "定位时序图";
            this.toolStripTPLineChartLocate.Click += new System.EventHandler(this.miLocateLineChart_Click);
            // 
            // toolStripTPExportExcel
            // 
            this.toolStripTPExportExcel.Name = "toolStripTPExportExcel";
            this.toolStripTPExportExcel.Size = new System.Drawing.Size(150, 22);
            this.toolStripTPExportExcel.Text = "导出到Excel...";
            this.toolStripTPExportExcel.Click += new System.EventHandler(this.miExportTp_Click);
            // 
            // gridViewTP
            // 
            this.gridViewTP.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridViewTP.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewTP.Appearance.Row.Options.UseTextOptions = true;
            this.gridViewTP.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewTP.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35});
            this.gridViewTP.GridControl = this.gridControlTP;
            this.gridViewTP.Name = "gridViewTP";
            this.gridViewTP.OptionsBehavior.Editable = false;
            this.gridViewTP.OptionsView.ShowGroupPanel = false;
            this.gridViewTP.DoubleClick += new System.EventHandler(this.gridViewTP_DoubleClick);
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "序号";
            this.gridColumn15.FieldName = "SN";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 0;
            this.gridColumn15.Width = 61;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "原因";
            this.gridColumn16.FieldName = "reason";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 1;
            this.gridColumn16.Width = 100;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "采样点时间";
            this.gridColumn33.FieldName = "tpTime";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 2;
            this.gridColumn33.Width = 200;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "信号强度";
            this.gridColumn34.FieldName = "rxlev";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 3;
            this.gridColumn34.Width = 134;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "C/I";
            this.gridColumn35.FieldName = "c2i";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 4;
            this.gridColumn35.Width = 134;
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.gridControlCellFcnCI);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(909, 439);
            this.xtraTabPage3.Text = "小区频点C/I分析";
            // 
            // ZTGSMRxQualAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(916, 469);
            this.ContextMenuStrip = this.ctxMenuTP;
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "ZTGSMRxQualAnaListForm";
            this.Text = "质差分类列表";
            ((System.ComponentModel.ISupportInitialize)(this.gridViewFcnC_I)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCellFcnCI)).EndInit();
            this.ctxMenuFcnCI.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewC_I)).EndInit();
            this.ctxMenuRegion.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRegion)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRegion)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCell)).EndInit();
            this.ctxMenuCell.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCell)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEditFile.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTP)).EndInit();
            this.ctxMenuTP.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTP)).EndInit();
            this.xtraTabPage3.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControlRegion;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewRegion;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private System.Windows.Forms.ContextMenuStrip ctxMenuRegion;
        private System.Windows.Forms.ToolStripMenuItem toolStripRegionExportExcel;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl gridControlCell;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView gridViewCell;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn7;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn9;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn11;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn12;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn13;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn14;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn21;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn22;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn23;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn24;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn25;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn26;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn27;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn32;
        private System.Windows.Forms.ContextMenuStrip ctxMenuCell;
        private System.Windows.Forms.ToolStripMenuItem toolStripCellExportExcel;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraGrid.GridControl gridControlTP;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewTP;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxEditFile;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn girdColumnTotalBler;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumnBlerRate;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand4;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand5;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private System.Windows.Forms.ContextMenuStrip ctxMenuTP;
        private System.Windows.Forms.ToolStripMenuItem toolStripTPReplay;
        private System.Windows.Forms.ToolStripMenuItem toolStripTPReplayCompare;
        private System.Windows.Forms.ToolStripMenuItem toolStripTPLineChartLocate;
        private System.Windows.Forms.ToolStripMenuItem toolStripTPExportExcel;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private DevExpress.XtraGrid.GridControl gridControlCellFcnCI;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewC_I;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewFcnC_I;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private System.Windows.Forms.ContextMenuStrip ctxMenuFcnCI;
        private System.Windows.Forms.ToolStripMenuItem toolStripCIExportExcel;
    }
}