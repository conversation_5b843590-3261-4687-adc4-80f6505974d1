﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;

using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.NOP.BatchImport
{
    public partial class BatchImportSettingForm : BaseDialog
    {
        public BatchImportSettingForm()
        {
            InitializeComponent();
            btnCancel.Click += BtnCancel_Click;
            btnSave.Click += BtnSave_Click;
            btnSubmit.Click += BtnSubmit_Click;
            btnSelect.Click += BtnSelect_Click;
        }

        public string FileName
        {
            get;
            private set;
        }

        private void BtnSelect_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Xlsx;
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                txtFile.Text = dlg.FileName;
            }
        }

        private void BtnSubmit_Click(object sender, EventArgs e)
        {
            if (!IsValid())
            {
                return;
            }
            DialogResult = System.Windows.Forms.DialogResult.Yes;
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (!IsValid())
            {
                return;
            }
            DialogResult = System.Windows.Forms.DialogResult.No;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private bool IsValid()
        {
            string fileName = txtFile.Text.Trim();
            if (string.IsNullOrEmpty(txtFile.Text) || !File.Exists(txtFile.Text))
            {
                MessageBox.Show("未选定文件或文件不存在", this.Text, MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = System.Windows.Forms.DialogResult.Retry;
                return false;
            }
            FileName = fileName;
            return true;
        }
    }
}
