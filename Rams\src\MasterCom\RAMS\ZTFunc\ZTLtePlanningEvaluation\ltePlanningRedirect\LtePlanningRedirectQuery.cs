﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class LtePlanningRedirectQueryByFile : DIYReplayFileQuery
    {
        public LtePlanningRedirectQueryByFile(MainModel mainModel) 
            : base(mainModel)
        {
            IsAddSampleToDTDataManager = true;
            IsAddSampleToDTDataManager = true;
            IsAddMessageToDTDataManager = true;
            isAutoLoadCQTPicture = false;
            queryer = LtePlanningRedirectQueryer.Instance;
        }

        public override string Name
        {
            get { return queryer.Name; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return queryer.getRecLogItem();
        }

        protected override bool isValidCondition()
        {
            return queryer.IsValidCondition();
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.DefaultSerialThemeName = null;
            option.EventInclude = true;
            option.MessageInclude = true;
            option.MessageL3HexCode = true;

            List<ColumnDefItem> cols = null;
            foreach (string item in queryer.Columns)
            {
                cols = InterfaceManager.GetInstance().GetColumnDefByShowName(item);
                option.SampleColumns.AddRange(cols);
            }

            return option;
        }

        protected override void queryReplayInfo(ClientProxy clientProxy, Package package, FileInfo fileInfo)
        {
            base.queryReplayInfo(clientProxy, package, fileInfo);
            queryer.DoStatAfterQueryFile();
        }

        protected override void doPostReplayAction()
        {
            queryer.GetResultAfterQuery();
        }

        protected override void fireShowResult()
        {
            queryer.FireShowForm();
        }

        private readonly LtePlanningRedirectQueryer queryer;
    }

    public class LtePlanningRedirectQueryer
    {
        public static LtePlanningRedirectQueryer Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new LtePlanningRedirectQueryer();
                }
                return instance;
            }
        }

        public string Name
        {
            get { return "LTE规划重定向异常小区"; }
        }

        public List<string> Columns
        {
            get
            {
                return new List<string>()
                {
                    "isampleid",
                    "itime",
                    "ilongitude",
                    "ilatitude",
                    "lte_TAC",
                    "lte_ECI",
                    "lte_SCell_CI",
                    "lte_EARFCN",
                    "lte_PCI",
                    "lte_RSRQ",
                    "lte_RSRP",
                    "lte_RSSI",
                    "lte_SINR",
                    "lte_NCell_EARFCN",
                    "lte_NCell_PCI",
                    "lte_NCell_RSRP",
                    "lte_NCell_RSRQ",
                    "lte_NCell_RSSI",
                    "lte_NCell_SINR",
                    "lte_PDCP_DL",
                };
            }
        }

        public MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22030, "LTE规划重定向异常");
        }

        public bool IsValidCondition()
        {
            if (setForm == null || setForm.IsDisposed)
            {
                setForm = new LtePlanningRedirectSettingForm();
            }
            if (setForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            string xlsFileName = setForm.GetXlsFileName();

            // load Excel
            try
            {
                if (!LtePlanningInfoManager.Instance.LoadFromXls(xlsFileName))
                {
                    return false;
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            evaluator = new LtePlanningRedirectEvaluator(mainModel);
            return true;
        }

        // 每查询完一次文件即进行分析，然后删掉，防止内存占用过大
        public void DoStatAfterQueryFile()
        {
            evaluator.EvaluateFile();
            mainModel.DTDataManager.Clear();
        }

        public void GetResultAfterQuery()
        {
            //
        }

        public void FireShowForm()
        {
            List<LtePlanningRedirectCellView> cellViewList = evaluator.GetResult();
            List<LtePlanningRedirectSummaryView> summaryList = new List<LtePlanningRedirectSummaryView>();
            List<object> result = new List<object>();
            LtePlanningRedirectSummaryView summary = new LtePlanningRedirectSummaryView();
            summary.CellCount = LtePlanningInfoManager.Instance.LteCells.Count;
            summary.No2GCount = evaluator.No2GCount;
            summary.NoRedirectCount = evaluator.NoRedirectCount;
            summaryList.Add(summary);
            result.Add(cellViewList);
            result.Add(summaryList);

            LtePlanningRedirectResultForm resultForm = mainModel.GetObjectFromBlackboard(typeof(LtePlanningRedirectResultForm).FullName) as LtePlanningRedirectResultForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new LtePlanningRedirectResultForm(mainModel);
            }
            resultForm.FillData(result);
            if (!resultForm.Visible)
            {
                resultForm.Show(mainModel.MainForm);
            }

            evaluator = null;
        }

        private LtePlanningRedirectQueryer()
        {
            mainModel = MainModel.GetInstance();
        }

        private readonly MainModel mainModel;
        private LtePlanningRedirectSettingForm setForm;
        private LtePlanningRedirectEvaluator evaluator;
        private static LtePlanningRedirectQueryer instance;
    }
}
