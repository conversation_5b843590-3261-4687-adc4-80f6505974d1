﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTWeakSINRRoadQuery_NB : ZTWeakSINRRoadQuery
    {
        public ZTWeakSINRRoadQuery_NB(ServiceName serviceName)
            : base(serviceName)
        {
        }

        public override string Name
        {
            get { return "SINR质差路段_NB"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34008, this.Name);
        }
    }
}
