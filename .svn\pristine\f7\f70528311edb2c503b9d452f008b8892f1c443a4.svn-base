﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.ES.Core;

namespace MasterCom.ES.UI
{
    public enum FindResultType
    {
        Enum_Find_ExpDesc =1,
        Enum_Find_DecideExp
    }
    public class FindResultItem
    {
        public RoutineGroup group { get; set; }
        public ProcRoutine routine { get; set; }
        public NodeEntry node { get; set; }
        public FindResultType findType { get; set; }
    }
}
