﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class DelayReasonAnalyseBase : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static DelayReasonAnalyseBase instance = null;
        private List<ReasonInfo> resultListMo = null;
        private List<ReasonInfo> resultListMt = null;
        private ReasonCondition con = null;
        private string netType = "NR";

        public static DelayReasonAnalyseBase GetInstance(string net)
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new DelayReasonAnalyseBase();
                    }

                    instance.netType = net;
                }
            }
            else
            {
                instance.netType = net;
            }

            return instance;
        }
        public DelayReasonAnalyseBase()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
        }
        public override string Name
        {
            get
            {
                if (netType == "NR")
                {
                    return "VoNR超长时延分析(按区域)";
                }
                else
                {
                    return "VoLTE超长时延分析(按区域)";
                }
            }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27015, this.Name);
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

        DelayReasonSettingDlg setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new DelayReasonSettingDlg();
            }
            if (setForm.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                resultListMo = new List<ReasonInfo>();
                resultListMt = new List<ReasonInfo>();
                setForm.GetCondition(out con);
                return true;
            }
            return false;
        }
        protected override void fireShowForm()
        {
            if (resultListMo.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            DelayReasonResultDlg frm = MainModel.GetInstance().CreateResultForm(typeof(DelayReasonResultDlg)) as DelayReasonResultDlg;
            frm.FillData(resultListMo, resultListMt);
            frm.Visible = true;
            frm.BringToFront();
            resultListMo = null;
            resultListMt = null;
        }
        protected override void analyseFiles()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = new Dictionary<FileInfo, FileInfo>();
            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (fileInfo.Momt == (int)MoMtFile.MoFlag)
                {
                    FileInfo mtFile = MainModel.FileInfos.Find(delegate(FileInfo x)
                    {
                        return x.ID == fileInfo.EventCount;
                    });
                    moMtPair[fileInfo] = mtFile;
                }
            }

            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (fileInfo.Momt == (int)MoMtFile.MtFlag && !moMtPair.ContainsValue(fileInfo))
                {
                    moMtPair[fileInfo] = null;
                }
            }

            try
            {
                clearDataBeforeAnalyseFiles();

                int iloop = 0;
                foreach (KeyValuePair<FileInfo, FileInfo> pair in moMtPair)
                {
                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + moMtPair.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / moMtPair.Count);

                    condition.FileInfos.Clear();
                    if (pair.Key != null)
                    {
                        condition.FileInfos.Add(pair.Key);
                    }
                    if (pair.Value != null)
                    {
                        condition.FileInfos.Add(pair.Value);
                    }
                    replay();
                    condition.FileInfos.Clear();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }

                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }
        protected override void doStatWithQuery()
        {
            DTFileDataManager moFile = null;
            DTFileDataManager mtFile = null;
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file.MoMtFlag == (int)MoMtFile.MoFlag)
                {
                    moFile = file;
                }
                else if (file.MoMtFlag == (int)MoMtFile.MtFlag)
                {
                    mtFile = file;
                }
            }

            #region MO时延分析
            dealReasonInfo(moFile, mtFile);
            #endregion

            #region MT时延分析
            dealReasonInfo(mtFile, moFile);
            #endregion
        }

        private void dealReasonInfo(DTFileDataManager ownFile, DTFileDataManager otherFile)
        {
            if (ownFile == null)
            {
                return;
            }

            ReasonInfo info = null;
            foreach (DTData data in ownFile.DTDatas)
            {
                info = dealDTData(ownFile, otherFile, info, data);
            }
        }

        private ReasonInfo dealDTData(DTFileDataManager ownFile, DTFileDataManager otherFile, ReasonInfo info, DTData data)
        {
            if (data is TestPoint)
            {
                if (info != null)
                {
                    info.AddTp(data as TestPoint);
                }
            }
            else if (data is Event)
            {
                if (info != null)
                {
                    info.AddEvt(data as Event);
                }
            }
            else if (data is Message)
            {
                if (ownFile.MoMtFlag == (int)MoMtFile.MoFlag)
                {
                    dealMoMsgInfo(ownFile, otherFile, ref info, data);
                }
                else if (ownFile.MoMtFlag == (int)MoMtFile.MtFlag)
                {
                    dealMtMsgInfo(otherFile, ownFile, ref info, data);
                }
            }

            return info;
        }

        private void dealMoMsgInfo(DTFileDataManager moFile, DTFileDataManager mtFile, ref ReasonInfo moinfo, DTData data)
        {
            Message msg = data as Message;
            if (msg.ID == (int)EMsgInfo.InviteRequest)
            {
                moinfo = new ReasonInfo(moFile, mtFile);
                moinfo.Inviterequest = msg;
            }
            else if (moinfo != null)
            {
                dealMoTry100Msg(moFile, mtFile, moinfo, msg);
                dealMoInvite183ProgressMsg(moFile, mtFile, moinfo, msg);
                dealMoPrackMsg(moFile, mtFile, moinfo, msg);
                dealMoPrack200OKMsg(moFile, mtFile, moinfo, msg);
                dealMoUpdateMsg(moFile, mtFile, moinfo, msg);
                dealMoUpdate200OKMsg(moFile, mtFile, moinfo, msg);
                dealMoInvite180RingingMsg(moFile, mtFile, moinfo, msg);
                moinfo.AddMsg(msg);
            }
        }

        private void dealMoTry100Msg(DTFileDataManager moFile, DTFileDataManager mtFile, ReasonInfo moinfo, Message msg)
        {
            if (msg.ID == (int)EMsgInfo.Try100 && moinfo.Inviterequest != null && moinfo.Trying100 == null)
            {
                moinfo.Trying100 = msg;
                ReasonInfoDetail infoDetail = new ReasonInfoDetail("Invite -> Try 100", moinfo.Inviterequest, msg, moFile, mtFile);
                infoDetail.TimeCondition = con.timeInvite2tryingMo;
                if (calSpan(moinfo.Inviterequest, msg, con.timeInvite2tryingMo))
                {
                    infoDetail.IsNormal = true;
                }
                else
                {
                    infoDetail.IsNormal = false;
                    infoDetail.IsHeartNet = judgeReason(moinfo, infoDetail);
                }
                doAfterDeal(moinfo, infoDetail);
            }
        }

        private void dealMoInvite183ProgressMsg(DTFileDataManager moFile, DTFileDataManager mtFile, ReasonInfo moinfo, Message msg)
        {
            if (msg.ID == (int)EMsgInfo.Invite183Progress && moinfo.Trying100 != null && moinfo.Invite183progress == null)
            {
                moinfo.Invite183progress = msg;
                ReasonInfoDetail infoDetail = new ReasonInfoDetail("Try 100 -> 183 PROGRESS", moinfo.Trying100, msg, moFile, mtFile);
                infoDetail.TimeCondition = con.timeTrying2progressMo;
                if (calSpan(moinfo.Trying100, msg, con.timeTrying2progressMo))
                {
                    infoDetail.IsNormal = true;
                }
                else
                {
                    infoDetail.IsNormal = false;
                    if (!calInviteSpan(moinfo.Inviterequest, mtFile))
                    {
                        infoDetail.IsHeartNet = false;
                        infoDetail.IsHeartNet = judgeMoReasonNet(mtFile, moinfo, infoDetail);
                    }
                }
                doAfterDeal(moinfo, infoDetail);
            }
        }

        private void dealMoPrackMsg(DTFileDataManager moFile, DTFileDataManager mtFile, ReasonInfo moinfo, Message msg)
        {
            if (msg.ID == (int)EMsgInfo.Prack && moinfo.Invite183progress != null && moinfo.Prack == null)
            {
                moinfo.Prack = msg;
                ReasonInfoDetail infoDetail = new ReasonInfoDetail("183 PROGRESS -> PRACK", moinfo.Invite183progress, msg, moFile, mtFile);
                infoDetail.TimeCondition = con.timeProgress2prackMo;
                if (calSpan(moinfo.Invite183progress, msg, con.timeProgress2prackMo))
                {
                    infoDetail.IsNormal = true;
                }
                else
                {
                    infoDetail.IsNormal = false;
                    infoDetail.IsHeartNet = judgeReason(moinfo, infoDetail);
                }
                doAfterDeal(moinfo, infoDetail);
            }
        }

        private void dealMoPrack200OKMsg(DTFileDataManager moFile, DTFileDataManager mtFile, ReasonInfo moinfo, Message msg)
        {
            if (msg.ID == (int)EMsgInfo.Prack200OK && moinfo.Prack != null && moinfo.Prack200ok == null)
            {
                moinfo.Prack200ok = msg;
                ReasonInfoDetail infoDetail = new ReasonInfoDetail("PRACK -> PRACK 200 OK", moinfo.Prack, msg, moFile, mtFile);
                infoDetail.TimeCondition = con.timePrack2okMo;
                if (calSpan(moinfo.Prack, msg, con.timePrack2okMo))
                {
                    infoDetail.IsNormal = true;
                }
                else
                {
                    infoDetail.IsNormal = false;
                    infoDetail.IsHeartNet = judgeMoReasonP(mtFile, moinfo, infoDetail);
                }
                doAfterDeal(moinfo, infoDetail);
            }
        }

        private void dealMoUpdateMsg(DTFileDataManager moFile, DTFileDataManager mtFile, ReasonInfo moinfo, Message msg)
        {
            if (msg.ID == (int)EMsgInfo.Update && moinfo.Prack200ok != null && moinfo.Update == null)
            {
                moinfo.Update = msg;
                ReasonInfoDetail infoDetail = new ReasonInfoDetail("PRACK 200 OK -> UPDATE", moinfo.Prack200ok, msg, moFile, mtFile);
                infoDetail.TimeCondition = con.timeOK2updateMo;
                if (calSpan(moinfo.Prack200ok, msg, con.timeOK2updateMo))
                {
                    infoDetail.IsNormal = true;
                }
                else
                {
                    infoDetail.IsNormal = false;
                    infoDetail.IsHeartNet = judgeReason(moinfo, infoDetail);
                }
                doAfterDeal(moinfo, infoDetail);
            }
        }

        private void dealMoUpdate200OKMsg(DTFileDataManager moFile, DTFileDataManager mtFile, ReasonInfo moinfo, Message msg)
        {
            if (msg.ID == (int)EMsgInfo.Update200OK && moinfo.Update != null && moinfo.Update200ok == null)
            {
                moinfo.Update200ok = msg;
                ReasonInfoDetail infoDetail = new ReasonInfoDetail("UPDATE -> UPDATE 200 OK", moinfo.Update, msg, moFile, mtFile);
                infoDetail.TimeCondition = con.timeUpdate2okMo;
                if (calSpan(moinfo.Update, msg, con.timeUpdate2okMo))
                {
                    infoDetail.IsNormal = true;
                }
                else
                {
                    infoDetail.IsNormal = false;
                    infoDetail.IsHeartNet = judgeMoReasonU(mtFile, moinfo, infoDetail);
                }
                doAfterDeal(moinfo, infoDetail);
            }
        }

        private void dealMoInvite180RingingMsg(DTFileDataManager moFile, DTFileDataManager mtFile, ReasonInfo moinfo, Message msg)
        {
            if (msg.ID == (int)EMsgInfo.Invite180Ringing && moinfo.Update200ok != null && moinfo.Invite180ringing == null)
            {
                moinfo.Invite180ringing = msg;
                ReasonInfoDetail infoDetail = new ReasonInfoDetail("UPDATE 200 OK -> 180 RINGING", moinfo.Update200ok, msg, moFile, mtFile);
                infoDetail.TimeCondition = con.timeOK2ringingMo;
                if (calSpan(moinfo.Update200ok, msg, con.timeOK2ringingMo))
                {
                    infoDetail.IsNormal = true;
                }
                else
                {
                    infoDetail.IsNormal = false;
                    infoDetail.IsHeartNet = judgeMoReasonU(mtFile, moinfo, infoDetail);
                }
                doAfterDeal(moinfo, infoDetail);
                moinfo.SN = resultListMo.Count + 1;
                resultListMo.Add(moinfo);
            }
        }

        private void dealMtMsgInfo(DTFileDataManager moFile, DTFileDataManager mtFile, ref ReasonInfo mtinfo, DTData data)
        {
            Message msg = data as Message;
            if (msg.ID == (int)EMsgInfo.Paging)
            {
                mtinfo = new ReasonInfo(moFile, mtFile);
                mtinfo.Paging = msg;
            }
            else if (mtinfo != null)
            {
                dealMtInviteRequestMsg(moFile, mtFile, mtinfo, msg);
                dealMtTry100Msg(moFile, mtFile, mtinfo, msg);
                dealMtInvite183ProgressMsg(moFile, mtFile, mtinfo, msg);
                dealMtPrackMsg(moFile, mtFile, mtinfo, msg);
                dealMtPrack200OKMsg(moFile, mtFile, mtinfo, msg);
                dealMtUpdateMsg(moFile, mtFile, mtinfo, msg);
                dealMtUpdate200OKMsg(moFile, mtFile, mtinfo, msg);
                dealMtInvite180RingingMsg(moFile, mtFile, mtinfo, msg);
            }
        }

        private void dealMtInviteRequestMsg(DTFileDataManager moFile, DTFileDataManager mtFile, ReasonInfo mtinfo, Message msg)
        {
            if (msg.ID == (int)EMsgInfo.InviteRequest && mtinfo.Paging != null && mtinfo.Inviterequest == null)
            {
                mtinfo.Inviterequest = msg;
                ReasonInfoDetail infoDetail = new ReasonInfoDetail("Paging -> Invite", mtinfo.Paging, msg, moFile, mtFile);
                infoDetail.TimeCondition = con.timePaging2inviteMt;
                if (calSpan(mtinfo.Paging, msg, con.timePaging2inviteMt))
                {
                    infoDetail.IsNormal = true;
                }
                else
                {
                    infoDetail.IsNormal = false;
                    infoDetail.IsHeartNet = judgeReason(mtinfo, infoDetail);
                }
                doAfterDeal(mtinfo, infoDetail);
            }
        }

        private void dealMtTry100Msg(DTFileDataManager moFile, DTFileDataManager mtFile, ReasonInfo mtinfo, Message msg)
        {
            if (msg.ID == (int)EMsgInfo.Try100 && mtinfo.Inviterequest != null && mtinfo.Trying100 == null)
            {
                mtinfo.Trying100 = msg;
                ReasonInfoDetail infoDetail = new ReasonInfoDetail("Invite -> Try 100", mtinfo.Inviterequest, msg, moFile, mtFile);
                infoDetail.TimeCondition = con.timeInvite2tryingMt;
                if (calSpan(mtinfo.Inviterequest, msg, con.timeInvite2tryingMt))
                {
                    infoDetail.IsNormal = true;
                }
                else
                {
                    infoDetail.IsNormal = false;
                    infoDetail.IsEnd = judgeReason(mtinfo, infoDetail);
                }
                doAfterDeal(mtinfo, infoDetail);
            }
        }

        private void dealMtInvite183ProgressMsg(DTFileDataManager moFile, DTFileDataManager mtFile, ReasonInfo mtinfo, Message msg)
        {
            if (msg.ID == (int)EMsgInfo.Invite183Progress && mtinfo.Trying100 != null && mtinfo.Invite183progress == null)
            {
                mtinfo.Invite183progress = msg;
                ReasonInfoDetail infoDetail = new ReasonInfoDetail("Try 100 -> 183 PROGRESS", mtinfo.Trying100, msg, moFile, mtFile);
                infoDetail.TimeCondition = con.timeTrying2progressMt;
                if (calSpan(mtinfo.Trying100, msg, con.timeTrying2progressMt))
                {
                    infoDetail.IsNormal = true;
                }
                else
                {
                    infoDetail.IsNormal = false;
                    infoDetail.IsEnd = judgeReason(mtinfo, infoDetail);
                }
                doAfterDeal(mtinfo, infoDetail);
            }
        }

        private void dealMtPrackMsg(DTFileDataManager moFile, DTFileDataManager mtFile, ReasonInfo mtinfo, Message msg)
        {
            if (msg.ID == (int)EMsgInfo.Prack && mtinfo.Invite183progress != null && mtinfo.Prack == null)
            {
                mtinfo.Prack = msg;
                ReasonInfoDetail infoDetail = new ReasonInfoDetail("183 PROGRESS -> PRACK", mtinfo.Invite183progress, msg, moFile, mtFile);
                infoDetail.TimeCondition = con.timeProgress2prackMt;
                if (calSpan(mtinfo.Invite183progress, msg, con.timeProgress2prackMt))
                {
                    infoDetail.IsNormal = true;
                }
                else
                {
                    infoDetail.IsNormal = false;
                    judgeMtReason183Progress(moFile, mtinfo, infoDetail);
                }
                doAfterDeal(mtinfo, infoDetail);
            }
        }

        private void dealMtPrack200OKMsg(DTFileDataManager moFile, DTFileDataManager mtFile, ReasonInfo mtinfo, Message msg)
        {
            if (msg.ID == (int)EMsgInfo.Prack200OK && mtinfo.Prack != null && mtinfo.Prack200ok == null)
            {
                mtinfo.Prack200ok = msg;
                ReasonInfoDetail infoDetail = new ReasonInfoDetail("PRACK -> PRACK 200 OK", mtinfo.Prack, msg, moFile, mtFile);
                infoDetail.TimeCondition = con.timePrack2okMt;
                if (calSpan(mtinfo.Prack, msg, con.timePrack2okMt))
                {
                    infoDetail.IsNormal = true;
                }
                else
                {
                    infoDetail.IsNormal = false;
                    infoDetail.IsEnd = judgeReason(mtinfo, infoDetail);
                }
                doAfterDeal(mtinfo, infoDetail);
            }
        }

        private void dealMtUpdateMsg(DTFileDataManager moFile, DTFileDataManager mtFile, ReasonInfo mtinfo, Message msg)
        {
            if (msg.ID == (int)EMsgInfo.Update && mtinfo.Prack200ok != null && mtinfo.Update == null)
            {
                mtinfo.Update = msg;
                ReasonInfoDetail infoDetail = new ReasonInfoDetail("PRACK 200 OK -> UPDATE", mtinfo.Prack200ok, msg, moFile, mtFile);
                infoDetail.TimeCondition = con.timeOK2updateMt;
                if (calSpan(mtinfo.Prack200ok, msg, con.timeOK2updateMt))
                {
                    infoDetail.IsNormal = true;
                }
                else
                {
                    infoDetail.IsNormal = false;
                    judgeMtReasonPrack200ok(moFile, mtinfo, infoDetail);
                }
                doAfterDeal(mtinfo, infoDetail);
            }
        }

        private void dealMtUpdate200OKMsg(DTFileDataManager moFile, DTFileDataManager mtFile, ReasonInfo mtinfo, Message msg)
        {
            if (msg.ID == (int)EMsgInfo.Update200OK && mtinfo.Update != null && mtinfo.Update200ok == null)
            {
                mtinfo.Update200ok = msg;
                ReasonInfoDetail infoDetail = new ReasonInfoDetail("UPDATE -> UPDATE 200 OK", mtinfo.Update, msg, moFile, mtFile);
                infoDetail.TimeCondition = con.timeUpdate2okMt;
                if (calSpan(mtinfo.Update, msg, con.timeUpdate2okMt))
                {
                    infoDetail.IsNormal = true;
                }
                else
                {
                    infoDetail.IsNormal = false;
                    infoDetail.IsEnd = judgeReason(mtinfo, infoDetail);
                }
                resultListMo.Add(mtinfo);
            }
        }

        private void dealMtInvite180RingingMsg(DTFileDataManager moFile, DTFileDataManager mtFile, ReasonInfo mtinfo, Message msg)
        {
            if (msg.ID == (int)EMsgInfo.Invite180Ringing && mtinfo.Update200ok != null && mtinfo.Invite180ringing == null)
            {
                mtinfo.Invite180ringing = msg;
                ReasonInfoDetail infoDetail = new ReasonInfoDetail("UPDATE 200 OK -> 180 RINGING", mtinfo.Update200ok, msg, moFile, mtFile);
                infoDetail.TimeCondition = con.timeOK2ringingMt;
                if (calSpan(mtinfo.Update200ok, msg, con.timeOK2ringingMt))
                {
                    infoDetail.IsNormal = true;
                }
                else
                {
                    infoDetail.IsNormal = false;
                    infoDetail.IsEnd = judgeReason(mtinfo, infoDetail);
                }
                doAfterDeal(mtinfo, infoDetail);
                mtinfo.SN = resultListMt.Count + 1;
                resultListMt.Add(mtinfo);
            }
        }

        private void judgeMtReason183Progress(DTFileDataManager moFile, ReasonInfo mtinfo, ReasonInfoDetail infoDetail)
        {
            if (!calHandover(mtinfo, infoDetail) && !calTaupdate(mtinfo, infoDetail))
            {
                calCover(mtinfo, infoDetail);
                if (!infoDetail.IsCover)
                {
                    if (calOverNet(moFile, mtinfo.Invite183progress, infoDetail))
                    {
                        infoDetail.IsMoAnalyse = true;
                    }
                    else
                    {
                        infoDetail.IsHeartNet = true;
                    }
                }
            }
        }

        private void judgeMtReasonPrack200ok(DTFileDataManager moFile, ReasonInfo mtinfo, ReasonInfoDetail infoDetail)
        {
            if (!calHandover(mtinfo, infoDetail) && !calTaupdate(mtinfo, infoDetail))
            {
                calCover(mtinfo, infoDetail);
                if (!infoDetail.IsCover)
                {
                    if (!calOverNet(moFile, mtinfo.Prack200ok, infoDetail))
                    {
                        infoDetail.IsHeartNet = true;
                    }
                    else
                    {
                        infoDetail.IsMoAnalyse = false;
                    }
                }
            }
        }

        private bool judgeMoReasonU(DTFileDataManager mtFile, ReasonInfo moinfo, ReasonInfoDetail infoDetail)
        {
            if (!calHandover(moinfo,  infoDetail) && !calTaupdate(moinfo,  infoDetail))
            {
                calCover(moinfo,  infoDetail);
                if (!infoDetail.IsCover 
                    && !calMtAnalyseU(moinfo.Prack, mtFile,  infoDetail)
                    && !calOverNet(mtFile, moinfo.Prack,  infoDetail))
                {
                    return true;
                }
            }

            return false;
        }

        private bool judgeMoReasonP(DTFileDataManager mtFile, ReasonInfo moinfo, ReasonInfoDetail infoDetail)
        {
            if (!calHandover(moinfo,  infoDetail) && !calTaupdate(moinfo,  infoDetail))
            {
                calCover(moinfo,  infoDetail);
                if (!infoDetail.IsCover 
                    && !calMtAnalyse(moinfo.Prack, mtFile,  infoDetail)
                    && !calOverNet(mtFile, moinfo.Prack,  infoDetail))
                {
                    return true;
                }
            }

            return false;
        }

        private bool judgeMoReasonNet(DTFileDataManager mtFile, ReasonInfo moinfo, ReasonInfoDetail infoDetail)
        {
            if (!calHandover(moinfo,  infoDetail) && !calTaupdate(moinfo,  infoDetail))
            {
                calCover(moinfo,  infoDetail);
                if (!infoDetail.IsCover 
                    && !calOverNet(mtFile, moinfo.Trying100,  infoDetail))
                {
                    return true;
                }
            }

            return false;
        }

        private bool judgeReason(ReasonInfo info, ReasonInfoDetail infoDetail)
        {
            if (!calHandover(info,  infoDetail) && !calTaupdate(info,  infoDetail))
            {
                calCover(info,  infoDetail);
                if (!infoDetail.IsCover)
                {
                    return true;
                }
            }

            return false;
        }

        private void doAfterDeal(ReasonInfo info, ReasonInfoDetail infoDetail)
        {
            info.DetailList.Add(infoDetail);
            info.TestPoints.Clear();
            info.Events.Clear();
            info.Messages.Clear();
        }

        /// <summary>
        /// 判断是否低于阀值
        /// </summary>
        /// <param name="msg1">开始信令</param>
        /// <param name="msg2">结束信令</param>
        /// <param name="num">阀值</param>
        /// <returns></returns>
        private bool calSpan(Message msg1, Message msg2, int num)
        {
            if ((msg2.DateTime - msg1.DateTime).TotalMilliseconds <= num)
            {
                return true;
            }
            return false;
        }
        /// <summary>
        /// 判断是否包含切换
        /// </summary>
        /// <param name="info"></param>
        /// <param name="detail"></param>
        /// <returns></returns>
        private bool calHandover(ReasonInfo info, ReasonInfoDetail detail)
        {
            foreach (Event evt in info.Events)
            {
                if (evt.ID == 850 || evt.ID == 898 ||     // LTE TDD HandOver
                    evt.ID == 3155 || evt.ID == 3158 ||   // LTE FDD HandOver
                    evt.ID == (int)NREventManager.LTEInterHandoverRequest || evt.ID == (int)NREventManager.LTEIntraHandoverRequest ||   // NR LTE HandOver
                    evt.ID == (int)NREventManager.NRInterHandoverRequest || evt.ID == (int)NREventManager.NRIntraHandoverRequest)     // NR HandOver
                {
                    detail.IsHandover = true;
                    return true;
                }
            }
            detail.IsHandover = false;
            return false;
        }
        /// <summary>
        /// 判断是否包含TA更新
        /// </summary>
        /// <param name="info"></param>
        /// <param name="detail"></param>
        /// <returns></returns>
        private bool calTaupdate(ReasonInfo info, ReasonInfoDetail detail)
        {
            foreach (Event evt in info.Events)
            {
                if (evt.ID == 852 || evt.ID == 3171 || evt.ID == 9008)    //Track Area Update Attempt
                {
                    detail.IsTaupdate = true;
                    return true;
                }
            }
            detail.IsTaupdate = false;
            return false;
        }
        /// <summary>
        /// 判断是否质差导致
        /// </summary>
        /// <param name="info"></param>
        /// <param name="detail"></param>
        private void calCover(ReasonInfo info, ReasonInfoDetail detail)
        {
            float rsrp = 0;
            float sinr = 0;
            foreach (TestPoint tp in info.TestPoints)
            {
                if (GetRSRP(tp) != null && GetSINR(tp) != null)
                {
                    rsrp = (float)GetRSRP(tp);
                    sinr = (float)GetSINR(tp);
                    if (rsrp < -105 && sinr < -3)
                    {
                        detail.IsCover = true;
                    }
                }
            }
            detail.IsCover = false;
        }
        /// <summary>
        /// 判断主被叫寻呼时间是否过长
        /// </summary>
        /// <param name="moMsg"></param>
        /// <param name="mtFile"></param>
        /// <returns></returns>
        private bool calInviteSpan(Message moMsg, DTFileDataManager mtFile)
        {
            if (mtFile != null)
            {
                foreach (Message msg in mtFile.Messages)
                {
                    if (msg.DateTime > moMsg.DateTime && msg.ID == (int)EMsgInfo.InviteRequest)
                    {
                        return (msg.DateTime - moMsg.DateTime).TotalMilliseconds >= con.timeFind;
                    }
                }
            }
            return false;
        }
        /// <summary>
        /// 判断主被叫是否在2/3G网络
        /// </summary>
        /// <param name="info"></param>
        /// <param name="msg"></param>
        /// <param name="detail"></param>
        /// <returns></returns>
        private bool calOverNet(DTFileDataManager file, Message msg, ReasonInfoDetail detail)
        {
            if (file != null)
            {
                foreach (Event evt in file.Events)
                {
                    if (msg.DateTime < evt.DateTime && 
                        (evt.ID == 1022 || evt.ID == 1042 || evt.ID == 1021 || evt.ID == 1041 ||
                         evt.ID == 9400 || evt.ID == 9401))    //GSM MT/MO Call Attempt, TD MT/MO Call Attempt
                    {
                        detail.IsOverNet = true;
                        return true;
                    }
                }
            }
            detail.IsOverNet = false;
            return false;
        }
        /// <summary>
        /// 判断被叫响应时长是否过长 PRA到PRA200
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="file"></param>
        /// <param name="detail"></param>
        /// <returns></returns>
        private bool calMtAnalyse(Message msg, DTFileDataManager file, ReasonInfoDetail detail)
        {
            if (file != null)
            {
                Message msgpra, msgpra200;
                getPraMsg(msg, file, out msgpra, out msgpra200);
                if (msgpra != null && msgpra200 != null
                    && (msgpra200.DateTime - msgpra.DateTime).TotalMilliseconds >= con.timePrack2okMt)
                {
                    detail.IsMtAnalyse = true;
                    return true;
                }
            }
            detail.IsMtAnalyse = false;
            return false;
        }

        private void getPraMsg(Message msg, DTFileDataManager file, out Message msgpra, out Message msgpra200)
        {
            msgpra = null;
            msgpra200 = null;
            foreach (Message m in file.Messages)
            {
                if (msg.DateTime < m.DateTime)
                {
                    if (msg.ID == (int)EMsgInfo.Prack)
                    {
                        msgpra = msg;
                    }
                    if (msg.ID == (int)EMsgInfo.Prack200OK)
                    {
                        msgpra200 = msg;
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 判断被叫响应时长是否过长 UPDATE到UPDATE200
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="file"></param>
        /// <param name="detail"></param>
        /// <returns></returns>
        private bool calMtAnalyseU(Message msg, DTFileDataManager file, ReasonInfoDetail detail)
        {
            if (file != null)
            {
                Message msgupd, msgupd200;
                getUpdMsg(msg, file, out msgupd, out msgupd200);
                if (msgupd != null && msgupd200 != null
                    && (msgupd200.DateTime - msgupd.DateTime).TotalMilliseconds >= con.timeUpdate2okMt)
                {
                    detail.IsMtAnalyse = true;
                    return true;
                }
            }
            detail.IsMtAnalyse = false;
            return false;
        }

        private void getUpdMsg(Message msg, DTFileDataManager file, out Message msgupd, out Message msgupd200)
        {
            msgupd = null;
            msgupd200 = null;
            foreach (Message m in file.Messages)
            {
                if (msg.DateTime < m.DateTime)
                {
                    if (msg.ID == (int)EMsgInfo.Update)
                    {
                        msgupd = msg;
                    }
                    if (msg.ID == (int)EMsgInfo.Update200OK)
                    {
                        msgupd200 = msg;
                        break;
                    }
                }
            }
        }

        protected object GetRSRP(TestPoint tp)
        {
            if (tp is TestPoint_NR)
            {
                return tp["NR_SS_RSRP"];
            }
            else
            {
                return tp["lte_RSRP"];
            }
        }
        protected object GetSINR(TestPoint tp)
        {
            if (tp is TestPoint_NR)
            {
                return tp["NR_SS_SINR"];
            }
            else
            {
                return tp["lte_SINR"];
            }
        }
    }

    public class ReasonCondition
    {
        public int timeFind { get; set; }               //寻呼时长
        //public int timeResponse { get; set; }         //响应时长
        public int timeInvite2tryingMo { get; set; }    //主叫 IMS_SIP_INVITE => IMS_SIP_INVITE->Trying(100)时间间隔（被叫同下）
        public int timeTrying2progressMo { get; set; }  //主叫 IMS_SIP_INVITE->Trying(100) => IMS_SIP_INVITE->Session_Progress(183)时间间隔（被叫同下）
        public int timeProgress2prackMo { get; set; }   //主叫 IMS_SIP_INVITE->Session_Progress(183) => IMS_SIP_PRACK时间间隔（被叫同下）
        public int timePrack2okMo { get; set; }         //主叫 IMS_SIP_PRACK => IMS_SIP_PRACK->OK(200)时间间隔（被叫同下）
        public int timeOK2updateMo { get; set; }        //主叫 IMS_SIP_PRACK->OK(200) => IMS_SIP_UPDATE时间间隔（被叫同下）
        public int timeUpdate2okMo { get; set; }        //主叫 IMS_SIP_UPDATE => IMS_SIP_UPDATE->OK(200)时间间隔（被叫同下）
        public int timeOK2ringingMo { get; set; }       //主叫 IMS_SIP_UPDATE->OK(200) => IMS_SIP_INVITE->Ringing(180)时间间隔（被叫同下）
        public int timePaging2inviteMt { get; set; }    //被叫 Paging => IMS_SIP_INVITE时间间隔
        public int timeInvite2tryingMt { get; set; }
        public int timeTrying2progressMt { get; set; }
        public int timeProgress2prackMt { get; set; }
        public int timePrack2okMt { get; set; }
        public int timeOK2updateMt { get; set; }
        public int timeUpdate2okMt { get; set; }
        public int timeOK2ringingMt { get; set; }
        public ReasonCondition()
        {
            timeFind = 100;

            timeInvite2tryingMo = 600;
            timeTrying2progressMo = 1800;
            timeProgress2prackMo = 100;
            timePrack2okMo = 500;
            timeOK2updateMo = 100;
            timeUpdate2okMo = 800;
            timeOK2ringingMo = 100;

            timePaging2inviteMt = 600;
            timeInvite2tryingMt = 20;
            timeTrying2progressMt = 30;
            timeProgress2prackMt = 1000;
            timePrack2okMt = 20;
            timeOK2updateMt = 500;
            timeUpdate2okMt = 30;
            timeOK2ringingMt = 20;
        }
    }

    public enum EMsgInfo
    {
        Paging = MessageManager.LTE_RRC_Paging,
        InviteRequest = MessageManager.Msg_IMS_SIP_INVITE,
        Try100 = MessageManager.Msg_IMS_SIP_INVITE_Trying,
        Invite183Progress = MessageManager.Msg_IMS_SIP_INVITE_Session_Progress,
        Prack = MessageManager.Msg_IMS_SIP_PRACK,
        Prack200OK = MessageManager.Msg_IMS_SIP_PRACK_OK,
        Update = MessageManager.Msg_IMS_SIP_UPDATE,
        Update200OK = MessageManager.Msg_IMS_SIP_UPDATE_OK,
        Invite180Ringing = MessageManager.Msg_IMS_SIP_INVITE_Ringing
    }

    public class ReasonInfo
    {
        public ReasonInfo(DTFileDataManager mofile, DTFileDataManager mtfile)
        {
            if (mofile != null)
            {
                MoFileName = mofile.FileName;
            }
            if (mtfile != null)
            {
                MtFileName = mtfile.FileName;
            }
        }
        public int SN { get; set; }
        public string MoFileName { get; set; }
        public string MtFileName { get; set; }
        public Message Paging { get; set; }
        public Message Inviterequest { get; set; }
        public Message Trying100 { get; set; }
        public Message Invite183progress { get; set; }
        public Message Prack { get; set; }
        public Message Prack200ok { get; set; }
        public Message Update { get; set; }
        public Message Update200ok { get; set; }
        public Message Invite180ringing { get; set; }
        public List<ReasonInfoDetail> DetailList { get; set; } = new List<ReasonInfoDetail>();

        private readonly List<TestPoint> tpList = new List<TestPoint>();
        public void AddTp(TestPoint tp)
        {
            tpList.Add(tp);
        }
        public List<TestPoint> TestPoints
        {
            get { return tpList; }
        }

        private readonly List<Event> evtList = new List<Event>();
        public void AddEvt(Event evt)
        {
            evtList.Add(evt);
        }
        public List<Event> Events
        {
            get { return evtList; }
        }

        private readonly List<Message> msgList = new List<Message>();
        public void AddMsg(Message msg)
        {
            msgList.Add(msg);
        }
        public List<Message> Messages
        {
            get { return msgList; }
        }
    }
    public class ReasonInfoDetail
    {
        public ReasonInfoDetail(string desc, Message start, Message end, DTFileDataManager moFile, DTFileDataManager mtFile)
        {
            this.Desc = desc;
            this.BeginTime = start.DateTime;
            this.EndTime = end.DateTime;
            this.SpanTime = (end.DateTime - start.DateTime).TotalMilliseconds;
            StartMsg = start;
            EndMsg = end;
            if (moFile != null)
            {
                this.MoFile = moFile.FileName;
            }
            if (mtFile != null)
            {
                this.MtFile = mtFile.FileName;
            }
        }
        public int TimeCondition { get; set; }
        public string MoFile { get; set; }
        public string MtFile { get; set; }
        public Message StartMsg { get; set; }
        public Message EndMsg { get; set; }
        public string Desc { get; set; }
        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }
        public double SpanTime { get; set; }
        public bool IsNormal { get; set; }
        public bool IsHandover { get; set; }
        public bool IsTaupdate { get; set; }
        public bool IsCover { get; set; }
        public bool IsHeartNet { get; set; }
        public bool IsEnd { get; set; }
        public bool IsOverNet { get; set; }
        public bool IsMtAnalyse { get; set; }
        public bool IsMoAnalyse { get; set; }
    }
}
