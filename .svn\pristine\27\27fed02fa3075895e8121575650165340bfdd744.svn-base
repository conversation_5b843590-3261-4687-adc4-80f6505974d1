﻿namespace MasterCom.RAMS.NOP
{
    partial class TaskOrderStatForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.XYDiagram xyDiagram1 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel1 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.Series series2 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel2 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel3 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle1 = new DevExpress.XtraCharts.ChartTitle();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gisPanel = new MasterCom.RAMS.NOP.GISPanel();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridCtrl = new DevExpress.XtraGrid.GridControl();
            this.gv = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.chart = new DevExpress.XtraCharts.ChartControl();
            this.panel1 = new System.Windows.Forms.Panel();
            this.colorEvt = new DevExpress.XtraEditors.ColorEdit();
            this.chkDisplayEvent = new DevExpress.XtraEditors.CheckEdit();
            this.cbxGisCol = new DevExpress.XtraEditors.ComboBoxEdit();
            this.btnRenderOption = new DevExpress.XtraEditors.SimpleButton();
            this.btnQuery = new DevExpress.XtraEditors.SimpleButton();
            this.dtPickerEnd = new System.Windows.Forms.DateTimePicker();
            this.label2 = new System.Windows.Forms.Label();
            this.dtPickerBegin = new System.Windows.Forms.DateTimePicker();
            this.label3 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chart)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).BeginInit();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorEvt.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDisplayEvent.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxGisCol.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(139, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(138, 22);
            this.miExportExcel.Text = "导出Excel...";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.gisPanel);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.AutoScroll = true;
            this.splitContainerControl1.Panel2.Controls.Add(this.splitContainerControl2);
            this.splitContainerControl1.Panel2.Controls.Add(this.panel1);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1284, 662);
            this.splitContainerControl1.SplitterPosition = 750;
            this.splitContainerControl1.TabIndex = 2;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // gisPanel
            // 
            this.gisPanel.CurColRender = null;
            this.gisPanel.DisplayNameLabel = true;
            this.gisPanel.DisplayValueLabel = true;
            this.gisPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gisPanel.Location = new System.Drawing.Point(0, 0);
            this.gisPanel.Name = "gisPanel";
            this.gisPanel.PointColor = System.Drawing.Color.Black;
            this.gisPanel.Renders = null;
            this.gisPanel.Size = new System.Drawing.Size(750, 662);
            this.gisPanel.TabIndex = 0;
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.Horizontal = false;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 69);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.gridCtrl);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.chart);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(528, 593);
            this.splitContainerControl2.SplitterPosition = 421;
            this.splitContainerControl2.TabIndex = 4;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // gridCtrl
            // 
            this.gridCtrl.ContextMenuStrip = this.contextMenuStrip1;
            this.gridCtrl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrl.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridCtrl.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridCtrl.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridCtrl.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridCtrl.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gridCtrl.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gridCtrl.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridCtrl.Location = new System.Drawing.Point(0, 0);
            this.gridCtrl.MainView = this.gv;
            this.gridCtrl.Name = "gridCtrl";
            this.gridCtrl.ShowOnlyPredefinedDetails = true;
            this.gridCtrl.Size = new System.Drawing.Size(528, 421);
            this.gridCtrl.TabIndex = 3;
            this.gridCtrl.UseEmbeddedNavigator = true;
            this.gridCtrl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv});
            // 
            // gv
            // 
            this.gv.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gv.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gv.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gv.ColumnPanelRowHeight = 50;
            this.gv.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11});
            this.gv.GridControl = this.gridCtrl;
            this.gv.Name = "gv";
            this.gv.OptionsBehavior.Editable = false;
            this.gv.OptionsDetail.ShowDetailTabs = false;
            this.gv.OptionsView.ShowGroupPanel = false;
            this.gv.OptionsView.ShowIndicator = false;
            this.gv.FocusedColumnChanged += new DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventHandler(this.gv_FocusedColumnChanged);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "地市名称";
            this.gridColumn1.FieldName = "地市名称";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "工单总数";
            this.gridColumn2.FieldName = "工单总数";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "待派发工单";
            this.gridColumn3.FieldName = "待派发工单";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.ToolTip = "预处理工单数";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "待接单工单";
            this.gridColumn4.FieldName = "待接单工单";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.ToolTip = "已派单工单数";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "待回单工单";
            this.gridColumn5.FieldName = "待回单工单";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.ToolTip = "已派单+已接单";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "待验证工单";
            this.gridColumn6.FieldName = "待验证工单";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.ToolTip = "已回单工单数";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "已归档工单";
            this.gridColumn7.FieldName = "已归档工单";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.ToolTip = "已归档工单";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "已解决工单";
            this.gridColumn8.FieldName = "已解决工单";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.ToolTip = "已解决工单";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 7;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "未解决工单";
            this.gridColumn9.FieldName = "未解决工单";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.ToolTip = "未解决工单";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 8;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "问题解决率";
            this.gridColumn10.FieldName = "问题解决率";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.ToolTip = "已解决工单/工单总数";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 9;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "事件个数";
            this.gridColumn11.FieldName = "事件个数";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 10;
            // 
            // chart
            // 
            xyDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisY.VisibleInPanesSerializable = "-1";
            this.chart.Diagram = xyDiagram1;
            this.chart.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chart.EmptyChartText.Text = "无数据";
            this.chart.Legend.Visible = false;
            this.chart.Location = new System.Drawing.Point(0, 0);
            this.chart.Name = "chart";
            sideBySideBarSeriesLabel1.LineVisible = true;
            sideBySideBarSeriesLabel1.ShowForZeroValues = true;
            series1.Label = sideBySideBarSeriesLabel1;
            series1.Name = "Series 1";
            sideBySideBarSeriesLabel2.LineVisible = true;
            series2.Label = sideBySideBarSeriesLabel2;
            series2.Name = "Series 2";
            this.chart.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1,
        series2};
            sideBySideBarSeriesLabel3.LineVisible = true;
            this.chart.SeriesTemplate.Label = sideBySideBarSeriesLabel3;
            this.chart.Size = new System.Drawing.Size(528, 166);
            this.chart.TabIndex = 0;
            chartTitle1.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold);
            chartTitle1.Text = "标题";
            this.chart.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle1});
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.colorEvt);
            this.panel1.Controls.Add(this.chkDisplayEvent);
            this.panel1.Controls.Add(this.cbxGisCol);
            this.panel1.Controls.Add(this.btnRenderOption);
            this.panel1.Controls.Add(this.btnQuery);
            this.panel1.Controls.Add(this.dtPickerEnd);
            this.panel1.Controls.Add(this.label2);
            this.panel1.Controls.Add(this.dtPickerBegin);
            this.panel1.Controls.Add(this.label3);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(528, 69);
            this.panel1.TabIndex = 2;
            // 
            // colorEvt
            // 
            this.colorEvt.EditValue = System.Drawing.Color.Black;
            this.colorEvt.Location = new System.Drawing.Point(470, 40);
            this.colorEvt.Name = "colorEvt";
            this.colorEvt.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorEvt.Properties.ColorAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colorEvt.Size = new System.Drawing.Size(46, 21);
            this.colorEvt.TabIndex = 8;
            this.colorEvt.EditValueChanged += new System.EventHandler(this.colorEvt_EditValueChanged);
            // 
            // chkDisplayEvent
            // 
            this.chkDisplayEvent.EditValue = true;
            this.chkDisplayEvent.Location = new System.Drawing.Point(357, 41);
            this.chkDisplayEvent.Name = "chkDisplayEvent";
            this.chkDisplayEvent.Properties.Caption = "显示问题点分布";
            this.chkDisplayEvent.Size = new System.Drawing.Size(107, 19);
            this.chkDisplayEvent.TabIndex = 7;
            this.chkDisplayEvent.CheckedChanged += new System.EventHandler(this.chkDisplayEvent_CheckedChanged);
            // 
            // cbxGisCol
            // 
            this.cbxGisCol.Location = new System.Drawing.Point(89, 40);
            this.cbxGisCol.Name = "cbxGisCol";
            this.cbxGisCol.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxGisCol.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxGisCol.Size = new System.Drawing.Size(118, 21);
            this.cbxGisCol.TabIndex = 6;
            // 
            // btnRenderOption
            // 
            this.btnRenderOption.Location = new System.Drawing.Point(238, 38);
            this.btnRenderOption.Name = "btnRenderOption";
            this.btnRenderOption.Size = new System.Drawing.Size(75, 23);
            this.btnRenderOption.TabIndex = 5;
            this.btnRenderOption.Text = "设置";
            this.btnRenderOption.Click += new System.EventHandler(this.btnRenderOption_Click);
            // 
            // btnQuery
            // 
            this.btnQuery.Location = new System.Drawing.Point(441, 11);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.Size = new System.Drawing.Size(75, 23);
            this.btnQuery.TabIndex = 5;
            this.btnQuery.Text = "查询";
            // 
            // dtPickerEnd
            // 
            this.dtPickerEnd.Location = new System.Drawing.Point(238, 12);
            this.dtPickerEnd.Name = "dtPickerEnd";
            this.dtPickerEnd.Size = new System.Drawing.Size(118, 22);
            this.dtPickerEnd.TabIndex = 3;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(213, 16);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(19, 14);
            this.label2.TabIndex = 2;
            this.label2.Text = "至";
            // 
            // dtPickerBegin
            // 
            this.dtPickerBegin.Location = new System.Drawing.Point(89, 12);
            this.dtPickerBegin.Name = "dtPickerBegin";
            this.dtPickerBegin.Size = new System.Drawing.Size(118, 22);
            this.dtPickerBegin.TabIndex = 1;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(17, 43);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(66, 14);
            this.label3.TabIndex = 0;
            this.label3.Text = "GIS渲染列:";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(12, 16);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(71, 14);
            this.label1.TabIndex = 0;
            this.label1.Text = "工单时间段:";
            // 
            // TaskOrderStatForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1284, 662);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "TaskOrderStatForm";
            this.Text = "工单统计";
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chart)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorEvt.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDisplayEvent.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxGisCol.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private GISPanel gisPanel;
        private DevExpress.XtraGrid.GridControl gridCtrl;
        private DevExpress.XtraGrid.Views.Grid.GridView gv;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.DateTimePicker dtPickerEnd;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.DateTimePicker dtPickerBegin;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraEditors.SimpleButton btnQuery;
        private DevExpress.XtraEditors.ComboBoxEdit cbxGisCol;
        private System.Windows.Forms.Label label3;
        private DevExpress.XtraEditors.SimpleButton btnRenderOption;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraEditors.CheckEdit chkDisplayEvent;
        private DevExpress.XtraEditors.ColorEdit colorEvt;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraCharts.ChartControl chart;
    }
}