﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ScanLTEModRoadByFile : ModRoadQueryByFile
    {
        public ScanLTEModRoadByFile(MainModel mm)
            : base(mm, new ScanLTEModRoadQuery(mm))
        {
        }
    }

    public class ScanLTEModRoadByRegion : ModRoadQueryByRegion
    {
        public ScanLTEModRoadByRegion(MainModel mm)
            : base(mm, new ScanLTEModRoadQuery(mm))
        {
        }
    }

    /// <summary>
    /// LTE扫频模干扰分析
    /// 负责功能的流程控制和道路划分
    /// </summary>
    public class ScanLTEModRoadQuery : ModRoadQueryBase
    {
        public ScanLTEModRoadQuery(MainModel mm) : base(mm)
        {
        }

        public override List<string> Columns
        {
            get
            {
                return new List<string>()
                {
                    "isampleid",
                    "itime",
                    "ilongitude",
                    "ilatitude",
                    "LTESCAN_TopN_PSS_RP", 
                    "LTESCAN_TopN_EARFCN",
                    "LTESCAN_TopN_PCI",
                    "LTESCAN_TopN_CELL_Specific_RSRP",
                };
            }
        }

        public override string Name
        {
            get { return "LTE模三干扰道路"; }
        }

        public override ModRoadStaterBase Stater
        {
            get { return this.stater; }
        }

        public override bool isValidCondition()
        {
            if (setForm == null || setForm.IsDisposed)
            {
                setForm = new ScanLTEModRoadSettingForm();
            }
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }

            cond = setForm.GetCondition();
            stater = new ScanLTEModRoadStater(mainModel, cond);
            curRoad = null;
            return true;
        }

        public override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23003, "LTE模三干扰道路");//////
        }

        public override void FireShowForm()
        {
            mainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");

            resultForm = mainModel.GetObjectFromBlackboard(typeof(ScanLTEModRoadResultForm).FullName) as ScanLTEModRoadResultForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new ScanLTEModRoadResultForm(mainModel);
            }
            resultForm.FillData(stater);
            if (!resultForm.Visible)
            {
                resultForm.Show(mainModel.MainForm);
            }
        }

        protected override ModRoadItemBase CreateNewRoad(TestPoint firstPoint, string fileName)
        {
            return new ScanLTEModRoadExtent(firstPoint, fileName, cond);
        }

        private ScanLTEModRoadResultForm resultForm { get; set; }
        private ScanLTEModRoadSettingForm setForm;
        private ScanLTEModRoadCondition cond;
        private ScanLTEModRoadStater stater;
    }
}
