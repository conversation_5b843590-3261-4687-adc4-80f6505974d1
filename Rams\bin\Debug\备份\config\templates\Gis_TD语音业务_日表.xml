<?xml version="1.0"?>
<Configs>
  <Config name="StatReports">
    <Item name="reports" typeName="IList">
      <Item typeName="ReporterTemplate">
        <Item name="Param" typeName="IDictionary">
          <Item typeName="String" key="Name">TD语音业务_日表</Item>
          <Item typeName="Int32" key="KeyCount">2</Item>
          <Item typeName="Int32" key="TimeShowType">0</Item>
          <Item typeName="IList" key="Columns">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">所属项目</Item>
              <Item typeName="String" key="Exp">{kProjId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">所属项目</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">日期</Item>
              <Item typeName="String" key="Exp">{kTimeValue}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">日期</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">总测试距离(公里)</Item>
              <Item typeName="String" key="Exp">{Tx_0806/1000}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">总测试距离(公里)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">总测试时长(分钟)</Item>
              <Item typeName="String" key="Exp">{Tx_0805/(60*1000)}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">总测试时长(分钟)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均时速(公里/小时)</Item>
              <Item typeName="String" key="Exp">{Tx_0806/Tx_0805}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD网占用时长(分钟)</Item>
              <Item typeName="String" key="Exp">{Tx_0825/(60*1000)}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD网占用时长(分钟)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM网占用时长(分钟)</Item>
              <Item typeName="String" key="Exp">{Tx_0823/(60*1000)}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">GSM网占用时长(分钟)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD网时长占比(%)</Item>
              <Item typeName="String" key="Exp">{100.0*Tx_0825/(Tx_0825+Tx_0823) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD网时长占比(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM网时长占比(%)</Item>
              <Item typeName="String" key="Exp">{100.0*Tx_0823/(Tx_0825+Tx_0823) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">GSM网时长占比(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD_90覆盖率(%)</Item>
              <Item typeName="String" key="Exp">{100*Tx_5C04030E/Tx_5C04030A }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD_90覆盖率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD_95覆盖率(%)</Item>
              <Item typeName="String" key="Exp">{100*Tx_5C040311/Tx_5C04030A }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD_95覆盖率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD_GSM_90覆盖率(%)</Item>
              <Item typeName="String" key="Exp">{100*(Tx_640408+Tx_640409+Tx_64040A+Tx_640417)/Tx_640401}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD_GSM_90覆盖率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD起呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[100]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD起呼次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD主叫未接通次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[104]+evtIdCount[188] }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD主叫未接通次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD主叫转GSM未接通次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[200]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD主叫转GSM未接通次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD主叫接通率(%)</Item>
              <Item typeName="String" key="Exp">{100.0*(evtIdCount[100]-(evtIdCount[104]+evtIdCount[200]) )/evtIdCount[100] }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD主叫接通率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD主叫掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[105]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD主叫掉话次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD主叫转GSM掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[198]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD主叫转GSM掉话次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD掉话率(%)</Item>
              <Item typeName="String" key="Exp">{100.0*(evtIdCount[105]+evtIdCount[117]+evtIdCount[198]+evtIdCount[199])/(evtIdCount[101]+evtIdCount[113]+evtIdCount[196]+evtIdCount[197]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD掉话率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM起呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[106]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">GSM起呼次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM主叫未接通次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[110]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">GSM主叫未接通次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM主叫掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[111]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">GSM主叫掉话次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM掉话率(%)</Item>
              <Item typeName="String" key="Exp">{100.0*(evtIdCount[111]+evtIdCount[123])/(evtIdCount[107]+evtIdCount[119]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">GSM掉话率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD全网接通率(%)</Item>
              <Item typeName="String" key="Exp">{100.0*(evtIdCount[100]+(evtIdCount[106]-(evtIdCount[104]+evtIdCount[110]+evtIdCount[200] ) ))/(evtIdCount[100]+evtIdCount[106] )}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD全网接通率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD全网掉话率(%)</Item>
              <Item typeName="String" key="Exp">{100.0*(evtIdCount[105]+evtIdCount[111]+evtIdCount[117]+evtIdCount[123]+evtIdCount[198]+evtIdCount[199])/(evtIdCount[101]+evtIdCount[107]+evtIdCount[113]+evtIdCount[119]+evtIdCount[196]+evtIdCount[197]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD全网掉话率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD网内切换成功率(%)</Item>
              <Item typeName="String" key="Exp">{100.0*(evtIdCount[144]+evtIdCount[147] )/(evtIdCount[144]+evtIdCount[145]+evtIdCount[147]+evtIdCount[148]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD网内切换成功率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM网内切换成功率(%)</Item>
              <Item typeName="String" key="Exp">{100.0*evtIdCount[150]/(evtIdCount[150]+evtIdCount[151]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">GSM网内切换成功率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">T2G切换成功率(%)</Item>
              <Item typeName="String" key="Exp">{100.0*evtIdCount[141]/(evtIdCount[141]+evtIdCount[142]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">T2G切换成功率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">全网切换成功率(%)</Item>
              <Item typeName="String" key="Exp">{100.0*(evtIdCount[141]+evtIdCount[144]+evtIdCount[147]+evtIdCount[150])/(evtIdCount[141]+evtIdCount[144]+evtIdCount[147]+evtIdCount[150]+evtIdCount[142]+evtIdCount[145]+evtIdCount[148]+evtIdCount[151]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">全网切换成功率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">切换比例(T网内)(%)</Item>
              <Item typeName="String" key="Exp">{100.0*(evtIdCount[144]+evtIdCount[147])/(evtIdCount[141]+evtIdCount[144]+evtIdCount[147]+evtIdCount[150]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">切换比例(T网内)(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">切换比例(G网内)(%)</Item>
              <Item typeName="String" key="Exp">{100.0*evtIdCount[150]/(evtIdCount[141]+evtIdCount[144]+evtIdCount[147]+evtIdCount[150]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">切换比例(G网内)(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">切换比例(T-&gt;G)(%)</Item>
              <Item typeName="String" key="Exp">{100.0*evtIdCount[141]/(evtIdCount[141]+evtIdCount[144]+evtIdCount[147]+evtIdCount[150]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">切换比例(T-&gt;G)(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD-GSM切换前5秒TD电平平均值</Item>
              <Item typeName="String" key="Exp">{value6[141]/evtIdCount[141] }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD-GSM切换前5秒TD电平平均值</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD-GSM切换后5秒GSM电平平均值</Item>
              <Item typeName="String" key="Exp">{value7[141]/evtIdCount[141] }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD-GSM切换后5秒GSM电平平均值</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD网内重选成功次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[178]-evtIdCount[179]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD网内重选成功次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM网内重选成功次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[180]-evtIdCount[181]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">GSM网内重选成功次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">T2G网内重选成功次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[136]-evtIdCount[137]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">T2G网内重选成功次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">G2T网内重选成功次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[138]-evtIdCount[139]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">G2T网内重选成功次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">T2G重选前5秒TD电平平均值</Item>
              <Item typeName="String" key="Exp">{value6[136]/evtIdCount[136] }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">T2G重选前5秒TD电平平均值</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">T2G重选后5秒GSM电平平均值</Item>
              <Item typeName="String" key="Exp">{value7[136]/evtIdCount[136] }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">T2G重选后5秒GSM电平平均值</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">T2G重选时延</Item>
              <Item typeName="String" key="Exp">{value5[136]/evtIdCount[136] }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">T2G重选时延</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">G2T重选前5秒GSM电平平均值</Item>
              <Item typeName="String" key="Exp">{value6[138]/evtIdCount[138] }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">G2T重选前5秒GSM电平平均值</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">G2T重选后5秒TD电平平均值</Item>
              <Item typeName="String" key="Exp">{value7[138]/evtIdCount[138] }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">G2T重选后5秒TD电平平均值</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">G2T重选时延</Item>
              <Item typeName="String" key="Exp">{value5[138]/evtIdCount[138] }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">G2T重选时延</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD网内位置更新成功次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[125]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD网内位置更新成功次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">TD网内位置更新失败次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[126]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">TD网内位置更新失败次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM网内位置更新成功次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[128]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">GSM网内位置更新成功次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">GSM网内位置更新失败次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[129]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">GSM网内位置更新失败次数</Item>
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>