﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRDropCallAnaBase : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        protected NRDropCallAnaBase()
            : base(MainModel.GetInstance())
        {
            FilterSampleByRegion = true;
            IncludeMessage = true;

            Columns = NRTpHelper.InitBaseReplayParamBackground(false, false);
            Columns.Add("NR_lte_RSRP");
            Columns.Add("NR_lte_SINR");

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NRVoice);
        }

        readonly NRDropCallAnaHelper helper = new NRDropCallAnaHelper();

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35039, Name);
        }

        protected override bool getCondition()
        {
            NRDropCallAnaDlg dlg = new NRDropCallAnaDlg();
            dlg.SetCondition(dropCallCond);
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            dropCallCond = dlg.GetCondition();
            dropCalls = new List<NRDropCallInfo>();
            return true;
        }

        protected override void queryTimePeriodInfo(ClientProxy clientProxy, Package package, TimePeriod period, bool byRound)
        {//只查询含掉话事件的文件
            condition.EventIDs = new List<int>();
            condition.EventIDs.AddRange(helper.MoCallDropEventIds);
            condition.EventIDs.AddRange(helper.MtCallDropEventIds);
            condition.FilterOffValue9 = true;
            base.queryTimePeriodInfo(clientProxy, package, period, byRound);
        }

        #region
        /// <summary>
        /// 先进行主被叫关联
        /// </summary>
        protected override void analyseFiles()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = getMoMtPair();

            try
            {
                clearDataBeforeAnalyseFiles();
                int iloop = 0;
                foreach (KeyValuePair<FileInfo, FileInfo> pair in moMtPair)
                {
                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + moMtPair.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / moMtPair.Count);

                    condition.FileInfos.Clear();
                    if (pair.Key != null)
                    {
                        condition.FileInfos.Add(pair.Key);
                    }
                    if (pair.Value != null)
                    {
                        condition.FileInfos.Add(pair.Value);
                    }
                    replay();
                    condition.FileInfos.Clear();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        private Dictionary<FileInfo, FileInfo> getMoMtPair()
        {
            Dictionary<FileInfo, FileInfo> moMtPair = new Dictionary<FileInfo, FileInfo>();
            Dictionary<int, bool> fileAdded = new Dictionary<int, bool>();
            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0
                   && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                {
                    continue;
                }
                if (fileAdded.ContainsKey(fileInfo.ID))
                {
                    continue;
                }
                fileAdded[fileInfo.ID] = true;
                if (fileInfo.EventCount == 0)
                {
                    moMtPair[fileInfo] = null;
                }
                else
                {
                    FileInfo mtFile = MainModel.FileInfos.Find(x => x.ID == fileInfo.EventCount);
                    if (mtFile == null)
                    {
                        mtFile = new FileInfo();
                        mtFile.ID = fileInfo.EventCount;
                        mtFile.LogTable = fileInfo.LogTable;
                    }
                    fileAdded[mtFile.ID] = true;
                    moMtPair[fileInfo] = mtFile;
                }
            }

            return moMtPair;
        }

        protected override void doStatWithQuery()
        {
            DTFileDataManager moFile = null;
            DTFileDataManager mtFile = null;
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file.MoMtFlag == (int)MoMtFile.MoFlag)
                {
                    moFile = file;
                }
                else if (file.MoMtFlag == (int)MoMtFile.MtFlag)
                {
                    mtFile = file;
                }
            }

            List<NRDropCallAnaInfo> moCalls = getNRDropCallAnaInfos(moFile, helper.MoCallAttemptEventIds
                , helper.MoCallOverEventIds, helper.MoCallDropEventIds);

            List<NRDropCallAnaInfo> mtCalls = getNRDropCallAnaInfos(mtFile, helper.MtCallAttemptEventIds
                , helper.MtCallOverEventIds, helper.MtCallDropEventIds);

            anaBothSideFiles(moCalls, mtCalls);
        }

        private List<NRDropCallAnaInfo> getNRDropCallAnaInfos(DTFileDataManager dtFile, List<int> callAttemptEventIds
            , List<int> callOverEventIds, List<int> callDropEventIds)
        {
            List<NRDropCallAnaInfo> NRDropCallAnaInfoList = new List<NRDropCallAnaInfo>();
            if (dtFile == null)
            {
                return NRDropCallAnaInfoList;
            }

            string strMoMtDesc = dtFile.GetMoMtDesc();
            NRDropCallAnaInfo curCall = null;
            NRDropCallAnaInfo lastCall = null;
            foreach (DTData data in dtFile.DTDatas)
            {
                if (data is TestPoint)
                {
                    doWithTestPoint(curCall, data);
                }
                else if (data is Message)
                {
                    doWithMessage(curCall, data);
                }
                else if (data is Event)
                {
                    Event evt = data as Event;
                    if (callAttemptEventIds.Contains(evt.ID))
                    {
                        doWithCallAttemptEvent(NRDropCallAnaInfoList, strMoMtDesc, evt, ref curCall, ref lastCall);
                    }
                    else if (callOverEventIds.Contains(evt.ID))
                    {
                        doWithCallOverEvent(callDropEventIds, evt, ref curCall, lastCall);
                    }
                    else if (curCall != null)
                    {
                        curCall.Events.Add(evt);
                    }
                }
            }

            return NRDropCallAnaInfoList;
        }

        private void doWithCallOverEvent(List<int> callDropEventIds, Event evt
            , ref NRDropCallAnaInfo curCall, NRDropCallAnaInfo lastCall)
        {
            if (curCall != null)
            {
                bool needAdd = true;
                if (callDropEventIds.Contains(evt.ID))
                {
                    bool filter = int.Parse(evt["Value9"].ToString()) == -1;
                    if (lastCall != null && evt.DateTime == curCall.BeginTime)
                    {
                        //drop time与attempt time一致，则该drop call属于上一次通话结果
                        needAdd = false;
                        lastCall.IsDropCall = true;
                        lastCall.DropEvt = evt;
                        lastCall.DropTime = evt.DateTime;
                        lastCall.DropTimeDesc = evt.DateTimeStringWithMillisecond;
                        lastCall.IsFilter = filter;
                    }
                    else
                    {
                        curCall.DropEvt = evt;
                        curCall.IsDropCall = true;
                        curCall.DropTime = evt.DateTime;
                        curCall.DropTimeDesc = evt.DateTimeStringWithMillisecond;
                        curCall.IsFilter = filter;
                    }
                }
                if (needAdd)
                {
                    curCall.Events.Add(evt);
                    curCall.EndTime = evt.DateTime;
                    curCall = null;
                }
            }
        }

        private void doWithCallAttemptEvent(List<NRDropCallAnaInfo> NRDropCallAnaInfoList, string strMoMtDesc, Event evt
            , ref NRDropCallAnaInfo curCall, ref NRDropCallAnaInfo lastCall)
        {
            lastCall = curCall;
            if (curCall != null)
            {
                curCall.EndTime = evt.DateTime;
            }
            curCall = new NRDropCallAnaInfo();
            curCall.FileName = evt.FileName;
            curCall.MoMtDesc = strMoMtDesc;
            curCall.BeginTime = evt.DateTime;
            curCall.Events.Add(evt);
            NRDropCallAnaInfoList.Add(curCall);
        }

        private void doWithTestPoint(NRDropCallAnaInfo curCall, DTData data)
        {
            TestPoint tp = data as TestPoint;
            if (tp != null && curCall != null)
            {
                curCall.TestPoints.Add(tp);
            }
        }

        private void doWithMessage(NRDropCallAnaInfo curCall, DTData data)
        {
            Message msg = data as Message;
            if (msg != null && curCall != null)
            {
                curCall.Messages.Add(msg);
            }
        }
        
        private void anaBothSideFiles(List<NRDropCallAnaInfo> moCalls
             , List<NRDropCallAnaInfo> mtCalls)
        {
            int lastCallIdx = 0;
            for (int i = 0; i < moCalls.Count; i++)
            {
                NRDropCallAnaInfo moCall = moCalls[i];
                if (moCall.IsDropCall && !moCall.IsFilter)
                {
                    lastCallIdx = setOtherSideCall(lastCallIdx, moCall, mtCalls);
                    saveDropCall(moCall);
                }
            }

            lastCallIdx = 0;
            for (int i = 0; i < mtCalls.Count; i++)
            {
                NRDropCallAnaInfo mtCall = mtCalls[i];
                if (mtCall.IsDropCall && !mtCall.IsFilter && mtCall.OtherSideCall == null)
                {
                    lastCallIdx = setOtherSideCall(lastCallIdx, mtCall, moCalls);
                    saveDropCall(mtCall);
                }
            }
        }

        private int setOtherSideCall(int lastCallIdx, NRDropCallAnaInfo call, List<NRDropCallAnaInfo> otherSideCalls)
        {
            for (int j = lastCallIdx; j < otherSideCalls.Count; j++)
            {
                NRDropCallAnaInfo otherSideCall = otherSideCalls[j];
                if (call.BeginTime > otherSideCall.BeginTime
                    && (call.BeginTime - otherSideCall.BeginTime).TotalSeconds < 15)
                {
                    call.OtherSideCall = otherSideCall;
                    lastCallIdx = j;
                    if (!otherSideCall.IsDropCall)
                    {
                        otherSideCall.DropTime = call.DropTime;
                        otherSideCall.DropTimeDesc = call.DropTimeDesc;
                    }
                    break;
                }
            }

            return lastCallIdx;
        }

        NRDropCallAnaCondtion dropCallCond;
        private List<NRDropCallInfo> dropCalls = new List<NRDropCallInfo>();
        private void saveDropCall(NRDropCallAnaInfo call)
        {
            call.Evaluate(dropCallCond, true);
            int sn = dropCalls.Count + 1;
            NRDropCallInfo dropCall = new NRDropCallInfo(sn, call);
            dropCalls.Add(dropCall);
        }
        #endregion

        protected override void fireShowForm()
        {
            if (dropCalls == null || dropCalls.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有掉话。");
                return;
            }

            NRDropCallAnaForm frm = MainModel.CreateResultForm(typeof(NRDropCallAnaForm)) as NRDropCallAnaForm;
            frm.FillData(dropCalls, dropCallCond);
            frm.Visible = true;
            frm.BringToFront();
            dropCalls = null;
        }
    }

    public class NRDropCallAnaByFile : NRDropCallAnaBase
    {
        private NRDropCallAnaByFile()
            : base()
        {
        }

        private static NRDropCallAnaByFile instance = null;
        public static NRDropCallAnaByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRDropCallAnaByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "掉话分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }

    public class NRDropCallAnaByRegion : NRDropCallAnaBase
    {
        protected NRDropCallAnaByRegion()
            : base()
        {
        }

        private static NRDropCallAnaByRegion instance = null;
        public static NRDropCallAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRDropCallAnaByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "掉话分析(按区域)"; }
        }
    }
}
