﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteMultiCompareAutoSet
    {
        public string ReprortSavePath { get; set; } = "C:\\Users\\<USER>\\Desktop";
        public bool IsFilterFile { get; set; }
        public string StrFileNameFilter { get; set; }
        public double DistanceEvt { get; set; }
        public bool IsAnaMessageCellInfo { get; set; }
        
        public List<TimePeriod> Periods { get; set; } = new List<TimePeriod>();
        public List<int> SelectEvtIDSet { get; set; } = new List<int>();
        public List<int> DistrictIDSet { get; set; } = new List<int>();
        public List<int> ProjectIDSet { get; set; } = new List<int>();
        public List<int> ServiceIDSet { get; set; } = new List<int>();
        public List<MsgParamSetting> MsgParamList { get; set; } = new List<MsgParamSetting>();

        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();

                List<object> objTimePeriodList = new List<object>();
                foreach (TimePeriod period in this.Periods)
                {
                    objTimePeriodList.Add(period.Param);
                }
                paramDic["Periods"] = objTimePeriodList;

                List<object> objMsgParamList = new List<object>();
                foreach (MsgParamSetting msgParam in this.MsgParamList)
                {
                    objMsgParamList.Add(msgParam.Param);
                }
                paramDic["MsgParamList"] = objMsgParamList;

                paramDic["ReprortSavePath"] = this.ReprortSavePath;
                paramDic["IsFilterFile"] = this.IsFilterFile;
                paramDic["StrFileNameFilter"] = this.StrFileNameFilter;
                paramDic["DistanceEvt"] = this.DistanceEvt;
                paramDic["IsAnaMessageCellInfo"] = this.IsAnaMessageCellInfo;
                paramDic["SelectEvtIDSet"] = this.SelectEvtIDSet;
                paramDic["DistrictIDSet"] = this.DistrictIDSet;
                paramDic["ProjectIDSet"] = this.ProjectIDSet;
                paramDic["ServiceIDSet"] = this.ServiceIDSet;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                setPeriods(value);

                setMsgParamList(value);

                setReprortSavePath(value);

                setDistanceEvt(value);

                setIsAnaMessageCellInfo(value);

                setIsFilterFile(value);

                setStrFileNameFilter(value);

                setSelectEvtIDSet(value);

                setProjectIDSet(value);

                setServiceIDSet(value);

                setDistrictIDSet(value);
            }
        }

        private void setPeriods(Dictionary<string, object> value)
        {
            if (value.ContainsKey("Periods"))
            {
                List<object> periodList = value["Periods"] as List<object>;
                Periods.Clear();
                foreach (object pd in periodList)
                {
                    TimePeriod period = new TimePeriod();
                    period.Param = pd as Dictionary<string, object>;
                    Periods.Add(period);
                }
            }
        }

        private void setMsgParamList(Dictionary<string, object> value)
        {
            if (value.ContainsKey("MsgParamList"))
            {
                List<object> msgParams = value["MsgParamList"] as List<object>;
                MsgParamList.Clear();
                foreach (object pm in msgParams)
                {
                    Dictionary<string, object> dic = pm as Dictionary<string, object>;
                    string strKey = "";
                    if (dic.ContainsKey("ParamNameDes") && dic["ParamNameDes"] != null)
                    {
                        strKey = dic["ParamNameDes"].ToString();
                    }
                    MsgParamSetting msgParam = new MsgParamSetting(false, strKey);
                    msgParam.Param = dic;
                    MsgParamList.Add(msgParam);
                }
            }
        }

        private void setReprortSavePath(Dictionary<string, object> value)
        {
            if (value.ContainsKey("ReprortSavePath") && value["ReprortSavePath"] != null)
            {
                this.ReprortSavePath = value["ReprortSavePath"].ToString();
            }
        }

        private void setDistanceEvt(Dictionary<string, object> value)
        {
            if (value.ContainsKey("DistanceEvt") && value["DistanceEvt"] != null)
            {
                this.DistanceEvt = (double)value["DistanceEvt"];
            }
        }

        private void setIsAnaMessageCellInfo(Dictionary<string, object> value)
        {
            if (value.ContainsKey("IsAnaMessageCellInfo") && value["IsAnaMessageCellInfo"] != null)
            {
                this.IsAnaMessageCellInfo = (bool)value["IsAnaMessageCellInfo"];
            }
        }

        private void setIsFilterFile(Dictionary<string, object> value)
        {
            if (value.ContainsKey("IsFilterFile"))
            {
                this.IsFilterFile = (bool)value["IsFilterFile"];
            }
        }

        private void setStrFileNameFilter(Dictionary<string, object> value)
        {
            if (value.ContainsKey("StrFileNameFilter") && value["StrFileNameFilter"] != null)
            {
                this.StrFileNameFilter = value["StrFileNameFilter"].ToString();
            }
        }

        private void setSelectEvtIDSet(Dictionary<string, object> value)
        {
            if (value.ContainsKey("SelectEvtIDSet"))
            {
                object objEvts = value["SelectEvtIDSet"];
                if (objEvts is List<object>)
                {
                    SelectEvtIDSet.Clear();
                    foreach (int id in objEvts as List<object>)
                    {
                        SelectEvtIDSet.Add(id);
                    }
                }
                else if (objEvts is List<int>)
                {
                    SelectEvtIDSet = value["SelectEvtIDSet"] as List<int>;
                }
            }
        }

        private void setProjectIDSet(Dictionary<string, object> value)
        {
            if (value.ContainsKey("ProjectIDSet"))
            {
                object objProjects = value["ProjectIDSet"];
                if (objProjects is List<object>)
                {
                    ProjectIDSet.Clear();
                    foreach (int id in objProjects as List<object>)
                    {
                        ProjectIDSet.Add(id);
                    }
                }
                else if (objProjects is List<int>)
                {
                    ProjectIDSet = value["ProjectIDSet"] as List<int>;
                }
            }
        }

        private void setServiceIDSet(Dictionary<string, object> value)
        {
            if (value.ContainsKey("ServiceIDSet"))
            {
                object objService = value["ServiceIDSet"];
                if (objService is List<object>)
                {
                    ServiceIDSet.Clear();
                    foreach (int id in objService as List<object>)
                    {
                        ServiceIDSet.Add(id);
                    }
                }
                else if (objService is List<int>)
                {
                    ServiceIDSet = value["ServiceIDSet"] as List<int>;
                }
            }
        }

        private void setDistrictIDSet(Dictionary<string, object> value)
        {
            if (value.ContainsKey("DistrictIDSet"))
            {
                object objDistrict = value["DistrictIDSet"];
                if (objDistrict is List<object>)
                {
                    DistrictIDSet.Clear();
                    foreach (int id in objDistrict as List<object>)
                    {
                        DistrictIDSet.Add(id);
                    }
                }
                else if (objDistrict is List<int>)
                {
                    DistrictIDSet = value["DistrictIDSet"] as List<int>;
                }
            }
        }
    }
    public class LteMultiCompareCfgManager
    {
        private static LteMultiCompareCfgManager instance = null;
        private LteMultiCompareCfgManager()
        {
            cfgFileName = System.Windows.Forms.Application.StartupPath + "/config/AutoLteMultiCompareSets.xml";
            LoadCfg();
        }
        public static LteMultiCompareCfgManager GetInstance()
        {
            if (instance == null)
            {
                instance = new LteMultiCompareCfgManager();
            }
            return instance;
        }

        private LteMultiCompareAutoSet autoSet = new LteMultiCompareAutoSet();
        public LteMultiCompareAutoSet AutoSet
        {
            get
            {
                if (autoSet == null)
                {
                    autoSet = new LteMultiCompareAutoSet();
                }
                return autoSet;
            }
            set
            {
                autoSet = value;
            }
        }
        private object cfgParam
        {
            get
            {
                return autoSet.CfgParam;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                autoSet.CfgParam = value as Dictionary<string, object>;
            }
        }

        private readonly string cfgFileName;
        public void LoadCfg()
        {
            if (System.IO.File.Exists(cfgFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(cfgFileName);
                cfgParam = configFile.GetItemValue("MultiCompareCfg", "Set") as Dictionary<string, object>;
            }
        }
        public void Save()
        {
            MasterCom.Util.XmlConfigFile xmlFile = new MasterCom.Util.XmlConfigFile();
            XmlElement cfgE = xmlFile.AddConfig("MultiCompareCfg");
            xmlFile.AddItem(cfgE, "Set", this.cfgParam);
            xmlFile.Save(cfgFileName);
        }
    }
}
