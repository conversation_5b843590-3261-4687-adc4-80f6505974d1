﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.CheckCellOccupation
{
    public partial class CheckCellOccupationResultForm : MinCloseForm
    {
        public CheckCellOccupationResultForm()
        {
            InitializeComponent();

            ToolStripMenuItem item = new ToolStripMenuItem("导出到Excel");
            item.Click += item_Click;
            contextMenuStrip1.Items.Add(item);
            this.gridControlResult.ContextMenuStrip = contextMenuStrip1;
            this.gridControlResult.MouseDoubleClick += gridControlResult_MouseDoubleClick;
        }

        void gridControlResult_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (this.gridView1.SelectedRowsCount <= 0)
            {
                return;
            }
            Result item = this.gridView1.GetRow(this.gridView1.GetSelectedRows()[0]) as Result;
            if (item == null)
            {
                return;
            }
            MainModel.GetInstance().DTDataManager.Clear();
            foreach (TestPoint tp in item.cellData.listValidTp)
            {
                MainModel.GetInstance().DTDataManager.Add(tp);
            } 
            //画平均经纬度点和相关连线
            MapForm mf = MainModel.GetInstance().MainForm.GetMapForm();
            //获取图层，是的该类型的图层实例被生成并存储在系统的图层队列中，重绘时即会生效
            CheckCellOccupationLayer layer = mf.GetLayerBase(typeof(CheckCellOccupationLayer)) as CheckCellOccupationLayer;
            layer.SetData(item.cellData);
            MainModel.SelectedLTECell = item.cellData.mainCell;
            MainModel.SelectedLTECells.Clear();
            MainModel.SelectedLTECells.Add(item.cellData.mainCell);
            MainModel.GetInstance().FireDTDataChanged(this);
            MainModel.FireSetDefaultMapSerialTheme("lte_RSRP");
        }

        void item_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(gridView1);
        }

        public void FillData(List<Result> listResult)
        {
            this.gridControlResult.DataSource = listResult;
            this.gridControlResult.Refresh();
        }
         
    }
}
