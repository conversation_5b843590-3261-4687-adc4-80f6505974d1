﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTLTESINR
{

    public class ResultModel
    {
        public ResultModel(int opre, float extremum, string kpiname,int carrierType)
        {
            sinrModel = new SINRModel(opre, extremum, kpiname);
            totalModel = new SINRModel();
            CarrierType = carrierType;
        }
        /// <summary>
        /// 存储满足SINR条件的数据
        /// </summary>
        public SINRModel sinrModel { get; set; }
        /// <summary>
        /// 存储总的数据
        /// </summary>
        public SINRModel totalModel { get; set; }
        public int CarrierType { get; set; }
        public string CarrierName
        {
            get
            {
                if (CarrierType == 1)
                {
                    return "移动";
                }else if (CarrierType == 2)
                {
                    return "联通";
                }
                else
                {
                    return "电信";
                }               
            }
        }
    }

    public class SINRModel
    {
        public SINRModel(int opre, float extremum,string kpiname)
        {
            sinrExtremum = extremum;
            Items = new List<ItemModel>();
            KPIName = kpiname;
        }
        public SINRModel()
        {
            Items = new List<ItemModel>();
            opre = -1;
        }
        
        private readonly int opre;
        public string Name
        {
            get 
            {
                if (opre != -1)
                {
                    return "SINR" + getType(opre) + sinrExtremum + "采样点";
                }
                else
                {
                    return "SINR总采样点";
                }
            }
        }
        /// <summary>
        /// 条件极值
        /// </summary>
        private readonly float sinrExtremum;
        public float SINRExtremum
        {
            get { return sinrExtremum; }
        }
        public string KPIName { get; set; }
        public List<ItemModel> Items { get; set; }

        private string getType(int type)
        {
            switch (type)
            {
                case 0:
                    return "<";
                case 1:
                    return ">";
                default:
                    return "";
            }
        }
    } 

    public class ItemModel
    {
        public ItemModel(int opreType, float extremumFirst, float extremumSecond,string kpiName)
        {
            this.operType = opreType;
            this.extremumFirst = extremumFirst;
            this.extremumSecond = extremumSecond;
            this.KPIName = kpiName;
        }
        public ItemModel()
        {
        }
        //条件符号
        private readonly int operType;
        public int OperType
        {
            get { return operType; }
        }
        //条件极值1
        private readonly float extremumFirst;
        public float ExtremumFirst
        {
            get { return extremumFirst; }
        }
        //条件极值2
        private readonly float extremumSecond;
        public float? ExtremumSecond
        {
            get { return extremumSecond; }
        }
        private readonly string KPIName;
        //满足条件的采样点数
        public int PointCount { get; set; }

        public string getType()
        {
            switch (operType)
            {
                case 0:
                    return "<";
                case 1:
                    return ">";
                case 3:
                    return "≤";
                default:
                    return "";
            }
        }

        public string Name
        {
            get
            {
                if (operType == (int)EnumOperatorsType.GTAndLT)
                {
                    return ExtremumFirst + "<" + KPIName + "≤" + ExtremumSecond;
                }
                else
                {
                    return KPIName + getType() + ExtremumFirst;
                }
            }
        }
    }
}
