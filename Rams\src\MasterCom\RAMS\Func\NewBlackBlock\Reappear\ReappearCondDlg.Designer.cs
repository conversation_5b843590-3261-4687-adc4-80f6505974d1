﻿namespace MasterCom.RAMS.NewBlackBlock
{
    partial class ReappearCondDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.numRadius = new System.Windows.Forms.NumericUpDown();
            this.gbBlackBlockCond = new System.Windows.Forms.GroupBox();
            this.label5 = new System.Windows.Forms.Label();
            this.cbxType = new System.Windows.Forms.ComboBox();
            this.pickerEndTime = new System.Windows.Forms.DateTimePicker();
            this.pickerStartTime = new System.Windows.Forms.DateTimePicker();
            this.gbReappearCond = new System.Windows.Forms.GroupBox();
            this.label4 = new System.Windows.Forms.Label();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            ((System.ComponentModel.ISupportInitialize)(this.numRadius)).BeginInit();
            this.gbBlackBlockCond.SuspendLayout();
            this.gbReappearCond.SuspendLayout();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(224, 55);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(17, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "至";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(27, 25);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(53, 12);
            this.label2.TabIndex = 1;
            this.label2.Text = "黑点类型";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(32, 27);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(71, 12);
            this.label3.TabIndex = 2;
            this.label3.Text = "重现半径 ≤";
            // 
            // numRadius
            // 
            this.numRadius.Location = new System.Drawing.Point(109, 23);
            this.numRadius.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numRadius.Name = "numRadius";
            this.numRadius.Size = new System.Drawing.Size(60, 21);
            this.numRadius.TabIndex = 5;
            this.numRadius.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRadius.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // gbBlackBlockCond
            // 
            this.gbBlackBlockCond.Controls.Add(this.label5);
            this.gbBlackBlockCond.Controls.Add(this.cbxType);
            this.gbBlackBlockCond.Controls.Add(this.pickerEndTime);
            this.gbBlackBlockCond.Controls.Add(this.pickerStartTime);
            this.gbBlackBlockCond.Controls.Add(this.label2);
            this.gbBlackBlockCond.Controls.Add(this.label1);
            this.gbBlackBlockCond.Location = new System.Drawing.Point(12, 12);
            this.gbBlackBlockCond.Name = "gbBlackBlockCond";
            this.gbBlackBlockCond.Size = new System.Drawing.Size(393, 84);
            this.gbBlackBlockCond.TabIndex = 5;
            this.gbBlackBlockCond.TabStop = false;
            this.gbBlackBlockCond.Text = "黑点条件";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(27, 55);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(53, 12);
            this.label5.TabIndex = 5;
            this.label5.Text = "关闭时间";
            // 
            // cbxType
            // 
            this.cbxType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxType.FormattingEnabled = true;
            this.cbxType.Location = new System.Drawing.Point(86, 20);
            this.cbxType.Name = "cbxType";
            this.cbxType.Size = new System.Drawing.Size(169, 20);
            this.cbxType.TabIndex = 4;
            // 
            // pickerEndTime
            // 
            this.pickerEndTime.Location = new System.Drawing.Point(250, 51);
            this.pickerEndTime.Name = "pickerEndTime";
            this.pickerEndTime.Size = new System.Drawing.Size(130, 21);
            this.pickerEndTime.TabIndex = 3;
            // 
            // pickerStartTime
            // 
            this.pickerStartTime.Location = new System.Drawing.Point(86, 51);
            this.pickerStartTime.Name = "pickerStartTime";
            this.pickerStartTime.Size = new System.Drawing.Size(129, 21);
            this.pickerStartTime.TabIndex = 2;
            // 
            // gbReappearCond
            // 
            this.gbReappearCond.Controls.Add(this.label4);
            this.gbReappearCond.Controls.Add(this.label3);
            this.gbReappearCond.Controls.Add(this.numRadius);
            this.gbReappearCond.Location = new System.Drawing.Point(12, 113);
            this.gbReappearCond.Name = "gbReappearCond";
            this.gbReappearCond.Size = new System.Drawing.Size(393, 59);
            this.gbReappearCond.TabIndex = 6;
            this.gbReappearCond.TabStop = false;
            this.gbReappearCond.Text = "重现条件";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(178, 27);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 5;
            this.label4.Text = "米";
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(330, 189);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 7;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(238, 189);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 6;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            // 
            // ReappearCondDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(416, 226);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.gbReappearCond);
            this.Controls.Add(this.gbBlackBlockCond);
            this.MaximizeBox = false;
            this.Name = "ReappearCondDlg";
            this.Text = "黑点重现分析条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numRadius)).EndInit();
            this.gbBlackBlockCond.ResumeLayout(false);
            this.gbBlackBlockCond.PerformLayout();
            this.gbReappearCond.ResumeLayout(false);
            this.gbReappearCond.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numRadius;
        private System.Windows.Forms.GroupBox gbBlackBlockCond;
        private System.Windows.Forms.DateTimePicker pickerStartTime;
        private System.Windows.Forms.DateTimePicker pickerEndTime;
        private System.Windows.Forms.ComboBox cbxType;
        private System.Windows.Forms.GroupBox gbReappearCond;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Label label5;
    }
}