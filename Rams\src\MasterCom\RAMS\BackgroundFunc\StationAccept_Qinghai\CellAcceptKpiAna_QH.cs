﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    class AcpAutoFtpDownloadGood_QH : AcpAutoFtpDownload
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("好点下载") 
                || (fileInfo.Name.Contains("好点") && fileInfo.Name.Contains("DL"));
        }
    }
    class AcpAutoFtpUploadGood_QH : AcpAutoFtpUpload
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("好点上传")
                || (fileInfo.Name.Contains("好点") && fileInfo.Name.Contains("UL"));
        }
    }
    class AcpAutoFtpDownloadBad_QH : AcpAutoFtpDownload
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("差点下载") 
                ||(fileInfo.Name.Contains("差点") && fileInfo.Name.Contains("DL")) ;
        }
        protected override Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.FtpDlRsrpAvg_Bad, kpiCell.AvgRsrp);
            kpiInfos.Add(KpiKey.FtpDlSinrAvg_Bad, kpiCell.AvgSinr);
            kpiInfos.Add(KpiKey.FtpDlSpeedAvg_Bad, kpiCell.AvgDLSpeed);
            kpiInfos.Add(KpiKey.FtpDlCoverPntCount_Valid_Bad, kpiCell.PointCount_RsrpB105sinr6);
            kpiInfos.Add(KpiKey.FtpDlCoverPntCount_Sum_Bad, kpiCell.PointCount_RsrpAndSinr);
            return kpiInfos;
        }
    }
    class AcpAutoFtpUploadBad_QH : AcpAutoFtpDownload
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("差点上传")
                || (fileInfo.Name.Contains("差点") && fileInfo.Name.Contains("UL"));
        }
        protected override Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.FtpUlRsrpAvg_Bad, kpiCell.AvgRsrp);
            kpiInfos.Add(KpiKey.FtpUlSinrAvg_Bad, kpiCell.AvgSinr);
            kpiInfos.Add(KpiKey.FtpUlSpeedAvg_Bad, kpiCell.AvgULSpeed);
            return kpiInfos;
        }
    }
    class AcpAutoCsfbInfo_QH : AcpAutoKpiBase
    {
        readonly List<int> csfbCallRequList = new List<int> { 1001 };
        readonly List<int> csfbCallSuccList = new List<int> { 1004 };
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToLower().Contains("csfb");
        }
        public override Dictionary<KpiKey, object> GetFileKpiInfos(FileInfo fileInfo
            , DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKPI kpiCell = anaFile(fileInfo, fileManager, targetCell);
            if (kpiCell == null)
            {
                reportInfo(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return new Dictionary<KpiKey, object>();
            }

            return getKpiInfos(kpiCell);
        }
        protected Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.CsfbRequestCnt, kpiCell.CsfbCallRequestCnt);
            kpiInfos.Add(KpiKey.CsfbSucceedCnt, kpiCell.CsfbCallSucceedCnt);
            kpiInfos.Add(KpiKey.CsfbReturnToLteRequestCnt, kpiCell.ReturnToLteRequestCnt);
            kpiInfos.Add(KpiKey.CsfbReturnToLteCompleteCnt, kpiCell.ReturnToLteCompleteCnt);
            kpiInfos.Add(KpiKey.CsfbReturnToLteTimeDelay, kpiCell.ReturnTimeDelay);
            return kpiInfos;
        }
        protected CellKPI anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKPI kpiCell = new CellKPI(targetCell.Name);
            foreach (Event evt in fileManager.Events)
            {
                if (csfbCallRequList.Contains(evt.ID))
                {
                    ++kpiCell.CsfbCallRequestCnt;
                }
                else if (csfbCallSuccList.Contains(evt.ID))
                {
                    ++kpiCell.CsfbCallSucceedCnt;
                }
                else if (evt.ID == 883)//Return Back to LTE Request
                {
                    ++kpiCell.ReturnToLteRequestCnt;
                }
                else if (evt.ID == 884)//Return Back to LTE Complete
                {
                    ++kpiCell.ReturnToLteCompleteCnt;

                    long? millSeconds = getLongValue(evt["Value1"]);
                    if (millSeconds != null)
                    {
                        kpiCell.ReturnTimeDelay += ((double)millSeconds / 1000);
                    }
                }
            }
            return kpiCell;
        }

        protected class CellKPI
        {
            public CellKPI(string cellName)
            {
                this.CellName = cellName;
            }
            public string CellName { get; set; }
            public int CsfbCallRequestCnt { get; set; }
            public int CsfbCallSucceedCnt { get; set; }
            public int ReturnToLteRequestCnt { get; set; }
            public int ReturnToLteCompleteCnt { get; set; }

            private double returnTimeDelay = 0;//单位为秒
            public double ReturnTimeDelay
            {
                get { return Math.Round(returnTimeDelay, 3); }
                set { returnTimeDelay = value; }
            }
        }
    }
    class AcpAutoSrvccRate : AcpAutoInnerHandover
    {
        public AcpAutoSrvccRate()
        {
            evtRequList = new List<int> { 1145 };
            evtSuccList = new List<int> { 1146 };
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToLower().Contains("srvcc");
        }

        protected override Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.SrvccCallRequestCnt, kpiCell.RequestCnt);
            kpiInfos.Add(KpiKey.SrvccCallSucceedCnt, kpiCell.SucceedCnt);
            return kpiInfos;
        }
    }
    class AcpAutoVolteAudioRate : AcpAutoKpiBase
    {
        protected List<int> evtCallRequList = new List<int> { 1070 };
        protected List<int> evtCallSuccList = new List<int> { 1074 };
        public AcpAutoVolteAudioRate()
        {
        }
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToLower().Contains("volte语音")
                || (fileInfo.Name.ToLower().Contains("volte") && fileInfo.ServiceType == (int)ServiceType.LTE_TDD_VOLTE);
        }
        public override Dictionary<KpiKey, object> GetFileKpiInfos(FileInfo fileInfo
            , DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKPI kpiCell = anaFile(fileInfo, fileManager, targetCell);
            if (kpiCell == null)
            {
                reportInfo(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return new Dictionary<KpiKey, object>();
            }

            return getKpiInfos(kpiCell);
        }
        protected Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.VolteAudioCallRequestCnt, kpiCell.RequestCnt);
            kpiInfos.Add(KpiKey.VolteAudioCallSucceedCnt, kpiCell.SucceedCnt);
            kpiInfos.Add(KpiKey.VolteAudioInOutHandTotalCnt, kpiCell.InOutHandTotalCnt);
            kpiInfos.Add(KpiKey.VolteAudioInOutHandSucceedCnt, kpiCell.InOutHandSucceedCnt);
            kpiInfos.Add(KpiKey.VolteAudioInHandTotalCnt, kpiCell.InHandTotalCnt);
            kpiInfos.Add(KpiKey.VolteAudioInHandSucceedCnt, kpiCell.InHandSucceedCnt);
            return kpiInfos;
        }
        protected CellKPI anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            CellKPI kpiCell = new CellKPI(targetCell.Name);
            foreach (Event evt in fileManager.Events)
            {
                if (evtCallRequList.Contains(evt.ID))
                {
                    ++kpiCell.RequestCnt;
                }
                else if (evtCallSuccList.Contains(evt.ID))
                {
                    ++kpiCell.SucceedCnt;
                }
                else if (evt.ID == 870 || evt.ID == 1100 //LTE Handover Failure
                    || evt.ID == 851 || evt.ID == 899)   //LTE Handover Success
                {
                    int? srcEarfcn = getIntValue(evt["Value2"]);
                    int? targetEarfcn = getIntValue(evt["Value4"]);
                    if (srcEarfcn != null && targetEarfcn != null)
                    {
                        bool isSuccess = (evt.ID == 851 || evt.ID == 899);//LTE Handover Success
                        addSucceedCnt(kpiCell, srcEarfcn, targetEarfcn, isSuccess);
                    }
                }
            }
            return kpiCell;
        }

        private void addSucceedCnt(CellKPI kpiCell, int? srcEarfcn, int? targetEarfcn, bool isSuccess)
        {
            LTEBandType srcBand = LTECell.GetBandTypeByEarfcn((int)srcEarfcn);
            LTEBandType targetBand = LTECell.GetBandTypeByEarfcn((int)targetEarfcn);
            if (srcBand != targetBand)//不同频段即为室内外切换
            {
                ++kpiCell.InOutHandTotalCnt;
                if (isSuccess)
                {
                    ++kpiCell.InOutHandSucceedCnt;
                }
            }
            else if (targetBand == LTEBandType.E)//E频段为室分小区频段
            {
                ++kpiCell.InHandTotalCnt;
                if (isSuccess)
                {
                    ++kpiCell.InHandSucceedCnt;
                }
            }
        }

        protected class CellKPI
        {
            public CellKPI(string cellName)
            {
                this.CellName = cellName;
            }
            public string CellName { get; set; }
            public int RequestCnt { get; set; }
            public int SucceedCnt { get; set; }
            public int InOutHandTotalCnt { get; set; }
            public int InOutHandSucceedCnt { get; set; }
            public int InHandTotalCnt { get; set; }
            public int InHandSucceedCnt { get; set; }
        }
    }
    class AcpAutoVolteVideoRate : AcpAutoInnerHandover
    {
        public AcpAutoVolteVideoRate()
        {
            evtRequList = new List<int> { 1370 };
            evtSuccList = new List<int> { 1374 };
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToLower().Contains("volte视频")
                || (fileInfo.Name.ToLower().Contains("volte") && fileInfo.ServiceType == (int)ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
        }

        protected override Dictionary<KpiKey, object> getKpiInfos(CellKPI kpiCell)
        {
            Dictionary<KpiKey, object> kpiInfos = new Dictionary<KpiKey, object>();
            kpiInfos.Add(KpiKey.VolteVideoCallRequestCnt, kpiCell.RequestCnt);
            kpiInfos.Add(KpiKey.VolteVideoCallSucceedCnt, kpiCell.SucceedCnt);
            return kpiInfos;
        }
    }
}