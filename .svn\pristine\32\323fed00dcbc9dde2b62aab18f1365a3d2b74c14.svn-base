﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRHandOverArfcnListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.gridControlGeneral = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuGeneral = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripGeneralExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewGeneral = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlDetail = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuDetail = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripDetaiExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewDetail = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand4 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand5 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn14 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.girdColumnTotalBler = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumnBlerRate = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn32 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGeneral)).BeginInit();
            this.ctxMenuGeneral.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGeneral)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlDetail)).BeginInit();
            this.ctxMenuDetail.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewDetail)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControlGeneral
            // 
            this.gridControlGeneral.ContextMenuStrip = this.ctxMenuGeneral;
            this.gridControlGeneral.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode1.RelationName = "Level1";
            this.gridControlGeneral.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControlGeneral.Location = new System.Drawing.Point(0, 0);
            this.gridControlGeneral.MainView = this.gridViewGeneral;
            this.gridControlGeneral.Name = "gridControlGeneral";
            this.gridControlGeneral.Size = new System.Drawing.Size(957, 382);
            this.gridControlGeneral.TabIndex = 0;
            this.gridControlGeneral.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewGeneral});
            // 
            // ctxMenuGeneral
            // 
            this.ctxMenuGeneral.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripGeneralExportExcel});
            this.ctxMenuGeneral.Name = "contextMenuStrip";
            this.ctxMenuGeneral.Size = new System.Drawing.Size(151, 26);
            // 
            // toolStripGeneralExportExcel
            // 
            this.toolStripGeneralExportExcel.Name = "toolStripGeneralExportExcel";
            this.toolStripGeneralExportExcel.Size = new System.Drawing.Size(150, 22);
            this.toolStripGeneralExportExcel.Text = "导出到Excel...";
            this.toolStripGeneralExportExcel.Click += new System.EventHandler(this.ToolStripMenuGeneralExportExcel_Click);
            // 
            // gridViewGeneral
            // 
            this.gridViewGeneral.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridViewGeneral.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewGeneral.Appearance.Row.Options.UseTextOptions = true;
            this.gridViewGeneral.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewGeneral.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4});
            this.gridViewGeneral.GridControl = this.gridControlGeneral;
            this.gridViewGeneral.Name = "gridViewGeneral";
            this.gridViewGeneral.OptionsBehavior.Editable = false;
            this.gridViewGeneral.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "切换方向";
            this.gridColumn1.FieldName = "HnadOverDirection";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 110;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "成功次数";
            this.gridColumn2.FieldName = "HnadOverSuccessTimes";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 110;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "失败次数";
            this.gridColumn3.FieldName = "HnadOverFailTimes";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            this.gridColumn3.Width = 108;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "总次数";
            this.gridColumn4.FieldName = "HnadOverTotalTimes";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            this.gridColumn4.Width = 108;
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(964, 412);
            this.xtraTabControl1.TabIndex = 1;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.gridControlGeneral);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(957, 382);
            this.xtraTabPage1.Text = "概要信息";
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.gridControlDetail);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(957, 382);
            this.xtraTabPage2.Text = "详细信息";
            // 
            // gridControlDetail
            // 
            this.gridControlDetail.ContextMenuStrip = this.ctxMenuDetail;
            this.gridControlDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlDetail.Location = new System.Drawing.Point(0, 0);
            this.gridControlDetail.MainView = this.gridViewDetail;
            this.gridControlDetail.Name = "gridControlDetail";
            this.gridControlDetail.ShowOnlyPredefinedDetails = true;
            this.gridControlDetail.Size = new System.Drawing.Size(957, 382);
            this.gridControlDetail.TabIndex = 0;
            this.gridControlDetail.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewDetail});
            this.gridControlDetail.DoubleClick += new System.EventHandler(this.gridControlDetail_DoubleClick);
            // 
            // ctxMenuDetail
            // 
            this.ctxMenuDetail.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripDetaiExportExcel});
            this.ctxMenuDetail.Name = "contextMenuStripProblem";
            this.ctxMenuDetail.Size = new System.Drawing.Size(151, 26);
            // 
            // toolStripDetaiExportExcel
            // 
            this.toolStripDetaiExportExcel.Name = "toolStripDetaiExportExcel";
            this.toolStripDetaiExportExcel.Size = new System.Drawing.Size(150, 22);
            this.toolStripDetaiExportExcel.Text = "导出到Excel...";
            this.toolStripDetaiExportExcel.Click += new System.EventHandler(this.ToolStripMenuDetailExportExcel_Click);
            // 
            // gridViewDetail
            // 
            this.gridViewDetail.Appearance.BandPanel.Options.UseTextOptions = true;
            this.gridViewDetail.Appearance.BandPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewDetail.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridViewDetail.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewDetail.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridViewDetail.Appearance.Row.Options.UseTextOptions = true;
            this.gridViewDetail.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewDetail.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand4,
            this.gridBand5,
            this.gridBand2});
            this.gridViewDetail.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn13,
            this.gridColumn12,
            this.gridColumn14,
            this.girdColumnTotalBler,
            this.gridColumnBlerRate,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            //this.gridColumn21,
            this.gridColumn32,
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.bandedGridColumn3,
            this.bandedGridColumn4,
            this.bandedGridColumn5
            //, this.bandedGridColumn6
            });
            this.gridViewDetail.GridControl = this.gridControlDetail;
            this.gridViewDetail.Name = "gridViewDetail";
            this.gridViewDetail.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridViewDetail.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridViewDetail.OptionsBehavior.Editable = false;
            this.gridViewDetail.OptionsCustomization.AllowBandMoving = false;
            this.gridViewDetail.OptionsCustomization.AllowBandResizing = false;
            this.gridViewDetail.OptionsCustomization.AllowGroup = false;
            this.gridViewDetail.OptionsFilter.AllowColumnMRUFilterList = false;
            this.gridViewDetail.OptionsFilter.AllowFilterEditor = false;
            this.gridViewDetail.OptionsFilter.AllowMRUFilterList = false;
            this.gridViewDetail.OptionsFilter.ShowAllTableValuesInFilterPopup = true;
            this.gridViewDetail.OptionsFilter.UseNewCustomFilterDialog = true;
            this.gridViewDetail.OptionsSelection.MultiSelect = true;
            this.gridViewDetail.OptionsView.ColumnAutoWidth = false;
            this.gridViewDetail.OptionsView.EnableAppearanceEvenRow = true;
            this.gridViewDetail.OptionsView.EnableAppearanceOddRow = true;
            this.gridViewDetail.OptionsView.ShowGroupPanel = false;
            // 
            // gridBand4
            // 
            this.gridBand4.Caption = "基本信息";
            this.gridBand4.Columns.Add(this.gridColumn6);
            this.gridBand4.Columns.Add(this.gridColumn7);
            this.gridBand4.Columns.Add(this.gridColumn10);
            this.gridBand4.Columns.Add(this.gridColumn11);
            this.gridBand4.Columns.Add(this.gridColumn8);
            this.gridBand4.Columns.Add(this.gridColumn9);
            this.gridBand4.Columns.Add(this.gridColumn13);
            this.gridBand4.Columns.Add(this.gridColumn12);
            this.gridBand4.MinWidth = 50;
            this.gridBand4.Name = "gridBand4";
            this.gridBand4.Width = 629;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "序号";
            this.gridColumn6.FieldName = "SN";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.Width = 45;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "时间";
            this.gridColumn7.FieldName = "DateTime";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.Width = 71;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "经度";
            this.gridColumn10.FieldName = "Longitude";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.Width = 100;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "纬度";
            this.gridColumn11.FieldName = "Latitude";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.Width = 76;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "切换状态";
            this.gridColumn8.FieldName = "HandOverResult";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.Width = 79;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "切换方向";
            this.gridColumn9.FieldName = "HnadOverDirection";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.Width = 85;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "文件名";
            this.gridColumn13.FieldName = "FileName";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.Width = 93;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "是否同站";
            this.gridColumn12.FieldName = "IsSameSite";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.Width = 80;
            // 
            // gridBand5
            // 
            this.gridBand5.Caption = "切换前";
            this.gridBand5.Columns.Add(this.gridColumn14);
            this.gridBand5.Columns.Add(this.girdColumnTotalBler);
            this.gridBand5.Columns.Add(this.gridColumnBlerRate);
            this.gridBand5.Columns.Add(this.gridColumn18);
            this.gridBand5.Columns.Add(this.gridColumn19);
            this.gridBand5.Columns.Add(this.gridColumn20);
            //this.gridBand5.Columns.Add(this.gridColumn21);
            this.gridBand5.MinWidth = 50;
            this.gridBand5.Name = "gridBand5";
            this.gridBand5.Width = 535;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "ARFCN";
            this.gridColumn14.FieldName = "BeforeArfcn";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.Width = 67;
            // 
            // girdColumnTotalBler
            // 
            this.girdColumnTotalBler.Caption = "PCI";
            this.girdColumnTotalBler.FieldName = "BeforePCI";
            this.girdColumnTotalBler.Name = "girdColumnTotalBler";
            this.girdColumnTotalBler.Visible = true;
            this.girdColumnTotalBler.Width = 52;
            // 
            // gridColumnBlerRate
            // 
            this.gridColumnBlerRate.Caption = "小区名";
            this.gridColumnBlerRate.FieldName = "BeforeCellName";
            this.gridColumnBlerRate.Name = "gridColumnBlerRate";
            this.gridColumnBlerRate.Visible = true;
            this.gridColumnBlerRate.Width = 150;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "小区距离";
            this.gridColumn18.FieldName = "BeforeCellDistance";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "RSRP";
            this.gridColumn19.FieldName = "BeforeRsrpAvg";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.Width = 56;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "SINR";
            this.gridColumn20.FieldName = "BeforeSinrAvg";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.Width = 55;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "AppSpeed";
            this.gridColumn21.FieldName = "BeforeAppSpeedAvg";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.Width = 80;
            // 
            // gridBand2
            // 
            this.gridBand2.Caption = "切换后";
            this.gridBand2.Columns.Add(this.gridColumn32);
            this.gridBand2.Columns.Add(this.bandedGridColumn1);
            this.gridBand2.Columns.Add(this.bandedGridColumn2);
            this.gridBand2.Columns.Add(this.bandedGridColumn3);
            this.gridBand2.Columns.Add(this.bandedGridColumn4);
            this.gridBand2.Columns.Add(this.bandedGridColumn5);
            //this.gridBand2.Columns.Add(this.bandedGridColumn6);
            this.gridBand2.MinWidth = 50;
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 540;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "ARFCN";
            this.gridColumn32.FieldName = "AfterArfcn";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.Width = 71;
            // 
            // bandedGridColumn1
            // 
            this.bandedGridColumn1.Caption = "PCI";
            this.bandedGridColumn1.FieldName = "AfterPCI";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.Visible = true;
            this.bandedGridColumn1.Width = 48;
            // 
            // bandedGridColumn2
            // 
            this.bandedGridColumn2.Caption = "小区名";
            this.bandedGridColumn2.FieldName = "AfterCellName";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.Visible = true;
            this.bandedGridColumn2.Width = 150;
            // 
            // bandedGridColumn3
            // 
            this.bandedGridColumn3.Caption = "小区距离";
            this.bandedGridColumn3.FieldName = "AfterCellDistance";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.Visible = true;
            // 
            // bandedGridColumn4
            // 
            this.bandedGridColumn4.Caption = "RSRP";
            this.bandedGridColumn4.FieldName = "AfterRsrpAvg";
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            this.bandedGridColumn4.Visible = true;
            this.bandedGridColumn4.Width = 56;
            // 
            // bandedGridColumn5
            // 
            this.bandedGridColumn5.Caption = "SINR";
            this.bandedGridColumn5.FieldName = "AfterSinrAvg";
            this.bandedGridColumn5.Name = "bandedGridColumn5";
            this.bandedGridColumn5.Visible = true;
            this.bandedGridColumn5.Width = 60;
            // 
            // bandedGridColumn6
            // 
            this.bandedGridColumn6.Caption = "AppSpeed";
            this.bandedGridColumn6.FieldName = "AfterAppSpeedAvg";
            this.bandedGridColumn6.Name = "bandedGridColumn6";
            this.bandedGridColumn6.Visible = true;
            this.bandedGridColumn6.Width = 80;
            // 
            // NRHandOverArfcnListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(964, 412);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "NRHandOverArfcnListForm";
            this.Text = "切换频段序列分析结果";
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGeneral)).EndInit();
            this.ctxMenuGeneral.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGeneral)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlDetail)).EndInit();
            this.ctxMenuDetail.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewDetail)).EndInit();
            this.ResumeLayout(false);
        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControlGeneral;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewGeneral;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private System.Windows.Forms.ContextMenuStrip ctxMenuGeneral;
        private System.Windows.Forms.ToolStripMenuItem toolStripGeneralExportExcel;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl gridControlDetail;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView gridViewDetail;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn7;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn9;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn11;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn12;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn13;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn14;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn21;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn32;
        private System.Windows.Forms.ContextMenuStrip ctxMenuDetail;
        private System.Windows.Forms.ToolStripMenuItem toolStripDetaiExportExcel;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn girdColumnTotalBler;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumnBlerRate;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand4;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand5;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
    }
}