﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Net
{
    public class DIYSZKPIQuery : QueryBase
    {
        /**
        /// <summary>
        /// 业务：0：集团ATU，1：省公司巡检
        /// </summary>
        int projectType;
        /// <summary>
        /// 数据类型，0：GSM，1：TD
        /// </summary>
        int dataType;
        */
        /// <summary>
        /// 问题状态，0：全部，1：已创建，2：已关闭
        /// </summary>
        public int status { get; set; }
        public bool initKPISetting { get; set; } = false;
        private DIYSZKPIQuery(MainModel mainModel)
            : base(mainModel)
        {

        }

        private static DIYSZKPIQuery queryKPI = null;

        public static DIYSZKPIQuery GetInstance()
        {
            if (queryKPI == null)
            {
                queryKPI = new DIYSZKPIQuery(MainModel.GetInstance());
            }
            return queryKPI;
        }

        public override string Name
        {
            get { return "指标查询"; }
        }

        public override string IconName
        {
            get { return ""; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18002, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            WaitBox.Show("开始查询道路问题点...", queryInThread);
            //MainModel.MainForm.FireRoadProblemForm(dataType, status)
        }

        private void queryInThread()
        {
            try
            {
                if (!initKPISetting)
                {
                    DIYSQLSZKPISetting kpiSettingQuery = new DIYSQLSZKPISetting(MainModel);
                    kpiSettingQuery.Query();
                    initKPISetting = true;
                }
                MainModel.AllRoadProblemsDic.Clear();
                /**
                string tableName = "tb_sz_road_td_problem";
                if (dataType == 0)
                {
                    tableName = "tb_sz_road_gsm_problem";
                }
                else if (dataType == 1)
                {
                    tableName = "tb_sz_road_td_problem";
                }
                string sqlGridProblem = "select A.ID, A.areaname, A.level, A.roadname, B.roadDistance, A.status," +
                    " A.repeatlast, A.createdyear, A.createdbatch, A.beginyear, A.beginBatch, A.lastAbnormalYear," +
                    " A.lastAbnormalBatch, A.closedYear, A.closedBatch, A.lasttestyear, A.lasttestbatch," +
                    " A.gooddayscount, A.validatestatus, A.lastvalidateyear, A.lastvalidatebatch, A.gridrepeatcount" +
                    " from " + tableName + " A " +
                    " left join tb_sz_road_info B on A.areaname = B.areaname and A.level = B.level " +
                    " and A.roadname = B.roadname" +
                    " order by A.ID";
                WaitBox.Text = "开始获取道路问题点...";
                DIYSQLRoadProblem roadQuery = new DIYSQLRoadProblem(MainModel, sqlGridProblem);
                roadQuery.Query();
                */
                if (MainModel.AllRoadProblemsDic.Count == 0)
                {
                    return;
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }

        class DIYSQLSZKPISetting : DIYSQLBase
        {
            public DIYSQLSZKPISetting(MainModel mainModel)
                : base(mainModel)
            {
                MainDB = true;
            }

            protected override string getSqlTextString()
            {
                string sql = "select kpisn, iprojecttype icarrierid, iservicetype, targetname, substring(formula,1,125), substring(formula,126,250)," +
                    "substring(formula,251,375), substring(formula,376,500), substring(formula,501,625)," +
                    "substring(formula,626,750), substring(formula,751,875), substring(formula,876,1000), " +
                    "checkMethod, checklimit, mergetype " +
                    "from tb_sz_areastat_kpi_setting order by icarrierid, iservicetype, kpisn";
                return sql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] types = new E_VType[16];
                types[0] = E_VType.E_Int;
                types[1] = E_VType.E_Int;
                types[2] = E_VType.E_Int;
                types[3] = E_VType.E_Int;
                types[4] = E_VType.E_String;
                types[5] = E_VType.E_String;
                types[6] = E_VType.E_String;
                types[7] = E_VType.E_String;
                types[8] = E_VType.E_String;
                types[9] = E_VType.E_String;
                types[10] = E_VType.E_String;
                types[11] = E_VType.E_String;
                types[12] = E_VType.E_String;
                types[13] = E_VType.E_String;
                types[14] = E_VType.E_Float;
                types[15] = E_VType.E_Int;
                return types;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                List<SZKPISetting> kpiSettingList = new List<SZKPISetting>();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        SZKPISetting kpiSetting = SZKPISetting.FillFrom(package.Content);
                        kpiSettingList.Add(kpiSetting);
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
                SZKPIManager.GetInstance().InitKPISetting(kpiSettingList);
            }

            public override string Name
            {
                get { return "查询KPI指标配置"; }
            }
        }

        /**
        private class DIYSQLKPIQueryDay : DIYSQLBase
        {
            string sql = "";
            public DIYSQLKPIQueryDay(MainModel mainModel, string tableName)
                : base(mainModel)
            {
            }
            protected override string getSqlTextString()
            {
                return sql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[55];
                rType[0] = E_VType.E_String;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_Int;
                rType[3] = E_VType.E_Int;
                rType[4] = E_VType.E_Int;
                rType[5] = E_VType.E_Float;
                rType[6] = E_VType.E_Float;
                rType[7] = E_VType.E_Float;
                rType[8] = E_VType.E_Float;
                rType[9] = E_VType.E_Float;
                rType[10] = E_VType.E_Float;
                rType[11] = E_VType.E_Float;
                rType[12] = E_VType.E_Float;
                rType[13] = E_VType.E_Float;
                rType[14] = E_VType.E_Float;
                rType[15] = E_VType.E_Float;
                rType[16] = E_VType.E_Float;
                rType[17] = E_VType.E_Float;
                rType[18] = E_VType.E_Float;
                rType[19] = E_VType.E_Float;
                rType[20] = E_VType.E_Float;
                rType[21] = E_VType.E_Float;
                rType[22] = E_VType.E_Float;
                rType[23] = E_VType.E_Float;
                rType[24] = E_VType.E_Float;
                rType[25] = E_VType.E_Float;
                rType[26] = E_VType.E_Float;
                rType[27] = E_VType.E_Float;
                rType[28] = E_VType.E_Float;
                rType[29] = E_VType.E_Float;
                rType[30] = E_VType.E_Float;
                rType[31] = E_VType.E_Float;
                rType[32] = E_VType.E_Float;
                rType[33] = E_VType.E_Float;
                rType[34] = E_VType.E_Float;
                rType[35] = E_VType.E_Float;
                rType[36] = E_VType.E_Float;
                rType[37] = E_VType.E_Float;
                rType[38] = E_VType.E_Float;
                rType[39] = E_VType.E_Float;
                rType[40] = E_VType.E_Float;
                rType[41] = E_VType.E_Float;
                rType[42] = E_VType.E_Float;
                rType[43] = E_VType.E_Float;
                rType[44] = E_VType.E_Float;
                rType[45] = E_VType.E_Float;
                rType[46] = E_VType.E_Float;
                rType[47] = E_VType.E_Float;
                rType[48] = E_VType.E_Float;
                rType[49] = E_VType.E_Float;
                rType[50] = E_VType.E_Float;
                rType[51] = E_VType.E_Float;
                rType[52] = E_VType.E_Float;
                rType[53] = E_VType.E_Float;
                rType[54] = E_VType.E_Float;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        RoadProblem road = new RoadProblem();
                        road.Fill(package.Content);
                        MainModel.AllRoadProblemsDic[road.ID] = road;
                        //do your code here
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                    {
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                    {
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }

            public override string Name
            {
                get { return "DIYSQLKPIQueryDay"; }
            }
        }*/
    }
}
