﻿using MasterCom.RAMS.Model;
using MasterCom.Util.UiEx;
using System;
using System.Collections.Generic;

namespace MasterCom.RAMS.ZTFunc
{
    class LteFddIndoorStationAcceptQuery : LteFddStationAcceptQuery
    {
        public LteFddIndoorStationAcceptQuery(MainModel mainModel)
            : base(mainModel)
        {
            isAutoLoadCQTPicture = false;
        }

        public override string Name
        {
            get
            {
                return "LTEFDD室分站验收";
            }
        }

        protected override void initManager(LteStationAcceptCondition cond)
        {
            manager = new LteFddIndoorStationAcceptManager();
            manager.SetAcceptCond(cond);
        }

        protected override List<string> queryColumns
        {
            get
            {
                return new List<string>()
                        {
                             "isampleid",
                             "itime",
                             "ilongitude",
                             "ilatitude",
                             "lte_TAC",
                             "lte_ECI",
                             "lte_RSRP",
                             "lte_SINR",
                             "lte_EARFCN",
                             "lte_PCI",
                             "lte_APP_ThroughputUL",
                             "lte_APP_ThroughputDL",
                             "lte_PDCP_UL",
                             "lte_PDCP_DL",
                             "lte_APP_type",
                             "lte_NCell_EARFCN",
                             "lte_NCell_PCI"
                        };
            }
        }
    }
}
