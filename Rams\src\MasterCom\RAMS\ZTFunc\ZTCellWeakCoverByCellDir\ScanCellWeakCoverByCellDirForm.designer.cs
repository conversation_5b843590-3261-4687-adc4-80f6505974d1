﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ScanCellWeakCoverByCellDirForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ScanCellWeakCoverByCellDirForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.objectListView = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBCCH = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBSIC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSampleCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxLevMean = new BrightIdeasSoftware.OLVColumn();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // objectListView
            // 
            this.objectListView.AllColumns.Add(this.olvColumnSN);
            this.objectListView.AllColumns.Add(this.olvColumnCellName);
            this.objectListView.AllColumns.Add(this.olvColumnLAC);
            this.objectListView.AllColumns.Add(this.olvColumnCI);
            this.objectListView.AllColumns.Add(this.olvColumnBCCH);
            this.objectListView.AllColumns.Add(this.olvColumnBSIC);
            this.objectListView.AllColumns.Add(this.olvColumnSampleCount);
            this.objectListView.AllColumns.Add(this.olvColumnRxLevMean);
            this.objectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnCellName,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnBCCH,
            this.olvColumnBSIC,
            this.olvColumnSampleCount,
            this.olvColumnRxLevMean});
            this.objectListView.ContextMenuStrip = this.ctxMenu;
            this.objectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListView.FullRowSelect = true;
            this.objectListView.GridLines = true;
            this.objectListView.Location = new System.Drawing.Point(0, 0);
            this.objectListView.Name = "objectListView";
            this.objectListView.ShowGroups = false;
            this.objectListView.Size = new System.Drawing.Size(784, 430);
            this.objectListView.TabIndex = 1;
            this.objectListView.UseCompatibleStateImageBehavior = false;
            this.objectListView.View = System.Windows.Forms.View.Details;
            this.objectListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.AspectName = "CellName";
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名";
            this.olvColumnCellName.Width = 80;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.AspectName = "LAC";
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.AspectName = "CI";
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // olvColumnBCCH
            // 
            this.olvColumnBCCH.AspectName = "BCCH";
            this.olvColumnBCCH.HeaderFont = null;
            this.olvColumnBCCH.Text = "频点";
            // 
            // olvColumnBSIC
            // 
            this.olvColumnBSIC.AspectName = "BSIC";
            this.olvColumnBSIC.HeaderFont = null;
            this.olvColumnBSIC.Text = "扰码";
            // 
            // olvColumnSampleCount
            // 
            this.olvColumnSampleCount.AspectName = "GridCount";
            this.olvColumnSampleCount.HeaderFont = null;
            this.olvColumnSampleCount.Text = "弱覆盖栅格数";
            this.olvColumnSampleCount.Width = 90;
            // 
            // olvColumnRxLevMean
            // 
            this.olvColumnRxLevMean.AspectName = "RxLevMean";
            this.olvColumnRxLevMean.HeaderFont = null;
            this.olvColumnRxLevMean.Text = "平均电平";
            this.olvColumnRxLevMean.Width = 80;
            // 
            // ScanCellWeakCoverByCellDirForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(784, 430);
            this.Controls.Add(this.objectListView);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ScanCellWeakCoverByCellDirForm";
            this.Text = "弱覆盖小区";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private BrightIdeasSoftware.ObjectListView objectListView;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnBCCH;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLevMean;

    }
}