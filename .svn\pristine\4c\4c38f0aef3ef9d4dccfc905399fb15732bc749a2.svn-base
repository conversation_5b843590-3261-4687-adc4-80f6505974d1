﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraGrid.Views.BandedGrid;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.CQTSiteStat
{
    public partial class CQTSiteStatResultForm : MinCloseForm
    {
        public CQTSiteStatResultForm()
        {
            InitializeComponent();
        }

        public void FillData(CQTSiteStatTemplate tpl,ICollection<SiteStatInfo> kpiDataSet)
        {
            createView(tpl);
            this.gridCtrl.DataSource = createDataTable(tpl, kpiDataSet);
        }

        private void createSubBand(GridBand parentBand, ColumnGroup subGrp)
        {
            GridBand band = new GridBand();
            band.Caption = subGrp.Name;
            parentBand.Children.Add(band);
            if (subGrp.Columns != null)
            {
                foreach (Column col in subGrp.Columns)
                {
                    BandedGridColumn bandCol = new BandedGridColumn();
                    bandCol.Caption = col.Name;
                    bandCol.FieldName = col.ToString();
                    bandCol.Visible = true;
                    this.bandedView.Columns.Add(bandCol);
                    band.Columns.Add(bandCol);
                }
            }
            else if (subGrp.Children != null)
            {
                foreach (ColumnGroup g in subGrp.Children)
                {
                    createSubBand(band, g);
                }
            }
        }

        private void createView(CQTSiteStatTemplate tpl) 
        {
            this.bandedView.Bands.Clear();
            this.bandedView.Columns.Clear();
            foreach (ColumnGroup grp in tpl.ColGroups)
            {
                GridBand band = new GridBand();
                band.Caption = grp.Name;
                this.bandedView.Bands.Add(band);

                if (grp.Columns != null)
                {
                    foreach (Column col in grp.Columns)
                    {
                        BandedGridColumn bandCol = new BandedGridColumn();
                        bandCol.Caption = col.Name;
                        bandCol.FieldName = col.ToString();
                        bandCol.Visible = true;
                        this.bandedView.Columns.Add(bandCol);
                        band.Columns.Add(bandCol);
                    }
                }
                else if (grp.Children != null)
                {
                    foreach (ColumnGroup subGrp in grp.Children)
                    {
                        createSubBand(band, subGrp);
                    }
                }
            }

            GridBand bandEx = new GridBand();
            bandEx.Caption = "5项指标对比";
            this.bandedView.Bands.Add(bandEx);
            createExCol(bandEx, "优于联通指标数量");
            createExCol(bandEx, "优于电信指标数量");
            createExCol(bandEx, "3项指标");
            createExCol(bandEx, "4项指标");
            createExCol(bandEx, "5项指标");
        }

        private void createExCol(GridBand bandEx, string name)
        {
            BandedGridColumn bandColEx = new BandedGridColumn();
            bandColEx.Caption = name;
            bandColEx.FieldName = name;
            bandColEx.Visible = true;
            this.bandedView.Columns.Add(bandColEx);
            bandEx.Columns.Add(bandColEx);
        }

        private DataTable createDataTable(CQTSiteStatTemplate tpl, ICollection<SiteStatInfo> kpiDataSet)
        {
            DataTable tb = new DataTable();
            foreach (Column col in tpl.Columns)
            {
                tb.Columns.Add(col.ToString());
            }
            tb.Columns.Add("优于联通指标数量");
            tb.Columns.Add("优于电信指标数量");
            tb.Columns.Add("3项指标");
            tb.Columns.Add("4项指标");
            tb.Columns.Add("5项指标");

            foreach (SiteStatInfo statInfo in kpiDataSet)
            {
                Dictionary<ColumnGroup, List<double>> competeDic = new Dictionary<ColumnGroup, List<double>>();
                DataRow row = tb.NewRow();
                tb.Rows.Add(row);
                int i = dealTemplate(tpl, statInfo, competeDic, row);

                int goodThanCUNum = 0;
                int goodThanCTNum = 0;
                getGoodNum(competeDic, ref goodThanCUNum, ref goodThanCTNum);
                row[i++] = goodThanCUNum;
                row[i++] = goodThanCTNum;
                row[i++] = (goodThanCUNum >= 3 && goodThanCTNum >= 3) ? "优于竞对" : "不优于竞对";
                row[i++] = (goodThanCUNum >= 4 && goodThanCTNum >= 4) ? "优于竞对" : "不优于竞对";
                row[i] = (goodThanCUNum >= 5 && goodThanCTNum >= 5) ? "优于竞对" : "不优于竞对";
            }

            return tb;
        }

        private int dealTemplate(CQTSiteStatTemplate tpl, SiteStatInfo statInfo, Dictionary<ColumnGroup, List<double>> competeDic, DataRow row)
        {
            int i = 0;
            for (; i < tpl.Columns.Count; i++)
            {
                if (i < tpl.KeyColNum)
                {
                    row[i] = statInfo.Keys[i];
                }
                else
                {
                    addCompeteDic(tpl, statInfo, competeDic, row, i);
                }
            }

            return i;
        }

        private void getGoodNum(Dictionary<ColumnGroup, List<double>> competeDic, ref int goodThanCUNum, ref int goodThanCTNum)
        {
            foreach (List<double> values in competeDic.Values)
            {
                if (values[0] >= values[1])
                {
                    goodThanCUNum++;
                }
                if (values[0] >= values[2])
                {
                    goodThanCTNum++;
                }
            }
        }

        private void addCompeteDic(CQTSiteStatTemplate tpl, SiteStatInfo statInfo, Dictionary<ColumnGroup, List<double>> competeDic, DataRow row, int i)
        {
            Column c = tpl.Columns[i];
            if (c.Formula == KeyWords.TestDate.ToString())
            {
                row[i] = statInfo.TestDate;
            }
            else
            {
                double d = statInfo.KPIData.CalcFormula((CarrierType)(int)c.CarrierType
               , -1, c.Formula, 2, null);
                d = double.IsNaN(d) ? 0 : d;
                row[i] = d;
                if (c.Group.IsCompeted)
                {
                    if (!competeDic.ContainsKey(c.Group))
                    {
                        competeDic[c.Group] = new List<double>();
                    }
                    competeDic[c.Group].Add(d);
                }
            }
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Filter = FilterHelper.Xlsx;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                this.gridCtrl.ExportToXlsx(dlg.FileName);
            }
        }

    }
}
