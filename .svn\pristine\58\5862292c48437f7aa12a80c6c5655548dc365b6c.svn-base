﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class NBFartherCoverQueryByRegion : FartherCoverQueryByRegion
    {
        public NBFartherCoverQueryByRegion(ServiceName serviceName)
            : base(serviceName)
        {
        }

        public override string Name
        {
            get { return "超远覆盖_NB(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34005, this.Name);
        }
    }

    class NBFartherCoverQueryByFile : FartherCoverQueryByFile
    {
        public NBFartherCoverQueryByFile(ServiceName serviceName)
            : base(serviceName)
        {
        }

        public override string Name
        {
            get { return "超远覆盖_NB(按文件)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34006, this.Name);
        }
    }
}
