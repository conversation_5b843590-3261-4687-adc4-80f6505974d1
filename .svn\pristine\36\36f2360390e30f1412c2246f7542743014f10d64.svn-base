﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    public partial class PRBLowSchedulingPnl : UserControl
    {
        public PRBLowSchedulingPnl()
        {
            InitializeComponent();
        }

        PRBLowSchedulingCause mainReason = null;

        public void LinkCondition(PRBLowSchedulingCause reason)
        {
            this.mainReason = reason;

            numPRBMin.Value = (decimal)reason.PRBMin;
            numPRBMin.ValueChanged += numPRBBefore_ValueChanged;

            numPRBMax.Value = (decimal)reason.PRBMax;
            numPRBMax.ValueChanged += numPRBAfter_ValueChanged;
        }

        void numPRBBefore_ValueChanged(object sender, EventArgs e)
        {
            mainReason.PRBMin = (int)numPRBMin.Value;
        }

        void numPRBAfter_ValueChanged(object sender, EventArgs e)
        {
            mainReason.PRBMax = (int)numPRBMax.Value;
        }
    }
}
