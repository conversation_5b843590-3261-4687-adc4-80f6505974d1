﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTSQLReportEventInfoQuery_QH : DIYSQLBase
    {
        readonly ReportEventCondition_QH reportEventCond = null;
        public ZTSQLReportEventInfoQuery_QH(MainModel mainModel, ReportEventCondition_QH reportEventCond)
            : base(mainModel)
        {
            this.reportEventCond = reportEventCond;
        }

        public ZTSQLReportEventInfoQuery_QH(MainModel mainModel, ReportEventCondition_QH reportEventCond, int did)
            : base(mainModel)
        {
            this.reportEventCond = reportEventCond;
            this.dbid = did;
        }

        private readonly List<ZTReportEventInfo_QH> reportEventInfoList = new List<ZTReportEventInfo_QH>();

        public List<ZTReportEventInfo_QH> GetReportEventInfoList()
        {
            return reportEventInfoList;
        }

        protected override string getSqlTextString()
        {
            int stime;
            int etime;
            if (ZTReportEventMngQuery_QH.thirdNameList.Count != 0
                && ZTReportEventMngQuery_QH.thirdNameList[0].Equals(ZTReportEventMngQuery_QH.nonThird))
            {
                stime = (int)(JavaDate.GetMilliseconds(new DateTime(1970, 1, 1)) / 1000L);
                etime = (int)(JavaDate.GetMilliseconds(new DateTime(2030, 1, 1)) / 1000L);

                return "exec mc_sp_qinghai_report_event_get '" + MainModel.User.LoginName + "'," + stime + "," + etime + ",'" + "全部" + "'";
            }
            else
            {
                stime = (int)(JavaDate.GetMilliseconds(reportEventCond.BeginTime) / 1000L);
                etime = (int)(JavaDate.GetMilliseconds(reportEventCond.EndTime) / 1000L);

                return "exec mc_sp_qinghai_report_event_get '" + reportEventCond.UserName + "'," + stime + "," + etime + ",'" + reportEventCond.NetType + "'";
            }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int index = 0;
            E_VType[] rType = new E_VType[31];
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;

            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;

            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_Int;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index++] = E_VType.E_String;
            rType[index] = E_VType.E_Int;
            return rType;
        }

        protected override void query()
        {
            MainModel.ClearDTData();
            base.query();
            MainModel.FireDTDataChanged(this);
            MainModel.RefreshLegend();
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        private void fillData(Package package)
        {
            ZTReportEventInfo_QH info = new ZTReportEventInfo_QH();
            info.SN = reportEventInfoList.Count + 1;
            if (info.Fill(package.Content, this.MainModel, QueryEvent.QueryAll))
            {
                //if (ZTReportEventMngQuery_QH.thirdNameList.Count == 0)
                if (ZTReportEventMngQuery_QH.thirdNameList.Count != 0
                    && ZTReportEventMngQuery_QH.thirdNameList[0].Equals(ZTReportEventMngQuery_QH.nonThird))
                {
                    reportEventInfoList.Add(info);
                    MainModel.DTDataManager.Add(info);
                }
                else
                {
                    if (isValidProject(info) && isInRegion(info))
                    {
                        reportEventInfoList.Add(info);
                        MainModel.DTDataManager.Add(info);
                    }
                }
            }
        }

        private bool isValidProject(ZTReportEventInfo_QH eventInfo)
        {
            if (reportEventCond.ProjDic.ContainsKey(eventInfo.ProjectType))
            {
                return true;
            }

            return false;
        }

        private bool isInRegion(ZTReportEventInfo_QH eventInfo)
        {
            if (MainModel.SearchGeometrys != null && MainModel.SearchGeometrys.IsSelectRegion())
            {
                return MainModel.SearchGeometrys.GeoOp.Contains(eventInfo.Longitude, eventInfo.Latitude);
            }
            return true;
        }

        public override string Name
        {
            get { return "ZTSQLReportEventInfoQuery_QH"; }
        }
    }
}
