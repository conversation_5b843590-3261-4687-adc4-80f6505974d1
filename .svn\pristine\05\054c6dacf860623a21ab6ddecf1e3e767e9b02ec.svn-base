﻿using MasterCom.RAMS.Chris.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteEventCellInfoAnaBase : DIYAnalyseByFileBackgroundBase
    {
        readonly List<int> hoRequestEvtIdList = new List<int> { 850, 898 };
        readonly List<int> hoSuccessEvtIdList = new List<int> { 851, 899 };
        readonly List<int> hotTAUFandRRCEvt = new List<int> { 854, 865 };
        readonly List<int> hoFailEvtIdList = new List<int> { 870, 1100 };

        List<EventCellInfo> evtCellInfoList = new List<EventCellInfo>();

        protected static readonly object lockObj = new object();

        protected LteEventCellInfoAnaBase()
            : base(MainModel.GetInstance())
        {
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22091, this.Name);
        }

        protected override bool getCondition()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
            ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.LTE_TDD_UEP);
            ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);

            Columns = new List<string>();
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_RSRQ");
            Columns.Add("lte_RSSI");
            Columns.Add("lte_SCell_LAC");
            Columns.Add("lte_SCell_CI");
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");

            IncludeMessage = true;
            return true;
        }

        protected override void fireShowForm()
        {
            if (evtCellInfoList.Count == 0)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("没有符合条件的信息！");
                return;
            }
            LteEventCellInfoListForm frm = MainModel.CreateResultForm(typeof(LteEventCellInfoListForm)) as LteEventCellInfoListForm;
            frm.FillData(evtCellInfoList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            evtCellInfoList = new List<EventCellInfo>();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
                {
                    List<DTData> dtDataList = getDtDataList(fileMng);

                    TestPoint lastTp = null;
                    int index = 0;
                    foreach (DTData data in dtDataList)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }

                        if (data is Event)
                        {
                            dealEvt(dtDataList, lastTp, index, data);
                        }
                        else if (data is TestPoint)
                        {
                            lastTp = data as TestPoint;
                        }
                        index++;
                    }
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private List<DTData> getDtDataList(DTFileDataManager fileMng)
        {
            List<DTData> dtDataList = new List<DTData>();

            foreach (TestPoint tp in fileMng.TestPoints)
            {
                dtDataList.Add((DTData)tp);
            }

            foreach (Event evt in fileMng.Events)
            {
                if (hoRequestEvtIdList.Contains(evt.ID) || hoSuccessEvtIdList.Contains(evt.ID)
                    || hoFailEvtIdList.Contains(evt.ID) || hotTAUFandRRCEvt.Contains(evt.ID))
                {
                    dtDataList.Add(evt);
                }
            }
            foreach (MasterCom.RAMS.Model.Message msg in fileMng.Messages)
            {
                if (msg.ID == (int)enumMsgId.RRCConnectionRelease || msg.ID == (int)enumMsgId.MeasurementReport
                    || msg.ID == (int)enumMsgId.RRCConnectionReconfig || msg.ID == (int)enumMsgId.TrackAreaUpdateFail)
                {
                    dtDataList.Add(msg);
                }
            }
            dtDataList.Sort(comparer);
            return dtDataList;
        }

        private void dealEvt(List<DTData> dtDataList, TestPoint lastTp, int index, DTData data)
        {
            Event evt = data as Event;
            EventCellInfo info = new EventCellInfo(evt);

            if (hotTAUFandRRCEvt.Contains(evt.ID))
            {
                if (lastTp != null && (evt.DateTime - lastTp.DateTime).TotalSeconds <= 3)
                {
                    info.PointList.Add(lastTp);
                    info.SrcRsrp = (float?)lastTp["lte_RSRP"];
                    info.SrcSinr = (float?)lastTp["lte_SINR"];
                    getCauseInfo(index, dtDataList, info);
                }
            }
            else
            {
                int? lac = (int?)evt["LAC"];
                int? ci = (int?)evt["CI"];
                if (lac != null && ci != null)
                {
                    string strToken = string.Format("{0}_{1}", lac.ToString(), ci.ToString());
                    getSrcAndTargetInfo(index, dtDataList, strToken, info);
                }
            }

            info.SN = evtCellInfoList.Count + 1;
            evtCellInfoList.Add(info);
        }

        private void getSrcAndTargetInfo(int curIndex, List<DTData> dataList, string cellToken, EventCellInfo info)
        {
            int evtId = info.CurEvent.ID;
            bool hasGotSrcInfo = false;
            bool hasGotTargetPci = false;
            bool hasGotTargetRsrp = false;

            int targetPci = int.MaxValue;

            if (hoSuccessEvtIdList.Contains(evtId))//切换成功事件不再获取目标小区信息
            {
                hasGotTargetRsrp = true;
            }

            for (int i = curIndex; i > 0; i--)
            {
                DTData data = dataList[i];

                if (WaitBox.CancelRequest || (info.CurEvent.DateTime - data.DateTime).TotalSeconds > 3)
                {
                    break;
                }

                if (data is TestPoint && !hasGotSrcInfo)//从采样点中获取源小区RSRP、SINR
                {
                    hasGotSrcInfo = dealTP(cellToken, info, hasGotSrcInfo, data);
                }
                else if (data is MasterCom.RAMS.Model.Message && !hasGotTargetRsrp)
                {
                    dealMsg(info, evtId, ref hasGotTargetPci, ref hasGotTargetRsrp, ref targetPci, data);
                }

                if (hasGotSrcInfo && hasGotTargetRsrp)
                {
                    break;
                }
            }
        }

        private bool dealTP(string cellToken, EventCellInfo info, bool hasGotSrcInfo, DTData data)
        {
            TestPoint tp = data as TestPoint;
            info.PointList.Add(tp);

            ICell cell = tp.GetMainCell();
            if (cell.Token == cellToken)
            {
                info.SrcRsrp = (float?)tp["lte_RSRP"];
                info.SrcSinr = (float?)tp["lte_SINR"];
                hasGotSrcInfo = true;
            }

            return hasGotSrcInfo;
        }

        private void dealMsg(EventCellInfo info, int evtId, ref bool hasGotTargetPci, ref bool hasGotTargetRsrp, ref int targetPci, DTData data)
        {
            int targetEarfcn;
            bool hasEvt = hoRequestEvtIdList.Contains(evtId) || hoFailEvtIdList.Contains(evtId);
            if (hasEvt)//获取目标小区RSRP（切换请求和失败事件从信令中）
            {
                MasterCom.RAMS.Model.Message msg = data as MasterCom.RAMS.Model.Message;
                info.MessageList.Add(msg);

                if (msg.ID == (int)enumMsgId.RRCConnectionReconfig && !hasGotTargetPci)
                {
                    targetPci = MessageDecodeHelper.GetMsgSingleInt(msg, "lte-rrc.targetPhysCellId");
                    targetEarfcn = MessageDecodeHelper.GetMsgSingleInt(msg, "lte-rrc.dl_CarrierFreq");
                    hasGotTargetPci = getHasGotTargetPci(info, hasGotTargetPci, targetPci, targetEarfcn, msg);
                }
                else if (msg.ID == (int)enumMsgId.MeasurementReport && hasGotTargetPci)
                {
                    uint[] pciList = MessageDecodeHelper.GetMsgMultiUInt(msg, "lte-rrc.measResultListEUTRA", "lte-rrc.physCellId");
                    uint[] rsrpList = MessageDecodeHelper.GetMsgMultiUInt(msg, "lte-rrc.measResultListEUTRA", "lte-rrc.rsrpResult");

                    if (pciList != null && rsrpList != null && (pciList.Length == rsrpList.Length - 1))
                    {
                        hasGotTargetRsrp = getHasGotTargetRsrp(info, hasGotTargetRsrp, targetPci, pciList, rsrpList);
                    }
                }
            }
        }

        private bool getHasGotTargetPci(EventCellInfo info, bool hasGotTargetPci, int targetPci, int targetEarfcn, Model.Message msg)
        {
            if (targetPci != int.MaxValue && targetEarfcn != int.MaxValue)
            {
                LTECell cell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(msg.DateTime, targetEarfcn
                    , targetPci, info.CurEvent.Longitude, info.CurEvent.Latitude);
                if (cell != null)
                {
                    info.TargetCellName = cell.Name;
                }

                hasGotTargetPci = true;
            }

            return hasGotTargetPci;
        }

        private bool getHasGotTargetRsrp(EventCellInfo info, bool hasGotTargetRsrp, int targetPci, uint[] pciList, uint[] rsrpList)
        {
            int pciIndex = 0;
            foreach (uint pci in pciList)
            {
                if (pci == targetPci)
                {
                    uint rsrpCode = rsrpList[pciIndex + 1];
                    if (rsrpCode != uint.MaxValue)
                    {
                        info.TargetRsrp = ((int)rsrpCode - 141) + "≤RSRP＜" + ((int)rsrpCode - 140);
                    }
                    hasGotTargetRsrp = true;
                    break;
                }
                pciIndex++;
            }

            return hasGotTargetRsrp;
        }

        private void getCauseInfo(int curIndex, List<DTData> dataList, EventCellInfo info)
        {
            for (int i = curIndex; i > 0; i--)
            {
                DTData data = dataList[i];
                if ((info.CurEvent.DateTime - data.DateTime).TotalSeconds > 3)
                {
                    break;
                }

                bool isFound = dealMsg(info, data);
                if (isFound)
                {
                    break;
                }
            }
        }

        private bool dealMsg(EventCellInfo info, DTData data)
        {
            if (data is MasterCom.RAMS.Model.Message)
            {
                MasterCom.RAMS.Model.Message msg = data as MasterCom.RAMS.Model.Message;
                if (msg.ID == (int)enumMsgId.RRCConnectionRelease && info.CurEvent.ID == 865)
                {
                    int cause = MessageDecodeHelper.GetMsgSingleInt(msg, "lte-rrc.releaseCause");
                    if (cause != int.MaxValue)
                    {
                        info.MessageList.Add(msg);
                        info.CauseValue = cause.ToString();
                    }
                    return true;
                }
                if (msg.ID == (int)enumMsgId.TrackAreaUpdateFail && info.CurEvent.ID == 854)
                {
                    int cause = MessageDecodeHelper.GetMsgSingleInt(msg, "nas_eps.emm.cause");
                    if (cause != int.MaxValue)
                    {
                        info.MessageList.Add(msg);
                        info.CauseValue = cause.ToString();
                    }
                    return true;
                }
            }
            return false;
        }

        protected override void getResultsAfterQuery()
        {
            //
        }

        private enum enumMsgId
        {
            MeasurementReport = 1093626369,
            RRCConnectionReconfig = 1093625860,
            RRCConnectionRelease = 1093625861,
            TrackAreaUpdateFail=1097533259,
        }

        private Comparer comparer { get; set; } = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }
    }

    public class EventCellInfo
    {
        public EventCellInfo(Event evt)
        {
            this.CurEvent = evt;
            PointList = new List<TestPoint>();
            MessageList = new List<MasterCom.RAMS.Model.Message>();
        }

        public List<TestPoint> PointList { get; set; }
        public List<MasterCom.RAMS.Model.Message> MessageList { get; set; }
        public int SN { get; set; }
        public Event CurEvent { get; set; }
        public string EventName { get { return CurEvent.Name; } }
        public string FileName { get { return CurEvent.FileName; } }
        public string Time { get { return CurEvent.DateTimeStringWithMillisecond; } }

        public string SrcCellName { get { return CurEvent.CellNameSrc; } }
        public float? SrcRsrp { get; set; }
        public float? SrcSinr { get; set; }

        public string TargetCellName { get; set; }
        public string TargetRsrp { get; set; }

        public string CauseValue { get; set; }
    }
}
