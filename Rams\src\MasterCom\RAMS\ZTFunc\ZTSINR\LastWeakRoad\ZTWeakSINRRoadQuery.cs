﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTWeakSINRRoadQuery : ZTWeakSINRRoadBaseQuery<ZTWeakSINRRoadQuery>
    {
        protected override string themeName { get { return "TD_LTE_SINR"; } }
        protected override string rsrpName { get { return "lte_RSRP"; } }
        protected override string sinrName { get { return "lte_SINR"; } }

        public ZTWeakSINRRoadQuery()
            : base()
        {
            init();
        }

        public ZTWeakSINRRoadQuery(ServiceName serviceName)
            : this()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        } 

        protected void init()
        {
            this.IsCanExportResultMapToWord = true;
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
        }

        public override string Name
        {
            get { return "SINR质差路段_LTE"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22013, this.Name);//////
        }

        protected override void fireShowForm()
        {
            if (weakCoverList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            WeakSINRRoadForm frm = MainModel.CreateResultForm(typeof(WeakSINRRoadForm)) as WeakSINRRoadForm;
            frm.weakSINRRoadCondition = WeakCondition;
            frm.FillData(weakCoverList);
            frm.Visible = true;
            frm.BringToFront();
            weakCoverList = null;
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                setRoadCond();
                return true;
            }
            WeakSINRRoadSettingDlg dlg = new WeakSINRRoadSettingDlg(WeakCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                WeakCondition = dlg.GetCondition();
                setRoadCond();
                return true;
            }
            return false;
        }

        protected override void addToReportInfo(List<TestPoint> testPointList, double curWeakPercent, double curDis)
        {
            if (testPointList.Count == 0)
            {
                return;
            }
            WeakSINRRoad weakCover = new WeakSINRRoad();
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];
                float? sinr = getSinr(testPoint);
                float? rsrp = getRsrp(testPoint);
                weakCover.Add(sinr, rsrp, testPoint);
                if (weakCover.MotorWay == "" || weakCover.MotorWay == null)
                {
                    weakCover.SetMotorWay(testPoint.AreaID, testPoint.AreaTypeID);
                }
            }
            weakCover.WeakPercent = curWeakPercent;
            weakCover.Distance = curDis;
            weakCover.Duration = duration;
            weakCover.CityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            if (WeakCondition.IsShowFDDPointFreq)
            {
                weakCover.FDDPointPercent = getFDDFreqPercent(testPointList);
            }
            saveWeakCoverInfo(weakCover);
        }

        /// <summary>
        /// 得到FDD占比
        /// </summary>
        /// <param name="listTestPoint"></param>
        /// <returns></returns>
        private double getFDDFreqPercent(List<TestPoint> listTestPoint)
        {
            if(listTestPoint.Count==0)
            {
                return 0;
            }
            double percent = 0;
            int count = 0;
            foreach (TestPoint tp in listTestPoint)
            {
                foreach (FreqRange fr in WeakCondition.ListFreqRange)
                {
                    if(isValidFDD(tp,fr))
                    {
                        count++;
                        break;
                    }
                }
            }
            percent = Math.Round(1.0 *count / listTestPoint.Count*100,2);
            return percent;
        }

        /// <summary>
        /// 判断是否满足在FDD频段
        /// </summary>
        /// <param name="tp"></param>
        /// <param name="fr"></param>
        /// <returns></returns>
        private bool isValidFDD (TestPoint tp, FreqRange fr) 
        {
            int? fdd = tp.GetBCCH();
            if (fdd < fr.FreqStart)
            {
                return false;
            }
            if(fdd>fr.FreqEnd)
            {
                return false;
            }
            return true;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.质量; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["MaxSINR"] = WeakCondition.MaxSINR;
                param["CheckRSRP"] = WeakCondition.CheckRSRP;
                param["MinRSRP"] = WeakCondition.MinRSRP;
                param["WeakSINRPercent"] = WeakCondition.WeakSINRPercent;
                param["Max2TPDistance"] = WeakCondition.Max2TPDistance;
                param["CheckMinDistance"] = WeakCondition.CheckMinDistance;
                param["MinCoverRoadDistance"] = WeakCondition.MinCoverRoadDistance;
                param["CheckMinDuration"] = WeakCondition.CheckMinDuration;
                param["MinDuration"] = WeakCondition.MinDuration;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("MaxSINR"))
                {
                    WeakCondition.MaxSINR = (float)param["MaxSINR"];
                }
                if (param.ContainsKey("CheckRSRP"))
                {
                    WeakCondition.CheckRSRP = (bool)param["CheckRSRP"];
                }
                if (param.ContainsKey("MinRSRP"))
                {
                    WeakCondition.MinRSRP = (float)param["MinRSRP"];
                }
                if (param.ContainsKey("WeakSINRPercent"))
                {
                    WeakCondition.WeakSINRPercent = (double)param["WeakSINRPercent"];
                }
                if (param.ContainsKey("Max2TPDistance"))
                {
                    WeakCondition.Max2TPDistance = (double)param["Max2TPDistance"];
                }
                if (param.ContainsKey("CheckMinDistance"))
                {
                    WeakCondition.CheckMinDistance = (bool)param["CheckMinDistance"];
                }
                if (param.ContainsKey("MinCoverRoadDistance"))
                {
                    WeakCondition.MinCoverRoadDistance = (double)param["MinCoverRoadDistance"];
                }
                if (param.ContainsKey("CheckMinDuration"))
                {
                    WeakCondition.CheckMinDuration = (bool)param["CheckMinDuration"];
                }
                if (param.ContainsKey("MinDuration"))
                {
                    WeakCondition.MinDuration = (double)param["MinDuration"];
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new WeakSinrRoadProperties_LTE(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (WeakSINRRoad item in weakCoverList)
            {
                BackgroundResult result = item.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                result.ProjectString = BackgroundFuncBaseSetting.GetInstance().projectType;
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Road(GetSubFuncID(), curAnaFileInfo, bgResultList);
            weakCoverList.Clear();
        }
        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                float weakPercent = bgResult.GetImageValueFloat();
                float avgSpeed = bgResult.GetImageValueFloat();
                float dlCode0Qam64Rate = bgResult.GetImageValueFloat();
                float Rank_Indicator = bgResult.GetImageValueFloat();
                float PDSCH_BLER = bgResult.GetImageValueFloat();
                float Transmission_Mode = bgResult.GetImageValueFloat();
                float PDSCH_PRb_Num_s = bgResult.GetImageValueFloat();
                float DlCode1Qam64Rate = bgResult.GetImageValueFloat();

                StringBuilder sb = new StringBuilder();
                sb.Append("质差点占比(%)：" + weakPercent + "\r\n");
                sb.Append("平均下载速率(Mbps)：" + avgSpeed + "\r\n");
                sb.Append("下行码字0 64QAM占比(%)：" + dlCode0Qam64Rate + "\r\n");
                sb.Append("双流时长占比(%)：" + Rank_Indicator + "\r\n");
                sb.Append("误块率(%)：" + PDSCH_BLER + "\r\n");
                sb.Append("TM3比例(%)：" + Transmission_Mode + "\r\n");
                sb.Append("PRB调度数：" + PDSCH_PRb_Num_s + "\r\n");
                sb.Append("下行码字1 64QAM占比(%)：" + DlCode1Qam64Rate);

                bgResult.ImageDesc = sb.ToString();
            }
        }
        #endregion
    }

    public class WeakSINRRoadCondition
    {
        /// <summary>
        /// 频段
        /// </summary>
        public List<FreqRange> ListFreqRange
        {
            get;
            set;
        }

        /// <summary>
        ///是否显示 FDD采样点占比
        /// </summary>
        public bool IsShowFDDPointFreq
        {
            get; set;
        } = true;

        public float MinRSRP { get; set; } = -110;
        public bool CheckRSRP { get; set; } = true;
        public float MaxSINR { get; set; } = -10;
        public double MinCoverRoadDistance { get; set; } = 50;//最小持续距离
        public double Max2TPDistance { get; set; } = 50;
        public double MinDuration { get; set; } = 10;//最小持续时长

        public bool IsValidate(float? sinr)
        {
            return sinr <= MaxSINR && sinr >= -50;
        }
        
        public bool CheckMinDistance { get; set; } = true;

        public bool MatchMinWeakCoverDistance(double distance)
        {
            if (CheckMinDistance)
            {
                return distance >= MinCoverRoadDistance;
            }
            else
            {
                return true;
            }
        }

        public bool Match2TestpointsMaxDistance(double distance)
        {
            return distance <= Max2TPDistance;
        }
        
        public bool CheckMinDuration { get; set; } = false;
        public bool MatchMinWeakCoverDuration(double duration)
        {
            if (CheckMinDuration)
            {
                return duration >= MinDuration;
            }
            else
            {
                return true;
            }
        }
        
        public double WeakSINRPercent { get; set; } = 80;
    }

    /*********************************************
     * LteFdd
     * */
    public class ZTWeakSINRRoadQuery_LteFdd : ZTWeakSINRRoadQuery
    {
        protected override string themeName { get { return "LTE_FDD:SINR"; } }
        protected override string rsrpName { get { return "lte_fdd_RSRP"; } }
        protected override string sinrName { get { return "lte_fdd_SINR"; } }

        private static readonly object lockObj = new object();
        private static ZTWeakSINRRoadQuery_LteFdd intance = null;
        public new static ZTWeakSINRRoadQuery_LteFdd GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTWeakSINRRoadQuery_LteFdd();
                    }
                }
            }
            return intance;
        }

        protected ZTWeakSINRRoadQuery_LteFdd()
        {
            init();
        }

        protected new void init()
        {
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.LTEFDD));
        }

        public override string Name
        {
            get { return "SINR质差路段_LTEFDD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26010, this.Name);//////
        }

        protected override void fireShowForm()
        {
            if (weakCoverList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            WeakSINRRoadForm frm =MainModel.CreateResultForm(typeof(WeakSINRRoadForm)) as WeakSINRRoadForm;
            frm.FillData(weakCoverList);
            frm.Visible = true;
            frm.BringToFront();
            weakCoverList = null;
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                setRoadCond();
                return true;
            }
            WeakSINRRoadSettingDlg dlg = new WeakSINRRoadSettingDlg(WeakCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                WeakCondition = dlg.GetCondition();
                setRoadCond();
                return true;
            }
            return false;
        }

        protected override void addToReportInfo(List<TestPoint> testPointList, double curWeakPercent, double curDis)
        {
            if (testPointList.Count == 0)
            {
                return;
            }
            WeakSINRRoad_LteFdd weakCover = new WeakSINRRoad_LteFdd();
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];
                float? sinr = getSinr(testPoint);
                float? rsrp = getRsrp(testPoint);
                weakCover.Add(sinr, rsrp, testPoint);
            }
            weakCover.WeakPercent = curWeakPercent;
            weakCover.Distance = curDis;
            saveWeakCoverInfo(ref weakCover);
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            weakCoverList = new List<WeakSINRRoad_LteFdd>();
        }

        protected new List<WeakSINRRoad_LteFdd> weakCoverList = null;

        protected void saveWeakCoverInfo(ref WeakSINRRoad_LteFdd info)
        {
            if (info == null || !WeakCondition.MatchMinWeakCoverDistance(info.Distance))
            {
                info = null;
                return;
            }
            if (weakCoverList == null)
            {
                weakCoverList = new List<WeakSINRRoad_LteFdd>();
            }
            if (!weakCoverList.Contains(info))
            {
                info.SN = weakCoverList.Count + 1;
                info.FindRoadName();
                info.MakeSummary();
                info.SetMotorWay(areaID, areaTypeID);
                weakCoverList.Add(info);
            }
            info = null;
        }
    }

    public class ZTWeakSINRRoadQuery_LteFdd_VOLTE : ZTWeakSINRRoadQuery_LteFdd
    {
        private static readonly object lockObj = new object();
        private static ZTWeakSINRRoadQuery_LteFdd_VOLTE intance = null;
        public new static ZTWeakSINRRoadQuery_LteFdd_VOLTE GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTWeakSINRRoadQuery_LteFdd_VOLTE();
                    }
                }
            }
            return intance;
        }

        public ZTWeakSINRRoadQuery_LteFdd_VOLTE()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }

        public override string Name
        {
            get { return "VOLTE_FDD SINR质差路段"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26010, this.Name);//////
        }
    }
}
