﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEAntennaMultiCoverageSetForm : BaseDialog
    {
        public LTEAntennaMultiCoverageSetForm(string type)
        {
            InitializeComponent();
            cmbStatTopN.Properties.Items.Clear();
            if (type.Equals("LTEScan"))
            {
                chkSaveSample.Visible = false;
                numRxLevDValue.Value = 6;
                numRxLevThreshold.Value = -80;
                spinEditInvalidThresold.Value = -110;
                cmbStatTopN.Properties.Items.Add("最强小区");
                cmbStatTopN.Properties.Items.Add("前3强小区");
                cmbStatTopN.SelectedIndex = 0;
            }
        }

        public CellMultiCoverageCondition GetSettingFilterRet()
        {
            CellMultiCoverageCondition condition = new CellMultiCoverageCondition();
            condition.SetRxlevDiff = (int)numRxLevDValue.Value;
            condition.SetRxlev = (int)numRxLevThreshold.Value;
            condition.CoFreq = false;
            condition.StrStatTopN = cmbStatTopN.SelectedItem.ToString().Trim();
            condition.InvalidPointRxLev = (int)spinEditInvalidThresold.Value;
            condition.IsSaveSample = chkSaveSample.Checked;

            return condition;
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButton2_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}