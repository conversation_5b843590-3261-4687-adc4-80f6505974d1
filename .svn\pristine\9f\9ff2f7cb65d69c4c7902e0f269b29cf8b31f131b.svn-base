﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTTDCellReselectAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTTDCellReselectAnaListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewCellReselectAna = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFileName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnGridName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTime = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRNCName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellDistance = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBeforePccpchRscp = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBeforePccpchC2I = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBeforeDpchRscp = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBeforeDpchC2I = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDestCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDestRNCName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDestLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDestCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDestDistance = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAfterPccpchRscp = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAfterPccpchC2I = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAfterDpchRscp = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAfterDpchC2I = new BrightIdeasSoftware.OLVColumn();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCellReselectAna)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay,
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 48);
            this.ctxMenu.Opening += new System.ComponentModel.CancelEventHandler(this.ctxMenu_Opening);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(129, 22);
            this.miReplay.Text = "回放";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // ListViewCellReselectAna
            // 
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnStatSN);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnFileName);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnGridName);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnTime);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnLongitude);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnLatitude);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnCellName);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnRNCName);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnCellLAC);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnCellCI);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnCellDistance);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnBeforePccpchRscp);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnBeforePccpchC2I);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnBeforeDpchRscp);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnBeforeDpchC2I);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnDestCellName);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnDestRNCName);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnDestLAC);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnDestCI);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnDestDistance);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnAfterPccpchRscp);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnAfterPccpchC2I);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnAfterDpchRscp);
            this.ListViewCellReselectAna.AllColumns.Add(this.olvColumnAfterDpchC2I);
            this.ListViewCellReselectAna.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.ListViewCellReselectAna.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnFileName,
            this.olvColumnGridName,
            this.olvColumnTime,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnCellName,
            this.olvColumnRNCName,
            this.olvColumnCellLAC,
            this.olvColumnCellCI,
            this.olvColumnCellDistance,
            this.olvColumnBeforePccpchRscp,
            this.olvColumnBeforePccpchC2I,
            this.olvColumnBeforeDpchRscp,
            this.olvColumnBeforeDpchC2I,
            this.olvColumnDestCellName,
            this.olvColumnDestRNCName,
            this.olvColumnDestLAC,
            this.olvColumnDestCI,
            this.olvColumnDestDistance,
            this.olvColumnAfterPccpchRscp,
            this.olvColumnAfterPccpchC2I,
            this.olvColumnAfterDpchRscp,
            this.olvColumnAfterDpchC2I});
            this.ListViewCellReselectAna.ContextMenuStrip = this.ctxMenu;
            this.ListViewCellReselectAna.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewCellReselectAna.FullRowSelect = true;
            this.ListViewCellReselectAna.GridLines = true;
            this.ListViewCellReselectAna.HeaderWordWrap = true;
            this.ListViewCellReselectAna.IsNeedShowOverlay = false;
            this.ListViewCellReselectAna.Location = new System.Drawing.Point(1, 1);
            this.ListViewCellReselectAna.Name = "ListViewCellReselectAna";
            this.ListViewCellReselectAna.OwnerDraw = true;
            this.ListViewCellReselectAna.ShowGroups = false;
            this.ListViewCellReselectAna.Size = new System.Drawing.Size(1298, 501);
            this.ListViewCellReselectAna.TabIndex = 7;
            this.ListViewCellReselectAna.UseCompatibleStateImageBehavior = false;
            this.ListViewCellReselectAna.View = System.Windows.Forms.View.Details;
            this.ListViewCellReselectAna.VirtualMode = true;
            this.ListViewCellReselectAna.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.ListViewCellReselectAna_MouseDoubleClick);
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            this.olvColumnStatSN.Width = 40;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 120;
            // 
            // olvColumnGridName
            // 
            this.olvColumnGridName.HeaderFont = null;
            this.olvColumnGridName.Text = "网格名称";
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "时间";
            this.olvColumnTime.Width = 80;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "源小区名称";
            // 
            // olvColumnRNCName
            // 
            this.olvColumnRNCName.HeaderFont = null;
            this.olvColumnRNCName.Text = "源RNC名称";
            // 
            // olvColumnCellLAC
            // 
            this.olvColumnCellLAC.HeaderFont = null;
            this.olvColumnCellLAC.Text = "源LAC";
            // 
            // olvColumnCellCI
            // 
            this.olvColumnCellCI.HeaderFont = null;
            this.olvColumnCellCI.Text = "源CI";
            // 
            // olvColumnCellDistance
            // 
            this.olvColumnCellDistance.HeaderFont = null;
            this.olvColumnCellDistance.Text = "源小区与事件距离";
            // 
            // olvColumnBeforePccpchRscp
            // 
            this.olvColumnBeforePccpchRscp.HeaderFont = null;
            this.olvColumnBeforePccpchRscp.Text = "重选前PCCPCHRSCP";
            // 
            // olvColumnBeforePccpchC2I
            // 
            this.olvColumnBeforePccpchC2I.HeaderFont = null;
            this.olvColumnBeforePccpchC2I.Text = "重选前PCCPCHC/I";
            // 
            // olvColumnBeforeDpchRscp
            // 
            this.olvColumnBeforeDpchRscp.HeaderFont = null;
            this.olvColumnBeforeDpchRscp.Text = "重选前DPCHRSCP";
            // 
            // olvColumnBeforeDpchC2I
            // 
            this.olvColumnBeforeDpchC2I.HeaderFont = null;
            this.olvColumnBeforeDpchC2I.Text = "重选前DPCHC/I";
            // 
            // olvColumnDestCellName
            // 
            this.olvColumnDestCellName.HeaderFont = null;
            this.olvColumnDestCellName.Text = "目的小区名称";
            // 
            // olvColumnDestRNCName
            // 
            this.olvColumnDestRNCName.HeaderFont = null;
            this.olvColumnDestRNCName.Text = "目的RNC名称";
            // 
            // olvColumnDestLAC
            // 
            this.olvColumnDestLAC.HeaderFont = null;
            this.olvColumnDestLAC.Text = "目的LAC";
            // 
            // olvColumnDestCI
            // 
            this.olvColumnDestCI.HeaderFont = null;
            this.olvColumnDestCI.Text = "目的CI";
            // 
            // olvColumnDestDistance
            // 
            this.olvColumnDestDistance.HeaderFont = null;
            this.olvColumnDestDistance.Text = "目的小区与事件距离";
            // 
            // olvColumnAfterPccpchRscp
            // 
            this.olvColumnAfterPccpchRscp.HeaderFont = null;
            this.olvColumnAfterPccpchRscp.Text = "重选后PCCPCHRSCP";
            // 
            // olvColumnAfterPccpchC2I
            // 
            this.olvColumnAfterPccpchC2I.HeaderFont = null;
            this.olvColumnAfterPccpchC2I.Text = "重选后PCCPCHC/I";
            // 
            // olvColumnAfterDpchRscp
            // 
            this.olvColumnAfterDpchRscp.HeaderFont = null;
            this.olvColumnAfterDpchRscp.Text = "重选后DPCHRSCP";
            // 
            // olvColumnAfterDpchC2I
            // 
            this.olvColumnAfterDpchC2I.HeaderFont = null;
            this.olvColumnAfterDpchC2I.Text = "重选后DPCHC/I";
            // 
            // ZTTDCellReselectAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1300, 502);
            this.Controls.Add(this.ListViewCellReselectAna);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTTDCellReselectAnaListForm";
            this.Text = "重选前后指标分析结果";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCellReselectAna)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private BrightIdeasSoftware.TreeListView ListViewCellReselectAna;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnRNCName;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforePccpchRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforePccpchC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private BrightIdeasSoftware.OLVColumn olvColumnDestCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforeDpchRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnDestRNCName;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellCI;
        private BrightIdeasSoftware.OLVColumn olvColumnDestLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnDestCI;
        private BrightIdeasSoftware.OLVColumn olvColumnCellDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnDestDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnGridName;
        private BrightIdeasSoftware.OLVColumn olvColumnBeforeDpchC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterPccpchRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterPccpchC2I;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterDpchRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterDpchC2I;

    }
}