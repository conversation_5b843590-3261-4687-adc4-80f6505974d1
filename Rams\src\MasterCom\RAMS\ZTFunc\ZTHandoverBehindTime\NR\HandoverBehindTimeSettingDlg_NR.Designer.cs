﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class HandoverBehindTimeSettingDlg_NR
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.numNrSvrRsrp = new DevExpress.XtraEditors.SpinEdit();
            this.chkNrConnect = new DevExpress.XtraEditors.CheckEdit();
            this.numNrMaxNCellRsrp = new DevExpress.XtraEditors.SpinEdit();
            this.numNrRsrpDiff = new DevExpress.XtraEditors.SpinEdit();
            this.numNrStaySecond = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBoxLTE = new System.Windows.Forms.GroupBox();
            this.label9 = new System.Windows.Forms.Label();
            this.numLteStaySecond = new DevExpress.XtraEditors.SpinEdit();
            this.label10 = new System.Windows.Forms.Label();
            this.numLteRsrpDiff = new DevExpress.XtraEditors.SpinEdit();
            this.label11 = new System.Windows.Forms.Label();
            this.numLteMaxNCellRsrp = new DevExpress.XtraEditors.SpinEdit();
            this.label12 = new System.Windows.Forms.Label();
            this.chkLteConnect = new DevExpress.XtraEditors.CheckEdit();
            this.label13 = new System.Windows.Forms.Label();
            this.numLteSvrRsrp = new DevExpress.XtraEditors.SpinEdit();
            this.label14 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.chkLteHandover = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.numNrSvrRsrp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkNrConnect.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNrMaxNCellRsrp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNrRsrpDiff.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNrStaySecond.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBoxLTE.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLteStaySecond.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLteRsrpDiff.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLteMaxNCellRsrp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkLteConnect.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLteSvrRsrp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkLteHandover.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(96, 25);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "主服电平≤";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.Location = new System.Drawing.Point(72, 55);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(89, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "最强邻区电平≥";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label3.Location = new System.Drawing.Point(18, 86);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(143, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "最强邻区电平-主服电平≥";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(96, 117);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "持续时间≥";
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(243, 375);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 2;
            this.btnOK.Text = "确定";
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(337, 375);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 2;
            this.btnCancel.Text = "取消";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(258, 25);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(23, 12);
            this.label5.TabIndex = 0;
            this.label5.Text = "dBm";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(258, 55);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(23, 12);
            this.label6.TabIndex = 0;
            this.label6.Text = "dBm";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(258, 86);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 0;
            this.label7.Text = "dB";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(258, 117);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(17, 12);
            this.label8.TabIndex = 0;
            this.label8.Text = "秒";
            // 
            // numNrSvrRsrp
            // 
            this.numNrSvrRsrp.EditValue = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            this.numNrSvrRsrp.Location = new System.Drawing.Point(169, 20);
            this.numNrSvrRsrp.Name = "numNrSvrRsrp";
            this.numNrSvrRsrp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numNrSvrRsrp.Size = new System.Drawing.Size(82, 21);
            this.numNrSvrRsrp.TabIndex = 4;
            // 
            // chkNrConnect
            // 
            this.chkNrConnect.Location = new System.Drawing.Point(93, 146);
            this.chkNrConnect.Name = "chkNrConnect";
            this.chkNrConnect.Properties.Caption = "采样点为NR连接态";
            this.chkNrConnect.Size = new System.Drawing.Size(120, 19);
            this.chkNrConnect.TabIndex = 5;
            // 
            // numNrMaxNCellRsrp
            // 
            this.numNrMaxNCellRsrp.EditValue = new decimal(new int[] {
            80,
            0,
            0,
            -2147483648});
            this.numNrMaxNCellRsrp.Location = new System.Drawing.Point(169, 50);
            this.numNrMaxNCellRsrp.Name = "numNrMaxNCellRsrp";
            this.numNrMaxNCellRsrp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numNrMaxNCellRsrp.Size = new System.Drawing.Size(82, 21);
            this.numNrMaxNCellRsrp.TabIndex = 6;
            // 
            // numNrRsrpDiff
            // 
            this.numNrRsrpDiff.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numNrRsrpDiff.Location = new System.Drawing.Point(169, 81);
            this.numNrRsrpDiff.Name = "numNrRsrpDiff";
            this.numNrRsrpDiff.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numNrRsrpDiff.Size = new System.Drawing.Size(82, 21);
            this.numNrRsrpDiff.TabIndex = 7;
            // 
            // numNrStaySecond
            // 
            this.numNrStaySecond.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numNrStaySecond.Location = new System.Drawing.Point(169, 112);
            this.numNrStaySecond.Name = "numNrStaySecond";
            this.numNrStaySecond.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numNrStaySecond.Size = new System.Drawing.Size(82, 21);
            this.numNrStaySecond.TabIndex = 8;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.numNrStaySecond);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.numNrRsrpDiff);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.numNrMaxNCellRsrp);
            this.groupBox1.Controls.Add(this.label7);
            this.groupBox1.Controls.Add(this.chkNrConnect);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.numNrSvrRsrp);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(400, 170);
            this.groupBox1.TabIndex = 9;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "NR切换不及时";
            // 
            // groupBoxLTE
            // 
            this.groupBoxLTE.Controls.Add(this.label9);
            this.groupBoxLTE.Controls.Add(this.numLteStaySecond);
            this.groupBoxLTE.Controls.Add(this.label10);
            this.groupBoxLTE.Controls.Add(this.numLteRsrpDiff);
            this.groupBoxLTE.Controls.Add(this.label11);
            this.groupBoxLTE.Controls.Add(this.numLteMaxNCellRsrp);
            this.groupBoxLTE.Controls.Add(this.label12);
            this.groupBoxLTE.Controls.Add(this.chkLteConnect);
            this.groupBoxLTE.Controls.Add(this.label13);
            this.groupBoxLTE.Controls.Add(this.numLteSvrRsrp);
            this.groupBoxLTE.Controls.Add(this.label14);
            this.groupBoxLTE.Controls.Add(this.label15);
            this.groupBoxLTE.Controls.Add(this.label16);
            this.groupBoxLTE.Enabled = false;
            this.groupBoxLTE.Location = new System.Drawing.Point(12, 195);
            this.groupBoxLTE.Name = "groupBoxLTE";
            this.groupBoxLTE.Size = new System.Drawing.Size(400, 171);
            this.groupBoxLTE.TabIndex = 10;
            this.groupBoxLTE.TabStop = false;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label9.Location = new System.Drawing.Point(96, 25);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(65, 12);
            this.label9.TabIndex = 0;
            this.label9.Text = "主服电平≤";
            // 
            // numLteStaySecond
            // 
            this.numLteStaySecond.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numLteStaySecond.Location = new System.Drawing.Point(169, 112);
            this.numLteStaySecond.Name = "numLteStaySecond";
            this.numLteStaySecond.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numLteStaySecond.Size = new System.Drawing.Size(82, 21);
            this.numLteStaySecond.TabIndex = 8;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(258, 25);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(23, 12);
            this.label10.TabIndex = 0;
            this.label10.Text = "dBm";
            // 
            // numLteRsrpDiff
            // 
            this.numLteRsrpDiff.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numLteRsrpDiff.Location = new System.Drawing.Point(169, 81);
            this.numLteRsrpDiff.Name = "numLteRsrpDiff";
            this.numLteRsrpDiff.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numLteRsrpDiff.Size = new System.Drawing.Size(82, 21);
            this.numLteRsrpDiff.TabIndex = 7;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(258, 55);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(23, 12);
            this.label11.TabIndex = 0;
            this.label11.Text = "dBm";
            // 
            // numLteMaxNCellRsrp
            // 
            this.numLteMaxNCellRsrp.EditValue = new decimal(new int[] {
            80,
            0,
            0,
            -2147483648});
            this.numLteMaxNCellRsrp.Location = new System.Drawing.Point(169, 50);
            this.numLteMaxNCellRsrp.Name = "numLteMaxNCellRsrp";
            this.numLteMaxNCellRsrp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numLteMaxNCellRsrp.Size = new System.Drawing.Size(82, 21);
            this.numLteMaxNCellRsrp.TabIndex = 6;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(258, 86);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(17, 12);
            this.label12.TabIndex = 0;
            this.label12.Text = "dB";
            // 
            // chkLteConnect
            // 
            this.chkLteConnect.Location = new System.Drawing.Point(93, 146);
            this.chkLteConnect.Name = "chkLteConnect";
            this.chkLteConnect.Properties.Caption = "采样点为LTE连接态";
            this.chkLteConnect.Size = new System.Drawing.Size(141, 19);
            this.chkLteConnect.TabIndex = 5;
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(258, 117);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(17, 12);
            this.label13.TabIndex = 0;
            this.label13.Text = "秒";
            // 
            // numLteSvrRsrp
            // 
            this.numLteSvrRsrp.EditValue = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            this.numLteSvrRsrp.Location = new System.Drawing.Point(169, 20);
            this.numLteSvrRsrp.Name = "numLteSvrRsrp";
            this.numLteSvrRsrp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numLteSvrRsrp.Size = new System.Drawing.Size(82, 21);
            this.numLteSvrRsrp.TabIndex = 4;
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label14.Location = new System.Drawing.Point(72, 55);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(89, 12);
            this.label14.TabIndex = 0;
            this.label14.Text = "最强邻区电平≥";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label15.Location = new System.Drawing.Point(18, 86);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(143, 12);
            this.label15.TabIndex = 0;
            this.label15.Text = "最强邻区电平-主服电平≥";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label16.Location = new System.Drawing.Point(96, 117);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(65, 12);
            this.label16.TabIndex = 0;
            this.label16.Text = "持续时间≥";
            // 
            // chkLteHandover
            // 
            this.chkLteHandover.Location = new System.Drawing.Point(18, 190);
            this.chkLteHandover.Name = "chkLteHandover";
            this.chkLteHandover.Properties.Caption = "LTE切换不及时";
            this.chkLteHandover.Size = new System.Drawing.Size(107, 19);
            this.chkLteHandover.TabIndex = 9;
            this.chkLteHandover.CheckedChanged += new System.EventHandler(this.chkLteHandover_CheckedChanged);
            // 
            // HandoverBehindTimeSettingDlg_NR
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(424, 407);
            this.Controls.Add(this.chkLteHandover);
            this.Controls.Add(this.groupBoxLTE);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "HandoverBehindTimeSettingDlg_NR";
            this.Text = "切换不及时条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numNrSvrRsrp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkNrConnect.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNrMaxNCellRsrp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNrRsrpDiff.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNrStaySecond.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBoxLTE.ResumeLayout(false);
            this.groupBoxLTE.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLteStaySecond.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLteRsrpDiff.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLteMaxNCellRsrp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkLteConnect.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numLteSvrRsrp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkLteHandover.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label8;
        private DevExpress.XtraEditors.SpinEdit numNrSvrRsrp;
        private DevExpress.XtraEditors.CheckEdit chkNrConnect;
        private DevExpress.XtraEditors.SpinEdit numNrMaxNCellRsrp;
        private DevExpress.XtraEditors.SpinEdit numNrRsrpDiff;
        private DevExpress.XtraEditors.SpinEdit numNrStaySecond;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBoxLTE;
        private System.Windows.Forms.Label label9;
        private DevExpress.XtraEditors.SpinEdit numLteStaySecond;
        private System.Windows.Forms.Label label10;
        private DevExpress.XtraEditors.SpinEdit numLteRsrpDiff;
        private System.Windows.Forms.Label label11;
        private DevExpress.XtraEditors.SpinEdit numLteMaxNCellRsrp;
        private System.Windows.Forms.Label label12;
        private DevExpress.XtraEditors.CheckEdit chkLteConnect;
        private System.Windows.Forms.Label label13;
        private DevExpress.XtraEditors.SpinEdit numLteSvrRsrp;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label16;
        private DevExpress.XtraEditors.CheckEdit chkLteHandover;
    }
}