﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Func.ProblemBlock;

namespace MasterCom.RAMS.CQT
{
    public class CQTProblemSummaryAnalysTD : QueryBase
    {
        public CQTProblemSummaryAnalysTD(MainModel mainModel, string netType)
        : base(mainModel)
        {
            if (netType == "TD")
            {
                if (CQTProblemSummaryAnalysGSM.cityLevel(mainModel.DistrictID) != "")
                    iareatype = 25;
                else
                    iareatype = -200;
            }
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        public override string Name
        {
            get { return "3G问题点统计分析"; }
        }
        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13057, this.Name);
        }
        #region 全局变量
        public static List<ProblemSummaryPont3G> problemPointSummarryList { get; set; } = new List<ProblemSummaryPont3G>();
        public static List<ProblemPont3G> problemPointList { get; set; } = new List<ProblemPont3G>();     
        readonly Dictionary<int, FileInfoItem> CQTFileDic = new Dictionary<int, FileInfoItem>();
        readonly Dictionary<int, FileInfoItem> CQTFileDicCount = new Dictionary<int, FileInfoItem>();
        readonly List<ProblemItem> result = new List<ProblemItem>();
        SumarryDdataInfoTD sumarryInfo;
        private bool pointNum = false;
        readonly int iareatype = 0;       
        int strcityid = 0;
        int iNo = 1;
        int newpoint = 0;
        string strCqtProblemType = "";
        #endregion
        protected override void query()
        {
            int cityIDTem = 0;
            if (MainModel.User.DBID == -1)
            {
                cityIDTem = MainModel.DistrictID;
                MainModel.DistrictID = 2;
            }
            DIYQueryTBTypeAll diyTem = new DIYQueryTBTypeAll(MainModel, "tb_func_cqt%");
            diyTem.Query();
            List<string> dBName = new List<string>();
            if (diyTem.dbNameList.Count > 0)
                dBName.Add("通报问题点");
            diyTem = new DIYQueryTBTypeAll(MainModel, "tb_case_cqt%");
            diyTem.Query();
            if (diyTem.dbNameList.Count > 0)
                dBName.Add("派单问题点");
            if (MainModel.User.DBID == -1)
                MainModel.DistrictID = cityIDTem;
            CQTProblemType cqtType = new CQTProblemType(dBName);
            if (cqtType.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            strCqtProblemType = cqtType.strCQTProblemType;
            WaitBox.Show("准备获取CQT点...", seachProblemData);
            showData();
        }
        /// <summary>
        /// 准备查询数据的条件
        /// </summary>
        private void seachProblemData()
        {
            problemPointSummarryList.Clear();
            problemPointList.Clear();
            sumarryInfo = new SumarryDdataInfoTD();     
            WaitBox.ProgressPercent = 20;
            problemPointSummarryList.Clear();
            iNo = 1;
            DateTime sDtime = condition.Periods[0].BeginTime;
            DateTime eDtime = condition.Periods[0].EndTime.AddSeconds(-1);
            string project = "";
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < condition.Projects.Count; i++)
            {
                if (i < condition.Projects.Count - 1)
                    sb.Append(condition.Projects[i].ToString() + ",");
                else
                    sb.Append(condition.Projects[i].ToString());
            }
            project = sb.ToString();
            List<string> logList = getLogFileNameList(sDtime, eDtime);  
            if (MainModel.User.DBID == -1)
            {
                WaitBox.ProgressPercent = 30;
                List<int> disid = new List<int>();
                //按特定顺序排列查询各是数据
                int[] zhidingID = { 1, 2, 3, 12, 6, 5, 4, 10, 13, 14, 16, 17, 21, 7, 8, 9, 11, 15, 18, 19, 20, 22, 23 };
                for (int idk = 0; idk < zhidingID.Length; idk++)
                {
                    if (condition.DistrictIDs.Contains(zhidingID[idk]))
                    {
                        disid.Add(zhidingID[idk]);
                    }
                }
                for (int chdistrid = 0; chdistrid < disid.Count; chdistrid++)
                {
                    CQTFileDic.Clear();
                    CQTFileDicCount.Clear();
                    MainModel.DistrictID = disid[chdistrid];
                    strcityid = disid[chdistrid];
                    seachProblemPointData(strcityid, sDtime, eDtime, project, logList);
                }             
            }
            else
            {
                CQTFileDic.Clear();
                CQTFileDicCount.Clear();
                strcityid = MainModel.DistrictID;
                seachProblemPointData(MainModel.DistrictID, sDtime, eDtime, project, logList);
            }
            WaitBox.ProgressPercent = 80;
            if (problemPointSummarryList.Count != 0)
            {
                WaitBox.Text = "正在进行汇总处理...";
                CQTProblemSummaryAnalysTD.problemPointSummarryList.Add(ProblemSummaryPont3G.fillDataTotal(sumarryInfo));
            }
            WaitBox.Close();
        }
        /// <summary>
        /// 真正查询问题点记录的过程
        /// </summary>
        private void seachProblemPointData(int cityId, DateTime sDtime, DateTime eDtime, string project, List<string> logList)
        {
            WaitBox.Text = "正在获取 " + DistrictManager.GetInstance().getDistrictName(cityId) + " 市的CQT点...";

            foreach (string log in logList)
            {
                DIYQueryCQTFileInfo diyFileInfo = new DIYQueryCQTFileInfo(MainModel, log, iareatype, project, true, sDtime, eDtime);
                diyFileInfo.Query();//查询测试地点的文件列表
                foreach (int idk in diyFileInfo.CQTFileDic.Keys)
                {
                    if (!CQTFileDic.ContainsKey(idk))
                        CQTFileDic.Add(idk, diyFileInfo.CQTFileDic[idk]);
                }
                DIYQueryCQTFileInfo diyTestCont = new DIYQueryCQTFileInfo(MainModel, log, iareatype, project, false, sDtime, eDtime);
                diyTestCont.Query();//查询测试总数
                foreach (int idk in diyTestCont.CQTFileCount.Keys)
                {
                    if (!CQTFileDicCount.ContainsKey(idk))
                        CQTFileDicCount.Add(idk, diyTestCont.CQTFileCount[idk]);
                }
            }
            DIYQueryEventLoLaInfo diyProblemLoLa = new DIYQueryEventLoLaInfo(MainModel, iareatype);
            diyProblemLoLa.Query();//查询问题点地点的经纬度
            DIYQueryCQTProblemInfo diyProblemInfo = new DIYQueryCQTProblemInfo(MainModel, iareatype,
                sDtime, eDtime, project, strCqtProblemType);
            diyProblemInfo.Query();//查测试地点问题点各字段
            int iareatypeid = 0;
            if (diyProblemInfo.CQTPromblemDic.Count == 0)
            {
                Dictionary<int, CQTPointTem> CQTPointDic = new Dictionary<int, CQTPointTem>();
                Dictionary<int, string> areaStrCover = new Dictionary<int, string>();
                Dictionary<int, ProblemLoLaInfo> CQTPromblemLoLaDic = new Dictionary<int, ProblemLoLaInfo>();
                dealWithDataXQ(CQTPointDic, diyProblemInfo.CQTPromblemDic, areaStrCover, CQTPromblemLoLaDic);
                dealWithSummarryData();
                return;
            }
            foreach (CQTProjectAreaIDKey proArea in diyProblemInfo.CQTPromblemDic.Keys)
            {
                iareatypeid = diyProblemInfo.CQTPromblemDic[proArea].IAreaType;
                if (iareatypeid > 0)
                {
                    break;
                }
            }
            MasterCom.RAMS.Stat.DIYQueryCQTPointInfo dqcpi = new MasterCom.RAMS.Stat.DIYQueryCQTPointInfo(MainModel, iareatypeid);
            dqcpi.Query();//查测试地点属性与地点名称
            WaitBox.ProgressPercent = WaitBox.ProgressPercent + 1;

            DIYQueryCQTCoverInfo dCoverInfo = new DIYQueryCQTCoverInfo(MainModel, iareatypeid);
            dCoverInfo.Query();//查询覆盖属性，因为点的属性查询类关联太广，而且覆盖属性表只有广东有
            //所以，分开来查询

            dealWithDataXQ(dqcpi.CQTPointDic, diyProblemInfo.CQTPromblemDic, dCoverInfo.areaStrCover, diyProblemLoLa.CQTPromblemLoLaDic);
            dealWithSummarryData();
        }
        /// <summary>
        /// 获取tb_log表名
        /// </summary>
        private List<string> getLogFileNameList(DateTime tmpDate, DateTime eDtime)
        {
            List<string> logList = new List<string>();
            while (tmpDate <= eDtime)
            {
                string strLogName = string.Format("tb_log_file_{0:yyyy}_{0:MM}", tmpDate);
                if (!logList.Contains(strLogName))
                {
                    logList.Add(strLogName);
                }
                tmpDate = tmpDate.AddDays(1);
            }
            return logList;
        }
        /// <summary>
        /// 处理数据
        /// </summary>
        private void dealWithSummarryData()
        {
            XQDataInfoTD xqInfo = new XQDataInfoTD();
            if (pointNum)
            {
                for (int id = problemPointList.Count - newpoint; id < problemPointList.Count; id++)
                {
#if GDProjectOrder
                    if (isMergeProject(problemPointList.Count - newpoint, id, ref xqInfo))
                    {
                        continue;
                    }
#endif
                    addXQDataInfoCount(xqInfo, id);
                }
            }
            ProblemSummaryPont3G lisSTem = ProblemSummaryPont3G.fillXQDataTD(xqInfo, CQTFileDicCount.Count, strcityid);
            CQTProblemSummaryAnalysTD.problemPointSummarryList.Add(lisSTem);
            SumarryDdataInfoTD.fillSummarryData(ref sumarryInfo, xqInfo, CQTFileDicCount.Count, strcityid);
        }

        private static void addXQDataInfoCount(XQDataInfoTD xqInfo, int id)
        {
            string type = problemPointList[id].SMainType;
            switch(type)
            {
                case "弱覆盖":
                    xqInfo.iweakcoverage++;
                    break;
                case "未接通":
                    xqInfo.inotconnected++;
                    break;
                case "掉话":
                    xqInfo.idroppedcalls++;
                    break;
                case "深度覆盖不足":
                case "覆盖不足":
                    xqInfo.ilessdowntdn++;
                    break;
                case "下载速率低":
                    xqInfo.idownlessother++;
                    break;
                case "单用户下载速率低":
                    xqInfo.isingleDownLess++;
                    break;
                case "多用户下载速率低":
                    xqInfo.imostDownLess++;
                    break;
                case "下载掉线":
                    xqInfo.idowndrop++;
                    break;
                case "下载超时":
                    xqInfo.idowntimeout++;
                    break;
                case "异系统切换失败":
                    xqInfo.ifallTDToGSM++;
                    break;
                case "TD深度覆盖不达标":
                    xqInfo.ilessthendeepTD++;
                    break;
                case "T劣":
                    xqInfo.itlessHG++;
                    break;
                case "竞对弱覆盖":
                    xqInfo.icompareWeak++;
                    break;
                case "竞对下载速率低":
                    xqInfo.icompareDownLess++;
                    break;
                case "全程呼叫成功率比竞争对手差":
                    xqInfo.icallSuccessLess++;
                    break;
            }
        }

        /// <summary>
        /// 对事前信息进行处理，以便对应原来的统计接口
        /// </summary>
        private void dealWithDataXQ(Dictionary<int, CQTPointTem> cqtNameDic, Dictionary<CQTProjectAreaIDKey, ProblemItem> CQTPromblemDic, 
            Dictionary<int, string> areaStrCoverDic, Dictionary<int, ProblemLoLaInfo> CQTPromblemLoLaDic)
        {
            result.Clear();
            addResult(cqtNameDic, CQTPromblemDic, areaStrCoverDic, CQTPromblemLoLaDic);
            if (result.Count > 0)
                pointNum = true;
            else
                pointNum = false;
            newpoint = 0;
            int mainProblemID = 0;
            for (int id = 0; id < result.Count; id++)
            {
                if (result[id].probFileList.Count != 0 && result[id].probFileList[0].ToString() != "")
                {
                    newpoint++;
                    ProblemPont3G lis1 = new ProblemPont3G();
                    mainProblemID = setProblemPont3GSMainType(mainProblemID, id, lis1);

                    setProblemPont3GSSecondType(mainProblemID, id, lis1);

                    setProblemPont3GInfo(id, lis1);

                    problemPointList.Add(lis1);
                }
            }
        }

        private void addResult(Dictionary<int, CQTPointTem> cqtNameDic, Dictionary<CQTProjectAreaIDKey, ProblemItem> CQTPromblemDic, 
            Dictionary<int, string> areaStrCoverDic, Dictionary<int, ProblemLoLaInfo> CQTPromblemLoLaDic)
        {
            foreach (CQTProjectAreaIDKey projArea in CQTPromblemDic.Keys)
            {
                ProblemItem pT = CQTPromblemDic[projArea];
                pT.StrCoverType = "非室分";
                if (cqtNameDic.ContainsKey(projArea.IAreaID))
                {
                    pT.Strcqtname = cqtNameDic[projArea.IAreaID].Strareaname;
                    pT.Strcqttype = cqtNameDic[projArea.IAreaID].Strcomment;
                    pT.probCellInfoList = getProbCellInfo(pT.StrCauseInfo);
                }
                if (CQTPromblemLoLaDic.ContainsKey(projArea.IAreaID))
                {
                    pT.DLongitude = CQTPromblemLoLaDic[projArea.IAreaID].DLongitude;
                    pT.DLatitude = CQTPromblemLoLaDic[projArea.IAreaID].DLatitude;
                }
                if (areaStrCoverDic.ContainsKey(projArea.IAreaID))
                {
                    pT.StrCoverType = areaStrCoverDic[projArea.IAreaID];
                }
                pT.probFileList = getFileNameList(pT);
                if (pT.Strcqtname != null && pT.Strcqttype != null && pT.Strcqtname != "")
                {
                    result.Add(pT);
                }
            }
        }

        private int setProblemPont3GSMainType(int mainProblemID, int id, ProblemPont3G lis1)
        {
            if (result[id].StrMainProblem2 != "")
            {
                lis1.SMainType = result[id].StrMainProblem2;
                lis1.SPointPosition = CQTProblemSummaryAnalysGSM.getsub(result[id].StrTestSite, 2);
                mainProblemID = 0;
            }
            else if (result[id].StrMainProblem3 != "")
            {
                lis1.SMainType = result[id].StrMainProblem3;
                lis1.SPointPosition = CQTProblemSummaryAnalysGSM.getsub(result[id].StrTestSite, 3);
                mainProblemID = 1;
            }
            else if (result[id].StrMainProblem1 != "")
            {
                lis1.SMainType = result[id].StrMainProblem1;
                lis1.SPointPosition = CQTProblemSummaryAnalysGSM.getsub(result[id].StrTestSite, 1);
                mainProblemID = 2;
            }
            else if (result[id].StrSecProblem1 != "")
            {
                lis1.SMainType = result[id].StrSecProblem1;
                mainProblemID = 3;
            }

            return mainProblemID;
        }

        private void setProblemPont3GSSecondType(int mainProblemID, int id, ProblemPont3G lis1)
        {
            if (mainProblemID == 0)
            {
                lis1.SSecondType = "";
                addSSecondType(lis1, result[id].StrMainProblem3, true);
                addSSecondType(lis1, result[id].StrMainProblem1, true);
                addSSecondType(lis1, result[id].StrSecProblem1, false);
            }
            else if (mainProblemID == 1)
            {
                lis1.SSecondType = "";
                addSSecondType(lis1, result[id].StrMainProblem1, true);
                addSSecondType(lis1, result[id].StrSecProblem1, false);
            }
            else if (mainProblemID == 2)
            {
                lis1.SSecondType = "";
                addSSecondType(lis1, result[id].StrSecProblem1, false);
            }
            else if (mainProblemID == 3)
            {
                lis1.SSecondType = "";
            }
        }

        private void addSSecondType(ProblemPont3G lis1, string problem, bool addSplit)
        {
            if (problem != "")
            {
                lis1.SSecondType += problem;
            }
            if (addSplit)
            {
                lis1.SSecondType += ",";
            }
        }

        private void setProblemPont3GInfo(int id, ProblemPont3G lis1)
        {
            int ifile;
            int ilac = 0, ici = 0;
            lis1.IID = iNo++;
            lis1.SCity = DistrictManager.GetInstance().getDistrictName(result[id].Icity);
            lis1.SProj = CQTProblemSummaryAnalysGSM.GetProjectNameByProjectID(result[id].IProject);
            lis1.STestTime = result[id].Dtime;
            lis1.STestPoint = result[id].Strcqtname;
            lis1.DLongitude = result[id].DLongitude;
            lis1.DLatitude = result[id].DLatitude;
            lis1.SSitePro = result[id].Strcqttype;
            lis1.StrCoverType = result[id].StrCoverType;
            lis1.StrValue8 = result[id].StrValue8;
            lis1.SImportLeve = "-";
            StringBuilder strFile = new StringBuilder();
            for (ifile = 0; ifile < result[id].probFileList.Count; ifile++)
            {
                strFile.Append(result[id].probFileList[ifile].ToString() + "；");
            }
            lis1.STestFilePosition = strFile.ToString();
            StringBuilder strCell = new StringBuilder();
            for (ifile = 0; ifile < result[id].probCellInfoList.Count; ifile++)
            {
                ilac = result[id].probCellInfoList[ifile].Ilac;
                ici = result[id].probCellInfoList[ifile].Ici;
                if (result[id].probCellInfoList[ifile].Strcellname == "")
                    strCell.Append("(" + ilac + "_" + ici + "),采样点共" + result[id].probCellInfoList[ifile].Isampleid + "个,场强为" + result[id].probCellInfoList[ifile].Irxlev + "dBm" + ";");
                else
                    strCell.Append(result[id].probCellInfoList[ifile].Strcellname + "(" + ilac + "_" + ici + "),采样点共" + result[id].probCellInfoList[ifile].Isampleid + "个,场强为" + result[id].probCellInfoList[ifile].Irxlev + "dBm" + ";");
            }
            lis1.SReasonAna = strCell.ToString();
            lis1.SReasonType = result[id].StrCauseType;
            lis1.SPreliminaryAna = CQTProblemSummaryAnalysGSM.getSummaryDesc(result[id], "TD", lis1.SReasonAna);
        }

#if GDProjectOrder
        /// <summary>
        /// 合并不同项目
        /// </summary>
        private bool isMergeProject(int iPerIndex, int iCurIndex, ref XQDataInfoTD xqInfo)
        {
            bool isMergeProject = false;
            for (int id = iPerIndex; id < iCurIndex; id++)
            {
                if (problemPointList[id].STestPoint.Equals(problemPointList[iCurIndex].STestPoint))
                {
                    isMergeProject = true;
                    if (isChangeIndex(problemPointList[id].SMainType, problemPointList[iCurIndex].SMainType))
                    {
                        isMergeProject = false;
                        if (problemPointList[id].SMainType.Contains("T劣"))
                        {
                            xqInfo.itlessHG -= 1;
                        }
                        else if (problemPointList[id].SMainType.Contains("掉话"))
                        {
                            xqInfo.idroppedcalls -= 1;
                        }
                        else if (problemPointList[id].SMainType.Contains("未接通"))
                        {
                            xqInfo.inotconnected -= 1;
                        }
                        else if (problemPointList[id].SMainType.Contains("覆盖不足"))
                        {
                            xqInfo.ilessdowntdn -= 1;
                        }
                        else if (problemPointList[id].SMainType.Contains("单用户下载速率低"))
                        {
                            xqInfo.isingleDownLess -= 1;
                        }
                        else if (problemPointList[id].SMainType.Contains("下载掉线"))
                        {
                            xqInfo.idowndrop -= 1;
                        }
                    }
                }
            }
            return isMergeProject;
        }

        /// <summary>
        /// 判断优先级
        /// </summary>
        private bool isChangeIndex(string strPerProblem, string strCurProblem)
        {
            string[] problem = { "T劣", "掉话", "未接通", "覆盖不足", "单用户下载速率低" };
            string[] problemOrder = { "掉话", "未接通", "下载掉线", "单用户下载速率低" };
            int iPerProblem = -1;
            int iCurProblem = -1;
            if (strCqtProblemType.Contains("派单"))
            {
                problem = problemOrder;
            }
            for (int i = 0; i < problem.Length; i++)
            {
                if (strPerProblem.Contains(problem[i]))
                {
                    iPerProblem = i;
                }

                if (strCurProblem.Contains(problem[i]))
                {
                    iCurProblem = i;
                }
            }
            if (iPerProblem == -1 || iCurProblem == -1)
            {
                return false;
            }
            else
            {
                return iCurProblem < iPerProblem;
            }
        }
#endif
        /// <summary>
        /// 获取对应文件名列表
        /// </summary>
        private List<string> getFileNameList(ProblemItem pT)
        {
            List<String> fileNameList = new List<String>();
            string strFilePart = "";
            string[] filePart = pT.StrLogInfo.Split('|');
             if (pT.StrMainProblem2 != "" && filePart.Length > 1)
                strFilePart = filePart[1];
            else if (pT.StrMainProblem3 != "" && filePart.Length > 2)
                strFilePart = filePart[2];
            else
                strFilePart = filePart[0];
            string[] fileids = strFilePart.Split(',');
            foreach (string strfileid in fileids)
            {
                try
                {
                    int ifileid = Convert.ToInt32(strfileid);
                    if (CQTFileDic.ContainsKey(ifileid))
                        fileNameList.Add(CQTFileDic[ifileid].StrfileName);
                }
                catch
                {
                    //continue
                }
            }
            return fileNameList;
        }
        /// <summary>
        /// 问题点信息详情列表
        /// </summary>
        private List<ProbCellInfo> getProbCellInfo(string probInfo)
        {
            List<ProbCellInfo> pcInfo = new List<ProbCellInfo>();
            string[] probCell = probInfo.Split('|');
            foreach (string prob in probCell)
            {
                try
                {
                    string[] info = prob.Split(',');
                    ProbCellInfo cpi = new ProbCellInfo();
                    cpi.Strcellname = info[0].ToString();
                    cpi.Ilac = Convert.ToInt32(info[1]);
                    cpi.Ici = Convert.ToInt32(info[2]);
                    cpi.Isampleid = Convert.ToInt32(info[3]);
                    cpi.Irxlev = Convert.ToInt32(info[4]);
                    pcInfo.Add(cpi);
                }
                catch
                {
                    //
                }
            }
            return pcInfo;
        }        
        /// <summary>
        /// 显示处理结果窗体
        /// </summary>
        private void showData()
        {
            CQTProblemSummary3G cqtproblemShowForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(CQTProblemSummary3G).FullName);
            cqtproblemShowForm = obj == null ? null : obj as CQTProblemSummary3G;
            if (cqtproblemShowForm == null || cqtproblemShowForm.IsDisposed)
            {
                cqtproblemShowForm = new CQTProblemSummary3G(MainModel, strCqtProblemType);
            }
            cqtproblemShowForm.filldataSummary3G(1); 
            cqtproblemShowForm.Show(MainModel.MainForm);
        }
    }
}
