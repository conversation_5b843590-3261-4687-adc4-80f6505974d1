﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Util;
using Excel = Microsoft.Office.Interop.Excel;
using MasterCom.RAMS.Model.Interface;
using DevExpress.XtraCharts;
using System.IO;

namespace MasterCom.RAMS.Func.PopShow
{
    public partial class AgentWorkPanel_ng : UserControl, PopShowPanelInterface
    {
        private static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        //第一命令字：config
        public const byte REQTYPE_CONFIG_KPI_TABLE = 0x2b;  //REQUEST
        public const byte RESTYPE_CONFIG_KPI_TABLE = 0x2b;

        //第一命令字：查询    启动预读
        public const byte REQTYPE_TABLE_DATA_INFO = 0x83; //REQUEST 
        public const byte RESTYPE_TABLE_DATA_INFO = 0x83; 
        private MainModel MainModel;
        public Dictionary<string, Object> colorRangeDic { get; set; }

        public AgentWorkPanel_ng()
        {
            InitializeComponent();
            initShowInfo();

            curRetDataDic = new Dictionary<string, KPIPopTable>();
        }

        private void initShowInfo()
        {
            cbxShowType.Items.Add("本月初至今");
            cbxShowType.Items.Add("最近一周");
            cbxShowType.Items.Add("最近一月");
            cbxShowType.Items.Add("按周");
            cbxShowType.Items.Add("按月");
            cbxShowType.SelectedIndex = 0;
        }

        #region PopShowPanelInterface 成员

        public void RunQuery(BackgroundWorker worker, TaskInfo task)
        {
            isProvUser = MainModel.User.DBID == -1;
            curDrillLevel = 0;
            Dictionary<string, KPIPopTable> entryHeaderDic = queryPopEntry(worker);
            foreach (string tbkey in entryHeaderDic.Keys)
            {
                KPIPopTable hdUnit = entryHeaderDic[tbkey];
                hdUnit.initFinderDic();
                List<KPIResultInfo> resultList = queryResultFromHeader(worker, hdUnit, MainModel.User.DBID);
                hdUnit.cityDataResult = buildCityStruct(resultList);
            }
            task.retResultInfo = entryHeaderDic;
           
        }

        private Dictionary<string, List<KPIResultInfo>> buildCityStruct(List<KPIResultInfo> resultList)
        {
            Dictionary<string, List<KPIResultInfo>> cityDic = new Dictionary<string, List<KPIResultInfo>>();
            foreach(KPIResultInfo info in resultList)
            {
                string strcity = DistrictManager.GetInstance().getDistrictName(info.dbid);
                List<KPIResultInfo> list = null;
                if(!cityDic.TryGetValue(strcity,out list))
                {
                    list = new List<KPIResultInfo>();
                    cityDic[strcity] = list;
                }
                list.Add(info);
            }
            return cityDic;
        }

        private List<KPIResultInfo> queryResultFromHeader(BackgroundWorker worker, KPIPopTable hdUnit,int dbid)
        {
            List<KPIResultInfo> retList = new List<KPIResultInfo>();
            ClientProxy clientProxy = new ClientProxy();

            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                worker.ReportProgress(99, "连接服务器端出错！");
                return retList;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.InfoQuery;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_TABLE_DATA_INFO;
                package.Content.PrepareAddParam();
                package.Content.AddParam(dbid);
                package.Content.AddParam(hdUnit.tablename);
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_TABLE_DATA_INFO)
                    {
                        package.Content.PrepareGetParam();
                        KPIResultInfo retItem = hdUnit.ReadResultItemFrom(package.Content);
                        retList.Add(retItem);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                }
                return retList;
            }

            finally
            {
                clientProxy.Close();
            }

        }
        private Dictionary<string, KPIPopTable> queryPopEntry(BackgroundWorker worker)
        {
            DiySqlPopKpiColor kpiColorQeruyTask = new DiySqlPopKpiColor(MainModel);
            colorRangeDic = kpiColorQeruyTask.colorRangeDic;
            
            Dictionary<string, KPIPopTable> entryDicList = new Dictionary<string, KPIPopTable>();
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                worker.ReportProgress(99,"连接服务器端出错！");
                return entryDicList;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.CellConfigManage;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_CONFIG_KPI_TABLE;
                package.Content.PrepareAddParam();
                clientProxy.Send();
                int rrrAt = 0;
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_CONFIG_KPI_TABLE)
                    {
                        rrrAt = fillData(kpiColorQeruyTask, entryDicList, package, rrrAt);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Info("****Error!"+rrrAt);
                    }
                }
                return entryDicList;
            }
            finally
            {
                clientProxy.Close();
            }
            

        }

        private static int fillData(DiySqlPopKpiColor kpiColorQeruyTask, Dictionary<string, KPIPopTable> entryDicList, Package package, int rrrAt)
        {
            package.Content.PrepareGetParam();
            PopKPIEntryItem entry = new PopKPIEntryItem();
            entry.Fill(package.Content);
            log.Info(rrrAt + "==" + entry.ToString());
            rrrAt++;
            if (entry.strtablename.IndexOf("_agent_") == -1)//特殊的供其它程序使用的，不在KPI显示处显示
            {
                //
            }
            else
            {
                KPIPopTable headerUnit = null;
                if (!entryDicList.TryGetValue(entry.strtablename, out headerUnit))
                {
                    headerUnit = new KPIPopTable();
                    headerUnit.tablename = entry.strtablename;
                    entryDicList[headerUnit.tablename] = headerUnit;
                }
                string key = entry.strtablename + entry.strcolname;
                if (kpiColorQeruyTask.colorRangeDic.ContainsKey(key))
                {
                    entry.colorLst = kpiColorQeruyTask.colorRangeDic[key] as List<DTParameterRangeColor>;
                }
                headerUnit.entryList.Add(entry);
            }

            return rrrAt;
        }
        #endregion

        #region PopShowPanelInterface 成员

        public Dictionary<string, KPIPopTable> curRetDataDic { get; set; }
        public void FireFreshShowData(TaskInfo task)
        {
            if (!(task.retResultInfo is Dictionary<string, KPIPopTable>))
            {
                curRetDataDic.Clear();
            }
            else
            {
                curRetDataDic = task.retResultInfo as Dictionary<string, KPIPopTable>;
            }
            cbxReportSel.Items.Clear();
            foreach(KPIPopTable popTable in curRetDataDic.Values)
            {
                cbxReportSel.Items.Add(popTable);
            }
            if(cbxReportSel.Items.Count>0)
            {
                cbxReportSel.SelectedIndex = 0;
            }
        }

        #endregion

        #region PopShowPanelInterface 成员


        public void SetMainModal(MasterCom.RAMS.Model.MainModel mm, WelcomForm welcomform)
        {
            this.MainModel = mm;
        }

        #endregion

        private void cbxReportSel_SelectedIndexChanged(object sender, EventArgs e)
        {
            if(isProvUser)
            {
                curDrillLevel = 0;
                cityName = "";
            }
            refreshShowReport(true);
        }

        private void cbxShowType_SelectedIndexChanged(object sender, EventArgs e)
        {
            string selShowType = cbxShowType.SelectedItem as string;
            if(selShowType==null)
            {
                return;
            }
            refreshShowReport(false);
        }
        private void refreshShowReport(bool resetContent)
        {
            btnLevel0.Visible = isProvUser;

            dataGridView.Columns.Clear();
            KPIPopTable kpiPopTable = cbxReportSel.SelectedItem as KPIPopTable;
            string selShowType = cbxShowType.SelectedItem as string;
            if(kpiPopTable!=null && selShowType!=null)
            {
                FinalShowResult showRet = parseShowFromTable(kpiPopTable,selShowType);
                if(resetContent)
                {
                    Dictionary<string, bool> namesDic = getNameList(showRet);
                    cbxContentType.Properties.Items.Clear();
                    cbxContentType.Properties.Items.Add("(全部)");
                    foreach (string nm in namesDic.Keys)
                    {
                        cbxContentType.Properties.Items.Add(nm);
                    }
                    cbxContentType.Text = "(全部)";
                }
                showInGrid(showRet);
            }
        }

        private Dictionary<string, bool> getNameList(FinalShowResult showRet)
        {
            Dictionary<string, bool> ret = new Dictionary<string, bool>();
            foreach (List<object> vList in showRet.dataRows)
            {
                ret[vList[1] as string] = true;
            }
            return ret;
        }
        /// <summary>
        /// 是否当前登录的是省用户/多地市用户
        /// </summary>
        private bool isProvUser = false;
        /// <summary>
        /// 当前所选地市
        /// </summary>
        private string cityName = "";
        private int curDrillLevel = 0;//0 全省  1地市片区

        private FinalShowResult parseShowFromTable(KPIPopTable kpiPopTable, string selShowType)
        {
            FinalShowResult sRet = new FinalShowResult();
            kpiPopTable.reloadDataResultByLevel(isProvUser, cityName,selShowType);
            if(selShowType == "本月初至今")
            {
                sRet = prepareShowByMonthToNow(kpiPopTable);
            }
            else if (selShowType == "按月")
            {
                sRet = prepareShowByMonth(kpiPopTable);
            }
            else if (selShowType == "按周")
            {
                sRet = prepareShowByWeek(kpiPopTable);
            }
            else if(selShowType == "最近一月")
            {
                sRet = prepareShowByLastMonth(kpiPopTable);

            }
            else if (selShowType == "最近一周")
            {
                sRet = prepareShowByLastWeek(kpiPopTable);
            }
            return sRet;
        }
        private FinalShowResult prepareShowByLastWeek(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = new FinalShowResult();
            sRet.columnNames.Add("时间");
            sRet.columnNames.Add("名称");
            for (int i = 4; i < kpiPopTable.entryList.Count; i++)
            {
                PopKPIEntryItem entry = kpiPopTable.entryList[i];
                if (entry.strcolname.IndexOf("_base") == -1)
                {
                    sRet.columnNames.Add(entry.strcoldesc);
                }
            }
            KPIPopTable gatheredTable = new KPIPopTable();
            gatheredTable.entryList.AddRange(kpiPopTable.entryList);
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach (KPIResultInfo rinfo in kpiPopTable.dataResult)
            {
                string key = rinfo.dbid + ":" + rinfo.strname + ":" + getWeekStr(rinfo.stime);
                KPIResultInfo ginfo = null;
                if (!retDic.TryGetValue(key, out ginfo))
                {
                    retDic[key] = rinfo.copyInstance();
                    retDic[key].stime = getWeekBeginTime(rinfo.stime);
                }
                else
                {
                    ginfo.Gather(rinfo, kpiPopTable);
                }
            }
            int maxstime = 0;
            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                if (ginfo.stime > maxstime)
                {
                    maxstime = ginfo.stime;
                }
            }
            addSRetRow(sRet, retDic, maxstime);
            return sRet;
        }

        private void addSRetRow(FinalShowResult sRet, Dictionary<string, KPIResultInfo> retDic, int maxstime)
        {
            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                if (ginfo.stime == maxstime)
                {
                    List<object> objList = new List<object>();
                    objList.Add(getWeekStr(ginfo.stime));
                    objList.Add(ginfo.strname);
                    for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                    {
                        object v = ginfo.valueList[i];
                        objList.Add(v);
                    }
                    sRet.dataRows.Add(objList);
                }
            }
        }

        private FinalShowResult prepareShowByWeek(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = new FinalShowResult();
            sRet.columnNames.Add("时间");
            sRet.columnNames.Add("名称");
            for (int i = 4; i < kpiPopTable.entryList.Count; i++)
            {
                PopKPIEntryItem entry = kpiPopTable.entryList[i];
                if (entry.strcolname.IndexOf("_base") == -1)
                {
                    sRet.columnNames.Add(entry.strcoldesc);
                }
            }
            KPIPopTable gatheredTable = new KPIPopTable();
            gatheredTable.entryList.AddRange(kpiPopTable.entryList);
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach (KPIResultInfo rinfo in kpiPopTable.dataResult)
            {
                string key = rinfo.dbid + ":" + rinfo.strname + ":" + getWeekStr(rinfo.stime);
                KPIResultInfo ginfo = null;
                if (!retDic.TryGetValue(key, out ginfo))
                {
                    retDic[key] = rinfo.copyInstance();
                }
                else
                {
                    ginfo.Gather(rinfo, kpiPopTable);
                }
            }

            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                List<object> objList = new List<object>();
                objList.Add(getWeekStr(ginfo.stime));
                objList.Add(ginfo.strname);
                for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = ginfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            return sRet;
        }
        private FinalShowResult prepareShowByLastMonth(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = new FinalShowResult();
            sRet.columnNames.Add("时间");
            sRet.columnNames.Add("名称");
            for (int i = 4; i < kpiPopTable.entryList.Count; i++)
            {
                PopKPIEntryItem entry = kpiPopTable.entryList[i];
                if (entry.strcolname.IndexOf("_base") == -1)
                {
                    sRet.columnNames.Add(entry.strcoldesc);
                }
            }
            KPIPopTable gatheredTable = new KPIPopTable();
            gatheredTable.entryList.AddRange(kpiPopTable.entryList);
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach (KPIResultInfo rinfo in kpiPopTable.dataResult)
            {
                string key = rinfo.dbid + ":" + rinfo.strname + ":" + getMonthStr(rinfo.stime);
                KPIResultInfo ginfo = null;
                if (!retDic.TryGetValue(key, out ginfo))
                {
                    retDic[key] = rinfo.copyInstance();
                    retDic[key].stime = getMonthBeginTime(rinfo.stime);
                }
                else
                {
                    ginfo.Gather(rinfo, kpiPopTable);
                }
            }
            int maxstime = 0;
            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                if (ginfo.stime > maxstime)
                {
                    maxstime = ginfo.stime;
                }
            }

            addSRetRowMonth(sRet, retDic, maxstime);
            return sRet;
        }

        private static void addSRetRowMonth(FinalShowResult sRet, Dictionary<string, KPIResultInfo> retDic, int maxstime)
        {
            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                if (ginfo.stime == maxstime)
                {
                    List<object> objList = new List<object>();
                    DateTime stimeDate = JavaDate.GetDateTimeFromMilliseconds(ginfo.stime * 1000L);
                    objList.Add(stimeDate.ToString("yyyy-MM"));
                    objList.Add(ginfo.strname);
                    for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                    {
                        object v = ginfo.valueList[i];
                        objList.Add(v);
                    }
                    sRet.dataRows.Add(objList);
                }
            }
        }

        private FinalShowResult prepareShowByMonth(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = new FinalShowResult();
            sRet.columnNames.Add("时间");
            sRet.columnNames.Add("名称");
            for (int i = 4; i < kpiPopTable.entryList.Count; i++)
            {
                PopKPIEntryItem entry = kpiPopTable.entryList[i];
                if (entry.strcolname.IndexOf("_base") == -1)
                {
                    sRet.columnNames.Add(entry.strcoldesc);
                }
            }
            KPIPopTable gatheredTable = new KPIPopTable();
            gatheredTable.entryList.AddRange(kpiPopTable.entryList);
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach(KPIResultInfo rinfo in kpiPopTable.dataResult)
            {
                string key = rinfo.dbid + ":" + rinfo.strname + ":" + getMonthStr(rinfo.stime);
                KPIResultInfo ginfo = null;
                if(!retDic.TryGetValue(key,out ginfo))
                {
                    retDic[key] = rinfo.copyInstance();
                }
                else
                {
                    ginfo.Gather(rinfo, kpiPopTable);
                }
            }
            
            foreach(KPIResultInfo ginfo in retDic.Values)
            {
                List<object> objList = new List<object>();
                DateTime stimeDate = JavaDate.GetDateTimeFromMilliseconds(ginfo.stime *1000L);
                objList.Add(stimeDate.ToString("yyyy-MM"));
                objList.Add(ginfo.strname);
                for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = ginfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            return sRet;
        }
        private FinalShowResult prepareShowByMonthToNow(KPIPopTable kpiPopTable)
        {
            FinalShowResult sRet = new FinalShowResult();
            sRet.columnNames.Add("时间");
            sRet.columnNames.Add("名称");
            for (int i = 4; i < kpiPopTable.entryList.Count; i++)
            {
                PopKPIEntryItem entry = kpiPopTable.entryList[i];
                if (entry.strcolname.IndexOf("_base") == -1)
                {
                    sRet.columnNames.Add(entry.strcoldesc);
                }
            }
            KPIPopTable gatheredTable = new KPIPopTable();
            gatheredTable.entryList.AddRange(kpiPopTable.entryList);
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach (KPIResultInfo rinfo in kpiPopTable.dataResult)
            {
                string key = rinfo.dbid + ":" + rinfo.strname + ":" + getMonthStr(rinfo.stime);
                KPIResultInfo ginfo = null;
                if (!retDic.TryGetValue(key, out ginfo))
                {
                    retDic[key] = rinfo.copyInstance();
                }
                else
                {
                    ginfo.Gather(rinfo, kpiPopTable);
                }
            }

            foreach (KPIResultInfo ginfo in retDic.Values)
            {
                List<object> objList = new List<object>();
                DateTime stimeDate = JavaDate.GetDateTimeFromMilliseconds(ginfo.stime * 1000L);
                objList.Add(stimeDate.ToString("yyyy-MM-dd")+"至今");
                objList.Add(ginfo.strname);
                for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = ginfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
            return sRet;
        }

        private int getWeekBeginTime(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DayOfWeek dweek = dt.DayOfWeek;
            int dayToMondy = 0;
            switch (dweek)
            {
                case DayOfWeek.Monday:
                    dayToMondy = 0;
                    break;
                case DayOfWeek.Tuesday:
                    dayToMondy = 1;
                    break;
                case DayOfWeek.Wednesday:
                    dayToMondy = 2;
                    break;
                case DayOfWeek.Thursday:
                    dayToMondy = 3;
                    break;
                case DayOfWeek.Friday:
                    dayToMondy = 4;
                    break;
                case DayOfWeek.Saturday:
                    dayToMondy = 5;
                    break;
                case DayOfWeek.Sunday:
                    dayToMondy = 6;
                    break;

            }
            DateTime dtbegin = dt.AddDays(-dayToMondy);
            dtbegin = (new DateTime(dtbegin.Year, dtbegin.Month, dtbegin.Day)).ToLocalTime();
            long seconds = JavaDate.GetMilliseconds(dtbegin)/1000;
            return (int)seconds;
        }

        private string getWeekStr(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DayOfWeek dweek = dt.DayOfWeek;
            int dayToMondy = 0;
            switch(dweek)
            {
                case DayOfWeek.Monday:
                    dayToMondy =0;
                    break;
                case DayOfWeek.Tuesday:
                    dayToMondy = 1;
                    break;
                case DayOfWeek.Wednesday:
                    dayToMondy = 2;
                    break;
                case DayOfWeek.Thursday:
                    dayToMondy = 3;
                    break;
                case DayOfWeek.Friday:
                    dayToMondy = 4;
                    break;
                case DayOfWeek.Saturday:
                    dayToMondy = 5;
                    break;
                case  DayOfWeek.Sunday:
                    dayToMondy = 6;
                    break;

            }
            return dt.AddDays(-dayToMondy).ToString("yyyy.MM.dd_") + dt.AddDays(6 - dayToMondy).ToString("MM.dd");
        }
        private int getMonthBeginTime(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DateTime dtbegin = new DateTime(dt.Year, dt.Month, 1).ToLocalTime();
            return (int)(JavaDate.GetMilliseconds(dtbegin)/1000);
        }

        private string getMonthStr(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            return dt.ToString("yyyy-MM");
        }

        private void showInGrid(FinalShowResult showRet)
        {
            dataGridView.Rows.Clear();
            dataGridView.Columns.Clear();
            for (int i = 0; i < showRet.columnNames.Count; i++)
            {
                string columnname = showRet.columnNames[i];
                dataGridView.Columns.Add("Column" + i, columnname);
            }
            dataGridView.Columns[0].SortMode = DataGridViewColumnSortMode.Programmatic;
            if(showRet.dataRows.Count>0)
            {
                int indexRowAt = 0;
                for (int r = 0; r < showRet.dataRows.Count; r++)
                {
                    indexRowAt = addDgvRow(showRet, indexRowAt, r);
                }
            }
            dataGridView.Sort(dataGridView.Columns[0], ListSortDirection.Ascending);
        }

        private int addDgvRow(FinalShowResult showRet, int indexRowAt, int r)
        {
            List<object> dataRow = showRet.dataRows[r];
            if ((cbxContentType.Text).Contains("(全部)") || (cbxContentType.Text).Contains((string)dataRow[1]))
            {
                dataGridView.Rows.Add(1);
                for (int c = 0; c < dataRow.Count; c++)
                {
                    object dv = dataRow[c];
                    if (dv is DateTime)
                    {
                        dataGridView.Rows[indexRowAt].Cells[c].Value = ((DateTime)dv).ToString("yyyy-MM-dd");
                    }
                    else
                    {
                        dataGridView.Rows[indexRowAt].Cells[c].Value = dataRow[c];
#if PopShow_KPI_Color
                                string selShowType = cbxShowType.SelectedItem as string;
                                if (selShowType.Contains("周"))
                                {
                                    KPIPopTable kpiPopTable = cbxReportSel.SelectedItem as KPIPopTable;
                                    string key = kpiPopTable.tablename;
                                    foreach (PopKPIEntryItem item in kpiPopTable.entryList)
                                    {
                                        if (item.strcoldesc == showRet.columnNames[c])
                                        {
                                            key += item.strcolname;
                                            break;
                                        }
                                    }
                                    if (colorRangeDic!=null && colorRangeDic.ContainsKey(key))
                                    {
                                        List<DTParameterRangeColor> colorRangeLst = colorRangeDic[key] as List<DTParameterRangeColor>;
                                        foreach (DTParameterRangeColor colorRange in colorRangeLst)
                                        {
                                            if (colorRange.Within((float)dataRow[c]))
                                            {
                                                dataGridView.Rows[indexRowAt].Cells[c].Style.BackColor = colorRange.Value;
                                                dataGridView.Rows[indexRowAt].Cells[c].ToolTipText = colorRange.DesInfo;

                                                break;
                                            }
                                        }
                                    }
                                }
#endif
                    }
                }
                indexRowAt++;
            }

            return indexRowAt;
        }

        private void cbxContentType_SelectedIndexChanged(object sender, EventArgs e)
        {
            changeType();
        }
        private void dataGridView_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex >= 2 && e.ColumnIndex<dataGridView.Columns.Count) 
            {
                freshShowChart(e.ColumnIndex);
            }
        }
        private void freshShowChart(int colIndex)
        {
            try
            {
	            string selShowType = cbxShowType.SelectedItem as string;
	            if (selShowType == "最近一周" || selShowType == "最近一月" || selShowType == "本月初至今")
                {
                    showLatestChart(colIndex, selShowType);
                }
                else if (selShowType == "按周" || selShowType == "按月")
	            {
	                string showCont = cbxContentType.Text;
	                if(showCont.Contains("(全部)"))//显示多个序列
                    {
                        showAllChart(colIndex, selShowType);
                    }
                    else
                    {
                        showOtherChart(colIndex, selShowType);
                    }
                }
	            
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
           // tChartKPI.Series.Add()
        }

        private void showLatestChart(int colIndex, string selShowType)
        {
            chartControl.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = selShowType + " " + dataGridView.Columns[colIndex].HeaderText;
            chartControl.Titles.Add(title);

            Series series1 = new Series(dataGridView.Columns[colIndex].HeaderText, ViewType.ManhattanBar);
            double[] doubles;
            string[] labels;
            extractLabValues(colIndex, 1, out doubles, out labels);
            for (int i = 0; i < labels.Length; i++)
            {
                series1.Points.Add(new SeriesPoint(labels[i], new double[] { doubles[i] }));
            }
            series1.PointOptions.ValueNumericOptions.Precision = 2;
            series1.PointOptions.ValueNumericOptions.Format = NumericFormat.Number;
            series1.Visible = true;
            chartControl.Series.Clear();
            chartControl.Series.Add(series1);
            ((DevExpress.XtraCharts.XYDiagram3D)(chartControl.Diagram)).RuntimeRotation = true;
            ((DevExpress.XtraCharts.XYDiagram3D)(chartControl.Diagram)).RuntimeZooming = true;
            ((DevExpress.XtraCharts.XYDiagram3D)(chartControl.Diagram)).ZoomingOptions.UseMouseWheel = false;
            ((DevExpress.XtraCharts.XYDiagram3D)(chartControl.Diagram)).ZoomingOptions.UseKeyboard = true;
            if (series1.Points.Count > 5)
            {
                ((DevExpress.XtraCharts.XYDiagram3D)(chartControl.Diagram)).AxisX.Label.Angle = 325;
            }
            else if (series1.Points.Count > 0)
            {
                ((DevExpress.XtraCharts.XYDiagram3D)(chartControl.Diagram)).AxisX.Label.Angle = 0;
            }
            Bar3DSeriesView seriesView = (Bar3DSeriesView)series1.View;
            series1.Label.Visible = true;
            seriesView.Model = Bar3DModel.Cylinder;
        }

        private void showAllChart(int colIndex, string selShowType)
        {
            chartControl.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = dataGridView.Columns[colIndex].HeaderText + selShowType + "情况";
            chartControl.Titles.Add(title);
            chartControl.Series.Clear();
            Dictionary<string, Series> areaSeriesDic = new Dictionary<string, Series>();
            for (int r = 0; r < dataGridView.Rows.Count; r++)
            {
                string namelabel = dataGridView.Rows[r].Cells[1].Value.ToString();//名称列
                Series nmBar;
                if (!areaSeriesDic.TryGetValue(namelabel, out nmBar))
                {
                    nmBar = new Series(namelabel, ViewType.ManhattanBar);
                    chartControl.Series.Add(nmBar);
                    areaSeriesDic[namelabel] = nmBar;
                    Bar3DSeriesView seriesView = (Bar3DSeriesView)nmBar.View;
                    seriesView.Model = Bar3DModel.Cylinder;
                }
                double vdouble = 0;
                string labelStr = "";
                extractLabValue(r, colIndex, 0, out vdouble, out labelStr);
                nmBar.Points.Add(new SeriesPoint(labelStr, new double[] { vdouble }));
            }
            if (chartControl.Series.Count > 0)
            {
                ((DevExpress.XtraCharts.XYDiagram3D)(chartControl.Diagram)).AxisX.Label.Angle = 325;
            }
            if (dataGridView.Rows.Count > 12)
            {
                ((DevExpress.XtraCharts.XYDiagram3D)(chartControl.Diagram)).AxisX.Label.Visible = false;
            }
            else
            {
                ((DevExpress.XtraCharts.XYDiagram3D)(chartControl.Diagram)).AxisX.Label.Visible = true;
            }
        }

        private void showOtherChart(int colIndex, string selShowType)
        {
            chartControl.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = dataGridView.Columns[colIndex].HeaderText + " " + selShowType;
            chartControl.Titles.Add(title);

            Series series1 = new Series(dataGridView.Columns[colIndex].HeaderText, ViewType.ManhattanBar);
            double[] doubles;
            string[] labels;
            extractLabValues(colIndex, 0, out doubles, out labels);
            for (int i = 0; i < labels.Length; i++)
            {
                series1.Points.Add(new SeriesPoint(labels[i], new double[] { doubles[i] }));
            }
            series1.Visible = true;
            chartControl.Series.Clear();
            chartControl.Series.Add(series1);
            if (series1.Points.Count > 5)
            {
                ((DevExpress.XtraCharts.XYDiagram3D)(chartControl.Diagram)).AxisX.Label.Angle = 325;
            }
            else if (series1.Points.Count > 0)
            {
                ((DevExpress.XtraCharts.XYDiagram3D)(chartControl.Diagram)).AxisX.Label.Angle = 0;
            }
            Bar3DSeriesView seriesView = (Bar3DSeriesView)series1.View;
            seriesView.Model = Bar3DModel.Cylinder;
            if (labels.Length > 12)
            {
                ((DevExpress.XtraCharts.XYDiagram3D)(chartControl.Diagram)).AxisX.Label.Visible = false;
            }
            else
            {
                ((DevExpress.XtraCharts.XYDiagram3D)(chartControl.Diagram)).AxisX.Label.Visible = true;
            }
        }

        private void extractLabValue(int rowAt,int vColumn, int labelColumn, out double doublev, out string labelv)
        {
            doublev = 0;
            object v = dataGridView.Rows[rowAt].Cells[vColumn].Value;
            if (v is int)
            {
                doublev = (int)v;
            }
            else if (v is double)
            {
                double db = (double)v;
                db = 0.01 * ((int)(db * 100));
                doublev = db;
            }
            else if (v is float)
            {
                float db = (float)v;
                db = 0.01f * ((int)(db * 100));
                doublev = db;
            }
            string vlabel = dataGridView.Rows[rowAt].Cells[labelColumn].Value.ToString();
            labelv = vlabel;
            
        }
        private void extractLabValues(int vColumn, int labelColumn,out double[] doubles, out string[] labels)
        {
            doubles = new double[dataGridView.Rows.Count];
            labels = new string[dataGridView.Rows.Count];
            for(int r = 0;r<dataGridView.Rows.Count;r++)
            {
                object v = dataGridView.Rows[r].Cells[vColumn].Value;
                if(v is int)
                {
                    doubles[r] = (int)v;
                }
                else if (v is double)
                {
                    double db = (double)v;
                    db = 0.01*((int)(db * 100));
                    doubles[r] = db;
                }
                else if (v is float)
                {
                    float db = (float)v;
                    db = 0.01f * ((int)(db * 100));
                    doubles[r] = db;
                }
                string vlabel = dataGridView.Rows[r].Cells[labelColumn].Value.ToString();
                labels[r] = vlabel;
            }
        }

        internal void ReturnTChartControl(Steema.TeeChart.TChart tchart)
        {
            tchart.Parent = null;
            splitMain.Panel2.Controls.Add(tchart);
            splitMain.Panel2Collapsed = false;
            tchart.Dock = DockStyle.Fill;
        }

        private void zoomTrackBarControl_EditValueChanged(object sender, EventArgs e)
        {
            ((DevExpress.XtraCharts.XYDiagram3D)(chartControl.Diagram)).ZoomPercent = zoomTrackBarControl.Value;
        }

        private void btnLevel0_Click(object sender, EventArgs e)
        {
            if(isProvUser)
            {
                curDrillLevel = 0;
                cityName = "";
                refreshShowReport(true);
            }
        }

        private void dataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.ColumnIndex == 1 && e.RowIndex < dataGridView.Rows.Count && curDrillLevel == 0)//选择了地市名称
                {
                    string cityname = dataGridView.Rows[e.RowIndex].Cells[e.ColumnIndex].Value as string;
                    if (cityname != null)
                    {
                        this.cityName = cityname;
                        this.curDrillLevel = 1;
                        refreshShowReport(true);
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
        }
        
        private void cbxContentType_EditValueChanged(object sender, EventArgs e)
        {
            changeType();
        }

        private void changeType()
        {
            refreshShowReport(false);
            freshShowChart(2);
        }

        private void miExp2Word_Click(object sender, EventArgs e)
        {
            if (dataGridView.RowCount <= 0)
            {
                MessageBox.Show("当前无数据可导出！");
                return;
            }
            SelectSavePath();
        }
        private void SelectSavePath()
        {
            SaveFileDialog saveFileDlg = new SaveFileDialog();
            saveFileDlg.Title = "选择要保存文档的路径";
            saveFileDlg.RestoreDirectory = true;
            saveFileDlg.Filter = FilterHelper.Excel;
            if (saveFileDlg.ShowDialog() == DialogResult.OK)
            {
                ExcelControl excel = new ExcelControl();
                try
                {
                    ExportInfo(excel);
                    excel.SaveFile(saveFileDlg.FileName);
                }
                catch
                {
                    MessageBox.Show("导出数据出错！");
                    return;
                }
                finally
                {
                    excel.CloseExcel();
                }
                if (DialogResult.Yes == MessageBox.Show(this, "Excel文件保存完毕，是否现在打开?", "打开文件", MessageBoxButtons.YesNo))
                {
                    try
                    {
                        System.Diagnostics.Process.Start(saveFileDlg.FileName);
                    }
                    catch
                    {
                        MessageBox.Show("打开失败!\r\n文件名:" + saveFileDlg.FileName);
                    }
                }
            }
        }

        public void ExportInfo(ExcelControl excel)
        {
            string dirPath = Path.Combine(Application.StartupPath, "KPIPictTemp");
            Directory.CreateDirectory(dirPath);

            MasterCom.RAMS.Func.PopShow.KPIInfoPanel_ng.KPIExport2XlsParam excelParam = new MasterCom.RAMS.Func.PopShow.KPIInfoPanel_ng.KPIExport2XlsParam();
            excelParam.excelApp = excel;
            excelParam.dgv = dataGridView;
            excelParam.pictPath = dirPath;
            excelParam.title = cbxReportSel.SelectedItem.ToString() + "_" + cbxShowType.SelectedItem.ToString();
            WaitBox.Show(this, printPict, excelParam);
        }

        private void printPict(object param)
        {
            WaitBox.Text = "正在生成图片...";
            MasterCom.RAMS.Func.PopShow.KPIInfoPanel_ng.KPIExport2XlsParam wordParam = param as MasterCom.RAMS.Func.PopShow.KPIInfoPanel_ng.KPIExport2XlsParam;
            for (int i = 2; i < dataGridView.ColumnCount; i++)
            {
                freshShowChart(i);
                string pictPath = wordParam.pictPath + Path.DirectorySeparatorChar + i.ToString() + ".jpg";
                chartControl.ExportToImage(pictPath, System.Drawing.Imaging.ImageFormat.Jpeg);
                WaitBox.ProgressPercent = (int)(i * 100.0 / dataGridView.ColumnCount);
            }
            fireExp2Xls(wordParam);
        }

        private void fireExp2Xls(object param)
        {
            MasterCom.RAMS.Func.PopShow.KPIInfoPanel_ng.KPIExport2XlsParam excelParam = param as MasterCom.RAMS.Func.PopShow.KPIInfoPanel_ng.KPIExport2XlsParam;
            excelParam.excelApp.Sheet.Name = "KPI报表_" + excelParam.title;
            excelParam.excelApp.ExportExcel(excelParam.excelApp.Sheet, excelParam.dgv);
            object cellHeightPt = ((Microsoft.Office.Interop.Excel.Range)(excelParam.excelApp.Sheet.Cells[1, 1])).Height;
            float fCellHeightPt = float.Parse(cellHeightPt.ToString());//Excel单元格的高度（磅）
            float picWidth = chartControl.Width / 96f * 72;//图片的宽度（磅）
            float picHeight = chartControl.Height / 96f * 72f;//图片的高度（磅）
            int h = (int)(picHeight / fCellHeightPt + 1);
            for (int index = 2; index < excelParam.dgv.ColumnCount; index++)//插入图片
            {
                int rowIndex = excelParam.dgv.RowCount + 2 + h * (index - 2);
                string pictPath = excelParam.pictPath + Path.DirectorySeparatorChar + index.ToString() + ".jpg";
                excelParam.excelApp.InsertPicture(rowIndex, pictPath, picWidth, picHeight);
            }
            WaitBox.Close();
        }
    }
}
