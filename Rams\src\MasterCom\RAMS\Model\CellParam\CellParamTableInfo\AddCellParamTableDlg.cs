﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Model
{
    public partial class AddCellParamTableDlg : BaseForm
    {
        private List<CellParamColumn> selCols = null;
        public AddCellParamTableDlg(List<CellParamColumn> selCols)
        {
            InitializeComponent();
            this.selCols = selCols;
            fillCbxDB();
        }

        private void fillCbxDB()
        {
            if (CellParamCfgManager.GetInstance().DataBaseInfoForAddParam == null)
            {
                QueryDataBaseInfo qryDB = new QueryDataBaseInfo(CellParamCfgManager.GetInstance().DBConnectionStr);
                List<string> dbNames = qryDB.Query();
                if (dbNames != null && dbNames.Count > 0)
                {
                    List<CellParamDataBase> dbSet = new List<CellParamDataBase>();
                    foreach (string dbName in dbNames)
                    {
                        CellParamDataBase db = new CellParamDataBase(dbName);
                        dbSet.Add(db);
                    }
                    CellParamCfgManager.GetInstance().DataBaseInfoForAddParam = dbSet;
                }
            }
            cbxDBSet.Items.Clear();
            foreach (CellParamDataBase db in CellParamCfgManager.GetInstance().DataBaseInfoForAddParam )
            {
                cbxDBSet.Items.Add(db);
            }
            cbxDBSet.SelectedIndexChanged -= cbxDBSet_SelectedIndexChanged;
            cbxDBSet.SelectedIndexChanged += cbxDBSet_SelectedIndexChanged;
        }

        void cbxDBSet_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cbxDBSet.SelectedItem is CellParamDataBase)
            {
                fillCbxTable(cbxDBSet.SelectedItem as CellParamDataBase);
            }
        }

        private void fillCbxTable(CellParamDataBase db)
        {
            if (db.Tables.Count == 0)
            {
                QueryDBTableInfo qryTB = new QueryDBTableInfo(CellParamCfgManager.GetInstance().DBConnectionStr, db.Name);
                List<string> tbNames= qryTB.Query();
                foreach (string tbName in tbNames)
                {
                    CellParamTable tb = new CellParamTable(tbName);
                    tb.DataBase = db;
                    db.Tables.Add(tb);
                }
            }
            cbxCols.Items.Clear();
            cbxTables.Items.Clear();
            foreach (CellParamTable tb in db.Tables)
            {
                cbxTables.Items.Add(tb);
            }
            cbxTables.SelectedIndexChanged -= cbxTables_SelectedIndexChanged;
            cbxTables.SelectedIndexChanged += cbxTables_SelectedIndexChanged;
        }

        void cbxTables_SelectedIndexChanged(object sender, EventArgs e)
        {
            cbxCols.Items.Clear();
            if (cbxTables.SelectedItem is CellParamTable)
            {
                fillCbxCol(cbxTables.SelectedItem as CellParamTable);
            }
        }

        private void fillCbxCol(CellParamTable table)
        {
            if (table.Columns.Count == 0)
            {
                QueryTableStructure queryTable = new QueryTableStructure(CellParamCfgManager.GetInstance().DBConnectionStr, (CellParamTable)cbxTables.SelectedItem);
                queryTable.Query();
            }
            cbxCols.Items.Clear();
            cbxDateField.Items.Clear();
            int idIdx = -1;
            int dateIdx = -1;
            for (int i = 0; i < table.Columns.Count; i++)
            {
                CellParamColumn col = table.Columns[i];
                cbxCols.Items.Add(col);
                cbxDateField.Items.Add(col);
                if (col.Name.Equals(table.CellIDForeignKey))
                {
                    idIdx = i;
                }
                else if(col.Name.Equals(table.CheckDateFieldName))
                {
                    dateIdx = i;
                }
            }
            cbxCols.SelectedIndex = idIdx;
            cbxDateField.SelectedIndex = dateIdx;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            bool isValid = judgeValid();
            if (!isValid)
            {
                return;
            }
            bool removeOld = false;
            if (CellParamCfgManager.GetInstance().ContainsTable(cbxTables.SelectedItem.ToString()))
            {
                DialogResult dlgResult = MessageBox.Show("参数配置中已存在相同表名！是否覆盖？", "警告", MessageBoxButtons.YesNo);
                if (DialogResult.Yes == dlgResult)
                {
                    removeOld = true;
                }
            }
            QueryTableStructure queryTable = new QueryTableStructure(CellParamCfgManager.GetInstance().DBConnectionStr, (CellParamTable)cbxTables.SelectedItem);
            CellParamTable tb = queryTable.Query();
            if (removeOld)
            {
                string tbName = cbxTables.SelectedItem.ToString();
                if (selCols != null)
                {
                    for (int i = 0; i < selCols.Count; i++)
                    {
                        CellParamColumn col = selCols[i];
                        if (col.Table.Name.Equals(tbName))
                        {
                            selCols.RemoveAt(i);
                            i--;
                        }
                    }
                }
                CellParamCfgManager.GetInstance().RemoveTable(tbName);
            }
            CellParamCfgManager.GetInstance().AddTable(tb);
            tb.CellIDForeignKey = cbxCols.SelectedItem.ToString();
            tb.CheckDateFieldName = cbxDateField.SelectedItem.ToString();
            DialogResult = DialogResult.OK;
        }

        private bool judgeValid()
        {
            if (cbxDBSet.SelectedItem == null)
            {
                MessageBox.Show("请选择数据库！");
                return false;
            }
            if (cbxTables.SelectedItem == null)
            {
                MessageBox.Show("请选择表！");
                return false;
            }
            if (cbxCols.SelectedItem == null)
            {
                MessageBox.Show("请选择关联字段（自维护ID）！");
                return false;
            }
            else
            {
                CellParamColumn col = cbxCols.SelectedItem as CellParamColumn;
                if (!col.IsNumericColumn)
                {
                    MessageBox.Show("所选关联字段（自维护ID）字段类型不是数字类型！");
                    return false;
                }
            }
            if (cbxDateField.SelectedItem == null)
            {
                MessageBox.Show("请选择记录时间字段（Checkdate）！");
                return false;
            }
            else
            {
                CellParamColumn col = cbxDateField.SelectedItem as CellParamColumn;
                if (!col.IsNumericColumn)
                {
                    MessageBox.Show("所选记录时间字段（自维护ID）字段类型不是日期类型！");
                    return false;
                }
            }
            return true;
        }
    }
}
