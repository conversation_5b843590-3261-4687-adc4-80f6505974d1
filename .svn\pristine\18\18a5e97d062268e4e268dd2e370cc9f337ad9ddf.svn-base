﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LtePlanningRedirectResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tabControl = new System.Windows.Forms.TabControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportCurXls = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportAllXls = new System.Windows.Forms.ToolStripMenuItem();
            this.tabSummary = new System.Windows.Forms.TabPage();
            this.gcSummary = new DevExpress.XtraGrid.GridControl();
            this.gvSummary = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tabRedirect = new System.Windows.Forms.TabPage();
            this.gcRedirect = new DevExpress.XtraGrid.GridControl();
            this.gvRedirect = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tabControl.SuspendLayout();
            this.contextMenuStrip1.SuspendLayout();
            this.tabSummary.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcSummary)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSummary)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            this.tabRedirect.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcRedirect)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvRedirect)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            this.SuspendLayout();
            // 
            // tabControl
            // 
            this.tabControl.ContextMenuStrip = this.contextMenuStrip1;
            this.tabControl.Controls.Add(this.tabSummary);
            this.tabControl.Controls.Add(this.tabRedirect);
            this.tabControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl.Location = new System.Drawing.Point(0, 0);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedIndex = 0;
            this.tabControl.Size = new System.Drawing.Size(1120, 580);
            this.tabControl.TabIndex = 1;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportCurXls,
            this.miExportAllXls});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(154, 70);
            // 
            // miExportCurXls
            // 
            this.miExportCurXls.Name = "miExportCurXls";
            this.miExportCurXls.Size = new System.Drawing.Size(153, 22);
            this.miExportCurXls.Text = "导出当前Excel";
            // 
            // miExportAllXls
            // 
            this.miExportAllXls.Name = "miExportAllXls";
            this.miExportAllXls.Size = new System.Drawing.Size(153, 22);
            this.miExportAllXls.Text = "导出全部Excel";
            // 
            // tabSummary
            // 
            this.tabSummary.Controls.Add(this.gcSummary);
            this.tabSummary.Location = new System.Drawing.Point(4, 23);
            this.tabSummary.Name = "tabSummary";
            this.tabSummary.Size = new System.Drawing.Size(1112, 553);
            this.tabSummary.TabIndex = 2;
            this.tabSummary.Text = "汇总统计";
            this.tabSummary.UseVisualStyleBackColor = true;
            // 
            // gcSummary
            // 
            this.gcSummary.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcSummary.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcSummary.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcSummary.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcSummary.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcSummary.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcSummary.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcSummary.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gcSummary.Location = new System.Drawing.Point(0, 0);
            this.gcSummary.MainView = this.gvSummary;
            this.gcSummary.Name = "gcSummary";
            this.gcSummary.ShowOnlyPredefinedDetails = true;
            this.gcSummary.Size = new System.Drawing.Size(1112, 553);
            this.gcSummary.TabIndex = 4;
            this.gcSummary.UseEmbeddedNavigator = true;
            this.gcSummary.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvSummary,
            this.gridView4});
            // 
            // gvSummary
            // 
            this.gvSummary.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn33,
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4});
            this.gvSummary.GridControl = this.gcSummary;
            this.gvSummary.Name = "gvSummary";
            this.gvSummary.OptionsBehavior.Editable = false;
            this.gvSummary.OptionsDetail.ShowDetailTabs = false;
            this.gvSummary.OptionsView.ShowGroupPanel = false;
            this.gvSummary.OptionsView.ShowIndicator = false;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "小区个数";
            this.gridColumn33.FieldName = "CellCount";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 0;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "重定向不含2G小区";
            this.gridColumn1.FieldName = "No2GCount";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 1;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "不含2G小区占比";
            this.gridColumn2.DisplayFormat.FormatString = "P2";
            this.gridColumn2.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn2.FieldName = "No2GRate";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 2;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "无重定向小区";
            this.gridColumn3.FieldName = "NoRedirectCount";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 3;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "无重定向小区占比";
            this.gridColumn4.DisplayFormat.FormatString = "P2";
            this.gridColumn4.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn4.FieldName = "NoRedirectRate";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 4;
            // 
            // gridView4
            // 
            this.gridView4.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn37,
            this.gridColumn38,
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn49,
            this.gridColumn50});
            this.gridView4.GridControl = this.gcSummary;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsBehavior.Editable = false;
            this.gridView4.OptionsDetail.ShowDetailTabs = false;
            this.gridView4.OptionsView.ShowGroupPanel = false;
            this.gridView4.OptionsView.ShowIndicator = false;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "小区名";
            this.gridColumn37.FieldName = "CellName";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 0;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "小区ID";
            this.gridColumn38.FieldName = "CellID";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 1;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "EARFCN";
            this.gridColumn39.FieldName = "Earfcn";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 2;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "PCI";
            this.gridColumn40.FieldName = "Pci";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 3;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "经度";
            this.gridColumn41.FieldName = "Longitude";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 4;
            // 
            // gridColumn49
            // 
            this.gridColumn49.Caption = "纬度";
            this.gridColumn49.FieldName = "Latitude";
            this.gridColumn49.Name = "gridColumn49";
            this.gridColumn49.Visible = true;
            this.gridColumn49.VisibleIndex = 5;
            // 
            // gridColumn50
            // 
            this.gridColumn50.Caption = "距离";
            this.gridColumn50.DisplayFormat.FormatString = "F2";
            this.gridColumn50.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn50.FieldName = "Distance";
            this.gridColumn50.Name = "gridColumn50";
            this.gridColumn50.Visible = true;
            this.gridColumn50.VisibleIndex = 6;
            // 
            // tabRedirect
            // 
            this.tabRedirect.Controls.Add(this.gcRedirect);
            this.tabRedirect.Location = new System.Drawing.Point(4, 23);
            this.tabRedirect.Name = "tabRedirect";
            this.tabRedirect.Size = new System.Drawing.Size(1112, 553);
            this.tabRedirect.TabIndex = 1;
            this.tabRedirect.Text = "重定向异常小区";
            this.tabRedirect.UseVisualStyleBackColor = true;
            // 
            // gcRedirect
            // 
            this.gcRedirect.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcRedirect.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcRedirect.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcRedirect.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcRedirect.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcRedirect.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcRedirect.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcRedirect.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gcRedirect.Location = new System.Drawing.Point(0, 0);
            this.gcRedirect.MainView = this.gvRedirect;
            this.gcRedirect.Name = "gcRedirect";
            this.gcRedirect.ShowOnlyPredefinedDetails = true;
            this.gcRedirect.Size = new System.Drawing.Size(1112, 553);
            this.gcRedirect.TabIndex = 3;
            this.gcRedirect.UseEmbeddedNavigator = true;
            this.gcRedirect.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvRedirect,
            this.gridView3});
            // 
            // gvRedirect
            // 
            this.gvRedirect.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn14,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn30});
            this.gvRedirect.GridControl = this.gcRedirect;
            this.gvRedirect.Name = "gvRedirect";
            this.gvRedirect.OptionsBehavior.Editable = false;
            this.gvRedirect.OptionsDetail.ShowDetailTabs = false;
            this.gvRedirect.OptionsView.ShowGroupPanel = false;
            this.gvRedirect.OptionsView.ShowIndicator = false;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "异常类型";
            this.gridColumn14.FieldName = "RedirectDesc";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 0;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "基站名";
            this.gridColumn26.FieldName = "BtsName";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 1;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "eNodeBID";
            this.gridColumn27.FieldName = "ENodeBID";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 2;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "小区名";
            this.gridColumn5.FieldName = "CellName";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 3;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "CellID";
            this.gridColumn6.FieldName = "CellID";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 4;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "TAC";
            this.gridColumn7.FieldName = "Tac";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 5;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "EARFCN";
            this.gridColumn9.FieldName = "Earfcn";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 6;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "PCI";
            this.gridColumn10.FieldName = "Pci";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 7;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "方位角";
            this.gridColumn11.FieldName = "Direction";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 8;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "下倾角";
            this.gridColumn12.FieldName = "Downward";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 9;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "挂高";
            this.gridColumn13.FieldName = "Altitude";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 10;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "经度";
            this.gridColumn28.FieldName = "Longitude";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 11;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "纬度";
            this.gridColumn29.FieldName = "Latitude";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 12;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "地址";
            this.gridColumn30.FieldName = "Address";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 13;
            // 
            // gridView3
            // 
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn44,
            this.gridColumn45,
            this.gridColumn46,
            this.gridColumn47,
            this.gridColumn48});
            this.gridView3.GridControl = this.gcRedirect;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsDetail.ShowDetailTabs = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            this.gridView3.OptionsView.ShowIndicator = false;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "小区名";
            this.gridColumn42.FieldName = "CellName";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 0;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "小区ID";
            this.gridColumn43.FieldName = "CellID";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 1;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "EARFCN";
            this.gridColumn44.FieldName = "EARFCN";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 2;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "PCI";
            this.gridColumn45.FieldName = "PCI";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 3;
            // 
            // gridColumn46
            // 
            this.gridColumn46.Caption = "经度";
            this.gridColumn46.FieldName = "Longitude";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 4;
            // 
            // gridColumn47
            // 
            this.gridColumn47.Caption = "纬度";
            this.gridColumn47.FieldName = "Latitude";
            this.gridColumn47.Name = "gridColumn47";
            this.gridColumn47.Visible = true;
            this.gridColumn47.VisibleIndex = 5;
            // 
            // gridColumn48
            // 
            this.gridColumn48.Caption = "距离";
            this.gridColumn48.DisplayFormat.FormatString = "F2";
            this.gridColumn48.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn48.FieldName = "Distance";
            this.gridColumn48.Name = "gridColumn48";
            this.gridColumn48.Visible = true;
            this.gridColumn48.VisibleIndex = 6;
            // 
            // LtePlanningRedirectResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1120, 580);
            this.Controls.Add(this.tabControl);
            this.Name = "LtePlanningRedirectResultForm";
            this.Text = "LTE规划重定向异常小区";
            this.tabControl.ResumeLayout(false);
            this.contextMenuStrip1.ResumeLayout(false);
            this.tabSummary.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcSummary)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSummary)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            this.tabRedirect.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcRedirect)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvRedirect)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabControl;
        private System.Windows.Forms.TabPage tabSummary;
        private DevExpress.XtraGrid.GridControl gcSummary;
        private DevExpress.XtraGrid.Views.Grid.GridView gvSummary;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private System.Windows.Forms.TabPage tabRedirect;
        private DevExpress.XtraGrid.GridControl gcRedirect;
        private DevExpress.XtraGrid.Views.Grid.GridView gvRedirect;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExportCurXls;
        private System.Windows.Forms.ToolStripMenuItem miExportAllXls;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
    }
}