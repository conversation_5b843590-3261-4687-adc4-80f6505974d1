﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using StationRoadDistanceUtil;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFuncZTAngleCalculate
{
    public partial class AngleCalculateResultForm : MinCloseForm
    {
        public AngleCalculateResultForm()
        {
            InitializeComponent();
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridControl1.ContextMenuStrip = this.contextMenuStrip1;
            ToolStripMenuItem toolStripMenuItemExcel = new ToolStripMenuItem("导出到Excel");
            this.contextMenuStrip1.Items.Add(toolStripMenuItemExcel);
            toolStripMenuItemExcel.Click += toolStripMenuItemExcel_Click;
        }

        void toolStripMenuItemExcel_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveFileDlg = new SaveFileDialog();
            saveFileDlg.Filter = "Excel|*.xls";
            if (saveFileDlg.ShowDialog() != System.Windows.Forms.DialogResult.OK) return;
            string outFileName = saveFileDlg.FileName;
            string exportSheetName = "夹角计算";

            DataTable table = new DataTable();
            table.Columns.Add("序号", typeof(double));
            table.Columns.Add("经度1(度)", typeof(double));
            table.Columns.Add("纬度1(度)", typeof(double));
            table.Columns.Add("经度2(度)", typeof(double));
            table.Columns.Add("纬度2(度)", typeof(double));
            table.Columns.Add("与正北方夹角(度)", typeof(double));
            List<AngleCalculateResultItem> listItem = this.gridControl1.DataSource as List<AngleCalculateResultItem>;
            foreach (AngleCalculateResultItem item in listItem)
            {
                DataRow row = table.NewRow();
                row["序号"] = item.SN;
                row["经度1(度)"] = item.Lon0;
                row["纬度1(度)"] = item.Lat0;
                row["经度2(度)"] = item.Lon1;
                row["纬度2(度)"] = item.Lat1;
                row["与正北方夹角(度)"] = item.Angle;
                table.Rows.Add(row);
            }
            ExcelNPOIReaderExt.DataTableToExcel(table, outFileName, exportSheetName,true);
            MessageBox.Show("导出到Excel表成功!");
        }

        public void fillData(List<AngleCalculateResultItem> listItem)
        {
            this.gridControl1.DataSource = listItem;
            this.gridControl1.RefreshDataSource();
        }

    }

    public class AngleCalculateResultItem
    {
        public string SN { set; get; }
        public string Lon0 { set; get; }
        public string Lat0 { set; get; }
        public string Lon1 { set; get; }
        public string Lat1 { set; get; }
        public string Angle { set; get; }
    }
}
