﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsCoverageRangeSetting : LteMgrsConditionControlBase
    {
        public LteMgrsCoverageRangeSetting()
        {
            InitializeComponent();
            InitCbxFreqType();
            chkOptionalRsrp.CheckedChanged += ChkOptionRsrp_CheckedChanged;

            checkEditTwoEarfcn.CheckedChanged += new EventHandler(checkEditTwoEarfcn_CheckedChanged);
            checkEditTwoEarfcn.Checked = false;
            checkEditTwoEarfcn_CheckedChanged(null, null);
            initValues();
        }

        public override string Title
        {
            get { return "重叠覆盖度"; }
        }

        public override object GetCondition(out string invalidReason)
        {
            invalidReason = null;
            LteMgrsCoverageCondition cond = new LteMgrsCoverageCondition();
            cond.MinRsrp = (double)numRsrpMin.Value;
            cond.DiffRsrp = (double)numRsrpDiff.Value;
            cond.EnableOptional = chkOptionalRsrp.Checked;
            cond.OptionalRsrp = (double)numOptionalRsrp.Value;
            cond.CheckTwoEarfcn = (bool)checkEditTwoEarfcn.EditValue;
            cond.EnableOutputSample = checkEditSampleData.Checked;
            cond.strCsvPath = txtCsvPath.Text;
            cond.EnableFBandType = chkFBandType.Checked;
            cond.FilterF2 = chkFilterF2.Checked;
            cond.FreqType = (LteMgrsCoverageBandType)EnumDescriptionAttribute.Parse(typeof(LteMgrsCoverageBandType), cbxFreqType.SelectedItem as string);
            return cond;
        }

        private void ChkOptionRsrp_CheckedChanged(object sender, EventArgs e)
        {
            numOptionalRsrp.Enabled = chkOptionalRsrp.Checked;
        }

        private void checkEditTwoEarfcn_CheckedChanged(object sender, EventArgs e)
        {
            if (checkEditTwoEarfcn.Checked)
            {
                cbxFreqType.SelectedItem = EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.All);
            }

            cbxFreqType.Enabled = !checkEditTwoEarfcn.Checked;
            chkFilterF2.Enabled = chkFBandType.Enabled = checkEditTwoEarfcn.Checked;
        }

        private void InitCbxFreqType()
        {
            cbxFreqType.Items.Clear();
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.All));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.Top));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD1));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD2));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD_38098));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleF));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleF1));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleF2));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.TopEarfcn));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD_40936));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD_40940));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleE_38950));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleE_39148));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD1_3683));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD1_3692));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD2_1259));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD2_1300));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD2_1309));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD2_1359));
            cbxFreqType.SelectedIndex = 0;
        }

        private void initValues()
        {
            XmlConfigFile configFile = new MyXmlConfigFile(LteMgrsBaseSettingManager.Instance.ConfigPath);
            if (configFile.Load())
            {
                setControlData(configFile);
            }
        }

        private void setControlData(XmlConfigFile configFile)
        {
            XmlElement configCoverRange = configFile.GetConfig("CoverageRange");
            object obj = configFile.GetItemValue(configCoverRange, "RSRPMin");
            if (obj != null)
            {
                numRsrpMin.Value = (decimal)(double)obj;
            }

            obj = configFile.GetItemValue(configCoverRange, "RSRPDiff");
            if (obj != null)
            {
                numRsrpDiff.Value = (decimal)(double)obj;
            }

            obj = configFile.GetItemValue(configCoverRange, "EnableOptional");
            if (obj != null)
            {
                chkOptionalRsrp.Checked = (bool)obj;
            }

            obj = configFile.GetItemValue(configCoverRange, "OptionalRsrp");
            if (obj != null)
            {
                numOptionalRsrp.Value = (decimal)(double)obj;
            }

            obj = configFile.GetItemValue(configCoverRange, "CheckTwoEarfcn");
            if (obj != null)
            {
                checkEditTwoEarfcn.EditValue = (bool)obj;
            }

            obj = configFile.GetItemValue(configCoverRange, "FBandType");
            if (obj != null)
            {
                chkFBandType.EditValue = (bool)obj;
            }

            obj = configFile.GetItemValue(configCoverRange, "FilterF2");
            if (obj != null)
            {
                chkFilterF2.EditValue = (bool)obj;
            }

            obj = configFile.GetItemValue(configCoverRange, "EditSampleData");
            if (obj != null)
            {
                checkEditSampleData.Checked = (bool)obj;
            }

            obj = configFile.GetItemValue(configCoverRange, "CsvPath");
            if (obj != null)
            {
                txtCsvPath.Text = obj.ToString();
            }

            obj = configFile.GetItemValue(configCoverRange, "FreqType");
            if (obj != null)
            {
                int index = cbxFreqType.Items.IndexOf(obj.ToString());
                if (index >= 0)
                {
                    cbxFreqType.SelectedIndex = index;
                }
            }
        }

        private void btnFile_Click(object sender, EventArgs e)
        {
            string filePath = Application.StartupPath + "\\userData\\";
            txtCsvPath.Text = filePath;
            FolderBrowserDialog dialog = new FolderBrowserDialog();
            dialog.Description = "请选择文件夹";
            dialog.ShowNewFolderButton = true;
            dialog.SelectedPath = filePath;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                txtCsvPath.Text = dialog.SelectedPath;
            }
        }

        private void checkEditSampleData_CheckedChanged(object sender, EventArgs e)
        {
            txtCsvPath.Enabled = checkEditSampleData.Checked;
            btnFile.Enabled = checkEditSampleData.Checked;
            string filePath = Application.StartupPath + "\\userData";
            txtCsvPath.Text = filePath;
        }

        public override void SaveCondititon(XmlConfigFile xcfg)
        {
            XmlElement configCoverageRange = xcfg.AddConfig("CoverageRange");
            xcfg.AddItem(configCoverageRange, "RSRPMin", (double)numRsrpMin.Value);
            xcfg.AddItem(configCoverageRange, "RSRPDiff", (double)numRsrpDiff.Value);
            xcfg.AddItem(configCoverageRange, "EnableOptional", chkOptionalRsrp.Enabled);
            xcfg.AddItem(configCoverageRange, "OptionalRsrp", (double)numOptionalRsrp.Value);
            xcfg.AddItem(configCoverageRange, "CheckTwoEarfcn", (bool)checkEditTwoEarfcn.EditValue);
            xcfg.AddItem(configCoverageRange, "FBandType", (bool)chkFBandType.EditValue);
            xcfg.AddItem(configCoverageRange, "FilterF2", (bool)chkFilterF2.EditValue);
            xcfg.AddItem(configCoverageRange, "EditSampleData", (bool)checkEditSampleData.EditValue);
            xcfg.AddItem(configCoverageRange, "CsvPath", txtCsvPath.Text);
            xcfg.AddItem(configCoverageRange, "FreqType", cbxFreqType.Text);
        }
    }
}
