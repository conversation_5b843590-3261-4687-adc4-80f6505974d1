﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.Collections;
using DevExpress.XtraCharts;
using DevExpress.XtraGrid.Views.Base;
using System.Reflection;
using MasterCom.RAMS.Util;

namespace MasterCom.RAMS.Func.PopShow
{
    public partial class CQTInfoPanel : UserControl, PopShowPanelInterface
    {
        private Dictionary<string, List<CQTCheckInfo>> cqtCheckInfoDict = new Dictionary<string, List<CQTCheckInfo>>();

        private MainModel MainModel;
        public CQTInfoPanel()
        {
            InitializeComponent();
        }
        
        #region PopShowPanelInterface 成员

        public void RunQuery(BackgroundWorker worker, TaskInfo task)
        {
            DIYQueryCQTInfo query = new DIYQueryCQTInfo(MainModel);
            query.Query();
            task.retResultInfo = query.CQTCheckInfoList;
            //foreach (CQTCheckInfo info in query.CQTCheckInfoList)
            //{
            //    if (cqtCheckInfoDict.ContainsKey(info.cqtType))
            //    {
            //        cqtCheckInfoDict[info.cqtType].Add(info);
            //    }
            //    else
            //    {
            //        List<CQTCheckInfo> list = new List<CQTCheckInfo>();
            //        list.Add(info);
            //        cqtCheckInfoDict.Add(info.cqtType, list);
            //    }
            //}
        }
       

        #endregion

        #region PopShowPanelInterface 成员


        public void FireFreshShowData(TaskInfo task)
        {
            if (task.retResultInfo is List<CQTCheckInfo>)
            {
                ShowGrid(task);
                ShowChart();
            }
        }

        private void ShowGrid(TaskInfo task)
        {
            List<CQTCheckInfo> cqtCheckInfoList = task.retResultInfo as List<CQTCheckInfo>;

            BindingSource bindingSource = new BindingSource();
            bindingSource.DataSource = (IList)cqtCheckInfoList;
            gridControl.DataSource = bindingSource;
            //gridControl.RefreshDataSource();

        }

        private void ShowChart()
        {
            chartControl.Legend.AlignmentHorizontal = LegendAlignmentHorizontal.Right;
            chartControl.Legend.AlignmentVertical = LegendAlignmentVertical.TopOutside;
            chartControl.Legend.Direction = LegendDirection.LeftToRight;
            chartControl.Legend.Visible = true;

            chartControl.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = "CQT信息";
            chartControl.Titles.Add(title);

            chartControl.Series.Clear();            
        }

        #endregion

        #region PopShowPanelInterface 成员


        public void SetMainModal(MasterCom.RAMS.Model.MainModel mm, WelcomForm welcomform)
        {
            this.MainModel = mm;
        }

        #endregion

        private void gridView_RowCellClick(object sender, DevExpress.XtraGrid.Views.Grid.RowCellClickEventArgs e)
        {
            cqtCheckInfoDict.Clear();
            int rowCount = gridView.RowCount;
            for (int i = 0; i < rowCount; i++)
            {
                CQTCheckInfo info = gridView.GetRow(i) as CQTCheckInfo;
                if (info != null)
                {
                    if (cqtCheckInfoDict.ContainsKey(info.CQTType))
                    {
                        cqtCheckInfoDict[info.CQTType].Add(info);
                    }
                    else
                    {
                        List<CQTCheckInfo> list = new List<CQTCheckInfo>();
                        list.Add(info);
                        cqtCheckInfoDict.Add(info.CQTType, list);
                    }
                }
            }

            chartControl.Legend.AlignmentHorizontal = LegendAlignmentHorizontal.Right;
            chartControl.Legend.AlignmentVertical = LegendAlignmentVertical.TopOutside;
            chartControl.Legend.Direction = LegendDirection.LeftToRight;
            chartControl.Legend.Visible = true;

            chartControl.Titles.Clear();
            ChartTitle title = new ChartTitle();
            title.Text = e.Column.Caption;
            chartControl.Titles.Add(title);

            chartControl.Series.Clear();
            foreach (string cqtType in cqtCheckInfoDict.Keys)
            {
                Series series = new Series(cqtType, ViewType.Bar);
                //object[] values = new object[cqtCheckInfoDict[cqtType].Count];
                //string[] labels = new string[cqtCheckInfoDict[cqtType].Count];
                foreach (CQTCheckInfo info in cqtCheckInfoDict[cqtType])
                {
                    Type type = info.GetType();
                    PropertyInfo propertyInfo = type.GetProperty(e.Column.FieldName);
                    double drawValue;
                    if (double.TryParse(propertyInfo.GetValue(info, null).ToString(), out drawValue))
                    {
                        series.Points.Add(new SeriesPoint(info.Period + " " + info.ProjectType+" "+info.Area, new double[] { drawValue }));
                    }
                    else
                    {
                        break;
                    }
                }
                chartControl.Series.Add(series);
            }
            ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Angle = 270;
            ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Visible = true;

            //double[] doubles;
            //string[] labels;
            //for (int i = 0; i < labels.Length; i++)
            //{
            //    series1.Points.Add(new SeriesPoint(labels[i], new double[] { doubles[i] }));
            //}
            //series1.PointOptions.ValueNumericOptions.Precision = isAllInt ? 0 : 2;
            //series1.PointOptions.ValueNumericOptions.Format = NumericFormat.Number;
            //series1.Label.ResolveOverlappingMode = ResolveOverlappingMode.HideOverlapped;
            //series1.Visible = true;
            //chartControl.Series.Clear();
            //chartControl.Series.Add(series1);
            //if (labels.Length > 5)
            //{
            //    ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Angle = 270;
            //    //((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Visible = false;
            //}
            //else if (labels.Length > 0)
            //{
            //    ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Angle = 0;
            //    // ((DevExpress.XtraCharts.XYDiagram)(chartControl.Diagram)).AxisX.Label.Visible = true;
            //}
        }

        private void miExp2Word_Click(object sender, EventArgs e)
        {
            try
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(gridView);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

    }

    public class CQTCheckInfo //CQT核查信息
    {
        public string CQTType { get; set; }
        public string ProjectType { get; set; }
        public string Period { get; set; }
        public string Area { get; set; }
        public int TestCount { get; set; }
        public int ProblemCount { get; set; }
        public float PassRate { get; set; }
        public int TestDuration { get; set; }
        public int TotalCount { get; set; }
        public float TestRate { get; set; }

        public CQTCheckInfo()
        { }

        public static CQTCheckInfo FillFrom(MasterCom.RAMS.Net.Package package)
        {
            CQTCheckInfo info = new CQTCheckInfo();
            info.CQTType = package.Content.GetParamString();
            info.ProjectType = package.Content.GetParamString();
            info.Period = package.Content.GetParamString();
            info.Area = package.Content.GetParamString();
            info.TestCount = package.Content.GetParamInt();
            info.ProblemCount = package.Content.GetParamInt();
            info.PassRate = package.Content.GetParamFloat();
            info.TestDuration = package.Content.GetParamInt();
            info.TotalCount = package.Content.GetParamInt();
            info.TestRate = package.Content.GetParamFloat();
            return info;
        }
    }

    public class DIYQueryCQTInfo : DIYSQLBase
    {
        public List<CQTCheckInfo> CQTCheckInfoList { get; set; }

        public DIYQueryCQTInfo(MainModel mainModel)
            : base(mainModel)
        {
            CQTCheckInfoList = new List<CQTCheckInfo>();
        }

        protected override string getSqlTextString()
        {
            return "select cqttype,projecttype,period,area,test_count,problem_count,pass_rate,test_duration,total_count,test_rate from tb_popkpi_gsmv_cqt_stat";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] types = new E_VType[10];
            types[0] = E_VType.E_String;
            types[1] = E_VType.E_String;
            types[2] = E_VType.E_String;
            types[3] = E_VType.E_String;
            types[4] = E_VType.E_Int;
            types[5] = E_VType.E_Int;
            types[6] = E_VType.E_Float;
            types[7] = E_VType.E_Int;
            types[8] = E_VType.E_Int;
            types[9] = E_VType.E_Float;
            return types;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            CQTCheckInfoList.Clear();
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CQTCheckInfo info = CQTCheckInfo.FillFrom(package);
                    CQTCheckInfoList.Add(info);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        public override string Name
        {
            get { return "查询CQT信息"; }
        }
    }
}
