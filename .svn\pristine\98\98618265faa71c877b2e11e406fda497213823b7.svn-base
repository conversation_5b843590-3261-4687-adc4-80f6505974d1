﻿namespace MasterCom.RAMS.ZTFunc.ZTCsfbCallStat
{
    partial class epsfbCallStatListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.bandedGridView = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn52 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn50 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn12 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn14 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn15 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn16 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn17 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn60 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn61 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn62 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn21 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn54 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn22 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn23 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn24 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn25 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn55 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn56 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn57 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn58 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn26 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn27 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn28 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn29 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn30 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn31 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn32 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn53 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn51 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn33 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn34 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn35 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn36 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn37 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn38 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn39 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn59 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn40 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn41 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn42 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn43 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn44 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn45 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn63 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn64 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn65 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn66 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn67 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn68 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn46 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn47 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn48 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn49 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn69 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.ctxMenu;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.bandedGridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(1147, 453);
            this.gridControl.TabIndex = 0;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView});
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay,
            this.toolStripSeparator1,
            this.miExport2Xls});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(139, 54);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(138, 22);
            this.miReplay.Text = "回放文件...";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(135, 6);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(138, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // bandedGridView
            // 
            this.bandedGridView.BandPanelRowHeight = 4;
            this.bandedGridView.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand1,
            this.gridBand2,
            this.gridBand3});
            this.bandedGridView.ColumnPanelRowHeight = 4;
            this.bandedGridView.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.bandedGridColumn3,
            this.bandedGridColumn4,
            this.bandedGridColumn5,
            this.bandedGridColumn6,
            this.bandedGridColumn7,
            this.bandedGridColumn8,
            this.bandedGridColumn52,
            this.bandedGridColumn50,
            this.bandedGridColumn9,
            this.bandedGridColumn10,
            this.bandedGridColumn11,
            this.bandedGridColumn12,
            this.bandedGridColumn13,
            this.bandedGridColumn14,
            this.bandedGridColumn15,
            this.bandedGridColumn16,
            this.bandedGridColumn17,
            this.bandedGridColumn18,
            this.bandedGridColumn19,
            this.bandedGridColumn20,
            this.bandedGridColumn60,
            this.bandedGridColumn61,
            this.bandedGridColumn62,
            this.bandedGridColumn21,
            this.bandedGridColumn54,
            this.bandedGridColumn22,
            this.bandedGridColumn23,
            this.bandedGridColumn24,
            this.bandedGridColumn25,
            this.bandedGridColumn55,
            this.bandedGridColumn56,
            this.bandedGridColumn57,
            this.bandedGridColumn58,
            this.bandedGridColumn26,
            this.bandedGridColumn27,
            this.bandedGridColumn28,
            this.bandedGridColumn29,
            this.bandedGridColumn30,
            this.bandedGridColumn31,
            this.bandedGridColumn32,
            this.bandedGridColumn53,
            this.bandedGridColumn51,
            this.bandedGridColumn33,
            this.bandedGridColumn34,
            this.bandedGridColumn35,
            this.bandedGridColumn36,
            this.bandedGridColumn37,
            this.bandedGridColumn38,
            this.bandedGridColumn39,
            this.bandedGridColumn59,
            this.bandedGridColumn69,
            this.bandedGridColumn40,
            this.bandedGridColumn41,
            this.bandedGridColumn42,
            this.bandedGridColumn43,
            this.bandedGridColumn44,
            this.bandedGridColumn45,
            this.bandedGridColumn63,
            this.bandedGridColumn64,
            this.bandedGridColumn65,
            this.bandedGridColumn66,
            this.bandedGridColumn67,
            this.bandedGridColumn68,
            this.bandedGridColumn46,
            this.bandedGridColumn47,
            this.bandedGridColumn48,
            this.bandedGridColumn49});
            this.bandedGridView.GridControl = this.gridControl;
            this.bandedGridView.Name = "bandedGridView";
            this.bandedGridView.OptionsBehavior.Editable = false;
            this.bandedGridView.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView.OptionsView.ShowGroupPanel = false;
            // 
            // bandedGridColumn1
            // 
            this.bandedGridColumn1.Caption = "序号";
            this.bandedGridColumn1.FieldName = "Sn";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.RowCount = 4;
            this.bandedGridColumn1.Visible = true;
            // 
            // bandedGridColumn2
            // 
            this.bandedGridColumn2.Caption = "log名称";
            this.bandedGridColumn2.FieldName = "MoFileName";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.RowCount = 4;
            this.bandedGridColumn2.Visible = true;
            // 
            // bandedGridColumn3
            // 
            this.bandedGridColumn3.Caption = "主叫起呼时间";
            this.bandedGridColumn3.DisplayFormat.FormatString = "HH:mm:ss.fff";
            this.bandedGridColumn3.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn3.FieldName = "MoCallAttemptTime";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.RowCount = 4;
            this.bandedGridColumn3.Visible = true;
            // 
            // bandedGridColumn4
            // 
            this.bandedGridColumn4.Caption = "经度";
            this.bandedGridColumn4.FieldName = "MoLng";
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            this.bandedGridColumn4.RowCount = 4;
            this.bandedGridColumn4.Visible = true;
            // 
            // bandedGridColumn5
            // 
            this.bandedGridColumn5.Caption = "纬度";
            this.bandedGridColumn5.FieldName = "MoLat";
            this.bandedGridColumn5.Name = "bandedGridColumn5";
            this.bandedGridColumn5.RowCount = 4;
            this.bandedGridColumn5.Visible = true;
            // 
            // bandedGridColumn6
            // 
            this.bandedGridColumn6.Caption = "主叫呼叫类型";
            this.bandedGridColumn6.FieldName = "MoPreCallNetType";
            this.bandedGridColumn6.Name = "bandedGridColumn6";
            this.bandedGridColumn6.RowCount = 4;
            this.bandedGridColumn6.Visible = true;
            // 
            // bandedGridColumn7
            // 
            this.bandedGridColumn7.Caption = "TAC";
            this.bandedGridColumn7.FieldName = "MoTac";
            this.bandedGridColumn7.Name = "bandedGridColumn7";
            this.bandedGridColumn7.RowCount = 4;
            this.bandedGridColumn7.Visible = true;
            // 
            // bandedGridColumn8
            // 
            this.bandedGridColumn8.Caption = "ECI";
            this.bandedGridColumn8.FieldName = "MoEci";
            this.bandedGridColumn8.Name = "bandedGridColumn8";
            this.bandedGridColumn8.RowCount = 4;
            this.bandedGridColumn8.Visible = true;
            // 
            // bandedGridColumn52
            // 
            this.bandedGridColumn52.Caption = "ENodeBID";
            this.bandedGridColumn52.FieldName = "MoEnodebid";
            this.bandedGridColumn52.Name = "bandedGridColumn52";
            this.bandedGridColumn52.Visible = true;
            // 
            // bandedGridColumn50
            // 
            this.bandedGridColumn50.Caption = "CellID";
            this.bandedGridColumn50.FieldName = "MoCellid";
            this.bandedGridColumn50.Name = "bandedGridColumn50";
            this.bandedGridColumn50.Visible = true;
            // 
            // bandedGridColumn9
            // 
            this.bandedGridColumn9.Caption = "RSRP(dBm)";
            this.bandedGridColumn9.FieldName = "MoRsrp";
            this.bandedGridColumn9.Name = "bandedGridColumn9";
            this.bandedGridColumn9.RowCount = 4;
            this.bandedGridColumn9.Visible = true;
            // 
            // bandedGridColumn10
            // 
            this.bandedGridColumn10.Caption = "回落小区LAC";
            this.bandedGridColumn10.FieldName = "MoLac";
            this.bandedGridColumn10.Name = "bandedGridColumn10";
            this.bandedGridColumn10.RowCount = 4;
            this.bandedGridColumn10.Visible = true;
            // 
            // bandedGridColumn11
            // 
            this.bandedGridColumn11.Caption = "回落小区CI";
            this.bandedGridColumn11.FieldName = "MoCi";
            this.bandedGridColumn11.Name = "bandedGridColumn11";
            this.bandedGridColumn11.RowCount = 4;
            this.bandedGridColumn11.Visible = true;
            // 
            // bandedGridColumn12
            // 
            this.bandedGridColumn12.Caption = "RxLevSub或PCCPCH RSCP(dBm)";
            this.bandedGridColumn12.FieldName = "MoRxLev_Rscp";
            this.bandedGridColumn12.Name = "bandedGridColumn12";
            this.bandedGridColumn12.RowCount = 4;
            this.bandedGridColumn12.Visible = true;
            // 
            // bandedGridColumn13
            // 
            this.bandedGridColumn13.Caption = "回落制式";
            this.bandedGridColumn13.FieldName = "MoCallNetType";
            this.bandedGridColumn13.Name = "bandedGridColumn13";
            this.bandedGridColumn13.RowCount = 4;
            this.bandedGridColumn13.Visible = true;
            // 
            // bandedGridColumn14
            // 
            this.bandedGridColumn14.Caption = "回落情况";
            this.bandedGridColumn14.FieldName = "MoBackResult";
            this.bandedGridColumn14.Name = "bandedGridColumn14";
            this.bandedGridColumn14.RowCount = 4;
            this.bandedGridColumn14.Visible = true;
            // 
            // bandedGridColumn15
            // 
            this.bandedGridColumn15.Caption = "呼叫结果";
            this.bandedGridColumn15.FieldName = "MoCallResultDesc";
            this.bandedGridColumn15.Name = "bandedGridColumn15";
            this.bandedGridColumn15.RowCount = 4;
            this.bandedGridColumn15.Visible = true;
            // 
            // bandedGridColumn16
            // 
            this.bandedGridColumn16.Caption = "Extended Service request-->Alerting";
            this.bandedGridColumn16.FieldName = "MoExRequest2Alerting";
            this.bandedGridColumn16.Name = "bandedGridColumn16";
            this.bandedGridColumn16.RowCount = 4;
            this.bandedGridColumn16.Visible = true;
            // 
            // bandedGridColumn17
            // 
            this.bandedGridColumn17.Caption = "Extended Service request-->RRC Connection Release ";
            this.bandedGridColumn17.FieldName = "MoExRequest2Release";
            this.bandedGridColumn17.Name = "bandedGridColumn17";
            this.bandedGridColumn17.RowCount = 4;
            this.bandedGridColumn17.Visible = true;
            // 
            // bandedGridColumn18
            // 
            this.bandedGridColumn18.Caption = "RRC Connection Release-->RR System Information Type(FRIST GERAN SI) ";
            this.bandedGridColumn18.FieldName = "MoRelease2SysInfo";
            this.bandedGridColumn18.Name = "bandedGridColumn18";
            this.bandedGridColumn18.RowCount = 4;
            this.bandedGridColumn18.Visible = true;
            // 
            // bandedGridColumn19
            // 
            this.bandedGridColumn19.Caption = "RR System Information Type-->CM Service Request";
            this.bandedGridColumn19.FieldName = "MoSysInfo2CmRequest";
            this.bandedGridColumn19.Name = "bandedGridColumn19";
            this.bandedGridColumn19.RowCount = 4;
            this.bandedGridColumn19.Visible = true;
            // 
            // bandedGridColumn20
            // 
            this.bandedGridColumn20.Caption = "CM Service Request-->CM Service Accept";
            this.bandedGridColumn20.FieldName = "MoCmRequest2CmAccept";
            this.bandedGridColumn20.Name = "bandedGridColumn20";
            this.bandedGridColumn20.RowCount = 4;
            this.bandedGridColumn20.Visible = true;
            // 
            // bandedGridColumn60
            // 
            this.bandedGridColumn60.Caption = "CM Service Request-->Authentication Request";
            this.bandedGridColumn60.FieldName = "MoCmRequest2AuRequest";
            this.bandedGridColumn60.Name = "bandedGridColumn60";
            this.bandedGridColumn60.RowCount = 4;
            this.bandedGridColumn60.Visible = true;
            // 
            // bandedGridColumn61
            // 
            this.bandedGridColumn61.Caption = "Authentication Request-->Authentication Response";
            this.bandedGridColumn61.FieldName = "MoAuRequest2AuResponse";
            this.bandedGridColumn61.Name = "bandedGridColumn61";
            this.bandedGridColumn61.RowCount = 4;
            this.bandedGridColumn61.Visible = true;
            // 
            // bandedGridColumn62
            // 
            this.bandedGridColumn62.Caption = "Authentication Response-->CM Service Accept";
            this.bandedGridColumn62.FieldName = "MoAuResponse2CmAccept";
            this.bandedGridColumn62.Name = "bandedGridColumn62";
            this.bandedGridColumn62.RowCount = 4;
            this.bandedGridColumn62.Visible = true;
            // 
            // bandedGridColumn21
            // 
            this.bandedGridColumn21.Caption = "CM Service Accept-->Setup";
            this.bandedGridColumn21.FieldName = "MoCmAccept2Setup";
            this.bandedGridColumn21.Name = "bandedGridColumn21";
            this.bandedGridColumn21.RowCount = 4;
            this.bandedGridColumn21.Visible = true;
            // 
            // bandedGridColumn54
            // 
            this.bandedGridColumn54.Caption = "CM Service Request-->Setup";
            this.bandedGridColumn54.FieldName = "MoCmRequest2Setup";
            this.bandedGridColumn54.Name = "bandedGridColumn54";
            this.bandedGridColumn54.Visible = true;
            // 
            // bandedGridColumn22
            // 
            this.bandedGridColumn22.Caption = "Setup-->Call Proceeding";
            this.bandedGridColumn22.FieldName = "MoSetup2Proc";
            this.bandedGridColumn22.Name = "bandedGridColumn22";
            this.bandedGridColumn22.RowCount = 4;
            this.bandedGridColumn22.Visible = true;
            // 
            // bandedGridColumn23
            // 
            this.bandedGridColumn23.Caption = "Call Proceeding-->RR Assignment Command";
            this.bandedGridColumn23.FieldName = "MoProc2AssignmentCmd";
            this.bandedGridColumn23.Name = "bandedGridColumn23";
            this.bandedGridColumn23.RowCount = 4;
            this.bandedGridColumn23.Visible = true;
            // 
            // bandedGridColumn24
            // 
            this.bandedGridColumn24.Caption = "RR Assignment Command-->RR Assignment Complete";
            this.bandedGridColumn24.FieldName = "MoAssigmentCmd2Complete";
            this.bandedGridColumn24.Name = "bandedGridColumn24";
            this.bandedGridColumn24.RowCount = 4;
            this.bandedGridColumn24.Visible = true;
            // 
            // bandedGridColumn25
            // 
            this.bandedGridColumn25.Caption = "RR Assignment Complete-->Alerting";
            this.bandedGridColumn25.FieldName = "MoComplete2Alerting";
            this.bandedGridColumn25.Name = "bandedGridColumn25";
            this.bandedGridColumn25.RowCount = 4;
            this.bandedGridColumn25.Visible = true;
            // 
            // bandedGridColumn55
            // 
            this.bandedGridColumn55.Caption = "Call Proceeding-->Channel Mode Modify";
            this.bandedGridColumn55.FieldName = "MoCallProceed2ChannelModify";
            this.bandedGridColumn55.Name = "bandedGridColumn55";
            this.bandedGridColumn55.RowCount = 4;
            this.bandedGridColumn55.Visible = true;
            // 
            // bandedGridColumn56
            // 
            this.bandedGridColumn56.Caption = "Channel Mode Modify-->Channel Mode Modify Acknowledge";
            this.bandedGridColumn56.FieldName = "MoChannelModify2ChannelAcknowledge";
            this.bandedGridColumn56.Name = "bandedGridColumn56";
            this.bandedGridColumn56.Visible = true;
            // 
            // bandedGridColumn57
            // 
            this.bandedGridColumn57.Caption = "Channel Mode Modify Acknowledge-->Alerting";
            this.bandedGridColumn57.FieldName = "MoChannelAcknowledge2Alerting";
            this.bandedGridColumn57.Name = "bandedGridColumn57";
            this.bandedGridColumn57.Visible = true;
            // 
            // bandedGridColumn58
            // 
            this.bandedGridColumn58.Caption = "Call Proceeding-->Alerting";
            this.bandedGridColumn58.FieldName = "MoCallProceeding2Alerting";
            this.bandedGridColumn58.Name = "bandedGridColumn58";
            this.bandedGridColumn58.Visible = true;
            // 
            // bandedGridColumn26
            // 
            this.bandedGridColumn26.Caption = "log名称";
            this.bandedGridColumn26.FieldName = "MtFileName";
            this.bandedGridColumn26.Name = "bandedGridColumn26";
            this.bandedGridColumn26.RowCount = 4;
            this.bandedGridColumn26.Visible = true;
            // 
            // bandedGridColumn27
            // 
            this.bandedGridColumn27.Caption = "被叫起呼时间";
            this.bandedGridColumn27.DisplayFormat.FormatString = "HH:mm:ss.fff";
            this.bandedGridColumn27.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn27.FieldName = "MtCallAttemptTime";
            this.bandedGridColumn27.Name = "bandedGridColumn27";
            this.bandedGridColumn27.RowCount = 4;
            this.bandedGridColumn27.Visible = true;
            // 
            // bandedGridColumn28
            // 
            this.bandedGridColumn28.Caption = "经度";
            this.bandedGridColumn28.FieldName = "MtLng";
            this.bandedGridColumn28.Name = "bandedGridColumn28";
            this.bandedGridColumn28.RowCount = 4;
            this.bandedGridColumn28.Visible = true;
            // 
            // bandedGridColumn29
            // 
            this.bandedGridColumn29.Caption = "纬度";
            this.bandedGridColumn29.FieldName = "MtLat";
            this.bandedGridColumn29.Name = "bandedGridColumn29";
            this.bandedGridColumn29.RowCount = 4;
            this.bandedGridColumn29.Visible = true;
            // 
            // bandedGridColumn30
            // 
            this.bandedGridColumn30.Caption = "被叫呼叫类型";
            this.bandedGridColumn30.FieldName = "MtPreCallNetType";
            this.bandedGridColumn30.Name = "bandedGridColumn30";
            this.bandedGridColumn30.RowCount = 4;
            this.bandedGridColumn30.Visible = true;
            // 
            // bandedGridColumn31
            // 
            this.bandedGridColumn31.Caption = "TAC";
            this.bandedGridColumn31.FieldName = "MtTac";
            this.bandedGridColumn31.Name = "bandedGridColumn31";
            this.bandedGridColumn31.RowCount = 4;
            this.bandedGridColumn31.Visible = true;
            // 
            // bandedGridColumn32
            // 
            this.bandedGridColumn32.Caption = "ECI";
            this.bandedGridColumn32.FieldName = "MtEci";
            this.bandedGridColumn32.Name = "bandedGridColumn32";
            this.bandedGridColumn32.RowCount = 4;
            this.bandedGridColumn32.Visible = true;
            // 
            // bandedGridColumn53
            // 
            this.bandedGridColumn53.Caption = "ENodeBID";
            this.bandedGridColumn53.FieldName = "MtEnodebid";
            this.bandedGridColumn53.Name = "bandedGridColumn53";
            this.bandedGridColumn53.Visible = true;
            // 
            // bandedGridColumn51
            // 
            this.bandedGridColumn51.Caption = "CellID";
            this.bandedGridColumn51.FieldName = "MtCellid";
            this.bandedGridColumn51.Name = "bandedGridColumn51";
            this.bandedGridColumn51.Visible = true;
            // 
            // bandedGridColumn33
            // 
            this.bandedGridColumn33.Caption = "RSRP(dBm)";
            this.bandedGridColumn33.FieldName = "MtRsrp";
            this.bandedGridColumn33.Name = "bandedGridColumn33";
            this.bandedGridColumn33.RowCount = 4;
            this.bandedGridColumn33.Visible = true;
            // 
            // bandedGridColumn34
            // 
            this.bandedGridColumn34.Caption = "回落小区LAC";
            this.bandedGridColumn34.FieldName = "MtLac";
            this.bandedGridColumn34.Name = "bandedGridColumn34";
            this.bandedGridColumn34.RowCount = 4;
            this.bandedGridColumn34.Visible = true;
            // 
            // bandedGridColumn35
            // 
            this.bandedGridColumn35.Caption = "回落小区CI";
            this.bandedGridColumn35.FieldName = "MtCi";
            this.bandedGridColumn35.Name = "bandedGridColumn35";
            this.bandedGridColumn35.RowCount = 4;
            this.bandedGridColumn35.Visible = true;
            // 
            // bandedGridColumn36
            // 
            this.bandedGridColumn36.Caption = "RxLevSub或PCCPCH RSCP(dBm)";
            this.bandedGridColumn36.FieldName = "MtRxLev_Rscp";
            this.bandedGridColumn36.Name = "bandedGridColumn36";
            this.bandedGridColumn36.RowCount = 4;
            this.bandedGridColumn36.Visible = true;
            // 
            // bandedGridColumn37
            // 
            this.bandedGridColumn37.Caption = "回落制式";
            this.bandedGridColumn37.FieldName = "MtCallNetType";
            this.bandedGridColumn37.Name = "bandedGridColumn37";
            this.bandedGridColumn37.RowCount = 4;
            this.bandedGridColumn37.Visible = true;
            // 
            // bandedGridColumn38
            // 
            this.bandedGridColumn38.Caption = "回落情况";
            this.bandedGridColumn38.FieldName = "MtBackResult";
            this.bandedGridColumn38.Name = "bandedGridColumn38";
            this.bandedGridColumn38.RowCount = 4;
            this.bandedGridColumn38.Visible = true;
            // 
            // bandedGridColumn39
            // 
            this.bandedGridColumn39.Caption = "呼叫结果";
            this.bandedGridColumn39.FieldName = "MtCallResultDesc";
            this.bandedGridColumn39.Name = "bandedGridColumn39";
            this.bandedGridColumn39.RowCount = 4;
            this.bandedGridColumn39.Visible = true;
            // 
            // bandedGridColumn59
            // 
            this.bandedGridColumn59.Caption = "MO Call Proceeding-->MT Paging或CS service notification";
            this.bandedGridColumn59.FieldName = "CallProceeding2Paging";
            this.bandedGridColumn59.Name = "bandedGridColumn59";
            this.bandedGridColumn59.Visible = true;
            // 
            // bandedGridColumn40
            // 
            this.bandedGridColumn40.Caption = "Paging或CS service notification-->Alerting";
            this.bandedGridColumn40.FieldName = "MtPaging2Alerting";
            this.bandedGridColumn40.Name = "bandedGridColumn40";
            this.bandedGridColumn40.RowCount = 4;
            this.bandedGridColumn40.Visible = true;
            // 
            // bandedGridColumn41
            // 
            this.bandedGridColumn41.Caption = "Paging或CS service notification-->Extended Service request";
            this.bandedGridColumn41.FieldName = "MtPaging2ExRequest";
            this.bandedGridColumn41.Name = "bandedGridColumn41";
            this.bandedGridColumn41.RowCount = 4;
            this.bandedGridColumn41.Visible = true;
            // 
            // bandedGridColumn42
            // 
            this.bandedGridColumn42.Caption = "Extended Service request-->RRC Connection Release ";
            this.bandedGridColumn42.FieldName = "MtExRequest2Release";
            this.bandedGridColumn42.Name = "bandedGridColumn42";
            this.bandedGridColumn42.RowCount = 4;
            this.bandedGridColumn42.Visible = true;
            // 
            // bandedGridColumn43
            // 
            this.bandedGridColumn43.Caption = "RRC Connection Release-->RR System Information Type(FRIST GERAN SI) ";
            this.bandedGridColumn43.FieldName = "MtRelease2SysInfo";
            this.bandedGridColumn43.Name = "bandedGridColumn43";
            this.bandedGridColumn43.RowCount = 4;
            this.bandedGridColumn43.Visible = true;
            // 
            // bandedGridColumn44
            // 
            this.bandedGridColumn44.Caption = "RR System Information Type(FRIST GERAN SI) ->RR Paging Response";
            this.bandedGridColumn44.FieldName = "MtSysInfo2Response";
            this.bandedGridColumn44.Name = "bandedGridColumn44";
            this.bandedGridColumn44.RowCount = 4;
            this.bandedGridColumn44.Visible = true;
            // 
            // bandedGridColumn45
            // 
            this.bandedGridColumn45.Caption = "RR Paging Response-->Setup";
            this.bandedGridColumn45.FieldName = "MtResponse2Setup";
            this.bandedGridColumn45.Name = "bandedGridColumn45";
            this.bandedGridColumn45.RowCount = 4;
            this.bandedGridColumn45.Visible = true;
            // 
            // bandedGridColumn63
            // 
            this.bandedGridColumn63.Caption = "RR Paging Response-->Authentication Request";
            this.bandedGridColumn63.FieldName = "MtPResponse2AuRequest";
            this.bandedGridColumn63.Name = "bandedGridColumn63";
            this.bandedGridColumn63.RowCount = 4;
            this.bandedGridColumn63.Visible = true;
            // 
            // bandedGridColumn64
            // 
            this.bandedGridColumn64.Caption = "Authentication Request-->Authentication Response";
            this.bandedGridColumn64.FieldName = "MtAuRequest2AuResponse";
            this.bandedGridColumn64.Name = "bandedGridColumn64";
            this.bandedGridColumn64.RowCount = 4;
            this.bandedGridColumn64.Visible = true;
            // 
            // bandedGridColumn65
            // 
            this.bandedGridColumn65.Caption = "Authentication Response-->Setup";
            this.bandedGridColumn65.FieldName = "MtAuResponse2Setup";
            this.bandedGridColumn65.Name = "bandedGridColumn65";
            this.bandedGridColumn65.RowCount = 4;
            this.bandedGridColumn65.Visible = true;
            // 
            // bandedGridColumn66
            // 
            this.bandedGridColumn66.Caption = "Setup-->Call Confirmed";
            this.bandedGridColumn66.FieldName = "MtSetup2ClConfirmed";
            this.bandedGridColumn66.Name = "bandedGridColumn66";
            this.bandedGridColumn66.RowCount = 4;
            this.bandedGridColumn66.Visible = true;
            // 
            // bandedGridColumn67
            // 
            this.bandedGridColumn67.Caption = "Call Confirmed-->RR Assignment Command";
            this.bandedGridColumn67.FieldName = "MtClConfirmed2AsCommand";
            this.bandedGridColumn67.Name = "bandedGridColumn67";
            this.bandedGridColumn67.RowCount = 4;
            this.bandedGridColumn67.Visible = true;
            // 
            // bandedGridColumn68
            // 
            this.bandedGridColumn68.Caption = "RR Assignment Command-->RR Assignment Complete";
            this.bandedGridColumn68.FieldName = "MtAsCommand2AsComplete";
            this.bandedGridColumn68.Name = "bandedGridColumn68";
            this.bandedGridColumn68.RowCount = 4;
            this.bandedGridColumn68.Visible = true;
            // 
            // bandedGridColumn46
            // 
            this.bandedGridColumn46.Caption = "RR Paging Response-->Call Confirmed";
            this.bandedGridColumn46.FieldName = "MtResponse2Confirmed";
            this.bandedGridColumn46.Name = "bandedGridColumn46";
            this.bandedGridColumn46.RowCount = 4;
            this.bandedGridColumn46.Visible = true;
            // 
            // bandedGridColumn47
            // 
            this.bandedGridColumn47.Caption = "RR Paging Response-->RR Assignment Command";
            this.bandedGridColumn47.FieldName = "MtResponse2AssigmentCmd";
            this.bandedGridColumn47.Name = "bandedGridColumn47";
            this.bandedGridColumn47.RowCount = 4;
            this.bandedGridColumn47.Visible = true;
            // 
            // bandedGridColumn48
            // 
            this.bandedGridColumn48.Caption = "RR Paging Response-->RR Assignment Complete";
            this.bandedGridColumn48.FieldName = "MtResponse2Complete";
            this.bandedGridColumn48.Name = "bandedGridColumn48";
            this.bandedGridColumn48.RowCount = 4;
            this.bandedGridColumn48.Visible = true;
            // 
            // bandedGridColumn49
            // 
            this.bandedGridColumn49.Caption = "RR Assignment Complete-->Alerting";
            this.bandedGridColumn49.FieldName = "MtComplete2Alerting";
            this.bandedGridColumn49.Name = "bandedGridColumn49";
            this.bandedGridColumn49.RowCount = 4;
            this.bandedGridColumn49.Visible = true;
            // 
            // bandedGridColumn69
            // 
            this.bandedGridColumn69.Caption = "主叫寻呼被叫时延";
            this.bandedGridColumn69.FieldName = "MoProceeding2MtPaging";
            this.bandedGridColumn69.Name = "bandedGridColumn69";
            this.bandedGridColumn69.RowCount = 4;
            this.bandedGridColumn69.Visible = true;
            // 
            // gridBand1
            // 
            this.gridBand1.Caption = "Call";
            this.gridBand1.Columns.Add(this.bandedGridColumn1);
            this.gridBand1.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.Width = 75;
            // 
            // gridBand2
            // 
            this.gridBand2.Caption = "主叫";
            this.gridBand2.Columns.Add(this.bandedGridColumn2);
            this.gridBand2.Columns.Add(this.bandedGridColumn3);
            this.gridBand2.Columns.Add(this.bandedGridColumn4);
            this.gridBand2.Columns.Add(this.bandedGridColumn5);
            this.gridBand2.Columns.Add(this.bandedGridColumn6);
            this.gridBand2.Columns.Add(this.bandedGridColumn7);
            this.gridBand2.Columns.Add(this.bandedGridColumn8);
            this.gridBand2.Columns.Add(this.bandedGridColumn52);
            this.gridBand2.Columns.Add(this.bandedGridColumn50);
            this.gridBand2.Columns.Add(this.bandedGridColumn9);
            this.gridBand2.Columns.Add(this.bandedGridColumn10);
            this.gridBand2.Columns.Add(this.bandedGridColumn11);
            this.gridBand2.Columns.Add(this.bandedGridColumn12);
            this.gridBand2.Columns.Add(this.bandedGridColumn13);
            this.gridBand2.Columns.Add(this.bandedGridColumn14);
            this.gridBand2.Columns.Add(this.bandedGridColumn15);
            this.gridBand2.Columns.Add(this.bandedGridColumn16);
            this.gridBand2.Columns.Add(this.bandedGridColumn17);
            this.gridBand2.Columns.Add(this.bandedGridColumn18);
            this.gridBand2.Columns.Add(this.bandedGridColumn19);
            this.gridBand2.Columns.Add(this.bandedGridColumn20);
            this.gridBand2.Columns.Add(this.bandedGridColumn60);
            this.gridBand2.Columns.Add(this.bandedGridColumn61);
            this.gridBand2.Columns.Add(this.bandedGridColumn62);
            this.gridBand2.Columns.Add(this.bandedGridColumn21);
            this.gridBand2.Columns.Add(this.bandedGridColumn54);
            this.gridBand2.Columns.Add(this.bandedGridColumn22);
            this.gridBand2.Columns.Add(this.bandedGridColumn23);
            this.gridBand2.Columns.Add(this.bandedGridColumn24);
            this.gridBand2.Columns.Add(this.bandedGridColumn25);
            this.gridBand2.Columns.Add(this.bandedGridColumn55);
            this.gridBand2.Columns.Add(this.bandedGridColumn56);
            this.gridBand2.Columns.Add(this.bandedGridColumn57);
            this.gridBand2.Columns.Add(this.bandedGridColumn58);
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 2550;
            // 
            // gridBand3
            // 
            this.gridBand3.Caption = "被叫";
            this.gridBand3.Columns.Add(this.bandedGridColumn26);
            this.gridBand3.Columns.Add(this.bandedGridColumn27);
            this.gridBand3.Columns.Add(this.bandedGridColumn28);
            this.gridBand3.Columns.Add(this.bandedGridColumn29);
            this.gridBand3.Columns.Add(this.bandedGridColumn30);
            this.gridBand3.Columns.Add(this.bandedGridColumn31);
            this.gridBand3.Columns.Add(this.bandedGridColumn32);
            this.gridBand3.Columns.Add(this.bandedGridColumn53);
            this.gridBand3.Columns.Add(this.bandedGridColumn51);
            this.gridBand3.Columns.Add(this.bandedGridColumn33);
            this.gridBand3.Columns.Add(this.bandedGridColumn34);
            this.gridBand3.Columns.Add(this.bandedGridColumn35);
            this.gridBand3.Columns.Add(this.bandedGridColumn36);
            this.gridBand3.Columns.Add(this.bandedGridColumn37);
            this.gridBand3.Columns.Add(this.bandedGridColumn38);
            this.gridBand3.Columns.Add(this.bandedGridColumn39);
            this.gridBand3.Columns.Add(this.bandedGridColumn59);
            this.gridBand3.Columns.Add(this.bandedGridColumn69);
            this.gridBand3.Columns.Add(this.bandedGridColumn40);
            this.gridBand3.Columns.Add(this.bandedGridColumn41);
            this.gridBand3.Columns.Add(this.bandedGridColumn42);
            this.gridBand3.Columns.Add(this.bandedGridColumn43);
            this.gridBand3.Columns.Add(this.bandedGridColumn44);
            this.gridBand3.Columns.Add(this.bandedGridColumn45);
            this.gridBand3.Columns.Add(this.bandedGridColumn63);
            this.gridBand3.Columns.Add(this.bandedGridColumn64);
            this.gridBand3.Columns.Add(this.bandedGridColumn65);
            this.gridBand3.Columns.Add(this.bandedGridColumn66);
            this.gridBand3.Columns.Add(this.bandedGridColumn67);
            this.gridBand3.Columns.Add(this.bandedGridColumn68);
            this.gridBand3.Columns.Add(this.bandedGridColumn46);
            this.gridBand3.Columns.Add(this.bandedGridColumn47);
            this.gridBand3.Columns.Add(this.bandedGridColumn48);
            this.gridBand3.Columns.Add(this.bandedGridColumn49);
            this.gridBand3.Name = "gridBand3";
            this.gridBand3.Width = 2550;
            // 
            // CsfbCallStatListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1147, 453);
            this.Controls.Add(this.gridControl);
            this.Name = "CsfbCallStatListForm";
            this.Text = "CSFB主被叫统计";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn7;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn9;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn11;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn12;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn13;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn14;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn15;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn16;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn17;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn21;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn22;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn23;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn24;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn25;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn26;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn27;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn28;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn29;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn30;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn31;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn32;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn33;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn34;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn35;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn36;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn37;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn38;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn39;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn40;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn41;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn42;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn43;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn44;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn45;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn46;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn47;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn48;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn49;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn50;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn51;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn52;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn53;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn54;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn55;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn56;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn57;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn58;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn59;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn60;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn61;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn62;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn63;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn64;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn65;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn66;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn67;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn68;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn69;
    }
}