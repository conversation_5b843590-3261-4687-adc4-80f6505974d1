﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.CQT
{
    public class ShowCQTPointListForm : QueryBase
    {
        public ShowCQTPointListForm()
            : base(MainModel.GetInstance())
        {

        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        public override string Name
        {
            get { return "显示CQT地点列表"; }
        }

        public override string IconName
        {
            get { return null; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 21000, 21001, this.Name);
        }

        protected override void query()
        {
            CQTPointManager.GetInstance().QueryPntFromDB();
            CQTPointListForm listForm = MainModel.GetObjectFromBlackboard(typeof(CQTPointListForm)) as CQTPointListForm;
            if (listForm == null || listForm.IsDisposed)
            {
                listForm = new CQTPointListForm(MainModel);
            }
            listForm.FillData(getCQTPoints());
            listForm.Owner = MainModel.MainForm;
            listForm.Visible = true;
            listForm.BringToFront();
        }


        private List<CQTPoint> getCQTPoints()
        {
            List<CQTPoint> pnts = new List<CQTPoint>();
            if (MainModel.SelCQTPoint != null)
            {
                pnts.Add(MainModel.SelCQTPoint);
            }
            else if (MainModel.SearchGeometrys != null
                && MainModel.SearchGeometrys.Region != null)
            {
                pnts = CQTPointManager.GetInstance().GetPointsByRegion(MainModel.SearchGeometrys.GeoOp);
            }
            else
            {
                pnts.AddRange(CQTPointManager.GetInstance().CQTPoints);
            }
            return pnts;
        }

    }
}
