﻿namespace MasterCom.RAMS.Model.RoadProtection
{
    partial class XtraFormRoadProtection
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.Grid_Export = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.导出ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.gview_GSM = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            ((System.ComponentModel.ISupportInitialize)(this.Grid_Export)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gview_GSM)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            this.SuspendLayout();
            // 
            // Grid_Export
            // 
            this.Grid_Export.ContextMenuStrip = this.contextMenuStrip1;
            this.Grid_Export.Dock = System.Windows.Forms.DockStyle.Fill;
            this.Grid_Export.Location = new System.Drawing.Point(0, 0);
            this.Grid_Export.MainView = this.gview_GSM;
            this.Grid_Export.Name = "Grid_Export";
            this.Grid_Export.Size = new System.Drawing.Size(732, 453);
            this.Grid_Export.TabIndex = 0;
            this.Grid_Export.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gview_GSM,
            this.gridView2});
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.导出ToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(99, 26);
            // 
            // 导出ToolStripMenuItem
            // 
            this.导出ToolStripMenuItem.Name = "导出ToolStripMenuItem";
            this.导出ToolStripMenuItem.Size = new System.Drawing.Size(98, 22);
            this.导出ToolStripMenuItem.Text = "导出";
            this.导出ToolStripMenuItem.Click += new System.EventHandler(this.导出ToolStripMenuItem_Click);
            // 
            // gview_GSM
            // 
            this.gview_GSM.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gview_GSM.GridControl = this.Grid_Export;
            this.gview_GSM.Name = "gview_GSM";
            this.gview_GSM.OptionsCustomization.AllowGroup = false;
            this.gview_GSM.OptionsCustomization.AllowRowSizing = true;
            this.gview_GSM.OptionsCustomization.AllowSort = false;
            this.gview_GSM.OptionsFilter.AllowColumnMRUFilterList = false;
            this.gview_GSM.OptionsFilter.AllowFilterEditor = false;
            this.gview_GSM.OptionsFilter.AllowMRUFilterList = false;
            this.gview_GSM.OptionsFilter.ShowAllTableValuesInFilterPopup = true;
            this.gview_GSM.OptionsFilter.UseNewCustomFilterDialog = true;
            this.gview_GSM.OptionsMenu.EnableColumnMenu = false;
            this.gview_GSM.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gview_GSM.OptionsView.AllowCellMerge = true;
            this.gview_GSM.OptionsView.ColumnAutoWidth = false;
            this.gview_GSM.OptionsView.ShowDetailButtons = false;
            this.gview_GSM.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
            this.gview_GSM.OptionsView.ShowGroupPanel = false;
            this.gview_GSM.OptionsView.ShowPreviewLines = false;
            this.gview_GSM.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowAlways;
            this.gview_GSM.CustomDrawCell += new DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventHandler(this.gview_GSM_CustomDrawCell);
            // 
            // gridView2
            // 
            this.gridView2.GridControl = this.Grid_Export;
            this.gridView2.Name = "gridView2";
            // 
            // XtraFormRoadProtection
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(732, 453);
            this.Controls.Add(this.Grid_Export);
            this.Name = "XtraFormRoadProtection";
            this.Text = "XtraFormRoadProtection";
            ((System.ComponentModel.ISupportInitialize)(this.Grid_Export)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gview_GSM)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl Grid_Export;
        private DevExpress.XtraGrid.Views.Grid.GridView gview_GSM;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem 导出ToolStripMenuItem;
    }
}