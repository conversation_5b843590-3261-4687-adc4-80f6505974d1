using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakCoverBlock
    {
        public WeakCoverBlock() { }

        public WeakCoverBlock(int idx, TestPoint item, float rscpMain)
        {
            this.ID = idx;
            AddTestPoint(item, rscpMain);
            LongitudeMid = item.Longitude;
            LatitudeMid = item.Latitude;
        }
        public int ID { get; set; }
        public List<CellOfWeakCoverBlock> Celllist { get; set; } = new List<CellOfWeakCoverBlock>();
        public List<float> RxlevList { get; set; } = new List<float>();
        private float rxLevAvg = 0;
        public float RxLevAvg
        {
            get { return rxLevAvg; }
        }
        public List<TDCell> Cells { get; set; } = new List<TDCell>();
        public List<WCellOfWeakCoverBlock> WCelllist { get; set; } = new List<WCellOfWeakCoverBlock>();
        public List<WCell> wCells { get; set; } = new List<WCell>();
        public List<TestPoint> TestPoints { get; set; } = new List<TestPoint>();
        public int TestPointCount { get; set; } = 0;

        public string TestPointDescription
        {
            get
            {
                string desc = "";
                return desc;
            }
        }

        public string TestPointsDetails
        {
            get
            {
                string desc = "";
                return desc;
            }
        }

        public int istime { get; set; } = int.MaxValue;
        public int ietime { get; set; } = int.MinValue;

        public double LongitudeMid { get; set; }
        public double LatitudeMid { get; set; }

        private string roadPlaceDesc = null;
        public string RoadPlaceDesc
        {
            get { return roadPlaceDesc; }
        }

        private string areaPlaceDesc = null;
        public string AreaPlaceDesc
        {
            get { return areaPlaceDesc; }
        }

        private string gridDesc = null;
        public string GridDesc
        {
            get { return gridDesc; }
        }

        private string areaAgentDesc = null;
        public string AreaAgentDesc
        {
            get { return areaAgentDesc; }
        }

        internal void Join(WeakCoverBlock tpBlock)
        {
            for (int i = 0; i < tpBlock.TestPoints.Count; i++)
            {
                TestPoint tp = tpBlock.TestPoints[i];
                float rxlev = tpBlock.RxlevList[i];
                AddTestPoint(tp, rxlev);
            }
            this.Celllist.AddRange(tpBlock.Celllist);
        }

        internal bool Within(double x1, double y1, double x2, double y2)
        {
            if (LongitudeMid >= x1 && LongitudeMid <= x2 && LatitudeMid >= y1 || LatitudeMid <= y2)
                {
                    return true;
                }
            return false;
        }

        public void AddTestPoint(TestPoint tp, float rscp)
        {
            if (!testPointContained(tp))
            {
                TestPoints.Add(tp);
                RxlevList.Add(rscp);
                if (istime > tp.Time)
                {
                    istime = tp.Time;
                }
                if (ietime < tp.Time)
                {
                    ietime = tp.Time;
                }
            }
        }

        public bool Intersect(double longitude, double latitude, int radius)
        {
            if (MathFuncs.GetDistance(LongitudeMid, LatitudeMid, longitude, latitude) <= radius)
            {
                return true;
            }
            return false;
        }

        private bool testPointContained(TestPoint tp)
        {
            return TestPoints.Contains(tp);
        }

        public void GetResult()
        {
            float sum = 0;
            foreach (float item in RxlevList)
            {
                sum += item;
            }
            rxLevAvg = sum == 0 ? 0 : (float)Math.Round((double)sum / RxlevList.Count, 2);
            TestPointCount = TestPoints.Count;
            areaPlaceDesc = GISManager.GetInstance().GetAreaPlaceDesc(LongitudeMid, LatitudeMid);
            roadPlaceDesc = GISManager.GetInstance().GetRoadPlaceDesc(LongitudeMid, LatitudeMid);
            gridDesc = GISManager.GetInstance().GetGridDesc(LongitudeMid, LatitudeMid);
            areaAgentDesc = GISManager.GetInstance().GetAreaAgentDesc(LongitudeMid, LatitudeMid);
        }

        public BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.ISTime = istime;
            bgResult.IETime = ietime;
            bgResult.LongitudeMid = LongitudeMid;
            bgResult.LatitudeMid = LatitudeMid;
            bgResult.SampleCount = TestPointCount;
            bgResult.RxLevMean = rxLevAvg;
            bgResult.RoadDesc = roadPlaceDesc;
            bgResult.AreaDesc = areaPlaceDesc;
            bgResult.AreaAgentDesc = areaAgentDesc;
            bgResult.GridDesc = gridDesc;
            return bgResult;
        }
    }

    public class CellOfWeakCoverBlock
    {
        public CellOfWeakCoverBlock() { }
        public CellOfWeakCoverBlock(int sn, TDCell cell, float pccpch_rscp)
        {
            this.Sn = sn;
            this.Cell = cell;
            this.Pccpch_rscp = pccpch_rscp;
        }
        public int Sn { get; set; }
        public TDCell Cell { get; set; }
        public float Pccpch_rscp { get; set; }
        public int SampleCount { get; set; }
    }

    public class WCellOfWeakCoverBlock
    {
        public WCellOfWeakCoverBlock() { }
        public WCellOfWeakCoverBlock(int sn, WCell cell, float pccpch_rscp)
        {
            this.Sn = sn;
            this.Cell = cell;
            this.Pccpch_rscp = pccpch_rscp;
        }
        public int Sn { get; set; }
        public WCell Cell { get; set; }
        public float Pccpch_rscp { get; set; }
        public int SampleCount { get; set; }
    }
}
