﻿using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLTERRCReleaseAnaByRegion : ZTLTERRCReleaseAnaBase
    {
        public ZTLTERRCReleaseAnaByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected static readonly object lockObj = new object();
        private static ZTLTERRCReleaseAnaByRegion intance = null;
        public static ZTLTERRCReleaseAnaByRegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTLTERRCReleaseAnaByRegion(MainModel.GetInstance());
                    }
                }
            }
            return intance;
        }

        public ZTLTERRCReleaseAnaByRegion(bool isVoLTE)
            : base(MainModel.GetInstance())
        {
            this.isVoLTE = isVoLTE;
        }

        public override string Name
        {
            get { return "LTE异频重定向(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22050, this.Name);//////
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        /// <summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is LTETestPointDetail)
                {
                    return Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }

    public class ZTLTERRCReleaseAnaByRegion_FDD : ZTLTERRCReleaseAnaByRegion
    {
        private static ZTLTERRCReleaseAnaByRegion_FDD instance = null;
        public static new ZTLTERRCReleaseAnaByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTLTERRCReleaseAnaByRegion_FDD(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public ZTLTERRCReleaseAnaByRegion_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get { return "LTE_FDD异频重定向(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26048, this.Name);//////
        }
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is LTEFddTestPoint)
                {
                    return Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}