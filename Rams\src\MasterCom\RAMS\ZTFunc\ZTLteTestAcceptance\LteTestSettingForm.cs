﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTLteTestAcceptance
{
    public partial class LteTestSettingForm : BaseDialog
    {
        public LteTestSettingForm(string strFormText)
            : this()
        {
            this.Text = strFormText;
            SetControlVisible(strFormText);
        }

        public void SetControlVisible(string strFormText)
        {
            if (strFormText == "LTE小站验收")
            {
                groupBox1.Visible = false;
                groupBox3.Visible = true;
            }
            else if (strFormText == "LTE室分验收")
            {
                groupBox1.Visible = true;
                groupBox3.Visible = false;
            }
            else
            {
                groupBox1.Visible = true;
                groupBox3.Visible = true;
            }
        }

        public LteTestSettingForm()
        {
            InitializeComponent();
            SetControlVisible("LTE室分验收");
            this.btnOK.Click += BtnOK_Click;
            this.btnCancel.Click += BtnCancel_Click;
            this.btnFolder.Click += BtnFolder_Click;
        }

        public void setSmallCondition(LteSmallStationSettingFormConfigModel_XJ condition)
        {
            if (condition == null)
            {
                return;
            }

            txtAccessSuccessRate1.Value = Convert.ToDecimal(condition.AccessSuccessRate);
            txtAccessTestCount1.Value = Convert.ToDecimal(condition.AccessTestCount);
            txtVolteTestCount1.Value = Convert.ToDecimal(condition.VOLTETestCount);
            txtVolteSuccessRate1.Value = Convert.ToDecimal(condition.VOLTESuccessRate);
            txtFTPDownSpeed1.Value = Convert.ToDecimal(condition.FTPDownloadThroughput);
            txtFTPUpSpeed1.Value = Convert.ToDecimal(condition.FTPUploadThroughput);
            txtDoorAvgRSRP.Value = Convert.ToDecimal(condition.InDoorAvgRSRP);
            txtDoorAvgSINR.Value = Convert.ToDecimal(condition.InDoorAvgSINR);
            txtDoorDownThroughput.Value = Convert.ToDecimal(condition.InDoorDownThroughput);
            txtDoorUpThroughput.Value = Convert.ToDecimal(condition.InDoorUpThroughput);
            txtAntAvgRSRP.Value = Convert.ToDecimal(condition.AntAvgRSRP);
            txtAntAvgSINR.Value = Convert.ToDecimal(condition.AntAvgSINR);
            txtStandard1.Text = condition.WeakCoverCheckStandard;
            txtSystemInSwitch1.Value = Convert.ToDecimal(condition.SystemInSwitch);
        }

        public LteSmallStationSettingFormConfigModel_XJ getSmallSationInfo()
        {
            LteSmallStationSettingFormConfigModel_XJ condition = new LteSmallStationSettingFormConfigModel_XJ();
            condition.AccessSuccessRate = txtAccessSuccessRate1.Value.ToString();
            condition.AccessTestCount = Convert.ToInt32(txtAccessTestCount1.Value);
            condition.VOLTETestCount = Convert.ToInt32(txtVolteTestCount1.Value);
            condition.VOLTESuccessRate = txtVolteSuccessRate1.Value.ToString();
            condition.FTPDownloadThroughput = txtFTPDownSpeed1.Value.ToString();
            condition.FTPUploadThroughput = txtFTPUpSpeed1.Value.ToString();
            condition.InDoorAvgRSRP = txtDoorAvgRSRP.Value.ToString();
            condition.InDoorAvgSINR = txtDoorAvgSINR.Value.ToString();
            condition.InDoorDownThroughput = txtDoorDownThroughput.Value.ToString();
            condition.InDoorUpThroughput = txtDoorUpThroughput.Value.ToString();
            condition.AntAvgRSRP = txtAntAvgRSRP.Value.ToString();
            condition.AntAvgSINR = txtAntAvgSINR.Value.ToString();
            condition.SystemInSwitch = txtSystemInSwitch1.Value.ToString();
            condition.WeakCoverCheckStandard = txtStandard1.Text;
            return condition;
        }

        public void setDoorCondition(LteDoorStationSettingFormConfigModel_XJ condition)
        {
            if (condition == null)
            {
                return;
            }

            txtAccessRate.Value = Convert.ToDecimal(condition.AccessSuccessRate);
            txtAccessTestCount.Value = Convert.ToDecimal(condition.AccessTestCount);
            txtCSFBTestCount.Value = Convert.ToDecimal(condition.CSFBTestCount);
            txtCSFBSuccessRate.Value = Convert.ToDecimal(condition.CSFBSuccessRate);
            txtVolteTestCount.Value = Convert.ToDecimal(condition.VOLTETestCount);
            txtVolteSuccessRate.Value = Convert.ToDecimal(condition.VOLTESuccessRate);
            txtFTPDownSpeed.Value = Convert.ToDecimal(condition.FTPDownloadThroughput);
            txtFTPUpSpeed.Value = Convert.ToDecimal(condition.FTPUploadThroughput);
            txtSwitchCount.Value = Convert.ToDecimal(condition.SwitchCount);
            txtSwitchSuccessRate.Value = Convert.ToDecimal(condition.SwtichSuccessRate);
            txtAvgRSRP.Value = Convert.ToDecimal(condition.AvgRSRP);
            txtAvgSINR.Value = Convert.ToDecimal(condition.AvgSINR);
            txtStandard.Text = condition.WeakCoverCheckStandard;
            txtSystemInSwitch.Value = Convert.ToDecimal(condition.SystemInSwitch);
        }

        public LteDoorStationSettingFormConfigModel_XJ getDoorStationInfo()
        {
            LteDoorStationSettingFormConfigModel_XJ condition = new LteDoorStationSettingFormConfigModel_XJ();
            condition.AccessSuccessRate = txtAccessRate.Value.ToString();
            condition.AccessTestCount = Convert.ToInt32(txtAccessTestCount.Value);
            condition.CSFBTestCount = Convert.ToInt32(txtCSFBTestCount.Value);
            condition.CSFBSuccessRate = txtCSFBSuccessRate.Value.ToString();
            condition.VOLTETestCount = Convert.ToInt32(txtVolteTestCount.Value);
            condition.VOLTESuccessRate = txtVolteSuccessRate.Value.ToString();
            condition.FTPDownloadThroughput = txtFTPDownSpeed.Value.ToString();
            condition.FTPUploadThroughput = txtFTPUpSpeed.Value.ToString();
            condition.SwitchCount = Convert.ToInt32(txtSwitchCount.Value);
            condition.SwtichSuccessRate = txtSwitchSuccessRate.Value.ToString();
            condition.AvgRSRP = txtAvgRSRP.Value.ToString();
            condition.AvgSINR = txtAvgSINR.Value.ToString();
            condition.WeakCoverCheckStandard = txtStandard.Text;
            condition.SystemInSwitch = txtSystemInSwitch.Value.ToString();
            return condition;
        }

        public LteTestAcceptCondition GetCondition()
        {
            LteTestAcceptCondition cond = new LteTestAcceptCondition();
            cond.SaveFolder = txtFolder.Text;
            return cond;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtFolder.Text))
            {
                MessageBox.Show("请选择导出文件保存目录!", this.Text,
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = System.Windows.Forms.DialogResult.None;
                return;
            }

            DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void BtnFolder_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog dlg = new FolderBrowserDialog();
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                txtFolder.Text = dlg.SelectedPath;
            }
        }

        private void btnOK_Click_1(object sender, EventArgs e)
        {
            if (this.Text == "LTE室分验收")
            {
                var cond = getDoorStationInfo();
                if (cond != null)
                {
                    LteDoorStationSettingFormConfig_XJ.Instance.SaveConfig(cond);
                    DialogResult = DialogResult.OK;
                }
                else
                {
                    MessageBox.Show("主库连接设置不能为空");
                }
            }
            else if (this.Text == "LTE小站验收")
            {
                var cond1 = getSmallSationInfo();
                if (cond1 != null)
                {
                    LteSmallStationSettingFormConfig_XJ.Instance.SaveConfig(cond1);
                    DialogResult = DialogResult.OK;
                }
                else
                {
                    MessageBox.Show("主库连接设置不能为空");
                }
            }
        }

        private void LteTestSettingForm_Load(object sender, EventArgs e)
        {

        }
    }

    public class LteTestAcceptCondition
    {
        public bool IsByFile
        {
            get;
            set;
        }

        public bool IsByDT
        {
            get;
            set;
        }

        public string SaveFolder
        {
            get;
            set;
        }
    }
}
