<?xml version="1.0"?>
<Configs>
  <Config name="StatParamCfg">
    <Item name="configs" typeName="IList">
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">WLAN参数</Item>
        <Item typeName="String" key="FDesc" />
        <Item typeName="String" key="FName" />
        <Item typeName="IList" key="children">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">基本参数</Item>
            <Item typeName="String" key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Fileid</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_0801</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TestType</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_0802</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">MS</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_0803</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">FileTime</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_0804</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration</Item>
                <Item typeName="String" key="FDesc">总时长</Item>
                <Item typeName="String" key="FName">Wl_0805</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance</Item>
                <Item typeName="String" key="FDesc">总里程</Item>
                <Item typeName="String" key="FName">Wl_0806</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum</Item>
                <Item typeName="String" key="FDesc">总采样点数</Item>
                <Item typeName="String" key="FName">Wl_0807</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">AP_CHANNEL</Item>
            <Item typeName="String" key="FDesc">14个Channel指标分别统计</Item>
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">AP_Channel_1</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_AP_Channel</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0101</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_AP_Freq</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0102</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_AP_RSSI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0103</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_SFI_AP_SFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1001</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_NFI_AP_NFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1101</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_CI_AP_CI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1201</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_RxThrput_RxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1301</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_RxPhysicalRate_RxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1401</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_RxRetrans_RxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1501</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_TxThrput_TxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1601</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_TxPhysicalRate_TxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1701</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_TxRetrans_TxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1801</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_AP_RSSI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0104</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_SFI_AP_SFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1002</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_NFI_AP_NFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1102</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_AP_NFI_Channel_1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0105</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_AP_NFI_Channel_2</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0106</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_AP_NFI_Channel_3</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0107</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_AP_NFI_Channel_4</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0108</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_CI_AP_CI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1202</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_BBS_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0109</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_BBS_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F010A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_Channel_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F010B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_Channel_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F010C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_RxThrput_RxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1302</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_RxPhysicalRate_RxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1402</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_RxRetrans_RxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1502</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_TxThrput_TxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1602</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_TxPhysicalRate_TxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1702</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_1_TxRetrans_TxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1802</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">AP_Channel_2</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_AP_Channel</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0201</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_AP_Freq</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0202</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_AP_RSSI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0203</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_SFI_AP_SFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1901</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_NFI_AP_NFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1A01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_CI_AP_CI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1B01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_RxThrput_RxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1C01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_RxPhysicalRate_RxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1D01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_RxRetrans_RxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1E01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_TxThrput_TxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1F01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_TxPhysicalRate_TxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2001</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_TxRetrans_TxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2101</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_AP_RSSI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0204</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_SFI_AP_SFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1902</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_NFI_AP_NFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1A02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_AP_NFI_Channel_1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0205</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_AP_NFI_Channel_2</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0206</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_AP_NFI_Channel_3</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0207</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_AP_NFI_Channel_4</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0208</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_CI_AP_CI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1B02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_BBS_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0209</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_BBS_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F020A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_Channel_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F020B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_Channel_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F020C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_RxThrput_RxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1C02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_RxPhysicalRate_RxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1D02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_RxRetrans_RxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1E02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_TxThrput_TxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F1F02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_TxPhysicalRate_TxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2002</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_2_TxRetrans_TxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2102</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">AP_Channel_3</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_AP_Channel</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0301</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_AP_Freq</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0302</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_AP_RSSI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0303</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_SFI_AP_SFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2201</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_NFI_AP_NFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2301</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_CI_AP_CI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2401</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_RxThrput_RxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2501</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_RxPhysicalRate_RxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2601</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_RxRetrans_RxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2701</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_TxThrput_TxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2801</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_TxPhysicalRate_TxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2901</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_TxRetrans_TxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2A01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_AP_RSSI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0304</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_SFI_AP_SFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2202</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_NFI_AP_NFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2302</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_AP_NFI_Channel_1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0305</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_AP_NFI_Channel_2</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0306</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_AP_NFI_Channel_3</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0307</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_AP_NFI_Channel_4</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0308</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_CI_AP_CI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2402</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_BBS_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0309</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_BBS_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F030A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_Channel_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F030B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_Channel_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F030C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_RxThrput_RxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2502</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_RxPhysicalRate_RxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2602</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_RxRetrans_RxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2702</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_TxThrput_TxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2802</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_TxPhysicalRate_TxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2902</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_3_TxRetrans_TxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2A02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">AP_Channel_4</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_AP_Channel</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0401</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_AP_Freq</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0402</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_AP_RSSI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0403</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_SFI_AP_SFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2B01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_NFI_AP_NFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2C01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_CI_AP_CI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2D01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_RxThrput_RxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2E01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_RxPhysicalRate_RxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2F01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_RxRetrans_RxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3001</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_TxThrput_TxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3101</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_TxPhysicalRate_TxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3201</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_TxRetrans_TxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3301</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_AP_RSSI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0404</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_SFI_AP_SFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2B02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_NFI_AP_NFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2C02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_AP_NFI_Channel_1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0405</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_AP_NFI_Channel_2</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0406</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_AP_NFI_Channel_3</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0407</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_AP_NFI_Channel_4</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0408</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_CI_AP_CI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2D02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_BBS_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0409</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_BBS_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F040A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_Channel_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F040B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_Channel_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F040C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_RxThrput_RxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2E02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_RxPhysicalRate_RxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F2F02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_RxRetrans_RxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3002</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_TxThrput_TxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3102</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_TxPhysicalRate_TxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3202</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_4_TxRetrans_TxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3302</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">AP_Channel_5</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_AP_Channel</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0501</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_AP_Freq</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0502</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_AP_RSSI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0503</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_SFI_AP_SFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3401</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_NFI_AP_NFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3501</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_CI_AP_CI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3601</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_RxThrput_RxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3701</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_RxPhysicalRate_RxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3801</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_RxRetrans_RxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3901</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_TxThrput_TxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3A01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_TxPhysicalRate_TxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3B01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_TxRetrans_TxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3C01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_AP_RSSI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0504</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_SFI_AP_SFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3402</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_NFI_AP_NFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3502</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_AP_NFI_Channel_1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0505</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_AP_NFI_Channel_2</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0506</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_AP_NFI_Channel_3</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0507</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_AP_NFI_Channel_4</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0508</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_CI_AP_CI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3602</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_BBS_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0509</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_BBS_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F050A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_Channel_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F050B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_Channel_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F050C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_RxThrput_RxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3702</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_RxPhysicalRate_RxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3802</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_RxRetrans_RxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3902</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_TxThrput_TxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3A02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_TxPhysicalRate_TxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3B02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_5_TxRetrans_TxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3C02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">AP_Channel_6</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_AP_Channel</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0601</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_AP_Freq</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0602</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_AP_RSSI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0603</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_SFI_AP_SFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3D01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_NFI_AP_NFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3E01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_CI_AP_CI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3F01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_RxThrput_RxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4001</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_RxPhysicalRate_RxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4101</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_RxRetrans_RxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4201</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_TxThrput_TxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4301</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_TxPhysicalRate_TxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4401</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_TxRetrans_TxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4501</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_AP_RSSI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0604</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_SFI_AP_SFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3D02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_NFI_AP_NFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3E02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_AP_NFI_Channel_1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0605</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_AP_NFI_Channel_2</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0606</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_AP_NFI_Channel_3</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0607</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_AP_NFI_Channel_4</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0608</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_CI_AP_CI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F3F02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_BBS_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0609</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_BBS_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F060A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_Channel_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F060B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_Channel_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F060C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_RxThrput_RxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4002</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_RxPhysicalRate_RxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4102</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_RxRetrans_RxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4202</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_TxThrput_TxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4302</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_TxPhysicalRate_TxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4402</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_6_TxRetrans_TxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4502</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">AP_Channel_7</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_AP_Channel</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0701</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_AP_Freq</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0702</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_AP_RSSI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0703</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_SFI_AP_SFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4601</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_NFI_AP_NFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4701</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_CI_AP_CI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4801</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_RxThrput_RxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4901</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_RxPhysicalRate_RxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4A01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_RxRetrans_RxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4B01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_TxThrput_TxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4C01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_TxPhysicalRate_TxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4D01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_TxRetrans_TxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4E01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_AP_RSSI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0704</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_SFI_AP_SFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4602</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_NFI_AP_NFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4702</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_AP_NFI_Channel_1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0705</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_AP_NFI_Channel_2</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0706</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_AP_NFI_Channel_3</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0707</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_AP_NFI_Channel_4</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0708</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_CI_AP_CI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4802</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_BBS_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0709</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_BBS_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F070A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_Channel_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F070B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_Channel_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F070C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_RxThrput_RxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4902</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_RxPhysicalRate_RxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4A02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_RxRetrans_RxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4B02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_TxThrput_TxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4C02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_TxPhysicalRate_TxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4D02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_7_TxRetrans_TxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4E02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">AP_Channel_8</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_AP_Channel</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0801</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_AP_Freq</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0802</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_AP_RSSI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0803</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_SFI_AP_SFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4F01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_NFI_AP_NFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5001</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_CI_AP_CI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5101</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_RxThrput_RxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5201</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_RxPhysicalRate_RxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5301</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_RxRetrans_RxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5401</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_TxThrput_TxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5501</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_TxPhysicalRate_TxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5601</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_TxRetrans_TxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5701</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_AP_RSSI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0804</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_SFI_AP_SFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F4F02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_NFI_AP_NFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5002</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_AP_NFI_Channel_1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0805</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_AP_NFI_Channel_2</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0806</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_AP_NFI_Channel_3</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0807</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_AP_NFI_Channel_4</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0808</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_CI_AP_CI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5102</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_BBS_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0809</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_BBS_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F080A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_Channel_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F080B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_Channel_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F080C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_RxThrput_RxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5202</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_RxPhysicalRate_RxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5302</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_RxRetrans_RxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5402</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_TxThrput_TxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5502</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_TxPhysicalRate_TxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5602</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_8_TxRetrans_TxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5702</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">AP_Channel_9</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_AP_Channel</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0901</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_AP_Freq</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0902</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_AP_RSSI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0903</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_SFI_AP_SFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5801</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_NFI_AP_NFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5901</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_CI_AP_CI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5A01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_RxThrput_RxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5B01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_RxPhysicalRate_RxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5C01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_RxRetrans_RxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5D01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_TxThrput_TxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5E01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_TxPhysicalRate_TxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5F01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_TxRetrans_TxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6001</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_AP_RSSI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0904</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_SFI_AP_SFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5802</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_NFI_AP_NFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5902</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_AP_NFI_Channel_1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0905</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_AP_NFI_Channel_2</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0906</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_AP_NFI_Channel_3</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0907</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_AP_NFI_Channel_4</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0908</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_CI_AP_CI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5A02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_BBS_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0909</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_BBS_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F090A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_Channel_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F090B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_Channel_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F090C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_RxThrput_RxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5B02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_RxPhysicalRate_RxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5C02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_RxRetrans_RxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5D02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_TxThrput_TxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5E02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_TxPhysicalRate_TxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F5F02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_9_TxRetrans_TxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6002</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">AP_Channel_10</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_AP_Channel</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0A01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_AP_Freq</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0A02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_AP_RSSI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0A03</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_SFI_AP_SFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6101</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_NFI_AP_NFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6201</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_CI_AP_CI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6301</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_RxThrput_RxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6401</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_RxPhysicalRate_RxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6501</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_RxRetrans_RxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6601</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_TxThrput_TxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6701</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_TxPhysicalRate_TxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6801</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_TxRetrans_TxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6901</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_AP_RSSI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0A04</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_SFI_AP_SFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6102</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_NFI_AP_NFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6202</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_AP_NFI_Channel_1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0A05</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_AP_NFI_Channel_2</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0A06</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_AP_NFI_Channel_3</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0A07</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_AP_NFI_Channel_4</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0A08</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_CI_AP_CI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6302</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_BBS_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0A09</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_BBS_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0A0A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_Channel_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0A0B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_Channel_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0A0C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_RxThrput_RxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6402</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_RxPhysicalRate_RxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6502</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_RxRetrans_RxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6602</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_TxThrput_TxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6702</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_TxPhysicalRate_TxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6802</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_10_TxRetrans_TxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6902</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">AP_Channel_11</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_AP_Channel</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0B01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_AP_Freq</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0B02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_AP_RSSI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0B03</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_SFI_AP_SFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6A01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_NFI_AP_NFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6B01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_CI_AP_CI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6C01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_RxThrput_RxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6D01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_RxPhysicalRate_RxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6E01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_RxRetrans_RxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6F01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_TxThrput_TxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7001</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_TxPhysicalRate_TxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7101</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_TxRetrans_TxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7201</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_AP_RSSI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0B04</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_SFI_AP_SFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6A02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_NFI_AP_NFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6B02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_AP_NFI_Channel_1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0B05</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_AP_NFI_Channel_2</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0B06</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_AP_NFI_Channel_3</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0B07</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_AP_NFI_Channel_4</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0B08</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_CI_AP_CI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6C02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_BBS_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0B09</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_BBS_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0B0A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_Channel_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0B0B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_Channel_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0B0C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_RxThrput_RxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6D02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_RxPhysicalRate_RxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6E02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_RxRetrans_RxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F6F02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_TxThrput_TxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7002</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_TxPhysicalRate_TxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7102</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_11_TxRetrans_TxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7202</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">AP_Channel_12</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_AP_Channel</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0C01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_AP_Freq</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0C02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_AP_RSSI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0C03</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_SFI_AP_SFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7301</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_NFI_AP_NFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7401</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_CI_AP_CI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7501</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_RxThrput_RxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7601</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_RxPhysicalRate_RxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7701</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_RxRetrans_RxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7801</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_TxThrput_TxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7901</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_TxPhysicalRate_TxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7A01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_TxRetrans_TxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7B01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_AP_RSSI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0C04</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_SFI_AP_SFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7302</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_NFI_AP_NFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7402</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_AP_NFI_Channel_1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0C05</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_AP_NFI_Channel_2</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0C06</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_AP_NFI_Channel_3</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0C07</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_AP_NFI_Channel_4</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0C08</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_CI_AP_CI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7502</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_BBS_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0C09</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_BBS_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0C0A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_Channel_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0C0B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_Channel_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0C0C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_RxThrput_RxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7602</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_RxPhysicalRate_RxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7702</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_RxRetrans_RxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7802</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_TxThrput_TxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7902</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_TxPhysicalRate_TxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7A02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_12_TxRetrans_TxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7B02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">AP_Channel_13</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_AP_Channel</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0D01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_AP_Freq</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0D02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_AP_RSSI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0D03</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_SFI_AP_SFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7C01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_NFI_AP_NFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7D01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_CI_AP_CI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7E01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_RxThrput_RxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7F01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_RxPhysicalRate_RxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8001</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_RxRetrans_RxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8101</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_TxThrput_TxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8201</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_TxPhysicalRate_TxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8301</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_TxRetrans_TxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8401</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_AP_RSSI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0D04</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_SFI_AP_SFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7C02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_NFI_AP_NFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7D02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_AP_NFI_Channel_1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0D05</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_AP_NFI_Channel_2</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0D06</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_AP_NFI_Channel_3</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0D07</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_AP_NFI_Channel_4</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0D08</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_CI_AP_CI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7E02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_BBS_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0D09</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_BBS_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0D0A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_Channel_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0D0B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_Channel_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0D0C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_RxThrput_RxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F7F02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_RxPhysicalRate_RxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8002</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_RxRetrans_RxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8102</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_TxThrput_TxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8202</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_TxPhysicalRate_TxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8302</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_13_TxRetrans_TxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8402</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">AP_Channel_14</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_AP_Channel</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0E01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_AP_Freq</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0E02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_AP_RSSI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0E03</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_SFI_AP_SFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8501</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_NFI_AP_NFI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8601</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_CI_AP_CI_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8701</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_RxThrput_RxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8801</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_RxPhysicalRate_RxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8901</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_RxRetrans_RxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8A01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_TxThrput_TxThrput_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8B01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_TxPhysicalRate_TxPhysicalRate_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8C01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_TxRetrans_TxRetrans_SampleNum</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8D01</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_AP_RSSI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0E04</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_SFI_AP_SFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8502</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_NFI_AP_NFI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8602</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_AP_NFI_Channel_1</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0E05</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_AP_NFI_Channel_2</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0E06</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_AP_NFI_Channel_3</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0E07</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_AP_NFI_Channel_4</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0E08</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_CI_AP_CI_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8702</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_BBS_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0E09</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_BBS_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0E0A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_Channel_Active_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0E0B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_Channel_Total_Users</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F0E0C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_RxThrput_RxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8802</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_RxPhysicalRate_RxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8902</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_RxRetrans_RxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8A02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_TxThrput_TxThrput_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8B02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_TxPhysicalRate_TxPhysicalRate_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8C02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">WLAN_AP_Channel_14_TxRetrans_TxRetrans_MeanValue</Item>
                    <Item typeName="String" key="FDesc" />
                    <Item typeName="String" key="FName">Wl_601F8D02</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">parameters Summary</Item>
            <Item typeName="String" key="FDesc">14个Channel指标统计到一起</Item>
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_AP_RSSI_SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F0F03</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_SFI_AP_SFI_SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F8E01</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_NFI_AP_NFI_SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F8F01</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_CI_AP_CI_SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F9001</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_RxThrput_RxThrput_SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F9101</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_RxPhysicalRate_RxPhysicalRate_SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F9201</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_RxRetrans_RxRetrans_SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F9301</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_TxThrput_TxThrput_SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F9401</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_TxPhysicalRate_TxPhysicalRate_SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F9501</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_TxRetrans_TxRetrans_SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F9601</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_AP_RSSI_MeanValue</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F0F04</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_SFI_AP_SFI_MeanValue</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F8E02</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_NFI_AP_NFI_MeanValue</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F8F02</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_CI_AP_CI_MeanValue</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F9002</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_BBS_Active_Users_SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F0F0F</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_BBS_Active_Users_MeanValue</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F0F10</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_BBS_Active_Users</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F0F09</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_BBS_Total_Users</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F0F0A</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_Channel_Active_Users</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F0F0B</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_Channel_Active_Users_SampleNum</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F0F0D</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_Channel_Active_Users_MeanValue</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F0F0E</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_Channel_Total_Users</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F0F0C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_RxThrput_RxThrput_MeanValue</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F9102</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_RxPhysicalRate_RxPhysicalRate_MeanValue</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F9202</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_RxRetrans_RxRetrans_MeanValue</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F9302</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_TxThrput_TxThrput_MeanValue</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F9402</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_TxPhysicalRate_TxPhysicalRate_MeanValue</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F9502</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">WLAN_AP_Channel_Summary_TxRetrans_TxRetrans_MeanValue</Item>
                <Item typeName="String" key="FDesc" />
                <Item typeName="String" key="FName">Wl_601F9602</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>