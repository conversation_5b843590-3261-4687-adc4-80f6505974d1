﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTTestPointBlock.Competition
{
    public partial class ResultForm : MinCloseForm
    {
        public ResultForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = true;
            initFixCol();
        }

        private void initFixCol()
        {
            setTPData();
            setOverLap();
            colTestPointCnt1.AspectGetter += delegate (object row)
            {
                if (row is TestPointBlock)
                {
                    return (row as TestPointBlock).TestPointCount;
                }
                return null;
            };
            colTestPointCnt2.AspectGetter += delegate (object row)
            {
                if (row is TestPointBlock)
                {
                    return (row as TestPointBlock).TestPointCount;
                }
                return null;
            };
            colTestPointCnt3.AspectGetter += delegate (object row)
            {
                if (row is TestPointBlock)
                {
                    return (row as TestPointBlock).TestPointCount;
                }
                return null;
            };

            colGrid1.AspectGetter += delegate (object row)
            {
                if (row is TestPointBlock)
                {
                    return (row as TestPointBlock).Grid;
                }
                return null;
            };
            colGrid2.AspectGetter += delegate (object row)
            {
                if (row is TestPointBlock)
                {
                    return (row as TestPointBlock).Grid;
                }
                return null;
            };
            colGrid3.AspectGetter += delegate (object row)
            {
                if (row is TestPointBlock)
                {
                    return (row as TestPointBlock).Grid;
                }
                return null;
            };

            lvHost.CanExpandGetter += delegate (object row)
            {
                return row is TestPointBlock;
            };
            lvGuest.CanExpandGetter += delegate (object row)
            {
                return row is TestPointBlock;
            };
            lvOverlap.CanExpandGetter += delegate (object row)
            {
                return row is TestPointBlock;
            };
            lvHost.ChildrenGetter += delegate (object row)
            {
                if (row is TestPointBlock)
                {
                    return (row as TestPointBlock).TestPoints;
                }
                return null;
            };
            lvGuest.ChildrenGetter += delegate (object row)
            {
                if (row is TestPointBlock)
                {
                    return (row as TestPointBlock).TestPoints;
                }
                return null;
            };
            lvOverlap.ChildrenGetter += delegate (object row)
            {
                if (row is TestPointBlock)
                {
                    return (row as TestPointBlock).TestPoints;
                }
                return null;
            };
        }

        private void setOverLap()
        {
            colOverlap1.AspectGetter += delegate (object row)
            {
                if (row is TestPointBlock)
                {
                    return (row as TestPointBlock).Overlap ? "是" : "否";
                }
                return null;
            };
            colOverlap2.AspectGetter += delegate (object row)
            {
                if (row is TestPointBlock)
                {
                    return (row as TestPointBlock).Overlap ? "是" : "否";
                }
                return null;
            };
            colSN1.AspectGetter += delegate (object row)
            {
                if (row is TestPointBlock)
                {
                    TestPointBlock blk = row as TestPointBlock;
                    IList list = lvHost.Roots as IList;
                    if (list != null)
                    {
                        return list.IndexOf(blk) + 1;
                    }
                }
                return null;
            };
            colSN2.AspectGetter += delegate (object row)
            {
                if (row is TestPointBlock)
                {
                    TestPointBlock blk = row as TestPointBlock;
                    IList list = lvGuest.Roots as IList;
                    if (list != null)
                    {
                        return list.IndexOf(blk) + 1;
                    }
                }
                return null;
            };
            colSN3.AspectGetter += delegate (object row)
            {
                if (row is TestPointBlock)
                {
                    TestPointBlock blk = row as TestPointBlock;
                    IList list = lvOverlap.Roots as IList;
                    if (list != null)
                    {
                        return list.IndexOf(blk) + 1;
                    }
                }
                return null;
            };
        }

        private void setTPData()
        {
            colFile1.AspectGetter += delegate (object row)
            {
                if (row is TestPoint)
                {
                    return (row as TestPoint).FileName;
                }
                return null;
            };
            colFile2.AspectGetter += delegate (object row)
            {
                if (row is TestPoint)
                {
                    return (row as TestPoint).FileName;
                }
                return null;
            };
            colFile3.AspectGetter += delegate (object row)
            {
                if (row is TestPoint)
                {
                    return (row as TestPoint).FileName;
                }
                return null;
            };
            colLat1.AspectGetter += delegate (object row)
            {
                if (row is TestPoint)
                {
                    return (row as TestPoint).Latitude;
                }
                return null;
            };
            colLat2.AspectGetter += delegate (object row)
            {
                if (row is TestPoint)
                {
                    return (row as TestPoint).Latitude;
                }
                return null;
            };
            colLat3.AspectGetter += delegate (object row)
            {
                if (row is TestPoint)
                {
                    return (row as TestPoint).Latitude;
                }
                return null;
            };
            colLng1.AspectGetter += delegate (object row)
            {
                if (row is TestPoint)
                {
                    return (row as TestPoint).Longitude;
                }
                return null;
            };
            colLng2.AspectGetter += delegate (object row)
            {
                if (row is TestPoint)
                {
                    return (row as TestPoint).Longitude;
                }
                return null;
            };
            colLng3.AspectGetter += delegate (object row)
            {
                if (row is TestPoint)
                {
                    return (row as TestPoint).Longitude;
                }
                return null;
            };
            colTime1.AspectGetter += delegate (object row)
            {
                if (row is TestPoint)
                {
                    return (row as TestPoint).DateTime;
                }
                return null;
            };
            colTime2.AspectGetter += delegate (object row)
            {
                if (row is TestPoint)
                {
                    return (row as TestPoint).DateTime;
                }
                return null;
            };
            colTime3.AspectGetter += delegate (object row)
            {
                if (row is TestPoint)
                {
                    return (row as TestPoint).DateTime;
                }
                return null;
            };
        }

        private void addCol2ListView(TreeListView lv)
        {
            List<TPBlockDisplayColumn> displayCols = new List<TPBlockDisplayColumn>();
            addDisplayCols(lv, displayCols);
            foreach (TPBlockDisplayColumn colInfo in displayCols)
            {
                OLVColumn col = new OLVColumn();
                col.Tag = colInfo;
                col.Text = colInfo.ToString();
                lv.AllColumns.Add(col);
                lv.Columns.AddRange(new ColumnHeader[] { col });
                col.AspectGetter += delegate (object row)
                {
                    TPBlockDisplayColumn disColInfo = col.Tag as TPBlockDisplayColumn;
                    if (row is TestPointBlock)
                    {
                        TestPointBlock tp = row as TestPointBlock;
                        double d = tp[disColInfo];
                        if (double.IsNaN(d))
                        {
                            return "-";
                        }
                        else
                        {
                            return d;
                        }
                    }
                    else if (row is TestPoint)
                    {
                        TestPoint tp = row as TestPoint;
                        return tp[disColInfo.DisplayParam.ParamInfo.Name, disColInfo.ParamArrayIndex];
                    }
                    else
                    {
                        return null;
                    }
                };
            }
            lv.RebuildColumns();
        }

        private void addDisplayCols(TreeListView lv, List<TPBlockDisplayColumn> displayCols)
        {
            if (lv == lvHost)
            {
                displayCols.AddRange(report.HostColumns);
            }
            else if (lv == lvGuest)
            {
                displayCols.AddRange(report.GuestColumns);
            }
            else if (lv == lvOverlap)
            {
                displayCols.AddRange(report.HostColumns);
                displayCols.AddRange(report.GuestColumns);
            }
        }

        private List<TestPointBlock> hostBlocks = null;
        private List<TestPointBlock> guestBlocks = null;
        private List<TestPointBlock> hostOnlyBlocks = null;
        private List<TestPointBlock> hostOverlapBlocks = null;
        private List<TestPointBlock> guestOnlyBlocks = null;
        private List<TestPointBlock> guestOverlapBlocks = null;
        private List<TestPointBlock> overlapBlocks = null;
        private double radius;
        private TestPointBlockReport report;
        TestPointBlockCompetitionLayer layer = null;
        public void FillData(TestPointBlockReport report, List<TestPointBlock> hostBlocks, List<TestPointBlock> guestBlocks, List<TestPointBlock> overlapBlocks)
        {
            lvOverlap.ClearObjects();
            lvHost.ClearObjects();
            lvGuest.ClearObjects();
            this.report = report;
            this.hostBlocks = hostBlocks;
            this.guestBlocks = guestBlocks;
            this.overlapBlocks = overlapBlocks;
            this.radius = report.BlockRadius;
            hostOnlyBlocks = new List<TestPointBlock>();
            hostOverlapBlocks = new List<TestPointBlock>();
            foreach (TestPointBlock blk in hostBlocks)
            {
                if (blk.Overlap)
                {
                    hostOverlapBlocks.Add(blk);
                }
                else
                {
                    hostOnlyBlocks.Add(blk);
                }
            }
            guestOnlyBlocks = new List<TestPointBlock>();
            guestOverlapBlocks = new List<TestPointBlock>();
            foreach (TestPointBlock blk in guestBlocks)
            {
                if (blk.Overlap)
                {
                    guestOverlapBlocks.Add(blk);
                }
                else
                {
                    guestOnlyBlocks.Add(blk);
                }
            }
            makesureLayerVisible();
            addCol2ListView(lvHost);
            addCol2ListView(lvGuest);
            addCol2ListView(lvOverlap);
            fillListView();
        }

        private void makesureLayerVisible()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                MasterCom.MTGis.CustomDrawLayer cLayer = mf.GetCustomLayer(typeof(TestPointBlockCompetitionLayer));
                if (cLayer == null)
                {
                    layer = new TestPointBlockCompetitionLayer(mf.GetMapOperation(), "采样点汇聚竞比图层");
                    mf.AddTempCustomLayer(layer);
                }
                else
                {
                    layer = cLayer as TestPointBlockCompetitionLayer;
                }
            }
            layer.BlockRadius = radius;
        }


        private void fillListView()
        {
            lvOverlap.ClearObjects();
            if (overlapBlocks != null)
            {
                lvOverlap.SetObjects(overlapBlocks);
                lvOverlap.Tag = overlapBlocks;
                lvOverlap.ExpandAll();
            }
            layer.OverlapBlocks = overlapBlocks;
            lvHost.ClearObjects();
            lvGuest.ClearObjects();
            if (rbDiff.Checked)
            {
                lvHost.SetObjects(hostOnlyBlocks);
                lvHost.Tag = hostOnlyBlocks;
                lvHost.ExpandAll();
                layer.HostBlocks = hostOnlyBlocks;
                lvGuest.SetObjects(guestOnlyBlocks);
                lvGuest.Tag = guestOnlyBlocks;
                lvGuest.ExpandAll();
                layer.GuestBlocks = guestOnlyBlocks;
            }
            else if (rbRepeat.Checked)
            {
                lvHost.SetObjects(hostOverlapBlocks);
                lvHost.Tag = hostOverlapBlocks;
                lvHost.ExpandAll();
                layer.HostBlocks = hostOverlapBlocks;
                lvGuest.SetObjects(guestOverlapBlocks);
                lvGuest.Tag = guestOverlapBlocks;
                lvGuest.ExpandAll();
                layer.GuestBlocks = guestOverlapBlocks;
            }
            else
            {
                lvHost.SetObjects(hostBlocks);
                lvHost.Tag = hostBlocks;
                lvHost.ExpandAll();
                layer.HostBlocks = hostBlocks;
                lvGuest.SetObjects(guestBlocks);
                lvGuest.Tag = guestBlocks;
                lvGuest.ExpandAll();
                layer.GuestBlocks = guestBlocks;
            }
            layer.Invalidate();
        }

        private void rbAll_CheckedChanged(object sender, EventArgs e)
        {
            fillListView();
        }

        private void rbDiff_CheckedChanged(object sender, EventArgs e)
        {
            fillListView();
        }

        private void rbRepeat_CheckedChanged(object sender, EventArgs e)
        {
            fillListView();
        }

        private void chkHost_CheckedChanged(object sender, EventArgs e)
        {
            colorHost.Enabled = chkHost.Checked;
            if (layer != null)
            {
                layer.DrawHost = chkHost.Checked;
                layer.Invalidate();
            }
        }

        private void chkGuest_CheckedChanged(object sender, EventArgs e)
        {
            colorGuest.Enabled = chkGuest.Checked;
            if (layer != null)
            {
                layer.DrawGuest = chkGuest.Checked;
                layer.Invalidate();
            }
        }

        private void chkOverlap_CheckedChanged(object sender, EventArgs e)
        {
            colorOverlap.Enabled = chkOverlap.Checked;
            if (layer != null)
            {
                layer.DrawOverlap = chkOverlap.Checked;
                layer.Invalidate();
            }
        }

        private void lvHost_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            TreeListView lv = sender as TreeListView;
            if (lv.SelectedObject is TestPointBlock)
            {
                layer.CurSelBlock = (lv.SelectedObject as TestPointBlock);
                layer.Invalidate();
            }
            else if (lv.SelectedObject is TestPoint)
            {
                TestPoint tp = lv.SelectedObject as TestPoint;
                MainModel.MainForm.GetMapForm().GoToView(tp.Longitude, tp.Latitude, 6000);
            }
        }

        private void miExpandAll1_Click(object sender, EventArgs e)
        {
            lvHost.ExpandAll();
        }

        private void miCollapseAll1_Click(object sender, EventArgs e)
        {
            lvHost.CollapseAll();
        }

        private void miExport1_Click(object sender, EventArgs e)
        {
            lvHost.ExpandAll();
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(lvHost);
        }

        private void miExpandAll2_Click(object sender, EventArgs e)
        {
            lvGuest.ExpandAll();
        }

        private void miCollapseAll2_Click(object sender, EventArgs e)
        {
            lvGuest.CollapseAll();
        }

        private void miExport2_Click(object sender, EventArgs e)
        {
            lvGuest.ExpandAll();
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(lvGuest);
        }

        private void miExpandAllOverlap_Click(object sender, EventArgs e)
        {
            lvOverlap.ExpandAll();
        }

        private void miCollapseAllOverlap_Click(object sender, EventArgs e)
        {
            lvOverlap.CollapseAll();
        }

        private void miExportOverlap_Click(object sender, EventArgs e)
        {
            lvOverlap.ExpandAll();
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(lvOverlap);
        }

        private void ExportShp_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.Filter = MasterCom.Util.FilterHelper.Shp;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            layer.Export2Shp(dlg.FileName);
        }
    }
}
