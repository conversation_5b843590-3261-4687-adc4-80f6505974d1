﻿using MasterCom.MControls;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public class RoadGridArchiveLayer : LayerBase
    {
        public enum RenderingIndex
        {
            EventNum,
            CellNum
        }

        public RoadGridArchiveLayer()
            : base("道路栅格档案库图层")
        {
        }

        /// <summary>
        /// 本次选择的栅格
        /// </summary>
        public ZTRoadGridArchiveRes CurSelectedGrid { get; set; }

        /// <summary>
        /// 路段合集
        /// </summary>
        public static List<ZTRoadGridArchiveRes> RoadGridInfos { get; set; } = new List<ZTRoadGridArchiveRes>();

        /// <summary>
        /// 已绘制的栅格路段合集 - 用于绘制路段名
        /// </summary>
        List<ZTRoadGridArchiveRes> drawedGridList { get; set; } = new List<ZTRoadGridArchiveRes>();

        /// <summary>
        /// 已绘制的路段名 - 用于判断label是否重叠
        /// </summary>
        List<Rectangle> drawedRoadLabels { get; set; } = new List<Rectangle>();

        /// <summary>
        /// 已选择栅格的边框画笔
        /// </summary>
        private readonly Pen penNotSelected = new Pen(Color.White, 1);

        /// <summary>
        /// 已选择栅格的边框画笔
        /// </summary>
        private readonly Pen penSelected = new Pen(Color.Red, 3);

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible || RoadGridInfos.Count <= 0)
            {
                return;
            }

            DbRect viewRect;
            GisAdapter.FromDisplay(updateRect, out viewRect);

            drawRoadGrid(viewRect, graphics, RoadGridInfos);

            drawRoadGridLabel(graphics, drawedGridList);

            drawSelectedRoadGrid(graphics);
        }



        #region grid
        private void drawRoadGrid(DbRect viewRect, Graphics graphics, List<ZTRoadGridArchiveRes> roadGridList)
        {
            drawedGridList.Clear();
            foreach (ZTRoadGridArchiveRes grid in roadGridList)
            {
                if (grid.Within(viewRect))
                {
                    drawRoadPart(graphics, grid);
                }
            }
        }

        private void drawRoadPart(Graphics graphics, ZTRoadGridArchiveRes roadGrid)
        {
            if (roadGrid.DbPointList != null)
            {
                Color color = GetColor(roadGrid);
                if (color != Color.Empty)
                {
                    DbPoint[] roadDps = roadGrid.DbPointList.ToArray();
                    PointF[] points;
                    gisAdapter.ToDisplay(roadDps, out points);
                    Brush brush = new SolidBrush(color);
                    //Brush brush = new SolidBrush(Color.FromArgb(Alpha, color))
                    graphics.FillPolygon(brush, points);
                    graphics.DrawPolygon(penNotSelected, points);
                    drawedGridList.Add(roadGrid);
                }
            }
        }

        public Color GetColor(ZTRoadGridArchiveRes grid)
        {
            float value = -999;
            switch (renderingType)
            {
                case RenderingIndex.EventNum:
                    value = grid.CurEventRoadInfo.TotalEventNum;
                    break;
                case RenderingIndex.CellNum:
                    value = grid.CellCount;
                    break;
            }

            Color? color = RoadGridArchiveColorRange.Instance.GetColor(value);
            if (color == null)
            {
                return Color.Empty;
            }
            else
            {
                return (Color)color;
            }
        }
        #endregion

        #region label
        private void drawRoadGridLabel(Graphics graphics, List<ZTRoadGridArchiveRes> roadGridList)
        {
            drawedRoadLabels.Clear();
            foreach (ZTRoadGridArchiveRes grid in roadGridList)
            {
                drawRoadNameLabel(graphics, grid);
            }
        }

        private void drawRoadNameLabel(Graphics graphics, ZTRoadGridArchiveRes roadGrid)
        {
            //画路段名
            Font fontStyle = new Font(new FontFamily("宋体"), 8, FontStyle.Regular);
            DbPoint dPoint = new DbPoint(roadGrid.TLLongitude, roadGrid.CenterLatitude);
            PointF point;
            gisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            SizeF size = graphics.MeasureString(roadGrid.AreaName, fontStyle);
            size.Height *= 0.8f;
            Rectangle rectangle = new Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
            //判断存在重叠则不绘制
            bool needDraw = true;
            foreach (Rectangle rectangleTemp in drawedRoadLabels)
            {
                if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                    && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                {
                    needDraw = false;
                    break;
                }
            }
            if (needDraw)
            {
                //如果不设置背景,或背景透明会导致文字显示异常,原因未知
                graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(roadGrid.AreaName, fontStyle, Brushes.Red, 3, -size.Height / 2);
                drawedRoadLabels.Add(rectangle);
            }
            graphics.ResetTransform();
        }
        #endregion

        #region select
        /// <summary>
        /// 绘制选中的栅格
        /// </summary>
        /// <param name="graphics"></param>
        private void drawSelectedRoadGrid(Graphics graphics)
        {
            if (CurSelectedGrid == null)
            {
                return;
            }

            //重绘栅格,置顶
            drawRoadPart(graphics, CurSelectedGrid);

            //绘制选择框
            if (CurSelectedGrid.DbPointList != null)
            {
                DbPoint[] roadDps = CurSelectedGrid.DbPointList.ToArray();
                PointF[] points;
                gisAdapter.ToDisplay(roadDps, out points);
                graphics.DrawPolygon(penSelected, points);
            }

            //重绘标签,置顶
            drawedRoadLabels.Clear();
            drawRoadNameLabel(graphics, CurSelectedGrid);
        }

        public event EventHandler SelectedGridChanged;

        public override void mapForm_MapFeatureSelecting(object sender, EventArgs e)
        {
            Select(((MapForm.MapEventArgs)e).MapOp2);
        }

        public void Select(MapOperation2 mop2)
        {
            if (!IsVisible || RoadGridInfos.Count <= 0)
            {
                return;
            }
            CurSelectedGrid = null;

            //循环所有栅格,如果点击的坐标在某个栅格的范围内则记录为所选点
            getSelectedGrid(mop2);

            if (SelectedGridChanged != null)
            {
                SelectedGridChanged(this, EventArgs.Empty);
            }
        }

        private void getSelectedGrid(MapOperation2 mop2)
        {
            foreach (ZTRoadGridArchiveRes grid in RoadGridInfos)
            {
                DbRect dRect = new DbRect(grid.TLLongitude, grid.TLLatitude, grid.BRLongitude, grid.BRLatitude);
                if (mop2.CheckCenterInDRect(dRect))
                {
                    CurSelectedGrid = grid;
                    break;
                }
            }
        }
        #endregion

        RenderingIndex renderingType = RenderingIndex.EventNum;
        public void InitColor(RenderingIndex curRangeType)
        {
            renderingType = curRangeType;
        }
    }

    public abstract class RoadGridColorRange
    {
        public float Minimum { get; set; }
        public float Maximum { get; set; }
        public List<ColorRange> ColorRanges { get; set; }
        public string RangeName { get; private set; }
        public string RangeTypeName { get; private set; }
        public RangeNameType RangeType { get; private set; }

        protected RoadGridColorRange(RangeNameType rangeType)
        {
            Minimum = -1000;
            Maximum = 1000;
            ColorRanges = new List<ColorRange>();
            RangeType = rangeType;
            RangeName = EnumDescriptionAttribute.GetText(rangeType);
            RangeTypeName = RangeType.ToString();
        }

        public virtual void AddInitData()
        {

        }
    }

    public class RoadGridEventColorRange : RoadGridColorRange
    {
        public RoadGridEventColorRange(RangeNameType rangeType) : base(rangeType)
        {
        }

        public override void AddInitData()
        {
            if (ColorRanges.Count == 0)
            {
                ColorRanges.Add(new ColorRange(Minimum, 1, Color.Khaki, "（-∞,1)"));
                ColorRanges.Add(new ColorRange(1, 4, Color.Blue, "[1,4)"));
                ColorRanges.Add(new ColorRange(4, 7, Color.Lime, "[4,7)"));
                ColorRanges.Add(new ColorRange(7, 10, Color.Aqua, "[7,10)"));
                ColorRanges.Add(new ColorRange(10, 13, Color.Yellow, "[10,13)"));
                ColorRanges.Add(new ColorRange(13, 16, Color.MistyRose, "[13,16)"));
                ColorRanges.Add(new ColorRange(16, 19, Color.Orange, "[16,19)"));
                ColorRanges.Add(new ColorRange(19, Maximum, Color.Red, "[19,+∞)"));
            }
        }
    }

    public class RoadGridCellColorRange : RoadGridColorRange
    {
        public RoadGridCellColorRange(RangeNameType rangeType) : base(rangeType)
        {
        }

        public override void AddInitData()
        {
            if (ColorRanges.Count == 0)
            {
                ColorRanges.Add(new ColorRange(Minimum, 5, Color.Khaki, "（-∞,5)"));
                ColorRanges.Add(new ColorRange(5, 10, Color.Blue, "[5,10)"));
                ColorRanges.Add(new ColorRange(10, 15, Color.Lime, "[10,15)"));
                ColorRanges.Add(new ColorRange(15, 20, Color.Aqua, "[15,20)"));
                ColorRanges.Add(new ColorRange(20, 25, Color.Yellow, "[20,25)"));
                ColorRanges.Add(new ColorRange(25, 30, Color.MistyRose, "[25,30)"));
                ColorRanges.Add(new ColorRange(30, 35, Color.Orange, "[30,35)"));
                ColorRanges.Add(new ColorRange(35, Maximum, Color.Red, "[35,+∞)"));
            }
        }
    }

    public enum RangeNameType
    {
        [EnumDescription("异常事件数")]
        EventColorRange,
        [EnumDescription("占用小区数")]
        CellColorRange
    }

    public class RoadGridArchiveColorRange
    {
        readonly string ConfigPath = System.Windows.Forms.Application.StartupPath + @"\config\RoadGridArchive.xml";

        public List<ColorRange> CurColorRanges { get; private set; }
        public string CurRangeName { get; private set; }
        public RangeNameType CurRangeType { get; private set; }

        public RoadGridEventColorRange EventColorRange { get; set; }
        public RoadGridCellColorRange CellColorRange { get; set; }

        private static RoadGridArchiveColorRange instance;
        public static RoadGridArchiveColorRange Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new RoadGridArchiveColorRange();
                }
                return instance;
            }
        }

        public RoadGridArchiveColorRange()
        {
            CurColorRanges = new List<ColorRange>();

            EventColorRange = new RoadGridEventColorRange(RangeNameType.EventColorRange);
            CellColorRange = new RoadGridCellColorRange(RangeNameType.CellColorRange);

            XmlConfigFile configFile = new MyXmlConfigFile(ConfigPath);
            if (configFile.Load())
            {
                loadConfig(configFile);
            }
            else
            {
                addInitData();
                SaveConfig(true);
            }

            //默认选择异常事件渲染
            CurColorRanges = EventColorRange.ColorRanges;
            CurRangeType = EventColorRange.RangeType;
        }

        public void ChangeRange(RoadGridColorRange colorRange)
        {
            CurColorRanges = colorRange.ColorRanges;
            CurRangeType = colorRange.RangeType;
            CurRangeName = colorRange.RangeName;
        }

        //public void ChangeRange(RangeNameType type)
        //{
        //    switch (type)
        //    {
        //        case RangeNameType.EventColorRange:
        //            CurColorRanges = EventColorRange.ColorRanges;
        //            CurRangeType = type;
        //            CurRangeName = EventColorRange.RangeName;
        //            break;
        //        case RangeNameType.CellColorRange:
        //            CurColorRanges = CellColorRange.ColorRanges;
        //            CurRangeType = type;
        //            CurRangeName = CellColorRange.RangeName;
        //            break;
        //        default:
        //            break;
        //    }
        //}

        private void addInitData()
        {
            EventColorRange.AddInitData();
            CellColorRange.AddInitData();
        }

        public virtual Color? GetColor(float value)
        {
            int idx = GetIndex(value);
            if (idx < 0)
            {
                return Color.Black;
            }
            else
            {
                if (CurColorRanges[idx].visible)
                {
                    return CurColorRanges[idx].color;
                }
                else
                {
                    return null;
                }
            }
        }

        public virtual int GetIndex(float value)
        {
            if (value < CurColorRanges[0].maxValue)
            {
                return 0;
            }
            if (value >= CurColorRanges[CurColorRanges.Count - 1].minValue)
            {
                return CurColorRanges.Count - 1;
            }
            for (int i = 1; i < CurColorRanges.Count - 1; ++i)
            {
                if (value >= CurColorRanges[i].minValue && value < CurColorRanges[i].maxValue)
                {
                    return i;
                }
            }
            return -1;
        }

        #region config
        private void loadConfig(XmlConfigFile configFile)
        {
            loadColorRange(configFile, EventColorRange.RangeTypeName, EventColorRange.ColorRanges);
            loadColorRange(configFile, CellColorRange.RangeTypeName, CellColorRange.ColorRanges);
        }

        private void loadColorRange(XmlConfigFile configFile, string type, List<ColorRange> colorRanges)
        {
            XmlElement cfgCoverageColorRange = configFile.GetConfig(type);
            if (cfgCoverageColorRange != null)
            {
                foreach (XmlNode node in cfgCoverageColorRange.ChildNodes)
                {
                    if (node.Attributes.Count == 0)
                    {
                        continue;
                    }
                    string des = node.Attributes["name"].InnerText;
                    string text = node.InnerText;
                    string[] s = text.Split(',');
                    float min, max;
                    int color;
                    if (s.Length == 3 && float.TryParse(s[0], out min) && float.TryParse(s[1], out max)
                        && int.TryParse(s[2], out color))
                    {
                        colorRanges.Add(new ColorRange(min, max, Color.FromArgb(color), des));
                    }
                }
            }
        }

        public void SaveConfig(bool isInit)
        {
            if (isInit)
            {
                XmlConfigFile configFile = new XmlConfigFile();
                saveInitColorRange(EventColorRange.RangeTypeName, EventColorRange.ColorRanges, configFile);
                saveInitColorRange(CellColorRange.RangeTypeName, CellColorRange.ColorRanges, configFile);
                configFile.Save(ConfigPath);
            }
            else
            {
                XmlConfigFile configFile = new XmlConfigFile(ConfigPath);
                saveColorRange(EventColorRange.RangeTypeName, EventColorRange.ColorRanges, configFile);
                saveColorRange(CellColorRange.RangeTypeName, CellColorRange.ColorRanges, configFile);
                configFile.Save(ConfigPath);
            }
        }

        private void saveInitColorRange(string type, List<ColorRange> colorRanges, XmlConfigFile configFile)
        {
            XmlElement cfgColorRange = configFile.AddConfig(type);
            foreach (ColorRange range in colorRanges)
            {
                configFile.AddItem(cfgColorRange, range.desInfo,
                    string.Format("{0},{1},{2}", range.minValue, range.maxValue, range.color.ToArgb()));
            }
        }

        private void saveColorRange(string type, List<ColorRange> colorRanges, XmlConfigFile configFile)
        {
            XmlElement cfgColorRange = configFile.GetConfig(type);
            if (cfgColorRange != null)
            {
                cfgColorRange.RemoveAll();
                cfgColorRange.SetAttribute("name", type);
            }
            else
            {
                cfgColorRange = configFile.AddConfig(type);
            }

            foreach (ColorRange range in colorRanges)
            {
                configFile.AddItem(cfgColorRange, range.desInfo,
                    string.Format("{0},{1},{2}", range.minValue, range.maxValue, range.color.ToArgb()));
            }
        }
        #endregion
    }
}
