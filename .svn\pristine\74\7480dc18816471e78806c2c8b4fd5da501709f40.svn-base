﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class DiyQueryFddDBSetting : DIYSQLBase
    {
        public Dictionary<string, FddDatabaseSetting> DatabaseSetting
        {
            get;
            private set;
        } = new Dictionary<string, FddDatabaseSetting>();

        public DiyQueryFddDBSetting()
            : base()
        {
            MainDB = true;
        }

        protected static readonly object lockObj = new object();
        private static DiyQueryFddDBSetting instance = null;
        public static DiyQueryFddDBSetting GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new DiyQueryFddDBSetting();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get
            {
                return "查询FDD单验数据库配置";
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.Append("select tableType,serverIp,dbName,tableNameHead from tb_xinjiang_fusionDbSetting");
            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[4];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            DatabaseSetting = new Dictionary<string, FddDatabaseSetting>();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    FddDatabaseSetting setting = new FddDatabaseSetting();
                    setting.FillData(package);
                    DatabaseSetting.Add(setting.TableType, setting);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error(Name + "Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class FddDatabaseSetting
    {
        public string TableType { get; set; }
        public string ServerIp { get; set; }
        public string DbName { get; set; }
        public string TableNameHead { get; set; }

        public void FillData(Package package)
        {
            TableType = package.Content.GetParamString();
            ServerIp = package.Content.GetParamString();
            DbName = package.Content.GetParamString();
            TableNameHead = package.Content.GetParamString();
        }
    }
}
