﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ScanOverlapPoint
    {
        public ScanOverlapPoint(TestPoint tp, ZTDIYQueryScanOverlap.NearestMainCellInfo cellInfo)
        {
            Point = tp;
            CellNearest = cellInfo.NearestCell;
            distanceNearestCell = cellInfo.DisMin;
            rxLevN = cellInfo.NearestCellRxLev;
            CellMaxRxLev = cellInfo.MainCell;
            distanceMaxRxLevCell = cellInfo.Dis2MainCell;
            rxLevMax = cellInfo.MainRxLev;
        }
        public ScanOverlapPoint(TestPoint tp, TDCell nearestCell, double nearestDis, float nearestRxLev, TDCell maxRxLevCell, double maxRxLevDis, float maxRxLev)
        {
            Point = tp;
            cellNearest_TD = nearestCell;
            distanceNearestCell = nearestDis;
            rxLevN = nearestRxLev;
            cellMax_TD = maxRxLevCell;
            distanceMaxRxLevCell = maxRxLevDis;
            rxLevMax = maxRxLev;
        }
        public ScanOverlapPoint(TestPoint tp, WCell nearestCell, double nearestDis, float nearestRxLev, WCell maxRxLevCell, double maxRxLevDis, float maxRxLev)
        {
            Point = tp;
            cellNearest_W = nearestCell;
            distanceNearestCell = nearestDis;
            rxLevN = nearestRxLev;
            cellMax_W = maxRxLevCell;
            distanceMaxRxLevCell = maxRxLevDis;
            rxLevMax = maxRxLev;
        }

        public TestPoint Point { get; set; }
        public double Longitude
        {
            get { return Math.Round(Point.Longitude, 5); }
        }
        public double Latitude
        {
            get { return Math.Round(Point.Latitude, 5); }
        }
        public Cell CellNearest { get; set; }
        private readonly TDCell cellNearest_TD = null;
        public TDCell CellNearest_TD
        {
            get { return cellNearest_TD; }
        }
        private readonly WCell cellNearest_W = null;
        public WCell CellNearest_W
        {
            get { return cellNearest_W; }
        }
        public string CellNameNearest
        {
            get
            {
                if (CellNearest != null)
                {
                    return CellNearest.Name;
                }
                else if (cellNearest_TD != null)
                {
                    return cellNearest_TD.Name;
                }
                else if (cellNearest_W != null)
                {
                    return cellNearest_W.Name;
                }
                return string.Empty;
            }
        }

        public double CellNearestEndLng
        {
            get
            {
                if (CellNearest != null)
                {
                    return CellNearest.EndPointLongitude;
                }
                else if (cellNearest_TD != null)
                {
                    return cellNearest_TD.EndPointLongitude;
                }
                else if (cellNearest_W != null)
                {
                    return cellNearest_W.EndPointLongitude;
                }
                return -1;
            }
        }
        public double CellNearestEndLat
        {
            get
            {
                if (CellNearest != null)
                {
                    return CellNearest.EndPointLatitude;
                }
                else if (cellNearest_TD != null)
                {
                    return cellNearest_TD.EndPointLatitude;
                }
                else if (cellNearest_W != null)
                {
                    return cellNearest_W.EndPointLatitude;
                }
                return -1;
            }
        }
        public int CINearest
        {
            get
            {
                if (CellNearest != null)
                {
                    return CellNearest.CI;
                }
                else if (cellNearest_TD != null)
                {
                    return cellNearest_TD.CI;
                }
                else if (cellNearest_W != null)
                {
                    return cellNearest_W.CI;
                }
                return -1;
            }
        }
        private readonly double distanceNearestCell;
        public double DistanceNearest
        {
            get { return Math.Round(distanceNearestCell, 2); }
        }
        private readonly float rxLevN;
        public float RxLevNearest
        {
            get { return rxLevN; }
        }

        public Cell CellMaxRxLev { get; set; }
        private readonly TDCell cellMax_TD = null;
        public TDCell CellMax_TD
        {
            get { return cellMax_TD; }
        }
        private readonly WCell cellMax_W = null;
        public WCell CellMax_W
        {
            get { return cellMax_W; }
        }
        public double CellMaxEndLng
        {
            get {
                if (CellMaxRxLev != null)
                {
                    return CellMaxRxLev.EndPointLongitude;
                }
                else if (cellMax_TD != null)
                {
                    return cellMax_TD.EndPointLongitude;
                }
                else if (cellMax_W != null)
                {
                    return cellMax_W.EndPointLongitude;
                }
                return -1;
            }
        }
        public double CellMaxEndLat
        {
            get
            {
                if (CellMaxRxLev != null)
                {
                    return CellMaxRxLev.EndPointLatitude;
                }
                else if (cellMax_TD != null)
                {
                    return cellMax_TD.EndPointLatitude;
                }
                else if (cellMax_W != null)
                {
                    return cellMax_W.EndPointLatitude;
                }
                return -1;
            }
        }
        public string CellNameMaxRxLev
        {
            get
            {
                if (CellMaxRxLev != null)
                {
                    return CellMaxRxLev.Name;
                }
                else if (cellMax_TD != null)
                {
                    return cellMax_TD.Name;
                }
                else if (cellMax_W != null)
                {
                    return cellMax_W.Name;
                }
                return string.Empty;
            }
        }
        public int CIMaxRxLev
        {
            get
            {
                if (CellMaxRxLev != null)
                {
                    return CellMaxRxLev.CI;
                }
                else if (cellMax_TD != null)
                {
                    return cellMax_TD.CI;
                }
                else if (cellMax_W != null)
                {
                    return cellMax_W.CI;
                }
                return -1;
            }
        }
        private readonly double distanceMaxRxLevCell;
        public double DistanceMaxRxLev
        {
            get { return Math.Round(distanceMaxRxLevCell, 2); }
        }
        private readonly float rxLevMax;
        public float RxLevMax
        {
            get { return rxLevMax; }
        }
        public double DistanceDiff
        {
            get { return Math.Round(distanceMaxRxLevCell - distanceNearestCell, 2); }
        }
        public float RxLevDiff
        {
            get { return (float)Math.Round(RxLevMax - RxLevNearest, 2); }
        }

    }
}
