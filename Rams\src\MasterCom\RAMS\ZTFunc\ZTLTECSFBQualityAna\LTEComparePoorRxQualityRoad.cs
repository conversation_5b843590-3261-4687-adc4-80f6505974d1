﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
using MasterCom.Util;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Func;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    class LTEComparePoorRxQualityRoad : GSMComparePoorRxQualityRoad
    {
        private static LTEComparePoorRxQualityRoad instance = null;
        public new static GSMComparePoorRxQualityRoad GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LTEComparePoorRxQualityRoad();
                    }
                }
            }
            return instance;
        }
        protected LTEComparePoorRxQualityRoad()
            : base()
        { 
        }
        public override string Name
        {
            get { return "质差路段对比_LTE"; }
        }

        protected override int? get_RxQualSub(TestPoint tp)
        {
            return (int?)(byte?)tp["lte_gsm_DM_RxQualSub"]; 
        }

        protected override GSMComparePoorRxQualityRoadGrid getGridInstance(double gridSpan, DbRect regionGrdiBounds)
        {
            return new LTEComparePoorRxQualityRoadGrid(gridSpan, regionGrdiBounds);
        }
    }

    class LTEComparePoorRxQualityRoad_FDD : LTEComparePoorRxQualityRoad
    {
        private static LTEComparePoorRxQualityRoad_FDD instance = null;
        public static new LTEComparePoorRxQualityRoad_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LTEComparePoorRxQualityRoad_FDD();
                    }
                }
            }
            return instance;
        }
        protected LTEComparePoorRxQualityRoad_FDD()
            : base()
        {

        }
        public override string Name
        {
            get { return "质差路段对比_LTE_FDD"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26070, this.Name);
        }
        protected float? get_RxQualSubFdd(TestPoint tp)
        {
            return (float?)tp["lte_fdd_wcdma_TotalEc_Io"];
        }
        protected override void doWithFileOfPeriod(bool isPeriod1File, DTFileDataManager fileMng)
        {
            List<TestPoint> testPointList = fileMng.TestPoints;
            GSMComparePoorRxQualityRoadGrid info = null;
            TestPoint prePoint = null;//前一点
            try
            {
                for (int i = 0; i < testPointList.Count; i++)
                {
                    TestPoint testPoint = testPointList[i];
                    if (!isValidTestPoint(testPoint))
                    {
                        savePoorRxQualInfo(isPeriod1File, info);
                        info = null;
                        continue;
                    }
                    if (isPeriod1File)
                    {//生成时间段1的测试路线栅格
                        makeRoadGridPath(testPoint);
                    }
                    float? rxQual = this.get_RxQualSubFdd(testPoint);
                    if (!poorRxQualCondition.MatchPoorRxQuality((int?)rxQual))//先作指标的判断，后作距离的判断
                    {//不符合设置的质差条件 
                        savePoorRxQualInfo(isPeriod1File, info);
                        info = null;
                    }
                    else//指标符合质差条件，还需要进行距离条件判断
                    {
                        info = dealPoorRxQualDistance(isPeriod1File, testPointList, info, prePoint, i, testPoint);
                    }
                    prePoint = testPoint;
                }
            }
            catch
            {
                //忽略
            }
        }

        private GSMComparePoorRxQualityRoadGrid dealPoorRxQualDistance(bool isPeriod1File, List<TestPoint> testPointList, GSMComparePoorRxQualityRoadGrid info, TestPoint prePoint, int i, TestPoint testPoint)
        {
            if (info == null)//质差路段开始
            {
                info = this.getGridInstance(curGridSpan, curRegionGridBounds);
                info.SetGridInfo(testPoint, 0);
                if (i == testPointList.Count - 1)//最后一采样点
                {
                    savePoorRxQualInfo(isPeriod1File, info);
                }
            }
            else
            {
                double dis = MathFuncs.GetDistance(prePoint.Longitude, prePoint.Latitude, testPoint.Longitude, testPoint.Latitude);
                if (poorRxQualCondition.MatchTestPointDistance(dis))
                {//符合两采样点之间的距离门限

                    info.SetGridInfo(testPoint, dis);
                    if (i == testPointList.Count - 1)//最后一采样点
                    {
                        savePoorRxQualInfo(isPeriod1File, info);
                    }
                }
                else
                {//两采样点距离不符合，该点开始新的质差路段
                    savePoorRxQualInfo(isPeriod1File, info);
                    info = this.getGridInstance(curGridSpan, curRegionGridBounds);
                    info.SetGridInfo(testPoint, 0);
                }
            }

            return info;
        }

        protected override GSMComparePoorRxQualityRoadGrid getGridInstance(double gridSpan, DbRect regionGrdiBounds)
        {
            return new LTEComparePoorRxQualityRoadGrid_FDD(gridSpan, regionGrdiBounds);
        }
    }

    class LTEComparePoorRxQualityRoadGrid : GSMComparePoorRxQualityRoadGrid
    {
        public LTEComparePoorRxQualityRoadGrid(double gridSpan, DbRect regionGrdiBounds)
            :base(gridSpan, regionGrdiBounds)
        {
        }
        protected override void get_Param(TestPoint tp, out int? rxQual, out short? rxlev, out short? c2i)
        {
            rxQual = (int?)(byte?)tp["lte_gsm_DM_RxQualSub"];
            rxlev = (short?)tp["lte_gsm_DM_RxLevSub"];
            c2i = null;
        }

    }

    class LTEComparePoorRxQualityRoadGrid_FDD : LTEComparePoorRxQualityRoadGrid
    {
        public LTEComparePoorRxQualityRoadGrid_FDD(double gridSpan, DbRect regionGrdiBounds)
            : base(gridSpan, regionGrdiBounds)
        {

        }
        protected void get_ParamFdd(TestPoint tp, out float? rxQual, out float? rxlev, out short? c2i)
        {
            rxQual = (float?)tp["lte_fdd_wcdma_TotalEc_Io"];
            rxlev = (float?)tp["lte_fdd_wcdma_TotalRSCP"];
            c2i = null;
        }
        public override void SetGridInfo(TestPoint tp, double dis)
        {
            float? rxQual,rxlev;
            short? C2I;
            this.get_ParamFdd(tp, out rxQual, out rxlev, out C2I);

            if (rxQual != null)
            {
                base.SetQuality((float)rxQual);
            }
            if ((rxlev != null) && (rxlev >= -140) && (rxlev <= -10))
            {
                base.SetRxlev((float)rxlev);
            }
            if ((C2I != null) && (C2I >= -20) && (C2I <= 25))
            {
                base.SetC2I((float)C2I);
            }
            base.distance += dis;
            base.sampleLst.Add(tp);
        }
    }
}
