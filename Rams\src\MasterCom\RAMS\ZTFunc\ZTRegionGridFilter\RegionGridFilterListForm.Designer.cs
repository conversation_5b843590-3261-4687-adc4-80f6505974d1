﻿namespace MasterCom.RAMS.ZTFunc.ZTRegionGridFilter
{
    partial class RegionGridFilterListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.btnGis = new DevExpress.XtraEditors.SimpleButton();
            this.btnFilter = new DevExpress.XtraEditors.SimpleButton();
            this.numDistance = new DevExpress.XtraEditors.SpinEdit();
            this.numDuration = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.numTpCount = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.lv = new BrightIdeasSoftware.TreeListView();
            this.colRegName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colGridSumNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colFilterGridNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colGridRate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colTpNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colDuration = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSpeedM = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colFtpNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLtLng = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLtLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExp2Xls = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDuration.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTpCount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lv)).BeginInit();
            this.ctxMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.btnGis);
            this.splitContainerControl1.Panel1.Controls.Add(this.btnFilter);
            this.splitContainerControl1.Panel1.Controls.Add(this.numDistance);
            this.splitContainerControl1.Panel1.Controls.Add(this.numDuration);
            this.splitContainerControl1.Panel1.Controls.Add(this.labelControl5);
            this.splitContainerControl1.Panel1.Controls.Add(this.numTpCount);
            this.splitContainerControl1.Panel1.Controls.Add(this.labelControl4);
            this.splitContainerControl1.Panel1.Controls.Add(this.labelControl2);
            this.splitContainerControl1.Panel1.Controls.Add(this.labelControl3);
            this.splitContainerControl1.Panel1.Controls.Add(this.labelControl1);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.lv);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(983, 403);
            this.splitContainerControl1.SplitterPosition = 37;
            this.splitContainerControl1.TabIndex = 0;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // btnGis
            // 
            this.btnGis.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnGis.Location = new System.Drawing.Point(896, 9);
            this.btnGis.Name = "btnGis";
            this.btnGis.Size = new System.Drawing.Size(75, 23);
            this.btnGis.TabIndex = 16;
            this.btnGis.Text = "GIS图例设置";
            this.btnGis.Click += new System.EventHandler(this.btnGis_Click);
            // 
            // btnFilter
            // 
            this.btnFilter.Location = new System.Drawing.Point(601, 9);
            this.btnFilter.Name = "btnFilter";
            this.btnFilter.Size = new System.Drawing.Size(75, 23);
            this.btnFilter.TabIndex = 16;
            this.btnFilter.Text = "过滤";
            this.btnFilter.Click += new System.EventHandler(this.btnFilter_Click);
            // 
            // numDistance
            // 
            this.numDistance.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numDistance.Location = new System.Drawing.Point(479, 9);
            this.numDistance.Name = "numDistance";
            this.numDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDistance.Properties.IsFloatValue = false;
            this.numDistance.Properties.Mask.EditMask = "N00";
            this.numDistance.Properties.MaxValue = new decimal(new int[] {
            100000000,
            0,
            0,
            0});
            this.numDistance.Size = new System.Drawing.Size(78, 21);
            this.numDistance.TabIndex = 13;
            // 
            // numDuration
            // 
            this.numDuration.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numDuration.Location = new System.Drawing.Point(280, 9);
            this.numDuration.Name = "numDuration";
            this.numDuration.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDuration.Properties.IsFloatValue = false;
            this.numDuration.Properties.Mask.EditMask = "N00";
            this.numDuration.Properties.MaxValue = new decimal(new int[] {
            100000000,
            0,
            0,
            0});
            this.numDuration.Size = new System.Drawing.Size(78, 21);
            this.numDuration.TabIndex = 14;
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(563, 12);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(12, 14);
            this.labelControl5.TabIndex = 8;
            this.labelControl5.Text = "米";
            // 
            // numTpCount
            // 
            this.numTpCount.EditValue = new decimal(new int[] {
            40,
            0,
            0,
            0});
            this.numTpCount.Location = new System.Drawing.Point(99, 9);
            this.numTpCount.Name = "numTpCount";
            this.numTpCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numTpCount.Properties.IsFloatValue = false;
            this.numTpCount.Properties.Mask.EditMask = "N00";
            this.numTpCount.Properties.MaxValue = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numTpCount.Size = new System.Drawing.Size(78, 21);
            this.numTpCount.TabIndex = 15;
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(392, 12);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(81, 14);
            this.labelControl4.TabIndex = 9;
            this.labelControl4.Text = "且栅格内里程≥";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(364, 12);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(12, 14);
            this.labelControl2.TabIndex = 10;
            this.labelControl2.Text = "秒";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(193, 12);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(81, 14);
            this.labelControl3.TabIndex = 11;
            this.labelControl3.Text = "且栅格内时长≥";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(12, 12);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(81, 14);
            this.labelControl1.TabIndex = 12;
            this.labelControl1.Text = "栅格采样点数≥";
            // 
            // lv
            // 
            this.lv.AllColumns.Add(this.colRegName);
            this.lv.AllColumns.Add(this.colGridSumNum);
            this.lv.AllColumns.Add(this.colFilterGridNum);
            this.lv.AllColumns.Add(this.colGridRate);
            this.lv.AllColumns.Add(this.colTpNum);
            this.lv.AllColumns.Add(this.colDuration);
            this.lv.AllColumns.Add(this.colDistance);
            this.lv.AllColumns.Add(this.colSpeedM);
            this.lv.AllColumns.Add(this.colFtpNum);
            this.lv.AllColumns.Add(this.colLtLng);
            this.lv.AllColumns.Add(this.colLtLat);
            this.lv.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colRegName,
            this.colGridSumNum,
            this.colFilterGridNum,
            this.colGridRate,
            this.colTpNum,
            this.colDuration,
            this.colDistance,
            this.colSpeedM,
            this.colFtpNum,
            this.colLtLng,
            this.colLtLat});
            this.lv.ContextMenuStrip = this.ctxMenu;
            this.lv.Cursor = System.Windows.Forms.Cursors.Default;
            this.lv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lv.FullRowSelect = true;
            this.lv.GridLines = true;
            this.lv.Location = new System.Drawing.Point(0, 0);
            this.lv.Name = "lv";
            this.lv.OwnerDraw = true;
            this.lv.SelectedColumnTint = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.lv.ShowGroups = false;
            this.lv.Size = new System.Drawing.Size(983, 360);
            this.lv.TabIndex = 0;
            this.lv.UseCompatibleStateImageBehavior = false;
            this.lv.View = System.Windows.Forms.View.Details;
            this.lv.VirtualMode = true;
            this.lv.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.lv_MouseDoubleClick);
            // 
            // colRegName
            // 
            this.colRegName.HeaderFont = null;
            this.colRegName.Text = "区域";
            // 
            // colGridSumNum
            // 
            this.colGridSumNum.HeaderFont = null;
            this.colGridSumNum.Text = "总测试栅格数";
            // 
            // colFilterGridNum
            // 
            this.colFilterGridNum.HeaderFont = null;
            this.colFilterGridNum.Text = "条件栅格个数";
            // 
            // colGridRate
            // 
            this.colGridRate.HeaderFont = null;
            this.colGridRate.Text = "条件栅格占比(%)";
            // 
            // colTpNum
            // 
            this.colTpNum.HeaderFont = null;
            this.colTpNum.Text = "采样点个数";
            // 
            // colDuration
            // 
            this.colDuration.HeaderFont = null;
            this.colDuration.Text = "时长(4.5个采样点为1秒)(秒)";
            // 
            // colDistance
            // 
            this.colDistance.HeaderFont = null;
            this.colDistance.Text = "里程(米)";
            // 
            // colSpeedM
            // 
            this.colSpeedM.HeaderFont = null;
            this.colSpeedM.Text = "App层平均下载速率(Mbps)";
            // 
            // colFtpNum
            // 
            this.colFtpNum.HeaderFont = null;
            this.colFtpNum.Text = "FTP下载成功次数";
            // 
            // colLtLng
            // 
            this.colLtLng.HeaderFont = null;
            this.colLtLng.Text = "左上经度";
            // 
            // colLtLat
            // 
            this.colLtLat.HeaderFont = null;
            this.colLtLat.Text = "左上纬度";
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExp2Xls});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(139, 26);
            // 
            // miExp2Xls
            // 
            this.miExp2Xls.Name = "miExp2Xls";
            this.miExp2Xls.Size = new System.Drawing.Size(138, 22);
            this.miExp2Xls.Text = "导出Excel...";
            this.miExp2Xls.Click += new System.EventHandler(this.miExp2Xls_Click);
            // 
            // RegionGridFilterListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(983, 403);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "RegionGridFilterListForm";
            this.Text = "栅格重复率";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.numDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDuration.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTpCount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lv)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.SpinEdit numDistance;
        private DevExpress.XtraEditors.SpinEdit numDuration;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit numTpCount;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private BrightIdeasSoftware.TreeListView lv;
        private BrightIdeasSoftware.OLVColumn colRegName;
        private BrightIdeasSoftware.OLVColumn colGridSumNum;
        private BrightIdeasSoftware.OLVColumn colFilterGridNum;
        private BrightIdeasSoftware.OLVColumn colGridRate;
        private BrightIdeasSoftware.OLVColumn colTpNum;
        private BrightIdeasSoftware.OLVColumn colDuration;
        private BrightIdeasSoftware.OLVColumn colDistance;
        private BrightIdeasSoftware.OLVColumn colSpeedM;
        private BrightIdeasSoftware.OLVColumn colFtpNum;
        private BrightIdeasSoftware.OLVColumn colLtLng;
        private BrightIdeasSoftware.OLVColumn colLtLat;
        private DevExpress.XtraEditors.SimpleButton btnFilter;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExp2Xls;
        private DevExpress.XtraEditors.SimpleButton btnGis;
    }
}