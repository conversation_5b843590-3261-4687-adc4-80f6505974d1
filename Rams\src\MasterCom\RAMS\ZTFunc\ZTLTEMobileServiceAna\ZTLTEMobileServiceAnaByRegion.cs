﻿using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLTEMobileServiceAnaByRegion : ZTLTEMobileServiceAnaBase
    {
        public ZTLTEMobileServiceAnaByRegion(MainModel mainModel)
            : base(mainModel)
        {
            mEvents = new MobileServiceEvent(false);
        }

        protected static readonly object lockObj = new object();
        private static ZTLTEMobileServiceAnaByRegion intance = null;
        public static ZTLTEMobileServiceAnaByRegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTLTEMobileServiceAnaByRegion(MainModel.GetInstance());
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "移动互联业务时长分析(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22057, this.Name);//////
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        /// <summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is LTETestPointDetail)
                {
                    return Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }

    public class ZTLTEMobileServiceAnaByRegion_FDD : ZTLTEMobileServiceAnaBase_FDD
    {
        private static ZTLTEMobileServiceAnaByRegion_FDD instance = null;
        protected static readonly object lockObj = new object();
        public static ZTLTEMobileServiceAnaByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTLTEMobileServiceAnaByRegion_FDD(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        public ZTLTEMobileServiceAnaByRegion_FDD(MainModel mainModel)
            : base(mainModel)
        {
            mEvents = new MobileServiceEvent(true);
        }
        public override string Name
        {
            get { return "移动互联业务时长分析LTE_FDD(按区域)"; }
        }
        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        /// <summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is LTEFddTestPoint)
                {
                    return Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}