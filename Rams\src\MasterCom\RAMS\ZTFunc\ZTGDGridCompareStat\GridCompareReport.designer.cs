﻿namespace MasterCom.RAMS.KPI_Statistics
{
    partial class GridCompareReport
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.lnkReloadRpt = new System.Windows.Forms.LinkLabel();
            this.label1 = new System.Windows.Forms.Label();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.chkAllParam = new System.Windows.Forms.CheckBox();
            this.lbNoteAll = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.numTimeSpan = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.chkOnlyCellAna = new System.Windows.Forms.CheckBox();
            this.reportFilter = new MasterCom.RAMS.KPI_Statistics.ReportFilterControl();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeSpan)).BeginInit();
            this.SuspendLayout();
            // 
            // lnkReloadRpt
            // 
            this.lnkReloadRpt.AutoSize = true;
            this.lnkReloadRpt.Location = new System.Drawing.Point(483, 47);
            this.lnkReloadRpt.Name = "lnkReloadRpt";
            this.lnkReloadRpt.Size = new System.Drawing.Size(53, 12);
            this.lnkReloadRpt.TabIndex = 1;
            this.lnkReloadRpt.TabStop = true;
            this.lnkReloadRpt.Text = "重新加载";
            this.lnkReloadRpt.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lnkReloadRpt_LinkClicked);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.ForeColor = System.Drawing.Color.Black;
            this.label1.Location = new System.Drawing.Point(12, 47);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 15;
            this.label1.Text = "统计报表：";
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(449, 156);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(87, 27);
            this.btnCancel.TabIndex = 4;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(356, 156);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(87, 27);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // chkAllParam
            // 
            this.chkAllParam.AutoSize = true;
            this.chkAllParam.Location = new System.Drawing.Point(83, 81);
            this.chkAllParam.Name = "chkAllParam";
            this.chkAllParam.Size = new System.Drawing.Size(72, 16);
            this.chkAllParam.TabIndex = 2;
            this.chkAllParam.Text = "所有指标";
            this.chkAllParam.UseVisualStyleBackColor = true;
            // 
            // lbNoteAll
            // 
            this.lbNoteAll.AutoSize = true;
            this.lbNoteAll.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.lbNoteAll.Location = new System.Drawing.Point(161, 82);
            this.lbNoteAll.Name = "lbNoteAll";
            this.lbNoteAll.Size = new System.Drawing.Size(257, 12);
            this.lbNoteAll.TabIndex = 18;
            this.lbNoteAll.Text = "（勾选时，将会查询全部指标，资源占用较大）";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.ForeColor = System.Drawing.Color.Black;
            this.label2.Location = new System.Drawing.Point(12, 117);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 19;
            this.label2.Text = "时间粒度：";
            // 
            // numTimeSpan
            // 
            this.numTimeSpan.DecimalPlaces = 1;
            this.numTimeSpan.Location = new System.Drawing.Point(83, 115);
            this.numTimeSpan.Maximum = new decimal(new int[] {
            24,
            0,
            0,
            0});
            this.numTimeSpan.Minimum = new decimal(new int[] {
            5,
            0,
            0,
            65536});
            this.numTimeSpan.Name = "numTimeSpan";
            this.numTimeSpan.Size = new System.Drawing.Size(60, 21);
            this.numTimeSpan.TabIndex = 20;
            this.numTimeSpan.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numTimeSpan.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.label3.Location = new System.Drawing.Point(149, 117);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(209, 12);
            this.label3.TabIndex = 21;
            this.label3.Text = "小时（通常可设置0.5、1、3、6、24）";
            // 
            // chkOnlyCellAna
            // 
            this.chkOnlyCellAna.AutoSize = true;
            this.chkOnlyCellAna.Location = new System.Drawing.Point(356, 116);
            this.chkOnlyCellAna.Name = "chkOnlyCellAna";
            this.chkOnlyCellAna.Size = new System.Drawing.Size(108, 16);
            this.chkOnlyCellAna.TabIndex = 22;
            this.chkOnlyCellAna.Text = "只分析小区情况";
            this.chkOnlyCellAna.UseVisualStyleBackColor = true;
            // 
            // reportFilter
            // 
            this.reportFilter.Location = new System.Drawing.Point(83, 43);
            this.reportFilter.Name = "reportFilter";
            this.reportFilter.Size = new System.Drawing.Size(394, 28);
            this.reportFilter.TabIndex = 23;
            // 
            // GridCompareReport
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(548, 195);
            this.Controls.Add(this.reportFilter);
            this.Controls.Add(this.chkOnlyCellAna);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.numTimeSpan);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.lbNoteAll);
            this.Controls.Add(this.chkAllParam);
            this.Controls.Add(this.lnkReloadRpt);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "GridCompareReport";
            this.Text = "选择报表模板";
            ((System.ComponentModel.ISupportInitialize)(this.numTimeSpan)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.LinkLabel lnkReloadRpt;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.CheckBox chkAllParam;
        private System.Windows.Forms.Label lbNoteAll;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numTimeSpan;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.CheckBox chkOnlyCellAna;
        private MasterCom.RAMS.KPI_Statistics.ReportFilterControl reportFilter;
    }
}