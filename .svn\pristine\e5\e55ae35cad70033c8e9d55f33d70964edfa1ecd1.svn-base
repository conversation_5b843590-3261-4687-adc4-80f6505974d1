﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    class LteURLAnalyzer
    {
        protected List<int> httpIDList = null;
        protected List<int> downloadIDList = null;
        protected List<int> videoIDList = null;


        public LteURLAnalyzer()
        {
            httpIDList = new List<int>();
            downloadIDList = new List<int>();
            videoIDList = new List<int>();

            httpIDList = GetHttpID();
            downloadIDList = GetDownloadID();
            videoIDList = GetVideoID();
        }

        public void SetBroInfo(LteURLBroURL broURL)
        {
            if (broURL.EvtDisSuc.Count > 0)
            {
                foreach (Event evt in broURL.EvtDisSuc)
                {
                    broURL.Value2Sum += double.Parse(evt["Value2"].ToString());
                }
                broURL.DisDelay = Math.Round((double)broURL.Value2Sum / (double)broURL.DisSuc / 1000, 3);
            }
            if (broURL.EvtComple.Count > 0)
            {
                foreach (Event evt in broURL.EvtComple)
                {
                    broURL.Value1Sum += double.Parse(evt["Value1"].ToString());
                    broURL.Value2Sum1 += double.Parse(evt["Value2"].ToString());
                }
                broURL.Time = Math.Round((double)broURL.Value1Sum / (double)broURL.Complete / 1000, 3);
                broURL.Speed = Math.Round((((double)broURL.Value2Sum1 * 8000) / (double)broURL.Value1Sum) / 1024, 3);
            }
        }

        protected List<LteURLBroURL> HttpAnalyze(Dictionary<string, List<Event>> dic)
        {
            List<LteURLBroURL> dtlBroURLs = new List<LteURLBroURL>();
            foreach (KeyValuePair<string, List<Event>> kvp in dic)
            {
                List<Event> CompleteTemp = new List<Event>();
                List<Event> DisSucTemp = new List<Event>();
                List<Event> DisFailTemp = new List<Event>();
                LteURLBroURL dtlBroURL = new LteURLBroURL(kvp.Key);
                Event evtBeforRequest = null;
                Event firstEvt = null;
                Event failEvt = null;
                if (kvp.Value.Count < 2)
                {
                    continue;
                }
                foreach (Event evt in kvp.Value)
                {
                    if (evt.ID == (int)LteURLCheckMsg.HttpRequestEventID || evt.ID == (int)LteURLCheckMsg.HttpRequestEventID_FDD)
                    {
                        if (evtBeforRequest != null)
                        {
                            dealHttpEndEvent(CompleteTemp, DisSucTemp, DisFailTemp, dtlBroURL, evtBeforRequest, firstEvt, failEvt);
                            failEvt = null;
                        }
                        firstEvt = evt;
                    }
                    else
                    {
                        evtBeforRequest = evt;
                        failEvt = addHttpEvt(CompleteTemp, DisSucTemp, DisFailTemp, failEvt, evt);
                    }
                }
                dealHttpEndEvent(CompleteTemp, DisSucTemp, DisFailTemp, dtlBroURL, evtBeforRequest, firstEvt, failEvt);
                SetBroInfo(dtlBroURL);
                dtlBroURLs.Add(dtlBroURL);
            }
            return dtlBroURLs;
        }

        private void dealHttpEndEvent(List<Event> CompleteTemp, List<Event> DisSucTemp, List<Event> DisFailTemp, LteURLBroURL dtlBroURL, Event evtBeforRequest, Event firstEvt, Event failEvt)
        {
            if (isHttpEndEvent(evtBeforRequest))
            {
                dtlBroURL.EvtComple.AddRange(CompleteTemp);
                dtlBroURL.EvtDisFai.AddRange(DisFailTemp);
                dtlBroURL.EvtDisSuc.AddRange(DisSucTemp);

                LteURLEvent dtlEvent1 = new LteURLEvent(firstEvt, evtBeforRequest, failEvt, dtlBroURL.URL);
                dtlBroURL.Events.Add(dtlEvent1);
            }
            CompleteTemp.Clear();
            DisFailTemp.Clear();
            DisSucTemp.Clear();
        }

        private Event addHttpEvt(List<Event> CompleteTemp, List<Event> DisSucTemp, List<Event> DisFailTemp, Event failEvt, Event evt)
        {
            if (evt.ID == (int)LteURLCheckMsg.HttpFailEventID || evt.ID == (int)LteURLCheckMsg.HttpFailEventID_FDD)
            {
                failEvt = evt;
            }
            else if (evt.ID == (int)LteURLCheckMsg.HttpDisFailID || evt.ID == (int)LteURLCheckMsg.HttpDisFailID_FDD)
            {
                DisFailTemp.Add(evt);
            }
            else if (evt.ID == (int)LteURLCheckMsg.HttpCompleteID || evt.ID == (int)LteURLCheckMsg.HttpCompleteID_FDD)
            {
                CompleteTemp.Add(evt);
            }
            else if (evt.ID == (int)LteURLCheckMsg.HttpSuccessID || evt.ID == (int)LteURLCheckMsg.HttpSuccessID_FDD)
            {
                DisSucTemp.Add(evt);
            }

            return failEvt;
        }

        private bool isHttpEndEvent(Event evt)
        {
            if (evt == null)
            {
                return false;
            }
            return evt.ID == (int)LteURLCheckMsg.HttpCompleteID || evt.ID == (int)LteURLCheckMsg.HttpFailEventID 
                || evt.ID == (int)LteURLCheckMsg.HttpIncomplete || evt.ID == (int)LteURLCheckMsg.HttpDisFailID
                || evt.ID == (int)LteURLCheckMsg.HttpCompleteID_FDD || evt.ID == (int)LteURLCheckMsg.HttpFailEventID_FDD
                || evt.ID == (int)LteURLCheckMsg.HttpIncomplete_FDD || evt.ID == (int)LteURLCheckMsg.HttpDisFailID_FDD;
        }

        public void SetDowInfo(LteURLDowURL dowURL)
        {
            if (dowURL.EvtDowSuc.Count > 0)
            {
                foreach (Event evt in dowURL.EvtDowSuc)
                {
                    dowURL.Value1Sum += double.Parse(evt["Value1"].ToString());
                    dowURL.Value2Sum += double.Parse(evt["Value2"].ToString());
                }
                dowURL.SucSpeed = Math.Round(((float)dowURL.Value2Sum / (float)dowURL.Value1Sum) * 8000 / 1024, 3);
                if (dowURL.EvtDowFai.Count > 0)
                {
                    foreach (Event evt in dowURL.EvtDowFai)
                    {
                        dowURL.Value1Sum1 += double.Parse(evt["Value1"].ToString());
                        dowURL.Value2Sum1 += double.Parse(evt["Value2"].ToString());
                    }
                }
                dowURL.Speed = Math.Round(((float)(dowURL.Value2Sum + dowURL.Value2Sum1) / (float)(dowURL.Value1Sum + dowURL.Value1Sum1)) * 8000 / 1024, 3);
            }
        }

        protected List<LteURLDowURL> DownAnalyze(Dictionary<string, List<Event>> dic)
        {
            List<LteURLDowURL> dtlDownURLs = new List<LteURLDowURL>();
            foreach (KeyValuePair<string, List<Event>> kvp in dic)
            {
                LteURLDowURL dtlDownURL = new LteURLDowURL(kvp.Key);
                Event evtBeforRequest = null;
                Event firstEvt = null;
                Event failEvt = null;
                if (kvp.Value.Count < 2)
                {
                    continue;
                }
                foreach (Event evt in kvp.Value)
                {
                    addDownEvt(kvp.Key, dtlDownURL, ref evtBeforRequest, ref firstEvt, ref failEvt, evt);
                }
                LteURLEvent dtlEventTemp = new LteURLEvent(firstEvt, evtBeforRequest, failEvt, kvp.Key);
                dtlDownURL.Events.Add(dtlEventTemp);

                SetDowInfo(dtlDownURL);
                dtlDownURLs.Add(dtlDownURL);

            }
            return dtlDownURLs;
        }

        private void addDownEvt(string key, LteURLDowURL dtlDownURL, ref Event evtBeforRequest, ref Event firstEvt, ref Event failEvt, Event evt)
        {
            if (evt.ID == (int)LteURLCheckMsg.DownRequestEventID || evt.ID == (int)LteURLCheckMsg.DownRequestEventID_FDD)
            {

                if (evtBeforRequest == null)
                {
                    firstEvt = evt;
                }
                else
                {
                    LteURLEvent dtlEvent = new LteURLEvent(firstEvt, evtBeforRequest, failEvt, key);
                    dtlDownURL.Events.Add(dtlEvent);
                    failEvt = null;
                    firstEvt = evt;
                }
            }
            else
            {
                evtBeforRequest = evt;
                if (evt.ID == (int)LteURLCheckMsg.DownloadFailEventID || evt.ID == (int)LteURLCheckMsg.DownloadFailEventID_FDD)
                {
                    failEvt = evt;
                    dtlDownURL.EvtFail.Add(evt);
                }
                else if (evt.ID == (int)LteURLCheckMsg.DownDropID || evt.ID == (int)LteURLCheckMsg.DownDropID_FDD)
                {
                    dtlDownURL.EvtDowFai.Add(evt);
                }
                else if (evt.ID == (int)LteURLCheckMsg.DownSuccessID || evt.ID == (int)LteURLCheckMsg.DownSuccessID_FDD)
                {
                    dtlDownURL.EvtDowSuc.Add(evt);
                }
            }
        }

        public void SetVideoInfo(LteURLVideoURL videoURL)
        {
            if (videoURL.EvtLastData.Count > 0)
            {
                int count = videoURL.EvtLastData.Count;
                foreach (Event evt in videoURL.EvtLastData)
                {
                    videoURL.value7Sum35 += double.Parse(evt["Value7"].ToString());
                    videoURL.value6Sum35 += double.Parse(evt["Value6"].ToString());
                    videoURL.value5Sum35 += double.Parse(evt["Value5"].ToString());
                    videoURL.value4Sum35 += double.Parse(evt["Value4"].ToString());
                    videoURL.value2Sum35 += double.Parse(evt["Value2"].ToString());
                    videoURL.value1Sum35 += double.Parse(evt["Value1"].ToString());
                }
                videoURL.Time = Math.Round(((float)videoURL.value6Sum35 / (float)count) / 1000, 3);
                //videoURL.RebufferTime = Math.Round(((float)videoURL.value4Sum35 / (float)count) / 1000, 3);
                videoURL.PlayTime = Math.Round(((float)videoURL.value5Sum35 / (float)count) / 1000, 3);
                videoURL.TimeoutRate = Math.Round(((float)videoURL.value5Sum35 / (float)videoURL.value6Sum35 - 1), 4);
                videoURL.DownSpeed = Math.Round(((float)videoURL.value2Sum35 / (float)videoURL.value1Sum35) * 8000 / 1024, 3);
            }
            
            if (videoURL.EvtPlayStart.Count > 0)
            {
                int count = videoURL.EvtPlayStart.Count;
                foreach (Event evt in videoURL.EvtPlayStart)
                {
                    videoURL.value1Sum38 += double.Parse(evt["Value1"].ToString());
                }
                videoURL.Delay = Math.Round(((float)videoURL.value1Sum38 / (float)count) / 1000, 3);
                videoURL.LoadSpeed = Math.Round(((float)videoURL.value7Sum35 / (float)videoURL.value1Sum38) * 8000 / 1024, 3);
            }

            if (videoURL.EvtRebufferEnd.Count > 0 || videoURL.EvtFlvPlayFinished.Count > 0)
            {
                int count = videoURL.EvtRebufferEnd.Count + videoURL.EvtFlvPlayFinished.Count;
                foreach(Event evt in videoURL.EvtRebufferEnd)
                {
                    videoURL.value2Sum34 += double.Parse(evt["Value2"].ToString());
                }
                foreach (Event evt in videoURL.EvtFlvPlayFinished)
                {
                    videoURL.value2Sum29 += double.Parse(evt["Value2"].ToString());
                }
                double? sum = videoURL.value2Sum34 + videoURL.value2Sum29;
                videoURL.RebufferTime = Math.Round(((float)sum / (float)count)/1000, 3);
            }
        }

        protected List<LteURLVideoURL> VideoAnalyze(Dictionary<string, List<Event>> dic)
        {
            List<LteURLVideoURL> dtlVideoURLs = new List<LteURLVideoURL>();
            foreach (KeyValuePair<string, List<Event>> kvp in dic)
            {
                LteURLVideoURL dtlVideoURL = new LteURLVideoURL(kvp.Key);
                Event evtBeforRequest = null;
                Event firstEvt = null;
                Event failEvt = null;
                if (kvp.Value.Count < 2)
                {
                    continue;
                }
                foreach (Event evt in kvp.Value)
                {
                    if (evt.ID == (int)LteURLCheckMsg.VideoRequestEventID || evt.ID == (int)LteURLCheckMsg.VideoRequestEventID_FDD)
                    {
                        if (evtBeforRequest == null)
                        {
                            firstEvt = evt;
                            dtlVideoURL.EvtReq.Add(evt);
                        }
                        else
                        {
                            dtlVideoURL.EvtReq.Add(evt);
                            LteURLEvent dtlEvent = new LteURLEvent(firstEvt, evtBeforRequest, failEvt, kvp.Key);
                            dtlVideoURL.Events.Add(dtlEvent);
                            failEvt = null;
                            firstEvt = evt;
                        }
                    }
                    evtBeforRequest = evt;
                    failEvt = addVideoEvt(dtlVideoURL, failEvt, evt);
                }
                LteURLEvent dtlEventTemp = new LteURLEvent(firstEvt, evtBeforRequest, failEvt, kvp.Key);
                dtlVideoURL.Events.Add(dtlEventTemp);

                SetVideoInfo(dtlVideoURL);
                dtlVideoURLs.Add(dtlVideoURL);
            }
            return dtlVideoURLs;
        }

        private Event addVideoEvt(LteURLVideoURL dtlVideoURL, Event failEvt, Event evt)
        {
            if (evt.ID == (int)LteURLCheckMsg.VideoFailID || evt.ID == (int)LteURLCheckMsg.VideoFailID_FDD)
            {
                failEvt = evt;
            }
            else if (evt.ID == (int)LteURLCheckMsg.VideoFinish || evt.ID == (int)LteURLCheckMsg.VideoFinish_FDD)
            {
                dtlVideoURL.EvtSuc.Add(evt);
            }
            else if (evt.ID == (int)LteURLCheckMsg.VideoLastData || evt.ID == (int)LteURLCheckMsg.VideoLastData_FDD)
            {
                dtlVideoURL.EvtLastData.Add(evt);
            }
            else if (evt.ID == (int)LteURLCheckMsg.VideoRebufferStartID || evt.ID == (int)LteURLCheckMsg.VideoRebufferStartID_FDD)
            {
                dtlVideoURL.EvtRebuffer.Add(evt);
            }
            else if (evt.ID == (int)LteURLCheckMsg.VideoReproductionStart || evt.ID == (int)LteURLCheckMsg.VideoReproductionStart_FDD)
            {
                dtlVideoURL.EvtPlayStart.Add(evt);
            }
            else if (evt.ID == (int)LteURLCheckMsg.VideoRebufferEndID || evt.ID == (int)LteURLCheckMsg.VideoRebufferEndID_FDD)
            {
                dtlVideoURL.EvtRebufferEnd.Add(evt);
            }
            else if (evt.ID == (int)LteURLCheckMsg.FlvPlayFinished || evt.ID == (int)LteURLCheckMsg.FlvPlayFinished_FDD)
            {
                dtlVideoURL.EvtFlvPlayFinished.Add(evt);
            }

            return failEvt;
        }

        public String GetURL(int sn, List<Message> messages)
        {
            string url = "";
            for (int i = sn - 1; i >= 0; i--)
            {
                foreach (Message msg in messages)
                {
                    if (msg.SN == i)
                    {
                        byte[] source = ((MessageWithSource)msg).Source;
                        url = DisplaySrcCode(source);
                        return url;
                    }
                }
            }
            return url;
        }


        #region 解析URL
        private String ToAssicString(int bgnPos, byte[] srcCode)
        {
            byte byt = srcCode[bgnPos];
            if (byt < 0 || byt > 0xa0)
                return ".";
            if (byt >= '!' && byt <= '~')
            {
                return Encoding.Default.GetString(srcCode, bgnPos, 1);
            }
            return ".";
        }

        private String SrcToString(int bgnPos, int len, byte[] srcCode)
        {
            StringBuilder dstStr = new StringBuilder();
            for (int pos = bgnPos; pos < (bgnPos + len); pos++)
            {
                String tmp = ToAssicString(pos, srcCode);
                dstStr.Append(tmp);
            }
            return dstStr.ToString();
        }

        private int FindFistChar(byte[] srcCode)
        {
            byte byt;
            for (int i = 0; i < srcCode.Length; i++)
            {
                byt = srcCode[i];
                if (Char.IsLetter((char)byt))
                {
                    string firstChar = Encoding.Default.GetString(srcCode, i, 1);
                    switch (firstChar)
                    {
                        case "B":
                            if (srcCode.Length > i + 14 && Encoding.Default.GetString(srcCode, i + 7, 4) == "http")
                            {
                                return i + 14;
                            }
                            return i + 7;
                        case "S":
                            return i + 7;
                        case "D":
                            return i + 9;
                    }
                }
            }
            return srcCode.Length - 1;
        }

        public string DisplaySrcCode(byte[] srcCode)
        {
            string URLtext = "";
            if (srcCode != null)
            {
                int pos = FindFistChar(srcCode);
                if (pos < srcCode.Length)
                {
                    int lastLen = (srcCode.Length - pos);
                    URLtext = SrcToString(pos, lastLen, srcCode);
                }
                else
                {
                    URLtext = ".";
                }
            }
            return URLtext;
        }
        #endregion

        #region 事件ID
        private List<int> GetHttpID()
        {
            List<int> httpIDListTmp = new List<int>();
            for (int i = 1211; i < 1216; i++)
            {
                httpIDListTmp.Add(i);
                httpIDListTmp.Add(i + 2000);
            }
            httpIDListTmp.Add(1269);
            httpIDListTmp.Add(3269);

            return httpIDListTmp;
        }

        private List<int> GetDownloadID()
        {
            List<int> downloadIDListTmp = new List<int>();
            for (int i = 1216; i < 1219; i++)
            {
                downloadIDListTmp.Add(i);
                downloadIDListTmp.Add(i + 2000);
            }
            downloadIDListTmp.Add(1270);
            downloadIDListTmp.Add(3270);
            return downloadIDListTmp;
        }

        private List<int> GetVideoID()
        {
            List<int> videoIDListTmp = new List<int>();
            for (int i = 1229; i < 1240; i++)
            {
                videoIDListTmp.Add(i);
                videoIDListTmp.Add(i + 2000);
            }
            return videoIDListTmp;
        }

        public bool IsInHttpID(int ID)
        {
            if (httpIDList.Contains(ID))
            {
                return true;
            }
            return false;
        }

        public bool IsInDownloadID(int ID)
        {
            if (downloadIDList.Contains(ID))
            {
                return true;
            }
            return false;
        }

        public bool IsInVideoID(int ID)
        {
            if (videoIDList.Contains(ID))
            {
                return true;
            }
            return false;
        }
        #endregion

    }

    enum LteURLCheckMsg
    {
        HttpPageRequest = 2147424830, //0x7fff1A3E,"Http_page_request"
        HttpDownloadBegin = 2147421185,
        VideoPlayRequest = 2147424513,

        HttpFailEventID = 1269,
        HttpRequestEventID = 1211,
        HttpCompleteID = 1214,
        HttpSuccessID = 1212,
        HttpDisFailID = 1213,
        HttpIncomplete = 1215,

        HttpFailEventID_FDD = 3269,
        HttpRequestEventID_FDD = 3211,
        HttpCompleteID_FDD = 3214,
        HttpSuccessID_FDD = 3212,
        HttpDisFailID_FDD = 3213,
        HttpIncomplete_FDD = 3215,

        DownloadFailEventID = 1270,
        DownRequestEventID = 1216,
        DownSuccessID = 1217,
        DownDropID = 1218,

        DownloadFailEventID_FDD = 3270,
        DownRequestEventID_FDD = 3216,
        DownSuccessID_FDD = 3217,
        DownDropID_FDD = 3218,

        FlvPlayFinished = 1229,
        VideoFailID = 1239,
        VideoRequestEventID = 1231,
        VideoRebufferStartID = 1233,
        VideoRebufferEndID = 1234,
        VideoLastData = 1235,
        VideoFinish = 1236,
        VideoReproductionStart = 1238,

        FlvPlayFinished_FDD = 3229,
        VideoFailID_FDD = 3239,
        VideoRequestEventID_FDD = 3231,
        VideoRebufferStartID_FDD = 3233,
        VideoRebufferEndID_FDD = 3234,
        VideoLastData_FDD = 3235,
        VideoFinish_FDD = 3236,
        VideoReproductionStart_FDD = 3238,



    }
}
