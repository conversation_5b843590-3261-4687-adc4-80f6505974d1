﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage._45G700MHighReverseFlowCoverage
{
    public partial class HighReverseFlowCoverage4G_700MDlg : BaseDialog
    {
        CoverageCondition curCondition = new CoverageCondition();

        public HighReverseFlowCoverage4G_700MDlg()
        {
            InitializeComponent();
            dataBaseConnection.Init("主库连接设置");
        }

        public void SetCondition(CoverageCondition condition)
        {
            if (condition == null)
            {
                return;
            }
            dataBaseConnection.SetCondition(condition.DBCond);
            curCondition.SetDefalut(condition);
        }

        public CoverageCondition GetCondition()
        {
            return curCondition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            var cond = dataBaseConnection.GetCondition();
            if (cond != null)
            {
                curCondition.DBCond = cond;
                DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show("主库连接设置不能为空");
            }
        }
    }
}
