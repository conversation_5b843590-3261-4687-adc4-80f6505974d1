﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.NOP
{
    public class ESResultInfo
    {
        public ESResultInfo(bool isVer2017 = false)
        {
            this.IsVer2017 = isVer2017;
        }
        public bool IsVer2017
        {
            get;
            private set;
        }
        public override string ToString()
        {
            return string.Format("{0}_{1}_{2}_{3}", FileID, EventID, SeqID, Suggest);
        }

        public int FileID { get; set; }
        public int EventID { get; set; }
        public int SeqID { get; set; }
        public int RelationID { get; set; }
        public string CTime { get; set; }
        public string STime { get; set; }
        public string ETime { get; set; }
        public double MidLongitude { get; set; }
        public double MidLatitude { get; set; }
        public string PrimaryType { get; set; }
        public string SpecificType { get; set; }
        public string Suggest { get; set; }
        public string Detail { get; set; }
        public Dictionary<int, bool> ContainProcID { get; set; }
        public byte[] Extend { get; set; }

        public void FillMsg(byte[] msgArr)
        {
            try
            {
                fillMsg2017(msgArr);
            }
            catch
            {
                fillMsg(msgArr);
            }
        }

        private void fillMsg2017(byte[] msgArr)
        {
            NodeResultSet = new List<NodeResultInfo>();
            ContainProcID = new Dictionary<int, bool>();
            int offset = 0;
            while (offset < msgArr.Length)
            {
                NodeResultInfo nodeInfo = new NodeResultInfo();
                int byteCount = BitConverter.ToInt32(msgArr, offset);
                offset += 4;
                nodeInfo.ProcSeq = Encoding.UTF8.GetString(msgArr, offset, byteCount);
                offset += byteCount;
                nodeInfo.NodeID = BitConverter.ToInt32(msgArr, offset);
                offset += 4;
                nodeInfo.PreNodeID = BitConverter.ToInt32(msgArr, offset);
                offset += 4;
                byteCount = BitConverter.ToInt32(msgArr, offset);
                offset += 4;
                nodeInfo.FillMsg(Encoding.UTF8.GetString(msgArr, offset, byteCount));
                offset += byteCount;
                NodeResultSet.Add(nodeInfo);
                fillContainProc(nodeInfo.ProcSeq);
            }
        }

        private void fillContainProc(string procSeq)
        {
            string[] procID = procSeq.TrimEnd('_').Split('>');
            foreach (string s in procID)
            {
                int id = 0;
                if (int.TryParse(s, out id))
                {
                    ContainProcID[id] = true;
                }
            }
        }

        private void fillMsg(byte[] msgArr)
        {
            NodeResultSet = new List<NodeResultInfo>();
            int offset = 0;
            while (offset < msgArr.Length)
            {
                NodeResultInfo nodeInfo = new NodeResultInfo();
                nodeInfo.ProcID = BitConverter.ToInt32(msgArr, offset);
                offset += 4;
                nodeInfo.NodeID = BitConverter.ToInt32(msgArr, offset);
                offset += 4;
                int byteCount = BitConverter.ToInt32(msgArr, offset);
                offset += 4;
                nodeInfo.FillMsg(Encoding.UTF8.GetString(msgArr, offset, byteCount));
                offset += byteCount;
                NodeResultSet.Add(nodeInfo);
            }
        }

        public List<NodeResultInfo> NodeResultSet { get; set; } = new List<NodeResultInfo>();

        public NodeResultInfo GetNodeInfo(int procID, int nodeID)
        {
            NodeResultInfo nodeInfo = NodeResultSet.Find(delegate (NodeResultInfo x) { return x.ProcID == procID && x.NodeID == nodeID; });
            return nodeInfo;
        }

        public NodeResultInfo GetNodeInfo(string seq, int nodeID)
        {
            NodeResultInfo nodeInfo = NodeResultSet.Find(delegate (NodeResultInfo x)
            {
                return x.ProcSeq == seq
&& x.NodeID == nodeID;
            });
            return nodeInfo;
        }

        public bool ContainsProcResult(int procID)
        {
            NodeResultInfo nodeInfo = NodeResultSet.Find(delegate (NodeResultInfo x) { return x.ProcID == procID; });
            return nodeInfo != null;
        }

        public string[] DetailSet
        {
            get
            {
                return Detail.Split(';');
            }
        }
    }

    public class NodeResultInfo
    {
        public override string ToString()
        {
            return string.Format("{0}_{1}", ProcSeq == null ? "" : ProcSeq, NodeID);
        }
        public string ProcSeq { get; set; }
        public int ProcID { get; set; }
        public int NodeID { get; set; }
        public void FillMsg(string content)
        {
            if (content.Length > 1 && content[1] == ',')
            {
                IsSatisified = content.Length > 1 && content.Substring(0, 1) == "1";
                if (content.Length > 2)
                {
                    Details = content.Substring(2);
                }
            }
            else
            {
                Details = content;
            }
        }

        public string Details { get; set; } = string.Empty;
        public bool IsSatisified { get; set; } = false;

        public int PreNodeID { get; set; }
    }

}
