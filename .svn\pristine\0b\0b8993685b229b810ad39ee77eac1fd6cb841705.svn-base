﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTLteDownloadIpGroup
{
    public partial class DownloadIpGroupForm : MinCloseForm
    {
        public DownloadIpGroupForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(gv);
        }

        public void FillData(List<DownloadInfo> dataSet)
        {
            gridCtrl.DataSource = dataSet;
            gridCtrl.RefreshDataSource();
            gv.BestFitColumns();
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gv.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            DownloadInfo dl = gv.GetRow(info.RowHandle) as DownloadInfo;
            if (dl==null)
            {
                return;
            }
            MainModel.ClearDTData();
            foreach (TestPoint tp in dl.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            MainModel.FireDTDataChanged(this);
        }



    }
}
