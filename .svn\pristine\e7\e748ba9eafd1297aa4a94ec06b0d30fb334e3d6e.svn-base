﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class MessageIntervalSettingForm : BaseDialog
    {
        public MessageIntervalSettingForm()
        {
            InitializeComponent();
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            btnStart.Click += BtnStart_Click;
            btnEnd.Click += BtnEnd_Click;
        }

        public void GetCondition(out int startMessageID, out int endMessageID)
        {
            startMessageID = (int)txtStart.Tag;
            endMessageID = (int)txtEnd.Tag;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtStart.Text))
            {
                MessageBox.Show("未选择开始信令", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.None;
            }
            else if (string.IsNullOrEmpty(txtEnd.Text))
            {
                MessageBox.Show("未选择结束信令", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.None;
            }
            else
            {
                DialogResult = DialogResult.OK;
            }
        }

        private void BtnStart_Click(object sender, EventArgs e)
        {
            if (selectForm == null)
            {
                selectForm = new MessageSelectForm();
            }
            if (selectForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            MessageInfo mInfo = MessageInfoManager.GetInstance()[selectForm.SelectedMessageID];
            txtStart.Text = GetMessageInfoDesc(mInfo);
            txtStart.Tag = selectForm.SelectedMessageID;
        }

        private void BtnEnd_Click(object sender, EventArgs e)
        {
            if (selectForm == null)
            {
                selectForm = new MessageSelectForm();
            }
            if (selectForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            MessageInfo mInfo = MessageInfoManager.GetInstance()[selectForm.SelectedMessageID];
            txtEnd.Text = GetMessageInfoDesc(mInfo);
            txtEnd.Tag = selectForm.SelectedMessageID;
        }

        private string GetMessageInfoDesc(MessageInfo mInfo)
        {
            string retString = null;
            while (mInfo != null)
            {
                if (retString == null)
                {
                    retString = mInfo.Name;
                }
                else
                {
                    retString = mInfo.Name + ":" + retString;
                }
                mInfo = mInfo.Parent;
            }
            return retString;
        }

        private MessageSelectForm selectForm;
    }
}
