﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYScanGridResultForm : MinCloseForm
    {
        protected ScanGridInfo lastSelectedData = new ScanGridInfo();
        protected List<ScanGridInfo> curInfos;
        protected List<ScanGridInfo> maxRSRPInfos;

        ZTScanGridLayer layer;
        MapForm mf = null;

        public ZTDIYScanGridResultForm()
        {
            InitializeComponent();
            init();
        }

        private void init()
        {
            cmbRendingIndex.SelectedIndexChanged -= cmbRendingIndex_SelectedIndexChanged;
            cmbRendingIndex.Items.Add("R0_RP");
            cmbRendingIndex.Items.Add("R0_RQ");
            cmbRendingIndex.Items.Add("R0_CINR");
            cmbRendingIndex.Items.Add("SSS_RP");
            cmbRendingIndex.Items.Add("SSS_RSSI");
            cmbRendingIndex.SelectedItem = "R0_RP";
            cmbRendingIndex.SelectedIndexChanged += cmbRendingIndex_SelectedIndexChanged;
        }

        public void FillData(List<ScanGridInfo> scanGridInfoList)
        {
            maxRSRPInfos = null;
            curInfos = scanGridInfoList;
            gridCtrl.DataSource = scanGridInfoList;

            mf = MainModel.MainForm.GetMapForm();
            MTGis.LayerBase clayer = mf.GetTempLayerBase(typeof(ZTScanGridLayer));
            layer = clayer as ZTScanGridLayer;
            layer.SelectedGridChanged -= selectedGridChanged;
            layer.SelectedGridChanged += selectedGridChanged;
            layer.GridInfos = scanGridInfoList;
            layer.SelectedGrid = null;
            layer.InitColor(ZTScanGridLayer.RenderingIndex.R0_RP);
            layer.CurType = typeof(ZTDIYScanGridResultForm);
            cbxMaxRSRP_CheckedChanged(null, null);
            mf.updateMap();
            MainModel.FireSetDefaultMapSerialTheme(layer.SerialInfoName);
            GotoSelectedView();
        }

        private void selectedGridChanged(object sender, EventArgs e)
        {
            if (sender == typeof(ZTDIYScanGridResultForm) && layer.SelectedGrid != null)
            {
                int index = curInfos.IndexOf(layer.SelectedGrid);
                int realIndex = gv.DataController.GetControllerRow(index);
                if (realIndex >= 0)
                {
                    gv.ClearSelection();
                    gv.FocusedRowHandle = realIndex;
                    gv.SelectRow(realIndex);
                }
                MainModel.MainForm.GetMapForm().GoToView(layer.SelectedGrid.CentLng, layer.SelectedGrid.CentLat);
            }
        }

        /// <summary>
        /// 图层跳转到数据所在位置
        /// </summary>
        protected virtual void GotoSelectedView()
        {
            object imlongitude = gv.GetFocusedRowCellValue("TLLongitude");
            object imlatitude = gv.GetFocusedRowCellValue("TLLatitude");
            if (imlongitude != null && imlatitude != null && imlongitude.ToString() != "" && imlatitude.ToString() != "")
            {
                double fLong = (double)imlongitude;
                double fLat = (double)imlatitude;
                MainModel.MainForm.GetMapForm().GoToView(fLong, fLat);
            }
        }

        private void cmbRendingIndex_SelectedIndexChanged(object sender, EventArgs e)
        {
            ZTScanGridLayer.RenderingIndex curRangeType = ZTScanGridLayer.RenderingIndex.R0_RP;
            if (cmbRendingIndex.SelectedItem.ToString() == "R0_RQ")
            {
                curRangeType = ZTScanGridLayer.RenderingIndex.R0_RQ;
            }
            else if (cmbRendingIndex.SelectedItem.ToString() == "R0_CINR")
            {
                curRangeType = ZTScanGridLayer.RenderingIndex.R0_CINR;
            }
            else if (cmbRendingIndex.SelectedItem.ToString() == "SSS_RP")
            {
                curRangeType = ZTScanGridLayer.RenderingIndex.SSS_RP;
            }
            else if (cmbRendingIndex.SelectedItem.ToString() == "SSS_RSSI")
            {
                curRangeType = ZTScanGridLayer.RenderingIndex.SSS_RSSI;
            }
            layer.InitColor(curRangeType);
            MainModel.FireSetDefaultMapSerialTheme(layer.SerialInfoName);
            mf.updateMap();
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            //设置选中行
            int focusedhandle = gv.FocusedRowHandle;
            object o = gv.GetRow(focusedhandle);
            ScanGridInfo selectedData = o as ScanGridInfo;
            if (selectedData != null)
            {
                layer.SelectedGrid = selectedData;
                MainModel.MainForm.GetMapForm().GoToView(selectedData.CentLng, selectedData.CentLat);
            }
        }

        #region 导出Excel
        private void ToolStripExport_Click(object sender, EventArgs e)
        {
            int maxCnt = 200000;
            if (gv.DataRowCount > 200000)
            {
                if (MessageBox.Show("数据量已超过" + maxCnt + "行，将导出CSV格式文件？", "提醒", MessageBoxButtons.YesNo) == DialogResult.Yes)
                {
                    SaveFileDialog dlg = new SaveFileDialog();
                    dlg.Filter = FilterHelper.Csv;
                    if (dlg.ShowDialog() == DialogResult.OK)
                    {
                        WaitTextBox.Show("", exportCsv, dlg.FileName);
                    }
                }
            }
            else
            {
                ExcelNPOIManager.ExportToExcel(gv);
            }
        }

        private void exportCsv(object fileName)
        {
            FileStream fs = new FileStream(fileName.ToString(), FileMode.Create, FileAccess.Write);
            StreamWriter streamWriter = new StreamWriter(fs, Encoding.UTF8);
            StringBuilder sb = new StringBuilder();
            for (int c = 0; c < gv.Columns.Count; c++)
            {
                sb.Append(gv.Columns[c].Caption);
                sb.Append(",");
            }
            streamWriter.WriteLine(sb.ToString().TrimEnd(','));

            List<ScanGridInfo> dataSrc = gridCtrl.DataSource as List<ScanGridInfo>;
            int x = 1;
            int count = gv.DataRowCount;
            int bat = 2000;
            sb = new StringBuilder();
            foreach (ScanGridInfo item in dataSrc)
            {
                sb.Append(item.MGRTIndex);
                sb.Append(item.EARFCN);
                sb.Append(",");
                sb.Append(item.PCI);
                sb.Append(",");
                sb.Append(item.R0_RP);
                sb.Append(",");
                sb.Append(item.R0_RQ);
                sb.Append(",");
                sb.Append(item.R0_CINR);
                sb.Append(",");
                sb.Append(item.SSS_RP);
                sb.Append(",");
                sb.Append(item.SSS_RSSI);
                sb.Append(",");
                sb.Append(item.SampleCount);
                sb.Append(Environment.NewLine);
                if (x % bat == 0)
                {
                    WaitTextBox.Text = string.Format("{0}/{1}", x, count);
                    streamWriter.Write(sb.ToString());
                    sb.Remove(0, sb.Length);
                }
                x++;
            }
            if (sb.Length > 0)
            {
                streamWriter.Write(sb.ToString());
            }
            streamWriter.Close();
            fs.Close();
            WaitTextBox.Close();
        }
        #endregion

        private void gv_CustomDrawRowIndicator(object sender, DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventArgs e)
        {
            if (e.Info.IsRowIndicator && e.RowHandle > -1)
            {
                e.Info.DisplayText = (e.RowHandle + 1).ToString();
            }  
        }

        private void cbxMaxRSRP_CheckedChanged(object sender, EventArgs e)
        {
            if (cbxMaxRSRP.Checked)
            {
                if (maxRSRPInfos == null)
                {
                    maxRSRPInfos = ScanGridInfo.ConvergeMaxGridInfo(curInfos);
                }
                changeRenderingData(maxRSRPInfos);
                mf.updateMap();
            }
            else
            {
                changeRenderingData(curInfos);
                mf.updateMap();
            }
        }

        private void changeRenderingData(List<ScanGridInfo> scanGridInfoList)
        {
            layer.GridInfos = scanGridInfoList;
            layer.SelectedGrid = null;
        }

        protected override void MinCloseForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            MainModel.FireSetDefaultMapSerialTheme("");
            layer.GridInfos = null;
            layer.SelectedGrid = null;
            MainModel.MainForm.GetMapForm().updateMap();
            mModel.QuickWindowItemDic.Remove(GetKeyTypeName());
            mModel.Blackboard.Properties.Remove(GetKeyTypeName());
            mModel.MainForm.Activate();
        }

        private void ToolStripExportShp_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveFileDlg = new SaveFileDialog();
            saveFileDlg.Filter = FilterHelper.Shp;
            saveFileDlg.FilterIndex = 1;
            saveFileDlg.RestoreDirectory = true;
            saveFileDlg.Title = "另存为";
            if (saveFileDlg.ShowDialog() == DialogResult.OK)
            {
                string fileName = saveFileDlg.FileName;
                int expRet = layer.MakeShpFile_inject(fileName);
                if (expRet == 0)
                {
                    MessageBox.Show(this, "所选导出图层没有需要导出的数据！");
                }
                else if (expRet == 1)
                {
                    MessageBox.Show(this, "导出成功！");
                }
                else if (expRet == 2)
                {
                    //取消导出
                }
                else
                {
                    MessageBox.Show(this, "导出图层发生错误！");
                }
            }
        }
    }
}
