﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WeakCellConditionDlg : BaseDialog
    {
        public WeakCellConditionDlg()
            : base()
        {
            InitializeComponent();
        }

        public WeakCellConditionDlg(string type)
           : this()
        {
            if (type == "NR")
            {
                label1.Text = "SS-RSRP≤";
                chkSINR.Text = "SS-SINR≤";
            }
        }

        public void SetCondition(WeakLteCellCondition cond)
        {
            numRSRP.Value = (decimal)cond.RsrpMax;
            numSINRMax.Value = (decimal)cond.SinrMax;
            chkSINR.Checked = cond.IsCheckSinr;
            radAndOr.SelectedIndex = cond.SinrIsAnd ? 0 : 1;
        }

        public WeakLteCellCondition GetCondition()
        {
            WeakLteCellCondition cond = new WeakLteCellCondition();
            cond.RsrpMax = (float)numRSRP.Value;
            cond.SinrMax = (float)numSINRMax.Value;
            cond.IsCheckSinr = chkSINR.Checked;
            cond.SinrIsAnd = radAndOr.SelectedIndex == 0;
            return cond;
        }

        private void chkSINR_CheckedChanged(object sender, EventArgs e)
        {
            numSINRMax.Enabled = chkSINR.Checked;
            radAndOr.Enabled = chkSINR.Checked;
        }

    }
    public class WeakLteCellCondition
    {
        public float RsrpMax { get; set; } = -105;
        
        public float SinrMax { get; set; } = 0;

        public bool IsCheckSinr { get; set; }
        
        public bool SinrIsAnd { get; set; } = true;
    }
}
