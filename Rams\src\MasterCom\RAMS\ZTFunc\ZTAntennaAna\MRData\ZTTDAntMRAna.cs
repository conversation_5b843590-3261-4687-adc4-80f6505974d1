﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Collections.ObjectModel;
using System.Drawing.Drawing2D;
using System.Drawing;
using MasterCom.MTGis;
using MapWinGIS;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTTDAntMRAna : ZTAntennaBase
    {
        public ZTTDAntMRAna(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
            this.mainModel = mainModel;
        }

        public override string Name
        {
            get { return "TD MR覆盖分析"; }
        }

        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 28000, 28011, this.Name);
        }

        protected override void doWithDTData(TestPoint tp)
        {
            //
        }

        string strCityName = "";
        readonly AntTimeCfg timeCfg = new AntTimeCfg();
        Dictionary<LaiKey, CellTdPara> cellTdParaDic = new Dictionary<LaiKey, CellTdPara>();
        Dictionary<LaiKey, ZTAntGsmTdCellInfo> antCfgRamsParaDic = new Dictionary<LaiKey, ZTAntGsmTdCellInfo>();

        Dictionary<LaiKey, ZTAntGTMRBaseItem> mrRxcpDic = new Dictionary<LaiKey, ZTAntGTMRBaseItem>();
        Dictionary<LaiKey, ZTAntGTMRBaseItem> mrUeTxPowerDic = new Dictionary<LaiKey, ZTAntGTMRBaseItem>();
        Dictionary<LaiKey, ZTAntGTMRBaseItem> mrTADic = new Dictionary<LaiKey, ZTAntGTMRBaseItem>();
        Dictionary<LaiKey, ZTAntGTMRBaseItem> mrAoaDic = new Dictionary<LaiKey, ZTAntGTMRBaseItem>();
        Dictionary<LaiKey, ZTAntGTMRBaseItem> mrSirDic = new Dictionary<LaiKey, ZTAntGTMRBaseItem>();
        Dictionary<LaiKey, CellTdMRKpi> mrCellTdKpiDic = new Dictionary<LaiKey, CellTdMRKpi>();

        readonly Dictionary<LaiKey, TdCellMRData> tdCellParaDataDic = new Dictionary<LaiKey, TdCellMRData>();

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                if (condition.IsByRound)
                {
                    timeCfg.ISitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0}/{1:00}/01 00:00:00", condition.ByRoundYear, condition.ByRoundRound))) / (1000L));
                    timeCfg.IEitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0}/{1:00}/01 23:59:59", condition.ByRoundYear, condition.ByRoundRound)).AddMonths(1).AddDays(-1)) / (1000L));
                }
                else
                {
                    timeCfg.ISitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0:yyyy-MM-dd} 00:00:00", condition.Periods[0].BeginTime))) / (1000L));
                    timeCfg.IEitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0:yyyy-MM-dd} 23:59:59", condition.Periods[0].EndTime.AddDays(-1)))) / (1000L));
                }
                clearData();
                tdCellParaDataDic.Clear();
                MainModel.ClearDTData();
                WaitBox.CanCancel = true;
                WaitBox.Text = "正在查询...";
                strCityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                WaitBox.Show("1.读取现网天线信息...", doWithAntCfgData);
                WaitBox.Show("2.分类获取MR数据...", doWithCellMRByTypeData);
                WaitBox.Show("3.读取小区性能数据...", doWithCellParaData);
                WaitBox.Show("4.联合数据处理...", AnaCellAngleData);
                dealMainUtranCellSample();
            }
            catch
            {
                clientProxy.Close();
            }
            finally
            {
                clearData();
                ZTLteAntenna.GetInstance().cmDataUnitAreaKPIQueryDic = null;
            }
        }

        private void clearData()
        {
            cellTdParaDic.Clear();
            antCfgRamsParaDic.Clear();

            mrRxcpDic.Clear();
            mrUeTxPowerDic.Clear();
            mrTADic.Clear();
            mrAoaDic.Clear();
            mrSirDic.Clear();
            mrCellTdKpiDic.Clear();
        }

        #region 数据获取过程
        /// <summary>
        /// 获取现网天线信息
        /// </summary>
        private void doWithAntCfgData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 20;
            DiyCfgRamsPara cellPara = new DiyCfgRamsPara(MainModel, "TD");
            cellPara.Query();
            antCfgRamsParaDic = cellPara.antCfgRamsParaDic;
            WaitBox.Close();
        }

        /// <summary>
        /// 查询小区性能参数数据
        /// </summary>
        protected void doWithCellParaData()
        {
            WaitBox.CanCancel = true;
            DiyTDCellPara cellPara = new DiyTDCellPara(MainModel, timeCfg);
            cellPara.SetQueryCondition(condition);
            cellPara.Query();
            cellTdParaDic = cellPara.cellTdParaDic;
            WaitBox.Close();
        }

        /// <summary>
        /// 分类获取MR数据
        /// </summary>
        protected void doWithCellMRByTypeData()
        {
            WaitBox.ProgressPercent = 10;
            doWithMRRscpData();
            WaitBox.ProgressPercent = 20;
            doWithMRTxPowerData();
            WaitBox.ProgressPercent = 40;
            doWithMRTAData();
            WaitBox.ProgressPercent = 60;
            doWithMRAoaData();
            WaitBox.ProgressPercent = 80;
            doWithMRSirDlData();
            WaitBox.ProgressPercent = 90;
            doWithMRKpi();
            WaitBox.ProgressPercent = 100;
            WaitBox.Close();
        }

        /// <summary>
        /// 参考信号接收功率
        /// </summary>
        protected void doWithMRRscpData()
        {
            WaitBox.CanCancel = true;
            DiyTDMRData tdMRData = new DiyTDMRData(MainModel, 1, 55, timeCfg);
            tdMRData.SetQueryCondition(condition);
            tdMRData.Query();
            mrRxcpDic = tdMRData.gtMRDic;
        }

        /// <summary>
        /// UE发射功率
        /// </summary>
        protected void doWithMRTxPowerData()
        {
            WaitBox.CanCancel = true;
            DiyTDMRData tdMRData = new DiyTDMRData(MainModel, 2, 28, timeCfg);
            tdMRData.SetQueryCondition(condition);
            tdMRData.Query();
            mrUeTxPowerDic = tdMRData.gtMRDic;
        }

        /// <summary>
        /// 时间提前量
        /// </summary>
        protected void doWithMRTAData()
        {
            WaitBox.CanCancel = true;
            DiyTDMRData tdMRData = new DiyTDMRData(MainModel, 3, 37, timeCfg);
            tdMRData.SetQueryCondition(condition);
            tdMRData.Query();
            mrTADic = tdMRData.gtMRDic;
        }

        /// <summary>
        /// ENB天线到达角
        /// </summary>
        protected void doWithMRAoaData()
        {
            WaitBox.CanCancel = true;
            DiyTDMRData tdMRData = new DiyTDMRData(MainModel, 4, 72, timeCfg);
            tdMRData.SetQueryCondition(condition);
            tdMRData.Query();
            mrAoaDic = tdMRData.gtMRDic;
        }

        /// <summary>
        /// 上行信噪比
        /// </summary>
        protected void doWithMRSirDlData()
        {
            WaitBox.CanCancel = true;
            DiyTDMRData tdMRData = new DiyTDMRData(MainModel, 5, 64, timeCfg);
            tdMRData.SetQueryCondition(condition);
            tdMRData.Query();
            mrSirDic = tdMRData.gtMRDic;
        }

        /// <summary>
        /// MR指标统计
        /// </summary>
        protected void doWithMRKpi()
        {
            WaitBox.CanCancel = true;
            DiyTDMRKpi tdMRData = new DiyTDMRKpi(MainModel, timeCfg);
            tdMRData.SetQueryCondition(condition);
            tdMRData.Query();
            mrCellTdKpiDic = tdMRData.cellTdMRKpiDic;
        }

        #endregion

        /// <summary>
        /// 分析小区的角度数组，进行平滑化处理
        /// </summary>
        private void AnaCellAngleData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 0;
            int iCount = antCfgRamsParaDic.Count;
            int iNum = 0;

            foreach (LaiKey lai in antCfgRamsParaDic.Keys)
            {
                if (WaitBox.CancelRequest)
                    break;
                WaitBox.ProgressPercent = (int)(100 * ((iNum * 1.0) / iCount));

                ZTAntGsmTdCellInfo antCfgSub = antCfgRamsParaDic[lai];
                if (antCfgSub.基站室分类型 == "室内")
                    continue;
                iNum = addTdCellMRData(iNum, lai, antCfgSub);
            }
            WaitBox.Close();
        }

        private int addTdCellMRData(int iNum, LaiKey lai, ZTAntGsmTdCellInfo antCfgSub)
        {
            TdCellMRData mrData = new TdCellMRData();
            AnaCellMrData(ref mrData, lai);

            mrData.index = ++iNum;
            mrData.iTac = lai.ILac;
            mrData.iEci = lai.ICi;

            mrData.strcityname = strCityName;
            mrData.cellname = antCfgSub.小区名称;
            mrData.iangle_dir = antCfgSub.天线方向角;
            mrData.tdCellInfo = antCfgSub;

            TDCell lteMainCell = CellManager.GetInstance().GetNearestTDCell(DateTime.Now, lai.ILac, lai.ICi, antCfgSub.天线经度, antCfgSub.天线纬度);
            if (lteMainCell != null)
            {
                mrData.dLongitude = lteMainCell.Longitude;
                mrData.dLatitude = lteMainCell.Latitude;
            }
            else
            {
                mrData.dLongitude = antCfgSub.天线经度;
                mrData.dLatitude = antCfgSub.天线纬度;
            }

            if (mrData.AnaRttdAoa90 != null)
            {
                int[,] taAoa90 = mrData.AnaRttdAoa90;
                int[] dirArray = getDirArray(taAoa90);
                mrData.maxTaArray = getDirMaxTa(taAoa90);
                int[] subValue = ZTLteAntMRAna.calcMainRegion(dirArray);
                mrData.iMainDir = subValue[1] == -1 ? -1 : subValue[1] * 5 + 2; //步长为5，除以2
                mrData.iTotalNum = subValue[2];
                mrData.iDirNum = ZTLteAntMRAna.calcDirNum(dirArray);
                List<ZTLteAntMRAna.AntMRInfo> sideList = ZTLteAntMRAna.calcMRInfo(dirArray, mrData.maxTaArray, mrData.iTotalNum);
                mrData.iSideNum = sideList.Count;

                //各区间数据填值
                FillAngleDataByRegion(ref mrData, dirArray);

                LongLat ll = new LongLat();
                ll.fLongitude = (float)(mrData.dLongitude);
                ll.fLatitude = (float)(mrData.dLatitude);
                int iMaxValue = -50;
                int iMinValue = 50;
                ZTAntFuncHelper.getMaxAndMinValue(mrData.maxTaArray, ref iMaxValue, ref iMinValue);
                mrData.mrAoaList = ZTAntFuncHelper.getCellMrCover(ll, mrData.maxTaArray, iMaxValue, iMinValue, 0);

                if (mrData.iMainDir != -1)
                {
                    mrData.strIsMrCell = "是";
                    mrData.iDirDiff = ZTAntFuncHelper.CalcAntDir(mrData.iMainDir, 0);
                    mrData.iMainDirAbs = (mrData.iMainDir + mrData.iangle_dir) % 360;
                }

                分析问题类型(ref mrData);
                if (!tdCellParaDataDic.ContainsKey(lai))
                {
                    tdCellParaDataDic.Add(lai, mrData);
                }
            }

            return iNum;
        }

        protected void AnaCellMrData(ref TdCellMRData cellPara, LaiKey lKey)
        {
            //小区性能数据
            CellTdPara cPara;
            if (!cellTdParaDic.TryGetValue(lKey, out cPara))
            {
                cPara = new CellTdPara();
            }
            cellPara.cPara = cPara;

            //小区MR指标
            CellTdMRKpi mrKpiItem;
            if (!mrCellTdKpiDic.TryGetValue(lKey, out mrKpiItem))
            {
                mrKpiItem = new CellTdMRKpi();
            }
            cellPara.mrData.tdKpiItem = mrKpiItem;

            //小区MR覆盖指标分析统计
            ZTAntGTMRBaseItem tdMRRscpItem;
            if (!mrRxcpDic.TryGetValue(lKey, out tdMRRscpItem))
            {
                tdMRRscpItem = new ZTAntGTMRBaseItem();
            }
            cellPara.mrData.tdMRRscpItem = tdMRRscpItem;

            ZTAntGTMRBaseItem tdMRTxPowerItem;
            if (!mrUeTxPowerDic.TryGetValue(lKey, out tdMRTxPowerItem))
            {
                tdMRTxPowerItem = new ZTAntGTMRBaseItem();
            }
            cellPara.mrData.tdMRTxPowerItem = tdMRTxPowerItem;

            ZTAntGTMRBaseItem tdMRAoaItem;
            if (!mrAoaDic.TryGetValue(lKey, out tdMRAoaItem))
            {
                tdMRAoaItem = new ZTAntGTMRBaseItem();
            }
            cellPara.mrData.tdMRAoaItem = tdMRAoaItem;

            ZTAntGTMRBaseItem tdMRTaItem;
            if (!mrTADic.TryGetValue(lKey, out tdMRTaItem))
            {
                tdMRTaItem = new ZTAntGTMRBaseItem();
            }
            cellPara.mrData.tdMRTaItem = tdMRTaItem;

            ZTAntGTMRBaseItem tdMRSirUlItem;
            if (!mrSirDic.TryGetValue(lKey, out tdMRSirUlItem))
            {
                tdMRSirUlItem = new ZTAntGTMRBaseItem();
            }
            cellPara.mrData.tdMRSirUlItem = tdMRSirUlItem;
        }

        private void FillAngleDataByRegion(ref TdCellMRData mrData, int[] dirArray)
        {
            //主覆盖方向
            mrData.iRel30Num = ZTLteAntMRAna.calcRegionSample(dirArray, 0, 30, 1, 0)
                             + ZTLteAntMRAna.calcRegionSample(dirArray, 0, 30, 2, 0);
            mrData.iRel60Num = ZTLteAntMRAna.calcRegionSample(dirArray, 0, 60, 1, 0)
                             + ZTLteAntMRAna.calcRegionSample(dirArray, 0, 60, 2, 0);
            mrData.iRel90Num = ZTLteAntMRAna.calcRegionSample(dirArray, 0, 90, 1, 0)
                             + ZTLteAntMRAna.calcRegionSample(dirArray, 0, 90, 2, 0);
            mrData.iRelMinus60To120Num = ZTLteAntMRAna.calcRegionSample(dirArray, 0, 60, 1, 60);
            mrData.iRelPlus60To120Num = ZTLteAntMRAna.calcRegionSample(dirArray, 0, 60, 2, 60);
            mrData.iRelBack60Num = ZTLteAntMRAna.calcRegionSample(dirArray, (0 + 180) % 360, 60, 1, 0)
                                 + ZTLteAntMRAna.calcRegionSample(dirArray, (0 + 180) % 360, 60, 2, 0);
            mrData.iRelBack15Num = ZTLteAntMRAna.calcRegionSample(dirArray, (0 + 180) % 360, 15, 1, 0)
                                 + ZTLteAntMRAna.calcRegionSample(dirArray, (0 + 180) % 360, 15, 2, 0);

            ZTLteAntMRAna.AnaRttdAoaArray ary = new ZTLteAntMRAna.AnaRttdAoaArray();
            ary.AnaRttdAoa90 = mrData.AnaRttdAoa90;
            mrData.dAbs30AvgDist = ZTLteAntMRAna.getRegionAvgTa(ary, 0, 30, 3, 0,37);
            mrData.dAbs60AvgDist = ZTLteAntMRAna.getRegionAvgTa(ary, 0, 60, 3, 0, 37);
            mrData.dAbs90AvgDist = ZTLteAntMRAna.getRegionAvgTa(ary, 0, 90, 3, 0, 37);
            mrData.dAbsPlus60To120AvgDist = ZTLteAntMRAna.getRegionAvgTa(ary, 0, 60, 1, 60, 37);
            mrData.dAbsMinus60To120AvgDist = ZTLteAntMRAna.getRegionAvgTa(ary, 0, 60, 2, 60, 37);
            mrData.dAbsBack60AvgDist = ZTLteAntMRAna.getRegionAvgTa(ary, (0 + 180) % 360, 60, 3, 0, 37);
            mrData.dAbsBack15AvgDist = ZTLteAntMRAna.getRegionAvgTa(ary, (0 + 180) % 360, 15, 3, 0, 37);
        }

        #region MR分析问题类型

        private void 分析问题类型(ref TdCellMRData data)
        {
            data.strProbType = "";
            主瓣弱覆盖(ref data);
            主瓣干扰(ref data);
            旁瓣泄露(ref data);
            背瓣覆盖异常(ref data);
            if (data.strProbType != "" && 是否一级告警(data))
            {
                data.strProbType = "一级告警";
            }
        }

        private void 主瓣弱覆盖(ref TdCellMRData data)
        {
            if (data.strRel30Rate != ""  && Convert.ToDouble(data.strRel30Rate.Replace("%","")) >= 20
                && data.strRel60Rate != "" && Convert.ToDouble(data.strRel60Rate.Replace("%", "")) >= 50 && data.dAbs60AvgDist <= 1500
                && (data.mrData.tdKpiItem.F弱覆盖采样点比例 >= 0.2 || data.mrData.tdKpiItem.F下行平均PCCPCH_RSCP <= -90))
            {
                data.strAntResult += "主瓣弱覆盖;";
                data.strProbType = "二级告警";
            }
        }

        private void 主瓣干扰(ref TdCellMRData data)
        {
            if (data.strRel30Rate != "" && Convert.ToDouble(data.strRel30Rate.Replace("%", "")) >= 20
                && data.strRel60Rate != "" && Convert.ToDouble(data.strRel60Rate.Replace("%", "")) >= 50 && data.dAbs60AvgDist <= 1500
                && (data.mrData.tdKpiItem.F下行时隙干扰采样点比例 >= 0.5 || data.mrData.tdKpiItem.F上行时隙干扰采样点比例 >= 0.1
                || data.mrData.tdKpiItem.FUPPTS干扰采样点比例 >= 0.3 || data.mrData.tdKpiItem.FUE高发射功率占比 >= 0.3 || data.mrData.tdKpiItem.FPS业务高误块率采样点占比 >= 0.3))
            {
                data.strAntResult += "主瓣干扰;";
                data.strProbType = "二级告警";
            }
        }

        private void 旁瓣泄露(ref TdCellMRData data)
        {
            if (((data.strRelPlus60To120Rate != "" && Convert.ToDouble(data.strRelPlus60To120Rate.Replace("%", "")) >= 15 && data.dAbsPlus60To120AvgDist >= 1000)
               || (data.strRelMinus60To120Rate != "" && Convert.ToDouble(data.strRelMinus60To120Rate.Replace("%", "")) >= 15 && data.dAbsMinus60To120AvgDist >= 1000))
               && (data.mrData.tdKpiItem.F良好覆盖采样点比例 >= 0.5 || data.mrData.tdKpiItem.F下行平均PCCPCH_RSCP <= -90))
            {
                data.strAntResult += "旁瓣泄露;";
                data.strProbType = "二级告警";
            }
        }

        private void 背瓣覆盖异常(ref TdCellMRData data)
        {
            if (((data.strRelBack15Rate != "" && Convert.ToDouble(data.strRelBack15Rate.Replace("%", "")) >= 10 && data.dAbsBack15AvgDist >= 500)
               || (data.strRelBack60Rate != "" && Convert.ToDouble(data.strRelBack60Rate.Replace("%", "")) >= 30 && data.dAbsBack60AvgDist >= 500))
               && (data.mrData.tdKpiItem.F良好覆盖采样点比例 >= 0.5 || data.mrData.tdKpiItem.F下行平均PCCPCH_RSCP <= -90))
            {
                data.strAntResult += "背瓣覆盖异常;";
                data.strProbType = "二级告警";
            }
        }

        private bool 是否一级告警(TdCellMRData data)
        {
            bool isTopWarn = false;
            if (data.cPara.TD语音业务无线掉话率 >= 0.05 || data.cPara.PS域无线掉线率 >= 0.05 
                || (data.cPara.RRC连接建立成功率 > 0 && data.cPara.RRC连接建立成功率 <= 0.8) || (data.cPara.RAB建立成功率 > 0 && data.cPara.RAB建立成功率 <= 0.8) 
                || (data.cPara.无线接通率 > 0 && data.cPara.无线接通率 <= 0.9)  || (data.cPara.PS域接通率 > 0 && data.cPara.PS域接通率 <= 0.9))
            {
                isTopWarn = true;
            }

            return isTopWarn;
        }

        #endregion

        #region MR覆盖分析判断算法

        /// <summary>
        /// 获取每个角度的采样点数
        /// </summary>
        private int[] getDirArray(int[,] AnaRttdAoa90)
        {
            int[] dirArray = new int[72];
            for (int j = 0; j < 72; j++)
            {
                int iNum = 0;
                for (int i = 0; i < 37; i++)
                {
                    iNum += AnaRttdAoa90[i, j];
                }
                dirArray[j] = iNum;
            }
            return dirArray;
        }

        /// <summary>
        /// 获取每个角度的最大TA值
        /// </summary>
        private static double[] getDirMaxTa(int[,] AnaRttdAoa90)
        {
            int[] dirArray = new int[72];
            for (int j = 0; j < 72; j++)
            {
                int iMaxTa = -1;
                for (int i = 0; i < 37; i++)
                {
                    if (AnaRttdAoa90[i, j] > 0)
                        iMaxTa = i;
                }
                dirArray[j] = iMaxTa;
            }

            double[] dirNewArray = new double[72];
            for (int i = 0; i < 72; i++)
            {
                int iDistF2 = ZTAntFuncHelper.calcDistByTdMrTa(dirArray[(i - 2 + 72) % 72]);
                int iDistF1 = ZTAntFuncHelper.calcDistByTdMrTa(dirArray[(i - 1 + 72) % 72]);
                int iDist = ZTAntFuncHelper.calcDistByTdMrTa(dirArray[i]);
                int iDist1 = ZTAntFuncHelper.calcDistByTdMrTa(dirArray[(i + 1 + 72) % 72]);
                int iDist2 = ZTAntFuncHelper.calcDistByTdMrTa(dirArray[(i + 2 + 72) % 72]);
                dirNewArray[i] = (iDistF2 + iDistF1 + iDist + iDist1 + iDist2)*1.0 / 5;
            }
            return dirNewArray;
        }

        public static double[] GetDirMaxTa(AnaRttdAoa anaRttdAoa)
        {
            return getDirMaxTa(anaRttdAoa.AnaRttdAoa90);
        }

        public class AnaRttdAoa
        {
            public int[,] AnaRttdAoa90 { get; set; }
        }

        #endregion

        ///<summary>
        ///显示结果窗体
        ///</summary>
        private void FireShowResultForm(List<List<NPOIRow>> nrDatasList, List<string> sheetNames)
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LteAntMRForm).FullName);
            TDAntMRAnaForm form = obj == null ? null : obj as TDAntMRAnaForm;
            if (form == null || form.IsDisposed)
            {
                form = new TDAntMRAnaForm(MainModel);
            }
            form.nrDatasList = nrDatasList;
            form.sheetNames = sheetNames;

            form.FillData(tdCellParaDataDic);
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }

        #region 数据整理及导出
        /// <summary>
        /// 按主服导出数据
        /// </summary>
        private void dealMainUtranCellSample()
        {
            List<NPOIRow> data8s = new List<NPOIRow>();
            NPOIRow nr8 = new NPOIRow();
            nr8.cellValues = GetExcelColName();
            data8s.Add(nr8);

            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            List<TdCellMRData> listCellParaData = new List<TdCellMRData>();
            foreach (LaiKey lKey in tdCellParaDataDic.Keys)
            {
                try
                {
                    NPOIRow nr9 = new NPOIRow();
                    nr9.cellValues = FillCellValue(tdCellParaDataDic[lKey]);
                    data8s.Add(nr9);
                    listCellParaData.Add(tdCellParaDataDic[lKey]);
                }
                catch
                {
                    //continue
                }
            }
            nrDatasList.Add(data8s);

            List<string> sheetNames = new List<string>();
            sheetNames.Add("TD MR覆盖分析");
            FireShowResultForm(nrDatasList, sheetNames);
        }

        /// <summary>
        /// 数据表头
        /// </summary>
        private List<object> GetExcelColName()
        {
            List<object> col8s = new List<object>();
            #region 字段定义
            col8s.Add("序号");
            col8s.Add("地市");
            col8s.Add("小区名");
            col8s.Add("LAC");
            col8s.Add("CI");
            col8s.Add("覆盖类型");
            col8s.Add("方位角");
            col8s.Add("下倾角");
            col8s.Add("挂高");

            col8s.Add("分析结果");
            col8s.Add("告警状态");

            col8s.Add("TD语音业务无线掉话率(统计)");
            col8s.Add("PS域无线掉线率(统计)");
            col8s.Add("RRC连接建立成功率(统计)");
            col8s.Add("RAB建立成功率(统计)");
            col8s.Add("无线接通率(统计)");
            col8s.Add("PS域接通率(统计)");
            col8s.Add("上行电路域误块率(统计)");
            col8s.Add("上行分组域误块率(统计)");
            col8s.Add("小区间切换出成功率(统计)");
            col8s.Add("小区间切换入成功率(统计)");

            col8s.Add("弱覆盖采样点比例(统计)");
            col8s.Add("良好覆盖采样点比例(统计)");
            col8s.Add("下行平均PCCPCH_RSCP(统计)");
            col8s.Add("下行时隙干扰采样点比例(统计)");
            col8s.Add("上行时隙干扰采样点比例(统计)");
            col8s.Add("UPPTS干扰采样点比例(统计)");
            col8s.Add("UE高发射功率占比(统计)");
            col8s.Add("PS业务高误块率采样点占比(统计)");

            col8s.Add("方向角正负30°范围打点比例");
            col8s.Add("方向角正负30°覆盖距离均值");
            col8s.Add("方向角正负60°范围打点比例");
            col8s.Add("方向角正负60°覆盖距离均值");
            col8s.Add("方向角正负90°范围打点比例");
            col8s.Add("方向角正负90°覆盖距离均值");
            col8s.Add("方向角正60-120°范围打点比例");
            col8s.Add("方向角正60-120°覆盖距离均值");
            col8s.Add("方向角负60-120°范围打点比例");
            col8s.Add("方向角负60-120°覆盖距离均值");
            col8s.Add("背向正负60°范围打点占比");
            col8s.Add("背向正负60°覆盖距离均值");
            col8s.Add("背向正负15°范围打点占比");
            col8s.Add("背向正负15°覆盖距离均值");

            col8s.Add("MR主覆盖角");
            col8s.Add("方向角偏差度数");
            col8s.Add("2000米外的打点占比");
            col8s.Add("是否可MR波形重构");
            #endregion
            return col8s;
        }

        /// <summary>
        /// 小区级赋值
        /// </summary>
        private List<object> FillCellValue(TdCellMRData data)
        {
            List<object> objs8 = new List<object>();
            objs8.Add(data.index);//序号
            objs8.Add(data.strcityname);//地市
            objs8.Add(data.cellname);//小区名
            objs8.Add(data.tdCellInfo.小区LAC);//小区LAC
            objs8.Add(data.tdCellInfo.小区CI);//小区CI
            objs8.Add(data.tdCellInfo.基站室分类型);//覆盖类型
            objs8.Add(data.tdCellInfo.天线方向角);//方位角
            objs8.Add(data.tdCellInfo.天线下倾角);//下倾角
            objs8.Add(data.tdCellInfo.天线挂高);//挂高

            objs8.Add(data.strAntResult);
            objs8.Add(data.strProbType);

            objs8.Add(data.cPara.STR_TD语音业务无线掉话率);
            objs8.Add(data.cPara.STR_PS域无线掉线率);
            objs8.Add(data.cPara.STR_RRC连接建立成功率);
            objs8.Add(data.cPara.STR_RAB建立成功率);
            objs8.Add(data.cPara.STR_无线接通率);
            objs8.Add(data.cPara.STR_PS域接通率);
            objs8.Add(data.cPara.STR_上行电路域误块率);
            objs8.Add(data.cPara.STR_上行分组域误块率);
            objs8.Add(data.cPara.STR_小区间切换出成功率);
            objs8.Add(data.cPara.STR_小区间切换入成功率);
            
            objs8.Add(data.mrData.tdKpiItem.S弱覆盖采样点比例);
            objs8.Add(data.mrData.tdKpiItem.S良好覆盖采样点比例);
            objs8.Add(data.mrData.tdKpiItem.S下行平均PCCPCH_RSCP);
            objs8.Add(data.mrData.tdKpiItem.S下行时隙干扰采样点比例);
            objs8.Add(data.mrData.tdKpiItem.S上行时隙干扰采样点比例);
            objs8.Add(data.mrData.tdKpiItem.SUPPTS干扰采样点比例);
            objs8.Add(data.mrData.tdKpiItem.SUE高发射功率占比);
            objs8.Add(data.mrData.tdKpiItem.SPS业务高误块率采样点占比);

            objs8.Add(data.strRel30Rate);
            objs8.Add(Math.Round(data.dAbs30AvgDist, 2));
            objs8.Add(data.strRel60Rate);
            objs8.Add(Math.Round(data.dAbs60AvgDist, 2));
            objs8.Add(data.strRel90Rate);
            objs8.Add(Math.Round(data.dAbs90AvgDist, 2));
            objs8.Add(data.strRelPlus60To120Rate);
            objs8.Add(Math.Round(data.dAbsPlus60To120AvgDist, 2));
            objs8.Add(data.strRelMinus60To120Rate);
            objs8.Add(Math.Round(data.dAbsMinus60To120AvgDist, 2));
            objs8.Add(data.strRelBack60Rate);
            objs8.Add(Math.Round(data.dAbsBack60AvgDist, 2));
            objs8.Add(data.strRelBack15Rate);
            objs8.Add(Math.Round(data.dAbsBack15AvgDist, 2));

            objs8.Add(data.iMainDirAbs);
            objs8.Add(data.iDirDiff);
            objs8.Add(data.strOverCoverRate);

            objs8.Add(data.strIsMrCell);

            return objs8;
        }

        #endregion

        public class TdCellMRData : ZTLteAntMRAna.CellMRData
        {
            /// <summary>
            /// 主覆盖方向(绝对值)
            /// </summary>
            public int iMainDirAbs { get; set; }

            public ZTAntGsmTdCellInfo tdCellInfo { get; set; } = new ZTAntGsmTdCellInfo();//现网天线参数
            public CellTdPara cPara { get; set; } = new CellTdPara();//性能数据
            public GTCellMrData mrData { get; set; } = new GTCellMrData();//测量数据

            /// <summary>
            /// 打点的90%
            /// </summary>
            public new int[,] AnaRttdAoa90
            {
                get
                {
                    int[,] rttdAoaItem = new int[37, 72];
                    List<ZTLteAntMRAna.AntMRAoaTa> atList = getAoaTaList();

                    int iNum = (int)(0.9 * atList.Count);
                    for (int i = 0; i < iNum; i++)
                    {
                        ZTLteAntMRAna.AntMRAoaTa at = atList[i];
                        rttdAoaItem[at.iTaId, at.iAoaId] = at.iCount;
                    }
                    return rttdAoaItem;
                }
            }

            /// <summary>
            /// 2000米以外打点占比
            /// </summary>
            public new string strOverCoverRate
            {
                get
                {
                    int[,] taAoa = AnaRttdAoa90;
                    int iNum = 0;
                    int iAllNum = 0;
                    for (int i = 0; i < 37; i++)
                    {
                        for (int j = 0; j < 72; j++)
                        {
                            if (taAoa[i, j] > 0)
                            {
                                iAllNum++;
                                if (i >= 12)
                                    iNum += 1;
                            }
                        }
                    }

                    if (iAllNum == 0)
                        return "0%";

                    return Math.Round(100 * ((float)iNum) / iAllNum, 2) + "%";
                }
            }

            /// <summary>
            /// 打点的90%-分段
            /// </summary>
            public new Dictionary<int, List<ZTLteAntMRAna.AntMRAoaTa>> aoaTaDic
            {
                get
                {
                    List<ZTLteAntMRAna.AntMRAoaTa> atList = getAoaTaList();

                    int iNum = atList.Count;
                    Dictionary<int, List<ZTLteAntMRAna.AntMRAoaTa>> aoaTaDicTmp = new Dictionary<int, List<ZTLteAntMRAna.AntMRAoaTa>>();
                    for (int i = 0; i < iNum; i++)
                    {
                        int iLevel = getMrLevel(iNum, i);
                        if (iLevel == 0)
                            break;

                        List<ZTLteAntMRAna.AntMRAoaTa> tmpList;
                        if (!aoaTaDicTmp.TryGetValue(iLevel, out tmpList))
                            tmpList = new List<ZTLteAntMRAna.AntMRAoaTa>();

                        tmpList.Add(atList[i]);
                        aoaTaDicTmp[iLevel] = tmpList;
                    }

                    return aoaTaDicTmp;
                }
            }

            /// <summary>
            /// 获取MR采样点
            /// </summary>
            private List<ZTLteAntMRAna.AntMRAoaTa> getAoaTaList()
            {
                List<ZTLteAntMRAna.AntMRAoaTa> atList = new List<ZTLteAntMRAna.AntMRAoaTa>();
                int[,] taAoa = mrData.AnaRttdAoa;
                for (int i = 0; i < 37; i++)
                {
                    for (int j = 0; j < 72; j++)
                    {
                        ZTLteAntMRAna.AntMRAoaTa at = new ZTLteAntMRAna.AntMRAoaTa();
                        at.iAoaId = j;
                        at.iTaId = i;
                        if (taAoa[i, j] > 0)
                        {
                            at.iCount = taAoa[i, j];
                            atList.Add(at);
                        }
                    }
                }
                atList.Sort(ZTLteAntMRAna.MRSampleByNum.GetCompareByNum());
                return atList;
            }
        }
    }
}
