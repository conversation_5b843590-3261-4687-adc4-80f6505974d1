﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class HandoverFailInfoForm : MinCloseForm
    {
        public HandoverFailInfoForm(MainModel mm):base(mm)
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void Stat()
        {
            MainModel.DTDataManager.Sort();
            MainModel.SelectedEvents.Clear();
            List<HandoverFailInfo> infoSet = new List<HandoverFailInfo>();
            int sn=1;
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (Event evt in file.Events)
                {
                    evt.Selected = true;
                    MainModel.SelectedEvents.Add(evt);
                    HandoverFailInfo info = new HandoverFailInfo(sn++, evt);
                    infoSet.Add(info);
                }
            }

            gridControl.DataSource = infoSet;
            gridControl.RefreshDataSource();
            gridView.BestFitColumns();
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            HandoverFailInfo info = gridView.GetRow(gridView.FocusedRowHandle) as HandoverFailInfo;
            if (info == null)
            {
                return;
            }
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf==null)
            {
                return;
            }
            mf.GoToView(info.Longitude, info.Latitude, 5000);
        }

        private void miExp2Xls_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(gridView);
        }
    }

    public class HandoverFailInfo
    {
        /// <summary>
        /// 源小区
        /// </summary>
        private readonly LTECell srcCell = null;
        /// <summary>
        /// 目标小区
        /// </summary>
        private readonly LTECell tarCell = null;

        public HandoverFailInfo(int sn, Event evt)
        {
            this.sn = sn;
            this.evt = evt;
            //优先根据TAC CI匹配
            srcCell = evt.GetSrcCell() as LTECell;
            tarCell = CellManager.GetInstance()
                .GetNearestLTECellByEARFCNPCI(evt.DateTime
                    , (int?)(long?)TarEARFCN, (int?)(long?)TarPCI, evt.Longitude, evt.Latitude);
        }

        public string EventName
        {
            get { return evt.Name; }
        }

        private readonly int sn = 0;
        public int SN
        {
            get { return sn; }
        }

        private readonly Event evt;
        public Event Event
        {
            get { return evt; }
        }

        public DateTime Time
        {
            get { return evt.DateTime; }
        }

        public double Longitude
        {
            get { return evt.Longitude; }
        }

        public double Latitude
        {
            get { return evt.Latitude; }
        }

        public object SrcTAC
        {
            get { return evt["LAC"]; }
        }
        public object SrcECI
        {
            get { return evt["CI"]; }
        }

        public object SrcCellID
        {
            get { return srcCell == null ? null : srcCell.SCellID.ToString(); }
        }

        public object SrcEARFCN
        {
            get { return evt["Value2"]; }
        }

        public object SrcPCI
        {
            get { return evt["Value3"]; }
        }

        public string SrcCellName
        {
            get {
                if (srcCell!=null)
                {
                    return srcCell.Name;
                }
                return string.Empty;
            }
        }

        public object TarEARFCN
        {
            get { return evt["Value4"]; }
        }

        public object TarPCI
        {
            get
            {
              return evt["Value5"];
            }
        }

        public object TarCellID
        {
            get { return tarCell == null ? null : tarCell.SCellID.ToString(); }
        }

        public string TarCellName
        {
            get {
                if (tarCell!=null)
                {
                    return tarCell.Name;
                }
                return string.Empty;
            }
        }

        public object TarTAC
        {
            get
            {
                if (tarCell!=null)
                {
                    return tarCell.TAC;
                }
                return null;
            }
        }

        public object TarECI
        {
            get
            {
                if (tarCell != null)
                {
                    return tarCell.ECI;
                }
                return null;
            }
        }

        public string FileName
        {
            get { return evt.FileName; }
        }

        public BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.LongitudeMid = Longitude;
            bgResult.LatitudeMid = Latitude;

            StringBuilder strb = new StringBuilder();
            strb.Append("事件：" + EventName + "\r\n");
            strb.Append("时间：" + Time + "\r\n");
            strb.Append("经度：" + Longitude + "\r\n");
            strb.Append("纬度：" + Latitude + "\r\n");
            strb.Append("切换前TAC：" + SrcTAC + "\r\n");
            strb.Append("切换前ECI：" + SrcECI + "\r\n");
            strb.Append("切换前CellID：" + SrcCellID + "\r\n");
            strb.Append("切换前EARFCN：" + SrcEARFCN + "\r\n");
            strb.Append("切换前PCI：" + SrcPCI + "\r\n");
            strb.Append("切换前小区：" + SrcCellName + "\r\n");
            strb.Append("切换后EARFCN：" + TarEARFCN + "\r\n");
            strb.Append("切换后PCI：" + TarPCI + "\r\n");
            strb.Append("切换后小区：" + TarCellName + "\r\n");
            strb.Append("切换后TAC：" + TarTAC + "\r\n");
            strb.Append("切换后ECI：" + TarECI + "\r\n");
            strb.Append("切换后CellID：" + TarCellID + "\r\n");
            strb.Append("文件名：" + FileName);
            bgResult.ImageDesc = strb.ToString();
            return bgResult;
        }
    }

}
