﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Imaging;

using MasterCom.RAMS.Model;

using Excel = Microsoft.Office.Interop.Excel;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class LteStationAcceptBase
    {
        public virtual int MaxCellCount { get { return LteStationAcceptManager.TddMaxCellCount; }  } 

        public virtual void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName, List<LTECell> tddCells)
        {
        }

        public virtual void FillResult(string btsName, Excel.Workbook eBook)
        {
            FillResultToSheet(btsName, eBook, 2);
        }

        public virtual bool IsValidFile(FileInfo fileInfo)
        {
            return false;
        }

        public virtual void Clear()
        {
            btsResultDic.Clear();
        }

        public int GetCellCount(string btsName)
        {
            return btsResultDic.ContainsKey(btsName) ? btsResultDic[btsName].CellCount : 0;
        }

        public List<string> BtsNames
        {
            get
            {
                return new List<string>(btsResultDic.Keys);
            }
        }

        protected virtual void FillResultToSheet(string btsName, Excel.Workbook eBook, int sheetIndex)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteStationAcceptResult result = btsResultDic[btsName];
            List<int> cellIDs = result.CellIDs;

            for (int i = 0; i < cellIDs.Count; ++i)
            {
                int cellId = cellIDs[i];
                if (!cellIDMap.TryGetValue(cellId, out var index))
                {
                    continue;
                }

                for (int row = 0; row < resultGrid.GetLength(1); ++row)
                {
                    for (int col = 0; col < resultGrid.GetLength(2); ++col)
                    {
                        object value = result.GetValue(cellId, row, col);
                        InsertExcelValue(eBook, sheetIndex, resultGrid[index, row, col], value);
                    }
                }
            }
        }

        protected void InsertExcelValue(Excel.Workbook eBook, int sheetIndex, string cell, object value)
        {
            if (value == null)
            {
                return;
            }
            if (value is double && (double)value == double.MinValue)
            {
                return;
            }

            Excel.Worksheet eSheet = (Excel.Worksheet)eBook.Sheets[sheetIndex];
            Excel.Range rng = eSheet.get_Range(cell, Type.Missing);
            rng.set_Value(Type.Missing, value);
        }

        protected Dictionary<string, LteStationAcceptResult> btsResultDic = new Dictionary<string, LteStationAcceptResult>();

        protected string[,,] resultGrid = null; // 小区个数，行数，列数

        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        protected Dictionary<int, int> cellIDMap = null;

        protected virtual void SetCellIDMap(ICell cell, List<LTECell> tddCells)
        {
            LTEBTS bts = null;
            if (cell != null)
            {
                bts = (cell as LTECell).BelongBTS;
            }
            if (bts == null)
            {
                cellIDMap = new Dictionary<int, int>();
                cellIDMap[0] = 0;
            }
            else
            {
                cellIDMap = new Dictionary<int, int>();
                for (int i = 0; i < tddCells.Count; i++)
                {
                    cellIDMap[tddCells[i].CellID] = i;
                }
            }
        }
    }

    // 从极好点文件中分析
    class AcpFtpDownload : LteStationAcceptBase
    {
        public AcpFtpDownload()
        {
            resultGrid = new string[MaxCellCount, 3, 1];
            int idx = 15;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + step * i;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 1, 0] = "p" + (++row).ToString();
                resultGrid[i, 2, 0] = "p" + (++row).ToString();
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName, List<LTECell> tddCells)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }

            SetCellIDMap(targetCell, tddCells);

            CellKPI kpiCell = AnaFile(fileInfo, fileManager, targetCell);
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }
            kpiCell.CalcResult();

            int colIndex = GetColumnIndex(fileInfo);
            if (colIndex == -1)
            {
                log.Info(string.Format("文件{0}未发现极好点关键字", fileInfo.Name));
                return;
            }
            SaveResult(fileInfo, kpiCell, colIndex, btsName);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("点下载");
        }

        protected virtual double GetSpeed(CellKPI kpiCell)
        {
            return kpiCell.AvgDLSpeed;
        }

        protected void SaveResult(FileInfo fileInfo, CellKPI kpiCell, int colIndex, string btsName)
        {
            int cellID = kpiCell.LteCell.CellID;
            LTEBTS bts = kpiCell.LteCell.BelongBTS;
            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(bts.ID, btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            SetValue(result, cellID, 0, colIndex, kpiCell.AvgRsrp);
            SetValue(result, cellID, 1, colIndex, kpiCell.AvgSinr);
            SetValue(result, cellID, 2, colIndex, GetSpeed(kpiCell));
        }

        protected CellKPI AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell)
        {
            CellKPI targetKpiCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                ICell iCell = tp.GetMainCell();
                if (iCell == null || iCell != targetCell)
                {
                    continue;
                }

                if (targetKpiCell == null)
                {
                    targetKpiCell = new CellKPI((LTECell)targetCell);
                }
                targetKpiCell.AddPoint(tp);
            }
            return targetKpiCell;
        }

        protected int GetColumnIndex(FileInfo fileInfo)
        {
            if (fileInfo.Name.Contains("极好点"))
            {
                return 0;
            }
            return -1;
        }

        protected void SetValue(LteStationAcceptResult result, int cellID, int rowIdx, int colIdx, double value)
        {
            // 直接覆盖原有的值
            result.SetValue(cellID, rowIdx, colIdx, value);
        }

        protected class CellKPI
        {
            public CellKPI(LTECell lteCell)
            {
                this.LteCell = lteCell;
            }

            public LTECell LteCell
            {
                get;
                private set;
            }

            public int PointCount
            {
                get;
                private set;
            }

            public double AvgRsrp
            {
                get;
                private set;
            }

            public double AvgSinr
            {
                get;
                private set;
            }

            public double AvgULSpeed
            {
                get;
                private set;
            }

            public double AvgDLSpeed
            {
                get;
                private set;
            }

            public void AddPoint(TestPoint tp)
            {
                ++PointCount;

                float? rsrp;
                float? sinr;
                double? ulSpeed;
                double? dlSpeed;

                rsrp = (float?)tp["lte_RSRP"];
                sinr = (float?)tp["lte_SINR"];
                ulSpeed = (double?)tp["lte_PDCP_UL_Mb"];
                dlSpeed = (double?)tp["lte_PDCP_DL_Mb"];

                if (rsrp != null && rsrp != -10000000)
                {
                    ++cntRsrp;
                    sumRsrp += (float)rsrp;
                }
                if (sinr != null && sinr != -10000000)
                {
                    ++cntSinr;
                    sumSinr += (float)sinr;
                }
                //空闲态数据
                if (ulSpeed != null)
                {
                    ++cntULSpeed;
                    sumULSpeed += (double)ulSpeed;
                }
                //空闲态数据
                if (dlSpeed != null)
                {
                    ++cntDLSpeed;
                    sumDLSpeed += (double)dlSpeed;
                }
            }

            public void CalcResult()
            {
                AvgRsrp = cntRsrp == 0 ? double.MinValue : Math.Round(sumRsrp / cntRsrp, 2);
                AvgSinr = cntSinr == 0 ? double.MinValue : Math.Round(sumSinr / cntSinr, 2);
                AvgULSpeed = cntULSpeed == 0 ? double.MinValue : Math.Round(sumULSpeed / cntULSpeed, 2);
                AvgDLSpeed = cntDLSpeed == 0 ? double.MinValue : Math.Round(sumDLSpeed / cntDLSpeed, 2);
            }

            private double sumRsrp;
            private int cntRsrp;

            private double sumSinr;
            private int cntSinr;

            private double sumULSpeed;
            private int cntULSpeed;

            private double sumDLSpeed;
            private int cntDLSpeed;
        }
    }

    // 从dt文件中按sinr和吞吐率将采样点分好中差进行统计
    class AcpFtpDownloadEx : AcpFtpDownload
    {
        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName, List<LTECell> tddCells)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }
            SetCellIDMap(targetCell, tddCells);

            CellKPI[] kpiCells = AnaFile(fileManager, targetCell);
            if (kpiCells == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }
            foreach (CellKPI c in kpiCells)
            {
                c.CalcResult();
            }

            SaveResult(fileInfo, kpiCells[0], 0, btsName);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToUpper().Contains("DT下载");
        }

        private CellKPI[] AnaFile(DTFileDataManager fileManager, ICell targetCell)
        {
            CellKPI[] cells = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                ICell iCell = tp.GetMainCell();
                if (iCell == null || iCell != targetCell)
                {
                    continue;
                }

                if (cells == null)
                {
                    cells = new CellKPI[1]
                    {
                        new CellKPI(iCell as LTECell),
                    };
                }

                int idx = JudgeQuality(tp);
                if (idx < 0) // 无效点
                {
                    continue;
                }
                cells[idx].AddPoint(tp);
            }
            return cells;
        }

        protected virtual int JudgeQuality(TestPoint tp)
        {
            float? sinr = (float?)tp["lte_SINR"];
            if (sinr == null || sinr == -10000000)
            {
                return -1;
            }

            float? rsrp = (float?)tp["lte_RSRP"];
            if (rsrp == null || rsrp == -10000000)
            {
                return -1;
            }

            double? dlSpeed = (double?)tp["lte_PDCP_DL_Mb"];
            if (dlSpeed == null)
            {
                return -1;
            }

            if (22 <= sinr && dlSpeed > 50)
            {
                return 0; // 极好点
            }

            /*
             * 极好点： RS-SINR ≥22dB，下行吞吐率>50Mbps
             */
            return -1;
        }
    }

    class AcpFtpUpload : AcpFtpDownload
    {
        public AcpFtpUpload()
        {
            resultGrid = new string[MaxCellCount, 3, 1];
            int idx = 18;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + step * i;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 1, 0] = "p" + (++row).ToString();
                resultGrid[i, 2, 0] = "p" + (++row).ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("点上传");
        }

        protected override double GetSpeed(CellKPI kpiCell)
        {
            return kpiCell.AvgULSpeed;
        }
    }

    class AcpFtpUploadEx : AcpFtpDownloadEx
    {
        public AcpFtpUploadEx()
        {
            resultGrid = new string[MaxCellCount, 3, 1];
            int idx = 18;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + step * i;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 1, 0] = "p" + (++row).ToString();
                resultGrid[i, 2, 0] = "p" + (++row).ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToUpper().Contains("DT上传");
        }

        protected override int JudgeQuality(TestPoint tp)
        {
            float? sinr = (float?)tp["lte_SINR"];
            if (sinr == null || sinr == -10000000)
            {
                return -1;
            }

            float? rsrp = (float?)tp["lte_RSRP"];
            if (rsrp == null || rsrp == -10000000)
            {
                return -1;
            }

            double? ulSpeed = (double?)tp["lte_PDCP_UL_Mb"];
            if (ulSpeed == null)
            {
                return -1;
            }

            if (22 <= sinr && ulSpeed > 7.5)
            {
                return 0; // 极好点
            }

            /*
             * 极好点：RS-SINR ≥22dB，上行吞吐率>7.5Mbps
             */
            return -1;
        }

        protected override double GetSpeed(CellKPI kpiCell)
        {
            return kpiCell.AvgULSpeed;
        }
    }

    class AcpInnerHandover : LteStationAcceptBase
    {
        public AcpInnerHandover()
        {
            evtRequList = new List<int> { 850, 898 };
            evtSuccList = new List<int> { 851, 899 };

            resultGrid = new string[1, 1, 2];
        }
        public virtual void ReSetResultGridValue(int cellCount)
        {
            int valueRowIndex;
            if (cellCount <= 3)
            {
                valueRowIndex = 58;
            }
            else if (cellCount <= 6)
            {
                valueRowIndex = 111;
            }
            else
            {
                valueRowIndex = 165;
            }

            resultGrid = new string[1, 1, 2] {
                    {
                        { "p" + valueRowIndex, "w" + valueRowIndex },
                    },
                };
        }

        protected CellKPI kpiCell = null;

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName, List<LTECell> tddCells)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }
            SetCellIDMap(null, tddCells);

            CellKPI curKpiCell = AnaFile(fileInfo, fileManager, targetCell);
            if (curKpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }
            SaveResult(fileInfo, curKpiCell, 0, btsName);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("系统内切换");
        }

        protected virtual CellKPI AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell)
        {
            if (kpiCell == null || kpiCell.LteCell != (targetCell as LTECell))
            {
                kpiCell = new CellKPI(targetCell as LTECell);
            }

            foreach (Event evt in fileManager.Events)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    ++kpiCell.RequestCnt;
                }
                if (evtSuccList.Contains(evt.ID))
                {
                    ++kpiCell.SucceedCnt;
                }
            }
            return kpiCell;
        }

        protected virtual void SaveResult(FileInfo fileInfo, CellKPI kpiCell, int cellID, string btsName)
        {
            int rowIdx = 0;
            LTEBTS bts = kpiCell.LteCell.BelongBTS;
            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(bts.ID, btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            result.SetValue(cellID, rowIdx, 0, kpiCell.RequestCnt);
            result.SetValue(cellID, rowIdx, 1, kpiCell.SucceedCnt);

            // 失败次数用Excel里面的公式计算
        }

        protected class CellKPI
        {
            public LTECell LteCell
            {
                get;
                protected set;
            }

            public int RequestCnt
            {
                get;
                set;
            }

            public int SucceedCnt
            {
                get;
                set;
            }

            public int FailedCnt
            {
                get;
                set;
            }

            public CellKPI(LTECell lteCell)
            {
                this.LteCell = lteCell;
            }
        }

        protected List<int> evtSuccList;
        protected List<int> evtRequList;
    }

    class AcpHandoverPic : AcpCoverPicture
    {
        public AcpHandoverPic()
        {
            resultGrid = new string[MaxCellCount, 1, 2];
            int idx = 103;
            int step = 115;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "a" + row.ToString();
                resultGrid[i, 0, 1] = "s" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("切换");
        }

        protected override void AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName, List<LTECell> tddCells)
        {
            LTECell lteCell = targetCell as LTECell;
            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(lteCell.BelongBTS.ID, btsName,
                    resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            //根据基站小区PCI重置PCI对应的图例
            reSetPCIMapView(tddCells);

            AcpAutoCoverPicture picAnaFunc = AcpAutoCoverPicture.Instance;
            foreach (LTECell btsLteCell in tddCells)
            {
                MasterCom.MTGis.DbRect bounds = picAnaFunc.GetCoverBounds(fileManager, btsLteCell);
                string picPath = picAnaFunc.FireMapAndTakePicByFunc("Handover", "PCI", bounds, btsLteCell.BTSName, btsLteCell.Name);

                int cellID = btsLteCell.CellID;
                result.SetValue(cellID, 0, 0, picPath);
            }
            MainModel.GetInstance().DrawDifferentServerColor = false;
        }



        /// <summary>
        /// 重置PCI图例
        /// </summary>
        /// <param name="bts"></param>
        protected virtual void reSetPCIMapView(List<LTECell> tddCells)
        {
            MapSerialInfo msi = DTLayerSerialManager.Instance.GetSerialByName("lte_PCI");
            if (msi == null)
            {
                return;
            }

            List<int> pciList;
            pciList = getPCIList(tddCells);

            List<RangeInfo> ranges;
            ranges = getRangesList(pciList);

            changePCISerial(msi, ranges);

            setServerCellColorByPCI(tddCells, ranges);
        }

        /// <summary>
        /// 重设pci图例,由于每个小区的PCI不同,图例需要根据pci进行变化
        /// </summary>
        /// <param name="msi"></param>
        /// <param name="ranges"></param>
        private void changePCISerial(MapSerialInfo msi, List<RangeInfo> ranges)
        {
            msi.ColorDisplayParam.Info.RangeColors = new List<DTParameterRangeColor>();
            foreach (RangeInfo range in ranges)
            {
                DTParameterRangeColor paramColor = new DTParameterRangeColor(range.Min, range.Max, range.RangeColor);
                paramColor.MaxIncluded = range.InculdeMax;
                paramColor.MinIncluded = range.InculdeMin;
                msi.ColorDisplayParam.Info.RangeColors.Add(paramColor);
            }
        }

        private void setServerCellColorByPCI(List<LTECell> tddCells, List<RangeInfo> ranges)
        {
            foreach (LTECell btsLteCell in tddCells)
            {
                foreach (RangeInfo range in ranges)
                {
                    if (btsLteCell.PCI >= range.Min && btsLteCell.PCI < range.Max)
                    {
                        btsLteCell.ServerCellColor = range.RangeColor;
                        break;
                    }
                }
            }
        }

        private List<int> getPCIList(List<LTECell> tddCells)
        {
            List<int> pciList = new List<int>();
            foreach (LTECell btsLteCell in tddCells)
            {
                    pciList.Add(btsLteCell.PCI);
            }
            return pciList;
        }

        private List<RangeInfo> getRangesList(List<int> pciList)
        {
            List<RangeInfo> ranges = new List<RangeInfo>();
            List<Color> colorList = new List<Color> 
            { 
                Color.Aqua, 
                Color.Green, 
                Color.Blue, 
                Color.Yellow, 
                Color.Fuchsia, 
                Color.Bisque, 
                Color.DarkOrange, 
                Color.Silver, 
                Color.LightGreen,
                Color.Pink,
                Color.Purple,
                Color.LimeGreen
            };
            for (int i = 0; i < pciList.Count; i++)
            {
                ranges.Add(new RangeInfo(true, false, pciList[i], pciList[i] + 1, colorList[i]));
            }
            return ranges;
        }

        protected class RangeInfo
        {
            public RangeInfo(bool inculdeMin, bool inculdeMax, float min, float max, Color rangeColor)
            {
                Min = min;
                InculdeMin = inculdeMin;
                Max = max;
                InculdeMax = inculdeMax;
                RangeColor = rangeColor;
            }

            public float Min
            {
                get;
                private set;
            }
            public bool InculdeMin
            {
                get;
                private set;
            }
            public float Max
            {
                get;
                private set;
            }
            public bool InculdeMax
            {
                get;
                private set;
            }

            public Color RangeColor
            {
                get;
                private set;
            }
        }
    }





    class AcpCsfbRate : AcpRrcRate
    {
        public AcpCsfbRate()
        {
            resultGrid = new string[MaxCellCount, 1, 2];
            int idx = 11;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + step * i;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName, List<LTECell> tddCells)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }
            SetCellIDMap(targetCell, tddCells);

            AcpAutoCsfbRate.GetCsfbEventIdsByDevice(fileInfo.DeviceType, out evtRequList, out evtSuccList);

            CellKPI kpiCell = AnaFile(fileInfo, fileManager, targetCell);
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }
            kpiCell.FailedCnt = kpiCell.RequestCnt - kpiCell.SucceedCnt;
            SaveResult(fileInfo, kpiCell, kpiCell.LteCell.CellID, btsName);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            if (fileInfo.Momt != (int)MoMtFile.MoFlag)
            {
                return false;
            }
            //return fileInfo.Name.IndexOf("csfb", StringComparison.CurrentCultureIgnoreCase) != -1;
            return (fileInfo.Name.ToUpper().Contains("CSFB") && fileInfo.Name.Contains("主叫"));
        }

    }

    class AcpRrcRate : AcpInnerHandover
    {
        public AcpRrcRate()
        {
            evtRequList = new List<int>() { 855 };
            evtSuccList = new List<int>() { 856 };

            resultGrid = new string[MaxCellCount, 1, 2];
            int idx = 5;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + step * i;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName, List<LTECell> tddCells)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }
            SetCellIDMap(targetCell, tddCells);

            CellKPI kpiCell = AnaFile(fileInfo, fileManager, targetCell);
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }
            SaveResult(fileInfo, kpiCell, kpiCell.LteCell.CellID, btsName);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("接入");
        }
        public override void ReSetResultGridValue(int cellCount)
        {
            //
        }
    }

    class AcpErabRate : AcpRrcRate
    {
        public AcpErabRate()
        {
            evtRequList = new List<int>() { 858 };
            evtSuccList = new List<int>() { 859 };

            resultGrid = new string[MaxCellCount, 1, 2];
            int idx = 6;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + step * i;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }
    }

    class AcpAccRate : AcpRrcRate
    {
        public AcpAccRate()
        {
            evtRequList = new List<int>() { 22 };
            evtSuccList = new List<int>() { 23 };

            resultGrid = new string[MaxCellCount, 1, 2];
            int idx = 7;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + step * i;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }
    }

    class Acp34ReselectRate : AcpRrcRate
    {
        public Acp34ReselectRate()
        {
            evtRequList = new List<int>() { 852 };
            evtSuccList = new List<int>() { 853 };

            resultGrid = new string[MaxCellCount, 1, 2];
            int idx = 8;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + step * i;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName, List<LTECell> tddCells)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }
            SetCellIDMap(targetCell, tddCells);

            SwitchEventIDs(fileInfo.DeviceType);

            CellKPI kpiCell = AnaFile(fileInfo, fileManager, targetCell);
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }
            SaveResult(fileInfo, kpiCell, kpiCell.LteCell.CellID, btsName);
        }

        protected virtual void SwitchEventIDs(int deviceType)
        {
            if (deviceType == 21 || deviceType == 8) // 中兴
            {
                evtRequList = new List<int>() { 852 };
                evtSuccList = new List<int>() { 853 };
            }
            else
            {
                evtRequList = new List<int>() { 1308 };
                evtSuccList = new List<int>() { 1308 };
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToUpper().Contains("34G重选");
        }
    }

    class Acp24ReselectRate : AcpRrcRate
    {
        public Acp24ReselectRate()
        {
            evtRequList = new List<int>() { 852 };
            evtSuccList = new List<int>() { 853 };

            resultGrid = new string[MaxCellCount, 1, 2];
            int idx = 9;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + step * i;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName, List<LTECell> tddCells)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }
            SetCellIDMap(targetCell, tddCells);

            SwitchEventIDs(fileInfo.DeviceType);

            CellKPI kpiCell = AnaFile(fileInfo, fileManager, targetCell);
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }
            SaveResult(fileInfo, kpiCell, kpiCell.LteCell.CellID, btsName);
        }

        protected virtual void SwitchEventIDs(int deviceType)
        {
            if (deviceType == 21 || deviceType == 8) // 中兴
            {
                evtRequList = new List<int>() { 852 };
                evtSuccList = new List<int>() { 853 };
            }
            else
            {
                evtRequList = new List<int>() { 1306 };
                evtSuccList = new List<int>() { 1306 };
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToUpper().Contains("24G重选");
        }
    }

    class AcpCoverPicture : LteStationAcceptBase
    {
        public AcpCoverPicture()
        {
            resultGrid = new string[MaxCellCount, 4, 2];
            int idx = 11;
            int step0 = 115;
            int step1 = 23;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row0 = idx + i * step0;
                for (int j = 0; j < 4; j++)
                {
                    int row1 = row0 + j * step1;
                    resultGrid[i, j, 0] = "a" + row1.ToString();
                    resultGrid[i, j, 1] = "s" + row1.ToString();
                }
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName, List<LTECell> tddCells)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }
            SetCellIDMap(targetCell, tddCells);

            AnaFile(fileInfo, fileManager, targetCell, btsName, tddCells);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToUpper().Contains("DT上传") || fileInfo.Name.ToUpper().Contains("DT下载");
        }

        public override void FillResult(string btsName, Excel.Workbook eBook)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteStationAcceptResult result = btsResultDic[btsName];

            foreach (var item in cellIDMap)
            {
                //int sid = cellIDs[i];
                for (int row = 0; row < resultGrid.GetLength(1); ++row)
                {
                    string picPath = result.GetValue(item.Key, row, 0) as string;
                    if (picPath != null)
                    {
                        AcpAutoCoverPicture.InsertExcelPicture(eBook, resultGrid[item.Value, row, 0], picPath);
                        string tpDes = result.GetValue(item.Key, row, 1) as string;
                        InsertExcelValue(eBook, 3, resultGrid[item.Value, row, 1], tpDes);
                    }
                }
            }
        }

        public override void Clear()
        {
            foreach (LteStationAcceptResult result in btsResultDic.Values)
            {
                string folderPath = AcpAutoCoverPicture.Instance.GetBtsPicFolder(result.BtsName);
                if (System.IO.Directory.Exists(folderPath))
                {
                    System.IO.Directory.Delete(folderPath, true);
                }
            }
            btsResultDic.Clear();
        }

        protected virtual void AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName, List<LTECell> tddCells)
        {
            LTECell lteCell = targetCell as LTECell;
            int cellID = lteCell.CellID;
            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(lteCell.BelongBTS.ID, btsName,
                    resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            AcpAutoCoverPicture picAnaFunc = AcpAutoCoverPicture.Instance;
            MasterCom.MTGis.DbRect bounds = picAnaFunc.GetCoverBounds(fileManager, lteCell);
            double nearestDistance;
            TestPoint nearestTp = picAnaFunc.GetNearestTp(fileManager.TestPoints, lteCell, out nearestDistance);
            string tpDes;
            tpDes = picAnaFunc.GetTpDes(nearestTp, nearestDistance);

            string picPath = null;
            if (fileInfo.Name.ToUpper().Contains("DT上传"))
            {
                picPath = picAnaFunc.FireMapAndTakePic("PDCP_UL_Mb", bounds, nearestTp, lteCell);
                result.SetValue(cellID, 3, 0, picPath);
                result.SetValue(cellID, 3, 1, tpDes);
            }
            else
            {
                picPath = picAnaFunc.FireMapAndTakePic("RSRP", bounds, nearestTp, lteCell);
                result.SetValue(cellID, 0, 0, picPath);
                result.SetValue(cellID, 0, 1, tpDes);

                picPath = picAnaFunc.FireMapAndTakePic("SINR", bounds, nearestTp, lteCell);
                result.SetValue(cellID, 1, 0, picPath);
                result.SetValue(cellID, 1, 1, tpDes);

                picPath = picAnaFunc.FireMapAndTakePic("PDCP_DL_Mb", bounds, nearestTp, lteCell);
                result.SetValue(cellID, 2, 0, picPath);
                result.SetValue(cellID, 2, 1, tpDes);
            }
        }
    }

    class AcpTitle : LteStationAcceptBase
    {
        public AcpTitle()
        {
            resultGrid = new string[1, 1, 2] {
                {
                    { "c2", "m2" },
                },
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName, List<LTECell> tddCells)
        {
            if (!IsValidFile(fileInfo))
            {
                return;
            }
            SetCellIDMap(null, tddCells);

            LTEBTS lteBts = (targetCell as LTECell).BelongBTS;
            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(lteBts.ID, btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            result.SetValue(this.cellID, 0, 0, btsName);
            result.SetValue(this.cellID, 0, 1, lteBts.BTSID);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return true;
        }

        protected int cellID = 0;
    }

    class AcpBtsInfo : LteStationAcceptBase
    {
        BtsParameterInfo btsParameters;
        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName, List<LTECell> tddCells)
        {
            if (btsParameters != null)
            {
                return;
            }
            btsParameters = new BtsParameterInfo();
            LTEBTS lteBts = (targetCell as LTECell).BelongBTS;
            btsParameters.BtsName = btsName;
            btsParameters.BtsType = lteBts.TypeStringDesc;

            addAntennaPlatform(btsParameters);

            addAssetManager(btsParameters);

            addNetworkConfig(btsParameters);

            addAuditData(btsParameters);

            addWireless(btsParameters);
        }

        public override void FillResult(string btsName, Excel.Workbook eBook)
        {
            Excel.Worksheet sheet = (Excel.Worksheet)eBook.Sheets[1];
            sheet.get_Range("e3").set_Value(Type.Missing, btsName);
            sheet.get_Range("z3").set_Value(Type.Missing, DateTime.Now.ToString("yyyy-MM-dd"));
            if (btsParameters != null)
            {
                sheet.get_Range("e5").set_Value(Type.Missing, btsParameters.NodeBID);
                sheet.get_Range("z5").set_Value(Type.Missing, btsParameters.Country);
                sheet.get_Range("e7").set_Value(Type.Missing, btsParameters.Address);
                sheet.get_Range("z7").set_Value(Type.Missing, btsParameters.BtsType);

                setCellValue(sheet.Cells, 13, 8, btsParameters.BtsLongutide.Planing, btsParameters.BtsLongutide.IsValid);
                setCellValue(sheet.Cells, 13, 14, btsParameters.BtsLongutide.Real, btsParameters.BtsLatitude.IsValid);

                setCellValue(sheet.Cells, 14, 8, btsParameters.BtsLongutide.Planing, btsParameters.BtsLongutide.IsValid);
                setCellValue(sheet.Cells, 14, 14, btsParameters.BtsLongutide.Real, btsParameters.BtsLatitude.IsValid);

                setCellValue(sheet.Cells, 15, 8, btsParameters.Bandwidth.Planing, btsParameters.BtsLongutide.IsValid);
                setCellValue(sheet.Cells, 15, 14, btsParameters.Bandwidth.Real, btsParameters.BtsLatitude.IsValid);

                setCellValue(sheet.Cells, 16, 8, btsParameters.ServiceIP.Planing, btsParameters.BtsLongutide.IsValid);
                setCellValue(sheet.Cells, 16, 14, btsParameters.ServiceIP.Real, btsParameters.BtsLatitude.IsValid);

                setCellValue(sheet.Cells, 17, 8, btsParameters.ManageIP.Planing, btsParameters.BtsLongutide.IsValid);
                setCellValue(sheet.Cells, 17, 14, btsParameters.ManageIP.Real, btsParameters.BtsLatitude.IsValid);

                int cellIdx = 0;
                int rowIdx = 18;
                int colIdx = 8;
                int colInterval = 8;
                foreach (var cellInfo in btsParameters.CellSortList)
                {
                    colIdx = colIdx + colInterval * cellIdx;
                    setCellValue(sheet.Cells, rowIdx, colIdx, cellInfo.CellName, true);
                    setCellBaseInfo(sheet, rowIdx + 2, colIdx, cellInfo.RsPower);
                    setCellBaseInfo(sheet, rowIdx + 3, colIdx, cellInfo.PA);
                    setCellBaseInfo(sheet, rowIdx + 4, colIdx, cellInfo.PB);
                    setCellBaseInfo(sheet, rowIdx + 5, colIdx, cellInfo.Altitude);
                    setCellBaseInfo(sheet, rowIdx + 6, colIdx, cellInfo.Direction);
                    cellInfo.Downward.Real = cellInfo.Downtilt.Real + cellInfo.MechanicalTilt.Real;
                    cellInfo.Downward.Planing = cellInfo.Downtilt.Planing + cellInfo.MechanicalTilt.Planing;
                    setCellBaseInfo(sheet, rowIdx + 7, colIdx, cellInfo.Downward);
                    setCellBaseInfo(sheet, rowIdx + 8, colIdx, cellInfo.Downtilt);
                    setCellBaseInfo(sheet, rowIdx + 9, colIdx, cellInfo.MechanicalTilt);
                    cellIdx++;
                }
            }
        }

        private void setCellBaseInfo<T>(Excel.Worksheet sheet, int rowIdx, int colIdx, ParamInfo<T> info)
        {
            setCellValue(sheet.Cells, rowIdx, colIdx, info.Planing, info.IsValid);
            setCellValue(sheet.Cells, rowIdx, colIdx + 3, info.Real, info.IsValid);
            //setCellValue(sheet.Cells, rowIdx, colIdx + 4, info.IsValid);
        }

        protected void setCellValue<T>(Excel.Range range, int rowIndex, int colIndex, T data, bool isValid)
        {
            range[rowIndex, colIndex] = data;
            if (!isValid)
            {
                Excel.Range r = range[rowIndex, colIndex] as Excel.Range;
                r.Font.ColorIndex = 3;
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return true;
        }

        private void addAssetManager(BtsParameterInfo btsParameters)
        {
            var antQuery = new DIYQueryLteTddAssetManager();
            antQuery.SetCondition(btsParameters.BtsName);
            antQuery.Query();

            foreach (var info in antQuery.LteTddAssetManagerDBInfoList)
            {
                btsParameters.Address = info.Address;
            }
        }

        private void addAntennaPlatform(BtsParameterInfo btsParameters)
        {
            var antQuery = new DIYQueryLteTddAntennaPlatform();
            antQuery.SetCondition(btsParameters.BtsName);
            antQuery.Query();

            foreach (var info in antQuery.LteTddAntennaPlatformDBInfoList)
            {
                if (!btsParameters.CellDic.TryGetValue(info.CellName, out var cellInfo))
                {
                    cellInfo = new CellParameterInfo();
                    cellInfo.CellName = info.CellName;
                    btsParameters.CellDic.Add(info.CellName, cellInfo);
                }

                btsParameters.BtsLongutide.Real = info.Longitude;
                btsParameters.BtsLatitude.Real = info.Latitude;
                cellInfo.Altitude.Real = info.Altitude;
                cellInfo.Direction.Real = info.Direction;
                cellInfo.MechanicalTilt.Real = info.MechanicalTilt;
            }
        }

        private void addNetworkConfig(BtsParameterInfo btsParameters)
        {
            var netQuery = new DIYQueryLteTddNetworkConfig();
            netQuery.SetCondition(btsParameters.BtsName);
            netQuery.Query();

            foreach (var info in netQuery.LteTddNetworkConfigDBInfoList)
            {
                if (!btsParameters.CellDic.TryGetValue(info.CellName, out var cellInfo))
                {
                    cellInfo = new CellParameterInfo();
                    cellInfo.CellName = info.CellName;
                    btsParameters.CellDic.Add(info.CellName, cellInfo);
                }

                btsParameters.NodeBID = info.NodeBID;
                btsParameters.Bandwidth.Real = info.Bandwidth;
                btsParameters.ServiceIP.Real = info.ServiceIP;
                btsParameters.ManageIP.Real = info.ManageIP;
                cellInfo.RsPower.Real = info.RsPower;
                cellInfo.PA.Real = info.PA;
                cellInfo.PB.Real = info.PB;
                cellInfo.Downtilt.Real = info.Downtilt;
            }
        }

        private void addAuditData(BtsParameterInfo btsParameters)
        {
            var dataQuery = new DIYQueryLteTddStationAuditData();
            dataQuery.SetCondition(btsParameters.BtsName);
            dataQuery.Query();

            foreach (var info in dataQuery.LteTddStationAuditDataDBInfoList)
            {
                if (!btsParameters.CellDic.TryGetValue(info.CellName, out var cellInfo))
                {
                    cellInfo = new CellParameterInfo();
                    cellInfo.CellName = info.CellName;
                    btsParameters.CellDic.Add(info.CellName, cellInfo);
                }

                btsParameters.Country = info.Country;
                btsParameters.BtsLongutide.Planing = info.Longitude;
                btsParameters.BtsLatitude.Planing = info.Latitude;

                cellInfo.Altitude.Planing = info.Altitude;
                cellInfo.Direction.Planing = info.Direction;
                cellInfo.Downtilt.Planing = info.Downtilt;
                cellInfo.MechanicalTilt.Planing = info.MechanicalTilt;
            }
        }

        private void addWireless(BtsParameterInfo btsParameters)
        {
            var queryTable = new DIYQueryLteTddWirelessPlanningTable();
            queryTable.Query();
            if (string.IsNullOrEmpty(queryTable.TableName))
            {
                return;
            }

            var wirelessQuery = new DIYQueryLteTddWirelessPlanning();
            wirelessQuery.SetCondition(btsParameters.BtsName);
            wirelessQuery.TableName = queryTable.TableName;
            wirelessQuery.Query();

            foreach (var info in wirelessQuery.LteTddWirelessPlanningDBInfoList)
            {
                if (!btsParameters.CellDic.TryGetValue(info.CellName, out var cellInfo))
                {
                    cellInfo = new CellParameterInfo();
                    cellInfo.CellName = info.CellName;
                    btsParameters.CellDic.Add(info.CellName, cellInfo);
                }
                btsParameters.Bandwidth.Planing = info.Bandwidth;
                btsParameters.ServiceIP.Planing = info.ServiceIP;
                btsParameters.ManageIP.Planing = info.ManageIP;
                cellInfo.RsPower.Planing = info.RsPower;
                cellInfo.PA.Planing = info.PA;
                cellInfo.PB.Planing = info.PB;
            }
        }

        public class BtsParameterInfo
        {
            public string BtsName { get; set; }
            public int NodeBID { get; set; }
            public string Address { get; set; }
            public string DeviceType { get; set; }
            public string Date { get; set; }
            public string Country { get; set; }
            public string BtsType { get; set; }
            public string TerminalType { get; set; }

            public ParamInfo<double?> BtsLongutide { get; set; } = new ParamInfo<double?>();
            public ParamInfo<double?> BtsLatitude { get; set; } = new ParamInfo<double?>();
            public ParamInfo<string> Bandwidth { get; set; } = new ParamInfo<string>();
            public ParamInfo<string> ServiceIP { get; set; } = new ParamInfo<string>();
            public ParamInfo<string> ManageIP { get; set; } = new ParamInfo<string>();

            public Dictionary<string, CellParameterInfo> CellDic { get; set; } = new Dictionary<string, CellParameterInfo>();

            public List<CellParameterInfo> CellSortList { get; set; } = new List<CellParameterInfo>();

            public void Calculate()
            { 
                //BtsLongutide.JudgeValidLongitude(50);
                //BtsLatitude.JudgeValidLatitude(50);
                //Bandwidth.JudgeValid();
                //ServiceIP.JudgeValid();
                //ManageIP.JudgeValid();

                foreach (var cell in CellDic.Values)
                {
                    if (!string.IsNullOrEmpty(cell.CellName))
                    {
                        cell.Calculate();
                        CellSortList.Add(cell);
                    }
                }
                CellSortList.Sort((a, b) => a.CellName.ToUpper().CompareTo(b.CellName.ToUpper()));
            }
        }

        public class CellParameterInfo
        {
            public string CellName { get; set; }

            public ParamInfo<string> RsPower { get; set; } = new ParamInfo<string>();
            public ParamInfo<string> PA { get; set; } = new ParamInfo<string>();
            public ParamInfo<string> PB { get; set; } = new ParamInfo<string>();
            public ParamInfo<double?> Altitude { get; set; } = new ParamInfo<double?>();
            public ParamInfo<double?> Direction { get; set; } = new ParamInfo<double?>();
            public ParamInfo<double?> Downtilt { get; set; } = new ParamInfo<double?>();
            public ParamInfo<double?> MechanicalTilt { get; set; } = new ParamInfo<double?>();
            public ParamInfo<double?> Downward { get; set; } = new ParamInfo<double?>();

            public void Calculate()
            {
                Downward.Real = Downtilt.Real + MechanicalTilt.Real;
                Downward.Planing = Downtilt.Planing + MechanicalTilt.Planing;

                //RsPower.JudgeValid();
                //PA.JudgeValid();
                //PB.JudgeValid();
                //Altitude.JudgeValid();
                //Direction.JudgeValid();
                //Downtilt.JudgeValid();
                //MechanicalTilt.JudgeValid();
                //Downward.JudgeValid();
            }
        }
    }

    class AcpVolteVoiceMo : AcpRrcRate
    {
        public AcpVolteVoiceMo()
        {
            evtRequList = new List<int>() { 1070 };
            evtSuccList = new List<int>() { 1072 };

            resultGrid = new string[MaxCellCount, 1, 2];
            int idx = 12;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            if (fileInfo.Momt != (int)MoMtFile.MoFlag)
            {
                return false;
            }
            return fileInfo.Name.ToUpper().Contains("VOLTE");
        }

        protected override void SaveResult(FileInfo fileInfo, CellKPI kpiCell, int cellID, string btsName)
        {
            int rowIdx = 0;
            LTEBTS bts = kpiCell.LteCell.BelongBTS;
            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(bts.ID, btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            result.SetValue(cellID, rowIdx, 0, kpiCell.RequestCnt);
            result.SetValue(cellID, rowIdx, 1, kpiCell.SucceedCnt);
        }
    }

    class AcpVolteVoiceMt : AcpRrcRate
    {
        public AcpVolteVoiceMt()
        {
            evtRequList = new List<int>() { 1071 };
            evtSuccList = new List<int>() { 1073 };

            resultGrid = new string[MaxCellCount, 1, 2];
            int idx = 13;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "P" + row.ToString();
                resultGrid[i, 0, 1] = "W" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            if (fileInfo.Momt != (int)MoMtFile.MtFlag)
            {
                return false;
            }
            return fileInfo.Name.ToUpper().Contains("VOLTE");
        }

        protected override void SaveResult(FileInfo fileInfo, CellKPI kpiCell, int cellID, string btsName)
        {
            int rowIdx = 0;
            LTEBTS bts = kpiCell.LteCell.BelongBTS;
            LteStationAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteStationAcceptResult(bts.ID, btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            result.SetValue(cellID, rowIdx, 0, kpiCell.RequestCnt);
            result.SetValue(cellID, rowIdx, 1, kpiCell.SucceedCnt);
        }
    }

    class AcpCellName : LteStationAcceptBase
    {
        public AcpCellName()
        {
            resultGrid = new string[MaxCellCount, 1, 1];
            int idx = 4;
            int step = 18;
            for (int i = 0; i < MaxCellCount; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "a" + row.ToString();
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, ICell targetCell, string btsName, List<LTECell> tddCells)
        {
            var lteCell = targetCell as LTECell;
            LTEBTS lteBts = lteCell.BelongBTS;
            if (btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            SetCellIDMap(targetCell, tddCells);

            var result = new LteStationAcceptResult(lteBts.ID, btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
            btsResultDic.Add(btsName, result);

            for (int i = 0; i < tddCells.Count; i++)
            {
                result.SetValue(tddCells[i].CellID, 0, 0, tddCells[i].Name);
            }
        }

        public override void FillResult(string btsName, Excel.Workbook eBook)
        {
            FillResultToSheet(btsName, eBook, 2);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return true;
        }
    }
}
