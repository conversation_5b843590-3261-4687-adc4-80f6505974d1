﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas;
using MasterCom.Util;
using MasterCom.MTGis;
using DevExpress.XtraEditors;
using System.Windows.Forms;

namespace MasterCom.RAMS.Net
{
    public static class CodeRateHelper
    {
        public static Dictionary<int, Dictionary<int, MasterCom.RAMS.Net.ZTLowSpeedAnaByRegion_GSM.TempData>> dicCode { get; set; }
        public static bool CheckFileID(int iFileid)
        {
            if (!CheckStatu())
                return false;
            return dicCode.ContainsKey(iFileid);
        }

        public static bool CheckStatu()
        {
            if (dicCode == null || dicCode.Count == 0)
                return false;
            return true;
        }

        /// <summary>
        /// 获取最近时间点信令
        /// </summary>
        public static MasterCom.RAMS.Net.ZTLowSpeedAnaByRegion_GSM.TempData GetNearCode(int iFileid, long iSTime,long iETime)
        {
            if (!CheckFileID(iFileid))
            {
                return null;
            }
            ZTLowSpeedAnaByRegion_GSM.TempData data = new ZTLowSpeedAnaByRegion_GSM.TempData();
            bool isFind = false;
            foreach (int idx in dicCode[iFileid].Keys)
            {
                ZTLowSpeedAnaByRegion_GSM.TempData tmp = dicCode[iFileid][idx];
                bool isValidSTime = tmp.CodeResult.belongSlot.IBeginTime <= iSTime && tmp.CodeResult.belongSlot.IEndTime >= iSTime;
                bool isValidETime = tmp.CodeResult.belongSlot.IBeginTime <= iETime && tmp.CodeResult.belongSlot.IEndTime >= iETime;

                if (isValidSTime && isValidETime)
                {
                    data = dicCode[iFileid][idx];
                    data.isPartLimit = false;
                    isFind = true;
                }
                else if (isValidSTime || isValidETime || (tmp.CodeResult.belongSlot.IBeginTime >= iSTime&& tmp.CodeResult.belongSlot.IEndTime <= iETime))
                {
                    setFindData(iFileid, iSTime, iETime, ref data, ref isFind, idx, tmp);
                }
            }
            if (!isFind)
                data.Init();
            return data;
        }

        private static void setFindData(int iFileid, long iSTime, long iETime, ref ZTLowSpeedAnaByRegion_GSM.TempData data, ref bool isFind, int idx, ZTLowSpeedAnaByRegion_GSM.TempData tmp)
        {
            if (!isFind)
            {
                data = dicCode[iFileid][idx];
                isFind = true;
            }
            else
            {
                long preTime = GetTimeSolt(data, iSTime, iETime);
                long nowTime = GetTimeSolt(tmp, iSTime, iETime);
                if (nowTime > preTime)
                {
                    data = dicCode[iFileid][idx];//获取占用时长最长的信令
                }
            }
            data.isPartLimit = true;
        }

        private static long GetTimeSolt(ZTLowSpeedAnaByRegion_GSM.TempData tmpData, long iSTime, long iETime)
        {
            long sTime = 0, etime = 0;
            if (tmpData.CodeResult.belongSlot.IBeginTime > iSTime)
                sTime = tmpData.CodeResult.belongSlot.IBeginTime;
            else
                sTime = iSTime;

            if (tmpData.CodeResult.belongSlot.IEndTime > iETime)
                etime = iETime;
            else
                etime = tmpData.CodeResult.belongSlot.IEndTime;

            return etime - sTime;
        }


        public static void Clear()
        {
            if (CheckStatu())
            {
                dicCode.Clear();
                dicCode = null;
            }
        }

        /// <summary>
        /// 解析信令
        /// </summary>
        public static void AnalysisMessage(DTFileDataManager DtFile, QueryCondition condition)
        {
            List<Model.Message> lsMsg = new List<Model.Message>();
            lsMsg.AddRange(DtFile.Messages.FindAll(x => x.ID == 1097532105)); //信令“Modify EPS bearer context request”
            if (lsMsg.Count > 0)
            {
                if (dicCode == null)
                    dicCode = new Dictionary<int, Dictionary<int, ZTLowSpeedAnaByRegion_GSM.TempData>>();
                if (!dicCode.ContainsKey(DtFile.FileID))
                    dicCode[DtFile.FileID] = new Dictionary<int, ZTLowSpeedAnaByRegion_GSM.TempData>();
                System.Windows.Forms.TreeView treeView_tmp = new System.Windows.Forms.TreeView();
                int idx = 0;
                for (int i = 0; i < lsMsg.Count; i++)
                {
                    long iNextMngTime = 0;
                    Model.Message msg = lsMsg[i];
                    if (i + 1 == lsMsg.Count)
                        iNextMngTime = long.MaxValue;
                    else
                        iNextMngTime = lsMsg[i + 1].lTimeWithMillsecond;
                    ZTLowSpeedAnaByRegion_GSM.TempData data = new ZTLowSpeedAnaByRegion_GSM.TempData();
                    if (msg is Model.MessageWithSource)
                    {
                        idx = dealMsg(DtFile, condition, treeView_tmp, idx, iNextMngTime, msg, data);
                    }
                }
                //JudgeCodeValidTime(dicCode[DtFile.FileID]);
            }
        }

        private static int dealMsg(DTFileDataManager DtFile, QueryCondition condition, TreeView treeView_tmp, int idx, long iNextMngTime, Model.Message msg, ZTLowSpeedAnaByRegion_GSM.TempData data)
        {
            data.Init();
            byte[] source = ((MessageWithSource)msg).Source;
            int msgId = ((MessageWithSource)msg).ID;

            if (source != null)
            {
                MessageDecodeHelper.DissectToTree(source, source.Length, msgId, treeView_tmp);
                treeView_tmp.ExpandAll();
                if (treeView_tmp.Nodes.Count > 0)
                {
                    treeView_tmp.Nodes[0].EnsureVisible();
                }
                Console.WriteLine("---------------------------" + msg.DateTime.ToString() + "-----------------------------");
                PrintTreeViewNode(treeView_tmp.Nodes, data);
                data.GetResult();
                if (data.CodeResult.isLimit)
                {
                    JudgeMngValid(DtFile, msg, data, iNextMngTime);
                }
                data.SaveBaseInfo(msg, DtFile, condition);
                if (data.BelongGrid)
                    dicCode[DtFile.FileID].Add(idx++, data);
            }

            return idx;
        }

        /// <summary>
        /// 保存信令
        /// </summary>
        private static void PrintTreeViewNode(TreeNodeCollection node, MasterCom.RAMS.Net.ZTLowSpeedAnaByRegion_GSM.TempData data)
        {
            foreach (TreeNode n in node)
            {
                Console.WriteLine(n.Text);
                if (n.Text.Contains("EPS quality of service"))
                {
                    SaveCodeValues(n.Nodes, data.CodeEPS);
                }
                else if (n.Text.Contains("APN aggregate maximum bit rate"))
                {
                    SaveCodeValues(n.Nodes, data.CodeAPN);
                }
                else if (n.Text.Contains("Quality Of Service"))
                {
                    SaveCodeValues(n.Nodes, data.CodeQuality);
                }
                else
                {
                    PrintTreeViewNode(n.Nodes, data);
                }
            }
        }

        private static void SaveCodeValues(TreeNodeCollection node, MasterCom.RAMS.Net.ZTLowSpeedAnaByRegion_GSM.CodeRate dataRate)
        {
            foreach (TreeNode n in node)
            {
                string nText = n.Text.ToLower();
                if ((nText.Contains("uplink") || nText.Contains("downlink"))
                    && (nText.Contains("maximum") || nText.Contains("apn-ambr")) && !nText.Contains("total"))
                {
                    string[] strCommand = n.Text.Split(':');
                    if (strCommand.Length == 2)
                    {
                        string[] strValue = strCommand[1].TrimStart().Split(' ');
                        double dVlues;
                        if (strCommand[0].Contains("uplink") && !strCommand[0].Contains("extended"))
                        {
                            if (double.TryParse(strValue[0], out dVlues))
                            {
                                dataRate.uplink = dVlues;
                                if (strValue.Length >= 2)
                                {
                                    dataRate.UnitUplink = strValue[1];
                                }
                            }
                        }
                        else if (strCommand[0].Contains("downlink") && !strCommand[0].Contains("extended"))
                        {
                            if (double.TryParse(strValue[0], out dVlues))
                            {
                                dataRate.downlink = dVlues;
                                if (strValue.Length >= 2)
                                {
                                    dataRate.UnitDownlink = strValue[1];
                                }
                            }
                        }
                        else if (strCommand[0].Contains("uplink") && strCommand[0].Contains("extended"))
                        {
                            if (double.TryParse(strValue[0], out dVlues))
                            {
                                dataRate.exUplink = dVlues;
                                if (strValue.Length >= 2)
                                {
                                    dataRate.UnitExUplink = strValue[1];
                                }
                            }
                        }
                        else if (strCommand[0].Contains("downlink") && strCommand[0].Contains("extended"))
                        {
                            if (double.TryParse(strValue[0], out dVlues))
                            {
                                dataRate.exDownlink = dVlues;
                                if (strValue.Length >= 2)
                                {
                                    dataRate.UnitExDownlink = strValue[1];
                                }
                            }
                        }
                        else
                        {
                            //
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 判断限速信令有效时间段
        /// </summary>
        private static void JudgeMngValid(DTFileDataManager DtFile, Model.Message msg, ZTLowSpeedAnaByRegion_GSM.TempData data, long iNextMngTime)
        {
            float limitSpeed = data.CodeResult.GetSpeed(DtFile.FileName);
            if (limitSpeed > 0)
            {
                long EventSTime = long.MaxValue, EventETime = long.MaxValue;
                long iTimeStep = msg.lTimeWithMillsecond;
                bool isFind = false;

                bool thanTime = false;
                List<Model.Event> lsEvent = new List<Event>();
                long iTimeSetp5min =msg.lTimeWithMillsecond- 1000L * 60 * 5;
                foreach (Model.Event mEvent in DtFile.Events)
                {
                    if (!thanTime && iTimeSetp5min <= mEvent.lTimeWithMillsecond)
                    {
                        bool isValid = judgeValidEvt(mEvent);
                        if (isValid)
                            lsEvent.Add(mEvent);
                    }
                    if (iTimeStep <= mEvent.lTimeWithMillsecond)
                    {
                        #region 解释注释
                        /*
                         * 正常情况下：
                         *                  信令位置
                         * 时间段：___|________o__________________|______
                         *          FTP Begin                 FTP Success/Failure
                         *          
                         *         信令位置
                         * 时间段：___o________|__________________|______
                         *                 FTP Begin          FTP Success/Failure
                         */
                        #endregion
                        bool isValid = judgeValidEvt(mEvent);
                        if (isValid)
                        {
                            FindTimeSolt(ref EventSTime, ref EventETime, mEvent);
                            if (EventSTime != long.MaxValue && EventETime == long.MaxValue
                                && (mEvent.EventInfo.Name.Contains("Http") || mEvent.EventInfo.Name.Contains("Video")))
                            {
                                #region 解释注释
                                /*
                                 * FTP事件缺失结束标志，但切换到HTTP、Video Play其他业务时候，作为结束时间：
                                 * 
                                 *          信令位置
                                 * 时间段：____o_________|________________|______
                                 *                   FTP Begin         HTTP/Video Play  
                                 *          
                                 */
                                #endregion
                                EventETime = mEvent.lTimeWithMillsecond;
                            }
                            if (!thanTime && EventSTime != long.MaxValue && EventETime == long.MaxValue && msg.lTimeWithMillsecond <= EventSTime)
                            {
                                #region 解释注释
                                /*
                                 * 第一次查找，FTP事件缺失结束标志，应往查找，判断当前是否落在FTP过程
                                 * 
                                 *                  信令位置
                                 * 时间段：_____|_______o_________________|______
                                 *           FTP Begin                FTP Begin  
                                 *          
                                 */
                                #endregion
                                long EventSTimeTmp = long.MaxValue, EventETimeTmp = long.MaxValue;
                                if (ForwardFind(lsEvent, msg.lTimeWithMillsecond, ref EventSTimeTmp, ref EventETimeTmp))
                                {
                                    EventSTime = EventSTimeTmp;
                                    EventETime = EventETimeTmp;
                                }
                                thanTime = true;
                            }
                            if (EventETime != long.MaxValue)
                            {
                                if (EventSTime > EventETime)
                                    EventSTime = iTimeStep;
                                if (EventSTime >= iNextMngTime)
                                    break;
                                if (EventETime > iNextMngTime)
                                    EventETime = iNextMngTime;
                                isFind = true;
                                if (JudgeMngTime(ref EventSTime, ref EventETime, ref iTimeStep, limitSpeed, data, DtFile) == "信令失效")
                                {
                                    break;
                                }
                            }
                            if (EventSTime != long.MaxValue)
                            {
                                isFind = false;
                            }
                        }
                    }
                }
                if (!isFind && EventSTime == long.MaxValue && EventETime == long.MaxValue)
                {
                    #region 解释注释
                    /*
                     *                   信令位置
                     * 时间段：___|________o__________________|
                     *          FTP Begin                文件结束
                     *          
                     * 当出现此情况，isFind = false，尝试往前找5分钟的事件，看是否是属于如上↑ 图情况
                     */
                    #endregion
                    iTimeStep = msg.lTimeWithMillsecond - 1000L * 60 * 5;
                    foreach (Model.Event mEvent in DtFile.Events)
                    {
                        if (iTimeStep <= mEvent.lTimeWithMillsecond && mEvent.lTimeWithMillsecond < iNextMngTime)
                        {
                            bool isValid = judgeValidEvt(mEvent);
                            if (isValid)
                            {
                                FindTimeSolt(ref EventSTime, ref EventETime, mEvent);
                                if (EventSTime != long.MaxValue)
                                    break;
                            }
                        }
                    }
                }
                if (!isFind && EventSTime != long.MaxValue && EventETime == long.MaxValue)
                {
                    #region 解释注释
                    /*
                     *                   信令位置
                     * 时间段：___|________o__________________|
                     *          FTP Begin                文件结束
                     *          
                     *         信令位置
                     * 时间段：___o________|__________________|
                     *                 FTP Begin         文件结束 
                     */
                    #endregion
                    EventETime = DtFile.Events[DtFile.Events.Count - 1].lTimeWithMillsecond;
                    if (EventETime > iNextMngTime)
                        EventETime = iNextMngTime;
                    if (msg.lTimeWithMillsecond > EventSTime)
                        EventSTime = msg.lTimeWithMillsecond;
                    JudgeMngTime(ref EventSTime, ref EventETime, ref iTimeStep, limitSpeed, data, DtFile);
                }
            }
        }

        private static bool judgeValidEvt(Event mEvent)
        {
            return mEvent.EventInfo != null && !string.IsNullOrEmpty(mEvent.EventInfo.Name);
        }

        private static void FindTimeSolt(ref long EventSTime, ref long EventETime, Model.Event mEvent)
        {
            if ((mEvent.EventInfo.Name.Contains("FTP") && mEvent.EventInfo.Name.Contains("Began")) ||
                (mEvent.EventInfo.Name.Contains("FTP") && mEvent.EventInfo.Name.Contains("First") && EventSTime == long.MaxValue))
            {
                #region 解释注释
                /*
                 *                   信令位置
                 * 时间段：___|________o__________________|____
                 *          FTP Begin                 FTP Begin
                 *         信令位置      
                 *         
                 * 时间段：___o________|__________________|____
                 *                  FTP Begin         FTP Begin
                 * 
                 * FTP事件缺失结尾的时候，以下一个FTP开始时间作为起始，
                 * 同时，下一个FTP起始时间则被忽略，所以使用 FTP First Data作为起始作为下一个FTP的其实时间。
                 * （FTP Began和FTP First Data 属于同一时间发生）
                 */
                #endregion
                if (EventSTime != long.MaxValue && EventETime == long.MaxValue) //FTP事件缺失结尾的时候，
                    EventETime = mEvent.lTimeWithMillsecond;
                else
                {
                    EventSTime = mEvent.lTimeWithMillsecond;
                    EventETime = long.MaxValue;
                }
            }
            else if (mEvent.EventInfo.Name.Contains("FTP") && (mEvent.EventInfo.Name.Contains("Success") || mEvent.EventInfo.Name.Contains("Failure")))
            {
                EventETime = mEvent.lTimeWithMillsecond;
            }
        }

        private static bool ForwardFind(List<Model.Event> lsEventTmp, long iMsgTime, ref long EventSTime, ref long EventETime)
        {
            lsEventTmp.Sort((a, b) => { return b.lTimeWithMillsecond.CompareTo(a.lTimeWithMillsecond); });
            bool isFind = judgeIsFound(lsEventTmp, iMsgTime, ref EventSTime, ref EventETime);
            if (isFind)
            {
                EventSTime = iMsgTime;
                lsEventTmp.Sort((a, b) => { return a.lTimeWithMillsecond.CompareTo(b.lTimeWithMillsecond); });
                foreach (Model.Event mEvent in lsEventTmp)
                {
                    if (mEvent.lTimeWithMillsecond >= EventSTime
                        && (mEvent.EventInfo.Name.Contains("Http") || mEvent.EventInfo.Name.Contains("Video")))
                    {
                        EventETime = mEvent.lTimeWithMillsecond;
                        break;
                    }
                }
                if (EventETime == long.MaxValue)
                    EventETime = lsEventTmp[lsEventTmp.Count - 1].lTimeWithMillsecond;
            }
            return isFind;
        }

        private static bool judgeIsFound(List<Event> lsEventTmp, long iMsgTime, ref long EventSTime, ref long EventETime)
        {
            bool isFind = false;
            foreach (Model.Event mEvent in lsEventTmp)
            {
                if (mEvent.lTimeWithMillsecond <= iMsgTime)
                {
                    FindTimeSolt(ref EventSTime, ref EventETime, mEvent);
                    if (EventETime != long.MaxValue || mEvent.EventInfo.Name.Contains("Http") || mEvent.EventInfo.Name.Contains("Video"))
                        break;
                    else if (EventSTime != long.MaxValue)
                    {
                        isFind = true;
                        break;
                    }
                }
            }

            return isFind;
        }

        private static string JudgeMngTime(ref long EventSTime, ref long EventETime, ref long iTimeStep, float limitSpeed, ZTLowSpeedAnaByRegion_GSM.TempData data, DTFileDataManager DtFile)
        {
            int iLimitTpCount = 0, iAllTpCount = 0, iSpeed0 = 0, iLimitDuration = 0;
            TestPoint tpPre = null;
            foreach (TestPoint tp in DtFile.TestPoints)
            {
                if (tp.lTimeWithMillsecond >= EventSTime && tp.lTimeWithMillsecond <= EventETime)
                {
                    addValidData(limitSpeed, ref iLimitTpCount, ref iAllTpCount, ref iSpeed0, ref iLimitDuration, tpPre, tp);
                }
                tpPre = tp;
            }
            if (iAllTpCount == 0)
            {
                EventSTime = long.MaxValue;
                EventETime = long.MaxValue;
                return "无效时间段，继续查找";
            }
            double rate = iLimitTpCount * 1.0 / iAllTpCount * 100;
            if (iSpeed0 == iAllTpCount)
            {
                iTimeStep = EventETime;
                EventSTime = long.MaxValue;
                EventETime = long.MaxValue;
                return "全0速率采样点不计入统计";
            }
            data.CodeResult.SetSectionTime(EventSTime, EventETime, iLimitTpCount, iAllTpCount);
            data.CodeResult.iLimitDuration += iLimitDuration;
            if (rate < 70.0)    //限速信令生效条件，小于等于限速的采样点大于等于70%，则该信令生效
            {
                return "信令失效";
            }
            EventSTime = long.MaxValue;
            EventETime = long.MaxValue;
            return "继续查找，直到限速失效";
        }

        private static void addValidData(float limitSpeed, ref int iLimitTpCount, ref int iAllTpCount, ref int iSpeed0, ref int iLimitDuration, TestPoint tpPre, TestPoint tp)
        {
            float? speed = null;
            object obj = tp["lte_APP_Speed_Mb"];
            if (obj != null)
            {
                speed = float.Parse(obj.ToString());
            }
            if (speed != null && speed >= 0)
            {
                iAllTpCount++;
                if (limitSpeed >= speed)
                {
                    iLimitTpCount++;
                    if (tpPre != null && tp.NetworkType == TestPoint.ECurrNetType.LTE && tpPre.NetworkType == tp.NetworkType)
                    {
                        iLimitDuration += tp.Time - tpPre.Time;
                    }
                }
                if (speed == 0)
                    iSpeed0++;
            }
        }
    }
}
