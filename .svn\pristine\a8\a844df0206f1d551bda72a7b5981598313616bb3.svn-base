﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.RAMS.Func.ExportTestPoint
{
    public class TemplateManager
    {
        public static readonly string CfgFileName = string.Format(System.Windows.Forms.Application.StartupPath + "/config/ExportTestPoint.xml");
        private static TemplateManager instance = null;
        private TemplateManager()
        {
            LoadCfg();
        }
        public static TemplateManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new TemplateManager();
                }
                return instance;
            }
        }
        
        public List<ExportTemplate> Templates { get; set; } = new List<ExportTemplate>();
        private List<object> cfgParam
        {
            get
            {
                List<object> rpts = new List<object>();
                foreach (ExportTemplate rpt in Templates)
                {
                    rpts.Add(rpt.CfgParam);
                }
                return rpts;
            }
            set {
                if (value==null)
                {
                    return;
                }
                Templates.Clear();
                foreach (object obj in value)
                {
                    ExportTemplate rpt = new ExportTemplate();
                    rpt.CfgParam = obj as Dictionary<string, object>;
                    Templates.Add(rpt);
                }
            }
        }

        public void LoadCfg()
        {
            if (File.Exists(CfgFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(CfgFileName);
                cfgParam = configFile.GetItemValue("ExportCfg", "Templates") as List<object>;
            }
        }

        public void Save()
        {
            MasterCom.Util.XmlConfigFile xmlFile = new MasterCom.Util.XmlConfigFile();
            XmlElement cfgE = xmlFile.AddConfig("ExportCfg");
            xmlFile.AddItem(cfgE, "Templates", this.cfgParam);
            xmlFile.Save(CfgFileName);
        }



    }
}
