﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakCoverRoadQuery＿LteUep : ZTWeakCoverRoadQueryModel
    {
        private static WeakCoverRoadQuery＿LteUep instance = null;
        public static new WeakCoverRoadQuery＿LteUep GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new WeakCoverRoadQuery＿LteUep();
                    }
                }
            }
            return instance;
        }

        protected WeakCoverRoadQuery＿LteUep()
            : base()
        {
            name = "弱覆盖路段_LTE_UEP";
            type = 2;
            funcId = 24000;
            subfuncId = 24001;
            desc = "分析";
            tpStr = "lte_uep_NCell_RSRP";
            tpRSRP = "lte_uep_RSRP";
            tpSINR = "lte_uep_SINR";
            tpTac = "lte_uep_TAC";

            if (instance != null)
            {
                return;
            }
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_UEP);
#if LT
            carrierID = CarrierType.ChinaUnicom;
#else
            carrierID = CarrierType.ChinaMobile;
#endif
        }

        public override string Name
        {
            get { return name; }
        }

        protected override float? getNbMaxRSRP(TestPoint testPoint)
        {
            float? max = null;
            for (int i = 0; i < 10; i++)
            {
                float? n = (float?)testPoint[tpStr, i];
                if (n == null || n < -141 || n > 25)
                {
                    continue;
                }
                max = max == null ? n : Math.Max((float)max, (float)n);
            }
            return max;
        }

        protected override float? getSINR(TestPoint tp)
        {
            return (float?)tp[tpSINR];
        }

        protected override float? getRsrp(TestPoint tp)
        {
            return (float?)tp[tpRSRP];
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(type, funcId, subfuncId, this.Name);
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE感知; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

       

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                //param["BackgroundStat"] = backgroundStat;
                //param["MaxRxLev"] = weakCovRoadCond.maxRxlev;
                //param["SampleDistance"] = weakCovRoadCond.sampleDistance;
                return param;
            }
            //set
            //{
                //if (value == null || value.Count <= 0)
                //{
                //    return;
                //}
                //Dictionary<string, object> param = value;
                //if (param.ContainsKey("BackgroundStat"))
                //{
                //    backgroundStat = (bool)param["BackgroundStat"];
                //}
                //if (param.ContainsKey("MaxRxLev"))
                //{
                //    weakCovRoadCond.maxRxlev = int.Parse(param["MaxRxLev"].ToString());
                //}
                //if (param.ContainsKey("SampleDistance"))
                //{
                //    weakCovRoadCond.sampleDistance = int.Parse(param["SampleDistance"].ToString());
                //}
            //}
        }

        public override PropertiesControl Properties
        {
            get
            {
                return null;
                //return new WeakCovRoadProperties_GSM(this);
            }
        }

        protected override void saveBackgroundData()
        {
            //List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            //foreach (ZTWeakCovRoadInfo item in ZTWeakCovRoadInfoList)
            //{
            //    BackgroundResult result = item.ConvertToBackgroundResult();
            //    result.SubFuncID = GetSubFuncID();
            //    bgResultList.Add(result);
            //}
            //BackgroundFuncQueryManager.GetInstance().SaveResult_Road(GetSubFuncID(), curAnaFileInfo, bgResultList);
            //ZTWeakCovRoadInfoList.Clear();
        }
        #endregion
    }

}
