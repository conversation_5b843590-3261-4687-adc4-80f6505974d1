﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using System.Drawing.Drawing2D;

namespace MasterCom.RAMS.Model
{
    class CellWeightRegionManager
    {
        private static CellWeightRegionManager instance = new CellWeightRegionManager();
        public static CellWeightRegionManager GetInstance()
        {
            return instance;
        }

        public MainModel MainModel
        {
            get { return MainModel.GetInstance(); }
        }

        // 构建CellWeightRegionInfo数组，主要是计算理想覆盖半径
        public void CreateCellWeightRegion(Dictionary<Cell, double> cellWeightDic, 
            double minWeight, double maxWeight)
        {
            if (MainModel.cellWeightRegionInfoList.Count > 0) return;
            CellWeightRegionInfo.MaxWeight = maxWeight;
            CellWeightRegionInfo.MinWeight = minWeight;
            WaitBox.Show("正在计算小区权重区域...", CreateCellWeightRegion, cellWeightDic);
        }

        private void CreateCellWeightRegion(object o)
        {
            Dictionary<Cell, double> cellWeightDic = (Dictionary<Cell, double>)o;
            int counter = 0;
            double radius;
            foreach (Cell cell in cellWeightDic.Keys)
            {
                WaitBox.ProgressPercent = (100 * ++counter / cellWeightDic.Count);
                if (MainModel.cellRadiusDic.ContainsKey(cell))
                {
                    radius = MainModel.cellRadiusDic[cell];
                }
                else
                {
                    radius = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(cell, 3, false);
                    MainModel.cellRadiusDic[cell] = radius;
                }
                if (radius < 1) continue;
                MainModel.cellWeightRegionInfoList.Add(new CellWeightRegionInfo(
                    cell, cellWeightDic[cell], radius));
            }
            System.Threading.Thread.Sleep(1000);
            WaitBox.Close();
        }

    } // end class

    public class CellWeightRegionInfo
    {
        public Cell cell { get; set; }
        public double weight { get; set; }
        public double radius { get; set; }

        public CellWeightRegionInfo(Cell cell, double weight, double radius)
        {
            this.cell = cell;
            this.weight = weight;
            this.radius = radius;
        }

        public double Ratio
        {
            get
            {
                return (weight - MinWeight) / (MaxWeight - MinWeight);
            }
        }

        public static double MaxWeight { get; set; }
        public static double MinWeight { get; set; }
    }

} // end namespace
