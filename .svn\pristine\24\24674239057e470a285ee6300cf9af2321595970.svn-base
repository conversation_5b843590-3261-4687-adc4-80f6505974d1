﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class TestAlarmSettingDlg : BaseDialog
    {
        private AlarmTestCondition cond;

        public TestAlarmSettingDlg()
        {
            InitializeComponent();

            cond = new AlarmTestCondition();
        }

        public void SetCondition(AlarmTestCondition condition)
        {
            this.cond = condition;
            this.spinEditTestDays.Value = condition.TestDays;
            this.spinEditSampleNum.Value = condition.SampleNum;
            this.spinEditPermeate.Value = condition.Permeate;
        }

        public AlarmTestCondition GetCondition()
        {
            cond.SetContext((int)spinEditTestDays.Value, (int)spinEditSampleNum.Value,
                (int)spinEditPermeate.Value, labelUnAchieve.BackColor, labelAchieve.BackColor);

            return cond;
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }

    public class AlarmTestCondition : AreaTestCondition
    {
        public int TestDays { get; set; } = 2;
        public int SampleNum { get; set; } = 10;

        public void SetContext(int days, int spCnt, int permeate, Color colorAch, Color colorUnAch)
        {
            this.TestDays = days;
            this.SampleNum = spCnt;
            base.SetContext(permeate, colorAch, colorUnAch);
        }

        public override void CheckAchieve(CPermeate permeate, bool isVillage)
        {
            if (isVillage)
                permeate.BAchieve = permeate.IValidCnt > 0;
            else
                permeate.BAchieve = permeate.DPermeate >= Permeate;
        }
    }
}
