﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Drawing;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model.Interface;
using MasterCom.MTGis;
using MasterCom.RAMS.CQT;

namespace MasterCom.RAMS.Func
{
    public class QueryATUCQTMap : DownLoadFileByPath
    {
        readonly FileInfo file;
        readonly ATUCQTManager atuCQTManager = ATUCQTManager.GetInstance();
        public QueryATUCQTMap(MainModel mainModel, FileInfo file)
            : base(mainModel)
        {
            this.file = file;
        }
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                atuCQTManager.Init();
                WaitBox.Show("开始下载数据文件...", queryInThread, clientProxy);
                if (MainModel.CQTPlanImg != null)
                {
                    MainModel.MainForm.GetMapForm().GoToView(MainModel.CQTPlanImgLTPos.x, MainModel.CQTPlanImgLTPos.y, 500);
                }
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected virtual void prepareSearchPackage(Package package)
        {
            package.Command = Command.DataManage;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = MasterCom.RAMS.Net.RequestType.REQTYPE_FILEDOWNLOAD_CQTImage;
            package.Content.PrepareAddParam();
            package.Content.AddParam(curFPName);
        }

        private void queryInThread(object o)
        {
            try
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Text = "正在下载CQT地点图片...";
                DiySqlFileFPs queryFileFPs = new DiySqlFileFPs(MainModel, file.ID);
                queryFileFPs.Query();
                List<ATUCQTFPInfo> fps = queryFileFPs.fpInfos;
                atuCQTManager.AddFPInfo(fps);
                List<string> fpNames = new List<string>();
                foreach (ATUCQTFPInfo fpInfo in fps)
                {
                    if (!fpNames.Contains(fpInfo.fpName))
                    {
                        fpNames.Add(fpInfo.fpName);
                    }
                }
                CQTPoint pnt = getCQTPoint();
                if (pnt == null) return;
                foreach (string fpName in fpNames)
                {
                    curFPName = fpName;
                    System.Threading.Thread.Sleep(20);
                    foreach (CQTPointPicture pic in pnt.Pictures)
                    {
                        if (pic.pictureName.Equals(fpName))
                        {
                            DbPoint ltPos = new DbPoint(pic.tlLongitude, pic.tlLatitude);
                            DbPoint brPos = new DbPoint(pic.brLongitude, pic.brLatitude);
                            double imageScale = MainModel.MainForm.GetMapForm().GetCQTImgScaleFromLTBR(ltPos, brPos, pic.pictureImage);
                            ATUCQTImageInfo imageInfo = new ATUCQTImageInfo(curFPName, pic.pictureImage, ltPos, brPos, imageScale);
                            atuCQTManager.AddImageInfo(imageInfo);
                        }
                    }
                }
                atuCQTManager.SetFirstImage();
            }
            catch (Exception e)
            {
                ErrorInfo += e.Message;
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private CQTPoint getCQTPoint()
        {
            DiySqlOneIntValueOnly idQuery;
            string sql = "select distinct a.pointID from tb_cqt_picture a,tb_cqt_atu_fps b,tb_cqt_point c where a.pictureName = b.TestImage and a.pointID = c.pointID and c.otherType1 = 1 and ifileid = "
                + file.ID;
            idQuery = new DiySqlOneIntValueOnly(MainModel.GetInstance(), sql);
            idQuery.Query();
            if (idQuery.IntValue == 0) return null;
            foreach (CQTPoint pnt in CQTPointManager.GetInstance().CQTPoints)
            {
                if (pnt.ID == idQuery.IntValue)
                {
                    return pnt;
                }
            }
            return null;
        }
        private string curFPName;

        private class DiySqlFileFPs : DIYSQLBase
        {
            private readonly int fileID;
            public List<ATUCQTFPInfo> fpInfos = new List<ATUCQTFPInfo>();
            public DiySqlFileFPs(MainModel mainModel, int fileID)
                : base(mainModel)
            {
                this.fileID = fileID;
            }
            protected override string getSqlTextString()
            {
                return "select itime, wtimems, testimage from tb_cqt_atu_fps where ifileid = " + fileID + " order by itime, wtimems";
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[3];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_String;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        int time = package.Content.GetParamInt();
                        int wTimeMS = package.Content.GetParamInt();
                        string name = package.Content.GetParamString();
                        fpInfos.Add(new ATUCQTFPInfo(time, wTimeMS, name));
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }

            public override string Name
            {
                get { return "DiySqlFileFPs"; }
            }
        };
    }
}
