﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.ZTFunc.Injection.TestPlanMultiLayer
{
    class CityOption
    {
        public override string ToString()
        {
            return City.Name;
        }
        public string Name
        {
            get { return City.Name; }
        }
        public IDNamePair City { get; set; }
        public string RegionLayerPath { get; set; }
        public string RegionNameField { get; set; }
        public List<string> RegionFields { get; set; }
        List<StreetInjectTableInfo> roadLayerSet = new List<StreetInjectTableInfo>();
        public List<StreetInjectTableInfo> RoadLayers
        {
            get { return roadLayerSet; }
        }
        
        public bool Check { get; set; } = true;

        public Dictionary<string, object> Param
        {
            get {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic["CityName"] = City.Name;
                dic["RegionLayer"] = RegionLayerPath;
                dic["RegionNameField"] = RegionNameField;
                dic["RegionNameFieldIdx"] = RegionNameFieldIdx;
                dic["RegionFields"] = RegionFields;
                List<object> layers = new List<object>();
                foreach (StreetInjectTableInfo info in RoadLayers)
                {
                    layers.Add(info.Param);
                }
                dic["RoadLayers"] = layers;
                return dic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                string name = value["CityName"] as string;
                int id = DistrictManager.GetInstance().GetDistrictID(name);
                this.City = new IDNamePair(id, name);
                this.RegionLayerPath = value["RegionLayer"] as string;
                this.RegionNameField = value["RegionNameField"] as string;
                this.RegionNameFieldIdx = (int)value["RegionNameFieldIdx"];
                this.RegionFields = new List<string>();
                List<object> list = value["RegionFields"] as List<object>;
                if (list!=null)
                {
                    foreach (string item in list)
                    {
                        this.RegionFields.Add(item);
                    }
                }
                
                this.roadLayerSet = new List<StreetInjectTableInfo>();
                list = value["RoadLayers"] as List<object>;
                if (list != null)
                {
                    foreach (object item in list)
                    {
                        StreetInjectTableInfo info = new StreetInjectTableInfo();
                        info.Param = item as Dictionary<string, object>;
                        this.roadLayerSet.Add(info);
                    }
                }
               
            }
        }


        public int RegionNameFieldIdx { get; set; }
    }
}
