﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTAtuLogKPIGroup
{
    class DevFileKPIGroup
    {
        readonly string name;
        public DevFileKPIGroup(string name)
        {
            this.name = name;
            kpiGroup = new KPIDataGroup(name);
        }

        readonly KPIDataGroup kpiGroup = null;
        public List<KPIDataGroup> SubItems = new List<KPIDataGroup>();

        internal void AddFileData(KPIDataGroup item)
        {
            SubItems.Add(item);
            kpiGroup.Merge(item);
        }

        public DeviceKPIInfo MakeSummary(GroupTemplate template)
        {
            int satisfiedFileNum = 0;
            Dictionary<GroupIndicatorOption, int> optionNumDic = new Dictionary<GroupIndicatorOption, int>();

            DeviceKPIInfo devInfo = new DeviceKPIInfo(name);
            foreach (KPIDataGroup fileItem in SubItems)
            {
                FileInfo fi = fileItem.GroupInfo as FileInfo;
                FileKPIInfo fiKPI = new FileKPIInfo(fi);

                bool? satisfied = null;
                foreach (GroupIndicatorOption item in template.Options)
                {
                    double value = double.NaN;
                    if (!item.IsMultiFormula && (item.MoMt1 == 0 || fi.Momt == item.MoMt1))
                    {
                        value = fileItem.CalcFormula((CarrierType)item.CarrierID, fi.Momt, item.KPIFormula);
                    }

                    bool matched = item.IsSatisfiedValue(value);
                    setOptionNumDic(optionNumDic, item, matched);

                    satisfied = setSatisfied(template, satisfied, matched);

                    setfiKPI(fiKPI, item, value, matched);
                }
                if (satisfied != null && ((bool)satisfied))
                {
                    satisfiedFileNum++;
                }
                devInfo.Files.Add(fiKPI);
            }

            this.kpiGroup.FinalMtMoGroup();
            setDevInfo(template, optionNumDic, devInfo);

            devInfo.SatisifiedFileNum = satisfiedFileNum;

            return devInfo;
        }

        private static void setOptionNumDic(Dictionary<GroupIndicatorOption, int> optionNumDic, GroupIndicatorOption item, bool matched)
        {
            if (matched)
            {
                if (optionNumDic.ContainsKey(item))
                {
                    optionNumDic[item]++;
                }
                else
                {
                    optionNumDic[item] = 1;
                }
            }
        }

        private bool? setSatisfied(GroupTemplate template, bool? satisfied, bool matched)
        {
            switch (template.LogicType)
            {
                case ELogicalType.与:
                    if (satisfied == null)
                    {
                        satisfied = matched;
                    }
                    else
                    {
                        satisfied &= matched;
                    }
                    break;
                case ELogicalType.或:
                    if (satisfied == null)
                    {
                        satisfied = matched;
                    }
                    else
                    {
                        satisfied |= matched;
                    }
                    break;
                default:
                    break;
            }

            return satisfied;
        }

        private void setfiKPI(FileKPIInfo fiKPI, GroupIndicatorOption item, double value, bool matched)
        {
            fiKPI.KeyValueDic[item.KPIName] = value;
            if (matched)
            {
                fiKPI.KeyValueDic[item.FileNumToken] = 1;
            }
            else
            {
                fiKPI.KeyValueDic[item.FileNumToken] = 0;
            }
        }

        private void setDevInfo(GroupTemplate template, Dictionary<GroupIndicatorOption, int> optionNumDic, DeviceKPIInfo devInfo)
        {
            foreach (GroupIndicatorOption item in template.Options)
            {
                CarrierType carrierType = (CarrierType)item.CarrierID;
                double value;
                if (item.IsMultiFormula)
                {
                    double value1 = this.kpiGroup.CalcFormula(carrierType, item.MoMt1, item.Formula1);
                    double value2 = this.kpiGroup.CalcFormula(carrierType, item.MoMt2, item.Formula2);
                    value = Math.Round(value1 - value2, 2);
                }
                else
                {
                    value = this.kpiGroup.CalcFormula(carrierType, item.MoMt1, item.KPIFormula);

                }
                devInfo.KeyValueDic[item.KPIName] = value;
                int num;
                optionNumDic.TryGetValue(item, out num);
                devInfo.KeyValueDic[item.FileNumToken] = num;
                devInfo.KeyValueDic[item.FilePerToken] = Math.Round(num * 100.0 / SubItems.Count, 2);
            }
        }
    }

    public class KPIInfoBase
    {
        public string Name { get; set; }
        public Dictionary<string, object> KeyValueDic { get; set; } = new Dictionary<string, object>();
    }

    public class DeviceKPIInfo : KPIInfoBase
    {
        public DeviceKPIInfo(string name)
        {
            this.Name = name;
        }
        public List<FileKPIInfo> Files { get; set; } = new List<FileKPIInfo>();
        public int SatisifiedFileNum { get; set; }
        public int FileNum
        {
            get { return Files.Count; }
        }
        public double SatisifiedPer
        {
            get { return Math.Round(SatisifiedFileNum * 100.0 / FileNum, 2); }
        }

    }

    public class FileKPIInfo : KPIInfoBase
    {
        public FileKPIInfo(FileInfo fi)
        {
            this.FileInfo = fi;
            this.Name = fi.Name;
        }
        public FileInfo FileInfo { get; set; }
    }

}
