﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    class GetWorkParamsHelper_SX_NR : GetWorkParamsHelper<GetWorkParamsHelper_SX_NR>
    {
        protected override StationAcceptWorkParams getWorkParams()
        {
            return new StationAcceptWorkParams_SX_NR();
        }

        protected override void setWorkParams(System.Data.DataRow row, StationAcceptWorkParams workParams)
        {
            //根据工参模板读取
            CellAcceptWorkParam_SX_NR info = new CellAcceptWorkParam_SX_NR();
            info.FillDataByExcel(row);
            if (string.IsNullOrEmpty(info.DistrictName))
            {
                throw (new Exception("地市名为空"));
            }
            StationAcceptWorkParams_SX_NR workParamSum = (StationAcceptWorkParams_SX_NR)workParams;
            workParamSum.AddWorkParamSum(info);
        }

        protected override bool judgeOtherInfo(System.Data.DataTable tb)
        {
            if (!tb.Columns.Contains("地市"))
            {
                reportInfo("表中不包含地市列");
                return false;
            }
            return true;
        }

        protected override bool upLoadWorkParams(StationAcceptWorkParams workParams, StationAcceptCondition condition)
        {
            UpLoadWorkParams_SX_NR upLoadParams = new UpLoadWorkParams_SX_NR(workParams);
            bool isValid = ((StationAcceptCondition_SX_NR)condition).JudgeConnectValid();
            if (isValid)
            {
                upLoadParams.Save(workParams, ((StationAcceptCondition_SX_NR)condition).SqlConnect);

                //upLoadParams.Query();
                reportInfo("有效工参条数:" + upLoadParams.ValidCount);
            }
            else
            {
                reportInfo("主库链接配置异常...");
            }
            return upLoadParams.SaveInDBSuccess;
        }

        protected override StationAcceptWorkParams readWorkParamsFromDB(StationAcceptCondition condition)
        {
            WorkParamsQuery_SX_NR query = new WorkParamsQuery_SX_NR((StationAcceptCondition_SX_NR)condition);
            query.Query();

            return query.WorkParams;
        }
    }

    public class BtsAcceptWorkParam_SX_NR : BtsAcceptWorkParam_SX
    {
        public BtsAcceptWorkParam_SX_NR(CellAcceptWorkParam_SX_NR cellParam)
            : base(cellParam)
        {
            Longitude = cellParam.Longitude;
            Latitude = cellParam.Latitude;
        }

        private NRBTS bts = null;

        public void AddCellParamsInfo(CellAcceptWorkParam_SX_NR info)
        {
            CellWorkParamDic[info.CellNameKey] = info;
        }

        #region 处理工参
        /// <summary>
        /// 是否是从库里读取的待单验工参
        /// </summary>
        private bool isNewAdd;
        /// <summary>
        /// 待单验站点对应缓存的工参小区
        /// </summary>
        private List<NRCell> oldCells;
        #region 添加单验工参
        public override void LoadCurBtsWorkParam(CellManager cellManager)
        {
            //加载单验工参
            CellAcceptWorkParam_SX_NR cellParam = CellWorkParams[0] as CellAcceptWorkParam_SX_NR;
            long nci = cellParam.ENodeBID * 4096L + cellParam.CellID;
            List<NRCell> tmpCells = cellManager.GetNRCellsByNCI(nci);
            if (tmpCells != null)
            {
                oldCells = new List<NRCell>(tmpCells.ToArray());
                removeCell(tmpCells, cellManager);
            }
            isNewAdd = addCellInfoToCellManager(ref bts, cellManager);
        }

        /// <summary>
        /// 移除该小区的旧工参
        /// </summary>
        /// <param name="cells"></param>
        private void removeCell(List<NRCell> cells, CellManager cellManager)
        {
            if (cells == null)
            {
                return;
            }
            cells = new List<NRCell>(cells);
            foreach (NRCell oldCell in cells)
            {
                cellManager.Remove(oldCell.BelongBTS);
                foreach (NRCell lteCell in oldCell.BelongBTS.Cells)
                {
                    cellManager.Remove(lteCell);
                    foreach (NRAntenna antenna in lteCell.Antennas)
                    {
                        cellManager.Remove(antenna);
                    }
                }
            }
        }

        /// <summary>
        /// 添加上传的工参
        /// </summary>
        /// <param name="btsParam"></param>
        /// <param name="bts"></param>
        /// <returns></returns>
        private bool addCellInfoToCellManager(ref NRBTS bts, CellManager cellManager)
        {
            foreach (CellAcceptWorkParamBase info in CellWorkParams)
            {
                CellAcceptWorkParam_SX_NR cellInfo = (CellAcceptWorkParam_SX_NR)info;
                NRCell cell = CellManager.GetInstance().GetNRCellByNCI(DateTime.Now, cellInfo.Nci);
                if (cell != null)
                {
                    bts = cell.BelongBTS;
                    return false;
                }
            }

            bts = new NRBTS();
            int snapShotId = -1;

            #region 暂时动态添加工参到CellManager，稍后移除
            bts.Fill(snapShotId, 0, 2147483647);
            bts.Name = BtsName;
            bts.BTSID = ENodeBID;
            bts.Type = IsOutDoor ? NRBTSType.Outdoor : NRBTSType.Indoor;

            foreach (CellAcceptWorkParamBase info in CellWorkParams)
            {
                CellAcceptWorkParam_SX_NR cellParamInfo = (CellAcceptWorkParam_SX_NR)info;
                bts.Longitude = cellParamInfo.Longitude;
                bts.Latitude = cellParamInfo.Latitude;

                snapShotId--;
                NRCell cell = new NRCell();
                cell.Fill(snapShotId, 0, 2147483647);
                cell.BelongBTS = bts;
                cell.Name = cellParamInfo.CellNameKey;
                cell.TAC = cellParamInfo.Tac;
                cell.NCI = cellParamInfo.ENodeBID * 4096L + cellParamInfo.CellID;
                cell.CellID = cellParamInfo.CellID;
                cell.PCI = cellParamInfo.Pci;
                cell.SSBARFCN = cellParamInfo.Earfcn;
                bts.AddCell(cell);
                cellManager.Add(cell);

                NRAntenna antenna = new NRAntenna();
                snapShotId--;
                antenna.Fill(snapShotId, 0, 2147483647);
                antenna.Cell = cell;
                antenna.Longitude = cellParamInfo.Longitude;
                antenna.Latitude = cellParamInfo.Latitude;
                antenna.Direction = (short)cellParamInfo.Direction;
                antenna.Downward = (short)cellParamInfo.Downward;
                antenna.Altitude = cellParamInfo.Altitude;
            }
            cellManager.Add(bts);
            #endregion

            return true;
        }
        #endregion

        #region 移除单验工参
        public override void RemoveCurBtsWorkParam(CellManager cellManager)
        {
            //移除单验工参
            removeNedAddedCell(bts, isNewAdd, cellManager);
            reCoverCells(oldCells, cellManager);
        }

        private void removeNedAddedCell(NRBTS bts, bool isNewAdd, CellManager cellManager)
        {
            if (isNewAdd)//将动态添加的工参移除
            {
                foreach (NRCell cell in bts.Cells)
                {
                    cellManager.Remove(cell);
                    foreach (NRAntenna ant in cell.Antennas)
                    {
                        cellManager.Remove(ant);
                    }
                }
                cellManager.Remove(bts);
            }
        }

        /// <summary>
        /// 恢复旧工参
        /// </summary>
        /// <param name="cells"></param>
        private void reCoverCells(List<NRCell> cells, CellManager cellManager)
        {
            if (cells != null)
            {
                List<NRBTS> lteBtsList = cellManager.GetCurrentNRBTSs();
                cells = new List<NRCell>(cells);
                foreach (NRCell oldCell in cells)
                {
                    foreach (NRCell lteCell in oldCell.BelongBTS.Cells)
                    {
                        cellManager.Add(lteCell);
                        foreach (NRAntenna antenna in lteCell.Antennas)
                        {
                            cellManager.Add(antenna);
                        }
                    }

                    if (!lteBtsList.Contains(oldCell.BelongBTS))
                    {
                        cellManager.Add(oldCell.BelongBTS);
                    }
                }
            }
        }
        #endregion
        #endregion
    }

    [Serializable]
    public class CellAcceptWorkParam_SX_NR : CellAcceptWorkParam_SX
    {
        public long Nci { get; set; }
        public int SsbArfcn { get; set; }
        public override string Token { get { return string.Format("{0}-{1}", Tac, Nci); } }

        public override void FillDataByExcel(System.Data.DataRow row)
        {
            DistrictName = getValid(row, "地市");
            DistrictName = DistrictName.Replace("市", "");
            BtsNameFull = getValid(row, "站名");
            ENodeBID = getValidIntData(getValid(row, "站号"));
            Longitude = getValidDoubleData(getValid(row, "经度"));
            Latitude = getValidDoubleData(getValid(row, "纬度"));
            CoverTypeDes = getValid(row, "宏微");
            CellID = getValidIntData(getValid(row, "小区标识"));
            CellNameFull = getValid(row, "小区名称");
            Tac = getValidIntData(getValid(row, "TAC"));
            Nci = ENodeBID * 4096L + CellID;
            Earfcn = getValidIntData(getValid(row, "频点"));
            Pci = getValidIntData(getValid(row, "PCI"));
            SsbArfcn = getValidIntData(getValid(row, "SSB频域位置"));
            Direction = (int)getValidDoubleData(getValid(row, "方位角"));
            Downward = (int)getValidDoubleData(getValid(row, "下倾角"));
            Altitude = (int)getValidDoubleData(getValid(row, "站高"));
            UpdateTime = DateTime.Now;
            ImportTime = UpdateTime;

            CellNameKey = FileNameRuleHelper_SX.GetCellNameKeyByFull(CellNameFull);
        }

        public override void FillDataByDB(Package package)
        {
            DistrictName = package.Content.GetParamString();
            BtsNameFull = package.Content.GetParamString();
            ENodeBID = package.Content.GetParamInt();
            CellNameFull = package.Content.GetParamString();
            CellID = package.Content.GetParamInt();
            Tac = package.Content.GetParamInt();
            Nci = package.Content.GetParamInt64();
            Earfcn = package.Content.GetParamInt();
            Pci = package.Content.GetParamInt();
            SsbArfcn = package.Content.GetParamInt();
            int iLongitude = package.Content.GetParamInt();
            int iLatitude = package.Content.GetParamInt();
            Longitude = (double)iLongitude / 10000000;
            Latitude = (double)iLatitude / 10000000;
            CoverTypeDes = package.Content.GetParamString();
            Altitude = package.Content.GetParamInt();
            Direction = package.Content.GetParamInt();
            Downward = package.Content.GetParamInt();

            string[] btsStr = BtsNameFull.Split(new char[] { '-' }, 2);
            if (btsStr.Length == 2)
            {
                BtsName = btsStr[1];
            }
            CellNameKey = FileNameRuleHelper_SX.GetCellNameKeyByFull(CellNameFull);
        }
    }

    public class StationAcceptWorkParams_SX_NR : StationAcceptWorkParams
    {
        public Dictionary<string, Dictionary<string, BtsAcceptWorkParam_SX_NR>> WorkParamSumDic { get; set; } = new Dictionary<string, Dictionary<string, BtsAcceptWorkParam_SX_NR>>();

        public void setWorkParam(Dictionary<string, Dictionary<string, BtsAcceptWorkParamBase<string>>> workParams)
        {
            foreach (var districtWorkParams in WorkParamSumDic)
            {
                Dictionary<string, BtsAcceptWorkParamBase<string>> curBtsWorkParams = new Dictionary<string, BtsAcceptWorkParamBase<string>>();
                workParams.Add(districtWorkParams.Key, curBtsWorkParams);
                foreach (var btsWorkParams in districtWorkParams.Value)
                {
                    curBtsWorkParams.Add(btsWorkParams.Key, btsWorkParams.Value);
                }
            }
        }

        public void AddWorkParamSum(CellAcceptWorkParam_SX_NR cellInfo)
        {
            if (!WorkParamSumDic.TryGetValue(cellInfo.DistrictName, out var btsInfoIDic))
            {
                btsInfoIDic = new Dictionary<string, BtsAcceptWorkParam_SX_NR>();
                WorkParamSumDic.Add(cellInfo.DistrictName, btsInfoIDic);
            }
            if (!btsInfoIDic.TryGetValue(cellInfo.BtsNameFull, out var btsInfo))
            {
                btsInfo = new BtsAcceptWorkParam_SX_NR(cellInfo);
                btsInfoIDic.Add(cellInfo.BtsNameFull, btsInfo);
            }
            btsInfo.AddCellParamsInfo(cellInfo);
        }
    }
}
