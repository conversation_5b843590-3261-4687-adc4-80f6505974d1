﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Frame;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYLeakOutCellSetScanLTE : DIYAnalyseByCellBackgroundBaseByFile
    {
        public int rsrpThreshold { get; set; } = -85; //最强信号门限
        public int rsrpDValue { get; set; } = 10;//相对最强信号门限
        public int nbSampleDistanceLimit { get; set; } = 50;//相邻采样点距离
        public int validDistance { get; set; } = 50;//连续覆盖长度
        readonly Dictionary<LTECell, LeakOutCell_LTESCAN> leakOutCellDic_LTE = new Dictionary<LTECell, LeakOutCell_LTESCAN>();

        private static ZTDIYLeakOutCellSetScanLTE intance = null;
        protected static readonly object lockObj = new object();
        public static ZTDIYLeakOutCellSetScanLTE GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYLeakOutCellSetScanLTE();
                    }
                }
            }
            return intance;
        }
        protected ZTDIYLeakOutCellSetScanLTE()
            : base(MainModel.GetInstance())
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_SCAN_TOPN);
            carrierID = CarrierType.ChinaMobile;
        }

        public ZTDIYLeakOutCellSetScanLTE(ServiceName serviceName)
            : this()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        public override string Name
        {
            get { return "室分外泄分析_LTE扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23007, this.Name);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            LeakOutCellSetConditionDlg conditionDlg = LeakOutCellSetConditionDlg.GetDlg();
            if (conditionDlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            rsrpThreshold = conditionDlg.RscpLow;
            rsrpDValue = conditionDlg.RscpLimit;
            nbSampleDistanceLimit = conditionDlg.MaxDistance;
            validDistance = conditionDlg.ValidDistance;
            return true;
        }

        protected override void fireShowForm()
        {
            if (MainModel.LeakOutCell_SCANList.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据。");
                return;
            }
            MainModel.MainForm.FireLeakOutCellSetScanTDForm();
        }

        protected override void fireSetDefaultMapSerialTheme()
        {
            MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            MainModel.LeakOutCell_SCANList.Clear();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                leakOutCellDic_LTE.Clear();
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    for (int i = 0; i < testPointList.Count; i++)
                    {
                        TestPoint testPoint = testPointList[i];
                        if (i == 0)
                        {
                            doWithDTData(testPoint);
                        }
                        else
                        {
                            addLeakOutCell_SCANList(testPointList, i, testPoint);
                        }
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void addLeakOutCell_SCANList(List<TestPoint> testPointList, int i, TestPoint testPoint)
        {
            double betweenDistance = MathFuncs.GetDistance(testPointList[i - 1].Longitude, testPointList[i - 1].Latitude, testPoint.Longitude, testPoint.Latitude);
            if (betweenDistance > nbSampleDistanceLimit || !doWithDTData(testPoint))
            {
                foreach (LeakOutCell_LTESCAN leakCell in leakOutCellDic_LTE.Values)
                {
                    if (leakCell.Distance >= validDistance)
                    {
                        MainModel.LeakOutCell_SCANList.Add(leakCell);
                    }
                }
                leakOutCellDic_LTE.Clear();
            }
        }

        private bool doWithDTData(TestPoint testPoint)
        {
            if (!Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
            {
                return false;
            }
            bool getLeak = false;
            float? rsrpMain = (float?)testPoint["LTESCAN_TopN_CELL_Specific_RSRP", 0];
            if (rsrpMain <= rsrpThreshold)
            {
                return getLeak;
            }
            for (int i = 0; i < 50; i++)
            {
                float? rsrpCur = (float?)testPoint["LTESCAN_TopN_CELL_Specific_RSRP", i];

                if (rsrpCur == null || rsrpMain - rsrpCur >= rsrpDValue)
                {
                    break;
                }
                LTECell curCell = testPoint.GetCell_LTEScan(i);
                if (curCell != null && curCell.Type == LTEBTSType.Indoor)
                {
                    if (!leakOutCellDic_LTE.ContainsKey(curCell))
                    {
                        LeakOutCell_LTESCAN leakOutCell = new LeakOutCell_LTESCAN(curCell, testPoint, (float)rsrpCur);
                        leakOutCellDic_LTE[curCell] = leakOutCell;
                    }
                    else
                    {
                        leakOutCellDic_LTE[curCell].AddTestPoint(testPoint, (float)rsrpCur);
                    }
                    getLeak = true;
                }
            }
            return getLeak;
        }

        protected override void getResultsAfterQuery()
        {
            foreach (LeakOutCell_SCAN item in MainModel.LeakOutCell_SCANList)
            {
                item.GetResult();
            }
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE扫频专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["RSCPThreshold"] = rsrpThreshold;
                param["RSCPDValue"] = rsrpDValue;
                param["NBSampleDistanceLimit"] = nbSampleDistanceLimit;
                param["ValidDistance"] = validDistance;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("RSCPThreshold"))
                {
                    rsrpThreshold = int.Parse(param["RSCPThreshold"].ToString());
                }
                if (param.ContainsKey("RSCPDValue"))
                {
                    rsrpDValue = int.Parse(param["RSCPDValue"].ToString());
                }
                if (param.ContainsKey("NBSampleDistanceLimit"))
                {
                    nbSampleDistanceLimit = int.Parse(param["NBSampleDistanceLimit"].ToString());
                }
                if (param.ContainsKey("ValidDistance"))
                {
                    validDistance = int.Parse(param["ValidDistance"].ToString());
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new LeakOutCellProperties_LTESCAN(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> resultList = new List<BackgroundResult>();
            foreach (LeakOutCell_SCAN item in MainModel.LeakOutCell_SCANList)
            {
                item.GetResult();
                BackgroundResult result = item.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                resultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), curAnaFileInfo, resultList);
            MainModel.LeakOutCell_SCANList.Clear();
        }
        #endregion
    }

    public class LeakOutCell_LTESCAN : LeakOutCell_SCAN
    {
        public LTECell lteCell { get; set; }

        public LeakOutCell_LTESCAN()
        {
        }

        public LeakOutCell_LTESCAN(LTECell lteCell, TestPoint tp, float rxlev)
        {
            this.lteCell = lteCell;
            AddTestPoint(tp, rxlev);
        }

        public override string CellName
        {
            get
            {
                if (lteCell != null)
                {
                    return lteCell.Name;
                }
                return "";
            }
        }

        public override string CellID
        {
            get
            {
                if (lteCell != null)
                {
                    return lteCell.SCellID.ToString();
                }
                return "";
            }
        }

        public override string BCCH
        {
            get
            {
                if (lteCell != null)
                {
                    return lteCell.PCI.ToString();
                }
                return "";
            }
        }

        public override string BSIC
        {
            get
            {
                if (lteCell != null)
                {
                    return lteCell.EARFCN.ToString();
                }
                return "";
            }
        }

        public override string LAC
        {
            get
            {
                if (lteCell != null)
                {
                    return lteCell.TAC.ToString();
                }
                return "";
            }
        }

        public override string CI
        {
            get
            {
                if (lteCell != null)
                {
                    return lteCell.ECI.ToString();
                }
                return "";
            }
        }

        protected override void fillCellInfo(BackgroundResult bgResult)
        {
            bgResult.CellType = BackgroundCellType.LTE;
            bgResult.LAC = lteCell.TAC;
            bgResult.CI = lteCell.ECI;
            bgResult.BCCH = lteCell.EARFCN;
            bgResult.BSIC = lteCell.PCI;

            bgResult.StrDesc = lteCell.BelongBTS.BTSID + "," + lteCell.SCellID;
        }
    }
}
