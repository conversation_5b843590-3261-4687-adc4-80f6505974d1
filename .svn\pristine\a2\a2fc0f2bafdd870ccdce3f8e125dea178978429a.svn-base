﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTRegionGridFilter;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryGridFilter : QueryKPIStatByRegion
    {
        protected override bool getConditionBeforeQuery()
        {
            regionGridSet = new List<RegionGridDataInfo>();
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18033, this.Name);
        }

        protected override void fireShowResult()
        {
            if (regionGridSet.Count==0)
            {
                System.Windows.Forms.MessageBox.Show("没有栅格数据！");
                return;
            }
            RegionGridFilterListForm frm = MainModel.GetObjectFromBlackboard(typeof(RegionGridFilterListForm)) as RegionGridFilterListForm;
            if (frm==null || frm.IsDisposed)
            {
                frm = new RegionGridFilterListForm();
                frm.Owner = MainModel.MainForm;
            }
            frm.FillData(regionGridSet);
            frm.Visible = true;
            frm.BringToFront();
            regionGridSet = null;
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> formulaSet = new List<string>();
            formulaSet.Add("{Nr_80040005}");  //duration
            formulaSet.Add("{Nr_80040006}");  //distance
            formulaSet.Add("{Nr_80040007}");  //samplteNum
            formulaSet.Add("{Nr_BA040084}");  //FTP_Download_TransferedSize（单位Byte）
            formulaSet.Add("{Nr_BA040085}");  //FTP_Download_TransferedTime（单位ms）
            formulaSet.Add("{Nr_BA040002}");  //NR_SS_RSRP_Avg

            formulaSet.Add("{Lte_0805}");  //duration
            formulaSet.Add("{Lte_0806}");  //distance
            formulaSet.Add("{Lte_61210301}");  //samplteNum
            formulaSet.Add("{Lte_052109020101}");  //LTE_VOICE_LTE_FTP_Download_DOWNLOAD_APP_BYTE（单位Byte）
            formulaSet.Add("{Lte_052109020102}");  //LTE_VOICE_LTE_FTP_Download_DOWNLOAD_APP_TIME（单位ms）
            formulaSet.Add("{Lte_61210309}");  //LTE_VOICE_RSRP_RSRP_MeanValue

            return getTriadIDIgnoreServiceType(formulaSet);
        }

        protected override void recieveAndHandleSpecificStatData(Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            if (!condition.Geometorys.GeoOp.ContainsRectCenter(grid.Bounds))
            {
                return;
            }
            fillStatData(package, curImgColumnDef, singleStatData);
            List<ResvRegion> regs = getStatImgIntersectRegions(lng, lat);
            foreach (ResvRegion reg in regs)
            {
                RegionGridDataInfo regData = regionGridSet.Find(x => { return x.Name.Equals(reg.RegionName); });
                if (regData==null)
                {
                    regData = new RegionGridDataInfo(reg.RegionName);
                    regionGridSet.Add(regData);
                }
                regData.AddGridData(singleStatData);
            }
        }

        protected override void handleStatEvent(Event evt)
        {
            if (evt.ID != 9058 && evt.ID != 58)
            {
                return;
            }
            StatDataEvent eventData = new StatDataEvent(evt, false, null);
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(eventData.LTLng, eventData.LTLat);
            if (!condition.Geometorys.GeoOp.ContainsRectCenter(grid.Bounds))
            {
                return;
            }

            List<ResvRegion> regs = getStatImgIntersectRegions(eventData.LTLng, eventData.LTLat);
            foreach (ResvRegion reg in regs)
            {
                RegionGridDataInfo regData = regionGridSet.Find(x => { return x.Name.Equals(reg.RegionName); });
                if (regData == null)
                {
                    regData = new RegionGridDataInfo(reg.RegionName);
                    regionGridSet.Add(regData);
                }
                regData.AddGridData(eventData);
            }
        }

        private List<RegionGridDataInfo> regionGridSet = null;

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            if (regionGridSet != null && regionGridSet.Count > 0)
            {
                WaitTextBox.Show("正在过滤栅格...", makeSummaryInThread);
            }
        }

        private void makeSummaryInThread()
        {
            foreach (RegionGridDataInfo reg in regionGridSet)
            {
                reg.MakeSummary();
            }
            System.Threading.Thread.Sleep(100);
            WaitTextBox.Close();
        }

    }

   

}
