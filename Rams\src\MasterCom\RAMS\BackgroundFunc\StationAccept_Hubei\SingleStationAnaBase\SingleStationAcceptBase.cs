﻿using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public abstract class SingleStationAcceptBase : DIYAnalyseByCellBackgroundBaseByFile
    {
        protected string strWorkParamTime = "";
        protected string curDistrictName = "";
        protected string folderPath = "";
        protected List<NPOIRow> sumRows = new List<NPOIRow>();

        protected Dictionary<string, Dictionary<int, Dictionary<int, CellWorkParam>>> workParamSumDic = null;
        public StationAcceptAutoSet FuncSet { get; set; } = new StationAcceptAutoSet();
        protected bool isAnaByAcceptRules = false;

        protected static readonly object lockObj = new object();
        protected SingleStationAcceptBase()
            : base(MainModel.GetInstance())
        {
            this.isIgnoreExport = true;
        }
        protected bool getWorkParams(bool IsAutoWorkParamFilePath, string fileName)
        {
            if (workParamSumDic == null)
            {
                workParamSumDic = new Dictionary<string, Dictionary<int, Dictionary<int, CellWorkParam>>>();
            }
            
            try
            {
                List<string> fileNameList = new List<string>();
                if (IsAutoWorkParamFilePath)
                {
                    bool hasFoundExcel = false;
                    if (System.IO.Directory.Exists(fileName))
                    {
                        System.IO.DirectoryInfo dinfo = new System.IO.DirectoryInfo(fileName);
                        foreach (System.IO.FileInfo file in dinfo.GetFiles())
                        {
                            hasFoundExcel = findExcel(fileNameList, hasFoundExcel, file);
                        }
                    }
                    if (!hasFoundExcel)
                    {
                        writeLog("未找到指定目录下的工参文件");
                        return false;
                    }
                }
                else
                {
                    fileNameList = new List<string> { fileName };
                }

                workParamSumDic.Clear(); 
                foreach (string excelFileName in fileNameList)
                {
                    getWorkParamFormExcle(excelFileName);
                }

            }
            catch (Exception ex)
            {
                writeLog(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
                return false;
            }
            return true;
        }

        private bool findExcel(List<string> fileNameList, bool hasFoundExcel, System.IO.FileInfo file)
        {
            if (FilterHelper.Excel.Contains(file.Extension))
            {
                bool isContains = false;
                for (int i = 0; i < FuncSet.AutoWorkParamRecentDays; i++)
                {
                    string strDate = DateTime.Now.Date.AddDays(-1 * i).ToString("yyyyMMdd");
                    if (file.Name.Contains(strDate))
                    {
                        isContains = true;
                        break;
                    }
                }
                if (isContains)
                {
                    fileNameList.Add(file.FullName);
                    hasFoundExcel = true;
                }
            }

            return hasFoundExcel;
        }

        protected bool getWorkParamFormExcle(string fileName)
        {
            strWorkParamTime = "";
            DataSet dataSet;
            try
            {
                int reReadCount = 0;
                while (reReadCount < 6 && FileStatus.FileIsOpen(fileName) == 1)
                {
                    System.Threading.Thread.Sleep(10000);
                    reReadCount++;
                }
                dataSet = ExcelNPOIManager.ImportFromExcel(fileName);

                if (dataSet == null || dataSet.Tables.Count <= 0)
                {
                    return false;
                }
                System.Data.DataTable tb = dataSet.Tables["工参"];
                if (tb == null || tb.Rows.Count <= 0)
                {
                    tb = dataSet.Tables[0];
                    if (tb == null || tb.Rows.Count <= 0)
                    {
                        return false;
                    }
                }
                getTableInfo(fileName, tb);
            }
            catch (Exception ex)
            {
                writeLog(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
                return false;
            }
            return true;
        }

        private void getTableInfo(string fileName, DataTable tb)
        {
            StringBuilder strbErrorInfo = new StringBuilder();
            int index = 0;
            bool hasRowTypeCol = tb.Columns.Contains("ROWTYPE");
            foreach (DataRow row in tb.Rows)
            {
                index++;

                if (hasRowTypeCol)
                {
                    string strRowtype = row["ROWTYPE"].ToString();
                    if (string.IsNullOrEmpty(strRowtype)
                        || (strRowtype.ToUpper() != "NEW") && !strRowtype.Contains("新增"))
                    {
                        continue;
                    }
                }

                try
                {
                    getRowInfo(row);
                }
                catch
                {
                    strbErrorInfo.AppendLine("第" + index + "行工参信息配置错误!   " + System.DateTime.Now.ToString());
                }
            }
            if (strbErrorInfo.Length > 0)
            {
                writeLog(fileName + strbErrorInfo.ToString());
            }
        }

        private void getRowInfo(DataRow row)
        {
            CellWorkParam info = new CellWorkParam();
            info.DistrictName = row["地市"].ToString().Replace("市", "");
            info.BtsName = row["基站名称"].ToString();
            info.ENodeBID = Convert.ToInt32(row["ENodeBID"]);
            info.Longitude = Convert.ToDouble(row["经度"]);
            info.Latitude = Convert.ToDouble(row["纬度"]);
            info.Address = row["地址"].ToString();
            info.CellName = row["小区名称"].ToString();
            info.Tac = string.IsNullOrEmpty(row["TAC"].ToString()) ? 0 : Convert.ToInt32(row["TAC"]);
            info.CellId = info.SectorID = Convert.ToInt32(row["CellID"]);
            info.ARFCN = string.IsNullOrEmpty(row["ARFCN"].ToString()) ? 0 : Convert.ToInt32(row["ARFCN"]);
            info.PCI = string.IsNullOrEmpty(row["PCI"].ToString()) ? 0 : Convert.ToInt32(row["PCI"]);
            if (row["覆盖类型"] != null)
            {
                info.IsOutDoor = row["覆盖类型"].ToString() != "室内";
            }
            if (row["方向角"] != null)
            {
                info.Direction = string.IsNullOrEmpty(row["方向角"].ToString()) ? 0 : Convert.ToInt32(row["方向角"]);
            }
            try
            {
                info.AcceptDateTime = Convert.ToDateTime(row["时间"].ToString());
            }
            catch
            {
                info.AcceptDateTime = DateTime.Now.Date.AddDays(-1);
            }
            if (string.IsNullOrEmpty(strWorkParamTime))
            {
                if (isAnaByAcceptRules)
                {
                    strWorkParamTime = info.AcceptDateTime.ToString("yyyyMMdd");
                }
                else
                {
                    strWorkParamTime = DateTime.Now.ToString("yyyyMMdd");
                }
            }

            if (!workParamSumDic.ContainsKey(info.DistrictName))
            {
                workParamSumDic[info.DistrictName] = new Dictionary<int, Dictionary<int, CellWorkParam>>();
            }
            if (!workParamSumDic[info.DistrictName].ContainsKey(info.ENodeBID))
            {
                workParamSumDic[info.DistrictName][info.ENodeBID] = new Dictionary<int, CellWorkParam>();
            }
            workParamSumDic[info.DistrictName][info.ENodeBID][info.CellId] = info;
        }

        protected override void query()
        {
            if (MainModel.IsBackground && !MainModel.BackgroundStarted)
            {
                return;
            }
            if (!getCondition())
            {
                return;
            }
            getReadyBeforeQuery();
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                clientProxy.Close();
            }
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = false;

            try
            {
                Dictionary<int, Dictionary<int, CellWorkParam>> curDistrictWorkParam = workParamSumDic[curDistrictName.Replace("市", "")];
                if (curDistrictWorkParam != null)
                {
                    queryInThread(clientProxy, curDistrictWorkParam);
                }
            }
            catch (Exception ex)
            {
                writeLog(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
            }
            finally
            {
                doSomethingAfterQueryThread();
            }
        }
        protected abstract void queryInThread(ClientProxy clientProxy, Dictionary<int, Dictionary<int, CellWorkParam>> curDistrictWorkParamDic);

        protected virtual void doSomethingAfterQueryThread()
        {
        }
        protected void writeLog(string strErr)
        {
            string path = BackgroundFuncManager.BackgroundLogSavePath;

            if (!File.Exists(path))
            {
                File.Create(path).Close();
            }
            using (StreamWriter sw = File.AppendText(path))
            {
                sw.Write(DateTime.Now.ToString() + "  " + curDistrictName + "  [" + this.Name + "]" + strErr + "\r\n");
                sw.Flush();
                sw.Close();
            }
        }

        #region Background
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.单站验收; }
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["OutDoorCallCheck"] = FuncSet.OutDoorCallCheck.Param;
                param["InDoorCallCheck"] = FuncSet.InDoorCallCheck.Param;
                param["DiyStatsRecentDays"] = FuncSet.DiyStatsRecentDays;
                param["ExcelPath"] = FuncSet.ExcelPath;
                param["IsAutoWorkParamFilePath"] = FuncSet.IsAutoWorkParamFilePath;
                param["AutoWorkParamRecentDays"] = FuncSet.AutoWorkParamRecentDays;
                param["FileNameEnodType"] = FuncSet.FileNameEnodType;
                param["FilePath"] = FuncSet.FilePath;
                param["FileCountAutoULFolder"] = FuncSet.FileCountAutoULFolder;
                param["FileCountAutoIsCheck"] = FuncSet.FileCountAutoIsCheck;
                param["AutoAnaDurationMinutes"] = FuncSet.AutoAnaDurationMinutes;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                setValue(param);
            }
        }

        private void setValue(Dictionary<string, object> param)
        {
            BackgroundStat = getValidValue(param, "BackgroundStat", false);
            FuncSet.DiyStatsRecentDays = getValidValue(param, "DiyStatsRecentDays", 2);
            FuncSet.ExcelPath = getValidValue(param, "ExcelPath", "");
            FuncSet.IsAutoWorkParamFilePath = getValidValue(param, "IsAutoWorkParamFilePath", true);
            FuncSet.AutoWorkParamRecentDays = getValidValue(param, "AutoWorkParamRecentDays", 0);
            FuncSet.FileNameEnodType = getValidValue(param, "FileNameEnodType", 0);
            FuncSet.FilePath = getValidValue(param, "FilePath", "");
            FuncSet.FileCountAutoULFolder = getValidValue(param, "FileCountAutoULFolder", "");
            FuncSet.FileCountAutoIsCheck = getValidValue(param, "FileCountAutoIsCheck", true);
            FuncSet.AutoAnaDurationMinutes = getValidValue(param, "AutoAnaDurationMinutes", 0);

            FuncSet.OutDoorCallCheck.Param = getValidValue(param, "OutDoorCallCheck", new Dictionary<string, object>());
            FuncSet.InDoorCallCheck.Param = getValidValue(param, "InDoorCallCheck", new Dictionary<string, object>());
        }

        private T getValidValue<T>(Dictionary<string, object> param, string name, T defaultValue)
        {
            if (param.ContainsKey(name))
            {
                object obj = param[name];
                if (obj != null && obj is T)
                {
                    return (T)obj;
                }
            }
            return defaultValue;
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new StationAcceptProperties_LTE(this);
            }
        }

        protected override void getFilesForAnalyse()
        {
            //
        }
        protected override void saveBackgroundData()
        {
            //
        }
        #endregion
    }

}
