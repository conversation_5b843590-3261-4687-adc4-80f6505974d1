﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTGridDownloadSpeedForm : MinCloseForm
    {
        public ZTGridDownloadSpeedForm(MainModel mainModel, string strStartTime)
            : base(mainModel)
        {
            InitializeComponent();
            this.Text += "   开始时间:" + strStartTime + " _ 结束时间:" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"); 
        }
        public void FillData(List<GridDownloadSpeedAvgInfo> dOwnloadSeeInfoList)
        {
            BindingSource source = new BindingSource();
            source.DataSource = dOwnloadSeeInfoList;
            dataGrid.DataSource = source;
            dataGrid.RefreshDataSource();
        }

        private void outPutExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.gridView1);
        }
    }
}
