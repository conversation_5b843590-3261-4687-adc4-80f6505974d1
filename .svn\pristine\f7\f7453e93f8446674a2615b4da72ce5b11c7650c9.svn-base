﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;
using System.Windows.Forms;
using DevExpress.Utils.Drawing;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.Func
{
    public partial class TDPhysicalChannelForm : ChildForm
    {
        public TDPhysicalChannelForm()
        {
            InitializeComponent();
            this.Load += load;
        }

        public override void Init()
        {
            base.Init();
            MainModel.SelectedTestPointsChanged += selectedTestPointsChanged;
            Disposed += disposed;
        }

        private void disposed(object sender, EventArgs e)
        {
            MainModel.SelectedTestPointsChanged -= selectedTestPointsChanged;
            view.CustomDrawRowIndicator -= view_CustomDrawRowIndicator;
            view.CustomDrawCell -= view_CustomDrawCell;
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic.Add("DownColor", colorEditDown.Color.ToArgb());
                dic.Add("UpColor", colorEditUp.Color.ToArgb());
                return dic;
            }
            set
            {
                if (value == null || value.Count == 0)
                {
                    return;
                }
                object colorStr = null;
                if (value.TryGetValue("DownColor", out colorStr))
                {
                    colorEditDown.Color = Color.FromArgb(int.Parse(colorStr.ToString()));
                }
                if (value.TryGetValue("UpColor", out colorStr))
                {
                    colorEditUp.Color = Color.FromArgb(int.Parse(colorStr.ToString()));
                }
            }
        }

        private List<TSRowInfo> occypyRows = null;
        private void selectedTestPointsChanged(object sender, EventArgs e)
        {
            occypyRows = new List<TSRowInfo>();
            DataTable tb = createDataTableForViewB();
            this.gridCtrlB.DataSource = tb;
            tbxWrkFreq.Text = string.Empty;
            if (MainModel.SelectedTestPoints.Count <= 0 || !(MainModel.SelectedTestPoints[0] is TDTestPointDetail))
            {
                this.gridCtrl.Invalidate();
                this.gridCtrlB.RefreshDataSource();
                return;
            }
            TDTestPointDetail tp = MainModel.SelectedTestPoints[0] as TDTestPointDetail;
            object freq = tp["TD_Phych_WorkFreq"];
            if (freq != null)
            {
                tbxWrkFreq.Text = freq.ToString();
            }
            for (int i = 0; i < 32; i++)
            {
                object value;
                int ts, groupNum, idx;
                string text;
                bool isValid = judgeValidData(tp, i, out value, out ts, out groupNum, out idx, out text);
                if (isValid)
                {
                    int dir = int.Parse(value.ToString());
                    if (dir == 255)
                    {
                        break;
                    }
                    if (dir == 1 || dir == 2)
                    {
                        int singleGrpCellCnt = 16 / groupNum;
                        int fromIdx = (idx - 1) * singleGrpCellCnt;
                        int toIdx = fromIdx + singleGrpCellCnt - 1;
                        int rowIdx = ts - 1;
                        MergeHCellInfo cell = new MergeHCellInfo(rowIdx, fromIdx, toIdx, text);
                        addDataRow(tb, tp, i, groupNum, dir, rowIdx, cell);
                    }
                }
            }
            this.gridCtrl.Invalidate();
            this.gridCtrlB.RefreshDataSource();
        }

        private void addDataRow(DataTable tb, TDTestPointDetail tp, int i, int groupNum, int dir, int rowIdx, MergeHCellInfo cell)
        {
            TSRowInfo row = occypyRows.Find(delegate (TSRowInfo s) { return s.Handle == rowIdx; });
            if (row == null)
            {
                row = new TSRowInfo(rowIdx, groupNum, dir);
                occypyRows.Add(row);
            }
            row.OccypyCells.Add(cell);
            DataRow dRow = tb.Rows[rowIdx];
            dRow["Repetition Period"] = dir == 1 ? tp["TD_Phych_RepePeriDL", i] : tp["TD_Phych_RepePeriUL", i];
            dRow["Repetition Length"] = dir == 1 ? tp["TD_Phych_RepeLenDL", i] : tp["TD_Phych_RepeLenUL", i];
            dRow["Midamble Allocation"] = tp["TD_Phych_MA_ModeSet", i];
            dRow["Midamble Configuration"] = tp["TD_Phych_Midamble_Cfg", i];
            dRow["Midamble Shift"] = tp["TD_Phych_MA_ShiftSet", i];
        }

        private bool judgeValidData(TDTestPointDetail tp, int i, out object value, out int ts, out int groupNum, out int idx, out string text)
        {
            ts = 0;
            groupNum = 0;
            idx = 0;
            text = "";

            value = tp["TD_Phych_Timeslot", i];
            if (value == null)
            {
                return false;
            }
            ts = int.Parse(value.ToString());
            if (ts < 1 || ts > 6)
            {
                return false;
            }
            value = tp["TD_Phych_Channel_code", i];
            if (value == null)
            {
                return false;
            }
            int code = int.Parse(value.ToString());
            text = tp.GetChannelCodeStringByDBCode(code, out groupNum, out idx);
            if (string.IsNullOrEmpty(text))
            {
                return false;
            }
            value = tp["TD_Phych_direction", i];
            if (value == null)
            {
                return false;
            }
            return true;
        }

        private DataTable createDataTableForViewB()
        {
            DataTable tb = new DataTable();
            tb.Columns.Add("Repetition Period", typeof(object));
            tb.Columns.Add("Repetition Length", typeof(object));
            tb.Columns.Add("Midamble Allocation", typeof(object));
            tb.Columns.Add("Midamble Configuration", typeof(object));
            tb.Columns.Add("Midamble Shift", typeof(object));
            for (int i = 0; i < 6; i++)
            {
                DataRow row = tb.NewRow();
                tb.Rows.Add(row);
            }
            return tb;
        }

        void load(object sender, EventArgs e)
        {
            initDataView();
        }

        private void initDataView()
        {
            gridCtrl.DataSource = new DataTable();
            for (int i = 0; i < view.Columns.Count; i++)
            {
                view.Columns[i].Caption = (i + 1).ToString();
            }
            for (int i = 0; i < 6; i++)
            {
                view.AddNewRow();
            }
            gridCtrl.RefreshDataSource();
            view.CustomDrawRowIndicator += view_CustomDrawRowIndicator;
            view.CustomDrawCell += view_CustomDrawCell;
        }

        void view_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            e.Handled = true;
        }

        void view_CustomDrawRowIndicator(object sender, DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventArgs e)
        {
            int rowIndex = e.RowHandle;
            if (rowIndex >= 0)
            {
                rowIndex++;
                e.Info.DisplayText = "TS" + rowIndex.ToString();
            }
            e.Info.ImageIndex = -1;
        }

        private void colorEdit_EditValueChanged(object sender, EventArgs e)
        {
            this.gridCtrl.Invalidate();
            this.gridCtrlB.Invalidate();
        }

        private void gridCtrl_Paint(object sender, PaintEventArgs e)
        {
            if (occypyRows != null)
            {
                foreach (TSRowInfo row in occypyRows)
                {
                    paintRow(e, row);
                }
            }
        }

        GraphicsPath arrowPath = null;
        private void initArrowPath()
        {
            Point[] pnts = new Point[] 
                {
                    new Point(1, -2), 
                    new Point(12, -2), 
                    new Point(12, -6), 
                    new Point(18, 0), 
                    new Point(12 , 6), 
                    new Point(12 , 2), 
                    new Point(1, 2), 
                };
            arrowPath = new GraphicsPath();
            arrowPath.AddPolygon(pnts);
        }

        private void paintRow(PaintEventArgs e, TSRowInfo row)
        {
            GridViewInfo vi = view.GetViewInfo() as GridViewInfo;
            int fromIdx = 0;
            GraphicsCache cache = new GraphicsCache(e.Graphics);
            for (int i = 0; i < row.GroupCnt; i++)
            {
                GridCellInfo cell1 = vi.GetGridCellInfo(row.Handle, fromIdx);
                int toIdx = fromIdx + (16 / row.GroupCnt) - 1;
                GridCellInfo cell2 = vi.GetGridCellInfo(row.Handle, toIdx);
                if (cell1 != null && cell2 != null)
                {
                    Rectangle targetRect = Rectangle.Union(cell1.Bounds, cell2.Bounds);
                    e.Graphics.FillRectangle(Brushes.White, targetRect);
                    fromIdx = toIdx + 1;
                }
            }
            foreach (MergeHCellInfo mCell in row.OccypyCells)
            {
                GridCellInfo gridCellInfo1 = vi.GetGridCellInfo(row.Handle, mCell.FromColIdx);
                GridCellInfo gridCellInfo2 = vi.GetGridCellInfo(row.Handle, mCell.ToColIdx);
                if (gridCellInfo1 != null && gridCellInfo2 != null)
                {
                    draw(e, row, cache, mCell, gridCellInfo1, gridCellInfo2);
                }
            }
        }

        private void draw(PaintEventArgs e, TSRowInfo row, GraphicsCache cache, MergeHCellInfo mCell, GridCellInfo gridCellInfo1, GridCellInfo gridCellInfo2)
        {
            Rectangle targetRect = Rectangle.Union(gridCellInfo1.Bounds, gridCellInfo2.Bounds);
            if (arrowPath == null)
            {
                initArrowPath();
            }
            int midX = gridCellInfo1.Bounds.X + targetRect.Width / 2;
            if (row.Direction == 1)
            {
                gridCellInfo1.Appearance.BackColor = colorEditDown.Color;
                gridCellInfo1.Appearance.FillRectangle(cache, targetRect);
                e.Graphics.TranslateTransform(midX, targetRect.Y);
                e.Graphics.RotateTransform(90);
                e.Graphics.FillPath(Brushes.Yellow, arrowPath);
            }
            else if (row.Direction == 2)
            {
                gridCellInfo1.Appearance.BackColor = colorEditUp.Color;
                gridCellInfo1.Appearance.FillRectangle(cache, targetRect);
                e.Graphics.TranslateTransform(midX, targetRect.Y + targetRect.Height);
                e.Graphics.RotateTransform(-90);
                e.Graphics.FillPath(Brushes.Blue, arrowPath);
            }
            e.Graphics.ResetTransform();

            Font font = gridCtrl.Font;
            SizeF size = e.Graphics.MeasureString(mCell.CellText, font);
            while (size.Width > targetRect.Width && font.Size > 1)
            {
                font = new Font(font.FontFamily, font.Size - 1);
                size = e.Graphics.MeasureString(mCell.CellText, font);
            }
            e.Graphics.DrawString(mCell.CellText, font, Brushes.Black, targetRect);
        }

        private void viweB_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            TSRowInfo row = occypyRows.Find(delegate(TSRowInfo s) { return s.Handle == e.RowHandle; });
            if (row == null)
            {
                return;
            }
            e.Appearance.BackColor2 = e.Appearance.BackColor
                = row.Direction == 1 ? colorEditDown.Color : colorEditUp.Color;
        }

    }

    class TSRowInfo
    {
        public TSRowInfo(int rowHandle, int mergeGrpCnt, int direction)
        {
            this.rowHandle = rowHandle;
            this.grpCnt = mergeGrpCnt;
            this.direction = direction;
            occupyCells = new List<MergeHCellInfo>();
        }
        private readonly int direction = 0;
        /// <summary>
        /// up or down(1 for down;2 for up)
        /// </summary>
        public int Direction
        {
            get { return direction; }
        }
        private readonly int rowHandle;
        public int Handle
        {
            get { return rowHandle; }
        }
        private readonly int grpCnt;
        public int GroupCnt
        {
            get { return grpCnt; }
        }
        private readonly List<MergeHCellInfo> occupyCells;
        public List<MergeHCellInfo> OccypyCells
        {
            get { return occupyCells; }
        }
    }

    class MergeHCellInfo
    {
        public MergeHCellInfo(int rowIdx, int fromColIdx, int toColIdx, string text)
        {
            this.rowIdx = rowIdx;
            this.fromColIdx = fromColIdx;
            this.toColIdx = toColIdx;
            this.cellText = text;
        }
        readonly int rowIdx;
        public int RowIdx
        {
            get { return rowIdx; }
        }
        readonly int fromColIdx;
        public int FromColIdx
        {
            get { return fromColIdx; }
        }
        readonly int toColIdx;
        public int ToColIdx
        {
            get { return toColIdx; }
        }
        readonly string cellText;
        public string CellText
        {
            get { return cellText; }
        }
    }
}
