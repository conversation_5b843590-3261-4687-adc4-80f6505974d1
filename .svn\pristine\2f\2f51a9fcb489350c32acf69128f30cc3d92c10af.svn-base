﻿using DevExpress.XtraEditors.Controls;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Stat
{
    public partial class AreaSelectBox : BaseDialog
    {
        public AreaSelectBox()
        {
            InitializeComponent();
            initComboBoxArea();
        }

        private void initComboBoxArea()
        {
            foreach (CategoryEnumItem item in ((CategoryEnum)MainModel.CategoryManager["AreaType"]).Items)
            {
                comboBoxArea.Properties.Items.Add(item);
            }
            comboBoxArea.SelectedItem = comboBoxArea.Properties.Items[0];
        }

        public CategoryEnumItem AreaItem { get; set; }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            AreaItem = (CategoryEnumItem)comboBoxArea.SelectedItem;
            DialogResult = DialogResult.OK;
        }
    }
}
