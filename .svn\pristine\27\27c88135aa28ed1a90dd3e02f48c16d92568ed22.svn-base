﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLteNBCellOmitAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTLteNBCellOmitAnaListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewNBOmit = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnEARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCfgNBList = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMRCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgRSRP = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnScore = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatus = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDTHO = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDTNBCfg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDTSIB1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDTMR = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.tabControlNBCellOmit = new System.Windows.Forms.TabControl();
            this.tabFileList = new System.Windows.Forms.TabPage();
            this.tabStat = new System.Windows.Forms.TabPage();
            this.ListViewNBOmitStat = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellEARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatMRCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellRSRP = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatScore = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatStatus = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewNBOmit)).BeginInit();
            this.tabControlNBCellOmit.SuspendLayout();
            this.tabFileList.SuspendLayout();
            this.tabStat.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewNBOmitStat)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(153, 98);
            this.ctxMenu.Opening += new System.ComponentModel.CancelEventHandler(this.ctxMenu_Opening);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(152, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(149, 6);
            this.toolStripMenuItem1.Visible = false;
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(152, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(152, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // ListViewNBOmit
            // 
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnSN);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnFileName);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnCellName);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnTAC);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnECI);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnEARFCN);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnPCI);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnCfgNBList);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnMRCount);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnAvgRSRP);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnScore);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnStatus);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnDistance);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnLongitude);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnLatitude);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnDTHO);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnDTNBCfg);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnDTSIB1);
            this.ListViewNBOmit.AllColumns.Add(this.olvColumnDTMR);
            this.ListViewNBOmit.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.ListViewNBOmit.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnFileName,
            this.olvColumnCellName,
            this.olvColumnTAC,
            this.olvColumnECI,
            this.olvColumnEARFCN,
            this.olvColumnPCI,
            this.olvColumnCfgNBList,
            this.olvColumnMRCount,
            this.olvColumnAvgRSRP,
            this.olvColumnScore,
            this.olvColumnStatus,
            this.olvColumnDistance,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnDTHO,
            this.olvColumnDTNBCfg,
            this.olvColumnDTSIB1,
            this.olvColumnDTMR});
            this.ListViewNBOmit.ContextMenuStrip = this.ctxMenu;
            this.ListViewNBOmit.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewNBOmit.FullRowSelect = true;
            this.ListViewNBOmit.GridLines = true;
            this.ListViewNBOmit.HeaderWordWrap = true;
            this.ListViewNBOmit.IsNeedShowOverlay = false;
            this.ListViewNBOmit.Location = new System.Drawing.Point(6, 3);
            this.ListViewNBOmit.Name = "ListViewNBOmit";
            this.ListViewNBOmit.OwnerDraw = true;
            this.ListViewNBOmit.ShowGroups = false;
            this.ListViewNBOmit.Size = new System.Drawing.Size(1208, 464);
            this.ListViewNBOmit.TabIndex = 5;
            this.ListViewNBOmit.UseCompatibleStateImageBehavior = false;
            this.ListViewNBOmit.View = System.Windows.Forms.View.Details;
            this.ListViewNBOmit.VirtualMode = true;
            this.ListViewNBOmit.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 120;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 120;
            // 
            // olvColumnTAC
            // 
            this.olvColumnTAC.HeaderFont = null;
            this.olvColumnTAC.Text = "TAC";
            // 
            // olvColumnECI
            // 
            this.olvColumnECI.HeaderFont = null;
            this.olvColumnECI.Text = "ECI";
            // 
            // olvColumnEARFCN
            // 
            this.olvColumnEARFCN.HeaderFont = null;
            this.olvColumnEARFCN.Text = "EARFCN";
            // 
            // olvColumnPCI
            // 
            this.olvColumnPCI.HeaderFont = null;
            this.olvColumnPCI.Text = "PCI";
            // 
            // olvColumnCfgNBList
            // 
            this.olvColumnCfgNBList.HeaderFont = null;
            this.olvColumnCfgNBList.Text = "邻区配置表";
            this.olvColumnCfgNBList.Width = 150;
            // 
            // olvColumnMRCount
            // 
            this.olvColumnMRCount.HeaderFont = null;
            this.olvColumnMRCount.Text = "MR次数";
            // 
            // olvColumnAvgRSRP
            // 
            this.olvColumnAvgRSRP.HeaderFont = null;
            this.olvColumnAvgRSRP.Text = "平均RSRP";
            // 
            // olvColumnScore
            // 
            this.olvColumnScore.HeaderFont = null;
            this.olvColumnScore.Text = "邻区得分";
            // 
            // olvColumnStatus
            // 
            this.olvColumnStatus.HeaderFont = null;
            this.olvColumnStatus.Text = "检测结果";
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "距离(米)";
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 80;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 80;
            // 
            // olvColumnDTHO
            // 
            this.olvColumnDTHO.HeaderFont = null;
            this.olvColumnDTHO.Text = "切换时间";
            this.olvColumnDTHO.Width = 80;
            // 
            // olvColumnDTNBCfg
            // 
            this.olvColumnDTNBCfg.HeaderFont = null;
            this.olvColumnDTNBCfg.Text = "邻区配置时间";
            // 
            // olvColumnDTSIB1
            // 
            this.olvColumnDTSIB1.HeaderFont = null;
            this.olvColumnDTSIB1.Text = "SIB1时间";
            // 
            // olvColumnDTMR
            // 
            this.olvColumnDTMR.HeaderFont = null;
            this.olvColumnDTMR.Text = "MR时间";
            // 
            // tabControlNBCellOmit
            // 
            this.tabControlNBCellOmit.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tabControlNBCellOmit.Controls.Add(this.tabFileList);
            this.tabControlNBCellOmit.Controls.Add(this.tabStat);
            this.tabControlNBCellOmit.Location = new System.Drawing.Point(-3, 1);
            this.tabControlNBCellOmit.Name = "tabControlNBCellOmit";
            this.tabControlNBCellOmit.SelectedIndex = 0;
            this.tabControlNBCellOmit.Size = new System.Drawing.Size(1225, 500);
            this.tabControlNBCellOmit.TabIndex = 6;
            this.tabControlNBCellOmit.SelectedIndexChanged += new System.EventHandler(this.tabControlNBCellOmit_SelectedIndexChanged);
            // 
            // tabFileList
            // 
            this.tabFileList.Controls.Add(this.ListViewNBOmit);
            this.tabFileList.Location = new System.Drawing.Point(4, 23);
            this.tabFileList.Name = "tabFileList";
            this.tabFileList.Padding = new System.Windows.Forms.Padding(3);
            this.tabFileList.Size = new System.Drawing.Size(1217, 473);
            this.tabFileList.TabIndex = 0;
            this.tabFileList.Text = "检测详情";
            this.tabFileList.UseVisualStyleBackColor = true;
            // 
            // tabStat
            // 
            this.tabStat.Controls.Add(this.ListViewNBOmitStat);
            this.tabStat.Location = new System.Drawing.Point(4, 23);
            this.tabStat.Name = "tabStat";
            this.tabStat.Padding = new System.Windows.Forms.Padding(3);
            this.tabStat.Size = new System.Drawing.Size(1217, 473);
            this.tabStat.TabIndex = 1;
            this.tabStat.Text = "统计结果";
            this.tabStat.UseVisualStyleBackColor = true;
            // 
            // ListViewNBOmitStat
            // 
            this.ListViewNBOmitStat.AllColumns.Add(this.olvColumnStatSN);
            this.ListViewNBOmitStat.AllColumns.Add(this.olvColumnStatCellName);
            this.ListViewNBOmitStat.AllColumns.Add(this.olvColumnStatCellTAC);
            this.ListViewNBOmitStat.AllColumns.Add(this.olvColumnStatCellECI);
            this.ListViewNBOmitStat.AllColumns.Add(this.olvColumnStatCellEARFCN);
            this.ListViewNBOmitStat.AllColumns.Add(this.olvColumnStatCellPCI);
            this.ListViewNBOmitStat.AllColumns.Add(this.olvColumnStatMRCount);
            this.ListViewNBOmitStat.AllColumns.Add(this.olvColumnStatCellRSRP);
            this.ListViewNBOmitStat.AllColumns.Add(this.olvColumnStatScore);
            this.ListViewNBOmitStat.AllColumns.Add(this.olvColumnStatStatus);
            this.ListViewNBOmitStat.AllColumns.Add(this.olvColumnStatDistance);
            this.ListViewNBOmitStat.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.ListViewNBOmitStat.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnStatCellName,
            this.olvColumnStatCellTAC,
            this.olvColumnStatCellECI,
            this.olvColumnStatCellEARFCN,
            this.olvColumnStatCellPCI,
            this.olvColumnStatMRCount,
            this.olvColumnStatCellRSRP,
            this.olvColumnStatScore,
            this.olvColumnStatStatus,
            this.olvColumnStatDistance});
            this.ListViewNBOmitStat.ContextMenuStrip = this.ctxMenu;
            this.ListViewNBOmitStat.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewNBOmitStat.FullRowSelect = true;
            this.ListViewNBOmitStat.GridLines = true;
            this.ListViewNBOmitStat.HeaderWordWrap = true;
            this.ListViewNBOmitStat.IsNeedShowOverlay = false;
            this.ListViewNBOmitStat.Location = new System.Drawing.Point(4, 4);
            this.ListViewNBOmitStat.Name = "ListViewNBOmitStat";
            this.ListViewNBOmitStat.OwnerDraw = true;
            this.ListViewNBOmitStat.ShowGroups = false;
            this.ListViewNBOmitStat.Size = new System.Drawing.Size(1208, 464);
            this.ListViewNBOmitStat.TabIndex = 6;
            this.ListViewNBOmitStat.UseCompatibleStateImageBehavior = false;
            this.ListViewNBOmitStat.View = System.Windows.Forms.View.Details;
            this.ListViewNBOmitStat.VirtualMode = true;
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            // 
            // olvColumnStatCellName
            // 
            this.olvColumnStatCellName.HeaderFont = null;
            this.olvColumnStatCellName.Text = "小区名称";
            this.olvColumnStatCellName.Width = 120;
            // 
            // olvColumnStatCellTAC
            // 
            this.olvColumnStatCellTAC.HeaderFont = null;
            this.olvColumnStatCellTAC.Text = "TAC";
            this.olvColumnStatCellTAC.Width = 80;
            // 
            // olvColumnStatCellECI
            // 
            this.olvColumnStatCellECI.HeaderFont = null;
            this.olvColumnStatCellECI.Text = "ECI";
            this.olvColumnStatCellECI.Width = 80;
            // 
            // olvColumnStatCellEARFCN
            // 
            this.olvColumnStatCellEARFCN.HeaderFont = null;
            this.olvColumnStatCellEARFCN.Text = "EARFCN";
            this.olvColumnStatCellEARFCN.Width = 80;
            // 
            // olvColumnStatCellPCI
            // 
            this.olvColumnStatCellPCI.HeaderFont = null;
            this.olvColumnStatCellPCI.Text = "PCI";
            // 
            // olvColumnStatMRCount
            // 
            this.olvColumnStatMRCount.HeaderFont = null;
            this.olvColumnStatMRCount.Text = "上报MR数";
            this.olvColumnStatMRCount.Width = 80;
            // 
            // olvColumnStatCellRSRP
            // 
            this.olvColumnStatCellRSRP.HeaderFont = null;
            this.olvColumnStatCellRSRP.Text = "平均RSRP";
            this.olvColumnStatCellRSRP.Width = 80;
            // 
            // olvColumnStatScore
            // 
            this.olvColumnStatScore.HeaderFont = null;
            this.olvColumnStatScore.Text = "总得分";
            this.olvColumnStatScore.Width = 80;
            // 
            // olvColumnStatStatus
            // 
            this.olvColumnStatStatus.HeaderFont = null;
            this.olvColumnStatStatus.Text = "检测结果";
            this.olvColumnStatStatus.Width = 80;
            // 
            // olvColumnStatDistance
            // 
            this.olvColumnStatDistance.HeaderFont = null;
            this.olvColumnStatDistance.Text = "距离(米)";
            this.olvColumnStatDistance.Width = 80;
            // 
            // ZTLteNBCellOmitAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1222, 502);
            this.Controls.Add(this.tabControlNBCellOmit);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTLteNBCellOmitAnaListForm";
            this.Text = "LTE邻区漏配分析结果";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewNBOmit)).EndInit();
            this.tabControlNBCellOmit.ResumeLayout(false);
            this.tabFileList.ResumeLayout(false);
            this.tabStat.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewNBOmitStat)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private BrightIdeasSoftware.TreeListView ListViewNBOmit;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnCfgNBList;
        private BrightIdeasSoftware.OLVColumn olvColumnMRCount;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRSRP;
        private BrightIdeasSoftware.OLVColumn olvColumnECI;
        private BrightIdeasSoftware.OLVColumn olvColumnEARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI;
        private BrightIdeasSoftware.OLVColumn olvColumnStatus;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnScore;
        private System.Windows.Forms.TabControl tabControlNBCellOmit;
        private System.Windows.Forms.TabPage tabFileList;
        private System.Windows.Forms.TabPage tabStat;
        private BrightIdeasSoftware.TreeListView ListViewNBOmitStat;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellECI;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellEARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellPCI;
        private BrightIdeasSoftware.OLVColumn olvColumnStatMRCount;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellRSRP;
        private BrightIdeasSoftware.OLVColumn olvColumnStatScore;
        private BrightIdeasSoftware.OLVColumn olvColumnStatDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnStatStatus;
        private BrightIdeasSoftware.OLVColumn olvColumnDTHO;
        private BrightIdeasSoftware.OLVColumn olvColumnDTNBCfg;
        private BrightIdeasSoftware.OLVColumn olvColumnDTSIB1;
        private BrightIdeasSoftware.OLVColumn olvColumnDTMR;

    }
}