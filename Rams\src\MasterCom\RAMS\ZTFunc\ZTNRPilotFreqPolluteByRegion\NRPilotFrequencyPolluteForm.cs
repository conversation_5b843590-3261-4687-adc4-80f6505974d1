﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRPilotFrequencyPolluteForm : MinCloseForm
    {
        MapForm mapForm;
        List<NRPilotFrequencyPolluteBlock> blockList = new List<NRPilotFrequencyPolluteBlock>();
        NRPilotFrequencyPolluteBlockLayer layer;

        public NRPilotFrequencyPolluteForm()
        {
            InitializeComponent();
        }

        public void FillData()
        {
            foreach (NRPilotFrequencyPolluteBlock item in MainModel.CurPilotFrequencyPolluteBlockList_NR)
            {
                blockList.Add(item);
            }
            gcDetail.DataSource = blockList;
            gcDetail.RefreshDataSource();

            mapForm = MainModel.MainForm.GetMapForm();
            //设置图层
            MTGis.LayerBase clayer = mapForm.GetTempLayerBase(typeof(NRPilotFrequencyPolluteBlockLayer));
            layer = clayer as NRPilotFrequencyPolluteBlockLayer;
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            export2ExcelNPOI();
        }

        private void export2ExcelNPOI()
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            rows.Add(createTitleRow());
            foreach (NRPilotFrequencyPolluteBlock block in blockList)
            {
                NPOIRow row = new NPOIRow();
                row.AddCellValue(block.ID);
                row.AddCellValue(block.RoadPlaceDesc);
                row.AddCellValue(block.LongitudeMid);
                row.AddCellValue(block.LatitudeMid);
                row.AddCellValue(block.TotalTestPointCount);
                row.AddCellValue(block.TestPointCount);
                row.AddCellValue(block.BadSampleScale);
                foreach (NRCellOfPilotFrequencyPolluteBlock item in block.CellDic.Values)
                {
                    NPOIRow subRow = new NPOIRow();
                    subRow.AddCellValue(item.SN);
                    subRow.AddCellValue(item.NRCell.Name);
                    subRow.AddCellValue(item.NRCell.SSBARFCN);
                    subRow.AddCellValue(item.NRCell.PCI);
                    subRow.AddCellValue(item.NRCell.TAC);
                    subRow.AddCellValue(item.NRCell.NCI);
                    subRow.AddCellValue(item.NRCell.Longitude);
                    subRow.AddCellValue(item.NRCell.Latitude);
                    subRow.AddCellValue(item.PCCPCH_RSCPAvg);
                    subRow.AddCellValue(item.SINRAvg);
                    row.AddSubRow(subRow);
                }
                rows.Add(row);
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private NPOIRow createTitleRow()
        {
            NPOIRow row = new NPOIRow();
            row.AddCellValue("序号");
            row.AddCellValue("道路名");
            row.AddCellValue("经度");
            row.AddCellValue("纬度");
            row.AddCellValue("总采样点数");
            row.AddCellValue("问题采样点数");
            row.AddCellValue("问题采样点比例(%)");
            row.AddCellValue("子序号");
            row.AddCellValue("小区名");
            row.AddCellValue("ARFCN");
            row.AddCellValue("PCI");
            row.AddCellValue("TAC");
            row.AddCellValue("NCI");
            row.AddCellValue("经度");
            row.AddCellValue("纬度");
            row.AddCellValue("平均RSRP");
            row.AddCellValue("平均SINR");
            return row;
        }
    }
}
