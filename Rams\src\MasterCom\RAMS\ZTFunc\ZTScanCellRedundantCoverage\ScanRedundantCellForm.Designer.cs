﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ScanRedundantCellForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ScanRedundantCellForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcelSimple = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.btnCancelCloud = new DevExpress.XtraEditors.SimpleButton();
            this.btnDrawCloud = new DevExpress.XtraEditors.SimpleButton();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.radioBandType = new DevExpress.XtraEditors.RadioGroup();
            this.labelControlType = new DevExpress.XtraEditors.LabelControl();
            this.btnColorSetting = new DevExpress.XtraEditors.SimpleButton();
            this.radioGroupType = new DevExpress.XtraEditors.RadioGroup();
            this.listViewTotal = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellLacCi = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRedundantLevel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMainCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistacnce = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMainCellRxLev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMainCellFrequence = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioBandType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.miExportExcelSimple,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(190, 120);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(189, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // miExportExcelSimple
            // 
            this.miExportExcelSimple.Name = "miExportExcelSimple";
            this.miExportExcelSimple.Size = new System.Drawing.Size(189, 22);
            this.miExportExcelSimple.Text = "导出简单信息到Excel";
            this.miExportExcelSimple.Click += new System.EventHandler(this.miExportExcelSimple_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(186, 6);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(189, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(189, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.btnCancelCloud);
            this.groupControl1.Controls.Add(this.btnDrawCloud);
            this.groupControl1.Controls.Add(this.groupBox1);
            this.groupControl1.Controls.Add(this.labelControlType);
            this.groupControl1.Controls.Add(this.btnColorSetting);
            this.groupControl1.Controls.Add(this.radioGroupType);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1118, 91);
            this.groupControl1.TabIndex = 5;
            this.groupControl1.Text = "GIS显示设置";
            // 
            // btnCancelCloud
            // 
            this.btnCancelCloud.Enabled = false;
            this.btnCancelCloud.Location = new System.Drawing.Point(349, 46);
            this.btnCancelCloud.Name = "btnCancelCloud";
            this.btnCancelCloud.Size = new System.Drawing.Size(75, 23);
            this.btnCancelCloud.TabIndex = 6;
            this.btnCancelCloud.Text = "撤销云图";
            this.btnCancelCloud.Click += new System.EventHandler(this.btnCancelCloud_Click);
            // 
            // btnDrawCloud
            // 
            this.btnDrawCloud.Location = new System.Drawing.Point(258, 46);
            this.btnDrawCloud.Name = "btnDrawCloud";
            this.btnDrawCloud.Size = new System.Drawing.Size(75, 23);
            this.btnDrawCloud.TabIndex = 5;
            this.btnDrawCloud.Text = "显示云图";
            this.btnDrawCloud.Click += new System.EventHandler(this.btnDrawCloud_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.radioBandType);
            this.groupBox1.Location = new System.Drawing.Point(12, 25);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(230, 61);
            this.groupBox1.TabIndex = 3;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "云图设置";
            // 
            // radioBandType
            // 
            this.radioBandType.Location = new System.Drawing.Point(6, 21);
            this.radioBandType.Name = "radioBandType";
            this.radioBandType.Properties.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.radioBandType.Properties.Appearance.Options.UseBackColor = true;
            this.radioBandType.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.radioBandType.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "GSM900"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "DCS1800"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "全部")});
            this.radioBandType.Size = new System.Drawing.Size(250, 27);
            this.radioBandType.TabIndex = 0;
            this.radioBandType.SelectedIndexChanged += new System.EventHandler(this.radioBandType_SelectedIndexChanged);
            // 
            // labelControlType
            // 
            this.labelControlType.Location = new System.Drawing.Point(583, 34);
            this.labelControlType.Name = "labelControlType";
            this.labelControlType.Size = new System.Drawing.Size(72, 14);
            this.labelControlType.TabIndex = 2;
            this.labelControlType.Text = "覆盖度类别：";
            this.labelControlType.Visible = false;
            // 
            // btnColorSetting
            // 
            this.btnColorSetting.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnColorSetting.Location = new System.Drawing.Point(1024, 34);
            this.btnColorSetting.Name = "btnColorSetting";
            this.btnColorSetting.Size = new System.Drawing.Size(87, 27);
            this.btnColorSetting.TabIndex = 1;
            this.btnColorSetting.Text = "着色设置...";
            this.btnColorSetting.Click += new System.EventHandler(this.btnColorSetting_Click);
            // 
            // radioGroupType
            // 
            this.radioGroupType.EditValue = true;
            this.radioGroupType.Location = new System.Drawing.Point(670, 28);
            this.radioGroupType.Name = "radioGroupType";
            this.radioGroupType.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(true, "相对覆盖度(-12dBm内)"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "绝对覆盖度( > -80dBm)")});
            this.radioGroupType.Size = new System.Drawing.Size(335, 33);
            this.radioGroupType.TabIndex = 0;
            this.radioGroupType.Visible = false;
            this.radioGroupType.SelectedIndexChanged += new System.EventHandler(this.radioGroupType_SelectedIndexChanged);
            // 
            // listViewTotal
            // 
            this.listViewTotal.AllColumns.Add(this.olvColumnSN);
            this.listViewTotal.AllColumns.Add(this.olvColumnCellName);
            this.listViewTotal.AllColumns.Add(this.olvColumnCellLacCi);
            this.listViewTotal.AllColumns.Add(this.olvColumnRxLev);
            this.listViewTotal.AllColumns.Add(this.olvColumnRedundantLevel);
            this.listViewTotal.AllColumns.Add(this.olvColumnMainCellName);
            this.listViewTotal.AllColumns.Add(this.olvColumnDistacnce);
            this.listViewTotal.AllColumns.Add(this.olvColumnMainCellRxLev);
            this.listViewTotal.AllColumns.Add(this.olvColumnMainCellFrequence);
            this.listViewTotal.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnCellName,
            this.olvColumnCellLacCi,
            this.olvColumnRxLev,
            this.olvColumnRedundantLevel,
            this.olvColumnMainCellName,
            this.olvColumnDistacnce,
            this.olvColumnMainCellRxLev,
            this.olvColumnMainCellFrequence});
            this.listViewTotal.ContextMenuStrip = this.ctxMenu;
            this.listViewTotal.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewTotal.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewTotal.FullRowSelect = true;
            this.listViewTotal.GridLines = true;
            this.listViewTotal.HeaderWordWrap = true;
            this.listViewTotal.IsNeedShowOverlay = false;
            this.listViewTotal.Location = new System.Drawing.Point(0, 91);
            this.listViewTotal.Name = "listViewTotal";
            this.listViewTotal.OwnerDraw = true;
            this.listViewTotal.ShowGroups = false;
            this.listViewTotal.Size = new System.Drawing.Size(1118, 411);
            this.listViewTotal.TabIndex = 6;
            this.listViewTotal.UseCompatibleStateImageBehavior = false;
            this.listViewTotal.View = System.Windows.Forms.View.Details;
            this.listViewTotal.VirtualMode = true;
            this.listViewTotal.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 70;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 100;
            // 
            // olvColumnCellLacCi
            // 
            this.olvColumnCellLacCi.HeaderFont = null;
            this.olvColumnCellLacCi.Text = "小区LAC-CI";
            this.olvColumnCellLacCi.Width = 100;
            // 
            // olvColumnRxLev
            // 
            this.olvColumnRxLev.HeaderFont = null;
            this.olvColumnRxLev.Text = "平均场强";
            this.olvColumnRxLev.Width = 100;
            // 
            // olvColumnRedundantLevel
            // 
            this.olvColumnRedundantLevel.HeaderFont = null;
            this.olvColumnRedundantLevel.Text = "冗余覆盖度";
            this.olvColumnRedundantLevel.Width = 100;
            // 
            // olvColumnMainCellName
            // 
            this.olvColumnMainCellName.HeaderFont = null;
            this.olvColumnMainCellName.Text = "主强小区";
            this.olvColumnMainCellName.Width = 110;
            // 
            // olvColumnDistacnce
            // 
            this.olvColumnDistacnce.HeaderFont = null;
            this.olvColumnDistacnce.Text = "两小区距离(米)";
            this.olvColumnDistacnce.Width = 100;
            // 
            // olvColumnMainCellRxLev
            // 
            this.olvColumnMainCellRxLev.HeaderFont = null;
            this.olvColumnMainCellRxLev.Text = "主强小区平均场强";
            this.olvColumnMainCellRxLev.Width = 110;
            // 
            // olvColumnMainCellFrequence
            // 
            this.olvColumnMainCellFrequence.HeaderFont = null;
            this.olvColumnMainCellFrequence.Text = "主强小区受影响次数";
            this.olvColumnMainCellFrequence.Width = 120;
            // 
            // ScanRedundantCellForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1118, 502);
            this.Controls.Add(this.listViewTotal);
            this.Controls.Add(this.groupControl1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ScanRedundantCellForm";
            this.Text = "GSM小区冗余覆盖";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.radioBandType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.LabelControl labelControlType;
        private DevExpress.XtraEditors.SimpleButton btnColorSetting;
        private DevExpress.XtraEditors.RadioGroup radioGroupType;
        private BrightIdeasSoftware.TreeListView listViewTotal;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellLacCi;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev;
        private BrightIdeasSoftware.OLVColumn olvColumnRedundantLevel;
        private BrightIdeasSoftware.OLVColumn olvColumnMainCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnDistacnce;
        private BrightIdeasSoftware.OLVColumn olvColumnMainCellRxLev;
        private BrightIdeasSoftware.OLVColumn olvColumnMainCellFrequence;
        private System.Windows.Forms.ToolStripMenuItem miExportExcelSimple;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.RadioGroup radioBandType;
        private DevExpress.XtraEditors.SimpleButton btnCancelCloud;
        private DevExpress.XtraEditors.SimpleButton btnDrawCloud;

    }
}