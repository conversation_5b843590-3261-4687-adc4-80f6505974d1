﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Stat.Data;
using System.IO;
using System.Data;
using System.Data.SqlClient;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;
using System.ComponentModel;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteScanAntenna : ZTAntennaBase
    {
        public ZTLteScanAntenna()
            : base(MainModel.GetInstance())
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_SCAN_TOPN);
#if LT
            carrierID = CarrierType.ChinaUnicom;
#else
            carrierID = CarrierType.ChinaMobile;
#endif

            BDtNbInfo = false;
            BSecAna = false;
            BSecDataExport = false;
            BSampleShow = true;
            BDTCellName = false;

            antCfgParaDic = new Dictionary<int, AntCfgSub>();
            antCfgParaSDic = new Dictionary<int, AntCfgSub>();
        }

        public bool BDtNbInfo { get; set; }//是否加载邻区信息
        public bool BSecAna { get; set; }//二维数据分析
        public bool BSecDataExport { get; set; }//二维数据导出
        public bool BSampleShow { get; set; }
        public bool BDTCellName { get; set; }//是否按小区查询
        List<String> cellNameList = new List<string>();//用于指定查询小区的列表
        readonly AntTimeCfg timeCfg = new AntTimeCfg();

        private static ZTLteScanAntenna instance = null;
        protected static readonly object lockObj = new object();

        public static ZTLteScanAntenna GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTLteScanAntenna();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "天线分析_LTE扫频"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 23000, 23018, this.Name);
        }

        #region 变量
        readonly Dictionary<string, CellAngleData> dicUtranCellAngelData = new Dictionary<string, CellAngleData>();
        readonly Dictionary<string, AntennaTypeStat> antennaTypeStatDic = new Dictionary<string, AntennaTypeStat>();
        Dictionary<int, AntennaPara> antParaEciDic = new Dictionary<int, AntennaPara>();
        Dictionary<string, AntennaPara> antParaCellNameDic = new Dictionary<string, AntennaPara>();
        Dictionary<int, CellPara> cellParaEciDic = new Dictionary<int, CellPara>();
        Dictionary<int, CellPara> cellParaEciSDic = new Dictionary<int, CellPara>();//Sector取一位数

        //状态库天线数据
        public Dictionary<int, AntCfgSub> antCfgParaDic { get; set; }
        public Dictionary<int, AntCfgSub> antCfgParaSDic { get; set; }
        Dictionary<int, LteMrItem> lteMREciDic = new Dictionary<int, LteMrItem>();
        Dictionary<int, LteMrItem> lteMREciSDic = new Dictionary<int, LteMrItem>();
        #endregion

        #region MR分类数据
        readonly Dictionary<int, ZTAntMRBaseItem> lteMRTimeDiffEciDic = new Dictionary<int, ZTAntMRBaseItem>();
        readonly Dictionary<int, ZTAntMRBaseItem> lteMRTimeDiffEciSDic = new Dictionary<int, ZTAntMRBaseItem>();

        Dictionary<int, ZTAntMRBaseItem> lteMRPowerHeadRoomEciDic = new Dictionary<int, ZTAntMRBaseItem>();
        Dictionary<int, ZTAntMRBaseItem> lteMRPowerHeadRoomEciSDic = new Dictionary<int, ZTAntMRBaseItem>();

        Dictionary<int, ZTAntMRBaseItem> lteMRRsrpEciDic = new Dictionary<int, ZTAntMRBaseItem>();
        Dictionary<int, ZTAntMRBaseItem> lteMRRsrpEciSDic = new Dictionary<int, ZTAntMRBaseItem>();

        Dictionary<int, ZTAntMRBaseItem> lteMRAoaEciDic = new Dictionary<int, ZTAntMRBaseItem>();
        Dictionary<int, ZTAntMRBaseItem> lteMRAoaEciSDic = new Dictionary<int, ZTAntMRBaseItem>();

        Dictionary<int, ZTAntMRBaseItem> lteMRSinrUlEciDic = new Dictionary<int, ZTAntMRBaseItem>();
        Dictionary<int, ZTAntMRBaseItem> lteMRSinrUlEciSDic = new Dictionary<int, ZTAntMRBaseItem>();

        Dictionary<int, ZTAntMRBaseItem> lteMRTAEciDic = new Dictionary<int, ZTAntMRBaseItem>();
        Dictionary<int, ZTAntMRBaseItem> lteMRTAEciSDic = new Dictionary<int, ZTAntMRBaseItem>();

        Dictionary<int, ZTAntMRBaseItem> lteMRTAAoaEciDic = new Dictionary<int, ZTAntMRBaseItem>();
        Dictionary<int, ZTAntMRBaseItem> lteMRTAAoaEciSDic = new Dictionary<int, ZTAntMRBaseItem>();

        Dictionary<int, LteCoverItem> lteMRCoverEciDic = new Dictionary<int, LteCoverItem>();
        Dictionary<int, LteCoverItem> lteMRCoverEciSDic = new Dictionary<int, LteCoverItem>();

        #endregion

        string strCityTypeName = "";

        protected override void query()
        {
            ZTLteAntenna.GetInstance().cmDataUnitAreaKPIQueryDic = new Dictionary<string, DataUnitAreaKPIQuery>();
            if (!MainModel.IsBackground && !MainModel.QueryFromBackground)
            {
                setVarBeforQuery();
                InitRegionMop2();
                ZTAntCfgForm antCfg = new ZTAntCfgForm(false);
                if (antCfg.ShowDialog() != DialogResult.OK)
                {
                    return;
                }
                BDtNbInfo = antCfg.BDtNbInfo;
                BSecAna = antCfg.BSecAna;
                BSecDataExport = antCfg.BSecDataExport;
                BSampleShow = antCfg.BSampleShow;
                BDTCellName = antCfg.BDTCellName;
                cellNameList = antCfg.cellNameList;
            }
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                dicUtranCellAngelData.Clear();
                antennaTypeStatDic.Clear();
                MainModel.ClearDTData();
                if (!MainModel.IsBackground)
                {
                    if (!MainModel.QueryFromBackground)
                    {
                        setRound();
                        WaitBox.CanCancel = true;
                        WaitBox.Text = "正在查询...";
                        strCityTypeName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                        WaitBox.Show("读取数据分析...", queryInThread, clientProxy);
                        WaitBox.Show("读取小区权值数据...", doWithParaData);
                        WaitBox.Show("读取MR数据处理...", doWithCellMRData);
                        WaitBox.Show("分类获取MR数据...", doWithCellMRByTypeData);
                        WaitBox.Show("读取状态库天线信息...", doWithAntCfgData);
                        WaitBox.Show("读取小区性能数据...", doWithCellParaData);
                        WaitBox.Show("联合数据处理...", AnaCellAngleData);
                        if (BSecDataExport && BSecAna)
                        {
                            WaitBox.Show("二维数据导出...", dealCellAngleSecData);
                        }
                        dealMainUtranCellSample();
                    }
                    else
                    {
                        getBackgroundData();
                        initBackgroundImageDesc();
                    }
                    MainModel.FireSetDefaultMapSerialTheme("LTE", "RSRP");
                }
                else
                {
                    strCityTypeName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                    doBackgroundStatByPeriod(clientProxy);
                }
            }
            catch (Exception exp)
            {
                clientProxy.Close();
                log.Error(exp.Message);
            }
            finally
            {
                clearData();
                MainModel.ClearDTData();
                ZTLteAntenna.GetInstance().cmDataUnitAreaKPIQueryDic = null;
            }
        }

        private void setRound()
        {
            if (Condition != null)
            {
                if (Condition.IsByRound)
                {
                    timeCfg.ISitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0}/{1:00}/01 00:00:00", condition.ByRoundYear, condition.ByRoundRound))) / (1000L));
                    timeCfg.IEitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0}/{1:00}/01 23:59:59", condition.ByRoundYear, condition.ByRoundRound)).AddMonths(1).AddDays(-1)) / (1000L));
                }
                else
                {
                    timeCfg.ISitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0:yyyy-MM-dd} 00:00:00", condition.Periods[0].BeginTime))) / (1000L));
                    timeCfg.IEitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0:yyyy-MM-dd} 23:59:59", condition.Periods[0].EndTime.AddDays(-1)))) / (1000L));
                }
            }
        }

        private void doWithCellMRData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 20;
            DiyLTECellMR lteCelMR = new DiyLTECellMR(MainModel, timeCfg);
            lteCelMR.SetQueryCondition(condition);
            lteCelMR.Query();
            lteMREciDic = lteCelMR.lteMREciDic;
            lteMREciSDic = lteCelMR.lteMREciSDic;

            WaitBox.ProgressPercent = 60;
            DiyLTEMRCover lteCover = new DiyLTEMRCover(MainModel, timeCfg);
            lteCover.SetQueryCondition(condition);
            lteCover.Query();
            lteMRCoverEciDic = lteCover.lteMREciDic;
            lteMRCoverEciSDic = lteCover.lteMREciSDic;

            WaitBox.ProgressPercent = 90;
            WaitBox.Close();
        }

        /// <summary>
        /// 分类获取MR数据
        /// </summary>
        private void doWithCellMRByTypeData()
        {
            WaitBox.ProgressPercent = 0;
            WaitBox.ProgressPercent = 10;
            doWithMRPowerHeadRoomData();
            WaitBox.ProgressPercent = 20;
            doWithMRRsrpData();
            WaitBox.ProgressPercent = 40;
            doWithMRAoaData();
            WaitBox.ProgressPercent = 60;
            doWithMRSinrUlData();
            WaitBox.ProgressPercent = 80;
            doWithMRTAData();
            WaitBox.ProgressPercent = 90;
            doWithMRRttdAoaData();
            WaitBox.ProgressPercent = 100;
            WaitBox.Close();
        }

        /// <summary>
        /// UE发射功率余量
        /// </summary>
        private void doWithMRPowerHeadRoomData()
        {
            WaitBox.CanCancel = true;
            DiyLTEMRData lteMRData = new DiyLTEMRData(MainModel, 2, 64, timeCfg);
            lteMRData.SetQueryCondition(condition);
            lteMRData.Query();
            lteMRPowerHeadRoomEciDic = lteMRData.lteMREciDic;
            lteMRPowerHeadRoomEciSDic = lteMRData.lteMREciSDic;
        }

        /// <summary>
        /// 参考信号接收功率
        /// </summary>
        private void doWithMRRsrpData()
        {
            WaitBox.CanCancel = true;
            DiyLTEMRData lteMRData = new DiyLTEMRData(MainModel, 3, 48, timeCfg);
            lteMRData.SetQueryCondition(condition);
            lteMRData.Query();
            lteMRRsrpEciDic = lteMRData.lteMREciDic;
            lteMRRsrpEciSDic = lteMRData.lteMREciSDic;
        }

        /// <summary>
        /// ENB天线到达角
        /// </summary>
        private void doWithMRAoaData()
        {
            WaitBox.CanCancel = true;
            DiyLTEMRData lteMRData = new DiyLTEMRData(MainModel, 4, 72, timeCfg);
            lteMRData.SetQueryCondition(condition);
            lteMRData.Query();
            lteMRAoaEciDic = lteMRData.lteMREciDic;
            lteMRAoaEciSDic = lteMRData.lteMREciSDic;
        }

        /// <summary>
        /// 上行信噪比
        /// </summary>
        private void doWithMRSinrUlData()
        {
            WaitBox.CanCancel = true;
            DiyLTEMRData lteMRData = new DiyLTEMRData(MainModel, 5, 37, timeCfg);
            lteMRData.SetQueryCondition(condition);
            lteMRData.Query();
            lteMRSinrUlEciDic = lteMRData.lteMREciDic;
            lteMRSinrUlEciSDic = lteMRData.lteMREciSDic;
        }

        /// <summary>
        /// 时间提前量
        /// </summary>
        private void doWithMRTAData()
        {
            WaitBox.CanCancel = true;
            DiyLTEMRData lteMRData = new DiyLTEMRData(MainModel, 6, 44, timeCfg);
            lteMRData.SetQueryCondition(condition);
            lteMRData.Query();
            lteMRTAEciDic = lteMRData.lteMREciDic;
            lteMRTAEciSDic = lteMRData.lteMREciSDic;
        }

        /// <summary>
        /// UE收发时间差与eNB天线到达角
        /// </summary>
        private void doWithMRRttdAoaData()
        {
            WaitBox.CanCancel = true;
            DiyLTEMRData lteMRData = new DiyLTEMRData(MainModel, 7, 132, timeCfg);
            lteMRData.SetQueryCondition(condition);
            lteMRData.Query();
            lteMRTAAoaEciDic = lteMRData.lteMREciDic;
            lteMRTAAoaEciSDic = lteMRData.lteMREciSDic;
        }

        protected override void statData(ClientProxy clientProxy)
        {
            setRound();
            InitRegionMop2();
            setVarBeforQuery();
            queryInThread(clientProxy);
            doWithParaData();
            doWithCellMRData();
            AnaCellAngleData();
            if (BSecDataExport && BSecAna)
                dealCellAngleSecData();
            dealMainUtranCellSample();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                if (mapOp2.CheckPointInRegion(tp.Longitude, tp.Latitude) && tp is ScanTestPoint_LTE)
                {
                    List<BtsSubInfo> sampleBtsList;
                    sampleBtsList = GetBtsListBySample(tp.Longitude, tp.Latitude, "4G");
                    sampleBtsList.Sort();

                    dealTP(tp);
                }
            }
            catch (Exception ee)
            {
                log.Error(ee.Message);
            }
        }

        private void dealTP(TestPoint tp)
        {
            for (int i = 0; i < 50; i++)
            {
                float rsrp, sinr;
                bool isValid = getValidTPInfo(tp, i, out rsrp, out sinr);
                if (!isValid)
                {
                    break;
                }

                LTECell lteCell = tp.GetCell_LTEScan(i);
                if (lteCell == null || lteCell.Direction > 360)
                    continue;

                if (BDTCellName && !cellNameList.Contains(lteCell.Name))
                {
                    return;
                }

                double dAngleDiffRel = 0;
                double dAngLeDiffAbs = 0;
                double cellDistance = 0;
                ZTAntFuncHelper.CellCollection col = new ZTAntFuncHelper.CellCollection()
                {
                    Cell = null,
                    TDCell = null,
                    LteCell = lteCell
                };
                ZTAntFuncHelper.calcSampleAngle(col, tp.Longitude, tp.Latitude, out dAngleDiffRel, out dAngLeDiffAbs, out cellDistance);
                int iAngleDiffRel = (int)dAngleDiffRel;//calcSampleAngle(null, null, lteCell, tp.Longitude, tp.Latitude, 0);//相对夹角
                int iAngleDiffAbs = (int)dAngLeDiffAbs;//calcSampleAngle(null, null, lteCell, tp.Longitude, tp.Latitude, 1);//相对夹角(绝对值)

                if (cellDistance > CD.MAX_COV_DISTANCE_LTE)//若大于3000米则忽略
                    return;
                CellAngleData utranCellAngleData = setCellAngleData(tp, lteCell);

                //按小区统计和角度统计
                dealWithCellAndAngleStat(ref utranCellAngleData, iAngleDiffRel, rsrp, sinr, cellDistance);

                //小区角度区间级
                setCellAngleData(utranCellAngleData, iAngleDiffAbs, tp, i, rsrp, sinr, cellDistance);
            }
        }

        private bool getValidTPInfo(TestPoint tp, int i, out float rsrp, out float sinr)
        {
            rsrp = 0;
            sinr = 0;

            object value = tp["LTESCAN_TopN_CELL_Specific_RSRP", i];
            if (value == null)
                return false;

            rsrp = float.Parse(value.ToString());
            if (rsrp < -141 || rsrp > 25)
                return false;

            value = tp["LTESCAN_TopN_CELL_Specific_RSSINR", i];
            if (value == null)
                return false;

            sinr = float.Parse(value.ToString());
            if (sinr < -50 || sinr > 50)
                return false;

            return true;
        }

        private CellAngleData setCellAngleData(TestPoint tp, LTECell lteCell)
        {
            CellAngleData utranCellAngleData;
            #region 天线信息处理
            if (dicUtranCellAngelData.ContainsKey(lteCell.Name))
            {
                utranCellAngleData = dicUtranCellAngelData[lteCell.Name];
            }
            else
            {
                utranCellAngleData = new CellAngleData(BSecAna);
                utranCellAngleData.strbscname = lteCell.BelongBTS.Name;
                utranCellAngleData.strTime = tp.DateTime.ToString("yyyy-MM-dd");
                utranCellAngleData.strcityname = strCityTypeName;
                string strGridTypeName = "";
                isContainPoint(lteCell.Longitude, lteCell.Latitude, ref strGridTypeName);
                if (strGridTypeName == "")
                    strGridTypeName = "无网格号";

                utranCellAngleData.strgridname = strGridTypeName;
                utranCellAngleData.cellname = lteCell.Name;
                utranCellAngleData.iTac = lteCell.TAC;
                utranCellAngleData.iEci = lteCell.ECI;
                utranCellAngleData.uarfcn = lteCell.EARFCN;
                utranCellAngleData.band = LTECell.GetBandTypeByEarfcn_BJ(lteCell.EARFCN);

                utranCellAngleData.ialtitude = lteCell.Altitude;
                utranCellAngleData.iangle_dir = (int)(lteCell.Direction);
                utranCellAngleData.iangle_ob = (int)(lteCell.Downward);

                utranCellAngleData.cellLongitude = lteCell.Longitude;
                utranCellAngleData.cellLatitude = lteCell.Latitude;
                utranCellAngleData.btsInfoList = GetBtsList(null, null, lteCell);
                utranCellAngleData.btsInfoList.Sort();
                dicUtranCellAngelData[lteCell.Name] = utranCellAngleData;
            }
            #endregion
            utranCellAngleData.sampleTotal++;
            return utranCellAngleData;
        }

        /// <summary>
        /// 查询小区性能参数数据
        /// </summary>
        private void doWithCellParaData()
        {
            WaitBox.CanCancel = true;
            DiyCellPara cellPara = new DiyCellPara(MainModel, timeCfg);
            cellPara.SetQueryCondition(condition);
            cellPara.Query();
            cellParaEciDic = cellPara.cellParaEciDic;
            cellParaEciSDic = cellPara.cellParaEciSDic;
            WaitBox.Close();
        }

        /// <summary>
        /// 查询状态库天线数据
        /// </summary>
        private void doWithAntCfgData()
        {
            WaitBox.CanCancel = true;
            DiyCfgPara cellPara = new DiyCfgPara(MainModel);
            cellPara.Query();
            antCfgParaDic = cellPara.antCfgParaDic;
            antCfgParaSDic = cellPara.antCfgParaSDic;
            WaitBox.Close();
        }

        /// <summary>
        /// 查询小区参数数据
        /// </summary>
        private void doWithParaData()
        {
            WaitBox.CanCancel = true;
            DiyAntennaPara antPara = new DiyAntennaPara(MainModel, timeCfg);
            antPara.SetQueryCondition(condition);
            antPara.Query();
            antParaEciDic = antPara.antParaEciDic;
            antParaCellNameDic = antPara.antParaCellNameDic;
            WaitBox.Close();
        }

        /// <summary>
        /// 按小区统计
        /// </summary>
        private void dealWithCellAndAngleStat(ref CellAngleData utranCellAngleData, int iAngleDiffRel, float? lteRsrp, float? lteSINR, double utranCellDistance)
        {
            //小区级
            setCellData(utranCellAngleData, lteRsrp, lteSINR, utranCellDistance);

            //角度级
            CellInfoItem ciItem = utranCellAngleData.ciItem;
            setAngleData(iAngleDiffRel, lteRsrp, lteSINR, utranCellDistance, ciItem);

            //天线二维数据
            setAntTwoData(iAngleDiffRel, lteRsrp, lteSINR, utranCellDistance, ciItem);
        }

        private static void setCellData(CellAngleData utranCellAngleData, float? lteRsrp, float? lteSINR, double utranCellDistance)
        {
            CellAngleDataExt cExt = utranCellAngleData.cExt;
            cExt.CalcRsrp((int)lteRsrp);
            cExt.CalcSinr((int)lteSINR);
            cExt.fSampleDistSum += utranCellDistance;
        }

        private static void setAngleData(int iAngleDiffRel, float? lteRsrp, float? lteSINR, double utranCellDistance, CellInfoItem ciItem)
        {
            ZTAntAngleItem angleItem;
            if (!ciItem.antAngleDic.TryGetValue(iAngleDiffRel, out angleItem))
                angleItem = new ZTAntAngleItem();

            angleItem.IRsrp += (int)lteRsrp;
            angleItem.ISinr += (int)lteSINR;
            angleItem.DSampDist += utranCellDistance;
            angleItem.ISampNum += 1;
            ciItem.antAngleDic[iAngleDiffRel] = angleItem;
        }

        private void setAntTwoData(int iAngleDiffRel, float? lteRsrp, float? lteSINR, double utranCellDistance, CellInfoItem ciItem)
        {
            int iLevel = ZTAntFuncHelper.getDistanceLevel(utranCellDistance);
            if (iLevel < 200)
            {
                ZTAntAngleItem vertItem;
                if (!ciItem.antVertDic.TryGetValue(iLevel, out vertItem))
                    vertItem = new ZTAntAngleItem();
                vertItem.IRsrp += (int)lteRsrp;
                vertItem.ISinr += (int)lteSINR;
                vertItem.ISampNum += 1;
                ciItem.antVertDic[iLevel] = vertItem;

                if (BSecAna)
                {
                    int idx = iAngleDiffRel * 1000 + iLevel;
                    if (ciItem.antTwoDic.ContainsKey(idx))
                    {
                        ciItem.antTwoDic[idx].IRsrp += (int)lteRsrp;
                        ciItem.antTwoDic[idx].ISampNum += 1;
                    }
                    else
                    {
                        ZTAntAngleItem twoItem = new ZTAntAngleItem();
                        twoItem.IRsrp += (int)lteRsrp;
                        twoItem.ISampNum += 1;
                        ciItem.antTwoDic[idx] = twoItem;
                    }
                }
            }
        }

        private void setCellAngleData(CellAngleData utranCellAngleData, int iAngleDiffAbs, TestPoint tp, int tpIndex, float? lteRsrp, float? lteSINR, double utranCellDistance)
        {
            string sectionName = ZTAntFuncHelper.GetAngleSection(iAngleDiffAbs);
            AngleData angleData = new AngleData();

            if (BSampleShow)//存储采样点信息
            {
                utranCellAngleData.tpList.Add(tp);
                string snk = tp.SN.ToString() + tp.FileID.ToString() + tp.Time.ToString();
                if (!utranCellAngleData.tpIndexDic.ContainsKey(snk))
                    utranCellAngleData.tpIndexDic.Add(snk, tpIndex);
            }

            if (utranCellAngleData.angleDatas.ContainsKey(sectionName))
            {
                angleData = utranCellAngleData.angleDatas[sectionName];
            }
            else
            {
                utranCellAngleData.angleDatas[sectionName] = angleData;
                angleData.rsrpMax = -140;
            }

            angleData.CalcRsrp((int)lteRsrp);
            angleData.CalcSinr((int)lteSINR);
            angleData.rsrpMax = (angleData.rsrpMax > lteRsrp ? angleData.rsrpMax : (float)lteRsrp);
            angleData.fSampleDistSum += utranCellDistance;

            if (angleData.top5RsrpList.Count >= 5)
            {
                angleData.top5RsrpList.Sort();
                if (angleData.top5RsrpList[0] < lteRsrp)
                {
                    angleData.top5RsrpList[0] = (int)(lteRsrp);
                }
            }
            else
            {
                angleData.top5RsrpList.Add((int)(lteRsrp));
            }
        }

        /// <summary>
        /// 设置需要的字段
        /// </summary>
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSSINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LTE_SCAN");
            tmpDic.Add("themeName", (object)"TopN_CELL_Specific_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void fillContentNeeded_Sample(Package package)
        {
            List<ColumnDefItem> colDefList = getNeededColumnDefList(curSelDIYSampleGroup);
            StringBuilder sbuilder = new StringBuilder();
            sbuilder.Append("0,1,51,0,2,51,0,4,51,0,5,51,");//isampleid
            for (int i = 0; i < colDefList.Count; i++)
            {
                ColumnDefItem sid = colDefList[i];
                sbuilder.Append(sid.GetTriIdStr());
                if (i < colDefList.Count - 1)
                {
                    sbuilder.Append(",");
                }
            }
            package.Content.AddParam(sbuilder.ToString());
        }

        /// <summary>
        /// 分析小区的角度数组，进行平滑化处理
        /// </summary>
        private void AnaCellAngleData()
        {
            int iStart = 0;
            int iEnd = 360;
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 0;
            int iCount = dicUtranCellAngelData.Count;
            int iNum = 0;
            foreach (string strCellName in dicUtranCellAngelData.Keys)
            {
                if (WaitBox.CancelRequest)
                    break;

                if (iNum % 100 == 0)
                {
                    WaitBox.ProgressPercent = (int)(100 * ((iNum * 1.0) / iCount));
                    WaitBox.Text = "联合数据处理(" + iNum + "/" + iCount + ")";
                }

                #region 采样点的覆盖波动处理
                CellAngleData caData = dicUtranCellAngelData[strCellName];
                double[] rsrpPathLossArray = new double[360];//修正距离损耗RSRP
                double[] rsrpUnifiedArray = new double[360];//归一化RSRP
                double[] rsrpNewArray = new double[360];//平滑RSRP

                int[] rsrpArray = caData.ciItem.rsrpArray;
                double[] sampleDistArray = caData.ciItem.sampArray;
                int[] sampleNumArray = caData.ciItem.sampNumArray;

                double dNewValue = 0;
                double dSumValue = getSumValue(iStart, iEnd, rsrpPathLossArray, rsrpArray, sampleDistArray, sampleNumArray);
                dNewValue = 10 * Math.Log10(dSumValue / 360);
                addRsrpUnifiedArray(iStart, iEnd, rsrpPathLossArray, rsrpUnifiedArray, dNewValue);
                addAntAngle(iStart, iEnd, caData, rsrpUnifiedArray, rsrpNewArray);
                caData.antInfoList = anaAntennaInfoByCellNew(rsrpNewArray);
                #endregion

                #region 权值理想覆盖模型
                AntennaPara antPara;
                if (!antParaEciDic.TryGetValue(caData.iEci, out antPara) && !antParaCellNameDic.TryGetValue(caData.cellname, out antPara))
                {
                    antPara = new AntennaPara();
                }
                caData.antPara = antPara;
                AntParaItem ant1Item = new AntParaItem(antPara.strbandtype, antPara.drangeport1, antPara.drangeport2, antPara.drangeport3, antPara.drangeport4, antPara.strdevvender);
                ant1Item.Init(antPara.dphaseport1, antPara.dphaseport2, antPara.dphaseport3, antPara.dphaseport4);
                AntParaItem ant2Item = new AntParaItem(antPara.strbandtype, antPara.drangeport5, antPara.drangeport6, antPara.drangeport7, antPara.drangeport8, antPara.strdevvender);
                ant2Item.Init(antPara.dphaseport5, antPara.dphaseport6, antPara.dphaseport7, antPara.dphaseport8);

                caData.ciItem.model1Array = ant1Item.getPowerArray();
                caData.ciItem.model2Array = ant2Item.getPowerArray();
                caData.ciItem.modelMaxArray = ZTAntFuncHelper.getMaxPowerArray(caData.ciItem.model1Array, caData.ciItem.model2Array);
                LongLat ll = new LongLat();
                ll.fLongitude = (float)(caData.cellLongitude);
                ll.fLatitude = (float)(caData.cellLatitude);
                //Model
                int iMaxValue = -50;
                int iMinValue = 50;
                ZTAntFuncHelper.getMaxAndMinValue(rsrpNewArray, ref iMaxValue, ref iMinValue);
                caData.longLatTestList = ZTAntFuncHelper.getCellEmulateCoverTest(ll, rsrpNewArray, iMaxValue, iMinValue, caData.iangle_dir);
                int iMaxValue2 = -50;
                int iMinValue2 = 50;
                ZTAntFuncHelper.getMaxAndMinValue(caData.ciItem.modelMaxArray, ref iMaxValue2, ref iMinValue2);
                caData.longLatModelList = ZTAntFuncHelper.getCellEmulateCoverModel(ll, caData.ciItem.modelMaxArray, iMaxValue2, iMinValue, caData.iangle_dir);

                #endregion

                OtherDataSetValue(ref caData);
                FillMrInfo(ref caData, ll);
                iNum++;
            }
            WaitBox.Close();
        }

        private static double getSumValue(int iStart, int iEnd, double[] rsrpPathLossArray, int[] rsrpArray, double[] sampleDistArray, int[] sampleNumArray)
        {
            double dSumValue = 0;
            for (int i = iStart; i < iEnd; i++)
            {
                double dTmpRsrp = (double)(sampleNumArray[i] == 0 ? -140 : rsrpArray[i] / sampleNumArray[i]);
                double dTmpDist = Math.Round((sampleNumArray[i] == 0 ? 0 : sampleDistArray[i] / sampleNumArray[i]), 2) / 1000;
                //修正自由空间路损
                double dFreeSpacePathLoss = dTmpRsrp == -140 ? 0 : 33 * Math.Log10(dTmpDist);
                double dDistancePathLoss = dTmpRsrp + dFreeSpacePathLoss;
                rsrpPathLossArray[i] = dDistancePathLoss;
                double dTmpValue = Math.Pow(10, dDistancePathLoss / 10);
                dSumValue += dTmpValue;
            }

            return dSumValue;
        }

        private static void addRsrpUnifiedArray(int iStart, int iEnd, double[] rsrpPathLossArray, double[] rsrpUnifiedArray, double dNewValue)
        {
            for (int i = iStart; i < iEnd; i++)
            {
                double dDistancePathLoss = rsrpPathLossArray[i];
                double dUnifiedValue = dDistancePathLoss > -120 ? dDistancePathLoss - dNewValue : -20;
                rsrpUnifiedArray[i] = dUnifiedValue;
            }
        }

        private static void addAntAngle(int iStart, int iEnd, CellAngleData caData, double[] rsrpUnifiedArray, double[] rsrpNewArray)
        {
            for (int i = iStart; i < iEnd; i++)
            {
                double iNewRsrp = ZTAntFuncHelper.getAvgRsrp(rsrpUnifiedArray, i);
                rsrpNewArray[i] = iNewRsrp;
                if (caData.ciItem.antAngleDic.ContainsKey(i))
                    caData.ciItem.antAngleDic[i].DRsrpNew = iNewRsrp;
                else
                {
                    ZTAntAngleItem newRsrpItem = new ZTAntAngleItem();
                    newRsrpItem.DRsrpNew = iNewRsrp;
                    caData.ciItem.antAngleDic[i] = newRsrpItem;
                }
            }
        }

        /// <summary>
        /// MR数据填充
        /// </summary>
        private void FillMrInfo(ref CellAngleData caData, LongLat ll)
        {
            //MR
            ZTLteAntMRAna.AnaRttdAoaArray ary = new ZTLteAntMRAna.AnaRttdAoaArray();
            ary.AnaRttdAoa90 = caData.cellMrData.AnaRttdAoa;
            Dictionary<int, int> tmpDirSampleDic = ZTAntFuncHelper.GetDirSample(ary);
            caData.dirSampleDic = ZTAntFuncHelper.GetNewDirSample(tmpDirSampleDic);
            caData.iAntMaxDir = ZTAntFuncHelper.GetAntMaxDir(tmpDirSampleDic);
            double[] aoaArray = ConvertArray(caData.dirSampleDic);
            int iMaxValue3 = -50;
            int iMinValue3 = 50;
            ZTAntFuncHelper.getMaxAndMinValue(aoaArray, ref iMaxValue3, ref iMinValue3);
            caData.mrAoaList = ZTAntFuncHelper.getCellMrCover(ll, aoaArray, iMaxValue3, iMinValue3, 0);
            if (caData.iAntMaxDir != -1)
            {
                caData.strIsMRCell = "是";
                int iDir = (caData.iAntMaxDir - caData.iangle_dir + 360) % 360;
                if (iDir > 180)
                    caData.iDirDiff = 360 - iDir;
                else
                    caData.iDirDiff = iDir;

                setStrAntResult(caData);
            }
        }

        private static void setStrAntResult(CellAngleData caData)
        {
            if (caData.iDirDiff >= 150)
                caData.strAntResult = "背瓣覆盖;";
            else if (caData.iDirDiff >= 75)
                caData.strAntResult = "旁瓣泄露;";

            if (caData.strAntResult != "")
            {
                if (caData.cellMrData.lteMRItem.dAvgSinr < -3)
                    caData.strAntResult += "严重干扰";
                else if (caData.cellMrData.lteMRItem.dAvgSinr < 0)
                    caData.strAntResult += "一般干扰";

                if (caData.cellMrData.lteMRItem.dAvgRsrp <= -110 && caData.cellMrData.lteMRItem.dRate95Rsrp < 20 && caData.cellMrData.lteMRItem.dRate110Rsrp < 60)
                    caData.strAntResult += "严重弱覆盖";
                else if (caData.cellMrData.lteMRItem.dAvgRsrp <= -110 || caData.cellMrData.lteMRItem.dRate95Rsrp < 20 || caData.cellMrData.lteMRItem.dRate110Rsrp < 60)
                    caData.strAntResult += "弱覆盖";
            }
        }

        /// <summary>
        /// 转换为数组
        /// </summary>
        private double[] ConvertArray(Dictionary<int, int> tmpDirSampleDic)
        {
            double[] tmpArray = new double[72];
            for (int i = 0; i < 72; i++)
            {
                if (tmpDirSampleDic.ContainsKey(i))
                {
                    tmpArray[i] = tmpDirSampleDic[i];
                }
            }
            return tmpArray;
        }

        /// <summary>
        /// 性能、状态库、MR数据赋值
        /// </summary>
        private void OtherDataSetValue(ref CellAngleData caData)
        {
            int iNewEci = (caData.iEci / 256) * 256 + ((caData.iEci % 256) % 10);

            CellPara cellPara = getItem(iNewEci, cellParaEciDic, cellParaEciSDic);//小区性能数据
            caData.cellPara = cellPara;

            AntCfgSub antCfg = getItem(iNewEci, antCfgParaDic, antCfgParaSDic);//状态库天线数据
            caData.antCfg = antCfg;

            LteMrItem lteMRItem = getItem(iNewEci, lteMREciDic, lteMREciSDic);
            caData.cellMrData.lteMRItem = lteMRItem;

            ZTAntMRBaseItem lteMRPowerHeadRoomItem = getItem(iNewEci, lteMRPowerHeadRoomEciDic, lteMRPowerHeadRoomEciSDic);
            caData.cellMrData.lteMRPowerHeadRoomItem = lteMRPowerHeadRoomItem;

            ZTAntMRBaseItem lteMRRsrpItem = getItem(iNewEci, lteMRRsrpEciDic, lteMRRsrpEciSDic);
            caData.cellMrData.lteMRRsrpItem = lteMRRsrpItem;

            ZTAntMRBaseItem lteMRAoaItem = getItem(iNewEci, lteMRAoaEciDic, lteMRAoaEciSDic);
            caData.cellMrData.lteMRAoaItem = lteMRAoaItem;

            ZTAntMRBaseItem lteMRSinrUlItem = getItem(iNewEci, lteMRSinrUlEciDic, lteMRSinrUlEciSDic);
            caData.cellMrData.lteMRSinrUlItem = lteMRSinrUlItem;

            ZTAntMRBaseItem lteMRRttdAoaItem = getItem(iNewEci, lteMRTAAoaEciDic, lteMRTAAoaEciSDic);
            caData.cellMrData.lteMRRttdAoaItem = lteMRRttdAoaItem;

            ZTAntMRBaseItem lteMRTaItem = getItem(iNewEci, lteMRTAEciDic, lteMRTAEciSDic);
            caData.cellMrData.lteMRTaItem = lteMRTaItem;

            //小区MR覆盖指数
            LteCoverItem lteMRCoverItem = getItem(iNewEci, lteMRCoverEciDic, lteMRCoverEciSDic);
            caData.cellMrData.lteMRCoverItem = lteMRCoverItem;
        }

        private T getItem<T>(int iNewEci, Dictionary<int,T> dic, Dictionary<int, T> sDic) where T : new()
        {
            T item;
            if (!dic.TryGetValue(iNewEci, out item) && !sDic.TryGetValue(iNewEci, out item))
            {
                item = new T();
            }
            return item;
        }

        /// <summary>
        /// 释放内存
        /// </summary>
        private void clearData()
        {
            dicUtranCellAngelData.Clear();
            antennaTypeStatDic.Clear();
            antParaEciDic.Clear();

            cellParaEciDic.Clear();
            cellParaEciSDic.Clear();
            antParaCellNameDic.Clear();
            antCfgParaDic.Clear();
            antCfgParaSDic.Clear();
            lteMREciDic.Clear();
            lteMREciSDic.Clear();

            lteMRTimeDiffEciDic.Clear();
            lteMRTimeDiffEciSDic.Clear();
            lteMRPowerHeadRoomEciDic.Clear();
            lteMRPowerHeadRoomEciSDic.Clear();
            lteMRRsrpEciDic.Clear();
            lteMRRsrpEciSDic.Clear();
            lteMRAoaEciDic.Clear();
            lteMRAoaEciSDic.Clear();
            lteMRSinrUlEciDic.Clear();
            lteMRSinrUlEciSDic.Clear();
            lteMRTAEciDic.Clear();
            lteMRTAEciSDic.Clear();
            lteMRTAAoaEciDic.Clear();
            lteMRTAAoaEciSDic.Clear();

            lteMRCoverEciDic.Clear();
            lteMRCoverEciSDic.Clear();
        }

        /// <summary>
        /// 天线辐射性能分析(新算法)
        /// </summary>
        private List<AntInfto> anaAntennaInfoByCellNew(double[] rsrpNewArray)
        {
            int tmpMinValue = 0;//第一个位置
            if (rsrpNewArray[0] > -20)
            {
                for (int i = 359; i > 0; i--)
                {
                    if (rsrpNewArray[i] <= -20)
                    {
                        tmpMinValue = i + 1;
                        break;
                    }
                }
            }
            if (tmpMinValue > 0)
                tmpMinValue -= 360;

            int iSampleNum;
            List<AntInfto> antInfoList;
            getAntInfoList(rsrpNewArray, tmpMinValue, out iSampleNum, out antInfoList);

            if (antInfoList.Count > 0)
            {
                for (int i = 0; i < antInfoList.Count; i++)
                {
                    antInfoList[i].sampNum = iSampleNum;
                }
                List<AntInfto> newAntInfoList = new List<AntInfto>();//辐射叶列表
                //定位主瓣
                addMainlobe(rsrpNewArray, iSampleNum, newAntInfoList);

                //定位背瓣
                int iBackMinDir, iBackMaxDir;
                addBacklobe(rsrpNewArray, iSampleNum, newAntInfoList, out iBackMinDir, out iBackMaxDir);

                //搜索旁瓣
                addSidelobe(antInfoList, newAntInfoList, iBackMinDir, iBackMaxDir);
                return newAntInfoList;
            }
            else
            {
                return new List<AntInfto>();
            }
        }

        private static void getAntInfoList(double[] rsrpNewArray, int tmpMinValue, out int iSampleNum, out List<AntInfto> antInfoList)
        {
            iSampleNum = 0;
            antInfoList = new List<AntInfto>();
            bool isStart = false;
            AntInfto aInfo = new AntInfto();
            for (int i = tmpMinValue; i < tmpMinValue + 360; i++)
            {
                int j = i < 0 ? i + 360 : i;
                if (isStart)
                {
                    setStartAntInfto(rsrpNewArray, ref iSampleNum, antInfoList, ref isStart, aInfo, i, j);
                }
                else
                {
                    setAntInfto(rsrpNewArray, ref iSampleNum, ref isStart, ref aInfo, i, j);
                }
            }
        }

        private static void setStartAntInfto(double[] rsrpNewArray, ref int iSampleNum, List<AntInfto> antInfoList, ref bool isStart, AntInfto aInfo, int i, int j)
        {
            if (rsrpNewArray[j] <= -20)
            {
                aInfo.iMaxDir = i - 1;
                if (aInfo.MaxRsrp >= 0)//少于0则忽略
                {
                    antInfoList.Add(aInfo);
                }
                isStart = false;
            }
            else
            {
                iSampleNum++;
                aInfo.newRsrpList.Add(rsrpNewArray[j]);
            }
        }

        private static void setAntInfto(double[] rsrpNewArray, ref int iSampleNum, ref bool isStart, ref AntInfto aInfo, int i, int j)
        {
            if (rsrpNewArray[j] > -20)
            {
                iSampleNum++;
                isStart = true;
                aInfo = new AntInfto();
                aInfo.iMinDir = i;
                aInfo.newRsrpList.Add(rsrpNewArray[j]);
            }
        }

        private static void addMainlobe(double[] rsrpNewArray, int iSampleNum, List<AntInfto> newAntInfoList)
        {
            AntInfto mainAntInfo = new AntInfto();
            int iMainMinDir = -60;
            int iMainMaxDir = 60;
            for (int i = iMainMinDir; i <= iMainMaxDir; i++)
            {
                int j = i < 0 ? i + 360 : i;
                mainAntInfo.newRsrpList.Add(rsrpNewArray[j]);
            }
            mainAntInfo.sampNum = iSampleNum;
            mainAntInfo.iMinDir = iMainMinDir;
            mainAntInfo.iMaxDir = iMainMaxDir;
            newAntInfoList.Add(mainAntInfo);
        }

        private static void addBacklobe(double[] rsrpNewArray, int iSampleNum, List<AntInfto> newAntInfoList, out int iBackMinDir, out int iBackMaxDir)
        {
            AntInfto backAntInfo = new AntInfto();
            iBackMinDir = 165;
            iBackMaxDir = 195;
            if (iBackMinDir > iBackMaxDir)
                iBackMinDir -= 360;
            for (int i = iBackMinDir; i <= iBackMaxDir; i++)
            {
                int j = i < 0 ? i + 360 : i;
                backAntInfo.newRsrpList.Add(rsrpNewArray[j]);
            }
            backAntInfo.sampNum = iSampleNum;
            backAntInfo.iMinDir = iBackMinDir;
            backAntInfo.iMaxDir = iBackMaxDir;
            newAntInfoList.Add(backAntInfo);
        }

        private static void addSidelobe(List<AntInfto> antInfoList, List<AntInfto> newAntInfoList, int iBackMinDir, int iBackMaxDir)
        {
            for (int i = 0; i < antInfoList.Count; i++)
            {
                if (antInfoList[i].MaxRsrpDir >= 300 || antInfoList[i].MaxRsrpDir <= 60
                    || (antInfoList[i].MaxRsrpDir >= iBackMinDir && antInfoList[i].MaxRsrpDir <= iBackMaxDir))
                    continue;

                int iDir = (Math.Abs(0 - antInfoList[i].MaxRsrpDir) + 180) % 180;
                if (iDir >= 35)
                    newAntInfoList.Add(antInfoList[i]);
            }
        }

        ///<summary>
        ///显示结果窗体
        ///</summary>
        private void FireShowResultForm(List<List<NPOIRow>> nrDatasList, List<string> sheetNames)
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LteScanAntennaForm).FullName);
            LteScanAntennaForm form = obj == null ? null : obj as LteScanAntennaForm;
            if (form == null || form.IsDisposed)
            {
                form = new LteScanAntennaForm(MainModel);
            }
            form.nrDatasList = nrDatasList;
            form.sheetNames = sheetNames;

            form.FillData(dicUtranCellAngelData);
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }

        #region 数据整理及导出
        /// <summary>
        /// 按主服导出数据
        /// </summary>
        private void dealMainUtranCellSample()
        {
            List<NPOIRow> datas = new List<NPOIRow>();
            NPOIRow nr1 = new NPOIRow();
            List<NPOIRow> data2s = new List<NPOIRow>();
            NPOIRow nr2 = new NPOIRow();
            List<NPOIRow> data8s = new List<NPOIRow>();
            NPOIRow nr8 = new NPOIRow();
            List<NPOIRow> data10s = new List<NPOIRow>();
            NPOIRow nr10 = new NPOIRow();

            //EXCEL-SHEET1列表构造
            nr1.cellValues = GetExcelSheet1ColName();
            datas.Add(nr1);

            //EXCEL-SHEET2列表构造
            List<object> col2s = new List<object>();
            col2s.Add("小区名称");
            col2s.Add("指标项");
            for (int i = 0; i < 360; i++)
            {
                col2s.Add(i.ToString());
            }
            nr2.cellValues = col2s;
            data2s.Add(nr2);

            //EXCEL-SHEET4列表构造
            nr8.cellValues = GetExcelSheet4ColName();
            data8s.Add(nr8);

            //EXCEL-SHEET5列表构造
            nr10.cellValues = GetExcelSheet5ColName();
            data10s.Add(nr10);

            List<CellAngleData> listCellAngleData = new List<CellAngleData>();
            listCellAngleData.AddRange(dicUtranCellAngelData.Values);
            listCellAngleData.Sort(delegate (CellAngleData a, CellAngleData b)
            {
                return b.sampleTotal.CompareTo(a.sampleTotal);
            });

            List<string> listSection = new List<string>();
            listSection.Add("[0,15]");
            listSection.Add("(15,30]");
            listSection.Add("(30,60]");
            listSection.Add("(60,90]");
            listSection.Add("(90,120]");
            listSection.Add("(120,150]");
            listSection.Add("(150,180]");

            int iCount = 1;
            foreach (CellAngleData data in listCellAngleData)
            {
                try
                {
                    foreach (string section in listSection)
                    {
                        datas.AddRange(FillSetionValue(data, section));
                    }
                    data2s.AddRange(FillAngleValue(data));

                    NPOIRow nr9 = new NPOIRow();
                    nr9.cellValues = FillCellValue(data, iCount);
                    data8s.Add(nr9);

                    NPOIRow nr11 = new NPOIRow();
                    FillAntValue(nr11, data);
                    data10s.Add(nr11);
                    iCount++;
                }
                catch (Exception e)
                {
                    log.Error(e.Message);
                }
            }

            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            nrDatasList.Add(datas);
            nrDatasList.Add(data2s);
            nrDatasList.Add(data8s);
            nrDatasList.Add(data10s);

            List<string> sheetNames = new List<string>();
            sheetNames.Add("覆盖角度区间化统计分析");
            sheetNames.Add("天线角度级采样数据分析");
            sheetNames.Add("小区权值设置及覆盖统计");
            sheetNames.Add("天线辐射性能分析");

            listCellAngleData.Sort(delegate (CellAngleData a, CellAngleData b)
            {
                return b.sampleTotal.CompareTo(a.sampleTotal);
            });
            
            if (!MainModel.IsBackground)
                FireShowResultForm(nrDatasList, sheetNames);
        }

        /// <summary>
        /// SHEET1列头
        /// </summary>
        private List<object> GetExcelSheet1ColName()
        {
            List<object> cols = new List<object>();
            cols.Add("小区名称");
            cols.Add("频点");
            cols.Add("频段");
            cols.Add("[0,30]区间与(150,180]区间最强PCCPCH的差值");
            cols.Add("方向角偏差区间");
            cols.Add("区间采样点数");
            cols.Add("采样点占比");
            cols.Add("区间平均RSRP(dBm)");
            cols.Add("区间最大RSRP(dBm)");
            cols.Add("区间SINR平均");
            cols.Add("区间平均通信距离");

            cols.Add("总采样点数");
            cols.Add("天线类型");
            cols.Add("下倾角");
            cols.Add("挂高");
            cols.Add("方向角");
            cols.Add("小区RSRP平均");
            cols.Add("小区SINR平均");
            cols.Add("小区平均通信距离");
            return cols;
        }

        /// <summary>
        /// SHEET4列头
        /// </summary>
        private List<object> GetExcelSheet4ColName()
        {
            List<object> col8s = new List<object>();
            col8s.Add("序号");
            col8s.Add("地市");
            col8s.Add("小区名");
            col8s.Add("所在网格");
            col8s.Add("BSC名");
            col8s.Add("频点标示");
            col8s.Add("采样点总数");

            col8s.Add("小区RSRP均值");
            col8s.Add("小区SINR<=-3占比(%)");
            col8s.Add("小区SINR均值");
            col8s.Add("小区平均通信距离");

            col8s.Add("主设备厂家");
            col8s.Add("CGI");
            col8s.Add("波束宽度");
            col8s.Add("覆盖类型");
            col8s.Add("Gmax");
            col8s.Add("3dB功率角");
            col8s.Add("6dB功率角");

            col8s.Add("端口1幅度");
            col8s.Add("端口2幅度");
            col8s.Add("端口3幅度");
            col8s.Add("端口4幅度");
            col8s.Add("端口5幅度");
            col8s.Add("端口6幅度");
            col8s.Add("端口7幅度");
            col8s.Add("端口8幅度");

            col8s.Add("端口1相位");
            col8s.Add("端口2相位");
            col8s.Add("端口3相位");
            col8s.Add("端口4相位");
            col8s.Add("端口5相位");
            col8s.Add("端口6相位");
            col8s.Add("端口7相位");
            col8s.Add("端口8相位");
            col8s.Add("是否(8通道)智能天线");

            col8s.Add("预置下倾角");
            col8s.Add("机械下倾角");
            col8s.Add("电调下倾角");
            col8s.Add("挂高");

            col8s.Add("上行吞吐量");
            col8s.Add("下行吞吐量");
            col8s.Add("无线接通率");
            col8s.Add("无线掉线率");
            col8s.Add("切换成功率");
            col8s.Add("ERAB建立成功率");
            col8s.Add("ERAB掉线率");

            col8s.Add("RSRP均值");
            col8s.Add("SINR均值");
            col8s.Add("95覆盖率");
            col8s.Add("110覆盖率");

            col8s.Add("MRO总采样点数");
            col8s.Add("重叠覆盖条件采样点数");
            col8s.Add("重叠覆盖指数");
            col8s.Add("过覆盖影响小区数");
            col8s.Add("高重叠覆盖小区");
            col8s.Add("过覆盖小区");

            col8s.Add("MR主覆盖角");
            col8s.Add("与天线工参偏差值");
            col8s.Add("是否可MR波形重构");
            return col8s;
        }

        /// <summary>
        /// SHEET5列头
        /// </summary>
        private List<object> GetExcelSheet5ColName()
        {
            List<object> col10s = new List<object>();
            col10s.Add("地市");
            col10s.Add("小区名");
            col10s.Add("所在网格");
            col10s.Add("主设备厂家");
            col10s.Add("BSC名");
            col10s.Add("频点标示");
            col10s.Add("小区RSRP均值");
            col10s.Add("小区SINR平均");
            col10s.Add("小区平均通信距离");
            col10s.Add("波束宽度");
            col10s.Add("采样点总数");
            col10s.Add("天线方位角");

            col10s.Add("±(0,60°)范围内采样点比例");
            col10s.Add("±(0,60°)范围内最强信号强度");

            col10s.Add("疑似旁瓣数量");
            col10s.Add("旁瓣1辐射方向");
            col10s.Add("旁瓣1最强信号强度");
            col10s.Add("旁瓣1平均信号强度");
            col10s.Add("旁瓣1采样点比例");
            col10s.Add("旁瓣2辐射方向");
            col10s.Add("旁瓣2最强信号强度");
            col10s.Add("旁瓣2平均信号强度");
            col10s.Add("旁瓣2采样点比例");
            col10s.Add("旁瓣3辐射方向");
            col10s.Add("旁瓣3最强信号强度");
            col10s.Add("旁瓣3平均信号强度");
            col10s.Add("旁瓣3采样点比例");
            col10s.Add("±(150,180°)范围内采样点比例");
            col10s.Add("前后比");
            return col10s;
        }

        /// <summary>
        /// 按区拆分导出数据
        /// </summary>
        private List<NPOIRow> FillSetionValue(CellAngleData data, string section)
        {
            List<NPOIRow> dataNpoi = new List<NPOIRow>();
            NPOIRow nr = new NPOIRow();

            float rsxp15Max = -140;
            if (data.angleDatas.ContainsKey("[0,15]"))
            {
                rsxp15Max = data.angleDatas["[0,15]"].rsrpMax;
            }

            if (data.angleDatas.ContainsKey("(15,30]")
                && data.angleDatas["(15,30]"].rsrpMax > rsxp15Max)
            {
                rsxp15Max = data.angleDatas["(15,30]"].rsrpMax;
            }

            nr.AddCellValue(data.cellname);
            nr.AddCellValue(data.uarfcn);
            nr.AddCellValue(data.band.ToString().Split('_')[0]);

            if (data.angleDatas.ContainsKey("(150,180]") && rsxp15Max > -140)
            {
                nr.AddCellValue(rsxp15Max - data.angleDatas["(150,180]"].rsrpMax);
            }
            else
            {
                nr.AddCellValue("");
            }

            setSection(data, section, nr);

            nr.AddCellValue(data.sampleTotal);//总采样点
            nr.AddCellValue(data.anaType);//天线类型
            nr.AddCellValue(data.iangle_ob);//下倾角
            nr.AddCellValue(data.ialtitude);//挂高
            nr.AddCellValue(data.iangle_dir);//方位角

            nr.AddCellValue(Math.Round(data.cExt.rsrpNum == 0 ? 0 : data.cExt.rsrpSum * 1.0 / data.cExt.rsrpNum, 2));
            nr.AddCellValue(Math.Round(data.cExt.sinrNum == 0 ? 0 : data.cExt.sinrSum * 1.0 / data.cExt.sinrNum, 2));
            nr.AddCellValue(Math.Round(data.sampleTotal == 0 ? 0 : data.cExt.fSampleDistSum / data.sampleTotal, 2));//小区平均通信距离

            dataNpoi.Add(nr);

            return dataNpoi;
        }

        private static void setSection(CellAngleData data, string section, NPOIRow nr)
        {
            nr.AddCellValue(section);
            if (data.angleDatas.ContainsKey(section))
            {
                AngleData angleData = data.angleDatas[section];
                nr.AddCellValue(angleData.rsrpNum);
                nr.AddCellValue(Math.Round(data.sampleTotal == 0 ? 0 : angleData.rsrpNum * 100.0 / data.sampleTotal, 2));
                nr.AddCellValue(Math.Round(angleData.rsrpNum == 0 ? 0 : angleData.rsrpSum * 1.0 / angleData.rsrpNum, 2));
                nr.AddCellValue(angleData.rsrpMax);
                nr.AddCellValue(Math.Round(angleData.sinrNum == 0 ? 0 : angleData.sinrSum * 1.0 / angleData.sinrNum, 2));
                nr.AddCellValue(Math.Round(angleData.rsrpNum == 0 ? 0 : angleData.fSampleDistSum / angleData.rsrpNum, 2));
            }
            else
            {
                nr.AddCellValue("");
                nr.AddCellValue("");
                nr.AddCellValue("");
                nr.AddCellValue("");
                nr.AddCellValue("");
                nr.AddCellValue("");
            }
        }

        private double getValidData(double count, double sum)
        {
            if (count == 0)
            {
                return 0;
            }
            return sum / count;
        }

        /// <summary>
        /// 小区级赋值
        /// </summary>
        private List<object> FillCellValue(CellAngleData data, int iCount)
        {
            List<object> objs8 = new List<object>();
            objs8.Add(iCount);
            objs8.Add(data.strcityname);
            objs8.Add(data.cellname);
            objs8.Add(data.strgridname);
            objs8.Add(data.strbscname);
            objs8.Add(data.uarfcn);
            //objs8.Add(data.strProblemType);//天线问题类型
            objs8.Add(data.sampleTotal);//总采样点

           
            objs8.Add(Math.Round(getValidData(data.cExt.rsrpNum, data.cExt.rsrpSum), 2));//RSRP均值
            objs8.Add(data.cExt.Sinr5_3);//小区SINR<=-3占比(%)
            objs8.Add(Math.Round(getValidData(data.cExt.sinrNum, data.cExt.sinrSum * 1.0), 2));//小区SINR平均
            objs8.Add(Math.Round(getValidData(data.sampleTotal, data.cExt.fSampleDistSum), 2));//小区平均通信距离

            objs8.Add(data.antCfg.strVender);//主设备厂家
            objs8.Add("460-00-" + data.antCfg.iEnodebID.ToString() + "-" + data.antCfg.iSectorID.ToString());//CGI
            objs8.Add(data.antPara.strbeamwidth);//波束宽度
            objs8.Add(data.antCfg.strType);//覆盖类型

            int iInfoShow = 1;
            if (data.antPara.iStatus == 1)
            {
                if (data.antCfg.strBtsType.Contains("E") || data.antCfg.strBtsType.Contains("A"))
                    iInfoShow = 0;
            }
            else
                iInfoShow = 0;

            double[] paraArray = data.ciItem.calcCellPara();
            if (iInfoShow == 0)
            {
                for (int i = 0; i < 20; i++)
                {
                    objs8.Add("-");
                }
            }
            else
            {
                objs8.Add(Math.Round(paraArray[0], 2).ToString());//Gmax
                objs8.Add(Math.Round(paraArray[1], 0).ToString());//3dB功率角
                objs8.Add(Math.Round(paraArray[2], 0).ToString());//6dB功率角
                objs8.Add(Math.Round(data.antPara.drangeport1, 2).ToString());//端口1幅度
                objs8.Add(Math.Round(data.antPara.drangeport2, 2).ToString());//端口2幅度
                objs8.Add(Math.Round(data.antPara.drangeport3, 2).ToString());//端口3幅度
                objs8.Add(Math.Round(data.antPara.drangeport4, 2).ToString());//端口4幅度
                objs8.Add(Math.Round(data.antPara.drangeport5, 2).ToString());//端口5幅度
                objs8.Add(Math.Round(data.antPara.drangeport6, 2).ToString());//端口6幅度
                objs8.Add(Math.Round(data.antPara.drangeport7, 2).ToString());//端口7幅度
                objs8.Add(Math.Round(data.antPara.drangeport8, 2).ToString());//端口8幅度
                objs8.Add(Math.Round(data.antPara.dphaseport1, 2).ToString());//端口1相位
                objs8.Add(Math.Round(data.antPara.dphaseport2, 2).ToString());//端口2相位
                objs8.Add(Math.Round(data.antPara.dphaseport3, 2).ToString());//端口3相位
                objs8.Add(Math.Round(data.antPara.dphaseport4, 2).ToString());//端口4相位
                objs8.Add(Math.Round(data.antPara.dphaseport5, 2).ToString());//端口5相位
                objs8.Add(Math.Round(data.antPara.dphaseport6, 2).ToString());//端口6相位
                objs8.Add(Math.Round(data.antPara.dphaseport7, 2).ToString());//端口7相位
                objs8.Add(Math.Round(data.antPara.dphaseport8, 2).ToString());//端口8相位
                objs8.Add(data.antPara.isSmartAnt);//是否(8通道)智能天线
            }

            objs8.Add(Math.Round(data.antCfg.预置下倾角, 2));//预置下倾角
            objs8.Add(Math.Round(data.antCfg.机械下倾角, 2));//机械下倾角
            objs8.Add(Math.Round(data.antCfg.电调下倾角, 2));//电调下倾角
            objs8.Add(Math.Round(data.antCfg.挂高, 2));//挂高

            objs8.Add(Math.Round(data.cellPara.F上行吞吐量, 2));//上行吞吐量
            objs8.Add(Math.Round(data.cellPara.F下行吞吐量, 2));//下行吞吐量
            objs8.Add(Math.Round(data.cellPara.F无线接通率, 2));//无线接通率
            objs8.Add(Math.Round(data.cellPara.F无线掉线率, 2));//无线掉线率
            objs8.Add(Math.Round(data.cellPara.F切换成功率, 2));//切换成功率
            objs8.Add(Math.Round(data.cellPara.fERAB建立成功率, 2));//ERAB建立成功率
            objs8.Add(Math.Round(data.cellPara.fERAB掉线率, 2));//ERAB掉线率

            objs8.Add(Math.Round(data.cellMrData.lteMRItem.dAvgRsrp, 2));//平均RSRP
            objs8.Add(Math.Round(data.cellMrData.lteMRItem.dAvgSinr, 2));//平均SINR
            objs8.Add(Math.Round(data.cellMrData.lteMRItem.dRate95Rsrp, 2));//-95覆盖率
            objs8.Add(Math.Round(data.cellMrData.lteMRItem.dRate110Rsrp, 2));//-110覆盖率

            objs8.Add(data.cellMrData.lteMRCoverItem.IMRO总采样点数);//MRO总采样点数
            objs8.Add(data.cellMrData.lteMRCoverItem.I重叠覆盖条件采样点数);//重叠覆盖条件采样点数
            objs8.Add(Math.Round(data.cellMrData.lteMRCoverItem.F重叠覆盖指数, 2) + "%");//重叠覆盖指数
            objs8.Add(data.cellMrData.lteMRCoverItem.I过覆盖影响小区数);//过覆盖影响小区数
            objs8.Add(data.cellMrData.lteMRCoverItem.S高重叠覆盖小区);//高重叠覆盖小区
            objs8.Add(data.cellMrData.lteMRCoverItem.S过覆盖小区);//过覆盖小区

            objs8.Add(data.iAntMaxDir);//MR主覆盖角
            objs8.Add(data.iDirDiff);  //与天线工参偏差值
            objs8.Add(data.strIsMRCell);//是否可MR波形重构

            return objs8;
        }

        /// <summary>
        /// 天线辐射辅值
        /// </summary>
        private void FillAntValue(NPOIRow row, CellAngleData data)
        {
            row.AddCellValue(data.strcityname);//地市
            row.AddCellValue(data.cellname);//小区名
            row.AddCellValue(data.strgridname);//所在网格
            row.AddCellValue(data.antPara.strdevvender);//主设备厂家
            row.AddCellValue(data.strbscname);//BSC名
            row.AddCellValue(data.uarfcn);//频点标示

            row.AddCellValue(Math.Round(getValidData(data.cExt.rsrpNum, data.cExt.rsrpSum), 2));//RSRP均值
            row.AddCellValue(Math.Round(getValidData(data.cExt.sinrNum, data.cExt.sinrSum * 1.0), 2));//小区SINR平均
            row.AddCellValue(Math.Round(getValidData(data.sampleTotal, data.cExt.fSampleDistSum), 2));//小区平均通信距离
            row.AddCellValue(data.antPara.strbeamwidth);//波束宽度
            row.AddCellValue(data.sampleTotal);//总采样点
            row.AddCellValue(data.iangle_dir);

            int iNum = data.antInfoList.Count;
            AntInfto mainAntInfo = new AntInfto();//主瓣
            AntInfto backAntInfo;//背瓣
            AntInfto side1AntInfo;//旁瓣1
            AntInfto side2AntInfo;//旁瓣2
            AntInfto side3AntInfo;//旁瓣3

            if (iNum > 0)
            {
                mainAntInfo = data.antInfoList[0];
                row.AddCellValue(mainAntInfo.SampleRate);//主瓣采样点比例
                row.AddCellValue(mainAntInfo.MaxRsrp);//主瓣最强信号强度
            }
            else
            {
                row.AddCellValue("-");
                row.AddCellValue("-");
            }

            int iSideNum = iNum > 2 ? iNum - 2 : 0;
            row.AddCellValue(iSideNum);//疑似旁瓣数量
            if (iNum > 2)
            {
                side1AntInfo = data.antInfoList[2];
                row.AddCellValue(side1AntInfo.MaxRsrpDir);//旁瓣1辐射方向
                //row.AddCellValue(CalcAntDir(side1AntInfo.MaxRsrpDir,mainAntInfo.MaxRsrpDir));//旁瓣1与主瓣方向角度差值
                row.AddCellValue(side1AntInfo.MaxRsrp);//旁瓣1最强信号强度
                row.AddCellValue(side1AntInfo.MeanRsrp);//旁瓣1平均信号强度
                row.AddCellValue(side1AntInfo.SampleRate);//旁瓣1采样点比例
            }
            else
                AddEmptyData(row);

            if (iNum > 3)
            {
                side2AntInfo = data.antInfoList[3];
                row.AddCellValue(side2AntInfo.MaxRsrpDir);//旁瓣2辐射方向
                //row.AddCellValue(CalcAntDir(side2AntInfo.MaxRsrpDir, mainAntInfo.MaxRsrpDir));//旁瓣2与主瓣方向角度差值
                row.AddCellValue(side2AntInfo.MaxRsrp);//旁瓣2最强信号强度
                row.AddCellValue(side2AntInfo.MeanRsrp);//旁瓣2平均信号强度
                row.AddCellValue(side2AntInfo.SampleRate);//旁瓣2采样点比例
            }
            else
                AddEmptyData(row);

            if (iNum > 4)
            {
                side3AntInfo = data.antInfoList[4];
                row.AddCellValue(side3AntInfo.MaxRsrpDir);//旁瓣3辐射方向
                //row.AddCellValue(CalcAntDir(side3AntInfo.MaxRsrpDir, mainAntInfo.MaxRsrpDir));//旁瓣3与主瓣方向角度差值
                row.AddCellValue(side3AntInfo.MaxRsrp);//旁瓣3最强信号强度
                row.AddCellValue(side3AntInfo.MeanRsrp);//旁瓣3平均信号强度
                row.AddCellValue(side3AntInfo.SampleRate);//旁瓣3采样点比例
            }
            else
                AddEmptyData(row);

            if (iNum > 1 && data.antInfoList[1].MaxRsrp >= 0)
            {
                backAntInfo = data.antInfoList[1];
                row.AddCellValue(backAntInfo.SampleRate);//背瓣采样点比例
                row.AddCellValue(mainAntInfo.MaxRsrp <= -20 ? 0 - backAntInfo.MaxRsrp : mainAntInfo.MaxRsrp - backAntInfo.MaxRsrp);//前后比
                //row.AddCellValue(backAntInfo.MaxRsrp);//背瓣信号强度
            }
            else
            {
                row.AddCellValue("-");
                row.AddCellValue("-");
            }
        }

        private void AddEmptyData(NPOIRow row)
        {
            row.AddCellValue("-");//辐射方向
            row.AddCellValue("-");//最强信号强度
            row.AddCellValue("-");//平均信号强度
            row.AddCellValue("-");//采样点比例
        }

        /// <summary>
        /// 按角度拆分导出数据
        /// </summary>
        private List<NPOIRow> FillAngleValue(CellAngleData data)
        {
            int[] rsrpArray = data.ciItem.rsrpArray;
            double[] newRsrpArray = data.ciItem.newRsrpArray;
            int[] sinrArray = data.ciItem.sinrArray;
            double[] sampleDistArray = data.ciItem.sampArray;
            int[] sampleNumArray = data.ciItem.sampNumArray;

            int iStart = 0;
            int iEnd = 360;

            List<NPOIRow> dataNpoi = new List<NPOIRow>();
            NPOIRow nr;

            List<object> objsL = new List<object>();
            objsL.Add(data.cellname);
            objsL.Add("RSRP");
            for (int i = iStart; i < iEnd; i++)
            {
                int iRsrp = sampleNumArray[i] == 0 ? -140 : rsrpArray[i] / sampleNumArray[i];
                objsL.Add(iRsrp);
            }
            nr = new NPOIRow();
            nr.cellValues = objsL;
            dataNpoi.Add(nr);

            objsL = new List<object>();
            objsL.Add(data.cellname);
            objsL.Add("平滑RSRP");
            for (int i = iStart; i < iEnd; i++)
            {
                objsL.Add(Math.Round(newRsrpArray[i], 2));
            }
            nr = new NPOIRow();
            nr.cellValues = objsL;
            dataNpoi.Add(nr);

            objsL = new List<object>();
            objsL.Add(data.cellname);
            objsL.Add("SINR");
            for (int i = iStart; i < iEnd; i++)
            {
                objsL.Add(sampleNumArray[i] == 0 ? -25 : sinrArray[i] / sampleNumArray[i]);
            }
            nr = new NPOIRow();
            nr.cellValues = objsL;
            dataNpoi.Add(nr);

            objsL = new List<object>();
            objsL.Add(data.cellname);
            objsL.Add("通信距离");
            for (int i = iStart; i < iEnd; i++)
            {
                objsL.Add(Math.Round(sampleNumArray[i] == 0 ? 0 : sampleDistArray[i] / sampleNumArray[i], 2));
            }
            nr = new NPOIRow();
            nr.cellValues = objsL;
            dataNpoi.Add(nr);

            return dataNpoi;
        }

        /// <summary>
        /// 整理及导出天线二维数据的CSV文件
        /// </summary>
        private void dealCellAngleSecData()
        {
            string strFileName = Application.StartupPath + "\\userData\\" + string.Format("二维天线数据_{0:yyyyMMddhhmmss}.csv", DateTime.Now);
            int iStart = 0;
            int iEnd = 360;
            WaitBox.ProgressPercent = 0;
            int iCount = dicUtranCellAngelData.Count;
            int iNum = 0;

            StreamWriter colSr = new StreamWriter(strFileName, true, Encoding.Default);
            StringBuilder strColName = new StringBuilder("CellName, DistLevel");
            for (int i = 0; i < 360; i++)
            {
                strColName.Append(",");
                strColName.Append(i.ToString());
            }
            colSr.WriteLine(strColName.ToString());

            foreach (string strCellName in dicUtranCellAngelData.Keys)
            {
                WaitBox.ProgressPercent = (int)(100 * ((iNum * 1.0) / iCount));
                CellAngleData caData = dicUtranCellAngelData[strCellName];
                for (int j = 0; j < 200; j++)
                {
                    StringBuilder strValue = new StringBuilder();
                    strValue.AppendFormat("{0},{1}", strCellName, j.ToString());
                    for (int i = iStart; i < iEnd; i++)
                    {
                        int idx = i * 1000 + j;
                        int iTmpRsrp = -140;
                        if (caData.ciItem.antTwoDic.ContainsKey(idx))
                            iTmpRsrp = caData.ciItem.antTwoDic[idx].IRsrp / caData.ciItem.antTwoDic[idx].ISampNum;
                        strColName.Append(",");
                        strColName.Append(iTmpRsrp.ToString());
                    }
                    colSr.WriteLine(strValue.ToString());
                }
                iNum++;
            }
            colSr.Flush();
            colSr.Close();

            WaitBox.Close();
        }

        #endregion

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE扫频专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["BDtNbInfo"] = BDtNbInfo;
                param["BSecAna"] = BSecAna;
                param["BSecDataExport"] = BSecDataExport;
                param["BSampleShow"] = BSampleShow;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("BDtNbInfo"))
                {
                    BDtNbInfo = (bool)param["BDtNbInfo"];
                }
                if (param.ContainsKey("BSecAna"))
                {
                    BSecAna = (bool)param["BSecAna"];
                }
                if (param.ContainsKey("BSecDataExport"))
                {
                    BSecDataExport = (bool)param["BSecDataExport"];
                }
                if (param.ContainsKey("BSampleShow"))
                {
                    BSampleShow = (bool)param["BSampleShow"];
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new LteScanAntennaProperties_LTE(this);
            }
        }
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }

        protected override void saveBackgroundData()
        {
            if (dicUtranCellAngelData.Count == 0)
                return;
            DIYQueryDBConect diyQuery = new DIYQueryDBConect(MainModel, strCityTypeName.Replace("市", ""));
            diyQuery.Query();
            Dictionary<string, string> cityConnDic = diyQuery.strConnDic;
            string strCurConn = cityConnDic[strCityTypeName.Replace("市", "")];
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            string tableName = "tb_probchk_cell_angle_result";
            DataTable importTable = CreateImportTable(tableName);
            DiySqlNonQuery queryNon = null;
            string sql = "if exists (select * from sysobjects where name = '" + tableName + "') delete  from " + tableName + " where istime = " + Condition.Periods[0].IBeginTime + " and ietime = " + Condition.Periods[0].IEndTime
                            + " and strprojectid = '" + BackgroundFuncConfigManager.GetInstance().ProjectType + "'";
            queryNon = new DiySqlNonQuery(MainModel, sql);
            queryNon.Query();

            foreach (CellAngleData cad in dicUtranCellAngelData.Values)
            {
                BackgroundResult result = cad.ConvertToBackgroundResult();
                result.ProjectString = BackgroundFuncConfigManager.GetInstance().ProjectType;
                result.ISTime = Condition.Periods[0].IBeginTime;
                result.IETime = Condition.Periods[0].IEndTime;
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
                #region 角度及画图信息
                StringBuilder RSRP = new StringBuilder();//|1_23.1|
                StringBuilder 平滑RSRP = new StringBuilder();//|1_23.1|
                StringBuilder SINR = new StringBuilder();//|1_23.1|
                StringBuilder 过覆盖指数 = new StringBuilder();//|1_23.1|
                StringBuilder 通讯距离 = new StringBuilder();//|1_23.1|
                StringBuilder 总采样点 = new StringBuilder();//|1_23.1|
                foreach (int key in cad.ciItem.antAngleDic.Keys)
                {
                    RSRP.Append("|" + key + "_" + cad.ciItem.antAngleDic[key].IRsrp);
                    平滑RSRP.Append("|" + key + "_" + cad.ciItem.antAngleDic[key].DRsrpNew);
                    SINR.Append("|" + key + "_" + cad.ciItem.antAngleDic[key].ISinr);
                    过覆盖指数.Append("|" + key + "_" + cad.ciItem.antAngleDic[key].DCover);
                    通讯距离.Append("|" + key + "_" + cad.ciItem.antAngleDic[key].DSampDist);
                    总采样点.Append("|" + key + "_" + cad.ciItem.antAngleDic[key].ISampNum);
                }
                RSRP = RSRP.Remove(0, 1);
                平滑RSRP = 平滑RSRP.Remove(0, 1);
                SINR = SINR.Remove(0, 1);
                过覆盖指数 = 过覆盖指数.Remove(0, 1);
                通讯距离 = 通讯距离.Remove(0, 1);
                总采样点 = 总采样点.Remove(0, 1);

                StringBuilder strModelMaxArray = new StringBuilder(); //最大模型
                foreach (double date in cad.ciItem.modelMaxArray)
                    strModelMaxArray.Append("|" + date);
                strModelMaxArray = strModelMaxArray.Remove(0, 1);

                StringBuilder strRSRP = new StringBuilder();
                StringBuilder strSampleNum = new StringBuilder();
                foreach (int key in cad.ciItem.antVertDic.Keys)
                {
                    strRSRP.Append("|" + key + "_" + cad.ciItem.antVertDic[key].IRsrp);
                    strSampleNum.Append("|" + key + "_" + cad.ciItem.antVertDic[key].ISampNum);
                }
                strRSRP = strRSRP.Remove(0, 1);
                strSampleNum = strSampleNum.Remove(0, 1);
                object[] values = new object[] { Condition.Periods[0].IBeginTime, Condition.Periods[0].IEndTime, cad.cellname,
                    result.ProjectString, cad.iEci, RSRP.ToString(), 平滑RSRP.ToString(), SINR.ToString(), 过覆盖指数.ToString(),
                    通讯距离.ToString(), strModelMaxArray.ToString(), strRSRP.ToString(), strSampleNum.ToString(), 总采样点.ToString() };
                importTable.Rows.Add(values);
                #endregion

            }
            if (importTable.Rows.Count != 0)
            {
                SqlBulkCopy bcp = new SqlBulkCopy(strCurConn);
                try
                {
                    bcp.BatchSize = 2000;
                    bcp.BulkCopyTimeout = 3600;
                    bcp.DestinationTableName = importTable.TableName;
                    bcp.WriteToServer(importTable);
                }
                catch (Exception ex)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("保存小区角度信息失败！\n\t" + ex.Message);
                }
                finally
                {
                    bcp.Close();
                }
                BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), Condition.Periods[0].IBeginTime,
                    Condition.Periods[0].IEndTime, bgResultList);
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo("入库成功！");
            }
        }

        private DataTable CreateImportTable(string strtable)
        {
            DataTable importTable = new DataTable(strtable);
            importTable.Columns.Add("istime", typeof(int));
            importTable.Columns.Add("ietime", typeof(int));
            importTable.Columns.Add("strcellname", typeof(string));
            importTable.Columns.Add("strprojectid", typeof(string));
            importTable.Columns.Add("iECI", typeof(int));
            importTable.Columns.Add("strRSRP", typeof(string));
            importTable.Columns.Add("strpRSRP", typeof(string));
            importTable.Columns.Add("strSINR", typeof(string));
            importTable.Columns.Add("过覆盖指数", typeof(string));
            importTable.Columns.Add("通讯距离", typeof(string));
            importTable.Columns.Add("strModelMaxArray", typeof(string));
            importTable.Columns.Add("strRSRP200", typeof(string));
            importTable.Columns.Add("strSampleNum", typeof(string));
            importTable.Columns.Add("SampleNum", typeof(string));
            return importTable;
        }

        protected override void getBackgroundData()
        {
            string[] strProject = BackgroundFuncConfigManager.GetInstance().ProjectType.Split('|');
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetResult_Cell(Condition.Periods[0].BeginTime.AddSeconds(1),
                Condition.Periods[0].EndTime.AddSeconds(-1), GetSubFuncID(), Name, StatType, strProject.Length == 1 ? strProject[0] : strProject[1]);
        }

        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                string strCityName = bgResult.GetImageValueString();
                string strCellName = bgResult.GetImageValueString();
                int strLac = bgResult.GetImageValueInt();
                int strCi = bgResult.GetImageValueInt();
                string strdevvender = bgResult.GetImageValueString();//设备厂家
                string strbscname = bgResult.GetImageValueString();//BSC名
                int uarfcn = bgResult.GetImageValueInt();//频点标示
                float rsrpavg = bgResult.GetImageValueFloat();//小区RSRP均值
                float SINRavg = bgResult.GetImageValueFloat();//小区SINR平均
                float fSampleDistavg = bgResult.GetImageValueFloat();//小区平均通信距离
                float iOverCellNum = 0;//bgResult.GetImageValueFloat();//小区过覆盖指数
                string strbeamwidth = bgResult.GetImageValueString();//波束宽度

                #region 整理描述值
                StringBuilder sb = new StringBuilder();
                sb.Append("地市：");
                sb.Append(strCityName);
                sb.Append("\r\n");
                sb.Append("小区名：");
                sb.Append(strCellName);
                sb.Append("\r\n");
                sb.Append("小区LAC：");
                sb.Append(strLac);
                sb.Append("\r\n");
                sb.Append("小区CI：");
                sb.Append(strCi);
                sb.Append("\r\n");
                sb.Append("设备厂家：");
                sb.Append(strdevvender);
                sb.Append("\r\n");
                sb.Append("BSC名：");
                sb.Append(strbscname);
                sb.Append("\r\n");
                sb.Append("频点标示：");
                sb.Append(uarfcn);
                sb.Append("\r\n");
                sb.Append("小区RSRP均值：");
                sb.Append(rsrpavg);
                sb.Append("\r\n");
                sb.Append("小区SINR平均：");
                sb.Append(SINRavg);
                sb.Append("\r\n");
                sb.Append("小区平均通信距离：");
                sb.Append(fSampleDistavg);
                sb.Append("\r\n");
                sb.Append("小区过覆盖指数：");
                sb.Append(iOverCellNum);
                sb.Append("\r\n");
                sb.Append("波束宽度：");
                sb.Append(strbeamwidth);
                sb.Append("\r\n");

                StringBuilder sb1 = new StringBuilder();
                sb1.Append("主瓣采样点比例：");
                sb1.Append(bgResult.GetImageValueString());
                sb1.Append("\r\n");
                sb1.Append("主瓣最强信号强度：");
                sb1.Append(bgResult.GetImageValueString());
                sb1.Append("\r\n");
                sb1.Append("疑似旁瓣数量：");
                sb1.Append(bgResult.GetImageValueString());
                sb1.Append("\r\n");

                sb1.Append("旁瓣1辐射方向：");
                sb1.Append(bgResult.GetImageValueString());
                sb1.Append("\r\n");
                sb1.Append("旁瓣1最强信号强度：");
                sb1.Append(bgResult.GetImageValueString());
                sb1.Append("\r\n");
                sb1.Append("旁瓣1平均信号强度：");
                sb1.Append(bgResult.GetImageValueString());
                sb1.Append("\r\n");
                sb1.Append("旁瓣1采样点比例：");
                sb1.Append(bgResult.GetImageValueString());
                sb1.Append("\r\n");

                sb1.Append("旁瓣2辐射方向：");
                sb1.Append(bgResult.GetImageValueString());
                sb1.Append("\r\n");
                sb1.Append("旁瓣2最强信号强度：");
                sb1.Append(bgResult.GetImageValueString());
                sb1.Append("\r\n");
                sb1.Append("旁瓣2平均信号强度：");
                sb1.Append(bgResult.GetImageValueString());
                sb1.Append("\r\n");
                sb1.Append("旁瓣2采样点比例：");
                sb1.Append(bgResult.GetImageValueString());
                sb1.Append("\r\n");

                sb1.Append("旁瓣3辐射方向：");
                sb1.Append(bgResult.GetImageValueString());
                sb1.Append("\r\n");
                sb1.Append("旁瓣3最强信号强度：");
                sb1.Append(bgResult.GetImageValueString());
                sb1.Append("\r\n");
                sb1.Append("旁瓣3平均信号强度：");
                sb1.Append(bgResult.GetImageValueString());
                sb1.Append("\r\n");
                sb1.Append("旁瓣3采样点比例：");
                sb1.Append(bgResult.GetImageValueString());

                sb1.Append("\r\n");
                sb1.Append("背瓣采样点比例：");
                sb1.Append(bgResult.GetImageValueString());
                sb1.Append("\r\n");
                sb1.Append("前后比：");
                sb1.Append(bgResult.GetImageValueString());
                #endregion

                bgResult.ImageDesc = sb.ToString() + sb1.ToString();
            }
        }

        public Dictionary<int, AntInfoItem> getBackgroundData(int istime, int ietime, string strPro)
        {
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetResult_Cell(istime + 1,
                ietime - 1, GetSubFuncID(), Name, StatType, strPro);
            return readBackgroundImageDesc();
        }

        private Dictionary<int, AntInfoItem> readBackgroundImageDesc()
        {
            Dictionary<int, AntInfoItem> antInfoDic = new Dictionary<int, AntInfoItem>();

            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                bgResult.GetImageValueString();
                string strCellName = bgResult.GetImageValueString();
                bgResult.GetImageValueInt();
                int strCi = bgResult.GetImageValueInt();
                bgResult.GetImageValueString();//设备厂家
                bgResult.GetImageValueString();//BSC名
                bgResult.GetImageValueInt();//频点标示
                bgResult.GetImageValueFloat();//小区RSRP均值
                bgResult.GetImageValueFloat();//小区SINR平均
                bgResult.GetImageValueFloat();//小区平均通信距离
                //float iOverCellNum = bgResult.GetImageValueFloat();//小区过覆盖指数
                bgResult.GetImageValueString();//波束宽度

                #region 整理描述值

                AntInfoItem antInfoitem = new AntInfoItem();
                try
                {
                    antInfoitem.cellname = strCellName;
                    antInfoitem.SampNum = bgResult.SampleCount;
                    antInfoitem.mSampleRate = bgResult.GetImageValueString();
                    antInfoitem.mMaxRsrp = bgResult.GetImageValueString();
                    antInfoitem.SuspectedFlap = bgResult.GetImageValueString();

                    antInfoitem.p1MaxRsrpDir = bgResult.GetImageValueString();
                    antInfoitem.p1MaxRsrp = bgResult.GetImageValueString();
                    antInfoitem.p1MeanRsrp = bgResult.GetImageValueString();
                    antInfoitem.p1SampleRate = bgResult.GetImageValueString();

                    antInfoitem.p2MaxRsrpDir = bgResult.GetImageValueString();
                    antInfoitem.p2MaxRsrp = bgResult.GetImageValueString();
                    antInfoitem.p2MeanRsrp = bgResult.GetImageValueString();
                    antInfoitem.p2SampleRate = bgResult.GetImageValueString();

                    antInfoitem.p3MaxRsrpDir = bgResult.GetImageValueString();
                    antInfoitem.p3MaxRsrp = bgResult.GetImageValueString();
                    antInfoitem.p3MeanRsrp = bgResult.GetImageValueString();
                    antInfoitem.p3SampleRate = bgResult.GetImageValueString();
                    
                    antInfoitem.bSampleRate = bgResult.GetImageValueString();
                    antInfoitem.bDiscrepancy = bgResult.GetImageValueString();
                }
                catch
                {
                    //continue
                }
                antInfoDic[strCi] = antInfoitem;
                #endregion
            }
            return antInfoDic;
        }
        #endregion


        public class CellAngleData
        {
            //小区
            public string strTime { get; set; }
            public string strcityname { get; set; }
            public string strgridname { get; set; }
            public string strbscname{ get; set; }
            public string cellname{ get; set; }
            public int iTac{ get; set; }
            public int iEci{ get; set; }
            public int uarfcn{ get; set; }
            public LTEBandType band{ get; set; }
            public int sampleTotal{ get; set; }//该区间采样点数

            //扩展
            public string anaType{ get; set; }//天线类型
            public int iangle_ob{ get; set; }//下倾角
            public int ialtitude{ get; set; }//挂高
            public int iangle_dir{ get; set; }//方向角

            public CellAngleDataExt cExt { get; set; }
            public Dictionary<string, AngleData> angleDatas { get; set; }
            public CellInfoItem ciItem{ get; set; }
            public AntennaPara antPara{ get; set; }
            public CellPara cellPara{ get; set; }
            public AntCfgSub antCfg{ get; set; }
            public CellMrData cellMrData{ get; set; }

            public List<TestPoint> tpList { get; set; }
            public Dictionary<string, int> tpIndexDic { get; set; }//存放本小区在每个采样点中序号，以便回放呈现

            public int iDirDiff{ get; set; }
            public int iAntMaxDir{ get; set; }
            public string strAntResult{ get; set; }
            public string strIsMRCell{ get; set; }
            public Dictionary<int, int> dirSampleDic { get; set; }

            public string strProblemType{ get; set; }//判断天线问题类型
            public double cellLongitude{ get; set; }
            public double cellLatitude{ get; set; }
            public List<LongLat> longLatTestList { get; set; }//小区测试模型
            public List<LongLat> longLatModelList { get; set; }//小区理想模型
            public List<LongLat> mrAoaList { get; set; }//MR数据角度级采样点
            public List<BtsSubInfo> btsInfoList { get; set; }
            public List<AntInfto> antInfoList { get; set; }//天线辐射性能

            public CellAngleData(bool isInitSecArray)
            {
                strTime = "";
                strcityname = "";
                strgridname = "";
                strbscname = "";
                cellname = "";
                iTac = 0;
                iEci = 0;
                uarfcn = 0;
                band = LTEBandType.Undefined;
                sampleTotal = 0;

                anaType = "";
                iangle_ob = 0;
                ialtitude = 0;
                iangle_dir = 0;

                iDirDiff = 0;
                iAntMaxDir = 0;
                strAntResult = "";
                strIsMRCell = "否";

                strProblemType = "";
                cellLongitude = 0;
                cellLatitude = 0;
                ciItem = new CellInfoItem(isInitSecArray);
                antPara = new AntennaPara();
                cellPara = new CellPara();
                antCfg = new AntCfgSub();
                cellMrData = new CellMrData();

                cExt = new CellAngleDataExt();
                angleDatas = new Dictionary<string, AngleData>();

                tpList = new List<TestPoint>();
                tpIndexDic = new Dictionary<string, int>();

                dirSampleDic = new Dictionary<int, int>();

                longLatTestList = new List<LongLat>();
                longLatModelList = new List<LongLat>();
                mrAoaList = new List<LongLat>();
                btsInfoList = new List<BtsSubInfo>();
                antInfoList = new List<AntInfto>();
            }

            private double getValidData(double count, double sum)
            {
                if (count == 0)
                {
                    return 0;
                }
                return sum / count;
            }

            public BackgroundResult ConvertToBackgroundResult()
            {
                try
                {
                    BackgroundResult bgResult = new BackgroundResult();
                    bgResult.LongitudeMid = cellLongitude;
                    bgResult.LatitudeMid = cellLatitude;
                    bgResult.GridDesc = strgridname;
                    bgResult.SampleCount = sampleTotal;

                    bgResult.AddImageValue(strcityname);//地市
                    bgResult.AddImageValue(cellname);//小区名
                    bgResult.AddImageValue(iTac);//LAC
                    bgResult.AddImageValue(iEci);//CI
                    bgResult.AddImageValue(antPara.strdevvender);//主设备厂家
                    bgResult.AddImageValue(strbscname);//BSC名
                    bgResult.AddImageValue(uarfcn);//频点标示
                    bgResult.AddImageValue((float)Math.Round(getValidData(cExt.rsrpNum, cExt.rsrpSum), 2));//RSRP均值
                    bgResult.AddImageValue((float)Math.Round(getValidData(cExt.sinrNum, cExt.sinrSum * 1.0), 2));//小区SINR平均
                    bgResult.AddImageValue((float)Math.Round(getValidData(sampleTotal, cExt.fSampleDistSum), 2));//小区平均通信距离
                    //过覆盖指数
                    bgResult.AddImageValue(antPara.strbeamwidth);//波束宽度
                    int iNum = antInfoList.Count;
                    AntInfto mainAntInfo = new AntInfto();//主瓣
                    AntInfto backAntInfo;//背瓣
                    AntInfto side1AntInfo;//旁瓣1
                    AntInfto side2AntInfo;//旁瓣2
                    AntInfto side3AntInfo;//旁瓣3
                    if (iNum > 0)
                    {
                        mainAntInfo = antInfoList[0];
                        //bgResult.AddImageValue(mainAntInfo.MaxRsrpDir.ToString());//主瓣辐射方向
                        //bgResult.AddImageValue(((Math.Abs(mainAntInfo.MaxRsrpDir - iangle_dir) + 180) % 180).ToString());//与天线工参偏差值
                        //bgResult.AddImageValue(mainAntInfo.MeanRsrp.ToString());//主瓣平均信号强度
                        bgResult.AddImageValue(mainAntInfo.SampleRate);//主瓣采样点比例
                        bgResult.AddImageValue(mainAntInfo.MaxRsrp.ToString());//主瓣最强信号强度
                    }
                    else
                    {
                        bgResult.AddImageValue("-");
                        bgResult.AddImageValue("-");
                    }

                    int iSideNum = iNum > 2 ? iNum - 2 : 0;
                    bgResult.AddImageValue(iSideNum.ToString());//疑似旁瓣数量
                    if (iNum > 2)
                    {
                        side1AntInfo = antInfoList[2];
                        bgResult.AddImageValue(side1AntInfo.MaxRsrpDir.ToString());//旁瓣1辐射方向
                        //bgResult.AddImageValue(((Math.Abs(side1AntInfo.MaxRsrpDir - mainAntInfo.MaxRsrpDir) + 180) % 180).ToString());//旁瓣1与主瓣方向角度差值
                        bgResult.AddImageValue(side1AntInfo.MaxRsrp.ToString());//旁瓣1最强信号强度
                        bgResult.AddImageValue(side1AntInfo.MeanRsrp.ToString());//旁瓣1平均信号强度
                        bgResult.AddImageValue(side1AntInfo.SampleRate);//旁瓣1采样点比例
                    }
                    else
                        fillAntNullValue(ref bgResult);

                    if (iNum > 3)
                    {
                        side2AntInfo = antInfoList[3];
                        bgResult.AddImageValue(side2AntInfo.MaxRsrpDir.ToString());//旁瓣2辐射方向
                        //bgResult.AddImageValue(((Math.Abs(side2AntInfo.MaxRsrpDir - mainAntInfo.MaxRsrpDir) + 180) % 180).ToString());//旁瓣2与主瓣方向角度差值
                        bgResult.AddImageValue(side2AntInfo.MaxRsrp.ToString());//旁瓣2最强信号强度
                        bgResult.AddImageValue(side2AntInfo.MeanRsrp.ToString());//旁瓣2平均信号强度
                        bgResult.AddImageValue(side2AntInfo.SampleRate);//旁瓣2采样点比例
                    }
                    else
                        fillAntNullValue(ref bgResult);

                    if (iNum > 4)
                    {
                        side3AntInfo = antInfoList[4];
                        bgResult.AddImageValue(side3AntInfo.MaxRsrpDir.ToString());//旁瓣3辐射方向
                        //bgResult.AddImageValue(((Math.Abs(side3AntInfo.MaxRsrpDir - mainAntInfo.MaxRsrpDir) + 180) % 180).ToString());//旁瓣3与主瓣方向角度差值
                        bgResult.AddImageValue(side3AntInfo.MaxRsrp.ToString());//旁瓣3最强信号强度
                        bgResult.AddImageValue(side3AntInfo.MeanRsrp.ToString());//旁瓣3平均信号强度
                        bgResult.AddImageValue(side3AntInfo.SampleRate);//旁瓣3采样点比例
                    }
                    else
                        fillAntNullValue(ref bgResult);

                    if (iNum > 1 && antInfoList[1].MaxRsrp >= 0)
                    {
                        backAntInfo = antInfoList[1];
                        bgResult.AddImageValue(backAntInfo.SampleRate);//背瓣采样点比例
                        bgResult.AddImageValue((mainAntInfo.MaxRsrp - backAntInfo.MaxRsrp).ToString());//前后比
                        //bgResult.AddImageValue(backAntInfo.MaxRsrp.ToString());//背瓣信号强度
                    }
                    else
                    {
                        bgResult.AddImageValue("-");
                        bgResult.AddImageValue("-");
                    }
                    //与天线工参偏差值,旁瓣1,2,3与主瓣方向角度差值,背瓣前后比。数据未加入数据库，需显示时自行计算

                    return bgResult;
                }
                catch (Exception ee)
                {
                    log.Error(ee.Message);
                    return null;
                }
            }

            private void fillAntNullValue(ref BackgroundResult bgResult)
            {
                bgResult.AddImageValue("-");
                bgResult.AddImageValue("-");
                bgResult.AddImageValue("-");
                bgResult.AddImageValue("-");
            }
        }

        public class AntInfoItem
        {

            public AntInfoItem()
            {
                SampNum = 0;

                cellname = "-";
                mMaxRsrp = "-";
                mSampleRate = "-";
                SuspectedFlap = "-";

                p1MaxRsrpDir = "-";
                p1MaxRsrp = "-";
                p1MeanRsrp = "-";
                p1SampleRate = "-";

                p2MaxRsrpDir = "-";
                p2MaxRsrp = "-";
                p2MeanRsrp = "-";
                p2SampleRate = "-";

                p3MaxRsrpDir = "-";
                p3MaxRsrp = "-";
                p3MeanRsrp = "-";
                p3SampleRate = "-";        

                bDiscrepancy = "-";
                bSampleRate = "-";

                ciItem = new CellInfoItem(true);
            }
            
            [Description("采样点总数")]
            public int SampNum { get; set; }
            public string cellname { get; set; }
            //主瓣信息
            public string mMaxRsrp { get; set; }
            public string mSampleRate { get; set; }
            public string SuspectedFlap { get; set; }
            //旁瓣1信息
            public string p1MaxRsrpDir { get; set; }
            public string p1MaxRsrp { get; set; }
            public string p1MeanRsrp { get; set; }
            public string p1SampleRate { get; set; }

            //旁瓣2信息
            public string p2MaxRsrpDir { get; set; }
            public string p2MaxRsrp { get; set; }
            public string p2MeanRsrp { get; set; }
            public string p2SampleRate { get; set; }

            //旁瓣3信息
            public string p3MaxRsrpDir { get; set; }
            public string p3MaxRsrp { get; set; }
            public string p3MeanRsrp { get; set; }
            public string p3SampleRate { get; set; }

            //背瓣信息
            public string bDiscrepancy { get; set; }//前后比
            public string bSampleRate { get; set; }
            public CellInfoItem ciItem { get; set; }
            [Description("疑似旁瓣数量")]
            public int ISideNum
            {
                get
                {
                    int tmp;
                    int.TryParse(SuspectedFlap, out tmp);
                    return tmp;
                }
            }
            [Description("主瓣最强信号强度")]
            public float fSacnMainMaxRsrp
            {
                get
                {
                    float tmp;
                    float.TryParse(mMaxRsrp, out tmp);
                    return tmp;
                }
            }
            [Description("主瓣采样点比例")]
            public float fScanMainSampleRate //主瓣占比
            {
                get
                {
                    float tmp;
                    float.TryParse(mSampleRate.Replace("%", ""), out tmp);
                    return tmp;
                }
            }
            [Description("背瓣采样点比例")]
            public float fScanBackSampleRate//背瓣占比
            {
                get
                {
                    float tmp;
                    float.TryParse(bSampleRate.Replace("%", ""), out tmp);
                    return tmp;
                }
            }
            [Description("前后比")]
            public float fScanDiscrepancy//前后比
            {
                get
                {
                    float tmp;
                    float.TryParse(bDiscrepancy.Replace("%", ""), out tmp);
                    return tmp;
                }
            }

            [Description("背瓣采样点数")]
            public float 背瓣采样点数
            {
                get
                {
                    return fScanBackSampleRate * SampNum / 100;
                }
            }
        }

        public class CellInfoItem
        {
            public CellInfoItem()
            {
                antAngleDic = new Dictionary<int, ZTAntAngleItem>();
                antVertDic = new Dictionary<int, ZTAntAngleItem>();

                model1Array = new double[180];
                model2Array = new double[180];
                modelMaxArray = new double[180];
            }

            //水平面360度模型
            public Dictionary<int, ZTAntAngleItem> antAngleDic { get; set; }

            //垂直面200级模型
            public Dictionary<int, ZTAntAngleItem> antVertDic { get; set; }

            //权值理想模型
            public double[] model1Array { get; set; }
            public double[] model2Array { get; set; }
            public double[] modelMaxArray { get; set; }

            //水平与垂直二维模型
            public Dictionary<int, ZTAntAngleItem> antTwoDic { get; set; }
            public CellInfoItem(bool isInit) : this()
            {
                if (isInit)
                {
                    antTwoDic = new Dictionary<int, ZTAntAngleItem>();
                }
            }

            //public CellInfoItem(string Rsrp, string 平滑Rsrp, string Sinr, string 过覆盖指数, string 通讯距离, string SampleNum, string strModelMaxArray, string strRSRP, string strSampleNum) : this()
            //{
            //    // Complete member initialization//后台体检专用
            //    antAngleDic = new Dictionary<int, ZTAntAngleItem>();
            //    antVertDic = new Dictionary<int, ZTAntAngleItem>();

            //    initItemInfo(Rsrp, antAngleDic, new SetValue(setRsrp));
            //    initItemInfo(平滑Rsrp, antAngleDic, new SetValue(setRsrpNew));
            //    initItemInfo(Sinr, antAngleDic, new SetValue(setSinr));
            //    initItemInfo(过覆盖指数, antAngleDic, new SetValue(setCover));
            //    initItemInfo(SampleNum, antAngleDic, new SetValue(setSampleNum));
            //    initItemInfo(通讯距离, antAngleDic, new SetValue(setSampDist));

            //    string[] strModelMaxArrays = strModelMaxArray.Split('|');
            //    if (strModelMaxArrays.Length == modelMaxArray.Length)
            //    {
            //        for (int i = 0; i < strModelMaxArrays.Length; i++)
            //            modelMaxArray[i] = Convert.ToDouble(strModelMaxArrays[i]);
            //    }

            //    initItemInfo(strRSRP, antVertDic, new SetValue(setRsrp));
            //    initItemInfo(strSampleNum, antVertDic, new SetValue(setSampleNum));
            //}

            /// <summary>
            /// 360度小区集精简数据
            /// </summary>
            public CellInfoItem(string Rsrp, string 平滑Rsrp, string Sinr, string 通讯距离, string SampleNum) : this()
            {
                // Complete member initialization//后台体检专用
                antAngleDic = new Dictionary<int, ZTAntAngleItem>();
                initItemInfo(Rsrp, antAngleDic, new SetValue(setRsrp));
                initItemInfo(平滑Rsrp, antAngleDic, new SetValue(setRsrpNew));
                initItemInfo(Sinr, antAngleDic, new SetValue(setSinr));
                initItemInfo(通讯距离, antAngleDic, new SetValue(setSampDist));
                initItemInfo(SampleNum, antAngleDic, new SetValue(setSampleNum));
            }

            private void initItemInfo(string item, Dictionary<int, ZTAntAngleItem> dic, SetValue setValueFunc)
            {
                string[] strs = item.Split('|');
                foreach (string str in strs)
                {
                    string[] tmpR = str.Split('_');
                    int key = -1;
                    if (Int32.TryParse(tmpR[0], out key))
                    {
                        if (!dic.ContainsKey(key))
                            dic[key] = new ZTAntAngleItem();
                        setValueFunc(dic[key], tmpR[1]);
                    }
                }
            }

            private delegate void SetValue(ZTAntAngleItem item, string value);

            private void setRsrp(ZTAntAngleItem item, string value)
            {
                item.IRsrp = Convert.ToInt32(value);
            }

            private void setRsrpNew(ZTAntAngleItem item, string value)
            {
                item.DRsrpNew = Convert.ToDouble(value);
            }

            private void setSinr(ZTAntAngleItem item, string value)
            {
                item.ISinr = Convert.ToInt32(value);
            }

            private void setSampDist(ZTAntAngleItem item, string value)
            {
                item.DSampDist = Convert.ToDouble(value);
            }

            private void setSampleNum(ZTAntAngleItem item, string value)
            {
                item.ISampNum = Convert.ToInt32(value);
            }

            //private void setCover(ZTAntAngleItem item, string value)
            //{
            //    item.DCover = Convert.ToDouble(value);
            //}

            /// <summary>
            /// RSRP原值
            /// </summary>
            public int[] rsrpArray
            {
                get
                {
                    int[] tmp = new int[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].IRsrp;
                        else
                            tmp[i] = -140;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// 平滑RSRP
            /// </summary>
            public double[] newRsrpArray
            {
                get
                {
                    double[] tmp = new double[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].DRsrpNew;
                        else
                            tmp[i] = -20;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// SINR原值
            /// </summary>
            public int[] sinrArray
            {
                get
                {
                    int[] tmp = new int[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].ISinr;
                        else
                            tmp[i] = -25;
                    }
                    return tmp;
                }
            }


            /// <summary>
            /// 过覆盖指数
            /// </summary>
            public double[] coverArray
            {
                get
                {
                    double[] tmp = new double[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].DCover;
                        else
                            tmp[i] = 0;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// 通信距离
            /// </summary>
            public double[] sampArray
            {
                get
                {
                    double[] tmp = new double[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].DSampDist;
                        else
                            tmp[i] = 0;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// 采样点总数
            /// </summary>
            public int[] sampNumArray
            {
                get
                {
                    int[] tmp = new int[360];
                    for (int i = 0; i < 360; i++)
                    {
                        if (antAngleDic.ContainsKey(i))
                            tmp[i] = antAngleDic[i].ISampNum;
                        else
                            tmp[i] = 0;
                    }
                    return tmp;
                }
            }

            /// <summary>
            /// 计算Gmax及半功率角
            /// </summary>
            public double[] calcCellPara()
            {
                double[] resultArray = new double[3];
                double dMaxValue = -999;
                double d3dBValue = 0;
                double d6dBValue = 0;
                foreach (double dTmp in modelMaxArray)
                {
                    dMaxValue = dMaxValue >= dTmp ? dMaxValue : dTmp;
                }
                foreach (double dTmp in modelMaxArray)
                {
                    if (dTmp > dMaxValue - 3)
                    {
                        d3dBValue++;
                    }
                    if (dTmp > dMaxValue - 6)
                    {
                        d6dBValue++;
                    }
                }
                resultArray[0] = dMaxValue;
                resultArray[1] = d3dBValue;
                resultArray[2] = d6dBValue;
                return resultArray;
            }
        }

        public class AntInfto
        {
            public List<double> newRsrpList { get; set; }
            /// <summary>
            /// 电平最大值
            /// </summary>
            public double MaxRsrp
            {
                get
                {
                    double dRsrp = -20;
                    foreach (double tmpRsrp in newRsrpList)
                    {
                        dRsrp = tmpRsrp > dRsrp ? tmpRsrp : dRsrp;
                    }
                    return Math.Round(dRsrp, 2);
                }
            }
            /// <summary>
            /// 电平均值
            /// </summary>
            public double MeanRsrp
            {
                get
                {
                    double avgRsrp = 0;
                    int iNum = 0;
                    foreach (double tmpRsrp in newRsrpList)
                    {
                        if (tmpRsrp > -20)
                        {
                            avgRsrp += tmpRsrp;
                            iNum++;
                        }
                    }
                    if (iNum > 0)
                    {
                        avgRsrp = avgRsrp / iNum;
                        return Math.Round(avgRsrp, 2);
                    }
                    else
                    {
                        return 0;
                    }
                }
            }
            private double dSampleRate
            {
                get
                {
                    if (sampNum != 0)
                    {
                        int iNum = 0;
                        foreach (double rsrp in newRsrpList)
                        {
                            if (rsrp > -20)
                            {
                                iNum++;
                            }
                        }
                        return (iNum * 1.0) / sampNum;
                    }
                    else
                    {
                        return 0;
                    }
                }
            }
            /// <summary>
            /// 采样占比
            /// </summary>
            public string SampleRate
            {
                get
                {
                    return string.Format("{0}%", Math.Round(100 * dSampleRate, 2));
                }
            }
            /// <summary>
            /// A值
            /// </summary>
            public double Avalue
            {
                get
                {
                    if (sampNum != 0)
                    {
                        return MaxRsrp * dSampleRate;
                    }
                    else
                    {
                        return 0;
                    }
                }
            }
            /// <summary>
            /// 最强电平角度
            /// </summary>
            public int MaxRsrpDir
            {
                get
                {
                    double dRsrp = -20;
                    int iMaxId = 0;
                    int idx = 0;
                    foreach (double tmpRsrp in newRsrpList)
                    {
                        idx++;
                        if (tmpRsrp > dRsrp)
                        {
                            dRsrp = tmpRsrp;
                            iMaxId = idx;
                        }
                    }

                    int iMaxRsrpDir = ((iMinDir + iMaxId) + 360) % 360;
                    return iMaxRsrpDir;
                }
            }

            public int iMinDir { get; set; }//辐射叶左边角度
            public int iMaxDir { get; set; }//辐射叶右边角度
            public int sampNum { get; set; }//采样点总数
            public double diffRsrp { get; set; }//前后比(仅背瓣分析用到)
            public int diffDir { get; set; }//方向角度差值

            public AntInfto()
            {
                newRsrpList = new List<double>();
                iMinDir = 0;
                iMaxDir = 0;

                diffRsrp = 0;
                sampNum = 0;
                diffDir = 0;
            }
        }

        public class CellAngleDataExt
        {
            public float rsrpSum { get; set; }
            public float rsrpNum { get; set; }
            public float rsrpMax { get; set; }//该区间最大RXLEV	
            public float sinrSum { get; set; }
            public float sinrNum { get; set; }
            public float sinrNum_3 { get; set; }
            public float sinrNum_5 { get; set; }
            public double fSampleDistSum { get; set; }//采样点距离之和

            public float Sinr5_3
            {
                get
                {
                    try
                    {
                        if ((sinrNum_3 + sinrNum) == 0)
                        {
                            return 0;
                        }
                        else
                        {
                            return (float)(Math.Round(sinrNum_3 * 100.0 / sinrNum, 2));
                        }
                    }
                    catch
                    {
                        return 0;
                    }
                }
            }

            public float Sinr5_5
            {
                get
                {
                    try
                    {
                        if ((sinrNum_5 + sinrNum) == 0)
                        {
                            return 0;
                        }
                        else
                        {
                            return (float)(Math.Round(sinrNum_5 * 100.0 / sinrNum, 2));
                        }
                    }
                    catch
                    {
                        return 0;
                    }
                }
            }

            public CellAngleDataExt()
            {
                rsrpSum = 0;
                rsrpNum = 0;
                rsrpMax = -140;
                sinrSum = 0;
                sinrNum = 0;
                sinrNum_3 = 0;
                sinrNum_5 = 0;
            }

            /// <summary>
            /// 计算RSRP
            /// </summary>
            public void CalcRsrp(int iRsrp)
            {
                this.rsrpSum += iRsrp;
                this.rsrpNum++;
            }

            /// <summary>
            /// 计算SINR
            /// </summary>
            public void CalcSinr(int iSinr)
            {
                this.sinrSum += iSinr;
                this.sinrNum++;
                if (iSinr <= -3)
                    this.sinrNum_3 += 1;
                if (iSinr <= -5)
                    this.sinrNum_5 += 1;
            }
        }

        public class AngleData
        {
            //采样点
            public float rsrpSum { get; set; }//该区间平均RXLEV(dBm)
            public int rsrpNum { get; set; }//该区间采样点数
            public float rsrpMax { get; set; }//该区间最大RXLEV	

            public float sinrSum { get; set; }
            public int sinrNum { get; set; }
            public int sinrNum_3 { get; set; }
            public int sinrNum_5 { get; set; }
            public double fSampleDistSum { get; set; }//采样点距离之和
            public List<int> top5RsrpList { get; set; }

            public AngleData()
            {
                top5RsrpList = new List<int>();
            }

            public float top5RsrpAvg
            {
                get
                {
                    float rxlevAvg = 0;
                    foreach (int iRxlev in top5RsrpList)
                    {
                        rxlevAvg += iRxlev;
                    }
                    return rxlevAvg / top5RsrpList.Count;
                }
            }

            public float FSinr
            {
                get
                {
                    try
                    {
                        if ((sinrNum_3 + sinrNum) == 0)
                        {
                            return 0;
                        }
                        else
                        {
                            return (float)Math.Round(sinrNum_3 * 100.0 / sinrNum, 2);
                        }
                    }
                    catch
                    {
                        return 0;
                    }
                }
            }

            public float FSinrValue
            {
                get
                {
                    try
                    {
                        if ((sinrNum_5 + sinrNum) == 0)
                        {
                            return 0;
                        }
                        else
                        {
                            return (float)Math.Round(sinrNum_5 * 100.0 / sinrNum, 2);
                        }
                    }
                    catch
                    {
                        return 0;
                    }
                }
            }

            /// <summary>
            /// 计算RSRP
            /// </summary>
            public void CalcRsrp(int iRsrp)
            {
                this.rsrpSum += iRsrp;
                this.rsrpNum++;
            }

            /// <summary>
            /// 计算SINR
            /// </summary>
            public void CalcSinr(int iSinr)
            {
                this.sinrSum += iSinr;
                this.sinrNum++;
                if (iSinr < -3)
                    this.sinrNum_3 += 1;
                if (iSinr < -5)
                    this.sinrNum_5 += 1;
            }
        }
    }

    public class DiyQueryCellAngleResult : DIYSQLBase
    {
        readonly Dictionary<int, int> cellEciDic;
        public DiyQueryCellAngleResult(MainModel mm, Dictionary<int, int> cellEciDic)
            : base(mm)
        {
            this.mainModel = mm;
            this.cellEciDic = cellEciDic;
            cellInfoDic = new Dictionary<int, ZTLteScanAntenna.CellInfoItem>();
        }
        int istime = 0;
        int ietime = 0;
        int iEci = 0;
        int iNewEci = 0;

        public override string Name
        {
            get { return "查询LTE扫频角度信息"; }
        }
        protected override string getSqlTextString()
        {
            string sql = @"select [strcellname],[iECI],[strRSRP],[strpRSRP],[strSINR],[通讯距离],[SampleNum]
                           from tb_probchk_cell_angle_result where istime = " + istime + " and ietime = " + ietime;
            if (this.iEci != 0)
            {
                sql = @"select [strcellname],[iECI],[strRSRP],[strpRSRP],[strSINR],[通讯距离],[SampleNum]
                        from tb_probchk_cell_angle_result where istime = " + istime + " and ietime = " + ietime +
                       " and (iECI = " + this.iEci + " or iECI = " + this.iNewEci + ")";
            }
            return sql;
        }

        public void SetCondition(int istime, int ietime)
        {
            this.istime = istime;
            this.ietime = ietime;
            this.iEci = 0;
        }

        public void SetCondition(int istime, int ietime, int iEci)
        {
            this.istime = istime;
            this.ietime = ietime;
            this.iEci = iEci;
            this.iNewEci = (iEci / 256) * 256 + ((iEci % 256) % 10);
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[7];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_String;
            return rType;
        }
        public Dictionary<int, ZTLteScanAntenna.CellInfoItem> cellInfoDic { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            cellInfoDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    package.Content.GetParamString();
                    int Eci = package.Content.GetParamInt();
                    int iCellEci = (Eci / 256) * 256 + ((Eci % 256) % 10);
                    if (cellEciDic.Count != 0 && !cellEciDic.ContainsKey(iCellEci))
                        continue;

                    string Rsrp = package.Content.GetParamString();
                    string 平滑Rsrp = package.Content.GetParamString();
                    string Sinr = package.Content.GetParamString();
                    string 通讯距离 = package.Content.GetParamString();
                    string SampleNum = package.Content.GetParamString();
                    ZTLteScanAntenna.CellInfoItem cellInfoItem = new ZTLteScanAntenna.CellInfoItem(Rsrp, 平滑Rsrp, Sinr, 通讯距离, SampleNum);
                    if (!cellInfoDic.ContainsKey(iCellEci))
                        cellInfoDic[iCellEci] = new ZTLteScanAntenna.CellInfoItem(false);
                    cellInfoDic[iCellEci] = cellInfoItem;
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
}
