﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.Func.EventBlock
{
    public partial class EventBlockCompetitionConditionDlg : BaseForm
    {
        public EventBlockCompetitionConditionDlg(MainModel mm, QueryCondition conditionOfHost, QueryCondition conditionOfGuest, EventBlockCondition blockCondition)
            : base(mm)
        {
            InitializeComponent();

            CategoryEnumItem[] projItems = ((CategoryEnum)CategoryManager.GetInstance()["Project"]).Items;
            CategoryEnumItem[] svcItems = ((CategoryEnum)CategoryManager.GetInstance()["ServiceType"]).Items;
            if (projItems == null || svcItems == null)
            {
                return;
            }
            MapFormItemSelection itemSelection = mm.MainForm.GetMapForm().ItemSelection;
            if (conditionOfHost == null)
            {
                fillCategoryView(lvProjectHost, null, projItems);
            }
            else
            {
                radioCarrierIDHost.SelectedIndex = conditionOfHost.CarrierTypes[0] - 1;
                fillCategoryView(lvProjectHost, conditionOfHost.Projects, projItems);
                fillCategoryView(lvServiceHost, conditionOfHost.ServiceTypes, svcItems);
                fillEventView(lvEventHost, conditionOfHost.EventIDs);
            }

            if (conditionOfGuest == null)
            {
                fillCategoryView(lvProjectGuest, null, projItems);
            }
            else
            {
                radioCarrierIDGuest.SelectedIndex = conditionOfGuest.CarrierTypes[0] - 1;
                fillCategoryView(lvProjectGuest, conditionOfGuest.Projects, projItems);
                fillCategoryView(lvServiceGuest, conditionOfGuest.ServiceTypes, svcItems);
                fillEventView(lvEventGuest, conditionOfGuest.EventIDs);
            }
            lblProjCntHost.Text = "[" + lvProjectHost.Items.Count.ToString() + "]";
            lblProjCntGuest.Text = "[" + lvProjectGuest.Items.Count.ToString() + "]";
            lblSvcCnt.Text = "[" + lvServiceHost.Items.Count.ToString() + "]";
            lblSvcCntGuest.Text = "[" + lvServiceGuest.Items.Count.ToString() + "]";
            ItemSelectionPanel projPanelOne = new ItemSelectionPanel(dropDownHost, lvProjectHost, lblProjCntHost, itemSelection, "Project", true);
            ItemSelectionPanel projPanelTwo = new ItemSelectionPanel(dropDownGuest, lvProjectGuest, lblProjCntGuest, itemSelection, "Project", true);
            projPanelOne.FreshItems();
            projPanelTwo.FreshItems();
            dropDownHost.Items.Add(new ToolStripControlHost(projPanelOne));
            dropDownGuest.Items.Add(new ToolStripControlHost(projPanelTwo));
            if (blockCondition != null)
            {
                numRadius.Value = (decimal)blockCondition.BlockRadius;
                numMinEventCnt.Value = (decimal)blockCondition.MinEventCount;
            }

            ItemSelectionPanel servPanelHost = new ItemSelectionPanel(dropDownSvcHost, lvServiceHost, lblSvcCnt, itemSelection, "ServiceType", true);
            ItemSelectionPanel servPanelGuest = new ItemSelectionPanel(dropDownSvcGuest, lvServiceGuest, lblSvcCntGuest, itemSelection, "ServiceType", true);
            servPanelHost.FreshItems();
            servPanelGuest.FreshItems();
            dropDownSvcHost.Items.Add(new ToolStripControlHost(servPanelHost));
            dropDownSvcGuest.Items.Add(new ToolStripControlHost(servPanelGuest));
        }

        private void fillCategoryView(ListView lv,List<int> projIDs, CategoryEnumItem[] projItems)
        {
            lv.Items.Clear();
            if (projIDs == null)
            {
                foreach (CategoryEnumItem item in projItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.Name;
                    lvi.Tag = item.ID;
                    lv.Items.Add(lvi);
                }
            }
            else
            {
                foreach (int id in projIDs)
                {
                    foreach (CategoryEnumItem item in projItems)
                    {
                        if (id == item.ID)
                        {
                            ListViewItem lvi = new ListViewItem();
                            lvi.Text = item.Name;
                            lvi.Tag = id;
                            lv.Items.Add(lvi);
                        }
                    }
                }
            }
        }
       
        private void fillEventView(ListView lv, List<int> eventIDs)
        {
            lv.Items.Clear();
            foreach (int id in eventIDs)
            {
                if (id==-1)
                {
                    ListViewItem item = new ListViewItem("所有事件");
                    item.Tag = id;
                    lv.Items.Add(item);
                    break;
                }
                EventInfo ei = EventInfoManager.GetInstance()[id];
                if (ei != null)
                {
                    ListViewItem item = new ListViewItem(ei.Name);
                    item.Tag = ei.ID;
                    lv.Items.Add(item);
                }
                else
                {
                    ListViewItem item = new ListViewItem("事件ID：" + id);
                    item.Tag = id;
                    lv.Items.Add(item);
                }
            }
        }

        private void btnProjSelHost_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(btnProjSelHost.Width, btnProjSelHost.Height);
            dropDownHost.Show(btnProjSelHost, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void btnProjSelGuest_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(btnProjSelGuest.Width, btnProjSelGuest.Height);
            dropDownGuest.Show(btnProjSelGuest, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void btnSelEvtHost_Click(object sender, EventArgs e)
        {
            EventChooserForm eventChooser = EventChooserForm.GetInstance(MainModel);
            if (eventChooser.ShowDialog() == DialogResult.OK)
            {
                fillEventView(lvEventHost, eventChooser.SelectedEventIDs);
            }
        }

        private void btnSelEvtGuest_Click(object sender, EventArgs e)
        {
            EventChooserForm eventChooser = EventChooserForm.GetInstance(MainModel);
            if (eventChooser.ShowDialog() == DialogResult.OK)
            {
                fillEventView(lvEventGuest, eventChooser.SelectedEventIDs);
            }
        }

        private bool checkDateTime(DateTime begin,DateTime end)
        {
            return begin <= end;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (!checkDateTime(dtBeginHost.Value, dtEndHost.Value))
            {
                MessageBox.Show("主队开始时间不能大于结束时间！请正确设置时间段！");
                return;
            }
            if (!checkDateTime(dtBeginGuest.Value, dtEndGuest.Value))
            {
                MessageBox.Show("客队开始时间不能大于结束时间！请正确设置时间段！");
                return;
            }
            if (lvProjectHost.Items.Count == 0)
            {
                MessageBox.Show("请选择主队的数据来源！");
                return;
            }
            if (lvProjectGuest.Items.Count == 0)
            {
                MessageBox.Show("请选择客队的数据来源！");
                return;
            }
            if (lvServiceHost.Items.Count == 0)
            {
                MessageBox.Show("请选择主队的服务类型！");
                return;
            }
            if (lvServiceGuest.Items.Count == 0)
            {
                MessageBox.Show("请选择客队的服务类型！");
                return;
            }
            if (lvEventHost.Items.Count == 0)
            {
                MessageBox.Show("请选择主队的汇聚事件！");
                return;
            }
            if (lvEventGuest.Items.Count == 0)
            {
                MessageBox.Show("请选择客队的汇聚事件！");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        public void GetCondition(out EventBlockCondition blockCondition,out QueryCondition conditionOfHost,out QueryCondition conditionOfGuest)
        {
            blockCondition = new EventBlockCondition((double)numRadius.Value, (int)numMinEventCnt.Value);

            conditionOfHost = new QueryCondition();
            List<int> carrierIDs = new List<int>();
            carrierIDs.Add(radioCarrierIDHost.SelectedIndex + 1);
            conditionOfHost.CarrierTypes = carrierIDs;
            DateTime bTime=dtBeginHost.Value.Date;
            DateTime eTime=dtEndHost.Value.Date.AddDays(1).AddMilliseconds(-1);
            List<TimePeriod> periods = new List<TimePeriod>();
            periods.Add(new TimePeriod(bTime, eTime));
            conditionOfHost.Periods = periods;
            conditionOfHost.Projects = getSelIDs(lvProjectHost);
            conditionOfHost.ServiceTypes = getSelIDs(lvServiceHost);
            conditionOfHost.EventIDs = getSelIDs(lvEventHost);
            conditionOfHost.FilterOffValue9 = true;
            conditionOfHost.IsAllAgent = true;
            if (chkHostFileName.Checked && tbxHostFileName.Text.Trim() != fileFilterTip)
            {
                conditionOfHost.NameFilterType = 0;
                int cnt = 0;
                conditionOfHost.FileName = QueryCondition.MakeFileFilterString(tbxHostFileName.Text, ref cnt);
                conditionOfHost.FileNameOrNum = cnt;
            }

            conditionOfGuest = new QueryCondition();
            carrierIDs = new List<int>();
            carrierIDs.Add(radioCarrierIDGuest.SelectedIndex + 1);
            conditionOfGuest.CarrierTypes = carrierIDs;
            bTime = dtBeginGuest.Value.Date;
            eTime = dtEndGuest.Value.Date.AddDays(1).AddMilliseconds(-1);
            periods = new List<TimePeriod>();
            periods.Add(new TimePeriod(bTime, eTime));
            conditionOfGuest.IsAllAgent = true;
            conditionOfGuest.Periods = periods;
            conditionOfGuest.Projects = getSelIDs(lvProjectGuest);
            conditionOfGuest.ServiceTypes = getSelIDs(lvServiceGuest);
            conditionOfGuest.EventIDs = getSelIDs(lvEventGuest);
            conditionOfGuest.FilterOffValue9 = true;
            if (chkGuestFileName.Checked && tbxGuestFileName.Text.Trim() != fileFilterTip)
            {
                conditionOfGuest.NameFilterType = 0;
                int cnt = 0;
                conditionOfGuest.FileName = QueryCondition.MakeFileFilterString(tbxGuestFileName.Text, ref cnt);
                conditionOfGuest.FileNameOrNum = cnt;
            }
        }

        private List<int> getSelIDs(ListView lv)
        {
            List<int> idList = new List<int>();
            foreach (ListViewItem item in lv.Items)
            {
                int id = (int)item.Tag;
                if (id==-1)
                {
                    idList.Clear();
                    idList.Add(-1);//全选
                    break; 
                }
                else
                {
                    idList.Add((int)item.Tag);
                }
            }
            return idList;
        }

        private void btnSelSvcHost_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(btnSelSvcHost.Width, btnSelSvcHost.Height);
            dropDownSvcHost.Show(btnSelSvcHost, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void btnSelSvcGuest_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(btnSelSvcGuest.Width, btnSelSvcGuest.Height);
            dropDownSvcGuest.Show(btnSelSvcGuest, pt, ToolStripDropDownDirection.BelowLeft);
        }

        readonly string fileFilterTip = "支持按文件名模糊匹配文件，多个文件名时请用“ or ”隔开（不带双引号）";
        private void chkHostFileName_CheckedChanged(object sender, EventArgs e)
        {
            tbxHostFileName.Enabled = chkHostFileName.Checked;
            if (chkHostFileName.Checked)
            {
                if (tbxHostFileName.Text.Trim().Equals(fileFilterTip))
                {
                    tbxHostFileName.Clear();
                }
            }
            else
            {
                if (tbxHostFileName.Text.Trim().Length == 0)
                {
                    tbxHostFileName.Text = fileFilterTip;
                }
            }
        }

        private void chkGuestFileName_CheckedChanged(object sender, EventArgs e)
        {
            tbxGuestFileName.Enabled = chkGuestFileName.Checked;
            if (chkGuestFileName.Checked)
            {
                if (tbxGuestFileName.Text.Trim().Equals(fileFilterTip))
                {
                    tbxGuestFileName.Clear();
                }
            }
            else
            {
                if (tbxGuestFileName.Text.Trim().Length == 0)
                {
                    tbxGuestFileName.Text = fileFilterTip;
                }
            }
        }
    }
}
