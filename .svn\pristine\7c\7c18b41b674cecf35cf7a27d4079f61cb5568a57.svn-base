﻿using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;

namespace MasterCom.RAMS.ZTFunc
{
    public class SmallBtsAcceptInfo : InDoorBtsAcceptInfo
    {
        public SmallBtsAcceptInfo(LTEBTS lteBts)
            : base(lteBts)
        {
        }
    }
    public class SmallCellAcceptInfo : InDoorCellAcceptInfo
    {
        public SmallCellAcceptInfo(LTECell cell)
            : base(cell)
        {
        }
        #region 极好点下载
        public string IsFtpGoodDlAccordDes { get { return IsFtpDlAccord ? "是" : "否"; } }
        public bool IsFtpGoodDlAccord { get; set; }
        public AvgKpiInfo FtpGoodDlSpeedInfo { get; set; } = new AvgKpiInfo();//下行吞吐率
        #endregion

        #region 极好点上传
        public string IsFtpGoodUlAccordDes { get { return IsFtpUlAccord ? "是" : "否"; } }
        public bool IsFtpGoodUlAccord { get; set; }
        public double FtpGoodUlSpeedMax { get; set; } = double.MinValue;
        public AvgKpiInfo FtpGoodUlSpeedInfo { get; set; } = new AvgKpiInfo();//上行吞吐率
        public RateKpiInfo MoVolteInfo { get; } = new RateKpiInfo("主叫VOLTE语音");
        public RateKpiInfo MtVolteInfo { get; } = new RateKpiInfo("被叫VOLTE语音");
        #endregion

        public AvgKpiInfo Rsrp_FaceToPassageway { get; set; } = new AvgKpiInfo();//走廊面向天线
        public AvgKpiInfo Rsrp_BackFaceToPassageway { get; set; } = new AvgKpiInfo();//走廊背向天线
        public Dictionary<string, LevelCqtCoverInfo> LevelCqtCoverInfoDic { get; set; } = new Dictionary<string, LevelCqtCoverInfo>();
        public override void AddAcceptKpiInfo(string fileName, Dictionary<uint, object> kpiDic)
        {
            object floorObj = getKpiFloor(kpiDic);
            FileTestTypeE fileType = getFileTypeByFileName(fileName);

            LevelTestKpiInfo kpiInfo = null;
            statsLevelTestKpi(fileName, fileType, floorObj, kpiDic, ref kpiInfo);
            statsLevelCoverInfo(fileName, fileType, floorObj, kpiInfo);
            statsCapabilityTestKpi(fileType, kpiDic, kpiInfo);
        }

        //统计平层测试指标
        protected void statsLevelTestKpi(string fileName, FileTestTypeE fileType, object floorObj
            , Dictionary<uint, object> kpiDic, ref LevelTestKpiInfo kpiInfo)
        {
            if ((!fileName.Contains("上传") && !fileName.Contains("下载") && !fileName.Contains("切换"))
                || fileType == FileTestTypeE.undefined)
            {
                return;
            }
            string fileNameKey;
            if (floorObj == null)
            {
                fileNameKey = fileType.ToString();
            }
            else
            {
                string strUnit = Regex.Match(fileName, @"-?\w+单元", RegexOptions.None).Value;
                fileNameKey = string.Format("{0}F{1}{2}", floorObj.ToString(), strUnit, fileType.ToString());
            }

            if (hasGotKpiKeyList.Contains(fileNameKey))
            {
                return;
            }
            hasGotKpiKeyList.Add(fileNameKey);

            kpiInfo = new LevelTestKpiInfo(fileName);
            kpiInfo.FillKpiInfo(kpiDic);
            this.LevelTestInfoList.Add(kpiInfo);
        }

        //统计平层定点覆盖指标
        protected void statsLevelCoverInfo(string fileName, FileTestTypeE fileType, object floorObj
            , LevelTestKpiInfo levelFileInfo)
        {
            if (floorObj == null || levelFileInfo == null)
            {
                return;
            }
            int floorIndex = (int)floorObj;
            string antennaPosition = Regex.Match(fileName, @"-?\d+#楼顶", RegexOptions.None).Value;

            string token = string.Format("{0}-{1}", antennaPosition, floorIndex);
            LevelCqtCoverInfo levelCqtCoverInfo;
            if (!LevelCqtCoverInfoDic.TryGetValue(token, out levelCqtCoverInfo))
            {
                levelCqtCoverInfo = new LevelCqtCoverInfo(antennaPosition, floorIndex);
                LevelCqtCoverInfoDic.Add(token, levelCqtCoverInfo);
            }
            levelCqtCoverInfo.AddLevelKpi(fileType, levelFileInfo);
        }

        //统计性能测试指标
        protected void statsCapabilityTestKpi(FileTestTypeE fileType, Dictionary<uint, object> kpiDic
            , LevelTestKpiInfo levelInfo)
        {
            //从平层测试指标中汇总各小区的遍历性测试性能指标
            if (levelInfo != null)
            {
                if (fileType == FileTestTypeE.切换)
                {
                    HandOverInfo.AddInfo(levelInfo.HandoverSucceedCnt, levelInfo.HandoverRequestCnt);
                }
                else if (fileType == FileTestTypeE.极好点下载)
                {
                    FtpGoodDlSpeedInfo.AddKpi(levelInfo.PointCount_DlSpeed, levelInfo.AvgDLSpeed);
                }
                else if (fileType == FileTestTypeE.极好点上传)
                {
                    FtpGoodUlSpeedMax = Math.Max(levelInfo.MaxULSpeed, FtpGoodUlSpeedMax);
                    FtpGoodUlSpeedInfo.AddKpi(levelInfo.PointCount_UlSpeed, levelInfo.AvgULSpeed);
                }
                else if (fileType == FileTestTypeE.上传_室内中间)
                {
                    statsFtpUlInfo(levelInfo);
                }
                else if (fileType == FileTestTypeE.下载_室内中间)
                {
                    statsFtpDlInfo(levelInfo);
                }
                else if (fileType == FileTestTypeE.下载_走廊面向)
                {
                    Rsrp_FaceToPassageway.AddKpi(levelInfo.PointCount_Rsrp, levelInfo.AvgRsrp);
                }
                else if (fileType == FileTestTypeE.下载_走廊背向)
                {
                    Rsrp_BackFaceToPassageway.AddKpi(levelInfo.PointCount_Rsrp, levelInfo.AvgRsrp);
                }
            }
            else if (fileType == FileTestTypeE.附着 || fileType == FileTestTypeE.语音
                || fileType == FileTestTypeE.锁频外泄 || fileType == FileTestTypeE.扫频外泄
                || fileType == FileTestTypeE.VOLTE)
            {//其他未匹配到的指标，只取最新一个文件的指标

                statsKpiNewestValues(kpiDic);
            }
        }

        protected override void statsKpiNewestValue(KpiKey kpiKey, object objValue)
        {
            base.statsKpiNewestValue(kpiKey, objValue);
            switch (kpiKey)
            {
                case KpiKey.VolteAudioCallRequestCnt:
                    this.MoVolteInfo.TotalCount = (int)objValue;
                    break;
                case KpiKey.VolteAudioCallSucceedCnt:
                    this.MoVolteInfo.ValidCount = (int)objValue;
                    break;
                case KpiKey.VolteVideoCallRequestCnt:
                    this.MtVolteInfo.TotalCount = (int)objValue;
                    break;
                case KpiKey.VolteVideoCallSucceedCnt:
                    this.MtVolteInfo.ValidCount = (int)objValue;
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 检验小区指标是否合格,获取完小区各指标信息后调用
        /// </summary>
        /// <returns></returns>
        public override void CheckCellIsAccordAccept()
        {
            this.RrcInfo.CheckIsAccord(25, 100);
            this.ErabInfo.CheckIsAccord(25, 100);
            this.AccessInfo.CheckIsAccord(25, 100);
            this.CsfbInfo.CheckIsAccord(10, 100);
            this.HandOverInfo.CheckIsAccord(5, 98);
            this.DlCoverRate.CheckIsAccord(1, 95);

            if (this.RoadTypeDes == "单路")
            {
                this.IsFtpDlAccord = FtpDlSpeedInfo.KpiAvgValue >= 35;
            }
            else if (this.RoadTypeDes == "双路")
            {
                this.IsFtpDlAccord = FtpDlSpeedInfo.KpiAvgValue >= 45;
            }
            else if (this.RoadTypeDes == "移频双路")
            {
                this.IsFtpDlAccord = FtpDlSpeedInfo.KpiAvgValue >= 40;
            }
            this.IsFtpUlAccord = FtpUlSpeedInfo.KpiAvgValue >= 6 && FtpUlSpeedMax >= 9;
            this.IsLeakoutRateAccord = (LeakoutRate_LockEarfcn != null && LeakoutRate_LockEarfcn >= 95)
                || (LeakoutRate_Scan != null && LeakoutRate_Scan > 95);

            bool isAllKpiAccord = true;
            StringBuilder strbNotAccordKpiName = new StringBuilder();
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.RrcInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.ErabInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.AccessInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.CsfbInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.HandOverInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.DlCoverRate, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(IsFtpDlAccord, "下行吞吐率", ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(IsFtpUlAccord, "上行吞吐率", ref isAllKpiAccord));

            this.isAccord = isAllKpiAccord;
            this.NotAccordKpiDes = strbNotAccordKpiName.ToString();
        }

        protected FileTestTypeE getFileTypeByFileName(string fileName)
        {
            if (fileName.Contains("极好点下载"))
            {
                return FileTestTypeE.极好点下载;
            }
            else if (fileName.Contains("极好点上传"))
            {
                return FileTestTypeE.极好点上传;
            }
            else if (fileName.Contains("附着"))
            {
                return FileTestTypeE.附着;
            }
            else if (fileName.Contains("切换"))
            {
                return FileTestTypeE.切换;
            }
            else if (fileName.Contains("语音"))
            {
                return FileTestTypeE.语音;
            }
            else if (fileName.Contains("锁频外泄"))
            {
                return FileTestTypeE.锁频外泄;
            }
            else if (fileName.Contains("扫频外泄"))
            {
                return FileTestTypeE.扫频外泄;
            }
            else if (fileName.Contains("下载"))
            {
                return downloadType(fileName);
            }
            else if (fileName.Contains("上传"))
            {
                return uploadType(fileName);
            }
            else if (fileName.Contains("VOLTE"))
            {
                return FileTestTypeE.VOLTE;
            }
            return FileTestTypeE.undefined;
        }

        private FileTestTypeE downloadType(string fileName)
        {
            if (fileName.Contains("室内面向"))
            {
                return FileTestTypeE.下载_室内面向;
            }
            else if (fileName.Contains("室内中间"))
            {
                return FileTestTypeE.下载_室内中间;
            }
            else if (fileName.Contains("室内背向"))
            {
                return FileTestTypeE.下载_室内背向;
            }
            else if (fileName.Contains("走廊面向"))
            {
                return FileTestTypeE.下载_走廊面向;
            }
            else if (fileName.Contains("走廊背向"))
            {
                return FileTestTypeE.下载_走廊背向;
            }
            return FileTestTypeE.undefined;
        }

        private FileTestTypeE uploadType(string fileName)
        {
            if (fileName.Contains("室内面向"))
            {
                return FileTestTypeE.上传_室内面向;
            }
            else if (fileName.Contains("室内中间"))
            {
                return FileTestTypeE.上传_室内中间;
            }
            else if (fileName.Contains("室内背向"))
            {
                return FileTestTypeE.上传_室内背向;
            }
            else if (fileName.Contains("走廊面向"))
            {
                return FileTestTypeE.上传_走廊面向;
            }
            else if (fileName.Contains("走廊背向"))
            {
                return FileTestTypeE.上传_走廊背向;
            }
            return FileTestTypeE.undefined;
        }
    }
    public enum FileTestTypeE
    {
        下载_室内面向,
        下载_室内中间,
        下载_室内背向,
        下载_走廊面向,
        下载_走廊背向,

        上传_室内面向,
        上传_室内中间,
        上传_室内背向,
        上传_走廊面向,
        上传_走廊背向,

        极好点下载,
        极好点上传,
        附着,
        切换,
        语音,
        锁频外泄,
        扫频外泄,
        VOLTE,

        undefined = 100
    }
    public class LevelCqtCoverInfo
    {
        public LevelCqtCoverInfo(string antennaPosition, int coverFloor)
        {
            this.AntennaPosition = antennaPosition;
            this.CoverFloor = coverFloor + "F";
        }
        public string AntennaPosition { get; set; }
        public string CoverFloor { get; set; }

        public AvgKpiInfo IndoorMid_RsrpInfo { get; set; } = new AvgKpiInfo();
        public AvgKpiInfo IndoorMid_SinrInfo { get; set; } = new AvgKpiInfo();

        public AvgKpiInfo IndoorFaceTo_RsrpInfo { get; set; } = new AvgKpiInfo();
        public AvgKpiInfo IndoorFaceTo_SinrInfo { get; set; } = new AvgKpiInfo();

        public AvgKpiInfo IndoorBackFaceTo_RsrpInfo { get; set; } = new AvgKpiInfo();
        public AvgKpiInfo IndoorBackFaceTo_SinrInfo { get; set; } = new AvgKpiInfo();

        public AvgKpiInfo PassagewayFaceTo_RsrpInfo { get; set; } = new AvgKpiInfo();
        public AvgKpiInfo PassagewayBackFaceTo_RsrpInfo { get; set; } = new AvgKpiInfo();

        public void AddLevelKpi(FileTestTypeE fileType, LevelTestKpiInfo levelFileInfo)
        {
            if (fileType == FileTestTypeE.上传_室内中间 || fileType == FileTestTypeE.下载_室内中间)
            {
                this.IndoorMid_RsrpInfo.AddKpi(levelFileInfo.PointCount_Rsrp, levelFileInfo.AvgRsrp);
                this.IndoorMid_SinrInfo.AddKpi(levelFileInfo.PointCount_Sinr, levelFileInfo.AvgSinr);
            }
            else if (fileType == FileTestTypeE.上传_室内面向 || fileType == FileTestTypeE.下载_室内面向)
            {
                this.IndoorFaceTo_RsrpInfo.AddKpi(levelFileInfo.PointCount_Rsrp, levelFileInfo.AvgRsrp);
                this.IndoorFaceTo_SinrInfo.AddKpi(levelFileInfo.PointCount_Sinr, levelFileInfo.AvgSinr);
            }
            else if (fileType == FileTestTypeE.上传_室内背向 || fileType == FileTestTypeE.下载_室内背向)
            {
                this.IndoorBackFaceTo_RsrpInfo.AddKpi(levelFileInfo.PointCount_Rsrp, levelFileInfo.AvgRsrp);
                this.IndoorBackFaceTo_SinrInfo.AddKpi(levelFileInfo.PointCount_Sinr, levelFileInfo.AvgSinr);
            }
            else if (fileType == FileTestTypeE.上传_走廊面向 || fileType == FileTestTypeE.下载_走廊面向)
            {
                this.PassagewayFaceTo_RsrpInfo.AddKpi(levelFileInfo.PointCount_Rsrp, levelFileInfo.AvgRsrp);
            }
            else if (fileType == FileTestTypeE.上传_走廊背向 || fileType == FileTestTypeE.下载_走廊背向)
            {
                this.PassagewayBackFaceTo_RsrpInfo.AddKpi(levelFileInfo.PointCount_Rsrp, levelFileInfo.AvgRsrp);
            }
        }
    }
}
