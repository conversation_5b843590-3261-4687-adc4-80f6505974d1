﻿using MasterCom.RAMS.KPI_Statistics;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTNRDominantAreaAna
{
    public static class NRDominantAreaImage
    {
        public static string RsrpAvg { get { return "Nr_BA040002"; } }
        public static string RsrpCount { get { return "Nr_BA040001"; } }
        public static string SinrAvg { get { return "Nr_BA040009"; } }
        public static string SinrCount { get { return "Nr_BA040008"; } }

        //5G 网络测试覆盖率 100*Nr_BA0400D7/Nr_BA040052
        public static string Rsrp93Sinr3Count { get { return "Nr_BA0400D7"; } }
        public static string RsrpSinrCount { get { return "Nr_BA040052"; } }

        //综合覆盖率 100*Nr_BA0400D7/Nr_BA040052 * (Nr_8004001F/Nr_80040005)*100
        public static string NrDuration { get { return "Nr_8004001F"; } }
        public static string Duration { get { return "Nr_80040005"; } }

        //下载速率
        public static string MacDLThroughputAvg { get { return "Nr_BA040103"; } }
        public static string MacDLThroughputCount { get { return "Nr_BA040102"; } }

        //业务态下行速率（Mbps）:{Nr_BA04003E /1000/1000}
        public static string MacDLThroughputBSAvg { get { return "Nr_BA04003E"; } }
        public static string MacDLThroughputBSCount { get { return "Nr_BA04003D"; } }

        //应用层上行平均速率：{((Nr_BA040086 * 8) / (Nr_BA040087 / 1000)) / 1000 / 1000}
        public static string ULTransferedSize { get { return "Nr_BA040086"; } }
        public static string ULTransferedTime { get { return "Nr_BA040087"; } }
        //应用层下行平均速率：{((Nr_BA040084* 8) / (Nr_BA040085 / 1000)) / 1000 / 1000 }
        public static string DLTransferedSize { get { return "Nr_BA040084"; } }
        public static string DLTransferedTime { get { return "Nr_BA040085"; } }

        public static string[] GetImage()
        {
            return new string[] { RsrpAvg, RsrpCount, SinrAvg, SinrCount, Rsrp93Sinr3Count, RsrpSinrCount
                , NrDuration, Duration, MacDLThroughputAvg, MacDLThroughputCount, MacDLThroughputBSAvg
                , MacDLThroughputBSCount, ULTransferedSize, ULTransferedTime, DLTransferedSize
                , DLTransferedTime };
        }
    }

    public class SceneKpiInfo
    {
        public Data Rsrp { get; set; } = new Data();
        public Data Sinr { get; set; } = new Data();
        public Data MacDLThroughput { get; set; } = new Data();
        public Data MacDLThroughputBS { get; set; } = new Data();

        public Data FtpULThroughput { get; set; } = new Data();
        public Data FtpDLThroughput { get; set; } = new Data();

        public double Rsrp93Sinr3Count { get; set; } = 0;
        public double RsrpSinrCount { get; set; } = 0;
        public double NrDuration { get; set; } = 0;
        public double Duration { get; set; } = 0;

        public void AddData(StatDataHubBase data)
        {
            Rsrp.AddData(data, NRDominantAreaImage.RsrpAvg, NRDominantAreaImage.RsrpCount);
            Sinr.AddData(data, NRDominantAreaImage.SinrAvg, NRDominantAreaImage.SinrCount);
            MacDLThroughput.AddData(data, NRDominantAreaImage.MacDLThroughputAvg, NRDominantAreaImage.MacDLThroughputCount);
            MacDLThroughputBS.AddData(data, NRDominantAreaImage.MacDLThroughputBSAvg, NRDominantAreaImage.MacDLThroughputBSCount);

            FtpULThroughput.AddData(data, NRDominantAreaImage.ULTransferedSize, NRDominantAreaImage.ULTransferedTime);
            FtpDLThroughput.AddData(data, NRDominantAreaImage.DLTransferedSize, NRDominantAreaImage.DLTransferedTime);

            Rsrp93Sinr3Count += Data.GetValidImageData(data, NRDominantAreaImage.Rsrp93Sinr3Count);
            RsrpSinrCount += Data.GetValidImageData(data, NRDominantAreaImage.RsrpSinrCount);
            NrDuration += Data.GetValidImageData(data, NRDominantAreaImage.NrDuration);
            Duration += Data.GetValidImageData(data, NRDominantAreaImage.Duration);
        }

        public void AddData(SceneKpiInfo data)
        {
            Rsrp.AddData(data.Rsrp);
            Sinr.AddData(data.Sinr);
            MacDLThroughput.AddData(data.MacDLThroughput);
            MacDLThroughputBS.AddData(data.MacDLThroughputBS);

            FtpULThroughput.AddData(data.FtpULThroughput);
            FtpDLThroughput.AddData(data.FtpDLThroughput);

            Rsrp93Sinr3Count += data.Rsrp93Sinr3Count;
            RsrpSinrCount += data.RsrpSinrCount;
            NrDuration += data.NrDuration;
            Duration += data.Duration;
        }

        public void Calculate(NRDominantAreaResult res)
        {
            res.CoverRate = 100 * getValidData(Rsrp93Sinr3Count, RsrpSinrCount, 4);
            res.ComprehensiveCoverRate = Math.Round(res.CoverRate * getValidData(NrDuration, Duration, 2), 2);
            res.DLMacSpeed = Math.Round(MacDLThroughput.GetAvg() / 1000 / 1000, 2);
            res.DLMacBSSpeed = Math.Round(MacDLThroughputBS.GetAvg() / 1000 / 1000, 2);
            res.FtpULSpeed = Math.Round(FtpULThroughput.GetAvg() / 1000 / 1000, 2);
            res.FtpDLSpeed = Math.Round(FtpDLThroughput.GetAvg() / 1000 / 1000, 2);
            res.RSRP = Rsrp.GetAvg();
            res.SINR = Sinr.GetAvg();
        }

        private double getValidData(double dividend, double divisor, int digits)
        {
            double data = 0;
            if (divisor > 0)
            {
                data = Math.Round(dividend / divisor, digits);
            }
            return data;
        }

        public class Data
        {
            public double Sum { get; set; } = 0;
            public double Count { get; set; } = 0;

            public void AddData(StatDataHubBase data, string avgFormula, string countFormula)
            {
                double avg = GetValidImageData(data, avgFormula);
                double count = GetValidImageData(data, countFormula);
                Count += count;
                Sum += avg * count;
            }

            public void AddSpeed(StatDataHubBase data, string sizeFormula, string timeFormula)
            {
                double size = GetValidImageData(data, sizeFormula);
                double time = GetValidImageData(data, timeFormula);

                Sum += size * 8 / 1000 / 1000;
                Count += time / 1000;
            }

            public void AddData(Data data)
            {
                Count += data.Count;
                Sum += data.Sum;
            }

            public double GetAvg()
            {
                if (Count > 0)
                {
                    return Math.Round(Sum / Count, 2);
                }
                return 0;
            }

            public static double GetValidImageData(StatDataHubBase data, string formula)
            {
                double value = data.CalcValueByFormula(formula);
                if (double.IsNaN(value))
                {
                    return 0;
                }
                return value;
            }
        }
    }
}
