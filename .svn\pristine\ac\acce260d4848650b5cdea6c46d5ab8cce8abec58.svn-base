﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public abstract class StationAcceptBase
    {
        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public virtual void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, BtsInfoBase bts, CellInfoBase cell, StationAcceptConditionBase condition)
        {
            if (!isValidFile(fileInfo))
            {
                return;
            }

            analyzeFile(fileInfo, fileManager, bts, cell, condition);
        }

        protected virtual void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, BtsInfoBase bts, CellInfoBase cell, StationAcceptConditionBase condition)
        {
        }

        protected abstract bool isValidFile(FileInfo fileInfo);

        public virtual void Clear()
        { 
        
        }
    }

    public abstract class StationAcceptCoverPic : StationAcceptBase
    {
        protected StationAcceptCoverPic(string picPath)
        {
            picFolderPath = picPath;
        }

        protected string picFolderPath { get; set; }
        protected string curBtsPicFolderPath { get; set; }

        #region 重置指标图例
        protected delegate List<RangeInfo> Func(string band);

        /// <summary>
        /// 重置指标图例
        /// </summary>
        /// <param name="paramName">指标名</param>
        /// <param name="func">获取指标颜色范围的方法</param>
        /// <param name="band">速率指标可能会用到的带宽</param>
        protected virtual void reSetMapView(string paramName, Func func, string band)
        {
            MapSerialInfo msi = DTLayerSerialManager.Instance.GetSerialByName(paramName);
            if (msi != null)
            {
                List<RangeInfo> ranges = func(band);

                msi.ColorDisplayParam.Info.RangeColors = new List<DTParameterRangeColor>();
                foreach (RangeInfo range in ranges)
                {
                    DTParameterRangeColor paramColor = new DTParameterRangeColor(range.Min, range.Max, range.RangeColor);
                    paramColor.MaxIncluded = range.InculdeMax;
                    paramColor.MinIncluded = range.InculdeMin;
                    msi.ColorDisplayParam.Info.RangeColors.Add(paramColor);
                }
            }
        }

        //Example Func:
        //reSetMapView("lte_RSRP", new Func(getRsrpRanges), "");
        //protected virtual List<RangeInfo> getRsrpRanges()
        //{
        //    List<RangeInfo> ranges = new List<RangeInfo>();
        //    ranges.Add(new RangeInfo(false, false, -145, -110, Color.Red));
        //    ranges.Add(new RangeInfo(true, false, -110, -90, Color.Orange));
        //    ranges.Add(new RangeInfo(true, false, -90, -80, Color.Yellow));
        //    ranges.Add(new RangeInfo(true, false, -80, -70, Color.Green));
        //    ranges.Add(new RangeInfo(true, false, -70, -40, Color.Blue));
        //    return ranges;
        //}
        #endregion

        /// <summary>
        /// 根据文件名命名规则,获取带宽
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        protected virtual string getBandWidth(string fileName)
        {
            string bandWidth = "";
            string[] name = fileName.Split('_');
            if (name.Length >= 2)
            {
                string[] tmp = name[name.Length - 1].Split('-');
                if (tmp.Length >= 2)
                {
                    bandWidth = tmp[0];
                }
            }
            return bandWidth;
        }

        protected MTGis.DbRect getCoverBounds(DTFileDataManager fileManager, double longitude, double latitude)
        {
            double lngMin = longitude;
            double lngMax = longitude;
            double latMin = latitude;
            double latMax = latitude;

            MTGis.DbRect bounds = getCoverBounds(fileManager, ref lngMin, ref lngMax, ref latMin, ref latMax);
            return bounds;
        }

        protected MTGis.DbRect getCoverBounds(DTFileDataManager fileManager)
        {
            double lngMin = 1000;
            double lngMax = -1000;
            double latMin = 1000;
            double latMax = -1000;

            MTGis.DbRect bounds = getCoverBounds(fileManager, ref lngMin, ref lngMax, ref latMin, ref latMax);
            return bounds;
        }

        private MTGis.DbRect getCoverBounds(DTFileDataManager fileManager, ref double lngMin
            , ref double lngMax, ref double latMin, ref double latMax)
        {
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                if (tp.Longitude > 70 && tp.Latitude > 3)
                {
                    lngMin = Math.Min(lngMin, tp.Longitude);
                    lngMax = Math.Max(lngMax, tp.Longitude);
                    latMin = Math.Min(latMin, tp.Latitude);
                    latMax = Math.Max(latMax, tp.Latitude);
                }
            }
            MTGis.DbRect bounds = new MTGis.DbRect(lngMin - 0.001, latMin - 0.001
                , lngMax + 0.001, latMax + 0.001);
            return bounds;
        }

        protected MapForm initMap(MTGis.DbRect bounds)
        {
            //如果不进行FireDTDataChanged,那么不会显示采样点
            MapForm mf = MainModel.GetInstance().MainForm.GetMapForm();
            MainModel.GetInstance().FireDTDataChanged(mf);
            mf.GoToView(bounds);
            return mf;
        }

        /// <summary>
        /// 将图例和覆盖图一起截取,目前只支持MapWinGis,不支持Google
        /// 如果不适用,可以自己另行实现截图(Google截图可参考MapForm中DrawToBitmapDIYGoogleMap)
        /// </summary>
        protected virtual string fireMapAndTakePic(MapForm mf, string paramName, string filePath)
        {
            mf.FireAndOutputCurMapToPic(paramName, false, filePath);
            return filePath;
        }

        protected string getCoverPicPath(string btsName, string cellName, string paramName)
        {
            curBtsPicFolderPath = System.IO.Path.Combine(picFolderPath, btsName.Trim());
            if (!System.IO.Directory.Exists(curBtsPicFolderPath))
            {
                System.IO.Directory.CreateDirectory(curBtsPicFolderPath);
            }
            return System.IO.Path.Combine(curBtsPicFolderPath, cellName + "_" + paramName + ".png");
        }

        public override void Clear()
        {
            if (!string.IsNullOrEmpty(curBtsPicFolderPath))
            {
                StationAcceptDownloadPicHelper.Clear(curBtsPicFolderPath);
            }
        }

        public class RangeInfo
        {
            public RangeInfo(bool inculdeMin, bool inculdeMax, float min, float max, Color rangeColor)
            {
                Min = min;
                InculdeMin = inculdeMin;
                Max = max;
                InculdeMax = inculdeMax;
                RangeColor = rangeColor;
            }

            public float Min
            {
                get;
                private set;
            }
            public bool InculdeMin
            {
                get;
                private set;
            }
            public float Max
            {
                get;
                private set;
            }
            public bool InculdeMax
            {
                get;
                private set;
            }

            public Color RangeColor
            {
                get;
                private set;
            }
        }
    }

    public abstract class StationAcceptHanOverPic : StationAcceptCoverPic
    {
        protected StationAcceptHanOverPic(string picPath)
            : base(picPath) 
        {
        }

        #region 重置PCI图例
        protected virtual void reSetPCIMapView(ISite bts, string btsNamePostfix, string pciParamName)
        {
            MapSerialInfo msi = DTLayerSerialManager.Instance.GetSerialByName(pciParamName);
            if (msi == null)
            {
                return;
            }

            List<ICell> cellList = getValidCells(bts, btsNamePostfix);
            List<int> pciList = getPCIList(cellList);
            List<RangeInfo> ranges = getRangesList(pciList);
            changePCISerial(msi, ranges);
            setServerCellColorByPCI(cellList, ranges);
        }

        protected abstract List<ICell> getValidCells(ISite bts, string btsNamePostfix);

        protected abstract List<int> getPCIList(List<ICell> cellList);

        protected abstract void setServerCellColorByPCI(List<ICell> cellList, List<RangeInfo> ranges);

        protected virtual List<RangeInfo> getRangesList(List<int> pciList)
        {
            //目前最大是9小区,预设9种PCI颜色
            List<RangeInfo> ranges = new List<RangeInfo>();
            List<Color> colorList = new List<Color>
            {
                Color.Red,
                Color.Green,
                Color.Blue,
                Color.Yellow,
                Color.Fuchsia,
                Color.Bisque,
                Color.Pink,
                Color.Purple,
                Color.Aqua,
            };
            for (int i = 0; i < pciList.Count; i++)
            {
                ranges.Add(new RangeInfo(true, false, pciList[i], pciList[i] + 1, colorList[i]));
            }
            return ranges;
        }

        /// <summary>
        /// 重设pci图例,由于每个小区的PCI不同,图例需要根据pci进行变化
        /// </summary>
        /// <param name="msi"></param>
        /// <param name="ranges"></param>
        protected virtual void changePCISerial(MapSerialInfo msi, List<RangeInfo> ranges)
        {
            msi.ColorDisplayParam.Info.RangeColors = new List<DTParameterRangeColor>();
            foreach (RangeInfo range in ranges)
            {
                DTParameterRangeColor paramColor = new DTParameterRangeColor(range.Min, range.Max, range.RangeColor);
                paramColor.MaxIncluded = range.InculdeMax;
                paramColor.MinIncluded = range.InculdeMin;
                msi.ColorDisplayParam.Info.RangeColors.Add(paramColor);
            }
        }
        #endregion
    }
}
