﻿namespace MasterCom.RAMS.Stat
{
    partial class StatReportDockForm_B
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            GeneLau.WinFormsUI.Docking.DockPanelSkin dockPanelSkin1 = new GeneLau.WinFormsUI.Docking.DockPanelSkin();
            GeneLau.WinFormsUI.Docking.AutoHideStripSkin autoHideStripSkin1 = new GeneLau.WinFormsUI.Docking.AutoHideStripSkin();
            GeneLau.WinFormsUI.Docking.DockPanelGradient dockPanelGradient1 = new GeneLau.WinFormsUI.Docking.DockPanelGradient();
            GeneLau.WinFormsUI.Docking.TabGradient tabGradient1 = new GeneLau.WinFormsUI.Docking.TabGradient();
            GeneLau.WinFormsUI.Docking.DockPaneStripSkin dockPaneStripSkin1 = new GeneLau.WinFormsUI.Docking.DockPaneStripSkin();
            GeneLau.WinFormsUI.Docking.DockPaneStripGradient dockPaneStripGradient1 = new GeneLau.WinFormsUI.Docking.DockPaneStripGradient();
            GeneLau.WinFormsUI.Docking.TabGradient tabGradient2 = new GeneLau.WinFormsUI.Docking.TabGradient();
            GeneLau.WinFormsUI.Docking.DockPanelGradient dockPanelGradient2 = new GeneLau.WinFormsUI.Docking.DockPanelGradient();
            GeneLau.WinFormsUI.Docking.TabGradient tabGradient3 = new GeneLau.WinFormsUI.Docking.TabGradient();
            GeneLau.WinFormsUI.Docking.DockPaneStripToolWindowGradient dockPaneStripToolWindowGradient1 = new GeneLau.WinFormsUI.Docking.DockPaneStripToolWindowGradient();
            GeneLau.WinFormsUI.Docking.TabGradient tabGradient4 = new GeneLau.WinFormsUI.Docking.TabGradient();
            GeneLau.WinFormsUI.Docking.TabGradient tabGradient5 = new GeneLau.WinFormsUI.Docking.TabGradient();
            GeneLau.WinFormsUI.Docking.DockPanelGradient dockPanelGradient3 = new GeneLau.WinFormsUI.Docking.DockPanelGradient();
            GeneLau.WinFormsUI.Docking.TabGradient tabGradient6 = new GeneLau.WinFormsUI.Docking.TabGradient();
            GeneLau.WinFormsUI.Docking.TabGradient tabGradient7 = new GeneLau.WinFormsUI.Docking.TabGradient();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(StatReportDockForm_B));
            this.dockPanel = new GeneLau.WinFormsUI.Docking.DockPanel();
            this.toolStrip2 = new System.Windows.Forms.ToolStrip();
            this.btnSaveStyle = new System.Windows.Forms.ToolStripButton();
            this.btnSaveAsStyle = new System.Windows.Forms.ToolStripButton();
            this.btnDelStyle = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.btnExportExcel = new System.Windows.Forms.ToolStripButton();
            this.btnExportPDF = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.cascadeToolStripButton = new System.Windows.Forms.ToolStripButton();
            this.tileVerticalToolStripButton = new System.Windows.Forms.ToolStripButton();
            this.tileHorizontalToolStripButton = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.btnDrawRegionGrid = new System.Windows.Forms.ToolStripButton();
            this.toolStrip2.SuspendLayout();
            this.SuspendLayout();
            // 
            // dockPanel
            // 
            this.dockPanel.ActiveAutoHideContent = null;
            this.dockPanel.AllowEndUserNestedDocking = false;
            this.dockPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dockPanel.DockBackColor = System.Drawing.SystemColors.ControlLightLight;
            this.dockPanel.DockBottomPortion = 150;
            this.dockPanel.DockLeftPortion = 200;
            this.dockPanel.DockRightPortion = 200;
            this.dockPanel.DockTopPortion = 150;
            this.dockPanel.DocumentStyle = GeneLau.WinFormsUI.Docking.DocumentStyle.DockingSdi;
            this.dockPanel.Location = new System.Drawing.Point(0, 25);
            this.dockPanel.Name = "dockPanel";
            this.dockPanel.RightToLeftLayout = true;
            this.dockPanel.Size = new System.Drawing.Size(974, 391);
            dockPanelGradient1.EndColor = System.Drawing.SystemColors.ControlLight;
            dockPanelGradient1.StartColor = System.Drawing.SystemColors.ControlLight;
            autoHideStripSkin1.DockStripGradient = dockPanelGradient1;
            tabGradient1.EndColor = System.Drawing.SystemColors.Control;
            tabGradient1.StartColor = System.Drawing.SystemColors.Control;
            tabGradient1.TextColor = System.Drawing.SystemColors.ControlDarkDark;
            autoHideStripSkin1.TabGradient = tabGradient1;
            dockPanelSkin1.AutoHideStripSkin = autoHideStripSkin1;
            tabGradient2.EndColor = System.Drawing.SystemColors.ControlLightLight;
            tabGradient2.StartColor = System.Drawing.SystemColors.ControlLightLight;
            tabGradient2.TextColor = System.Drawing.SystemColors.ControlText;
            dockPaneStripGradient1.ActiveTabGradient = tabGradient2;
            dockPanelGradient2.EndColor = System.Drawing.SystemColors.Control;
            dockPanelGradient2.StartColor = System.Drawing.SystemColors.Control;
            dockPaneStripGradient1.DockStripGradient = dockPanelGradient2;
            tabGradient3.EndColor = System.Drawing.SystemColors.ControlLight;
            tabGradient3.StartColor = System.Drawing.SystemColors.ControlLight;
            tabGradient3.TextColor = System.Drawing.SystemColors.ControlText;
            dockPaneStripGradient1.InactiveTabGradient = tabGradient3;
            dockPaneStripSkin1.DocumentGradient = dockPaneStripGradient1;
            tabGradient4.EndColor = System.Drawing.SystemColors.GradientInactiveCaption;
            tabGradient4.LinearGradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical;
            tabGradient4.StartColor = System.Drawing.SystemColors.Control;
            tabGradient4.TextColor = System.Drawing.SystemColors.ActiveCaptionText;
            dockPaneStripToolWindowGradient1.ActiveCaptionGradient = tabGradient4;
            tabGradient5.EndColor = System.Drawing.SystemColors.Control;
            tabGradient5.StartColor = System.Drawing.SystemColors.Control;
            tabGradient5.TextColor = System.Drawing.SystemColors.ControlText;
            dockPaneStripToolWindowGradient1.ActiveTabGradient = tabGradient5;
            dockPanelGradient3.EndColor = System.Drawing.SystemColors.ControlLight;
            dockPanelGradient3.StartColor = System.Drawing.SystemColors.ControlLight;
            dockPaneStripToolWindowGradient1.DockStripGradient = dockPanelGradient3;
            tabGradient6.EndColor = System.Drawing.SystemColors.ControlDarkDark;
            tabGradient6.LinearGradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical;
            tabGradient6.StartColor = System.Drawing.SystemColors.Control;
            tabGradient6.TextColor = System.Drawing.SystemColors.ControlText;
            dockPaneStripToolWindowGradient1.InactiveCaptionGradient = tabGradient6;
            tabGradient7.EndColor = System.Drawing.Color.Transparent;
            tabGradient7.StartColor = System.Drawing.Color.Transparent;
            tabGradient7.TextColor = System.Drawing.SystemColors.ControlDarkDark;
            dockPaneStripToolWindowGradient1.InactiveTabGradient = tabGradient7;
            dockPaneStripSkin1.ToolWindowGradient = dockPaneStripToolWindowGradient1;
            dockPanelSkin1.DockPaneStripSkin = dockPaneStripSkin1;
            this.dockPanel.Skin = dockPanelSkin1;
            this.dockPanel.TabIndex = 11;
            this.dockPanel.ActiveDocumentChanged += new System.EventHandler(this.dockPanel_ActiveDocumentChanged);
            // 
            // toolStrip2
            // 
            this.toolStrip2.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.btnSaveStyle,
            this.btnSaveAsStyle,
            this.btnDelStyle,
            this.toolStripSeparator2,
            this.btnExportExcel,
            this.btnExportPDF,
            this.toolStripSeparator1,
            this.cascadeToolStripButton,
            this.tileVerticalToolStripButton,
            this.tileHorizontalToolStripButton,
            this.toolStripSeparator3,
            this.btnDrawRegionGrid});
            this.toolStrip2.Location = new System.Drawing.Point(0, 0);
            this.toolStrip2.Name = "toolStrip2";
            this.toolStrip2.RenderMode = System.Windows.Forms.ToolStripRenderMode.System;
            this.toolStrip2.Size = new System.Drawing.Size(974, 25);
            this.toolStrip2.TabIndex = 10;
            this.toolStrip2.Text = "toolStrip2";
            // 
            // btnSaveStyle
            // 
            this.btnSaveStyle.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnSaveStyle.Enabled = false;
            this.btnSaveStyle.Image = global::MasterCom.RAMS.Properties.Resources.save;
            this.btnSaveStyle.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnSaveStyle.Name = "btnSaveStyle";
            this.btnSaveStyle.Size = new System.Drawing.Size(23, 22);
            this.btnSaveStyle.Text = "保存报表样式";
            this.btnSaveStyle.Click += new System.EventHandler(this.btnSaveStyle_Click);
            this.btnSaveStyle.Alignment = System.Windows.Forms.ToolStripItemAlignment.Left;
            // 
            // btnSaveAsStyle
            // 
            this.btnSaveAsStyle.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnSaveAsStyle.Enabled = false;
            this.btnSaveAsStyle.Image = global::MasterCom.RAMS.Properties.Resources.saveas;
            this.btnSaveAsStyle.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnSaveAsStyle.Name = "btnSaveAsStyle";
            this.btnSaveAsStyle.Size = new System.Drawing.Size(23, 22);
            this.btnSaveAsStyle.Text = "保存并拷贝为新的报表样式";
            this.btnSaveAsStyle.Click += new System.EventHandler(this.btnSaveAsStyle_Click);
            this.btnSaveAsStyle.Alignment = System.Windows.Forms.ToolStripItemAlignment.Left;
            // 
            // btnDelStyle
            // 
            this.btnDelStyle.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnDelStyle.Enabled = false;
            this.btnDelStyle.Image = global::MasterCom.RAMS.Properties.Resources.delete;
            this.btnDelStyle.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnDelStyle.Name = "btnDelStyle";
            this.btnDelStyle.Size = new System.Drawing.Size(23, 22);
            this.btnDelStyle.Text = "删除报表样式";
            this.btnDelStyle.Click += new System.EventHandler(this.btnDelStyle_Click);
            this.btnDelStyle.Alignment = System.Windows.Forms.ToolStripItemAlignment.Left;
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(6, 25);
            // 
            // btnExportExcel
            // 
            this.btnExportExcel.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnExportExcel.Enabled = false;
            this.btnExportExcel.Image = global::MasterCom.RAMS.Properties.Resources.xls;
            this.btnExportExcel.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnExportExcel.Name = "btnExportExcel";
            this.btnExportExcel.Size = new System.Drawing.Size(23, 22);
            this.btnExportExcel.Text = "导出到Excel文件";
            this.btnExportExcel.Click += new System.EventHandler(this.btnExportExcel_Click);
            // 
            // btnExportPDF
            // 
            this.btnExportPDF.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnExportPDF.Enabled = false;
            this.btnExportPDF.Image = global::MasterCom.RAMS.Properties.Resources.pdf;
            this.btnExportPDF.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnExportPDF.Name = "btnExportPDF";
            this.btnExportPDF.Size = new System.Drawing.Size(23, 22);
            this.btnExportPDF.Text = "导出到PDF文件";
            this.btnExportPDF.Click += new System.EventHandler(this.btnExportPDF_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(6, 25);
            // 
            // cascadeToolStripButton
            // 
            this.cascadeToolStripButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.cascadeToolStripButton.Image = ((System.Drawing.Image)(resources.GetObject("cascadeToolStripButton.Image")));
            this.cascadeToolStripButton.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.cascadeToolStripButton.Name = "cascadeToolStripButton";
            this.cascadeToolStripButton.Size = new System.Drawing.Size(23, 22);
            this.cascadeToolStripButton.Text = "层叠窗口";
            this.cascadeToolStripButton.Click += new System.EventHandler(this.cascadeToolStripButton_Click);
            // 
            // tileVerticalToolStripButton
            // 
            this.tileVerticalToolStripButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tileVerticalToolStripButton.Image = ((System.Drawing.Image)(resources.GetObject("tileVerticalToolStripButton.Image")));
            this.tileVerticalToolStripButton.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tileVerticalToolStripButton.Name = "tileVerticalToolStripButton";
            this.tileVerticalToolStripButton.Size = new System.Drawing.Size(23, 22);
            this.tileVerticalToolStripButton.Text = "垂直平铺窗口";
            this.tileVerticalToolStripButton.Click += new System.EventHandler(this.tileVerticalToolStripButton_Click);
            // 
            // tileHorizontalToolStripButton
            // 
            this.tileHorizontalToolStripButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tileHorizontalToolStripButton.Image = ((System.Drawing.Image)(resources.GetObject("tileHorizontalToolStripButton.Image")));
            this.tileHorizontalToolStripButton.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tileHorizontalToolStripButton.Name = "tileHorizontalToolStripButton";
            this.tileHorizontalToolStripButton.Size = new System.Drawing.Size(23, 22);
            this.tileHorizontalToolStripButton.Text = "水平平铺窗口";
            this.tileHorizontalToolStripButton.Click += new System.EventHandler(this.tileHorizontalToolStripButton_Click);
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(6, 25);
            // 
            // btnDrawRegionGrid
            // 
            this.btnDrawRegionGrid.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnDrawRegionGrid.Image = ((System.Drawing.Image)(resources.GetObject("btnDrawRegionGrid.Image")));
            this.btnDrawRegionGrid.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnDrawRegionGrid.Name = "btnDrawRegionGrid";
            this.btnDrawRegionGrid.Size = new System.Drawing.Size(23, 22);
            this.btnDrawRegionGrid.Text = "渲染区域网格";
            this.btnDrawRegionGrid.Click += new System.EventHandler(this.btnDrawRegionGrid_Click);
            // 
            // StatReportDockForm_B
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(974, 416);
            this.Controls.Add(this.dockPanel);
            this.Controls.Add(this.toolStrip2);
            this.Name = "StatReportDockForm_B";
            this.Text = "集团网格统计";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.StatReportDockForm_B_FormClosing);
            this.toolStrip2.ResumeLayout(false);
            this.toolStrip2.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private GeneLau.WinFormsUI.Docking.DockPanel dockPanel;
        private System.Windows.Forms.ToolStrip toolStrip2;
        private System.Windows.Forms.ToolStripButton btnSaveStyle;
        private System.Windows.Forms.ToolStripButton btnSaveAsStyle;
        private System.Windows.Forms.ToolStripButton btnDelStyle;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripButton btnExportExcel;
        private System.Windows.Forms.ToolStripButton btnExportPDF;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripButton cascadeToolStripButton;
        private System.Windows.Forms.ToolStripButton tileVerticalToolStripButton;
        private System.Windows.Forms.ToolStripButton tileHorizontalToolStripButton;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private System.Windows.Forms.ToolStripButton btnDrawRegionGrid;
    }
}