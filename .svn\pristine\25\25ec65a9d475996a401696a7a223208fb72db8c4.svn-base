﻿using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.src.MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class NBScanTraverseRate : DIYSampleByRegion
    {
        public NBScanTraverseRate() : base(MainModel.GetInstance())
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NBIOTSCAN);
        }

        private static NBScanTraverseRate intance = null;
        protected static readonly object LockObj = new object();
        public static NBScanTraverseRate GetInstance()
        {
            if (intance == null)
            {
                lock (LockObj)
                {
                    if (intance == null)
                    {
                        intance = new NBScanTraverseRate();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "NB-IOT扫频遍历率指标统计"; }
        }
        public override string IconName
        {
            get { return string.Empty; }
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSSINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LTE_SCAN");
            tmpDic.Add("themeName", (object)"TopN_CELL_Specific_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        ZTAntennaBase antBase = null;
        public Dictionary<string, AreaNetCellInfo> dicResult { get; set; }
        protected override void query()
        {
            //地图匹配处理
            antBase = new ZTAntennaBase(MainModel);
            antBase.InitRegionMop2();

            dicResult = new Dictionary<string, AreaNetCellInfo>();
            getAreaNetCells();
            base.query();
            fireShowForm();
        }

        /// <summary>
        /// 获取区域内网格小区（分母）
        /// </summary>
        public void getAreaNetCells()
        {
            var cells = CellManager.GetInstance().GetCurrentLTECells();
            foreach (var item in cells)
            {
                //获取网格名称
                string strGrid = "";
                if (antBase != null)
                {
                    antBase.isContainPoint(item.Longitude, item.Latitude, ref strGrid);
                }

                if (!string.IsNullOrEmpty(strGrid))
                {
                    //判断是否NB小区
                    if (isNBCell(item.EARFCN))
                    {
                        //如果是新加的网格
                        if (!dicResult.ContainsKey(strGrid))
                        {
                            AreaNetCellInfo areaNetCellInfo = new AreaNetCellInfo();
                            areaNetCellInfo.AreaNetCellList = new List<string>();
                            areaNetCellInfo.SuspectCellList = new List<string>();
                            if (!areaNetCellInfo.AreaNetCellList.Contains(item.Name))
                            {
                                areaNetCellInfo.AreaNetCellList.Add(item.Name);
                            }

                            dicResult.Add(strGrid, areaNetCellInfo);
                        }
                        else
                        {
                            //在已有网格项中添加区域网格内的小区（分母）
                            if (!dicResult[strGrid].AreaNetCellList.Contains(item.Name))
                            {
                                dicResult[strGrid].AreaNetCellList.Add(item.Name);
                            }
                        }
                    }
                }
            }
        }

        public bool isNBCell(int EARFCN)
        {
            //移动
            if ((EARFCN >= 3450 && EARFCN <= 3531) ||
                (EARFCN >= 3533 && EARFCN <= 3575) ||
                (EARFCN >= 3577 && EARFCN <= 3656) ||
                (EARFCN >= 3680 && EARFCN <= 3688) ||
                (EARFCN >= 3708 && EARFCN <= 3714) ||
                 (EARFCN >= 3716 && EARFCN <= 3769) ||
                  (EARFCN >= 3771 && EARFCN <= 3791) ||
                   (EARFCN >= 39401 && EARFCN <= 19948) ||
                   //联通
                   (EARFCN >= 3792 && EARFCN <= 3798) ||
                   (EARFCN == 1454) ||
                   //电信
                   (EARFCN >= 2504 && EARFCN <= 2509) ||
                   (EARFCN >= 1547 && EARFCN <= 1548) ||
                    (EARFCN == 20506))
            {
                return true;
            }

            return false;
        }

        private void fireShowForm()
        {
            var fmr = MainModel.CreateResultForm(typeof(NBScanTraverseRateForm))
                as NBScanTraverseRateForm;
            fmr.FillData(dicResult);
            fmr.Visible = true;
            fmr.BringToFront();
        }

        /// <summary>
        /// 处理采样点数据
        /// </summary>
        /// <param name="tp"></param>
        protected override void doWithDTData(TestPoint tp)
        {
            for (int i = 0; i < 16; i++)
            {
                if (tp["LTESCAN_TopN_CELL_Specific_RSRP"] != null)
                {
                    //匹配小区
                    ICell cell = tp.GetCell_LTEScan(i);
                    if (cell == null)
                    {
                        return;
                    }
                    //获取网格名称
                    string strGrid = "";
                    if (antBase != null)
                    {
                        antBase.isContainPoint(tp.Longitude, tp.Latitude, ref strGrid);
                    }
                    if (string.IsNullOrEmpty(strGrid))
                    {
                        return;
                    }

                    int EARFCN = Convert.ToInt32(tp["LTESCAN_TopN_EARFCN"].ToString());
                    //判断是否NB小区
                    if (isNBCell(EARFCN))
                    {
                        //添加分子
                        if (isSuspectCell(tp, cell))
                        {
                            if (!dicResult[strGrid].SuspectCellList.Contains(cell.Name))
                            {
                                dicResult[strGrid].SuspectCellList.Add(cell.Name);
                            }
                        }
                    }
                }
            }
        }

        public bool isSuspectCell(TestPoint tp, ICell cell)
        {
            double rsrp = Convert.ToDouble(tp["LTESCAN_TopN_CELL_Specific_RSRP"].ToString());

            //遍历率分子条件
            if (rsrp > -90)
            {
                //判断是非可疑小区
                if (MathFuncs.GetDistance(tp.Longitude, tp.Latitude, cell.Longitude, cell.Latitude) < 1200)
                {
                    return true;
                }
            }

            return false;
        }
    }

    /// <summary>
    /// 区域网格内小区信息
    /// </summary>
    public class AreaNetCellInfo
    {
        public List<string> SuspectCellList { get; set; }

        public List<string> AreaNetCellList { get; set; }
    }
}
