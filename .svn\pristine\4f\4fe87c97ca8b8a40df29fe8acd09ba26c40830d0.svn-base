﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Func.SystemSetting
{
    public partial class CellMultiCoverageProperties_LTESCAN : PropertiesControl
    {
        public CellMultiCoverageProperties_LTESCAN(ZTCellMultiCoverage_LTE queryFunc)
        {
            InitializeComponent();
            this.queryFunc = queryFunc;
        }

        public override string ParentName
        {
            get { return queryFunc.FuncType.ToString(); }
        }

        public override string ParentSubName
        {
            get { return queryFunc.SubFuncType.ToString(); }
        }

        public override string SelfName
        {
            get { return queryFunc.Name; }
        }

        public override string TabPageName
        {
            get { return queryFunc.Name; }
        }

        public override void Flush()
        {
            chkBackgroundStat.Checked = queryFunc.BackgroundStat;
            numRxLevDValue.Value = (decimal)queryFunc.setRxlevDiff;
            numRxLevThreshold.Value = (decimal)queryFunc.setRxlev;
            numInvalidThresold.Value = (decimal)queryFunc.invalidPointRxLev;
        }

        public override bool IsValid()
        {
            return true;
        }

        public override void Apply()
        {
            queryFunc.BackgroundStat = chkBackgroundStat.Checked;
            queryFunc.setRxlev = (int)numRxLevThreshold.Value;
            queryFunc.setRxlevDiff = (int)numRxLevDValue.Value;
            queryFunc.invalidPointRxLev = (int)numInvalidThresold.Value;
        }

        private ZTCellMultiCoverage_LTE queryFunc;
    }
}
