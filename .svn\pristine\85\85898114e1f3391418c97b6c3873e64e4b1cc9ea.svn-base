﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTReportEventStatSetForm_BJ
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.dtEnd = new System.Windows.Forms.DateTimePicker();
            this.dtBegin = new System.Windows.Forms.DateTimePicker();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.ckbSelectAllATU = new System.Windows.Forms.CheckBox();
            this.ckbSelectNoneATU = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.treeListDevice = new DevExpress.XtraTreeList.TreeList();
            this.colDeviceChk = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colDeviceNO = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colDeviceDesc = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.ckbShieldOther = new System.Windows.Forms.CheckBox();
            this.label10 = new System.Windows.Forms.Label();
            this.cmbNetType = new System.Windows.Forms.ComboBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.treeListProject = new DevExpress.XtraTreeList.TreeList();
            this.treeListColumn1 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumn3 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.ckbSelectNoneProj = new System.Windows.Forms.CheckBox();
            this.ckbSelectAllProj = new System.Windows.Forms.CheckBox();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListDevice)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListProject)).BeginInit();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(720, 472);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(87, 27);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnOK.Location = new System.Drawing.Point(611, 472);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(87, 27);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // dtEnd
            // 
            this.dtEnd.Location = new System.Drawing.Point(334, 16);
            this.dtEnd.Name = "dtEnd";
            this.dtEnd.Size = new System.Drawing.Size(126, 21);
            this.dtEnd.TabIndex = 18;
            // 
            // dtBegin
            // 
            this.dtBegin.Location = new System.Drawing.Point(99, 17);
            this.dtBegin.Name = "dtBegin";
            this.dtBegin.Size = new System.Drawing.Size(126, 21);
            this.dtBegin.TabIndex = 17;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(29, 22);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 21;
            this.label1.Text = "开始时间";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(261, 22);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(53, 12);
            this.label2.TabIndex = 22;
            this.label2.Text = "结束时间";
            // 
            // ckbSelectAllATU
            // 
            this.ckbSelectAllATU.AutoSize = true;
            this.ckbSelectAllATU.Location = new System.Drawing.Point(23, 346);
            this.ckbSelectAllATU.Name = "ckbSelectAllATU";
            this.ckbSelectAllATU.Size = new System.Drawing.Size(48, 16);
            this.ckbSelectAllATU.TabIndex = 24;
            this.ckbSelectAllATU.Text = "全选";
            this.ckbSelectAllATU.UseVisualStyleBackColor = true;
            this.ckbSelectAllATU.CheckedChanged += new System.EventHandler(this.ckbSelectAllATU_CheckedChanged);
            // 
            // ckbSelectNoneATU
            // 
            this.ckbSelectNoneATU.AutoSize = true;
            this.ckbSelectNoneATU.Location = new System.Drawing.Point(93, 346);
            this.ckbSelectNoneATU.Name = "ckbSelectNoneATU";
            this.ckbSelectNoneATU.Size = new System.Drawing.Size(60, 16);
            this.ckbSelectNoneATU.TabIndex = 25;
            this.ckbSelectNoneATU.Text = "全不选";
            this.ckbSelectNoneATU.UseVisualStyleBackColor = true;
            this.ckbSelectNoneATU.CheckedChanged += new System.EventHandler(this.ckbSelectNoneATU_CheckedChanged);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.treeListDevice);
            this.groupBox1.Controls.Add(this.ckbShieldOther);
            this.groupBox1.Controls.Add(this.ckbSelectNoneATU);
            this.groupBox1.Controls.Add(this.ckbSelectAllATU);
            this.groupBox1.Location = new System.Drawing.Point(28, 70);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(440, 374);
            this.groupBox1.TabIndex = 26;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "ATU设备筛选";
            // 
            // treeListDevice
            // 
            this.treeListDevice.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.colDeviceChk,
            this.colDeviceNO,
            this.colDeviceDesc});
            this.treeListDevice.Location = new System.Drawing.Point(23, 23);
            this.treeListDevice.Name = "treeListDevice";
            this.treeListDevice.BeginUnboundLoad();
            this.treeListDevice.AppendNode(new object[] {
            null,
            null,
            null}, -1);
            this.treeListDevice.AppendNode(new object[] {
            null,
            null,
            null}, -1);
            this.treeListDevice.AppendNode(new object[] {
            null,
            null,
            null}, -1);
            this.treeListDevice.AppendNode(new object[] {
            null,
            null,
            null}, -1);
            this.treeListDevice.AppendNode(new object[] {
            null,
            null,
            null}, -1);
            this.treeListDevice.AppendNode(new object[] {
            null,
            null,
            null}, -1);
            this.treeListDevice.EndUnboundLoad();
            this.treeListDevice.OptionsView.AutoWidth = false;
            this.treeListDevice.OptionsView.ShowCheckBoxes = true;
            this.treeListDevice.OptionsView.ShowFocusedFrame = false;
            this.treeListDevice.OptionsView.ShowIndicator = false;
            this.treeListDevice.Size = new System.Drawing.Size(395, 313);
            this.treeListDevice.TabIndex = 27;
            this.treeListDevice.TreeLineStyle = DevExpress.XtraTreeList.LineStyle.None;
            // 
            // colDeviceChk
            // 
            this.colDeviceChk.Caption = "参与统计";
            this.colDeviceChk.FieldName = "参与统计";
            this.colDeviceChk.MinWidth = 36;
            this.colDeviceChk.Name = "colDeviceChk";
            this.colDeviceChk.OptionsColumn.AllowEdit = false;
            this.colDeviceChk.Visible = true;
            this.colDeviceChk.VisibleIndex = 0;
            this.colDeviceChk.Width = 58;
            // 
            // colDeviceNO
            // 
            this.colDeviceNO.Caption = "设备编号";
            this.colDeviceNO.FieldName = "设备编号";
            this.colDeviceNO.Name = "colDeviceNO";
            this.colDeviceNO.OptionsColumn.AllowEdit = false;
            this.colDeviceNO.Visible = true;
            this.colDeviceNO.VisibleIndex = 1;
            this.colDeviceNO.Width = 94;
            // 
            // colDeviceDesc
            // 
            this.colDeviceDesc.Caption = "设备描述";
            this.colDeviceDesc.FieldName = "设备描述";
            this.colDeviceDesc.Name = "colDeviceDesc";
            this.colDeviceDesc.OptionsColumn.AllowEdit = false;
            this.colDeviceDesc.SortOrder = System.Windows.Forms.SortOrder.Descending;
            this.colDeviceDesc.Visible = true;
            this.colDeviceDesc.VisibleIndex = 2;
            this.colDeviceDesc.Width = 216;
            // 
            // ckbShieldOther
            // 
            this.ckbShieldOther.AutoSize = true;
            this.ckbShieldOther.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.ckbShieldOther.Location = new System.Drawing.Point(257, 346);
            this.ckbShieldOther.Name = "ckbShieldOther";
            this.ckbShieldOther.Size = new System.Drawing.Size(138, 16);
            this.ckbShieldOther.TabIndex = 26;
            this.ckbShieldOther.Text = "不显示非ATU设备数据";
            this.ckbShieldOther.UseVisualStyleBackColor = false;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(499, 22);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(53, 12);
            this.label10.TabIndex = 27;
            this.label10.Text = "网络类型";
            // 
            // cmbNetType
            // 
            this.cmbNetType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbNetType.FormattingEnabled = true;
            this.cmbNetType.Items.AddRange(new object[] {
            "GSM",
            "TD",
            "LTE",
            "LTESCAN",
            "CSFB",
            "VoLTE",
            "全部"});
            this.cmbNetType.Location = new System.Drawing.Point(569, 19);
            this.cmbNetType.Name = "cmbNetType";
            this.cmbNetType.Size = new System.Drawing.Size(126, 22);
            this.cmbNetType.TabIndex = 26;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.treeListProject);
            this.groupBox2.Controls.Add(this.ckbSelectNoneProj);
            this.groupBox2.Controls.Add(this.ckbSelectAllProj);
            this.groupBox2.Location = new System.Drawing.Point(497, 70);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(310, 374);
            this.groupBox2.TabIndex = 28;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "项目筛选";
            // 
            // treeListProject
            // 
            this.treeListProject.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.treeListColumn1,
            this.treeListColumn3});
            this.treeListProject.Location = new System.Drawing.Point(21, 20);
            this.treeListProject.Name = "treeListProject";
            this.treeListProject.BeginUnboundLoad();
            this.treeListProject.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListProject.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListProject.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListProject.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListProject.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListProject.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListProject.EndUnboundLoad();
            this.treeListProject.OptionsView.AutoWidth = false;
            this.treeListProject.OptionsView.ShowCheckBoxes = true;
            this.treeListProject.OptionsView.ShowFocusedFrame = false;
            this.treeListProject.OptionsView.ShowIndicator = false;
            this.treeListProject.Size = new System.Drawing.Size(269, 313);
            this.treeListProject.TabIndex = 29;
            this.treeListProject.TreeLineStyle = DevExpress.XtraTreeList.LineStyle.None;
            // 
            // treeListColumn1
            // 
            this.treeListColumn1.Caption = "参与统计";
            this.treeListColumn1.FieldName = "参与统计";
            this.treeListColumn1.MinWidth = 36;
            this.treeListColumn1.Name = "treeListColumn1";
            this.treeListColumn1.OptionsColumn.AllowEdit = false;
            this.treeListColumn1.Visible = true;
            this.treeListColumn1.VisibleIndex = 0;
            this.treeListColumn1.Width = 60;
            // 
            // treeListColumn3
            // 
            this.treeListColumn3.Caption = "项目";
            this.treeListColumn3.FieldName = "项目";
            this.treeListColumn3.Name = "treeListColumn3";
            this.treeListColumn3.OptionsColumn.AllowEdit = false;
            this.treeListColumn3.Visible = true;
            this.treeListColumn3.VisibleIndex = 1;
            this.treeListColumn3.Width = 181;
            // 
            // ckbSelectNoneProj
            // 
            this.ckbSelectNoneProj.AutoSize = true;
            this.ckbSelectNoneProj.Location = new System.Drawing.Point(91, 346);
            this.ckbSelectNoneProj.Name = "ckbSelectNoneProj";
            this.ckbSelectNoneProj.Size = new System.Drawing.Size(60, 16);
            this.ckbSelectNoneProj.TabIndex = 28;
            this.ckbSelectNoneProj.Text = "全不选";
            this.ckbSelectNoneProj.UseVisualStyleBackColor = true;
            this.ckbSelectNoneProj.CheckedChanged += new System.EventHandler(this.ckbSelectNoneProj_CheckedChanged);
            // 
            // ckbSelectAllProj
            // 
            this.ckbSelectAllProj.AutoSize = true;
            this.ckbSelectAllProj.Location = new System.Drawing.Point(21, 346);
            this.ckbSelectAllProj.Name = "ckbSelectAllProj";
            this.ckbSelectAllProj.Size = new System.Drawing.Size(48, 16);
            this.ckbSelectAllProj.TabIndex = 27;
            this.ckbSelectAllProj.Text = "全选";
            this.ckbSelectAllProj.UseVisualStyleBackColor = true;
            this.ckbSelectAllProj.CheckedChanged += new System.EventHandler(this.ckbSelectAllProj_CheckedChanged);
            // 
            // ZTReportEventStatSetForm_BJ
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(840, 513);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.label10);
            this.Controls.Add(this.cmbNetType);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.dtEnd);
            this.Controls.Add(this.dtBegin);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Name = "ZTReportEventStatSetForm_BJ";
            this.ShowIcon = false;
            this.Text = "条件设置";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListDevice)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListProject)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.DateTimePicker dtEnd;
        private System.Windows.Forms.DateTimePicker dtBegin;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.CheckBox ckbSelectAllATU;
        private System.Windows.Forms.CheckBox ckbSelectNoneATU;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.ComboBox cmbNetType;
        private System.Windows.Forms.CheckBox ckbShieldOther;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.CheckBox ckbSelectNoneProj;
        private System.Windows.Forms.CheckBox ckbSelectAllProj;
        private DevExpress.XtraTreeList.TreeList treeListDevice;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colDeviceChk;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colDeviceNO;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colDeviceDesc;
        private DevExpress.XtraTreeList.TreeList treeListProject;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn1;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn3;
    }
}