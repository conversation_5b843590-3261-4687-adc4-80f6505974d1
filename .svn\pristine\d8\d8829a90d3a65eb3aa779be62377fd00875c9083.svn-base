﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.Injection;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public class RegionRoadInjectionQuery : QueryKPIStatBase
    {
        public RegionRoadInjectionQuery()
            : base(MainModel.GetInstance())
        {
        }

        public override string Name
        {
            get { return "测试计划区域道路渗透率"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18035, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.None;
        }

        protected override void AddGeographicFilter(Package package)
        {
            package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
            package.Content.AddParam(regionBounds.x1);
            package.Content.AddParam(regionBounds.y2);
            package.Content.AddParam(regionBounds.x2);
            package.Content.AddParam(regionBounds.y1);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        DbRect regionBounds;
        protected override bool getConditionBeforeQuery()
        {
            RegionRoadLayerSettingDlg dlg = new RegionRoadLayerSettingDlg();
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            regionBounds = null;
            idRegionDic = new Dictionary<int, ResvRegion>();
            regionGridDic = new Dictionary<ResvRegion, GridMatrix<InjectGridUnit>>();
            Shapefile regionShp = new Shapefile();
            regionShp.Open(dlg.RegionPath, null);
            int regionIDIdx = dlg.RegionIDIdx;
            for (int i = 0; i < regionShp.NumShapes; i++)
            {
                MapWinGIS.Shape shp = regionShp.get_Shape(i);
                ResvRegion rg = new ResvRegion();
                rg.RegionName = regionShp.get_CellValue(regionIDIdx, i).ToString();
                rg.Shape = shp;
                int areaID;
                if (!int.TryParse(rg.RegionName, out areaID))
                {
                    MessageBox.Show(string.Format("区域图层，第{0}字段不是有效的AreaID，请核查！", regionIDIdx + 1));
                    return false;
                }
                idRegionDic[areaID] = rg;
                regionGridDic[rg] = new GridMatrix<InjectGridUnit>();
                DbRect bounds = new DbRect(shp.Extents.xMin, shp.Extents.yMin, shp.Extents.xMax, shp.Extents.yMax);
                if (regionBounds == null)
                {
                    regionBounds = bounds;
                }
                else
                {
                    regionBounds.MergeRects(bounds);
                }
            }
            condition.Areas = new Dictionary<int, List<int>>();
            condition.Areas[dlg.AreaType] = null;//只查询指定AreaType文件
            roadLayerSet = dlg.RoadLayers;
            return true;
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            getTriadIDIgnoreServiceType(new string[] { "" });
            return "1,1,1";
        }

        protected override void fireShowResult()
        {
            InjectionResultForm injectForm = MainModel.CreateResultForm(typeof(InjectionResultForm)) as InjectionResultForm;
            injectForm.FillData(totalResultList, regionMileageInfoSet);
            injectForm.Visible = true;
            injectForm.BringToFront();
        }

        private Dictionary<int, ResvRegion> idRegionDic;
        private Dictionary<ResvRegion, GridMatrix<InjectGridUnit>> regionGridDic;
        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, params object[] reservedParams)
        {
            Package package = clientProxy.Package;
            int counter = 0;
            bool recved = false;
            int curPercent = 11;
            DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (!recved)
                {
                    WaitBox.Text = "正在从服务器接收数据...";
                }
                recved = true;
                KPIStatDataBase singleStatData = null;
                if (isFileHeaderContentType(package.Content.Type))
                {
                    recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                }
                else if (isImgColDefContent(package, curImgColumnDef))
                {
                    //
                }
                else if (isKPIDataContent(package, out singleStatData))
                {
                    fillData(package, headerManager, curImgColumnDef, singleStatData);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    MessageBox.Show("Unexpected type: " + package.Content.Type);
                    break;
                }

                setProgressPercent(ref counter, ref curPercent);
            }
        }

        private void fillData(Package package, DTDataHeaderManager headerManager, List<StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            fillStatData(package, curImgColumnDef, singleStatData);
            FileInfo fi = headerManager.GetHeaderByFileID(singleStatData.FileID);
            double distance = singleStatData.GetValue("0806", null, null);
            ResvRegion region;
            if (idRegionDic.TryGetValue(fi.AreaID, out region))
            {
                InjectGridUnit igu = new InjectGridUnit();
                igu.LTLng = lng;
                igu.LTLat = lat;
                GridMatrix<InjectGridUnit> matrix = regionGridDic[region];
                igu = matrix[igu.RowIdx, igu.ColIdx];
                if (igu == null)
                {
                    igu = new InjectGridUnit();
                    igu.LTLng = lng;
                    igu.LTLat = lat;
                    matrix[igu.RowIdx, igu.ColIdx] = igu;
                    igu.Status = 1;
                }
                igu.fileRepeatDic[fi.ID] = true;
                igu.repeatCount++;
                igu.TestDistance += distance;
            }
        }

        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.grid;
        }
       
        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            WaitTextBox.Show(calcInjection);
        }

        List<StreetInjectTableInfo> roadLayerSet = null;
        List<RegionMileageInfo> regionMileageInfoSet = null;
        List<StreetInjectInfo> totalResultList = null;
        private void calcInjection()
        {
            int iLoopRegion = 0;
            regionMileageInfoSet = new List<RegionMileageInfo>();
            totalResultList = new List<StreetInjectInfo>();
            foreach (ResvRegion resvRegion in regionGridDic.Keys)
            {
                GridMatrix<InjectGridUnit> gridMatrix = regionGridDic[resvRegion];

                //区域内道路分析时占用到的栅格。有可能多条道路都占用到同一个栅格。
                Dictionary<InjectGridUnit, int> coveredGridDic = new Dictionary<InjectGridUnit, int>();
                iLoopRegion++;
                int iLoopMap = 0;
                foreach (StreetInjectTableInfo tableInfo in roadLayerSet)
                {
                    iLoopMap++;
                    Shapefile roadShpFile = new Shapefile();
                    roadShpFile.Open(tableInfo.FilePath, null);
                    WaitTextBox.Text = string.Format("{0}/{1} " + "正在分析区域["
                        + resvRegion.RegionName + "] [ " + tableInfo.FileName + " ]覆盖情况", iLoopRegion, regionGridDic.Count);
                    totalResultList.AddRange(
                        calcRegionInjection(gridMatrix, resvRegion, roadShpFile
                        , tableInfo.ColumnName, out coveredGridDic));
                    roadShpFile.Close();
                }

                RegionMileageInfo regionMileageInfo = new RegionMileageInfo(resvRegion.RegionName);
                regionMileageInfo.Area = RegionAreaCalculator.CalculateArea(resvRegion.Shape);
                //区域内栅格里程
                foreach (InjectGridUnit grid in gridMatrix)
                {
                    if (grid != null && resvRegion.GeoOp.CheckRectCenterInRegion(grid.Bounds))
                    {
                        regionMileageInfo.MileageInfo.Total += grid.TestDistance;
                    }
                }

                double coveredMileage = 0;
                foreach (InjectGridUnit item in coveredGridDic.Keys)
                {
                    coveredMileage += item.TestDistance;
                }
                regionMileageInfo.MileageInfo.CoveredData = coveredMileage;
                regionMileageInfoSet.Add(regionMileageInfo);
            }
            WaitTextBox.Close();
        }

        private List<StreetInjectInfo> calcRegionInjection(GridMatrix<InjectGridUnit> gridMatrix
            , ResvRegion region, Shapefile roadShpFile, string roadNameCol, out Dictionary<InjectGridUnit, int> coveredGridDic)
        {
            List<StreetInjectInfo> roadInjectionSet = new List<StreetInjectInfo>();
            MTPolygon mapOp = region.GeoOp;
            string regionName = region.RegionName;
            coveredGridDic = new Dictionary<InjectGridUnit, int>();
            int nmFldIdx = 0;
            int numFields = roadShpFile.NumFields;
            for (int x = 0; x < numFields; x++)
            {
                MapWinGIS.Field field = roadShpFile.get_Field(x);
                if (field.Name.Equals(roadNameCol))
                {
                    nmFldIdx = x;
                    break;
                }
            }
            string shpFileName = System.IO.Path.GetFileNameWithoutExtension(roadShpFile.Filename);
            int streetCount = roadShpFile.NumShapes;
            for (int dx = 0; dx < streetCount; dx++)
            {
                MapWinGIS.Shape street = roadShpFile.get_Shape(dx);
                if (street == null || !(mapOp.CheckStreetInRegion(street)))
                {
                    continue;
                }
                string streetName = "未命名";
                object nmObj = roadShpFile.get_CellValue(nmFldIdx, dx);
                if (nmObj != null)
                {
                    string tempName = nmObj.ToString().Trim();
                    streetName = string.IsNullOrEmpty(tempName) ? streetName : tempName;
                }

                MapWinGIS.Shape roadMultiCurv = street;
                StreetInjectInfo ijinfo = new StreetInjectInfo();
                ijinfo.StreetName = streetName;
                ijinfo.AreaName = regionName;
                ijinfo.StreetTableName = shpFileName;

                int coveredCount = 0;
                int uncoveredCount = 0;
                for (int part = 0; part < roadMultiCurv.NumParts; part++)
                {
                    List<DbPoint> pnts = ShapeHelper.GetPartShapePoints(roadMultiCurv, part);
                    if (pnts == null)
                    {
                        continue;
                    }
                    DbPoint[] pts = pnts.ToArray();
                    CovSegmentGenerator csg = new CovSegmentGenerator();

                    #region 计算渗透
                    for (int i = 0; i < pts.Length - 1; i++)
                    {
                        int lineTokenId = i;
                        DbPoint ptStart = pts[i];
                        DbPoint ptEnd = pts[i + 1];
                        double xGap = Math.Abs(ptEnd.x - ptStart.x);
                        double yGap = Math.Abs(ptEnd.y - ptStart.y);
                        if (xGap == 0 && yGap == 0)
                        {
                            continue;
                        }

                        int xStepNum = (int)Math.Ceiling(xGap / CD.ATOM_SPAN_LONG);//跨2.2个栅格应分析3个栅格，向上取整
                        int yStepNum = (int)Math.Ceiling(yGap / CD.ATOM_SPAN_LAT);//向上取整
                        if (xStepNum == 1 && yStepNum == 1)
                        {
                            if (xGap >= yGap)
                            {
                                xStepNum = 2;
                            }
                            else
                            {
                                yStepNum = 2;
                            }
                        }
                        float dirXbY = yStepNum > 0 ? (float)(xStepNum) / yStepNum : 9999;

                        #region X跨度大
                        if (xStepNum >= yStepNum)//X的跨度大些
                        {//from小经度to大经度 
                            double fromXX = 0;
                            double fromYY = 0;
                            double toXX = 0;
                            double toYY = 0;
                            int yFlag = 1;

                            if (ptStart.x <= ptEnd.x)
                            {
                                fromXX = ptStart.x;
                                fromYY = ptStart.y;
                                toXX = ptEnd.x;
                                toYY = ptEnd.y;
                                if (ptStart.y >= ptEnd.y)
                                {
                                    yFlag = -1;
                                }
                                else
                                {
                                    yFlag = 1;
                                }
                            }
                            else
                            {
                                fromXX = ptEnd.x;
                                fromYY = ptEnd.y;
                                toXX = ptStart.x;
                                toYY = ptStart.y;
                                if (ptEnd.y < ptStart.y)
                                {
                                    yFlag = 1;
                                }
                                else
                                {
                                    yFlag = -1;
                                }
                            }
                            bool greater = false;
                            for (int p = 0; p < xStepNum + 1; p++)//分析的点数为步长数+1
                            {
                                if (greater)
                                {
                                    break;
                                }
                                double xxMove = p * CD.ATOM_SPAN_LONG;
                                double yyMove = xxMove * yGap / xGap;

                                double xx = fromXX + xxMove;
                                double yy = fromYY + yFlag * yyMove;

                                if (p == xStepNum || xx > toXX)
                                {
                                    xx = toXX;
                                    yy = toYY;
                                    greater = true;
                                }
                                if (mapOp.CheckPointInRegion(xx, yy))
                                {//in seleceted-region
                                    int rAt, cAt;
                                    int rAt_Near, cAt_Near;
                                    GridHelper.GetIndexOfDefaultSizeGrid(xx, yy, out rAt, out cAt, out rAt_Near, out cAt_Near);

                                    InjectGridUnit cu = gridMatrix[rAt, cAt];
                                    if (cu != null && cu.repeatCount > 0)
                                    {
                                        coveredCount++;
                                        if (!coveredGridDic.ContainsKey(cu))
                                        {
                                            coveredGridDic.Add(cu, 1);
                                        }
                                        csg.AddPoint(xx, yy, true, true, lineTokenId, cu.repeatCount);
                                    }
                                    else
                                    {
                                        int repeatCount = 0;
                                        if (findNear(gridMatrix, ref repeatCount, new NearGrid(rAt, cAt, rAt_Near, cAt_Near, dirXbY, yFlag)))
                                        {
                                            coveredCount++;
                                            if (cu == null)
                                            {
                                                gridMatrix[rAt, cAt] = new InjectGridUnit();
                                                cu = gridMatrix[rAt, cAt];
                                                if (!coveredGridDic.ContainsKey(cu))
                                                {
                                                    coveredGridDic.Add(cu, 1);
                                                }
                                            }
                                            csg.AddPoint(xx, yy, true, true, lineTokenId, repeatCount);
                                        }
                                        else//周边均没有采样点
                                        {
                                            uncoveredCount++;
                                            csg.AddPoint(xx, yy, false, true, lineTokenId, 0);
                                        }
                                    }
                                }
                                else
                                {
                                    csg.AddPoint(xx, yy, false, false, lineTokenId, 0);
                                }
                            }
                        }
                        #endregion
                        #region Y跨度大
                        else //Y的跨度大些
                        {
                            double fromXX = 0;
                            double fromYY = 0;
                            double toXX = 0;
                            double toYY = 0;
                            int xFlag = 1;
                            if (ptStart.y <= ptEnd.y)
                            {
                                fromXX = ptStart.x;
                                fromYY = ptStart.y;
                                toXX = ptEnd.x;
                                toYY = ptEnd.y;
                                if (ptStart.x >= ptEnd.x)
                                {
                                    xFlag = -1;
                                }
                                else
                                {
                                    xFlag = 1;
                                }
                            }
                            else
                            {
                                fromXX = ptEnd.x;
                                fromYY = ptEnd.y;
                                toXX = ptStart.x;
                                toYY = ptStart.y;
                                if (ptEnd.x < ptStart.x)
                                {
                                    xFlag = 1;
                                }
                                else
                                {
                                    xFlag = -1;
                                }
                            }
                            bool greater = false;
                            for (int p = 0; p < yStepNum + 1; p++)//分析的点数为步长数+1
                            {
                                if (greater)
                                {
                                    break;
                                }
                                double yyMove = p * CD.ATOM_SPAN_LAT;
                                double xxMove = yyMove * xGap / yGap;

                                double yy = fromYY + yyMove;
                                double xx = fromXX + xFlag * xxMove;

                                if (p == yStepNum || yy > toYY)
                                {
                                    xx = toXX;
                                    yy = toYY;
                                    greater = true;
                                }
                                if (mapOp.CheckPointInRegion(xx, yy))
                                {
                                    int rAt, cAt;
                                    int rAt_Near, cAt_Near;
                                    GridHelper.GetIndexOfDefaultSizeGrid(xx, yy, out rAt, out cAt, out rAt_Near, out cAt_Near);

                                    InjectGridUnit cu = gridMatrix[rAt, cAt];
                                    if (cu != null && cu.repeatCount > 0)
                                    {
                                        coveredCount++;
                                        if (!coveredGridDic.ContainsKey(cu))
                                        {
                                            coveredGridDic.Add(cu, 1);
                                        }
                                        csg.AddPoint(xx, yy, true, true, lineTokenId, cu.repeatCount);
                                    }
                                    else
                                    {
                                        int repeatCount = 0;
                                        if (findNear(gridMatrix, ref repeatCount, new NearGrid(rAt, cAt, rAt_Near, cAt_Near, dirXbY, xFlag)))
                                        {
                                            coveredCount++;
                                            if (cu == null)
                                            {
                                                gridMatrix[rAt, cAt] = new InjectGridUnit();
                                                cu = gridMatrix[rAt, cAt];
                                                if (!coveredGridDic.ContainsKey(cu))
                                                {
                                                    coveredGridDic.Add(cu, 1);
                                                }
                                            }
                                            csg.AddPoint(xx, yy, true, true, lineTokenId, repeatCount);
                                        }
                                        else//周边均没有采样点
                                        {
                                            uncoveredCount++;
                                            csg.AddPoint(xx, yy, false, true, lineTokenId, 0);
                                        }
                                    }
                                }
                                else
                                {
                                    csg.AddPoint(xx, yy, false, false, lineTokenId, 0);
                                }
                            }
                        }
                        #endregion
                    }
                    #endregion
                    ijinfo.AddToCovSeg(csg.ToCovSegList());
                }
                ijinfo.countCovered = coveredCount;
                ijinfo.countUnCovered = uncoveredCount;
                ijinfo.streetCurv = roadMultiCurv;
                ijinfo.PrepareDoCalc();
                roadInjectionSet.Add(ijinfo);
            }
            return roadInjectionSet;
        }

        class NearGrid
        {
            public NearGrid(int rAt, int cAt, int rAt_Near, int cAt_Near, float dirXbY, int yFlag)
            {
                RAt = rAt;
                CAt = cAt;
                RAt_Near = rAt_Near;
                CAt_Near = cAt_Near;
                DirXbY = dirXbY;
                Flag = yFlag;
            }

            public int RAt { get; set; }
            public int CAt { get; set; }
            public int RAt_Near { get; set; }
            public int CAt_Near { get; set; }
            public float DirXbY { get; set; }
            public int Flag { get; set; }
        }

        private bool findNear(GridMatrix<InjectGridUnit> gridMatrix, ref int repeatCountRet, NearGrid nearGrid)
        {
            int maxRepeatCt = 0;
            if (nearGrid.DirXbY >= 3)//左右的，栅格上下找（保持列，行加减1)
            {
                if (gridMatrix[nearGrid.RAt + 1, nearGrid.CAt] != null)//下
                {
                    maxRepeatCt = Math.Max(maxRepeatCt, gridMatrix[nearGrid.RAt + 1, nearGrid.CAt].repeatCount);
                }
                if (gridMatrix[nearGrid.RAt - 1, nearGrid.CAt] != null)
                {
                    maxRepeatCt = Math.Max(maxRepeatCt, gridMatrix[nearGrid.RAt - 1, nearGrid.CAt].repeatCount);
                }
            }
            else if (nearGrid.DirXbY < 0.33)
            {//上下的，栅格左右找（保持行，列加减1)
                if (gridMatrix[nearGrid.RAt, nearGrid.CAt - 1] != null)//左
                {
                    maxRepeatCt = Math.Max(maxRepeatCt, gridMatrix[nearGrid.RAt, nearGrid.CAt - 1].repeatCount);
                }
                if (gridMatrix[nearGrid.RAt, nearGrid.CAt + 1] != null)//右
                {
                    maxRepeatCt = Math.Max(maxRepeatCt, gridMatrix[nearGrid.RAt, nearGrid.CAt + 1].repeatCount);
                }
            }
            else//对角方向的
            {
                maxRepeatCt = dealDiagonal(gridMatrix, nearGrid, maxRepeatCt);
            }
            repeatCountRet = maxRepeatCt;
            return maxRepeatCt > 0;
        }

        private int dealDiagonal(GridMatrix<InjectGridUnit> gridMatrix, NearGrid nearGrid, int maxRepeatCt)
        {
            if (nearGrid.Flag > 0)
            {
                //左下到右上，栅格左上和栅格右下找
                if (gridMatrix[nearGrid.RAt + 1, nearGrid.CAt - 1] != null)//左上
                {
                    maxRepeatCt = Math.Max(maxRepeatCt, gridMatrix[nearGrid.RAt + 1, nearGrid.CAt - 1].repeatCount);
                }
                if (gridMatrix[nearGrid.RAt - 1, nearGrid.CAt + 1] != null)//右下
                {
                    maxRepeatCt = Math.Max(maxRepeatCt, gridMatrix[nearGrid.RAt - 1, nearGrid.CAt + 1].repeatCount);
                }

                maxRepeatCt = dealDiagonalIssue(gridMatrix, nearGrid, maxRepeatCt);
            }
            else
            {
                //左上到右下，栅格左下和栅格右上找
                if (gridMatrix[nearGrid.RAt - 1, nearGrid.CAt - 1] != null)//左下
                {
                    maxRepeatCt = Math.Max(maxRepeatCt, gridMatrix[nearGrid.RAt - 1, nearGrid.CAt - 1].repeatCount);
                }
                if (gridMatrix[nearGrid.RAt + 1, nearGrid.CAt + 1] != null)//右上
                {
                    maxRepeatCt = Math.Max(maxRepeatCt, gridMatrix[nearGrid.RAt + 1, nearGrid.CAt + 1].repeatCount);
                }

                maxRepeatCt = dealDiagonalIssue(gridMatrix, nearGrid, maxRepeatCt);
            }

            return maxRepeatCt;
        }

        private int dealDiagonalIssue(GridMatrix<InjectGridUnit> gridMatrix, NearGrid nearGrid, int maxRepeatCt)
        {
            //避免对角问题==begin
            //左下，右上
            if (gridMatrix[nearGrid.RAt_Near, nearGrid.CAt] != null)
            {
                maxRepeatCt = Math.Max(maxRepeatCt, gridMatrix[nearGrid.RAt_Near, nearGrid.CAt].repeatCount);
            }
            if (gridMatrix[nearGrid.RAt, nearGrid.CAt_Near] != null)
            {
                maxRepeatCt = Math.Max(maxRepeatCt, gridMatrix[nearGrid.RAt, nearGrid.CAt_Near].repeatCount);
            }
            //end==
            return maxRepeatCt;
        }
    }
}
