﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    public class TableCfgManager
    {
        private static TableCfgManager instance = null;
        public static TableCfgManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new TableCfgManager();
                }
                return instance;
            }
        }

        public List<TableTemplate> Tables
        {
            get;
            set;
        }

        public TableTemplate AreaTable
        {
            get;
            private set;
        }

        private TableCfgManager()
        {
            Tables = new List<TableTemplate>();
            AreaTable = initAreaTable();
            Tables.Add(AreaTable);
            Tables.Add(initDTTable());
            Tables.Add(initPerfTable());
            Tables.Add(initInjectionTable());
            Tables.Add(initFaultTable());
            foreach (TableTemplate tb in Tables)
            {
                foreach (TableColumn col in tb.Columns)
                {
                    colDic[col.HashName] = col;
                }
            }

            TestTagSet = new List<TestTag>();
            QueryTableCfg query = new QueryTableCfg(this);
            query.Query();
        }

        private TableTemplate initAreaTable()
        {
            TableTemplate tb = new TableTemplate("区域信息", "tb_cfg_area"
                , AreaType.成分 | AreaType.大区 | AreaType.行政区 | AreaType.网格);
            tb.AddColumn("区域类型", E_VType.E_String);
            tb.AddColumn("区域名称", E_VType.E_String);
            tb.AddColumn("归属大区", E_VType.E_String);
            tb.AddColumn("归属行政区", E_VType.E_String);
            return tb;
        }

        private TableTemplate initDTTable()
        {
            TableTemplate tb = new TableTemplate("路测", "tb_kpi_dt_main_"
                , AreaType.成分 | AreaType.大区 | AreaType.行政区 | AreaType.网格);
            tb.AddColumn("testTag", E_VType.E_String);
            tb.AddColumn("移动LTE-测试里程(公里)", E_VType.E_Float);
            tb.AddColumn("移动LTE-测试时长(小时)", E_VType.E_Float);
            tb.AddColumn("移动LTE-测试车速(公里/小时)", E_VType.E_Float);
            tb.AddColumn("移动LTE-综合覆盖率_业务态(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-覆盖率_业务态(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-时长占比_业务态(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-覆盖率_RSRP>-110AndSINR>-3(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-RSRP>-110dbm占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-平均RSRP(dBm)", E_VType.E_Float);
            tb.AddColumn("移动LTE-SINR>-3db占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-平均SINR(dB)", E_VType.E_Float);
            tb.AddColumn("移动LTE-SINR>0dB占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-SINR>12dB占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-综合覆盖率_空闲态(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-覆盖率_空闲态(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-时长占比_空闲态(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-连续SINR质差里程占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-连续弱覆盖里程占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-下载速率含掉线(Mbps)", E_VType.E_Float);
            tb.AddColumn("移动LTE-上传速率含掉线(Mbps)", E_VType.E_Float);
            tb.AddColumn("移动LTE-下载速率不含掉线(Mbps)", E_VType.E_Float);
            tb.AddColumn("移动LTE-上传速率不含掉线(Mbps)", E_VType.E_Float);
            tb.AddColumn("移动LTE-下载速率2M以下占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-下载速率10M以上占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-下载掉线率(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-上传掉线率(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-FTP下载速率(Mbps)", E_VType.E_Float);
            tb.AddColumn("移动LTE-HTTP浏览速率(Mbps)", E_VType.E_Float);
            tb.AddColumn("移动LTE-HTTP下载速率(Mbps)", E_VType.E_Float);
            tb.AddColumn("移动LTE-视频业务速率(Mbps)", E_VType.E_Float);
            tb.AddColumn("移动LTE-FTP上传速率(Mbps)", E_VType.E_Float);
            tb.AddColumn("移动LTE-CSFB全成功率(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-CSFB回落GSM成功率(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-CSFB呼叫时延(秒)", E_VType.E_Float);
            tb.AddColumn("移动LTE-高重叠占比≥3(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-D频段时长占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-F频段时长占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-E频段时长占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-网内切换尝试总次数(次)", E_VType.E_Float);
            tb.AddColumn("移动LTE-网内切换成功总次数(次)", E_VType.E_Float);
            tb.AddColumn("移动LTE-网内切换成功率(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-同频切换尝试总次数(次)", E_VType.E_Float);
            tb.AddColumn("移动LTE-异频切换尝试总次数(次)", E_VType.E_Float);
            tb.AddColumn("移动LTE-同频切换比例(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-异频切换比例(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-每公里切换次数(次/公里)", E_VType.E_Float);
            tb.AddColumn("移动LTE-每分钟切换次数(次/分钟)", E_VType.E_Float);
            tb.AddColumn("移动LTE-LTE向TD重定向尝试次数(次)", E_VType.E_Float);
            tb.AddColumn("移动LTE-LTE向GSM重定向尝试次数(次)", E_VType.E_Float);
            tb.AddColumn("移动LTE-低速率路段数(条)", E_VType.E_Float);
            tb.AddColumn("移动LTE-下行平均每时隙调度PRB个数(个)", E_VType.E_Float);
            tb.AddColumn("移动LTE-下行子帧调度率(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-传输模式TM=2时长占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-传输模式TM=3时长占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-传输模式TM=8时长占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-FTP下载速率分段[0,1)占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-FTP下载速率分段[1,10)占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-FTP下载速率分段[10,20)占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-FTP下载速率分段[20,25)占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-FTP下载速率分段[25,30)占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-FTP下载速率分段[30,35)占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-FTP下载速率分段[35,40)占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-FTP下载速率分段[30,40)占比(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-FTP下载速率分段>40M占比(%)", E_VType.E_Float);
            tb.AddColumn("移动G/T-全程成功率(gsm83%+td17%)(%)", E_VType.E_Float);
            tb.AddColumn("移动2G-GSM全程成功率(%)", E_VType.E_Float);
            tb.AddColumn("移动2G-GSM话音质量(%)", E_VType.E_Float);
            tb.AddColumn("移动2G-采样点≥-80dbm占比(%)", E_VType.E_Float);
            tb.AddColumn("移动2G-采样点≥-85dbm占比(%)", E_VType.E_Float);
            tb.AddColumn("移动2G-采样点≥-90dbm占比(%)", E_VType.E_Float);
            tb.AddColumn("移动2G-采样点≥-94dbm占比(%)", E_VType.E_Float);
            tb.AddColumn("移动3G-TD全程成功率(%)", E_VType.E_Float);
            tb.AddColumn("联通LTE-综合覆盖率(%)", E_VType.E_Float);
            tb.AddColumn("联通LTE-覆盖率(%)", E_VType.E_Float);
            tb.AddColumn("联通LTE-时长占比(%)", E_VType.E_Float);
            tb.AddColumn("联通LTE-RSRP>-110dbm占比(%)", E_VType.E_Float);
            tb.AddColumn("联通LTE-SINR>-3dB占比(%)", E_VType.E_Float);
            tb.AddColumn("联通LTE-FTP下载速率(Mbps)", E_VType.E_Float);
            tb.AddColumn("联通LTE-HTTP浏览速率(Mbps)", E_VType.E_Float);
            tb.AddColumn("联通LTE-HTTP下载速率(Mbps)", E_VType.E_Float);
            tb.AddColumn("联通LTE-视频业务速率(Mbps)", E_VType.E_Float);
            tb.AddColumn("联通LTE-FTP上传速率(Mbps)", E_VType.E_Float);
            tb.AddColumn("联通G/W-全程成功率(%)", E_VType.E_Float);
            tb.AddColumn("电信LTE-综合覆盖率(%)", E_VType.E_Float);
            tb.AddColumn("电信LTE-覆盖率(%)", E_VType.E_Float);
            tb.AddColumn("电信LTE-时长占比(%)", E_VType.E_Float);
            tb.AddColumn("电信LTE-RSRP>-110dbm占比(%)", E_VType.E_Float);
            tb.AddColumn("电信LTE-SINR>-3dB占比(%)", E_VType.E_Float);
            tb.AddColumn("电信LTE-FTP下载速率(Mbps)", E_VType.E_Float);
            tb.AddColumn("电信LTE-HTTP浏览速率(Mbps)", E_VType.E_Float);
            tb.AddColumn("电信LTE-HTTP下载速率(Mbps)", E_VType.E_Float);
            tb.AddColumn("电信LTE-视频业务速率(Mbps)", E_VType.E_Float);
            tb.AddColumn("电信LTE-FTP上传速率(Mbps)", E_VType.E_Float);
            tb.AddColumn("电信CDMA-全程成功率(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE扫频-高重叠占比D+F(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE扫频-高重叠占比单D(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE扫频-高重叠占比单F(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE扫频-覆盖率>-110dBm(%)", E_VType.E_Float);
            tb.AddColumn("联通LTE扫频-覆盖率>-110dBm(%)", E_VType.E_Float);
            tb.AddColumn("电信LTE扫频-覆盖率>-110dBm(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE扫频-平均电平(dBm)", E_VType.E_Float);
            tb.AddColumn("联通LTE扫频-平均电平(dBm)", E_VType.E_Float);
            tb.AddColumn("电信LTE扫频-平均电平(dBm)", E_VType.E_Float);
            return tb;
        }

        private TableTemplate initPerfTable()
        {
            TableTemplate tb = new TableTemplate("性能", "tb_kpi_dt_perf_", AreaType.网格);
            tb.AddColumn("testTag", E_VType.E_String);
            tb.AddColumn("移动LTE-下行PRB平均利用率(%)", E_VType.E_Float);
            tb.AddColumn("移动LTE-小区内平均用户数(个)", E_VType.E_Float);
            tb.AddColumn("移动LTE-需扩容小区数(个)", E_VType.E_Float);
            return tb;
        }

        private TableTemplate initInjectionTable()
        {
            TableTemplate tb = new TableTemplate("渗透率", "tb_kpi_dt_roadInjection_", AreaType.网格);
            tb.AddColumn("testTag", E_VType.E_String);
            tb.AddColumn("面积(km²)", E_VType.E_Float);
            tb.AddColumn("指定道路里程(公里)", E_VType.E_Float);
            tb.AddColumn("指定道路测试里程(公里)", E_VType.E_Float);
            tb.AddColumn("渗透率(%)", E_VType.E_Float);
            tb.AddColumn("变异系数", E_VType.E_Float);
            tb.AddColumn("重复测试距离(公里)", E_VType.E_Float);
            tb.AddColumn("重复率(%)", E_VType.E_Float);
            tb.AddColumn("未测试距离(公里)", E_VType.E_Float);
            tb.AddColumn("未测试率(%)", E_VType.E_Float);
            tb.AddColumn("栅格总里程(公里)", E_VType.E_Float);
            tb.AddColumn("渗透在道路上的栅格里程(公里)", E_VType.E_Float);
            tb.AddColumn("未渗透在道路上的栅格里程(公里)", E_VType.E_Float);
            tb.AddColumn("里程渗透率(%)", E_VType.E_Float);
            tb.AddColumn("(道路渗透距离+非渗透里程)/(道路总距离+非渗透里程)(%)", E_VType.E_Float);
            return tb;
        }

        private TableTemplate initFaultTable()
        {
            TableTemplate tb = new TableTemplate("网格分析结论", "tb_kpi_dt_fault_", AreaType.网格);
            tb.AddColumn("testTag", E_VType.E_String);
            tb.AddColumn("分析结论", E_VType.E_String);
            return tb;
        }

        public List<TestTag> TestTagSet
        {
            get;
            private set;
        }

        readonly Dictionary<string, TableColumn> colDic = new Dictionary<string, TableColumn>();
        internal TableColumn GetTableColumn(string colHashName)
        {
            TableColumn col = null;
            colDic.TryGetValue(colHashName, out col);
            return col;
        }

        readonly Dictionary<string, AreaInfo> areaDic = new Dictionary<string, AreaInfo>();
        internal void AddAreaInfo(AreaInfo areaInfo)
        {
            areaDic[areaInfo.Key] = areaInfo;
        }

        public AreaInfo GetArea(int cityID, string areaType, string areaName)
        {
            AreaInfo ai = null;
            areaDic.TryGetValue(string.Format("{0}&{1}&{2}", cityID, areaType, areaName), out ai);
            return ai;
        }

        Dictionary<string, TestTag> testTagDic = null;
        internal TestTag GetTestTag(string testTag)
        {
            if (testTagDic==null)
            {
                testTagDic = new Dictionary<string, TestTag>();
                foreach (TestTag tt in TestTagSet)
                {
                    testTagDic[tt.Name] = tt;
                }
            }
            TestTag ret;
            testTagDic.TryGetValue(testTag, out ret);
            return ret;
        }
    }

    [Flags]
    public enum AreaType
    {
        网格 = 1,
        成分 = 2,
        大区 = 4,
        行政区 = 8
    }

}
