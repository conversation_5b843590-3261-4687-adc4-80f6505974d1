﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CallDelayAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CallDelayAnaListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.miCompareReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewCallEvent = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMO = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMT = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCallAttempt = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAlerting = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDelay = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSignalName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSignalTime = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSignalTimeEvent = new BrightIdeasSoftware.OLVColumn();
            this.tabControlCallEvent = new System.Windows.Forms.TabControl();
            this.tabPageCallEvent = new System.Windows.Forms.TabPage();
            this.dETime = new System.Windows.Forms.DateTimePicker();
            this.dSTime = new System.Windows.Forms.DateTimePicker();
            this.btnUpata = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.txtMsgName = new System.Windows.Forms.TextBox();
            this.tabPageCallDelay = new System.Windows.Forms.TabPage();
            this.ListViewCallDelay = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMO2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMT2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCallType = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSignalType = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnPrevSignal = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAfterSignal = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSignalDelay = new BrightIdeasSoftware.OLVColumn();
            this.tabPageCallModel = new System.Windows.Forms.TabPage();
            this.ListViewCallModel = new BrightIdeasSoftware.TreeListView();
            this.olvColumnModelSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAvgDelay = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSignalSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSignal = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSignalAvgDelay = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn3 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn4 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn5 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn6 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn7 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn8 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn9 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn10 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn11 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn12 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn13 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn14 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn15 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn16 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn17 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn18 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn19 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumn20 = new BrightIdeasSoftware.OLVColumn();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCallEvent)).BeginInit();
            this.tabControlCallEvent.SuspendLayout();
            this.tabPageCallEvent.SuspendLayout();
            this.tabPageCallDelay.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCallDelay)).BeginInit();
            this.tabPageCallModel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCallModel)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay,
            this.miCompareReplay,
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 120);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(129, 22);
            this.miReplay.Text = "回放";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // miCompareReplay
            // 
            this.miCompareReplay.Name = "miCompareReplay";
            this.miCompareReplay.Size = new System.Drawing.Size(129, 22);
            this.miCompareReplay.Text = "对比回放";
            this.miCompareReplay.Click += new System.EventHandler(this.miCompareReplay_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(126, 6);
            this.toolStripMenuItem1.Visible = false;
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(129, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // ListViewCallEvent
            // 
            this.ListViewCallEvent.AllColumns.Add(this.olvColumnSN);
            this.ListViewCallEvent.AllColumns.Add(this.olvColumnMO);
            this.ListViewCallEvent.AllColumns.Add(this.olvColumnMT);
            this.ListViewCallEvent.AllColumns.Add(this.olvColumnCallAttempt);
            this.ListViewCallEvent.AllColumns.Add(this.olvColumnAlerting);
            this.ListViewCallEvent.AllColumns.Add(this.olvColumnDelay);
            this.ListViewCallEvent.AllColumns.Add(this.olvColumnLongitude);
            this.ListViewCallEvent.AllColumns.Add(this.olvColumnLatitude);
            this.ListViewCallEvent.AllColumns.Add(this.olvColumnSignalName);
            this.ListViewCallEvent.AllColumns.Add(this.olvColumnSignalTime);
            this.ListViewCallEvent.AllColumns.Add(this.olvColumnSignalTimeEvent);
            this.ListViewCallEvent.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.ListViewCallEvent.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnMO,
            this.olvColumnMT,
            this.olvColumnCallAttempt,
            this.olvColumnAlerting,
            this.olvColumnDelay,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnSignalName,
            this.olvColumnSignalTime,
            this.olvColumnSignalTimeEvent});
            this.ListViewCallEvent.ContextMenuStrip = this.ctxMenu;
            this.ListViewCallEvent.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewCallEvent.FullRowSelect = true;
            this.ListViewCallEvent.GridLines = true;
            this.ListViewCallEvent.HeaderWordWrap = true;
            this.ListViewCallEvent.IsNeedShowOverlay = false;
            this.ListViewCallEvent.Location = new System.Drawing.Point(6, 28);
            this.ListViewCallEvent.Name = "ListViewCallEvent";
            this.ListViewCallEvent.OwnerDraw = true;
            this.ListViewCallEvent.ShowGroups = false;
            this.ListViewCallEvent.Size = new System.Drawing.Size(1077, 429);
            this.ListViewCallEvent.TabIndex = 5;
            this.ListViewCallEvent.UseCompatibleStateImageBehavior = false;
            this.ListViewCallEvent.View = System.Windows.Forms.View.Details;
            this.ListViewCallEvent.VirtualMode = true;
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnMO
            // 
            this.olvColumnMO.HeaderFont = null;
            this.olvColumnMO.Text = "主叫";
            this.olvColumnMO.Width = 70;
            // 
            // olvColumnMT
            // 
            this.olvColumnMT.HeaderFont = null;
            this.olvColumnMT.Text = "被叫";
            this.olvColumnMT.Width = 70;
            // 
            // olvColumnCallAttempt
            // 
            this.olvColumnCallAttempt.HeaderFont = null;
            this.olvColumnCallAttempt.Text = "起呼时间";
            this.olvColumnCallAttempt.Width = 120;
            // 
            // olvColumnAlerting
            // 
            this.olvColumnAlerting.HeaderFont = null;
            this.olvColumnAlerting.Text = "振铃时间";
            this.olvColumnAlerting.Width = 120;
            // 
            // olvColumnDelay
            // 
            this.olvColumnDelay.HeaderFont = null;
            this.olvColumnDelay.Text = "接续时长（毫秒）";
            this.olvColumnDelay.Width = 110;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 80;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 80;
            // 
            // olvColumnSignalName
            // 
            this.olvColumnSignalName.HeaderFont = null;
            this.olvColumnSignalName.Text = "信令名称";
            this.olvColumnSignalName.Width = 150;
            // 
            // olvColumnSignalTime
            // 
            this.olvColumnSignalTime.HeaderFont = null;
            this.olvColumnSignalTime.Text = "信令时间";
            this.olvColumnSignalTime.Width = 150;
            // 
            // olvColumnSignalTimeEvent
            // 
            this.olvColumnSignalTimeEvent.HeaderFont = null;
            this.olvColumnSignalTimeEvent.Text = "时延(毫秒)";
            this.olvColumnSignalTimeEvent.Width = 80;
            // 
            // tabControlCallEvent
            // 
            this.tabControlCallEvent.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.tabControlCallEvent.Controls.Add(this.tabPageCallEvent);
            this.tabControlCallEvent.Controls.Add(this.tabPageCallDelay);
            this.tabControlCallEvent.Controls.Add(this.tabPageCallModel);
            this.tabControlCallEvent.Location = new System.Drawing.Point(-6, 0);
            this.tabControlCallEvent.Name = "tabControlCallEvent";
            this.tabControlCallEvent.SelectedIndex = 0;
            this.tabControlCallEvent.Size = new System.Drawing.Size(1097, 490);
            this.tabControlCallEvent.TabIndex = 6;
            this.tabControlCallEvent.SelectedIndexChanged += new System.EventHandler(this.tabControlCallEvent_SelectedIndexChanged);
            // 
            // tabPageCallEvent
            // 
            this.tabPageCallEvent.Controls.Add(this.dETime);
            this.tabPageCallEvent.Controls.Add(this.dSTime);
            this.tabPageCallEvent.Controls.Add(this.btnUpata);
            this.tabPageCallEvent.Controls.Add(this.label2);
            this.tabPageCallEvent.Controls.Add(this.label1);
            this.tabPageCallEvent.Controls.Add(this.txtMsgName);
            this.tabPageCallEvent.Controls.Add(this.ListViewCallEvent);
            this.tabPageCallEvent.Location = new System.Drawing.Point(4, 23);
            this.tabPageCallEvent.Name = "tabPageCallEvent";
            this.tabPageCallEvent.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageCallEvent.Size = new System.Drawing.Size(1089, 463);
            this.tabPageCallEvent.TabIndex = 0;
            this.tabPageCallEvent.Text = "接续事件";
            this.tabPageCallEvent.UseVisualStyleBackColor = true;
            // 
            // dETime
            // 
            this.dETime.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            this.dETime.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dETime.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.dETime.Location = new System.Drawing.Point(681, 2);
            this.dETime.Name = "dETime";
            this.dETime.Size = new System.Drawing.Size(149, 21);
            this.dETime.TabIndex = 14;
            // 
            // dSTime
            // 
            this.dSTime.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            this.dSTime.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dSTime.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.dSTime.Location = new System.Drawing.Point(424, 2);
            this.dSTime.Name = "dSTime";
            this.dSTime.Size = new System.Drawing.Size(160, 21);
            this.dSTime.TabIndex = 13;
            // 
            // btnUpata
            // 
            this.btnUpata.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnUpata.Location = new System.Drawing.Point(842, 1);
            this.btnUpata.Name = "btnUpata";
            this.btnUpata.Size = new System.Drawing.Size(75, 23);
            this.btnUpata.TabIndex = 12;
            this.btnUpata.Text = "筛选";
            this.btnUpata.UseVisualStyleBackColor = true;
            this.btnUpata.Click += new System.EventHandler(this.btnUpata_Click);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.Location = new System.Drawing.Point(592, 8);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(77, 12);
            this.label2.TabIndex = 10;
            this.label2.Text = "≤信令时间≤";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(137, 6);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(89, 12);
            this.label1.TabIndex = 7;
            this.label1.Text = "信令名称(包含)";
            // 
            // txtMsgName
            // 
            this.txtMsgName.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtMsgName.Location = new System.Drawing.Point(232, 2);
            this.txtMsgName.Name = "txtMsgName";
            this.txtMsgName.Size = new System.Drawing.Size(166, 21);
            this.txtMsgName.TabIndex = 6;
            // 
            // tabPageCallDelay
            // 
            this.tabPageCallDelay.Controls.Add(this.ListViewCallDelay);
            this.tabPageCallDelay.Location = new System.Drawing.Point(4, 23);
            this.tabPageCallDelay.Name = "tabPageCallDelay";
            this.tabPageCallDelay.Size = new System.Drawing.Size(1089, 463);
            this.tabPageCallDelay.TabIndex = 2;
            this.tabPageCallDelay.Text = "接续时延";
            this.tabPageCallDelay.UseVisualStyleBackColor = true;
            // 
            // ListViewCallDelay
            // 
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnSN2);
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnMO2);
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnMT2);
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnCallType);
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnSignalType);
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnPrevSignal);
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnAfterSignal);
            this.ListViewCallDelay.AllColumns.Add(this.olvColumnSignalDelay);
            this.ListViewCallDelay.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.ListViewCallDelay.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN2,
            this.olvColumnMO2,
            this.olvColumnMT2,
            this.olvColumnCallType,
            this.olvColumnSignalType,
            this.olvColumnPrevSignal,
            this.olvColumnAfterSignal,
            this.olvColumnSignalDelay});
            this.ListViewCallDelay.ContextMenuStrip = this.ctxMenu;
            this.ListViewCallDelay.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewCallDelay.FullRowSelect = true;
            this.ListViewCallDelay.GridLines = true;
            this.ListViewCallDelay.HeaderWordWrap = true;
            this.ListViewCallDelay.IsNeedShowOverlay = false;
            this.ListViewCallDelay.Location = new System.Drawing.Point(6, 4);
            this.ListViewCallDelay.Name = "ListViewCallDelay";
            this.ListViewCallDelay.OwnerDraw = true;
            this.ListViewCallDelay.ShowGroups = false;
            this.ListViewCallDelay.Size = new System.Drawing.Size(1077, 454);
            this.ListViewCallDelay.TabIndex = 6;
            this.ListViewCallDelay.UseCompatibleStateImageBehavior = false;
            this.ListViewCallDelay.View = System.Windows.Forms.View.Details;
            this.ListViewCallDelay.VirtualMode = true;
            // 
            // olvColumnSN2
            // 
            this.olvColumnSN2.AspectName = "";
            this.olvColumnSN2.HeaderFont = null;
            this.olvColumnSN2.Text = "序号";
            // 
            // olvColumnMO2
            // 
            this.olvColumnMO2.HeaderFont = null;
            this.olvColumnMO2.Text = "主叫";
            this.olvColumnMO2.Width = 100;
            // 
            // olvColumnMT2
            // 
            this.olvColumnMT2.HeaderFont = null;
            this.olvColumnMT2.Text = "被叫";
            this.olvColumnMT2.Width = 100;
            // 
            // olvColumnCallType
            // 
            this.olvColumnCallType.HeaderFont = null;
            this.olvColumnCallType.Text = "类别";
            // 
            // olvColumnSignalType
            // 
            this.olvColumnSignalType.HeaderFont = null;
            this.olvColumnSignalType.Text = "描述";
            this.olvColumnSignalType.Width = 150;
            // 
            // olvColumnPrevSignal
            // 
            this.olvColumnPrevSignal.HeaderFont = null;
            this.olvColumnPrevSignal.Text = "前一信令";
            this.olvColumnPrevSignal.Width = 250;
            // 
            // olvColumnAfterSignal
            // 
            this.olvColumnAfterSignal.HeaderFont = null;
            this.olvColumnAfterSignal.Text = "后一信令";
            this.olvColumnAfterSignal.Width = 250;
            // 
            // olvColumnSignalDelay
            // 
            this.olvColumnSignalDelay.HeaderFont = null;
            this.olvColumnSignalDelay.Text = "时延（毫秒）";
            this.olvColumnSignalDelay.Width = 80;
            // 
            // tabPageCallModel
            // 
            this.tabPageCallModel.Controls.Add(this.ListViewCallModel);
            this.tabPageCallModel.Location = new System.Drawing.Point(4, 23);
            this.tabPageCallModel.Name = "tabPageCallModel";
            this.tabPageCallModel.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageCallModel.Size = new System.Drawing.Size(1089, 463);
            this.tabPageCallModel.TabIndex = 1;
            this.tabPageCallModel.Text = "接续模型";
            this.tabPageCallModel.UseVisualStyleBackColor = true;
            // 
            // ListViewCallModel
            // 
            this.ListViewCallModel.AllColumns.Add(this.olvColumnModelSN);
            this.ListViewCallModel.AllColumns.Add(this.olvColumnRate);
            this.ListViewCallModel.AllColumns.Add(this.olvColumnAvgDelay);
            this.ListViewCallModel.AllColumns.Add(this.olvColumnSignalSN);
            this.ListViewCallModel.AllColumns.Add(this.olvColumnSignal);
            this.ListViewCallModel.AllColumns.Add(this.olvColumnSignalAvgDelay);
            this.ListViewCallModel.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.ListViewCallModel.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnModelSN,
            this.olvColumnRate,
            this.olvColumnAvgDelay,
            this.olvColumnSignalSN,
            this.olvColumnSignal,
            this.olvColumnSignalAvgDelay});
            this.ListViewCallModel.ContextMenuStrip = this.ctxMenu;
            this.ListViewCallModel.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewCallModel.FullRowSelect = true;
            this.ListViewCallModel.GridLines = true;
            this.ListViewCallModel.HeaderWordWrap = true;
            this.ListViewCallModel.IsNeedShowOverlay = false;
            this.ListViewCallModel.Location = new System.Drawing.Point(6, 3);
            this.ListViewCallModel.Name = "ListViewCallModel";
            this.ListViewCallModel.OwnerDraw = true;
            this.ListViewCallModel.ShowGroups = false;
            this.ListViewCallModel.Size = new System.Drawing.Size(1077, 454);
            this.ListViewCallModel.TabIndex = 6;
            this.ListViewCallModel.UseCompatibleStateImageBehavior = false;
            this.ListViewCallModel.View = System.Windows.Forms.View.Details;
            this.ListViewCallModel.VirtualMode = true;
            // 
            // olvColumnModelSN
            // 
            this.olvColumnModelSN.AspectName = "";
            this.olvColumnModelSN.HeaderFont = null;
            this.olvColumnModelSN.Text = "模型序号";
            // 
            // olvColumnRate
            // 
            this.olvColumnRate.HeaderFont = null;
            this.olvColumnRate.Text = "比例";
            this.olvColumnRate.Width = 100;
            // 
            // olvColumnAvgDelay
            // 
            this.olvColumnAvgDelay.HeaderFont = null;
            this.olvColumnAvgDelay.Text = "时延平均值（毫秒）";
            this.olvColumnAvgDelay.Width = 120;
            // 
            // olvColumnSignalSN
            // 
            this.olvColumnSignalSN.HeaderFont = null;
            this.olvColumnSignalSN.Text = "信令序号";
            // 
            // olvColumnSignal
            // 
            this.olvColumnSignal.HeaderFont = null;
            this.olvColumnSignal.Text = "信令";
            this.olvColumnSignal.Width = 400;
            // 
            // olvColumnSignalAvgDelay
            // 
            this.olvColumnSignalAvgDelay.HeaderFont = null;
            this.olvColumnSignalAvgDelay.Text = "信令时延平均值（毫秒）";
            this.olvColumnSignalAvgDelay.Width = 200;
            // 
            // olvColumn1
            // 
            this.olvColumn1.AspectName = "";
            this.olvColumn1.HeaderFont = null;
            this.olvColumn1.Text = "序号";
            // 
            // olvColumn2
            // 
            this.olvColumn2.HeaderFont = null;
            this.olvColumn2.Text = "主叫";
            this.olvColumn2.Width = 100;
            // 
            // olvColumn3
            // 
            this.olvColumn3.HeaderFont = null;
            this.olvColumn3.Text = "被叫";
            this.olvColumn3.Width = 100;
            // 
            // olvColumn4
            // 
            this.olvColumn4.HeaderFont = null;
            this.olvColumn4.Text = "起呼时间";
            this.olvColumn4.Width = 200;
            // 
            // olvColumn5
            // 
            this.olvColumn5.HeaderFont = null;
            this.olvColumn5.Text = "振铃时间";
            this.olvColumn5.Width = 200;
            // 
            // olvColumn6
            // 
            this.olvColumn6.HeaderFont = null;
            this.olvColumn6.Text = "接续时长（毫秒）";
            this.olvColumn6.Width = 110;
            // 
            // olvColumn7
            // 
            this.olvColumn7.HeaderFont = null;
            this.olvColumn7.Text = "经度";
            this.olvColumn7.Width = 80;
            // 
            // olvColumn8
            // 
            this.olvColumn8.HeaderFont = null;
            this.olvColumn8.Text = "纬度";
            this.olvColumn8.Width = 80;
            // 
            // olvColumn9
            // 
            this.olvColumn9.HeaderFont = null;
            this.olvColumn9.Text = "信令名称";
            this.olvColumn9.Width = 200;
            // 
            // olvColumn10
            // 
            this.olvColumn10.HeaderFont = null;
            this.olvColumn10.Text = "信令时间";
            this.olvColumn10.Width = 200;
            // 
            // olvColumn11
            // 
            this.olvColumn11.AspectName = "";
            this.olvColumn11.HeaderFont = null;
            this.olvColumn11.Text = "序号";
            // 
            // olvColumn12
            // 
            this.olvColumn12.HeaderFont = null;
            this.olvColumn12.Text = "主叫";
            this.olvColumn12.Width = 100;
            // 
            // olvColumn13
            // 
            this.olvColumn13.HeaderFont = null;
            this.olvColumn13.Text = "被叫";
            this.olvColumn13.Width = 100;
            // 
            // olvColumn14
            // 
            this.olvColumn14.HeaderFont = null;
            this.olvColumn14.Text = "起呼时间";
            this.olvColumn14.Width = 200;
            // 
            // olvColumn15
            // 
            this.olvColumn15.HeaderFont = null;
            this.olvColumn15.Text = "振铃时间";
            this.olvColumn15.Width = 200;
            // 
            // olvColumn16
            // 
            this.olvColumn16.HeaderFont = null;
            this.olvColumn16.Text = "接续时长（毫秒）";
            this.olvColumn16.Width = 110;
            // 
            // olvColumn17
            // 
            this.olvColumn17.HeaderFont = null;
            this.olvColumn17.Text = "经度";
            this.olvColumn17.Width = 80;
            // 
            // olvColumn18
            // 
            this.olvColumn18.HeaderFont = null;
            this.olvColumn18.Text = "纬度";
            this.olvColumn18.Width = 80;
            // 
            // olvColumn19
            // 
            this.olvColumn19.HeaderFont = null;
            this.olvColumn19.Text = "信令名称";
            this.olvColumn19.Width = 200;
            // 
            // olvColumn20
            // 
            this.olvColumn20.HeaderFont = null;
            this.olvColumn20.Text = "信令时间";
            this.olvColumn20.Width = 200;
            // 
            // CallDelayAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1103, 502);
            this.Controls.Add(this.tabControlCallEvent);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "CallDelayAnaListForm";
            this.Text = "呼叫接续分析";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCallEvent)).EndInit();
            this.tabControlCallEvent.ResumeLayout(false);
            this.tabPageCallEvent.ResumeLayout(false);
            this.tabPageCallEvent.PerformLayout();
            this.tabPageCallDelay.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCallDelay)).EndInit();
            this.tabPageCallModel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCallModel)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private BrightIdeasSoftware.TreeListView ListViewCallEvent;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnMT;
        private BrightIdeasSoftware.OLVColumn olvColumnMO;
        private BrightIdeasSoftware.OLVColumn olvColumnCallAttempt;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnDelay;
        private BrightIdeasSoftware.OLVColumn olvColumnAlerting;
        private BrightIdeasSoftware.OLVColumn olvColumnSignalName;
        private BrightIdeasSoftware.OLVColumn olvColumnSignalTime;
        private System.Windows.Forms.TabControl tabControlCallEvent;
        private System.Windows.Forms.TabPage tabPageCallEvent;
        private System.Windows.Forms.TabPage tabPageCallModel;
        private BrightIdeasSoftware.TreeListView ListViewCallModel;
        private BrightIdeasSoftware.OLVColumn olvColumnModelSN;
        private BrightIdeasSoftware.OLVColumn olvColumnRate;
        private BrightIdeasSoftware.OLVColumn olvColumn1;
        private BrightIdeasSoftware.OLVColumn olvColumn2;
        private BrightIdeasSoftware.OLVColumn olvColumn3;
        private BrightIdeasSoftware.OLVColumn olvColumn4;
        private BrightIdeasSoftware.OLVColumn olvColumn5;
        private BrightIdeasSoftware.OLVColumn olvColumn6;
        private BrightIdeasSoftware.OLVColumn olvColumn7;
        private BrightIdeasSoftware.OLVColumn olvColumn8;
        private BrightIdeasSoftware.OLVColumn olvColumn9;
        private BrightIdeasSoftware.OLVColumn olvColumn10;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgDelay;
        private BrightIdeasSoftware.OLVColumn olvColumnSignal;
        private BrightIdeasSoftware.OLVColumn olvColumnSignalAvgDelay;
        private BrightIdeasSoftware.OLVColumn olvColumnSignalSN;
        private System.Windows.Forms.TabPage tabPageCallDelay;
        private BrightIdeasSoftware.TreeListView ListViewCallDelay;
        private BrightIdeasSoftware.OLVColumn olvColumnSN2;
        private BrightIdeasSoftware.OLVColumn olvColumnMO2;
        private BrightIdeasSoftware.OLVColumn olvColumnMT2;
        private BrightIdeasSoftware.OLVColumn olvColumnCallType;
        private BrightIdeasSoftware.OLVColumn olvColumnSignalType;
        private BrightIdeasSoftware.OLVColumn olvColumnPrevSignal;
        private BrightIdeasSoftware.OLVColumn olvColumnAfterSignal;
        private BrightIdeasSoftware.OLVColumn olvColumnSignalDelay;
        private BrightIdeasSoftware.OLVColumn olvColumn11;
        private BrightIdeasSoftware.OLVColumn olvColumn12;
        private BrightIdeasSoftware.OLVColumn olvColumn13;
        private BrightIdeasSoftware.OLVColumn olvColumn14;
        private BrightIdeasSoftware.OLVColumn olvColumn15;
        private BrightIdeasSoftware.OLVColumn olvColumn16;
        private BrightIdeasSoftware.OLVColumn olvColumn17;
        private BrightIdeasSoftware.OLVColumn olvColumn18;
        private BrightIdeasSoftware.OLVColumn olvColumn19;
        private BrightIdeasSoftware.OLVColumn olvColumn20;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private System.Windows.Forms.ToolStripMenuItem miCompareReplay;
        private BrightIdeasSoftware.OLVColumn olvColumnSignalTimeEvent;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox txtMsgName;
        private System.Windows.Forms.Button btnUpata;
        private System.Windows.Forms.DateTimePicker dETime;
        private System.Windows.Forms.DateTimePicker dSTime;

    }
}