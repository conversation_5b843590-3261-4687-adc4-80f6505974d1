﻿namespace MasterCom.RAMS.Model.Interface
{
    partial class SetHaveNoMainCellFilterDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.numRxlevMin = new System.Windows.Forms.NumericUpDown();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.numRadius = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.numSampleCountLimit = new System.Windows.Forms.NumericUpDown();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.numericUpDownDistance = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.numCellCountLimit = new System.Windows.Forms.NumericUpDown();
            this.numRxLevDValue = new System.Windows.Forms.NumericUpDown();
            this.txtHelp = new System.Windows.Forms.TextBox();
            this.btnHelp = new System.Windows.Forms.Button();
            this.numRxLevMax = new System.Windows.Forms.NumericUpDown();
            this.numHandoverTimeOffset = new System.Windows.Forms.NumericUpDown();
            this.label9 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.numSampleRxQuality = new System.Windows.Forms.NumericUpDown();
            this.label15 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.numRxQuality = new System.Windows.Forms.NumericUpDown();
            this.label14 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numRxlevMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRadius)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCountLimit)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCellCountLimit)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHandoverTimeOffset)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleRxQuality)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRxQuality)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(90, 30);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 10;
            this.label1.Text = "≤信号强度≤";
            // 
            // numRxlevMin
            // 
            this.numRxlevMin.Location = new System.Drawing.Point(29, 26);
            this.numRxlevMin.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numRxlevMin.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRxlevMin.Name = "numRxlevMin";
            this.numRxlevMin.Size = new System.Drawing.Size(55, 21);
            this.numRxlevMin.TabIndex = 3;
            this.numRxlevMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxlevMin.Value = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(392, 273);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(298, 273);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(25, 60);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 11;
            this.label2.Text = "汇聚半径≤";
            // 
            // numRadius
            // 
            this.numRadius.Increment = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numRadius.Location = new System.Drawing.Point(94, 56);
            this.numRadius.Maximum = new decimal(new int[] {
            500,
            0,
            0,
            0});
            this.numRadius.Minimum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numRadius.Name = "numRadius";
            this.numRadius.Size = new System.Drawing.Size(55, 21);
            this.numRadius.TabIndex = 6;
            this.numRadius.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRadius.Value = new decimal(new int[] {
            70,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(154, 60);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(17, 12);
            this.label3.TabIndex = 13;
            this.label3.Text = "米";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(25, 91);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 14;
            this.label4.Text = "采样点数≥";
            // 
            // numSampleCountLimit
            // 
            this.numSampleCountLimit.Location = new System.Drawing.Point(94, 87);
            this.numSampleCountLimit.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSampleCountLimit.Name = "numSampleCountLimit";
            this.numSampleCountLimit.Size = new System.Drawing.Size(55, 21);
            this.numSampleCountLimit.TabIndex = 7;
            this.numSampleCountLimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSampleCountLimit.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(346, 59);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(17, 12);
            this.label6.TabIndex = 13;
            this.label6.Text = "米";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(204, 60);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(77, 12);
            this.label5.TabIndex = 11;
            this.label5.Text = "与小区距离≤";
            // 
            // numericUpDownDistance
            // 
            this.numericUpDownDistance.Increment = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numericUpDownDistance.Location = new System.Drawing.Point(285, 55);
            this.numericUpDownDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numericUpDownDistance.Name = "numericUpDownDistance";
            this.numericUpDownDistance.Size = new System.Drawing.Size(55, 21);
            this.numericUpDownDistance.TabIndex = 8;
            this.numericUpDownDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numericUpDownDistance.Value = new decimal(new int[] {
            800,
            0,
            0,
            0});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(248, 30);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(77, 12);
            this.label7.TabIndex = 16;
            this.label7.Text = "包含小区数≥";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(24, 31);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(107, 12);
            this.label8.TabIndex = 17;
            this.label8.Text = "最大最小Rxlev差≤";
            // 
            // numCellCountLimit
            // 
            this.numCellCountLimit.Location = new System.Drawing.Point(330, 26);
            this.numCellCountLimit.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numCellCountLimit.Name = "numCellCountLimit";
            this.numCellCountLimit.Size = new System.Drawing.Size(55, 21);
            this.numCellCountLimit.TabIndex = 4;
            this.numCellCountLimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numCellCountLimit.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            // 
            // numRxLevDValue
            // 
            this.numRxLevDValue.Location = new System.Drawing.Point(134, 26);
            this.numRxLevDValue.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numRxLevDValue.Name = "numRxLevDValue";
            this.numRxLevDValue.Size = new System.Drawing.Size(55, 21);
            this.numRxLevDValue.TabIndex = 5;
            this.numRxLevDValue.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxLevDValue.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // txtHelp
            // 
            this.txtHelp.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.txtHelp.Location = new System.Drawing.Point(0, 314);
            this.txtHelp.Multiline = true;
            this.txtHelp.Name = "txtHelp";
            this.txtHelp.ReadOnly = true;
            this.txtHelp.Size = new System.Drawing.Size(481, 60);
            this.txtHelp.TabIndex = 10;
            this.txtHelp.Text = "在某一点存在过多的强RxLev小区却没有一个足够强的小区作为主控小区，即小区表(包含主服小区)中有4个或4个以上RxLev值都>-85bdm，并且最大的和最小的R" +
                "xLev之间相差小于等于6dbm。";
            // 
            // btnHelp
            // 
            this.btnHelp.Location = new System.Drawing.Point(13, 273);
            this.btnHelp.Name = "btnHelp";
            this.btnHelp.Size = new System.Drawing.Size(75, 23);
            this.btnHelp.TabIndex = 9;
            this.btnHelp.Text = "说明";
            this.btnHelp.UseVisualStyleBackColor = true;
            this.btnHelp.Click += new System.EventHandler(this.btnHelp_Click);
            // 
            // numRxLevMax
            // 
            this.numRxLevMax.Location = new System.Drawing.Point(173, 24);
            this.numRxLevMax.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numRxLevMax.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRxLevMax.Name = "numRxLevMax";
            this.numRxLevMax.Size = new System.Drawing.Size(55, 21);
            this.numRxLevMax.TabIndex = 18;
            this.numRxLevMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxLevMax.Value = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            // 
            // numHandoverTimeOffset
            // 
            this.numHandoverTimeOffset.Location = new System.Drawing.Point(332, 85);
            this.numHandoverTimeOffset.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numHandoverTimeOffset.Name = "numHandoverTimeOffset";
            this.numHandoverTimeOffset.Size = new System.Drawing.Size(55, 21);
            this.numHandoverTimeOffset.TabIndex = 19;
            this.numHandoverTimeOffset.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numHandoverTimeOffset.Value = new decimal(new int[] {
            2,
            0,
            0,
            0});
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(204, 91);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(125, 12);
            this.label9.TabIndex = 20;
            this.label9.Text = "切换请求关联时间差≤";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(395, 91);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(17, 12);
            this.label10.TabIndex = 21;
            this.label10.Text = "秒";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(391, 31);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(17, 12);
            this.label11.TabIndex = 22;
            this.label11.Text = "个";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.numSampleRxQuality);
            this.groupBox1.Controls.Add(this.label15);
            this.groupBox1.Controls.Add(this.label13);
            this.groupBox1.Controls.Add(this.numRxlevMin);
            this.groupBox1.Controls.Add(this.label10);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.numHandoverTimeOffset);
            this.groupBox1.Controls.Add(this.numRxLevMax);
            this.groupBox1.Controls.Add(this.label9);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.numRadius);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.numSampleCountLimit);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.numericUpDownDistance);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(455, 124);
            this.groupBox1.TabIndex = 23;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "采样点条件设置";
            // 
            // numSampleRxQuality
            // 
            this.numSampleRxQuality.Location = new System.Drawing.Point(335, 24);
            this.numSampleRxQuality.Maximum = new decimal(new int[] {
            7,
            0,
            0,
            0});
            this.numSampleRxQuality.Name = "numSampleRxQuality";
            this.numSampleRxQuality.Size = new System.Drawing.Size(55, 21);
            this.numSampleRxQuality.TabIndex = 26;
            this.numSampleRxQuality.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSampleRxQuality.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(266, 29);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(65, 12);
            this.label15.TabIndex = 25;
            this.label15.Text = "信号质量≥";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(156, 91);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(17, 12);
            this.label13.TabIndex = 24;
            this.label13.Text = "个";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.numRxQuality);
            this.groupBox2.Controls.Add(this.label14);
            this.groupBox2.Controls.Add(this.label12);
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Controls.Add(this.numCellCountLimit);
            this.groupBox2.Controls.Add(this.label11);
            this.groupBox2.Controls.Add(this.label8);
            this.groupBox2.Controls.Add(this.numRxLevDValue);
            this.groupBox2.Location = new System.Drawing.Point(13, 154);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(454, 95);
            this.groupBox2.TabIndex = 24;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "无主导条件设置";
            // 
            // numRxQuality
            // 
            this.numRxQuality.Increment = new decimal(new int[] {
            5,
            0,
            0,
            65536});
            this.numRxQuality.Location = new System.Drawing.Point(135, 61);
            this.numRxQuality.Maximum = new decimal(new int[] {
            7,
            0,
            0,
            0});
            this.numRxQuality.Name = "numRxQuality";
            this.numRxQuality.Size = new System.Drawing.Size(55, 21);
            this.numRxQuality.TabIndex = 27;
            this.numRxQuality.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(26, 65);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(107, 12);
            this.label14.TabIndex = 26;
            this.label14.Text = "RxQuality平均值≥";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(193, 30);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(23, 12);
            this.label12.TabIndex = 25;
            this.label12.Text = "dBm";
            // 
            // SetHaveNoMainCellFilterDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(481, 374);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnHelp);
            this.Controls.Add(this.txtHelp);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "SetHaveNoMainCellFilterDlg";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "无主导小区分析条件设置";
            this.Load += new System.EventHandler(this.SetHaveNoMainCellFilterDlg_Load);
            ((System.ComponentModel.ISupportInitialize)(this.numRxlevMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRadius)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCountLimit)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCellCountLimit)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHandoverTimeOffset)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleRxQuality)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRxQuality)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numRxlevMin;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numRadius;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numSampleCountLimit;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown numericUpDownDistance;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown numCellCountLimit;
        private System.Windows.Forms.NumericUpDown numRxLevDValue;
        private System.Windows.Forms.TextBox txtHelp;
        private System.Windows.Forms.Button btnHelp;
        private System.Windows.Forms.NumericUpDown numRxLevMax;
        private System.Windows.Forms.NumericUpDown numHandoverTimeOffset;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.NumericUpDown numRxQuality;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.NumericUpDown numSampleRxQuality;
    }
}