﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NoSignalCellForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(NoSignalCellForm));
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBCCH = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBSIC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTestPointCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellID = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.contextMenuStrip;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Remove.Visible = false;
            gridLevelNode1.RelationName = "Level1";
            this.gridControl.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(689, 388);
            this.gridControl.TabIndex = 1;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            this.gridControl.DoubleClick += new System.EventHandler(this.gridControl_DoubleClick);
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(139, 26);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(138, 22);
            this.miExportToExcel.Text = "导出Excel...";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnCellName,
            this.gridColumnCellID,
            this.gridColumnLAC,
            this.gridColumnCI,
            this.gridColumnBCCH,
            this.gridColumnBSIC,
            this.gridColumnTestPointCount});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsDetail.AllowZoomDetail = false;
            this.gridView.OptionsSelection.MultiSelect = true;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnCellName
            // 
            this.gridColumnCellName.Caption = "小区名";
            this.gridColumnCellName.FieldName = "CellName";
            this.gridColumnCellName.Name = "gridColumnCellName";
            this.gridColumnCellName.Visible = true;
            this.gridColumnCellName.VisibleIndex = 0;
            // 
            // gridColumnLAC
            // 
            this.gridColumnLAC.Caption = "LAC";
            this.gridColumnLAC.FieldName = "LAC";
            this.gridColumnLAC.Name = "gridColumnLAC";
            this.gridColumnLAC.Visible = true;
            this.gridColumnLAC.VisibleIndex = 1;
            // 
            // gridColumnCI
            // 
            this.gridColumnCI.Caption = "CI";
            this.gridColumnCI.FieldName = "CI";
            this.gridColumnCI.Name = "gridColumnCI";
            this.gridColumnCI.Visible = true;
            this.gridColumnCI.VisibleIndex = 2;
            // 
            // gridColumnBCCH
            // 
            this.gridColumnBCCH.Caption = "BCCH";
            this.gridColumnBCCH.FieldName = "BCCH";
            this.gridColumnBCCH.Name = "gridColumnBCCH";
            this.gridColumnBCCH.Visible = true;
            this.gridColumnBCCH.VisibleIndex = 3;
            // 
            // gridColumnBSIC
            // 
            this.gridColumnBSIC.Caption = "BSIC";
            this.gridColumnBSIC.FieldName = "BSIC";
            this.gridColumnBSIC.Name = "gridColumnBSIC";
            this.gridColumnBSIC.Visible = true;
            this.gridColumnBSIC.VisibleIndex = 4;
            // 
            // gridColumnTestPointCount
            // 
            this.gridColumnTestPointCount.Caption = "采样点数";
            this.gridColumnTestPointCount.FieldName = "TestPointCount";
            this.gridColumnTestPointCount.Name = "gridColumnTestPointCount";
            this.gridColumnTestPointCount.Visible = true;
            this.gridColumnTestPointCount.VisibleIndex = 5;
            // 
            // gridColumnCellID
            // 
            this.gridColumnCellID.Caption = "小区ID";
            this.gridColumnCellID.FieldName = "CellID";
            this.gridColumnCellID.Name = "gridColumnCellID";
            // 
            // NoSignalCellForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("NoSignalCellForm.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(689, 388);
            this.Controls.Add(this.gridControl);
            this.Name = "NoSignalCellForm";
            this.Text = "无信号小区核查";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLAC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBCCH;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBSIC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTestPointCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellID;
    }
}