﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Func
{
    public partial class CityIDsPanel : UserControl
    {
        private List<int> selDistrictIDs;
        /// <summary>
        /// 已选择的地市ID
        /// </summary>
        public List<int> SelDistrictIDs
        {
            get { return selDistrictIDs; }
        }
        ToolStripDropDown parentDropDown;
        Label countLabel;
        int curDistrictID;
        public CityIDsPanel(ToolStripDropDown dropDown,Label lblCount)
        {
            InitializeComponent();

            parentDropDown = dropDown;
            countLabel = lblCount;
        }

        internal void FillItems(int curDistrictID,List<int> selDistrictIDs)
        {
            this.curDistrictID = curDistrictID;
            this.selDistrictIDs = selDistrictIDs;
            List<IDNamePair> availableCitys = MainModel.GetInstance().User.GetAvailableCitys();
            foreach (IDNamePair pair in availableCitys)
            {
                ListViewItem item = new ListViewItem();
                item.Tag = pair;
                item.Text = pair.ToString();
                if (pair.id == curDistrictID)
                    item.Checked = true;
                listView.Items.Add(item);
            }
        }


        private void chkAll_CheckedChanged(object sender, EventArgs e)
        {
            if (chkAll.Checked)
            {
                foreach (ListViewItem item in listView.Items)
                {
                    item.Checked = chkAll.Checked;
                }
                chkAll.Text = "√全选/当前地市";
            }
            else
            {
                foreach (ListViewItem item in listView.Items)
                {
                    if (((IDNamePair)item.Tag).id == curDistrictID)
                    {
                        item.Checked = true;
                    }
                    else
                        item.Checked = false;
                }
                chkAll.Text = "全选/√当前地市";
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            selDistrictIDs.Clear();
            foreach (ListViewItem si in listView.CheckedItems)
            {
                selDistrictIDs.Add(((IDNamePair)si.Tag).id);
            }

            parentDropDown.Close();
            countLabel.Text = "[" + SelDistrictIDs.Count + "]";
        }

        private void listView_ItemChecked(object sender, ItemCheckedEventArgs e)
        {
            if (listView.CheckedItems.Count == 0)
            {
                foreach (ListViewItem item in listView.Items)
                {
                    if (((IDNamePair)item.Tag).id==curDistrictID)
                    {
                        item.Checked = true;
                        break;
                    }
                } 
                
            }
        }

    }
}
