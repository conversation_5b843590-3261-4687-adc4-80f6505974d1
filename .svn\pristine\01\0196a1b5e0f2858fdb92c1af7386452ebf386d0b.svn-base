﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.NewBlackBlock;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class BtsKpiAcceptInfo_SX
    {
        public BtsKpiAcceptInfo_SX(int enodeBid)
        {
            this.ENodeBID = enodeBid;
        }
        public int ENodeBID { get; set; }

        public List<FileInfo> FileList { get; set; } = new List<FileInfo>();

        public Dictionary<int, CellKpiAcceptInfo_SX> CellKpiAcceptInfoDic { get; set; } = new Dictionary<int, CellKpiAcceptInfo_SX>();
    }
    public class CellKpiAcceptInfo_SX
    {
        public CellKpiAcceptInfo_SX(int eci)
        {
            this.ECI = eci;
        }
        public int ECI { get; set; }

        public KpiRateInfo_SX RrcKpiInfo { get; set; }
        public KpiRateInfo_SX ErabKpiInfo { get; set; }

        public int AccessSuccessCount
        {
            get
            {
                int accessSuccessCount = 0;
                if (RrcKpiInfo != null)
                {
                    accessSuccessCount += RrcKpiInfo.SuccessCount;
                }
                if (ErabKpiInfo != null)
                {
                    accessSuccessCount += ErabKpiInfo.SuccessCount;
                }
                return accessSuccessCount;
            }
        }
        public double AccessSuccessRate
        {
            get
            {
                //LTE无线接通率(陕西要求):(E-RAB建立成功数/E-RAB建立请求数)*(RRC连接建立成功次数/ RRC连接建立请求次数)*100%,
                return Math.Round((RrcKpiInfo.SuccessRate * ErabKpiInfo.SuccessRate) / 100, 2);
            }
        }

        public void AddData(KpiRateInfo_SX rrcKpiInfo, KpiRateInfo_SX erabKpiInfo)
        {
            if (rrcKpiInfo != null)
            {
                if (this.RrcKpiInfo == null)
                {
                    this.RrcKpiInfo = rrcKpiInfo.Clone();
                }
                else
                {
                    this.RrcKpiInfo.Merge(rrcKpiInfo);
                }
            }

            if (erabKpiInfo != null)
            {
                if (this.ErabKpiInfo == null)
                {
                    this.ErabKpiInfo = erabKpiInfo.Clone();
                }
                else
                {
                    this.ErabKpiInfo.Merge(erabKpiInfo);
                }
            }
        }
    }

    public class KpiRateInfo_SX
    {
        public int RequestCnt { get; set; }
        public int SuccessCount { get; set; }
        public double SuccessRate
        {
            get
            {
                if (RequestCnt > 0)
                {
                    return Math.Round(100 * (double)SuccessCount / RequestCnt, 2);
                }
                return 0;
            }
        }

        public void Merge(KpiRateInfo_SX info)
        {
            this.RequestCnt += info.RequestCnt;
            this.SuccessCount += info.SuccessCount;
        }
        public KpiRateInfo_SX Clone()
        {
            KpiRateInfo_SX info = new KpiRateInfo_SX();
            info.RequestCnt = this.RequestCnt;
            info.SuccessCount = this.SuccessCount;
            return info;
        }
    }
}
