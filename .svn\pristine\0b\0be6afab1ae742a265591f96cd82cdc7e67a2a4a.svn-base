﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTNoCoverRoadListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.TDScanPccpchRscpcontextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ExportToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.treeListViewNoneCover = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDistance = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHeadPointLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHeadPointLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHeadPointRoadName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMiddlePointLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMiddlePointLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMiddlePointsRoadName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLastPointLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLastPointLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLastPointRoadName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLowPccpchRscp = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHighPccpchRscp = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMeanPccpchRscp = new BrightIdeasSoftware.OLVColumn();
            this.TDScanPccpchRscpcontextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewNoneCover)).BeginInit();
            this.SuspendLayout();
            // 
            // TDScanPccpchRscpcontextMenuStrip
            // 
            this.TDScanPccpchRscpcontextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ExportToolStripMenuItem});
            this.TDScanPccpchRscpcontextMenuStrip.Name = "TDScanPccpchRscpcontextMenuStrip";
            this.TDScanPccpchRscpcontextMenuStrip.Size = new System.Drawing.Size(125, 26);
            // 
            // ExportToolStripMenuItem
            // 
            this.ExportToolStripMenuItem.Name = "ExportToolStripMenuItem";
            this.ExportToolStripMenuItem.Size = new System.Drawing.Size(124, 22);
            this.ExportToolStripMenuItem.Text = "导出列表";
            this.ExportToolStripMenuItem.Click += new System.EventHandler(this.ExportToolStripMenuItem_Click);
            // 
            // treeListViewNoneCover
            // 
            this.treeListViewNoneCover.AllColumns.Add(this.olvColumnSN);
            this.treeListViewNoneCover.AllColumns.Add(this.olvColumnDistance);
            this.treeListViewNoneCover.AllColumns.Add(this.olvColumnHeadPointLongitude);
            this.treeListViewNoneCover.AllColumns.Add(this.olvColumnHeadPointLatitude);
            this.treeListViewNoneCover.AllColumns.Add(this.olvColumnHeadPointRoadName);
            this.treeListViewNoneCover.AllColumns.Add(this.olvColumnMiddlePointLongitude);
            this.treeListViewNoneCover.AllColumns.Add(this.olvColumnMiddlePointLatitude);
            this.treeListViewNoneCover.AllColumns.Add(this.olvColumnMiddlePointsRoadName);
            this.treeListViewNoneCover.AllColumns.Add(this.olvColumnLastPointLongitude);
            this.treeListViewNoneCover.AllColumns.Add(this.olvColumnLastPointLatitude);
            this.treeListViewNoneCover.AllColumns.Add(this.olvColumnLastPointRoadName);
            this.treeListViewNoneCover.AllColumns.Add(this.olvColumnLowPccpchRscp);
            this.treeListViewNoneCover.AllColumns.Add(this.olvColumnHighPccpchRscp);
            this.treeListViewNoneCover.AllColumns.Add(this.olvColumnMeanPccpchRscp);
            this.treeListViewNoneCover.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnDistance,
            this.olvColumnHeadPointLongitude,
            this.olvColumnHeadPointLatitude,
            this.olvColumnHeadPointRoadName,
            this.olvColumnMiddlePointLongitude,
            this.olvColumnMiddlePointLatitude,
            this.olvColumnMiddlePointsRoadName,
            this.olvColumnLastPointLongitude,
            this.olvColumnLastPointLatitude,
            this.olvColumnLastPointRoadName,
            this.olvColumnLowPccpchRscp,
            this.olvColumnHighPccpchRscp,
            this.olvColumnMeanPccpchRscp});
            this.treeListViewNoneCover.ContextMenuStrip = this.TDScanPccpchRscpcontextMenuStrip;
            this.treeListViewNoneCover.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListViewNoneCover.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListViewNoneCover.FullRowSelect = true;
            this.treeListViewNoneCover.GridLines = true;
            this.treeListViewNoneCover.Location = new System.Drawing.Point(0, 0);
            this.treeListViewNoneCover.Name = "treeListViewNoneCover";
            this.treeListViewNoneCover.OwnerDraw = true;
            this.treeListViewNoneCover.ShowGroups = false;
            this.treeListViewNoneCover.Size = new System.Drawing.Size(1063, 443);
            this.treeListViewNoneCover.TabIndex = 1;
            this.treeListViewNoneCover.UseCompatibleStateImageBehavior = false;
            this.treeListViewNoneCover.View = System.Windows.Forms.View.Details;
            this.treeListViewNoneCover.VirtualMode = true;
            this.treeListViewNoneCover.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeListViewNoneCover_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 42;
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "连续无覆盖长度(米)";
            this.olvColumnDistance.Width = 126;
            // 
            // olvColumnHeadPointLongitude
            // 
            this.olvColumnHeadPointLongitude.HeaderFont = null;
            this.olvColumnHeadPointLongitude.Text = "首采样点经度";
            this.olvColumnHeadPointLongitude.Width = 89;
            // 
            // olvColumnHeadPointLatitude
            // 
            this.olvColumnHeadPointLatitude.HeaderFont = null;
            this.olvColumnHeadPointLatitude.Text = "首采样点纬度";
            this.olvColumnHeadPointLatitude.Width = 89;
            // 
            // olvColumnHeadPointRoadName
            // 
            this.olvColumnHeadPointRoadName.HeaderFont = null;
            this.olvColumnHeadPointRoadName.Text = "首采样点所在道路";
            this.olvColumnHeadPointRoadName.Width = 113;
            // 
            // olvColumnMiddlePointLongitude
            // 
            this.olvColumnMiddlePointLongitude.HeaderFont = null;
            this.olvColumnMiddlePointLongitude.Text = "中间采样点经度";
            this.olvColumnMiddlePointLongitude.Width = 100;
            // 
            // olvColumnMiddlePointLatitude
            // 
            this.olvColumnMiddlePointLatitude.HeaderFont = null;
            this.olvColumnMiddlePointLatitude.Text = "中间采样点纬度";
            this.olvColumnMiddlePointLatitude.Width = 100;
            // 
            // olvColumnMiddlePointsRoadName
            // 
            this.olvColumnMiddlePointsRoadName.HeaderFont = null;
            this.olvColumnMiddlePointsRoadName.Text = "中间采样点所在道路";
            this.olvColumnMiddlePointsRoadName.Width = 124;
            // 
            // olvColumnLastPointLongitude
            // 
            this.olvColumnLastPointLongitude.HeaderFont = null;
            this.olvColumnLastPointLongitude.Text = "尾采样点经度";
            this.olvColumnLastPointLongitude.Width = 89;
            // 
            // olvColumnLastPointLatitude
            // 
            this.olvColumnLastPointLatitude.HeaderFont = null;
            this.olvColumnLastPointLatitude.Text = "尾采样点纬度";
            this.olvColumnLastPointLatitude.Width = 88;
            // 
            // olvColumnLastPointRoadName
            // 
            this.olvColumnLastPointRoadName.HeaderFont = null;
            this.olvColumnLastPointRoadName.Text = "尾采样点所在道路";
            this.olvColumnLastPointRoadName.Width = 112;
            // 
            // olvColumnLowPccpchRscp
            // 
            this.olvColumnLowPccpchRscp.HeaderFont = null;
            this.olvColumnLowPccpchRscp.Text = "最低电平PCCPCH_RSCP";
            this.olvColumnLowPccpchRscp.Width = 131;
            // 
            // olvColumnHighPccpchRscp
            // 
            this.olvColumnHighPccpchRscp.HeaderFont = null;
            this.olvColumnHighPccpchRscp.Text = "最高电平PCCPCH_RSCP";
            this.olvColumnHighPccpchRscp.Width = 129;
            // 
            // olvColumnMeanPccpchRscp
            // 
            this.olvColumnMeanPccpchRscp.HeaderFont = null;
            this.olvColumnMeanPccpchRscp.Text = "平均电平PCCPCH_RSCP";
            this.olvColumnMeanPccpchRscp.Width = 130;
            // 
            // DITNoneCoverScanTDPccpchRscpReport
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1063, 443);
            this.Controls.Add(this.treeListViewNoneCover);
            this.Name = "DITNoneCoverScanTDPccpchRscpReport";
            this.Text = "TD扫频无覆盖道路";
            this.TDScanPccpchRscpcontextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewNoneCover)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip TDScanPccpchRscpcontextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem ExportToolStripMenuItem;
        private BrightIdeasSoftware.TreeListView treeListViewNoneCover;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnMiddlePointLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnMiddlePointLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnMiddlePointsRoadName;
        private BrightIdeasSoftware.OLVColumn olvColumnLowPccpchRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnHighPccpchRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnMeanPccpchRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnHeadPointLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnHeadPointLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnHeadPointRoadName;
        private BrightIdeasSoftware.OLVColumn olvColumnLastPointLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLastPointLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLastPointRoadName;
    }
}