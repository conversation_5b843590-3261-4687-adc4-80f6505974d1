﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;

namespace MasterCom.RAMS.Net
{
    public class DIYQueryRoadProblemInfo : QueryBase
    {
        /// <summary>
        /// 数据类型，0：GSM，1：TD
        /// </summary>
        public int dataType { get; set; } = 0;
        public int year { get; set; }
        public int batch { get; set; }
        public string areaName { get; set; }
        public int level { get; set; }
        public string roadName { get; set; }
        public List<RoadKPIMonth> RoadKPIList { get; set; } = new List<RoadKPIMonth>();
        private DIYQueryRoadProblemInfo(MainModel mainModel)
            : base(mainModel)
        {

        }

        private static DIYQueryRoadProblemInfo queryRoadProblemInfo = null;

        public static DIYQueryRoadProblemInfo GetInstance()
        {
            if (queryRoadProblemInfo == null)
            {
                queryRoadProblemInfo = new DIYQueryRoadProblemInfo(MainModel.GetInstance());
            }
            return queryRoadProblemInfo;
        }

        public override string Name
        {
            get { return "道路问题点详情"; }
        }

        public override string IconName
        {
            get { return ""; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18002, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }
        protected override void query()
        {
            queryInThread();
        }

        private void queryInThread()
        {
            try
            {
                if (dataType == 0)
                {
                    /**
                    string sqlRoadMonthKPI = "select year, batch, netGridID, isPoor, rxqualTotal, rxqual0_4Pct, rxqual5Pct," +
                    "rxqual6_7Pct, mosTotal, mos2d8Pct from tb_sz_netgrid_gsm_batch_result where netGridID = " + netGridID +
                    " and convert(varchar(4),year)+convert(varchar(2),batch) >= " + curBatchString +
                    " order by year desc, batch desc";
                    DIYSQLRoadKPIMonth_GSM roadKPIQuery = new DIYSQLRoadKPIMonth_GSM(MainModel, sqlRoadMonthKPI);
                    roadKPIQuery.Query();
                    RoadKPIList = roadKPIQuery.GridKPIList;
                    *///
                }
                else if (dataType == 1)
                {
                    string sqlRoadMonthKPI = "select year, batch, areaName, level, roadName, isPoor, RSCPTotal, RSCP85Pct " +
                    " from tb_sz_road_td_batch_result where areaName = '" + areaName + "' and level = " + level +
                    " and roadName = '" + roadName + "' " +
                    " and (year > " + year + " or (year = " + year + " and batch >= " + batch + "))" +
                    " order by year desc, batch desc";
                    DIYSQLRoadKPIMonth_TD roadKPIQuery = new DIYSQLRoadKPIMonth_TD(MainModel, sqlRoadMonthKPI);
                    roadKPIQuery.Query();
                    RoadKPIList = roadKPIQuery.RoadKPIList;
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                //WaitBox.Close()
            }
        }

        /**
        private class DIYSQLRoadKPIMonth_GSM : DIYSQLBase
        {
            public List<RoadKPIMonth> GridKPIList = new List<RoadKPIMonth>();
            protected string sql = "";
            public string SQL
            {
                get { return sql; }
                set { sql = value; }
            }
            public DIYSQLRoadKPIMonth_GSM(MainModel mainModel, string sql)
                : base(mainModel)
            {
                this.sql = sql;
            }

            protected override string getSqlTextString()
            {
                return sql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[10];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_Int;
                rType[3] = E_VType.E_Int;
                rType[4] = E_VType.E_Int;
                rType[5] = E_VType.E_Float;
                rType[6] = E_VType.E_Float;
                rType[7] = E_VType.E_Float;
                rType[8] = E_VType.E_Int;
                rType[9] = E_VType.E_Float;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                GridKPIList.Clear();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        RoadKPIMonthGSM gridKPI = new RoadKPIMonthGSM();
                        gridKPI.Fill(package.Content);
                        GridKPIList.Add(gridKPI);
                        //do your code here
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                    {
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                    {
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }

            public override string Name
            {
                get { return "DIYSQLRoadKPIMonth_GSM"; }
            }
        }
        */

        private class DIYSQLRoadKPIMonth_TD : DIYSQLBase
        {
            public List<RoadKPIMonth> RoadKPIList = new List<RoadKPIMonth>();
            protected string sql;

            public DIYSQLRoadKPIMonth_TD(MainModel mainModel, string sql)
                : base(mainModel)
            {
                this.sql = sql;
            }

            protected override string getSqlTextString()
            {
                return sql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[8];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_String;
                rType[3] = E_VType.E_Int;
                rType[4] = E_VType.E_String;
                rType[5] = E_VType.E_Int;
                rType[6] = E_VType.E_Int;
                rType[7] = E_VType.E_Float;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                RoadKPIList.Clear();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        RoadKPIMonthTD roadKPI = new RoadKPIMonthTD();
                        roadKPI.Fill(package.Content);
                        RoadKPIList.Add(roadKPI);
                        //do your code here
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }

            public override string Name
            {
                get { return "DIYSQLRoadKPIMonth_TD"; }
            }
        }
    }
}
