﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    public partial class WorkParamsImportForm<T> : MinCloseForm
        where T : CellAcceptWorkParam_SX
    {
        Dictionary<string, T> changedWorkParam = new Dictionary<string, T>();
        Dictionary<string, T> selectedRow = new Dictionary<string, T>();
        List<T> resList;
        List<T> originalResList;
        NetType netType;

        public WorkParamsImportForm()
        {
            InitializeComponent();
        }

        public void FillData(List<T> resList, NetType type)
        {
            this.netType = type;
            this.resList = resList;
            originalResList = ListDeepCopy<T>(resList);
            gridControl1.DataSource = resList;
            gridControl1.RefreshDataSource();
        }

        public List<U> ListDeepCopy<U>(object List)
        {
            using (Stream objectStream = new MemoryStream())
            {
                BinaryFormatter formatter = new BinaryFormatter();
                formatter.Serialize(objectStream, List);
                objectStream.Seek(0, SeekOrigin.Begin);
                return formatter.Deserialize(objectStream) as List<U>;
            }
        }

        bool isMasterModel = false;
        private void btnOpenUpdate_Click(object sender, EventArgs e)
        {
            if (!isMasterModel)
            {
                TextInputBox box = new TextInputBox("请输入系统管理员口令", "口令", null);
                box.SetPwdMode(true);
                while (box.ShowDialog() == DialogResult.OK)
                {
                    if (box.TextInput == "mastercom168")
                    {
                        changedWorkParam.Clear();
                        selectedRow.Clear();
                        gv.OptionsBehavior.Editable = true;
                        btnOpenUpdate.Text = "取消管理员模式";
                        isMasterModel = true;

                        gridColumnSelected.Visible = true;
                        gridColumnSelected.VisibleIndex = 0;

                        panelMasterModel.Visible = true;
                        break;
                    }
                }
            }
            else
            {
                if (changedWorkParam.Count > 0)
                {
                    DialogResult result = MessageBox.Show("有" + changedWorkParam.Count + "条数据已修改,但并未提交,确认放弃修改吗?"
                        , "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information);
                    if (result == DialogResult.OK)
                    {
                        recoverData();
                    }
                    else
                    {
                        return;
                    }
                }

                gv.OptionsBehavior.Editable = false;
                btnOpenUpdate.Text = "进入管理员模式";
                isMasterModel = false;
                panelMasterModel.Visible = false;
                gridColumnSelected.Visible = false;
            }
        }

        private static string getToken(T res)
        {
            //return res.GetHashCode().ToString();
            return res.ENodeBID + "_" + res.CellNameFull;
        }

        private void btnUpdateAll_Click(object sender, EventArgs e)
        {
            DialogResult result = MessageBox.Show("已修改的工参有" + changedWorkParam.Count + "条,确认提交修改这些工参数据吗?"
                        , "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information);
            if (result == DialogResult.OK)
            {
                var query = new WorkParamsUpdateQuery<T>(changedWorkParam, netType);
                query.Query();
                changedWorkParam.Clear();
                originalResList = ListDeepCopy<T>(resList);
                MessageBox.Show("提交完成");
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            //DialogResult result = MessageBox.Show("所选工参有" + selectedRow.Count + "条数据,确认删除这些数据吗?"
            //          , "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information);
            //if (result == DialogResult.OK)
            //{
                foreach (var data in selectedRow.Values)
                {
                    data.AnalysedType = (int)AnalyseType.Delete;
                    resList.Remove(data);

                    string token = getToken(data);
                    changedWorkParam[token] = data;
                }
                selectedRow.Clear();
                gridControl1.RefreshDataSource();
            //}
        }

        private void btnModifyStates_Click(object sender, EventArgs e)
        {
            //DialogResult result = MessageBox.Show("所选工参有" + selectedRow.Count + "条数据,确认将其改为未单验状态吗?"
            //         , "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information);
            //if (result == DialogResult.OK)
            //{
                foreach (var data in selectedRow.Values)
                {
                    data.AnalysedType = -1;
                    data.AnalysedTypeString = EnumDescriptionAttribute.GetText((AnalyseType)data.AnalysedType);

                    string token = getToken(data);
                    changedWorkParam[token] = data;
                }
                gridControl1.RefreshDataSource();
            //}
        }

        private void btnRecover_Click(object sender, EventArgs e)
        {
            if (changedWorkParam.Count > 0)
            {
                DialogResult result = MessageBox.Show("有" + changedWorkParam.Count + "条数据已修改,确认放弃修改吗?"
                    , "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information);
                if (result == DialogResult.OK)
                {
                    recoverData();
                }
            }
        }

        private void recoverData()
        {
            changedWorkParam.Clear();
            selectedRow.Clear();
            resList = new List<T>(originalResList);
            gridControl1.DataSource = resList;
            gridControl1.RefreshDataSource();
        }

        private string beforeEditValue;
        private void gv_CellValueChanging(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            object row = gv.GetRow(e.RowHandle);
            if (row is T)
            {
                T res = row as T;
                beforeEditValue = getToken(res);
                if (e.Column.ColumnHandle != 11)
                {
                    changedWorkParam[beforeEditValue] = res;
                }
            }
        }

        private void gv_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            object row = gv.GetRow(e.RowHandle);
            if (row is T)
            {
                T res = row as T;
                switch (e.Column.ColumnHandle)
                {
                    case 8:
                        AnalyseType type = (AnalyseType)EnumDescriptionAttribute.Parse(typeof(AnalyseType), res.AnalysedTypeString);
                        res.AnalysedType = (int)type;
                        break;
                    case 11:
                        if (res.Selected)
                        {
                            selectedRow[beforeEditValue] = res;
                        }
                        else
                        {
                            if (selectedRow.ContainsKey(beforeEditValue))
                            {
                                selectedRow.Remove(beforeEditValue);
                            }
                        }
                        break;
                }
            }
        }

        protected override void MinCloseForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (changedWorkParam.Count > 0)
            {
                DialogResult result = MessageBox.Show("有" + changedWorkParam.Count + "条数据已修改,确认放弃修改吗?"
                    , "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information);
                if (result == DialogResult.OK)
                {
                    base.MinCloseForm_FormClosing(sender, e);
                }
                else
                {
                    e.Cancel = true;
                }
            }
            else 
            { 
                base.MinCloseForm_FormClosing(sender, e);
            }
        }
    }
}
