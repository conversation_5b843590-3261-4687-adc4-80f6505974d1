﻿using System;
using System.Collections.Generic;
using System.Text; 
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using DevExpress.XtraEditors;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc.CheckCellOccupation
{
    /// <summary>
    /// 本类用来存储所有小区和这些小区各自的采样点，
    /// 并提供相应操作。
    /// </summary>
    class CellTpList
    {
        public Dictionary<int, CellData> dicCellData { get; set; }

        public CellTpList()
        {
            this.dicCellData = new Dictionary<int, CellData>();
        }
        public void AddTestPoint(TestPoint tp, bool isLteFdd)
        {
            LTECell cell = null;
            if (isLteFdd)
            {
                cell = tp.GetMainCell_LTE_FDD();
            }
            else
            {
                cell = tp.GetMainCell_LTE();
            }

            if (cell == null)
            {
                return;
            }
            int id = cell.ID;
            CellData cellData = null;
            if (!this.dicCellData.TryGetValue(id, out cellData))
            {
                cellData = new CellData(isLteFdd);
                this.dicCellData[id] = cellData;
            }
            cellData.AddTestPoint(tp);
        }
        public List<Result> listResult = null;
        public void GetResult(object o)
        {
            Condition_CheckCellOccupation condition = o as Condition_CheckCellOccupation;
            int sn = 1;
            this.listResult = new List<Result>();
            Result result = null;

            foreach (CellData cellData in this.dicCellData.Values)
            {
                if (!cellData.Calculator(condition))
                {
                    continue;
                }
                result = new Result();
                listResult.Add(result);

                result.cellData = cellData;
                LTECell cell = cellData.mainCell;

                result.SN = sn++;
                result.CellName = cell.Name;
                result.NodeBID = cell.BelongBTS.BTSID;
                result.TAC = cell.TAC;
                result.CI = cell.ECI;
                result.ArgRSRP = cellData.argRSRP;
                result.ArgSINR = cellData.argSINR;
                result.TestPointSum = cellData.listTp.Count;
                result.ArgLon = Math.Round(cellData.argLon, 5);
                result.ArgLat = Math.Round(cellData.argLat, 5);
                result.DistanceMainCell = cellData.distanceArgLonlat2Cell;
                LTEBTS bts = cellData.nearestBTS;
                if (bts != null)
                {
                    result.NearstestBTS_Name = bts.Name;
                    result.NearstestBTS_NodeBID = bts.BTSID;
                    result.NearstestBTS_Distance = cellData.distance2NearestBTS;
                    result.IsOccupationUnusual = cellData.isOccupationUnusual;
                    result.DistanceDifference = Math.Round(cellData.distance2NearestBTS - cellData.distanceArgLonlat2Cell, 2);
                    result.IsOverCover = cellData.isOverCover;
                }
                else
                {
                    result.NearstestBTS_Name = "-";
                    result.NearstestBTS_NodeBID = 0;
                    result.NearstestBTS_Distance = 0;
                    result.IsOccupationUnusual = "-";
                    result.DistanceDifference = 0;
                    result.IsOverCover = "-";
                }
            }
        }

    }

    public class CellDataBase
    {
        public ICell Cell { get; set; }
        public ISite BTS { get; set; }
        public double argLon { get; set; } = 0;
        public double argLat { get; set; } = 0;
    }

    public class CellData : CellDataBase
    {
        public CellData(bool isLteFdd)
        {
            this.isLteFdd = isLteFdd;
            if (isLteFdd)
            {
                paramStrRsrp = "lte_fdd_RSRP";
                paramStrSinr = "lte_fdd_SINR";
            }
        }
        bool isLteFdd { get; set; } = false;
        string paramStrRsrp { get; set; } = "lte_RSRP";
        string paramStrSinr { get; set; } = "lte_SINR";

        public LTECell mainCell { get; set; }
        public List<TestPoint> listTp { get; set; }
        public List<TestPoint> listValidTp { get; set; }
        public double argRSRP { get; set; } = 0;
        public double argSINR { get; set; } = 0;
        public double distanceArgLonlat2Cell { get; set; } = 0;//米
        public LTEBTS nearestBTS { get; set; }
        public double distance2NearestBTS { get; set; } = 0;//米
        public string isOccupationUnusual { get; set; }
        public string isOverCover { get; set; }

        public void AddTestPoint(TestPoint tp)
        {
            if (this.listTp == null)
            {
                this.listTp = new List<TestPoint>();
                if (isLteFdd)
                {
                    this.mainCell = tp.GetMainCell_LTE_FDD();
                }
                else
                {
                    this.mainCell = tp.GetMainCell_LTE();
                }
                Cell = mainCell;
            }
            this.listTp.Add(tp);
        }

        /// <summary>
        /// 根据原始数据计算其他所有结果
        /// </summary>
        public bool Calculator(Condition_CheckCellOccupation condition)
        {
            //计算平均经纬度、平均RSRP、平均SINR
            float? f_rsrp = null;
            float? f_sinr = null;
            double lon = 0;
            double lat = 0;
            double rsrp = 0;
            double sinr = 0;
            int counter = 0;
            this.listValidTp = new List<TestPoint>();
            foreach (TestPoint tp in this.listTp)
            {
                f_rsrp = (float?)tp[paramStrRsrp];
                f_sinr = (float?)tp[paramStrSinr];
                if (f_rsrp == null || f_sinr == null) continue;
                if (((float)tp[paramStrRsrp]) <= condition.rsrp) continue;
                counter++;
                lon += tp.Longitude;
                lat += tp.Latitude;
                rsrp += (float)tp[paramStrRsrp];
                sinr += (float)tp[paramStrSinr];
                this.listValidTp.Add(tp);
            }
            if (counter == 0)
            {
                return false;
            }
            this.argLon = lon / counter;
            this.argLat = lat / counter;
            this.argRSRP = Math.Round(rsrp / counter, 2);
            this.argSINR = Math.Round(sinr / counter, 2);
            //平均经纬度到主服小区的距离
            this.distanceArgLonlat2Cell = MathFuncs.GetDistance(this.argLon, this.argLat, this.mainCell.Longitude, this.mainCell.Latitude);
            this.distanceArgLonlat2Cell = Math.Round(this.distanceArgLonlat2Cell, 2);
            //计算N米内最近基站
            List<LTEBTS> listBTS = CellManager.GetInstance().GetCurrentLTEBTSs();
            if (listBTS == null) listBTS = new List<LTEBTS>();
            this.distance2NearestBTS = double.MaxValue;
            setNearestBTS(listBTS);
            this.distance2NearestBTS = Math.Round(this.distance2NearestBTS, 2);
            if (this.distance2NearestBTS > condition.radius)
            {
                this.distance2NearestBTS = double.MaxValue;
                this.nearestBTS = null;
                return true;
            }
            //是否占用异常
            judgeIsOccupationUnusual();
            //判断是否过覆盖
            return judgeIsOverCover(condition);
        }

        private void setNearestBTS(List<LTEBTS> listBTS)
        {
            foreach (LTEBTS bts in listBTS)
            {
                if (bts.BTSID == this.mainCell.BelongBTS.BTSID)
                {
                    //不包括主服小区所属基站
                    continue;
                }
                if (bts.Type == LTEBTSType.Indoor)
                {
                    //不包括室分小区
                    continue;
                }
                double d = MathFuncs.GetDistance(this.argLon, this.argLat, bts.Longitude, bts.Latitude);
                if (d < this.distance2NearestBTS)
                {
                    this.distance2NearestBTS = d;
                    this.nearestBTS = bts;
                    BTS = bts;
                }
            }
        }

        private bool judgeIsOverCover(Condition_CheckCellOccupation condition)
        {
            if (this.isOccupationUnusual.Equals("异常"))
            {
                if (this.checkOverCover(condition) == 1)
                {
                    this.isOverCover = "是";
                }
                else if (this.checkOverCover(condition) == 0)
                {
                    this.isOverCover = "否";
                }
                else
                {
                    return false;
                }
            }
            return true;
        }

        private void judgeIsOccupationUnusual()
        {
            if (this.distanceArgLonlat2Cell < this.distance2NearestBTS)
            {
                this.isOccupationUnusual = "正常";
            }
            else if (this.distanceArgLonlat2Cell == this.distance2NearestBTS)
            {
                this.isOccupationUnusual = "";
            }
            else if (this.distanceArgLonlat2Cell > this.distance2NearestBTS)
            {
                this.isOccupationUnusual = "异常";
            }
        }

        private int checkOverCover(Condition_CheckCellOccupation condition)
        {
            if (this.mainCell.Antennas == null || this.mainCell.Antennas.Count == 0)//避免工参不对Antennas为空时CalculateRadius()中报错
            {
                return -1;
            }
            double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(this.mainCell, condition.btsCount);
            foreach (TestPoint tp in this.listTp)
            {
                float? pccpchRSCP = (float?)tp[paramStrRsrp];
                if (pccpchRSCP == null)
                {
                    continue;
                }
                if (pccpchRSCP < condition.rxlev)
                {
                    continue;
                }
                if (this.distanceArgLonlat2Cell > (radiusOfCell * condition.radiusFactor))
                {
                    return 1;
                }
            }
            return 0;
        }
    }
    public class Condition_CheckCellOccupation
    {
        public float rsrp { get; set; }
        public double radius { get; set; }
        public int rxlev { get; set; }
        public int btsCount { get; set; }
        public double radiusFactor { get; set; }
        public Condition_CheckCellOccupation(float parRsrp, double parRadisus, int parRxlev, int parBtsCount, double parRadiusFactor)
        {
            this.rsrp = parRsrp;
            this.radius = parRadisus;
            this.rxlev = parRxlev;
            this.btsCount = parBtsCount;
            this.radiusFactor = parRadiusFactor;
        }
    }

    /// <summary>
    /// 结果显示列表的一行数据存数对象。
    /// </summary>
    public class Result
    {
        public int SN { set; get; }
        public string CellName { set; get; }
        public int NodeBID { set; get; }
        public int TAC { set; get; }
        public int CI { set; get; }
        public double ArgRSRP{ set; get; }
        public double ArgSINR { set; get; }
        public int TestPointSum { set; get; }
        public double ArgLon { set; get; }
        public double ArgLat { set; get; }
        public double DistanceMainCell { set; get; }
        public string NearstestBTS_Name { set; get; }
        public int NearstestBTS_NodeBID{ set; get; }
        public double NearstestBTS_Distance { set; get; }
        public string IsOccupationUnusual { set; get; }
        public string IsOverCover { set; get; }
        public double DistanceDifference { set; get; }

        //显示图层时会用到的数据
        public CellData cellData { get; set; }
    }
}
