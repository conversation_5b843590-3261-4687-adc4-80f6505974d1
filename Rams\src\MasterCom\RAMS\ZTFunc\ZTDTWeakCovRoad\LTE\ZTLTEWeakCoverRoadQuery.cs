﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLTEWeakCoverRoadQuery : ZTWeakCoverRoadQueryModel
    {
        private WeakCoverRoadLTEAssocCondtion assocCond;
        private static ZTLTEWeakCoverRoadQuery intance = null;
        public static new ZTLTEWeakCoverRoadQuery GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTLTEWeakCoverRoadQuery();
                    }
                }
            }
            return intance;
        }

        public ZTLTEWeakCoverRoadQuery()
        {
            init();
        }

        protected void init()
        {
            this.IsCanExportResultMapToWord = true;
            name = "弱覆盖路段_LTE";
            type = 2;
            funcId = 22000;
            subfuncId = 22009;
            desc = "分析";
            tpStr = "lte_NCell_RSRP";
            tpRSRP = "lte_RSRP";
            tpSINR = "lte_SINR";
            tpTac = "lte_TAC";
            tpSpeed = "lte_APP_Speed_Mb";
            themeName = "TD_LTE_RSRP";
            tpNCell_EARFCN = "lte_NCell_EARFCN";
            tpNCell_PCI = "lte_NCell_PCI";
            tpAppType = "lte_APP_type";

            assocCond = new WeakCoverRoadLTEAssocCondtion
            {
                NbRsrpThreshold = this.weakCondition.MaxRSRP,
                SiteRadius = 1000,
                RefSignalPower = 92
            };

            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
        }
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                weakCondition = weakCovRoadCond;
            }
            return base.getCondition();
        }
        protected override void fireShowForm()
        {
            if (!weakCondition.IsAssocAnalysis)
            {
                base.fireShowForm();
                return;
            }

            WaitBox.Show("正在进行关联分析...", DoAssociationAnalysis);
            WeakCoverRoadLTEFormEx frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(WeakCoverRoadLTEFormEx)) as WeakCoverRoadLTEFormEx;
            if (frm == null || frm.IsDisposed)
            {
                frm = new WeakCoverRoadLTEFormEx(MainModel);
            }
            frm.weakCondition = this.weakCondition;
            frm.FillData(weakCoverList, this.assocCond);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            weakCoverList = null;
        }

        protected override void fireSetDefaultMapSerialTheme()
        {
            MainModel.FireSetDefaultMapSerialTheme(themeName);
        }

        protected void DoAssociationAnalysis()
        {
            try
            {
                List<LTEBTS> btsList = null;
                if (MapLTECellLayer.DrawCurrent)
                {
                    btsList = CellManager.GetInstance().GetCurrentLTEBTSs();
                }
                else
                {
                    btsList = CellManager.GetInstance().GetLTEBTSs(MapLTECellLayer.CurShowSnapshotTime);
                }

                int iLoop = 0;
                foreach (WeakCoverRoadLTE weakRoad in weakCoverList)
                {
                    WaitBox.ProgressPercent = ++iLoop * 100 / weakCoverList.Count;
                    List<WeakCoverRoadLTEAssocSCell> assocSCells = FindAssociationCells(weakRoad.TestPoints);
                    weakRoad.AssocSCells = assocSCells;

                    foreach (WeakCoverRoadLTEAssocSCell sCell in assocSCells)
                    {
                        sCell.BtsList = btsList;
                        sCell.GetResult(assocCond);
                    }
                }
            }
            catch (System.Exception e)
            {
                MessageBox.Show(e.Message + Environment.NewLine + e.StackTrace, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitBox.Close();
            }
        }

        protected virtual List<WeakCoverRoadLTEAssocSCell> FindAssociationCells(List<TestPoint> testPoints)
        {
            Dictionary<LTECell, WeakCoverRoadLTEAssocSCell> sCellDic = new Dictionary<LTECell, WeakCoverRoadLTEAssocSCell>();
            foreach (TestPoint tp in testPoints)
            {
                LTECell mainCell = tp.GetMainCell_LTE();
                if (mainCell != null)
                {
                    if (!sCellDic.ContainsKey(mainCell))
                    {
                        sCellDic[mainCell] = new WeakCoverRoadLTEAssocSCell(mainCell);
                    }
                    sCellDic[mainCell].TestPoints.Add(tp);

                    LTECell topNbCell = null;
                    float topNbRsrp = float.MinValue;
                    getNBCellInfo(tp, ref topNbCell, ref topNbRsrp);

                    if (topNbCell != null)
                    {
                        sCellDic[mainCell].AddNbCell(topNbCell, tp, topNbRsrp);
                    }
                }
            }

            List<WeakCoverRoadLTEAssocSCell> cellList = new List<WeakCoverRoadLTEAssocSCell>(sCellDic.Values);
            sCellDic.Clear();
            return cellList;
        }

        private void getNBCellInfo(TestPoint tp, ref LTECell topNbCell, ref float topNbRsrp)
        {
            for (int i = 0; i < 6; ++i)
            {
                LTECell nbCell = tp.GetNBCell_LTE(i);
                if (nbCell != null)
                {
                    float? nbRsrp = (float?)tp[tpStr, i];
                    if (nbRsrp != null && topNbRsrp < nbRsrp)
                    {
                        topNbCell = nbCell;
                        topNbRsrp = (float)nbRsrp;
                    }
                }
            }
        }

        public WeakCoverRoadCondition_LTE weakCovRoadCond { get; set; } = new WeakCoverRoadCondition_LTE();
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>
                {
                    ["BackgroundStat"] = BackgroundStat,
                    ["MaxRSRP"] = weakCovRoadCond.MaxRSRP,
                    ["MinCoverRoadDistance"] = weakCovRoadCond.MinCoverRoadDistance,
                    ["MaxTPDistance"] = weakCovRoadCond.MaxTPDistance,
                    ["CheckAndOr"] = weakCovRoadCond.CheckCondAnd,
                    ["CheckSINR"] = weakCovRoadCond.CheckSINR,
                    ["MaxSINR"] = weakCovRoadCond.MaxSINR,
                    ["CheckNbMaxRSRP"] = weakCovRoadCond.CheckNbMaxRSRP,
                    ["compareSymble"] = "<"
                };
                if (weakCovRoadCond.compareSymble == CompareSymble.GreaterThan)
                {
                    param["compareSymble"] = ">";
                }
                param["NbMaxRSRP"] = weakCovRoadCond.NbMaxRSRP;
                param["MaxSampleCellDistance"] = weakCovRoadCond.MaxSampleCellDistance;
                param["MaxSampleCellAngle"] = weakCovRoadCond.MaxSampleCellAngle;
                param["MinWeakPointPercent"] = weakCovRoadCond.MinWeakPointPercent;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                setParam(param);
            }
        }

        private void setParam(Dictionary<string, object> param)
        {
            BackgroundStat = getValidData(param, "BackgroundStat", BackgroundStat);
            weakCovRoadCond.MaxRSRP = getValidData(param, "MaxRSRP", weakCovRoadCond.MaxRSRP);
            weakCovRoadCond.MinCoverRoadDistance = getValidData(param, "MinCoverRoadDistance", weakCovRoadCond.MinCoverRoadDistance);
            weakCovRoadCond.MaxTPDistance = getValidData(param, "MaxTPDistance", weakCovRoadCond.MaxTPDistance);

            weakCovRoadCond.CheckCondAnd = getValidData(param, "CheckAndOr", weakCovRoadCond.CheckCondAnd);
            weakCovRoadCond.CheckSINR = getValidData(param, "CheckSINR", weakCovRoadCond.CheckSINR);
            weakCovRoadCond.MaxSINR = getValidData(param, "MaxSINR", weakCovRoadCond.MaxSINR);
            weakCovRoadCond.CheckNbMaxRSRP = getValidData(param, "CheckNbMaxRSRP", weakCovRoadCond.CheckNbMaxRSRP);

            if (param.ContainsKey("compareSymble"))
            {
                if (param["compareSymble"].ToString().Contains("<"))
                {
                    weakCovRoadCond.compareSymble = CompareSymble.LessThan;
                }
                else if (param["compareSymble"].ToString().Contains(">"))
                {
                    weakCovRoadCond.compareSymble = CompareSymble.GreaterThan;
                }
            }

            weakCovRoadCond.NbMaxRSRP = getValidData(param, "NbMaxRSRP", weakCovRoadCond.NbMaxRSRP);
            weakCovRoadCond.MaxSampleCellDistance = getValidData(param, "MaxSampleCellDistance", weakCovRoadCond.MaxSampleCellDistance);
            weakCovRoadCond.MaxSampleCellAngle = getValidData(param, "MaxSampleCellAngle", weakCovRoadCond.MaxSampleCellAngle);
            weakCovRoadCond.MinWeakPointPercent = getValidData(param, "MinWeakPointPercent", weakCovRoadCond.MinWeakPointPercent);
        }

        private bool getValidData(Dictionary<string, object> param, string name, bool defaultValue)
        {
            if (param.ContainsKey(name))
            {
                return (bool)param[name];
            }
            return defaultValue;
        }

        private int getValidData(Dictionary<string, object> param, string name, double defaultValue)
        {
            if (param.ContainsKey(name))
            {
                return int.Parse(param[name].ToString());
            }
            return (int)defaultValue;
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new WeakCovRoadProperties_LTE(this);
            }
        }
        public int ISnIndex { get; set; } = 1;
        protected override void getReadyBeforeQuery()
        {
            ISnIndex = 1;
        }
        protected override void saveBackgroundData()
        {
            doSomethingAfterAna();
            if (weakCovRoadCond.IsAssocAnalysis)
            {
                DoAssociationAnalysis();
            }
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (WeakCoverRoadLTE item in weakCoverList)
            {
                if (!weakCovRoadCond.IsAssocAnalysis)
                {
                    BackgroundResult result = item.ConvertToBackgroundResult();
                    result.SN = ISnIndex++;
                    result.SubFuncID = GetSubFuncID();
                    bgResultList.Add(result);
                }
                else
                {
                    List<BackgroundResult> resultList = item.ConvertToBackgroundResultList(item);
                    foreach (BackgroundResult result in resultList)
                    {
                        result.SubFuncID = GetSubFuncID();
                        bgResultList.Add(result);
                    }
                }
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Road(GetSubFuncID(), curAnaFileInfo, bgResultList);
            weakCoverList.Clear();
        }
        protected override void initBackgroundImageDesc()
        {
            int iSn = 1;
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                float fSampleCountOp = bgResult.GetImageValueFloat();
                string strLacCi_opt = bgResult.GetImageValueString();
                string strCellName_opt = bgResult.GetImageValueString();
                float fSampleCountPlan = bgResult.GetImageValueFloat();
                string strLacCi_plan = bgResult.GetImageValueString();
                string strCellName_plan = bgResult.GetImageValueString();
                int IMaxNBCellCount = bgResult.GetImageValueInt();
                string strMaxRsrpCellName = bgResult.GetImageValueString();
                string strMaxNBCellMinDistince = bgResult.GetImageValueString();
                float MaxNbRsrp = bgResult.GetImageValueFloat();
                float MinNbRsrp = bgResult.GetImageValueFloat();
                float AvgNbRsrp = bgResult.GetImageValueFloat();
                bgResult.GetImageValueFloat();//MaxSINR
                bgResult.GetImageValueFloat();//MinSINR
                bgResult.GetImageValueFloat();//AvgSINR
                float avgSpeed = bgResult.GetImageValueFloat();
                float weakPointPercent = bgResult.GetImageValueFloat();

                string strProject = "";

                StringBuilder sb = new StringBuilder();
                sb.Append("序号：");
                sb.Append(iSn++);
                sb.Append("\r\n");
                sb.Append("优化问题比例：");
                sb.Append(fSampleCountOp);
                sb.Append("%");
                sb.Append("\r\n");
                sb.Append("TAC-CI(优化)：");
                sb.Append(strLacCi_opt);
                sb.Append("\r\n");
                sb.Append("小区名(优化)：");
                sb.Append(strCellName_opt);
                sb.Append("\r\n");
                sb.Append("规划问题比例：");
                sb.Append(fSampleCountPlan);
                sb.Append("%");
                sb.Append("\r\n");
                sb.Append("TAC-CI(规划)：");
                sb.Append(strLacCi_plan);
                sb.Append("\r\n");
                sb.Append("小区名(规划)：");
                sb.Append(strCellName_plan);
                sb.Append("\r\n");
                sb.Append("最强邻区数(去重)：");
                sb.Append(IMaxNBCellCount);
                sb.Append("\r\n");
                sb.Append("最强邻区名称：");
                sb.Append(strMaxRsrpCellName);
                sb.Append("\r\n");
                sb.Append("最强邻区与中心点距离：");
                sb.Append(strMaxNBCellMinDistince);
                sb.Append("\r\n");
                sb.Append("最强邻区最大场强：");
                sb.Append(MaxNbRsrp);
                sb.Append("\r\n");
                sb.Append("最强邻区最小场强：");
                sb.Append(MinNbRsrp);
                sb.Append("\r\n");
                sb.Append("最强邻区平均场强：");
                sb.Append(AvgNbRsrp);
                sb.Append("\r\n平均速率：");
                if (avgSpeed < 0)
                {
                    sb.Append("-");
                }
                else
                {
                    sb.Append(avgSpeed);
                }
                sb.Append("\r\n弱覆盖占比：");
                sb.Append(weakPointPercent + "%");

                if (weakCovRoadCond.IsAssocAnalysis)
                {
                    string MainCellName = bgResult.GetImageValueString();
                    string TopNbName = bgResult.GetImageValueString();
                    string IsNbRelationCorrect = bgResult.GetImageValueString();
                    string IsNbCellAlarm = bgResult.GetImageValueString();
                    string IsNbOcnCorrect = bgResult.GetImageValueString();
                    string IsNbOcsCorrect = bgResult.GetImageValueString();
                    string IsExistRadiusSite = bgResult.GetImageValueString();
                    string AlarmSiteName = bgResult.GetImageValueString();
                    string IsPaCorrect = bgResult.GetImageValueString();
                    string IsPbCorrect = bgResult.GetImageValueString();
                    string IsRefSignalCorrect = bgResult.GetImageValueString();
                    string DistanceCenter = bgResult.GetImageValueString();
                    string AngleCenter = bgResult.GetImageValueString();
                    string Direction = bgResult.GetImageValueString();
                    string Downward = bgResult.GetImageValueString();
                    string Altitude = bgResult.GetImageValueString();
                    sb.Append("\r\n");
                    sb.Append("主服小区：");
                    sb.Append(MainCellName);
                    sb.Append("\r\n");
                    sb.Append("最强邻区：");
                    sb.Append(TopNbName);
                    sb.Append("\r\n");
                    sb.Append("是否添加邻区关系：");
                    sb.Append(IsNbRelationCorrect);
                    sb.Append("\r\n");
                    sb.Append("最强邻区是否告警：");
                    sb.Append(IsNbCellAlarm);
                    sb.Append("\r\n");
                    sb.Append("最强邻区偏移量：");
                    sb.Append(IsNbOcnCorrect);
                    sb.Append("\r\n");
                    sb.Append("最强邻区偏置：");
                    sb.Append(IsNbOcsCorrect);
                    sb.Append("\r\n");
                    sb.Append("半径1000米范围内是否基站：");
                    sb.Append(IsExistRadiusSite);
                    sb.Append("\r\n");
                    sb.Append("半径1000米范围内基站是否告警：");
                    sb.Append(AlarmSiteName);
                    sb.Append("\r\n");
                    sb.Append("主服PA是否合理：");
                    sb.Append(IsPaCorrect);
                    sb.Append("\r\n");
                    sb.Append("主服PB是否合理：");
                    sb.Append(IsPbCorrect);
                    sb.Append("\r\n");
                    sb.Append("主服是否满功率配置：");
                    sb.Append(IsRefSignalCorrect);
                    sb.Append("\r\n");
                    sb.Append("主服与路段中心距离：");
                    sb.Append(DistanceCenter);
                    sb.Append("\r\n");
                    sb.Append("主服与路段中心夹角：");
                    sb.Append(AngleCenter);
                    sb.Append("\r\n");
                    sb.Append("主服方位角：");
                    sb.Append(Direction);
                    sb.Append("\r\n");
                    sb.Append("主服下倾角：");
                    sb.Append(Downward);
                    sb.Append("\r\n");
                    sb.Append("主服挂高：");
                    sb.Append(Altitude);
                }
                strProject = bgResult.GetImageValueString();
                if (strProject != "" && !BackgroundFuncBaseSetting.GetInstance().projectType.Equals(strProject))
                {
                    continue;
                }
                if (!string.IsNullOrEmpty(strProject))
                {
                    sb.Append("\r\n");
                    sb.Append("项目ID：");
                    sb.Append(strProject.Replace(",","，"));
                }
                bgResult.ImageDesc = sb.ToString();
            }
        }
    }

    public class ZTLTEFDDWeakCoverRoadQuery : ZTLTEWeakCoverRoadQuery
    {
        private WeakCoverRoadLTEAssocCondtion assocCond { get; set; }

        public ZTLTEFDDWeakCoverRoadQuery()
        {
            //没有使用到
        }

        public ZTLTEFDDWeakCoverRoadQuery(MainModel mainModel)
            : base()
        {
            init();
        }

        protected new void init()
        {
            name = "弱覆盖路段_LTE_FDD";
            type = 2;
            funcId = 26000;
            subfuncId = 26003;
            desc = "分析";
            tpStr = "lte_fdd_NCell_RSRP";
            tpRSRP = "lte_fdd_RSRP";
            tpSINR = "lte_fdd_SINR";
            tpTac = "lte_fdd_TAC";
            tpSpeed = "lte_fdd_APP_Speed_Mb";
            themeName = "LTE_FDD:RSRP";
            tpNCell_EARFCN = "lte_fdd_NCell_EARFCN";
            tpNCell_PCI = "lte_fdd_NCell_PCI";
            tpAppType = "lte_fdd_APP_type";

            assocCond = new WeakCoverRoadLTEAssocCondtion
            {
                NbRsrpThreshold = this.weakCondition.MaxRSRP,
                SiteRadius = 1000,
                RefSignalPower = 92
            };
        }
        protected override List<WeakCoverRoadLTEAssocSCell> FindAssociationCells(List<TestPoint> testPoints)
        {
            Dictionary<LTECell, WeakCoverRoadLTEAssocSCell> sCellDic = new Dictionary<LTECell, WeakCoverRoadLTEAssocSCell>();
            foreach (TestPoint tp in testPoints)
            {
                LTECell mainCell = tp.GetMainCell_LTE_FDD();//与LTE_TDD不同
                if (mainCell != null)
                {
                    if (!sCellDic.ContainsKey(mainCell))
                    {
                        sCellDic[mainCell] = new WeakCoverRoadLTEAssocSCell(mainCell);
                    }
                    sCellDic[mainCell].TestPoints.Add(tp);

                    LTECell topNbCell = null;
                    float topNbRsrp = float.MinValue;
                    getNBCellInfo(tp, ref topNbCell, ref topNbRsrp);

                    if (topNbCell != null)
                    {
                        sCellDic[mainCell].AddNbCell(topNbCell, tp, topNbRsrp);
                    }
                }
            }

            List<WeakCoverRoadLTEAssocSCell> cellList = new List<WeakCoverRoadLTEAssocSCell>(sCellDic.Values);
            sCellDic.Clear();
            return cellList;
        }

        private void getNBCellInfo(TestPoint tp, ref LTECell topNbCell, ref float topNbRsrp)
        {
            for (int i = 0; i < 6; ++i)
            {
                LTECell nbCell = tp.GetNBCell_LTE_FDD(i);//与LTE_TDD不同
                if (nbCell != null)
                {
                    float? nbRsrp = (float?)tp[tpStr, i];
                    if (nbRsrp != null && topNbRsrp < nbRsrp)
                    {
                        topNbCell = nbCell;
                        topNbRsrp = (float)nbRsrp;
                    }
                }
            }
        }
    }
}
