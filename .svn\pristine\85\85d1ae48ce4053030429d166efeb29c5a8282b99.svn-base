﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Net
{
    public class DIYAnalyseByCellBackgroundBaseByFile : DIYAnalyseFilesOneByOneByRegion
    {
        /// <summary>
        /// 路段长度
        /// </summary>
        public int ValidDistance { get; set; } = 50;
        protected FileInfo curAnaFileInfo;
        public DIYAnalyseByCellBackgroundBaseByFile(MainModel mainModel)
            : base(mainModel)
        {
        }

        public DIYAnalyseByCellBackgroundBaseByFile()
          : base(MainModel.GetInstance())
        {
        }

        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Road; }
        }

        protected override void query()
        {
            if (MainModel.IsBackground && !MainModel.BackgroundStarted)
            {
                return;
            }
            if (!getCondition())
            {
                return;
            }
            getReadyBeforeQuery();
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
            }
            catch
            {
                //continue
            }
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            bool drawServer = MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer;
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = false;
            if (!MainModel.IsBackground)
            {
                if (!MainModel.QueryFromBackground)
                {
                    queryFileToAnalyse();
                    WaitBox.CanCancel = true;
                    WaitBox.Show("开始分析文件...", analyseFiles);
                    DoWaitBoxAfterGetResults();
                }
                else
                {
                    getBackgroundData();
                }
                MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = drawServer;
                if (!MainModel.QueryFromBackground)
                {
                    MainModel.FireDTDataChanged(this);
                    fireShowForm();
                    fireSetDefaultMapSerialTheme();
                }
                else
                {
                    initBackgroundImageDesc();
                }
            }
            else
            {
                doBackgroundStatByFile(clientProxy);
                clientProxy.Close();
            }
        }

        protected override void statData(ClientProxy clientProxy)
        {
            getFilesForAnalyse();
            analyseFiles();
        }

        protected virtual void doBackgroundStatByFile(ClientProxy clientProxy)
        {
            QueryCondition cond = new QueryCondition();
            cond.Geometorys = new SearchGeometrys();
            cond.Geometorys.Region = BackgroundFuncConfigManager.GetInstance().RegionBorder;
            SetQueryCondition(cond);
            getFilesForAnalyse();
            analyseFiles();
        }

        protected virtual void fireSetDefaultMapSerialTheme()
        {
        }

        protected override void getBackgroundData()
        {
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetResult_Road(Condition.Periods[0].BeginTime,
                Condition.Periods[0].EndTime, GetSubFuncID(), Name, StatType, BackgroundFuncConfigManager.GetInstance().ProjectType);
        }

        protected virtual void getFilesForAnalyse()
        {
            BackgroundFuncQueryManager.GetInstance().GetFile_Road(GetSubFuncID(), ServiceTypeString, ((int)carrierID).ToString());
            /**
            Condition.DistrictIDs.Clear();
            Condition.DistrictIDs.Add(MainModel.DistrictID);
            queryFileToAnalyse();
            List<int> statedFileIDs = BackgroundFuncQueryManager.GetInstance().GetStatedFileID_Cell(GetSubFuncID());
            for (int i = 0; i < MainModel.FileInfos.Count; ++i)
            {
                if (statedFileIDs.Contains(MainModel.FileInfos[i].ID))
                {
                    MainModel.FileInfos.RemoveAt(i);
                    --i;
                }
            }
             */
        }

        protected override void analyseFiles()
        {
            try
            {
                List<FileInfo> files = new List<FileInfo>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0 && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                    {
                        continue;
                    }
                    files.Add(fileInfo);
                }
                clearDataBeforeAnalyseFiles();
                int iloop = 0;
                if (MainModel.IsBackground)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("共读取待分析文件" + files.Count + "个...");
                }
                foreach (FileInfo fileInfo in files)
                {
                    bool isStop = showInfo(files, ref iloop, fileInfo);
                    if (isStop)
                    {
                        break;
                    }
                    if (filterFile(fileInfo))
                    {
                        continue;
                    }
                    curAnaFileInfo = fileInfo;
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    replay();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        protected bool showInfo(List<FileInfo> files, ref int iloop, FileInfo fileInfo)
        {
            if (MainModel.IsBackground)
            {
                //if (!(this is DIYAnalyseByCellBackgroundBaseByFileOfPeriod) && MainModel.BackgroundStopRequest)
                //{
                //    break;
                //}
                bool isStop = stopQuery();
                if (isStop)
                {
                    return true;
                }
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo("正在分析 " + FuncType.ToString() +
                    SubFuncType.ToString() + " 类 " + Name + "，当前文件 " + (++iloop) + "/" + files.Count +
                    "个...文件名：" + fileInfo.Name);
            }
            else
            {
                WaitBox.Text = "正在分析文件( " + (++iloop) + "/" + files.Count + " )...";
                WaitBox.ProgressPercent = (int)(iloop * 100.0 / files.Count);
            }
            return false;
        }

        protected virtual bool stopQuery()
        {
            if (MainModel.BackgroundStopRequest)
            {
                return true;
            }
            return false;
        }

        protected virtual void doSomethingAfterAnalyseFiles()
        {
            if (!MainModel.IsBackground)
            {
                getResultsAfterQuery();
            }
        }

        protected virtual bool filterFile(FileInfo fileInfo)
        {
            return false;
        }
    }
}
