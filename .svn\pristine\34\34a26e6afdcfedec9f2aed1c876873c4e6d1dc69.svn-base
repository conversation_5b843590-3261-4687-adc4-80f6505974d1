﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LowSpeedCellDlg_NR : BaseDialog
    {
        public LowSpeedCellDlg_NR()
        {
            InitializeComponent();
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            cmbThroughputType.SelectedIndex = 0;
        }

        public void SetCondition(LowSpeedCellCond_NR cond)
        {
            if(cond == null)
            {
                return;
            }

            numMinSinr.Value = (decimal)cond.MinSinr;
            numMaxSinr.Value = (decimal)cond.MaxSinr;
            numMinRsrp.Value = (decimal)cond.MinRsrp;
            numMaxRsrp.Value = (decimal)cond.MaxRsrp;

            radioApp.Checked = cond.IsAppSpeed;
            radioThroughput.Checked = !radioApp.Checked;

            chkFTPDL.Checked = cond.FtpDL.IsChecked;
            numFTPDLMax.Value = (decimal)cond.FtpDL.MaxSpeed;
            numFTPDLMin.Value = (decimal)cond.FtpDL.MinSpeed;

            chkFTPUL.Checked = cond.FtpUL.IsChecked;
            numFTPULMax.Value = (decimal)cond.FtpUL.MaxSpeed;
            numFTPULMin.Value = (decimal)cond.FtpUL.MinSpeed;

            chkHttp.Checked = cond.HttpDL.IsChecked;
            numHTTPMax.Value = (decimal)cond.HttpDL.MaxSpeed;
            numHTTPMin.Value = (decimal)cond.HttpDL.MinSpeed;

            chkEmail.Checked = cond.EmailSMTP.IsChecked;
            numEmailMax.Value = (decimal)cond.EmailSMTP.MaxSpeed;
            numEmailMin.Value = (decimal)cond.EmailSMTP.MinSpeed;

            if (cmbThroughputType.SelectedItem.ToString() == "MAC")
            {
                chkThroughputDL.Checked = cond.MacDL.IsChecked;
                numThroughputDLMax.Value = (decimal)cond.MacDL.MaxSpeed;
                numThroughputDLMin.Value = (decimal)cond.MacDL.MinSpeed;

                chkThroughputUL.Checked = cond.MacUL.IsChecked;
                numThroughputULMax.Value = (decimal)cond.MacUL.MaxSpeed;
                numThroughputULMin.Value = (decimal)cond.MacUL.MinSpeed;
            }
            else
            {
                chkThroughputDL.Checked = cond.PdcpDL.IsChecked;
                numThroughputDLMax.Value = (decimal)cond.PdcpDL.MaxSpeed;
                numThroughputDLMin.Value = (decimal)cond.PdcpDL.MinSpeed;

                chkThroughputUL.Checked = cond.PdcpUL.IsChecked;
                numThroughputULMax.Value = (decimal)cond.PdcpUL.MaxSpeed;
                numThroughputULMin.Value = (decimal)cond.PdcpUL.MinSpeed;
            }

            numProblemCount.Value = (decimal)cond.MinProblemCount;
            numProblemRate.Value = (decimal)cond.MinProblemRate*100;
        }

        public LowSpeedCellCond_NR GetCondition()
        {
            LowSpeedCellCond_NR cond = new LowSpeedCellCond_NR();
            cond.MinSinr = (double)numMinSinr.Value;
            cond.MaxSinr = (double)numMaxSinr.Value;
            cond.MinRsrp = (double)numMinRsrp.Value;
            cond.MaxRsrp = (double)numMaxRsrp.Value;

            cond.IsAppSpeed = radioApp.Checked;

            cond.MinProblemCount = (int)numProblemCount.Value;
            cond.MinProblemRate = (double)numProblemRate.Value / 100;

            cond.FtpDL.IsChecked = chkFTPDL.Checked;
            cond.FtpDL.MaxSpeed = (double)numFTPDLMax.Value;
            cond.FtpDL.MinSpeed = (double)numFTPDLMin.Value;

            cond.FtpUL.IsChecked = chkFTPUL.Checked;
            cond.FtpUL.MaxSpeed = (double)numFTPULMax.Value;
            cond.FtpUL.MinSpeed = (double)numFTPULMin.Value;

            cond.HttpDL.IsChecked = chkHttp.Checked;
            cond.HttpDL.MaxSpeed = (double)numHTTPMax.Value;
            cond.HttpDL.MinSpeed = (double)numHTTPMin.Value;

            cond.EmailSMTP.IsChecked = chkEmail.Checked;
            cond.EmailSMTP.MaxSpeed = (double)numEmailMax.Value;
            cond.EmailSMTP.MinSpeed = (double)numEmailMin.Value;

            if (cmbThroughputType.SelectedItem.ToString() == "MAC")
            {
                cond.ThroughputTypeInfo = LowSpeedCellCond_NR.ThroughputType.MAC;
                cond.MacDL.IsChecked = chkThroughputDL.Checked;
                cond.MacDL.MaxSpeed = (double)numThroughputDLMax.Value;
                cond.MacDL.MinSpeed = (double)numThroughputDLMin.Value;

                cond.MacUL.IsChecked = chkThroughputUL.Checked;
                cond.MacUL.MaxSpeed = (double)numThroughputULMax.Value;
                cond.MacUL.MinSpeed = (double)numThroughputULMin.Value;
            }
            else
            {
                cond.ThroughputTypeInfo = LowSpeedCellCond_NR.ThroughputType.PDCP;
                cond.PdcpDL.IsChecked = chkThroughputDL.Checked;
                cond.PdcpDL.MaxSpeed = (double)numThroughputDLMax.Value;
                cond.PdcpDL.MinSpeed = (double)numThroughputDLMin.Value;

                cond.PdcpUL.IsChecked = chkThroughputUL.Checked;
                cond.PdcpUL.MaxSpeed = (double)numThroughputULMax.Value;
                cond.PdcpUL.MinSpeed = (double)numThroughputULMin.Value;
            }

            return cond;
        }

        private void BtnOK_Click(object sender , EventArgs e)
        {
            bool isValid = judgeValidCheck();
            if (!isValid)
            {
                MessageBox.Show("请至少选择一种速率进行分析！");
                return;
            }
            isValid = judgeValidValue();
            if (!isValid)
            {
                MessageBox.Show("最小值必须小于最大值", "设置错误");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private bool judgeValidCheck()
        {
            if (radioApp.Checked)
            {
                if (!(chkFTPDL.Checked || chkFTPUL.Checked || chkHttp.Checked || chkEmail.Checked))
                {
                    return false;
                }
            }
            else
            {
                if (!(chkThroughputDL.Checked || chkThroughputUL.Checked))
                {
                    return false;
                }
            }
            return true;
        }

        private bool judgeValidValue()
        {
            if (radioApp.Checked)
            {
                if (numFTPDLMin.Value > numFTPDLMax.Value
                    || numFTPULMin.Value > numFTPULMax.Value
                    || numHTTPMin.Value > numHTTPMax.Value
                    || numEmailMin.Value > numEmailMax.Value)
                {
                    return false;
                }
            }
            else
            {
                if (numThroughputDLMin.Value > numThroughputDLMax.Value
                    || numThroughputULMin.Value > numThroughputULMax.Value)
                {
                    return false;
                }
            }
            if (numMinSinr.Value > numMaxSinr.Value
               || numMinRsrp.Value > numMaxRsrp.Value)
            {
                return false;
            }
            return true;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void chkFTP_CheckedChanged(object sender, EventArgs e)
        {
            numFTPDLMin.Enabled = numFTPDLMax.Enabled = chkFTPDL.Checked;
        }

        private void chkFTPUpLoad_CheckedChanged(object sender, EventArgs e)
        {
            numFTPULMin.Enabled = numFTPULMax.Enabled = chkFTPUL.Checked;
        }

        private void chkHttp_CheckedChanged(object sender, EventArgs e)
        {
            numHTTPMin.Enabled = numHTTPMax.Enabled = chkHttp.Checked;
        }

        private void chkEmail_CheckedChanged(object sender, EventArgs e)
        {
            numEmailMin.Enabled = numEmailMax.Enabled = chkEmail.Checked;
        }

        private void radioApp_CheckedChanged(object sender, EventArgs e)
        {
            grpApp.Enabled = radioApp.Checked;
            grpThroughput.Enabled = !grpApp.Enabled;
        }
    }

    public class LowSpeedCellCond_NR
    {
        public class SpeedInfo
        { 
            public bool IsChecked { get; set; }
            public double MinSpeed { get; set; }
            public double MaxSpeed { get; set; }

            public SpeedInfo(bool isChecked, double minSpeed, double maxSpeed)
            {
                IsChecked = isChecked;
                MinSpeed = minSpeed;
                MaxSpeed = maxSpeed;
            }
        }

        public enum ThroughputType
        { 
            MAC,
            PDCP
        }

        public SpeedInfo FtpDL { get; set; } = new SpeedInfo(true, 0, 100);
        public SpeedInfo FtpUL { get; set; } = new SpeedInfo(true, 0, 2);
        public SpeedInfo HttpDL { get; set; } = new SpeedInfo(true, 0, 100);
        public SpeedInfo EmailSMTP { get; set; } = new SpeedInfo(true, 0, 100);

        public SpeedInfo PdcpDL { get; set; } = new SpeedInfo(true, 0, 100);
        public SpeedInfo PdcpUL { get; set; } = new SpeedInfo(true, 0, 2);
        public SpeedInfo MacDL { get; set; } = new SpeedInfo(true, 0, 100);
        public SpeedInfo MacUL { get; set; } = new SpeedInfo(true, 0, 2);

        public double MinSinr { get; set; }
        public double MaxSinr { get; set; }
        public double MinRsrp { get; set; }
        public double MaxRsrp { get; set; }
        public int MinProblemCount { get; set; }
        public double MinProblemRate { get; set; }
        /// <summary>
        /// 是否分析应用层速率
        /// </summary>
        public bool IsAppSpeed{ get; set; }
        /// <summary>
        /// 吞吐率类型
        /// </summary>
        public ThroughputType ThroughputTypeInfo { get; set; }

        public class SpeedTypeInfo
        {
            public SpeedType Type { get; set; }
            public double Speed { get; set; }
            public bool IsValid { get; set; }

            public SpeedTypeInfo(SpeedType type, double speed, bool isValid)
            {
                Type = type;
                Speed = speed;
                IsValid = isValid;
            }
        }

        public List<SpeedTypeInfo> GetValidSpeed(TestPoint tp)
        {
            short? type = NRTpHelper.NrTpManager.GetAppType(tp);
            if (type == null)
            {
                return new List<SpeedTypeInfo>();
            }

            List<SpeedTypeInfo> speedTypeInfoList = new List<SpeedTypeInfo>();
            if (this.IsAppSpeed)
            {
                double? speed = NRTpHelper.NrTpManager.GetAppSpeedMb(tp);
                if (speed == null || speed <= 0)
                {
                    return new List<SpeedTypeInfo>();
                }

                addValidAppSpeed(speedTypeInfoList, type, (double)speed);
            }
            else
            {
                int? status = NRTpHelper.NrTpManager.GetAppStatus(tp);
                if (status == null)
                {
                    return new List<SpeedTypeInfo>();
                }

                getThroughputSpeed(tp, type, status, speedTypeInfoList);
            }

            return speedTypeInfoList;
        }

        private void getThroughputSpeed(TestPoint tp, short? type, int? status, List<SpeedTypeInfo> speedTypeInfoList)
        {
            double? speed = null;
            if (type == (int)AppType.FTP_Download
                && (status == (int)EAppStatus.FTPSTATUS_DOWNLOAD_CONTINUE
                || status == (int)EAppStatus.FTPSTATUS_DOWNLOAD_Qos))
            {
                if (ThroughputTypeInfo == ThroughputType.MAC)
                {
                    speed = NRTpHelper.NrTpManager.GetMacDLMb(tp);
                    addValidThroughputSpeed(speedTypeInfoList, speed, MacDL, SpeedType.MAC_DL);
                }
                else
                {
                    speed = NRTpHelper.NrTpManager.GetPdcpDLMb(tp);
                    addValidThroughputSpeed(speedTypeInfoList, speed, PdcpDL, SpeedType.PDCP_DL);
                }
            }
            else if (type == (int)AppType.FTP_Upload
                && (status == (int)EAppStatus.FTPSTATUS_UPLOAD_CONTINUE
                || status == (int)EAppStatus.FTPSTATUS_UPLOAD_Qos))
            {
                if (ThroughputTypeInfo == ThroughputType.MAC)
                {
                    speed = NRTpHelper.NrTpManager.GetMacULMb(tp);
                    addValidThroughputSpeed(speedTypeInfoList, speed, MacUL, SpeedType.MAC_UL);
                }
                else
                {
                    speed = NRTpHelper.NrTpManager.GetPdcpULMb(tp);
                    addValidThroughputSpeed(speedTypeInfoList, speed, PdcpUL, SpeedType.PDCP_UL);
                }
            }
        }

        private void addValidAppSpeed(List<SpeedTypeInfo> speedTypeInfoList, short? type, double speed)
        {
            if (type == (int)AppType.FTP_Download && FtpDL.IsChecked)
            {
                bool isValid = FtpDL.MinSpeed <= speed && speed <= FtpDL.MaxSpeed;
                SpeedTypeInfo speedTypeInfo = new SpeedTypeInfo(SpeedType.FTP_DL, speed, isValid);
                speedTypeInfoList.Add(speedTypeInfo);
            }
            else if (type == (int)AppType.FTP_Upload && FtpUL.IsChecked)
            {
                bool isValid = FtpUL.MinSpeed <= speed && speed <= FtpUL.MaxSpeed;
                SpeedTypeInfo speedTypeInfo = new SpeedTypeInfo(SpeedType.FTP_UL, speed, isValid);
                speedTypeInfoList.Add(speedTypeInfo);
            }
            else if (type == (int)AppType.Http_Download && HttpDL.IsChecked)
            {
                bool isValid = HttpDL.MinSpeed <= speed && speed <= HttpDL.MaxSpeed;
                SpeedTypeInfo speedTypeInfo = new SpeedTypeInfo(SpeedType.HTTP_DL, speed, isValid);
                speedTypeInfoList.Add(speedTypeInfo);
            }
            else if (type == (int)AppType.Email_SMTP && EmailSMTP.IsChecked)
            {
                bool isValid = EmailSMTP.MinSpeed <= speed && speed <= EmailSMTP.MaxSpeed;
                SpeedTypeInfo speedTypeInfo = new SpeedTypeInfo(SpeedType.Email_SMTP, speed, isValid);
                speedTypeInfoList.Add(speedTypeInfo);
            }
        }

        private void addValidThroughputSpeed(List<SpeedTypeInfo> speedTypeInfoList, double? speed, SpeedInfo speedInfo, SpeedType type)
        {
            if (speedInfo.IsChecked && speed != null && speed >= 0)
            {
                bool isValid = speedInfo.MinSpeed <= speed && speed <= speedInfo.MaxSpeed;
                SpeedTypeInfo speedTypeInfo = new SpeedTypeInfo(type, (double)speed, isValid);
                speedTypeInfoList.Add(speedTypeInfo);
            }
        }
    }

    public enum SpeedType
    {
        [EnumDescription("FTP下载速率")]
        FTP_DL,
        [EnumDescription("FTP上传速率")]
        FTP_UL,
        [EnumDescription("HTTP下载速率")]
        HTTP_DL,
        [EnumDescription("Email速率")]
        Email_SMTP,
        [EnumDescription("MAC层下载速率")]
        MAC_DL,
        [EnumDescription("MAC层上传速率")]
        MAC_UL,
        [EnumDescription("PDCP层下载速率")]
        PDCP_DL,
        [EnumDescription("PDCP层上传速率")]
        PDCP_UL
    }
}
