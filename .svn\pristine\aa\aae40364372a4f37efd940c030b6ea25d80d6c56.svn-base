﻿using MasterCom.RAMS.Chris.Util;

namespace MasterCom.RAMS.ZTFunc
{
    partial class LTEEarfcnCoverageSettingBox
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            MasterCom.RAMS.Chris.Util.RangeSet rangeSet1 = new MasterCom.RAMS.Chris.Util.RangeSet();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.rangeSetSetting = new MasterCom.RAMS.Chris.Util.RangeSetSetting();
            this.grpEarfcnRange = new System.Windows.Forms.GroupBox();
            this.txtEarfcn1800 = new System.Windows.Forms.TextBox();
            this.txtEarfcn900 = new System.Windows.Forms.TextBox();
            this.txtEarfcnF = new System.Windows.Forms.TextBox();
            this.txtEarfcnE = new System.Windows.Forms.TextBox();
            this.txtEarfcnD = new System.Windows.Forms.TextBox();
            this.chkBoxFDD1800 = new System.Windows.Forms.CheckBox();
            this.chkBoxFDD900 = new System.Windows.Forms.CheckBox();
            this.chkBoxEarfcnF = new System.Windows.Forms.CheckBox();
            this.chkBoxEarfcnE = new System.Windows.Forms.CheckBox();
            this.chkEarfcnD = new System.Windows.Forms.CheckBox();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.groupBox1.SuspendLayout();
            this.grpEarfcnRange.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.rangeSetSetting);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(375, 240);
            this.groupBox1.TabIndex = 1;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "RSRP区间设置";
            // 
            // rangeSetSetting
            // 
            this.rangeSetSetting.AutoRangeSet = null;
            this.rangeSetSetting.Location = new System.Drawing.Point(14, 20);
            this.rangeSetSetting.Name = "rangeSetSetting";
            this.rangeSetSetting.RangeSet = rangeSet1;
            this.rangeSetSetting.Size = new System.Drawing.Size(355, 209);
            this.rangeSetSetting.TabIndex = 0;
            // 
            // grpEarfcnRange
            // 
            this.grpEarfcnRange.Controls.Add(this.txtEarfcn1800);
            this.grpEarfcnRange.Controls.Add(this.txtEarfcn900);
            this.grpEarfcnRange.Controls.Add(this.txtEarfcnF);
            this.grpEarfcnRange.Controls.Add(this.txtEarfcnE);
            this.grpEarfcnRange.Controls.Add(this.txtEarfcnD);
            this.grpEarfcnRange.Controls.Add(this.chkBoxFDD1800);
            this.grpEarfcnRange.Controls.Add(this.chkBoxFDD900);
            this.grpEarfcnRange.Controls.Add(this.chkBoxEarfcnF);
            this.grpEarfcnRange.Controls.Add(this.chkBoxEarfcnE);
            this.grpEarfcnRange.Controls.Add(this.chkEarfcnD);
            this.grpEarfcnRange.Location = new System.Drawing.Point(12, 272);
            this.grpEarfcnRange.Name = "grpEarfcnRange";
            this.grpEarfcnRange.Size = new System.Drawing.Size(375, 166);
            this.grpEarfcnRange.TabIndex = 2;
            this.grpEarfcnRange.TabStop = false;
            this.grpEarfcnRange.Text = "EARFCN设置";
            // 
            // txtEarfcn1800
            // 
            this.txtEarfcn1800.Location = new System.Drawing.Point(85, 132);
            this.txtEarfcn1800.Name = "txtEarfcn1800";
            this.txtEarfcn1800.Size = new System.Drawing.Size(273, 21);
            this.txtEarfcn1800.TabIndex = 9;
            // 
            // txtEarfcn900
            // 
            this.txtEarfcn900.Location = new System.Drawing.Point(85, 104);
            this.txtEarfcn900.Name = "txtEarfcn900";
            this.txtEarfcn900.Size = new System.Drawing.Size(273, 21);
            this.txtEarfcn900.TabIndex = 8;
            // 
            // txtEarfcnF
            // 
            this.txtEarfcnF.Location = new System.Drawing.Point(85, 76);
            this.txtEarfcnF.Name = "txtEarfcnF";
            this.txtEarfcnF.Size = new System.Drawing.Size(273, 21);
            this.txtEarfcnF.TabIndex = 7;
            // 
            // txtEarfcnE
            // 
            this.txtEarfcnE.Location = new System.Drawing.Point(85, 48);
            this.txtEarfcnE.Name = "txtEarfcnE";
            this.txtEarfcnE.Size = new System.Drawing.Size(273, 21);
            this.txtEarfcnE.TabIndex = 6;
            // 
            // txtEarfcnD
            // 
            this.txtEarfcnD.Location = new System.Drawing.Point(85, 20);
            this.txtEarfcnD.Name = "txtEarfcnD";
            this.txtEarfcnD.Size = new System.Drawing.Size(273, 21);
            this.txtEarfcnD.TabIndex = 5;
            // 
            // chkBoxFDD1800
            // 
            this.chkBoxFDD1800.AutoSize = true;
            this.chkBoxFDD1800.Checked = true;
            this.chkBoxFDD1800.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkBoxFDD1800.Location = new System.Drawing.Point(13, 136);
            this.chkBoxFDD1800.Name = "chkBoxFDD1800";
            this.chkBoxFDD1800.Size = new System.Drawing.Size(66, 16);
            this.chkBoxFDD1800.TabIndex = 4;
            this.chkBoxFDD1800.Text = "FDD1800";
            this.chkBoxFDD1800.UseVisualStyleBackColor = true;
            // 
            // chkBoxFDD900
            // 
            this.chkBoxFDD900.AutoSize = true;
            this.chkBoxFDD900.Checked = true;
            this.chkBoxFDD900.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkBoxFDD900.Location = new System.Drawing.Point(13, 108);
            this.chkBoxFDD900.Name = "chkBoxFDD900";
            this.chkBoxFDD900.Size = new System.Drawing.Size(60, 16);
            this.chkBoxFDD900.TabIndex = 3;
            this.chkBoxFDD900.Text = "FDD900";
            this.chkBoxFDD900.UseVisualStyleBackColor = true;
            // 
            // chkBoxEarfcnF
            // 
            this.chkBoxEarfcnF.AutoSize = true;
            this.chkBoxEarfcnF.Checked = true;
            this.chkBoxEarfcnF.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkBoxEarfcnF.Location = new System.Drawing.Point(14, 80);
            this.chkBoxEarfcnF.Name = "chkBoxEarfcnF";
            this.chkBoxEarfcnF.Size = new System.Drawing.Size(42, 16);
            this.chkBoxEarfcnF.TabIndex = 2;
            this.chkBoxEarfcnF.Text = "F频";
            this.chkBoxEarfcnF.UseVisualStyleBackColor = true;
            // 
            // chkBoxEarfcnE
            // 
            this.chkBoxEarfcnE.AutoSize = true;
            this.chkBoxEarfcnE.Checked = true;
            this.chkBoxEarfcnE.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkBoxEarfcnE.Location = new System.Drawing.Point(13, 52);
            this.chkBoxEarfcnE.Name = "chkBoxEarfcnE";
            this.chkBoxEarfcnE.Size = new System.Drawing.Size(42, 16);
            this.chkBoxEarfcnE.TabIndex = 1;
            this.chkBoxEarfcnE.Text = "E频";
            this.chkBoxEarfcnE.UseVisualStyleBackColor = true;
            // 
            // chkEarfcnD
            // 
            this.chkEarfcnD.AutoSize = true;
            this.chkEarfcnD.Checked = true;
            this.chkEarfcnD.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkEarfcnD.Location = new System.Drawing.Point(14, 24);
            this.chkEarfcnD.Name = "chkEarfcnD";
            this.chkEarfcnD.Size = new System.Drawing.Size(42, 16);
            this.chkEarfcnD.TabIndex = 0;
            this.chkEarfcnD.Text = "D频";
            this.chkEarfcnD.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(207, 454);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(87, 27);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确认";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(300, 454);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(87, 27);
            this.btnCancel.TabIndex = 4;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // LTEEarfcnCoverageSettingBox
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(401, 489);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.grpEarfcnRange);
            this.Controls.Add(this.groupBox1);
            this.Name = "LTEEarfcnCoverageSettingBox";
            this.Text = "条件设置";
            this.groupBox1.ResumeLayout(false);
            this.grpEarfcnRange.ResumeLayout(false);
            this.grpEarfcnRange.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion
        private System.Windows.Forms.GroupBox groupBox1;

        private System.Windows.Forms.GroupBox grpEarfcnRange;
        private System.Windows.Forms.TextBox txtEarfcn1800;
        private System.Windows.Forms.TextBox txtEarfcn900;
        private System.Windows.Forms.TextBox txtEarfcnF;
        private System.Windows.Forms.TextBox txtEarfcnE;
        private System.Windows.Forms.TextBox txtEarfcnD;
        private System.Windows.Forms.CheckBox chkBoxFDD1800;
        private System.Windows.Forms.CheckBox chkBoxFDD900;
        private System.Windows.Forms.CheckBox chkBoxEarfcnF;
        private System.Windows.Forms.CheckBox chkBoxEarfcnE;
        private System.Windows.Forms.CheckBox chkEarfcnD;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private Chris.Util.RangeSetSetting rangeSetSetting;
    }
}