﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Model.Interface
{
    public class DIYSampleGroup : IComparable<DIYSampleGroup>
    {
        public DIYSampleGroup()
        {
            ColumnsDefSet = new List<DIYSampleParamDef>();
            Name = string.Empty;
            ThemeName = string.Empty;
        }
        
        public List<DIYSampleParamDef> ColumnsDefSet { get; set; }
        public string Name { get; set; }
        public string ThemeName { get; set; }
        public override string ToString()
        {
            return Name;
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["name"] = Name;
                param["themeName"] = ThemeName;
                List<object> columnsParams = new List<object>();
                param["columnsDef"] = columnsParams;
                foreach (DIYSampleParamDef cr in ColumnsDefSet)
                {
                    columnsParams.Add(cr.Param);
                }
                return param;
            }
            set
            {
                if(value.ContainsKey("name"))
                {
                    Name = (String)value["name"];
                }
                if (value.ContainsKey("themeName"))
                {
                    ThemeName = (string)value["themeName"];
                }
                ColumnsDefSet.Clear();
                List<object> columnsParams = (List<object>)value["columnsDef"];
                foreach (object o in columnsParams)
                {
                    Dictionary<string, object> rptParam = (Dictionary<string, object>)o;
                    DIYSampleParamDef cr = new DIYSampleParamDef();
                    cr.Param = rptParam;
                    ColumnsDefSet.Add(cr);
                }
            }
        }

        #region IComparable<DIYSampleGroup> 成员
        public int CompareTo(DIYSampleGroup other)
        {
            if (other == null)
            {
                return -1;
            }
            return Name.CompareTo(other.Name);
        }
        #endregion
    }
    public class DIYSampleParamDef
    {
        public DTParameter parameter { get; set; }
        public DIYSampleParamDef()
        {

        }
        public DIYSampleParamDef(DTParameter para)
        {
            parameter = para;
        }
        public override string ToString()
        {
            if(parameter!=null)
            {
                return parameter.Info.Name + (parameter.ArrayIndex > 0 ? "[" + parameter.ArrayIndex + "]":"");
            }
            else
            {
                return "";
            }
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                if(parameter!=null)
                {
                    param["param_name"] = parameter.Info.Name;
                    param["param_arg"] = parameter.ArrayIndex;
                }
                else
                {
                    param["param_name"] = "";
                    param["param_arg"] = -1;
                }
                return param;
            }
            set
            {
                string param_name = (String)value["param_name"];
                int param_arg = (int)value["param_arg"];
                if(param_name != null &&param_name!="")
                {
                    if(param_arg!=-1)
                    {
                        parameter = DTParameterManager.GetInstance().GetParameter(param_name, param_arg);
                    }
                    else
                    {
                        parameter = DTParameterManager.GetInstance().GetParameter(param_name);
                    }
                }
            }
        }
     
        
    }
}
