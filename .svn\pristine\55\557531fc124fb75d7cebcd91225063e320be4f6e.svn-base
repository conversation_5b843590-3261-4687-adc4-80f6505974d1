﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class FreqBandSetting
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.gvFreq = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gvFreqBand = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btnRemove = new System.Windows.Forms.Button();
            this.btnModifyFreqBand = new System.Windows.Forms.Button();
            this.btnAddFreqBand = new System.Windows.Forms.Button();
            this.GroupFreqBand = new DevExpress.XtraEditors.GroupControl();
            this.btnModifyFreq = new System.Windows.Forms.Button();
            this.btnAddFreq = new System.Windows.Forms.Button();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gvFreq)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvFreqBand)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.GroupFreqBand)).BeginInit();
            this.GroupFreqBand.SuspendLayout();
            this.SuspendLayout();
            // 
            // gvFreq
            // 
            this.gvFreq.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn3,
            this.gridColumn5});
            this.gvFreq.GridControl = this.gridControl1;
            this.gvFreq.Name = "gvFreq";
            this.gvFreq.OptionsDetail.ShowDetailTabs = false;
            this.gvFreq.OptionsView.ShowGroupPanel = false;
            this.gvFreq.OptionsView.ShowIndicator = false;
            this.gvFreq.RowClick += new DevExpress.XtraGrid.Views.Grid.RowClickEventHandler(this.gvFreq_RowClick);
            this.gvFreq.CellValueChanged += new DevExpress.XtraGrid.Views.Base.CellValueChangedEventHandler(this.gvFreq_CellValueChanged);
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "是否应用";
            this.gridColumn3.FieldName = "Enable";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 0;
            this.gridColumn3.Width = 73;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "频点范围";
            this.gridColumn5.FieldName = "FreqDesc";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.OptionsColumn.AllowEdit = false;
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 1;
            this.gridColumn5.Width = 332;
            // 
            // gridControl1
            // 
            this.gridControl1.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl1.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl1.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl1.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl1.EmbeddedNavigator.Buttons.Remove.Visible = false;
            gridLevelNode1.LevelTemplate = this.gvFreq;
            gridLevelNode1.RelationName = "RangeList";
            this.gridControl1.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControl1.Location = new System.Drawing.Point(0, 22);
            this.gridControl1.MainView = this.gvFreqBand;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.ShowOnlyPredefinedDetails = true;
            this.gridControl1.Size = new System.Drawing.Size(409, 323);
            this.gridControl1.TabIndex = 116;
            this.gridControl1.UseEmbeddedNavigator = true;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvFreqBand,
            this.gvFreq});
            // 
            // gvFreqBand
            // 
            this.gvFreqBand.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn4,
            this.gridColumn6});
            this.gvFreqBand.GridControl = this.gridControl1;
            this.gvFreqBand.Name = "gvFreqBand";
            this.gvFreqBand.OptionsDetail.ShowDetailTabs = false;
            this.gvFreqBand.OptionsView.ShowGroupPanel = false;
            this.gvFreqBand.OptionsView.ShowIndicator = false;
            this.gvFreqBand.RowClick += new DevExpress.XtraGrid.Views.Grid.RowClickEventHandler(this.gvFreqBand_RowClick);
            this.gvFreqBand.CellValueChanged += new DevExpress.XtraGrid.Views.Base.CellValueChangedEventHandler(this.gvFreqBand_CellValueChanged);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "是否应用";
            this.gridColumn1.FieldName = "Enable";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 68;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "频段";
            this.gridColumn2.FieldName = "FreqBandName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.OptionsColumn.AllowEdit = false;
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 44;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "频点描述";
            this.gridColumn4.FieldName = "FreqBandDesc";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.OptionsColumn.AllowEdit = false;
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 2;
            this.gridColumn4.Width = 211;
            // 
            // btnRemove
            // 
            this.btnRemove.Location = new System.Drawing.Point(433, 256);
            this.btnRemove.Name = "btnRemove";
            this.btnRemove.Size = new System.Drawing.Size(75, 23);
            this.btnRemove.TabIndex = 115;
            this.btnRemove.Text = "删除";
            this.btnRemove.UseVisualStyleBackColor = true;
            this.btnRemove.Click += new System.EventHandler(this.btnRemove_Click);
            // 
            // btnModifyFreqBand
            // 
            this.btnModifyFreqBand.Location = new System.Drawing.Point(433, 170);
            this.btnModifyFreqBand.Name = "btnModifyFreqBand";
            this.btnModifyFreqBand.Size = new System.Drawing.Size(75, 23);
            this.btnModifyFreqBand.TabIndex = 114;
            this.btnModifyFreqBand.Text = "修改频段";
            this.btnModifyFreqBand.UseVisualStyleBackColor = true;
            this.btnModifyFreqBand.Click += new System.EventHandler(this.btnModifyFreqBand_Click);
            // 
            // btnAddFreqBand
            // 
            this.btnAddFreqBand.Location = new System.Drawing.Point(433, 92);
            this.btnAddFreqBand.Name = "btnAddFreqBand";
            this.btnAddFreqBand.Size = new System.Drawing.Size(75, 23);
            this.btnAddFreqBand.TabIndex = 113;
            this.btnAddFreqBand.Text = "增加频段";
            this.btnAddFreqBand.UseVisualStyleBackColor = true;
            this.btnAddFreqBand.Click += new System.EventHandler(this.btnAddFreqBand_Click);
            // 
            // GroupFreqBand
            // 
            this.GroupFreqBand.Controls.Add(this.btnModifyFreq);
            this.GroupFreqBand.Controls.Add(this.btnAddFreq);
            this.GroupFreqBand.Controls.Add(this.gridControl1);
            this.GroupFreqBand.Controls.Add(this.btnAddFreqBand);
            this.GroupFreqBand.Controls.Add(this.btnModifyFreqBand);
            this.GroupFreqBand.Controls.Add(this.btnRemove);
            this.GroupFreqBand.Dock = System.Windows.Forms.DockStyle.Fill;
            this.GroupFreqBand.Location = new System.Drawing.Point(0, 0);
            this.GroupFreqBand.Name = "GroupFreqBand";
            this.GroupFreqBand.Size = new System.Drawing.Size(527, 345);
            this.GroupFreqBand.TabIndex = 118;
            this.GroupFreqBand.Text = "设置频段";
            // 
            // btnModifyFreq
            // 
            this.btnModifyFreq.Location = new System.Drawing.Point(433, 214);
            this.btnModifyFreq.Name = "btnModifyFreq";
            this.btnModifyFreq.Size = new System.Drawing.Size(75, 23);
            this.btnModifyFreq.TabIndex = 118;
            this.btnModifyFreq.Text = "修改频点";
            this.btnModifyFreq.UseVisualStyleBackColor = true;
            this.btnModifyFreq.Click += new System.EventHandler(this.btnModifyFreq_Click);
            // 
            // btnAddFreq
            // 
            this.btnAddFreq.Location = new System.Drawing.Point(433, 130);
            this.btnAddFreq.Name = "btnAddFreq";
            this.btnAddFreq.Size = new System.Drawing.Size(75, 23);
            this.btnAddFreq.TabIndex = 117;
            this.btnAddFreq.Text = "增加频点";
            this.btnAddFreq.UseVisualStyleBackColor = true;
            this.btnAddFreq.Click += new System.EventHandler(this.btnAddFreq_Click);
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "父频段名称";
            this.gridColumn6.FieldName = "ParentFreqBandName";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.OptionsColumn.AllowEdit = false;
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 3;
            this.gridColumn6.Width = 82;
            // 
            // FreqBandSetting
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.GroupFreqBand);
            this.Name = "FreqBandSetting";
            this.Size = new System.Drawing.Size(527, 345);
            ((System.ComponentModel.ISupportInitialize)(this.gvFreq)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvFreqBand)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.GroupFreqBand)).EndInit();
            this.GroupFreqBand.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gvFreqBand;
        private System.Windows.Forms.Button btnRemove;
        private System.Windows.Forms.Button btnModifyFreqBand;
        private System.Windows.Forms.Button btnAddFreqBand;
        private DevExpress.XtraEditors.GroupControl GroupFreqBand;
        private DevExpress.XtraGrid.Views.Grid.GridView gvFreq;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private System.Windows.Forms.Button btnModifyFreq;
        private System.Windows.Forms.Button btnAddFreq;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
    }
}
