﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTPoorRSRQCell
{
    public partial class ResultFormNR : MinCloseForm
    {
        public ResultFormNR()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = false;
        }

        internal void FillData(List<PoorRsrqCell_NR> poorCellList)
        {
            gridControl.Refresh();

            gridControl.DataSource = poorCellList;
            gridControl.RefreshDataSource();
            gv.BestFitColumns();
            MainModel.ClearDTData();
            MainModel.FireDTDataChanged(this);
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gv);
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            PoorRsrqCell_NR poorCell = gv.GetFocusedRow() as PoorRsrqCell_NR;
            if (poorCell != null)
            {
                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in poorCell.GoodPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                foreach (TestPoint tp in poorCell.PoorPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.SetSelectedNRCell(poorCell.Cell);
                MainModel.FireDTDataChanged(this);
            }
        }
    }
}
