﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using DBDataViewer;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;

namespace MasterCom.RAMS.Net
{
    public static partial class RequestType
    {
        public const byte REQTYPE_DIY_AREASTAT_KPI = 0x1a;
        public const byte REQTYPE_DIY_AREASTAT_EVNET = 0x1b;
    }
    public static partial class ResponseType
    {
        public const byte RESTYPE_DIY_AREASTAT_KPI_GSM = 0x21;

        public const byte RESTYPE_DIY_AREASTAT_KPI_GPRS = 0x23;

        public const byte RESTYPE_DIY_AREASTAT_KPI_SCAN = 0x25;

        public const byte RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_AMR = 0x27;

        public const byte RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_PS = 0x29;

        public const byte RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_VP = 0x2b;

        public const byte RESTYPE_DIY_AREASTAT_KPI_WCDMA_AMR = 0x2d;

        public const byte RESTYPE_DIY_AREASTAT_KPI_WCDMA_PS = 0x2f;

        public const byte RESTYPE_DIY_AREASTAT_KPI_WCDMA_VP = 0x31;

        public const byte RESTYPE_DIY_AREASTAT_KPI_WCDMA_PSHS = 0x33;

        public const byte RESTYPE_DIY_AREASTAT_KPI_CDMA_V = 0x35;

        public const byte RESTYPE_DIY_AREASTAT_KPI_CDMA_D = 0x37;

        public const byte RESTYPE_DIY_AREASTAT_KPI_CDMA2000_D = 0x39;

        public const byte AREASTAT_KPI_LTE = 0x40;
        public const byte RESTYPE_DIY_AREASTAT_KPI_LTE_FDD_AMR = 0x41;

        //events...
        public const byte RESTYPE_DIY_AREASTAT_KPI_EVENT_GSM = 0x21;

        public const byte RESTYPE_DIY_AREASTAT_KPI_EVENT_TDSCDMA = 0x23;

        public const byte RESTYPE_DIY_AREASTAT_KPI_EVENT_WCDMA = 0x25;

        public const byte RESTYPE_DIY_AREASTAT_KPI_EVENT_CDMA = 0x27;

        public const byte RESTYPE_DIY_AREASTAT_KPI_EVENT_CDMA2000 = 0x29;
        public const byte AREASTAT_KPI_EVENT_LTE = 0x2A;
        public const byte AREASTAT_KPI_EVENT_LTE_FDD = 0x2B;
        public const byte RESTYPE_DIY_AREASTAT_KPI_LTE_SIGNAL = 0x42;

        public const byte RESTYPE_DIY_AREASTAT_KPI_SCAN_GSM = 0x43;
        public const byte RESTYPE_DIY_AREASTAT_KPI_SCAN_TDSCDMA = 0x44;
        public const byte RESTYPE_DIY_AREASTAT_KPI_SCAN_LTETOPN = 0x45;
        public const byte RESTYPE_DIY_AREASTAT_KPI_SCAN_LTEFREQSPECTRUM = 0xce;
                          

    };
    public class DIYStatAreaNoGisQuery : DIYStatQuery
    {
        private List<FileAreaStatInfo> retFileStatInfoList = null;
        private readonly ReporterTemplate tpl = null;
        public int DistrictId { get; set; }
        public DIYStatAreaNoGisQuery(MainModel mainModel, ReporterTemplate tpl)
            : base(mainModel)
        {
            this.tpl = tpl;
            DistrictId = 1;
        }
        public List<FileAreaStatInfo> ResultStatList
        {
            get
            {
                return retFileStatInfoList;
            }
        }
        public override string Name
        {
            get { return "既定预处理统计"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 1300, 1301, this.Name);
        }
        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.area;
        }
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                string userName = MainModel.User.LoginName;
                string password = MainModel.User.Password;
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, DistrictId) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                retFileStatInfoList = new List<FileAreaStatInfo>();
                queryNoGISAreaStat(clientProxy,retFileStatInfoList);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void queryNoGISAreaStat(ClientProxy clientProxy, List<FileAreaStatInfo> retFileStatInfoList)
        {
            try
            {
                Dictionary<string, FileAreaStatInfo> dicOfResult = new Dictionary<string, FileAreaStatInfo>();
                Package package = clientProxy.Package;
                List<int> tmpList = new List<int>(noGisCond.areaList);
                while (tmpList.Count > 0)
                {
                    noGisCond.areaList.Clear();
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < tmpList.Count; i++)
                    {
                        int id = tmpList[i];
                        noGisCond.areaList.Add(id);
                        sb.Append(id);
                        sb.Append(",");
                        if (sb.Length > 7000)
                        {
                            tmpList.RemoveRange(0, i + 1);
                            break;
                        }
                        else if (i == tmpList.Count - 1)
                        {
                            tmpList.Clear();
                        }
                    }
                    prepareStatLogKPI_ImgGrid_FileFilter(package);
                    prepareAreaStatFilter(package);
                    fillContentNeeded_ImgGrid(package);
                    clientProxy.Send();
                    recieveInfo_ImgGrid(clientProxy, dicOfResult);
                    prepareStatLogKPI_Event_FileFilter(package);
                    prepareAreaStatFilter(package);
                    fillContentNeededArea_Event(package);
                    clientProxy.Send();
                    recieveInfo_Event(clientProxy, dicOfResult);
                }
                foreach (FileAreaStatInfo fsi in dicOfResult.Values)
                {
                    retFileStatInfoList.Add(fsi);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString() + Environment.NewLine + ex.StackTrace);
            }
        }

        protected void fillContentNeededArea_Event(Package package)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("0,1,47,");
            sb.Append("0,2,47,");
            sb.Append("0,3,47,");
            sb.Append("0,4,47,");
            sb.Append("0,5,47,");
            sb.Append("0,6,47,");
            sb.Append("0,7,47,");
            sb.Append("0,8,47,");
            sb.Append("0,9,47,");
            sb.Append("0,10,47,");
            sb.Append("0,11,47,");
            sb.Append("0,12,47,");
            sb.Append("0,13,47,");
            sb.Append("0,14,47,");
            sb.Append("0,15,47,");
            sb.Append("0,16,47");
            package.Content.AddParam(sb.ToString());
        }

        private void prepareAreaStatFilter(Package package)
        {
            package.Content.AddParam((byte)OpOptionDef.InSelect);
            package.Content.AddParam("0,25,1");
            package.Content.AddParam("" + noGisCond.areaType);
            package.Content.AddParam((byte)OpOptionDef.InSelect);
            package.Content.AddParam("0,26,1");
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < noGisCond.areaList.Count; i++)
            {
                int id = noGisCond.areaList[i];
                sb.Append(id);
                if (i < noGisCond.areaList.Count - 1)
                {
                    sb.Append(",");
                }
            }
            package.Content.AddParam(sb.ToString());
            AddDIYEndOpFlag(package);
        }

        private void prepareStatLogKPI_ImgGrid_FileFilter(Package package)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_AREASTAT_KPI;
            package.Content.PrepareAddParam();
            AddDIYPeriod(package, condition.Periods[0]);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);
            AddDIYStatStatus(package);
            //AddDIYAreaType(package, condition.Areas)
            //
            AddDIYEndOpFlag(package);
        }

        private void fillContentNeeded_ImgGrid(Package package)
        {
            package.Content.AddParam("-1,-1,-1");
        }

        private void prepareStatLogKPI_Event_FileFilter(Package package)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_AREASTAT_EVNET;
            package.Content.PrepareAddParam();
            AddDIYPeriod(package, condition.Periods[0]);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);
            AddDIYStatStatus(package);
            //AddDIYAreaTypeAndID(package, condition.Areas)
            //
            AddDIYEndOpFlag(package);
        }
        protected override void prepareStatPackage_Event_EventFilter(Package package, TimePeriod period)
        {
            AddDIYEndOpFlag(package);
        }

        private void recieveInfo_ImgGrid(ClientProxy clientProxy, Dictionary<string, FileAreaStatInfo> dicOfResult)
        {
            DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader header = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    if (header != null)
                    {
                        headerManager.AddDTDataHeader(header);
                    }
                }
                else if (package.Content.Type == ResponseType.COLUMN_DEFINE)
                {
                    curImgColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurImgColumnDef(idpairs, curImgColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_GSM
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_GPRS)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();

                    DataGSM_NewImg newImg = new DataGSM_NewImg();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            //string areaFileKey = makeAreaFileKey(fileID, areaType, areaid);
                            //FileAreaStatInfo fsi = null;
                            //if (!dicOfResult.TryGetValue(areaFileKey, out fsi))
                            //{

                            //    fsi = new FileAreaStatInfo();
                            //    fsi.areaType = areaType;
                            //    fsi.areaId = areaid;
                            //    fsi.fileHeader = header;
                            //    fsi.kpiData = retResult;
                            //    dicOfResult[areaFileKey] = fsi;
                            //}
                            //fsi.kpiData.addStatData(newImg);
                            //string oldKey = makeAreaFileKey(fileID, areaType, areaid);
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);

                            StatShowHelper.curDistrictId = DistrictId;
                            string areaFileKey = StatShowHelper.getKeyUnionString(tpl, header, areaType, areaid, retResult);
                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(areaFileKey, out fsi))
                            {

                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[areaFileKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }

                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_LTE_FDD_AMR)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();

# if GDCompareStat
                    if (noGisCond.isReject && areaType == 40 && areaid == 1)
                    {
                        continue;
                    }
#endif
                    DataLTE_FDD newImg = new DataLTE_FDD();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);

                            StatShowHelper.curDistrictId = DistrictId;
                            string areaFileKey = StatShowHelper.getKeyUnionString(tpl, header, areaType, areaid, retResult);
                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(areaFileKey, out fsi))
                            {

                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[areaFileKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }

                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_GSM)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();

# if GDCompareStat
                    if (noGisCond.isReject && areaType == 40 && areaid == 1)
                    {
                        continue;
                    }
#endif
                    DataScan_GSM newImg = new DataScan_GSM();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);

                            StatShowHelper.curDistrictId = DistrictId;
                            string areaFileKey = StatShowHelper.getKeyUnionString(tpl, header, areaType, areaid, retResult);
                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(areaFileKey, out fsi))
                            {

                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[areaFileKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }

                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_TDSCDMA)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();

# if GDCompareStat
                    if (noGisCond.isReject && areaType == 40 && areaid == 1)
                    {
                        continue;
                    }
#endif
                    DataScan_TD newImg = new DataScan_TD();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);

                            StatShowHelper.curDistrictId = DistrictId;
                            string areaFileKey = StatShowHelper.getKeyUnionString(tpl, header, areaType, areaid, retResult);
                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(areaFileKey, out fsi))
                            {

                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[areaFileKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }

                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_LTETOPN
                    || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_LTEFREQSPECTRUM)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();

# if GDCompareStat
                    if (noGisCond.isReject && areaType == 40 && areaid == 1)
                    {
                        continue;
                    }
#endif
                    DataScan_LTE newImg = new DataScan_LTE();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);

                            StatShowHelper.curDistrictId = DistrictId;
                            string areaFileKey = StatShowHelper.getKeyUnionString(tpl, header, areaType, areaid, retResult);
                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(areaFileKey, out fsi))
                            {

                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[areaFileKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }

                        }
                    }
                }
                else if (package.Content.Type == ResponseType.AREASTAT_KPI_LTE)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();
# if GDCompareStat
                    if (noGisCond.isReject && areaType == 40 && areaid == 1)
                    {
                        continue;
                    }
#endif
                    DataLTE newImg = new DataLTE();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = null;
                        try
                        {
                            imgBytes = package.Content.GetParamBytes();
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show("imageID:" + cdf.imgID
                                + Environment.NewLine + ex.ToString() + Environment.NewLine + ex.StackTrace);
                        }
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);

                            StatShowHelper.curDistrictId = DistrictId;
                            string areaFileKey = StatShowHelper.getKeyUnionString(tpl, header, areaType, areaid, retResult);
                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(areaFileKey, out fsi))
                            {

                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[areaFileKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }

                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_AMR
              || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_PS
              || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_TDSCDMA_VP)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();

                    DataTDSCDMA_NewImg newImg = new DataTDSCDMA_NewImg();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            //string areaFileKey = makeAreaFileKey(fileID, areaType, areaid);
                            //FileAreaStatInfo fsi = null;
                            //if (!dicOfResult.TryGetValue(areaFileKey, out fsi))
                            //{
                            //    DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            //    fsi = new FileAreaStatInfo();
                            //    fsi.areaType = areaType;
                            //    fsi.areaId = areaid;
                            //    fsi.fileHeader = header;
                            //    fsi.kpiData = retResult;
                            //    dicOfResult[areaFileKey] = fsi;
                            //}
                            //fsi.kpiData.addStatData(newImg);
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);
                            StatShowHelper.curDistrictId = DistrictId;
                            string areaFileKey = StatShowHelper.getKeyUnionString(tpl, header, areaType, areaid, retResult);
                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(areaFileKey, out fsi))
                            {
                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[areaFileKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }
                        }
                    }

                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_WCDMA_AMR
              || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_WCDMA_PS
              || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_WCDMA_VP
              || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_WCDMA_PSHS)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();

                    DataWCDMA_AMR newImg = new DataWCDMA_AMR();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            //string areaFileKey = makeAreaFileKey(fileID, areaType, areaid);
                            //FileAreaStatInfo fsi = null;
                            //if (!dicOfResult.TryGetValue(areaFileKey, out fsi))
                            //{
                            //    DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            //    fsi = new FileAreaStatInfo();
                            //    fsi.areaType = areaType;
                            //    fsi.areaId = areaid;
                            //    fsi.fileHeader = header;
                            //    fsi.kpiData = retResult;
                            //    dicOfResult[areaFileKey] = fsi;
                            //}
                            //fsi.kpiData.addStatData(newImg);
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);
                            StatShowHelper.curDistrictId = DistrictId;
                            string areaFileKey = StatShowHelper.getKeyUnionString(tpl, header, areaType, areaid, retResult);
                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(areaFileKey, out fsi))
                            {
                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[areaFileKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }
                        }
                    }

                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_CDMA_V
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_CDMA_D)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();

                    DataCDMA_Voice newImg = new DataCDMA_Voice();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            //string areaFileKey = makeAreaFileKey(fileID, areaType, areaid);
                            //FileAreaStatInfo fsi = null;
                            //if (!dicOfResult.TryGetValue(areaFileKey, out fsi))
                            //{
                            //    DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            //    fsi = new FileAreaStatInfo();
                            //    fsi.areaType = areaType;
                            //    fsi.areaId = areaid;
                            //    fsi.fileHeader = header;
                            //    fsi.kpiData = retResult;
                            //    dicOfResult[areaFileKey] = fsi;
                            //}
                            //fsi.kpiData.addStatData(newImg);
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);
                            StatShowHelper.curDistrictId = DistrictId;
                            string areaFileKey = StatShowHelper.getKeyUnionString(tpl, header, areaType, areaid, retResult);
                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(areaFileKey, out fsi))
                            {
                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[areaFileKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_CDMA2000_D)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();

                    DataEVDO_Data newImg = new DataEVDO_Data();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("0801", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            //string areaFileKey = makeAreaFileKey(fileID, areaType, areaid);
                            //FileAreaStatInfo fsi = null;
                            //if (!dicOfResult.TryGetValue(areaFileKey, out fsi))
                            //{
                            //    DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            //    fsi = new FileAreaStatInfo();
                            //    fsi.areaType = areaType;
                            //    fsi.areaId = areaid;
                            //    fsi.fileHeader = header;
                            //    fsi.kpiData = retResult;
                            //    dicOfResult[areaFileKey] = fsi;
                            //}
                            //fsi.kpiData.addStatData(newImg);
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);
                            StatShowHelper.curDistrictId = DistrictId;
                            string areaFileKey = StatShowHelper.getKeyUnionString(tpl, header, areaType, areaid, retResult);
                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(areaFileKey, out fsi))
                            {

                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[areaFileKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }
                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_NR)
                {
                    int areaType = package.Content.GetParamInt();
                    int areaid = package.Content.GetParamInt();
# if GDCompareStat
                    if (noGisCond.isReject && areaType == 40 && areaid == 1)
                    {
                        continue;
                    }
#endif
                    DataNR newImg = new DataNR();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = null;
                        try
                        {
                            imgBytes = package.Content.GetParamBytes();
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show("imageID:" + cdf.imgID
                                + Environment.NewLine + ex.ToString() + Environment.NewLine + ex.StackTrace);
                        }
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    double fileIDDouble = 0;
                    if (newImg.wInfoDic.TryGetValue("80040001", out fileIDDouble))
                    {
                        int fileID = (int)fileIDDouble;
                        DTDataHeader header = headerManager.GetHeaderByFileID(fileID);
                        if (header != null)
                        {
                            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
                            retResult.addStatData(newImg);

                            StatShowHelper.curDistrictId = DistrictId;
                            string areaFileKey = StatShowHelper.getKeyUnionString(tpl, header, areaType, areaid, retResult);
                            FileAreaStatInfo fsi = null;
                            if (!dicOfResult.TryGetValue(areaFileKey, out fsi))
                            {

                                fsi = new FileAreaStatInfo();
                                fsi.DistrictId = DistrictId;
                                fsi.areaType = areaType;
                                fsi.areaId = areaid;
                                fsi.fileHeader = header;
                                fsi.kpiData = retResult;
                                dicOfResult[areaFileKey] = fsi;
                            }
                            else
                            {
                                fsi.kpiData.addStatData(newImg);
                            }

                        }
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_SEARCHERROR)
                {
                    log.Error("返回错误命令字: " + package.Content.Type + " 需终止查询！");
                    //MessageBox.Show("查询过程出错，需终止当前查询，进行重查！");
                    //break;
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion

                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        private void recieveInfo_Event(ClientProxy clientProxy, Dictionary<string, FileAreaStatInfo> dicOfResult)
        {
            DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> curDefColumnDef = new List<ColumnDefItem>();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();

            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            bool separateByServiceID = this.tpl.IsSeparateByServiceID;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader header = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    if (header != null)
                    {
                        headerManager.AddDTDataHeader(header);
                    }
                }
                else if (package.Content.Type == ResponseType.COLUMN_DEFINE)
                {
                    curDefColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurColumnDef(idpairs, curDefColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_GSM
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_TDSCDMA
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_WCDMA
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_CDMA
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_CDMA2000
                || package.Content.Type == ResponseType.AREASTAT_KPI_EVENT_LTE
                || package.Content.Type == ResponseType.AREASTAT_KPI_EVENT_LTE_FDD
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_EVENT_NR)
                {
                    Event evt = Event.Create(package.Content, curDefColumnDef);

                    int iareatype = evt.AreaTypeID;
                    int iareaid = evt.AreaID;
                    int ifileid = evt.FileID;
#if GDCompareStat
                    if (noGisCond.isReject && iareatype == 40 && iareaid == 1)
                    {
                        continue;
                    }
#endif
                    setDicOfResult(dicOfResult, headerManager, separateByServiceID, evt, iareatype, iareaid, ifileid);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_SEARCHERROR)
                {
                    log.Error("返回错误命令字: " + package.Content.Type + " 需终止查询！");
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion

                setProgress(ref index, ref progress);
            }
        }

        private void setDicOfResult(Dictionary<string, FileAreaStatInfo> dicOfResult, 
            DTDataHeaderManager headerManager, bool separateByServiceID, Event evt, int iareatype, int iareaid, int ifileid)
        {
            DTDataHeader header = headerManager.GetHeaderByFileID(ifileid);
            if (header != null)
            {
                string fileAreaKey = StatShowHelper.getKeyUnionString(tpl, header, iareatype, iareaid, null);
                FileAreaStatInfo fsi = null;
                if (!dicOfResult.TryGetValue(fileAreaKey, out fsi))
                {
                    fsi = new FileAreaStatInfo();
                    fsi.DistrictId = DistrictId;
                    fsi.areaType = iareatype;
                    fsi.areaId = iareaid;
                    fsi.fileHeader = header;
                    fsi.kpiData = new DataUnitAreaKPIQuery();
                    fsi.kpiData.addStatData(evt, separateByServiceID);
                    dicOfResult[fileAreaKey] = fsi;
                }
                else
                {
                    fsi.kpiData.addStatData(evt, separateByServiceID);
                }
            }
        }

        private void setProgress(ref int index, ref int progress)
        {
            if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
            {
                progress++;
                if (progress > 95)
                {
                    progress = 5;
                    index = 0;
                }
                if (progress % 5 == 0)
                {
                    WaitBox.ProgressPercent = progress;
                }
            }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        private NoGisStatCond noGisCond = null;
        internal void FillNoGisCondition(NoGisStatCond cond)
        {
            noGisCond = cond;
            this.SetQueryCondition(cond.queryCondition);
        }
    }
    public class FileAreaStatInfo
    {
        public int areaType{ get; set; }
        public int areaId{ get; set; }
        public int DistrictId{ get; set; }
        public DTDataHeader fileHeader { get; set; }
        public DataUnitAreaKPIQuery kpiData { get; set; }

        public static IComparer<FileAreaStatInfo> GetCompareByDistrictId()
        {
            if (comparerByDistrictId == null)
            {
                comparerByDistrictId = new ComparerByDistance();
            }
            return comparerByDistrictId;
        }
        public class ComparerByDistance : IComparer<FileAreaStatInfo>
        {
            public int Compare(FileAreaStatInfo x, FileAreaStatInfo y)
            {
                return (x.DistrictId - y.DistrictId);
            }
        }
        private static IComparer<FileAreaStatInfo> comparerByDistrictId;
    };
}
