﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.JiLinFocusSet
{
    public class GridOrder
    {
        internal static GridOrder Create(Net.Content content)
        {
            GridOrder item = new GridOrder();
            item.Grids = new List<OrderGridItem>();
            item.DistrictID = content.GetParamInt();
            item.SetOrderType = content.GetParamString();
            item.ID = content.GetParamInt();
            item.Status = content.GetParamInt();
            item.StatusEx = content.GetParamInt();
            item.CreateDate = MasterCom.Util.JavaDate.GetDateTimeFromMilliseconds(content.GetParamInt() * 1000L);
            content.GetParamInt();//count
            string areas = content.GetParamString();
            string[] arr = areas.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
            if (arr.Length > 0 && (!arr[0].Contains("中心点经纬度")))
            {
                item.AreaNames = arr[0];
            }
            item.RoadNames = content.GetParamString();
            item.OrderID = content.GetParamString();
            return item;
        }

        public string Key
        {
            get { return string.Format("{0}.{1}.{2}", DistrictID, SetOrderType, ID); }
        }

        public int DistrictID { get; set; }

        public string DistrictName
        {
            get { return DistrictManager.GetInstance().getDistrictName(this.DistrictID); }
        }

        public int ID { get; set; }

        public DateTime CreateDate { get; set; }

        public double Lng { get; set; }

        public double Lat { get; set; }

        public int GridCount
        {
            get { return this.Grids.Count; }
        }

        public string AreaNames { get; set; }

        public string RoadNames { get; set; }

        public List<OrderGridItem> Grids
        { get; set; }

        internal void AddItem(OrderGridItem grid)
        {
            Grids.Add(grid);
            grid.Order = this;
        }

        public string OrderID { get; set; }

        public string SetOrderType { get; set; }

        public int Status { get; set; }
        public string StatusDesc
        {
            get
            {
                if (Status == 1)
                {
                    return "已创建";
                }
                else if (Status == 2)
                {
                    return "验证通过";
                }
                else if (Status == 3)
                {
                    return "验证失败";
                }
                return string.Empty;
            }
        }

        public int StatusEx { get; set; }
    }
}
