﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Grid;
using MasterCom.MControls;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System.Reflection;
using MasterCom.RAMS.Net;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTGDProbTaskMngForm : MinCloseForm
    {
        public ZTGDProbTaskMngForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            this.mainModel = mainModel;
        }

        MainModel mainModel;
        /*网格信息*/
        Dictionary<string, CityGridInfo> dicCityGrid = new Dictionary<string, CityGridInfo>();

        /*轮次信息*/
        Dictionary<string, RoundInfo> RoundDic = new Dictionary<string, RoundInfo>();

        /*网格工单信息*/
        Dictionary<BlockKey, List<BlockInfo>> blockDic = new Dictionary<BlockKey, List<BlockInfo>>();

        /*工单指派信息*/
        List<CaseMngInfo> CaseMngList = new List<CaseMngInfo>();

        
        public void fillData(Dictionary<string, CityGridInfo> dicCityGrid, Dictionary<string, RoundInfo> RoundDic)
        {
            this.dicCityGrid = dicCityGrid;
            this.RoundDic = RoundDic;
            initInfo();
            initWorkSheetStati();
        }

        #region 控件初始化
        private void initInfo()
        {
            #region 地市初始化
            object[] city = new object[22];
            city[0] = "全部";
            city[1] = "广州";
            city[2] = "深圳";
            city[3] = "珠海";
            city[4] = "汕头";
            city[5] = "佛山";
            city[6] = "韶关";
            city[7] = "河源";
            city[8] = "梅州";
            city[9] = "惠州";
            city[10] = "汕尾";

            city[11] = "东莞";
            city[12] = "中山";
            city[13] = "江门";
            city[14] = "阳江";
            city[15] = "湛江";
            city[16] = "茂名";
            city[17] = "肇庆";
            city[18] = "清远";
            city[19] = "潮州";
            city[20] = "揭阳";
            city[21] = "云浮";
            #endregion

            comboBoxSGrid.Properties.Items.Clear();
            comboBoxWGrid.Properties.Items.Clear();
            comboBoxSCity.Properties.Items.Clear();
            comboBoxWCity.Properties.Items.Clear();
            comboBoxWSCity.Properties.Items.Clear();
            comboBoxWRound.Properties.Items.Clear();
            comboBoxWSRound.Properties.Items.Clear();
            comboBoxSSRound.Properties.Items.Clear();
            comboBoxSERound.Properties.Items.Clear();

            #region 网格初始化
            comboBoxSGrid.Properties.Items.AddRange(dicCityGrid["广州"].getAllGridList);
            comboBoxWGrid.Properties.Items.AddRange(dicCityGrid["广州"].getAllGridList);
            comboBoxWGrid.Properties.Items.Remove("全部");
            comboBoxSGrid.SelectedIndex = 0;
            comboBoxSGrid.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            comboBoxWGrid.SelectedIndex = 0;
            comboBoxWGrid.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            #endregion

            #region 网格 下拉控件

            comboBoxSCity.Properties.Items.AddRange(city);
            comboBoxSCity.SelectedIndex = 0;
            comboBoxSCity.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            comboBoxSCity.Tag = "网格工单查询";
            comboBoxSCity.SelectedValueChanged += new EventHandler(ComboBoxCityGridChangeEven);

            comboBoxWCity.Properties.Items.AddRange(city);
            comboBoxWCity.Properties.Items.Remove("全部");
            comboBoxWCity.SelectedIndex = 0;
            comboBoxWCity.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            comboBoxWCity.Tag = "网格工单指派";
            comboBoxWCity.SelectedValueChanged += new EventHandler(ComboBoxCityGridChangeEven);

            comboBoxWSCity.Properties.Items.AddRange(city);
            comboBoxWSCity.SelectedIndex = 0;
            comboBoxWSCity.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            comboBoxWSCity.Tag = "工单查询";
            #endregion

            #region 轮次 下拉控件
            List<object> roundList = new List<object>();
            foreach (string strRound in RoundDic.Keys)
            {
                roundList.Add(strRound);
            }
            comboBoxWRound.Properties.Items.AddRange(roundList);
            comboBoxWRound.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            comboBoxWRound.SelectedIndex = 0;
            comboBoxWSRound.Properties.Items.AddRange(roundList);
            comboBoxWSRound.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            comboBoxWSRound.SelectedIndex = 0;

            comboBoxSSRound.Properties.Items.AddRange(roundList);
            comboBoxSSRound.SelectedIndex = 0;
            comboBoxSSRound.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            comboBoxSSRound.Tag = "开始轮次";
            comboBoxSSRound.SelectedValueChanged += new EventHandler(ComboBoxRoundChangeEven);
            comboBoxSERound.Properties.Items.AddRange(roundList);
            comboBoxSERound.SelectedIndex = 0;
            comboBoxSERound.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            comboBoxSERound.Tag = "结束轮次";
            comboBoxSERound.SelectedValueChanged += new EventHandler(ComboBoxRoundChangeEven);
            #endregion
        }

        private void initWorkSheetStati()
        {
            #region dataGridView控件初始化
            this.dataGridViewSStati.Columns.Clear();
            this.dataGridViewSStati.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewSStati.Columns.Add("城市", "城市");
            this.dataGridViewSStati.Columns.Add("网格", "网格");
            this.dataGridViewSStati.Columns.Add("工单总数", "工单总数");
            this.dataGridViewSStati.Columns.Add("正在处理数", "正在处理数");
            this.dataGridViewSStati.Columns.Add("待评估数", "待评估数");
            this.dataGridViewSStati.Columns.Add("评估达标数", "评估达标数");
            this.dataGridViewSStati.Columns[0].Width = 60;
            this.dataGridViewSStati.Columns[1].Width = 60;
            this.dataGridViewSStati.Columns[2].Width = 100;
            this.dataGridViewSStati.Columns[3].Width = 100;
            this.dataGridViewSStati.Columns[4].Width = 100;
            this.dataGridViewSStati.Columns[5].Width = 100;

            this.dataGridViewSInfo.Columns.Clear();
            this.dataGridViewSInfo.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewSInfo.Columns.Add("派单日期", "派单日期");
            this.dataGridViewSInfo.Columns.Add("工单编号", "工单编号");
            this.dataGridViewSInfo.Columns.Add("状态", "状态");
            this.dataGridViewSInfo.Columns.Add("指标类别", "指标类别");
            this.dataGridViewSInfo.Columns.Add("指标名称", "指标名称");
            this.dataGridViewSInfo.Columns.Add("指标描述", "指标描述");
            this.dataGridViewSInfo.Columns.Add("状态更新日期", "状态更新日期");
            this.dataGridViewSInfo.Columns.Add("备注", "备注");
            this.dataGridViewSInfo.Columns[0].Width = 100;
            this.dataGridViewSInfo.Columns[1].Width = 100;
            this.dataGridViewSInfo.Columns[2].Width = 60;
            this.dataGridViewSInfo.Columns[3].Width = 100;
            this.dataGridViewSInfo.Columns[4].Width = 100;
            this.dataGridViewSInfo.Columns[5].Width = 100;
            this.dataGridViewSInfo.Columns[6].Width = 150;
            this.dataGridViewSInfo.Columns[7].Width = 60;

            this.dataGridViewWInfo.Columns.Clear();
            this.dataGridViewWInfo.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewWInfo.Columns.Add("城市", "城市");
            this.dataGridViewWInfo.Columns.Add("网格", "网格");
            this.dataGridViewWInfo.Columns.Add("指派状态", "指派状态");
            this.dataGridViewWInfo.Columns.Add("测试完成日期", "测试完成日期");
            this.dataGridViewWInfo.Columns.Add("指派时间", "指派时间");
            this.dataGridViewWInfo.Columns.Add("指派结果", "指派结果");
            this.dataGridViewWInfo.Columns.Add("备注", "备注");
            this.dataGridViewWInfo.Columns[0].Width = 60;
            this.dataGridViewWInfo.Columns[1].Width = 60;
            this.dataGridViewWInfo.Columns[2].Width = 100;
            this.dataGridViewWInfo.Columns[3].Width = 150;
            this.dataGridViewWInfo.Columns[4].Width = 100;
            this.dataGridViewWInfo.Columns[5].Width = 150;
            this.dataGridViewWInfo.Columns[6].Width = 100;
            #endregion
        }

        /// <summary>
        /// ComboBox控件联动事件  地市→网格
        /// </summary>
        private void ComboBoxCityGridChangeEven(object sender, EventArgs e)
        {
            #region ComboBox控件联动事件  地市→网格
            ComboBoxEdit comboBox = (ComboBoxEdit)sender;
            string strCity = comboBox.SelectedItem.ToString();
            if (comboBox.Tag.ToString() == "网格工单查询")
            {
                comboBoxSGrid.Properties.Items.Clear();
                if (strCity == "全部")
                {
                    addPropertieItems(comboBoxSGrid);
                }
                else
                {
                    //网格
                    comboBoxSGrid.Properties.Items.AddRange(dicCityGrid[strCity].getFormatGridADic.Keys);
                    comboBoxSGrid.Properties.Items.AddRange(dicCityGrid[strCity].getFormatGridBDic.Keys);
                    comboBoxSGrid.SelectedIndex = 0;
                }
            }
            else if (comboBox.Tag.ToString() == "网格工单指派")
            {
                comboBoxWGrid.Properties.Items.Clear();
                if (strCity == "全部")
                {
                    addPropertieItems(comboBoxWGrid);
                }
                else
                {
                    comboBoxWGrid.Properties.Items.AddRange(dicCityGrid[strCity].getFormatGridADic.Keys);
                    comboBoxWGrid.Properties.Items.AddRange(dicCityGrid[strCity].getFormatGridBDic.Keys);
                    comboBoxWGrid.Properties.Items.Remove("全部");
                    comboBoxWGrid.SelectedIndex = 0;
                }
            }
            #endregion
        }

        private void addPropertieItems(ComboBoxEdit cmb)
        {
            for (int index = 1; index <= 58; index++)
            {
                string strGrid = "A" + index.ToString();
                if (index < 10)
                    strGrid = "A0" + index;
                cmb.Properties.Items.Add(strGrid);
            }
        }

        /// <summary>
        /// ComboBox控件联动事件  结束轮次不能小于开始轮次
        /// </summary>
        private void ComboBoxRoundChangeEven(object sender, EventArgs e)
        {
            ComboBoxEdit comboBox = (ComboBoxEdit)sender;
            int strSelectValue = Convert.ToInt32(comboBox.SelectedItem.ToString());
            if (comboBox.Tag.ToString() == "开始轮次")
            {
                int endTime = Convert.ToInt32(comboBoxSERound.SelectedItem.ToString());
                if (strSelectValue > endTime)
                {
                    comboBoxSERound.SelectedIndex = comboBox.SelectedIndex;
                }
            }
            if (comboBox.Tag.ToString() == "结束轮次")
            {
                int startTime = Convert.ToInt32(comboBoxSSRound.SelectedItem.ToString());
                if (strSelectValue < startTime)
                {
                    comboBoxSSRound.SelectedIndex = comboBox.SelectedIndex;
                }
            }
        }
        #endregion

        /// <summary>
        /// 填充数据 to DataGridView
        /// </summary>
        private void FillDataToGridView(DataGridView dgv, List<NPOIRow> RowsList)
        {
            dgv.Rows.Clear();
            foreach (NPOIRow NPOI in RowsList)
            {
                DataGridViewRow row = new DataGridViewRow();
                List<object> objs = NPOI.cellValues;
                foreach (object obj in objs)
                {
                    DataGridViewTextBoxCell boxcell = new DataGridViewTextBoxCell();
                    boxcell.Value = obj.ToString();
                    row.Cells.Add(boxcell);
                }
                dgv.Rows.Add(row);
            }
        }

        #region 查询网格工单信息
        private void btnSSelect_Click(object sender, EventArgs e)
        {
            dataGridViewSInfo.Rows.Clear();
            List<string> objs = new List<string>();
            string strCity = comboBoxSCity.SelectedItem.ToString();
            string strGrid = comboBoxSGrid.SelectedItem.ToString();
            string strNo = txtBoxSWorkNum.Text.Trim() == "" ? "" : txtBoxSWorkNum.Text.Trim();
            string strSRound = comboBoxSSRound.SelectedItem.ToString();
            string strERound = comboBoxSERound.SelectedItem.ToString();
            objs.Add(strCity);
            if (strCity != "全部" && strGrid != "全部")
            {
                objs.Add(dicCityGrid[strCity].getGridDicValue(strGrid, "value"));
            }
            else
            {
                objs.Add(strGrid);
            }
            objs.Add(strNo);
            objs.Add(strSRound);
            objs.Add(strERound);
            WaitBox.Show("正在查询网格工单信息...", queryGridSheetInfo, objs);

            List<NPOIRow> nrDataList = new List<NPOIRow>();
            foreach (BlockKey key in blockDic.Keys)
            {
                NPOIRow nr = new NPOIRow();
                List<object> objList = new List<object>();
                objList.Add(key.strCity);
                objList.Add(key.strGrid);
                objList.Add(key.iSum);
                objList.Add(key.iDoing);
                objList.Add(key.iWaiting);
                objList.Add(key.iQualified);
                nr.cellValues = objList;
                nrDataList.Add(nr);
            }
            FillDataToGridView(dataGridViewSStati, nrDataList);
            if (blockDic.Count == 0)
                XtraMessageBox.Show("预设条件查询不到网格工单信息");
        }

        /// <summary>
        /// 查询网格工单信息
        /// </summary>
        private void queryGridSheetInfo(object obj)
        {
            WaitBox.ProgressPercent = 50;
            blockDic.Clear();
            List<string>  objs = obj as List<string>;
            string strCity = objs[0];
            string strGrid = objs[1];
            string strNo = objs[2];
            int iSRound = Convert.ToInt32(objs[3]);
            int iERound = Convert.ToInt32(objs[4]);
            DIVQueryBlock divQuery = new DIVQueryBlock(mainModel);
            divQuery.setCondition(strCity, strGrid, strNo, iSRound, iERound);
            divQuery.Query();
            this.blockDic = divQuery.blockDic;
            WaitBox.Close();
        }
        #endregion

        private void dataGridViewSStati_CellMouseDoubleClick(object sender, DataGridViewCellMouseEventArgs e)
        {
            #region datagridview 联动设置
            if (dataGridViewSStati.Rows.Count > 0)
            {
                string strCity = dataGridViewSStati.SelectedRows[0].Cells[0].Value.ToString();
                string strGird = dataGridViewSStati.SelectedRows[0].Cells[1].Value.ToString();
                if (Convert.ToInt32(dataGridViewSStati.SelectedRows[0].Cells["工单总数"].Value.ToString()) != 0)
                {
                    BlockKey Tkey = new BlockKey(strCity, strGird);
                    if (blockDic.ContainsKey(Tkey))
                    {
                        List<NPOIRow> nrDataList = new List<NPOIRow>();
                        foreach (BlockInfo blockInfo in blockDic[Tkey])
                        {
                            NPOIRow nr = new NPOIRow();
                            List<object> objList = new List<object>();
                            objList.Add(blockInfo.getDateTimeByCaseDate);
                            objList.Add(blockInfo.strNo);
                            objList.Add(blockInfo.strState);
                            objList.Add(blockInfo.strKpiType);
                            objList.Add(blockInfo.strKpiName);
                            objList.Add(blockInfo.strKpiDesc);
                            objList.Add(blockInfo.getDateTimeByUpdDate);
                            objList.Add(blockInfo.strcomment);
                            nr.cellValues = objList;
                            nrDataList.Add(nr);
                        }
                        FillDataToGridView(dataGridViewSInfo, nrDataList);
                    }
                }
                else
                {
                    XtraMessageBox.Show(string.Format("地市：{0}\r\n网格：{1}\r\n无工单信息", strCity, strGird));
                }
            }
                #endregion
        }

        #region 工单查询
        private void btnWSelect_Click(object sender, EventArgs e)
        {
            List<string> objs = new List<string>();
            string strCity = comboBoxWSCity.SelectedItem.ToString();
            string strSRound = comboBoxWSRound.SelectedItem.ToString();
            objs.Add(strCity);
            objs.Add(strSRound);
            WaitBox.Show("正在查询工单派发信息...", queryCaseMngInfo, objs);
            fillCaseMngInGridView();
        }

        /// <summary>
        /// 工单派发 信息  显示到 gridview 
        /// </summary>
        private void fillCaseMngInGridView()
        {
            List<NPOIRow> nrDataList = new List<NPOIRow>();
            labelDataCount.Text = "共 " + CaseMngList.Count + " 条";
            foreach (CaseMngInfo key in CaseMngList)
            {
                NPOIRow nr = new NPOIRow();
                List<object> objList = new List<object>();
                objList.Add(key.strCity);
                objList.Add(dicCityGrid[key.strCity].getGridDicValue(key.strGrid,"key"));
                objList.Add(key.strState);
                objList.Add(key.getDateTimeByTestTime);
                objList.Add(key.getDateTimeBySendTime);
                objList.Add(key.strresult);
                objList.Add(key.strcomment);
                nr.cellValues = objList;
                nrDataList.Add(nr);
            }
            FillDataToGridView(dataGridViewWInfo, nrDataList);
        }

        /// <summary>
        /// 查询工单派发,获取结果集
        /// 结束后调用 fillCaseMngInGridView方法显示
        /// </summary>
        private void queryCaseMngInfo(object obj)
        {
            WaitBox.ProgressPercent = 80;
            blockDic.Clear();
            List<string> objs = obj as List<string>;
            string strCity = objs[0];
            string strRound = objs[1];
            DIVQueryCaseMng divQuery = new DIVQueryCaseMng(mainModel);
            divQuery.setCondition(strCity, strRound);
            divQuery.Query();
            this.CaseMngList = divQuery.CaseMngList;
            WaitBox.Close();
        }
        #endregion

        #region 工单派发
        List<NPOIRow> errorDatas = null;
        /// <summary>
        /// 模板导入
        /// </summary>
        private void btnLeadIn_Click(object sender, EventArgs e)
        {
            #region 模板导入
            try
            {

                OpenFileDialog openDlg = new OpenFileDialog();
                openDlg.Filter = "Excel文件|*.xlsx;*.xls";
                if (openDlg.ShowDialog() == DialogResult.OK)
                {
                    DataSet ds = MasterCom.Util.ExcelNPOIManager.ImportFromExcel(openDlg.FileName);
                    if (ds == null)
                    {
                        XtraMessageBox.Show("读取失败,该文件不是有效表格，请下载并按照模板进行填写。");
                        return;
                    }
                    DataTable dt = ds.Tables[0];
                    foreach (DataColumn dc in dt.Columns)
                    {
                        string column = "地市,所属轮次,网格,指派状态,测试完成日期,备注";
                        if (!column.Contains(dc.ColumnName))
                        {
                            XtraMessageBox.Show("该文件列名不完整，请下载并按照模板进行填写。");
                            return;
                        }
                    }
                    errorDatas = new List<NPOIRow>();
                    List<DataTable> objDatatable = new List<DataTable>();
                    objDatatable.Add(dt);
                    WaitBox.CanCancel = true;
                    WaitBox.Show("正在批量更新入库", updateCasemngByExcel, objDatatable);
                    fillCaseMngInGridView();
                    if (errorDatas.Count > 0)
                    {
                        DialogResult res = MessageBox.Show("数据部分无法更新，是否导出查看？", "错误提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
                        if (res == DialogResult.OK)
                        {
                            MasterCom.Util.ExcelNPOIManager.ExportToExcel(errorDatas);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("导入失败（error：" + ex.Message + "）");
            }
            #endregion 
        }

        /// <summary>
        /// 更新批量工单数据
        /// </summary>
        private void updateCasemngByExcel(object obj)
        {
            #region 批量更新
            List<DataTable> objDatatable = obj as List<DataTable>;
            errorDatas.Clear();
            DataTable dt = objDatatable[0];
            StringBuilder strCityTmp = new StringBuilder();
            StringBuilder strSRoundTmp = new StringBuilder();
            WaitBox.Text = "正在更新的数据...";
            WaitBox.ProgressPercent = 40;
            for (int idx = 0; idx < dt.Rows.Count; idx++)
            {
                int iReturnCode = -1;
                List<object> secValue1 = new List<object>();
                try
                {
                    string strCity = dt.Rows[idx][0].ToString();
                    secValue1.Add(strCity);
                    string strround = dt.Rows[idx][1].ToString();
                    secValue1.Add(strround);
                    string strGrid = dt.Rows[idx][2].ToString();
                    secValue1.Add(strGrid);
                    string testtime = dt.Rows[idx][4].ToString() == null ? "" : dt.Rows[idx][4].ToString();
                    secValue1.Add(testtime);
                    string sendtime = DateTime.Now.ToString();
                    string strState = dt.Rows[idx][3].ToString();
                    secValue1.Add(strState);
                    string strcomment = dt.Rows[idx][5].ToString() == null ? "" : dt.Rows[idx][5].ToString();
                    secValue1.Add(strcomment);
                    string strGridID = "";

                    if (strCity == "" || strround == "" || strGrid == "")
                        continue;

                    if (strState == "指派")
                        strState = "已指派";
                    int gridType = 12;
                    getGridInfo(strCity, strGrid, ref strGridID, ref gridType);
                    if (strGridID != "")
                    {
                        DIVUpdateCaseMng updateCMng = new DIVUpdateCaseMng(mainModel);
                        updateCMng.setCondition(strCity, strround, strGridID, strState, strcomment, gridType);
                        updateCMng.SetDateTime(testtime, sendtime);
                        updateCMng.Query();
                        iReturnCode = updateCMng.returnCode;
                    }
                    addErrorData(secValue1, iReturnCode);
                    addCityAndRound(strCityTmp, strSRoundTmp, iReturnCode, strCity, strround);
                }
                catch
                {
                    addErrorData(secValue1, iReturnCode);
                }
            }
            WaitBox.Text = "正在列出已更新的数据...";
            WaitBox.ProgressPercent = 80;
            List<string> objs = new List<string>();
            objs.Add(strCityTmp.ToString());
            objs.Add(strSRoundTmp.ToString());
            queryCaseMngInfo(objs);
            #endregion 
        }

        private void getGridInfo(string strCity, string strGrid, ref string strGridID, ref int gridType)
        {
            if (dicCityGrid[strCity].getFormatGridADic.ContainsKey(strGrid))
            {
                strGridID = dicCityGrid[strCity].getFormatGridADic[strGrid].ToString();
                gridType = 12;
            }
            if (dicCityGrid[strCity].getFormatGridBDic.ContainsKey(strGrid))
            {
                strGridID = dicCityGrid[strCity].getFormatGridBDic[strGrid].ToString();
                gridType = 15;
            }
        }

        private void addCityAndRound(StringBuilder strCityTmp, StringBuilder strSRoundTmp, int iReturnCode, string strCity, string strround)
        {
            if (iReturnCode == 200)
            {
                if (strCityTmp.Length > 0)
                {
                    strCityTmp.Append(",");
                }
                strCityTmp.Append(strCity);
                if (strSRoundTmp.Length > 0)
                {
                    strSRoundTmp.Append(",");
                }
                strSRoundTmp.Append(strround);
            }
        }

        private void addErrorData(List<object> secValue1,int iCode)
        {
            if (iCode == 0)
            {
                secValue1.Add("失败原因:工单已指派");
            }
            if (iCode == 1)
            {
                secValue1.Add("失败原因:工单未生成，请联系管理员");
            }
            if (iCode == -1)
            {
                secValue1.Add("失败可能原因:1、该网格不存在；2、填写格式不正确；3、表头缺失，完整表头：地市,所属轮次,网格,指派状态,测试完成日期,备注");
            }
            if (iCode != 200)
            {
                NPOIRow nr1 = new NPOIRow();
                nr1.cellValues = secValue1;
                errorDatas.Add(nr1);
            }
        }
        /// <summary>
        /// 模板下载
        /// </summary>
        private void btnDLtemplate_Click(object sender, EventArgs e)
        {
            #region 导出模板
            List<NPOIRow> datas = new List<NPOIRow>();
            List<object> cols = new List<object>();
            cols.Add("地市");
            cols.Add("所属轮次");
            cols.Add("网格");
            cols.Add("指派状态");
            cols.Add("测试完成日期");
            cols.Add("备注");
            NPOIRow nr = new NPOIRow();
            nr.cellValues = cols;
            datas.Add(nr);

            List<object> secValue1 = new List<object>();
            secValue1.Add("广州");
            secValue1.Add("201601");
            secValue1.Add("A05");
            secValue1.Add("已指派");
            secValue1.Add("20160131");
            secValue1.Add("示例，填写请删除,日期格式不能改变，表头不能删除或改变，内容清空即可");
            NPOIRow nr1 = new NPOIRow();
            nr1.cellValues = secValue1;
            datas.Add(nr1);

            List<object> secValue2 = new List<object>();
            secValue2.Add("广州");
            secValue2.Add("201602");
            secValue2.Add("A18");
            secValue2.Add("标记结束");
            secValue2.Add("20160229");
            secValue2.Add("示例，填写请删除,日期格式不能改变，表头不能删除或改变，内容清空即可");
            NPOIRow nr2 = new NPOIRow();
            nr2.cellValues = secValue2;
            datas.Add(nr2);

            List<object> secValue3 = new List<object>();
            secValue3.Add("广州");
            secValue3.Add("201603");
            secValue3.Add("A19");
            secValue3.Add("不测试");
            secValue3.Add("20160331");
            secValue3.Add("示例，填写请删除,日期格式不能改变，表头不能删除或改变，内容清空即可");
            NPOIRow nr3 = new NPOIRow();
            nr3.cellValues = secValue3;
            datas.Add(nr3);
            try
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(datas);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("下载模板失败（error："+ex.Message+"）");
            }
            #endregion
        }

        private void btnMarker_Click(object sender, EventArgs e)
        {
            if (comboBoxWCity.SelectedItem.ToString() == "" || comboBoxWRound.SelectedItem.ToString() == "" ||
                comboBoxWGrid.SelectedItem.ToString() == "" || datePickerTestDate.SelectedText.ToString() == "")
            {
                XtraMessageBox.Show("信息填写不完整");
                return;
            }
            updateCondtion("标记结束");
        }
        private void btnDesignate_Click(object sender, EventArgs e)
        {
            if (comboBoxWCity.SelectedItem.ToString() == "" || comboBoxWRound.SelectedItem.ToString() == "" ||
               comboBoxWGrid.SelectedItem.ToString() == "" || datePickerTestDate.SelectedText.ToString() == "")
            {
                XtraMessageBox.Show("信息填写不完整");
                return;
            }
            updateCondtion("已指派");
        }

        /// <summary>
        /// 设置更新条件
        /// </summary>
        private void updateCondtion(string strState)
        {
            string strCity = comboBoxWCity.SelectedItem.ToString();
            string strround = comboBoxWRound.SelectedItem.ToString();
            string strGrid = comboBoxWGrid.SelectedItem.ToString();
            string testtime = Convert.ToDateTime(datePickerTestDate.Text.ToString()).ToLongDateString();
            string sendtime = DateTime.Now.ToString();
            string strcomment = "";
            comboBoxWSCity.SelectedIndex = comboBoxWCity.SelectedIndex + 1;
            comboBoxWSRound.SelectedIndex = comboBoxWRound.SelectedIndex;

            string strGridID = "";
            int gridType = 12;
            if (dicCityGrid[strCity].getFormatGridADic.ContainsKey(strGrid))
            {
                strGridID = dicCityGrid[strCity].getFormatGridADic[strGrid].ToString();
                gridType = 12;
            }
            if (dicCityGrid[strCity].getFormatGridBDic.ContainsKey(strGrid))
            {
                strGridID = dicCityGrid[strCity].getFormatGridBDic[strGrid].ToString();
                gridType = 15;
            }

            List<string> listCondtion = new List<string>();
            listCondtion.Add(strCity);
            listCondtion.Add(strround);
            listCondtion.Add(strGridID);
            listCondtion.Add(testtime);
            listCondtion.Add(sendtime);
            listCondtion.Add(strState);
            listCondtion.Add(strcomment);
            listCondtion.Add(gridType.ToString());
            WaitBox.Show("正在更新...", updateCasemngByCondtion, listCondtion);

            fillCaseMngInGridView();
        }

        /// <summary>
        /// 根据条件更新信息
        /// </summary>
        private void updateCasemngByCondtion(object obj)
        {
            List<string> objData = obj as List<string>;
            DIVUpdateCaseMng updateCMng = new DIVUpdateCaseMng(mainModel);

            updateCMng.setCondition(objData[0], objData[1], objData[2], objData[5], objData[6],Convert.ToInt32(objData[7].ToString()));
            updateCMng.SetDateTime(objData[3], objData[4]);
            updateCMng.Query();
            List<string> objs = new List<string>();
            objs.Add(objData[0]);
            objs.Add(objData[1]);
            if (updateCMng.returnCode == 200)
            {
                queryCaseMngInfo(objs);
            }
            else
            {
                if (updateCMng.returnCode == 0)
                {
                    XtraMessageBox.Show("更新失败,不能重复指派");
                }
                if (updateCMng.returnCode == 1)
                {
                    XtraMessageBox.Show("更新失败,该工单未能更新");
                }
                WaitBox.Close();
            }
        }

        #endregion

        private void btnNotTest_Click(object sender, EventArgs e)
        {
            if (comboBoxWCity.SelectedItem.ToString() == "" || comboBoxWRound.SelectedItem.ToString() == "" ||
               comboBoxWGrid.SelectedItem.ToString() == "" || datePickerTestDate.SelectedText.ToString() == "")
            {
                XtraMessageBox.Show("信息填写不完整");
                return;
            }
            updateCondtion("不测试");
        }

        private void ImportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(dataGridViewWInfo);
        }

    }
}
