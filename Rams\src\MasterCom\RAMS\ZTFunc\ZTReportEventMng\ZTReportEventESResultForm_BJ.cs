using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTReportEventESResultForm_BJ : BaseForm
    {
        public ZTReportEventESResultForm_BJ()
        {
            InitializeComponent();
            this.dataGridPreType.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
            this.dataGridReason.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
        }

        public void FillData(ZTReportEventInfo_BJ reportEventInfo)
        {
            this.dataGridPreType.Rows.Clear();
            this.dataGridReason.Rows.Clear();

            fillGrid(dataGridPreType, reportEventInfo.PreTypeDesc);
            fillGrid(dataGridReason, reportEventInfo.ReasonDesc);
        }

        private void fillGrid(DataGridView dgView, string fillContent)
        {
            string[] strContents = fillContent.Split('|');
            int row = 0;
            foreach (string content in strContents)
            {
                if (!string.IsNullOrEmpty(content.Trim()))
                {
                    dgView.Rows.Add(1);
                    dgView.Rows[row++].Cells[0].Value = content;
                }
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        } 
    }
}