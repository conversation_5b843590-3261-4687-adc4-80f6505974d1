﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.Func.PopShow
{
    public partial class KpiAlarmPanel : UserControl, PopShowPanelInterface
    {
        public const byte REQTYPE_TABLE_DATA_INFO = 0x83; //REQUEST 
        public const byte RESTYPE_TABLE_DATA_INFO = 0x83; 

        private MainModel MainModel;
        public KpiAlarmPanel()
        {
            InitializeComponent();
        }

        public void SetMainModal(MasterCom.RAMS.Model.MainModel mm, WelcomForm welcomform)
        {
            this.MainModel = mm;
        }

        public void RunQuery(BackgroundWorker worker, TaskInfo task)
        {
            DiySqlPopKpiAlarmLog querytask = new DiySqlPopKpiAlarmLog(MainModel);
            querytask.Query();
            List<AlarmLog> retList = new List<AlarmLog>();
            retList.AddRange(querytask.alarmLogLst);

            task.retResultInfo = retList;
        }

        List<AlarmLog> curRetDataDic = new List<AlarmLog>();
        public void FireFreshShowData(TaskInfo task)
        {
            if (!(task.retResultInfo is List<AlarmLog>))
            {
                curRetDataDic.Clear();
            }
            else
            {
                curRetDataDic = task.retResultInfo as List<AlarmLog>;
            }
            refreshShowReport();
        }

        public void SetMainModal(MasterCom.RAMS.Model.MainModel mm)
        {
            this.MainModel = mm;
        }

        private void cbxTypeSel_SelectedIndexChanged(object sender, EventArgs e)
        {
            refreshShowReport();
        }

        private void cbxShowType_SelectedIndexChanged(object sender, EventArgs e)
        {
            refreshShowReport();
        }

        private void cbxContentType_SelectedIndexChanged(object sender, EventArgs e)
        {
            refreshShowReport();
        }
        private void refreshShowReport()
        {
            /*string seltype = cbxTypeSel.SelectedItem as string;
            if(seltype==null)
            {
                return;
            }
            List<BlockEntryItem> blockList = null;
            if(curRetDataDic.TryGetValue(seltype,out blockList))
            {
                string selShowType = cbxShowType.SelectedItem as string;
                if(selShowType!=null)
                {
                    List<BlockNumResult> blockShowResult = parseShowFromList(blockList,selShowType);
                    if (resetContent)
                    {
                        Dictionary<string, bool> namesDic = getNameList(blockShowResult);
                        cbxContentType.Items.Clear();
                        cbxContentType.Items.Add("(全部)");
                        foreach (string nm in namesDic.Keys)
                        {
                            cbxContentType.Items.Add(nm);
                        }
                        cbxContentType.SelectedIndex = 0;
                    }
                    showInGrid(blockShowResult);
                }
            }*/
            showInGrid(curRetDataDic);
        }

        
        /**
        private string getDayStr(int time,out int dayValue)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(1000L * time);
            dayValue = (int)(JavaDate.GetMilliseconds(dt) / 1000L);
            return dt.ToString("yyyy.MM.dd");
        }
        private string getMonthStr(int time,out int monthValue)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(1000L * time);
            monthValue = (int)(JavaDate.GetMilliseconds(new DateTime(dt.Year, dt.Month, 1)) / 1000L);
            return dt.ToString("yyyy.MM");
        }
        private string getWeekStr(int time,out int weekValue)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L).Date;
            DayOfWeek dweek = dt.DayOfWeek;
            int dayToMondy = 0;
            switch (dweek)
            {
                case DayOfWeek.Monday:
                    dayToMondy = 0;
                    break;
                case DayOfWeek.Tuesday:
                    dayToMondy = 1;
                    break;
                case DayOfWeek.Wednesday:
                    dayToMondy = 2;
                    break;
                case DayOfWeek.Thursday:
                    dayToMondy = 3;
                    break;
                case DayOfWeek.Friday:
                    dayToMondy = 4;
                    break;
                case DayOfWeek.Saturday:
                    dayToMondy = 5;
                    break;
                case DayOfWeek.Sunday:
                    dayToMondy = 6;
                    break;

            }
            weekValue = (int)(JavaDate.GetMilliseconds(dt.AddDays(-dayToMondy)) / 1000L); 
            return dt.AddDays(-dayToMondy).ToString("yyyy.MM.dd_") + dt.AddDays(6 - dayToMondy).ToString("MM.dd");
            //return dt.AddDays(-dayToMondy).ToString("yyyy-MM-dd") + "至" + dt.AddDays(6 - dayToMondy).ToString("yyyy-MM-dd");
        }
       */

        private void showInGrid(List<AlarmLog> blockShowResult)
        {
            dataGridView.Rows.Clear();
            if (blockShowResult.Count > 0)
            {
                int indexRowAt = 0;
                for (int r = 0; r < blockShowResult.Count; r++)
                {
                    AlarmLog numItem = blockShowResult[r];
                    dataGridView.Rows.Add(1);
                    string strWeekRangeStart = JavaDate.GetDateTimeFromMilliseconds(numItem.stime*1000L).ToString("yyyy-MM-dd");
                    string strWeekRangeend = JavaDate.GetDateTimeFromMilliseconds(numItem.etime * 1000L).ToString("yyyy-MM-dd");
                    string dealinfo = "";
                    if (numItem.isdealed == 1)
                    {
                        dealinfo = "处理时间：" + JavaDate.GetDateTimeFromMilliseconds(numItem.alarmtime * 1000L).ToString("yyyy-MM-dd");
                    }
                    else
                    {
                        dealinfo = "未处理";
                    }

                    dataGridView.Rows[indexRowAt].Cells[0].Value = numItem.strrepname;
                    dataGridView.Rows[indexRowAt].Cells[1].Value = numItem.strcoldesc;
                    dataGridView.Rows[indexRowAt].Cells[2].Value = numItem.strname;
                    dataGridView.Rows[indexRowAt].Cells[3].Value = strWeekRangeStart + "至" + strWeekRangeend;
                    dataGridView.Rows[indexRowAt].Cells[4].Value = JavaDate.GetDateTimeFromMilliseconds(numItem.stattime*1000L).ToString("yyyy-MM-dd");
                    dataGridView.Rows[indexRowAt].Cells[5].Value = numItem.descinfo;
                    dataGridView.Rows[indexRowAt].Cells[6].Value = dealinfo;
                    indexRowAt++;
                }
            }
        }

        /**
        private void extractLabValue(int rowAt, int vColumn, int labelColumn, out double doublev, out string labelv)
        {
            doublev = 0;
            object v = dataGridView.Rows[rowAt].Cells[vColumn].Value;
            if (v is int)
            {
                doublev = (int)v;
            }
            else if (v is double)
            {
                doublev = (double)v;
            }
            else if (v is float)
            {
                doublev = (float)v;
            }
            string vlabel = dataGridView.Rows[rowAt].Cells[labelColumn].Value.ToString();
            labelv = vlabel;

        }
        private void extractLabValues(int vColumn, int labelColumn, out double[] doubles, out string[] labels)
        {
            doubles = new double[dataGridView.Rows.Count];
            labels = new string[dataGridView.Rows.Count];
            for (int r = 0; r < dataGridView.Rows.Count; r++)
            {
                object v = dataGridView.Rows[r].Cells[vColumn].Value;
                if (v is int)
                {
                    doubles[r] = (int)v;
                }
                else if (v is double)
                {
                    doubles[r] = (double)v;
                }
                else if (v is float)
                {
                    doubles[r] = (float)v;
                }
                string vlabel = dataGridView.Rows[r].Cells[labelColumn].Value.ToString();
                labels[r] = vlabel;
            }
        }
        */

        private void btnAlarmCfg_Click(object sender, EventArgs e)
        {
            kpiAlarmCfgForm form = new kpiAlarmCfgForm(KPIInfoPanel_ng.theKPIInfoPanel);
            form.ShowDialog();
        }
    }
    public class AlarmLog
    {
        public  string strcolname { get; set; }
        public string strcoldesc { get; set; }
        public string strrepname { get; set; }
        public string strname { get; set; }
        public int stime { get; set; }
        public int etime { get; set; }
        public float stat_value { get; set; }
        public float threshhold { get; set; }
        public string descinfo { get; set; }
        public int stattime { get; set; }
        public int isalarmed { get; set; }
        public int alarmtime { get; set; }
        public int isdealed { get; set; }
    }

    public class DiySqlPopKpiAlarmLog : MasterCom.RAMS.Net.DIYSQLBase
    {
        public List<AlarmLog> alarmLogLst { get; set; }

        public DiySqlPopKpiAlarmLog(MainModel mainModel)
                : base(mainModel)
        {
            alarmLogLst = new List<AlarmLog>();
        }

        protected override string getSqlTextString()
        {
            return "select * from tb_popkpi_alarm_log a where a.stime > datediff(ss,'19700101 8:0',dateadd(mm,-2,getdate()))";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[13];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Float;
            rType[7] = E_VType.E_Float;
            rType[8] = E_VType.E_String;
            rType[9] = E_VType.E_Int;
            rType[10] = E_VType.E_Int;
            rType[11] = E_VType.E_Int;
            rType[12] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(MasterCom.RAMS.Net.ClientProxy clientProxy)
        {
            MasterCom.RAMS.Net.Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    AlarmLog alarmLog = new AlarmLog();
                    alarmLog.strcolname = package.Content.GetParamString();
                    alarmLog.strcoldesc = package.Content.GetParamString();
                    alarmLog.strrepname = package.Content.GetParamString();
                    alarmLog.strname = package.Content.GetParamString();
                    alarmLog.stime = package.Content.GetParamInt();
                    alarmLog.etime = package.Content.GetParamInt();
                    alarmLog.stat_value = package.Content.GetParamFloat();
                    alarmLog.threshhold = package.Content.GetParamFloat();
                    alarmLog.descinfo = package.Content.GetParamString();
                    alarmLog.stattime = package.Content.GetParamInt();
                    alarmLog.isalarmed = package.Content.GetParamInt();
                    alarmLog.alarmtime = package.Content.GetParamInt();
                    alarmLog.isdealed = package.Content.GetParamInt();
                    alarmLogLst.Add(alarmLog);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        public override string Name
        {
            get { return "DiySqlPopKpiAlarmLog"; }
        }
    };
    
}
