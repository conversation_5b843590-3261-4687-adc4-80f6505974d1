﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using Message = MasterCom.RAMS.Model.Message;

namespace MasterCom.RAMS.ZTFunc
{
    class VoNRStatDelayAnaByFile_Divided : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private List<NrResultRow> listRow = null;
        private VoNRStatDelayInfoForm_Divided resultForm = null;
        private static VoNRStatDelayAnaByFile_Divided instance = null;
        public static VoNRStatDelayAnaByFile_Divided GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoNRStatDelayAnaByFile_Divided();
                    }
                }
            }
            return instance;
        }
        protected VoNRStatDelayAnaByFile_Divided()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
            ServiceTypes.Clear();

            ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
            ServiceTypes.Add(ServiceType.NR_NSA_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.NR_SA_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.NR_DM_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_NR_SA_TDD_EPSFB);
            ServiceTypes.Add(ServiceType.SER_NR_DM_TDD_EPSFB);
            ServiceTypes.Add(ServiceType.NR_SA_TDD_VONR);
        }
        public override string Name
        {
            get
            {
                return "VoNR时延分析（分段统计）";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27010, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
        protected override bool getCondition()
        {
            return true;
        }
        protected override void getReadyBeforeQuery()
        {
            this.listRow = new List<NrResultRow>();
        }
        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    NrStatus NrStatus = NrStatus.SIP_NONE;
                    NrResultRow row = null;
                    for (int i = 0; i < file.Messages.Count; i++)
                    {
                        Message mesg = file.Messages[i];
                        if (this.dealWithEvent(ref NrStatus, mesg, ref row))
                        {
                            row.fileName = file.FileName;
                            this.listRow.Add(row);

                            if (NrStatus != NrStatus.SIP_180)
                            {
                                i--;
                            }

                            NrStatus = NrStatus.SIP_NONE;
                        }
                    }
                }
            }
            catch
            {
                //continue
            }
        }
        protected override void fireShowForm()
        {
            if (this.resultForm == null || this.resultForm.IsDisposed)
            {
                this.resultForm = new VoNRStatDelayInfoForm_Divided();
            }
            this.resultForm.FillData(this.listRow);
            this.resultForm.Show();
            this.resultForm.Focus();
        }

        private DateTime getMsgTime(Message mesg, int NrStatus, DateTime defaultTime)
        {
            if (mesg.ID == NrStatus)
            { 
                return mesg.DateTime;
            }
            return defaultTime;
        }

        /// <summary>
        /// 根据状态和消息ID获取对应消息的时间。
        /// </summary>
        /// <param name="NrStatus"></param>
        /// <param name="mesg"></param>
        /// <param name="row"></param>
        /// <returns>true: 完成一个信息周期，可以保存信息。</returns>
        private bool dealWithEvent(ref NrStatus NrStatus, Message mesg, ref NrResultRow row)
        {
            if (mesg.ID == (int)MessageManager.Msg_IMS_SIP_INVITE && NrStatus != NrStatus.SIP_NONE)
            {
                return true;
            }

            if (mesg.ID == (int)MessageManager.Msg_IMS_SIP_INVITE && NrStatus == NrStatus.SIP_NONE)
            {
                row = new NrResultRow();
                row.time_invite = getMsgTime(mesg, (int)MessageManager.Msg_IMS_SIP_INVITE, row.time_invite);
                NrStatus = NrStatus.SIP_INVITE;

                return false;
            }

            switch (mesg.ID)
            {
                case (int)NrStatus.SIP_100:
                    row.time_100 = getMsgTime(mesg, (int)NrStatus.SIP_100, row.time_100);
                    NrStatus = NrStatus.SIP_100;
                    break;
                case (int)NrStatus.SIP_183:
                    row.time_183 = getMsgTime(mesg, (int)NrStatus.SIP_183, row.time_183);
                    NrStatus = NrStatus.SIP_183;
                    break;
                case (int)NrStatus.SIP_PRACK:
                    row.time_prack = getMsgTime(mesg, (int)NrStatus.SIP_PRACK, row.time_prack);
                    NrStatus = NrStatus.SIP_PRACK;
                    break;
                case (int)NrStatus.SIP_PRACK_200:
                    row.time_prack200 = getMsgTime(mesg, (int)NrStatus.SIP_PRACK_200, row.time_prack200);
                    NrStatus = NrStatus.SIP_PRACK_200;
                    break;
                case (int)NrStatus.SIP_UPDATE:
                    row.time_update = getMsgTime(mesg, (int)NrStatus.SIP_UPDATE, row.time_update);
                    NrStatus = NrStatus.SIP_UPDATE;
                    break;
                case (int)NrStatus.SIP_UPDATE_200:
                    row.time_update200 = getMsgTime(mesg, (int)NrStatus.SIP_UPDATE_200, row.time_update200);
                    NrStatus = NrStatus.SIP_UPDATE_200;
                    break;
                case (int)NrStatus.SIP_180:
                    row.time_180 = getMsgTime(mesg, (int)NrStatus.SIP_180, row.time_180);
                    NrStatus = NrStatus.SIP_180;
                    return true;
            }
            return false;
        }
    }


    class VoNRStatDelayAnaByFile_Divided_FDD : VoNRStatDelayAnaByFile_Divided
    {
        private static VoNRStatDelayAnaByFile_Divided_FDD instance = null;
        public static new VoNRStatDelayAnaByFile_Divided_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoNRStatDelayAnaByFile_Divided_FDD();
                    }
                }
            }
            return instance;
        }
        protected VoNRStatDelayAnaByFile_Divided_FDD()
            : base()
        {
            ServiceTypes.Clear();
        }
        public override string Name
        {
            get
            {
                return "VoNR时延分析（分段统计）";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30021, this.Name);
        }
    }

    public enum NrStatus
    {
        SIP_NONE = 0,                   //无效
        SIP_ACK = MessageManager.Msg_IMS_SIP_ACK,
        SIP_INVITE = MessageManager.Msg_IMS_SIP_INVITE,        //0x42060000  Request IMS_SIP_INVITE
        SIP_100 = 0x42064064,           //0x42064064  NrStatus IMS_SIP_INVITE->Trying
        SIP_183 = 0x420640b7,           //0x420640b7  NrStatus IMS_SIP_INVITE->SessionProgress183
        SIP_PRACK = 0x420A0000,         //0x420A0000  Request IMS_SIP_PRACK
        SIP_PRACK_200 = 0x420A40c8,     //0x420640C8  NrStatus IMS_SIP_PRACK->OK
        SIP_UPDATE = 0x42100000,        //0x42100000  Request IMS_SIP_UPDATE
        SIP_UPDATE_200 = 0x421040c8,    //0x420640C8  NrStatus IMS_SIP_UPDATE->OK
        SIP_180 = 0x420640B4,           //0x420640B4  NrStatus IMS_SIP_INVITE->Ringing
    }
}
