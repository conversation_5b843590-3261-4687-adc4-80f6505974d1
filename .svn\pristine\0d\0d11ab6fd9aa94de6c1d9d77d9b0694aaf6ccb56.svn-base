﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.Globalization;

namespace MasterCom.RAMS.ZTFunc
{
    class TableNamesQuery: DIYSQLBase
    {
        private readonly bool IsTDIn;
        public TableNamesQuery(MainModel mainModel, bool IsTDIn)
            : base(mainModel)
        {
            this.IsTDIn = IsTDIn;
        }
        protected override string getSqlTextString()
        {
            if (IsTDIn)
            {
                return "select name from sysobjects where name like 'tb_para_utrancell_counter_detail_%'";
            }
            else
            {
                return "select name from sysobjects where name like 'tb_para_cell_counter_detail_%'";
            }
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_String;
            return rType;
        }
        public List<string> TableNames;
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            TableNames = new List<string>();
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    try
                    {
                        TableNames.Add(package.Content.GetParamString());
                    }
                    catch
                    {
                        //continue
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgress(ref index, ref progress);
            }
        }

        private static void setProgress(ref int index, ref int progress)
        {
            if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
            {
                progress++;
                if (progress > 95)
                {
                    progress = 5;
                    index = 0;
                }
                if (progress % 5 == 0)
                {
                    WaitBox.ProgressPercent = progress;
                }
            }
        }

        public override string Name
        {
            get { return "TableNamesQuery"; }
        }
    }
}
