﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Windows.Forms;
using System.Text.RegularExpressions;
using System.Net;
namespace System.IPCtrl
{
    /// <summary>
    /// IP地址输入控件 written by <PERSON><PERSON><PERSON>
    /// </summary>

    /// <summary>
    /// 表示IP地址的类型为A、B、C或D类地址
    /// </summary>
    public enum IPType : byte { A, B, C, D };
    public class IPbox : System.Windows.Forms.UserControl
    {
        private System.Windows.Forms.TextBox txt1;
        private System.Windows.Forms.TextBox txt2;
        private System.Windows.Forms.TextBox txt3;
        private System.Windows.Forms.TextBox txt4;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.Container components = null;
        public IPbox()
        {
            // This call is required by the Windows.Forms Form Designer.
            InitializeComponent();
        }
        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (components != null)
                    components.Dispose();
            }
            base.Dispose(disposing);
        }
        #region Component Designer generated code
        /// <summary>
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.txt1 = new System.Windows.Forms.TextBox();
            this.txt2 = new System.Windows.Forms.TextBox();
            this.txt3 = new System.Windows.Forms.TextBox();
            this.txt4 = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // txt1
            // 
            this.txt1.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.txt1.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txt1.Location = new System.Drawing.Point(2, 2);
            this.txt1.MaxLength = 3;
            this.txt1.Name = "txt1";
            this.txt1.Size = new System.Drawing.Size(24, 16);
            this.txt1.TabIndex = 0;
            this.txt1.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt1.GotFocus += new System.EventHandler(this.txt_LostFocus);
            this.txt1.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txt1_KeyPress);
            this.txt1.LostFocus += new System.EventHandler(this.txt_LostFocus);
            // 
            // txt2
            // 
            this.txt2.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.txt2.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txt2.Location = new System.Drawing.Point(34, 2);
            this.txt2.MaxLength = 3;
            this.txt2.Name = "txt2";
            this.txt2.Size = new System.Drawing.Size(24, 16);
            this.txt2.TabIndex = 1;
            this.txt2.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt2.GotFocus += new System.EventHandler(this.txt_LostFocus);
            this.txt2.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txt2_KeyPress);
            this.txt2.LostFocus += new System.EventHandler(this.txt_LostFocus);
            // 
            // txt3
            // 
            this.txt3.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.txt3.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txt3.Location = new System.Drawing.Point(66, 2);
            this.txt3.MaxLength = 3;
            this.txt3.Name = "txt3";
            this.txt3.Size = new System.Drawing.Size(24, 16);
            this.txt3.TabIndex = 2;
            this.txt3.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt3.GotFocus += new System.EventHandler(this.txt_LostFocus);
            this.txt3.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txt3_KeyPress);
            this.txt3.LostFocus += new System.EventHandler(this.txt_LostFocus);
            // 
            // txt4
            // 
            this.txt4.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.txt4.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txt4.Location = new System.Drawing.Point(98, 2);
            this.txt4.MaxLength = 3;
            this.txt4.Name = "txt4";
            this.txt4.Size = new System.Drawing.Size(24, 16);
            this.txt4.TabIndex = 3;
            this.txt4.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt4.GotFocus += new System.EventHandler(this.txt_LostFocus);
            this.txt4.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txt4_KeyPress);
            this.txt4.LostFocus += new System.EventHandler(this.txt_LostFocus);
            // 
            // label1
            // 
            this.label1.BackColor = System.Drawing.SystemColors.Window;
            this.label1.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label1.Location = new System.Drawing.Point(26, 2);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(8, 16);
            this.label1.TabIndex = 1;
            this.label1.Text = ".";
            this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label2
            // 
            this.label2.BackColor = System.Drawing.SystemColors.Window;
            this.label2.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label2.Location = new System.Drawing.Point(90, 2);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(8, 16);
            this.label2.TabIndex = 8;
            this.label2.Text = ".";
            this.label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label3
            // 
            this.label3.BackColor = System.Drawing.SystemColors.Window;
            this.label3.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label3.Location = new System.Drawing.Point(58, 2);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(8, 16);
            this.label3.TabIndex = 2;
            this.label3.Text = ".";
            this.label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label4
            // 
            this.label4.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.label4.Font = new System.Drawing.Font("宋体", 15.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(0, 0);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(122, 18);
            this.label4.TabIndex = 10;
            // 
            // IPbox
            // 
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.txt4);
            this.Controls.Add(this.txt3);
            this.Controls.Add(this.txt2);
            this.Controls.Add(this.txt1);
            this.Controls.Add(this.label4);
            this.Name = "IPbox";
            this.Size = new System.Drawing.Size(124, 20);
            this.Load += new System.EventHandler(this.UserControl1_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

        }
        #endregion
        private void UserControl1_Load(object sender, System.EventArgs e)
        {

        }
        public void Clear()
        {
            this.txt1.Text = "";
            this.txt2.Text = "";
            this.txt3.Text = "";
            this.txt4.Text = "";
        }
        private void txt1_KeyPress(object sender, KeyPressEventArgs e)
        {
            try
            {
                string str = e.KeyChar.ToString();
                if (e.KeyChar.ToString() == "." || e.KeyChar.ToString() == "。" || e.KeyChar.ToString() == " ")
                {
                    if (txt1.SelectedText.ToString() == "" && txt1.Text.ToString() != "")
                    {
                        SendKeys.Send("{Tab}");


                    }
                    e.Handled = true;
                    return;
                }
                if (Regex.Match(e.KeyChar.ToString(), "[0-9]").Success)
                {
                    if (txt1.Text.Length == 2)
                    {
                        if (int.Parse(txt1.Text.ToString() + e.KeyChar.ToString()) > 255)
                        {
                            e.Handled = true;
                        }
                        else
                        {
                            txt2.Focus();
                        }

                    }

                }
                else
                {
                    if (e.KeyChar.ToString() == "\b")
                    {
                    }
                    else
                    {
                        e.Handled = true;
                    }
                }
            }
            catch
            {
                e.Handled = true;
            }

        }
        private void txt2_KeyPress(object sender, KeyPressEventArgs e)
        {
            try
            {
                if (e.KeyChar.ToString() == "." || e.KeyChar.ToString() == "。" || e.KeyChar.ToString() == " ")
                {
                    if (txt2.SelectedText.ToString() == "" && txt2.Text.ToString() != "")
                    {
                        SendKeys.Send("{Tab}");


                    }
                    e.Handled = true; return;
                }
                if (Regex.Match(e.KeyChar.ToString(), "[0-9]").Success)
                {
                    if (txt2.Text.Length == 2)
                    {
                        if (int.Parse(txt2.Text.ToString() + e.KeyChar.ToString()) > 255)
                        {
                            e.Handled = true;
                        }
                        else
                        {
                            txt3.Focus();
                        }
                    }

                }
                else
                {
                    if (e.KeyChar.ToString() == "\b")
                    {
                        if (txt2.Text.ToString() == "")
                        {
                            txt1.Focus();
                            SendKeys.Send("{End}");
                        }
                    }
                    else
                    {
                        e.Handled = true;
                    }
                }
            }
            catch
            {
                e.Handled = true;
            }
        }
        private void txt3_KeyPress(object sender, KeyPressEventArgs e)
        {
            try
            {
                if (e.KeyChar.ToString() == "." || e.KeyChar.ToString() == "。" || e.KeyChar.ToString() == " ")
                {
                    if (txt3.SelectedText.ToString() == "" && txt3.Text.ToString() != "")
                    {
                        SendKeys.Send("{Tab}");


                    }
                    e.Handled = true; return;
                }
                if (Regex.Match(e.KeyChar.ToString(), "[0-9]").Success)
                {
                    if (txt3.Text.Length == 2)
                    {
                        if (int.Parse(txt3.Text.ToString() + e.KeyChar.ToString()) > 255)
                        {
                            e.Handled = true;
                        }
                        else
                        {
                            txt4.Focus();
                        }
                    }

                }
                else
                {
                    if (e.KeyChar.ToString() == "\b")
                    {
                        if (txt3.Text.ToString() == "")
                        {
                            txt2.Focus();
                            SendKeys.Send("{End}");
                        }
                    }
                    else
                    {
                        e.Handled = true;
                    }
                }
            }
            catch
            {
                e.Handled = true;
            }


        }
        private void txt4_KeyPress(object sender, KeyPressEventArgs e)
        {
            try
            {
                if (e.KeyChar.ToString() == "." || e.KeyChar.ToString() == "。" || e.KeyChar.ToString() == " ")
                {

                    e.Handled = true;

                }
                if (Regex.Match(e.KeyChar.ToString(), "[0-9]").Success)
                {
                    if (txt4.Text.Length == 2)
                    {
                        if (int.Parse(txt4.Text.ToString() + e.KeyChar.ToString()) > 255)
                        {
                            e.Handled = true;
                        }
                    }

                }
                else
                {
                    if (e.KeyChar.ToString() == "\b")
                    {
                        if (txt4.Text.ToString() == "")
                        {
                            txt3.Focus();
                            SendKeys.Send("{End}");
                        }
                    }
                    else
                    {
                        e.Handled = true;
                    }
                }
            }
            catch
            {
                e.Handled = true;
            }

        }
        private void txt_LostFocus(object sender, EventArgs e)
        {
            while (txt1.Text.Length > 1 && txt1.Text.ToString().Substring(0, 1) == "0")
            {
                txt1.Text = txt1.Text.ToString().Remove(0, 1);
            }
            while (txt2.Text.Length > 1 && txt2.Text.ToString().Substring(0, 1) == "0")
            {
                txt2.Text = txt2.Text.ToString().Remove(0, 1);
            }
            while (txt3.Text.Length > 1 && txt3.Text.ToString().Substring(0, 1) == "0")
            {
                txt3.Text = txt3.Text.ToString().Remove(0, 1);
            }
            while (txt4.Text.Length > 1 && txt4.Text.ToString().Substring(0, 1) == "0")
            {
                txt4.Text = txt4.Text.ToString().Remove(0, 1);
            }
        }

        public bool IsInputValid
        {
            get
            {
                return txt1.Text.Trim().Length > 0
                    && txt2.Text.Trim().Length > 0
                    && txt3.Text.Trim().Length > 0
                    && txt4.Text.Trim().Length > 0;
            }
        }

        /// <summary>
        /// 返回string类型的用户在IPBox控件中输入的IP地址
        /// </summary>
        override public string Text
        {
            get
            {
                return txt1.Text.ToString().Trim() + "." + txt2.Text.ToString().Trim() + "." + txt3.Text.ToString().Trim() + "." + txt4.Text.ToString().Trim();
            }
            set
            {
                try
                {
                    string[] str = new string[4];
                    str = value.Split(char.Parse("."));
                    for (int i = 0; i < 4; i++)
                    {
                        if (int.Parse(str[i]) > -1 && int.Parse(str[i]) < 256)
                        {
                            txt1.Text = str[0];
                            txt2.Text = str[1];
                            txt3.Text = str[2];
                            txt4.Text = str[3];
                        }
                    }
                }
                catch
                {
                }
            }

        }
        /// <summary>
        /// 返回System.Net.IPAddress类型的IP地址
        /// </summary>
        public IPAddress IPaddress
        {
            get
            {
                try
                {
                    return IPAddress.Parse(this.Text.ToString());
                }
                catch
                {
                    return IPAddress.Parse("0.0.0.0");
                }
            }
        }
        /// <summary>
        /// 返回IP地址的类型（A,B,C,D），返回值为IPType类型
        /// </summary>
        public IPType Type
        {
            get
            {
                if (int.Parse(this.txt1.Text.ToString().Trim()) < 128)
                {
                    return IPType.A;
                }
                else
                {
                    if (int.Parse(this.txt1.Text.ToString().Trim()) < 192)
                    {
                        return IPType.B;
                    }
                    else
                    {
                        if (int.Parse(this.txt1.Text.ToString().Trim()) < 224)
                        {
                            return IPType.C;
                        }
                        else
                        {
                            return IPType.D;
                        }
                    }
                }
            }
        }
        public BorderStyle BorderStyle
        {
            get
            {
                return this.label4.BorderStyle;
            }
            set
            {
                try
                {
                    this.label4.BorderStyle = value;
                }
                catch
                {
                }
            }
        }

    }
}