﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.Func.CellPerformanceData
{
    public partial class QueryCellTypeSettingForm : XtraForm
    {
        public QueryCellTypeSettingForm()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        public bool IsQueryGsmCell
        {
            get
            {
                if (radioGroup.SelectedIndex==0)
                {
                    return true;
                }
                return false;
            }
        }
    }
}