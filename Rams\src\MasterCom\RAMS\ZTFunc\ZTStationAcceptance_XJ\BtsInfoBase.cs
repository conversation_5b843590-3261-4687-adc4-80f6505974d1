﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class BtsInfoBase
    {
        public ISite Bts { get; set; }
        public String BtsName { get; set; }
        public int BtsID { get; set; }

        public BtsParameters BtsBaseInfo { get; set; }
        //小区
        public List<CellInfoBase> CellInfoList { get; set; }
        public Dictionary<string, CellInfoBase> CellInfoDic { get; set; } = new Dictionary<string, CellInfoBase>();

        public BtsInfoBase(ISite bts)
        {
            Bts = bts;
        }

        public virtual void Calculate()
        {
            CellInfoList = new List<CellInfoBase>(CellInfoDic.Values);
            CellInfoList.Sort((a, b) => a.Cell.ID.CompareTo(b.Cell.ID));
        }
    }

    public class BtsParameters
    {

    }

    public class ParamInfo<T>
    {
        public T Planing { get; set; }
        public T Real { get; set; }
        public bool IsValid { get; set; } = false;

        public void JudgeValid()
        {
            switch (Planing)
            {
                case int _:
                case double _:
                case string _:
                    IsValid = Planing.Equals(Real);
                    break;
                default:
                    return;
            }
        }

        public void JudgeValid(double deviation)
        {
            if (Planing == null || Real == null)
            {
                return;
            }
            double plaing = (double)(object)Planing;
            double real = (double)(object)Real;

            double curDeviation = Math.Abs(plaing - real);
            if (curDeviation <= deviation)
            {
                IsValid = true;
            }
        }

        public void JudgeValidPercent(double deviationPercent)
        {
            if (Planing == null || Real == null)
            {
                return;
            }
            double plaing = (double)(object)Planing;
            double real = (double)(object)Real;

            double curDeviation = Math.Abs(plaing - real);
            double curDeviationPercent = Math.Round(curDeviation / plaing, 2) * 100;
            if (curDeviationPercent <= deviationPercent)
            {
                IsValid = true;
            }
        }

        public void JudgeValidLongitude(double deviation)
        {
            if (Planing == null || Real == null)
            {
                return;
            }
            double x1 = (double)(object)Planing;
            double x2 = (double)(object)Real;

            double distance = MasterCom.Util.MathFuncs.GetDistance(x1, 0, x2, 0);
            if (distance <= deviation)
            {
                IsValid = true;
            }
        }

        public void JudgeValidLatitude(double deviation)
        {
            if (Planing == null || Real == null)
            {
                return;
            }
            double y1 = (double)(object)Planing;
            double y2 = (double)(object)Real;

            double distance = MasterCom.Util.MathFuncs.GetDistance(0, y1, 0, y2);
            if (distance <= deviation)
            {
                IsValid = true;
            }
        }
    }
}
