﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class CallEndDelayInfo
    {
        public CallEndDelayInfo(Event evt)
        {
            this.EvtCallEnd = evt;
        }
        public int SN { get; set; }
        public string FileName
        {
            get { return this.EvtCallEnd == null ? "" : this.EvtCallEnd.FileName; }
        }
        public float DelaySeconds { get; set; }

        public Event EvtCallEnd { get; set; }
        public int? TAC
        {
            get { return this.EvtCallEnd == null ? null : (int?)this.EvtCallEnd["LAC"]; }
        }
        public int? ECI
        {
            get { return this.EvtCallEnd == null ? null : (int?)this.EvtCallEnd["CI"]; }
        }
        public double Longitude
        {
            get { return this.EvtCallEnd == null ? 0 : this.EvtCallEnd.Longitude; }
        }
        public double Latitude
        {
            get { return this.EvtCallEnd == null ? 0 : this.EvtCallEnd.Latitude; }
        }
        
        public List<TestPoint> TestPointBefore { get; set; } = new List<TestPoint>();
        public float? RsrpAvgBefore { get; set; }
        public float? SinrAvgBefore { get; set; }
        public double? QAM16UlPercentBefore { get; set; }
        public List<TestPoint> TestPointAfter { get; set; } = new List<TestPoint>();
        public float? RsrpAvgAfter { get; set; }
        public float? SinrAvgAfter { get; set; }
        public double? QAM16UlPercentAfter { get; set; }

        public void StatBeforeAndAfterInfo()
        {
            float? rsrp;
            float? sinr;
            double? qam16;
            statInfo(TestPointBefore, out rsrp, out sinr, out qam16);
            RsrpAvgBefore = rsrp;
            SinrAvgBefore = sinr;
            QAM16UlPercentBefore = qam16;

            statInfo(TestPointAfter, out rsrp, out sinr, out qam16);
            RsrpAvgAfter = rsrp;
            SinrAvgAfter = sinr;
            QAM16UlPercentAfter = qam16;
        }

        private void statInfo(List<TestPoint> testPoint, out float? rsrpAvg, out float? sinrAvg
            , out double? qam16UlPercent)
        {
            int rsrpCount = 0;
            float rsrpSum = 0;
            int sinrCount = 0;
            float sinrSum = 0;
            int qam16UlTimesTotal = 0;
            int ulTimesTotal = 0;
            foreach (TestPoint tp in testPoint)
            {
                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null)
                {
                    rsrpCount++;
                    rsrpSum += (float)rsrp;
                }

                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null)
                {
                    sinrCount++;
                    sinrSum += (float)sinr;
                }

                int? timesQAM16Ul = (int?)tp["lte_Times_QAM16_UL"];
                if (timesQAM16Ul != null)
                {
                    qam16UlTimesTotal += (int)timesQAM16Ul;
                    ulTimesTotal += (int)timesQAM16Ul;
                }

                int? timesQAM64Ul = (int?)tp["lte_Times_QAM64_UL"];
                if (timesQAM64Ul != null)
                {
                    ulTimesTotal += (int)timesQAM64Ul;
                }
                int? timesQPSKUl = (int?)tp["lte_Times_QPSK_UL"];
                if (timesQPSKUl != null)
                {
                    ulTimesTotal += (int)timesQPSKUl;
                }
            }
            rsrpAvg = rsrpCount > 0 ? (float?)(Math.Round(rsrpSum / rsrpCount, 2)) : null;
            sinrAvg = sinrCount > 0 ? (float?)(Math.Round(sinrSum / sinrCount, 2)) : null;
            qam16UlPercent = ulTimesTotal > 0 ? (double?)(Math.Round((double)100 * qam16UlTimesTotal / ulTimesTotal, 2)) : null;
        }
    }
}
