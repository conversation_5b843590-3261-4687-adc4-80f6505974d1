﻿using MapWinGIS;
using MasterCom.MControls;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    [Serializable()]
    public abstract class ScanRoadMultiCoverageLayerBase : LayerBase
    {
        protected ScanRoadMultiCoverageLayerBase(string name)
           : base(name)
        {
        }

        public Color InvalidPointColor { get; set; } = Color.Gray;
        public Color NoneMainPointColor { get; set; } = Color.Olive;
        public ShowCoverage ShowCoverageType { get; set; } = ShowCoverage.Absolute;

        public List<RoadMultiCoverageInfoBase> DataList { get; set; } = new List<RoadMultiCoverageInfoBase>();
        public List<GridRoadMultiCoverageInfoBase> GridList { get; set; } = new List<GridRoadMultiCoverageInfoBase>();
        public List<ColorRange> ColorRanges { get; set; } = new List<ColorRange>();


        public void SetShowCoverageType(int type)
        {
            if (type == 0) //相对覆盖度
            {
                ShowCoverageType = ShowCoverage.Relative;
            }
            else if (type == 1) //绝对
            {
                ShowCoverageType = ShowCoverage.Absolute;
            }
            else if (type == 2) //综合
            {
                ShowCoverageType = ShowCoverage.Synthesize;
            }
        }

        public void Clear()
        {
            DataList.Clear();
            GridList.Clear();
        }

        #region 渲染
        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            float ratio = CustomDrawLayer.GetTestPointDisplayRatio(MapScale);
            DbRect dRect;
            GisAdapter.FromDisplay(updateRect, out dRect);

            if (GridList.Count > 0)
            {
                foreach (var grid in GridList)
                {
                    drawGrid(grid, dRect, graphics, ColorRanges);
                }
            }
            else if (DataList.Count > 0)
            {
                foreach (var data in DataList)
                {
                    draw(data, dRect, graphics, ratio, ColorRanges);
                }
            }
        }

        protected void drawGrid(GridRoadMultiCoverageInfoBase grid, DbRect dRect, Graphics graphics, List<ColorRange> modeRange)
        {
            if (grid.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
            {
                Color color;
                int level = -1;
                if (ShowCoverageType == ShowCoverage.Relative)
                {
                    level = grid.RelativeLevel;
                }
                else if (ShowCoverageType == ShowCoverage.Absolute)
                {
                    level = grid.AbsoluteLevel;
                }
                else if (ShowCoverageType == ShowCoverage.Synthesize)
                {
                    level = grid.RelANDAbsLevel;
                }
                color = GetColor(level, modeRange);

                if (color != Color.Empty)
                {
                    DbPoint ltPoint = new DbPoint(grid.ltLong, grid.ltLat);
                    PointF pointLt;
                    GisAdapter.ToDisplay(ltPoint, out pointLt);
                    DbPoint brPoint = new DbPoint(grid.brLong, grid.brLat);
                    PointF pointBr;
                    GisAdapter.ToDisplay(brPoint, out pointBr);
                    Brush brush = new SolidBrush(color);

                    graphics.FillRectangle(brush, pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
                }
            }
        }

        protected void draw(RoadMultiCoverageInfoBase multiCovInfo, DbRect dRect, Graphics graphics
            , float ratio, List<ColorRange> modeRange)
        {
            if (multiCovInfo.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
            {
                Color color;
                int level = -1;
                if (ShowCoverageType == ShowCoverage.Relative)
                {
                    level = multiCovInfo.RelativeLevel;
                }
                else if (ShowCoverageType == ShowCoverage.Absolute)
                {
                    level = multiCovInfo.AbsoluteLevel;
                }
                else if (ShowCoverageType == ShowCoverage.Synthesize)
                {
                    level = multiCovInfo.RelANDAbsLevel;
                }
                color = multiCovInfo.InvalidatePoint ? InvalidPointColor : GetColor(level, modeRange);
                if (multiCovInfo.IsNoneMainPoint)
                {
                    color = NoneMainPointColor;
                }
                if (color != Color.Empty)
                {
                    float radius = ratio * 20;
                    Brush brush = new SolidBrush(color);
                    DbPoint dPoint = new DbPoint(multiCovInfo.Longitude, multiCovInfo.Latitude);
                    PointF point;
                    GisAdapter.ToDisplay(dPoint, out point);
                    graphics.TranslateTransform(point.X, point.Y);
                    graphics.ScaleTransform(radius, radius);
                    GraphicsPath gp = SymbolManager.GetInstance().Paths[0];
                    graphics.FillPath(brush, gp);
                    graphics.ResetTransform();
                }
            }
        }

        protected Color GetColor(int multiCoverageLevel, List<ColorRange> ranges)
        {
            int count = ranges.Count;
            if (count == 0)
            {
                return Color.Empty;
            }
            for (int i = 0; i < count; i++)
            {
                ColorRange cr = ranges[i];
                if (cr.visible && multiCoverageLevel >= cr.minValue && multiCoverageLevel < cr.maxValue)
                {
                    return cr.color;
                }
            }
            return Color.Empty;
        }
        #endregion

        #region 导出图层
        protected int outputShpFile(string filename, List<RoadMultiCoverageInfoBase> pointInfo
            , List<ColorRange> modeRange, bool bWaitBox)
        {
            Shapefile shpFile = new Shapefile();
            bool result = shpFile.CreateNewWithShapeID("", ShpfileType.SHP_POINT);
            if (!result)
            {
                MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                return -1;
            }
            shpFile.DefaultDrawingOptions.SetDefaultPointSymbol(tkDefaultPointSymbol.dpsCircle);

            //---列
            int idIdx = 0;
            int fiFileName = idIdx++;
            int fiDateTime = idIdx++;
            int fiLongitude = idIdx++;
            int fiLatitude = idIdx++;
            int fiMainCellName = idIdx++;
            int fiCoverLevel = idIdx++;
            int fiCoverCellName = idIdx;
            ShapeHelper.InsertNewField(shpFile, "FileName", FieldType.STRING_FIELD, 10, 254, ref fiFileName);
            ShapeHelper.InsertNewField(shpFile, "DateTime", FieldType.STRING_FIELD, 10, 30, ref fiDateTime);
            ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiLongitude);
            ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiLatitude);
            ShapeHelper.InsertNewField(shpFile, "MainCellName", FieldType.STRING_FIELD, 10, 254, ref fiMainCellName);
            ShapeHelper.InsertNewField(shpFile, "CoverLevel", FieldType.INTEGER_FIELD, 10, 30, ref fiCoverLevel);
            ShapeHelper.InsertNewField(shpFile, "CoverCellName", FieldType.STRING_FIELD, 10, 254, ref fiCoverCellName);

            try
            {
                int iLoop = 0;
                foreach (var point in pointInfo)
                {
                    RoadMultiCoverageInfoBase data = getValidData(point);
                    if (data == null)
                    {
                        continue;
                    }

                    int level = getDataLevel(data);
                    MapWinGIS.Shape spBase = new MapWinGIS.Shape();
                    spBase.Create(ShpfileType.SHP_POINT);
                    MapWinGIS.Point pt = new MapWinGIS.Point();
                    pt.x = data.Longitude;
                    pt.y = data.Latitude;
                    int j = 0;
                    spBase.InsertPoint(pt, ref j);
                    int shpIdx = 0;
                    shpFile.EditInsertShape(spBase, ref shpIdx);
                    shpFile.EditCellValue(fiFileName, shpIdx, data.FileName);
                    shpFile.EditCellValue(fiDateTime, shpIdx, data.DateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                    shpFile.EditCellValue(fiLongitude, shpIdx, data.Longitude);
                    shpFile.EditCellValue(fiLatitude, shpIdx, data.Latitude);
                    shpFile.EditCellValue(fiMainCellName, shpIdx, "");
                    shpFile.EditCellValue(fiCoverLevel, shpIdx, level);
                    shpFile.EditCellValue(fiCoverCellName, shpIdx, "");
                    if (bWaitBox)
                    {
                        WaitBox.ProgressPercent = (int)(100.0 * ++iLoop / pointInfo.Count);
                    }
                }
                string delPath = System.IO.Path.GetDirectoryName(filename);
                string delFile = System.IO.Path.GetFileNameWithoutExtension(filename);
                System.IO.File.Delete($@"{delPath}\{delFile}.shp");
                System.IO.File.Delete($@"{delPath}\{delFile}.dbf");
                System.IO.File.Delete($@"{delPath}\{delFile}.prj");
                System.IO.File.Delete($@"{delPath}\{delFile}.shx");
                shpFile.SaveAs(filename, null);
                shpFile.Close();
                return 1;
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(ex.Message);
                return -1;
            }
        }

        protected virtual RoadMultiCoverageInfoBase getValidData(RoadMultiCoverageInfoBase point)
        {
            RoadMultiCoverageInfoBase multiCovLelInfo = null;
            //if (obj is RoadMultiCoverageInfo_TD)
            //{
            //    multiCovLelInfo = obj as RoadMultiCoverageInfo_TD;
            //}
            //else
            //{
            //    multiCovLelInfo = obj as RoadMultiCoverageInfo;
            //    if (ShowBand != multiCovLelInfo.BandType)
            //    {
            //        //continue;
            //    }
            //}
            return multiCovLelInfo;
        }

        private int getDataLevel(RoadMultiCoverageInfoBase data)
        {
            int level = -1;
            if (ShowCoverageType == ShowCoverage.Relative)
            {
                level = data.RelativeLevel;
            }
            else if (ShowCoverageType == ShowCoverage.Absolute)
            {
                level = data.AbsoluteLevel;
            }
            else if (ShowCoverageType == ShowCoverage.Synthesize)
            {
                level = data.RelANDAbsLevel;
            }
            if (data.InvalidatePoint)
            {
                level = -1;//无效点
            }

            return level;
        }

        protected int outputShpFile_Grid(string filename, List<GridRoadMultiCoverageInfoBase> pointInfo, List<ColorRange> modeRange, bool bWaitBox)
        {
            try
            {
                Shapefile shpFile = new Shapefile();
                bool result = shpFile.CreateNewWithShapeID("", ShpfileType.SHP_POLYGON);
                if (!result)
                {
                    MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    return -1;
                }
                int idIdx = 0;
                int fiLongitude = idIdx++;
                int fiLatitude = idIdx++;
                int fiCoverLevel = idIdx;
                ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiLongitude);
                ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiLatitude);
                ShapeHelper.InsertNewField(shpFile, "CoverLevel", FieldType.INTEGER_FIELD, 10, 30, ref fiCoverLevel);

                int iLoop = 0;
                foreach (var point in pointInfo)
                {
                    GridRoadMultiCoverageInfoBase grid = getValidGrid(point);
                    if (grid == null)
                    {
                        continue;
                    }

                    int level = getGridLevel(grid);

                    Color color;
                    if (modeRange == null)
                    {
                        color = Color.Red;
                    }
                    else
                    {
                        color = GetColor(level, modeRange);
                    }
                    if (color == Color.Empty)
                        continue;

                    int numShp = 0;
                    shpFile.EditInsertShape(ShapeHelper.CreateRectShape(grid.ltLong, grid.ltLat, grid.brLong, grid.brLat), ref numShp);
                    shpFile.EditCellValue(fiLongitude, numShp, grid.midLong);
                    shpFile.EditCellValue(fiLatitude, numShp, grid.midLat);
                    shpFile.EditCellValue(fiCoverLevel, numShp, level);

                    if (bWaitBox)
                    {
                        WaitBox.ProgressPercent = (int)(100.0 * ++iLoop / pointInfo.Count);
                    }
                }
                string delPath = System.IO.Path.GetDirectoryName(filename);
                string delFile = System.IO.Path.GetFileNameWithoutExtension(filename);
                System.IO.File.Delete($@"{delPath}\{delFile}.shp");
                System.IO.File.Delete($@"{delPath}\{delFile}.dbf");
                System.IO.File.Delete($@"{delPath}\{delFile}.prj");
                System.IO.File.Delete($@"{delPath}\{delFile}.shx");
                if (!shpFile.SaveAs(filename, null))
                {
                    MessageBox.Show("保存文件失败！" + shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    shpFile.Close();
                    return -1;
                }
                shpFile.Close();
                return 1;
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(ex.Message);
                return -1;
            }
        }

        protected virtual GridRoadMultiCoverageInfoBase getValidGrid(GridRoadMultiCoverageInfoBase point)
        {
            GridRoadMultiCoverageInfoBase grid = null;
            //if (obj is GridRoadMultiCoverageInfo_TD)
            //{
            //    grid = obj as GridRoadMultiCoverageInfo_TD;
            //}
            //else
            //{
            //    grid = obj as GridRoadMultiCoverageInfo;
            //}
            return grid;
        }

        protected int getGridLevel(GridRoadMultiCoverageInfoBase grid)
        {
            int level = -1;
            if (ShowCoverageType == ShowCoverage.Relative)
            {
                level = grid.RelativeLevel;
            }
            else if (ShowCoverageType == ShowCoverage.Absolute)
            {
                level = grid.AbsoluteLevel;
            }
            else if (ShowCoverageType == ShowCoverage.Synthesize)
            {
                level = grid.RelANDAbsLevel;
            }

            return level;
        }
        #endregion
    }
}
