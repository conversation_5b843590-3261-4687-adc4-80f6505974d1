﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.NewBlackBlock
{
    public enum EImageValueType
    {
        Null,
        Byte,
        SByte,
        UShort,
        Short,
        UInt,
        Int,
        ULong,
        Long,
        Float,
        Double,
        String,
        ByteArray
    }

    public class KeyValueImageParser
    {
        protected static int maxValueSize { get; set; } = 65535;

        protected KeyValueImageParser()
        {
        }

        public static string BytesToHexString(byte[] bytes)
        {
            return BitConverter.ToString(bytes).Replace("-", "");
        }

        protected static void FillPairBytes(ref List<byte> lst, KeyValuePair<uint, object> pair)
        {
            byte[] numArray;
            if (pair.Value == null)
            {
                KeyValueImageParser.FillPairBytes(ref lst, pair.Key, EImageValueType.Null, 0, null);
            }
            else if (pair.Value is byte)
            {
                uint key = pair.Key;
                numArray = new byte[] { (byte)pair.Value };
                KeyValueImageParser.FillPairBytes(ref lst, key, EImageValueType.Byte, 1, numArray);
            }
            else if (pair.Value is sbyte)
            {
                uint num = pair.Key;
                numArray = new byte[] { (byte)((sbyte)pair.Value) };
                KeyValueImageParser.FillPairBytes(ref lst, num, EImageValueType.SByte, 1, numArray);
            }
            else if (pair.Value is ushort)
            {
                KeyValueImageParser.FillPairBytes(ref lst, pair.Key, EImageValueType.UShort, 2, BitConverter.GetBytes((ushort)pair.Value));
            }
            else if (pair.Value is short)
            {
                KeyValueImageParser.FillPairBytes(ref lst, pair.Key, EImageValueType.Short, 2, BitConverter.GetBytes((short)pair.Value));
            }
            else if (pair.Value is uint)
            {
                KeyValueImageParser.FillPairBytes(ref lst, pair.Key, EImageValueType.UInt, 4, BitConverter.GetBytes((uint)pair.Value));
            }
            else if (pair.Value is int)
            {
                KeyValueImageParser.FillPairBytes(ref lst, pair.Key, EImageValueType.Int, 4, BitConverter.GetBytes((int)pair.Value));
            }
            else if (pair.Value is ulong)
            {
                KeyValueImageParser.FillPairBytes(ref lst, pair.Key, EImageValueType.ULong, 8, BitConverter.GetBytes((ulong)pair.Value));
            }
            else if (pair.Value is long)
            {
                KeyValueImageParser.FillPairBytes(ref lst, pair.Key, EImageValueType.Long, 8, BitConverter.GetBytes((long)pair.Value));
            }
            else if (pair.Value is float)
            {
                KeyValueImageParser.FillPairBytes(ref lst, pair.Key, EImageValueType.Float, 4, BitConverter.GetBytes((float)pair.Value));
            }
            else if (pair.Value is double)
            {
                KeyValueImageParser.FillPairBytes(ref lst, pair.Key, EImageValueType.Double, 8, BitConverter.GetBytes((double)pair.Value));
            }
            else if (!(pair.Value is string))
            {
                setString(ref lst, pair);
            }
            else
            {
                setdefault(ref lst, pair);
            }
        }

        private static void setString(ref List<byte> lst, KeyValuePair<uint, object> pair)
        {
            byte[] value;
            if (!(pair.Value is byte[]))
            {
                throw (new Exception(string.Format("Not Supported Value Type {0}, Key is {1}", pair.Value.GetType().FullName, pair.Key)));
            }
            value = pair.Value as byte[];
            if (value.Length > KeyValueImageParser.maxValueSize)
            {
                throw (new Exception(string.Format("Size of Value Larger Than {0}, Key is {1}", KeyValueImageParser.maxValueSize, pair.Key)));
            }
            KeyValueImageParser.FillPairBytes(ref lst, pair.Key, EImageValueType.ByteArray, value.Length, value);
        }

        private static void setdefault(ref List<byte> lst, KeyValuePair<uint, object> pair)
        {
            byte[] value = Encoding.Default.GetBytes(pair.Value as string);
            if (value.Length > KeyValueImageParser.maxValueSize)
            {
                throw (new Exception(string.Format("Size of Value Larger Than {0}, Key is {1}", KeyValueImageParser.maxValueSize, pair.Key)));
            }
            KeyValueImageParser.FillPairBytes(ref lst, pair.Key, EImageValueType.String, value.Length, value);
        }

        private static void FillPairBytes(ref List<byte> lst, uint key, EImageValueType vType, int vLen, byte[] vBytes)
        {
            lst.AddRange(BitConverter.GetBytes(key));
            lst.Add((byte)vType);
            lst.AddRange(BitConverter.GetBytes((ushort)vLen));
            if (vLen > 0)
            {
                lst.AddRange(vBytes);
            }
        }

        public static Dictionary<uint, object> FromImage(byte[] bytes)
        {
            Dictionary<uint, object> nums;
            Dictionary<uint, object> nums1 = new Dictionary<uint, object>();
            if (bytes.Length != 0)
            {
                int num = 0;
                uint num1 = BitConverter.ToUInt32(bytes, num);
                num = num + 4;
                for (int i = 0; i < num1; i++)
                {
                    uint num2 = BitConverter.ToUInt32(bytes, num);
                    num = num + 4;
                    EImageValueType eImageValueType = (EImageValueType)bytes[num];
                    num++;
                    ushort num3 = BitConverter.ToUInt16(bytes, num);
                    num = num + 2;
                    object obj = KeyValueImageParser.ParseBytes(eImageValueType, (int)num3, bytes, num);
                    num = num + num3;
                    nums1.Add(num2, obj);
                }
                nums = nums1;
            }
            else
            {
                nums = nums1;
            }
            return nums;
        }

        public static bool GetValueType(object value, out EImageValueType valueType)
        {
            bool flag;
            if (value == null)
            {
                valueType = EImageValueType.Null;
            }
            else if (value is byte)
            {
                valueType = EImageValueType.Byte;
            }
            else if (value is sbyte)
            {
                valueType = EImageValueType.SByte;
            }
            else if (value is ushort)
            {
                valueType = EImageValueType.UShort;
            }
            else if (value is short)
            {
                valueType = EImageValueType.Short;
            }
            else if (value is uint)
            {
                valueType = EImageValueType.UInt;
            }
            else if (value is int)
            {
                valueType = EImageValueType.Int;
            }
            else if (value is ulong)
            {
                valueType = EImageValueType.ULong;
            }
            else if (value is long)
            {
                valueType = EImageValueType.Long;
            }
            else if (value is float)
            {
                valueType = EImageValueType.Float;
            }
            else if (value is double)
            {
                valueType = EImageValueType.Double;
            }
            else if (!(value is string))
            {
                if (value is byte[])
                {
                    valueType = EImageValueType.ByteArray;
                    flag = true;
                    return flag;
                }
                valueType = EImageValueType.Null;
                flag = false;
                return flag;
            }
            else
            {
                valueType = EImageValueType.String;
            }
            flag = true;
            return flag;
        }

        public static bool HexStringToBytes(string hexString, out byte[] bytes)
        {
            byte num;
            bool flag;
            if (hexString.StartsWith("0x", StringComparison.CurrentCultureIgnoreCase))
            {
                hexString = hexString.Substring(2);
            }
            if (hexString.Length % 2 == 1)
            {
                hexString = string.Concat("0", hexString);
            }
            bytes = new byte[hexString.Length / 2];
            int num1 = 0;
            while (true)
            {
                if (num1 >= hexString.Length)
                {
                    flag = true;
                    break;
                }
                else if (byte.TryParse(hexString.Substring(num1, 2), NumberStyles.HexNumber, (IFormatProvider)null, out num))
                {
                    bytes[num1 / 2] = num;
                    num1 = num1 + 2;
                }
                else
                {
                    bytes = null;
                    flag = false;
                    break;
                }
            }
            return flag;
        }

        protected static object ParseBytes(EImageValueType type, int length, byte[] bytes, int offset)
        {
            object num;
            switch (type)
            {
                case EImageValueType.Null:
                    {
                        num = null;
                        break;
                    }
                case EImageValueType.Byte:
                    {
                        num = bytes[offset];
                        break;
                    }
                case EImageValueType.SByte:
                    {
                        num = (sbyte)bytes[offset];
                        break;
                    }
                case EImageValueType.UShort:
                    {
                        num = BitConverter.ToUInt16(bytes, offset);
                        break;
                    }
                case EImageValueType.Short:
                    {
                        num = BitConverter.ToInt16(bytes, offset);
                        break;
                    }
                case EImageValueType.UInt:
                    {
                        num = BitConverter.ToUInt32(bytes, offset);
                        break;
                    }
                case EImageValueType.Int:
                    {
                        num = BitConverter.ToInt32(bytes, offset);
                        break;
                    }
                case EImageValueType.ULong:
                    {
                        num = BitConverter.ToUInt64(bytes, offset);
                        break;
                    }
                case EImageValueType.Long:
                    {
                        num = BitConverter.ToInt64(bytes, offset);
                        break;
                    }
                case EImageValueType.Float:
                    {
                        num = BitConverter.ToSingle(bytes, offset);
                        break;
                    }
                case EImageValueType.Double:
                    {
                        num = BitConverter.ToDouble(bytes, offset);
                        break;
                    }
                case EImageValueType.String:
                    {
                        num = Encoding.Default.GetString(bytes, offset, length);
                        break;
                    }
                case EImageValueType.ByteArray:
                    {
                        byte[] numArray = new byte[length];
                        Array.Copy(bytes, offset, numArray, 0, length);
                        num = numArray;
                        break;
                    }
                default:
                    {
                        throw (new Exception(string.Concat("Unknow Value Type in Image, Type is ", type.ToString())));
                    }
            }
            return num;
        }

        public static byte[] ToImage(Dictionary<uint, object> dic)
        {
            List<byte> nums = new List<byte>();
            nums.AddRange(BitConverter.GetBytes((uint)dic.Count));
            foreach (KeyValuePair<uint, object> keyValuePair in dic)
            {
                KeyValueImageParser.FillPairBytes(ref nums, keyValuePair);
            }
            return nums.ToArray();
        }
    }

    class KeyValueImageParserV1 : KeyValueImageParser
    {
        public KeyValueImageParserV1()
        {
            maxValueSize = int.MaxValue;
        }

        public new static byte[] ToImage(Dictionary<uint, object> dic)
        {
            List<byte> retBytes = new List<byte>();
            retBytes.AddRange(BitConverter.GetBytes(ConvertPairCount(dic.Count)));
            foreach (KeyValuePair<uint, object> kvp in dic)
            {
                KeyValueImageParser.FillPairBytes(ref retBytes, kvp);
            }
            return retBytes.ToArray();
        }

        public new static Dictionary<uint, object> FromImage(byte[] bytes)
        {
            Dictionary<uint, object> retDic = new Dictionary<uint, object>();
            if (bytes.Length == 0)
            {
                return retDic;
            }

            int offset = 0;
            uint pairCount = ConvertPairCount(bytes, offset);
            uint version = GetVersion(bytes, offset);

            offset += 4;
            for (int i = 0; i < pairCount; ++i)
            {
                uint key = BitConverter.ToUInt32(bytes, offset);
                offset += 4;

                EImageValueType type = (EImageValueType)bytes[offset];
                offset += 1;

                int length = 0;
                if (version == versionMask && (type == EImageValueType.ByteArray || type == EImageValueType.String))
                {
                    length = BitConverter.ToInt32(bytes, offset);
                    offset += 4;
                }
                else
                {
                    length = BitConverter.ToUInt16(bytes, offset);
                    offset += 2;
                }

                object value = KeyValueImageParser.ParseBytes(type, length, bytes, offset);
                offset += length;

                retDic.Add(key, value);
            }

            return retDic;
        }

        protected static void FillPairBytes(ref List<byte> lst, uint key, EImageValueType vType, int vLen, byte[] vBytes)
        {
            lst.AddRange(BitConverter.GetBytes(key));
            lst.Add((byte)vType);
            lst.AddRange(vType == EImageValueType.ByteArray || vType == EImageValueType.String ?
                BitConverter.GetBytes(vLen) : BitConverter.GetBytes((ushort)vLen));
            if (vLen > 0)
            {
                lst.AddRange(vBytes);
            }
        }

        private static uint ConvertPairCount(int dicCount)
        {
            if (dicCount > 0x00FFFFFF)
            {
                throw (new Exception("Dictionary.Count too large!"));
            }
            return versionMask | (uint)dicCount;
        }

        private static uint ConvertPairCount(byte[] bytes, int offset)
        {
            return BitConverter.ToUInt32(bytes, offset) & (~versionMask);
        }

        private static uint GetVersion(byte[] bytes, int offset)
        {
            return BitConverter.ToUInt32(bytes, offset) & versionMask;
        }

        private static uint versionMask = 0x01000000;
    }

    /// <summary>
    /// 小区告警
    /// </summary>
    public class CellAlarmData : CellFusionDataBase
    {
        public DateTime EndTime
        {
            get;
            set;
        }
        public string Desc
        {
            get;
            set;
        }
        public string TypeName
        {
            get;
            set;
        }

        private static Dictionary<int, string> kpiKey_NameDic = null;
        public static Dictionary<int, string> KpiKey_NameDic
        {
            get
            {
                if (kpiKey_NameDic == null)
                {
                    kpiKey_NameDic = new Dictionary<int, string>();
                    kpiKey_NameDic[0x01010101] = "网元类型";
                    kpiKey_NameDic[0x01010102] = "告警源";
                    kpiKey_NameDic[0x01010103] = "MO对象";
                    kpiKey_NameDic[0x01010104] = "定位信息";
                    kpiKey_NameDic[0x01010105] = "确认时间";
                    kpiKey_NameDic[0x01010106] = "确认用户";
                    kpiKey_NameDic[0x01010107] = "所属子网";
                    kpiKey_NameDic[0x01010108] = "网元名称";
                }
                return kpiKey_NameDic;
            }
        }
    }

    /// <summary>
    /// 小区性能
    /// </summary>
    public class CellPerfData : CellFusionDataBase
    {
        public string 空口上行业务字节数
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x05010101, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        public string 空口下行业务字节数
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x05010102, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        public string RRC连接平均数
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x05010103, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        public string RRC连接最大数
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x05010104, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        public string eNB接收干扰功率平均值
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x05010105, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        public string 上行PRB平均利用率
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x05010106, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        public string 下行PRB平均利用率
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x05010107, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        public string 无线利用率
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x05010108, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        public string RRC连接建立请求次数
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x05010109, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        public string RRC连接建立成功次数
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x0501010A, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        public string RRC连接建立成功率
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x0501010B, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        public string E_RAB建立请求数
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x0501010C, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        public string E_RAB建立成功数
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x0501010D, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        public string E_RAB建立成功率
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x0501010E, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        public string 无线接通率
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x0501010F, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        public string eNB请求释放上下文数
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x05010110, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        public string 正常的eNB请求释放上下文数
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x05010111, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }
        public string 初始上下文建立成功次数
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x05010112, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }
        public string 无线掉线率
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x05010113, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }
        public string 切换成功率
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x0501017F, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        private static Dictionary<int, string> kpiKey_NameDic = null;
        public static Dictionary<int, string> KpiKey_NameDic
        {
            get
            {
                if (kpiKey_NameDic == null)
                {
                    kpiKey_NameDic = new Dictionary<int, string>();
                    kpiKey_NameDic[0x05010101] = "空口上行业务字节数";
                    kpiKey_NameDic[0x05010102] = "空口下行业务字节数";
                    kpiKey_NameDic[0x05010103] = "RRC连接平均数";
                    kpiKey_NameDic[0x05010104] = "RRC连接最大数";
                    kpiKey_NameDic[0x05010105] = "eNB接收干扰功率平均值";
                    kpiKey_NameDic[0x05010106] = "上行PRB平均利用率";
                    kpiKey_NameDic[0x05010107] = "下行PRB平均利用率";
                    kpiKey_NameDic[0x05010108] = "无线利用率";
                    kpiKey_NameDic[0x05010109] = "RRC连接建立请求次数";
                    kpiKey_NameDic[0x0501010A] = "RRC连接建立成功次数";
                    kpiKey_NameDic[0x0501010B] = "RRC连接建立成功率";
                    kpiKey_NameDic[0x0501010C] = "E-RAB建立请求数";
                    kpiKey_NameDic[0x0501010D] = "E-RAB建立成功数";
                    kpiKey_NameDic[0x0501010E] = "E-RAB建立成功率";
                    kpiKey_NameDic[0x0501010F] = "无线接通率";
                    kpiKey_NameDic[0x05010110] = "eNB请求释放上下文数";
                    kpiKey_NameDic[0x05010111] = "正常的eNB请求释放上下文数";
                    kpiKey_NameDic[0x05010112] = "初始上下文建立成功次数";
                    kpiKey_NameDic[0x05010113] = "无线掉线率";
                }
                return kpiKey_NameDic;
            }
        }
    }

    /// <summary>
    /// 小区参数
    /// </summary>
    public class CellArgData : CellFusionDataBase
    {
        public string 上下行子帧配比
        {
            get { return "1:03"; }
        }
        public string 参考信号功率
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x02010101, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        public string PDSCH采用均匀功率分配时的PA值
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x02010102, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }
        public string PB
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x02010103, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }
        public string 小区偏移量
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x02010104, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }
        public string 小区偏置
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x02010105, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }
        public string 上下行业务子帧配置
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x02010106, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }
        public string 特殊子帧配置
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x02010107, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }
        public string 同频_A3_offset
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x02010108, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }
        public string 同频_A3_Hysteresis
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x02010109, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }
        public string 同频_A3_Time_to_trigger
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x0201010A, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }
        public string 异频A2门限
        {
            get
            {
                if (DataDic == null)
                {
                    return "-";
                }
                object ret;
                DataDic.TryGetValue(0x0201010B, out ret);
                if (ret == null)
                {
                    return "-";
                }
                return ret.ToString();
            }
        }

        private static Dictionary<int, string> kpiKey_NameDic = null;
        public static Dictionary<int, string> KpiKey_NameDic
        {
            get
            {
                if (kpiKey_NameDic == null)
                {
                    kpiKey_NameDic = new Dictionary<int, string>();
                    kpiKey_NameDic[0x02010101] = "参考信号功率";
                    kpiKey_NameDic[0x02010102] = "PDSCH采用均匀功率分配时的PA值";
                    kpiKey_NameDic[0x02010103] = "PB";
                    kpiKey_NameDic[0x02010104] = "小区偏移量";
                    kpiKey_NameDic[0x02010105] = "小区偏置";
                    kpiKey_NameDic[0x02010106] = "上下行业务子帧配置";
                    kpiKey_NameDic[0x02010107] = "特殊子帧配置";
                    kpiKey_NameDic[0x02010108] = "同频 A3 offset";
                    kpiKey_NameDic[0x02010109] = "同频 A3 Hysteresis";
                    kpiKey_NameDic[0x0201010A] = "同频 A3 Time-to-trigger";
                    kpiKey_NameDic[0x0201010B] = "异频A2门限（A2 Threshold)";
                }
                return kpiKey_NameDic;
            }
        }
    }

    public abstract class CellFusionDataBase
    {         
        public static string FusionDB { get; set; } = "[10.238.68.136].[FusionAna_DB]";
        public LTECell Cell
        {
            get;
            set;
        }
        public string CellName
        {
            get { return Cell.Name; }
        }
        public string Token
        {
            get { return Cell.Token; }
        }
        public int DistrictId { get; set; }
        public int TAC
        {
            get { return Cell.TAC; }
        }
        public int ECI
        {
            get { return Cell.ECI; }
        }
        public int BTSID
        {
            get { return Cell.BelongBTS.BTSID; }
        }
        public string BTSName
        {
            get { return Cell.BelongBTS.Name; }
        }
        public DateTime BeginTime
        {
            get;
            set;
        }
        public string BeginTimeDes
        {
            get
            {
                return BeginTime.ToString();
            }
        }
        
        public Dictionary<uint, object> DataDic { get; set; }
        public void FillImg(byte[] img)
        {
            DataDic = KeyValueImageParser.FromImage(img);
        }
    }
}
