﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.ES.Data
{
    public class FuncInfo
    {
        public string Name { get; set; }
        public string Desc { get; set; }
        public List<ParamInfo> ParamDescList { get; set; } = new List<ParamInfo>();
        public event EventHandler eventHandler;
        public bool fromReplay { get; set; } = true;
        public FuncInfo(string name,string desc,EventHandler handler)
        {
            this.Name = name;
            this.Desc = desc;
            this.eventHandler = handler;
        }
        public FuncInfo(string name, string desc, EventHandler handler, bool fromReplay)
        {
            this.Name = name;
            this.Desc = desc;
            this.eventHandler = handler;
            this.fromReplay = fromReplay;
        }
        public void ExecuteReturnInfo(string paramStr,out object retObj)
        {
            CommonEventArgs arg = new CommonEventArgs();
            arg.param = paramStr;
            object sender = null;
            eventHandler(sender, arg);
            if(!Double.IsNaN(arg.ret))
            {
                retObj = arg.ret;
            }
            else
            {
                retObj = arg.str;
            }
        }
        public override string ToString()
        {
            return Name;
        }
    }
    public class VirtualFuncGroup
    {
        public string Name { get; set; }
        public List<FuncInfo> Funcs { get; set; } = new List<FuncInfo>();
        public VirtualFuncGroup()
        {

        }
        public VirtualFuncGroup(string name)
        {
            this.Name = name;
        }
        public override string ToString()
        {
            return Name;
        }
    };

    public class ParamInfo
    {
        public string desc { get; set; }
        public ParamInfo(string s)
        {
            this.desc = s;
        }
    };
    public class CommonEventArgs : EventArgs
    {
        public string param { get; set; }
        public object argData { get; set; }
        public double ret { get; set; } = double.NaN;
        public string str { get; set; } = string.Empty;
    };
}
