﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRNCellLevelHigherDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.numLevelDB = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.chkMinDuration = new System.Windows.Forms.CheckBox();
            this.chkMinDistance = new System.Windows.Forms.CheckBox();
            this.label18 = new System.Windows.Forms.Label();
            this.numMinDuration = new System.Windows.Forms.NumericUpDown();
            this.label15 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.numRate = new System.Windows.Forms.NumericUpDown();
            this.numMinDistance = new System.Windows.Forms.NumericUpDown();
            this.chkCoOnly = new System.Windows.Forms.CheckBox();
            ((System.ComponentModel.ISupportInitialize)(this.numLevelDB.Properties)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDuration)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDistance)).BeginInit();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(196, 223);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 46;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(102, 223);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 45;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(228, 18);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(36, 12);
            this.labelControl2.TabIndex = 44;
            this.labelControl2.Text = "db以上";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(12, 17);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(156, 12);
            this.labelControl1.TabIndex = 43;
            this.labelControl1.Text = "邻区电平强于主服务小区电平";
            // 
            // numLevelDB
            // 
            this.numLevelDB.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numLevelDB.Location = new System.Drawing.Point(174, 14);
            this.numLevelDB.Name = "numLevelDB";
            this.numLevelDB.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numLevelDB.Properties.Appearance.Options.UseFont = true;
            this.numLevelDB.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numLevelDB.Properties.IsFloatValue = false;
            this.numLevelDB.Properties.Mask.EditMask = "n0";
            this.numLevelDB.Properties.MaxValue = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numLevelDB.Size = new System.Drawing.Size(49, 20);
            this.numLevelDB.TabIndex = 42;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.chkMinDuration);
            this.groupBox3.Controls.Add(this.chkMinDistance);
            this.groupBox3.Controls.Add(this.label18);
            this.groupBox3.Controls.Add(this.numMinDuration);
            this.groupBox3.Controls.Add(this.label15);
            this.groupBox3.Controls.Add(this.label4);
            this.groupBox3.Controls.Add(this.label16);
            this.groupBox3.Controls.Add(this.numRate);
            this.groupBox3.Controls.Add(this.numMinDistance);
            this.groupBox3.Location = new System.Drawing.Point(12, 83);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(252, 123);
            this.groupBox3.TabIndex = 65;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "持续性";
            // 
            // chkMinDuration
            // 
            this.chkMinDuration.AutoSize = true;
            this.chkMinDuration.Location = new System.Drawing.Point(39, 96);
            this.chkMinDuration.Name = "chkMinDuration";
            this.chkMinDuration.Size = new System.Drawing.Size(84, 16);
            this.chkMinDuration.TabIndex = 67;
            this.chkMinDuration.Text = "持续时长≥";
            this.chkMinDuration.UseVisualStyleBackColor = true;
            // 
            // chkMinDistance
            // 
            this.chkMinDistance.AutoSize = true;
            this.chkMinDistance.Checked = true;
            this.chkMinDistance.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkMinDistance.Location = new System.Drawing.Point(38, 61);
            this.chkMinDistance.Name = "chkMinDistance";
            this.chkMinDistance.Size = new System.Drawing.Size(84, 16);
            this.chkMinDistance.TabIndex = 66;
            this.chkMinDistance.Text = "持续距离≥";
            this.chkMinDistance.UseVisualStyleBackColor = true;
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(217, 96);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(17, 12);
            this.label18.TabIndex = 65;
            this.label18.Text = "秒";
            // 
            // numMinDuration
            // 
            this.numMinDuration.Location = new System.Drawing.Point(130, 91);
            this.numMinDuration.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMinDuration.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numMinDuration.Name = "numMinDuration";
            this.numMinDuration.Size = new System.Drawing.Size(80, 21);
            this.numMinDuration.TabIndex = 63;
            this.numMinDuration.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinDuration.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(220, 25);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(11, 12);
            this.label15.TabIndex = 62;
            this.label15.Text = "%";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(217, 62);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 48;
            this.label4.Text = "米";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(11, 25);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(113, 12);
            this.label16.TabIndex = 61;
            this.label16.Text = "邻区高电平点占比≥";
            // 
            // numRate
            // 
            this.numRate.Location = new System.Drawing.Point(131, 20);
            this.numRate.Name = "numRate";
            this.numRate.Size = new System.Drawing.Size(80, 21);
            this.numRate.TabIndex = 60;
            this.numRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRate.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // numMinDistance
            // 
            this.numMinDistance.Location = new System.Drawing.Point(130, 57);
            this.numMinDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMinDistance.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numMinDistance.Name = "numMinDistance";
            this.numMinDistance.Size = new System.Drawing.Size(80, 21);
            this.numMinDistance.TabIndex = 3;
            this.numMinDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinDistance.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // chkCoOnly
            // 
            this.chkCoOnly.AutoSize = true;
            this.chkCoOnly.Location = new System.Drawing.Point(12, 47);
            this.chkCoOnly.Name = "chkCoOnly";
            this.chkCoOnly.Size = new System.Drawing.Size(84, 16);
            this.chkCoOnly.TabIndex = 66;
            this.chkCoOnly.Text = "只分析同频";
            this.chkCoOnly.UseVisualStyleBackColor = true;
            // 
            // NRNCellLevelHigherDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(280, 258);
            this.Controls.Add(this.chkCoOnly);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.numLevelDB);
            this.Name = "NRNCellLevelHigherDlg";
            this.Text = "邻区电平条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numLevelDB.Properties)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDuration)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDistance)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit numLevelDB;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.CheckBox chkMinDuration;
        private System.Windows.Forms.CheckBox chkMinDistance;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.NumericUpDown numMinDuration;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.NumericUpDown numRate;
        private System.Windows.Forms.NumericUpDown numMinDistance;
        private System.Windows.Forms.CheckBox chkCoOnly;
    }
}