﻿using System;
using System.Collections.Generic;
using System.Text;
using GMap.NET;
using MasterCom.MTGis;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc;
using System.Drawing;
using System.Drawing.Drawing2D;

namespace MasterCom.RAMS.ExMap
{
    //copy from file ExMapRevRegionLayer.cs
    public class ExMapScanMod3IndexLayer : ExMapDrawBaseLayer
    {
        public ExMapScanMod3IndexLayer(MTExGMap mapCtrl)
            : base(mapCtrl)
        {
        }

        public double mapViewZoom { get; set; } = 1;
        public void setZoom(double mapViewZoom)
        {
            this.mapViewZoom = mapViewZoom;
        }
        private List<List<PointLatLng>> regionPointSet { get; set; }
        
        internal void ApplyResvRegion(List<ResvRegion> list)
        {
            regionPointSet = new List<List<PointLatLng>>();
            if (list == null)
            {
                return;
            }
            foreach (ResvRegion reg in list)
            {
                for (int x = 0; x < reg.Shape.NumParts; x++)
                {

                    List<PointLatLng> pnts = ShapeHelper.GetPartShapePointsEx(reg.Shape, x);
                    if (pnts != null && pnts.Count > 0)
                    {
                        regionPointSet.Add(pnts);
                    }
                }
            }
        }

        private List<ScanMod3IndexDrawItem> drawList { get; set; }//在图中要画的模三干扰指数的点

        private float getRadius()
        {
            float radius = 0;
            if (mapViewZoom > 282)
            {
                radius = 1;
            }
            else if (mapViewZoom > 34)
            {
                radius = 2;
            }
            else if (mapViewZoom > 8)
            {
                radius = 4;
            }
            else if (mapViewZoom == 1)
            {
                radius = 4;
            }
            else
            {
                radius = 8;
            }
            return radius;
        }
        public override void Draw(System.Drawing.Graphics g, PointLatLng ltPt, PointLatLng brPt)
        {
            if (!this.exMap.ParentMapForm.ScanMod3IndexLayer.IsVisible)
            {
                return;
            }
            drawList = this.exMap.ParentMapForm.ScanMod3IndexLayer.Entitys2Draw;
            ScanMod3IndexLegend legend = exMap.ParentMapForm.ScanMod3IndexLayer.Legend;
            if (drawList == null || drawList.Count == 0)
            {
                return;
            }
            DbRect dRect = new DbRect(ltPt.Lng, ltPt.Lat, brPt.Lng, brPt.Lat);
            SolidBrush bushBlack = new SolidBrush(Color.Black);
            SolidBrush bush = new SolidBrush(Color.Green);
            Color color;
            GraphicsPath path = null;
            float radius = this.getRadius() ;
            
            foreach (ScanMod3IndexDrawItem drawItem in drawList)
            {
                if (drawItem.Within(dRect))
                {
                    if (!legend.GetDisplayStyle(drawItem, out color, out path))
                    {
                        continue;
                    }
                    PointLatLng pointll = new PointLatLng(drawItem.TestPoint.Latitude, drawItem.TestPoint.Longitude);
                    GPoint gPnt = this.exMap.FromLatLngToLocalAdaptered(pointll);
                    if (drawItem.Selected)
                    {
                        g.FillEllipse(bushBlack, gPnt.X - radius-3, gPnt.Y - radius-3, (radius+3) * 2, (radius+3) * 2+3);
                    }
                    bush.Color = color;
                    g.FillEllipse(bush, gPnt.X - radius, gPnt.Y - radius, radius * 2, radius * 2);
                }
            }
        }

        public override string Alias
        {
            get { return "预存区域图层"; }
        }
    }
}
