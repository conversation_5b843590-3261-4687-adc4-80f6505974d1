﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage.PK;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryArea2PeriodGrid : QueryKPIStatByRegion
    {
        protected override MasterCom.RAMS.Model.Interface.StatTbToken getTableNameToken()
        {
            return MasterCom.RAMS.Model.Interface.StatTbToken.grid;
        }

        public override string Name
        {
            get
            {
                return "村庄两时间段测试统计";
            }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 31000, 31005, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.None;
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        protected AreaReportTemplate curTemplate = null;
        private List<AreaBase> selAreas = null;
        protected ArchiveCondition archiveCondition { get; set; }
        private TimePeriod period1;
        private TimePeriod period2;
        DbRect bounds = null;
        protected override bool getConditionBeforeQuery()
        {
            archiveCondition = ArchiveSettingManager.GetInstance().Condition;

            if (archiveCondition.VillageCondition.RootLeafDic.Count == 0)
            {
                MessageBox.Show("请设置基础配置....", "提醒");
                return false;
            }
            IsShowResultForm = true;
            AreaPeriodTestSettingDlg dlg = new AreaPeriodTestSettingDlg(TemplateMngr.Instance);
            dlg.SelectedAreas = selAreas;
            dlg.Period1 = period1;
            dlg.Period2 = period2;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            curTemplate = dlg.SelTemplate;
            selAreas = dlg.SelectedAreas;
            period1 = dlg.Period1;
            period2 = dlg.Period2;
            bounds = null;
            foreach (AreaBase area in selAreas)
            {
                if (bounds == null)
                {
                    bounds = area.Bounds.Clone();
                }
                else
                {
                    bounds.MergeRects(area.Bounds);
                }
            }
            condition = archiveCondition.GetBaseConditionBackUp();
            List<TimePeriod> periods = new List<TimePeriod>();
            periods.Add(period1);
            periods.Add(period2);
            condition.Periods = periods;
            areaGridDic = new Dictionary<AreaBase, Area2PeriodGrid>();
            gridDataDic = new Dictionary<int, Dictionary<int, AreaKPIDataGroup<GridUnitBase>>>();
            gridDataDic2 = new Dictionary<int, Dictionary<int, AreaKPIDataGroup<GridUnitBase>>>();
            return true;
        }

        public override bool CanEnabled(Model.SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> expSet = new List<string>();
            foreach (TemplateColumn col in curTemplate.Columns)
            {
                expSet.Add(col.Expression);
            }
            return this.getTriadIDIgnoreServiceType(expSet);
        }

        bool isQueringPeriod1 = true;
        protected override void queryPeriodInfo(TimePeriod period, ClientProxy clientProxy, params object[] reservedParams)
        {
            isQueringPeriod1 = period == period1;
            base.queryPeriodInfo(period, clientProxy, reservedParams);
        }


        protected override void AddGeographicFilter(Package package)
        {
            if (isQueringEvent)
            {
                AddDIYEndOpFlag(package);
                package.Content.AddParam((byte)OpOptionDef.AreaSelectSample);
                package.Content.AddParam(bounds.x1);
                package.Content.AddParam(bounds.y2);
                package.Content.AddParam(bounds.x2);
                package.Content.AddParam(bounds.y1);
            }
            else
            {
                package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
                package.Content.AddParam(bounds.x1);
                package.Content.AddParam(bounds.y2);
                package.Content.AddParam(bounds.x2);
                package.Content.AddParam(bounds.y1);
            }
        }

        Dictionary<AreaBase, Area2PeriodGrid> areaGridDic = null;
        Dictionary<int, Dictionary<int, AreaKPIDataGroup<GridUnitBase>>> gridDataDic = new Dictionary<int, Dictionary<int, AreaKPIDataGroup<GridUnitBase>>>();
        Dictionary<int, Dictionary<int, AreaKPIDataGroup<GridUnitBase>>> gridDataDic2 = new Dictionary<int, Dictionary<int, AreaKPIDataGroup<GridUnitBase>>>();

        private AreaKPIDataGroup<GridUnitBase> getDataHub(GridUnitBase grid)
        {
            Dictionary<int, Dictionary<int, AreaKPIDataGroup<GridUnitBase>>> matrix = null;
            if (isQueringPeriod1)
            {
                matrix = gridDataDic;
            }
            else
            {
                matrix = gridDataDic2;
            }
            Dictionary<int, AreaKPIDataGroup<GridUnitBase>> dic = null;
            if (!matrix.TryGetValue(grid.RowIdx, out dic))
            {
                dic = new Dictionary<int, AreaKPIDataGroup<GridUnitBase>>();
                matrix[grid.RowIdx] = dic;
            }

            AreaKPIDataGroup<GridUnitBase> hub = null;
            if (!dic.TryGetValue(grid.ColIdx, out hub))
            {
                hub = new AreaKPIDataGroup<GridUnitBase>(grid);
                dic[grid.ColIdx] = hub;
            }
            return hub;
        }

        protected override void recieveAndHandleSpecificStatData(Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            fillStatData(package, curImgColumnDef, singleStatData);
            int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            saveAsGridData(grid, fi, singleStatData);
        }

        private void saveAsGridData(GridUnitBase grid, FileInfo fi, KPIStatDataBase statData)
        {
            AreaKPIDataGroup<GridUnitBase> hub = getDataHub(grid);
            hub.AddStatData(fi, statData);
        }

        protected override void handleStatEvent(Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, false, null);
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
            int rowIdx, colIdx;
            GridHelper.GetIndexOfDefaultSizeGrid(evt.Longitude, evt.Latitude, out rowIdx, out colIdx);
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase();
            grid.RowIdx = rowIdx;
            grid.ColIdx = colIdx;
            saveAsGridData(grid, fi, eventData);
        }

        protected override void afterRecieveOnePeriodData(params object[] reservedParams)
        {
            Dictionary<int, Dictionary<int, AreaKPIDataGroup<GridUnitBase>>> matrix = null;
            if (isQueringPeriod1)
            {
                matrix = gridDataDic;
            }
            else
            {
                matrix = gridDataDic2;
            }

            foreach (int row in matrix.Keys)
            {
                foreach (int col in matrix[row].Keys)
                {
                    addAreaGrid(matrix, row, col);
                }
            }
        }

        private void addAreaGrid(Dictionary<int, Dictionary<int, AreaKPIDataGroup<GridUnitBase>>> matrix, int row, int col)
        {
            AreaKPIDataGroup<GridUnitBase> grid = matrix[row][col];
            foreach (AreaBase area in selAreas)
            {
                if (area.Bounds.IsPointInThisRect(grid.Area.CenterLng, grid.Area.CenterLat)
                    && area.MapOper.CheckPointInRegion(grid.Area.CenterLng, grid.Area.CenterLat))
                {
                    Area2PeriodGrid areaGrid = null;
                    if (!areaGridDic.TryGetValue(area, out areaGrid))
                    {
                        areaGrid = new Area2PeriodGrid(area);
                        areaGridDic[area] = areaGrid;
                    }
                    areaGrid.AddStatData(isQueringPeriod1, grid);

                    break;
                }
            }
        }

        DataTable table = null;
        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            table = createDataTable(curTemplate);
            foreach (AreaBase area in areaGridDic.Keys)
            {
                Area2PeriodGrid areaGrid = areaGridDic[area];
                addRowInfo(area, areaGrid, areaGrid.DataGrp1, period1);
                addRowInfo(area, areaGrid, areaGrid.DataGrp2, period2);
            }
        }

        private void addRowInfo(AreaBase area, Area2PeriodGrid areaGrid, AreaKPIDataGroup<GridUnitBase> dataGrp, TimePeriod period)
        {
            if (dataGrp != null)
            {
                DataRow row = table.NewRow();
                row["Tag"] = areaGrid;
                row["村庄"] = area.FullName;
                row["时间段"] = period.BeginTime.ToString("yyyyMMdd")
                    + " ~ " + period.EndTime.ToString("yyyyMMdd");
                foreach (TemplateColumn col in curTemplate.Columns)
                {
                    double value = dataGrp.CalcFormula((CarrierType)col.CarrierID, col.MoMtFlag, col.Expression);
                    if (double.IsNaN(value))
                    {
                        row[col.Caption] = "-";
                    }
                    else
                    {
                        row[col.Caption] = value;
                    }
                }
                table.Rows.Add(row);
            }
        }

        private DataTable createDataTable(AreaReportTemplate template)
        {
            DataTable curTable = new DataTable();
            curTable.Columns.Add("Tag", typeof(Area2PeriodGrid));
            curTable.Columns.Add("村庄", typeof(string));
            curTable.Columns.Add("时间段", typeof(string));
            foreach (TemplateColumn col in template.Columns)
            {
                curTable.Columns.Add(col.Caption);
            }
            return curTable;
        }

        protected override void fireShowResult()
        {
            Area2PeriodTestResultForm frm = MainModel.CreateResultForm(typeof(Area2PeriodTestResultForm)) as Area2PeriodTestResultForm;
            frm.FillData(curTemplate,table, selAreas);
            frm.Visible = true;
            frm.BringToFront();

            table = null;
            areaGridDic = null;
            gridDataDic = null;
            gridDataDic2 = null;
        }

    }
}
