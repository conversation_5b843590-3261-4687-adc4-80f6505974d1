﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using BrightIdeasSoftware;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTLteFRFrequencyConVerifyInfoForm : MinCloseForm
    {
        public ZTLteFRFrequencyConVerifyInfoForm()
        {
            InitializeComponent();
#if LT
            foreach (BrightIdeasSoftware.OLVColumn column in this.ListViewFRVerify.AllColumns)
            {
                column.Text = column.Text.Replace("GSM", "WCDMA");
            }
#endif
            init();
        }

        private void init()
        {
            olvSN.AspectGetter = delegate (object row)
            {
                if (row is FRFreConVerifyInfo)
                {
                    FRFreConVerifyInfo item = row as FRFreConVerifyInfo;
                    return item.SN;
                }
                else if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.SN;
                }
                return "";
            };
            olvGridName.AspectGetter = delegate (object row)
            {
                if (row is FRFreConVerifyInfo)
                {
                    FRFreConVerifyInfo item = row as FRFreConVerifyInfo;
                    return item.GridName;
                }
                else if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.GridName;
                }
                return "";
            };
            #region 主叫
            olvFileName.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.FileName;
                }
                return "";
            };

            olvEventTime.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.EventTime;
                }
                return "";
            };

            olvEventLongitude.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.EventLongitude;
                }
                return "";
            };
            olvEventLatitude.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.EventLatitude;
                }
                return "";
            };
            setGsmData();
            olvHasFreON.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.HasFreON;
                }
                return "";
            };
            olvChannelReleList.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.ChannelReleList;
                }
                return "";
            };
            setLteData();
            olvNearLTECI.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.NearLTECI;
                }
                return "";
            };

            olvNearLTEName.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.NearLTEName;
                }
                return "";
            };

            olvNearLTEFre.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.NearLTEFre;
                }
                return "";
            };

            #endregion
            ListViewFRVerify.CanExpandGetter = delegate (object row)
            {
                if (row is FRFreConVerifyInfo)
                {
                    return true;
                }
                return false;
            };
            ListViewFRVerify.ChildrenGetter = delegate (object row)
            {
                FRFreConVerifyInfo item = row as FRFreConVerifyInfo;
                return item.ListFrItemsInfo;
            };
        }

        private void setLteData()
        {
            olvSCell_Name.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.SCell_Name;
                }
                return "";
            };
            olvSCell_BaseName.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.SCell_BaseName;
                }
                return "";
            };
            olvSCell_LAC.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.SCell_LAC;
                }
                return "";
            };
            olvSCell_ECI.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.SCell_ECI;
                }
                return "";
            };
            olvBackEspri.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.Back_SCell_EARFCN;
                }
                return "";
            };
            olvbACKpci.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.Back_SCell_PCI;
                }
                return "";
            };
            olvSCell_Distance.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.SCell_Distance;
                }
                return "";
            };
            olvSCell_RSRP.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.SCell_RSRP;
                }
                return "";
            };
            olvSCell_SINR.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.SCell_SINR;
                }
                return "";
            };
            olvSCell_RSRQ.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.SCell_RSRQ;
                }
                return "";
            };
            olvSCell_RSSI.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.SCell_RSSI;
                }
                return "";
            };
        }

        private void setGsmData()
        {
            olvGSM_Name.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.GSM_Name;
                }
                return "";
            };
            olvGSM_LAC.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.GSM_LAC;
                }
                return "";
            };
            olvGSM_CI.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.GSM_CI;
                }
                return "";
            };
            olvGSM_Distance.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.GSM_Distance;
                }
                return "";
            };
            olvGSMAvgQual.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.GSMRxLev;
                }
                return "";
            };
            olvGSMAvgRxLev.AspectGetter = delegate (object row)
            {
                if (row is FrItemInfo)
                {
                    FrItemInfo item = row as FrItemInfo;
                    return item.GSMQual;
                }
                return "";
            };
        }

        public void FillData(List<FRFreConVerifyInfo> listFRFreConVerifyInfo)
        {
            ListViewFRVerify.ClearObjects();
            ListViewFRVerify.SetObjects(listFRFreConVerifyInfo);
            if (listFRFreConVerifyInfo.Count == 1)
            {
                ListViewFRVerify.ExpandAll();
            }
            MainModel.ClearDTData();

            //foreach (FRFreConVerifyInfo volInfo in listFRFreConVerifyInfo)
            //{
            //    foreach (FrItemInfo pairInfo in volInfo.ListFrItemsInfo)
            //    {
            //        fillDTDataManager(pairInfo);
            //    }
            //}
            MainModel.FireSetDefaultMapSerialTheme("LTE_TDD:RSRP");
            MainModel.FireDTDataChanged(this);
        }

        private void tlvCellsInfo_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (ListViewFRVerify.SelectedObject is FrItemInfo)
            {
                MainModel.ClearDTData();
                //FrItemInfo vol = ListViewFRVerify.SelectedObject as FrItemInfo;
                //fillDTDataManager(vol);
                MainModel.FireSetDefaultMapSerialTheme("LTE_TDD:RSRP");
                MainModel.FireDTDataChanged(this);
            }
        }
       
        private void miExpandAll_Click(object sender, EventArgs e)
        {
            ListViewFRVerify.ExpandAll();
        }

        private void miCloseAll_Click(object sender, EventArgs e)
        {
            ListViewFRVerify.CollapseAll();
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            ListViewFRVerify.ExpandAll();
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(ListViewFRVerify);
        }
    }
}
