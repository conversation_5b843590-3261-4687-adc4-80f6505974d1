﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Model.RoadProtection
{
    public class DIYQueryGSMDataByNum : DIYSQLBase
    {
        readonly List<int> tdIDList = new List<int>();
        public List<int> TdIDList
        {
            get { return tdIDList; }
        }

        readonly List<RoadWarningEntity> _道路预警集合 = new List<RoadWarningEntity>();
        public List<RoadWarningEntity> 道路预警集合
        {
            get { return _道路预警集合; }
        }

        readonly Dictionary<int, TDData> tdDic = new Dictionary<int, TDData>();
        public Dictionary<int, TDData> TdDic
        {
            get { return tdDic; }
        }

        private int _istatnum { get; set; }
        readonly private string _dbname;
        public DIYQueryGSMDataByNum(MainModel mainModel, int istatnum, DBSetting db)
            : base(mainModel)
        {
            _istatnum = istatnum;
            _dbname = db.Dbname;
        }
        public DIYQueryGSMDataByNum(MainModel mainModel, int istatnum)
            : base(mainModel)
        {
            _istatnum = istatnum;
            _dbname = null;
        }
        protected override string getSqlTextString()
        {
            if (_dbname == null)
            {
                return "SELECT istatnum, level AS 严重程度, weightvalue AS 权值, fDate AS 首次发生时间,lDate AS 最后发生时间, abnormal_event_count AS 事件总数,abnormal_days AS 问题天数, strgridinfo AS 所属网格, strroadinfo AS 位置信息,strname AS 事件类型, dtime AS 发生时间, strcellname AS 占用小区, ilac AS LAC,ici AS CI, flong AS 经度, flat AS 纬度, pretype_desc AS 问题类型,reason_desc AS 原因描述, solution_desc AS 建议方案 FROM v_autotest_relana_gsmevent  ORDER BY 权值 DESC, istatnum, 所属网格 DESC";
            }
            else
            {
                return "SELECT istatnum, level AS 严重程度, weightvalue AS 权值, fDate AS 首次发生时间,lDate AS 最后发生时间, abnormal_event_count AS 事件总数,abnormal_days AS 问题天数, strgridinfo AS 所属网格, strroadinfo AS 位置信息,strname AS 事件类型, dtime AS 发生时间, strcellname AS 占用小区, ilac AS LAC,ici AS CI, flong AS 经度, flat AS 纬度, pretype_desc AS 问题类型,reason_desc AS 原因描述, solution_desc AS 建议方案 FROM " + _dbname + ".dbo.v_autotest_relana_gsmevent  ORDER BY 权值 DESC, istatnum, 所属网格 DESC";
            }

        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[19];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_Float;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Int;
            rType[7] = E_VType.E_String;
            rType[8] = E_VType.E_String;
            rType[9] = E_VType.E_String;
            rType[10] = E_VType.E_String;
            rType[11] = E_VType.E_String;
            rType[12] = E_VType.E_Int;
            rType[13] = E_VType.E_Int;
            rType[14] = E_VType.E_Float;
            rType[15] = E_VType.E_Float;
            rType[16] = E_VType.E_String;
            rType[17] = E_VType.E_String;
            rType[18] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            _道路预警集合.Clear();
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    RoadWarningEntity _道路预警 = new RoadWarningEntity();
                    _道路预警.Istatnum = package.Content.GetParamInt().ToString();
                    _道路预警.严重程度 = package.Content.GetParamString();
                    _道路预警.权值 = package.Content.GetParamFloat().ToString();
                    _道路预警.首次发生时间 = DateTime.Parse(package.Content.GetParamString());
                    _道路预警.最后发生时间 = DateTime.Parse(package.Content.GetParamString());
                    _道路预警.事件总数 = package.Content.GetParamInt().ToString();
                    _道路预警.问题天数 = package.Content.GetParamInt().ToString();
                    _道路预警.所属网格 = package.Content.GetParamString();
                    _道路预警.位置信息 = package.Content.GetParamString();
                    _道路预警.事件类型 = package.Content.GetParamString();
                    _道路预警.发生时间 = DateTime.Parse(package.Content.GetParamString());
                    _道路预警.占用小区 = package.Content.GetParamString();
                    _道路预警.LAC = package.Content.GetParamInt().ToString();
                    _道路预警.CI = package.Content.GetParamInt().ToString();
                    _道路预警.经度 = double.Parse(package.Content.GetParamFloat().ToString());
                    _道路预警.纬度 = double.Parse(package.Content.GetParamFloat().ToString());
                    _道路预警.问题类型 = package.Content.GetParamString();
                    _道路预警.原因描述 = package.Content.GetParamString();
                    _道路预警.建议方案 = package.Content.GetParamString();
                    _道路预警集合.Add(_道路预警);

                    //do your code here
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        public override string Name
        {
            get { return "DIYQueryTDData"; }
        }
    }
}
