﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Collections;
using MasterCom.RAMS.Func;
using MasterCom.Util.UiEx;
using MasterCom.ES.ColorManager;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WeakCoverEventForm : MinCloseForm
    {
        MapForm mapForm;
        ToolStripNumericUpDown edtWeakCoverRxLev = new ToolStripNumericUpDown();
        public WeakCoverEventForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            this.mapForm = mainModel.MainForm.GetMapForm();
            init();
            DisposeWhenClose = true;
        }

        public void init()
        {
            edtWeakCoverRxLev.Increment = 1;
            edtWeakCoverRxLev.Minimum = -120;
            edtWeakCoverRxLev.Maximum = -10;
            edtWeakCoverRxLev.Value = -90;
            edtWeakCoverRxLev.ValueChanged += new EventHandler(edtWeakCoverRxLev_ValueChanged);
            toolStrip1.Items.Insert(1, edtWeakCoverRxLev);
            this.olvColumnIndex.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.sn;
            };
            this.olvColumnName.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.e.Name;
            };
            this.olvColumnDateTime.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.e.DateTime;
            };
            this.olvColumnLongitude.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.e.Longitude;
            };
            this.olvColumnLatitude.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.e.Latitude;
            };
            this.olvColumnCellName.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.e.CellNameSrc;
            };
            this.olvColumnCode.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.e.CellCodeSrc;
            };
            this.olvColumnLAC.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.e["LAC"];
            };
            this.olvColumnCI.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.e["CI"];
            };
            this.olvColumnBCCH.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.e.CellBCCHSrc;
            };
            this.olvColumnBSIC.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.e.CellBSICSrc;
            };
            this.olvColumnRxLev.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.rxLev;
            };
            this.olvColumnStreetDesc.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.e.RoadPlaceDesc;
            };
            this.olvColumnFileName.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.e.FileName;
            };

            this.olvColumnPCCPCH_C2I.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.Pccpch_C2i;
            };
            this.olvColumnDPCH_RSCP.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.Dpch_Rscp;
            };
            this.olvColumnDPCH_C2I.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.Dpch_C2i;
            };
            this.olvColumnDPCH_ISCP.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.Dpch_Iscp;
            };
            this.olvColumnBLER.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                return wcEvent.Bler;
            };
            this.olvColumnTxPower.AspectGetter = delegate(object row)
            {
                WeakCoverEvent wcEvent = row as WeakCoverEvent;
                if (wcEvent.TxPower == 255)
                {
                    return null;
                }
                return wcEvent.TxPower;
            };
        }

        List<WeakCoverEvent> showList = new List<WeakCoverEvent>();
        public void FillData(int weakCoverRxlev)
        {
            edtWeakCoverRxLev.Value = weakCoverRxlev;
            refreshData();
            
        }

        private void refreshData()
        {
            showList.Clear();
            MainModel.ClearDTData();
            for (int i = 0; i < MainModel.WeakCoverEvents.Count; i++)
            {
                WeakCoverEvent wcEvent = MainModel.WeakCoverEvents[i];
                if (wcEvent.rxLev <= (int)edtWeakCoverRxLev.Value)
                {
                    wcEvent.sn = showList.Count + 1;
                    showList.Add(wcEvent);
                    MainModel.DTDataManager.Add(wcEvent.e);
                }
            }
            MainModel.FireDTDataChanged(this);
            objectListView.ClearObjects();
            objectListView.SetObjects(showList);
        }

        private void edtWeakCoverRxLev_ValueChanged(object sender, EventArgs e)
        {
            refreshData();
        }

        private void objectListView_DoubleClick(object sender, EventArgs e)
        {
            if (objectListView.SelectedObjects.Count <= 0)
            {
                return;
            }
            WeakCoverEvent wcEvent = objectListView.SelectedObject as WeakCoverEvent;
            foreach (Event eve in MainModel.SelectedEvents)
            {
                eve.Selected = false;
            }
            MainModel.SelectedEvents.Clear();
            MainModel.SelectedEvents.Add(wcEvent.e);
            wcEvent.e.Selected = true;
            mapForm.GoToView(wcEvent.e.Longitude, wcEvent.e.Latitude);
        }

        private void miReplayEvent_Click(object sender, EventArgs e)
        {
            if (objectListView.SelectedObject is WeakCoverEvent)
            {
                WeakCoverEvent wcEvent = objectListView.SelectedObject as WeakCoverEvent;
                MasterCom.RAMS.Model.Interface.FileReplayer.ReplayOnePart(wcEvent.e);
            }
        }

        private void miExportToExcel_Click(object sender, EventArgs e)
        {
            exportToExcel();
        }

        private void exportToExcel()
        {
            List<List<object>> datas = new List<List<object>>();
            List<object> rowTitle = new List<object>();
            rowTitle.Add("名称");
            rowTitle.Add("时间");
            rowTitle.Add("文件");
            rowTitle.Add("小区名");
            rowTitle.Add("Code");
            rowTitle.Add("LAC");
            rowTitle.Add("CI");
            rowTitle.Add("RAC");
            rowTitle.Add("目标小区名");
            rowTitle.Add("目标小区Code");
            rowTitle.Add("目标小区LAC");
            rowTitle.Add("目标小区CI");
            rowTitle.Add("目标小区RAC");
            rowTitle.Add("经度");
            rowTitle.Add("纬度");
            rowTitle.Add("Info");
            rowTitle.Add("StartTime");
            rowTitle.Add("道路名称");
            rowTitle.Add("区域名称");
            rowTitle.Add("Value1");
            rowTitle.Add("Value2");
            rowTitle.Add("Value3");
            rowTitle.Add("Value4");
            rowTitle.Add("Value5");
            rowTitle.Add("Value6");
            rowTitle.Add("Value7");
            rowTitle.Add("Value8");
            rowTitle.Add("Value9");
            rowTitle.Add("Value10");
            rowTitle.Add("场强");
            rowTitle.Add("掉话前PCCPCH_C/I");
            rowTitle.Add("掉话前DPCH_RSCP");
            rowTitle.Add("掉话前DPCH_C/I");
            rowTitle.Add("掉话前DPCH_ISCP");
            rowTitle.Add("掉话前BLER");
            rowTitle.Add("TxPower");
            
            datas.Add(rowTitle);

            foreach (WeakCoverEvent wcEvent in showList)
            {
                Event e = wcEvent.e;
                List<object> row = new List<object>();
                row.Add(e.Name);
                row.Add(e.DateTimeStringWithMillisecond);
                row.Add(e.FileName == null ? "" : e.FileName);
                row.Add(e.CellNameSrc);
                row.Add(e.CellCodeSrc);
                row.Add(e["LAC"]);
                row.Add(e["CI"]);
                row.Add(e["RAC"]);
                row.Add(e.CellNameTarget);
                row.Add(e.CellCodeTarget);
                row.Add(e["TargetLAC"]);
                row.Add(e["TargetCI"]);
                row.Add(e["TargetRAC"]);
                row.Add(e.Longitude);
                row.Add(e.Latitude);
                row.Add(e.Info);
                row.Add(e.StartTime);
                row.Add(e.RoadPlaceDesc);
                row.Add(e.AreaPlaceDesc);
                row.Add(e["Value1"]);
                row.Add(e["Value2"]);
                row.Add(e["Value3"]);
                row.Add(e["Value4"]);
                row.Add(e["Value5"]);
                row.Add(e["Value6"]);
                row.Add(e["Value7"]);
                row.Add(e["Value8"]);
                row.Add(e["Value9"]);
                row.Add(e["Value10"]);
                row.Add(wcEvent.rxLev);
                row.Add(wcEvent.Pccpch_C2i);
                row.Add(wcEvent.Dpch_Rscp);
                row.Add(wcEvent.Dpch_C2i);
                row.Add(wcEvent.Dpch_Iscp);
                row.Add(wcEvent.Bler);
                row.Add(wcEvent.TxPower);

                datas.Add(row);
            }
            ExcelNPOIManager.ExportToExcel(datas);
        }
    }
}
