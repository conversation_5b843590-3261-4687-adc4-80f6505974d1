﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using System.Windows.Forms;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLowSpeedByFile_LTE : ZTLowSpeedByFile_TD
    {
        
        public ZTLowSpeedByFile_LTE(MainModel mainModel)
            : base(mainModel)
        {
            this.cellType = new LTECell();
        }

        public override string Name
        {
            get { return "LTE按文件按小区每分钟下载速率统计分析"; }
        }


        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22037, this.Name);
        }

        protected override void analyseFiles()
        {
            try
            {
                List<FileInfo> files = new List<FileInfo>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (fileInfo.ServiceType != (int)ServiceType.LTE_TDD_DATA)
                    {
                        continue;
                    }
                    files.Add(fileInfo);
                }
                int iloop = 0;
                foreach (FileInfo fileInfo in files)
                {
                    WaitBox.Text = "正在分析文件(" + (++iloop) + "/" + files.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / files.Count);
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    replay();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        protected override void doStat()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    for (int i = 0; i < testPointList.Count; i++)
                    {
                        TestPoint testPoint = testPointList[i];
                        addValidTPInfo(testPoint);
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void addValidTPInfo(TestPoint testPoint)
        {
            if (isValidTestPoint(testPoint) && GetAppSpeed(testPoint) >= 0 && GetAppSpeed(testPoint) != null)
            {
                LTECell lteCell = testPoint.GetMainLTECell_TdOrFdd();
                if (lteCell != null)
                {
                    StringBuilder sb = new StringBuilder();
                    sb.Append(testPoint.FileID);
                    sb.Append(lteCell.TAC);
                    sb.Append(lteCell.ECI);
                    sb.Append(testPoint.DateTime.ToString("yy-MM-dd HH:mm"));
                    if (sampleSpeedInfo_LTE.ContainsKey(sb.ToString()))
                    {
                        sampleSpeedInfo_LTE[sb.ToString()].setData(testPoint, lteCell, testPoint.DateTime.ToString("yy-MM-dd HH:mm"));
                    }
                    else
                    {
                        sampleSpeedInfo_LTE[sb.ToString()] = new SampleSpeedInfo_LTE(testPoint, lteCell, testPoint.DateTime.ToString("yy-MM-dd HH:mm"));
                    }
                }
            }
        }

        protected int? GetAppSpeed(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (int?)tp["lte_fdd_APP_ThroughputDL"];
            }
            return (int?)tp["lte_APP_ThroughputDL"];
        }

        protected override void fireShowForm()
        {
            List<SampleSpeedInfo_LTE> sampleList = new List<SampleSpeedInfo_LTE>();
            foreach (SampleSpeedInfo_LTE sample in sampleSpeedInfo_LTE.Values)
            {
                if (sample.TransferedTime >= transferedTimeLimit)
                {
                    sample.calInfo();
                    sampleList.Add(sample);
                }
            }
            if (lowSpeedInfoByFileForm_LTE == null || lowSpeedInfoByFileForm_LTE.IsDisposed)
            {
                lowSpeedInfoByFileForm_LTE = new LowSpeedInfoByFileForm_LTE(MainModel);
            }
            lowSpeedInfoByFileForm_LTE.FillData(sampleList);
            if (!lowSpeedInfoByFileForm_LTE.Visible)
            {
                lowSpeedInfoByFileForm_LTE.Show(MainModel.MainForm);
            }
        }
    }

    public class ZTLowSpeedByFile_LTE_FDD : ZTLowSpeedByFile_LTE
    {
        public ZTLowSpeedByFile_LTE_FDD(MainModel mainModel)
            : base(mainModel)
        {
            this.cellType = new LTECell();
            this.carrierID = CarrierType.ChinaUnicom;
        }

        protected override void analyseFiles()
        {
            try
            {
                List<FileInfo> files = new List<FileInfo>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (fileInfo.ServiceType != (int)ServiceType.LTE_FDD_DATA)
                    {
                        continue;
                    }
                    files.Add(fileInfo);
                }
                int iloop = 0;
                foreach (FileInfo fileInfo in files)
                {
                    WaitBox.Text = "正在分析文件(" + (++iloop) + "/" + files.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / files.Count);
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    replay();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }
        public override string Name
        {
            get { return "LTE_FDD按文件按小区每分钟下载速率统计分析"; }
        }


        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26057, this.Name);
        }
    }

    public class SampleSpeedInfo_LTE : SampleSpeedInfo
    {
        private double transmission_mode = 0;
        public double Transmission_Mode
        {
            get
            {
                return transmission_mode;
            }
        }

        private double rank_indicator = 0;
        public double Rank_Indicator
        {
            get { return rank_indicator; }
        }

        private double pdsch_rb_number = 0;
        public double PDSCH_RB_Number
        {
            get { return pdsch_rb_number; }
        }

        private double pdsch_bler = 0;
        public double PDSCH_BLER
        {
            get { return pdsch_bler; }
        }

        private double pdcch_dl_grant_count = 0;
        public double PDCCH_DL_Grant_Count
        {
            get { return pdcch_dl_grant_count; }
        }

        private double ratio_dl_code0_harq_ack = 0;
        public double Ratio_DL_Code0_HARQ_ACK
        {
            get
            {
                return ratio_dl_code0_harq_ack;
            }
        }

        private double ratio_dl_code0_harq_nack = 0;
        public double Ratio_DL_Code0_HARQ_NACK
        {
            get
            {
                return ratio_dl_code0_harq_nack;
            }
        }

        private double ratio_dl_code1_harq_ack = 0;
        public double Ratio_DL_Code1_HARQ_ACK
        {
            get
            {
                return ratio_dl_code1_harq_ack;
            }
        }

        private double ratio_dl_code1_harq_nack = 0;
        public double Ratio_DL_Code1_HARQ_NACK
        {
            get
            {
                return ratio_dl_code1_harq_nack;
            }
        }

        public SampleSpeedInfo_LTE()
            : base()
        {

        }
        public SampleSpeedInfo_LTE(TestPoint tp, LTECell cell, String dateTime)
        {
            setData(tp, cell, dateTime);
        }

        public void setData(TestPoint tp, LTECell cell, String dateTime)
        {
            this.FileName = tp.FileName;
            this.DateTime = dateTime;
            this.LTECell = cell;
            this.CellName = LTECell.Name;
            this.LAC = LTECell.TAC;
            this.CI = LTECell.ECI;
            this.CellID = LTECell.SCellID;
            if (tp is LTEFddTestPoint)
            {
                if (tp["lte_fdd_APP_TransferedSize"] != null)
                {
                    this.TransferedSize += (int)tp["lte_fdd_APP_TransferedSize"];
                }
                if (tp["lte_fdd_APP_TransferedTime"] != null)
                {
                    this.TransferedTime += (int)tp["lte_fdd_APP_TransferedTime"];
                }

                short? transMode = (short?)tp["lte_fdd_Transmission_Mode"];
                this.transmission_mode += getValidData(transMode);

                short? lte_fdd_Rank_Indicator = (short?)tp["lte_fdd_Rank_Indicator"];
                rank_indicator += getValidData(lte_fdd_Rank_Indicator);

                int? pdsch_rb_num = (int?)tp["lte_fdd_PDSCH_RB_Number"];
                this.pdsch_rb_number += getValidData(pdsch_rb_num);

                float? lte_fdd_PDSCH_BLER = (float?)tp["lte_fdd_PDSCH_BLER"];
                pdsch_bler += getValidData(lte_fdd_PDSCH_BLER);

                short? lte_fdd_PDCCH_DL_Grant_Count = (short?)tp["lte_fdd_PDCCH_DL_Grant_Count"];
                pdcch_dl_grant_count += getValidData(lte_fdd_PDCCH_DL_Grant_Count);

                float? lte_fdd_Ratio_DL_Code0_HARQ_ACK = (float?)tp["lte_fdd_Ratio_DL_Code0_HARQ_ACK"];
                ratio_dl_code0_harq_ack += getValidData(lte_fdd_Ratio_DL_Code0_HARQ_ACK);

                float? lte_fdd_Ratio_DL_Code0_HARQ_NACK = (float?)tp["lte_fdd_Ratio_DL_Code0_HARQ_NACK"];
                ratio_dl_code0_harq_nack += getValidData(lte_fdd_Ratio_DL_Code0_HARQ_NACK);

                float? lte_fdd_Ratio_DL_Code1_HARQ_ACK = (float?)tp["lte_fdd_Ratio_DL_Code1_HARQ_ACK"];
                ratio_dl_code1_harq_ack += getValidData(lte_fdd_Ratio_DL_Code1_HARQ_ACK);

                float? lte_Ratio_DL_Code1_HARQ_NACK = (float?)tp["lte_fdd_Ratio_DL_Code1_HARQ_NACK"];
                ratio_dl_code1_harq_nack += getValidData(lte_Ratio_DL_Code1_HARQ_NACK);
            }
            else
            {
                if (tp["lte_APP_TransferedSize"] != null)
                {
                    this.TransferedSize += (int)tp["lte_APP_TransferedSize"];
                }
                if (tp["lte_APP_TransferedTime"] != null)
                {
                    this.TransferedTime += (int)tp["lte_APP_TransferedTime"];
                }

                short? transMode = (short?)tp["lte_Transmission_Mode"];
                transmission_mode += getValidData(transMode);

                short? lte_Rank_Indicator = (short?)tp["lte_Rank_Indicator"];
                rank_indicator += getValidData(lte_Rank_Indicator);

                int? pdsch_rb_num = (int?)tp["lte_PDSCH_RB_Number"];
                pdsch_rb_number += getValidData(pdsch_rb_num);

                float? lte_PDSCH_BLER = (float?)tp["lte_PDSCH_BLER"];
                pdsch_bler += getValidData(lte_PDSCH_BLER);

                short? lte_PDCCH_DL_Grant_Count = (short?)tp["lte_PDCCH_DL_Grant_Count"];
                pdcch_dl_grant_count += getValidData(lte_PDCCH_DL_Grant_Count);

                float? lte_Ratio_DL_Code0_HARQ_ACK = (float?)tp["lte_Ratio_DL_Code0_HARQ_ACK"];
                ratio_dl_code0_harq_ack += getValidData(lte_Ratio_DL_Code0_HARQ_ACK);

                float? lte_Ratio_DL_Code0_HARQ_NACK = (float?)tp["lte_Ratio_DL_Code0_HARQ_NACK"];
                ratio_dl_code0_harq_nack += getValidData(lte_Ratio_DL_Code0_HARQ_NACK);

                float? lte_Ratio_DL_Code1_HARQ_ACK = (float?)tp["lte_Ratio_DL_Code1_HARQ_ACK"];
                ratio_dl_code1_harq_ack += getValidData(lte_Ratio_DL_Code1_HARQ_ACK);

                float? lte_Ratio_DL_Code1_HARQ_NACK = (float?)tp["lte_Ratio_DL_Code1_HARQ_NACK"];
                ratio_dl_code1_harq_nack += getValidData(lte_Ratio_DL_Code1_HARQ_NACK);
            }

            this.SampleCount++;
        }

        private float getValidData(float? value)
        {
            if (value == null)
            {
                return 0;
            }
            else
            {
                return (float)value;
            }
        }

        private short getValidData(short? value)
        {
            if (value == null)
            {
                return 0;
            }
            else
            {
                return (short)value;
            }
        }

        private int getValidData(int? value)
        {
            if (value == null)
            {
                return 0;
            }
            else
            {
                return (int)value;
            }
        }

        public void calInfo()
        {
            this.transmission_mode = this.transmission_mode / SampleCount;
            this.rank_indicator = this.rank_indicator / SampleCount;
            this.pdsch_rb_number = this.pdsch_rb_number / SampleCount;
            this.pdsch_bler = this.pdsch_bler / SampleCount;
            this.pdcch_dl_grant_count = this.pdcch_dl_grant_count / SampleCount;
            this.ratio_dl_code0_harq_ack = this.ratio_dl_code0_harq_ack / SampleCount;
            this.ratio_dl_code0_harq_nack = this.ratio_dl_code0_harq_nack / SampleCount;
            this.ratio_dl_code1_harq_ack = this.ratio_dl_code1_harq_ack / SampleCount;
            this.ratio_dl_code1_harq_nack = this.ratio_dl_code1_harq_nack / SampleCount;
        }
    }
}
