﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTHandOverAndCellReselDeal
    {
        protected ZTHandOverAndCellReselDeal()
        {

        }

        public static void ProcessFiles(Dictionary<string, List<Event>> orgFileEventDic, ZTHandOverAndCellReselCondition hoAndCrCondition,
                                        List<ZTHandOverAndCellReselFileInfo> fileList, Dictionary<string, ZTHandOverAndCellReselCellInfo> cellDic)
        {
            foreach (string fileName in orgFileEventDic.Keys)
            {
                ZTHandOverAndCellReselFileInfo fileInfo = new ZTHandOverAndCellReselFileInfo(fileName);
                processFile(fileInfo, orgFileEventDic[fileName], hoAndCrCondition, cellDic);

                if (fileInfo.HoInfoList.Count > 0)
                {
                    fileInfo.SN = fileList.Count + 1;
                    fileList.Add(fileInfo);
                }
            }
        }

        internal static void processFile(ZTHandOverAndCellReselFileInfo fileInfo, List<Event> eventList, ZTHandOverAndCellReselCondition hoAndCrCondition, Dictionary<string, ZTHandOverAndCellReselCellInfo> cellDic)
        {
            Event lastEvt = null;

            if (eventList.Count > 0)
            {
                lastEvt = eventList[0];
            }
            else
            {
                return;
            }

            foreach (Event curEvt in eventList)
            {
                if (((int)lastEvt["TargetLAC"] == (int)curEvt["LAC"] && (int)lastEvt["TargetCI"] == (int)curEvt["CI"] &&
                            (int)curEvt["TargetLAC"] == (int)lastEvt["LAC"] && (int)curEvt["TargetCI"] == (int)lastEvt["CI"]))  //出现乒乓切换
                {
                    addValidHOInfo(fileInfo, hoAndCrCondition, cellDic, lastEvt, curEvt);
                }

                lastEvt = curEvt;
            }
        }

        private static void addValidHOInfo(ZTHandOverAndCellReselFileInfo fileInfo, ZTHandOverAndCellReselCondition hoAndCrCondition, Dictionary<string, ZTHandOverAndCellReselCellInfo> cellDic, Event lastEvt, Event curEvt)
        {
            TimeSpan timeSpan = curEvt.DateTime - lastEvt.DateTime;
            if (timeSpan.TotalSeconds <= hoAndCrCondition.TimeLimit)     //时间符合
            {
                bool isSpeedValid = false;
                if (hoAndCrCondition.IsLimitSpeed)
                {
                    double curSpeed = MathFuncs.GetDistance(curEvt.Longitude, curEvt.Latitude, lastEvt.Longitude, lastEvt.Latitude) / Math.Abs(curEvt.Time - lastEvt.Time) * 3.6;
                    if ((curSpeed >= hoAndCrCondition.SpeedLimitMin) && (curSpeed <= hoAndCrCondition.SpeedLimitMax))   //车速符合
                    {
                        isSpeedValid = true;
                    }
                }
                else
                {
                    isSpeedValid = true;    //不考虑车速
                }

                if (isSpeedValid)    //符合乒乓切换
                {
                    addHOInfoToFile(fileInfo, lastEvt, curEvt, cellDic);
                }
            }
        }

        internal static void addHOInfoToFile(ZTHandOverAndCellReselFileInfo fileInfo, Event lastEvt, Event curEvt, Dictionary<string, ZTHandOverAndCellReselCellInfo> cellDic)
        {

            //增加小区信息
            string strOrgCell = string.Empty;
            string strDestCell = string.Empty;

            if (EventInfoManager.GetInstance()[lastEvt.ID].Name.StartsWith("W"))
            {
                addWCellInfoToFile(ref fileInfo, ref lastEvt, ref strOrgCell, ref strDestCell, ref cellDic);
            }
            else if (EventInfoManager.GetInstance()[lastEvt.ID].Name.StartsWith("TD"))
            {
                addTDCellInfoToFile(ref fileInfo, ref lastEvt, ref strOrgCell, ref strDestCell, ref cellDic);
            }
            else
            {
                addGSMCellInfoToFile(ref fileInfo, ref lastEvt, ref strOrgCell, ref strDestCell, ref cellDic);
            }

            //增加事件信息
            ZTHandOverAndCellReselHOInfo handoverInfo = new ZTHandOverAndCellReselHOInfo();
            handoverInfo.SN = fileInfo.HoInfoList.Count + 1;
            fileInfo.HoInfoList.Add(handoverInfo);

            StringBuilder sb = new StringBuilder();
            sb.Append(strOrgCell);
            sb.Append("->");
            sb.Append(strDestCell);
            ZTHandOverAndCellReselEventInfo eventInfo = new ZTHandOverAndCellReselEventInfo(sb.ToString(), lastEvt);
            handoverInfo.EventList.Add(eventInfo);   //A --> B 事件

            sb = new StringBuilder();
            sb.Append(strDestCell);
            sb.Append("->");
            sb.Append(strOrgCell);
            eventInfo = new ZTHandOverAndCellReselEventInfo(sb.ToString(), curEvt);
            handoverInfo.EventList.Add(eventInfo);    //B --> A 事件

            //事件信息中，增加小区切换描述
            StringBuilder sbCellSeq = new StringBuilder();  //A->B->A
            sbCellSeq.Append(strOrgCell);
            sbCellSeq.Append("->");
            sbCellSeq.Append(strDestCell);
            sbCellSeq.Append("->");
            sbCellSeq.Append(strOrgCell);
            handoverInfo.SeqCells = sbCellSeq.ToString();
        }

        internal static void addGSMCellInfoToFile(ref ZTHandOverAndCellReselFileInfo fileInfo, ref Event lastEvt, ref string strOrgCell, ref string strDestCell, ref  Dictionary<string, ZTHandOverAndCellReselCellInfo> cellDic)
        {
            Cell orgCell = MainModel.GetInstance().CellManager.GetCell(lastEvt.DateTime, (ushort)(int)lastEvt["LAC"], (ushort)(int)lastEvt["CI"]);
            strOrgCell = ZTHandOverAndCellReselCellInfo.AddGsmCell(fileInfo.CellDic, orgCell, (int)lastEvt["LAC"], (int)lastEvt["CI"]);
            ZTHandOverAndCellReselCellInfo.AddGsmCell(cellDic, orgCell, (int)lastEvt["LAC"], (int)lastEvt["CI"]);

            Cell destCell = MainModel.GetInstance().CellManager.GetCell(lastEvt.DateTime, (ushort)(int)lastEvt["TargetLAC"], (ushort)(int)lastEvt["TargetCI"]);
            strDestCell = ZTHandOverAndCellReselCellInfo.AddGsmCell(fileInfo.CellDic, destCell, (int)lastEvt["TargetLAC"], (int)lastEvt["TargetCI"]);
            ZTHandOverAndCellReselCellInfo.AddGsmCell(cellDic, destCell, (int)lastEvt["TargetLAC"], (int)lastEvt["TargetCI"]);
        }

        internal static void addTDCellInfoToFile(ref ZTHandOverAndCellReselFileInfo fileInfo, ref Event lastEvt, ref string strOrgCell, ref string strDestCell, ref  Dictionary<string, ZTHandOverAndCellReselCellInfo> cellDic)
        {
            if (lastEvt.ID == 145 || lastEvt.ID == 148 || lastEvt.ID == 232)   //TD_HandoverSuccess_IntraT,TD_HandoverSuccess_Baton,TD_HandoverSuccess_RBReconfigT
            {
                TDCell orgCell = MainModel.GetInstance().CellManager.GetTDCell(lastEvt.DateTime, (int)lastEvt["LAC"], (int)lastEvt["CI"]);
                strOrgCell = ZTHandOverAndCellReselCellInfo.AddTdCell(fileInfo.CellDic, orgCell, (int)lastEvt["LAC"], (int)lastEvt["CI"]);
                ZTHandOverAndCellReselCellInfo.AddTdCell(cellDic, orgCell, (int)lastEvt["LAC"], (int)lastEvt["CI"]);    //添加到小区总表中

                TDCell destCell = MainModel.GetInstance().CellManager.GetTDCell(lastEvt.DateTime, (int)lastEvt["TargetLAC"], (int)lastEvt["TargetCI"]);
                strDestCell = ZTHandOverAndCellReselCellInfo.AddTdCell(fileInfo.CellDic, destCell, (int)lastEvt["TargetLAC"], (int)lastEvt["TargetCI"]);
                ZTHandOverAndCellReselCellInfo.AddTdCell(cellDic, destCell, (int)lastEvt["TargetLAC"], (int)lastEvt["TargetCI"]);    //添加到小区总表中
            }
            else if (lastEvt.ID == 151)  //TD_HandoverSuccess_IntraG
            {
                Cell orgCell = MainModel.GetInstance().CellManager.GetCell(lastEvt.DateTime, (ushort)(int)lastEvt["LAC"], (ushort)(int)lastEvt["CI"]);
                strOrgCell = ZTHandOverAndCellReselCellInfo.AddGsmCell(fileInfo.CellDic, orgCell, (int)lastEvt["LAC"], (int)lastEvt["CI"]);
                ZTHandOverAndCellReselCellInfo.AddGsmCell(cellDic, orgCell, (int)lastEvt["LAC"], (int)lastEvt["CI"]);

                Cell destCell = MainModel.GetInstance().CellManager.GetCell(lastEvt.DateTime, (ushort)(int)lastEvt["TargetLAC"], (ushort)(int)lastEvt["TargetCI"]);
                strDestCell = ZTHandOverAndCellReselCellInfo.AddGsmCell(fileInfo.CellDic, destCell, (int)lastEvt["TargetLAC"], (int)lastEvt["TargetCI"]);
                ZTHandOverAndCellReselCellInfo.AddGsmCell(cellDic, destCell, (int)lastEvt["TargetLAC"], (int)lastEvt["TargetCI"]);
            }
            else if (lastEvt.ID == 142) //TD_HandoverSuccess_T2G
            {
                TDCell orgCell = MainModel.GetInstance().CellManager.GetTDCell(lastEvt.DateTime, (int)lastEvt["LAC"], (int)lastEvt["CI"]);
                strOrgCell = ZTHandOverAndCellReselCellInfo.AddTdCell(fileInfo.CellDic, orgCell, (int)lastEvt["LAC"], (int)lastEvt["CI"]);
                ZTHandOverAndCellReselCellInfo.AddTdCell(cellDic, orgCell, (int)lastEvt["LAC"], (int)lastEvt["CI"]);    //添加到小区总表中

                Cell destCell = MainModel.GetInstance().CellManager.GetCell(lastEvt.DateTime, (ushort)(int)lastEvt["TargetLAC"], (ushort)(int)lastEvt["TargetCI"]);
                strDestCell = ZTHandOverAndCellReselCellInfo.AddGsmCell(fileInfo.CellDic, destCell, (int)lastEvt["TargetLAC"], (int)lastEvt["TargetCI"]);
                ZTHandOverAndCellReselCellInfo.AddGsmCell(cellDic, destCell, (int)lastEvt["TargetLAC"], (int)lastEvt["TargetCI"]);
            }
        }

        internal static void addWCellInfoToFile(ref ZTHandOverAndCellReselFileInfo fileInfo, ref Event lastEvt, ref string strOrgCell, ref string strDestCell, ref  Dictionary<string, ZTHandOverAndCellReselCellInfo> cellDic)
        {
            if (lastEvt.ID == 545 || lastEvt.ID == 548 || lastEvt.ID == 611)   //WCDMA_HandoverSuccess_IntraW,WCDMA_HandoverSuccess_Soft,WCDMA_GPRS_HandoverSuccess
            {
                WCell orgCell = MainModel.GetInstance().CellManager.GetWCell(lastEvt.DateTime, (int)lastEvt["LAC"], (int)lastEvt["CI"]);
                strOrgCell = ZTHandOverAndCellReselCellInfo.AddWCell(fileInfo.CellDic, orgCell, (int)lastEvt["LAC"], (int)lastEvt["CI"]);
                ZTHandOverAndCellReselCellInfo.AddWCell(cellDic, orgCell, (int)lastEvt["LAC"], (int)lastEvt["CI"]);    //添加到小区总表中

                WCell destCell = MainModel.GetInstance().CellManager.GetWCell(lastEvt.DateTime, (int)lastEvt["TargetLAC"], (int)lastEvt["TargetCI"]);
                strDestCell = ZTHandOverAndCellReselCellInfo.AddWCell(fileInfo.CellDic, destCell, (int)lastEvt["TargetLAC"], (int)lastEvt["TargetCI"]);
                ZTHandOverAndCellReselCellInfo.AddWCell(cellDic, destCell, (int)lastEvt["TargetLAC"], (int)lastEvt["TargetCI"]);    //添加到小区总表中
            }
            else if (lastEvt.ID == 551)  //WCDMA_HandoverSuccess_IntraG
            {
                Cell orgCell = MainModel.GetInstance().CellManager.GetCell(lastEvt.DateTime, (ushort)(int)lastEvt["LAC"], (ushort)(int)lastEvt["CI"]);
                strOrgCell = ZTHandOverAndCellReselCellInfo.AddGsmCell(fileInfo.CellDic, orgCell, (int)lastEvt["LAC"], (int)lastEvt["CI"]);
                ZTHandOverAndCellReselCellInfo.AddGsmCell(cellDic, orgCell, (int)lastEvt["LAC"], (int)lastEvt["CI"]);

                Cell destCell = MainModel.GetInstance().CellManager.GetCell(lastEvt.DateTime, (ushort)(int)lastEvt["TargetLAC"], (ushort)(int)lastEvt["TargetCI"]);
                strDestCell = ZTHandOverAndCellReselCellInfo.AddGsmCell(fileInfo.CellDic, destCell, (int)lastEvt["TargetLAC"], (int)lastEvt["TargetCI"]);
                ZTHandOverAndCellReselCellInfo.AddGsmCell(cellDic, destCell, (int)lastEvt["TargetLAC"], (int)lastEvt["TargetCI"]);

            }
            else if (lastEvt.ID == 542) //WCDMA_HandoverSuccess_W2G
            {
                WCell orgCell = MainModel.GetInstance().CellManager.GetWCell(lastEvt.DateTime, (int)lastEvt["LAC"], (int)lastEvt["CI"]);
                strOrgCell = ZTHandOverAndCellReselCellInfo.AddWCell(fileInfo.CellDic, orgCell, (int)lastEvt["LAC"], (int)lastEvt["CI"]);
                ZTHandOverAndCellReselCellInfo.AddWCell(cellDic, orgCell, (int)lastEvt["LAC"], (int)lastEvt["CI"]);    //添加到小区总表中

                Cell destCell = MainModel.GetInstance().CellManager.GetCell(lastEvt.DateTime, (ushort)(int)lastEvt["TargetLAC"], (ushort)(int)lastEvt["TargetCI"]);
                strDestCell = ZTHandOverAndCellReselCellInfo.AddGsmCell(fileInfo.CellDic, destCell, (int)lastEvt["TargetLAC"], (int)lastEvt["TargetCI"]);
                ZTHandOverAndCellReselCellInfo.AddGsmCell(cellDic, destCell, (int)lastEvt["TargetLAC"], (int)lastEvt["TargetCI"]);
            }
        }
    }
}
