﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.VoLTEBlockCallCause
{
    public abstract class BlockCallCauseBase
    {
        public abstract bool IsSatisfy(CallInfo call);
        public abstract bool ShowCaption { get; set; }
    }

    public class VoiceHangupCause : BlockCallCauseBase
    {
        public override bool IsSatisfy(CallInfo call)
        {//addMessage(child, new MessageInfo(0x7FFF1C43, "Voice_Hangup"));
            for (int i = call.Messages.Count - 1; i >= 0; i--)
            {
                Message msg = call.Messages[i];
                if (msg.ID == 0x7FFF1C43)
                {
                    call.BlockCause = BlockCallCause.VoiceHangup;
                    return true;
                }
            }
            return false;
        }

        public override bool ShowCaption
        {
            get
            {
                throw new NotImplementedException();
            }
            set
            {
                throw new NotImplementedException();
            }
        }
    }

    public class IMSErrorCause : BlockCallCauseBase
    {
        public override bool IsSatisfy(CallInfo call)
        {
            for (int i = call.Messages.Count - 1; i >= 0; i--)
            {
                Message msg = call.Messages[i];
                int id;
                if (MessageInfoManager.TryParseToSipReqMsgID(msg.ID, out id))
                {
                    int statusCode = (msg.ID & 0x00003fff);
                    if (400 <= statusCode)
                    {
                        call.ErrorMsg = msg;
                        call.BlockCause = BlockCallCause.异常信令;
                        return true;
                    }
                }
            }
            return false;
        }

        public override bool ShowCaption
        {
            get
            {
                throw new NotImplementedException();
            }
            set
            {
                throw new NotImplementedException();
            }
        }
    }

    public class UECancelCause : BlockCallCauseBase
    {
        public override bool IsSatisfy(CallInfo call)
        {
            for (int i = call.Messages.Count - 1; i >= 0; i--)
            {
                Message msg = call.Messages[i];
                if (msg.ID==0x42030000)
                {
                    call.BlockCause = BlockCallCause.UE_Cancel;
                    call.ErrorMsg = msg;
                    return true;
                }
            }
            return false;
        }

        public override bool ShowCaption
        {
            get
            {
                throw new NotImplementedException();
            }
            set
            {
                throw new NotImplementedException();
            }
        }
    }
}
