﻿using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NbIotMgrsOverlapCoverageRatioSetting : LteMgrsConditionControlBase
    {
        public NbIotMgrsOverlapCoverageRatioSetting()
        {
            InitializeComponent();
            initValues();
        }

        public override string Title
        {
            get { return "连续重叠覆盖"; }
        }

        public override object GetCondition(out string invalidReason)
        {
            invalidReason = null;
            return new object[] {
                (int)numGridCount.Value,
                (int)numCoverage.Value,
                (double)numMinRSRP.Value,
                (double)numRSRPDis.Value,
            };
        }

        /// <summary>
        /// 保存条件信息
        /// </summary>
        /// <param name="xcfg"></param>
        public override void SaveCondititon(XmlConfigFile xcfg)
        {
            XmlElement configWeakRsrp = xcfg.AddConfig("OverlapCoverageRatio");
            xcfg.AddItem(configWeakRsrp, "GridCount", (int)numGridCount.Value);
            xcfg.AddItem(configWeakRsrp, "Coverage", (int)numCoverage.Value);
            xcfg.AddItem(configWeakRsrp, "MinRsrp", (double)numMinRSRP.Value);
            xcfg.AddItem(configWeakRsrp, "RsrpDis", (double)numRSRPDis.Value);
        }

        /// <summary>
        /// 从配置文件读取参数值
        /// </summary>
        private void initValues()
        {
            XmlConfigFile configFile = new MyXmlConfigFile(NbIotMgrsBaseSettingManager.Instance.ConfigPath);
            if (configFile.Load())
            {
                XmlElement configCoverage = configFile.GetConfig("OverlapCoverageRatio");
                object obj = configFile.GetItemValue(configCoverage, "GridCount");
                if (obj != null)
                {
                    numGridCount.Value = (int)obj;
                }
                obj = configFile.GetItemValue(configCoverage, "Coverage");
                if (obj != null)
                {
                    numCoverage.Value = (int)obj;
                }
                obj = configFile.GetItemValue(configCoverage, "MinRsrp");
                if (obj != null)
                {
                    numMinRSRP.Value = (decimal)(double)obj;
                }
                obj = configFile.GetItemValue(configCoverage, "RsrpDis");
                if (obj != null)
                {
                    numRSRPDis.Value = (decimal)(double)obj;
                }
            }
        }
    }
}
