﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Util;
using MasterCom.MTGis;

using DevExpress.XtraEditors;
using DevExpress.XtraBars;
using System.Xml;

namespace MasterCom.RAMS.Func
{
    public partial class ScanGridAnaSettingForm : BaseFormStyle
    {
        public ScanGridAnaSettingForm(MainModel model) : base()
        {
            InitializeComponent();
            this.model = model;

            chkUseCmp.CheckedChanged += ChkUseCmp_CheckedChanged;
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            btnOpen.Click += BtnOpen_Click;

            pickerAnaStartTime.Value = DateTime.Now.Date;
            pickerAnaEndTime.Value = DateTime.Now.Date;
            pickerCmpStartTime.Value = pickerAnaStartTime.Value;
            pickerCmpEndTime.Value = pickerAnaEndTime.Value;
            txtShpfile.Enabled = false;
            chkUseCmp.Checked = true;
            chkUseCmp.Checked = false;

            numRelative.Value = 12;
            numRelMin.Value = -100;
            numAbsolute.Value = -80;
            numValid.Value = -95;
            numGridSize.Value = 50;
            numGridCount.Value = 3;
            numHighCoverage.Value = 7;
            numWeakRxlev.Value = -85;

            initValues();

            cbxServiceType.Items.Add("GSM扫频");
            cbxServiceType.Items.Add("TD扫频");
            cbxServiceType.Items.Add("GSM路测");
            cbxServiceType.Items.Add("TD路测");

            btnAnaProjectType.Click += BtnAnaProjectType_Click;
            btnCmpProjectType.Click += BtnCmpProjectType_Click;
            anaChkFileName.CheckedChanged += ChkAnaFileName_CheckedChanged;
            cmpChkFileName.CheckedChanged += ChkCmpFileName_CheckedChanged;
            anaRadioGroup.MouseClick += RadioGroup_MouseClick;
            anaRadioGroup.SelectedIndexChanged += RadioGroup_SelectedChanged;
            cmpRadioGroup.MouseClick += RadioGroup_MouseClick;
            cmpRadioGroup.SelectedIndexChanged += RadioGroup_SelectedChanged;
            cbxServiceType.SelectedIndexChanged += cbxServiceType_SelectedIndexChanged;
            treeViewServiceType.AfterCheck += treeViewServiceType_AfterCheck;
            treeViewServiceTypeCmp.AfterCheck += treeViewServiceType_AfterCheck;
            InitProjectTypes();

            cbxServiceType.SelectedIndex = 0;
        }

        void treeViewServiceType_AfterCheck(object sender, TreeViewEventArgs e)
        {
            if (e.Node.Nodes == null || e.Node.Nodes.Count <= 0)
            {
                return;
            }
            foreach (TreeNode tn in e.Node.Nodes)
            {
                tn.Checked = e.Node.Checked;
            }
        }

        void cbxServiceType_SelectedIndexChanged(object sender, EventArgs e)
        {
            int index = this.cbxServiceType.SelectedIndex;
            this.labelServiceType.Text = this.cbxServiceType.Text;
            if (index == 0)//GSM扫频
            {
                this.initGSMScanServiceType(this.treeViewServiceType);
                this.initGSMScanServiceType(this.treeViewServiceTypeCmp);
            }
            else if (index == 1)//TD扫频 
            {
                this.initTDScanServiceType(this.treeViewServiceType);
                this.initTDScanServiceType(this.treeViewServiceTypeCmp);
            }
            else if (index == 2)//GSM路测
            {
                List<int> listOld = ScanGridAnaSettingCondition.Instance.AnaServiceType_GSM;
                this.initGSMDTServiceType(this.treeViewServiceType, listOld);
                listOld = ScanGridAnaSettingCondition.Instance.CmpServiceType_GSM;
                this.initGSMDTServiceType(this.treeViewServiceTypeCmp, listOld);
            }
            else if (index == 3)//TD路测
            {
                List<int> listOld = ScanGridAnaSettingCondition.Instance.AnaServiceType_TD;
                this.initTDDTServiceType(this.treeViewServiceType, listOld);
                listOld = ScanGridAnaSettingCondition.Instance.CmpServiceType_TD;
                this.initTDDTServiceType(this.treeViewServiceTypeCmp, listOld);
            }
        }
        private void initTDScanServiceType(TreeView tv)
        {
            tv.Nodes.Clear();
            TreeNode tn = new TreeNode("全部");
            tv.Nodes.Add(tn);
            Dictionary<string, int> dic = new Dictionary<string, int>();
            dic["TD_扫频"] = 19;
            foreach (string key in dic.Keys)
            {
                TreeNode t = new TreeNode(key);
                t.Tag = dic[key];
                t.Checked = true;
                tn.Nodes.Add(t);
            }
            tv.ExpandAll();
        }
        private void initGSMScanServiceType(TreeView tv)
        {
            tv.Nodes.Clear();
            TreeNode tn = new TreeNode("全部");
            tv.Nodes.Add(tn);
            Dictionary<string, int> dic = new Dictionary<string, int>();
            dic["GSM_扫频"] = 12;
            foreach (string key in dic.Keys)
            {
                TreeNode t = new TreeNode(key);
                t.Tag = dic[key];
                t.Checked = true;
                tn.Nodes.Add(t);
            }
            tv.ExpandAll();
        }
        private void initGSMDTServiceType(TreeView tv, List<int> listCheck)
        {
            listCheck = listCheck == null ? new List<int>() : listCheck;
            tv.Nodes.Clear();
            TreeNode tn = new TreeNode("全部");
            tv.Nodes.Add(tn);
            Dictionary<string, int> dic = new Dictionary<string, int>();
            dic["GSM_语音"] = 1;
            dic["GSM_GPRS数据"] = 2;
            dic["GSM_EDGE数据"] = 3;
            dic["GSM_空闲模式"] = 22;
            dic["GSM_MTR"] = 24;
            dic["GSM_CTR"] = 29;
            foreach (string key in dic.Keys)
            {
                TreeNode t = new TreeNode(key);
                t.Tag = dic[key];
                if (listCheck.Count == 0
                    || listCheck.Contains(dic[key]))
                {
                    t.Checked = true;
                }
                tn.Nodes.Add(t);
            }
            tv.ExpandAll();
        }
        private void initTDDTServiceType(TreeView tv, List<int> listCheck)
        {
            listCheck = listCheck == null ? new List<int>() : listCheck;
            tv.Nodes.Clear();
            TreeNode tn = new TreeNode("全部");
            tv.Nodes.Add(tn);
            Dictionary<string, int> dic = new Dictionary<string, int>();
            dic["TD_语音"] = 4;
            dic["TD_数据R4"] = 5;
            dic["TD_视频通话"] = 13;
            dic["TD_空闲模式"] = 17;
            dic["TD_HSDPA"] = 18;
            dic["TD_HSUPA"] = 27;
            dic["TD_CTR"] = 30;
            foreach (string key in dic.Keys)
            {
                TreeNode t = new TreeNode(key);
                t.Tag = dic[key];
                if (listCheck.Count == 0
                    || listCheck.Contains(dic[key]))
                {
                    t.Checked = true;
                }
                tn.Nodes.Add(t);
            }
            tv.ExpandAll();
        }
        private void initValues()
        {
            ScanGridAnaSettingCondition cond = ScanGridAnaSettingCondition.Instance;
            if (cond.LoadCondition())
            {
                txtShpfile.Text = cond.Shpfile;
                if (!string.IsNullOrEmpty(cond.Shpfile))
                {
                    cbxNameField.Items.Clear();

                    List<string> fields = ShapeHelper.GetFieldNamesFromFile(txtShpfile.Text);
                    if (fields == null || fields.Count == 0)
                    {
                        MessageBox.Show("打开Shapefile文件失败", "错误");
                        txtShpfile.Text = "";
                        return;
                    }
                    foreach (string str in fields)
                    {
                        cbxNameField.Items.Add(str);
                    }
                    if (cbxNameField.Items.Count > 0)
                    {
                        cbxNameField.SelectedIndex = 0;
                    }
                }
                numGridSize.Value= cond.GridSize;
                numRelative.Value= (decimal)cond.RelativeValue;
                numRelMin.Value= (decimal)cond.RelativeMin;
                numAbsolute.Value= (decimal)cond.AbsoluteValue;
                numValid.Value= (decimal)cond.ValidValue;
                numGridCount.Value= cond.ConsecutiveCount;
                numHighCoverage.Value= (decimal)cond.HighCoverage;
                numWeakRxlev.Value = (decimal)cond.WeakRxlev;     
            }
        }
        private void ChkUseCmp_CheckedChanged(object sender, EventArgs e)
        {
            bool cmped = chkUseCmp.Checked;

            pickerCmpStartTime.Enabled = cmped;
            pickerCmpEndTime.Enabled = cmped;
            btnCmpProjectType.Enabled = cmped;
            listViewCmp.Enabled = cmped;
            cmpFileFilter.Enabled = cmped;
            this.treeViewServiceTypeCmp.Enabled = cmped;

        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.None;

            bool isValid = judageValidCondition();
            if (!isValid)
            {
                return;
            }
            int index = this.cbxServiceType.SelectedIndex;
            if (index == 2 || index == 3)//选择了GSM路测或者TD路测
            {
                TreeView treeView = this.treeViewServiceType;
                List<int> listServiceType;
                listServiceType = initAnaServiceType(index);
                bool isErr = setServiceType(index, ref treeView, ref listServiceType);
                if (isErr)
                {
                    return;
                }
            }
            if (!checkBoxYiDong.Checked && !checkBoxDianXin.Checked && !checkBoxLianTong.Checked)
            {
                MessageBox.Show("至少选择一个运营商", "错误");
                return;
            }
            SetCondition();
            DialogResult = DialogResult.OK;
        }

        private List<int> initAnaServiceType(int index)
        {
            List<int> listServiceType;
            if (index == 2)//GSM路测
            {
                ScanGridAnaSettingCondition.Instance.AnaServiceType_GSM = new List<int>();
                listServiceType = ScanGridAnaSettingCondition.Instance.AnaServiceType_GSM;
            }
            else//TD路测
            {
                ScanGridAnaSettingCondition.Instance.AnaServiceType_TD = new List<int>();
                listServiceType = ScanGridAnaSettingCondition.Instance.AnaServiceType_TD;
            }

            return listServiceType;
        }

        private bool setServiceType(int index, ref TreeView treeView, ref List<int> listServiceType)
        {
            string errMesg = "业务类型至少选择一个项目";
            for (int i = 0; i < 2; i++)
            {
                if (treeView.Nodes.Count < 0)
                {
                    MessageBox.Show("业务类型初始化出错", "错误");//不应该发生
                    return true;
                }
                TreeNode tn = treeView.Nodes[0];
                int selectCount = addListServiceType(listServiceType, tn);
                if (selectCount == 0)
                {
                    MessageBox.Show(errMesg, "错误");
                    return true;
                }
                if ((i != 0) || (!chkUseCmp.Checked))
                {
                    break;
                }
                //把变量指向对比数据源的业务类型条件，下一个循环将是检查对比数据源的业务类型是否有效。
                treeView = this.treeViewServiceTypeCmp;
                errMesg = "对比业务类型至少选择一个项目";
                listServiceType = initCmpServiceType(index);
            }
            return false;
        }

        private static int addListServiceType(List<int> listServiceType, TreeNode tn)
        {
            int selectCount = 0;
            foreach (TreeNode t in tn.Nodes)
            {
                if (t.Checked)
                {
                    selectCount++;
                    listServiceType.Add((int)t.Tag);
                }
            }
            return selectCount;
        }

        private List<int> initCmpServiceType(int index)
        {
            List<int> listServiceType;
            if (index == 2)//GSM路测
            {
                ScanGridAnaSettingCondition.Instance.CmpServiceType_GSM = new List<int>();
                listServiceType = ScanGridAnaSettingCondition.Instance.CmpServiceType_GSM;
            }
            else//TD路测
            {
                ScanGridAnaSettingCondition.Instance.CmpServiceType_TD = new List<int>();
                listServiceType = ScanGridAnaSettingCondition.Instance.CmpServiceType_TD;
            }

            return listServiceType;
        }

        private bool judageValidCondition()
        {
            if (pickerAnaStartTime.Value > pickerAnaEndTime.Value)
            {
                MessageBox.Show("分析数据源时间设置不正确", "错误");
                return false;
            }
            if (chkUseCmp.Checked && pickerCmpStartTime.Value > pickerCmpEndTime.Value)
            {
                MessageBox.Show("对比数据源时间设置不正确", "错误");
                return false;
            }
            if (listViewAna.Items.Count == 0)
            {
                MessageBox.Show("分析数据源至少选择一个项目", "错误");
                return false;
            }
            if (chkUseCmp.Checked && listViewAna.Items.Count == 0)
            {
                MessageBox.Show("对比数据源至少选择一个项目", "错误");
                return false;
            }
            if (txtShpfile.Text == "")
            {
                MessageBox.Show("请指定网格图层文件", "错误");
                return false;
            }
            if (cbxNameField.SelectedItem == null)
            {
                MessageBox.Show("请指定网格图层名称字段", "错误");
                return false;
            }
            return true;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void BtnOpen_Click(object sender, EventArgs e)
        {
            OpenFileDialog openDlg = new OpenFileDialog();
            openDlg.Filter = FilterHelper.Shp;
            if (openDlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            txtShpfile.Text = openDlg.FileName;
            cbxNameField.Items.Clear();

            List<string> fields = ShapeHelper.GetFieldNamesFromFile(txtShpfile.Text);
            if (fields == null || fields.Count == 0)
            {
                MessageBox.Show("打开Shapefile文件失败", "错误");
                txtShpfile.Text = "";
                return;
            }
            foreach (string str in fields)
            {
                cbxNameField.Items.Add(str);
            }
            if (cbxNameField.Items.Count > 0)
            {
                cbxNameField.SelectedIndex = 0;
            }
        }

        private void BtnAnaProjectType_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(btnAnaProjectType.Width, btnAnaProjectType.Height);
            dropDownAnaProject.Show(btnAnaProjectType, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void BtnCmpProjectType_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(btnCmpProjectType.Width, btnCmpProjectType.Height);
            dropDownCmpProject.Show(btnCmpProjectType, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void ChkAnaFileName_CheckedChanged(object sender, EventArgs e)
        {
            anaRadioGroup.Enabled = anaTxtFileName.Enabled = anaChkFileName.Checked;
        }

        private void ChkCmpFileName_CheckedChanged(object sender, EventArgs e)
        {
            cmpRadioGroup.Enabled = cmpTxtFileName.Enabled = cmpChkFileName.Checked;
        }

        private void RadioGroup_MouseClick(object sender, MouseEventArgs e)
        {
            TextBox curTxt = null;
            RadioGroup curGroup = sender as RadioGroup;
            if (curGroup == anaRadioGroup)
            {
                curTxt = anaTxtFileName;
            }
            else
            {
                curTxt = cmpTxtFileName;

            }

            if (curGroup.SelectedIndex == 3)
            {
                Rectangle rct = curGroup.GetItemRectangle(3);
                if (rct.Contains(new Point(e.X, e.Y)))
                {
                    curTxt.Enabled = false;
                    Point pt =
                        curGroup.PointToScreen(
                            new Point(curGroup.Width - curGroup.Location.X,
                                curGroup.Height));
                    MainModel.GetInstance().MainForm.PopupContainer.Tag = curGroup;
                    MainModel.GetInstance().MainForm.PopupContainer.CloseUp += PopupControl_CloseUp;
                    MainModel.GetInstance().MainForm.PopupContainer.ShowPopup(pt);
                }
            }
        }

        private void RadioGroup_SelectedChanged(object sender, EventArgs e)
        {
            TextBox curTxt = null;
            RadioGroup curGroup = sender as RadioGroup;
            if (curGroup == anaRadioGroup)
            {
                curTxt = anaTxtFileName;
            }
            else
            {
                curTxt = cmpTxtFileName;

            }
            curTxt.Enabled = curGroup.SelectedIndex != 3;
        }

        private void PopupControl_CloseUp(object sender, EventArgs e)
        {
            RadioGroup curGroup = (sender as PopupControlContainer).Tag as RadioGroup;
            curGroup.Tag = MainModel.GetInstance().MainForm.makeMarkFileFilterString();
            MainModel.GetInstance().MainForm.PopupContainer.CloseUp -= PopupControl_CloseUp;
            MainModel.GetInstance().MainForm.PopupContainer.Tag = null;
        }

        private void InitProjectTypes()
        {
            if (model.CategoryManager["Project"] != null)
            {
                ItemSelectionPanel projPanelAna = new ItemSelectionPanel(dropDownAnaProject, listViewAna, labelAnaProject, ItemSelection, "Project", true);
                dropDownAnaProject.Items.Clear();
                projPanelAna.FreshItems();
                dropDownAnaProject.Items.Add(new ToolStripControlHost(projPanelAna));

                ItemSelectionPanel projPanelCmp = new ItemSelectionPanel(dropDownCmpProject, listViewCmp, labelCmpProject, ItemSelection, "Project", true);
                dropDownCmpProject.Items.Clear();
                projPanelCmp.FreshItems();
                dropDownCmpProject.Items.Add(new ToolStripControlHost(projPanelCmp));
            }
        }

        private void SetCondition()
        {
            ScanGridAnaSettingCondition cond = ScanGridAnaSettingCondition.Instance;
            cond.AnaStartTime = pickerAnaStartTime.Value.Date;
            cond.AnaEndTime = pickerAnaEndTime.Value.Date.AddDays(1);
            cond.CmpStartTime = pickerCmpStartTime.Value.Date;
            cond.CmpEndTime = pickerCmpEndTime.Value.Date.AddDays(1);
            cond.IsCmpUsed = chkUseCmp.Checked;
            if (cbxServiceType.SelectedIndex == 0) cond.ServiceType = 12;//GSM扫频
            else if (cbxServiceType.SelectedIndex == 1) cond.ServiceType = 19;//TD扫频
            else if (cbxServiceType.SelectedIndex == 2) cond.ServiceType = 1212;//GSM路测
            else cond.ServiceType = 1919;//TD路测

            List<string> anaProjs = new List<string>();
            cond.AnaProjectTypes = new List<int>();
            foreach (ListViewItem item in listViewAna.Items)
            {
                cond.AnaProjectTypes.Add((byte)(int)item.Tag);
                anaProjs.Add(item.Text);
            }
            cond.AnaProjectDesc = anaProjs;

            List<string> cmpProjs = new List<string>();
            cond.CmpProjectTypes = new List<int>();
            foreach (ListViewItem item in listViewCmp.Items)
            {
                cond.CmpProjectTypes.Add((byte)(int)item.Tag);
                cmpProjs.Add(item.Text);
            }
            cond.CmpProjectDesc = cmpProjs;

            cond.Shpfile = txtShpfile.Text;
            cond.NameField = cbxNameField.SelectedItem as string;
            cond.GridSize = (int)numGridSize.Value;
            cond.RelativeValue = (double)numRelative.Value;
            cond.RelativeMin = (double)numRelMin.Value;
            cond.AbsoluteValue = (double)numAbsolute.Value;
            cond.ValidValue = (double)numValid.Value;
            cond.ConsecutiveCount = (int)numGridCount.Value;
            cond.HighCoverage = (double)numHighCoverage.Value;
            cond.WeakRxlev = (double)numWeakRxlev.Value;

            addCondAnaFile(cond);
            addCondCmpFile(cond);
            addCondCarriers(cond);
            cond.Save();
        }

        private void addCondAnaFile(ScanGridAnaSettingCondition cond)
        {
            cond.AnaFileName = null;
            if (anaChkFileName.Checked)
            {
                FileFilterType fType = (FileFilterType)anaRadioGroup.SelectedIndex;
                cond.AnaFilterType = fType;
                if (fType == FileFilterType.ByMark_ID) //文件标记 ID过滤
                {
                    cond.AnaFileName = anaRadioGroup.Tag as string;
                }
                else
                {
                    int orNum = 1;
                    cond.AnaFileName = QueryCondition.MakeFileFilterString(anaTxtFileName.Text, ref orNum);
                    cond.AnaFileNameOrNum = orNum;
                }
            }
        }

        private void addCondCmpFile(ScanGridAnaSettingCondition cond)
        {
            cond.CmpFileName = null;
            if (chkUseCmp.Checked && cmpChkFileName.Checked)
            {
                FileFilterType fType = (FileFilterType)cmpRadioGroup.SelectedIndex;
                cond.CmpFilterType = fType;
                if (fType == FileFilterType.ByMark_ID) //文件标记 ID过滤
                {
                    cond.CmpFileName = cmpRadioGroup.Tag as string;
                }
                else
                {
                    int orNum = 1;
                    cond.CmpFileName = QueryCondition.MakeFileFilterString(cmpTxtFileName.Text, ref orNum);
                    cond.CmpFileNameOrNum = orNum;
                }
            }
        }

        private void addCondCarriers(ScanGridAnaSettingCondition cond)
        {
            cond.Carriers = new List<int>();
            if (checkBoxYiDong.Checked)
            {
                cond.Carriers.Add(1);
            }
            if (checkBoxDianXin.Checked)
            {
                cond.Carriers.Add(3);
            }
            if (checkBoxLianTong.Checked)
            {
                cond.Carriers.Add(2);
            }
        }

        private MainModel model;
        private MapFormItemSelection ItemSelection = new MapFormItemSelection();
    }

    public class ScanGridAnaSettingCondition
    {
        public DateTime AnaStartTime { get; set; }
        public DateTime AnaEndTime { get; set; }
        public DateTime CmpStartTime { get; set; }
        public DateTime CmpEndTime { get; set; }
        public int ServiceType { get; set; }

        public static readonly string ConfigPath = Application.StartupPath + @"\config\GSM_TDMgrsCondition.xml";

        public string ServiceDesc
        {
            get
            {
                if (ServiceType == 12)
                {
                    return "GSM扫频";
                }
                else if (ServiceType == 19)
                {
                    return "TD扫频";
                }
                else if (ServiceType == 1212)
                {
                    return "GSM路测";
                }
                else if (ServiceType == 1919)
                {
                    return "TD路测";
                }
                return "未知";
            }
        }
        public List<int> AnaProjectTypes { get; set; }
        public List<int> CmpProjectTypes { get; set; }
        public List<string> AnaProjectDesc { get; set; }
        public List<string> CmpProjectDesc { get; set; }
        public List<int> AnaServiceType_GSM { get; set; }//gsm路测业务类型
        public List<int> CmpServiceType_GSM { get; set; }//gsm路测对比数据源业务类型
        public List<int> AnaServiceType_TD { get; set; }//TD路测业务类型
        public List<int> CmpServiceType_TD { get; set; }//TD路测对比数据源业务类型
        public List<int> Carriers { get; set; } = new List<int>();//运营商
        public bool IsCmpUsed { get; set; }
        public string AnaFileName { get; set; }
        public int AnaFileNameOrNum { get; set; } = 1;
        public string CmpFileName { get; set; }
        public int CmpFileNameOrNum { get; set; } = 1;
        public FileFilterType AnaFilterType { get; set; }
        public FileFilterType CmpFilterType { get; set; }

        public string Shpfile { get; set; }
        public string NameField { get; set; }

        public double RelativeValue { get; set; }
        public double RelativeMin { get; set; }
        public double AbsoluteValue { get; set; }
        public double ValidValue { get; set; }
        public double WeakRxlev { get; set; }
        public double HighCoverage { get; set; }
        public int ConsecutiveCount { get; set; }

        public int GridSize { get; set; }

        public void Save()
        {
            XmlConfigFile xcfg = new XmlConfigFile();
            XmlElement cfgCondition = xcfg.AddConfig("SettingCondition");
            xcfg.AddItem(cfgCondition, "Shpfile", Shpfile);
            xcfg.AddItem(cfgCondition, "RelativeValue", RelativeValue);
            xcfg.AddItem(cfgCondition, "RelativeMin", RelativeMin);
            xcfg.AddItem(cfgCondition, "AbsoluteValue", AbsoluteValue);
            xcfg.AddItem(cfgCondition, "ValidValue", ValidValue);
            xcfg.AddItem(cfgCondition, "WeakRxlev", WeakRxlev);
            xcfg.AddItem(cfgCondition, "HighCoverage", HighCoverage);
            xcfg.AddItem(cfgCondition, "ConsecutiveCount", ConsecutiveCount);
            xcfg.AddItem(cfgCondition, "GridSize", GridSize);
            ScanGridAnaColorRanger.Instance.SaveAllColorRanges(xcfg);
            xcfg.Save(ConfigPath);
        }

        public bool LoadCondition()
        {
            XmlConfigFile xcfg = new XmlConfigFile(ConfigPath);
            if (xcfg.Load())
            {
                XmlElement configCondition = xcfg.GetConfig("SettingCondition");
                Shpfile = setData(xcfg, configCondition, "Shpfile", Shpfile);
                RelativeValue = setData(xcfg, configCondition, "RelativeValue", RelativeValue);
                RelativeMin = setData(xcfg, configCondition, "RelativeMin", RelativeMin);
                AbsoluteValue = setData(xcfg, configCondition, "AbsoluteValue", AbsoluteValue);
                ValidValue = setData(xcfg, configCondition, "ValidValue", ValidValue);
                WeakRxlev = setData(xcfg, configCondition, "WeakRxlev", WeakRxlev);
                HighCoverage = setData(xcfg, configCondition, "HighCoverage", HighCoverage);
                ConsecutiveCount = setData(xcfg, configCondition, "ConsecutiveCount", ConsecutiveCount);
                GridSize = setData(xcfg, configCondition, "GridSize", GridSize);
                return true;
            }
            return false;
        }

        private T setData<T>(XmlConfigFile xcfg, XmlElement configCondition, string name, T defaultData)
        {
            object obj = xcfg.GetItemValue(configCondition, name);
            if (obj != null && obj is T)
            {
                return (T)obj;
            }
            return defaultData;
        }

        private static readonly ScanGridAnaSettingCondition instance = new ScanGridAnaSettingCondition();
        static ScanGridAnaSettingCondition()
        {
        }
        private ScanGridAnaSettingCondition()
        {
        }

        public static ScanGridAnaSettingCondition Instance
        {
            get { return instance; }
        }
    }
}
