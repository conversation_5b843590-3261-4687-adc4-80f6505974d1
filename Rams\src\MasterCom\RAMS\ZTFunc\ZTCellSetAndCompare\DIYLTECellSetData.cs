﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.src.MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Net
{
    public class DIYLTECellSetData : QueryBase
    {
        public DIYLTECellSetData(MainModel mainModel)
            : base(mainModel)
        { 
        }
        public override string Name
        {
            get { return "小区集精简查询"; }
        }
        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22035, this.Name);
        }
        #region 全局变量
        Dictionary<CellKey, List<LongLat>> cellkeyDic { get; set; }
        int curDistricID { get; set; } = -1;
        int iCurCity { get; set; } = 0;
        int iCityNum { get; set; } = 0;
        float fCityPart { get; set; } = 0;
        string strCityName { get; set; } = "";
        public bool isMaiCell { get; set; } = false;
        public bool isNearCell { get; set; } = false;
        public bool isContainNotGrid { get; set; } = false;
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> ltecellStaterMap { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> ltecellStaterMapLT { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> ltecellStaterMapDX { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        List<CellSetDetailInfo> cellSetDetailInfoList { get; set; } = new List<CellSetDetailInfo>();
        List<CellSetSummaryInfo> cellSetSummaryInfoList { get; set; } = new List<CellSetSummaryInfo>();
        List<CellSetStat> cellSetStatList { get; set; } = new List<CellSetStat>();
        Dictionary<CellSetDetailInfo, CellSetDetailInfo> cellSetSeverNullInfoDic { get; set; } = new Dictionary<CellSetDetailInfo, CellSetDetailInfo>();
        Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic { get; set; } = null;
        #endregion        
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        /// <summary>
        /// 查询准备条件
        /// </summary>
        protected override void query()
        {
            ZTCellSetBriefSetForm cellTypeForm = new ZTCellSetBriefSetForm();
            if (cellTypeForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            bool maiCell = false, nearCell = false, containNotGrid = false;
            cellTypeForm.getSelect(ref maiCell, ref nearCell, ref containNotGrid);
            isMaiCell = maiCell;
            isNearCell = nearCell;
            isContainNotGrid = containNotGrid;

            cellkeyDic = new Dictionary<CellKey, List<LongLat>>();
            mutRegionMopDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
            InitRegionMop2();
            ltecellStaterMap.Clear();
            ltecellStaterMapLT.Clear();
            ltecellStaterMapDX.Clear();
            cellSetDetailInfoList.Clear();
            cellSetSummaryInfoList.Clear();
            cellSetStatList.Clear();
            cellSetSeverNullInfoDic.Clear();

            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy;
            iCurCity = 0;
            iCityNum = condition.DistrictIDs.Count;
            if (iCityNum == 0)
                iCityNum = 1;
            fCityPart = 90 / ((float)iCityNum);
            WaitBox.ProgressPercent = 0;

            curDistricID = MainModel.DistrictID;
            foreach (int DistrictID in condition.DistrictIDs)
            {
                iCurCity++;
                strCityName = DistrictManager.GetInstance().getDistrictName(DistrictID);
                clientProxy = new ClientProxy();
                MainModel.DistrictID = DistrictID;
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    curDistricID = -1;
                    return;
                }
                WaitBox.CanCancel = true;
                WaitBox.Show("正在获取小区...", queryInThread);
            }
            MainModel.DistrictID = curDistricID;
            cellSetDetailInfoList.AddRange(ltecellStaterMap.Keys);
            cellSetDetailInfoList.AddRange(ltecellStaterMapLT.Keys);
            cellSetDetailInfoList.AddRange(ltecellStaterMapDX.Keys);
            cellSetDetailInfoList.Sort(CellSetDetailInfo.GetCompareByGridType());
            MergeCellSummaryInfo();//小区集概要统计
            MergeCellByServiceInfo();//小区集按业务汇总
            ShowFormAfterQueryNew();
        }
        /// <summary>
        /// 查询数据过程
        /// </summary>
        private void queryInThread()
        {
            try
            {
                string projIdStr = "";
                string servIdStr = "";
                string carrierTypeStr = "";
                string areaTypeIdStr = "";
                string agentIdStr = "";
                cellkeyDic = new Dictionary<CellKey, List<LongLat>>();
                getConditionStr(ref projIdStr, ref servIdStr, ref carrierTypeStr, ref areaTypeIdStr, ref agentIdStr);

                DateTime dBeginTime = condition.Periods[0].BeginTime;
                DateTime dEndTime = condition.Periods[0].EndTime;
                int beginTime = (int)(JavaDate.GetMilliseconds(condition.Periods[0].BeginTime) / 1000);
                int endTime = (int)(JavaDate.GetMilliseconds(condition.Periods[0].EndTime) / 1000);

                List<DateTime> dTimeList = new List<DateTime>();
                while (dBeginTime <= dEndTime)
                {
                    DateTime dTime = Convert.ToDateTime(string.Format("{0:yyyy-MM}-01 00:00:00", dBeginTime));
                    if (!dTimeList.Contains(dTime))
                        dTimeList.Add(dTime);
                    dBeginTime = dBeginTime.AddDays(1);
                }

                int iCurRound = 0;
                int iRoundNum = dTimeList.Count;
                float fRoundPart = fCityPart / ((float)iRoundNum);
                DiySqlQueryTbLogFile queryTbLogFile = new DiySqlQueryTbLogFile(MainModel, projIdStr, servIdStr, carrierTypeStr, areaTypeIdStr, agentIdStr);
                DiySqlQueryCellDatabase queryCell = new DiySqlQueryCellDatabase(MainModel, projIdStr, servIdStr, carrierTypeStr, areaTypeIdStr, agentIdStr);
                foreach (DateTime dTime in dTimeList)
                {
                    iCurRound++;
                    DiySqlQueryCellDatabase.dayDBList.Clear();
                    string logFileName = string.Format("tb_log_file_{0:yyyy}_{0:MM}", dTime);
                    queryTbLogFile.FillData(logFileName, beginTime, endTime);
                    queryTbLogFile.Query();
                    int iTableNum = queryTbLogFile.sampletbnameLst.Count;
                    if (iTableNum == 0)
                        iTableNum = 1;

                    queryCell.FillData(Condition.FileName, logFileName, cellkeyDic, queryTbLogFile.sampletbnameLst, dEndTime);
                    queryCellInfo(iCurRound, iRoundNum, fRoundPart, queryTbLogFile, iTableNum, queryCell);
                }
                WaitBox.Text = "正在获取小区名称...";

                dealCellKeyDic();
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void dealCellKeyDic()
        {
            foreach (CellKey key in cellkeyDic.Keys)
            {
                List<LongLat> tmpList = cellkeyDic[key];
                if (key.iServicetype == (int)ServiceType.LTE_TDD_VOICE || key.iServicetype == (int)ServiceType.LTE_TDD_DATA || key.iServicetype == (int)ServiceType.LTE_TDD_IDLE
                    || key.iServicetype == (int)ServiceType.LTE_TDD_MULTI || key.iServicetype == (int)ServiceType.LTE_TDD_VOLTE || key.iServicetype == (int)ServiceType.SER_LTE_TDD_VIDEO_VOLTE // LTE_TDD
                    || key.iServicetype == (int)ServiceType.LTE_FDD_VOICE || key.iServicetype == (int)ServiceType.LTE_FDD_DATA || key.iServicetype == (int)ServiceType.LTE_FDD_IDLE
                    || key.iServicetype == (int)ServiceType.LTE_FDD_MULTI || key.iServicetype == (int)ServiceType.LTE_FDD_VOLTE || key.iServicetype == (int)ServiceType.SER_LTE_FDD_VIDEO_VOLTE)//LTE_FDD
                {
                    dealDataByCarrier(key, tmpList);
                }
            }
        }

        private void dealDataByCarrier(CellKey key, List<LongLat> tmpList)
        {
            int iECiTem = key.iCi;
            if (key.inet == 4)
            {
                iECiTem = key.iCi / 256 * 256 + key.iCi % 256 % 10;
            }
            if (key.iCarriertype == 1)
            {
                dealCMData(key, tmpList, iECiTem);
            }
            else if (key.iCarriertype == 2)
            {
                dealCUData(key, tmpList, iECiTem);
            }
            else
            {
                dealCTData(key, tmpList, iECiTem);
            }
        }

        private void dealCMData(CellKey key, List<LongLat> tmpList, int iECiTem)
        {
            if (key.inet == 2 || key.inet == 3 || (key.inet == 4 && (key.iMnc == 0 || key.iMnc == 2 || key.iMnc == 7)))
            {
                doWithLTEData_YD(key, tmpList[0], iECiTem);
            }
        }

        private void dealCUData(CellKey key, List<LongLat> tmpList, int iECiTem)
        {
            if (key.inet == 2 || key.inet == 3 || (key.inet == 4 && key.iMnc == 1))
            {
                doWithLTEData_LT(key, tmpList[0], iECiTem);
            }
        }

        private void dealCTData(CellKey key, List<LongLat> tmpList, int iECiTem)
        {
            if (key.inet == 2 || key.inet == 3 || (key.inet == 4 && (key.iMnc == 3 || key.iMnc == 11)))
            {
                doWithLTEData_DX(key, tmpList[0], iECiTem);
            }
        }

        private void queryCellInfo(int iCurRound, int iRoundNum, float fRoundPart, DiySqlQueryTbLogFile queryTbLogFile, int iTableNum, DiySqlQueryCellDatabase queryCell)
        {
            string tbsample = "";
            for (int i = -1; i < queryTbLogFile.sampletbnameLst.Count; i++)
            {
                bool hasGet = true;
                if (isMaiCell)
                {
                    hasGet = getTbSample(queryTbLogFile, ref tbsample, i);
                    if (hasGet)
                    {
                        queryCell.FillData(tbsample, "sp_auto_stat_cellinfo_all");
                        queryCell.SetQueryCondition(Condition);
                        queryCell.Query();
                    }
                }
                //邻小区信息查询
                if (isNearCell)
                {
                    hasGet = getTbSample(queryTbLogFile, ref tbsample, i);
                    if (hasGet)
                    {
                        queryCell.FillData(tbsample, "sp_auto_stat_nbcellinfo");
                        queryCell.SetQueryCondition(Condition);
                        queryCell.Query();
                    }
                }
                if (hasGet)
                {
                    WaitBox.Text = string.Format("当前城市{2},{3}/{4};月份:{0}/{1},数据表：{5}/{6}...", iCurRound, iRoundNum, strCityName, iCurCity, iCityNum, i + 1, iTableNum);
                    WaitBox.ProgressPercent = (int)((iCurCity - 1) * fCityPart + (iCurRound - 1) * fRoundPart);
                }
            }
        }


        private bool getTbSample(DiySqlQueryTbLogFile queryTbLogFile, ref string tbsample, int i)
        {
            if (i != -1)
            {
                if (!DiySqlQueryCellDatabase.dayDBList.Contains(queryTbLogFile.sampletbnameLst[i]))
                {
                    tbsample = queryTbLogFile.sampletbnameLst[i];
                }
                else
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 获取预存或圈选的区域
        /// </summary>
        private void InitRegionMop2()
        {
            Dictionary<string, List<ResvRegion>> resvRegionsDic = MainModel.SearchGeometrys.SelectedResvRegionDic;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;
            if (resvRegionsDic != null && resvRegionsDic.Count > 0)
            {
                foreach (string strGridType in resvRegionsDic.Keys)
                {
                    addRegion(resvRegionsDic, strGridType);
                }
            }
            else if (gmt != null)//单个区域
            {
                Dictionary<string, MapOperation2> regionMopDic =
                    new Dictionary<string, MapOperation2>();
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
                mutRegionMopDic.Add("无网格类型", regionMopDic);
            }
        }

        private void addRegion(Dictionary<string, List<ResvRegion>> resvRegionsDic, string strGridType)
        {
            if (!mutRegionMopDic.ContainsKey(strGridType))
            {
                Dictionary<string, MapOperation2> regionMop = new Dictionary<string, MapOperation2>();
                foreach (ResvRegion region in resvRegionsDic[strGridType])
                {
                    if (!regionMop.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMop.Add(region.RegionName, mapOp2);
                    }
                }
                mutRegionMopDic.Add(strGridType, regionMop);
            }
        }

        /// <summary>
        /// 小区集概要统计
        /// </summary>
        private void MergeCellSummaryInfo()
        {
            Dictionary<CellSetSummaryInfo, CellSetSummaryInfo> cellSetSummaryInfoDic = new Dictionary<CellSetSummaryInfo, CellSetSummaryInfo>();
            foreach (CellSetDetailInfo csInfo in cellSetDetailInfoList)
            {
                CellSetSummaryInfo csSubInfo = new CellSetSummaryInfo();
                csSubInfo.strCarrier = csInfo.strCarrier;
                csSubInfo.strCellType = csInfo.strCellType;
                csSubInfo.strCity = csInfo.strCity;
                csSubInfo.strGridType = csInfo.strGridType;
                csSubInfo.strGrid = csInfo.strGrid;
                csSubInfo.strServiceType = csInfo.strServiceType;

                if (cellSetSummaryInfoDic.ContainsKey(csSubInfo))
                {
                    cellSetSummaryInfoDic[csSubInfo].iCellCount += 1;
                    List<string> cellList = cellSetSummaryInfoDic[csSubInfo].indoorCellList;
                    List<string> btsList = cellSetSummaryInfoDic[csSubInfo].btsList;
                    if (csInfo.strBtsType == "室内" && !cellList.Contains(csInfo.strCellName))
                    {
                        cellList.Add(csInfo.strCellName);
                    }
                    if (!btsList.Contains(csInfo.strBtsName))
                    {
                        btsList.Add(csInfo.strBtsName);
                    }
                }
                else
                {
                    List<string> cellList = new List<string>();
                    List<string> btsList = new List<string>();
                    if (csInfo.strBtsType == "室内")
                        cellList.Add(csInfo.strCellName);
                    btsList.Add(csInfo.strBtsName);
                    csSubInfo.indoorCellList = cellList;
                    csSubInfo.btsList = btsList;
                    csSubInfo.iCellCount = 1;
                    cellSetSummaryInfoDic.Add(csSubInfo, csSubInfo);
                }
            }

            foreach (CellSetSummaryInfo cellSetSum in cellSetSummaryInfoDic.Keys)
            {
                cellSetSummaryInfoList.Add(cellSetSummaryInfoDic[cellSetSum]);
            }
        }
        /// <summary>
        /// 按业务类型逐项累加
        /// </summary>
        private void StatCellNumOnByOne(Dictionary<StatKey, CellSetStat> cellSetDic,
            CellSetDetailInfo cellSetInfo, string strNet)
        {
            bool issame = isTheSame(cellSetInfo);
            StatKey statKey = new StatKey();
            statKey.strCity = cellSetInfo.strCity;
            statKey.strGridType = cellSetInfo.strGridType;
            statKey.strGrid = cellSetInfo.strGrid;

            CellSetStat csStat = new CellSetStat();
            if (cellSetDic.ContainsKey(statKey))
                csStat = cellSetDic[statKey];
            if (strNet == "LTE_YD")
            {
                addCMCsStatNum(cellSetInfo, issame, csStat);
            }
            else if (strNet == "LTE_LT")
            {
                addCUCsStatNum(cellSetInfo, issame, csStat);
            }
            else if (strNet == "LTE_DX")
            {
                addCTCsStatNum(cellSetInfo, issame, csStat);
            }

            cellSetDic[statKey] = csStat;
        }

        private static void addCTCsStatNum(CellSetDetailInfo cellSetInfo, bool issame, CellSetStat csStat)
        {
            if (!issame)
                csStat.iAllLteDXCellNum += 1;
            if (cellSetInfo.strServiceType == "电信FDD_语音")
                csStat.iVDXLteCellNum += 1;
            else if (cellSetInfo.strServiceType == "电信FDD_Volte")
                csStat.iVoDXLteCellNum += 1;
            else if (cellSetInfo.strServiceType == "电信FDD_数据")
                csStat.iDDXLteCellNum += 1;
            else if (cellSetInfo.strServiceType == "电信FDD_空闲")
                csStat.iFDXLteCellNum += 1;
            else if (cellSetInfo.strServiceType == "电信FDD_并发")
                csStat.iBDXLteCellNum += 1;
        }

        private static void addCUCsStatNum(CellSetDetailInfo cellSetInfo, bool issame, CellSetStat csStat)
        {
            if (!issame)
                csStat.iAllLteLTCellNum += 1;
            if (cellSetInfo.strServiceType == "联通FDD_语音")
                csStat.iVLTLteCellNum += 1;
            else if (cellSetInfo.strServiceType == "联通FDD_Volte")
                csStat.iVoLTLteCellNum += 1;
            else if (cellSetInfo.strServiceType == "联通FDD_数据")
                csStat.iDLTLteCellNum += 1;
            else if (cellSetInfo.strServiceType == "联通FDD_空闲")
                csStat.iFLTLteCellNum += 1;
            else if (cellSetInfo.strServiceType == "联通FDD_并发")
                csStat.iBLTLteCellNum += 1;
        }

        private static void addCMCsStatNum(CellSetDetailInfo cellSetInfo, bool issame, CellSetStat csStat)
        {
            if (!issame)
                csStat.iAllLteCellNum += 1;
            if (cellSetInfo.strServiceType == "移动TDD_语音")
                csStat.iVLteCellNum += 1;
            else if (cellSetInfo.strServiceType == "移动TDD_Volte")
                csStat.iVoLteCellNum += 1;
            else if (cellSetInfo.strServiceType == "移动TDD_数据")
                csStat.iDLteCellNum += 1;
            else if (cellSetInfo.strServiceType == "移动TDD_空闲")
                csStat.iFLteCellNum += 1;
            else if (cellSetInfo.strServiceType == "移动TDD_并发")
                csStat.iBLteCellNum += 1;
        }

        /// <summary>
        /// 小区集按业务汇总
        /// </summary>
        private void MergeCellByServiceInfo()
        {
            Dictionary<StatKey, CellSetStat> cellSetDic = new Dictionary<StatKey, CellSetStat>();

            foreach (CellSetDetailInfo cellSetInfo in ltecellStaterMap.Keys)
            {
                StatCellNumOnByOne(cellSetDic, cellSetInfo, "LTE_YD");
            }

            foreach (CellSetDetailInfo cellSetInfo in ltecellStaterMapLT.Keys)
            {
                StatCellNumOnByOne(cellSetDic, cellSetInfo, "LTE_LT");
            }

            foreach (CellSetDetailInfo cellSetInfo in ltecellStaterMapDX.Keys)
            {
                StatCellNumOnByOne(cellSetDic, cellSetInfo, "LTE_DX");
            }

            MergeCellByServiceStat(cellSetDic);
        }
        /// <summary>
        /// 小区集按业务统计
        /// </summary>
        private void MergeCellByServiceStat(Dictionary<StatKey, CellSetStat> cellSetDic)
        {
            Dictionary<string, CellSetStat> allCellSetStatDic = new Dictionary<string, CellSetStat>();
            foreach (StatKey statKey in cellSetDic.Keys)
            {
                CellSetStat cellSetStat = cellSetDic[statKey];
                cellSetStat.strCity = statKey.strCity;
                cellSetStat.strGridType = statKey.strGridType;
                cellSetStat.strGrid = statKey.strGrid;
                cellSetStatList.Add(cellSetStat);

                if (allCellSetStatDic.ContainsKey(statKey.strCity))
                {
                    CellSetStat csStat = allCellSetStatDic[statKey.strCity];

                    csStat.iAllLteCellNum += cellSetStat.iAllLteCellNum;
                    csStat.iFLteCellNum += cellSetStat.iFLteCellNum;
                    csStat.iVLteCellNum += cellSetStat.iVLteCellNum;
                    csStat.iDLteCellNum += cellSetStat.iDLteCellNum;
                    csStat.iBLteCellNum += cellSetStat.iBLteCellNum;
                    csStat.iVoLteCellNum += cellSetStat.iVoLteCellNum;

                    csStat.iAllLteLTCellNum += cellSetStat.iAllLteLTCellNum;
                    csStat.iFLTLteCellNum += cellSetStat.iFLTLteCellNum;
                    csStat.iVLTLteCellNum += cellSetStat.iVLTLteCellNum;
                    csStat.iDLTLteCellNum += cellSetStat.iDLTLteCellNum;
                    csStat.iBLTLteCellNum += cellSetStat.iBLTLteCellNum;
                    csStat.iVoLTLteCellNum += cellSetStat.iVoLTLteCellNum;

                    csStat.iAllLteDXCellNum += cellSetStat.iAllLteDXCellNum;
                    csStat.iFDXLteCellNum += cellSetStat.iFDXLteCellNum;
                    csStat.iVDXLteCellNum += cellSetStat.iVDXLteCellNum;
                    csStat.iDDXLteCellNum += cellSetStat.iDDXLteCellNum;
                    csStat.iBDXLteCellNum += cellSetStat.iBDXLteCellNum;
                    csStat.iVoDXLteCellNum += cellSetStat.iVoDXLteCellNum;

                }
                else
                {
                    CellSetStat csStat = new CellSetStat();
                    csStat.strCity = cellSetStat.strCity;
                    csStat.strGridType = "汇总";
                    csStat.strGrid = "汇总";

                    csStat.iAllLteCellNum = cellSetStat.iAllLteCellNum;
                    csStat.iFLteCellNum = cellSetStat.iFLteCellNum;
                    csStat.iVLteCellNum = cellSetStat.iVLteCellNum;
                    csStat.iDLteCellNum = cellSetStat.iDLteCellNum;
                    csStat.iBLteCellNum = cellSetStat.iBLteCellNum;
                    csStat.iVoLteCellNum = cellSetStat.iVoLteCellNum;

                    csStat.iAllLteLTCellNum = cellSetStat.iAllLteLTCellNum;
                    csStat.iFLTLteCellNum = cellSetStat.iFLTLteCellNum;
                    csStat.iVLTLteCellNum = cellSetStat.iVLTLteCellNum;
                    csStat.iDLTLteCellNum = cellSetStat.iDLTLteCellNum;
                    csStat.iBLTLteCellNum = cellSetStat.iBLTLteCellNum;
                    csStat.iVoLTLteCellNum = cellSetStat.iVoLTLteCellNum;

                    csStat.iAllLteDXCellNum = cellSetStat.iAllLteDXCellNum;
                    csStat.iFDXLteCellNum = cellSetStat.iFDXLteCellNum;
                    csStat.iVDXLteCellNum = cellSetStat.iVDXLteCellNum;
                    csStat.iDDXLteCellNum = cellSetStat.iDDXLteCellNum;
                    csStat.iBDXLteCellNum = cellSetStat.iBDXLteCellNum;
                    csStat.iVoDXLteCellNum = cellSetStat.iVoDXLteCellNum;

                    allCellSetStatDic.Add(statKey.strCity, csStat);
                }
            }

            foreach (string strCity in allCellSetStatDic.Keys)
            {
                CellSetStat cellSetStat = allCellSetStatDic[strCity];
                cellSetStat.strCity = strCity;
                cellSetStat.strGridType = "汇总";
                cellSetStat.strGrid = "汇总";
                cellSetStatList.Add(cellSetStat);
            }
        }
        /// <summary>
        /// 判断去重
        /// </summary>
        private bool isTheSame(CellSetDetailInfo cellSetInfo)
        {
            bool isSame = false;
            CellSetDetailInfo cellTem = new CellSetDetailInfo();
            cellTem.strCity = cellSetInfo.strCity;
            cellTem.strCarrier = cellSetInfo.strCarrier;
            cellTem.strGridType = cellSetInfo.strGridType;
            cellTem.strGrid = cellSetInfo.strGrid;
            cellTem.strCellName = cellSetInfo.strCellName;
            cellTem.strBtsName = cellSetInfo.strBtsName;
            cellTem.strBtsType = cellSetInfo.strBtsType;

            if (cellSetSeverNullInfoDic.ContainsKey(cellTem))
                isSame = true;
            else
                cellSetSeverNullInfoDic.Add(cellTem, cellTem);
            return isSame;
        }

        #region getCondition
        /// <summary>
        /// 构造SQL的查询条件
        /// </summary>
        private void getConditionStr(ref string projIdStr, ref string servIdStr, ref string carrierTypeStr, ref string areaTypeIdStr, ref string agentIdStr)
        {
            projIdStr = getProjIdStr(projIdStr);

            servIdStr = getServIdStr(servIdStr);

            carrierTypeStr = getCarrierTypeStr(carrierTypeStr);

            areaTypeIdStr = getAreaTypeIdStr(areaTypeIdStr);

            agentIdStr = getAgentIdStr(agentIdStr);
        }

        private string getProjIdStr(string projIdStr)
        {
            for (int i = 0; i < Condition.Projects.Count; i++)
            {
                if (i == 0)
                    projIdStr = Condition.Projects[i].ToString();
                else
                {
                    projIdStr = string.Format(projIdStr + "," + Condition.Projects[i]);
                }
            }
            projIdStr = "(" + projIdStr + ")";
            return projIdStr;
        }

        private string getServIdStr(string servIdStr)
        {
            for (int i = 0; i < Condition.ServiceTypes.Count; i++)
            {
                if (i == 0)
                {
                    servIdStr = Condition.ServiceTypes[i].ToString();
                }
                else
                {
                    servIdStr = string.Format(servIdStr + "," + Condition.ServiceTypes[i]);
                }
            }
            servIdStr = "(" + servIdStr + ")";
            return servIdStr;
        }

        private string getCarrierTypeStr(string carrierTypeStr)
        {
            for (int i = 0; i < Condition.CarrierTypes.Count; i++)
            {
                if (i == 0)
                {
                    carrierTypeStr = Condition.CarrierTypes[i].ToString();
                }
                else
                {
                    carrierTypeStr = string.Format(carrierTypeStr + "," + Condition.CarrierTypes[i]);
                }
            }
            carrierTypeStr = "(" + carrierTypeStr + ")";
            return carrierTypeStr;
        }

        private string getAreaTypeIdStr(string areaTypeIdStr)
        {
            int iKey = 0;
            foreach (int id in Condition.Areas.Keys)
            {
                for (int j = 0; j < Condition.Areas[id].Count; j++)
                {
                    if (iKey == 0)
                    {
                        areaTypeIdStr = Condition.Areas[id][0].ToString();
                        iKey++;
                    }
                    else
                    {
                        areaTypeIdStr = string.Format(areaTypeIdStr + "," + Condition.Areas[id][j]);
                    }
                }
            }
            if (areaTypeIdStr != "")
                areaTypeIdStr = "(" + areaTypeIdStr + ")";
            return areaTypeIdStr;
        }

        private string getAgentIdStr(string agentIdStr)
        {
            for (int i = 0; i < Condition.AgentIds.Count; i++)
            {
                if (i == 0)
                {
                    agentIdStr = Condition.AgentIds[i].ToString();
                }
                else
                {
                    agentIdStr = string.Format(agentIdStr + "," + Condition.AgentIds[i]);
                }
            }
            agentIdStr = "(" + agentIdStr + ")";
            return agentIdStr;
        }
        #endregion

        /// <summary>
        /// 定位所在网格
        /// </summary>
        private void isContainPoint(double x, double y, ref Dictionary<string, string> gridTypeGrid)
        {
            foreach (string gridType in mutRegionMopDic.Keys)
            {
                foreach (string grid in mutRegionMopDic[gridType].Keys)
                {
                    if (mutRegionMopDic[gridType][grid].CheckPointInRegion(x, y) && !gridTypeGrid.ContainsKey(gridType))
                    {
                        gridTypeGrid.Add(gridType, grid);
                        break;
                    }
                }
            }
            if (isContainNotGrid && gridTypeGrid.Count == 0)
            {
                gridTypeGrid.Add("无网格类型", "无网格名称");
            }
        }
        /// <summary>
        /// 获取业务类型(LTE只有移动按业务类型统计,联通电信按频点)
        /// </summary>
        private string getServiceType(int iServiceType,int iCarrierType)
        {
            string strServiceType = "";
            if (iCarrierType == 1)
            {
                strServiceType = getCMServiceType(iServiceType, strServiceType);
            }
            else if (iCarrierType == 2)
            {
                strServiceType = getCUServiceType(iServiceType, strServiceType);
            }
            else if (iCarrierType == 3)
            {
                strServiceType = getCTServiceType(iServiceType, strServiceType);
            }
            return strServiceType;
        }

        private static string getCTServiceType(int iServiceType, string strServiceType)
        {
            if (iServiceType == 45)
                strServiceType = "电信FDD_语音";
            else if (iServiceType == 46)
                strServiceType = "电信FDD_数据";
            else if (iServiceType == 47)
                strServiceType = "电信FDD_空闲";
            else if (iServiceType == 48)
                strServiceType = "电信FDD_并发";
            else if (iServiceType == 49)
                strServiceType = "电信FDD_Volte";
            return strServiceType;
        }

        private static string getCUServiceType(int iServiceType, string strServiceType)
        {
            if (iServiceType == 45)
                strServiceType = "联通FDD_语音";
            else if (iServiceType == 46)
                strServiceType = "联通FDD_数据";
            else if (iServiceType == 47)
                strServiceType = "联通FDD_空闲";
            else if (iServiceType == 48)
                strServiceType = "联通FDD_并发";
            else if (iServiceType == 49)
                strServiceType = "联通FDD_Volte";
            return strServiceType;
        }

        private static string getCMServiceType(int iServiceType, string strServiceType)
        {
            if (iServiceType == 33)
                strServiceType = "移动TDD_语音";
            else if (iServiceType == 34)
                strServiceType = "移动TDD_数据";
            else if (iServiceType == 41)
                strServiceType = "移动TDD_空闲";
            else if (iServiceType == 42)
                strServiceType = "移动TDD_并发";
            else if (iServiceType == 43)
                strServiceType = "移动TDD_Volte";
            return strServiceType;
        }

        /// <summary>
        /// 按LAC\CI构造小区及基站
        /// </summary>
        private void getCellByLacCi(CellKey cKey, ref string strLacCi, ref string strBtsKey)
        {
            strLacCi = string.Format("{0}_{1}", cKey.iLac, cKey.iCi);
            strBtsKey = string.Format("{0}_{1}", cKey.iLac, (cKey.iCi / 256).ToString());
        }
        /// <summary>
        /// 处理移动LTE小区信息
        /// </summary>
        private void doWithLTEData_YD(CellKey cKey, LongLat longLat,int iECi)
        {
            Dictionary<string, string> gridTypeGrid = new Dictionary<string, string>();
            isContainPoint(longLat.fLongitude, longLat.fLatitude, ref gridTypeGrid);
            if (gridTypeGrid.Count < mutRegionMopDic.Count)
            {
                isContainPoint(longLat.fMaxLongitude, longLat.fMaxLatitude, ref gridTypeGrid);
                if (gridTypeGrid.Count == 0)
                    return;
            }
            LTECell ltecell = CellManager.GetInstance().GetLTECell(cKey.dTime, cKey.iLac, cKey.iCi);
            if (ltecell == null)
            {
                ltecell = CellManager.GetInstance().GetLTECell(cKey.dTime, cKey.iLac, iECi);
            }
            foreach (string gridType in gridTypeGrid.Keys)
            {
                addLTECell_YD(ltecell, cKey, gridType, gridTypeGrid[gridType], longLat.iSampleNum);
            }
        }
        /// <summary>
        /// 组织移动LTE
        /// </summary>
        private void addLTECell_YD(LTECell cell, CellKey cKey, string gridType, string gridname, int sampleNum)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = cKey.inet + "G";
            stater.strCarrier = "移动";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype,cKey.iCarriertype);
            stater.sampelSum = sampleNum;
            if (cell != null)
            {
                stater.strCellName = cell.Name;
                stater.strBtsType = "室内";
                if (cell.Type == LTEBTSType.Outdoor)
                    stater.strBtsType = "室外";
                stater.strBtsName = cell.BelongBTS.Name;
            }
            else
            {
                string strLacCi = "";
                string strBtsKey = "";
                getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
                stater.strCellName = strLacCi;
                stater.strBtsName = strBtsKey;
            }
            if (!ltecellStaterMap.ContainsKey(stater))
            {
                ltecellStaterMap.Add(stater, stater);
            }
        }
        /// <summary>
        /// 处理联通LTE小区信息
        /// </summary>
        private void doWithLTEData_LT(CellKey cKey, LongLat longLat, int iECi)
        {
            Dictionary<string, string> gridTypeGrid = new Dictionary<string, string>();
            isContainPoint(longLat.fLongitude, longLat.fLatitude, ref gridTypeGrid);
            if (gridTypeGrid.Count < mutRegionMopDic.Count)
            {
                isContainPoint(longLat.fMaxLongitude, longLat.fMaxLatitude, ref gridTypeGrid);
                if (gridTypeGrid.Count == 0)
                    return;
            }
            LTECell ltecell = CellManager.GetInstance().GetLTECell(cKey.dTime, cKey.iLac, cKey.iCi);
            if (ltecell == null)
            {
                ltecell = CellManager.GetInstance().GetLTECell(cKey.dTime, cKey.iLac, iECi);
            }
            foreach (string gridType in gridTypeGrid.Keys)
            {
                addLTECell_LT(ltecell, cKey, gridType, gridTypeGrid[gridType], longLat.iSampleNum);
            }
        }
        /// <summary>
        /// 组织联通LTE
        /// </summary>
        private void addLTECell_LT(LTECell cell, CellKey cKey, string gridType, string gridname, int sampleNum)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = cKey.inet + "G";
            stater.strCarrier = "联通";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype, cKey.iCarriertype);
            stater.sampelSum = sampleNum;
            if (cell != null)
            {
                stater.strCellName = cell.Name;
                stater.strBtsType = "室内";
                if (cell.Type == LTEBTSType.Outdoor)
                    stater.strBtsType = "室外";
                stater.strBtsName = cell.BelongBTS.Name;
            }
            else
            {
                string strLacCi = "";
                string strBtsKey = "";
                getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
                stater.strCellName = strLacCi;
                stater.strBtsName = strBtsKey;
            }
            if (!ltecellStaterMapLT.ContainsKey(stater))
            {
                ltecellStaterMapLT.Add(stater, stater);
            }
        }
        /// <summary>
        /// 处理电信LTE小区信息
        /// </summary>
        private void doWithLTEData_DX(CellKey cKey, LongLat longLat, int iECi)
        {
            Dictionary<string, string> gridTypeGrid = new Dictionary<string, string>();
            isContainPoint(longLat.fLongitude, longLat.fLatitude, ref gridTypeGrid);
            if (gridTypeGrid.Count < mutRegionMopDic.Count)
            {
                isContainPoint(longLat.fMaxLongitude, longLat.fMaxLatitude, ref gridTypeGrid);
                if (gridTypeGrid.Count == 0)
                    return;
            }
            LTECell ltecell = CellManager.GetInstance().GetLTECell(cKey.dTime, cKey.iLac, cKey.iCi);
            if (ltecell == null)
            {
                ltecell = CellManager.GetInstance().GetLTECell(cKey.dTime, cKey.iLac, iECi);
            }
            foreach (string gridType in gridTypeGrid.Keys)
            {
                addLTECell_DX(ltecell, cKey, gridType, gridTypeGrid[gridType], longLat.iSampleNum);
            }
        }
        /// <summary>
        /// 组织电信LTE
        /// </summary>
        private void addLTECell_DX(LTECell cell, CellKey cKey, string gridType, string gridname, int sampleNum)
        {
            CellSetDetailInfo stater = new CellSetDetailInfo();
            stater.iLac = cKey.iLac;
            stater.iCi = cKey.iCi;
            stater.strCellType = cKey.inet + "G";
            stater.strCarrier = "电信";
            stater.strGridType = gridType;
            stater.strGrid = gridname;
            stater.strCity = cKey.strCity;
            stater.strServiceType = getServiceType(cKey.iServicetype, cKey.iCarriertype);
            stater.sampelSum = sampleNum;
            if (cell != null)
            {
                stater.strCellName = cell.Name;
                stater.strBtsType = "室内";
                if (cell.Type == LTEBTSType.Outdoor)
                    stater.strBtsType = "室外";
                stater.strBtsName = cell.BelongBTS.Name;
            }
            else
            {
                string strLacCi = "";
                string strBtsKey = "";
                getCellByLacCi(cKey, ref strLacCi, ref strBtsKey);
                stater.strCellName = strLacCi;
                stater.strBtsName = strBtsKey;
            }
            if (!ltecellStaterMapDX.ContainsKey(stater))
            {
                ltecellStaterMapDX.Add(stater, stater);
            }
        }
        /// <summary>
        /// 查询完毕后显示小区集
        /// </summary>
        private void ShowFormAfterQueryNew()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(DIYLTECellSetDataForm).FullName);
            DIYLTECellSetDataForm cellLTEForm = obj == null ? null : obj as DIYLTECellSetDataForm;
            if (cellLTEForm == null || cellLTEForm.IsDisposed)
            {
                cellLTEForm = new DIYLTECellSetDataForm(MainModel);
            }
            cellLTEForm.FillData(cellSetDetailInfoList, cellSetSummaryInfoList, cellSetStatList);
            if (!cellLTEForm.Visible)
            {
                cellLTEForm.Show(MainModel.MainForm);
            }
        }
    }
}
