﻿using MapWinGIS;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Collections;
using System.IO;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc.ZTWirelessNetTest
{
    #region 读取图层
    public class WirelessNetTestMap
    {
        private static WirelessNetTestMap instance = null;
        public static WirelessNetTestMap Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new WirelessNetTestMap();
                }
                return instance;
            }
        }

        //用于统计地市级
        public Shape CityMergeShape { get; set; }//用于查询

        //用于统计区县级
        public Shape CountyMergeShape { get; set; }//用于查询
        public Dictionary<string, ResvRegion> CountyDic { get; set; }//用于统计时,区分数据所属区县

        //用于统计场景级的城区和区县数据
        public Dictionary<string, ResvRegion> CountyGridDic { get; set; }//用于统计时,区分数据所属区县网格
        public Dictionary<string, ResvRegion> CityGridDic { get; set; }//用于统计时,区分数据所属城区网格

        //用于统计场景级的城区渗透率
        public Dictionary<string, Shapefile> RoadDic { get; set; }

        public void LoadMap(string districtName, string path)
        {
            string curDistrictMapPath = $@"{path}\{districtName}";
            string countyRegionPath = curDistrictMapPath + @"\区县网格图层.shp";
            string cityRegionPath = curDistrictMapPath + @"\城区网格图层.shp";
            string normalRoadPath = curDistrictMapPath + @"\1-5级道路图层_polyline.shp";

            //string errMsg = "";
            //errMsg += JudgeFileExist(countyRegionPath);
            //errMsg += JudgeFileExist(cityRegionPath);
            //errMsg += JudgeFileExist(normalRoadPath);
            //if (!string.IsNullOrEmpty(errMsg))
            //{
            //    WirelessNetTestConfig.Instance.WriteLog(errMsg, "Error");
            //    return;
            //}

            WirelessNetTestConfig.Instance.WriteLog($"正在加载[区县网格图层]信息...");
            CountyDic = getShapeData<Dictionary<string, ResvRegion>>(countyRegionPath, dealCountyShape);
            CountyMergeShape = combineRegions(new List<ResvRegion>(CountyDic.Values));
            WirelessNetTestConfig.Instance.WriteLog($"获取到[区县]信息{CountyDic.Count}条");
            CountyGridDic = getShapeData<Dictionary<string, ResvRegion>>(countyRegionPath, dealCountyGridShape);
            WirelessNetTestConfig.Instance.WriteLog($"获取到[区县网格]信息{CountyGridDic.Count}条");

            WirelessNetTestConfig.Instance.WriteLog($"正在加载[城区网格图层]信息...");
            CityGridDic = getShapeData<Dictionary<string, ResvRegion>>(cityRegionPath, dealCityGridShape);
            CityMergeShape = combineRegions(new List<ResvRegion>(CityGridDic.Values));
            WirelessNetTestConfig.Instance.WriteLog($"获取到[城区网格]信息{CityGridDic.Count}条");

            WirelessNetTestConfig.Instance.WriteLog($"正在加载[1~5级道路图层]信息...");
            RoadDic = getShapePath(normalRoadPath);
            WirelessNetTestConfig.Instance.WriteLog($"获取到[1~5级道路]信息{RoadDic.Count}条");
        }

        #region 公共方法
        delegate void Func<in T>(Shapefile table, T resDic, params object[] param);
        private T getShapeData<T>(string path, Func<T> func, params object[] param)
            where T : ICollection, new()
        {
            T resDic = new T();
            if (!File.Exists(path))
            {
                WirelessNetTestConfig.Instance.WriteLog($"[{path}]不存在", "Error");
                return resDic;
            }

            Shapefile table = new Shapefile();
            try
            {
                if (!table.Open(path, null))
                {
                    WirelessNetTestConfig.Instance.WriteLog($"图层打开失败{path}", "Error");
                    return resDic;
                }

                func(table, resDic, param);
                if (resDic.Count == 0)
                {
                    WirelessNetTestConfig.Instance.WriteLog($"图层中没有有效的信息{path}", "Error");
                }
            }
            catch (Exception e)
            {
                resDic = new T();
                WirelessNetTestConfig.Instance.WriteLog($"加载图层{path}出错.{e.Message}", "Error");
            }
            finally
            {
                table.Close();
            }
            return resDic;
        }

        delegate void AddShpFunc<in T>(Shape shp, string[] name, T collection, params object[] param);
        private void dealShpTable<T>(Shapefile table, string[] colNames, T collection, AddShpFunc<T> addShpFunc, params object[] param)
        {
            int[] indexs = new int[colNames.Length];
            for (int i = 0; i < colNames.Length; i++)
            {
                indexs[i] = getColumnFieldIndex(table, colNames[i]);
            }

            for (int i = 0; i < table.NumShapes; i++)
            {
                bool isValid = judgeValidShape(table, i, indexs, out Shape shape, out string[] names);
                if (isValid)
                {
                    addShpFunc(shape, names, collection, param);
                }
            }
        }

        private bool judgeValidShape(Shapefile table, int i, int[] indexs, out Shape shape, out string[] names)
        {
            shape = table.get_Shape(i);
            names = new string[indexs.Length];
            if (shape == null)
            {
                return false;
            }

            for (int j = 0; j < indexs.Length; j++)
            {
                names[j] = table.get_CellValue(indexs[j], i).ToString();
                if (string.IsNullOrEmpty(names[j]))
                {
                    return false;
                }
            }

            if (shape.ShapeType == ShpfileType.SHP_POLYGON
                || shape.ShapeType == ShpfileType.SHP_POLYGONZ
                || shape.ShapeType == ShpfileType.SHP_POLYLINE
                || shape.ShapeType == ShpfileType.SHP_POLYLINEZ)
            {
                return true;
            }
            return false;
        }

        private int getColumnFieldIndex(Shapefile sfile, string column)
        {
            int fdIndex = -1;
            for (int fd = 0; fd < sfile.NumFields; fd++)
            {
                Field field = sfile.get_Field(fd);
                if (field.Name == column)
                {
                    fdIndex = fd;
                    break;
                }
            }
            return fdIndex;
        }
        #endregion

        #region 加载网格图层
        private void dealCountyShape(Shapefile table, Dictionary<string, ResvRegion> resDic, params object[] param)
        {
            dealShpTable(table, new string[] { "NAME" }, resDic, addGridShape, param);
        }

        private void dealCountyGridShape(Shapefile table, Dictionary<string, ResvRegion> resDic, params object[] param)
        {
            dealShpTable(table, new string[] { "SNAME" }, resDic, addGridShape, param);
        }

        private void dealCityGridShape(Shapefile table, Dictionary<string, ResvRegion> resDic, params object[] param)
        {
            dealShpTable(table, new string[] { "GRIDID" }, resDic, addGridShape, param);
        }

        private void addGridShape(Shape shape, string[] names, Dictionary<string, ResvRegion> resDic, params object[] param)
        {
            string name = names[0];
            if (!resDic.TryGetValue(name, out ResvRegion scene))
            {
                scene = new ResvRegion
                {
                    RegionName = name,
                    Shape = shape
                };
                resDic.Add(name, scene);
            }
            else
            {
                Shape tempShp = scene.Shape.Clip(shape, tkClipOperation.clUnion);
                if (tempShp != null)
                {
                    resDic[name].Shape = tempShp;
                }
            }
        }
        #endregion

        #region 加载道路图层
        private Dictionary<string, Shapefile> getShapePath(string path)
        {
            var resDic = new Dictionary<string, Shapefile>();
            if (!File.Exists(path))
            {
                WirelessNetTestConfig.Instance.WriteLog($"[{path}]不存在", "Error");
                return resDic;
            }

            if (!string.IsNullOrEmpty(path))
            {
                resDic.Add(path, null);
            }
            return resDic;
        }

        //private void dealNormalRoadShape(Shapefile table, Dictionary<string, Shape> resDic, params object[] param)
        //{
        //    dealShpTable(table, new string[] { "类别" }, resDic, addNormalRoadShape, param);
        //}

        //private void addNormalRoadShape(Shape shape, string[] names, Dictionary<string, Shape> resDic, params object[] param)
        //{
        //    string typeName = names[0];
        //    if (!resDic.TryGetValue(typeName, out Shape road))
        //    {
        //        resDic.Add(typeName, shape);
        //    }
        //    else
        //    {
        //        Shape tempShp = road.Clip(shape, tkClipOperation.clUnion);
        //        if (tempShp != null)
        //        {
        //            resDic[typeName] = tempShp;
        //        }
        //    }
        //}
        #endregion

        private Shape combineRegions(List<ResvRegion> gridList)
        {
            if (gridList.Count == 0)
            {
                return null;
            }
            Shape fstShape = gridList[0].Shape.Clone();//首个区域
            for (int x = 1; x < gridList.Count; x++)//从第二个区域开始与首个区域合并
            {
                Shape shp = gridList[x].Shape;
                for (int subP = 0; subP < shp.NumParts; subP++)//区域可能有多个部分，合并时要分部分合并
                {
                    List<DbPoint> pnts = ShapeHelper.GetPartShapePoints(shp, subP);
                    int numPart = fstShape.NumParts;
                    int numPoint = fstShape.numPoints;
                    fstShape.InsertPart(numPoint, ref numPart);
                    for (int i = 0; i < pnts.Count; i++)
                    {
                        Point pnt = new Point();
                        pnt.x = pnts[i].x;
                        pnt.y = pnts[i].y;
                        fstShape.InsertPoint(pnt, ref numPoint);
                        numPoint++;
                    }
                }
            }
            return fstShape;
        }

        public void Clear()
        {
            CountyDic = null;
            CountyMergeShape = null;
            CountyGridDic = null;
            CityGridDic = null;
            CityMergeShape = null;
            RoadDic = null;
        }
    }

    #endregion
}
