﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public class Area2PeriodGridLayer : LayerBase
    {
        public Area2PeriodGridLayer()
            : base("村庄两时间段栅格")
        {
        }

        public List<Area2PeriodGrid> AreaGrids { get; set; }
        public Dictionary<AreaKPIDataGroup<GridUnitBase>, Color> GridColorDic { get; set; } = new Dictionary<AreaKPIDataGroup<GridUnitBase>, Color>();
        public int XOffset1{ get; set; } = 0;
        public int YOffset1{ get; set; } = 0;
        public int XOffset2{ get; set; } = 2;
        public int YOffset2 { get; set; } = 2;
        readonly Pen pen1 = new Pen(Brushes.Purple, 2);
        readonly Pen pen2 = new Pen(Brushes.Blue, 2);
        public Color Color1
        {
            get
            {
                return pen1.Color;
            }
            set
            {
                pen1.Color = value;
            }
        }
        public Color Color2
        {
            get
            {
                return pen2.Color;
            }
            set
            {
                pen2.Color = value;
            }
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, System.Drawing.Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            if (AreaGrids == null)
            {
                return;
            }
            Rectangle inflatedRect = new Rectangle(updateRect.X, updateRect.Y, updateRect.Width, updateRect.Height);
            inflatedRect.Inflate(200, 200);
            DbRect dRect;
            this.gisAdapter.FromDisplay(updateRect, out dRect);

            RectangleF? size = null;

            foreach (Area2PeriodGrid areaGrid in this.AreaGrids)
            {
                size = drawAreaGrid(graphics, dRect, size, areaGrid.Grids1, XOffset1, YOffset1, pen1);
                size = drawAreaGrid(graphics, dRect, size, areaGrid.Grids2, XOffset2, YOffset2, pen2);
            }
        }

        private RectangleF? drawAreaGrid(Graphics graphics, DbRect dRect, RectangleF? size, 
            List<AreaKPIDataGroup<GridUnitBase>> areaGrid, int xOffSet, int yOffset, Pen pen)
        {
            if (areaGrid != null)
            {
                foreach (AreaKPIDataGroup<GridUnitBase> grid in areaGrid)
                {
                    if (grid.Area.Bounds.Within(dRect))
                    {
                        Color color;
                        GridColorDic.TryGetValue(grid, out color);
                        drawGrid(graphics, grid.Area, xOffSet, yOffset, pen, color, ref size);
                    }
                }
            }

            return size;
        }

        private void drawGrid(Graphics graphics, GridUnitBase grid, int xOffSet, int yOffset
            , Pen borderPen, Color color, ref RectangleF? size)
        {
            PointF pointLt;
            this.gisAdapter.ToDisplay(new DbPoint(grid.LTLng + (xOffSet * CD.ATOM_SPAN_LONG), grid.LTLat - (yOffset * CD.ATOM_SPAN_LAT))
                , out pointLt);
            if (size == null)
            {
                DbPoint brPoint = new DbPoint(grid.BRLng + (xOffSet * CD.ATOM_SPAN_LONG), grid.BRLat - (yOffset * CD.ATOM_SPAN_LAT));
                PointF pointBr;
                this.gisAdapter.ToDisplay(brPoint, out pointBr);
                size = new RectangleF(pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
            }
            RectangleF rect = new RectangleF(pointLt.X, pointLt.Y, ((RectangleF)size).Width, ((RectangleF)size).Height);
            if (!color.IsEmpty)
            {
                graphics.FillRectangle(new SolidBrush(Color.FromArgb(200, color)), rect);
            }
            graphics.DrawRectangle(borderPen, rect.X, rect.Y, rect.Width, rect.Height);
        }


    }
}
