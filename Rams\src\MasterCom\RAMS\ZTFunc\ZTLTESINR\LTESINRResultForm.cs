﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTLTESINR
{
    public partial class LTESINRResult : MinCloseForm
    {
        public LTESINRResult(MainModel mModel)
            : base(mModel)
        {
            InitializeComponent();
            DisposeWhenClose = true;
            miExportExcel.Click += MiExportExcel_Click;
        }

        private delegate void setGCDataSource(DevExpress.XtraGrid.GridControl gc, DataTable dtTable);
    
        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            if (resultFiles == null)
            {
                return;
            }
            List<NPOIRow> table = new List<NPOIRow>();
            int columnsCount = resultFiles[0].sinrModel.Items.Count;
            NPOIRow titleRow = new NPOIRow();
            titleRow.AddCellValue("运营商");
            titleRow.AddCellValue("电平分布");
            foreach (ItemModel item in resultFiles[0].sinrModel.Items)
            {
                titleRow.AddCellValue(item.Name);
            }
            table.Add(titleRow);
            foreach (ResultModel res in resultFiles)
            {
                NPOIRow row = new NPOIRow();
                NPOIRow totalRow = new NPOIRow();
                row.AddCellValue(res.CarrierName);
                row.AddCellValue(res.sinrModel.Name);
                totalRow.AddCellValue(res.CarrierName);
                totalRow.AddCellValue(res.totalModel.Name);
                for (int i = 0; i < columnsCount; i++)
                {
                    row.AddCellValue(res.sinrModel.Items[i].PointCount);
                    totalRow.AddCellValue(res.totalModel.Items[i].PointCount);
                }
                table.Add(row);
                table.Add(totalRow);
            }
            ExcelNPOIManager.ExportToExcel(new List<List<NPOIRow>>() { table  },
                new List<string>() { resultFiles[0].sinrModel.Name});
                           
           
        }

        private List<ResultModel> resultFiles = null;

        public void FillData(object result)
        {
            resultFiles = new List<ResultModel>();
            resultFiles = result as List<ResultModel>;
            int columnsCount = resultFiles[0].sinrModel.Items.Count ;
            try
            {
                DataTable dtTDResult = new DataTable();
                dtTDResult.Columns.Add("运营商");
                dtTDResult.Columns.Add("电平分布");
                for (int i = 0; i < columnsCount; i++)
                {
                    dtTDResult.Columns.Add(resultFiles[0].sinrModel.Items[i].Name);
                }
                foreach (ResultModel res in resultFiles)
                {
                    List<object> points = new List<object>() { res.CarrierName, res.sinrModel.Name };
                    List<object> all = new List<object>() { res.CarrierName, "SINR总采样点" };
                    for (int i = 0; i < columnsCount; i++)
                    {
                        points.Add(res.sinrModel.Items[i].PointCount);
                        all.Add(res.totalModel.Items[i].PointCount);
                                               
                    }
                    dtTDResult.Rows.Add(points.ToArray());
                    dtTDResult.Rows.Add(all.ToArray());
                    CheckForIllegalCrossThreadCalls = false;
                    SetGCDataSource(gcSINR, dtTDResult);                   
                }
            }
            catch
            {
                //continue
            }
        }

        private void SetGCDataSource(DevExpress.XtraGrid.GridControl gc, DataTable dtTable)
        {
            if (gc.InvokeRequired)
            {
                setGCDataSource set = new setGCDataSource(delegate(DevExpress.XtraGrid.GridControl _gc,
                   DataTable _dtTable)
                {
                    _gc.DataSource = _dtTable;
                });
                this.Invoke(set, gc, dtTable);
            }
            else
            {
                gc.DataSource = dtTable;
            }
        }
    }
}
