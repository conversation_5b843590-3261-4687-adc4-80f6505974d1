﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using System.Reflection;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteAntParaForm : MinCloseForm
    {
        MapForm mapForm;
        public LteAntParaForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            initAngleArgs();
            this.mapForm = MainModel.MainForm.GetMapForm();
        }

        public List<List<NPOIRow>> nrDatasList { get; set; }
        public List<string> sheetNames { get; set; }
        private int iPointIndex = 0;
        private Dictionary<string, ZTLteAntPara.CellParaData> DicCellParaData;

        public void FillData(Dictionary<string, ZTLteAntPara.CellParaData> DicCellParaData)
        {
            try
            {
                this.DicCellParaData = DicCellParaData;
                labNum.Text = DicCellParaData.Count.ToString();
                int iPage = DicCellParaData.Count % 200 > 0 ? DicCellParaData.Count / 200 + 1 : DicCellParaData.Count / 200;
                labPage.Text = iPage.ToString();

                dataGridViewCell.Columns.Clear();
                dataGridViewCell.Rows.Clear();//小区级
                dataGridViewPro.Columns.Clear();
                dataGridViewPro.Rows.Clear();

                int rowCellAt = 0;
                foreach (NPOIRow rowData in nrDatasList[0])
                {
                    if (rowCellAt == 0)
                    {
                        intDataViewColumn(dataGridViewCell, rowData.cellValues);
                        rowCellAt++;
                        continue;
                    }
                    if (rowCellAt > 200)
                        break;
                    initDataRow(dataGridViewCell, rowData);
                    rowCellAt++;
                }
                rowCellAt = 0;
                foreach (NPOIRow rowData in nrDatasList[2])
                {
                    if (rowCellAt == 0)
                    {
                        intDataViewColumn(dataGridViewPro, rowData.cellValues);
                        rowCellAt++;
                        continue;
                    }
                    if (rowCellAt > 200)
                        break;
                    initDataRow(dataGridViewPro, rowData);
                    rowCellAt++;
                }
                txtPage.Text = "1";
            }
            catch
            {
                //continue
            }
        }

        private void intDataViewColumn(DataGridView dataGridView,List<object> objs)
        {
            dataGridView.Columns.Clear();
            int idx = 1;
            foreach (object obj in objs)
            {
                dataGridView.Columns.Add(idx++.ToString(), obj.ToString());
            }
        }

        /// <summary>
        /// 按小区模糊查找，前200个小区
        /// </summary>
        private void FillData(string strCellName)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int rowCellAt = 0;
            foreach (NPOIRow rowData in nrDatasList[0])
            {
                if (nrDatasList[0][0].cellValues[0].ToString() == rowData.cellValues[0].ToString())
                    continue;

                if (strCellName != "" && rowData.cellValues[3].ToString().IndexOf(strCellName) < 0)
                    continue;

                if (rowCellAt >= 200)
                    break;
                initDataRow(dataGridViewCell, rowData);
                rowCellAt++;
            }
        }

        /// <summary>
        /// 按页数查找
        /// </summary>
        private void FillData(int iPage)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int iCount = -1;
            int rowCellAt = 0;
            foreach (NPOIRow rowData in nrDatasList[0])
            {
                if (nrDatasList[0][0].cellValues[0].ToString() == rowData.cellValues[0].ToString())
                    continue;

                iCount++;
                if (iCount / 200 != iPage)
                    continue;
                initDataRow(dataGridViewCell, rowData);
                rowCellAt++;
            }
        }

        private void initDataRow(DataGridView datatGridView ,NPOIRow nop)
        {
            DataGridViewRow row = new DataGridViewRow();
            row.Tag = nop.cellValues[3];//小区名称
            foreach (object obj in nop.cellValues)
            {
                DataGridViewTextBoxCell boxcell = new DataGridViewTextBoxCell();
                boxcell.Value = obj.ToString();
                row.Cells.Add(boxcell);
            }
            datatGridView.Rows.Add(row);
        }

        ZTLteAntPara.CellParaData data;
        /// <summary>
        /// 按平滑线绘雷达图
        /// </summary>
        private void drawAntRadarSeries(double[] modelSeriesValues, double[] dtTestValues, double[] scanTestValues)
        {
            chartControl2.Series.Clear();
            int iNum = 0;

            #region 扫频测试数据
            iNum = setScanTest(scanTestValues, iNum);
            #endregion

            #region 路测测试数据
            iNum = setDtTest(dtTestValues, iNum);
            #endregion

            #region 权值模型数据
            setModelSeries(modelSeriesValues, iNum);
            #endregion

            int iMaxValue = -19;
            int iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(modelSeriesValues, ref iMaxValue, ref iMinValue);
            ZTAntFuncHelper.getMaxAndMinValue(dtTestValues, ref iMaxValue, ref iMinValue);
            ZTAntFuncHelper.getMaxAndMinValue(scanTestValues, ref iMaxValue, ref iMinValue);
            iMinValue = -20;
            ((RadarDiagram)chartControl2.Diagram).AxisY.Range.MinValue = iMinValue;
            ((RadarDiagram)chartControl2.Diagram).AxisY.Range.MaxValue = iMaxValue;

            ((RadarDiagram)chartControl2.Diagram).AxisX.GridSpacing = 20;

            ((RadarDiagram)chartControl2.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
            ((RadarDiagram)chartControl2.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;

            chartControl2.Focus();
        }

        private int setScanTest(double[] scanTestValues, int iNum)
        {
            if (scanTestValues != null && scanTestValues.Length == 360)
            {
                Series seriesScan = new Series();
                seriesScan.ShowInLegend = true;
                seriesScan.LegendText = "扫频RSRP";
                seriesScan.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesScanView = new RadarLineSeriesView();
                lineSeriesScanView.Color = Color.Blue;
                lineSeriesScanView.LineMarkerOptions.Size = 2;

                seriesScan.View = lineSeriesScanView;
                seriesScan.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                seriesScan.Label.Visible = false;

                for (int i = 359; i >= 0; i--)
                {
                    seriesScan.Points.Add(new SeriesPoint(i.ToString(), Math.Round(scanTestValues[i], 2)));
                }
                chartControl2.Series.Insert(iNum, seriesScan);
                iNum++;
            }

            return iNum;
        }

        private int setDtTest(double[] dtTestValues, int iNum)
        {
            if (dtTestValues != null && dtTestValues.Length == 360)
            {
                Series seriesDT = new Series();
                seriesDT.ShowInLegend = true;
                seriesDT.LegendText = "路测RSRP";
                seriesDT.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesDTView = new RadarLineSeriesView();
                lineSeriesDTView.Color = Color.DarkViolet;
                lineSeriesDTView.LineMarkerOptions.Size = 2;

                seriesDT.View = lineSeriesDTView;
                seriesDT.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                seriesDT.Label.Visible = false;

                for (int i = 359; i >= 0; i--)
                {
                    seriesDT.Points.Add(new SeriesPoint(i.ToString(), Math.Round(dtTestValues[i], 2)));
                }
                chartControl2.Series.Insert(iNum, seriesDT);
                iNum++;
            }

            return iNum;
        }

        private void setModelSeries(double[] modelSeriesValues, int iNum)
        {
            if (modelSeriesValues != null && modelSeriesValues.Length == 180)
            {
                Series seriesModel = new Series();
                seriesModel.ShowInLegend = true;
                seriesModel.LegendText = "权值仿真";
                seriesModel.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesView2 = new RadarLineSeriesView();
                lineSeriesView2.Color = Color.Red;
                lineSeriesView2.LineMarkerOptions.Size = 2;

                seriesModel.View = lineSeriesView2;
                seriesModel.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                seriesModel.Label.Visible = false;

                int iInfoShow = 1;
                if (data.antPara.iStatus == 1)
                {
                    if (data.antCfg.strBtsType.Contains("E") || data.antCfg.strBtsType.Contains("A"))
                        iInfoShow = 0;
                }
                else
                    iInfoShow = 0;

                addSeriesModelPoints(modelSeriesValues, seriesModel, iInfoShow);
                chartControl2.Series.Insert(iNum, seriesModel);
            }
        }

        private static void addSeriesModelPoints(double[] modelSeriesValues, Series seriesModel, int iInfoShow)
        {
            if (iInfoShow == 1)
            {
                for (int i = 0; i < 360; i++)
                {
                    double tmpValue = -120;
                    if (i >= 270)
                        tmpValue = modelSeriesValues[i - 270];
                    else if (i < 90)
                        tmpValue = modelSeriesValues[i + 90];

                    seriesModel.Points.Add(new SeriesPoint(i.ToString(), Math.Round(tmpValue, 2)));
                }
            }
        }

        /// <summary>
        /// 按权值理想覆盖图
        /// </summary>
        private void DrawWeightTableSeries(double[] modelSeries)
        {
            chartControl5.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 180; i++)
            {
                series.Points.Add(new SeriesPoint((-90 + i).ToString(), Math.Round(modelSeries[i], 2)));
            }
            iPointIndex = 0;
            chartControl5.Series.Insert(0, series);

            ((XYDiagram)chartControl5.Diagram).AxisX.Range.MinValue = -90;
            ((XYDiagram)chartControl5.Diagram).AxisX.Range.MaxValue = 90;

            chartControl5.Focus();
        }

        /// <summary>
        /// 按MR二维数据绘雷达图
        /// </summary>
        private void drawCellMRRadarSeries()
        {
            chartCtrlMRSample.Series.Clear();

            int idx = 0;
            Dictionary<int, List<string>> colorDic = getMRDataColorDic();
            int iMaxValue = 0;
            for (int c = 0; c <= 10;c++ )
            {
                if (!colorDic.ContainsKey(c))
                    continue;

                AntLegend antLegend = ZTAntFuncHelper.GetMRDataLegend(c);
                Series series = new Series();
                series.ShowInLegend = true;
                series.LegendText = antLegend.strLegend;
                series.PointOptions.PointView = PointView.Values;

                RadarPointSeriesView pointSeriesView = new RadarPointSeriesView();
                pointSeriesView.Color = antLegend.colorType;
                pointSeriesView.PointMarkerOptions.Size = 3;

                series.View = pointSeriesView;
                series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                series.Label.Visible = false;

                foreach (string strInfo in colorDic[c])
                {
                    string[] str = strInfo.Split('_');
                    int iTmpValue = 0;
                    int.TryParse(str[1], out iTmpValue);
                    if (iTmpValue > iMaxValue)
                        iMaxValue = iTmpValue;
                    series.Points.Add(new SeriesPoint(str[0], str[1]));
                }

                chartCtrlMRSample.Series.Insert(idx, series);
                idx++;
            }

            #region 增加天线方位角连线
            RadarLineSeriesView lineSeriesCellView = new RadarLineSeriesView();
            lineSeriesCellView.Color = Color.Red;
            lineSeriesCellView.LineMarkerOptions.Size = 2;

            Series seriesCell = new Series();
            seriesCell.ShowInLegend = true;
            seriesCell.LegendText = "工参";
            seriesCell.PointOptions.PointView = PointView.Values;

            seriesCell.View = lineSeriesCellView;
            seriesCell.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesCell.Label.Visible = false;
            seriesCell.Points.Add(new SeriesPoint(data.antCfg.方向角, 0));
            seriesCell.Points.Add(new SeriesPoint(data.antCfg.方向角, iMaxValue));
            chartCtrlMRSample.Series.Insert(idx, seriesCell);
            idx++;

            RadarLineSeriesView lineSeriesMRView = new RadarLineSeriesView();
            lineSeriesMRView.Color = Color.DarkBlue;
            lineSeriesMRView.LineMarkerOptions.Size = 2;

            Series seriesMR = new Series();
            seriesMR.ShowInLegend = true;
            seriesMR.LegendText = "MR";
            seriesMR.PointOptions.PointView = PointView.Values;

            seriesMR.View = lineSeriesMRView;
            seriesMR.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            seriesMR.Label.Visible = false;
            seriesMR.Points.Add(new SeriesPoint(data.mrCellParaData.iAntMaxDir, 0));
            seriesMR.Points.Add(new SeriesPoint(data.mrCellParaData.iAntMaxDir, iMaxValue));
            chartCtrlMRSample.Series.Insert(idx, seriesMR);

            #endregion

            if (colorDic.Count > 0)
            {
                ((RadarDiagram)chartCtrlMRSample.Diagram).AxisY.Range.MinValue = -1;
                ((RadarDiagram)chartCtrlMRSample.Diagram).AxisY.Range.MaxValue = iMaxValue + 300;

                ((RadarDiagram)chartCtrlMRSample.Diagram).AxisX.GridSpacing = 20;

                ((RadarDiagram)chartCtrlMRSample.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
                ((RadarDiagram)chartCtrlMRSample.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;
            }
            else
            {
                Series series = new Series();
                series.ShowInLegend = false;
                series.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
                lineSeriesView.Color = Color.Blue;
                lineSeriesView.LineMarkerOptions.Size = 2;

                series.View = lineSeriesView;
                series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                series.Label.Visible = false;
                chartCtrlMRSample.Series.Insert(0, series);
            }
            chartCtrlMRSample.Focus();
        }

        /// <summary>
        /// 获取MR二维数据
        /// </summary>
        private Dictionary<int, List<string>> getMRDataColorDic()
        {
            Dictionary<int, List<string>> colorDic = new Dictionary<int, List<string>>();
            if (data != null)
            {
                for (int i = 0; i < 44; i++)
                {
                    for (int j = 0; j < 72; j++)
                    {
                        addColorDic(colorDic, i, j);
                    }
                }
            }
            
            return colorDic;
        }

        private void addColorDic(Dictionary<int, List<string>> colorDic, int i, int j)
        {
            int n = j * 5;
            if (data.mrCellParaData.cellMrData.AnaRttdAoa[i, j] > 0)
            {
                int c = ZTAntFuncHelper.GetMRDataLevel(data.mrCellParaData.cellMrData.AnaRttdAoa[i, j]);
                int iDist = ZTAntFuncHelper.calcDistByLteMrTa(i);
                string strDesc = string.Format("{0}_{1}", n.ToString(), iDist);
                if (colorDic.ContainsKey(c))
                {
                    colorDic[c].Add(strDesc);
                }
                else
                {
                    List<string> descList = new List<string>();
                    descList.Add(strDesc);
                    colorDic.Add(c, descList);
                }
            }
        }

        /// <summary>
        /// 按MR模拟覆盖图
        /// </summary>
        private void drawCellCoverRadarSeries(int[] seriesValues)
        {
            chartCtrlMRLine.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
            lineSeriesView.Color = Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;

            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 72; i++)
            {
                int j = i * 5;
                series.Points.Add(new SeriesPoint(j, seriesValues[i]));
            }
            chartCtrlMRLine.Series.Insert(0, series);

            ((RadarDiagram)chartCtrlMRLine.Diagram).AxisY.Range.MinValue = -1;
            ((RadarDiagram)chartCtrlMRLine.Diagram).AxisY.Range.MaxValue = getMaxValue(seriesValues);
            ((RadarDiagram)chartCtrlMRLine.Diagram).AxisX.GridSpacing = 20;
            ((RadarDiagram)chartCtrlMRLine.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
            ((RadarDiagram)chartCtrlMRLine.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;

            chartCtrlMRLine.Focus();
        }

        /// <summary>
        /// 获取数组中的最大及最小值
        /// </summary>
        public int getMaxValue(int[] seriesValues)
        {
            int iMaxValue = 1;
            foreach (double tmpValue in seriesValues)
            {
                if (tmpValue > iMaxValue)
                {
                    iMaxValue = (int)tmpValue + 1;
                }
            }
            return iMaxValue;
        }

        /// <summary>
        /// 按权值配置画图表
        /// </summary>
        private void DrawWeightCfgSeries()
        {
            chartControl3.Series.Clear();

            //幅度
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.5;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;

            series.Points.Add(new SeriesPoint("1", Math.Round(data.antPara.drangeport1, 2)));
            series.Points.Add(new SeriesPoint("2", Math.Round(data.antPara.drangeport2, 2)));
            series.Points.Add(new SeriesPoint("3", Math.Round(data.antPara.drangeport3, 2)));
            series.Points.Add(new SeriesPoint("4", Math.Round(data.antPara.drangeport4, 2)));
            series.Points.Add(new SeriesPoint("5", Math.Round(data.antPara.drangeport5, 2)));
            series.Points.Add(new SeriesPoint("6", Math.Round(data.antPara.drangeport6, 2)));
            series.Points.Add(new SeriesPoint("7", Math.Round(data.antPara.drangeport7, 2)));
            series.Points.Add(new SeriesPoint("8", Math.Round(data.antPara.drangeport8, 2)));
            chartControl3.Series.Insert(0, series);

            ((XYDiagram)chartControl3.Diagram).AxisY.Range.MinValue = -180;
            ((XYDiagram)chartControl3.Diagram).AxisY.Range.MaxValue = 180;

            //相位
            Series series2 = new Series();
            series2.ShowInLegend = false;
            series2.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView2 = new SideBySideBarSeriesView();
            sideBySideBarSeriesView2.BarWidth = 0.5;
            sideBySideBarSeriesView2.Border.Visible = false;
            series2.View = sideBySideBarSeriesView2;

            series2.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;

            series2.Points.Add(new SeriesPoint("1", Math.Round(data.antPara.dphaseport1, 2)));
            series2.Points.Add(new SeriesPoint("2", Math.Round(data.antPara.dphaseport2, 2)));
            series2.Points.Add(new SeriesPoint("3", Math.Round(data.antPara.dphaseport3, 2)));
            series2.Points.Add(new SeriesPoint("4", Math.Round(data.antPara.dphaseport4, 2)));
            series2.Points.Add(new SeriesPoint("5", Math.Round(data.antPara.dphaseport5, 2)));
            series2.Points.Add(new SeriesPoint("6", Math.Round(data.antPara.dphaseport6, 2)));
            series2.Points.Add(new SeriesPoint("7", Math.Round(data.antPara.dphaseport7, 2)));
            series2.Points.Add(new SeriesPoint("8", Math.Round(data.antPara.dphaseport8, 2)));
            chartControl3.Series.Insert(1, series2);
            ((SideBySideBarSeriesView)series.View).AxisY = ((XYDiagram)chartControl3.Diagram).SecondaryAxesY[0];

            chartControl3.Focus();
        }

        ///<summary>
        ///生成0~360度的角度列
        ///</summary>
        public void initAngleArgs()
        {
            for (int i = -179; i < 181; i++)
            {
                DataGridViewTextBoxColumn angelCol = new DataGridViewTextBoxColumn();
                angelCol.HeaderText = i + "°";
                angelCol.Width = 63;
                dataGridViewAngle.Columns.Add(angelCol);
            }

            cbbxSeries1.Items.Add("RSRP");
            cbbxSeries1.Items.Add("平滑RSRP");
            cbbxSeries1.Items.Add("SINR");
            cbbxSeries1.Items.Add("通信距离");
            cbbxSeries1.SelectedIndex = 0;

            cbbxSeries2.Items.Add("RSRP");
            cbbxSeries2.Items.Add("平滑RSRP");
            cbbxSeries2.Items.Add("SINR");
            cbbxSeries2.Items.Add("通信距离");
            cbbxSeries2.SelectedIndex = 1;

            Series series = chartControl1.Series[0];
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical; //设置x轴有固定间距
            SeriesPoint pt;
            pt = new SeriesPoint(-150, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-90, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-60, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-30, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-15, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(0, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(15, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(30, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(60, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(90, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(150, 50);
            series.Points.Add(pt);

            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;
        }

        /// <summary>
        /// "天线辐射波形重建"中显示表格信息
        /// </summary>
        public void setAngelTable(ZTLteAntPara.CellParaData data)
        {
            dataGridViewAngle.Rows.Clear();
            int rowAt = 0;
            dataGridViewAngle.Rows.Add(1);
            int colAt = 0;

            int[] rsrpArray = data.ciItem.rsrpArray;
            double[] newRsrpArray = data.ciItem.newRsrpArray;
            int[] sinrArray = data.ciItem.sinrArray;
            double[] sampleDistArray = data.ciItem.sampArray;
            int[] sampleNumArray = data.ciItem.sampNumArray;

            int[] dtrsrpArray = data.ciopItem.citem.rsrpArray;
            double[] dtnewRsrpArray = data.ciopItem.citem.newRsrpArray;
            int[] dtsinrArray = data.ciopItem.citem.sinrArray;
            double[] dtsampleDistArray = data.ciopItem.citem.sampArray;
            int[] dtsampleNumArray = data.ciopItem.citem.sampNumArray;

            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "RSRP(Scan)";
            setCellValue(rowAt, colAt, rsrpArray, sampleNumArray, -140, 2);
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "平滑RSRP(Scan)";
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = Math.Round(newRsrpArray[i], 2);
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = Math.Round(newRsrpArray[i], 2);
            }
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "SINR(Scan)";
            setCellValue(rowAt, colAt, sinrArray, sampleNumArray, -25, 2);
            dataGridViewAngle.Rows.Add(1);
            
            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "通信距离(Scan)";
            setCellValue(rowAt, colAt, sampleDistArray, sampleNumArray, 0, 2);
            dataGridViewAngle.Rows.Add(1);

            //路测角度信息
            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "RSRP(DT)";
            setCellValue(rowAt, colAt, dtrsrpArray, dtsampleNumArray, -140, 2);
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "平滑RSRP(DT)";
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = Math.Round(dtnewRsrpArray[i], 2);
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = Math.Round(dtnewRsrpArray[i], 2);
            }
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "SINR(DT)";
            setCellValue(rowAt, colAt, dtsinrArray, dtsampleNumArray, -25, 2);
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "通信距离(DT)";
            setCellValue(rowAt, colAt, dtsampleDistArray, dtsampleNumArray, 0, 2);
        }

        private void setCellValue(int rowAt, int colAt, double[] sumArray, int[] numArray, int def, int Digits)
        {
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (Math.Round(numArray[i] == 0 ? def : sumArray[i] / numArray[i], Digits)).ToString();
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (Math.Round(numArray[i] == 0 ? def : sumArray[i] / numArray[i], Digits)).ToString();
            }
        }

        private void setCellValue(int rowAt, int colAt, int[] sumArray, int[] numArray, int def, int Digits)
        {
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (Math.Round(numArray[i] == 0 ? def : 1.0 * sumArray[i] / numArray[i], Digits)).ToString();
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (Math.Round(numArray[i] == 0 ? def : 1.0 * sumArray[i] / numArray[i], Digits)).ToString();
            }
        }

        /// <summary>
        /// 按距离等间线绘图
        /// </summary>
        private void drawVertSplineSeries()
        {
            double[] tmpAarry = new double[200];
            chartControl3.Series.Clear();
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SplineSeriesView lineSeriesView = new SplineSeriesView();
            lineSeriesView.Color = Color.DarkCyan;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;

            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 200; i++)
            {
                double tmpValue = -140;
                if (data.ciItem.antVertDic.ContainsKey(i))
                    tmpValue = Math.Round(data.ciItem.antVertDic[i].IRsrp * 1.0 / data.ciItem.antVertDic[i].ISampNum, 2);

                series.Points.Add(new SeriesPoint(i.ToString(), tmpValue));
                tmpAarry[i] = tmpValue;
            }
            chartControl3.Series.Insert(0, series);

            int iMaxValue = -50;
            int iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(tmpAarry, ref iMaxValue, ref iMinValue);
            ((XYDiagram)chartControl3.Diagram).AxisY.Range.MinValue = iMinValue;
            ((XYDiagram)chartControl3.Diagram).AxisY.Range.MaxValue = iMaxValue;
            ((SplineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl3.Focus();
        }

        /// <summary>
        /// 调整Y轴，按绝对值大小描写刻度
        /// </summary>
        private void checkReverse()
        {
            if ((double)(((XYDiagram)chartControl1.Diagram).AxisY.Range.MaxValue) > 0)
            {
                ((XYDiagram)chartControl1.Diagram).AxisY.Reverse = false;
            }
            else
            {
                ((XYDiagram)chartControl1.Diagram).AxisY.Reverse = true;
            }

            if ((double)(((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Range.MaxValue) > 0)
            {
                ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Reverse = false;
            }
            else
            {
                ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Reverse = true;
            }
        }

        /// <summary>
        /// 按参数值1画图表
        /// </summary>
        private void DrawTable1Series(int[] seriesValues)
        {
            int count = 0;
            SeriesPoint pt;
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.2;
            sideBySideBarSeriesView.Color = Color.Orange;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            int[] sampleNumArray = data.ciItem.sampNumArray;
            for (int i = 181; i < 360; i++)
            {
                string arg = (-179 + count).ToString();
                double value = Math.Round(sampleNumArray[i] == 0 ? 0 : seriesValues[i] * 1.0 / sampleNumArray[i], 2);
                pt = new SeriesPoint(arg, value);
                series.Points.Add(pt);
                count++;
            }
            for (int i = 1; i < 181; i++)
            {
                string arg = i.ToString();
                double value = Math.Round(sampleNumArray[i] == 0 ? 0 : seriesValues[i] * 1.0 / sampleNumArray[i], 2);
                pt = new SeriesPoint(arg, value);
                series.Points.Add(pt);
            }

            chartControl1.Series.Insert(0, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;

            ((XYDiagram)chartControl1.Diagram).AxisY.Range.Auto = true;
            chartControl1.Focus();
        }

        /// <summary>
        /// 按参数值1画图表
        /// </summary>
        private void DrawTable1Series(double[] seriesValues)
        {
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.2;
            sideBySideBarSeriesView.Color = Color.Orange;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            int[] sampleNumArray = data.ciItem.sampNumArray;

            int count = 0;
            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(sampleNumArray[i] == 0 ? 0 : seriesValues[i] / sampleNumArray[i], 2)));
            }
            for (int i = 1; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(sampleNumArray[i] == 0 ? 0 : seriesValues[i] / sampleNumArray[i], 2)));
            }

            chartControl1.Series.Insert(0, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;


            ((XYDiagram)chartControl1.Diagram).AxisY.Range.Auto = true;
            chartControl1.Focus();
        }

        /// <summary>
        /// 按参数值1画图表
        /// </summary>
        private void DrawTable1SeriesNotCalc(double[] seriesValues)
        {
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.2;
            sideBySideBarSeriesView.Color = Color.Orange;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            int count = 0;
            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(seriesValues[i], 2)));
            }
            for (int i = 1; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(seriesValues[i], 2)));
            }

            chartControl1.Series.Insert(0, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;


            ((XYDiagram)chartControl1.Diagram).AxisY.Range.Auto = true;
            chartControl1.Focus();
        }

        /// <summary>
        /// 按参数值2画图表
        /// </summary>
        private void drawTable2Series(int[] seriesValues)
        {
            int count = 0;
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            LineSeriesView lineSeriesView = new LineSeriesView();
            lineSeriesView.Color = System.Drawing.Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            ((LineSeriesView)series.View).AxisY = ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0];
            int[] sampleNumArray = data.ciItem.sampNumArray;

            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(sampleNumArray[i] == 0 ? 0 : seriesValues[i] * 1.0 / sampleNumArray[i], 2)));
            }
            for (int i = 0; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(sampleNumArray[i] == 0 ? 0 : seriesValues[i] * 1.0 / sampleNumArray[i], 2)));
            }

            chartControl1.Series.Insert(1, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;

            ((LineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl1.Focus();
        }

        /// <summary>
        /// 按参数值2画图表
        /// </summary>
        private void drawTable2Series(double[] seriesValues)
        {
            int count = 0;
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            LineSeriesView lineSeriesView = new LineSeriesView();
            lineSeriesView.Color = System.Drawing.Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            ((LineSeriesView)series.View).AxisY = ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0];
            int[] sampleNumArray = data.ciItem.sampNumArray;

            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(sampleNumArray[i] == 0 ? 0 : seriesValues[i] / sampleNumArray[i], 2)));
            }
            for (int i = 0; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(sampleNumArray[i] == 0 ? 0 : seriesValues[i] / sampleNumArray[i], 2)));
            }

            chartControl1.Series.Insert(1, series);

            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;
            ((LineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl1.Focus();
        }

        /// <summary>
        /// 按参数值2画图表
        /// </summary>
        private void drawTable2SeriesNotCalc(double[] seriesValues)
        {
            int count = 0;
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            LineSeriesView lineSeriesView = new LineSeriesView();
            lineSeriesView.Color = System.Drawing.Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            ((LineSeriesView)series.View).AxisY = ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0];

            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(seriesValues[i], 2)));
            }
            for (int i = 0; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(seriesValues[i], 2)));
            }

            chartControl1.Series.Insert(1, series);

            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;
            ((LineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl1.Focus();
        }

        private void cbbxSeries1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (data != null)
            {
                UpdateTableSeries();
            }
        }

        private void cbbxSeries2_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (data != null)
            {
                UpdateTableSeries();
            }
        }

        /// <summary>
        /// 更新表数据
        /// </summary>
        private void UpdateTableSeries()
        {
            chartControl1.Series.Clear();
            Cursor.Current = Cursors.WaitCursor;
            #region 图例1内容
            switch (this.cbbxSeries1.Text)
            {
                case "RSRP":
                    {
                        DrawTable1Series(data.ciItem.rsrpArray);
                    }
                    break;
                case "平滑RSRP":
                    {
                        DrawTable1SeriesNotCalc(data.ciItem.newRsrpArray);
                    }
                    break;
                case "SINR":
                    {
                        DrawTable1Series(data.ciItem.sinrArray);
                    }
                    break;
                case "通信距离":
                    {
                        DrawTable1Series(data.ciItem.sampArray);
                    }
                    break;
                default:
                    break;
            }
            #endregion

            #region 图例2内容
            switch (this.cbbxSeries2.Text)
            {
                case "RSRP":
                    {
                        drawTable2Series(data.ciopItem.citem.rsrpArray);
                    }
                    break;
                case "平滑RSRP":
                    {
                        drawTable2SeriesNotCalc(data.ciopItem.citem.newRsrpArray);
                    }
                    break;
                case "SINR":
                    {
                        drawTable2Series(data.ciopItem.citem.sinrArray);
                    }
                    break;
                case "通信距离":
                    {
                        drawTable2Series(data.ciopItem.citem.sampArray);
                    }
                    break;
                default:
                    break;
            }
            #endregion
            checkReverse();
            Cursor.Current = Cursors.Default;
        }

        private void miShwoChart_Click(object sender, EventArgs e)
        {
            string cellname = dataGridViewCell.SelectedRows[0].Tag as string;
            if (!DicCellParaData.TryGetValue(cellname, out data))
                return;
            if (data != null)
            {
                groupControl3.Text = string.Format("天线全向分析({0})", data.cellname);
                this.xtraTabControl1.SelectedTabPageIndex = 1;
                if (data.ciItem.antAngleDic.Count == 0)
                    getCellScanInfoByEci();
                if (data.ciopItem.citem.antAngleDic.Count == 0)
                    getCellDTInfoByEci();
                setAngelTable(data);
                CalcAntDetailValue();

                cbbxSeries1_SelectedIndexChanged(null, null);
                cbbxSeries2_SelectedIndexChanged(null, null);

                drawAntRadarSeries(data.ciItem.modelMaxArray, data.ciopItem.citem.newRsrpArray, data.ciItem.newRsrpArray);//俯视面雷达图
                drawVertSplineSeries();//垂直面覆盖图
                DrawWeightTableSeries(data.ciItem.modelMaxArray);//权值理想覆盖图
                DrawWeightCfgSeries();//按权值配置画图表
            }
        }

        /// <summary>
        /// 按ECI取扫频数据
        /// </summary>
        public void getCellScanInfoByEci()
        {
            int iNewEci = (data.iEci / 256) * 256 + ((data.iEci % 256) % 10);
            Dictionary<int,int> cellEciDic =new Dictionary<int,int>();
            Dictionary<int, ZTLteScanAntenna.CellInfoItem> cellScanInfoDic;
            DiyQueryCellAngleResult scanQuery = new DiyQueryCellAngleResult(MainModel, cellEciDic);
            scanQuery.SetCondition(data.iStime, data.iEtime, data.iEci);
            scanQuery.Query();
            cellScanInfoDic = scanQuery.cellInfoDic;
            if (cellScanInfoDic.ContainsKey(data.iEci))
                data.ciItem = cellScanInfoDic[data.iEci];
            else if (cellScanInfoDic.ContainsKey(iNewEci))
                data.ciItem = cellScanInfoDic[iNewEci];
        }

        /// <summary>
        /// 按ECI取路测数据
        /// </summary>
        public void getCellDTInfoByEci()
        {
            int iNewEci = (data.iEci / 256) * 256 + ((data.iEci % 256) % 10);
            Dictionary<int, int> cellEciDic = new Dictionary<int, int>();
            Dictionary<int, ZTLteAntenna.CellInfoItem> cellDTInfoDic;
            DIYQueryCellDTAngleResult dtQuery = new DIYQueryCellDTAngleResult(MainModel, cellEciDic);
            dtQuery.SetCondition(data.iStime, data.iEtime, data.iEci);
            dtQuery.Query();
            cellDTInfoDic = dtQuery.cellInfoDic;

            if (cellDTInfoDic.ContainsKey(data.iEci))
                data.ciopItem.citem = cellDTInfoDic[data.iEci];
            else if (cellDTInfoDic.ContainsKey(iNewEci))
                data.ciopItem.citem = cellDTInfoDic[iNewEci];
        }

        private void miShwoMRChart_Click(object sender, EventArgs e)
        {
            string cellname = dataGridViewCell.SelectedRows[0].Tag as string;
            if (!DicCellParaData.TryGetValue(cellname, out data))
                return;
            if (data != null)
            {
                groupCtrlMR.Text = string.Format("MR测量项数据图表({0})", data.cellname);
                this.xtraTabControl1.SelectedTabPageIndex = 4;

                drawCellMRRadarSeries();
                int[] dirArray = new int[72];
                for (int i = 0; i < 72; i++)
                {
                    if (data.mrCellParaData.dirSampleDic.ContainsKey(i))
                        dirArray[i] = data.mrCellParaData.dirSampleDic[i];
                }
                drawCellCoverRadarSeries(dirArray);
            }
        }

        /// <summary>
        /// MR模拟覆盖图GIS呈现
        /// </summary>
        private void miShwoMRGis_Click(object sender, EventArgs e)
        {
            string cellname = dataGridViewCell.SelectedRows[0].Tag as string;
            if (!DicCellParaData.TryGetValue(cellname, out data))
                return;

            if (data != null)
            {
                Dictionary<int, List<string>> colorDic = getMRDataColorDic();
                Dictionary<int, List<LongLat>> gisSampleDic = new Dictionary<int, List<LongLat>>();
                foreach (int c in colorDic.Keys)
                {
                    List<LongLat> longLatList = new List<LongLat>();
                    foreach (string strInfo in colorDic[c])
                    {
                        string[] str = strInfo.Split('_');
                        int iAngle = 0;
                        int iDist = 0;
                        int.TryParse(str[0],out iAngle);
                        int.TryParse(str[1],out iDist);
                        LongLat cellLongLat = new LongLat();
                        cellLongLat.fLongitude = (float)data.cellLongitude;
                        cellLongLat.fLatitude = (float)data.cellLatitude;
                        LongLat tmpLongLat = ZTAntFuncHelper.calcPointX(iAngle, iDist, cellLongLat);
                        longLatList.Add(tmpLongLat);
                    }
                    gisSampleDic.Add(c, longLatList);
                }

                AntPointLayer antLayer = mapForm.GetLayerBase(typeof(AntPointLayer)) as AntPointLayer;
                if (antLayer != null)
                {
                    MainModel.MainForm.GetMapForm().GoToView(data.cellLongitude, data.cellLatitude);
                    antLayer.iFunc = 2;
                    antLayer.gisSampleDic = gisSampleDic;
                    antLayer.Invalidate();
                }
            }
        }

        private void miShowSimulation_Click(object sender, EventArgs e)
        {
            string cellname = dataGridViewCell.SelectedRows[0].Tag as string;
            if (!DicCellParaData.TryGetValue(cellname, out data))
                return;
            if (data != null)
            {
                if (data.cellLongitude == 0 || data.cellLatitude == 0)
                    return;
                CalcAntDetailValue();
                if (data.ciItem.antAngleDic.Count == 0)
                    getCellScanInfoByEci();
                if (data.ciopItem.citem.antAngleDic.Count == 0)
                    getCellDTInfoByEci();
                ZTAntennaBase.SimulationPoints simulationPoints = new ZTAntennaBase.SimulationPoints();
                simulationPoints.cellLongitude = data.cellLongitude;
                simulationPoints.cellLatitude = data.cellLatitude;
                simulationPoints.strNet = "LTE";
                simulationPoints.longLatModelList.AddRange(data.longLatModelList);
                simulationPoints.longLatTestList.AddRange(data.longLatScanList);
                simulationPoints.longLatMRList.AddRange(data.longLatDTlList);

                MainModel.SimulationPoints = simulationPoints;
                MainModel.SelectedLTECell = CellManager.GetInstance().GetLTECellLatest(data.cellname);
                MainModel.MainForm.GetMapForm().GoToView(data.cellLongitude, data.cellLatitude);
                MainModel.FireCellDrawInfoChanged(this);
            }
        }
        private void removeUselessData(List<List<NPOIRow>> nrDatasList)
        {
            try
            {
                List<object> obj = new List<object>();
                obj.AddRange(new object[] { -140, -20.0, -25, 0.0 });
                List<NPOIRow> rowDataLst = new List<NPOIRow>();
                List<NPOIRow> tmpdata = nrDatasList[nrDatasList.Count - 1];
                bool isUseData = false;
                foreach (NPOIRow nor in tmpdata)
                {
                    isUseData = false;
                    for (int i = 0; i < nor.cellValues.Count;i++ )
                    {
                        if (i < 2)
                            continue;
                        if (!obj.Contains(nor.cellValues[i]))
                        {
                            isUseData = true;
                            break;
                        }
                    }
                    if (isUseData)
                        rowDataLst.Add(nor);
                }
                nrDatasList.RemoveAt(nrDatasList.Count - 1);
                nrDatasList.Add(rowDataLst);
            }
            catch
            {
                //continue
            }
        }

        private void miExportWholeExcel_Click(object sender, EventArgs e)
        {
            removeUselessData(nrDatasList);
            doExport(nrDatasList, sheetNames);
        }

        private void 导出CSVToolStripMenuItem_Click(object sender, EventArgs e)
        {
            removeUselessData(nrDatasList);
            ZTAntFuncHelper.OutputCsvFile(nrDatasList, sheetNames);
        }
        public void doExport(List<List<NPOIRow>> nrDatasList, List<string> strSheetName)
        {
            string fileName;
            if (!ExportResultSecurityHelper.GetExportPermit(FileSimpleTypeHelper.Excel, out fileName))
            {
                return;
            }
            Microsoft.Office.Interop.Excel.Application excel = new Microsoft.Office.Interop.Excel.Application();  //Execl的操作类
            Microsoft.Office.Interop.Excel.Workbook bookDest = excel.Workbooks.Add(Missing.Value);
            Microsoft.Office.Interop.Excel.Worksheet sheetAngle = bookDest.Worksheets.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value) as Microsoft.Office.Interop.Excel.Worksheet;//给工作薄添加一个Sheet   
            sheetAngle.Name = strSheetName[1];
            Microsoft.Office.Interop.Excel.Worksheet sheetDest = bookDest.Worksheets.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value) as Microsoft.Office.Interop.Excel.Worksheet;//给工作薄添加一个Sheet   
            sheetDest.Name = strSheetName[0];
            
            for (int i = bookDest.Worksheets.Count; i > 1; i--)
            {
                Microsoft.Office.Interop.Excel.Worksheet wt = (Microsoft.Office.Interop.Excel.Worksheet)bookDest.Worksheets[i];
                if (!strSheetName.Contains(wt.Name))
                    wt.Delete();
            }
            try
            {
                Microsoft.Office.Interop.Excel.Range rngRow = (Microsoft.Office.Interop.Excel.Range)sheetDest.Columns[1, Type.Missing];
                rngRow.UseStandardWidth = 70;
                int idx = 0;
                int row = 1;
                addCol(sheetDest, ref idx, row, 10, "小区基本信息");
                addCol(sheetDest, ref idx, row, 18, "天线权值信息");
                addCol(sheetDest, ref idx, row, 3, "状态库信息");
                addCol(sheetDest, ref idx, row, 10, "小区性能统计");
                addCol(sheetDest, ref idx, row, 24, "扫频天线分析");
                addCol(sheetDest, ref idx, row, 8, "路测天线分析");
                excel.Application.Workbooks.Add(true);

                //导入数据行
                int count = 0;
                int row2 = 0;
                foreach (List<NPOIRow> listNPOI in nrDatasList)
                {
                    row = 2;
                    count++;
                    row2 = 1;
                    foreach (NPOIRow npoi in listNPOI)
                    {
                        if (count == 1)
                        {
                            idx = npoi.cellValues.Count;
                            Microsoft.Office.Interop.Excel.Range cell1ran = sheetDest.get_Range(sheetDest.Cells[row, 1], sheetDest.Cells[row++, idx]);
                            cell1ran.Value2 = npoi.cellValues.ToArray();
                        }
                        else
                        {
                            idx = npoi.cellValues.Count;
                            Microsoft.Office.Interop.Excel.Range cell2ran = sheetAngle.get_Range(sheetAngle.Cells[row2, 1], sheetAngle.Cells[row2++, idx]);
                            cell2ran.Value2 = npoi.cellValues.ToArray();
                        }
                    }
                }
                bookDest.Saved = true;
                bookDest.SaveCopyAs(fileName);//保存
                MessageBox.Show("导出成功！");
            }
            catch (Exception w)
            {
                MessageBox.Show("导出异常:" + w.Message);
            }
            finally
            {
                excel.Quit(); 
            }
        }

        private void addCol(Microsoft.Office.Interop.Excel.Worksheet sheetDest, ref int idx, int row, int count, string name)
        {
            int col1 = idx + 1;
            int col2 = col1 + count;
            idx = col2;
            Microsoft.Office.Interop.Excel.Range ran1 = sheetDest.get_Range(sheetDest.Cells[row, col1], sheetDest.Cells[row, col2]);
            ran1.Merge(ran1.MergeCells);//合并单元格,小区基本信息
            ran1.HorizontalAlignment = Microsoft.Office.Interop.Excel.XlHAlign.xlHAlignCenter;
            ran1.Value2 = name;
        }

        /// <summary>
        /// 计算权值
        /// </summary>
        private void CalcAntDetailValue()
        {
            AntParaItem ant1Item = new AntParaItem(data.antPara.strbandtype, data.antPara.drangeport1, data.antPara.drangeport2, data.antPara.drangeport3, data.antPara.drangeport4, data.antPara.strdevvender);
            ant1Item.Init(data.antPara.dphaseport1, data.antPara.dphaseport2, data.antPara.dphaseport3, data.antPara.dphaseport4);
            AntParaItem ant2Item = new AntParaItem(data.antPara.strbandtype, data.antPara.drangeport5, data.antPara.drangeport6, data.antPara.drangeport7, data.antPara.drangeport8, data.antPara.strdevvender);
            ant2Item.Init(data.antPara.dphaseport5, data.antPara.dphaseport6, data.antPara.dphaseport7, data.antPara.dphaseport8);
            data.ciItem.model1Array = ant1Item.getPowerArray();
            data.ciItem.model2Array = ant2Item.getPowerArray();
            data.ciItem.modelMaxArray = AntParaItem.getMaxPowerArray(data.ciItem.model1Array, data.ciItem.model2Array);

            LongLat ll = new LongLat();
            ll.fLongitude = (float)(data.cellLongitude);
            ll.fLatitude = (float)(data.cellLatitude);
            int iMaxValue = -19;
            int iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(data.ciItem.modelMaxArray, ref iMaxValue, ref iMinValue);
            iMinValue = -20;
            data.longLatModelList = ZTAntFuncHelper.getCellEmulateCoverModel(ll, data.ciItem.modelMaxArray, iMaxValue, iMinValue, data.iangle_dir);

            iMaxValue = -19;
            iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(data.ciItem.newRsrpArray, ref iMaxValue, ref iMinValue);
            data.longLatScanList = ZTAntFuncHelper.getCellEmulateCoverTest(ll, data.ciItem.newRsrpArray, iMaxValue, iMinValue, data.iangle_dir);

            iMaxValue = -19;
            iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(data.ciopItem.citem.newRsrpArray, ref iMaxValue, ref iMinValue);
            data.longLatDTlList = ZTAntFuncHelper.getCellEmulateCoverTest(ll, data.ciopItem.citem.newRsrpArray, iMaxValue, iMinValue, data.iangle_dir);
        }
        
        private void xtraTabControl1_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            if (xtraTabControl1.SelectedTabPageIndex == 0 || xtraTabControl1.SelectedTabPageIndex == 1)
            {
                miShwoChart.Visible = true;
                miShowSimulation.Visible = true;
            }
        }

        private void chartControl5_CustomDrawSeriesPoint(object sender, CustomDrawSeriesPointEventArgs e)
        {
            double dRsrp = e.SeriesPoint.Values[0];
            iPointIndex = iPointIndex % 180;
            if (Math.Round(data.ciItem.model1Array[iPointIndex], 2) >= dRsrp)
                e.SeriesDrawOptions.Color = Color.Blue;
            else
                e.SeriesDrawOptions.Color = Color.Red;

            iPointIndex++;
        }

        private void chartControl2_SizeChanged(object sender, EventArgs e)
        {
            ((RadarDiagram)chartControl2.Diagram).AxisX.GridSpacing = 20;
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string strCellName = txtCellName.Text;
            FillData(strCellName);
        }

        private void btnGo_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = DicCellParaData.Count;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;
            if (iPage < 0)
                iPage = 0;
            else if (iPage > iCount - 1)
                iPage = iCount - 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnPrevpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            iPage = iPage - 1 >= 0 ? iPage - 1 : iPage;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnNextpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = DicCellParaData.Count;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;

            iPage = iPage + 1 >= iCount ? iPage : iPage + 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        

        

    }
}
