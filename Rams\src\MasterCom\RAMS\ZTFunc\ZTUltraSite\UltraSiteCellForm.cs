﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTUltraSite
{
    public partial class UltraSiteCellForm : MinCloseForm
    {
        public UltraSiteCellForm()
        {
            InitializeComponent();
            initTV();
        }

        private void initTV()
        {
            setColMainType();

            setColDisByDelaunay();

            setColAreaName();

            setColDisByDir();

            setColCell();

            setColLat();

            setColLng();

            setColOtherCell();

            setColOtherSite();

            setColProbInfo();

            setColSite();

            setTvList();
        }

        private void setColMainType()
        {
            this.colMainType.AspectGetter += delegate (object row)
            {
                string ret = null;
                if (row is List<UltraFarSite> || row is UltraFarSite)
                {
                    ret = "超远站";
                }
                else if (row is List<UltraHighSite> || row is UltraHighSite)
                {
                    ret = "超高站";
                }
                else if (row is List<UltraNearSite> || row is UltraNearSite)
                {
                    ret = "超近站";
                }
                return ret;
            };
        }

        private void setColDisByDelaunay()
        {
            this.colDisByDelaunay.AspectGetter += delegate (object row)
            {
                string ret = null;
                if (row is UltraSiteCell)
                {
                    UltraSiteCell site = row as UltraSiteCell;
                    ret = site.DistanceByDelaunay.ToString();
                }
                return ret;
            };
        }

        private void setColAreaName()
        {
            this.colAreaName.AspectGetter += delegate (object row)
            {
                string ret = null;
                if (row is UltraSiteCell)
                {
                    UltraSiteCell site = row as UltraSiteCell;
                    ret = site.AreaName;
                }
                return ret;
            };
        }

        private void setColDisByDir()
        {
            this.colDisByDir.AspectGetter += delegate (object row)
            {
                string ret = null;
                if (row is UltraSiteCell)
                {
                    UltraSiteCell site = row as UltraSiteCell;
                    ret = site.DistanceByDir.ToString();
                }
                return ret;
            };
        }

        private void setColCell()
        {
            this.colCell.AspectGetter += delegate (object row)
            {
                string cellName = null;
                if (row is UltraFarSite)
                {
                    UltraFarSite site = row as UltraFarSite;
                    cellName = site.Cell.Name;
                }
                else if (row is UltraHighSite)
                {
                    UltraHighSite site = row as UltraHighSite;
                    cellName = site.Cell.Name;
                }
                else if (row is UltraNearSite)
                {
                    UltraNearSite site = row as UltraNearSite;
                    cellName = site.Cell.Name;
                }
                return cellName;
            };
        }

        private void setColLat()
        {
            this.colLat.AspectGetter += delegate (object row)
            {
                double lat;
                if (row is UltraFarSite)
                {
                    UltraFarSite site = row as UltraFarSite;
                    lat = site.Cell.Latitude;
                }
                else if (row is UltraHighSite)
                {
                    UltraHighSite site = row as UltraHighSite;
                    lat = site.Cell.Latitude;
                }
                else if (row is UltraNearSite)
                {
                    UltraNearSite site = row as UltraNearSite;
                    lat = site.Site.Latitude;
                }
                else
                {
                    return null;
                }
                return lat;
            };
        }

        private void setColLng()
        {
            this.colLng.AspectGetter += delegate (object row)
            {
                double lat;
                if (row is UltraFarSite)
                {
                    UltraFarSite site = row as UltraFarSite;
                    lat = site.Cell.Longitude;
                }
                else if (row is UltraHighSite)
                {
                    UltraHighSite site = row as UltraHighSite;
                    lat = site.Cell.Longitude;
                }
                else if (row is UltraNearSite)
                {
                    UltraNearSite site = row as UltraNearSite;
                    lat = site.Site.Longitude;
                }
                else
                {
                    return null;
                }
                return lat;
            };
        }

        private void setColOtherCell()
        {
            this.colOtherCell.AspectGetter += delegate (object row)
            {
                string cellName;
                if (row is UltraFarSite)
                {
                    cellName = getCellName(row);
                }
                else if (row is UltraNearSite)
                {
                    UltraNearSite site = row as UltraNearSite;
                    cellName = site.OtherSite.Name;
                }
                else
                {
                    return null;
                }
                return cellName;
            };
        }

        private static string getCellName(object row)
        {
            string cellName;
            StringBuilder strCellName = new StringBuilder();
            UltraFarSite farSite = row as UltraFarSite;
            foreach (SiteDistance site in farSite.DirSites)
            {
                getCellNames(site.Site, strCellName);
            }
            cellName = strCellName.ToString().TrimEnd('，');
            return cellName;
        }

        private static void getCellNames(Model.ISite iSite, StringBuilder strCellName)
        {
            if (iSite is LTEBTS)
            {
                LTEBTS bts = iSite as LTEBTS;
                foreach (LTECell cell in bts.LatestCells)
                {
                    strCellName.Append(cell.Name + '，');
                }
            }
        }

        private void setColOtherSite()
        {
            this.colOtherSite.AspectGetter += delegate (object row)
            {
                String cellName;
                StringBuilder strCellName = new StringBuilder();
                if (row is UltraFarSite)
                {
                    UltraFarSite farSite = row as UltraFarSite;
                    foreach (SiteDistance site in farSite.DirSites)
                    {
                        strCellName.Append(site.Site.Name + "，");
                    }
                    cellName = strCellName.ToString().TrimEnd('，');
                }
                else if (row is UltraNearSite)
                {
                    UltraNearSite site = row as UltraNearSite;
                    cellName = site.OtherSite.Name;
                }
                else
                {
                    return null;
                }
                return cellName;
            };
        }

        private void setColProbInfo()
        {
            this.colProbInfo.AspectGetter += delegate (object row)
            {
                if (row is UltraFarSite)
                {
                    UltraFarSite site = row as UltraFarSite;
                    return site.Distance;
                }
                else if (row is UltraHighSite)
                {
                    UltraHighSite site = row as UltraHighSite;
                    return site.Cell.Altitude;
                }
                else if (row is UltraNearSite)
                {
                    UltraNearSite site = row as UltraNearSite;
                    return site.Distance;
                }
                return null;
            };
        }

        private void setColSite()
        {
            this.colSite.AspectGetter += delegate (object row)
            {
                if (row is UltraFarSite)
                {
                    UltraFarSite site = row as UltraFarSite;
                    return getSiteName(site.Cell);
                }
                else if (row is UltraHighSite)
                {
                    UltraHighSite site = row as UltraHighSite;
                    return getSiteName(site.Cell);
                }
                else if (row is UltraNearSite)
                {
                    UltraNearSite site = row as UltraNearSite;
                    return site.Site.Name;
                }
                return null;
            };
        }

        private object getSiteName(ICell iCell)
        {
            if (iCell is LTECell)
            {
                LTECell cell = iCell as LTECell;
                return cell.BelongBTS.Name;
            }
            return null;
        }

        private void setTvList()
        {
            this.tvList.CanExpandGetter += delegate (object row)
            {
                return row is System.Collections.IList;
            };

            this.tvList.ChildrenGetter += delegate (object row)
            {
                if (row is List<UltraFarSite>)
                {
                    return row as List<UltraFarSite>;
                }
                else if (row is List<UltraHighSite>)
                {
                    return row as List<UltraHighSite>;
                }
                else if (row is List<UltraNearSite>)
                {
                    return row as List<UltraNearSite>;
                }
                return null;
            };
        }

        public void FillData(List<UltraFarSite> farSites
            , List<UltraHighSite> highSites, List<UltraNearSite> nearSites)
        {
            List<object> src = new List<object>();
            src.Add(farSites);
            src.Add(highSites);
            src.Add(nearSites);
            tvList.ClearObjects();
            tvList.SetObjects(src);
            tvList.RebuildColumns();
            tvList.ExpandAll();
            //tvList.AutoResizeColumns(ColumnHeaderAutoResizeStyle.ColumnContent);
        }

        private void tvList_DoubleClick(object sender, EventArgs e)
        {
            MainModel.SelectedLTECells = new List<LTECell>();
            if (tvList.SelectedObject is UltraFarSite)
            {
                selectUltraFarSite();
            }
            else if (tvList.SelectedObject is UltraHighSite)
            {
                selectUltraHighSite();
            }
            else if (tvList.SelectedObject is UltraNearSite)
            {
                selectUltraNearSite();
            }

        }

        private void selectUltraFarSite()
        {
            UltraFarSite site = tvList.SelectedObject as UltraFarSite;
            setLteSite(site);
        }

        private void setLteSite(UltraFarSite site)
        {
            if (!(site.Cell is LTECell))
            {
                return;
            }
            LTECell cell = site.Cell as LTECell;
            MainModel.SelectedLTECells.Add(cell);
            double lngMax = site.Cell.Longitude;
            double latMax = site.Cell.Latitude;
            double lngMin = lngMax;
            double latMin = latMax;
            foreach (SiteDistance s in site.DirSites)
            {
                if (s.Site is LTEBTS)
                {
                    LTEBTS bts = s.Site as LTEBTS;
                    foreach (LTECell xcell in bts.LatestCells)
                    {
                        MainModel.SelectedLTECells.Add(xcell);
                    }
                    lngMax = Math.Max(lngMax, bts.Longitude);
                    latMax = Math.Max(latMax, bts.Latitude);
                    lngMin = Math.Min(lngMin, bts.Longitude);
                    latMin = Math.Min(latMin, bts.Latitude);
                }
            }
            foreach (MasterCom.RAMS.Model.ISite s in site.DelaunaySites)
            {
                if (s is LTEBTS)
                {
                    LTEBTS bts = s as LTEBTS;
                    foreach (LTECell xcell in bts.LatestCells)
                    {
                        MainModel.SelectedLTECells.Add(xcell);
                    }
                    lngMax = Math.Max(lngMax, bts.Longitude);
                    latMax = Math.Max(latMax, bts.Latitude);
                    lngMin = Math.Min(lngMin, bts.Longitude);
                    latMin = Math.Min(latMin, bts.Latitude);
                }
            }
            DbRect rect = new DbRect(lngMin - 0.001, latMin - 0.001, lngMax + 0.001, latMax + 0.001);
            MainModel.MainForm.GetMapForm().GoToView(rect);
        }

        private void selectUltraHighSite()
        {
            UltraHighSite site = tvList.SelectedObject as UltraHighSite;
            if (site.Cell is LTECell)
            {
                LTECell cell = site.Cell as LTECell;
                MainModel.SelectedLTECells.Add(cell);
                MainModel.MainForm.GetMapForm().GoToView(cell.Longitude, cell.Latitude, 6000);
            }
        }

        private void selectUltraNearSite()
        {
            UltraNearSite site = tvList.SelectedObject as UltraNearSite;
            double lngMax = 0;
            double latMax = 0;
            double lngMin = 0;
            double latMin = 0;
            if (site.Site is LTEBTS)
            {
                LTEBTS bts = site.Site as LTEBTS;
                foreach (LTECell cell in bts.LatestCells)
                {
                    MainModel.SelectedLTECells.Add(cell);
                }
                bts = site.OtherSite as LTEBTS;
                foreach (LTECell cell in bts.LatestCells)
                {
                    MainModel.SelectedLTECells.Add(cell);
                }
                lngMax = Math.Max(site.Site.Longitude, site.OtherSite.Longitude);
                lngMin = Math.Min(site.Site.Longitude, site.OtherSite.Longitude);
                latMax = Math.Max(site.Site.Latitude, site.OtherSite.Latitude);
                latMin = Math.Min(site.Site.Latitude, site.OtherSite.Latitude);
                DbRect rect = new DbRect(lngMin - 0.001, latMin - 0.001, lngMax + 0.001, latMax + 0.001);
                MainModel.MainForm.GetMapForm().GoToView(rect);
            }
        }

        private void miExportXlsx_Click(object sender, EventArgs e)
        {
            this.tvList.ExpandAll();
            ExcelNPOIManager.ExportToExcel(this.tvList);
        }
    }
}
