﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class HandoverBehindTimeCondition_NR
    {
        public NrHandoverBehindTimeCond_LTE HandoverCondition_LTE { get; set; } = new NrHandoverBehindTimeCond_LTE(-101, -95, 6, 3, true);
        public NrHandoverBehindTimeCond_NR HandoverCondition_NR { get; set; } = new NrHandoverBehindTimeCond_NR(-88, -82, 6, 3, true);
        public bool IsAnaLteHandover { get; set; }
    }
    public class NrHandoverBehindTimeCond_NR : NrHandoverBehindTimeCondBase
    {
        public NrHandoverBehindTimeCond_NR(float svrRsrpMax, float nCellRsrpMin, float RsrpDiffMin
            , int staySecondsMin, bool checkType)
            : base(svrRsrpMax, nCellRsrpMin, RsrpDiffMin, staySecondsMin, checkType)
        {
            this.Netype = ENetType.NR;
            this.MaxValue = 0;
            this.MinValue = -141;
        }

        public override float? GetSCellRsrp(TestPoint tp)
        {
            return NRTpHelper.NrTpManager.GetSCellRsrp(tp);
        }
        public override float? GetMaxNCellRsrp(TestPoint tp)
        {
            float? maxNCellRsrp = null;
            NRCell nrMainCell = tp.GetMainCell_NR();
            
            for (int i = 0; i < 16; i++)
            {
                bool isNCell = NRTpHelper.NrTpManager.JudgeIsNCell(tp, i);
                if (isNCell)
                {
                    maxNCellRsrp = dealNCell(tp, maxNCellRsrp, nrMainCell, i);
                }
            }

            return maxNCellRsrp;
        }

        private float? dealNCell(TestPoint tp, float? maxNCellRsrp, NRCell nrMainCell, int i)
        {
            NRCell nrNCell = tp.GetNBCell_NR(i);
            if (nrNCell != nrMainCell)
            {
                float? nCellRsrp = NRTpHelper.NrTpManager.GetNCellRsrp(tp, i);
                if (nCellRsrp != null)
                {
                    if (maxNCellRsrp == null)
                    {
                        maxNCellRsrp = nCellRsrp;
                    }
                    else
                    {
                        maxNCellRsrp = Math.Max((float)nCellRsrp, (float)maxNCellRsrp);
                    }
                }
            }

            return maxNCellRsrp;
        }

        public override object GetAppType(TestPoint tp)
        {
            return NRTpHelper.NrTpManager.GetAppType(tp);
        }
    }
    public class NrHandoverBehindTimeCond_LTE : NrHandoverBehindTimeCondBase
    {
        public NrHandoverBehindTimeCond_LTE(float svrRsrpMax, float nCellRsrpMin, float RsrpDiffMin
            , int staySecondsMin, bool checkType) 
            : base(svrRsrpMax, nCellRsrpMin, RsrpDiffMin, staySecondsMin, checkType)
        {
            this.Netype = ENetType.LTE;
            this.MaxValue = 25;
            this.MinValue = -141;
        }

        public override float? GetSCellRsrp(TestPoint tp)
        {
            return NRTpHelper.NrLteTpManager.GetSCellRsrp(tp);
        }
        public override float? GetMaxNCellRsrp(TestPoint tp)
        {
            float? maxNCellRsrp = null;
            for (int i = 0; i < 10; i++)
            {
                float? nCellRsrp = NRTpHelper.NrLteTpManager.GetNCellRsrp(tp, i);
                if (nCellRsrp != null)
                {
                    if (maxNCellRsrp == null)
                    {
                        maxNCellRsrp = nCellRsrp;
                    }
                    else
                    {
                        maxNCellRsrp = Math.Max((float)nCellRsrp, (float)maxNCellRsrp);
                    }
                }
            }

            return maxNCellRsrp;
        }
        public override object GetAppType(TestPoint tp)
        {
            return tp["NR_lte_Work_Mode"];
        }
    }
    public abstract class NrHandoverBehindTimeCondBase : ZTHandoverBehindTimeCondition
    {
        public ENetType Netype { get; set; }
        protected NrHandoverBehindTimeCondBase(float svrRsrpMax, float nCellRsrpMin, float RsrpDiffMin
            , int staySecondsMin, bool checkType)
            : base(svrRsrpMax, nCellRsrpMin, RsrpDiffMin, staySecondsMin, checkType)
        {
        }

        public abstract float? GetSCellRsrp(TestPoint tp);
        public abstract float? GetMaxNCellRsrp(TestPoint tp);
        public abstract object GetAppType(TestPoint tp);
    }
}
