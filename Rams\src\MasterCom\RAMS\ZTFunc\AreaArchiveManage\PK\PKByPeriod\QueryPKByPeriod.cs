﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Grid;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.CoverageCheck;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryPKByPeriod : AreaKpiQueryBase
    {
        public static readonly string pkCondPath = System.Windows.Forms.Application.StartupPath + "\\config\\AreaArchive\\PKMode\\pkMode.xml";

        private readonly PKPeriodCondition pkCond;

        private readonly MapFormItemSelection itemSelection;

        public QueryPKByPeriod()
            : base(MainModel.GetInstance())
        {
            pkCond = new PKPeriodCondition();
            itemSelection = new MapFormItemSelection();
        }

        protected override bool setConditionDlg()
        {
            pkCond.CompareModel.loadConfig();

            PeriodSettingDlg dlg = new PeriodSettingDlg(pkCond, itemSelection);
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            if (pkCond.ParamSelected == null)
            {
                MessageBox.Show("尚未选择竞比模板，请选择...", "提示");
                return false;
            }
            return true;
        }

        protected override void searchData()
        {
            QueryCondition conditionHost;
            QueryCondition conditionGuest;
            getCondition(pkCond.ParamSelected, out conditionHost, out conditionGuest);
            searchHostFormula(pkCond.ParamSelected, conditionHost);
            searchGuestFormula(pkCond.ParamSelected, conditionGuest);

            MasterCom.Util.WaitBox.Show("正在竞比...", doCompare, pkCond.ParamSelected);
        }

        private void getCondition(CompareParam param, out QueryCondition conditionHost, out QueryCondition conditionGuest)
        {
            initHostCondition(param, out conditionHost);
            initGuestCondition(param, out conditionGuest);
        }

        private void initHostCondition(CompareParam param, out QueryCondition conditionHost)
        {
            QueryCondition baseCond = ArchiveSettingManager.GetInstance().Condition.BaseCondition;

            conditionHost = new QueryCondition();
            conditionHost.Geometorys = new SearchGeometrys();
            DbRect rect = ArchiveSettingManager.GetInstance().Condition.GetBound();
            conditionHost.Geometorys = new SearchGeometrys();
            conditionHost.Geometorys.Region = ShapeHelper.CreateRectShape(rect.x1, rect.y1, rect.x2, rect.y2);
            conditionHost.AgentIds = baseCond.AgentIds;
            conditionHost.Areas = baseCond.Areas;
            conditionHost.DistrictID = baseCond.DistrictID;
            conditionHost.DistrictIDs = baseCond.DistrictIDs;
            conditionHost.EventIDs = baseCond.EventIDs;
            conditionHost.FileInfos = baseCond.FileInfos;
            conditionHost.FileName = baseCond.FileName;
            conditionHost.FileNameOrNum = baseCond.FileNameOrNum;
            conditionHost.NameFilterType = baseCond.NameFilterType;
            conditionHost.IsAllAgent = baseCond.IsAllAgent;
            conditionHost.QueryType = baseCond.QueryType;

            conditionHost.Periods.Clear();
            conditionHost.Periods.Add(pkCond.HostPeriod);
            conditionHost.Projects = baseCond.Projects;
            conditionHost.ServiceTypes.Clear();
            foreach (int servID in param.serviceList_A)
            {
                conditionHost.ServiceTypes.Add(servID);
            }
            conditionHost.CarrierTypes.Clear();
            conditionHost.CarrierTypes.Add(param.carrier_A);
        }

        private void initGuestCondition(CompareParam param, out QueryCondition conditionGuest)
        {
            QueryCondition baseCond = ArchiveSettingManager.GetInstance().Condition.BaseCondition;
            conditionGuest = new QueryCondition();
            DbRect rect = ArchiveSettingManager.GetInstance().Condition.GetBound();
            conditionGuest.Geometorys = new SearchGeometrys();
            conditionGuest.Geometorys.Region = ShapeHelper.CreateRectShape(rect.x1, rect.y1, rect.x2, rect.y2);
            conditionGuest.AgentIds = baseCond.AgentIds;
            conditionGuest.Areas = baseCond.Areas;
            conditionGuest.DistrictID = baseCond.DistrictID;
            conditionGuest.DistrictIDs = baseCond.DistrictIDs;
            conditionGuest.EventIDs = baseCond.EventIDs;
            conditionGuest.FileInfos = baseCond.FileInfos;
            conditionGuest.FileName = baseCond.FileName;
            conditionGuest.FileNameOrNum = baseCond.FileNameOrNum;
            conditionGuest.NameFilterType = baseCond.NameFilterType;
            conditionGuest.IsAllAgent = baseCond.IsAllAgent;
            conditionGuest.QueryType = baseCond.QueryType;

            conditionGuest.Periods.Clear();
            conditionGuest.Periods.Add(pkCond.GuestPeriod);
            conditionGuest.Projects = baseCond.Projects;
            conditionGuest.ServiceTypes.Clear();
            foreach (int servID in param.serviceList_B)
            {
                conditionGuest.ServiceTypes.Add(servID);
            }
            conditionGuest.CarrierTypes.Clear();
            conditionGuest.CarrierTypes.Add(param.carrier_B);
        }

        GridMatrix<GridFormula> hostGridMatrix;
        private void searchHostFormula(CompareParam param, QueryCondition conditionHost)
        {
            DIYQueryFormulaInGridByRegion queryHost = new DIYQueryFormulaInGridByRegion(MainModel);
            queryHost.setQueryFormulas(new List<string>(), param.formula_A);
            queryHost.SetQueryCondition(conditionHost);
            queryHost.Query();
            hostGridMatrix = queryHost.formulaGridMatrix;
        }

        GridMatrix<GridFormula> guestGridMatrix;
        private void searchGuestFormula(CompareParam param, QueryCondition conditionGuest)
        {
            DIYQueryFormulaInGridByRegion queryGuest = new DIYQueryFormulaInGridByRegion(MainModel);
            queryGuest.setQueryFormulas(new List<string>(), param.formula_B);
            queryGuest.SetQueryCondition(conditionGuest);
            queryGuest.Query();
            guestGridMatrix = queryGuest.formulaGridMatrix;
        }

        private AreaBase getArea(DbRect gridBond)
        {
            AreaBase rtArea = null;
            foreach (List<AreaBase> villages in archiveCondition.VillageCondition.RootLeafDic.Values)
            {
                foreach (AreaBase village in villages)
                {
                    if (village.Bounds.Within(gridBond) && village.MapOper.CheckRectIntersectWithRegion(gridBond))
                    {
                        rtArea = village;
                        break;
                    }
                }
            }
            return rtArea;
        }

        readonly Dictionary<AreaBase, CompareResultVillage> regionCPResultDic = new Dictionary<AreaBase, CompareResultVillage>();
        private void doCompare(object o)
        {
            int index = 0, progress = 0;
            regionCPResultDic.Clear();
            CompareParam cpParam = o as CompareParam;
            
            dealGridMatrix(ref index, ref progress, cpParam, hostGridMatrix, guestGridMatrix, false);

            dealGridMatrix(ref index, ref progress, cpParam, guestGridMatrix, hostGridMatrix, true);

            fillIn();

            System.Threading.Thread.Sleep(100);
            MasterCom.Util.WaitBox.Close();
        }

        private void dealGridMatrix(ref int index, ref int progress, CompareParam cpParam, GridMatrix<GridFormula> gridMatrix, GridMatrix<GridFormula> comparedGridMatrix, bool isGuset)
        {
            foreach (GridFormula grid in gridMatrix)
            {
                reportWaitBox(ref index, ref progress);
                AreaBase village = getArea(grid.Bounds);
                if (village != null)
                {
                    GridFormula gridCompare = comparedGridMatrix[grid.RowIdx, grid.ColIdx];
                    double dHost = double.NaN;
                    double dGuest = double.NaN;
                    TextColorRange rtColor = cpParam.GetTextColorRange(gridCompare, grid, ref dHost, ref dGuest);

                    if (isGuset)
                    {
                        setGuestCompareResultGrid(grid, village, dHost, dGuest, rtColor, gridCompare);
                    }
                    else
                    {
                        setHostCompareResultGrid(grid, village, dHost, dGuest, rtColor, gridCompare);
                    }
                }
            }
        }

        private void setGuestCompareResultGrid(GridFormula grid, AreaBase village, double dHost, double dGuest, TextColorRange rtColor, GridFormula gridCompare)
        {
            if (rtColor != null && rtColor.description != CPModeEditForm.GUESTNULL)
            {
                CompareResultGrid info = getCompareResultGrid(grid, gridCompare, dHost, dGuest, rtColor);

                addCPResult(village, rtColor.description, info, gridCompare, grid);
            }
        }

        private void setHostCompareResultGrid(GridFormula grid, AreaBase village, double dHost, double dGuest, TextColorRange rtColor, GridFormula gridCompare)
        {
            if (rtColor != null && rtColor.description == CPModeEditForm.GUESTNULL)
            {
                CompareResultGrid info = getCompareResultGrid(grid, gridCompare, dHost, dGuest, rtColor);

                addCPResult(village, rtColor.description, info, grid, null);
            }
        }

        private CompareResultGrid getCompareResultGrid(GridFormula grid, GridFormula gridCompare, double dHost, double dGuest, TextColorRange rtColor)
        {
            CompareResultGrid info = new CompareResultGrid();

            bool isSatisfied = dHost.Equals(double.NaN);
            info.HostDistance = getData(isSatisfied, double.NaN, Math.Round(gridCompare.formulaValueDic["0806"], 2));

            isSatisfied = dGuest.Equals(double.NaN);
            info.GuestDistance = getData(isSatisfied, double.NaN, Math.Round(grid.formulaValueDic["0806"], 2));

            isSatisfied = double.IsNaN(dHost) || double.IsNaN(dGuest);
            string hmgValue = getData(isSatisfied, "-", Convert.ToString(dHost - dGuest));

            isSatisfied = double.IsNaN(dHost);
            string valueHost = getData(isSatisfied, "-", Convert.ToString(dHost));

            isSatisfied = double.IsNaN(dGuest);
            string valueGuest = getData(isSatisfied, "-", Convert.ToString(dGuest));

            info.HostDistance = dHost.Equals(double.NaN) ? double.NaN : Math.Round(gridCompare.formulaValueDic["0806"], 2);
            info.GuestDistance = dGuest.Equals(double.NaN) ? double.NaN : Math.Round(grid.formulaValueDic["0806"], 2);
            info.FillData(grid, rtColor, hmgValue, valueHost, valueGuest);
            return info;
        }

        private T getData<T>(bool satisfied, T validData, T defaultData)
        {
            if (satisfied)
            {
                return validData;
            }
            else
            {
                return defaultData;
            }
        }

        private void reportWaitBox(ref int index, ref int progress)
        {
            if (Math.Log(index++) * (100000 / 10000) > MasterCom.Util.WaitBox.ProgressPercent)
            {
                progress++;
                if (progress > 95)
                {
                    progress = 5;
                    index = 0;
                }
                if (progress % 5 == 0)
                {
                    MasterCom.Util.WaitBox.ProgressPercent = progress;
                }
            }
        }

        private void addCPResult(AreaBase village, string level, CompareResultGrid cpResult, GridFormula hostGrid, GridFormula guestGrid)
        {
            if (!regionCPResultDic.ContainsKey(village))
            {
                regionCPResultDic.Add(village, new CompareResultVillage(village));
            }
            regionCPResultDic[village].AddGrid(hostGrid, guestGrid);
            regionCPResultDic[village].AddCompareResultGrid(level, cpResult);
        }

        private void fillIn()
        {
            foreach (List<AreaBase> villages in archiveCondition.VillageCondition.RootLeafDic.Values)
            {
                foreach (AreaBase village in villages)
                {
                    if (!regionCPResultDic.ContainsKey(village))
                    {
                        regionCPResultDic.Add(village, new CompareResultVillage(village));
                    }
                }
            }
        }

        protected override void fireShowForm()
        {
            PKByPeriodForm form = MainModel.GetObjectFromBlackboard(typeof(PKByPeriodForm)) as PKByPeriodForm;
            if (form == null || form.IsDisposed)
            {
                form = new PKByPeriodForm();
            }
            form.FillData(pkCond, regionCPResultDic);
            form.Visible = true;
            form.BringToFront();
        }

        public override string Name
        {
            get { return "不同时段竞比"; }
        }
    }

    public class CompareResultVillage
    {
        public AreaBase Village { get; set; }
        public ColorUnit HostUnit { get; set; }
        public ColorUnit GuestUnit { get; set; }
        public GridMatrix<GridFormula> HostGirds { get; set; }
        public GridMatrix<GridFormula> GuestGrids { get; set; }
        public Dictionary<string, List<CompareResultGrid>> CompareResultGridDic { get; set; }

        public CompareResultVillage(AreaBase area)
        {
            this.Village = area;
            HostUnit = new ColorUnit();
            GuestUnit = new ColorUnit();
            HostGirds = new GridMatrix<GridFormula>();
            GuestGrids = new GridMatrix<GridFormula>();
            CompareResultGridDic = new Dictionary<string, List<CompareResultGrid>>();
        }

        public void AddGrid(GridFormula gridHost, GridFormula gridGuest)
        {
            if (gridHost != null)
            {
                HostGirds[gridHost.RowIdx, gridHost.ColIdx] = gridHost;
                HostUnit.Merge(gridHost.cu);
            }
            if (gridGuest != null)
            {
                GuestGrids[gridGuest.RowIdx, gridGuest.ColIdx] = gridGuest;
                GuestUnit.Merge(gridGuest.cu);
            }
        }

        public void AddCompareResultGrid(string level, CompareResultGrid cpResult)
        {
            List<CompareResultGrid> cpResultGridList;
            if (!CompareResultGridDic.TryGetValue(level, out cpResultGridList))
            {
                cpResultGridList = new List<CompareResultGrid>();
                CompareResultGridDic.Add(level, cpResultGridList);
            }
            cpResultGridList.Add(cpResult);
        }

        public void MergeResult(CompareResultVillage result)
        {
            foreach (string level in result.CompareResultGridDic.Keys)
            {
                List<CompareResultGrid> grids;
                if (!CompareResultGridDic.TryGetValue(level, out grids))
                {
                    grids = new List<CompareResultGrid>();
                    CompareResultGridDic[level] = grids;
                }
                grids.AddRange(result.CompareResultGridDic[level]);
            }

            foreach (GridFormula grid in result.HostGirds)
            {
                if (HostGirds[grid.RowIdx, grid.ColIdx] == null)
                    HostGirds[grid.RowIdx, grid.ColIdx] = grid;
                else
                    HostGirds[grid.RowIdx, grid.ColIdx].cu.Merge(grid.cu);

                HostUnit.Merge(grid.cu);
            }

            foreach (GridFormula grid in result.GuestGrids)
            {
                if (GuestGrids[grid.RowIdx, grid.ColIdx] == null)
                    GuestGrids[grid.RowIdx, grid.ColIdx] = grid;
                else
                    GuestGrids[grid.RowIdx, grid.ColIdx].cu.Merge(grid.cu);

                GuestUnit.Merge(grid.cu);
            }
        }
    }
}
