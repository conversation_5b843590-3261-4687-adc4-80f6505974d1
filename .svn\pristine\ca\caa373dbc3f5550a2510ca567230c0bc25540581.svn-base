﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTRtpPacketsLostByRegion : DIYSampleByRegion
    {
        protected static readonly object lockObj = new object();
        private static ZTRtpPacketsLostByRegion instance = null;
        public static ZTRtpPacketsLostByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTRtpPacketsLostByRegion(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        public ZTRtpPacketsLostByRegion(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;

        }
        #region Group
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {

            DIYSampleGroup cellSetGroup = new DIYSampleGroup();
            cellSetGroup.ThemeName = "---";

            addDTParameter(cellSetGroup, "lte_volte_Source_SSRC");
            addDTParameter(cellSetGroup, "lte_TAC");
            addDTParameter(cellSetGroup, "lte_ECI");
            addDTParameter(cellSetGroup, "lte_PCI");
            addDTParameter(cellSetGroup, "lte_RSRP");
            addDTParameter(cellSetGroup, "lte_SINR");
            addDTParameter(cellSetGroup, "lte_PUSCH_Power");
            addDTParameter(cellSetGroup, "lte_gsm_SC_BCCH");
            addDTParameter(cellSetGroup, "lte_gsm_SC_LAC");
            addDTParameter(cellSetGroup, "lte_gsm_SC_CI");
            addDTParameter(cellSetGroup, "isampleid");
            addDTParameter(cellSetGroup, "itime");
            addDTParameter(cellSetGroup, "ilongitude");
            addDTParameter(cellSetGroup, "ilatitude");
            addDTParameter(cellSetGroup, "lte_Pathloss");
            addDTParameter(cellSetGroup, "lte_PDSCH_BLER");
            addDTParameter(cellSetGroup, "lte_PUSCH_BLER");
            addDTParameter(cellSetGroup, "lte_PDSCH_RB_Number");
            addDTParameter(cellSetGroup, "lte_volte_RTP_Sequence_Number");
            addDTParameter(cellSetGroup, "lte_volte_UL_Source_SSRC");
            addDTParameter(cellSetGroup, "lte_PESQMos");
            addDTParameter(cellSetGroup, "lte_POLQA_Score_SWB");

            return cellSetGroup;
        }

        private void addDTParameter(DIYSampleGroup cellSetGroup, string name)
        {
            DTParameter parameter = DTParameterManager.GetInstance().GetParameter(name);
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
        }


        #endregion

        public override string Name
        {
            get
            {
                return "单通问题点统计(按区域)";
            }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22111, this.Name);
        }
        protected ZTRtpPacketsLostMessageConditon hoCondition = new ZTRtpPacketsLostMessageConditon();
    }
    public class ZTRtpPacketsLostRegionInfo
    {
        public ZTRtpPacketsLostRegionInfo(Model.Message startMsg, Model.Message endtMsg)
        {
            this.StartMsg = startMsg;
            this.EndMsg = endtMsg;
            TestPoints = new List<TestPoint>();
        }

        public Model.Message StartMsg { get; set; }
        public Model.Message EndMsg { get; set; }
        public int SN { get; set; }
        public string Area { get; set; }
        public string Grid { get; set; }
        public string FileName { get; set; }
        public DateTime? SCallTime { get; set; }
        public DateTime? SCallCompleteTime { get; set; }
        public string Direction { get; set; }
        public DateTime? SLossTime { get; set; }
        public DateTime? SLossCompleteTime { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public TimeSpan? LossTime { get; set; }
        public long LossNumber { get; set; }
        public string MosTime { get; set; }
        public float? MosVal { get; set; }
        public float? MosAveVal { get; set; }
        public LTECell LossCell { get; set; }
        public double? RSRP { get; set; }
        public double? SINR { get; set; }
        public double? PathLoss { get; set; }
        public double? Pdsch_bler { get; set; }
        public double? Pusch_bler { get; set; }
        public int? Uetxpower { get; set; }
        public int? RBCount { get; set; }
        public List<TestPoint> TestPoints { get; set; }

    }
    public class MosInfomation
    {
        public MosInfomation(TimePeriod mosPeriod, DateTime pointTime, float mosVal)
        {
            this.MosPeriod = mosPeriod;
            this.PointTime = pointTime;
            this.MosVal = mosVal;
        }

        public TimePeriod MosPeriod { get; set; }
        public DateTime PointTime { get; set; }
        public float MosVal { get; set; }
    }
    public class MessageTime
    {
        public MessageTime(TimePeriod handsetsTime, TimePeriod completeTime)
        {
            this.HandsetsTime = handsetsTime;
            this.CompleteTime = completeTime;
        }

        public TimePeriod HandsetsTime { get; set; }
        public TimePeriod CompleteTime { get; set; }
    }
    public class ZTRtpPacketsLostAllRegionInfo
    {
        public List<ZTRtpPacketsLostRegionInfo> MessageInfos { get; set; }

        public string FileName { get; set; }

        public ZTRtpPacketsLostAllRegionInfo(List<ZTRtpPacketsLostRegionInfo> dlMessageInfos, List<ZTRtpPacketsLostRegionInfo> ulMessageInfos, string fileName)
        {
            this.MessageInfos = dlMessageInfos;
            this.MessageInfos.AddRange(ulMessageInfos);
            this.FileName = fileName;
            GetSN();
        }

        private void GetSN()
        {
            int index = 1;

            if (MessageInfos != null)
                MessageInfos.ForEach(msg =>
                {
                    msg.SN = index++;
                });
        }
    }
    //public class ZTRtpPacketsLostRegionConditon
    //{
    //    public double RtpLossRate { get; set; }
    //    //public int Radium { get; set; }
    //    public float LossTime { get; set; }

    //    public ZTRtpPacketsLostRegionConditon()
    //    {
    //        RtpLossRate = 0.5;
    //        LossTime = 1;
    //    }
    //}
}
