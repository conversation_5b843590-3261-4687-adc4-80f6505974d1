﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTTdAntParaCommon : ZTAntennaBase
    {
        public ZTTdAntParaCommon(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
            this.mainModel = mainModel;
        }

        #region 功能相关信息
        public override string Name
        {
            get { return "TD天线综合分析"; }
        }

        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.None; }
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 28000, 28004, this.Name);
        }
        #endregion

        #region 公共变量
        int iFunc = 1;
        AntTimeCfg timeCfg = new AntTimeCfg();
        string strCityName = "";
        Dictionary<LaiKey, ZTAntGsmTdCellInfo> antCfgRamsParaDic = null;
        Dictionary<LaiKey, ZTTdAntenna.CellAngleData> antScanInfoDic = null;
        Dictionary<LaiKey, ZTTdAntenna.CellAngleData> antInfoDic = null;
        Dictionary<LaiKey, TdAntParaItem> cellAntParaInfoDic = null;
        Dictionary<LaiKey, CellTdPara> cellTdParaEciDic = null;
        Dictionary<LaiKey, CellTdMRKpi> cellTdMRKpiDic = null;
        Dictionary<LaiKey, ZTAntGTMRBaseItem> mrRxcpDic = null;
        Dictionary<LaiKey, ZTAntGTMRBaseItem> mrUeTxPowerDic = null;
        Dictionary<LaiKey, ZTAntGTMRBaseItem> mrTADic = null;
        Dictionary<LaiKey, ZTAntGTMRBaseItem> mrAoaDic = null;
        Dictionary<LaiKey, ZTAntGTMRBaseItem> mrSirDic = null;
        List<List<NPOIRow>> nrDatasList = null;
        List<string> sheetNames = null;
        #endregion

        protected override void query()
        {
            ZTDataCfgForm dataCfg = new ZTDataCfgForm(false, "TD");
            if (dataCfg.ShowDialog() != DialogResult.OK)
                return;
            iFunc = dataCfg.iFunc;
            timeCfg = dataCfg.timeCfgList[0];
            GetDt_ScanProject(timeCfg, "TD");
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName
                    , MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                intData();
                WaitBox.CanCancel = true;
                WaitBox.Text = "正在查询...";
                strCityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                WaitBox.Show("1.读取现网天线信息...", doWithAntCfgData);
                WaitBox.Show("2.天线分析数据处理...", AnaCellTestData);
                WaitBox.Show("3.读取小区性能数据...", doWithCellParaData);
                WaitBox.Show("4.读取小区MR数据...", doWithCellMRByTypeData);
                WaitBox.Show("5.联合数据处理...", AnaCellAngleData);
                if (iFunc != 2)
                    WaitBox.Show("6.获取天线角度赋形数据...", AnaAngleLevelData);
                WaitBox.Show("整理数据结果集...", dealMainUtranCellSample);
                FireShowResultForm();
            }
            catch
            {
                clientProxy.Close();
            }
            finally
            {
                intData();
            }
        }

        private void intData()
        {
            antCfgRamsParaDic = new Dictionary<LaiKey, ZTAntGsmTdCellInfo>();
            antScanInfoDic = new Dictionary<LaiKey, ZTTdAntenna.CellAngleData>();
            antInfoDic = new Dictionary<LaiKey, ZTTdAntenna.CellAngleData>();
            cellAntParaInfoDic = new Dictionary<LaiKey, TdAntParaItem>();
            cellTdParaEciDic = new Dictionary<LaiKey, CellTdPara>();
            cellTdMRKpiDic = new Dictionary<LaiKey, CellTdMRKpi>();
            mrRxcpDic = new Dictionary<LaiKey, ZTAntGTMRBaseItem>();
            mrUeTxPowerDic = new Dictionary<LaiKey, ZTAntGTMRBaseItem>();
            mrTADic = new Dictionary<LaiKey, ZTAntGTMRBaseItem>();
            mrAoaDic = new Dictionary<LaiKey, ZTAntGTMRBaseItem>();
            mrSirDic = new Dictionary<LaiKey, ZTAntGTMRBaseItem>();
            nrDatasList = new List<List<NPOIRow>>();
            sheetNames = new List<string>();
            MainModel.ClearDTData();
        }

        #region 查询综合小区相关信息

        private void doWithAntCfgData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 20;
            DiyCfgRamsPara cellPara = new DiyCfgRamsPara(MainModel, "TD");
            cellPara.Query();
            antCfgRamsParaDic = cellPara.antCfgRamsParaDic;
            WaitBox.Close();
        }

        private void AnaCellTestData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 20;
            ZTGsmAntenna.isScanStat = true;
            if (strProject[0].Length > 0)
            {
                antScanInfoDic = ZTTdScanAntenna.GetInstance().getBackgroundData(timeCfg.ISitme, timeCfg.IEitme, strProject[0]);
            }

            ZTGsmAntenna.isScanStat = false;
            WaitBox.ProgressPercent = 60;
            if (strProject[1].Length > 0)
            {
                antInfoDic = ZTTdAntenna.GetInstance().getBackgroundData(timeCfg.ISitme, timeCfg.IEitme, strProject[1]);
            }
            WaitBox.Close();
        }

        private void doWithCellParaData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 20;
            DiyTDCellPara cellPara = new DiyTDCellPara(MainModel, timeCfg);
            cellPara.Query();
            cellTdParaEciDic = cellPara.cellTdParaDic;

            WaitBox.Close();
        }

        protected void doWithCellMRByTypeData()
        {
            WaitBox.ProgressPercent = 10;
            doWithMRRscpData();
            WaitBox.ProgressPercent = 20;
            doWithMRTxPowerData();
            WaitBox.ProgressPercent = 40;
            doWithMRTAData();
            WaitBox.ProgressPercent = 60;
            doWithMRAoaData();
            WaitBox.ProgressPercent = 80;
            doWithMRSirDlData();
            WaitBox.ProgressPercent = 90;
            doWithMRKpi();
            WaitBox.ProgressPercent = 100;
            WaitBox.Close();
        }

        protected void doWithMRRscpData()
        {
            WaitBox.CanCancel = true;
            DiyTDMRData tdMRData = new DiyTDMRData(MainModel, 1, 55, timeCfg);
            tdMRData.SetQueryCondition(condition);
            tdMRData.Query();
            mrRxcpDic = tdMRData.gtMRDic;
        }

        protected void doWithMRTxPowerData()
        {
            WaitBox.CanCancel = true;
            DiyTDMRData tdMRData = new DiyTDMRData(MainModel, 2, 28, timeCfg);
            tdMRData.SetQueryCondition(condition);
            tdMRData.Query();
            mrUeTxPowerDic = tdMRData.gtMRDic;
        }

        protected void doWithMRTAData()
        {
            WaitBox.CanCancel = true;
            DiyTDMRData tdMRData = new DiyTDMRData(MainModel, 3, 37, timeCfg);
            tdMRData.SetQueryCondition(condition);
            tdMRData.Query();
            mrTADic = tdMRData.gtMRDic;
        }

        protected void doWithMRAoaData()
        {
            WaitBox.CanCancel = true;
            DiyTDMRData tdMRData = new DiyTDMRData(MainModel, 4, 72, timeCfg);
            tdMRData.SetQueryCondition(condition);
            tdMRData.Query();
            mrAoaDic = tdMRData.gtMRDic;
        }

        protected void doWithMRSirDlData()
        {
            WaitBox.CanCancel = true;
            DiyTDMRData tdMRData = new DiyTDMRData(MainModel, 5, 64, timeCfg);
            tdMRData.SetQueryCondition(condition);
            tdMRData.Query();
            mrSirDic = tdMRData.gtMRDic;
        }

        private void doWithMRKpi()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 20;
            DiyTDMRKpi cellMr = new DiyTDMRKpi(MainModel, timeCfg);
            cellMr.Query();
            cellTdMRKpiDic = cellMr.cellTdMRKpiDic;

            WaitBox.Close();
        }

        private void AnaCellAngleData()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 0;
            int iCount = antCfgRamsParaDic.Count;
            int iNum = 0;
            cellAntParaInfoDic.Clear();
            foreach (LaiKey cellKey in antCfgRamsParaDic.Keys)
            {
                if (WaitBox.CancelRequest)
                    break;
                if (iNum % 200 == 0)
                {
                    WaitBox.ProgressPercent = (int)(100 * ((iNum * 1.0) / iCount));
                    WaitBox.Text = "5.联合数据处理(" + iNum++ + "/" + iCount + ")";
                }
                try
                {
                    TdAntParaItem tdAntParaItem = new TdAntParaItem();
                    tdAntParaItem.序号 = cellAntParaInfoDic.Keys.Count + 1;

                    antCellInfo(ref tdAntParaItem, antCfgRamsParaDic[cellKey]);
                    tdAntParaItem = getTdAntParaItem(cellKey, tdAntParaItem);

                    setMRData(cellKey, tdAntParaItem);

                    tdAntParaItem.分析结果 = "";
                    tdAntParaItem.告警状态 = "";
                    分析问题类型(ref tdAntParaItem);

                    if (!cellAntParaInfoDic.ContainsKey(cellKey))
                    {
                        cellAntParaInfoDic[cellKey] = tdAntParaItem;
                    }
                }
                catch (System.Exception ex)
                {
                    log.Error(ex.Message);
                }
            }
            antCfgRamsParaDic.Clear();
            antInfoDic.Clear();
            antScanInfoDic.Clear();
            WaitBox.Close();
        }

        private TdAntParaItem getTdAntParaItem(LaiKey cellKey, TdAntParaItem tdAntParaItem)
        {
            if (antInfoDic.ContainsKey(cellKey))
            {
                antDtInfo(ref tdAntParaItem, antInfoDic[cellKey]);
            }
            else
            {
                antDtInfo(ref tdAntParaItem, new ZTTdAntenna.CellAngleData());
            }
            if (antScanInfoDic.ContainsKey(cellKey))
            {
                antScanInfo(ref tdAntParaItem, antScanInfoDic[cellKey]);
            }
            else
            {
                antScanInfo(ref tdAntParaItem, new ZTTdAntenna.CellAngleData());
            }

            if (cellTdParaEciDic.ContainsKey(cellKey))
            {
                tdAntParaItem.paraCellInfoIem = cellTdParaEciDic[cellKey];
            }
            else
            {
                tdAntParaItem.paraCellInfoIem = new CellTdPara();
            }

            if (cellTdMRKpiDic.ContainsKey(cellKey))
            {
                tdAntParaItem.mrCellInfoIem = cellTdMRKpiDic[cellKey];
            }
            else
            {
                tdAntParaItem.mrCellInfoIem = new CellTdMRKpi();
            }

            return tdAntParaItem;
        }

        private void setMRData(LaiKey cellKey, TdAntParaItem tdAntParaItem)
        {
            tdAntParaItem.mrData = new GTCellMrData();
            //小区MR覆盖指标分析统计
            ZTAntGTMRBaseItem tdMRRscpItem;
            if (!mrRxcpDic.TryGetValue(cellKey, out tdMRRscpItem))
            {
                tdMRRscpItem = new ZTAntGTMRBaseItem();
            }
            tdAntParaItem.mrData.tdMRRscpItem = tdMRRscpItem;

            ZTAntGTMRBaseItem tdMRTxPowerItem;
            if (!mrUeTxPowerDic.TryGetValue(cellKey, out tdMRTxPowerItem))
            {
                tdMRTxPowerItem = new ZTAntGTMRBaseItem();
            }
            tdAntParaItem.mrData.tdMRTxPowerItem = tdMRTxPowerItem;

            ZTAntGTMRBaseItem tdMRAoaItem;
            if (!mrAoaDic.TryGetValue(cellKey, out tdMRAoaItem))
            {
                tdMRAoaItem = new ZTAntGTMRBaseItem();
            }
            tdAntParaItem.mrData.tdMRAoaItem = tdMRAoaItem;

            ZTAntGTMRBaseItem tdMRTaItem;
            if (!mrTADic.TryGetValue(cellKey, out tdMRTaItem))
            {
                tdMRTaItem = new ZTAntGTMRBaseItem();
            }
            tdAntParaItem.mrData.tdMRTaItem = tdMRTaItem;

            ZTAntGTMRBaseItem tdMRSirUlItem;
            if (!mrSirDic.TryGetValue(cellKey, out tdMRSirUlItem))
            {
                tdMRSirUlItem = new ZTAntGTMRBaseItem();
            }
            tdAntParaItem.mrData.tdMRSirUlItem = tdMRSirUlItem;
            tdAntParaItem.tdMrData.mrData = tdAntParaItem.mrData;
        }
        #endregion

        #region 综合小区指标信息赋值

        private void antCellInfo(ref TdAntParaItem tdAntParaItem, ZTAntGsmTdCellInfo antGsmTdCellInfo)
        {
            tdAntParaItem.iFunc = iFunc;
            tdAntParaItem.timeCfg = timeCfg;
            tdAntParaItem.地市 = strCityName;
            tdAntParaItem.基站名称 = antGsmTdCellInfo.基站BTS名称;
            tdAntParaItem.小区名称 = antGsmTdCellInfo.小区名称;
            tdAntParaItem.小区LAC = antGsmTdCellInfo.小区LAC;
            tdAntParaItem.小区CI = antGsmTdCellInfo.小区CI;
            tdAntParaItem.覆盖类型 = antGsmTdCellInfo.基站室分类型;
            tdAntParaItem.场景类型 = "";
            tdAntParaItem.小区主频 = antGsmTdCellInfo.小区频点;
            tdAntParaItem.天线经度 = antGsmTdCellInfo.天线经度;
            tdAntParaItem.天线纬度 = antGsmTdCellInfo.天线纬度;
            tdAntParaItem.方位角 = antGsmTdCellInfo.天线方向角;
            tdAntParaItem.下倾角 = antGsmTdCellInfo.天线下倾角;
            tdAntParaItem.挂高 = antGsmTdCellInfo.天线挂高;
        }

        private void antDtInfo(ref TdAntParaItem tdAntParaItem, ZTTdAntenna.CellAngleData dtCellAngleData)
        {
            tdAntParaItem.采样点总数_路测 = dtCellAngleData.bgExt.strSampleCountNum;
            tdAntParaItem.覆盖率PCCPCHRSCP90_C_I_3_路测 = dtCellAngleData.bgExt.strPcchRscp90_3Rate;
            tdAntParaItem.覆盖率PCCPCHRSCP95_C_I_3_路测 = dtCellAngleData.bgExt.strPcchRscp95_3Rate;
            tdAntParaItem.覆盖率DPCHRSCP90_C_I_3_路测 = dtCellAngleData.bgExt.strDcchRscp90_3Rate;
            tdAntParaItem.覆盖率DPCHRSCP95_C_I_3_路测 = dtCellAngleData.bgExt.strDcchRscp95_3Rate;
            tdAntParaItem.小区PCCPCHRSCP均值_路测 = dtCellAngleData.bgExt.strPcchRscpAvg;
            tdAntParaItem.小区PCCPCH_C2I_3占比_路测 = dtCellAngleData.bgExt.strPcchC2I;
            tdAntParaItem.小区PCCPCH_C_I平均_路测 = dtCellAngleData.bgExt.strPcch2iAvg;
            tdAntParaItem.小区平均通信距离_路测 = dtCellAngleData.bgExt.strSampleDist;
            tdAntParaItem.范围内采样点比例_60_路测 = dtCellAngleData.bgExt.strMainSampleCountNumRate;
            tdAntParaItem.范围内平均覆盖距离_60_路测 = dtCellAngleData.bgExt.strMainDistance;
            tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_60_路测 = dtCellAngleData.bgExt.strMainPcchRscp90_3Rate;
            tdAntParaItem.范围内平均PCCPCHRSCP_60_路测 = dtCellAngleData.bgExt.strMainPcchRscpAvg;
            tdAntParaItem.范围内采样点比例_150_路测 = dtCellAngleData.bgExt.strSideSampleCountNumRate;
            tdAntParaItem.范围内平均覆盖距离_150_路测 = dtCellAngleData.bgExt.strSideDistance;
            tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_150_路测 = dtCellAngleData.bgExt.strSidePcchRscp90_3Rate;
            tdAntParaItem.范围内平均PCCPCHRSCP_150_路测 = dtCellAngleData.bgExt.strSidePcchRscpAvg;
            tdAntParaItem.范围内采样点比例_180_路测 = dtCellAngleData.bgExt.strBackSampleCountNumRate;
            tdAntParaItem.范围内平均覆盖距离_180_路测 = dtCellAngleData.bgExt.strBackDistance;
            tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_180_路测 = dtCellAngleData.bgExt.strBackPcchRscp90_3Rate;
            tdAntParaItem.范围内平均PCCPCHRSCP_180_路测 = dtCellAngleData.bgExt.strBackPcchRscpAvg;
            tdAntParaItem.前后比_路测 = dtCellAngleData.bgExt.strMainBackPcchRscpDiff;

            tdAntParaItem.范围内覆盖率PCCPCHRSCPC2I_3_150_路测 = dtCellAngleData.bgExt.strSidePcchC2I;
            tdAntParaItem.dtCellAngleData = dtCellAngleData;
        }

        private void antScanInfo(ref TdAntParaItem tdAntParaItem, ZTTdAntenna.CellAngleData scanCellAngleData)
        {
            tdAntParaItem.采样点总数_扫频 = scanCellAngleData.bgExt.strSampleCountNum;
            tdAntParaItem.覆盖率PCCPCHRSCP90_C_I_3_扫频 = scanCellAngleData.bgExt.strPcchRscp90_3Rate;
            tdAntParaItem.覆盖率PCCPCHRSCP95_C_I_3_扫频 = scanCellAngleData.bgExt.strPcchRscp95_3Rate;
            tdAntParaItem.小区PCCPCHRSCP均值_扫频 = scanCellAngleData.bgExt.strPcchRscpAvg;
            tdAntParaItem.小区PCCPCH_C2I_3占比_扫频 = scanCellAngleData.bgExt.strPcchC2I;
            tdAntParaItem.小区PCCPCH_C_I平均_扫频 = scanCellAngleData.bgExt.strPcch2iAvg;
            tdAntParaItem.小区平均通信距离_扫频 = scanCellAngleData.bgExt.strSampleDist;
            tdAntParaItem.范围内采样点比例_60_扫频 = scanCellAngleData.bgExt.strMainSampleCountNumRate;
            tdAntParaItem.范围内平均覆盖距离_60_扫频 = scanCellAngleData.bgExt.strMainDistance;
            tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_60_扫频 = scanCellAngleData.bgExt.strMainPcchRscp90_3Rate;
            tdAntParaItem.范围内平均PCCPCHRSCP_60_扫频 = scanCellAngleData.bgExt.strMainPcchRscpAvg;
            tdAntParaItem.范围内采样点比例_150_扫频 = scanCellAngleData.bgExt.strSideSampleCountNumRate;
            tdAntParaItem.范围内平均覆盖距离_150_扫频 = scanCellAngleData.bgExt.strSideDistance;
            tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_150_扫频 = scanCellAngleData.bgExt.strSidePcchRscp90_3Rate;
            tdAntParaItem.范围内平均PCCPCHRSCP_150_扫频 = scanCellAngleData.bgExt.strSidePcchRscpAvg;
            tdAntParaItem.范围内采样点比例_180_扫频 = scanCellAngleData.bgExt.strBackSampleCountNumRate;
            tdAntParaItem.范围内平均覆盖距离_180_扫频 = scanCellAngleData.bgExt.strBackDistance;
            tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_180_扫频 = scanCellAngleData.bgExt.strBackPcchRscp90_3Rate;
            tdAntParaItem.范围内平均PCCPCHRSCP_180_扫频 = scanCellAngleData.bgExt.strBackPcchRscpAvg;
            tdAntParaItem.前后比_扫频 = scanCellAngleData.bgExt.strMainBackPcchRscpDiff;

            tdAntParaItem.范围内覆盖率PCCPCHRSCPC2I_3_150_扫频 = scanCellAngleData.bgExt.strSidePcchC2I;
            tdAntParaItem.scanCellAngleData = scanCellAngleData;
        }

        private void AnaAngleLevelData()
        {
            //路测 and 扫频 角度信息
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 40;
            WaitBox.Text = "4.获取天线角度赋形数据...";
            Dictionary<LaiKey, ZTTdAntenna.CellInfoItem> dtCellInfoDic;
            Dictionary<LaiKey, ZTTdAntenna.CellInfoItem> scanCellInfoDic;
            DIYQueryTDAntAngle tdAntQuery = new DIYQueryTDAntAngle(MainModel);
            tdAntQuery.SetCondition(timeCfg.ISitme, timeCfg.IEitme);
            tdAntQuery.Query();
            dtCellInfoDic = tdAntQuery.dtCellInfoDic;
            scanCellInfoDic = tdAntQuery.scanCellInfoDic;

            WaitBox.ProgressPercent = 60;
            WaitBox.Text = "4.获取天线角度赋形数据完毕，正在匹配小区进行赋值...";
            foreach (LaiKey cellKey in dtCellInfoDic.Keys)
            {
                if (cellAntParaInfoDic.ContainsKey(cellKey))
                {
                    cellAntParaInfoDic[cellKey].dtCellInfoItem = dtCellInfoDic[cellKey];
                }
            }
            foreach (LaiKey cellKey in scanCellInfoDic.Keys)
            {
                if (cellAntParaInfoDic.ContainsKey(cellKey))
                {
                    cellAntParaInfoDic[cellKey].scanCellInfoItem = scanCellInfoDic[cellKey];
                }
            }
            WaitBox.Close();
        }
        #endregion

        #region 问题判断分析

        private void 分析问题类型(ref TdAntParaItem tdAntParaItem)
        {
            tdAntParaItem.告警状态 = "";
            string strCityLevel = ZTAntFuncHelper.getCityLevel(strCityName);
            if (tdAntParaItem.覆盖类型.Equals("室分") || tdAntParaItem.覆盖类型.Equals("室内"))
            {
                室分泄露问题分析(ref tdAntParaItem, strCityLevel);
            }
            else
            {
                主瓣弱覆盖问题分析(ref tdAntParaItem, strCityLevel);
                旁瓣泄露问题分析(ref tdAntParaItem, strCityLevel);
                背瓣覆盖问题分析(ref tdAntParaItem, strCityLevel);
                天线工参核查(ref tdAntParaItem);
            }
        }

        private void 主瓣弱覆盖问题分析(ref TdAntParaItem tdAntParaItem, string strCityLevel)
        {
            int iSampleNumDef = 0;
            if (strCityLevel == "一类")
                iSampleNumDef = 120;
            else if (strCityLevel == "二类")
                iSampleNumDef = 105;
            else
                iSampleNumDef = 95;

            double 采样点比例_60_路测;
            if (tdAntParaItem.范围内采样点比例_60_路测 != ""
                && double.TryParse(tdAntParaItem.范围内采样点比例_60_路测, out 采样点比例_60_路测)
                && double.Parse(tdAntParaItem.采样点总数_路测) * 采样点比例_60_路测 >= iSampleNumDef
                && double.Parse(tdAntParaItem.范围内平均PCCPCHRSCP_60_路测) < -94
                && double.Parse(tdAntParaItem.范围内平均覆盖距离_60_路测) <= 800)
            {
                tdAntParaItem.分析结果 += "主瓣弱覆盖,";
                tdAntParaItem.告警状态 = "二级告警";
                if (double.Parse(tdAntParaItem.覆盖率PCCPCHRSCP90_C_I_3_路测) <= 85 || double.Parse(tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_60_路测) <= 85
                || double.Parse(tdAntParaItem.小区PCCPCH_C2I_3占比_路测) > 10)
                {
                    if (!tdAntParaItem.分析结果.Contains("主瓣弱覆盖"))
                    {
                        tdAntParaItem.分析结果 += "主瓣弱覆盖,";
                    }
                    tdAntParaItem.告警状态 = "一级告警";
                }
            }
        }

        private void 旁瓣泄露问题分析(ref TdAntParaItem tdAntParaItem, string strCityLevel)
        {
            int iSampleNumDef = 0;
            if (strCityLevel == "一类")
                iSampleNumDef = 300;
            else if (strCityLevel == "二类")
                iSampleNumDef = 270;
            else
                iSampleNumDef = 240;

            dealSidelobeDT(tdAntParaItem, iSampleNumDef);

            dealSidelobeScan(tdAntParaItem, iSampleNumDef);
        }

        private void dealSidelobeDT(TdAntParaItem tdAntParaItem, int iSampleNumDef)
        {
            double 采样点比例_150_路测;
            if (tdAntParaItem.范围内采样点比例_150_路测 != ""
                && double.TryParse(tdAntParaItem.范围内采样点比例_150_路测, out 采样点比例_150_路测)
                && double.Parse(tdAntParaItem.采样点总数_路测) >= iSampleNumDef
                && 采样点比例_150_路测 < 60 && 采样点比例_150_路测 > 40
                && double.Parse(tdAntParaItem.范围内平均覆盖距离_150_路测) >= 150)
            {
                tdAntParaItem.分析结果 += "旁瓣泄露,";
                if (tdAntParaItem.告警状态 == "")
                    tdAntParaItem.告警状态 = "二级告警";

                if (double.Parse(tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_150_路测) >= 85
                   || double.Parse(tdAntParaItem.范围内覆盖率PCCPCHRSCPC2I_3_150_路测) >= 90
                   || double.Parse(tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_60_路测) <= 85)
                {
                    if (!tdAntParaItem.分析结果.Contains("旁瓣泄露"))
                    {
                        tdAntParaItem.分析结果 += "旁瓣泄露,";
                    }
                    tdAntParaItem.告警状态 = "一级告警";
                }
            }
        }

        private void dealSidelobeScan(TdAntParaItem tdAntParaItem, int iSampleNumDef)
        {
            double 采样点比例_150_扫频;
            if (tdAntParaItem.范围内采样点比例_150_扫频 != ""
                && double.TryParse(tdAntParaItem.范围内采样点比例_150_扫频, out 采样点比例_150_扫频)
                && double.Parse(tdAntParaItem.采样点总数_扫频) >= iSampleNumDef
                && 采样点比例_150_扫频 < 60
                && 采样点比例_150_扫频 > 40
                && double.Parse(tdAntParaItem.范围内平均覆盖距离_150_扫频) >= 200)
            {
                if (!tdAntParaItem.分析结果.Contains("旁瓣泄露"))
                {
                    tdAntParaItem.分析结果 += "旁瓣泄露,";
                }
                if (tdAntParaItem.告警状态 == "")
                    tdAntParaItem.告警状态 = "二级告警";
                if (double.Parse(tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_150_扫频) >= 85
                    || double.Parse(tdAntParaItem.范围内覆盖率PCCPCHRSCPC2I_3_150_扫频) >= 90
                    || double.Parse(tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_150_扫频) <= 85)
                {
                    if (!tdAntParaItem.分析结果.Contains("旁瓣泄露"))
                    {
                        tdAntParaItem.分析结果 += "旁瓣泄露,";
                    }
                    tdAntParaItem.告警状态 = "一级告警";
                }
            }
        }

        private void 背瓣覆盖问题分析(ref TdAntParaItem tdAntParaItem, string strCityLevel)
        {
            int iSampleNumDef = 0;
            if (strCityLevel == "一类")
                iSampleNumDef = 300;
            else if (strCityLevel == "二类")
                iSampleNumDef = 270;
            else
                iSampleNumDef = 240;

            dealBacklobeDT(tdAntParaItem, iSampleNumDef);

            dealBacklobeScan(tdAntParaItem, iSampleNumDef);
        }

        private void dealBacklobeDT(TdAntParaItem tdAntParaItem, int iSampleNumDef)
        {
            double 采样点比例_180_路测;
            if (tdAntParaItem.范围内采样点比例_180_路测 != ""
                && double.TryParse(tdAntParaItem.范围内采样点比例_180_路测, out 采样点比例_180_路测)
                && double.Parse(tdAntParaItem.采样点总数_路测) >= iSampleNumDef && 采样点比例_180_路测 >= 8
                && double.Parse(tdAntParaItem.范围内平均覆盖距离_180_路测) >= 50 && tdAntParaItem.前后比_路测 != ""
                && tdAntParaItem.前后比_路测 != "-" && double.Parse(tdAntParaItem.前后比_路测) <= 5)
            {
                tdAntParaItem.分析结果 += "背瓣覆盖异常,";
                if (tdAntParaItem.告警状态 == "")
                    tdAntParaItem.告警状态 = "二级告警";
                if (double.Parse(tdAntParaItem.采样点总数_路测) * 采样点比例_180_路测 >= 12 &&
                    (double.Parse(tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_150_路测) >= 85
                    || double.Parse(tdAntParaItem.范围内覆盖率PCCPCHRSCPC2I_3_150_路测) >= 90
                    || double.Parse(tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_60_路测) <= 85))
                {
                    if (!tdAntParaItem.分析结果.Contains("背瓣覆盖异常"))
                    {
                        tdAntParaItem.分析结果 += "背瓣覆盖异常,";
                    }
                    tdAntParaItem.告警状态 = "一级告警";
                }
            }
        }

        private void dealBacklobeScan(TdAntParaItem tdAntParaItem, int iSampleNumDef)
        {
            double 采样点比例_180_扫频;
            if (tdAntParaItem.范围内采样点比例_180_扫频 != ""
                && double.TryParse(tdAntParaItem.范围内采样点比例_180_扫频, out 采样点比例_180_扫频)
                && double.Parse(tdAntParaItem.采样点总数_扫频) >= iSampleNumDef && 采样点比例_180_扫频 >= 8
                && double.Parse(tdAntParaItem.范围内平均覆盖距离_180_扫频) >= 50 && tdAntParaItem.前后比_扫频 != ""
                && tdAntParaItem.前后比_扫频 != "-" && double.Parse(tdAntParaItem.前后比_扫频) <= 5)
            {
                if (!tdAntParaItem.分析结果.Contains("背瓣覆盖异常"))
                {
                    tdAntParaItem.分析结果 += "背瓣覆盖异常,";
                }
                if (tdAntParaItem.告警状态 == "")
                    tdAntParaItem.告警状态 = "二级告警";
                if (double.Parse(tdAntParaItem.采样点总数_扫频) * 采样点比例_180_扫频 >= 12 &&
                    (double.Parse(tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_150_扫频) >= 85
                    || double.Parse(tdAntParaItem.范围内覆盖率PCCPCHRSCPC2I_3_150_扫频) >= 90
                    || double.Parse(tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_60_扫频) <= 85))
                {
                    if (!tdAntParaItem.分析结果.Contains("背瓣覆盖异常"))
                    {
                        tdAntParaItem.分析结果 += "背瓣覆盖异常,";
                    }
                    tdAntParaItem.告警状态 = "一级告警";
                }
            }
        }

        private void 室分泄露问题分析(ref TdAntParaItem tdAntParaItem, string strCityLevel)
        {
            int iSampleNumDef = 0;
            if (strCityLevel == "一类")
                iSampleNumDef = 50;
            else
                iSampleNumDef = 30;

            double dSampleCount;
            if (double.TryParse(tdAntParaItem.采样点总数_路测, out dSampleCount)
                && dSampleCount >= iSampleNumDef && dSampleCount <= 1000)
            {
                tdAntParaItem.分析结果 += "室分信号泄露,";
                if (tdAntParaItem.告警状态 == "")
                    tdAntParaItem.告警状态 = "二级告警";
                if (double.Parse(tdAntParaItem.覆盖率PCCPCHRSCP90_C_I_3_路测) <= 85)
                {
                    if (!tdAntParaItem.分析结果.Contains("室分信号泄露"))
                    {
                        tdAntParaItem.分析结果 += "室分信号泄露,";
                    }
                    tdAntParaItem.告警状态 = "一级告警";
                }
            }
        }

        private void 天线工参核查(ref TdAntParaItem tdAntParaItem)
        {
            double dSampleCount;
            if (double.TryParse(tdAntParaItem.采样点总数_路测, out dSampleCount))
            {
                double 采样点比例_60_路测 = 0;
                if (dSampleCount > 300)
                {
                    if (tdAntParaItem.范围内采样点比例_60_路测 != "")
                    {
                        double.TryParse(tdAntParaItem.范围内采样点比例_60_路测, out 采样点比例_60_路测);
                    }
                    if (采样点比例_60_路测 < 40)
                    {
                        tdAntParaItem.分析结果 += "天线工参核查,";
                        tdAntParaItem.告警状态 = "一级告警";
                    }
                }
            }
        }
        #endregion

        #region 整理导出数据

        private List<NPOIRow> fillAntAgleData(TdAntParaItem tdAntParaItem, string strDt_Scan)
        {
            List<NPOIRow> dataAntAngle = new List<NPOIRow>();
            NPOIRow nr360;

            List<object> objsL360 = new List<object>();
            objsL360.Add(tdAntParaItem.地市);
            objsL360.Add(tdAntParaItem.小区名称);
            objsL360.Add("PCCPCH RSCP" + strDt_Scan);
            ZTTdAntenna.CellInfoItem cellInfoItem = null;
            cellInfoItem = strDt_Scan == "_Scan" ? tdAntParaItem.scanCellInfoItem : tdAntParaItem.dtCellInfoItem;
            if (cellInfoItem == null)
            {
                cellInfoItem = new ZTTdAntenna.CellInfoItem();
            }
            getValidData(objsL360, cellInfoItem.rscpArray, cellInfoItem.samplNumArray);
            nr360 = new NPOIRow();
            nr360.cellValues = objsL360;
            dataAntAngle.Add(nr360);

            objsL360 = new List<object>();
            objsL360.Add(tdAntParaItem.地市);
            objsL360.Add(tdAntParaItem.小区名称);
            objsL360.Add("通信距离" + strDt_Scan);
            getValidData(objsL360, cellInfoItem.samplArray, cellInfoItem.samplNumArray);
            nr360 = new NPOIRow();
            nr360.cellValues = objsL360;
            dataAntAngle.Add(nr360);

            if (!ZTGsmAntenna.isScanStat)
            {
                objsL360 = new List<object>();
                objsL360.Add(tdAntParaItem.地市);
                objsL360.Add(tdAntParaItem.小区名称);
                objsL360.Add("DPCH RSCP_Dt");
                getValidData(objsL360, cellInfoItem.dpchRscpArray, cellInfoItem.samplNumArray);
                nr360 = new NPOIRow();
                nr360.cellValues = objsL360;
                dataAntAngle.Add(nr360);

                objsL360 = new List<object>();
                objsL360.Add(tdAntParaItem.地市);
                objsL360.Add(tdAntParaItem.小区名称);
                objsL360.Add("TD BLER_Dt");
                getValidData(objsL360, cellInfoItem.blerArray, cellInfoItem.samplNumArray);
                nr360 = new NPOIRow();
                nr360.cellValues = objsL360;
                dataAntAngle.Add(nr360);

                objsL360 = new List<object>();
                objsL360.Add(tdAntParaItem.地市);
                objsL360.Add(tdAntParaItem.小区名称);
                objsL360.Add("过覆盖指数_Dt");
                getValidData(objsL360, cellInfoItem.coverArray, cellInfoItem.samplNumArray);
                nr360 = new NPOIRow();
                nr360.cellValues = objsL360;
                dataAntAngle.Add(nr360);

                objsL360 = new List<object>();
                objsL360.Add(tdAntParaItem.地市);
                objsL360.Add(tdAntParaItem.小区名称);
                objsL360.Add("DPCH C/I_Dt");
                getValidData(objsL360, cellInfoItem.dpchc2iArray, cellInfoItem.samplNumArray);
                nr360 = new NPOIRow();
                nr360.cellValues = objsL360;
                dataAntAngle.Add(nr360);
            }
            else
            {
                objsL360 = new List<object>();
                objsL360.Add(tdAntParaItem.地市);
                objsL360.Add(tdAntParaItem.小区名称);
                objsL360.Add("平滑PCCPCH RSCP_Scan");
                getValidData(objsL360, cellInfoItem.rscpNewArray, cellInfoItem.samplNumArray);
                nr360 = new NPOIRow();
                nr360.cellValues = objsL360;
                dataAntAngle.Add(nr360);

                objsL360 = new List<object>();
                objsL360.Add(tdAntParaItem.地市);
                objsL360.Add(tdAntParaItem.小区名称);
                objsL360.Add("PCCPCH C/I_Scan");
                getValidData(objsL360, cellInfoItem.pcchc2iArrayAll, cellInfoItem.samplNumArray);
                nr360 = new NPOIRow();
                nr360.cellValues = objsL360;
                dataAntAngle.Add(nr360);

                objsL360 = new List<object>();
                objsL360.Add(tdAntParaItem.地市);
                objsL360.Add(tdAntParaItem.小区名称);
                objsL360.Add("PCCPCH Ec/Io_Scan");
                getValidData(objsL360, cellInfoItem.pcchc2iArrayAll_EcIo, cellInfoItem.samplNumArray);
                nr360 = new NPOIRow();
                nr360.cellValues = objsL360;
                dataAntAngle.Add(nr360);
            }

            return dataAntAngle;
        }

        private void getValidData(List<object> objsL360, int[] sumArray, int[] numArray)
        {
            for (int i = 0; i < 360; i++)
            {
                objsL360.Add(Math.Round(numArray[i] == 0 ? 0 : sumArray[i] * 1.0 / numArray[i], 2));
            }
        }

        private void getValidData(List<object> objsL360, double[] sumArray, int[] numArray)
        {
            for (int i = 0; i < 360; i++)
            {
                objsL360.Add(Math.Round(numArray[i] == 0 ? 0 : sumArray[i] / numArray[i], 2));
            }
        }

        private List<object> fillCellData(TdAntParaItem tdAntParaItem)
        {
            List<object> objsCell = new List<object>();
            objsCell.Add(tdAntParaItem.序号);
            objsCell.Add(tdAntParaItem.地市);
            objsCell.Add(tdAntParaItem.基站名称);
            objsCell.Add(tdAntParaItem.小区名称);
            objsCell.Add(tdAntParaItem.小区LAC);
            objsCell.Add(tdAntParaItem.小区CI);
            objsCell.Add(tdAntParaItem.覆盖类型);
            objsCell.Add(tdAntParaItem.小区主频);
            objsCell.Add(tdAntParaItem.天线经度);
            objsCell.Add(tdAntParaItem.天线纬度);
            objsCell.Add(tdAntParaItem.方位角);
            objsCell.Add(tdAntParaItem.下倾角);
            objsCell.Add(tdAntParaItem.挂高);
            objsCell.Add(tdAntParaItem.分析结果);
            objsCell.Add(tdAntParaItem.告警状态);
            objsCell.Add(tdAntParaItem.采样点总数_路测);
            objsCell.Add(tdAntParaItem.覆盖率PCCPCHRSCP90_C_I_3_路测);
            objsCell.Add(tdAntParaItem.覆盖率PCCPCHRSCP95_C_I_3_路测);
            objsCell.Add(tdAntParaItem.覆盖率DPCHRSCP90_C_I_3_路测);
            objsCell.Add(tdAntParaItem.覆盖率DPCHRSCP95_C_I_3_路测);
            objsCell.Add(tdAntParaItem.小区PCCPCHRSCP均值_路测);
            objsCell.Add(tdAntParaItem.小区PCCPCH_C2I_3占比_路测);
            objsCell.Add(tdAntParaItem.小区PCCPCH_C_I平均_路测);
            objsCell.Add(tdAntParaItem.小区平均通信距离_路测);
            objsCell.Add(tdAntParaItem.范围内采样点比例_60_路测);
            objsCell.Add(tdAntParaItem.范围内平均覆盖距离_60_路测);
            objsCell.Add(tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_60_路测);
            objsCell.Add(tdAntParaItem.范围内平均PCCPCHRSCP_60_路测);
            objsCell.Add(tdAntParaItem.范围内采样点比例_150_路测);
            objsCell.Add(tdAntParaItem.范围内平均覆盖距离_150_路测);
            objsCell.Add(tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_150_路测);
            objsCell.Add(tdAntParaItem.范围内平均PCCPCHRSCP_150_路测);
            objsCell.Add(tdAntParaItem.范围内采样点比例_180_路测);
            objsCell.Add(tdAntParaItem.范围内平均覆盖距离_180_路测);
            objsCell.Add(tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_180_路测);
            objsCell.Add(tdAntParaItem.范围内平均PCCPCHRSCP_180_路测);
            objsCell.Add(tdAntParaItem.前后比_路测);
            objsCell.Add(tdAntParaItem.采样点总数_扫频);
            objsCell.Add(tdAntParaItem.覆盖率PCCPCHRSCP90_C_I_3_扫频);
            objsCell.Add(tdAntParaItem.覆盖率PCCPCHRSCP95_C_I_3_扫频);
            objsCell.Add(tdAntParaItem.小区PCCPCHRSCP均值_扫频);
            objsCell.Add(tdAntParaItem.小区PCCPCH_C2I_3占比_扫频);
            objsCell.Add(tdAntParaItem.小区PCCPCH_C_I平均_扫频);
            objsCell.Add(tdAntParaItem.小区平均通信距离_扫频);
            objsCell.Add(tdAntParaItem.范围内采样点比例_60_扫频);
            objsCell.Add(tdAntParaItem.范围内平均覆盖距离_60_扫频);
            objsCell.Add(tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_60_扫频);
            objsCell.Add(tdAntParaItem.范围内平均PCCPCHRSCP_60_扫频);
            objsCell.Add(tdAntParaItem.范围内采样点比例_150_扫频);
            objsCell.Add(tdAntParaItem.范围内平均覆盖距离_150_扫频);
            objsCell.Add(tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_150_扫频);
            objsCell.Add(tdAntParaItem.范围内平均PCCPCHRSCP_150_扫频);
            objsCell.Add(tdAntParaItem.范围内采样点比例_180_扫频);
            objsCell.Add(tdAntParaItem.范围内平均覆盖距离_180_扫频);
            objsCell.Add(tdAntParaItem.范围内覆盖率PCCPCHRSCP90_C_I_3_180_扫频);
            objsCell.Add(tdAntParaItem.范围内平均PCCPCHRSCP_180_扫频);
            objsCell.Add(tdAntParaItem.前后比_扫频);
            objsCell.Add(tdAntParaItem.paraCellInfoIem.STR_TD语音业务无线掉话率);
            objsCell.Add(tdAntParaItem.paraCellInfoIem.STR_PS域无线掉线率);
            objsCell.Add(tdAntParaItem.paraCellInfoIem.STR_RRC连接建立成功率);
            objsCell.Add(tdAntParaItem.paraCellInfoIem.STR_RAB建立成功率);
            objsCell.Add(tdAntParaItem.paraCellInfoIem.STR_无线接通率);
            objsCell.Add(tdAntParaItem.paraCellInfoIem.STR_PS域接通率);
            objsCell.Add(tdAntParaItem.paraCellInfoIem.STR_PS域下行重行率);
            objsCell.Add(tdAntParaItem.paraCellInfoIem.STR_上行电路域误块率);
            objsCell.Add(tdAntParaItem.paraCellInfoIem.STR_上行分组域误块率);
            objsCell.Add(tdAntParaItem.paraCellInfoIem.STR_TD网内RNC间切换出成功率);
            objsCell.Add(tdAntParaItem.paraCellInfoIem.STR_TD网内RNC间硬切换入成功率);
            objsCell.Add(tdAntParaItem.paraCellInfoIem.STR_RNC内小区间切换成功率);
            objsCell.Add(tdAntParaItem.paraCellInfoIem.STR_小区间切换出成功率);
            objsCell.Add(tdAntParaItem.paraCellInfoIem.STR_小区间切换入成功率);
            objsCell.Add(tdAntParaItem.mrCellInfoIem.S弱覆盖采样点比例);
            objsCell.Add(tdAntParaItem.mrCellInfoIem.S良好覆盖采样点比例);
            objsCell.Add(tdAntParaItem.mrCellInfoIem.S下行平均PCCPCH_RSCP);
            objsCell.Add(tdAntParaItem.mrCellInfoIem.S下行时隙干扰采样点比例);
            objsCell.Add(tdAntParaItem.mrCellInfoIem.S上行时隙干扰采样点比例);
            objsCell.Add(tdAntParaItem.mrCellInfoIem.SUPPTS干扰采样点比例);
            objsCell.Add(tdAntParaItem.mrCellInfoIem.SUE高发射功率占比);
            objsCell.Add(tdAntParaItem.mrCellInfoIem.SPS业务高误块率采样点占比);

            return objsCell;
        }

        private List<object> 天线综合分析总表字段配置()
        {
            List<object> objsCell = new List<object>();
            objsCell.Add("序号");
            objsCell.Add("地市");
            objsCell.Add("基站名称");
            objsCell.Add("小区名称");
            objsCell.Add("LAC");
            objsCell.Add("CI");
            objsCell.Add("覆盖类型");
            objsCell.Add("小区主频");
            objsCell.Add("天线经度");
            objsCell.Add("天线纬度");
            objsCell.Add("方位角");
            objsCell.Add("下倾角");
            objsCell.Add("挂高");
            objsCell.Add("分析结果");
            objsCell.Add("告警状态");
            objsCell.Add("采样点总数(路测)");
            objsCell.Add("覆盖率(PCCPCH RSCP>=-90 & C/I>=-3)(%)(路测)");
            objsCell.Add("覆盖率(PCCPCH RSCP>=-95 & C/I>=-3)(%)(路测)");
            objsCell.Add("覆盖率(DPCH RSCP>=-90 & C/I>=-3)(%)(路测)");
            objsCell.Add("覆盖率(DPCH RSCP>=-95 & C/I>=-3)(%)(路测)");
            objsCell.Add("小区PCCPCH RSCP均值(路测)");
            objsCell.Add("小区PCCPCH C2I<=-3占比(%)(路测)");
            objsCell.Add("小区PCCPCH C/I平均(路测)");
            objsCell.Add("小区平均通信距离(路测)");
            objsCell.Add("±(0，60°)范围内采样点比例(路测)");
            objsCell.Add("±(0，60°)范围内平均覆盖距离(路测)");
            objsCell.Add("±(0，60°)范围内覆盖率(PCCPCH RSCP>=-90 & C/I>=-3)(%)(路测)");
            objsCell.Add("±(0，60°)范围内PCCPCH RSCP均值(路测)");
            objsCell.Add("±(60，150°)范围内采样点比例(路测)");
            objsCell.Add("±(60，150°)范围内平均覆盖距离(路测)");
            objsCell.Add("±(60，150°)范围内覆盖率(PCCPCH RSCP>=-90 & C/I>=-3)(%)(路测)");
            objsCell.Add("±(60，150°)范围内PCCPCH RSCP均值(路测)");
            objsCell.Add("±(150，180°)范围内采样点比例(路测)");
            objsCell.Add("±(150，180°)范围内平均覆盖距离(路测)");
            objsCell.Add("±(150，180°)范围内覆盖率(PCCPCH RSCP>=-90 & C/I>=-3)(%)(路测)");
            objsCell.Add("±(150，180°)范围内PCCPCH RSCP均值(路测)");
            objsCell.Add("前后比(路测)");
            objsCell.Add("采样点总数(扫频)");
            objsCell.Add("覆盖率(PCCPCH RSCP>=-90 & C/I>=-3)(%)(扫频)");
            objsCell.Add("覆盖率(PCCPCH RSCP>=-95 & C/I>=-3)(%)(扫频)");
            objsCell.Add("小区PCCPCH RSCP均值(扫频)");
            objsCell.Add("小区PCCPCH C2I<=-3占比(%)(扫频)");
            objsCell.Add("小区PCCPCH C/I平均(扫频)");
            objsCell.Add("小区平均通信距离(扫频)");
            objsCell.Add("±(0，60°)范围内采样点比例(扫频)");
            objsCell.Add("±(0，60°)范围内平均覆盖距离(扫频)");
            objsCell.Add("±(0，60°)范围内覆盖率(PCCPCH RSCP>=-90 & C/I>=-3)(%)(扫频)");
            objsCell.Add("±(0，60°)范围内PCCPCH RSCP均值(扫频)");
            objsCell.Add("±(60，150°)范围内采样点比例(扫频)");
            objsCell.Add("±(60，150°)范围内平均覆盖距离(扫频)");
            objsCell.Add("±(60，150°)范围内覆盖率(PCCPCH RSCP>=-90 & C/I>=-3)(%)(扫频)");
            objsCell.Add("±(60，150°)范围内PCCPCH RSCP均值(扫频)");
            objsCell.Add("±(150，180°)范围内采样点比例(扫频)");
            objsCell.Add("±(150，180°)范围内平均覆盖距离(扫频)");
            objsCell.Add("±(150，180°)范围内覆盖率(PCCPCH RSCP>=-90 & C/I>=-3)(%)(扫频)");
            objsCell.Add("±(150，180°)范围内PCCPCH RSCP均值(扫频)");
            objsCell.Add("前后比(扫频)");
            objsCell.Add("TD语音业务无线掉话率");
            objsCell.Add("PS域无线掉线率");
            objsCell.Add("RRC连接建立成功率");
            objsCell.Add("RAB建立成功率");
            objsCell.Add("无线接通率");
            objsCell.Add("PS域接通率");
            objsCell.Add("PS域下行重行率");
            objsCell.Add("上行电路域误块率");
            objsCell.Add("上行分组域误块率");
            objsCell.Add("TD网内RNC间切换出成功率");
            objsCell.Add("TD网内RNC间硬切换入成功率");
            objsCell.Add("RNC内小区间切换成功率");
            objsCell.Add("小区间切换出成功率");
            objsCell.Add("小区间切换入成功率");
            objsCell.Add("F弱覆盖采样点比例");
            objsCell.Add("F良好覆盖采样点比例");
            objsCell.Add("F下行平均PCCPCH_RSCP");
            objsCell.Add("F下行时隙干扰采样点比例");
            objsCell.Add("F上行时隙干扰采样点比例");
            objsCell.Add("FUPPTS干扰采样点比例");
            objsCell.Add("FUE高发射功率占比");
            objsCell.Add("FPS业务高误块率采样点占比");

            return objsCell;
        }

        private void dealMainUtranCellSample()
        {
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 10;
            List<NPOIRow> dataCell = new List<NPOIRow>();
            NPOIRow nrCell = new NPOIRow();
            List<object> colCell = 天线综合分析总表字段配置();
            nrCell.cellValues = colCell;
            dataCell.Add(nrCell);

            List<NPOIRow> dataAngle = new List<NPOIRow>();
            NPOIRow nrAngle = new NPOIRow();
            List<object> colAngle = new List<object>();
            colAngle.Add("地市");
            colAngle.Add("小区名称");
            colAngle.Add("指标项");
            for (int i = 0; i < 360; i++)
            {
                colAngle.Add(i.ToString() + "°");
            }
            nrAngle.cellValues = colAngle;
            dataAngle.Add(nrAngle);

            int index = 0;
            int iCount = cellAntParaInfoDic.Keys.Count / 100 + 1;
            foreach (LaiKey cellKey in cellAntParaInfoDic.Keys)
            {
                try
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    WaitBox.ProgressPercent = index++ / iCount;
                    NPOIRow nrCellData = new NPOIRow();
                    nrCellData.cellValues = fillCellData(cellAntParaInfoDic[cellKey]);
                    dataCell.Add(nrCellData);
                    //天线360°角度信息
                    dataAngle.AddRange(fillAntAgleData(cellAntParaInfoDic[cellKey], "_Scan"));
                    dataAngle.AddRange(fillAntAgleData(cellAntParaInfoDic[cellKey], "_Dt"));
                }
                catch (Exception ex)
                {
                    log.Error(ex.Message);
                }
            }
            nrDatasList.Add(dataCell);
            nrDatasList.Add(dataAngle);
            sheetNames.Add("天线综合分析总表");
            sheetNames.Add("小区角度数据");
            WaitBox.Close();
        }

        #endregion

        private void FireShowResultForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(TdAntParaCommonForm).FullName);
            TdAntParaCommonForm form = obj == null ? null : obj as TdAntParaCommonForm;
            if (form == null || form.IsDisposed)
            {
                form = new TdAntParaCommonForm(MainModel);
            }
            form.nrDatasList = nrDatasList;
            form.sheetNames = sheetNames;

            form.FillData(cellAntParaInfoDic);
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }
    }
}
