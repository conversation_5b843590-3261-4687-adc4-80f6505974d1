﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRReasonPanelBase : UserControl
    {
        public NRReasonPanelBase()
        {
            InitializeComponent();
        }

        protected virtual string Title
        {
            get { return grp.Text; }
            set { grp.Text = value; }
        }

        protected NRReasonBase reason = null;
        public virtual void AttachReason(NRReasonBase reason)
        {
            this.reason = reason;
            this.Title = reason.Name;
        }
    }
}
