﻿namespace MasterCom.RAMS.ZTFunc.LteSignalImsi
{
    partial class FileImsiDrawLineSetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.Label label5;
            System.Windows.Forms.Label label7;
            System.Windows.Forms.Label label1;
            this.cbxGisInterval = new System.Windows.Forms.CheckBox();
            this.numberInterval = new System.Windows.Forms.NumericUpDown();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            label5 = new System.Windows.Forms.Label();
            label7 = new System.Windows.Forms.Label();
            label1 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numberInterval)).BeginInit();
            this.SuspendLayout();
            // 
            // cbxGisInterval
            // 
            this.cbxGisInterval.AutoSize = true;
            this.cbxGisInterval.Location = new System.Drawing.Point(81, 25);
            this.cbxGisInterval.Name = "cbxGisInterval";
            this.cbxGisInterval.Size = new System.Drawing.Size(138, 16);
            this.cbxGisInterval.TabIndex = 21;
            this.cbxGisInterval.Text = "启用GIS连线间隔限制";
            this.cbxGisInterval.UseVisualStyleBackColor = true;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new System.Drawing.Point(69, 64);
            label5.Name = "label5";
            label5.Size = new System.Drawing.Size(65, 12);
            label5.TabIndex = 31;
            label5.Text = "间隔时长≤";
            // 
            // numberInterval
            // 
            this.numberInterval.Location = new System.Drawing.Point(140, 60);
            this.numberInterval.Maximum = new decimal(new int[] {
            1440,
            0,
            0,
            0});
            this.numberInterval.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numberInterval.Name = "numberInterval";
            this.numberInterval.Size = new System.Drawing.Size(55, 21);
            this.numberInterval.TabIndex = 29;
            this.numberInterval.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numberInterval.Value = new decimal(new int[] {
            2,
            0,
            0,
            0});
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Location = new System.Drawing.Point(201, 64);
            label7.Name = "label7";
            label7.Size = new System.Drawing.Size(29, 12);
            label7.TabIndex = 30;
            label7.Text = "分钟";
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(226, 135);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 32;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(43, 98);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(233, 12);
            label1.TabIndex = 33;
            label1.Text = "(超过间隔时长的两个事件之间不绘制连线)";
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(140, 135);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 34;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            // 
            // FileImsiDrawLineSetForm
            // 
            this.Appearance.Font = new System.Drawing.Font("SimSun", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(313, 170);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(label1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.cbxGisInterval);
            this.Controls.Add(label5);
            this.Controls.Add(this.numberInterval);
            this.Controls.Add(label7);
            this.Name = "FileImsiDrawLineSetForm";
            this.Text = "GIS连线间隔设置";
            ((System.ComponentModel.ISupportInitialize)(this.numberInterval)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.CheckBox cbxGisInterval;
        private System.Windows.Forms.NumericUpDown numberInterval;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
    }
}