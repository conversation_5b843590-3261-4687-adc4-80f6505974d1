﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTNotHoTopLevCell
{
    public partial class NotHoTopLevCellListForm : MinCloseForm
    {
        public NotHoTopLevCellListForm()
            : base()
        {
            InitializeComponent();
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.gv);
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }

            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gv.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }

            NotHoTopLevCellInfo item = gv.GetRow(info.RowHandle) as NotHoTopLevCellInfo;
            MainModel.ClearDTData();
            foreach (TestPoint tp in item.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            MainModel.DTDataManager.Add(item.HoSuccessEvt);

            if (item.TestPoints[0] is TDTestPointDetail)
            {
                MainModel.FireSetDefaultMapSerialTheme("TD_PCCPCH_RSCP");
            }
            else
            {
                MainModel.FireSetDefaultMapSerialTheme("GSM RxLevSub");
            }
            MainModel.FireDTDataChanged(this);
        }

        internal void FillData(List<NotHoTopLevCellInfo> resultSet)
        {
            gridCtrl.DataSource = resultSet;
            gridCtrl.RefreshDataSource();
        }
    }
}
