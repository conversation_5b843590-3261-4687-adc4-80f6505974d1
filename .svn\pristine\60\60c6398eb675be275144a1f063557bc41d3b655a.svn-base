﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Collections;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTAnaTpAmongEvents
    {
        public delegate void DealTimePeriodPnts(List<TimePeriodPoints> TimePeriodPnts, DTFileDataManager file);
        private readonly DealTimePeriodPnts delegateMethod = null;
        private readonly MainModel mainModel = null;

        private readonly TimeOrder order = new TimeOrder();
        private List<int> eventIDLst;
        public List<TimePeriodPoints> TpPntList { get; set; }
        public List<Event> EventLst { get; set; }

        public ZTAnaTpAmongEvents(DealTimePeriodPnts delegateMethod, MainModel mainModel)
        {
            this.delegateMethod = delegateMethod;
            this.mainModel = mainModel;
            this.TpPntList = new List<TimePeriodPoints>();
            this.eventIDLst = new List<int>();
            this.EventLst = new List<Event>();
        }

        public void SetEventID(List<int> eventIDLst)
        {
            this.eventIDLst = eventIDLst;
        }

        public void DealData()
        {
            List<TimePeriodPoints> tpPntList;
            List<Event> eventLst;
            foreach (DTFileDataManager file in mainModel.DTDataManager.FileDataManagers)
            {
                getEvents(file.Events, out eventLst);
                EventLst = eventLst;
                getTpPnts(EventLst, out tpPntList);
                TpPntList = tpPntList;
                int idx = 0;
                foreach (TestPoint tp in file.TestPoints)
                {
                    if (idx >= TpPntList.Count) break;
                    distributePnts(tp, TpPntList, ref idx);
                }
                
                if (delegateMethod != null)
                {
                    delegateMethod(TpPntList, file);
                }
            }
        }

        private void getEvents(List<Event> events, out List<Event> outEvent)
        {
            outEvent = new List<Event>();
            foreach (Event e in events)
            {
                if (eventIDLst.Contains(e.ID))
                {
                    outEvent.Add(e);
                }
            }
            outEvent.Sort(order);
        }

        private void getTpPnts(List<Event> events, out List<TimePeriodPoints> outTpPnts)
        {
            outTpPnts = new List<TimePeriodPoints>();
            Event ePrev = null;
            foreach (Event e in events)
            {
                if (ePrev == null)
                {
                    ePrev = e;
                    continue;
                }
                outTpPnts.Add(new TimePeriodPoints(ePrev, e));
                ePrev = e;
            }
            if(ePrev != null)
                outTpPnts.Add(new TimePeriodPoints(ePrev, ePrev));
        }

        private void distributePnts(TestPoint tp, List<TimePeriodPoints> tpPnts, ref int idx)
        {
            TimePeriodPoints timePeriodPnt = getTimePeriodPnt(tp, tpPnts, ref idx);
            if (timePeriodPnt == null) return;
            timePeriodPnt.AddTestPoint(tp);
        }

        private TimePeriodPoints getTimePeriodPnt(TestPoint tp, List<TimePeriodPoints> tpPnts, ref int idx)
        {
            TimePeriodPoints timePeriodPnt = null;
            while (idx < tpPnts.Count)
            {
                if (tpPnts[idx].IsInside(tp))
                {
                    timePeriodPnt = tpPnts[idx];
                    break;
                }
                else if (tpPnts[idx].IsPrev(tp))
                {
                    timePeriodPnt = null;
                    break;
                }
                idx++;
            }
            return timePeriodPnt;
        }

        public class TimeOrder : IComparer<Event>
        {

            #region IComparer<Event> 成员

            public int Compare(Event x, Event y)
            {
                return x.DateTime.CompareTo(y.DateTime);
            }

            #endregion
        }
    }

    public class TimePeriodPoints
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public List<TestPoint> TestPnts { get; set; }

        public Event eStart { get; set; }
        public Event eEnd { get; set; }

        public TimePeriodPoints(Event eStart, Event eEnd)
        {
            this.eStart = eStart;
            this.eEnd = eEnd;
            this.StartTime = eStart.DateTime;
            this.EndTime = eEnd.DateTime;
            TestPnts = new List<TestPoint>();
        }

        public void AddTestPoint(TestPoint tp)
        {
            TestPnts.Add(tp);
        }

        public bool IsPrev(TestPoint tp)
        {
            return tp.DateTime < StartTime;
        }

        public bool IsInside(TestPoint tp)
        {
            return tp.DateTime >= StartTime && tp.DateTime <= EndTime;
        }
    }
}
