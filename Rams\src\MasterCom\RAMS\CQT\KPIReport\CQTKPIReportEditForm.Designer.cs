﻿namespace MasterCom.RAMS.CQT
{
    partial class CQTKPIReportEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CQTKPIReportEditForm));
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.dataGridViewNavi = new System.Windows.Forms.DataGridView();
            this.ColumnName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.btnDelColumn = new DevExpress.XtraEditors.SimpleButton();
            this.btnAddColumn = new DevExpress.XtraEditors.SimpleButton();
            this.cqtkpiColumnPanel_PK = new MasterCom.RAMS.CQT.CQTKPIColumnPanel_PK();
            this.barManager = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barEditItemCurReport = new DevExpress.XtraBars.BarEditItem();
            this.comboBoxReports = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.biDelelte = new DevExpress.XtraBars.BarButtonItem();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.biNew = new DevExpress.XtraBars.BarButtonItem();
            this.biOpen = new DevExpress.XtraBars.BarButtonItem();
            this.biSave = new DevExpress.XtraBars.BarButtonItem();
            this.biSaveAs = new DevExpress.XtraBars.BarButtonItem();
            this.biSaveAll = new DevExpress.XtraBars.BarButtonItem();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewNavi)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxReports)).BeginInit();
            this.SuspendLayout();
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 26);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.dataGridViewNavi);
            this.splitContainerControl1.Panel1.Controls.Add(this.btnDelColumn);
            this.splitContainerControl1.Panel1.Controls.Add(this.btnAddColumn);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.AutoScroll = true;
            this.splitContainerControl1.Panel2.Controls.Add(this.cqtkpiColumnPanel_PK);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1019, 666);
            this.splitContainerControl1.SplitterPosition = 201;
            this.splitContainerControl1.TabIndex = 0;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // dataGridViewNavi
            // 
            this.dataGridViewNavi.AllowUserToAddRows = false;
            this.dataGridViewNavi.AllowUserToDeleteRows = false;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle1.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridViewNavi.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dataGridViewNavi.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewNavi.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.ColumnName});
            this.dataGridViewNavi.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.dataGridViewNavi.Location = new System.Drawing.Point(0, 35);
            this.dataGridViewNavi.MultiSelect = false;
            this.dataGridViewNavi.Name = "dataGridViewNavi";
            this.dataGridViewNavi.RowHeadersVisible = false;
            this.dataGridViewNavi.RowTemplate.Height = 23;
            this.dataGridViewNavi.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewNavi.ShowEditingIcon = false;
            this.dataGridViewNavi.Size = new System.Drawing.Size(201, 627);
            this.dataGridViewNavi.TabIndex = 2;
            this.dataGridViewNavi.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridViewNavi_CellClick);
            this.dataGridViewNavi.CellEndEdit += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridViewNavi_CellEndEdit);
            this.dataGridViewNavi.SelectionChanged += new System.EventHandler(this.dataGridViewNavi_SelectionChanged);
            // 
            // ColumnName
            // 
            this.ColumnName.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            this.ColumnName.DefaultCellStyle = dataGridViewCellStyle2;
            this.ColumnName.HeaderText = "报告列";
            this.ColumnName.Name = "ColumnName";
            this.ColumnName.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
            // 
            // btnDelColumn
            // 
            this.btnDelColumn.Location = new System.Drawing.Point(103, 5);
            this.btnDelColumn.Name = "btnDelColumn";
            this.btnDelColumn.Size = new System.Drawing.Size(87, 27);
            this.btnDelColumn.TabIndex = 1;
            this.btnDelColumn.Text = "删除列";
            this.btnDelColumn.Click += new System.EventHandler(this.btnDelColumn_Click);
            // 
            // btnAddColumn
            // 
            this.btnAddColumn.Location = new System.Drawing.Point(10, 5);
            this.btnAddColumn.Name = "btnAddColumn";
            this.btnAddColumn.Size = new System.Drawing.Size(87, 27);
            this.btnAddColumn.TabIndex = 1;
            this.btnAddColumn.Text = "增加列";
            this.btnAddColumn.Click += new System.EventHandler(this.btnAddColumn_Click);
            // 
            // cqtkpiColumnPanel_PK
            // 
            this.cqtkpiColumnPanel_PK.AutoScroll = true;
            this.cqtkpiColumnPanel_PK.Location = new System.Drawing.Point(2, -2);
            this.cqtkpiColumnPanel_PK.MinimumSize = new System.Drawing.Size(654, 662);
            this.cqtkpiColumnPanel_PK.Name = "cqtkpiColumnPanel_PK";
            this.cqtkpiColumnPanel_PK.Size = new System.Drawing.Size(654, 662);
            this.cqtkpiColumnPanel_PK.TabIndex = 0;
            this.cqtkpiColumnPanel_PK.Visible = false;
            // 
            // barManager
            // 
            this.barManager.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1,
            this.bar2});
            this.barManager.DockControls.Add(this.barDockControlTop);
            this.barManager.DockControls.Add(this.barDockControlBottom);
            this.barManager.DockControls.Add(this.barDockControlLeft);
            this.barManager.DockControls.Add(this.barDockControlRight);
            this.barManager.Form = this;
            this.barManager.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.biNew,
            this.biOpen,
            this.biSave,
            this.barEditItemCurReport,
            this.biSaveAs,
            this.biSaveAll,
            this.biDelelte});
            this.barManager.MainMenu = this.bar2;
            this.barManager.MaxItemId = 7;
            this.barManager.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.comboBoxReports});
            // 
            // bar1
            // 
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 1;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(284, 190);
            this.bar1.FloatSize = new System.Drawing.Size(46, 24);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.Width, this.barEditItemCurReport, "", false, true, true, 197),
            new DevExpress.XtraBars.LinkPersistInfo(this.biDelelte)});
            this.bar1.Offset = 157;
            this.bar1.Text = "Tools";
            // 
            // barEditItemCurReport
            // 
            this.barEditItemCurReport.Caption = "当前报表：";
            this.barEditItemCurReport.Edit = this.comboBoxReports;
            this.barEditItemCurReport.EditorShowMode = DevExpress.Utils.EditorShowMode.MouseDownFocused;
            this.barEditItemCurReport.Id = 3;
            this.barEditItemCurReport.Name = "barEditItemCurReport";
            this.barEditItemCurReport.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.Caption;
            this.barEditItemCurReport.EditValueChanged += new System.EventHandler(this.barEditItemCurReport_EditValueChanged);
            // 
            // comboBoxReports
            // 
            this.comboBoxReports.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxReports.Name = "comboBoxReports";
            // 
            // biDelelte
            // 
            this.biDelelte.Caption = "删除";
            this.biDelelte.Id = 6;
            this.biDelelte.Name = "biDelelte";
            this.biDelelte.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.biDelelte_ItemClick);
            // 
            // bar2
            // 
            this.bar2.BarName = "Main menu";
            this.bar2.DockCol = 0;
            this.bar2.DockRow = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar2.FloatLocation = new System.Drawing.Point(76, 153);
            this.bar2.FloatSize = new System.Drawing.Size(145, 24);
            this.bar2.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.biNew),
            new DevExpress.XtraBars.LinkPersistInfo(this.biOpen),
            new DevExpress.XtraBars.LinkPersistInfo(this.biSave),
            new DevExpress.XtraBars.LinkPersistInfo(this.biSaveAs),
            new DevExpress.XtraBars.LinkPersistInfo(this.biSaveAll)});
            this.bar2.OptionsBar.MultiLine = true;
            this.bar2.Text = "Main menu";
            // 
            // biNew
            // 
            this.biNew.Caption = "新建";
            this.biNew.Id = 0;
            this.biNew.Name = "biNew";
            this.biNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.biNew_ItemClick);
            // 
            // biOpen
            // 
            this.biOpen.Caption = "打开";
            this.biOpen.Id = 1;
            this.biOpen.Name = "biOpen";
            this.biOpen.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.biOpen_ItemClick);
            // 
            // biSave
            // 
            this.biSave.Caption = "保存";
            this.biSave.Id = 2;
            this.biSave.Name = "biSave";
            this.biSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.biSave_ItemClick);
            // 
            // biSaveAs
            // 
            this.biSaveAs.Caption = "另存为";
            this.biSaveAs.Id = 4;
            this.biSaveAs.Name = "biSaveAs";
            this.biSaveAs.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.biSaveAs_ItemClick);
            // 
            // biSaveAll
            // 
            this.biSaveAll.Caption = "保存全部";
            this.biSaveAll.Id = 5;
            this.biSaveAll.Name = "biSaveAll";
            this.biSaveAll.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.biSaveAll_ItemClick);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Size = new System.Drawing.Size(1019, 26);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 692);
            this.barDockControlBottom.Size = new System.Drawing.Size(1019, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 26);
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 666);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1019, 26);
            this.barDockControlRight.Size = new System.Drawing.Size(0, 666);
            // 
            // CQTKPIReportEditForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("CQTKPIReportEditForm.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1019, 692);
            this.Controls.Add(this.splitContainerControl1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.MinimumSize = new System.Drawing.Size(871, 715);
            this.Name = "CQTKPIReportEditForm";
            this.Text = "报表设置";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewNavi)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxReports)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraBars.BarManager barManager;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraBars.BarButtonItem biNew;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem biOpen;
        private DevExpress.XtraBars.BarButtonItem biSave;
        private DevExpress.XtraBars.BarEditItem barEditItemCurReport;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox comboBoxReports;
        private DevExpress.XtraEditors.SimpleButton btnDelColumn;
        private DevExpress.XtraEditors.SimpleButton btnAddColumn;
        private DevExpress.XtraBars.BarButtonItem biSaveAs;
        private DevExpress.XtraBars.BarButtonItem biSaveAll;
        private DevExpress.XtraBars.BarButtonItem biDelelte;
        private CQTKPIColumnPanel_PK cqtkpiColumnPanel_PK;
        private System.Windows.Forms.DataGridView dataGridViewNavi;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnName;
    }
}