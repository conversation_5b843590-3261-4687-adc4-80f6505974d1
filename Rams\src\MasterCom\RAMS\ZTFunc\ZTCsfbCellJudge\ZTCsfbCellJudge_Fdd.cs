﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Text.RegularExpressions;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCsfbCellJudge_Fdd : ZTCsfbCellJudgeBase
    {
        public ZTCsfbCellJudge_Fdd(MainModel mainModel)
            : base(mainModel)
        {
            curLteServiceType = (int)ServiceType.LTE_FDD_VOICE;
            curFallServiceType = (int)ServiceType.WCDMA_VOICE;
            EvtIdMtCsfbLteRelease = 3073;
            EvtIdMoCsfbLteRelease = 3072;
            EvtIdLte_CellReselection_L2G = 3302;//L2W FDD回落WCDMA
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26066, this.Name);
        }

        protected override ICell getSrcICellByEvt(Event evt)
        {
            return CellManager.GetInstance().GetNearestWCell(evt.DateTime, (int?)evt["TargetLAC"]
                , (int?)evt["TargetCI"], evt.Longitude, evt.Latitude);
        }
        protected override void addInfoToResultList(ZTCsfbCellJudge judge, int maxRxLev, double maxLongitude, double maxLatitude)
        {
            WCell cell = iCell as WCell;
            if (cell == null)
            {
                return;
            }
            judge.Sort();
            if (judge.Contains(cell))
            {
                if (isContainsKey(judge.CellRxLevList, cell))
                {
                    judge.IsInList = true;
                }
                else
                {
                    judge.IsInList = false;
                }
                judge.SN = resultList.Count + 1;
                judge.CellName = cell.Name;
                judge.CellID = cell.CI;
                judge.LAC = cell.LAC;
                judge.FallLongitude = cell.Longitude;
                judge.FallLatitude = cell.Latitude;
                judge.MaxFallDistance = Math.Round(MathFuncs.GetDistance(judge.Longitude, judge.Latitude, maxLongitude, maxLatitude), 2);
                judge.MaxFallRxLev = maxRxLev;
                resultList.Add(judge);
            }
        }
    }
}
