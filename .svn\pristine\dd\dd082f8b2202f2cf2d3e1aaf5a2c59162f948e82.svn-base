﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTRoadCompareAna : DIYInjectionGridQuery
    {
        readonly CityRoadCompareSetting comPareCond = new CityRoadCompareSetting();
        RoadCompareDlg mapSettingDlg = null;

        //<时段，同时段的道路信息集合> 渲染不同时段的GIS用
        readonly Dictionary<string, List<StreetInjectInfo>> difTimeRoadInfoDic = new Dictionary<string, List<StreetInjectInfo>>();

        //<道路主键,不同时段的道路信息集合>
        readonly Dictionary<string, RoadInjectCompareItem> roadCompareDic = new Dictionary<string, RoadInjectCompareItem>();
        public ZTRoadCompareAna()
            : base(MainModel.GetInstance())
        {
        }
        public override string Name
        {
            get { return "多时段道路渗透率对比"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20050, this.Name);
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        protected bool getCondition()
        {
            if (mapSettingDlg == null)
            {
                mapSettingDlg = new RoadCompareDlg(comPareCond);
            }
            if (mapSettingDlg.ShowDialog() == DialogResult.OK)
            {
                return true;
            } 
            return false;
        }
        protected void clearDatas()
        {
            difTimeRoadInfoDic.Clear();
            roadCompareDic.Clear();
        }
        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }
            clearDatas();
            bool multiGeometrys = mainModel.MultiGeometrys;
            mainModel.MultiGeometrys = true;

            foreach (TimePeriod timePeriod in comPareCond.PeriodList)
            {
                string period = GetTimePeriodDes(timePeriod);

                List<StreetInjectInfo> streetInjectInfos = queryInjectByPeriod(timePeriod);

                Dictionary<string, StreetInjectInfoTotal> regionInjectDic = new Dictionary<string, StreetInjectInfoTotal>();
                List<StreetInjectInfo> sameTimeStreetList = new List<StreetInjectInfo>();
                difTimeRoadInfoDic.Add(period, sameTimeStreetList);

                foreach (StreetInjectInfo info in streetInjectInfos)
                {
                    info.TimeDes = period;
                    sameTimeStreetList.Add(info);

                    addRoadCompareDic(info);

                    #region 统计网格渗透率
                    if (!regionInjectDic.ContainsKey(info.AreaName))
                    {
                        regionInjectDic[info.AreaName] = new StreetInjectInfoTotal();
                        regionInjectDic[info.AreaName].RegionName = info.AreaName;
                    }
                    regionInjectDic[info.AreaName].Add(info);
                    #endregion

                }

                #region 设置网格渗透率
                StreetInjectInfoTotal regionInfo;
                foreach (StreetInjectInfo info in sameTimeStreetList)
                {
                    if (regionInjectDic.TryGetValue(info.AreaName, out regionInfo))
                    {
                        info.GridInjectPercent = regionInfo.CoverPercent;
                    }
                }
                #endregion
            }

            mainModel.MultiGeometrys = multiGeometrys;
            showResultForm();
        }

        private void addRoadCompareDic(StreetInjectInfo info)
        {
            string token = RoadInjectCompareItem.GetToken(info);
            RoadInjectCompareItem item;
            if (roadCompareDic.TryGetValue(token, out item))
            {
                item.AddData(info);
            }
            else
            {
                item = new RoadInjectCompareItem(info);
                roadCompareDic[token] = item;
                item.SN = roadCompareDic.Count;
            }
        }

        protected List<StreetInjectInfo> queryInjectByPeriod(TimePeriod timePeriod)
        {
            List<StreetInjectInfo> streetInjectInfo;
            ClientProxy clientProxy = null;
            injectGridMatrix = null;
            try
            {
                condition.Periods.Clear();
                condition.Periods.Add(timePeriod);

                if (MainModel.User.DBID == -1)  //如果是省库
                {
                    foreach (int DistrictID in condition.DistrictIDs)
                    {
                        clientProxy = new ClientProxy();
                        query(clientProxy, DistrictID);
                    }
                }
                else
                {
                    clientProxy = new ClientProxy();
                    bool isSuccess = query(clientProxy, MainModel.DistrictID);
                    if (!isSuccess)
                    {
                        return new List<StreetInjectInfo>();
                    }
                }

                MainModel.CurStreetInjectMatrix = injectGridMatrix;

                MapFormStreetInjectAnaLayer layer = MainModel.MainForm.GetMapForm().GetStreetInjectAnaLayer();
                MainModel.MainForm.GetMapForm().MakeSureCustomLayerVisible(layer, true);
                streetInjectInfo = layer.GetInjectResultList(this);
            }
            finally
            {
                injectGridMatrix = null;
                if (clientProxy != null)
                {
                    clientProxy.Close();
                }
            }
            return streetInjectInfo;
        }

        private bool query(ClientProxy clientProxy, int DistrictID)
        {
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return false;
            }
            else
            {
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                MainModel.StreetInjectMultiTables = true;
                MainModel.LastSearchGeometry = Condition.Geometorys.Region;
                MainModel.StreetInjectResvRegions = condition.Geometorys.SelectedResvRegions;
                return true;
            }
        }

        protected void showResultForm()
        {
            RoadCompareInfoForm frm = mainModel.CreateResultForm(typeof(RoadCompareInfoForm)) as RoadCompareInfoForm;
            frm.FillDatas(difTimeRoadInfoDic, new List<RoadInjectCompareItem>(roadCompareDic.Values));
            frm.Visible = true;
            frm.BringToFront();
        }
        public static string GetTimePeriodDes(TimePeriod period)
        {
            DateTime sMonthTime = new DateTime(period.BeginTime.Year, period.BeginTime.Month, 1);
            DateTime eMonthTime = sMonthTime.AddMonths(1).AddMilliseconds(-1);
            if (period.BeginTime == sMonthTime && period.EndTime == eMonthTime)
            {
                return string.Format("{0}年{1}月", period.BeginTime.Year, period.BeginTime.Month);
            }
            return period.ToString();
        }
      
    }
    public class RoadInjectCompareItem
    {
        public RoadInjectCompareItem(StreetInjectInfo info)
        {
            this.GridName = info.AreaName;
            this.RoadLevel = info.StreetTableName;
            this.RoadName = info.StreetName;
            this.RoadIdList = info.StreetIdList;
            this.RoadLength = info.DistTotal;
            this.InjectInfos.Add(info);
        }
        public int SN { get; set; }
        public string GridName { get; set; }
        public string RoadLevel { get; set; }
        public string RoadName { get; set; }
        public string RoadIdDes
        {
            get
            {
                return StreetInjectInfo.GetIntListDes(RoadIdList);
            }
        }
        
        public List<int> RoadIdList { get; set; }

        private double roadLength;
        public double RoadLength
        {
            get
            {
                return Math.Round(roadLength, 2);
            }
            set
            {
                roadLength = value;
            }
        }
        
        public List<StreetInjectInfo> InjectInfos { get; set; } = new List<StreetInjectInfo>();

        /// <summary>
        /// 能确定唯一道路的主键
        /// </summary>
        public string Token
        {
            get
            {
                if (InjectInfos.Count > 0)
                {
                    return GetToken(InjectInfos[0]);
                }
                return string.Empty;
            }
        }
        public static string GetToken(StreetInjectInfo info)
        {
            return string.Format("{0}_{1}_{2}_{3}", info.AreaName, info.StreetTableName, info.StreetName, info.StreetIdDes);
        }

        public void AddData(StreetInjectInfo info)
        {
            this.InjectInfos.Add(info);
            foreach (int streetId in info.StreetIdList)
            {
                if (!this.RoadIdList.Contains(streetId))
                {
                    this.RoadIdList.Add(streetId);
                    this.roadLength += info.DistTotal;
                }
            }
        }
    }
}
