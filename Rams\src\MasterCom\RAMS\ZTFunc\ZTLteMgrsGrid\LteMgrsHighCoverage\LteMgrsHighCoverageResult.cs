﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsHighCoverageResult : LteMgrsResultControlBase
    {
        LteMgrsFuncItem funcItem = null;

        public LteMgrsHighCoverageResult()
        {
            InitializeComponent();
            InitCbxFreqType();

            cbxFreqType.SelectedIndexChanged += CbxFreqType_SelectedChanged;
            miExportSimpleExcel.Click += MiExportSimpleExcel_Click;
            miExportDetailExcel.Click += MiExportDetailExcel_Click;
            gridView1.DoubleClick += GridView_DoubleClick;
            gridView2.DoubleClick += GridView_DoubleClick;
            gridView3.DoubleClick += GridView_DoubleClick;

            miExportAllExcel.Click += base.MiExportExcelAll_Click;
            miExportWord.Click += base.MiExportWord_Click;
            miExportList.Click += MiExportGridList_Click;
        }

        public override string Desc
        {
            get { return "高重叠覆盖区域"; }
        }

        public void FillData(LteMgrsFuncItem funcItem)
        {
            this.funcItem = funcItem;
            LteMgrsHighCoverageCondition cond = funcItem.FuncCondtion as LteMgrsHighCoverageCondition;
            cbxFreqType.SelectedItem = EnumDescriptionAttribute.GetText(cond.FreqType);
            RefreshResult();
        }

        protected override void ExportAllExcel(string savePath)
        {
            for (int i = 0; i < cbxFreqType.Items.Count; ++i)
            {
                cbxFreqType.SelectedIndex = i;
                string sheetName = cbxFreqType.SelectedItem.ToString() + Desc;
                string fileName = System.IO.Path.Combine(savePath, sheetName + ".xlsx");
                ExcelNPOIManager.ExportToExcel(gridView1, fileName, sheetName);
            }
        }

        private void CbxFreqType_SelectedChanged(object sender, EventArgs e)
        {
            RefreshResult();
            DrawOnLayer();
        }

        private void GridView_DoubleClick(object sender, EventArgs e)
        {
            LteMgrsLayer.SelGrid = null;
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object row = gv.GetRow(gv.GetSelectedRows()[0]);
            if (row is LteMgrsCell)
            {
                LteMgrsCell cell = row as LteMgrsCell;
                if (cell.Cell != null)
                {
                    MainModel.SelectedLTECell = cell.Cell as LTECell;
                    LteMgrsLayer.SelGrid = gv.SourceRow as LteMgrsWeakRsrpGrid;
                    DbRect rect = new DbRect(cell.Cell.Longitude,
                        cell.Cell.Latitude
                        , LteMgrsLayer.SelGrid.CentLng
                        , LteMgrsLayer.SelGrid.CentLat);
                    MainModel.MainForm.GetMapForm().GoToView(rect.Expend(1.2));
                }
            }
            else if (row is LteMgrsWeakRsrpGrid)
            {
                LteMgrsWeakRsrpGrid grid = row as LteMgrsWeakRsrpGrid;
                MainModel.MainForm.GetMapForm().GoToView(grid.CentLng, grid.CentLat);
                LteMgrsLayer.SelGrid = grid;
            }
            else if (row is LteMgrsWeakRsrpView)
            {
                LteMgrsWeakRsrpView view = row as LteMgrsWeakRsrpView;
                LteMgrsWeakRsrpGrid grid = view.GridViews[view.GridViews.Count / 2];
                MainModel.MainForm.GetMapForm().GoToView(grid.CentLng, grid.CentLat);
            }
        }

        private void InitCbxFreqType()
        {
            cbxFreqType.Items.Clear();
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.All));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.Top));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD1));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD2));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD_38098));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleF));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleF1));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleF2));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.TopEarfcn));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD_40936));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD_40940));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleE_38950));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleE_39148));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD1_3683));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD1_3692));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD2_1259));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD2_1300));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD2_1309));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD2_1359));
            cbxFreqType.SelectedIndex = 0;
        }

        private void RefreshResult()
        {
            LteMgrsHighCoverageCondition cond = this.funcItem.FuncCondtion as LteMgrsHighCoverageCondition;
            cond.FreqType = (LteMgrsCoverageBandType)EnumDescriptionAttribute.Parse(typeof(LteMgrsCoverageBandType), cbxFreqType.SelectedItem as string);
            LteMgrsHighCoverageStater stater = this.funcItem.Stater as LteMgrsHighCoverageStater;
            List<LteMgrsHighCoverageView> retList = stater.GetViews(funcItem.CurQueryCitys[funcItem.SelectedCityIndex]);
            gridControl1.DataSource = retList;
            gridControl1.RefreshDataSource();

            if (cond.EnableOutputSample)
                OutputMgrsInfo(retList, cond, funcItem.CurQueryCitys[funcItem.SelectedCityIndex]);
        }

        private void OutputMgrsInfo(List<LteMgrsHighCoverageView> retList, LteMgrsHighCoverageCondition cond, LteMgrsCity city)
        {
            List<string> sheetList = new List<string>();
            sheetList.Add(string.Format("{0}_高重叠覆盖区域_{2}_{1:yyyyMMddHHmmss}.csv", city.CityName, DateTime.Now, EnumDescriptionAttribute.GetText(cond.FreqType)));

            List<List<NPOIRow>> datasList = new List<List<NPOIRow>>();
            List<NPOIRow> datas = fillMgrsHighData(retList);
            datasList.Add(datas);
            LteMgrsCoverageRangeStater.OutputCsvFile(datasList, sheetList,cond.strCsvPath);
        }

        private void MiExportGridList_Click(object sender, EventArgs e)
        {
            List<LteMgrsHighCoverageView> viewList = gridControl1.DataSource as List<LteMgrsHighCoverageView>;
            if (viewList == null)
            {
                return;
            }

            List<List<object>> content = new List<List<object>>();
            List<object> title = new List<object>();
            title.Add("栅格序号");
            title.Add("所属网格");
            title.Add("栅格编号");
            title.Add("采样点数");
            title.Add("重叠覆盖度");
            title.Add("中心经度");
            title.Add("中心纬度");
            content.Add(title);

            int idIndex = 0;
            foreach (LteMgrsHighCoverageView view in viewList)
            {
                foreach (LteMgrsWeakRsrpGrid grid in view.GridViews)
                {
                    List<object> row = new List<object>();
                    row.Add(++idIndex);
                    row.Add(view.RegionName);
                    row.Add(grid.MgrsString);
                    row.Add(grid.SampleCount);
                    row.Add((int)grid.TopRsrp);
                    row.Add(grid.CentLng);
                    row.Add(grid.CentLat);
                    content.Add(row);
                }
            }
            ExcelNPOIManager.ExportToExcel(content);
        }

        private void MiExportSimpleExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }

        private void MiExportDetailExcel_Click(object sender, EventArgs e)
        {
            List<LteMgrsHighCoverageView> viewList = gridControl1.DataSource as List<LteMgrsHighCoverageView>;
            if (viewList == null)
            {
                return;
            }

            List<NPOIRow> content = fillMgrsHighData(viewList);
            ExcelNPOIManager.ExportToExcel(content);
        }

        /// <summary>
        /// 高重叠覆盖区域详细列名
        /// </summary>
        private List<NPOIRow> fillMgrsHighData(List<LteMgrsHighCoverageView> retList)
        {
            List<NPOIRow> content = new List<NPOIRow>();

            NPOIRow nr = new NPOIRow();
            List<object> title = new List<object>();
            title.Add("区域序号");
            title.Add("网格");
            title.Add("连续栅格数");
            title.Add("连续栅格集");
            title.Add("最大重叠覆盖度");
            title.Add("最小重叠覆盖度");
            title.Add("平均重叠覆盖度");
            title.Add("道路名称");
            title.Add("栅格编号");
            title.Add("采样点数");
            title.Add("平均场强");
            title.Add("重叠覆盖度");
            title.Add("中心经度");
            title.Add("中心纬度");
            title.Add("小区名");
            title.Add("TAC");
            title.Add("ECI");
            title.Add("EARFCN");
            title.Add("PCI");
            title.Add("小区采样点数");
            title.Add("小区平均场强");
            title.Add("经度");
            title.Add("纬度");
            nr.cellValues = title;
            content.Add(nr);

            foreach (LteMgrsHighCoverageView view in retList)
            {
                List<object> levelOne = new List<object>();
                levelOne.Add(view.SN);
                levelOne.Add(view.RegionName);
                levelOne.Add(view.GridCount);
                levelOne.Add(view.GridSetDesc);
                levelOne.Add(view.MaxRsrp);
                levelOne.Add(view.MinRsrp);
                levelOne.Add(view.AvgRsrp);
                levelOne.Add(view.RoadName);
                foreach (LteMgrsWeakRsrpGrid grid in view.GridViews)
                {
                    List<object> levelTwo = new List<object>(levelOne);
                    levelTwo.Add(grid.MgrsString);
                    levelTwo.Add(grid.SampleCount);
                    levelTwo.Add(grid.AvgRsrp);
                    levelTwo.Add(grid.TopRsrp);
                    levelTwo.Add(grid.CentLng);
                    levelTwo.Add(grid.CentLat);
                    foreach (LteMgrsCell cell in grid.Cells)
                    {
                        NPOIRow nrValue = new NPOIRow();
                        List<object> levelThree = new List<object>(levelTwo);
                        levelThree.Add(cell.CellName);
                        levelThree.Add(cell.Tac);
                        levelThree.Add(cell.Eci);
                        levelThree.Add(cell.Earfcn);
                        levelThree.Add(cell.Pci);
                        levelThree.Add(cell.SampleCount);
                        levelThree.Add(cell.AvgRsrp);
                        levelThree.Add(cell.Longitude);
                        levelThree.Add(cell.Latitude);
                        nrValue.cellValues = levelThree;
                        content.Add(nrValue);
                    }
                }
            }

            return content;
        }

        public override void DrawOnLayer()
        {
            List<LteMgrsHighCoverageView> viewList = gridControl1.DataSource as List<LteMgrsHighCoverageView>;
            if (viewList == null || viewList.Count == 0)
            {
                return;
            }

            LteMgrsHighCoverageStater stater = funcItem.Stater as LteMgrsHighCoverageStater;
            List<LteMgrsDrawItem> drawList = stater.GetDrawList(viewList);
            LteMgrsLayer.DrawList = drawList;
            DbRect rect = LteMgrsLayer.GetDrawBound(drawList);
            if (rect != null)
            {
                MainModel.MainForm.GetMapForm().GoToView(rect);
                SetNormalMapScale();
            }
            LteMgrsLayer.LegendGroup = stater.GetLegend();
            MainModel.RefreshLegend();
        }

        protected override bool ExportWord(MasterCom.RAMS.Model.WordControl word, string title)
        {
            if (title != "不分段高重叠覆盖度区域" && title != "最强归属段高重叠覆盖度区域"
                && title != "D频段高重叠覆盖度区域" && title != "F频段高重叠覆盖度区域")
            {
                return false;
            }

            WaitTextBox.Text = "正在导出 " + title + "...";

            if (title == "不分段高重叠覆盖度区域") cbxFreqType.SelectedIndex = 0;
            else if (title == "最强归属段高重叠覆盖度区域") cbxFreqType.SelectedIndex = 1;
            else if (title == "D频段高重叠覆盖度区域") cbxFreqType.SelectedIndex = 2;
            else if (title == "F频段高重叠覆盖度区域") cbxFreqType.SelectedIndex = 3;
            else if (title == "最强信号频点高重叠覆盖度区域") cbxFreqType.SelectedIndex = 4;

            if (gridView1.RowCount == 0)
            {
                word.InsertText("未发现高重叠覆盖路段。", "正文");
                word.NewLine();
                return true;
            }

            this.DrawOnLayer();
            string text = cbxFreqType.SelectedItem.ToString() + "连续高重叠覆盖度";
            word.InsertText("整体渲染效果如下：", "正文");
            word.NewLine();
            word.InsertPicture(LteMgrsScreenShoter.GisScreenshot("LTE_" + text));
            word.NewLine();
            return true;
        }
    }
}
