﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class HighRSRPLowSINR_NR : HighRSRPLowSINR
    {
        public DateTime StartTime { get; set; }
        public string CellListDesc { get; set; }

        protected int lteRsrpCount = 0;
        protected float lteRsrpSum = 0;
        public float? LteRsrpAvg
        {
            get
            {
                return lteRsrpCount > 0 ? (float?)(Math.Round(lteRsrpSum / lteRsrpCount, 2)) : null;
            }
        }

        protected int lteSinrCount = 0;
        protected float lteSinrSum = 0;
        public float? LteSinrAvg
        {
            get
            {
                return lteSinrCount > 0 ? (float?)(Math.Round(lteSinrSum / lteSinrCount, 2)) : null;
            }
        }

        public void AddOtherTPInfo(TestPoint tp)
        {
            bool isValid;
            float? lteRSRP = NRTpHelper.NrLteTpManager.GetSCellRsrp(tp);
            isValid = isValidData(lteRSRP, -200, 100);
            if (isValid)
            {
                lteRsrpCount++;
                lteRsrpSum += (float)lteRSRP;
            }

            float? lteSINR = NRTpHelper.NrLteTpManager.GetSCellSinr(tp);
            isValid = isValidData(lteSINR, -50, 50);
            if (isValid)
            {
                lteSinrCount++;
                lteSinrSum += (float)lteSINR;
            }
        }

        private bool isValidData(float? data, float min, float max)
        {
            if (data != null && data >= min && data <= max)
            {
                return true;
            }
            return false;
        }
    }
}
