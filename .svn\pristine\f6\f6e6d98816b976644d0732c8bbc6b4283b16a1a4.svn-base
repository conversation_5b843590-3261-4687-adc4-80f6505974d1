﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public static class NRStationAcceptAlarmHelper
    {
        public static bool DealAlarmInfo()
        {
            //加载单验配置
            DiyQueryFddDBSetting.GetInstance().Query();

            try
            {
                //加载告警Excel
                //注: 现场提出需求,地市人员不能操作告警列表入库,故改为写死只加载固定路径的告警
                string startPath = System.Windows.Forms.Application.StartupPath;
                string fullPath = startPath + @"\userData\NRStationAcceptance\告警.xlsx";
                string bakPath = startPath + @"\userData\NRStationAcceptance\bak\告警.xlsx";
                if (File.Exists(fullPath))
                {
                    List<NRAlarmInfo> list = ExcelHelper.ReadExcel<List<NRAlarmInfo>>(fullPath, dealData);

                    //插入到数据库
                    DIYInsertNRAlarm query = new DIYInsertNRAlarm(list);
                    query.Query();

                    moveFile(fullPath, bakPath);
                }
                return true;
            }
            catch(Exception e)
            {
                System.Windows.Forms.MessageBox.Show("加载告警配置失败" + e.Message);
                return false;
            }
        }

        private static void dealData(List<NRAlarmInfo> list, DataRow dr)
        {
            NRAlarmInfo data = new NRAlarmInfo();
            data.FillData(dr);
            list.Add(data);
        }

        private static void moveFile(string sourceFileFullPath, string destFilePath)
        {
            if (File.Exists(destFilePath))
            {
                File.Delete(destFilePath);
            }
            File.Move(sourceFileFullPath, destFilePath);
        }
    }
}
