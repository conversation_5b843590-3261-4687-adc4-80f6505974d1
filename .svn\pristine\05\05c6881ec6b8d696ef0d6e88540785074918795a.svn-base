using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTScanInterfereModelSelDlg : Form
    {
        public ZTScanInterfereModelSelDlg()
        {
            InitializeComponent();
            setEnable();
            cbxInterferenceType.SelectedIndex = 1;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        public int Model
        {
            get { return (rbCo.Checked ? 0 : 1); }
        }

        public int BCCHDValue
        {
            get { return (int)numBCCHDValue.Value; }
        }

        public int InterferenceType
        {
            get { return cbxInterferenceType.SelectedIndex; }
        }

        private void setEnable()
        {
            numBCCHDValue.Enabled = rbAdj.Checked;
        }

        private void rb_CheckedChanged(object sender, EventArgs e)
        {
            setEnable();
        }
    }
}