﻿namespace MasterCom.RAMS.KPI_Statistics
{
    partial class KpiPkResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.treeList = new DevExpress.XtraTreeList.TreeList();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportEveryGridXls = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportSerialGridXls = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportXls = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportShp = new System.Windows.Forms.ToolStripMenuItem();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.spEditSerialNum = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.lbxLegend = new System.Windows.Forms.ListBox();
            ((System.ComponentModel.ISupportInitialize)(this.treeList)).BeginInit();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spEditSerialNum.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeList
            // 
            this.treeList.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.treeList.ContextMenuStrip = this.ctxMenu;
            this.treeList.Location = new System.Drawing.Point(0, 41);
            this.treeList.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.treeList.Name = "treeList";
            this.treeList.Size = new System.Drawing.Size(704, 425);
            this.treeList.TabIndex = 1;
            this.treeList.DoubleClick += new System.EventHandler(this.treeList_DoubleClick);
            // 
            // ctxMenu
            // 
            this.ctxMenu.ImageScalingSize = new System.Drawing.Size(24, 24);
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportEveryGridXls,
            this.miExportSerialGridXls,
            this.miExportXls,
            this.miExportShp});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(298, 124);
            // 
            // miExportEveryGridXls
            // 
            this.miExportEveryGridXls.Name = "miExportEveryGridXls";
            this.miExportEveryGridXls.Size = new System.Drawing.Size(297, 30);
            this.miExportEveryGridXls.Text = "导出各栅信息到Excel...";
            this.miExportEveryGridXls.Click += new System.EventHandler(this.miExportEveryGridXls_Click);
            // 
            // miExportSerialGridXls
            // 
            this.miExportSerialGridXls.Name = "miExportSerialGridXls";
            this.miExportSerialGridXls.Size = new System.Drawing.Size(297, 30);
            this.miExportSerialGridXls.Text = "导出栅格汇聚信息到Excel...";
            this.miExportSerialGridXls.Click += new System.EventHandler(this.miExportSerialGridXls_Click);
            // 
            // miExportXls
            // 
            this.miExportXls.Name = "miExportXls";
            this.miExportXls.Size = new System.Drawing.Size(297, 30);
            this.miExportXls.Text = "导出全部结果到Excel...";
            this.miExportXls.Click += new System.EventHandler(this.miExportXls_Click);
            // 
            // miExportShp
            // 
            this.miExportShp.Name = "miExportShp";
            this.miExportShp.Size = new System.Drawing.Size(297, 30);
            this.miExportShp.Text = "导出Shp图层...";
            this.miExportShp.Click += new System.EventHandler(this.miExportShp_Click);
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.panelControl1);
            this.splitContainerControl1.Panel1.Controls.Add(this.treeList);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl1);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1364, 682);
            this.splitContainerControl1.SplitterPosition = 704;
            this.splitContainerControl1.TabIndex = 2;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.spEditSerialNum);
            this.panelControl1.Controls.Add(this.labelControl1);
            this.panelControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControl1.Location = new System.Drawing.Point(0, 0);
            this.panelControl1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(1006, 44);
            this.panelControl1.TabIndex = 2;
            // 
            // spEditSerialNum
            // 
            this.spEditSerialNum.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.spEditSerialNum.Location = new System.Drawing.Point(223, 5);
            this.spEditSerialNum.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.spEditSerialNum.Name = "spEditSerialNum";
            this.spEditSerialNum.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spEditSerialNum.Properties.IsFloatValue = false;
            this.spEditSerialNum.Properties.Mask.EditMask = "N00";
            this.spEditSerialNum.Properties.MaxValue = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.spEditSerialNum.Properties.MinValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.spEditSerialNum.Size = new System.Drawing.Size(96, 28);
            this.spEditSerialNum.TabIndex = 1;
            this.spEditSerialNum.EditValueChanged += new System.EventHandler(this.spEditSerialNum_EditValueChanged);
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(59, 9);
            this.labelControl1.Margin = new System.Windows.Forms.Padding(6, 8, 6, 8);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(163, 22);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "连续汇聚栅格个数 ≥";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.lbxLegend);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(934, 1072);
            this.groupControl1.TabIndex = 67;
            this.groupControl1.Text = "图例";
            // 
            // lbxLegend
            // 
            this.lbxLegend.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lbxLegend.DrawMode = System.Windows.Forms.DrawMode.OwnerDrawFixed;
            this.lbxLegend.FormattingEnabled = true;
            this.lbxLegend.IntegralHeight = false;
            this.lbxLegend.ItemHeight = 18;
            this.lbxLegend.Location = new System.Drawing.Point(7, 41);
            this.lbxLegend.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.lbxLegend.Name = "lbxLegend";
            this.lbxLegend.SelectionMode = System.Windows.Forms.SelectionMode.None;
            this.lbxLegend.Size = new System.Drawing.Size(1026, 1269);
            this.lbxLegend.TabIndex = 66;
            this.lbxLegend.MouseClick += new System.Windows.Forms.MouseEventHandler(this.lbxLegend_MouseClick);
            this.lbxLegend.DrawItem += new System.Windows.Forms.DrawItemEventHandler(this.lbxLegend_DrawItem);
            // 
            // KpiPkResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(10F, 22F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1364, 682);
            this.Controls.Add(this.splitContainerControl1);
            this.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.Name = "KpiPkResultForm";
            this.Text = "竞比结果";
            ((System.ComponentModel.ISupportInitialize)(this.treeList)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spEditSerialNum.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTreeList.TreeList treeList;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportXls;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.ListBox lbxLegend;
        private System.Windows.Forms.ToolStripMenuItem miExportShp;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.SpinEdit spEditSerialNum;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.ToolStripMenuItem miExportEveryGridXls;
        private System.Windows.Forms.ToolStripMenuItem miExportSerialGridXls;
    }
}