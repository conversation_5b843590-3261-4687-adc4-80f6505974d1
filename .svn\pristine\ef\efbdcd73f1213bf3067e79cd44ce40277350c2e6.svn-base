﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.Func
{
    public partial class CellGridBlockResultForm : MinCloseForm
    {
        public CellGridBlockResultForm()
            : base()
        {
            InitializeComponent();
            mapForm = MainModel.MainForm.GetMapForm();
            init();
            DisposeWhenClose = true;
        }

        private MapForm mapForm = null;
        private List<CellGridBlockInfo> resultList = null;
        private CellGridCondition condition = null;
        private void init()
        {
            dealCellGridBlockInfo();
            dealCellGridAllInfo();
        }

        private void dealCellGridAllInfo()
        {
            this.olvColumnCgID.AspectGetter = delegate (object row)
            {
                if (row is CellGridAllInfo)
                {
                    CellGridAllInfo cg = row as CellGridAllInfo;
                    return cg.ID;
                }
                return "";
            };
            this.olvColumnTlLng.AspectGetter = delegate (object row)
            {
                if (row is CellGridAllInfo)
                {
                    CellGridAllInfo cg = row as CellGridAllInfo;
                    return (double)cg.TlLongitude / 10000000;
                }
                return "";
            };
            this.olvColumnTlLat.AspectGetter = delegate (object row)
            {
                if (row is CellGridAllInfo)
                {
                    CellGridAllInfo cg = row as CellGridAllInfo;
                    return (double)cg.TlLatitude / 10000000;
                }
                return "";
            };
            this.olvColumnBrLng.AspectGetter = delegate (object row)
            {
                if (row is CellGridAllInfo)
                {
                    CellGridAllInfo cg = row as CellGridAllInfo;
                    return (double)cg.BrLongitude / 10000000;
                }
                return "";
            };
            this.olvColumnBrLat.AspectGetter = delegate (object row)
            {
                if (row is CellGridAllInfo)
                {
                    CellGridAllInfo cg = row as CellGridAllInfo;
                    return (double)cg.BrLatitude / 10000000;
                }
                return "";
            };
            this.olvColumnIsProb.AspectGetter = delegate (object row)
            {
                if (row is CellGridAllInfo)
                {
                    CellGridAllInfo cg = row as CellGridAllInfo;
                    return getDesc(cg.IsProb);
                }
                return "";
            };
            this.olvColumnInBlock.AspectGetter = delegate (object row)
            {
                if (row is CellGridAllInfo)
                {
                    CellGridAllInfo cg = row as CellGridAllInfo;
                    return getDesc(cg.InBlock);
                }
                return "";
            };
            this.olvColumnAvgRSRP.AspectGetter = delegate (object row)
            {
                if (row is CellGridAllInfo)
                {
                    CellGridAllInfo cg = row as CellGridAllInfo;
                    return Math.Round(cg.AvgRSRP, 2);
                }
                return "";
            };
            this.olvColumnDirection.AspectGetter = delegate (object row)
            {
                if (row is CellGridAllInfo)
                {
                    CellGridAllInfo cg = row as CellGridAllInfo;
                    if (cg.LteCell != null)
                    {
                        return cg.Angle;
                    }
                }
                return "";
            };
            this.olvColumnDistance.AspectGetter = delegate (object row)
            {
                if (row is CellGridAllInfo)
                {
                    CellGridAllInfo cg = row as CellGridAllInfo;
                    if (cg.LteCell != null)
                    {
                        return Math.Round(MathFuncs.GetDistance(cg.LteCell.Longitude, cg.LteCell.Latitude, cg.MidLongitude / 10000000, cg.MidLatitude / 10000000), 2);
                    }
                }
                return "";
            };
        }

        private string getDesc(int flag)
        {
            return flag == 1 ? "是" : "否";
        }

        private void dealCellGridBlockInfo()
        {
            LTECell cell = null;
            this.ListViewBlocks.CanExpandGetter = delegate (object x)
            {
                return x is CellGridBlockInfo;
            };
            this.ListViewBlocks.ChildrenGetter = delegate (object x)
            {
                if (x is CellGridBlockInfo)
                {
                    CellGridBlockInfo block = x as CellGridBlockInfo;
                    return block.CellGridAll;
                }
                return "";
            };
            this.olvColumnSN.AspectGetter = delegate (object row)
            {
                if (row is CellGridBlockInfo)
                {
                    CellGridBlockInfo block = row as CellGridBlockInfo;
                    return block.SN;
                }
                return "";
            };
            this.colSuggest.AspectGetter = delegate (object row)
            {
                if (row is CellGridBlockInfo)
                {
                    CellGridBlockInfo block = row as CellGridBlockInfo;
                    return block.Suggest;
                }
                return "";
            };
            this.colPrimaryType.AspectGetter = delegate (object row)
            {
                if (row is CellGridBlockInfo)
                {
                    CellGridBlockInfo block = row as CellGridBlockInfo;
                    return block.PrimaryType;
                }
                return "";
            };
            this.colSpecificType.AspectGetter = delegate (object row)
            {
                if (row is CellGridBlockInfo)
                {
                    CellGridBlockInfo block = row as CellGridBlockInfo;
                    return block.SpecificType;
                }
                return "";
            };
            this.colDetail.AspectGetter = delegate (object row)
            {
                if (row is CellGridBlockInfo)
                {
                    CellGridBlockInfo block = row as CellGridBlockInfo;
                    return block.Detail;
                }
                return "";
            };
            this.olvColumnType.AspectGetter = delegate (object row)
            {
                if (row is CellGridBlockInfo)
                {
                    CellGridBlockInfo block = row as CellGridBlockInfo;
                    return block.Type;
                }
                return "";
            };
            this.olvColumnBlockID.AspectGetter = delegate (object row)
            {
                if (row is CellGridBlockInfo)
                {
                    CellGridBlockInfo block = row as CellGridBlockInfo;
                    return block.BlockID;
                }
                return "";
            };
            this.olvColumnLAC.AspectGetter = delegate (object row)
            {
                if (row is CellGridBlockInfo)
                {
                    CellGridBlockInfo block = row as CellGridBlockInfo;
                    return block.LAC;
                }
                return "";
            };
            this.olvColumnCI.AspectGetter = delegate (object row)
            {
                if (row is CellGridBlockInfo)
                {
                    CellGridBlockInfo block = row as CellGridBlockInfo;
                    return block.CI;
                }
                return "";
            };
            this.olvColumnServCell.AspectGetter = delegate (object row)
            {
                if (row is CellGridBlockInfo)
                {
                    CellGridBlockInfo block = row as CellGridBlockInfo;
                    cell = CellManager.GetInstance().GetLTECell(block.Time, block.LAC, block.CI);
                    if (cell != null)
                    {
                        return cell.Name;
                    }
                }
                return "";
            };
            this.olvColumnStatus.AspectGetter = delegate (object row)
            {
                if (row is CellGridBlockInfo)
                {
                    CellGridBlockInfo block = row as CellGridBlockInfo;
                    return block.EsStatus;
                }
                return "";
            };
        }

        public void FillData(List<CellGridBlockInfo> ResultList, CellGridCondition condition)
        {
            this.resultList = new List<CellGridBlockInfo>();
            this.resultList = ResultList;
            this.condition = condition;

            this.ListViewBlocks.RebuildColumns();
            this.ListViewBlocks.ClearObjects();
            this.ListViewBlocks.SetObjects(resultList);

            //MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        private void expandAll_Click(object sender, EventArgs e)
        {
            this.ListViewBlocks.ExpandAll();
        }

        private void collapsAll_Click(object sender, EventArgs e)
        {
            this.ListViewBlocks.CollapseAll();
        }

        CellGridBlockLayer layer = null;
        private void ListViewBlocks_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            double minLng = double.MaxValue;
            double maxLng = double.MinValue;
            double minLat = double.MaxValue;
            double maxLat = double.MinValue;
            makeSureLayerVisible();
            if (ListViewBlocks.SelectedObject is CellGridBlockInfo)
            {
                CellGridBlockInfo block = ListViewBlocks.SelectedObject as CellGridBlockInfo;
                
                foreach (CellGridInfo cg in block.CellGrids)
                {
                    minLng = Math.Min(minLng, cg.MidLongitude);
                    maxLng = Math.Max(maxLng, cg.MidLongitude);
                    minLat = Math.Min(minLat, cg.MidLatitude);
                    maxLat = Math.Max(maxLat, cg.MidLatitude);
                }
                foreach (CellGridAllInfo all in block.CellGridAll)
                {
                    minLng = Math.Min(minLng, all.MidLongitude);
                    maxLng = Math.Max(maxLng, all.MidLongitude);
                    minLat = Math.Min(minLat, all.MidLatitude);
                    maxLat = Math.Max(maxLat, all.MidLatitude);
                }
                layer.Blocks = new List<CellGridBlockInfo>();
                layer.conn = this.condition;
                layer.Blocks.Add(block);
                MasterCom.MTGis.DbRect rect = new MTGis.DbRect(minLng / 10000000, minLat / 10000000, maxLng / 10000000, maxLat / 10000000);
                mapForm.GoToView(rect);
                LTECell cell = CellManager.GetInstance().GetLTECell(block.Time, block.LAC, block.CI);
                if (cell != null)
                {
                    MainModel.SelectedLTECell = cell;
                    mapForm.goToSelectedCellView(cell.Longitude, cell.Latitude);
                    mapForm.updateLTECellPlanning();
                    mapForm.searchGeometrysChanged();
                }
            }
            else if (ListViewBlocks.SelectedObject is CellGridAllInfo)
            {
                CellGridAllInfo all = ListViewBlocks.SelectedObject as CellGridAllInfo;
                foreach (CellGridBlockInfo block in this.resultList)
                {
                    if (all.LAC == block.LAC && all.CI == block.CI && block.CellGridAll.Contains(all))
                    {
                        layer.Blocks = new List<CellGridBlockInfo>();
                        layer.conn = this.condition;
                        layer.Blocks.Add(block);
                    }
                }
                minLng = Math.Min(all.TlLongitude, all.BrLongitude);
                maxLng = Math.Max(all.TlLongitude, all.BrLongitude);
                minLat = Math.Min(all.TlLatitude, all.BrLatitude);
                maxLat = Math.Max(all.TlLatitude, all.BrLatitude);
                MasterCom.MTGis.DbRect rect = new MTGis.DbRect(minLng / 10000000, minLat / 10000000, maxLng / 10000000, maxLat / 10000000);
                mapForm.GoToView(rect);
            }
        }
        private void makeSureLayerVisible()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            layer = mf.GetLayerBase(typeof(CellGridBlockLayer)) as CellGridBlockLayer;
        }

        private void exportExcel_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(ListViewBlocks);
        }

        private void ListViewBlocks_ItemChecked(object sender, ItemCheckedEventArgs e)
        {
            //
        }
    }
}
