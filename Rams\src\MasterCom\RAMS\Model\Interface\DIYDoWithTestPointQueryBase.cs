﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.Func
{
    public class DIYDoWithTestPointByFile : DIYReplayFileQuery
    {
        public DIYDoWithTestPointByFile(DIYDoWithTestPointQueryBase queryer) 
            : this(MainModel.GetInstance(), queryer)
        {
        }

        public DIYDoWithTestPointByFile(MainModel mainModel, DIYDoWithTestPointQueryBase queryer)
            : base(mainModel)
        {
            IsAddSampleToDTDataManager = false;
            IsAddMessageToDTDataManager = false;
            isAutoLoadCQTPicture = false;
            this.queryer = queryer;
        }

        public DIYDoWithTestPointQueryBase Queryer
        {
            get { return this.queryer; }
        }

        public override string Name
        {
            get { return queryer.Name; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return queryer.getRecLogItem();
        }

        protected override bool isValidCondition()
        {
            return queryer.isValidCondition();
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.DefaultSerialThemeName = null;

            List<ColumnDefItem> items = null;
            foreach (string col in queryer.QueryColumns)
            {
                items = InterfaceManager.GetInstance().GetColumnDefByShowName(col);
                option.SampleColumns.AddRange(items);
            }

            return option;
        }

        protected override void doPostReplayAction()
        {
            queryer.GetResultAfterQuery();
        }

        protected override void fireShowResult()
        {
            queryer.FireShowResult();
            queryer.Clear();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            queryer.DoWithTestPoint(tp);
        }

        private readonly DIYDoWithTestPointQueryBase queryer;
    }

    public class DIYDoWithTestPointByRegion : DIYAnalyseFilesOneByOneByRegion
    {
        public DIYDoWithTestPointByRegion(DIYDoWithTestPointQueryBase queryer) : this(MainModel.GetInstance(), queryer)
        {
        }

        public DIYDoWithTestPointByRegion(MainModel mm, DIYDoWithTestPointQueryBase queryer)
            : base(mm)
        {
            IncludeEvent = false;
            IncludeMessage = false;
            FilterEventByRegion = false;
            FilterSampleByRegion = true;
            this.queryer = queryer;
            Columns = new List<string>(queryer.QueryColumns);
        }

        public DIYDoWithTestPointQueryBase Queryer
        {
            get { return this.queryer; }
        }

        public override string Name
        {
            get { return queryer.Name; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return queryer.getRecLogItem();
        }

        protected override void fireShowForm()
        {
            queryer.FireShowResult();
            queryer.Clear();
        }

        protected override void getResultsAfterQuery()
        {
            queryer.GetResultAfterQuery();
        }

        protected override bool isValidCondition()
        {
            return queryer.isValidCondition();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                List<TestPoint> testPointList = fileDataManager.TestPoints;
                foreach (TestPoint tp in testPointList)
                {
                    queryer.DoWithTestPoint(tp);
                }
            }
        }

        protected DIYDoWithTestPointQueryBase queryer;
    }

    public abstract class DIYDoWithTestPointQueryBase
    {
        public abstract string Name
        {
            get;
        }

        public abstract List<string> QueryColumns
        {
            get;
        }

        public MainModel MainModel
        {
            get;
            private set;
        }

        public abstract void DoWithTestPoint(TestPoint tp);

        public virtual MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return null;
        }

        public virtual bool isValidCondition()
        {
            return true;
        }

        public virtual void GetResultAfterQuery()
        {
        }

        public virtual void FireShowResult()
        {
        }

        public virtual void Clear()
        {
        }

        protected DIYDoWithTestPointQueryBase()
        {
            MainModel = MainModel.GetInstance();
        }

        protected DIYDoWithTestPointQueryBase(MainModel mainModel)
        {
            this.MainModel = mainModel;
        }
    }
}
