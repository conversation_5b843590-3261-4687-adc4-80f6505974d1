﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LTEDownloadPerformanceForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridData = new DevExpress.XtraGrid.GridControl();
            this.conOutPutExcel = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.outPutExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn66 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn67 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn68 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn69 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn72 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn70 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn73 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn71 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn64 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn65 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn51 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn52 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn53 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn54 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn55 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn56 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn57 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn58 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn59 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn60 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn61 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn62 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn63 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn74 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridData)).BeginInit();
            this.conOutPutExcel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // gridData
            // 
            this.gridData.ContextMenuStrip = this.conOutPutExcel;
            this.gridData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridData.Location = new System.Drawing.Point(0, 0);
            this.gridData.MainView = this.gridView1;
            this.gridData.Name = "gridData";
            this.gridData.Size = new System.Drawing.Size(1012, 444);
            this.gridData.TabIndex = 0;
            this.gridData.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // conOutPutExcel
            // 
            this.conOutPutExcel.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.outPutExcel});
            this.conOutPutExcel.Name = "conOutPutExcel";
            this.conOutPutExcel.Size = new System.Drawing.Size(125, 26);
            // 
            // outPutExcel
            // 
            this.outPutExcel.Name = "outPutExcel";
            this.outPutExcel.Size = new System.Drawing.Size(124, 22);
            this.outPutExcel.Text = "导出Excel";
            this.outPutExcel.Click += new System.EventHandler(this.outPutExcel_Click);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn66,
            this.gridColumn2,
            this.gridColumn8,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn67,
            this.gridColumn68,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn69,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn72,
            this.gridColumn70,
            this.gridColumn73,
            this.gridColumn71,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn74,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn38,
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn64,
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn44,
            this.gridColumn45,
            this.gridColumn46,
            this.gridColumn47,
            this.gridColumn48,
            this.gridColumn49,
            this.gridColumn65,
            this.gridColumn50,
            this.gridColumn51,
            this.gridColumn52,
            this.gridColumn53,
            this.gridColumn54,
            this.gridColumn55,
            this.gridColumn56,
            this.gridColumn57,
            this.gridColumn58,
            this.gridColumn59,
            this.gridColumn60,
            this.gridColumn61,
            this.gridColumn62,
            this.gridColumn63});
            this.gridView1.GridControl = this.gridData;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "序号";
            this.gridColumn1.FieldName = "I序号";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn66
            // 
            this.gridColumn66.Caption = "地市名称";
            this.gridColumn66.FieldName = "Str地市";
            this.gridColumn66.Name = "gridColumn66";
            this.gridColumn66.Visible = true;
            this.gridColumn66.VisibleIndex = 1;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "小区名称";
            this.gridColumn2.FieldName = "Str小区名称";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 2;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "网格号";
            this.gridColumn8.FieldName = "Str覆盖网格";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 3;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "EnodBID";
            this.gridColumn3.FieldName = "IEnodBID";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 4;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "SectorID";
            this.gridColumn4.FieldName = "ISectorID";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 5;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "ECI";
            this.gridColumn5.FieldName = "IECI";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 6;
            // 
            // gridColumn67
            // 
            this.gridColumn67.Caption = "问题归类";
            this.gridColumn67.FieldName = "Str问题归类";
            this.gridColumn67.Name = "gridColumn67";
            this.gridColumn67.Visible = true;
            this.gridColumn67.VisibleIndex = 7;
            // 
            // gridColumn68
            // 
            this.gridColumn68.Caption = "问题影响";
            this.gridColumn68.FieldName = "Str问题影响";
            this.gridColumn68.Name = "gridColumn68";
            this.gridColumn68.Visible = true;
            this.gridColumn68.VisibleIndex = 8;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "测试占用时长";
            this.gridColumn6.FieldName = "F测试占用时长";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 9;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "测试采样点数";
            this.gridColumn7.FieldName = "F测试采样点数";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 10;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "PDCP_DL平均速率(Mbit/s)";
            this.gridColumn9.FieldName = "FPDCP_DL平均速率";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 11;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "MAC_DL平均速率(Mbit/s)";
            this.gridColumn10.FieldName = "FMAC_DL平均速率";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 12;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "PHY_DL平均速率(Mbit/s)";
            this.gridColumn11.FieldName = "FPHY_DL平均速率";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 13;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "覆盖率（RSRP > -100dBm且SINR ≥ -3db）";
            this.gridColumn12.FieldName = "F覆盖率100_3";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 14;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "覆盖率（RSRP > -110dBm且SINR ≥ -3db）";
            this.gridColumn13.FieldName = "F覆盖率110_3";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 15;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "平均RSRP（dBm）";
            this.gridColumn14.FieldName = "F平均RSRP";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 16;
            // 
            // gridColumn69
            // 
            this.gridColumn69.Caption = "RSRP>-80dBm采样点数";
            this.gridColumn69.FieldName = "FRSRP_80个数";
            this.gridColumn69.Name = "gridColumn69";
            this.gridColumn69.Visible = true;
            this.gridColumn69.VisibleIndex = 17;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "平均SINR（db）";
            this.gridColumn15.FieldName = "F平均SINR";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 18;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "SINR>-3占比";
            this.gridColumn16.FieldName = "FSINR_3_R";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 19;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "SINR>10占比";
            this.gridColumn17.FieldName = "FSINR10_R";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 20;
            // 
            // gridColumn72
            // 
            this.gridColumn72.Caption = "RSRQ均值";
            this.gridColumn72.FieldName = "F平均RSRQ";
            this.gridColumn72.Name = "gridColumn72";
            this.gridColumn72.Visible = true;
            this.gridColumn72.VisibleIndex = 21;
            // 
            // gridColumn70
            // 
            this.gridColumn70.Caption = "RSRQ<-11.5dB占比";
            this.gridColumn70.FieldName = "FPRSQ_115_R";
            this.gridColumn70.Name = "gridColumn70";
            this.gridColumn70.Visible = true;
            this.gridColumn70.VisibleIndex = 22;
            // 
            // gridColumn73
            // 
            this.gridColumn73.Caption = "RSRP>-80dBm且RSRQ<-11.5dB采样点数";
            this.gridColumn73.FieldName = "FRSRP_80_RSRQ个数";
            this.gridColumn73.Name = "gridColumn73";
            this.gridColumn73.Visible = true;
            this.gridColumn73.VisibleIndex = 23;
            // 
            // gridColumn71
            // 
            this.gridColumn71.Caption = "RSRP>-80dBm且RSRQ<-11.5dB占比";
            this.gridColumn71.FieldName = "FRSRP_80_RSRQ_R";
            this.gridColumn71.Name = "gridColumn71";
            this.gridColumn71.Visible = true;
            this.gridColumn71.VisibleIndex = 24;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "平均RI";
            this.gridColumn18.FieldName = "F平均RI";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 25;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "RI>1占比";
            this.gridColumn19.FieldName = "FRI1_R";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 26;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "平均CQI";
            this.gridColumn20.FieldName = "F平均CQI";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 27;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "CQI>10占比";
            this.gridColumn21.FieldName = "FCQI10_R";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 28;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "平均下行PDSCH_BLER";
            this.gridColumn22.FieldName = "F平均下行PDSCH_BLER";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 29;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "下行PDSCH_BLER>10%占比";
            this.gridColumn23.FieldName = "F下行PDSCH_BLER10_R";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 30;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "平均下行PDCCH_BLER";
            this.gridColumn24.FieldName = "F平均下行PDCCH_BLER";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 31;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "QPSK调整占比";
            this.gridColumn25.FieldName = "FQPSK调整_R";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 32;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "16QAM调制占比";
            this.gridColumn26.FieldName = "F16QAM调制_R";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 33;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "64QAM调制占比";
            this.gridColumn27.FieldName = "F64QAM调制_R";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 34;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "平均下行MCS";
            this.gridColumn28.FieldName = "F平均下行MCS";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 35;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "下行平均每秒调度PRB个数";
            this.gridColumn29.FieldName = "F平均分配下行PRB数";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 36;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "TM1占比";
            this.gridColumn30.FieldName = "FTM1_R";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 38;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "TM2占比";
            this.gridColumn31.FieldName = "FTM2_R";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 39;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "TM3占比";
            this.gridColumn32.FieldName = "FTM3_R";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 40;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "TM7占比";
            this.gridColumn33.FieldName = "FTM7_R";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 41;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "TM8占比";
            this.gridColumn34.FieldName = "FTM8_R";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 42;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "PHY_Code0_DL平均速率(Mbps)";
            this.gridColumn35.FieldName = "FPHY_Code0_DL平均速率";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 43;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "平均PDSCH_Code0_BLER";
            this.gridColumn36.FieldName = "F平均PDSCH_Code0_BLER";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 44;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "Code0_QPSK调整占比";
            this.gridColumn37.FieldName = "FCode0_QPSK调整_R";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 45;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "Code0_16QAM调制占比";
            this.gridColumn38.FieldName = "FCode0_16QAM调制_R";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 46;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "Code0_64QAM调制占比";
            this.gridColumn39.FieldName = "FCode0_64QAM调制_R";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 47;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "Code0_平均下行MCS";
            this.gridColumn40.FieldName = "FCode0_平均下行MCS";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 48;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "平均lte_Wideband_CQI_for_CW0";
            this.gridColumn41.FieldName = "F平均lte_Wideband_CQI_for_CW0";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 49;
            // 
            // gridColumn64
            // 
            this.gridColumn64.Caption = "lte_Wideband_CQI_for_CW0>10占比";
            this.gridColumn64.FieldName = "Flte_Wideband_CQI_for_CW0_10_R";
            this.gridColumn64.Name = "gridColumn64";
            this.gridColumn64.Visible = true;
            this.gridColumn64.VisibleIndex = 50;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "RANK2_SINR1";
            this.gridColumn42.FieldName = "FRANK2_SINR1";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 51;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "PHY_Code1_DL平均速率(Mbps)";
            this.gridColumn43.FieldName = "FPHY_Code1_DL平均速率";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 52;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "平均PDSCH_Code1_BLER";
            this.gridColumn44.FieldName = "F平均PDSCH_Code1_BLER";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 53;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "Code1_QPSK调整占比";
            this.gridColumn45.FieldName = "FCode1_QPSK调整_R";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 54;
            // 
            // gridColumn46
            // 
            this.gridColumn46.Caption = "Code1_16QAM调制占比";
            this.gridColumn46.FieldName = "FCode1_16QAM调制_R";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 55;
            // 
            // gridColumn47
            // 
            this.gridColumn47.Caption = "Code1_64QAM调制占比";
            this.gridColumn47.FieldName = "FCode1_64QAM调制_R";
            this.gridColumn47.Name = "gridColumn47";
            this.gridColumn47.Visible = true;
            this.gridColumn47.VisibleIndex = 56;
            // 
            // gridColumn48
            // 
            this.gridColumn48.Caption = "Code1_平均下行MCS";
            this.gridColumn48.FieldName = "FCode1_平均下行MCS";
            this.gridColumn48.Name = "gridColumn48";
            this.gridColumn48.Visible = true;
            this.gridColumn48.VisibleIndex = 57;
            // 
            // gridColumn49
            // 
            this.gridColumn49.Caption = "平均lte_Wideband_CQI_for_CW1";
            this.gridColumn49.FieldName = "F平均lte_Wideband_CQI_for_CW1";
            this.gridColumn49.Name = "gridColumn49";
            this.gridColumn49.Visible = true;
            this.gridColumn49.VisibleIndex = 58;
            // 
            // gridColumn65
            // 
            this.gridColumn65.Caption = "lte_Wideband_CQI_for_CW1>10占比";
            this.gridColumn65.FieldName = "Flte_Wideband_CQI_for_CW1_10_R";
            this.gridColumn65.Name = "gridColumn65";
            this.gridColumn65.Visible = true;
            this.gridColumn65.VisibleIndex = 59;
            // 
            // gridColumn50
            // 
            this.gridColumn50.Caption = "RANK2_SINR2";
            this.gridColumn50.FieldName = "FRANK2_SINR2";
            this.gridColumn50.Name = "gridColumn50";
            this.gridColumn50.Visible = true;
            this.gridColumn50.VisibleIndex = 60;
            // 
            // gridColumn51
            // 
            this.gridColumn51.Caption = "双流功率不平衡|RSRP0-RSRP1|";
            this.gridColumn51.FieldName = "F双流功率不平衡_RSRP0_RSRP1";
            this.gridColumn51.Name = "gridColumn51";
            this.gridColumn51.Visible = true;
            this.gridColumn51.VisibleIndex = 61;
            // 
            // gridColumn52
            // 
            this.gridColumn52.Caption = "RSRP0>-110比例";
            this.gridColumn52.FieldName = "FRSRP0_110_R";
            this.gridColumn52.Name = "gridColumn52";
            this.gridColumn52.Visible = true;
            this.gridColumn52.VisibleIndex = 62;
            // 
            // gridColumn53
            // 
            this.gridColumn53.Caption = "RSRP1>-110比例";
            this.gridColumn53.FieldName = "FRSRP1_110_R";
            this.gridColumn53.Name = "gridColumn53";
            this.gridColumn53.Visible = true;
            this.gridColumn53.VisibleIndex = 63;
            // 
            // gridColumn54
            // 
            this.gridColumn54.Caption = "SINR0>-3的比例";
            this.gridColumn54.FieldName = "FSINR0_3_R";
            this.gridColumn54.Name = "gridColumn54";
            this.gridColumn54.Visible = true;
            this.gridColumn54.VisibleIndex = 64;
            // 
            // gridColumn55
            // 
            this.gridColumn55.Caption = "SINR1>-3的比例";
            this.gridColumn55.FieldName = "FSINR1_3_R";
            this.gridColumn55.Name = "gridColumn55";
            this.gridColumn55.Visible = true;
            this.gridColumn55.VisibleIndex = 65;
            // 
            // gridColumn56
            // 
            this.gridColumn56.Caption = "同频RSRP>(主小区RSRP-6)的邻区数平均";
            this.gridColumn56.FieldName = "F同频主小区平均邻区数";
            this.gridColumn56.Name = "gridColumn56";
            this.gridColumn56.Visible = true;
            this.gridColumn56.VisibleIndex = 66;
            // 
            // gridColumn57
            // 
            this.gridColumn57.Caption = "同频RSRP>(主小区RSRP-6)的最强邻区E-UtranID";
            this.gridColumn57.FieldName = "I同频主小区最强邻区E_UtranID";
            this.gridColumn57.Name = "gridColumn57";
            this.gridColumn57.Visible = true;
            this.gridColumn57.VisibleIndex = 67;
            // 
            // gridColumn58
            // 
            this.gridColumn58.Caption = "同频RSRP>(主小区RSRP-6)的最强邻区中文名";
            this.gridColumn58.FieldName = "Str同频主小区最强邻区中文名";
            this.gridColumn58.Name = "gridColumn58";
            this.gridColumn58.Visible = true;
            this.gridColumn58.VisibleIndex = 68;
            // 
            // gridColumn59
            // 
            this.gridColumn59.Caption = "同频RSRP>(主小区RSRP-6)的采样点占比";
            this.gridColumn59.FieldName = "F同频主小区采样点_R";
            this.gridColumn59.Name = "gridColumn59";
            this.gridColumn59.Visible = true;
            this.gridColumn59.VisibleIndex = 69;
            // 
            // gridColumn60
            // 
            this.gridColumn60.Caption = "同频RSRP>(主小区RSRP-6)的且mod3相同邻区数平均";
            this.gridColumn60.FieldName = "F同频主小区mod3平均邻区数";
            this.gridColumn60.Name = "gridColumn60";
            this.gridColumn60.Visible = true;
            this.gridColumn60.VisibleIndex = 70;
            // 
            // gridColumn61
            // 
            this.gridColumn61.Caption = "同频RSRP>(主小区RSRP-6)的且mod3相同最强邻区E-UtranID";
            this.gridColumn61.FieldName = "I同频主小区mod3最强邻区E_UtranID";
            this.gridColumn61.Name = "gridColumn61";
            this.gridColumn61.Visible = true;
            this.gridColumn61.VisibleIndex = 71;
            // 
            // gridColumn62
            // 
            this.gridColumn62.Caption = "同频RSRP>(主小区RSRP-6)的且mod3相同最强邻区中文名";
            this.gridColumn62.FieldName = "Str同频主小区mod3最强邻区中文名";
            this.gridColumn62.Name = "gridColumn62";
            this.gridColumn62.Visible = true;
            this.gridColumn62.VisibleIndex = 72;
            // 
            // gridColumn63
            // 
            this.gridColumn63.Caption = "同频RSRP>(主小区RSRP-6)的且mod3相同的采样点占比";
            this.gridColumn63.FieldName = "F同频主小区mod3的采样点_R";
            this.gridColumn63.Name = "gridColumn63";
            this.gridColumn63.Visible = true;
            this.gridColumn63.VisibleIndex = 73;
            // 
            // gridColumn74
            // 
            this.gridColumn74.Caption = "下行平均每时隙调度PRB个数";
            this.gridColumn74.FieldName = "F平均时隙分配下行PRB数";
            this.gridColumn74.Name = "gridColumn74";
            this.gridColumn74.Visible = true;
            this.gridColumn74.VisibleIndex = 37;
            // 
            // LTEDownloadPerformanceForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1012, 444);
            this.Controls.Add(this.gridData);
            this.Name = "LTEDownloadPerformanceForm";
            this.Text = "数据业务下载性能评估";
            ((System.ComponentModel.ISupportInitialize)(this.gridData)).EndInit();
            this.conOutPutExcel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridData;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn51;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn52;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn53;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn54;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn55;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn56;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn57;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn58;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn59;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn60;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn61;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn62;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn63;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn64;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn65;
        private System.Windows.Forms.ContextMenuStrip conOutPutExcel;
        private System.Windows.Forms.ToolStripMenuItem outPutExcel;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn66;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn67;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn68;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn69;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn70;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn71;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn72;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn73;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn74;
    }
}