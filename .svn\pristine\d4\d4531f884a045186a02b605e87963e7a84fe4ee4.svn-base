﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    class LteURLSummarize
    {
        SummarizeModel result = null;
        Dictionary<string, List<LteURLBroURL>> dicBro = null;
        Dictionary<string, List<LteURLDowURL>> dicDown = null;
        Dictionary<string, List<LteURLVideoURL>> dicVideo = null;
        string DistrictName = null;
        List<string> DistrictNames { get; set; }

        public SummarizeModel Summarize(List<LteURLModel> results)
        {
            this.result = new SummarizeModel();
            this.dicBro = new Dictionary<string, List<LteURLBroURL>>();
            this.dicDown = new Dictionary<string, List<LteURLDowURL>>();
            this.dicVideo = new Dictionary<string, List<LteURLVideoURL>>();
            this.DistrictNames = new List<string>();
            foreach (LteURLModel dtlModel in results)
            {
                if (!this.DistrictNames.Contains(dtlModel.DistrictName))
                {
                    this.DistrictNames.Add(dtlModel.DistrictName);
                }
            }
            foreach (string districtName in this.DistrictNames)
            {
                foreach (LteURLModel dtlModel in results)
                {
                    if (districtName == dtlModel.DistrictName)
                    {
                        this.DistrictName = districtName;
                        Summarize(dtlModel);
                    }
                }
                Summarize();
            }
            this.DistrictNames.Clear();
            this.DistrictName = null;
            return this.result;
        }

        private void Summarize(LteURLModel dtlModel)
        {
            foreach (LteURLBroURL bro in dtlModel.Bros)
            {
                if (dicBro.ContainsKey(bro.URL))
                {
                    dicBro[bro.URL].Add(bro);
                }
                else
                {
                    List<LteURLBroURL> listTemp = new List<LteURLBroURL>();
                    listTemp.Add(bro);
                    dicBro.Add(bro.URL, listTemp);
                }
            }
            foreach (LteURLDowURL down in dtlModel.Downs)
            {
                if (dicDown.ContainsKey(down.URL))
                {
                    dicDown[down.URL].Add(down);
                }
                else
                {
                    List<LteURLDowURL> listTemp = new List<LteURLDowURL>();
                    listTemp.Add(down);
                    dicDown.Add(down.URL, listTemp);
                }
            }
            foreach (LteURLVideoURL video in dtlModel.Videos)
            {
                if (dicVideo.ContainsKey(video.URL))
                {
                    dicVideo[video.URL].Add(video);
                }
                else
                {
                    List<LteURLVideoURL> listTemp = new List<LteURLVideoURL>();
                    listTemp.Add(video);
                    dicVideo.Add(video.URL, listTemp);
                }
            }

        }

        private void Summarize()
        {
            foreach (KeyValuePair<string, List<LteURLBroURL>> kvp in dicBro)
            {
                SummarizeBro summBro = new SummarizeBro();
                summBro.URL = kvp.Key;
                summBro.DistrictName = DistrictName;
                summBro.Bros = kvp.Value;
                SetInfo(summBro);
                result.Bros.Add(summBro);
            }
            dicBro.Clear();
            foreach (KeyValuePair<string, List<LteURLDowURL>> kvp in dicDown)
            {
                SummarizeDown summDown = new SummarizeDown();
                summDown.DistrictName = DistrictName;
                summDown.URL = kvp.Key;
                summDown.Downs = kvp.Value;
                SetInfo(summDown);
                result.Downs.Add(summDown);
            }
            dicDown.Clear();
            foreach (KeyValuePair<string, List<LteURLVideoURL>> kvp in dicVideo)
            {
                SummarizeVideo summVideo = new SummarizeVideo();
                summVideo.URL = kvp.Key;
                summVideo.DistrictName = DistrictName;
                summVideo.Videos = kvp.Value;
                SetInfo(summVideo);
                result.Videos.Add(summVideo);
            }
            dicVideo.Clear();
        }

        private void SetInfo(SummarizeBro bro)
        {
            double? Value2Sum = 0;
            double? Value1Sum = 0;
            double? Value2Sum1 = 0;
            int sumDisplayCount = 0;
            int sumDisSucCount = 0;
            int sumCompleteCount = 0;
            foreach (LteURLBroURL broURL in bro.Bros)
            {
                sumCompleteCount += broURL.Complete;
                sumDisplayCount += broURL.DisCount;
                sumDisSucCount += broURL.DisSuc;
                Value2Sum += broURL.Value2Sum;
                Value1Sum += broURL.Value1Sum;
                Value2Sum1 += broURL.Value2Sum1;

            }
            bro.CompleteCount = sumCompleteCount;
            bro.DisplayCount = sumDisplayCount;
            bro.DisSucCount = sumDisSucCount;
            bro.DisplayRate = Math.Round(((float)sumDisSucCount / (float)sumDisplayCount) * 100, 2);
            bro.CompleteRate = Math.Round(((float)sumCompleteCount / (float)sumDisSucCount) * 100, 2);
            bro.DisplayDelay = Math.Round((double)Value2Sum / (double)sumDisSucCount / 1000, 3);
            bro.Time = Math.Round((double)Value1Sum / (double)sumCompleteCount / 1000, 3);
            bro.Speed = Math.Round((((double)Value2Sum1 * 8000) / (double)Value1Sum) / 1024, 3);
        }

        private void SetInfo(SummarizeDown down)
        {
            double? Value1Sum = 0;
            double? Value2Sum = 0;
            double? Value1Sum1 = 0;
            double? Value2Sum1 = 0;
            int sumDowcount = 0;
            int sumDowSuc = 0;
            int sumDowFail = 0;
            foreach (LteURLDowURL dowURL in down.Downs)
            {
                sumDowFail += dowURL.DowFail;
                sumDowcount += dowURL.Dowcount;
                sumDowSuc += dowURL.DowSuc;
                Value1Sum += dowURL.Value1Sum;
                Value2Sum += dowURL.Value2Sum;
                Value1Sum1 += dowURL.Value1Sum1;
                Value2Sum1 += dowURL.Value2Sum1;
            }
            down.Dowcount = sumDowcount;
            down.DowSuc = sumDowSuc;
            down.DowFail = sumDowFail;
            down.SucSpeed = Math.Round(((double)Value2Sum / (double)Value1Sum) * 8000 / 1024, 3);
            down.Speed = Math.Round(((double)(Value2Sum + Value2Sum1) / (double)(Value1Sum + Value1Sum1)) * 8000 / 1024, 3);
            down.DowSucRate = Math.Round(((float)sumDowSuc / (float)sumDowcount) * 100, 2);
            down.DowFaiRate = 100 - down.DowSucRate;
        }

        private void SetInfo(SummarizeVideo video)
        {
            double? value1Sum38 = 0;
            double? value7Sum35 = 0;
            double? value6Sum35 = 0;
            double? value5Sum35 = 0;
            double? value4Sum35 = 0;
            double? value2Sum35 = 0;
            double? value1Sum35 = 0;
            double? value2Sum34 = 0;
            double? value2Sum29 = 0;
            int sumReqCount = 0;
            int sumSucCount = 0;
            int sumCount = 0;
            int sumRebufferCount = 0;
            int sumStartCount = 0;
            int sumRebufferEndCount = 0;
            int sumFlvPlayFinishedCount = 0;
            foreach (LteURLVideoURL videoURL in video.Videos)
            {
                sumRebufferCount += videoURL.RebufferCount;
                sumReqCount += videoURL.ReqCount;
                sumSucCount += videoURL.SucCount;
                sumCount += videoURL.EvtLastData.Count;
                sumStartCount += videoURL.EvtPlayStart.Count;
                sumRebufferEndCount += videoURL.RebufferEndCount;
                sumFlvPlayFinishedCount += videoURL.FlvPlayFinishedCount;

                value7Sum35 += videoURL.value7Sum35;
                value6Sum35 += videoURL.value6Sum35;
                value5Sum35 += videoURL.value5Sum35;
                value4Sum35 += videoURL.value4Sum35;
                value2Sum35 += videoURL.value2Sum35;
                value1Sum35 += videoURL.value1Sum35;
                value1Sum38 += videoURL.value1Sum38;
                value2Sum29 += videoURL.value2Sum29;
                value2Sum34 += videoURL.value2Sum34;

            }
            video.ReqCount = sumReqCount;
            video.SucCount = sumSucCount;
            video.SucRate = Math.Round(((float)sumSucCount / (float)sumReqCount) * 100, 2);
            video.Time = Math.Round(((double)value6Sum35 / (double)sumCount) / 1000, 3);
            video.RebufferCount = sumRebufferCount;
            video.RebufferTime = Math.Round(((double)(value2Sum29 + value2Sum34) / (double)(sumRebufferEndCount + sumFlvPlayFinishedCount)) / 1000, 3);
            video.PlayTime = Math.Round(((double)value5Sum35 / (double)sumCount) / 1000, 3);
            video.TimeoutRate = Math.Round(((double)value5Sum35 / (double)value6Sum35 - 1) * 100, 2);
            video.DownSpeed = Math.Round(((double)value2Sum35 / (double)value1Sum35) * 8000 / 1024, 3);
            video.Delay = Math.Round(((double)value1Sum38 / (double)sumStartCount) / 1000, 3);
            video.LoadSpeed = Math.Round(((double)value7Sum35 / (double)value1Sum38) * 8000 / 1024, 3);
        }
    }

    class SummarizeModel
    {
        public SummarizeModel()
        {
            this.Bros = new List<SummarizeBro>();
            this.Downs = new List<SummarizeDown>();
            this.Videos = new List<SummarizeVideo>();
        }
        public List<SummarizeBro> Bros
        {
            get;
            set;
        }
        public List<SummarizeDown> Downs
        {
            get;
            set;
        }
        public List<SummarizeVideo> Videos
        {
            get;
            set;
        }
    }
    class SummarizeBro
    {
        public SummarizeBro()
        {
            this.Bros = new List<LteURLBroURL>();
        }
        public List<LteURLBroURL> Bros
        {
            get;
            set;
        }
        public int SN
        {
            get;
            set;
        }
        public string DistrictName
        {
            get;
            set;
        }
        public string URL
        {
            get;
            set;
        }
        public int DisplayCount
        {
            get;
            set;
        }
        public int DisSucCount
        {
            get;
            set;
        }
        public int CompleteCount
        {
            get;
            set;
        }
        public double? DisplayRate
        {
            get;
            set;
        }
        public double? DisplayDelay
        {
            get;
            set;
        }
        public double? CompleteRate
        {
            get;
            set;
        }
        public double? Time
        {
            get;
            set;
        }
        public double? Speed
        {
            get;
            set;
        }
    }
    class SummarizeDown
    {
        public SummarizeDown()
        {
            this.Downs = new List<LteURLDowURL>();
        }
        public List<LteURLDowURL> Downs
        {
            get;
            set;
        }
        public int SN
        {
            get;
            set;
        }
        public string DistrictName
        {
            get;
            set;
        }
        public string URL
        {
            get;
            set;
        }
        public int Dowcount
        {
            get;
            set;
        }
        public int DowSuc
        {
            get;
            set;
        }
        public double? DowSucRate
        {
            get;
            set;
        }
        public int DowFail
        {
            get;
            set;
        }
        public double? DowFaiRate
        {
            get;
            set;
        }
        public double? SucSpeed
        {
            get;
            set;
        }
        public double? Speed
        {
            get;
            set;
        }
    }
    class SummarizeVideo
    {
        public SummarizeVideo()
        {
            this.Videos = new List<LteURLVideoURL>();
        }
        public List<LteURLVideoURL> Videos
        {
            get;
            set;
        }
        public int SN
        {
            get;
            set;
        }
        public string DistrictName
        {
            get;
            set;
        }
        public string URL
        {
            get;
            set;
        }
        public int ReqCount
        {
            get;
            set;
        }
        public int SucCount
        {
            get;
            set;
        }

        public double? SucRate
        {
            get;
            set;
        }
        public double? Delay
        {
            get;
            set;
        }
        public double? Time
        {
            get;
            set;
        }
        public double? RebufferTime
        {
            get;
            set;
        }
        public double? PlayTime
        {
            get;
            set;
        }
        public int RebufferCount
        {
            get;
            set;
        }
        public double? TimeoutRate
        {
            get;
            set;
        }
        public double? DownSpeed
        {
            get;
            set;
        }
        public double? LoadSpeed
        {
            get;
            set;
        }
    }
}
