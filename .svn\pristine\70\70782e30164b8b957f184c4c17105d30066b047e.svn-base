﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTHandOverAndCellReselSetForm : BaseDialog
    {
        public ZTHandOverAndCellReselSetForm()
        {
            InitializeComponent();
            
        }

        public void SetCondition(ZTHandOverAndCellReselCondition hoAndCrCondition)
        {
            numTimeLimit.Value = (decimal)hoAndCrCondition.TimeLimit;
            chkSpeedLimit.Checked = hoAndCrCondition.IsLimitSpeed;
            numSpeedLimitMin.Value = (decimal)hoAndCrCondition.SpeedLimitMin;
            numSpeedLimitMax.Value = (decimal)hoAndCrCondition.SpeedLimitMax;
            chkSpeedLimit_CheckedChanged(null, null);
        }

        public void GetCondition(out ZTHandOverAndCellReselCondition hoAndCrCondition)
        {
            hoAndCrCondition = new ZTHandOverAndCellReselCondition();
            hoAndCrCondition.TimeLimit = (int)numTimeLimit.Value;
            hoAndCrCondition.IsLimitSpeed = chkSpeedLimit.Checked;
            hoAndCrCondition.SpeedLimitMin = (int)numSpeedLimitMin.Value;
            hoAndCrCondition.SpeedLimitMax = (int)numSpeedLimitMax.Value;
        }

        private void chkSpeedLimit_CheckedChanged(object sender, EventArgs e)
        {
            numSpeedLimitMin.Enabled = chkSpeedLimit.Checked;
            numSpeedLimitMax.Enabled = chkSpeedLimit.Checked;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (chkSpeedLimit.Checked && (int)numSpeedLimitMin.Value > (int)numSpeedLimitMax.Value)
            {
                MessageBox.Show("时速最小值不能超过最大值，请重新设置。");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
