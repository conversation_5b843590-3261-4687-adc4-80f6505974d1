﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Stat;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public class TemplateMngr
    {
        public static readonly string PATH = Application.StartupPath + "\\config\\AreaArchive\\KPITemplate\\";
        private static TemplateMngr instance = null;
        private TemplateMngr()
        {
            Init();
        }

        public void Init()
        {
            ReportTemplates = new List<AreaReportTemplate>();
            if (!System.IO.Directory.Exists(PATH))
            {
                return;
            }
            try
            {
                System.IO.DirectoryInfo directory = new System.IO.DirectoryInfo(PATH);
                System.IO.FileInfo[] files = directory.GetFiles("*.xml", System.IO.SearchOption.TopDirectoryOnly);
                if (files.Length > 0)
                {
                    foreach (System.IO.FileInfo file in files)
                    {
                        XmlConfigFile configFile = new XmlConfigFile(file.FullName);
                        Dictionary<string, object> dic = configFile.GetItemValue("Template", "Options") as Dictionary<string, object>;
                        if (dic == null)
                        {
                            continue;
                        }
                        AreaReportTemplate rptstyle = new AreaReportTemplate();
                        rptstyle.Param = dic;
                        ReportTemplates.Add(rptstyle);
                    }
                }
            }
            catch (Exception e)
            {
                System.Windows.Forms.MessageBox.Show("加载档案模板异常。" + e.Message);
            }
        }

        public static TemplateMngr Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new TemplateMngr();
                }
                return instance;
            }
        }

        public List<AreaReportTemplate> ReportTemplates
        {
            get;
            set;
        }
    }

    public class AreaReportTemplate
    {
        public override string ToString()
        {
            return Name;
        }
        public AreaReportTemplate()
        {
            Columns = new List<TemplateColumn>();
        }
        public string Name
        {
            get;
            set;
        }

        public List<TemplateColumn> Columns
        {
            get;
            set;
        }

        public void Save()
        {
            XmlConfigFile configFile = new XmlConfigFile();
            System.Xml.XmlElement cfg = configFile.AddConfig("Template");
            configFile.AddItem(cfg, "Options", this.Param);
            try
            {
                configFile.Save(TemplateMngr.PATH + Name + ".xml");
            }
            catch
            {
                //continue
            }
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic["Name"] = Name;
                List<object> list = new List<object>();
                foreach (TemplateColumn col in this.Columns)
                {
                    list.Add(col.Param);
                }
                dic["Columns"] = list;
                return dic;
            }
            set
            {
                if (value==null)
                {
                    return;
                }
                Name = value["Name"].ToString();
                Columns = new List<TemplateColumn>();
                List<object> list = value["Columns"] as List<object>;
                foreach (Dictionary<string,object> item in list)
                {
                    TemplateColumn col = new TemplateColumn();
                    col.Param = item;
                    Columns.Add(col);
                }
            }
        }

    }

    public class TemplateColumn
    {
        public TemplateColumn()
        {
            DynamicBKColorRanges = new List<DTParameterRangeColor>();
        }

        public override string ToString()
        {
            return this.Caption;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Caption"] = Caption;
                param["Expression"] = Expression;
                param["CarrierID"] = (int)(CarrierID);
                param["MoMtFlag"] = (int)(MoMtFlag);
                param["IsDynamicBKClr"] = IsDynamicBKColor;
                List<object> colorParam = new List<object>();
                if (DynamicBKColorRanges != null)
                {
                    foreach (DTParameterRangeColor item in DynamicBKColorRanges)
                    {
                        colorParam.Add(item.Params);
                    }
                }
                param["ColorParam"] = colorParam;
                return param;
            }
            set
            {
                Caption = (string)value["Caption"];
                Expression = (string)value["Expression"];
                if (value.ContainsKey("IsDynamicBKClr"))
                {
                    IsDynamicBKColor = (bool)value["IsDynamicBKClr"];
                }
                if (value.ContainsKey("CarrierID"))
                {
                    CarrierID = (byte)(int)value["CarrierID"];
                }

                if (value.ContainsKey("MoMtFlag"))
                {
                    MoMtFlag = (byte)(int)value["MoMtFlag"];
                }

                List<object> colorParam = value["ColorParam"] as List<object>;
                if (colorParam != null)
                {
                    float fMin = float.MaxValue, fMax = float.MinValue;
                    this.DynamicBKColorRanges = new List<DTParameterRangeColor>();
                    foreach (object item in colorParam)
                    {
                        DTParameterRangeColor color = new DTParameterRangeColor();
                        color.Params = (Dictionary<string, object>)item;
                        this.DynamicBKColorRanges.Add(color);

                        if (fMin > color.Min)
                        {
                            fMin = color.Min;
                        }
                        if (fMax < color.Max)
                        {
                            fMax = color.Max;
                        }
                    }
                    if (fMin != float.MaxValue && fMax != float.MinValue)
                    {
                        valueRamgeMin = fMin;
                        valueRangeMax = fMax;
                    }
                }
            }
        }

        public string Caption { get; set; }
        public string Expression { get; set; } = string.Empty;
        public byte CarrierID { get; set; } = 1;//默认运营商是中国移动
        public byte MoMtFlag { get; set; } = 0;
        public bool IsDynamicBKColor
        {
            get;
            set;
        }
        private float valueRamgeMin = 0;
        public float ValueRangeMin
        {
            get { return valueRamgeMin; }
            set
            {
                if (value <= valueRangeMax)
                {
                    valueRamgeMin = value;
                }
            }
        }
        private float valueRangeMax = 100;
        public float ValueRangeMax
        {
            get { return valueRangeMax; }
            set
            {
                if (value >= valueRamgeMin)
                {
                    valueRangeMax = value;
                }
            }
        }
        public List<DTParameterRangeColor> DynamicBKColorRanges { get; set; }

        public Color GetBKColorByValue(double value)
        {
            Color ret = Color.Empty;
            if (IsDynamicBKColor && DynamicBKColorRanges != null)
            {
                foreach (DTParameterRangeColor item in DynamicBKColorRanges)
                {
                    if (item.Within((float)value))
                    {
                        ret = item.Value;
                        break;
                    }
                }
            }
            return ret;
        }
    }

}
