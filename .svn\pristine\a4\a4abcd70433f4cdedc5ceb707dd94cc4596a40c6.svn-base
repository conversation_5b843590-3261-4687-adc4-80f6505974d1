﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class VolteMosWithRtpEvtAnaByRegion : VolteMosWithRtpEvtAnaBase
    {
        protected VolteMosWithRtpEvtAnaByRegion()
            : base()
        {
            FilterSampleByRegion = true;
        }

        private static VolteMosWithRtpEvtAnaByRegion instance = null;
        public static VolteMosWithRtpEvtAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VolteMosWithRtpEvtAnaByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "弱MOS关联RTP问题分析(按区域)"; }
        }
    }

    public class VolteMosWithRtpEvtAnaByRegion_FDD : VolteMosWithRtpEvtAnaBase_FDD
    {
        private static VolteMosWithRtpEvtAnaByRegion_FDD instance = null;
        public static VolteMosWithRtpEvtAnaByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VolteMosWithRtpEvtAnaByRegion_FDD();
                    }
                }
            }
            return instance;
        }
        protected VolteMosWithRtpEvtAnaByRegion_FDD()
            : base()
        {
            FilterSampleByRegion = true;
        }
        public override string Name
        {
            get { return "弱MOS关联RTP问题分析(VOLTE_FDD按区域)"; }
        }
        
    }
}
