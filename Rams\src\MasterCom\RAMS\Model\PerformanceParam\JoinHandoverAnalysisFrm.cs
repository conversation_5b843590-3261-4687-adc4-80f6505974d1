﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using Steema.TeeChart.Styles;
using DevExpress.XtraCharts;
using MasterCom.RAMS.Func.NebulaForm;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Model.PerformanceParam;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Model.PerformanceParam
{
    public partial class JoinHandoverAnalysisFrm : DevExpress.XtraEditors.XtraForm
    {
        MainModel mainModel;
        private AllNebulaDetailForm allNebulaDetailForm;
        private bool IsTDAnalysis = false;
        public JoinHandoverAnalysisFrm(MainModel mainModel,bool IsTDAnalysis)
        {
            this.IsTDAnalysis = IsTDAnalysis;

            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = global::MasterCom.RAMS.Properties.Resources.unfrozen;
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            InitializeComponent();

            if (IsTDAnalysis)
            {
                this.Text += "(TD)";
            }
            else
            {
                this.Text += "(GSM)";
            }

            this.mainModel = mainModel;

            detailform = new NebulaDetailForm(mainModel,IsTDAnalysis);
            allNebulaDetailForm = new AllNebulaDetailForm(mainModel,IsTDAnalysis);

            this.panelControl1.BackColor = Color.White;

            detailform.TopLevel = false;
            detailform.FormBorderStyle = FormBorderStyle.None;
            this.panelControl2.Controls.Add(detailform);
            detailform.Size = this.panelControl2.Size;
            detailform.ShowEleDetail = new NebulaDetailForm.ShowElementDetailInf(ShowEleDetailInf);
            detailform.Show();


            allNebulaDetailForm.TopLevel = false;
            allNebulaDetailForm.FormBorderStyle = FormBorderStyle.None;
            this.panelControlAllNebulaDetail.Controls.Add(allNebulaDetailForm);
            allNebulaDetailForm.Size = this.panelControlAllNebulaDetail.Size;
            allNebulaDetailForm.Show();

            nebulaForm = new HandoverNebulaForm(detailform, this,IsTDAnalysis);
            
        }
        //点击查看详细时查看详细
        private void ShowEleDetailInf(string lac_ci)
        {
            allNebulaDetailForm.SetMainGridviewFocueCellLocation(lac_ci);
            xtraTabControl1.SelectedTabPage = xtraTabPage3;
        }
        
        public JoinHandoverAnalysisFrm AnalysisFrm { get; set; }
        NebulaDetailForm detailform;
        HandoverNebulaForm nebulaForm;
        public BindingSource Source { get; set; } = new BindingSource();
        public List<HandoverNebulaForm.DataXY> dataxyList { get; set; } = new List<HandoverNebulaForm.DataXY>();
        private Dictionary<int, List<JoinHandoverAnalysis>> handoverAnalysisDic = new Dictionary<int, List<JoinHandoverAnalysis>>();

        public Dictionary<int, List<JoinHandoverAnalysis>> HandoverAnalysisDic
        {
            get { return handoverAnalysisDic; }
            set
            {
                allNebulaDetailForm.HandoverAnalysisDic = value;
                handoverAnalysisDic = value;
            }
        }


        public void ChangeTabControl()
        {
            this.xtraTabControl1.SelectedTabPageIndex = 1;
        }
        /// <summary>
        /// 加载象限图
        /// </summary>
        public void LoadNebula()
        {
            if (XYData.TargetX != "性能侧切换成功率")
            {
                XYData.TarXchanged = true;
            }

            if (XYData.TargetY != "路测切换次数")
            {
                XYData.TarYchanged = true;
            }
            XYData.Xsmall = 0;
            XYData.Xlarge = 100;
            if (IsTDAnalysis)
            {
                XYData.Xcenter = 97;
            }
            else
            {
                XYData.Xcenter = 90;
            }
            XYData.Ysmall = 0;
            XYData.Ylarge = 50;
            XYData.Ycenter = 5;
            XYData.Xcomboboxindex = 0;
            XYData.Ycomboboxindex = 0;
            XYData.Xcheckbox = true;
            XYData.Ycheckbox = true;
            XYData.Pointcheckbox = true;
            if (IsTDAnalysis)
            {
                XYData.TargetX = "97%";
            }
            else
            {
                XYData.TargetX = "90%";
            }
            XYData.TargetY = "5";
            XYData.ColorFirst = Color.DodgerBlue;
            XYData.ColorSecond = Color.Red;
            XYData.ColorThird = Color.Orange;
            XYData.ColorFourth = Color.Green;

            nebulaForm.Dataxy.AddRange(dataxyList);
            nebulaForm.HandoverAnalysisDic = handoverAnalysisDic;
            nebulaForm.TopLevel = false;
            nebulaForm.FormBorderStyle = FormBorderStyle.None;
            this.panelControl1.Controls.Add(nebulaForm);
            nebulaForm.MaximizeBox = true;
            nebulaForm.Size = this.panelControl1.Size;
            nebulaForm.Show();


        }

        /// <summary>
        /// 当窗体大小变化时
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void JoinHandoverAnalysisFrm_SizeChanged(object sender, EventArgs e)
        {
            if (detailform != null)
            {
                detailform.Size = this.xtraTabPage2.Size;
                detailform.Refresh();
            }
            if (nebulaForm != null)
            {
                nebulaForm.Size = this.xtraTabPage1.Size;
                nebulaForm.Refresh();
            }
        }

        /// <summary>
        /// 验证数据是否在所选区域内
        /// </summary>
        private List<JoinHandoverAnalysis> VerificationRegion(List<JoinHandoverAnalysis> gssList)
        {
            List<JoinHandoverAnalysis> newGsslist = new List<JoinHandoverAnalysis>();
            foreach (JoinHandoverAnalysis gss in gssList)
            {
                string date = gss.IMon.ToString().Substring(0, 4) + "-" + gss.IMon.ToString().Substring(4, 2);
                DateTime dateTime = Convert.ToDateTime(date);
                Cell cell = mainModel.CellManager.GetCell(dateTime, (ushort)gss.ILac, (ushort)gss.ICi);
                if (cell != null)
                {
                    if (mainModel.SearchGeometrys.GeoOp.Contains(cell.Longitude, cell.Latitude))
                    {
                        newGsslist.Add(gss);
                    }
                }
                else
                {
                    TDCell tdCell = mainModel.CellManager.GetTDCell(dateTime, (ushort)gss.ILac, (ushort)gss.ICi);
                    if (tdCell != null && mainModel.SearchGeometrys.GeoOp.Contains(tdCell.Longitude, tdCell.Latitude))
                    {
                        newGsslist.Add(gss);
                    }
                }
            }
            return newGsslist;
        }

        private void 查看区域内所有小区切换情况ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            List<JoinHandoverAnalysis> witchingList = VerificationRegion((List<JoinHandoverAnalysis>)Source.DataSource);
            mainModel.HandoverAnalysisList = witchingList;
            mainModel.FireDTDataChanged(this);
        }

        private void 导出ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //ExcelNPOIManager.ExportToExcel(gridView_result)
        }

        private void panelControl2_SizeChanged(object sender, EventArgs e)
        {
            if (detailform != null)
            {
                detailform.Size = this.panelControl2.Size;
            }
        }

        private void panelControlAllNebulaDetail_SizeChanged(object sender, EventArgs e)
        {
            if (allNebulaDetailForm != null)
            {
                allNebulaDetailForm.Size = panelControlAllNebulaDetail.Size;
            }
        }

        private void panelControl1_SizeChanged(object sender, EventArgs e)
        {
            if (nebulaForm != null)
            {
                nebulaForm.Size = panelControl1.Size;
            }
        }
    }
}