﻿namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    partial class IndoorLeakCoverPnl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.grpOutdoorWC = new System.Windows.Forms.GroupBox();
            this.numWCRSRPDiff = new DevExpress.XtraEditors.SpinEdit();
            this.numWCDis = new DevExpress.XtraEditors.SpinEdit();
            this.label7 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.grpOverCover = new System.Windows.Forms.GroupBox();
            this.label3 = new System.Windows.Forms.Label();
            this.numDisMin = new DevExpress.XtraEditors.SpinEdit();
            this.label5 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.numRSRPMin = new DevExpress.XtraEditors.SpinEdit();
            this.label4 = new System.Windows.Forms.Label();
            this.grpHO = new System.Windows.Forms.GroupBox();
            this.numHOSecond = new DevExpress.XtraEditors.SpinEdit();
            this.numHORSRPDiff = new DevExpress.XtraEditors.SpinEdit();
            this.label8 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.groupBox1.SuspendLayout();
            this.grpOutdoorWC.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWCRSRPDiff.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWCDis.Properties)).BeginInit();
            this.grpOverCover.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDisMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPMin.Properties)).BeginInit();
            this.grpHO.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numHOSecond.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHORSRPDiff.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.groupBox1.Controls.Add(this.grpOutdoorWC);
            this.groupBox1.Controls.Add(this.grpOverCover);
            this.groupBox1.Controls.Add(this.grpHO);
            this.groupBox1.Controls.Add(this.label15);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(408, 326);
            this.groupBox1.TabIndex = 2;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "室分外泄";
            // 
            // grpOutdoorWC
            // 
            this.grpOutdoorWC.Controls.Add(this.numWCRSRPDiff);
            this.grpOutdoorWC.Controls.Add(this.numWCDis);
            this.grpOutdoorWC.Controls.Add(this.label7);
            this.grpOutdoorWC.Controls.Add(this.label9);
            this.grpOutdoorWC.Controls.Add(this.label12);
            this.grpOutdoorWC.Controls.Add(this.label14);
            this.grpOutdoorWC.Location = new System.Drawing.Point(33, 212);
            this.grpOutdoorWC.Name = "grpOutdoorWC";
            this.grpOutdoorWC.Size = new System.Drawing.Size(355, 74);
            this.grpOutdoorWC.TabIndex = 2;
            this.grpOutdoorWC.TabStop = false;
            this.grpOutdoorWC.Text = "室外覆盖不足";
            // 
            // numWCRSRPDiff
            // 
            this.numWCRSRPDiff.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numWCRSRPDiff.Location = new System.Drawing.Point(246, 43);
            this.numWCRSRPDiff.Name = "numWCRSRPDiff";
            this.numWCRSRPDiff.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numWCRSRPDiff.Properties.MaxValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numWCRSRPDiff.Size = new System.Drawing.Size(60, 21);
            this.numWCRSRPDiff.TabIndex = 1;
            // 
            // numWCDis
            // 
            this.numWCDis.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numWCDis.Location = new System.Drawing.Point(126, 14);
            this.numWCDis.Name = "numWCDis";
            this.numWCDis.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numWCDis.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numWCDis.Size = new System.Drawing.Size(60, 21);
            this.numWCDis.TabIndex = 0;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(192, 19);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(29, 12);
            this.label7.TabIndex = 0;
            this.label7.Text = "米，";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(312, 48);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(17, 12);
            this.label9.TabIndex = 0;
            this.label9.Text = "dB";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(7, 19);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(113, 12);
            this.label12.TabIndex = 0;
            this.label12.Text = "主服与采样点距离≥";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(7, 48);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(233, 12);
            this.label14.TabIndex = 0;
            this.label14.Text = "且邻区中有室外站，与主服信号强度差异≤";
            // 
            // grpOverCover
            // 
            this.grpOverCover.Controls.Add(this.label3);
            this.grpOverCover.Controls.Add(this.numDisMin);
            this.grpOverCover.Controls.Add(this.label5);
            this.grpOverCover.Controls.Add(this.label2);
            this.grpOverCover.Controls.Add(this.numRSRPMin);
            this.grpOverCover.Controls.Add(this.label4);
            this.grpOverCover.ForeColor = System.Drawing.SystemColors.ControlText;
            this.grpOverCover.Location = new System.Drawing.Point(33, 52);
            this.grpOverCover.Name = "grpOverCover";
            this.grpOverCover.Size = new System.Drawing.Size(355, 74);
            this.grpOverCover.TabIndex = 0;
            this.grpOverCover.TabStop = false;
            this.grpOverCover.Text = "室内小区覆盖过远";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(6, 21);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(113, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "主服与采样点距离≥";
            // 
            // numDisMin
            // 
            this.numDisMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numDisMin.Location = new System.Drawing.Point(125, 16);
            this.numDisMin.Name = "numDisMin";
            this.numDisMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDisMin.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numDisMin.Size = new System.Drawing.Size(60, 21);
            this.numDisMin.TabIndex = 0;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(42, 48);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(77, 12);
            this.label5.TabIndex = 0;
            this.label5.Text = "且信号强度≥";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(191, 48);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(23, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "dBm";
            // 
            // numRSRPMin
            // 
            this.numRSRPMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numRSRPMin.Location = new System.Drawing.Point(125, 43);
            this.numRSRPMin.Name = "numRSRPMin";
            this.numRSRPMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRSRPMin.Size = new System.Drawing.Size(60, 21);
            this.numRSRPMin.TabIndex = 1;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(191, 21);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(29, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "米，";
            // 
            // grpHO
            // 
            this.grpHO.Controls.Add(this.numHOSecond);
            this.grpHO.Controls.Add(this.numHORSRPDiff);
            this.grpHO.Controls.Add(this.label8);
            this.grpHO.Controls.Add(this.label1);
            this.grpHO.Controls.Add(this.label11);
            this.grpHO.Controls.Add(this.label6);
            this.grpHO.ForeColor = System.Drawing.SystemColors.ControlText;
            this.grpHO.Location = new System.Drawing.Point(33, 132);
            this.grpHO.Name = "grpHO";
            this.grpHO.Size = new System.Drawing.Size(355, 74);
            this.grpHO.TabIndex = 1;
            this.grpHO.TabStop = false;
            this.grpHO.Text = "切换参数不合理";
            // 
            // numHOSecond
            // 
            this.numHOSecond.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numHOSecond.Location = new System.Drawing.Point(245, 43);
            this.numHOSecond.Name = "numHOSecond";
            this.numHOSecond.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numHOSecond.Properties.MaxValue = new decimal(new int[] {
            600,
            0,
            0,
            0});
            this.numHOSecond.Size = new System.Drawing.Size(60, 21);
            this.numHOSecond.TabIndex = 1;
            // 
            // numHORSRPDiff
            // 
            this.numHORSRPDiff.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numHORSRPDiff.Location = new System.Drawing.Point(245, 16);
            this.numHORSRPDiff.Name = "numHORSRPDiff";
            this.numHORSRPDiff.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numHORSRPDiff.Properties.MaxValue = new decimal(new int[] {
            151,
            0,
            0,
            0});
            this.numHORSRPDiff.Size = new System.Drawing.Size(60, 21);
            this.numHORSRPDiff.TabIndex = 0;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(311, 48);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(17, 12);
            this.label8.TabIndex = 0;
            this.label8.Text = "秒";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(311, 21);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(29, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "dB，";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(6, 21);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(233, 12);
            this.label11.TabIndex = 0;
            this.label11.Text = "邻区中有室外站，且比主服小区信号强度≥";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(186, 48);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(53, 12);
            this.label6.TabIndex = 0;
            this.label6.Text = "且时间≥";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(31, 27);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(101, 12);
            this.label15.TabIndex = 0;
            this.label15.Text = "主服小区为室内站";
            // 
            // IndoorLeakCoverPnl
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.groupBox1);
            this.Name = "IndoorLeakCoverPnl";
            this.Size = new System.Drawing.Size(408, 326);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.grpOutdoorWC.ResumeLayout(false);
            this.grpOutdoorWC.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWCRSRPDiff.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWCDis.Properties)).EndInit();
            this.grpOverCover.ResumeLayout(false);
            this.grpOverCover.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDisMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPMin.Properties)).EndInit();
            this.grpHO.ResumeLayout(false);
            this.grpHO.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numHOSecond.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHORSRPDiff.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox grpHO;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.GroupBox grpOverCover;
        private System.Windows.Forms.Label label3;
        private DevExpress.XtraEditors.SpinEdit numDisMin;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private DevExpress.XtraEditors.SpinEdit numRSRPMin;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox grpOutdoorWC;
        private DevExpress.XtraEditors.SpinEdit numWCRSRPDiff;
        private DevExpress.XtraEditors.SpinEdit numWCDis;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label14;
        private DevExpress.XtraEditors.SpinEdit numHOSecond;
        private DevExpress.XtraEditors.SpinEdit numHORSRPDiff;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label9;
    }
}
