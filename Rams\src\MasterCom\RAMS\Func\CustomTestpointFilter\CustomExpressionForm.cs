﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using System.Xml;
using MasterCom.RAMS.Func.CustomTestpointFilter;

namespace MasterCom.RAMS.Func
{
    public partial class CustomExpressionForm : MinCloseForm
    {
        string curLogic;
   
        public CustomExpressionForm()
            :base()
        {
            InitializeComponent();
            Init();
        }

        private void Init()
        {
            LoadSystem();
            LoadMS();

            radioGroupLogic.SelectedIndex = 0;
            curLogic = radioGroupLogic.SelectedIndex == 0 ? "[与]" : "[或]";

            toolTip.SetToolTip(btnDeleteRecord, "删除选择公式");
            toolTip.SetToolTip(btnReset, "重置显示所有采样点");
            toolTip.SetToolTip(btnDeleteGroup, "删除此公式组");
            toolTip.SetToolTip(btnSave, "保存当前公式组");
            toolTip.SetToolTip(btnAddGroup, "新增公式组");

            CheckApplybutton();
        }

        internal void FillStoredExpress()
        {
            try
            {
                XmlConfigFile configfile = new XmlConfigFile(string.Format(Application.StartupPath + "/config/tpCustomExpresssion.xml"));
                List<object> list = (List<object>)configfile.GetItemValue("CustomGroups", "TestpointFilterExpression");
                if (list != null)
                {
                    listBoxControlOld.Items.Clear();
                    foreach (object obj in list)
                    {
                        TestpointFilterExpression testpointFilterExpression = new TestpointFilterExpression();
                        testpointFilterExpression.Param = obj as Dictionary<string, object>;
                        listBoxControlOld.Items.Add(testpointFilterExpression);
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void LoadSystem()
        {
            foreach (DTDisplayParameterSystem system in DTDisplayParameterManager.GetInstance().Systems)
            {
                comboBoxSystem.Properties.Items.Add(system.Name);
            }
            if (comboBoxSystem.Properties.Items.Count > 0
                && comboBoxSystem.SelectedIndex < 0)
            {
                comboBoxSystem.SelectedIndex = 0;
            }
        }

        private void LoadMS()
        {
            for (int i = 1; i < 9; i++)
            {
                comboBoxMS.Properties.Items.Add("MS" + i);
            }
            comboBoxMS.SelectedIndex = 0;
        }

        private enum expressionType { Number,String }

        private void checkInformationExpression()
        {
            expressionType curExpressionType = expressionType.Number;  //当前选择的参数的门限值数据类型
            DTDisplayParameterInfo displayInfo = DTDisplayParameterManager.GetInstance()[(string)comboBoxSystem.SelectedItem][(string)comboBoxInfomation.SelectedItem];
            switch (displayInfo.ParamInfo.ValueType)
            {
                case DTParameterValueType.Byte:
                    curExpressionType = expressionType.Number;
                    break;
                case DTParameterValueType.Double:
                    curExpressionType = expressionType.Number;
                    break;
                case DTParameterValueType.Float:
                    curExpressionType = expressionType.Number;
                    break;
                case DTParameterValueType.Int:
                    curExpressionType = expressionType.Number;
                    break;
                case DTParameterValueType.Long:
                    curExpressionType = expressionType.Number;
                    break;
                case DTParameterValueType.Short:
                    curExpressionType = expressionType.Number;
                    break;
                case DTParameterValueType.String:
                    curExpressionType = expressionType.String;
                    break;
                default:
                    break;
            }

            if (curExpressionType == expressionType.Number)
            {
                cbxInfomationControl.Properties.Items.Clear();
                cbxInfomationControl.EditValue = ">";
                cbxInfomationControl.Properties.Items.Add("=");
                cbxInfomationControl.Properties.Items.Add(">=");
                cbxInfomationControl.Properties.Items.Add(">");
                cbxInfomationControl.Properties.Items.Add("=<");
                cbxInfomationControl.Properties.Items.Add("<");

            }
            else if (curExpressionType == expressionType.String)
            {
                cbxInfomationControl.Properties.Items.Clear();
                cbxInfomationControl.EditValue = "是";
                cbxInfomationControl.Properties.Items.Add("是");
            }

        }

        private void comboBoxSystem_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                loadInformation();
            }
            catch {
                comboBoxSystem.SelectedIndex = 0;
            }
        }

        private void comboBoxInfomation_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                loadArgument();
                checkInformationExpression();
            }
            catch {
                comboBoxInfomation.SelectedIndex = 0;
            }
        }

        private void comboBoxArgument_EditValueChanged(object sender, EventArgs e)
        {
            int p;
            if (!Int32.TryParse(comboBoxArgument.EditValue.ToString(), out p))
            {
                XtraMessageBox.Show("编辑了无效值");
                comboBoxArgument.SelectedIndex = 0;
            }
        }

        private void comboBoxMS_EditValueChanged(object sender, EventArgs e)
        {
            string eStr = comboBoxMS.EditValue.ToString();
            int p;
            if (eStr.Substring(0, 2) != "MS" || !Int32.TryParse(eStr.Substring(2), out p))
            {
                XtraMessageBox.Show("编辑了无效值");
                comboBoxMS.SelectedIndex = 0;
            }
        }

        private void loadInformation()
        {
            comboBoxInfomation.Properties.Items.Clear();
            string systemName = comboBoxSystem.SelectedItem.ToString();
            foreach (DTDisplayParameterInfo displayInfo in DTDisplayParameterManager.GetInstance()[systemName].DisplayParamInfos)
            {
                if ((displayInfo.Type & (int)DTDisplayParameterInfoType.TestPoint) != 0)
                {
                    comboBoxInfomation.Properties.Items.Add(displayInfo.Name);
                }
                else if (displayInfo.Name.IndexOf("Name") != -1)
                {
                    comboBoxInfomation.Properties.Items.Add(displayInfo.Name);
                }
            }
            if (comboBoxInfomation.Properties.Items.Count > 0 && comboBoxInfomation.SelectedIndex < 0)
            {
                comboBoxInfomation.SelectedIndex = 0;
            }
        }

        private void loadArgument()
        {
            comboBoxArgument.Properties.Items.Clear();
            
            DTDisplayParameterInfo displayInfo = DTDisplayParameterManager.GetInstance()[(string)comboBoxSystem.SelectedItem][(string)comboBoxInfomation.SelectedItem];
            if (displayInfo.ArrayBounds > 1)
            {
                for (int i = 0; i < displayInfo.ArrayBounds; i++)
                {
                    comboBoxArgument.Properties.Items.Add(i.ToString());
                }
                if (comboBoxArgument.Properties.Items.Count > 0)
                {
                    chkArgument.Enabled = true;
                    comboBoxArgument.Enabled = true;
                    chkMS.Enabled = true;
                    comboBoxMS.Enabled = true;
                    if (comboBoxArgument.SelectedIndex < 0)
                    {
                        comboBoxArgument.SelectedIndex = 0;
                    }
                }
                else
                {
                    chkArgument.Checked = chkArgument.Enabled = comboBoxArgument.Enabled = false;
                    chkMS.Checked = chkMS.Enabled = comboBoxMS.Enabled = false; 
                }
            }
            else
            {
                chkArgument.Checked = chkArgument.Enabled = comboBoxArgument.Enabled = false;
                chkMS.Checked = chkMS.Enabled = comboBoxMS.Enabled = false; 
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (txtInformationThreshold.Text == "")
            {
                XtraMessageBox.Show("请填写参数名称门限值！", "提示");
                return;
            }
            CExpress.Information.Sign sign;
            switch (cbxInfomationControl.EditValue.ToString())
            {
                case "=": sign = CExpress.Information.Sign.eq;
                    break;
                case ">=": sign = CExpress.Information.Sign.ge;
                    break;
                case ">": sign = CExpress.Information.Sign.gt;
                    break;
                case "=<": sign = CExpress.Information.Sign.le;
                    break;
                case "<": sign = CExpress.Information.Sign.lt;
                    break;
                case "是": sign = CExpress.Information.Sign.be;
                    break;
                case "不是": sign = CExpress.Information.Sign.notbe;
                    break;
                default:
                    XtraMessageBox.Show("表达式无效，请修改！");
                    return;
            }
            if (sign != CExpress.Information.Sign.be && sign != CExpress.Information.Sign.notbe)
            {
                double infoThreshold;
                if (!Double.TryParse(txtInformationThreshold.Text, out infoThreshold))
                {
                    XtraMessageBox.Show("参数名称的门限值不合法，请重写。");
                    return;
                }
            }
            if (comboBoxArgument.Enabled)
            {
                if (comboBoxMS.Enabled)  //有选择类别，名称，序号和端口
                {
                    CExpress.Information info = new CExpress.Information(comboBoxInfomation.EditValue.ToString(), sign, txtInformationThreshold.Text);
                    CExpress ce = new CExpress(curLogic, comboBoxSystem.EditValue.ToString(), info, Int32.Parse(comboBoxArgument.EditValue.ToString()), comboBoxMS.SelectedIndex + 1);
                    listBoxControlResult.Items.Add(ce);
                }
                else  //有选择类别，名称和序号，没有选择端口
                {
                    CExpress.Information info = new CExpress.Information(comboBoxInfomation.EditValue.ToString(), sign, txtInformationThreshold.Text);
                    CExpress ce = new CExpress(curLogic, comboBoxSystem.EditValue.ToString(), info);
                    ce.argument = Int32.Parse(comboBoxArgument.EditValue.ToString());
                    ce.enableMS = false;
                    listBoxControlResult.Items.Add(ce);
                }
            }
            else  //有选择类别和名称，没有选择序号和端口
            {
                CExpress.Information info = new CExpress.Information(comboBoxInfomation.EditValue.ToString(), sign, txtInformationThreshold.Text);
                CExpress ce = new CExpress(curLogic, comboBoxSystem.EditValue.ToString(), info);
                ce.enableArgument = false;
                ce.enableMS = false;
                listBoxControlResult.Items.Add(ce);
            }
        }

        private void CheckApplybutton()
        {
            btnApply.Enabled = listBoxControlResult.Items.Count > 0;
            btnOK.Enabled = listBoxControlResult.Items.Count > 0;
        }


        private void btnClear_Click(object sender, EventArgs e)
        {
            listBoxControlResult.Items.Clear();
        }

        private void listBoxControlResult_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right && listBoxControlResult.SelectedItem != null)
            {
                contextMenuStripResult.Show(listBoxControlResult, e.Location);
            }
        }

        private void miDelete_Click(object sender, EventArgs e)
        {
            if (listBoxControlResult.SelectedItem != null)
            {
                CExpress selCE = listBoxControlResult.SelectedItem as CExpress;
                listBoxControlResult.Items.Remove(selCE);
            }
        }

        ///// <summary>
        ///// 窗口动作结果
        ///// </summary>
        public enum FormAction { Ok, Apply, Cancel };

        private void btnOK_Click(object sender, EventArgs e)
        {
            mModel.CExpressList.Clear();
            foreach (CExpress ce in listBoxControlResult.Items)
            {
                mModel.CExpressList.Add(ce);
            }

            mModel.FormAction = FormAction.Ok;
            this.Close();
        }

        private void chkArgument_CheckedChanged(object sender, EventArgs e)
        {
            if (comboBoxArgument.EditValue!=null)
            {
                comboBoxArgument.Enabled = chkArgument.Checked;
                comboBoxMS.Enabled = chkMS.Enabled = chkArgument.Checked;
            }

        }

        private void chkMS_CheckedChanged(object sender, EventArgs e)
        {
            comboBoxMS.Enabled = chkMS.Checked;
        }

        private void btnDeleteRecord_Click(object sender, EventArgs e)
        {
            if (listBoxControlResult.SelectedItem != null)
            {
                CExpress selCE = listBoxControlResult.SelectedItem as CExpress;
                listBoxControlResult.Items.Remove(selCE);
            }
        }

        private void btnWriteRecord_Click(object sender, EventArgs e)
        {
            if (listBoxControlResult.SelectedItem != null)
            {
                CExpress selCE = listBoxControlResult.SelectedItem as CExpress;
                comboBoxSystem.EditValue = selCE.systemName;
                comboBoxInfomation.EditValue = selCE.information.name;
                comboBoxArgument.EditValue = selCE.argument;
                comboBoxMS.EditValue = "MS" + selCE.MS;
            }
        }

        private void listBoxControlResult_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listBoxControlResult.SelectedItem != null)
            {
                btnDeleteRecord.Enabled = true;
            }
            else
            {
                btnDeleteRecord.Enabled = false;
            }
        }

        private void radioGroupLogic_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (radioGroupLogic.SelectedIndex==0)
                curLogic = "[与]";
            else
                curLogic = "[或]";
            foreach (CExpress ce in listBoxControlResult.Items)
            {
                ce.logic = curLogic;
            }
            listBoxControlResult.Focus();
        }

        private void btnApply_Click(object sender, EventArgs e)
        {
            mModel.CExpressList.Clear();
            foreach (CExpress ce in listBoxControlResult.Items)
            {
                mModel.CExpressList.Add(ce);
            }
            mModel.FormAction = FormAction.Apply;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            mModel.FormAction = FormAction.Cancel;
            this.Close();
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            if (MainModel.TempPointList.Count > 0)
            {
                foreach (TestPoint tp in MainModel.TempPointList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }

            if (MainModel.MainForm.GetMapForm() != null)
            {
                MapDTLayer dtLayer = MainModel.MainForm.GetMapForm().GetDTLayer();
                dtLayer.Invalidate();
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (listBoxControlOld.SelectedItem != null)
                {
                    TestpointFilterExpression testpointFilterExpression = listBoxControlOld.SelectedItem as TestpointFilterExpression;
                    testpointFilterExpression.cexpresses.Clear();
                    foreach (CExpress ce in listBoxControlResult.Items)
                    {
                        testpointFilterExpression.cexpresses.Add(ce);
                    }

                    XmlConfigFile configFile = new XmlConfigFile();
                    XmlElement cfg = configFile.AddConfig("CustomGroups");
                    List<object> groupParam = new List<object>();
                    foreach (TestpointFilterExpression tfe in listBoxControlOld.Items)
                    {
                        groupParam.Add(tfe.Param);
                    }
                    configFile.AddItem(cfg, "TestpointFilterExpression", groupParam);

                    configFile.Save(string.Format(Application.StartupPath + "/config/tpCustomExpresssion.xml"));
                }
                else
                {
                    SetGroupNameDlg nameDlg = new SetGroupNameDlg();
                    if (nameDlg.ShowDialog() == DialogResult.OK)
                    {
                        TestpointFilterExpression testpointFilterExpression = new TestpointFilterExpression(nameDlg.GroupName);
                        foreach (CExpress ce in listBoxControlResult.Items)
                        {
                            testpointFilterExpression.cexpresses.Add(ce);
                        }

                        XmlConfigFile configFile = new XmlConfigFile();
                        XmlElement cfg = configFile.AddConfig("CustomGroups");
                        List<object> groupParam = new List<object>();

                        foreach (TestpointFilterExpression tfe in listBoxControlOld.Items)
                        {
                            groupParam.Add(tfe.Param);
                        }
                        groupParam.Add(testpointFilterExpression.Param);
                        configFile.AddItem(cfg, "TestpointFilterExpression", groupParam);

                        configFile.Save(string.Format(Application.StartupPath + "/config/tpCustomExpresssion.xml"));
                    }
                }

                FillStoredExpress();
            }
            catch
            {
                XtraMessageBox.Show("保存公式组失败! ");

            }
        }

        private void listBoxControlOld_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listBoxControlOld.SelectedItem!=null)
            {
                listBoxControlResult.Items.Clear();
                foreach (CExpress ce in ((TestpointFilterExpression)listBoxControlOld.SelectedItem).cexpresses)
                {
                    listBoxControlResult.Items.Add(ce);
                }
            }
            radioGroupLogic_SelectedIndexChanged(null, null);

            CheckApplybutton();
        }

        private void btnDeleteGroup_Click(object sender, EventArgs e)
        {
            if (XtraMessageBox.Show("是否删除公式组-"+((TestpointFilterExpression)listBoxControlOld.SelectedItem).groupName+"?","注意",MessageBoxButtons.OKCancel)==DialogResult.OK)
            {
                listBoxControlOld.Items.Remove(listBoxControlOld.SelectedItem);
            }

            
        }

        private void btnAddGroup_Click(object sender, EventArgs e)
        {
            try
            {
                SetGroupNameDlg nameDlg = new SetGroupNameDlg();
                if (nameDlg.ShowDialog() == DialogResult.OK)
                {
                    TestpointFilterExpression testpointFilterExpression = new TestpointFilterExpression(nameDlg.GroupName);
                    foreach (CExpress ce in listBoxControlResult.Items)
                    {
                        testpointFilterExpression.cexpresses.Add(ce);
                    }

                    XmlConfigFile configFile = new XmlConfigFile();
                    XmlElement cfg = configFile.AddConfig("CustomGroups");
                    List<object> groupParam = new List<object>();

                    foreach (TestpointFilterExpression tfe in listBoxControlOld.Items)
                    {
                        groupParam.Add(tfe.Param);
                    }
                    groupParam.Add(testpointFilterExpression.Param);
                    configFile.AddItem(cfg, "TestpointFilterExpression", groupParam);

                    configFile.Save(string.Format(Application.StartupPath + "/config/tpCustomExpresssion.xml"));

                    XtraMessageBox.Show("新增公式组成功!");
                }
                FillStoredExpress();
            }
            catch
            {
                XtraMessageBox.Show("新增公式组失败! ");
            }
        }


    }

    public class CExpress
    {
        public string logic { get; set; }
        public string systemName { get; set; }
        public Information information { get; set; }
        public int argument { get; set; }
        public bool enableArgument { get; set; } = true;
        public int MS { get; set; } = -1;
        public bool enableMS { get; set; } = true;
        public override string ToString()
        {
            string signDesc = getSignDesc();
            string res = "";

            if (information.sign != Information.Sign.range)
            {
                res = logic + "，类别:" + systemName + "，名称:" + information.name + signDesc + information.threshold;
            }
            else
            {
                res = logic + "，类别:" + systemName + "，名称:" + information.headThreshold + "<" + information.name + "<" + information.tailThreshold;
            }

            if (enableArgument)
            {
                res += "，序号:" + argument;
                if (enableMS)
                {
                    res += "，端口号:MS" + MS;
                }
            }
            return res;
        }

        private string getSignDesc()
        {
            string signDesc = null;
            switch (information.sign)
            {
                case Information.Sign.eq:
                    signDesc = "=";
                    break;
                case Information.Sign.ge:
                    signDesc = ">=";
                    break;
                case Information.Sign.gt:
                    signDesc = ">";
                    break;
                case Information.Sign.le:
                    signDesc = "=<";
                    break;
                case Information.Sign.lt:
                    signDesc = "<";
                    break;
                case Information.Sign.be:
                    signDesc = "是";
                    break;
                case Information.Sign.notbe:
                    signDesc = "不是";
                    break;
            }

            return signDesc;
        }

        public CExpress()
        {
        }

        public CExpress(string logic, string systemName, Information information, int argument, int ms)
        {
            this.logic = logic;
            this.systemName = systemName;
            this.information = information;
            this.argument = argument;
            this.MS = ms;
        }

        public CExpress(string logic, string systemName, Information information)
        {
            this.logic = logic;
            this.systemName = systemName;
            this.information = information;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["systemName"] = systemName;
                param["informationName"] = information.name;
                param["informationSign"] = information.sign.ToString();
                param["informationThreshold"] = information.threshold;
                if (enableArgument)
                {
                    param["argument"] = argument;
                }
                if (enableMS)
                {
                    param["ms"] = MS;
                }
                return param;
            }
            set
            {
                systemName = (string)value["systemName"];
                information = new Information();
                information.name = (string)value["informationName"];
                switch ((string)value["informationSign"])
                {
                    case "lt":
                        information.sign=Information.Sign.lt;
                        break;
                    case "le":
                        information.sign = Information.Sign.le;
                        break;
                    case "eq":
                        information.sign = Information.Sign.eq;
                        break;
                    case "gt":
                        information.sign = Information.Sign.gt;
                        break;
                    case "ge":
                        information.sign = Information.Sign.ge;
                        break;
                    case "be":
                        information.sign = Information.Sign.be;
                        break;
                    case "notbe":
                        information.sign = Information.Sign.notbe;
                        break;
                    default:
                        break;
                }
                information.threshold = (string)value["informationThreshold"];
                if (value.ContainsKey("argument"))
                {
                    argument = (int)value["argument"];
                }
                else
                {
                    enableArgument = false;
                }
                if (value.ContainsKey("ms"))
                {
                    MS = (int)value["ms"];
                }
                else
                {
                    enableMS = false;
                }
            }
        }

        public class Information
        {
            public string name { get; set; }
            public Sign sign { get; set; }
            public string threshold { get; set; }
            public double headThreshold { get; set; }
            public double tailThreshold { get; set; }
            public List<string> notbeThresholds { get; set; }
            public Information()
            { 
            }

            public Information(string name, Sign sign, string threshold)
            {
                this.name = name;
                this.sign = sign;
                double th;
                if (Double.TryParse(threshold, out th))
                    this.threshold = th.ToString();
                else
                    this.threshold = threshold;
            }

            /// <summary>
            /// 数字类的计算符号
            /// ( lt：小于
            /// le：小于等于
            /// eq：等于
            /// ge：大于等于
            /// gt：大于
            /// range: 范围）
            /// 
            /// 文本类的判断符号
            /// （be：是
            /// notbe:不是)
            /// </summary>
            public enum Sign { lt, le, eq, ge, gt, range, be, notbe };
        }
    }

    public class CExpressRet_and 
    {
        public List<childCExpress> childCExpressList { get; set; } = new List<childCExpress>();

        public class childCExpress:CExpress
        {
            public void fillFromCE(CExpress ce)
            {
                systemName = ce.systemName;
                information = new Information(ce.information.name, ce.information.sign, ce.information.threshold);
                if (ce.information.sign==CExpress.Information.Sign.notbe)
                {
                    information.notbeThresholds = new List<string>();
                    information.notbeThresholds.Add(ce.information.threshold);
                }
                argument = ce.argument;
                enableArgument = ce.enableArgument;
                MS = ce.MS;
                enableMS = ce.enableMS;
            }
        }

    }
}
