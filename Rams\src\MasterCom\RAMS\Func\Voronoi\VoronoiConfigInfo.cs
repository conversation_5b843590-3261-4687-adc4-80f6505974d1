﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Func
{
    /// <summary>
    /// 泰森多边形的地市设置信息
    /// </summary>
    public class VoronoiConfigInfo
    {
        public string DistrictName { get; set; }

        /// <summary>
        /// 边界进行模糊处理后保留的点数（约等于）
        /// </summary>
        public int BorderReserveCount { get; set; }

        /// <summary>
        /// 当构建泰森多边形的点数少于该变量值的时候使用边界精确切割
        /// </summary>
        public int ClipAllLessCount { get; set; }

        /// <summary>
        /// 允许组合到泰森多边形中的三角形的最大角度
        /// </summary>
        public double MaxAngle { get; set; }

        /// <summary>
        /// 默认切割边界图层文件路径
        /// </summary>
        public string DefaultBorderPath { get; set; }

        public VoronoiConfigInfo()
        {
            DistrictName = "";
            BorderReserveCount = 1000;
            ClipAllLessCount = 1000;
            MaxAngle = 179;
            DefaultBorderPath = "";
        }
    }
}
