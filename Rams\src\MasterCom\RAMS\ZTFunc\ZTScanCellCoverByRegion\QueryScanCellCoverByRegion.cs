﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryScanCellCoverByRegion : DIYSampleByRegion
    {
        private Dictionary<string, ScanCellCoverInfo_GSM> nameCellCoverInfoDic { get; set; }
        protected bool bExpand = false;

        public QueryScanCellCoverByRegion(MainModel mainModel)
            :base(mainModel)
        {
            this.mainModel = mainModel;
            isAddSampleToDTDataManager = false;
            nameCellCoverInfoDic = new Dictionary<string,ScanCellCoverInfo_GSM>();
        }

        public override string IconName
        {
            get { return ""; }
        }

        public override string Name
        {
            get { return "小区覆盖(按区域)"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 15000, 15039, this.Name);//////
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup cellSetGroup = new DIYSampleGroup();
            cellSetGroup.ThemeName = "GSM_SCAN_RxLev";

            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("GSCAN_BCCH");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }

            parameter = DTParameterManager.GetInstance().GetParameter("GSCAN_BSIC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("GSCAN_RxLev");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("GSCAN_C/I");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("GSCAN_RxQual");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }

            return cellSetGroup;
        }

        private void clear()
        {
            bExpand = false;
            nameCellCoverInfoDic.Clear();
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (!bExpand)
            {
                regionRectExpand(500);
                bExpand = true;
            }
            return base.isValidTestPoint(tp);
        }

        protected virtual void regionRectExpand(int distance)
        {
            MasterCom.MTGis.DbRect rect = new MasterCom.MTGis.DbRect();
            rect.x1 = condition.Geometorys.Region.Extents.xMin - distance * CD.ATOM_SPAN_LONG;
            rect.y1 = condition.Geometorys.Region.Extents.yMax + distance * CD.ATOM_SPAN_LAT;
            rect.x2 = condition.Geometorys.Region.Extents.xMax + distance * CD.ATOM_SPAN_LONG;
            rect.y2 = condition.Geometorys.Region.Extents.yMin - distance * CD.ATOM_SPAN_LAT;
            MapWinGIS.Shape shpRect = MasterCom.MTGis.ShapeHelper.CreateRectShape(rect.x1, rect.y1, rect.x2, rect.y2);
            condition.Geometorys.Region = shpRect;
        }

        protected override bool getConditionBeforeQuery()
        {
            clear();
            if (!bExpand)
            {
                regionRectExpand(500);
                bExpand = true;
            }
            return true;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            dealTestPoint(tp);
        }

        protected override void FireShowFormAfterQuery()
        {
            if (bExpand)
            {
                regionRectExpand(-500);
                bExpand = false;
            }
            fireShowForm();
            fireSerialInfo();
            refreshLayer();
        }

        private void refreshLayer()
        {
            MapForm mf = mainModel.MainForm.GetMapForm();
            if (mf == null)
            {
                return;
            }
            mainModel.MainForm.RefreshLegend();
            MasterCom.MTGis.CustomDrawLayer cLayer = mf.GetCustomLayer(typeof(ScanCellCoverLayer));
            if (cLayer == null)
            {
                cLayer = new ScanCellCoverLayer(mf.GetMapOperation(), "扫频小区覆盖图层");
                mf.AddTempCustomLayer(cLayer);
            }
            cLayer.Invalidate();
        }

        protected virtual void fireShowForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ScanCellCoverInfoForm).FullName);
            ScanCellCoverInfoForm scanCellCoverForm = obj == null ? null : obj as ScanCellCoverInfoForm;
            if (scanCellCoverForm == null || scanCellCoverForm.IsDisposed)
            {
                scanCellCoverForm = new ScanCellCoverInfoForm(MainModel);
            }
            scanCellCoverForm.FillData(new List<ScanCellCoverInfo_GSM>(nameCellCoverInfoDic.Values));
            if (!scanCellCoverForm.Visible)
            {
                scanCellCoverForm.Show(MainModel.MainForm);
            }
        }

        protected virtual void fireSerialInfo()
        {
            string serialInfoName = "GSM_SCAN_RxLev";
            foreach (MasterCom.RAMS.Func.MapSerialInfo serialInfo in mainModel.MainForm.GetMapForm().GetDTLayer().SerialInfos)
            {
                if (serialInfo.Name.Equals(serialInfoName))
                {
                    mainModel.MainForm.GetMapForm().GetDTLayer().CurFlyLinesSerialInfo = serialInfo;
                }
            }
        }

        private void dealTestPoint(TestPoint tp)
        {
            if (tp is ScanTestPoint_G)
            {
                try
                {
                    if (bExpand)
                    {
                        regionRectExpand(-500);
                        bExpand = false;
                    }
                    for (int idx = 0; idx < 50; ++idx)
                    {
                        Cell cell = tp.GetCell_GSMScan(idx);
                        if (cell == null ||
                            !condition.Geometorys.GeoOp.Contains(cell.Longitude, cell.Latitude))
                        {
                            continue;
                        }
                        ScanCellCoverInfo_GSM cellCoverInfo;
                        if (!nameCellCoverInfoDic.TryGetValue(cell.Name, out cellCoverInfo))
                        {
                            cellCoverInfo = new ScanCellCoverInfo_GSM(cell);
                            nameCellCoverInfoDic.Add(cell.Name, cellCoverInfo);
                        }
                        cellCoverInfo.TpInfoList.Add(new ScanCellCoverPointInfo_GSM(cell, tp as ScanTestPoint_G));
                        cellCoverInfo.DealParam(tp as ScanTestPoint_G);
                    }
                }
                catch
                {
                    //continue
                }
            }
        }
    }

    public class ScanCellCoverInfo
    {
        public double Longitude { get; set; }
        public double Latitude { get; set; }
    }

    public class ScanCellCoverInfo_GSM : ScanCellCoverInfo
    {
        public Cell CoverCell { get; set; }
        public string CellName { get; set; }
        public int BCCH { get; set; }
        public int BSIC { get; set; }

        private float rxlevMax;
        private float rxlevMin;
        private float rxlevMean;
        private int rxlevSampleNum;
        public string RxlevMax
        {
            get
            {
                if (rxlevSampleNum > 0)
                {
                    return rxlevMax.ToString();
                }
                return "";
            }
        }
        public string RxlevMin
        {
            get
            {
                if (rxlevSampleNum > 0)
                {
                    return rxlevMin.ToString();
                }
                return "";
            }
        }
        public string RxlevMean
        {
            get
            {
                if (rxlevSampleNum > 0)
                {
                    return Math.Round(rxlevMean / rxlevSampleNum, 2).ToString();
                }
                return "";
            }
        }
        
        private int c2iMax;
        private int c2iMin;
        private float c2iMean;
        private int c2iSampleNum;
        public string C2IMax
        {
            get
            {
                if (c2iSampleNum > 0)
                {
                    return c2iMax.ToString();
                }
                return "";
            }
        }
        public string C2IMin
        {
            get
            {
                if (c2iSampleNum > 0)
                {
                    return c2iMin.ToString();
                }
                return "";
            }
        }
        public string C2IMean
        {
            get
            {
                if (c2iSampleNum > 0)
                {
                    return Math.Round(c2iMean / c2iSampleNum, 2).ToString();
                }
                return "";
            }
        }

        private int rxQualMax;
        private int rxQualMin;
        private float rxQualMean;
        private int rxQualSampleNum;
        public string RxQualMax
        {
            get
            {
                if (rxQualSampleNum > 0)
                {
                    return rxQualMax.ToString();
                }
                return "";
            }
        }
        public string RxQualMin
        {
            get
            {
                if (rxQualSampleNum > 0)
                {
                    return rxQualMin.ToString();
                }
                return "";
            }
        }
        public string RxQualMean
        {
            get
            {
                if (rxQualSampleNum > 0)
                {
                    return Math.Round(rxQualMean / rxQualSampleNum, 2).ToString();
                }
                return "";
            }
        }

        public int TPCount { get { return TpInfoList.Count; } }
        public List<ScanCellCoverPointInfo_GSM> TpInfoList { get; set; }

        public ScanCellCoverInfo_GSM(Cell cell)
        {
            this.CoverCell = cell;
            this.CellName = CoverCell.Name;
            this.BCCH = CoverCell.BCCH;
            this.BSIC = CoverCell.BSIC;
            this.Longitude = CoverCell.Longitude;
            this.Latitude = CoverCell.Latitude;
            initParam();
            TpInfoList = new List<ScanCellCoverPointInfo_GSM>();
        }

        private void initParam()
        {
            rxlevMax = -10000000;
            rxlevMin = 10000000;
            rxlevMean = 0;
            rxlevSampleNum = 0;
            
            c2iMax = -10000000;
            c2iMin = 10000000;
            c2iMean = 0;
            c2iSampleNum = 0;

            rxQualMax = -10000000;
            rxQualMin = 10000000;
            rxQualMean = 0;
            rxQualSampleNum = 0;
        }

        public void DealParam(ScanTestPoint_G tp)
        {
            float rxlev = (float?)tp["GSCAN_RxLev", 0] == null ? -10000000 : (float)(float?)tp["GSCAN_RxLev", 0];
            int c2i = (int?)tp["GSCAN_C/I", 0] == null ? -10000000 : (int)(int?)tp["GSCAN_C/I", 0];
            int rxQual = (int?)tp["GSCAN_RxQual", 0] == null ? -10000000 : (int)(int?)tp["GSCAN_RxQual", 0];

            if (rxlev >= -140 && rxlev <= -10)
            {
                this.rxlevMax = this.rxlevMax > rxlev ? this.rxlevMax : rxlev;
                this.rxlevMin = this.rxlevMin < rxlev ? this.rxlevMin : rxlev;
                rxlevMax = (float)getValidData(rxlevMax, rxlev, true);
                rxlevMin = (float)getValidData(rxlevMin, rxlev, false);
                this.rxlevMean += rxlev;
                rxlevSampleNum++;
            }
            if (c2i >= -35 && c2i <= 35)
            {
                c2iMax = (int)getValidData(c2iMax, c2i, true);
                c2iMin = (int)getValidData(c2iMin, rxlev, false);
                this.c2iMean += c2i;
                c2iSampleNum++;
            }
            if (rxQual >= 0 && rxQual <= 7)
            {
                rxQualMax = (int)getValidData(rxQualMax, rxQual, true);
                rxQualMin = (int)getValidData(rxQualMin, rxlev, false);
                this.rxQualMean += rxQual;
                rxQualSampleNum++;
            }
        }

        private double getValidData(double value1, double value2, bool moreThan)
        {
            if ((moreThan && value1 > value2) || (!moreThan && value1 < value2))
            {
                return value1;
            }
            else
            {
                return value2;
            }
        }
    }

    public class ScanCellCoverPointInfo_GSM
    {
        public TestPoint TP { get; set; }
        public double Longitude { get { return TP.Longitude; } }
        public double Latitude { get { return TP.Latitude; } }

        public Cell CoverCell { get; set; }
        public double Distance
        {
            get
            {
                return Math.Round(MasterCom.Util.MathFuncs.GetDistance(CoverCell.Longitude, CoverCell.Latitude, TP.Longitude, TP.Latitude), 2);
            }
        }
        private float rxlev;
        public string Rxlev
        {
            get
            {
                if (rxlev >= -140 && rxlev <= -10)
                {
                    return Math.Round(rxlev, 2).ToString();
                }
                return "";
            }
        }

        private int c2i;
        public string C2I 
        {
            get
            {
                if (c2i >= -35 && c2i <= 35)
                {
                    return c2i.ToString();
                }
                return "";
            }
        }

        private int rxQual;
        public string RxQual
        {
            get
            {
                if (rxQual >= 0 && rxQual <= 7)
                {
                    return rxQual.ToString();
                }
                return "";
            }
        }

        public ScanCellCoverPointInfo_GSM(Cell cell, ScanTestPoint_G tp)
        {
            this.CoverCell = cell;
            this.TP = tp;
            fill(tp);
        }

        private void fill(ScanTestPoint_G tp)
        {
            this.rxlev = (float?)tp["GSCAN_RxLev", 0] == null ? -10000000 : (float)(float?)tp["GSCAN_RxLev", 0];
            this.c2i = (int?)tp["GSCAN_C/I", 0] == null ? -10000000 : (int)(int?)tp["GSCAN_C/I", 0];
            this.rxQual = (int?)tp["GSCAN_RxQual", 0] == null ? -10000000 : (int)(int?)tp["GSCAN_RxQual", 0];
        }
    }
}
