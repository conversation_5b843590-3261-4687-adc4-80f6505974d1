﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public abstract class AreaKpiQueryBase : QueryBase
    {
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        protected bool isQueryAllParams = false;

        protected bool isQueryEvents = true;

        protected bool isStatLatestOnly = false;

        protected ArchiveCondition archiveCondition { get; set; }

        protected Dictionary<AreaBase, List<AreaBase>> rootLeafDic { get; set; }

        protected Dictionary<AreaBase, CAreaSummary> areaSummaryMap { get; set; }

        protected AnaDealerBase anaDealer;

        protected AreaTestCondition areaCondition;

        protected AreaKpiQueryBase(MainModel mainModel)
            : base(mainModel)
        {
            areaSummaryMap = new Dictionary<AreaBase, CAreaSummary>();
            anaDealer = new NoneAnaDealer(null);
        }

        public override string IconName
        {
            get { return null; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 1300, 1301, this.Name);
        }

        protected override void query()
        {
            archiveCondition = ArchiveSettingManager.GetInstance().Condition;
            rootLeafDic = archiveCondition.VillageCondition.RootLeafDic;

            if (rootLeafDic.Count == 0)
            {
                MessageBox.Show("尚未选择村庄，请设置基础配置....", "提醒");
                return;
            }
            if (!setConditionDlg())
            {
                return;
            }
            searchData();
            fireShowForm();
        }

        protected virtual void searchData()
        {
            try
            {
                areaSummaryMap.Clear();

                AreaKpiSearchPartByPart search = new AreaKpiSearchPartByPart();
                search.SetCondition(archiveCondition);
                search.SetFormula(getFormulas());
                search.SetQueryAllParams(isQueryAllParams);
                search.SetQueryEvents(isQueryEvents);
                search.SetStatLatestOnly(isStatLatestOnly);
                search.SetTypes(getTypeIds());
                search.SetDealer(anaDealer, areaCondition);
                search.Query();

                areaSummaryMap = search.AreaKpiMap;
            }
            catch
            {
                //continue
            }
            finally
            {
                rootLeafDic.Clear();
            }
        }

        protected Dictionary<int, Dictionary<int, AreaBase>> getTypeIds()
        {
            Dictionary<int, Dictionary<int, AreaBase>> typeIds = new Dictionary<int, Dictionary<int, AreaBase>>();
            foreach (AreaBase root in rootLeafDic.Keys)
            {
                Dictionary<int, AreaBase> ids = new Dictionary<int, AreaBase>();
                foreach (AreaBase area in rootLeafDic[root])
                {
                    ids[area.AreaID] = area;
                }
                typeIds[root.AreaTypeID] = ids;
            }
            return typeIds;
        }

        protected virtual List<string> getFormulas() { return new List<string>(); }

        protected abstract bool setConditionDlg();

        protected abstract void fireShowForm();

        protected override bool isValidCondition()
        {
            return true;
        }
    }

    public class CAreaSummary
    {
        public AreaBase Area { get; set; }

        public AreaKPIDataGroup<AreaBase> AreaKpiGroup { get; set; }

        public object Permeate { get; set; }

        public List<CAreaSummary> VillageVec { get; set; }

        public Dictionary<AreaBase, CAreaSummary> VillageDic { get; set; }

        public CAreaSummary(AreaBase area)
        {
            this.Area = area;
            AreaKpiGroup = new AreaKPIDataGroup<AreaBase>(area);
            VillageVec = new List<CAreaSummary>();
            VillageDic = new Dictionary<AreaBase, CAreaSummary>();
        }

        public void Merge(AreaKPIDataGroup<AreaBase> group)
        {
            this.AreaKpiGroup.Merge(group);
        }

        public void AddVillage(CAreaSummary village)
        {
            VillageVec.Add(village);
            VillageDic[village.Area] = village;
        }

        private Dictionary<ECarrier, List<FileInfo>> groupFiles()
        {
            Dictionary<ECarrier, List<FileInfo>> serCarFilesMap = new Dictionary<ECarrier, List<FileInfo>>();
            foreach (FileInfo file in AreaKpiGroup.FileIDDic.Values)
            {
                ECarrier car = (ECarrier)file.CarrierType;
                List<FileInfo> files;
                if (!serCarFilesMap.TryGetValue(car, out files))
                {
                    files = new List<FileInfo>();
                    serCarFilesMap[car] = files;
                }
                files.Add(file);
            }
            return serCarFilesMap;
        }

        public void CalcIntegrity(IntegrityTestCondition condition)
        {
            Dictionary<ECarrier, CPermeate> servCarPerMap = new Dictionary<ECarrier, CPermeate>();
            Dictionary<ECarrier, List<FileInfo>> serCarFilesMap = groupFiles();

            foreach (ECarrier serCar in Enum.GetValues(typeof(ECarrier)))
            {
                CPermeate per;
                if (!servCarPerMap.TryGetValue(serCar, out per))
                {
                    per = new CPermeate(serCar.ToString());
                    servCarPerMap[serCar] = per;
                }
                List<FileInfo> files;
                if (serCarFilesMap.TryGetValue(serCar, out files))
                {
                    per.ITotalCnt = files.Count;
                    if (files.Count >= condition.TestTimes)
                    {
                        per.IValidCnt = 1;
                    }
                }
            }
            Permeate = servCarPerMap;
        }

        public void CalcTestAlarm(AlarmTestCondition condition)
        {
            Dictionary<ECarrier, CPermeate> servCarPerMap = new Dictionary<ECarrier, CPermeate>();
            Dictionary<ECarrier, List<FileInfo>> serCarFilesMap = groupFiles();

            foreach (ECarrier serCar in Enum.GetValues(typeof(ECarrier)))
            {
                CPermeate per;
                if (!servCarPerMap.TryGetValue(serCar, out per))
                {
                    per = new CPermeate(serCar.ToString(), "预警", "未预警");
                    servCarPerMap[serCar] = per;
                }
                List<FileInfo> files;
                if (serCarFilesMap.TryGetValue(serCar, out files))
                {
                    per.ITotalCnt = files.Count;
                    foreach (FileInfo file in files)
                    {
                        per.AddDays(file.BeginTime);
                    }
                    string formula = AreaTestCondition.GetFormulaCarrier(serCar);
                    double dNum = AreaKpiGroup.CalcFormula((CarrierType)serCar, 0, formula);
                    if (!double.IsNaN(dNum))
                        per.ISampleNum = Convert.ToInt64(dNum);
                }

                if (per.TestDays.Count <= condition.TestDays &&
                    per.ISampleNum <= condition.SampleNum)
                {
                    per.IValidCnt = 1;
                }
            }
            Permeate = servCarPerMap;
        }

        public void CalcTestWork(WorkTestCondition condition)
        {
            Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>> timeServCarPerMap = new Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>>();
            Dictionary<ECarrier, List<FileInfo>> serCarFilesMap = groupFiles();

            foreach (TimePeriod tPeriod in condition.TimeVec)
            {
                foreach (ECarrier serCar in Enum.GetValues(typeof(ECarrier)))
                {
                    Dictionary<ECarrier, CPermeate> serCarMap;
                    if (!timeServCarPerMap.TryGetValue(tPeriod, out serCarMap))
                    {
                        serCarMap = new Dictionary<ECarrier, CPermeate>();
                        timeServCarPerMap[tPeriod] = serCarMap;
                    }
                    CPermeate per;
                    if (!serCarMap.TryGetValue(serCar, out per))
                    {
                        per = new CPermeate(serCar.ToString(), "有测", "未测");
                        serCarMap[serCar] = per;
                    }
                }
            }

            foreach (ECarrier serCar in serCarFilesMap.Keys)
            {
                foreach (FileInfo file in serCarFilesMap[serCar])
                {
                    setTimeInfo(condition, timeServCarPerMap, serCar, file);
                }
            }
            Permeate = timeServCarPerMap;
        }

        private void setTimeInfo(WorkTestCondition condition, Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>> timeServCarPerMap, ECarrier serCar, FileInfo file)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(file.BeginTime * 1000L);
            foreach (TimePeriod tPeriod in condition.TimeVec)
            {
                if (tPeriod.Contains(dt))
                {
                    timeServCarPerMap[tPeriod][serCar].ITotalCnt++;
                    timeServCarPerMap[tPeriod][serCar].IValidCnt = 1;
                }
            }
        }
    }

    public class CPermeate
    {
        public string StrName { get; set; }
        public Int64 IValidCnt { get; set; }
        public Int64 ITotalCnt { get; set; }
        public List<int> TestDays { get; set; }
        public Int64 ISampleNum { get; set; }
        public double DPermeate
        {
            get
            {
                if (ITotalCnt > 0)
                {
                    return Math.Round(100.0 * IValidCnt / ITotalCnt, 2);
                }
                return 0;
            }
        }
        public bool BAchieve { get; set; }

        private string strAchieveDesc { get; set; }
        private string strUnAchieveDesc { get; set; }
        public string StrDesc
        {
            get
            {
                if (BAchieve)
                    return strAchieveDesc;
                return strUnAchieveDesc;
            }
        }

        public CPermeate(string name)
        {
            this.StrName = name;
            IValidCnt = 0;
            ITotalCnt = 0;
            TestDays = new List<int>();
            ISampleNum = 0;
            this.BAchieve = false;

            this.strAchieveDesc = "达标";
            this.strUnAchieveDesc = "未达标";
        }

        public CPermeate(string name, string strAchieveDesc, string strUnAchieveDesc)
            : this(name)
        {
            this.strAchieveDesc = strAchieveDesc;
            this.strUnAchieveDesc = strUnAchieveDesc;
        }

        public void AddDays(int day)
        {
            if (!TestDays.Contains(day))
            {
                TestDays.Add(day);
            }
        }
    }
}
