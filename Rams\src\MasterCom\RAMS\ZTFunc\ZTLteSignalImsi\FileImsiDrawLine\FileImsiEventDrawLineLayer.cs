﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Drawing2D;

using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using GeneGraph;

namespace MasterCom.RAMS.ZTFunc.LteSignalImsi
{
    class FileImsiEventDrawLineLayer
    {
        public static FileImsiEventDrawLineLayer Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new FileImsiEventDrawLineLayer();
                }
                return instance;
            }
        }

        public bool IsGisIntervalLimit
        {
            get;
            set;
        }

        public int GisIntervalMinutes
        {
            get;
            set;
        }

        private FileImsiEventDrawLineLayer()
        {
            MainModel.GetInstance().DTDataChanged += MainModel_DTDataChanged;
        }

        private void DrawHandler(Rectangle clientRect, Rectangle updateRect, Graphics graphics, MapOperation mop)
        {
            AdjustableArrowCap lineCap = new AdjustableArrowCap(4, 4);
            lineCap.Filled = false;
            Pen linePen = new Pen(Color.Blue, 2.5f);
            linePen.CustomEndCap = lineCap;

            foreach (FileImsiDrawItem drawItem in drawList)
            {
                int lineCnt = 0;
                List<Event> evtList = drawItem.Events;
                for (int i = 0; i < evtList.Count - 1; ++i)
                {
                    Event sEvt = evtList[i];
                    Event eEvt = evtList[i + 1];
                    if (sEvt.Longitude == eEvt.Longitude && sEvt.Latitude == eEvt.Latitude)
                    {
                        continue;
                    }
                    if (IsGisIntervalLimit && (eEvt.DateTime - sEvt.DateTime).TotalMinutes > GisIntervalMinutes)
                    {
                        lineCnt = 0;
                        continue;
                    }

                    PointF sp, ep;
                    DbPoint startPoint = new DbPoint(sEvt.Longitude, sEvt.Latitude);
                    DbPoint endPoint = new DbPoint(eEvt.Longitude, eEvt.Latitude);
                    mop.ToDisplay(startPoint, out sp);
                    mop.ToDisplay(endPoint, out ep);

                    graphics.DrawLine(linePen, sp, ep);

                    ++lineCnt;
                    LabelElement label = new LabelElement(new PointF((sp.X + ep.X) / 2, (sp.Y + ep.Y) / 2), lineCnt.ToString());
                    label.Draw(graphics);
                }
            }
        }

        private void MainModel_DTDataChanged(object sender, EventArgs e)
        {
            isNeedDraw = sender.GetType() == typeof(FileImsiEventQueryByRegion);
            if (!isNeedDraw)
            {
                drawList = null;
                TempLayer.Instance.Clear();
                return;
            }

            drawList = new List<FileImsiDrawItem>();
            foreach (DTFileDataManager fileManager in MainModel.GetInstance().DTDataManager.FileDataManagers)
            {
                Dictionary<long, List<Event>> imsiEventDic = addImsiEventDic(fileManager);

                foreach (KeyValuePair<long, List<Event>> kvp in imsiEventDic)
                {
                    FileImsiDrawItem drawItem = new FileImsiDrawItem(fileManager.FileID, kvp.Key);
                    drawItem.Events.AddRange(kvp.Value);
                    drawList.Add(drawItem);
                }
            }
            TempLayer.Instance.Draw(DrawHandler, true);
        }

        private static Dictionary<long, List<Event>> addImsiEventDic(DTFileDataManager fileManager)
        {
            Dictionary<long, List<Event>> imsiEventDic = new Dictionary<long, List<Event>>();
            foreach (Event evt in fileManager.Events)
            {
                if (evt.Longitude < 1 || evt.Latitude < 1)
                {
                    continue;
                }

                object imsiObj = evt["Value3"];
                if (imsiObj == null)
                {
                    continue;
                }

                long imsi = (long)imsiObj;
                if (!imsiEventDic.ContainsKey(imsi))
                {
                    imsiEventDic[imsi] = new List<Event>();
                }
                imsiEventDic[imsi].Add(evt);
            }

            return imsiEventDic;
        }

        private List<FileImsiDrawItem> drawList;

        private bool isNeedDraw { get; set; }

        private static FileImsiEventDrawLineLayer instance;

        private class FileImsiDrawItem
        {
            public int FileID
            {
                get;
                private set;
            }

            public long Imsi
            {
                get;
                private set;
            }

            public List<Event> Events
            {
                get;
                private set;
            }

            public FileImsiDrawItem(int fileID, long imsi)
            {
                this.FileID = fileID;
                this.Imsi = imsi;
                this.Events = new List<Event>();
            }
        }
    }
}
