﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class DIYQueryLteTddAssetManager : DiyQueryDataBase
    {
        public string tableName { get; set; } = "tb_xinjiang_LteTddAssetManager";
        public List<LteTddAssetManagerDBInfo> LteTddAssetManagerDBInfoList { get; private set; }

        public DIYQueryLteTddAssetManager()
            : base()
        { }

        public override string Name { get { return "查询LteTdd资管平台"; } }

        protected override string getSqlTextString()
        {
            string name = $"{tableName}_" + DateTime.Now.ToString("yyyyMMdd");
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.AppendFormat(@"SELECT [基站名称],[小区名称],[地址] FROM {0} where [基站名称]='{1}'", name, btsName);

            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[3];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_String;
            return rType;
        }

        protected override void initData()
        {
            LteTddAssetManagerDBInfoList = new List<LteTddAssetManagerDBInfo>();
        }

        protected override void dealReceiveData(Package package)
        {
            LteTddAssetManagerDBInfo info = new LteTddAssetManagerDBInfo();
            info.FillData(package);
            LteTddAssetManagerDBInfoList.Add(info);
        }
    }

    public class LteTddAssetManagerDBInfo
    {
        public string BtsName { get; set; }
        public string CellName { get; set; }
        public string Address { get; set; }

        public void FillData(Package package)
        {
            BtsName = package.Content.GetParamString();
            CellName = package.Content.GetParamString();
            Address = package.Content.GetParamString();
        }
    }
}
