using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class TestReport
    {
        public int TestType { get; set; }

        public string TestTypeDescription
        {
            get { return ((CategoryEnum)CategoryManager.GetInstance()["TestType"]).GetDescription(TestType); }
        }

        public string FileName { get; set; }

        public string SavePath { get; set; }

        public int ImportTime { get; set; }

        public int ProjectType { get; set; }

        public String ProjectDescription
        {
            get { return ((CategoryEnum)CategoryManager.GetInstance()["Project"]).GetDescription(ProjectType); }
        }

        public int Year { get; set; }

        public int Batch { get; set; }

        public int AreaType { get; set; }

        public string AreaTypeDescription
        {
            get { return ((CategoryEnum)CategoryManager.GetInstance()["AreaType"]).GetDescription(AreaType); }
        }

        public int AreaId { get; set; }

        public int DeviceType { get; set; }

        public string DeviceTypeDescription
        {
            get { return ((CategoryEnum)CategoryManager.GetInstance()["DeviceType"]).GetDescription(DeviceType); }
        }

        public int FileType { get; set; }

        public String FileTypeDescription
        {
            get { return ((CategoryEnum)CategoryManager.GetInstance()["FileType"]).GetDescription(FileType); }
        }

        public int ServiceType { get; set; }

        public String ServiceTypeDescription
        {
            get { return ((CategoryEnum)CategoryManager.GetInstance()["ServiceType"]).GetDescription(ServiceType); }
        }

        public int CarrierType { get; set; }

        public string CarrierTypeDescription
        {
            get { return ((CategoryEnum)CategoryManager.GetInstance()["Carrier"]).GetDescription(CarrierType); }
        }

        public int AgentId { get; set; }

        public string AgentDescription
        {
            get { return ((CategoryEnum)CategoryManager.GetInstance()["Agent"]).GetDescription(AgentId); }
        }

        public int StaffId { get; set; }

        public string StaffDescription
        {
            get { return ((CategoryEnum)CategoryManager.GetInstance()["Staff"]).GetDescription(StaffId); }
        }

        public string Desc { get; set; }

        public int DBValue { get; set; }

        public int SubType1 { get; set; }

        public int SubType2 { get; set; }

        public short StatStatus { get; set; }

        public static TestReport FillData(MasterCom.RAMS.Net.Content content)
        {
            TestReport testReport = new TestReport();
            testReport.TestType = content.GetParamInt();
            testReport.FileName = content.GetParamString();
            testReport.SavePath = content.GetParamString();
            testReport.ImportTime = content.GetParamInt();
            testReport.ProjectType = content.GetParamInt();
            testReport.Year = content.GetParamInt();
            testReport.Batch = content.GetParamInt();
            testReport.AreaType = content.GetParamInt();
            testReport.AreaId = content.GetParamInt();
            testReport.DeviceType = content.GetParamInt();
            testReport.FileType = content.GetParamInt();
            testReport.ServiceType = content.GetParamInt();
            testReport.CarrierType = content.GetParamInt();
            testReport.AgentId = content.GetParamInt();
            testReport.StaffId = content.GetParamInt();
            testReport.Desc = content.GetParamString();
            testReport.DBValue = content.GetParamInt();
            testReport.SubType1 = content.GetParamInt();
            testReport.SubType2 = content.GetParamInt();
            testReport.StatStatus = content.GetParamShort();
            return testReport;
        }
    }
}
