﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Drawing;
using System.Drawing.Drawing2D;
using MasterCom.Util;
using System.Windows.Forms;
using System.Runtime.Serialization;
using System.Collections;
using MasterCom.MControls;
using MasterCom.RAMS.Func.EventBlock;
using MasterCom.MTGis;
using MapWinGIS;
using MasterCom.RAMS.Func;
using System.Drawing.Imaging;
using MasterCom.RAMS.Grid;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteAntSimulatorLayer : LayerBase
    {
        public ZTLteAntSimulatorLayer()
            : base("LTE天线权值模拟器-栅格绘制")
        {
            GridColor = new Dictionary<GridLongLat, Color>();
        }

        public Dictionary<GridLongLat, Color> GridColor { get; set; }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            if (GridColor.Count > 0)
            {
                DrawRectangle(graphics);
            }
        }

        private void DrawRectangle(Graphics graphics)
        {
            foreach (GridLongLat key in GridColor.Keys)
            {
                Color rectColor = GridColor[key];
                DbPoint dbP = new DbPoint(key.fltlongitude, key.fltlatitude);
                PointF pointLeft, pointRight;
                gisAdapter.ToDisplay(dbP, out pointLeft);
                dbP = new DbPoint(key.fbrlongitude, key.fbrlatitude);
                gisAdapter.ToDisplay(dbP, out pointRight);
                RectangleF rectF = new RectangleF(pointLeft.X, pointLeft.Y,
                        pointRight.X - pointLeft.X, pointRight.Y - pointLeft.Y);
                //==================0<=Alpha值<=255，越小越透明======================
                graphics.FillRectangle(new SolidBrush(Color.FromArgb(200, rectColor)), rectF);
            }
            graphics.ResetTransform();
        }
    }
}
