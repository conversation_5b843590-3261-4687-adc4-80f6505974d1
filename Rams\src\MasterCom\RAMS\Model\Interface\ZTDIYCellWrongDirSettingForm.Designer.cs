﻿namespace MasterCom.RAMS.Net
{
    partial class ZTDIYCellWrongDirSettingForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.editMeanRxLev = new DevExpress.XtraEditors.SpinEdit();
            this.editDistance = new DevExpress.XtraEditors.SpinEdit();
            this.editAngle = new DevExpress.XtraEditors.SpinEdit();
            this.editRate = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.editMeanRxLev.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.editDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.editAngle.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.editRate.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(49, 19);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(65, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "平均电平 ≥ ";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(37, 58);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(77, 14);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "与小区距离 ≥ ";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(49, 100);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(65, 14);
            this.labelControl3.TabIndex = 2;
            this.labelControl3.Text = "异常角度 ≥ ";
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(49, 147);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(65, 14);
            this.labelControl4.TabIndex = 3;
            this.labelControl4.Text = "异常比例 ≥ ";
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(120, 185);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 4;
            this.btnOK.Text = "确定";
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(207, 185);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 5;
            this.btnCancel.Text = "取消";
            // 
            // editMeanRxLev
            // 
            this.editMeanRxLev.EditValue = new decimal(new int[] {
            80,
            0,
            0,
            -2147483648});
            this.editMeanRxLev.Location = new System.Drawing.Point(120, 16);
            this.editMeanRxLev.Name = "editMeanRxLev";
            this.editMeanRxLev.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.editMeanRxLev.Properties.IsFloatValue = false;
            this.editMeanRxLev.Properties.Mask.EditMask = "N00";
            this.editMeanRxLev.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.editMeanRxLev.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.editMeanRxLev.Size = new System.Drawing.Size(100, 21);
            this.editMeanRxLev.TabIndex = 6;
            // 
            // editDistance
            // 
            this.editDistance.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.editDistance.Location = new System.Drawing.Point(120, 55);
            this.editDistance.Name = "editDistance";
            this.editDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.editDistance.Properties.IsFloatValue = false;
            this.editDistance.Properties.Mask.EditMask = "N00";
            this.editDistance.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.editDistance.Size = new System.Drawing.Size(100, 21);
            this.editDistance.TabIndex = 7;
            // 
            // editAngle
            // 
            this.editAngle.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.editAngle.Location = new System.Drawing.Point(120, 97);
            this.editAngle.Name = "editAngle";
            this.editAngle.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.editAngle.Properties.IsFloatValue = false;
            this.editAngle.Properties.Mask.EditMask = "N00";
            this.editAngle.Properties.MaxValue = new decimal(new int[] {
            180,
            0,
            0,
            0});
            this.editAngle.Size = new System.Drawing.Size(100, 21);
            this.editAngle.TabIndex = 8;
            // 
            // editRate
            // 
            this.editRate.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.editRate.Location = new System.Drawing.Point(120, 140);
            this.editRate.Name = "editRate";
            this.editRate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.editRate.Properties.IsFloatValue = false;
            this.editRate.Properties.Mask.EditMask = "N00";
            this.editRate.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.editRate.Size = new System.Drawing.Size(100, 21);
            this.editRate.TabIndex = 9;
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(226, 19);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(24, 14);
            this.labelControl5.TabIndex = 10;
            this.labelControl5.Text = "dBm";
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(226, 58);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(12, 14);
            this.labelControl6.TabIndex = 11;
            this.labelControl6.Text = "米";
            // 
            // labelControl7
            // 
            this.labelControl7.Location = new System.Drawing.Point(226, 100);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(12, 14);
            this.labelControl7.TabIndex = 12;
            this.labelControl7.Text = "度";
            // 
            // labelControl8
            // 
            this.labelControl8.Location = new System.Drawing.Point(226, 143);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(12, 14);
            this.labelControl8.TabIndex = 13;
            this.labelControl8.Text = "%";
            // 
            // ZTDIYCellWrongDirSettingForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(292, 222);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.editRate);
            this.Controls.Add(this.editAngle);
            this.Controls.Add(this.editDistance);
            this.Controls.Add(this.editMeanRxLev);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl1);
            this.Name = "ZTDIYCellWrongDirSettingForm";
            this.Text = "覆盖方位异常条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.editMeanRxLev.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.editDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.editAngle.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.editRate.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SpinEdit editMeanRxLev;
        private DevExpress.XtraEditors.SpinEdit editDistance;
        private DevExpress.XtraEditors.SpinEdit editAngle;
        private DevExpress.XtraEditors.SpinEdit editRate;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl8;
    }
}