﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.NOP.WF.Core;
using MasterCom.RAMS.Model;
using MasterCom.NOP.DataSet;
using MasterCom.NOP.Report;

namespace MasterCom.RAMS.NOP
{
    class RepeatTaskEvent
    {
        public DateTime Time
        {
            get;
            set;
        }

        public string Name
        {
            get;
            set;
        }

        public string TimeString
        {
            get;
            set;
        }

        public string FileName
        {
            get;
            set;
        }
    }

    class RepeatHistoryTask
    {
        public RepeatHistoryTask(Task task)
        {
            this.task = task;
            this.SetProperties();
        }

        public Task NopTask
        {
            get { return task; }
        }

        public string Name
        {
            get;
            private set;
        }

        public int EventCount
        {
            get;
            private set;
        }

        public DateTime Time
        {
            get;
            private set;
        }

        public string StatusName
        {
            get;
            private set;
        }

        public string TimeString
        {
            get;
            private set;
        }

        public double Longitude
        {
            get;
            private set;
        }

        public double Latitude
        {
            get;
            private set;
        }

        public List<RepeatTaskEvent> TaskEvents
        {
            get;
            private set;
        }

        private void SetProperties()
        {
            this.Name = task.Name;
            this.EventCount = (int)task.GetValue("汇聚事件个数");
            this.Time = (DateTime)task.GetValue("工单月份");
            this.TimeString = this.Time.ToString("yyyy-MM-dd HH:mm:ss");
            this.StatusName = task.CurrentStatusName;
            this.Longitude = (double)task.GetValue("问题点经度");
            this.Longitude = (double)task.GetValue("问题点纬度");
            this.CreateEvents();
        }

        private void CreateEvents()
        {
            byte[] bytes = (byte[])task.GetValue("汇聚事件列表");
            MasterCom.NOP.Report.TemplateInfo info = new MasterCom.NOP.Report.TemplateInfo(SNopSchema);
            Schema tmp = SNopSchema;
            ResultSet resultSet = ReportNetHelper.BytesToResultSet(bytes, ref tmp, ref info);
            SNopSchema = tmp;

            this.TaskEvents = new List<RepeatTaskEvent>();
            foreach (MasterCom.NOP.DataSet.Row nopRow in resultSet.Rows)
            {
                RepeatTaskEvent taskEvt = new RepeatTaskEvent();
                taskEvt.FileName = nopRow.GetValue("文件名称") as string;
                taskEvt.Name = nopRow.GetValue("问题点名称") as string;
                taskEvt.Time = (DateTime)nopRow.GetValue("问题点时间");
                taskEvt.TimeString = taskEvt.Time.ToString("yyyy-MM-dd HH:mm:ss");
                this.TaskEvents.Add(taskEvt);
            }
        }

        private readonly Task task;

        public static Schema SNopSchema { get; set; }
    }

    class RepeatSummaryTask
    {
        public int SN
        {
            get;
            set;
        }

        public RepeatSummaryTask(Task task)
        {
            this.task = task;
            this.SetProperties();
        }

        public Task NopTask
        {
            get { return task; }
        }

        public DateTime Time
        {
            get;
            private set;
        }

        public string TimeString
        {
            get;
            private set;
        }

        public string CityName
        {
            get;
            private set;
        }

        public int GatherEvtCnt
        {
            get;
            private set;
        }

        public int GatherTaskCnt
        {
            get { return HistoryTasks.Count; }
        }

        public string StatusName
        {
            get;
            private set;
        }

        public string RegionName
        {
            get;
            private set;
        }

        public string TaskNames
        {
            get;
            private set;
        }

        public string Name
        {
            get;
            private set;
        }

        public List<RepeatHistoryTask> HistoryTasks
        {
            get;
            private set;
        }

        public void AddHistoryTask(RepeatHistoryTask hTask)
        {
            int pos = 0;
            for (pos = 0; pos < HistoryTasks.Count; ++pos)
            {
                if (HistoryTasks[pos].Time < hTask.Time)
                {
                    break;
                }
            }
            HistoryTasks.Insert(pos, hTask);
        }

        public void CalcResult()
        {
            StringBuilder sb = new StringBuilder();
            int cnt = 0;
            foreach (RepeatHistoryTask t in HistoryTasks)
            {
                sb.Append(t.Name + "|");
                cnt += t.EventCount;
            }
            TaskNames = sb.Length > 0 ? sb.Remove(sb.Length - 1, 1).ToString() : "";
            GatherEvtCnt = cnt;
        }

        private void SetProperties()
        {
            this.Name = task.Name;
            this.Time = (DateTime)task.GetValue("工单月份");
            this.TimeString = this.Time.ToString("yyyy-MM-dd HH:mm:ss");
            this.CityName = task.GetValue("地市") as string;
            this.StatusName = task.CurrentStatusName;
            this.RegionName = task.GetValue("区域名称") as string;
            this.HistoryTasks = new List<RepeatHistoryTask>();
        }

        private readonly Task task;
    }
}
