﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Collections.ObjectModel;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class LtePlanningRoadCondition
    {
        public string XlsFileName { get; set; }

        public bool IsSavePoint { get; set; } = false;

        public int RoadMinInterval { get; set; } = 20;
        public bool IsRoadByTime { get; set; } = true;
        public int RoadLastTime { get; set; } = 10;
        public bool IsRoadByLength { get; set; } = false;
        public int RoadMinLength { get; set; } = 50;

        public bool WeakCoverageEnable { get; set; } = true;
        public double WeakCoverageRsrp { get; set; } = -105;
        public double WeakCoverageRadius { get; set; } = 300;
        public double WeakCoverageRate { get; set; } = 0.7;

        public bool MultiCoverageEnable { get; set; } = true;
        public double MultiCoverageRsrp { get; set; } = -105;
        public double MultiCoverageDiff { get; set; } = 6;
        public double MultiCoverageValue { get; set; } = 4;
        public double MultiCoverageRate { get; set; } = 0.1;
    }

    public class LtePlanningRoadItem
    {
        public FileInfo FileInfo { get; set; }
        public List<TestPoint> TestPoints { get; set; }
        public int LastTime { get; set; }
        public double Length { get; set; }
        public bool IsSavePoint { get; set; }
    }

    public class LtePlanningRoadView
    {
        public LtePlanningRoadView(LtePlanningRoadItem road)
        {
            int midIndex = road.TestPoints.Count / 2;
            this.CentLng = road.TestPoints[midIndex].Longitude;
            this.CentLat = road.TestPoints[midIndex].Latitude;
            this.RoadName = GISManager.GetInstance().GetRoadPlaceDesc(this.CentLng, this.CentLat);
            this.SampleCount = road.TestPoints.Count;
            this.LastTime = road.LastTime;
            this.Length = road.Length;
            this.FileName = road.FileInfo.Name;

            this.DbPoints = new List<DbPoint>();
            foreach (TestPoint tp in road.TestPoints)
            {
                DbPoint dPoint = new DbPoint(tp.Longitude, tp.Latitude);
                this.DbPoints.Add(dPoint);
            }

            TestPoints = new List<TestPoint>();
            if (road.IsSavePoint)
            {
                TestPoints.AddRange(road.TestPoints);
            }
        }

        public string RoadName
        {
            get;
            private set;
        }

        public int SampleCount
        {
            get;
            private set;
        }

        public int LastTime
        {
            get;
            private set;
        }

        public double Length
        {
            get;
            private set;
        }

        public double CentLng
        {
            get;
            private set;
        }

        public double CentLat
        {
            get;
            private set;
        }

        public string FileName
        {
            get;
            private set;
        }

        public List<DbPoint> DbPoints
        {
            get;
            private set;
        }

        public List<TestPoint> TestPoints
        {
            get;
            private set;
        }
    }

    public class MultiCoverageRoadView : LtePlanningRoadView
    {
        public MultiCoverageRoadView(LtePlanningRoadItem road) 
            : base(road)
        {

        }

        public int MultiCoverageSampleCount
        {
            get;
            set;
        }

        public double MultiCoverageSampleRate
        {
            get { return SampleCount == 0 ? 0 : 1D * MultiCoverageSampleCount / SampleCount; }
        }
    }

    public class WeakCoverageRoadView : LtePlanningRoadView
    {
        public WeakCoverageRoadView(LtePlanningRoadItem road)
            : base(road)
        {

        }

        public int WeakCoverageSampleCount
        {
            get;
            set;
        }

        public double WeakCoverageSampleRate
        {
            get { return SampleCount == 0 ? 0 : 1D * WeakCoverageSampleCount / SampleCount; }
        }

        public int CenterBtsCount
        {
            get;
            set;
        }

        public string CenterBtsName
        {
            get;
            set;
        }
    }

    public abstract class LtePlanningRoadEvaluatorBase
    {
        protected LtePlanningRoadEvaluatorBase(LtePlanningRoadCondition cond, double roadPercent)
        {
            PercentRoadCondition roadCond = new PercentRoadCondition(roadPercent, PercentRoadComplete);
            roadCond.IsCheckDuration = cond.IsRoadByTime;
            roadCond.MinDuration = cond.RoadLastTime;
            roadCond.IsCheckMinLength = cond.IsRoadByLength;
            roadCond.MinLength = cond.RoadMinLength;
            roadCond.IsCheckDistanceGap = true;
            roadCond.MaxDistanceGap = cond.RoadMinInterval;

            roadBuilder = new PercentRoadBuilder(roadCond);
        }

        public bool Enable
        {
            get;
            protected set;
        }

        public virtual void CalcResult()
        {
        }

        public abstract object GetResult();

        public abstract void Clear();

        public virtual void StartStatFile(FileInfo fileInfo)
        {
            curFileInfo = fileInfo;
        }

        public virtual void DoWithTestPoint(TestPoint tp)
        {
            if (IsValidTestPoint(tp))
            {
                roadBuilder.AddPoint(tp, true);
            }
            else
            {
                roadBuilder.AddPoint(tp, false);
            }
        }

        public virtual void FinishStatFile()
        {
            roadBuilder.StopRoading();
        }

        protected abstract void PercentRoadComplete(object sender, PercentRoadItem roadItem);

        protected abstract bool IsValidTestPoint(TestPoint tp);

        protected PercentRoadBuilder roadBuilder;
        protected FileInfo curFileInfo;
    }

    public class WeakCoverageRoadEvaluator : LtePlanningRoadEvaluatorBase
    {
        public WeakCoverageRoadEvaluator(LtePlanningRoadCondition cond) : base(cond, cond.WeakCoverageRate)
        {
            this.cond = cond;
            this.Enable = cond.WeakCoverageEnable;
            this.roadViewList = new List<WeakCoverageRoadView>();
            this.lnglatRadius = DistanceTranslator.LatitudePerMeter() * cond.WeakCoverageRadius;
        }

        public override object GetResult()
        {
            List<WeakCoverageRoadView> result = new List<WeakCoverageRoadView>();
            foreach (WeakCoverageRoadView roadView in roadViewList)
            {
                if (roadView.WeakCoverageSampleRate < cond.WeakCoverageRate)
                {
                    continue;
                }
                GetCentreBts(roadView);
                result.Add(roadView);
            }
            return result;
        }

        public override void Clear()
        {
            roadViewList.Clear();
        }

        protected override bool IsValidTestPoint(TestPoint tp)
        {
            float? rsrp = (float?)tp["lte_RSRP"];
            return rsrp == null || rsrp <= cond.WeakCoverageRsrp;
        }

        protected override void PercentRoadComplete(object sender, PercentRoadItem roadItem)
        {
            LtePlanningRoadItem planRoad = new LtePlanningRoadItem();
            planRoad.FileInfo = curFileInfo;
            planRoad.IsSavePoint = cond.IsSavePoint;
            planRoad.LastTime = (int)roadItem.Duration;
            planRoad.Length = roadItem.Length;
            planRoad.TestPoints = roadItem.TestPoints;
            ProcessRoad(planRoad);
        }

        private void ProcessRoad(LtePlanningRoadItem road)
        {
            WeakCoverageRoadView roadView = new WeakCoverageRoadView(road);
            foreach (TestPoint tp in road.TestPoints)
            {
                if (IsValidTestPoint(tp))
                {
                    ++roadView.WeakCoverageSampleCount;
                }
            }
            roadViewList.Add(roadView);
        }

        private void GetCentreBts(WeakCoverageRoadView roadView)
        {
            ReadOnlyCollection<LTEBTS> btsList = LtePlanningInfoManager.Instance.LteBTSs;
            StringBuilder btsNames = new StringBuilder();
            foreach (LTEBTS bts in btsList)
            {
                if (Math.Abs(bts.Longitude - roadView.CentLng) > lnglatRadius
                    || Math.Abs(bts.Latitude - roadView.CentLat) > lnglatRadius)
                {
                    continue;
                }

                double dis = MathFuncs.GetDistance(bts.Longitude, bts.Latitude, roadView.CentLng, roadView.CentLat);
                if (dis > cond.WeakCoverageRadius)
                {
                    continue;
                }

                btsNames.Append(bts.Name);
                btsNames.Append('|');
                ++roadView.CenterBtsCount;
            }

            if (btsNames.Length > 0)
            {
                btsNames.Remove(btsNames.Length - 1, 1);
            }
            roadView.CenterBtsName = btsNames.ToString();
        }

        private readonly LtePlanningRoadCondition cond;
        private readonly List<WeakCoverageRoadView> roadViewList;
        private readonly double lnglatRadius;
    }

    public class MultiCoverageRoadEvaluator : LtePlanningRoadEvaluatorBase
    {
        public MultiCoverageRoadEvaluator(LtePlanningRoadCondition cond) : base(cond, cond.MultiCoverageRate)
        {
            this.cond = cond;
            this.Enable = cond.MultiCoverageEnable;
            this.roadViewList = new List<MultiCoverageRoadView>();
        }

        public override object GetResult()
        {
            List<MultiCoverageRoadView> result = new List<MultiCoverageRoadView>();
            foreach (MultiCoverageRoadView roadView in roadViewList)
            {
                if (roadView.MultiCoverageSampleRate >= cond.MultiCoverageRate)
                {
                    result.Add(roadView);
                }
            }
            return result;
        }

        public override void Clear()
        {
            roadViewList.Clear();
        }

        protected override bool IsValidTestPoint(TestPoint tp)
        {
            LtePlanningTestPoint ptp = new LtePlanningTestPoint(tp);
            if (ptp.Rsrp == null || ptp.Rsrp < cond.MultiCoverageRsrp)
            {
                return false;
            }
            if (ptp.MainCell == null || ptp.NbCells.Count < cond.MultiCoverageValue - 1)
            {
                return false;
            }

            int multiCoverage = 1; // contain maincell
            for (int i = 0; i < ptp.NbCells.Count; ++i)
            {
                if (ptp.MainCell.EARFCN != ptp.NbCells[i].EARFCN)
                {
                    continue;
                }

                if (ptp.Rsrp - ptp.NbRsrpList[i] <= cond.MultiCoverageDiff)
                {
                    ++multiCoverage;
                }
            }

            return multiCoverage >= cond.MultiCoverageValue;
        }

        protected override void PercentRoadComplete(object sender, PercentRoadItem roadItem)
        {
            LtePlanningRoadItem planRoad = new LtePlanningRoadItem();
            planRoad.FileInfo = curFileInfo;
            planRoad.IsSavePoint = cond.IsSavePoint;
            planRoad.LastTime = (int)roadItem.Duration;
            planRoad.Length = roadItem.Length;
            planRoad.TestPoints = roadItem.TestPoints;

            MultiCoverageRoadView roadView = new MultiCoverageRoadView(planRoad);
            roadView.MultiCoverageSampleCount = roadItem.ValidCount;
            roadViewList.Add(roadView);
        }

        private readonly LtePlanningRoadCondition cond;
        private readonly List<MultiCoverageRoadView> roadViewList;
    }
}
