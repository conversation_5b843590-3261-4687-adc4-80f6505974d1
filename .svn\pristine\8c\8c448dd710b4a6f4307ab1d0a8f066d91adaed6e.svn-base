﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.ES.Core;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS;


namespace MasterCom.ES.Data
{
    public class DTDataProvider : DataProvider
    {
        private static DTDataProvider provInstance = null;
        public static DTDataProvider GetDTProviderInstance()
        {
            if (provInstance == null)
            {
                provInstance = new DTDataProvider();
            }
            return provInstance;
        }
        public DTDataProvider()
        {
            /**
            this.VFuncGroup["事件"] = new VirtualFuncGroup("事件");
            this.VFuncGroup["层三消息"] = new VirtualFuncGroup("层三消息");
            this.VFuncGroup["指标"] = new VirtualFuncGroup("指标");
            this.VFuncGroup["其它"] = new VirtualFuncGroup("其它");

            FuncInfo fi = new FuncInfo("获取最近某事件的时间", "输入参数[Time,时长毫秒,事件ID] 返回参数：long表示的时间毫秒值", getLastEventTime);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值负表示向前找，正值表示向后找)"));
            fi.ParamDescList.Add(new ParamInfo("事件ID(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["事件"].Funcs.Add(fi);
            fi = new FuncInfo("获取EventID", "返回参数：int表示的事件ID", getEventId, false);
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["事件"].Funcs.Add(fi);

            fi = new FuncInfo("获取事件value值", "返回参数：int表示的事件value值", getEventValue, false);
            fi.ParamDescList.Add(new ParamInfo("ValueN"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["事件"].Funcs.Add(fi);

            fi = new FuncInfo("前N毫秒平均值", "输入参数[Time,前N毫秒,参数名,最小采样点个数] 返回参数：int表示的平均值", getParamMean);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值，负表示向前，正表示向后)"));
            fi.ParamDescList.Add(new ParamInfo("参数名(参数字符串)"));
            fi.ParamDescList.Add(new ParamInfo("最小采样点个数(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["指标"].Funcs.Add(fi);

            fi = new FuncInfo("前N毫秒最小值", "输入参数[Time,前N毫秒,参数名,最小采样点个数] 返回参数：int表示的最小值", getParamMin);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值，负表示向前，正表示向后)"));
            fi.ParamDescList.Add(new ParamInfo("参数名(参数字符串)"));
            fi.ParamDescList.Add(new ParamInfo("最小采样点个数(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["指标"].Funcs.Add(fi);

            fi = new FuncInfo("前N毫秒最大值", "输入参数[Time,前N毫秒,参数名,最小采样点个数] 返回参数：int表示的最大值", getParamMax);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值，负表示向前，正表示向后)"));
            fi.ParamDescList.Add(new ParamInfo("参数名(参数字符串)"));
            fi.ParamDescList.Add(new ParamInfo("最小采样点个数(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["指标"].Funcs.Add(fi);

            fi = new FuncInfo("前N毫秒GSM临区是否一直比主服强", "输入参数[Time,前N毫秒,差距值,最小采样点个数] 返回参数：1 一直强，0不满足", isGSMNeibAlwayseStrong);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值，负表示向前，正表示向后)"));
            fi.ParamDescList.Add(new ParamInfo("差距值(整数值)"));
            fi.ParamDescList.Add(new ParamInfo("最小采样点个数(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["指标"].Funcs.Add(fi);

            fi = new FuncInfo("被叫前N毫秒平均值", "输入参数[Time,前N毫秒,参数名,最小采样点个数] 返回参数：int表示的平均值", getParamMean_TarFile);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值，负表示向前，正表示向后)"));
            fi.ParamDescList.Add(new ParamInfo("参数名(参数字符串)"));
            fi.ParamDescList.Add(new ParamInfo("最小采样点个数(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["指标"].Funcs.Add(fi);

            this.VFuncGroup["事件"].Funcs.Add(fi);
            fi = new FuncInfo("同车是否有指定事件时间", "输入参数[Time,事件ID,距离米,间隔时间秒] 返回参数：事件的时间毫秒 表示有 -99999 表示无", sameCarHasEventByTime_getTime);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("指定事件ID(整数值)"));
            fi.ParamDescList.Add(new ParamInfo("指定距离米(整数值)"));
            fi.ParamDescList.Add(new ParamInfo("指定时间间隔(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["事件"].Funcs.Add(fi);
            fi = new FuncInfo("获取到下一个事件的间隔", "输入参数[Time,事件ID] 返回参数：毫秒值表示的时间间隔,-99999表示未找到", getNextEventSpanByTime, false);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("指定事件ID(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["事件"].Funcs.Add(fi);
            fi = new FuncInfo("获取到前一个事件的间隔", "输入参数[Time,事件ID] 返回参数：毫秒值表示的时间间隔,-99999表示未找到", getPreviousEventSpanByTime, false);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("指定事件ID(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["事件"].Funcs.Add(fi);

            fi = new FuncInfo("搜索指定文件中指定事件",
                 "fileID,事件ID号，查找起始时间(long) ,时间间隔(>0，往后找；<0，往前找)long , 返回参数:事件时间(没找到，返回0)",
                 GetEventInFile);
            fi.ParamDescList.Add(new ParamInfo("文件ID"));
            fi.ParamDescList.Add(new ParamInfo("事件ID"));
            fi.ParamDescList.Add(new ParamInfo("查找起始时间"));
            fi.ParamDescList.Add(new ParamInfo("时间间隔"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["事件"].Funcs.Add(fi);

            fi = new FuncInfo("检验参数持续性", "输入参数[Time,前N毫秒,参数名,最小采样点个数,比较值,比较方法] 返回参数：1 持续 0 不持续 -99999采样数不符", checkConsistency);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值)"));
            fi.ParamDescList.Add(new ParamInfo("参数名(参数字符串)"));
            fi.ParamDescList.Add(new ParamInfo("最小采样点个数(整数值)"));
            fi.ParamDescList.Add(new ParamInfo("比较值(整数小数均可)"));
            fi.ParamDescList.Add(new ParamInfo("比较方法(1 小于；2 大于；3 等于；4 小于等于；5 大于等于；)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["指标"].Funcs.Add(fi);


            fi = new FuncInfo("获取层三消息的参数值", "层三消息解码 输入参数[Time,时长毫秒,消息ID,参数名,第几个值] 返回参数：-99999：没有对应消息  -99998：没有取到对应值", getL3DecodeValue);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值，负表示向前，正表示向后)"));
            fi.ParamDescList.Add(new ParamInfo("消息ID(整数值)"));
            fi.ParamDescList.Add(new ParamInfo("参数名(参数字符串)"));
            fi.ParamDescList.Add(new ParamInfo("第几个值(-1表示只取一个值，其他表示取得数组中的值0.1.2.3...)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["层三消息"].Funcs.Add(fi);
            fi = new FuncInfo("获取被叫层三消息的参数值", "层三消息解码 输入参数[Time,时长毫秒,消息ID,参数名,第几个值] 返回参数：-99999：没有对应消息  -99998：没有取到对应值", getL3DecodeValueTarFile, false);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值，负表示向前，正表示向后)"));
            fi.ParamDescList.Add(new ParamInfo("消息ID(整数值)"));
            fi.ParamDescList.Add(new ParamInfo("参数名(参数字符串)"));
            fi.ParamDescList.Add(new ParamInfo("第几个值(-1表示只取一个值，其他表示取得数组中的值0.1.2.3...)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["层三消息"].Funcs.Add(fi);
            fi = new FuncInfo("获取层三消息的方向", "输入参数[Time,时长毫秒,消息ID] 返回参数：1下行 2 上行 -99999：没找到", getL3MessageDirection);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值，负表示向前，正表示向后)"));
            fi.ParamDescList.Add(new ParamInfo("消息ID(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["层三消息"].Funcs.Add(fi);
            fi = new FuncInfo("获取层三消息的时间点", "输入参数[Time,时长毫秒,消息ID] 返回参数：时间值 -99999：没找到", getL3MessageTime);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值，负表示向前，正表示向后)"));
            fi.ParamDescList.Add(new ParamInfo("消息ID(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["层三消息"].Funcs.Add(fi);
            fi = new FuncInfo("获取被叫层三消息的时间点", "输入参数[Time,时长毫秒,消息ID] 返回参数：时间值 -99999：没找到", getL3MessageTimeTarFile, false);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值，负表示向前，正表示向后)"));
            fi.ParamDescList.Add(new ParamInfo("消息ID(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["层三消息"].Funcs.Add(fi);
            fi = new FuncInfo("获取被叫层三消息的方向", "输入参数[Time,时长毫秒,消息ID] 返回参数：1下行 2 上行 -99999：没找到", getL3MessageDirectionTarFile, false);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值，负表示向前，正表示向后)"));
            fi.ParamDescList.Add(new ParamInfo("消息ID(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["层三消息"].Funcs.Add(fi);
            fi = new FuncInfo("获取前一个指定方向的消息的ID", "输入参数[Time,时长毫秒,指定方向] 返回参数：整型表示的消息ID -99999：没找到", getL3MessageOfDirection);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值)负数表示向前，正数表示向后找"));
            fi.ParamDescList.Add(new ParamInfo("指定方向(1 下行；2 上行)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["层三消息"].Funcs.Add(fi);
            fi = new FuncInfo("检测TD空闲状态", "输入参数[Time,时长毫秒] 返回参数：空闲开始时间 -99999：没找到", checkTDIdleStatus);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("向后毫秒(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["层三消息"].Funcs.Add(fi);
            fi = new FuncInfo("查询是否有事件", "判断在某时间点前是否有指定的事件 输入参数[Time,时长毫秒,事件ID] 返回参数：0：没有  1：有", hasEventBefore);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值,负数表示向前，正数表示向后找)"));
            fi.ParamDescList.Add(new ParamInfo("事件ID(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["事件"].Funcs.Add(fi);
            fi = new FuncInfo("获取当前事件的时间", "获取当前时间的时间 返回参数：毫秒值表示的时间", getEventTimeValue, false);
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["事件"].Funcs.Add(fi);
            fi = new FuncInfo("参数陡降查询", "判断是否发生参数陡降 输入参数[Time,时长毫秒,参数名,正常值,异常值,最小异常采样点数] 返回参数：0：没有  1：有陡降", hasParamSuddenDown);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值)"));
            fi.ParamDescList.Add(new ParamInfo("参数名(参数字符串)"));
            fi.ParamDescList.Add(new ParamInfo("正常值(整数小数均可)"));
            fi.ParamDescList.Add(new ParamInfo("异常值(整数小数均可)"));
            fi.ParamDescList.Add(new ParamInfo("最小异常采样点数(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["指标"].Funcs.Add(fi);
            fi = new FuncInfo("参数陡升查询", "判断是否发生参数陡升 输入参数[Time,时长毫秒,参数名,正常值,异常值,最小异常采样点数] 返回参数：0：没有  1：有陡升", hasParamSuddenUp);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值)"));
            fi.ParamDescList.Add(new ParamInfo("参数名(参数字符串)"));
            fi.ParamDescList.Add(new ParamInfo("正常值(整数小数均可)"));
            fi.ParamDescList.Add(new ParamInfo("异常值(整数小数均可)"));
            fi.ParamDescList.Add(new ParamInfo("最小异常采样点数(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["指标"].Funcs.Add(fi);
            fi = new FuncInfo("参数快衰落查询", "判断是否发生参数快衰落查询 输入参数[Time,时长毫秒,参数名,正常值,异常值,窗格最小采样点数] 返回参数：0：没有  1：有", hasParamFastDown);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值)"));
            fi.ParamDescList.Add(new ParamInfo("参数名(参数字符串)"));
            fi.ParamDescList.Add(new ParamInfo("正常值(整数小数均可)"));
            fi.ParamDescList.Add(new ParamInfo("异常值(整数小数均可)"));
            fi.ParamDescList.Add(new ParamInfo("窗格最小采样点数(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["指标"].Funcs.Add(fi);
            fi = new FuncInfo("参数快提升查询", "判断是否发生参数快提升查询 输入参数[Time,时长毫秒,参数名,正常值,异常值,窗格最小采样点数] 返回参数：0：没有  1：有", hasParamFastUp);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值)"));
            fi.ParamDescList.Add(new ParamInfo("参数名(参数字符串)"));
            fi.ParamDescList.Add(new ParamInfo("正常值(整数小数均可)"));
            fi.ParamDescList.Add(new ParamInfo("异常值(整数小数均可)"));
            fi.ParamDescList.Add(new ParamInfo("窗格最小采样点数(整数值)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["指标"].Funcs.Add(fi);
            fi = new FuncInfo("获取某时刻参数某参数值", "获取某时刻参数某参数值 输入参数[Time,参数名,时间范围毫秒] 返回参数：-99999 未取到 其它表示取得的参数值", getParamByTimeValue);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("参数名(参数字符串)"));
            fi.ParamDescList.Add(new ParamInfo("时间范围毫秒(整数正表示向后找参数，负表示向前找)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["指标"].Funcs.Add(fi);

            fi = new FuncInfo("获取被叫某时刻参数某参数值", "获取被叫某时刻参数某参数值 输入参数[Time,参数名,时间范围毫秒] 返回参数：-99999 未取到 其它表示取得的参数值", getParamByTimeValue_TarFile);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("参数名(参数字符串)"));
            fi.ParamDescList.Add(new ParamInfo("时间范围毫秒(整数正表示向后找参数，负表示向前找)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["指标"].Funcs.Add(fi);

            fi = new FuncInfo("获取某时刻采样点时间", "获取某时刻采样点时间 输入参数[Time,时间范围毫秒] 返回参数：-99999 未取到 其它表示取得的时间值", getSampleTimeByTimeValue);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("时间范围毫秒(整数正表示向后找参数，负表示向前找)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["指标"].Funcs.Add(fi);

            fi = new FuncInfo("判断是否信令缺失", "判断是否信令缺失，返回 输入参数[Time,时间范围毫秒,缺失最小时间间隔毫秒] 返回参数：信令开始缺失的毫秒时间(与指定时间点靠近) 未取到-99999", getLostMessage);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("时间范围毫秒(整数正表示向后找参数，负表示向前找)"));
            fi.ParamDescList.Add(new ParamInfo("缺失最小时间间隔毫秒(long值毫秒)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["层三消息"].Funcs.Add(fi);

            fi = new FuncInfo("判断是否采样点缺失", "判断是否采样点缺失，返回 输入参数[Time,时间范围毫秒,缺失最小时间间隔毫秒] 返回参数：采样点开始缺失的毫秒时间(与指定时间点靠近) 未取到-99999", getLostTestPoint);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("时间范围毫秒(整数正表示向后找参数，负表示向前找)"));
            fi.ParamDescList.Add(new ParamInfo("缺失最小时间间隔毫秒(long值毫秒)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["指标"].Funcs.Add(fi);

            fi = new FuncInfo("获得消息的时间", "输入参数[Time,时长毫秒] 返回参数：消息出现的时间  -99999 未找到：没找到", getAnyMessageTime);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("前N毫秒(整数值)负数表示向前，正数表示向后找"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["层三消息"].Funcs.Add(fi);

            fi = new FuncInfo("获得取值描述", "输入参数[字典名称,值ID] 返回参数：描述值", getDicDescription, false);
            fi.ParamDescList.Add(new ParamInfo("字典名称(字符串)"));
            fi.ParamDescList.Add(new ParamInfo("整数ID"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["其它"].Funcs.Add(fi);

            fi = new FuncInfo("获得时间描述", "输入参数[时间值long] 返回参数：时间描述值", getTimeDescription, false);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["其它"].Funcs.Add(fi);

            fi = new FuncInfo("事件Value赋值", "输入参数[序号(0-9),值int] 返回参数：无", setEventValueAt, false);
            fi.ParamDescList.Add(new ParamInfo("序号(0-9)(int值)"));
            fi.ParamDescList.Add(new ParamInfo("整型值"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["其它"].Funcs.Add(fi);

            fi = new FuncInfo("临区漏定义核查", "输出漏定义临区到公共预存值中pub_ES_LostNeibCells 返回值 漏定义临区的个数 0 表示没有漏定义的", checkLostNeibours);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["其它"].Funcs.Add(fi);

            fi = new FuncInfo("重新载入回放数据", "重新载入回放数据", doReloadReplayFile, false);
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["其它"].Funcs.Add(fi);

            fi = new FuncInfo("获取被叫文件ID", "获取被叫文件ID", getCalledFileID, false);
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["其它"].Funcs.Add(fi);

            fi = new FuncInfo("GSM_切换_记忆效应核查", "test1", func_GSM_搜索下一次空闲状态消息时间);
            fi.ParamDescList.Add(new ParamInfo("para1"));
            fi.ParamDescList.Add(new ParamInfo("para2"));
            fi.ParamDescList.Add(new ParamInfo("para3"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["其它"].Funcs.Add(fi);

            //fi = new FuncInfo("GSM_核查载频故障", "输入参数[Time,前N毫秒,bcch,bsic] 返回参数：int表示的个数", func_GSM_核查载频故障);
            //fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            //fi.ParamDescList.Add(new ParamInfo("参数名(bcch)"));
            //fi.ParamDescList.Add(new ParamInfo("参数名(bsic)"));
            //this.funcInfoDic[fi.Name] = fi;
            //this.VFuncGroup["其它"].Funcs.Add(fi);

            //add by yht
            fi = new FuncInfo("PUB_拐角时间点获取", "输入参数[起始Time,结束Time] 返回参数：int表示拐角时间点", func_PUB_拐角时间点获取);
            fi.ParamDescList.Add(new ParamInfo("起始Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("结束Time(long值毫秒)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["其它"].Funcs.Add(fi);

            fi = new FuncInfo("CDMA_搜索指定时间点后正常的PN", "CDMA_搜索指定时间点后正常的PN，输入参数[Time,时间范围毫秒] 返回参数：对应的PN 未取到-99999", func_CDMA_搜索指定时间点后正常的PN);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("时间范围毫秒(整数正表示向后找参数，负表示向前找)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["指标"].Funcs.Add(fi);

            fi = new FuncInfo("CDMA_获取PN在指定时间段数据中的状态", "CDMA_获取PN在指定时间段数据中的状态，输入参数[Time,时间范围毫秒,PN] 返回所处的状态0激活，1 候选，2 邻区，-1，无，-99999异常", func_CDMA_获取PN在指定时间段数据中的状态);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("时间范围毫秒(整数正表示向后找参数，负表示向前找)"));
            fi.ParamDescList.Add(new ParamInfo("PN"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["指标"].Funcs.Add(fi);

            fi = new FuncInfo("CDMA_获取消息PSMM消息中的PN状态", "CDMA_获取消息PSMM消息中的PN状态，输入参数[Time,时间范围毫秒,PN,PilotINC]  返回：KEEPALIVE状态,-99999异常", func_CDMA_获取消息PSMM消息中的PN状态);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("时间范围毫秒(整数正表示向后找参数，负表示向前找)"));
            fi.ParamDescList.Add(new ParamInfo("PN"));
            fi.ParamDescList.Add(new ParamInfo("Pilot_INC"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["层三消息"].Funcs.Add(fi);

            fi = new FuncInfo("CDMA_获取消息NLUM消息中的PN状态", "CDMA_获取消息NLUM消息中的PN状态，输入参数[Time,时间范围毫秒,PN]  返回：1,在,-99999异常", func_CDMA_获取消息NLUM消息中的PN状态);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("时间范围毫秒(整数正表示向后找参数，负表示向前找)"));
            fi.ParamDescList.Add(new ParamInfo("PN"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["层三消息"].Funcs.Add(fi);

            fi = new FuncInfo("CDMA_获取指定时间点的指定PN的ECIO", "CDMA_获取消息PSMM消息中的PN状态，输入参数[Time,时间范围毫秒,PN]  返回：ec/io,-99999异常", func_CDMA_获取指定时间点的指定PN的ECIO);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("时间范围毫秒(整数正表示向后找参数，负表示向前找)"));
            fi.ParamDescList.Add(new ParamInfo("PN"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["指标"].Funcs.Add(fi);

            fi = new FuncInfo("CDMA_获取指定时间段的导频污染分级个数", "CDMA_获取指定时间段的导频污染分级个数，输入参数[Time,时间范围毫秒]  返回：满足与最强查6dB内,都EC/IO都大于-18的分集个数,-99999异常", func_CDMA_获取指定时间段的导频污染分级个数);
            fi.ParamDescList.Add(new ParamInfo("Time(long值毫秒)"));
            fi.ParamDescList.Add(new ParamInfo("时间范围毫秒(整数正表示向后找参数，负表示向前找)"));
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["其它"].Funcs.Add(fi);

            //add by yht end

            fi = new FuncInfo("自定义函数", "执行自定义函数", doSelDefineCodeFunc);
            this.funcInfoDic[fi.Name] = fi;
            this.VFuncGroup["其它"].Funcs.Add(fi);
            fi.ParamDescList.Add(new ParamInfo("自定义操作名称"));
            fi.ParamDescList.Add(new ParamInfo("自定义参数1"));
            fi.ParamDescList.Add(new ParamInfo("自定义参数2"));
            fi.ParamDescList.Add(new ParamInfo("自定义参数3"));
            fi.ParamDescList.Add(new ParamInfo("自定义参数4"));
            fi.ParamDescList.Add(new ParamInfo("自定义参数5"));
            provInstance = this;
            *///
        }

        public override string Name
        {
            get { return "路测数据"; }
        }
        #region 对外提供的调用函数
        /**
        private void func_GSM_搜索下一次空闲状态消息时间(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                if (evt == null)
                {
                    arg.ret = -1;
                    throw (new Exception("未指定待分析事件！"));
                }
                GSM_切换_记忆效应核查_es lo = new GSM_切换_记忆效应核查_es();
                arg.ret = lo.GetFuncResult(arg.param);
            }
        }   
        private void func_PUB_拐角时间点获取(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                if (evt == null)
                {
                    arg.ret = -1;
                    throw (new Exception("未指定待分析事件！"));
                }
                PUB_拐角点核查_es lo = new PUB_拐角点核查_es();
                arg.ret = lo.GetFuncResult(arg.param);
            }
        }
        private void func_CDMA_搜索指定时间点后正常的PN(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                if (evt == null)
                {
                    arg.ret = -1;
                    throw (new Exception("未指定待分析事件！"));
                }
                CDMA_搜索指定时间点后正常的PN_es lo = new CDMA_搜索指定时间点后正常的PN_es();
                arg.ret = lo.GetFuncResult(arg.param);
            }
        }
        private void func_CDMA_获取PN在指定时间段数据中的状态(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                if (evt == null)
                {
                    arg.ret = -1;
                    throw (new Exception("未指定待分析事件！"));
                }
                CDMA_获取PN在指定时间段数据中的状态_es lo = new CDMA_获取PN在指定时间段数据中的状态_es();
                arg.ret = lo.GetFuncResult(arg.param);
            }
        }

        private void func_CDMA_获取消息PSMM消息中的PN状态(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                if (evt == null)
                {
                    arg.ret = -1;
                    throw (new Exception("未指定待分析事件！"));
                }
                CDMA_获取消息PSMM消息中的PN状态_es lo = new CDMA_获取消息PSMM消息中的PN状态_es();
                arg.ret = lo.GetFuncResult(arg.param);
            }
        }
        private void func_CDMA_获取消息NLUM消息中的PN状态(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                if (evt == null)
                {
                    arg.ret = -1;
                    throw (new Exception("未指定待分析事件！"));
                }
                CDMA_获取消息NLUM消息中的PN状态_es lo = new CDMA_获取消息NLUM消息中的PN状态_es();
                arg.ret = lo.GetFuncResult(arg.param);
            }
        }
        private void func_CDMA_获取指定时间点的指定PN的ECIO(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                if (evt == null)
                {
                    arg.ret = -1;
                    throw (new Exception("未指定待分析事件！"));
                }
                CDMA_获取指定时间点的指定PN的ECIO_es lo = new CDMA_获取指定时间点的指定PN的ECIO_es();
                arg.ret = lo.GetFuncResult(arg.param);
            }
        }
        private void func_CDMA_获取指定时间段的导频污染分级个数(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                if (evt == null)
                {
                    arg.ret = -1;
                    throw (new Exception("未指定待分析事件！"));
                }
                CDMA_获取指定时间段的导频污染分级个数_es lo = new CDMA_获取指定时间段的导频污染分级个数_es();
                arg.ret = lo.GetFuncResult(arg.param);
            }
        }

        private void doSelDefineCodeFunc(object sender, EventArgs e)
        {
            if (e is CommonEventArgs)
            {
                CommonEventArgs arg = e as CommonEventArgs;
                if (evt == null)
                {
                    arg.ret = -1;
                    throw (new Exception("未指定待分析事件！"));
                }
                else
                {
                    string paraString = arg.param;
                    string[] parametersList = paraString.Split(',');
                    if (parametersList.Length >= 2)
                    {
                        int firstPos = paraString.IndexOf(',');
                        string laterParam = paraString.Substring(firstPos + 1);
                        string modName = parametersList[0];
                        ESOwnFuncCommander commander = null;
                        if (CommanderDic.TryGetValue(modName, out commander))
                        {
                            if (!commander._classReady && !commander._hasError)
                            {
                                commander.initFuncClass();
                            }
                            if (commander._classReady)
                            {
                                object[] paramObj = new object[1];
                                paramObj[0] = laterParam;
                                object objClass = commander.clzzInst;
                                Type restype = objClass.GetType().GetMethod("GetFuncResult").ReturnType;
                                if (restype == typeof(double))
                                {
                                    double dResult = (double)objClass.GetType().InvokeMember(
                                                   "GetFuncResult",
                                                   System.Reflection.BindingFlags.InvokeMethod, null, objClass,
                                                   paramObj);
                                    arg.ret = dResult;
                                }
                                else if (restype == typeof(string))
                                {
                                    string strResult = (string)objClass.GetType().InvokeMember(
                                                   "GetFuncResult",
                                                   System.Reflection.BindingFlags.InvokeMethod, null, objClass,
                                                   paramObj);
                                    arg.str = strResult;
                                }
                                else
                                {
                                    throw (new Exception("自定义函数：" + modName + "返回类型非预期，必须为double 或 string"));
                                }
                            }
                        }
                        else
                        {
                            throw (new Exception("未找到自定义函数：" + modName + ""));
                        }
                    }
                }
            }
        }
        */
       
        #endregion

        internal Dictionary<string, ESOwnFuncCommander> CommanderDic { get; set; } = new Dictionary<string, ESOwnFuncCommander>();
        
        //private Event evt = null;
        public override void fireDataFill(object objData)
        {
            //if (objData is Event)
            //{
            //    this.evt = objData as Event;
            //}
        }
        
    }
}
