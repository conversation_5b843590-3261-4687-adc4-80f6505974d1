using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NrCellInfo
    {
        public NRCell Cell { get; set; }
        public int SN { get; set; }
        public string FileName { get; set; }
        public string CellName { get; set; }
        public int? Tac { get; set; }
        public long? Nci { get; set; }
        public int Arfcn { get; set; }
        public int Pci { get; set; }
        public int CellID { get; set; }

        public string StrCellID
        {
            get
            {
                if (CellID == 0)
                {
                    return "";
                }
                else
                {
                    return CellID.ToString();
                }
            }
        }
        public string IndoorOrOutdoor { get; set; }
        public int SampleCount { get; set; }
        public long SampleTotalCount { get; set; }
        public float SampleCountRatio { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public float MinRsrp { get; set; }
        public float MaxRsrp { get; set; }
        public double? AvgRsrp { get; set; } = 0;

        public float MinRsrq { get; set; }
        public float MaxRsrq { get; set; }
        public double? AvgRsrq { get; set; } = 0;

        public double MinDistance { get; set; }
        public double MaxDistance { get; set; }
        public double? AvgDistance { get; set; } = 0;

        public float MinSinr { get; set; }
        public float MaxSinr { get; set; }
        public double? AvgSinr { get; set; } = 0;

        public string Grid { get; set; }
        public string CellProperty { get; set; }
        public string CellCode { get; set; }

        protected float rsrpSum = 0;
        protected float sinrSum = 0;
        protected float rsrqSum = 0;
        protected double distanceSum = 0;

        public void AddCellInfo(NRCell nrCell, TestPoint tp, float rsrp, float rsrq, float sinr)
        {
            setNormalIndexData(rsrp, rsrq, sinr);
            FileName = tp.FileName;

            double distance = Math.Round(MathFuncs.GetDistance(tp.Longitude, tp.Latitude, nrCell.Longitude, nrCell.Latitude), 2);
            if (MinDistance > distance)
                MinDistance = distance;
            else if (MaxDistance < distance)
                MaxDistance = distance;
            distanceSum += distance;
        }

        public void AddCellInfo(string fileName, float rsrp, float rsrq, float sinr)
        {
            setNormalIndexData(rsrp, rsrq, sinr);
            FileName = fileName;
        }

        private void setNormalIndexData(float rsrp, float rsrq, float sinr)
        {
            SampleCount++;

            if (MinRsrp > rsrp)
                MinRsrp = rsrp;
            else if (MaxRsrp < rsrp)
                MaxRsrp = rsrp;
            rsrpSum += rsrp;

            if (MinRsrq > rsrq)
                MinRsrq = rsrq;
            else if (MaxRsrq < rsrq)
                MaxRsrq = rsrq;
            rsrqSum += rsrq;

            if (MinSinr > sinr)
                MinSinr = sinr;
            else if (MaxSinr < sinr)
                MaxSinr = sinr;
            sinrSum += sinr;
        }

        public void AddNoCellInfo(float rsrp, float rsrq, float sinr)
        {
            setNormalIndexData(rsrp, rsrq, sinr);
        }

        public void Caculate()
        {
            AvgRsrp = caculateAvg(SampleCount, rsrpSum);
            AvgSinr = caculateAvg(SampleCount, sinrSum);
            AvgRsrq = caculateAvg(SampleCount, rsrqSum);
            AvgDistance = caculateAvg(SampleCount, distanceSum);
        }

        protected double? caculateAvg(int count, double sum)
        {
            double? res = null;
            if (count > 0)
            {
                res = Math.Round(sum / count, 2);
            }
            return res;
        }
    }

    /// <summary>
    /// 查询统计信息
    /// </summary>
    public class QueryStatistics
    {
        /// <summary>
        /// 查询开始时间
        /// </summary>
        public DateTime QueryStartTime { get; set; }

        /// <summary>
        /// 查询结束时间
        /// </summary>
        public DateTime QueryEndTime { get; set; }

        /// <summary>
        /// 查询时间
        /// </summary>
        public DateTime QueryTime { get; set; }

        /// <summary>
        /// 处理耗时（毫秒）
        /// </summary>
        public long ProcessingTimeMs { get; set; }

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalRecords { get; set; }

        /// <summary>
        /// 处理的测试点总数
        /// </summary>
        public long TotalTestPoints { get; set; }

        /// <summary>
        /// 有效测试点数
        /// </summary>
        public long ValidTestPoints { get; set; }

        /// <summary>
        /// 无效测试点数
        /// </summary>
        public long InvalidTestPoints { get; set; }

        /// <summary>
        /// 匹配到小区的测试点数
        /// </summary>
        public long MatchedTestPoints { get; set; }

        /// <summary>
        /// 未匹配到小区的测试点数
        /// </summary>
        public long UnmatchedTestPoints { get; set; }

        /// <summary>
        /// 查询的区域数量
        /// </summary>
        public int RegionCount { get; set; }

        /// <summary>
        /// 查询的文件数量
        /// </summary>
        public int FileCount { get; set; }

        /// <summary>
        /// 发现的小区总数
        /// </summary>
        public int TotalCellCount { get; set; }

        /// <summary>
        /// 匹配到工参的小区数
        /// </summary>
        public int MatchedCellCount { get; set; }

        /// <summary>
        /// 未匹配到工参的小区数
        /// </summary>
        public int UnmatchedCellCount { get; set; }

        /// <summary>
        /// 数据覆盖率（匹配测试点数/总测试点数）
        /// </summary>
        public double DataCoverageRate => TotalTestPoints > 0 ? (double)MatchedTestPoints / TotalTestPoints : 0;

        /// <summary>
        /// 小区匹配率（匹配小区数/总小区数）
        /// </summary>
        public double CellMatchRate => TotalCellCount > 0 ? (double)MatchedCellCount / TotalCellCount : 0;

        /// <summary>
        /// 查询条件摘要
        /// </summary>
        public string QueryConditionSummary { get; set; }

        /// <summary>
        /// 错误信息列表
        /// </summary>
        public List<string> ErrorMessages { get; set; } = new List<string>();

        /// <summary>
        /// 警告信息列表
        /// </summary>
        public List<string> WarningMessages { get; set; } = new List<string>();

        /// <summary>
        /// 处理速度（测试点数/秒）
        /// </summary>
        public double ProcessingSpeed => ProcessingTimeMs > 0 ? (double)TotalTestPoints / (ProcessingTimeMs / 1000.0) : 0;
    }

    /// <summary>
    /// 未匹配小区信息DTO
    /// </summary>
    public class UnmatchedCellInfoDto
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int SN { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 小区标识（ARFCN_PCI格式）
        /// </summary>
        public string CellIdentifier { get; set; }

        /// <summary>
        /// TAC
        /// </summary>
        public int? Tac { get; set; }

        /// <summary>
        /// NCI
        /// </summary>
        public long? Nci { get; set; }

        /// <summary>
        /// ARFCN
        /// </summary>
        public int Arfcn { get; set; }

        /// <summary>
        /// PCI
        /// </summary>
        public int Pci { get; set; }

        /// <summary>
        /// 采样点数
        /// </summary>
        public int SampleCount { get; set; }

        /// <summary>
        /// 采样点总数
        /// </summary>
        public long SampleTotalCount { get; set; }

        /// <summary>
        /// 采样点占比
        /// </summary>
        public float SampleCountRatio { get; set; }

        /// <summary>
        /// RSRP信号质量指标
        /// </summary>
        public SignalQualityMetrics RsrpMetrics { get; set; } = new SignalQualityMetrics();

        /// <summary>
        /// RSRQ信号质量指标
        /// </summary>
        public SignalQualityMetrics RsrqMetrics { get; set; } = new SignalQualityMetrics();

        /// <summary>
        /// SINR信号质量指标
        /// </summary>
        public SignalQualityMetrics SinrMetrics { get; set; } = new SignalQualityMetrics();

        /// <summary>
        /// 首次发现时间
        /// </summary>
        public DateTime? FirstSeenTime { get; set; }

        /// <summary>
        /// 最后发现时间
        /// </summary>
        public DateTime? LastSeenTime { get; set; }

        /// <summary>
        /// 发现的区域列表
        /// </summary>
        public List<string> DiscoveredRegions { get; set; } = new List<string>();

        /// <summary>
        /// 可能的小区名称（基于邻近工参小区推测）
        /// </summary>
        public string PossibleCellName { get; set; }

        /// <summary>
        /// 可能的基站名称
        /// </summary>
        public string PossibleBtsName { get; set; }

        /// <summary>
        /// 推测的经度（基于测试点位置）
        /// </summary>
        public double? EstimatedLongitude { get; set; }

        /// <summary>
        /// 推测的纬度（基于测试点位置）
        /// </summary>
        public double? EstimatedLatitude { get; set; }

        /// <summary>
        /// 信号覆盖范围（公里）
        /// </summary>
        public double? CoverageRange { get; set; }

        /// <summary>
        /// 是否为疑似新建小区
        /// </summary>
        public bool IsSuspectedNewCell { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string Remarks { get; set; }

        /// <summary>
        /// 相关测试点位置信息
        /// </summary>
        public List<TestPointLocationInfo> TestPointLocations { get; set; } = new List<TestPointLocationInfo>();
    }

    /// <summary>
    /// 信号质量指标
    /// </summary>
    public class SignalQualityMetrics
    {
        /// <summary>
        /// 最小值
        /// </summary>
        public float Min { get; set; }

        /// <summary>
        /// 最大值
        /// </summary>
        public float Max { get; set; }

        /// <summary>
        /// 平均值
        /// </summary>
        public double? Avg { get; set; }

        /// <summary>
        /// 标准差
        /// </summary>
        public double? StandardDeviation { get; set; }

        /// <summary>
        /// 中位数
        /// </summary>
        public double? Median { get; set; }

        /// <summary>
        /// 95%分位数
        /// </summary>
        public double? Percentile95 { get; set; }

        /// <summary>
        /// 样本数量
        /// </summary>
        public int SampleCount { get; set; }
    }

    /// <summary>
    /// 测试点位置信息
    /// </summary>
    public class TestPointLocationInfo
    {
        /// <summary>
        /// 经度
        /// </summary>
        public double Longitude { get; set; }

        /// <summary>
        /// 纬度
        /// </summary>
        public double Latitude { get; set; }

        /// <summary>
        /// 测试时间
        /// </summary>
        public DateTime TestTime { get; set; }

        /// <summary>
        /// RSRP值
        /// </summary>
        public float Rsrp { get; set; }

        /// <summary>
        /// RSRQ值
        /// </summary>
        public float Rsrq { get; set; }

        /// <summary>
        /// SINR值
        /// </summary>
        public float Sinr { get; set; }

        /// <summary>
        /// 区域名称
        /// </summary>
        public string RegionName { get; set; }
    }
}
