﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NrCellInfo
    {
        public NRCell Cell { get; set; }
        public int SN { get; set; }
        public string FileName { get; set; }
        public string CellName { get; set; }
        public int? Tac { get; set; }
        public long? Nci { get; set; }
        public int Arfcn { get; set; }
        public int Pci { get; set; }
        public int CellID { get; set; }

        public string StrCellID
        {
            get
            {
                if (CellID == 0)
                {
                    return "";
                }
                else
                {
                    return CellID.ToString();
                }
            }
        }
        public string IndoorOrOutdoor { get; set; }
        public int SampleCount { get; set; }
        public long SampleTotalCount { get; set; }
        public float SampleCountRatio { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public float MinRsrp { get; set; }
        public float MaxRsrp { get; set; }
        public double? AvgRsrp { get; set; } = 0;

        public float MinRsrq { get; set; }
        public float MaxRsrq { get; set; }
        public double? AvgRsrq { get; set; } = 0;

        public double MinDistance { get; set; }
        public double MaxDistance { get; set; }
        public double? AvgDistance { get; set; } = 0;

        public float MinSinr { get; set; }
        public float MaxSinr { get; set; }
        public double? AvgSinr { get; set; } = 0;

        public string Grid { get; set; }
        public string CellProperty { get; set; }
        public string CellCode { get; set; }

        protected float rsrpSum = 0;
        protected float sinrSum = 0;
        protected float rsrqSum = 0;
        protected double distanceSum = 0;

        public void AddCellInfo(NRCell nrCell, TestPoint tp, float rsrp, float rsrq, float sinr)
        {
            setNormalIndexData(rsrp, rsrq, sinr);
            FileName = tp.FileName;

            double distance = Math.Round(MathFuncs.GetDistance(tp.Longitude, tp.Latitude, nrCell.Longitude, nrCell.Latitude), 2);
            if (MinDistance > distance)
                MinDistance = distance;
            else if (MaxDistance < distance)
                MaxDistance = distance;
            distanceSum += distance;
        }

        public void AddCellInfo(string fileName, float rsrp, float rsrq, float sinr)
        {
            setNormalIndexData(rsrp, rsrq, sinr);
            FileName = fileName;
        }

        private void setNormalIndexData(float rsrp, float rsrq, float sinr)
        {
            SampleCount++;

            if (MinRsrp > rsrp)
                MinRsrp = rsrp;
            else if (MaxRsrp < rsrp)
                MaxRsrp = rsrp;
            rsrpSum += rsrp;

            if (MinRsrq > rsrq)
                MinRsrq = rsrq;
            else if (MaxRsrq < rsrq)
                MaxRsrq = rsrq;
            rsrqSum += rsrq;

            if (MinSinr > sinr)
                MinSinr = sinr;
            else if (MaxSinr < sinr)
                MaxSinr = sinr;
            sinrSum += sinr;
        }

        public void AddNoCellInfo(float rsrp, float rsrq, float sinr)
        {
            setNormalIndexData(rsrp, rsrq, sinr);
        }

        public void Caculate()
        {
            AvgRsrp = caculateAvg(SampleCount, rsrpSum);
            AvgSinr = caculateAvg(SampleCount, sinrSum);
            AvgRsrq = caculateAvg(SampleCount, rsrqSum);
            AvgDistance = caculateAvg(SampleCount, distanceSum);
        }

        protected double? caculateAvg(int count, double sum)
        {
            double? res = null;
            if (count > 0)
            {
                res = Math.Round(sum / count, 2);
            }
            return res;
        }
    }
}
