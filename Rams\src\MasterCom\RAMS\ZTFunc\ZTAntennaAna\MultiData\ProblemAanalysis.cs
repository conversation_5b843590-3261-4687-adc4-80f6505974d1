﻿using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Reflection;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ProblemAanalysis
    {
        private static ProblemAanalysis instance = null;
        public static ProblemAanalysis GetInstance()
        {
            if (instance == null)
            {
                instance = new ProblemAanalysis();
            }
            return instance;
        }

        #region 全局变量
        private string strCityName { get; set; } = "";
        private Dictionary<string, string> classValueDic = new Dictionary<string, string>();//获取路测及扫频指标
        private List<StatIndexProblem> statList = new List<StatIndexProblem>();//问题分析指标
        private bool iIndoorType { get; set; } = false;
        private readonly DataTable eval = new DataTable();
        #endregion
        public void init(List<StatIndexProblem> statList, string strCityName)
        {
            this.strCityName = strCityName;
            this.statList = statList;
        }

        public void Aanalysis(ref ZTLteAntParaCommon.CellParaData cellPara, ref int[] probStat)
        {
            classValueDic = new Dictionary<string, string>();
            getNameAndValue(cellPara.antInfoItem, "扫频");
            getNameAndValue(cellPara.ciopItem, "路测");
            getNameAndValue(cellPara.scanR0R1Item, "扫频");
            getNameAndValue(cellPara.lteMRCoverItem, "测量");
            getNameAndValue(cellPara.cellPara, "统计");
            getNameAndValue(cellPara.cellParaRank, "统计");
            iIndoorType = false;
            if (cellPara.antCfg.strBtsType.Contains("E") || cellPara.antCfg.strType.Contains("室内"))
                iIndoorType = true;
           
            StringBuilder probStatus = new StringBuilder(cellPara.strProbStatus);
            foreach (StatIndexProblem stat in statList)
            {
                try
                {
                    if (!stat.pChecked)//不勾选的不统计
                        continue;
                    bool istrue = judgeIstrue(cellPara, probStatus, stat);

                    bool isEnd = judgeIndoor(cellPara, ref probStatus, stat, istrue);
                    if (isEnd)
                    {
                        return;
                    }
                }
                catch (Exception ex)
                {
                    probStatus.Append("判断" + stat.pName + "出错！" + ex.Message);
                }
            }
            cellPara.strProbStatus = probStatus.ToString();
            analysysAdditional(ref cellPara, ref probStat);
        }

        private bool judgeIstrue(ZTLteAntParaCommon.CellParaData cellPara, StringBuilder probStatus, StatIndexProblem stat)
        {
            bool istrue = false;
            StringBuilder probType = new StringBuilder(cellPara.strProbType);
            foreach (StatIndexCondition statIndex in stat.conditionList)
            {
                string newFormula = reBuildFormula(statIndex.Formula);
                object result = eval.Compute(newFormula, "");
                if (result.ToString().ToLower().Contains("true"))//MyEvaluator.EvaluateToBool(newFormula)
                {
                    probStatus.Append(statIndex.tip + ";");
                    probType.Append(stat.pName + ";");
                    istrue = true;
                    break;
                }
            }
            cellPara.strProbType = probType.ToString();
            return istrue;
        }

        private bool judgeIndoor(ZTLteAntParaCommon.CellParaData cellPara, ref StringBuilder probStatus, StatIndexProblem stat, bool istrue)
        {
            if (istrue)
            {
                if (stat.ptype == "室分")
                {
                    if (iIndoorType)//判断是否为室分泄露
                        return true;
                    else
                    {
                        probStatus = new StringBuilder();
                        cellPara.strProbType = "";
                    }
                }
                if (stat.ptype == "返回")
                    return true;
            }
            return false;
        }

        /// <summary>
        /// 重组公式
        /// </summary>
        private string reBuildFormula(string p)
        {
            //[路测_采样点数]>=102&&[路测_LTE覆盖率(RSRP≥-110)]>=90%||[路测_小区平均RSRP0]<=-5
            string str1 = p.Replace("%", "");
            char[] char1 = new char[] { '[', ']' };
            string[] str2 = str1.Split(char1);
            StringBuilder famula = new StringBuilder();
            for (int i = 0; i < str2.Length; i++)
            {
                if (i % 2 != 0)
                {
                    if (classValueDic[str2[i]].Contains("是") || classValueDic[str2[i]].Contains("否"))
                    {
                        famula.Append("'" + classValueDic[str2[i]] + "'");
                    }
                    else
                        famula.Append(classValueDic[str2[i]]);
                }
                else
                    famula.Append(str2[i]);
            }
            return famula.ToString();
        }

        /// <summary>
        /// 额外统计
        /// </summary>
        private void analysysAdditional(ref ZTLteAntParaCommon.CellParaData cellPara, ref int[] probStat)
        {
            //
        }

        /// <summary>
        /// 初始化类信息
        /// </summary>
        private void getNameAndValue<T>(T t,string info)
        {
            if (t == null)
                return;
            System.Reflection.PropertyInfo[] properties = t.GetType().GetProperties(System.Reflection.BindingFlags.Instance 
                | System.Reflection.BindingFlags.Public);
            if (properties.Length <= 0)
                return;
            foreach (System.Reflection.PropertyInfo item in properties)
            {
                object[] objs = item.GetCustomAttributes(typeof(DescriptionAttribute), true);
                if (objs.Length <= 0)
                    continue;
                string des = ((DescriptionAttribute)objs[0]).Description.ToString();
                //string name = item.Name; //名称
                object value = item.GetValue(t, null);  //值
                if (item.PropertyType.IsValueType || item.PropertyType.Name.StartsWith("String"))
                {
                    classValueDic[info + "_" + des] = value.ToString();
                }
            }
        }

        /// <summary>
        /// 检查公式是否可用
        /// </summary>
        /// <param name="p"></param>
        /// <returns></returns>
        internal bool CheckFormula(string p)
        {
            //[路测_采样点数]>=102&&[路测_LTE覆盖率(RSRP≥-110)]>=90%||[路测_小区平均RSRP0]<=-5
            try
            {
                string str1 = p.Replace("%", "").Replace("'是'", "1").Replace("'否'", "1");
                char[] char1 = new char[] { '[', ']' };
                string[] str2 = str1.Split(char1);
                StringBuilder famula = new StringBuilder();

                for (int i = 0; i < str2.Length; i++)
                {
                    if (str2[i] == "" && i != 0 && i != str2.Length - 1)
                        return false;
                    if (i % 2 != 0)
                        famula.Append(1);
                    else
                        famula.Append(str2[i]);
                }
                if (famula.Length < 3)
                    return false;
                eval.Compute(famula.ToString(), "");
            }
            catch (Exception)
            {
                return false;
            }
            return true;
        }
    }
}
