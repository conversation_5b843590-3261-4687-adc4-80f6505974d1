﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ScanOverlapCellForm : MinCloseForm
    {
        public ScanOverlapCellForm(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        private List<OverlapSubCellInfo> dataSource = null;
        public void FillData(List<OverlapCellInfo> overlapCells)
        {
            gridControl.DataSource = dataSource;
        }

        private List<ScanOverlapPoint> dataSrc = null;
        public void FillData(List<ScanOverlapPoint> overlapPnts)
        {
            gridControlPnt.BeginUpdate();
            dataSrc = overlapPnts;
            gridControlPnt.DataSource = overlapPnts;
            gridControlPnt.EndUpdate();
        }
        

        public void FillData(List<OverlapSubCellInfo> subCellInfoList)
        {
            dataSource=subCellInfoList;
            gridControlNew.DataSource = dataSource;
        }

        

        private void checkEditShowOvrlLine_CheckedChanged(object sender, EventArgs e)
        {
            MainModel.ShowOverlapLine = checkEditShowOvrlLine.Checked;
            MainModel.MainForm.GetMapForm().updateMap();
        }

        private void checkEditShowNearestLine_CheckedChanged(object sender, EventArgs e)
        {
            MainModel.ShowNearestLine = checkEditShowNearestLine.Checked;
            MainModel.MainForm.GetMapForm().updateMap();
        }

        private void btnGIS_Click(object sender, EventArgs e)
        {
            splitContainerControl1.Collapsed = !splitContainerControl1.Collapsed;
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            int[] selRows = gridView.GetSelectedRows();
            if (selRows.Length>0)
            {
                OverlapCellInfo ovlCell = gridView.GetRow(selRows[0]) as OverlapCellInfo;
                if (ovlCell != null)
                {
                    MainModel.DTDataManager.Clear();
                    foreach (TestPoint tp in ovlCell.TestPoints)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                    MainModel.FireDTDataChanged(this);

                    MainModel.CurOverlapInfoList = new List<OverlapSubCellInfo>();
                    MainModel.CurOverlapInfoList.AddRange(ovlCell.SubCells);
                    MainModel.MainForm.GetMapForm().updateMap();
                }
            }
        }

        private void gridViewSub_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView view = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            int[] selRows = view.GetSelectedRows();
            if (selRows.Length > 0)
            {
                OverlapSubCellInfo subCell = view.GetRow(selRows[0]) as OverlapSubCellInfo;
                if (subCell != null)
                {
                    MainModel.DTDataManager.Clear();
                    foreach (TestPoint tp in subCell.TestPoints)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                    MainModel.FireDTDataChanged(this);

                    MainModel.CurOverlapInfoList = new List<OverlapSubCellInfo>();
                    MainModel.CurOverlapInfoList.Add(subCell);
                    MainModel.MainForm.GetMapForm().updateMap();
                }
            }
        }

        private void btnShowAll_Click(object sender, EventArgs e)
        {
            MainModel.CurOverlapInfoList.Clear();
            MainModel.DTDataManager.Clear();
            MainModel.CurOverlapPointList = dataSrc;
            foreach (ScanOverlapPoint pnt in dataSrc)
            {
                MainModel.DTDataManager.Add(pnt.Point);
            }
            MainModel.FireDTDataChanged(this);
            try
            {
                MainModel.MainForm.GetMapForm().updateMap();
            }
            catch
            {
                //continue
            }
        }

        private void miExp2Xls_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(gridViewPnt);
        }

        private void gridViewPnt_DoubleClick(object sender, EventArgs e)
        {
            int[] selRows = gridViewPnt.GetSelectedRows();
            if (selRows.Length > 0)
            {
                ScanOverlapPoint pnt = gridViewPnt.GetRow(selRows[0]) as ScanOverlapPoint;
                if (pnt != null)
                {
                    MainModel.DTDataManager.Clear();
                    MainModel.DTDataManager.Add(pnt.Point);
                    MainModel.CurOverlapPointList = new List<ScanOverlapPoint>();
                    MainModel.CurOverlapPointList.Add(pnt);
                    MainModel.FireDTDataChanged(this);
                    try
                    {
                        MainModel.MainForm.GetMapForm().GoToView(pnt.Point.Longitude, pnt.Point.Latitude,8000);
                        MainModel.MainForm.GetMapForm().updateMap();
                    }
                    catch
                    {
                        //continue
                    }
                }
            }
        }
    }
}
