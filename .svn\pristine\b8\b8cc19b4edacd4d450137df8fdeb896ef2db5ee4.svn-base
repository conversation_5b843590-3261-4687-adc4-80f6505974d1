﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment
{
    public interface IProblemData
    {
        List<string> Cells
        {
            get;
        }
        List<TestPoint> TestPoints
        {
            get;
        }
        List<Event> Events
        {
            get;
        }
        float? RSRPMin
        {
            get;
        }
        float? RSRPMax
        {
            get;
        }
        float? RSRPAvg
        {
            get;
        }

        float? SINRMin { get; }

        float? SINRMax { get; }

        float? SINRAvg { get; }
        string RoadDesc { get; }
        void MakeSummary();
    }
}
