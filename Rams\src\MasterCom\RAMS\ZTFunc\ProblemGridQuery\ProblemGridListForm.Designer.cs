﻿namespace MasterCom.RAMS.ZTFunc.ProblemGridQuery
{
    partial class ProblemGridListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tabCtrl = new DevExpress.XtraTab.XtraTabControl();
            this.pageGSM = new DevExpress.XtraTab.XtraTabPage();
            this.lvGSM = new BrightIdeasSoftware.TreeListView();
            this.olvColumn1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.pageLTECover = new DevExpress.XtraTab.XtraTabPage();
            this.lvLTECvr = new BrightIdeasSoftware.TreeListView();
            this.olvColumn2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.pageLTEDownload = new DevExpress.XtraTab.XtraTabPage();
            this.lvLTEDl = new BrightIdeasSoftware.TreeListView();
            this.olvColumn3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.pageLTEUpload = new DevExpress.XtraTab.XtraTabPage();
            this.lvLTEUl = new BrightIdeasSoftware.TreeListView();
            this.olvColumn4 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShowAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.miExportXls = new System.Windows.Forms.ToolStripMenuItem();
            this.pageMOS = new DevExpress.XtraTab.XtraTabPage();
            this.lvMOS = new BrightIdeasSoftware.TreeListView();
            this.olvColumn5 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            ((System.ComponentModel.ISupportInitialize)(this.tabCtrl)).BeginInit();
            this.tabCtrl.SuspendLayout();
            this.pageGSM.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvGSM)).BeginInit();
            this.pageLTECover.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvLTECvr)).BeginInit();
            this.pageLTEDownload.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvLTEDl)).BeginInit();
            this.pageLTEUpload.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvLTEUl)).BeginInit();
            this.ctxMenu.SuspendLayout();
            this.pageMOS.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvMOS)).BeginInit();
            this.SuspendLayout();
            // 
            // tabCtrl
            // 
            this.tabCtrl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabCtrl.Location = new System.Drawing.Point(0, 0);
            this.tabCtrl.Name = "tabCtrl";
            this.tabCtrl.SelectedTabPage = this.pageGSM;
            this.tabCtrl.Size = new System.Drawing.Size(1057, 329);
            this.tabCtrl.TabIndex = 0;
            this.tabCtrl.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.pageGSM,
            this.pageLTECover,
            this.pageLTEDownload,
            this.pageLTEUpload,
            this.pageMOS});
            // 
            // pageGSM
            // 
            this.pageGSM.Controls.Add(this.lvGSM);
            this.pageGSM.Name = "pageGSM";
            this.pageGSM.Size = new System.Drawing.Size(1050, 299);
            this.pageGSM.Text = "GSM问题";
            // 
            // lvGSM
            // 
            this.lvGSM.Activation = System.Windows.Forms.ItemActivation.OneClick;
            this.lvGSM.AllColumns.Add(this.olvColumn1);
            this.lvGSM.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumn1});
            this.lvGSM.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvGSM.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lvGSM.FullRowSelect = true;
            this.lvGSM.GridLines = true;
            this.lvGSM.HeaderWordWrap = true;
            this.lvGSM.IsNeedShowOverlay = false;
            this.lvGSM.Location = new System.Drawing.Point(0, 0);
            this.lvGSM.Name = "lvGSM";
            this.lvGSM.OwnerDraw = true;
            this.lvGSM.ShowGroups = false;
            this.lvGSM.ShowItemToolTips = true;
            this.lvGSM.Size = new System.Drawing.Size(1050, 299);
            this.lvGSM.TabIndex = 6;
            this.lvGSM.UseCompatibleStateImageBehavior = false;
            this.lvGSM.View = System.Windows.Forms.View.Details;
            this.lvGSM.VirtualMode = true;
            // 
            // olvColumn1
            // 
            this.olvColumn1.HeaderFont = null;
            // 
            // pageLTECover
            // 
            this.pageLTECover.Controls.Add(this.lvLTECvr);
            this.pageLTECover.Name = "pageLTECover";
            this.pageLTECover.Size = new System.Drawing.Size(1050, 299);
            this.pageLTECover.Text = "LTE覆盖";
            // 
            // lvLTECvr
            // 
            this.lvLTECvr.Activation = System.Windows.Forms.ItemActivation.OneClick;
            this.lvLTECvr.AllColumns.Add(this.olvColumn2);
            this.lvLTECvr.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumn2});
            this.lvLTECvr.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvLTECvr.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lvLTECvr.FullRowSelect = true;
            this.lvLTECvr.GridLines = true;
            this.lvLTECvr.HeaderWordWrap = true;
            this.lvLTECvr.IsNeedShowOverlay = false;
            this.lvLTECvr.Location = new System.Drawing.Point(0, 0);
            this.lvLTECvr.Name = "lvLTECvr";
            this.lvLTECvr.OwnerDraw = true;
            this.lvLTECvr.ShowGroups = false;
            this.lvLTECvr.ShowItemToolTips = true;
            this.lvLTECvr.Size = new System.Drawing.Size(1050, 299);
            this.lvLTECvr.TabIndex = 7;
            this.lvLTECvr.UseCompatibleStateImageBehavior = false;
            this.lvLTECvr.View = System.Windows.Forms.View.Details;
            this.lvLTECvr.VirtualMode = true;
            // 
            // olvColumn2
            // 
            this.olvColumn2.HeaderFont = null;
            // 
            // pageLTEDownload
            // 
            this.pageLTEDownload.Controls.Add(this.lvLTEDl);
            this.pageLTEDownload.Name = "pageLTEDownload";
            this.pageLTEDownload.Size = new System.Drawing.Size(1050, 299);
            this.pageLTEDownload.Text = "LTE下载";
            // 
            // lvLTEDl
            // 
            this.lvLTEDl.Activation = System.Windows.Forms.ItemActivation.OneClick;
            this.lvLTEDl.AllColumns.Add(this.olvColumn3);
            this.lvLTEDl.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumn3});
            this.lvLTEDl.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvLTEDl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lvLTEDl.FullRowSelect = true;
            this.lvLTEDl.GridLines = true;
            this.lvLTEDl.HeaderWordWrap = true;
            this.lvLTEDl.IsNeedShowOverlay = false;
            this.lvLTEDl.Location = new System.Drawing.Point(0, 0);
            this.lvLTEDl.Name = "lvLTEDl";
            this.lvLTEDl.OwnerDraw = true;
            this.lvLTEDl.ShowGroups = false;
            this.lvLTEDl.ShowItemToolTips = true;
            this.lvLTEDl.Size = new System.Drawing.Size(1050, 299);
            this.lvLTEDl.TabIndex = 8;
            this.lvLTEDl.UseCompatibleStateImageBehavior = false;
            this.lvLTEDl.View = System.Windows.Forms.View.Details;
            this.lvLTEDl.VirtualMode = true;
            // 
            // olvColumn3
            // 
            this.olvColumn3.HeaderFont = null;
            // 
            // pageLTEUpload
            // 
            this.pageLTEUpload.Controls.Add(this.lvLTEUl);
            this.pageLTEUpload.Name = "pageLTEUpload";
            this.pageLTEUpload.Size = new System.Drawing.Size(1050, 299);
            this.pageLTEUpload.Text = "LTE上传";
            // 
            // lvLTEUl
            // 
            this.lvLTEUl.Activation = System.Windows.Forms.ItemActivation.OneClick;
            this.lvLTEUl.AllColumns.Add(this.olvColumn4);
            this.lvLTEUl.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumn4});
            this.lvLTEUl.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvLTEUl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lvLTEUl.FullRowSelect = true;
            this.lvLTEUl.GridLines = true;
            this.lvLTEUl.HeaderWordWrap = true;
            this.lvLTEUl.IsNeedShowOverlay = false;
            this.lvLTEUl.Location = new System.Drawing.Point(0, 0);
            this.lvLTEUl.Name = "lvLTEUl";
            this.lvLTEUl.OwnerDraw = true;
            this.lvLTEUl.ShowGroups = false;
            this.lvLTEUl.ShowItemToolTips = true;
            this.lvLTEUl.Size = new System.Drawing.Size(1050, 299);
            this.lvLTEUl.TabIndex = 9;
            this.lvLTEUl.UseCompatibleStateImageBehavior = false;
            this.lvLTEUl.View = System.Windows.Forms.View.Details;
            this.lvLTEUl.VirtualMode = true;
            // 
            // olvColumn4
            // 
            this.olvColumn4.HeaderFont = null;
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShowAll,
            this.toolStripSeparator1,
            this.miExpandAll,
            this.miCollapseAll,
            this.toolStripSeparator2,
            this.miExportXls});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(149, 104);
            // 
            // miShowAll
            // 
            this.miShowAll.Name = "miShowAll";
            this.miShowAll.Size = new System.Drawing.Size(148, 22);
            this.miShowAll.Text = "渲染所有栅格";
            this.miShowAll.Click += new System.EventHandler(this.miShowAll_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(145, 6);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(148, 22);
            this.miExpandAll.Text = "展开所有节点";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(148, 22);
            this.miCollapseAll.Text = "收缩所有节点";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(145, 6);
            // 
            // miExportXls
            // 
            this.miExportXls.Name = "miExportXls";
            this.miExportXls.Size = new System.Drawing.Size(148, 22);
            this.miExportXls.Text = "导出Excel...";
            this.miExportXls.Click += new System.EventHandler(this.miExportXls_Click);
            // 
            // pageMOS
            // 
            this.pageMOS.Controls.Add(this.lvMOS);
            this.pageMOS.Name = "pageMOS";
            this.pageMOS.Size = new System.Drawing.Size(1050, 299);
            this.pageMOS.Text = "VoLTE-MOS";
            // 
            // lvMOS
            // 
            this.lvMOS.Activation = System.Windows.Forms.ItemActivation.OneClick;
            this.lvMOS.AllColumns.Add(this.olvColumn5);
            this.lvMOS.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumn5});
            this.lvMOS.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvMOS.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lvMOS.FullRowSelect = true;
            this.lvMOS.GridLines = true;
            this.lvMOS.HeaderWordWrap = true;
            this.lvMOS.IsNeedShowOverlay = false;
            this.lvMOS.Location = new System.Drawing.Point(0, 0);
            this.lvMOS.Name = "lvMOS";
            this.lvMOS.OwnerDraw = true;
            this.lvMOS.ShowGroups = false;
            this.lvMOS.ShowItemToolTips = true;
            this.lvMOS.Size = new System.Drawing.Size(1050, 299);
            this.lvMOS.TabIndex = 10;
            this.lvMOS.UseCompatibleStateImageBehavior = false;
            this.lvMOS.View = System.Windows.Forms.View.Details;
            this.lvMOS.VirtualMode = true;
            // 
            // olvColumn5
            // 
            this.olvColumn5.HeaderFont = null;
            // 
            // ProblemGridListForm
            // 
            this.ClientSize = new System.Drawing.Size(1057, 329);
            this.ContextMenuStrip = this.ctxMenu;
            this.Controls.Add(this.tabCtrl);
            this.Name = "ProblemGridListForm";
            this.Text = "问题栅格列表";
            ((System.ComponentModel.ISupportInitialize)(this.tabCtrl)).EndInit();
            this.tabCtrl.ResumeLayout(false);
            this.pageGSM.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lvGSM)).EndInit();
            this.pageLTECover.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lvLTECvr)).EndInit();
            this.pageLTEDownload.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lvLTEDl)).EndInit();
            this.pageLTEUpload.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lvLTEUl)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            this.pageMOS.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lvMOS)).EndInit();
            this.ResumeLayout(false);

        }

        private DevExpress.XtraTab.XtraTabControl tabCtrl;
        private DevExpress.XtraTab.XtraTabPage pageGSM;
        private DevExpress.XtraTab.XtraTabPage pageLTEUpload;
        private DevExpress.XtraTab.XtraTabPage pageLTECover;
        private DevExpress.XtraTab.XtraTabPage pageLTEDownload;

        #endregion
        private BrightIdeasSoftware.TreeListView lvGSM;
        private BrightIdeasSoftware.TreeListView lvLTECvr;
        private BrightIdeasSoftware.TreeListView lvLTEDl;
        private BrightIdeasSoftware.TreeListView lvLTEUl;
        private BrightIdeasSoftware.OLVColumn olvColumn1;
        private BrightIdeasSoftware.OLVColumn olvColumn2;
        private BrightIdeasSoftware.OLVColumn olvColumn3;
        private BrightIdeasSoftware.OLVColumn olvColumn4;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miShowAll;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem miExportXls;
        private DevExpress.XtraTab.XtraTabPage pageMOS;
        private BrightIdeasSoftware.TreeListView lvMOS;
        private BrightIdeasSoftware.OLVColumn olvColumn5;
    }
}