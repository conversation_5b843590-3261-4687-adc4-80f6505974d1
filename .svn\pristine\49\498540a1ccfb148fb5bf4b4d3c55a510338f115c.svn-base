﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTPrivateNetCellAnalysisQueryByRegion : ZTPrivateNetCellAnalysisQuery
    {
        private ZTPrivateNetCellAnalysisQueryByRegion()
            : base()
        {
            FilterSampleByRegion = true;
        }

        private static ZTPrivateNetCellAnalysisQueryByRegion instance = null;
        public new static ZTPrivateNetCellAnalysisQueryByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTPrivateNetCellAnalysisQueryByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "专网小区统计(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22124, this.Name);
        }
    }
}
