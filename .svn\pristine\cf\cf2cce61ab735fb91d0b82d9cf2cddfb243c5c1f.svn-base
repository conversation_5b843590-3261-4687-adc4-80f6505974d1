﻿namespace MasterCom.RAMS.CQT
{
    partial class CQTKPIColumnPanel
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.textEditName = new DevExpress.XtraEditors.TextEdit();
            this.grpSingle = new DevExpress.XtraEditors.GroupControl();
            this.radioScoreType = new DevExpress.XtraEditors.RadioGroup();
            this.scoreColorRangeSettingPanel = new MasterCom.RAMS.Util.ScoreColorRangeSettingPanel();
            this.seKpiRangeMax = new DevExpress.XtraEditors.SpinEdit();
            this.seScoreRangeMax = new DevExpress.XtraEditors.SpinEdit();
            this.seScoreRangeMin = new DevExpress.XtraEditors.SpinEdit();
            this.seKpiRangeMin = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.textBoxFormula = new System.Windows.Forms.TextBox();
            this.kpiFormulaEditor = new MasterCom.RAMS.Util.KPIFormulaEditor();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.textEditName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpSingle)).BeginInit();
            this.grpSingle.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioScoreType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.seKpiRangeMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.seScoreRangeMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.seScoreRangeMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.seKpiRangeMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(36, 34);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(36, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "列名：";
            // 
            // textEditName
            // 
            this.textEditName.Location = new System.Drawing.Point(78, 31);
            this.textEditName.Name = "textEditName";
            this.textEditName.Size = new System.Drawing.Size(177, 21);
            this.textEditName.TabIndex = 1;
            this.textEditName.EditValueChanged += new System.EventHandler(this.textEditName_EditValueChanged);
            // 
            // grpSingle
            // 
            this.grpSingle.Controls.Add(this.radioScoreType);
            this.grpSingle.Controls.Add(this.scoreColorRangeSettingPanel);
            this.grpSingle.Controls.Add(this.seKpiRangeMax);
            this.grpSingle.Controls.Add(this.seScoreRangeMax);
            this.grpSingle.Controls.Add(this.seScoreRangeMin);
            this.grpSingle.Controls.Add(this.seKpiRangeMin);
            this.grpSingle.Controls.Add(this.textEditName);
            this.grpSingle.Controls.Add(this.labelControl6);
            this.grpSingle.Controls.Add(this.labelControl5);
            this.grpSingle.Controls.Add(this.labelControl2);
            this.grpSingle.Controls.Add(this.labelControl7);
            this.grpSingle.Controls.Add(this.labelControl4);
            this.grpSingle.Controls.Add(this.labelControl1);
            this.grpSingle.Dock = System.Windows.Forms.DockStyle.Top;
            this.grpSingle.Location = new System.Drawing.Point(0, 0);
            this.grpSingle.MinimumSize = new System.Drawing.Size(654, 176);
            this.grpSingle.Name = "grpSingle";
            this.grpSingle.Size = new System.Drawing.Size(654, 176);
            this.grpSingle.TabIndex = 2;
            this.grpSingle.Text = "显示与打分";
            // 
            // radioScoreType
            // 
            this.radioScoreType.Location = new System.Drawing.Point(78, 121);
            this.radioScoreType.Name = "radioScoreType";
            this.radioScoreType.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "正向打分（指标值大分数高）"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "反向打分（指标值大分数低）")});
            this.radioScoreType.Size = new System.Drawing.Size(177, 49);
            this.radioScoreType.TabIndex = 4;
            this.radioScoreType.SelectedIndexChanged += new System.EventHandler(this.radioScoreType_SelectedIndexChanged);
            // 
            // scoreColorRangeSettingPanel
            // 
            this.scoreColorRangeSettingPanel.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.scoreColorRangeSettingPanel.IsSmoothScore = true;
            this.scoreColorRangeSettingPanel.Location = new System.Drawing.Point(270, 26);
            this.scoreColorRangeSettingPanel.Name = "scoreColorRangeSettingPanel";
            this.scoreColorRangeSettingPanel.Size = new System.Drawing.Size(370, 146);
            this.scoreColorRangeSettingPanel.TabIndex = 2;
            // 
            // seKpiRangeMax
            // 
            this.seKpiRangeMax.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.seKpiRangeMax.Location = new System.Drawing.Point(189, 61);
            this.seKpiRangeMax.Name = "seKpiRangeMax";
            this.seKpiRangeMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.seKpiRangeMax.Size = new System.Drawing.Size(66, 21);
            this.seKpiRangeMax.TabIndex = 3;
            // 
            // seScoreRangeMax
            // 
            this.seScoreRangeMax.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.seScoreRangeMax.Location = new System.Drawing.Point(189, 93);
            this.seScoreRangeMax.Name = "seScoreRangeMax";
            this.seScoreRangeMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.seScoreRangeMax.Size = new System.Drawing.Size(66, 21);
            this.seScoreRangeMax.TabIndex = 3;
            // 
            // seScoreRangeMin
            // 
            this.seScoreRangeMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.seScoreRangeMin.Location = new System.Drawing.Point(78, 93);
            this.seScoreRangeMin.Name = "seScoreRangeMin";
            this.seScoreRangeMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.seScoreRangeMin.Size = new System.Drawing.Size(66, 21);
            this.seScoreRangeMin.TabIndex = 3;
            // 
            // seKpiRangeMin
            // 
            this.seKpiRangeMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.seKpiRangeMin.Location = new System.Drawing.Point(78, 61);
            this.seKpiRangeMin.Name = "seKpiRangeMin";
            this.seKpiRangeMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.seKpiRangeMin.Size = new System.Drawing.Size(66, 21);
            this.seKpiRangeMin.TabIndex = 3;
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(12, 137);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(60, 14);
            this.labelControl6.TabIndex = 0;
            this.labelControl6.Text = "评分规则：";
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(15, 96);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(60, 14);
            this.labelControl5.TabIndex = 0;
            this.labelControl5.Text = "评分值域：";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(15, 65);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(60, 14);
            this.labelControl2.TabIndex = 0;
            this.labelControl2.Text = "指标值域：";
            // 
            // labelControl7
            // 
            this.labelControl7.Location = new System.Drawing.Point(155, 96);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(24, 14);
            this.labelControl7.TabIndex = 0;
            this.labelControl7.Text = "≤x≤";
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(155, 65);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(24, 14);
            this.labelControl4.TabIndex = 0;
            this.labelControl4.Text = "≤x≤";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.textBoxFormula);
            this.groupControl2.Controls.Add(this.kpiFormulaEditor);
            this.groupControl2.Controls.Add(this.labelControl3);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl2.Location = new System.Drawing.Point(0, 176);
            this.groupControl2.MinimumSize = new System.Drawing.Size(654, 412);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(654, 486);
            this.groupControl2.TabIndex = 3;
            this.groupControl2.Text = "指标公式(指标应该为纯数字，不能带单位，编辑公式时，请手工去掉“%”等单位)";
            // 
            // textBoxFormula
            // 
            this.textBoxFormula.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBoxFormula.Location = new System.Drawing.Point(57, 26);
            this.textBoxFormula.Multiline = true;
            this.textBoxFormula.Name = "textBoxFormula";
            this.textBoxFormula.Size = new System.Drawing.Size(583, 55);
            this.textBoxFormula.TabIndex = 1;
            this.textBoxFormula.TextChanged += new System.EventHandler(this.textBoxFormula_TextChanged_1);
            // 
            // kpiFormulaEditor
            // 
            this.kpiFormulaEditor.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.kpiFormulaEditor.Formula = "";
            this.kpiFormulaEditor.Location = new System.Drawing.Point(2, 87);
            this.kpiFormulaEditor.Name = "kpiFormulaEditor";
            this.kpiFormulaEditor.Size = new System.Drawing.Size(650, 397);
            this.kpiFormulaEditor.TabIndex = 0;
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(15, 26);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(36, 14);
            this.labelControl3.TabIndex = 0;
            this.labelControl3.Text = "公式：";
            // 
            // CQTKPIColumnPanel
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.grpSingle);
            this.Name = "CQTKPIColumnPanel";
            this.Size = new System.Drawing.Size(654, 662);
            ((System.ComponentModel.ISupportInitialize)(this.textEditName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpSingle)).EndInit();
            this.grpSingle.ResumeLayout(false);
            this.grpSingle.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioScoreType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.seKpiRangeMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.seScoreRangeMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.seScoreRangeMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.seKpiRangeMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.TextEdit textEditName;
        private DevExpress.XtraEditors.GroupControl grpSingle;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private global::MasterCom.RAMS.Util.ScoreColorRangeSettingPanel scoreColorRangeSettingPanel;
        private global::MasterCom.RAMS.Util.KPIFormulaEditor kpiFormulaEditor;
        private DevExpress.XtraEditors.SpinEdit seKpiRangeMin;
        private DevExpress.XtraEditors.SpinEdit seKpiRangeMax;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.RadioGroup radioScoreType;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit seScoreRangeMin;
        private DevExpress.XtraEditors.SpinEdit seScoreRangeMax;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private System.Windows.Forms.TextBox textBoxFormula;
    }
}
