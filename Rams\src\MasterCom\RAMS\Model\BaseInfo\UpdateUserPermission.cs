﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.BaseInfo
{
    public class UpdateUserPermission: DIYSQLBase
    {
        private readonly List<User> user2Update;
        public UpdateUserPermission(MainModel mainModel, List<User> user2Update)
            : base(mainModel)
        {
            this.user2Update = user2Update;
            MainDB = true;
        }
        protected override string getSqlTextString()
        {
            StringBuilder sb = new StringBuilder();
            foreach (User usr in user2Update)
            {
                sb.Append("delete from tb_cfg_static_user_role where user_id=" + usr.ID.ToString() + ";");
                foreach (int roleID in usr.FunctionRoleIDList)
                {
                    sb.Append("INSERT INTO tb_cfg_static_user_role (user_id, role_id) VALUES ("
                            + usr.ID.ToString() + "," + roleID.ToString() + ");");
                }
            }
            return sb.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                   //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        public override string Name
        {
            get { return "更新用户权限"; }
        }
    }
}
