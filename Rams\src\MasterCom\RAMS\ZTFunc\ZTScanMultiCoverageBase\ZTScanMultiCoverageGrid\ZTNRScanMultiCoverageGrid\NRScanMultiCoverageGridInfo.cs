﻿using MasterCom.MControls;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRScanMultiCoverageGridColorRanges : ScanMultiCoverageColorRanges
    {
        private static NRScanMultiCoverageGridColorRanges instance = null;
        public static NRScanMultiCoverageGridColorRanges Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new NRScanMultiCoverageGridColorRanges();
                }
                return instance;
            }
        }

        private NRScanMultiCoverageGridColorRanges()
        {
            initialize();
        }

        public override string RangeName { get { return "NRScanMultiCoverageGridColorRanges"; } }

        public float Minimum { get; set; } = -1000;
        public float Maximum { get; set; } = 1000;
        public Color InvalidColor { get; set; } = Color.Black;
        public string InvalidDesc { get; set; } = "无效栅格";

        protected override void initialize()
        {
            ColorRanges = new List<ColorRange>();
            ColorRanges.Add(new ColorRange(Minimum, 1, Color.Blue, "（-∞,1)"));
            ColorRanges.Add(new ColorRange(1, 2, Color.DarkSlateBlue, "[1,2)"));
            ColorRanges.Add(new ColorRange(2, 3, Color.DarkGreen, "[2,3)"));
            ColorRanges.Add(new ColorRange(3, 4, Color.MediumSeaGreen, "[3,4"));
            ColorRanges.Add(new ColorRange(4, 5, Color.Lime, "[4,5)"));
            ColorRanges.Add(new ColorRange(5, 6, Color.LawnGreen, "[5,6)"));
            ColorRanges.Add(new ColorRange(6, 7, Color.GreenYellow, "[6,7)"));
            ColorRanges.Add(new ColorRange(7, 8, Color.Yellow, "[7,8)"));
            ColorRanges.Add(new ColorRange(8, 9, Color.Orange, "[8,9)"));
            ColorRanges.Add(new ColorRange(9, 10, Color.DarkOrange, "[9,10)"));
            ColorRanges.Add(new ColorRange(10, Maximum, Color.Red, "[10,+∞)"));
        }

        public int GetIndex(float value)
        {
            if (value < ColorRanges[0].maxValue)
            {
                return 0;
            }
            if (value >= ColorRanges[ColorRanges.Count - 1].minValue)
            {
                return ColorRanges.Count - 1;
            }
            for (int i = 1; i < ColorRanges.Count - 1; ++i)
            {
                if (value >= ColorRanges[i].minValue && value < ColorRanges[i].maxValue)
                {
                    return i;
                }
            }

            return -1;
        }

        public Color GetColor(float value)
        {
            int idx = GetIndex(value);
            return idx <= 0 ? InvalidColor : ColorRanges[idx].color;
        }
    }

    public class NRScanMultiCoverageGridInfo : ScanMultiCoverageGridInfo
    {
        public NRScanMultiCoverageGridInfo(string mgrsString)
            : base(mgrsString)
        {
        }

        protected override MgrsTestPoint getPointInfo()
        {
            return new NRMgrsTestPoint();
        }

        protected override MgrsCell getCellInfo()
        {
            return new NRMgrsCell();
        }

        protected override ICell getCell(MgrsTestPoint curPoint, int index)
        {
            return CellManager.GetInstance().GetNearestNRCellByARFCNPCI(curPoint.Time
                , curPoint.CellInfoList[index].Arfcn, curPoint.CellInfoList[index].Pci
                , curPoint.Longitude, curPoint.Latitude);
        }

        protected override List<MgrsCell> getCoverageCell(ScanMultiCoverageCondition curCondition, List<MgrsCell> cellList)
        {
            List<MgrsCell> coverageCell = new List<MgrsCell>();
            if (cellList.Count == 0)
            {
                return coverageCell;
            }
            NRScanMultiCoverageGridCond condition = curCondition as NRScanMultiCoverageGridCond;

            var fstCell = cellList[0];
            if (fstCell.Rsrp.Avg < condition.AbsoluteValue)
            {
                return coverageCell;
            }
            double cmpValue;
            if (condition.EnableOptional)
            {
                cmpValue = Math.Max(fstCell.Rsrp.Avg - condition.CoverBandDiff, condition.ValidValue);
            }
            else
            {
                cmpValue = fstCell.Rsrp.Avg - condition.CoverBandDiff;
            }
           
            for (int i = 0; i < cellList.Count; ++i)
            {
                MgrsCell cell = cellList[i];
                if (cell.Rsrp.Avg >= cmpValue)
                {
                    ++Coverage;
                    coverageCell.Add(cell);
                }
            }
            return coverageCell;
        }
    }

    public class NRMgrsTestPoint : MgrsTestPoint
    {
        protected override void addCellInfo(TestPoint tp)
        {
            Dictionary<int, int> groupDic = NRTpHelper.NrScanTpManager.GetCellMaxBeam(tp);
            foreach (var index in groupDic.Values)
            {
                float? rsrp = NRTpHelper.NrScanTpManager.GetCellRsrp(tp, index);
                float? sinr = NRTpHelper.NrScanTpManager.GetCellSinr(tp, index);
                int? earfcn = (int?)NRTpHelper.NrScanTpManager.GetEARFCN(tp, index);
                int? pci = (int?)NRTpHelper.NrScanTpManager.GetPCI(tp, index);

                if (earfcn == null || pci == null || rsrp == null)
                {
                    break;
                }
                CellInfo info = new CellInfo()
                {
                    Arfcn = (int)earfcn,
                    Pci = (int)pci,
                    Rsrp = rsrp,
                    Sinr = sinr
                };
                CellInfoList.Add(info);
            }
        }
    }

    public class NRMgrsCell : MgrsCell
    {
        protected override void SetInfos()
        {
            NRCell cell = Cell as NRCell;
            Tac = cell.TAC.ToString();
            Ci = cell.NCI.ToString();
            Arfcn = cell.SSBARFCN;
            Pci = cell.PCI;
            CellName = cell.Name;
            Longitude = cell.Longitude;
            Latitude = cell.Latitude;
        }
    }
}
