﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func.OwnSampleAnalyse
{
    class SampleAnaDebugClassHerePlease
    {
        public bool CheckValidFunc(TestPoint tp, String str, object saver)
        {
            SampleAnaDebugClassHerePleaseSaver ss = saver as SampleAnaDebugClassHerePleaseSaver;
            if (ss != null)
            {
                TestPointDetail tpDetail = tp as TestPointDetail;
                if (tpDetail != null)
                {
                    short? rxlev = (short?)tpDetail["RxLevSub"];
                    if (rxlev != null && rxlev > -140 && rxlev < -10)
                    {
                        ss.count++;
                        if (rxlev < -90)
                        {
                            ss.badcount++;
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        public object PrepareResultSaver()
        {
            return new SampleAnaDebugClassHerePleaseSaver();
        }

        public bool ShowInListView(object saver, ListView lv)
        {
            SampleAnaDebugClassHerePleaseSaver ss = saver as SampleAnaDebugClassHerePleaseSaver;
            if (ss != null)
            {
                lv.Columns.Clear();
                lv.Columns.Add("总采样点数量");
                lv.Columns.Add("弱覆盖采样点数量");
                lv.Columns.Add("弱覆盖比例");
                ListViewItem listViewItem = new ListViewItem();
                listViewItem.Text = ss.count + "";
                listViewItem.SubItems.Add(ss.badcount + "");
                listViewItem.SubItems.Add(string.Format("{0:F2}%", ss.count > 0 ? (100f * ss.badcount / ss.count) : 0));
                lv.Items.Add(listViewItem);
            }

            return true;
        }
    }


    class SampleAnaDebugClassHerePleaseSaver
    {
        public int badcount { get; set; }
        public int count { get; set; }
    }
}
