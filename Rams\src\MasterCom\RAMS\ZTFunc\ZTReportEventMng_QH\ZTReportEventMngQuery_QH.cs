﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTReportEventMngQuery_QH : QueryBase
    {
        public ZTReportEventMngQuery_QH(MainModel mainModel)
            : base(mainModel)
        {
            thirdNameList = this.getThirdNameList(MainModel.User.LoginName);
        }

        private readonly List<ZTReportEventInfo_QH> reportEventInfoList = new List<ZTReportEventInfo_QH>();
        private readonly List<ZTReportEventInfo_QH> reportEventInfoArchivedList = new List<ZTReportEventInfo_QH>();

        private ReportEventCondition_QH reportEventCond = null;

        ZTReportEventStatSetForm_QH reportEventStatSetForm = null;

        public static List<string> thirdNameList { get; set; } = new List<string>();
        public static string nonThird { get; set; } = "0000";
        private bool getCondition()
        {
            if (reportEventStatSetForm == null)
            {
                reportEventStatSetForm = new ZTReportEventStatSetForm_QH(this.MainModel, 1);
            }

            if (reportEventStatSetForm.ShowDialog() == DialogResult.OK)
            {
                reportEventStatSetForm.GetCondition(out reportEventCond);
                reportEventCond.UserName = MainModel.User.LoginName;     //用户权限决定能看到的信息
            }
            else
            {
                return false;
            }

            return true;
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                if (!(ZTReportEventMngQuery_QH.thirdNameList.Count != 0
                && ZTReportEventMngQuery_QH.thirdNameList[0].Equals(ZTReportEventMngQuery_QH.nonThird)))
                {
                    bool isGet = getCondition();
                    if (!isGet)
                    {
                        return;
                    }
                }

                reportEventInfoList.Clear();
                reportEventInfoArchivedList.Clear();

                WaitBox.Show("开始查询异常事件...", queryInThread, clientProxy);
                fireShowFormAfterQuery();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected void queryInThread(object o)
        {
            try
            {
                List<int> selDistrictIDList = new List<int>();
                selDistrictIDList.Add(MainModel.DistrictID);
                if (MainModel.User.DBID == -1 && MainModel.MainForm.SelDistrictIDs.Count != 0)
                {
                    selDistrictIDList.Clear();
                    selDistrictIDList.AddRange(MainModel.MainForm.SelDistrictIDs);
                }

                foreach (int did in selDistrictIDList)
                {
                    reportEventInfoList.AddRange(getReportEventInfoList(reportEventCond, did));
                    reportEventInfoArchivedList.AddRange(getReportEventInfoArchivedList(reportEventCond, did));
                }
            }
            catch (Exception e)
            {
                ErrorInfo += e.Message;
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private List<string> getThirdNameList(string userName)
        {
            ZTSQLThirdList_QH query = new ZTSQLThirdList_QH(MainModel, userName);
            query.Query();

            return query.GetReportEventThirdNameList();
        }

        private List<ZTReportEventInfo_QH> getReportEventInfoList(ReportEventCondition_QH reportEventCond, int did)
        {
            ZTSQLReportEventInfoQuery_QH query = new ZTSQLReportEventInfoQuery_QH(MainModel, reportEventCond, did);
            query.Query();

            return query.GetReportEventInfoList();
        }

        private List<ZTReportEventInfo_QH> getReportEventInfoArchivedList(ReportEventCondition_QH reportEventCond, int did)
        {
            ZTSQLReportEventInfoArchivedQuery_QH query = new ZTSQLReportEventInfoArchivedQuery_QH(MainModel, reportEventCond, did);
            query.Query();

            return query.GetReportEventInfoList();
        }

        private void fireShowFormAfterQuery()
        {
            ZTReportEventMngListForm_QH reportEventMngListForm
                = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTReportEventMngListForm_QH)) as ZTReportEventMngListForm_QH;
            if (reportEventMngListForm == null || reportEventMngListForm.IsDisposed)
            {
                reportEventMngListForm = new ZTReportEventMngListForm_QH(MainModel);
            }
            reportEventMngListForm.FillData(reportEventInfoList,reportEventInfoArchivedList, thirdNameList);
            reportEventMngListForm.Owner = MainModel.MainForm;
            reportEventMngListForm.Visible = true;
            reportEventMngListForm.BringToFront();
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18031, this.Name);
        }
        
        public override string Name
        {
            get { return "QH异常事件跟踪"; }
        }

        public override string IconName
        {
            get { return "Images/cellquery.gif"; }
        }
    }

    public class ReportEventCondition_QH
    {
        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }
        public string NetType { get; set; }
        public string UserName { get; set; }   
        public Dictionary<int, string> ProjDic { get; set; }//项目列表
    }

    public enum QueryEvent
    {
        QueryAll = 1,
        QueryArchived,
    }
}