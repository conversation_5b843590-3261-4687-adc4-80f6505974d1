﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using System.Reflection;
using CQTLibrary.DbManage;
using MasterCom.RAMS.Model.Interface;
using System.Xml;

namespace DTLibrary
{
    public class DTParaManage
    {
        /// <summary>
        /// 构造性能SQL查询语句
        /// </summary>
        public List<string> getSearchSql(DateTime sTime, DateTime eTime, string strTbName,List<int> ciList, List<DtParaColumnItem> pcItemList,
                                         ref List<E_VType> fieldTypeArr,ref List<string> itemList)
        {
            List<string> sqlList = new List<string>();
            List<string> lognameList = new List<string>();
            List<string> strList = new List<string>();
            getLogNameList(sTime, eTime, strTbName, pcItemList, ref lognameList, ref strList);

            fieldTypeArr.Add(E_VType.E_String);
            fieldTypeArr.Add(E_VType.E_Int);
            fieldTypeArr.Add(E_VType.E_Int);

            itemList.Add("StrTime");
            itemList.Add("ILac");
            itemList.Add("ICi");
            
            StringBuilder strColumnsWithConvert = new StringBuilder();//由于服务器传数值的精度不够，需要转换字符
            foreach (string str in strList)
            {
                fieldTypeArr.Add(E_VType.E_String);
                itemList.Add(str);
                strColumnsWithConvert.Append("convert(varchar(20),cast(" + str + " as decimal)),");
            }

            StringBuilder strCi = new StringBuilder();
            foreach(int iCi in ciList)
            {
                strCi.Append(iCi.ToString() + ",");
            }

            foreach (string tbName in lognameList)
            {
                string strSql = "SELECT Convert(varchar(20),[dTime],20),LAC,CI," + strColumnsWithConvert.ToString().TrimEnd(',') + " FROM " + tbName +
                                " where dTime >= '" + sTime + "' and dTime <= '" + eTime + "' and CI in (" + strCi.ToString().TrimEnd(',') + ")";
                sqlList.Add(strSql);
            }
            return sqlList;
        }

        /// <summary>
        /// 构造性能渲染SQL查询语句
        /// </summary>
        public List<string> getKpiNameList(DateTime sTime, DateTime eTime, string strTbName, List<DtParaColumnItem> pcItemList,
                                         ref List<E_VType> fieldTypeArr, ref List<string> itemList)
        {
            List<string> sqlList = new List<string>();
            List<string> lognameList = new List<string>();
            List<string> strList = new List<string>();
            getLogNameList(sTime, eTime, strTbName, pcItemList, ref lognameList, ref strList);

            fieldTypeArr.Add(E_VType.E_String);
            fieldTypeArr.Add(E_VType.E_Int);
            fieldTypeArr.Add(E_VType.E_Int);

            itemList.Add("StrTime");
            itemList.Add("ILac");
            itemList.Add("ICi");

            StringBuilder strColumnsWithConvert = new StringBuilder();//由于服务器传数值的精度不够，需要转换字符
            foreach (string str in strList)
            {
                fieldTypeArr.Add(E_VType.E_String);
                itemList.Add(str);
                strColumnsWithConvert.Append("convert(varchar(20),cast(" + str + " as decimal)),");
            }
            foreach (string tbName in lognameList)
            {
                string strSql = "SELECT Convert(varchar(20),[dTime],20),LAC,CI," + strColumnsWithConvert.ToString().TrimEnd(',') + " FROM " + tbName +
                                " where dTime >= '" + sTime + "' and dTime <= '" + eTime + "'";
                sqlList.Add(strSql);
            }
            return sqlList;
        }

        /// <summary>
        /// 表名及结构构造
        /// </summary>
        private void getLogNameList(DateTime sTime, DateTime eTime, string strTbName, List<DtParaColumnItem> pcItemList, ref List<string> lognameList, ref List<string> strList)
        {
            DateTime eDate = eTime;
            DateTime sDate = sTime;
            while (sDate <= eDate)
            {
                string strTbNameNew = strTbName + string.Format("_{0:yyMM}", sDate);
                if (!lognameList.Contains(strTbNameNew))
                {
                    lognameList.Add(strTbNameNew);
                }
                sDate = sDate.AddDays(1);
            }

            foreach (DtParaColumnItem pcItem in pcItemList)
            {
                string[] strCols = pcItem.StrColumn.Split(',');
                foreach (string strCol in strCols)
                {
                    if (!strList.Contains(strCol))
                        strList.Add(strCol);
                }
            }
        }

        /// <summary>
        /// 获取性能指标值
        /// </summary>
        public Dictionary<DtParaKey, Dictionary<String, DtParaValueItem>> getParaValue(List<Object[]> paraObjList, List<String> itemList, List<DtParaColumnItem> pcItemList)
        {
            Dictionary<DtParaKey, DtParaItem> paraDic = new Dictionary<DtParaKey, DtParaItem>();
            List<DtParaItemString> paraStrList = ToModel<DtParaItemString>.GetDelegate_ToModelList(paraObjList, itemList) as List<DtParaItemString>;
            foreach (DtParaItemString pis in paraStrList)
            {
                DtParaItem pItem = pis.FillData();
                DtParaKey pKey = new DtParaKey();
                pKey.DTime = pItem.DTime;
                pKey.ILac = pItem.ILac;
                pKey.ICi = pItem.ICi;
                if (!paraDic.ContainsKey(pKey))
                    paraDic.Add(pKey, pItem);
            }

            return ParaStatByKpiName(paraDic, pcItemList);
        }

        //利用委托反射将DataTable转换为实体集
        public delegate void SetValue<in T>(T value);
        public static class ToModel<T> where T : class, new()
        {
            private static Delegate CreateSetDelegate(T model, string propertyName)
            {
                MethodInfo mi = model.GetType().GetProperty(propertyName, BindingFlags.Public | BindingFlags.IgnoreCase | BindingFlags.Instance | BindingFlags.GetProperty).GetSetMethod();
                //这里构造泛型委托类型
                Type delType = typeof(SetValue<>).MakeGenericType(GetPropertyType(propertyName));

                return Delegate.CreateDelegate(delType, model, mi);
            }

            private static Type GetPropertyType(string propertyName)
            {
                return typeof(T).GetProperty(propertyName, BindingFlags.Public | BindingFlags.IgnoreCase | BindingFlags.Instance | BindingFlags.GetProperty).PropertyType;
            }

            public static IList<T> GetDelegate_ToModelList(List<Object[]> paraStrList, List<String> itemList)
            {
                IList<T> list = new List<T>();
                if (itemList == null || itemList.Count < 1 || paraStrList == null || paraStrList.Count < 1) return list;

                Delegate setDelegate;
                foreach (Object[] objPara in paraStrList)
                {
                    T model = new T();
                    for (int i = 0; i < itemList.Count; i++)
                    {
                        try
                        {
                            setDelegate = CreateSetDelegate(model, itemList[i]);
                            //这里改变类型
                            setDelegate.DynamicInvoke(Convert.ChangeType(objPara[i], GetPropertyType(itemList[i])));
                        }
                        catch
                        {
                            //continue
                        }
                    }
                    list.Add(model);
                }
                return list;
            }
        }

        /// <summary>
        /// 性能Counter运算
        /// </summary>
        private Dictionary<DtParaKey, Dictionary<String, DtParaValueItem>> ParaStatByKpiName(Dictionary<DtParaKey, DtParaItem> paraDic, List<DtParaColumnItem> pcItemList)
        {
            Dictionary<DtParaKey, Dictionary<string, DtParaValueItem>> cqtDetailResultDic = new Dictionary<DtParaKey, Dictionary<string, DtParaValueItem>>();
            foreach (DtParaKey pKey in paraDic.Keys)
            {
                DtParaItem pItem = paraDic[pKey];
                Dictionary<string, DtParaValueItem> evaDic = new Dictionary<string, DtParaValueItem>();
                foreach (DtParaColumnItem pci in pcItemList)
                {
                    setDtParaColumnItem(pItem, evaDic, pci);
                }
                cqtDetailResultDic.Add(pKey, evaDic);
            }
            return cqtDetailResultDic;
        }

        private void setDtParaColumnItem(DtParaItem pItem, Dictionary<string, DtParaValueItem> evaDic, DtParaColumnItem pci)
        {
            try
            {
                DtParaValueItem pvItem = new DtParaValueItem();
                pvItem.StrNetType = pci.StrNetType;
                pvItem.StrKpiType = pci.StrKpiType;
                pvItem.StrKpiName = pci.StrKpiName;
                pvItem.StrComment = pci.StrComment;
                pvItem.StrFunc = pci.StrFunc;

                setDtParaValueItemFValue(pItem, pci, pvItem);

                setDtParaValueItemIColor(pci, pvItem);

                setDtParaValueItemStrValue(pci, pvItem);

                evaDic.Add(pci.StrKpiName, pvItem);
            }
            catch
            {
                //continue
            }
        }

        private void setDtParaValueItemFValue(DtParaItem pItem, DtParaColumnItem pci, DtParaValueItem pvItem)
        {
            MatchCollection mc = Regex.Matches(pci.StrFormula, @"col\d+");  //获取本组公式内所有指标
            string tempformula = pci.StrFormula;
            for (int i = 0; i < mc.Count; i++)  //替换所有指标为实际值
            {
                tempformula = tempformula.Replace(mc[i].Value + "c", pItem.GetType().GetProperty(mc[i].Value, BindingFlags.Public | BindingFlags.IgnoreCase | BindingFlags.Instance | BindingFlags.GetProperty).GetValue(pItem, null).ToString());
            }

            tempformula = tempformula.Replace('{', ' ').Replace('}', ' ').Replace('%', ' ').Trim();//剔除无关符号

            if (tempformula.IndexOf("(0/0)") >= 0 || tempformula.IndexOf("(0+0)/((0+0)*0.75)") >= 0)
            {
                pvItem.FValue = -1;
            }
            else
            {
                Expression exp = new Expression();
                try
                {
                    pvItem.FValue = float.Parse(exp.ParseCommand(tempformula));//计算公式
                }
                catch
                {
                    pvItem.FValue = -1;
                }
            }
        }

        private void setDtParaValueItemIColor(DtParaColumnItem pci, DtParaValueItem pvItem)
        {
            DtThresholdValue tsValue;
            if (pci.thresholdDic.ContainsKey("不合格"))
            {
                tsValue = pci.thresholdDic["不合格"];
                if (pvItem.FValue >= tsValue.FDownValue && pvItem.FValue <= tsValue.FUpValue)
                    pvItem.IColor = 1;
            }
            if (pci.thresholdDic.ContainsKey("合格"))
            {
                tsValue = pci.thresholdDic["合格"];
                if (pvItem.FValue >= tsValue.FDownValue && pvItem.FValue <= tsValue.FUpValue)
                    pvItem.IColor = 2;
            }
            if (pci.thresholdDic.ContainsKey("优秀"))
            {
                tsValue = pci.thresholdDic["优秀"];
                if (pvItem.FValue >= tsValue.FDownValue && pvItem.FValue <= tsValue.FUpValue)
                    pvItem.IColor = 3;
            }
        }

        private void setDtParaValueItemStrValue(DtParaColumnItem pci, DtParaValueItem pvItem)
        {
            if (pvItem.FValue == -1)
                pvItem.StrValue = "-";
            else if (pci.StrFormula.IndexOf('%') != -1)
                pvItem.StrValue = pvItem.FValue.ToString("0.00") + "%";
            else
            {
                if (pvItem.FValue - (int)pvItem.FValue != 0)
                    pvItem.StrValue = pvItem.FValue.ToString("0.00");
                else
                    pvItem.StrValue = pvItem.FValue.ToString();
            }
        }
    }

    public class DtParaKey
    {
        /// <summary>
        /// 性能时间
        /// </summary>
        public DateTime DTime { get; set; }
        /// <summary>
        /// 小区LAC
        /// </summary>
        public int ILac { get; set; }
        /// <summary>
        /// 小区CI
        /// </summary>
        public int ICi { get; set; }

        public override bool Equals(object obj)
        {
            DtParaKey other = obj as DtParaKey;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.DTime.Equals(other.DTime) &&
                    this.ILac.Equals(other.ILac) &&
                    this.ICi.Equals(other.ICi));
        }

        public override int GetHashCode()
        {
            return this.ICi.GetHashCode();
        }
    }

    public class DtParaItem
    {
        public DateTime DTime { get; set; }
        public int ILac { get; set; }
        public int ICi { get; set; }   
        public double Col1 { get; set; }
        public double Col2 { get; set; }
        public double Col3 { get; set; }
        public double Col4 { get; set; }
        public double Col5 { get; set; }
        public double Col6 { get; set; }
        public double Col7 { get; set; }
        public double Col8 { get; set; }
        public double Col9 { get; set; }
        public double Col10 { get; set; }
        public double Col11 { get; set; }
        public double Col12 { get; set; }
        public double Col13 { get; set; }
        public double Col14 { get; set; }
        public double Col15 { get; set; }
        public double Col16 { get; set; }
        public double Col17 { get; set; }
        public double Col18 { get; set; }
        public double Col19 { get; set; }
        public double Col20 { get; set; }
        public double Col21 { get; set; }
        public double Col22 { get; set; }
        public double Col23 { get; set; }
        public double Col24 { get; set; }
        public double Col25 { get; set; }
        public double Col26 { get; set; }
        public double Col27 { get; set; }
        public double Col28 { get; set; }
        public double Col29 { get; set; }
        public double Col30 { get; set; }
        public double Col31 { get; set; }
        public double Col32 { get; set; }
        public double Col33 { get; set; }
        public double Col34 { get; set; }
        public double Col35 { get; set; }
        public double Col36 { get; set; }
        public double Col37 { get; set; }
        public double Col38 { get; set; }
        public double Col39 { get; set; }
        public double Col40 { get; set; }
        public double Col41 { get; set; }
        public double Col42 { get; set; }
        public double Col43 { get; set; }
        public double Col44 { get; set; }
        public double Col45 { get; set; }
        public double Col46 { get; set; }
        public double Col47 { get; set; }
        public double Col48 { get; set; }
        public double Col49 { get; set; }
        public double Col50 { get; set; }
        public double Col51 { get; set; }
        public double Col52 { get; set; }
        public double Col53 { get; set; }
        public double Col54 { get; set; }
        public double Col55 { get; set; }
        public double Col56 { get; set; }
        public double Col57 { get; set; }
        public double Col58 { get; set; }
        public double Col59 { get; set; }
        public double Col60 { get; set; }
        public double Col61 { get; set; }
        public double Col62 { get; set; }
        public double Col63 { get; set; }
        public double Col64 { get; set; }
        public double Col65 { get; set; }
        public double Col66 { get; set; }
        public double Col67 { get; set; }
        public double Col68 { get; set; }
        public double Col69 { get; set; }
        public double Col70 { get; set; }
        public double Col71 { get; set; }
        public double Col72 { get; set; }
        public double Col73 { get; set; }
        public double Col74 { get; set; }
        public double Col75 { get; set; }
        public double Col76 { get; set; }
        public double Col77 { get; set; }
        public double Col78 { get; set; }
        public double Col79 { get; set; }
        public double Col80 { get; set; }
        public double Col81 { get; set; }
        public double Col82 { get; set; }
        public double Col83 { get; set; }
        public double Col84 { get; set; }
        public double Col85 { get; set; }
        public double Col86 { get; set; }
        public double Col87 { get; set; }
        public double Col88 { get; set; }
        public double Col89 { get; set; }
        public double Col90 { get; set; }
        public double Col91 { get; set; }
        public double Col92 { get; set; }
        public double Col93 { get; set; }
        public double Col94 { get; set; }
        public double Col95 { get; set; }
        public double Col96 { get; set; }
        public double Col97 { get; set; }
        public double Col98 { get; set; }
        public double Col99 { get; set; }
        public double Col100 { get; set; }
    }

    public class DtParaItemString
    {
        public DtParaItemString()
        {
            Col1 = "";
            Col2 = "";
            Col3 = "";
            Col4 = "";
            Col5 = "";
            Col6 = "";
            Col7 = "";
            Col8 = "";
            Col9 = "";
            Col10 = "";
            Col11 = "";
            Col12 = "";
            Col13 = "";
            Col14 = "";
            Col15 = "";
            Col16 = "";
            Col17 = "";
            Col18 = "";
            Col19 = "";
            Col20 = "";
            Col21 = "";
            Col22 = "";
            Col23 = "";
            Col24 = "";
            Col25 = "";
            Col26 = "";
            Col27 = "";
            Col28 = "";
            Col29 = "";
            Col30 = "";
            Col31 = "";
            Col32 = "";
            Col33 = "";
            Col34 = "";
            Col35 = "";
            Col36 = "";
            Col37 = "";
            Col38 = "";
            Col39 = "";
            Col40 = "";
            Col41 = "";
            Col42 = "";
            Col43 = "";
            Col44 = "";
            Col45 = "";
            Col46 = "";
            Col47 = "";
            Col48 = "";
            Col49 = "";
            Col50 = "";
            Col51 = "";
            Col52 = "";
            Col53 = "";
            Col54 = "";
            Col55 = "";
            Col56 = "";
            Col57 = "";
            Col58 = "";
            Col59 = "";
            Col60 = "";
            Col61 = "";
            Col62 = "";
            Col63 = "";
            Col64 = "";
            Col65 = "";
            Col66 = "";
            Col67 = "";
            Col68 = "";
            Col69 = "";
            Col70 = "";
            Col71 = "";
            Col72 = "";
            Col73 = "";
            Col74 = "";
            Col75 = "";
            Col76 = "";
            Col77 = "";
            Col78 = "";
            Col79 = "";
            Col80 = "";
            Col81 = "";
            Col82 = "";
            Col83 = "";
            Col84 = "";
            Col85 = "";
            Col86 = "";
            Col87 = "";
            Col88 = "";
            Col89 = "";
            Col90 = "";
            Col91 = "";
            Col92 = "";
            Col93 = "";
            Col94 = "";
            Col95 = "";
            Col96 = "";
            Col97 = "";
            Col98 = "";
            Col99 = "";
            Col100 = "";
        }

        public DtParaItem FillData()
        {
            double dCol1 = 0;
            double dCol2 = 0;
            double dCol3 = 0;
            double dCol4 = 0;
            double dCol5 = 0;
            double dCol6 = 0;
            double dCol7 = 0;
            double dCol8 = 0;
            double dCol9 = 0;
            double dCol10 = 0;
            double dCol11 = 0;
            double dCol12 = 0;
            double dCol13 = 0;
            double dCol14 = 0;
            double dCol15 = 0;
            double dCol16 = 0;
            double dCol17 = 0;
            double dCol18 = 0;
            double dCol19 = 0;
            double dCol20 = 0;
            double dCol21 = 0;
            double dCol22 = 0;
            double dCol23 = 0;
            double dCol24 = 0;
            double dCol25 = 0;
            double dCol26 = 0;
            double dCol27 = 0;
            double dCol28 = 0;
            double dCol29 = 0;
            double dCol30 = 0;
            double dCol31 = 0;
            double dCol32 = 0;
            double dCol33 = 0;
            double dCol34 = 0;
            double dCol35 = 0;
            double dCol36 = 0;
            double dCol37 = 0;
            double dCol38 = 0;
            double dCol39 = 0;
            double dCol40 = 0;
            double dCol41 = 0;
            double dCol42 = 0;
            double dCol43 = 0;
            double dCol44 = 0;
            double dCol45 = 0;
            double dCol46 = 0;
            double dCol47 = 0;
            double dCol48 = 0;
            double dCol49 = 0;
            double dCol50 = 0;
            double dCol51 = 0;
            double dCol52 = 0;
            double dCol53 = 0;
            double dCol54 = 0;
            double dCol55 = 0;
            double dCol56 = 0;
            double dCol57 = 0;
            double dCol58 = 0;
            double dCol59 = 0;
            double dCol60 = 0;
            double dCol61 = 0;
            double dCol62 = 0;
            double dCol63 = 0;
            double dCol64 = 0;
            double dCol65 = 0;
            double dCol66 = 0;
            double dCol67 = 0;
            double dCol68 = 0;
            double dCol69 = 0;
            double dCol70 = 0;
            double dCol71 = 0;
            double dCol72 = 0;
            double dCol73 = 0;
            double dCol74 = 0;
            double dCol75 = 0;
            double dCol76 = 0;
            double dCol77 = 0;
            double dCol78 = 0;
            double dCol79 = 0;
            double dCol80 = 0;
            double dCol81 = 0;
            double dCol82 = 0;
            double dCol83 = 0;
            double dCol84 = 0;
            double dCol85 = 0;
            double dCol86 = 0;
            double dCol87 = 0;
            double dCol88 = 0;
            double dCol89 = 0;
            double dCol90 = 0;
            double dCol91 = 0;
            double dCol92 = 0;
            double dCol93 = 0;
            double dCol94 = 0;
            double dCol95 = 0;
            double dCol96 = 0;
            double dCol97 = 0;
            double dCol98 = 0;
            double dCol99 = 0;
            double dCol100 = 0;

            double.TryParse(this.Col1, out dCol1);
            double.TryParse(this.Col2, out dCol2);
            double.TryParse(this.Col3, out dCol3);
            double.TryParse(this.Col4, out dCol4);
            double.TryParse(this.Col5, out dCol5);
            double.TryParse(this.Col6, out dCol6);
            double.TryParse(this.Col7, out dCol7);
            double.TryParse(this.Col8, out dCol8);
            double.TryParse(this.Col9, out dCol9);
            double.TryParse(this.Col10, out dCol10);
            double.TryParse(this.Col11, out dCol11);
            double.TryParse(this.Col12, out dCol12);
            double.TryParse(this.Col13, out dCol13);
            double.TryParse(this.Col14, out dCol14);
            double.TryParse(this.Col15, out dCol15);
            double.TryParse(this.Col16, out dCol16);
            double.TryParse(this.Col17, out dCol17);
            double.TryParse(this.Col18, out dCol18);
            double.TryParse(this.Col19, out dCol19);
            double.TryParse(this.Col20, out dCol20);
            double.TryParse(this.Col21, out dCol21);
            double.TryParse(this.Col22, out dCol22);
            double.TryParse(this.Col23, out dCol23);
            double.TryParse(this.Col24, out dCol24);
            double.TryParse(this.Col25, out dCol25);
            double.TryParse(this.Col26, out dCol26);
            double.TryParse(this.Col27, out dCol27);
            double.TryParse(this.Col28, out dCol28);
            double.TryParse(this.Col29, out dCol29);
            double.TryParse(this.Col30, out dCol30);
            double.TryParse(this.Col31, out dCol31);
            double.TryParse(this.Col32, out dCol32);
            double.TryParse(this.Col33, out dCol33);
            double.TryParse(this.Col34, out dCol34);
            double.TryParse(this.Col35, out dCol35);
            double.TryParse(this.Col36, out dCol36);
            double.TryParse(this.Col37, out dCol37);
            double.TryParse(this.Col38, out dCol38);
            double.TryParse(this.Col39, out dCol39);
            double.TryParse(this.Col40, out dCol40);
            double.TryParse(this.Col41, out dCol41);
            double.TryParse(this.Col42, out dCol42);
            double.TryParse(this.Col43, out dCol43);
            double.TryParse(this.Col44, out dCol44);
            double.TryParse(this.Col45, out dCol45);
            double.TryParse(this.Col46, out dCol46);
            double.TryParse(this.Col47, out dCol47);
            double.TryParse(this.Col48, out dCol48);
            double.TryParse(this.Col49, out dCol49);
            double.TryParse(this.Col50, out dCol50);
            double.TryParse(this.Col51, out dCol51);
            double.TryParse(this.Col52, out dCol52);
            double.TryParse(this.Col53, out dCol53);
            double.TryParse(this.Col54, out dCol54);
            double.TryParse(this.Col55, out dCol55);
            double.TryParse(this.Col56, out dCol56);
            double.TryParse(this.Col57, out dCol57);
            double.TryParse(this.Col58, out dCol58);
            double.TryParse(this.Col59, out dCol59);
            double.TryParse(this.Col60, out dCol60);
            double.TryParse(this.Col61, out dCol61);
            double.TryParse(this.Col62, out dCol62);
            double.TryParse(this.Col63, out dCol63);
            double.TryParse(this.Col64, out dCol64);
            double.TryParse(this.Col65, out dCol65);
            double.TryParse(this.Col66, out dCol66);
            double.TryParse(this.Col67, out dCol67);
            double.TryParse(this.Col68, out dCol68);
            double.TryParse(this.Col69, out dCol69);
            double.TryParse(this.Col70, out dCol70);
            double.TryParse(this.Col71, out dCol71);
            double.TryParse(this.Col72, out dCol72);
            double.TryParse(this.Col73, out dCol73);
            double.TryParse(this.Col74, out dCol74);
            double.TryParse(this.Col75, out dCol75);
            double.TryParse(this.Col76, out dCol76);
            double.TryParse(this.Col77, out dCol77);
            double.TryParse(this.Col78, out dCol78);
            double.TryParse(this.Col79, out dCol79);
            double.TryParse(this.Col80, out dCol80);
            double.TryParse(this.Col81, out dCol81);
            double.TryParse(this.Col82, out dCol82);
            double.TryParse(this.Col83, out dCol83);
            double.TryParse(this.Col84, out dCol84);
            double.TryParse(this.Col85, out dCol85);
            double.TryParse(this.Col86, out dCol86);
            double.TryParse(this.Col87, out dCol87);
            double.TryParse(this.Col88, out dCol88);
            double.TryParse(this.Col89, out dCol89);
            double.TryParse(this.Col90, out dCol90);
            double.TryParse(this.Col91, out dCol91);
            double.TryParse(this.Col92, out dCol92);
            double.TryParse(this.Col93, out dCol93);
            double.TryParse(this.Col94, out dCol94);
            double.TryParse(this.Col95, out dCol95);
            double.TryParse(this.Col96, out dCol96);
            double.TryParse(this.Col97, out dCol97);
            double.TryParse(this.Col98, out dCol98);
            double.TryParse(this.Col99, out dCol99);
            double.TryParse(this.Col100, out dCol100);

            DtParaItem pItem = new DtParaItem();
            pItem.DTime = Convert.ToDateTime(this.StrTime);
            pItem.ILac = this.ILac;
            pItem.ICi = this.ICi;
            pItem.Col1 = dCol1;
            pItem.Col2 = dCol2;
            pItem.Col3 = dCol3;
            pItem.Col4 = dCol4;
            pItem.Col5 = dCol5;
            pItem.Col6 = dCol6;
            pItem.Col7 = dCol7;
            pItem.Col8 = dCol8;
            pItem.Col9 = dCol9;
            pItem.Col10 = dCol10;
            pItem.Col11 = dCol11;
            pItem.Col12 = dCol12;
            pItem.Col13 = dCol13;
            pItem.Col14 = dCol14;
            pItem.Col15 = dCol15;
            pItem.Col16 = dCol16;
            pItem.Col17 = dCol17;
            pItem.Col18 = dCol18;
            pItem.Col19 = dCol19;
            pItem.Col20 = dCol20;
            pItem.Col21 = dCol21;
            pItem.Col22 = dCol22;
            pItem.Col23 = dCol23;
            pItem.Col24 = dCol24;
            pItem.Col25 = dCol25;
            pItem.Col26 = dCol26;
            pItem.Col27 = dCol27;
            pItem.Col28 = dCol28;
            pItem.Col29 = dCol29;
            pItem.Col30 = dCol30;
            pItem.Col31 = dCol31;
            pItem.Col32 = dCol32;
            pItem.Col33 = dCol33;
            pItem.Col34 = dCol34;
            pItem.Col35 = dCol35;
            pItem.Col36 = dCol36;
            pItem.Col37 = dCol37;
            pItem.Col38 = dCol38;
            pItem.Col39 = dCol39;
            pItem.Col40 = dCol40;
            pItem.Col41 = dCol41;
            pItem.Col42 = dCol42;
            pItem.Col43 = dCol43;
            pItem.Col44 = dCol44;
            pItem.Col45 = dCol45;
            pItem.Col46 = dCol46;
            pItem.Col47 = dCol47;
            pItem.Col48 = dCol48;
            pItem.Col49 = dCol49;
            pItem.Col50 = dCol50;
            pItem.Col51 = dCol51;
            pItem.Col52 = dCol52;
            pItem.Col53 = dCol53;
            pItem.Col54 = dCol54;
            pItem.Col55 = dCol55;
            pItem.Col56 = dCol56;
            pItem.Col57 = dCol57;
            pItem.Col58 = dCol58;
            pItem.Col59 = dCol59;
            pItem.Col60 = dCol60;
            pItem.Col61 = dCol61;
            pItem.Col62 = dCol62;
            pItem.Col63 = dCol63;
            pItem.Col64 = dCol64;
            pItem.Col65 = dCol65;
            pItem.Col66 = dCol66;
            pItem.Col67 = dCol67;
            pItem.Col68 = dCol68;
            pItem.Col69 = dCol69;
            pItem.Col70 = dCol70;
            pItem.Col71 = dCol71;
            pItem.Col72 = dCol72;
            pItem.Col73 = dCol73;
            pItem.Col74 = dCol74;
            pItem.Col75 = dCol75;
            pItem.Col76 = dCol76;
            pItem.Col77 = dCol77;
            pItem.Col78 = dCol78;
            pItem.Col79 = dCol79;
            pItem.Col80 = dCol80;
            pItem.Col81 = dCol81;
            pItem.Col82 = dCol82;
            pItem.Col83 = dCol83;
            pItem.Col84 = dCol84;
            pItem.Col85 = dCol85;
            pItem.Col86 = dCol86;
            pItem.Col87 = dCol87;
            pItem.Col88 = dCol88;
            pItem.Col89 = dCol89;
            pItem.Col90 = dCol90;
            pItem.Col91 = dCol91;
            pItem.Col92 = dCol92;
            pItem.Col93 = dCol93;
            pItem.Col94 = dCol94;
            pItem.Col95 = dCol95;
            pItem.Col96 = dCol96;
            pItem.Col97 = dCol97;
            pItem.Col98 = dCol98;
            pItem.Col99 = dCol99;
            pItem.Col100 = dCol100;
            return pItem;
        }

        public string StrTime { get; set; }
        public int ILac { get; set; }
        public int ICi { get; set; }
        public string Col1 { get; set; }
        public string Col2 { get; set; }
        public string Col3 { get; set; }
        public string Col4 { get; set; }
        public string Col5 { get; set; }
        public string Col6 { get; set; }
        public string Col7 { get; set; }
        public string Col8 { get; set; }
        public string Col9 { get; set; }
        public string Col10 { get; set; }
        public string Col11 { get; set; }
        public string Col12 { get; set; }
        public string Col13 { get; set; }
        public string Col14 { get; set; }
        public string Col15 { get; set; }
        public string Col16 { get; set; }
        public string Col17 { get; set; }
        public string Col18 { get; set; }
        public string Col19 { get; set; }
        public string Col20 { get; set; }
        public string Col21 { get; set; }
        public string Col22 { get; set; }
        public string Col23 { get; set; }
        public string Col24 { get; set; }
        public string Col25 { get; set; }
        public string Col26 { get; set; }
        public string Col27 { get; set; }
        public string Col28 { get; set; }
        public string Col29 { get; set; }
        public string Col30 { get; set; }
        public string Col31 { get; set; }
        public string Col32 { get; set; }
        public string Col33 { get; set; }
        public string Col34 { get; set; }
        public string Col35 { get; set; }
        public string Col36 { get; set; }
        public string Col37 { get; set; }
        public string Col38 { get; set; }
        public string Col39 { get; set; }
        public string Col40 { get; set; }
        public string Col41 { get; set; }
        public string Col42 { get; set; }
        public string Col43 { get; set; }
        public string Col44 { get; set; }
        public string Col45 { get; set; }
        public string Col46 { get; set; }
        public string Col47 { get; set; }
        public string Col48 { get; set; }
        public string Col49 { get; set; }
        public string Col50 { get; set; }
        public string Col51 { get; set; }
        public string Col52 { get; set; }
        public string Col53 { get; set; }
        public string Col54 { get; set; }
        public string Col55 { get; set; }
        public string Col56 { get; set; }
        public string Col57 { get; set; }
        public string Col58 { get; set; }
        public string Col59 { get; set; }
        public string Col60 { get; set; }
        public string Col61 { get; set; }
        public string Col62 { get; set; }
        public string Col63 { get; set; }
        public string Col64 { get; set; }
        public string Col65 { get; set; }
        public string Col66 { get; set; }
        public string Col67 { get; set; }
        public string Col68 { get; set; }
        public string Col69 { get; set; }
        public string Col70 { get; set; }
        public string Col71 { get; set; }
        public string Col72 { get; set; }
        public string Col73 { get; set; }
        public string Col74 { get; set; }
        public string Col75 { get; set; }
        public string Col76 { get; set; }
        public string Col77 { get; set; }
        public string Col78 { get; set; }
        public string Col79 { get; set; }
        public string Col80 { get; set; }
        public string Col81 { get; set; }
        public string Col82 { get; set; }
        public string Col83 { get; set; }
        public string Col84 { get; set; }
        public string Col85 { get; set; }
        public string Col86 { get; set; }
        public string Col87 { get; set; }
        public string Col88 { get; set; }
        public string Col89 { get; set; }
        public string Col90 { get; set; }
        public string Col91 { get; set; }
        public string Col92 { get; set; }
        public string Col93 { get; set; }
        public string Col94 { get; set; }
        public string Col95 { get; set; }
        public string Col96 { get; set; }
        public string Col97 { get; set; }
        public string Col98 { get; set; }
        public string Col99 { get; set; }
        public string Col100 { get; set; }
    }

    public class DtParaValueItem
    {
        /// <summary>
        /// 网络类型
        /// </summary>
        public string StrNetType { get; set; }
        /// <summary>
        /// 指标类型
        /// </summary>
        public string StrKpiType { get; set; }
        /// <summary>
        /// 指标名称
        /// </summary>
        public string StrKpiName { get; set; }
        /// <summary>
        /// 指标备注
        /// </summary>
        public string StrComment { get; set; }
        /// <summary>
        /// 功能项目
        /// </summary>
        public string StrFunc { get; set; }
        /// <summary>
        /// 颜色配置(1.不合格－红色50%透明度;2.合格;3.优秀)
        /// </summary>
        public int IColor { get; set; }
        /// <summary>
        /// KPI值
        /// </summary>
        public float FValue { get; set; }
        /// <summary>
        /// KPI字符值
        /// </summary>
        public string StrValue { get; set; }
        /// <summary>
        /// 渲染区间
        /// </summary>
        public string StrThresholdDraw { get; set; }
    }

    public class DtParaColumnItem : DtParaValueItem
    {
        /// <summary>
        /// 指标公式
        /// </summary>
        public string StrFormula { get; set; }
        /// <summary>
        /// 表名
        /// </summary>
        public string StrTableName { get; set; }
        /// <summary>
        /// 数据列名
        /// </summary>
        public string StrColumn { get; set; }
        /// <summary>
        /// 门限值（默认为小于等于 和 大于）
        /// </summary>
        public Dictionary<string, DtThresholdValue> thresholdDic { get; set; } = new Dictionary<string, DtThresholdValue>();
    }

    public class DtThresholdValue
    {
        /// <summary>
        /// 上限值
        /// </summary>
        public float FUpValue { get; set; }
        /// <summary>
        /// 下限值
        /// </summary>
        public float FDownValue { get; set; }
    }

    public class ReadXML
    {
        public ReadXML()
        {
        }
        /// <summary>
        /// 读取配置文件DtFormulaCfg.xml
        /// </summary>
        public List<DtParaColumnItem> getConfigXml(string path,string network)
        {
            XmlDocument doc = new XmlDocument();
            try
            {
                doc.Load(path);
            }
            catch
            {
                return new List<DtParaColumnItem>();
            }

            XmlNode xn;
            xn = doc.DocumentElement.SelectSingleNode("Config");                   //找到根节点

            List<DtParaColumnItem> paraList = new List<DtParaColumnItem>();

            //读取节点,填充ParaColumnItem数据结构
            foreach (XmlNode item2 in xn.SelectSingleNode("Item"))
            {
                DtParaColumnItem paraItem = new DtParaColumnItem();

                foreach (XmlNode item3 in item2.ChildNodes)
                {
                    setParaItem(paraItem, item3);
                }
                if (paraItem.StrNetType.Equals(network))
                    paraList.Add(paraItem);
            }

            return paraList;
        }

        private void setParaItem(DtParaColumnItem paraItem, XmlNode item3)
        {
            if (((XmlElement)item3).GetAttribute("name") == "NetType")
                paraItem.StrNetType = ((XmlElement)item3).InnerText;
            if (((XmlElement)item3).GetAttribute("name") == "KpiType")
                paraItem.StrKpiType = ((XmlElement)item3).InnerText;
            if (((XmlElement)item3).GetAttribute("name") == "KpiName")
                paraItem.StrKpiName = ((XmlElement)item3).InnerText;
            if (((XmlElement)item3).GetAttribute("name") == "Formula")
                paraItem.StrFormula = ((XmlElement)item3).InnerText;
            if (((XmlElement)item3).GetAttribute("name") == "TableName")
                paraItem.StrTableName = ((XmlElement)item3).InnerText;
            if (((XmlElement)item3).GetAttribute("name") == "Column")
                paraItem.StrColumn = ((XmlElement)item3).InnerText;
            if (((XmlElement)item3).GetAttribute("name") == "Comment")
                paraItem.StrComment = ((XmlElement)item3).InnerText;
            if (((XmlElement)item3).GetAttribute("name") == "Func")
                paraItem.StrFunc = ((XmlElement)item3).InnerText;
            if (((XmlElement)item3).GetAttribute("name") == "ThresholdDraw")
                paraItem.StrThresholdDraw = ((XmlElement)item3).InnerText;
            if (((XmlElement)item3).GetAttribute("name") == "ThresholdValue")
            {
                string[] threshold = ((XmlElement)item3).InnerText.Split('}');
                foreach (string thr in threshold)
                {
                    if (thr != "")
                    {
                        DtThresholdValue thrvalue = new DtThresholdValue();
                        thrvalue.FDownValue = float.Parse(thr.Split(',')[0].Replace('[', ' ').Replace(']', ' ').Replace('{', ' ').Replace('}', ' ').Replace('(', ' ').Replace(')', ' ').Trim());
                        thrvalue.FUpValue = float.Parse(thr.Split(',')[1].Replace('[', ' ').Replace(']', ' ').Replace('{', ' ').Replace('}', ' ').Replace('(', ' ').Replace(')', ' ').Trim());

                        paraItem.thresholdDic.Add(thr.Split(',')[2].Replace('[', ' ').Replace(']', ' ').Replace('{', ' ').Replace('}', ' ').Replace('(', ' ').Replace(')', ' ').Trim(), thrvalue);
                    }
                }
            }
        }

        /// <summary>
        ///获取渲染指标 
        /// </summary>
        public List<DtParaColumnItem> getKPISplitXml(string path, string network)
        {
            List<DtParaColumnItem> KPISplit = new List<DtParaColumnItem>();
            List<DtParaColumnItem> paraList = new List<DtParaColumnItem>();
            paraList.AddRange(getConfigXml(path, network));
            foreach (DtParaColumnItem dtpc in paraList)
            {
                if (dtpc.StrFunc.IndexOf("渲染") > -1)
                    KPISplit.Add(dtpc);
            }
            return KPISplit;
        }
    }
}
