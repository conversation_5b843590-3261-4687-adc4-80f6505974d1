﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class NRBlockCallAnaHelper
    {
        public List<int> MoCallBlockEventIds { get; } = NREventHelper.VoiceHelper.MoCallBlockEventIds;
        public List<int> MoCallAttemptEventIds { get; } = NREventHelper.VoiceHelper.MoCallAttemptEventIds;
        public List<int> MoCallOverEventIds { get; } = NREventHelper.VoiceHelper.MoCallOverEventIds;

        public List<int> MtCallBlockEventIds { get; } = NREventHelper.VoiceHelper.MtCallBlockEventIds;
        public List<int> MtCallAttemptEventIds { get; } = NREventHelper.VoiceHelper.MtCallAttemptEventIds;
        public List<int> MtCallOverEventIds { get; } = NREventHelper.VoiceHelper.MtCallOverEventIds;

        public List<int> HandOverEvtIdList { get; } = NREventHelper.HandoverHelper.GetHandoverRequestEvt(false);
    }
}
