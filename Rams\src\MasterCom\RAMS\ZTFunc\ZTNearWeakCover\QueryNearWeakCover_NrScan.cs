﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTNearWeakCover;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryNearWeakCover_NrScan : DIYAnalyseByCellBackgroundBaseByPeriod
    {
        private static QueryNearWeakCover_NrScan intance = null;
        protected static readonly object lockObj = new object();
        public static QueryNearWeakCover_NrScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new QueryNearWeakCover_NrScan();
                    }
                }
            }
            return intance;
        }

        protected QueryNearWeakCover_NrScan()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
#if LT
            carrierID = CarrierType.ChinaUnicom;
#else
            carrierID = CarrierType.ChinaMobile;
#endif
        }

        public override string Name
        {
            get { return "近场低输出_NRScan"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23014, this.Name);
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            cells = new List<NearWeakCoverNrCell>();
        }

        private List<NearWeakCoverNrCell> cells = null;
        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (!Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude))
            {
                return false;
            }
            for (int i = 0; i < 10; i++)
            {
                object value = tp["NRSCAN_SSB_RSRP"];
                if (value == null)
                {
                    break;
                }
                float rsrp = float.Parse(value.ToString());
                if (rsrp < -141 || rsrp > 25 || rsrp > funcCond.Rsrp)
                {
                    break;
                }

                NRCell cell = tp.GetCell_NRScan(i);
                if (cell==null)
                {
                    continue;
                }
                double distance = tp.Distance2(cell.Longitude, cell.Latitude);
                if (distance <= funcCond.Distance)
                {
                    NearWeakCoverNrCell cellItem = cells.Find(delegate(NearWeakCoverNrCell x)
                    { return x.Cell == cell; });
                    if (cellItem == null)
                    {
                        cellItem = new NearWeakCoverNrCell(cell);
                        cells.Add(cellItem);
                    }
                    cellItem.AddPoint(tp, distance, rsrp, i == 0);
                }
            }
            return false;
        }

        FuncCondition funcCond = null;
        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            NearWeakCoverSettingDlg dlg = new NearWeakCoverSettingDlg();
            dlg.Condition = funcCond;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            funcCond = dlg.Condition;
            return true;
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "NRSCAN_SSB_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param["param_name"] = "NRSCAN_SSB_SINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "NRSCAN_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "NRSCAN_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"NR_SCAN");
            tmpDic.Add("themeName", (object)"SSB_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void getResultAfterQuery()
        {
            int sn = 1;
            cells.Sort();
            foreach (NearWeakCoverNrCell cell in cells)
            {
                cell.Sn = sn++;
                cell.MakeSummary();
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            NrResultForm form = MainModel.GetObjectFromBlackboard(typeof(NrResultForm)) as NrResultForm;
            if (form == null || form.IsDisposed)
            {
                form = new NrResultForm();
                form.Owner = MainModel.MainForm;
            }
            form.FillData(new List<NearWeakCoverNrCell>(cells));
            form.Visible = true;
            form.BringToFront();
            cells = null;
        }

    }
}
