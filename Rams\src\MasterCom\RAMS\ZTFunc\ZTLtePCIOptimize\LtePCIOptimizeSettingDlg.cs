﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using System.Threading;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LtePCIOptimizeSettingDlg : BaseFormStyle
    {
        private List<LTECell> allCells;
        private List<LTECell> showCells;
        private List<LTECell> checkedCells;

        public LtePCIOptimizeSettingDlg()
        {
            InitializeComponent();
            allCells = new List<LTECell>();
            showCells = new List<LTECell>();
            checkedCells = new List<LTECell>();
        }

        public void FillCells(List<LTECell> cells)
        {
            allCells.Clear();
            showCells.Clear();
            checkedCells.Clear();
            allCells.AddRange(cells);
            refreshLV(showCells);
        }

        private void searchCell()
        {
            try
            {
                simpleButtonSearch.Enabled = false;
                string name = textBoxCellName.Text.Trim();
                if (name == "") return;
                string[] nameArry = name.Split(new char[] { '\r', '\n' });
                List<string> nameLst = new List<string>();
                foreach (string nameStr in nameArry)
                {
                    if (nameStr != "")
                    {
                        nameLst.Add(nameStr);
                    }
                }
                if (nameLst.Count == 0) return;
                foreach (LTECell cell in allCells)
                {
                    foreach (string nStr in nameLst)
                    {
                        if (cell.Name.Contains(nStr))
                        {
                            if (!showCells.Contains(cell))
                            {
                                showCells.Add(cell);
                            }
                            break;
                        }
                    }
                }
                refreshLV(showCells);
                simpleButtonSearch.Enabled = true;
            }
            finally
            {
                Thread.Sleep(100);
                WaitBox.Close();
            }
        }

        private void refreshLV(List<LTECell> showCellList)
        {
            listViewCellList.BeginUpdate();
            listViewCellList.Items.Clear();
            ListViewItem[] items = new ListViewItem[showCellList.Count];
            int idx = 0;
            foreach (LTECell cell in showCellList)
            {
                ListViewItem item = new ListViewItem(cell.Name);
                items[idx++] = item;
                item.Tag = cell;
            }
            listViewCellList.Items.AddRange(items);
            listViewCellList.EndUpdate();
        }

        private void checkBoxAll_CheckedChanged(object sender, EventArgs e)
        {
            foreach (ListViewItem lvi in listViewCellList.Items)
            {
                lvi.Checked = checkBoxAll.Checked;
            }
            if (checkBoxAll.Checked)
            {
                checkedCells.Clear();
                checkedCells.AddRange(showCells);
            }
            else
            {
                checkedCells.Clear();
            }
        }

        private void simpleButtonSearch_Click(object sender, EventArgs e)
        {
            showCells.Clear();
            WaitBox.Show("正在查找小区...", searchCell);
        }

        private void listViewCellList_ItemChecked(object sender, ItemCheckedEventArgs e)
        {
            LTECell cell = e.Item.Tag as LTECell;
            if (cell != null)
            {
                if(e.Item.Checked)
                {
                    if (!checkedCells.Contains(cell))
                    {
                        checkedCells.Add(cell);
                    }
                }
                else
                {
                    if (checkedCells.Contains(cell))
                    {
                        checkedCells.Remove(cell);
                    }
                }
            }
        }

        private void simpleBtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void simpleBtnOK_Click(object sender, EventArgs e)
        {
            if (checkedCells.Count <= 0 || showCells.Count <= 0)
            {
                MessageBox.Show("请选择小区", "提示");
                return;
            }
            this.DialogResult = DialogResult.OK;
        }

        public List<LTECell> GetCheckedCells()
        {
            return checkedCells;
        }
    }
}
