﻿using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage._45G700MHighReverseFlowCoverage
{
    public abstract class DiyInsertResultInfo
    {
        public string ErrMsg { get; protected set; }
        protected abstract string tableName { get; }
        protected List<CellResult> cellInfos = null;
        protected DiyInsertResultInfo(List<CellResult> cellInfos)
        {
            this.cellInfos = cellInfos;
        }

        #region 直连主库进行BCP
        public virtual bool Bcp(SqlConnectionStringBuilder sb)
        {
            string errMsg = BcpHelper<List<CellResult>>.Bcp(sb, tableName, addDatas, cellInfos);

            if (!string.IsNullOrEmpty(errMsg))
            {
                MessageBox.Show(errMsg);
                return false;
            }

            return true;
        }

        protected virtual void addDatas(BCPStore bcp, List<CellResult> cellInfos)
        {
            foreach (var cellInfo in cellInfos)
            {
                var info = cellInfo.CellInfo;
                object[] values = new object[] { info.Province, info.ProvinceID, info.City
                        , info.CityID, info.Region, info.Grid, info.Enodebid, info.CellName
                        , info.Type, info.TAC, info.CI, info.TAC16, info.CI16
                        , cellInfo.SerialNumber };
                bcp.AddData(values);
            }
        }
        #endregion
    }

    public class DiyInsertNRResultInfo : DiyInsertResultInfo
    {
        protected override string tableName { get { return "tb_high_reverse_flow_coverage_nr700m_cell_result"; } }

        public DiyInsertNRResultInfo(List<CellResult> cellInfos)
            : base(cellInfos)
        {
        }
    }

    public class DiyInsertLTEResultInfo : DiyInsertResultInfo
    {
        protected override string tableName { get { return "tb_high_reverse_flow_coverage_lte700m_cell_result"; } }

        public DiyInsertLTEResultInfo(List<CellResult> cellInfos)
            : base(cellInfos)
        {
        }
    }
}

