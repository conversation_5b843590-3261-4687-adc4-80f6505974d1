﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
using MasterCom.Util;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Func;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCompareWeakCoverRoad_GSM : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static ZTCompareWeakCoverRoad_GSM instance = null;
        public static ZTCompareWeakCoverRoad_GSM GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCompareWeakCoverRoad_GSM();
                    }
                }
            }
            return instance;
        }
        protected WeakCoverCondition_GSM weakCoverCondition = null;
        protected ZTCompareWeakCoverRoad_GSM()
            : base(MainModel.GetInstance())
        {
        }

        public override string Description
        {
            get
            {
                return "选择2个时间段分析文件，做弱覆盖的对比分析。";
            }
        }

        public override string Name
        {
            get { return "弱覆盖路段对比_GSM"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12077, this.Name);
        }

        /// <summary>
        /// 查询之前初始化各个变量
        /// </summary>
        protected override void getReadyBeforeQuery()
        {
            p1Files = new List<FileInfo>();
            p2Files = new List<FileInfo>();
            weakCoverGridOfPeriod1 = new List<ZTWeakCoverGrid>();
            weakCoverGridOfPeriod2 = new List<ZTWeakCoverGrid>();
            repeatGrids = new List<ZTWeakCoverGrid>();
            newGrids = new List<ZTWeakCoverGrid>();
            curRegionGridBounds = getGridBounds(condition.Geometorys.RegionBounds);
        }

        List<FileInfo> p1Files = null;//时间段1对应的文件
        List<FileInfo> p2Files = null;//时间段2对应的文件

        /// <summary>
        /// 已经获取了2个时间段的所有文件信息，这里要先分析时间段1的文件，再分析时间段2的文件，从而对比计算
        /// </summary>
        protected override void analyseFiles()
        {
            try
            {
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (weakCoverCondition.Period1.Contains(JavaDate.GetDateTimeFromMilliseconds(fileInfo.BeginTime * 1000L)) 
                        || weakCoverCondition.Period1.Contains(JavaDate.GetDateTimeFromMilliseconds(fileInfo.EndTime * 1000L)))
                    {
                        p1Files.Add(fileInfo);//时间段1的文件
                    }
                    else if (weakCoverCondition.Period2.Contains(JavaDate.GetDateTimeFromMilliseconds(fileInfo.BeginTime * 1000L)) 
                        || weakCoverCondition.Period2.Contains(JavaDate.GetDateTimeFromMilliseconds(fileInfo.EndTime * 1000L)))
                    {
                        p2Files.Add(fileInfo);//时间段2的文件
                    }
                }
                clearDataBeforeAnalyseFiles();
                if (MainModel.IsBackground)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("共读取待分析文件" + MainModel.FileInfos.Count + "个...");
                }
                replayFiles(p1Files);//先分析时间段1的文件
                replayFiles(p2Files);
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        /// <summary>
        /// 逐个回放对应的时间段文件
        /// </summary>
        /// <param name="files2replay">要回放的文件</param>
        protected void replayFiles(List<FileInfo> files2replay)
        {
            int iloop = 0;
            foreach (FileInfo fileInfo in files2replay)
            {
                if (MainModel.IsBackground)
                {
                    if (MainModel.BackgroundStopRequest)
                    {
                        break;
                    }
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("正在分析" + FuncType.ToString() +
                        SubFuncType.ToString() + "类" + Name + "，当前文件" + (++iloop) + "/" + files2replay.Count +
                        "个...文件名：" + fileInfo.Name);
                }
                else
                {
                    WaitBox.Text = "正在分析文件(" + (++iloop) + "/" + files2replay.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / files2replay.Count);
                }
                if (filterFile(fileInfo))
                {
                    continue;
                }

                curAnaFileInfo = fileInfo;
                Condition.FileInfos.Clear();
                Condition.FileInfos.Add(fileInfo);
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                replay();//回放
                if (WaitBox.CancelRequest)
                {
                    break;
                }
            }
        }

        /// <summary>
        /// 文件已回放，开始统计该文件
        /// </summary>
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                if (isFileInPeriod(fileDataManager.FileID, p1Files))//时段1的文件分析
                {
                    doWithFileOfPeriod(true, fileDataManager);
                }
                else if (isFileInPeriod(fileDataManager.FileID, p2Files))//时间段2的文件分析
                {
                    doWithFileOfPeriod(false, fileDataManager);
                }
            }
        }

        /// <summary>
        /// 判断某文件是否为某时间段内的文件
        /// </summary>
        /// <param name="fileId">要判断的文件ID</param>
        /// <param name="filesInPeroid">某时间段内的文件</param>
        /// <returns>在时间段内true;不在false</returns>
        protected bool isFileInPeriod(int fileId, List<FileInfo> filesInPeroid)
        {
            bool inThisPeriod = false;
            foreach (FileInfo fi in filesInPeroid)
            {
                if (fi.ID == fileId)
                {
                    inThisPeriod = true;
                    break;
                }
            }
            return inThisPeriod;
        }

        protected List<ZTWeakCoverGrid> weakCoverGridOfPeriod1 = null;
        protected List<ZTWeakCoverGrid> weakCoverGridOfPeriod2 = null;
        protected void doWithFileOfPeriod(bool isPeriod1File, DTFileDataManager fileMng)
        {
            List<TestPoint> testPointList = fileMng.TestPoints;
            ZTWeakCoverGrid info = null;
            TestPoint prePoint = null;//前一点
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];
                if (isValidTestPoint(testPoint))
                {
                    info = addZTWeakCoverGrid(isPeriod1File, info, prePoint, testPoint);
                    prePoint = testPoint;
                }
                else//区域外的采样点
                {
                    saveWeakCoverInfo(isPeriod1File,ref info);
                }
            }
            //避免遗漏每个文件的最后一段
            saveWeakCoverInfo(isPeriod1File, ref info);
        }

        private ZTWeakCoverGrid addZTWeakCoverGrid(bool isPeriod1File, ZTWeakCoverGrid info, TestPoint prePoint, TestPoint testPoint)
        {
            short? rxLev = (short?)testPoint["RxLevSub"];
            if (!weakCoverCondition.MatchWeakRxLev(rxLev))//先作指标的判断，后作距离的判断
            {//不符合设置的弱覆盖条件
                saveWeakCoverInfo(isPeriod1File, ref info);
            }
            else//指标符合弱覆盖，还需要进行距离条件判断
            {
                if (info == null)//弱覆盖开始
                {
                    info = new ZTWeakCoverGrid(curGridSpan, curRegionGridBounds);
                    info.Add(float.Parse(rxLev.ToString()), 0, testPoint);
                }
                else
                {//上一点为弱覆盖点
                    double dis = MathFuncs.GetDistance(prePoint.Longitude, prePoint.Latitude, testPoint.Longitude, testPoint.Latitude);
                    if (weakCoverCondition.Match2TestpointsMaxDistance(dis))
                    {//符合两采样点之间的距离门限
                        info.Add(float.Parse(rxLev.ToString()), dis, testPoint);
                    }
                    else
                    {//两采样点距离不符合，该点开始新的弱覆盖
                        saveWeakCoverInfo(isPeriod1File, ref info);
                        info = new ZTWeakCoverGrid(curGridSpan, curRegionGridBounds);
                        info.Add(float.Parse(rxLev.ToString()), dis, testPoint);
                    }
                }
            }

            return info;
        }

        protected List<ZTWeakCoverGrid> repeatGrids = null;
        protected List<ZTWeakCoverGrid> newGrids = null;
        protected override void doSomethingAfterAnalyseFiles()
        {
            foreach (ZTWeakCoverGrid grid in weakCoverGridOfPeriod2)
            {
                bool repeat = false;
                foreach (ZTWeakCoverGrid gridBase in weakCoverGridOfPeriod1)
                {
                    if (gridBase.CanMerge(grid))
                    {//有重叠
                        repeatGrids.Add(grid);
                        grid.SN = repeatGrids.Count;
                        repeat = true;
                        break;
                    }
                }
                if (!repeat)
                {//新的弱覆盖路段
                    newGrids.Add(grid);
                    grid.SN = newGrids.Count;
                }
            }
        }

        private void saveWeakCoverInfo(bool isPeriod1File, ref ZTWeakCoverGrid info)
        {
            if (info == null || !weakCoverCondition.MatchWeakCoverMinDistance(info.Distance))
            {//不符合最小持续距离
                info = null;
                return;
            }
            if (isPeriod1File)
            {
                if (!weakCoverGridOfPeriod1.Contains(info))
                {
                    info.SN = weakCoverGridOfPeriod1.Count + 1;
                    info.GetResult(saveTestPoints);
                    weakCoverGridOfPeriod1.Add(info);
                }
            }
            else
            {
                if (!weakCoverGridOfPeriod2.Contains(info))
                {
                    info.SN = weakCoverGridOfPeriod2.Count + 1;
                    info.GetResult(saveTestPoints);
                    weakCoverGridOfPeriod2.Add(info);
                }
            }
            info = null;
        }

        private DbRect curRegionGridBounds = null;
        private double curGridSpan = double.NaN;

        private DbRect getGridBounds(DbRect queryBounds)
        {
            curGridSpan = weakCoverCondition.RoadGridSpan / 10 * 0.0001;
            DbRect gridBounds = new DbRect();
            gridBounds.x1 = 0.0000001 * ((int)(queryBounds.x1 * 10000000) / ((int)(curGridSpan * 10000000))) * ((int)(curGridSpan * 10000000));
            //避免漏掉最右边栅格
            gridBounds.x2 = 0.0000001 * ((int)(queryBounds.x2 * 10000000) / ((int)(curGridSpan * 10000000))) * ((int)(curGridSpan * 10000000)) + curGridSpan;
            //避免漏掉最上面栅格
            gridBounds.y2 = 0.0000001 * ((int)(queryBounds.y2 * 10000000) / ((int)(curGridSpan * 10000000))) * ((int)(curGridSpan * 10000000)) + curGridSpan;
            gridBounds.y1 = 0.0000001 * ((int)(queryBounds.y1 * 10000000) / ((int)(curGridSpan * 10000000))) * ((int)(curGridSpan * 10000000));
            return gridBounds;
        }

        protected override void fireShowForm()
        {
            GSMWeakCoverRoadCompareForm frm = null;
            frm = MainModel.GetObjectFromBlackboard(typeof(GSMWeakCoverRoadCompareForm)) as GSMWeakCoverRoadCompareForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new GSMWeakCoverRoadCompareForm(MainModel);
            }
            frm.FillData(weakCoverGridOfPeriod1, repeatGrids, newGrids);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            weakCoverGridOfPeriod1 = null;
            weakCoverGridOfPeriod2 = null;
            repeatGrids = null;
            newGrids = null;
        }
        protected bool saveTestPoints = true;

        /// <summary>
        /// 弹出条件设置窗口，获取条件
        /// </summary>
        /// <returns></returns>
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                weakCoverCondition = new WeakCoverCondition_GSM();
                saveTestPoints = false;
                return true;
            }
            GSMCopareWeakCoverRoadSettingDlg conditionDlg = new GSMCopareWeakCoverRoadSettingDlg(weakCoverCondition);
            if (conditionDlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            weakCoverCondition = conditionDlg.GetCondition();
            condition.Periods.Clear();
            condition.Periods.Add(weakCoverCondition.Period1);
            condition.Periods.Add(weakCoverCondition.Period2);
            return true;
        }
    }

    public class WeakCoverCondition_GSM
    {
        public short RxLevMax { get; set; } = -85;
        public double WeakCoverDistanceMin { get; set; } = 50;//最小持续距离
        public double DistanceOf2TestpointMax { get; set; } = 50;
        public TimePeriod Period1 { get; set; }
        public TimePeriod Period2 { get; set; }
        public double RoadGridSpan { get; set; } = 50;//道路栅格大小

        /// <summary>
        /// 是否符合设置条件
        /// </summary>
        /// <param name="rscp"></param>
        /// <param name="pccpchC2i"></param>
        /// <param name="dpchC2i"></param>
        /// <returns>各个指标都小于等于设定的条件值，返回true，否则false</returns>
        public bool MatchWeakRxLev(short? rxLev)
        {
            return rxLev != null && rxLev >= -120 && rxLev <= -10 && rxLev <= RxLevMax;
        }

        public bool MatchWeakCoverMinDistance(double distance)
        {
            return distance >= WeakCoverDistanceMin;
        }

        public bool Match2TestpointsMaxDistance(double distance)
        {
            return distance <= DistanceOf2TestpointMax;
        }
    }
}
