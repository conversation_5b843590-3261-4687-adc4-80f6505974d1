﻿using MasterCom.ES.Data;
using MasterCom.RAMS.Chris.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class GSMWeakCoverReasonAnaBase : GSMOverCoverReasonAnaBase
    {
        GSMWeakCoverReasonCondtion weakCoverCond = new GSMWeakCoverReasonCondtion();
        protected GSMWeakCoverReasonAnaBase()
            : base()
        {
            isOverCover = false;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12094, this.Name);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            WeakCoverReasonAnaSetForm dlg = new WeakCoverReasonAnaSetForm();
            dlg.SetCondition(weakCoverCond);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            weakCoverCond = dlg.GetCondition();
            return true;
        }
        protected override void fireShowForm()
        {
            GSMOverCoverAnaListForm form = MainModel.GetInstance().GetObjectFromBlackboard(typeof(GSMOverCoverAnaListForm)) as GSMOverCoverAnaListForm;
            if (form == null || form.IsDisposed)
            {
                form = new GSMOverCoverAnaListForm(MainModel);
            }
            form.FillData(regionInfoDic, cellInfoDic, false);
            form.Show(MainModel.MainForm);
        }
        protected override bool isTpRxLevValid(TestPoint tp)
        {
            int? rxLev = (int?)(short?)tp["RxLevSub"];
            if (rxLev != null && rxLev < weakCoverCond.RxLevWeakGate)
            {
                return true;
            }
            return false;
        }
        protected override string getResult(int curTpIndex, List<TestPoint> pointsList, List<Event> evtList)
        {
            TestPoint tp = pointsList[curTpIndex];
            bool hasResult = false;
            string reason = "";
            foreach (var InventoryPair in weakCoverCond.InventoryDic)
            {
                if (hasResult)
                {
                    break;
                }
                if (!InventoryPair.Value)//未选择该项
                {
                    continue;
                }
                reason = InventoryPair.Key;
                hasResult = judgeHasResult(curTpIndex, pointsList, tp, hasResult, reason);
            }

            if (hasResult)
            {
                return reason;
            }
            else
            {
                return "其它";
            }
        }

        private bool judgeHasResult(int curTpIndex, List<TestPoint> pointsList, TestPoint tp, bool hasResult, string reason)
        {
            switch (reason)
            {
                case "缺少基站":
                    if (isPoorBts(tp, weakCoverCond.PoorBtsDisGate))
                    {
                        hasResult = true;
                    }
                    break;
                case "邻区漏配":
                    if (isNCellLackSet(tp, weakCoverCond.LackNcellDisGate))
                    {
                        hasResult = true;
                    }
                    break;
                case "室分泄漏":
                    if (isIndoor(tp))
                    {
                        hasResult = true;
                    }
                    break;
                case "主服小区覆盖不足":
                    if (isMainCellCoverWeak(tp))
                    {
                        hasResult = true;
                    }
                    break;
                case "邻小区覆盖不足":
                    if (isNCellCoverWeak(tp))
                    {
                        hasResult = true;
                    }
                    break;
                case "覆盖不稳定":
                    if (isUnstabitilyCover(pointsList, curTpIndex))
                    {
                        hasResult = true;
                    }
                    break;
                default:
                    break;
            }

            return hasResult;
        }

        protected bool isMainCellCoverWeak(TestPoint tp)
        {
            int? rxLev = (int?)(short?)tp["RxLevSub"];
            int? nMaxRxLev = (int?)(short?)tp["N_RxLev", 0];
            if (rxLev != null && nMaxRxLev != null && rxLev - nMaxRxLev < weakCoverCond.RxLevMainLessNCellGate)
            {
                return true;
            }
            return false;
        }
        protected bool isNCellCoverWeak(TestPoint tp)
        {
            int validNCellCount = 0;
            for (int i = 0; i < 50; i++)
            {
                int? nRxLev = (int?)(short?)tp["N_RxLev", i];
                if (nRxLev == null)
                {
                    break;
                }
                else
                {
                    validNCellCount++;
                }
            }
            return validNCellCount < weakCoverCond.NCellWeakCountGate;
        }
        protected bool isUnstabitilyCover(List<TestPoint> pointsList, int curIndex)//是否覆盖不稳定
        {
            TestPoint curTp = pointsList[curIndex];
            for (int i = curIndex - 1; i >= 0; i--)
            {
                TestPoint tp = pointsList[i];
                if ((curTp.DateTime - tp.DateTime).TotalSeconds > weakCoverCond.LastSecondsBeforeWeak)
                {
                    break;
                }
                int? rxLev = (int?)(short?)tp["RxLevSub"];
                if (rxLev != null && rxLev < weakCoverCond.RxLevUnstabitilyGate)
                {
                    return false;
                }
            }
            return true;
        }
    }
}
