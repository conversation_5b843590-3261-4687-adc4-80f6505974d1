﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTProblemTrackGSMConditionForm : DevExpress.XtraEditors.XtraForm
    {
        public CQTProblemTrackGSMConditionForm()
        {
            InitializeComponent();
            init();
        }
        bool isAdd = true;
        public Dictionary<string, ConditionSetForm> conditionDic { get; set; }
        public List<string> stateNameListTem
        {
            get
            {
                List<string> stateNameTem = new List<string>();
                for (int i = 0; i < cbxState.Items.Count; i++)
                {
                    if (!stateNameTem.Contains(cbxState.Items[i].ToString()))
                        stateNameTem.Add(cbxState.Items[i].ToString());
                }
                return stateNameTem;
            }
        }
        public string strSelectSateName
        {
            get
            {
                string selectName = "";
                selectName = cbxState.SelectedItem.ToString();
                return selectName;
            }
        }
        public void init()
        {
            conditionDic = new Dictionary<string, ConditionSetForm>();
            ConditionSetForm conditionTem = new ConditionSetForm();
            conditionDic.Add(conditionTem.StrStateName, conditionTem);
            this.Height = 140;
            panel2.Visible = false;
            panel3.Visible = false;
            cbxState.Items.Add("所有状态");
            cbxState.SelectedIndex = 0;

            cbJudgeOne.Items.Add(">=");
            cbJudgeOne.Items.Add("<=");
            cbJudgeOne.Items.Add("=");
            cbJudgeOne.Items.Add(">");
            cbJudgeOne.Items.Add("<");
            cbJudgeOne.SelectedIndex = 0;

            cbJudgeTwo.Items.Add(">=");
            cbJudgeTwo.Items.Add("<=");
            cbJudgeTwo.Items.Add("=");
            cbJudgeTwo.Items.Add(">");
            cbJudgeTwo.Items.Add("<");
            cbJudgeTwo.SelectedIndex = 0;

            cbJudgeThree.Items.Add(">=");
            cbJudgeThree.Items.Add("<=");
            cbJudgeThree.Items.Add("=");
            cbJudgeThree.Items.Add(">");
            cbJudgeThree.Items.Add("<");
            cbJudgeThree.SelectedIndex = 0;

            cbJudgeFour.Items.Add(">=");
            cbJudgeFour.Items.Add("<=");
            cbJudgeFour.Items.Add("=");
            cbJudgeFour.Items.Add(">");
            cbJudgeFour.Items.Add("<");
            cbJudgeFour.SelectedIndex = 0;
        }
        /// <summary>
        /// 编辑状态
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnEditState_Click(object sender, EventArgs e)
        {
            if (cbxStateLsit.SelectedItem == null)
            {
                MessageBox.Show("没有选择需要编辑的状态项");
                return;
            }
            this.Height = 570;
            panel3.Visible = true;
            stateName.Enabled = false;
            isAdd = false;
            string stateTem = "";
            
            if (cbxStateLsit.SelectedItem.ToString().Split(',').Length > 0
                && cbxStateLsit.SelectedItem.ToString().Split(',')[0].Split(':').Length > 1)
            {
                stateTem = cbxStateLsit.SelectedItem.ToString().Split(',')[0].Split(':')[1];
                if (conditionDic.ContainsKey(stateTem))
                {
                    stateName.Text = conditionDic[stateTem].StrStateName;
                    testNumber.Value = (decimal)conditionDic[stateTem].ITestNum;
                    cbJudgeOne.SelectedItem = conditionDic[stateTem].StrTestNumJudge;
                    normalNumber.Value = (decimal)conditionDic[stateTem].INormalNum;
                    cbJudgeTwo.SelectedItem = conditionDic[stateTem].StrNormalNumJudge;
                    abnormalNumber.Value = (decimal)conditionDic[stateTem].IAbnormalNum;
                    cbJudgeThree.SelectedItem = conditionDic[stateTem].StrAbnormalNum;
                    lastNormalNumber.Value = (decimal)conditionDic[stateTem].ILastNormalNum;
                    cbJudgeFour.SelectedItem = conditionDic[stateTem].StrLastNormalNum;
                }
            }
            else
            {
                MessageBox.Show("所选项有异常，请重新编辑");
            }
            
        }
        /// <summary>
        /// 增加状态
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnAddState_Click(object sender, EventArgs e)
        {
            this.Height = 570;
            panel3.Visible = true;
            stateName.Enabled = true;
            isAdd = true;
        }

        private void btnSubmit_Click(object sender, EventArgs e)
        {
            string strSetItem = "";
            if (stateName.Text == null || stateName.Text == "")
            {
                MessageBox.Show("状态名称不能为空，请重新输入");
                return;
            }
            strSetItem += "第" + cbxState.Items.Count + "优先级名称:" + stateName.Text + ",";
            strSetItem += "测试:" + cbJudgeOne.SelectedItem.ToString() + testNumber.Value + ",";
            strSetItem += "正常:" + cbJudgeTwo.SelectedItem.ToString() + normalNumber.Value + ";";

            if (isAdd && cbxState.Items.Contains(stateName.Text))
            {
                MessageBox.Show("已有改名称的状态，不能添加，但可以编辑");
                return;
            }

            if (conditionDic.ContainsKey(stateName.Text))
            {
                if (DialogResult.Yes != MessageBox.Show("是否对该状态进行覆盖重置？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Information))
                    return;
                int idex = index();
                cbxStateLsit.Items.RemoveAt(idex);
                cbxStateLsit.Items.Insert(idex, strSetItem);

                conditionDic[stateName.Text].ITestNum = (int)testNumber.Value;
                conditionDic[stateName.Text].StrTestNumJudge = cbJudgeOne.SelectedItem.ToString();
                conditionDic[stateName.Text].INormalNum = (int)normalNumber.Value;
                conditionDic[stateName.Text].StrNormalNumJudge = cbJudgeTwo.SelectedItem.ToString();
                conditionDic[stateName.Text].IAbnormalNum = (int)abnormalNumber.Value;
                conditionDic[stateName.Text].StrAbnormalNum = cbJudgeThree.SelectedItem.ToString();
                conditionDic[stateName.Text].ILastNormalNum = (int)lastNormalNumber.Value;
                conditionDic[stateName.Text].StrLastNormalNum = cbJudgeFour.SelectedItem.ToString();
                conditionDic[stateName.Text].ColorClose = colEdit.Color;

            }
            else
            {
                cbxStateLsit.Items.Add(strSetItem);
                if (cbxStateLsit.Items.Count > 1)
                    cbxStateLsit.SelectedIndex = cbxStateLsit.Items.Count - 1;
                ConditionSetForm condition = new ConditionSetForm();
                condition.StrStateName = stateName.Text;
                condition.ITestNum = (int)testNumber.Value;
                condition.StrTestNumJudge = cbJudgeOne.SelectedItem.ToString();
                condition.INormalNum = (int)normalNumber.Value;
                condition.StrNormalNumJudge = cbJudgeTwo.SelectedItem.ToString();
                condition.IAbnormalNum = (int)abnormalNumber.Value;
                condition.StrAbnormalNum = cbJudgeThree.SelectedItem.ToString();
                condition.ILastNormalNum = (int)lastNormalNumber.Value;
                condition.StrLastNormalNum = cbJudgeFour.SelectedItem.ToString();
                condition.ColorClose = colEdit.Color;
                if (!conditionDic.ContainsKey(stateName.Text))
                    conditionDic.Add(stateName.Text, condition);
                if (!cbxState.Items.Contains(stateName.Text))
                    cbxState.Items.Add(stateName.Text);
            }

        }
        private int index()
        {
            int index = 0;
            for(int i = 0;i< cbxStateLsit.Items.Count -1;i++)
            { 
                string tem = cbxStateLsit.Items[i].ToString().Split(',')[0].Split(':')[1];
                if (stateName.Text == tem)
                    index = i;
            }
            return index;
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void chb_CheckedChanged(object sender, EventArgs e)
        {
            if (chbDisplayEdit.Checked)
            {
                this.Height = 350;
            }
            else
            {
                this.Height = 140;
                panel3.Visible = false;
            }
            panel2.Visible = chbDisplayEdit.Checked;
        }
        /// <summary>
        /// 删除状态
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (cbxStateLsit.SelectedItem == null)
            {
                MessageBox.Show("请选择要删除的状态");
                return;
            }
            string stateTem = "";
            if (cbxStateLsit.SelectedItem.ToString().Split(',').Length > 0
               && cbxStateLsit.SelectedItem.ToString().Split(',')[0].Split(':').Length > 1)
            {
                stateTem = cbxStateLsit.SelectedItem.ToString().Split(',')[0].Split(':')[1];
            }
            else
            {
                MessageBox.Show("所选项有异常，请重新编辑");
                return;
            }
            cbxStateLsit.Items.Remove(cbxStateLsit.SelectedItem);
            conditionDic.Remove(stateTem);
        }
        /// <summary>
        /// 上移状态
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnup_Click(object sender, EventArgs e)
        {
            if (cbxStateLsit.Items.Count == 0)
                return;
            if (cbxStateLsit.SelectedItem == null)
            {
                MessageBox.Show("请选择要上移的状态项");
                return;
            }
            if (cbxStateLsit.SelectedIndex == 0)
            {
                MessageBox.Show("当前的状态已是最高项，无需上移");
                return;
            }
            
            int stateIDTem = cbxStateLsit.SelectedIndex;
            string stateTem = cbxStateLsit.Items[cbxStateLsit.SelectedIndex - 1].ToString();
            string curStateName = "";
            if (cbxStateLsit.Items[cbxStateLsit.SelectedIndex - 1].ToString().Split(',').Length > 0
               && cbxStateLsit.Items[cbxStateLsit.SelectedIndex - 1].ToString().Split(',')[0].Split(':').Length > 1)
            {
                curStateName = cbxStateLsit.Items[cbxStateLsit.SelectedIndex - 1].ToString().Split(',')[0].Split(':')[1];
                cbxStateLsit.Items.RemoveAt(stateIDTem - 1);
                cbxStateLsit.Items.Insert(stateIDTem, stateTem);
                cbxState.Items.RemoveAt(stateIDTem);
                cbxState.Items.Insert(stateIDTem + 1, curStateName);
                cbxStateLsit.SelectedIndex = stateIDTem - 1;
            }
            else
            {
                MessageBox.Show("所选项的上一项状态有异常，请重新编辑");
            }
        }
        /// <summary>
        /// 下移状态
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btndown_Click(object sender, EventArgs e)
        {
            if (cbxStateLsit.Items.Count == 0)
                return;
            if (cbxStateLsit.SelectedItem == null)
            {
                MessageBox.Show("请选择要下移的状态项");
                return;
            }
            if (cbxStateLsit.SelectedIndex == cbxStateLsit.Items.Count - 1)
            {
                MessageBox.Show("当前的状态已是最低项，无需下移");
                return;
            }
            int stateIDTem = cbxStateLsit.SelectedIndex;
            string stateTem = cbxStateLsit.SelectedItem.ToString();
            string curStateName = "";
            if (cbxStateLsit.SelectedItem.ToString().Split(',').Length > 0
                && cbxStateLsit.SelectedItem.ToString().Split(',')[0].Split(':').Length > 1)
            {
                curStateName = cbxStateLsit.SelectedItem.ToString().Split(',')[0].Split(':')[1];
                cbxStateLsit.Items.RemoveAt(stateIDTem);
                cbxStateLsit.Items.Insert(stateIDTem + 1, stateTem);
                cbxState.Items.RemoveAt(stateIDTem + 1);
                cbxState.Items.Insert(stateIDTem + 2, curStateName);
                cbxStateLsit.SelectedIndex = stateIDTem + 1;
            }
            else
            {
                MessageBox.Show("所选项的项状态有异常，请重新编辑");
            }
        }

        private void cbxStateLsit_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (!cbxState.Items.Contains(stateName.Text))
                cbxState.Items.Add(stateName.Text);
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }

    public class ConditionSetForm
    {
        /// <summary>
        /// 状态名称
        /// </summary>
        public string StrStateName { get; set; }
        /// <summary>
        /// 测试次数
        /// </summary>
        public int ITestNum { get; set; }
        /// <summary>
        /// 测试次数判断符号
        /// </summary>
        public string StrTestNumJudge { get; set; }
        /// <summary>
        /// 正常次数
        /// </summary>
        public int INormalNum { get; set; }
        /// <summary>
        /// 正常次数判断符号
        /// </summary>
        public string StrNormalNumJudge { get; set; }
        /// <summary>
        /// 异常次数
        /// </summary>
        public int IAbnormalNum { get; set; }
        /// <summary>
        /// 异常次数判断符号
        /// </summary>
        public string StrAbnormalNum { get; set; }
        /// <summary>
        /// 最后正常的次数
        /// </summary>
        public int ILastNormalNum { get; set; }
        /// <summary>
        /// 最后正常次数判断符号
        /// </summary>
        public string StrLastNormalNum { get; set; }
        private readonly SolidBrush brushSet;
        /// <summary>
        /// 状态颜色
        /// </summary>
        public Color ColorClose
        {
            get { return brushSet.Color; }
            set { brushSet.Color = Color.FromArgb(150, value); }
        }
        public ConditionSetForm()
        {
            StrStateName = "所有状态";
            ITestNum = 0;
            StrTestNumJudge = ">=";
            INormalNum = 0;
            StrNormalNumJudge = ">=";
            IAbnormalNum = 0;
            StrAbnormalNum = ">=";
            ILastNormalNum = 0;
            StrLastNormalNum = ">=";
            brushSet = new SolidBrush(Color.FromArgb(150, 50, 102, 0));
        }
    }
}
