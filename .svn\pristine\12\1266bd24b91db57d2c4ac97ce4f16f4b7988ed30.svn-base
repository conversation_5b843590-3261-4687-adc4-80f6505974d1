﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TDAntMRAnaForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.XYDiagram xyDiagram1 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel1 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel2 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram2 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series2 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel3 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel4 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram3 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series3 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel5 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel6 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram4 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series4 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel7 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel8 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram5 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series5 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel9 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel10 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.RadarDiagram radarDiagram1 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series6 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel1 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint1 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint2 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint3 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint4 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint5 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView1 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.Series series7 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel2 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView2 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel3 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView3 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.RadarDiagram radarDiagram2 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series8 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel4 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint6 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint7 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint8 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint9 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint10 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView4 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.Series series9 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel5 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView5 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel6 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView6 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.RadarDiagram radarDiagram3 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series10 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel7 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint11 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint12 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint13 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint14 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint15 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView7 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.Series series11 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel8 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView8 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel9 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView9 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.RadarDiagram radarDiagram4 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series12 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel10 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint16 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint17 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint18 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint19 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint20 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView10 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.Series series13 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel11 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView11 = new DevExpress.XtraCharts.RadarPointSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel12 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarPointSeriesView radarPointSeriesView12 = new DevExpress.XtraCharts.RadarPointSeriesView();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage4 = new DevExpress.XtraTab.XtraTabPage();
            this.btnNextpage = new System.Windows.Forms.Button();
            this.btnPrevpage = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.txtPage = new System.Windows.Forms.TextBox();
            this.labPage = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.btnGo = new System.Windows.Forms.Button();
            this.labNum = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.btnSearch = new System.Windows.Forms.Button();
            this.txtCellName = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.dataGridViewTdCell = new System.Windows.Forms.DataGridView();
            this.colIndex = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column11 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column14 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colvender = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column41 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colcgi = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colcovertype = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dirCol = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column9 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column10 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column12 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column13 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column15 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column16 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column17 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column18 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column19 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column20 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column23 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column24 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column25 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column26 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column27 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column28 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column21 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column22 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShowChart = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowSimulation = new System.Windows.Forms.ToolStripMenuItem();
            this.导出CSVToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportWholeExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl4 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox12 = new System.Windows.Forms.GroupBox();
            this.rtbDesc = new System.Windows.Forms.RichTextBox();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.chartControlAoa = new DevExpress.XtraCharts.ChartControl();
            this.groupBox11 = new System.Windows.Forms.GroupBox();
            this.chartControlSinr = new DevExpress.XtraCharts.ChartControl();
            this.groupBox10 = new System.Windows.Forms.GroupBox();
            this.chartControlRsrp = new DevExpress.XtraCharts.ChartControl();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.chartControlPower = new DevExpress.XtraCharts.ChartControl();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.chartControlTA = new DevExpress.XtraCharts.ChartControl();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl5 = new DevExpress.XtraEditors.GroupControl();
            this.cbPart = new System.Windows.Forms.ComboBox();
            this.label7 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.chartCellLine = new DevExpress.XtraCharts.ChartControl();
            this.groupBox15 = new System.Windows.Forms.GroupBox();
            this.chartCellPoint = new DevExpress.XtraCharts.ChartControl();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.cbBtsPart = new System.Windows.Forms.ComboBox();
            this.label8 = new System.Windows.Forms.Label();
            this.cbCellName = new System.Windows.Forms.ComboBox();
            this.label6 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.chartEnodebLine = new DevExpress.XtraCharts.ChartControl();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.chartEnodebPoint = new DevExpress.XtraCharts.ChartControl();
            this.xtraTabPage5 = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.cbNPart = new System.Windows.Forms.ComboBox();
            this.label9 = new System.Windows.Forms.Label();
            this.dataGridViewRight = new System.Windows.Forms.DataGridView();
            this.dataGridViewLeft = new System.Windows.Forms.DataGridView();
            this.btnDraw = new System.Windows.Forms.Button();
            this.btnOneOut = new System.Windows.Forms.Button();
            this.btnOneIn = new System.Windows.Forms.Button();
            this.btnAllOut = new System.Windows.Forms.Button();
            this.btnAllIn = new System.Windows.Forms.Button();
            this.tbDistance = new System.Windows.Forms.TextBox();
            this.serBtn = new System.Windows.Forms.Button();
            this.label10 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewTdCell)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).BeginInit();
            this.groupControl4.SuspendLayout();
            this.groupBox12.SuspendLayout();
            this.groupBox8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlAoa)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).BeginInit();
            this.groupBox11.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlSinr)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).BeginInit();
            this.groupBox10.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRsrp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).BeginInit();
            this.groupBox7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlPower)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel8)).BeginInit();
            this.groupBox9.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlTA)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel10)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl5)).BeginInit();
            this.groupControl5.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartCellLine)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView3)).BeginInit();
            this.groupBox15.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartCellPoint)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView6)).BeginInit();
            this.xtraTabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartEnodebLine)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView9)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartEnodebPoint)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView12)).BeginInit();
            this.xtraTabPage5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewRight)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewLeft)).BeginInit();
            this.SuspendLayout();
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage4;
            this.xtraTabControl1.Size = new System.Drawing.Size(1184, 650);
            this.xtraTabControl1.TabIndex = 1;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage4,
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3,
            this.xtraTabPage5});
            this.xtraTabControl1.SelectedPageChanged += new DevExpress.XtraTab.TabPageChangedEventHandler(this.xtraTabControl1_SelectedPageChanged);
            // 
            // xtraTabPage4
            // 
            this.xtraTabPage4.Controls.Add(this.btnNextpage);
            this.xtraTabPage4.Controls.Add(this.btnPrevpage);
            this.xtraTabPage4.Controls.Add(this.label5);
            this.xtraTabPage4.Controls.Add(this.txtPage);
            this.xtraTabPage4.Controls.Add(this.labPage);
            this.xtraTabPage4.Controls.Add(this.label4);
            this.xtraTabPage4.Controls.Add(this.btnGo);
            this.xtraTabPage4.Controls.Add(this.labNum);
            this.xtraTabPage4.Controls.Add(this.label3);
            this.xtraTabPage4.Controls.Add(this.label2);
            this.xtraTabPage4.Controls.Add(this.btnSearch);
            this.xtraTabPage4.Controls.Add(this.txtCellName);
            this.xtraTabPage4.Controls.Add(this.label1);
            this.xtraTabPage4.Controls.Add(this.dataGridViewTdCell);
            this.xtraTabPage4.Name = "xtraTabPage4";
            this.xtraTabPage4.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage4.Text = "小区MR数据集";
            // 
            // btnNextpage
            // 
            this.btnNextpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNextpage.Location = new System.Drawing.Point(866, 594);
            this.btnNextpage.Name = "btnNextpage";
            this.btnNextpage.Size = new System.Drawing.Size(33, 23);
            this.btnNextpage.TabIndex = 18;
            this.btnNextpage.Text = ">>";
            this.btnNextpage.UseVisualStyleBackColor = true;
            this.btnNextpage.Click += new System.EventHandler(this.btnNextpage_Click);
            // 
            // btnPrevpage
            // 
            this.btnPrevpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPrevpage.Location = new System.Drawing.Point(827, 594);
            this.btnPrevpage.Name = "btnPrevpage";
            this.btnPrevpage.Size = new System.Drawing.Size(33, 23);
            this.btnPrevpage.TabIndex = 17;
            this.btnPrevpage.Text = "<<";
            this.btnPrevpage.UseVisualStyleBackColor = true;
            this.btnPrevpage.Click += new System.EventHandler(this.btnPrevpage_Click);
            // 
            // label5
            // 
            this.label5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(763, 597);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(19, 14);
            this.label5.TabIndex = 14;
            this.label5.Text = "页";
            // 
            // txtPage
            // 
            this.txtPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtPage.Location = new System.Drawing.Point(698, 594);
            this.txtPage.Name = "txtPage";
            this.txtPage.Size = new System.Drawing.Size(63, 22);
            this.txtPage.TabIndex = 13;
            // 
            // labPage
            // 
            this.labPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labPage.AutoSize = true;
            this.labPage.Location = new System.Drawing.Point(598, 599);
            this.labPage.Name = "labPage";
            this.labPage.Size = new System.Drawing.Size(14, 14);
            this.labPage.TabIndex = 12;
            this.labPage.Text = "0";
            // 
            // label4
            // 
            this.label4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(555, 598);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(43, 14);
            this.label4.TabIndex = 11;
            this.label4.Text = "个，共";
            // 
            // btnGo
            // 
            this.btnGo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnGo.Location = new System.Drawing.Point(782, 594);
            this.btnGo.Name = "btnGo";
            this.btnGo.Size = new System.Drawing.Size(33, 23);
            this.btnGo.TabIndex = 9;
            this.btnGo.Text = "GO";
            this.btnGo.UseVisualStyleBackColor = true;
            this.btnGo.Click += new System.EventHandler(this.btnGo_Click);
            // 
            // labNum
            // 
            this.labNum.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labNum.AutoSize = true;
            this.labNum.Location = new System.Drawing.Point(510, 599);
            this.labNum.Name = "labNum";
            this.labNum.Size = new System.Drawing.Size(14, 14);
            this.labNum.TabIndex = 8;
            this.labNum.Text = "0";
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(632, 598);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(67, 14);
            this.label3.TabIndex = 7;
            this.label3.Text = "页，跳转至";
            // 
            // label2
            // 
            this.label2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(434, 598);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(79, 14);
            this.label2.TabIndex = 6;
            this.label2.Text = "总计小区共：";
            // 
            // btnSearch
            // 
            this.btnSearch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSearch.Location = new System.Drawing.Point(1128, 594);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(42, 23);
            this.btnSearch.TabIndex = 5;
            this.btnSearch.Text = "查找";
            this.btnSearch.UseVisualStyleBackColor = true;
            this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
            // 
            // txtCellName
            // 
            this.txtCellName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtCellName.Location = new System.Drawing.Point(966, 595);
            this.txtCellName.Name = "txtCellName";
            this.txtCellName.Size = new System.Drawing.Size(157, 22);
            this.txtCellName.TabIndex = 4;
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(905, 599);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(67, 14);
            this.label1.TabIndex = 3;
            this.label1.Text = "小区名称：";
            // 
            // dataGridViewTdCell
            // 
            this.dataGridViewTdCell.AllowUserToAddRows = false;
            this.dataGridViewTdCell.AllowUserToDeleteRows = false;
            this.dataGridViewTdCell.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridViewTdCell.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridViewTdCell.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewTdCell.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.colIndex,
            this.Column11,
            this.Column1,
            this.Column14,
            this.colvender,
            this.Column41,
            this.colcgi,
            this.colcovertype,
            this.Column3,
            this.dirCol,
            this.Column4,
            this.Column5,
            this.Column6,
            this.Column7,
            this.Column8,
            this.Column9,
            this.Column10,
            this.Column12,
            this.Column13,
            this.Column15,
            this.Column16,
            this.Column17,
            this.Column18,
            this.Column19,
            this.Column20,
            this.Column23,
            this.Column24,
            this.Column25,
            this.Column26,
            this.Column27,
            this.Column28,
            this.Column2,
            this.Column21,
            this.Column22});
            this.dataGridViewTdCell.ContextMenuStrip = this.contextMenuStrip;
            this.dataGridViewTdCell.Location = new System.Drawing.Point(0, 0);
            this.dataGridViewTdCell.Name = "dataGridViewTdCell";
            this.dataGridViewTdCell.RowTemplate.Height = 23;
            this.dataGridViewTdCell.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewTdCell.Size = new System.Drawing.Size(1177, 590);
            this.dataGridViewTdCell.TabIndex = 2;
            // 
            // colIndex
            // 
            this.colIndex.HeaderText = "序号";
            this.colIndex.Name = "colIndex";
            // 
            // Column11
            // 
            this.Column11.HeaderText = "地市";
            this.Column11.Name = "Column11";
            // 
            // Column1
            // 
            this.Column1.HeaderText = "小区英文名";
            this.Column1.Name = "Column1";
            // 
            // Column14
            // 
            this.Column14.HeaderText = "小区名";
            this.Column14.Name = "Column14";
            // 
            // colvender
            // 
            this.colvender.HeaderText = "主设备厂家";
            this.colvender.Name = "colvender";
            // 
            // Column41
            // 
            this.Column41.HeaderText = "是否匹配工参";
            this.Column41.Name = "Column41";
            // 
            // colcgi
            // 
            this.colcgi.HeaderText = "CGI";
            this.colcgi.Name = "colcgi";
            // 
            // colcovertype
            // 
            this.colcovertype.HeaderText = "覆盖类型";
            this.colcovertype.Name = "colcovertype";
            // 
            // Column3
            // 
            this.Column3.HeaderText = "小区频段";
            this.Column3.Name = "Column3";
            // 
            // dirCol
            // 
            this.dirCol.HeaderText = "方位角";
            this.dirCol.Name = "dirCol";
            // 
            // Column4
            // 
            this.Column4.HeaderText = "预置下倾角";
            this.Column4.Name = "Column4";
            // 
            // Column5
            // 
            this.Column5.HeaderText = "机械下倾角";
            this.Column5.Name = "Column5";
            // 
            // Column6
            // 
            this.Column6.HeaderText = "电调下倾角";
            this.Column6.Name = "Column6";
            // 
            // Column7
            // 
            this.Column7.HeaderText = "挂高";
            this.Column7.Name = "Column7";
            // 
            // Column8
            // 
            this.Column8.HeaderText = "上行吞吐量";
            this.Column8.Name = "Column8";
            // 
            // Column9
            // 
            this.Column9.HeaderText = "下行吞吐量";
            this.Column9.Name = "Column9";
            // 
            // Column10
            // 
            this.Column10.HeaderText = "无线接通率";
            this.Column10.Name = "Column10";
            // 
            // Column12
            // 
            this.Column12.HeaderText = "无线掉线率";
            this.Column12.Name = "Column12";
            // 
            // Column13
            // 
            this.Column13.HeaderText = "切换成功率";
            this.Column13.Name = "Column13";
            // 
            // Column15
            // 
            this.Column15.HeaderText = "ERAB建立成功率";
            this.Column15.Name = "Column15";
            // 
            // Column16
            // 
            this.Column16.HeaderText = "ERAB掉线率";
            this.Column16.Name = "Column16";
            // 
            // Column17
            // 
            this.Column17.HeaderText = "RSRP均值";
            this.Column17.Name = "Column17";
            // 
            // Column18
            // 
            this.Column18.HeaderText = "SINR均值";
            this.Column18.Name = "Column18";
            // 
            // Column19
            // 
            this.Column19.HeaderText = "95覆盖率";
            this.Column19.Name = "Column19";
            // 
            // Column20
            // 
            this.Column20.HeaderText = "110覆盖率";
            this.Column20.Name = "Column20";
            // 
            // Column23
            // 
            this.Column23.HeaderText = "MRO总采样点数";
            this.Column23.Name = "Column23";
            // 
            // Column24
            // 
            this.Column24.HeaderText = "重叠覆盖条件采样点数";
            this.Column24.Name = "Column24";
            // 
            // Column25
            // 
            this.Column25.HeaderText = "重叠覆盖指数";
            this.Column25.Name = "Column25";
            // 
            // Column26
            // 
            this.Column26.HeaderText = "过覆盖影响小区数";
            this.Column26.Name = "Column26";
            // 
            // Column27
            // 
            this.Column27.HeaderText = "高重叠覆盖小区";
            this.Column27.Name = "Column27";
            // 
            // Column28
            // 
            this.Column28.HeaderText = "过覆盖小区";
            this.Column28.Name = "Column28";
            // 
            // Column2
            // 
            this.Column2.HeaderText = "MR主覆盖角";
            this.Column2.Name = "Column2";
            // 
            // Column21
            // 
            this.Column21.HeaderText = "与天线工参偏差值";
            this.Column21.Name = "Column21";
            // 
            // Column22
            // 
            this.Column22.HeaderText = "天线分析";
            this.Column22.Name = "Column22";
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShowChart,
            this.miShowSimulation,
            this.导出CSVToolStripMenuItem,
            this.miExportWholeExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(221, 92);
            // 
            // miShowChart
            // 
            this.miShowChart.Name = "miShowChart";
            this.miShowChart.Size = new System.Drawing.Size(220, 22);
            this.miShowChart.Text = "显示统计图表及二维覆盖图";
            this.miShowChart.Click += new System.EventHandler(this.miShowChart_Click);
            // 
            // miShowSimulation
            // 
            this.miShowSimulation.Name = "miShowSimulation";
            this.miShowSimulation.Size = new System.Drawing.Size(220, 22);
            this.miShowSimulation.Text = "显示覆盖仿真(GIS地图)";
            this.miShowSimulation.Click += new System.EventHandler(this.miShowSimulation_Click);
            // 
            // 导出CSVToolStripMenuItem
            // 
            this.导出CSVToolStripMenuItem.Name = "导出CSVToolStripMenuItem";
            this.导出CSVToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.导出CSVToolStripMenuItem.Text = "拆分导出CSV";
            this.导出CSVToolStripMenuItem.Click += new System.EventHandler(this.导出CSVToolStripMenuItem_Click);
            // 
            // miExportWholeExcel
            // 
            this.miExportWholeExcel.Name = "miExportWholeExcel";
            this.miExportWholeExcel.Size = new System.Drawing.Size(220, 22);
            this.miExportWholeExcel.Text = "导出Excel";
            this.miExportWholeExcel.Click += new System.EventHandler(this.miExportWholeExcel_Click);
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.groupControl4);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage1.Text = "MR数据统计表";
            // 
            // groupControl4
            // 
            this.groupControl4.Controls.Add(this.groupBox12);
            this.groupControl4.Controls.Add(this.groupBox8);
            this.groupControl4.Controls.Add(this.groupBox11);
            this.groupControl4.Controls.Add(this.groupBox10);
            this.groupControl4.Controls.Add(this.groupBox7);
            this.groupControl4.Controls.Add(this.groupBox9);
            this.groupControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl4.Location = new System.Drawing.Point(0, 0);
            this.groupControl4.Name = "groupControl4";
            this.groupControl4.Size = new System.Drawing.Size(1177, 620);
            this.groupControl4.TabIndex = 2;
            this.groupControl4.Text = "MR测量项数据图表";
            // 
            // groupBox12
            // 
            this.groupBox12.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox12.Controls.Add(this.rtbDesc);
            this.groupBox12.Location = new System.Drawing.Point(592, 414);
            this.groupBox12.Name = "groupBox12";
            this.groupBox12.Size = new System.Drawing.Size(576, 188);
            this.groupBox12.TabIndex = 4;
            this.groupBox12.TabStop = false;
            this.groupBox12.Text = "MR测量项说明";
            // 
            // rtbDesc
            // 
            this.rtbDesc.Location = new System.Drawing.Point(6, 18);
            this.rtbDesc.Name = "rtbDesc";
            this.rtbDesc.ReadOnly = true;
            this.rtbDesc.Size = new System.Drawing.Size(564, 161);
            this.rtbDesc.TabIndex = 1;
            this.rtbDesc.Text = "";
            // 
            // groupBox8
            // 
            this.groupBox8.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox8.Controls.Add(this.chartControlAoa);
            this.groupBox8.Location = new System.Drawing.Point(592, 220);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Size = new System.Drawing.Size(576, 188);
            this.groupBox8.TabIndex = 4;
            this.groupBox8.TabStop = false;
            this.groupBox8.Text = "天线到达角（度）";
            // 
            // chartControlAoa
            // 
            xyDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlAoa.Diagram = xyDiagram1;
            this.chartControlAoa.Legend.Visible = false;
            this.chartControlAoa.Location = new System.Drawing.Point(6, 21);
            this.chartControlAoa.Name = "chartControlAoa";
            sideBySideBarSeriesLabel1.LineVisible = true;
            series1.Label = sideBySideBarSeriesLabel1;
            series1.Name = "Series 1";
            this.chartControlAoa.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1};
            sideBySideBarSeriesLabel2.LineVisible = true;
            this.chartControlAoa.SeriesTemplate.Label = sideBySideBarSeriesLabel2;
            this.chartControlAoa.Size = new System.Drawing.Size(564, 161);
            this.chartControlAoa.TabIndex = 1;
            // 
            // groupBox11
            // 
            this.groupBox11.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox11.Controls.Add(this.chartControlSinr);
            this.groupBox11.Location = new System.Drawing.Point(10, 414);
            this.groupBox11.Name = "groupBox11";
            this.groupBox11.Size = new System.Drawing.Size(576, 188);
            this.groupBox11.TabIndex = 1;
            this.groupBox11.TabStop = false;
            this.groupBox11.Text = "上行信噪比（dB）";
            // 
            // chartControlSinr
            // 
            xyDiagram2.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram2.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlSinr.Diagram = xyDiagram2;
            this.chartControlSinr.Legend.Visible = false;
            this.chartControlSinr.Location = new System.Drawing.Point(6, 21);
            this.chartControlSinr.Name = "chartControlSinr";
            sideBySideBarSeriesLabel3.LineVisible = true;
            series2.Label = sideBySideBarSeriesLabel3;
            series2.Name = "Series 1";
            this.chartControlSinr.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series2};
            sideBySideBarSeriesLabel4.LineVisible = true;
            this.chartControlSinr.SeriesTemplate.Label = sideBySideBarSeriesLabel4;
            this.chartControlSinr.Size = new System.Drawing.Size(564, 161);
            this.chartControlSinr.TabIndex = 2;
            // 
            // groupBox10
            // 
            this.groupBox10.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox10.Controls.Add(this.chartControlRsrp);
            this.groupBox10.Location = new System.Drawing.Point(10, 220);
            this.groupBox10.Name = "groupBox10";
            this.groupBox10.Size = new System.Drawing.Size(576, 188);
            this.groupBox10.TabIndex = 3;
            this.groupBox10.TabStop = false;
            this.groupBox10.Text = "接收信号码功率（dBm）";
            // 
            // chartControlRsrp
            // 
            xyDiagram3.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram3.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram3.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram3.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlRsrp.Diagram = xyDiagram3;
            this.chartControlRsrp.Legend.Visible = false;
            this.chartControlRsrp.Location = new System.Drawing.Point(6, 21);
            this.chartControlRsrp.Name = "chartControlRsrp";
            sideBySideBarSeriesLabel5.LineVisible = true;
            series3.Label = sideBySideBarSeriesLabel5;
            series3.Name = "Series 1";
            this.chartControlRsrp.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series3};
            sideBySideBarSeriesLabel6.LineVisible = true;
            this.chartControlRsrp.SeriesTemplate.Label = sideBySideBarSeriesLabel6;
            this.chartControlRsrp.Size = new System.Drawing.Size(564, 161);
            this.chartControlRsrp.TabIndex = 1;
            // 
            // groupBox7
            // 
            this.groupBox7.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox7.Controls.Add(this.chartControlPower);
            this.groupBox7.Location = new System.Drawing.Point(592, 26);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(576, 188);
            this.groupBox7.TabIndex = 2;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "UE发射功率（dB）";
            // 
            // chartControlPower
            // 
            xyDiagram4.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram4.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram4.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram4.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram4.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram4.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlPower.Diagram = xyDiagram4;
            this.chartControlPower.Legend.Visible = false;
            this.chartControlPower.Location = new System.Drawing.Point(6, 21);
            this.chartControlPower.Name = "chartControlPower";
            sideBySideBarSeriesLabel7.LineVisible = true;
            series4.Label = sideBySideBarSeriesLabel7;
            series4.Name = "Series 1";
            this.chartControlPower.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series4};
            sideBySideBarSeriesLabel8.LineVisible = true;
            this.chartControlPower.SeriesTemplate.Label = sideBySideBarSeriesLabel8;
            this.chartControlPower.Size = new System.Drawing.Size(564, 161);
            this.chartControlPower.TabIndex = 1;
            // 
            // groupBox9
            // 
            this.groupBox9.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox9.Controls.Add(this.chartControlTA);
            this.groupBox9.Location = new System.Drawing.Point(10, 26);
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.Size = new System.Drawing.Size(576, 188);
            this.groupBox9.TabIndex = 0;
            this.groupBox9.TabStop = false;
            this.groupBox9.Text = "时间提前量（米）";
            // 
            // chartControlTA
            // 
            xyDiagram5.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram5.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram5.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram5.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram5.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram5.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlTA.Diagram = xyDiagram5;
            this.chartControlTA.Legend.Visible = false;
            this.chartControlTA.Location = new System.Drawing.Point(6, 21);
            this.chartControlTA.Name = "chartControlTA";
            sideBySideBarSeriesLabel9.LineVisible = true;
            series5.Label = sideBySideBarSeriesLabel9;
            series5.Name = "Series 1";
            this.chartControlTA.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series5};
            sideBySideBarSeriesLabel10.LineVisible = true;
            this.chartControlTA.SeriesTemplate.Label = sideBySideBarSeriesLabel10;
            this.chartControlTA.Size = new System.Drawing.Size(564, 161);
            this.chartControlTA.TabIndex = 2;
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.groupControl5);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage2.Text = "MR数据二维图(小区)";
            // 
            // groupControl5
            // 
            this.groupControl5.Controls.Add(this.cbPart);
            this.groupControl5.Controls.Add(this.label7);
            this.groupControl5.Controls.Add(this.groupBox1);
            this.groupControl5.Controls.Add(this.groupBox15);
            this.groupControl5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl5.Location = new System.Drawing.Point(0, 0);
            this.groupControl5.Name = "groupControl5";
            this.groupControl5.Size = new System.Drawing.Size(1177, 620);
            this.groupControl5.TabIndex = 2;
            this.groupControl5.Text = "MR全向分析";
            // 
            // cbPart
            // 
            this.cbPart.FormattingEnabled = true;
            this.cbPart.Location = new System.Drawing.Point(76, 24);
            this.cbPart.Name = "cbPart";
            this.cbPart.Size = new System.Drawing.Size(281, 22);
            this.cbPart.TabIndex = 5;
            this.cbPart.Text = "全部";
            this.cbPart.SelectedIndexChanged += new System.EventHandler(this.cbPart_SelectedIndexChanged);
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(11, 29);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(67, 14);
            this.label7.TabIndex = 4;
            this.label7.Text = "选择区间：";
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox1.Controls.Add(this.chartCellLine);
            this.groupBox1.Location = new System.Drawing.Point(599, 47);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(583, 566);
            this.groupBox1.TabIndex = 1;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "MR覆盖模拟图";
            // 
            // chartCellLine
            // 
            this.chartCellLine.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.chartCellLine.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram1.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartCellLine.Diagram = radarDiagram1;
            this.chartCellLine.Location = new System.Drawing.Point(6, 21);
            this.chartCellLine.Name = "chartCellLine";
            radarPointSeriesLabel1.LineVisible = true;
            series6.Label = radarPointSeriesLabel1;
            series6.Name = "AntSeries";
            series6.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint1,
            seriesPoint2,
            seriesPoint3,
            seriesPoint4,
            seriesPoint5});
            series6.ShowInLegend = false;
            radarPointSeriesView1.PointMarkerOptions.Size = 10;
            series6.View = radarPointSeriesView1;
            radarPointSeriesLabel2.LineVisible = true;
            series7.Label = radarPointSeriesLabel2;
            series7.Name = "StandardSeries";
            series7.ShowInLegend = false;
            radarPointSeriesView2.PointMarkerOptions.Size = 10;
            series7.View = radarPointSeriesView2;
            this.chartCellLine.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series6,
        series7};
            radarPointSeriesLabel3.LineVisible = true;
            this.chartCellLine.SeriesTemplate.Label = radarPointSeriesLabel3;
            radarPointSeriesView3.PointMarkerOptions.Size = 10;
            this.chartCellLine.SeriesTemplate.View = radarPointSeriesView3;
            this.chartCellLine.Size = new System.Drawing.Size(565, 539);
            this.chartCellLine.TabIndex = 0;
            // 
            // groupBox15
            // 
            this.groupBox15.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox15.Controls.Add(this.chartCellPoint);
            this.groupBox15.Location = new System.Drawing.Point(10, 47);
            this.groupBox15.Name = "groupBox15";
            this.groupBox15.Size = new System.Drawing.Size(583, 566);
            this.groupBox15.TabIndex = 0;
            this.groupBox15.TabStop = false;
            this.groupBox15.Text = "MR采样点分布图";
            // 
            // chartCellPoint
            // 
            this.chartCellPoint.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.chartCellPoint.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram2.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram2.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram2.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartCellPoint.Diagram = radarDiagram2;
            this.chartCellPoint.Location = new System.Drawing.Point(6, 21);
            this.chartCellPoint.Name = "chartCellPoint";
            radarPointSeriesLabel4.LineVisible = true;
            series8.Label = radarPointSeriesLabel4;
            series8.Name = "AntSeries";
            series8.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint6,
            seriesPoint7,
            seriesPoint8,
            seriesPoint9,
            seriesPoint10});
            series8.ShowInLegend = false;
            radarPointSeriesView4.PointMarkerOptions.Size = 10;
            series8.View = radarPointSeriesView4;
            radarPointSeriesLabel5.LineVisible = true;
            series9.Label = radarPointSeriesLabel5;
            series9.Name = "StandardSeries";
            series9.ShowInLegend = false;
            radarPointSeriesView5.PointMarkerOptions.Size = 10;
            series9.View = radarPointSeriesView5;
            this.chartCellPoint.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series8,
        series9};
            radarPointSeriesLabel6.LineVisible = true;
            this.chartCellPoint.SeriesTemplate.Label = radarPointSeriesLabel6;
            radarPointSeriesView6.PointMarkerOptions.Size = 10;
            this.chartCellPoint.SeriesTemplate.View = radarPointSeriesView6;
            this.chartCellPoint.Size = new System.Drawing.Size(571, 539);
            this.chartCellPoint.TabIndex = 0;
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.groupControl1);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage3.Text = "MR数据二维图(基站)";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.cbBtsPart);
            this.groupControl1.Controls.Add(this.label8);
            this.groupControl1.Controls.Add(this.cbCellName);
            this.groupControl1.Controls.Add(this.label6);
            this.groupControl1.Controls.Add(this.groupBox2);
            this.groupControl1.Controls.Add(this.groupBox3);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1177, 620);
            this.groupControl1.TabIndex = 3;
            this.groupControl1.Text = "MR全向分析";
            // 
            // cbBtsPart
            // 
            this.cbBtsPart.FormattingEnabled = true;
            this.cbBtsPart.Location = new System.Drawing.Point(438, 24);
            this.cbBtsPart.Name = "cbBtsPart";
            this.cbBtsPart.Size = new System.Drawing.Size(155, 22);
            this.cbBtsPart.TabIndex = 7;
            this.cbBtsPart.Text = "全部";
            this.cbBtsPart.SelectedIndexChanged += new System.EventHandler(this.cbBtsPart_SelectedIndexChanged);
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(373, 29);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(67, 14);
            this.label8.TabIndex = 6;
            this.label8.Text = "选择区间：";
            // 
            // cbCellName
            // 
            this.cbCellName.FormattingEnabled = true;
            this.cbCellName.Location = new System.Drawing.Point(75, 24);
            this.cbCellName.Name = "cbCellName";
            this.cbCellName.Size = new System.Drawing.Size(281, 22);
            this.cbCellName.TabIndex = 3;
            this.cbCellName.Text = "全部";
            this.cbCellName.SelectedIndexChanged += new System.EventHandler(this.cbCellName_SelectedIndexChanged);
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(10, 29);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(67, 14);
            this.label6.TabIndex = 2;
            this.label6.Text = "选择小区：";
            // 
            // groupBox2
            // 
            this.groupBox2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox2.Controls.Add(this.chartEnodebLine);
            this.groupBox2.Location = new System.Drawing.Point(599, 47);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(583, 566);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "MR覆盖模拟图";
            // 
            // chartEnodebLine
            // 
            this.chartEnodebLine.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.chartEnodebLine.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram3.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram3.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram3.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartEnodebLine.Diagram = radarDiagram3;
            this.chartEnodebLine.Location = new System.Drawing.Point(6, 21);
            this.chartEnodebLine.Name = "chartEnodebLine";
            radarPointSeriesLabel7.LineVisible = true;
            series10.Label = radarPointSeriesLabel7;
            series10.Name = "AntSeries";
            series10.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint11,
            seriesPoint12,
            seriesPoint13,
            seriesPoint14,
            seriesPoint15});
            series10.ShowInLegend = false;
            radarPointSeriesView7.PointMarkerOptions.Size = 10;
            series10.View = radarPointSeriesView7;
            radarPointSeriesLabel8.LineVisible = true;
            series11.Label = radarPointSeriesLabel8;
            series11.Name = "StandardSeries";
            series11.ShowInLegend = false;
            radarPointSeriesView8.PointMarkerOptions.Size = 10;
            series11.View = radarPointSeriesView8;
            this.chartEnodebLine.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series10,
        series11};
            radarPointSeriesLabel9.LineVisible = true;
            this.chartEnodebLine.SeriesTemplate.Label = radarPointSeriesLabel9;
            radarPointSeriesView9.PointMarkerOptions.Size = 10;
            this.chartEnodebLine.SeriesTemplate.View = radarPointSeriesView9;
            this.chartEnodebLine.Size = new System.Drawing.Size(565, 539);
            this.chartEnodebLine.TabIndex = 0;
            // 
            // groupBox3
            // 
            this.groupBox3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox3.Controls.Add(this.chartEnodebPoint);
            this.groupBox3.Location = new System.Drawing.Point(10, 47);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(583, 566);
            this.groupBox3.TabIndex = 0;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "MR采样点分布图";
            // 
            // chartEnodebPoint
            // 
            this.chartEnodebPoint.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.chartEnodebPoint.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram4.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram4.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram4.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartEnodebPoint.Diagram = radarDiagram4;
            this.chartEnodebPoint.Location = new System.Drawing.Point(6, 21);
            this.chartEnodebPoint.Name = "chartEnodebPoint";
            radarPointSeriesLabel10.LineVisible = true;
            series12.Label = radarPointSeriesLabel10;
            series12.Name = "AntSeries";
            series12.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint16,
            seriesPoint17,
            seriesPoint18,
            seriesPoint19,
            seriesPoint20});
            series12.ShowInLegend = false;
            radarPointSeriesView10.PointMarkerOptions.Size = 10;
            series12.View = radarPointSeriesView10;
            radarPointSeriesLabel11.LineVisible = true;
            series13.Label = radarPointSeriesLabel11;
            series13.Name = "StandardSeries";
            series13.ShowInLegend = false;
            radarPointSeriesView11.PointMarkerOptions.Size = 10;
            series13.View = radarPointSeriesView11;
            this.chartEnodebPoint.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series12,
        series13};
            radarPointSeriesLabel12.LineVisible = true;
            this.chartEnodebPoint.SeriesTemplate.Label = radarPointSeriesLabel12;
            radarPointSeriesView12.PointMarkerOptions.Size = 10;
            this.chartEnodebPoint.SeriesTemplate.View = radarPointSeriesView12;
            this.chartEnodebPoint.Size = new System.Drawing.Size(571, 539);
            this.chartEnodebPoint.TabIndex = 0;
            // 
            // xtraTabPage5
            // 
            this.xtraTabPage5.Controls.Add(this.groupControl2);
            this.xtraTabPage5.Name = "xtraTabPage5";
            this.xtraTabPage5.Size = new System.Drawing.Size(1177, 620);
            this.xtraTabPage5.Text = "小区渲染方案配置";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.cbNPart);
            this.groupControl2.Controls.Add(this.label9);
            this.groupControl2.Controls.Add(this.dataGridViewRight);
            this.groupControl2.Controls.Add(this.dataGridViewLeft);
            this.groupControl2.Controls.Add(this.btnDraw);
            this.groupControl2.Controls.Add(this.btnOneOut);
            this.groupControl2.Controls.Add(this.btnOneIn);
            this.groupControl2.Controls.Add(this.btnAllOut);
            this.groupControl2.Controls.Add(this.btnAllIn);
            this.groupControl2.Controls.Add(this.tbDistance);
            this.groupControl2.Controls.Add(this.serBtn);
            this.groupControl2.Controls.Add(this.label10);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl2.Location = new System.Drawing.Point(0, 0);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(1177, 620);
            this.groupControl2.TabIndex = 4;
            this.groupControl2.Text = "MR全向分析";
            // 
            // cbNPart
            // 
            this.cbNPart.FormattingEnabled = true;
            this.cbNPart.Location = new System.Drawing.Point(688, 28);
            this.cbNPart.Name = "cbNPart";
            this.cbNPart.Size = new System.Drawing.Size(112, 22);
            this.cbNPart.TabIndex = 26;
            this.cbNPart.Text = "全部";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(625, 32);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(67, 14);
            this.label9.TabIndex = 25;
            this.label9.Text = "选择区间：";
            // 
            // dataGridViewRight
            // 
            this.dataGridViewRight.AllowUserToAddRows = false;
            this.dataGridViewRight.AllowUserToDeleteRows = false;
            this.dataGridViewRight.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridViewRight.BackgroundColor = System.Drawing.SystemColors.ControlLightLight;
            this.dataGridViewRight.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewRight.Location = new System.Drawing.Point(628, 58);
            this.dataGridViewRight.Name = "dataGridViewRight";
            this.dataGridViewRight.ReadOnly = true;
            this.dataGridViewRight.RowTemplate.Height = 23;
            this.dataGridViewRight.Size = new System.Drawing.Size(543, 555);
            this.dataGridViewRight.TabIndex = 24;
            this.dataGridViewRight.CellMouseDoubleClick += new System.Windows.Forms.DataGridViewCellMouseEventHandler(this.btnOneOut_Click);
            this.dataGridViewRight.CurrentCellChanged += new System.EventHandler(this.dataGridViewRight_CurrentCellChanged);
            this.dataGridViewRight.SortCompare += new System.Windows.Forms.DataGridViewSortCompareEventHandler(this.dataGridViewLeft_SortCompare);
            // 
            // dataGridViewLeft
            // 
            this.dataGridViewLeft.AllowUserToAddRows = false;
            this.dataGridViewLeft.AllowUserToDeleteRows = false;
            this.dataGridViewLeft.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.dataGridViewLeft.BackgroundColor = System.Drawing.SystemColors.ControlLightLight;
            this.dataGridViewLeft.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewLeft.Location = new System.Drawing.Point(13, 58);
            this.dataGridViewLeft.Name = "dataGridViewLeft";
            this.dataGridViewLeft.ReadOnly = true;
            this.dataGridViewLeft.RowTemplate.Height = 23;
            this.dataGridViewLeft.Size = new System.Drawing.Size(544, 555);
            this.dataGridViewLeft.TabIndex = 23;
            this.dataGridViewLeft.CellMouseDoubleClick += new System.Windows.Forms.DataGridViewCellMouseEventHandler(this.btnOneIn_Click);
            this.dataGridViewLeft.SortCompare += new System.Windows.Forms.DataGridViewSortCompareEventHandler(this.dataGridViewLeft_SortCompare);
            // 
            // btnDraw
            // 
            this.btnDraw.Location = new System.Drawing.Point(806, 27);
            this.btnDraw.Name = "btnDraw";
            this.btnDraw.Size = new System.Drawing.Size(75, 23);
            this.btnDraw.TabIndex = 22;
            this.btnDraw.Text = "立即绘制";
            this.btnDraw.UseVisualStyleBackColor = true;
            this.btnDraw.Click += new System.EventHandler(this.btnDraw_Click);
            // 
            // btnOneOut
            // 
            this.btnOneOut.Location = new System.Drawing.Point(574, 409);
            this.btnOneOut.Name = "btnOneOut";
            this.btnOneOut.Size = new System.Drawing.Size(33, 23);
            this.btnOneOut.TabIndex = 20;
            this.btnOneOut.Text = "<";
            this.btnOneOut.UseVisualStyleBackColor = true;
            this.btnOneOut.Click += new System.EventHandler(this.btnOneOut_Click);
            // 
            // btnOneIn
            // 
            this.btnOneIn.Location = new System.Drawing.Point(574, 289);
            this.btnOneIn.Name = "btnOneIn";
            this.btnOneIn.Size = new System.Drawing.Size(33, 23);
            this.btnOneIn.TabIndex = 19;
            this.btnOneIn.Text = ">";
            this.btnOneIn.UseVisualStyleBackColor = true;
            this.btnOneIn.Click += new System.EventHandler(this.btnOneIn_Click);
            // 
            // btnAllOut
            // 
            this.btnAllOut.Location = new System.Drawing.Point(574, 371);
            this.btnAllOut.Name = "btnAllOut";
            this.btnAllOut.Size = new System.Drawing.Size(33, 23);
            this.btnAllOut.TabIndex = 18;
            this.btnAllOut.Text = "<<";
            this.btnAllOut.UseVisualStyleBackColor = true;
            this.btnAllOut.Click += new System.EventHandler(this.btnAllOut_Click);
            // 
            // btnAllIn
            // 
            this.btnAllIn.Location = new System.Drawing.Point(574, 333);
            this.btnAllIn.Name = "btnAllIn";
            this.btnAllIn.Size = new System.Drawing.Size(33, 23);
            this.btnAllIn.TabIndex = 17;
            this.btnAllIn.Text = ">>";
            this.btnAllIn.UseVisualStyleBackColor = true;
            this.btnAllIn.Click += new System.EventHandler(this.btnAllIn_Click);
            // 
            // tbDistance
            // 
            this.tbDistance.Location = new System.Drawing.Point(99, 27);
            this.tbDistance.Name = "tbDistance";
            this.tbDistance.Size = new System.Drawing.Size(100, 22);
            this.tbDistance.TabIndex = 4;
            this.tbDistance.Text = "500";
            // 
            // serBtn
            // 
            this.serBtn.Location = new System.Drawing.Point(205, 26);
            this.serBtn.Name = "serBtn";
            this.serBtn.Size = new System.Drawing.Size(75, 23);
            this.serBtn.TabIndex = 3;
            this.serBtn.Text = "查找";
            this.serBtn.UseVisualStyleBackColor = true;
            this.serBtn.Click += new System.EventHandler(this.serBtn_Click);
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(10, 29);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(89, 14);
            this.label10.TabIndex = 2;
            this.label10.Text = "小区间距(米)：";
            // 
            // TDAntMRAnaForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1184, 650);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "TDAntMRAnaForm";
            this.Text = "TD MR覆盖分析";
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage4.ResumeLayout(false);
            this.xtraTabPage4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewTdCell)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).EndInit();
            this.groupControl4.ResumeLayout(false);
            this.groupBox12.ResumeLayout(false);
            this.groupBox8.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlAoa)).EndInit();
            this.groupBox11.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlSinr)).EndInit();
            this.groupBox10.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlRsrp)).EndInit();
            this.groupBox7.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlPower)).EndInit();
            this.groupBox9.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlTA)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl5)).EndInit();
            this.groupControl5.ResumeLayout(false);
            this.groupControl5.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartCellLine)).EndInit();
            this.groupBox15.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartCellPoint)).EndInit();
            this.xtraTabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartEnodebLine)).EndInit();
            this.groupBox3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesView12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartEnodebPoint)).EndInit();
            this.xtraTabPage5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewRight)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewLeft)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miShowChart;
        private System.Windows.Forms.ToolStripMenuItem miShowSimulation;
        private System.Windows.Forms.ToolStripMenuItem miExportWholeExcel;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage4;
        private System.Windows.Forms.DataGridView dataGridViewTdCell;
        private System.Windows.Forms.Button btnSearch;
        private System.Windows.Forms.TextBox txtCellName;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnGo;
        private System.Windows.Forms.Label labNum;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label labPage;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtPage;
        private System.Windows.Forms.ToolStripMenuItem 导出CSVToolStripMenuItem;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraEditors.GroupControl groupControl4;
        private System.Windows.Forms.GroupBox groupBox12;
        private System.Windows.Forms.GroupBox groupBox8;
        private DevExpress.XtraCharts.ChartControl chartControlAoa;
        private System.Windows.Forms.GroupBox groupBox11;
        private DevExpress.XtraCharts.ChartControl chartControlSinr;
        private System.Windows.Forms.GroupBox groupBox10;
        private DevExpress.XtraCharts.ChartControl chartControlRsrp;
        private System.Windows.Forms.GroupBox groupBox7;
        private DevExpress.XtraCharts.ChartControl chartControlPower;
        private System.Windows.Forms.GroupBox groupBox9;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraEditors.GroupControl groupControl5;
        private System.Windows.Forms.GroupBox groupBox15;
        private DevExpress.XtraCharts.ChartControl chartCellPoint;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraCharts.ChartControl chartCellLine;
        private System.Windows.Forms.DataGridViewTextBoxColumn colIndex;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column11;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column1;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column14;
        private System.Windows.Forms.DataGridViewTextBoxColumn colvender;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column41;
        private System.Windows.Forms.DataGridViewTextBoxColumn colcgi;
        private System.Windows.Forms.DataGridViewTextBoxColumn colcovertype;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column3;
        private System.Windows.Forms.DataGridViewTextBoxColumn dirCol;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column4;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column5;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column6;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column7;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column8;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column9;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column10;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column12;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column13;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column15;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column16;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column17;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column18;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column19;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column20;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column23;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column24;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column25;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column26;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column27;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column28;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column2;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column21;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column22;
        private DevExpress.XtraCharts.ChartControl chartControlTA;
        private System.Windows.Forms.RichTextBox rtbDesc;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraCharts.ChartControl chartEnodebLine;
        private System.Windows.Forms.GroupBox groupBox3;
        private DevExpress.XtraCharts.ChartControl chartEnodebPoint;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.ComboBox cbCellName;
        private System.Windows.Forms.ComboBox cbPart;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.ComboBox cbBtsPart;
        private System.Windows.Forms.Label label8;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage5;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TextBox tbDistance;
        private System.Windows.Forms.Button serBtn;
        private System.Windows.Forms.Button btnOneOut;
        private System.Windows.Forms.Button btnOneIn;
        private System.Windows.Forms.Button btnAllOut;
        private System.Windows.Forms.Button btnAllIn;
        private System.Windows.Forms.Button btnDraw;
        private System.Windows.Forms.DataGridView dataGridViewRight;
        private System.Windows.Forms.DataGridView dataGridViewLeft;
        private System.Windows.Forms.ComboBox cbNPart;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Button btnNextpage;
        private System.Windows.Forms.Button btnPrevpage;
    }
}