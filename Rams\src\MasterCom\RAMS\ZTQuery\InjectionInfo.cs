using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using System.Drawing;
using MasterCom.RAMS.Util;
using MasterCom.RAMS.ZTQuery;
using System.Collections;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    public class InjectionStreetSet //��͸��·��
    {
        public string streetSetName;
        public int passPercent;
        public int streetLevel;
        public bool isJoinWorkStat = false;
        public List<string> injectionStreetNames = new List<string>();
        public string remark;
        public int setId;

        public override string ToString()
        {
            return streetSetName + "[" + passPercent.ToString() + "]";
        }

        public static InjectionStreetSet fillFrom(MasterCom.RAMS.Net.Content c)
        {
            InjectionStreetSet set = new InjectionStreetSet();
            set.streetSetName = c.GetParamString();
            set.passPercent = c.GetParamInt();
            set.streetLevel = c.GetParamInt();
            set.isJoinWorkStat = c.GetParamInt() == 0 ? false : true;
            set.remark = c.GetParamString();
            return set;
        }

        public void addStreetName(string streetName)
        {
            this.injectionStreetNames.Add(streetName);
        }

        public static Dictionary<string, List<string>> getStreetInfoFromFiles() 
        {
            Dictionary<string, List<string>> streetMap = new Dictionary<string, List<string>>();
            return streetMap;
        }

    }

    interface IListViewItem //�ƶ���ͨ����������͸�ʽӿ�
    {
        string AreaName { get;}
        string StreetName { get;}
        double StreetDistInArea { get;}
        double CmccInjectDist { get;}
        double CmccInjectPct { get;}
        double CmccRepeatDist { get;}
        double CmccRepeatPct { get;}
        double CuInjectDist { get;}
        double CuInjectPct { get;}
        double CuRepeatDist { get;}
        double CuRepeatPct { get;}
        double CtInjectDist { get;}
        double CtInjectPct { get;}
        double CtRepeatDist { get;}
        double CtRepeatPct { get;}
    }

    public class CarrierAreaInjectionInfo //��Ӫ��������͸��Ϣ
    {
        private InjectGridUnit[,] streetInjectColorMatrix = null;
        private List<StreetInjectInfo> totalInfoResultList = new List<StreetInjectInfo>();

        public InjectGridUnit[,] StreetInjectColorMatrix
        {
            get { return streetInjectColorMatrix; }
            set { streetInjectColorMatrix = value; }
        }

        public List<StreetInjectInfo> TotalInfoResultList
        {
            get { return totalInfoResultList; }
            set { totalInfoResultList = value; }
        }
    }

    public class AreaStreetCarrierInjectInfo : IListViewItem, ICloneable
    {
        public List<StreetCarrierInjectInfo> streetCarrierInjectInfos = new List<StreetCarrierInjectInfo>();

        public List<StreetSetCarrierInjectInfo> StreetSetCarrierInjectInfos
        {
            get
            {
                MainModel mModel = MainModel.GetInstance();
                List<StreetSetCarrierInjectInfo> list = new List<StreetSetCarrierInjectInfo>();
                Dictionary<string, List<StreetCarrierInjectInfo>> streetSetStreetInjectMap = new Dictionary<string, List<StreetCarrierInjectInfo>>();
                foreach (StreetCarrierInjectInfo streetCarrierInjectInfo in streetCarrierInjectInfos)
                {
                    foreach (InjectionStreetSet set in mModel.SelectedInjectionStreetSetList)
                    {
                        if (set.streetSetName.Equals("ȫ��"))
                        {
                            if (streetSetStreetInjectMap.ContainsKey(set.streetSetName))
                            {
                                streetSetStreetInjectMap[set.streetSetName].Add(streetCarrierInjectInfo);
                            }
                            else
                            {
                                streetSetStreetInjectMap[set.streetSetName] = new List<StreetCarrierInjectInfo>();
                                streetSetStreetInjectMap[set.streetSetName].Add(streetCarrierInjectInfo);
                            }
                        }
                        else
                        {
                            foreach (string streetName in set.injectionStreetNames)
                            {
                                if (streetCarrierInjectInfo.StreetName.Equals(streetName))
                                {
                                    if (streetSetStreetInjectMap.ContainsKey(set.streetSetName))
                                    {
                                        streetSetStreetInjectMap[set.streetSetName].Add(streetCarrierInjectInfo);
                                    }
                                    else
                                    {
                                        streetSetStreetInjectMap[set.streetSetName] = new List<StreetCarrierInjectInfo>();
                                        streetSetStreetInjectMap[set.streetSetName].Add(streetCarrierInjectInfo);
                                    }
                                }
                            }
                        }
                    }
                }
                foreach (string streetSetName in streetSetStreetInjectMap.Keys)
                {
                    StreetSetCarrierInjectInfo info = new StreetSetCarrierInjectInfo();
                    info.AreaName = areaName;
                    info.streetSetName = streetSetName;
                    info.streetCarrierInjectInfos.AddRange(streetSetStreetInjectMap[streetSetName]);
                    list.Add(info);
                }

                return list;
            }
        }

        #region IListViewItem ��Ա
        public string areaName;
        public string AreaName
        {
            get { return areaName; }
            set { areaName = value; }
        }

        public string StreetName
        {
            get { return ""; }
        }

        public double StreetDistInArea
        {
            get
            {
                double totalDistance = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    totalDistance += info.StreetDistInArea;
                }
                return totalDistance;
            }
        }

        public double CmccInjectDist
        {
            get
            {
                double dist = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    dist += info.cmccStreetInjectInfo.distCovered;
                }
                return dist;
            }
        }

        public double CmccInjectPct
        {
            get
            {
                double distUnCovered = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    distUnCovered += info.cmccStreetInjectInfo.distUnCovered;
                }
                if (CmccInjectDist + distUnCovered != 0.0)
                {
                    return CmccInjectDist / (CmccInjectDist + distUnCovered);
                }
                else
                {
                    return 0.0;
                }
            }
        }

        public double CmccRepeatDist
        {
            get
            {
                double dist = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    dist += info.cmccStreetInjectInfo.distRepeatedCovered;
                }
                return dist;
            }
        }

        public double CmccRepeatPct
        {
            get
            {
                double distUnCovered = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    distUnCovered += info.cmccStreetInjectInfo.distUnCovered;
                }
                if (CmccInjectDist + distUnCovered != 0.0)
                {
                    return CmccRepeatDist / (CmccInjectDist + distUnCovered);
                }
                else
                {
                    return 0.0;
                }
            }
        }

        public double CuInjectDist
        {
            get
            {
                double dist = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    dist += info.cuStreetInjectInfo.distCovered;
                }
                return dist;
            }
        }

        public double CuInjectPct
        {
            get
            {
                double distUnCovered = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    distUnCovered += info.cuStreetInjectInfo.distUnCovered;
                }
                if (CuInjectDist + distUnCovered != 0.0)
                {
                    return CuInjectDist / (CuInjectDist + distUnCovered);
                }
                else
                {
                    return 0.0;
                }
            }
        }

        public double CuRepeatDist
        {
            get
            {
                double dist = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    dist += info.cuStreetInjectInfo.distRepeatedCovered;
                }
                return dist;
            }
        }

        public double CuRepeatPct
        {
            get
            {
                double distUnCovered = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    distUnCovered += info.cuStreetInjectInfo.distUnCovered;
                }
                if (CuInjectDist + distUnCovered != 0.0)
                {
                    return CuRepeatDist / (CuInjectDist + distUnCovered);
                }
                else
                {
                    return 0.0;
                }
            }
        }

        public double CtInjectDist
        {
            get
            {
                double dist = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    dist += info.ctStreetInjectInfo.distCovered;
                }
                return dist;
            }
        }

        public double CtInjectPct
        {
            get
            {
                double distUnCovered = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    distUnCovered += info.ctStreetInjectInfo.distUnCovered;
                }
                if (CtInjectDist + distUnCovered != 0.0)
                {
                    return CtInjectDist / (CtInjectDist + distUnCovered);
                }
                else
                {
                    return 0.0;
                }
            }
        }

        public double CtRepeatDist
        {
            get
            {
                double dist = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    dist += info.ctStreetInjectInfo.distRepeatedCovered;
                }
                return dist;
            }
        }

        public double CtRepeatPct
        {
            get
            {
                double distUnCovered = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    distUnCovered += info.ctStreetInjectInfo.distUnCovered;
                }
                if (CtInjectDist + distUnCovered != 0.0)
                {
                    return CtRepeatDist / (CtInjectDist + distUnCovered);
                }
                else
                {
                    return 0.0;
                }
            }
        }

        #endregion

        #region ICloneable ��Ա

        public object Clone()
        {
            AreaStreetCarrierInjectInfo info = new AreaStreetCarrierInjectInfo();
            info.areaName = this.areaName;
            foreach (StreetCarrierInjectInfo tempInfo in this.streetCarrierInjectInfos)
            {
                info.streetCarrierInjectInfos.Add(tempInfo.Clone() as StreetCarrierInjectInfo);
            }
            return info;
        }

        #endregion
    }

    public class StreetSetCarrierInjectInfo : IListViewItem, ICloneable
    {
        public string streetSetName;

        public List<StreetCarrierInjectInfo> streetCarrierInjectInfos = new List<StreetCarrierInjectInfo>();

        #region IListViewItem ��Ա

        private string areaName = "";
        public string AreaName
        {
            get { return areaName; }
            set { areaName = value; }
        }

        public string StreetName
        {
            get { return streetSetName; }
            set { streetSetName = value; }
        }

        public double StreetDistInArea
        {
            get
            {
                double dist = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    dist += info.StreetDistInArea;
                }
                return dist;
            }
        }

        public double CmccInjectDist
        {
            get
            {
                double dist = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    dist += info.CmccInjectDist;
                }
                return dist;
            }
        }

        public double CmccInjectPct
        {
            get
            {
                return CmccInjectDist / StreetDistInArea;
            }
        }

        public double CmccRepeatDist
        {
            get
            {
                double dist = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    dist += info.CmccRepeatDist;
                }
                return dist;
            }
        }

        public double CmccRepeatPct
        {
            get
            {
                return CmccRepeatDist / StreetDistInArea;
            }
        }

        public double CuInjectDist
        {
            get
            {
                double dist = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    dist += info.CuInjectDist;
                }
                return dist;
            }
        }

        public double CuInjectPct
        {
            get
            {
                return CuInjectDist / StreetDistInArea;
            }
        }

        public double CuRepeatDist
        {
            get
            {
                double dist = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    dist += info.CuRepeatDist;
                }
                return dist;
            }
        }

        public double CuRepeatPct
        {
            get
            {
                return CuRepeatDist / StreetDistInArea;
            }
        }

        public double CtInjectDist
        {
            get
            {
                double dist = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    dist += info.CtInjectDist;
                }
                return dist;
            }
        }

        public double CtInjectPct
        {
            get
            {
                return CtInjectDist / StreetDistInArea;
            }
        }

        public double CtRepeatDist
        {
            get
            {
                double dist = 0.0;
                foreach (StreetCarrierInjectInfo info in streetCarrierInjectInfos)
                {
                    dist += info.CtRepeatDist;
                }
                return dist;
            }
        }

        public double CtRepeatPct
        {
            get
            {
                return CtRepeatDist / StreetDistInArea;
            }
        }

        #endregion

        #region ICloneable ��Ա

        public object Clone()
        {
            throw new NotImplementedException("The method or operation is not implemented.");
        }

        #endregion
    }

    public class StreetCarrierInjectInfo : IListViewItem, ICloneable
    {
        public StreetInjectInfo cmccStreetInjectInfo = new StreetInjectInfo();
        public StreetInjectInfo cuStreetInjectInfo = new StreetInjectInfo();
        public StreetInjectInfo ctStreetInjectInfo = new StreetInjectInfo();

        #region IListViewItem ��Ա

        private string areaName = "";
        public string AreaName
        {
            get { return areaName; }
            set { areaName = value; }
        }

        private string streetName = "";
        public string StreetName
        {
            get { return streetName; }
            set { streetName = value; }
        }

        //private double streetDistInArea = 0.0;
        public double StreetDistInArea
        {
            get
            {
                if (cmccStreetInjectInfo.distCovered + cmccStreetInjectInfo.distUnCovered > 0)
                {
                    return cmccStreetInjectInfo.distCovered + cmccStreetInjectInfo.distUnCovered;
                }
                else if (cuStreetInjectInfo.distCovered + cuStreetInjectInfo.distUnCovered > 0)
                {
                    return cuStreetInjectInfo.distCovered + cuStreetInjectInfo.distUnCovered;
                }
                else if (ctStreetInjectInfo.distCovered + ctStreetInjectInfo.distUnCovered > 0)
                {
                    return ctStreetInjectInfo.distCovered + ctStreetInjectInfo.distUnCovered;
                }
                return 0.0;
                //return streetDistInArea; 
            }
            //set { streetDistInArea = value; }
        }

        public double CmccInjectDist
        {
            get
            {
                return cmccStreetInjectInfo.distCovered;
            }
            set
            {
                cmccStreetInjectInfo.distCovered = value;
            }
        }
        public double CmccUnInjectDist
        {
            get { return cmccStreetInjectInfo.distUnCovered; }
            set { cmccStreetInjectInfo.distUnCovered = value; }
        }

        public double CmccInjectPct
        {
            get
            {
                return cmccStreetInjectInfo.CoverPercent;
            }
        }

        public double CmccRepeatDist
        {
            get
            {
                return cmccStreetInjectInfo.distRepeatedCovered;
            }
            set
            {
                cmccStreetInjectInfo.distRepeatedCovered = value;
            }
        }
        public double CmccRepeatPct
        {
            get
            {
                return cmccStreetInjectInfo.RepeatPct;
            }
        }

        public double CuInjectDist
        {
            get
            {
                return cuStreetInjectInfo.distCovered;
            }
            set
            {
                cuStreetInjectInfo.distCovered = value;
            }
        }

        public double CuUnInjectDist
        {
            get { return cuStreetInjectInfo.distUnCovered; }
            set { cuStreetInjectInfo.distUnCovered = value; }
        }

        public double CuInjectPct
        {
            get
            {
                return cuStreetInjectInfo.CoverPercent;
            }
        }

        public double CuRepeatDist
        {
            get
            {
                return cuStreetInjectInfo.distRepeatedCovered;
            }
            set
            {
                cuStreetInjectInfo.distRepeatedCovered = value;
            }
        }

        public double CuRepeatPct
        {
            get
            {
                return cuStreetInjectInfo.RepeatPct;
            }
        }

        public double CtInjectDist
        {
            get
            {
                return ctStreetInjectInfo.distCovered;
            }
            set
            {
                ctStreetInjectInfo.distCovered = value;
            }
        }

        public double CtUnInjectDist
        {
            get { return ctStreetInjectInfo.distUnCovered; }
            set { ctStreetInjectInfo.distUnCovered = value; }
        }

        public double CtInjectPct
        {
            get
            {
                return ctStreetInjectInfo.CoverPercent;
            }
        }

        public double CtRepeatDist
        {
            get
            {
                return ctStreetInjectInfo.distRepeatedCovered;
            }
            set
            {
                ctStreetInjectInfo.distRepeatedCovered = value;
            }
        }

        public double CtRepeatPct
        {
            get
            {
                return ctStreetInjectInfo.RepeatPct;
            }
        }

        #endregion

        #region ICloneable ��Ա

        public object Clone()
        {
            StreetCarrierInjectInfo info = new StreetCarrierInjectInfo();
            info.cmccStreetInjectInfo = this.cmccStreetInjectInfo.Clone() as StreetInjectInfo;
            info.cuStreetInjectInfo = this.cuStreetInjectInfo.Clone() as StreetInjectInfo;
            info.ctStreetInjectInfo = this.ctStreetInjectInfo.Clone() as StreetInjectInfo;
            //info.streetDistInArea = this.streetDistInArea;
            info.streetName = this.streetName;
            info.areaName = this.areaName;
            return info;
        }

        #endregion
    }
}
