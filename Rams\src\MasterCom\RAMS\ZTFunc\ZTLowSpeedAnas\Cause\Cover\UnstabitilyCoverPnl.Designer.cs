﻿namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    partial class UnstabitilyCoverPnl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.numSecond = new DevExpress.XtraEditors.SpinEdit();
            this.numRSRPDiff = new DevExpress.XtraEditors.SpinEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSecond.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPDiff.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.numSecond);
            this.groupBox1.Controls.Add(this.numRSRPDiff);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(357, 58);
            this.groupBox1.TabIndex = 2;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "覆盖不稳定";
            // 
            // numSecond
            // 
            this.numSecond.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numSecond.Location = new System.Drawing.Point(31, 27);
            this.numSecond.Name = "numSecond";
            this.numSecond.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSecond.Properties.MaxValue = new decimal(new int[] {
            600,
            0,
            0,
            0});
            this.numSecond.Size = new System.Drawing.Size(60, 21);
            this.numSecond.TabIndex = 0;
            // 
            // numRSRPDiff
            // 
            this.numRSRPDiff.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numRSRPDiff.Location = new System.Drawing.Point(252, 27);
            this.numRSRPDiff.Name = "numRSRPDiff";
            this.numRSRPDiff.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRSRPDiff.Size = new System.Drawing.Size(60, 21);
            this.numRSRPDiff.TabIndex = 1;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(318, 32);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(17, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "dB";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(97, 32);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(149, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "秒内，信号强度变化幅度≥";
            // 
            // UnstabitilyCoverPnl
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.Controls.Add(this.groupBox1);
            this.Name = "UnstabitilyCoverPnl";
            this.Size = new System.Drawing.Size(357, 58);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSecond.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPDiff.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.SpinEdit numRSRPDiff;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit numSecond;
        private System.Windows.Forms.Label label2;
    }
}
