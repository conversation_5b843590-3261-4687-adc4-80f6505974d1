﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public partial class ReasonPnlHandOverUnTimely : ReasonPanelBase
    {
        public ReasonPnlHandOverUnTimely()
        {
            InitializeComponent();
        }

        public override void AttachReason(ReasonBase reason)
        {
            base.AttachReason(reason);
            numLastTime.ValueChanged -= timeLimit_ValueChanged;
            numLastTime.Value = (decimal)ZTWeakSINRReason.stayTime;
            numLastTime.ValueChanged += timeLimit_ValueChanged;
            rsrpDiffer.ValueChanged -= rsrpDiffer_ValueChanged;
            rsrpDiffer.Value = (decimal)ZTWeakSINRReason.rsrpDiffer;
            rsrpDiffer.ValueChanged += rsrpDiffer_ValueChanged;
            numBeforeTime.ValueChanged -= numBeforeTime_ValueChanged;
            numBeforeTime.Value = (decimal)ZTWeakSINRReason.beforeTime;
            numBeforeTime.ValueChanged += numBeforeTime_ValueChanged;
        }

        void timeLimit_ValueChanged(object sender, EventArgs e)
        {
            ZTWeakSINRReason.stayTime = (int)numLastTime.Value;
        }
        void rsrpDiffer_ValueChanged(object sender, EventArgs e)
        {
            ZTWeakSINRReason.rsrpDiffer = (int)rsrpDiffer.Value;
        }
        void numBeforeTime_ValueChanged(object sender, EventArgs e)
        {
            ZTWeakSINRReason.beforeTime = (int)numBeforeTime.Value;
        }
    }
}
