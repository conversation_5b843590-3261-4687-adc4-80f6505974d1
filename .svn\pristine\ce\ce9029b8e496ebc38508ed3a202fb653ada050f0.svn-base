﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment
{
    public abstract class LastSegmentConditionBase
    {
        public abstract string Name
        {
            get;
        }
        public override string ToString()
        {
            return Name;
        }
        protected double lastDistance = 0;
        public double LastDistance
        {
            get { return lastDistance; }
            set
            {
                if (value >= 0)
                {
                    lastDistance = value;
                }
            }
        }
        public abstract bool IsValid(TestPoint tp);
        public virtual bool IsValidSegment(RoadSegment segment)
        {
            if (segment==null)
            {
                return false;
            }
            return segment.LastDistance >= lastDistance;
        }
        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            return tp["lte_RSRP"];
        }
        protected object GetEARFCN(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_EARFCN"];
            }
            return tp["lte_EARFCN"];
        }
        protected object GetSINR(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_SINR"];
            }
            return tp["lte_SINR"];
        }
        protected object GetNRSRP(TestPoint tp,int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_NCell_RSRP", index];
            }
            return tp["lte_NCell_RSRP", index];
        }
        protected object GetNEARFCN(TestPoint tp, int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_NCell_EARFCN", index];
            }
            return tp["lte_NCell_EARFCN", index];
        }
    }

    public class WeakCoverCondition : LastSegmentConditionBase
    {
        public WeakCoverCondition()
        {
            RSRP = -105;
            lastDistance = 50;
        }
        public override string Name
        {
            get { return "弱覆盖"; }
        }

        public float RSRP
        {
            get;
            set;
        }

        public override bool IsValid(TestPoint tp)
        {
            float? rsrp = (float?)GetRSRP(tp);
            return rsrp <= this.RSRP;
        }
        
    }

    public class MultiCoverCondition : LastSegmentConditionBase
    {
        public MultiCoverCondition()
        {
            RSRP = -105;
            RSRPDiff = 6;
            MultiGrade = 4;
            lastDistance = 20;
            SameARFCN = false;
        }
        public override string Name
        {
            get { return "重叠覆盖"; }
        }

        /// <summary>
        /// 最强信号
        /// </summary>
        public float RSRP
        {
            get;
            set;
        }
        public float RSRPDiff
        {
            get;
            set;
        }
        /// <summary>
        ///   重叠覆盖度
        /// </summary>
        public int MultiGrade
        {
            get;
            set;
        }

        public bool SameARFCN
        {
            get;
            set;
        }

        public override bool IsValid(TestPoint tp)
        {
            List<float> list = new List<float>();
            float? rsrp = (float?)GetRSRP(tp);
            if (rsrp >= RSRP)
            {
                list.Add((float)rsrp);
            }
            var earfcn = (int?)GetEARFCN(tp);

            for (int i = 0; i < 10; i++)
            {
                float? nRsrp = (float?)GetNRSRP(tp, i);
                var nEarfcn = (int?)GetNEARFCN(tp, i);

                if (SameARFCN && earfcn != null)
                {
                    if (nRsrp >= RSRP && earfcn == nEarfcn)
                    {
                        list.Add((float)nRsrp);
                    }
                }
                else
                {
                    if (nRsrp >= RSRP)
                    {
                        list.Add((float)nRsrp);
                    }
                }
            }

            if (list.Count < MultiGrade)
            {
                return false;
            }

            list.Sort();
            float max = list[list.Count - 1];
            if (max < RSRP)
            {
                return false;
            }

            float limit = list[list.Count - MultiGrade];
            return max - limit <= RSRPDiff;
        }
    }

    public class PoorSINRCondition : LastSegmentConditionBase
    {
        public PoorSINRCondition()
        {
            RSRP = -105;
            SINR = 0;
            lastDistance = 20;
        }
        public override string Name
        {
            get { return "质差路段"; }
        }

        public float RSRP
        {
            get;
            set;
        }
        public float SINR
        {
            get;
            set;
        }

        public override bool IsValid(TestPoint tp)
        {
            float? sinr = (float?)GetSINR(tp);
            if (sinr>SINR)
            {
                return false;
            }
            float? rsrp = (float?)GetRSRP(tp);
            if (rsrp<=RSRP)
            {
                return false;
            }
            return true;
        }

    }


}
