﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public class LteScanTpHelper
    {
        public static LTEScanManager LteScanTpManager { get; set; } = new LTEScanManager();

        public class LTEScanManager : LTEScanTpManagerBase
        {
            public string RsrpThemeName { get; } = "LTESCAN_TopN_CELL_Specific_RSRP";
            public string SinrThemeName { get; } = "LTESCAN_TopN_CELL_Specific_RSSINR";

            public string RsrpFullThemeName { get; } = "NR_SCAN:NR_SCAN_SSB_RSRP";
            public string SinrFullThemeName { get; } = "NR_SCAN:NR_SCAN_SSB_SINR";

            public override float? GetCellRsrp(TestPoint tp, int index, bool judgeRange = false)
            {
                float? data = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", index];
                return judgeRangeFunc(judgeRange, data, -150, 30);
            }

            public override float? GetCellSinr(TestPoint tp, int index, bool judgeRange = false)
            {
                float? data = (float?)tp["LTESCAN_TopN_CELL_Specific_RSSINR", index];
                return judgeRangeFunc(judgeRange, data, -40, 40);
            }

            public override object GetEARFCN(TestPoint tp, int index)
            {
                int? earfcn = (int?)tp["LTESCAN_TopN_EARFCN", index];
                return earfcn;
            }

            public override object GetPCI(TestPoint tp, int index)
            {
                int? pci = (int?)tp["LTESCAN_TopN_PCI", index];
                return pci;
            }

            public int? GetCellGroup(TestPoint tp, int index)
            {
                int? data = (int?)tp["NRSCAN_GroupNum", index];
                return data;
            }

            public Dictionary<int, int> GetCellMaxBeam(TestPoint tp)
            {
                Dictionary<int, int> groupDic = new Dictionary<int, int>();
                //NR扫频数据每个Group有8个Beam
                //采样点数据仅按Rsrp大小排序
                //因此每个Group取到的第一条数据就是这个小区Beam的最大值
                for (int index = 0; index < 50; index++)
                {
                    int? group = GetCellGroup(tp, index);
                    if (group == null)
                    {
                        break;
                    }
                    int data = (int)group;
                    if (groupDic.ContainsKey(data))
                    {
                        continue;
                    }

                    groupDic.Add(data, index);
                }
                return groupDic;
            }
        }

        public abstract class LTEScanTpManagerBase : TpManagerBase
        {
            public abstract float? GetCellRsrp(TestPoint tp, int index, bool judgeRange = false);
            public abstract float? GetCellSinr(TestPoint tp, int index, bool judgeRange = false);
            public abstract object GetEARFCN(TestPoint tp, int index);
            public abstract object GetPCI(TestPoint tp, int index);
        }
    }
}
