﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRUnknownDisturbInfoForm : MinCloseForm
    {
        public NRUnknownDisturbInfoForm(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
        }
        public void FillData(List<NRUnknownDisturbInfo> list)
        {
            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
            gv.BestFitColumns();
            MainModel.ClearDTData();
            foreach (NRUnknownDisturbInfo item in list)
            {
                foreach (TestPoint tp in item.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.FireSetDefaultMapSerialTheme("NR:SS_RSRP");
            MainModel.FireDTDataChanged(this);
        }
        private void gv_DoubleClick(object sender, EventArgs e)
        {
            NRUnknownDisturbInfo unknownDisturbence = gv.GetFocusedRow() as NRUnknownDisturbInfo;
            if (unknownDisturbence != null)
            {
                MainModel.ClearDTData();
                foreach (TestPoint tp in unknownDisturbence.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(this);
            }
        }

        private void ExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(gv);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }
    }
}
