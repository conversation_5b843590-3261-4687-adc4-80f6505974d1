using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

using System.Windows.Forms;

using System.IO;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc.ZTCluster.Simulation
{
    public class CellAreaManager
    {
        public static CellAreaManager GetInstance()
        {
            if (instance == null)
            {
                instance = new CellAreaManager();
            }
            return instance;
        }

        public CellAreaManager()
        {
            this.geometryNameDic = getAreaGeometryDic();
        }

        public void add(List<Cell> cells)
        {
            this.cells = cells;
            FindCellAreaName();
        }

        private void FindCellAreaName()
        {
            foreach (Cell cell in cells)
            {
                foreach (MapWinGIS.Shape geometry in geometryNameDic.Keys)
                {
                    MapOperation2 oper = new MapOperation2();
                    oper.FillPolygon(geometry);
                    if (oper.CheckPointInRegion(cell.Longitude, cell.Latitude)
                        && !cellNameAreaNameDic.ContainsKey(cell.Name))
                    {
                        cellNameAreaNameDic.Add(cell.Name, geometryNameDic[geometry]);
                    }
                }
            }
        }

        private Dictionary<MapWinGIS.Shape,string> getAreaGeometryDic()
        {
            Dictionary<MapWinGIS.Shape, string> dict = new Dictionary<MapWinGIS.Shape, string>();
            return dict;
        }

        public string getAreaNameByCellName(string cellName)
        {
            if (cellNameAreaNameDic.ContainsKey(cellName))
            {
                return cellNameAreaNameDic[cellName];
            }
            else
            {
                return "-";
            }
        }

        public List<string> getAreaNamesByCellNames(List<string> cellNames)
        {
            List<string> list = new List<string>();
            foreach (string cellName in cellNames)
            {
                string tempstr = getAreaNameByCellName(cellName);
                if (tempstr != "-" && !list.Contains(tempstr))
                {
                    list.Add(tempstr);
                }
            }
            return list;
        }

        public void clear()
        {
            cellNameAreaNameDic.Clear();
        }

        private static CellAreaManager instance;

        private List<Cell> cells = new List<Cell>();

        private readonly Dictionary<MapWinGIS.Shape, string> geometryNameDic;

        private readonly Dictionary<string, string> cellNameAreaNameDic = new Dictionary<string, string>();
        
    }
}
