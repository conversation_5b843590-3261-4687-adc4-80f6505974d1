﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Printing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.MTGis
{
    public partial class PrintHDCutDlg : Form
    {
        public PrintHDCutDlg()
        {
            InitializeComponent();
        }

        private bool paintBorderLine = false;
        private bool paintMark = false;
        private void btnPrint_Click(object sender, EventArgs e)
        {
            PrintDialog printDlg = new PrintDialog();
            printDlg.Document = printDocument;
            printDlg.UseEXDialog = true;
            //printDocument.OriginAtMargins = true;
            //printDocument.DefaultPageSettings.Margins = new System.Drawing.Printing.Margins(0, 0, 0, 0);
            
            if (printDlg.ShowDialog() == DialogResult.OK)
            {
                paintBorderLine = cbxPaintBorder.Checked;
                paintMark = cbxPaintMark.Checked;
                try
                {
                    ipg = 0;
                    printDocument.Print();
                }
                catch
                {   //停止打印
                    printDocument.PrintController.OnEndPrint(printDocument, new System.Drawing.Printing.PrintEventArgs());
                }
            }
        }
        private int ipg = 0;
        private void printDocument_PrintPage(object sender, System.Drawing.Printing.PrintPageEventArgs e)
        {
            Graphics g = e.Graphics;

            // If you set printDocumet.OriginAtMargins to 'false' this event 
            // will print the largest rectangle your printer is physically 
            // capable of. This is often 1/8" - 1/4" from each page edge.
            // ----------
            // If you set printDocument.OriginAtMargins to 'false' this event
            // will print the largest rectangle permitted by the currently 
            // configured page margins. By default the page margins are 
            // usually 1" from each page edge but can be configured by the end
            // user or overridden in your code.
            // (ex: printDocument.DefaultPageSettings.Margins)

            // Grab a copy of our "soft margins" (configured printer settings)
            // Defaults to 1 inch margins, but could be configured otherwise by 
            // the end user. You can also specify some default page margins in 
            // your printDocument.DefaultPageSetting properties.
            RectangleF marginBounds = e.MarginBounds;

            // Grab a copy of our "hard margins" (printer's capabilities) 
            // This varies between printer models. Software printers like 
            // CutePDF will have no "physical limitations" and so will return 
            // the full page size 850,1100 for a letter page size.
            RectangleF printableArea = e.PageSettings.PrintableArea;

            // If we are print to a print preview control, the origin won't have 
            // been automatically adjusted for the printer's physical limitations. 
            // So let's adjust the origin for preview to reflect the printer's 
            // hard margins.
            if (printAction == PrintAction.PrintToPreview)
                g.TranslateTransform(printableArea.X, printableArea.Y);

            // Are we using soft margins or hard margins? Lets grab the correct 
            // width/height from either the soft/hard margin rectangles. The 
            // hard margins are usually a little wider than the soft margins.
            // ----------
            // Note: Margins are automatically applied to the rotated page size 
            // when the page is set to landscape, but physical hard margins are 
            // not (the printer is not physically rotating any mechanics inside, 
            // the paper still travels through the printer the same way. So we 
            // rotate in software for landscape)
            int availableWidth, availableHeight;
            getValidMargins(e, marginBounds, printableArea, out availableWidth, out availableHeight);

            // Draw our rectangle which will either be the soft margin rectangle 
            // or the hard margin (printer capabilities) rectangle.
            // ----------
            // Note: we adjust the width and height minus one as it is a zero, 
            // zero based co-ordinates system. This will put the rectangle just 
            // inside the available width and height.
            //g.DrawRectangle(Pens.Red, -1, -1, availableWidth, availableHeight);

            int pixelW_printer = 0;
            int pixelH_printer = 0;
            e.Graphics.PageUnit = GraphicsUnit.Pixel;
            e.HasMorePages = true;

            int pxWidthOfPage = 0;
            int pxHeightOfPage = 0;
            if (e.PageSettings.PaperSize.Kind == PaperKind.A4)
            {
                pxWidthOfPage = 210;
                pxHeightOfPage = 297;
            }
            else if (e.PageSettings.PaperSize.Kind == PaperKind.A3)
            {
                pxWidthOfPage = 297;
                pxHeightOfPage = 420;
            }
            else if (e.PageSettings.PaperSize.Kind == PaperKind.A2)
            {
                pxWidthOfPage = 420;
                pxHeightOfPage = 594;
            }
            else if (e.PageSettings.PaperSize.Kind == PaperKind.A5)
            {
                pxWidthOfPage = 210;
                pxHeightOfPage = 148;
            }
            else if (e.PageSettings.PaperSize.Kind == PaperKind.B4)
            {
                pxWidthOfPage = 250;
                pxHeightOfPage = 353;
            }
            else
            {
                e.HasMorePages = false;
                MessageBox.Show("暂不支持的打印尺寸！");
                return;
            }

            if (printDocument.DefaultPageSettings.Landscape)
            {
                int wTemp = pxWidthOfPage;
                int hTemp = pxHeightOfPage;
                pxWidthOfPage = hTemp;
                pxHeightOfPage = wTemp;
            }

            int pixelsForOnePageWidth;
            using (System.Drawing.Graphics gx = System.Drawing.Graphics.FromHwnd(IntPtr.Zero))
            {
                pixelsForOnePageWidth = (int)Math.Round(((double)gx.DpiX / 25.4f) * (double)pxWidthOfPage);
            }

            pixelW_printer = (int)Math.Round((e.Graphics.DpiX * (1 / 25.4f)) * pxWidthOfPage);
            pixelH_printer = (int)Math.Round((e.Graphics.DpiY * (1 / 25.4f)) * pxHeightOfPage);

            int pixelsForOnePageHeight = pixelsForOnePageWidth * pixelH_printer / pixelW_printer;

            int totalWidth = highResolutionMapBitmap.Width;
            int totalHeight = highResolutionMapBitmap.Height;
            int rowCount = (int)Math.Ceiling(1.0 * totalHeight / pixelsForOnePageHeight);
            int colCount = (int)Math.Ceiling(1.0 * totalWidth / pixelsForOnePageWidth);

            int rowAt = ipg / colCount;
            int colAt = ipg % colCount;

            ipg++;
            Rectangle rectImg = new Rectangle(colAt * pixelsForOnePageWidth, rowAt * pixelsForOnePageHeight, pixelsForOnePageWidth, pixelsForOnePageHeight);
            RectangleF srcF = rectImg;

            int wxx = pixelW_printer * availableWidth / e.PageBounds.Width;
            int hyy = pixelH_printer * availableHeight / e.PageBounds.Height;
            RectangleF dstF = new RectangleF(0, 0, wxx, hyy);
            e.Graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
            e.Graphics.DrawImage(highResolutionMapBitmap, dstF, srcF, GraphicsUnit.Pixel);
            //e.Graphics.FillEllipse(Brushes.Blue, wxx - 20, hyy - 20, 20, 20);
            if (paintBorderLine)
            {
                g.DrawRectangle(Pens.LightGray, 0, 0, wxx, hyy);
            }
            if (paintMark)
            {
                g.DrawString("(" + rowAt + "," + colAt + ")", new Font("宋体", 10), Brushes.LightGray, new PointF(1, hyy - 80));
            }

            if (ipg == rowCount * colCount)
            {
                e.HasMorePages = false;
            }

        }

        private void getValidMargins(PrintPageEventArgs e, RectangleF marginBounds, RectangleF printableArea, out int availableWidth, out int availableHeight)
        {
            if (printDocument.OriginAtMargins)
            {
                availableWidth = (int)Math.Floor(marginBounds.Width);
                availableHeight = (int)Math.Floor(marginBounds.Height);
            }
            else
            {
                if (e.PageSettings.Landscape)
                {
                    availableWidth = (int)Math.Floor(printableArea.Height);
                    availableHeight = (int)Math.Floor(printableArea.Width);
                }
                else
                {
                    availableWidth = (int)Math.Floor(printableArea.Width);
                    availableHeight = (int)Math.Floor(printableArea.Height);
                }
            }
        }

        private Bitmap highResolutionMapBitmap = null;
        internal void SetToPrintImage(Bitmap bmp)
        {
            this.highResolutionMapBitmap = bmp;
        }

        PrintAction printAction = PrintAction.PrintToFile;
        private void printDocument_BeginPrint(object sender, PrintEventArgs e)
        {
            // Save our print action so we know if we are printing 
            // a preview or a real document.
            printAction = e.PrintAction;

            // Set some preferences, our method should print a box with any 
            // combination of these properties being true/false.
            printDocument.OriginAtMargins = false;   //true = soft margins, false = hard margins
            //printDocument.DefaultPageSettings.Landscape = false;
        }
    }
}
