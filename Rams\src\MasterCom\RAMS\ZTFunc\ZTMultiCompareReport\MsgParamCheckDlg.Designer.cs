﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class MsgParamCheckDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MsgParamCheckDlg));
            this.button1 = new System.Windows.Forms.Button();
            this.button2 = new System.Windows.Forms.Button();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripSet = new System.Windows.Forms.ToolStripMenuItem();
            this.btnselectAll = new System.Windows.Forms.Button();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridViewDetail = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnCheck = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnValueRangeDes = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnValueRange = new DevExpress.XtraGrid.Columns.GridColumn();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewDetail)).BeginInit();
            this.SuspendLayout();
            // 
            // button1
            // 
            this.button1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.button1.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.button1.Location = new System.Drawing.Point(360, 242);
            this.button1.Margin = new System.Windows.Forms.Padding(2);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(50, 25);
            this.button1.TabIndex = 0;
            this.button1.Text = "确认";
            this.button1.UseVisualStyleBackColor = true;
            // 
            // button2
            // 
            this.button2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.button2.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.button2.Location = new System.Drawing.Point(422, 242);
            this.button2.Margin = new System.Windows.Forms.Padding(2);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(50, 25);
            this.button2.TabIndex = 1;
            this.button2.Text = "取消";
            this.button2.UseVisualStyleBackColor = true;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripSet});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(101, 26);
            // 
            // ToolStripSet
            // 
            this.ToolStripSet.Name = "ToolStripSet";
            this.ToolStripSet.Size = new System.Drawing.Size(100, 22);
            this.ToolStripSet.Text = "设置";
            this.ToolStripSet.Click += new System.EventHandler(this.ToolStripSet_Click);
            // 
            // btnselectAll
            // 
            this.btnselectAll.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnselectAll.Location = new System.Drawing.Point(12, 241);
            this.btnselectAll.Name = "btnselectAll";
            this.btnselectAll.Size = new System.Drawing.Size(45, 25);
            this.btnselectAll.TabIndex = 4;
            this.btnselectAll.Text = "全选";
            this.btnselectAll.UseVisualStyleBackColor = true;
            this.btnselectAll.Click += new System.EventHandler(this.btnselectAll_Click);
            // 
            // gridControl1
            // 
            this.gridControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gridControl1.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.gridViewDetail;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(489, 237);
            this.gridControl1.TabIndex = 25;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewDetail});
            this.gridControl1.DoubleClick += new System.EventHandler(this.ToolStripSet_Click);
            // 
            // gridViewDetail
            // 
            this.gridViewDetail.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            this.gridViewDetail.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnCheck,
            this.gridColumnName,
            this.gridColumnValueRangeDes,
            this.gridColumnValueRange});
            this.gridViewDetail.GridControl = this.gridControl1;
            this.gridViewDetail.Name = "gridViewDetail";
            this.gridViewDetail.OptionsSelection.MultiSelect = true;
            this.gridViewDetail.OptionsView.ShowDetailButtons = false;
            this.gridViewDetail.OptionsView.ShowGroupPanel = false;
            this.gridViewDetail.OptionsView.ShowIndicator = false;
            // 
            // gridColumnCheck
            // 
            this.gridColumnCheck.Caption = "选择";
            this.gridColumnCheck.FieldName = "IsChecked";
            this.gridColumnCheck.Name = "gridColumnCheck";
            this.gridColumnCheck.Visible = true;
            this.gridColumnCheck.VisibleIndex = 0;
            this.gridColumnCheck.Width = 57;
            // 
            // gridColumnName
            // 
            this.gridColumnName.Caption = "参数名";
            this.gridColumnName.FieldName = "ParamNameDes";
            this.gridColumnName.Name = "gridColumnName";
            this.gridColumnName.OptionsColumn.AllowEdit = false;
            this.gridColumnName.Visible = true;
            this.gridColumnName.VisibleIndex = 1;
            this.gridColumnName.Width = 157;
            // 
            // gridColumnValueRangeDes
            // 
            this.gridColumnValueRangeDes.Caption = "参数值范围";
            this.gridColumnValueRangeDes.FieldName = "ParamRightValueDes";
            this.gridColumnValueRangeDes.Name = "gridColumnValueRangeDes";
            this.gridColumnValueRangeDes.OptionsColumn.AllowEdit = false;
            this.gridColumnValueRangeDes.Visible = true;
            this.gridColumnValueRangeDes.VisibleIndex = 2;
            this.gridColumnValueRangeDes.Width = 133;
            // 
            // gridColumnValueRange
            // 
            this.gridColumnValueRange.Caption = "对应协议值范围(整数)";
            this.gridColumnValueRange.FieldName = "ParamRightValue";
            this.gridColumnValueRange.Name = "gridColumnValueRange";
            this.gridColumnValueRange.OptionsColumn.AllowEdit = false;
            this.gridColumnValueRange.Visible = true;
            this.gridColumnValueRange.VisibleIndex = 3;
            this.gridColumnValueRange.Width = 138;
            // 
            // MsgParamCheckDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(489, 278);
            this.Controls.Add(this.gridControl1);
            this.Controls.Add(this.btnselectAll);
            this.Controls.Add(this.button2);
            this.Controls.Add(this.button1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(2);
            this.Name = "MsgParamCheckDlg";
            this.Text = "三层信令小区参数选择";
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewDetail)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.Button button2;
        private System.Windows.Forms.NumericUpDown numMaxTPDistance;
        private System.Windows.Forms.CheckBox chkDistance;
        private System.Windows.Forms.Button btnselectAll;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripSet;
        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewDetail;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCheck;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnValueRangeDes;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnValueRange;
    }
}