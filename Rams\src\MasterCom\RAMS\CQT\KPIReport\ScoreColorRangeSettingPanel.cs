﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.CQT;

namespace MasterCom.RAMS.Util
{
    public partial class ScoreColorRangeSettingPanel : UserControl
    {
        private float rangeMin;
        private float rangeMax;
        private double scoreMin;
        private double scoreMax;
        protected List<CQTKPIScoreColorRange> rangeValues = new List<CQTKPIScoreColorRange>();
        public ScoreColorRangeSettingPanel()
        {
            InitializeComponent();
        }
        
        public bool IsSmoothScore { get; set; } = true;

        protected ScoreOrderType scoreType;
        public void SetRange(List<CQTKPIScoreColorRange> rangeColors, float rgMin, float rgMax, double scoreMin, double scoreMax,ScoreOrderType scoreType)
        {
            rangeValues = rangeColors;
            this.rangeMin = rgMin;
            this.rangeMax = rgMax;
            this.scoreMin = scoreMin;
            this.scoreMax = scoreMax;
            this.scoreType = scoreType;
            checkButtonState();
            rowCountChanged();
        }

        private void checkButtonState()
        {
            buttonModify.Enabled = dataGridView.SelectedRows.Count == 1;
            buttonRemove.Enabled = dataGridView.SelectedRows.Count == 1;
        }

        protected virtual ScoreColorRangeSettingBox getRangeValueSettingBox(CQTKPIScoreColorRange rangeValue, float min, float max)
        {
            if (rangeValue == null)
            {
                rangeValue = new CQTKPIScoreColorRange(min, max, scoreMin, scoreMax, Color.Green, "",scoreType);
                rangeValue.IsSmoonthScore = IsSmoothScore;
            }
            return new ScoreColorRangeSettingBox(rangeValue, min, max, scoreMin, scoreMax, scoreType);
        }

        protected virtual ScoreColorRangeAutoSettingBox getRangeValueAutoSettingBox(float min, float max)
        {
            return new ScoreColorRangeAutoSettingBox(min, max, scoreMin, scoreMax, scoreType);
        }

        private void addRangeValue(CQTKPIScoreColorRange rangeValue)
        {
            for (int i = 0; i < rangeValues.Count; i++)
            {
                if (rangeValue.Min < rangeValues[i].Min)
                {
                    rangeValues.Insert(i, rangeValue);
                    return;
                }
            }
            rangeValues.Add(rangeValue);
        }

        private void rowCountChanged()
        {
            if (rangeValues==null)
            {
                return;
            }
            dataGridView.RowCount = rangeValues.Count;
            foreach (DataGridViewRow row in dataGridView.Rows)
            {
                row.Height = 18;
            }
            dataGridView.Invalidate();
        }

        private void buttonAdd_Click(object sender, EventArgs e)
        {
            ScoreColorRangeSettingBox box = getRangeValueSettingBox(null, rangeMin, rangeMax);
            if (box.ShowDialog() == DialogResult.OK)
            {
                addRangeValue(box.ScoreColorRange);
                rowCountChanged();
            }
        }

        private void buttonModify_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count > 0)
            {
                CQTKPIScoreColorRange rangeValue = rangeValues[dataGridView.SelectedRows[0].Index];
                ScoreColorRangeSettingBox box = getRangeValueSettingBox(rangeValue, rangeMin, rangeMax);
                if (box.ShowDialog() == DialogResult.OK)
                {
                    dataGridView.Invalidate();
                }
            }
            else
            {
                checkButtonState();
            }
        }

        private void buttonRemove_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count==0)
            {
                checkButtonState();
                return;
            }
            rangeValues.RemoveAt(dataGridView.SelectedRows[0].Index);
            rowCountChanged();
        }

        private void buttonAutoSetting_Click(object sender, EventArgs e)
        {
            ScoreColorRangeAutoSettingBox box = getRangeValueAutoSettingBox(rangeMin, rangeMax);
            box.IsSmoothScore = IsSmoothScore;
            if (box.ShowDialog() == DialogResult.OK)
            {
                rangeValues.Clear();
                foreach (CQTKPIScoreColorRange rangeValue in box.RangeValues)
                {
                    rangeValue.ScoreOrderType = scoreType;
                    addRangeValue(rangeValue);
                }
                rowCountChanged();
            }
        }

        #region dataGridView 
        private void dataGridView_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.ColumnIndex == 2)//color
            {
                try
                {
                    e.CellStyle.BackColor = rangeValues[e.RowIndex].Color;
                    e.CellStyle.SelectionBackColor = rangeValues[e.RowIndex].Color;
                }
                catch
                {
                    //continue
                }
            }
        }
        private void dataGridView_CellValueNeeded(object sender, DataGridViewCellValueEventArgs e)
        {
            if (e.RowIndex >= rangeValues.Count)
            {
                return;
            }
            if (e.ColumnIndex == 0)
            {
                e.Value = rangeValues[e.RowIndex].RangeDescription;
            }
            else if (e.ColumnIndex==1)
            {
                e.Value = rangeValues[e.RowIndex].ScoreRangeDescription;
            }
            else if (e.ColumnIndex==2)
            {
                dataGridView[e.ColumnIndex, e.RowIndex].Style.BackColor = rangeValues[e.RowIndex].Color;
            }
            else if (e.ColumnIndex==3)
            {
                e.Value = rangeValues[e.RowIndex].DesInfo;
            }
        }
        private void dataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            buttonModify_Click(null, null);
        }
        private void dataGridView_SelectionChanged(object sender, EventArgs e)
        {
            checkButtonState();
        }
        #endregion
    
    }
}
