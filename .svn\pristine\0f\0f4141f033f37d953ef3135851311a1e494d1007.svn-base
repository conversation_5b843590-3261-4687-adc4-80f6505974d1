﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Util
{
    public partial class FormulaEditor : BaseForm
    {
        public FormulaEditor()
        {
            InitializeComponent();
            editor.SubmitFormula += editor_SubmitFormula;
        }

        void editor_SubmitFormula(object sender, EventArgs e)
        {
            MasterCom.RAMS.Util.KPIFormulaEditor.SubmitFormulaEventArgs fe = e as MasterCom.RAMS.Util.KPIFormulaEditor.SubmitFormulaEventArgs;
            formula = fe.Formula;
            Caption = fe.Desc;
        }

        private string formula = string.Empty;
        public string Formula
        {
            get { return formula; }
            set
            {
                formula = value;
                editor.Formula = value;
            }
        }

        public string Caption
        {
            get;
            private set;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            formula = editor.Formula;
            if (formula == null || string.IsNullOrEmpty(formula.Trim()))
            {
                MessageBox.Show("公式不能为空！");
                return;
            }
            try
            {//去掉单位
                formula = formula.Substring(formula.IndexOf('{')
                                , formula.IndexOf('}') + 1);
            }
            catch
            {
                MessageBox.Show("公式格式有误！");
            }
            DialogResult = DialogResult.OK;
        }

    }
}
