﻿using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCellCoverLapByRegion_NR : ZTCellCoverLapByRegion
    {
        private static ZTCellCoverLapByRegion_NR instance = null;
        public new static ZTCellCoverLapByRegion_NR GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCellCoverLapByRegion_NR();
                    }
                }
            }
            return instance;
        }

        public ZTCellCoverLapByRegion_NR()
          : base()
        {
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        public override string Name
        {
            get { return "过覆盖分析_NR"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35002, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = NRTpHelper.InitBaseReplayParamSample(false, false);
            NRTpHelper.AddParam(columnsDef, "NR_SS_RSRQ", 0);
            NRTpHelper.AddParam(columnsDef, "NR_lte_RSRP", 0);
            NRTpHelper.AddParam(columnsDef, "NR_lte_RSRQ", 0);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        ZTCellCoverLapSetDlg_NR fDlg = null;
        public CellCoverLapCondition_NR SettingCondition_NR { get; set; } = new CellCoverLapCondition_NR();
        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (fDlg == null)
            {
                fDlg = new ZTCellCoverLapSetDlg_NR();
            }
            if (fDlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            fDlg.GetSettingFilterRet(SettingCondition_NR);
            curFilterRxlev = SettingCondition_NR.CurFilterRxlev;
            curMinSampleCount = SettingCondition_NR.CurMinSampleCount;
            curMinPercent = SettingCondition_NR.CurMinPercent;
            curMinDistance = SettingCondition_NR.CurMinDistance;
            curMaxDistance = SettingCondition_NR.CurMaxDistance;
            nearestCellCount = SettingCondition_NR.NearestCellCount;
            disFactor = SettingCondition_NR.DisFactor;

            return true;
        }

        protected virtual bool isTPInRegion(TestPoint tp)
        {
            bool inRegion = Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
            if (inRegion)
            {
                return true;
            }
            return false;
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                bool inRegion = isTPInRegion(tp);
                if (inRegion)  //进行过覆盖算法运算
                {
                    float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp, true);
                    if (rsrp == null || rsrp < curFilterRxlev)
                    {
                        return false;
                    }
                    NRCell cell = tp.GetMainCell_NR();
                    if (cell == null || cell.Type == NRBTSType.Indoor || cell.Antennas == null || cell.Antennas.Count == 0)
                    {
                        return false;
                    }

                    CellCoverLap_NR covLap = getCovLap(cell);

                    if (fileIDNameDic.ContainsKey(tp.FileID) && !covLap.strFileID.Contains(fileIDNameDic[tp.FileID] + ""))
                    {
                        covLap.strFileID += fileIDNameDic[tp.FileID] + ",";
                    }

                    return judgeCovLapDistance(tp, rsrp, cell, covLap);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private bool judgeCovLapDistance(TestPoint tp, float? rsrp, NRCell cell, CellCoverLap_NR covLap)
        {
            double distanceToCell = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, cell.Longitude, cell.Latitude);
            bool isBadCheck = distanceToCell > covLap.rationalDistance;
            if (isBadCheck)
            {
                float? rsrq = NRTpHelper.NrTpManager.GetSCellRsrq(tp, true);
                covLap.AddBadSample(tp, distanceToCell, (float)rsrp, rsrq);
                covLap.AddOtherTPInfo(tp);
                return true;
            }
            else
            {
                covLap.goodSampleCount++;
                return false;
            }
        }

        private CellCoverLap_NR getCovLap(NRCell cell)
        {
            CellCoverLap_NR covLap = null;
            CellCoverLap clTmp = null;
            if (!cellLapRetDic.TryGetValue(cell.Name, out clTmp))
            {
                double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(cell, nearestCellCount);
                covLap = new CellCoverLap_NR(cell, radiusOfCell);
                covLap.rationalDistance = radiusOfCell * disFactor;
                covLap.nearestBTSs = MasterCom.ES.Data.CfgDataProvider.GetNearestBTSs(cell, nearestCellCount);
                covLap.mnger = new DTDataManager(MainModel.GetInstance());
                cellLapRetDic[cell.Name] = covLap;
            }
            else
            {
                covLap = (CellCoverLap_NR)clTmp;
            }

            return covLap;
        }

        protected override void getResultAfterQuery()
        {
            curSelDIYSampleGroup.ThemeName = "NR:SS_RSRP";
            FilterCellCoverLap();
        }

        protected override void FireShowFormAfterQuery()
        {
            if (cellLapRetDic.Count == 0)
            {
                XtraMessageBox.Show(MainModel.MainForm, "没有符合条件的数据。");
                return;
            }
            ZTCellCoverLapListForm_NR cellCoverLapListForm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTCellCoverLapListForm_NR)) as ZTCellCoverLapListForm_NR;
            if (cellCoverLapListForm == null || cellCoverLapListForm.IsDisposed)
            {
                cellCoverLapListForm = new ZTCellCoverLapListForm_NR(MainModel);
            }
            cellCoverLapListForm.showCellSet(cellLapRetDic, curMinSampleCount, curMinPercent, curMinDistance, curMaxDistance, ShowNearBTS);
            cellCoverLapListForm.Owner = MainModel.MainForm;
            cellCoverLapListForm.Visible = true;
            cellCoverLapListForm.BringToFront();
        }
    }

    public class CellCoverLap_NR : CellCoverLap
    {
        public NRCell Cell { get; set; }

        protected int lteRsrpCount = 0;
        protected float lteRsrpSum = 0;
        public string AvgLteRSRP { get { return caculateAvg(lteRsrpCount, lteRsrpSum); } }

        protected int lteRsrqCount = 0;
        protected float lteRsrqSum = 0;
        public string AvgLteRSRQ { get { return caculateAvg(lteRsrqCount, lteRsrqSum); } }

        public CellCoverLap_NR(NRCell cell, double idealCoverDis)
        {
            this.Cell = cell;
            this._CellCovRadius = idealCoverDis;
            //if (cell.Altitude != 0 && cell.Altitude != 999)
            //{
            //    double dir = 90 + cell.Downward / 2 - Math.Atan(idealCoverDis / cell.Altitude) * 180 / Math.PI;
            //    SuggustDownDir = Math.Round(dir, 2).ToString();
            //}
            //else
            //{
            //    SuggustDownDir = "无挂高信息，无法计算建议下倾角";
            //}
        }

        //public short CellDownDir
        //{
        //    get { return this.Cell.Downward; }
        //}

        //public string SuggustDownDir
        //{
        //    get;
        //    private set;
        //}

        public void AddBadSample(TestPoint tp, double distanceToCell, float rxlevSub, float? rxQual)
        {
            badSampleCount++;
            mnger.Add(tp);
            distanceAllBadList.Add((float)distanceToCell);
            if (distanceToCell > maxBadDistance)
            {
                maxBadDistance = (float)distanceToCell;
            }
            if (distanceToCell < minBadDistance)
            {
                minBadDistance = (float)distanceToCell;
            }
            TestPointCount++;
            SumRxLev += rxlevSub;
            if (rxlevSub > MaxRxLev)
            {
                MaxRxLev = rxlevSub;
            }
            if (rxlevSub < MinRxLev)
            {
                MinRxLev = rxlevSub;
            }
            if (rxQual != null)
            {
                RxQualTestPointCount++;
                SumRxQual += (float)rxQual;
                if (rxQual > MaxRxQual)
                {
                    MaxRxQual = (float)rxQual;
                }
                if (rxQual < MinRxQual)
                {
                    MinRxQual = (float)rxQual;
                }
            }

            if (istime > tp.Time)
            {
                istime = tp.Time;
            }
            if (ietime < tp.Time)
            {
                ietime = tp.Time;
            }
        }

        public void AddOtherTPInfo(TestPoint tp)
        {
            float? lteRsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(tp, true);
            if (lteRsrp != null)
            {
                lteRsrpCount++;
                lteRsrpSum += (float)lteRsrp;
            }

            float? lteRsrq = NRTpHelper.GetValidData((float?)tp["NR_lte_RSRQ"], -40, 40);
            if (lteRsrq != null)
            {
                lteRsrqCount++;
                lteRsrqSum += (float)lteRsrq;
            }
        }

        protected string caculateAvg(int count, float sum)
        {
            float? res = null;
            if (count > 0)
            {
                res = (float?)Math.Round(sum / count, 2);
                return res.ToString();
            }
            else
            {
                return "";
            }
        }
    }

    public class ZTCellCoverLapByFile_NR : ZTCellCoverLapByRegion_NR
    {
        private static ZTCellCoverLapByFile_NR instance = null;
        public new static ZTCellCoverLapByFile_NR GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCellCoverLapByFile_NR();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "过覆盖分析_NR(按文件)"; }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isTPInRegion(TestPoint tp)
        {
            return true;
        }
    }
}
