﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteAntennaOverlapCoverage : ZTDiyCellMultiCoverageQueryByRegion
    {
        private static ZTLteAntennaOverlapCoverage instance = null;
        protected LTEAntennaOverlapCoverageSetForm LTEAntennaOverlapCoverageDlg = null;

        public new static ZTLteAntennaOverlapCoverage GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTLteAntennaOverlapCoverage();
                    }
                }
            }
            return instance;
        }

        protected ZTLteAntennaOverlapCoverage()
            : base()
        {
            setRxlevDiff = 6;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_SCAN_TOPN);

            iMinRsrpValue = -110;
            iMaxRsrpValue = -10;
            iPoint6dBm = 6;
            antCfgParaDic = new Dictionary<int, AntCfgSub>();
            antCfgParaSDic = new Dictionary<int, AntCfgSub>();
        }

        public override string Name
        {
            get { return "LTE覆盖干扰优化"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 28000, 28006, this.Name);
        }

        public int iMinRsrpValue { get; set; }/*主覆盖小区有效区间*/
        public int iMaxRsrpValue { get; set; }
        public int iPoint6dBm { get; set; }

        protected Dictionary<string, LteSampleMainInfo> dicLteOverlapData = new Dictionary<string, LteSampleMainInfo>();
        //状态库天线数据
        public Dictionary<int, AntCfgSub> antCfgParaDic { get; set; }
        public Dictionary<int, AntCfgSub> antCfgParaSDic { get; set; }
        ZTAntennaBase antBase;//用于地图GIS判断

        Dictionary<int, AntennaPara> antParaEciDic = new Dictionary<int, AntennaPara>();
        Dictionary<string, AntennaPara> antParaCellNameDic = new Dictionary<string, AntennaPara>();
        readonly AntTimeCfg timeCfg = new AntTimeCfg();

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                coFreq = false;
                return true;
            }
            if (LTEAntennaOverlapCoverageDlg == null)
            {
                LTEAntennaOverlapCoverageDlg = new LTEAntennaOverlapCoverageSetForm();
            }
            if (LTEAntennaOverlapCoverageDlg.ShowDialog() == DialogResult.OK)
            {
                int iMinRsrp;
                int iMaxRsrp;
                LTEAntennaOverlapCoverageDlg.GetSettingFilterRet(out iMinRsrp, out iMaxRsrp);
                iMinRsrpValue = iMinRsrp;
                iMaxRsrpValue = iMaxRsrp;
                return true;
            }
            return false;
        }

        protected override void fireShowForm()
        {
            LTEAntennaOverlapCoverageForm frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LTEAntennaMultiCoverageForm).FullName) as LTEAntennaOverlapCoverageForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new LTEAntennaOverlapCoverageForm(MainModel);
            }
            frm.FillData(dicLteOverlapData);
            if (!frm.Visible)
            {
                frm.Show(MainModel.MainForm);
            }
            frm.BringToFront();
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            clearDic();

            //地图匹配处理
            antBase = new ZTAntennaBase(MainModel);
            antBase.InitRegionMop2();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    int iCount = testPointList.Count;
                    for (int i = 0; i < iCount; i++)
                    {
                        TestPoint testPoint = testPointList[i];
                        if (isValidTestPoint(testPoint) && testPoint is ScanTestPoint_LTE)
                        {
                            Dictionary<int, LteSampleSubInfo> maxEarfcnDic = new Dictionary<int, LteSampleSubInfo>();//各频点最大值列表
                            List<LteSampleSubInfo> cellList = doWithSCANData(testPoint, ref maxEarfcnDic, false, false);
                            doWithAllSample(testPoint, cellList, maxEarfcnDic, ref dicLteOverlapData);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
            }
        }

        /// <summary>
        /// 全局变量初始化
        /// </summary>
        private void clearDic()
        {
            antCfgParaDic.Clear();
            antCfgParaSDic.Clear();
            dicLteOverlapData.Clear();
        }

        /// <summary>
        /// 分析扫频采样点
        /// </summary>
        public List<LteSampleSubInfo> doWithSCANData(TestPoint testPoint, ref Dictionary<int, LteSampleSubInfo> maxEarfcnDic
            , bool isMainSite, bool isMode3)
        {
            float maxRsrp = -10;    //最大有效电平 
            float minRsrp = -140;   //最小有效电平
            float maxSinr = 40;     //最大有效信噪比
            float minSinr = -25;    //最小有效信噪比

            List<LteSampleSubInfo> cellList = new List<LteSampleSubInfo>();
            for (int index = 0; index < 50; index++)
            {
                int? earfcn = (int?)testPoint["LTESCAN_TopN_EARFCN", index];
                int? pci = (int?)(short?)testPoint["LTESCAN_TopN_PCI", index];
                float? rsrp = (float?)testPoint["LTESCAN_TopN_CELL_Specific_RSRP", index];
                float? sinr = (float?)testPoint["LTESCAN_TopN_CELL_Specific_RSSINR", index];
                if (earfcn == null || pci == null || rsrp == null || sinr == null
                    || rsrp >= maxRsrp || rsrp <= minRsrp || sinr >= maxSinr || sinr <= minSinr)
                {
                    continue;
                }
                if (isMainSite && isMode3 && (int)pci % 3 != 0)
                {
                    continue;
                }
                LTECell servCell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(
                                    testPoint.DateTime, earfcn, pci, testPoint.Longitude, testPoint.Latitude);
                LteSampleSubInfo LTEInfo = new LteSampleSubInfo(index, (int)earfcn, (int)pci, (float)rsrp, (float)sinr, servCell);
                bool isValid = dealCellTPAngle(testPoint, isMainSite, servCell, LTEInfo);
                if (!isValid)
                {
                    continue;
                }
                if (!maxEarfcnDic.ContainsKey((int)earfcn))
                {
                    maxEarfcnDic.Add((int)earfcn, LTEInfo);
                }
                cellList.Add(LTEInfo);
            }
            return cellList;
        }

        private bool dealCellTPAngle(TestPoint testPoint, bool isMainSite, LTECell servCell, LteSampleSubInfo LTEInfo)
        {
            if (servCell != null)
            {
                double dAngleDiffRel = 0;
                double dAngLeDiffAbs = 0;
                double cellDistance = 0;
                ZTAntFuncHelper.CellCollection col = new ZTAntFuncHelper.CellCollection()
                {
                    Cell = null,
                    TDCell = null,
                    LteCell = servCell
                };
                ZTAntFuncHelper.calcSampleAngle(col, testPoint.Longitude, testPoint.Latitude,
                                                out dAngleDiffRel, out dAngLeDiffAbs, out cellDistance);
                LTEInfo.fDist = (float)cellDistance;
                LTEInfo.iDir = (int)dAngleDiffRel;
                if (isMainSite && (dAngleDiffRel < -60 || dAngleDiffRel > 60))
                    return false;
            }
            else
            {
                if (isMainSite)
                    return false;
            }
            return true;
        }

        /// <summary>
        /// 统计小区信息
        /// </summary>
        public void doWithAllSample(TestPoint testPoint, List<LteSampleSubInfo> cellList, Dictionary<int, LteSampleSubInfo> mainDic
            , ref Dictionary<string, LteSampleMainInfo> dicLteOverlapData)
        {
            string strGrid = "";
            if (antBase != null)
            {
                antBase.isContainPoint(testPoint.Longitude, testPoint.Latitude, ref strGrid);
            }
            LongLat longLat = new LongLat((float)testPoint.Longitude, (float)testPoint.Latitude);
            List<int> overLapList = new List<int>();//是否已添加重叠覆盖采样点信息
            Dictionary<int, float> tmpDic = new Dictionary<int, float>();
            foreach (LteSampleSubInfo curSample in cellList)
            {
                try
                {
                    LteSampleSubInfo mainSample;
                    if (!mainDic.TryGetValue(curSample.iEarfcn, out mainSample))
                    {
                        break;
                    }
                    LteSampleMainInfo mainInfo = getMainInfo(dicLteOverlapData, mainSample);

                    LteSampleMainInfo curInfo = getCurInfo(dicLteOverlapData, curSample);
                    curInfo.fillCellInfo(curSample, longLat, strGrid, testPoint);//全部采样点
                    if (curSample.CellName == mainSample.CellName)
                    {
                        curInfo.fillMainCellInfo(curSample, longLat, testPoint);//主覆盖采样点
                    }
                    else if ((mainSample.fRSRP <= iMaxRsrpValue && mainSample.fRSRP >= iMinRsrpValue)
                              && mainSample.fRSRP - curSample.fRSRP < iPoint6dBm)
                    {
                        dealMainInfo(testPoint, longLat, overLapList, curSample, mainSample, mainInfo);
                        dicLteOverlapData[mainSample.CellName] = mainInfo;
                    }
                    if (!tmpDic.ContainsKey(curSample.iEarfcn) && curSample.CellName != mainSample.CellName)
                    {
                        float diffRsrp = mainSample.fRSRP - curSample.fRSRP;
                        tmpDic.Add(curSample.iEarfcn, diffRsrp);
                        mainInfo.LTEMainCellInfo.lteMainCellCover.calcRSRPSampleCount(diffRsrp, curSample, longLat);
                    }
                    dicLteOverlapData[curSample.CellName] = curInfo;
                }
                catch (Exception exp)
                {
                    log.Error(exp.Message);
                }
            }
        }

        private static LteSampleMainInfo getMainInfo(Dictionary<string, LteSampleMainInfo> dicLteOverlapData, LteSampleSubInfo mainSample)
        {
            LteSampleMainInfo mainInfo;
            if (!dicLteOverlapData.TryGetValue(mainSample.CellName, out mainInfo))
            {
                mainInfo = new LteSampleMainInfo();
            }

            return mainInfo;
        }

        private static LteSampleMainInfo getCurInfo(Dictionary<string, LteSampleMainInfo> dicLteOverlapData, LteSampleSubInfo curSample)
        {
            LteSampleMainInfo curInfo;
            if (!dicLteOverlapData.TryGetValue(curSample.CellName, out curInfo))
            {
                curInfo = new LteSampleMainInfo();
            }

            return curInfo;
        }

        private void dealMainInfo(TestPoint testPoint, LongLat longLat, List<int> overLapList, LteSampleSubInfo curSample, LteSampleSubInfo mainSample, LteSampleMainInfo mainInfo)
        {
            if (!overLapList.Contains(curSample.iEarfcn))
            {
                mainInfo.fillOverlapCellInfo(mainSample, longLat, testPoint);//重叠覆盖采样点
                overLapList.Add(curSample.iEarfcn);
            }
            mainInfo.fillCellPairInfo(mainSample, curSample, longLat);//主邻关系小区对
        }

        /// <summary>
        /// 判断采样点是否有效
        /// </summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (Condition.Geometorys == null || Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
            {
                return true;
            }
            return false;
        }

        protected override void getResultsAfterQuery()
        {
            try
            {
                WaitBox.ProgressPercent = 20;
                WaitBox.Text = "正在关联状态库小区...";
                doWithAntCfgData();

                WaitBox.ProgressPercent = 20;
                WaitBox.Text = "正在关联状态库小区权值信息...";
                doWithParaData();

                WaitBox.ProgressPercent = 50;
                WaitBox.Text = "正在计算小区覆盖面积...";
                int index = 1;
                foreach (string strCellName in dicLteOverlapData.Keys)
                {
                    LteSampleMainInfo lteInfo = dicLteOverlapData[strCellName];
                    lteInfo.idx = index++;
                    if (lteInfo.servCell != null)
                    {
                        lteInfo.antCgfSub = getAntCfgSub(lteInfo.servCell.ECI);
                    }
                    lteInfo.calcRegionArea();//计算面积
                    setNbCellInfo(lteInfo);
                    dicLteOverlapData[strCellName].antennaPare = getAntennaParaData(lteInfo);
                }

                WaitBox.ProgressPercent = 80;
                WaitBox.Text = "正在同步主邻信息...";
                ZTSiteDistanceQueryByRegion zTSite = new ZTSiteDistanceQueryByRegion(MainModel);
                zTSite.initOutdoorBtsSet();
                foreach (string strCellName in dicLteOverlapData.Keys)
                {
                    foreach (string nCellName in dicLteOverlapData[strCellName].nbCellInfoDic.Keys)
                    {
                        dicLteOverlapData[strCellName].nbCellInfoDic[nCellName].idx = dicLteOverlapData[nCellName].idx;
                        dicLteOverlapData[strCellName].nbCellInfoDic[nCellName].antCgfSub = dicLteOverlapData[nCellName].antCgfSub;
                        dicLteOverlapData[strCellName].nbCellInfoDic[nCellName].antennaPare = dicLteOverlapData[nCellName].antennaPare;
                        string strSameBts = "否";
                        string strSameBts90 = "否";
                        float fSameAngle = -999;
                        setSameBtsInfo(strCellName, nCellName, ref strSameBts, ref strSameBts90, ref fSameAngle);
                        dicLteOverlapData[strCellName].nbCellInfoDic[nCellName].strSameBts = strSameBts;
                        dicLteOverlapData[strCellName].nbCellInfoDic[nCellName].strSameBts90 = strSameBts90;
                        dicLteOverlapData[strCellName].nbCellInfoDic[nCellName].fSameAngle = fSameAngle;
                        if (!dicLteOverlapData[strCellName].nbCellInfoDic[nCellName].nbCellInfo.gridList.Contains(dicLteOverlapData[nCellName].LTECellInfo.strGrid))
                            dicLteOverlapData[strCellName].nbCellInfoDic[nCellName].nbCellInfo.gridList.Add(dicLteOverlapData[nCellName].LTECellInfo.strGrid);
                    }
                    //==================站间距和所属区域=============
                    LTECell cell = dicLteOverlapData[strCellName].servCell;
                    setOverlapDataArea(zTSite, strCellName, cell);
                }
            }
            catch (Exception exp)
            {
                log.Error(exp.Message);
            }
        }

        private void setNbCellInfo(LteSampleMainInfo lteInfo)
        {
            foreach (string strNbName in lteInfo.nbCellInfoDic.Keys)
            {
                if (lteInfo.nbCellInfoDic[strNbName].nbCell != null)
                {
                    lteInfo.nbCellInfoDic[strNbName].antCgfSub = getAntCfgSub(lteInfo.nbCellInfoDic[strNbName].nbCell.ECI);
                    if (lteInfo.servCell != null)
                    {
                        lteInfo.nbCellInfoDic[strNbName].strAntennaType = lteInfo.getDisturbType(
                            lteInfo.nbCellInfoDic[strNbName].nbCell, lteInfo.nbCellInfoDic[strNbName].mainTpDic
                            );
                    }
                }
            }
        }

        private void setSameBtsInfo(string strCellName, string nCellName, ref string strSameBts, ref string strSameBts90, ref float fSameAngle)
        {
            if (dicLteOverlapData[strCellName].nbCellInfoDic[nCellName].antCgfSub.iEnodebID
                == dicLteOverlapData[strCellName].antCgfSub.iEnodebID
                && dicLteOverlapData[strCellName].antCgfSub.iEnodebID != 0
                && dicLteOverlapData[strCellName].nbCellInfoDic[nCellName].antCgfSub.fLatitude
                    == dicLteOverlapData[strCellName].antCgfSub.fLatitude
                && dicLteOverlapData[strCellName].nbCellInfoDic[nCellName].antCgfSub.fLongitude
                    == dicLteOverlapData[strCellName].antCgfSub.fLongitude)
            {
                strSameBts = "是";
                float fAngle = Math.Abs(dicLteOverlapData[strCellName].nbCellInfoDic[nCellName].antCgfSub.方向角
                             - dicLteOverlapData[strCellName].antCgfSub.方向角);
                fAngle = fAngle > 180 ? 360 - fAngle : fAngle;
                if (fAngle < 90)
                {
                    strSameBts90 = "是";
                }
                fSameAngle = fAngle;
            }
            else
            {
                if (dicLteOverlapData[strCellName].servCell != null && dicLteOverlapData[nCellName].servCell != null)
                    fSameAngle = calcCellAngle(dicLteOverlapData[strCellName].servCell,
                        dicLteOverlapData[nCellName].servCell,
                        dicLteOverlapData[strCellName].antCgfSub.方向角,
                        dicLteOverlapData[nCellName].antCgfSub.方向角
                        );
            }
        }

        private void setOverlapDataArea(ZTSiteDistanceQueryByRegion zTSite, string strCellName, LTECell cell)
        {
            if (cell != null)
            {
                CellInfo cellInfo = new CellInfo(cell);
                CalculateCell calCellMix = zTSite.calculateRadiusMix(cellInfo, cell.BelongBTS.Name, 3, 3000);
                dicLteOverlapData[strCellName].d站间距 = calCellMix.Distance;
                if (calCellMix.Distance < 500)
                {
                    dicLteOverlapData[strCellName].str所属区域 = "密集城区";
                }
                else if (calCellMix.Distance >= 500 && calCellMix.Distance < 1000)
                {
                    dicLteOverlapData[strCellName].str所属区域 = "一般城区";
                }
                else
                {
                    dicLteOverlapData[strCellName].str所属区域 = "郊区";
                }
            }
        }

        private float calcCellAngle(LTECell mainCell, LTECell nCell, float main方位角, float nCell方位角)
        {
            float fAngle = 0;
            double dA_B = 0;
            double dMesalMirror = 0; //中线的镜面所在角度
            double dVersaMirror = 0;//B站点方位角镜面取反角度
            dA_B = ZTAntFuncHelper.calcTwoPointAngle(mainCell.Longitude, mainCell.Latitude, nCell.Longitude, nCell.Latitude);
            if (double.IsNaN(dA_B))
            {
                return -999;
            }
            dMesalMirror = dA_B - 90;
            dMesalMirror = (dMesalMirror + 360) % 360;
            double angleTmp = Math.Abs(dMesalMirror - nCell方位角); //镜面与B站点夹角
            angleTmp = angleTmp > 90 ? 360 - angleTmp : angleTmp;
            if (angleTmp > 90)
            {
                dMesalMirror = dA_B + 90;
                dMesalMirror = (dMesalMirror + 360) % 360;
                angleTmp = Math.Abs(dMesalMirror - nCell方位角);
                angleTmp = angleTmp > 90 ? 360 - angleTmp : angleTmp;
            }
            dVersaMirror = (angleTmp + dMesalMirror + 360) % 360;
            if (dVersaMirror == nCell方位角)
            {
                dVersaMirror = (dMesalMirror - angleTmp + 360) % 360;
            }
            fAngle = (float)Math.Abs(main方位角 - dVersaMirror);
            fAngle = (float)Math.Round(fAngle, 2);
            return fAngle;
        }

        /// <summary>
        /// 关联状态库
        /// <returns>方位角、挂高、下倾角</returns>
        private AntCfgSub getAntCfgSub(int iEci)
        {
            int iNewEci = (iEci / 256) * 256 + ((iEci % 256) % 10);
            AntCfgSub antCfg;
            if (!antCfgParaDic.TryGetValue(iNewEci, out antCfg) && !antCfgParaSDic.TryGetValue(iNewEci, out antCfg))
            {
                antCfg = new AntCfgSub();
            }
            return antCfg;
        }

        private AntennaPara getAntennaParaData(LteSampleMainInfo mainCell)
        {
            AntennaPara antPara;
            if (mainCell.servCell != null)
            {
                if (!antParaEciDic.TryGetValue(mainCell.servCell.ECI, out antPara) && !antParaCellNameDic.TryGetValue(mainCell.CellName, out antPara))
                {
                    antPara = getNewAntennaPara();
                }
            }
            else
            {
                if (!antParaCellNameDic.TryGetValue(mainCell.CellName, out antPara))
                {
                    antPara = getNewAntennaPara();
                }
            }
            return antPara;
        }

        private AntennaPara getNewAntennaPara()
        {
            AntennaPara antPara = new AntennaPara();
            antPara.drangeport1 = -10000;
            antPara.drangeport2 = -10000;
            antPara.drangeport3 = -10000;
            antPara.drangeport4 = -10000;
            antPara.drangeport5 = -10000;
            antPara.drangeport6 = -10000;
            antPara.drangeport7 = -10000;
            antPara.drangeport8 = -10000;
            antPara.dphaseport1 = -10000;
            antPara.dphaseport2 = -10000;
            antPara.dphaseport3 = -10000;
            antPara.dphaseport4 = -10000;
            antPara.dphaseport5 = -10000;
            antPara.dphaseport6 = -10000;
            antPara.dphaseport7 = -10000;
            antPara.dphaseport8 = -10000;
            antPara._3dbValue = -10000;
            antPara._6dbValue = -10000;
            antPara.GMax = -10000;
            return antPara;
        }

        /// <summary>
        /// 查询状态库天线数据
        /// </summary>
        private void doWithAntCfgData()
        {
            WaitBox.CanCancel = true;
            DiyCfgPara cellPara = new DiyCfgPara(MainModel);
            cellPara.Query();
            antCfgParaDic = cellPara.antCfgParaDic;
            antCfgParaSDic = cellPara.antCfgParaSDic;
            WaitBox.Close();
        }
        /// <summary>
        /// 查询获取权值信息
        /// </summary>
        private void doWithParaData()
        {
            WaitBox.CanCancel = true;
            if (Condition != null)
            {
                if (Condition.IsByRound)
                {
                    timeCfg.ISitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0}/{1:00}/01 00:00:00", condition.ByRoundYear, condition.ByRoundRound))) / (1000L));
                    timeCfg.IEitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0}/{1:00}/01 23:59:59", condition.ByRoundYear, condition.ByRoundRound)).AddMonths(1).AddDays(-1)) / (1000L));
                }
                else
                {
                    timeCfg.ISitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0:yyyy-MM-dd} 00:00:00", condition.Periods[0].BeginTime))) / (1000L));
                    timeCfg.IEitme = (int)(JavaDate.GetMilliseconds(DateTime.Parse(string.Format("{0:yyyy-MM-dd} 23:59:59", condition.Periods[0].EndTime.AddDays(-1)))) / (1000L));
                }
            }
            DiyAntennaPara antPara = new DiyAntennaPara(MainModel, timeCfg);
            antPara.SetQueryCondition(condition);
            antPara.Query();
            antParaEciDic = antPara.antParaEciDic;
            antParaCellNameDic = antPara.antParaCellNameDic;
            WaitBox.Close();
        }
    }

    public class LteSampleMainInfo : LteSampleSubInfo
    {
        /// <summary>
        /// 状态库：挂高、下倾角、方位角
        /// </summary>
        public AntCfgSub antCgfSub { get; set; }
        /// <summary>
        /// 小区信息
        /// </summary>
        public LteCellCoverInfo LTECellInfo { get; set; }
        /// <summary>
        /// 主覆盖小区信息
        /// </summary>
        public LteCellCoverInfo LTEMainCellInfo { get; set; }
        /// <summary>
        /// 重叠小区信息
        /// </summary>
        public LteCellCoverInfo LTEOverlapCellInfo { get; set; }
        /// <summary>
        /// 主邻小区信息
        /// </summary>
        public Dictionary<string, LteCellPairCoverInfo> nbCellInfoDic { get; set; }
        /// <summary>
        /// 小区权值信息
        /// </summary>
        public AntennaPara antennaPare { get; set; }
        public LteSampleMainInfo()
        {
            Initialize();
        }

        public LteSampleMainInfo(int idx, int iEarfcn, int iPCI, float fRSRP, float fSINR, LTECell servCell)
            : base(idx, iEarfcn, iPCI, fRSRP, fSINR, servCell)
        {
            this.idx = idx;
            this.iEarfcn = iEarfcn;
            this.iPCI = iPCI;
            this.fRSRP = fRSRP;
            this.fSINR = fSINR;
            this.servCell = servCell;
            Initialize();
        }

        public void Initialize()
        {
            this.antCgfSub = new AntCfgSub();
            this.LTECellInfo = new LteCellCoverInfo();
            this.LTEMainCellInfo = new LteCellCoverInfo();
            this.LTEOverlapCellInfo = new LteCellCoverInfo();
            this.nbCellInfoDic = new Dictionary<string, LteCellPairCoverInfo>();
            this.antennaPare = new AntennaPara();
        }

        /// <summary>
        /// 所有采样点数据
        /// </summary>
        public void fillCellInfo(LteSampleSubInfo info, LongLat longLat, string strGrid, TestPoint testPoint)
        {
            LTECellInfo.isampleCount++;
            LTECellInfo.fRSRPSum += info.fRSRP;
            LTECellInfo.fSINRSum += info.fSINR;
            LTECellInfo.fDistSum += info.fDist;

            this.servCell = info.servCell;
            this.iEarfcn = info.iEarfcn;
            this.iPCI = info.iPCI;

            if (!LTECellInfo.gridList.Contains(strGrid))
                LTECellInfo.gridList.Add(strGrid);

            List<LteTestPointInfo> tpMainList;
            if (!LTECellInfo.testPointDic.TryGetValue(info.iDir, out tpMainList))
                tpMainList = new List<LteTestPointInfo>();

            LteTestPointInfo mainTp = new LteTestPointInfo(longLat, info.fRSRP, info.fSINR, info.fDist);
            tpMainList.Add(mainTp);
            LTECellInfo.testPointDic[info.iDir] = tpMainList;

            LTECellInfo.tpList.Add(testPoint);
            string snk = testPoint.SN.ToString() + testPoint.FileID.ToString() + testPoint.Time.ToString();
            if (!LTECellInfo.tpIndexDic.ContainsKey(snk))
                LTECellInfo.tpIndexDic.Add(snk, info.idx);
        }

        /// <summary>
        /// 主覆盖采样点
        /// </summary>
        public void fillMainCellInfo(LteSampleSubInfo info, LongLat longLat, TestPoint testPoint)
        {
            LTEMainCellInfo.lteMainCellCover.iSampleCount++;
            LTEMainCellInfo.isampleCount++;
            LTEMainCellInfo.fRSRPSum += info.fRSRP;
            LTEMainCellInfo.fSINRSum += info.fSINR;
            LTEMainCellInfo.fDistSum += info.fDist;

            List<LteTestPointInfo> tpMainList;
            if (!LTEMainCellInfo.testPointDic.TryGetValue(info.iDir, out tpMainList))
                tpMainList = new List<LteTestPointInfo>();

            LteTestPointInfo mainTp = new LteTestPointInfo(longLat, info.fRSRP, info.fSINR, info.fDist);
            tpMainList.Add(mainTp);
            LTEMainCellInfo.testPointDic[info.iDir] = tpMainList;

            LTEMainCellInfo.tpList.Add(testPoint);
            string snk = testPoint.SN.ToString() + testPoint.FileID.ToString() + testPoint.Time.ToString();
            if (!LTEMainCellInfo.tpIndexDic.ContainsKey(snk))
                LTEMainCellInfo.tpIndexDic.Add(snk, info.idx);
        }

        /// <summary>
        /// 重叠覆盖采样点
        /// </summary>
        public void fillOverlapCellInfo(LteSampleSubInfo info, LongLat longLat, TestPoint testPoint)
        {
            LTEOverlapCellInfo.isampleCount++;
            LTEOverlapCellInfo.fRSRPSum += info.fRSRP;
            LTEOverlapCellInfo.fSINRSum += info.fSINR;
            LTEOverlapCellInfo.fDistSum += info.fDist;

            List<LteTestPointInfo> tpMainList;
            if (!LTEOverlapCellInfo.testPointDic.TryGetValue(info.iDir, out tpMainList))
                tpMainList = new List<LteTestPointInfo>();

            LteTestPointInfo mainTp = new LteTestPointInfo(longLat, info.fRSRP, info.fSINR, info.fDist);
            tpMainList.Add(mainTp);
            LTEOverlapCellInfo.testPointDic[info.iDir] = tpMainList;

            LTEOverlapCellInfo.tpList.Add(testPoint);
            string snk = testPoint.SN.ToString() + testPoint.FileID.ToString() + testPoint.Time.ToString();
            if (!LTEOverlapCellInfo.tpIndexDic.ContainsKey(snk))
                LTEOverlapCellInfo.tpIndexDic.Add(snk, info.idx);
        }

        /// <summary>
        /// 主邻小区对数据填值
        /// </summary>
        public void fillCellPairInfo(LteSampleSubInfo mainInfo, LteSampleSubInfo nbInfo, LongLat longLat)
        {
            LteCellPairCoverInfo tmpInfo;
            if (!nbCellInfoDic.TryGetValue(nbInfo.CellName, out tmpInfo))
            {
                tmpInfo = new LteCellPairCoverInfo();
                tmpInfo.nbCell = nbInfo.servCell;
                tmpInfo.iNbEarfcn = nbInfo.iEarfcn;
                tmpInfo.iNbPCI = nbInfo.iPCI;
                if (nbInfo.iPCI % 3 == mainInfo.iPCI % 3)
                {
                    tmpInfo.isMod3 = "是";
                }
                if (nbInfo.iPCI % 6 == mainInfo.iPCI % 6)
                {
                    tmpInfo.isMod6 = "是";
                }
                if (mainInfo.servCell != null && nbInfo.servCell != null)
                    tmpInfo.dCellDist = Math.Round(MathFuncs.GetDistance(mainInfo.servCell.Longitude, mainInfo.servCell.Latitude, nbInfo.servCell.Longitude, nbInfo.servCell.Latitude), 2);
            }

            //主小区信息
            tmpInfo.mainCellInfo.isampleCount++;
            tmpInfo.mainCellInfo.fRSRPSum += mainInfo.fRSRP;
            tmpInfo.mainCellInfo.fSINRSum += mainInfo.fSINR;
            tmpInfo.mainCellInfo.fDistSum += mainInfo.fDist;

            List<LteTestPointInfo> tpMainList;
            if (!tmpInfo.mainTpDic.TryGetValue(mainInfo.iDir, out tpMainList))
                tpMainList = new List<LteTestPointInfo>();

            LteTestPointInfo mainTp = new LteTestPointInfo(longLat, mainInfo.fRSRP, mainInfo.fSINR, mainInfo.fDist);
            tpMainList.Add(mainTp);
            tmpInfo.mainTpDic[mainInfo.iDir] = tpMainList;

            //邻小区信息
            tmpInfo.nbCellInfo.isampleCount++;
            tmpInfo.nbCellInfo.fRSRPSum += nbInfo.fRSRP;
            tmpInfo.nbCellInfo.fSINRSum += nbInfo.fSINR;
            tmpInfo.nbCellInfo.fDistSum += nbInfo.fDist;

            List<LteTestPointInfo> tpNbList;
            if (!tmpInfo.nbTpDic.TryGetValue(nbInfo.iDir, out tpNbList))
                tpNbList = new List<LteTestPointInfo>();

            LteTestPointInfo nbTp = new LteTestPointInfo(longLat, nbInfo.fRSRP, nbInfo.fSINR, nbInfo.fDist);
            tpNbList.Add(nbTp);
            tmpInfo.nbTpDic[nbInfo.iDir] = tpNbList;

            nbCellInfoDic[nbInfo.CellName] = tmpInfo;
        }

        /// <summary>
        /// 计算区域面积
        /// </summary>
        public void calcRegionArea()
        {
            if (servCell == null)
                return;

            LTECellInfo.cellAreaSub = calcAreaByLongLat(LTECellInfo.testPointDic);
            LTEMainCellInfo.cellAreaSub = calcAreaByLongLat(LTEMainCellInfo.testPointDic);
            LTEOverlapCellInfo.cellAreaSub = calcAreaByLongLat(LTEOverlapCellInfo.testPointDic);

            LTEMainCellInfo.lteMainCellCover.dArea0_3 = calcAreaByLongLat(LTEMainCellInfo.lteMainCellCover.testPointDic0_3);
            LTEMainCellInfo.lteMainCellCover.dArea3_6 = calcAreaByLongLat(LTEMainCellInfo.lteMainCellCover.testPointDic3_6);
            LTEMainCellInfo.lteMainCellCover.dArea6_10 = calcAreaByLongLat(LTEMainCellInfo.lteMainCellCover.testPointDic6_10);
            LTEMainCellInfo.lteMainCellCover.dArea10_15 = calcAreaByLongLat(LTEMainCellInfo.lteMainCellCover.testPointDic10_15);
            LTEMainCellInfo.lteMainCellCover.dArea15 = calcAreaByLongLat(LTEMainCellInfo.lteMainCellCover.testPointDic15);

            foreach (string strNbCell in nbCellInfoDic.Keys)
            {
                LteCellPairCoverInfo cellPair = nbCellInfoDic[strNbCell];
                cellPair.mainCellInfo.cellAreaSub = calcNbCellAreaByLongLat(cellPair.mainTpDic);
                cellPair.nbCellInfo.cellAreaSub = calcNbCellAreaByLongLat(cellPair.nbTpDic);
            }
        }

        /// <summary>
        /// 采样点覆盖区域面积
        /// </summary>
        private double calcAreaByLongLat(Dictionary<int, List<LteTestPointInfo>> tpDic)
        {
            double dArea = 0;
            foreach (int iDir in tpDic.Keys)
            {
                List<LteTestPointInfo> tmpList = tpDic[iDir];
                tmpList.Sort(LteSampleByDist.GetCompareByDist());
                if (tmpList.Count > 0)
                {
                    dArea += (Math.PI * tmpList[0].fDistance * tmpList[0].fDistance) / 360;
                }
            }
            return Math.Round(dArea, 2);
        }
        /// <summary>
        /// 采样点重叠覆盖面积
        /// </summary>
        private double calcNbCellAreaByLongLat(Dictionary<int, List<LteTestPointInfo>> nbCellDic)
        {
            double dArea = 0;
            double dAreaMax = 0;
            double dAreaMin = 0;
            foreach (int iDir in nbCellDic.Keys)
            {
                List<LteTestPointInfo> tmpList = nbCellDic[iDir];
                tmpList.Sort(LteSampleByDist.GetCompareByDist());
                if (tmpList.Count == 1)
                {
                    dArea += (Math.PI * tmpList[0].fDistance * tmpList[0].fDistance) / 360;
                }
                else if (tmpList.Count > 1)
                {
                    dAreaMax = (Math.PI * tmpList[0].fDistance * tmpList[0].fDistance) / 360;
                    dAreaMin = (Math.PI * tmpList[tmpList.Count - 1].fDistance * tmpList[tmpList.Count - 1].fDistance)
                        / 360;
                    dArea += dAreaMax - dAreaMin;
                }
            }
            return Math.Round(dArea, 2);
        }

        public string getDisturbType(LTECell lteNCell, Dictionary<int, List<LteTestPointInfo>> testPointDic)
        {
            Dictionary<string, int> dicTmp = new Dictionary<string, int>();
            dicTmp.Add("主瓣", 0);
            dicTmp.Add("旁瓣", 0);
            dicTmp.Add("背瓣", 0);
            dicTmp.Add("同站旁瓣", 0);
            foreach (int iKey in testPointDic.Keys)
            {
                foreach (LteTestPointInfo tp in testPointDic[iKey])
                {
                    int iTmp = calcSampleAngle(lteNCell, tp.longLat.fLongitude, tp.longLat.fLatitude, 1);
                    switch (iTmp)
                    {
                        case 1:
                            dicTmp["主瓣"]++;
                            break;
                        case 2:
                            dicTmp["旁瓣"]++;
                            break;
                        case 3:
                            dicTmp["背瓣"]++;
                            break;
                        case 4:
                            dicTmp["同站旁瓣"]++;
                            break;
                        default:
                            break;
                    }
                }
            }
            int iMaxNum = 0;
            string strTmp = "";
            foreach (string strKey in dicTmp.Keys)
            {
                int iValue = dicTmp[strKey];
                if (iValue > iMaxNum)
                {
                    iMaxNum = iValue;
                    strTmp = strKey;
                }
            }
            return strTmp;
        }
        /// <summary>
        /// 采样点相对小区夹角
        /// </summary>
        /// <param name="itype">0为相对夹角，1为相对夹角(绝对值)</param>
        ///  <param name="iReturn">0返回判定值，1返回夹角值</param>
        public int calcSampleAngle(LTECell lteNCell, double longitude, double latitude, int itype)
        {
            //所有角度按正北方向算起始，顺时针算夹角，正北为0度
            double angleDiff = 0;
            double distance = lteNCell.GetDistance(longitude, latitude);

            double angle = getAngle(lteNCell, longitude, latitude, distance);

            angleDiff = getAngleDiff(lteNCell, itype, angle);
            if (0 <= angleDiff && angleDiff < 60)
            {
                return 1;
            }
            else if (60 <= angleDiff && angleDiff < 150)
            {
                return 2;
            }
            else if (150 <= angleDiff && angleDiff < 180)
            {
                return 3;
            }
            else if (lteNCell.Longitude == longitude && lteNCell.Latitude == latitude)
            {
                return 4;
            }
            return 0;
        }

        private static double getAngle(LTECell lteNCell, double longitude, double latitude, double distance)
        {
            double angle;
            double ygap = lteNCell.GetDistance(lteNCell.Longitude, latitude);
            double angleV = Math.Acos(ygap / distance);
            if (longitude >= lteNCell.Longitude && latitude >= lteNCell.Latitude)//1象限
            {
                angle = angleV * 180 / Math.PI;
            }
            else if (longitude <= lteNCell.Longitude && latitude >= lteNCell.Latitude)//2象限
            {
                angle = 360 - angleV * 180 / Math.PI;
            }
            else if (longitude <= lteNCell.Longitude && latitude <= lteNCell.Latitude)//3象限
            {
                angle = 180 + angleV * 180 / Math.PI;
            }
            else                                                                    //4象限
            {
                angle = 180 - angleV * 180 / Math.PI;
            }

            return angle;
        }

        private static double getAngleDiff(LTECell lteNCell, int itype, double angle)
        {
            double angleDiff;
            if (itype == 0)
            {
                angleDiff = angle - lteNCell.Direction;
                if (angleDiff < 0)
                {
                    angleDiff = 360 + angleDiff;
                }
            }
            else
            {
                angleDiff = Math.Abs(angle - lteNCell.Direction);
                if (angleDiff > 180)
                {
                    angleDiff = 360 - angleDiff;
                }
            }

            return angleDiff;
        }
    }

    public class LteTestPointInfo
    {
        public LongLat longLat { get; set; }
        public float fRsrp { get; set; }
        public float fSinr { get; set; }
        public float fDistance { get; set; }

        public LteTestPointInfo()
        {
            longLat = new LongLat();
            fRsrp = 0;
            fSinr = 0;
            fDistance = 0;
        }

        public LteTestPointInfo(LongLat longLat, float fRsrp, float fSinr, float fDistance)
        {
            this.longLat = longLat;
            this.fRsrp = fRsrp;
            this.fSinr = fSinr;
            this.fDistance = fDistance;
        }
    }

    public class LteCellCoverInfo
    {
        /// <summary>
        /// 采样点数
        /// </summary>
        public int isampleCount { get; set; }
        /// <summary>
        /// RSRP累加值
        /// </summary>
        public float fRSRPSum { get; set; }
        /// <summary>
        /// 平均rsrp
        /// </summary>
        public double dRSRPAvg
        {
            get
            {
                if (isampleCount == 0)
                    return 0;
                else
                    return Math.Round(fRSRPSum / isampleCount, 2);
            }
        }
        /// <summary>
        /// SINR累加值
        /// </summary>
        public float fSINRSum { get; set; }
        /// <summary>
        /// 平均sinr
        /// </summary>
        public double dSINRAvg
        {
            get
            {
                if (isampleCount == 0)
                    return 0;
                else
                    return Math.Round(fSINRSum / isampleCount, 2);
            }
        }
        /// <summary>
        /// 距离累加值
        /// </summary>
        public float fDistSum { get; set; }
        /// <summary>
        /// 平均覆盖距离
        /// </summary>
        public double cellDistanceAvg
        {
            get
            {
                if (isampleCount == 0)
                    return 0;
                else
                    return Math.Round(fDistSum / isampleCount, 2);
            }
        }

        public List<string> gridList { get; set; }
        /// <summary>
        /// 占用网格名称
        /// </summary>
        public string strGrid
        {
            get
            {
                StringBuilder strTmpGrid = new StringBuilder();
                foreach (string sg in gridList)
                {
                    strTmpGrid.Append(sg);
                    strTmpGrid.Append(",");
                }
                return strTmpGrid.ToString().TrimEnd(',');
            }
        }

        /// <summary>
        /// 采样点信息
        /// </summary>
        public Dictionary<int, List<LteTestPointInfo>> testPointDic { get; set; }
        /// <summary>
        /// 存放本小区在每个采样点中序号，以便回放呈现
        /// </summary>
        public Dictionary<string, int> tpIndexDic { get; set; }
        /// <summary>
        /// 采样点，用于回放
        /// </summary>
        public List<TestPoint> tpList { get; set; }
        /// <summary>
        /// 平均覆盖面积
        /// </summary>
        public double cellAreaSub { get; set; }
        /// <summary>
        /// 主小区 覆盖分段统计信息								
        /// </summary>
        public LteMainCellCover lteMainCellCover { get; set; }

        public LteCellCoverInfo()
        {
            this.isampleCount = 0;
            this.fRSRPSum = -1;
            this.fSINRSum = -1;
            this.fDistSum = 0;
            this.cellAreaSub = 0;

            gridList = new List<string>();
            testPointDic = new Dictionary<int, List<LteTestPointInfo>>();
            tpIndexDic = new Dictionary<string, int>();
            tpList = new List<TestPoint>();
            lteMainCellCover = new LteMainCellCover();
        }
        /// <summary>
        /// RSRP原值
        /// </summary>
        public int[] rsrpArray
        {
            get
            {
                int[] tmp = new int[360];
                for (int i = 0; i < 360; i++)
                {
                    if (testPointDic.ContainsKey(i))
                    {
                        List<LteTestPointInfo> tmpList = testPointDic[i];
                        tmpList.Sort(LteSampleByDist.GetCompareByDist());
                        tmp[i] = (int)tmpList[0].fRsrp;
                    }
                    else
                        tmp[i] = -140;
                }
                return tmp;
            }
        }
        /// <summary>
        /// 通信距离
        /// </summary>
        public double[] sampArray
        {
            get
            {
                double[] tmp = new double[360];
                for (int i = 0; i < 360; i++)
                {
                    if (testPointDic.ContainsKey(i))
                    {
                        List<LteTestPointInfo> tmpList = testPointDic[i];
                        tmpList.Sort(LteSampleByDist.GetCompareByDist());
                        tmp[i] = (int)tmpList[0].fDistance;
                    }
                    else
                        tmp[i] = 0;
                }
                return tmp;
            }
        }
        /// <summary>
        /// 采样点总数
        /// </summary>
        public int[] sampNumArray
        {
            get
            {
                int[] tmp = new int[360];
                for (int i = 0; i < 360; i++)
                {
                    if (testPointDic.ContainsKey(i))
                    {
                        tmp[i] = testPointDic[i].Count;
                    }
                    else
                        tmp[i] = 0;
                }
                return tmp;
            }
        }
    }

    public class LteCellPairCoverInfo
    {
        public int idx { get; set; }
        public LTECell nbCell { get; set; }
        public int iNbEarfcn { get; set; }
        public int iNbPCI { get; set; }
        public string isMod3 { get; set; }
        public string isMod6 { get; set; }
        public double dCellDist { get; set; }
        public AntCfgSub antCgfSub { get; set; }
        public AntennaPara antennaPare { get; set; }
        public string strSameBts { get; set; }
        public string strSameBts90 { get; set; }
        public float fSameAngle { get; set; }
        public string strAntennaType { get; set; }
        public LteCellCoverInfo mainCellInfo { get; set; }
        public LteCellCoverInfo nbCellInfo { get; set; }
        public Dictionary<int, List<LteTestPointInfo>> mainTpDic { get; set; }
        public Dictionary<int, List<LteTestPointInfo>> nbTpDic { get; set; }

        public LteCellPairCoverInfo()
        {
            idx = 0;
            iNbEarfcn = 0;
            iNbPCI = 0;
            isMod3 = "否";
            isMod6 = "否";
            dCellDist = 0;
            antCgfSub = new AntCfgSub();
            antennaPare = new AntennaPara();
            strSameBts = "否";
            strSameBts90 = "否";
            fSameAngle = 0;
            strAntennaType = "";
            mainCellInfo = new LteCellCoverInfo();
            nbCellInfo = new LteCellCoverInfo();
            mainTpDic = new Dictionary<int, List<LteTestPointInfo>>();
            nbTpDic = new Dictionary<int, List<LteTestPointInfo>>();
        }

        public int iSampleNum
        {
            get
            {
                return nbCellInfo.isampleCount;
            }
        }

        public string CellName
        {
            get
            {
                if (nbCell == null)
                    return iNbEarfcn.ToString() + "-" + iNbPCI.ToString();
                else
                    return nbCell.Name;
            }
        }
    }

    public class LteMainCellCover
    {
        public int iSampleCount { get; set; }
        public int iRSRPCount0_3 { get; set; }
        public int iRSRPCount3_6 { get; set; }
        public int iRSRPCount6_10 { get; set; }
        public int iRSRPCount10_15 { get; set; }
        public int iRSRPCount15 { get; set; }
        public double dArea0_3 { get; set; }
        public double dArea3_6 { get; set; }
        public double dArea6_10 { get; set; }
        public double dArea10_15 { get; set; }
        public double dArea15 { get; set; }
        public Dictionary<int, List<LteTestPointInfo>> testPointDic0_3 { get; set; }
        public Dictionary<int, List<LteTestPointInfo>> testPointDic3_6 { get; set; }
        public Dictionary<int, List<LteTestPointInfo>> testPointDic6_10 { get; set; }
        public Dictionary<int, List<LteTestPointInfo>> testPointDic10_15 { get; set; }
        public Dictionary<int, List<LteTestPointInfo>> testPointDic15 { get; set; }

        public LteMainCellCover()
        {
            iSampleCount = 0;
            iRSRPCount0_3 = 0;
            iRSRPCount3_6 = 0;
            iRSRPCount6_10 = 0;
            iRSRPCount10_15 = 0;
            iRSRPCount15 = 0;
            dArea0_3 = 0;
            dArea3_6 = 0;
            dArea6_10 = 0;
            dArea10_15 = 0;
            dArea15 = 0;
            testPointDic0_3 = new Dictionary<int, List<LteTestPointInfo>>();
            testPointDic3_6 = new Dictionary<int, List<LteTestPointInfo>>();
            testPointDic6_10 = new Dictionary<int, List<LteTestPointInfo>>();
            testPointDic10_15 = new Dictionary<int, List<LteTestPointInfo>>();
            testPointDic15 = new Dictionary<int, List<LteTestPointInfo>>();
        }

        public void calcRSRPSampleCount(float diffRSRP, LteSampleSubInfo info, LongLat longLat)
        {
            if (0 < diffRSRP && diffRSRP <= 3)
            {
                this.iRSRPCount0_3++;
                addTestPointDic(info, longLat, testPointDic0_3);
            }
            else if (3 < diffRSRP && diffRSRP <= 6)
            {
                this.iRSRPCount3_6++;
                addTestPointDic(info, longLat, testPointDic3_6);
            }
            else if (6 < diffRSRP && diffRSRP <= 10)
            {
                this.iRSRPCount6_10++;
                addTestPointDic(info, longLat, testPointDic6_10);
            }
            else if (10 < diffRSRP && diffRSRP <= 15)
            {
                this.iRSRPCount10_15++;
                addTestPointDic(info, longLat, testPointDic10_15);
            }
            else if (15 < diffRSRP)
            {
                this.iRSRPCount15++;
                addTestPointDic(info, longLat, testPointDic15);
            }
        }

        private void addTestPointDic(LteSampleSubInfo info, LongLat longLat, Dictionary<int, List<LteTestPointInfo>> testPointDic)
        {
            List<LteTestPointInfo> tpMainList;
            if (!testPointDic.TryGetValue(info.iDir, out tpMainList))
                tpMainList = new List<LteTestPointInfo>();

            LteTestPointInfo mainTp = new LteTestPointInfo(longLat, info.fRSRP, info.fSINR, info.fDist);
            tpMainList.Add(mainTp);
            testPointDic[info.iDir] = tpMainList;
        }

        public double getSample0_3
        {
            get
            {
                if (iSampleCount == 0)
                    return 0;
                else
                    return Math.Round(iRSRPCount0_3 * 1.0 / iSampleCount * 100.0, 2);
            }
        }
        public double getSample3_6
        {
            get
            {
                if (iSampleCount == 0)
                    return 0;
                else
                    return Math.Round(iRSRPCount3_6 * 1.0 / iSampleCount * 100.0, 2);
            }
        }
        public double getSample6_10
        {
            get
            {
                if (iSampleCount == 0)
                    return 0;
                else
                    return Math.Round(iRSRPCount6_10 * 1.0 / iSampleCount * 100.0, 2);
            }
        }
        public double getSample10_15
        {
            get
            {
                if (iSampleCount == 0)
                    return 0;
                else
                    return Math.Round(iRSRPCount10_15 * 1.0 / iSampleCount * 100.0, 2);
            }
        }
        public double getSample15
        {
            get
            {
                if (iSampleCount == 0)
                    return 0;
                else
                    return Math.Round(iRSRPCount15 * 1.0 / iSampleCount * 100.0, 2);
            }
        }
        public int _testSumSample
        {
            get
            {
                return this.iRSRPCount0_3 + this.iRSRPCount3_6 + this.iRSRPCount6_10 + this.iRSRPCount10_15 + this.iRSRPCount15;
            }
        }
    }

    /// <summary>
    /// 实现对采样点时间的排序类
    /// </summary>
    public class LteSampleByDist
    {
        //实现排序的接口
        public static IComparer<LteTestPointInfo> GetCompareByDist()
        {
            if (comparerByDist == null)
            {
                comparerByDist = new CompareBySampleDist();
            }
            return comparerByDist;
        }
        public class CompareBySampleDist : IComparer<LteTestPointInfo>
        {
            public int Compare(LteTestPointInfo x, LteTestPointInfo y)
            {
                return y.fDistance.CompareTo(x.fDistance);
            }
        }
        private static IComparer<LteTestPointInfo> comparerByDist;
    }

    /// <summary>
    /// 实现对采样点时间的排序类
    /// </summary>
    public class LteSampleByNum
    {
        //实现排序的接口
        public static IComparer<LteCellPairCoverInfo> GetCompareByNum()
        {
            if (comparerByNum == null)
            {
                comparerByNum = new CompareBySampleNum();
            }
            return comparerByNum;
        }
        public class CompareBySampleNum : IComparer<LteCellPairCoverInfo>
        {
            public int Compare(LteCellPairCoverInfo x, LteCellPairCoverInfo y)
            {
                return y.iSampleNum.CompareTo(x.iSampleNum);
            }
        }
        private static IComparer<LteCellPairCoverInfo> comparerByNum;
    }
}
