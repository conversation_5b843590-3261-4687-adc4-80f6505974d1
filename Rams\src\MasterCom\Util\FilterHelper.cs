﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.Util
{
    public static class FilterHelper
    {
        public const string All = "所有文件 (*.*)|*.*";
        public const string Excel = "Excel 97-2003 工作簿(*.xls)|*.xls|Excel 工作簿(*.xlsx)|*.xlsx";
        public const string ExcelOld = "Excel 97-2003 工作簿(*.xls)|*.xls";
        public const string ExcelX = "Excel 工作簿(*.xlsx)|*.xlsx";
        public const string Xls = "Excel 97-2003 工作簿(*.xls)|*.xls";
        public const string Xlsx = "Excel 工作簿(*.xlsx)|*.xlsx";
        public const string Word = "Word 97-2003 文档(*.doc)|*.doc|Word 文档(*.docx)|*.docx";
        public const string Doc = "Word 97-2003 文档(*.doc)|*.doc";
        public const string Docx = "Word 文档(*.docx)|*.docx";
        public const string Shp = "ESRI Shapefiles (*.shp)|*.shp";
        public const string Txt = "文本文档(*.txt)|*.txt";
        public const string Csv = "CSV文件(*.csv)|*.csv";
        public const string Jpg = "JPEG (*.jpg;*.jpeg)|*jpg;*.jpeg";
        public const string Png = "PNG (*.png)|*.png";
        public const string Kml = "KML (*.kml)|*.kml";
        public const string Zip = "Zip压缩包 (*.zip)|*.zip|Rar压缩包 (*.rar)|*.rar";
        public const string CAP = "CAP File (*.cap)|*.cap";
    }
}
