<?xml version="1.0"?>
<Configs>
  <Config name="StatReports">
    <Item name="reports" typeName="IList">
      <Item typeName="ReporterTemplate">
        <Item name="Param" typeName="IDictionary">
          <Item typeName="String" key="Name">00_山西KPI派单_GSM</Item>
          <Item typeName="Int32" key="KeyCount">12</Item>
          <Item typeName="Int32" key="TimeShowType">1</Item>
          <Item typeName="IList" key="Columns">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">日期</Item>
              <Item typeName="String" key="Exp">{kDistrictId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">地市</Item>
              <Item typeName="String" key="Exp">{kDistrictId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">项目</Item>
              <Item typeName="String" key="Exp">{kProjId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">AreaComment</Item>
              <Item typeName="String" key="Exp">{kAreaComment}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">AreaTypeID</Item>
              <Item typeName="String" key="Exp">{kAreaTypeIdInt}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">AreaID</Item>
              <Item typeName="String" key="Exp">{kAreaIdInt}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">LogAreaTypeID</Item>
              <Item typeName="String" key="Exp">{kLogAreaTypeId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">LogAreaID</Item>
              <Item typeName="String" key="Exp">{kLogAreaId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">文件区域类型</Item>
              <Item typeName="String" key="Exp">{kLogAreaTypeName}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">文件区域名</Item>
              <Item typeName="String" key="Exp">{kLogAreaName}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">统计表区域类型</Item>
              <Item typeName="String" key="Exp">{kAreaTypeId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">统计表区域名</Item>
              <Item typeName="String" key="Exp">{kAreaId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">试呼次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[0]+value9[0]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">接通次数</Item>
              <Item typeName="String" key="Exp">{(evtIdCount[0]+value9[0]) - (evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+value9[7]+value9[9]+value9[81])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">掉话次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[5]+evtIdCount[6]+value9[5]+value9[6]+evtIdCount[906]+evtIdCount[907]+value9[906]+value9[907]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">全程呼叫成功率(%)</Item>
              <Item typeName="String" key="Exp">{100*(1-((evtIdCount[5]+value9[5]+evtIdCount[6]+value9[6]+evtIdCount[906]+value9[906]+evtIdCount[907]+value9[907])/((evtIdCount[0]+evtIdCount[1]+value9[0]+value9[1])-(evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+evtIdCount[8]+evtIdCount[10]+evtIdCount[82]+value9[7]+value9[9]+value9[81]+value9[8]+value9[10]+value9[82]))))*(((evtIdCount[0]+value9[0]) - (evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+value9[7]+value9[9]+value9[81]))/(evtIdCount[0]+value9[0]))}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">接通率(%)</Item>
              <Item typeName="String" key="Exp">{100.0*((evtIdCount[0]+value9[0]) - (evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+value9[7]+value9[9]+value9[81]))/(evtIdCount[0]+value9[0])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">掉话率(%)</Item>
              <Item typeName="String" key="Exp">{100*(evtIdCount[5]+value9[5]+evtIdCount[6]+value9[6]+evtIdCount[906]+value9[906]+evtIdCount[907]+value9[907])/((evtIdCount[0]+evtIdCount[1]+value9[0]+value9[1])-(evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+evtIdCount[8]+evtIdCount[10]+evtIdCount[82]+value9[7]+value9[9]+value9[81]+value9[8]+value9[10]+value9[82]))}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">语音质量2013(%)</Item>
              <Item typeName="String" key="Exp">{100*(Mx_5A010501+Mx_5A010502+Mx_5A010503+Mx_5A010504+Mx_5A010505)/Mx_5A01050C}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title"> MOS&gt;=2.8占比(%)</Item>
              <Item typeName="String" key="Exp">{100.0*(Mx_5A010B58+Mx_5A010B59+Mx_5A010B5A+Mx_5A010B5B) /Mx_5A010B5C }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title"> MOS均值</Item>
              <Item typeName="String" key="Exp">{Mx_5A010B53}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">覆盖率(%)(&gt;=-90dBm)</Item>
              <Item typeName="String" key="Exp">{100*(Mx_5A010208+Mx_5A010209+Mx_5A01020A+Mx_5A010217)/Mx_5A010201 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">覆盖率(%)(&gt;=-94dBm)</Item>
              <Item typeName="String" key="Exp">{100*(Mx_5A010207+Mx_5A010208+Mx_5A010209+Mx_5A01020A+Mx_5A010217)/Mx_5A010201 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">呼叫建立平均时延(秒)</Item>
              <Item typeName="String" key="Exp">{value1[13]/(1000.0*evtIdCount[13]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均车速(千米/小时)</Item>
              <Item typeName="String" key="Exp">{(Mx_0861/1000)/(Mx_0860/3600000) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">4</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">FileIDs</Item>
              <Item typeName="String" key="Exp">{FileIDs}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>