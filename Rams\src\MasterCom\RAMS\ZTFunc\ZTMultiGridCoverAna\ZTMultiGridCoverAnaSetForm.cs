﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTMultiGridCoverAnaSetForm : BaseDialog
    {
        public ZTMultiGridCoverAnaSetForm()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
         
        public  MultGridCoverAnaThresholdSet MultGridThresholdSet
        {
            get
            {
                MultGridCoverAnaThresholdSet multGridThresholdSet = new MultGridCoverAnaThresholdSet();
                multGridThresholdSet.IGSMStatThreshold = int.Parse(numGSM.Value.ToString());
                multGridThresholdSet.ITDStatThreshold = int.Parse(numTD.Value.ToString());
                multGridThresholdSet.ITDEcIoThreshold = int.Parse(numTDCI.Value.ToString());
                multGridThresholdSet.IWCDMAStatThreshold = int.Parse(numWCDMA.Value.ToString());
                multGridThresholdSet.IWCDMAEcIoThreshold = int.Parse(numWCDMACI.Value.ToString());
                multGridThresholdSet.ICDMAStatThreshold = int.Parse(numCDMA.Value.ToString());
                multGridThresholdSet.ICDMAPowerThreshold = int.Parse(numPower.Value.ToString());
                multGridThresholdSet.ICDMAEcIoThreshold = int.Parse(numECIO.Value.ToString());
                multGridThresholdSet.IEVDOStatThreshold = int.Parse(numEVDO.Value.ToString());
                multGridThresholdSet.IEVDOPowerThreshold = int.Parse(numEVDOPower.Value.ToString());
                multGridThresholdSet.IEVDOSinrThreshold = int.Parse(numEVDOSinr.Value.ToString());
                multGridThresholdSet.IsShowGridSample = chGridSample.Checked;
                return multGridThresholdSet;
            }
        }
    }
}
