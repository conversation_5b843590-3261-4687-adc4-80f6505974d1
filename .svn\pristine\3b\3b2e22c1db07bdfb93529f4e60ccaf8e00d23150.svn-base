using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Stat
{
    public partial class StatReportColumnSetDlg : Form
    {
        ColorDialog dialog = new ColorDialog();
        int col = 0;
        public ColumnInfo ColInfo
        {
            get 
            { 
                ColumnInfo colInfo = new ColumnInfo();
                colInfo.name = txtName.Text.Trim();
                colInfo.bkColor = lbBgColor.BackColor;
                colInfo.foreColor = lbForeColor.BackColor;
                colInfo.width = (int)numWidth.Value;
                colInfo.col = col;
                return colInfo; 
            }
            set
            {
                if (value != null)
                {
                    ColumnInfo columnInfo = value;
                    txtName.Text = columnInfo.name;
                    numWidth.Value = columnInfo.width;
                    lbBgColor.BackColor = columnInfo.bkColor;
                    lbForeColor.BackColor = columnInfo.foreColor;
                    col = columnInfo.col;
                }
            }
        }

        public StatReportColumnSetDlg()
        {
            InitializeComponent();
        }

        private void lbBgColor_Click(object sender, EventArgs e)
        {
            dialog.Color = lbBgColor.BackColor;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                lbBgColor.BackColor = dialog.Color;
            }
        }

        private void lbForeColor_Click(object sender, EventArgs e)
        {
            dialog.Color = lbForeColor.BackColor;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                lbForeColor.BackColor = dialog.Color;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }


    }
}