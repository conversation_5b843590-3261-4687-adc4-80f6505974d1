﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.Util
{
    public static class Sort
    {
        public delegate TResult Func<in T1, in T2, out TResult>(T1 arg1, T2 arg2);

        public static Dictionary<TKey, TValue> SortDic<TKey, TValue>(Dictionary<TKey, TValue> dic
            , Func<KeyValuePair<TKey, TValue>, KeyValuePair<TKey, TValue>, int> func)
        {
            List<KeyValuePair<TKey, TValue>> list = new List<KeyValuePair<TKey, TValue>>(dic);
            list.Sort((a, b) => func(a, b));
            dic.Clear();
            foreach (KeyValuePair<TKey, TValue> pair in list)
            {
                dic.Add(pair.Key, pair.Value);
            }
            return dic;
        }
    }
}
