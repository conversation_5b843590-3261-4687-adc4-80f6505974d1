﻿namespace MasterCom.RAMS.ZTFunc.ZTNRMCSStatistic
{
    partial class MCSStatisticForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.bandedGridView = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn12 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn14 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn15 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn16 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn17 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.ctxMenu;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.bandedGridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(1147, 453);
            this.gridControl.TabIndex = 0;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView});
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay,
            this.toolStripSeparator1,
            this.miExport2Xls});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(139, 54);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(138, 22);
            this.miReplay.Text = "回放文件...";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(135, 6);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(138, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // bandedGridView
            // 
            this.bandedGridView.BandPanelRowHeight = 4;
            this.bandedGridView.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand1,
            this.gridBand2});
            this.bandedGridView.ColumnPanelRowHeight = 4;
            this.bandedGridView.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.bandedGridColumn3,
            this.bandedGridColumn5,
            this.bandedGridColumn7,
            this.bandedGridColumn9,
            this.bandedGridColumn4,
            this.bandedGridColumn6,
            this.bandedGridColumn8,
            this.bandedGridColumn10,
            this.bandedGridColumn11,
            this.bandedGridColumn13,
            this.bandedGridColumn15,
            this.bandedGridColumn17,
            this.bandedGridColumn12,
            this.bandedGridColumn14,
            this.bandedGridColumn16,
            this.bandedGridColumn18});
            this.bandedGridView.GridControl = this.gridControl;
            this.bandedGridView.Name = "bandedGridView";
            this.bandedGridView.OptionsBehavior.Editable = false;
            this.bandedGridView.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView.OptionsView.ShowGroupPanel = false;
            // 
            // gridBand1
            // 
            this.gridBand1.Caption = "Call";
            this.gridBand1.Columns.Add(this.bandedGridColumn1);
            this.gridBand1.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.Width = 75;
            // 
            // bandedGridColumn1
            // 
            this.bandedGridColumn1.Caption = "序号";
            this.bandedGridColumn1.FieldName = "Sn";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.RowCount = 4;
            this.bandedGridColumn1.Visible = true;
            // 
            // gridBand2
            // 
            this.gridBand2.Caption = "调度编码方式分析";
            this.gridBand2.Columns.Add(this.bandedGridColumn2);
            this.gridBand2.Columns.Add(this.bandedGridColumn3);
            this.gridBand2.Columns.Add(this.bandedGridColumn5);
            this.gridBand2.Columns.Add(this.bandedGridColumn7);
            this.gridBand2.Columns.Add(this.bandedGridColumn9);
            this.gridBand2.Columns.Add(this.bandedGridColumn4);
            this.gridBand2.Columns.Add(this.bandedGridColumn6);
            this.gridBand2.Columns.Add(this.bandedGridColumn8);
            this.gridBand2.Columns.Add(this.bandedGridColumn10);
            this.gridBand2.Columns.Add(this.bandedGridColumn11);
            this.gridBand2.Columns.Add(this.bandedGridColumn13);
            this.gridBand2.Columns.Add(this.bandedGridColumn15);
            this.gridBand2.Columns.Add(this.bandedGridColumn17);
            this.gridBand2.Columns.Add(this.bandedGridColumn12);
            this.gridBand2.Columns.Add(this.bandedGridColumn14);
            this.gridBand2.Columns.Add(this.bandedGridColumn16);
            this.gridBand2.Columns.Add(this.bandedGridColumn18);
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 900;
            // 
            // bandedGridColumn2
            // 
            this.bandedGridColumn2.Caption = "log名称";
            this.bandedGridColumn2.FieldName = "FileName";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.RowCount = 4;
            this.bandedGridColumn2.Visible = true;
            // 
            // bandedGridColumn3
            // 
            this.bandedGridColumn3.Caption = "QPSK上行占比(%)";
            this.bandedGridColumn3.DisplayFormat.FormatString = "0.00%";
            this.bandedGridColumn3.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn3.FieldName = "_qpskUpProportion";
            this.bandedGridColumn3.GroupFormat.FormatString = "0.00%";
            this.bandedGridColumn3.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.RowCount = 4;
            this.bandedGridColumn3.Visible = true;
            // 
            // bandedGridColumn4
            // 
            this.bandedGridColumn4.Caption = "QPSK下行占比(%)";
            this.bandedGridColumn4.DisplayFormat.FormatString = "0.00%";
            this.bandedGridColumn4.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn4.FieldName = "_qpskDownProportion";
            this.bandedGridColumn4.GroupFormat.FormatString = "0.00%";
            this.bandedGridColumn4.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            this.bandedGridColumn4.RowCount = 4;
            this.bandedGridColumn4.Visible = true;
            // 
            // bandedGridColumn5
            // 
            this.bandedGridColumn5.Caption = "16QAM上行占比(%)";
            this.bandedGridColumn5.DisplayFormat.FormatString = "0.00%";
            this.bandedGridColumn5.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn5.FieldName = "_16qamUpProportion";
            this.bandedGridColumn5.GroupFormat.FormatString = "0.00%";
            this.bandedGridColumn5.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn5.Name = "bandedGridColumn5";
            this.bandedGridColumn5.RowCount = 4;
            this.bandedGridColumn5.Visible = true;
            // 
            // bandedGridColumn6
            // 
            this.bandedGridColumn6.Caption = "16QAM下行占比(%)";
            this.bandedGridColumn6.DisplayFormat.FormatString = "0.00%";
            this.bandedGridColumn6.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn6.FieldName = "_16qamDownProportion";
            this.bandedGridColumn6.GroupFormat.FormatString = "0.00%";
            this.bandedGridColumn6.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn6.Name = "bandedGridColumn6";
            this.bandedGridColumn6.RowCount = 4;
            this.bandedGridColumn6.Visible = true;
            // 
            // bandedGridColumn7
            // 
            this.bandedGridColumn7.Caption = "64QAM上行占比(%)";
            this.bandedGridColumn7.DisplayFormat.FormatString = "0.00%";
            this.bandedGridColumn7.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn7.FieldName = "_64qamUpProportion";
            this.bandedGridColumn7.GroupFormat.FormatString = "0.00%";
            this.bandedGridColumn7.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn7.Name = "bandedGridColumn7";
            this.bandedGridColumn7.RowCount = 4;
            this.bandedGridColumn7.Visible = true;
            // 
            // bandedGridColumn8
            // 
            this.bandedGridColumn8.Caption = "64QAM下行占比(%)";
            this.bandedGridColumn8.DisplayFormat.FormatString = "0.00%";
            this.bandedGridColumn8.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn8.FieldName = "_64qamDownProportion";
            this.bandedGridColumn8.GroupFormat.FormatString = "0.00%";
            this.bandedGridColumn8.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn8.Name = "bandedGridColumn8";
            this.bandedGridColumn8.RowCount = 4;
            this.bandedGridColumn8.Visible = true;
            // 
            // bandedGridColumn9
            // 
            this.bandedGridColumn9.Caption = "256QAM上行占比(%)";
            this.bandedGridColumn9.DisplayFormat.FormatString = "0.00%";
            this.bandedGridColumn9.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn9.FieldName = "_256qamUpProportion";
            this.bandedGridColumn9.GroupFormat.FormatString = "0.00%";
            this.bandedGridColumn9.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn9.Name = "bandedGridColumn9";
            this.bandedGridColumn9.RowCount = 4;
            this.bandedGridColumn9.Visible = true;
            // 
            // bandedGridColumn10
            // 
            this.bandedGridColumn10.Caption = "256QAM下行占比(%)";
            this.bandedGridColumn10.DisplayFormat.FormatString = "0.00%";
            this.bandedGridColumn10.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn10.FieldName = "_256qamDownProportion";
            this.bandedGridColumn10.GroupFormat.FormatString = "0.00%";
            this.bandedGridColumn10.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn10.Name = "bandedGridColumn10";
            this.bandedGridColumn10.RowCount = 4;
            this.bandedGridColumn10.Visible = true;
            // 
            // bandedGridColumn11
            // 
            this.bandedGridColumn11.Caption = "QPSK上行总数";
            this.bandedGridColumn11.FieldName = "_qpskUpCount";
            this.bandedGridColumn11.Name = "bandedGridColumn11";
            this.bandedGridColumn11.RowCount = 4;
            this.bandedGridColumn11.Visible = true;
            // 
            // bandedGridColumn12
            // 
            this.bandedGridColumn12.Caption = "QPSK下行总数";
            this.bandedGridColumn12.FieldName = "_qpskDownCount";
            this.bandedGridColumn12.Name = "bandedGridColumn12";
            this.bandedGridColumn12.RowCount = 4;
            this.bandedGridColumn12.Visible = true;
            // 
            // bandedGridColumn13
            // 
            this.bandedGridColumn13.Caption = "16QAM上行总数";
            this.bandedGridColumn13.FieldName = "_16qamUpCount";
            this.bandedGridColumn13.Name = "bandedGridColumn13";
            this.bandedGridColumn13.RowCount = 4;
            this.bandedGridColumn13.Visible = true;
            // 
            // bandedGridColumn14
            // 
            this.bandedGridColumn14.Caption = "16QAM下行总数";
            this.bandedGridColumn14.FieldName = "_16qamDownCount";
            this.bandedGridColumn14.Name = "bandedGridColumn14";
            this.bandedGridColumn14.RowCount = 4;
            this.bandedGridColumn14.Visible = true;
            // 
            // bandedGridColumn15
            // 
            this.bandedGridColumn15.Caption = "64QAM上行总数";
            this.bandedGridColumn15.FieldName = "_64qamUpCount";
            this.bandedGridColumn15.Name = "bandedGridColumn15";
            this.bandedGridColumn15.RowCount = 4;
            this.bandedGridColumn15.Visible = true;
            // 
            // bandedGridColumn16
            // 
            this.bandedGridColumn16.Caption = "64QAM下行总数";
            this.bandedGridColumn16.FieldName = "_64qamDownCount";
            this.bandedGridColumn16.Name = "bandedGridColumn16";
            this.bandedGridColumn16.RowCount = 4;
            this.bandedGridColumn16.Visible = true;
            // 
            // bandedGridColumn17
            // 
            this.bandedGridColumn17.Caption = "256QAM上行总数";
            this.bandedGridColumn17.FieldName = "_256qamUpCount";
            this.bandedGridColumn17.Name = "bandedGridColumn17";
            this.bandedGridColumn17.RowCount = 4;
            this.bandedGridColumn17.Visible = true;
            // 
            // bandedGridColumn18
            // 
            this.bandedGridColumn18.Caption = "256QAM下行总数";
            this.bandedGridColumn18.FieldName = "_256qamDownCount";
            this.bandedGridColumn18.Name = "bandedGridColumn18";
            this.bandedGridColumn18.RowCount = 4;
            this.bandedGridColumn18.Visible = true;


            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1147, 453);
            this.Controls.Add(this.gridControl);
            this.Name = "ServiceDelayForm";
            this.Text = "调度编码方式分析";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView)).EndInit();
            this.ResumeLayout(false);
        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView;

        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;

        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;    // 序号
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;    // log名称
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;    // QPSK 上行占比(%)
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn5;    // 16QAM 上行占比(%)
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn7;    // 64QAM 上行占比(%)
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn9;    // 256QAM 上行占比(%)
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;    // QPSK 下行占比(%)
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn6;    // 16QAM 下行占比(%)
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn8;    // 64QAM 下行占比(%)
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn10;   // 256QAM 下行占比(%)
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn11;   // QPSK 上行总数
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn13;   // 16QAM 上行总数
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn15;   // 64QAM 上行总数
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn17;   // 256QAM 上行总数
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn12;   // QPSK 下行总数
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn14;   // 16QAM 下行总数
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn16;   // 64QAM 下行总数
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn18;   // 256QAM 下行总数


        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
    }
}