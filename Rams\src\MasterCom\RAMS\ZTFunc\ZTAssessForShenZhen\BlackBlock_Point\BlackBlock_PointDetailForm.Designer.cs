﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class BlackBlock_PointDetailForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.edtBlockID = new DevExpress.XtraEditors.TextEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.xtraTabControl = new DevExpress.XtraTab.XtraTabControl();
            this.pagEvent = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlEvent = new DevExpress.XtraGrid.GridControl();
            this.gridViewEvent = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnDateTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnEventDesc = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLongitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLatitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnProject = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCenterPoint = new DevExpress.XtraGrid.Columns.GridColumn();
            this.pagValidate = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlDate = new DevExpress.XtraGrid.GridControl();
            this.gridViewDate = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTestResult = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnIncludeProjectsNeeds = new DevExpress.XtraGrid.Columns.GridColumn();
            this.pagEventValidate = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlEventValidate = new DevExpress.XtraGrid.GridControl();
            this.gridViewEventValidate = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edtBlockID.Properties)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl)).BeginInit();
            this.xtraTabControl.SuspendLayout();
            this.pagEvent.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlEvent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewEvent)).BeginInit();
            this.pagValidate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewDate)).BeginInit();
            this.pagEventValidate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlEventValidate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewEventValidate)).BeginInit();
            this.SuspendLayout();
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.edtBlockID);
            this.panelControl1.Controls.Add(this.labelControl1);
            this.panelControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControl1.Location = new System.Drawing.Point(0, 0);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(932, 47);
            this.panelControl1.TabIndex = 0;
            // 
            // edtBlockID
            // 
            this.edtBlockID.Location = new System.Drawing.Point(110, 13);
            this.edtBlockID.Name = "edtBlockID";
            this.edtBlockID.Properties.ReadOnly = true;
            this.edtBlockID.Size = new System.Drawing.Size(100, 21);
            this.edtBlockID.TabIndex = 1;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(32, 16);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(72, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "问题点编号：";
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(134, 26);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(133, 22);
            this.miReplay.Text = "回放事件...";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // xtraTabControl
            // 
            this.xtraTabControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl.Location = new System.Drawing.Point(0, 47);
            this.xtraTabControl.Name = "xtraTabControl";
            this.xtraTabControl.SelectedTabPage = this.pagEvent;
            this.xtraTabControl.Size = new System.Drawing.Size(932, 383);
            this.xtraTabControl.TabIndex = 2;
            this.xtraTabControl.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.pagEvent,
            this.pagValidate,
            this.pagEventValidate});
            // 
            // pagEvent
            // 
            this.pagEvent.Controls.Add(this.gridControlEvent);
            this.pagEvent.Name = "pagEvent";
            this.pagEvent.Size = new System.Drawing.Size(925, 353);
            this.pagEvent.Text = "生成异常事件";
            // 
            // gridControlEvent
            // 
            this.gridControlEvent.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlEvent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlEvent.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlEvent.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlEvent.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlEvent.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlEvent.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlEvent.Location = new System.Drawing.Point(0, 0);
            this.gridControlEvent.MainView = this.gridViewEvent;
            this.gridControlEvent.Name = "gridControlEvent";
            this.gridControlEvent.Size = new System.Drawing.Size(925, 353);
            this.gridControlEvent.TabIndex = 4;
            this.gridControlEvent.UseEmbeddedNavigator = true;
            this.gridControlEvent.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewEvent});
            // 
            // gridViewEvent
            // 
            this.gridViewEvent.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnDateTime,
            this.gridColumnEventDesc,
            this.gridColumnFileName,
            this.gridColumnCellName,
            this.gridColumnLongitude,
            this.gridColumnLatitude,
            this.gridColumnLAC,
            this.gridColumnCI,
            this.gridColumnProject,
            this.gridColumnType,
            this.gridColumnCenterPoint});
            this.gridViewEvent.GridControl = this.gridControlEvent;
            this.gridViewEvent.Name = "gridViewEvent";
            this.gridViewEvent.OptionsBehavior.Editable = false;
            this.gridViewEvent.OptionsView.ColumnAutoWidth = false;
            this.gridViewEvent.OptionsView.ShowDetailButtons = false;
            this.gridViewEvent.OptionsView.ShowGroupPanel = false;
            this.gridViewEvent.DoubleClick += new System.EventHandler(this.gridViewEvent_DoubleClick);
            // 
            // gridColumnDateTime
            // 
            this.gridColumnDateTime.Caption = "时间";
            this.gridColumnDateTime.FieldName = "DateTimeString";
            this.gridColumnDateTime.Name = "gridColumnDateTime";
            this.gridColumnDateTime.Visible = true;
            this.gridColumnDateTime.VisibleIndex = 0;
            // 
            // gridColumnEventDesc
            // 
            this.gridColumnEventDesc.Caption = "事件";
            this.gridColumnEventDesc.FieldName = "EventDesc";
            this.gridColumnEventDesc.Name = "gridColumnEventDesc";
            this.gridColumnEventDesc.Visible = true;
            this.gridColumnEventDesc.VisibleIndex = 1;
            // 
            // gridColumnFileName
            // 
            this.gridColumnFileName.Caption = "文件名";
            this.gridColumnFileName.FieldName = "FileName";
            this.gridColumnFileName.Name = "gridColumnFileName";
            this.gridColumnFileName.Visible = true;
            this.gridColumnFileName.VisibleIndex = 9;
            // 
            // gridColumnCellName
            // 
            this.gridColumnCellName.Caption = "小区";
            this.gridColumnCellName.FieldName = "CellName";
            this.gridColumnCellName.Name = "gridColumnCellName";
            this.gridColumnCellName.Visible = true;
            this.gridColumnCellName.VisibleIndex = 2;
            // 
            // gridColumnLongitude
            // 
            this.gridColumnLongitude.Caption = "经度";
            this.gridColumnLongitude.FieldName = "Longitude";
            this.gridColumnLongitude.Name = "gridColumnLongitude";
            this.gridColumnLongitude.Visible = true;
            this.gridColumnLongitude.VisibleIndex = 3;
            // 
            // gridColumnLatitude
            // 
            this.gridColumnLatitude.Caption = "纬度";
            this.gridColumnLatitude.FieldName = "Latitude";
            this.gridColumnLatitude.Name = "gridColumnLatitude";
            this.gridColumnLatitude.Visible = true;
            this.gridColumnLatitude.VisibleIndex = 4;
            // 
            // gridColumnLAC
            // 
            this.gridColumnLAC.Caption = "LAC";
            this.gridColumnLAC.FieldName = "LAC";
            this.gridColumnLAC.Name = "gridColumnLAC";
            this.gridColumnLAC.Visible = true;
            this.gridColumnLAC.VisibleIndex = 5;
            // 
            // gridColumnCI
            // 
            this.gridColumnCI.Caption = "CI";
            this.gridColumnCI.FieldName = "CI";
            this.gridColumnCI.Name = "gridColumnCI";
            this.gridColumnCI.Visible = true;
            this.gridColumnCI.VisibleIndex = 6;
            // 
            // gridColumnProject
            // 
            this.gridColumnProject.Caption = "项目";
            this.gridColumnProject.FieldName = "ProjectName";
            this.gridColumnProject.Name = "gridColumnProject";
            this.gridColumnProject.Visible = true;
            this.gridColumnProject.VisibleIndex = 10;
            this.gridColumnProject.Width = 143;
            // 
            // gridColumnType
            // 
            this.gridColumnType.Caption = "重复类型";
            this.gridColumnType.FieldName = "TypeString";
            this.gridColumnType.Name = "gridColumnType";
            this.gridColumnType.Visible = true;
            this.gridColumnType.VisibleIndex = 7;
            // 
            // gridColumnCenterPoint
            // 
            this.gridColumnCenterPoint.Caption = "是否圆心";
            this.gridColumnCenterPoint.FieldName = "CenterPointString";
            this.gridColumnCenterPoint.Name = "gridColumnCenterPoint";
            this.gridColumnCenterPoint.Visible = true;
            this.gridColumnCenterPoint.VisibleIndex = 8;
            // 
            // pagValidate
            // 
            this.pagValidate.Controls.Add(this.gridControlDate);
            this.pagValidate.Name = "pagValidate";
            this.pagValidate.Size = new System.Drawing.Size(925, 353);
            this.pagValidate.Text = "验证情况";
            // 
            // gridControlDate
            // 
            this.gridControlDate.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlDate.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlDate.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlDate.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlDate.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlDate.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlDate.Location = new System.Drawing.Point(0, 0);
            this.gridControlDate.MainView = this.gridViewDate;
            this.gridControlDate.Name = "gridControlDate";
            this.gridControlDate.Size = new System.Drawing.Size(925, 353);
            this.gridControlDate.TabIndex = 5;
            this.gridControlDate.UseEmbeddedNavigator = true;
            this.gridControlDate.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewDate});
            // 
            // gridViewDate
            // 
            this.gridViewDate.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnDate,
            this.gridColumnTestResult,
            this.gridColumnIncludeProjectsNeeds});
            this.gridViewDate.GridControl = this.gridControlDate;
            this.gridViewDate.Name = "gridViewDate";
            this.gridViewDate.OptionsBehavior.Editable = false;
            this.gridViewDate.OptionsView.ColumnAutoWidth = false;
            this.gridViewDate.OptionsView.ShowDetailButtons = false;
            this.gridViewDate.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnDate
            // 
            this.gridColumnDate.Caption = "测试日期";
            this.gridColumnDate.FieldName = "DateString";
            this.gridColumnDate.Name = "gridColumnDate";
            this.gridColumnDate.Visible = true;
            this.gridColumnDate.VisibleIndex = 0;
            this.gridColumnDate.Width = 88;
            // 
            // gridColumnTestResult
            // 
            this.gridColumnTestResult.Caption = "测试情况";
            this.gridColumnTestResult.FieldName = "TestResultString";
            this.gridColumnTestResult.Name = "gridColumnTestResult";
            this.gridColumnTestResult.Visible = true;
            this.gridColumnTestResult.VisibleIndex = 1;
            this.gridColumnTestResult.Width = 99;
            // 
            // gridColumnIncludeProjectsNeeds
            // 
            this.gridColumnIncludeProjectsNeeds.Caption = "是否包含ATU项目";
            this.gridColumnIncludeProjectsNeeds.FieldName = "IncludeProjectsNeedsString";
            this.gridColumnIncludeProjectsNeeds.Name = "gridColumnIncludeProjectsNeeds";
            this.gridColumnIncludeProjectsNeeds.Visible = true;
            this.gridColumnIncludeProjectsNeeds.VisibleIndex = 2;
            this.gridColumnIncludeProjectsNeeds.Width = 118;
            // 
            // pagEventValidate
            // 
            this.pagEventValidate.Controls.Add(this.gridControlEventValidate);
            this.pagEventValidate.Name = "pagEventValidate";
            this.pagEventValidate.Size = new System.Drawing.Size(925, 353);
            this.pagEventValidate.Text = "验证异常事件";
            // 
            // gridControlEventValidate
            // 
            this.gridControlEventValidate.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlEventValidate.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlEventValidate.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlEventValidate.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlEventValidate.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlEventValidate.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlEventValidate.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlEventValidate.Location = new System.Drawing.Point(0, 0);
            this.gridControlEventValidate.MainView = this.gridViewEventValidate;
            this.gridControlEventValidate.Name = "gridControlEventValidate";
            this.gridControlEventValidate.Size = new System.Drawing.Size(925, 353);
            this.gridControlEventValidate.TabIndex = 5;
            this.gridControlEventValidate.UseEmbeddedNavigator = true;
            this.gridControlEventValidate.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewEventValidate});
            this.gridControlEventValidate.DoubleClick += new System.EventHandler(this.gridViewEvent_DoubleClick);
            // 
            // gridViewEventValidate
            // 
            this.gridViewEventValidate.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11});
            this.gridViewEventValidate.GridControl = this.gridControlEventValidate;
            this.gridViewEventValidate.Name = "gridViewEventValidate";
            this.gridViewEventValidate.OptionsBehavior.Editable = false;
            this.gridViewEventValidate.OptionsView.ColumnAutoWidth = false;
            this.gridViewEventValidate.OptionsView.ShowDetailButtons = false;
            this.gridViewEventValidate.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "时间";
            this.gridColumn1.FieldName = "DateTimeString";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "事件";
            this.gridColumn2.FieldName = "EventDesc";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "文件名";
            this.gridColumn3.FieldName = "FileName";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 9;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "小区";
            this.gridColumn4.FieldName = "CellName";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 2;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "经度";
            this.gridColumn5.FieldName = "Longitude";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 3;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "纬度";
            this.gridColumn6.FieldName = "Latitude";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 4;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "LAC";
            this.gridColumn7.FieldName = "LAC";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 5;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "CI";
            this.gridColumn8.FieldName = "CI";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 6;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "项目";
            this.gridColumn9.FieldName = "ProjectName";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 10;
            this.gridColumn9.Width = 143;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "重复类型";
            this.gridColumn10.FieldName = "TypeString";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 7;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "是否圆心";
            this.gridColumn11.FieldName = "CenterPointString";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 8;
            // 
            // BlackBlock_PointDetailForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(932, 430);
            this.Controls.Add(this.xtraTabControl);
            this.Controls.Add(this.panelControl1);
            this.Name = "BlackBlock_PointDetailForm";
            this.Text = "重复问题点详情";
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edtBlockID.Properties)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl)).EndInit();
            this.xtraTabControl.ResumeLayout(false);
            this.pagEvent.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlEvent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewEvent)).EndInit();
            this.pagValidate.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewDate)).EndInit();
            this.pagEventValidate.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlEventValidate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewEventValidate)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.TextEdit edtBlockID;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl;
        private DevExpress.XtraTab.XtraTabPage pagEvent;
        private DevExpress.XtraTab.XtraTabPage pagValidate;
        private DevExpress.XtraGrid.GridControl gridControlEvent;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewEvent;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDateTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnEventDesc;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLongitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLatitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLAC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnProject;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnType;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCenterPoint;
        private DevExpress.XtraGrid.GridControl gridControlDate;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewDate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTestResult;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnIncludeProjectsNeeds;
        private DevExpress.XtraTab.XtraTabPage pagEventValidate;
        private DevExpress.XtraGrid.GridControl gridControlEventValidate;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewEventValidate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
    }
}