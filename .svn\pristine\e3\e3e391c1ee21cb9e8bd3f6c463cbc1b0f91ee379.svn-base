﻿using System;
using System.Collections.Generic;
using System.Text;
using AxMapWinGIS;
using MasterCom.MTGis;
using MapWinGIS;
using GMap.NET;

namespace MasterCom.RAMS.MapControlTool
{
    public class MapControlToolAddRect
    {
        public event EventHandler RectangleCreated;
        readonly AxMap mapcontrol;
        private MapWinGIS.Shapefile sf;
        private int layerHandle = -1;
        private bool isActive = false;
        public Shape Rectangle { get; set; }
        public DbRect RectangleRegion
        {
            get
            {
                DbRect rect = null;
                if (Rectangle != null)
                {
                    double minX = double.MaxValue;
                    double minY = double.MaxValue;
                    double maxX = double.MinValue;
                    double maxY = double.MinValue;
                    for (int i = 0; i < Rectangle.numPoints; i++)
                    {
                        minX = Math.Min(Rectangle.get_Point(i).x, minX);
                        minY = Math.Min(Rectangle.get_Point(i).y, minY);
                        maxX = Math.Max(Rectangle.get_Point(i).x, maxX);
                        maxY = Math.Max(Rectangle.get_Point(i).y, maxY);
                    }
                    rect = new DbRect(minX, minY, maxX, maxY);
                }
                return rect;
            }
        }

        public MapWinGIS.Shapefile ShapeLayer
        {
            get
            {
                return sf;
            }
        }

        public MapControlToolAddRect(AxMap map)
        {
            mapcontrol = map;
        }

        public void Clear()
        {
            sf.EditClear();
            Rectangle = null;
        }

        private void FireRectangleCreated()
        {
            if (RectangleCreated != null)
            {
                RectangleCreated(this, EventArgs.Empty);
            }
        }

        public void Activate()
        {
            if (!isActive)
            {
                mapcontrol.MouseUpEvent += new AxMapWinGIS._DMapEvents_MouseUpEventHandler(mapcontrol_MouseUpEvent);
                mapcontrol.SelectBoxFinal += new AxMapWinGIS._DMapEvents_SelectBoxFinalEventHandler(mapcontrol_SelectBoxFinal);
            }
            isActive = true;
            mapcontrol.CursorMode = tkCursorMode.cmSelection;
            mapcontrol.MapCursor = tkCursor.crsrCross;
            addRectangleLayer();
        }

        void mapcontrol_MouseUpEvent(object sender, _DMapEvents_MouseUpEvent e)
        {
            if (e.button==2)
            {
                return;
            }
            sf.EditClear();
            Rectangle = null;
            FireRectangleCreated();
        }

        void mapcontrol_SelectBoxFinal(object sender, _DMapEvents_SelectBoxFinalEvent e)
        {
            if (e.bottom == e.top || e.left == e.right)
            {
                return;
            }
            double minX = 0;
            double minY = 0;
            double maxX = 0;
            double maxY = 0;
            mapcontrol.PixelToProj(e.left, e.top, ref minX, ref maxY);
            mapcontrol.PixelToProj(e.right, e.bottom, ref maxX, ref minY);
            createRect(minX, maxY, maxX, minY);
        }

        /// <summary>
        /// 创建矩形
        /// </summary>
        /// <param name="ltX">左上角经度</param>
        /// <param name="ltY">左上角纬度</param>
        /// <param name="brX">右下角经度</param>
        /// <param name="brY">右下角纬度</param>
        private void createRect(double ltX, double ltY, double brX, double brY)
        {
            sf.EditClear();
            int shpIdx = 0;
            Rectangle = ShapeHelper.CreateRectShape(ltX, ltY, brX, brY);
            sf.EditInsertShape(Rectangle, ref shpIdx);
            int pos = mapcontrol.get_LayerPosition(layerHandle);
            mapcontrol.MoveLayerTop(pos);
            FireRectangleCreated();
        }

        public void CreateRect(DbRect rectRegion)
        {
            sf.EditClear();
            if (rectRegion == null)
            {
                Rectangle = null;
                FireRectangleCreated();
            }
            else
            {
                ltPoint = new DbPoint(Math.Min(rectRegion.x1, rectRegion.x2), Math.Max(rectRegion.y1, rectRegion.y2));
                brPoint = new DbPoint(Math.Max(rectRegion.x1, rectRegion.x2), Math.Min(rectRegion.y1, rectRegion.y2));
                createRect(ltPoint.x, ltPoint.y, brPoint.x, brPoint.y);
            }
        }

        private DbPoint ltPoint { get; set; }
        private DbPoint brPoint { get; set; }

        /// <summary>
        /// Deactivates the rectangle drawing function
        /// </summary>
        public void Deactivate()
        {
            if (!isActive) return;
            mapcontrol.SelectBoxFinal -= this.mapcontrol_SelectBoxFinal;
            mapcontrol.MouseUpEvent -= mapcontrol_MouseUpEvent;
            isActive = false;
        }

        private void addRectangleLayer()
        {
            if (sf == null)
            {
                sf = new Shapefile();
                sf.CreateNew("", ShpfileType.SHP_POLYGON);
                sf.DefaultDrawingOptions.LineColor = (uint)System.Drawing.ColorTranslator.ToOle(System.Drawing.Color.Green);
                sf.DefaultDrawingOptions.LineWidth = 3;
                sf.DefaultDrawingOptions.FillVisible = true;
                sf.DefaultDrawingOptions.FillColor = (uint)System.Drawing.ColorTranslator.ToOle(System.Drawing.Color.Green);
                sf.DefaultDrawingOptions.FillTransparency = 35;
            }
            Shapefile sfTemp = mapcontrol.get_Shapefile(layerHandle);
            if (sfTemp != sf)
            {
                layerHandle = mapcontrol.AddLayer(sf, true);
            }
        }

    }
}
