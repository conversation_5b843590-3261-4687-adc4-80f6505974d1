﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 持续道路报告显示列
    /// </summary>
    public class TestPointDisplayColumn
    {
        public TestPointDisplayColumn() { }
        public TestPointDisplayColumn(string caption,DTDisplayParameterInfo param, int paramArrayIndex, ESummaryValueType valueType)
        {
            this.Caption = caption;
            this.DisplayParam = param;
            this.ParamArrayIndex = paramArrayIndex;
            this.ValueType = valueType;
        }
        public string Caption { get; set; } = "未命名";
        /// <summary>
        /// 由 ParamName 和 ParamArrayIndex 组成的参数唯一key
        /// </summary>
        public string ParamKey
        {
            get
            {
                return string.Format("{0}[{1}]", DisplayParam.ParamInfo.Name,
                    ParamArrayIndex);
            }
        }
        public override string ToString()
        {
            return Caption;
        }
        public DTDisplayParameterInfo DisplayParam
        {
            get;
            set;
        }
        public int ParamArrayIndex
        {
            get;
            set;
        }
        public ESummaryValueType ValueType
        {
            get;
            set;
        }

        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["Caption"] = this.Caption;
                paramDic["SysName"] = this.DisplayParam.System.Name;
                paramDic["ParamName"] = this.DisplayParam.Name;
                paramDic["ParamArrayIndex"] = this.ParamArrayIndex;
                paramDic["SummaryValueType"] = this.ValueType.ToString();
                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.Caption = value["Caption"].ToString();
                string sysName = value["SysName"].ToString();
                string paramName = value["ParamName"].ToString();
                this.DisplayParam = DTDisplayParameterManager.GetInstance()[sysName][paramName];
                this.ParamArrayIndex = (int)value["ParamArrayIndex"];
                this.ValueType = (ESummaryValueType)Enum.Parse(typeof(ESummaryValueType), value["SummaryValueType"].ToString());
            }
        }

        public static string MakeShortCaption(DTDisplayParameterInfo param, int paramArrIdx)
        {
            string idxStr = paramArrIdx == -1 ? " " : "[" + paramArrIdx.ToString() + "] ";
            return param.Name + idxStr;
        }
        public override bool Equals(object obj)
        {
            if (obj is TestPointDisplayColumn)
            {
                TestPointDisplayColumn col = obj as TestPointDisplayColumn;
                return col.ParamKey.Equals(this.ParamKey) && col.ValueType.Equals(this.ValueType);
            }
            return false;
        }

        public override int GetHashCode()
        {
            return (ParamKey + "_" + ValueType).GetHashCode();
        }
    }
}
