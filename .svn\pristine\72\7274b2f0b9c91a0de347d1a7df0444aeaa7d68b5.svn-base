﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTNrNBCellCheckAna
{
    public partial class NBCellCheckConditionForm : BaseDialog
    {
        public NBCellCheckConditionForm()
        {
            InitializeComponent();
        }

        public NrNBCellCheckCondition GetCondition()
        {
            NrNBCellCheckCondition condition = new NrNBCellCheckCondition();
            condition.SampleCount = (int)numSampleCount.Value;
            condition.RSRP = (int)numRSRP.Value;
            condition.Distance = (int)numDistance.Value;
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
