using System;
using System.Collections.Generic;
using System.Text;
using GeneGraph;
using System.Collections;

namespace MasterCom.RAMS.Func.AssistLayer
{
    [Serializable()]
    public class CombinationElement : BaseElementEx
    {
        public CombinationElement():base()
        {
        }

        public LabelElementEx Label { get; set; }

        public ArrayList Elements
        {
            get { return al; }
        }

        ArrayList al = new ArrayList();

        public override void Draw(System.Drawing.Graphics g)
        {
            foreach (BaseElementEx be in al)
            {
                be.LocationM = this.LocationM;
                be.GisAdapter = this.GisAdapter;
                be.IsScale = this.IsScale;
                be.Draw(g);
            }
            if (Label != null)
            {
                Label.LocationM = this.LocationM;
                Label.GisAdapter = this.GisAdapter;
                Label.IsScale = this.IsScale;
                Label.Draw(g);
            }
        }

        public override System.Drawing.Drawing2D.GraphicsPath GraphicsPath
        {
            get
            {
                System.Drawing.Drawing2D.GraphicsPath gp = new System.Drawing.Drawing2D.GraphicsPath();
                foreach (BaseElement be in al)
                {
                    gp.AddPath(be.GraphicsPath, false);
                }
                return gp;
            }
            set
            {
                base.GraphicsPath = value;
            }
        }
    }
}
