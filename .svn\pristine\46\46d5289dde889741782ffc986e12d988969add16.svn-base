﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTWeakCoverByEventSetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.gpCond = new DevExpress.XtraEditors.GroupControl();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.numDistanceSpan = new System.Windows.Forms.NumericUpDown();
            this.numTimeSpan = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.gpDefine = new DevExpress.XtraEditors.GroupControl();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.gpCond)).BeginInit();
            this.gpCond.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceSpan)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeSpan)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gpDefine)).BeginInit();
            this.gpDefine.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(314, 155);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(65, 23);
            this.btnCancel.TabIndex = 12;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(198, 155);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(63, 23);
            this.btnOK.TabIndex = 11;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // gpCond
            // 
            this.gpCond.Controls.Add(this.label4);
            this.gpCond.Controls.Add(this.label3);
            this.gpCond.Controls.Add(this.numDistanceSpan);
            this.gpCond.Controls.Add(this.numTimeSpan);
            this.gpCond.Controls.Add(this.label2);
            this.gpCond.Controls.Add(this.label1);
            this.gpCond.Location = new System.Drawing.Point(1, 3);
            this.gpCond.Name = "gpCond";
            this.gpCond.Size = new System.Drawing.Size(475, 58);
            this.gpCond.TabIndex = 19;
            this.gpCond.Text = "条件设置";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(384, 33);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(35, 12);
            this.label4.TabIndex = 22;
            this.label4.Text = "米(m)";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(169, 33);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(35, 12);
            this.label3.TabIndex = 21;
            this.label3.Text = "秒(s)";
            // 
            // numDistanceSpan
            // 
            this.numDistanceSpan.Location = new System.Drawing.Point(298, 28);
            this.numDistanceSpan.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numDistanceSpan.Name = "numDistanceSpan";
            this.numDistanceSpan.Size = new System.Drawing.Size(80, 21);
            this.numDistanceSpan.TabIndex = 20;
            this.numDistanceSpan.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDistanceSpan.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // numTimeSpan
            // 
            this.numTimeSpan.Location = new System.Drawing.Point(84, 28);
            this.numTimeSpan.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numTimeSpan.Name = "numTimeSpan";
            this.numTimeSpan.Size = new System.Drawing.Size(79, 21);
            this.numTimeSpan.TabIndex = 19;
            this.numTimeSpan.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numTimeSpan.Value = new decimal(new int[] {
            180,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(227, 33);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 18;
            this.label2.Text = "距离间隔：";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(5, 33);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 17;
            this.label1.Text = "时间间隔：";
            // 
            // gpDefine
            // 
            this.gpDefine.Controls.Add(this.label6);
            this.gpDefine.Controls.Add(this.label5);
            this.gpDefine.Location = new System.Drawing.Point(1, 67);
            this.gpDefine.Name = "gpDefine";
            this.gpDefine.Size = new System.Drawing.Size(475, 82);
            this.gpDefine.TabIndex = 20;
            this.gpDefine.Text = "定义描述";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(5, 62);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(401, 12);
            this.label6.TabIndex = 20;
            this.label6.Text = "条件过滤规则定义：时间相隔180秒内，且距离在100米内的同类事件剔除。";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(5, 35);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(437, 12);
            this.label5.TabIndex = 19;
            this.label5.Text = "弱覆盖路段事件定义：大于100米小于500米，且采样点小于-110dbm比例大于70%。";
            // 
            // ZTWeakCoverByEventSetForm
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(480, 193);
            this.Controls.Add(this.gpDefine);
            this.Controls.Add(this.gpCond);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "ZTWeakCoverByEventSetForm";
            this.Text = "干线弱覆盖路段数条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.gpCond)).EndInit();
            this.gpCond.ResumeLayout(false);
            this.gpCond.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceSpan)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeSpan)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gpDefine)).EndInit();
            this.gpDefine.ResumeLayout(false);
            this.gpDefine.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private DevExpress.XtraEditors.GroupControl gpCond;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numDistanceSpan;
        private System.Windows.Forms.NumericUpDown numTimeSpan;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.GroupControl gpDefine;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
    }
}