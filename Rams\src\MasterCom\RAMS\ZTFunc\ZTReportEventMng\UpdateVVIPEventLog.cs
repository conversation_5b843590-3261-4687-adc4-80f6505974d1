﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc.ZTReportEventMng
{
    public class UpdateVVIPEventLog:DIYSQLBase
    {
        private readonly EventLog evtLog;
        public UpdateVVIPEventLog(EventLog log)
            : base(MainModel.GetInstance())
        {
            MainDB = true;
            evtLog = log;
        }

        protected override string getSqlTextString()
        {
            StringBuilder sb = new StringBuilder("update ");
            sb.Append(evtLog is EventLogGSM ? "tb_gsm_vvip_log " : "tb_td_vvip_log");
            sb.Append(" set 基站类型='");
            sb.Append(evtLog.BTSType);
            sb.Append("',事件产生地点='");
            sb.Append(evtLog.Place);
            sb.Append("',经度=");
            sb.Append(evtLog.Lng.ToString());
            sb.Append(",纬度=");
            sb.Append(evtLog.Lat.ToString());
            sb.Append(",事件分析='");
            sb.Append(evtLog.Analytics);
            sb.Append("',解决建议='");
            sb.Append(evtLog.Suggestion);
            sb.Append("',分类原因='");
            sb.Append(evtLog.ErrorType);
            sb.Append("',所属片='");
            sb.Append(evtLog.OwnRegion);
            sb.Append("',是否闭环='");
            sb.Append(evtLog.IsCloseLoop);
            sb.Append("',是否有traffic记录='");
            sb.Append(evtLog.HasTrafficLog);
            sb.Append("' where 事件编号=");
            sb.Append(evtLog.SN.ToString());
            return sb.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] arr = new E_VType[1];
            arr[0] = E_VType.E_Int;
            return arr;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
        }
    }
}
