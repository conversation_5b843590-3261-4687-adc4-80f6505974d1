using System;
using System.Collections.Generic;
using System.Text;
using System.Runtime.InteropServices;

namespace MasterCom.Util.UiEx
{
    internal class Win32
    {
        [DllImport("user32", EntryPoint = "GetClientRect")]
        public static extern int GetClientRect(
            IntPtr hwnd,
            ref RECT lpRect
            );

        [DllImport("user32", EntryPoint = "GetWindowRect")]
        public static extern int GetWindowRect(
            IntPtr hwnd,
            ref RECT lpRect
            );

        [DllImport("user32", EntryPoint = "PtInRect")]
        public static extern int PtInRect(
            ref RECT lpRect,
            ref POINTAPI pt
            );

        [DllImport("user32", EntryPoint = "SendMessage")]
        public static extern int SendMessage(
            IntPtr hwnd,
            int wMsg,
            int wParam,
            int lParam
            );

        [DllImport("user32", EntryPoint = "SendMessage")]
        public static extern int SendMessage(
            IntPtr hwnd,
            int wMsg,
            int wParam,
            ref RECT rect
            );

        [DllImport("user32", EntryPoint = "InvalidateRect")]
        public static extern int InvalidateRect(
            IntPtr hwnd,
            RECT lpRect,
            int bErase
            );

        [StructLayout(LayoutKind.Sequential)]
        public struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }
        [StructLayout(LayoutKind.Sequential)]
        public struct POINTAPI
        {
            public int x;
            public int y;
        }

        public static int HIWORD(int lparam)
        {
            return ((lparam >> 16) & 0xffff);
        }

        public static int LOWORD(int lparam)
        {
            return (lparam & 0xffff);
        }

        public static int MakeLParam(int LoWord, int HiWord)
        {
            return (HiWord << 16) | (LoWord & 0xffff);
        }


    }


}
