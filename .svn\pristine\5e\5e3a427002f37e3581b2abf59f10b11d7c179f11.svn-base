using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYCoverDistanceByRegion_TDScan : ZTDIYCoverDistanceByRegion_TD
    {
        public ZTDIYCoverDistanceByRegion_TDScan(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }
        
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 16000, 16021, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_Channel";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_CPI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"CoverDistance");
            tmpDic.Add("themeName", (object)"TDSCAN_PCCPCH_RSCP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void clearDataBeforeQuery()
        {
            tdCellCoverDistanceDic.Clear();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                for (int i = 0; i < 50; i++)
                {
                    float? rscp = (float?)tp["TDS_PCCPCH_RSCP", i];
                    if (rscp == null || rscp < -120 || rscp > -10)
                    {
                        break;
                    }
                    short? channel = (short?)(int?)tp["TDS_PCCPCH_Channel", i];
                    byte? cpi = (byte?)(int?)tp["TDS_PCCPCH_CPI", i];
                    if (channel == null || cpi == null)
                    {
                        continue;
                    }
                    TDCell mainTDCell = CellManager.GetInstance().GetNearestTDCell(tp.DateTime, (short)channel, (byte)cpi, tp.Longitude, tp.Latitude);
                    if (mainTDCell == null)
                    {
                        continue;
                    }
                    saveTestPoint(tp, mainTDCell, (float)rscp);
                }
            }
            catch
            {
                //continue
            }
        }

        protected override void getResultAfterQuery()
        {
            MainModel.CellCoverDistanceList = new List<CellCoverDistance>(tdCellCoverDistanceDic.Values);
        }
    }

    public class ZTDIYCoverDistanceByRegion_WScan : ZTDIYCoverDistanceByRegion_W
    {
        public ZTDIYCoverDistanceByRegion_WScan(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 32000, 32005, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "WS_CPICHTotalRSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "WS_CPICHChannel";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "WS_CPICHPilot";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"CoverDistance");
            tmpDic.Add("themeName", (object)"WCDAMSCAN_CPICHTotalRSCP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void clearDataBeforeQuery()
        {
            wCellCoverDistanceDic.Clear();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                for (int i = 0; i < 50; i++)
                {
                    float? rscp = (float?)tp["WS_CPICHTotalRSCP", i];
                    if (rscp == null || rscp < -120 || rscp > -10)
                    {
                        break;
                    }
                    short? channel = tp["WS_CPICHChannel", i] as short?;
                    short? cpi = tp["WS_CPICHPilot", i] as short?;
                    if (channel == null || cpi == null)
                    {
                        continue;
                    }
                    WCell mainWCell = CellManager.GetInstance().GetNearestWCell(tp.DateTime, (short)channel, (short)cpi, tp.Longitude, tp.Latitude);
                    if (mainWCell == null)
                    {
                        continue;
                    }
                    saveTestPoint(tp, mainWCell, (float)rscp);
                }
            }
            catch
            {
                //continue
            }
        }

        protected override void getResultAfterQuery()
        {
            MainModel.CellCoverDistanceList = new List<CellCoverDistance>(wCellCoverDistanceDic.Values);
        }
    }
}