﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.ES.UI;
using System.Drawing;
using MasterCom.Util;
using System.ComponentModel;
using System.Drawing.Design;
using MasterCom.ES.Data;

namespace MasterCom.ES.Core
{
    public enum NodeType
    {
        Condition = 0,  //判定节点
        Operate,        //操作节点
        PreProblemType, //预判定类型
        ReasonDesc,     //问题原因描述
        SolveDesc,       //解决方案描述
        OtherProc        //其它流程调用
    }
    public class NodeEntry
    {
        //
        public int xPos { get; set; }
        public int yPos { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
        //
        public int _xOffset { get; set; }
        public int _yOffset { get; set; }
        public bool _breakPoint { get; set; } = false;
        //
        /// <summary>
        /// 节点描述内容
        /// </summary>
        public string expString { get; set; } = "";
        public NodeType Type { get; set; } = NodeType.Condition;

        public string decideExp { get; set; } = "";//执行操作，返回结果true or false
        public NodeEntry YesNode { get; set; }
        public NodeEntry NoNode { get; set; }
        public List<NodeEntry> _ParentNodes { get; set; } = new List<NodeEntry>();
        public int _Idx { get; set; }
        [CategoryAttribute("显示内容"), DescriptionAttribute("坐标值X")]
        public int XPos
        {
            get
            {
                return xPos;
            }
            set
            {
                xPos = value;
            }
        }
        [CategoryAttribute("显示内容"), DescriptionAttribute("坐标值Y")]
        public int YPos
        {
            get
            {
                return yPos;
            }
            set
            {
                yPos = value;
            }
        }
        [CategoryAttribute("显示内容"), DescriptionAttribute("节点的描述内容"),
            EditorAttribute(typeof(PropertyGridRichText), typeof(System.Drawing.Design.UITypeEditor))]
        public string ExpString
        {
            get 
            {
                return expString;
            }
            set
            {
                expString = value;
            }
        }
        [CategoryAttribute("内部公式"), DescriptionAttribute("判定节点时为判定条件的定义公式，其它为输出的内容描述表达式"),
           EditorAttribute(typeof(SetExpFormulaEditor), typeof(UITypeEditor)), TypeConverter(typeof(MasterCom.ES.UI.SetExpFormulaEditor.EditFormulaConverter))]
        public string DecideExp
        {
            get
            {
                if(decideExp==null)
                {
                    return "";
                }
                else
                {
                    return decideExp;
                }
                
            }
            set
            {
                if(value==null)
                {
                    decideExp = "";
                }
                else
                {
                    decideExp = value;
                }
            }
        }
        /// <summary>
        /// 判定分支状态
        /// </summary>
        /// <returns></returns>
        public bool doDecideOperation()
        {
            if(DecideExp.Trim().Equals(""))
            {
                return true;
            }
            else
            {
                bool canCalc = false;
                string formulaStr = parseDecideExp(DecideExp,out canCalc);
                if(canCalc)
                {
                    MathParser mp = new MathParser();
                    return mp.Calculate(formulaStr) == 1;
                }
                else
                {
                    throw (new Exception("表达式中含有了字符串值，不能进行条件判断！"));
                }
            }
        }
        private string parseDescOutput(string descFormula)
        {
            StringBuilder sb = new StringBuilder();
            int pos = 0;
            int lastpos =0;
            while(lastpos<descFormula.Length)
            {
                pos = descFormula.IndexOf("R[",lastpos);
                if(pos>=0)
                {
                    sb.Append(descFormula.Substring(lastpos, pos - lastpos));
                    int endpos = descFormula.IndexOf("]", pos + 1);
                    if(endpos>0)
                    {
                        string rsvParam = descFormula.Substring(pos + 2, endpos - pos - 2);
                        string rsvRet = getFromResv(rsvParam);
                        sb.Append(rsvRet);
                        lastpos = endpos+1;
                    }
                    else
                    {
                        sb.Append(descFormula.Substring(lastpos));
                        lastpos = descFormula.Length;
                    }
                }
                else
                {
                    sb.Append(descFormula.Substring(lastpos));
                    lastpos = descFormula.Length;
                }
            }
            return sb.ToString();
        }

        private string getFromResv(string rsvParam)
        {
            string storeName = "";
            string rsvName = "";
            int spt = rsvParam.IndexOf(",");
            if (spt > 0)////公共池中
            {
                storeName = rsvParam.Substring(0, spt).Trim();
                rsvName = rsvParam.Substring(spt + 1, rsvParam.Length - spt - 1);
            }
            else//流程内预存值
            {
                rsvName = rsvParam.Trim();
            }
            ResvStore store = null;
            if (storeName == "")
            {
                store = ESEngine.GetInstance().CurRunningProc.ReservStore;
            }
            else if(storeName=="公共预存值")
            {
                store = DataGatherProxy.GetInstance().PublicResvStore;
            }
            else
            {
                ProcRoutine proc = null;
                if(ESEngine.GetInstance().inProcModuleDic.TryGetValue(storeName,out proc))
                {
                    store = proc.ReservStore;
                }
            }
            if(store!=null)
            {
                return getValidResv(rsvName, store);
            }
            return "";
        }

        private string getValidResv(string rsvName, ResvStore store)
        {
            int ret = store.ContainName(rsvName);
            if (ret == 1)
            {
                double retdb;
                if (store.TryGetResvValue(rsvName, out retdb))
                {
                    if (rsvName.IndexOf("经度") != -1
                        || rsvName.IndexOf("纬度") != -1
                        || rsvName.IndexOf("longitude") != -1
                        || rsvName.IndexOf("latitude") != -1)
                    {
                        return string.Format("{0:F7}", retdb);
                    }
                    else
                    {
                        return string.Format("{0:F2}", retdb);
                    }
                }
            }
            else if (ret == 2)
            {
                string str;
                if (store.TryGetResvValue(rsvName, out str))
                {
                    return str;
                }
            }
            return "";
        }

        /// <summary>
        /// 执行一个函数
        /// </summary>
        /// <returns></returns>
        private void doFunctionAction()
        {
            if (DecideExp.Trim().Equals(""))
            {
                return;
            }
            bool canCalc = false;
            parseDecideExp(DecideExp, out canCalc);
        }
        /// <summary>
        /// 执行操作
        /// </summary>
        private void doActionOperation()
        {
            if (DecideExp.Trim().Equals(""))
            {
                return;
            }
            int eqpos = DecideExp.IndexOf("=");
            if (eqpos > 0)
            {
                string storeName = "";
                string rsvName = "";
                string leftstr = decideExp.Substring(0, eqpos);
                string rightstr = decideExp.Substring(eqpos + 1, decideExp.Length - eqpos - 1);
                if (leftstr.IndexOf("R[") == 0 && (leftstr.IndexOf("]") == (leftstr.Length - 1)))//左值必须为"R[xxx]"
                {
                    string param = leftstr.Substring(2, leftstr.Length - 3);
                    int spt = param.IndexOf(",");
                    if (spt > 0)//公共池中
                    {
                        storeName = param.Substring(0, spt).Trim();
                        rsvName = param.Substring(spt + 1, param.Length - spt - 1);
                    }
                    else//流程池中
                    {
                        rsvName = param.Trim();
                    }
                }
                else
                {
                    return;//暂不支持左边不是预存值的
                }
                bool canCalc = false;
                string formulaStr = parseDecideExp(rightstr, out canCalc);
                ResvStore store = getResvStore(storeName);

                setValue(rsvName, canCalc, formulaStr, store);
            }
            else
            {
                if(!DecideExp.Trim().Equals(""))
                {
                    bool cc = true;
                    parseDecideExp(DecideExp, out cc);
                }
            }
        }

        private ResvStore getResvStore(string storeName)
        {
            ResvStore store = null;
            if (storeName == "")
            {
                store = ESEngine.GetInstance().CurRunningProc.ReservStore;
            }
            else if (storeName == "公共预存值")
            {
                store = DataGatherProxy.GetInstance().PublicResvStore;
            }
            else
            {
                ProcRoutine proc = null;
                if (ESEngine.GetInstance().inProcModuleDic.TryGetValue(storeName, out proc))
                {
                    store = proc.ReservStore;
                }
            }

            return store;
        }

        private void setValue(string rsvName, bool canCalc, string formulaStr, ResvStore store)
        {
            if (canCalc)
            {
                MathParser mp = new MathParser();
                try
                {
                    decimal decret = mp.Calculate(formulaStr);
                    //赋值操作
                    if (store != null)
                    {
                        store.SetResvValue(rsvName, (double)decret);
                    }
                }
                catch
                {
                    //赋值操作
                    if (store != null)
                    {
                        store.SetResvValue(rsvName, formulaStr);
                    }
                }
            }
            else
            {
                //赋值操作
                if (store != null)
                {
                    store.SetResvValue(rsvName, formulaStr);
                }
            }
        }

        private string parseDecideExp(string expstr,out bool canCalc)
        {
            canCalc = true;
            string exp = expstr;
            int pos = exp.IndexOf("R[");
            while (pos != -1)
            {
                int pos2 = exp.IndexOf("]", pos);
                if (pos2 < pos)
                {
                    throw (new Exception("解析预存值格式错误！"));
                }
                string param = exp.Substring(pos + 2, pos2 - pos - 2);//****,**** of "R[****,****]"
                exp = exp.Remove(pos, pos2 - pos + 1);
                //取值
                int spt = param.IndexOf(",");
                string storeName = "";
                string rsvName = "";
                if (spt > 0)////公共池中
                {
                    storeName = param.Substring(0, spt).Trim();
                    rsvName = param.Substring(spt + 1, param.Length - spt - 1);
                }
                else//流程池
                {
                    rsvName = param.Trim();
                }
                ResvStore store = null;
                store = getStoreData(storeName);
                if (store == null)
                {
                    throw (new Exception("未预期的预测值获取失败,从" + storeName));
                }
                setExpData(ref canCalc, ref exp, ref pos, rsvName, store);
            }
            //
            StringBuilder sb = new StringBuilder();
            canCalc = setCanCalc(canCalc, exp, sb);
            return sb.ToString();
        }

        private static ResvStore getStoreData(string storeName)
        {
            ResvStore store;
            if (storeName == "")
            {
                store = ESEngine.GetInstance().CurRunningProc.ReservStore;
            }
            else if (storeName == "公共预存值")
            {
                store = DataGatherProxy.GetInstance().PublicResvStore;
            }
            else
            {
                ProcRoutine proc = null;
                if (ESEngine.GetInstance().inProcModuleDic.TryGetValue(storeName, out proc))
                {
                    store = proc.ReservStore;
                }
                else
                {
                    throw (new Exception("未预期的模块:" + storeName));
                }
            }

            return store;
        }

        private void setExpData(ref bool canCalc, ref string exp, ref int pos, string rsvName, ResvStore store)
        {
            double valueR;
            string valueRString = "";
            if (store.TryGetResvValue(rsvName, out valueR))
            {
                string valueToAppend = "";
                if (rsvName.IndexOf("经度") != -1
                        || rsvName.IndexOf("纬度") != -1
                        || rsvName.IndexOf("longitude") != -1
                        || rsvName.IndexOf("latitude") != -1)
                {
                    valueToAppend = string.Format("{0:F7}", valueR);
                }
                else
                {
                    valueToAppend = string.Format("{0:F2}", valueR);
                    int vposx = valueToAppend.IndexOf(".00");
                    if (vposx != -1 && valueToAppend.Length - 3 == vposx)
                    {
                        valueToAppend = valueToAppend.Substring(0, vposx);
                    }
                }
                //替换
                exp = exp.Insert(pos, valueToAppend);
                pos = exp.IndexOf("R[");
            }
            else if (store.TryGetResvValue(rsvName, out valueRString))
            {
                exp = exp.Insert(pos, valueRString);
                pos = exp.IndexOf("R[");
                canCalc = false;
            }
            else
            {
                throw (new Exception("未预期的预测值获取失败" + rsvName));
            }
        }

        private bool setCanCalc(bool canCalc, string exp, StringBuilder sb)
        {
            sb.Append("(");
            //
            bool tokenStarted = false;
            int tokenStep = 0;//0数据源 1 函数 2参数
            StringBuilder srcNameSb = new StringBuilder();
            StringBuilder opNameSb = new StringBuilder();
            StringBuilder paramSb = new StringBuilder();
            for (int i = 0; i < exp.Length; i++)
            {
                char ch = exp[i];
                if (tokenStarted)
                {
                    if (ch == ':')
                    {
                        tokenStep = 1;
                    }
                    else if (ch == '[')
                    {
                        tokenStep = 2;
                    }
                    else if (ch == '}')
                    {
                        getcanCalc(out canCalc, sb, out tokenStarted, srcNameSb, opNameSb, paramSb);
                    }
                    else if (ch == ']')
                    {
                        tokenStep = 1;
                    }
                    else
                    {
                        addString(tokenStep, srcNameSb, opNameSb, paramSb, ch);
                    }
                }
                else
                {
                    dealEndData(sb, ref tokenStarted, ref tokenStep, srcNameSb, opNameSb, paramSb, ch);
                }
            }
            //
            sb.Append(")");
            if (!canCalc)
            {
                sb.Remove(sb.Length - 1, 1);
                sb.Remove(0, 1);
            }

            return canCalc;
        }

        private void dealEndData(StringBuilder sb, ref bool tokenStarted, ref int tokenStep, StringBuilder srcNameSb, StringBuilder opNameSb, StringBuilder paramSb, char ch)
        {
            if (ch == '{')
            {
                tokenStarted = true;
                tokenStep = 0;
                srcNameSb.Remove(0, srcNameSb.Length);
                opNameSb.Remove(0, opNameSb.Length);
                paramSb.Remove(0, paramSb.Length);
            }
            else
            {
                sb.Append(ch);
            }
        }

        private void addString(int tokenStep, StringBuilder srcNameSb, StringBuilder opNameSb, StringBuilder paramSb, char ch)
        {
            if (tokenStep == 0)
            {
                srcNameSb.Append(ch);
            }
            else if (tokenStep == 1)
            {
                opNameSb.Append(ch);
            }
            else if (tokenStep == 2)
            {
                paramSb.Append(ch);
            }
        }

        private void getcanCalc(out bool canCalc, StringBuilder sb, out bool tokenStarted, StringBuilder srcNameSb, StringBuilder opNameSb, StringBuilder paramSb)
        {
            tokenStarted = false;
            object outret;
            DataGatherProxy.GetInstance().processFunction(srcNameSb.ToString(), opNameSb.ToString(), paramSb.ToString(), out outret);
            if (outret is double)
            {
                sb.Append((double)outret);
                canCalc = true;
            }
            else
            {
                sb.Append(outret as string);//含有了字符串类型的返回值后就不能再进行运算了
                canCalc = false;
            }
        }

        public NodeEntry GetNextNode(bool value)
        {
            if (value)// path one
            {
                return YesNode;
            }
            else// path second
            {
                return NoNode;
            }
        }

        internal object processNodeEntry(BackgroundWorker bgWorker,bool parseOnlyOne)
        {
            //System.Console.WriteLine(this.expString + "  " + this.decideExp);
            if(_breakPoint)
            {
                return this;
            }
            if (this.Type == NodeType.Condition)
            {
                return dealCondition(bgWorker, parseOnlyOne);
            }
            else if (this.Type == NodeType.Operate)
            {
                return dealOperate(bgWorker, parseOnlyOne);
            }
            else if (this.Type == NodeType.PreProblemType)
            {
                return dealPreProblemType(bgWorker, parseOnlyOne);
            }
            else if (this.Type == NodeType.ReasonDesc)
            {
                return dealReasonDesc(bgWorker, parseOnlyOne);
            }
            else if(this.Type == NodeType.SolveDesc)
            {
                return dealSolveDesc(bgWorker, parseOnlyOne);
            }
            else if (this.Type == NodeType.OtherProc)
            {
                return dealOtherProc(bgWorker, parseOnlyOne);
            }
            else
            {
                return -1;
            }
        }

        private object dealCondition(BackgroundWorker bgWorker, bool parseOnlyOne)
        {
            bgWorker.ReportProgress(1, new OutputUnit(this.ExpString, Color.Black));
            //esform.OutputInfo(this.ExpString, Color.Black);
            bool decide = doDecideOperation();
            if (decide)
            {
                bgWorker.ReportProgress(1, new OutputUnit("Yes\r\n", Color.Red));
                //esform.OutputInfo("Yes\r\n", Color.Red);
                NodeEntry nodeEntry = GetNextNode(decide);
                if (nodeEntry != null)
                {
                    if (parseOnlyOne)
                    {
                        return nodeEntry;
                    }
                    return nodeEntry.processNodeEntry(bgWorker, false);
                }
                else
                {
                    return -1;
                }

            }
            else
            {
                bgWorker.ReportProgress(1, new OutputUnit("No\r\n", Color.Red));
                //esform.OutputInfo("No\r\n", Color.Red);
                NodeEntry nodeEntry = GetNextNode(decide);
                if (nodeEntry != null)
                {
                    if (parseOnlyOne)
                    {
                        return nodeEntry;
                    }
                    return nodeEntry.processNodeEntry(bgWorker, false);
                }
                else
                {
                    return -1;
                }
            }
        }

        private object dealOperate(BackgroundWorker bgWorker, bool parseOnlyOne)
        {
            //to do operation
            doActionOperation();
            bgWorker.ReportProgress(1, new OutputUnit(ExpString + "\r\n", Color.Blue));
            //esform.OutputInfo(ExpString, Color.Blue);
            NodeEntry next = this.YesNode != null ? this.YesNode : this.NoNode;
            if (next != null)
            {
                if (parseOnlyOne)
                {
                    return next;
                }
                return next.processNodeEntry(bgWorker, false);
            }
            else
            {
                return -1;
            }
        }

        private object dealPreProblemType(BackgroundWorker bgWorker, bool parseOnlyOne)
        {
            doFunctionAction();
            bgWorker.ReportProgress(1, new OutputUnit(ExpString + "\r\n", Color.Black, Color.Yellow, 1));
            //esform.OutputInfo(ExpString, Color.Yellow);
            NodeEntry next = this.YesNode != null ? this.YesNode : this.NoNode;
            if (next != null)
            {
                if (parseOnlyOne)
                {
                    return next;
                }
                return next.processNodeEntry(bgWorker, false);
            }
            else
            {
                return 1;//Finish now
            }
        }

        private object dealReasonDesc(BackgroundWorker bgWorker, bool parseOnlyOne)
        {
            string reasonDes = parseDescOutput(DecideExp);
            bgWorker.ReportProgress(1, new OutputUnit(ExpString + ":" + reasonDes + "\r\n", Color.Brown, 2));
            //esform.OutputInfo(DecideExp, Color.Brown);
            NodeEntry next = this.YesNode != null ? this.YesNode : this.NoNode;
            if (next != null)
            {
                if (parseOnlyOne)
                {
                    return next;
                }
                return next.processNodeEntry(bgWorker, false);
            }
            return 1;//Finish now
        }

        private object dealSolveDesc(BackgroundWorker bgWorker, bool parseOnlyOne)
        {
            string solveDesc = parseDescOutput(DecideExp);
            bgWorker.ReportProgress(1, new OutputUnit(ExpString + ":" + solveDesc + "\r\n", Color.Green, 3));
            //esform.OutputInfo(DecideExp, Color.Green);
            NodeEntry next = this.YesNode != null ? this.YesNode : this.NoNode;
            if (next != null)
            {
                if (parseOnlyOne)
                {
                    return next;
                }
                return next.processNodeEntry(bgWorker, false);
            }
            return 1;//Finish now
        }

        private object dealOtherProc(BackgroundWorker bgWorker, bool parseOnlyOne)
        {
            bgWorker.ReportProgress(1, new OutputUnit("调用流程开始:" + ExpString + ":" + DecideExp + "\r\n", Color.Red));
            NodeEntry next = this.YesNode != null ? this.YesNode : this.NoNode;
            string inprocName = DecideExp.Trim();
            ProcRoutine inProc = null;
            if (ESEngine.GetInstance().inProcModuleDic.TryGetValue(inprocName, out inProc))
            {
                ProcRoutine oldRoutine = ESEngine.GetInstance().CurRunningProc;
                ESEngine.GetInstance().RunningStack.Push(new RunningStackRem(oldRoutine, next));
                ESEngine.GetInstance().CurRunningProc = inProc;
                object processObj = inProc.RootNode.processNodeEntry(bgWorker, false);
                if (processObj is NodeEntry)//从断点跳出的
                {
                    bgWorker.ReportProgress(1, new OutputUnit("子模块中断Break " + (processObj as NodeEntry).ExpString + "\r\n ", Color.Red, Color.Yellow));
                    return processObj;
                }
                else
                {
                    ESEngine.GetInstance().RunningStack.Pop();
                    ESEngine.GetInstance().CurRunningProc = oldRoutine;
                }
            }
            else
            {
                bgWorker.ReportProgress(1, new OutputUnit("调用流程出错:" + ExpString + ":" + DecideExp + "未找到流程模块" + inprocName + "\r\n", Color.Red));
            }
            bgWorker.ReportProgress(1, new OutputUnit("调用流程结束:" + ExpString + ":" + DecideExp + "\r\n", Color.Red));

            if (next != null)
            {
                if (parseOnlyOne)
                {
                    ESEngine.GetInstance().CurBrkNode = next;
                }
                else
                {
                    return next.processNodeEntry(bgWorker, false);
                }
            }
            return 1;//Finish now
        }

        public void GotoInProcStartNode(BackgroundWorker bgWorker)
        {
            bgWorker.ReportProgress(1, new OutputUnit("调用流程开始:" + ExpString + ":" + DecideExp + "\r\n", Color.Red));
            NodeEntry next = this.YesNode != null ? this.YesNode : this.NoNode;
            string inprocName = DecideExp.Trim();
            ProcRoutine inProc = null;
            if (ESEngine.GetInstance().inProcModuleDic.TryGetValue(inprocName, out inProc))
            {
                ProcRoutine oldRoutine = ESEngine.GetInstance().CurRunningProc;
                ESEngine.GetInstance().RunningStack.Push(new RunningStackRem(oldRoutine, next));
                ESEngine.GetInstance().CurRunningProc = inProc;
                ESEngine.GetInstance().CurBrkNode = inProc.RootNode;
            }
            else
            {
                bgWorker.ReportProgress(1, new OutputUnit("调用流程出错:" + ExpString + ":" + DecideExp + "未找到流程模块" + inprocName + "\r\n", Color.Red));
            }
        }
        //
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["XPos"] = xPos;
                param["YPos"] = yPos;
                param["Width"] = Width;
                param["Height"] = Height;
                param["Type"] = (int)Type;
                param["ExpString"] = expString;
                param["DecideExp"] = decideExp;
                param["_Idx"] = _Idx;
                if (YesNode != null)
                {
                    int ysIdx = YesNode._Idx;
                    if (!checkPathExist(_Idx, ysIdx))
                    {
                        param["YesNode"] = YesNode.Param;
                    }
                }
                if (NoNode != null)
                {
                    int noIdx = NoNode._Idx;
                    if (!checkPathExist(_Idx, noIdx))
                    {
                        param["NoNode"] = NoNode.Param;
                    }
                }
                return param;
            }
            set
            {
                if (value == null || value.Count == 0)
                {
                    return;
                }
                this.XPos = (int)value["XPos"];
                this.YPos = (int)value["YPos"];
                this.Width = (int)value["Width"];
                this.Height = (int)value["Height"];
                this.Type = (NodeType)((int)value["Type"]);
                this.expString = (string)value["ExpString"];
                this.DecideExp = (string)value["DecideExp"];
                object objIdx;
                if (value.TryGetValue("_Idx", out objIdx))
                {
                    this._Idx = (int)objIdx;
                }
                if (value.ContainsKey("YesNode"))
                {
                    NodeEntry wNode = new NodeEntry();
                    wNode.Param = (Dictionary<string, object>)value["YesNode"];
                    if (wNode._Idx != 0)
                    {
                        NodeEntry nex = null;
                        if (ProcRoutine.TempKeyInRoutineDic.TryGetValue(wNode._Idx, out nex))//已经创建过了该节点
                        {
                            wNode = nex;
                        }
                        else
                        {
                            ProcRoutine.TempKeyInRoutineDic[wNode._Idx] = wNode;
                        }
                        if (!wNode.alreadyHasFather(this._Idx))
                        {
                            wNode._ParentNodes.Add(this);
                        }
                        this.YesNode = wNode;
                    }
                }
                NoNode = setNode(value, NoNode, "NoNode");
            }
        }

        private NodeEntry setNode(Dictionary<string, object> value, NodeEntry node, string nodeName)
        {
            if (value.ContainsKey(nodeName))
            {
                NodeEntry wNode = new NodeEntry();
                wNode.Param = (Dictionary<string, object>)value[nodeName];
                if (wNode._Idx != 0)
                {
                    NodeEntry nex = null;
                    if (ProcRoutine.TempKeyInRoutineDic.TryGetValue(wNode._Idx, out nex))//已经创建过了该节点
                    {
                        wNode = nex;
                    }
                    else
                    {
                        ProcRoutine.TempKeyInRoutineDic[wNode._Idx] = wNode;
                    }
                    if (!wNode.alreadyHasFather(this._Idx))
                    {
                        wNode._ParentNodes.Add(this);
                    }
                    return wNode;
                }
            }
            return node;
        }

        private bool checkPathExist(int fromId, int toId)
        {
            string key = fromId + "->" + toId;
            if (ProcRoutine.PathExistDic.ContainsKey(key))
            {
                return true;
            }
            else
            {
                ProcRoutine.PathExistDic[key] = true;
                return false;
            }
        }

        internal NodeEntry Clone(Dictionary<int, NodeEntry> cloneDic)
        {
            NodeEntry node = new NodeEntry();
            node.Type = this.Type;
            node.XPos = this.XPos;
            node.YPos = this.YPos;
            node.Width = this.Width;
            node.Height = this.Height;
            node.ExpString = this.ExpString;
            node.decideExp = this.decideExp;
            node._Idx = this._Idx;
            NodeEntry nex = null;
            if(cloneDic.TryGetValue(this._Idx,out nex))//已经创建过
            {
                node = nex;
            }
            else
            {
                cloneDic[node._Idx] = node;
            }
            if(this.YesNode!=null)
            {
                node.YesNode = YesNode.Clone(cloneDic);
                node.YesNode._ParentNodes.Add(node);
            }
            if (this.NoNode != null)
            {
                node.NoNode = NoNode.Clone(cloneDic);
                node.NoNode._ParentNodes.Add(node);
            }
            return node;
        }
        internal bool alreadyHasFather(int idx)
        {
            foreach (NodeEntry node in _ParentNodes)
            {
                if (node._Idx == idx)
                {
                    return true;
                }
            }
            return false;
        }

        internal void moveOffset(int xoffset, int yoffset)
        {
            if (this._Idx > 0)
            {
                return;
            }
            this._Idx = 1;
            this.XPos += xoffset;
            this.YPos += yoffset;
            if(this.YesNode!=null)
            {
                this.YesNode.moveOffset(xoffset, yoffset);
            }
            if (this.NoNode != null)
            {
                this.NoNode.moveOffset(xoffset, yoffset);
            }
        }
        internal Point GetMostLeftTopPoint()
        {
            Point pt = new Point(this.XPos, this.YPos);
            if(YesNode!=null)
            {
                Point v = YesNode.GetMostLeftTopPoint();
                if(v.X<pt.X)
                {
                    pt.X = v.X;
                }
                if(v.Y<pt.Y)
                {
                    pt.Y = v.Y;
                }
            }
            if (NoNode != null)
            {
                Point v = NoNode.GetMostLeftTopPoint();
                if (v.X < pt.X)
                {
                    pt.X = v.X;
                }
                if (v.Y < pt.Y)
                {
                    pt.Y = v.Y;
                }
            }
            return pt;
        }
        internal Size GetMaxSize()
        {
            Size sz = new Size(XPos + 200, YPos + 200);
            if(YesNode!=null)
            {
                Size ySize = YesNode.GetMaxSize();
                if(ySize.Width>sz.Width)
                {
                    sz.Width = ySize.Width;
                }
                if (ySize.Height > sz.Height)
                {
                    sz.Height = ySize.Height;
                }
            }
            if (NoNode != null)
            {
                Size nSize = NoNode.GetMaxSize();
                if (nSize.Width > sz.Width)
                {
                    sz.Width = nSize.Width;
                }
                if (nSize.Height > sz.Height)
                {
                    sz.Height = nSize.Height;
                }
            }
            return sz;
        }

        internal void ClearIdx()
        {
            this._Idx = 0;
            if(YesNode!=null)
            {
                YesNode.ClearIdx();
            }
            if(NoNode!=null)
            {
                NoNode.ClearIdx();
            }
        }
        internal void ClearAllBreaks()
        {
            this._breakPoint = false;
            if (YesNode != null)
            {
                YesNode.ClearAllBreaks();
            }
            if (NoNode != null)
            {
                NoNode.ClearAllBreaks();
            }
        }
        internal int ResetIdx(int idx)
        {
            int v = idx;
            if(this._Idx==0)
            {
                this._Idx = v;
                v++;
            }
            if(YesNode!=null)
            {
                v = YesNode.ResetIdx(v);
            }
            if (NoNode != null)
            {
                v = NoNode.ResetIdx(v);
            }
            return v;
        }
        //更新其子节点的Offsets
        internal void UpdateChildrenOffsets(int baseX,int baseY)
        {
            this._xOffset = this.XPos - baseX;
            this._yOffset = this.YPos - baseY;
            if(this.YesNode!=null)
            {
                this.YesNode.UpdateChildrenOffsets(baseX, baseY);
            }
            if (this.NoNode != null)
            {
                this.NoNode.UpdateChildrenOffsets(baseX, baseY);
            }
        }
        //用offsets更新位置
        internal void UpdateChildrenByOffsets(int newBaseX, int newBaseY)
        {
            this.xPos = newBaseX + this._xOffset;
            this.YPos = newBaseY + this._yOffset;
            if (this.YesNode != null)
            {
                this.YesNode.UpdateChildrenByOffsets(newBaseX, newBaseY);
            }
            if (this.NoNode != null)
            {
                this.NoNode.UpdateChildrenByOffsets(newBaseX, newBaseY);
            }
        }

        internal void FindTextWith(Dictionary<NodeEntry, FindResultType> findMatchNodes, string stext)
        {
            if (this.expString != null && this.expString.IndexOf(stext) != -1)
            {
                findMatchNodes[this] = FindResultType.Enum_Find_ExpDesc;
            }
            if (this.decideExp != null && this.decideExp.IndexOf(stext) != -1)
            {
                findMatchNodes[this] = FindResultType.Enum_Find_DecideExp;
            }
            if (YesNode != null)
            {
                YesNode.FindTextWith(findMatchNodes, stext);
            }
            if (NoNode != null)
            {
                NoNode.FindTextWith(findMatchNodes, stext);
            }
        }
    }
}
