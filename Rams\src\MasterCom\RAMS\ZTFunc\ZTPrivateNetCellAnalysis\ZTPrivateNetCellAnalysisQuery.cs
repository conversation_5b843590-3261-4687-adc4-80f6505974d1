﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTPrivateNetCellAnalysisQuery : DIYAnalyseByFileBackgroundBase
    {
        /// <summary>
        /// 界面设置的条件
        /// </summary>
        protected PrivateNetCellAnalysisCondition privateNetCondition = new PrivateNetCellAnalysisCondition();

        protected Dictionary<string, PrivateNetCellAnalysisResult> resultDic;

        #region 单例
        protected static readonly object lockObj = new object();
        private static ZTPrivateNetCellAnalysisQuery intance = null;
        public static ZTPrivateNetCellAnalysisQuery GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTPrivateNetCellAnalysisQuery();
                    }
                }
            }
            return intance;
        }
        #endregion

        protected ZTPrivateNetCellAnalysisQuery()
            : base(MainModel.GetInstance())
        {
            if (intance != null)
            {
                return;
            }
            ServiceTypes.Clear();
            carrierID = CarrierType.ChinaMobile;
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
        }

        public override string Name
        {
            get { return "专网小区统计"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22123, this.Name);
        }

        protected override void query()
        {
            if (MainModel.IsBackground && !MainModel.BackgroundStarted)
            {
                return;
            }
            if (!getCondition())
            {
                return;
            }
            getReadyBeforeQuery();
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }

            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;

            queryFileToAnalyse();
            WaitBox.CanCancel = true;
            WaitBox.Show("开始分析文件...", analyseFiles);
            DoWaitBoxAfterGetResults();
        }

        protected override bool getCondition()
        {
            //获取专网小区 
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            ZTPrivateNetCellAnalysisDlg dlg = ZTPrivateNetCellAnalysisDlg.GetInstance(privateNetCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                privateNetCondition = dlg.GetConditon();
                return true;
            }
            privateNetCondition = dlg.GetConditon();
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultDic = new Dictionary<string, PrivateNetCellAnalysisResult>();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    dealWithData(fileDataManager.TestPoints);
                }
            }
            catch (Exception e)
            {
                log.Error(e.Message + Environment.NewLine + e.StackTrace);
            }
        }

        /// <summary>
        /// 按采样点对数据进行处理
        /// </summary>
        /// <param name="testPoints">文件中的所有采样点</param>
        private void dealWithData(List<TestPoint> testPoints)
        {
            for (int i = 0; i < testPoints.Count; i++)
            {
                TestPoint tp = testPoints[i];
                int? tac = (int?)(ushort?)tp["lte_TAC"];
                int? eci = (int?)tp["lte_ECI"];
                //TAC,ECI,经纬度任一为0的点舍弃
                if (tac != null && eci != null && tp.Longitude != 0 && tp.Latitude != 0)
                {
                    //判断采样点属于哪个地市
                    string districtName = getDistrictName(tp);
                    //不在图层范围内的点舍弃
                    if (!string.IsNullOrEmpty(districtName))
                    {
                        setPrivateNetCellData(tp, tac, eci, districtName);
                    }
                }
            }
            addFinalData();
        }

        private void addFinalData()
        {
            //结束时还处于专网状态添加路段信息
            foreach (PrivateNetCellAnalysisResult item in resultDic.Values)
            {
                if (item.LastPrivateNetTestPoint != null)
                {
                    item.InPrivateNetRoads.Add(item.CurInPrivateNetRoad);
                    item.LastTestPoint = null;
                    item.LastPrivateNetTestPoint = null;
                    item.CurInPrivateNetRoad = new PrivateNetCellAnalysisResult.InPrivateNetRoad();
                }
            }
        }

        private void setPrivateNetCellData(TestPoint tp, int? tac, int? eci, string districtName)
        {
            //根据不同的地市处理结果
            PrivateNetCellAnalysisResult result;
            if (!resultDic.TryGetValue(districtName, out result))
            {
                result = new PrivateNetCellAnalysisResult();
                result.DistrictName = districtName;
                resultDic.Add(districtName, result);
            }
            //添加区域内的总时长
            if (result.LastTestPoint != null)
            {
                double timeGap = Math.Abs(1.0 * (result.LastTestPoint.lTimeWithMillsecond - tp.lTimeWithMillsecond) / 1000);
                result.TotalDuration += timeGap;
            }
            result.LastTestPoint = tp;
            double inPrivateNetDuration;
            double inPrivateNetDistance;
            //是专网小区则不断累加持续时间和持续距离
            bool isPrivateNetCell = judgePrivateNetCell((int)tac, (int)eci, tp, result.LastPrivateNetTestPoint, out inPrivateNetDuration, out inPrivateNetDistance);
            if (isPrivateNetCell)
            {
                result.LastPrivateNetTestPoint = tp;
                result.InPrivateNetDuration += inPrivateNetDuration;
                result.InPrivateNetDistance += inPrivateNetDistance;
                result.CurInPrivateNetRoad.InPrivateNetRoadTPs.Add(tp);
                result.CurInPrivateNetRoad.InPrivateNetRoadDistance += inPrivateNetDistance;
            }
            else if (result.LastPrivateNetTestPoint != null)
            {
                //不是专网小区则保存路段
                result.InPrivateNetRoads.Add(result.CurInPrivateNetRoad);
                result.LastTestPoint = null;
                result.LastPrivateNetTestPoint = null;
                result.CurInPrivateNetRoad = new PrivateNetCellAnalysisResult.InPrivateNetRoad();
            }
        }

        private string getDistrictName(TestPoint tp)
        {
            string districtName = "";
            foreach (ResvRegion region in privateNetCondition.ResvRegionList)
            {
                if (region.GeoOp.CheckPointInRegion(tp.Longitude, tp.Latitude))
                {
                    districtName = region.RegionName;
                    break;
                }
            }
            return districtName;
        }

        /// <summary>
        /// 判断是否为专网小区(如果占用专网,则还要添加出网信息到占用专网合集中)
        /// </summary>
        /// <param name="tac">当前采样点tac</param>
        /// <param name="eci">当前采样点eci</param>
        /// <param name="testPoint">当前需判断的采样点</param>
        /// <param name="lastInPrivateNetTP">上个出网采样点</param>
        /// <param name="inPrivateNetDuration">占用专网时长</param>
        /// <param name="inPrivateNetDistance">占用专网距离</param>
        /// <param name="tpOutPrivateNetLists">出网采样点合集</param>
        /// <returns>true为专网小区</returns>
        public bool judgePrivateNetCell(int tac, int eci, TestPoint testPoint, TestPoint lastInPrivateNetTP, out double inPrivateNetDuration, out double inPrivateNetDistance)
        {
            foreach (PrivateNetCell privateNetCell in privateNetCondition.PrivateNetCell)
            {
                if (privateNetCell.TAC == tac && privateNetCell.ECI == eci)
                {
                    addOutPrivateNetInfo(testPoint, lastInPrivateNetTP, out inPrivateNetDuration, out inPrivateNetDistance);
                    return true;
                }
            }
            inPrivateNetDuration = 0;
            inPrivateNetDistance = 0;
            return false;
        }

        /// <summary>
        /// 添加占用专网信息(累加占用专网时间,距离,添加采样点和对应主服小区)
        /// </summary>
        /// <param name="testPoint">当前占用专网采样点</param>
        /// <param name="lastInPrivateNetTP">上个出网采样点</param>
        /// <param name="inPrivateNetDuration">占用专网时长</param>
        /// <param name="inPrivateNetDistance">占用专网距离</param>
        /// <param name="tpInPrivateNetLists">占用专网采样点合集</param>
        private void addOutPrivateNetInfo(TestPoint testPoint, TestPoint lastInPrivateNetTP, out double inPrivateNetDuration, out double inPrivateNetDistance)
        {
            if (lastInPrivateNetTP != null)
            {
                long lastInPrivateNetTime = lastInPrivateNetTP.lTimeWithMillsecond;
                double timeGap = Math.Abs(1.0 * (lastInPrivateNetTime - testPoint.lTimeWithMillsecond) / 1000);
                inPrivateNetDuration = timeGap;

                double distance = testPoint.Distance2(lastInPrivateNetTP);
                inPrivateNetDistance = distance;
            }
            else
            {
                inPrivateNetDuration = 0;
                inPrivateNetDistance = 0;
            }
        }

        #region 导出
        protected override void DoWaitBoxAfterGetResults()
        {
            addTotalInfo();
            exportToExcel();
        }

        private void addTotalInfo()
        {
            PrivateNetCellAnalysisResult totalResult = new PrivateNetCellAnalysisResult();
            totalResult.DistrictName = "汇总";

            foreach (var item in resultDic.Values)
            {
                totalResult.InPrivateNetDistance += item.InPrivateNetDistance;
                totalResult.TotalDuration += item.TotalDuration;
                totalResult.InPrivateNetDuration += item.InPrivateNetDuration;
                totalResult.InPrivateNetRoads.AddRange(item.InPrivateNetRoads);
            }

            resultDic.Add(totalResult.DistrictName, totalResult);
        }

        private void exportToExcel()
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.cellValues.Add("地市");
            row.cellValues.Add("空闲态出专网路段数");
            row.cellValues.Add("空闲态出专网路段里程（公里）");
            row.cellValues.Add("LTE专网时长占比");
            rows.Add(row);

            foreach (var result in resultDic.Values)
            {
                row = new NPOIRow();
                row.cellValues.Add(result.DistrictName);
                row.cellValues.Add(result.GetVaildPrivateNetRoadCount(100));
                row.cellValues.Add(result.InPrivateNetDistanceKM);
                row.cellValues.Add(result.InPrivateNetDurationRate);
                rows.Add(row);
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }
        #endregion
    }

    public class PrivateNetCellAnalysisResult
    {
        /// <summary>
        /// 地市名
        /// </summary>
        public string DistrictName { get; set; }
        
        /// <summary>
        /// 占用专网总里程(米)
        /// </summary>
        public double InPrivateNetDistance { get; set; }

        /// <summary>
        /// 占用专网总里程(公里)
        /// </summary>
        public double InPrivateNetDistanceKM
        {
            get { return Math.Round(InPrivateNetDistance / 1000, 2); }
        }
        
        /// <summary>
        /// 占用专网总时长(秒)
        /// </summary>
        public double InPrivateNetDuration { get; set; }
        
        /// <summary>
        /// 占用专网路段合集
        /// </summary>
        public List<InPrivateNetRoad> InPrivateNetRoads { get; set; } = new List<InPrivateNetRoad>();

        /// <summary>
        /// 获取有效的占用专网路段数
        /// </summary>
        /// <param name="distanceLimit"></param>
        /// <returns></returns>
        public int GetVaildPrivateNetRoadCount(double distanceLimit)
        {
            int count = 0;
            foreach (var road in InPrivateNetRoads)
            {
                if (road.InPrivateNetRoadDistance >= distanceLimit)
                {
                    count++;
                }
            }
            return count;
        }

        /// <summary>
        /// 专网时长占比
        /// </summary>
        public string InPrivateNetDurationRate
        {
            get { return Math.Round(InPrivateNetDuration * 100 / TotalDuration, 2).ToString() + "%"; }
        }
        /// <summary>
        /// 总时长
        /// </summary>
        public double TotalDuration { get; set; }
        /// <summary>
        /// 上一个采样点(用于计算总时长)
        /// </summary>
        public TestPoint LastTestPoint { get; set; }
        /// <summary>
        /// 上一个主服为专网的采样点(用于计算专网时长)
        /// </summary>
        public TestPoint LastPrivateNetTestPoint { get; set; }
        /// <summary>
        /// 当前专网路段(结束路段时保存并清空)
        /// </summary>
        public InPrivateNetRoad CurInPrivateNetRoad { get; set; } = new InPrivateNetRoad();

        /// <summary>
        /// 占用专网路段类
        /// </summary>
        public class InPrivateNetRoad
        {
            /// <summary>
            /// 路段占用专网采样点
            /// </summary>
            public List<TestPoint> InPrivateNetRoadTPs { get; set; }
            
            /// <summary>
            /// 路段占用专网里程
            /// </summary>
            public double InPrivateNetRoadDistance { get; set; }
        }
    }
}
