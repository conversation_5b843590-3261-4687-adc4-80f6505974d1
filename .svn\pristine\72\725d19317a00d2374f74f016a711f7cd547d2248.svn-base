﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CityFileStateForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExp2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.dateEditDateEnd = new DevExpress.XtraEditors.DateEdit();
            this.dateEditDateStart = new DevExpress.XtraEditors.DateEdit();
            this.simpleButtonSearch = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControlInfo = new DevExpress.XtraEditors.LabelControl();
            this.listViewDateCityInfo = new System.Windows.Forms.ListView();
            this.columnHeaderDate = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCity = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderType = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderFileCount = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderExistCount = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderExistPercent = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderNotExistCount = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderNotExistPercent = new System.Windows.Forms.ColumnHeader();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.listViewFileInfo = new System.Windows.Forms.ListView();
            this.columnHeaderDate_File = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCity_File = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderFileName = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderBeginTimeString = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderEndTimeString = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderDesc = new System.Windows.Forms.ColumnHeader();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.listViewNotExist = new System.Windows.Forms.ListView();
            this.columnHeaderDate_Not = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCity_Not = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderFileName_Not = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderStartTime_Not = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderEndTime_Not = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderStatus_Not = new System.Windows.Forms.ColumnHeader();
            this.spinEditNotExistPercent = new DevExpress.XtraEditors.SpinEdit();
            this.pnlQuery = new System.Windows.Forms.Panel();
            this.tabCtrlNavi = new DevExpress.XtraTab.XtraTabControl();
            this.pageBasic = new DevExpress.XtraTab.XtraTabPage();
            this.pageTestPlan = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridTestPlan = new DevExpress.XtraGrid.GridControl();
            this.viewTestPlan = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tabPlanDetail = new DevExpress.XtraTab.XtraTabControl();
            this.pagePlanExist = new DevExpress.XtraTab.XtraTabPage();
            this.gridPlanExist = new DevExpress.XtraGrid.GridControl();
            this.viewPlanExist = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.pageNotExist = new DevExpress.XtraTab.XtraTabPage();
            this.gridPlanNotExist = new DevExpress.XtraGrid.GridControl();
            this.viewPlanNotExist = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditDateEnd.Properties.VistaTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditDateEnd.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditDateStart.Properties.VistaTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditDateStart.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditNotExistPercent.Properties)).BeginInit();
            this.pnlQuery.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabCtrlNavi)).BeginInit();
            this.tabCtrlNavi.SuspendLayout();
            this.pageBasic.SuspendLayout();
            this.pageTestPlan.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridTestPlan)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewTestPlan)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabPlanDetail)).BeginInit();
            this.tabPlanDetail.SuspendLayout();
            this.pagePlanExist.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridPlanExist)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewPlanExist)).BeginInit();
            this.pageNotExist.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridPlanNotExist)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewPlanNotExist)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExp2Xls});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(139, 26);
            // 
            // miExp2Xls
            // 
            this.miExp2Xls.Name = "miExp2Xls";
            this.miExp2Xls.Size = new System.Drawing.Size(138, 22);
            this.miExp2Xls.Text = "导出Excel...";
            this.miExp2Xls.Click += new System.EventHandler(this.miExp2Xls_Click);
            // 
            // dateEditDateEnd
            // 
            this.dateEditDateEnd.EditValue = null;
            this.dateEditDateEnd.Location = new System.Drawing.Point(235, 13);
            this.dateEditDateEnd.Name = "dateEditDateEnd";
            this.dateEditDateEnd.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.dateEditDateEnd.Properties.VistaTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dateEditDateEnd.Size = new System.Drawing.Size(100, 21);
            this.dateEditDateEnd.TabIndex = 5;
            // 
            // dateEditDateStart
            // 
            this.dateEditDateStart.EditValue = null;
            this.dateEditDateStart.Location = new System.Drawing.Point(63, 13);
            this.dateEditDateStart.Name = "dateEditDateStart";
            this.dateEditDateStart.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.dateEditDateStart.Properties.VistaTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dateEditDateStart.Size = new System.Drawing.Size(100, 21);
            this.dateEditDateStart.TabIndex = 5;
            // 
            // simpleButtonSearch
            // 
            this.simpleButtonSearch.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonSearch.Appearance.Options.UseFont = true;
            this.simpleButtonSearch.Location = new System.Drawing.Point(619, 10);
            this.simpleButtonSearch.Name = "simpleButtonSearch";
            this.simpleButtonSearch.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonSearch.TabIndex = 4;
            this.simpleButtonSearch.Text = "查询";
            this.simpleButtonSearch.Click += new System.EventHandler(this.simpleButtonSearch_Click);
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(516, 18);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(6, 12);
            this.labelControl3.TabIndex = 2;
            this.labelControl3.Text = "%";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(366, 17);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(78, 12);
            this.labelControl5.TabIndex = 2;
            this.labelControl5.Text = "未推送比例 ≥";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(176, 17);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(60, 12);
            this.labelControl2.TabIndex = 0;
            this.labelControl2.Text = "结束时间：";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(3, 17);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(60, 12);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "开始时间：";
            // 
            // labelControlInfo
            // 
            this.labelControlInfo.Appearance.Font = new System.Drawing.Font("Tahoma", 33F);
            this.labelControlInfo.Appearance.ForeColor = System.Drawing.Color.Red;
            this.labelControlInfo.Appearance.Options.UseFont = true;
            this.labelControlInfo.Appearance.Options.UseForeColor = true;
            this.labelControlInfo.Location = new System.Drawing.Point(173, 59);
            this.labelControlInfo.Name = "labelControlInfo";
            this.labelControlInfo.Size = new System.Drawing.Size(396, 53);
            this.labelControlInfo.TabIndex = 6;
            this.labelControlInfo.Text = "所选时间段没有数据";
            this.labelControlInfo.Visible = false;
            // 
            // listViewDateCityInfo
            // 
            this.listViewDateCityInfo.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderDate,
            this.columnHeaderCity,
            this.columnHeaderType,
            this.columnHeaderFileCount,
            this.columnHeaderExistCount,
            this.columnHeaderExistPercent,
            this.columnHeaderNotExistCount,
            this.columnHeaderNotExistPercent});
            this.listViewDateCityInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewDateCityInfo.FullRowSelect = true;
            this.listViewDateCityInfo.GridLines = true;
            this.listViewDateCityInfo.Location = new System.Drawing.Point(0, 0);
            this.listViewDateCityInfo.Name = "listViewDateCityInfo";
            this.listViewDateCityInfo.Size = new System.Drawing.Size(1087, 238);
            this.listViewDateCityInfo.TabIndex = 7;
            this.listViewDateCityInfo.UseCompatibleStateImageBehavior = false;
            this.listViewDateCityInfo.View = System.Windows.Forms.View.Details;
            this.listViewDateCityInfo.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewDateCityInfo_MouseDoubleClick);
            // 
            // columnHeaderDate
            // 
            this.columnHeaderDate.Text = "日期";
            this.columnHeaderDate.Width = 90;
            // 
            // columnHeaderCity
            // 
            this.columnHeaderCity.Text = "城市";
            // 
            // columnHeaderType
            // 
            this.columnHeaderType.Text = "类型";
            this.columnHeaderType.Width = 80;
            // 
            // columnHeaderFileCount
            // 
            this.columnHeaderFileCount.Text = "文件数量";
            this.columnHeaderFileCount.Width = 90;
            // 
            // columnHeaderExistCount
            // 
            this.columnHeaderExistCount.Text = "已推送数量";
            this.columnHeaderExistCount.Width = 90;
            // 
            // columnHeaderExistPercent
            // 
            this.columnHeaderExistPercent.Text = "已推送比例";
            this.columnHeaderExistPercent.Width = 90;
            // 
            // columnHeaderNotExistCount
            // 
            this.columnHeaderNotExistCount.Text = "未推送数量";
            this.columnHeaderNotExistCount.Width = 90;
            // 
            // columnHeaderNotExistPercent
            // 
            this.columnHeaderNotExistPercent.Text = "未推送比例";
            this.columnHeaderNotExistPercent.Width = 90;
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.ContextMenuStrip = this.contextMenuStrip1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.labelControlInfo);
            this.splitContainerControl1.Panel1.Controls.Add(this.listViewDateCityInfo);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.xtraTabControl1);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1087, 474);
            this.splitContainerControl1.SplitterPosition = 238;
            this.splitContainerControl1.TabIndex = 8;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(1087, 230);
            this.xtraTabControl1.TabIndex = 9;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.listViewFileInfo);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(1080, 200);
            this.xtraTabPage1.Text = "已推送";
            // 
            // listViewFileInfo
            // 
            this.listViewFileInfo.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderDate_File,
            this.columnHeaderCity_File,
            this.columnHeaderFileName,
            this.columnHeaderBeginTimeString,
            this.columnHeaderEndTimeString,
            this.columnHeaderDesc});
            this.listViewFileInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewFileInfo.FullRowSelect = true;
            this.listViewFileInfo.GridLines = true;
            this.listViewFileInfo.Location = new System.Drawing.Point(0, 0);
            this.listViewFileInfo.Name = "listViewFileInfo";
            this.listViewFileInfo.Size = new System.Drawing.Size(1080, 200);
            this.listViewFileInfo.TabIndex = 8;
            this.listViewFileInfo.UseCompatibleStateImageBehavior = false;
            this.listViewFileInfo.View = System.Windows.Forms.View.Details;
            // 
            // columnHeaderDate_File
            // 
            this.columnHeaderDate_File.Text = "日期";
            this.columnHeaderDate_File.Width = 90;
            // 
            // columnHeaderCity_File
            // 
            this.columnHeaderCity_File.Text = "城市";
            // 
            // columnHeaderFileName
            // 
            this.columnHeaderFileName.Text = "文件名称";
            this.columnHeaderFileName.Width = 250;
            // 
            // columnHeaderBeginTimeString
            // 
            this.columnHeaderBeginTimeString.Text = "开始时间";
            this.columnHeaderBeginTimeString.Width = 130;
            // 
            // columnHeaderEndTimeString
            // 
            this.columnHeaderEndTimeString.Text = "结束时间";
            this.columnHeaderEndTimeString.Width = 130;
            // 
            // columnHeaderDesc
            // 
            this.columnHeaderDesc.Text = "状态";
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.listViewNotExist);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(1003, 200);
            this.xtraTabPage2.Text = "未推送";
            // 
            // listViewNotExist
            // 
            this.listViewNotExist.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderDate_Not,
            this.columnHeaderCity_Not,
            this.columnHeaderFileName_Not,
            this.columnHeaderStartTime_Not,
            this.columnHeaderEndTime_Not,
            this.columnHeaderStatus_Not});
            this.listViewNotExist.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewNotExist.FullRowSelect = true;
            this.listViewNotExist.GridLines = true;
            this.listViewNotExist.Location = new System.Drawing.Point(0, 0);
            this.listViewNotExist.Name = "listViewNotExist";
            this.listViewNotExist.Size = new System.Drawing.Size(1003, 200);
            this.listViewNotExist.TabIndex = 9;
            this.listViewNotExist.UseCompatibleStateImageBehavior = false;
            this.listViewNotExist.View = System.Windows.Forms.View.Details;
            // 
            // columnHeaderDate_Not
            // 
            this.columnHeaderDate_Not.Text = "日期";
            this.columnHeaderDate_Not.Width = 90;
            // 
            // columnHeaderCity_Not
            // 
            this.columnHeaderCity_Not.Text = "城市";
            // 
            // columnHeaderFileName_Not
            // 
            this.columnHeaderFileName_Not.Text = "文件名称";
            this.columnHeaderFileName_Not.Width = 250;
            // 
            // columnHeaderStartTime_Not
            // 
            this.columnHeaderStartTime_Not.Text = "开始时间";
            this.columnHeaderStartTime_Not.Width = 130;
            // 
            // columnHeaderEndTime_Not
            // 
            this.columnHeaderEndTime_Not.Text = "结束时间";
            this.columnHeaderEndTime_Not.Width = 130;
            // 
            // columnHeaderStatus_Not
            // 
            this.columnHeaderStatus_Not.Text = "状态";
            // 
            // spinEditNotExistPercent
            // 
            this.spinEditNotExistPercent.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditNotExistPercent.Location = new System.Drawing.Point(447, 12);
            this.spinEditNotExistPercent.Name = "spinEditNotExistPercent";
            this.spinEditNotExistPercent.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditNotExistPercent.Properties.Mask.EditMask = "f2";
            this.spinEditNotExistPercent.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditNotExistPercent.Size = new System.Drawing.Size(63, 21);
            this.spinEditNotExistPercent.TabIndex = 9;
            // 
            // pnlQuery
            // 
            this.pnlQuery.Controls.Add(this.labelControl1);
            this.pnlQuery.Controls.Add(this.labelControl2);
            this.pnlQuery.Controls.Add(this.spinEditNotExistPercent);
            this.pnlQuery.Controls.Add(this.labelControl5);
            this.pnlQuery.Controls.Add(this.dateEditDateEnd);
            this.pnlQuery.Controls.Add(this.simpleButtonSearch);
            this.pnlQuery.Controls.Add(this.dateEditDateStart);
            this.pnlQuery.Controls.Add(this.labelControl3);
            this.pnlQuery.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlQuery.Location = new System.Drawing.Point(0, 0);
            this.pnlQuery.Name = "pnlQuery";
            this.pnlQuery.Size = new System.Drawing.Size(1094, 44);
            this.pnlQuery.TabIndex = 10;
            // 
            // tabCtrlNavi
            // 
            this.tabCtrlNavi.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabCtrlNavi.Location = new System.Drawing.Point(0, 44);
            this.tabCtrlNavi.Name = "tabCtrlNavi";
            this.tabCtrlNavi.SelectedTabPage = this.pageBasic;
            this.tabCtrlNavi.Size = new System.Drawing.Size(1094, 504);
            this.tabCtrlNavi.TabIndex = 11;
            this.tabCtrlNavi.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.pageBasic,
            this.pageTestPlan});
            // 
            // pageBasic
            // 
            this.pageBasic.Controls.Add(this.splitContainerControl1);
            this.pageBasic.Name = "pageBasic";
            this.pageBasic.Size = new System.Drawing.Size(1087, 474);
            this.pageBasic.Text = "推送信息";
            // 
            // pageTestPlan
            // 
            this.pageTestPlan.Controls.Add(this.splitContainerControl2);
            this.pageTestPlan.Name = "pageTestPlan";
            this.pageTestPlan.PageVisible = false;
            this.pageTestPlan.Size = new System.Drawing.Size(1087, 474);
            this.pageTestPlan.Text = "测试计划文件推送情况";
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.Horizontal = false;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.gridTestPlan);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.tabPlanDetail);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(1087, 474);
            this.splitContainerControl2.SplitterPosition = 204;
            this.splitContainerControl2.TabIndex = 2;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // gridTestPlan
            // 
            this.gridTestPlan.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridTestPlan.Location = new System.Drawing.Point(0, 0);
            this.gridTestPlan.MainView = this.viewTestPlan;
            this.gridTestPlan.Name = "gridTestPlan";
            this.gridTestPlan.Size = new System.Drawing.Size(1087, 204);
            this.gridTestPlan.TabIndex = 0;
            this.gridTestPlan.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.viewTestPlan});
            // 
            // viewTestPlan
            // 
            this.viewTestPlan.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11});
            this.viewTestPlan.GridControl = this.gridTestPlan;
            this.viewTestPlan.Name = "viewTestPlan";
            this.viewTestPlan.OptionsBehavior.Editable = false;
            this.viewTestPlan.OptionsDetail.ShowDetailTabs = false;
            this.viewTestPlan.OptionsView.ColumnAutoWidth = false;
            this.viewTestPlan.OptionsView.ShowDetailButtons = false;
            this.viewTestPlan.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.viewTestPlan_FocusedRowChanged);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "开始时间";
            this.gridColumn1.DisplayFormat.FormatString = "yyyy/MM/dd HH:mm:ss";
            this.gridColumn1.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn1.FieldName = "SDateTime";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "结束时间";
            this.gridColumn2.DisplayFormat.FormatString = "yyyy/MM/dd HH:mm:ss";
            this.gridColumn2.FieldName = "EDateTime";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "地市";
            this.gridColumn3.FieldName = "CityName";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            this.gridColumn3.Width = 68;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "语音测试设备号";
            this.gridColumn4.FieldName = "VoiceDevice";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "数据测试设备号";
            this.gridColumn5.FieldName = "DataDevice";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "测试项目";
            this.gridColumn6.FieldName = "Project";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "测试网格";
            this.gridColumn7.FieldName = "GridName";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            this.gridColumn7.Width = 88;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "文件数量";
            this.gridColumn8.FieldName = "ShouldExistNum";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 7;
            this.gridColumn8.Width = 88;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "已推送数量";
            this.gridColumn9.FieldName = "ExistNum";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 8;
            this.gridColumn9.Width = 88;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "未推送数量";
            this.gridColumn10.FieldName = "NotExistNum";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 9;
            this.gridColumn10.Width = 88;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "未推送比例";
            this.gridColumn11.FieldName = "NotExistRate";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 10;
            this.gridColumn11.Width = 90;
            // 
            // tabPlanDetail
            // 
            this.tabPlanDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabPlanDetail.Location = new System.Drawing.Point(0, 0);
            this.tabPlanDetail.Name = "tabPlanDetail";
            this.tabPlanDetail.SelectedTabPage = this.pagePlanExist;
            this.tabPlanDetail.Size = new System.Drawing.Size(1087, 264);
            this.tabPlanDetail.TabIndex = 1;
            this.tabPlanDetail.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.pagePlanExist,
            this.pageNotExist});
            // 
            // pagePlanExist
            // 
            this.pagePlanExist.Controls.Add(this.gridPlanExist);
            this.pagePlanExist.Name = "pagePlanExist";
            this.pagePlanExist.Size = new System.Drawing.Size(1080, 234);
            this.pagePlanExist.Text = "已推送";
            // 
            // gridPlanExist
            // 
            this.gridPlanExist.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridPlanExist.Location = new System.Drawing.Point(0, 0);
            this.gridPlanExist.MainView = this.viewPlanExist;
            this.gridPlanExist.Name = "gridPlanExist";
            this.gridPlanExist.ShowOnlyPredefinedDetails = true;
            this.gridPlanExist.Size = new System.Drawing.Size(1080, 234);
            this.gridPlanExist.TabIndex = 1;
            this.gridPlanExist.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.viewPlanExist});
            // 
            // viewPlanExist
            // 
            this.viewPlanExist.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn16,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn18,
            this.gridColumn15});
            this.viewPlanExist.GridControl = this.gridPlanExist;
            this.viewPlanExist.Name = "viewPlanExist";
            this.viewPlanExist.OptionsBehavior.Editable = false;
            this.viewPlanExist.OptionsView.ColumnAutoWidth = false;
            this.viewPlanExist.OptionsView.ShowDetailButtons = false;
            this.viewPlanExist.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "文件";
            this.gridColumn16.FieldName = "FileName";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 0;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "开始时间";
            this.gridColumn12.DisplayFormat.FormatString = "yyyy/MM/dd HH:mm:ss";
            this.gridColumn12.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn12.FieldName = "BeginTime";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 1;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "结束时间";
            this.gridColumn13.DisplayFormat.FormatString = "yyyy/MM/dd HH:mm:ss";
            this.gridColumn13.FieldName = "EndTime";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 2;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "地市";
            this.gridColumn14.FieldName = "CityName";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 3;
            this.gridColumn14.Width = 68;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "网络类型";
            this.gridColumn18.FieldName = "NetType";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 4;
            this.gridColumn18.Width = 88;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "状态";
            this.gridColumn15.FieldName = "Desc";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 5;
            // 
            // pageNotExist
            // 
            this.pageNotExist.Controls.Add(this.gridPlanNotExist);
            this.pageNotExist.Name = "pageNotExist";
            this.pageNotExist.Size = new System.Drawing.Size(1080, 234);
            this.pageNotExist.Text = "未推送";
            // 
            // gridPlanNotExist
            // 
            this.gridPlanNotExist.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridPlanNotExist.Location = new System.Drawing.Point(0, 0);
            this.gridPlanNotExist.MainView = this.viewPlanNotExist;
            this.gridPlanNotExist.Name = "gridPlanNotExist";
            this.gridPlanNotExist.ShowOnlyPredefinedDetails = true;
            this.gridPlanNotExist.Size = new System.Drawing.Size(1080, 234);
            this.gridPlanNotExist.TabIndex = 2;
            this.gridPlanNotExist.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.viewPlanNotExist});
            // 
            // viewPlanNotExist
            // 
            this.viewPlanNotExist.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn17,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23});
            this.viewPlanNotExist.GridControl = this.gridPlanNotExist;
            this.viewPlanNotExist.Name = "viewPlanNotExist";
            this.viewPlanNotExist.OptionsBehavior.Editable = false;
            this.viewPlanNotExist.OptionsView.ColumnAutoWidth = false;
            this.viewPlanNotExist.OptionsView.ShowDetailButtons = false;
            this.viewPlanNotExist.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "文件";
            this.gridColumn17.FieldName = "FileName";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 0;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "开始时间";
            this.gridColumn19.DisplayFormat.FormatString = "yyyy/MM/dd HH:mm:ss";
            this.gridColumn19.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn19.FieldName = "BeginTime";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 1;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "结束时间";
            this.gridColumn20.DisplayFormat.FormatString = "yyyy/MM/dd HH:mm:ss";
            this.gridColumn20.FieldName = "EndTime";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 2;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "地市";
            this.gridColumn21.FieldName = "CityName";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 3;
            this.gridColumn21.Width = 68;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "网络类型";
            this.gridColumn22.FieldName = "NetType";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 4;
            this.gridColumn22.Width = 88;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "状态";
            this.gridColumn23.FieldName = "Desc";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 5;
            // 
            // CityFileStateForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1094, 548);
            this.ContextMenuStrip = this.contextMenuStrip1;
            this.Controls.Add(this.tabCtrlNavi);
            this.Controls.Add(this.pnlQuery);
            this.Name = "CityFileStateForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "集团文件推送情况";
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dateEditDateEnd.Properties.VistaTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditDateEnd.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditDateStart.Properties.VistaTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEditDateStart.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.spinEditNotExistPercent.Properties)).EndInit();
            this.pnlQuery.ResumeLayout(false);
            this.pnlQuery.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabCtrlNavi)).EndInit();
            this.tabCtrlNavi.ResumeLayout(false);
            this.pageBasic.ResumeLayout(false);
            this.pageTestPlan.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridTestPlan)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewTestPlan)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabPlanDetail)).EndInit();
            this.tabPlanDetail.ResumeLayout(false);
            this.pagePlanExist.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridPlanExist)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewPlanExist)).EndInit();
            this.pageNotExist.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridPlanNotExist)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewPlanNotExist)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExp2Xls;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.LabelControl labelControlInfo;
        private System.Windows.Forms.ListView listViewDateCityInfo;
        private System.Windows.Forms.ColumnHeader columnHeaderDate;
        private System.Windows.Forms.ColumnHeader columnHeaderCity;
        private System.Windows.Forms.ColumnHeader columnHeaderType;
        private System.Windows.Forms.ColumnHeader columnHeaderFileCount;
        private System.Windows.Forms.ColumnHeader columnHeaderExistCount;
        private System.Windows.Forms.ColumnHeader columnHeaderExistPercent;
        private System.Windows.Forms.ColumnHeader columnHeaderNotExistCount;
        private System.Windows.Forms.ColumnHeader columnHeaderNotExistPercent;
        private System.Windows.Forms.ListView listViewFileInfo;
        private System.Windows.Forms.ColumnHeader columnHeaderDate_File;
        private System.Windows.Forms.ColumnHeader columnHeaderCity_File;
        private System.Windows.Forms.ColumnHeader columnHeaderFileName;
        private System.Windows.Forms.ColumnHeader columnHeaderBeginTimeString;
        private System.Windows.Forms.ColumnHeader columnHeaderEndTimeString;
        private System.Windows.Forms.ColumnHeader columnHeaderDesc;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private System.Windows.Forms.ListView listViewNotExist;
        private System.Windows.Forms.ColumnHeader columnHeaderDate_Not;
        private System.Windows.Forms.ColumnHeader columnHeaderCity_Not;
        private System.Windows.Forms.ColumnHeader columnHeaderFileName_Not;
        private System.Windows.Forms.ColumnHeader columnHeaderStartTime_Not;
        private System.Windows.Forms.ColumnHeader columnHeaderEndTime_Not;
        private System.Windows.Forms.ColumnHeader columnHeaderStatus_Not;
        private DevExpress.XtraEditors.SimpleButton simpleButtonSearch;
        private DevExpress.XtraEditors.DateEdit dateEditDateEnd;
        private DevExpress.XtraEditors.DateEdit dateEditDateStart;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit spinEditNotExistPercent;
        private System.Windows.Forms.Panel pnlQuery;
        private DevExpress.XtraTab.XtraTabControl tabCtrlNavi;
        private DevExpress.XtraTab.XtraTabPage pageBasic;
        private DevExpress.XtraTab.XtraTabPage pageTestPlan;
        private DevExpress.XtraGrid.GridControl gridTestPlan;
        private DevExpress.XtraGrid.Views.Grid.GridView viewTestPlan;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraTab.XtraTabControl tabPlanDetail;
        private DevExpress.XtraTab.XtraTabPage pagePlanExist;
        private DevExpress.XtraGrid.GridControl gridPlanExist;
        private DevExpress.XtraGrid.Views.Grid.GridView viewPlanExist;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraTab.XtraTabPage pageNotExist;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.GridControl gridPlanNotExist;
        private DevExpress.XtraGrid.Views.Grid.GridView viewPlanNotExist;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
    }
}