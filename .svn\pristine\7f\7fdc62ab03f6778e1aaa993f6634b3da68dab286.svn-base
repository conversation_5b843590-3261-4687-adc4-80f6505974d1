﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NearestBtsSettingForm : BaseDialog
    {
        public NearestBtsSettingForm()
        {
            InitializeComponent();
        }

        public string FilePath
        {
            get { return textBoxFilePath.Text; }
        }

        public int Distance
        {
            get { return (int)spinEditDistance.Value; }
        }

        public int BtsNum
        {
            get { return (int)spinEditBtsNum.Value; }
        }

        public void SetFilePath(string filePath)
        {
            if (filePath != null)
            {
                textBoxFilePath.Text = filePath;
            }
        }

        private void simpleBtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void simpleBtnSelect_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Multiselect = false;
            dlg.Filter = "Excel files |*.xls;*.xlsx";
            if (dlg.ShowDialog() != DialogResult.OK) return;
            textBoxFilePath.Text = dlg.FileName;
        }

        private void simpleBtnSearch_Click(object sender, EventArgs e)
        {
            if (textBoxFilePath.Text.Trim().Equals(""))
                return;
            this.DialogResult = DialogResult.OK;
        }
    }
}
