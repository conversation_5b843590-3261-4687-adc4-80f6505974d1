<?xml version="1.0"?>
<Configs>
  <Config name="RoadQualAna">
    <Item name="Setting" typeName="IDictionary">
      <Item typeName="String" key="RoadAnaBaseDBName">RoadAna_GuangZhou</Item>
      <Item typeName="IDictionary" key="RoadQualAnaSetting">
        <Item typeName="Boolean" key="IsCheck2G">True</Item>
        <Item typeName="Boolean" key="IsCheck4G">True</Item>
        <Item typeName="Double" key="WeakRsrpGate">-100</Item>
        <Item typeName="Double" key="WeakRxlevGate">-90</Item>
        <Item typeName="Double" key="RoadAbnormalGate">10</Item>
        <Item typeName="Int32" key="CurUsingColorModeIndex">4</Item>
        <Item typeName="Double" key="RoadTestCount">27.2</Item>
        <Item typeName="IList" key="GridColorModeList">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">RSRP</Item>
            <Item typeName="Single" key="MinR">-141</Item>
            <Item typeName="Single" key="MaxR">25</Item>
            <Item typeName="String" key="Formula">Formula_LteRsrp</Item>
            <Item typeName="IList" key="ColorRanges">
              <Item typeName="IDictionary">
                <Item typeName="Single" key="MinV">-141</Item>
                <Item typeName="Single" key="MaxV">-100</Item>
                <Item typeName="Int32" key="ColorR">255</Item>
                <Item typeName="Int32" key="ColorG">0</Item>
                <Item typeName="Int32" key="ColorB">0</Item>
                <Item key="desInfo" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="Single" key="MinV">-100</Item>
                <Item typeName="Single" key="MaxV">-90</Item>
                <Item typeName="Int32" key="ColorR">255</Item>
                <Item typeName="Int32" key="ColorG">255</Item>
                <Item typeName="Int32" key="ColorB">0</Item>
                <Item key="desInfo" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="Single" key="MinV">-90</Item>
                <Item typeName="Single" key="MaxV">25</Item>
                <Item typeName="Int32" key="ColorR">0</Item>
                <Item typeName="Int32" key="ColorG">128</Item>
                <Item typeName="Int32" key="ColorB">0</Item>
                <Item key="desInfo" />
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">SINR</Item>
            <Item typeName="Single" key="MinR">-50</Item>
            <Item typeName="Single" key="MaxR">50</Item>
            <Item typeName="String" key="Formula">Formula_LteSinr</Item>
            <Item typeName="IList" key="ColorRanges">
              <Item typeName="IDictionary">
                <Item typeName="Single" key="MinV">-50</Item>
                <Item typeName="Single" key="MaxV">-10</Item>
                <Item typeName="Int32" key="ColorR">255</Item>
                <Item typeName="Int32" key="ColorG">0</Item>
                <Item typeName="Int32" key="ColorB">0</Item>
                <Item key="desInfo" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="Single" key="MinV">-10</Item>
                <Item typeName="Single" key="MaxV">10</Item>
                <Item typeName="Int32" key="ColorR">255</Item>
                <Item typeName="Int32" key="ColorG">255</Item>
                <Item typeName="Int32" key="ColorB">0</Item>
                <Item key="desInfo" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="Single" key="MinV">10</Item>
                <Item typeName="Single" key="MaxV">50</Item>
                <Item typeName="Int32" key="ColorR">0</Item>
                <Item typeName="Int32" key="ColorG">128</Item>
                <Item typeName="Int32" key="ColorB">0</Item>
                <Item key="desInfo" />
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">LTE综合覆盖率</Item>
            <Item typeName="Single" key="MinR">0</Item>
            <Item typeName="Single" key="MaxR">100</Item>
            <Item typeName="String" key="Formula">Formula_LteCoverRate</Item>
            <Item typeName="IList" key="ColorRanges">
              <Item typeName="IDictionary">
                <Item typeName="Single" key="MinV">0</Item>
                <Item typeName="Single" key="MaxV">10</Item>
                <Item typeName="Int32" key="ColorR">255</Item>
                <Item typeName="Int32" key="ColorG">0</Item>
                <Item typeName="Int32" key="ColorB">0</Item>
                <Item key="desInfo" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="Single" key="MinV">10</Item>
                <Item typeName="Single" key="MaxV">30</Item>
                <Item typeName="Int32" key="ColorR">255</Item>
                <Item typeName="Int32" key="ColorG">255</Item>
                <Item typeName="Int32" key="ColorB">0</Item>
                <Item key="desInfo" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="Single" key="MinV">30</Item>
                <Item typeName="Single" key="MaxV">100</Item>
                <Item typeName="Int32" key="ColorR">0</Item>
                <Item typeName="Int32" key="ColorG">128</Item>
                <Item typeName="Int32" key="ColorB">0</Item>
                <Item key="desInfo" />
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">RxLev</Item>
            <Item typeName="Single" key="MinR">-120</Item>
            <Item typeName="Single" key="MaxR">-10</Item>
            <Item typeName="String" key="Formula">Formula_GsmRxlev</Item>
            <Item typeName="IList" key="ColorRanges">
              <Item typeName="IDictionary">
                <Item typeName="Single" key="MinV">-120</Item>
                <Item typeName="Single" key="MaxV">-100</Item>
                <Item typeName="Int32" key="ColorR">255</Item>
                <Item typeName="Int32" key="ColorG">0</Item>
                <Item typeName="Int32" key="ColorB">0</Item>
                <Item key="desInfo" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="Single" key="MinV">-100</Item>
                <Item typeName="Single" key="MaxV">-90</Item>
                <Item typeName="Int32" key="ColorR">255</Item>
                <Item typeName="Int32" key="ColorG">255</Item>
                <Item typeName="Int32" key="ColorB">0</Item>
                <Item key="desInfo" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="Single" key="MinV">-90</Item>
                <Item typeName="Single" key="MaxV">-10</Item>
                <Item typeName="Int32" key="ColorR">0</Item>
                <Item typeName="Int32" key="ColorG">128</Item>
                <Item typeName="Int32" key="ColorB">0</Item>
                <Item key="desInfo" />
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">异常概率</Item>
            <Item typeName="Single" key="MinR">0</Item>
            <Item typeName="Single" key="MaxR">100</Item>
            <Item typeName="String" key="Formula">Formula_AbnomalPer</Item>
            <Item typeName="IList" key="ColorRanges">
              <Item typeName="IDictionary">
                <Item typeName="Single" key="MinV">0</Item>
                <Item typeName="Single" key="MaxV">10</Item>
                <Item typeName="Int32" key="ColorR">0</Item>
                <Item typeName="Int32" key="ColorG">128</Item>
                <Item typeName="Int32" key="ColorB">0</Item>
                <Item key="desInfo" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="Single" key="MinV">10</Item>
                <Item typeName="Single" key="MaxV">30</Item>
                <Item typeName="Int32" key="ColorR">255</Item>
                <Item typeName="Int32" key="ColorG">255</Item>
                <Item typeName="Int32" key="ColorB">0</Item>
                <Item key="desInfo" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="Single" key="MinV">30</Item>
                <Item typeName="Single" key="MaxV">100</Item>
                <Item typeName="Int32" key="ColorR">255</Item>
                <Item typeName="Int32" key="ColorG">0</Item>
                <Item typeName="Int32" key="ColorB">0</Item>
                <Item key="desInfo" />
              </Item>
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>