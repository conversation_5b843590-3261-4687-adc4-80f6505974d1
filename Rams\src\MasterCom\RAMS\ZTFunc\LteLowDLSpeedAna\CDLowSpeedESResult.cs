﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.NOP;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.LteLowDLSpeedAna
{
    public class CDLowSpeedESResult : ESResultInfo
    {
        public CDLowSpeedESResult() { }
        private List<TestPoint> testPointList { get; set; }
        public List<TestPoint> TestPoints
        {
            get { return testPointList; }
        }
        public CDLowSpeedESResult(MasterCom.RAMS.Net.ZTLowSpeedAnaByRegion_GSM.TempData data,
            List<TestPoint> listPt, double distance, string laccis, string bcchs, string cellName)
        {
            if (data.high_speed >= 0 && data.speed_sampleNum > 0)
            {
                this.HighSpeed = data.high_speed;
                this.LowSpeed = data.low_speed;
                this.meanSpeed = Math.Round(data.mean_speed / data.speed_sampleNum, 2);
            }
            this.distance_0Speed = data.distance_speed_0;
            this.laccis = laccis;
            this.BCCHs = bcchs;
            this.Distance = distance;
            this.testPointList = listPt;
            this.CellName = cellName;
            this.RSRP_Max = data.rsrp_sampleNum > 0 ? data.high_rsrp.ToString() : "-";
            this.RSRP_Min = data.rsrp_sampleNum > 0 ? data.low_rsrp.ToString() : "-";
            this.RSRP_Mean = data.rsrp_sampleNum > 0 ? Math.Round(data.mean_rsrp / data.rsrp_sampleNum, 2).ToString() : "-";
            this.SINR_Max = data.sinr_sampleNum > 0 ? data.high_sinr.ToString() : "-";
            this.SINR_Min = data.sinr_sampleNum > 0 ? data.low_sinr.ToString() : "-";
            this.SINR_Mean = data.sinr_sampleNum > 0 ? Math.Round(data.mean_sinr / data.sinr_sampleNum, 2).ToString() : "-";
            this.Throughput_DL_Max = data.throughput_DL_sampleNum > 0 ? data.high_throughput_DL.ToString() : "-";
            this.Throughput_DL_Min = data.throughput_DL_sampleNum > 0 ? data.low_throughput_DL.ToString() : "-";
            this.Throughput_DL_Mean = data.throughput_DL_sampleNum > 0 ? Math.Round(data.mean_throughput_DL / data.throughput_DL_sampleNum, 2).ToString() : "-";
            this.multiCoverageSampleNum = data.multiCoverageSampleNum;
        }


        public Event Event { get; set; }
        private string fileName = null;
        public string FileName
        {
            get
            {
                if (this.fileName != null)
                {
                    return this.fileName;
                }
                if (Event != null)
                {
                    return Event.FileName;
                }
                return string.Empty;
            }
            set
            {
                this.fileName = value;
            }
        }

        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }
        public float MidLng { get; set; }
        public double LastSec
        {
            get { return (EndTime - BeginTime).TotalSeconds; }
        }

        public DateTime CreateTime { get; set; }

        public DateTime EventTime { get; set; }

        public float MidLat { get; set; }

        //添加的列
        public int SN { get; set; }
        public string GridName { get; set; }
        public string AppType { get; set; }
        public string RoadDesc { get; set; }
        public double Distance { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public double Duration { get; set; }
        public double TotalPointCount
        {
            get
            {
                if (this.testPointList != null)
                {
                    return this.testPointList.Count;
                }
                return 0;
            }
        }
        public double LowPointCount { get; set; }
        public double lowPercent { get; set; } = 0;
        public string LowPercent
        {
            get
            {
                double retVal = Math.Round(lowPercent, 2);
                if (double.IsNaN(retVal)) return "-";
                return (retVal).ToString();
            }
        }
        public double HighSpeed { get; set; }
        public double LowSpeed { get; set; }
        public double distance_0Speed { get; set; } = 0;
        public string Distance_0Speed
        {
            get
            {
                double retVal = Math.Round(distance_0Speed, 2);
                if (double.IsNaN(retVal)) return "-";
                return (retVal).ToString();
            }
        }
        public string Rate_Distance_0Speed
        {
            get 
            { 
                double retVal = Math.Round(100 * distance_0Speed / Distance, 2);
                if (double.IsNaN(retVal)) return "-";
                return (retVal).ToString();
            }
        }
        public double meanSpeed { get; set; } = 0;
        public string MeanSpeed
        {
            get
            {
                double retVal = Math.Round(meanSpeed, 2);
                if (double.IsNaN(retVal)) return "-";
                return (retVal).ToString();
            }
        }
        public double MeanPDSCHBLER
        {
            get { return Math.Round(meanPDSCHBLER, 2); }
        }
        public string laccis { get; set; } = "";
        public string LACCIs
        {
            get
            {
                return this.laccis;
            }
        }
        public string CellName { get; set; }
        public string BCCHs { get; set; }
        public string AreaName { get; set; }
        public string AreaAgentName { get; set; }
        public string RSRP_Max { get; set; }
        public string RSRP_Min { get; set; }
        public string RSRP_Mean { get; set; }
        public string SINR_Max { get; set; }
        public string SINR_Min { get; set; }
        public string SINR_Mean { get; set; }
        public string RateWeakSinr 
        {
            get 
            { 
                double retVal = Math.Round(100 * weakSinrDistance / Distance, 2);
                if (double.IsNaN(retVal)) return "-";
                return retVal.ToString();
            }
        }
        public string RateMultiCoverage 
        { 
            get 
            { 
                double retVal = Math.Round(100.0f * multiCoverageSampleNum / testPointList.Count, 2);
                if (double.IsNaN(retVal)) return "-";
                return retVal.ToString();
            }
        }
        public string RateDisMulticoverage
        {
            get 
            { 
                double retVal = Math.Round(100.0f * distanceMulticoverage / Distance, 2); 
                if (double.IsNaN(retVal)) return "-";
                return retVal.ToString();
            }
        }
        public string Code0Mean
        {
            get
            {
                if (double.IsNaN(code0Mean))
                {
                    return "-";
                }
                return Math.Round(code0Mean, 2).ToString();
            }
        }
        public string Code1Mean
        {
            get
            {
                if (double.IsNaN(code1Mean))
                {
                    return "-";
                }
                return Math.Round(code1Mean, 2).ToString();
            }
        }
        public string Code0Max
        {
            get
            {
                if (double.IsNaN(code0Max))
                {
                    return "-";
                }
                return Math.Round(code0Max, 2).ToString();
            }
        }
        public string Code1Max
        {
            get
            {
                if (double.IsNaN(code1Max))
                {
                    return "-";
                }
                return Math.Round(code1Max, 2).ToString();
            }
        }
        public string CQI0Mean
        {
            get
            {
                if (double.IsNaN(cqi0Mean))
                {
                    return "-";
                }
                return Math.Round(cqi0Mean, 2).ToString();
            }
        }
        public string CQI1Mean
        {
            get
            {
                if (double.IsNaN(cqi1Mean))
                {
                    return "-";
                }
                return Math.Round(cqi1Mean, 2).ToString();
            }
        }
        public string Code0_64QAMRate
        {
            get
            {
                double retVal = Math.Round(code0_64QAMRate, 2);
                if (double.IsNaN(retVal)) return "-";
                return retVal.ToString();
            }
        }
        public string Code1_64QAMRate
        {
            get
            {
                double retVal = Math.Round(code1_64QAMRate, 2);
                if (double.IsNaN(retVal)) return "-";
                return retVal.ToString();
            }
        }
        public string Code0_16QAMRate
        {
            get
            {
                double retVal = Math.Round(code0_16QAMRate, 2);
                if (double.IsNaN(retVal)) return "-";
                return retVal.ToString();
            }
        }
        public string Code1_16QAMRate
        {
            get
            {
                double retVal = Math.Round(code1_16QAMRate, 2);
                if (double.IsNaN(retVal)) return "-";
                return retVal.ToString();
            }
        }
        public string Throughput_DL_Max { get; set; }
        public string Throughput_DL_Min { get; set; }
        public string Throughput_DL_Mean { get; set; }
        public string Transmission_Mode
        {
            get
            {
                double retVal = transmission_mode;
                if (double.IsNaN(retVal)) return "-";
                return Math.Round(retVal, 2).ToString();
            }
        }
        public string Transmission_Mode3
        {
            get
            {
                double retVal = transmission_mode3;
                if (double.IsNaN(retVal)) return "-";
                return retVal.ToString();
            }
        }
        public string Rank_Indicator
        {
            get
            {
                double retVal = rank_indicator; 
                if (double.IsNaN(retVal)) return "-";
                return Math.Round(retVal,2).ToString();
            }
        }
        public string Rank2_Indicator
        {
            get
            {
                double retVal = rank2_indicator; 
                if (double.IsNaN(retVal)) return "-";
                return Math.Round(retVal,2).ToString();
            }
        }
        public double pdsch_bler { get; set; } = 0;
        public string PDSCH_BLER
        {
            get
            {
                double retVal = pdsch_bler;
                if (double.IsNaN(retVal)) return "-";
                return Math.Round(retVal,2).ToString();
            }
        }
        public double pdsch_rb_number { get; set; } = 0;
        public string PDSCH_RB_Number
        {
            get
            {
                double retVal = pdsch_rb_number;
                if (double.IsNaN(retVal)) return "-";
                return Math.Round(retVal,2).ToString();
            }
        }
        public double pdsch_prb_num_s { get; set; } = 0;
        public string PDSCH_PRb_Num_s
        {
            get
            {
                double retVal = pdsch_prb_num_s;
                if (double.IsNaN(retVal)) return "-";
                return Math.Round(retVal,2).ToString();
            }
        }
        public double pdcch_dl_grant_count { get; set; } = 0;
        public string PDCCH_DL_Grant_Count
        {
            get
            {
                double retVal = pdcch_dl_grant_count;
                if (double.IsNaN(retVal)) return "-";
                return Math.Round(retVal,2).ToString();
            }
        }
        public double ratio_DL_Code0_HARQ_ACK { get; set; } = 0;
        public string Ratio_DL_Code0_HARQ_ACK
        {
            get
            {
                double retVal = ratio_DL_Code0_HARQ_ACK;
                if (double.IsNaN(retVal)) return "-";
                return Math.Round(retVal,2).ToString();
            }
        }
        public double ratio_DL_Code0_HARQ_NACK { get; set; } = 0;
        public string Ratio_DL_Code0_HARQ_NACK
        {
            get
            {
                double retVal = ratio_DL_Code0_HARQ_NACK;
                if (double.IsNaN(retVal)) return "-";
                return Math.Round(retVal,2).ToString();
            }
        }
        public double ratio_DL_Code1_HARQ_ACK { get; set; } = 0;
        public string Ratio_DL_Code1_HARQ_ACK
        {
            get
            {
                double retVal = ratio_DL_Code1_HARQ_ACK;
                if (double.IsNaN(retVal)) return "-";
                return Math.Round(retVal,2).ToString();
            }
        }
        public double ratio_DL_Code1_HARQ_NACK { get; set; } = 0;
        public string Ratio_DL_Code1_HARQ_NACK
        {
            get
            {
                double retVal = ratio_DL_Code1_HARQ_NACK;
                if (double.IsNaN(retVal)) return "-";
                return Math.Round(retVal,2).ToString();
            }
        }
        //
        private int multiCoverageSampleNum { get; set; } = 0;
        public List<TestPoint> weakSinrLst { get; set; } = new List<TestPoint>();
        public double weakSinrDistance { get; set; } = 0;
        public double distanceMulticoverage { get; set; } = 0;
        public double meanPDSCHBLER { get; set; } = 0;
        public double code0Mean { get; set; } = 0;
        public double code1Mean { get; set; } = 0;
        public double code0Max { get; set; } = 0;
        public double code1Max { get; set; } = 0;
        public double cqi0Mean { get; set; } = 0;
        public double cqi1Mean { get; set; } = 0;
        public double code0_64QAMRate { get; set; } = 0;
        public double code0_16QAMRate { get; set; } = 0;
        public double code1_64QAMRate { get; set; } = 0;
        public double code1_16QAMRate { get; set; } = 0;
        public double transmission_mode { get; set; } = 0;
        public double transmission_mode3 { get; set; } = 0;
        public double rank_indicator { get; set; } = 0;
        public double rank2_indicator { get; set; } = 0;

        public void GetResult()
        {
            FileName = testPointList[0].FileName;
            BeginTime = testPointList[0].DateTime;
            EndTime = testPointList[testPointList.Count - 1].DateTime;
            Longitude = testPointList[testPointList.Count / 2].Longitude;
            Latitude = testPointList[testPointList.Count / 2].Latitude;
            Duration = (EndTime - BeginTime).TotalSeconds;
            RoadDesc = GISManager.GetInstance().GetRoadPlaceDesc(Longitude, Latitude);
            string strAreaName = GISManager.GetInstance().GetAreaPlaceDesc(Longitude, Latitude);
            AreaName = getName(AreaName, strAreaName);
            string strGridName = GISManager.GetInstance().GetGridDesc(Longitude, Latitude);
            GridName = getName(GridName, strGridName);
            string strAreaAgentName = GISManager.GetInstance().GetAreaAgentDesc(Longitude, Latitude);
            AreaAgentName = getName(AreaAgentName, strAreaAgentName);
        }

        private string getName(string resName, string curName)
        {
            if (curName != null)
            {
                if (resName == null || resName == "")
                {
                    resName = curName;
                }
                else
                {
                    if (!resName.Contains(curName) && curName != "")
                    {
                        resName += "," + curName;
                    }
                }
            }
            return resName;
        }
    }
}
