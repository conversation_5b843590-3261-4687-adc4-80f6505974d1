﻿namespace MasterCom.RAMS.BackgroundFunc
{
    partial class BackgroundResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miSeparate = new System.Windows.Forms.ToolStripSeparator();
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToWord = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToWordAll = new System.Windows.Forms.ToolStripMenuItem();
            this.xtraTabControl = new DevExpress.XtraTab.XtraTabControl();
            this.pagSummary = new DevExpress.XtraTab.XtraTabPage();
            this.treeListDetail = new DevExpress.XtraTreeList.TreeList();
            this.treeListColumnType = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnFuncName = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumnResultCount = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.pagDetail = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl = new DevExpress.XtraEditors.SplitContainerControl();
            this.treeList = new DevExpress.XtraTreeList.TreeList();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.memDIYInfo = new DevExpress.XtraEditors.MemoEdit();
            this.miMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl)).BeginInit();
            this.xtraTabControl.SuspendLayout();
            this.pagSummary.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListDetail)).BeginInit();
            this.pagDetail.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl)).BeginInit();
            this.splitContainerControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeList)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.memDIYInfo.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.miSeparate,
            this.miExportToExcel,
            this.miMenuItem,
            this.miExportToWord,
            this.miExportToWordAll});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(229, 164);
            this.contextMenuStrip.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip_Opening);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(228, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(228, 22);
            this.miCollapseAll.Text = "全部折叠";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // miSeparate
            // 
            this.miSeparate.Name = "miSeparate";
            this.miSeparate.Size = new System.Drawing.Size(225, 6);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(228, 22);
            this.miExportToExcel.Text = "导出到Excel(不拆分个性信息)";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // miExportToWord
            // 
            this.miExportToWord.Name = "miExportToWord";
            this.miExportToWord.Size = new System.Drawing.Size(228, 22);
            this.miExportToWord.Text = "导出当前专题Word报告...";
            this.miExportToWord.Click += new System.EventHandler(this.miExportToWord_Click);
            // 
            // miExportToWordAll
            // 
            this.miExportToWordAll.Name = "miExportToWordAll";
            this.miExportToWordAll.Size = new System.Drawing.Size(228, 22);
            this.miExportToWordAll.Text = "导出所有专题Word报告...";
            this.miExportToWordAll.Click += new System.EventHandler(this.miExportToWordAll_Click);
            // 
            // xtraTabControl
            // 
            this.xtraTabControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl.Name = "xtraTabControl";
            this.xtraTabControl.SelectedTabPage = this.pagSummary;
            this.xtraTabControl.Size = new System.Drawing.Size(1002, 484);
            this.xtraTabControl.TabIndex = 1;
            this.xtraTabControl.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.pagSummary,
            this.pagDetail});
            this.xtraTabControl.SelectedPageChanged += new DevExpress.XtraTab.TabPageChangedEventHandler(this.xtraTabControl_SelectedPageChanged);
            // 
            // pagSummary
            // 
            this.pagSummary.Controls.Add(this.treeListDetail);
            this.pagSummary.Name = "pagSummary";
            this.pagSummary.Size = new System.Drawing.Size(995, 454);
            this.pagSummary.Text = "汇总";
            // 
            // treeListDetail
            // 
            this.treeListDetail.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.treeListColumnType,
            this.treeListColumnFuncName,
            this.treeListColumnResultCount});
            this.treeListDetail.ContextMenuStrip = this.contextMenuStrip;
            this.treeListDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListDetail.Location = new System.Drawing.Point(0, 0);
            this.treeListDetail.Name = "treeListDetail";
            this.treeListDetail.OptionsBehavior.Editable = false;
            this.treeListDetail.OptionsBehavior.PopulateServiceColumns = true;
            this.treeListDetail.OptionsView.AutoWidth = false;
            this.treeListDetail.Size = new System.Drawing.Size(995, 454);
            this.treeListDetail.TabIndex = 0;
            this.treeListDetail.VirtualTreeGetChildNodes += new DevExpress.XtraTreeList.VirtualTreeGetChildNodesEventHandler(this.treeListDetail_VirtualTreeGetChildNodes);
            this.treeListDetail.VirtualTreeGetCellValue += new DevExpress.XtraTreeList.VirtualTreeGetCellValueEventHandler(this.treeListDetail_VirtualTreeGetCellValue);
            // 
            // treeListColumnType
            // 
            this.treeListColumnType.Caption = "专题类型";
            this.treeListColumnType.FieldName = "Type";
            this.treeListColumnType.Name = "treeListColumnType";
            this.treeListColumnType.Visible = true;
            this.treeListColumnType.VisibleIndex = 0;
            this.treeListColumnType.Width = 178;
            // 
            // treeListColumnFuncName
            // 
            this.treeListColumnFuncName.Caption = "专题名称";
            this.treeListColumnFuncName.FieldName = "FuncName";
            this.treeListColumnFuncName.Name = "treeListColumnFuncName";
            this.treeListColumnFuncName.Visible = true;
            this.treeListColumnFuncName.VisibleIndex = 1;
            this.treeListColumnFuncName.Width = 277;
            // 
            // treeListColumnResultCount
            // 
            this.treeListColumnResultCount.Caption = "问题点个数";
            this.treeListColumnResultCount.FieldName = "ResultCount";
            this.treeListColumnResultCount.Name = "treeListColumnResultCount";
            this.treeListColumnResultCount.Visible = true;
            this.treeListColumnResultCount.VisibleIndex = 2;
            this.treeListColumnResultCount.Width = 177;
            // 
            // pagDetail
            // 
            this.pagDetail.Controls.Add(this.splitContainerControl);
            this.pagDetail.Name = "pagDetail";
            this.pagDetail.Size = new System.Drawing.Size(995, 454);
            this.pagDetail.Text = "明细";
            // 
            // splitContainerControl
            // 
            this.splitContainerControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl.Name = "splitContainerControl";
            this.splitContainerControl.Panel1.Controls.Add(this.treeList);
            this.splitContainerControl.Panel1.Text = "Panel1";
            this.splitContainerControl.Panel2.Controls.Add(this.splitContainerControl1);
            this.splitContainerControl.Panel2.Text = "Panel2";
            this.splitContainerControl.Size = new System.Drawing.Size(995, 454);
            this.splitContainerControl.SplitterPosition = 209;
            this.splitContainerControl.TabIndex = 2;
            this.splitContainerControl.Text = "splitContainerControl1";
            // 
            // treeList
            // 
            this.treeList.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeList.Location = new System.Drawing.Point(0, 0);
            this.treeList.Name = "treeList";
            this.treeList.Size = new System.Drawing.Size(209, 454);
            this.treeList.TabIndex = 0;
            this.treeList.BeforeExpand += new DevExpress.XtraTreeList.BeforeExpandEventHandler(this.treeList_BeforeExpand);
            this.treeList.FocusedNodeChanged += new DevExpress.XtraTreeList.FocusedNodeChangedEventHandler(this.treeList_FocusedNodeChanged);
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.Panel2;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.gridControl);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl1);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(780, 454);
            this.splitContainerControl1.SplitterPosition = 200;
            this.splitContainerControl1.TabIndex = 0;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.contextMenuStrip;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(574, 454);
            this.gridControl.TabIndex = 2;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            this.gridControl.DoubleClick += new System.EventHandler(this.gridControl_DoubleClick);
            // 
            // gridView
            // 
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsSelection.MultiSelect = true;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            this.gridView.SelectionChanged += new DevExpress.Data.SelectionChangedEventHandler(this.gridView_SelectionChanged);
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.memDIYInfo);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(200, 454);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "专题功能个性信息";
            // 
            // memDIYInfo
            // 
            this.memDIYInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.memDIYInfo.Location = new System.Drawing.Point(2, 23);
            this.memDIYInfo.Name = "memDIYInfo";
            this.memDIYInfo.Properties.ReadOnly = true;
            this.memDIYInfo.Size = new System.Drawing.Size(196, 429);
            this.memDIYInfo.TabIndex = 0;
            // 
            // miMenuItem
            // 
            this.miMenuItem.Name = "miMenuItem";
            this.miMenuItem.Size = new System.Drawing.Size(228, 22);
            this.miMenuItem.Text = "导出Excel(拆分个性化信息)";
            this.miMenuItem.Click += new System.EventHandler(this.miMenuItem_Click);
            // 
            // BackgroundResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1002, 484);
            this.Controls.Add(this.xtraTabControl);
            this.Name = "BackgroundResultForm";
            this.Text = "网络体检结果";
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl)).EndInit();
            this.xtraTabControl.ResumeLayout(false);
            this.pagSummary.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeListDetail)).EndInit();
            this.pagDetail.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl)).EndInit();
            this.splitContainerControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeList)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.memDIYInfo.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private System.Windows.Forms.ToolStripMenuItem miExportToWord;
        private System.Windows.Forms.ToolStripMenuItem miExportToWordAll;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl;
        private DevExpress.XtraTab.XtraTabPage pagSummary;
        private DevExpress.XtraTab.XtraTabPage pagDetail;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl;
        private DevExpress.XtraTreeList.TreeList treeList;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.MemoEdit memDIYInfo;
        private DevExpress.XtraTreeList.TreeList treeListDetail;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnType;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnFuncName;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumnResultCount;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private System.Windows.Forms.ToolStripSeparator miSeparate;
        private System.Windows.Forms.ToolStripMenuItem miMenuItem;
    }
}