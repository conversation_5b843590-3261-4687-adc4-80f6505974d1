﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTSatisfactionAnalysisQuery:QueryBase
    {
        public ZTSatisfactionAnalysisQuery(MainModel mainModel)
            : base(mainModel)
        { 
            
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        public override string Name
        {
            get { return "满意度问卷调查分析"; }
        }

        public override string IconName
        {
            get { return ""; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            SatisfactionAnalysisReportForm frm = null;
            frm = MainModel.GetObjectFromBlackboard(typeof(SatisfactionAnalysisReportForm).FullName) as SatisfactionAnalysisReportForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new SatisfactionAnalysisReportForm(MainModel);
            }
            if (!frm.Visible)
            {
                frm.Show(MainModel.MainForm);
            }
            else
            {
                frm.BringToFront();
            }
        }
    }
}
