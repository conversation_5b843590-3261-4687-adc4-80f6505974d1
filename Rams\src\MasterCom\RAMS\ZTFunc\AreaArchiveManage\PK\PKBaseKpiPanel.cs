﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class PKBaseKpiPanel : UserControl
    {
        private PKHub hub = null;
        public PKBaseKpiPanel()
        {
            InitializeComponent();

            attributePanelServ.SetAttribute("ServiceType", "业务类型");
            rangeSettingValue.RangeAll = new MasterCom.RAMS.Chris.Util.Range(-10000, true, 10000, true);
        }

        private void saveClick(object sender, EventArgs e)
        {
            if(attributePanelServ.GetAttribute().Count == 0)
            {
                MessageBox.Show("未设置业务类型...", "提示");
            }
            else if (richTextBoxFormulaExp.Text.Trim() == string.Empty)
            {
                MessageBox.Show("未设置竞比指标...", "提示");
            }
        }

        public void SetAttribute(PKHub hub)
        {
            this.hub = new PKHub(hub.Carrier);
            this.hub.Param = hub.Param;
            attributePanelServ.FillData(this.hub.PkBase.ServVec);
            richTextBoxFormulaExp.Text = this.hub.PkBase.FormulaExp;
            rangeSettingValue.Range = this.hub.PkBase.ValueRange;
        }

        public PKHub GetAttribute()
        {
            if (hub == null)
                hub = new PKHub(ECarrier.移动);
            PkBaseKpi pkBase = hub.PkBase;
            pkBase.ServVec = attributePanelServ.GetAttribute();
            pkBase.FormulaExp = richTextBoxFormulaExp.Text;
            pkBase.ValueRange = rangeSettingValue.Range;
            return hub;
        }

        private void simpleButtonEditFormula_Click(object sender, EventArgs e)
        {
            editExp(richTextBoxFormulaExp);
        }

        private void editExp(RichTextBox rtxt)
        {
            FormulaEditor formulaEditor = new FormulaEditor();
            formulaEditor.Formula = rtxt.Text;
            if (formulaEditor.ShowDialog() == DialogResult.OK)
            {
                rtxt.Text = formulaEditor.Formula;
            }
        }
    }

    public class PkBaseKpi
    {
        public List<int> ServVec { get; set; }
        public string FormulaName { get; set; }
        public string FormulaExp { get; set; }
        public Chris.Util.Range ValueRange { get; set; }

        public PkBaseKpi()
        {
            ServVec = new List<int>();
            ValueRange = new Chris.Util.Range(-10000, true, 10000, true);
        }

        public bool IsValid()
        {
            return ServVec.Count > 0 && FormulaExp.Length > 0;
        }

        public bool Contain(double dValue)
        {
            return ValueRange.Contains(dValue);
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic["ServVec"] = ServVec;
                dic["FormulaName"] = FormulaName;
                dic["FormulaExp"] = FormulaExp;
                dic["ValueRange"] = ValueRange.Param;
                return dic;
            }
            set
            {
                Dictionary<string, object> dic = value;
                if (dic["ServVec"] is IList<object>)
                {
                    List<object> serLst = dic["ServVec"] as List<object>;
                    foreach (object oServ in serLst)
                    {
                        ServVec.Add((int)oServ);
                    }
                }
                else if (dic["ServVec"] is List<int>)
                {
                    List<int> serLst = dic["ServVec"] as List<int>;
                    foreach (int oServ in serLst)
                    {
                        ServVec.Add(oServ);
                    }
                }
               
                FormulaName = Convert.ToString(dic["FormulaName"]);
                FormulaExp = Convert.ToString(dic["FormulaExp"]);
                ValueRange.Param = dic["ValueRange"] as Dictionary<string, object>;
            }
        }
    }
}
