﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TauHandoverConflictResultListForm : MinCloseForm
    {
        public TauHandoverConflictResultListForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            mapForm = MainModel.MainForm.GetMapForm();
            init();
            DisposeWhenClose = true;
        }

        private MapForm mapForm = null;
        List<TauHandoverInfo> resultList = new List<TauHandoverInfo>();

        public void FillData(List<TauHandoverInfo> resultList)
        {
            this.resultList = resultList;
            ListViewTauHandover.RebuildColumns();
            ListViewTauHandover.ClearObjects();
            ListViewTauHandover.SetObjects(resultList);

            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        private void init()
        {
            #region  界面数据绑定

            olvColumnStatSN.AspectGetter = delegate(object row)
            {
                if (row is TauHandoverInfo)
                {
                    TauHandoverInfo info = row as TauHandoverInfo;
                    return info.SN;
                }
                return null;
            };

            olvColumnFileName.AspectGetter = delegate(object row)
            {
                if (row is TauHandoverInfo)
                {
                    TauHandoverInfo info = row as TauHandoverInfo;
                    return info.file.Name;
                }
                return null;
            };

            olvColumnDate.AspectGetter = delegate(object row)
            {
                if (row is TauHandoverInfo)
                {
                    TauHandoverInfo info = row as TauHandoverInfo;
                    return info.BeginTime.ToShortDateString();
                }
                return null;
            };

            olvColumnTime.AspectGetter = delegate(object row)
            {
                if (row is TauHandoverInfo)
                {
                    TauHandoverInfo info = row as TauHandoverInfo;
                    return info.BeginTime.ToString("HH:mm:ss.fff");
                }
                return null;
            };

            #endregion
        }

        private void ToolStripReplay_Click(object sender, EventArgs e)
        {
            object rows = ListViewTauHandover.GetSelectedObject();
            if (rows == null)
            {
                MessageBox.Show("请选择要回放的文件");
                return;
            }
            TauHandoverInfo info = ListViewTauHandover.GetSelectedObject() as TauHandoverInfo;
            if (info == null)
            {
                return;
            }

            MainModel.MainForm.NeedChangeWorkSpace(false);
            PreNextMinutesForm frm = new PreNextMinutesForm(false);
            frm.Pre = 2;
            frm.Next = 2;
            if (frm.ShowDialog() == DialogResult.OK)
            {
                int pre = frm.Pre;
                int next = frm.Next;
                DateTime timeStart = info.BeginTime.AddMinutes(-pre);
                DateTime timeEnd = info.BeginTime.AddMinutes(next);
                FileReplayer.Replay(info.file, new MasterCom.Util.TimePeriod(timeStart, timeEnd));
            }
            else
            {
                MainModel.MainForm.CancelChange = true;
            }
            MainModel.MainForm.ChangeWorkSpace();
        }

        private void ToolStripExport_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(ListViewTauHandover);
        }
    }
}
