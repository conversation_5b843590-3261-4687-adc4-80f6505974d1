﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTNRServiceDelay;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRServiceDelayQuery : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static NRServiceDelayQuery instance = null;
        public static NRServiceDelayQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRServiceDelayQuery();
                    }
                }
            }
            return instance;
        }

        protected NRServiceDelayQuery()
           : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
            this.Columns = new List<string>();
        }

        public override string Name
        {
            get
            {
                return "接入延统计(按文件)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22046, this.Name);
        }

        protected bool checkDelay = true;
        protected int maxDelaySec = 10;

        protected List<SingleServiceDelayInfo> callStatList = null;

        protected override bool getCondition()
        {
            //var dlg = new CallConditionDlg();
            //dlg.SetCondition(checkDelay, maxDelaySec);
            //if (dlg.ShowDialog() != DialogResult.OK)
            //{
            //    return false;
            //}

            //dlg.GetCondition(out checkDelay, out maxDelaySec);

            callStatList = new List<SingleServiceDelayInfo>();

            return callStatList != null;
        }


        protected override void fireShowForm()
        {
            if (callStatList == null || callStatList.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据。");
                return;
            }

            var frm = MainModel.GetObjectFromBlackboard(typeof(ServiceDelayForm)) as ServiceDelayForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new ServiceDelayForm();
            }
            frm.FillData(callStatList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            callStatList = null;
        }


        protected override void analyseFiles()
        {
            try
            {
                clearDataBeforeAnalyseFiles();
                int iloop = 0;

                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0
                       && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                    {
                        continue;
                    }

                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + MainModel.FileInfos.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / MainModel.FileInfos.Count);

                    condition.FileInfos.Add(fileInfo);

                    replay();
                    condition.FileInfos.Clear();

                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }


        protected override void doStatWithQuery()
        {
            foreach (var file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file != null)
                {
                    dealServiceFile(file);
                }
            }
        }
        private void dealServiceFile(DTFileDataManager serFile)
        {
            try
            {
                bool isServiceSuccess = false;
                SingleServiceDelayInfo singleService = null;
                for (int i = 0; i < serFile.Events.Count; i++)
                {
                    var evt = serFile.Events[i];
                    if (evt.ID == 9292)    // NR Service Request
                    {
                        singleService = new SingleServiceDelayInfo();
                        singleService.ServiceRequestTime = evt.DateTime;
                    }

                    if (singleService != null && (evt.ID == 9311 || isServiceSuccess))    // NR Service Success
                    {
                        // 完成一次接入过程
                        dealServiceSuccess(ref singleService, evt, ref isServiceSuccess);
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + Environment.NewLine + e.StackTrace);
            }
        }
        private void dealServiceSuccess(ref SingleServiceDelayInfo singleService, Event evt, ref bool nciIsNull)
        {
            if (!nciIsNull)
            {
                singleService.Sn = callStatList.Count + 1;
                singleService.FileName = evt.FileName;
                singleService.OneEvent = evt;
                singleService.ServiceSuccessTime = evt.DateTime;
                singleService.Lng = evt.Longitude;
                singleService.Lat = evt.Latitude;
                singleService.NrTac = Convert.ToInt32(evt.LAC);
                singleService.NrNci = Convert.ToInt64(evt.CI);

                if (singleService.ServiceSuccessTime != null && singleService.ServiceRequestTime != null)
                {
                    singleService.ServiceDelayTime = (int)(singleService.ServiceSuccessTime - singleService.ServiceRequestTime).TotalMilliseconds;
                }
            }
            
            if (singleService.NrTac == null || singleService.NrNci == null || 
                singleService.NrTac == -1 || singleService.NrNci == -1 ||
                singleService.NrTac == 0 || singleService.NrNci == 0)
            {
                singleService.NrTac = Convert.ToInt32(evt.LAC);
                singleService.NrNci = Convert.ToInt64(evt.CI);
            }

            if (singleService.NrTac == null || singleService.NrNci == null ||
                singleService.NrTac == -1 || singleService.NrNci == -1 ||
                singleService.NrTac == 0 || singleService.NrNci == 0)
            {
                nciIsNull = true;
                return;
            }

            callStatList.Add(singleService);
            singleService = null;
            nciIsNull = false;
        }

    }

    public class SingleServiceDelayInfo
    {
        public SingleServiceDelayInfo()
        {

        }

        public int Sn { get; set; }
        public string FileName { get; set; }
        public Model.Event OneEvent { get; set; }

        public int? ServiceDelayTime { get; set; }    // 接入时延(ms)

        public DateTime ServiceRequestTime { get; set; }    // 请求时间

        public DateTime ServiceSuccessTime { get; set; }    // 完成时间

        public double? Lng { get; set; }
        public double? Lat { get; set; }

        public int? NrTac { get; set; }
        public long? NrNci { get; set; }

    }

}
