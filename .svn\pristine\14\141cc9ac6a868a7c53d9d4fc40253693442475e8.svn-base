﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;

using System.Text;
using System.Windows.Forms;

namespace MasterCom.Util
{
    public partial class SortForm : BaseFormStyle
    {
        private ListView listView;
        public SortForm(ListView listView)
        {
            InitializeComponent();
            this.listView = listView;
            initCols();
        }

        void initCols()
        {
            for (int i = 0; i < this.listView.Columns.Count; i++)
            {
                this.allCol_CB.Items.Add(new ColItem(this.listView.Columns[i],i));
            }
            if (this.allCol_CB.Items.Count > 0)
                this.allCol_CB.SelectedIndex = 0;
            else
                this.allCol_CB.SelectedIndex = -1;
        }

        private void allCol_CB_SelectedIndexChanged(object sender, EventArgs e)
        {
            this.add_B.Enabled = allCol_CB.SelectedIndex > -1;
        }

        private void add_B_Click(object sender, EventArgs e)
        {
            ColItem colItem = this.allCol_CB.SelectedItem as ColItem;
            int index = this.allCol_CB.SelectedIndex;

            ListViewItem item = new ListViewItem(colItem.ch.Text);
            item.Tag = colItem;
            item.StateImageIndex = colItem.isAsc ? 0 : 1;
            seled_LV.Items.Add(item);

            this.allCol_CB.Items.Remove(this.allCol_CB.SelectedItem);
            if (index < allCol_CB.Items.Count)
            {
                this.allCol_CB.SelectedIndex = index;
            }
        }

        private void seled_LV_SelectedIndexChanged(object sender, EventArgs e)
        {
            this.moveUp_B.Enabled = seled_LV.Items.Count > 1 && seled_LV.SelectedIndices.Count > 0 && seled_LV.SelectedIndices[0] != 0;
            this.remove_B.Enabled = seled_LV.SelectedIndices.Count > 0 && seled_LV.SelectedIndices[0] > -1;
            this.moveDown_B.Enabled = seled_LV.Items.Count > 1 && seled_LV.SelectedIndices.Count > 0 && seled_LV.SelectedIndices[0] != seled_LV.Items.Count - 1;
            this.asc_RB.Enabled = this.remove_B.Enabled;
            this.desc_RB.Enabled = this.asc_RB.Enabled;
            if (SelectedColItem != null)
            {
                this.asc_RB.Checked = SelectedColItem.isAsc;
                this.desc_RB.Checked = !SelectedColItem.isAsc;
            }
        }

        private ColItem SelectedColItem
        {
            get
            {
                if (seled_LV.SelectedItems.Count > 0) return seled_LV.SelectedItems[0].Tag as ColItem;
                return null;
            }
        }

        private void remove_B_Click(object sender, EventArgs e)
        {
            ColItem ci=this.seled_LV.SelectedItems[0].Tag as ColItem;
            this.allCol_CB.Items.Add(ci);
            this.seled_LV.Items.Remove(this.seled_LV.SelectedItems[0]);
        }

        private void moveUp_B_Click(object sender, EventArgs e)
        {
            ListViewItem item = this.seled_LV.SelectedItems[0];
            int index = this.seled_LV.SelectedIndices[0];
            this.seled_LV.Items.RemoveAt(index);
            this.seled_LV.Items.Insert(index - 1, item);
            this.seled_LV.SelectedIndices.Clear();
            this.seled_LV.SelectedIndices.Add(index - 1);
        }

        private void moveDown_B_Click(object sender, EventArgs e)
        {
            ListViewItem item = this.seled_LV.SelectedItems[0];
            int index = this.seled_LV.SelectedIndices[0];
            this.seled_LV.Items.RemoveAt(index);
            this.seled_LV.Items.Insert(index + 1, item);
            this.seled_LV.SelectedIndices.Clear();
            this.seled_LV.SelectedIndices.Add(index + 1);
        }

        private void ok_B_Click(object sender, EventArgs e)
        {
            try
            {
                this.listView.ListViewItemSorter = new ListViewItemsComparer(SelectedCol());
                this.listView.Sort();
                this.Close();
            }
            catch (Exception ee)
            { Console.WriteLine(ee.Source.ToString()+":"+ee.Message);}
        }

        private ColItem[] SelectedCol()
        {
            ColItem[] cols = new ColItem[this.seled_LV.Items.Count];
            for (int i = 0; i < this.seled_LV.Items.Count;i++ )
            {
                cols[i] = (ColItem)this.seled_LV.Items[i].Tag;
            }
            return cols;
        }

        private void cancel_B_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void asc_RB_Click(object sender, EventArgs e)
        {
            desc_RB.Checked = !asc_RB.Checked;
            fireAscChange();
        }

        private void desc_RB_Click(object sender, EventArgs e)
        {
            asc_RB.Checked = !desc_RB.Checked;
            fireAscChange();
        }

        private void fireAscChange()
        {
            ColItem item = SelectedColItem;
            if (item != null)
            {
                item.isAsc = asc_RB.Checked;
                this.seled_LV.SelectedItems[0].StateImageIndex = item.isAsc ? 0 : 1;
            }
        }

    }

    public class ColItem
    {
        public ColumnHeader ch { get; set; }
        int id { get; set; }
        public ColItem(ColumnHeader ch,int id)
        {
            this.ch = ch;
            this.id = id;
        }
        public override string ToString()
        {
            if (string.IsNullOrEmpty(ch.Text))
                return "列" + (id + 1);
            return ch.Text;
        }
        public bool isAsc { get; set; } = true;
    }

    /// <summary>
    ///  ********************* 存在问题
    /// </summary>
    public class ListViewItemsComparer : System.Collections.IComparer
    {
        private ColItem[] col { get; set; } //要排序的列
        public ListViewItemsComparer()
        {
        }
        public ListViewItemsComparer(ColItem[] cols) //构造函数
        {
            col = new ColItem[cols.Length];
            for (int i = 0; i < cols.Length; i++)
            {
                col[i] = cols[i];
            }
        }

        //实现compare方法
        public int Compare(object x, object y)
        {
            string compStrX = "", compStrY = "";
            for (int i = 0; i < col.Length; i++)
            {
                compStrX=((ListViewItem)x).SubItems[col[i].ch.Index].Text;
                compStrY=((ListViewItem)y).SubItems[col[i].ch.Index].Text;
                if (compStrX.Equals(compStrY)) continue;
                if (col[i].isAsc) return Compare(compStrX, compStrY);
                else return Compare(compStrY, compStrX);
            }
            return 0;
        }

        private int Compare(string str1, string str2)
        {
            double dou1, dou2;
            if (double.TryParse(str1, out dou1) && double.TryParse(str2, out dou2))
            {
                return Comparer<double>.Default.Compare(dou1, dou2);
            }
            else
                return string.Compare(str1, str2);
        }
    }


}
