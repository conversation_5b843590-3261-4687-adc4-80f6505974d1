﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.IO;
using System.Drawing;
using System.Drawing.Imaging;
using System.Windows.Forms;

using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Model.Interface;
using MasterCom.MControls;

using DevExpress.XtraTab;
using DevExpress.XtraCharts;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.Func.ScanGridAnaExporter
{
    public class ScanGridAnaWord : WordControl
    {
        public ScanGridAnaWord(bool visible) : base(visible)
        {
            oMissing = System.Reflection.Missing.Value;
        }

        public override void InsertText(string pText, object styles)
        {
            object styleObj = styles;
            object Units = Word.WdUnits.wdStory;
            WordApp.Selection.EndKey(ref Units, ref oMissing);
            WordApp.Application.Selection.set_Style(ref styleObj);
            if (styles.ToString() == "标题")
            {
                WordApp.Application.Selection.Font.Size = 18;
            }
            else if (styles.ToString() == "标题 1" || styles.ToString() == "标题 2" || styles.ToString() == "标题 3")
            {
                WordApp.Application.Selection.Font.Size = 14;
            }
            else if (styles.ToString() == "正文")
            {
                WordApp.Application.Selection.Font.Size = 12f;
            }
            WordApp.Application.Selection.TypeText(pText);
        }

        public void ApplyGlobalStyle()
        {
            WordDoc.Content.Font.Name = "微软雅黑";
            WordDoc.Paragraphs.LineSpacingRule = Word.WdLineSpacing.wdLineSpace1pt5;
            foreach (Word.Table tb in WordDoc.Tables)
            {
                tb.Range.Font.Size = 9;
                tb.Range.ParagraphFormat.LineSpacingRule = Word.WdLineSpacing.wdLineSpaceSingle;
            }
        }
    }

    public class TestDataExper
    {
        public void Export(WordControl word)
        {
            ScanGridAnaSettingCondition cond = ScanGridAnaSettingCondition.Instance;
            Export(word, "当前时间段", 
                new TimePeriod(cond.AnaStartTime, cond.AnaEndTime), 
                cond.AnaProjectDesc.ToArray(),
                new string[] { cond.ServiceDesc });
            if (!cond.IsCmpUsed)
            {
                return;
            }

            word.NewLine();
            word.NewLine();
            Export(word, "基准库时间段",
                new TimePeriod(cond.CmpStartTime, cond.CmpEndTime),
                cond.CmpProjectDesc.ToArray(),
                new string[] { cond.ServiceDesc });
        }

        private void Export(WordControl word, string timeDesc, TimePeriod tp, string[] projs, string[] srvs)
        {
            TimePeriod tmp = new TimePeriod(tp.BeginTime, tp.EndTime.Date.AddDays(-1));
            tmp.showDayFormat = true;
            word.InsertText(string.Format("{0}：{1}", timeDesc, tmp.ToString()), "正文");
            word.NewLine();

            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < projs.Length; ++i)
            {
                sb.Append(projs[i]);
                if (i < projs.Length - 1)
                {
                    sb.Append(", ");
                }
            }
            word.InsertText(string.Format("项目来源：{0}", sb.ToString()), "正文");
            word.NewLine();

            sb = new StringBuilder();
            for (int i = 0; i < srvs.Length; ++i)
            {
                sb.Append(srvs[i]);
                if (i < srvs.Length - 1)
                {
                    sb.Append(", ");
                }
            }
            word.InsertText(string.Format("业务类型：{0}", sb.ToString()), "正文");
        }
    }

    public class StatConditionExper
    {
        public void Export(WordControl word)
        {
            word.InsertText("相对覆盖度：道路上弱于最强信号设定值范围内的小区数（含最强信号小区）。", "正文");
            word.NewLine();
            word.InsertText("绝对覆盖度：道路上电平值大于设定值的小区数。", "正文");
            word.NewLine();
            word.InsertText("综合覆盖度：上述两种覆盖度的交集。", "正文");
        }
    }

    public class CountRateGisExper
    {
        public void Export(WordControl word, Form form, List<DataTable> cnts, List<DataTable> rates, MapForm mf)
        {
            List<ColorRange> curColorRanges = ScanGridAnaColorRanger.Instance.GetColorRanges();
            ScanGridAnaRangeType rangeType = ScanGridAnaColorRanger.Instance.CurRangeType;

            word.InsertText("栅格个数统计：", "正文");
            word.NewLine();
            GridView gv = ExperMethod.FillCountGridToForm(form, cnts[0]);
            ExperMethod.GridViewToWord(word, gv, curColorRanges);
            gv.Dispose();
            word.NewLine();
            ChartControl cc = ExperMethod.FillCountChartToForm(form, cnts[1], rangeType);
            string picFile = ExperMethod.ChartScreenshot(cc, cnts[1].Rows.Count);
            cc.Dispose();
            word.InsertPicture(picFile);
            word.InsertBreak();

            word.InsertText("栅格占比统计：", "正文");
            word.NewLine();
            gv = ExperMethod.FillRateGridToForm(form, rates[0]);
            ExperMethod.GridViewToWord(word, gv, curColorRanges);
            gv.Dispose();
            word.NewLine();
            cc = ExperMethod.FillRateChartToForm(form, rates[1], rangeType);
            picFile = ExperMethod.ChartScreenshot(cc, rates[1].Rows.Count);
            cc.Dispose();
            word.InsertPicture(picFile);
            
            if (mf == null)
            {
                return;
            }
            word.InsertBreak();
            word.InsertText("GIS渲染效果：", "正文");
            word.NewLine();
            picFile = ExperMethod.GisScreenshot(mf);
            word.InsertPicture(picFile);
        }
    }

    public class CompareExper
    {
        public void Export(WordControl word, Form form, DataTable dt, MapForm mf)
        {
            word.InsertText("整体渲染效果如下：", "正文");
            string picFile = ExperMethod.GisScreenshot(mf);
            word.InsertPicture(picFile);
            word.NewLine();

            word.InsertText("分网格统计数据如下：", "正文");
            GridView gv = ExperMethod.FillCountGridToForm(form, dt);
            gv.Columns[gv.Columns.Count - 1].DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            gv.Columns[gv.Columns.Count - 1].DisplayFormat.FormatString = "p";
            ExperMethod.GridViewToWord(word, gv, null);
            word.NewLine();
        }
    }

    public class Coverage07Exper
    {
        public void Export(WordControl word, Form form, DataTable dt)
        {
            GridView gv = ExperMethod.FillRateGridToForm(form, dt);
            ExperMethod.GridViewToWord(word, gv, null);
            for (int i = 1; i < dt.Columns.Count - 1; i += 2)
            {
                word.NewLine();
                ChartControl chart = CreateChartByTbIndex(form, dt, new List<int>() { i, i + 1 });
                string picFile = ExperMethod.ChartScreenshot(chart, dt.Rows.Count - 1);
                word.InsertPicture(picFile);
            }
            word.NewLine();
            ChartControl chartDep = CreateChartByTbIndex(form, dt, new List<int>() { dt.Columns.Count - 1 });
            string picFileDep = ExperMethod.ChartScreenshot(chartDep, dt.Rows.Count - 1);
            word.InsertPicture(picFileDep);
        }

        private ChartControl CreateChartByTbIndex(Form form, DataTable dt, List<int> idxs)
        {
            form.Controls.Clear();
            ChartControl chart = new ChartControl();
            form.Controls.Add(chart);

            foreach (int idx in idxs)
            {
                Series sis = new Series(dt.Columns[idx].Caption, ViewType.Bar);
                for (int i = 0; i < dt.Rows.Count - 1; ++i)
                {
                    string regionName = dt.Rows[i].ItemArray[0] as string;
                    double value = (double)dt.Rows[i].ItemArray[idx];
                    sis.Points.Add(new SeriesPoint(regionName, new double[] { value }));
                }
                ((SideBySideBarSeriesView)sis.View).BarWidth = 0.4;
                ((SideBySideBarSeriesLabel)sis.Label).Visible = false;
                chart.Series.Add(sis);
            }
            chart.Dock = DockStyle.Fill;
            ((XYDiagram)chart.Diagram).Rotated = true;
            ((XYDiagram)chart.Diagram).AxisY.NumericOptions.Format = NumericFormat.Percent;
            ((XYDiagram)chart.Diagram).AxisY.NumericOptions.Precision = 0;
            ((XYDiagram)chart.Diagram).AxisX.Reverse = true;

            return chart;
        }
    }

    public class ConsecutiveRegionExper
    {
        public void Export(WordControl word, MapForm mf)
        {
            string picFile = ExperMethod.GisScreenshot(mf);
            word.InsertPicture(picFile);
        }
    }

    public static class ExperMethod
    {
        public static GridView FillCountGridToForm(Form form, DataTable dt)
        {
            form.Controls.Clear();
            GridView gv = ScanGridAnaWidgetMethod.CreateGridView();
            gv.GridControl.Dock = DockStyle.Fill;
            form.Controls.Add(gv.GridControl);
            ScanGridAnaWidgetMethod.FillCountGrid(gv, dt);
            return gv;
        }

        public static ChartControl FillCountChartToForm(Form form, DataTable dt, ScanGridAnaRangeType rangeType)
        {
            form.Controls.Clear();
            ChartControl cc = ScanGridAnaWidgetMethod.CreateChartControl();
            cc.Dock = DockStyle.Fill;
            form.Controls.Add(cc);
            ScanGridAnaWidgetMethod.FillCountChart(cc, dt, rangeType);
            return cc;
        }

        public static GridView FillRateGridToForm(Form form, DataTable dt)
        {
            form.Controls.Clear();
            GridView gv = ScanGridAnaWidgetMethod.CreateGridView();
            gv.GridControl.Dock = DockStyle.Fill;
            form.Controls.Add(gv.GridControl);
            ScanGridAnaWidgetMethod.FillRateGrid(gv, dt);
            return gv;
        }

        public static ChartControl FillRateChartToForm(Form form, DataTable dt, ScanGridAnaRangeType rangeType)
        {
            form.Controls.Clear();
            ChartControl cc = ScanGridAnaWidgetMethod.CreateChartControl();
            cc.Dock = DockStyle.Fill;
            form.Controls.Add(cc);
            ScanGridAnaWidgetMethod.FillRateChart(cc, dt, rangeType);
            return cc;
        }

        public static string GisScreenshot(MapForm mf)
        {
            string picDir = Path.Combine(Application.StartupPath, @"userdata\ScanGridPictures");
            if (!Directory.Exists(picDir))
            {
                Directory.CreateDirectory(picDir);
            }

            string picFile = Path.Combine(picDir, Guid.NewGuid().ToString() + ".png");
            mf.updateMap();
            Bitmap bitMap = mf.DrawToBitmapDIY();
            bitMap.Save(picFile, ImageFormat.Png);
            bitMap.Dispose();
            return picFile;
        }

        public static string ChartScreenshot(ChartControl chart, int rowCnt)
        {
            string picDir = Path.Combine(Application.StartupPath, @"userdata\ScanGridPictures");
            if (!Directory.Exists(picDir))
            {
                Directory.CreateDirectory(picDir);
            }

            string picFile = Path.Combine(picDir, Guid.NewGuid().ToString() + ".wmf");
            ChartControl tmpChart = chart.Clone() as ChartControl;
            tmpChart.Size = new Size(540, rowCnt <= 0 ? 240 : rowCnt * 40);
            tmpChart.ExportToImage(picFile, ImageFormat.Wmf);
            tmpChart.Dispose();
            return picFile;
        }

        public static void GridViewToWord(WordControl word, GridView gv, List<ColorRange> colorRange)
        {
            Word.Table wtb = word.CreateTable(gv.RowCount + 1, gv.Columns.Count);
            wtb.AutoFitBehavior(Word.WdAutoFitBehavior.wdAutoFitWindow);

            int curRow = 1;
            for (int i = 0; i < gv.Columns.Count; ++i)
            {
                word.InsertText(
                    wtb,
                    curRow, i + 1,
                    gv.Columns[i].Caption == "" ? gv.Columns[i].ToString() : gv.Columns[i].Caption,
                    "正文");
                wtb.Cell(curRow, i + 1).Range.ParagraphFormat.Alignment = Word.WdParagraphAlignment.wdAlignParagraphCenter;
                wtb.Cell(curRow, i + 1).Range.Bold = 1;
                if (colorRange != null && i > 0 && i - 1 < colorRange.Count)
                {
                    word.ApplyStyle(wtb, curRow, i + 1, Color.Black, colorRange[i - 1].color);
                }
            }

            for (int i = 0; i < gv.RowCount; ++i)
            {
                for (int j = 0; j < gv.Columns.Count; ++j)
                {
                    string txt = gv.GetRowCellDisplayText(i, gv.Columns[j]);
                    word.InsertText(wtb, i + 2, j + 1, txt, "正文");
                    wtb.Cell(i + 2, j + 1).Range.ParagraphFormat.Alignment = Word.WdParagraphAlignment.wdAlignParagraphCenter;
                }
            }
        }
    }
}
