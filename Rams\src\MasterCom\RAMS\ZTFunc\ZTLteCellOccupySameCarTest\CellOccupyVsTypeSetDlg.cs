﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CellOccupyVsTypeSetDlg : BaseDialog
    {
        public bool IsCheckDlAndUl { get; set; } = true;
        public int TimeSpanVolteAndDl { get; set; } = 30;

        public CellOccupyVsTypeSetDlg()
        {
            InitializeComponent();
        }
        public void SetCondition(bool isCheckDlAndUl, int timeSpanVolteAndDl)
        {
            this.IsCheckDlAndUl = isCheckDlAndUl;
            this.TimeSpanVolteAndDl = timeSpanVolteAndDl;
            if (isCheckDlAndUl)
            {
                cbxVsType.SelectedIndex = 0;
                panelTimeSpan.Visible = false;
            }
            else
            {
                cbxVsType.SelectedIndex = 1;
                panelTimeSpan.Visible = true;
                numTimeSpan.Value = timeSpanVolteAndDl;
            }
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            TimeSpanVolteAndDl = (int)numTimeSpan.Value;
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void cbxVsType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cbxVsType.SelectedIndex == 0)
            {
                IsCheckDlAndUl = true;
                panelTimeSpan.Visible = false;
            }
            else
            {
                IsCheckDlAndUl = false;
                panelTimeSpan.Visible = true;
                numTimeSpan.Value = TimeSpanVolteAndDl;
            }
        }
    }
}
