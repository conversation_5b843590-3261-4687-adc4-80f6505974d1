﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.Interface
{
    public class DIYSqlGetGMapConstr : DIYSQLBase
    {
        string gmapConStr = null;
        public DIYSqlGetGMapConstr(MainModel mainModel)
            : base(mainModel)
        {
            this.MainDB = true;
        }
        protected override string getSqlTextString()
        {
            return "select constr from tb_cfg_static_gmapdbstr";
        }
        public string GetResultConstr()
        {
            return gmapConStr;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    gmapConStr = package.Content.GetParamString();
                }
                else if (package.Content.Type == Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public override string Name
        {
            get { return "DIYSqlGetGMapConstr"; }
        }
    }
}
