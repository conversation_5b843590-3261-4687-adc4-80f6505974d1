﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using MasterCom.RAMS.Grid;
using MasterCom.MTGis;
using MasterCom.RAMS.KPI_Statistics;
namespace MasterCom.RAMS.Net
{
    public abstract class QueryCellGridScan : DIYGridQuery
    {
        protected GridMatrix<GridDataUnit> CurScanGridUnitMatrix;
        protected QueryCellGridScan(MainModel mainModel)
            : base(mainModel)
        {
            isQueryAllParams = true;
        }
        protected override bool isValidCondition()
        {
            return true;
        }
     
        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                WaitBox.Text = "开始查询栅格数据...";
                WaitBox.CanCancel = true;
                CurScanGridUnitMatrix = new GridMatrix<GridDataUnit>();
                string statImgIDSet = getStatImgNeededTriadID();
                if (condition.IsByRound)
                {
                    queryPeriodInfo(null, clientProxy, statImgIDSet, CurScanGridUnitMatrix);
                }
                else
                {
                    foreach (TimePeriod period in condition.Periods)
                    {
                        queryPeriodInfo(period, clientProxy, statImgIDSet, CurScanGridUnitMatrix);
                    }
                }
                WaitBox.Text = "数据获取完毕，进行分析...";
            }
            catch (Exception e)
            {
                showException(e);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.cell_grid;
        }

        protected override void preparePackageCommand(Package package)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_STATI_SCAN_CELL_GRID;
            package.Content.PrepareAddParam();
        }

        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, params object[] reservedParams)
        {
            Package package = clientProxy.Package;
            int counter = 0;
            bool recved = false;
            int curPercent = 11;
            DTDataHeaderManager.GetInstance();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            KPIStatDataBase kpiData = null;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (!recved)
                {
                    WaitBox.Text = "正在从服务器接收数据...";
                }
                recved = true;
       
                if (isFileHeaderContentType(package.Content.Type))
                {
                    //
                }
                else if (isImgColDefContent(package, curImgColumnDef))
                {
                    //
                }
                else if (isKPIDataContent(package, out kpiData))
                {
                    fillData(package, curImgColumnDef, kpiData);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    MessageBox.Show("Unexpected type: " + package.Content.Type);
                    break;
                }

                setProgressPercent(ref counter, ref curPercent);
            }
        }

        private void fillData(Package package, List<StatImgDefItem> curImgColumnDef, KPIStatDataBase kpiData)
        {
            double ltLng = 0;
            double ltLat = 0;
            ltLng = package.Content.GetParamDouble();
            ltLat = package.Content.GetParamDouble();
            GridUnitBase gridBase = new GridUnitBase(ltLng, ltLat);
            if (isValidStatImg(gridBase))
            {
                Dictionary<string, double> cellIndicatorDic = new Dictionary<string, double>();
                setCellIndicator(package, curImgColumnDef, kpiData, cellIndicatorDic);

                //===按小区保存统计数据
                addCellStatData(kpiData, ltLng, ltLat, cellIndicatorDic);

                GridDataUnit grid = new GridDataUnit(ltLng, ltLat);
                grid.AddStatData(kpiData);
                int rAt, cAt;
                GridHelper.GetIndexOfDefaultSizeGrid(grid.CenterLng, grid.CenterLat, out rAt, out cAt);
                GridDataUnit old = CurScanGridUnitMatrix[rAt, cAt];
                if (old == null)
                {
                    CurScanGridUnitMatrix[rAt, cAt] = grid;
                }
                else
                {
                    old.Gather(grid);
                }
            }
        }

        private static void setCellIndicator(Package package, List<StatImgDefItem> curImgColumnDef, KPIStatDataBase kpiData, Dictionary<string, double> cellIndicatorDic)
        {
            foreach (StatImgDefItem cdf in curImgColumnDef)
            {
                byte[] imgBytes = package.Content.GetParamBytes();
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);

                foreach (string str in cellStatInfoDic.Keys)
                {
                    double value;
                    if (double.TryParse(cellStatInfoDic[str].Value.ToString(), out value))
                    {
                        kpiData[str, -1] = value;
                        cellIndicatorDic[str] = value;
                    }
                }
            }
        }

        private static void addCellStatData(KPIStatDataBase kpiData, double ltLng, double ltLat, Dictionary<string, double> cellIndicatorDic)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds((long)kpiData[KPIStatDataBase.FileTimeKey, KPIStatDataBase.NewGridFileTimeKey, -1] * 1000L);
            if (kpiData is StatDataSCAN_GSM)
            {
                addStatDataSCAN_GSM(kpiData, ltLng, ltLat, cellIndicatorDic, dt);
            }
            else if (kpiData is StatDataSCAN_TD)
            {
                addStatDataSCAN_TD(kpiData, ltLng, ltLat, cellIndicatorDic, dt);
            }
            else if (kpiData is StatDataSCAN_LTE)
            {
                addStatDataSCAN_LTE(kpiData, ltLng, ltLat, cellIndicatorDic, dt);
            }
            else if (kpiData is StatDataSCAN_WCDMA)
            {
                addStatDataSCAN_WCDMA(kpiData, ltLng, ltLat, cellIndicatorDic, dt);
            }
            else if (kpiData is StatDataSCAN_CDMA)
            {
                //
            }
        }

        private static void addStatDataSCAN_GSM(KPIStatDataBase kpiData, double ltLng, double ltLat, Dictionary<string, double> cellIndicatorDic, DateTime dt)
        {
            ICell cell = null;
            double bcch = kpiData[StatDataSCAN_GSM.BCCHKey, -1];
            double bsic = kpiData[StatDataSCAN_GSM.BSICKey, -1];
            if (!double.IsNaN(bcch) && !double.IsNaN(bsic))
            {
                cell = CellManager.GetInstance().GetNearestCell(dt, (short)bcch, (byte)bsic, ltLng, ltLat);
            }
            if (cell != null)
            {
                (kpiData as StatDataSCAN_GSM).AddCellStatData(cell.ID, cellIndicatorDic);
            }
        }

        private static void addStatDataSCAN_TD(KPIStatDataBase kpiData, double ltLng, double ltLat, Dictionary<string, double> cellIndicatorDic, DateTime dt)
        {
            ICell cell = null;
            double freq = kpiData[StatDataSCAN_TD.ChannelKey, -1];
            double cpi = kpiData[StatDataSCAN_TD.CPIKey, -1];
            if (!double.IsNaN(freq) && !double.IsNaN(cpi))
            {
                cell = CellManager.GetInstance().GetNearestTDCell(dt, (short)freq, (short)cpi, ltLng, ltLat);
            }
            if (cell != null)
            {
                (kpiData as StatDataSCAN_TD).AddCellStatData(cell.ID, cellIndicatorDic);
            }
        }

        private static void addStatDataSCAN_LTE(KPIStatDataBase kpiData, double ltLng, double ltLat, Dictionary<string, double> cellIndicatorDic, DateTime dt)
        {
            ICell cell = null;
            double freq = kpiData[StatDataSCAN_LTE.CellFreqKey, -1];
            double pci = kpiData[StatDataSCAN_LTE.CellPCIKey, -1];
            if (!double.IsNaN(freq) && !double.IsNaN(pci))
            {
                cell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(dt, (int?)freq, (int?)pci, ltLng, ltLat);
            }
            if (cell != null)
            {
                (kpiData as StatDataSCAN_LTE).AddCellStatData(cell.ID, cellIndicatorDic);
            }
        }

        private static void addStatDataSCAN_WCDMA(KPIStatDataBase kpiData, double ltLng, double ltLat, Dictionary<string, double> cellIndicatorDic, DateTime dt)
        {
            ICell cell = null;
            double freq = kpiData[StatDataSCAN_WCDMA.ChannelKey, -1];
            double cpi = kpiData[StatDataSCAN_WCDMA.CPIKey, -1];
            if (!double.IsNaN(freq) && !double.IsNaN(cpi))
            {
                cell = CellManager.GetInstance().GetNearestWCell(dt, (short)freq, (short)cpi, ltLng, ltLat);
            }
            if (cell != null)
            {
                (kpiData as StatDataSCAN_WCDMA).AddCellStatData(cell.ID, cellIndicatorDic);
            }
        }

        protected override bool isKPIDataContent(Package package, out KPIStatDataBase statData)
        {
            statData = null;
            if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_GSM
                   || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_DTGSM
                   || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_GSM)
            {
                statData = new StatDataSCAN_GSM();
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_LTE_TOPN
                  || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_LTETOPN
                  || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_LTE_FREQSPECTRUM)
            {
                statData = new StatDataSCAN_LTE();
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_TDSCDMA
                  || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_TD)
            {
                statData = new StatDataSCAN_TD();
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_WCDMA
                  || package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_DTWCDMA)
            {
                statData = new StatDataSCAN_WCDMA();
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_CDMA)
            {
                statData = new StatDataSCAN_CDMA();
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_COVER_GRID_SCAN_NBIOT_TOPN
                || package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_SCAN_NBIOT_TOPN
                || package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_SCAN_NBIOTTOPN)
            {
                statData = new StatDataSCAN_NBIOT();
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_NR
              || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_NR_FREQSPECTRUM)
            {
                statData = new StatDataSCAN_NR();
            }
            return statData != null;
        }

        protected virtual bool isValidStatImg(GridUnitBase gridBase)
        {
            return true;
        }


    }
}
