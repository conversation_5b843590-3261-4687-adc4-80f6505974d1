﻿using MasterCom.ES.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.NOP
{
    public class NodeConnection
    {
        public NodeEntry CurNode { get; set; }
        public NodeEntry NextNode { get; set; }
        public bool IsDownFirst { get; set; } = true;
        public bool IsClickMid { get; set; } = false;
        [CategoryAttribute("连接信息"), DescriptionAttribute("父节点")]
        public string CurProcName
        {
            get
            {
                if (this.CurNode != null)
                {
                    return CurNode.ExpString;
                }
                return "";
            }
        }
        [CategoryAttribute("连接信息"), DescriptionAttribute("子节点")]
        public string NextProcName
        {
            get
            {
                if (this.NextNode != null)
                {
                    return NextNode.ExpString;
                }
                return "";
            }
        }
        private int rfromX = 0;
        [CategoryAttribute("连接信息"), DescriptionAttribute("RFromX")]
        public int RFromX
        {
            get
            {
                return rfromX;
            }
            set
            {
                if (CurNode != null && value <= CurNode.Width / 2 && value >= -CurNode.Width / 2)
                {
                    rfromX = value;
                }
            }
        }
        private int rfromY = 0;
        [CategoryAttribute("连接信息"), DescriptionAttribute("RFromY")]
        public int RFromY
        {
            get
            {
                return rfromY;
            }
            set
            {
                if (CurNode != null)
                {
                    if (CurNode.Type == NodeType.Condition && (value == 0 || value == CurNode.Height / 2))
                    {
                        rfromY = value;
                    }
                    else if (value == CurNode.Height / 2)
                    {
                        rfromY = value;
                    }
                }
            }
        }
        private int rtoX = 0;
        [CategoryAttribute("连接信息"), DescriptionAttribute("RToX")]
        public int RToX
        {
            get
            {
                return rtoX;
            }
            set
            {
                if (NextNode != null && value <= NextNode.Width / 2 && value >= -NextNode.Width / 2)
                {
                    rtoX = value;
                }
            }
        }
        public int RToY
        {
            get
            {
                return -NextNode.Height / 2;
            }
        }
        public int FromX
        {
            get
            {
                if (CurNode == null)
                {
                    return 0;
                }
                return CurNode.XPos + RFromX;
            }
        }
        public int FromY
        {
            get
            {
                if (CurNode == null)
                {
                    return 0;
                }
                return CurNode.YPos + RFromY;
            }
        }
        public int ToX
        {
            get
            {
                if (NextNode == null)
                {
                    return 0;
                }
                return NextNode.XPos + RToX;
            }
        }
        public int ToY
        {
            get
            {
                if (NextNode == null)
                {
                    return 0;
                }
                return NextNode.YPos + RToY;
            }
        }
        private int midPos = 0;
        [CategoryAttribute("连接信息"), DescriptionAttribute("MidPos")]
        public int MidPos
        {
            get
            {
                return midPos;
            }
            set
            {
                if (value > 0 && value < (NextNode.YPos - NextNode.Height / 2) - (CurNode.YPos + CurNode.Height / 2))
                {
                    midPos = value;
                }
            }
        }

        public int DownMidY
        {
            get
            {
                return midPos + CurNode.Height / 2;
            }
        }
        public NodeConnection(NodeEntry cur, NodeEntry next)
        {
            this.CurNode = cur;
            this.NextNode = next;
            this.InitLocation();
        }

        public void InitLocation()
        {
            bool set = false;
            if (CurNode.Type == NodeType.Condition)
            {
                if (NextNode.XPos < CurNode.XPos - CurNode.Width / 2)
                {//左边
                    this.IsDownFirst = false;
                    this.RFromX = 0 - this.CurNode.Width / 2;
                    set = true;
                }
                else if (NextNode.XPos > CurNode.XPos + CurNode.Width / 2)
                {//右边
                    this.IsDownFirst = false;
                    this.RFromX = this.CurNode.Width / 2;
                    set = true;
                }
            }
            if (!set)
            {
                this.RFromY = this.CurNode.Height / 2;
            }
            this.midPos = (this.NextNode.YPos - this.NextNode.Height / 2
                - this.CurNode.YPos - this.CurNode.Height / 2) / 2;
        }

        public bool IsNodeNull()
        {
            return this.CurNode == null || this.NextNode == null;
        }

        public void AutoMove()
        {
            if (CurNode.Type == NodeType.Condition && !IsDownFirst)
            {
                if (NextNode.XPos < CurNode.XPos - CurNode.Width / 2)
                {//左边
                    this.IsDownFirst = false;
                    this.RFromX = 0 - this.CurNode.Width / 2;
                    this.RFromY = 0;
                }
                else if (NextNode.XPos > CurNode.XPos + CurNode.Width / 2)
                {//右边
                    this.IsDownFirst = false;
                    this.RFromX = this.CurNode.Width / 2;
                    this.RFromY = 0;
                }
                else
                {
                    this.IsDownFirst = true;
                    this.RFromX = 0;
                    this.RFromY = this.CurNode.Height / 2;
                    this.midPos = (this.NextNode.YPos - this.CurNode.YPos) / 2;
                }
            }
        }

        public List<ConnectLine> GetConnectLine(int xoffset, int yoffset)
        {
            List<ConnectLine> ret = new List<ConnectLine>();
            int midY = CurNode.YPos + DownMidY;
            if (IsDownFirst)
            {
                ret.Add(new ConnectLine(FromX + xoffset, FromY + yoffset, FromX + xoffset, midY + yoffset));
                ret.Add(new ConnectLine(FromX + xoffset, midY + yoffset, ToX + xoffset, midY + yoffset));
                ret.Add(new ConnectLine(ToX + xoffset, midY + yoffset, ToX + xoffset, ToY + yoffset));
            }
            else
            {
                ret.Add(new ConnectLine(FromX + xoffset, FromY + yoffset, ToX + xoffset, FromY + yoffset));
                ret.Add(new ConnectLine(ToX + xoffset, FromY + yoffset, ToX + xoffset, ToY + yoffset));
            }
            return ret;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["RFromX"] = RFromX;
                param["RFromY"] = RFromY;
                param["RToX"] = RToX;
                param["MidPos"] = MidPos;
                param["IsDownFirst"] = IsDownFirst;
                return param;
            }
            set
            {
                this.RFromX = (int)value["RFromX"];
                this.RFromY = (int)value["RFromY"];
                this.RToX = (int)value["RToX"];
                this.MidPos = (int)value["MidPos"];
                if (value.ContainsKey("IsDownFirst"))
                {
                    this.IsDownFirst = (bool)value["IsDownFirst"];
                }
            }
        }
    }

    public class ConnectLine
    {
        public Point P1 { get; set; }
        public Point P2 { get; set; }
        public ConnectLine(Point p1, Point p2)
        {
            this.P1 = p1;
            this.P2 = p2;
        }
        public ConnectLine(int x1, int y1, int x2, int y2)
            : this(new Point(x1, y1), new Point(x2, y2))
        {

        }
    }
    public enum RectType
    {
        None = 0,
        From = 1,
        To = 2
    }
}
