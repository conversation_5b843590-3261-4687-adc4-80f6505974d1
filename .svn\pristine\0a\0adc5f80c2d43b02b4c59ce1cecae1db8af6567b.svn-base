﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.BaseInfo;
using MasterCom.Util.UiEx;
using System.Text.RegularExpressions;

namespace MasterCom.RAMS.Model
{
    public partial class UserListForm : BaseForm
    {
        private bool isAdmin = false;
        List<UserStatusInfo> userStatusList;
        public UserListForm(List<UserStatusInfo> userStatusList)
            : base()
        {
            InitializeComponent();
            this.userStatusList = userStatusList;

            if (!MainModel.PermissionManager.HasQueriedUsers)
            {
                QueryUsers query = new QueryUsers(mainModel);
                query.Query();
                MainModel.PermissionManager.Users = query.UserList;
            }

            List<User> cloneUsers = new List<User>();
            cloneUsers.Add(mainModel.User.Clone());
            refreashListView(cloneUsers);
            this.btnInputAdminPw.Visible = MainModel.User.DBID == -1;
#if NotModifyPassword
            if (MainModel.User.DBID != -1)
            {
                btnModify.Visible = btnSubmit.Visible = false;
                gvUser.DoubleClick -= new System.EventHandler(this.gvUser_DoubleClick);
            }
#endif
            gcState.Visible = false;
            gcProp.Visible = false;
            gcLastLogin.Visible = false;
            gcBelong.Visible = false;
        }

        private List<User> curDisplayUsers = new List<User>();
        private void refreashListView(List<User> userSet)
        {
            curDisplayUsers = userSet;
            gridCtrlUser.DataSource = curDisplayUsers;
            gridCtrlUser.RefreshDataSource();
        }

        private List<User> usersRemove = new List<User>();
        /// <summary>
        /// 包括新增，修改的用户
        /// </summary>
        private List<User> usersModify = new List<User>();

        private void gvUser_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxE = e as DevExpress.Utils.DXMouseEventArgs;
            int rowHd = gvUser.CalcHitInfo(dxE.Location).RowHandle;
            if (rowHd >= 0)
            {
                User usr = gvUser.GetRow(rowHd) as User;
                modifyUser(usr);
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            UserInfoForm frm = new UserInfoForm(mainModel.PermissionManager.Users, null, true, userStatusList);
            if (frm.ShowDialog(this) == DialogResult.OK)
            {
                curDisplayUsers.Add(frm.User);
                usersModify.Add(frm.User);
                mainModel.PermissionManager.Users.Add(frm.User);
                gridCtrlUser.RefreshDataSource();
            }
        }

        private void btnModify_Click(object sender, EventArgs e)
        {
            User usr = gvUser.GetFocusedRow() as User;
            if (usr == null)
            {
                MessageBox.Show("请选择账号！");
                return;
            }
            modifyUser(usr);
        }

        private void modifyUser(User user)
        {
            UserInfoForm frm = new UserInfoForm(mainModel.PermissionManager.Users, user, user.DBID == -1 || isAdmin, userStatusList);
            if (frm.ShowDialog(this) == DialogResult.OK)
            {
                if (!usersModify.Contains(user))
                {
                    usersModify.Add(user);
                }
                gridCtrlUser.RefreshDataSource();
            }
        }

        private void btnRemove_Click(object sender, EventArgs e)
        {
            int[] hdRows = gvUser.GetSelectedRows();
            if (hdRows.Length == 0)
            {
                MessageBox.Show("请选择账号！");
                return;
            }
            for (int i = 0; i < hdRows.Length; i++)
            {
                User usr = gvUser.GetRow(hdRows[i]) as User;
                if (usr!=null)
                {
                    usersRemove.Add(usr);
                    User old = mainModel.PermissionManager.Users.Find(delegate(User x) { return x.ID == usr.ID; });
                    if (old != null)
                    {
                        mainModel.PermissionManager.Users.Remove(old);
                    }
                    curDisplayUsers.Remove(usr);
                }
            }
            gridCtrlUser.RefreshDataSource();
        }

        private void btnSubmit_Click(object sender, EventArgs e)
        {
            if (usersRemove.Count > 0 || usersModify.Count > 0)
            {
                WaitTextBox.Show("正在提交数据...", updateInThread);
                foreach (User item in usersModify)
                {
                    if (item.ID == mainModel.User.ID)
                    {
                        mainModel.User = item.Clone();
                    }
                    int idx = getUserIndex(item);
                    if (idx != -1)
                    {
                        mainModel.PermissionManager.Users[idx] = item.Clone();
                    }
                }
                usersRemove.Clear();
                usersModify.Clear();
            }
            else
            {
                MessageBox.Show("没有需要提交的数据！");
            }
        }

        private int getUserIndex(User item)
        {
            return mainModel.PermissionManager.Users.FindIndex(delegate (User x) { return x.ID == item.ID; });
        }

        private void updateInThread()
        {
            try
            {
                if (usersRemove.Count > 0)
                {
                    UserDBOperator op = new UserDBOperator(usersRemove, true);
                    op.Query();

                    StringBuilder strbUserRemove = new StringBuilder();
                    foreach (User user in usersRemove)
                    {
                        strbUserRemove.Append(user.LoginName + "、");
                    }
                    mainModel.MainForm.MakeLog(new MasterCom.RAMS.UserMng.LogInfoItem(4, 100, 104
                        , "删除用户：" + strbUserRemove.Remove(strbUserRemove.Length - 1, 1)));
                }
                if (usersModify.Count > 0)
                {
                    StringBuilder strbUserNames = getChangedUsers();
                    UserDBOperator op = new UserDBOperator(usersModify, false);
                    op.Query();

#if LoginManage
                    UpdateUserPwdRecord opPwd = new UpdateUserPwdRecord(usersModify);
                    opPwd.Query();
#else
                    UpdateUserPwd opPwd = new UpdateUserPwd(usersModify, true);
                    opPwd.Query();
#endif
                    mainModel.MainForm.MakeLog(new MasterCom.RAMS.UserMng.LogInfoItem(5, 100, 103, "修改用户"));
                    if (strbUserNames.Length > 0)
                    {
                        mainModel.MainForm.MakeLog(new MasterCom.RAMS.UserMng.LogInfoItem(5, 200, 201
                            , "修改用户密码：" + strbUserNames.Remove(strbUserNames.Length - 1, 1)));
                    }
                }
            }
            finally 
            {
                WaitTextBox.Close();
            }
           
        }

        private StringBuilder getChangedUsers()
        {
            StringBuilder strbUserNames = new StringBuilder();
            #region 记录修改密码信息
            foreach (User user in usersModify)
            {
                if (!string.IsNullOrEmpty(user.NewPassword))
                {
                    if (user.LoginName == mainModel.User.LoginName)
                    {//判断当前登录用户的新旧密码是否一样
                        if (user.Password != user.NewPassword)
                        {
                            strbUserNames.Append(user.LoginName + "、");
                        }
                    }
                    else
                    {//非当前登录用户时，Password都为空，只要NewPassword不为空即认为修改密码
                        strbUserNames.Append(user.LoginName + "、");
                    }
                }
            }
            #endregion
            return strbUserNames;
        }

        private void btnInputAdminPw_Click(object sender, System.EventArgs e)
        {
            MasterCom.Util.TextInputBox box = new MasterCom.Util.TextInputBox("请输入系统管理员口令", "口令", null);
            box.SetPwdMode(true);
            while (box.ShowDialog() == DialogResult.OK)
            {
                if (box.TextInput == "mastercom168")
                {
                    btnAdd.Visible = btnRemove.Visible = isAdmin = true;
                    List<User> userSet = new List<User>();
                    foreach (User item in mainModel.PermissionManager.Users)
                    {
                        userSet.Add(item.Clone());
                    }
                    refreashListView(userSet);
                    break;
                }
            }
        }
    }
}
