﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.ES.Data;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTLteRxQualAnaByRegion : ZTGSMRxQualAnaByRegion
    {
        public ZTLteRxQualAnaByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }
        protected override GSMRxQualPointInfo getInfoInstance(int SN, string reason, TestPoint tp)
        {
            return new LTERxQualPointInfo(SN, reason, tp);
        }
        protected override Cell getCell(TestPoint tp)
        {
            return tp.GetMainCell_LTE_GSM();
        }
        protected override bool getCondition(out GSMRxQualAnaCondition rxQualCond)
        {
            rxQualCond = new GSMRxQualAnaCondition();
            ZTGSMRxQualAnaSetForm dlg = new ZTGSMRxQualAnaSetForm();
            dlg.InitForLTE();
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            dlg.getCondition(out rxQualCond);
            rxQualCond.FreqInterference_C2I = int.MinValue;
            return true;
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_DM_RxLevSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_SC_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_SC_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_SC_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_SC_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_DM_RxQualSub";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_gsm_NC_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LTE NoMainCell");
            tmpDic.Add("themeName", (object)"LTE RxLevSub");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }
        protected override Cell4GSMRxQual getCellTag(TestPoint tp, Cell cell)
        {
            Cell4GSMRxQual cellTag = new Cell4GSMRxQual();
            if (cell != null)
            {
                cellTag.Name = cell.Name;
                cellTag.LAC = cell.LAC;
                cellTag.CI = cell.CI;
                cellTag.Longitude = cell.Longitude;
                cellTag.Latitude = cell.Latitude;
            }
            else
            {
                cellTag.Name = "未知小区";
                if (tp["lte_gsm_SC_LAC"] != null)
                {
                    cellTag.LAC = (int)tp["lte_gsm_SC_LAC"];
                }
                else
                {
                    cellTag.LAC = 0;
                }
                if (tp["lte_gsm_SC_CI"] != null)
                {
                    cellTag.CI = (int)tp["lte_gsm_SC_CI"];
                }
                else
                {
                    cellTag.CI = 0;
                }
                cellTag.Longitude = 0;
                cellTag.Latitude = 0;
            }
            return cellTag;
        }
        /// <summary>
        ///准备查询质差原因所需的事件
        /// </summary>
        protected override void PrepareEvents()
        {
            DIYEventByRegion queryEvent = new DIYEventByRegion(MainModel);
            queryEvent.SetIsAddEventToDTDataManager(false);
            queryEvent.SetSaveAsFileEventsDic(true);
            queryEvent.showEventChooser = false;
            queryEvent.IsQueryAllEvents = false;
            List<int> eventIds = new List<int>();
            eventIds.Add(1146);//VOLTE eSRVCC HandOver Success(LTE -> GSM)  ==> gsm:17
            //eventIds.Add(1134);//LTE切换不及时事件
            eventIds.Add(1074); //VoLte MO Call Established     ==> gsm:4
            eventIds.Add(1080);//VOLTE MO Block Call  ==> gsm:10
            condition.EventIDs = eventIds;
            queryEvent.SetQueryCondition(condition);
            queryEvent.Query();
            fileEventDic = queryEvent.fileEventsDic;
        }
        protected override bool isValidTestPoint(TestPoint tp)
        {
            bool isValid = false;
            if (tp is LTETestPointDetail)
            {
                int? rxQual = (int?)(byte?)tp["lte_gsm_DM_RxQualSub"];
                if (rxQual != null && rxQual >= 0 && rxQual <= 7)
                {
                    try
                    {
                        return Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
                    }
                    catch
                    {
                        try
                        {
                            return Condition.Geometorys.GeoOp2.CheckPointInRegion(tp.Longitude, tp.Latitude);//网络体检后台
                        }
                        catch (Exception)
                        {
                            //continue
                        }
                        return false;
                    }
                }
            }
            return isValid;
        }
        protected override bool isPoorRxQual(TestPoint tp)
        {
            int? rxqual = (int?)(byte?)tp["lte_gsm_DM_RxQualSub"];
            if (rxqual < 5)
            {
                return false;
            }
            return true;
        }
        /// <summary>
        /// 是否弱覆盖
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isPoorCover(TestPoint tp)
        {
            int? sRxLev = (int?)(short?)tp["lte_gsm_DM_RxLevSub"];
            int? nbRxLev = (int?)(short?)tp["lte_gsm_NC_RxLev", 0];
            if (sRxLev != null && nbRxLev != null 
                && sRxLev <= rxQualCond.WeakCover_ServCell_PccpchRscp 
                && nbRxLev <= rxQualCond.WeakCover_NBCell_PccpchRscp)
            {
                return true;
            }
            return false;
        }
        /// <summary>
        /// 是否过覆盖
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isCoverLap(TestPoint tp)
        {
            Cell mainCell = tp.GetMainCell_LTE_GSM();

            if (mainCell != null)
            {
                double cellDistance = mainCell.GetDistance((tp.Longitude), (tp.Latitude));
                double maxDistance = (double)rxQualCond.LapCover_Ratio * CfgDataProvider.CalculateRadius(mainCell, 3, false);

                int? sRxLev = (int?)(short?)tp["lte_gsm_DM_RxLevSub"];
                if (sRxLev != null && sRxLev >= rxQualCond.LapCover_PccpchRscp && cellDistance >= maxDistance)
                {
                    return true;
                }
                return false;
            }
            return false;
        }
        /// <summary>
        /// 是否覆盖杂乱
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isNoMainCell(TestPoint tp)
        {
            int count = 0;
            List<int> rxLevList = new List<int>();

            //主服场强
            int? sRxLev = (int?)(short?)tp["lte_gsm_DM_RxLevSub"];
            if (sRxLev != null)
            {
                rxLevList.Add((int)sRxLev);
            }
            else
            {
                return false;
            }

            //邻区场强
            try
            {
                for (int i = 0; i < 6; i++)
                {
                    int? nbRxLev = (int?)(short?)tp["lte_gsm_NC_RxLev", i];
                    if (nbRxLev == null)
                    {
                        break;
                    }
                    else
                    {
                        rxLevList.Add((int)nbRxLev);
                    }
                }

                //排序后统计与最强信号6dB内的小区数量
                if (rxLevList.Count > 0)
                {
                    rxLevList.Sort();

                    int index = rxLevList.Count;
                    float maxRscp = rxLevList[index - 1];

                    for (int i = 0; i < index; i++)
                    {
                        if ((maxRscp - rxLevList[i]) < rxQualCond.NoMainCover_PccpchRscp_Span)
                        {
                            count++;
                        }
                    }
                }
            }
            catch
            {
                //continue
            }
            return count >= rxQualCond.NoMainCover_CellCount;
        }
        /// <summary>
        /// 是否室分小区
        /// </summary>
        protected override bool isIndoor(TestPoint tp)
        {
            Cell mainCell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (ushort?)(int?)tp["lte_gsm_SC_LAC"], (ushort?)(int?)tp["lte_gsm_SC_CI"], (short?)tp["lte_gsm_SC_BCCH"], (byte?)tp["lte_gsm_SC_BSIC"], tp.Longitude, tp.Latitude);
            if (mainCell != null)
            {
                if (mainCell.Type == BTSType.Indoor)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            return false;
        }
        /// <summary>
        /// 是否切换不合理 
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isHandoverProblem(TestPoint tp)
        {
            if (fileEventDic.ContainsKey(tp.FileID))
            {
                List<Event> evtList = fileEventDic[tp.FileID];
                List<Event> hoEvtList = getHoEvtList(evtList);

                int i = 0;
                foreach (Event evt in hoEvtList)
                {
                    i++;
                    DateTime dCurTime = evt.DateTime;
                    DateTime dNextTime = getNextTime(hoEvtList, i, dCurTime);

                    //切换前后2秒比较
                    int ibeforeHoBcch = int.Parse(evt["Value1"].ToString());
                    int iafterHoBcch = int.Parse(evt["Value3"].ToString());
                    if (ibeforeHoBcch >= 128 || iafterHoBcch < 128)
                    {
                        int ibeforeHoRelev = int.Parse(evt["Value6"].ToString());
                        int iafterHoRelev = int.Parse(evt["Value8"].ToString());
                        if (ibeforeHoRelev > iafterHoRelev
                            && tp.DateTime >= dCurTime && tp.DateTime <= dNextTime)
                        {
                            return true;
                        }
                    }
                }
                //来自gsm相关代码，先留着
                bool isHandoverProblem = getGsmHandoverProblem(tp, evtList);
                if (isHandoverProblem)
                {
                    return true;
                }
            }

            isHandoverFrequently(tp);  //切换频繁归类到切换不合理

            return false;
        }

        private bool getGsmHandoverProblem(TestPoint tp, List<Event> evtList)
        {
            foreach (Event evt in evtList)
            {
                if (evt.ID == 964
                    && tp.DateTime >= evt.DateTime.AddSeconds(-rxQualCond.HOAbnormal_Span)
                    && tp.DateTime <= evt.DateTime.AddSeconds(rxQualCond.HOAbnormal_Span))
                {
                    //与第一邻区比较
                    return true;
                }
            }
            return false;
        }

        private static List<Event> getHoEvtList(List<Event> evtList)
        {
            List<Event> hoEvtList = new List<Event>();

            foreach (Event evt in evtList)
            {
                if (evt.ID == 1146) //gsm:17
                {
                    hoEvtList.Add(evt);
                }
            }

            return hoEvtList;
        }

        private static DateTime getNextTime(List<Event> hoEvtList, int i, DateTime dCurTime)
        {
            DateTime dNextTime;
            if (hoEvtList.Count <= i) //最后一个事件
            {
                dNextTime = dCurTime.AddSeconds(10);
            }
            else
            {
                DateTime tmpTime = hoEvtList[i].DateTime;//下一事件时间
                if (tmpTime >= dCurTime.AddSeconds(10))
                {
                    dNextTime = dCurTime.AddSeconds(10);
                }
                else
                {
                    dNextTime = tmpTime;
                }
            }

            return dNextTime;
        }
    }

    class LTERxQualPointInfo : GSMRxQualPointInfo
    {

        public LTERxQualPointInfo(int SN, string reason, TestPoint tp)
            :base(SN,reason,tp)
        {
        }
        protected override void getParam(TestPoint tp, out object rxlev, out int? c2i)
        {
            rxlev = (int?)(short?)tp["lte_gsm_DM_RxLevSub"];
            c2i = null;
        }
    }
}
