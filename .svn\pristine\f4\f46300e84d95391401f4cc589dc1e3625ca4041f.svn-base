﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using System.Reflection;

namespace MasterCom.RAMS.ZTFunc
{
    public class SimpleGridManager : ShowFuncForm
    {
        public SimpleGridManager(MainModel mm) : base(mm)
        {
        }

        public override string Name
        {
            get { return "生成网格图层"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20034, this.Name);
        }

        protected override void showForm()
        {
            object obj = MainModel.GetObjectFromBlackboard(typeof(SimpleGridControlForm).FullName);
            SimpleGridControlForm form = obj == null ? null : obj as SimpleGridControlForm;
            if (form == null || form.IsDisposed)
            {
                form = new SimpleGridControlForm(MainModel);
            }
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }
    }

    public struct SimpleGridCondition
    {
        public DbPoint StartPoint { get; set; }
        public MapWinGIS.Shape Region { get; set; }
        public double XStep { get; set; }
        public double YStep { get; set; }
    }

    public class SimpleGridCreater
    {
        private readonly SimpleGridCondition cond;
        private DbPoint ltPoint;
        private int xCount;
        private int yCount;
        private MapOperation2 mop2;

        public SimpleGridCreater(SimpleGridCondition condition)
        {
            cond = condition;
        }

        public MapWinGIS.Shapefile Create()
        {
            if (!IsValidCondition())
            {
                return null;
            }

            Init();
            MapWinGIS.Shape[,] shapes = new MapWinGIS.Shape[yCount, xCount];
            for (int i = 0; i < yCount; ++i)
            {
                double ltY = ltPoint.y + -cond.YStep * i;
                double rbY = ltY + -cond.YStep;
                for (int j = 0; j < xCount; ++j)
                {
                    double ltX = ltPoint.x + cond.XStep * j;
                    double rbX = ltX + cond.XStep;

                   shapes[i, j] = CreateShape(ltX, ltY, rbX, rbY);
                }
            }

            return CreateFile(shapes);
        }

        private MapWinGIS.Shapefile CreateFile(MapWinGIS.Shape[,] shapes)
        {
            MapWinGIS.Shapefile shp = new MapWinGIS.Shapefile();
            shp.CreateNew("", MapWinGIS.ShpfileType.SHP_POLYGON);

            int fieldIndex = 0;
            MapWinGIS.Field field = new MapWinGIS.Field();
            field.Name = "Name";
            field.Type = MapWinGIS.FieldType.STRING_FIELD;
            shp.EditInsertField(field, ref fieldIndex, null);

            int shapeIndex = 0;
            int row = shapes.GetLength(0);
            int col = shapes.GetLength(1);
            for (int i = 0; i < row; ++i)
            {
                for (int j = 0; j < col; ++j)
                {
                    if (shapes[i, j] == null)
                    {
                        continue;
                    }
                    shp.EditInsertShape(shapes[i, j], ref shapeIndex);
                    shp.EditCellValue(fieldIndex, shapeIndex, string.Format("网格{0}-{1}", i + 1, j + 1));
                    ++shapeIndex;
                }
            }

            return shp;
        }

        private void Init()
        {
            MapWinGIS.Extents extent = cond.Region.Extents;
            int x1 = GridCountInCoord(cond.StartPoint.x, extent.xMin, -cond.XStep);
            int x2 = GridCountInCoord(cond.StartPoint.x, extent.xMax, cond.XStep);
            int y1 = GridCountInCoord(cond.StartPoint.y, extent.yMax, cond.YStep);
            int y2 = GridCountInCoord(cond.StartPoint.y, extent.yMin, -cond.YStep);
            xCount = x1 + x2;
            yCount = y1 + y2;
            ltPoint = new DbPoint(cond.StartPoint.x + -cond.XStep * x1,
                cond.StartPoint.y + cond.YStep * y1);
        }

        private MapWinGIS.Shape CreateShape(double ltX, double ltY, double rbX, double rbY)
        {
            if (!IsGridInRegion(ltX, ltY, rbX, rbY))
            {
                return null;
            }

            MapWinGIS.Shape shape = new MapWinGIS.Shape();
            shape.ShapeType = MapWinGIS.ShpfileType.SHP_POLYGON;
            int ptIndex = 0;

            MapWinGIS.Point pt1 = new MapWinGIS.Point();
            pt1.x = ltX; pt1.y = ltY;
            shape.InsertPoint(pt1, ref ptIndex);
            ++ptIndex;

            MapWinGIS.Point pt2 = new MapWinGIS.Point();
            pt2.x = rbX; pt2.y = ltY;
            shape.InsertPoint(pt2, ref ptIndex);
            ++ptIndex;

            MapWinGIS.Point pt3 = new MapWinGIS.Point();
            pt3.x = rbX; pt3.y = rbY;
            shape.InsertPoint(pt3, ref ptIndex);
            ++ptIndex;

            MapWinGIS.Point pt4 = new MapWinGIS.Point();
            pt4.x = ltX; pt4.y = rbY;
            shape.InsertPoint(pt4, ref ptIndex);
            ++ptIndex;

            shape.InsertPoint(pt1, ref ptIndex);
#if DEBUG
            if (shape.numPoints != 5 || !shape.IsValid)
            {
                throw (new Exception(
                    string.Format("shape.numPoints = {0}, InvalidReason: {1}", 
                    shape.numPoints, shape.IsValidReason)));
            }
#endif 
            return shape;
        }

        private bool IsValidCondition()
        {
            if (cond.Region == null || cond.Region.Area == 0)
            {
                return false;
            }

            mop2 = new MapOperation2();
            mop2.FillPolygon(cond.Region);

            return cond.XStep != 0 && cond.YStep != 0
                && cond.Region.Extents.xMin <= cond.StartPoint.x
                && cond.Region.Extents.xMax >= cond.StartPoint.x
                && cond.Region.Extents.yMin <= cond.StartPoint.y
                && cond.Region.Extents.yMax >= cond.StartPoint.y;
        }

        private int GridCountInCoord(double start, double end, double step)
        {
            return Convert.ToInt32(Math.Ceiling((end - start) / step));
        }

        private bool IsGridInRegion(double ltX, double ltY, double rbX, double rbY)
        {
            return mop2.CheckPointInRegion(ltX, ltY)
                || mop2.CheckPointInRegion(rbX, rbY)
                || mop2.CheckPointInRegion(ltX, rbY)
                || mop2.CheckPointInRegion(rbX, ltY);
        }
    }

    public static class SimpleGridShpLayer
    {
        private static int layerHandle = -1;
        private static AxMapWinGIS.AxMap axMap;

        public static bool Show(MapWinGIS.Shapefile shp)
        {
            if (axMap == null)
            {
                MainModel mainModel = MainModel.GetInstance();
                if (mainModel == null)
                {
                    return false;
                }
                MapForm mapForm = mainModel.MainForm.GetMapForm();
                if (mapForm == null)
                {
                    return false;
                }
                axMap = mapForm.GetMapFormControl();
            }

            if (layerHandle != -1)
            {
                Clear();
            }

            layerHandle = axMap.AddLayer(shp, true);
            return true;
        }

        public static void Clear()
        {
            if (layerHandle == -1 || axMap == null)
            {
                return;
            }

            axMap.RemoveLayer(layerHandle);
            layerHandle = -1;
        }
    }
}
