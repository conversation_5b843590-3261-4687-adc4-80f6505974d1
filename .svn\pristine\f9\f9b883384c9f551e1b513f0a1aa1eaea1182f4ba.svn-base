﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTAtuLogKPIGroup
{
    public class TemplateManager
    {
        public static readonly string CfgFileName = string.Format(System.Windows.Forms.Application.StartupPath + "/config/AtuLogGroup.xml");
        private static TemplateManager instance = null;
        private TemplateManager()
        {
            LoadCfg();
        }
        public static TemplateManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new TemplateManager();
                }
                return instance;
            }
        }
        
        public List<GroupTemplate> Templates { get; set; } = new List<GroupTemplate>();
        private List<object> cfgParam
        {
            get
            {
                List<object> rpts = new List<object>();
                foreach (GroupTemplate rpt in Templates)
                {
                    rpts.Add(rpt.Param);
                }
                return rpts;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                Templates.Clear();
                foreach (object obj in value)
                {
                    GroupTemplate rpt = new GroupTemplate();
                    rpt.Param = obj as Dictionary<string, object>;
                    Templates.Add(rpt);
                }
            }
        }

        public void LoadCfg()
        {
            if (File.Exists(CfgFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(CfgFileName);
                cfgParam = configFile.GetItemValue("AtuLogGroupCfg", "Templates") as List<object>;
            }
        }

        public void Save()
        {
            MasterCom.Util.XmlConfigFile xmlFile = new MasterCom.Util.XmlConfigFile();
            XmlElement cfgE = xmlFile.AddConfig("AtuLogGroupCfg");
            xmlFile.AddItem(cfgE, "Templates", this.cfgParam);
            xmlFile.Save(CfgFileName);
        }



    }
}
