﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTWirelessNetTest
{
    public partial class WirelessNetTestProjects : BaseDialog
    {
        private MapFormItemSelection itemSelection = new MapFormItemSelection();
        protected ItemSelectionPanel projPanel;

        public List<int> Projects { get; set; }

        public WirelessNetTestProjects(List<int> projects)
        {
            InitializeComponent();
            initProjs();

            fillProjs(projects);
        }

        /// <summary>
        /// 加载数据来源选择panel
        /// </summary>
        private void initProjs()
        {
            Projects = new List<int>();

            listViewProject.Items.Clear();
            if (MainModel.CategoryManager["Project"] != null)
            {
                projPanel = new ItemSelectionPanel(toolStripDropDownProject, listViewProject, lbProjCountHost, itemSelection, "Project", true);
                toolStripDropDownProject.Items.Clear();
                projPanel.FreshItems();
                toolStripDropDownProject.Items.Add(new ToolStripControlHost(projPanel));
            }
        }

        private void fillProjs(List<int> projects)
        {
            if (projects.Count > 0)
            {
                listViewProject.Items.Clear();
                CategoryEnum projectCate = (CategoryEnum)CategoryManager.GetInstance()["Project"];
                foreach (int projID in projects)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = projectCate[projID].Name;
                    lvi.Tag = projectCate[projID].ID;
                    listViewProject.Items.Add(lvi);
                }

                //选择列表赋值
                List<int> idSetHost = new List<int>();
                foreach (ListViewItem item in this.listViewProject.Items)
                {
                    idSetHost.Add((int)item.Tag);
                }
                projPanel.UpdateNodeState(idSetHost);
                lbProjCountHost.Text = "[" + listViewProject.Items.Count + "]";
            }
        }

        private void btnProject_Click(object sender, EventArgs e)
        {
            Point pt = new Point(btnProject.Width, btnProject.Height);
            toolStripDropDownProject.Show(btnProject, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void simpleBtnOK_Click(object sender, EventArgs e)
        {
            Projects = getSelectProjects();
            DialogResult = DialogResult.OK;
        }

        private List<int> getSelectProjects()
        {
            List<int> projectIDList = new List<int>();
            foreach (ListViewItem item in this.listViewProject.Items)
            {
                projectIDList.Add((int)item.Tag);
            }
            projectIDList.Sort();
            return projectIDList;
        }

        private void simpleBtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
