﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NbIotMgrsOverlapCoverageSetting
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.numMinRSRP = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.chkAllRSRP = new System.Windows.Forms.CheckBox();
            this.numAllMinRSRP = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.numRSRPDis = new System.Windows.Forms.NumericUpDown();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRSRP)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAllMinRSRP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPDis)).BeginInit();
            this.SuspendLayout();
            // 
            // numMinRSRP
            // 
            this.numMinRSRP.Location = new System.Drawing.Point(275, 24);
            this.numMinRSRP.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numMinRSRP.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numMinRSRP.Name = "numMinRSRP";
            this.numMinRSRP.Size = new System.Drawing.Size(120, 21);
            this.numMinRSRP.TabIndex = 6;
            this.numMinRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinRSRP.Value = new decimal(new int[] {
            84,
            0,
            0,
            -2147483648});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(204, 28);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 5;
            this.label1.Text = "最强RSRP≥";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.chkAllRSRP);
            this.groupBox1.Controls.Add(this.numAllMinRSRP);
            this.groupBox1.Controls.Add(this.label7);
            this.groupBox1.Controls.Add(this.numRSRPDis);
            this.groupBox1.Controls.Add(this.numMinRSRP);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(620, 132);
            this.groupBox1.TabIndex = 12;
            this.groupBox1.TabStop = false;
            // 
            // chkAllRSRP
            // 
            this.chkAllRSRP.AutoSize = true;
            this.chkAllRSRP.Location = new System.Drawing.Point(125, 62);
            this.chkAllRSRP.Name = "chkAllRSRP";
            this.chkAllRSRP.Size = new System.Drawing.Size(144, 16);
            this.chkAllRSRP.TabIndex = 64;
            this.chkAllRSRP.Text = "所有采样点最小RSRP≥";
            this.chkAllRSRP.UseVisualStyleBackColor = true;
            // 
            // numAllMinRSRP
            // 
            this.numAllMinRSRP.Enabled = false;
            this.numAllMinRSRP.Location = new System.Drawing.Point(275, 60);
            this.numAllMinRSRP.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numAllMinRSRP.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numAllMinRSRP.Name = "numAllMinRSRP";
            this.numAllMinRSRP.Size = new System.Drawing.Size(120, 21);
            this.numAllMinRSRP.TabIndex = 63;
            this.numAllMinRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numAllMinRSRP.Value = new decimal(new int[] {
            104,
            0,
            0,
            -2147483648});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(156, 99);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(113, 12);
            this.label7.TabIndex = 11;
            this.label7.Text = "小区最强RSRP差距≤";
            // 
            // numRSRPDis
            // 
            this.numRSRPDis.Location = new System.Drawing.Point(275, 95);
            this.numRSRPDis.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numRSRPDis.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numRSRPDis.Name = "numRSRPDis";
            this.numRSRPDis.Size = new System.Drawing.Size(120, 21);
            this.numRSRPDis.TabIndex = 12;
            this.numRSRPDis.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRSRPDis.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // NBIOTMgrsOverlapCoverageSetting
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.groupBox1);
            this.Name = "NBIOTMgrsOverlapCoverageSetting";
            ((System.ComponentModel.ISupportInitialize)(this.numMinRSRP)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAllMinRSRP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPDis)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.NumericUpDown numMinRSRP;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown numRSRPDis;
        private System.Windows.Forms.CheckBox chkAllRSRP;
        private System.Windows.Forms.NumericUpDown numAllMinRSRP;
    }
}
