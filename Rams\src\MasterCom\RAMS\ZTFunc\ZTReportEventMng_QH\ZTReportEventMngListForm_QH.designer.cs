﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTReportEventMngListForm_QH
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTReportEventMngListForm_QH));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.miCompareReplayEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.taskUP = new System.Windows.Forms.ToolStripMenuItem();
            this.allocateTask = new System.Windows.Forms.ToolStripMenuItem();
            this.CmbThirdList = new System.Windows.Forms.ToolStripComboBox();
            this.setArchived = new System.Windows.Forms.ToolStripMenuItem();
            this.redistributeTask = new System.Windows.Forms.ToolStripMenuItem();
            this.showArchived = new System.Windows.Forms.ToolStripMenuItem();
            this.gridControlReportEvent = new DevExpress.XtraGrid.GridControl();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnSN = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDwUserName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNetType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnEventType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnEventName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAreaName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnProjName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTestDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTestTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLongitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLatitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRemark = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCause = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCauseDetail = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnMethod = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnSolution = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnIsRectify = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnIsReTest = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnIsKeyProb = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnOptEffect = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDwTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDeadLine = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.gridControlArchived = new DevExpress.XtraGrid.GridControl();
            this.gridViewArchived = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlReportEvent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.tabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlArchived)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewArchived)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayEvent,
            this.miCompareReplayEvent,
            this.miExportExcel,
            this.taskUP,
            this.allocateTask,
            this.setArchived,
            this.redistributeTask,
            this.showArchived});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(173, 180);
            // 
            // miReplayEvent
            // 
            this.miReplayEvent.Name = "miReplayEvent";
            this.miReplayEvent.Size = new System.Drawing.Size(172, 22);
            this.miReplayEvent.Text = "回放事件";
            this.miReplayEvent.Click += new System.EventHandler(this.miReplayEvent_Click);
            // 
            // miCompareReplayEvent
            // 
            this.miCompareReplayEvent.Name = "miCompareReplayEvent";
            this.miCompareReplayEvent.Size = new System.Drawing.Size(172, 22);
            this.miCompareReplayEvent.Text = "对比回放事件";
            this.miCompareReplayEvent.Click += new System.EventHandler(this.miCompareReplayEvent_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(172, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // taskUP
            // 
            this.taskUP.Name = "taskUP";
            this.taskUP.Size = new System.Drawing.Size(172, 22);
            this.taskUP.Text = "任务归档";
            this.taskUP.Click += new System.EventHandler(this.taskUP_Click);
            // 
            // allocateTask
            // 
            this.allocateTask.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.CmbThirdList});
            this.allocateTask.Name = "allocateTask";
            this.allocateTask.Size = new System.Drawing.Size(172, 22);
            this.allocateTask.Text = "指派任务给...";
            // 
            // CmbThirdList
            // 
            this.CmbThirdList.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CmbThirdList.Name = "CmbThirdList";
            this.CmbThirdList.Size = new System.Drawing.Size(121, 25);
            this.CmbThirdList.SelectedIndexChanged += new System.EventHandler(this.CmbThirdList_SelectedIndexChanged);
            // 
            // setArchived
            // 
            this.setArchived.Name = "setArchived";
            this.setArchived.Size = new System.Drawing.Size(172, 22);
            this.setArchived.Text = "事件闭环";
            this.setArchived.Click += new System.EventHandler(this.setArchived_Click);
            // 
            // redistributeTask
            // 
            this.redistributeTask.Name = "redistributeTask";
            this.redistributeTask.Size = new System.Drawing.Size(172, 22);
            this.redistributeTask.Text = "重新指派...";
            this.redistributeTask.Click += new System.EventHandler(this.redistributeTask_Click);
            // 
            // showArchived
            // 
            this.showArchived.Name = "showArchived";
            this.showArchived.Size = new System.Drawing.Size(172, 22);
            this.showArchived.Text = "不显示已闭环事件";
            this.showArchived.Click += new System.EventHandler(this.showArchived_Click);
            // 
            // gridControlReportEvent
            // 
            this.gridControlReportEvent.ContextMenuStrip = this.ctxMenu;
            this.gridControlReportEvent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlReportEvent.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlReportEvent.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlReportEvent.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlReportEvent.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlReportEvent.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlReportEvent.Location = new System.Drawing.Point(3, 3);
            this.gridControlReportEvent.MainView = this.gridView;
            this.gridControlReportEvent.Name = "gridControlReportEvent";
            this.gridControlReportEvent.Size = new System.Drawing.Size(1274, 548);
            this.gridControlReportEvent.TabIndex = 5;
            this.gridControlReportEvent.UseEmbeddedNavigator = true;
            this.gridControlReportEvent.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnSN,
            this.gridColumnDwUserName,
            this.gridColumnNetType,
            this.gridColumnEventType,
            this.gridColumnEventName,
            this.gridColumnAreaName,
            this.gridColumnProjName,
            this.gridColumnTestDate,
            this.gridColumnTestTime,
            this.gridColumnFileName,
            this.gridColumnCellName,
            this.gridColumnLAC,
            this.gridColumnCI,
            this.gridColumnLongitude,
            this.gridColumnLatitude,
            this.gridColumnRemark,
            this.gridColumnCause,
            this.gridColumnCauseDetail,
            this.gridColumnMethod,
            this.gridColumnSolution,
            this.gridColumnIsRectify,
            this.gridColumnIsReTest,
            this.gridColumnIsKeyProb,
            this.gridColumnOptEffect,
            this.gridColumnDwTime,
            this.gridColumnDeadLine});
            this.gridView.GridControl = this.gridControlReportEvent;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            this.gridView.DoubleClick += new System.EventHandler(this.gridView_DoubleClick);
            // 
            // gridColumnSN
            // 
            this.gridColumnSN.Caption = "序号";
            this.gridColumnSN.FieldName = "SN";
            this.gridColumnSN.Name = "gridColumnSN";
            this.gridColumnSN.Visible = true;
            this.gridColumnSN.VisibleIndex = 0;
            this.gridColumnSN.Width = 47;
            // 
            // gridColumnDwUserName
            // 
            this.gridColumnDwUserName.Caption = "指派给...";
            this.gridColumnDwUserName.FieldName = "DwUserName";
            this.gridColumnDwUserName.Name = "gridColumnDwUserName";
            this.gridColumnDwUserName.Visible = true;
            this.gridColumnDwUserName.VisibleIndex = 1;
            // 
            // gridColumnNetType
            // 
            this.gridColumnNetType.Caption = "网络制式";
            this.gridColumnNetType.FieldName = "NetType";
            this.gridColumnNetType.Name = "gridColumnNetType";
            this.gridColumnNetType.Visible = true;
            this.gridColumnNetType.VisibleIndex = 2;
            this.gridColumnNetType.Width = 60;
            // 
            // gridColumnEventType
            // 
            this.gridColumnEventType.Caption = "事件类别";
            this.gridColumnEventType.FieldName = "EventType";
            this.gridColumnEventType.Name = "gridColumnEventType";
            this.gridColumnEventType.Visible = true;
            this.gridColumnEventType.VisibleIndex = 3;
            this.gridColumnEventType.Width = 60;
            // 
            // gridColumnEventName
            // 
            this.gridColumnEventName.Caption = "事件名称";
            this.gridColumnEventName.FieldName = "EventName";
            this.gridColumnEventName.Name = "gridColumnEventName";
            this.gridColumnEventName.Visible = true;
            this.gridColumnEventName.VisibleIndex = 4;
            this.gridColumnEventName.Width = 80;
            // 
            // gridColumnAreaName
            // 
            this.gridColumnAreaName.Caption = "区域名称";
            this.gridColumnAreaName.FieldName = "AreaName";
            this.gridColumnAreaName.Name = "gridColumnAreaName";
            this.gridColumnAreaName.Visible = true;
            this.gridColumnAreaName.VisibleIndex = 5;
            // 
            // gridColumnProjName
            // 
            this.gridColumnProjName.Caption = "测试类别";
            this.gridColumnProjName.FieldName = "ProjName";
            this.gridColumnProjName.Name = "gridColumnProjName";
            this.gridColumnProjName.Visible = true;
            this.gridColumnProjName.VisibleIndex = 6;
            // 
            // gridColumnTestDate
            // 
            this.gridColumnTestDate.Caption = "测试日期";
            this.gridColumnTestDate.FieldName = "TestDate";
            this.gridColumnTestDate.Name = "gridColumnTestDate";
            this.gridColumnTestDate.Visible = true;
            this.gridColumnTestDate.VisibleIndex = 7;
            // 
            // gridColumnTestTime
            // 
            this.gridColumnTestTime.Caption = "事件时间";
            this.gridColumnTestTime.FieldName = "TestTime";
            this.gridColumnTestTime.Name = "gridColumnTestTime";
            this.gridColumnTestTime.Visible = true;
            this.gridColumnTestTime.VisibleIndex = 8;
            // 
            // gridColumnFileName
            // 
            this.gridColumnFileName.Caption = "文件名称";
            this.gridColumnFileName.FieldName = "FileName";
            this.gridColumnFileName.Name = "gridColumnFileName";
            this.gridColumnFileName.Visible = true;
            this.gridColumnFileName.VisibleIndex = 9;
            // 
            // gridColumnCellName
            // 
            this.gridColumnCellName.Caption = "小区名称";
            this.gridColumnCellName.FieldName = "CellName";
            this.gridColumnCellName.Name = "gridColumnCellName";
            this.gridColumnCellName.Visible = true;
            this.gridColumnCellName.VisibleIndex = 10;
            // 
            // gridColumnLAC
            // 
            this.gridColumnLAC.Caption = "LAC";
            this.gridColumnLAC.FieldName = "LAC";
            this.gridColumnLAC.Name = "gridColumnLAC";
            this.gridColumnLAC.Visible = true;
            this.gridColumnLAC.VisibleIndex = 11;
            // 
            // gridColumnCI
            // 
            this.gridColumnCI.Caption = "CI";
            this.gridColumnCI.FieldName = "CI";
            this.gridColumnCI.Name = "gridColumnCI";
            this.gridColumnCI.Visible = true;
            this.gridColumnCI.VisibleIndex = 12;
            // 
            // gridColumnLongitude
            // 
            this.gridColumnLongitude.Caption = "经度";
            this.gridColumnLongitude.FieldName = "Longitude";
            this.gridColumnLongitude.Name = "gridColumnLongitude";
            this.gridColumnLongitude.Visible = true;
            this.gridColumnLongitude.VisibleIndex = 13;
            // 
            // gridColumnLatitude
            // 
            this.gridColumnLatitude.Caption = "纬度";
            this.gridColumnLatitude.FieldName = "Latitude";
            this.gridColumnLatitude.Name = "gridColumnLatitude";
            this.gridColumnLatitude.Visible = true;
            this.gridColumnLatitude.VisibleIndex = 14;
            // 
            // gridColumnRemark
            // 
            this.gridColumnRemark.Caption = "备注";
            this.gridColumnRemark.FieldName = "Remark";
            this.gridColumnRemark.Name = "gridColumnRemark";
            this.gridColumnRemark.Visible = true;
            this.gridColumnRemark.VisibleIndex = 16;
            // 
            // gridColumnCause
            // 
            this.gridColumnCause.Caption = "原因";
            this.gridColumnCause.FieldName = "Cause";
            this.gridColumnCause.Name = "gridColumnCause";
            this.gridColumnCause.Visible = true;
            this.gridColumnCause.VisibleIndex = 15;
            // 
            // gridColumnCauseDetail
            // 
            this.gridColumnCauseDetail.Caption = "原因分析";
            this.gridColumnCauseDetail.FieldName = "CauseDetail";
            this.gridColumnCauseDetail.Name = "gridColumnCauseDetail";
            this.gridColumnCauseDetail.Visible = true;
            this.gridColumnCauseDetail.VisibleIndex = 17;
            // 
            // gridColumnMethod
            // 
            this.gridColumnMethod.Caption = "问题分析";
            this.gridColumnMethod.FieldName = "Method";
            this.gridColumnMethod.Name = "gridColumnMethod";
            this.gridColumnMethod.Visible = true;
            this.gridColumnMethod.VisibleIndex = 18;
            // 
            // gridColumnSolution
            // 
            this.gridColumnSolution.Caption = "解决办法";
            this.gridColumnSolution.FieldName = "Solution";
            this.gridColumnSolution.Name = "gridColumnSolution";
            this.gridColumnSolution.Visible = true;
            this.gridColumnSolution.VisibleIndex = 19;
            // 
            // gridColumnIsRectify
            // 
            this.gridColumnIsRectify.Caption = "已整改";
            this.gridColumnIsRectify.FieldName = "IsRectify";
            this.gridColumnIsRectify.Name = "gridColumnIsRectify";
            this.gridColumnIsRectify.Visible = true;
            this.gridColumnIsRectify.VisibleIndex = 20;
            // 
            // gridColumnIsReTest
            // 
            this.gridColumnIsReTest.Caption = "已复测";
            this.gridColumnIsReTest.FieldName = "IsReTest";
            this.gridColumnIsReTest.Name = "gridColumnIsReTest";
            this.gridColumnIsReTest.Visible = true;
            this.gridColumnIsReTest.VisibleIndex = 21;
            // 
            // gridColumnIsKeyProb
            // 
            this.gridColumnIsKeyProb.Caption = "是否重点问题";
            this.gridColumnIsKeyProb.FieldName = "IsKeyProb";
            this.gridColumnIsKeyProb.Name = "gridColumnIsKeyProb";
            this.gridColumnIsKeyProb.Visible = true;
            this.gridColumnIsKeyProb.VisibleIndex = 22;
            // 
            // gridColumnOptEffect
            // 
            this.gridColumnOptEffect.Caption = "复测结果";
            this.gridColumnOptEffect.FieldName = "OptEffect";
            this.gridColumnOptEffect.Name = "gridColumnOptEffect";
            this.gridColumnOptEffect.Visible = true;
            this.gridColumnOptEffect.VisibleIndex = 23;
            // 
            // gridColumnDwTime
            // 
            this.gridColumnDwTime.Caption = "处理时间";
            this.gridColumnDwTime.FieldName = "DwTime";
            this.gridColumnDwTime.Name = "gridColumnDwTime";
            this.gridColumnDwTime.Visible = true;
            this.gridColumnDwTime.VisibleIndex = 24;
            // 
            // gridColumnDeadLine
            // 
            this.gridColumnDeadLine.Caption = "最迟反馈日期";
            this.gridColumnDeadLine.FieldName = "DealLine";
            this.gridColumnDeadLine.Name = "gridColumnDeadLine";
            this.gridColumnDeadLine.Visible = true;
            this.gridColumnDeadLine.VisibleIndex = 25;
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(1288, 581);
            this.tabControl1.TabIndex = 6;
            this.tabControl1.SelectedIndexChanged += new System.EventHandler(this.tabControl1_SelectedIndexChanged);
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.gridControlReportEvent);
            this.tabPage1.Location = new System.Drawing.Point(4, 23);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(1280, 554);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "未归档异常事件";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.gridControlArchived);
            this.tabPage2.Location = new System.Drawing.Point(4, 23);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(1280, 554);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "已归档异常事件";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // gridControlArchived
            // 
            this.gridControlArchived.ContextMenuStrip = this.ctxMenu;
            this.gridControlArchived.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlArchived.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlArchived.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlArchived.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlArchived.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlArchived.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlArchived.Location = new System.Drawing.Point(3, 3);
            this.gridControlArchived.MainView = this.gridViewArchived;
            this.gridControlArchived.Name = "gridControlArchived";
            this.gridControlArchived.Size = new System.Drawing.Size(1274, 548);
            this.gridControlArchived.TabIndex = 6;
            this.gridControlArchived.UseEmbeddedNavigator = true;
            this.gridControlArchived.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewArchived});
            this.gridControlArchived.DoubleClick += new System.EventHandler(this.gridControlArchived_DoubleClick);
            // 
            // gridViewArchived
            // 
            this.gridViewArchived.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn27,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26});
            this.gridViewArchived.GridControl = this.gridControlArchived;
            this.gridViewArchived.Name = "gridViewArchived";
            this.gridViewArchived.OptionsBehavior.Editable = false;
            this.gridViewArchived.OptionsView.ColumnAutoWidth = false;
            this.gridViewArchived.OptionsView.ShowDetailButtons = false;
            this.gridViewArchived.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "序号";
            this.gridColumn1.FieldName = "SN";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 47;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "是否闭环";
            this.gridColumn27.FieldName = "IsDone";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 1;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "处理人";
            this.gridColumn2.FieldName = "DwUserName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 2;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "网络制式";
            this.gridColumn3.FieldName = "NetType";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 3;
            this.gridColumn3.Width = 60;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "事件类别";
            this.gridColumn4.FieldName = "EventType";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 4;
            this.gridColumn4.Width = 60;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "事件名称";
            this.gridColumn5.FieldName = "EventName";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 5;
            this.gridColumn5.Width = 80;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "区域名称";
            this.gridColumn6.FieldName = "AreaName";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 6;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "测试类别";
            this.gridColumn7.FieldName = "ProjName";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 7;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "测试日期";
            this.gridColumn8.FieldName = "TestDate";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 8;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "事件时间";
            this.gridColumn9.FieldName = "TestTime";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 9;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "文件名称";
            this.gridColumn10.FieldName = "FileName";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 10;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "小区名称";
            this.gridColumn11.FieldName = "CellName";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 11;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "LAC";
            this.gridColumn12.FieldName = "LAC";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 12;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "CI";
            this.gridColumn13.FieldName = "CI";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 13;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "经度";
            this.gridColumn14.FieldName = "Longitude";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 14;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "纬度";
            this.gridColumn15.FieldName = "Latitude";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 15;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "备注";
            this.gridColumn16.FieldName = "Remark";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 17;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "原因";
            this.gridColumn17.FieldName = "Cause";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 16;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "原因分析";
            this.gridColumn18.FieldName = "CauseDetail";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 18;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "问题分析";
            this.gridColumn19.FieldName = "Method";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 19;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "解决办法";
            this.gridColumn20.FieldName = "Solution";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 20;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "已整改";
            this.gridColumn21.FieldName = "IsRectify";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 21;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "已复测";
            this.gridColumn22.FieldName = "IsReTest";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 22;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "是否重点问题";
            this.gridColumn23.FieldName = "IsKeyProb";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 23;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "复测结果";
            this.gridColumn24.FieldName = "OptEffect";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 24;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "处理时间";
            this.gridColumn25.FieldName = "DwTime";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 25;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "最迟反馈日期";
            this.gridColumn26.FieldName = "DealLine";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 26;
            // 
            // ZTReportEventMngListForm_QH
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1288, 581);
            this.Controls.Add(this.tabControl1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTReportEventMngListForm_QH";
            this.Text = "异常事件跟踪";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlReportEvent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.tabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlArchived)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewArchived)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miReplayEvent;
        private DevExpress.XtraGrid.GridControl gridControlReportEvent;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSN;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNetType;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnEventType;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnEventName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAreaName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnProjName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTestDate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTestTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLAC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLongitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLatitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRemark;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCause;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCauseDetail;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnMethod;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSolution;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnIsRectify;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnIsReTest;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnOptEffect;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDwUserName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDwTime;
        private System.Windows.Forms.ToolStripMenuItem miCompareReplayEvent;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDeadLine;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnIsKeyProb;
        private System.Windows.Forms.ToolStripMenuItem allocateTask;
        private System.Windows.Forms.ToolStripComboBox CmbThirdList;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private DevExpress.XtraGrid.GridControl gridControlArchived;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewArchived;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private System.Windows.Forms.ToolStripMenuItem setArchived;
        private System.Windows.Forms.ToolStripMenuItem redistributeTask;
        private System.Windows.Forms.ToolStripMenuItem showArchived;
        private System.Windows.Forms.ToolStripMenuItem taskUP;

    }
}