﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.UserMng;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryFileStatus : QueryBase
    {
        public QueryFileStatus(MainModel mainModel)
            : base(mainModel)
        {
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        protected override void query()
        {
            fireShowForm();
        }

        protected void fireShowForm()
        {
            QueryFileStatusInfo frm = MainModel.GetInstance().CreateResultForm(typeof(QueryFileStatusInfo)) as QueryFileStatusInfo;
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }


        public override string Name
        {
            get { return "文件状态查询"; }
        }
        public override string IconName
        {
            get { return "Images/repeater.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11032, this.Name);
        }
    }

    /// <summary>
    /// 已入库文件
    /// </summary>
    public class FileStoredInfo
    {
        private const string success = "成功";
        private const string fail = "异常";
        private const string disposing = "正在处理";
        public string FileName { get; set; }
        public int Istatus { get; set; }
        public string ProjectName { get; set; }
        public int Istime { get; set; }
        public string IsTimeToString
        {
            get
            {
                DateTime dateTmp = JavaDate.GetDateTimeFromMilliseconds(Istime * 1000L);
                return dateTmp.ToString("yyyy-MM-dd HH:mm:ss");
            }
        }
        public int Ietime { get; set; }
        public string IeTimeToString
        {
            get
            {
                DateTime dateTmp = JavaDate.GetDateTimeFromMilliseconds(Ietime * 1000L);
                return dateTmp.ToString("yyyy-MM-dd HH:mm:ss");
            }
        }
        public int Iimporttime { get; set; }
        public string IImportTimeToString
        {
            get
            {
                DateTime dateTmp = JavaDate.GetDateTimeFromMilliseconds(Iimporttime * 1000L);
                return dateTmp.ToString("yyyy-MM-dd HH:mm:ss");
            }
        }
        public int Statstatus { get; set; }
        public string FilePaths { get; set; }
        public string FileInfo { get; set; }
        public string FileTime { get; set; }
        public string ImportStatus
        {
            get
            {
                return Statstatus > 0 ? success : fail;
            }
        }
        public string EventDefineStatus
        {
            get
            {
                if (Istatus == 1 || Istatus == 8)
                {
                    return success;
                }
                else if (Istatus == 88 || Istatus == 89)
                {
                    return disposing;
                }
                else if (Istatus == 90)
                {
                    return fail;
                }
                else
                {
                    return "";
                }
            }
        }
        public string ESStatus
        {
            get
            {
                if (Istatus == 8)
                {
                    return success;
                }
                else
                {
                    return "";
                }
            }
        }
        public string GridStatStatus
        {
            get
            {
                if (Statstatus == 3 || Statstatus == 4)
                {
                    return success;
                }
                else if (Statstatus == 103)
                {
                    return fail;
                }
                else
                {
                    return "";
                }
            }
        }
        public string AreaStatStatus
        {
            get
            {
                if (Statstatus == 4)
                {
                    return success;
                }
                else if (Statstatus == 104)
                {
                    return fail;
                }
                else
                {
                    return "";
                }
            }
        }

        public static IComparer<FileStoredInfo> GetCompareaBySTime()
        {
            if (compareBySTime == null)
            {
                compareBySTime = new CompareBySTime();
            }
            return compareBySTime;
        }

        private static IComparer<FileStoredInfo> compareBySTime;
        public class CompareBySTime : IComparer<FileStoredInfo>
        {
            public int Compare(FileStoredInfo x, FileStoredInfo y)
            {
                return x.Istime - y.Istime;
            }
        }
    }

    /// <summary>
    /// 查询已入库文件
    /// </summary>
    public class QueryFileStored : DIYSQLBase
    {
        public List<FileStoredInfo> fileList { get; set; } = new List<FileStoredInfo>();
        private readonly string sqlStr;
        public QueryFileStored(MainModel mainModel, string sqlStr)
            : base(mainModel)
        {
            this.sqlStr = sqlStr;
        }

        protected override string getSqlTextString()
        {
            return sqlStr;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[7];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            fileList.Clear();

            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();

                if (package.Content.Type == Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    FileStoredInfo fileInfo = new FileStoredInfo();
                    fileInfo.FileName = package.Content.GetParamString();
                    fileInfo.ProjectName = package.Content.GetParamString();
                    fileInfo.Statstatus = package.Content.GetParamInt();
                    fileInfo.Istatus = package.Content.GetParamInt();
                    fileInfo.Iimporttime = package.Content.GetParamInt();
                    fileInfo.Istime = package.Content.GetParamInt();
                    fileInfo.Ietime = package.Content.GetParamInt();
                    fileList.Add(fileInfo);
                }
                if (package.Content.Type == Net.ResponseType.END)
                {
                    break;
                }
            }
        }

        public override string Name
        {
            get { return "已入库文件查询"; }
        }
    }

    /// <summary>
    /// 未入库文件
    /// </summary>
    public class FileNotStoredInfo
    {
        public int FileId { get; set; }
        public string FileName { get; set; }
        public string ProjectName { get; set; }
        public int Iimporttime { get; set; }
        public string IImportTimeToString
        {
            get
            {
                DateTime dateTmp = JavaDate.GetDateTimeFromMilliseconds(Iimporttime * 1000L);
                return dateTmp.ToString("yyyy-MM-dd HH:mm:ss");
            }
        }
        public int Statstatus { get; set; }
        public string FileTime { get; set; }
        public int Priority { get; set; }
        public string PriorityToString
        {
            get
            { 
                switch (Priority)
                {
                    case 1:
                    case 2:
                    case 3:
                    case 4:
                    case 5:
                        return Priority.ToString();
                    default:
                        return "";
                }
            }
        }

        public static IComparer<FileNotStoredInfo> GetCompareaByImportTime()
        {
            if (compareByImportTime == null)
            {
                compareByImportTime = new CompareByImportTime();
            }
            return compareByImportTime;
        }

        private static IComparer<FileNotStoredInfo> compareByImportTime;
        public class CompareByImportTime : IComparer<FileNotStoredInfo>
        {
            public int Compare(FileNotStoredInfo x, FileNotStoredInfo y)
            {
                return x.Iimporttime - y.Iimporttime;
            }
        }
    }

    /// <summary>
    /// 查询未入库文件
    /// </summary>
    public class QueryFileNotStored : DIYSQLBase
    {
        public List<FileNotStoredInfo> fileList { get; set; } = new List<FileNotStoredInfo>();
        private readonly string sqlStr;
        public QueryFileNotStored(MainModel mainModel, string sqlStr)
            : base(mainModel)
        {
            this.sqlStr = sqlStr;
        }

        protected override string getSqlTextString()
        {
            return sqlStr;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[5];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            fileList.Clear();

            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();

                if (package.Content.Type == Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    FileNotStoredInfo fileInfo = new FileNotStoredInfo();
                    fileInfo.FileName = package.Content.GetParamString();
                    fileInfo.ProjectName = package.Content.GetParamString();
                    fileInfo.Iimporttime = package.Content.GetParamInt();
                    fileInfo.Priority = package.Content.GetParamInt();
                    fileInfo.FileId = package.Content.GetParamInt();
                    fileList.Add(fileInfo);
                }
                if (package.Content.Type == Net.ResponseType.END)
                {
                    break;
                }
            }
        }

        public override string Name
        {
            get { return "未入库文件查询"; }
        }
    }

    public class DIYSQLUpdateAdapterFilePriority : DIYSQLBase
    {
        public string sql { get; set; }
        public DIYSQLUpdateAdapterFilePriority(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override string getSqlTextString()
        {
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[3];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public override string Name
        {
            get { return "DIYSQLUpdateAdapterFilePriority"; }
        }
    }
}