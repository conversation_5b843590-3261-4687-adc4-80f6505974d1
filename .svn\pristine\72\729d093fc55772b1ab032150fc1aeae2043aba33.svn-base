﻿namespace MasterCom.RAMS.ZTFunc.ZTTestPointBlock.Competition
{
    partial class ResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panel1 = new System.Windows.Forms.Panel();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.colorGuest = new DevExpress.XtraEditors.ColorEdit();
            this.chkGuest = new DevExpress.XtraEditors.CheckEdit();
            this.chkHost = new DevExpress.XtraEditors.CheckEdit();
            this.colorOverlap = new DevExpress.XtraEditors.ColorEdit();
            this.chkOverlap = new DevExpress.XtraEditors.CheckEdit();
            this.colorHost = new DevExpress.XtraEditors.ColorEdit();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.rbRepeat = new System.Windows.Forms.RadioButton();
            this.rbDiff = new System.Windows.Forms.RadioButton();
            this.rbAll = new System.Windows.Forms.RadioButton();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.splitContainer2 = new System.Windows.Forms.SplitContainer();
            this.gbxTime1 = new System.Windows.Forms.GroupBox();
            this.lvHost = new BrightIdeasSoftware.TreeListView();
            this.colSN1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colOverlap1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colTestPointCnt1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colTime1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLng1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLat1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colFile1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colGrid1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll1 = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll1 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport1 = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportShp1 = new System.Windows.Forms.ToolStripMenuItem();
            this.gbxTime2 = new System.Windows.Forms.GroupBox();
            this.lvGuest = new BrightIdeasSoftware.TreeListView();
            this.colSN2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colOverlap2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colTestPointCnt2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colTime2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLng2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLat2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colFile2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colGrid2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip2 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll2 = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll2 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport2 = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportShp2 = new System.Windows.Forms.ToolStripMenuItem();
            this.gbxRepeat = new System.Windows.Forms.GroupBox();
            this.lvOverlap = new BrightIdeasSoftware.TreeListView();
            this.colSN3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colTestPointCnt3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colTime3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLng3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLat3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colFile3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colGrid3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStripOverlap = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAllOverlap = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAllOverlap = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.miExportOverlap = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportShpOverlap = new System.Windows.Forms.ToolStripMenuItem();
            this.panel1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorGuest.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkGuest.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHost.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorOverlap.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkOverlap.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorHost.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            this.splitContainer2.Panel1.SuspendLayout();
            this.splitContainer2.Panel2.SuspendLayout();
            this.splitContainer2.SuspendLayout();
            this.gbxTime1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvHost)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.gbxTime2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvGuest)).BeginInit();
            this.contextMenuStrip2.SuspendLayout();
            this.gbxRepeat.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvOverlap)).BeginInit();
            this.contextMenuStripOverlap.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.groupBox2);
            this.panel1.Controls.Add(this.groupBox1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1006, 56);
            this.panel1.TabIndex = 2;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.colorGuest);
            this.groupBox2.Controls.Add(this.chkGuest);
            this.groupBox2.Controls.Add(this.chkHost);
            this.groupBox2.Controls.Add(this.colorOverlap);
            this.groupBox2.Controls.Add(this.chkOverlap);
            this.groupBox2.Controls.Add(this.colorHost);
            this.groupBox2.Location = new System.Drawing.Point(327, 3);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(598, 45);
            this.groupBox2.TabIndex = 0;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "图层显示设置";
            // 
            // colorGuest
            // 
            this.colorGuest.EditValue = System.Drawing.Color.Lime;
            this.colorGuest.Location = new System.Drawing.Point(230, 16);
            this.colorGuest.Name = "colorGuest";
            this.colorGuest.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorGuest.Size = new System.Drawing.Size(51, 21);
            this.colorGuest.TabIndex = 3;
            // 
            // chkGuest
            // 
            this.chkGuest.EditValue = true;
            this.chkGuest.Location = new System.Drawing.Point(173, 18);
            this.chkGuest.Name = "chkGuest";
            this.chkGuest.Properties.Caption = "客队：";
            this.chkGuest.Size = new System.Drawing.Size(51, 19);
            this.chkGuest.TabIndex = 2;
            this.chkGuest.CheckedChanged += new System.EventHandler(this.chkGuest_CheckedChanged);
            // 
            // chkHost
            // 
            this.chkHost.EditValue = true;
            this.chkHost.Location = new System.Drawing.Point(18, 18);
            this.chkHost.Name = "chkHost";
            this.chkHost.Properties.Caption = "主队：";
            this.chkHost.Size = new System.Drawing.Size(50, 19);
            this.chkHost.TabIndex = 2;
            this.chkHost.CheckedChanged += new System.EventHandler(this.chkHost_CheckedChanged);
            // 
            // colorOverlap
            // 
            this.colorOverlap.EditValue = System.Drawing.Color.Red;
            this.colorOverlap.Location = new System.Drawing.Point(389, 15);
            this.colorOverlap.Name = "colorOverlap";
            this.colorOverlap.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorOverlap.Size = new System.Drawing.Size(51, 21);
            this.colorOverlap.TabIndex = 3;
            // 
            // chkOverlap
            // 
            this.chkOverlap.Location = new System.Drawing.Point(332, 16);
            this.chkOverlap.Name = "chkOverlap";
            this.chkOverlap.Properties.Caption = "重叠：";
            this.chkOverlap.Size = new System.Drawing.Size(51, 19);
            this.chkOverlap.TabIndex = 2;
            this.chkOverlap.CheckedChanged += new System.EventHandler(this.chkOverlap_CheckedChanged);
            // 
            // colorHost
            // 
            this.colorHost.EditValue = System.Drawing.Color.Blue;
            this.colorHost.Location = new System.Drawing.Point(74, 16);
            this.colorHost.Name = "colorHost";
            this.colorHost.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorHost.Size = new System.Drawing.Size(51, 21);
            this.colorHost.TabIndex = 3;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.rbRepeat);
            this.groupBox1.Controls.Add(this.rbDiff);
            this.groupBox1.Controls.Add(this.rbAll);
            this.groupBox1.Location = new System.Drawing.Point(3, 3);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(318, 45);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "汇聚列表显示选择";
            // 
            // rbRepeat
            // 
            this.rbRepeat.AutoSize = true;
            this.rbRepeat.Location = new System.Drawing.Point(212, 20);
            this.rbRepeat.Name = "rbRepeat";
            this.rbRepeat.Size = new System.Drawing.Size(73, 18);
            this.rbRepeat.TabIndex = 2;
            this.rbRepeat.Text = "重叠部分";
            this.rbRepeat.UseVisualStyleBackColor = true;
            this.rbRepeat.CheckedChanged += new System.EventHandler(this.rbRepeat_CheckedChanged);
            // 
            // rbDiff
            // 
            this.rbDiff.AutoSize = true;
            this.rbDiff.Location = new System.Drawing.Point(125, 20);
            this.rbDiff.Name = "rbDiff";
            this.rbDiff.Size = new System.Drawing.Size(73, 18);
            this.rbDiff.TabIndex = 1;
            this.rbDiff.Text = "各队独有";
            this.rbDiff.UseVisualStyleBackColor = true;
            this.rbDiff.CheckedChanged += new System.EventHandler(this.rbDiff_CheckedChanged);
            // 
            // rbAll
            // 
            this.rbAll.AutoSize = true;
            this.rbAll.Checked = true;
            this.rbAll.Location = new System.Drawing.Point(33, 20);
            this.rbAll.Name = "rbAll";
            this.rbAll.Size = new System.Drawing.Size(49, 18);
            this.rbAll.TabIndex = 0;
            this.rbAll.TabStop = true;
            this.rbAll.Text = "全部";
            this.rbAll.UseVisualStyleBackColor = true;
            this.rbAll.CheckedChanged += new System.EventHandler(this.rbAll_CheckedChanged);
            // 
            // splitContainer1
            // 
            this.splitContainer1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(0, 56);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.splitContainer2);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.gbxRepeat);
            this.splitContainer1.Size = new System.Drawing.Size(1006, 379);
            this.splitContainer1.SplitterDistance = 208;
            this.splitContainer1.SplitterWidth = 5;
            this.splitContainer1.TabIndex = 3;
            // 
            // splitContainer2
            // 
            this.splitContainer2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.splitContainer2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer2.Location = new System.Drawing.Point(0, 0);
            this.splitContainer2.Name = "splitContainer2";
            // 
            // splitContainer2.Panel1
            // 
            this.splitContainer2.Panel1.Controls.Add(this.gbxTime1);
            // 
            // splitContainer2.Panel2
            // 
            this.splitContainer2.Panel2.Controls.Add(this.gbxTime2);
            this.splitContainer2.Size = new System.Drawing.Size(1004, 206);
            this.splitContainer2.SplitterDistance = 494;
            this.splitContainer2.SplitterWidth = 5;
            this.splitContainer2.TabIndex = 3;
            // 
            // gbxTime1
            // 
            this.gbxTime1.Controls.Add(this.lvHost);
            this.gbxTime1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbxTime1.Location = new System.Drawing.Point(0, 0);
            this.gbxTime1.Name = "gbxTime1";
            this.gbxTime1.Size = new System.Drawing.Size(490, 202);
            this.gbxTime1.TabIndex = 0;
            this.gbxTime1.TabStop = false;
            this.gbxTime1.Text = "主队";
            // 
            // lvHost
            // 
            this.lvHost.AllColumns.Add(this.colSN1);
            this.lvHost.AllColumns.Add(this.colOverlap1);
            this.lvHost.AllColumns.Add(this.colTestPointCnt1);
            this.lvHost.AllColumns.Add(this.colGrid1);
            this.lvHost.AllColumns.Add(this.colTime1);
            this.lvHost.AllColumns.Add(this.colLng1);
            this.lvHost.AllColumns.Add(this.colLat1);
            this.lvHost.AllColumns.Add(this.colFile1);
            this.lvHost.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colSN1,
            this.colOverlap1,
            this.colTestPointCnt1,
            this.colGrid1,
            this.colTime1,
            this.colLng1,
            this.colLat1,
            this.colFile1});
            this.lvHost.ContextMenuStrip = this.contextMenuStrip1;
            this.lvHost.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvHost.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lvHost.FullRowSelect = true;
            this.lvHost.GridLines = true;
            this.lvHost.HeaderWordWrap = true;
            this.lvHost.IsNeedShowOverlay = false;
            this.lvHost.Location = new System.Drawing.Point(3, 18);
            this.lvHost.Name = "lvHost";
            this.lvHost.OwnerDraw = true;
            this.lvHost.ShowGroups = false;
            this.lvHost.Size = new System.Drawing.Size(484, 181);
            this.lvHost.TabIndex = 2;
            this.lvHost.UseCompatibleStateImageBehavior = false;
            this.lvHost.View = System.Windows.Forms.View.Details;
            this.lvHost.VirtualMode = true;
            this.lvHost.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.lvHost_MouseDoubleClick);
            // 
            // colSN1
            // 
            this.colSN1.HeaderFont = null;
            this.colSN1.Text = "序号";
            // 
            // colOverlap1
            // 
            this.colOverlap1.HeaderFont = null;
            this.colOverlap1.Text = "与竞比方有重叠";
            // 
            // colTestPointCnt1
            // 
            this.colTestPointCnt1.HeaderFont = null;
            this.colTestPointCnt1.Text = "采样点个数";
            // 
            // colTime1
            // 
            this.colTime1.AspectName = "";
            this.colTime1.HeaderFont = null;
            this.colTime1.Text = "测试时间";
            this.colTime1.Width = 146;
            // 
            // colLng1
            // 
            this.colLng1.HeaderFont = null;
            this.colLng1.Text = "经度";
            // 
            // colLat1
            // 
            this.colLat1.HeaderFont = null;
            this.colLat1.Text = "纬度";
            // 
            // colFile1
            // 
            this.colFile1.HeaderFont = null;
            this.colFile1.Text = "文件名";
            // 
            // colGrid1
            // 
            this.colGrid1.HeaderFont = null;
            this.colGrid1.Text = "网格";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll1,
            this.miCollapseAll1,
            this.toolStripSeparator1,
            this.miExport1,
            this.miExportShp1});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(149, 98);
            // 
            // miExpandAll1
            // 
            this.miExpandAll1.Name = "miExpandAll1";
            this.miExpandAll1.Size = new System.Drawing.Size(148, 22);
            this.miExpandAll1.Text = "展开所有节点";
            this.miExpandAll1.Click += new System.EventHandler(this.miExpandAll1_Click);
            // 
            // miCollapseAll1
            // 
            this.miCollapseAll1.Name = "miCollapseAll1";
            this.miCollapseAll1.Size = new System.Drawing.Size(148, 22);
            this.miCollapseAll1.Text = "折叠所有节点";
            this.miCollapseAll1.Click += new System.EventHandler(this.miCollapseAll1_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(145, 6);
            // 
            // miExport1
            // 
            this.miExport1.Name = "miExport1";
            this.miExport1.Size = new System.Drawing.Size(148, 22);
            this.miExport1.Text = "导出Excel...";
            this.miExport1.Click += new System.EventHandler(this.miExport1_Click);
            // 
            // miExportShp1
            // 
            this.miExportShp1.Name = "miExportShp1";
            this.miExportShp1.Size = new System.Drawing.Size(148, 22);
            this.miExportShp1.Text = "导出图层...";
            this.miExportShp1.Click += new System.EventHandler(this.ExportShp_Click);
            // 
            // gbxTime2
            // 
            this.gbxTime2.Controls.Add(this.lvGuest);
            this.gbxTime2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbxTime2.Location = new System.Drawing.Point(0, 0);
            this.gbxTime2.Name = "gbxTime2";
            this.gbxTime2.Size = new System.Drawing.Size(501, 202);
            this.gbxTime2.TabIndex = 0;
            this.gbxTime2.TabStop = false;
            this.gbxTime2.Text = "客队";
            // 
            // lvGuest
            // 
            this.lvGuest.AllColumns.Add(this.colSN2);
            this.lvGuest.AllColumns.Add(this.colOverlap2);
            this.lvGuest.AllColumns.Add(this.colTestPointCnt2);
            this.lvGuest.AllColumns.Add(this.colGrid2);
            this.lvGuest.AllColumns.Add(this.colTime2);
            this.lvGuest.AllColumns.Add(this.colLng2);
            this.lvGuest.AllColumns.Add(this.colLat2);
            this.lvGuest.AllColumns.Add(this.colFile2);
            this.lvGuest.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colSN2,
            this.colOverlap2,
            this.colTestPointCnt2,
            this.colGrid2,
            this.colTime2,
            this.colLng2,
            this.colLat2,
            this.colFile2});
            this.lvGuest.ContextMenuStrip = this.contextMenuStrip2;
            this.lvGuest.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvGuest.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lvGuest.FullRowSelect = true;
            this.lvGuest.GridLines = true;
            this.lvGuest.HeaderWordWrap = true;
            this.lvGuest.IsNeedShowOverlay = false;
            this.lvGuest.Location = new System.Drawing.Point(3, 18);
            this.lvGuest.Name = "lvGuest";
            this.lvGuest.OwnerDraw = true;
            this.lvGuest.ShowGroups = false;
            this.lvGuest.Size = new System.Drawing.Size(495, 181);
            this.lvGuest.TabIndex = 3;
            this.lvGuest.UseCompatibleStateImageBehavior = false;
            this.lvGuest.View = System.Windows.Forms.View.Details;
            this.lvGuest.VirtualMode = true;
            this.lvGuest.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.lvHost_MouseDoubleClick);
            // 
            // colSN2
            // 
            this.colSN2.HeaderFont = null;
            this.colSN2.Text = "序号";
            // 
            // colOverlap2
            // 
            this.colOverlap2.HeaderFont = null;
            this.colOverlap2.Text = "与竞比方有重叠";
            // 
            // colTestPointCnt2
            // 
            this.colTestPointCnt2.HeaderFont = null;
            this.colTestPointCnt2.Text = "采样点个数";
            // 
            // colTime2
            // 
            this.colTime2.AspectName = "";
            this.colTime2.HeaderFont = null;
            this.colTime2.Text = "测试时间";
            this.colTime2.Width = 146;
            // 
            // colLng2
            // 
            this.colLng2.HeaderFont = null;
            this.colLng2.Text = "经度";
            // 
            // colLat2
            // 
            this.colLat2.HeaderFont = null;
            this.colLat2.Text = "纬度";
            // 
            // colFile2
            // 
            this.colFile2.HeaderFont = null;
            this.colFile2.Text = "文件名";
            // 
            // colGrid2
            // 
            this.colGrid2.HeaderFont = null;
            this.colGrid2.Text = "网格";
            // 
            // contextMenuStrip2
            // 
            this.contextMenuStrip2.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll2,
            this.miCollapseAll2,
            this.toolStripSeparator2,
            this.miExport2,
            this.miExportShp2});
            this.contextMenuStrip2.Name = "contextMenuStrip1";
            this.contextMenuStrip2.Size = new System.Drawing.Size(149, 98);
            // 
            // miExpandAll2
            // 
            this.miExpandAll2.Name = "miExpandAll2";
            this.miExpandAll2.Size = new System.Drawing.Size(148, 22);
            this.miExpandAll2.Text = "展开所有节点";
            this.miExpandAll2.Click += new System.EventHandler(this.miExpandAll2_Click);
            // 
            // miCollapseAll2
            // 
            this.miCollapseAll2.Name = "miCollapseAll2";
            this.miCollapseAll2.Size = new System.Drawing.Size(148, 22);
            this.miCollapseAll2.Text = "折叠所有节点";
            this.miCollapseAll2.Click += new System.EventHandler(this.miCollapseAll2_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(145, 6);
            // 
            // miExport2
            // 
            this.miExport2.Name = "miExport2";
            this.miExport2.Size = new System.Drawing.Size(148, 22);
            this.miExport2.Text = "导出Excel...";
            this.miExport2.Click += new System.EventHandler(this.miExport2_Click);
            // 
            // miExportShp2
            // 
            this.miExportShp2.Name = "miExportShp2";
            this.miExportShp2.Size = new System.Drawing.Size(148, 22);
            this.miExportShp2.Text = "导出图层...";
            this.miExportShp2.Click += new System.EventHandler(this.ExportShp_Click);
            // 
            // gbxRepeat
            // 
            this.gbxRepeat.Controls.Add(this.lvOverlap);
            this.gbxRepeat.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbxRepeat.Location = new System.Drawing.Point(0, 0);
            this.gbxRepeat.Name = "gbxRepeat";
            this.gbxRepeat.Size = new System.Drawing.Size(1004, 164);
            this.gbxRepeat.TabIndex = 1;
            this.gbxRepeat.TabStop = false;
            this.gbxRepeat.Text = "双方重叠部分";
            // 
            // lvOverlap
            // 
            this.lvOverlap.AllColumns.Add(this.colSN3);
            this.lvOverlap.AllColumns.Add(this.colTestPointCnt3);
            this.lvOverlap.AllColumns.Add(this.colGrid3);
            this.lvOverlap.AllColumns.Add(this.colTime3);
            this.lvOverlap.AllColumns.Add(this.colLng3);
            this.lvOverlap.AllColumns.Add(this.colLat3);
            this.lvOverlap.AllColumns.Add(this.colFile3);
            this.lvOverlap.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colSN3,
            this.colTestPointCnt3,
            this.colGrid3,
            this.colTime3,
            this.colLng3,
            this.colLat3,
            this.colFile3});
            this.lvOverlap.ContextMenuStrip = this.contextMenuStripOverlap;
            this.lvOverlap.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvOverlap.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lvOverlap.FullRowSelect = true;
            this.lvOverlap.GridLines = true;
            this.lvOverlap.HeaderWordWrap = true;
            this.lvOverlap.IsNeedShowOverlay = false;
            this.lvOverlap.Location = new System.Drawing.Point(3, 18);
            this.lvOverlap.Name = "lvOverlap";
            this.lvOverlap.OwnerDraw = true;
            this.lvOverlap.ShowGroups = false;
            this.lvOverlap.Size = new System.Drawing.Size(998, 143);
            this.lvOverlap.TabIndex = 3;
            this.lvOverlap.UseCompatibleStateImageBehavior = false;
            this.lvOverlap.View = System.Windows.Forms.View.Details;
            this.lvOverlap.VirtualMode = true;
            this.lvOverlap.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.lvHost_MouseDoubleClick);
            // 
            // colSN3
            // 
            this.colSN3.HeaderFont = null;
            this.colSN3.Text = "序号";
            // 
            // colTestPointCnt3
            // 
            this.colTestPointCnt3.HeaderFont = null;
            this.colTestPointCnt3.Text = "采样点个数";
            // 
            // colTime3
            // 
            this.colTime3.AspectName = "";
            this.colTime3.HeaderFont = null;
            this.colTime3.Text = "测试时间";
            this.colTime3.Width = 146;
            // 
            // colLng3
            // 
            this.colLng3.HeaderFont = null;
            this.colLng3.Text = "经度";
            // 
            // colLat3
            // 
            this.colLat3.HeaderFont = null;
            this.colLat3.Text = "纬度";
            // 
            // colFile3
            // 
            this.colFile3.HeaderFont = null;
            this.colFile3.Text = "文件名";
            // 
            // colGrid3
            // 
            this.colGrid3.HeaderFont = null;
            this.colGrid3.Text = "网格";
            // 
            // contextMenuStripOverlap
            // 
            this.contextMenuStripOverlap.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAllOverlap,
            this.miCollapseAllOverlap,
            this.toolStripSeparator3,
            this.miExportOverlap,
            this.miExportShpOverlap});
            this.contextMenuStripOverlap.Name = "contextMenuStrip1";
            this.contextMenuStripOverlap.Size = new System.Drawing.Size(149, 98);
            // 
            // miExpandAllOverlap
            // 
            this.miExpandAllOverlap.Name = "miExpandAllOverlap";
            this.miExpandAllOverlap.Size = new System.Drawing.Size(148, 22);
            this.miExpandAllOverlap.Text = "展开所有节点";
            this.miExpandAllOverlap.Click += new System.EventHandler(this.miExpandAllOverlap_Click);
            // 
            // miCollapseAllOverlap
            // 
            this.miCollapseAllOverlap.Name = "miCollapseAllOverlap";
            this.miCollapseAllOverlap.Size = new System.Drawing.Size(148, 22);
            this.miCollapseAllOverlap.Text = "折叠所有节点";
            this.miCollapseAllOverlap.Click += new System.EventHandler(this.miCollapseAllOverlap_Click);
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(145, 6);
            // 
            // miExportOverlap
            // 
            this.miExportOverlap.Name = "miExportOverlap";
            this.miExportOverlap.Size = new System.Drawing.Size(148, 22);
            this.miExportOverlap.Text = "导出Excel...";
            this.miExportOverlap.Click += new System.EventHandler(this.miExportOverlap_Click);
            // 
            // miExportShpOverlap
            // 
            this.miExportShpOverlap.Name = "miExportShpOverlap";
            this.miExportShpOverlap.Size = new System.Drawing.Size(148, 22);
            this.miExportShpOverlap.Text = "导出图层...";
            this.miExportShpOverlap.Click += new System.EventHandler(this.ExportShp_Click);
            // 
            // ResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1006, 435);
            this.Controls.Add(this.splitContainer1);
            this.Controls.Add(this.panel1);
            this.Name = "ResultForm";
            this.Text = "采样点汇聚竞比列表";
            this.panel1.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.colorGuest.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkGuest.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHost.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorOverlap.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkOverlap.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorHost.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.ResumeLayout(false);
            this.splitContainer2.Panel1.ResumeLayout(false);
            this.splitContainer2.Panel2.ResumeLayout(false);
            this.splitContainer2.ResumeLayout(false);
            this.gbxTime1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lvHost)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.gbxTime2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lvGuest)).EndInit();
            this.contextMenuStrip2.ResumeLayout(false);
            this.gbxRepeat.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lvOverlap)).EndInit();
            this.contextMenuStripOverlap.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraEditors.ColorEdit colorGuest;
        private DevExpress.XtraEditors.CheckEdit chkGuest;
        private DevExpress.XtraEditors.CheckEdit chkHost;
        private DevExpress.XtraEditors.ColorEdit colorOverlap;
        private DevExpress.XtraEditors.CheckEdit chkOverlap;
        private DevExpress.XtraEditors.ColorEdit colorHost;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.RadioButton rbRepeat;
        private System.Windows.Forms.RadioButton rbDiff;
        private System.Windows.Forms.RadioButton rbAll;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.SplitContainer splitContainer2;
        private System.Windows.Forms.GroupBox gbxTime1;
        private BrightIdeasSoftware.TreeListView lvHost;
        private BrightIdeasSoftware.OLVColumn colTime1;
        private System.Windows.Forms.GroupBox gbxTime2;
        private System.Windows.Forms.GroupBox gbxRepeat;
        private BrightIdeasSoftware.OLVColumn colTestPointCnt1;
        private BrightIdeasSoftware.OLVColumn colOverlap1;
        private BrightIdeasSoftware.OLVColumn colSN1;
        private BrightIdeasSoftware.OLVColumn colLng1;
        private BrightIdeasSoftware.OLVColumn colLat1;
        private BrightIdeasSoftware.OLVColumn colFile1;
        private BrightIdeasSoftware.TreeListView lvGuest;
        private BrightIdeasSoftware.OLVColumn colSN2;
        private BrightIdeasSoftware.OLVColumn colOverlap2;
        private BrightIdeasSoftware.OLVColumn colTestPointCnt2;
        private BrightIdeasSoftware.OLVColumn colTime2;
        private BrightIdeasSoftware.OLVColumn colLng2;
        private BrightIdeasSoftware.OLVColumn colLat2;
        private BrightIdeasSoftware.OLVColumn colFile2;
        private BrightIdeasSoftware.TreeListView lvOverlap;
        private BrightIdeasSoftware.OLVColumn colSN3;
        private BrightIdeasSoftware.OLVColumn colTestPointCnt3;
        private BrightIdeasSoftware.OLVColumn colTime3;
        private BrightIdeasSoftware.OLVColumn colLng3;
        private BrightIdeasSoftware.OLVColumn colLat3;
        private BrightIdeasSoftware.OLVColumn colFile3;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripOverlap;
        private System.Windows.Forms.ToolStripMenuItem miExpandAllOverlap;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAllOverlap;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private System.Windows.Forms.ToolStripMenuItem miExportOverlap;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip2;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll2;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll2;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem miExport2;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll1;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll1;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExport1;
        private System.Windows.Forms.ToolStripMenuItem miExportShp1;
        private System.Windows.Forms.ToolStripMenuItem miExportShp2;
        private System.Windows.Forms.ToolStripMenuItem miExportShpOverlap;
        private BrightIdeasSoftware.OLVColumn colGrid1;
        private BrightIdeasSoftware.OLVColumn colGrid2;
        private BrightIdeasSoftware.OLVColumn colGrid3;
    }
}