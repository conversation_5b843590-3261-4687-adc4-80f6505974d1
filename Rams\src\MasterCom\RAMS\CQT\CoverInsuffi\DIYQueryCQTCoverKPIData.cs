﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.Windows.Forms;

namespace MasterCom.RAMS.CQT
{
    public class DIYQueryCQTCoverKPIData : DIYStatQuery
    {
        public DIYQueryCQTCoverKPIData(MainModel mainModel, string netWorker)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            netType = netWorker;
        }
        readonly string netType;
        /// <summary>
        /// 标题名称
        /// </summary>
        public override string Name
        {
            get { return ""; }
        }
        /// <summary>
        /// 图标名称
        /// </summary>
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }
        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }
        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.log;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            string str = "查询" + netType + "业务覆盖不足";
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 21000, 21016, string.Format("Name[{0}], {1}", this.Name, str));
        }
        
        /// <summary>
        /// 涉及的全局变量
        /// </summary>
        #region 全局变量
        private readonly List<string> formulaList = new List<string>();
        private readonly List<FileInfo> fileList = new List<FileInfo>();
        private readonly List<string> fileValueNameList = new List<string>();
        private readonly List<string> finalValueNameList = new List<string>();
        readonly Dictionary<string, List<FileInfo>> fileValueList = new Dictionary<string, List<FileInfo>>();        
        readonly Dictionary<string, List<Event>> cqtNameEventList = new Dictionary<string, List<Event>>();
        readonly Dictionary<string, List<CQTEventItem>> nameEventDic = new Dictionary<string, List<CQTEventItem>>();
        readonly List<CQTCoverItem> resultList = new List<CQTCoverItem>();
        readonly List<CQTCoverItem> cqtResultList = new List<CQTCoverItem>();
        Dictionary<int, List<Event>> condEventsDic = new Dictionary<int, List<Event>>();
        string cqtName = "";
        public int numfugai { get; set; }
        public double num90 { get; set; }
        public double num80 { get; set; }
        #endregion
        /// <summary>
        /// 使用前清空变量
        /// </summary>
        private void clealVar()
        {
            fileList.Clear();
            SetFormulaList();
            fileValueNameList.Clear();
            fileValueList.Clear();
            resultList.Clear();
            cqtResultList.Clear();
            finalValueNameList.Clear();
            numfugai = 0;
            num90 = 0;
            num80 = 0;
        }
        /// <summary>
        /// 准备查询数据
        /// </summary>
        protected override void query()
        {
            //清空以及重新初始化变量
            clealVar();
            CQTGSMCoverQueryForm xtraformstatus = new CQTGSMCoverQueryForm();
            if (xtraformstatus.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            int numfugaiTmp;
            double num90Tmp;
            double num80Tmp;
            xtraformstatus.getSelect(out numfugaiTmp, out num90Tmp, out num80Tmp);
            numfugai = numfugaiTmp;
            num90 = num90Tmp;
            num80 = num80Tmp;
            // 查找全区域文件
            DIYFileInfoData diyFileInfoData = new DIYFileInfoData(mainModel);
            diyFileInfoData.SetQueryCondition(this.Condition);
            diyFileInfoData.Query();
            fileList.AddRange(diyFileInfoData.FlieInfoData);
            //找出所有文件中包含的地点名称
            addFileValueNameList();
            //处理事件
            PrepareEvents();
            List<int> eventId = new List<int>();
            eventId.AddRange(condEventsDic.Keys);
            //每个地点所涉及的文件
            addFileValueList(eventId);
            //各个地点事件
            cqtEventList();
            //根据用户设置的弱覆盖事件数去掉不符合的地点
            addFinalValueNameList();
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, condition.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
            //显示数据结果
            CQTGSMCoverNew cqtform = new CQTGSMCoverNew(MainModel, condition, netType);
            cqtform.setData(cqtResultList);
            cqtform.Show();
        }

        private void addFinalValueNameList()
        {
            foreach (string name in fileValueNameList)
            {
                if (nameEventDic[name].Count >= numfugai)
                {
                    finalValueNameList.Add(name);
                }
            }
        }

        private void addFileValueList(List<int> eventId)
        {
            foreach (string nameL in fileValueNameList)
            {
                List<FileInfo> subFileList = new List<FileInfo>();
                foreach (FileInfo fileIn in fileList)
                {
                    string[] name = fileIn.Name.Split('_');
                    if (name.Length < 3)
                        continue;
                    if (nameL.Equals(name[2]) && eventId.Contains(fileIn.ID))
                    {
                        subFileList.Add(fileIn);
                    }
                }
                fileValueList.Add(nameL, subFileList);
            }
        }

        private void addFileValueNameList()
        {
            foreach (FileInfo fileIn in fileList)
            {
                string[] name = fileIn.Name.Split('_');
                if (name.Length >= 3 && !fileValueNameList.Contains(name[2]))
                {
                    fileValueNameList.Add(name[2]);
                }
            }
        }

        /// <summary>
        /// 指标的计算公式列表
        /// </summary>
        private void SetFormulaList()
        {
            formulaList.Clear();
            if (netType.Equals("GSM"))
            {
                formulaList.Add("{100*(Mx_5A010217+Mx_5A01020A+Mx_5A010209+Mx_5A010208)/Mx_5A010201}%");//90覆盖率
                formulaList.Add("{100*(Mx_5A010217+Mx_5A01020A)/Mx_5A010201}%");//80覆盖率
            }
            else if (netType.Equals("TD"))
            {
                formulaList.Add("{100*Tx_5C04030E/Tx_5C04030A }%");//90覆盖率
                formulaList.Add("{100*Tx_5C04031B/Tx_5C04030A }%");//80覆盖率
            }
        }
        /// <summary>
        /// 查询数据
        /// </summary>
        private void queryInThread(object o)
        {
            ClientProxy clientProxy = (ClientProxy)o;
            Package package = clientProxy.Package;
            int idx = 1;          
            WaitBox.CanCancel = true;
            WaitBox.Text = "开始查询...";
            foreach (TimePeriod period in condition.Periods)
            {
                int countTotal = finalValueNameList.Count;
                foreach (string cqtPnt in finalValueNameList)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    WaitBox.Text = "正在统计( " + idx++ + "/" + countTotal + " ) " + cqtPnt + " 的指标值...";
                    cqtName = cqtPnt;
                    prepareStatPackage_ImgGrid(package, period, (byte)condition.CarrierTypes[0], condition.IsByRound, cqtPnt);                  
                    fillContentNeeded_ImgGrid(package);
                    clientProxy.Send();
                    recieveInfo_ImgGrid(clientProxy, period);
                }
            }
            foreach (CQTCoverItem item in resultList)
            {
                if (item.IWeakCoverNum >= numfugai && item.F90CoverRate < num90 && item.F90CoverRate >= 0 && item.F80CoverRate < num80 && item.F80CoverRate >= 0)
                {
                    cqtResultList.Add(item);
                }
            }           
            WaitBox.Close();           
        }       
        /// <summary>
        /// 查询全区域弱覆盖事件
        /// </summary>
        private void PrepareEvents()
        {
            DIYEventByAllRegion queryEvent = new DIYEventByAllRegion(MainModel);
            queryEvent.SetIsAddEventToDTDataManager(false);
            queryEvent.SetSaveAsFileEventsDic(true);
            queryEvent.showEventChooser = false;
            queryEvent.IsQueryAllEvents = false;
            List<int> eventIds = new List<int>();
            if (netType.Equals("GSM"))
            {
                eventIds.Add(41);//GSM弱覆盖
            }
            else if (netType.Equals("TD"))
            {
                eventIds.Add(187);//TD弱覆盖
            }
            condition.EventIDs = eventIds;
            queryEvent.SetQueryCondition(condition);
            queryEvent.Query();
            condEventsDic = queryEvent.fileEventsDic;
        }
        /// <summary>
        /// 获取各个测试地点的弱覆盖事件
        /// </summary>
        private void cqtEventList()
        {
            cqtNameEventList.Clear();
            try
            {
                foreach (string cpn in fileValueNameList)
                {
                    List<Event> sunEventList = new List<Event>();
                    foreach (FileInfo fileinfo in fileValueList[cpn])//每个测试地点所涉及的文件
                    {
                        addSunEventList(sunEventList, fileinfo);
                    }
                    cqtNameEventList.Add(cpn, sunEventList);
                }
            }
            catch
            {
                //continue
            }
            getCQTEventList();
        }

        private void addSunEventList(List<Event> sunEventList, FileInfo fileinfo)
        {
            foreach (Event eve in condEventsDic[fileinfo.ID])
            {
                if (netType.Equals("GSM") && eve.ID == 41)
                {
                    sunEventList.Add(eve);
                }
                else if (netType.Equals("TD") && eve.ID == 187)
                {
                    sunEventList.Add(eve);
                }
            }
        }

        /// <summary>
        /// 处理事件对应关系
        /// </summary>
        private void getCQTEventList()
        {
            nameEventDic.Clear();
            foreach (string cname in cqtNameEventList.Keys)
            {
                List<CQTEventItem> eventList = new List<CQTEventItem>();
                foreach (Event ev in cqtNameEventList[cname])
                {
                    CQTEventItem cqtEventItem = new CQTEventItem();
                    cqtEventItem.Ieventid = ev.ID;
                    cqtEventItem.Strfilename = ev.FileName;
                    cqtEventItem.Dtime = ev.DateTime;
                    cqtEventItem.Strcellname = ev.CellNameSrc;
                    cqtEventItem.Ilac = (int)ev["LAC"];
                    cqtEventItem.Ici = (int)ev["CI"];
                    cqtEventItem.Ifileid = ev.FileID;
                    cqtEventItem.Itime = ev.Time;
                    eventList.Add(cqtEventItem);
                }
                nameEventDic.Add(cname, eventList);
            }
        }
        /// <summary>
        /// 添加接收指标项
        /// </summary>
        protected virtual void fillContentNeeded_ImgGrid(Package package)
        {
            package.Content.AddParam("-1,-1,-1");
        }
        /// <summary>
        /// 设置查询条件
        /// </summary>
        protected void prepareStatPackage_ImgGrid(Package package, TimePeriod period, byte carrierID, bool byRound, string cqtPointName)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = MasterCom.RAMS.Net.RequestType.REQTYPE_DIY_LOG_KPI;
            package.Content.PrepareAddParam();
            if (byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYFileName(package, cqtPointName);//按CQT地点匹配文件

            //AddDIYCarrierType(package, carrierID)
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);

            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            AddDIYMomt(package, condition.Momt);

            AddDIYEndOpFlag(package);
        }
        /// <summary>
        /// 按照测试地点名称匹配文件
        /// </summary>
        protected void AddDIYFileName(Package package, string fileNameLikeStr)
        {
            fileNameLikeStr = "_" + fileNameLikeStr + "_@";
            package.Content.AddParam((byte)OpOptionDef.StrLike);
            package.Content.AddParam("0,2,1");//filename
            package.Content.AddParam(fileNameLikeStr);
        }
        protected void AddDIYFileNameFilter(Package package, string totalfilterStr, int orNumCount, int nameFilterType, string cqtPointName)
        {
            if (totalfilterStr.Length == 0)
            {
                return;
            }
            package.Content.AddParam((byte)OpOptionDef.StrLike);
            StringBuilder sbFileTrid = new StringBuilder();
            for (int i = 0; i < orNumCount; i++)
            {
                if (nameFilterType == 1)
                {
                    sbFileTrid.Append("0,16,1");//filepath
                }
                else if (nameFilterType == 2)
                {
                    sbFileTrid.Append("0,33,1");//strdesc
                }
                else
                {
                    sbFileTrid.Append("0,2,1");//filename
                }
                if (i < orNumCount - 1)
                {
                    sbFileTrid.Append(",");
                }
            }
            package.Content.AddParam(sbFileTrid.ToString());
            package.Content.AddParam(totalfilterStr);
        }
        /// <summary>
        /// 接收查询结果
        /// </summary>
        protected void recieveInfo_ImgGrid(ClientProxy clientProxy,TimePeriod period)
        {
            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 25;
            WaitBox.ProgressPercent = progress;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                bool isEnd = readStatData(retResult, curImgColumnDef, package);
                if (isEnd)
                {
                    break;
                }
                #endregion

                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
            caculaValueByFormula(retResult);
            WaitBox.ProgressPercent = 95;
        }

        private bool readStatData(DataUnitAreaKPIQuery retResult, List<StatImgDefItem> curImgColumnDef, Package package)
        {
            if (isFileHeaderContentType(package.Content.Type))
            {
                //
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.COLUMN_DEFINE)
            {
                curImgColumnDef.Clear();
                string idpairs = package.Content.GetParamString();
                parseToCurImgColumnDef(idpairs, curImgColumnDef);
            }
            else if(addRetResult(retResult, curImgColumnDef, package))
            {
                //add data
            }
            else if(package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
            {
                return true;
            }
            else
            {
                log.Error("Unexpected type: " + package.Content.Type);
                return true;
            }
            return false;
        }

        private bool addRetResult(DataUnitAreaKPIQuery retResult, List<StatImgDefItem> curImgColumnDef, Package package)
        {
            if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_GSM
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_GPRS)
            {
                DataGSM_NewImg newImg = new DataGSM_NewImg();
                addWInfoDic(curImgColumnDef, package, newImg.WInfoDic);
                retResult.addStatData(newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_AMR
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_PS
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_VP)
            {
                DataTDSCDMA_NewImg newImg = new DataTDSCDMA_NewImg();
                addWInfoDic(curImgColumnDef, package, newImg.WInfoDic);
                retResult.addStatData(newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_AMR
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_PS
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_VP
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_PSHS)
            {
                DataWCDMA_AMR newImg = new DataWCDMA_AMR();
                addWInfoDic(curImgColumnDef, package, newImg.WInfoDic);
                retResult.addStatData(newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_CDMA_V
                || package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_CDMA_D)
            {
                DataCDMA_Voice newImg = new DataCDMA_Voice();
                addWInfoDic(curImgColumnDef, package, newImg.WInfoDic);
                retResult.addStatData(newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_CDMA2000_D)
            {
                DataEVDO_Data newImg = new DataEVDO_Data();
                addWInfoDic(curImgColumnDef, package, newImg.WInfoDic);
                retResult.addStatData(newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_GSM_MTR)
            {
                DataMTR_GSM newImg = new DataMTR_GSM();
                addWInfoDic(curImgColumnDef, package, newImg.WInfoDic);
                retResult.addStatData(newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_TD)
            {
                DataScan_TD newImg = new DataScan_TD();
                addWInfoDic(curImgColumnDef, package, newImg.WInfoDic);
                retResult.addStatData(newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_GSM)
            {
                DataScan_GSM newImg = new DataScan_GSM();
                addWInfoDic(curImgColumnDef, package, newImg.WInfoDic);
                retResult.addStatData(newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_WLAN)
            {
                DataWLAN newImg = new DataWLAN();
                addWInfoDic(curImgColumnDef, package, newImg.WInfoDic);
                retResult.addStatData(newImg);
            }
            else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIY_LOG_KPI_LTE_AMR)
            {
                DataLTE newImg = new DataLTE();
                addWInfoDic(curImgColumnDef, package, newImg.WInfoDic);
                retResult.addStatData(newImg);
            }
            else
            {
                return true;
            }
            return false;
        }

        private void addWInfoDic(List<StatImgDefItem> curImgColumnDef, Package package, Dictionary<string, double> wInfoDic)
        {
            foreach (StatImgDefItem cdf in curImgColumnDef)
            {
                byte[] imgBytes = package.Content.GetParamBytes();
                Dictionary<String, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
        }

        /// <summary>
        /// 按照指标公式计算指标值
        /// </summary>
        private void caculaValueByFormula(DataUnitAreaKPIQuery retResult)
        {         
            List<string> strFormu = new List<string>();
            string  strSun = "";
            CQTCoverItem cqtCoverItem = new CQTCoverItem();
            foreach (string sunFom in formulaList)
            {
                strSun = retResult.CalcValueByFormula(sunFom);
                if (!strSun.Equals("-") && strSun != "")
                    strFormu.Add(strSun);
            }
            if (strFormu.Count == formulaList.Count)
            {
                cqtCoverItem.F90CoverRate = float.Parse(strFormu[0].Split('%')[0]);
                cqtCoverItem.F80CoverRate = float.Parse(strFormu[1].Split('%')[0]);
                cqtCoverItem.Strcqtname = cqtName;
                cqtCoverItem.IWeakCoverNum = nameEventDic[cqtName].Count;
                cqtCoverItem.cqtEventList.AddRange(nameEventDic[cqtName]);
                resultList.Add(cqtCoverItem);
            }
            WaitBox.ProgressPercent = 90;
            
        }
    }
    public class CQTCoverItem
    {
        /// <summary>
        /// CQT地点名称
        /// </summary>
        public string Strcqtname { get; set; }
        /// <summary>
        /// 80覆盖率(浮点型)
        /// </summary>
        public float F80CoverRate { get; set; }
        /// <summary>
        /// 85覆盖率(浮点型)
        /// </summary>
        public float F85CoverRate { get; set; }
        /// <summary>
        /// 90覆盖率(浮点型)
        /// </summary>
        public float F90CoverRate { get; set; }
        /// <summary>
        /// 95覆盖率(浮点型)
        /// </summary>
        public float F95CoverRate { get; set; }
        /// <summary>
        /// 80覆盖率(字符型)
        /// </summary>
        public string Str80CoverRate
        {
            get { return Math.Round(F80CoverRate, 2).ToString() + "%"; }
        }
        /// <summary>
        /// 85覆盖率(字符型)
        /// </summary>
        public string Str85CoverRate
        {
            get { return Math.Round(F85CoverRate, 2).ToString() + "%"; }
        }
        /// <summary>
        /// 90覆盖率(字符型)
        /// </summary>
        public string Str90CoverRate
        {
            get { return Math.Round(F90CoverRate, 2).ToString() + "%"; }
        }
        /// <summary>
        /// 95覆盖率(字符型)
        /// </summary>
        public string Str95CoverRate
        {
            get { return Math.Round(F95CoverRate, 2).ToString() + "%"; }
        }
        
        /// <summary>
        /// 弱覆盖数目
        /// </summary>
        public int IWeakCoverNum { get; set; }

        public List<CQTEventItem> cqtEventList { get; set; } = new List<CQTEventItem>();
    }

    public class CQTEventItem
    {
        /// <summary>
        /// 整型时间
        /// </summary>
        public int Itime { get; set; }
        /// <summary>
        /// 文件ID
        /// </summary>
        public int Ifileid { get; set; }
        /// <summary>
        /// 事件ID
        /// </summary>
        public int Ieventid { get; set; }
        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime Dtime { get; set; }
        /// <summary>
        /// 文件名
        /// </summary>
        public string Strfilename { get; set; }
        /// <summary>
        /// 事件类型
        /// </summary>
        public string Streventtype
        {
            get
            {
                if (Ieventid == 41)
                    return "弱覆盖";
                else if (Ieventid == 187)
                    return "TD弱覆盖";
                else if (Ieventid == 43)
                    return "感知质差";
                else if (Ieventid == 900)
                    return "持续质差";
                else if (Ieventid == 230)
                    return "TD高BLER";

                return null;
            }
        }
        /// <summary>
        /// 小区名称
        /// </summary>
        public string Strcellname { get; set; }
        /// <summary>
        /// 占用小区LAC
        /// </summary>
        public int Ilac { get; set; }
        /// <summary>
        /// 占用小区CI
        /// </summary>
        public int Ici { get; set; }
    }
}
