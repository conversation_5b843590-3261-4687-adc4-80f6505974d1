﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.JiLinFocusSet;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    class QueryGridOrderSet : DIYSQLBase
    {
        public QueryGridOrderSet()
            : base()
        {
            MainDB = true;
            IsShowConditonDlg = true;
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        private enum TbName
        {
            Order,
            OrderGrid
        }

        public bool IsShowConditonDlg
        {
            get;
            set;
        }
        
        public DateTime DateFrom { get; set; } = DateTime.Now.AddMonths(-1);
        public DateTime DateTo { get; set; } = DateTime.Now.Date.AddDays(1).AddMilliseconds(-1);
        public string OrderIDLower
        {
            get;
            set;
        }

        protected override void query()
        {
            if (IsShowConditonDlg)
            {
                PeriodPicker pk = new PeriodPicker();
                pk.DateFrom = DateFrom;
                pk.DateTo = DateTo;
                if (pk.ShowDialog() != DialogResult.OK)
                {
                    return;
                }
                DateFrom = pk.DateFrom;
                DateTo = pk.DateTo;
                OrderIDLower = pk.OrderIDLower;
            }

            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                MainModel.DTDataManager.Clear();
                orderDic = new Dictionary<string, GridOrder>();
                WaitTextBox.Show("正在查询工单...", queryInThread, clientProxy);
                fireShowResultForm();
                MainModel.FireDTDataChanged(this);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void fireShowResultForm()
        {
            GridOrderSetListForm form = MainModel.CreateResultForm(typeof(GridOrderSetListForm)) as GridOrderSetListForm;
            form.FillData(new List<GridOrder>(orderDic.Values), DateFrom, DateTo,OrderIDLower);
            form.Visible = true;
            form.BringToFront();
        }

        TbName curTbName;
        protected override void queryInThread(object o)
        {
            try
            {
                queryFromDB((ClientProxy)o, TbName.Order);
                if (orderDic.Count > 0)
                {
                    queryFromDB((ClientProxy)o, TbName.OrderGrid);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                WaitTextBox.Close();
            }
        }

        private void queryFromDB(ClientProxy proxy, TbName table)
        {
            Package package = proxy.Package;
            curTbName = table;
            string strsql = getSqlTextString();
            E_VType[] retArrDef = getSqlRetTypeArr();
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            if (MainDB)
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
            }
            else
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
            }
            package.Content.PrepareAddParam();
            package.Content.AddParam(strsql);
            StringBuilder sb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    sb.Append((int)retArrDef[i]);
                    sb.Append(",");
                }
            }

            package.Content.AddParam(sb.ToString().TrimEnd(','));
            proxy.Send();
            receiveRetData(proxy);
        }

        protected override string getSqlTextString()
        {
            string orderCond = " where createdDate >=" + (int)(JavaDate.GetMilliseconds(DateFrom) / 1000)
                + " and createdDate<=" + (int)(JavaDate.GetMilliseconds(DateTo) / 1000)
                + (MainModel.User.DBID == -1 && MainModel.DistrictID == 10 ? "" : " and cityID = " + MainModel.DistrictID);
            switch (curTbName)
            {
                case TbName.Order:
                    return @"SELECT [cityID],[setOrderType],[setID],[status],[statusEx],[createdDate],[itemCount],[areaNames],[roadNames],[orderID] FROM Province.[dbo].[tb_grid_order]"
                        + orderCond + " order by cityID,setID";
                case TbName.OrderGrid:
                    return @"SELECT [cityID],[setOrderType],[setID],[itemID],[gridSN],[LAC],[CI] FROM Province.[dbo].[tb_grid_order_event]"
                     + (MainModel.User.DBID == -1 && MainModel.DistrictID == 10 ? "" : " where cityID = " + MainModel.DistrictID)
                     + " order by setID,itemID";
                default:
                    break;
            }
            return null;
        }

        protected override Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            if (TbName.Order == curTbName)
            {
                E_VType[] arr = new E_VType[10];
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i] = E_VType.E_String;
                return arr;
            }
            else if (TbName.OrderGrid == curTbName)
            {
                E_VType[] arr = new E_VType[7];
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_Int;
                arr[i] = E_VType.E_Int;
                return arr;
            }
            return new E_VType[0];
        }

        private Dictionary<string, GridOrder> orderDic = null;
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            if (curTbName == TbName.Order)
            {
                GridOrder order = GridOrder.Create(package.Content);
                if (string.IsNullOrEmpty(OrderIDLower)
                    || order.OrderID.ToLower().Contains(OrderIDLower))
                {
                    orderDic[order.Key] = order;
                }
            }
            else if (curTbName == TbName.OrderGrid)
            {
                OrderGridItem grid = OrderGridItem.Create(package.Content);
                GridOrder order = null;
                if (orderDic.TryGetValue(grid.OrderKey, out order))
                {
                    order.AddItem(grid);
                }
            }
        }
    }
}
