﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRSameCarTestHelper
    {
        private FileInfo fileHost { get; set; }
        private List<FileInfo> fileListGuest { get; set; }

        private QueryCondition condition { get; set; }

        private NRSameCarTestResult occupyResult { get; set; }
        public NRSameCarTestResult OccupyResult
        {
            get { return occupyResult; }
        }

        private List<string> lstColumns
        {
            get
            {
                List<string> columns = new List<string>();
                columns.Add("NR_SSB_ARFCN");
                columns.Add("NR_PCI");
                columns.Add("NR_APP_type");
                columns.Add("NR_APP_DataStatus_UL");
                columns.Add("NR_APP_DataStatus_DL");
                columns.Add("NR_lte_RSRP");
                columns.Add("NR_lte_SINR");

                //columns.Add("lte_TAC");
                //columns.Add("lte_ECI");
                //columns.Add("lte_EARFCN");
                //columns.Add("lte_PCI");
                //columns.Add("lte_APP_type");
                //columns.Add("lte_APP_DataStatus_DL");
                //columns.Add("lte_APP_DataStatus_UL");
                //columns.Add("lte_gsm_SC_LAC");
                //columns.Add("lte_gsm_SC_CI");
                //columns.Add("lte_gsm_SC_BCCH");
                //columns.Add("lte_gsm_SC_BSIC");
                //columns.Add("lte_td_SC_LAC");
                //columns.Add("lte_td_SC_CellID");
                //columns.Add("lte_td_SC_UARFCN");
                //columns.Add("lte_td_SC_CPI");
                //columns.Add("mode");


                return columns;
            }
        }

        //public bool IsCheckDlAndUl { get; set; }
        public NRCellOccupySameCarTestType Type { get; set; }

        public NRSameCarTestHelper(FileInfo fHost, List<FileInfo> fGuest, NRCellOccupySameCarTestType type)
        {
            this.fileHost = fHost;
            this.fileListGuest = fGuest;
            this.Type = type;
            this.occupyResult = new NRSameCarTestResult(fHost, fGuest, type);
        }

        public void SetCondition(QueryCondition cond)
        {
            this.condition = cond;
        }

        public void Analyse()
        {
            if (condition == null)
                return;

            WaitBox.Text = string.Format("正在回放同车测试文件：{0}",
                fileHost.Name);

            QueryCondition cond = new QueryCondition();
            cond.FileInfos.AddRange(occupyResult.ResultHost.FileList);
            cond.FileInfos.AddRange(occupyResult.ResultGuest.FileList);

            DIYReplayFileWithNoWaitBox query = new DIYReplayFileWithNoWaitBox(MainModel.GetInstance());
            query.IncludeTestPoint = true;
            query.IncludeEvent = false;
            query.IncludeMessage = false;
            query.Columns = lstColumns;
            query.SetQueryCondition(cond);
            query.Query();

            doStat();
        }

        private void doStat()
        {
            WaitBox.Text = string.Format("正在分析同车测试文件：{0}",
                fileHost.Name);

            List<TestPoint> lstTestPnts = new List<TestPoint>();

            List<TestPoint> lstEndPnts = new List<TestPoint>();
            foreach (DTFileDataManager file in MainModel.GetInstance().DTDataManager.FileDataManagers)
            {
                lstTestPnts.AddRange(file.TestPoints);

                if (file.TestPoints.Count > 0)
                    lstEndPnts.Add(file.TestPoints[file.TestPoints.Count - 1]);

                TestPoint lastPnt = null;
                foreach (TestPoint curPnt in file.TestPoints)
                {
                    if (!isValidTestPoint(curPnt) && lastPnt != null && isValidTestPoint(lastPnt) && !lstEndPnts.Contains(lastPnt))
                    {
                        lstEndPnts.Add(lastPnt);
                    }
                    lastPnt = curPnt;
                }
            }
            lstTestPnts.Sort((x,y) => x.lTimeWithMillsecond.CompareTo(y.lTimeWithMillsecond));

            foreach (TestPoint pnt in lstTestPnts)
            {
                bool isHost = (pnt.FileName == fileHost.Name);
                if (!isValidTestPoint(pnt))
                {
                    occupyResult.PrevPnt = null;
                    continue;
                }
                occupyResult.DealPoint(pnt, isHost, lstEndPnts);
            }
        }

        private bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                if (condition.Geometorys != null && condition.Geometorys.Region != null && !condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude))
                {
                    return false;
                }
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
