﻿using MasterCom.RAMS.Chris.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class LtePRBAnaQueryBase : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static LtePRBAnaQueryBase instance = null;
        public static LtePRBAnaQueryBase GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LtePRBAnaQueryBase();
                    }
                }
            }
            return instance;
        }

        protected LtePRBAnaQueryBase()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);

            Columns = new List<string>();
            Columns.Add("lte_APP_type");
            Columns.Add("lte_PDSCH_PRb_Num_slot");
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22066, this.Name);
        }

        readonly List<PrbInfo> prbInfoList = new List<PrbInfo>();
        int sumCount = 0;
        readonly Dictionary<Range, int> dicSumPrbCount = new Dictionary<Range, int>();
        PrbCondition prbCondition = null;

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            PRBSettingDlg dlg = new PRBSettingDlg(prbCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                prbCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void fireShowForm()
        {
            if (prbInfoList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            LtePRBInfoForm frm = MainModel.CreateResultForm(typeof(LtePRBInfoForm)) as LtePRBInfoForm;
            frm.FillData(prbInfoList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            sumCount = 0;
            dicSumPrbCount.Clear();
            prbInfoList.Clear();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    Dictionary<Range, int> dicPrbCount = new Dictionary<Range, int>();
                    int count = 0;
                    count = dealTPPrbCount(file, dicPrbCount, count);
                    PrbInfo prbInfo = getPrbInfo(dicPrbCount, count);
                    if (prbInfo != null)
                    {
                        prbInfo.FileName = file.FileName;
                        prbInfo.SN = prbInfoList.Count + 1;
                        prbInfoList.Add(prbInfo);
                    }
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private int dealTPPrbCount(DTFileDataManager file, Dictionary<Range, int> dicPrbCount, int count)
        {
            foreach (TestPoint tp in file.TestPoints)
            {
                short? type = GetType(tp);
                int? prb = GetPDSCH(tp);
                if (isValueType(type) && prb != null)
                {
                    Range prbRange = getPrbType((int)prb);
                    if (prbRange == null)
                    {
                        continue;
                    }
                    if (dicSumPrbCount.ContainsKey(prbRange))
                    {
                        dicSumPrbCount[prbRange]++;
                    }
                    else
                    {
                        dicSumPrbCount.Add(prbRange, 1);
                    }
                    sumCount++;

                    if (dicPrbCount.ContainsKey(prbRange))
                    {
                        dicPrbCount[prbRange]++;
                    }
                    else
                    {
                        dicPrbCount.Add(prbRange, 1);
                    }
                    count++;
                }
            }

            return count;
        }

        private bool isValueType(int? type)
        {
            if (type != null)
            {
                foreach (int i in prbCondition.TypeArray)
                {
                    if (type == i)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        private Range getPrbType(int intPrb)
        {
            foreach (Range range in prbCondition.PrbRangeValues.Values)
            {
                if (range.Contains(intPrb))
                {
                    return range;
                }
            }
            return null;
        }

        private PrbInfo getPrbInfo(Dictionary<Range, int> dicPrbCount, int count)
        {
            if(count==0 ||dicPrbCount.Count==0)
            {
                return null;
            }
            PrbInfo prbInfo = new PrbInfo();
            foreach (var item in dicPrbCount)
            {
                PrbRangeSet prbSet = new PrbRangeSet(item.Key);
                prbSet.Count = item.Value;
                prbSet.Per = Math.Round((double)item.Value / count, 4) * 100;

                prbInfo.Count += item.Value;
                prbInfo.PrbSetList.Add(prbSet);
            }
            prbInfo.PrbSetList.Sort(comparer);
            return prbInfo;
        }

        protected override void getResultsAfterQuery()
        {
            PrbInfo prbInfo = getPrbInfo(dicSumPrbCount, sumCount);
            if (prbInfo != null)
            {
                prbInfo.FileName = "汇总";
                prbInfo.SN = prbInfoList.Count + 1;
                prbInfoList.Add(prbInfo);
            }
        }

        private readonly Comparer comparer = new Comparer();
        private class Comparer : IComparer<PrbRangeSet>
        {
            public int Compare(PrbRangeSet x, PrbRangeSet y)
            {
                return x.Min - y.Min;
            }
        }

        protected virtual short? GetType(TestPoint tp)
        {
            return (short?)tp["lte_APP_type"];
        }
        protected virtual int? GetPDSCH(TestPoint tp)
        {
            return (int?)tp["lte_PDSCH_PRb_Num_slot"];
        }
    }

    public class LtePRBAnaQueryBase_FDD : LtePRBAnaQueryBase
    {
        private static LtePRBAnaQueryBase_FDD instance = null;
        public static new LtePRBAnaQueryBase_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LtePRBAnaQueryBase_FDD();
                    }
                }
            }
            return instance;
        }

        protected LtePRBAnaQueryBase_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);

            Columns = new List<string>();
            Columns.Add("lte_fdd_APP_type");
            Columns.Add("lte_fdd_PDSCH_PRb_Num_slot");
            carrierID = CarrierType.ChinaUnicom;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26034, this.Name);
        }

        protected override short? GetType(TestPoint tp)
        {
            return (short?)tp["lte_fdd_APP_type"];
        }
        protected override int? GetPDSCH(TestPoint tp)
        {
            return (int?)tp["lte_fdd_PDSCH_PRb_Num_slot"];
        }
    }

    public class PrbCondition
    {
        public PrbCondition()
        {
            PrbRangeValues = new RangeSet();
        }

        readonly int[] typeArray = new int[] { 2, 3, 12, 15, 25 };
        public int[] TypeArray 
        {
            get
            {
                return typeArray;
            }
        }

        public RangeSet PrbRangeValues { get; set; }
    }

    public class PrbInfo
    {
        public PrbInfo()
        {
            PrbSetList = new List<PrbRangeSet>();
        }
        public int SN { get; set; }
        public string FileName { get; set; }
        public int Count { get; set; }
        public List<PrbRangeSet> PrbSetList { get; set; }
    }
    public class PrbRangeSet
    {
        public PrbRangeSet(Range range)
        {
            PrbRange = range.ToString();
            Min = (int)range.Min;
        }
        public string PrbRange { get; set; }
        public int Min { get; set; }
        public double Per { get; set; }
        public int Count { get; set; }
    }
}
