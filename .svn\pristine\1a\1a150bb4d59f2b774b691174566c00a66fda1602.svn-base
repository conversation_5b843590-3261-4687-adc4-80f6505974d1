﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class DiyQueryAcceptLeveling_XJ : DIYSQLBase
    {
        public DiyQueryAcceptLeveling_XJ(int eci, FddDatabaseSetting setting)
            : base()
        {
            MainDB = true;
            this.setting = setting;
            this.eci = eci;
            ResultList = new List<AcceptLevelingInfo>();
        }

        #region 基础数据重写
        public override string Name
        {
            get { return "查询新疆单验平层测试数据"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }
        #endregion

        readonly FddDatabaseSetting setting;
        readonly int eci;
        public List<AcceptLevelingInfo> ResultList { get; private set; }

        protected override string getSqlTextString()
        {
            string spName = string.Format(@"{0}.{1}.[dbo].[{2}]", setting.ServerIp, setting.DbName, setting.TableNameHead);
            string strSQL = string.Format(@"EXEC {0} {1}", spName, eci);
            return strSQL;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[12];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_Float;
            rType[2] = E_VType.E_Float;
            rType[3] = E_VType.E_Float;
            rType[4] = E_VType.E_Float;
            rType[5] = E_VType.E_Float;
            rType[6] = E_VType.E_Float;
            rType[7] = E_VType.E_Float;
            rType[8] = E_VType.E_Float;
            rType[9] = E_VType.E_Float;
            rType[10] = E_VType.E_Float;
            rType[11] = E_VType.E_Float;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;

            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    AcceptLevelingInfo acceptInfo = new AcceptLevelingInfo();
                    acceptInfo.Fill(package, eci);
                    ResultList.Add(acceptInfo);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error(Name + " Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }
    }

    public class AcceptLevelingInfo
    {
        public int ECI { get; private set; }
        public string Name { get; private set; }
        public double AvgRsrp { get; private set; }
        public double AvgSinr { get; private set; }
        public double AvgDLSpeed { get; private set; }
        public double AvgULSpeed { get; private set; }
        public double MaxDLSpeed { get; private set; }
        public double MaxULSpeed { get; private set; }
        public double SinrMoreThan6Rate { get; private set; }
        public double SinrMoreThan9Rate { get; private set; }
        public double RsrpMoreThan105Rate { get; private set; }
        public double RsrpMoreThan95Rate { get; private set; }
        public double RsrpMoreThan85Rate { get; private set; }

        public void Fill(Package package, int eci)
        {
            ECI = eci;
            Name = ECI + "-" + package.Content.GetParamString();
            AvgRsrp = getDoubleValue(package);
            AvgSinr = getDoubleValue(package);
            SinrMoreThan6Rate = getDoubleValue(package);
            SinrMoreThan9Rate = getDoubleValue(package);
            RsrpMoreThan105Rate = getDoubleValue(package);
            RsrpMoreThan95Rate = getDoubleValue(package);
            RsrpMoreThan85Rate = getDoubleValue(package);
            AvgULSpeed = getDoubleValue(package);
            AvgDLSpeed = getDoubleValue(package);
            MaxULSpeed = getDoubleValue(package);
            MaxDLSpeed = getDoubleValue(package);
        }

        private double getDoubleValue(Package package)
        {
            return package.Content.GetParamInt() / 1000d;
        }
    }
}
