﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public static class HandoverAndReselectionManagerNew
    {
        /// <summary>
        /// 获取切换(重选)过频繁结果
        /// </summary>
        /// <param name="dtFileDataManager">文件列表</param>
        /// <param name="timeLimit">时间限制(多少秒内发生的)</param>
        /// <param name="distanceLimit">距离限制</param>
        /// <param name="handoverCount">切换次数</param>
        /// <param name="handoverEvents">输出的问题点涉及到的事件列表</param>
        /// <returns>形成过频繁问题的文件列表</returns>
        public static HandoverFileDataManagerNew GetHandoverTooMuchResult(DTFileDataManager dtFileDataManager, int timeLimit, int distanceLimit,
            int handoverCount)
        {
            DTFileDataManager dtFileDataManagerNew = new DTFileDataManager(dtFileDataManager.FileID, dtFileDataManager.FileName,
                dtFileDataManager.ProjectType, dtFileDataManager.TestType, dtFileDataManager.CarrierType, dtFileDataManager.LogTable,
                dtFileDataManager.SampleTableName, dtFileDataManager.ServiceType, dtFileDataManager.MoMtFlag);
            HandoverFileDataManagerNew item = new HandoverFileDataManagerNew(dtFileDataManagerNew);
            List<Event> events;
            List<List<Event>> eventsList;
            //在切换的事件中查询频繁切换的事件并记录
            if (isTooFrequent(dtFileDataManager.Events, timeLimit, distanceLimit, handoverCount, out events, out eventsList))
            {
                foreach (Event e in events)
                {
                    dtFileDataManagerNew.Add(e);
                }
                item.Events.AddRange(events);
                item.EventsList = eventsList;
                item.HandoverTimes = eventsList.Count;
            }
            return item;
        }

        //判断是否有对应条件下的过频繁事件发生
        private static bool isTooFrequent(List<Event> events, int secondLimit, int distanceLimit, int timesLimit, out List<Event> resultEvents,
            out List<List<Event>> eventsList)
        {
            return HandoverFileDataManagerNew.isTooFrequent(events, secondLimit, distanceLimit, timesLimit,
                out resultEvents, out eventsList);
        }

        #region 添加小区描述
        /// <summary>
        /// 获取切换重新事件小区变更序列描述
        /// </summary>
        /// <param name="list">切换重新事件列表</param>
        /// <returns></returns>
        public static string MakeCellUpdateStr(List<List<Event>> list)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < list.Count; i++)
            {
                sb.Append(MakeCellUpdateStr(list[i]));
                sb.Append("；\r\n");
            }
            return sb.ToString();
        }

        /// <summary>
        /// 获取切换重新事件小区变更序列描述
        /// </summary>
        /// <param name="eList"></param>
        /// <returns></returns>
        public static string MakeCellUpdateStr(List<Event> eList)
        {
            string lastLacCi = "";
            StringBuilder sb = new StringBuilder();
            for (int j = 0; j < eList.Count; j++)
            {
                Event e = eList[j];
                int eLac1 = (int)e["LAC"];
                int eCi1 = (int)e["CI"];
                int eLac2 = (int)e["TargetLAC"];
                int eCi2 = (int)e["TargetCI"];

                ICell iCellSrc = e.GetSrcCell();
                ICell iCellTar = e.GetTargetCell();
                string cellName = "";
                if (lastLacCi != (eLac1 + "_" + eCi1))
                {
                    addUpdateSign2StringBuilder(ref sb, e);
                    if (iCellSrc != null)
                    {
                        cellName = iCellSrc.Name + "(" + eLac1 + "," + eCi1 + ")";
                    }
                    else if (e.ID == 851) // LTE
                    {
                        cellName = "(TAC:" + eLac1 + ",ECI:" + eCi1 + ")";
                    }
                    else
                    {
                        cellName = "(LAC:" + eLac1 + ",CI:" + eCi1 + ")";
                    }
                    sb.Append(cellName);
                }

                addUpdateSign2StringBuilder(ref sb, e);
                if (iCellTar != null)
                {
                    cellName = iCellTar.Name + "(" + eLac2 + "," + eCi2 + ")";
                }
                else if (e.ID == 851)
                {
                    cellName = "(TAC:" + eLac1 + ",ECI:" + eCi1 + ")";
                }
                else
                {
                    cellName = "(LAC:" + eLac1 + ",CI:" + eCi1 + ")";
                }
                sb.Append(cellName);
                lastLacCi = eLac2 + "_" + eCi2;
            }
            return sb.ToString();
        }

        private static void addUpdateSign2StringBuilder(ref StringBuilder sb, Event e)
        {
            if (e.ID == 40 || e.ID == 137 || e.ID == 139 || e.ID == 179 || e.ID == 537
                    || e.ID == 539 || e.ID == 579)//小区重选
            {
                sb.Append("=>");
            }
            else
            {
                sb.Append("->");
            }
        }
        #endregion

        /// <summary>
        /// 根据频繁切换事件和采样点获取对应的详细数据并保存
        /// </summary>
        /// <param name="handoverFile"></param>
        /// <param name="fileDataManager"></param>
        public static void GetHandoverToMuchDetails(HandoverFileDataManagerNew handoverFile, DTFileDataManager fileDataManager)
        {
            if (handoverFile.EventsList.Count <= 0 || fileDataManager == null)
                return;

            for (int i = 0; i < handoverFile.Events.Count; i++)
            {
                Event ev = handoverFile.Events[i];
                List<HandoverCellItem> hoCellList = statHandoverCells(fileDataManager.TestPoints, handoverFile.Events, ev);
                HandoverItemNew handoverItem = new HandoverItemNew(ev);
                foreach (HandoverCellItem cellItem in hoCellList)
                {
                    if (cellItem == null) continue;
                    cellItem.Ev = ev;
                    if (cellItem.Type == HandoverCellType.BeforeHandover)
                        handoverItem.CellItemBefore = cellItem;
                    else
                        handoverItem.CellItemAfter = cellItem;
                }
                handoverFile.HandoverEventDic[ev] = handoverItem;
            }
            getHandoverItems(handoverFile, fileDataManager);
            fileDataManager.TestPoints.Clear();
        }

        #region statHandoverCells
        /// <summary>
        /// 根据切换事件获取切换前后小区数据
        /// </summary>
        /// <param name="dtfdm"></param>
        /// <param name="e"></param>
        /// <returns></returns>
        private static List<HandoverCellItem> statHandoverCells(List<TestPoint> testPoints, List<Event> events, Event e)
        {
            List<HandoverCellItem> hoCellList = new List<HandoverCellItem>();

            HandoverCellItem tps1 = new HandoverCellItem(e, HandoverCellType.BeforeHandover);  //切换前
            HandoverCellItem tps2 = new HandoverCellItem(e, HandoverCellType.AfterHandover);  //切换后

            hoCellList.Add(tps1);
            hoCellList.Add(tps2);

            int index = getChangeTestpointIndex(testPoints, e);
            if (index == -1)
            {
                return hoCellList;
            }

            TestPoint tp;

            //根据Index统计相关的点
            for (int i = index; i >= 0; i--)
            {//切换前3秒
                tp = testPoints[i];

                if (!checkTestPoint(tp, e, HandoverCellType.BeforeHandover))
                {
                    break;
                }
                AddTp2CellItem(tps1, tp);
            }

            #region 查询TAU更新前小区名
            if (e.ID == 853)
            {
                tps2.CellName = e.CellNameSrc;

                int evtIndex = getChangeEventIndex(events, e);
                for (int i = evtIndex; i >= 0; i--)
                {
                    Event evt = events[i];
                    if (evt.CellNameSrc != "" && evt.CellNameSrc != tps2.CellName)
                    {
                        tps1.CellName = evt.CellNameSrc;
                        break;
                    }
                }
            }
            #endregion

            for (int i = index + 1; i < testPoints.Count; i++)
            {
                tp = testPoints[i];

                if (!checkTestPoint(tp, e, HandoverCellType.AfterHandover))//切换后3秒
                {
                    break;
                }
                AddTp2CellItem(tps2, tp);
            }
            return hoCellList;
        }

        /// <summary>
        /// 查找切换点的索引
        /// </summary>
        /// <param name="cellItemAfter">TestPoint 集合</param>
        /// <param name="events">Event  集合</param>
        /// <returns></returns>
        private static int getChangeTestpointIndex(List<TestPoint> tpList, Event e)
        {
            int index = -1;
            for (int i = 0; i < tpList.Count; i++)
            {
                if (tpList[i].SN > e.SN)
                {
                    index = i - 1;
                    break;
                }
                if (tpList[i].SN == e.SN)
                {
                    index = i;
                    break;
                }
            }

            return index;
        }

        /// <summary>
        /// 检测是否属于有效范围内的切换点
        /// 1、切换前后 3 秒
        /// 2、切换前后 LAC、CI比较
        /// </summary>
        /// <param name="tp">切换所在的点</param>
        /// <param name="e">切换事件</param>
        /// <returns>true or false</returns>
        private static bool checkTestPoint(TestPoint tp, Event e, HandoverCellType cellType)
        {
            bool isFlag = false;
            int? lac = tp.GetLAC();
            int? ci = tp.GetCI();
            int? eLac1 = (int?)e["LAC"];
            int? eCi1 = (int?)e["CI"];

            if (lac == null || ci == null)
            {
                return isFlag;
            }

            long timeTestpoint = tp.Time * 1000L + tp.Millisecond;
            long tiemEvent = e.Time * 1000L + e.Millisecond;

            if (((cellType == HandoverCellType.BeforeHandover)
                    && ((timeTestpoint + 3 * 1000) >= tiemEvent))//比较时间范围(小于它的3秒内）
                && (lac == eLac1 && ci == eCi1))
            {
                isFlag = true;
            }
            else if (((cellType == HandoverCellType.AfterHandover)
                        && ((timeTestpoint - 3 * 1000) <= tiemEvent))) //比较时间范围(大于它的3秒内）
            //&& (lac == eLac2 && ci == eCi2)) //shielded by wj 切换后LAC,CI获取太慢，导致匹配不到，因此屏蔽
            {
                isFlag = true;
            }

            return isFlag;
        }

        /// <summary>
        /// 采样点算入相应小区信息
        /// </summary>
        /// <param name="cellItem">小区信息对象</param>
        /// <param name="tp">TestPoint</param>
        private static void AddTp2CellItem(HandoverCellItem cellItem, TestPoint tp)
        {
            int? lac = tp.GetLAC();
            int? ci = tp.GetCI();

            if (lac == null || ci == null || lac == 0 || ci == 0)
            {
                return;
            }

            if ((cellItem.Lac == lac && cellItem.Ci == ci) || cellItem.Ev.ID == 853 || cellItem.Ev.ID == 3172)
            {
                if (tp is WCDMATestPointDetail)
                {
                    setWcdmaCellItem(cellItem, tp);
                }
                else if (tp is TDTestPointDetail || tp is TDTestPointSummary)
                {
                    setTDCellItem(cellItem, tp);
                }
                else if (tp is LTETestPointDetail)
                {
                    setLteTddCellItem(cellItem, tp);
                }
                else if (tp is LTEFddTestPoint)
                {
                    setLteFddCellItem(cellItem, tp);
                }
                else
                {
                    setGsmCellItem(cellItem, tp);
                }
            }
        }

        private static void setGsmCellItem(HandoverCellItem cellItem, TestPoint tp)
        {
            object obj = tp["TA"];
            if (obj != null)
            {
                int ta = int.Parse(obj.ToString());
                cellItem.Ta += ta;
                cellItem.TaCount++;
            }
            obj = tp["RxLevSub"];
            if (obj != null)
            {
                int rxlev = int.Parse(obj.ToString());
                cellItem.Rxlev += rxlev;
                cellItem.RxlevCount++;
            }
            obj = tp["RxQualSub"];
            if (obj != null)
            {
                int rxqual = int.Parse(obj.ToString());
                cellItem.Rxqual += rxqual;
                cellItem.RxqualCount++;
            }
        }

        private static void setLteFddCellItem(HandoverCellItem cellItem, TestPoint tp)
        {
            float? rxlev = (float?)tp["lte_fdd_RSRP"];
            float? rxqual = (float?)tp["lte_fdd_SINR"];
            if (-141 <= rxlev && rxlev <= 25)
            {
                cellItem.Rxlev += (int)(float)rxlev;
                cellItem.RxlevCount++;
            }
            if (-50 <= rxqual && rxqual <= 50)
            {
                cellItem.Rxqual += (int)(float)rxqual;
                cellItem.RxqualCount++;
            }
        }

        private static void setLteTddCellItem(HandoverCellItem cellItem, TestPoint tp)
        {
            float? rxlev = (float?)tp["lte_RSRP"];
            float? rxqual = (float?)tp["lte_SINR"];
            if (-141 <= rxlev && rxlev <= 25)
            {
                cellItem.Rxlev += (int)(float)rxlev;
                cellItem.RxlevCount++;
            }
            if (-50 <= rxqual && rxqual <= 50)
            {
                cellItem.Rxqual += (int)(float)rxqual;
                cellItem.RxqualCount++;
            }
        }

        private static void setTDCellItem(HandoverCellItem cellItem, TestPoint tp)
        {
            DTDisplayParameterSystem dtd = DTDisplayParameterManager.GetInstance()["TD-SCDMA"];
            int ta = int.Parse(tp[MainModel.TD_TA].ToString());
            int rxlev = (int)(float?)tp["TD_PCCPCH_RSCP"];
            if (dtd[MainModel.TD_TA.Substring(3)].ValueMin <= ta && ta <= dtd[MainModel.TD_TA.Substring(3)].ValueMax)
            {
                cellItem.Ta += ta;
                cellItem.TaCount++;
            }
            if (dtd["PCCPCH_RSCP"].ValueMin <= rxlev && rxlev <= dtd["PCCPCH_RSCP"].ValueMax)
            {
                cellItem.Rxlev += rxlev;
                cellItem.RxlevCount++;
            }
        }

        private static void setWcdmaCellItem(HandoverCellItem cellItem, TestPoint tp)
        {
            DTDisplayParameterSystem dtd = DTDisplayParameterManager.GetInstance()["WCDMA"];
            int rxlev = (int)(float?)tp["W_Reference_RSCP"];
            int rxqual = (int)(float?)tp["W_Reference_Ec_Io"];
            if (dtd["Reference_RSCP"].ValueMin <= rxlev && rxlev <= dtd["Reference_RSCP"].ValueMax)
            {
                cellItem.Rxlev += rxlev;
                cellItem.RxlevCount++;
            }
            if (dtd["Reference_Ec_Io"].ValueMin <= rxqual && rxqual <= dtd["Reference_Ec_Io"].ValueMax)
            {
                cellItem.Rxqual += rxqual;
                cellItem.RxqualCount++;
            }
        }

        private static int getChangeEventIndex(List<Event> evtList, Event e)
        {
            int index = -1;
            for (int i = 0; i < evtList.Count; i++)
            {
                if (evtList[i].SN > e.SN)
                {
                    index = i - 1;
                    break;
                }
                if (evtList[i].SN == e.SN)
                {
                    index = i;
                    break;
                }
            }

            return index;
        }
        #endregion

        #region getHandoverItems 获取三级HandoverItems数据
        /// <summary>
        /// 获取采样点的详细指标数据
        /// </summary>
        /// <param name="dtfdmi"></param>
        /// <param name="fileMngr"></param>
        private static void getHandoverItems(HandoverFileDataManagerNew dtfdmi, DTFileDataManager fileMngr)
        {
            if (dtfdmi.EventsList.Count <= 0)
            {
                return;
            }
            foreach (List<Event> eList in dtfdmi.EventsList)
            {
                if (eList.Count > 1)
                {
                    HandoverProblemItemNew item = new HandoverProblemItemNew();
                    item.Index = dtfdmi.HandoverItems.Count + 1;
                    dtfdmi.HandoverItems.Add(item);
                    for (int i = 0; i < eList.Count; i++)
                    {
                        Event e = eList[i];
                        // 同一个e可能存在不同切换组中，导致hoItem被重新修改Index，故加上Clone修正
                        HandoverItemNew hoItem = dtfdmi.HandoverEventDic[e].Clone();
                        hoItem.Index = i + 1;
                        item.HandoverItems.Add(hoItem);
                    }
                    fillHandoverItem(item);

                    if (fileMngr.ServiceType == (int)ServiceType.LTE_TDD_DATA
                        || fileMngr.ServiceType == (int)ServiceType.LTE_TDD_MULTI
                        || fileMngr.ServiceType == (int)ServiceType.LTE_TDD_VOICE
                        || fileMngr.ServiceType == (int)ServiceType.LTE_FDD_DATA
                        || fileMngr.ServiceType == (int)ServiceType.LTE_FDD_MULTI
                        || fileMngr.ServiceType == (int)ServiceType.LTE_FDD_VOICE)
                    {
                        setTestPointData(fileMngr, eList, item);
                    }
                }
            }
        }

        private static void setTestPointData(DTFileDataManager fileMngr, List<Event> eList, HandoverProblemItemNew item)
        {
            if (eList.Count > 1)
            {
                int bSn = eList[0].SN;
                int eSn = eList[eList.Count - 1].SN;
                int rsrpNum = 0;
                double rsrpSum = 0;
                int sinrNum = 0;
                double sinrSum = 0;
                int speedNum = 0;
                double speedSum = 0;
                int grantNum = 0;
                double grantSum = 0;
                bool isfdd = false;
                int iAppSeepNum = 0;
                double dAppSeepSum = 0;
                foreach (TestPoint tp in fileMngr.TestPoints)
                {
                    if (tp is LTEFddTestPoint)
                    {
                        isfdd = true;
                    }
                    else if (tp is LTETestPointDetail)
                    {
                        isfdd = false;
                    }
                    if (tp.SN < bSn)
                    {
                        continue;
                    }
                    if (tp.SN > eSn)
                    {
                        break;
                    }
                    setRsrp(ref rsrpNum, ref rsrpSum, tp);
                    setSinr(ref sinrNum, ref sinrSum, tp);
                    setSpeed(ref speedNum, ref speedSum, tp);
                    setGrant(ref grantNum, ref grantSum, tp);
                    setAppSeep(ref iAppSeepNum, ref dAppSeepSum, tp);
                }
                setRsrpNum(item, rsrpNum, rsrpSum, isfdd);
                setSinrNum(item, sinrNum, sinrSum, isfdd);
                setSpeedNum(item, speedNum, speedSum, isfdd);
                setGrantNum(item, grantNum, grantSum, isfdd);
                setAppSeepNum(item, isfdd, iAppSeepNum, dAppSeepSum);
            }
        }

        private static void setRsrp(ref int rsrpNum, ref double rsrpSum, TestPoint tp)
        {
            float? rsrp = (float?)tp["lte_RSRP"];
            if (tp is LTEFddTestPoint)
            {
                rsrp = (float?)tp["lte_fdd_RSRP"];
            }

            if (-141 <= rsrp && rsrp <= 25)
            {
                rsrpNum++;
                rsrpSum += (float)rsrp;
            }
        }

        private static void setSinr(ref int sinrNum, ref double sinrSum, TestPoint tp)
        {
            float? sinr = (float?)tp["lte_SINR"];
            if (tp is LTEFddTestPoint)
            {
                sinr = (float?)tp["lte_fdd_SINR"];
            }

            if (-50 <= sinr && sinr <= 50)
            {
                sinrNum++;
                sinrSum += (float)sinr;
            }
        }

        private static void setSpeed(ref int speedNum, ref double speedSum, TestPoint tp)
        {
            double? speed = (double?)tp["lte_PDCP_DL_Mb"];
            if (tp is LTEFddTestPoint)
            {
                speed = (double?)tp["lte_fdd_PDCP_DL_Mb"];
            }

            if (0 < speed)
            {
                speedNum++;
                speedSum += (double)speed;
            }
        }

        private static void setGrant(ref int grantNum, ref double grantSum, TestPoint tp)
        {
            short? grant = (short?)tp["lte_PDCCH_DL_Grant_Count"];
            if (tp is LTEFddTestPoint)
            {
                grant = (short?)tp["lte_fdd_PDCCH_DL_Grant_Count"];
            }
            if (0 <= grant)
            {
                grantNum++;
                grantSum += (short)grant;
            }
        }

        private static void setAppSeep(ref int iAppSeepNum, ref double dAppSeepSum, TestPoint tp)
        {
            double? dAppSeep = (double?)(int?)tp["lte_APP_ThroughputDL"];
            if (tp.FileName.Contains("上传"))
            {
                dAppSeep = (double?)(int?)tp["lte_APP_ThroughputUL"];
            }
            if (tp is LTEFddTestPoint)
            {
                dAppSeep = (double?)(int?)tp["lte_fdd_APP_ThroughputDL"];
            }
            if (dAppSeep >= 0)
            {
                iAppSeepNum++;
                dAppSeepSum += (double)dAppSeep;
            }
        }

        private static void setRsrpNum(HandoverProblemItemNew item, int rsrpNum, double rsrpSum, bool isfdd)
        {
            if (rsrpNum != 0)
            {
                if (isfdd)
                {
                    item.Param["lte_fdd_RSRP_Avg"] = Math.Round(rsrpSum / rsrpNum, 2);
                }
                else
                {
                    item.Param["lte_RSRP_Avg"] = Math.Round(rsrpSum / rsrpNum, 2);
                }
            }
        }

        private static void setSinrNum(HandoverProblemItemNew item, int sinrNum, double sinrSum, bool isfdd)
        {
            if (sinrNum != 0)
            {
                if (isfdd)
                {
                    item.Param["lte_fdd_SINR_Avg"] = Math.Round(sinrSum / sinrNum, 2);
                }
                else
                {
                    item.Param["lte_SINR_Avg"] = Math.Round(sinrSum / sinrNum, 2);
                }
            }
        }

        private static void setSpeedNum(HandoverProblemItemNew item, int speedNum, double speedSum, bool isfdd)
        {
            if (speedNum != 0)
            {
                if (isfdd)
                {
                    item.Param["lte_fdd_PDCP_DL_Mb_Avg"] = Math.Round(speedSum / speedNum, 2);
                }
                else
                {
                    item.Param["lte_PDCP_DL_Mb_Avg"] = Math.Round(speedSum / speedNum, 2);
                }
            }
        }

        private static void setGrantNum(HandoverProblemItemNew item, int grantNum, double grantSum, bool isfdd)
        {
            if (grantNum != 0)
            {
                if (isfdd)
                {
                    item.Param["lte_fdd_PDCCH_DL_Grant_Count_Avg"] = Math.Round(grantSum / grantNum, 2);
                }
                else
                {
                    item.Param["lte_PDCCH_DL_Grant_Count_Avg"] = Math.Round(grantSum / grantNum, 2);
                }
            }
        }

        private static void setAppSeepNum(HandoverProblemItemNew item, bool isfdd, int iAppSeepNum, double dAppSeepSum)
        {
            if (iAppSeepNum != 0)
            {
                if (isfdd)
                {
                    item.Param["lte_fdd_APP_ThroughputDL_Num"] = iAppSeepNum;
                    item.Param["lte_fdd_APP_ThroughputDL_Avg"] = Math.Round(dAppSeepSum / iAppSeepNum / 1024.0 / 1024.0, 2);
                }
                else
                {
                    item.Param["lte_APP_ThroughputDL_Num"] = iAppSeepNum;
                    item.Param["lte_APP_ThroughputDL_Avg"] = Math.Round(dAppSeepSum / iAppSeepNum / 1024.0 / 1024.0, 2);
                }
            }
        }

        private static void fillHandoverItem(HandoverProblemItemNew item)
        {
            List<Event> eList = new List<Event>();
            foreach (HandoverItemNew cellItem in item.HandoverItems)
            {
                if (!eList.Contains(cellItem.CurEvent))
                {
                    eList.Add(cellItem.CurEvent);
                }
            }
            item.Name = HandoverAndReselectionManagerNew.MakeCellUpdateStr(eList);
            item.Events = eList;
        }
        #endregion
    }

    /// <summary>
    /// 一级切换文件列表
    /// </summary>
    public class HandoverFileDataManagerNew
    {
        public DTFileDataManager fmnger { get; set; }
        public HandoverFileDataManagerNew(DTFileDataManager fmnger)
        {
            this.fmnger = fmnger;
        }
                
        /// <summary>
        /// 文件序号 用于导出
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 文件名 用于导出和TreeView显示
        /// </summary>
        public string Name
        {
            get { return fmnger.FileName; }
        }
      
        public List<Event> Events { get; } = new List<Event>();

        public List<List<Event>> EventsList { get; set; } = new List<List<Event>>();

        /// <summary>
        /// 过频繁组数
        /// </summary>
        public int HandoverTimes { get; set; }

        //事件对应的切换数据
        public Dictionary<Event, HandoverItemNew> HandoverEventDic { get; set; } = new Dictionary<Event, HandoverItemNew>();

        //切换数据集合
        public List<HandoverProblemItemNew> HandoverItems { get; set; } = new List<HandoverProblemItemNew>();

        //判断是否过频繁
        public static bool isTooFrequent(List<Event> events, int secondLimit, int distanceLimit, int timesLimit,
            out List<Event> resultEvents, out List<List<Event>> eventsList)
        {
            resultEvents = new List<Event>();
            eventsList = new List<List<Event>>();
            if (events.Count == 0)
            {
                return false;
            }
            
            WindowSplitter splitter = new WindowSplitter(secondLimit, distanceLimit, timesLimit, events);
            foreach (WindowSplitter.EventWindow win in splitter.WindowSet)
            {
                resultEvents.AddRange(win.GetEventSet());
                eventsList.Add(win.GetEventSet());
            }
            return resultEvents.Count > 0;
        }
    }

    /// <summary>
    /// 二级切换集合(切换或者重选的问题类)
    /// </summary>
    public class HandoverProblemItemNew
    {
        /// <summary>
        /// 切换序号 用于导出
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 切换名称 用于导出
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// 对应的三级切换详细数据
        /// </summary>
        public List<HandoverItemNew> HandoverItems { get; set; } = new List<HandoverItemNew>();

        /// <summary>
        /// 事件合集
        /// </summary>       
        public List<Event> Events { get; set; } = new List<Event>();

        /// <summary>
        /// 参数合集
        /// </summary>
        public Dictionary<string, object> Param { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 三级切换详细数据(单个切换或者重选事件类)
    /// </summary>
    public class HandoverItemNew
    {
        public HandoverItemNew(Event e)
        {
            CurEvent = e;
        }
       
        /// <summary>
        /// 小区序号 用于导出
        /// </summary>
        public int Index { get; set; }

        public Event CurEvent { get; set; }

        /// <summary>
        /// 切换前小区
        /// </summary>
        public string CellNameSrc
        {
            get { return CurEvent.CellNameSrc; }
        }

        /// <summary>
        /// 切换后小区
        /// </summary>
        public string CellNameTarget
        {
            get { return CurEvent.CellNameTarget; }
        }

        /// <summary>
        /// 详细数据中的时间格式
        /// </summary>
        public string TimeString
        {
            get { return CurEvent.DateTime.ToString("HH:mm:ss.fff"); }
        }

        /// <summary>
        /// 格式化切换小区
        /// </summary>
        public string HandOverDec
        {
            get
            {
                StringBuilder sbHandover = new StringBuilder();
                sbHandover.Append("【");
                sbHandover.Append(CurEvent.CellNameSrc);
                sbHandover.Append("】 -> 【");
                sbHandover.Append(CurEvent.CellNameTarget);
                sbHandover.Append("】");
                return sbHandover.ToString();
            }
        }
        
        public HandoverCellItem CellItemBefore { get; set; }
        public HandoverCellItem CellItemAfter { get; set; }

        #region Excel导出数据
        /// <summary>
        /// 小区名称
        /// </summary>
        public string Name
        {
            get
            {
                return CellItemBefore.CellName + "->" + CellItemAfter.CellName;
            }
        }

        public string DateTimeString
        {
            get { return CurEvent.DateTime.ToString("yyyy-MM-dd HH:mm:ss"); }
        }

        public double Longitude
        {
            get { return CurEvent.Longitude; }
        }

        public double Latitude
        {
            get { return CurEvent.Latitude; }
        }

        /// <summary>
        /// 切换前RSRP
        /// </summary>
        public string RxLevAvgStringBefore
        {
            get { return getValidData(CellItemBefore.Rxlev, CellItemBefore.RxqualCount); }
        }

        /// <summary>
        /// 切换后RSRP
        /// </summary>
        public string RxLevAvgStringAfter
        {
            get { return getValidData(CellItemAfter.Rxlev, CellItemAfter.RxqualCount); }
        }

        /// <summary>
        /// 切换前SINR
        /// </summary>
        public string RxQualStringBefore
        {
            get { return getValidData(CellItemBefore.Rxqual, CellItemBefore.RxqualCount); }
        }

        /// <summary>
        /// 切换后SINR
        /// </summary>
        public string RxQualStringAfter
        {
            get { return getValidData(CellItemAfter.Rxqual, CellItemAfter.RxqualCount); }
        }

        /// <summary>
        /// 切换前TA
        /// </summary>
        public string TAStringBefore
        {
            get { return getValidData(CellItemBefore.Ta, CellItemBefore.TaCount); }
        }

        /// <summary>
        /// 切换后TA
        /// </summary>
        public string TAStringAfter
        {
            get { return getValidData(CellItemAfter.Ta, CellItemAfter.TaCount); }
        }

        public string getValidData(double value1, double value2)
        {
            if (value2 == 0)
            {
                return " _  ";
            }
            else
            {
                return (value1 / value2).ToString("0.00");
            }
        }
        #endregion

        public object Tag { get { return CellItemAfter; } }

        public HandoverItemNew Clone()
        {
            return this.MemberwiseClone() as HandoverItemNew;
        }
    }
}
