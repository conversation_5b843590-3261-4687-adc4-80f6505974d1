﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MapWinGIS;

namespace MasterCom.MapSpaceManager.MapSpaceManager
{
    public partial class LayerInfoPanel : UserControl
    {
        public event EventHandler LayerPropertyChanged;
        public VecLayerItem m_layer = null;
        AxMapWinGIS.AxMap mapControl = null;
        
        private Dictionary<string, LinePatternItem> patternDic = new Dictionary<string, LinePatternItem>();
        public LayerInfoPanel()
        {
            InitializeComponent();

            List<LinePatternItem> linePatternList = LinePatternItem.InitCustomLineType();
            cbxLineType.Items.Clear();
            for (int i = 0; i < linePatternList.Count; i++)
            {
                LinePatternItem pattern = linePatternList[i];
                patternDic[pattern.name] = pattern;
                cbxLineType.Items.Add(pattern);
            }
        }

        
        public void showInfo(AxMapWinGIS.AxMap mapControl,VecLayerItem layer)
        {
            this.mapControl = mapControl;
            if (layer != null)
            {
                m_layer = layer;
                cbxBgFill.Checked = layer.style_bg_fill == 1;
                cbxIsRoad.Checked = layer.is_road_layer == 1;
                cbxRange.Checked = layer.visible_range_enable == 1;
                cbxVisible.Checked = layer.visible == 1 ? true : false;

                txtRangMax.Text = layer.visible_range_max.ToString();
                txtRangMin.Text = layer.visible_range_min.ToString();
                pnBgColor.BackColor = layer.style_bg_color;
                pnLineColor.BackColor = layer.style_line_color;
                nudLineWidth.Value = (decimal)layer.style_line_width;
                //=====line type start
                if(layer.style_line_patten!=null)
                {
                    LinePatternItem pattern = null;
                    if (patternDic.TryGetValue(layer.style_line_patten, out pattern))
                    {
                        cbxLineType.SelectedItem = pattern;
                    }
                    else
                    {
                        cbxLineType.SelectedIndex = 0;
                    }
                }
                else
                {
                    cbxLineType.SelectedIndex = 0;
                }
                //====line type end
                tbBg.Value = (int)(layer.style_bg_opaque * 255);
                cbxShowLabel.Checked = layer.label_show == 1;
                List<string> nameColumnNames = new List<string>();
                int numFields = layer._sfile.NumFields;
                for (int x = 0; x < numFields; x++)
                {
                    MapWinGIS.Field field = layer._sfile.get_Field(x);
                    nameColumnNames.Add(field.Name);
                }
                cbxLabelField.Items.Clear();
                cbxBgColorCol.Items.Clear();
                cbxBgColorCol.Items.Add("[不使用]");
                for (int x = 0; x < numFields; x++)
                {
                    cbxLabelField.Items.Add(nameColumnNames[x]);
                    cbxBgColorCol.Items.Add(nameColumnNames[x]);
                }
                if (layer.label_field != null && layer.label_field != "")
                {
                    cbxLabelField.SelectedItem = layer.label_field;
                }
                if (cbxLabelField.SelectedItem == null)
                {
                    if (cbxLabelField.Items.Count > 0)
                    {
                        cbxLabelField.SelectedIndex = 0;
                    }
                }
                if (layer.style_bg_color_column != null && layer.style_bg_color_column != "")
                {
                    cbxBgColorCol.SelectedItem = layer.style_bg_color_column;
                }
                if (cbxBgColorCol.SelectedItem == null)
                {
                    if (cbxBgColorCol.Items.Count > 0)
                    {
                        cbxBgColorCol.SelectedIndex = 0;
                    }
                }
                numLabelFontSize.Value = layer.label_font_size;
                lbLabelFontColor.BackColor = layer.label_font_color;

            }
        }

        private void cbxRange_CheckedChanged(object sender, EventArgs e)
        {
            bool rangeEnabled = cbxRange.Checked;
            m_layer.visible_range_enable = rangeEnabled ? 1 : 0;
            txtRangMax.Enabled = rangeEnabled;
            txtRangMin.Enabled = rangeEnabled;
            btnScaleCurMax.Enabled = rangeEnabled;
            btnScaleCurMin.Enabled = rangeEnabled;
            VecArg arg = new VecArg();
            arg.vecItem = m_layer;
            LayerPropertyChanged(this, arg);
        }

        private void txtRangMax_TextChanged(object sender, EventArgs e)
        {
            int value = m_layer.visible_range_max;
            int.TryParse(txtRangMax.Text, out value);
            m_layer.visible_range_max = value;
            VecArg arg = new VecArg();
            arg.vecItem = m_layer;
            LayerPropertyChanged(this, arg);
        }

        private void txtRangMin_TextChanged(object sender, EventArgs e)
        {
            int value = m_layer.visible_range_min;
            int.TryParse(txtRangMin.Text, out value);
            m_layer.visible_range_min = value;
            VecArg arg = new VecArg();
            arg.vecItem = m_layer;
            LayerPropertyChanged(this, arg);
        }

        private void cbxVisible_CheckedChanged(object sender, EventArgs e)
        {
            m_layer.visible = cbxVisible.Checked == true ? 1 : 0;
            VecArg arg = new VecArg();
            arg.vecItem = m_layer;
            LayerPropertyChanged(this, arg);
        }

        private void cbxIsRoad_CheckedChanged(object sender, EventArgs e)
        {
            m_layer.is_road_layer = cbxIsRoad.Checked == true ? 1 : 0;
            VecArg arg = new VecArg();
            arg.vecItem = m_layer;
            LayerPropertyChanged(this, arg);
        }

        private void cbxShowLabel_CheckedChanged(object sender, EventArgs e)
        {
            m_layer.label_show = cbxShowLabel.Checked == true ? 1 : 0;
            cbxLabelField.Visible = cbxShowLabel.Checked;
            numLabelFontSize.Enabled = cbxShowLabel.Checked;
            lbLabelFontColor.Enabled = cbxShowLabel.Checked;
            VecArg arg = new VecArg();
            arg.vecItem = m_layer;
            LayerPropertyChanged(this, arg);
        }
        private void cbxLabelField_SelectedIndexChanged(object sender, EventArgs e)
        {
            string selField = cbxLabelField.SelectedItem as String;
            if (selField != null)
            {
                m_layer.label_field = selField;
                VecArg arg = new VecArg();
                arg.vecItem = m_layer;
                LayerPropertyChanged(this, arg);
            }
            
        }

        private void pnBgColor_Click(object sender, EventArgs e)
        {
            selectColor(pnBgColor);
            m_layer.style_bg_color = pnBgColor.BackColor;
            VecArg arg = new VecArg();
            arg.vecItem = m_layer;
            LayerPropertyChanged(this, arg);
        }

        private void selectColor(Panel pnColor)
        {
            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                pnColor.BackColor = colorDialog.Color;
            }
        }

        private void pnLineColor_Click(object sender, EventArgs e)
        {
            selectColor(pnLineColor);
            m_layer.style_line_color = pnLineColor.BackColor;
            VecArg arg = new VecArg();
            arg.vecItem = m_layer;
            LayerPropertyChanged(this, arg);
        }

        private void tbBg_Scroll(object sender, EventArgs e)
        {
            m_layer.style_bg_opaque = tbBg.Value / 255f;
            VecArg arg = new VecArg();
            arg.vecItem = m_layer;
            LayerPropertyChanged(this, arg);
        }

        private void cbxBgFill_CheckedChanged(object sender, EventArgs e)
        {
            m_layer.style_bg_fill = cbxBgFill.Checked == true ? 1 : 0;
            pnBgColor.Enabled = tbBg.Enabled = cbxBgFill.Checked;
            VecArg arg = new VecArg();
            arg.vecItem = m_layer;
            LayerPropertyChanged(this, arg);
        }

        private void nudLineWidth_ValueChanged(object sender, EventArgs e)
        {
            m_layer.style_line_width = (int)nudLineWidth.Value;
            VecArg arg = new VecArg();
            arg.vecItem = m_layer;
            LayerPropertyChanged(this, arg);
        }

        private void btnScaleCurMin_Click(object sender, EventArgs e)
        {
            txtRangMin.Text = ""+(int)mapControl.CurrentScale;
        }

        private void btnScaleCurMax_Click(object sender, EventArgs e)
        {
            txtRangMax.Text = "" + (int)mapControl.CurrentScale; 
        }

        private void numLabelFontSize_ValueChanged(object sender, EventArgs e)
        {
            m_layer.label_font_size = (int)numLabelFontSize.Value;
            VecArg arg = new VecArg();
            arg.vecItem = m_layer;
            LayerPropertyChanged(this, arg);
        }

        private void lbLabelFontColor_Click(object sender, EventArgs e)
        {
            selectColor(lbLabelFontColor);
            m_layer.label_font_color = lbLabelFontColor.BackColor;
            VecArg arg = new VecArg();
            arg.vecItem = m_layer;
            LayerPropertyChanged(this, arg);
        }

        private void cbxLineType_SelectedIndexChanged(object sender, EventArgs e)
        {
            LinePatternItem item = cbxLineType.SelectedItem as LinePatternItem;
            if (item != null)
            {
                m_layer.style_line_patten = item.name;
                VecArg arg = new VecArg();
                arg.vecItem = m_layer;
                LayerPropertyChanged(this, arg);
            }
            
        }

        private void cbxBgColorCol_SelectedIndexChanged(object sender, EventArgs e)
        {
            string selField = cbxBgColorCol.SelectedItem as String;
            if (selField == null || selField == "[不使用]")
            {
                selField = "";
            }
            m_layer.style_bg_color_column = selField;
            VecArg arg = new VecArg();
            arg.vecItem = m_layer;
            LayerPropertyChanged(this, arg);
        }
        

    }
    public class VecArg : EventArgs
    {
        public VecLayerItem vecItem = null;
    }
}
