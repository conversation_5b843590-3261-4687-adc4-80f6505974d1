﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LtePlanningCellSettingForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.txtExcel = new System.Windows.Forms.TextBox();
            this.btnExcel = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.chkNotOpened = new System.Windows.Forms.CheckBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.numSampleLessCount = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.chkSampleLess = new System.Windows.Forms.CheckBox();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.label22 = new System.Windows.Forms.Label();
            this.numWeakCoverCellDistance = new System.Windows.Forms.NumericUpDown();
            this.label23 = new System.Windows.Forms.Label();
            this.numWeakCoverSampleCount = new System.Windows.Forms.NumericUpDown();
            this.label24 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.numWeakCoverRate = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.numWeakCoverRsrp = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.chkWeakCoverage = new System.Windows.Forms.CheckBox();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.label17 = new System.Windows.Forms.Label();
            this.numWeakQualCellDistance = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.numWeakQualSampleCount = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.numWeakQualRate = new System.Windows.Forms.NumericUpDown();
            this.label14 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.numWeakQualSinr = new System.Windows.Forms.NumericUpDown();
            this.label10 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.numWeakQualRsrp = new System.Windows.Forms.NumericUpDown();
            this.label12 = new System.Windows.Forms.Label();
            this.chkWeakQuality = new System.Windows.Forms.CheckBox();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.label19 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.numOverCoverCellDistance = new System.Windows.Forms.NumericUpDown();
            this.numOverCoverRate = new System.Windows.Forms.NumericUpDown();
            this.label20 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.numOverCoverSampleCount = new System.Windows.Forms.NumericUpDown();
            this.numOverCoverRadiusRatio = new System.Windows.Forms.NumericUpDown();
            this.label21 = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.chkOverCoverage = new System.Windows.Forms.CheckBox();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnReset = new System.Windows.Forms.Button();
            this.label25 = new System.Windows.Forms.Label();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleLessCount)).BeginInit();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverCellDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverSampleCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverRsrp)).BeginInit();
            this.groupBox6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakQualCellDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakQualSampleCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakQualRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakQualSinr)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakQualRsrp)).BeginInit();
            this.groupBox7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverCellDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverSampleCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverRadiusRatio)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.txtExcel);
            this.groupBox1.Controls.Add(this.btnExcel);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Location = new System.Drawing.Point(21, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(540, 68);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "文件";
            // 
            // txtExcel
            // 
            this.txtExcel.Location = new System.Drawing.Point(118, 26);
            this.txtExcel.Name = "txtExcel";
            this.txtExcel.ReadOnly = true;
            this.txtExcel.Size = new System.Drawing.Size(326, 22);
            this.txtExcel.TabIndex = 2;
            // 
            // btnExcel
            // 
            this.btnExcel.Location = new System.Drawing.Point(462, 26);
            this.btnExcel.Name = "btnExcel";
            this.btnExcel.Size = new System.Drawing.Size(75, 23);
            this.btnExcel.TabIndex = 1;
            this.btnExcel.Text = "选择";
            this.btnExcel.UseVisualStyleBackColor = true;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(25, 29);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(89, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "Excel工参文件:";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.chkNotOpened);
            this.groupBox2.Location = new System.Drawing.Point(21, 86);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(261, 63);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "基站";
            // 
            // chkNotOpened
            // 
            this.chkNotOpened.AutoSize = true;
            this.chkNotOpened.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkNotOpened.Location = new System.Drawing.Point(51, 28);
            this.chkNotOpened.Name = "chkNotOpened";
            this.chkNotOpened.Size = new System.Drawing.Size(84, 16);
            this.chkNotOpened.TabIndex = 2;
            this.chkNotOpened.Text = "未开通检查";
            this.chkNotOpened.UseVisualStyleBackColor = true;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.numSampleLessCount);
            this.groupBox3.Controls.Add(this.label3);
            this.groupBox3.Controls.Add(this.chkSampleLess);
            this.groupBox3.Location = new System.Drawing.Point(21, 155);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(261, 66);
            this.groupBox3.TabIndex = 2;
            this.groupBox3.TabStop = false;
            // 
            // numSampleLessCount
            // 
            this.numSampleLessCount.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSampleLessCount.Location = new System.Drawing.Point(131, 29);
            this.numSampleLessCount.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numSampleLessCount.Name = "numSampleLessCount";
            this.numSampleLessCount.Size = new System.Drawing.Size(80, 21);
            this.numSampleLessCount.TabIndex = 5;
            this.numSampleLessCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSampleLessCount.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label3.Location = new System.Drawing.Point(49, 34);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(77, 12);
            this.label3.TabIndex = 4;
            this.label3.Text = "采样点个数≤";
            // 
            // chkSampleLess
            // 
            this.chkSampleLess.AutoSize = true;
            this.chkSampleLess.Location = new System.Drawing.Point(26, 0);
            this.chkSampleLess.Name = "chkSampleLess";
            this.chkSampleLess.Size = new System.Drawing.Size(98, 18);
            this.chkSampleLess.TabIndex = 3;
            this.chkSampleLess.Text = "空口问题检查";
            this.chkSampleLess.UseVisualStyleBackColor = true;
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.label22);
            this.groupBox5.Controls.Add(this.numWeakCoverCellDistance);
            this.groupBox5.Controls.Add(this.label23);
            this.groupBox5.Controls.Add(this.numWeakCoverSampleCount);
            this.groupBox5.Controls.Add(this.label24);
            this.groupBox5.Controls.Add(this.label8);
            this.groupBox5.Controls.Add(this.numWeakCoverRate);
            this.groupBox5.Controls.Add(this.label7);
            this.groupBox5.Controls.Add(this.label6);
            this.groupBox5.Controls.Add(this.numWeakCoverRsrp);
            this.groupBox5.Controls.Add(this.label5);
            this.groupBox5.Controls.Add(this.chkWeakCoverage);
            this.groupBox5.Location = new System.Drawing.Point(300, 86);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(261, 167);
            this.groupBox5.TabIndex = 4;
            this.groupBox5.TabStop = false;
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label22.Location = new System.Drawing.Point(206, 127);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(17, 12);
            this.label22.TabIndex = 27;
            this.label22.Text = "米";
            // 
            // numWeakCoverCellDistance
            // 
            this.numWeakCoverCellDistance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakCoverCellDistance.Location = new System.Drawing.Point(120, 123);
            this.numWeakCoverCellDistance.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numWeakCoverCellDistance.Name = "numWeakCoverCellDistance";
            this.numWeakCoverCellDistance.Size = new System.Drawing.Size(80, 21);
            this.numWeakCoverCellDistance.TabIndex = 26;
            this.numWeakCoverCellDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakCoverCellDistance.Value = new decimal(new int[] {
            500,
            0,
            0,
            0});
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label23.Location = new System.Drawing.Point(48, 127);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(65, 12);
            this.label23.TabIndex = 25;
            this.label23.Text = "小区距离≤";
            // 
            // numWeakCoverSampleCount
            // 
            this.numWeakCoverSampleCount.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakCoverSampleCount.Location = new System.Drawing.Point(121, 92);
            this.numWeakCoverSampleCount.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numWeakCoverSampleCount.Name = "numWeakCoverSampleCount";
            this.numWeakCoverSampleCount.Size = new System.Drawing.Size(80, 21);
            this.numWeakCoverSampleCount.TabIndex = 24;
            this.numWeakCoverSampleCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakCoverSampleCount.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label24.Location = new System.Drawing.Point(48, 96);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(65, 12);
            this.label24.TabIndex = 23;
            this.label24.Text = "采样点数≥";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label8.Location = new System.Drawing.Point(210, 67);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(11, 12);
            this.label8.TabIndex = 9;
            this.label8.Text = "%";
            // 
            // numWeakCoverRate
            // 
            this.numWeakCoverRate.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakCoverRate.Location = new System.Drawing.Point(122, 60);
            this.numWeakCoverRate.Name = "numWeakCoverRate";
            this.numWeakCoverRate.Size = new System.Drawing.Size(80, 21);
            this.numWeakCoverRate.TabIndex = 8;
            this.numWeakCoverRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakCoverRate.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label7.Location = new System.Drawing.Point(72, 64);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(41, 12);
            this.label7.TabIndex = 7;
            this.label7.Text = "比例≥";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label6.Location = new System.Drawing.Point(210, 34);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(23, 12);
            this.label6.TabIndex = 6;
            this.label6.Text = "dBm";
            // 
            // numWeakCoverRsrp
            // 
            this.numWeakCoverRsrp.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakCoverRsrp.Location = new System.Drawing.Point(122, 29);
            this.numWeakCoverRsrp.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numWeakCoverRsrp.Name = "numWeakCoverRsrp";
            this.numWeakCoverRsrp.Size = new System.Drawing.Size(80, 21);
            this.numWeakCoverRsrp.TabIndex = 5;
            this.numWeakCoverRsrp.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakCoverRsrp.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.Location = new System.Drawing.Point(68, 33);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(41, 12);
            this.label5.TabIndex = 4;
            this.label5.Text = "RSRP≤";
            // 
            // chkWeakCoverage
            // 
            this.chkWeakCoverage.AutoSize = true;
            this.chkWeakCoverage.Location = new System.Drawing.Point(26, 0);
            this.chkWeakCoverage.Name = "chkWeakCoverage";
            this.chkWeakCoverage.Size = new System.Drawing.Size(86, 18);
            this.chkWeakCoverage.TabIndex = 3;
            this.chkWeakCoverage.Text = "弱覆盖检查";
            this.chkWeakCoverage.UseVisualStyleBackColor = true;
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.label25);
            this.groupBox6.Controls.Add(this.label17);
            this.groupBox6.Controls.Add(this.numWeakQualCellDistance);
            this.groupBox6.Controls.Add(this.label4);
            this.groupBox6.Controls.Add(this.numWeakQualSampleCount);
            this.groupBox6.Controls.Add(this.label2);
            this.groupBox6.Controls.Add(this.label13);
            this.groupBox6.Controls.Add(this.numWeakQualRate);
            this.groupBox6.Controls.Add(this.label14);
            this.groupBox6.Controls.Add(this.label9);
            this.groupBox6.Controls.Add(this.numWeakQualSinr);
            this.groupBox6.Controls.Add(this.label10);
            this.groupBox6.Controls.Add(this.label11);
            this.groupBox6.Controls.Add(this.numWeakQualRsrp);
            this.groupBox6.Controls.Add(this.label12);
            this.groupBox6.Controls.Add(this.chkWeakQuality);
            this.groupBox6.Location = new System.Drawing.Point(21, 227);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(257, 209);
            this.groupBox6.TabIndex = 5;
            this.groupBox6.TabStop = false;
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label17.Location = new System.Drawing.Point(208, 167);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(17, 12);
            this.label17.TabIndex = 17;
            this.label17.Text = "米";
            // 
            // numWeakQualCellDistance
            // 
            this.numWeakQualCellDistance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakQualCellDistance.Location = new System.Drawing.Point(122, 163);
            this.numWeakQualCellDistance.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numWeakQualCellDistance.Name = "numWeakQualCellDistance";
            this.numWeakQualCellDistance.Size = new System.Drawing.Size(80, 21);
            this.numWeakQualCellDistance.TabIndex = 16;
            this.numWeakQualCellDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakQualCellDistance.Value = new decimal(new int[] {
            500,
            0,
            0,
            0});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(47, 167);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 15;
            this.label4.Text = "小区距离≤";
            // 
            // numWeakQualSampleCount
            // 
            this.numWeakQualSampleCount.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakQualSampleCount.Location = new System.Drawing.Point(123, 132);
            this.numWeakQualSampleCount.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numWeakQualSampleCount.Name = "numWeakQualSampleCount";
            this.numWeakQualSampleCount.Size = new System.Drawing.Size(80, 21);
            this.numWeakQualSampleCount.TabIndex = 14;
            this.numWeakQualSampleCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakQualSampleCount.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.Location = new System.Drawing.Point(47, 136);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 13;
            this.label2.Text = "采样点数≥";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label13.Location = new System.Drawing.Point(210, 106);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(11, 12);
            this.label13.TabIndex = 12;
            this.label13.Text = "%";
            // 
            // numWeakQualRate
            // 
            this.numWeakQualRate.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakQualRate.Location = new System.Drawing.Point(122, 100);
            this.numWeakQualRate.Name = "numWeakQualRate";
            this.numWeakQualRate.Size = new System.Drawing.Size(80, 21);
            this.numWeakQualRate.TabIndex = 11;
            this.numWeakQualRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakQualRate.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label14.Location = new System.Drawing.Point(69, 104);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(41, 12);
            this.label14.TabIndex = 10;
            this.label14.Text = "比例≥";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label9.Location = new System.Drawing.Point(211, 75);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(0, 12);
            this.label9.TabIndex = 9;
            // 
            // numWeakQualSinr
            // 
            this.numWeakQualSinr.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakQualSinr.Location = new System.Drawing.Point(123, 68);
            this.numWeakQualSinr.Minimum = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            this.numWeakQualSinr.Name = "numWeakQualSinr";
            this.numWeakQualSinr.Size = new System.Drawing.Size(80, 21);
            this.numWeakQualSinr.TabIndex = 8;
            this.numWeakQualSinr.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakQualSinr.Value = new decimal(new int[] {
            3,
            0,
            0,
            -2147483648});
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label10.Location = new System.Drawing.Point(69, 72);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(41, 12);
            this.label10.TabIndex = 7;
            this.label10.Text = "SINR≤";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label11.Location = new System.Drawing.Point(208, 42);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(23, 12);
            this.label11.TabIndex = 6;
            this.label11.Text = "dBm";
            // 
            // numWeakQualRsrp
            // 
            this.numWeakQualRsrp.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakQualRsrp.Location = new System.Drawing.Point(123, 37);
            this.numWeakQualRsrp.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numWeakQualRsrp.Name = "numWeakQualRsrp";
            this.numWeakQualRsrp.Size = new System.Drawing.Size(80, 21);
            this.numWeakQualRsrp.TabIndex = 5;
            this.numWeakQualRsrp.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakQualRsrp.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label12.Location = new System.Drawing.Point(69, 41);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(41, 12);
            this.label12.TabIndex = 4;
            this.label12.Text = "RSRP≥";
            // 
            // chkWeakQuality
            // 
            this.chkWeakQuality.AutoSize = true;
            this.chkWeakQuality.Location = new System.Drawing.Point(26, 0);
            this.chkWeakQuality.Name = "chkWeakQuality";
            this.chkWeakQuality.Size = new System.Drawing.Size(74, 18);
            this.chkWeakQuality.TabIndex = 3;
            this.chkWeakQuality.Text = "质差检查";
            this.chkWeakQuality.UseVisualStyleBackColor = true;
            // 
            // groupBox7
            // 
            this.groupBox7.Controls.Add(this.label19);
            this.groupBox7.Controls.Add(this.label15);
            this.groupBox7.Controls.Add(this.numOverCoverCellDistance);
            this.groupBox7.Controls.Add(this.numOverCoverRate);
            this.groupBox7.Controls.Add(this.label20);
            this.groupBox7.Controls.Add(this.label16);
            this.groupBox7.Controls.Add(this.numOverCoverSampleCount);
            this.groupBox7.Controls.Add(this.numOverCoverRadiusRatio);
            this.groupBox7.Controls.Add(this.label21);
            this.groupBox7.Controls.Add(this.label18);
            this.groupBox7.Controls.Add(this.chkOverCoverage);
            this.groupBox7.Location = new System.Drawing.Point(300, 259);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(261, 177);
            this.groupBox7.TabIndex = 6;
            this.groupBox7.TabStop = false;
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label19.Location = new System.Drawing.Point(221, 135);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(17, 12);
            this.label19.TabIndex = 22;
            this.label19.Text = "米";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label15.Location = new System.Drawing.Point(223, 76);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(11, 12);
            this.label15.TabIndex = 9;
            this.label15.Text = "%";
            // 
            // numOverCoverCellDistance
            // 
            this.numOverCoverCellDistance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numOverCoverCellDistance.Location = new System.Drawing.Point(135, 131);
            this.numOverCoverCellDistance.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numOverCoverCellDistance.Name = "numOverCoverCellDistance";
            this.numOverCoverCellDistance.Size = new System.Drawing.Size(80, 21);
            this.numOverCoverCellDistance.TabIndex = 21;
            this.numOverCoverCellDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOverCoverCellDistance.Value = new decimal(new int[] {
            500,
            0,
            0,
            0});
            // 
            // numOverCoverRate
            // 
            this.numOverCoverRate.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numOverCoverRate.Location = new System.Drawing.Point(136, 69);
            this.numOverCoverRate.Name = "numOverCoverRate";
            this.numOverCoverRate.Size = new System.Drawing.Size(80, 21);
            this.numOverCoverRate.TabIndex = 8;
            this.numOverCoverRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOverCoverRate.Value = new decimal(new int[] {
            30,
            0,
            0,
            0});
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label20.Location = new System.Drawing.Point(57, 134);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(65, 12);
            this.label20.TabIndex = 20;
            this.label20.Text = "小区距离≤";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label16.Location = new System.Drawing.Point(81, 74);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(41, 12);
            this.label16.TabIndex = 7;
            this.label16.Text = "比例≥";
            // 
            // numOverCoverSampleCount
            // 
            this.numOverCoverSampleCount.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numOverCoverSampleCount.Location = new System.Drawing.Point(136, 100);
            this.numOverCoverSampleCount.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numOverCoverSampleCount.Name = "numOverCoverSampleCount";
            this.numOverCoverSampleCount.Size = new System.Drawing.Size(80, 21);
            this.numOverCoverSampleCount.TabIndex = 19;
            this.numOverCoverSampleCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOverCoverSampleCount.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // numOverCoverRadiusRatio
            // 
            this.numOverCoverRadiusRatio.DecimalPlaces = 1;
            this.numOverCoverRadiusRatio.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numOverCoverRadiusRatio.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numOverCoverRadiusRatio.Location = new System.Drawing.Point(136, 35);
            this.numOverCoverRadiusRatio.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numOverCoverRadiusRatio.Name = "numOverCoverRadiusRatio";
            this.numOverCoverRadiusRatio.Size = new System.Drawing.Size(80, 21);
            this.numOverCoverRadiusRatio.TabIndex = 5;
            this.numOverCoverRadiusRatio.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOverCoverRadiusRatio.Value = new decimal(new int[] {
            16,
            0,
            0,
            65536});
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label21.Location = new System.Drawing.Point(57, 103);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(65, 12);
            this.label21.TabIndex = 18;
            this.label21.Text = "采样点数≥";
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label18.Location = new System.Drawing.Point(39, 40);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(83, 12);
            this.label18.TabIndex = 4;
            this.label18.Text = "理想半径系数=";
            // 
            // chkOverCoverage
            // 
            this.chkOverCoverage.AutoSize = true;
            this.chkOverCoverage.Location = new System.Drawing.Point(26, 0);
            this.chkOverCoverage.Name = "chkOverCoverage";
            this.chkOverCoverage.Size = new System.Drawing.Size(86, 18);
            this.chkOverCoverage.TabIndex = 3;
            this.chkOverCoverage.Text = "过覆盖检查";
            this.chkOverCoverage.UseVisualStyleBackColor = true;
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(488, 450);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 7;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(399, 450);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 8;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            // 
            // btnReset
            // 
            this.btnReset.Location = new System.Drawing.Point(21, 450);
            this.btnReset.Name = "btnReset";
            this.btnReset.Size = new System.Drawing.Size(75, 23);
            this.btnReset.TabIndex = 9;
            this.btnReset.Text = "重置条件";
            this.btnReset.UseVisualStyleBackColor = true;
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label25.Location = new System.Drawing.Point(208, 75);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(17, 12);
            this.label25.TabIndex = 18;
            this.label25.Text = "dB";
            // 
            // LtePlanningCellSettingForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(588, 489);
            this.Controls.Add(this.btnReset);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.groupBox7);
            this.Controls.Add(this.groupBox6);
            this.Controls.Add(this.groupBox5);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "LtePlanningCellSettingForm";
            this.Text = "LTE规划小区类检查条件";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleLessCount)).EndInit();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverCellDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverSampleCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCoverRsrp)).EndInit();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakQualCellDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakQualSampleCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakQualRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakQualSinr)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakQualRsrp)).EndInit();
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverCellDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverSampleCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOverCoverRadiusRatio)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.TextBox txtExcel;
        private System.Windows.Forms.Button btnExcel;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.CheckBox chkNotOpened;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.NumericUpDown numSampleLessCount;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.CheckBox chkSampleLess;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown numWeakCoverRate;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown numWeakCoverRsrp;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.CheckBox chkWeakCoverage;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.NumericUpDown numWeakQualSinr;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.NumericUpDown numWeakQualRsrp;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.CheckBox chkWeakQuality;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.NumericUpDown numWeakQualRate;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.NumericUpDown numOverCoverRate;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.NumericUpDown numOverCoverRadiusRatio;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.CheckBox chkOverCoverage;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnReset;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.NumericUpDown numOverCoverCellDistance;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.NumericUpDown numOverCoverSampleCount;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.NumericUpDown numWeakQualCellDistance;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numWeakQualSampleCount;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.NumericUpDown numWeakCoverCellDistance;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.NumericUpDown numWeakCoverSampleCount;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.Label label25;
    }
}

