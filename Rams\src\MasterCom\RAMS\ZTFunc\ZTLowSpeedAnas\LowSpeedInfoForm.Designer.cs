﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LowSpeedInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.contextMenuStripNoFlow = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemExportShp = new System.Windows.Forms.ToolStripMenuItem();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLongitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLatitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDuration = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDistance = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnHighSpeed = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLowSpeed = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnMeanSpeed = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLACCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnFreq = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRoadDesc = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAreaName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGridName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAgentName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.contextMenuStripNoFlow.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStripNoFlow
            // 
            this.contextMenuStripNoFlow.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExport,
            this.ToolStripMenuItemExportShp});
            this.contextMenuStripNoFlow.Name = "contextMenuStripNoFlow";
            this.contextMenuStripNoFlow.Size = new System.Drawing.Size(127, 48);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(126, 22);
            this.ToolStripMenuItemExport.Text = "导出xls...";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // ToolStripMenuItemExportShp
            // 
            this.ToolStripMenuItemExportShp.Name = "ToolStripMenuItemExportShp";
            this.ToolStripMenuItemExportShp.Size = new System.Drawing.Size(126, 22);
            this.ToolStripMenuItemExportShp.Text = "导出shp...";
            this.ToolStripMenuItemExportShp.Click += new System.EventHandler(this.ToolStripMenuItemExportShp_Click);
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.contextMenuStripNoFlow;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(1010, 383);
            this.gridControl.TabIndex = 1;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            this.gridControl.DoubleClick += new System.EventHandler(this.gridControl_DoubleClick);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnFileName,
            this.gridColumnTime,
            this.gridColumnLongitude,
            this.gridColumnLatitude,
            this.gridColumnDuration,
            this.gridColumnDistance,
            this.gridColumnHighSpeed,
            this.gridColumnLowSpeed,
            this.gridColumnMeanSpeed,
            this.gridColumnLACCI,
            this.gridColumnFreq,
            this.gridColumnRoadDesc,
            this.gridColumnAreaName,
            this.gridColumnGridName,
            this.gridColumnAgentName,
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsSelection.MultiSelect = true;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnFileName
            // 
            this.gridColumnFileName.Caption = "文件名";
            this.gridColumnFileName.FieldName = "FileName";
            this.gridColumnFileName.Name = "gridColumnFileName";
            this.gridColumnFileName.Visible = true;
            this.gridColumnFileName.VisibleIndex = 0;
            this.gridColumnFileName.Width = 151;
            // 
            // gridColumnTime
            // 
            this.gridColumnTime.Caption = "时间";
            this.gridColumnTime.FieldName = "DateTimeString";
            this.gridColumnTime.Name = "gridColumnTime";
            this.gridColumnTime.Visible = true;
            this.gridColumnTime.VisibleIndex = 1;
            this.gridColumnTime.Width = 146;
            // 
            // gridColumnLongitude
            // 
            this.gridColumnLongitude.Caption = "经度";
            this.gridColumnLongitude.FieldName = "Longitude";
            this.gridColumnLongitude.Name = "gridColumnLongitude";
            this.gridColumnLongitude.Visible = true;
            this.gridColumnLongitude.VisibleIndex = 2;
            // 
            // gridColumnLatitude
            // 
            this.gridColumnLatitude.Caption = "纬度";
            this.gridColumnLatitude.FieldName = "Latitude";
            this.gridColumnLatitude.Name = "gridColumnLatitude";
            this.gridColumnLatitude.Visible = true;
            this.gridColumnLatitude.VisibleIndex = 3;
            this.gridColumnLatitude.Width = 66;
            // 
            // gridColumnDuration
            // 
            this.gridColumnDuration.Caption = "时长(秒)";
            this.gridColumnDuration.FieldName = "Duration";
            this.gridColumnDuration.Name = "gridColumnDuration";
            this.gridColumnDuration.Visible = true;
            this.gridColumnDuration.VisibleIndex = 4;
            this.gridColumnDuration.Width = 66;
            // 
            // gridColumnDistance
            // 
            this.gridColumnDistance.Caption = "里程(米)";
            this.gridColumnDistance.FieldName = "Distance";
            this.gridColumnDistance.Name = "gridColumnDistance";
            this.gridColumnDistance.Visible = true;
            this.gridColumnDistance.VisibleIndex = 5;
            this.gridColumnDistance.Width = 73;
            // 
            // gridColumnHighSpeed
            // 
            this.gridColumnHighSpeed.Caption = "最高速率(K)";
            this.gridColumnHighSpeed.FieldName = "HighSpeed";
            this.gridColumnHighSpeed.Name = "gridColumnHighSpeed";
            this.gridColumnHighSpeed.Visible = true;
            this.gridColumnHighSpeed.VisibleIndex = 6;
            this.gridColumnHighSpeed.Width = 98;
            // 
            // gridColumnLowSpeed
            // 
            this.gridColumnLowSpeed.Caption = "最低速率(K)";
            this.gridColumnLowSpeed.FieldName = "LowSpeed";
            this.gridColumnLowSpeed.Name = "gridColumnLowSpeed";
            this.gridColumnLowSpeed.Visible = true;
            this.gridColumnLowSpeed.VisibleIndex = 7;
            this.gridColumnLowSpeed.Width = 99;
            // 
            // gridColumnMeanSpeed
            // 
            this.gridColumnMeanSpeed.Caption = "平均速率(K)";
            this.gridColumnMeanSpeed.FieldName = "MeanSpeed";
            this.gridColumnMeanSpeed.Name = "gridColumnMeanSpeed";
            this.gridColumnMeanSpeed.Visible = true;
            this.gridColumnMeanSpeed.VisibleIndex = 8;
            this.gridColumnMeanSpeed.Width = 104;
            // 
            // gridColumnLACCI
            // 
            this.gridColumnLACCI.Caption = "LAC-CI";
            this.gridColumnLACCI.FieldName = "LACCIs";
            this.gridColumnLACCI.Name = "gridColumnLACCI";
            this.gridColumnLACCI.Visible = true;
            this.gridColumnLACCI.VisibleIndex = 9;
            this.gridColumnLACCI.Width = 177;
            // 
            // gridColumnFreq
            // 
            this.gridColumnFreq.Caption = "频点";
            this.gridColumnFreq.FieldName = "BCCHs";
            this.gridColumnFreq.Name = "gridColumnFreq";
            this.gridColumnFreq.Visible = true;
            this.gridColumnFreq.VisibleIndex = 10;
            this.gridColumnFreq.Width = 115;
            // 
            // gridColumnRoadDesc
            // 
            this.gridColumnRoadDesc.Caption = "道路";
            this.gridColumnRoadDesc.FieldName = "RoadDesc";
            this.gridColumnRoadDesc.Name = "gridColumnRoadDesc";
            this.gridColumnRoadDesc.Visible = true;
            this.gridColumnRoadDesc.VisibleIndex = 11;
            this.gridColumnRoadDesc.Width = 115;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "最大场强";
            this.gridColumn1.FieldName = "HighRxlev";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 15;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "最小场强";
            this.gridColumn2.FieldName = "LowRxlev";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 16;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "平均场强";
            this.gridColumn3.FieldName = "MeanRxlev";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 17;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "最大BLER";
            this.gridColumn4.FieldName = "HighBlerGprs";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 18;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "最小BLER";
            this.gridColumn5.FieldName = "LowBlerGprs";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 19;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "平均BLER";
            this.gridColumn6.FieldName = "MeanBlerGprs";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 20;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "最大MCS";
            this.gridColumn7.FieldName = "HighMCS";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 21;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "最小MCS";
            this.gridColumn8.FieldName = "LowMCS";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 22;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "平均MCS";
            this.gridColumn9.FieldName = "MeanMCS";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 23;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "最大时隙";
            this.gridColumn10.FieldName = "HighTS";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 24;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "最小时隙";
            this.gridColumn11.FieldName = "LowTS";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 25;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "平均时隙";
            this.gridColumn12.FieldName = "MeanTS";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 26;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "最大C/I";
            this.gridColumn13.FieldName = "HighC2I";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 27;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "最小C/I";
            this.gridColumn14.FieldName = "LowC2I";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 28;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "平均C/I";
            this.gridColumn15.FieldName = "MeanC2I";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 29;
            this.gridColumn15.Width = 84;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "最大MeanBEP";
            this.gridColumn16.FieldName = "HighMeanBEP";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 30;
            this.gridColumn16.Width = 109;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "最小MeanBEP";
            this.gridColumn17.FieldName = "LowMeanBEP";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 31;
            this.gridColumn17.Width = 102;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "平均MeanBEP";
            this.gridColumn18.FieldName = "MeanMeanBEP";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 32;
            this.gridColumn18.Width = 108;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "最大CVBEP";
            this.gridColumn19.FieldName = "HighCVBEP";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 33;
            this.gridColumn19.Width = 106;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "最小CVBEP";
            this.gridColumn20.FieldName = "LowCVBEP";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 34;
            this.gridColumn20.Width = 103;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "平均CVBEP";
            this.gridColumn21.FieldName = "MeanCVBEP";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 35;
            this.gridColumn21.Width = 106;
            // 
            // gridColumnAreaName
            // 
            this.gridColumnAreaName.Caption = "片区";
            this.gridColumnAreaName.FieldName = "AreaName";
            this.gridColumnAreaName.Name = "gridColumnAreaName";
            this.gridColumnAreaName.Visible = true;
            this.gridColumnAreaName.VisibleIndex = 12;
            // 
            // gridColumnGridName
            // 
            this.gridColumnGridName.Caption = "网格";
            this.gridColumnGridName.FieldName = "GridName";
            this.gridColumnGridName.Name = "gridColumnGridName";
            this.gridColumnGridName.Visible = true;
            this.gridColumnGridName.VisibleIndex = 13;
            // 
            // gridColumnAgentName
            // 
            this.gridColumnAgentName.Caption = "代维";
            this.gridColumnAgentName.FieldName = "AreaAgentName";
            this.gridColumnAgentName.Name = "gridColumnAgentName";
            this.gridColumnAgentName.Visible = true;
            this.gridColumnAgentName.VisibleIndex = 14;
            // 
            // LowSpeedInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1010, 383);
            this.Controls.Add(this.gridControl);
            this.Name = "LowSpeedInfoForm";
            this.Text = "低速率里程分析";
            this.contextMenuStripNoFlow.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStripNoFlow;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLongitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLatitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDuration;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDistance;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnHighSpeed;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLowSpeed;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnMeanSpeed;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLACCI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFreq;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRoadDesc;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExportShp;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAreaName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGridName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAgentName;


    }
}