﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model;
using System.Drawing;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.CQT
{
    public class CQTPointKPIFineData
    {
        public CQTPointKPIFineData()
        { }
        public static MainModel mainModel { get; set; }
        public static QueryCondition qCondition { get; set; }
        protected Dictionary<int, List<FileInfo>> carreerFiles = new Dictionary<int, List<FileInfo>>();
        protected void addFile(DTDataHeader dataHeader)
        {
            if (carreerFiles.ContainsKey(dataHeader.CarrierType))
            {
                List<FileInfo> files = carreerFiles[dataHeader.CarrierType];
                for (int i = 0; i < files.Count; i++)
                {
                    if (dataHeader.ID == files[i].ID)
                    {
                        return;
                    }
                }
                files.Add(dataHeader);
            }
            else
            {
                List<FileInfo> files = new List<FileInfo>();
                files.Add(dataHeader);
                carreerFiles.Add(dataHeader.CarrierType, files);
            }
        }
        public List<FileInfo> GetFileInfo(int carreerID)
        {
            if (carreerFiles.ContainsKey(carreerID))
            {
                return carreerFiles[carreerID];
            }
            return new List<FileInfo>();
        }

        public CQTKPIFinePoint CQTPoint { get; set; }
        public string Name { get; set; }
        /// <summary>
        /// 按运营商索引的KPI数据字典
        /// </summary>
        public Dictionary<int, DataUnitAreaKPIQuery> CarreerFineDataDic { get; set; } = new Dictionary<int, DataUnitAreaKPIQuery>();
        /// <summary>
        /// 当前报表各指标列计算结果
        /// </summary>
        public Dictionary<CQTKPIReportColumn, CQTKPIFineStatResult> ColumnKPIFineResultDic { get; set; } = new Dictionary<CQTKPIReportColumn, CQTKPIFineStatResult>();
        /// <summary>
        /// 当前报表汇各总列结果字典
        /// </summary>
        public Dictionary<CQTKPISummaryColumn, CQTKPIFineStatResult> SummaryResultDic { get; set; } = new Dictionary<CQTKPISummaryColumn, CQTKPIFineStatResult>();
        protected object[] makeRow()
        {
            int idx = 0;
            object[] colValueArr = new object[13 + ColumnKPIFineResultDic.Count + SummaryResultDic.Count];//名字列+指标列+汇总列
            colValueArr[idx++] = Name;
            if (DIYQueryCQTKPIFine.isStatByCell)
            {
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
            }
            foreach (CQTKPIFineStatResult result in ColumnKPIFineResultDic.Values)
            {
                if (double.IsNaN(result.KPIValue) || result.KPIValue == -999999 || result.KPIValue == 999999)
                {
                    colValueArr[idx] = "-";
                }
                else
                {
                    colValueArr[idx] = result.KPIValue;
                }
                idx++;
            }
            foreach (CQTKPIFineStatResult sResult in SummaryResultDic.Values )
            {
                if (double.IsNaN(sResult.Score))
                {
                    colValueArr[idx] = "-";
                }
                else
                {
                    colValueArr[idx] = sResult.Score;
                }
                idx++;
            }
            return colValueArr;
        }
        protected object[] makeMainRow()
        {
            int idx = 0;
            object[] colValueArr = new object[13 + ColumnKPIFineResultDic.Count + SummaryResultDic.Count];//名字列+指标列+汇总列
            colValueArr[idx++] = Name;
            if (DIYQueryCQTKPIFine.isStatByCell)
            {
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
                colValueArr[idx++] = CQTPoint.Longitude;
                colValueArr[idx++] = CQTPoint.Latitude;
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
                colValueArr[idx++] = "-";
            }
            foreach (CQTKPIFineStatResult result in ColumnKPIFineResultDic.Values)
            {
                if (double.IsNaN(result.KPIValue) || result.KPIValue == -999999 || result.KPIValue == 999999)
                {
                    colValueArr[idx] = "-";
                }
                else
                {
                    colValueArr[idx] = result.KPIValue;
                }
                idx++;
            }
            foreach (CQTKPIFineStatResult sResult in SummaryResultDic.Values)
            {
                if (double.IsNaN(sResult.Score))
                {
                    colValueArr[idx] = "-";
                }
                else
                {
                    colValueArr[idx] = sResult.Score;
                }
                idx++;
            }
            return colValueArr;
        }

        protected void setValidResult(out DataUnitAreaKPIQuery statData, CQTKPIReportColumn rptCol, out CQTKPIFineStatResult result)
        {
            result = null;
            if (CarreerFineDataDic.TryGetValue(rptCol.CarreerID, out statData))
            {
                string value = statData.CalcValueByFormula(rptCol.Formula);
                double dValue = 0;
                if (double.TryParse(value, out dValue))
                {
                    result = getResultInfo(rptCol, dValue);
                }
                else//非数字数字，无法按范围打分
                {
                    result = new CQTKPIFineStatResult();
                    result.Color = Color.Empty;
                    result.KPIValue = double.NaN;
                    result.Score = double.NaN;
                }
            }
            else//无数据
            {
                result = new CQTKPIFineStatResult();
                result.Color = Color.Empty;
                result.KPIValue = double.NaN;
                result.Score = double.NaN;
            }
        }

        protected CQTKPIFineStatResult getResultInfo(CQTKPIReportColumn rptCol, double dValue)
        {
            CQTKPIFineStatResult result;
            double score;
            CQTKPIScoreColorRange range = rptCol.ScoreScheme.GetRangeByKPIValue(dValue, out score);
            if (range != null)//在打分规则内
            {
                result = new CQTKPIFineStatResult();
                result.Color = range.Color;
                result.KPIValue = dValue;
                result.Score = score;
            }
            else
            {
                result = new CQTKPIFineStatResult();
                result.Color = Color.Empty;
                result.KPIValue = dValue;
                result.Score = double.NaN;
            }

            return result;
        }

        protected void addSummaryResultDic(CQTKPIReport report, bool isRound)
        {
            foreach (CQTKPISummaryColumn smCol in report.SummaryColumns)
            {
                CQTKPIFineStatResult result = new CQTKPIFineStatResult();
                result.KPIValue = double.NaN;
                foreach (SummaryItem item in smCol.SummaryColumns)
                {
                    if (ColumnKPIFineResultDic.ContainsKey(item.Column))
                    {
                        double colScore = ColumnKPIFineResultDic[item.Column].Score;
                        if (!double.IsNaN(colScore))
                        {
                            result.Score += colScore * item.Weight;
                        }
                    }
                }
                if (isRound)
                {
                    result.Score = Math.Round(result.Score, 2);
                }
                result.Color = smCol.GetColorByScore(result.Score);
                SummaryResultDic.Add(smCol, result);
            }
        }
    }

    public class CQTMainPointKPIFine : CQTPointKPIFineData
    {
        public CQTMainPointKPIFine(CQTKPIFinePoint point)
        {
            CQTPoint = point;
            Name = point.Name;
            CarreerFineDataDic = new Dictionary<int, DataUnitAreaKPIQuery>();
        }
        private readonly Dictionary<string, CQTSubPointKPIFine> subPointNameDataDic = new Dictionary<string, CQTSubPointKPIFine>();
        public Dictionary<string, CQTSubPointKPIFine> SubPointNameDataDic
        {
            get { return subPointNameDataDic; }
        }

        public void AddKPIStatData(DTDataHeader dataHeader, string subPointName, string floorName, PartialData partialStatData)
        {
            addFile(dataHeader);
            int carreerID = dataHeader.CarrierType;
            DataUnitAreaKPIQuery kpiData = null;
            if (!CarreerFineDataDic.TryGetValue(carreerID, out kpiData))
            {
                kpiData = new DataUnitAreaKPIQuery();
                CarreerFineDataDic.Add(carreerID, kpiData);
            }
            kpiData.AddStatData(partialStatData);

            //子地点统计
            CQTSubPointKPIFine subPnt = null;
            if (!subPointNameDataDic.TryGetValue(subPointName, out subPnt))
            {
                subPnt = new CQTSubPointKPIFine(CQTPoint, subPointName);
                subPointNameDataDic.Add(subPointName, subPnt);
            }
            string testTime = JavaDate.GetDateTimeFromMilliseconds(dataHeader.BeginTime * 1000L).ToString("yyyyMMdd");
            subPnt.AddKPIStatData(testTime, dataHeader, partialStatData);
        }

        public void MakeReportData(DevExpress.XtraTreeList.TreeList treeList, CQTKPIReport report)
        {
            ColumnKPIFineResultDic.Clear();
            DataUnitAreaKPIQuery statData = null;
            foreach (CQTKPIReportColumn rptCol in report.Columns)
            {
                CQTKPIFineStatResult result;
                setValidResult(out statData, rptCol, out result);
                ColumnKPIFineResultDic.Add(rptCol, result);
            }
            SummaryResultDic.Clear();
            addSummaryResultDic(report, true);

            object[] row = makeMainRow();
            DevExpress.XtraTreeList.Nodes.TreeListNode mainNode = treeList.AppendNode(row, null);
            mainNode.Tag = this;
            foreach (CQTSubPointKPIFine subPntData in subPointNameDataDic.Values)//子地点统计
            {
                subPntData.MakeReportData(treeList, mainNode, report);
            }
        }

        public void Stat(CQTKPIReport report)
        {
            ColumnKPIFineResultDic.Clear();
            DataUnitAreaKPIQuery statData = null;
            foreach (CQTKPIReportColumn rptCol in report.Columns)
            {
                CQTKPIFineStatResult result;
                setValidResult(out statData, rptCol, out result);
                ColumnKPIFineResultDic.Add(rptCol, result);
            }
            SummaryResultDic.Clear();
            addSummaryResultDic(report, false);

            foreach (CQTSubPointKPIFine subPntData in subPointNameDataDic.Values)//子地点统计
            {
                subPntData.Stat(report);
            }
        }
    }
    
    public class CQTSubPointKPIFine : CQTPointKPIFineData
    {
        public CQTSubPointKPIFine(CQTKPIFinePoint point, string name)
        {
            CQTPoint = point;
            Name = name;
            CarreerFineDataDic = new Dictionary<int, DataUnitAreaKPIQuery>();
        }
        public class ComparerCQTSubPoint : IComparer<CQTSubPointKPIFine>
        {
            public int Compare(CQTSubPointKPIFine x, CQTSubPointKPIFine y)
            {
                return y.Name.CompareTo(x.Name);
            }
        }
        public Dictionary<int, List<CQTFileKPIFineData>> CarreerFileDataDic { get; set; } = new Dictionary<int, List<CQTFileKPIFineData>>();
        public List<CQTStatCellKPIInfo> CarreerCellDataList { get; set; } = new List<CQTStatCellKPIInfo>();
        private readonly Dictionary<string, CQTSubPointKPIFine> subPointNameDataDic = new Dictionary<string, CQTSubPointKPIFine>();
        public Dictionary<string, CQTSubPointKPIFine> SubPointNameDataDic
        {
            get { return subPointNameDataDic; }
        }
        public bool HasChild
        {
            get { return subPointNameDataDic.Count > 0; }
        }
        public void AddKPIStatData(string floorName, DTDataHeader dataHeader, PartialData partialStatData)
        {
            addFile(dataHeader);
            int carreerID = dataHeader.CarrierType;

            DataUnitAreaKPIQuery kpiData = null;
            if (!CarreerFineDataDic.TryGetValue(carreerID, out kpiData))
            {
                kpiData = new DataUnitAreaKPIQuery();
                CarreerFineDataDic.Add(carreerID, kpiData);
            }
            kpiData.AddStatData(partialStatData);

            if (string.IsNullOrEmpty(floorName))
            {
                //按文件保存数据
                List<CQTFileKPIFineData> fileDataList;//文件集
                if (!CarreerFileDataDic.TryGetValue(carreerID, out fileDataList))
                {
                    fileDataList = new List<CQTFileKPIFineData>();
                    CarreerFileDataDic.Add(carreerID, fileDataList);
                }

                bool existFileData = false;
                foreach (CQTFileKPIFineData item in fileDataList)
                {
                    if (item.DataHeader.ID == dataHeader.ID)
                    {
                        item.AddKPIStatData(partialStatData);
                        existFileData = true;
                        break;
                    }
                }
                if (!existFileData)
                {
                    CQTFileKPIFineData fileData = new CQTFileKPIFineData(dataHeader);
                    fileData.AddKPIStatData(partialStatData);
                    fileDataList.Add(fileData);
                }
            }
            else
            {
                //子地点统计
                CQTSubPointKPIFine subPnt = null;
                if (!subPointNameDataDic.TryGetValue(floorName, out subPnt))
                {
                    subPnt = new CQTSubPointKPIFine(this.CQTPoint, floorName);
                    subPointNameDataDic.Add(floorName, subPnt);
                }
                subPnt.AddKPIStatData(null, dataHeader, partialStatData);
            }
            
        }

        public void MakeReportData(DevExpress.XtraTreeList.TreeList treeList, DevExpress.XtraTreeList.Nodes.TreeListNode parentNode, CQTKPIReport report)
        {
            ColumnKPIFineResultDic.Clear();
            DataUnitAreaKPIQuery statData = null;
            foreach (CQTKPIReportColumn rptCol in report.Columns)
            {
                CQTKPIFineStatResult result;
                setValidResult(out statData, rptCol, out result);
                ColumnKPIFineResultDic.Add(rptCol, result);
            }

            SummaryResultDic.Clear();
            addSummaryResultDic(report, true);

            object[] row = makeRow();
            DevExpress.XtraTreeList.Nodes.TreeListNode node = treeList.AppendNode(row, parentNode);
            node.Tag = this;
            if (HasChild)
            {
                foreach (CQTSubPointKPIFine subPntData in subPointNameDataDic.Values)//子地点统计
                {
                    subPntData.MakeReportData(treeList, node, report);
                }
            }
            else
            {
                List<CQTFileKPIFineData> fileDataList = null;
                if (CarreerFileDataDic.ContainsKey(report.CarreerID))
                {
                    fileDataList = CarreerFileDataDic[report.CarreerID];
                    if (DIYQueryCQTKPIFine.isStatByCell)
                    {
                        dealCarreerCellDataList(treeList, report, node, fileDataList);
                    }
                    else
                    {
                        foreach (CQTFileKPIFineData fileData in fileDataList)//各文件统计
                        {
                            fileData.MakeReportData(treeList, node, report);
                        }
                    }
                }
            }
            
        }

        private void dealCarreerCellDataList(DevExpress.XtraTreeList.TreeList treeList, CQTKPIReport report, DevExpress.XtraTreeList.Nodes.TreeListNode node, List<CQTFileKPIFineData> fileDataList)
        {
            CarreerCellDataList.Clear();
            string[] fileNames = fileDataList[0].DataHeader.Name.Split('_');
            string testTime = JavaDate.GetDateTimeFromMilliseconds(fileDataList[0].DataHeader.BeginTime * 1000L).ToString("yyyyMMdd");
            DIYCQTCellStatByFile diyCQTCellStatByFile = DIYQueryCQTKPIFine.DiyCQTCellStatByFile;
            CQTWYKey cqtWY = new CQTWYKey();
            cqtWY.WYName = fileNames[2];
            cqtWY.ChildName = fileNames[3];
            if (diyCQTCellStatByFile.cqtStatCellKPIInfoDic.ContainsKey(cqtWY))
            {
                foreach (CQTStatCellKPIInfo cqtStatCellKPIInfo in diyCQTCellStatByFile.cqtStatCellKPIInfoDic[cqtWY])//各小区统计
                {
                    if (cqtStatCellKPIInfo.StateTime != testTime)
                        continue;
                    CQTFileKPIFineData cellData = new CQTFileKPIFineData(null);
                    if (cqtStatCellKPIInfo.CellLng != 0 || cqtStatCellKPIInfo.CellLat != 0)
                    {
                        double distance = GetDistance(CQTPoint.Longitude, CQTPoint.Latitude, cqtStatCellKPIInfo.CellLng, cqtStatCellKPIInfo.CellLat);
                        cqtStatCellKPIInfo.Distance = String.Format("{0:F}", distance);
                    }
                    cellData.MakeReportCellData(treeList, node, report, cqtStatCellKPIInfo);
                    cqtStatCellKPIInfo.ColumnKPIFineResultDic = cellData.ColumnKPIFineResultDic;
                    CarreerCellDataList.Add(cqtStatCellKPIInfo);
                }
            }
        }

        public void Stat(CQTKPIReport report)
        {
            ColumnKPIFineResultDic.Clear();
            DataUnitAreaKPIQuery statData = null;
            foreach (CQTKPIReportColumn rptCol in report.Columns)
            {
                CQTKPIFineStatResult result;
                setValidResult(out statData, rptCol, out result);
                ColumnKPIFineResultDic.Add(rptCol, result);
            }
            SummaryResultDic.Clear();
            addSummaryResultDic(report, false);

            List<CQTFileKPIFineData> fileDataList = null;
            if (CarreerFileDataDic.ContainsKey(report.CarreerID))
            {
                fileDataList = CarreerFileDataDic[report.CarreerID];
                foreach (CQTFileKPIFineData fileData in fileDataList)//各文件统计
                {
                    fileData.Stat(report);
                }
            }
        }
        private double GetDistance(double x1, double y1, double x2, double y2)
        {
            double longitudeDistance = (Math.Sin((90 - y1) * 2 * Math.PI / 360) + Math.Sin((90 - y2) * 2 * Math.PI / 360)) / 2 * (x1 - x2) / 360 * 40075360;
            double latitudeDistance = (y1 - y2) / 360 * 39940670;
            return Math.Sqrt(longitudeDistance * longitudeDistance + latitudeDistance * latitudeDistance);
        }
    }

    public class CQTFileKPIFineData
    {
        public Dictionary<CQTKPIReportColumn, CQTKPIFineStatResult> ColumnKPIFineResultDic { get; set; } = new Dictionary<CQTKPIReportColumn, CQTKPIFineStatResult>();
        public Dictionary<CQTKPISummaryColumn, CQTKPIFineStatResult> SummaryResultDic { get; set; } = new Dictionary<CQTKPISummaryColumn, CQTKPIFineStatResult>();
        public DTDataHeader DataHeader { get; set; }
        public DataUnitAreaKPIQuery KPIData { get; set; } = new DataUnitAreaKPIQuery();
        public CQTFileKPIFineData(DTDataHeader dataHeader)
        {
            DataHeader = dataHeader;
        }
        public void AddKPIStatData(PartialData partialStatData)
        {
            KPIData.AddStatData(partialStatData);
        }

        /// <summary>
        /// 按文件统计
        /// </summary>
        public void MakeReportData(DevExpress.XtraTreeList.TreeList treeList, DevExpress.XtraTreeList.Nodes.TreeListNode parentNode, CQTKPIReport report)
        {
            ColumnKPIFineResultDic.Clear();
            foreach (CQTKPIReportColumn rptCol in report.Columns)
            {
                CQTKPIFineStatResult result = setValidResult(rptCol);
                ColumnKPIFineResultDic.Add(rptCol, result);
            }

            SummaryResultDic.Clear();
            addSummaryResultDic(report, true);

            object[] row = makeRow();
            DevExpress.XtraTreeList.Nodes.TreeListNode node = treeList.AppendNode(row, parentNode);
            node.Tag = this;
        }

        private CQTKPIFineStatResult setValidResult(CQTKPIReportColumn rptCol)
        {
            CQTKPIFineStatResult result = null;
            string value = KPIData.CalcValueByFormula(rptCol.Formula);
            double dValue = 0;
            if (double.TryParse(value, out dValue))
            {
                result = getResultInfo(rptCol, dValue);
            }
            else
            {
                result = new CQTKPIFineStatResult();
                result.FileInfo = DataHeader;
                result.Color = Color.Empty;
                result.KPIValue = double.NaN;
                result.Score = double.NaN;
            }

            return result;
        }

        private CQTKPIFineStatResult getResultInfo(CQTKPIReportColumn rptCol, double dValue)
        {
            CQTKPIFineStatResult result;
            double score;
            CQTKPIScoreColorRange range = rptCol.ScoreScheme.GetRangeByKPIValue(dValue, out score);
            if (range != null)
            {
                result = new CQTKPIFineStatResult();
                result.FileInfo = DataHeader;
                result.Color = range.Color;
                result.KPIValue = dValue;
                result.Score = score;
            }
            else
            {
                result = new CQTKPIFineStatResult();
                result.FileInfo = DataHeader;
                result.Color = Color.Empty;
                result.KPIValue = dValue;
                result.Score = double.NaN;
            }

            return result;
        }

        private void addSummaryResultDic(CQTKPIReport report, bool isRound)
        {
            foreach (CQTKPISummaryColumn smCol in report.SummaryColumns)
            {
                CQTKPIFineStatResult result = new CQTKPIFineStatResult();
                result.KPIValue = double.NaN;
                foreach (SummaryItem item in smCol.SummaryColumns)
                {
                    if (ColumnKPIFineResultDic.ContainsKey(item.Column))
                    {
                        double colScore = ColumnKPIFineResultDic[item.Column].Score;
                        if (!double.IsNaN(colScore))
                        {
                            result.Score += colScore * item.Weight;
                        }
                    }
                }
                result.Color = smCol.GetColorByScore(result.Score);
                if (isRound)
                {
                    result.Score = Math.Round(result.Score, 2);
                }
                SummaryResultDic.Add(smCol, result);
            }
        }

        /// <summary>
        ///按小区统计
        /// </summary>
        public void MakeReportCellData(DevExpress.XtraTreeList.TreeList treeList, DevExpress.XtraTreeList.Nodes.TreeListNode parentNode, CQTKPIReport report
            , CQTStatCellKPIInfo cqtStatCellKPIInfo)
        {
            ColumnKPIFineResultDic.Clear();
            foreach (CQTKPIReportColumn rptCol in report.Columns)
            {
                CQTKPIFineStatResult result = setValidCellResult(cqtStatCellKPIInfo, rptCol);
                ColumnKPIFineResultDic.Add(rptCol, result);
            }

            SummaryResultDic.Clear();
            addSummaryResultDic(report, true);

            object[] row = makeCellRow(cqtStatCellKPIInfo);
            DevExpress.XtraTreeList.Nodes.TreeListNode node = treeList.AppendNode(row, parentNode);
            node.Tag = this;
        }

        private CQTKPIFineStatResult setValidCellResult(CQTStatCellKPIInfo cqtStatCellKPIInfo, CQTKPIReportColumn rptCol)
        {
            CQTKPIFineStatResult result = null;
            string value = cqtStatCellKPIInfo.DataUnitAreaKPIQuery.CalcValueByFormula(rptCol.Formula);
            double dValue = 0;
            if (double.TryParse(value, out dValue))
            {
                result = getCellResultInfo(cqtStatCellKPIInfo, rptCol, dValue);
            }
            else
            {
                result = new CQTKPIFineStatResult();
                result.CellName = cqtStatCellKPIInfo.StrCellName;
                result.LAC = cqtStatCellKPIInfo.StrLac;
                result.CI = cqtStatCellKPIInfo.StrCi;
                result.CoverType = cqtStatCellKPIInfo.CellCoverType;
                result.Address = cqtStatCellKPIInfo.CellAddress;
                result.Color = Color.Empty;
                result.KPIValue = double.NaN;
                result.Score = double.NaN;
            }

            return result;
        }

        private CQTKPIFineStatResult getCellResultInfo(CQTStatCellKPIInfo cqtStatCellKPIInfo, CQTKPIReportColumn rptCol, double dValue)
        {
            CQTKPIFineStatResult result;
            double score;
            CQTKPIScoreColorRange range = rptCol.ScoreScheme.GetRangeByKPIValue(dValue, out score);
            if (range != null)
            {
                result = new CQTKPIFineStatResult();
                result.CellName = cqtStatCellKPIInfo.StrCellName;
                result.LAC = cqtStatCellKPIInfo.StrLac;
                result.CI = cqtStatCellKPIInfo.StrCi;
                result.CoverType = cqtStatCellKPIInfo.CellCoverType;
                result.Address = cqtStatCellKPIInfo.CellAddress;
                result.Color = range.Color;
                result.KPIValue = dValue;
                result.Score = score;
            }
            else
            {
                result = new CQTKPIFineStatResult();
                result.CellName = cqtStatCellKPIInfo.StrCellName;
                result.LAC = cqtStatCellKPIInfo.StrLac;
                result.CI = cqtStatCellKPIInfo.StrCi;
                result.CoverType = cqtStatCellKPIInfo.CellCoverType;
                result.Address = cqtStatCellKPIInfo.CellAddress;
                result.Color = Color.Empty;
                result.KPIValue = dValue;
                result.Score = double.NaN;
            }

            return result;
        }

        public void Stat(CQTKPIReport report)
        {
            ColumnKPIFineResultDic.Clear();
            foreach (CQTKPIReportColumn rptCol in report.Columns)
            {
                CQTKPIFineStatResult result = setValidResult(rptCol);
                ColumnKPIFineResultDic.Add(rptCol, result);
            }

            addSummaryResultDic(report, false);
        }

        protected object[] makeRow()
        {
            object[] colValueArr = new object[1 + ColumnKPIFineResultDic.Count+SummaryResultDic.Count];//名字列+指标列+汇总列
            colValueArr[0] = DataHeader.Name;
            int idx = 1;
            foreach (CQTKPIFineStatResult result in ColumnKPIFineResultDic.Values)
            {
                if (double.IsNaN(result.KPIValue) || result.KPIValue == -999999 || result.KPIValue == 999999)
                {
                    colValueArr[idx] = "-";
                }
                else
                {
                    colValueArr[idx] = result.KPIValue;
                }
                idx++;
            }
            foreach (CQTKPIFineStatResult sR in SummaryResultDic.Values)
            {
                colValueArr[idx] = sR.Score;
                idx++;
            }
            return colValueArr;
        }
        protected object[] makeCellRow(CQTStatCellKPIInfo cqtStatCellKPIInfo)
        {
            int idx = 0;
            object[] colValueArr = new object[13 + ColumnKPIFineResultDic.Count + SummaryResultDic.Count];//名字列+指标列+汇总列
            colValueArr[idx++] = cqtStatCellKPIInfo.StrCellName;
            colValueArr[idx++] = cqtStatCellKPIInfo.StrLac;
            colValueArr[idx++] = cqtStatCellKPIInfo.StrCi;
            colValueArr[idx++] = cqtStatCellKPIInfo.Str频点;
            colValueArr[idx++] = cqtStatCellKPIInfo.Str扰码;
            colValueArr[idx++] = cqtStatCellKPIInfo.StrCGI;
            colValueArr[idx++] = cqtStatCellKPIInfo.CellLng;
            colValueArr[idx++]= cqtStatCellKPIInfo.CellLat;
            colValueArr[idx++] = cqtStatCellKPIInfo.Distance;
            colValueArr[idx++] = cqtStatCellKPIInfo.NetType;
            colValueArr[idx++] = cqtStatCellKPIInfo.CellCoverType;
            colValueArr[idx++] = cqtStatCellKPIInfo.CellAddress;
            colValueArr[idx++] = cqtStatCellKPIInfo.CellFileNames;
            foreach (CQTKPIFineStatResult result in ColumnKPIFineResultDic.Values)
            {
                if (double.IsNaN(result.KPIValue) || result.KPIValue == -999999 || result.KPIValue == 999999)
                {
                    colValueArr[idx] = "-";
                }
                else
                {
                    colValueArr[idx] = result.KPIValue;
                }
                idx++;
            }
            foreach (CQTKPIFineStatResult sR in SummaryResultDic.Values)
            {
                if (double.IsNaN(sR.Score))
                {
                    colValueArr[idx] = "-";
                }
                else
                {
                    colValueArr[idx] = sR.Score;
                }
                idx++;
            }
            return colValueArr;
        }
    }

    public class CQTKPIFineStatResult
    {
        public FileInfo FileInfo { get; set; }
        public double KPIValue { get; set; }
        public Color Color { get; set; }
        public double Score { get; set; }
        public string CellName { get; set; }
        public string LAC { get; set; }
        public string CI { get; set; }
        public string CoverType { get; set; }
        public string Address { get; set; }
    }

}
