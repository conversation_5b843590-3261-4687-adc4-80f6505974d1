﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TDLTECoSiteAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.treeListView = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRegionName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMissedCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCoEarfcnPciCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSiteName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCelleNodeBID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellCellID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellFreqBand = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellEARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellLong = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.treeListViewDetail = new BrightIdeasSoftware.TreeListView();
            this.olvColumnNBCellSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBSiteName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCellTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCellECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCelleNodeBID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCellCellID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCellFreqBand = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCellEARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCellPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCellLong = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCellLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBExistCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBExistSiteName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBExistCellTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBExistCellECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBExistCelleNodeBID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBExistCellCellID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBExistCellFreqBand = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBExistCellEARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBExistCellPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBExistCellLong = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBExistCellLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAngle = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewDetail)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeListView
            // 
            this.treeListView.AllColumns.Add(this.olvColumnSN);
            this.treeListView.AllColumns.Add(this.olvColumnRegionName);
            this.treeListView.AllColumns.Add(this.olvColumnCellName);
            this.treeListView.AllColumns.Add(this.olvColumnMissedCount);
            this.treeListView.AllColumns.Add(this.olvColumnCoEarfcnPciCount);
            this.treeListView.AllColumns.Add(this.olvColumnSiteName);
            this.treeListView.AllColumns.Add(this.olvColumnCellTAC);
            this.treeListView.AllColumns.Add(this.olvColumnCellECI);
            this.treeListView.AllColumns.Add(this.olvColumnCelleNodeBID);
            this.treeListView.AllColumns.Add(this.olvColumnCellCellID);
            this.treeListView.AllColumns.Add(this.olvColumnCellFreqBand);
            this.treeListView.AllColumns.Add(this.olvColumnCellEARFCN);
            this.treeListView.AllColumns.Add(this.olvColumnCellPCI);
            this.treeListView.AllColumns.Add(this.olvColumnCellLong);
            this.treeListView.AllColumns.Add(this.olvColumnCellLat);
            this.treeListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnRegionName,
            this.olvColumnCellName,
            this.olvColumnMissedCount,
            this.olvColumnCoEarfcnPciCount,
            this.olvColumnSiteName,
            this.olvColumnCellTAC,
            this.olvColumnCellECI,
            this.olvColumnCelleNodeBID,
            this.olvColumnCellCellID,
            this.olvColumnCellFreqBand,
            this.olvColumnCellEARFCN,
            this.olvColumnCellPCI,
            this.olvColumnCellLong,
            this.olvColumnCellLat});
            this.treeListView.ContextMenuStrip = this.contextMenuStrip;
            this.treeListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListView.FullRowSelect = true;
            this.treeListView.GridLines = true;
            this.treeListView.Location = new System.Drawing.Point(0, 0);
            this.treeListView.MultiSelect = false;
            this.treeListView.Name = "treeListView";
            this.treeListView.OwnerDraw = true;
            this.treeListView.ShowGroups = false;
            this.treeListView.Size = new System.Drawing.Size(1061, 245);
            this.treeListView.TabIndex = 1;
            this.treeListView.UseCompatibleStateImageBehavior = false;
            this.treeListView.View = System.Windows.Forms.View.Details;
            this.treeListView.VirtualMode = true;
            this.treeListView.MouseClick += new System.Windows.Forms.MouseEventHandler(this.treeListView_MouseClick);
            this.treeListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeListView_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 130;
            // 
            // olvColumnRegionName
            // 
            this.olvColumnRegionName.HeaderFont = null;
            this.olvColumnRegionName.Text = "区域名称";
            this.olvColumnRegionName.Width = 80;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 100;
            // 
            // olvColumnMissedCount
            // 
            this.olvColumnMissedCount.HeaderFont = null;
            this.olvColumnMissedCount.Text = "建议邻区数量";
            this.olvColumnMissedCount.Width = 90;
            // 
            // olvColumnCoEarfcnPciCount
            // 
            this.olvColumnCoEarfcnPciCount.HeaderFont = null;
            this.olvColumnCoEarfcnPciCount.Text = "同频同PCI数量";
            this.olvColumnCoEarfcnPciCount.Width = 90;
            // 
            // olvColumnSiteName
            // 
            this.olvColumnSiteName.HeaderFont = null;
            this.olvColumnSiteName.Text = "基站名称";
            this.olvColumnSiteName.Width = 100;
            // 
            // olvColumnCellTAC
            // 
            this.olvColumnCellTAC.HeaderFont = null;
            this.olvColumnCellTAC.Text = "TAC";
            // 
            // olvColumnCellECI
            // 
            this.olvColumnCellECI.HeaderFont = null;
            this.olvColumnCellECI.Text = "ECI";
            // 
            // olvColumnCelleNodeBID
            // 
            this.olvColumnCelleNodeBID.HeaderFont = null;
            this.olvColumnCelleNodeBID.Text = "eNodeBID";
            // 
            // olvColumnCellCellID
            // 
            this.olvColumnCellCellID.HeaderFont = null;
            this.olvColumnCellCellID.Text = "CellID";
            // 
            // olvColumnCellFreqBand
            // 
            this.olvColumnCellFreqBand.HeaderFont = null;
            this.olvColumnCellFreqBand.Text = "频点属性";
            // 
            // olvColumnCellEARFCN
            // 
            this.olvColumnCellEARFCN.HeaderFont = null;
            this.olvColumnCellEARFCN.Text = "EARFCN";
            // 
            // olvColumnCellPCI
            // 
            this.olvColumnCellPCI.HeaderFont = null;
            this.olvColumnCellPCI.Text = "PCI";
            // 
            // olvColumnCellLong
            // 
            this.olvColumnCellLong.HeaderFont = null;
            this.olvColumnCellLong.Text = "经度";
            this.olvColumnCellLong.Width = 100;
            // 
            // olvColumnCellLat
            // 
            this.olvColumnCellLat.HeaderFont = null;
            this.olvColumnCellLat.Text = "纬度";
            this.olvColumnCellLat.Width = 100;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(153, 92);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(152, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(152, 22);
            this.miCollapseAll.Text = "全部折叠";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(152, 22);
            this.miExportToExcel.Text = "导出Excel";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // treeListViewDetail
            // 
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBCellSN);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBCellName);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBSiteName);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBCellTAC);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBCellECI);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBCelleNodeBID);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBCellCellID);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBCellFreqBand);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBCellEARFCN);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBCellPCI);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBCellLong);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBCellLat);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBExistCellName);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBExistSiteName);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBExistCellTAC);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBExistCellECI);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBExistCelleNodeBID);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBExistCellCellID);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBExistCellFreqBand);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBExistCellEARFCN);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBExistCellPCI);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBExistCellLong);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnNBExistCellLat);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnDistance);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnAngle);
            this.treeListViewDetail.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnNBCellSN,
            this.olvColumnNBCellName,
            this.olvColumnNBSiteName,
            this.olvColumnNBCellTAC,
            this.olvColumnNBCellECI,
            this.olvColumnNBCelleNodeBID,
            this.olvColumnNBCellCellID,
            this.olvColumnNBCellFreqBand,
            this.olvColumnNBCellEARFCN,
            this.olvColumnNBCellPCI,
            this.olvColumnNBCellLong,
            this.olvColumnNBCellLat,
            this.olvColumnNBExistCellName,
            this.olvColumnNBExistSiteName,
            this.olvColumnNBExistCellTAC,
            this.olvColumnNBExistCellECI,
            this.olvColumnNBExistCelleNodeBID,
            this.olvColumnNBExistCellCellID,
            this.olvColumnNBExistCellFreqBand,
            this.olvColumnNBExistCellEARFCN,
            this.olvColumnNBExistCellPCI,
            this.olvColumnNBExistCellLong,
            this.olvColumnNBExistCellLat,
            this.olvColumnDistance,
            this.olvColumnAngle});
            this.treeListViewDetail.ContextMenuStrip = this.contextMenuStrip;
            this.treeListViewDetail.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListViewDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListViewDetail.FullRowSelect = true;
            this.treeListViewDetail.GridLines = true;
            this.treeListViewDetail.Location = new System.Drawing.Point(0, 0);
            this.treeListViewDetail.MultiSelect = false;
            this.treeListViewDetail.Name = "treeListViewDetail";
            this.treeListViewDetail.OwnerDraw = true;
            this.treeListViewDetail.ShowGroups = false;
            this.treeListViewDetail.Size = new System.Drawing.Size(1061, 297);
            this.treeListViewDetail.TabIndex = 2;
            this.treeListViewDetail.UseCompatibleStateImageBehavior = false;
            this.treeListViewDetail.View = System.Windows.Forms.View.Details;
            this.treeListViewDetail.VirtualMode = true;
            this.treeListViewDetail.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeListViewDetail_MouseDoubleClick);
            // 
            // olvColumnNBCellSN
            // 
            this.olvColumnNBCellSN.HeaderFont = null;
            this.olvColumnNBCellSN.Text = "序号";
            this.olvColumnNBCellSN.Width = 109;
            // 
            // olvColumnNBCellName
            // 
            this.olvColumnNBCellName.HeaderFont = null;
            this.olvColumnNBCellName.Text = "建议邻区名称";
            this.olvColumnNBCellName.Width = 100;
            // 
            // olvColumnNBSiteName
            // 
            this.olvColumnNBSiteName.HeaderFont = null;
            this.olvColumnNBSiteName.Text = "建议邻区基站名称";
            this.olvColumnNBSiteName.Width = 120;
            // 
            // olvColumnNBCellTAC
            // 
            this.olvColumnNBCellTAC.HeaderFont = null;
            this.olvColumnNBCellTAC.Text = "建议邻区TAC";
            this.olvColumnNBCellTAC.Width = 100;
            // 
            // olvColumnNBCellECI
            // 
            this.olvColumnNBCellECI.HeaderFont = null;
            this.olvColumnNBCellECI.Text = "建议邻区ECI";
            this.olvColumnNBCellECI.Width = 100;
            // 
            // olvColumnNBCelleNodeBID
            // 
            this.olvColumnNBCelleNodeBID.HeaderFont = null;
            this.olvColumnNBCelleNodeBID.Text = "建议邻区eNodeBID";
            this.olvColumnNBCelleNodeBID.Width = 106;
            // 
            // olvColumnNBCellCellID
            // 
            this.olvColumnNBCellCellID.HeaderFont = null;
            this.olvColumnNBCellCellID.Text = "建议邻区CellID";
            this.olvColumnNBCellCellID.Width = 100;
            // 
            // olvColumnNBCellFreqBand
            // 
            this.olvColumnNBCellFreqBand.HeaderFont = null;
            this.olvColumnNBCellFreqBand.Text = "建议邻区频点属性";
            this.olvColumnNBCellFreqBand.Width = 100;
            // 
            // olvColumnNBCellEARFCN
            // 
            this.olvColumnNBCellEARFCN.HeaderFont = null;
            this.olvColumnNBCellEARFCN.Text = "建议邻区EARFCN";
            this.olvColumnNBCellEARFCN.Width = 100;
            // 
            // olvColumnNBCellPCI
            // 
            this.olvColumnNBCellPCI.HeaderFont = null;
            this.olvColumnNBCellPCI.Text = "建议邻区PCI";
            this.olvColumnNBCellPCI.Width = 100;
            // 
            // olvColumnNBCellLong
            // 
            this.olvColumnNBCellLong.HeaderFont = null;
            this.olvColumnNBCellLong.Text = "建议邻区经度";
            this.olvColumnNBCellLong.Width = 100;
            // 
            // olvColumnNBCellLat
            // 
            this.olvColumnNBCellLat.HeaderFont = null;
            this.olvColumnNBCellLat.Text = "建议邻区纬度";
            this.olvColumnNBCellLat.Width = 100;
            // 
            // olvColumnNBExistCellName
            // 
            this.olvColumnNBExistCellName.HeaderFont = null;
            this.olvColumnNBExistCellName.Text = "已配邻区名称";
            this.olvColumnNBExistCellName.Width = 100;
            // 
            // olvColumnNBExistSiteName
            // 
            this.olvColumnNBExistSiteName.HeaderFont = null;
            this.olvColumnNBExistSiteName.Text = "已配邻区基站名称";
            this.olvColumnNBExistSiteName.Width = 120;
            // 
            // olvColumnNBExistCellTAC
            // 
            this.olvColumnNBExistCellTAC.HeaderFont = null;
            this.olvColumnNBExistCellTAC.Text = "已配邻区TAC";
            this.olvColumnNBExistCellTAC.Width = 100;
            // 
            // olvColumnNBExistCellECI
            // 
            this.olvColumnNBExistCellECI.HeaderFont = null;
            this.olvColumnNBExistCellECI.Text = "已配邻区ECI";
            this.olvColumnNBExistCellECI.Width = 100;
            // 
            // olvColumnNBExistCelleNodeBID
            // 
            this.olvColumnNBExistCelleNodeBID.HeaderFont = null;
            this.olvColumnNBExistCelleNodeBID.Text = "已配邻区eNodeBID";
            this.olvColumnNBExistCelleNodeBID.Width = 100;
            // 
            // olvColumnNBExistCellCellID
            // 
            this.olvColumnNBExistCellCellID.HeaderFont = null;
            this.olvColumnNBExistCellCellID.Text = "已配邻区CellID";
            this.olvColumnNBExistCellCellID.Width = 100;
            // 
            // olvColumnNBExistCellFreqBand
            // 
            this.olvColumnNBExistCellFreqBand.HeaderFont = null;
            this.olvColumnNBExistCellFreqBand.Text = "已配邻区频点属性";
            this.olvColumnNBExistCellFreqBand.Width = 100;
            // 
            // olvColumnNBExistCellEARFCN
            // 
            this.olvColumnNBExistCellEARFCN.HeaderFont = null;
            this.olvColumnNBExistCellEARFCN.Text = "已配邻区EARFCN";
            this.olvColumnNBExistCellEARFCN.Width = 100;
            // 
            // olvColumnNBExistCellPCI
            // 
            this.olvColumnNBExistCellPCI.HeaderFont = null;
            this.olvColumnNBExistCellPCI.Text = "已配邻区PCI";
            this.olvColumnNBExistCellPCI.Width = 100;
            // 
            // olvColumnNBExistCellLong
            // 
            this.olvColumnNBExistCellLong.HeaderFont = null;
            this.olvColumnNBExistCellLong.Text = "已配邻区经度";
            this.olvColumnNBExistCellLong.Width = 100;
            // 
            // olvColumnNBExistCellLat
            // 
            this.olvColumnNBExistCellLat.HeaderFont = null;
            this.olvColumnNBExistCellLat.Text = "已配邻区纬度";
            this.olvColumnNBExistCellLat.Width = 100;
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "距离";
            this.olvColumnDistance.Width = 100;
            // 
            // olvColumnAngle
            // 
            this.olvColumnAngle.HeaderFont = null;
            this.olvColumnAngle.Text = "夹角";
            this.olvColumnAngle.Width = 100;
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(0, 0);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.treeListView);
            this.splitContainer1.Panel1.RightToLeft = System.Windows.Forms.RightToLeft.No;
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.treeListViewDetail);
            this.splitContainer1.Panel2.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.splitContainer1.Size = new System.Drawing.Size(1061, 546);
            this.splitContainer1.SplitterDistance = 245;
            this.splitContainer1.TabIndex = 3;
            // 
            // TDLTECoSiteAnaListForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1061, 546);
            this.Controls.Add(this.splitContainer1);
            this.Name = "TDLTECoSiteAnaListForm";
            this.Text = "同址共向小区分析结果列表";
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewDetail)).EndInit();
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeListView;
        private BrightIdeasSoftware.OLVColumn olvColumnRegionName;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnSiteName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCellECI;
        private BrightIdeasSoftware.OLVColumn olvColumnCellEARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellPCI;
        private BrightIdeasSoftware.OLVColumn olvColumnCellLong;
        private BrightIdeasSoftware.OLVColumn olvColumnCellLat;
        private BrightIdeasSoftware.OLVColumn olvColumnCelleNodeBID;
        private BrightIdeasSoftware.OLVColumn olvColumnCellCellID;
        private BrightIdeasSoftware.OLVColumn olvColumnCellFreqBand;
        private BrightIdeasSoftware.OLVColumn olvColumnMissedCount;
        private BrightIdeasSoftware.TreeListView treeListViewDetail;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnNBSiteName;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellECI;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCelleNodeBID;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellCellID;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellFreqBand;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellEARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellPCI;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellLong;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellLat;
        private BrightIdeasSoftware.OLVColumn olvColumnNBExistCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnNBExistSiteName;
        private BrightIdeasSoftware.OLVColumn olvColumnNBExistCellTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnNBExistCellECI;
        private BrightIdeasSoftware.OLVColumn olvColumnNBExistCelleNodeBID;
        private BrightIdeasSoftware.OLVColumn olvColumnNBExistCellCellID;
        private BrightIdeasSoftware.OLVColumn olvColumnNBExistCellFreqBand;
        private BrightIdeasSoftware.OLVColumn olvColumnNBExistCellEARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnNBExistCellPCI;
        private BrightIdeasSoftware.OLVColumn olvColumnNBExistCellLong;
        private BrightIdeasSoftware.OLVColumn olvColumnNBExistCellLat;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnAngle;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCoEarfcnPciCount;
        private System.Windows.Forms.SplitContainer splitContainer1;
    }
}