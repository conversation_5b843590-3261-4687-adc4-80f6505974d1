﻿namespace MasterCom.RAMS.Stat
{
    partial class StatReportContent_B
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            this.tab_cms = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.popUp_tsmi = new System.Windows.Forms.ToolStripMenuItem();
            this.close_tsmi = new System.Windows.Forms.ToolStripMenuItem();
            this.contextGridManu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miSort = new System.Windows.Forms.ToolStripMenuItem();
            this.miDrawRegionGrid = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.insertRowMenu = new System.Windows.Forms.ToolStripMenuItem();
            this.insertColumnMenu = new System.Windows.Forms.ToolStripMenuItem();
            this.delRowMenu = new System.Windows.Forms.ToolStripMenuItem();
            this.delColumnMenu = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.goodToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToExcelSimple = new System.Windows.Forms.ToolStripMenuItem();
            this.exportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.cellSetupMenu = new System.Windows.Forms.ToolStripMenuItem();
            this.groupBox = new System.Windows.Forms.GroupBox();
            this.dataGridView = new System.Windows.Forms.DataGridView();
            this.tab_cms.SuspendLayout();
            this.contextGridManu.SuspendLayout();
            this.groupBox.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).BeginInit();
            this.SuspendLayout();
            // 
            // tab_cms
            // 
            this.tab_cms.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.popUp_tsmi,
            this.close_tsmi});
            this.tab_cms.Name = "tab_cms";
            this.tab_cms.Size = new System.Drawing.Size(99, 48);
            // 
            // popUp_tsmi
            // 
            this.popUp_tsmi.Name = "popUp_tsmi";
            this.popUp_tsmi.Size = new System.Drawing.Size(98, 22);
            this.popUp_tsmi.Text = "弹出";
            this.popUp_tsmi.Visible = false;
            this.popUp_tsmi.Click += new System.EventHandler(this.popUp_tsmi_Click);
            // 
            // close_tsmi
            // 
            this.close_tsmi.Name = "close_tsmi";
            this.close_tsmi.Size = new System.Drawing.Size(98, 22);
            this.close_tsmi.Text = "关闭";
            this.close_tsmi.Click += new System.EventHandler(this.close_tsmi_Click);
            // 
            // contextGridManu
            // 
            this.contextGridManu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miSort,
            this.miDrawRegionGrid,
            this.toolStripSeparator2,
            this.insertRowMenu,
            this.insertColumnMenu,
            this.delRowMenu,
            this.delColumnMenu,
            this.toolStripMenuItem1,
            this.goodToolStripMenuItem,
            this.toolStripSeparator1,
            this.cellSetupMenu});
            this.contextGridManu.Name = "contextMenuStrip1";
            this.contextGridManu.Size = new System.Drawing.Size(168, 264);
            this.contextGridManu.Opening += new System.ComponentModel.CancelEventHandler(this.contextGridManu_Opening);
            // 
            // miSort
            // 
            this.miSort.Name = "miSort";
            this.miSort.Size = new System.Drawing.Size(167, 22);
            this.miSort.Text = "排序";
            this.miSort.Click += new System.EventHandler(this.miSort_Click);
            // 
            // miDrawRegionGrid
            // 
            this.miDrawRegionGrid.Name = "miDrawRegionGrid";
            this.miDrawRegionGrid.Size = new System.Drawing.Size(167, 22);
            this.miDrawRegionGrid.Text = "区域网格渲染";
            this.miDrawRegionGrid.Click += new System.EventHandler(this.miDrawRegionGrid_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(164, 6);
            // 
            // insertRowMenu
            // 
            this.insertRowMenu.Name = "insertRowMenu";
            this.insertRowMenu.Size = new System.Drawing.Size(167, 22);
            this.insertRowMenu.Text = "前插入行";
            this.insertRowMenu.Visible = false;
            this.insertRowMenu.Click += new System.EventHandler(this.insertRowMenu_Click);
            // 
            // insertColumnMenu
            // 
            this.insertColumnMenu.Name = "insertColumnMenu";
            this.insertColumnMenu.Size = new System.Drawing.Size(167, 22);
            this.insertColumnMenu.Text = "前插入列";
            this.insertColumnMenu.Click += new System.EventHandler(this.insertColumnMenu_Click);
            // 
            // delRowMenu
            // 
            this.delRowMenu.Name = "delRowMenu";
            this.delRowMenu.Size = new System.Drawing.Size(167, 22);
            this.delRowMenu.Text = "删除所在行";
            this.delRowMenu.Visible = false;
            this.delRowMenu.Click += new System.EventHandler(this.delRowMenu_Click);
            // 
            // delColumnMenu
            // 
            this.delColumnMenu.Name = "delColumnMenu";
            this.delColumnMenu.Size = new System.Drawing.Size(167, 22);
            this.delColumnMenu.Text = "删除所在列";
            this.delColumnMenu.Click += new System.EventHandler(this.delColumnMenu_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(164, 6);
            // 
            // goodToolStripMenuItem
            // 
            this.goodToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportToExcelSimple,
            this.exportExcel});
            this.goodToolStripMenuItem.Name = "goodToolStripMenuItem";
            this.goodToolStripMenuItem.Size = new System.Drawing.Size(167, 22);
            this.goodToolStripMenuItem.Text = "保存为";
            // 
            // miExportToExcelSimple
            // 
            this.miExportToExcelSimple.Name = "miExportToExcelSimple";
            this.miExportToExcelSimple.Size = new System.Drawing.Size(159, 22);
            this.miExportToExcelSimple.Text = "Excel文件(简单)";
            this.miExportToExcelSimple.Click += new System.EventHandler(this.miExportToExcelSimple_Click);
            // 
            // exportExcel
            // 
            this.exportExcel.Name = "exportExcel";
            this.exportExcel.Size = new System.Drawing.Size(159, 22);
            this.exportExcel.Text = "Excel文件(详细)";
            this.exportExcel.Click += new System.EventHandler(this.exportExcel_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(164, 6);
            // 
            // cellSetupMenu
            // 
            this.cellSetupMenu.Name = "cellSetupMenu";
            this.cellSetupMenu.Size = new System.Drawing.Size(167, 22);
            this.cellSetupMenu.Text = "设置单元格内容...";
            this.cellSetupMenu.Click += new System.EventHandler(this.cellSetupMenu_Click);
            // 
            // groupBox
            // 
            this.groupBox.Controls.Add(this.dataGridView);
            this.groupBox.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox.Location = new System.Drawing.Point(0, 0);
            this.groupBox.Name = "groupBox";
            this.groupBox.Size = new System.Drawing.Size(563, 310);
            this.groupBox.TabIndex = 12;
            this.groupBox.TabStop = false;
            // 
            // dataGridView
            // 
            this.dataGridView.AllowUserToAddRows = false;
            this.dataGridView.AllowUserToDeleteRows = false;
            this.dataGridView.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView.ColumnHeadersVisible = false;
            this.dataGridView.ContextMenuStrip = this.contextGridManu;
            this.dataGridView.Cursor = System.Windows.Forms.Cursors.Default;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle1.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.GradientInactiveCaption;
            dataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.InfoText;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridView.DefaultCellStyle = dataGridViewCellStyle1;
            this.dataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridView.GridColor = System.Drawing.Color.LightCyan;
            this.dataGridView.Location = new System.Drawing.Point(3, 17);
            this.dataGridView.Name = "dataGridView";
            this.dataGridView.ReadOnly = true;
            this.dataGridView.RowHeadersVisible = false;
            this.dataGridView.RowHeadersWidth = 30;
            this.dataGridView.RowTemplate.Height = 18;
            this.dataGridView.ShowCellErrors = false;
            this.dataGridView.ShowCellToolTips = false;
            this.dataGridView.ShowEditingIcon = false;
            this.dataGridView.ShowRowErrors = false;
            this.dataGridView.Size = new System.Drawing.Size(557, 290);
            this.dataGridView.TabIndex = 10;
            this.dataGridView.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView_CellClick);
            this.dataGridView.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView_CellDoubleClick);
            this.dataGridView.ColumnWidthChanged += new System.Windows.Forms.DataGridViewColumnEventHandler(this.dataGridView_ColumnWidthChanged);
            // 
            // StatReportContent_B
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(563, 310);
            this.Controls.Add(this.groupBox);
            this.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "StatReportContent_B";
            this.TabPageContextMenuStrip = this.tab_cms;
            this.tab_cms.ResumeLayout(false);
            this.contextGridManu.ResumeLayout(false);
            this.groupBox.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip tab_cms;
        private System.Windows.Forms.ToolStripMenuItem popUp_tsmi;
        private System.Windows.Forms.ToolStripMenuItem close_tsmi;
        private System.Windows.Forms.ContextMenuStrip contextGridManu;
        private System.Windows.Forms.ToolStripMenuItem insertRowMenu;
        private System.Windows.Forms.ToolStripMenuItem insertColumnMenu;
        private System.Windows.Forms.ToolStripMenuItem delRowMenu;
        private System.Windows.Forms.ToolStripMenuItem delColumnMenu;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem goodToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem exportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem cellSetupMenu;
        private System.Windows.Forms.GroupBox groupBox;
        private System.Windows.Forms.DataGridView dataGridView;
        private System.Windows.Forms.ToolStripMenuItem miSort;
        private System.Windows.Forms.ToolStripMenuItem miDrawRegionGrid;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcelSimple;
    }
}