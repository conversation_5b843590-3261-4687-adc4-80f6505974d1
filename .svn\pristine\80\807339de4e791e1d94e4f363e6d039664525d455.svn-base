﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    public partial class MapLTECellLayerBTSPropertis : MTLayerPropUserControl
    {
        public MapLTECellLayerBTSPropertis()
        {
            InitializeComponent();
        }

        private MapLTECellLayer layer = null;
        public override void Setup(object obj)
        {
            layer = obj as MapLTECellLayer;
            if (layer==null)
            {
                return;
            }
            Text = "基站";
            this.checkBoxDisplay.Checked = layer.DrawBTS;
            this.colorBTS.Color = layer.ColorBTS;
            this.numericUpDownSize.Value = (decimal)layer.SizeBTS;
            this.TrackBarOpacity.Value = layer.ColorBTS.A;
            this.checkBoxDrawBTSLabel.Checked = layer.DrawBTSLabel;
            this.cbxBTSName.Checked = layer.DrawBTSLabel;
            this.cbxBTSType.Checked = layer.DrawBTSType;
            this.cbxBTSDescription.Checked = layer.DrawBTSDescription;
            this.cbxBTSLatitude.Checked = layer.DrawBTSLatitude;
            this.cbxBTSLongitude.Checked = layer.DrawBTSLongitude;
            colorLable.Color = layer.ColorBTSLabel;

            checkBoxDisplay.CheckedChanged += new EventHandler(checkBoxDisplay_CheckedChanged);
            colorBTS.ColorChanged += new EventHandler(colorBTS_ColorChanged);
            numericUpDownSize.ValueChanged += new EventHandler(numericUpDownSize_ValueChanged);
            TrackBarOpacity.ValueChanged += new EventHandler(TrackBarOpacity_ValueChanged);
            checkBoxDrawBTSLabel.CheckedChanged += new EventHandler(checkBoxDrawBTSLabel_CheckedChanged);
            cbxBTSName.CheckedChanged += new EventHandler(cbxBTSName_CheckedChanged);
            cbxBTSType.CheckedChanged += new EventHandler(cbxBTSType_CheckedChanged);
            cbxBTSDescription.CheckedChanged += new EventHandler(cbxBTSDescription_CheckedChanged);
            cbxBTSLatitude.CheckedChanged += new EventHandler(cbxBTSLatitude_CheckedChanged);
            cbxBTSLongitude.CheckedChanged += new EventHandler(cbxBTSLongitude_CheckedChanged);
            btnFont.Click += new EventHandler(btnFont_Click);
            colorLable.ColorChanged += new EventHandler(colorLabel_ColorChanged);
        }

        FontDialog fontDialog = new FontDialog();
        void btnFont_Click(object sender, EventArgs e)
        {
            fontDialog.Font = layer.FontCellLabel;
            if (fontDialog.ShowDialog(this) == DialogResult.OK)
            {
                layer.FontBTSLabel = fontDialog.Font;
            }
        }
        void cbxBTSLongitude_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawBTSLongitude = cbxBTSLongitude.Checked;
        }

        void cbxBTSLatitude_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawBTSLatitude = cbxBTSLatitude.Checked;
        }

        void cbxBTSDescription_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawBTSDescription = cbxBTSDescription.Checked;
        }

        void cbxBTSType_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawBTSType = cbxBTSType.Checked;
        }

        void cbxBTSName_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawBTSName = cbxBTSName.Checked;
        }

        void checkBoxDrawBTSLabel_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawBTSLabel = checkBoxDrawBTSLabel.Checked;
        }

        void TrackBarOpacity_ValueChanged(object sender, EventArgs e)
        {
            layer.ColorBTS = Color.FromArgb(TrackBarOpacity.Value,layer.ColorBTS);
        }

        void numericUpDownSize_ValueChanged(object sender, EventArgs e)
        {
            layer.SizeBTS = (int)numericUpDownSize.Value;
        }

        void colorBTS_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorBTS = colorBTS.Color;
        }

        void checkBoxDisplay_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawBTS = checkBoxDisplay.Checked;
        }

        void colorLabel_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorBTSLabel = colorLable.Color;
        }
    }
}
