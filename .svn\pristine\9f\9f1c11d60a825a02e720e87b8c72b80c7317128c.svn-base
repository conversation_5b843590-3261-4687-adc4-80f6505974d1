using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using GeneGraph;
using System.Drawing;
using System.Collections;
using System.Drawing.Drawing2D;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func.AssistLayer
{
    [Serializable()]
    public class DTDataArrowManager
    {
        public bool Visible { get; set; } = false;
        [NonSerialized]
        private Dictionary<int, DTDataArrowFileItem> dtArrows = new Dictionary<int, DTDataArrowFileItem>();
        [NonSerialized]
        ArrayList showElements = new ArrayList();
        public void Draw(Graphics g)
        {
            if (!Visible) return;
            showElements.Clear();
            foreach (DTDataArrowFileItem dtdafi in dtArrows.Values)
            {
                dtdafi.Draw(g, showElements);
            }
        }

        public DTDataArrowFileItem this[int id]
        {
            get
            {
                if (dtArrows.ContainsKey(id))
                    return dtArrows[id];
                return null;
            }
        }

        public bool Contains(int id)
        {
            return dtArrows.ContainsKey(id);
        }

        public void Clear()
        {
            dtArrows.Clear();
        }

        public void Add(DTFileDataManager dm, IGisAdapter gisAdapter)
        {
            if (!dtArrows.ContainsKey(dm.FileID))
            {
                dtArrows.Add(dm.FileID, new DTDataArrowFileItem(dm, gisAdapter));
            }
        }

        public void Remove(DTFileDataManager dm)
        {
            if (dtArrows.ContainsKey(dm.FileID))
                dtArrows.Remove(dm.FileID);
        }
    }

    [Serializable()]
    public class DTDataArrowFileItem
    {
        public bool Visible { get; set; } = true;
        DTFileDataManager fileDataManger { get; set; }
        [NonSerialized]
        ArrayList elements = new ArrayList();
        [NonSerialized]
        List<Event> events = new List<Event>();
        public double ArrowOffset { get; set; } = 10;

        public void SetShowEvents(List<Event> value)
        {
            events = new List<Event>();
            if (value != null)
                events.AddRange(value);
        }

        private bool IsContains(List<Event> allevts, Event[] evts)
        {
            foreach (Event ev in evts)
            {
                if (!allevts.Contains(ev))
                    return false;
            }
            return true;
        }

        IGisAdapter gisAdapter;
        public DTDataArrowFileItem(DTFileDataManager fileDataManger, IGisAdapter gisAdapter)
        {
            this.fileDataManger = fileDataManger;
            this.gisAdapter = gisAdapter;
            CreateArrow(fileDataManger.Events, gisAdapter);
        }

        public void Draw(Graphics g, ArrayList showElements)
        {
            if (!Visible) return;
            foreach (BaseElement be in elements)
            {
                if (be.Tag != null && IsContains(events, be.Tag as Event[]))
                {
                    drawVisibleElement(g, showElements, be);
                }
            }
        }

        private void drawVisibleElement(Graphics g, ArrayList showElements, BaseElement be)
        {
            be.Visible = true;
            if (be is LabelElementEx)
            {
                if (this.gisAdapter.GetScale() <= 200000)
                {
                    setBaseElementVisible(g, showElements, be);
                    if (be.Visible)
                    {
                        showElements.Add(be);
                    }
                    be.Draw(g);
                }
            }
            else
            {
                if (this.gisAdapter.GetScale() <= 100000)
                {
                    if (be.Visible)
                    {
                        showElements.Add(be);
                    }
                    be.Draw(g);
                }
            }
        }

        private void setBaseElementVisible(Graphics g, ArrayList showElements, BaseElement be)
        {
            foreach (BaseElement be1 in showElements)
            {
                if (be1 is LabelElementEx && (be as LabelElementEx).GetTestBound(g).IntersectsWith((be1 as LabelElementEx).GetTestBound(g)))
                {
                    be.Visible = false;
                    break;
                }
            }
        }

        private void CreateArrow(List<Event> events, IGisAdapter gisAdapter)
        {
            elements = new ArrayList();
            for (int i = 0; i < events.Count; i++)
            {
                if (i + 1 < events.Count)
                    elements.Add(CreateArrow(events[i], events[i + 1], gisAdapter));
                elements.Add(CreateLabel(events[i], i.ToString(), gisAdapter));
            }
        }

        private BaseElement CreateLabel(Event ev, string txt, IGisAdapter gisAdapter)
        {
            LabelElementEx le = new LabelElementEx();
            le.Text = txt;
            le.LocationM = new DbPoint(ev.Longitude, ev.Latitude);
            le.GisAdapter = gisAdapter;
            le.IsDraw = true;
            le.MyFill.Color = Color.Yellow;
            le.MyFont.IsBold = true;
            le.OffsetV = 15;
            le.Tag = new Event[] { ev };
            return le;
        }

        private BaseElement CreateArrow(Event ev1, Event ev2, IGisAdapter gisAdapter)
        {
            ArrowElementEx aee = new ArrowElementEx(new DbPoint(ev1.Longitude, ev1.Latitude), new DbPoint(ev2.Longitude, ev2.Latitude));
            aee.GisAdapter = gisAdapter;
            aee.Tag = new Event[] { ev1, ev2 };
            aee.MyPen.Width = 2;
            aee.MyPen.Color = Color.DarkViolet;
            return aee;
        }
    }
}
