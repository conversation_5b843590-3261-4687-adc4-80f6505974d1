﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc.CQTSiteStat
{
    class CQTSiteCellMnger
    {
        private static CQTSiteCellMnger instance = null;
        public static CQTSiteCellMnger Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new CQTSiteCellMnger();
                }
                return instance;
            }
        }

        private CQTSiteCellMnger()
        {
          
        }

        private Dictionary<string, CQTSiteCellInfo> siteNameDic = new Dictionary<string, CQTSiteCellInfo>();

        public CQTSiteCellInfo GetSiteInfo(string siteName)
        {
            CQTSiteCellInfo info = null;
            if (!siteNameDic.TryGetValue(siteName, out info))
            {
                info = new CQTSiteCellInfo();
            }
            return info;
        }

        public CQTSiteCellInfo GetSiteInfo(int areaID)
        {
            CQTSiteCellInfo info = new CQTSiteCellInfo();
            string name = AreaManager.GetInstance().GetAreaDesc(10, areaID);//10：cqt标准化地点
            if (string.IsNullOrEmpty(name))
            {
                return info;
            }
            return this.GetSiteInfo(name);
        }

        bool isInited = false;
        internal void Init()
        {
            if (isInited)
            {
                return;
            }
            WaitTextBox.Show("正在获取室分站点信息...", queryFromDB);
            isInited = true;
        }

        private void queryFromDB()
        {
            try
            {
                QueryCQTSiteCellCfg qry = new QueryCQTSiteCellCfg();
                qry.Query();
                siteNameDic = qry.SiteNameDic;
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show(ex.ToString());
            }
            finally
            {
                WaitTextBox.Close();
            }
        }
    }


    public class QueryCQTSiteCellCfg : DIYSQLBase
    {
        public QueryCQTSiteCellCfg()
        {
            this.MainDB = true;
        }

        protected override string getSqlTextString()
        {
            return "select [strDistrictName],[strSiteName],[strCellName],[strCGI] from [tb_cfg_site_cell]";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] arr = new E_VType[4];
            int i = 0;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i] = E_VType.E_String;
            return arr;
        }

        readonly Dictionary<string, CQTSiteCellInfo> siteNameDic = new Dictionary<string, CQTSiteCellInfo>();
        public Dictionary<string, CQTSiteCellInfo> SiteNameDic
        {
            get { return siteNameDic; }
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CQTSiteCellInfo info = new CQTSiteCellInfo();
                    info.DistrictName = package.Content.GetParamString();
                    info.SiteName = package.Content.GetParamString();
                    info.CellName = package.Content.GetParamString();
                    info.CGI = package.Content.GetParamString();
                    siteNameDic[info.SiteName] = info;
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

}
