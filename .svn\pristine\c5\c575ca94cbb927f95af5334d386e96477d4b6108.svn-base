﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Model.RoadProtection
{
    public class DIYQueryTDData : DIYSQLBase
    {
        readonly List<int> tdIDList = new List<int>();
        public List<int> TdIDList
        {
            get { return tdIDList; }
        }

        readonly List<TDData> tdList = new List<TDData>();
        public List<TDData> TDDataList
        {
            get { return tdList; }
        }

        readonly Dictionary<int, TDData> tdDic = new Dictionary<int, TDData>();
        public Dictionary<int, TDData> TdDic
        {
            get { return tdDic; }
        }

        private readonly string _dbname;
        public DIYQueryTDData(MainModel mainModel, DBSetting db)
            : base(mainModel)
        {
            _dbname = db.Dbname;
        }
        protected override string getSqlTextString()
        {
            return "select distinct top 10 istatnum as INUM,weightvalue as WVAL from " + _dbname + ".dbo.v_autotest_relana_tdevent order by weightvalue desc,istatnum";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[2];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Float;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            tdList.Clear();
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    TDData tddata = new TDData();
                    tddata.Inum = package.Content.GetParamInt();
                    tddata.Wval = package.Content.GetParamFloat();

                    tdList.Add(tddata);
                    //do your code here
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        public override string Name
        {
            get { return "DIYQueryTDData"; }
        }
    }
}
