﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    [Serializable]
    public class PoorSINRCause : CauseBase
    {
        public PoorSINRCause()
        {
            AddSubReason(new PoorQualMod3Interf());
            AddSubReason(new PoorQualTDSInterfFFreqBand());
            AddSubReason(new PoorQualWeakCover());
        }

        public override string Name
        {
            get { return "SINR差"; }
        }
        public float SINRMax { get; set; } = 6;
        public override string Desc
        {
            get
            {
                return string.Format("SINR≤{0}", SINRMax);
            }
        }

        public override string Suggestion
        {
            get
            {
                return null;
            }
        }

        public override void Judge(LowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP)
        {
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                if (!segItem.IsNeedJudge(pnt))
                {
                    continue;
                }

                float? sinr = (float?)GetSINR(pnt);
                if (sinr == null || sinr >= SINRMax)
                {
                    continue;
                }

                foreach (CauseBase subReason in SubCauses)
                {
                    if (!segItem.IsNeedJudge(pnt))
                    {
                        break;
                    }
                    subReason.JudgeSinglePoint(segItem, pnt);
                }

                if (segItem.IsNeedJudge(pnt))
                {
                    UnknowReason r = new UnknowReason();
                    r.Parent = this;
                    segItem.SetReason(new LowSpeedPointDetail(pnt, r));
                }
                if (!segItem.NeedJudge)
                {
                    return;
                }

            }
        }

        protected object GetSINR(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_SINR"];
            }
            return tp["lte_SINR"];
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;

                paramDic["sinrMax"] = this.SINRMax;

                List<object> list = new List<object>();
                foreach (CauseBase cause in SubCauses)
                {
                    list.Add(cause.CfgParam);
                }
                paramDic["SubCauseSet"] = list;

                return paramDic;

            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.SINRMax = (float)value["sinrMax"];

                SubCauses = new List<CauseBase>();
                List<object> list = value["SubCauseSet"] as List<object>;
                foreach (object item in list)
                {
                    Dictionary<string, object> dic = item as Dictionary<string, object>;
                    string typeName = dic["TypeName"].ToString();
                    System.Reflection.Assembly assembly = System.Reflection.Assembly.GetExecutingAssembly();
                    CauseBase cause = (CauseBase)assembly.CreateInstance(typeName);
                    cause.CfgParam = dic;
                    AddSubReason(cause);
                }
            }
        }
    }

    [Serializable]
    public class PoorBLERCause : CauseBase
    {
        public PoorBLERCause()
        {
            AddSubReason(new PoorQualMod3Interf());
            AddSubReason(new PoorQualTDSInterfFFreqBand());
            AddSubReason(new PoorQualWeakCover());
        }

        public override string Name
        {
            get { return "高BLER"; }
        }

        public override string Desc
        {
            get { return null; }
        }

        public override string Suggestion
        {
            get { return null; }
        }
        
        public float BLERMin { get; set; } = 10;

        public override void Judge(LowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP)
        {
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                if (!segItem.IsNeedJudge(pnt))
                {
                    continue;
                }

                float? bler = (float?)GetBler(pnt);
                if (bler == null || bler <= BLERMin)
                {
                    continue;
                }

                foreach (CauseBase subReason in SubCauses)
                {
                    if (!segItem.IsNeedJudge(pnt))
                    {
                        break;
                    }
                    subReason.JudgeSinglePoint(segItem, pnt);
                }
                if (segItem.IsNeedJudge(pnt))
                {
                    UnknowReason r = new UnknowReason();
                    r.Parent = this;
                    segItem.SetReason(new LowSpeedPointDetail(pnt, r));
                }
                if (!segItem.NeedJudge)
                {
                    return;
                }

            }
        }
        protected object GetBler(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_PDCCH_BLER"];
            }
            return tp["lte_PDCCH_BLER"];
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["blerMin"] = this.BLERMin;
                List<object> list = new List<object>();
                foreach (CauseBase cause in SubCauses)
                {
                    list.Add(cause.CfgParam);
                }
                paramDic["SubCauseSet"] = list;

                return paramDic;

            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.BLERMin = (float)value["blerMin"];
                SubCauses = new List<CauseBase>();
                List<object> list = value["SubCauseSet"] as List<object>;
                foreach (object item in list)
                {
                    Dictionary<string, object> dic = item as Dictionary<string, object>;
                    string typeName = dic["TypeName"].ToString();
                    System.Reflection.Assembly assembly = System.Reflection.Assembly.GetExecutingAssembly();
                    CauseBase cause = (CauseBase)assembly.CreateInstance(typeName);
                    cause.CfgParam = dic;
                    AddSubReason(cause);
                }
            }
        }
    }

    [Serializable]
    public class PoorQualMod3Interf : CauseBase
    {
        public override string Name
        {
            get { return "PCI模三冲突"; }
        }
        
        public float RSRPDiffMax { get; set; } = 6;
        public override string Desc
        {
            get
            {
                return string.Format("与主服相差{0}dB内的邻区中，存在与主服同模的小区", RSRPDiffMax);
            }
        }
        [NonSerialized]
        private ICell serverCell = null;
        [NonSerialized]
        private ICell nbCell = null;
        public override string Suggestion
        {
            get
            {
                return string.Format("建议对{0}小区（与主服{1}同模）的发射功率进行调整，或者调整PCI"
                    , nbCell != null ? nbCell.Name : "", serverCell != null ? serverCell.Name : "");
            }
        }

        public override void JudgeSinglePoint(LowSpeedSeg segItem, TestPoint testPoint)
        {
            float? rsrp = (float?)GetRSRP(testPoint);
            if (rsrp == null || rsrp < -141 || rsrp > 25)
            {
                return;
            }
            LTECell scell = testPoint.GetMainLTECell_TdOrFdd();
            if (scell == null)
            {
                return;
            }
            for (int i = 0; i < 6; i++)
            {
                float? nRsrp = (float?)GetNRSRP(testPoint, i);
                if (nRsrp == null)
                {
                    continue;
                }
                if (Math.Abs((float)rsrp - (float)nRsrp) <= RSRPDiffMax)
                {
                    LTECell ncell = testPoint.GetNBLTECell_TdOrFdd(i);
                    if (ncell == null)
                    {
                        continue;
                    }
                    if (ncell.PCI % 3 == scell.PCI % 3)
                    {
                        nbCell = ncell;
                        break;
                    }
                }
            }
            if (nbCell != null)
            {
                PoorQualMod3Interf cln = this.Clone() as PoorQualMod3Interf;
                cln.serverCell = scell;
                cln.nbCell = nbCell;
                segItem.SetReason(new LowSpeedPointDetail(testPoint, cln));
            }
        }

        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            return tp["lte_RSRP"];
        }
        protected object GetNRSRP(TestPoint tp, int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_NCell_RSRP", index];
            }
            return tp["lte_NCell_RSRP", index];
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["rsrpDiffMax"] = this.RSRPDiffMax;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RSRPDiffMax = (float)value["rsrpDiffMax"];
            }
        }
    }

    [Serializable]
    public class PoorQualTDSInterfFFreqBand : CauseBase
    {
        public override string Name
        {
            get { return "TDS对TDL的F频段干扰"; }
        }
        public double Distance { get; set; } = 500;
        public override string Desc
        {
            get { return string.Format("占用到F频段，且{0}米范围内有TDS小区", Distance); }
        }

        [NonSerialized]
        private TDCell tdCell = null;
        public override string Suggestion
        {
            get { return string.Format("对TDS小区{0}的覆盖进行核查", tdCell != null ? tdCell.Name : string.Empty); }
        }

        public override void JudgeSinglePoint(LowSpeedSeg segItem, TestPoint testPoint)
        {
            int? freq = (int?)GetEARFCN(testPoint);
            if (freq == null)
            {
                return;
            }
            if (LTECell.GetBandTypeByEarfcn((int)freq) == LTEBandType.F)
            {//占用F频段
                double distanceX = Distance * 0.00001;
                List<TDCell> cells = CellManager.GetInstance().GetTDCells(testPoint.DateTime);
                foreach (TDCell cell in cells)
                {
                    if (Math.Abs(cell.Longitude - testPoint.Longitude) > distanceX
                        || Math.Abs(cell.Latitude - testPoint.Latitude) > distanceX)
                    {//粗略计算
                        continue;
                    }
                    if (testPoint.Distance2(cell.Longitude, cell.Latitude) <= Distance)
                    {
                        tdCell = cell;
                        break;
                    }
                }
                if (tdCell != null)
                {
                    PoorQualTDSInterfFFreqBand cln = this.Clone() as PoorQualTDSInterfFFreqBand;
                    cln.tdCell = tdCell;
                    segItem.SetReason(new LowSpeedPointDetail(testPoint, cln));
                }
            }
        }
        protected object GetEARFCN(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_EARFCN"];
            }
            return tp["lte_EARFCN"];
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["distance"] = this.Distance;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.Distance = (double)value["distance"];
            }
        }
    }

    [Serializable]
    public class PoorQualWeakCover : CauseBase
    {
        public override string Name
        {
            get { return "弱覆盖"; }
        }
        public float RSRPMax { get; set; } = -85;
        public override string Desc
        {
            get
            {
                return string.Format("信号≤{0}dBm", RSRPMax);
            }
        }
        [NonSerialized]
        private ICell serverCell = null;
        public override string Suggestion
        {
            get
            {
                return string.Format("对小区{0}的发射功率进行调整", serverCell != null ? serverCell.Name : "");
            }
        }

        public override void JudgeSinglePoint(LowSpeedSeg segItem, TestPoint testPoint)
        {
            float? rsrp = (float?)GetRSRP(testPoint);
            if (rsrp == null || rsrp < -141 || rsrp > 25)
            {
                return;
            }
            if (rsrp < RSRPMax)
            {
                PoorQualWeakCover cln = this.Clone() as PoorQualWeakCover;
                segItem.SetReason(new LowSpeedPointDetail(testPoint, cln));
            }
        }
        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            return tp["lte_RSRP"];
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["rsrpMax"] = this.RSRPMax;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.RSRPMax = (float)value["rsrpMax"];
            }
        }
    }


}
