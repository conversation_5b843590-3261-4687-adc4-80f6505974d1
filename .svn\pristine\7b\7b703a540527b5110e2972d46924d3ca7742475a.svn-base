﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FileDistributeAddCityForm : BaseDialog
    {
        public FileDistributeAddCityForm()
        {
            InitializeComponent();
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
        }

        public int SelectedIndex
        {
            get;
            private set;
        }

        public void FillCitys(List<string> citys)
        {
            listBox1.Items.Clear();
            foreach (string c in citys)
            {
                listBox1.Items.Add(c);
            }
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            SelectedIndex = listBox1.SelectedItem == null ? -1 : listBox1.SelectedIndex;
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
