using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Chris.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTGSMDataRateSettingForm : Form
    {
        public ZTGSMDataRateSettingForm()
        {
            InitializeComponent();
            rangeSettingDLRate.RangeAll = new Range(0, false, 5000, true);
            rangeSettingDLRate.Range = new Range(0, false, 90, true);
        }
        public Range DataRateRange
        {
            get { return rangeSettingDLRate.Range; }
        }
        public int Radius
        {
            get {return (int)numRadius.Value; }
        }
        public int CVValue
        {
            get {return (int)numCVValue.Value;}
        }
        public int MeanValue
        {
            get { return (int)numMEANValue.Value; }
        }
        public int TestPointCountLimit
        {
            get { return (int)numSampleCountLimit.Value; }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}