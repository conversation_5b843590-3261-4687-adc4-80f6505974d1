﻿using System.Collections.Generic;

namespace MasterCom.RAMS.ZTFunc
{
    public class NbIotMgrsWeakSinrStater : NbIotMgrsWeakRsrpStater
    {
        public double MaxSINR { get; set; }

        public override void DoStat(NbIotMgrsFuncItem curFuncItem)
        {
            this.tmpFuncItem = curFuncItem;
        }

        public override void SetResultControl()
        {
            NbIotMgrsWeakSinrResult resultControl = new NbIotMgrsWeakSinrResult();
            staterName = resultControl.Desc;
            object[] values = tmpFuncItem.FuncCondtion as object[];
            SerialGridCount = (int)values[0];
            MaxSINR = (double)values[1];
            resultControl.FillData(tmpFuncItem);
            resultControlList = new List<NbIotMgrsResultControlBase>() { resultControl };
        }

        protected override List<WeakGrid> GetWeakGridList(Dictionary<string, List<ScanGridInfo>> gridList)
        {
            List<WeakGrid> weakGridList = new List<WeakGrid>();
            //按栅格中最强sinr小区判断弱覆盖栅格
            foreach (var item in gridList)
            {
                //将栅格中的小区按sinr降序排序
                item.Value.Sort((x, y) => { return -x.R0_CINR.CompareTo(y.R0_CINR); });
                ScanGridInfo grid = item.Value[0];
                if (grid.R0_CINR < MaxSINR)
                {
                    WeakGrid weakGrid = new WeakGrid();
                    weakGrid.CellGrid = item.Value;

                    weakGridList.Add(weakGrid);
                }
            }
            return weakGridList;
        }

        public override Dictionary<string, string> GetResultData()
        {
            return resultList;
        }

        public override void Clear()
        {
            this.tmpFuncItem = null;
        }

        protected override void addResultInfo(List<AreaGridData> areaDataList, CarrierAreaResult carrierAreaResult)
        {
            foreach (var area in areaDataList)
            {
                foreach (var item in carrierAreaResult.carrierResult.AreaList)
                {
                    if (item.AreaName == area.AreaName)
                    {
                        item.SerialWeakSINRGridCoverage = carrierAreaResult.getAreaResultData(area.AreaName, area.IssuesGridCount);
                        break;
                    }
                }
            }

            carrierAreaResult.carrierResult.SerialWeakSINRGridCoverage = carrierAreaResult.getTotalResultData(serialWeakGridCount);
        }
    }
}
