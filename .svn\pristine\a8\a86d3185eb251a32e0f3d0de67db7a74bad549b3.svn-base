﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLowSpeedSettingDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.spinEditSpeed_Max = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditDistance_Max = new DevExpress.XtraEditors.SpinEdit();
            this.simpleButtonOK = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonCancel = new DevExpress.XtraEditors.SimpleButton();
            this.label3 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.spinEditDistanceDiff_Max = new DevExpress.XtraEditors.SpinEdit();
            this.chk_ts = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditTS_Min = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditTS_Max = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditRxlev_Max = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditRxlev_Min = new DevExpress.XtraEditors.SpinEdit();
            this.chk_rxlev = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditBLER_Max = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditBLER_Min = new DevExpress.XtraEditors.SpinEdit();
            this.chk_bler = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditC2I_Max = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditC2I_Min = new DevExpress.XtraEditors.SpinEdit();
            this.chk_c2i = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditMCS_Max = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditMCS_Min = new DevExpress.XtraEditors.SpinEdit();
            this.chk_mcs = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditMEAN_BEP_Max = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditMEAN_BEP_Min = new DevExpress.XtraEditors.SpinEdit();
            this.chk_mean_bep = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditCV_BEP_Max = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditCV_BEP_Min = new DevExpress.XtraEditors.SpinEdit();
            this.chk_cv_bep = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditDistanceDiff_Min = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditDistance_Min = new DevExpress.XtraEditors.SpinEdit();
            this.chk_distanceDiff = new DevExpress.XtraEditors.CheckEdit();
            this.chk_distance = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditSpeed_Min = new DevExpress.XtraEditors.SpinEdit();
            this.chk_speed = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSpeed_Max.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistance_Max.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistanceDiff_Max.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_ts.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTS_Min.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTS_Max.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxlev_Max.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxlev_Min.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_rxlev.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBLER_Max.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBLER_Min.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_bler.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditC2I_Max.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditC2I_Min.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_c2i.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMCS_Max.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMCS_Min.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_mcs.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMEAN_BEP_Max.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMEAN_BEP_Min.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_mean_bep.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCV_BEP_Max.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCV_BEP_Min.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_cv_bep.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistanceDiff_Min.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistance_Min.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_distanceDiff.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_distance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSpeed_Min.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_speed.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox4.SuspendLayout();
            this.groupBox5.SuspendLayout();
            this.SuspendLayout();
            // 
            // spinEditSpeed_Max
            // 
            this.spinEditSpeed_Max.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.spinEditSpeed_Max.Location = new System.Drawing.Point(248, 31);
            this.spinEditSpeed_Max.Name = "spinEditSpeed_Max";
            this.spinEditSpeed_Max.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditSpeed_Max.Properties.Appearance.Options.UseFont = true;
            this.spinEditSpeed_Max.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditSpeed_Max.Properties.Mask.EditMask = "f0";
            this.spinEditSpeed_Max.Size = new System.Drawing.Size(80, 20);
            this.spinEditSpeed_Max.TabIndex = 1;
            // 
            // spinEditDistance_Max
            // 
            this.spinEditDistance_Max.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditDistance_Max.Location = new System.Drawing.Point(248, 21);
            this.spinEditDistance_Max.Name = "spinEditDistance_Max";
            this.spinEditDistance_Max.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditDistance_Max.Properties.Appearance.Options.UseFont = true;
            this.spinEditDistance_Max.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDistance_Max.Properties.Mask.EditMask = "f0";
            this.spinEditDistance_Max.Size = new System.Drawing.Size(80, 20);
            this.spinEditDistance_Max.TabIndex = 2;
            // 
            // simpleButtonOK
            // 
            this.simpleButtonOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonOK.Appearance.Options.UseFont = true;
            this.simpleButtonOK.Location = new System.Drawing.Point(645, 255);
            this.simpleButtonOK.Name = "simpleButtonOK";
            this.simpleButtonOK.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonOK.TabIndex = 4;
            this.simpleButtonOK.Text = "确定";
            this.simpleButtonOK.Click += new System.EventHandler(this.simpleButtonOK_Click);
            // 
            // simpleButtonCancel
            // 
            this.simpleButtonCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonCancel.Appearance.Options.UseFont = true;
            this.simpleButtonCancel.Location = new System.Drawing.Point(738, 253);
            this.simpleButtonCancel.Name = "simpleButtonCancel";
            this.simpleButtonCancel.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonCancel.TabIndex = 5;
            this.simpleButtonCancel.Text = "取消";
            this.simpleButtonCancel.Click += new System.EventHandler(this.simpleButtonCancel_Click);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label3.Location = new System.Drawing.Point(334, 36);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(29, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "Kbps";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.Location = new System.Drawing.Point(334, 25);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(17, 12);
            this.label5.TabIndex = 3;
            this.label5.Text = "米";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label19.Location = new System.Drawing.Point(334, 58);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(17, 12);
            this.label19.TabIndex = 0;
            this.label19.Text = "米";
            // 
            // spinEditDistanceDiff_Max
            // 
            this.spinEditDistanceDiff_Max.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.spinEditDistanceDiff_Max.Location = new System.Drawing.Point(248, 53);
            this.spinEditDistanceDiff_Max.Name = "spinEditDistanceDiff_Max";
            this.spinEditDistanceDiff_Max.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditDistanceDiff_Max.Properties.Appearance.Options.UseFont = true;
            this.spinEditDistanceDiff_Max.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDistanceDiff_Max.Properties.Mask.EditMask = "f0";
            this.spinEditDistanceDiff_Max.Size = new System.Drawing.Size(80, 20);
            this.spinEditDistanceDiff_Max.TabIndex = 1;
            // 
            // chk_ts
            // 
            this.chk_ts.Location = new System.Drawing.Point(42, 56);
            this.chk_ts.Name = "chk_ts";
            this.chk_ts.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chk_ts.Properties.Appearance.Options.UseFont = true;
            this.chk_ts.Properties.Caption = "";
            this.chk_ts.Size = new System.Drawing.Size(21, 19);
            this.chk_ts.TabIndex = 6;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(160, 58);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(72, 12);
            this.labelControl1.TabIndex = 7;
            this.labelControl1.Text = "≤ 时隙数 ≤";
            // 
            // spinEditTS_Min
            // 
            this.spinEditTS_Min.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.spinEditTS_Min.Location = new System.Drawing.Point(69, 55);
            this.spinEditTS_Min.Name = "spinEditTS_Min";
            this.spinEditTS_Min.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditTS_Min.Properties.Appearance.Options.UseFont = true;
            this.spinEditTS_Min.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditTS_Min.Properties.Mask.EditMask = "f0";
            this.spinEditTS_Min.Size = new System.Drawing.Size(80, 20);
            this.spinEditTS_Min.TabIndex = 1;
            // 
            // spinEditTS_Max
            // 
            this.spinEditTS_Max.EditValue = new decimal(new int[] {
            8,
            0,
            0,
            0});
            this.spinEditTS_Max.Location = new System.Drawing.Point(238, 55);
            this.spinEditTS_Max.Name = "spinEditTS_Max";
            this.spinEditTS_Max.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditTS_Max.Properties.Appearance.Options.UseFont = true;
            this.spinEditTS_Max.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditTS_Max.Properties.Mask.EditMask = "f0";
            this.spinEditTS_Max.Size = new System.Drawing.Size(80, 20);
            this.spinEditTS_Max.TabIndex = 1;
            // 
            // spinEditRxlev_Max
            // 
            this.spinEditRxlev_Max.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.spinEditRxlev_Max.Location = new System.Drawing.Point(238, 28);
            this.spinEditRxlev_Max.Name = "spinEditRxlev_Max";
            this.spinEditRxlev_Max.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditRxlev_Max.Properties.Appearance.Options.UseFont = true;
            this.spinEditRxlev_Max.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRxlev_Max.Properties.Mask.EditMask = "f0";
            this.spinEditRxlev_Max.Size = new System.Drawing.Size(80, 20);
            this.spinEditRxlev_Max.TabIndex = 1;
            // 
            // spinEditRxlev_Min
            // 
            this.spinEditRxlev_Min.EditValue = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.spinEditRxlev_Min.Location = new System.Drawing.Point(69, 28);
            this.spinEditRxlev_Min.Name = "spinEditRxlev_Min";
            this.spinEditRxlev_Min.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditRxlev_Min.Properties.Appearance.Options.UseFont = true;
            this.spinEditRxlev_Min.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRxlev_Min.Properties.Mask.EditMask = "f0";
            this.spinEditRxlev_Min.Size = new System.Drawing.Size(80, 20);
            this.spinEditRxlev_Min.TabIndex = 1;
            // 
            // chk_rxlev
            // 
            this.chk_rxlev.Location = new System.Drawing.Point(42, 29);
            this.chk_rxlev.Name = "chk_rxlev";
            this.chk_rxlev.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chk_rxlev.Properties.Appearance.Options.UseFont = true;
            this.chk_rxlev.Properties.Caption = "";
            this.chk_rxlev.Size = new System.Drawing.Size(21, 19);
            this.chk_rxlev.TabIndex = 6;
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(166, 31);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(60, 12);
            this.labelControl2.TabIndex = 7;
            this.labelControl2.Text = "≤ 场强 ≤";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(325, 31);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(18, 12);
            this.labelControl3.TabIndex = 7;
            this.labelControl3.Text = "dBm";
            // 
            // spinEditBLER_Max
            // 
            this.spinEditBLER_Max.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditBLER_Max.Location = new System.Drawing.Point(238, 84);
            this.spinEditBLER_Max.Name = "spinEditBLER_Max";
            this.spinEditBLER_Max.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditBLER_Max.Properties.Appearance.Options.UseFont = true;
            this.spinEditBLER_Max.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditBLER_Max.Properties.Mask.EditMask = "f0";
            this.spinEditBLER_Max.Size = new System.Drawing.Size(80, 20);
            this.spinEditBLER_Max.TabIndex = 1;
            // 
            // spinEditBLER_Min
            // 
            this.spinEditBLER_Min.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditBLER_Min.Location = new System.Drawing.Point(69, 84);
            this.spinEditBLER_Min.Name = "spinEditBLER_Min";
            this.spinEditBLER_Min.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditBLER_Min.Properties.Appearance.Options.UseFont = true;
            this.spinEditBLER_Min.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditBLER_Min.Properties.Mask.EditMask = "f0";
            this.spinEditBLER_Min.Size = new System.Drawing.Size(80, 20);
            this.spinEditBLER_Min.TabIndex = 1;
            // 
            // chk_bler
            // 
            this.chk_bler.Location = new System.Drawing.Point(42, 85);
            this.chk_bler.Name = "chk_bler";
            this.chk_bler.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chk_bler.Properties.Appearance.Options.UseFont = true;
            this.chk_bler.Properties.Caption = "";
            this.chk_bler.Size = new System.Drawing.Size(21, 19);
            this.chk_bler.TabIndex = 6;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(164, 87);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(60, 12);
            this.labelControl4.TabIndex = 7;
            this.labelControl4.Text = "≤ BLER ≤";
            // 
            // spinEditC2I_Max
            // 
            this.spinEditC2I_Max.EditValue = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.spinEditC2I_Max.Location = new System.Drawing.Point(238, 56);
            this.spinEditC2I_Max.Name = "spinEditC2I_Max";
            this.spinEditC2I_Max.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditC2I_Max.Properties.Appearance.Options.UseFont = true;
            this.spinEditC2I_Max.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditC2I_Max.Properties.Mask.EditMask = "f0";
            this.spinEditC2I_Max.Size = new System.Drawing.Size(80, 20);
            this.spinEditC2I_Max.TabIndex = 1;
            // 
            // spinEditC2I_Min
            // 
            this.spinEditC2I_Min.EditValue = new decimal(new int[] {
            30,
            0,
            0,
            -2147483648});
            this.spinEditC2I_Min.Location = new System.Drawing.Point(69, 56);
            this.spinEditC2I_Min.Name = "spinEditC2I_Min";
            this.spinEditC2I_Min.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditC2I_Min.Properties.Appearance.Options.UseFont = true;
            this.spinEditC2I_Min.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditC2I_Min.Properties.Mask.EditMask = "f0";
            this.spinEditC2I_Min.Size = new System.Drawing.Size(80, 20);
            this.spinEditC2I_Min.TabIndex = 1;
            // 
            // chk_c2i
            // 
            this.chk_c2i.Location = new System.Drawing.Point(42, 57);
            this.chk_c2i.Name = "chk_c2i";
            this.chk_c2i.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chk_c2i.Properties.Appearance.Options.UseFont = true;
            this.chk_c2i.Properties.Caption = "";
            this.chk_c2i.Size = new System.Drawing.Size(21, 19);
            this.chk_c2i.TabIndex = 6;
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(167, 59);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(54, 12);
            this.labelControl5.TabIndex = 7;
            this.labelControl5.Text = "≤ C/I ≤";
            // 
            // spinEditMCS_Max
            // 
            this.spinEditMCS_Max.EditValue = new decimal(new int[] {
            13,
            0,
            0,
            0});
            this.spinEditMCS_Max.Location = new System.Drawing.Point(238, 24);
            this.spinEditMCS_Max.Name = "spinEditMCS_Max";
            this.spinEditMCS_Max.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditMCS_Max.Properties.Appearance.Options.UseFont = true;
            this.spinEditMCS_Max.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditMCS_Max.Properties.Mask.EditMask = "f0";
            this.spinEditMCS_Max.Size = new System.Drawing.Size(80, 20);
            this.spinEditMCS_Max.TabIndex = 1;
            // 
            // spinEditMCS_Min
            // 
            this.spinEditMCS_Min.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.spinEditMCS_Min.Location = new System.Drawing.Point(69, 24);
            this.spinEditMCS_Min.Name = "spinEditMCS_Min";
            this.spinEditMCS_Min.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditMCS_Min.Properties.Appearance.Options.UseFont = true;
            this.spinEditMCS_Min.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditMCS_Min.Properties.Mask.EditMask = "f0";
            this.spinEditMCS_Min.Size = new System.Drawing.Size(80, 20);
            this.spinEditMCS_Min.TabIndex = 1;
            // 
            // chk_mcs
            // 
            this.chk_mcs.Location = new System.Drawing.Point(42, 25);
            this.chk_mcs.Name = "chk_mcs";
            this.chk_mcs.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chk_mcs.Properties.Appearance.Options.UseFont = true;
            this.chk_mcs.Properties.Caption = "";
            this.chk_mcs.Size = new System.Drawing.Size(21, 19);
            this.chk_mcs.TabIndex = 6;
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(170, 27);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(54, 12);
            this.labelControl6.TabIndex = 7;
            this.labelControl6.Text = "≤ MCS ≤";
            // 
            // spinEditMEAN_BEP_Max
            // 
            this.spinEditMEAN_BEP_Max.EditValue = new decimal(new int[] {
            64,
            0,
            0,
            0});
            this.spinEditMEAN_BEP_Max.Location = new System.Drawing.Point(248, 54);
            this.spinEditMEAN_BEP_Max.Name = "spinEditMEAN_BEP_Max";
            this.spinEditMEAN_BEP_Max.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditMEAN_BEP_Max.Properties.Appearance.Options.UseFont = true;
            this.spinEditMEAN_BEP_Max.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditMEAN_BEP_Max.Properties.Mask.EditMask = "f0";
            this.spinEditMEAN_BEP_Max.Size = new System.Drawing.Size(80, 20);
            this.spinEditMEAN_BEP_Max.TabIndex = 1;
            // 
            // spinEditMEAN_BEP_Min
            // 
            this.spinEditMEAN_BEP_Min.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditMEAN_BEP_Min.Location = new System.Drawing.Point(58, 54);
            this.spinEditMEAN_BEP_Min.Name = "spinEditMEAN_BEP_Min";
            this.spinEditMEAN_BEP_Min.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditMEAN_BEP_Min.Properties.Appearance.Options.UseFont = true;
            this.spinEditMEAN_BEP_Min.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditMEAN_BEP_Min.Properties.Mask.EditMask = "f0";
            this.spinEditMEAN_BEP_Min.Size = new System.Drawing.Size(80, 20);
            this.spinEditMEAN_BEP_Min.TabIndex = 1;
            // 
            // chk_mean_bep
            // 
            this.chk_mean_bep.Location = new System.Drawing.Point(32, 55);
            this.chk_mean_bep.Name = "chk_mean_bep";
            this.chk_mean_bep.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chk_mean_bep.Properties.Appearance.Options.UseFont = true;
            this.chk_mean_bep.Properties.Caption = "";
            this.chk_mean_bep.Size = new System.Drawing.Size(21, 19);
            this.chk_mean_bep.TabIndex = 6;
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(151, 59);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(84, 12);
            this.labelControl7.TabIndex = 7;
            this.labelControl7.Text = "≤ MEAN_BEP ≤";
            // 
            // spinEditCV_BEP_Max
            // 
            this.spinEditCV_BEP_Max.EditValue = new decimal(new int[] {
            64,
            0,
            0,
            0});
            this.spinEditCV_BEP_Max.Location = new System.Drawing.Point(248, 21);
            this.spinEditCV_BEP_Max.Name = "spinEditCV_BEP_Max";
            this.spinEditCV_BEP_Max.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditCV_BEP_Max.Properties.Appearance.Options.UseFont = true;
            this.spinEditCV_BEP_Max.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditCV_BEP_Max.Properties.Mask.EditMask = "f0";
            this.spinEditCV_BEP_Max.Size = new System.Drawing.Size(80, 20);
            this.spinEditCV_BEP_Max.TabIndex = 1;
            // 
            // spinEditCV_BEP_Min
            // 
            this.spinEditCV_BEP_Min.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditCV_BEP_Min.Location = new System.Drawing.Point(58, 21);
            this.spinEditCV_BEP_Min.Name = "spinEditCV_BEP_Min";
            this.spinEditCV_BEP_Min.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditCV_BEP_Min.Properties.Appearance.Options.UseFont = true;
            this.spinEditCV_BEP_Min.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditCV_BEP_Min.Properties.Mask.EditMask = "f0";
            this.spinEditCV_BEP_Min.Size = new System.Drawing.Size(80, 20);
            this.spinEditCV_BEP_Min.TabIndex = 1;
            // 
            // chk_cv_bep
            // 
            this.chk_cv_bep.Location = new System.Drawing.Point(32, 22);
            this.chk_cv_bep.Name = "chk_cv_bep";
            this.chk_cv_bep.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chk_cv_bep.Properties.Appearance.Options.UseFont = true;
            this.chk_cv_bep.Properties.Caption = "";
            this.chk_cv_bep.Size = new System.Drawing.Size(21, 19);
            this.chk_cv_bep.TabIndex = 6;
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(156, 27);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(72, 12);
            this.labelControl8.TabIndex = 7;
            this.labelControl8.Text = "≤ CV_BEP ≤";
            // 
            // spinEditDistanceDiff_Min
            // 
            this.spinEditDistanceDiff_Min.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditDistanceDiff_Min.Location = new System.Drawing.Point(58, 53);
            this.spinEditDistanceDiff_Min.Name = "spinEditDistanceDiff_Min";
            this.spinEditDistanceDiff_Min.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditDistanceDiff_Min.Properties.Appearance.Options.UseFont = true;
            this.spinEditDistanceDiff_Min.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDistanceDiff_Min.Properties.Mask.EditMask = "f0";
            this.spinEditDistanceDiff_Min.Size = new System.Drawing.Size(80, 20);
            this.spinEditDistanceDiff_Min.TabIndex = 1;
            // 
            // spinEditDistance_Min
            // 
            this.spinEditDistance_Min.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditDistance_Min.Location = new System.Drawing.Point(58, 21);
            this.spinEditDistance_Min.Name = "spinEditDistance_Min";
            this.spinEditDistance_Min.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditDistance_Min.Properties.Appearance.Options.UseFont = true;
            this.spinEditDistance_Min.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDistance_Min.Properties.Mask.EditMask = "f0";
            this.spinEditDistance_Min.Size = new System.Drawing.Size(80, 20);
            this.spinEditDistance_Min.TabIndex = 1;
            // 
            // chk_distanceDiff
            // 
            this.chk_distanceDiff.Location = new System.Drawing.Point(32, 54);
            this.chk_distanceDiff.Name = "chk_distanceDiff";
            this.chk_distanceDiff.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chk_distanceDiff.Properties.Appearance.Options.UseFont = true;
            this.chk_distanceDiff.Properties.Caption = "";
            this.chk_distanceDiff.Size = new System.Drawing.Size(21, 19);
            this.chk_distanceDiff.TabIndex = 6;
            // 
            // chk_distance
            // 
            this.chk_distance.Location = new System.Drawing.Point(32, 22);
            this.chk_distance.Name = "chk_distance";
            this.chk_distance.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chk_distance.Properties.Appearance.Options.UseFont = true;
            this.chk_distance.Properties.Caption = "";
            this.chk_distance.Size = new System.Drawing.Size(21, 19);
            this.chk_distance.TabIndex = 6;
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Location = new System.Drawing.Point(145, 56);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(96, 12);
            this.labelControl9.TabIndex = 7;
            this.labelControl9.Text = "≤ 采样点距离 ≤";
            // 
            // labelControl10
            // 
            this.labelControl10.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl10.Appearance.Options.UseFont = true;
            this.labelControl10.Location = new System.Drawing.Point(157, 25);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(60, 12);
            this.labelControl10.TabIndex = 7;
            this.labelControl10.Text = "≤ 距离 ≤";
            // 
            // spinEditSpeed_Min
            // 
            this.spinEditSpeed_Min.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditSpeed_Min.Location = new System.Drawing.Point(58, 31);
            this.spinEditSpeed_Min.Name = "spinEditSpeed_Min";
            this.spinEditSpeed_Min.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditSpeed_Min.Properties.Appearance.Options.UseFont = true;
            this.spinEditSpeed_Min.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditSpeed_Min.Properties.Mask.EditMask = "f0";
            this.spinEditSpeed_Min.Size = new System.Drawing.Size(80, 20);
            this.spinEditSpeed_Min.TabIndex = 1;
            // 
            // chk_speed
            // 
            this.chk_speed.Location = new System.Drawing.Point(32, 32);
            this.chk_speed.Name = "chk_speed";
            this.chk_speed.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chk_speed.Properties.Appearance.Options.UseFont = true;
            this.chk_speed.Properties.Caption = "";
            this.chk_speed.Size = new System.Drawing.Size(21, 19);
            this.chk_speed.TabIndex = 6;
            // 
            // labelControl11
            // 
            this.labelControl11.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl11.Appearance.Options.UseFont = true;
            this.labelControl11.Location = new System.Drawing.Point(157, 37);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(60, 12);
            this.labelControl11.TabIndex = 7;
            this.labelControl11.Text = "≤ 速率 ≤";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.spinEditDistance_Min);
            this.groupBox1.Controls.Add(this.spinEditDistance_Max);
            this.groupBox1.Controls.Add(this.labelControl10);
            this.groupBox1.Controls.Add(this.chk_distance);
            this.groupBox1.Controls.Add(this.labelControl9);
            this.groupBox1.Controls.Add(this.spinEditDistanceDiff_Min);
            this.groupBox1.Controls.Add(this.spinEditDistanceDiff_Max);
            this.groupBox1.Controls.Add(this.chk_distanceDiff);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.label19);
            this.groupBox1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox1.Location = new System.Drawing.Point(19, 98);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(374, 87);
            this.groupBox1.TabIndex = 8;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "距离";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.spinEditCV_BEP_Min);
            this.groupBox2.Controls.Add(this.spinEditCV_BEP_Max);
            this.groupBox2.Controls.Add(this.chk_cv_bep);
            this.groupBox2.Controls.Add(this.labelControl8);
            this.groupBox2.Controls.Add(this.spinEditMEAN_BEP_Min);
            this.groupBox2.Controls.Add(this.spinEditMEAN_BEP_Max);
            this.groupBox2.Controls.Add(this.chk_mean_bep);
            this.groupBox2.Controls.Add(this.labelControl7);
            this.groupBox2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox2.Location = new System.Drawing.Point(19, 196);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(374, 87);
            this.groupBox2.TabIndex = 9;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "BEP";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.chk_speed);
            this.groupBox3.Controls.Add(this.label3);
            this.groupBox3.Controls.Add(this.spinEditSpeed_Max);
            this.groupBox3.Controls.Add(this.spinEditSpeed_Min);
            this.groupBox3.Controls.Add(this.labelControl11);
            this.groupBox3.ForeColor = System.Drawing.Color.Black;
            this.groupBox3.Location = new System.Drawing.Point(19, 12);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(374, 76);
            this.groupBox3.TabIndex = 10;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "速率设置";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.chk_rxlev);
            this.groupBox4.Controls.Add(this.spinEditRxlev_Max);
            this.groupBox4.Controls.Add(this.spinEditRxlev_Min);
            this.groupBox4.Controls.Add(this.labelControl3);
            this.groupBox4.Controls.Add(this.labelControl4);
            this.groupBox4.Controls.Add(this.labelControl5);
            this.groupBox4.Controls.Add(this.labelControl2);
            this.groupBox4.Controls.Add(this.chk_c2i);
            this.groupBox4.Controls.Add(this.spinEditC2I_Max);
            this.groupBox4.Controls.Add(this.chk_bler);
            this.groupBox4.Controls.Add(this.spinEditC2I_Min);
            this.groupBox4.Controls.Add(this.spinEditBLER_Max);
            this.groupBox4.Controls.Add(this.spinEditBLER_Min);
            this.groupBox4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox4.Location = new System.Drawing.Point(439, 12);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(374, 119);
            this.groupBox4.TabIndex = 11;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "场强、C/I、BLER";
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.chk_mcs);
            this.groupBox5.Controls.Add(this.spinEditMCS_Max);
            this.groupBox5.Controls.Add(this.spinEditMCS_Min);
            this.groupBox5.Controls.Add(this.labelControl6);
            this.groupBox5.Controls.Add(this.chk_ts);
            this.groupBox5.Controls.Add(this.labelControl1);
            this.groupBox5.Controls.Add(this.spinEditTS_Max);
            this.groupBox5.Controls.Add(this.spinEditTS_Min);
            this.groupBox5.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox5.Location = new System.Drawing.Point(439, 142);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(374, 87);
            this.groupBox5.TabIndex = 12;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "编码、时隙数";
            // 
            // ZTLowSpeedSettingDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(841, 300);
            this.Controls.Add(this.groupBox5);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.simpleButtonCancel);
            this.Controls.Add(this.simpleButtonOK);
            this.Name = "ZTLowSpeedSettingDlg";
            this.Text = "低速率分析设置";
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSpeed_Max.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistance_Max.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistanceDiff_Max.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_ts.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTS_Min.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTS_Max.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxlev_Max.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRxlev_Min.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_rxlev.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBLER_Max.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBLER_Min.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_bler.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditC2I_Max.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditC2I_Min.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_c2i.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMCS_Max.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMCS_Min.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_mcs.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMEAN_BEP_Max.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMEAN_BEP_Min.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_mean_bep.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCV_BEP_Max.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCV_BEP_Min.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_cv_bep.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistanceDiff_Min.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistance_Min.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_distanceDiff.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_distance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSpeed_Min.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_speed.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SpinEdit spinEditSpeed_Max;
        private DevExpress.XtraEditors.SpinEdit spinEditDistance_Max;
        private DevExpress.XtraEditors.SimpleButton simpleButtonOK;
        private DevExpress.XtraEditors.SimpleButton simpleButtonCancel;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label19;
        private DevExpress.XtraEditors.SpinEdit spinEditDistanceDiff_Max;
        private DevExpress.XtraEditors.CheckEdit chk_ts;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit spinEditTS_Min;
        private DevExpress.XtraEditors.SpinEdit spinEditTS_Max;
        private DevExpress.XtraEditors.SpinEdit spinEditRxlev_Max;
        private DevExpress.XtraEditors.SpinEdit spinEditRxlev_Min;
        private DevExpress.XtraEditors.CheckEdit chk_rxlev;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit spinEditBLER_Max;
        private DevExpress.XtraEditors.SpinEdit spinEditBLER_Min;
        private DevExpress.XtraEditors.CheckEdit chk_bler;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SpinEdit spinEditC2I_Max;
        private DevExpress.XtraEditors.SpinEdit spinEditC2I_Min;
        private DevExpress.XtraEditors.CheckEdit chk_c2i;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit spinEditMCS_Max;
        private DevExpress.XtraEditors.SpinEdit spinEditMCS_Min;
        private DevExpress.XtraEditors.CheckEdit chk_mcs;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.SpinEdit spinEditMEAN_BEP_Max;
        private DevExpress.XtraEditors.SpinEdit spinEditMEAN_BEP_Min;
        private DevExpress.XtraEditors.CheckEdit chk_mean_bep;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.SpinEdit spinEditCV_BEP_Max;
        private DevExpress.XtraEditors.SpinEdit spinEditCV_BEP_Min;
        private DevExpress.XtraEditors.CheckEdit chk_cv_bep;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.SpinEdit spinEditDistanceDiff_Min;
        private DevExpress.XtraEditors.SpinEdit spinEditDistance_Min;
        private DevExpress.XtraEditors.CheckEdit chk_distanceDiff;
        private DevExpress.XtraEditors.CheckEdit chk_distance;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.SpinEdit spinEditSpeed_Min;
        private DevExpress.XtraEditors.CheckEdit chk_speed;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.GroupBox groupBox5;
    }
}