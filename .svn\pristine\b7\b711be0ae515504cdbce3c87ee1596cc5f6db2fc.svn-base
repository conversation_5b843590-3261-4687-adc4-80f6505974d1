﻿namespace MasterCom.RAMS.ZTFunc.ZTAtuLogKPIGroup
{
    partial class ATUFileGroupForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.lv = new BrightIdeasSoftware.TreeListView();
            this.colName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colFileNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSatisifiedNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSatisifiedPer = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.cms = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportLevelDev = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.lv)).BeginInit();
            this.cms.SuspendLayout();
            this.SuspendLayout();
            // 
            // lv
            // 
            this.lv.AllColumns.Add(this.colName);
            this.lv.AllColumns.Add(this.colFileNum);
            this.lv.AllColumns.Add(this.colSatisifiedNum);
            this.lv.AllColumns.Add(this.colSatisifiedPer);
            this.lv.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colName,
            this.colFileNum,
            this.colSatisifiedNum,
            this.colSatisifiedPer});
            this.lv.ContextMenuStrip = this.cms;
            this.lv.Cursor = System.Windows.Forms.Cursors.Default;
            this.lv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lv.FullRowSelect = true;
            this.lv.GridLines = true;
            this.lv.HeaderWordWrap = true;
            this.lv.IsNeedShowOverlay = false;
            this.lv.Location = new System.Drawing.Point(0, 0);
            this.lv.Name = "lv";
            this.lv.OwnerDraw = true;
            this.lv.ShowGroups = false;
            this.lv.Size = new System.Drawing.Size(1034, 392);
            this.lv.TabIndex = 4;
            this.lv.UseCompatibleStateImageBehavior = false;
            this.lv.View = System.Windows.Forms.View.Details;
            this.lv.VirtualMode = true;
            // 
            // colName
            // 
            this.colName.AspectName = "Name";
            this.colName.HeaderFont = null;
            this.colName.Text = "设备/文件";
            // 
            // colFileNum
            // 
            this.colFileNum.HeaderFont = null;
            this.colFileNum.Text = "文件总数";
            // 
            // colSatisifiedNum
            // 
            this.colSatisifiedNum.HeaderFont = null;
            this.colSatisifiedNum.Text = "符合条件文件个数";
            // 
            // colSatisifiedPer
            // 
            this.colSatisifiedPer.HeaderFont = null;
            this.colSatisifiedPer.Text = "符合条件文件占比(%)";
            this.colSatisifiedPer.Width = 69;
            // 
            // cms
            // 
            this.cms.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.toolStripSeparator1,
            this.miExport,
            this.miExportLevelDev});
            this.cms.Name = "contextMenuStrip1";
            this.cms.Size = new System.Drawing.Size(199, 98);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(198, 22);
            this.miExpandAll.Text = "展开所有节点";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(198, 22);
            this.miCollapseAll.Text = "折叠所有节点";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(195, 6);
            // 
            // miExport
            // 
            this.miExport.Name = "miExport";
            this.miExport.Size = new System.Drawing.Size(198, 22);
            this.miExport.Text = "导出Excel（全部）...";
            this.miExport.Click += new System.EventHandler(this.miExport_Click);
            // 
            // miExportLevelDev
            // 
            this.miExportLevelDev.Name = "miExportLevelDev";
            this.miExportLevelDev.Size = new System.Drawing.Size(198, 22);
            this.miExportLevelDev.Text = "导出Excel（仅设备）...";
            this.miExportLevelDev.Click += new System.EventHandler(this.miExportLevelDev_Click);
            // 
            // ATUFileGroupForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1034, 392);
            this.Controls.Add(this.lv);
            this.Name = "ATUFileGroupForm";
            this.Text = "ATU文件自定义汇总";
            ((System.ComponentModel.ISupportInitialize)(this.lv)).EndInit();
            this.cms.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView lv;
        private BrightIdeasSoftware.OLVColumn colName;
        private BrightIdeasSoftware.OLVColumn colFileNum;
        private BrightIdeasSoftware.OLVColumn colSatisifiedNum;
        private BrightIdeasSoftware.OLVColumn colSatisifiedPer;
        private System.Windows.Forms.ContextMenuStrip cms;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExport;
        private System.Windows.Forms.ToolStripMenuItem miExportLevelDev;
    }
}