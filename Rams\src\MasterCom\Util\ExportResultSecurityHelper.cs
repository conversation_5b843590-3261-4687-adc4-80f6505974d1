﻿using DevExpress.XtraEditors;
using MasterCom.RAMS.UserMng;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.Util
{
    /// <summary>
    /// 安全导出模块（CallBackMethodWithParams方法返回前需执行 WaitBox.Close()）
    /// </summary>
    public class ExportResultSecurityHelper
    {
        protected ExportResultSecurityHelper()
        {

        }

        public const string ObjFileName = "ObjFileName";
        public static bool ExportToExcel(CallBackMethodWithParams doExport, object obj, bool bRemindOpen = false)
        {
            string fileSavePath = "";
            return ExportToExcel(doExport, obj, ref fileSavePath, bRemindOpen);
        }
        public static bool ExportToExcel(CallBackMethodWithParams doExport, object obj, ref string fileSavePath
            , bool bRemindOpen = false)
        {
            return export(FileSimpleTypeHelper.Excel, doExport, obj, ref fileSavePath, bRemindOpen);
        }
        public static bool ExportToCsv(CallBackMethodWithParams doExport, object obj, bool bRemindOpen = false)
        {
            string fileSavePath = "";
            return export(FileSimpleTypeHelper.Csv, doExport, obj, ref fileSavePath, bRemindOpen);
        }
        public static bool ExportToTxt(CallBackMethodWithParams doExport, object obj, bool bRemindOpen = false)
        {
            string fileSavePath = "";
            return export(FileSimpleTypeHelper.Txt, doExport, obj, ref fileSavePath, bRemindOpen);
        }
        public static bool ExportToWord(CallBackMethodWithParams doExport, object obj, bool bRemindOpen = false)
        {
            string fileSavePath = "";
            return export(FileSimpleTypeHelper.Word, doExport, obj, ref fileSavePath, bRemindOpen);
        }
        public static bool ExportToShp(CallBackMethodWithParams doExport, object obj, bool bRemindOpen = false)
        {
            string fileSavePath = "";
            return export(FileSimpleTypeHelper.Shp, doExport, obj, ref fileSavePath, bRemindOpen);
        }
        public static bool ExportToCAP(CallBackMethodWithParams doExport, object obj, bool bRemindOpen = false)
        {
            string fileSavePath = "";
            return export(FileSimpleTypeHelper.CAP, doExport, obj, ref fileSavePath, bRemindOpen);
        }

        /// <summary>
        /// 鉴权是否有权导出和是否需提交导出原因，并返回文件保存路径(暂时使用于未实现压缩加密的部分功能)
        /// </summary>
        /// <returns></returns>
        public static bool GetExportPermit(FileSimpleType fileType, out string fileName)
        {
            fileName = null;
#if PermissionControl_DataExport
            LogInfoItem logItem = ExportFuncResultManager.GetInstance().GetCurLogItem();
            FuncExportPermit exportPermit = ExportFuncResultManager.GetInstance().GetFuncExportPermit(logItem);
            if (!exportPermit.IsCanExportResult)
            {
                ExportFuncResultManager.GetInstance().ClearData();
                DevExpress.XtraEditors.XtraMessageBox.Show("您没有导出该功能结果的权限，请联系管理员！");
                return false;
            }

            if (exportPermit.IsNeedExportCause)
            {
                exportPermit.IsExportToZip = false;

                ExportSecuritySetDlg exportdlg = new ExportSecuritySetDlg(fileType, exportPermit);
                if (exportdlg.ShowDialog() == DialogResult.OK)
                {
                    ExportSecurityCondition cond = exportdlg.GetCondition();
                    fileName = cond.FolderPath + "\\" + cond.FileName;//文件完整路径
                    ExportFuncResultManager.GetInstance().SendExportLog(cond.FileName, logItem, cond.ExportCause);
                    return true;
                }
                else
                {
                    ExportFuncResultManager.GetInstance().ClearData();
                    return false;
                }
            }
            else if (fileType == null)
            {
                if (exportPermit.IsNeedExportLog)
                {
                    ExportFuncResultManager.GetInstance().SendExportLog(fileName, logItem, "");
                }
                return true;
            }
#else
            if (fileType == null)
            {
                return true;
            }
#endif
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Filter = fileType.FilterStr;
            dlg.FilterIndex = fileType.FilterIndex + 1;
            dlg.FileName = ExportFuncResultManager.GetInstance().GetSubFuncName();
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                fileName = dlg.FileName;

#if PermissionControl_DataExport
                if (exportPermit.IsNeedExportLog)
                {
                    ExportFuncResultManager.GetInstance().SendExportLog(fileName, logItem, "");
                }
#endif
                return true;
            }
            return false;
        }

        protected static bool export(FileSimpleType fileType, CallBackMethodWithParams doExport
          , object obj, ref string fileSavePath, bool bRemindOpen)
        {
            LogInfoItem logItem = ExportFuncResultManager.GetInstance().GetCurLogItem();

            ExportSecurityCondition exportCond;
            if (!GetExportPermit(logItem, fileType, ref fileSavePath, out exportCond))
            {
                return false;
            }

            if (obj != null && obj.ToString() == ObjFileName)
            {
                obj = fileSavePath;
            }
            WaitBox.Show("正在导出到" + fileType + "...", doExport, obj);

            return ExportSecurity(logItem, ref fileSavePath, exportCond, bRemindOpen);
        }

        #region 导出鉴权&安全导出
        /// <summary>
        /// 鉴权是否有权导出和是否需压缩导出提交导出原因，
        /// 并返回保存路径和其他安全导出设置(与下文ExportSecurity方法组合使用)
        /// </summary>
        /// <param name="fileType">FileSimpleType(fileName为已自定义的文件夹或“文件名+文件格式”时，该值取null)</param>
        /// <param name="fileName">保存路径（压缩加密导出时返回路径为临时保存路径）</param>
        /// <param name="cond">安全导出设置</param>
        /// <returns></returns>
        public static bool GetExportPermit(LogInfoItem logItem, FileSimpleType fileType
            , ref string fileName, out ExportSecurityCondition cond)
        {
            cond = null;
#if PermissionControl_DataExport

            FuncExportPermit exportPermit = ExportFuncResultManager.GetInstance().GetFuncExportPermit(logItem);
            if (!exportPermit.IsCanExportResult)
            {
                ExportFuncResultManager.GetInstance().ClearData();
                DevExpress.XtraEditors.XtraMessageBox.Show("您没有导出该功能结果的权限，请联系管理员！");
                return false;
            }

            if (exportPermit.IsNeedExportCause || exportPermit.IsExportToZip)
            {
                ExportSecuritySetDlg exportdlg = new ExportSecuritySetDlg(fileType, exportPermit);
                if (exportdlg.ShowDialog() == DialogResult.OK)
                {
                    cond = exportdlg.GetCondition();

                    if (exportPermit.IsExportToZip)
                    {//压缩加密导出

                        string folderProvisionalPath = getProvisionalFolderPath();//临时保存文件夹
                        if (fileType == null)
                        {
                            if (string.IsNullOrEmpty(fileName))
                            {
                                DevExpress.XtraEditors.XtraMessageBox.Show("保存路径为空！");
                                return false;
                            }
                            cond.FileType = System.IO.Path.GetExtension(fileName);
                            cond.FileTitleName = System.IO.Path.GetFileNameWithoutExtension(fileName);
                            if (string.IsNullOrEmpty(cond.FileType))
                            {
                                cond.FolderPath = fileName;
                                fileName = folderProvisionalPath;//文件临时完整路径
                            }
                            else
                            {
                                cond.FolderPath = System.IO.Path.GetDirectoryName(fileName);
                                fileName = System.IO.Path.Combine(folderProvisionalPath, cond.FileName);//文件临时完整路径
                            }
                        }
                        else
                        {
                            fileName = System.IO.Path.Combine(folderProvisionalPath, cond.FileName);//文件临时完整路径
                        }
                    }
                    else
                    {
                        if (fileType != null)
                        {
                            fileName = System.IO.Path.Combine(cond.FolderPath, cond.FileName);//文件完整路径
                        }
                    }
                    return true;
                }
                else
                {
                    ExportFuncResultManager.GetInstance().ClearData();
                    return false;
                }
            }
#endif
            if (fileType != null)
            {
                SaveFileDialog dlg = new SaveFileDialog();
                dlg.RestoreDirectory = true;
                dlg.Filter = fileType.FilterStr;
                dlg.FilterIndex = fileType.FilterIndex + 1;
                dlg.FileName = ExportFuncResultManager.GetInstance().GetSubFuncName();
                if (dlg.ShowDialog() == DialogResult.OK)
                {
                    fileName = dlg.FileName;
                    return true;
                }
                return false;
            }
            else
            {
                return true;
            }
        }

        /// <summary>
        /// 安全导出文件（与上文GetExportPermit方法组合使用）
        /// </summary>
        /// <param name="logItem"></param>
        /// <param name="fileSavePath"></param>
        /// <param name="exportCond"></param>
        /// <returns></returns>
        public static bool ExportSecurity(LogInfoItem logItem, ref string fileSavePath
            , ExportSecurityCondition exportCond, bool bRemindOpen = false)
        {
#if PermissionControl_DataExport
            bool hasExport = false;
            setBackgroundPic(fileSavePath);

            try
            {
                FuncExportPermit exportPermit = setExportPermit(logItem, fileSavePath, exportCond);
                if (exportPermit.IsExportToZip)
                {
                    try
                    {
                        if (exportCond == null)
                        {
                            return false;
                        }

                        fileSavePath = exportToZip(fileSavePath, exportCond);
                        hasExport = true;
                    }
                    catch
                    {
                        hasExport = false;
                        DevExpress.XtraEditors.XtraMessageBox.Show("导出压缩文件出错！");
                    }
                }
                else
                {
                    hasExport = true;
                }
            }
            catch
            {
                hasExport = false;
            }
            if (hasExport)
            {
                doSometingAfterExport(bRemindOpen, fileSavePath);
            }

            return hasExport;
#else
            doSometingAfterExport(bRemindOpen, fileSavePath);
            return true;
#endif
        }

#if PermissionControl_DataExport
        private static string exportToZip(string fileSavePath, ExportSecurityCondition exportCond)
        {
            string folderZipPath = System.IO.Path.GetDirectoryName(exportCond.ZipSavePath);
            if (!Directory.Exists(folderZipPath))
            {
                Directory.CreateDirectory(folderZipPath);
            }

            string folderProvisionalPath;
            if (fileSavePath.Contains(".shp"))
            {//Shp文件的保存路径同时包含多个文件,需压缩整个文件夹
                folderProvisionalPath = System.IO.Path.GetDirectoryName(fileSavePath);
                ZipClass.ZipFileMain(folderProvisionalPath, exportCond.ZipSavePath, exportCond.ZipPassword);
            }
            else if (System.IO.Path.HasExtension(fileSavePath))
            {//单个文件
                folderProvisionalPath = System.IO.Path.GetDirectoryName(fileSavePath);
                ZipClass.ZipFileSecurity(fileSavePath, exportCond.ZipSavePath, exportCond.ZipPassword);
            }
            else
            {//文件夹
                folderProvisionalPath = fileSavePath;
                ZipClass.ZipFileMain(folderProvisionalPath, exportCond.ZipSavePath, exportCond.ZipPassword);
            }

            fileSavePath = exportCond.ZipSavePath;
            if (folderProvisionalPath.Contains("result_"))
            {
                System.IO.Directory.Delete(folderProvisionalPath, true);//压缩加密后删除临时文件夹
            }

            return fileSavePath;
        }

        private static FuncExportPermit setExportPermit(LogInfoItem logItem, string fileSavePath, ExportSecurityCondition exportCond)
        {
            FuncExportPermit exportPermit = ExportFuncResultManager.GetInstance().GetFuncExportPermit(logItem);
            if (exportPermit.IsNeedExportLog)
            {
                string exportCause;
                if (exportCond == null)
                {
                    exportCause = "";
                }
                else
                {
                    exportCause = exportCond.ExportCause;
                }
                ExportFuncResultManager.GetInstance().SendExportLog(fileSavePath, logItem, exportCause);
            }

            return exportPermit;
        }

        /// <summary>
        /// 获取临时保存路径
        /// </summary>
        /// <returns></returns>
        private static string getProvisionalFolderPath()
        {
            string folderProvisionalPath = System.Windows.Forms.Application.StartupPath
                + "\\result_" + (new Random().Next(100000, 999999));

            if (!Directory.Exists(folderProvisionalPath))
            {
                Directory.CreateDirectory(folderProvisionalPath);
                File.SetAttributes(folderProvisionalPath, FileAttributes.Hidden);//隐藏临时文件夹
            }
            return folderProvisionalPath;
        }
#endif
        #endregion

        protected static void setBackgroundPic(string strFilePath)
        {
            try
            {
                string picPath = System.Windows.Forms.Application.StartupPath + "\\images\\shuiyin.png";
                if (!File.Exists(picPath) || string.IsNullOrEmpty(strFilePath))
                {
                    return;
                }

                List<string> excelFilenameList = getExcelFileNames(strFilePath);
                if (excelFilenameList.Count < 1)
                {
                    return;
                }

                Microsoft.Office.Interop.Excel.Application app = new Microsoft.Office.Interop.Excel.Application();
                try
                {
                    app.Visible = false;
                    app.UserControl = true;
                    foreach (string excelName in excelFilenameList)
                    {
                        Workbook wb = app.Application.Workbooks.Open(excelName, System.Reflection.Missing.Value, false);
                        foreach (Worksheet wSheet in wb.Sheets)
                        {
                            wSheet.SetBackgroundPicture(picPath);
                        }
                        wb.Save();
                        wb.Close();
                    }
                }
                finally
                {
                    app.Quit();
                    ExcelHelper.KillCurrentExcel(app);
                    //GC.Collect();
                }
            }
            catch
            {
                //continue
            }
        }
        private static List<string> getExcelFileNames(string strFilePath)
        {
            List<string> excelFilenameList = new List<string>();
            string strExtension = System.IO.Path.GetExtension(strFilePath);
            if (string.IsNullOrEmpty(strExtension))
            {
                string[] filenames = Directory.GetFiles(strFilePath, "*.xls", SearchOption.AllDirectories);
                excelFilenameList.AddRange(filenames);
            }
            else if (FileSimpleTypeHelper.Excel.FileTypeList.Contains(strExtension))
            {
                excelFilenameList.Add(strFilePath);
            }
            return excelFilenameList;
        }
        private static void doSometingAfterExport(bool bRemindOpen, string filePath)
        {
            if (bRemindOpen && DialogResult.Yes == XtraMessageBox.Show("文件保存完毕，是否现在打开?", "打开文件", MessageBoxButtons.YesNo))
            {
                try
                {
                    System.Diagnostics.Process.Start(filePath);
                }
                catch
                {
                    XtraMessageBox.Show("打开失败!\r\n文件名:" + filePath);
                }
            }
        }

    }
    public static class FileSimpleTypeHelper
    {
        public static readonly FileSimpleType Excel = new FileSimpleType(FilterHelper.Excel);
        public static readonly FileSimpleType Csv = new FileSimpleType(FilterHelper.Csv);
        public static readonly FileSimpleType Txt = new FileSimpleType(FilterHelper.Txt);
        public static readonly FileSimpleType Word = new FileSimpleType(FilterHelper.Word);
        public static readonly FileSimpleType Shp = new FileSimpleType(FilterHelper.Shp);
        public static readonly FileSimpleType Kml = new FileSimpleType(FilterHelper.Kml);
        public static readonly FileSimpleType CAP = new FileSimpleType(FilterHelper.CAP);
        public static readonly FileSimpleType Zip = new FileSimpleType(FilterHelper.Zip);
    }
    public class FileSimpleType
    {
        public FileSimpleType(string filter)
        {
            this.FilterStr = filter;
            init(filter);
        }
        private void init(string filter)
        {
            switch (filter)
            {
                case FilterHelper.Excel:
                    this.FilterIndex = 1;//默认xlsx
                    this.Name = "Excel";
                    this.FileTypeList = new List<string> { ".xls", ".xlsx" };
                    break;

                case FilterHelper.Csv:
                    this.Name = "Csv";
                    this.FileTypeList = new List<string> { ".csv" };
                    break;

                case FilterHelper.Txt:
                    this.Name = "Txt";
                    this.FileTypeList = new List<string> { ".txt" };
                    break;

                case FilterHelper.Word:
                    this.Name = "Word";
                    this.FileTypeList = new List<string> { ".doc", ".docx" };
                    break;

                case FilterHelper.Shp:
                    this.Name = "Shp";
                    this.FileTypeList = new List<string> { ".shp" };
                    break;

                case FilterHelper.Kml:
                    this.Name = "Kml";
                    this.FileTypeList = new List<string> { ".kml" };
                    break;

                case FilterHelper.CAP:
                    this.Name = "CAP";
                    this.FileTypeList = new List<string> { ".cap" };
                    break;

                case FilterHelper.Zip:
                    this.Name = "Zip";
                    this.FileTypeList = new List<string> { ".zip", ".rar" };
                    break;
                default:
                    break;
            }
        }
        public string Name { get; set; }

        public List<string> FileTypeList { get; set; }
        public string FilterStr { get; set; }
        public int FilterIndex { get; set; }
        public override string ToString()
        {
            return Name;
        }
    }
}
