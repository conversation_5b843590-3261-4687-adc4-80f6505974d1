﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.Net;
using System.Windows.Forms;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    class NRHandOverPingPangAna : DIYAnalyseByCellBackgroundBaseByFile
    {
        //protected List<DTFileDataManager> fileManagers = new List<DTFileDataManager>();//结果列表重新统计用到
        protected NRPingPangAnalyzer analyzer;

        protected static readonly object lockObj = new object();
        private static NRHandOverPingPangAna instance = null;
        public static NRHandOverPingPangAna GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRHandOverPingPangAna(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        protected NRHandOverPingPangAna(MainModel mModel)
            : base(mModel)
        {
            analyzer = new NRPingPangAnalyzer();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
            Columns = NRTpHelper.InitBaseReplayParamBackground(true, true);
        }

        public override string Name => "NR乒乓切换";

        public NRPingPangAnalyzer Analyzer => analyzer;

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35023, this.Name);
        }

        NRPingPangSettingForm dlg;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                FilterEventByRegion = true;
                return true;
            }
            if (dlg == null)
            {
                dlg = new NRPingPangSettingForm();
            }
            FilterEventByRegion = false;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                this.Analyzer.PingPangCond = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void getReadyBeforeQuery()
        {
            //fileManagers.Clear();
            analyzer.Init();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager dtFile in MainModel.DTDataManager.FileDataManagers)
            {
                //重新分析会导致文件信息不被释放,内存一直增加,经常溢出,暂时先取消该功能
                //if (!mainModel.IsBackground)
                //{
                //    fileManagers.Add(dtFile);
                //}
                analyzer.Analyze(dtFile);
            }
        }

        protected override void getResultsAfterQuery()
        {
            analyzer.SetSN();
        }

        protected override void fireShowForm()
        {
            NRPingPangResultForm frm = MainModel.CreateResultForm(typeof(NRPingPangResultForm)) as NRPingPangResultForm;
            frm.FillData(analyzer.Results, this);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }

        // 在结果窗口里面点击重新统计
        //public void DoQuery()
        //{
        //    if (fileManagers == null || fileManagers.Count <= 0)
        //    {
        //        base.query();
        //        return;
        //    }

        //    WaitTextBox.Show("正在重新分析...", ReAnalyzeInThread);
        //    fireShowForm();
        //}

        //private void ReAnalyzeInThread()
        //{
        //    try
        //    {
        //        analyzer.Analyze(fileManagers);
        //    }
        //    catch (Exception ex)
        //    {
        //        System.Windows.Forms.MessageBox.Show(ex.Message + Environment.NewLine + ex.StackTrace, this.Name,
        //            System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
        //    }
        //    finally
        //    {
        //        System.Threading.Thread.Sleep(300);
        //        WaitTextBox.Close();
        //    }
        //}
    }
}
