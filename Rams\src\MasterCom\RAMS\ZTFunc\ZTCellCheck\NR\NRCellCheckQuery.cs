﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTCellCheck;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRCellCheckQuery : DIYAnalyseByFileBackgroundBase
    {
        FuncConditionNR funcCond = null;
        Dictionary<NRCell, CellCheckInfoNR> cellCheckDic = null;

        public NRCellCheckQuery()
            : base(MainModel.GetInstance())
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(false, true);
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        public override string Name
        {
            get
            {
                return "小区指标体检";
            }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 35000, 35017, this.Name);
        }

        protected override bool getCondition()
        {
            CellCheckConditionNRDlg dlg = new CellCheckConditionNRDlg();
            dlg.SetCondition(funcCond);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                dlg.Dispose();
                return false;
            }
            funcCond = dlg.GetCondition();
            cellCheckDic = new Dictionary<NRCell, CellCheckInfoNR>();
            dlg.Dispose();
            return true;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (TestPoint tp in file.TestPoints)
                {
                    bool isValid = isValidTestPoint(tp);
                    if (!isValid)
                    {
                        continue;
                    }

                    NRCell cell = tp.GetMainCell_NR();
                    float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp, true);
                    if (cell == null || cell.Antennas == null || cell.Antennas.Count == 0 || rsrp == null)
                    {
                        continue;
                    }
                    CellCheckInfoNR cellChk = getCellCheckItem(cell);
                    cellChk.AddTestPoint();
                             
                    //弱覆盖
                    bool isWeakCover = funcCond.IsWeakCover((float)rsrp);
                    cellChk.AddWeakCoverInfo(isWeakCover, (float)rsrp);

                    //过覆盖
                    double distance = tp.Distance2(cell.Longitude, cell.Latitude);
                    bool isCoverLap = funcCond.IsCoverLap((float)rsrp, distance, cellChk.CellCvrDisMax);
                    cellChk.AddCoverLapInfo(isCoverLap, (float)rsrp, distance);

                    //MOD3
                    dealMod(tp, rsrp, cellChk);

                    //重叠覆盖
                    dealMultiCover(tp, rsrp, cellChk);
                }
            }
        }

        private CellCheckInfoNR getCellCheckItem(NRCell cell)
        {
            CellCheckInfoNR item;
            if (!cellCheckDic.TryGetValue(cell, out item))
            {
                item = new CellCheckInfoNR(cell, funcCond);
                cellCheckDic.Add(cell, item);
            }
            return item;
        }

        private void dealMod(TestPoint tp, float? rsrp, CellCheckInfoNR cellChk)
        {
            int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
            if (pci == null)
            {
                return;
            }

            dealModDetailInfo((int)pci, (float)rsrp, tp, funcCond.Mod3RSRP, funcCond.Mod3Diff, cellChk.Mod3, 3);
            dealModDetailInfo((int)pci, (float)rsrp, tp, funcCond.Mod4RSRP, funcCond.Mod4Diff, cellChk.Mod4, 4);
            dealModDetailInfo((int)pci, (float)rsrp, tp, funcCond.Mod6RSRP, funcCond.Mod6Diff, cellChk.Mod6, 6);
        }

        private void dealModDetailInfo(int pci, float rsrp, TestPoint tp, float conditioRsrp, float conditionDiff, CellCheckInfoNR.ModInterference mod, int modType)
        {
            if(rsrp < conditioRsrp)
            {
                return;
            }

            int modValue = getModValue(pci, modType);
            int modCellCnt = 0;
            for (int i = 0; i < 16; i++)
            {
                NRTpHelper.NRNCellType type = NRTpHelper.NrTpManager.GetNCellType(tp, i);
                if (type != NRTpHelper.NRNCellType.NCELL)
                {
                    continue;
                }
                float? nRsrp = NRTpHelper.NrTpManager.GetNCellRsrp(tp, i);
                if (nRsrp == null)
                {
                    break;
                }
                int? nPci = (int?)NRTpHelper.NrTpManager.GetNPCI(tp, i);
                if (nPci == null)
                {
                    break;
                }
                int nModValue = getModValue((int)nPci, modType);
                addModCellCnt(rsrp, (float)nRsrp, modValue, nModValue, conditionDiff, ref modCellCnt);
            }
            mod.AddModInfo(modCellCnt);
        }

        private int getModValue(int pci, int mod)
        {
            int res = pci % mod;
            return res;
        }

        private void addModCellCnt(float rsrp, float nRsrp, int pss, int nPss, float conditionDiff, ref int modCellCnt)
        {
            float diff = rsrp - nRsrp;
            if ((nRsrp >= rsrp || diff >= conditionDiff) && pss == nPss)
            {
                modCellCnt++;
            }
        }

        private void dealMultiCover(TestPoint tp, float? rsrp, CellCheckInfoNR cellChk)
        {
            int multiCoverCnt = 1;//自身为1
            for (int i = 0; i < 16; i++)
            {
                NRTpHelper.NRNCellType type = NRTpHelper.NrTpManager.GetNCellType(tp, i);
                if (type != NRTpHelper.NRNCellType.NCELL)
                {
                    continue;
                }
                float? nRsrp = NRTpHelper.NrTpManager.GetNCellRsrp(tp, i);
                if (nRsrp == null)
                {
                    break;
                }
               
                addMultiCoverInfo(rsrp, ref multiCoverCnt, nRsrp);
            }
            cellChk.AddMultiCoverInfo(multiCoverCnt);
        }

        private void addMultiCoverInfo(float? rsrp, ref int multiCoverCnt, float? nRsrp)
        {
            float diff = (float)rsrp - (float)nRsrp;
            if (nRsrp >= rsrp || diff >= funcCond.MultiCoverDiff)
            {
                multiCoverCnt++;
            }
        }

        protected override void getResultsAfterQuery()
        {
            UltraSiteQueryNR qry = new UltraSiteQueryNR();
            qry.SetQueryCondition(condition);
            qry.ShowSettingDlg = false;
            qry.UltraSiteCondition = funcCond.UltraSiteCondition;
            qry.Query();

            Dictionary<ICell, List<UltraSiteInfo>> cellDic = qry.Result;
            Dictionary<string, List<UltraSiteInfo>> cellNameDic = new Dictionary<string, List<UltraSiteInfo>>();
            foreach (ICell cell in cellDic.Keys)
            {
                cellNameDic[cell.Name] = cellDic[cell];
            }

            foreach (NRCell cell in cellCheckDic.Keys)
            {
                CellCheckInfoNR info = cellCheckDic[cell];
                if (cellNameDic.ContainsKey(cell.Name))
                {
                    foreach (UltraSiteInfo item in cellNameDic[cell.Name])
                    {
                        if (item is UltraFarSiteInfo)
                        {
                            info.IsUltraFar = true;
                        }
                        else if (item is UltraHighSiteInfo)
                        {
                            info.IsUltraHigh = true;
                        }
                        else if (item is UltraNearSiteInfo)
                        {
                            info.IsUltraNear = true;
                        }
                    }
                }
                info.MakeSummary();
            }
        }

        protected override void fireShowForm()
        {
            List<CellCheckInfoNR> cells = new List<CellCheckInfoNR>(cellCheckDic.Values);
            CellCheckNRResultForm form = MainModel.GetObjectFromBlackboard(typeof(CellCheckNRResultForm)) as CellCheckNRResultForm;
            if (form == null || form.IsDisposed)
            {
                form = new CellCheckNRResultForm();
            }
            form.FillData(cells);
            form.Owner = MainModel.MainForm;
            form.Visible = true;
            form.BringToFront();
        }
    }

    public class CellCheckInfoNR
    {
        public CellCheckInfoNR(NRCell cell, FuncConditionNR condition)
        {
            this.Cell = cell;
            double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(cell, condition.CoverSiteNum);
            CellCvrDisMax = radiusOfCell * condition.CvrDisFactorMax;
        }

        public NRCell Cell{ get; private set; }
        public string CellName { get { return Cell.Name; } }
        public int ARFCN { get { return Cell.SSBARFCN; } }
        public int PCI { get { return Cell.PCI; } }
        public int TAC { get { return Cell.TAC; } }
        public long NCI { get { return Cell.NCI; } }

        public int TotalTpCnt { get; private set; }
        public void AddTestPoint()
        {
            TotalTpCnt++;
        }

        public double WeakCoverRate { get; private set; }
        //float weakCoverTotalRSRP = 0;
        int weakCoverCnt = 0;
        //float noneWeakCoverTotalRSRP = 0;
        int noneWeakCoverCnt = 0;
        public void AddWeakCoverInfo(bool isWeakCover, float rsrp)
        {
            if (isWeakCover)
            {
                //weakCoverTotalRSRP += rsrp;
                weakCoverCnt++;
            }
            else
            {
                //noneWeakCoverTotalRSRP += rsrp;
                noneWeakCoverCnt++;
            }
        }

        public double CoverLapRate { get; private set; }
        public double CellCvrDisMax { get; private set; }
        //float coverLapTotalRSRP = 0;
        int coverLapCnt = 0;
        //double coverLapTotalDis = 0;
        //float noneCoverLapTotalRSRP = 0;
        int noneCoverLapCnt = 0;
        //double noneCoverLapTotalDis = 0;
        public void AddCoverLapInfo(bool isCoverLap, float rsrp, double distance)
        {
            if (isCoverLap)
            {
                //coverLapTotalRSRP += rsrp;
                //coverLapTotalDis += distance;
                coverLapCnt++;
            }
            else
            {
                //noneCoverLapTotalRSRP += rsrp;
                //noneCoverLapTotalDis += distance;
                noneCoverLapCnt++;
            }
        }

        public class ModInterference
        {
            public double AvgMod { get; set; }
            public int ModTotalCellCnt { get; set; }
            public int ModTotalTpCnt { get; set; }

            public void AddModInfo(int modCellCnt)
            {
                ModTotalTpCnt++;
                if (modCellCnt > 0)
                {
                    ModTotalCellCnt += modCellCnt;
                }
            }

            public void MakeSummary()
            {
                if (ModTotalTpCnt > 0)
                {
                    AvgMod = Math.Round(1.0 * ModTotalCellCnt / ModTotalTpCnt, 2);
                }
            }
        }

        public ModInterference Mod3 { get; set; } = new ModInterference();
        public ModInterference Mod4 { get; set; } = new ModInterference();
        public ModInterference Mod6 { get; set; } = new ModInterference();

        //public double AvgMod3 { get; private set; }
        //int mod3TotalCellCnt = 0;
        //int mod3TotalTpCnt = 0;
        //int mod3NoneTpCnt = 0;
        //internal void AddMod3Info(int mod3CellCnt)
        //{
        //    mod3TotalTpCnt++;
        //    if (mod3CellCnt == 0)
        //    {
        //        mod3NoneTpCnt++;
        //    }
        //    else
        //    {
        //        mod3TotalCellCnt += mod3CellCnt;
        //    }
        //}

        public double AvgMultiCover { get; private set; }
        int multiCoverTotalCellCnt = 0;
        int multiCovertTotalTpCnt = 0;
        internal void AddMultiCoverInfo(int multiCoverCnt)
        {
            multiCoverTotalCellCnt += multiCoverCnt;
            multiCovertTotalTpCnt++;
        }

        public UltraFarSite FarSite { get; set; }
        public bool IsUltraFar { get; set; }
        public string UltraFarDesc { get; private set; }

        public UltraHighSite HighSite { get; set; }
        public bool IsUltraHigh { get; set; }
        public string UltraHighDesc { get; private set; }

        public UltraNearSite NearSite { get; set; }
        public bool IsUltraNear { get; set; }
        public string UltraNearDesc { get; private set; }

        internal void MakeSummary()
        {
            WeakCoverRate = Math.Round(100.0 * weakCoverCnt / (weakCoverCnt + noneWeakCoverCnt), 2);
            CoverLapRate = Math.Round(100.0 * coverLapCnt / (coverLapCnt + noneCoverLapCnt), 2);
            //AvgMod3 = Math.Round(1.0 * mod3TotalCellCnt / mod3TotalTpCnt, 2);
            Mod3.MakeSummary();
            Mod4.MakeSummary();
            Mod6.MakeSummary();

            AvgMultiCover = Math.Round(1.0 * multiCoverTotalCellCnt / multiCovertTotalTpCnt, 2);

            UltraFarDesc = getDesc(IsUltraFar);
            UltraHighDesc = getDesc(IsUltraHigh);
            UltraNearDesc = getDesc(IsUltraNear);
        }

        private string getDesc(bool flag)
        {
            if (flag)
            {
                return "是";
            }
            else
            {
                return "否";
            }
        }
    }

    public class NRCellCheckQueryByFile : NRCellCheckQuery
    {
        private static NRCellCheckQueryByFile instance = null;
        public static NRCellCheckQueryByFile GetInstance()
        {
            if (instance == null)
            {
                instance = new NRCellCheckQueryByFile();
            }
            return instance;
        }

        public override string Name
        {
            get { return "小区指标体检(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
    }
}
