﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using System.Data.SqlClient;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CfgSettingDlg : BaseForm
    {
        private static CfgSettingDlg instance = null;
        public static CfgSettingDlg GetInstance()
        {
            if (instance == null)
            {
                instance = new CfgSettingDlg();
            }
            return instance;
        }
        public CfgSettingDlg()
        {
            InitializeComponent();

            this.dateTimePickerBeginTime.Value = DateTime.Now.AddDays(-2);
            this.cbxGridSize.Items.Add("10*10");
            this.cbxGridSize.Items.Add("20*20");
            this.cbxGridSize.Items.Add("40*40");
            this.cbxGridSize.Items.Add("80*80");
            this.cbxGridSize.SelectedIndex = 2;

            this.cbxRxLev.Items.Add("-70 dBm");
            this.cbxRxLev.Items.Add("-80 dBm");
            this.cbxRxLev.Items.Add("-85 dBm");
            this.cbxRxLev.Items.Add("-90 dBm");
            this.cbxRxLev.Items.Add("-94 dBm");
            this.cbxRxLev.Items.Add("-95 dBm");
            this.cbxRxLev.SelectedIndex = 1;
        }

        public int BeginTime
        {
            get 
            {
                DateTime dtime = dateTimePickerBeginTime.Value.Date;
                return (int)(JavaDate.GetMilliseconds(dtime) / 1000);
            }
        }

        public int EndTime
        {
            get 
            {
                DateTime dtime = dateTimePickerEndTime.Value.Date.AddHours(23);
                return (int)(JavaDate.GetMilliseconds(dtime) / 1000); 
            }
        }

        public int gridSizeLon
        {
            get
            {
                if (cbxGridSize.SelectedIndex==0)
                {
                    return 1000;
                }
                else if (cbxGridSize.SelectedIndex==1)
                {
                    return 2000;
                }
                else if (cbxGridSize.SelectedIndex==2)
                {
                    return 4000;
                }
                else if (cbxGridSize.SelectedIndex==3)
                {
                    return 8000;
                }
                return 0;
            }
        }

        public int gridSizeLat
        {
            get
            {
                if (cbxGridSize.SelectedIndex == 0)
                {
                    return 900;
                }
                else if (cbxGridSize.SelectedIndex == 1)
                {
                    return 1800;
                }
                else if (cbxGridSize.SelectedIndex == 2)
                {
                    return 3600;
                }
                else if (cbxGridSize.SelectedIndex == 3)
                {
                    return 7200;
                }
                return 0;
            }
        }

        public int Rxlev
        {
            get
            {
                if(cbxRxLev.SelectedIndex==0)
                {
                    VisibleRxlevTopN = 1;
                    return -70;
                }
                if (cbxRxLev.SelectedIndex == 1)
                {
                    VisibleRxlevTopN = 2;
                    return -80;
                }
                else if (cbxRxLev.SelectedIndex == 2)
                {
                    VisibleRxlevTopN = 3;
                    return -85;
                }
                else if (cbxRxLev.SelectedIndex == 3)
                {
                    VisibleRxlevTopN = 4;
                    return -90;
                }
                else if (cbxRxLev.SelectedIndex == 4)
                {
                    VisibleRxlevTopN = 5;
                    return -94;
                }
                else if (cbxRxLev.SelectedIndex == 5)
                {
                    VisibleRxlevTopN = 6;
                    return -95;
                }
                return -999;
            }
        }

        /// <summary>
        /// 显示前N个电平的门限栅格数据结果
        /// </summary>
        public int VisibleRxlevTopN { get; set; }

        public bool IsTaDownWard
        {
            get
            {
                if (rbtnTaDownWard.Checked)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 测试电平：测试栅格电平值，1最大，2平均，3最小
        /// </summary>
        public int TestValue
        {
            get
            {
                if (radioButtonTestValueMax.Checked)
                {
                    return 1;
                }
                else if (radioButtonTestValueAvg.Checked)
                {
                    return 2;
                }
                else if (radioButtonTestValueMin.Checked)
                {
                    return 3;
                }
                return 1;
            }
        }

        /// <summary>
        /// 1.仅保存正常TA;2.不过滤直放站;3.截断空值TA
        /// </summary>
        public int RepeaterBts
        {
            get
            {
                if (rbtTa1.Checked)
                {
                    return 1;
                }
                else if (rbtTa2.Checked)
                {
                    return 2;
                }
                else if (rbtTa3.Checked)
                {
                    return 3;
                }
                return 1;
            }
        }

        /// <summary>
        /// ABIS-MR数据：若需要添加ABIS中的MR则置为TRUE
        /// </summary>
        public bool IsAddAbisMr
        {
            get 
            {
                if (chkAbisMR.Checked)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }

        List<string> displayNameList = new List<string>();
        List<int> queryIDList = new List<int>();
        string strConn = "";
        string strTableNameOfConditon = "";
        string strTableNameOfMap = "";
        string strTableNameOfResult = "";
        SqlConnection sqlConn = null;
        /// <summary>
        /// 窗体加载时添加历史查询条件
        /// </summary>
        private void CfgSettingDlg_Load(object sender, EventArgs e)
        {
            displayNameList.Clear();
            queryIDList.Clear();

            //strConn = databaseOpen();
            //if (ZTGSMCellEmulateCoverAnaByMR.startGSM == true)
            //{
            //    strTableNameOfConditon = "tb_mr_cover_history_data_conditon";
            //    strTableNameOfMap = "tb_mr_cover_history_data_map_";
            //    strTableNameOfResult = "tb_mr_cover_history_data_result";
            //    ZTGSMCellEmulateCoverAnaByMR.startGSM = false;
            //}
            //else if (ZTTDCellEmulateCoverAnaByMR.startTD == true)
            //{
            //    strTableNameOfConditon = "tb_tdmr_cover_history_data_conditon";
            //    strTableNameOfMap = "tb_tdmr_cover_history_data_map_";
            //    strTableNameOfResult = "tb_tdmr_cover_history_data_result";
            //    ZTTDCellEmulateCoverAnaByMR.startTD = false;
            //}
            //string strSelectTable = "select iqueryid, strQueryname from " + strTableNameOfConditon + " order by istime";
            //DataSet ds = databaseControl(strSelectTable, strConn);
            //for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
            //{
            //    string[] querynameArr = ds.Tables[0].Rows[i]["strQueryname"].ToString().Split('_');
            //    string beginTime = querynameArr[0];
            //    string endTime = querynameArr[1];
            //    string queryname = querynameArr[2];

            //    DateTime originalTime = DateTime.Parse("1970-1-1 8:00:00");
            //    DateTime dBegin = new DateTime();
            //    DateTime dEnd = new DateTime();
            //    dBegin = originalTime.AddSeconds(Int32.Parse(beginTime));
            //    dEnd = originalTime.AddSeconds(Int32.Parse(endTime));
            //    beginTime = dBegin.ToLongDateString();
            //    endTime = dEnd.ToLongDateString();
            //    //beginTime = dBegin.ToString("yyyyMMdd");
            //    //endTime = dEnd.ToString("yyyyMMdd");

            //    displayNameList.Add(beginTime + "～" + endTime + "：" + queryname);
            //    queryIDList.Add(Int32.Parse(ds.Tables[0].Rows[i]["iqueryid"].ToString()));
            //}
            //lstConditon.Items.Clear();
            //foreach (string queryname in displayNameList)
            //{
            //    lstConditon.Items.Add(queryname);
            //}
        }

        int queryID = 0;
        /// <summary>
        /// 单击ListBox中的任意一项，显示对应的历史查询条件
        /// </summary>
        private void lstConditon_Click(object sender, EventArgs e)
        {
            if (this.lstConditon.SelectedItems.Count > 0)
            {
                queryID = queryIDList[lstConditon.SelectedIndex];

                string strSelectTable = "select * from " + strTableNameOfConditon + " where iqueryid=" + queryID;

                DataSet ds = databaseControl(strSelectTable, strConn);

                #region 还原历史查询条件
                DateTime originalTime = DateTime.Parse("1970-1-1 8:00:00");
                DateTime dBegin = originalTime.AddSeconds(Int32.Parse(ds.Tables[0].Rows[0]["istime"].ToString()));
                DateTime dend = originalTime.AddSeconds(Int32.Parse(ds.Tables[0].Rows[0]["ietime"].ToString()));
                dateTimePickerBeginTime.Value = dBegin;
                dateTimePickerEndTime.Value = dend;
                cbxGridSize.SelectedIndex = Int32.Parse(ds.Tables[0].Rows[0]["igridSize"].ToString());
                string isTaDownWard = ds.Tables[0].Rows[0]["bitIsTaDownWard"].ToString();
                switch (isTaDownWard)
                {
                    case "False": rBtnTaUpWard.Checked = true; break;
                    case "True": rbtnTaDownWard.Checked = true; break;
                    default: break;
                }
                int rxlev = Int32.Parse(ds.Tables[0].Rows[0]["irxlev"].ToString());
                switch (rxlev)
                {
                    case -70: cbxRxLev.SelectedIndex = 0; break;
                    case -80: cbxRxLev.SelectedIndex = 1; break;
                    case -85: cbxRxLev.SelectedIndex = 2; break;
                    case -90: cbxRxLev.SelectedIndex = 3; break;
                    case -94: cbxRxLev.SelectedIndex = 4; break;
                    case -95: cbxRxLev.SelectedIndex = 5; break;
                    default: break;
                }
                string isAbisMR = ds.Tables[0].Rows[0]["bitIsAbisMR"].ToString();
                switch (isAbisMR)
                {
                    case "False": chkAbisMR.Checked = false; break;
                    case "True": chkAbisMR.Checked = true; break;
                    default: break;
                }
                string testValue = ds.Tables[0].Rows[0]["strTestValue"].ToString();
                switch (testValue)
                {
                    case "最大": radioButtonTestValueMax.Checked = true; break;
                    case "平均": radioButtonTestValueAvg.Checked = true; break;
                    case "最小": radioButtonTestValueMin.Checked = true; break;
                    default: break;
                }
                string repeaterDeal = ds.Tables[0].Rows[0]["strRepeaterDeal"].ToString();
                switch (repeaterDeal)
                {
                    case "仅保存正常": rbtTa1.Checked = true; break;
                    case "不过滤直放站": rbtTa2.Checked = true; break;
                    case "截断空值TA": rbtTa3.Checked = true; break;
                    default: break;
                }
                #endregion

            }
        }

        /// <summary>
        /// 按历史查询条件加载信息
        /// </summary>
        private void btnLoad_Click(object sender, EventArgs e)
        {
            if (this.lstConditon.SelectedItems.Count > 0)
            {
                setCfgEnable(false);

                RxlevGridLongLat rxlevGridLongLat = null;
                GridLongLat gridLongLat = null;
                CfgSettingDlg dlg = CfgSettingDlg.GetInstance();
                setShowItem(dlg);

                #region 按条件绘制覆盖区域
                string strTableName = strTableNameOfMap + queryID.ToString();
                string strSelectTable = "select * from " + strTableName;

                //数据量过大，改用SqlDataReader读取数据（原先用DataSet）
                SqlCommand com = new SqlCommand();
                com.CommandText = strSelectTable;
                com.CommandType = CommandType.Text;
                com.Connection = sqlConn;
                SqlDataReader dataReader = com.ExecuteReader();

                rxlevGridLongLat = new RxlevGridLongLat();
                while (dataReader.Read())
                {
                    gridLongLat = new GridLongLat();
                    gridLongLat.fltlongitude = float.Parse(dataReader["fltlongitudeRxlev"].ToString());
                    gridLongLat.fltlatitude = float.Parse(dataReader["fltlatitudeRxlev"].ToString());
                    gridLongLat.fbrlongitude = gridLongLat.fltlongitude + 0.0004f;
                    gridLongLat.fbrlatitude = gridLongLat.fltlatitude - 0.00036f;

                    string regionType = dataReader["strRegion"].ToString();
                    setRxlevGridLongLat(rxlevGridLongLat, gridLongLat, regionType);
                }
                MainModel.MainForm.FireCellEmulateMRQueried(queryID);
                #endregion

                #region 按条件还原MR仿真覆盖的结果
                List<CellEmulateCovResult> retList = new List<CellEmulateCovResult>();
                string strSelectTableResult = "select * from " + strTableNameOfResult + " where iqueryid=" + queryID;
                DataSet dsResult = databaseControl(strSelectTableResult, strConn);
                for (int i = 0; i < dsResult.Tables[0].Rows.Count; i++)
                {
                    CellEmulateCovResult covResult = new CellEmulateCovResult();
                    covResult.region = dsResult.Tables[0].Rows[i]["strRegion"].ToString();
                    covResult.totalGrid = Int32.Parse(dsResult.Tables[0].Rows[i]["iTotalGrid"].ToString());
                    covResult.totalSquare = dsResult.Tables[0].Rows[i]["strTotalSquare"].ToString();
                    covResult.coverGrid = Int32.Parse(dsResult.Tables[0].Rows[i]["intCoverGrid"].ToString());
                    covResult.coverSquare = dsResult.Tables[0].Rows[i]["stringCoverSquare"].ToString();
                    covResult.coverRate = dsResult.Tables[0].Rows[i]["stringCoverRate"].ToString();

                    covResult.coverGrid70 = Int32.Parse(dsResult.Tables[0].Rows[i]["intCoverGrid70"].ToString());
                    covResult.coverGrid80 = Int32.Parse(dsResult.Tables[0].Rows[i]["intCoverGrid80"].ToString());
                    covResult.coverGrid85 = Int32.Parse(dsResult.Tables[0].Rows[i]["intCoverGrid85"].ToString());
                    covResult.coverGrid90 = Int32.Parse(dsResult.Tables[0].Rows[i]["intCoverGrid90"].ToString());
                    covResult.coverGrid94 = Int32.Parse(dsResult.Tables[0].Rows[i]["intCoverGrid94"].ToString());
                    covResult.coverGrid95 = Int32.Parse(dsResult.Tables[0].Rows[i]["intCoverGrid95"].ToString());

                    covResult.coverSquare70 = dsResult.Tables[0].Rows[i]["strCoverSquare70"].ToString();
                    covResult.coverSquare80 = dsResult.Tables[0].Rows[i]["strCoverSquare80"].ToString();
                    covResult.coverSquare85 = dsResult.Tables[0].Rows[i]["strCoverSquare85"].ToString();
                    covResult.coverSquare90 = dsResult.Tables[0].Rows[i]["strCoverSquare90"].ToString();
                    covResult.coverSquare94 = dsResult.Tables[0].Rows[i]["strCoverSquare94"].ToString();
                    covResult.coverSquare95 = dsResult.Tables[0].Rows[i]["strCoverSquare95"].ToString();

                    covResult.coverRate70 = dsResult.Tables[0].Rows[i]["strCoverRate70"].ToString();
                    covResult.coverRate80 = dsResult.Tables[0].Rows[i]["strCoverRate80"].ToString();
                    covResult.coverRate85 = dsResult.Tables[0].Rows[i]["strCoverRate85"].ToString();
                    covResult.coverRate90 = dsResult.Tables[0].Rows[i]["strCoverRate90"].ToString();
                    covResult.coverRate94 = dsResult.Tables[0].Rows[i]["strCoverRate94"].ToString();
                    covResult.coverRate95 = dsResult.Tables[0].Rows[i]["strCoverRate95"].ToString();

                    retList.Add(covResult);
                }

                GSMCellEmulateCovMRForm frm = new GSMCellEmulateCovMRForm(MainModel);
                frm.FillData(retList);
                if (!frm.Visible)
                {
                    frm.Show(MainModel.MainForm);
                }

                dataReader.Close();
                sqlConn.Close();
                #endregion

                setCfgEnable(true);
                dlg.Close();
            }
            else
            {
                MessageBox.Show("请选择其中一个历史查询条件", "错误");
            }
        }

        private void setRxlevGridLongLat(RxlevGridLongLat rxlevGridLongLat, GridLongLat gridLongLat, string regionType)
        {
            if (regionType.IndexOf("70") != -1)
            {
                rxlevGridLongLat.Rxlev70GllList.Add(gridLongLat);
                MainModel.RxlevGridLongLat = rxlevGridLongLat;
            }
            else if (regionType.IndexOf("80") != -1)
            {
                rxlevGridLongLat.Rxlev80GllList.Add(gridLongLat);
                MainModel.RxlevGridLongLat = rxlevGridLongLat;
            }
            else if (regionType.IndexOf("85") != -1)
            {
                rxlevGridLongLat.Rxlev85GllList.Add(gridLongLat);
                MainModel.RxlevGridLongLat = rxlevGridLongLat;
            }
            else if (regionType.IndexOf("90") != -1)
            {
                rxlevGridLongLat.Rxlev90GllList.Add(gridLongLat);
                MainModel.RxlevGridLongLat = rxlevGridLongLat;
            }
            else if (regionType.IndexOf("94") != -1)
            {
                rxlevGridLongLat.Rxlev94GllList.Add(gridLongLat);
                MainModel.RxlevGridLongLat = rxlevGridLongLat;
            }
            else if (regionType.IndexOf("95") != -1)
            {
                rxlevGridLongLat.Rxlev95GllList.Add(gridLongLat);
                MainModel.RxlevGridLongLat = rxlevGridLongLat;
            }
            else if (regionType.IndexOf("无覆盖") != -1)
            {
                rxlevGridLongLat.RxlevNonCoverGllList.Add(gridLongLat);
                MainModel.RxlevGridLongLat = rxlevGridLongLat;
            }
        }

        private void setShowItem(CfgSettingDlg dlg)
        {
            CellEmulateShowItem showItem = new CellEmulateShowItem();
            MainModel.cellEmulateShowItem = showItem;
            if (cbxRxLev.SelectedIndex == 0)
            {
                showItem.visibleRxlevTopN = 1;
            }
            if (cbxRxLev.SelectedIndex == 1)
            {
                showItem.visibleRxlevTopN = 2;
            }
            else if (cbxRxLev.SelectedIndex == 2)
            {
                showItem.visibleRxlevTopN = 3;
            }
            else if (cbxRxLev.SelectedIndex == 3)
            {
                showItem.visibleRxlevTopN = 4;
            }
            else if (cbxRxLev.SelectedIndex == 4)
            {
                showItem.visibleRxlevTopN = 5;
            }
            else if (cbxRxLev.SelectedIndex == 5)
            {
                showItem.visibleRxlevTopN = 6;
            }
            showItem.gridSpanLon = dlg.gridSizeLon * 0.0000001;
            showItem.gridSpanLat = dlg.gridSizeLat * 0.0000001;
        }

        /**
        /// <summary>
        /// 打开数据库连接
        /// </summary>
        private string databaseOpen()
        {
            //string serverName = "";
            //string database = "";
            //if (MainModel.DistrictID == 2 || MainModel.DistrictID == 3 || MainModel.DistrictID == 4
            //    || MainModel.DistrictID == 6 || MainModel.DistrictID == 10 || MainModel.DistrictID == 12
            //    || MainModel.DistrictID == 13 || MainModel.DistrictID == 16 || MainModel.DistrictID == 17
            //    || MainModel.DistrictID == 18 || MainModel.DistrictID == 21)
            //{
            //    serverName = "10.243.167.92";
            //    switch (MainModel.DistrictID)
            //    {
            //        case 2: database = "GUANGZHOU"; break;
            //        case 3: database = "SHENZHEN"; break;
            //        case 4: database = "ZHUHAI"; break;
            //        case 6: database = "FOSHAN"; break;
            //        case 10: database = "HUIZHOU"; break;
            //        case 12: database = "DONGGUAN"; break;
            //        case 13: database = "ZHONGSHAN"; break;
            //        case 16: database = "ZHANJIANG"; break;
            //        case 17: database = "MAOMING"; break;
            //        case 18: database = "ZHAOQING"; break;
            //        case 21: database = "JIEYANG"; break;
            //        default: break;
            //    }
            //}
            //else if (MainModel.DistrictID == 1)
            //{
            //    serverName = "10.243.167.167";
            //    database = "GMCC";
            //}
            //else
            //{
            //    serverName = "10.243.167.93";
            //    switch (MainModel.DistrictID)
            //    {
            //        case 5: database = "SHANTOU"; break;
            //        case 7: database = "SHAOGUAN"; break;
            //        case 8: database = "HEYUAN"; break;
            //        case 9: database = "MEIZHOU"; break;
            //        case 11: database = "SHANWEI"; break;
            //        case 14: database = "JIANGMEN"; break;
            //        case 15: database = "YANGJIANG"; break;
            //        case 19: database = "QINGYUAN"; break;
            //        case 20: database = "CHAOZHOU"; break;
            //        case 22: database = "YUNFU"; break;
            //        default: break;
            //    }
            //}
            //string userName = "dtauser";
            //string password = "dtauser";
            //string strConn = "server = " + serverName + "; DataBase =" + database +
            //    "; User id =" + userName + "; PWD =" + password;

            ////本地数据库（调试用）
            ////string serverName = "127.0.0.1";
            ////string database = "GUANGDONG";
            ////string userName = "sa";
            ////string password = "123";
            ////string strConn = "server = " + serverName + "; DataBase =" + database +
            ////    "; User id =" + userName + "; PWD =" + password;

            ////打开数据库链接
            //sqlConn = new SqlConnection(strConn);
            //if (sqlConn.State != ConnectionState.Open)
            //{
            //    sqlConn.Open();
            //}

            return strConn;
        }
        */

        /// <summary>
        /// 数据库操作
        /// </summary>
        private DataSet databaseControl(string strSelectTable, string strConn)
        {
            // 创建数据适配器
            SqlDataAdapter da = new SqlDataAdapter(strSelectTable, strConn);
            // 创建一个数据集对象并填充数据
            DataSet ds = new DataSet();
            da.Fill(ds);

            return ds;
        }

        private void setCfgEnable(bool isEnable)
        {
            dateTimePickerBeginTime.Enabled = isEnable;
            dateTimePickerEndTime.Enabled = isEnable;
            cbxGridSize.Enabled = isEnable;
            rbtnTaDownWard.Enabled = isEnable;
            rBtnTaUpWard.Enabled = isEnable;
            cbxRxLev.Enabled = isEnable;
            chkAbisMR.Enabled = isEnable;
            radioButtonTestValueMax.Enabled = isEnable;
            radioButtonTestValueAvg.Enabled = isEnable;
            radioButtonTestValueAvg.Enabled = isEnable;
            rbtTa1.Enabled = isEnable;
            rbtTa2.Enabled = isEnable;
            rbtTa3.Enabled = isEnable;
            button1.Enabled = isEnable;
            button2.Enabled = isEnable;
            btnLoad.Enabled = isEnable;
        }
    }
}
