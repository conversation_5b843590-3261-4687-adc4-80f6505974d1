﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class BtsAcceptInfo_SX<T, U> where T : ICell
    {
        public BtsAcceptInfo_SX(bool isOutdoor)
        {
            this.IsOutDoor = isOutdoor;
        }

        public SortedDictionary<U, CellAcceptInfoBase_SX> CellAcceptInfoDic { get; set; } = new SortedDictionary<U, CellAcceptInfoBase_SX>();

        public List<TestPoint> TestPointList_Ul { get; set; } = new List<TestPoint>();
        public List<TestPoint> TestPointList_Dl { get; set; } = new List<TestPoint>();
        public List<T> CellList { get; set; }
        public bool IsOutDoor { get; set; }

        private string btsName = "";
        public string BtsName
        {
            get { return btsName.Trim(); }
            set { btsName = value; }
        }
        public int BtsId { get; set; }

        public List<NPOIRow> GetKpiSumRows()
        {
            if (this.IsOutDoor)
            {
                return getOutDoorBtsKpiSumRows();
            }
            else
            {
                return getInDoorBtsKpiSumRows();
            }
        }

        protected virtual List<NPOIRow> getOutDoorBtsKpiSumRows()
        {
            List<NPOIRow> sumRows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("基站名称");
            row.AddCellValue("小区名");
            row.AddCellValue("下载好点RSRP");
            row.AddCellValue("下载中点RSRP");
            row.AddCellValue("下载差点RSRP");
            row.AddCellValue("下载任意点RSRP");

            row.AddCellValue("下载好点SINR");
            row.AddCellValue("下载中点SINR");
            row.AddCellValue("下载差点SINR");
            row.AddCellValue("下载任意点SINR");

            row.AddCellValue("下载好点速率");
            row.AddCellValue("下载中点速率");
            row.AddCellValue("下载差点速率");
            row.AddCellValue("下载任意点速率");

            row.AddCellValue("上传好点RSRP");
            row.AddCellValue("上传中点RSRP");
            row.AddCellValue("上传差点RSRP");
            row.AddCellValue("上传任意点RSRP");

            row.AddCellValue("上传好点SINR");
            row.AddCellValue("上传中点SINR");
            row.AddCellValue("上传差点SINR");
            row.AddCellValue("上传任意点SINR");

            row.AddCellValue("上传好点速率");
            row.AddCellValue("上传中点速率");
            row.AddCellValue("上传差点速率");
            row.AddCellValue("上传任意点速率");

            row.AddCellValue("大包时延");
            row.AddCellValue("小包时延");
            sumRows.Add(row);

            foreach (var varPair in this.CellAcceptInfoDic)
            {
                CellAcceptInfoBase_SX cellAcceptInfo = varPair.Value;
                row = new NPOIRow();
                row.AddCellValue(cellAcceptInfo.BtsName);
                row.AddCellValue(cellAcceptInfo.CellName);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["下载好点"].Rsrp.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["下载中点"].Rsrp.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["下载差点"].Rsrp.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["下载任意点"].Rsrp.Avg);

                row.AddCellValue(cellAcceptInfo.KpiInfoDic["下载好点"].Sinr.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["下载中点"].Sinr.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["下载差点"].Sinr.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["下载任意点"].Sinr.Avg);

                row.AddCellValue(cellAcceptInfo.KpiInfoDic["下载好点"].Speed.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["下载中点"].Speed.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["下载差点"].Speed.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["下载任意点"].Speed.Avg);

                row.AddCellValue(cellAcceptInfo.KpiInfoDic["上传好点"].Rsrp.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["上传中点"].Rsrp.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["上传差点"].Rsrp.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["上传任意点"].Rsrp.Avg);

                row.AddCellValue(cellAcceptInfo.KpiInfoDic["上传好点"].Sinr.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["上传中点"].Sinr.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["上传差点"].Sinr.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["上传任意点"].Sinr.Avg);

                row.AddCellValue(cellAcceptInfo.KpiInfoDic["上传好点"].Speed.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["上传中点"].Speed.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["上传差点"].Speed.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["上传任意点"].Speed.Avg);

                row.AddCellValue(cellAcceptInfo.KpiInfoDic["大包时延"].PingBig.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["小包时延"].PingSmall.Avg);
                sumRows.Add(row);
            }
            return sumRows;
        }
        protected virtual List<NPOIRow> getInDoorBtsKpiSumRows()
        {
            List<NPOIRow> sumRows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("基站名");
            row.AddCellValue("小区名称");
            row.AddCellValue("下行覆盖率");
            row.AddCellValue("下行吞吐率（Mbps）");

            row.AddCellValue("上行覆盖率");
            row.AddCellValue("上行吞吐率（Mbps）");
            sumRows.Add(row);

            foreach (var varPair in this.CellAcceptInfoDic)
            {
                CellAcceptInfoBase_SX cellAcceptInfo = varPair.Value;
                row = new NPOIRow();
                row.AddCellValue(cellAcceptInfo.BtsName);
                row.AddCellValue(cellAcceptInfo.CellName);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["下行"].Rsrp.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["下行"].Speed.Avg);

                row.AddCellValue(cellAcceptInfo.KpiInfoDic["上行"].Rsrp.Avg);
                row.AddCellValue(cellAcceptInfo.KpiInfoDic["上行"].Speed.Avg);
                sumRows.Add(row);
            }
            return sumRows;
        }
    }

    public abstract class CellAcceptInfoBase_SX
    {
        public Dictionary<string, AcceptKpiInfo_SX> KpiInfoDic { get; set; } = new Dictionary<string, AcceptKpiInfo_SX>();
        public int CellId { get; set; }
        public string CellName { get; set; }
        public int BtsId { get; set; }
        public string BtsName { get; set; }
        public abstract bool IsOutDoorCell { get; }

        public string CellNameKey { get; set; }

        public void AddFileAcceptInfo(string fileNameKey, AcceptKpiInfo_SX kpiInfo)
        {
            if (KpiInfoDic.TryGetValue(fileNameKey, out var info))
            {
                info.Merge(kpiInfo);
            }
        }

        public void Calculate()
        {
            foreach (var item in KpiInfoDic.Values)
            {
                item.Calculate();
            }
        }
    }

    public class OutDoorCellAcceptInfo_SX : CellAcceptInfoBase_SX
    {
        public OutDoorCellAcceptInfo_SX()
        {
            KpiInfoDic = new Dictionary<string, AcceptKpiInfo_SX>
            {
                ["下载好点"] = new AcceptKpiInfo_SX(),
                ["下载中点"] = new AcceptKpiInfo_SX(),
                ["下载差点"] = new AcceptKpiInfo_SX(),
                ["下载任意点"] = new AcceptKpiInfo_SX(),

                ["上传好点"] = new AcceptKpiInfo_SX(),
                ["上传中点"] = new AcceptKpiInfo_SX(),
                ["上传差点"] = new AcceptKpiInfo_SX(),
                ["上传任意点"] = new AcceptKpiInfo_SX(),

                ["大包时延"] = new AcceptKpiInfo_SX(),
                ["小包时延"] = new AcceptKpiInfo_SX()
            };
        }
        public override bool IsOutDoorCell
        {
            get { return true; }
        }
    }

    public class IndoorCellAcceptInfo_SX : CellAcceptInfoBase_SX
    {
        public IndoorCellAcceptInfo_SX()
        {
            KpiInfoDic = new Dictionary<string, AcceptKpiInfo_SX>
            {
                ["下行"] = new AcceptKpiInfo_SX(),
                ["上行"] = new AcceptKpiInfo_SX()
            };
        }
        public override bool IsOutDoorCell
        { 
            get { return false; }
        }
    }

    public class AcceptKpiInfo_SX
    {
        public string CurKpiName { get; set; } = "";

        public DataInfo Rsrp { get; set; } = new DataInfo();
        public DataInfo Sinr { get; set; } = new DataInfo();
        public DataInfo Speed { get; set; } = new DataInfo();

        public DataInfo PingBig { get; set; } = new DataInfo();
        public DataInfo PingSmall { get; set; } = new DataInfo();

        public void Add<T>(CellAcceptFileInfo_SX<T> info) where T : ICell
        {
            Rsrp.Merge(info.Rsrp);
            Sinr.Merge(info.Sinr);
            Speed.Merge(info.Speed);

            PingBig.Merge(info.PingBig);
            PingSmall.Merge(info.PingSmall);
        }

        public void Merge(AcceptKpiInfo_SX info)
        {
            Rsrp.Merge(info.Rsrp);
            Sinr.Merge(info.Sinr);
            Speed.Merge(info.Speed);

            PingBig.Merge(info.PingBig);
            PingSmall.Merge(info.PingSmall);
        }

        public void Calculate()
        {
            Rsrp.CalCulate();
            Sinr.CalCulate();
            Speed.CalCulate();

            PingBig.CalCulate();
            PingSmall.CalCulate();
        }
    }

    public class DataInfo
    {
        public float Sum { get; set; }
        public int Count { get; set; }
        public float Avg { get; set; } = float.NaN;

        public void Add(float data)
        {
            Sum += data;
            Count++;
        }

        public void Merge(DataInfo info)
        {
            Count += info.Count;
            Sum += info.Sum;
        }

        public void CalCulate()
        {
            if (Count > 0)
            {
                Avg = (float)Math.Round(Sum / Count, 2);
            }
        }
    }
}
