﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage.WeakCoverArea;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakCoverAreaQuery : AreaKpiBaseQuery
    {
        protected ArchiveCondition archiveCondition { get; set; }
        protected Dictionary<AreaBase, List<AreaBase>> rootLeafDic { get; set; }
        private Dictionary<int, Dictionary<int, AreaBase>> areaTypeIDDic = null;

        protected void initAreaTypeIDDic()
        {
            this.strType = string.Empty;
            areaTypeIDDic = new Dictionary<int, Dictionary<int, AreaBase>>();
            foreach (AreaBase root in rootLeafDic.Keys)
            {
                foreach (AreaBase area in rootLeafDic[root])
                {
                    Dictionary<int, AreaBase> leafDic;
                    if (!areaTypeIDDic.TryGetValue(area.AreaTypeID, out leafDic))
                    {
                        leafDic = new Dictionary<int, AreaBase>();
                        areaTypeIDDic[area.AreaTypeID] = leafDic;
                    }
                    leafDic[area.AreaID] = area;
                }
            }
            this.strType = "110";
        }

        protected override bool getConditionBeforeQuery()
        {
            archiveCondition = ArchiveSettingManager.GetInstance().Condition;
            rootLeafDic = archiveCondition.VillageCondition.RootLeafDic;

            if (rootLeafDic.Count == 0)
            {
                MessageBox.Show("尚未选择村庄，请设置基础配置....", "提醒");
                return false;
            }
            WeakCvrSettingDlg dlg = new WeakCvrSettingDlg();
            dlg.Condition = weakCvrCond;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            weakCvrCond = dlg.Condition;
            IsShowResultForm = true;
            condition = archiveCondition.GetBaseConditionBackUp();
            initAreaTypeIDDic();
            areaDataGrpDic = new Dictionary<AreaBase, KPIDataGroup>();
            return true;
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> expSet = new List<string>();
            expSet.Add(weakCvrCond.RxLevAvgExp);
            return getTriadIDIgnoreServiceType(expSet);
        }

        public override string Name
        {
            get { return "缺站弱覆盖村庄分析"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 31000, 31008, this.Name);
        }
        private new AreaBase getArea(int areaTypeID, int areaSubID)
        {
            AreaBase area = null;
            Dictionary<int, AreaBase> leafDic = null;
            if (areaTypeIDDic.TryGetValue(areaTypeID, out leafDic))
            {
                leafDic.TryGetValue(areaSubID, out area);
            }
            return area;
        }

        private Dictionary<AreaBase, KPIDataGroup> areaDataGrpDic = null;
        protected override void recieveAndHandleSpecificStatData(Package package, List<StatImgDefItem> curImgColumnDef
         , KPIStatDataBase singleStatData)
        {
            int areaTypeID = package.Content.GetParamInt();
            int areaSubID = package.Content.GetParamInt();
            AreaBase area = getArea(areaTypeID, areaSubID);
            if (area == null)
            {
                return;
            }

            fillStatData(package, curImgColumnDef, singleStatData);
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(singleStatData.FileID);

            KPIDataGroup areaDataGrp = null;
            if (!areaDataGrpDic.TryGetValue(area, out areaDataGrp))
            {
                areaDataGrp = new KPIDataGroup(area);
                areaDataGrpDic[area] = areaDataGrp;
            }
            areaDataGrp.AddStatData(fi, singleStatData, false);
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            MasterCom.Util.UiEx.WaitTextBox.Show("正在分析村庄指标...", makeSummary);
        }


        List<WeakCoverArea> weakAreaSet = null;
        protected virtual void makeSummary()
        {
            weakAreaSet = new List<WeakCoverArea>();
            try
            {
                foreach (KPIDataGroup areaGrp in areaDataGrpDic.Values)
                {
                    areaGrp.FinalMtMoGroup();
                    double rxLev = areaGrp.CalcFormula(CarrierType.ChinaMobile, -1, weakCvrCond.RxLevAvgExp);
                    if (weakCvrCond.IsWeakCover(rxLev))
                    {
                        WeakCoverArea area = new WeakCoverArea(areaGrp.GroupInfo as AreaBase);
                        area.AttachNearestCells(CellManager.GetInstance().GetCurrentCells());
                        area.RxLevAvg = rxLev;
                        weakAreaSet.Add(area);
                    }
                }

                for (int i = 0; i < weakAreaSet.Count; i++)
                {
                    WeakCoverArea area = weakAreaSet[i];
                    area.IsFarCell = weakCvrCond.IsFarCell(area.NearestCellDis);
                    for (int j = i + 1; j < weakAreaSet.Count; j++)
                    {
                        WeakCoverArea otherArea = weakAreaSet[j];
                        double areaDis = area.Area.Distance2(otherArea.Area);
                        if (weakCvrCond.IsNbArea(areaDis))
                        {
                            area.AddEachOtherNbArea(otherArea);
                        }
                    }
                    weakCvrCond.Recommend(area);
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                MasterCom.Util.UiEx.WaitTextBox.Close();
            }
        }

        protected override void fireShowResult()
        {
            if (weakAreaSet == null || weakAreaSet.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("无弱覆盖村庄");
                return;
            }
            WeakCoverAreaListForm frm = MainModel.GetObjectFromBlackboard(typeof(WeakCoverAreaListForm)) as WeakCoverAreaListForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new WeakCoverAreaListForm();
                frm.Owner = MainModel.MainForm;
            }
            frm.FillData(weakAreaSet);
            frm.Visible = true;
            frm.BringToFront();
            areaDataGrpDic = null;
            weakAreaSet = null;
            rootLeafDic = null;
            areaTypeIDDic = null;
        }

        public WeakCvrAreaCondition weakCvrCond { get; set; }
    }
}
