﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRVoiceAnaByFreqBand : DIYAnalyseByFileBackgroundBase
    {
        NRVoiceAnaByFreqBandHelper helper;
        List<NRVoiceAnaByFreqBandFileResult> resList;

        protected static readonly object lockObj = new object();
        protected NRVoiceAnaByFreqBand()
            : base(MainModel.GetInstance())
        {
            //FilterSampleByRegion = true;

            Columns = new List<string>();
            Columns.Add("NR_PESQScore");
            Columns.Add("NR_PESQLQ");
            Columns.Add("NR_PESQMos");
            Columns.Add("NR_POLQA_Score_SWB");

            Columns.Add("NR_SSB_ARFCN");
            Columns.Add("NR_PCI");
            Columns.Add("NR_SS_RSRP");
            Columns.Add("NR_SS_SINR");

            Columns.Add("NR_lte_EARFCN");
            Columns.Add("NR_lte_PCI");
            Columns.Add("NR_lte_RSRP");
            Columns.Add("NR_lte_SINR");

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NRVoice);
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 35000, 35041, this.Name);
        }

        protected override bool getCondition()
        {
            return true;
        }

        protected override void getReadyBeforeQuery()
        {
            helper = new NRVoiceAnaByFreqBandHelper();
            resList = new List<NRVoiceAnaByFreqBandFileResult>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                helper.DealWithData(file);
                NRVoiceAnaByFreqBandFileResult fileRes = helper.GetResult(file.FileName);
                if (fileRes != null)
                {
                    resList.Add(fileRes);
                }
            }
        }

        protected override void getResultsAfterQuery()
        {
            NRVoiceAnaByFreqBandFileResult totalRes = helper.StatTotalResult(resList);
            resList.Add(totalRes);
        }

        protected override void fireShowForm()
        {
            NRVoiceAnaByFreqBandForm frm = MainModel.CreateResultForm(typeof(NRVoiceAnaByFreqBandForm)) as NRVoiceAnaByFreqBandForm;
            frm.FillData(resList);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class NRVoiceAnaByFreqBandByFile : NRVoiceAnaByFreqBand
    {
        private NRVoiceAnaByFreqBandByFile()
            : base()
        {
        }

        private static NRVoiceAnaByFreqBandByFile instance = null;
        public static NRVoiceAnaByFreqBandByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRVoiceAnaByFreqBandByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "分频段指标统计(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }

    public class NRVoiceAnaByFreqBandByRegion : NRVoiceAnaByFreqBand
    {
        protected NRVoiceAnaByFreqBandByRegion()
            : base()
        {
        }

        private static NRVoiceAnaByFreqBandByRegion instance = null;
        public static NRVoiceAnaByFreqBandByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRVoiceAnaByFreqBandByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "分频段指标统计(按区域)"; }
        }
    }
}
