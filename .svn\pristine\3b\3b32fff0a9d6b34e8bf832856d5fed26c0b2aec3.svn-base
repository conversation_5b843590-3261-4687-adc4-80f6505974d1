﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc.KPISheet_HaiNan
{
    public class TableCfgManager
    {
        private static TableCfgManager instance = null;
        public static TableCfgManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new TableCfgManager();
                }
                return instance;
            }
        }

        public List<TableTemplate> Tables
        {
            get;
            set;
        }

        public TableTemplate AreaTable
        {
            get;
            private set;
        }

        private TableCfgManager()
        {
            Tables = new List<TableTemplate>();
            Tables.Add(initCityTable()); 
            foreach (TableTemplate tb in Tables)
            {
                foreach (TableColumn col in tb.Columns)
                {
                    colDic[col.HashName] = col;
                }
            }

            TestTagSet = new List<TestTag>();
            QueryTableCfg query = new QueryTableCfg(this);
            query.Query();
        }

        private TableTemplate initCityTable()
        {
            TableTemplate tb = new TableTemplate("市县", "tb_kpi_city", AreaType.None);
            tb.AddColumn("testTag", E_VType.E_String);
            tb.AddColumn("cityID", E_VType.E_Int);
            tb.AddColumn("日期", E_VType.E_String);
            tb.AddColumn("市县", E_VType.E_String);
            tb.AddColumn("应用层平均下载速率（不含掉线）(Mbps)", E_VType.E_Float);
            tb.AddColumn("应用层平均上传速率（不含掉线）(Mbps)", E_VType.E_Float);
            tb.AddColumn("下行低速率占比（<2Mbps采样点占比%）", E_VType.E_Float);
            tb.AddColumn("上行低速率占比（＜512kbps采样点占比）", E_VType.E_Float);
            tb.AddColumn("道路重叠覆盖度", E_VType.E_Float);
            tb.AddColumn("道路重叠覆盖度（RSRP>-110&SINR>=-3）", E_VType.E_Float);
            tb.AddColumn("全程呼叫成功率", E_VType.E_Float);
            tb.AddColumn("连续弱覆盖里程占比", E_VType.E_Float);
            tb.AddColumn("SINR 0以上占比", E_VType.E_Float);
            tb.AddColumn("SINR -3以上占比", E_VType.E_Float);
            tb.AddColumn("FTP下载>10M占比(采样点)", E_VType.E_Float);
            tb.AddColumn("FTP下载速率<2M占比（采样点）%", E_VType.E_Float);
            tb.AddColumn("LTE连续质差里程占比", E_VType.E_Float);
            tb.AddColumn("CSFB呼叫建立时延", E_VType.E_Float);
            tb.AddColumn("GSM路测覆盖率(90覆盖率(%))", E_VType.E_Float);
            tb.AddColumn("GSM语音呼叫全程成功率", E_VType.E_Float);
            tb.AddColumn("GSM语音掉话率", E_VType.E_Float);
            tb.AddColumn("GSM语音接通率", E_VType.E_Float);
            tb.AddColumn("GSM语音质量（0-4级占比）", E_VType.E_Float);
            tb.AddColumn("GSM数据下载速率", E_VType.E_Float);
            tb.AddColumn("TDS路测覆盖率", E_VType.E_Float);
            tb.AddColumn("TD语音呼叫全程成功率", E_VType.E_Float);
            tb.AddColumn("TD语音掉话率", E_VType.E_Float);
            tb.AddColumn("TD语音接通率", E_VType.E_Float);
            tb.AddColumn("TD语音质量（BLER）%", E_VType.E_Float);
            tb.AddColumn("TD呼叫切换比", E_VType.E_Float);
            tb.AddColumn("TD网时长占比", E_VType.E_Float);
            tb.AddColumn("TD网FTP平均下载速率小于300Kbps路段占比", E_VType.E_Float);
            tb.AddColumn("TD数据下载速率_不含掉线（Kbps）", E_VType.E_Float);
            tb.AddColumn("电信LTE路测覆盖率", E_VType.E_Float);
            tb.AddColumn("联通LTE路测覆盖率", E_VType.E_Float);
            tb.AddColumn("问题分析_后台人员", E_VType.E_Int);
            tb.AddColumn("问题分发", E_VType.E_Int);
            tb.AddColumn("问题分析_前台人员", E_VType.E_Int);
            tb.AddColumn("测试跟踪", E_VType.E_Int);
            tb.AddColumn("效果评估", E_VType.E_Int);
            tb.AddColumn("已完成", E_VType.E_Int);
            tb.AddColumn("各状态汇总", E_VType.E_Int);
            tb.AddColumn("问题解决率", E_VType.E_Float);
            tb.AddColumn("上两个月工单未处理数", E_VType.E_Int);
            tb.AddColumn("问题处理及时率", E_VType.E_Float);
            return tb;
        } 

        public List<TestTag> TestTagSet
        {
            get;
            private set;
        }

        readonly Dictionary<string, TableColumn> colDic = new Dictionary<string, TableColumn>();
        internal TableColumn GetTableColumn(string colHashName)
        {
            TableColumn col = null;
            colDic.TryGetValue(colHashName, out col);
            return col;
        }

        readonly Dictionary<string, AreaInfo> areaDic = new Dictionary<string, AreaInfo>();
        internal void AddAreaInfo(AreaInfo areaInfo)
        {
            areaDic[areaInfo.Key] = areaInfo;
        }
         

        Dictionary<string, TestTag> testTagDic = null;
        internal TestTag GetTestTag(string testTag)
        {
            if (testTagDic==null)
            {
                testTagDic = new Dictionary<string, TestTag>();
                foreach (TestTag tt in TestTagSet)
                {
                    testTagDic[tt.Name] = tt;
                }
            }
            TestTag ret;
            testTagDic.TryGetValue(testTag, out ret);
            return ret;
        }
    }

    [Flags]
    public enum AreaType
    {
        None  = 0,
        网格 = 1,
        成分 = 2,
        大区 = 4,
        行政区 = 8
    }


    public class TableTemplate
    {
        public TableTemplate(string typeName, string tableName, AreaType areaType)
        {
            this.TypeName = typeName;
            this.Name = tableName;
            this.AreaType = areaType;
            Columns = new List<TableColumn>();
        }

        public AreaType AreaType
        {
            get;
            private set;
        }

        public void AddColumn(string colName, E_VType valueType)
        {
            TableColumn col = new TableColumn(this, colName, valueType);
            Columns.Add(col);
        }

        public string Name
        {
            get;
            set;
        }

        public List<TableColumn> Columns
        {
            get;
            set;
        }

        public string TypeName
        {
            get;
            set;
        }
    }


    public class TableColumn
    {
        public TableTemplate Table
        { get; set; }

        public string Name
        { get; set; }

        public string FullName
        {
            get { return string.Format("[{0}].[{1}]", Table.TypeName, Name); }
        }

        public string HashName
        {
            get { return string.Format("[{0}].[{1}]", Table.Name, Name); }
        }

        public TableColumn(TableTemplate table, string name)
        {
            this.Table = table;
            this.Name = name;
        }

        public E_VType ValueType
        {
            get;
            private set;
        }

        public TableColumn(TableTemplate tableTemplate, string colName, Model.Interface.E_VType valueType)
        {
            this.Table = tableTemplate;
            this.Name = colName;
            this.ValueType = valueType;
        }

        public override string ToString()
        {
            if (this.Name == null)
            {
                return string.Empty;
            }
            return Name;
        }

    }


    public class TestTag : IComparable<TestTag>
    {
        public override string ToString()
        {
            return Name;
        }

        public string Name
        {
            get;
            private set;
        }
        public string Desc
        {
            get;
            private set;
        }

        public TestTag(string testName, string desc)
        {
            this.Name = testName;
            this.Desc = desc;
        }

        #region IComparable<TestTag> 成员

        public int CompareTo(TestTag other)
        {
            return this.Desc.CompareTo(other.Desc);
        }

        #endregion
    }
}
