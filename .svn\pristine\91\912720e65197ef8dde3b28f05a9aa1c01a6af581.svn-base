﻿using MasterCom.RAMS.Model;
namespace MasterCom.RAMS.ZTFunc
{
    public class ScanHighCoverageRoadCondition
    {
        public int RxLevMaxDiff { get; set; }
        public int RxLevMin { get; set; }
        public int RelCoverate { get; set; }
        public int RoadDistance { get; set; }
        public double RoadMinPercent { get; set; } = 100;
        public int SampleDistance { get; set; }
    }
}