﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class DIYMRDataResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miCloudPicture = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colLac = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCi = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colUL90RxLev = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDL90RxLev = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colRxLevDiff = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colULQual = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDLQual = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTA = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPL = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl1
            // 
            this.gridControl1.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(1264, 490);
            this.gridControl1.TabIndex = 7;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.miCloudPicture});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(139, 48);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(138, 22);
            this.miExportExcel.Text = "导出Excel...";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Clicked);
            // 
            // miCloudPicture
            // 
            this.miCloudPicture.Name = "miCloudPicture";
            this.miCloudPicture.Size = new System.Drawing.Size(138, 22);
            this.miCloudPicture.Text = "云图渲染";
            this.miCloudPicture.Click += new System.EventHandler(this.miCloudPicture_Clicked);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colID,
            this.colLac,
            this.colCi,
            this.colUL90RxLev,
            this.colDL90RxLev,
            this.colRxLevDiff,
            this.colULQual,
            this.colDLQual,
            this.colTA,
            this.colPL});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.CustomDrawCell += new DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventHandler(this.gridView_CustomDrawCell);
            // 
            // colID
            // 
            this.colID.Caption = "序号";
            this.colID.FieldName = "ID";
            this.colID.Name = "colID";
            this.colID.Visible = true;
            this.colID.VisibleIndex = 0;
            // 
            // colLac
            // 
            this.colLac.Caption = "LAC";
            this.colLac.FieldName = "Lac";
            this.colLac.Name = "colLac";
            this.colLac.Visible = true;
            this.colLac.VisibleIndex = 1;
            // 
            // colCi
            // 
            this.colCi.Caption = "CI";
            this.colCi.FieldName = "Ci";
            this.colCi.Name = "colCi";
            this.colCi.Visible = true;
            this.colCi.VisibleIndex = 2;
            // 
            // colUL90RxLev
            // 
            this.colUL90RxLev.Caption = "上行90覆盖率(%)";
            this.colUL90RxLev.FieldName = "UL90Cover";
            this.colUL90RxLev.Name = "colUL90RxLev";
            this.colUL90RxLev.Visible = true;
            this.colUL90RxLev.VisibleIndex = 3;
            // 
            // colDL90RxLev
            // 
            this.colDL90RxLev.Caption = "下行90覆盖率(%)";
            this.colDL90RxLev.FieldName = "DL90Cover";
            this.colDL90RxLev.Name = "colDL90RxLev";
            this.colDL90RxLev.Visible = true;
            this.colDL90RxLev.VisibleIndex = 4;
            // 
            // colRxLevDiff
            // 
            this.colRxLevDiff.Caption = "上下行覆盖差值";
            this.colRxLevDiff.FieldName = "CoverImbalance";
            this.colRxLevDiff.Name = "colRxLevDiff";
            this.colRxLevDiff.Visible = true;
            this.colRxLevDiff.VisibleIndex = 5;
            // 
            // colULQual
            // 
            this.colULQual.Caption = "上行RxQuality0-4占比(%)";
            this.colULQual.FieldName = "ULQual";
            this.colULQual.Name = "colULQual";
            this.colULQual.Visible = true;
            this.colULQual.VisibleIndex = 6;
            // 
            // colDLQual
            // 
            this.colDLQual.Caption = "下行RxQuality0-4占比(%)";
            this.colDLQual.FieldName = "DLQual";
            this.colDLQual.Name = "colDLQual";
            this.colDLQual.Visible = true;
            this.colDLQual.VisibleIndex = 7;
            // 
            // colTA
            // 
            this.colTA.Caption = "TA小于3占比(%)";
            this.colTA.FieldName = "TA";
            this.colTA.Name = "colTA";
            this.colTA.Visible = true;
            this.colTA.VisibleIndex = 8;
            // 
            // colPL
            // 
            this.colPL.Caption = "上下行路损在12dB内占比(%)";
            this.colPL.FieldName = "PathLossImbalance";
            this.colPL.Name = "colPL";
            this.colPL.Visible = true;
            this.colPL.VisibleIndex = 9;
            // 
            // DIYMRDataResultForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1264, 490);
            this.Controls.Add(this.gridControl1);
            this.Name = "DIYMRDataResultForm";
            this.Text = "MR查询结果";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn colID;
        private DevExpress.XtraGrid.Columns.GridColumn colLac;
        private DevExpress.XtraGrid.Columns.GridColumn colCi;
        private DevExpress.XtraGrid.Columns.GridColumn colUL90RxLev;
        private DevExpress.XtraGrid.Columns.GridColumn colDL90RxLev;
        private DevExpress.XtraGrid.Columns.GridColumn colRxLevDiff;
        private DevExpress.XtraGrid.Columns.GridColumn colULQual;
        private DevExpress.XtraGrid.Columns.GridColumn colDLQual;
        private DevExpress.XtraGrid.Columns.GridColumn colTA;
        private DevExpress.XtraGrid.Columns.GridColumn colPL;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miCloudPicture;
    }
}