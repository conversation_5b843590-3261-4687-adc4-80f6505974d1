﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class PeriodSettingDlg : BaseForm
    {
        private PKPeriodCondition pkCond;

        private MapFormItemSelection itemSelection;

        public PeriodSettingDlg(PKPeriodCondition pkCond, MapFormItemSelection itemSelection)
        {
            InitializeComponent();

            this.pkCond = pkCond;
            dateTimePickerHostStart.Value = pkCond.HostPeriod.BeginTime;
            dateTimePickerHostEnd.Value = pkCond.HostPeriod.EndTime;
            dateTimePickerGuestStart.Value = pkCond.GuestPeriod.BeginTime;
            dateTimePickerGuestEnd.Value = pkCond.GuestPeriod.EndTime;
            this.itemSelection = itemSelection;
            refreshCombobox();
        }

        private void simpleButtonTemplateOption_Click(object sender, EventArgs e)
        {
            try
            {
                CompareMode compareConfigTemp = new CompareMode(QueryPKByPeriod.pkCondPath);
                compareConfigTemp.Param = pkCond.CompareModel.Param;
                CPModeEditForm cpModeEditForm = new CPModeEditForm(itemSelection);
                cpModeEditForm.FillData(compareConfigTemp);
                if (DialogResult.OK == cpModeEditForm.ShowDialog())
                {
                    pkCond.CompareModel = cpModeEditForm.compareConfig;
                    refreshCombobox();
                }
            }
            catch
            {
                //continue
            }
        }

        private void refreshCombobox()
        {
            comboBoxEditReportTemplate.SelectedItem = null;
            comboBoxEditReportTemplate.Properties.Items.Clear();

            if (pkCond == null) return;

            foreach (CompareParam param in pkCond.CompareModel.CompareConfigList)
            {
                comboBoxEditReportTemplate.Properties.Items.Add(param);
            }

            if (pkCond.ParamSelected != null && pkCond.CompareModel.CompareConfigList.Contains(pkCond.ParamSelected))
                comboBoxEditReportTemplate.SelectedItem = pkCond.ParamSelected;
            else if (comboBoxEditReportTemplate.Properties.Items.Count > 0)
                comboBoxEditReportTemplate.SelectedIndex = 0;

            pkCond.ParamSelected = comboBoxEditReportTemplate.SelectedItem as CompareParam;
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            if (dateTimePickerHostStart.Value > dateTimePickerHostEnd.Value ||
                dateTimePickerGuestStart.Value > dateTimePickerGuestEnd.Value)
            {
                MessageBox.Show("开始时间大于结束时间...", "警告");
                return;
            }

            pkCond.HostPeriod = new TimePeriod(dateTimePickerHostStart.Value, dateTimePickerHostEnd.Value);
            pkCond.GuestPeriod = new TimePeriod(dateTimePickerGuestStart.Value, dateTimePickerGuestEnd.Value);
            pkCond.ParamSelected = comboBoxEditReportTemplate.SelectedItem as CompareParam;

            this.DialogResult = DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }

    public class PKPeriodCondition
    {
        public TimePeriod HostPeriod { get; set; }

        public TimePeriod GuestPeriod { get; set; }

        public CompareMode CompareModel { get; set; }

        public CompareParam ParamSelected { get; set; }

        public PKPeriodCondition()
        {
            HostPeriod = new TimePeriod(DateTime.Now.Date.AddDays(-1), DateTime.Now.Date);
            GuestPeriod = new TimePeriod(DateTime.Now.Date.AddDays(-1), DateTime.Now.Date);
            CompareModel = new CompareMode(QueryPKByPeriod.pkCondPath);
            ParamSelected = null;
        }
    }
}
