<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="toolTip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="columnMark.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="columnIndex.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="columnInfo.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="StartTime.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ColumnStreetName.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="columnAreaName.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="columnNetType.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="columnCellName.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="columnPCI.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="columnID.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="columnID_Hex.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="columnHandsetTime.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="contextMenuStripDataGridView.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>110, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="toolStripMenuItem_compareReplay.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIIAAACAgP///wAAAAAAgOzp2AAAAAAAAAAAACH/C05FVFNDQVBFMi4wAwEBAAAh+QQB
        AAAFACwAAAAAEAAQAAAIVAALCBxIsKBBAAgTKjwYQOHCgg4dMgxAsWLDAhEVWgSAsWHGjRgzZuxosSJH
        iCUHqFzJEkDKACxbOowZk4DNmwNKVhxwE6dOijx7Esj5M2hPmiwDAgA7
</value>
  </data>
  <metadata name="toolStripBarTop.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>349, 17</value>
  </metadata>
  <data name="toolBtnMark.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAHNSURBVDhPlZI9b9NAHIezAd1oRb6IF8SGmFCXbCAKgW/A
        yMAax3lhycDYZiCVUBJFYgGBxAcAAUppAdtNcIuISmhe/JakMSEPd64jVLW04ZF+0t35//zvfHZsHmq1
        GpVKhXK5HEaOo0dnk8lk8PwhrueHsV0PR2SuJlJ2RfGbt+8orhVRUylWV9fo9+3wJFHZyWSyWXyx8/sP
        deobH0mrKvF4/HJKNOmd1UDK7uCA+pcG9S2dzU86pdI6qpqi9GSdbrf37wZStgcBWzv7fF1ZYNNq89m0
        0I0GpinTpNfrU61WjzfQMlmcwRhjz2E7uQivUzTuLqK3epjWLk1rh2/fW6TTaSkrh1aElN1hgNUZYiaX
        4NVDeHwFXj6geesCu3sdfrT3kRcryv/KhUJB0eRtD8e07OBQfnEfitdErobzlhPQtT3k6wnl6M6P8nn8
        wYifzpDtm+fh+T14uixyHSN5ifYBOKNfyBOK8qOyJC8aBMEE2xvREcX6ykV4toxxZ4n+BEYi8oSi9Lgc
        oWiaxmQ6xR9PcKZg3DiHDwiXtHa6PEORNysJfouEIylrc8kzFFX8bTPUkz7VHCi5XA6ZRCJxO1r7b+Su
        Mgvh7FRisT8t7pE9PBeIJAAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="toolBtnMarkClear.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACCSURBVDhPrY8BCoAwDAP7DP8PvnM27SKbazsFDwK6XIbK
        75xytP64UHUGBDVaJLLTx/gSCsx4ybPTo7szngKD86zTGTIRilHg+mQlHIyB42pOOETQuVKQ/TOiNZJT
        jRk4XZ95M2aWS7KxVpbxjIm+ZBLw7sdG1U1UwnZMKmE7/ojIBSXy3HuncrdZAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="toolBtnMarkPrev.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAF9SURBVDhPzZC7S4JhGMWfhkaV7AZdhqCbECRWUtIgDYFY
        CeISuuQYyPd5+QiMSiJ1MNOm1Ia8IJiiRCBkhCQ4VUtQoxD9I6dXzYQsUGjowBneh/f8ngv9XyVSZZj5
        Am7v3/BZal++YBo06kMg/oIp1UVnAJcnCRrxoGcpAYUuA4M51T5g/yjOOntBMxFoLHdw+Uqdht0sHIZI
        ncIqA0QvX1EsvyMUe4bdVYLFWay5Wr8uVJpwu/McNLQDkp2ClHF0q5IY1mahtbLA8QOsgSdw/scvx/IV
        rJmumoAVjQU0uAWa3APNhWuQmhfYARVnIHkQNOtvulobYNM25HA4IF/YAPUbQRO77EOIhaPsfQiSciCJ
        CSQ2gET6uiVGdPXyTUA6nQbHcXVI3yZonEHkEYjGvBAEocWhEGvwXZlMpgZRKNfrkGkP9Ns3EA7yrZ9/
        UzabBc/zmF/UMQgbWxZgtzlpH1BVLpeDzWbDsprtKmaTSG2dARpyu90/7/q3IvoAWwnhgXQ7UIYAAAAA
        SUVORK5CYII=
</value>
  </data>
  <data name="toolBtnMarkNext.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAF2SURBVDhPzZA7S8JxFIaPQ1DQxRwsjCiXGgwrpIigGiJa
        K9PBSCEkLAkr0LyEluZkYlfRBs1IwoIWKwrK0EECFzdXI+hrvP28UCIO/YegF57hDOfhnJf+f3hCF05C
        WZRHbgkEAqBWHcY1d0ikP7hL7HY7xJIFUJcTwescHpPv3CThcBhqtRr1IgsGVXHcPufx8JqvLeEJdyCb
        usSaNVFEb37BvDYOQe8pqM2DxokrKK2pouQ+USUp/boCGjqHwZv5ZsmVxtjyE4TTN6gbjYI/GYN8Kwl/
        LAfb/hsy2c+SyGg0glpUIIkT1O+twAcaOALJgkU5DV+UkJ6BxB64fakKQdMMY5YxV0YOalYysQYk2ChL
        wqC+Q1CnDZuW0M8bhRcKkmr0ej0UCgUa2lfZNX524QGowwyd4bh2kdWJRqOQjrBuut1smb3Clg1GJvpt
        IpEIiK8F9eyBRCasmzgsF+JwOFgPCtbDIrZ3WXFcU+jG7Wbn/12IvgDU393niGSjtAAAAABJRU5ErkJg
        gg==
</value>
  </data>
  <data name="toolBtnColour.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAK7SURBVDhPjZLrS9NRHMb3tjcR/QV20y5GmAtXmbrUrBcl
        RQW9EMlIrOhCeWtuznkpa87bC80kI7FCIi+bmzrCTQ3N66ZoFwNTZ2pKZJaV0/Xp95tSiL3wgQOHc57n
        c77nfI/kfzqotU0G6roIyu4mLM9eubS8OoXk2FI1pmG+/YZZQJ5jZ2lrdQrM6qJlbJ683nmGf0CAzoZU
        qS+QJhnYrTAI8xr8UkxfluzLFZxjS1HWDFPSNTsT8Whouvcr7NFYOJXfSOeYk7ZRJx3jLnYpDf+vKki4
        d8PoAjHPRj7sUzdPmx0L7IyvpvjlKHHGKc48dmAdmWODQr8SEJLbrY7TD/F8BE6XvueAtouzpTYii1op
        bJrAL72JoPsfKembY+j2C97dbfgHOalrWSMXTjc5XJQJgI4ZKH8zg1RRTZlthlBt40+f+IpReek0GWlJ
        OKqHaC1op1tVsQgJ1nWWxhocPBxwcvLBW+TZdnyTTFx+3MuNmgnR5OMVW9WcmZHBp8lJ0rIyMd00037h
        HpJDeTZO3O/DOA7hRa9Fs++mo9eiA1Jr0bXO4i90ZWtqB+9VG/kxWIkmM596i5WrygS3VxKaa6PdCQU9
        3wnMavsoViRTGbheOUiU8ReJLdCX5AntcnjmwUCVhoRExWJY1OF8OyECJDTP/WG8ZSp95hGthTiri23p
        dvoUW+BVsBDeAcXrKI/ZjHdKs+j9K5HkHjK1frefUs8lw2fkJVOD/Yke0BoohLcL4fWUnfckygJSVd0y
        gFv71DWE3LUSXii0r074w9UyGL8j1K+GorXucLgRztbOE36vh/1q43KIuHDFCjEvXESYhTamHafuaTL0
        a3kSvZkI4eRIAXyu3skVi2slwF9jQqxClmzER91A2sVj3IoOYzgnDC91C9JkM+Lj7lXp3T7/FNOKa/x9
        i9UPieQPRqDnW/4HeiMAAAAASUVORK5CYII=
</value>
  </data>
  <data name="toolBtnSearchPrev.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAF9SURBVDhPzZC7S4JhGMWfhkaV7AZdhqCbECRWUtIgDYFY
        CeISuuQYyPd5+QiMSiJ1MNOm1Ia8IJiiRCBkhCQ4VUtQoxD9I6dXzYQsUGjowBneh/f8ngv9XyVSZZj5
        Am7v3/BZal++YBo06kMg/oIp1UVnAJcnCRrxoGcpAYUuA4M51T5g/yjOOntBMxFoLHdw+Uqdht0sHIZI
        ncIqA0QvX1EsvyMUe4bdVYLFWay5Wr8uVJpwu/McNLQDkp2ClHF0q5IY1mahtbLA8QOsgSdw/scvx/IV
        rJmumoAVjQU0uAWa3APNhWuQmhfYARVnIHkQNOtvulobYNM25HA4IF/YAPUbQRO77EOIhaPsfQiSciCJ
        CSQ2gET6uiVGdPXyTUA6nQbHcXVI3yZonEHkEYjGvBAEocWhEGvwXZlMpgZRKNfrkGkP9Ns3EA7yrZ9/
        UzabBc/zmF/UMQgbWxZgtzlpH1BVLpeDzWbDsprtKmaTSG2dARpyu90/7/q3IvoAWwnhgXQ7UIYAAAAA
        SUVORK5CYII=
</value>
  </data>
  <data name="toolBtnSearchNext.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAF2SURBVDhPzZA7S8JxFIaPQ1DQxRwsjCiXGgwrpIigGiJa
        K9PBSCEkLAkr0LyEluZkYlfRBs1IwoIWKwrK0EECFzdXI+hrvP28UCIO/YegF57hDOfhnJf+f3hCF05C
        WZRHbgkEAqBWHcY1d0ikP7hL7HY7xJIFUJcTwescHpPv3CThcBhqtRr1IgsGVXHcPufx8JqvLeEJdyCb
        usSaNVFEb37BvDYOQe8pqM2DxokrKK2pouQ+USUp/boCGjqHwZv5ZsmVxtjyE4TTN6gbjYI/GYN8Kwl/
        LAfb/hsy2c+SyGg0glpUIIkT1O+twAcaOALJgkU5DV+UkJ6BxB64fakKQdMMY5YxV0YOalYysQYk2ChL
        wqC+Q1CnDZuW0M8bhRcKkmr0ej0UCgUa2lfZNX524QGowwyd4bh2kdWJRqOQjrBuut1smb3Clg1GJvpt
        IpEIiK8F9eyBRCasmzgsF+JwOFgPCtbDIrZ3WXFcU+jG7Wbn/12IvgDU393niGSjtAAAAABJRU5ErkJg
        gg==
</value>
  </data>
  <data name="toolBtnSetting.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAABESURBVDhPYxg04D+ZGA6AnAY4PnDgABE07QwASxCL4QBs
        KsxkYgBIHVgnFAA5CBfAFBCigRgORl0A5FDBBWThAQcMDADwOHNPeJnxaQAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="toolBtnExportExcel.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAH6SURBVDhPbZPdK+txHMd/5a9Qx6ULuThISSncLLGUkyZ5
        yDMhHcIpu0FpJU6nPJSnciFqKVK44MaVW1fydMhDbGZmZrON7c37w3fbz/x+vdv69nu/Po9f7d/2AIbX
        zRha6UG/9TfMi23onW9E50w1WifL0PS3FLUWo6iy3wBTX56oqCtbpNG8tm8VLe3NiWZ3xyOa2BzG1yAq
        kA6gnvD7GwqH8BJ6RfAlAH/QD1/Qh6fAE9zPbrh8Lji9TslSAKQSEA5/GOPMgXizw+OIApgO0yagY7UZ
        Lcv1oqppk5g9fg/KB0062R/t0iNDewY01kLA63tU42ShZGPe6hJVjxWIgeDyhVJkWtKR0JKA64frKICp
        sFkqbcNYrgC7N9rRYK1AgSU/znzlupIJ5TemQiOJAJrZLG/AKx/zjACaGUmZL+8vcO48jwL4h6NSZqn5
        MyLHxzJYWlZlMuqnakRnd2dyzjONi8IPVadp7lmrk9+kPz8wtGPWQUatA/h/exIFcMO4Cyryz5JUnQhh
        P2Ihx7EArid3IXbGHJPNbZNmMeJX6JH9CKaRHKT9SoLGveYuRMzu20in2TDWe+o4lagUzQc3B3oAd4Fm
        Rv3OzJppPrQdipkqHslEijERGtdR3UB1C9VNpFirEqNSNOsASpy3EmesxGYpMW2lFGMi3gANZr0L+6WB
        dQAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="toolStripButton1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAIDSURBVDhPpZLrS5NhGMb3j4SWh0oRQVExD4gonkDpg4hG
        YKxG6WBogkMZKgPNCEVJFBGdGETEvgwyO9DJE5syZw3PIlPEE9pgBCLZ5XvdMB8Ew8gXbl54nuf63dd9
        0OGSnwCahxbPRNPAPMw9Xpg6ZmF46kZZ0xSKzJPIrhpDWsVnpBhGkKx3nAX8Pv7z1zg8OoY/cITdn4fw
        bf/C0kYAN3Ma/w3gWfZL5kzTKBxjWyK2DftwI9tyMYCZKXbNHaD91bLYJrDXsYbrWfUKwJrPE9M2M1Oc
        VzOOpHI7Jr376Hi9ogHqFIANO0/MmmmbmSmm9a8ze+I4MrNWAdjtoJgWcx+PSzg166yZZ8xM8XvXDix9
        c4jIqFYAjoriBV9AhEPv1mH/sonogha0afbZMMZz+yreTGyhpusHwtNNCsA5U1zS4BLxzJIfg299qO32
        Ir7UJtZfftyATqeT+8o2D8JSjQrAJblrncYL7ZJ2+bfaFnC/1S1NjL3diRat7qrO7wLRP3HjWsojBeCo
        mDEo5mNjuweFGvjWg2EBhCbpkW78htSHHwRyNdmgAFzPEee2iFkzayy2OLXzT4gr6UdUnlXrullsxxQ+
        kx0g8BTA3aZlButjSTyjODq/WcQcW/B/Je4OQhLvKQDnzN1mp0nnkvAhR8VuMzNrpm1mpjgkoVwB/v8D
        TgDQASA1MVpwzwAAAABJRU5ErkJggg==
</value>
  </data>
  <metadata name="imageList.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>494, 17</value>
  </metadata>
  <data name="imageList.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        ********************************************************************************
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAABW
        CAAAAk1TRnQBSQFMAgEBAgEAAVwBAgFcAQIBEAEAARABAAT/AQkBAAj/AUIBTQE2AQQGAAE2AQQCAAEo
        AwABQAMAARADAAEBAQABCAYAAQQYAAGAAgABgAMAAoABAAGAAwABgAEAAYABAAKAAgADwAEAAcAB3AHA
        AQAB8AHKAaYBAAEzBQABMwEAATMBAAEzAQACMwIAAxYBAAMcAQADIgEAAykBAANVAQADTQEAA0IBAAM5
        AQABgAF8Af8BAAJQAf8BAAGTAQAB1gEAAf8B7AHMAQABxgHWAe8BAAHWAucBAAGQAakBrQIAAf8BMwMA
        AWYDAAGZAwABzAIAATMDAAIzAgABMwFmAgABMwGZAgABMwHMAgABMwH/AgABZgMAAWYBMwIAAmYCAAFm
        AZkCAAFmAcwCAAFmAf8CAAGZAwABmQEzAgABmQFmAgACmQIAAZkBzAIAAZkB/wIAAcwDAAHMATMCAAHM
        AWYCAAHMAZkCAALMAgABzAH/AgAB/wFmAgAB/wGZAgAB/wHMAQABMwH/AgAB/wEAATMBAAEzAQABZgEA
        ATMBAAGZAQABMwEAAcwBAAEzAQAB/wEAAf8BMwIAAzMBAAIzAWYBAAIzAZkBAAIzAcwBAAIzAf8BAAEz
        AWYCAAEzAWYBMwEAATMCZgEAATMBZgGZAQABMwFmAcwBAAEzAWYB/wEAATMBmQIAATMBmQEzAQABMwGZ
        AWYBAAEzApkBAAEzAZkBzAEAATMBmQH/AQABMwHMAgABMwHMATMBAAEzAcwBZgEAATMBzAGZAQABMwLM
        AQABMwHMAf8BAAEzAf8BMwEAATMB/wFmAQABMwH/AZkBAAEzAf8BzAEAATMC/wEAAWYDAAFmAQABMwEA
        AWYBAAFmAQABZgEAAZkBAAFmAQABzAEAAWYBAAH/AQABZgEzAgABZgIzAQABZgEzAWYBAAFmATMBmQEA
        AWYBMwHMAQABZgEzAf8BAAJmAgACZgEzAQADZgEAAmYBmQEAAmYBzAEAAWYBmQIAAWYBmQEzAQABZgGZ
        AWYBAAFmApkBAAFmAZkBzAEAAWYBmQH/AQABZgHMAgABZgHMATMBAAFmAcwBmQEAAWYCzAEAAWYBzAH/
        AQABZgH/AgABZgH/ATMBAAFmAf8BmQEAAWYB/wHMAQABzAEAAf8BAAH/AQABzAEAApkCAAGZATMBmQEA
        AZkBAAGZAQABmQEAAcwBAAGZAwABmQIzAQABmQEAAWYBAAGZATMBzAEAAZkBAAH/AQABmQFmAgABmQFm
        ATMBAAGZATMBZgEAAZkBZgGZAQABmQFmAcwBAAGZATMB/wEAApkBMwEAApkBZgEAA5kBAAKZAcwBAAKZ
        Af8BAAGZAcwCAAGZAcwBMwEAAWYBzAFmAQABmQHMAZkBAAGZAswBAAGZAcwB/wEAAZkB/wIAAZkB/wEz
        AQABmQHMAWYBAAGZAf8BmQEAAZkB/wHMAQABmQL/AQABzAMAAZkBAAEzAQABzAEAAWYBAAHMAQABmQEA
        AcwBAAHMAQABmQEzAgABzAIzAQABzAEzAWYBAAHMATMBmQEAAcwBMwHMAQABzAEzAf8BAAHMAWYCAAHM
        AWYBMwEAAZkCZgEAAcwBZgGZAQABzAFmAcwBAAGZAWYB/wEAAcwBmQIAAcwBmQEzAQABzAGZAWYBAAHM
        ApkBAAHMAZkBzAEAAcwBmQH/AQACzAIAAswBMwEAAswBZgEAAswBmQEAA8wBAALMAf8BAAHMAf8CAAHM
        Af8BMwEAAZkB/wFmAQABzAH/AZkBAAHMAf8BzAEAAcwC/wEAAcwBAAEzAQAB/wEAAWYBAAH/AQABmQEA
        AcwBMwIAAf8CMwEAAf8BMwFmAQAB/wEzAZkBAAH/ATMBzAEAAf8BMwH/AQAB/wFmAgAB/wFmATMBAAHM
        AmYBAAH/AWYBmQEAAf8BZgHMAQABzAFmAf8BAAH/AZkCAAH/AZkBMwEAAf8BmQFmAQAB/wKZAQAB/wGZ
        AcwBAAH/AZkB/wEAAf8BzAIAAf8BzAEzAQAB/wHMAWYBAAH/AcwBmQEAAf8CzAEAAf8BzAH/AQAC/wEz
        AQABzAH/AWYBAAL/AZkBAAL/AcwBAAJmAf8BAAFmAf8BZgEAAWYC/wEAAf8CZgEAAf8BZgH/AQAC/wFm
        AQABIQEAAaUBAANfAQADdwEAA4YBAAOWAQADywEAA7IBAAPXAQAD3QEAA+MBAAPqAQAD8QEAA/gBAAHw
        AfsB/wEAAaQCoAEAA4ADAAH/AgAB/wMAAv8BAAH/AwAB/wEAAf8BAAL/AgAD/wUAAu8BvDwAAe8C7AH3
        OwAB7wHsAv8B7AHvOQAB7wHsBP8B7AHvNwAB7wHsA/8BKwL/AewB7zYAAewB/wH0Af8BKwFZASsC/wHs
        Ae81AAHsAvQBKwF6AlMBKwL/AewB7zQAAQcB7AH/AfQBKwF6AlMBKwL0AewB7zUAAewB/wHzASsBegFT
        AXoBKwLzAewB7zUAAewB/wHzASsBmgErAfIC8QHyAewB7zUAAewB/wHyASsB8QHwA/EB8wHvNgAB7AH/
        A/AB9wHsAfcB8wHvNwAB7AH0AbwB8AHsAe8B7AH0Ae84AAHsAfQBvAGSAewBkgH0Ae85AAHsBfQB7zoA
        Be8yAAFCAU0BPgcAAT4DAAEoAwABQAMAARADAAEBAQABAQUAAYAXAAP/AQAB8QP/BAAB4QP/BAABwAP/
        BAABgAF/Av8FAAE/Av8FAAEfAv8FAAEPAv8FAAEHAv8EAAHAAQMC/wQAAeABAQL/BAAB8AEBAv8EAAH4
        AQEC/wQAAfwBAQL/BAAB/gEBAv8EAAH/AQEC/wQAAf8BgwL/BAAL
</value>
  </data>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>61</value>
  </metadata>
  <data name="BriefDataGridForm.Appearance.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIcAAAEAAAAAAP///yZLgqGwxqfH6leX4EN4ukyFyv39/aW0y1iW4FaU3VeT3Pr6+uj1
        +Jmvyuz2+pywye32+leS2Pz8/JGsytzw+tzw+5Ksydzv+gAAAFeR2FeQ1tnr/Nnv/GeNvXup3gAAAFiR
        1liQ1I2qyo6qytXr+9Xs+wAAAFiP1FiP09Dq+4upyoypytLq+3mo3nio3liO0wAAAAAAAAAAAAAAAFeM
        0FuJwGCNwpK34szm+wAAAFiNz1GAvvX6/meUy8rl+1eMzvz+/2GQyFSFxU98t058twAAAKi20AAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        ACH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAAAACwAAAAAEAAQAAAIiQABCBxIsKDBgwYLGFi4cMABhggK
        DDSQoKLFiwkWDGSAsSODgQ0cgBhJEoZJBw0GUqhwoSUGlxouOOAwsEOFkjBC6AxRYcRAEglOCB16AgWK
        BCoGrkgQo6lTpwlkDLyRYIfVq1cF9Bjo44fJr08FCCHoYwgRAWjToi1S0MgRI3Djwk2CsK5dgwEBADs=
</value>
  </data>
</root>