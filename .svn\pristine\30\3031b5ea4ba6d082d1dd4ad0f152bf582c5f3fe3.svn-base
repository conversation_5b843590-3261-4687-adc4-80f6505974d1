﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEUnknownDisturbDlg : BaseForm
    {
        public LTEUnknownDisturbDlg(LTEUnknownDisturbCondition condition)
        {
            InitializeComponent();
            setCondition(condition);
        }
        private void setCondition(LTEUnknownDisturbCondition condition)
        {
            if (condition == null)
            {
                return;
            }
            numMinDiffer.Value = (decimal)condition.MinDiffer;
            numMaxSINR.Value = (decimal)condition.MaxSinr;
            numMinDistance.Value = (decimal)condition.MinStayDistance;
            chkTime.Checked = condition.CheckTime;
            numTime.Value = (decimal)condition.MinStaySecond;
        }
        public LTEUnknownDisturbCondition GetCondition()
        {
           LTEUnknownDisturbCondition condition = new LTEUnknownDisturbCondition();
            condition.MinDiffer= (float)numMinDiffer.Value;
            condition.MaxSinr = (float)numMaxSINR.Value;
            condition.MinStayDistance = (float)numMinDistance.Value;
            condition.CheckTime = chkTime.Checked;
            condition.MinStaySecond = (double)numTime.Value;
            return condition;
        }
        private void chkTime_CheckedChanged(object sender, EventArgs e)
        {
            numTime.Enabled = chkTime.Checked;
        }     
    }
}
