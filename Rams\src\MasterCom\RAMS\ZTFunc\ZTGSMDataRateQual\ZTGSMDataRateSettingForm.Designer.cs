﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTGSMDataRateSettingForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.rangeSettingDLRate = new MasterCom.RAMS.Chris.Util.RangeSetting();
            this.numSampleCountLimit = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.numRadius = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.numCVValue = new System.Windows.Forms.NumericUpDown();
            this.label8 = new System.Windows.Forms.Label();
            this.numMEANValue = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCountLimit)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRadius)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCVValue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMEANValue)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(24, 21);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "下载速率门限";
            // 
            // rangeSettingDLRate
            // 
            this.rangeSettingDLRate.Location = new System.Drawing.Point(101, 12);
            this.rangeSettingDLRate.Name = "rangeSettingDLRate";
            this.rangeSettingDLRate.Size = new System.Drawing.Size(243, 28);
            this.rangeSettingDLRate.TabIndex = 4;
            // 
            // numSampleCountLimit
            // 
            this.numSampleCountLimit.Location = new System.Drawing.Point(300, 98);
            this.numSampleCountLimit.Maximum = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.numSampleCountLimit.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSampleCountLimit.Name = "numSampleCountLimit";
            this.numSampleCountLimit.Size = new System.Drawing.Size(41, 21);
            this.numSampleCountLimit.TabIndex = 16;
            this.numSampleCountLimit.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(195, 102);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(101, 12);
            this.label4.TabIndex = 19;
            this.label4.Text = "至少包含采样点数";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(147, 102);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(17, 12);
            this.label3.TabIndex = 18;
            this.label3.Text = "米";
            // 
            // numRadius
            // 
            this.numRadius.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numRadius.Location = new System.Drawing.Point(103, 98);
            this.numRadius.Maximum = new decimal(new int[] {
            500,
            0,
            0,
            0});
            this.numRadius.Minimum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numRadius.Name = "numRadius";
            this.numRadius.Size = new System.Drawing.Size(41, 21);
            this.numRadius.TabIndex = 15;
            this.numRadius.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(48, 103);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(53, 12);
            this.label2.TabIndex = 17;
            this.label2.Text = "汇聚半径";
            // 
            // numCVValue
            // 
            this.numCVValue.Location = new System.Drawing.Point(103, 56);
            this.numCVValue.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numCVValue.Name = "numCVValue";
            this.numCVValue.Size = new System.Drawing.Size(41, 21);
            this.numCVValue.TabIndex = 20;
            this.numCVValue.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(42, 61);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(59, 12);
            this.label8.TabIndex = 21;
            this.label8.Text = "CV_BEP ≤";
            // 
            // numMEANValue
            // 
            this.numMEANValue.Location = new System.Drawing.Point(300, 56);
            this.numMEANValue.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numMEANValue.Name = "numMEANValue";
            this.numMEANValue.Size = new System.Drawing.Size(41, 21);
            this.numMEANValue.TabIndex = 22;
            this.numMEANValue.Value = new decimal(new int[] {
            28,
            0,
            0,
            0});
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(225, 61);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(71, 12);
            this.label5.TabIndex = 23;
            this.label5.Text = "MEAN_BEP ≤";
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(269, 158);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 24;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(172, 158);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 25;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // ZTGSMDataRateSettingForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(365, 193);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.numMEANValue);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.numCVValue);
            this.Controls.Add(this.label8);
            this.Controls.Add(this.numSampleCountLimit);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.numRadius);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.rangeSettingDLRate);
            this.Controls.Add(this.label1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.Name = "ZTGSMDataRateSettingForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "门限设置";
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCountLimit)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRadius)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCVValue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMEANValue)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private MasterCom.RAMS.Chris.Util.RangeSetting rangeSettingDLRate;
        private System.Windows.Forms.NumericUpDown numSampleCountLimit;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numRadius;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numCVValue;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown numMEANValue;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
    }
}