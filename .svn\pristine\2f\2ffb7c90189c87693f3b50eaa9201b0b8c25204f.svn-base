﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteMgrsStaterBase
    {
        protected MainModel MainModel;

        public LteMgrsStaterBase()
        {
            MainModel = MainModel.GetInstance();
        }

        public virtual void DoStat(LteMgrsFuncItem curFuncItem)
        {

        }

        public virtual List<LteMgrsResultControlBase> GetResult()
        {
            return new List<LteMgrsResultControlBase>();
        }

        public virtual void Clear()
        {

        }
    }
}
