﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using System.Reflection;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEScanRSTimingForm : MinCloseForm
    {
        public LTEScanRSTimingForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
        }     

        public List<List<NPOIRow>> nrDatasList { get; set; }
        public List<string> sheetNames { get; set; }
        List<ZTLteScanRSTimingAna.TddTimingResult> listRsTimingData;
        ZTLteScanRSTimingAna.TddTimingResult curData = new ZTLteScanRSTimingAna.TddTimingResult();

        /// <summary>
        /// 数据初始化，加载前200个小区
        /// </summary>
        public void FillData(List<ZTLteScanRSTimingAna.TddTimingResult> listRsTimingData)
        {
            labNum.Text = listRsTimingData.Count.ToString();
            int iPage = listRsTimingData.Count % 200 > 0 ? listRsTimingData.Count / 200 + 1 : listRsTimingData.Count / 200;
            labPage.Text = iPage.ToString();
            this.listRsTimingData = listRsTimingData;

            dataGridViewCell.Columns.Clear();
            dataGridViewCell.Rows.Clear();//小区级

            int rowCellAt = 0;
            foreach (NPOIRow data in nrDatasList[0])
            {
                if (rowCellAt == 0)
                {
                    intDataViewColumn();
                    rowCellAt++;
                    continue;
                }
                if (rowCellAt > 200)
                    break;
                initDataRow(data);
                rowCellAt++;
            }

            txtPage.Text = "1";
        }

        /// <summary>
        /// 按小区模糊查找，前200个小区
        /// </summary>
        private void FillData(string strCellName)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int rowCellAt = 0;
            foreach (NPOIRow data in nrDatasList[0])
            {
                if (nrDatasList[0][0].cellValues[0].ToString() == data.cellValues[0].ToString())
                    continue;

                if (strCellName != "" && data.cellValues[4].ToString().IndexOf(strCellName) < 0 && 
                    data.cellValues[12].ToString().IndexOf(strCellName) < 0)
                    continue;

                if (rowCellAt >= 200)
                    break;
                initDataRow(data);
                rowCellAt++;
            }
        }

        /// <summary>
        /// 按页数查找
        /// </summary>
        private void FillData(int iPage)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int iCount = -1;
            int rowCellAt = 0;
            foreach (NPOIRow data in nrDatasList[0])
            {
                if (data.cellValues[0].ToString() == "序号")
                    continue;

                iCount++;
                if (iCount / 200 != iPage)
                    continue;
                initDataRow(data);
                rowCellAt++;
            }
        }

        /// <summary>
        /// 初始化列头
        /// </summary>
        private void intDataViewColumn()
        {
            dataGridViewCell.Columns.Clear();
            int idx = 1;
            foreach (object obj in nrDatasList[0][0].cellValues)
            {
                dataGridViewCell.Columns.Add(idx++.ToString(), obj.ToString());
            }
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void initDataRow(NPOIRow nop)
        {
            DataGridViewRow row = new DataGridViewRow();
            row.Tag = nop.cellValues[3];//小区名称
            foreach (object obj in nop.cellValues)
            {
                DataGridViewTextBoxCell boxcell = new DataGridViewTextBoxCell();
                boxcell.Value = obj.ToString();
                row.Cells.Add(boxcell);
            }
            dataGridViewCell.Rows.Add(row);
        }

        private void miExportWholeExcel_Click(object sender, EventArgs e)
        {
            doExport(nrDatasList, sheetNames[0]);
        }

        private void 导出CSVToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ZTAntFuncHelper.OutputCsvFile(nrDatasList, sheetNames);
        }

        public void doExport(List<List<NPOIRow>> nrDatasList, string strSheetName)
        {
            string fileName;
            if (!ExportResultSecurityHelper.GetExportPermit(FileSimpleTypeHelper.Excel, out fileName))
            {
                return;
            }
            Microsoft.Office.Interop.Excel.Application excel = new Microsoft.Office.Interop.Excel.Application();  //Execl的操作类
            Microsoft.Office.Interop.Excel.Workbook bookDest = excel.Workbooks.Add(Missing.Value);
            Microsoft.Office.Interop.Excel.Worksheet sheetDest = bookDest.Worksheets.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value) as Microsoft.Office.Interop.Excel.Worksheet;//给工作薄添加一个Sheet   
            sheetDest.Name = strSheetName;
            for (int i = bookDest.Worksheets.Count; i > 1; i--)
            {
                Microsoft.Office.Interop.Excel.Worksheet wt = (Microsoft.Office.Interop.Excel.Worksheet)bookDest.Worksheets[i];
                if (wt.Name != strSheetName)
                {
                    wt.Delete();
                }
            }
            try
            {
                Microsoft.Office.Interop.Excel.Range rngRow = (Microsoft.Office.Interop.Excel.Range)sheetDest.Columns[1, Type.Missing];
                rngRow.UseStandardWidth = 70;
                int idx = 0;
                int row = 0;

                //导入数据行
                foreach (List<NPOIRow> listNPOI in nrDatasList)
                {
                    row = 1;
                    foreach (NPOIRow npoi in listNPOI)
                    {
                        idx = npoi.cellValues.Count;
                        Microsoft.Office.Interop.Excel.Range cell1ran = sheetDest.get_Range(sheetDest.Cells[row, 1], sheetDest.Cells[row++, idx]);
                        cell1ran.Value2 = npoi.cellValues.ToArray();
                    }
                }
                bookDest.Saved = true;
                bookDest.SaveCopyAs(fileName);//保存
                MessageBox.Show("导出成功！");
            }
            catch (Exception w)
            {
                MessageBox.Show("导出异常:" + w.Message);
            }
            finally
            {
                excel.Quit();
                //GC.Collect();//垃圾回收   
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string strCellName = txtCellName.Text;
            FillData(strCellName);
        }

        private void btnGo_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = MainModel.ListCellLteMRData.Count;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;
            if (iPage < 0)
                iPage = 0;
            else if (iPage > iCount - 1)
                iPage = iCount - 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnPrevpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            iPage = iPage - 1 >= 0 ? iPage - 1 : iPage;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnNextpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = listRsTimingData.Count;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;

            iPage = iPage + 1 >= iCount ? iPage : iPage + 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void showSampleToolMenu_Click(object sender, EventArgs e)
        {
            MainModel.ClearDTData();
            int iRowId = dataGridViewCell.SelectedCells[0].RowIndex;
            foreach (ZTLteScanRSTimingAna.TddTimingResult tmpData in listRsTimingData)
            {
                if (dataGridViewCell.Rows[iRowId].Cells[4].Value.ToString() == tmpData.StrMainCellName &&
                    dataGridViewCell.Rows[iRowId].Cells[12].Value.ToString() == tmpData.StrNbCellName)
                {
                    curData = tmpData;
                    break;
                }
            }
            if (curData != null)
            {
                foreach (TestPoint tp in curData.tpList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.SelectedLTECells.Add(CellManager.GetInstance().GetLTECellLatest(curData.StrMainCellName));
                MainModel.SelectedLTECells.Add(CellManager.GetInstance().GetLTECellLatest(curData.StrNbCellName));
                MainModel.ShowLastWeakRoadFly = true;
                MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
                MainModel.FireDTDataChanged(this);
            }
        }

    }
}
