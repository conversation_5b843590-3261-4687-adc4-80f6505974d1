﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.Net
{
    public class DIYAnalyseByCellBackgroundBaseByPeriod : DIYAnalyseByPeriodBackgroundBase_Sample
    {
        public DIYAnalyseByCellBackgroundBaseByPeriod(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }

        public override string Name
        {
            get { return "区域内数据按小区汇聚专题分析基类"; }
        }

        protected override void getBackgroundData()
        {
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetResult_Cell(Condition.Periods[0].IBeginTime,
                            Condition.Periods[0].IEndTime, GetSubFuncID(), Name, StatType, BackgroundFuncBaseSetting.GetInstance().projectType);
        }

        protected override List<TimePeriod> GetStatedTimePeriod()
        {
            return BackgroundFuncQueryManager.GetInstance().GetStatedTimePeriod_Cell(GetSubFuncID(), BackgroundFuncBaseSetting.GetInstance().projectType);
        }
    }
}
