﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEScanStructForm : MinCloseForm
    {
        MapForm mapForm;
        public LTEScanStructForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            this.mapForm = MainModel.MainForm.GetMapForm();

            RsrpSplit = 6;
            GridAntStatDic = new Dictionary<string, ZTLteAntennaCoverStruct.CellAntStat>();
            AllSampleDataDic = new Dictionary<string, ZTLteAntennaCoverStruct.SampleData>();
            CellInfoDic = new Dictionary<ZTLteAntennaCoverStruct.GridCellKey, ZTLteAntennaCoverStruct.CellAntData>();
            cellGridDic = new Dictionary<string, List<ZTLteAntennaCoverStruct.CellAntData>>();
            RoadStatDic = new Dictionary<int, ZTLteAntennaCoverStruct.RoadStat>();
        }

        public int RsrpSplit { get; set; }
        public List<List<NPOIRow>> RowDatasList { get; set; }
        public List<string> sheetNames { get; set; }
        public Dictionary<string, ZTLteAntennaCoverStruct.CellAntStat> GridAntStatDic { get; set; }
        public Dictionary<string, ZTLteAntennaCoverStruct.SampleData> AllSampleDataDic { get; set; }
        public Dictionary<ZTLteAntennaCoverStruct.GridCellKey, ZTLteAntennaCoverStruct.CellAntData> CellInfoDic { get; set; }
        private Dictionary<string, List<ZTLteAntennaCoverStruct.CellAntData>> cellGridDic { get; set; }
        public Dictionary<int, ZTLteAntennaCoverStruct.RoadStat> RoadStatDic { get; set; }

        /// <summary>
        /// 新填充方法是通过nrDatasList数据进行填充，请勿更改里面的顺序
        /// </summary>
        public void FillData()
        {
            dgViewSample.Columns.Clear();
            dgViewSample.Rows.Clear();
            dgViewGrid.Columns.Clear();
            dgViewGrid.Rows.Clear();
            dgViewSinr.Columns.Clear();
            dgViewSinr.Rows.Clear();
            dgViewCell.Columns.Clear();
            dgViewCell.Rows.Clear();
            dgViewRoad.Columns.Clear();
            dgViewRoad.Rows.Clear();

            //小区明细
            List<ZTLteAntennaCoverStruct.CellAntData> cellList;
            foreach (ZTLteAntennaCoverStruct.GridCellKey gKey in CellInfoDic.Keys)
            {
                if (!cellGridDic.TryGetValue(gKey.strGridName, out cellList))
                    cellList = new List<ZTLteAntennaCoverStruct.CellAntData>();

                cellList.Add(CellInfoDic[gKey]);
                cellGridDic[gKey.strGridName] = cellList;
            }

            for (int i = 0; i < RowDatasList.Count; i++)
            {
                int idx = 0;
                foreach (NPOIRow row in RowDatasList[i])
                {
                    idx = addData(i, idx, row);
                    idx++;
                }
            }
            dgViewSet();
            this.scanAntDataXTCtrl.SelectedTabPageIndex = 0;
        }

        private int addData(int i, int idx, NPOIRow row)
        {
            if (i == 0 || i == 4)//第一个Sheet
            {
                if (idx == 0)//第一行
                {
                    idx = setColHeader(i, idx, row);
                }
                else
                {
                    if (i == 0)
                    {
                        addGrdiData(row);
                    }
                    else
                    {
                        addRoadData(row);
                    }
                }
            }

            return idx;
        }

        private int setColHeader(int i, int idx, NPOIRow row)
        {
            foreach (object obj in row.cellValues)
            {
                if (i == 0)
                    dgViewGrid.Columns.Add(idx++.ToString(), obj.ToString());
                else
                    dgViewRoad.Columns.Add(idx++.ToString(), obj.ToString());
            }

            return idx;
        }

        private void addGrdiData(NPOIRow row)
        {
            string grid = row.cellValues[2].ToString();
            DataGridViewRow dgRow = new DataGridViewRow();
            dgRow.Tag = grid;//小区名称
            foreach (object obj in row.cellValues)
            {
                DataGridViewTextBoxCell boxcell = new DataGridViewTextBoxCell();
                if (obj != null)
                {
                    boxcell.Value = obj.ToString();
                }
                else
                {
                    boxcell.Value = "";
                }
                dgRow.Cells.Add(boxcell);
            }
            dgViewGrid.Rows.Add(dgRow);
        }

        private void addRoadData(NPOIRow row)
        {
            DataGridViewRow dgRow = new DataGridViewRow();
            dgRow.Tag = row.cellValues[0].ToString();//序号
            foreach (object obj in row.cellValues)
            {
                DataGridViewTextBoxCell boxcell = new DataGridViewTextBoxCell();
                if (obj != null)
                {
                    boxcell.Value = obj.ToString();
                }
                else
                {
                    boxcell.Value = "";
                }
                dgRow.Cells.Add(boxcell);
            }
            dgViewRoad.Rows.Add(dgRow);
        }

        /// <summary>
        /// 详细列表初始化
        /// </summary>
        private void dgViewSet()
        {
            dgViewSample.Columns.Add("1", "RSRP\\覆盖度-采样点占比");
            dgViewSample.Columns.Add("2", "1");
            dgViewSample.Columns.Add("3", "2");
            dgViewSample.Columns.Add("4", "3");
            dgViewSample.Columns.Add("5", "4");
            dgViewSample.Columns.Add("6", "5");
            dgViewSample.Columns.Add("7", ">=5");

            dgViewSinr.Columns.Add("1", "RSRP\\覆盖度-SINR均值");
            dgViewSinr.Columns.Add("2", "1");
            dgViewSinr.Columns.Add("3", "2");
            dgViewSinr.Columns.Add("4", "3");
            dgViewSinr.Columns.Add("5", "4");
            dgViewSinr.Columns.Add("6", "5");
            dgViewSinr.Columns.Add("7", ">=5");

            for (int i = 0; i < 7; i++)
            {
                if (i == 0)
                {
                    dgViewSample.Columns[i].Width = 90;
                    dgViewSinr.Columns[i].Width = 90;
                }
                else
                {
                    dgViewSample.Columns[i].Width = 75;
                    dgViewSinr.Columns[i].Width = 75;
                }
            }

            if (RowDatasList.Count >= 4)
            {
                int idx = 0;
                foreach (object obj in RowDatasList[3][0].cellValues)
                {
                    dgViewCell.Columns.Add(idx++.ToString(), obj.ToString());
                }
            }
        }

        /// <summary>
        /// 采样点信息回放
        /// </summary>
        private void miShowGis_Click(object sender, EventArgs e)
        {
            Dictionary<int, List<LongLat>> testPointDic = new Dictionary<int, List<LongLat>>();

            if (this.scanAntDataXTCtrl.SelectedTabPageIndex == 2)
            {
                int iRowId = dgViewCell.SelectedCells[0].RowIndex;
                ZTLteAntennaCoverStruct.GridCellKey gKey = dgViewCell.Rows[iRowId].Tag as ZTLteAntennaCoverStruct.GridCellKey;

                ZTLteAntennaCoverStruct.CellAntData cellData;
                if (CellInfoDic.TryGetValue(gKey, out cellData))
                {
                    testPointDic = cellData.sampleData.testPointDic;
                }
                MainModel.SelectedLTECell = CellManager.GetInstance().GetLTECellLatest(gKey.strCellName);
            }
            else if (this.scanAntDataXTCtrl.SelectedTabPageIndex == 3)
            {
                int iRowId = dgViewRoad.SelectedCells[0].RowIndex;
                int iRoad = Convert.ToInt32(dgViewRoad.Rows[iRowId].Tag);
                ZTLteAntennaCoverStruct.RoadStat roadStat;
                if (RoadStatDic.TryGetValue(iRoad, out roadStat))
                {
                    testPointDic = roadStat.testPointDic;
                }
            }
            else
            {
                int iRowId = dgViewGrid.SelectedCells[0].RowIndex;
                string strGrid = dgViewGrid.Rows[iRowId].Tag.ToString();

                ZTLteAntennaCoverStruct.SampleData sampleData;
                if (AllSampleDataDic.TryGetValue(strGrid, out sampleData))
                {

                    if (this.scanAntDataXTCtrl.SelectedTabPageIndex == 1)
                    {
                        addValidTestPoint(testPointDic, sampleData);
                    }
                    else if (this.scanAntDataXTCtrl.SelectedTabPageIndex == 0)
                    {
                        testPointDic = sampleData.testPointDic;
                    }
                }
            }

            setLayer(testPointDic);
        }

        private void setLayer(Dictionary<int, List<LongLat>> testPointDic)
        {
            AntPointLayer antLayer = mapForm.GetLayerBase(typeof(AntPointLayer)) as AntPointLayer;
            if (antLayer != null)
            {
                if (testPointDic.Count > 0)
                {
                    List<int> testPointList = new List<int>(testPointDic.Keys);
                    List<LongLat> llList = testPointDic[testPointList[0]];
                    MainModel.MainForm.GetMapForm().GoToView(llList[0].fLongitude, llList[0].fLatitude);
                }
                antLayer.iRsrpSplit = RsrpSplit;
                antLayer.iFunc = 4;
                antLayer.gisSampleDic = testPointDic;
                antLayer.Invalidate();
            }
        }

        private void addValidTestPoint(Dictionary<int, List<LongLat>> testPointDic, ZTLteAntennaCoverStruct.SampleData sampleData)
        {
            foreach (DataGridViewCell cell in dgViewSample.SelectedCells)
            {
                int iCellRowId = cell.RowIndex;
                int iCellColId = cell.ColumnIndex - 1;
                if (iCellRowId >= 0 && iCellRowId < 13 && iCellColId >= 0 && iCellColId < 6)
                {
                    int iMergeKey = iCellRowId * 10 + iCellColId;
                    if (sampleData.testPointDic.ContainsKey(iMergeKey))
                    {
                        testPointDic.Add(iMergeKey, sampleData.testPointDic[iMergeKey]);
                    }
                }
            }
        }

        /// <summary>
        /// 天线分析数据导出
        /// </summary>
        private void miExportWholeExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(RowDatasList, sheetNames);
        }

        /// <summary>
        /// 天线分析数据导出csv
        /// </summary>
        private void 拆分导出CSVToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ZTAntFuncHelper.OutputCsvFile(RowDatasList, sheetNames);
        }

        /// <summary>
        /// 呈现二维四象分析表
        /// </summary>
        private void miShowChart_Click(object sender, EventArgs e)
        {
            dgViewSinr.Rows.Clear();
            dgViewSample.Rows.Clear();
            dgViewCell.Columns.Clear();
            dgViewCell.Rows.Clear();
            int idx = 0;
            foreach (object obj in RowDatasList[3][0].cellValues)
            {
                dgViewCell.Columns.Add(idx++.ToString(), obj.ToString());
            }

            string strGrid = "";
            if (this.scanAntDataXTCtrl.SelectedTabPageIndex == 0)
            {
                int iRowId = dgViewGrid.SelectedCells[0].RowIndex;
                strGrid = dgViewGrid.Rows[iRowId].Tag.ToString();


                ZTLteAntennaCoverStruct.SampleData sampleData;
                if (AllSampleDataDic.TryGetValue(strGrid, out sampleData))
                {
                    addDgvRowCell(sampleData);
                }

                List<ZTLteAntennaCoverStruct.CellAntData> cellList;
                if (cellGridDic.TryGetValue(strGrid, out cellList))
                {
                    fillCellData(cellList);
                }
                this.scanAntDataXTCtrl.SelectedTabPageIndex = 1;
            }
        }

        private void addDgvRowCell(ZTLteAntennaCoverStruct.SampleData sampleData)
        {
            string[] rsrpLegend = { ">=65","[-70,-65)","[-75,-70)","[-80,-75)","[-85,-80)","[-90,-85)",
                                        "[-95,-90)","[-100,-95)","[-105,-100)","[-110,-105)","[-115,-110)",
                                        "[-120,-115)","<-120" };

            for (int i = 0; i < 13; i++)
            {
                DataGridViewTextBoxCell legendCell = new DataGridViewTextBoxCell();
                legendCell.Value = rsrpLegend[i].ToString();
                DataGridViewRow dgSampleRow = new DataGridViewRow();
                dgSampleRow.Cells.Add(legendCell);
                for (int j = 0; j < 6; j++)
                {
                    DataGridViewTextBoxCell cell = new DataGridViewTextBoxCell();
                    cell.Value = sampleData.DMultiSampleRate[i, j];
                    cell.Style = getLegendColor(i, j);
                    dgSampleRow.Cells.Add(cell);
                }
                dgViewSample.Rows.Add(dgSampleRow);

                DataGridViewTextBoxCell legendSinr = new DataGridViewTextBoxCell();
                legendSinr.Value = rsrpLegend[i].ToString();
                DataGridViewRow dgSinrRow = new DataGridViewRow();
                dgSinrRow.Cells.Add(legendSinr);
                for (int j = 0; j < 6; j++)
                {
                    DataGridViewTextBoxCell cell = new DataGridViewTextBoxCell();
                    cell.Value = sampleData.DMultiSinr[i, j];
                    cell.Style = getLegendColor(i, j);
                    dgSinrRow.Cells.Add(cell);
                }
                dgViewSinr.Rows.Add(dgSinrRow);
            }
        }

        /// <summary>
        /// dgViewCell数据填值
        /// </summary>
        private void fillCellData(List<ZTLteAntennaCoverStruct.CellAntData> cellList)
        {
            int idx = 0;
            foreach (ZTLteAntennaCoverStruct.CellAntData cellData in cellList)
            {
                ZTLteAntennaCoverStruct.GridCellKey gKey = new ZTLteAntennaCoverStruct.GridCellKey();
                gKey.strGridName = cellData.strGrid;
                gKey.strCellName = cellData.strCellName;

                dgViewCell.Rows.Add(1);
                int iCellId = 0;
                dgViewCell.Rows[idx].Tag = gKey;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = idx + 1;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.strGrid;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.strCellName;
                if (cellData.lteCell != null)
                {
                    dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.lteCell.ECI;
                    dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.lteCell.SectorID;
                }
                else
                {
                    dgViewCell.Rows[idx].Cells[iCellId++].Value = "";
                    dgViewCell.Rows[idx].Cells[iCellId++].Value = "";
                }

                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.iEarfcn;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.iPci;

                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.sampleData.iSampleTotalNum;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.iSampleNum;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FProbPointRate;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FRsrp;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FSinr;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FCover;

                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.iWeakLowNum;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FWeakLowRate;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FWeakLowRsrp;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FWeakLowSinr;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FWeakLowCover;

                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.iWeakHighNum;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FWeakHighRate;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FWeakHighRsrp;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FWeakHighSinr;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FWeakHighCover;

                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.iGoodHighNum;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FGoodHighRate;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FGoodHighRsrp;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FGoodHighSinr;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FGoodHighCover;

                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.iGoodMod3Num;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FGoodMod3Rate;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FGoodMod3Rsrp;
                dgViewCell.Rows[idx].Cells[iCellId++].Value = cellData.cellAntStat.FGoodMod3Sinr;
                dgViewCell.Rows[idx].Cells[iCellId].Value = cellData.cellAntStat.FGoodMod3Cover;
                idx++;
            }
        }

        /// <summary>
        /// 获取颜色
        /// </summary>
        private DataGridViewCellStyle getLegendColor(int iRsrp, int iCover)
        {
            DataGridViewCellStyle sty = new DataGridViewCellStyle();
            AntLegend legend = ZTAntFuncHelper.GetScanStructLegend(iRsrp, iCover, RsrpSplit);
            sty.BackColor = legend.colorType;
            return sty;
        }

        /// <summary>
        /// 切换TAB时触发
        /// </summary>
        private void scanAntDataXTCtrl_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            if (scanAntDataXTCtrl.SelectedTabPageIndex == 0)
            {
                miShowChart.Visible = true;
                miShowGis.Visible = true;
                miExportWholeExcel.Visible = true;
                拆分导出CSVToolStripMenuItem.Visible = true;
            }
            else if (scanAntDataXTCtrl.SelectedTabPageIndex == 1 || scanAntDataXTCtrl.SelectedTabPageIndex == 2|| scanAntDataXTCtrl.SelectedTabPageIndex == 3)
            {
                miShowChart.Visible = false;
                miShowGis.Visible = true;
                miExportWholeExcel.Visible = true;
                拆分导出CSVToolStripMenuItem.Visible = true;
            }
        }
    }
}
