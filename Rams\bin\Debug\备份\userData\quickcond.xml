<?xml version="1.0"?>
<Configs>
  <Config name="Quicks">
    <Item name="QuickCondSavers" typeName="IList">
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635678051020697203</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">武汉低速率路段</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635241312000000000</Item>
              <Item typeName="String" key="EndTime">635319935990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">33</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635682640247675345</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">LTE呼叫时延</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635241312000000000</Item>
              <Item typeName="String" key="EndTime">635530751990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">33</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635724715022523956</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">武汉LTE语音</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635332032000000000</Item>
              <Item typeName="String" key="EndTime">635491836000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">33</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635966812979636825</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">多重对比</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635016672000000000</Item>
              <Item typeName="String" key="EndTime">635478012000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635998591177442615</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">广州同车测速率</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635977440000000000</Item>
              <Item typeName="String" key="EndTime">635980860000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">33</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636003011118797659</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">新疆单站</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635785632000000000</Item>
              <Item typeName="String" key="EndTime">635787324000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636015148706247746</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">广州同车测试异常</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635986944000000000</Item>
              <Item typeName="String" key="EndTime">635988636000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636069711639984232</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">广州Volte和Dl同车测试</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636061248000000000</Item>
              <Item typeName="String" key="EndTime">636068124000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636089599063888239</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">贵州隧道测试</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636069024000000000</Item>
              <Item typeName="String" key="EndTime">636075900000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636090998598133637</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">延庆白名单</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636063840000000000</Item>
              <Item typeName="String" key="EndTime">636075900000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636205081471686190</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">模三干扰</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635292288000000000</Item>
              <Item typeName="String" key="EndTime">635293116000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636220937876297837</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">北京流媒体</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636188256000000000</Item>
              <Item typeName="String" key="EndTime">636240060000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636252602849877769</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">新疆库尔勒室外</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636164928000000000</Item>
              <Item typeName="String" key="EndTime">636166620000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">51</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636284949840050682</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">关联小区性能</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636239232000000000</Item>
              <Item typeName="String" key="EndTime">636258204000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
            <Item typeName="Int32">51</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636286280066478259</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">武汉关联小区告警</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635766624000000000</Item>
              <Item typeName="String" key="EndTime">635792508000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">37</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636410138526365416</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">扫频频谱分析</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636327360000000000</Item>
              <Item typeName="String" key="EndTime">636328188000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636476596830339286</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">北京回落非最佳</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635923872000000000</Item>
              <Item typeName="String" key="EndTime">635949756000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">33</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636561444586175211</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">武汉语音</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636188256000000000</Item>
              <Item typeName="String" key="EndTime">636477695990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
            <Item typeName="Int32">51</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">39</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636712321099981682</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">广州道路质量分析</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636188256000000000</Item>
              <Item typeName="String" key="EndTime">636424991990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">43</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">4</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635700480161756797</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">北京高速黑点</Item>
          <Item typeName="String" key="GroupName">未分类</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635568768000000000</Item>
              <Item typeName="String" key="EndTime">635700924000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">46</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635736801264016965</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">FDD数据</Item>
          <Item typeName="String" key="GroupName">未分类</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635488416000000000</Item>
              <Item typeName="String" key="EndTime">635490972000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635739541617079879</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">北京GSM语音</Item>
          <Item typeName="String" key="GroupName">未分类</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635569632000000000</Item>
              <Item typeName="String" key="EndTime">635727743990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">6</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">35</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635749676461651581</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">LTE扫频</Item>
          <Item typeName="String" key="GroupName">未分类</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635673312000000000</Item>
              <Item typeName="String" key="EndTime">635754492000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">6</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">4</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635757490979596842</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">深圳TD</Item>
          <Item typeName="String" key="GroupName">未分类</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635440032000000000</Item>
              <Item typeName="String" key="EndTime">635757084000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">4</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635772314970285519</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">乌鲁木齐3G</Item>
          <Item typeName="String" key="GroupName">未分类</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635016672000000000</Item>
              <Item typeName="String" key="EndTime">635491836000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">5</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635918541770395532</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">TD下载</Item>
          <Item typeName="String" key="GroupName">未分类</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635254272000000000</Item>
              <Item typeName="String" key="EndTime">635680188000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">35</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635997170207599602</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">云南扫频</Item>
          <Item typeName="String" key="GroupName">未分类</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635989536000000000</Item>
              <Item typeName="String" key="EndTime">635992092000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636047287611889206</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">乌鲁木齐GSM</Item>
          <Item typeName="String" key="GroupName">未分类</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635599872000000000</Item>
              <Item typeName="String" key="EndTime">635652540000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">4</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">0</Item>
          <Item key="RegionId" />
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">测试管理设备核查</Item>
          <Item typeName="String" key="GroupName">未分类</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636069024000000000</Item>
              <Item typeName="String" key="EndTime">636071580000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">46</Item>
            <Item typeName="Int32">47</Item>
            <Item typeName="Int32">45</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636087712367886626</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">武汉FDD</Item>
          <Item typeName="String" key="GroupName">未分类</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635852160000000000</Item>
              <Item typeName="String" key="EndTime">635865084000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636241374158704067</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">武汉GSM乒乓切换</Item>
          <Item typeName="String" key="GroupName">未分类</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635490144000000000</Item>
              <Item typeName="String" key="EndTime">635523804000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">12</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636338276269043210</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">武汉GSM扫频</Item>
          <Item typeName="String" key="GroupName">未分类</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635397696000000000</Item>
              <Item typeName="String" key="EndTime">635406300000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">35</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636857484619824579</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">武汉LTE扫频</Item>
          <Item typeName="String" key="GroupName">未分类</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636253920000000000</Item>
              <Item typeName="String" key="EndTime">636255647990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635845622591072718</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">北京VoLte</Item>
          <Item typeName="String" key="GroupName">VoLTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635673312000000000</Item>
              <Item typeName="String" key="EndTime">635807196000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635845871898362115</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">广东VoLTE-Mos</Item>
          <Item typeName="String" key="GroupName">VoLTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635623200000000000</Item>
              <Item typeName="String" key="EndTime">635803740000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635845872479315344</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">广州VoLTE</Item>
          <Item typeName="String" key="GroupName">VoLTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635623200000000000</Item>
              <Item typeName="String" key="EndTime">635784732000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635845873051378064</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">eSRVCC无线性能分析</Item>
          <Item typeName="String" key="GroupName">VoLTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635623200000000000</Item>
              <Item typeName="String" key="EndTime">635625756000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635846643354769182</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">Esrvcc</Item>
          <Item typeName="String" key="GroupName">VoLTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635556672000000000</Item>
              <Item typeName="String" key="EndTime">635837436000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635895959863790533</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">武汉VoLte</Item>
          <Item typeName="String" key="GroupName">VoLTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635812416000000000</Item>
              <Item typeName="String" key="EndTime">635872860000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635919964409576720</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">云浮</Item>
          <Item typeName="String" key="GroupName">VoLTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635812416000000000</Item>
              <Item typeName="String" key="EndTime">635899644000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635920178717134411</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">广州智能预判</Item>
          <Item typeName="String" key="GroupName">VoLTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635812416000000000</Item>
              <Item typeName="String" key="EndTime">635899644000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635925276465913908</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">VOLTE指标分析</Item>
          <Item typeName="String" key="GroupName">VoLTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635896224000000000</Item>
              <Item typeName="String" key="EndTime">635924700000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">635968291887847100</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">mos</Item>
          <Item typeName="String" key="GroupName">VoLTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635923872000000000</Item>
              <Item typeName="String" key="EndTime">635951484000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636027308987048752</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">mos周期导出</Item>
          <Item typeName="String" key="GroupName">VoLTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636003360000000000</Item>
              <Item typeName="String" key="EndTime">636014556000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636081719401706650</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">同车测试</Item>
          <Item typeName="String" key="GroupName">VoLTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636033600000000000</Item>
              <Item typeName="String" key="EndTime">636041340000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">0</Item>
          <Item key="RegionId" />
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">RTP丢包</Item>
          <Item typeName="String" key="GroupName">VoLTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636169248000000000</Item>
              <Item typeName="String" key="EndTime">636170940000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636240512817657417</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">RTP</Item>
          <Item typeName="String" key="GroupName">VoLTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636063840000000000</Item>
              <Item typeName="String" key="EndTime">636064668000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">0</Item>
          <Item key="RegionId" />
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">RTP所有信令</Item>
          <Item typeName="String" key="GroupName">VoLTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636221088000000000</Item>
              <Item typeName="String" key="EndTime">636221916000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
            <Item typeName="Int32">51</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636531120661629196</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">Mos-RTP分析</Item>
          <Item typeName="String" key="GroupName">VoLTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636483744000000000</Item>
              <Item typeName="String" key="EndTime">636484607990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">43</Item>
            <Item typeName="Int32">51</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636846154462024535</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">volte掉话智能预判分析</Item>
          <Item typeName="String" key="GroupName">VoLTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636818976000000000</Item>
              <Item typeName="String" key="EndTime">636846623990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">45</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636091212236288521</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">天津FDD语音</Item>
          <Item typeName="String" key="GroupName">联通</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635979168000000000</Item>
              <Item typeName="String" key="EndTime">635985180000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">28</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636118717404233745</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">天津W</Item>
          <Item typeName="String" key="GroupName">联通</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635976576000000000</Item>
              <Item typeName="String" key="EndTime">635985180000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636119857369577829</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">W语音</Item>
          <Item typeName="String" key="GroupName">联通</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635976576000000000</Item>
              <Item typeName="String" key="EndTime">636037884000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">28</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636119857875536769</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">W数据</Item>
          <Item typeName="String" key="GroupName">联通</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635976576000000000</Item>
              <Item typeName="String" key="EndTime">636037884000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">46</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636122970529115494</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">FDD数据</Item>
          <Item typeName="String" key="GroupName">联通</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635976576000000000</Item>
              <Item typeName="String" key="EndTime">635996412000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">45</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636179382064681492</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">FDD语音CSFB呼叫时延</Item>
          <Item typeName="String" key="GroupName">联通</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635559264000000000</Item>
              <Item typeName="String" key="EndTime">636170076000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">12</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636474573889290946</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">天津W语音-切换问题</Item>
          <Item typeName="String" key="GroupName">联通</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">635976576000000000</Item>
              <Item typeName="String" key="EndTime">636037884000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">12</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">21</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636474605693450040</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">浙江W扫频</Item>
          <Item typeName="String" key="GroupName">联通</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">634082688000000000</Item>
              <Item typeName="String" key="EndTime">634775580000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">39</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636474715813598552</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">天津GSM语音</Item>
          <Item typeName="String" key="GroupName">联通</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636266016000000000</Item>
              <Item typeName="String" key="EndTime">636300540000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">12</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">21</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636476340728061074</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">杭州联通W扫频</Item>
          <Item typeName="String" key="GroupName">联通</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">634151808000000000</Item>
              <Item typeName="String" key="EndTime">634161276000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636476470990651668</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">天津W语音转扫频</Item>
          <Item typeName="String" key="GroupName">联通</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636450912000000000</Item>
              <Item typeName="String" key="EndTime">636476796000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
            <Item typeName="Int32">51</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">0</Item>
          <Item key="RegionId" />
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">贵阳</Item>
          <Item typeName="String" key="GroupName">LTE单站</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636385248000000000</Item>
              <Item typeName="String" key="EndTime">636386940000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
            <Item typeName="Int32">51</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">0</Item>
          <Item key="RegionId" />
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">遵义</Item>
          <Item typeName="String" key="GroupName">LTE单站</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636385248000000000</Item>
              <Item typeName="String" key="EndTime">636386940000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
            <Item typeName="Int32">51</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">0</Item>
          <Item key="RegionId" />
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">六盘水</Item>
          <Item typeName="String" key="GroupName">LTE单站</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636371424000000000</Item>
              <Item typeName="String" key="EndTime">636373116000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
            <Item typeName="Int32">51</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">0</Item>
          <Item key="RegionId" />
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">毕节</Item>
          <Item typeName="String" key="GroupName">LTE单站</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636344640000000000</Item>
              <Item typeName="String" key="EndTime">636385212000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
            <Item typeName="Int32">51</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636453122953399200</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">新疆小站单验-奎屯迎宾园</Item>
          <Item typeName="String" key="GroupName">LTE单站</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636422400000000000</Item>
              <Item typeName="String" key="EndTime">636447420000000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
            <Item typeName="Int32">51</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">0</Item>
          <Item key="RegionId" />
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">新疆小站单元楼层</Item>
          <Item typeName="String" key="GroupName">LTE单站</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636497568000000000</Item>
              <Item typeName="String" key="EndTime">636498431990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">55</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">0</Item>
          <Item key="RegionId" />
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">NBIOT测试</Item>
          <Item typeName="String" key="GroupName">NBIOT</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636594336000000000</Item>
              <Item typeName="String" key="EndTime">636595199990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">55</Item>
            <Item typeName="Int32">56</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">0</Item>
          <Item key="RegionId" />
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">广东NB回放卡慢</Item>
          <Item typeName="String" key="GroupName">NBIOT</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636806880000000000</Item>
              <Item typeName="String" key="EndTime">637123967990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">55</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636842693276206215</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">秦皇岛NB扫频</Item>
          <Item typeName="String" key="GroupName">NBIOT</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636608160000000000</Item>
              <Item typeName="String" key="EndTime">636610751990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">56</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636842954922411522</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">NB数据业务</Item>
          <Item typeName="String" key="GroupName">NBIOT</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636565824000000000</Item>
              <Item typeName="String" key="EndTime">636883775990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">56</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636843801484013869</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">莱芜NB数据业务</Item>
          <Item typeName="String" key="GroupName">NBIOT</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636607296000000000</Item>
              <Item typeName="String" key="EndTime">636615935990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">55</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636843873834752094</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">山东菏泽NB扫频</Item>
          <Item typeName="String" key="GroupName">NBIOT</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636621984000000000</Item>
              <Item typeName="String" key="EndTime">636624575990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
            <Item typeName="Int32">51</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">636935114689210781</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">广州测试</Item>
          <Item typeName="String" key="GroupName">LTE-2019</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636818976000000000</Item>
              <Item typeName="String" key="EndTime">636846623990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">46</Item>
            <Item typeName="Int32">47</Item>
            <Item typeName="Int32">48</Item>
            <Item typeName="Int32">45</Item>
            <Item typeName="Int32">49</Item>
            <Item typeName="Int32">52</Item>
            <Item typeName="Int32">51</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">637027581873453949</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">上海FDD</Item>
          <Item typeName="String" key="GroupName">未分类</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636503616000000000</Item>
              <Item typeName="String" key="EndTime">637029791990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
            <Item typeName="Int32">51</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">637086496325918289</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">成都测试</Item>
          <Item typeName="String" key="GroupName">LTE-2019</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636818976000000000</Item>
              <Item typeName="String" key="EndTime">637108415990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">58</Item>
            <Item typeName="Int32">57</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">0</Item>
          <Item key="RegionId" />
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">5G测试数据</Item>
          <Item typeName="String" key="GroupName">5G</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636946848000000000</Item>
              <Item typeName="String" key="EndTime">637107551990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">35</Item>
            <Item typeName="Int32">36</Item>
            <Item typeName="Int32">37</Item>
            <Item typeName="Int32">38</Item>
            <Item typeName="Int32">39</Item>
            <Item typeName="Int32">40</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">58</Item>
            <Item typeName="Int32">57</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">637108730183326186</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">低速率路段</Item>
          <Item typeName="String" key="GroupName">5G</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">637081632000000000</Item>
              <Item typeName="String" key="EndTime">637107551990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">35</Item>
            <Item typeName="Int32">36</Item>
            <Item typeName="Int32">37</Item>
            <Item typeName="Int32">38</Item>
            <Item typeName="Int32">39</Item>
            <Item typeName="Int32">40</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">58</Item>
            <Item typeName="Int32">57</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">637110550903715436</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">北京5G文件</Item>
          <Item typeName="String" key="GroupName">5G</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">637081632000000000</Item>
              <Item typeName="String" key="EndTime">637107551990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">35</Item>
            <Item typeName="Int32">36</Item>
            <Item typeName="Int32">37</Item>
            <Item typeName="Int32">38</Item>
            <Item typeName="Int32">39</Item>
            <Item typeName="Int32">40</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">56</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">637147986474451656</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">宁夏NB数据</Item>
          <Item typeName="String" key="GroupName">NBIOT</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">637107552000000000</Item>
              <Item typeName="String" key="EndTime">637135199990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">35</Item>
            <Item typeName="Int32">36</Item>
            <Item typeName="Int32">37</Item>
            <Item typeName="Int32">38</Item>
            <Item typeName="Int32">39</Item>
            <Item typeName="Int32">40</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">56</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">637148643043075234</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">宁夏NB数据2</Item>
          <Item typeName="String" key="GroupName">NBIOT</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636818976000000000</Item>
              <Item typeName="String" key="EndTime">637135199990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">35</Item>
            <Item typeName="Int32">36</Item>
            <Item typeName="Int32">37</Item>
            <Item typeName="Int32">38</Item>
            <Item typeName="Int32">39</Item>
            <Item typeName="Int32">40</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
            <Item typeName="Int32">51</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">637320602472051802</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">北京2018文件</Item>
          <Item typeName="String" key="GroupName">LTE</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">636504480000000000</Item>
              <Item typeName="String" key="EndTime">636794783990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">35</Item>
            <Item typeName="Int32">36</Item>
            <Item typeName="Int32">37</Item>
            <Item typeName="Int32">38</Item>
            <Item typeName="Int32">39</Item>
            <Item typeName="Int32">40</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">35</Item>
            <Item typeName="Int32">38</Item>
            <Item typeName="Int32">37</Item>
            <Item typeName="Int32">36</Item>
            <Item typeName="Int32">40</Item>
            <Item typeName="Int32">39</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">74</Item>
            <Item typeName="Int32">64</Item>
            <Item typeName="Int32">63</Item>
            <Item typeName="Int32">65</Item>
            <Item typeName="Int32">58</Item>
            <Item typeName="Int32">57</Item>
            <Item typeName="Int32">59</Item>
            <Item typeName="Int32">61</Item>
            <Item typeName="Int32">60</Item>
            <Item typeName="Int32">62</Item>
            <Item typeName="Int32">67</Item>
            <Item typeName="Int32">70</Item>
            <Item typeName="Int32">68</Item>
            <Item typeName="Int32">66</Item>
            <Item typeName="Int32">69</Item>
            <Item typeName="Int32">73</Item>
            <Item typeName="Int32">71</Item>
            <Item typeName="Int32">72</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">0</Item>
          <Item key="RegionId" />
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item typeName="String" key="TaskName" />
          <Item typeName="String" key="Name">NR宏站单验</Item>
          <Item typeName="String" key="GroupName">新疆单验</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">637869600000000000</Item>
              <Item typeName="String" key="EndTime">637895519990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">35</Item>
            <Item typeName="Int32">36</Item>
            <Item typeName="Int32">37</Item>
            <Item typeName="Int32">38</Item>
            <Item typeName="Int32">39</Item>
            <Item typeName="Int32">40</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">43</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">45</Item>
            <Item typeName="Int32">46</Item>
            <Item typeName="Int32">47</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">35</Item>
            <Item typeName="Int32">38</Item>
            <Item typeName="Int32">37</Item>
            <Item typeName="Int32">36</Item>
            <Item typeName="Int32">40</Item>
            <Item typeName="Int32">39</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">40</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">39</Item>
            <Item typeName="Int32">54</Item>
            <Item typeName="Int32">46</Item>
            <Item typeName="Int32">47</Item>
            <Item typeName="Int32">48</Item>
            <Item typeName="Int32">45</Item>
            <Item typeName="Int32">49</Item>
            <Item typeName="Int32">36</Item>
            <Item typeName="Int32">35</Item>
            <Item typeName="Int32">50</Item>
            <Item typeName="Int32">53</Item>
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
            <Item typeName="Int32">44</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">43</Item>
            <Item typeName="Int32">37</Item>
            <Item typeName="Int32">64</Item>
            <Item typeName="Int32">63</Item>
            <Item typeName="Int32">65</Item>
            <Item typeName="Int32">58</Item>
            <Item typeName="Int32">57</Item>
            <Item typeName="Int32">59</Item>
            <Item typeName="Int32">61</Item>
            <Item typeName="Int32">60</Item>
            <Item typeName="Int32">62</Item>
            <Item typeName="Int32">55</Item>
            <Item typeName="Int32">52</Item>
            <Item typeName="Int32">51</Item>
            <Item typeName="Int32">56</Item>
            <Item typeName="Int32">67</Item>
            <Item typeName="Int32">70</Item>
            <Item typeName="Int32">68</Item>
            <Item typeName="Int32">66</Item>
            <Item typeName="Int32">69</Item>
            <Item typeName="Int32">73</Item>
            <Item typeName="Int32">71</Item>
            <Item typeName="Int32">72</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">38</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">31</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">1</Item>
          <Item typeName="String" key="RegionId">637810353596733311</Item>
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">0402011120220126093534ms3.l5g</Item>
          <Item typeName="String" key="GroupName">派单</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">637765920000000000</Item>
              <Item typeName="String" key="EndTime">637810847990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
            <Item typeName="Int32">35</Item>
            <Item typeName="Int32">36</Item>
            <Item typeName="Int32">37</Item>
            <Item typeName="Int32">38</Item>
            <Item typeName="Int32">39</Item>
            <Item typeName="Int32">40</Item>
            <Item typeName="Int32">41</Item>
            <Item typeName="Int32">42</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="CondSaverItem">
        <Item name="Saver" typeName="IDictionary">
          <Item typeName="Int32" key="AutoRun">1</Item>
          <Item typeName="Int32" key="TimeType">0</Item>
          <Item typeName="Int32" key="DayValue">0</Item>
          <Item typeName="IList" key="Projects">
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="IList" key="Services">
            <Item typeName="Int32">55</Item>
          </Item>
          <Item typeName="Int32" key="TargetType">0</Item>
          <Item key="RegionId" />
          <Item key="StreetId" />
          <Item key="CellName" />
          <Item key="TaskName" />
          <Item typeName="String" key="Name">遍历率统计</Item>
          <Item typeName="String" key="GroupName">NBIOT</Item>
          <Item typeName="IList" key="TimePeriods">
            <Item typeName="IDictionary">
              <Item typeName="String" key="BeginTime">637739136000000000</Item>
              <Item typeName="String" key="EndTime">637765919990000000</Item>
            </Item>
          </Item>
          <Item key="KpiDic" />
          <Item typeName="Double" key="Radius">0</Item>
          <Item typeName="IList" key="Areas" />
          <Item typeName="IList" key="Devices">
            <Item typeName="Int32">1</Item>
            <Item typeName="Int32">2</Item>
            <Item typeName="Int32">3</Item>
            <Item typeName="Int32">4</Item>
            <Item typeName="Int32">5</Item>
            <Item typeName="Int32">6</Item>
            <Item typeName="Int32">7</Item>
            <Item typeName="Int32">8</Item>
            <Item typeName="Int32">9</Item>
            <Item typeName="Int32">10</Item>
            <Item typeName="Int32">11</Item>
            <Item typeName="Int32">12</Item>
            <Item typeName="Int32">13</Item>
            <Item typeName="Int32">14</Item>
            <Item typeName="Int32">15</Item>
            <Item typeName="Int32">16</Item>
            <Item typeName="Int32">17</Item>
            <Item typeName="Int32">18</Item>
            <Item typeName="Int32">19</Item>
            <Item typeName="Int32">20</Item>
            <Item typeName="Int32">21</Item>
            <Item typeName="Int32">22</Item>
            <Item typeName="Int32">23</Item>
            <Item typeName="Int32">24</Item>
            <Item typeName="Int32">25</Item>
            <Item typeName="Int32">26</Item>
            <Item typeName="Int32">27</Item>
            <Item typeName="Int32">28</Item>
            <Item typeName="Int32">29</Item>
            <Item typeName="Int32">30</Item>
            <Item typeName="Int32">31</Item>
            <Item typeName="Int32">32</Item>
            <Item typeName="Int32">33</Item>
            <Item typeName="Int32">34</Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>