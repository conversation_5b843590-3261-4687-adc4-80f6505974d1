﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Data.SqlClient;
using MasterCom.RAMS.Util;
using MasterCom.Util;

namespace MasterCom.RAMS.src.MasterCom.RAMS.ZTFunc.Save4Use
{
    public partial class Save4UseMngForm : Form
    {
        private String ConnectionString;
        public Save4UseMngForm(String connStr)
        {
            InitializeComponent();
            this.ConnectionString = connStr;
        }

        private void Save4UseMngForm_Load(object sender, EventArgs e)
        {
            reloadData();
        }

        private void btnDeleteSel_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count > 0)
            {
                DataGridViewRow dvrow = dataGridView.SelectedRows[0];
                try
                {
                    int id = (int)dvrow.Cells["ID"].Value;
                    String name = (String)dvrow.Cells["名称"].Value;
                    if (MessageBox.Show(this, "删除所选的记录：ID=" + id + "  Name=" + name + "？", "删除", MessageBoxButtons.OKCancel) == DialogResult.OK)
                    {
                        string sql = @"delete  FROM [MTNOH_APP_ANA].[dbo].[TB_DT_Grid_Index] where id = " + id;
                        SqlHelper.ExecuteNonQuery(ConnectionString, CommandType.Text, sql);
                        sql = @"delete  FROM [MTNOH_APP_ANA].[dbo].[TB_DT_Grid_TileLayer] where id = " + id;
                        SqlHelper.ExecuteNonQuery(ConnectionString, CommandType.Text, sql);
                        sql = @"delete  FROM [MTNOH_APP_ANA].[dbo].[TB_DT_Grid_Legend] where id = " + id;
                        SqlHelper.ExecuteNonQuery(ConnectionString, CommandType.Text, sql);
                        reloadData();
                    }
                }
                catch
                {
                    //continue
                }
            }
        }

        private void reloadData()
        {
            SqlConnection connection = new SqlConnection(ConnectionString);
            try
            {
                if (connection.State == ConnectionState.Closed)
                {
                    connection.Open();
                }
                string sql = @"select id,count(1) as ct from [MTNOH_APP_ANA].[dbo].[TB_DT_Grid_TileLayer] group by id";
                Dictionary<int, int> countDic = new Dictionary<int, int>();
                using (SqlDataReader reader = SqlHelper.ExecuteReader(connection, CommandType.Text, sql))
                {
                    while (reader.Read())
                    {
                        int id = reader.GetInt32(0);
                        int ct = reader.GetInt32(1);
                        countDic[id] = ct;
                    }
                }
                sql = @"select [id] as ID,[type] as 类型,[cityid] as 地市ID,[name] 名称,0 as Tile数量 FROM [MTNOH_APP_ANA].[dbo].[TB_DT_Grid_Index] ";
                DataSet ds = SqlHelper.ExecuteDataset(connection, CommandType.Text, sql);
                DataTable dt = ds.Tables[0];
                foreach (DataRow drow in dt.Rows)
                {
                    int id = (int)drow["ID"];
                    int ct = 0;
                    if (countDic.TryGetValue(id, out ct))
                    {
                        drow["Tile数量"] = ct;
                    }
                }
                dataGridView.DataSource = dt;
            }
            catch
            {
                //continue
            }
            finally
            {
                connection.Close();
            }
        }
    }
}
