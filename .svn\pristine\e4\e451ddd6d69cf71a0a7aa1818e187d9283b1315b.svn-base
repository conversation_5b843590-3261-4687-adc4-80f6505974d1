﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.Func
{
    public partial class MultiTimePeriodChooser : BaseDialog
    {
        public MultiTimePeriodChooser()
        {
            InitializeComponent();

            this.Load += Form_Load;
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            btnAdd.Click += BtnAdd_Click;
            btnDel.Click += BtnDel_Click;
            listBoxTimePeriods.SelectedIndexChanged += ListBox_SelectedChanged;

        }

        public List<TimePeriod> TimePeriods
        {
            get { return periodList; }
        }

        private void Form_Load(object sender, EventArgs e)
        {
            btnDel.Enabled = false;
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            DateTime startDt;
            DateTime endDt;
            GetTimes(out startDt, out endDt);
            if (endDt < startDt)
            {
                MessageBox.Show("结束时间不能早于开始时间", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            TimePeriod curPeriod = new TimePeriod(startDt, endDt);
            if (IsIntersect(curPeriod))
            {
                if (DialogResult.No == MessageBox.Show("添加时间段与当前已有时间段发生重叠，是否合并？", "合并时间段", MessageBoxButtons.YesNo, MessageBoxIcon.Question))
                {
                    return;
                }
                InsertToList(curPeriod);
                MergePeriod();
            }
            else
            {
                InsertToList(curPeriod);
            }
            RefreshListBox();
        }

        private void BtnDel_Click(object sender, EventArgs e)
        {
            periodList.RemoveAt(listBoxTimePeriods.SelectedIndex);
            listBoxTimePeriods.Items.RemoveAt(listBoxTimePeriods.SelectedIndex);
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (periodList.Count == 0)
            {
                MessageBox.Show("至少选择一个时间段", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.None;
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void ListBox_SelectedChanged(object sender, EventArgs e)
        {
            btnDel.Enabled = listBoxTimePeriods.SelectedItem != null;
        }

        private void GetTimes(out DateTime startDt, out DateTime endDt)
        {
            DateTime sd = pickerDateStart.Value;
            DateTime st = pickerTimeStart.Value;
            startDt = new DateTime(sd.Year, sd.Month, sd.Day, st.Hour, st.Minute, st.Second);

            DateTime ed = pickerDateEnd.Value;
            DateTime et = pickerTimeEnd.Value;
            endDt = new DateTime(ed.Year, ed.Month, ed.Day, et.Hour, et.Minute, et.Second);
        }

        private void RefreshListBox()
        {
            listBoxTimePeriods.Items.Clear();
            foreach (TimePeriod tp in periodList)
            {
                listBoxTimePeriods.Items.Add(tp.GetShortString());
            }
            ListBox_SelectedChanged(listBoxTimePeriods, EventArgs.Empty);
        }

        private bool IsIntersect(TimePeriod tp)
        {
            foreach (TimePeriod period in periodList)
            {
                if (tp.EndTime >= period.BeginTime && tp.BeginTime <= period.EndTime)
                {
                    return true;
                }
            }
            return false;
        }

        private void InsertToList(TimePeriod tp)
        {
            int i = 0;
            for (; i < periodList.Count; ++i)
            {
                TimePeriod period = periodList[i];
                if (tpComparer.Compare(tp, period) <= 0)
                {
                    break;
                }
            }
            periodList.Insert(i, tp);
        }

        private void MergePeriod()
        {
            if (periodList.Count < 2)
            {
                return;
            }

            List<TimePeriod> tarList = new List<TimePeriod>();
            for (int i = 0, j = 1; i < periodList.Count - 1; ++i, ++j)
            {
                TimePeriod tp1 = periodList[i];
                TimePeriod tp2 = periodList[j];
                if (tp1.EndTime < tp2.BeginTime)
                {
                    tarList.Add(tp1);
                    continue;
                }

                DateTime endTime = tp1.EndTime > tp2.EndTime ? tp1.EndTime : tp2.EndTime;
                periodList[j] = new TimePeriod(tp1.BeginTime, endTime);
                if (j == periodList.Count - 1)
                {
                    tarList.Add(periodList[j]);
                }
            }

            periodList.Clear();
            periodList.AddRange(tarList);
        }

        private List<TimePeriod> periodList = new List<TimePeriod>();

        private TimePeriodComparer tpComparer = new TimePeriodComparer();

        private class TimePeriodComparer : IComparer<TimePeriod>
        {
            public int Compare(TimePeriod x, TimePeriod y)
            {
                if (x == null && y == null)
                {
                    return 0;
                }
                else if (x == null)
                {
                    return -1;
                }
                else if (y == null)
                {
                    return 1;
                }

                if (x.BeginTime == y.BeginTime)
                {
                    if (x.EndTime == y.EndTime)
                    {
                        return 0;
                    }
                    return x.EndTime > y.EndTime ? 1 : -1;
                }
                return x.BeginTime > y.BeginTime ? 1 : -1;
            }
        }
    }
}
