﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.NewBlackBlock
{
    public class BlackBlockExtend
    {
        public BlackBlockItem BlockItem
        {
            get;
            set;
        }
        public int BlockID
        {
            get;
            set;
        }
        private string causeMain = string.Empty;
        public string CauseMain
        {
            get { return causeMain; }
            set
            {
                if (!string.IsNullOrEmpty(value))
                {
                    causeMain = value;
                }
            }
        }
        private string causeSub = string.Empty;
        public string CauseSub
        {
            get { return causeSub; }
            set {
                if (!string.IsNullOrEmpty(value))
                {
                    causeSub = value;
                }
               }
        }
        private string causeDetail = string.Empty;
        public string CauseDetail
        {
            get { return causeDetail; }
            set
            {
                if (!string.IsNullOrEmpty(value))
                {
                    causeDetail = value;
                }
            }
        }
        public string Problem
        {
            get;
            set;
        }
        public string Solution
        {
            get;
            set;
        }
        public string Person
        {
            get;
            set;
        }
        public DateTime LastTime { get; set; } = JavaDate.JavaDateBase;
        public DateTime Plan2CloseTime { get; set; } = JavaDate.JavaDateBase;
        public string Token
        {
            get;
            set;
        }

        public string Plan2CloseTimeStr
        {
            get
            {
                return Plan2CloseTime != JavaDate.JavaDateBase ?
                    Plan2CloseTime.ToShortDateString() : string.Empty;
            }
        }

        public string LastTimeStr
        {
            get
            {
                return LastTime != JavaDate.JavaDateBase ?
                    LastTime.ToShortDateString() : string.Empty;
            }
        }

        public int CellHandledNum
        {
            get;
            set;
        }
        
        public string StatusByFile { get; set; } = string.Empty;

        public int HandleProgress
        { get; set; }

    }
}
