﻿namespace MasterCom.RAMS.Func
{
    partial class LTECellInfoForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.GroupBox groupBox1;
            System.Windows.Forms.Label label12;
            System.Windows.Forms.Label label9;
            System.Windows.Forms.Label label8;
            System.Windows.Forms.Label label6;
            System.Windows.Forms.Label label10;
            System.Windows.Forms.Label label7;
            System.Windows.Forms.Label label5;
            System.Windows.Forms.Label label4;
            System.Windows.Forms.Label label2;
            System.Windows.Forms.Label label1;
            System.Windows.Forms.Label label3;
            System.Windows.Forms.Label labelCI;
            System.Windows.Forms.Label label11;
            System.Windows.Forms.Label labelID;
            this.btnBTSInfo = new DevExpress.XtraEditors.SimpleButton();
            this.txtCode = new System.Windows.Forms.TextBox();
            this.txtName = new System.Windows.Forms.TextBox();
            this.txtBTS = new System.Windows.Forms.TextBox();
            this.txtFreqList = new System.Windows.Forms.TextBox();
            this.txtDesc = new System.Windows.Forms.TextBox();
            this.txtDirection = new System.Windows.Forms.TextBox();
            this.txtPCI = new System.Windows.Forms.TextBox();
            this.txtTAC = new System.Windows.Forms.TextBox();
            this.txtSectorID = new System.Windows.Forms.TextBox();
            this.txtEarfcn = new System.Windows.Forms.TextBox();
            this.txtECI = new System.Windows.Forms.TextBox();
            this.txtLat = new System.Windows.Forms.TextBox();
            this.txtLng = new System.Windows.Forms.TextBox();
            this.txtID = new System.Windows.Forms.TextBox();
            groupBox1 = new System.Windows.Forms.GroupBox();
            label12 = new System.Windows.Forms.Label();
            label9 = new System.Windows.Forms.Label();
            label8 = new System.Windows.Forms.Label();
            label6 = new System.Windows.Forms.Label();
            label10 = new System.Windows.Forms.Label();
            label7 = new System.Windows.Forms.Label();
            label5 = new System.Windows.Forms.Label();
            label4 = new System.Windows.Forms.Label();
            label2 = new System.Windows.Forms.Label();
            label1 = new System.Windows.Forms.Label();
            label3 = new System.Windows.Forms.Label();
            labelCI = new System.Windows.Forms.Label();
            label11 = new System.Windows.Forms.Label();
            labelID = new System.Windows.Forms.Label();
            groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(this.btnBTSInfo);
            groupBox1.Controls.Add(label12);
            groupBox1.Controls.Add(label9);
            groupBox1.Controls.Add(label8);
            groupBox1.Controls.Add(this.txtCode);
            groupBox1.Controls.Add(this.txtName);
            groupBox1.Controls.Add(this.txtBTS);
            groupBox1.Controls.Add(this.txtFreqList);
            groupBox1.Controls.Add(this.txtDesc);
            groupBox1.Controls.Add(this.txtDirection);
            groupBox1.Controls.Add(this.txtPCI);
            groupBox1.Controls.Add(this.txtTAC);
            groupBox1.Controls.Add(label6);
            groupBox1.Controls.Add(label10);
            groupBox1.Controls.Add(label7);
            groupBox1.Controls.Add(label5);
            groupBox1.Controls.Add(label4);
            groupBox1.Controls.Add(label2);
            groupBox1.Controls.Add(label1);
            groupBox1.Controls.Add(this.txtSectorID);
            groupBox1.Controls.Add(this.txtEarfcn);
            groupBox1.Controls.Add(this.txtECI);
            groupBox1.Controls.Add(label3);
            groupBox1.Controls.Add(labelCI);
            groupBox1.Controls.Add(this.txtLat);
            groupBox1.Controls.Add(this.txtLng);
            groupBox1.Controls.Add(this.txtID);
            groupBox1.Controls.Add(label11);
            groupBox1.Controls.Add(labelID);
            groupBox1.Location = new System.Drawing.Point(12, 12);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new System.Drawing.Size(304, 271);
            groupBox1.TabIndex = 1;
            groupBox1.TabStop = false;
            groupBox1.Text = "Info";
            // 
            // btnBTSInfo
            // 
            this.btnBTSInfo.Location = new System.Drawing.Point(163, 236);
            this.btnBTSInfo.Name = "btnBTSInfo";
            this.btnBTSInfo.Size = new System.Drawing.Size(45, 23);
            this.btnBTSInfo.TabIndex = 10;
            this.btnBTSInfo.Text = "&Info";
            this.btnBTSInfo.Click += new System.EventHandler(this.btnBTSInfo_Click);
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Location = new System.Drawing.Point(169, 79);
            label12.Name = "label12";
            label12.Size = new System.Drawing.Size(35, 12);
            label12.TabIndex = 4;
            label12.Text = "&纬度:";
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Location = new System.Drawing.Point(173, 49);
            label9.Name = "label9";
            label9.Size = new System.Drawing.Size(35, 12);
            label9.TabIndex = 4;
            label9.Text = "&Code:";
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Location = new System.Drawing.Point(29, 22);
            label8.Name = "label8";
            label8.Size = new System.Drawing.Size(35, 12);
            label8.TabIndex = 2;
            label8.Text = "&Name:";
            // 
            // txtCode
            // 
            this.txtCode.AcceptsReturn = true;
            this.txtCode.BackColor = System.Drawing.SystemColors.Window;
            this.txtCode.Location = new System.Drawing.Point(211, 46);
            this.txtCode.Name = "txtCode";
            this.txtCode.ReadOnly = true;
            this.txtCode.Size = new System.Drawing.Size(80, 21);
            this.txtCode.TabIndex = 5;
            this.txtCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // txtName
            // 
            this.txtName.BackColor = System.Drawing.SystemColors.Window;
            this.txtName.Location = new System.Drawing.Point(70, 19);
            this.txtName.Name = "txtName";
            this.txtName.ReadOnly = true;
            this.txtName.Size = new System.Drawing.Size(220, 21);
            this.txtName.TabIndex = 3;
            this.txtName.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // txtBTS
            // 
            this.txtBTS.BackColor = System.Drawing.SystemColors.Window;
            this.txtBTS.Location = new System.Drawing.Point(69, 237);
            this.txtBTS.Name = "txtBTS";
            this.txtBTS.ReadOnly = true;
            this.txtBTS.Size = new System.Drawing.Size(80, 21);
            this.txtBTS.TabIndex = 7;
            // 
            // txtFreqList
            // 
            this.txtFreqList.BackColor = System.Drawing.SystemColors.Window;
            this.txtFreqList.Location = new System.Drawing.Point(69, 208);
            this.txtFreqList.Name = "txtFreqList";
            this.txtFreqList.ReadOnly = true;
            this.txtFreqList.Size = new System.Drawing.Size(221, 21);
            this.txtFreqList.TabIndex = 7;
            // 
            // txtDesc
            // 
            this.txtDesc.BackColor = System.Drawing.SystemColors.Window;
            this.txtDesc.Location = new System.Drawing.Point(69, 181);
            this.txtDesc.Name = "txtDesc";
            this.txtDesc.ReadOnly = true;
            this.txtDesc.Size = new System.Drawing.Size(221, 21);
            this.txtDesc.TabIndex = 7;
            // 
            // txtDirection
            // 
            this.txtDirection.BackColor = System.Drawing.SystemColors.Window;
            this.txtDirection.Location = new System.Drawing.Point(70, 154);
            this.txtDirection.Name = "txtDirection";
            this.txtDirection.ReadOnly = true;
            this.txtDirection.Size = new System.Drawing.Size(80, 21);
            this.txtDirection.TabIndex = 7;
            // 
            // txtPCI
            // 
            this.txtPCI.BackColor = System.Drawing.SystemColors.Window;
            this.txtPCI.Location = new System.Drawing.Point(70, 127);
            this.txtPCI.Name = "txtPCI";
            this.txtPCI.ReadOnly = true;
            this.txtPCI.Size = new System.Drawing.Size(80, 21);
            this.txtPCI.TabIndex = 7;
            // 
            // txtTAC
            // 
            this.txtTAC.BackColor = System.Drawing.SystemColors.Window;
            this.txtTAC.Location = new System.Drawing.Point(70, 100);
            this.txtTAC.Name = "txtTAC";
            this.txtTAC.ReadOnly = true;
            this.txtTAC.Size = new System.Drawing.Size(80, 21);
            this.txtTAC.TabIndex = 7;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new System.Drawing.Point(35, 240);
            label6.Name = "label6";
            label6.Size = new System.Drawing.Size(29, 12);
            label6.TabIndex = 6;
            label6.Text = "&BTS:";
            // 
            // label10
            // 
            label10.AutoSize = true;
            label10.Location = new System.Drawing.Point(5, 211);
            label10.Name = "label10";
            label10.Size = new System.Drawing.Size(59, 12);
            label10.TabIndex = 6;
            label10.Text = "&FreqList:";
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Location = new System.Drawing.Point(29, 184);
            label7.Name = "label7";
            label7.Size = new System.Drawing.Size(35, 12);
            label7.TabIndex = 6;
            label7.Text = "&描述:";
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new System.Drawing.Point(17, 157);
            label5.Name = "label5";
            label5.Size = new System.Drawing.Size(47, 12);
            label5.TabIndex = 6;
            label5.Text = "&方向角:";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new System.Drawing.Point(161, 157);
            label4.Name = "label4";
            label4.Size = new System.Drawing.Size(47, 12);
            label4.TabIndex = 6;
            label4.Text = "&扇区号:";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new System.Drawing.Point(35, 130);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(29, 12);
            label2.TabIndex = 6;
            label2.Text = "&PCI:";
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(35, 103);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(29, 12);
            label1.TabIndex = 6;
            label1.Text = "&TAC:";
            // 
            // txtSectorID
            // 
            this.txtSectorID.BackColor = System.Drawing.SystemColors.Window;
            this.txtSectorID.Location = new System.Drawing.Point(211, 154);
            this.txtSectorID.Name = "txtSectorID";
            this.txtSectorID.ReadOnly = true;
            this.txtSectorID.Size = new System.Drawing.Size(80, 21);
            this.txtSectorID.TabIndex = 9;
            // 
            // txtEarfcn
            // 
            this.txtEarfcn.BackColor = System.Drawing.SystemColors.Window;
            this.txtEarfcn.Location = new System.Drawing.Point(211, 127);
            this.txtEarfcn.Name = "txtEarfcn";
            this.txtEarfcn.ReadOnly = true;
            this.txtEarfcn.Size = new System.Drawing.Size(80, 21);
            this.txtEarfcn.TabIndex = 9;
            // 
            // txtECI
            // 
            this.txtECI.BackColor = System.Drawing.SystemColors.Window;
            this.txtECI.Location = new System.Drawing.Point(211, 100);
            this.txtECI.Name = "txtECI";
            this.txtECI.ReadOnly = true;
            this.txtECI.Size = new System.Drawing.Size(80, 21);
            this.txtECI.TabIndex = 9;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new System.Drawing.Point(161, 130);
            label3.Name = "label3";
            label3.Size = new System.Drawing.Size(47, 12);
            label3.TabIndex = 8;
            label3.Text = "&EARFCN:";
            // 
            // labelCI
            // 
            labelCI.AutoSize = true;
            labelCI.Location = new System.Drawing.Point(179, 103);
            labelCI.Name = "labelCI";
            labelCI.Size = new System.Drawing.Size(29, 12);
            labelCI.TabIndex = 8;
            labelCI.Text = "&ECI:";
            // 
            // txtLat
            // 
            this.txtLat.BackColor = System.Drawing.SystemColors.Window;
            this.txtLat.Location = new System.Drawing.Point(210, 73);
            this.txtLat.Name = "txtLat";
            this.txtLat.ReadOnly = true;
            this.txtLat.Size = new System.Drawing.Size(80, 21);
            this.txtLat.TabIndex = 1;
            // 
            // txtLng
            // 
            this.txtLng.BackColor = System.Drawing.SystemColors.Window;
            this.txtLng.Location = new System.Drawing.Point(70, 73);
            this.txtLng.Name = "txtLng";
            this.txtLng.ReadOnly = true;
            this.txtLng.Size = new System.Drawing.Size(80, 21);
            this.txtLng.TabIndex = 1;
            // 
            // txtID
            // 
            this.txtID.BackColor = System.Drawing.SystemColors.Window;
            this.txtID.Location = new System.Drawing.Point(70, 46);
            this.txtID.Name = "txtID";
            this.txtID.ReadOnly = true;
            this.txtID.Size = new System.Drawing.Size(80, 21);
            this.txtID.TabIndex = 1;
            // 
            // label11
            // 
            label11.AutoSize = true;
            label11.Location = new System.Drawing.Point(29, 79);
            label11.Name = "label11";
            label11.Size = new System.Drawing.Size(35, 12);
            label11.TabIndex = 0;
            label11.Text = "&经度:";
            // 
            // labelID
            // 
            labelID.AutoSize = true;
            labelID.Location = new System.Drawing.Point(41, 49);
            labelID.Name = "labelID";
            labelID.Size = new System.Drawing.Size(23, 12);
            labelID.TabIndex = 0;
            labelID.Text = "&ID:";
            // 
            // LTECellInfoForm
            // 
            this.Appearance.Font = new System.Drawing.Font("SimSun", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(330, 295);
            this.Controls.Add(groupBox1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.Name = "LTECellInfoForm";
            this.Text = "LTE小区信息";
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TextBox txtCode;
        private System.Windows.Forms.TextBox txtName;
        private System.Windows.Forms.TextBox txtTAC;
        private System.Windows.Forms.TextBox txtECI;
        private System.Windows.Forms.TextBox txtID;
        private System.Windows.Forms.TextBox txtPCI;
        private System.Windows.Forms.TextBox txtSectorID;
        private System.Windows.Forms.TextBox txtEarfcn;
        private System.Windows.Forms.TextBox txtDirection;
        private DevExpress.XtraEditors.SimpleButton btnBTSInfo;
        private System.Windows.Forms.TextBox txtBTS;
        private System.Windows.Forms.TextBox txtFreqList;
        private System.Windows.Forms.TextBox txtDesc;
        private System.Windows.Forms.TextBox txtLat;
        private System.Windows.Forms.TextBox txtLng;
    }
}