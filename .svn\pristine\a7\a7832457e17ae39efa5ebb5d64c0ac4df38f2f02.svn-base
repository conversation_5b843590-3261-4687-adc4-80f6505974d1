﻿namespace MasterCom.RAMS.Func.MeshGridOf3D
{
    partial class MeshGridOf3DForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panel1 = new System.Windows.Forms.Panel();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.cmbcolortype = new System.Windows.Forms.ComboBox();
            this.label3 = new System.Windows.Forms.Label();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.SettingToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.trackBarDirection = new System.Windows.Forms.TrackBar();
            this.trackBarViewAngle = new System.Windows.Forms.TrackBar();
            this.panel2 = new System.Windows.Forms.Panel();
            this.panel3 = new System.Windows.Forms.Panel();
            this.PlotPanel = new System.Windows.Forms.PictureBox();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarDirection)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarViewAngle)).BeginInit();
            this.panel2.SuspendLayout();
            this.panel3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.PlotPanel)).BeginInit();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.pictureBox1);
            this.panel1.Controls.Add(this.cmbcolortype);
            this.panel1.Controls.Add(this.label3);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(664, 27);
            this.panel1.TabIndex = 0;
            // 
            // pictureBox1
            // 
            this.pictureBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.pictureBox1.BackColor = System.Drawing.SystemColors.ControlDark;
            this.pictureBox1.Location = new System.Drawing.Point(0, 23);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(686, 1);
            this.pictureBox1.TabIndex = 28;
            this.pictureBox1.TabStop = false;
            // 
            // cmbcolortype
            // 
            this.cmbcolortype.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.cmbcolortype.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbcolortype.FormattingEnabled = true;
            this.cmbcolortype.Items.AddRange(new object[] {
            "Autumn",
            "FallGrYl",
            "FallRdGr",
            "Cool",
            "Cool1",
            "Cool11",
            "Cool2",
            "Cool22",
            "DeltaRdGr",
            "DeltaRdBl",
            "DeltaGrBl",
            "DeltaGrRd",
            "Gray",
            "Hot",
            "Hot1",
            "Hot2",
            "Jet",
            "JetD1",
            "Mix1",
            "Mix2",
            "Mix3",
            "Mix4",
            "Rainbow",
            "Spring",
            "Summer",
            "Winter"});
            this.cmbcolortype.Location = new System.Drawing.Point(575, 2);
            this.cmbcolortype.Name = "cmbcolortype";
            this.cmbcolortype.Size = new System.Drawing.Size(88, 20);
            this.cmbcolortype.TabIndex = 21;
            this.cmbcolortype.SelectedIndexChanged += new System.EventHandler(this.cmbcolortype_SelectedIndexChanged);
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(535, 6);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(35, 12);
            this.label3.TabIndex = 20;
            this.label3.Text = "颜色:";
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.SettingToolStripMenuItem});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(95, 26);
            // 
            // SettingToolStripMenuItem
            // 
            this.SettingToolStripMenuItem.Name = "SettingToolStripMenuItem";
            this.SettingToolStripMenuItem.Size = new System.Drawing.Size(94, 22);
            this.SettingToolStripMenuItem.Text = "设置";
            this.SettingToolStripMenuItem.Click += new System.EventHandler(this.SettingToolStripMenuItem_Click);
            // 
            // trackBarDirection
            // 
            this.trackBarDirection.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.trackBarDirection.AutoSize = false;
            this.trackBarDirection.Location = new System.Drawing.Point(3, 1);
            this.trackBarDirection.Maximum = 180;
            this.trackBarDirection.Minimum = -180;
            this.trackBarDirection.Name = "trackBarDirection";
            this.trackBarDirection.Size = new System.Drawing.Size(632, 23);
            this.trackBarDirection.TabIndex = 2;
            this.trackBarDirection.TickStyle = System.Windows.Forms.TickStyle.None;
            this.trackBarDirection.Value = 37;
            this.trackBarDirection.Scroll += new System.EventHandler(this.trackBarDirection_Scroll);
            // 
            // trackBarViewAngle
            // 
            this.trackBarViewAngle.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.trackBarViewAngle.AutoSize = false;
            this.trackBarViewAngle.Location = new System.Drawing.Point(1, 3);
            this.trackBarViewAngle.Maximum = 90;
            this.trackBarViewAngle.Minimum = -90;
            this.trackBarViewAngle.Name = "trackBarViewAngle";
            this.trackBarViewAngle.Orientation = System.Windows.Forms.Orientation.Vertical;
            this.trackBarViewAngle.Size = new System.Drawing.Size(25, 387);
            this.trackBarViewAngle.TabIndex = 3;
            this.trackBarViewAngle.TickStyle = System.Windows.Forms.TickStyle.None;
            this.trackBarViewAngle.Value = -30;
            this.trackBarViewAngle.Scroll += new System.EventHandler(this.trackBarViewAngle_Scroll);
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.trackBarDirection);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel2.Location = new System.Drawing.Point(0, 417);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(664, 24);
            this.panel2.TabIndex = 4;
            // 
            // panel3
            // 
            this.panel3.Controls.Add(this.trackBarViewAngle);
            this.panel3.Dock = System.Windows.Forms.DockStyle.Right;
            this.panel3.Location = new System.Drawing.Point(635, 27);
            this.panel3.Name = "panel3";
            this.panel3.Size = new System.Drawing.Size(29, 390);
            this.panel3.TabIndex = 5;
            // 
            // PlotPanel
            // 
            this.PlotPanel.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.PlotPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.PlotPanel.Location = new System.Drawing.Point(0, 27);
            this.PlotPanel.Name = "PlotPanel";
            this.PlotPanel.Size = new System.Drawing.Size(635, 390);
            this.PlotPanel.TabIndex = 1;
            this.PlotPanel.TabStop = false;
            // 
            // MeshGridOf3DForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(664, 441);
            this.Controls.Add(this.PlotPanel);
            this.Controls.Add(this.panel3);
            this.Controls.Add(this.panel2);
            this.Controls.Add(this.panel1);
            this.Name = "MeshGridOf3DForm";
            this.Text = "MeshGridOf3DForm";
            this.Load += new System.EventHandler(this.MeshGridOf3DForm_Load);
            this.SizeChanged += new System.EventHandler(this.MeshGridOf3DForm_SizeChanged);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.trackBarDirection)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarViewAngle)).EndInit();
            this.panel2.ResumeLayout(false);
            this.panel3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.PlotPanel)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.ComboBox cmbcolortype;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.PictureBox pictureBox1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem SettingToolStripMenuItem;
        private System.Windows.Forms.TrackBar trackBarDirection;
        private System.Windows.Forms.TrackBar trackBarViewAngle;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Panel panel3;
        public System.Windows.Forms.PictureBox PlotPanel;
        //private System.Windows.Forms.Panel panel2;

    }
}