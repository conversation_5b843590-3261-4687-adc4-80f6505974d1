﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Text.RegularExpressions;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCsfbCellJudgeBase : DIYAnalyseByFileBackgroundBase
    {
        protected int curLteServiceType = (int)ServiceType.LTE_TDD_VOICE;
        protected int curFallServiceType = (int)ServiceType.GSM_VOICE;
        protected int EvtIdMtCsfbLteRelease = 886;
        protected int EvtIdMoCsfbLteRelease = 878;
        protected int EvtIdLte_CellReselection_L2G = 1300;

        public ZTCsfbCellJudgeSetRadiusCondition hoCondition { get; set; } = new ZTCsfbCellJudgeSetRadiusCondition();
        public ZTCsfbCellJudge clJudge { get; set; } = new ZTCsfbCellJudge();

        public Dictionary<string, int> levDc { get; set; }  //统计各采样点小区和各自的电平和
        public Dictionary<string, int> cellCountlist { get; set; } //统计每个小区影响的采样点个数
        public List<ZTCsfbCellJudge> resultList { get; set; }  //保存结果

        protected ICell iCell = null;
        
        public ZTCsfbCellJudgeBase(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeMessage = true;
        }

        /// <summary>
        /// 对同一网格的csfb文件和gsm文件进行关联
        /// </summary>
        protected override void analyseFiles()
        {
            Dictionary<FileInfo, List<FileInfo>> moMtPair = new Dictionary<FileInfo, List<FileInfo>>();   
            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (fileInfo.ServiceType == curLteServiceType)   //csfb文件关联到gsm文件
                {
                    List<FileInfo> mtFileList = getMtFileList(fileInfo);
                    if (mtFileList == null || mtFileList.Count == 0)
                    {
                        continue;
                    }
                    moMtPair[fileInfo] = mtFileList;
                }
            }
            try
            {
                clearDataBeforeAnalyseFiles();
                int iloop = 0;
                foreach (KeyValuePair<FileInfo, List<FileInfo>> pair in moMtPair)
                {
                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + moMtPair.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / moMtPair.Count);

                    analysePairFiles(pair);
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        private void analysePairFiles(KeyValuePair<FileInfo, List<FileInfo>> pair)
        {
            condition.FileInfos.Clear();
            if (pair.Key != null)
            {
                condition.FileInfos.Add(pair.Key);
            }
            if (pair.Value.Count != 0 || pair.Value != null)
            {
                foreach (FileInfo file in pair.Value)
                {
                    condition.FileInfos.Add(file);
                }
            }
            replay();
            condition.FileInfos.Clear();
        }

        private List<FileInfo> getMtFileList(FileInfo fileInfo)
        {
            List<FileInfo> mtFileList = MainModel.FileInfos.FindAll(delegate (FileInfo x)
            {
                #region  汉字网格
                List<string> netList = new List<string>{"昌平大学城","昌平区1号","城关","大兴区1号","房山区1号","怀柔区1号","怀柔区2号","回龙观","马驹桥",
                                                "门头沟区1号","密云县1号","密云县2号","平谷区1号","平谷区2号","平谷区3号","沙峪别墅区","沙峪工业区",
                                                "顺义区1号","顺义区2号","天通苑","天竺","通州区1号","通州区2号","通州区3号","延庆县1号","延庆县2号",
                                                "燕山石化","亦庄核心区1","亦庄核心区2","亦庄核心区3","亦庄路东区","长阳"};
                #endregion
                string netWorkX = Regex.Match(x.Name, @"网格\d+", RegexOptions.None).Value;
                string netWorkFile = Regex.Match(fileInfo.Name, @"网格\d+", RegexOptions.None).Value;
                if (string.IsNullOrEmpty(netWorkFile) && string.IsNullOrEmpty(netWorkX))
                {
                    foreach (string net in netList)
                    {
                        if (fileInfo.Name.Contains(net) && x.Name.Contains(net))
                        {
                            netWorkX = net;
                            netWorkFile = net;
                        }
                    }
                }
                if (string.IsNullOrEmpty(netWorkX) || string.IsNullOrEmpty(netWorkFile))
                {
                    return false;
                }
                if (fileInfo.ServiceType != x.ServiceType && netWorkX == netWorkFile)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            });
            return mtFileList;
        }

        ZTCsfbCellJudgeSetRadiusForm setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTCsfbCellJudgeSetRadiusForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                resultList = new List<ZTCsfbCellJudge>();
                hoCondition = setForm.GetCondition();
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            DTFileDataManager moFile = null;
            List<DTFileDataManager> fileList = new List<DTFileDataManager>();

            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file.ServiceType == curLteServiceType)
                {
                    moFile = file;
                }
                else if (file.ServiceType == curFallServiceType)
                {
                    fileList.Add(file);
                }
            }

            List<ZTCsfbCellJudge> moJudges = new List<ZTCsfbCellJudge>();        
            if (moFile != null)
            {
                try
                {
                    dealDTDatas(moFile, fileList, moJudges);
                }
                catch (Exception e)
                {
                    log.Debug("........", e);
                    throw;
                }
                foreach (ZTCsfbCellJudge cj in moJudges)
                {
                    foreach (DTData dt in moFile.DTDatas)
                    {
                        addZTCsfbCellJudgeData(cj, dt);
                    }
                }
            }
            
            if (fileList.Count != 0)
            {
                dealZTCsfbCellJudge(fileList, moJudges);
            }
        }

        private void dealDTDatas(DTFileDataManager moFile, List<DTFileDataManager> fileList, List<ZTCsfbCellJudge> moJudges)
        {
            ZTCsfbCellJudge judge = null;
            foreach (DTData data in moFile.DTDatas)
            {
                if (data is TestPoint)
                {
                    if (judge != null)
                    {
                        judge.AddTestPoint(data as TestPoint);
                    }
                }
                else if (data is Event)
                {
                    dealEvent(moFile, fileList, moJudges, ref judge, data);
                }
                else if (data is Model.Message && judge != null)
                {
                    judge.AddMessage(data as Model.Message);
                }
            }
        }

        private void dealEvent(DTFileDataManager moFile, List<DTFileDataManager> fileList, List<ZTCsfbCellJudge> moJudges, ref ZTCsfbCellJudge judge, DTData data)
        {
            Event evt = data as Event;
            cellCountlist = new Dictionary<string, int>();
            levDc = new Dictionary<string, int>();

            if (evt.ID == EvtIdMtCsfbLteRelease || evt.ID == EvtIdMoCsfbLteRelease)        //MT MO CSFB LTE Release
            {
                LTECell cell = evt.GetSrcCell() as LTECell;
                if (cell == null)
                {
                    return;
                }
                judge = new ZTCsfbCellJudge();
                judge.Longitude = evt.Longitude;
                judge.Latitude = evt.Latitude;
                judge.BeginTime = evt.DateTime;
                judge.AddEvent(evt);
                judge.MoFileName = moFile.FileName;
                judge.LteCellName = cell.Name;
                judge.LteECI = cell.ECI;
                judge.ENBID = (cell.ECI / 256).ToString() + "-" + (cell.ECI % 256).ToString();
                judge.LteTAC = cell.TAC;
                judge.LteLongitude = cell.Longitude;
                judge.LteLatitude = cell.Latitude;
                judge.LTEDistance = Math.Round(cell.GetDistance(evt.Longitude, evt.Latitude), 2);
                moJudges.Add(judge);
            }
            else if (evt.ID == EvtIdLte_CellReselection_L2G && judge != null)     //LTE_CellReselection_L2G
            {
                judge.EndTime = evt.DateTime;
                iCell = getSrcICellByEvt(evt);
                if (iCell == null)
                {
                    return;
                }
                judge.FallDistance = Math.Round(MathFuncs.GetDistance(judge.Longitude, judge.Latitude, iCell.Longitude, iCell.Latitude), 2);
                List<TestPoint> tpListFall = moFile.TestPoints;
                dealTPListFall(judge, evt, tpListFall);
                judge.AddEvent(evt);
                GetTable(fileList, judge);
                judge = null;
            }
            else if (judge != null)
            {
                judge.AddEvent(evt);
            }
        }

        private void dealTPListFall(ZTCsfbCellJudge judge, Event evt, List<TestPoint> tpListFall)
        {
            for (int j = 0; j < tpListFall.Count - 1;)
            {
                try
                {
                    bool isValid = dealTP(judge, evt, tpListFall, ref j);
                    if (!isValid)
                    {
                        break;
                    }
                }
                catch (Exception e)
                {
                    log.Debug("参数有问题", e);
                    throw;
                }
            }
        }

        private bool dealTP(ZTCsfbCellJudge judge, Event evt, List<TestPoint> tpListFall, ref int j)
        {
            TestPoint curTp = tpListFall[j];
            if (curTp.DateTime >= evt.DateTime)
            {
                int? curTpLac = curTp.GetLAC();
                int? curTpCi = curTp.GetCI();

                if (evt["TargetLAC"] == null || curTpLac == null || evt["TargetCI"] == null || curTpCi == null)
                {
                    log.Debug("count的值是：" + tpListFall.Count + ";  j的值是" + j);
                    j++;
                }
                else
                {
                    bool isValid = dealNextTP(judge, evt, tpListFall, ref j, curTp, curTpLac, curTpCi);
                    if (!isValid)
                    {
                        return false;
                    }
                }
            }
            else
            {
                j++;
            }

            return true;
        }

        private bool dealNextTP(ZTCsfbCellJudge judge, Event evt, List<TestPoint> tpListFall, ref int j, TestPoint curTp, int? curTpLac, int? curTpCi)
        {
            TestPoint nextTp = tpListFall[j + 1];
            int? nextTpLac = nextTp.GetLAC();
            int? nextTpCi = nextTp.GetCI();
            if (nextTpLac != null && nextTpCi != null)
            {
                if ((int)evt["TargetLAC"] == (int)curTpLac && (int)evt["TargetCI"] == (int)curTpCi)
                {
                    int? curTpRxlev = (int?)curTp.GetRxlev();
                    if (curTpRxlev == null && (int)evt["TargetLAC"] == (int)nextTpLac && (int)evt["TargetCI"] == (int)nextTpCi)
                    {
                        j++;
                    }
                    else if (curTpRxlev == null)
                    {
                        return false;
                    }
                    else
                    {
                        judge.FallRxLev = (int)curTpRxlev;
                        return false;
                    }
                }
                else
                {
                    j++;
                }
            }
            else
            {
                j++;
            }

            return true;
        }

        private static void addZTCsfbCellJudgeData(ZTCsfbCellJudge cj, DTData dt)
        {
            if ((cj.BeginTime - dt.DateTime).TotalSeconds <= 3 && (dt.DateTime - cj.EndTime).TotalSeconds <= 3)
            {
                if (dt is Event)
                {
                    Event evt = dt as Event;
                    if (!cj.Events.Contains(evt))
                    {
                        cj.Events.Add(evt);
                    }
                }
                else if (dt is TestPoint)
                {
                    TestPoint tp = dt as TestPoint;
                    if (!cj.TestPoints.Contains(tp))
                    {
                        cj.TestPoints.Add(tp);
                    }
                }
                else if (dt is MasterCom.RAMS.Model.Message)
                {
                    Model.Message msg = dt as Model.Message;
                    if (!cj.Messages.Contains(msg))
                    {
                        cj.Messages.Add(msg);
                    }
                }
            }
        }

        private void dealZTCsfbCellJudge(List<DTFileDataManager> fileList, List<ZTCsfbCellJudge> moJudges)
        {
            ZTCsfbCellJudge judge = null;
            foreach (ZTCsfbCellJudge jg in moJudges)
            {
                foreach (DTFileDataManager mtFile in fileList)
                {
                    foreach (DTData data in mtFile.DTDatas)
                    {
                        judge = getZTCsfbCellJudge(judge, jg, data);
                    }
                    jg.AddFileName(mtFile.FileName);
                }
            }
        }

        private ZTCsfbCellJudge getZTCsfbCellJudge(ZTCsfbCellJudge judge, ZTCsfbCellJudge jg, DTData data)
        {
            if (data is TestPoint)
            {
                TestPoint tp = data as TestPoint;
                double distance = MathFuncs.GetDistance(jg.Longitude, jg.Latitude, tp.Longitude, tp.Latitude);
                if (distance <= (double)hoCondition.Radius)
                {
                    judge = new ZTCsfbCellJudge();
                    judge.AddTestPoint(tp);
                    jg.AddList(judge);
                }
                else
                {
                    judge = null;
                }
            }
            else if (data is Event && judge != null)
            {
                judge.AddEvent(data as Event);
            }
            else if (data is Model.Message && judge != null)
            {
                judge.AddMessage(data as Model.Message);
            }

            return judge;
        }

        protected virtual ICell getSrcICellByEvt(Event evt)
        {
            return CellManager.GetInstance().GetNearestCell(evt.DateTime, (ushort?)(int?)evt["TargetLAC"]
                , (ushort?)(int?)evt["TargetCI"], evt.Longitude, evt.Latitude);
        }
        private void GetTable(List<DTFileDataManager> fileList,ZTCsfbCellJudge judge)
        {
            try
            {
                foreach (DTFileDataManager mtFile in fileList)
                {
                    List<TestPoint> tpList = mtFile.TestPoints;
                    dealTPs(judge, tpList);
                }

                foreach (KeyValuePair<string, int> pair in cellCountlist)
                {
                    if (levDc.ContainsKey(pair.Key))
                    {
                        levDc[pair.Key] = (int)Math.Round((decimal)levDc[pair.Key] / cellCountlist[pair.Key], 2);

                        judge.AddCellRxLev(pair.Key, levDc[pair.Key]);
                    }
                }
                //将小区中与最强小区电平差大于6DB的去掉
                int maxRxLev = GetMaxValues(levDc);
                double maxLongitude = 0;
                double maxLatitude = 0;
                List<string> lCell = new List<string>();
                foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                {
                    if (maxRxLev - pair.Value > hoCondition.RxLev)
                    {
                        lCell.Add(pair.Key);
                    }
                }
                foreach (string cl in lCell)
                {
                    judge.RemoveCellRxLev(cl);
                }

                foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                {
                    CellManager cm = CellManager.GetInstance();
                    Cell cell = cm.GetCellByName(pair.Key);
                    if (maxRxLev == pair.Value && cell != null)
                    {
                        maxLongitude = cell.Longitude;
                        maxLatitude = cell.Latitude;
                        break;
                    }
                }

                addInfoToResultList(judge, maxRxLev, maxLongitude, maxLatitude);
            }
            catch (Exception e)
            {
                log.Debug("getTable()方法中异常：", e);
                throw;
            }
        }

        private void dealTPs(ZTCsfbCellJudge judge, List<TestPoint> tpList)
        {
            for (int i = 0; i < tpList.Count; i++)
            {
                TestPoint curTp = tpList[i];
                int? rxlev = (int?)curTp.GetRxlev();
                ICell mCell = curTp.GetMainCell();

                double distance = MathFuncs.GetDistance(judge.Longitude, judge.Latitude, curTp.Longitude, curTp.Latitude);

                if (distance <= (double)hoCondition.Radius && mCell != null && rxlev != null)
                {
                    if (isContainsKey(levDc, mCell))
                    {
                        levDc[mCell.Name] += (int)rxlev;
                        cellCountlist[mCell.Name]++;
                    }
                    else
                    {
                        levDc.Add(mCell.Name, (int)rxlev);
                        cellCountlist.Add(mCell.Name, 1);
                    }

                    if (!judge.Contains(mCell))
                    {
                        judge.AddCell(mCell);
                    }
                    dealNBCell(judge, curTp);
                }
            }
        }

        private void dealNBCell(ZTCsfbCellJudge judge, TestPoint curTp)
        {
            int gsmCount = 0;
            int dscCount = 0;
            bool flag = true;
            for (int j = 0; j < 32; j++)
            {
                ICell nbCell = curTp.GetNBCell(j);

                int? nRxlev = (int?)curTp.GetNbRxlev(j);
                if (nbCell != null && nRxlev != null)
                {
                    Cell gCell = CellManager.GetInstance().GetCellByName(nbCell.Name);
                    if (gCell != null)
                    {
                        judgeFlag(ref gsmCount, ref dscCount, ref flag, gCell);

                        setResult(judge, flag, nbCell, nRxlev);
                    }
                }
            }
        }

        private void judgeFlag(ref int gsmCount, ref int dscCount, ref bool flag, Cell gCell)
        {
            if (gCell.BCCH >= 1 && gCell.BCCH <= 124)
            {
                gsmCount++;
                if (gsmCount > 16)
                {
                    flag = false;
                }
            }
            else if (gCell.BCCH >= 512 && gCell.BCCH <= 1024)
            {
                dscCount++;
                if (dscCount > 16)
                {
                    flag = false;
                }
            }
        }

        private void setResult(ZTCsfbCellJudge judge, bool flag, ICell nbCell, int? nRxlev)
        {
            if (flag)
            {
                if (isContainsKey(levDc, nbCell))
                {
                    levDc[nbCell.Name] += (int)nRxlev;
                    cellCountlist[nbCell.Name]++;
                }
                else
                {
                    levDc.Add(nbCell.Name, (int)nRxlev);
                    cellCountlist.Add(nbCell.Name, 1);
                }

                if (!judge.Contains(nbCell))
                {
                    judge.AddCell(nbCell);
                }
            }
        }

        protected virtual void addInfoToResultList(ZTCsfbCellJudge judge, int maxRxLev, double maxLongitude, double maxLatitude)
        {
            Cell cell = iCell as Cell;
            if (cell == null)
            {
                return;
            }
            judge.Sort();
            if (judge.Contains(cell))
            {
                if (isInSix(judge.CellRxLevList, cell))
                {
                    judge.IsInList = true;
                }
                else
                {
                    judge.IsInList = false;
                }
                judge.SN = resultList.Count + 1;
                judge.CellName = cell.Name;
                judge.CellID = cell.CI;
                judge.LAC = cell.LAC;
                judge.FallLongitude = cell.Longitude;
                judge.FallLatitude = cell.Latitude;
                judge.MaxFallDistance = Math.Round(MathFuncs.GetDistance(judge.Longitude, judge.Latitude, maxLongitude, maxLatitude), 2);
                judge.MaxFallRxLev = maxRxLev;
                resultList.Add(judge);
            }
        }
        protected bool isContainsKey(Dictionary<string, int> levDc, ICell cl)
        {
            foreach (KeyValuePair<string, int> pair in levDc)
            {
                if (pair.Key == cl.Name)
                {
                    return true;
                }
            }
            return false;
        }

        protected bool isInSix(Dictionary<string, int> levDc, ICell cl)
        {
            int i = 0;
            foreach (KeyValuePair<string, int> pair in levDc)
            {
                i++;
                if (i <= 6)
                {
                    Cell cell = CellManager.GetInstance().GetCellByName(pair.Key);
                    if (cell !=null && cell.CI.ToString() == cl.Code)
                    {
                        return true;
                    }
                }
                else
                {
                    break;
                }
            }
            return false;
        }

        /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            ZTCsfbCellJudgeListForm frm = MainModel.GetInstance().CreateResultForm(typeof(ZTCsfbCellJudgeListForm)) as ZTCsfbCellJudgeListForm;
            frm.FillData(resultList);
            frm.Visible = true;
            frm.BringToFront();
            resultList = null;
        }
        protected int GetMaxValues(Dictionary<string, int> dc)
        {
            int max = int.MinValue;
            foreach (int key in dc.Values)
            {
                if (max <= key)
                {
                    max = key;
                }
            }
            return max;
        }
    }

    public class ZTCsfbCellJudgeSetRadiusCondition
    {
        public int Radius { get; set; }       //覆盖半径选择
        public int BeginTimeSpan { get; set; }     //开始时间间隔
        public int TestTimeSpan { get; set; }     //测试时长间隔
        public int Distance { get; set; }     //距离间隔
        public int RxLev { get; set; }       //与最强小区的场强差
        public ZTCsfbCellJudgeSetRadiusCondition()
        {
            Radius = 200;
            BeginTimeSpan = 1;
            TestTimeSpan=20;
            Distance = 500;
            RxLev = 6;
        }
    }

    public class ZTCsfbCellJudge
    {
        private readonly List<ZTCsfbCellJudge> allList = new List<ZTCsfbCellJudge>();
        public List<ZTCsfbCellJudge> AllList
        {
            get { return allList; }
        }
        public void AddList(ZTCsfbCellJudge cj)
        {
            allList.Add(cj);
        }
        public int SN { get; set; }     //序号
        public string MoFileName { get; set; }     //csfb文件名

        public readonly List<string> mtFileName = new List<string>();    //gsm文件名
        public List<string> MtFileName
        {
            get { return mtFileName; }
        }

        public void AddFileName(string fileName)
        {
            mtFileName.Add(fileName);
        }
        public string CellName { get; set; }      //回落的小区名
        public int LAC { get; set; }
        public int CellID { get; set; }
        public double Latitude { get; set; }       //回落位置的纬度
        public double Longitude { get; set; }      //回落位置的经度
        public DateTime BeginTime { get; set; }    //回落开始的时间
        public DateTime EndTime { get; set; }      //回落结束时间
        public string LteCellName { get; set; }    //回落前4G小区名称
        public int LteECI { get; set; }            //回落前4G小区ECI
        public string ENBID { get; set; }          //ECI/256-LocalCellID
        public int LteTAC { get; set; }            //回落前4G小区TAC
        public double LteLongitude { get; set; }   //回落前4G小区经度
        public double LteLatitude { get; set; }    //回落前4G小区纬度
        public bool IsInList { get; set; }         //是否在前六强小区中

        private readonly List<Cell> GSMCell = new List<Cell>();    //所有覆盖回落位置的小区集合
        private readonly List<WCell> WCellList = new List<WCell>();    //所有覆盖回落位置的小区集合
        public void AddCell(ICell cl)
        {
            if (cl is Cell)
            {
                GSMCell.Add(cl as Cell);
            }
            else if (cl is WCell)
            {
                WCellList.Add(cl as WCell);
            }
        }

        public bool Contains(ICell cl)
        {
            if (cl is Cell)
            {
                foreach (Cell gcl in GSMCell)
                {
                    if (gcl.Name == cl.Name)
                    {
                        return true;
                    }
                }
            }
            else if (cl is WCell)
            {
                foreach (WCell wcl in WCellList)
                {
                    if (wcl.Name == cl.Name)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        private readonly Dictionary<string, int> cellReLevList = new Dictionary<string, int>();   //小区及对应的电平
        public void AddCellRxLev(string cl,int cValue)
        {
            cellReLevList.Add(cl,cValue);
        }
        public void RemoveCellRxLev(string cl)
        {
            cellReLevList.Remove(cl);
        }
        public Dictionary<string, int> CellRxLevList
        {
            get { return cellReLevList; }
        }

        public Dictionary<string, int> Sort()
        {
            if (cellReLevList.Count > 0)
            {
                List<KeyValuePair<string, int>> lst = new List<KeyValuePair<string, int>>(cellReLevList);
                lst.Sort(delegate(KeyValuePair<string, int> k1, KeyValuePair<string, int> k2)
                {
                    return k2.Value.CompareTo(k1.Value);
                });
                cellReLevList.Clear();
                foreach (KeyValuePair<string, int> pair in lst)
                {
                    cellReLevList.Add(pair.Key, pair.Value);
                }
                return cellReLevList;
            }
            return cellReLevList;
        }
        public double LTEDistance { get; set; }        //回落前与LTE小区的距离
        public double FallLatitude { get; set; }        //回落后小区的经度
        public double FallLongitude { get; set; }       //回落后小区的纬度
        public double FallRxLev { get; set; }           //回落后小区的电平
        public double FallDistance { get; set; }        //回落后与小区的距离
        public double MaxFallRxLev { get; set; }        //最强小区的电平
        public double MaxFallDistance { get; set; }     //回落位置与最强小区的距离

        private readonly List<Event> evtList = new List<Model.Event>();
        public void AddEvent(Event evt)
        {
            evtList.Add(evt);
        }

        private readonly List<TestPoint> tpList = new List<TestPoint>();
        public void AddTestPoint(Model.TestPoint tp)
        {
            tpList.Add(tp);
        }

        private readonly List<MasterCom.RAMS.Model.Message> msgList = new List<Model.Message>();
        public void AddMessage(MasterCom.RAMS.Model.Message msg)
        {
            msgList.Add(msg);
        }
        public List<Event> Events
        {
            get { return evtList; }
        }
        public List<TestPoint> TestPoints
        {
            get { return tpList; }
        }
        public List<MasterCom.RAMS.Model.Message> Messages
        {
            get { return msgList; }
        }
        public string MoMtDesc { get; set; }
    }
}
