using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    partial class TDCellInfoForm : Form
    {
        public TDCellInfoForm(MainModel mainModel, TDCell cell)
        {
            InitializeComponent();
            this.mainModel = mainModel;
            this.cell = cell;
            textBoxID.Text = cell.ID.ToString();
            textBoxName.Text = cell.Name;
            textBoxCode.Text = cell.Code;
            textBoxLAC.Text = cell.LAC.ToString();
            textBoxCI.Text = cell.CI.ToString();
            textBoxCPI.Text = cell.CPI.ToString();
            textBoxFREQ.Text = cell.FREQ.ToString();
            textBoxTCH.Text = cell.FreqDesc;
            textBoxType.Text = cell.Type.ToString();
            textBoxDirection.Text = cell.Direction.ToString();
            textBoxMSC.Text = cell.BelongBTS.BelongBSC.BelongMSC.Name;
            textBoxBSC.Text = cell.BelongBTS.BelongBSC.Name;
            textBoxBTS.Text = cell.BelongBTS.Name;
            textBoxLongitude.Text = cell.Longitude.ToString();
            textBoxLatitude.Text = cell.Latitude.ToString();
            List<TDAntenna> antennas = new List<TDAntenna>();
            antennas.Add(cell.Antenna);
            listBoxAntenna.DataSource = antennas;
            listBoxAntenna.DisplayMember = "SimpleInfo";
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            Close();
        }
        private void InitializeComponent()
        {
            System.Windows.Forms.Label labelID;
            System.Windows.Forms.Label labelCI;
            System.Windows.Forms.Label labelBCCH;
            System.Windows.Forms.Label labelBSIC;
            System.Windows.Forms.Label labelTCH;
            System.Windows.Forms.Label label1;
            System.Windows.Forms.Label label3;
            System.Windows.Forms.Label label4;
            System.Windows.Forms.Label label5;
            System.Windows.Forms.Label label6;
            System.Windows.Forms.Label label7;
            System.Windows.Forms.GroupBox groupBox1;
            System.Windows.Forms.Label label12;
            System.Windows.Forms.Label label10;
            System.Windows.Forms.Label label11;
            System.Windows.Forms.Label label9;
            System.Windows.Forms.Label label8;
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(TDCellInfoForm));
            this.listBoxAntenna = new System.Windows.Forms.ListBox();
            this.textBoxBTS = new System.Windows.Forms.TextBox();
            this.textBoxLatitude = new System.Windows.Forms.TextBox();
            this.textBoxDirection = new System.Windows.Forms.TextBox();
            this.textBoxType = new System.Windows.Forms.TextBox();
            this.textBoxLongitude = new System.Windows.Forms.TextBox();
            this.textBoxMSC = new System.Windows.Forms.TextBox();
            this.textBoxBSC = new System.Windows.Forms.TextBox();
            this.textBoxCode = new System.Windows.Forms.TextBox();
            this.textBoxName = new System.Windows.Forms.TextBox();
            this.textBoxLAC = new System.Windows.Forms.TextBox();
            this.textBoxTCH = new System.Windows.Forms.TextBox();
            this.textBoxFREQ = new System.Windows.Forms.TextBox();
            this.textBoxCPI = new System.Windows.Forms.TextBox();
            this.textBoxCI = new System.Windows.Forms.TextBox();
            this.textBoxID = new System.Windows.Forms.TextBox();
            this.buttonOK = new System.Windows.Forms.Button();
            labelID = new System.Windows.Forms.Label();
            labelCI = new System.Windows.Forms.Label();
            labelBCCH = new System.Windows.Forms.Label();
            labelBSIC = new System.Windows.Forms.Label();
            labelTCH = new System.Windows.Forms.Label();
            label1 = new System.Windows.Forms.Label();
            label3 = new System.Windows.Forms.Label();
            label4 = new System.Windows.Forms.Label();
            label5 = new System.Windows.Forms.Label();
            label6 = new System.Windows.Forms.Label();
            label7 = new System.Windows.Forms.Label();
            groupBox1 = new System.Windows.Forms.GroupBox();
            label12 = new System.Windows.Forms.Label();
            label10 = new System.Windows.Forms.Label();
            label11 = new System.Windows.Forms.Label();
            label9 = new System.Windows.Forms.Label();
            label8 = new System.Windows.Forms.Label();
            groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelID
            // 
            labelID.AutoSize = true;
            labelID.Location = new System.Drawing.Point(6, 22);
            labelID.Name = "labelID";
            labelID.Size = new System.Drawing.Size(23, 12);
            labelID.TabIndex = 0;
            labelID.Text = "&ID:";
            // 
            // labelCI
            // 
            labelCI.AutoSize = true;
            labelCI.Location = new System.Drawing.Point(137, 48);
            labelCI.Name = "labelCI";
            labelCI.Size = new System.Drawing.Size(23, 12);
            labelCI.TabIndex = 8;
            labelCI.Text = "&CI:";
            // 
            // labelBCCH
            // 
            labelBCCH.AutoSize = true;
            labelBCCH.Location = new System.Drawing.Point(136, 76);
            labelBCCH.Name = "labelBCCH";
            labelBCCH.Size = new System.Drawing.Size(29, 12);
            labelBCCH.TabIndex = 10;
            labelBCCH.Text = "&CPI:";
            // 
            // labelBSIC
            // 
            labelBSIC.AutoSize = true;
            labelBSIC.Location = new System.Drawing.Point(7, 74);
            labelBSIC.Name = "labelBSIC";
            labelBSIC.Size = new System.Drawing.Size(47, 12);
            labelBSIC.TabIndex = 12;
            labelBSIC.Text = "&UARFCN:";
            // 
            // labelTCH
            // 
            labelTCH.AutoSize = true;
            labelTCH.Location = new System.Drawing.Point(6, 100);
            labelTCH.Name = "labelTCH";
            labelTCH.Size = new System.Drawing.Size(77, 12);
            labelTCH.TabIndex = 16;
            labelTCH.Text = "&UARFCN List:";
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(6, 48);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(29, 12);
            label1.TabIndex = 6;
            label1.Text = "&LAC:";
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new System.Drawing.Point(135, 126);
            label3.Name = "label3";
            label3.Size = new System.Drawing.Size(29, 12);
            label3.TabIndex = 22;
            label3.Text = "&Dir:";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new System.Drawing.Point(6, 126);
            label4.Name = "label4";
            label4.Size = new System.Drawing.Size(35, 12);
            label4.TabIndex = 18;
            label4.Text = "&Type:";
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new System.Drawing.Point(6, 204);
            label5.Name = "label5";
            label5.Size = new System.Drawing.Size(35, 12);
            label5.TabIndex = 31;
            label5.Text = "&Long:";
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new System.Drawing.Point(137, 204);
            label6.Name = "label6";
            label6.Size = new System.Drawing.Size(29, 12);
            label6.TabIndex = 33;
            label6.Text = "&Lat:";
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Location = new System.Drawing.Point(6, 178);
            label7.Name = "label7";
            label7.Size = new System.Drawing.Size(41, 12);
            label7.TabIndex = 28;
            label7.Text = "&NodeB:";
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(label12);
            groupBox1.Controls.Add(this.listBoxAntenna);
            groupBox1.Controls.Add(this.textBoxBTS);
            groupBox1.Controls.Add(label10);
            groupBox1.Controls.Add(this.textBoxLatitude);
            groupBox1.Controls.Add(this.textBoxDirection);
            groupBox1.Controls.Add(label11);
            groupBox1.Controls.Add(label3);
            groupBox1.Controls.Add(label9);
            groupBox1.Controls.Add(this.textBoxType);
            groupBox1.Controls.Add(label8);
            groupBox1.Controls.Add(this.textBoxLongitude);
            groupBox1.Controls.Add(label7);
            groupBox1.Controls.Add(label4);
            groupBox1.Controls.Add(this.textBoxMSC);
            groupBox1.Controls.Add(label5);
            groupBox1.Controls.Add(this.textBoxBSC);
            groupBox1.Controls.Add(label6);
            groupBox1.Controls.Add(this.textBoxCode);
            groupBox1.Controls.Add(this.textBoxName);
            groupBox1.Controls.Add(this.textBoxLAC);
            groupBox1.Controls.Add(label1);
            groupBox1.Controls.Add(this.textBoxTCH);
            groupBox1.Controls.Add(this.textBoxFREQ);
            groupBox1.Controls.Add(this.textBoxCPI);
            groupBox1.Controls.Add(this.textBoxCI);
            groupBox1.Controls.Add(labelTCH);
            groupBox1.Controls.Add(labelBSIC);
            groupBox1.Controls.Add(labelBCCH);
            groupBox1.Controls.Add(labelCI);
            groupBox1.Controls.Add(this.textBoxID);
            groupBox1.Controls.Add(labelID);
            groupBox1.Location = new System.Drawing.Point(12, 12);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new System.Drawing.Size(400, 276);
            groupBox1.TabIndex = 0;
            groupBox1.TabStop = false;
            groupBox1.Text = "Info";
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Location = new System.Drawing.Point(8, 231);
            label12.Name = "label12";
            label12.Size = new System.Drawing.Size(35, 12);
            label12.TabIndex = 35;
            label12.Text = "&Ante:";
            // 
            // listBoxAntenna
            // 
            this.listBoxAntenna.FormattingEnabled = true;
            this.listBoxAntenna.HorizontalScrollbar = true;
            this.listBoxAntenna.ItemHeight = 12;
            this.listBoxAntenna.Location = new System.Drawing.Point(51, 227);
            this.listBoxAntenna.Name = "listBoxAntenna";
            this.listBoxAntenna.Size = new System.Drawing.Size(210, 40);
            this.listBoxAntenna.TabIndex = 36;
            // 
            // textBoxBTS
            // 
            this.textBoxBTS.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxBTS.Location = new System.Drawing.Point(51, 175);
            this.textBoxBTS.Name = "textBoxBTS";
            this.textBoxBTS.ReadOnly = true;
            this.textBoxBTS.Size = new System.Drawing.Size(80, 21);
            this.textBoxBTS.TabIndex = 29;
            // 
            // label10
            // 
            label10.AutoSize = true;
            label10.Location = new System.Drawing.Point(6, 152);
            label10.Name = "label10";
            label10.Size = new System.Drawing.Size(29, 12);
            label10.TabIndex = 24;
            label10.Text = "&MSC:";
            // 
            // textBoxLatitude
            // 
            this.textBoxLatitude.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxLatitude.Location = new System.Drawing.Point(181, 201);
            this.textBoxLatitude.Name = "textBoxLatitude";
            this.textBoxLatitude.ReadOnly = true;
            this.textBoxLatitude.Size = new System.Drawing.Size(80, 21);
            this.textBoxLatitude.TabIndex = 34;
            // 
            // textBoxDirection
            // 
            this.textBoxDirection.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxDirection.Location = new System.Drawing.Point(181, 123);
            this.textBoxDirection.Name = "textBoxDirection";
            this.textBoxDirection.ReadOnly = true;
            this.textBoxDirection.Size = new System.Drawing.Size(80, 21);
            this.textBoxDirection.TabIndex = 23;
            // 
            // label11
            // 
            label11.AutoSize = true;
            label11.Location = new System.Drawing.Point(137, 152);
            label11.Name = "label11";
            label11.Size = new System.Drawing.Size(29, 12);
            label11.TabIndex = 26;
            label11.Text = "&BSC:";
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Location = new System.Drawing.Point(267, 22);
            label9.Name = "label9";
            label9.Size = new System.Drawing.Size(35, 12);
            label9.TabIndex = 4;
            label9.Text = "&Code:";
            // 
            // textBoxType
            // 
            this.textBoxType.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxType.Location = new System.Drawing.Point(51, 123);
            this.textBoxType.Name = "textBoxType";
            this.textBoxType.ReadOnly = true;
            this.textBoxType.Size = new System.Drawing.Size(80, 21);
            this.textBoxType.TabIndex = 19;
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Location = new System.Drawing.Point(137, 22);
            label8.Name = "label8";
            label8.Size = new System.Drawing.Size(35, 12);
            label8.TabIndex = 2;
            label8.Text = "&Name:";
            // 
            // textBoxLongitude
            // 
            this.textBoxLongitude.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxLongitude.Location = new System.Drawing.Point(51, 201);
            this.textBoxLongitude.Name = "textBoxLongitude";
            this.textBoxLongitude.ReadOnly = true;
            this.textBoxLongitude.Size = new System.Drawing.Size(80, 21);
            this.textBoxLongitude.TabIndex = 32;
            // 
            // textBoxMSC
            // 
            this.textBoxMSC.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxMSC.Location = new System.Drawing.Point(51, 149);
            this.textBoxMSC.Name = "textBoxMSC";
            this.textBoxMSC.ReadOnly = true;
            this.textBoxMSC.Size = new System.Drawing.Size(80, 21);
            this.textBoxMSC.TabIndex = 25;
            // 
            // textBoxBSC
            // 
            this.textBoxBSC.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxBSC.Location = new System.Drawing.Point(181, 149);
            this.textBoxBSC.Name = "textBoxBSC";
            this.textBoxBSC.ReadOnly = true;
            this.textBoxBSC.Size = new System.Drawing.Size(80, 21);
            this.textBoxBSC.TabIndex = 27;
            // 
            // textBoxCode
            // 
            this.textBoxCode.AcceptsReturn = true;
            this.textBoxCode.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxCode.Location = new System.Drawing.Point(308, 19);
            this.textBoxCode.Name = "textBoxCode";
            this.textBoxCode.ReadOnly = true;
            this.textBoxCode.Size = new System.Drawing.Size(80, 21);
            this.textBoxCode.TabIndex = 5;
            this.textBoxCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // textBoxName
            // 
            this.textBoxName.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxName.Location = new System.Drawing.Point(181, 19);
            this.textBoxName.Name = "textBoxName";
            this.textBoxName.ReadOnly = true;
            this.textBoxName.Size = new System.Drawing.Size(80, 21);
            this.textBoxName.TabIndex = 3;
            // 
            // textBoxLAC
            // 
            this.textBoxLAC.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxLAC.Location = new System.Drawing.Point(51, 45);
            this.textBoxLAC.Name = "textBoxLAC";
            this.textBoxLAC.ReadOnly = true;
            this.textBoxLAC.Size = new System.Drawing.Size(80, 21);
            this.textBoxLAC.TabIndex = 7;
            // 
            // textBoxTCH
            // 
            this.textBoxTCH.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxTCH.Location = new System.Drawing.Point(83, 97);
            this.textBoxTCH.Name = "textBoxTCH";
            this.textBoxTCH.ReadOnly = true;
            this.textBoxTCH.Size = new System.Drawing.Size(311, 21);
            this.textBoxTCH.TabIndex = 17;
            // 
            // textBoxFREQ
            // 
            this.textBoxFREQ.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxFREQ.Location = new System.Drawing.Point(51, 70);
            this.textBoxFREQ.Name = "textBoxFREQ";
            this.textBoxFREQ.ReadOnly = true;
            this.textBoxFREQ.Size = new System.Drawing.Size(80, 21);
            this.textBoxFREQ.TabIndex = 13;
            // 
            // textBoxCPI
            // 
            this.textBoxCPI.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxCPI.Location = new System.Drawing.Point(181, 72);
            this.textBoxCPI.Name = "textBoxCPI";
            this.textBoxCPI.ReadOnly = true;
            this.textBoxCPI.Size = new System.Drawing.Size(80, 21);
            this.textBoxCPI.TabIndex = 11;
            // 
            // textBoxCI
            // 
            this.textBoxCI.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxCI.Location = new System.Drawing.Point(181, 45);
            this.textBoxCI.Name = "textBoxCI";
            this.textBoxCI.ReadOnly = true;
            this.textBoxCI.Size = new System.Drawing.Size(80, 21);
            this.textBoxCI.TabIndex = 9;
            // 
            // textBoxID
            // 
            this.textBoxID.BackColor = System.Drawing.SystemColors.Window;
            this.textBoxID.Location = new System.Drawing.Point(51, 19);
            this.textBoxID.Name = "textBoxID";
            this.textBoxID.ReadOnly = true;
            this.textBoxID.Size = new System.Drawing.Size(80, 21);
            this.textBoxID.TabIndex = 1;
            // 
            // buttonOK
            // 
            this.buttonOK.Anchor = (System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right);
            this.buttonOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.buttonOK.Location = new System.Drawing.Point(291, 294);
            this.buttonOK.Name = "buttonOK";
            this.buttonOK.Size = new System.Drawing.Size(87, 23);
            this.buttonOK.TabIndex = 2;
            this.buttonOK.Text = "&OK";
            this.buttonOK.UseVisualStyleBackColor = true;
            this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
            // 
            // TDCellInfoForm
            // 
            this.AcceptButton = this.buttonOK;
            this.CancelButton = this.buttonOK;
            this.ClientSize = new System.Drawing.Size(424, 329);
            this.Controls.Add(groupBox1);
            this.Controls.Add(this.buttonOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "TDCellInfoForm";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Cell Info";
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        private readonly MainModel mainModel;

        private readonly TDCell cell;

        private TextBox textBoxID;

        private TextBox textBoxName;

        private TextBox textBoxCode;

        private TextBox textBoxLAC;

        private TextBox textBoxCI;

        private TextBox textBoxCPI;

        private TextBox textBoxFREQ;

        private TextBox textBoxTCH;

        private TextBox textBoxType;

        private TextBox textBoxDirection;

        private TextBox textBoxMSC;

        private TextBox textBoxBSC;

        private TextBox textBoxBTS;

        private TextBox textBoxLongitude;

        private TextBox textBoxLatitude;

        private ListBox listBoxAntenna;

        private Button buttonOK;
    }
}
