using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class AreaManager
    {
        public static AreaManager GetInstance()
        {
            return instance;
        }

        private static AreaManager instance = new AreaManager();

        private AreaManager()
        {
        }

        public void Clear()
        {
            areaMap.Clear();
            areaList.Clear();
        }

        public CategoryEnum AreaTypes
        {
            get { return (CategoryEnum)CategoryManager.GetInstance()["AreaType"]; }
        }

        public CategoryEnumItem GetAreaType(int type)
        {
            if (AreaTypes != null)
            {
                return AreaTypes[type];
            }
            return null;
        }

        public string GetAreaTypeDesc(int type)
        {
            if (AreaTypes != null && AreaTypes[type] != null)
            {
                return AreaTypes[type].Name;
            }
            return "";
        }

        public List<CategoryEnumItem> this[int type]
        {
            get { return areaList.ContainsKey(type) ? areaList[type] : null; }
        }
        
        public CategoryEnumItem this[int type, int id]
        {
            get
            {
                if (areaMap.ContainsKey(type) && areaMap[type].ContainsKey(id))
                {
                    return areaMap[type][id];
                }
                return null;
            }
        }

        public string GetAreaDesc(int type, int id)
        {
            if (this[type, id] != null)
            {
                return this[type, id].Name;
            }
            return "";
        }

        public void Add(int type, CategoryEnumItem area)
        {
            if (!areaMap.ContainsKey(type))
            {
                areaMap[type] = new Dictionary<int, CategoryEnumItem>();
                areaList[type] = new List<CategoryEnumItem>();
            }
            if (!areaMap[type].ContainsKey(area.ID))
            {
                areaMap[type][area.ID] = area;
                areaList[type].Add(area);
            }
        }

        private readonly Dictionary<int, Dictionary<int, CategoryEnumItem>> areaMap = new Dictionary<int, Dictionary<int, CategoryEnumItem>>();

        private readonly Dictionary<int, List<CategoryEnumItem>> areaList = new Dictionary<int,List<CategoryEnumItem>>();
    }
}
