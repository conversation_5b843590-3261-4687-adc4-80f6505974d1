﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTPoorRSRQRoad
{
    public class PoorRsrqRoadCondition
    {
        public float MaxRsrq { get; set; } = -13.8f;
        public double MinCoverRoadDistance { get; set; } = 50;//最小持续距离
        public double Max2TPDistance { get; set; } = 50;

        public bool IsPoorRsrq(float? rsrq)
        {
            return rsrq <= MaxRsrq && rsrq >= -40;
        }

        public bool MatchMinPoorRsrqDistance(double distance)
        {
            return distance >= MinCoverRoadDistance;
        }

        public bool Match2TestpointsMaxDistance(double distance)
        {
            return distance <= Max2TPDistance;
        }

    }
}
