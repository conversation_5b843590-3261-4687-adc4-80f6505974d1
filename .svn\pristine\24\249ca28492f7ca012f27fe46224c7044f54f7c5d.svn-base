﻿using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTLteTestAcceptance;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteSmallBtsAcceptAnaBase_XJ : DIYAnalyseByCellBackgroundBaseByFile
    {
        string strSaveFolder = "";
        LteTestSettingForm setForm;
        readonly Dictionary<int, SmallBtsAcceptInfo> btsAcceptInfoDic = new Dictionary<int, SmallBtsAcceptInfo>();

        protected SmallBtsAcceptManager_XJ manager;
        protected static readonly object lockObj = new object();
        protected LteSmallBtsAcceptAnaBase_XJ()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.LTE));
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.VoLTE));

            this.Columns = new List<string>();
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_RSRQ");
            Columns.Add("lte_RSSI");
            Columns.Add("lte_SCell_LAC");
            Columns.Add("lte_SCell_CI");

            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_PDCP_UL_Mb");
            Columns.Add("lte_PDCP_DL_Mb");
            Columns.Add("lte_RSRP_Rx0");
            Columns.Add("lte_RSRP_Rx1");
            Columns.Add("lte_NCell_EARFCN");
            Columns.Add("lte_NCell_PCI");
            Columns.Add("lte_NCell_RSRP");
        }
     
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 22000, 22088, "查询");
        }
        protected override bool getCondition()
        {
            if (setForm == null)
            {
                setForm = new LteTestSettingForm("LTE小站验收");
            }
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            strSaveFolder = setForm.GetCondition().SaveFolder;
            return true;
        }
        protected override void getReadyBeforeQuery()
        {
            btsAcceptInfoDic.Clear();
        }
        protected override void doStatWithQuery()
        {
            if (curAnaFileInfo == null || MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }
            manager = new SmallBtsAcceptManager_XJ();
            manager.AnalyzeFile(curAnaFileInfo, MainModel.DTDataManager.FileDataManagers[0]);
            MainModel.DTDataManager.Clear();
            manager.AddFileAcceptInfoToResult(btsAcceptInfoDic);
        }
        protected override void doSomethingAfterAnalyseFiles()
        {
            int i = 0;
            int iSuccessCnt = 0;
            WaitBox.ProgressPercent = 10;
            foreach (SmallBtsAcceptInfo btsAcceptInfo in btsAcceptInfoDic.Values)
            {
                if (WaitBox.CancelRequest)
                {
                    return;
                }
                i++;
                WaitBox.ProgressPercent = (i * 100) / btsAcceptInfoDic.Count;
                WaitBox.Text = "正在导出站点" + btsAcceptInfo.BtsName + "的报告...";

                btsAcceptInfo.CheckBtsIsAccordAccept();
                bool isExportSuccess = ExportSmallBtsReportHelper_XJ.ExportReports(btsAcceptInfo, strSaveFolder);
                if (isExportSuccess)
                {
                    iSuccessCnt++;
                }
            }
            btsAcceptInfoDic.Clear();

            System.Windows.Forms.MessageBox.Show("已导出" + iSuccessCnt + "个站点的小站报告！");
        }
    }

    public class LteSmallBtsAcceptAnaByRegion_XJ : LteSmallBtsAcceptAnaBase_XJ
    {

        private static LteSmallBtsAcceptAnaByRegion_XJ instance = null;
        public static LteSmallBtsAcceptAnaByRegion_XJ GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteSmallBtsAcceptAnaByRegion_XJ();
                    }
                }
            }
            return instance;
        }
        private LteSmallBtsAcceptAnaByRegion_XJ()
            : base()
        {
            FilterSampleByRegion = true;
        }
        public override string Name { get { return "小站单站验收(按区域)"; } }

    }
    public class LteSmallBtsAcceptAnaByFile_XJ : LteSmallBtsAcceptAnaBase_XJ
    {

        private static LteSmallBtsAcceptAnaByFile_XJ instance = null;
        public static LteSmallBtsAcceptAnaByFile_XJ GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteSmallBtsAcceptAnaByFile_XJ();
                    }
                }
            }
            return instance;
        }
        public override string Name { get { return "小站单站验收(按文件)"; } }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
            MainModel.FileInfos.Sort(FileInfo.GetCompareByBeginTimeDesc());
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
