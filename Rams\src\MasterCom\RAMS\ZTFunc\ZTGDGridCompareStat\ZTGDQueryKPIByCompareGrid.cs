﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class ZTGDQueryKPIByCompareGrid : QueryKPIStatByRegion
    {
        public ZTGDQueryKPIByCompareGrid()
            : base()
        {
        }
        public override string Name
        {
            get { return "KPI统计(按对比测试区域)"; }
        }

        public bool isHost { get; set; } = false;
        public double dTimeSpan { get; set; } = 3;
        public bool isOnlyCellAna { get; set; } = false;
        public static Dictionary<int, int> IFileIdDic { get; set; }

        public new void query()
        {
            queryDistrictData(condition.DistrictID);
            afterRecieveAllData();
            if (IsShowResultForm)
            {
                fireShowResult();
            }
            initReportName();
        }

        protected override bool getConditionBeforeQuery()
        {
            return true;
        }

        public bool getSelectReport(QueryCondition condition)
        {
            this.condition = condition;
            if (!isHost)
            {
                GridCompareReport dlg = new GridCompareReport();
                if (dlg.ShowDialog() != DialogResult.OK)
                {
                    return false;
                }
                isOnlyCellAna = dlg.IsOnlyCellAna;
                dTimeSpan = dlg.DTimeSpan;
                curReportStyle = dlg.Report;
                curReportStyle.name = "对比统计上轮_" + curReportStyle.name;
                isQueryAllParams = dlg.IsQueryAllParams;
                ZTGDQueryKPIByCompareGrid.IFileIdDic = new Dictionary<int, int>();
            }
            else
            {
                curReportStyle.name = "对比统计本轮_" + curReportStyle.name;
            }
            KpiDataManager = new KPIDataManager();
            return true;
        }

        public void initReportName()
        {
            curReportStyle.name = curReportStyle.name.Replace("对比统计上轮_", "").Replace("对比统计本轮_", "");
        }

        protected override void recieveAndHandleSpecificStatData(Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            if (!condition.Geometorys.GeoOp.Contains(grid.CenterLng, grid.CenterLat))
            {
                return;
            }
            fillStatData(package, curImgColumnDef, singleStatData);
            int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);

            List<ResvRegion> regs = getStatImgIntersectRegions(lng, lat);
            foreach (ResvRegion reg in regs)
            {
                bool saveAsGrid = false;
                if (curReportStyle != null)
                {
                    saveAsGrid = curReportStyle.HasGridPerCell;
                }
                KpiDataManager.AddStatData(reg.RegionName, reg, fi, singleStatData
                    , saveAsGrid);
            }
            mergeRootNodeData(grid.CenterLng, grid.CenterLat, fi, singleStatData);
        }

        protected new void fillStatData(Package package, List<StatImgDefItem> imgColDefSet
                                  , KPIStatDataBase statData)
        {
            FileInfo fi = null;
            foreach (StatImgDefItem cdf in imgColDefSet)
            {
                byte[] imgBytes = package.Content.GetParamBytes();
                Dictionary<string, DataItem> statInfoDic = StatDataConverter.parseByte(imgBytes);
                foreach (KeyValuePair<string, DataItem> pair in statInfoDic)
                {
                    fi = setKPIStatData(statData, fi, cdf, pair);
                }
            }
        }

        private FileInfo setKPIStatData(KPIStatDataBase statData, FileInfo fi, StatImgDefItem cdf, KeyValuePair<string, DataItem> pair)
        {
            Dictionary<int, bool> serviceIDDic = null;
            Dictionary<string, bool> fileNameDic = null;
            bool needSvrIdSave = true;
            bool needFileNameSave = true;
            if (imgCodeSvrIDDic != null)
            {
                needSvrIdSave = imgCodeSvrIDDic.TryGetValue(pair.Key, out serviceIDDic);
            }
            if (imgCodeFileNameDic != null)
            {
                needFileNameSave = imgCodeFileNameDic.TryGetValue(pair.Key, out fileNameDic);
            }
            if (needSvrIdSave || needFileNameSave || cdf.imgID == 1 || isQueryAllParams)
            {//image1保存的是基础指标（文件信息，距离，点数等）
                if (fi == null)
                {//默认iamge1第一个指标为FileID
                    int fileID = int.Parse(pair.Value.Value.ToString());
                    fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);
                }
                if (IFileIdDic.ContainsKey(fi.ID))
                {
                    int svId = -1;//默认不区分业务类型
                    if (serviceIDDic != null && serviceIDDic.ContainsKey(fi.ServiceType))
                    {//该指标需要根据业务类分开统计
                        svId = fi.ServiceType;
                    }
                    string fileNameKey = getFileNameKey(fi.Name, fileNameDic);

                    statData[pair.Key, svId, fileNameKey] = double.Parse(pair.Value.Value.ToString());
                }
            }

            return fi;
        }
    }
}
