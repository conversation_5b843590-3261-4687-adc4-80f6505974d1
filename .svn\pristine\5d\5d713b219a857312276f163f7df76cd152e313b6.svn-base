﻿using System;
using System.Collections.Generic;
using System.Text;

using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class PolygonRegionCreater
    {
        protected List<object[]> data;
        protected Dictionary<int, string> errorDic;
        protected Shapefile shp;

        public static List<string> ColumnNames { get; set; } = new List<string>()
        {
            "id",
            "网格名",
            "点经度",
            "点纬度",
            "形状",
            "半径（米）",
            "扇面方位角（度）",
            "扇面夹角（度）",
            "正方形边长（米）",
        };

        public PolygonRegionCreater(List<object[]> data, Dictionary<int, string> errorDic)
        {
            this.data = data;
            this.errorDic = errorDic;
        }

        public Shapefile CreateShapefile()
        {
            WaitBox.Text = "正在创建Shapefile图层...";
            InitShapefile();
            InitField();
            for (int i = 0; i < data.Count; ++i)
            {
                WaitBox.ProgressPercent = i * 100 / data.Count;
                InsertRow(i, data[i]);
            }
            return shp;
        }

        protected virtual void InitShapefile()
        {
            shp = new Shapefile();
            shp.CreateNew("", ShpfileType.SHP_POLYGON);
        }

        protected virtual void InitField()
        {
            int fieldIndex = shp.NumFields;
            Field field = new Field();
            field.Name = "ID";
            field.Type = FieldType.INTEGER_FIELD;
            if (!shp.EditInsertField(field, ref fieldIndex, null))
            {
                throw (new Exception("EditInsertField Error: " + shp.get_ErrorMsg(shp.LastErrorCode))); // do not catch these exceptions
            }

            ++fieldIndex;
            field = new Field();
            field.Name = "Name";
            field.Type = FieldType.STRING_FIELD;
            if (!shp.EditInsertField(field, ref fieldIndex, null))
            {
                throw (new Exception("EditInsertField Error: " + shp.get_ErrorMsg(shp.LastErrorCode)));
            }
        }

        protected virtual void InsertRow(int rowIndex, object[] rowValues)
        {
            object[] row = rowValues;
            int i = rowIndex;

            if (!IsValidRow(row))
            {
                errorDic.Add(i, "Row Invalid");
                return;
            }

            double lng, lat;
            if (!double.TryParse(row[2].ToString(), out lng))
            {
                errorDic.Add(i, "Longitude Error");
                return;
            }
            if (!double.TryParse(row[3].ToString(), out lat))
            {
                errorDic.Add(i, "Latitude Error");
                return;
            }

            string shapeType = row[4].ToString();
            Shape shape;
            bool isGet = getShape(row, i, lng, lat, shapeType, out shape);
            if (!isGet)
            {
                return;
            }

            int shapeIndex = shp.NumShapes;
            if (!InsertShape(shapeIndex, shape))
            {
                errorDic.Add(i, "EditInsertShape Error: " + shp.get_ErrorMsg(shp.LastErrorCode));
                return;
            }
            if (!InsertField(shapeIndex, row[0], row[1]))
            {
                errorDic.Add(i, "EditCellValue Error: " + shp.get_ErrorMsg(shp.LastErrorCode));
            }
        }

        private bool getShape(object[] row, int i, double lng, double lat, string shapeType, out Shape shape)
        {
            double radius = 0;
            double directionAngle = 0;
            double coverAngle = 0;
            double edgeLength = 0;
            shape = null;
            if (shapeType == "圆")
            {
                if (!double.TryParse(row[5].ToString(), out radius))
                {
                    errorDic.Add(i, "半径 Error");
                    return false;
                }
                CircleRegion circle = new CircleRegion(lng, lat, radius);
                shape = circle.CreateShape();
            }
            else if (shapeType == "正方形")
            {
                if (!double.TryParse(row[8].ToString(), out edgeLength))
                {
                    errorDic.Add(i, "边长 Error");
                    return false;
                }
                SquareRegion square = new SquareRegion(lng, lat, edgeLength);
                shape = square.CreateShape();
            }
            else if (shapeType == "扇形")
            {
                if (!double.TryParse(row[5].ToString(), out radius))
                {
                    errorDic.Add(i, "半径 Error");
                    return false;
                }
                if (!double.TryParse(row[6].ToString(), out directionAngle))
                {
                    errorDic.Add(i, "扇面方位角 Error");
                    return false;
                }
                if (!double.TryParse(row[7].ToString(), out coverAngle))
                {
                    errorDic.Add(i, "扇面夹角 Error");
                    return false;
                }
                SectorRegion sector = new SectorRegion(lng, lat, radius, directionAngle, coverAngle);
                shape = sector.CreateShape();
            }
            else
            {
                errorDic.Add(i, "形状 错误");
                return false;
            }
            return true;
        }

        protected virtual bool InsertField(int shapeIndex, params object[] fieldValues)
        {
            for (int i = 0; i < fieldValues.Length; ++i)
            {
                if (!shp.EditCellValue(i, shapeIndex, fieldValues[i]))
                {
                    return false;
                }
            }
            return true;
        }

        protected virtual bool InsertShape(int shapeIndex, Shape shape)
        {
            if (!shp.EditInsertShape(shape, ref shapeIndex))
            {
                return false;
            }
            return true;
        }

        protected virtual bool IsValidRow(object[] row)
        {
            if (ColumnNames.Count != row.Length)
            {
                return false;
            }

            for (int i = 0; i < 5; ++i)
            {
                if (row[i] == null)
                {
                    return false;
                }
            }
            return true;
        }
    }

    public class SquareRegion
    {
        private readonly double edgeLength;
        private readonly double centLng;
        private readonly double centLat;
        private readonly double lngPm;
        private readonly double latPm;

        public SquareRegion(double centLng, double centLat, double edgeLength)
        {
            this.edgeLength = edgeLength;
            this.centLng = centLng;
            this.centLat = centLat;
            this.latPm = DistanceTranslator.LatitudePerMeter();
            this.lngPm = latPm;
        }

        public Shape CreateShape()
        {
            double offsetX = lngPm * edgeLength / 2;
            double offsetY = latPm * edgeLength / 2;
            double tlX = centLng - offsetX;
            double tlY = centLat + offsetY;
            double brX = centLng + offsetX;
            double brY = centLat - offsetY;
            return ShapeHelper.CreateRectShape(tlX, tlY, brX, brY);
        }
    }

    public class CircleRegion
    {        
        private readonly double centLng;
        private readonly double centLat;
        private readonly double radius;
        private readonly double latPm;

        public CircleRegion(double centLng, double centLat, double radius)
        {
            this.centLng = centLng;
            this.centLat = centLat;
            this.radius = radius;
            this.latPm = DistanceTranslator.LatitudePerMeter();
        }

        public Shape CreateShape()
        {
            return ShapeHelper.CreateCircleShape(centLng, centLat, latPm * radius);
        }
    }

    public class SectorRegion
    {
        private readonly double centLng;
        private readonly double centLat;
        private readonly double radius;
        private readonly double directionAngle;
        private readonly double coverAngle;

        public SectorRegion(double centLng, double centLat, double radius, double directionAngle, double coverAngle)
        {
            this.centLng = centLng;
            this.centLat = centLat;
            this.radius = radius;
            this.directionAngle = directionAngle;
            this.coverAngle = coverAngle;
        }

        public Shape CreateShape()
        {
            return ShapeHelper.CreateOutdoorCellShape(centLng, centLat, radius * DistanceTranslator.LatitudePerMeter(), (float)directionAngle, (float)coverAngle * 4);
        }
    }
}
