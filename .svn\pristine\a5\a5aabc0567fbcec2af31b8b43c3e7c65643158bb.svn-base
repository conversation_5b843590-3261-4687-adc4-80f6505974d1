﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.BaseInfo
{
    public class TestRoundDBOperator : DIYSQLBase
    {
        readonly int userId = 0;
        private readonly List<TestRound> rounds2Update;
        private readonly bool removeOnly = false;
        public TestRoundDBOperator(List<TestRound> roles2Update,bool removeOnly,int userId)
            : base(MainModel.GetInstance())
        {
            this.rounds2Update = roles2Update;
            this.removeOnly = removeOnly;
            this.userId = userId;
            MainDB = true;
        }

        //sql语句可能过长，需分包处理
        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                if (MainDB)
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                }
                else
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                }

                E_VType[] retArrDef = getSqlRetTypeArr();
                string strsql = getSqlTextString();
                string[] strArr = strsql.Split(';');
                int curIdx = 0;
                while (curIdx < strArr.Length)
                {
                    StringBuilder txt = new StringBuilder();
                    for (; curIdx < strArr.Length; curIdx++)
                    {
                        txt.Append(strArr[curIdx] + ";");
                        if (txt.Length > 6000)
                        {
                            break;
                        }
                    }
                    package.Content.PrepareAddParam();
                    package.Content.AddParam(txt.ToString());
                    StringBuilder sb = geRetArrDefStr(retArrDef);
                    package.Content.AddParam(sb.ToString());
                    clientProxy.Send();
                    System.Threading.Thread.Sleep(1000);
                }
                receiveRetData(clientProxy);
            }
            catch
            {
                //continune
            }
        }

        private StringBuilder geRetArrDefStr(E_VType[] retArrDef)
        {
            StringBuilder sb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    sb.Append((int)retArrDef[i]);
                    if (i < retArrDef.Length - 1)
                    {
                        sb.Append(",");
                    }
                }
            }

            return sb;
        }

        protected override string getSqlTextString()
        {
            StringBuilder sb = new StringBuilder();
            foreach (TestRound round in rounds2Update)
            {
                if (removeOnly)
                {
                    sb.Append(string.Format("update tb_stat_area_whitelist_TestRound set isvalid = 0,userid = {0},updatetime = getdate() where year ={1} and month = {2} and isvalid =1;"
                        ,userId, round.Year, round.Month));
                }
                else
                {//新增 or 修改
                    string str = string.Empty;
                    str = @"if not exists (SELECT top(1) * FROM tb_stat_area_whitelist_TestRound where year ={0} and month = {1} and isvalid =1 )
begin
{2}
end
else
begin
{3}
end;";
                    string insertTxt = @"insert into tb_stat_area_whitelist_TestRound (year,month,begintime,endtime,userid,isvalid,comment) 
values ({0},{1},{2},{3},{4},{5},'{6}');";
                    insertTxt = string.Format(insertTxt, round.Year, round.Month, round.BeginTimeInt, round.EndTimeInt, userId, 1, round.StrDesc);

                    string updateTxt = @"update tb_stat_area_whitelist_TestRound set updatetime = getdate(), begintime={0},endtime={1},userid={2},comment='{3}' where year ={4} and month = {5} and isvalid =1;";
                    updateTxt = string.Format(updateTxt, round.BeginTimeInt, round.EndTimeInt, userId, round.StrDesc, round.Year,round.Month);

                    str = string.Format(str, round.Year, round.Month, insertTxt, updateTxt);

                    sb.AppendLine(str);
                }
            }
            return sb.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }

        public override string Name
        {
            get { return "更新测试轮次表（适用新增，修改，删除）"; }
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
        }
    }
}
