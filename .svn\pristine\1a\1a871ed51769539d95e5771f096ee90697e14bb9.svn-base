﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.CQT
{
    public class DIYSQLDeleteCQTTestTask : DIYSQLInsertCQTTestTask
    {
        public DIYSQLDeleteCQTTestTask(MainModel mm, List<CQTTestTask> task2Insert)
            : base(mm)
        {
            testTaskList = task2Insert;
        }

        protected override string makeSqlTextByCQTTestTask(CQTTestTask task)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("delete from [tb_cqt_testtask] where taskID='");
            sb.Append(task.ID.ToString());
            sb.Append("';delete from tb_cqt_testtask_detail where taskID='");
            sb.Append(task.ID.ToString());
            sb.Append("'");
            return sb.ToString();
        }
    }
}
