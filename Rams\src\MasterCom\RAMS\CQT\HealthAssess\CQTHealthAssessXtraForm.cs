﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTHealthAssessXtraForm : DevExpress.XtraEditors.XtraForm
    {
        public CQTHealthAssessXtraForm(MainModel mainModel)
        {
            InitializeComponent();
            mainmodel = mainModel;
        }

        private MainModel mainmodel;

        public void setData(List<CQTLibrary.PublicItem.EvaluateResult> result)
        {
            this.gridControl1.DataSource = result;
        }

        private void gridView1_RowCellStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
        {
            if (e.Column.Caption == "健康状况" || e.Column.Caption == "网络覆盖" || e.Column.Caption == "网络质量" || e.Column.Caption == "网络容量" || e.Column.Caption == "网络感知" || e.Column.Caption == "网络满意度")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, e.Column);
                if (aa == "健康")
                {
                    e.Appearance.ForeColor = Color.Green;
                }

                if (aa == "良好")
                {
                    e.Appearance.ForeColor = Color.DarkGreen;
                }


                if (aa == "合格")
                {
                    e.Appearance.ForeColor = Color.Orange;
                }

                if (aa == "不合格")
                {
                    e.Appearance.ForeColor = Color.Red;
                }

                if (aa == "满意")
                {
                    e.Appearance.ForeColor = Color.Green;
                }
            }
        }

        //双击查看详情
        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            int[] rows = gridView1.GetSelectedRows();
            if (rows.Length == 0)
                return;
            object o = gridView1.GetRow(rows[0]);
            CQTLibrary.PublicItem.EvaluateResult evaluate = o as CQTLibrary.PublicItem.EvaluateResult;


            if (evaluate != null)
            {


                mainmodel.MainForm.GetMapForm().GoToView(evaluate.FLongitude, evaluate.FLatitude);


                CQTBubbleXtraForm form = new CQTBubbleXtraForm();
                form.setData(evaluate);
                form.Show();




            }
        }

        private void 导出ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.gridView1);
        }
    }
}