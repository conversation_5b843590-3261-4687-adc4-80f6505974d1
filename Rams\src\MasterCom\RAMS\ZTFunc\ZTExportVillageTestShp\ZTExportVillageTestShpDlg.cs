﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTExportVillageTestShpDlg : BaseDialog
    {
        public ZTExportVillageTestShpDlg()
        {
            InitializeComponent();

            loadConfig();
        }

        private readonly string configPath = Application.StartupPath + @"\config\VillageTest.xml";
        public string DistrictPath { get; private set; }
        public string ExportShpPath { get; private set; }

        protected bool loadConfig()
        {
            try
            {
                XmlConfigFile configFile = new XmlConfigFile(configPath);
                if (configFile.Load())
                {
                    XmlElement configInfo = configFile.GetConfig("Path");
                    object obj = configFile.GetItemValue(configInfo, "DistrictPath");
                    DistrictPath = obj.ToString();
                    txtDistrictPath.Text = DistrictPath;
                    obj = configFile.GetItemValue(configInfo, "ExportShpPath");
                    ExportShpPath = obj.ToString();
                    txtExportShpPath.Text = ExportShpPath;
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        protected void saveConfig()
        {
            XmlConfigFile xcfg = new XmlConfigFile();
            XmlElement path = xcfg.AddConfig("Path");
            xcfg.AddItem(path, "DistrictPath", DistrictPath);
            xcfg.AddItem(path, "ExportShpPath", ExportShpPath);
            xcfg.Save(configPath);
        }

        private void btnDistrict_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog fbd = new FolderBrowserDialog();
            if (fbd.ShowDialog() == DialogResult.OK)
            {
                txtDistrictPath.Text = fbd.SelectedPath;
            }
        }

        private void btnExportShp_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog fbd = new FolderBrowserDialog();
            if (fbd.ShowDialog() == DialogResult.OK)
            {
                txtExportShpPath.Text = fbd.SelectedPath;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (txtDistrictPath.Text == "" || txtExportShpPath.Text == "")
            {
                DialogResult = DialogResult.None;
            }
            else
            {
                DistrictPath = txtDistrictPath.Text;
                ExportShpPath = txtExportShpPath.Text;
                saveConfig();
                DialogResult = DialogResult.OK;
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
