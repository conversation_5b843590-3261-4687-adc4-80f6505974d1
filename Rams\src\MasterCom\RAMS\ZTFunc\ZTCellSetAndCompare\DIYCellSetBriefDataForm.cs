﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public partial class DIYCellSetBriefDataForm : MinCloseForm
    {
        public DIYCellSetBriefDataForm(MainModel mainModel)
            :base(mainModel)
        {
            InitializeComponent();
         DisposeWhenClose = true;
        }

        private List<CellSetDetailInfo> cellSetDetailInfoListNew = new List<CellSetDetailInfo>();
        private List<CellSetSummaryInfo> cellSetSummaryInfoListNew = new List<CellSetSummaryInfo>();
        private List<CellSetStat> cellSetStatListNew = new List<CellSetStat>();

        public void FillData(List<CellSetDetailInfo> cellSetDetailInfoList, 
            List<CellSetSummaryInfo> cellSetSummaryInfoList, List<CellSetStat> cellSetStatList)
        {
            this.cellSetDetailInfoListNew.Clear();
            this.cellSetSummaryInfoListNew.Clear();
            this.cellSetStatListNew.Clear();

            cellSetDetailInfoListNew = cellSetDetailInfoList;
            cellSetSummaryInfoListNew = cellSetSummaryInfoList;
            cellSetStatListNew = cellSetStatList;

            int idxOne = 1;
            foreach (CellSetDetailInfo cellSetD in cellSetDetailInfoList)
            {
                ListViewItem listViewItem = new ListViewItem();
                listViewItem.Text = idxOne.ToString();
                listViewItem.SubItems.Add(cellSetD.strCity);
                listViewItem.SubItems.Add(cellSetD.strCarrier);                
                listViewItem.SubItems.Add(cellSetD.strCellType);
                listViewItem.SubItems.Add(cellSetD.strGridType);
                listViewItem.SubItems.Add(cellSetD.strGrid);
                listViewItem.SubItems.Add(cellSetD.strServiceType);
                listViewItem.SubItems.Add(cellSetD.strCellName);
                listViewItem.SubItems.Add(cellSetD.strBtsType);
                listViewItem.SubItems.Add(cellSetD.iLac.ToString());
                listViewItem.SubItems.Add(cellSetD.iCi.ToString());
                listViewItem.SubItems.Add(cellSetD.strBtsType);
                listViewItem.SubItems.Add(cellSetD.sampelSum.ToString());

                listView1.Items.Add(listViewItem);
                idxOne++;
            }

            idxOne = 1;
            foreach (CellSetSummaryInfo cellSetSum in cellSetSummaryInfoList)
            {
                ListViewItem listViewItem = new ListViewItem();
                listViewItem.Text = idxOne.ToString();
                listViewItem.SubItems.Add(cellSetSum.strCity);
                listViewItem.SubItems.Add(cellSetSum.strCarrier);
                listViewItem.SubItems.Add(cellSetSum.strCellType);
                listViewItem.SubItems.Add(cellSetSum.strGridType);
                listViewItem.SubItems.Add(cellSetSum.strGrid);
                listViewItem.SubItems.Add(cellSetSum.strServiceType);
                listViewItem.SubItems.Add(cellSetSum.IBtsCount.ToString());
                listViewItem.SubItems.Add(cellSetSum.iCellCount.ToString());
                listViewItem.SubItems.Add(cellSetSum.IIndoorCellCount.ToString());

                listView2.Items.Add(listViewItem);
                idxOne++;
            }

            idxOne = 1;
            foreach (CellSetStat cellss in cellSetStatList)
            {
                ListViewItem listViewItem = new ListViewItem();
                listViewItem.Text = idxOne.ToString();
                listViewItem.SubItems.Add(cellss.strCity);
                listViewItem.SubItems.Add(cellss.strGridType);
                listViewItem.SubItems.Add(cellss.strGrid);

                listViewItem.SubItems.Add(cellss.iAllTdCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iAllYDGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iAllWCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iAllLTGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iAllEvodCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iAllCdmaCellNum.ToString());

                listViewItem.SubItems.Add(cellss.iFTdCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iFWCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iFEvodoCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iFYDGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iFLTGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iFDXCdmaCellNum.ToString());  

                listViewItem.SubItems.Add(cellss.iVTdCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iVTdGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iVWCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iVWGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iVCd2000CellNum.ToString());
                listViewItem.SubItems.Add(cellss.iVGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iVLtGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iVCdCellNum.ToString());

                listViewItem.SubItems.Add(cellss.iDTdCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iDTdGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iDWCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iDWGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iDEvdoCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iDEvdoCdma1xCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iDGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iDLtGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellss.iDCdCellNum.ToString());
                          
                listView3.Items.Add(listViewItem);
                idxOne++;
            }
        }

        private void E4xcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            outPutCellSetByExcel(true);
        }
        /// <summary>
        /// 将小区集结果集导出EXCEL
        /// </summary>
        private void outPutCellSetByExcel(bool isCont)
        {
            List<NPOIRow> datas = new List<NPOIRow>();
            List<NPOIRow> datas2 = new List<NPOIRow>();
            List<NPOIRow> datas3 = new List<NPOIRow>();

            #region EXCEL-SHEET1列表构造
            NPOIRow nr1 = new NPOIRow();
            List<object> cols = new List<object>();
            cols.Add("序号");
            cols.Add("城市");
            cols.Add("运营商");
            cols.Add("小区类型");
            cols.Add("图层类型");
            cols.Add("网格");
            cols.Add("业务类型");
            cols.Add("小区名称");
            cols.Add("基站名称");
            cols.Add("LAC");
            cols.Add("CI");
            cols.Add("基站类型");
            cols.Add("采样点数");

            nr1.cellValues = cols;
            datas.Add(nr1);
            #endregion

            #region EXCEL-SHEET2列表构造
            NPOIRow nr2 = new NPOIRow();
            List<object> cols2 = new List<object>();
            cols2.Add("序号");
            cols2.Add("城市");
            cols2.Add("运营商");
            cols2.Add("小区类型");
            cols2.Add("图层类型");
            cols2.Add("网格");
            cols2.Add("业务类型");
            cols2.Add("基站数目");
            cols2.Add("小区数目");
            cols2.Add("室内小区数目");

            nr2.cellValues = cols2;
            datas2.Add(nr2);
            #endregion

            #region EXCEL-SHEET3列表构造
            NPOIRow nr3 = new NPOIRow();
            List<object> cols3 = new List<object>();
            cols3.Add("序号");
            cols3.Add("城市");
            cols3.Add("图层类型");
            cols3.Add("网格");

            cols3.Add("所有TD小区");
            cols3.Add("所有移动GSM小区");
            cols3.Add("所有W小区");
            cols3.Add("所有联通GSM小区");
            cols3.Add("所有EVDO小区");
            cols3.Add("所有CMDA小区");

            cols3.Add("TD锁网小区");
            cols3.Add("W锁网小区");
            cols3.Add("EVDO锁网小区");
            cols3.Add("移动GSM锁网小区");
            cols3.Add("联通GSM锁网小区");
            cols3.Add("电信CDMA锁网小区");  

            cols3.Add("移动语音TD小区");
            cols3.Add("移动语音TD-GSM小区");
            cols3.Add("联通语音W小区");
            cols3.Add("联通语音W-GSM小区");
            cols3.Add("电信CDMA2000空闲小区");
            cols3.Add("移动语音GSM小区");
            cols3.Add("联通语音GSM小区");
            cols3.Add("电信语音CDMA小区");

            cols3.Add("移动数据TD小区");
            cols3.Add("移动数据TD-GSM小区");
            cols3.Add("联通数据W小区");
            cols3.Add("联通数据W-GSM小区");
            cols3.Add("电信数据CDMA2000小区");
            cols3.Add("电信数据CDMA小区(2G)");
            cols3.Add("移动数据GSM小区");
            cols3.Add("联通数据GSM小区");
            cols3.Add("电信数据CDMA小区");

            nr3.cellValues = cols3;
            datas3.Add(nr3);
            #endregion

            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            List<string> sheetNames = new List<string>();
            
            int idx = 0;
            if (isCont)
            {
                foreach (CellSetDetailInfo cellSetDetail in cellSetDetailInfoListNew)
                {
                    NPOIRow nr = new NPOIRow();
                    List<object> objs = new List<object>();
                    idx++;

                    objs.Add(idx.ToString());
                    objs.Add(cellSetDetail.strCity.ToString());
                    objs.Add(cellSetDetail.strCarrier.ToString());
                    objs.Add(cellSetDetail.strCellType.ToString());
                    objs.Add(cellSetDetail.strGridType.ToString());
                    objs.Add(cellSetDetail.strGrid.ToString());
                    objs.Add(cellSetDetail.strServiceType.ToString());
                    objs.Add(cellSetDetail.strCellName.ToString());
                    objs.Add(cellSetDetail.strBtsName.ToString());
                    objs.Add(cellSetDetail.iLac.ToString());
                    objs.Add(cellSetDetail.iCi.ToString());
                    objs.Add(cellSetDetail.strBtsType.ToString());
                    objs.Add(cellSetDetail.sampelSum.ToString());

                    nr.cellValues = objs;
                    datas.Add(nr);
                }
            }
            int idx2 = 0;
            foreach (CellSetSummaryInfo cellSetSum in cellSetSummaryInfoListNew)
            {
                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();
                idx2++;

                objs.Add(idx2.ToString());
                objs.Add(cellSetSum.strCity.ToString());
                objs.Add(cellSetSum.strCarrier.ToString());
                if (cellSetSum.strCellType.Equals("CDMA"))
                {
                    objs.Add("EVDO-CDMA(2G)");
                }
                else
                {
                    objs.Add(cellSetSum.strCellType.ToString());
                }
                objs.Add(cellSetSum.strGridType.ToString());
                objs.Add(cellSetSum.strGrid.ToString());
                objs.Add(cellSetSum.strServiceType.ToString());
                objs.Add(cellSetSum.IBtsCount.ToString());
                objs.Add(cellSetSum.iCellCount.ToString());
                objs.Add(cellSetSum.IIndoorCellCount.ToString());

                nr.cellValues = objs;
                datas2.Add(nr);
            }

            int idx3 = 0;
            foreach (CellSetStat cellSetStat in cellSetStatListNew)
            {
                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();
                idx3++;
                objs.Add(idx3.ToString());
                objs.Add(cellSetStat.strCity);
                objs.Add(cellSetStat.strGridType);
                objs.Add(cellSetStat.strGrid);

                objs.Add(cellSetStat.iAllTdCellNum);
                objs.Add(cellSetStat.iAllYDGsmCellNum);
                objs.Add(cellSetStat.iAllWCellNum);
                objs.Add(cellSetStat.iAllLTGsmCellNum);
                objs.Add(cellSetStat.iAllEvodCellNum);
                objs.Add(cellSetStat.iAllCdmaCellNum);

                objs.Add(cellSetStat.iFTdCellNum);
                objs.Add(cellSetStat.iFWCellNum);
                objs.Add(cellSetStat.iFEvodoCellNum);
                objs.Add(cellSetStat.iFYDGsmCellNum);
                objs.Add(cellSetStat.iFLTGsmCellNum);
                objs.Add(cellSetStat.iFDXCdmaCellNum);

                objs.Add(cellSetStat.iVTdCellNum);
                objs.Add(cellSetStat.iVTdGsmCellNum);
                objs.Add(cellSetStat.iVWCellNum);
                objs.Add(cellSetStat.iVWGsmCellNum);
                objs.Add(cellSetStat.iVCd2000CellNum);
                objs.Add(cellSetStat.iVGsmCellNum);
                objs.Add(cellSetStat.iVLtGsmCellNum);
                objs.Add(cellSetStat.iVCdCellNum);

                objs.Add(cellSetStat.iDTdCellNum);
                objs.Add(cellSetStat.iDTdGsmCellNum);
                objs.Add(cellSetStat.iDWCellNum);
                objs.Add(cellSetStat.iDWGsmCellNum);
                objs.Add(cellSetStat.iDEvdoCellNum);
                objs.Add(cellSetStat.iDEvdoCdma1xCellNum);
                objs.Add(cellSetStat.iDGsmCellNum);
                objs.Add(cellSetStat.iDLtGsmCellNum);
                objs.Add(cellSetStat.iDCdCellNum);
                             
                nr.cellValues = objs;
                datas3.Add(nr);
            }
            if (isCont)
            {
                nrDatasList.Add(datas);
                sheetNames.Add("小区集概明细");
            }
            nrDatasList.Add(datas2);
            nrDatasList.Add(datas3);
           
            sheetNames.Add("小区集概要统计");
            sheetNames.Add("小区集按业务类型统计");


            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);

        }

        private void toolStripMenu_Click(object sender, EventArgs e)
        {
            outPutCellSetByExcel(false);
        }
    }
}
