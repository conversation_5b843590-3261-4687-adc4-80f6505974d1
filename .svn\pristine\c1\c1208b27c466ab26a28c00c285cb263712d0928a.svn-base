﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Util;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CellComparisonForm : MinCloseForm
    {
        List<cellComparisonInfo> cellComparisonInfoList = null;

        public CellComparisonForm()
            :base()
        {
            InitializeComponent();

            cellComparisonInfoList = MainModel.CellComparisonInfoList;
            if (MainModel.SetQueryForm.trackBarChannel != null)
            {
                if (MainModel.SetQueryForm.trackBarChannel.Value == 1)
                {
                    this.textBoxCurChannel.Text = "900";
                }
                else if (MainModel.SetQueryForm.trackBarChannel.Value == 2)
                {
                    this.textBoxCurChannel.Text = "1800";
                }
                else if (MainModel.SetQueryForm.trackBarChannel.Value == 3)
                {
                    this.textBoxCurChannel.Text = "900&1800";
                }
            }
        }

        public void FillData()
        {
            cellComparisonInfoList = MainModel.CellComparisonInfoList;
            dataGridView.RowCount = cellComparisonInfoList.Count;

            for (int row = 0; row < cellComparisonInfoList.Count; row++)
            {
                dataGridView.Rows[row].Cells[0].Value = cellComparisonInfoList[row].cellName;  //小区名
                dataGridView.Rows[row].Cells[1].Value = cellComparisonInfoList[row].lacCi;  //LacCi
                dataGridView.Rows[row].Cells[2].Value = cellComparisonInfoList[row].bcchBsic; //BcchBsic
                dataGridView.Rows[row].Cells[3].Value = cellComparisonInfoList[row].sampleScanCount;//扫频采样点数目
                dataGridView.Rows[row].Cells[4].Value = cellComparisonInfoList[row].sampleDtCount;//Dt采样点数目
                dataGridView.Rows[row].Cells[5].Value = cellComparisonInfoList[row].avgRxlevScan;//平均Scan电平 
                dataGridView.Rows[row].Cells[6].Value = cellComparisonInfoList[row].avgRxlevDt; //平均Dt电平
                dataGridView.Rows[row].Cells[7].Value = cellComparisonInfoList[row].scanPct; //扫频占比
                dataGridView.Rows[row].Cells[8].Value = cellComparisonInfoList[row].dtPct; //GSM占比
                dataGridView.Rows[row].Cells[9].Value = cellComparisonInfoList[row].differPct;  //占比差值

                dataGridView.Rows[row].Tag = cellComparisonInfoList[row];
            }
        }

        private void mi_exportExcel_Click(object sender, EventArgs e)
        {
            ExcelOperator.ByTxtExcel(dataGridView);
        }

        private void dataGridView_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            cellComparisonInfo selCellComparisonInfo =((DataGridView)sender).CurrentRow.Tag as cellComparisonInfo;

            mModel.DTDataManager.Clear();
            foreach (TestPoint tpDt in selCellComparisonInfo.dtTestpointList)
            {
                mModel.DTDataManager.Add(tpDt as TestPointDetail); 
            }
            foreach (TestPoint tpScan in selCellComparisonInfo.scanTestpointList)
            {
                mModel.DTDataManager.Add(tpScan as ScanTestPoint_G); 
            }

            MapForm mapForm = MainModel.MainForm.GetMapForm();
            if (mapForm != null)
            {
                MapDTLayer dtLayer = mapForm.GetDTLayer();
                foreach (MapSerialInfo mapSerialInfo in dtLayer.SerialInfos) //选择显示路测和扫频的采样点指标
                {
                    if (mapSerialInfo.Name == "GSM_SCAN_RxLev" || mapSerialInfo.Name == "GSM RxLevSub")
                    {
                        mapSerialInfo.Visible = true;
                    }
                }
            }

            mModel.FireDTDataChanged(this);
            mModel.RefreshLegend();
        }

        private void buttonChangeChannel_Click(object sender, EventArgs e)
        {
            SetQueryForm setQueryForm = MainModel.SetQueryForm;
            if (this.radioButton900.Checked)   //选择900频段
            {
                setQueryForm.trackBarChannel.Value=1;
            }
            else if (this.radioButton1800.Checked)  //选择1800频段
            {
                setQueryForm.trackBarChannel.Value = 2;
            }
            else
            {
                setQueryForm.trackBarChannel.Value = 3;
            }
            WaitBox.Show(setQueryForm.compareTpGridDicDTToTpGridDicScan);
            WaitBox.Show(setQueryForm.queryStrongestCell);
            setQueryForm.isFirstTimeFireShowForm = false;
            setQueryForm.fireShowForm();
            FillData();

            if (MainModel.SetQueryForm.trackBarChannel.Value == 1)
            {
                this.textBoxCurChannel.Text = "900";
            }
            else if (MainModel.SetQueryForm.trackBarChannel.Value == 2)
            {
                this.textBoxCurChannel.Text = "1800";
            }
            else if (MainModel.SetQueryForm.trackBarChannel.Value == 3)
            {
                this.textBoxCurChannel.Text = "900&1800";
            }
        }

        private void CellComparisonForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            //释放内存-------------------------
            MainModel.SetQueryForm = null;
            MainModel.CellComparisonInfoList = null;
            //----------------------------------
        }
    }
}
