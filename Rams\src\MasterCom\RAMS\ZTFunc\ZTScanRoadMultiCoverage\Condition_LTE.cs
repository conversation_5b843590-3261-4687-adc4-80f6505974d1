﻿using System;
using MasterCom.RAMS.Model;
using System.Collections.Generic;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class Condition_LTE
    {
        public int CoverBandDiff { get; set; }
        public int AbsoluteValue { get; set; }
        public int ValidValue { get; set; }
        public bool CheckDualBandJT { get; set; }
        public bool CheckFreqBandOnly { get; set; }

        public bool CheckBandDiffFreq { get; set; }

        /// <summary>
        ///运营商 0：移动 1：电信 2：联通
        /// </summary>
        public int CarrierId { get; set; }

        /// <summary>
        /// 频点集合
        /// </summary>
        public List<FreqPoint> ListFreqPoint { get; set; }


        public bool SaveTestPoint { get; set; }

        public DelegatePrev PrevHandler { get; set; }

        public bool CheckAllCellRsrp { get; set; }

        public int AllCellRsrpMin { get; set; }

        public delegate bool DelegatePrev(TestPoint testPoint, out List<CCellWithRsrp> cellRsrps);
        public void SetHandler(DelegatePrev handleDualBandJT, DelegatePrev handleBandType, DelegatePrev handleDiffFreq)
        {
            if (CheckDualBandJT)
            {
                PrevHandler = handleDualBandJT;
            }
            else if(CheckBandDiffFreq)
            {
                PrevHandler = handleDiffFreq;
            }
            else//CheckFreqBand 频段操作
            {
                PrevHandler = handleBandType;
            }
        }
    }
}
