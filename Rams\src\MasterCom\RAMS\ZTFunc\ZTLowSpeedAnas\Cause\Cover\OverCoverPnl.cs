﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    public partial class OverCoverPnl : UserControl
    {
        public OverCoverPnl()
        {
            InitializeComponent();
        }

        OverCoverCause mainReason = null;
        OverCoverNoSite nositeR = null;
        OverCoverLackNbCell lncR = null;
        OverCoverHandoverParam hoR = null;
        public void LinkCondition(OverCoverCause reason)
        {
            if (reason == null)
            {
                return;
            }
            this.mainReason = reason;
            foreach (CauseBase item in reason.SubCauses)
            {
                if (item is OverCoverNoSite)
                {
                    nositeR = item as OverCoverNoSite;
                }
                else if (item is OverCoverLackNbCell)
                {
                    lncR = item as OverCoverLackNbCell;
                }
                else if (item is OverCoverHandoverParam)
                {
                    hoR = item as OverCoverHandoverParam;
                }
            }
            numRSRPMin.Value = (decimal)mainReason.RSRPMin;
            numRSRPMin.ValueChanged += numRSRPMin_ValueChanged;

            if (nositeR != null)
            {
                numDisMin.Value = (decimal)nositeR.RadiusMin;
                numDisMin.ValueChanged += numDisMin_ValueChanged;
                lblNoSite.Text = string.Format("周围{0}米内，没有其它LTE基站", nositeR.RadiusMin);
            }

            if (lncR != null)
            {
                numDisMin2.Value = (decimal)lncR.RadiusMin;
                numDisMin2.ValueChanged += numDisMin2_ValueChanged;
                lblNCell.Text = string.Format("周围{0}米内，有其它LTE基站", lncR.RadiusMin);
            }

            if (hoR != null)
            {
                numRSRPDiff.Value = (decimal)hoR.RSRPDiff;
                numRSRPDiff.ValueChanged += numRSRPDiff_ValueChanged;
            }
        }

        void numRSRPDiff_ValueChanged(object sender, EventArgs e)
        {
            hoR.RSRPDiff = (float)numRSRPDiff.Value;
        }

        void numDisMin2_ValueChanged(object sender, EventArgs e)
        {
            lncR.RadiusMin = (float)numDisMin2.Value;
            lblNCell.Text = string.Format("周围{0}米内，有其它LTE基站", lncR.RadiusMin);
            numDisMin.Value = numDisMin2.Value;
        }

        void numDisMin_ValueChanged(object sender, EventArgs e)
        {
            nositeR.RadiusMin = (float)numDisMin.Value;
            lblNoSite.Text = string.Format("周围{0}米内，没有其它LTE基站", nositeR.RadiusMin);
            numDisMin2.Value = numDisMin.Value;
        }

        void numRSRPMin_ValueChanged(object sender, EventArgs e)
        {
            mainReason.RSRPMin = (float)numRSRPMin.Value;
        }

    }
}
