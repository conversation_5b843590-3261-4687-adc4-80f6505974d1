﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTMultiCoverQuery_W : ZTDiyRoadMultiCoverageQueryByRegion_TD
    {
        public ZTMultiCoverQuery_W(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get { return "重叠覆盖分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 14000, 14020, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            rsrpParamInfo = DTDisplayParameterManager.GetInstance()["WCDMA", "TotalRSCP"];
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "W_TotalRSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysLAI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysCellID";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_frequency";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_Reference_PSC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            for (int i = 0; i < 6; i++)
            {
                param = new Dictionary<string, object>();
                param["param_name"] = "W_SNeiFreq";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "W_SNeiPSC";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "W_SNeiRSCP";
                param["param_arg"] = i;
                columnsDef.Add((object)param);
            }

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"WCDMA_RSCP");
            tmpDic.Add("themeName", (object)"WCDMA_RSCP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override bool getConditionBeforeQuery()
        {
            if (xtraSetRoadfrom == null)
            {
                xtraSetRoadfrom = new XtraSetRoadMultiForm("WCDMA");
            }
            return base.getConditionBeforeQuery();
        }

        protected override void doWithDTData(TestPoint testPoint)
        {
            string strRegionName = isContainPoint(new DbPoint(testPoint.Longitude, testPoint.Latitude));
            if (strRegionName == null) return;

            int relativeLevel = 0;
            List<string> relCovCellsName = new List<string>();
            int absoluteLevel = 0;
            List<string> absCovCellsName = new List<string>();
            int relANDabsLevel = 0;
            List<string> relANDabsCovCellsName = new List<string>();
            float maxRxlev = 0;
            string mainCellName = "";

            Dictionary<string, float> cellRscpDic = getCellList(testPoint, ref maxRxlev, ref mainCellName);
            if (cellRscpDic.Count == 0)
            {
                return;
            }
            foreach (string cellName in cellRscpDic.Keys)
            {
                float rscp = cellRscpDic[cellName];
                if (rscp > setRxlev)//绝对覆盖度
                {
                    absoluteLevel++;
                    absCovCellsName.Add(cellName);
                }
                if (rscp - maxRxlev > -setRxlevDiff)//相对覆盖度
                {
                    relativeLevel++;
                    relCovCellsName.Add(cellName);
                }

                if ((rscp > setRxlev) && (rscp - maxRxlev > -setRxlevDiff))
                {
                    relANDabsLevel++;
                    relANDabsCovCellsName.Add(cellName);
                }
            }

            bool invalidate = maxRxlev < invalidPointRxLev;
            RoadMultiCoverageInfo_TD info = new RoadMultiCoverageInfo_TD(testPoint, maxRxlev, mainCellName, relativeLevel, relCovCellsName, absoluteLevel, absCovCellsName,
            relANDabsLevel, relANDabsCovCellsName, saveTestPoint, invalidate);
            MainModel.RegionRoadMultiCoveragePoints_TD[strRegionName].Add(info);
            MainModel.RegionRoadMultiCoveragePoints_TD["全部汇总"].Add(info);
        }

        private DTDisplayParameterInfo rsrpParamInfo = null;

        /// <summary>
        /// 获取信号列表
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        private Dictionary<string, float> getCellList(TestPoint testPoint, ref float maxRxlev, ref string mainCellName)
        {
            Dictionary<string, float> cellRscpDic = new Dictionary<string, float>();
            maxRxlev = float.MinValue;

            //////////////////////////////////////////////////////////////////////////主服小区 begin
            float? rxlev = (float?)testPoint["W_TotalRSCP"];
            mainCellName = string.Empty;
            if (rxlev != null && rxlev >= rsrpParamInfo.ValueMin && rxlev <= rsrpParamInfo.ValueMax)
            {
                int? bcch = (int?)testPoint["W_frequency"];
                int? bsic = (int?)testPoint["W_Reference_PSC"];
                if (bcch != null && bsic != null)
                {
                    mainCellName = bcch.ToString() + "_" + bsic.ToString();
                    maxRxlev = (float)rxlev;
                    cellRscpDic[mainCellName] = (float)rxlev;
                }
            }
            //////////////////////////////////////////////////////////////////////////主服小区 end

            //////////////////////////////////////////////////////////////////////////邻区 begin
            for (int i = 0; i < 6; i++)
            {
                rxlev = (float?)testPoint["W_SNeiRSCP", i];
                int? nBcch = (int?)testPoint["W_SNeiFreq", i];
                int? nCpi = (int?)testPoint["W_SNeiPSC", i];
                if (rxlev == null || nBcch == null || nCpi == null
                    || rxlev > rsrpParamInfo.ValueMax || rxlev < rsrpParamInfo.ValueMin)
                {
                    continue;
                }

                string cellName = nBcch.ToString() + "_" + nCpi.ToString();
                cellRscpDic[cellName] = (float)rxlev;
                if (rxlev > maxRxlev)
                {
                    mainCellName = cellName;
                    maxRxlev = (float)rxlev;
                }
            }
            //////////////////////////////////////////////////////////////////////////邻区 end

            return cellRscpDic;
        }
    }
}
