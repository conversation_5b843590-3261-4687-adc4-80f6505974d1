﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Net
{
    public class DiyQueryInjectGrid : DIYInjectionGridQuery
    {
        readonly int districtID;

        public DiyQueryInjectGrid(int districtID)
          : base(MainModel.GetInstance())
        {
            this.districtID = districtID;
        }

        public override string Name
        {
            get { return "渗透率栅格查询"; }
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            injectGridMatrix = null;
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, districtID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                queryOneDistrict(clientProxy);
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void queryOneDistrict(ClientProxy clientProxy)
        {
            queryInThread(clientProxy);
            MainModel.LastSearchGeometry = Condition.Geometorys.Region;
            if (MainModel.MultiGeometrys && Condition.Geometorys.SelectedResvRegions != null && condition.Geometorys.SelectedResvRegions.Count > 0)//
            {
                MainModel.StreetInjectResvRegions = condition.Geometorys.SelectedResvRegions;
            }
            else
            {
                MainModel.StreetInjectResvRegions.Clear();
                ResvRegion resvRegion = new ResvRegion();
                resvRegion.RegionName = "当前选择区域";
                resvRegion.Shape = Condition.Geometorys.Region;
                MainModel.StreetInjectResvRegions.Add(resvRegion);
            }
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;

                foreach (TimePeriod period in condition.Periods)
                {
                    prepareQueryPackage(package, period);
                    prepareNeededInfo(package);
                    clientProxy.Send();
                    recieveInfo(clientProxy);
                }
            }
            catch (Exception e)
            {
                log.Error("Error:" + e.Message);
            }
        }
    }
}
