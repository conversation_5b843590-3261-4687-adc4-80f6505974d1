﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTCellCoverageRangeAnaSetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.rdbServAndNbCell = new System.Windows.Forms.RadioButton();
            this.rdbServCell = new System.Windows.Forms.RadioButton();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label2 = new System.Windows.Forms.Label();
            this.numNbDiffServ = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.rdbSignalStrength = new System.Windows.Forms.RadioButton();
            this.rdbOccupiedCell = new System.Windows.Forms.RadioButton();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numNbDiffServ)).BeginInit();
            this.groupBox3.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(267, 164);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 41;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(171, 164);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 40;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.rdbServAndNbCell);
            this.groupBox1.Controls.Add(this.rdbServCell);
            this.groupBox1.Location = new System.Drawing.Point(21, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(321, 63);
            this.groupBox1.TabIndex = 45;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "小区筛选设置";
            // 
            // rdbServAndNbCell
            // 
            this.rdbServAndNbCell.AutoSize = true;
            this.rdbServAndNbCell.Checked = true;
            this.rdbServAndNbCell.Location = new System.Drawing.Point(47, 28);
            this.rdbServAndNbCell.Name = "rdbServAndNbCell";
            this.rdbServAndNbCell.Size = new System.Drawing.Size(77, 16);
            this.rdbServAndNbCell.TabIndex = 1;
            this.rdbServAndNbCell.TabStop = true;
            this.rdbServAndNbCell.Text = "主服+邻区";
            this.rdbServAndNbCell.UseVisualStyleBackColor = true;
            // 
            // rdbServCell
            // 
            this.rdbServCell.AutoSize = true;
            this.rdbServCell.Location = new System.Drawing.Point(192, 28);
            this.rdbServCell.Name = "rdbServCell";
            this.rdbServCell.Size = new System.Drawing.Size(47, 16);
            this.rdbServCell.TabIndex = 0;
            this.rdbServCell.Text = "主服";
            this.rdbServCell.UseVisualStyleBackColor = true;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label2);
            this.groupBox2.Controls.Add(this.numNbDiffServ);
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Location = new System.Drawing.Point(21, 85);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(321, 63);
            this.groupBox2.TabIndex = 46;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "邻区筛选设置";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(259, 31);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(17, 12);
            this.label2.TabIndex = 2;
            this.label2.Text = "dB";
            // 
            // numNbDiffServ
            // 
            this.numNbDiffServ.Location = new System.Drawing.Point(171, 26);
            this.numNbDiffServ.Name = "numNbDiffServ";
            this.numNbDiffServ.Size = new System.Drawing.Size(81, 21);
            this.numNbDiffServ.TabIndex = 1;
            this.numNbDiffServ.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numNbDiffServ.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(40, 32);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(125, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "与主服信号强度差异≤";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.rdbSignalStrength);
            this.groupBox3.Controls.Add(this.rdbOccupiedCell);
            this.groupBox3.Location = new System.Drawing.Point(21, 193);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(321, 73);
            this.groupBox3.TabIndex = 47;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "采样点颜色设置";
            this.groupBox3.Visible = false;
            // 
            // rdbSignalStrength
            // 
            this.rdbSignalStrength.AutoSize = true;
            this.rdbSignalStrength.Checked = true;
            this.rdbSignalStrength.Location = new System.Drawing.Point(49, 34);
            this.rdbSignalStrength.Name = "rdbSignalStrength";
            this.rdbSignalStrength.Size = new System.Drawing.Size(71, 16);
            this.rdbSignalStrength.TabIndex = 3;
            this.rdbSignalStrength.TabStop = true;
            this.rdbSignalStrength.Text = "信号强度";
            this.rdbSignalStrength.UseVisualStyleBackColor = true;
            // 
            // rdbOccupiedCell
            // 
            this.rdbOccupiedCell.AutoSize = true;
            this.rdbOccupiedCell.Location = new System.Drawing.Point(194, 34);
            this.rdbOccupiedCell.Name = "rdbOccupiedCell";
            this.rdbOccupiedCell.Size = new System.Drawing.Size(71, 16);
            this.rdbOccupiedCell.TabIndex = 2;
            this.rdbOccupiedCell.TabStop = true;
            this.rdbOccupiedCell.Text = "占用小区";
            this.rdbOccupiedCell.UseVisualStyleBackColor = true;
            // 
            // ZTCellCoverageRangeAnaSetForm
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(364, 205);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "ZTCellCoverageRangeAnaSetForm";
            this.Text = "小区覆盖带分析条件设置";
            this.Load += new System.EventHandler(this.ZTCellCoverageRangeAnaSetForm_Load);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numNbDiffServ)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.RadioButton rdbServAndNbCell;
        private System.Windows.Forms.RadioButton rdbServCell;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numNbDiffServ;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.RadioButton rdbSignalStrength;
        private System.Windows.Forms.RadioButton rdbOccupiedCell;
    }
}