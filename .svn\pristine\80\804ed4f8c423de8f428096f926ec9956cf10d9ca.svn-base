﻿using MasterCom.MTGis;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Runtime.Serialization;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    [Serializable()]
    public class MapCQTLayer : LayerBase
    {
        private Font fontBTSLabel = new Font(new FontFamily("宋体"), 9, FontStyle.Bold);
        private static Image cqtPoint;

        public bool drawCQTPoint { get; set; } = true;
        public bool drawCQTLabel { get; set; } = true;
        static MapCQTLayer()
        {
            initCQTImages();
        }
        private static void initCQTImages()
        {
            cqtPoint = Properties.Resources.cqtPlace;
        }

        public MapCQTLayer(string name)
            : base(name)
        {
            initCQTImages();
        }

        public bool CqtManagerInit { get; set; } = false;

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            updateRect.Inflate((int)(40 * 10000 / mapScale), (int)(40 * 10000 / mapScale));
            DbRect dRect;
            gisAdapter.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

            drawedBTSLabelRectangles.Clear();
            foreach (CQTAddressItem addr in MainModel.CurCQTPointList)
            {
                if (addr.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
                {
                    DbPoint dPoint = new DbPoint(addr.jd, addr.wd);
                    PointF point;
                    gisAdapter.ToDisplay(dPoint, out point);
                    drawCQTPointFunc(graphics, addr, point);
                    drawCQTLabelFunc(graphics, addr, point);
                }
            }

            float widthRatio = 1.2585F; //将图片宽度乘以该系数才能与事件准确对应
            drawImg(graphics, mainModel.CQTPlanImg, widthRatio);
            if (mainModel.CQTPlanImg != null)
            {
                mainModel.DTDataManager.CalculateDTDataCoordinates(gisAdapter, mapScale);
            }

            widthRatio = 1;
            drawImg(graphics, mainModel.CQTShowImg, widthRatio);
        }

        private void drawCQTPointFunc(Graphics graphics, CQTAddressItem addr, PointF point)
        {
            if (drawCQTPoint)
            {
                float radius = 3;
                RectangleF rect = new RectangleF(point.X - radius * 8, point.Y - radius * 16, radius * 16, radius * 16);
                graphics.DrawImage(cqtPoint, rect);
                if (MainModel.SelectedCQTAddrItem == addr)
                {
                    graphics.DrawRectangle(new Pen(Brushes.Red), rect.Left, rect.Top, rect.Width, rect.Height);
                }
            }
        }

        private void drawCQTLabelFunc(Graphics graphics, CQTAddressItem addr, PointF point)
        {
            if (drawCQTLabel)
            {
                graphics.TranslateTransform(point.X, point.Y);
                SizeF size = graphics.MeasureString(addr.name, fontBTSLabel);
                size.Height *= 0.8f;
                System.Drawing.Rectangle rectangle = new System.Drawing.Rectangle((int)point.X - 3, (int)(point.Y - (size.Height / 2 - 5)), (int)size.Width + 10, (int)size.Height + 10);
                bool needDraw = true;
                foreach (System.Drawing.Rectangle rectangleTemp in drawedBTSLabelRectangles)
                {
                    if (rectangle.Right >= rectangleTemp.Left && rectangle.Left <= rectangleTemp.Right
                        && rectangle.Bottom >= rectangleTemp.Top && rectangle.Top <= rectangleTemp.Bottom)
                    {
                        needDraw = false;
                        break;
                    }
                }
                if (needDraw)
                {
                    graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                    graphics.DrawString(addr.name, fontBTSLabel, Brushes.Black, 3, -size.Height / 2);
                    drawedBTSLabelRectangles.Add(rectangle);
                }
                graphics.ResetTransform();
            }
        }

        public void drawImg(Graphics graphics, Image img, float widthRatio)
        {
            if (img != null)
            {
                PointF point;
                gisAdapter.ToDisplay(mainModel.CQTPlanImgLTPos, out point);
                float ratio = (float)(mainModel.CQTPlanIMGScale / mapScale);
                RectangleF rect = new RectangleF(point.X, point.Y, widthRatio * ratio * img.Width, ratio * img.Height);
                PointF leftTop = new PointF(rect.Left, rect.Top);
                PointF rightTop = new PointF(rect.Right, rect.Top);
                PointF leftBottom = new PointF(rect.Left, rect.Bottom);
                PointF[] destPara = { leftTop, rightTop, leftBottom };
                RectangleF srcRect = new RectangleF(0.0f, 0.0f, img.Width, img.Height);
                float opaque = (float)(mainModel.CQTPlanImgOpaqueValue) / 255;
                float[][] colorArray = {
                                            new float[] {1, 0, 0, 0, 0},
                                            new float[] {0, 1, 0, 0, 0},
                                            new float[] {0, 0, 1, 0, 0},
                                            new float[] {0, 0, 0, opaque, 0}, //注意：此处为0.5f，图像为半透明
                                            new float[] {0, 0, 0, 0, 1}
                                        };
                System.Drawing.Imaging.ColorMatrix clrMatrix = new System.Drawing.Imaging.ColorMatrix(colorArray);
                System.Drawing.Imaging.ImageAttributes imgAttributes = new System.Drawing.Imaging.ImageAttributes();
                //设置图像的颜色属性
                imgAttributes.SetColorMatrix(clrMatrix, System.Drawing.Imaging.ColorMatrixFlag.Default, System.Drawing.Imaging.ColorAdjustType.Bitmap);

                Bitmap CQTPlanBmp = new Bitmap(img);
                graphics.DrawImage(CQTPlanBmp, destPara, srcRect, GraphicsUnit.Pixel, imgAttributes);
            }
        }

        public void Select(MapOperation2 mop2, List<CQTAddressItem> selectedCQTAddrs)
        {
            if (IsVisible && (drawCQTPoint || drawCQTLabel))
            {
                PointF selPoint;
                DbRect gbound = mop2.GetRegion().Bounds;
                gisAdapter.ToDisplay(new DbPoint(gbound.x1, gbound.y1), out selPoint);
                foreach (CQTAddressItem addr in MainModel.CurCQTPointList)
                {
                    DbPoint dPoint = new DbPoint(addr.jd, addr.wd);
                    PointF point;
                    gisAdapter.ToDisplay(dPoint, out point);
                    if (point.X < 0 || point.Y < 0)
                    {
                        continue;
                    }
                    float radius = 3;
                    RectangleF rect = new RectangleF(point.X - radius * 8, point.Y - radius * 16, radius * 16, radius * 16);
                    if (rect.X <= selPoint.X && rect.X + rect.Width >= selPoint.X && rect.Y <= selPoint.Y && rect.Y + rect.Height >= selPoint.Y)
                    {
                        selectedCQTAddrs.Add(addr);
                        return;//Select One Only
                    }
                }
            }
        }

        private List<System.Drawing.Rectangle> drawedBTSLabelRectangles = new List<System.Drawing.Rectangle>();

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["drawCQTPoint"] = drawCQTPoint;
                param["drawCQTLabel"] = drawCQTLabel;
                return param;
            }
            set
            {
                try
                {
                    drawCQTPoint = (bool)value["drawCQTPoint"];
                    drawCQTLabel = (bool)value["drawCQTLabel"];
                }
                catch
                {
                    //continue
                }
            }
        }
    }
}
