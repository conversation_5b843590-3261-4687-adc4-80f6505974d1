﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class FDDLittleStationSettingDlg_XJ
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.txtStandard1 = new System.Windows.Forms.TextBox();
            this.label52 = new System.Windows.Forms.Label();
            this.txtSystemInSwitch1 = new System.Windows.Forms.NumericUpDown();
            this.label55 = new System.Windows.Forms.Label();
            this.label48 = new System.Windows.Forms.Label();
            this.txtAntAvgSINR = new System.Windows.Forms.NumericUpDown();
            this.label49 = new System.Windows.Forms.Label();
            this.label50 = new System.Windows.Forms.Label();
            this.txtAntAvgRSRP = new System.Windows.Forms.NumericUpDown();
            this.label51 = new System.Windows.Forms.Label();
            this.label44 = new System.Windows.Forms.Label();
            this.txtDoorUpThroughput = new System.Windows.Forms.NumericUpDown();
            this.label45 = new System.Windows.Forms.Label();
            this.label46 = new System.Windows.Forms.Label();
            this.txtDoorDownThroughput = new System.Windows.Forms.NumericUpDown();
            this.label47 = new System.Windows.Forms.Label();
            this.label40 = new System.Windows.Forms.Label();
            this.txtDoorAvgSINR = new System.Windows.Forms.NumericUpDown();
            this.label41 = new System.Windows.Forms.Label();
            this.label42 = new System.Windows.Forms.Label();
            this.txtDoorAvgRSRP = new System.Windows.Forms.NumericUpDown();
            this.label43 = new System.Windows.Forms.Label();
            this.label36 = new System.Windows.Forms.Label();
            this.txtFTPUpSpeed1 = new System.Windows.Forms.NumericUpDown();
            this.label37 = new System.Windows.Forms.Label();
            this.label38 = new System.Windows.Forms.Label();
            this.txtFTPDownSpeed1 = new System.Windows.Forms.NumericUpDown();
            this.label39 = new System.Windows.Forms.Label();
            this.label32 = new System.Windows.Forms.Label();
            this.txtVolteSuccessRate1 = new System.Windows.Forms.NumericUpDown();
            this.label33 = new System.Windows.Forms.Label();
            this.label34 = new System.Windows.Forms.Label();
            this.txtVolteTestCount1 = new System.Windows.Forms.NumericUpDown();
            this.label35 = new System.Windows.Forms.Label();
            this.label26 = new System.Windows.Forms.Label();
            this.txtAccessTestCount1 = new System.Windows.Forms.NumericUpDown();
            this.label28 = new System.Windows.Forms.Label();
            this.label30 = new System.Windows.Forms.Label();
            this.txtAccessSuccessRate1 = new System.Windows.Forms.NumericUpDown();
            this.label31 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.txtSystemInSwitch1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAntAvgSINR)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAntAvgRSRP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDoorUpThroughput)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDoorDownThroughput)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDoorAvgSINR)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDoorAvgRSRP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFTPUpSpeed1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFTPDownSpeed1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVolteSuccessRate1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVolteTestCount1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessTestCount1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessSuccessRate1)).BeginInit();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(607, 456);
            this.btnOK.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(107, 36);
            this.btnOK.TabIndex = 5;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(742, 456);
            this.btnCancel.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(107, 36);
            this.btnCancel.TabIndex = 6;
            this.btnCancel.Text = "取消";
            // 
            // txtStandard1
            // 
            this.txtStandard1.Location = new System.Drawing.Point(693, 389);
            this.txtStandard1.Name = "txtStandard1";
            this.txtStandard1.Size = new System.Drawing.Size(113, 28);
            this.txtStandard1.TabIndex = 242;
            // 
            // label52
            // 
            this.label52.AutoSize = true;
            this.label52.Location = new System.Drawing.Point(552, 394);
            this.label52.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label52.Name = "label52";
            this.label52.Size = new System.Drawing.Size(134, 18);
            this.label52.TabIndex = 241;
            this.label52.Text = "室分泄露标准：";
            // 
            // txtSystemInSwitch1
            // 
            this.txtSystemInSwitch1.Location = new System.Drawing.Point(235, 387);
            this.txtSystemInSwitch1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtSystemInSwitch1.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtSystemInSwitch1.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtSystemInSwitch1.Name = "txtSystemInSwitch1";
            this.txtSystemInSwitch1.Size = new System.Drawing.Size(114, 28);
            this.txtSystemInSwitch1.TabIndex = 240;
            this.txtSystemInSwitch1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtSystemInSwitch1.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label55
            // 
            this.label55.AutoSize = true;
            this.label55.Location = new System.Drawing.Point(111, 392);
            this.label55.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label55.Name = "label55";
            this.label55.Size = new System.Drawing.Size(116, 18);
            this.label55.TabIndex = 239;
            this.label55.Text = "系统内切换≥";
            // 
            // label48
            // 
            this.label48.AutoSize = true;
            this.label48.Location = new System.Drawing.Point(814, 330);
            this.label48.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label48.Name = "label48";
            this.label48.Size = new System.Drawing.Size(26, 18);
            this.label48.TabIndex = 238;
            this.label48.Text = "dB";
            // 
            // txtAntAvgSINR
            // 
            this.txtAntAvgSINR.Location = new System.Drawing.Point(693, 325);
            this.txtAntAvgSINR.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtAntAvgSINR.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtAntAvgSINR.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtAntAvgSINR.Name = "txtAntAvgSINR";
            this.txtAntAvgSINR.Size = new System.Drawing.Size(114, 28);
            this.txtAntAvgSINR.TabIndex = 237;
            this.txtAntAvgSINR.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtAntAvgSINR.Value = new decimal(new int[] {
            75,
            0,
            0,
            65536});
            // 
            // label49
            // 
            this.label49.AutoSize = true;
            this.label49.Location = new System.Drawing.Point(551, 332);
            this.label49.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label49.Name = "label49";
            this.label49.Size = new System.Drawing.Size(134, 18);
            this.label49.TabIndex = 236;
            this.label49.Text = "天线Avg SINR≥";
            // 
            // label50
            // 
            this.label50.AutoSize = true;
            this.label50.Location = new System.Drawing.Point(355, 329);
            this.label50.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label50.Name = "label50";
            this.label50.Size = new System.Drawing.Size(35, 18);
            this.label50.TabIndex = 235;
            this.label50.Text = "dBm";
            // 
            // txtAntAvgRSRP
            // 
            this.txtAntAvgRSRP.Location = new System.Drawing.Point(234, 324);
            this.txtAntAvgRSRP.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtAntAvgRSRP.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtAntAvgRSRP.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtAntAvgRSRP.Name = "txtAntAvgRSRP";
            this.txtAntAvgRSRP.Size = new System.Drawing.Size(114, 28);
            this.txtAntAvgRSRP.TabIndex = 234;
            this.txtAntAvgRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtAntAvgRSRP.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label51
            // 
            this.label51.AutoSize = true;
            this.label51.Location = new System.Drawing.Point(93, 329);
            this.label51.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label51.Name = "label51";
            this.label51.Size = new System.Drawing.Size(134, 18);
            this.label51.TabIndex = 233;
            this.label51.Text = "天线Avg RSRP≥";
            // 
            // label44
            // 
            this.label44.AutoSize = true;
            this.label44.Location = new System.Drawing.Point(815, 271);
            this.label44.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label44.Name = "label44";
            this.label44.Size = new System.Drawing.Size(17, 18);
            this.label44.TabIndex = 232;
            this.label44.Text = "M";
            // 
            // txtDoorUpThroughput
            // 
            this.txtDoorUpThroughput.Location = new System.Drawing.Point(693, 266);
            this.txtDoorUpThroughput.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtDoorUpThroughput.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtDoorUpThroughput.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtDoorUpThroughput.Name = "txtDoorUpThroughput";
            this.txtDoorUpThroughput.Size = new System.Drawing.Size(114, 28);
            this.txtDoorUpThroughput.TabIndex = 231;
            this.txtDoorUpThroughput.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtDoorUpThroughput.Value = new decimal(new int[] {
            75,
            0,
            0,
            65536});
            // 
            // label45
            // 
            this.label45.AutoSize = true;
            this.label45.Location = new System.Drawing.Point(534, 273);
            this.label45.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label45.Name = "label45";
            this.label45.Size = new System.Drawing.Size(152, 18);
            this.label45.TabIndex = 230;
            this.label45.Text = "室内上行吞吐率≥";
            // 
            // label46
            // 
            this.label46.AutoSize = true;
            this.label46.Location = new System.Drawing.Point(358, 269);
            this.label46.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label46.Name = "label46";
            this.label46.Size = new System.Drawing.Size(17, 18);
            this.label46.TabIndex = 229;
            this.label46.Text = "M";
            // 
            // txtDoorDownThroughput
            // 
            this.txtDoorDownThroughput.Location = new System.Drawing.Point(234, 265);
            this.txtDoorDownThroughput.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtDoorDownThroughput.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtDoorDownThroughput.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtDoorDownThroughput.Name = "txtDoorDownThroughput";
            this.txtDoorDownThroughput.Size = new System.Drawing.Size(114, 28);
            this.txtDoorDownThroughput.TabIndex = 228;
            this.txtDoorDownThroughput.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtDoorDownThroughput.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label47
            // 
            this.label47.AutoSize = true;
            this.label47.Location = new System.Drawing.Point(75, 270);
            this.label47.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label47.Name = "label47";
            this.label47.Size = new System.Drawing.Size(152, 18);
            this.label47.TabIndex = 227;
            this.label47.Text = "室内下行吞吐率≥";
            // 
            // label40
            // 
            this.label40.AutoSize = true;
            this.label40.Location = new System.Drawing.Point(815, 216);
            this.label40.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(17, 18);
            this.label40.TabIndex = 226;
            this.label40.Text = "%";
            // 
            // txtDoorAvgSINR
            // 
            this.txtDoorAvgSINR.Location = new System.Drawing.Point(693, 211);
            this.txtDoorAvgSINR.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtDoorAvgSINR.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtDoorAvgSINR.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtDoorAvgSINR.Name = "txtDoorAvgSINR";
            this.txtDoorAvgSINR.Size = new System.Drawing.Size(114, 28);
            this.txtDoorAvgSINR.TabIndex = 225;
            this.txtDoorAvgSINR.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtDoorAvgSINR.Value = new decimal(new int[] {
            75,
            0,
            0,
            65536});
            // 
            // label41
            // 
            this.label41.AutoSize = true;
            this.label41.Location = new System.Drawing.Point(551, 216);
            this.label41.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label41.Name = "label41";
            this.label41.Size = new System.Drawing.Size(134, 18);
            this.label41.TabIndex = 224;
            this.label41.Text = "室内Avg SINR≥";
            // 
            // label42
            // 
            this.label42.AutoSize = true;
            this.label42.Location = new System.Drawing.Point(354, 215);
            this.label42.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label42.Name = "label42";
            this.label42.Size = new System.Drawing.Size(26, 18);
            this.label42.TabIndex = 223;
            this.label42.Text = "次";
            // 
            // txtDoorAvgRSRP
            // 
            this.txtDoorAvgRSRP.Location = new System.Drawing.Point(234, 210);
            this.txtDoorAvgRSRP.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtDoorAvgRSRP.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtDoorAvgRSRP.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtDoorAvgRSRP.Name = "txtDoorAvgRSRP";
            this.txtDoorAvgRSRP.Size = new System.Drawing.Size(114, 28);
            this.txtDoorAvgRSRP.TabIndex = 222;
            this.txtDoorAvgRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtDoorAvgRSRP.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label43
            // 
            this.label43.AutoSize = true;
            this.label43.Location = new System.Drawing.Point(93, 215);
            this.label43.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label43.Name = "label43";
            this.label43.Size = new System.Drawing.Size(134, 18);
            this.label43.TabIndex = 221;
            this.label43.Text = "室内Avg RSRP≥";
            // 
            // label36
            // 
            this.label36.AutoSize = true;
            this.label36.Location = new System.Drawing.Point(815, 160);
            this.label36.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(17, 18);
            this.label36.TabIndex = 220;
            this.label36.Text = "M";
            // 
            // txtFTPUpSpeed1
            // 
            this.txtFTPUpSpeed1.Location = new System.Drawing.Point(693, 155);
            this.txtFTPUpSpeed1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtFTPUpSpeed1.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtFTPUpSpeed1.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtFTPUpSpeed1.Name = "txtFTPUpSpeed1";
            this.txtFTPUpSpeed1.Size = new System.Drawing.Size(114, 28);
            this.txtFTPUpSpeed1.TabIndex = 219;
            this.txtFTPUpSpeed1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtFTPUpSpeed1.Value = new decimal(new int[] {
            75,
            0,
            0,
            65536});
            // 
            // label37
            // 
            this.label37.AutoSize = true;
            this.label37.Location = new System.Drawing.Point(558, 162);
            this.label37.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label37.Name = "label37";
            this.label37.Size = new System.Drawing.Size(125, 18);
            this.label37.TabIndex = 218;
            this.label37.Text = "FTP上传速率≥";
            // 
            // label38
            // 
            this.label38.AutoSize = true;
            this.label38.Location = new System.Drawing.Point(358, 158);
            this.label38.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label38.Name = "label38";
            this.label38.Size = new System.Drawing.Size(17, 18);
            this.label38.TabIndex = 217;
            this.label38.Text = "M";
            // 
            // txtFTPDownSpeed1
            // 
            this.txtFTPDownSpeed1.Location = new System.Drawing.Point(234, 154);
            this.txtFTPDownSpeed1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtFTPDownSpeed1.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtFTPDownSpeed1.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.txtFTPDownSpeed1.Name = "txtFTPDownSpeed1";
            this.txtFTPDownSpeed1.Size = new System.Drawing.Size(114, 28);
            this.txtFTPDownSpeed1.TabIndex = 216;
            this.txtFTPDownSpeed1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtFTPDownSpeed1.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label39
            // 
            this.label39.AutoSize = true;
            this.label39.Location = new System.Drawing.Point(102, 159);
            this.label39.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label39.Name = "label39";
            this.label39.Size = new System.Drawing.Size(125, 18);
            this.label39.TabIndex = 215;
            this.label39.Text = "FTP下载速率≥";
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Location = new System.Drawing.Point(813, 102);
            this.label32.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(17, 18);
            this.label32.TabIndex = 214;
            this.label32.Text = "%";
            // 
            // txtVolteSuccessRate1
            // 
            this.txtVolteSuccessRate1.Location = new System.Drawing.Point(692, 97);
            this.txtVolteSuccessRate1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtVolteSuccessRate1.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtVolteSuccessRate1.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtVolteSuccessRate1.Name = "txtVolteSuccessRate1";
            this.txtVolteSuccessRate1.Size = new System.Drawing.Size(114, 28);
            this.txtVolteSuccessRate1.TabIndex = 213;
            this.txtVolteSuccessRate1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtVolteSuccessRate1.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Location = new System.Drawing.Point(559, 101);
            this.label33.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(125, 18);
            this.label33.TabIndex = 212;
            this.label33.Text = "VOLTE成功率≥";
            // 
            // label34
            // 
            this.label34.AutoSize = true;
            this.label34.Location = new System.Drawing.Point(356, 101);
            this.label34.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label34.Name = "label34";
            this.label34.Size = new System.Drawing.Size(26, 18);
            this.label34.TabIndex = 211;
            this.label34.Text = "次";
            // 
            // txtVolteTestCount1
            // 
            this.txtVolteTestCount1.Location = new System.Drawing.Point(234, 96);
            this.txtVolteTestCount1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtVolteTestCount1.Maximum = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.txtVolteTestCount1.Name = "txtVolteTestCount1";
            this.txtVolteTestCount1.Size = new System.Drawing.Size(114, 28);
            this.txtVolteTestCount1.TabIndex = 210;
            this.txtVolteTestCount1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtVolteTestCount1.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label35
            // 
            this.label35.AutoSize = true;
            this.label35.Location = new System.Drawing.Point(84, 101);
            this.label35.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label35.Name = "label35";
            this.label35.Size = new System.Drawing.Size(143, 18);
            this.label35.TabIndex = 209;
            this.label35.Text = "VOLTE测试次数≥";
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(813, 45);
            this.label26.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(26, 18);
            this.label26.TabIndex = 208;
            this.label26.Text = "次";
            // 
            // txtAccessTestCount1
            // 
            this.txtAccessTestCount1.Location = new System.Drawing.Point(692, 40);
            this.txtAccessTestCount1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtAccessTestCount1.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.txtAccessTestCount1.Name = "txtAccessTestCount1";
            this.txtAccessTestCount1.Size = new System.Drawing.Size(114, 28);
            this.txtAccessTestCount1.TabIndex = 207;
            this.txtAccessTestCount1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtAccessTestCount1.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(551, 46);
            this.label28.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(134, 18);
            this.label28.TabIndex = 206;
            this.label28.Text = "接入测试次数≥";
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(357, 42);
            this.label30.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(17, 18);
            this.label30.TabIndex = 205;
            this.label30.Text = "%";
            // 
            // txtAccessSuccessRate1
            // 
            this.txtAccessSuccessRate1.Location = new System.Drawing.Point(235, 36);
            this.txtAccessSuccessRate1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtAccessSuccessRate1.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.txtAccessSuccessRate1.Minimum = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.txtAccessSuccessRate1.Name = "txtAccessSuccessRate1";
            this.txtAccessSuccessRate1.Size = new System.Drawing.Size(114, 28);
            this.txtAccessSuccessRate1.TabIndex = 204;
            this.txtAccessSuccessRate1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.txtAccessSuccessRate1.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Location = new System.Drawing.Point(30, 43);
            this.label31.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(197, 18);
            this.label31.TabIndex = 203;
            this.label31.Text = "Access Success Rate≥";
            // 
            // FDDLittleStationSettingDlg_XJ
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(10F, 22F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(890, 527);
            this.Controls.Add(this.txtStandard1);
            this.Controls.Add(this.label52);
            this.Controls.Add(this.txtSystemInSwitch1);
            this.Controls.Add(this.label55);
            this.Controls.Add(this.label48);
            this.Controls.Add(this.txtAntAvgSINR);
            this.Controls.Add(this.label49);
            this.Controls.Add(this.label50);
            this.Controls.Add(this.txtAntAvgRSRP);
            this.Controls.Add(this.label51);
            this.Controls.Add(this.label44);
            this.Controls.Add(this.txtDoorUpThroughput);
            this.Controls.Add(this.label45);
            this.Controls.Add(this.label46);
            this.Controls.Add(this.txtDoorDownThroughput);
            this.Controls.Add(this.label47);
            this.Controls.Add(this.label40);
            this.Controls.Add(this.txtDoorAvgSINR);
            this.Controls.Add(this.label41);
            this.Controls.Add(this.label42);
            this.Controls.Add(this.txtDoorAvgRSRP);
            this.Controls.Add(this.label43);
            this.Controls.Add(this.label36);
            this.Controls.Add(this.txtFTPUpSpeed1);
            this.Controls.Add(this.label37);
            this.Controls.Add(this.label38);
            this.Controls.Add(this.txtFTPDownSpeed1);
            this.Controls.Add(this.label39);
            this.Controls.Add(this.label32);
            this.Controls.Add(this.txtVolteSuccessRate1);
            this.Controls.Add(this.label33);
            this.Controls.Add(this.label34);
            this.Controls.Add(this.txtVolteTestCount1);
            this.Controls.Add(this.label35);
            this.Controls.Add(this.label26);
            this.Controls.Add(this.txtAccessTestCount1);
            this.Controls.Add(this.label28);
            this.Controls.Add(this.label30);
            this.Controls.Add(this.txtAccessSuccessRate1);
            this.Controls.Add(this.label31);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Margin = new System.Windows.Forms.Padding(10, 14, 10, 14);
            this.Name = "FDDLittleStationSettingDlg_XJ";
            this.Text = "FDD小站单验门限设置";
            ((System.ComponentModel.ISupportInitialize)(this.txtSystemInSwitch1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAntAvgSINR)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAntAvgRSRP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDoorUpThroughput)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDoorDownThroughput)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDoorAvgSINR)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDoorAvgRSRP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFTPUpSpeed1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFTPDownSpeed1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVolteSuccessRate1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVolteTestCount1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessTestCount1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAccessSuccessRate1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.TextBox txtStandard1;
        private System.Windows.Forms.Label label52;
        private System.Windows.Forms.NumericUpDown txtSystemInSwitch1;
        private System.Windows.Forms.Label label55;
        private System.Windows.Forms.Label label48;
        private System.Windows.Forms.NumericUpDown txtAntAvgSINR;
        private System.Windows.Forms.Label label49;
        private System.Windows.Forms.Label label50;
        private System.Windows.Forms.NumericUpDown txtAntAvgRSRP;
        private System.Windows.Forms.Label label51;
        private System.Windows.Forms.Label label44;
        private System.Windows.Forms.NumericUpDown txtDoorUpThroughput;
        private System.Windows.Forms.Label label45;
        private System.Windows.Forms.Label label46;
        private System.Windows.Forms.NumericUpDown txtDoorDownThroughput;
        private System.Windows.Forms.Label label47;
        private System.Windows.Forms.Label label40;
        private System.Windows.Forms.NumericUpDown txtDoorAvgSINR;
        private System.Windows.Forms.Label label41;
        private System.Windows.Forms.Label label42;
        private System.Windows.Forms.NumericUpDown txtDoorAvgRSRP;
        private System.Windows.Forms.Label label43;
        private System.Windows.Forms.Label label36;
        private System.Windows.Forms.NumericUpDown txtFTPUpSpeed1;
        private System.Windows.Forms.Label label37;
        private System.Windows.Forms.Label label38;
        private System.Windows.Forms.NumericUpDown txtFTPDownSpeed1;
        private System.Windows.Forms.Label label39;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.NumericUpDown txtVolteSuccessRate1;
        private System.Windows.Forms.Label label33;
        private System.Windows.Forms.Label label34;
        private System.Windows.Forms.NumericUpDown txtVolteTestCount1;
        private System.Windows.Forms.Label label35;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.NumericUpDown txtAccessTestCount1;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.NumericUpDown txtAccessSuccessRate1;
        private System.Windows.Forms.Label label31;
    }
}