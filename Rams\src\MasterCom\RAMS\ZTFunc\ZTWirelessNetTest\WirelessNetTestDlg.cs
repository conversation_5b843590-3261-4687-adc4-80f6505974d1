﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTWirelessNetTest
{
    public partial class WirelessNetTestDlg : BaseDialog
    {
        //Dictionary<string, Dictionary<string, NRDominantAreaSceneInfo>> sceneInfoDic;
        //Dictionary<string, Dictionary<string, NRDominantAreaCellInfo>> sceneCellDic;

        public WirelessNetTestDlg()
        {
            InitializeComponent();

            chkLstDistrict.Items.Clear();
            foreach (IDNamePair item in DistrictManager.GetInstance().GetAvailableDistrict())
            {
                if (mainModel.User.DBID == -1)
                {
                    chkLstDistrict.Items.Add(item, false);
                }
                else if (item.id == mainModel.User.DBID)
                {
                    chkLstDistrict.Items.Add(item, false);
                    break;
                }
            }
        }

        WirelessNetTestCond curCond;
        Dictionary<WirelessNetTestProjectType, WirelessNetTestCond.Project> curProjectDic;

        #region 设置条件
        public void SetCondition(WirelessNetTestCond cond)
        {
            this.curCond = cond;
            curProjectDic = new Dictionary<WirelessNetTestProjectType, WirelessNetTestCond.Project>();
            foreach (var item in cond.ProjectDic.Values)
            {
                curProjectDic[item.Type] = new WirelessNetTestCond.Project(item.Type, item.ProjectList);
            }

            //根据设置的路径,加载对应配置文件中的详细信息
            WaitBox.Show("正在加载配置信息...", loadConfigFile);

            txtCityProjects.Text = cond.ProjectDic[WirelessNetTestProjectType.城区].ProjectDesc;
            txtCountyProjects.Text = cond.ProjectDic[WirelessNetTestProjectType.区县].ProjectDesc;
            txtRailwayProjects.Text = cond.ProjectDic[WirelessNetTestProjectType.高铁].ProjectDesc;
            txtSubwayProjects.Text = cond.ProjectDic[WirelessNetTestProjectType.地铁].ProjectDesc;
            txtSpeedwayProjects.Text = cond.ProjectDic[WirelessNetTestProjectType.高速].ProjectDesc;
            txtAirportProjects.Text = cond.ProjectDic[WirelessNetTestProjectType.机场].ProjectDesc;

            dPSTime.Value = cond.TestPeriod.BeginTime;
            dPETime.Value = cond.TestPeriod.EndTime;
            chkIsStatCU.Checked = cond.IsStatCU;

            for (int i = 0; i < chkLstDistrict.Items.Count; i++)
            {
                IDNamePair p = chkLstDistrict.Items[i] as IDNamePair;
                chkLstDistrict.SetItemChecked(i, cond.DistrictIDs.Contains(p.id));
            }
        }

        private void loadConfigFile()
        {
            try
            {
                txtPath.Text = "";
                WirelessNetTestSceneCell.Instance.ReadExcel(curCond.SceneExcelPath);
                if (WirelessNetTestSceneCell.Instance.DistrictDic.Count > 0)
                {
                    txtPath.Text = curCond.SceneExcelPath;
                }
                if (!string.IsNullOrEmpty(curCond.ShapePath) && Directory.Exists(curCond.ShapePath))
                {
                    txtShpPath.Text = curCond.ShapePath;
                }
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + e.StackTrace);
            }
            finally
            {
                System.Threading.Thread.Sleep(200);
                WaitBox.Close();
            }
        }
        #endregion

        #region 获取条件
        public WirelessNetTestCond GetCondition()
        {
            //WirelessNetTestCond cond = new WirelessNetTestCond();
            //cond.SetCondition(dPSTime.Value, dPETime.Value);
            //foreach (IDNamePair chkItem in chkLstDistrict.CheckedItems)
            //{
            //    cond.DistrictIDs.Add(chkItem.id);
            //}

            ////cond.DistrictConfigDic = getDistrictConfigDic();

            //cond.SceneExcelPath = txtPath.Text;
            //cond.ShapePath = txtShpPath.Text;

            return curCond;
        }
        #endregion

        #region 选择项目类型
        private void btnCity_Click(object sender, EventArgs e)
        {
            txtCityProjects.Text = setProject(WirelessNetTestProjectType.城区);
        }

        private void btnCounty_Click(object sender, EventArgs e)
        {
            txtCountyProjects.Text = setProject(WirelessNetTestProjectType.区县);
        }

        private void btnRailway_Click(object sender, EventArgs e)
        {
            txtRailwayProjects.Text = setProject(WirelessNetTestProjectType.高铁);
        }

        private void btnSubway_Click(object sender, EventArgs e)
        {
            txtSubwayProjects.Text = setProject(WirelessNetTestProjectType.地铁);
        }

        private void btnSpeedway_Click(object sender, EventArgs e)
        {
            txtSpeedwayProjects.Text = setProject(WirelessNetTestProjectType.高速);
        }

        private void btnAirport_Click(object sender, EventArgs e)
        {
            txtAirportProjects.Text = setProject(WirelessNetTestProjectType.机场);
        }

        private string setProject(WirelessNetTestProjectType type)
        {
            var projects = curProjectDic[type];
            bool isValid = chooseProjects(projects.ProjectList, out var newProjects);
            if (isValid)
            {
                curProjectDic[type] = new WirelessNetTestCond.Project(type, newProjects);
                //curProjectDic[type].GetProjectsDesc();
                return curProjectDic[type].ProjectDesc;
            }
            return projects.ProjectDesc;
        }

        private bool chooseProjects(List<int> projects, out List<int> newProjects)
        {
            WirelessNetTestProjects dlg = new WirelessNetTestProjects(projects);
            dlg.ShowDialog();
            if (dlg.DialogResult == DialogResult.OK)
            {
                newProjects = dlg.Projects;
                return true;
            }
            newProjects = projects;
            return false;
        }
        #endregion

        private void btnShp_Click(object sender, EventArgs e)
        {
            //仅加载图层路径
            FolderBrowserDialog folderDlg = new FolderBrowserDialog();
            if (folderDlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            txtShpPath.Text = folderDlg.SelectedPath;
        }

        #region 加载用户选择的场景小区信息
        private void btnSearchPath_Click(object sender, EventArgs e)
        {
            //选择Excel路径
            OpenFileDialog openDlg = new OpenFileDialog();
            openDlg.Filter = "Excel|*.xlsx;*.xls";
            openDlg.Title = "请选择场景类型关系表";
            if (openDlg.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    WirelessNetTestSceneCell.Instance.ReadExcel(openDlg.FileName);
                    if (WirelessNetTestSceneCell.Instance.DistrictDic.Count > 0)
                    {
                        txtPath.Text = openDlg.FileName;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }
            }
        }

        #region 下载模板
        private void btnDownLoad_Click(object sender, EventArgs e)
        {
            List<ExportToExcelModel> lsData = new List<ExportToExcelModel>();
            initSenceModel(lsData);

            initNrCellModel(lsData);

            initLteCellModel(lsData);

            ExcelNPOIManager.ExportToExcelMore(lsData);
        }

        private static void initSenceModel(List<ExportToExcelModel> lsData)
        {
            ExportToExcelModel sheet = new ExportToExcelModel();
            sheet.SheetName = "场景";

            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow nr = new NPOIRow();
            nr.AddCellValue("地市");
            nr.AddCellValue("场景名称");
            nr.AddCellValue("子场景名称");
            nr.AddCellValue("类别");
            rows.Add(nr);

            nr = new NPOIRow();
            nr.AddCellValue("成都市");
            nr.AddCellValue("双流机场");
            nr.AddCellValue("双流机场_T1航站楼");
            nr.AddCellValue("机场");
            rows.Add(nr);

            nr = new NPOIRow();
            nr.AddCellValue("成都市");
            nr.AddCellValue("双流机场");
            nr.AddCellValue("");
            nr.AddCellValue("机场");
            rows.Add(nr);

            sheet.Data = rows;
            lsData.Add(sheet);
        }

        private static void initNrCellModel(List<ExportToExcelModel> lsData)
        {
            ExportToExcelModel sheet = new ExportToExcelModel();
            sheet.SheetName = "NR主服小区";

            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow nr = new NPOIRow();
            nr.AddCellValue("地市");
            nr.AddCellValue("场景名称");
            nr.AddCellValue("子场景名称");
            nr.AddCellValue("基站标识");
            nr.AddCellValue("CI");
            nr.AddCellValue("备注");
            rows.Add(nr);

            nr = new NPOIRow();
            nr.AddCellValue("成都市");
            nr.AddCellValue("双流机场");
            nr.AddCellValue("双流机场_T1航站楼");
            nr.AddCellValue("9431326");
            nr.AddCellValue("38630711298");
            nr.AddCellValue("机场");
            rows.Add(nr);

            nr = new NPOIRow();
            nr.AddCellValue("成都市");
            nr.AddCellValue("双流机场");
            nr.AddCellValue("");
            nr.AddCellValue("9431327");
            nr.AddCellValue("38630711299");
            nr.AddCellValue("机场");
            rows.Add(nr);

            sheet.Data = rows;
            lsData.Add(sheet);
        }

        private static void initLteCellModel(List<ExportToExcelModel> lsData)
        {
            ExportToExcelModel sheet = new ExportToExcelModel();
            sheet.SheetName = "LTE主服小区";

            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow nr = new NPOIRow();
            nr.AddCellValue("地市");
            nr.AddCellValue("场景名称");
            nr.AddCellValue("子场景名称");
            nr.AddCellValue("基站标识");
            nr.AddCellValue("CI");
            nr.AddCellValue("备注");
            rows.Add(nr);

            nr = new NPOIRow();
            nr.AddCellValue("成都市");
            nr.AddCellValue("双流机场");
            nr.AddCellValue("双流机场_T1航站楼");
            nr.AddCellValue("533648");
            nr.AddCellValue("136613901");
            nr.AddCellValue("机场");
            rows.Add(nr);

            nr = new NPOIRow();
            nr.AddCellValue("成都市");
            nr.AddCellValue("双流机场");
            nr.AddCellValue("");
            nr.AddCellValue("533650");
            nr.AddCellValue("136614401");
            nr.AddCellValue("机场");
            rows.Add(nr);

            sheet.Data = rows;
            lsData.Add(sheet);
        }
        #endregion
        #endregion

        private void chkAllDistrict_CheckedChanged(object sender, EventArgs e)
        {
            for (int i = 0; i < chkLstDistrict.Items.Count; i++)
            {
                chkLstDistrict.SetItemChecked(i, chkAllDistrict.Checked);
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            string errMsg = JudgeValid();
            if (!string.IsNullOrEmpty(errMsg))
            {
                MessageBox.Show(errMsg);
                return;
            }

            curCond.SceneExcelPath = txtPath.Text;
            curCond.ShapePath = txtShpPath.Text;
            curCond.TestPeriod.SetPeriod(dPSTime.Value, dPETime.Value);

            foreach (var project in curProjectDic)
            {
                curCond.ProjectDic[project.Key] = project.Value;
            }

            curCond.DistrictIDs.Clear();
            foreach (IDNamePair chkItem in chkLstDistrict.CheckedItems)
            {
                curCond.DistrictIDs.Add(chkItem.id);
            }

            curCond.SetCarrier(chkIsStatCU.Checked);

            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        public string JudgeValid()
        {
            if (string.IsNullOrEmpty(txtPath.Text))
            {
                return "场景小区Excel文件路径未设置";
            }
            if (string.IsNullOrEmpty(txtShpPath.Text))
            {
                return "图层路径未设置";
            }
            foreach (var project in curProjectDic)
            {
                if (project.Value.ProjectList.Count == 0)
                {
                    return $"{project.Key}项目类型未设置";
                }
            }
            if (chkLstDistrict.CheckedItems.Count == 0)
            {
                return "至少选择一个地市";
            }
            if (dPSTime.Value > dPETime.Value)
            {
                return "起始时间不能大于结束时间";
            }
            return "";
        }

        private void chkIsStatCU_CheckStateChanged(object sender, EventArgs e)
        {
            curCond.SetCarrier(chkIsStatCU.Checked);
        }
    }
}
