﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public class LogDownloadInfoQuery : DIYSQLBase
    {
        public LogDownloadInfoQuery(MainModel mainModel) : base(mainModel)
        {
            MainDB = true;
        }

        public override string Name
        {
            get { return "文件下载记录"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18028, this.Name);
        }

        public override bool IsNeedSetQueryCondition
        {
            get { return false; }
        }

        protected override bool isValidCondition()
        {
            if (MainModel.User.DBID != -1)
            {
                MessageBox.Show("该查询功能需要管理员权限", "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return false;
            }
            if (setForm == null)
            {
                setForm = new LogDownloadSettingForm();
            }
            if (setForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            queryPeriod = setForm.GetCondition();
            downloadList = new List<LogDownloadInfo>();
            return true;
        }

        protected override void query()
        {
            WaitTextBox.Show("正在查询...", DoQueryInWaitBox);

            for (int i = 0; i < downloadList.Count; ++i)
            {
                if (!queryPeriod.Contains(downloadList[i].DownloadTime))
                {
                    downloadList.RemoveAt(i);
                    --i;
                }
            }

            LogDownloadInfoForm form = MainModel.CreateResultForm(typeof(LogDownloadInfoForm)) as LogDownloadInfoForm;
            form.Fill(downloadList);
            form.Visible = true;
            downloadList = null;
        }

        private void DoQueryInWaitBox()
        {
            base.query();
        }

        protected override string getSqlTextString()
        {
            return string.Format("exec sp_get_log_download_info '{0}','{1}'"
                , this.queryPeriod.BeginTime.ToString("yyyy-MM-dd")
                , this.queryPeriod.EndTime.ToString("yyyy-MM-dd"));
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            List<E_VType> retTypes = new List<E_VType>();
            retTypes.Add(E_VType.E_String);
            retTypes.Add(E_VType.E_String);
            retTypes.Add(E_VType.E_String);
            retTypes.Add(E_VType.E_String);
            retTypes.Add(E_VType.E_String);
            return retTypes.ToArray();
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            try
            {
                Package package = clientProxy.Package;
                while (true)
                {
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        LogDownloadInfo dInfo = new LogDownloadInfo();
                        dInfo.Fill(package.Content);
                        downloadList.Add(dInfo);
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                    {
                        //
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString() + ex.StackTrace);
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        protected List<LogDownloadInfo> downloadList = new List<LogDownloadInfo>();
        protected LogDownloadSettingForm setForm;
        protected TimePeriod queryPeriod;
    }

    public class LogDownloadInfo
    {
        public string UserName
        {
            get;
            set;
        }

        public string UserIP
        {
            get;
            set;
        }

        public DateTime DownloadTime
        {
            get;
            set;
        }

        public string DbName
        {
            get;
            set;
        }

        public string FileName
        {
            get;
            set;
        }

        public DateTime StartTime
        {
            get;
            set;
        }

        public string ProjectName
        {
            get;
            set;
        }

        public string ServiceName
        {
            get;
            set;
        }

        public void Fill(Content content)
        {
            try
            {
                UserName = content.GetParamString();
                UserIP = content.GetParamString();
                DownloadTime = JavaDate.GetDateTimeFromMilliseconds(content.GetParamInt() * 1000L);
                FileName = content.GetParamString();
                ProjectName = content.GetParamString();

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString() + ex.StackTrace);
            }
        }
    }
}
