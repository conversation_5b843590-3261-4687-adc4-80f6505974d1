﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using DevExpress.XtraGrid.Views.Grid;

namespace MasterCom.RAMS.NewBlackBlock
{
    public partial class FusionDataForm : MinCloseForm
    {
        public FusionDataForm()
            : base()
        {
            InitializeComponent();
        }


        internal void FillData(List<CellAlarmData> alarmDataSet
            , List<CellPerfData> perfDataSet, List<CellArgData> argDataSet)
        {
            gridCtrlAlarm.DataSource = alarmDataSet;
            gridCtrlAlarm.RefreshDataSource();
            gvAlarm.BestFitColumns();

            gridCtrlPerf.DataSource = perfDataSet;
            gridCtrlPerf.RefreshDataSource();
            gvPerf.BestFitColumns();

            gridCtrlArg.DataSource = argDataSet;
            gridCtrlArg.RefreshDataSource();
            gvArg.BestFitColumns();
        }

        private void gvAlarm_CustomDrawEmptyForeground(object sender, DevExpress.XtraGrid.Views.Base.CustomDrawEventArgs e)
        {
            DevGridControlManager.DrawNoRowCountMessage(gvAlarm, e, "所回放数据主服，无告警信息");
        }

        private void gvPerf_CustomDrawEmptyForeground(object sender, DevExpress.XtraGrid.Views.Base.CustomDrawEventArgs e)
        {
            DevGridControlManager.DrawNoRowCountMessage(gvPerf, e, "所回放数据主服，无性能信息");
        }

        private void gvArg_CustomDrawEmptyForeground(object sender, DevExpress.XtraGrid.Views.Base.CustomDrawEventArgs e)
        {
            DevGridControlManager.DrawNoRowCountMessage(gvArg, e, "所回放数据主服，无参数信息");
        }

        private void ToolStripMenuItem_ExportAll_Click(object sender, EventArgs e)
        {
            List<GridView> gvs = new List<GridView>();
            List<string> sheetNames = new List<string>();
            foreach (TabPage tp in tabCtrl.TabPages)
            {
                DevExpress.XtraGrid.GridControl gc = tp.Controls[0] as DevExpress.XtraGrid.GridControl;
                GridView gv = gc.MainView as GridView;

                gvs.Add(gv);
                sheetNames.Add(tp.Text);
            }
            ExcelNPOIManager.ExportToExcel(gvs, sheetNames);
        }
    }
}
