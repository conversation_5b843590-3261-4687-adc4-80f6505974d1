﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTAreaQuery : AreaKpiQueryBase
    {
        public ZTAreaQuery()
            : base(MainModel.GetInstance())
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "查看行政区域"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 31000, 31002, this.Name);
        }
        protected override bool setConditionDlg()
        {
            return true;
        }

        protected override void searchData()
        {
            //
        }

        ZTAreaListForm areaForm = null;
        protected override void fireShowForm()
        {
            ZTAreaManager mgr = ZTAreaManager.Instance;
            if (areaForm == null || areaForm.IsDisposed)
            {
                areaForm = new ZTAreaListForm(mgr);
            }
            areaForm.InitData();
            areaForm.Visible = true;
            areaForm.Owner = MainModel.MainForm;
            areaForm.BringToFront();
        }
    }
}
