﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class HandoverBehindTimeListForm_NR : MinCloseForm
    {
        public HandoverBehindTimeListForm_NR()
            : base()
        {
            InitializeComponent();
        }

        public void FillData(List<HandoverBehindTimeInfo_NR> list)
        {
            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
            MainModel.ClearDTData();
            foreach (HandoverBehindTimeInfo_NR item in list)
            {
                foreach (TestPoint tp in item.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.FireDTDataChanged(this);
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            int[] rows = gridView.GetSelectedRows();
            if (rows.Length>0)
            {
                HandoverBehindTimeInfo_NR info = gridView.GetRow(rows[0]) as HandoverBehindTimeInfo_NR;
                if (info!=null)
                {
                    MainModel.SelectedTestPoints.Clear();
                    MainModel.SelectedTestPoints.AddRange(info.TestPoints);
                    TestPoint midTp = info.TestPoints[info.TestPoints.Count / 2];
                    MainModel.MainForm.GetMapForm().GoToView(midTp.Longitude, midTp.Latitude, 6000);
                }
            }
        }

        private void miExp2Xls_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(gridView);
        }
    }
}