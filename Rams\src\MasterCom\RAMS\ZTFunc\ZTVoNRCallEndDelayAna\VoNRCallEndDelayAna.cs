﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class VoNRCallEndDelayAnaBase : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();

        float minDelaySeconds = 2.0f;
        readonly List<NRCallEndDelayInfo> callEndDelayInfoList = new List<NRCallEndDelayInfo>();

        protected VoNRCallEndDelayAnaBase()
            : base(MainModel.GetInstance())
        {
            this.IncludeEvent = true;
            this.Columns = new List<string>();

            Columns.Add("NR_TAC");
            Columns.Add("NR_NCI");
            Columns.Add("NR_SSB_ARFCN");
            Columns.Add("NR_PCI");
            Columns.Add("NR_SS_RSRP");
            Columns.Add("NR_SS_SINR");

            Columns.Add("NR_lte_TAC");
            Columns.Add("NR_lte_ECI");
            Columns.Add("NR_lte_EARFCN");
            Columns.Add("NR_lte_PCI");
            Columns.Add("NR_lte_RSRP");
            Columns.Add("NR_lte_SINR");
        }
        public override string Name
        {
            get { return "VoNR挂机时延分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27017, this.Name);
        }
        protected override void clearDataBeforeAnalyseFiles()
        {
            callEndDelayInfoList.Clear();
        }
        protected override bool getCondition()
        {
            NRCallEndConditionDlg dlg = new NRCallEndConditionDlg();
            dlg.SetCondition(minDelaySeconds);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            dlg.GetCondition(out minDelaySeconds);
            return true;
        }
        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    NRCallEndDelayInfo info = null;
                    foreach (Event evt in file.Events)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            return;
                        }

                        if (evt.ID == (int)EnumEvt.VoLteAudioMoCallEnd || evt.ID == (int)EnumEvt.VoLteVideoMoCallEnd ||
                            evt.ID == (int)EnumEvt.VoLteAudioMtCallEnd || evt.ID == (int)EnumEvt.VoLteVideoMtCallEnd ||
                            evt.ID == (int)EnumEvt.EpsfbAudioMoCallEnd || evt.ID == (int)EnumEvt.EpsfbVideoMoCallEnd ||
                            evt.ID == (int)EnumEvt.EpsfbAudioMtCallEnd || evt.ID == (int)EnumEvt.EpsfbVideoMtCallEnd ||
                            evt.ID == (int)EnumEvt.VoNrAudioMoCallEnd || evt.ID == (int)EnumEvt.VoNrVideoMoCallEnd ||
                            evt.ID == (int)EnumEvt.VoNrAudioMtCallEnd || evt.ID == (int)EnumEvt.VoNrVideoMtCallEnd)
                        {
                            int milSec = int.Parse(evt["Value1"].ToString());
                            float timeDelay = milSec / 1000.0f;
                            if (timeDelay >= minDelaySeconds)
                            {
                                info = new NRCallEndDelayInfo(evt);
                                info.DelaySeconds = timeDelay;
                                getBeforeAndAfterInfo(info, file.TestPoints);

                                info.SN = callEndDelayInfoList.Count + 1;
                                callEndDelayInfoList.Add(info);
                            }
                        }
                    }
                }
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private void getBeforeAndAfterInfo(NRCallEndDelayInfo item, List<TestPoint> fileTestPoints)
        {
            int endTpIndex = getChangeTestpointIndex(fileTestPoints, item.EvtCallEnd);

            if (endTpIndex + 3 < fileTestPoints.Count)
            {
                endTpIndex = endTpIndex + 3;
            }

            for (int i = endTpIndex; i >= 0; i--)
            {
                TestPoint tp = fileTestPoints[i];
                double duration = (item.EvtCallEnd.DateTime - tp.DateTime).TotalSeconds;
                if (duration < 5 + item.DelaySeconds && duration >= item.DelaySeconds)  //挂机前5秒
                {
                    item.TestPointBefore.Add(tp);
                }
                else if (duration < item.DelaySeconds)  //挂机后（取4个采样点）
                {
                    item.TestPointAfter.Add(tp);
                }
                else
                {
                    break;
                }
            }
            item.StatBeforeAndAfterInfo();
        }

        private static int getChangeTestpointIndex(List<TestPoint> tpList, Event e)
        {
            int index = -1;
            for (int i = 0; i < tpList.Count; i++)
            {
                if (tpList[i].SN > e.SN)
                {
                    index = i - 1;
                    break;
                }
                if (tpList[i].SN == e.SN)
                {
                    index = i;
                    break;
                }
            }

            return index;
        }

        protected override void fireShowForm()
        {
            if (callEndDelayInfoList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            VoNRCallEndDelayInfoForm frm = MainModel.CreateResultForm(typeof(VoNRCallEndDelayInfoForm)) as VoNRCallEndDelayInfoForm;
            frm.FillData(callEndDelayInfoList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();

            mainModel.FireSetDefaultMapSerialTheme("NR_SS_RSRP");
        }

        protected enum EnumEvt
        {
            VoLteAudioMoCallEnd = 9155,
            VoLteAudioMtCallEnd = 9156,
            VoLteVideoMoCallEnd = 9193,
            VoLteVideoMtCallEnd = 9194,

            EpsfbAudioMoCallEnd = 9340,
            EpsfbAudioMtCallEnd = 9341,
            EpsfbVideoMoCallEnd = 9352,
            EpsfbVideoMtCallEnd = 9353,

            VoNrAudioMoCallEnd = 9840,
            VoNrAudioMtCallEnd = 9841,
            VoNrVideoMoCallEnd = 9866,
            VoNrVideoMtCallEnd = 9867,
        }

    }

    public class VoNRCallEndDelayAnaByRegion : VoNRCallEndDelayAnaBase
    {
        private static VoNRCallEndDelayAnaByRegion instance = null;
        public static VoNRCallEndDelayAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoNRCallEndDelayAnaByRegion();
                    }
                }
            }
            return instance;
        }

        protected VoNRCallEndDelayAnaByRegion()
            : base()
        {
            this.FilterSampleByRegion = true;
        }
        public override string Name
        {
            get { return "VoNR挂机时延分析(按区域)"; }
        }
    }

    public class VoNRCallEndDelayAnaByFile : VoNRCallEndDelayAnaBase
    {
        protected VoNRCallEndDelayAnaByFile()
            : base()
        {
        }

        private static VoNRCallEndDelayAnaByFile intance = null;
        public static VoNRCallEndDelayAnaByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new VoNRCallEndDelayAnaByFile();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "VoNR挂机时延分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }
}
