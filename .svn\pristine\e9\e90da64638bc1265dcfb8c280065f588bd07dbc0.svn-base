<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="toolStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>170, 17</value>
  </metadata>
  <metadata name="contextMenuStripDisplayChartCount.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 54</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="toolStripSplitButtonDisplayChartCount.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAKMSURBVDhPnZJpSJRRFIYnSCpEQiK0lAiCNDNJzQTH/tjm
        kmVSBkUqTkpCKWgLVI7buJtUmlhIi5QohGIDWaZojjpqKmG5ZmqCZjLajJpN5vjUN6XyVX/qhRfu5d73
        4XDOkSzoubqLCHkOv67/pmf1nVy/V4aLl2wRoG5UkppxRORfT2JVNHRyNb+UioYuEUAIGAxa5uZG+apv
        JTbW8++AvIJSKhu7aO4c+QOQdzscpVJBW2OxEdDVVMf9aBkPfjgzyO/n3/isO7T2jNLeNyYCpKQeZnay
        mVltDS2qXBTyAxTLI5kf7AaVkkDrlUuAjgENvUMTIkCywgu9pgz9x4e8rIohK9CXogthtCdH0yY/w4l1
        JkuA/mEt5eoRzuaMEH5tCFl6H0lxu5kZzubz+ySay2WkB+xl9lYiX7Iv0xQdKAZ80EwhDXnCmNbAwNg8
        tZ0GFFfcmH4XzVTPKdQl+0nz28V0RhS6xNOoQn3FgIlJPVv9C/EIKyMoro6oG20kXnRC98Yf3as91Bdt
        I8XbhfFLJxmL8qcqwE0MmNF/w+ZgAcklemKKZghWqEmI2oK2xZlPTTbUFViQ5GHPcPg+hoKlPPW0FQPm
        DPNYeeQSma8j9OYEPudeEBexkYl6S8ZVq1HdNUEh3UT/MUfeHtrMY6mFGCDI3DWTo2mj+MSPsFNWQWy4
        JZqa5WiqJdTmS0jYsZ5uT2s6PNZS4mT6WwVzBlZtT8L9/CB2x8vZYO+NPNR8MezkaEe8wxpeu5vxyGGF
        0QLAOHZjE3XTRoAQFFytakEeYoqP6zKkzlbkFSiJszUzhhZcXN64BChU1hohQiWChJ4IjRWmI4xY2BNh
        2YSNrWzqXQoLEg7/Y2NYIpF8B8ukIfJEi/m4AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="toolStripButtonLegend.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAIWSURBVDhPpZLRT1JRHMf5Z3ys3JoPrce2tv6LWg9tPfXc
        VqyaW8yVlnCBBBKvXAJC8F4FvKmBpBggc5CxTGxrbjhbs5BC4N777ZwDkme0tdbZfg/n7Hw/v9/nnmui
        yx/Lg5Y0n8W0koUor8MbWYUnnIYrlMSzwDLs0iKEaRVWMdErFqZLms/hJ/DXOtIMVOs6tg80DF253g8I
        BADzfWDc0R+uG8CXho7KVw3pfJkH+JS37NLdB8CbNDAwEGd7b1CFS0pAmIphzK1gxDGL4l4bS5n3PGBq
        NsMCzglgcDCBm4/47t9aBva+69iqasjstBBLlXjAZHSVC5yuIx3E28AH4p3dbWHxXRPRV5s8wDOTZpch
        CsC1y2R2K9v/IN4HDYN5Fz638brchLxxjFCiwANcwWQHcPUSoNgQvnCO7T3+BdiJ/xP3HEacMh4KUbxc
        byBAnpwDOF8sdyewwXfxLDaE0Y53jXjva1gj3oliEzPZY0jpOsRohgc4yE9CASdVI++9T+an3rlP7Y53
        vhOeXDqEK5DiAYJPZcHDSgxaxISdotzzTna9T8I2pYpxr8oDbOICA9Aw9BTMN86w93aKcTx9PofHEzEM
        WyMwj4Zh8Vdgsct/BlQ2FQzfOo+Q393zDq797kzDd1wl3BsL9QNquoGtj7vIFbexkisjvlJCWC2wD0ad
        6di0Mw3ftvj6AfTgX6sb/59lMv0C7dmbjUpdSoAAAAAASUVORK5CYII=
</value>
  </data>
  <data name="toolStripButtonInfo.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAIdSURBVDhPxdDfT1JhHMdx/oIuuug/8aZWrVorW65ZLrd+
        LmuuFkUZhj+QDtNO5LB0lrh+UFtxoVCdYSZOGYoUGf7CUEQZ2WnDFqEhgQgcPj08MI7rpq7quz0359nr
        fZ7nkfzzWZ734IX0FF2rQR75z38334J+CkNP1Ji6novkt/48WTzANcBk+QD3zWsUtxYVQeIw38DGZedU
        GH6lhM1UD6uxDoNdNeh7rqB4eVELn0MKlm2g2Ge35QIZYea35YGQdiOdnMDS5170Pi0vYPODYmzZvAn8
        J2/u+HZS3oiF9EcCJ5FeH8PSYg96HpVQ7LVVFHAHUy7e3dpdW4BCaorCVOI9knEHOh8/g7NPA35CXsD9
        xio8bDouBiwGeR6OEzhK4Dskfw5jzMXRQCYjoKu9lOKAuwXzLg3uKcvEgFkvI9iFVNyJZMyORNSKtYgF
        DKPG5avVKC2jx8UcgSv8fficDFrkJWLApLtA4AjWozYkIv1YW3mNWPglRdkIZ2zGD76DvEMrwv5mzI4o
        cOvSfjFguHsWidUBAt8gHuYQC3Uj+tWASFCPyJdOAtsQDmjxfYFFaI6BZ0gGdeVuMaBnTxJoRmBaB/9k
        OxbGs/e8Dd9oEzmuCt63dZi1yzEzdAUemxTTg5WoP71DDOhUR8mjHEFbzWHcqT4EbdVBaGQHwF7ch8bz
        e8nf9kB1bheUZ3YSuB21J7ZBcWyrGPjPI5H8An0f2bZlUaW2AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="toolStripButtonTile.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAE1SURBVDhP7ZHPK4MBGMf3Tzm4yElxcJQzuUiKw1a0JGtk
        JTU/WhImFCkHDhwdHEgRGoa17d2Pd8bYu/d99/7YPmzvuyK7kIvyrc/lqe/3+/Q8jl+RbzNDDe96hpEV
        EedCmr6ZFF1TSTrHE7SPCrQMCzQ54zQOxrCtlirGMhA8hLl9k+k9A9+OjmdLw71RxBVUGFiU6Q3I9MxK
        9QMqeNZEhpZE+uet5g5vgjZ3nGa79SO21ZJ/+aC6gWGCqpUoKCY5ySST0xEeNSIplZuYysWDzOltgYbW
        7s8Bk4FdzFIZTS8hqyavBZPsi0EyqxFNFwkLClcRmbOwzHFI+how5t9GN8oo7+152eQpb5B+1omJGncJ
        lVBU4fxe5uRa4uiyToBrYrU6/A621VLtiBX+3/hn3/hzORxv5HYtf0SibUoAAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="contextMenuStripMS.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="toolStripSplitButtonMS.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAKNSURBVDhPvdNZSJRRGMbxISKCNisN3MolFwqhugu8SOyi
        Igg1ynKMyhKkItGgAosWyQIpt3FccjQ1l9RsLLVCLRiVjNRSw7DoomhF0aSwZb7z77XJrmJGDXpuPs65
        +PGe55xP99/T1/eUvCtl/F5OP92dj/j81cp4RttqMMaGTw990N7K84HnqHHpwzOs9Weh+SID2YlTB9tb
        LYzZhkJ15KKKw1E34lGWTHhcgeVE5OTQ5qYm3r77aJPed6KKgiFlDlRGQO0+1K1ErC0X4KEJc9x6++hu
        fTTdXU9E+s4PSxIqfT5c0KFSZ0PuClRlGNTsEvQwquUcWAzUHdpoHz2SeIzXhnUCzEQzLpSvC8rgBJfm
        weUgQbcIqkeZDwiazFh9MoURQfbROBcdX3LcUeVL0UyuUOCKEpgMgfNXyv5mqN6Bdj0Wmk4zWH6U42s9
        7aMlm6S3al+0Ch9UyTIo9EDlCZ4pU+f4o0o3yLTb0K7thTtJvDLGOb6kjvgl0LACa8VyVJkvFAmc5w7p
        iyDLW9ahUBqGVrkH6hLoP7XZPqp3mcHbdB9oDJJJx6fyRZkEzXGTTp1QactQBSEoYzBa4XZ42UZfT699
        9OSa2Xyrkt7qBC0PgGKpIN8TlS0VnJdaUhajbsZjHXr166UND4+g3xllHzVHyhEbVklf8nSu+oNJ0LQF
        cmwvVJfJ9idJent6iIqMItArwD641XkW/SflqA2r0Ur95MadUbXS3eCATVIKc+0NQkNCHV/MRPbP1TEk
        nVHlj9ad8Weq0U+jZGcZCPRzMNXfUhzhjXp3H2W1MtyZz72WUxyMTcDdw23q2ESevXgDYyOYU88Qs2cS
        b89RDOkZ3G68S1h0zL9jU4tO9xPYzQcfCNJSuAAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="toolStripButtonSetting.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAF+SURBVDhPrZBdL4JxGMb7LIb5Aoxx5ohxZHNsY1rfwpnN
        kWZ5ZNVMGb0oS0lZL15akidaZdbrapMlUkhFuXj+nkx6c9Bvu/ffvee6fnt2c9qG9ugaf0dt8UFuvIBs
        1wn6Kg42Wh+m0AiR0oa+0ZnmEo3Vz8ZrEciMMNl93xJ/rL5EeeBh47UsrRtIuTJspZrNvXMSlshU6Ozq
        gWpHT3aGUrkMnZWG0uBoLJBqT0m4o7MbNE1jYnKK7ALxFvhCKag1BeR6O3pHpusLJOpjUlBodBgcGobB
        ZCN7IBzHpS8ElycIkcLcWLAqt5DCb0rlDzzlikikXhCIpX9uwVaqWd4wkZLb7QaPx0MkEkG+8I7U4yui
        Nxl4gymIv/6gf2y2voCxM3C5XPJSFIXMcwGLK1LM88WYW6Cwve/EwHgLQSgUglAoRPIuheRDDrQvihNX
        AGZHAFLNYeMbVAQMxbcS0tk8YrdZ+MP3cHoTMJ/FyA1aCpi32TQVMB//M2ylHXA4n3yH2+GIroPEAAAA
        AElFTkSuQmCC
</value>
  </data>
  <data name="toolStripButton_compareReplay.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAABiSURBVDhP1YzRCYAwDAUdPWO6guAOMS2klfSlrwh+9OAU
        kvSOfxDRJVNsWRgeRFPQMTLFloweeBcnRurcA20wMVLnHvisBxj9GJEEbLFqHvA/0T5IfDx6X6ciy5Kx
        QYAr+gAhWyhRlL64vgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="toolStripMenuItemDisplayChartCount.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAKMSURBVDhPnZJpSJRRFIYnSCpEQiK0lAiCNDNJzQTH/tjm
        kmVSBkUqTkpCKWgLVI7buJtUmlhIi5QohGIDWaZojjpqKmG5ZmqCZjLajJpN5vjUN6XyVX/qhRfu5d73
        4XDOkSzoubqLCHkOv67/pmf1nVy/V4aLl2wRoG5UkppxRORfT2JVNHRyNb+UioYuEUAIGAxa5uZG+apv
        JTbW8++AvIJSKhu7aO4c+QOQdzscpVJBW2OxEdDVVMf9aBkPfjgzyO/n3/isO7T2jNLeNyYCpKQeZnay
        mVltDS2qXBTyAxTLI5kf7AaVkkDrlUuAjgENvUMTIkCywgu9pgz9x4e8rIohK9CXogthtCdH0yY/w4l1
        JkuA/mEt5eoRzuaMEH5tCFl6H0lxu5kZzubz+ySay2WkB+xl9lYiX7Iv0xQdKAZ80EwhDXnCmNbAwNg8
        tZ0GFFfcmH4XzVTPKdQl+0nz28V0RhS6xNOoQn3FgIlJPVv9C/EIKyMoro6oG20kXnRC98Yf3as91Bdt
        I8XbhfFLJxmL8qcqwE0MmNF/w+ZgAcklemKKZghWqEmI2oK2xZlPTTbUFViQ5GHPcPg+hoKlPPW0FQPm
        DPNYeeQSma8j9OYEPudeEBexkYl6S8ZVq1HdNUEh3UT/MUfeHtrMY6mFGCDI3DWTo2mj+MSPsFNWQWy4
        JZqa5WiqJdTmS0jYsZ5uT2s6PNZS4mT6WwVzBlZtT8L9/CB2x8vZYO+NPNR8MezkaEe8wxpeu5vxyGGF
        0QLAOHZjE3XTRoAQFFytakEeYoqP6zKkzlbkFSiJszUzhhZcXN64BChU1hohQiWChJ4IjRWmI4xY2BNh
        2YSNrWzqXQoLEg7/Y2NYIpF8B8ukIfJEi/m4AAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="contextMenuStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>263, 17</value>
  </metadata>
  <data name="toolStripMenuItem_compareReplay.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIIAAACAgP///wAAAAAAgOzp2AAAAAAAAAAAACH/C05FVFNDQVBFMi4wAwEBAAAh+QQB
        AAAFACwAAAAAEAAQAAAIVAALCBxIsKBBAAgTKjwYQOHCgg4dMgxAsWLDAhEVWgSAsWHGjRgzZuxosSJH
        iCUHqFzJEkDKACxbOowZk4DNmwNKVhxwE6dOijx7Esj5M2hPmiwDAgA7
</value>
  </data>
  <data name="toolStripMenuItemTile.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAE1SURBVDhP7ZHPK4MBGMf3Tzm4yElxcJQzuUiKw1a0JGtk
        JTU/WhImFCkHDhwdHEgRGoa17d2Pd8bYu/d99/7YPmzvuyK7kIvyrc/lqe/3+/Q8jl+RbzNDDe96hpEV
        EedCmr6ZFF1TSTrHE7SPCrQMCzQ54zQOxrCtlirGMhA8hLl9k+k9A9+OjmdLw71RxBVUGFiU6Q3I9MxK
        9QMqeNZEhpZE+uet5g5vgjZ3nGa79SO21ZJ/+aC6gWGCqpUoKCY5ySST0xEeNSIplZuYysWDzOltgYbW
        7s8Bk4FdzFIZTS8hqyavBZPsi0EyqxFNFwkLClcRmbOwzHFI+how5t9GN8oo7+152eQpb5B+1omJGncJ
        lVBU4fxe5uRa4uiyToBrYrU6/A621VLtiBX+3/hn3/hzORxv5HYtf0SibUoAAAAASUVORK5CYII=
</value>
  </data>
  <data name="toolStripMenuItemSetting.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAF+SURBVDhPrZBdL4JxGMb7LIb5Aoxx5ohxZHNsY1rfwpnN
        kWZ5ZNVMGb0oS0lZL15akidaZdbrapMlUkhFuXj+nkx6c9Bvu/ffvee6fnt2c9qG9ugaf0dt8UFuvIBs
        1wn6Kg42Wh+m0AiR0oa+0ZnmEo3Vz8ZrEciMMNl93xJ/rL5EeeBh47UsrRtIuTJspZrNvXMSlshU6Ozq
        gWpHT3aGUrkMnZWG0uBoLJBqT0m4o7MbNE1jYnKK7ALxFvhCKag1BeR6O3pHpusLJOpjUlBodBgcGobB
        ZCN7IBzHpS8ElycIkcLcWLAqt5DCb0rlDzzlikikXhCIpX9uwVaqWd4wkZLb7QaPx0MkEkG+8I7U4yui
        Nxl4gymIv/6gf2y2voCxM3C5XPJSFIXMcwGLK1LM88WYW6Cwve/EwHgLQSgUglAoRPIuheRDDrQvihNX
        AGZHAFLNYeMbVAQMxbcS0tk8YrdZ+MP3cHoTMJ/FyA1aCpi32TQVMB//M2ylHXA4n3yH2+GIroPEAAAA
        AElFTkSuQmCC
</value>
  </data>
  <data name="LineChartForm.Appearance.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIcAAAEAAAAAAP///yZLgqGwxqfH6leX4EN4ukyFyv39/aW0y1iW4FaU3VeT3Pr6+uj1
        +Jmvyuz2+pywye32+leS2Pz8/JGsytzw+tzw+5Ksydzv+gAAAFeR2FeQ1tnr/Nnv/GeNvXup3gAAAFiR
        1liQ1I2qyo6qytXr+9Xs+wAAAFiP1FiP09Dq+4upyoypytLq+3mo3nio3liO0wAAAAAAAAAAAAAAAFeM
        0FuJwGCNwpK34szm+wAAAFiNz1GAvvX6/meUy8rl+1eMzvz+/2GQyFSFxU98t058twAAAKi20AAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        ACH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAAAACwAAAAAEAAQAAAIiQABCBxIsKDBgwYLGFi4cMABhggK
        DDSQoKLFiwkWDGSAsSODgQ0cgBhJEoZJBw0GUqhwoSUGlxouOOAwsEOFkjBC6AxRYcRAEglOCB16AgWK
        BCoGrkgQo6lTpwlkDLyRYIfVq1cF9Bjo44fJr08FCCHoYwgRAWjToi1S0MgRI3Djwk2CsK5dgwEBADs=
</value>
  </data>
</root>