﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRQueryHandoverFailInfo : DIYEventByRegion
    {
        protected static readonly object lockObj = new object();
        private static NRQueryHandoverFailInfo instance = null;
        public static NRQueryHandoverFailInfo GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRQueryHandoverFailInfo(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        private List<NRHandoverFailInfo> infoSet = new List<NRHandoverFailInfo>();

        protected NRQueryHandoverFailInfo(MainModel mainModel)
            : base(mainModel)
        {
            this.IsCanExportResultMapToWord = true;
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        public override string Name
        {
            get { return "切换失败分析"; }
        }
        public override string IconName
        {
            get { return "Images/event/handover.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35021, this.Name);
        }

        private NRHandoverFailCondition curCondition = new NRHandoverFailCondition();
        protected override bool getConditionBeforeQuery()
        {
            NRHandoverFailDlg dlg = new NRHandoverFailDlg(curCondition);
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                curCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override bool prepareAskWhatEvent()
        {
            Condition.EventIDs = NREventHelper.HandoverHelper.GetHandoverFailEvt(curCondition.IsAnaLte);
            return true;
        }

        protected override void getResultAfterQuery()
        {
            infoSet = new List<NRHandoverFailInfo>();
            MainModel.DTDataManager.Sort();
            MainModel.SelectedEvents.Clear();
         
            int sn = 1;
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (curCondition.IsAnaLte)
                {
                    sn = anaLTEAndNREvt(sn, file);
                }
                else
                {
                    sn = anaNREvt(sn, file);
                }
            }
        }

        private int anaNREvt(int sn, DTFileDataManager file)
        {
            foreach (Event evt in file.Events)
            {
                evt.Selected = true;
                MainModel.SelectedEvents.Add(evt);
                NRHandoverEventHelper.HandOverCellInfo cellInfo = NREventHelper.HandoverHelper.GetHandOverCellInfo(evt);

                NRHandoverFailInfo info = new NRHandoverFailInfo(sn++, evt, cellInfo);
                infoSet.Add(info);
            }

            return sn;
        }

        private int anaLTEAndNREvt(int sn, DTFileDataManager file)
        {
            NREventHelper.HandoverHelper.FilterHandoverEvents(file.Events);
            foreach (Event evt in file.Events)
            {
                evt.Selected = true;
                MainModel.SelectedEvents.Add(evt);
                NRHandoverEventHelper.HandOverCellInfo cellInfo = NREventHelper.HandoverHelper.GetHandOverCellInfo(evt);

                NRHandoverFailInfo info = new NRHandoverFailInfo(sn++, evt, cellInfo);
                infoSet.Add(info);
            }

            return sn;
        }

        protected override void fireShowFormAfterQuery()
        {
            NRHandoverFailForm frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(NRHandoverFailForm).FullName) as NRHandoverFailForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new NRHandoverFailForm();
            }

            frm.FillData(infoSet);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }
}
