﻿using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class LeakOutCellAnaByRegion_LTE : LeakOutCellAnaBase_LTE
    {
        private LeakOutCellAnaByRegion_LTE()
            : base()
        {
            FilterSampleByRegion = true;
        }

        private static LeakOutCellAnaByRegion_LTE instance = null;
        public static LeakOutCellAnaByRegion_LTE GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LeakOutCellAnaByRegion_LTE();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "外泄分析_LTE(按区域)"; }
        }

    }
    public class LeakOutCellAnaByFile_LTE : LeakOutCellAnaBase_LTE
    {
        private LeakOutCellAnaByFile_LTE()
            : base()
        {
        }

        private static LeakOutCellAnaByFile_LTE instance = null;
        public static LeakOutCellAnaByFile_LTE GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LeakOutCellAnaByFile_LTE();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "外泄分析_LTE(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
    public class LeakOutCellAnaBase_LTE : DIYAnalyseByCellBackgroundBaseByFile
    {
        public LTELeakOutCellSetCondition LTELeakOutCond { get; set; } = new LTELeakOutCellSetCondition();
        LeakOutAsNCellDlg conditionDlg = null;
        int rsrpThreshold = -85;
        int rsrpDValue = 10;
        int gsmSN = 0;

        readonly Dictionary<LTECell, LteLeakOutIndoorCell> lteCellLeakOutDic = new Dictionary<LTECell, LteLeakOutIndoorCell>();
        Dictionary<Cell, LteGSMLeakOutCell> gsmCellLeakOutDic = new Dictionary<Cell, LteGSMLeakOutCell>();

        protected static readonly object lockObj = new object();
        protected LeakOutCellAnaBase_LTE()
            : base(MainModel.GetInstance())
        {
            IncludeEvent = false;
            init();
        }
        protected void init()
        {
            Columns = new List<string>();
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_NCell_RSRP");
            Columns.Add("lte_NCell_SINR");
            Columns.Add("lte_NCell_EARFCN");
            Columns.Add("lte_NCell_PCI");
            Columns.Add("lte_gsm_DM_RxLevSub");
            Columns.Add("lte_gsm_DM_RxQualSub");
            Columns.Add("lte_gsm_NC_RxLev");
            Columns.Add("lte_gsm_SC_LAC");
            Columns.Add("lte_gsm_SC_CI");
            Columns.Add("lte_gsm_SC_BCCH");
            Columns.Add("lte_gsm_SC_BSIC");

            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        public override string Name
        {
            get { return "外泄分析_LTE"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22042, this.Name);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            if (conditionDlg == null)
            {
                conditionDlg = new LeakOutAsNCellDlg(true);
            }
            if (conditionDlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                this.LTELeakOutCond = new LTELeakOutCellSetCondition(conditionDlg);
                if (!this.LTELeakOutCond.LeakOutAsMainCell && !this.LTELeakOutCond.LeakOutAsNCell)
                {
                    return false;
                }
                this.rsrpThreshold = LTELeakOutCond.MinNCellRxlev;
                this.rsrpDValue = LTELeakOutCond.DiffRxlev;
                gsmCellLeakOutDic = new Dictionary<Cell, LteGSMLeakOutCell>();
                gsmSN = 0;
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            lteCellLeakOutDic.Clear();
            gsmCellLeakOutDic.Clear();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    if (MainModel.IsBackground)
                    {
                        FileInfo fileInfo = file.GetFileInfo();
                        curAnaFileInfo = fileInfo == null ? curAnaFileInfo : fileInfo;
                    }

                    foreach (TestPoint tp in file.TestPoints)
                    {
                        doWithTestPoint(tp);
                    }
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }
        protected virtual void doWithTestPoint(TestPoint tp)
        {
            bool isBreak = false;
            if (LTELeakOutCond.IsGetGSMCell)
                isBreak = dowithCSFBDataSC(tp);
            if (LTELeakOutCond.IsGetGSMCell && LTELeakOutCond.LeakOutAsNCell && !isBreak)
                dowithCSFBDataNC(tp);
            float? sRsrp = (float?)tp["lte_RSRP"];
            float? sSinr = (float?)tp["lte_SINR"];
            LTECell sLteCell = tp.GetMainCell_LTE();//getMainCell(tp);//原 
            if (sRsrp == null)
            {
                return;
            }

            bool isLeakOutBySCell = false;
            if (this.LTELeakOutCond.LeakOutAsMainCell && sLteCell != null && sLteCell.Type == LTEBTSType.Indoor)
            {
                LteLeakOutIndoorCell indoorCell = null;
                if (!lteCellLeakOutDic.TryGetValue(sLteCell, out indoorCell))
                {
                    indoorCell = new LteLeakOutIndoorCell(sLteCell);
                    lteCellLeakOutDic.Add(sLteCell, indoorCell);
                }
                indoorCell.AddTestPoint(tp, (float)sRsrp, sSinr, true);
                isLeakOutBySCell = true;
            }

            if (this.LTELeakOutCond.LeakOutAsNCell && !isLeakOutBySCell)
            {
                addLteCellLeakOutDic(tp, sRsrp);
            }
        }

        private void addLteCellLeakOutDic(TestPoint tp, float? sRsrp)
        {
            for (int i = 0; i < 12; ++i)
            {
                float? nRsrp = (float?)tp["lte_NCell_RSRP"];
                if (nRsrp == null || nRsrp < this.LTELeakOutCond.MinNCellRxlev || sRsrp - nRsrp > this.LTELeakOutCond.DiffRxlev)
                {
                    break;
                }

                float? nSinr = (float?)tp["lte_NCell_SINR"];
                LTECell nLteCell = tp.GetNBCell_LTE(i);//getNBCell(tp, i);//原 
                if (nLteCell == null || nLteCell.Type != LTEBTSType.Indoor)
                {
                    continue;
                }

                LteLeakOutIndoorCell indoorCell = null;
                if (!lteCellLeakOutDic.TryGetValue(nLteCell, out indoorCell))
                {
                    indoorCell = new LteLeakOutIndoorCell(nLteCell);
                    lteCellLeakOutDic.Add(nLteCell, indoorCell);
                }
                indoorCell.AddTestPoint(tp, (float)nRsrp, nSinr, false);
            }
        }

        /// <summary>
        /// /分析采样点的主服是否是GSM室内小区
        /// </summary>
        protected bool dowithCSFBDataSC(TestPoint testPoint)
        {
            short? rxlev = (short?)testPoint["lte_gsm_DM_RxLevSub"];
            if (rxlev == null || rxlev < -120 || rxlev > -10)
                return false;

            Cell mainCell = testPoint.GetMainCell_LTE_GSM();
            if (mainCell != null && mainCell.Type == BTSType.Indoor)
            {
                addLeakoutGSMCell(mainCell, testPoint, false, -1);
                return true;
            }
            else
                return false;
        }

        /// <summary>
        /// 分析采样点的邻区是否是GSM室内小区
        /// </summary>
        protected void dowithCSFBDataNC(TestPoint testPoint)
        {
            int? mainRxLev = (int?)(short?)testPoint["lte_gsm_DM_RxLevSub"];
            if (mainRxLev == null || mainRxLev < -120 || mainRxLev > -10)
                return;

            for (int i = 0; i < 6; i++)
            {
                int? rxlev = (int?)(short?)testPoint["lte_gsm_NC_RxLev", i];
                if (rxlev == null || rxlev < rsrpThreshold || mainRxLev - rxlev > rsrpDValue)
                {
                    break;
                }
                Cell cell = CellManager.GetInstance().GetNearestCell(testPoint.DateTime, (short?)testPoint["lte_gsm_NC_BCCH", i],
                    (byte?)testPoint["lte_gsm_NC_BSIC", i], testPoint.Longitude, testPoint.Latitude, (ushort?)(int?)testPoint["lte_gsm_SC_LAC"],
                    (ushort?)(int?)testPoint["lte_gsm_SC_CI"], (short?)testPoint["lte_gsm_SC_BCCH"], (byte?)testPoint["lte_gsm_SC_BSIC"]);
                if (cell != null && cell.Type == BTSType.Indoor)
                {
                    addLeakoutGSMCell(cell, testPoint, true, i);
                }
            }
        }
        protected void addLeakoutGSMCell(Cell cell, TestPoint testPoint, bool bNBCell, int indexNB)
        {
            LteGSMLeakOutCell stater = null;
            if (gsmCellLeakOutDic.ContainsKey(cell))
            {
                stater = gsmCellLeakOutDic[cell];
            }
            else
            {
                stater = new LteGSMLeakOutCell(cell);
                gsmCellLeakOutDic[cell] = stater;
                stater.SN = ++gsmSN;
            }
            stater.AddGSMTestPoint(testPoint);
            if (bNBCell)
                stater.AddRxLevSub((float?)(short?)testPoint["lte_gsm_NC_RxLev", indexNB], false);
            else
            {
                stater.AddRxLevSub((float?)(short?)testPoint["lte_gsm_DM_RxLevSub"], true);
                byte? rxQual = (byte?)testPoint["lte_gsm_DM_RxQualSub"];
                if (rxQual != null && rxQual >= 0 && rxQual <= 7)
                    stater.AddQualVal((float)rxQual);
            }
            stater.AddDistance(cell.GetDistance(testPoint.Longitude, testPoint.Latitude));
        }

        protected override void getResultsAfterQuery()
        {
            int sn = 0;
            foreach (LteLeakOutIndoorCell indoorCell in this.lteCellLeakOutDic.Values)
            {
                indoorCell.SN = ++sn;
                indoorCell.GetResult(this.LTELeakOutCond.IsGetRoadDesc);
                indoorCell.SetTop5FileName();
            }
            foreach (Cell cell in gsmCellLeakOutDic.Keys)
            {
                gsmCellLeakOutDic[cell].GetResult();
                gsmCellLeakOutDic[cell].SetTop5FileName();
                getGSMRoadDesc(cell.Longitude, cell.Latitude, gsmCellLeakOutDic[cell]);
            }
        }
        protected void getGSMRoadDesc(double longitude, double latitude, LteGSMLeakOutCell leakOut)
        {
            string area = GISManager.GetInstance().GetAreaPlaceDesc(longitude, latitude);
            leakOut.AreaDesc = area;

            if (LTELeakOutCond.IsGetRoadDesc)
            {
                List<string> multiRoads = new List<string>();
                foreach (TestPoint tp in leakOut.testPointList)
                {
                    string road = GISManager.GetInstance().GetRoadPlaceDesc(tp.Longitude, tp.Latitude);
                    addMultiRoads(multiRoads, road);
                }
                StringBuilder roadDesc = new StringBuilder();
                foreach (string road in multiRoads)
                {
                    if (roadDesc.Length > 0)
                    {
                        roadDesc.Append(";");
                    }
                    roadDesc.Append(road);
                }
                leakOut.RoadDesc = roadDesc.ToString();
            }
        }

        private static void addMultiRoads(List<string> multiRoads, string road)
        {
            if (!string.IsNullOrEmpty(road))
            {
                string[] roads = road.Split(';');
                foreach (string item in roads)
                {
                    if (!string.IsNullOrEmpty(item) && !multiRoads.Contains(item))
                    {
                        multiRoads.Add(item);
                    }
                }
            }
        }

        protected override void fireShowForm()
        {
            if (lteCellLeakOutDic.Values.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            LTELeakOutCellSetResultForm frm = MainModel.CreateResultForm(typeof(LTELeakOutCellSetResultForm)) as LTELeakOutCellSetResultForm;
            frm.FillData(new List<LteLeakOutIndoorCell>(lteCellLeakOutDic.Values), new List<LteGSMLeakOutCell>(gsmCellLeakOutDic.Values));
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }


        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["LeakOutAsMainCell"] = LTELeakOutCond.LeakOutAsMainCell;
                param["LeakOutAsNCell"] = LTELeakOutCond.LeakOutAsNCell;
                param["IsGetRoadDesc"] = LTELeakOutCond.IsGetRoadDesc;
                param["IsGetGSMCell"] = LTELeakOutCond.IsGetGSMCell;
                param["MinNCellRxlev"] = LTELeakOutCond.MinNCellRxlev;
                param["DiffRxlev"] = LTELeakOutCond.DiffRxlev;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("LeakOutAsMainCell"))
                {
                    LTELeakOutCond.LeakOutAsMainCell = (bool)param["LeakOutAsMainCell"];
                }
                if (param.ContainsKey("LeakOutAsNCell"))
                {
                    LTELeakOutCond.LeakOutAsNCell = (bool)param["LeakOutAsNCell"];
                }
                if (param.ContainsKey("IsGetRoadDesc"))
                {
                    LTELeakOutCond.IsGetRoadDesc = (bool)param["IsGetRoadDesc"];
                }
                if (param.ContainsKey("IsGetGSMCell"))
                {
                    LTELeakOutCond.IsGetGSMCell = (bool)param["IsGetGSMCell"];
                }
                if (param.ContainsKey("MinNCellRxlev"))
                {
                    LTELeakOutCond.MinNCellRxlev = int.Parse(param["MinNCellRxlev"].ToString());
                }
                if (param.ContainsKey("DiffRxlev"))
                {
                    LTELeakOutCond.DiffRxlev = int.Parse(param["DiffRxlev"].ToString());
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new LeakOutCellProperties_LTE(this);
            }
        }

        protected override void saveBackgroundData()
        {
            getResultsAfterQuery();
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (LteLeakOutIndoorCell item in lteCellLeakOutDic.Values)
            {
                BackgroundResult result = item.ConvertToBackgroundResult(curAnaFileInfo);
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            foreach (LteGSMLeakOutCell item in gsmCellLeakOutDic.Values)
            {
                BackgroundResult result = item.ConvertToBackgroundResult(curAnaFileInfo);
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), curAnaFileInfo, bgResultList);
            lteCellLeakOutDic.Clear();
            gsmCellLeakOutDic.Clear();
        }

        protected override void initBackgroundImageDesc()
        {
            BackgroundResultList.Sort(BackgroundResult.ComparerByFileIdAndISTimeAsc);
            Dictionary<string, BackgroundResult> bgResultDic = new Dictionary<string, BackgroundResult>();
            string lastFileName = "";
            foreach (BackgroundResult curBgResult in BackgroundResultList)
            {
                int sinrLT0SampleCount = 0;
                int gsmQualHT4Count = 0;
                float distanceMean = 0;
                int mainCellSampleCount = curBgResult.GetImageValueInt();
                if (curBgResult.CellType == BackgroundCellType.LTE)
                {
                    curBgResult.GetImageValueInt();//nCellSampleCount
                    sinrLT0SampleCount = curBgResult.GetImageValueInt();
                    distanceMean = curBgResult.GetImageValueFloat();
                    curBgResult.GetImageValueInt();//outdoorCellsCount

                }
                else if (curBgResult.CellType == BackgroundCellType.GSM)
                {
                    gsmQualHT4Count = curBgResult.GetImageValueInt();
                    distanceMean = curBgResult.GetImageValueFloat();
                    curBgResult.GetImageValueInt();//outdoorCellsCount
                }
                
                BackgroundResult cellBgResult;
                Dictionary<string, float> imageDic;
                string cellKey = curBgResult.LAC + "_" + curBgResult.CI;
                if (bgResultDic.TryGetValue(cellKey, out cellBgResult))
                {
                    imageDic = cellBgResult.ImageResultObj as Dictionary<string, float>;

                    cellBgResult.SampleCount += curBgResult.SampleCount;

                    cellBgResult.RxLevMax = Math.Max(cellBgResult.RxLevMax, curBgResult.RxLevMax);
                    cellBgResult.RxLevMin = Math.Min(cellBgResult.RxLevMin, curBgResult.RxLevMin);
                    imageDic["总场强"] += curBgResult.SampleCount * curBgResult.RxLevMean;

                    imageDic["主服采样点数"] += mainCellSampleCount;
                    imageDic["SINR≤0采样点数"] += sinrLT0SampleCount;
                    imageDic["RxQual>4采样点数"] += gsmQualHT4Count;
                    imageDic["总距离"] += curBgResult.SampleCount * distanceMean;
                }
                else
                {
                    imageDic = new Dictionary<string, float>();
                    imageDic["总场强"] = curBgResult.SampleCount * curBgResult.RxLevMean;
                    imageDic["主服采样点数"] = mainCellSampleCount;
                    imageDic["SINR≤0采样点数"] = sinrLT0SampleCount;
                    imageDic["RxQual>4采样点数"] = gsmQualHT4Count;
                    imageDic["总距离"] = curBgResult.SampleCount * distanceMean;
                    curBgResult.ImageResultObj = imageDic;

                    cellBgResult = curBgResult;
                    bgResultDic[cellKey] = cellBgResult;
                }
                StringBuilder fileName = new StringBuilder(cellBgResult.FileName);
                if (curBgResult.FileName != lastFileName)
                {
                    fileName.Append(curBgResult.FileName);
                    cellBgResult.FileName = fileName.ToString();
                }
                lastFileName = curBgResult.FileName;
            }
            bgResultToNPOIRow(bgResultDic);
        }
        private NPOIRow getRowTitle(BackgroundCellType cellType)
        {
            NPOIRow rowTitle = new NPOIRow();
            rowTitle.AddCellValue("室分小区");
            rowTitle.AddCellValue("文件名");
            rowTitle.AddCellValue("总采样点数");
            rowTitle.AddCellValue("主服采样点数");
            if (cellType == BackgroundCellType.LTE)
            {
                rowTitle.AddCellValue("TAC");
                rowTitle.AddCellValue("ECI");
                rowTitle.AddCellValue("EARFCN");
                rowTitle.AddCellValue("PCI");
                rowTitle.AddCellValue("SINR≤0采样点数");
            }
            else
            {
                rowTitle.AddCellValue("LAC");
                rowTitle.AddCellValue("CI");
                rowTitle.AddCellValue("RxQual>4采样点数");
            }
            rowTitle.AddCellValue("最大RSRP");
            rowTitle.AddCellValue("最小RSRP");
            rowTitle.AddCellValue("平均RSRP");
            rowTitle.AddCellValue("最大SINR");
            rowTitle.AddCellValue("最小SINR");
            rowTitle.AddCellValue("平均SINR");
            rowTitle.AddCellValue("平均距离");
            rowTitle.AddCellValue("道路名称");
            return rowTitle;
        }
        private void bgResultToNPOIRow(Dictionary<string, BackgroundResult> bgResultDic)
        {
            List<NPOIRow> lteNPOIRowList = new List<NPOIRow>();
            List<NPOIRow> gsmNPOIRowList = new List<NPOIRow>();
            this.BackgroundNPOIRowResultDic.Clear();
            this.BackgroundNPOIRowResultDic["LTE"] = lteNPOIRowList;
            this.BackgroundNPOIRowResultDic["GSM"] = gsmNPOIRowList;

            lteNPOIRowList.Add(getRowTitle(BackgroundCellType.LTE));
            gsmNPOIRowList.Add(getRowTitle(BackgroundCellType.GSM));

            foreach (BackgroundResult bgResult in bgResultDic.Values)
            {
                Dictionary<string, float> imageDic = bgResult.ImageResultObj as Dictionary<string, float>;
                bgResult.RxLevMean = (float)Math.Round(imageDic["总场强"] * 1.0 / bgResult.SampleCount, 2);
                imageDic["平均距离"] = (float)Math.Round(imageDic["总距离"] * 1.0 / bgResult.SampleCount, 2);


                NPOIRow row = new NPOIRow();
                row.AddCellValue(bgResult.CellName);
                row.AddCellValue(bgResult.FileName);
                row.AddCellValue(bgResult.SampleCount);
                row.AddCellValue(imageDic["主服采样点数"]);
                row.AddCellValue(bgResult.LAC);
                row.AddCellValue(bgResult.CI);
                if (bgResult.CellType == BackgroundCellType.LTE)
                {
                    row.AddCellValue(bgResult.BCCH);
                    row.AddCellValue(bgResult.BSIC);
                    row.AddCellValue(imageDic["SINR≤0采样点数"]);
                }
                else if (bgResult.CellType == BackgroundCellType.GSM)
                {
                    row.AddCellValue(imageDic["RxQual>4采样点数"]);
                }
                row.AddCellValue(bgResult.RxLevMax);
                row.AddCellValue(bgResult.RxLevMin);
                row.AddCellValue(bgResult.RxLevMean);
                row.AddCellValue(bgResult.RxQualMax);
                row.AddCellValue(bgResult.RxQualMin);
                row.AddCellValue(bgResult.RxQualMean);
                row.AddCellValue(imageDic["平均距离"]);
                row.AddCellValue(bgResult.RoadDesc);

                if (bgResult.CellType == BackgroundCellType.LTE)
                {
                    lteNPOIRowList.Add(row);
                }
                else
                {
                    gsmNPOIRowList.Add(row);
                }

                imageDic.Clear();
            }
        }
        #endregion
    }
}
