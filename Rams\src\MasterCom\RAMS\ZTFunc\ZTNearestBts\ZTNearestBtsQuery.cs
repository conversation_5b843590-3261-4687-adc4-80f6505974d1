﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Data;
using System.Collections;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTNearestBtsQuery : QueryBase
    {
        protected string xlsFilePath = null;
        protected int distance = 3000;
        protected int btsNum = 3;
        protected int ColumnCounts = 18;
        private readonly List<VillageGrid> villageGrids;

        public ZTNearestBtsQuery(MainModel mainModel)
            : base(mainModel)
        {
            villageGrids = new List<VillageGrid>();
        }

        public override string Name
        {
            get { return "最近基站"; }
        }

        public override string IconName
        {
            get { return ""; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 19000, 19048, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
 
        protected void clearData()
        {
            villageGrids.Clear();
        }

        protected virtual bool ReadXLS(ref DataTable datatable)
        {
            NearestBtsSettingForm form = new NearestBtsSettingForm();
            form.SetFilePath(xlsFilePath);
            if (form.ShowDialog() != System.Windows.Forms.DialogResult.OK) return false;
            xlsFilePath = form.FilePath;
            distance = form.Distance;
            btsNum = form.BtsNum;

            if (!System.IO.File.Exists(xlsFilePath))
            {
                System.Windows.Forms.MessageBox.Show("此xls文件不存在", "提示");
                return false;
            }

            ArrayList sheets = ExcelTools.GetSheetNameList(xlsFilePath);
            if (sheets.Count > 0)
            {
                string item = sheets[0].ToString();
                DataTable table = ExcelTools.ExcelToDataTable(xlsFilePath, item);
                if (table.Rows.Count > 0)
                {
                    if (table.Columns.Count < ColumnCounts)
                    {
                        System.Windows.Forms.MessageBox.Show("sheet  " + item + "缺少字段");
                    }
                    else if (table.Columns.Count > ColumnCounts)
                    {
                        System.Windows.Forms.MessageBox.Show("sheet  " + item + "过多字段");
                    }
                    else
                    {
                        datatable.Merge(table);
                    }
                }

            }
            return true;
        }

        protected override void query()
        {
            clearData();
            DataTable datatable = new DataTable();
            if (!ReadXLS(ref datatable)) return;

            if (datatable.Rows.Count > 0)
            {
                getData(datatable);
            }
            classify();
            fireShowForm();
        }

        protected void getData(DataTable datatable)
        {
            foreach (DataRow dr in datatable.Rows)
            {
                villageGrids.Add(new VillageGrid(dr));
            }
        }

        private void classify()
        {
            List<BTS> btsList = CellManager.GetInstance().GetCurrentBTSs();
            List<TDNodeB> nodebList = CellManager.GetInstance().GetCurrentTDBTSs();

            foreach (VillageGrid grid in villageGrids)
            {
                foreach (BTS bts in btsList)
                {
                    grid.DealBts(bts, distance);
                }
                foreach (TDNodeB nodeb in nodebList)
                {
                    grid.DealNodeB(nodeb, distance);
                }
                grid.FinalDeal(btsNum);
            }
        }

        private void fireShowForm()
        {
            object obj = MainModel.GetObjectFromBlackboard(typeof(NearestBtsInfoForm).FullName);
            NearestBtsInfoForm form = obj == null ? new NearestBtsInfoForm(MainModel) : obj as NearestBtsInfoForm;
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
            form.FillData(villageGrids, btsNum);
        }
    }

    public class VillageGrid
    {
        public int SN { get; set; }
        public string Province { get; set; }
        public string City { get; set; }
        public string Country { get; set; }
        public string Town { get; set; }
        public string Scene { get; set; }

        public PointLongLat FirstPoint
        {
            get
            {
                if (pointVec.Count <= 0)
                {
                    return new PointLongLat(0.0, 0.0);
                }
                return pointVec[0];
            }
        }

        private readonly MapWinGIS.Shape shp = new MapWinGIS.Shape();
        public double RegionArea { get; set; } = 0;

        private readonly List<PointLongLat> pointVec = new List<PointLongLat>();
        private readonly List<DisBts> nearestBtsVec = new List<DisBts>();
        private readonly List<DisTDNodeB> nearestNodebVec = new List<DisTDNodeB>();

        public List<DisBts> NearestBtsVec
        {
            get { return nearestBtsVec; }
        }

        public List<DisTDNodeB> NearestNodebVec
        {
            get { return nearestNodebVec; }
        }

        private VillageGrid() { }

        public VillageGrid(DataRow dr)
        {
            shp.Create(MapWinGIS.ShpfileType.SHP_POLYGON);
            int ptIdx = 0;
            int idx = 0;

            this.SN = Convert.ToInt32(dr[idx++]);
            this.Province = Convert.ToString(dr[idx++]);
            this.City = Convert.ToString(dr[idx++]);
            this.Country = Convert.ToString(dr[idx++]);
            this.Town = Convert.ToString(dr[idx++]);
            this.Scene = Convert.ToString(dr[idx++]);

            for (int i = 0; i < 6;i++ )
            {
                double dblongitude;
                double dblatitude;
                string longitude = Convert.ToString(dr[idx++]);
                string latitude = Convert.ToString(dr[idx++]);
                if (longitude != null &&
                    latitude != null &&
                    double.TryParse(longitude, out dblongitude) &&
                    double .TryParse(latitude, out dblatitude))
                {
                    pointVec.Add(new PointLongLat(dblongitude, dblatitude));
                    MapWinGIS.Point pnt = new MapWinGIS.Point();
                    pnt.x = dblongitude;
                    pnt.y = dblatitude;
                    shp.InsertPoint(pnt, ref ptIdx);
                }
                else
                {
                    break;
                }
            }
            if (pointVec.Count > 0)
            {
                MapWinGIS.Point pnt = new MapWinGIS.Point();
                pnt.x = pointVec[0].Longitude;
                pnt.y = pointVec[0].Latitude;
                shp.InsertPoint(pnt, ref ptIdx);
            }
        }

        public void DealBts(BTS bts, double distanceThreshold)
        {
            double distance = MathFuncs.GetDistance(FirstPoint.Longitude, FirstPoint.Latitude, bts.Longitude, bts.Latitude);
            if (distance > distanceThreshold) return;
            nearestBtsVec.Add(new DisBts(distance, bts));
        }

        public void DealNodeB(TDNodeB nodeb, double distanceThreshold)
        {
            double distance = MathFuncs.GetDistance(FirstPoint.Longitude, FirstPoint.Latitude, nodeb.Longitude, nodeb.Latitude);
            if (distance > distanceThreshold) return;
            nearestNodebVec.Add(new DisTDNodeB(distance, nodeb));
        }

        public void FinalDeal(int btsNum)
        {
            nearestBtsVec.Sort();
            nearestNodebVec.Sort();

            if (nearestBtsVec.Count > btsNum)
            {
                nearestBtsVec.RemoveRange(btsNum, nearestBtsVec.Count - btsNum);
            }
            if (nearestNodebVec.Count > btsNum)
            {
                nearestNodebVec.RemoveRange(btsNum, nearestNodebVec.Count - btsNum);
            }

            RegionArea = RegionAreaCalculator.CalculateArea(shp);
        }
    }

    public class PointLongLat
    {
        public double Longitude { get; set; }
        public double Latitude { get; set; }

        public PointLongLat(double longitude, double latitude)
        {
            this.Longitude = longitude;
            this.Latitude = latitude;
        }
    }

    public class DisBts : IComparable
    {
        public double distance { get; set; }
        public BTS bts { get; set; }

        private DisBts() { }

        public DisBts(double distance, BTS bts)
        {
            this.distance = distance;
            this.bts = bts;
        }

        #region IComparable 成员

        public int CompareTo(object obj)
        {
            DisBts disBts = obj as DisBts;
            return this.distance.CompareTo(disBts.distance);
        }

        #endregion
    }

    public class DisTDNodeB : IComparable
    {
        public double distance { get; set; }
        public TDNodeB nodeb { get; set; }

        private DisTDNodeB() { }

        public DisTDNodeB(double distance, TDNodeB nodeb)
        {
            this.distance = distance;
            this.nodeb = nodeb;
        }

        #region IComparable 成员

        public int CompareTo(object obj)
        {
            DisTDNodeB node = obj as DisTDNodeB;
            return this.distance.CompareTo(node.distance);
        }

        #endregion
    }
}
