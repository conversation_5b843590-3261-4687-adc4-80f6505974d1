﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using System.Drawing;

namespace MasterCom.RAMS.ZTFunc
{
    public class FartherCoverLayer : CustomDrawLayer
    {
        private FartherCoverInfo info;

        public FartherCoverLayer(MapOperation mp, string name)
            : base(mp, name)
        {
        }

        public void FillData(FartherCoverInfo info)
        {
            this.info = info;
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, System.Drawing.Graphics graphics)
        {
            if (info == null) return;
            DbPoint pntCell = new DbPoint(info.Longitude, info.Latitude);
            PointF pntFCell;
            this.Map.ToDisplay(pntCell, out pntFCell);

            foreach (TestPoint tp in info.TestPointVec)
            {
                DbPoint pnt = new DbPoint(tp.Longitude, tp.Latitude);
                PointF pntF;
                this.Map.ToDisplay(pnt, out pntF);

                graphics.DrawLine(new Pen(Color.Red), pntFCell, pntF);
            }
        }
    }
}
