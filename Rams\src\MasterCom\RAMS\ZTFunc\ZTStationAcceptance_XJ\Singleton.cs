﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class Singleton<T> where T : new()
    {
        protected Singleton()
        {
            if (Instance != null)
            {
                throw (new Exception("You have tried to create a new singleton class"));
            }
        }

        public static T Instance
        {
            get { return SingletonCreator.instance; }
        }

        static class SingletonCreator
        {
            internal static readonly T instance = new T();
        }
    }
}
