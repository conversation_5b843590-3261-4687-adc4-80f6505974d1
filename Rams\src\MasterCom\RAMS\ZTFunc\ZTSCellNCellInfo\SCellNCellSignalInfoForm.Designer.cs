﻿namespace MasterCom.RAMS.ZTFunc.ZTSCellNCellInfo
{
    partial class SCellNCellSignalInfoForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tvCell = new BrightIdeasSoftware.TreeListView();
            this.colCell = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLac = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCi = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colPointNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colNMinLev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colNMaxLev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colNAvgLev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSMinLev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSMaxLev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSAvgLev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCloseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.tvCell)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // tvCell
            // 
            this.tvCell.AllColumns.Add(this.colCell);
            this.tvCell.AllColumns.Add(this.colLac);
            this.tvCell.AllColumns.Add(this.colCi);
            this.tvCell.AllColumns.Add(this.colDistance);
            this.tvCell.AllColumns.Add(this.colPointNum);
            this.tvCell.AllColumns.Add(this.colNMinLev);
            this.tvCell.AllColumns.Add(this.colNMaxLev);
            this.tvCell.AllColumns.Add(this.colNAvgLev);
            this.tvCell.AllColumns.Add(this.colSMinLev);
            this.tvCell.AllColumns.Add(this.colSMaxLev);
            this.tvCell.AllColumns.Add(this.colSAvgLev);
            this.tvCell.AllColumns.Add(this.colFileName);
            this.tvCell.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colCell,
            this.colLac,
            this.colCi,
            this.colDistance,
            this.colPointNum,
            this.colNMinLev,
            this.colNMaxLev,
            this.colNAvgLev,
            this.colSMinLev,
            this.colSMaxLev,
            this.colSAvgLev,
            this.colFileName});
            this.tvCell.ContextMenuStrip = this.contextMenuStrip;
            this.tvCell.Cursor = System.Windows.Forms.Cursors.Default;
            this.tvCell.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tvCell.FullRowSelect = true;
            this.tvCell.GridLines = true;
            this.tvCell.HeaderWordWrap = true;
            this.tvCell.IsNeedShowOverlay = false;
            this.tvCell.Location = new System.Drawing.Point(0, 0);
            this.tvCell.Name = "tvCell";
            this.tvCell.OwnerDraw = true;
            this.tvCell.ShowGroups = false;
            this.tvCell.Size = new System.Drawing.Size(950, 391);
            this.tvCell.TabIndex = 13;
            this.tvCell.UseCompatibleStateImageBehavior = false;
            this.tvCell.View = System.Windows.Forms.View.Details;
            this.tvCell.VirtualMode = true;
            this.tvCell.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.tvCell_MouseDoubleClick);
            // 
            // colCell
            // 
            this.colCell.HeaderFont = null;
            this.colCell.Text = "主服/邻区";
            this.colCell.Width = 160;
            // 
            // colLac
            // 
            this.colLac.HeaderFont = null;
            this.colLac.Text = "LAC";
            // 
            // colCi
            // 
            this.colCi.HeaderFont = null;
            this.colCi.Text = "CI";
            // 
            // colDistance
            // 
            this.colDistance.HeaderFont = null;
            this.colDistance.Text = "邻区与主服距离(m)";
            // 
            // colPointNum
            // 
            this.colPointNum.HeaderFont = null;
            this.colPointNum.Text = "涉及采样点个数";
            // 
            // colNMinLev
            // 
            this.colNMinLev.HeaderFont = null;
            this.colNMinLev.Text = "邻区最小场强";
            // 
            // colNMaxLev
            // 
            this.colNMaxLev.HeaderFont = null;
            this.colNMaxLev.Text = "邻区最大场强";
            // 
            // colNAvgLev
            // 
            this.colNAvgLev.HeaderFont = null;
            this.colNAvgLev.Text = "邻区平均场强";
            // 
            // colSMinLev
            // 
            this.colSMinLev.HeaderFont = null;
            this.colSMinLev.Text = "主服最小场强";
            // 
            // colSMaxLev
            // 
            this.colSMaxLev.HeaderFont = null;
            this.colSMaxLev.Text = "主服最大场强";
            // 
            // colSAvgLev
            // 
            this.colSAvgLev.HeaderFont = null;
            this.colSAvgLev.Text = "主服平均场强";
            // 
            // colFileName
            // 
            this.colFileName.HeaderFont = null;
            this.colFileName.Text = "文件";
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCloseAll,
            this.toolStripMenuItem1,
            this.miExport2Xls});
            this.contextMenuStrip.Name = "contextMenuStrip1";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 76);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCloseAll
            // 
            this.miCloseAll.Name = "miCloseAll";
            this.miCloseAll.Size = new System.Drawing.Size(129, 22);
            this.miCloseAll.Text = "全部合并";
            this.miCloseAll.Click += new System.EventHandler(this.miCloseAll_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(126, 6);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(129, 22);
            this.miExport2Xls.Text = "导出Excel";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // SCellNCellSignalInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(950, 391);
            this.Controls.Add(this.tvCell);
            this.Name = "SCellNCellSignalInfoForm";
            this.Text = "主服邻区场强信息列表";
            ((System.ComponentModel.ISupportInitialize)(this.tvCell)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView tvCell;
        private BrightIdeasSoftware.OLVColumn colCell;
        private BrightIdeasSoftware.OLVColumn colLac;
        private BrightIdeasSoftware.OLVColumn colCi;
        private BrightIdeasSoftware.OLVColumn colPointNum;
        private BrightIdeasSoftware.OLVColumn colNMinLev;
        private BrightIdeasSoftware.OLVColumn colNMaxLev;
        private BrightIdeasSoftware.OLVColumn colNAvgLev;
        private BrightIdeasSoftware.OLVColumn colSMinLev;
        private BrightIdeasSoftware.OLVColumn colSMaxLev;
        private BrightIdeasSoftware.OLVColumn colSAvgLev;
        private BrightIdeasSoftware.OLVColumn colFileName;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCloseAll;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private BrightIdeasSoftware.OLVColumn colDistance;
    }
}