﻿using System;
using System.Collections.Generic;
using System.Text;
using System.ComponentModel;
using MasterCom.ES.UI;
using MasterCom.Util;
using System.IO;
using System.Windows.Forms;
using System.Xml;
using MasterCom.ES.Data;
using System.Drawing;

namespace MasterCom.ES.Core
{
    public class ESEngine
    {
        private static ESEngine engine = null;
        //
        public Dictionary<string, ProcRoutine> inProcModuleDic { get; set; } = new Dictionary<string, ProcRoutine>();
        public List<RoutineGroup> groups { get; set; } = new List<RoutineGroup>();
        public ResvStore pubResvStore { get; set; } = new ResvStore();
        public Dictionary<string, DTParameter> dtParamDic { get; set; } = new Dictionary<string, DTParameter>();
        public Dictionary<int, List<L3MsgParam>> dtL3ParamListDic { get; set; } = new Dictionary<int, List<L3MsgParam>>();
        public Dictionary<int, DTEvent> dtEventDic { get; set; } = new Dictionary<int, DTEvent>();
        public Dictionary<string, Dictionary<int, string>> dtIdDescDic { get; set; } = new Dictionary<string, Dictionary<int, string>>();
        public ProcRoutine CurRunningProc { get; set; }
        public Stack<RunningStackRem> RunningStack { get; set; } = new Stack<RunningStackRem>();
        ESProcessForm esform = null;
        public static ESEngine GetInstance(ESProcessForm form)
        {
            if(engine==null)
            {
                engine = new ESEngine();
                engine.initEngine();
                engine.esform = form;
            }
            return engine;
        }
        public static ESEngine GetInstance()
        {
            if (engine == null)
            {
                engine = new ESEngine();
                engine.initEngine();
            }
            return engine;
        }
        public ESEngine()
        {
            
        }

        private void initEngine()
        {
            prepareUnZipFromFile();
            loadSettings();
        }

        private void prepareUnZipFromFile()
        {
            try
            {
                string srcZipFile = Application.StartupPath + @"\esconfig.dlz";
                string targetESconfigFolder = Application.StartupPath + @"\esconfig\";
                FileInfo zipFileInfo = new FileInfo(srcZipFile);
                DirectoryInfo tarDirInfo = new DirectoryInfo(targetESconfigFolder);
                if (tarDirInfo.Exists && zipFileInfo.Exists)//检测是不是更新了新的esconfig_pack.zip文件
                {
                    DateTime zipFileDate = zipFileInfo.LastWriteTime;
                    FileInfo[] allFileInfos = tarDirInfo.GetFiles("*.*", SearchOption.AllDirectories);
                    foreach (FileInfo finfo in allFileInfos)
                    {
                        DateTime fileDate = finfo.LastWriteTime;
                        if (fileDate > zipFileDate)
                        {
                            //目录中文件是新的，不要进行替换
                            return;
                        }
                    }
                    //目录中文件都不晚于Zip的修改时间，需要进行替换
                    //先清空目录
                    tarDirInfo.Delete(true);

                }
                else if (!tarDirInfo.Exists && zipFileInfo.Exists)
                {
                    //不存在esconfig目录，但存在zip文件
                }
                else
                {
                    return;
                }
                //到此，已经保证无esconfig目录，有zip文件，下面解压缩...
                UnZipClass UnZc = new UnZipClass();
                UnZc.UnZip(srcZipFile, targetESconfigFolder);
            }
            catch
            {
                //continue
            }
        }
        private void loadSettings()
        {
            string saveRootDir = Application.StartupPath + @"\esconfig";
            //预存值===
            DataGatherProxy proxy = DataGatherProxy.GetInstance();
            pubResvStore = proxy.PublicResvStore;

            addResvValue(saveRootDir);
            //parakeys
            addSettingsConfig(saveRootDir);
            //==end para keys
            //begin reason define
            addReasondefConfig(saveRootDir);
            //==end reason define
            //判断流程
            groups.Clear();
            inProcModuleDic.Clear();
            DirectoryInfo rootDirInfo = new DirectoryInfo(saveRootDir);
            DirectoryInfo[] groupDirs = rootDirInfo.GetDirectories("[*]");
            foreach (DirectoryInfo groupDir in groupDirs)
            {
                addDirectoryAllConfig(groupDir);
            }
            addDefConfig(saveRootDir);
        }

        private void addResvValue(string saveRootDir)
        {
            XmlConfigFile configFile = new XmlConfigFile(saveRootDir + @"\resv.xml");
            XmlElement configResv = configFile.GetConfig("ResvValue");
            Dictionary<string, object> dicPub = configFile.GetItemValue(configResv, "Public", ResvStore.GetItemValue) as Dictionary<string, object>;
            pubResvStore.Param = dicPub;
            try
            {
                configResv = configFile.GetConfig("ResvValueString");
                Dictionary<string, object> dicPubStr = configFile.GetItemValue(configResv, "Public", ResvStore.GetItemValue) as Dictionary<string, object>;
                pubResvStore.ParamStr = dicPubStr;
            }
            catch
            {
                //continue
            }
        }

        private void addSettingsConfig(string saveRootDir)
        {
            try
            {
                XmlConfigFile cfgParamFile = new XmlConfigFile(saveRootDir + @"\params.xml");
                XmlElement cfgAll = cfgParamFile.GetConfig("Settings");
                Dictionary<string, object> objDic = cfgParamFile.GetItemValue(cfgAll, "ParamKeys", DTParameter.GetItemValue) as Dictionary<string, object>;
                foreach (object o in objDic.Values)
                {
                    DTParameter p = o as DTParameter;
                    dtParamDic[p.paramKey] = p;
                }
                Dictionary<string, object> objL3Dic = cfgParamFile.GetItemValue(cfgAll, "L3MsgParams", L3MsgParam.GetItemValue) as Dictionary<string, object>;
                if (objL3Dic != null)
                {
                    foreach (string k in objL3Dic.Keys)
                    {
                        int msgid = int.Parse(k);
                        List<object> listx = objL3Dic[k] as List<object>;
                        List<L3MsgParam> rlist = new List<L3MsgParam>();
                        foreach (object obj in listx)
                        {
                            rlist.Add(obj as L3MsgParam);
                        }
                        dtL3ParamListDic[msgid] = rlist;
                    }
                }
                try
                {
                    Dictionary<string, object> objEvtDic = cfgParamFile.GetItemValue(cfgAll, "Events", DTEvent.GetItemValue) as Dictionary<string, object>;
                    foreach (object o in objEvtDic.Values)
                    {
                        DTEvent p = o as DTEvent;
                        dtEventDic[p.eventId] = p;
                    }
                }
                catch
                {
                    //continue
                }
            }
            catch
            {
                //continue
            }
        }

        private void addReasondefConfig(string saveRootDir)
        {
            try
            {
                XmlConfigFile cfgParamFile = new XmlConfigFile(saveRootDir + @"\reasondef.xml");
                XmlElement cfgAll = cfgParamFile.GetConfig("IDStrDic");
                Dictionary<string, object> objDic = cfgParamFile.GetItemValue(cfgAll, "Defines") as Dictionary<string, object>;
                foreach (string keystr in objDic.Keys)
                {
                    Dictionary<string, object> dicx = objDic[keystr] as Dictionary<string, object>;
                    Dictionary<int, string> resDicX = new Dictionary<int, string>();
                    foreach (string idstr in dicx.Keys)
                    {
                        int id = int.Parse(idstr);
                        resDicX[id] = dicx[idstr] as string;
                    }
                    dtIdDescDic[keystr] = resDicX;
                }
            }
            catch
            {
                //continue
            }
        }

        private void addDirectoryAllConfig(DirectoryInfo groupDir)
        {
            RoutineGroup rg = new RoutineGroup();
            rg.Name = groupDir.Name.Substring(1, groupDir.Name.Length - 2);
            groups.Add(rg);
            FileInfo[] routineFiles = groupDir.GetFiles("[*].xml");
            foreach (FileInfo fi in routineFiles)
            {
                XmlConfigFile procFile = new XmlConfigFile(fi.FullName);
                XmlElement configRoutine = procFile.GetConfig("ProcRoutine");
                NodeEntry rootNode = procFile.GetItemValue(configRoutine, "EntryRoot", ProcRoutine.GetItemValue) as NodeEntry;
                if (rootNode != null)
                {
                    ProcRoutine routine = new ProcRoutine();
                    routine.Name = fi.Name.Substring(1, fi.Name.Length - 6);
                    routine.RootNode = rootNode;
                    rg.Routines.Add(routine);
                    //读取预存值配置
                    try
                    {
                        XmlElement inProcRsvCfg = procFile.GetConfig("InProcResvValue");
                        Dictionary<string, object> dicFloat = procFile.GetItemValue(inProcRsvCfg, "ResvValue", ResvStore.GetItemValue) as Dictionary<string, object>;
                        routine.ReservStore.Param = dicFloat;
                        Dictionary<string, object> dicString = procFile.GetItemValue(inProcRsvCfg, "ResvStringValue", ResvStore.GetItemValue) as Dictionary<string, object>;
                        routine.ReservStore.ParamStr = dicString;
                        routine.InProcModule = (bool)procFile.GetItemValue(configRoutine, "ModuleType");
                    }
                    catch
                    {
                        //continue
                    }
                    if (routine.InProcModule)
                    {
                        inProcModuleDic[routine.Name] = routine;
                    }
                }
            }
        }

        private void addDefConfig(string saveRootDir)
        {
            //自定义函数类
            try
            {
                XmlConfigFile configFileCmd = new XmlConfigFile(saveRootDir + @"\defcmds.xml");
                System.Xml.XmlElement configCommanders = configFileCmd.GetConfig("DefConfig");
                Dictionary<string, object> dicCmders = configFileCmd.GetItemValue(configCommanders, "Commanders", ESOwnFuncCommander.GetItemValue) as Dictionary<string, object>;
                DTDataProvider provider = DTDataProvider.GetDTProviderInstance();
                provider.CommanderDic.Clear();
                foreach (object o in dicCmders.Values)
                {
                    ESOwnFuncCommander p = o as ESOwnFuncCommander;
                    provider.CommanderDic[p.desc] = p;
                }
            }
            catch
            {
                //continue
            }
        }

        public void SaveSettings()
        {
            string saveRootDir = Application.StartupPath + @"\esconfig";
            //预存值
            XmlConfigFile configFile = new XmlConfigFile();
            XmlElement configResv = configFile.AddConfig("ResvValue");
            configFile.AddItem(configResv, "Public", pubResvStore.ResvValueDic, ResvStore.AddItem);
            configResv = configFile.AddConfig("ResvValueString");
            configFile.AddItem(configResv, "Public", pubResvStore.ResvStringDic, ResvStore.AddItem);
            configFile.Save(saveRootDir + @"\resv.xml");
            //para keys
            XmlConfigFile configParaFile = new XmlConfigFile();
            XmlElement configAll = configParaFile.AddConfig("Settings");
            configParaFile.AddItem(configAll, "ParamKeys", dtParamDic, DTParameter.AddItem);
            configParaFile.AddItem(configAll, "L3MsgParams", dtL3ParamListDic, L3MsgParam.AddItem);
            configParaFile.AddItem(configAll, "Events", dtEventDic, DTEvent.AddItem);
            configParaFile.Save(saveRootDir + @"\params.xml");
            //end para keys
            //reason id->reason desc
            XmlConfigFile configReasonFile = new XmlConfigFile();
            XmlElement configAllReason = configReasonFile.AddConfig("IDStrDic");
            configReasonFile.AddItem(configAllReason, "Defines", dtIdDescDic);
            configReasonFile.Save(saveRootDir + @"\reasondef.xml");
            //end reason
            //判断流程
            foreach (RoutineGroup rg in groups)
            {
                string subFolderName = saveRootDir + @"\[" + rg.Name + "]";//组
                if (!Directory.Exists(subFolderName))
                {
                    Directory.CreateDirectory(subFolderName);
                }
                foreach (ProcRoutine pr in rg.Routines)//组内各流程
                {
                    if(!pr.IsDirty)
                    {
                        continue;
                    }
                    pr.ResetIdx();
                    string routineFileName = subFolderName + @"\[" + pr.Name + "]";
                    XmlConfigFile procFile = new XmlConfigFile();
                    XmlElement configRoutine = procFile.AddConfig("ProcRoutine");
                    procFile.AddItem(configRoutine, "EntryRoot", pr, ProcRoutine.AddItem);
                    procFile.AddItem(configRoutine, "ModuleType", pr.InProcModule);
                    //预存值
                    XmlElement inProcRsvCfg = procFile.AddConfig("InProcResvValue");
                    procFile.AddItem(inProcRsvCfg, "ResvValue", pr.ReservStore.ResvValueDic, ResvStore.AddItem);
                    procFile.AddItem(inProcRsvCfg, "ResvStringValue", pr.ReservStore.ResvStringDic, ResvStore.AddItem);
                    //end rsv
                    procFile.Save(routineFileName + ".xml");
                    pr.IsDirty = false;
                }
            }
            //自定义函数类
            XmlConfigFile configCommanderFile = new XmlConfigFile();
            XmlElement configCmder = configCommanderFile.AddConfig("DefConfig");
            configCommanderFile.AddItem(configCmder, "Commanders", DTDataProvider.GetDTProviderInstance().CommanderDic, ESOwnFuncCommander.AddItem);
            configCommanderFile.Save(saveRootDir + @"\defcmds.xml");

        }
        internal void SaveDirtySettingsTo(string destination)
        {
            string saveRootDir = destination + @"\esconfig";
            //预存值
            XmlConfigFile configFile = new XmlConfigFile();
            XmlElement configResv = configFile.AddConfig("ResvValue");
            configFile.AddItem(configResv, "Public", pubResvStore.ResvValueDic, ResvStore.AddItem);
            configResv = configFile.AddConfig("ResvValueString");
            configFile.AddItem(configResv, "Public", pubResvStore.ResvStringDic, ResvStore.AddItem);
            configFile.Save(saveRootDir + @"\resv.xml");
            //para keys
            XmlConfigFile configParaFile = new XmlConfigFile();
            XmlElement configAll = configParaFile.AddConfig("Settings");
            configParaFile.AddItem(configAll, "ParamKeys", dtParamDic, DTParameter.AddItem);
            configParaFile.AddItem(configAll, "L3MsgParams", dtL3ParamListDic, L3MsgParam.AddItem);
            configParaFile.AddItem(configAll, "Events", dtEventDic, DTEvent.AddItem);
            configParaFile.Save(saveRootDir + @"\params.xml");
            //end para keys
            //reason id->reason desc
            XmlConfigFile configReasonFile = new XmlConfigFile();
            XmlElement configAllReason = configReasonFile.AddConfig("IDStrDic");
            configReasonFile.AddItem(configAllReason, "Defines", dtIdDescDic);
            configReasonFile.Save(saveRootDir + @"\reasondef.xml");
            //end reason
            //判断流程
            foreach (RoutineGroup rg in groups)
            {
                bool hasDirt = false;
                foreach (ProcRoutine pr in rg.Routines)//组内各流程
                {
                    if (pr.IsDirty)
                    {
                        hasDirt = true;
                        break;
                    }
                }
                if(!hasDirt)
                {
                    continue;
                }
                string subFolderName = saveRootDir + @"\[" + rg.Name + "]";//组
                if (!Directory.Exists(subFolderName))
                {
                    Directory.CreateDirectory(subFolderName);
                }
                foreach (ProcRoutine pr in rg.Routines)//组内各流程
                {
                    if (!pr.IsDirty)
                    {
                        continue;
                    }
                    pr.ResetIdx();
                    string routineFileName = subFolderName + @"\[" + pr.Name + "]";
                    XmlConfigFile procFile = new XmlConfigFile();
                    XmlElement configRoutine = procFile.AddConfig("ProcRoutine");
                    procFile.AddItem(configRoutine, "EntryRoot", pr, ProcRoutine.AddItem);
                    procFile.AddItem(configRoutine, "ModuleType", pr.InProcModule);
                    //预存值
                    XmlElement inProcRsvCfg = procFile.AddConfig("InProcResvValue");
                    procFile.AddItem(inProcRsvCfg, "ResvValue", pr.ReservStore.ResvValueDic, ResvStore.AddItem);
                    procFile.AddItem(inProcRsvCfg, "ResvStringValue", pr.ReservStore.ResvStringDic, ResvStore.AddItem);
                    //end rsv
                    procFile.Save(routineFileName + ".xml");
                }
            }
        }

        private void resetAllProc()
        {
            foreach (RoutineGroup group in groups)
            {
                foreach (ProcRoutine routine in group.Routines)
                {
                    routine.ReservStore.Reset();
                }
            }
            pubResvStore.Reset();
            RunningStack.Clear();
        }
        public NodeEntry CurBrkNode { get; set; }
        internal void doRunNow(BackgroundWorker worker)
        {
            resetAllProc();
            CurBrkNode = null;
            foreach (RoutineGroup group in groups)
            {
                foreach (ProcRoutine routine in group.Routines)
                {
                    if(!routine.InProcModule)
                    {
                        CurRunningProc = routine;
                        object processObj = routine.RootNode.processNodeEntry(worker,false);
                        if (processObj is NodeEntry)//从断点跳出的
                        {
                            CurBrkNode = processObj as NodeEntry;
                            worker.ReportProgress(1, new OutputUnit("进入断点 Breaked " + CurBrkNode.ExpString + "\r\n ", Color.Red,Color.Yellow));
                            return;
                        }
                    }
                }
            }
        }
        internal void ContinueRunOnNode(BackgroundWorker worker)
        {
            if (CurBrkNode != null)
            {
                bool finished = false;
                while (!finished)
                {
                    CurBrkNode._breakPoint = false;
                    object processObj = CurBrkNode.processNodeEntry(worker, false);
                    CurBrkNode._breakPoint = true;
                    if (processObj is NodeEntry)//从断点跳出的
                    {
                        CurBrkNode = processObj as NodeEntry;
                        worker.ReportProgress(1, new OutputUnit("进入断点 Breaked " + CurBrkNode.ExpString + "\r\n ", Color.Red, Color.Yellow));
                        return;
                    }
                    else
                    {
                        bool isBreak = getFinished(worker, ref finished);
                        if (isBreak)
                        {
                            return;
                        }
                    }
                }
            }
        }

        private bool getFinished(BackgroundWorker worker, ref bool finished)
        {
            CurBrkNode = null;
            if (RunningStack.Count > 0)
            {
                RunningStackRem stackRem = RunningStack.Pop();
                CurRunningProc = stackRem.routine;
                NodeEntry nextNode = stackRem.nodeAt;
                if (nextNode != null)
                {
                    CurBrkNode = nextNode;
                    if (CurBrkNode._breakPoint)
                    {
                        worker.ReportProgress(1, new OutputUnit("进入断点 Breaked " + CurBrkNode.ExpString + "\r\n ", Color.Red, Color.Yellow));
                        return true;
                    }
                }
            }
            if (CurBrkNode == null)
            {
                finished = true;
            }

            return false;
        }

        internal void StepOverRunOnNode(BackgroundWorker worker)
        {
            if (CurBrkNode == null)
            {
                return;
            }

            NodeEntry beginNode = CurBrkNode;
            bool curFlag = CurBrkNode._breakPoint;
            CurBrkNode._breakPoint = false;
            object processObj = CurBrkNode.processNodeEntry(worker, true);
            CurBrkNode._breakPoint = curFlag;//恢复中断属性
            if (processObj is NodeEntry)//从断点跳出的
            {
                CurBrkNode = processObj as NodeEntry;
                worker.ReportProgress(1, new OutputUnit("进入断点 Breaked " + CurBrkNode.ExpString + "\r\n ", Color.Red, Color.Yellow));
            }
            else
            {
                if (beginNode != CurBrkNode)
                {
                    //不做处理
                }
                else if (RunningStack.Count == 0)
                {
                    CurBrkNode = null;
                }
                else if (RunningStack.Count > 0)
                {
                    RunningStackRem stackRem = RunningStack.Pop();
                    CurRunningProc = stackRem.routine;
                    NodeEntry nextNode = stackRem.nodeAt;
                    if (nextNode != null)
                    {
                        CurBrkNode = nextNode;
                    }
                }
            }
        }

        internal void StepInRunOnNode(BackgroundWorker worker)
        {
            if (CurBrkNode != null)
            {
                if(CurBrkNode.Type != NodeType.OtherProc)
                {
                    StepOverRunOnNode(worker);//调用Step Over
                }
                else//OtherProc
                {
                    CurBrkNode.GotoInProcStartNode(worker);
                }
            }
        }

        internal void ClearAllBreakPoint()
        {
            foreach (RoutineGroup rg in groups)
            {
                foreach (ProcRoutine pr in rg.Routines)//组内各流程
                {
                    if(pr.RootNode!=null)
                    {
                        pr.RootNode.ClearAllBreaks();
                    }
                }
            }
        }
        internal int GetTarFileID()
        {
            double fileid;
            if (pubResvStore.TryGetResvValue("pub_FILEID", out fileid))
            {
                return (int)fileid;
            }
            return -99999;
        }
        internal int GetFromTime()
        {
            double frm;
            if (pubResvStore.TryGetResvValue("pub_FROM", out frm) && frm >= 1)
            {
                return (int)frm;
            }
            return 3 * 60 * 1000;
        }

        internal int GetToTime()
        {
            double frm;
            if (pubResvStore.TryGetResvValue("pub_TO", out frm) && frm >= 1)
            {
                return (int)frm;
            }
            return 1 * 60 * 1000;
        }
        internal int GetFromTime_TarFile()
        {
            double frm;
            if (pubResvStore.TryGetResvValue("pub_FROM_Tar", out frm) && frm >= 1)
            {
                return (int)frm;
            }
            return 3 * 60 * 1000;
        }

        internal int GetToTime_TarFile()
        {
            double frm;
            if (pubResvStore.TryGetResvValue("pub_TO_Tar", out frm) && frm >= 1)
            {
                return (int)frm;
            }
            return 1 * 60 * 1000;
        }
    }
}
