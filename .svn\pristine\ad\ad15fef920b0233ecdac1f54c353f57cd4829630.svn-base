﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.BackgroundFunc
{
    public abstract class BackgroundQueryBase : QueryBase
    {
        protected BackgroundQueryBase(MainModel mainModel)
            : base(mainModel)
        {
        }

        /// <summary>
        /// 专题类型：业务级别
        /// </summary>
        public abstract BackgroundFuncType FuncType
        {
            get;
        }

        /// <summary>
        /// 专题子类型：问题类型
        /// </summary>
        public abstract BackgroundSubFuncType SubFuncType
        {
            get;
        }

        /// <summary>
        /// 统计类型
        /// </summary>
        public abstract BackgroundStatType StatType
        {
            get;
        }

        public override string Name
        {
            get { return "网络体检专题功能基类"; }
        }

        public override string IconName
        {
            get { return ""; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
        }

        /// <summary>
        /// 按时间段统计类
        /// </summary>
        /// <param name="clientProxy"></param>
        protected virtual void doBackgroundStatByPeriod(ClientProxy clientProxy)
        {
            QueryCondition queryCond = new QueryCondition();
            SetQueryCondition(queryCond);

            BackgroundFuncConfigManager configManager = BackgroundFuncConfigManager.GetInstance();
            Condition.Geometorys = new SearchGeometrys();
            Condition.Geometorys.Region = configManager.RegionBorder;

            BackgroundFuncBaseSetting baseSetting = BackgroundFuncBaseSetting.GetInstance();
            Condition.Projects = baseSetting.ProjectTypeList;
            this.ResetServTypesAndCarrierID();

            if (baseSetting.queryTimeType != 0)
            {
                TimeGatherMode statMode = configManager.AreaStatTimeMode;
                int statTimeUnitCount = configManager.StatTimeUnitCount;
                DateTime dateTime = configManager.StartTime;// 当月1号前推N个统计月份
                DateTime endDateTime = DIYBackgroundResultExport.GetDateTime(statMode, DateTime.Now.Date);  // 当前时间前推至统计周期的开始，作为分析的结束时间点
                DateTime beginDateTime = DIYBackgroundResultExport.GetDateTime(statMode, dateTime); // 前推N个月份后，再推前至统计周期开始，作为分析的开始时间点
                List<TimePeriod> statedTimePriods = GetStatedTimePeriod();
                while (beginDateTime <= endDateTime)
                {
                    if (MainModel.BackgroundStopRequest)
                    {
                        break;
                    }
                    DateTime nextDateTime = DIYBackgroundResultExport.GetNextDateTime(statMode, beginDateTime);
                    if (beginDateTime < getPriorDateTime(statMode, endDateTime, statTimeUnitCount)
                        && statedTimePriods.Contains(new TimePeriod(beginDateTime, nextDateTime)))
                    {
                        beginDateTime = nextDateTime; // 已统计的时间包含当前分析时间段，并且时间段开始早于重算时间点
                        continue;
                    }
                    Condition.Periods.Clear();
                    Condition.Periods.Add(new TimePeriod(beginDateTime, nextDateTime));
                    MainModel.ClearDTData();
                    reportBackgroundInfo("开始统计" + FuncType.ToString() + SubFuncType.ToString() + "类" 
                        + Name + " " + beginDateTime.ToString("yyyy-MM-dd") + " 至 " 
                        + nextDateTime.ToString("yyyy-MM-dd") + " 的数据...");

                    statData(clientProxy);

                    saveBackgroundData();
                    beginDateTime = nextDateTime;
                }
            }
            else
            {
                DateTime dateTime = configManager.StartTime;
                DateTime endDateTime = configManager.EndTime;
                MainModel.ClearDTData();
                Condition.Periods.Clear();
                Condition.Periods.Add(new TimePeriod(dateTime, endDateTime));
                reportBackgroundInfo("开始统计" + FuncType.ToString() + SubFuncType.ToString() + "类" 
                    + Name + " " + dateTime.ToString("yyyy-MM-dd") + " 至 " 
                    + endDateTime.ToString("yyyy-MM-dd") + " 的数据...");

                statData(clientProxy);

                saveBackgroundData();
            }
        }

        protected virtual void statData(ClientProxy clientProxy)
        {
        }

        protected virtual List<TimePeriod> GetStatedTimePeriod()
        {
            return new List<TimePeriod>();
        }
        protected DateTime getPriorDateTime(TimeGatherMode statMode, DateTime dateTime, int timeUnitCount)
        {
            if (statMode == TimeGatherMode.Day)
            {
                return dateTime.AddDays(-1 * (timeUnitCount - 1));
            }
            else if (statMode == TimeGatherMode.Week)
            {
                return dateTime.AddDays(-7 * (timeUnitCount - 1));
            }
            else if (statMode == TimeGatherMode.Month)
            {
                return dateTime.AddMonths(-1 * (timeUnitCount - 1));
            }
            return dateTime;
        }
        
        public bool BackgroundStat { get; set; } = false;
        /// <summary>
        /// 功能版本（改变该值以自动清空网络体检预处理结果后重算）
        /// </summary>
        protected double funcVersion = double.NaN;
        public virtual Dictionary<string, object> Param
        {
            get { return new Dictionary<string, object>(); }
            set { Console.Write(value); }
        }

        /// <summary>
        /// 网络体检保存分析条件到数据库时，可忽略保存的参数名称(BackgroundStat已自动忽略，不需再添加到该集合中)
        /// </summary>
        protected List<string> ignoreParamKeys = new List<string>();
        public string GetQueryCondParams()
        {
            string paramStr = paramToString(Param);
            if (!double.IsNaN(funcVersion))
            {
                return string.Format("<FuncVersion:{0}>{1}", funcVersion, paramStr);
            }
            else
            {
                return paramStr;
            }
        }

        protected string paramToString(object value)
        {
            if (value != null)
            {
                if (value is string || value is int || value is long || value is float || value is byte || value is double
                     || value is char || value is bool || value is DateTime)
                {
                    return value.ToString();
                }
                else if (value is System.Collections.IList)
                {
                    System.Collections.IList list = value as System.Collections.IList;
                    if (list != null && list.Count > 0)
                    {
                        StringBuilder strb = getListStr(list);
                        return strb.Remove(strb.Length - 1, 1).ToString();
                    }
                }
                else if (value is System.Collections.IDictionary)
                {
                    System.Collections.IDictionary dictionary = value as System.Collections.IDictionary;
                    if (dictionary != null && dictionary.Count > 0)
                    {
                        StringBuilder strb = getDictionaryStr(dictionary);
                        return strb.ToString();
                    }
                }
            }
            return "";
        }

        private StringBuilder getListStr(System.Collections.IList list)
        {
            StringBuilder strb = new StringBuilder();
            foreach (object childValue in list)
            {
                strb.Append(paramToString(childValue) + "，");
            }

            return strb;
        }

        private StringBuilder getDictionaryStr(System.Collections.IDictionary dictionary)
        {
            StringBuilder strb = new StringBuilder();
            foreach (object key in dictionary.Keys)
            {
                if (!key.Equals("BackgroundStat") && !ignoreParamKeys.Contains(key.ToString()))
                {
                    object childValue = dictionary[key];
                    if (childValue is System.Collections.IDictionary)
                    {
                        strb.Append(string.Format("<{0}>{1}</{0}>", key, paramToString(childValue)));
                    }
                    else
                    {
                        strb.Append(string.Format("<{0}:{1}>", key, paramToString(childValue)));
                    }
                }
            }

            return strb;
        }

        public virtual PropertiesControl Properties
        {
            get { return null; }
        }

        /// <summary>
        /// 保存网络体检结果
        /// </summary>
        protected virtual void saveBackgroundData()
        {

        }

        /// <summary>
        /// 获取网络体检结果
        /// </summary>
        protected virtual void getBackgroundData()
        {
        }

        /// <summary>
        /// 如果有个性信息存在image，查询完后，要构成image的文字描述，供呈现
        /// </summary>
        protected virtual void initBackgroundImageDesc()
        {

        }

        /// <summary>
        /// 网络体检结果列表
        /// </summary>
        public List<BackgroundResult> BackgroundResultList { get; set; } = new List<BackgroundResult>();

        /// <summary>
        /// 网络体检时是否需要导出专题结果图层
        /// </summary>
        public bool IsCanExportResultMapToWord { get; set; } = false;
        
        /// <summary>
        /// 自动导出的网络体检结果集合，该集合有值时，则不再导出BackgroundResultList的信息
        /// </summary>
        public Dictionary<string, List<NPOIRow>> BackgroundNPOIRowResultDic { get; set; } = new Dictionary<string, List<NPOIRow>>();

        /// <summary>
        /// 分析数据的业务类型，如果为空不过滤，都分析
        /// </summary>
        protected List<ServiceType> ServiceTypes = new List<ServiceType>();
        protected string ServiceTypeString
        {
            get
            {
                StringBuilder s = new StringBuilder();
                foreach (ServiceType service in ServiceTypes)
                {
                    if (s.Length > 0)
                    {
                        s.Append(",");
                    }
                    s.Append((int)service);
                }
                return s.ToString();
            }
        }
        protected CarrierType carrierID = CarrierType.ChinaMobile;

        public void ResetServTypesAndCarrierID()
        {
            condition.ServiceTypes.Clear();
            foreach (ServiceType st in ServiceTypes)
            {
                condition.ServiceTypes.Add((int)st);
            }
            condition.CarrierTypes.Clear();
            condition.CarrierTypes.Add((int)carrierID);
        }

        protected bool isIgnoreExport = false;
        /// <summary>
        /// 是否忽略自动导出网络体检结果
        /// </summary>
        public bool IsIgnoreExport
        {
            get { return isIgnoreExport; }
        }

        /// <summary>
        /// 在分地市运行网络体检前的准备操作(如初始化地市汇总信息)
        /// </summary>
        public virtual void DealBeforeBackgroundQueryByCity()
        {
        }

        /// <summary>
        /// 在分地市运行网络体检后的后续操作(如导出地市汇总信息)
        /// </summary>
        public virtual void DealAfterBackgroundQueryByCity()
        {
        }
        protected static void reportBackgroundInfo(string str)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(str);
        }
        protected static void reportBackgroundError(Exception ex)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
        }
    }
}
