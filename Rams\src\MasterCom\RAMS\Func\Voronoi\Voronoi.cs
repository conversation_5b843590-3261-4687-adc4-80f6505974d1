﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MasterCom.MTGis;
using MapWinGIS;

namespace MasterCom.RAMS.Func.Voronoi
{
    public class Triangle
    {
        public Vertex P1 { get; set; }
        public Vertex P2 { get; set; }
        public Vertex P3 { get; set; }
        // 外接圆心
        public Vertex Center { get; set; }
        // 最大角弧度
        public double MaxRadian { get; set; }
        // 顶点所在角弧度
        public double Radian1 { get; set; }
        public double Radian2 { get; set; }
        public double Radian3 { get; set; }
        // 顶点对边的长度
        public double Edge1 { get; set; }
        public double Edge2 { get; set; }
        public double Edge3 { get; set; }

        public Triangle(Vertex p1, Vertex p2, Vertex p3)
        {
            P1 = p1;
            P2 = p2;
            P3 = p3;
            SetCenter();
            SetEdge();
            SetRadian();
        }

        public List<VTriangle> ConvertToVTriangle()
        {
            List<VTriangle> retList = new List<VTriangle>();
            retList.Add(new VTriangle(P1, P2, P3, Center));
            retList.Add(new VTriangle(P2, P1, P3, Center));
            retList.Add(new VTriangle(P3, P1, P2, Center));
            return retList;
        }

        private void SetCenter()
        {
            double x1 = P1.X, x2 = P2.X, x3 = P3.X;
            double y1 = P1.Y, y2 = P2.Y, y3 = P3.Y;
            double x, y;
            x = ((y2 - y1) * (y3 * y3 - y1 * y1 + x3 * x3 - x1 * x1) - (y3 - y1) * (y2 * y2 - y1 * y1 + x2 * x2 - x1 * x1)) / (2 * (x3 - x1) * (y2 - y1) - 2 * ((x2 - x1) * (y3 - y1)));
            y = ((x2 - x1) * (x3 * x3 - x1 * x1 + y3 * y3 - y1 * y1) - (x3 - x1) * (x2 * x2 - x1 * x1 + y2 * y2 - y1 * y1)) / (2 * (y3 - y1) * (x2 - x1) - 2 * ((y2 - y1) * (x3 - x1)));
            Center = new Vertex(x, y);
        }

        private void SetEdge()
        {
            aSquare = (P2.X - P3.X) * (P2.X - P3.X) + (P2.Y - P3.Y) * (P2.Y - P3.Y);
            bSquare = (P1.X - P3.X) * (P1.X - P3.X) + (P1.Y - P3.Y) * (P1.Y - P3.Y);
            cSquare = (P1.X - P2.X) * (P1.X - P2.X) + (P1.Y - P2.Y) * (P1.Y - P2.Y);
            Edge1 = Math.Sqrt(aSquare);
            Edge2 = Math.Sqrt(bSquare);
            Edge3 = Math.Sqrt(cSquare);
        }

        private void SetRadian()
        {
            Radian1 = Math.Acos((bSquare + cSquare - aSquare) / (2 * Edge2 * Edge3));
            Radian2 = Math.Acos((aSquare + cSquare - bSquare) / (2 * Edge1 * Edge3));
            Radian3 = Math.Acos((aSquare + bSquare - cSquare) / (2 * Edge1 * Edge2));
            MaxRadian = Math.Max(Radian1, Math.Max(Radian2, Radian3));
        }

        private double aSquare;
        private double bSquare;
        private double cSquare;
    }

    /// <summary>
    /// 用于构建泰森多边形的三角形类
    /// </summary>
    public class VTriangle
    {
        public Vertex P1 { get; set; }
        public Vertex P2 { get; set; }
        public Vertex P3 { get; set; }
        public Vertex Center { get; set; }

        /// <summary>
        /// 顶点P1和外心Center的弧度
        /// </summary>
        public double Radian
        {
            get;
            private set;
        }
        
        /// <summary>
        /// 是否包含P2和P3顶点，见构造函数
        /// </summary>
        public bool IsComplete
        {
            get;
            private set;
        }

        /// <summary>
        /// 指示包含该三角形的泰森多边形是否需要被边界切割
        /// </summary>
        public bool IsNeedToClip
        {
            get;
            set;
        }

        /// <summary>
        /// 构建一个完整的三角形
        /// </summary>
        /// <param name="p1"></param>
        /// <param name="p2"></param>
        /// <param name="p3"></param>
        /// <param name="center"></param>
        public VTriangle(Vertex p1, Vertex p2, Vertex p3, Vertex center)
        {
            P1 = p1;
            P2 = p2;
            P3 = p3;
            Center = center;
            Radian = Math.Atan2(P1.Y - Center.Y, P1.X - Center.X);
            IsComplete = true;
            IsNeedToClip = false;
        }

        // 构建一个不完整的三角形
        public VTriangle(Vertex p1, Vertex center)
        {
            P1 = p1;
            Center = center;
            Radian = Math.Atan2(P1.Y - Center.Y, P1.X - Center.X);
            IsComplete = false;
            IsNeedToClip = true;
        }

        public bool IsBorderUpon(VTriangle vt)
        {
            if (!this.IsComplete || !vt.IsComplete || this.P1 != vt.P1)
            {
                return false;
            }
            return this.P2 == vt.P2 || this.P2 == vt.P3 || this.P3 == vt.P2 || this.P3 == vt.P3;
        }
    }

    /// <summary>
    /// 泰森多边形
    /// </summary>
    public class Voronoi
    {
        /// <summary>
        /// 多边形包围的内部点
        /// </summary>
        public Vertex InnerPoint { get; set; }

        /// <summary>
        /// 组成该多边形的三角形列表
        /// </summary>
        public List<VTriangle> VTriangleList { get; set; } = new List<VTriangle>();

        /// <summary>
        /// 多边形各个顶点出现的次数，在进行ComplementSelf之后，该字典失效
        /// </summary>
        public Dictionary<Vertex, int> VertexCountDict { get; set; } = new Dictionary<Vertex, int>();

        /// <summary>
        /// 三角形个数
        /// </summary>
        public int VTriangleCount
        {
            get { return VTriangleList.Count; }
        }

        /// <summary>
        /// 顶点个数，在进行ComplementSelf之后无效
        /// </summary>
        public int VertexCount
        {
            get { return VertexCountDict.Count; }
        }

        /// <summary>
        /// 用于判断是否需要调用ComplementSelf
        /// </summary>
        public bool IsComplete
        {
            get { return VTriangleCount != 0 && VTriangleCount == VertexCount; }
        }

        private List<Vertex[]> vertexList = null;
        public List<Vertex[]> VertexList
        {
            get
            {
                if (vertexList != null)
                {
                    return vertexList;
                }

                List<Vertex[]> retList = new List<Vertex[]>();
                Vertex[] vs = new Vertex[VTriangleList.Count];
                for (int i = 0; i < VTriangleList.Count; ++i)
                {
                    vs[i] = new Vertex(VTriangleList[i].Center.X, VTriangleList[i].Center.Y);
                }
                retList.Add(vs);
                return retList;
            }
            set
            {
                vertexList = value;
            }
        }

        /// <summary>
        /// 添加一个完整的三角形
        /// </summary>
        /// <param name="vt"></param>
        /// <returns></returns>
        public bool AddVTriangle(VTriangle vt)
        {
            if (VTriangleCount == 0)
            {
                VTriangleList.Add(vt);
                InnerPoint = vt.P1;
                VertexCountDict.Add(vt.P2, 1);
                VertexCountDict.Add(vt.P3, 1);
                return true;
            }
            else if (InnerPoint != vt.P1)
            {
                return false;
            }

            int i = 0;
            for (i = 0; i < VTriangleList.Count; ++i)
            {
                if (VTriangleList[i].Radian < vt.Radian)
                {
                    break;
                }
            }
            VTriangleList.Insert(i, vt);

            InnerPoint = vt.P1;
            if (VertexCountDict.ContainsKey(vt.P2))
            {
                ++VertexCountDict[vt.P2];
            }
            else
            {
                VertexCountDict.Add(vt.P2, 1);
            }
            if (VertexCountDict.ContainsKey(vt.P3))
            {
                ++VertexCountDict[vt.P3];
            }
            else
            {
                VertexCountDict.Add(vt.P3, 1);
            }

            return true;
        }

        /// <summary>
        /// 补全（闭合）泰森多边形
        /// </summary>
        public void ComplementSelf()
        {
            VTriangle vt;
            Vertex infPoint1 = new Vertex(0, 0), infPoint2 = new Vertex(0, 0);

            if (VTriangleCount == 0)
            {
                return;
            }
            else if (VTriangleCount == 1)
            {
                vt = VTriangleList[0];
                infPoint1 = GetInfinitePoint(vt.P2, InnerPoint, vt.P3);
                infPoint2 = GetInfinitePoint(vt.P3, InnerPoint, vt.P2);
            }
            else
            {
                SortVTriangleList();

                vt = VTriangleList[0];
                if (VertexCountDict[vt.P2] == 1)
                {
                    infPoint1 = GetInfinitePoint(vt.P2, InnerPoint, vt.P3);
                }
                else if (VertexCountDict[vt.P3] == 1)
                {
                    infPoint1 = GetInfinitePoint(vt.P3, InnerPoint, vt.P2);
                }

                vt = VTriangleList[VTriangleList.Count - 1];
                if (VertexCountDict[vt.P2] == 1)
                {
                    infPoint2 = GetInfinitePoint(vt.P2, InnerPoint, vt.P3);
                }
                else if (VertexCountDict[vt.P3] == 1)
                {
                    infPoint2 = GetInfinitePoint(vt.P3, InnerPoint, vt.P2);
                }
            }

            VTriangleList.Insert(0, new VTriangle(InnerPoint, infPoint1));
            VTriangleList.Add(new VTriangle(InnerPoint, infPoint2));
        }

        private void SortVTriangleList()
        {
            if (VTriangleCount <= 2 || this.IsComplete)
            {
                return;
            }

            while (VTriangleList[0].IsBorderUpon(VTriangleList[VTriangleList.Count - 1]))
            {
                VTriangle vt = VTriangleList[0];
                VTriangleList.RemoveAt(0);
                VTriangleList.Add(vt);
            }
        }

        private Vertex GetInfinitePoint(Vertex p1, Vertex p2, Vertex p)
        {
            double inf = 20000;
            Vertex tmp1, tmp2;

            if (p1.X == p2.X)
            {
                double y = (p1.Y + p2.Y) / 2;
                tmp1 = new Vertex(p1.X + inf, y);
                tmp2 = new Vertex(p1.X - inf, y);
            }
            else if (p1.Y == p2.Y)
            {
                double x = (p1.X + p2.X) / 2;
                tmp1 = new Vertex(x, p1.Y + inf);
                tmp2 = new Vertex(x, p1.Y - inf);
            }
            else
            {
                double k = (p1.X - p2.X) / (p2.Y - p1.Y); // 与p1,p2直线垂直的直线斜率
                double x = (p1.X + p2.X) / 2;
                double y = (p1.Y + p2.Y) / 2;

                double sqrt = Math.Sqrt(inf * inf / (k * k + 1));
                double x1 = x + sqrt;
                double y1 = k * (x1 - x) + y;
                double x2 = x - sqrt;
                double y2 = k * (x2 - x) + y;

                tmp1 = new Vertex(x1, y1);
                tmp2 = new Vertex(x2, y2);
            }

            double disP1 = (tmp1.X - p.X) * (tmp1.X - p.X) + (tmp1.Y - p.Y) * (tmp1.Y - p.Y);
            double disP2 = (tmp2.X - p.X) * (tmp2.X - p.X) + (tmp2.Y - p.Y) * (tmp2.Y - p.Y);
            return disP1 > disP2 ? tmp1 : tmp2;
        }
    }

    /// <summary>
    /// 泰森多边形构建器
    /// </summary>
    public class VoronoiBuilder
    {
        // 允许组合到泰森多边形中的三角形的最大角度
        public double MaxAngle { get; set; }
        // 当构建泰森多边形的点数少于该变量值的时候使用边界精确切割
        public int ClipAllLessCount { get; set; }
        public string ErrorText { get; private set; }
        public bool IsErrorOccur { get; private set; }

        public VoronoiBuilder()
        {
            Border = new VoronoiBorder();
            ClipAllLessCount = 1000;
            MaxAngle = 179;
            ErrorText = Border.ErrorText;
            IsErrorOccur = Border.IsErrorOccur;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="points">需要建立泰森多边形的点，不能出现重复</param>
        /// <param name="voiBorder"></param>
        /// <returns>输入点对应的泰森多边形（被切割后多边形可能有多个部分）</returns>
        public Dictionary<Vertex, List<Vertex[]>> Construct(List<Vertex> points, VoronoiBorder voiBorder)
        {
            Border = voiBorder;
            if (Border.IsErrorOccur)
            {
                return ErrorReturn(Border.ErrorText);
            }
            IsErrorOccur = Border.IsErrorOccur;
            ErrorText = Border.ErrorText;

            return Construct(points);
        }

        private Dictionary<Vertex, List<Vertex[]>> Construct(List<Vertex> points)
        {
            CTriangle cTriangle = new CTriangle();
            List<Vertex> cTriangleResult = cTriangle.Triangulate(points);
            isClipAll = points.Count < ClipAllLessCount;
            List<VTriangle> vTriangleList = GetVTriangleList(cTriangleResult);

            Dictionary<Vertex, Voronoi> vvDict = ConstructVoronoi(vTriangleList);
            CorrectVoronoi(vvDict);
            if (IsErrorOccur)
            {
                return ErrorReturn(ErrorText);
            }

            Dictionary<Vertex, List<Vertex[]>> retDict = new Dictionary<Vertex, List<Vertex[]>>();
            foreach (Vertex vkey in vvDict.Keys)
            {
                retDict.Add(vkey, vvDict[vkey].VertexList);
            }

            return retDict;
        }

        /// <summary>
        /// 切割判断和角度过滤
        /// </summary>
        /// <param name="cTriangleList"></param>
        /// <returns></returns>
        private List<VTriangle> GetVTriangleList(List<Vertex> cTriangleList)
        {
            List<VTriangle> vTriangleList = new List<VTriangle>();
            for (int i = 0; i < cTriangleList.Count; i += 3)
            {
                bool needToClip = false;
                Triangle triangle = new Triangle(
                    cTriangleList[i], cTriangleList[i + 1], cTriangleList[i + 2]);

                if (triangle.MaxRadian > MaxAngle * Math.PI / 180)
                {
                    continue;
                }

                if (isClipAll || (triangle.MaxRadian > Math.PI / 2
                    && !Border.BorderMop2.CheckPointInRegion(triangle.Center.X, triangle.Center.Y)))
                {
                    needToClip = true;
                }

                List<VTriangle> threeVTriangle = triangle.ConvertToVTriangle();
                foreach (VTriangle vt in threeVTriangle)
                {
                    vt.IsNeedToClip = needToClip;
                }
                vTriangleList.AddRange(threeVTriangle);
            }
            return vTriangleList;
        }
        
        /// <summary>
        /// 初步构建泰森多边形
        /// </summary>
        /// <param name="vTriangleList"></param>
        /// <returns></returns>
        private Dictionary<Vertex, Voronoi> ConstructVoronoi(List<VTriangle> vTriangleList)
        {
            Dictionary<Vertex, Voronoi> vvDict = new Dictionary<Vertex, Voronoi>();
            foreach (VTriangle vt in vTriangleList)
            {
                if (!vvDict.ContainsKey(vt.P1))
                {
                    vvDict.Add(vt.P1, new Voronoi());
                }
                vvDict[vt.P1].AddVTriangle(vt);
            }
            return vvDict;
        }

        /// <summary>
        /// 泰森多边形补全和切割
        /// </summary>
        /// <param name="vvDict"></param>
        private void CorrectVoronoi(Dictionary<Vertex, Voronoi> vvDict)
        {
            CPolygonClipper clipper = new CPolygonClipper();
            clipper.DoCacheForClipper(Border.BorderArrayList);
            foreach (Voronoi voi in vvDict.Values)
            {
                if (!voi.IsComplete)
                {
                    voi.ComplementSelf();
                }

                foreach (VTriangle vt in voi.VTriangleList)
                {
                    if (!vt.IsNeedToClip)
                    {
                        continue;
                    }
                    List<Vertex[]> retList = clipper.ClipPolygon(voi.VertexList);
                    if (retList.Count == 0)
                    {
                        ErrorText = "ClipPolygon Return Unexpected Result!";
                        IsErrorOccur = true;
                        clipper.ClearCache();
                        return;
                    }
                    voi.VertexList = retList;
                    break;
                }
            }
            clipper.ClearCache();
        }

        private Dictionary<Vertex, List<Vertex[]>> ErrorReturn(string errText)
        {
            ErrorText = errText;
            IsErrorOccur = true;
            return new Dictionary<Vertex, List<Vertex[]>>();
        }

        private VoronoiBorder Border;
        private bool isClipAll;
    }

    /// <summary>
    /// 泰森多边形切割边界
    /// </summary>
    public class VoronoiBorder
    {
        // 边界进行模糊处理后保留的点数（约等于）
        public int ReserveCount { get; set; }
        public string ErrorText { get; private set; }
        public bool IsErrorOccur { get; private set; }

        /// <summary>
        /// 用于判断在该边界内的点
        /// </summary>
        public MapOperation2 BorderMop2 { get; private set; }
        public List<Vertex[]> BorderArrayList { get; private set; }

        /// <summary>
        /// BorderShape可以是非法的，无闭合或者自相交，都可以接受
        /// </summary>
        public Shape BorderShape { get; private set; }
       
        public VoronoiBorder()
        {
            ErrorText = "Border Is Empty";
            IsErrorOccur = true;
            BorderMop2 = new MapOperation2();
            BorderArrayList = new List<Vertex[]>();
            BorderShape = new Shape();
            BorderShape.ShapeType = ShpfileType.SHP_POLYGON;
            ReserveCount = 1000;
        }

        /// <summary>
        /// 设置边界
        /// </summary>
        /// <param name="origShape"></param>
        /// <returns></returns>
        public bool SetBorder(Shape origShape)
        {
            if (origShape == null || origShape.ShapeType != ShpfileType.SHP_POLYGON
                || origShape.numPoints < 4)
            {
                ErrorText = "Border Shape is Empty or Too Few Points";
                IsErrorOccur = true;
                return !IsErrorOccur;
            }

            Shape fixedShape = null;
            if (!origShape.IsValid)
            {
                origShape.FixUp(out fixedShape);
                origShape = fixedShape;
            }
            if (origShape == null || !origShape.IsValid)
            {
                ErrorText = "Border Shape Invalid and Fix Up Failed";
                IsErrorOccur = true;
                return !IsErrorOccur;
            }

            Shape newShape = UnionShapePart(origShape);
            if (newShape == null)
            {
                ErrorText = "Shape Parts Union Operation Failed";
                IsErrorOccur = true;
                return !IsErrorOccur;
            }

            BlurBorderShape(newShape);
            return !IsErrorOccur;
        }

        /// <summary>
        /// 设置边界
        /// </summary>
        /// <param name="shp"></param>
        /// <returns></returns>
        public bool SetBorder(Shapefile shp)
        {
            if (shp == null || shp.ShapefileType != ShpfileType.SHP_POLYGON
                || shp.NumShapes < 1)
            {
                ErrorText = "Border Shapefile Invalid";
                IsErrorOccur = true;
                return !IsErrorOccur;
            }

            Shapefile fixedShp = null;
            shp.FixUpShapes(out fixedShp);
            if (fixedShp == null)
            {
                ErrorText = "Shapes In Shapefile Invalid And Fix Up Failed";
                IsErrorOccur = true;
                return !IsErrorOccur;
            }

            Shape newShape = fixedShp.get_Shape(0);
            for (int i = 1; i < fixedShp.NumShapes; ++i)
            {
                Shape curShape = fixedShp.get_Shape(i);
                newShape = newShape.Clip(curShape, tkClipOperation.clUnion);
                if (newShape == null)
                {
                    ErrorText = "Shapefile Union Operation Failed";
                    IsErrorOccur = true;
                    return !IsErrorOccur;
                }
            }

            BlurBorderShape(newShape);
            return !IsErrorOccur;
        }

        /// <summary>
        /// 设置边界
        /// </summary>
        /// <param name="shpFilePath"></param>
        /// <returns></returns>
        public bool SetBorder(string shpFilePath)
        {
            Shapefile shp = new Shapefile();
            if (!shp.Open(shpFilePath, null))
            {
                ErrorText = "打开图层失败";
                IsErrorOccur = true;
                return !IsErrorOccur;
            }

            bool retVal = SetBorder(shp);
            shp.Close();
            return retVal;
        }

        /// <summary>
        /// 边界模糊处理，处理后的边界Shape可能出现自相交
        /// </summary>
        /// <param name="validShape"></param>
        private void BlurBorderShape(Shape validShape)
        {
            int blurStep = validShape.numPoints / ReserveCount + 1;
            int partCount = BorderShape.NumParts;
            int pointCount = BorderShape.numPoints;

            for (int i = 0; i < validShape.NumParts; ++i)
            {
                List<Vertex> vList = new List<Vertex>();
                List<DbPoint> pnts = ShapeHelper.GetPartShapePoints(validShape, i);
                if (pnts == null)
                {
                    continue;
                }
                int step = pnts.Count / blurStep >= 3 ? blurStep : 1;
                for (int j = 0; j < pnts.Count; j += step)
                {
                    DbPoint pnt = pnts[j];
                    vList.Add(new Vertex(pnt.x, pnt.y));
                }

                BorderShape.InsertPart(pointCount, ref partCount);
                foreach (Vertex v in vList)
                {
                    MapWinGIS.Point pt = new MapWinGIS.Point();
                    pt.x = v.X;
                    pt.y = v.Y;
                    BorderShape.InsertPoint(pt, ref pointCount);
                    ++pointCount;
                }
                ++partCount;
                BorderArrayList.Add(vList.ToArray());
            }

            BorderMop2.FillPolygon(BorderShape);
            ErrorText = "";
            IsErrorOccur = false;
        }

        /// <summary>
        /// shape的各个部分尝试进行合并
        /// </summary>
        /// <param name="origShape"></param>
        /// <returns></returns>
        private Shape UnionShapePart(Shape origShape)
        {
            Shape resultShape = ShapeHelper.GetPartAsShape(origShape, 0);
            for (int i = 1; i < origShape.NumParts; ++i)
            {
                Shape partShp = ShapeHelper.GetPartAsShape(origShape, i);
                if (partShp == null)
                {
                    continue;
                }
                resultShape = resultShape.Clip(partShp, tkClipOperation.clUnion);
                if (resultShape == null)
                {
                    break;
                }
            }
            return resultShape;
        }
    }
}
