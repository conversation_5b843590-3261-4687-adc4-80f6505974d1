using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class EventResult:Event
    {
        public int taskId { get; set; }
        public int anaTime { get; set; }
        public int status { get; set; }
        public string preTypeDesc { get; set; } = "";
        public string reasonDesc { get; set; } = "";
        public string solutionDesc { get; set; } = "";

        internal void Fill(List<ColumnDefItem> cols, Net.Content content)
        {
            FileInfo fi = new Model.FileInfo();
            foreach (ColumnDefItem cdf in cols)
            {
                setColsInfo(content, fi, cdf);
            }
            fi.Name = content.GetParamString();
            fi.ServiceType = content.GetParamInt();
            fi.SampleTbName = content.GetParamString();
            fi.LogTable = string.Format("tb_log_file_{0}_{1:D2}", DateTime.Year, DateTime.Month);
            this.ApplyHeader(fi);
        }

        private void setColsInfo(Net.Content content, FileInfo fi, ColumnDefItem cdf)
        {
            switch (cdf.showName)
            {
                case "FileID":
                    FileID = content.GetParamInt();
                    fi.ID = FileID;
                    break;
                case "ProjectType":
                    int projectID = content.GetParamInt();
                    fi.ProjectID = projectID;
                    break;
                case "SeqID":
                    SN = content.GetParamInt();
                    break;
                case "Time":
                    Time = content.GetParamInt();
                    break;
                case "TimeMS":
                    Millisecond = content.GetParamShort();
                    break;
                case "bms":
                    MS = content.GetParamByte();
                    break;
                case "EventID":
                    ID = content.GetParamInt();
                    break;
                case "Longitude":
                    Longitude = content.GetParamDouble();
                    break;
                case "Latitude":
                    Latitude = content.GetParamDouble();
                    break;
                case "CqtPosID":
                    content.GetParamInt();
                    break;
                case "TaskID":
                    taskId = content.GetParamInt();
                    break;
                case "AnaTime":
                    anaTime = content.GetParamInt();
                    break;
                case "Status":
                    status = content.GetParamInt();
                    break;
                case "PretypeDesc":
                    preTypeDesc = content.GetParamString();
                    break;
                case "ReasonDesc":
                    reasonDesc = content.GetParamString();
                    break;
                case "SolutionDesc":
                    solutionDesc = content.GetParamString();
                    break;
                default:
                    setOrtherCol(content, cdf);
                    break;
            }
        }

        private void setOrtherCol(Net.Content content, ColumnDefItem cdf)
        {
            switch (cdf.vType)
            {
                case E_VType.E_Int:
                    {
                        int datav = content.GetParamInt();
                        this[cdf.showName] = datav;
                    }
                    break;
                case E_VType.E_Short:
                    {
                        short datav = content.GetParamShort();
                        this[cdf.showName] = datav;
                    }
                    break;
                case E_VType.E_Byte:
                    {
                        byte datav = content.GetParamByte();
                        this[cdf.showName] = datav;
                    }
                    break;
                case E_VType.E_Float:
                case E_VType.E_IntFloat:
                    {
                        float datav = content.GetParamFloat();
                        this[cdf.showName] = datav;
                    }
                    break;
                case E_VType.E_String:
                    {
                        string datav = content.GetParamString();
                        this[cdf.showName] = datav;
                    }
                    break;
                case E_VType.E_UInt64:
                    {
                        UInt64 datav = content.GetParamUInt64();
                        this[cdf.showName] = datav;
                    }
                    break;
                case E_VType.E_Int64:
                    {
                        Int64 datav = content.GetParamInt64();
                        this[cdf.showName] = datav;
                    }
                    break;
                default:
                    break;
            }
        }
    }
}
