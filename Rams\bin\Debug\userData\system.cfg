<?xml version="1.0"?>
<Configs>
  <Config name="Sample">
    <Item name="IsMoveCenterEveryTime" typeName="Boolean">True</Item>
  </Config>
  <Config name="CellRelevance">
    <Item name="DistLimit" typeName="Boolean">False</Item>
    <Item name="IsOnlyServLine" typeName="Boolean">False</Item>
    <Item name="BNBLineTopN" typeName="Boolean">True</Item>
    <Item name="NBLineTopN" typeName="Int32">6</Item>
    <Item name="NBLineDValue" typeName="Int32">12</Item>
    <Item name="TDCellRelevanceType" typeName="Int32">0</Item>
    <Item name="TDDistance" typeName="Int32">5000</Item>
    <Item name="GSMDistance" typeName="Int32">3000</Item>
    <Item name="WDistance" typeName="Int32">300000</Item>
    <Item name="LTEDistance" typeName="Int32">3000</Item>
    <Item name="NRDistance" typeName="Int32">2000</Item>
    <Item name="IsBtsLngLat" typeName="Boolean">True</Item>
    <Item name="NBCellGetFromConfig" typeName="Boolean">False</Item>
    <Item name="IsDisplayHisCell" typeName="Boolean">True</Item>
  </Config>
  <Config name="CellSelection">
    <Item name="IsSimpleSelectCell" typeName="Boolean">True</Item>
  </Config>
  <Config name="BSIC">
    <Item name="BSIC10" typeName="Boolean">False</Item>
  </Config>
  <Config name="Attenuation">
    <Item name="Attenuationint" typeName="Int32">0</Item>
  </Config>
  <Config name="LTEScanWorkMode">
    <Item name="LTEScanWorkMode" typeName="Int32">0</Item>
  </Config>
  <Config name="System">
    <Item name="IsBackground" typeName="Boolean">False</Item>
    <Item name="TitleName" typeName="String" />
    <Item name="MultiSqlDelayMs" typeName="Int32">300</Item>
  </Config>
  <Config name="ConfigParames">
    <Item name="MasterCom.RAMS.Func.TestPointArrowManager" typeName="IDictionary">
      <Item typeName="Boolean" key="IsByDistance">True</Item>
      <Item typeName="Boolean" key="IsNeedLength">True</Item>
      <Item typeName="Int32" key="ArrowInterfCount">3</Item>
      <Item typeName="Int32" key="ArrowCount">10</Item>
      <Item typeName="Int32" key="ArrowLength">30</Item>
      <Item typeName="Int32" key="ArrowWidth">2</Item>
      <Item typeName="Double" key="ArrowOffset">10</Item>
      <Item typeName="Color" key="ArrowColor">255,148,0,211</Item>
    </Item>
    <Item name="MapSetting" typeName="IDictionary">
      <Item typeName="IDictionary" key="1">
        <Item typeName="IDictionary" key="2">
          <Item typeName="String" key="MapPath">E:\Project\Rams\bin\Debug\GEOGRAPHIC\区县分公司_region.shp</Item>
          <Item typeName="String" key="MapName">区县分公司_region</Item>
          <Item typeName="String" key="ColumnName">NAME</Item>
        </Item>
        <Item typeName="IDictionary" key="4">
          <Item typeName="String" key="MapPath">E:\Project\Rams\bin\Debug\GEOGRAPHIC\网络部网格图层_region.shp</Item>
          <Item typeName="String" key="MapName">网络部网格图层_region</Item>
          <Item typeName="String" key="ColumnName">NAME</Item>
        </Item>
        <Item typeName="IDictionary" key="3">
          <Item typeName="String" key="MapPath">E:\Project\Rams\bin\Debug\GEOGRAPHIC\LTE厂家分界图层_region.shp</Item>
          <Item typeName="String" key="MapName">LTE厂家分界图层_region</Item>
          <Item typeName="String" key="ColumnName">厂家</Item>
        </Item>
      </Item>
      <Item typeName="IList" key="RoadRevanceOptions">
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="DistrictID">1</Item>
          <Item typeName="String" key="RoadRelevanceType">ByMatrix</Item>
          <Item typeName="Boolean" key="IsAllRoadLayer">True</Item>
          <Item key="RoadLayerName" />
          <Item typeName="Int32" key="RoadNameFieldIdx">-1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="DistrictID">3</Item>
          <Item typeName="String" key="RoadRelevanceType">ByMatrix</Item>
          <Item typeName="Boolean" key="IsAllRoadLayer">True</Item>
          <Item key="RoadLayerName" />
          <Item typeName="Int32" key="RoadNameFieldIdx">-1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="Int32" key="DistrictID">2</Item>
          <Item typeName="String" key="RoadRelevanceType">ByMapWinGis</Item>
        </Item>
      </Item>
    </Item>
  </Config>
  <Config name="Voronoi">
    <Item name="北京">
      <Item name="BlurBorderCount" typeName="Int32">1000</Item>
      <Item name="ClipAllCount" typeName="Int32">1000</Item>
      <Item name="MaxAngle" typeName="Double">179</Item>
      <Item name="DefaultBorderPath" typeName="String" />
    </Item>
  </Config>
</Configs>