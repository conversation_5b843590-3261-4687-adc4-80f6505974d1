﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Model
{
    public partial class CellParamDBSettingDlg : BaseForm
    {
        public CellParamDBSettingDlg(SqlConnectionStringBuilder sb)
        {
            InitializeComponent();
            if (sb!=null)
            {
                int splitIdx=sb.DataSource.IndexOf(',');
                txtDB.Text = sb.DataSource.Substring(0, splitIdx + 1);
                int port;
                if (int.TryParse(sb.DataSource.Substring(splitIdx, sb.DataSource.Length - splitIdx), out port))
                {
                    numPort.Value = port;
                }
                txtUsrName.Text = sb.UserID;
                txtPW.Text = sb.Password;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (isEmptyContent(txtDB))
            {
                MessageBox.Show("数据库地址不能为空！");
                return;
            }
            if (isEmptyContent(txtUsrName))
            {
                MessageBox.Show("请输入登录名！");
                return;
            }
            if (isEmptyContent(txtPW))
            {
                MessageBox.Show("请输入密码！");
                return;
            }
            sb = new SqlConnectionStringBuilder();
            sb.DataSource = txtDB.Text.Trim() + "," + numPort.Value.ToString();
            sb.UserID = txtUsrName.Text;
            sb.Password = txtPW.Text;
            if (tryConnect(sb))
            {
                DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show("连接数据库失败！");
            }
        }
        SqlConnectionStringBuilder sb = null;
        public SqlConnectionStringBuilder ConnSB
        {
            get { return sb; }
        }

        private bool tryConnect(SqlConnectionStringBuilder cnSb)
        {
            SqlConnection conn = new SqlConnection(cnSb.ToString());
            try
            {
                conn.Open();
                return conn.State == ConnectionState.Open;
            }
            catch
            {
                return false;
            }
            finally
            {
                conn.Dispose();
            }
        }

        private bool isEmptyContent(TextBox txt)
        {
            return txt.Text.Trim().Length == 0;
        }
        

    }
}
