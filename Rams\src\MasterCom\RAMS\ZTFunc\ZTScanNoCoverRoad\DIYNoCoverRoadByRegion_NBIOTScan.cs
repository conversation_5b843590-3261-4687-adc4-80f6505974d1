﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.MTGis;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class DIYNoCoverRoadByRegion_NBIOTScan : DIYNoCoverRoadByRegion_TDLTEScan
    {
        private static DIYNoCoverRoadByRegion_NBIOTScan intance = null;
        public new static DIYNoCoverRoadByRegion_NBIOTScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new DIYNoCoverRoadByRegion_NBIOTScan();
                    }
                }
            }
            return intance;
        }

        protected DIYNoCoverRoadByRegion_NBIOTScan()
            : base()
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.SCAN_NBIOT_TOPN);
            carrierID = CarrierType.ChinaMobile;
            IsAddAllOtherParameter = false;
        }

        public override string Name
        {
            get { return "弱覆盖路段分析_NBIOT扫频"; }
        }

        protected override bool validTestPoint(TestPoint tp)
        {
            if (tp is ScanTestPoint_NBIOT)
            {
                float? rxLev = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", 0];
                if (rxLev != null && rxLev <= rxLevThreshold)
                {
                    return true;
                }
            }
            return false;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33004, this.Name);
        }
    }
}
