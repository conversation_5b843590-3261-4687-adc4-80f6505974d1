<?xml version="1.0"?>
<Configs>
  <Config name="StatReports">
    <Item name="reports" typeName="IList">
      <Item typeName="ReporterTemplate">
        <Item name="Param" typeName="IDictionary">
          <Item typeName="String" key="Name">TD数据业务_月表</Item>
          <Item typeName="Int32" key="KeyCount">2</Item>
          <Item typeName="Int32" key="TimeShowType">1</Item>
          <Item typeName="IList" key="Columns">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">所属项目</Item>
              <Item typeName="String" key="Exp">{kProjId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">所属项目</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">日期</Item>
              <Item typeName="String" key="Exp">{kTimeValue}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">日期</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试距离(公里)</Item>
              <Item typeName="String" key="Exp">{Tx_0806/1000}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">测试距离(公里)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试时长(分钟)</Item>
              <Item typeName="String" key="Exp">{Tx_0805/60000}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">测试时长(分钟)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均时速(公里/小时)</Item>
              <Item typeName="String" key="Exp">{Tx_0806*3600/Tx_0805 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">90覆盖率(%)</Item>
              <Item typeName="String" key="Exp">{100*(Tx_710533+Tx_710534+Tx_710535+Tx_710536+Tx_710537)/Tx_71053C}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">90覆盖率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">94覆盖率(%)</Item>
              <Item typeName="String" key="Exp">{100*(Tx_710533+Tx_710534+Tx_710535+Tx_710536+Tx_710537+Tx_710538)/Tx_71053C }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">94覆盖率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均RLC吞吐量(Kbps)</Item>
              <Item typeName="String" key="Exp">{(Tx_03050102+Tx_03050202+Tx_03050302+Tx_03050402+Tx_03050502+Tx_03050802)/((Tx_03050101+Tx_03050201+Tx_03050301+Tx_03050401+Tx_03050501+Tx_03050801)*1024) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">平均RLC吞吐量(Kbps)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均BLER(%)</Item>
              <Item typeName="String" key="Exp">{(Tx_04050102+Tx_04050202+Tx_04050302+Tx_04050402+Tx_04050502+Tx_04050802)/(1*(Tx_04050101+Tx_04050201+Tx_04050301+Tx_04050401+Tx_04050501+Tx_04050801)) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">平均BLER(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">FTP下载成功次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[57]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">FTP下载成功次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">FTP下载失败次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[58]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">FTP下载失败次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">FTP下载成功率(%)</Item>
              <Item typeName="String" key="Exp">{100*evtIdCount[57]/(evtIdCount[57]+evtIdCount[58]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">FTP下载成功率(%)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">FTP下载速率(Kbps)</Item>
              <Item typeName="String" key="Exp">{(Tx_050501020101+Tx_050502020101+Tx_050503020101+Tx_050504020101+Tx_050505020101+Tx_050508020101)*(1000*8)/((Tx_050501020102+Tx_050502020102+Tx_050503020102+Tx_050504020102+Tx_050505020102+Tx_050508020102)*1024) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">FTP下载速率(Kbps)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">RAU成功次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[27]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">RAU成功次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">RAU失败次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[28]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">RAU失败次数</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均RAU间隔时长(分钟/次)</Item>
              <Item typeName="String" key="Exp">{(Tx_0805)/((evtIdCount[131]+evtIdCount[134])*60000)}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">平均RAU间隔时长(分钟/次)</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均RAU间隔距离(公里/次)</Item>
              <Item typeName="String" key="Exp">{(Tx_0806)/((evtIdCount[131]+evtIdCount[134])*1000)}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">平均RAU间隔距离(公里/次)</Item>
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>