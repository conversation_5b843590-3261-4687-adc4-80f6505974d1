﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTRtpPacketsLostShowForm_NR
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.gridViewMessageInfo = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn55 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn52 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn51 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn53 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn54 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControlRTP = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuRTP = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportRTPExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowAllRTPNode = new System.Windows.Forms.ToolStripMenuItem();
            this.miHideAllRTPNode = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewRTP = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewMessageInfo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRTP)).BeginInit();
            this.ctxMenuRTP.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRTP)).BeginInit();
            this.SuspendLayout();
            // 
            // gridViewMessageInfo
            // 
            this.gridViewMessageInfo.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn55,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn38,
            this.gridColumn52,
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn50,
            this.gridColumn44,
            this.gridColumn51,
            this.gridColumn45,
            this.gridColumn48,
            this.gridColumn49,
            this.gridColumn53,
            this.gridColumn54});
            this.gridViewMessageInfo.GridControl = this.gridControlRTP;
            this.gridViewMessageInfo.Name = "gridViewMessageInfo";
            this.gridViewMessageInfo.OptionsBehavior.Editable = false;
            this.gridViewMessageInfo.OptionsDetail.ShowDetailTabs = false;
            this.gridViewMessageInfo.OptionsView.ColumnAutoWidth = false;
            this.gridViewMessageInfo.OptionsView.ShowDetailButtons = false;
            this.gridViewMessageInfo.OptionsView.ShowGroupPanel = false;
            this.gridViewMessageInfo.DoubleClick += new System.EventHandler(this.gridViewMessageInfo_DoubleClick);
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "编号";
            this.gridColumn2.FieldName = "SN";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 0;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "文件区域";
            this.gridColumn3.FieldName = "Area";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 1;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "预存区域";
            this.gridColumn4.FieldName = "Grid";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 2;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "道路名称";
            this.gridColumn5.FieldName = "RoadName";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 3;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "起呼时间（completetime）";
            this.gridColumn6.FieldName = "SCallCompleteTimeStr";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 4;
            this.gridColumn6.Width = 169;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "起呼时间（handsetstime）";
            this.gridColumn7.FieldName = "SCallTimeStr";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 5;
            this.gridColumn7.Width = 172;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "丢包方向";
            this.gridColumn8.FieldName = "Direction";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 6;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "丢包起点时间（computertime）";
            this.gridColumn9.FieldName = "SLossCompleteTimeStr";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 7;
            this.gridColumn9.Width = 196;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "丢包起点时间（handsetstime）";
            this.gridColumn10.FieldName = "SLossTimeStr";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 8;
            this.gridColumn10.Width = 190;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "经度";
            this.gridColumn11.FieldName = "Longitude";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 9;
            this.gridColumn11.Width = 97;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "纬度";
            this.gridColumn12.FieldName = "Latitude";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 10;
            this.gridColumn12.Width = 96;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "RTP丢包数";
            this.gridColumn13.FieldName = "LossNumber";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 11;
            this.gridColumn13.Width = 78;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "丢包持续时间(秒)(handsetstime)";
            this.gridColumn14.FieldName = "LossTime";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 12;
            this.gridColumn14.Width = 206;
            // 
            // gridColumn55
            // 
            this.gridColumn55.Caption = "丢包原因";
            this.gridColumn55.FieldName = "LossReason";
            this.gridColumn55.Name = "gridColumn55";
            this.gridColumn55.Visible = true;
            this.gridColumn55.VisibleIndex = 12;
            this.gridColumn55.Width = 206;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "MOS打分时间(computertime)";
            this.gridColumn15.FieldName = "MosTime";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 13;
            this.gridColumn15.Width = 189;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "当前MOS值(computertime)";
            this.gridColumn16.FieldName = "MosVal";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 14;
            this.gridColumn16.Width = 177;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "平均MOS值(computertime)";
            this.gridColumn17.FieldName = "MosAveVal";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 15;
            this.gridColumn17.Width = 175;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "丢包前占用的小区";
            this.gridColumn18.FieldName = "LossCellName";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 16;
            this.gridColumn18.Width = 121;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "丢包前占用的小区RSRP";
            this.gridColumn19.FieldName = "RSRP";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 17;
            this.gridColumn19.Width = 146;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "丢包前占用的小区SINR";
            this.gridColumn20.FieldName = "SINR";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 18;
            this.gridColumn20.Width = 148;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "丢包前占用的小区路损";
            this.gridColumn21.FieldName = "PathLoss";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 19;
            this.gridColumn21.Width = 148;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "丢包前占用的小区pdsch_bler";
            this.gridColumn22.FieldName = "Pdsch_bler";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 20;
            this.gridColumn22.Width = 180;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "丢包前占用的小区pusch_bler";
            this.gridColumn23.FieldName = "Pusch_bler";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 21;
            this.gridColumn23.Width = 185;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "丢包前占用的小区uetxpower";
            this.gridColumn24.FieldName = "Uetxpower";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 22;
            this.gridColumn24.Width = 180;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "丢包前占用的小区RB占用数";
            this.gridColumn25.FieldName = "RBCount";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 23;
            this.gridColumn25.Width = 175;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "丢包前占用的频点EARFCN";
            this.gridColumn26.FieldName = "EARFCN";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 24;
            this.gridColumn26.Width = 166;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "PDCCH_UL_Grant_Count";
            this.gridColumn27.FieldName = "PDCCH_UL_Grant_Count";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 25;
            this.gridColumn27.Width = 160;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "PDCCH_DL_Grant_Count";
            this.gridColumn28.FieldName = "PDCCH_DL_Grant_Count";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 26;
            this.gridColumn28.Width = 160;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "MCSCode0_DL";
            this.gridColumn29.FieldName = "MCSCode0_DL";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 27;
            this.gridColumn29.Width = 101;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "MCSCode1_DL";
            this.gridColumn30.FieldName = "MCSCode1_DL";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 28;
            this.gridColumn30.Width = 102;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "MCS_UL";
            this.gridColumn31.FieldName = "MCS_UL";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 29;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "PDSCH_Code0_BLER";
            this.gridColumn32.FieldName = "PDSCH_Code0_BLER";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 30;
            this.gridColumn32.Width = 141;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "PDSCH_Code1_BLER";
            this.gridColumn33.FieldName = "PDSCH_Code1_BLER";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 31;
            this.gridColumn33.Width = 137;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "PDCCH_CCE_Start";
            this.gridColumn34.FieldName = "PDCCH_CCE_Start";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 32;
            this.gridColumn34.Width = 126;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "PDCCH_CCEs_Number";
            this.gridColumn35.FieldName = "PDCCH_CCEs_Number";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 33;
            this.gridColumn35.Width = 145;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "上行QPSK(%)";
            this.gridColumn36.FieldName = "Times_QPSK_UL";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 34;
            this.gridColumn36.Width = 114;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "上行QAM16(%)";
            this.gridColumn37.FieldName = "Times_QAM16_UL";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 35;
            this.gridColumn37.Width = 114;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "上行QAM64(%)";
            this.gridColumn38.FieldName = "Times_QAM64_UL";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 36;
            this.gridColumn38.Width = 114;
            // 
            // gridColumn52
            // 
            this.gridColumn52.Caption = "上行QAM256(%)";
            this.gridColumn52.FieldName = "Times_QAM64_DLCode1";
            this.gridColumn52.Name = "gridColumn52";
            this.gridColumn52.Visible = true;
            this.gridColumn52.VisibleIndex = 37;
            this.gridColumn52.Width = 114;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "PUSCH_prb_num_slot";
            this.gridColumn39.FieldName = "PUSCH_PRb_Num_slot";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 38;
            this.gridColumn39.Width = 143;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "上行初传BLER";
            this.gridColumn40.FieldName = "PUSCH_Initial_BLER";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 39;
            this.gridColumn40.Width = 101;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "TM传输模式";
            this.gridColumn41.FieldName = "Transmission_Mode";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 40;
            this.gridColumn41.Width = 92;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "rank";
            this.gridColumn42.FieldName = "Rank";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 41;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "下行BPSK(%)";
            this.gridColumn43.FieldName = "Times_QPSK_DLCode0";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 42;
            this.gridColumn43.Width = 114;
            // 
            // gridColumn50
            // 
            this.gridColumn50.Caption = "下行QPSK(%)";
            this.gridColumn50.FieldName = "Times_QPSK_DLCode1";
            this.gridColumn50.Name = "gridColumn50";
            this.gridColumn50.Visible = true;
            this.gridColumn50.VisibleIndex = 43;
            this.gridColumn50.Width = 114;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "下行QAM16(%)";
            this.gridColumn44.FieldName = "Times_QAM16_DLCode0";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 44;
            this.gridColumn44.Width = 114;
            // 
            // gridColumn51
            // 
            this.gridColumn51.Caption = "下行QAM64(%)";
            this.gridColumn51.FieldName = "Times_QAM16_DLCode1";
            this.gridColumn51.Name = "gridColumn51";
            this.gridColumn51.Visible = true;
            this.gridColumn51.VisibleIndex = 45;
            this.gridColumn51.Width = 114;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "下行QAM256(%)";
            this.gridColumn45.FieldName = "Times_QAM64_DLCode0";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 46;
            this.gridColumn45.Width = 114;
            // 
            // gridColumn48
            // 
            this.gridColumn48.Caption = "PDSCH_prb_num_slot";
            this.gridColumn48.FieldName = "PDSCH_PRb_Num_slot";
            this.gridColumn48.Name = "gridColumn48";
            this.gridColumn48.Visible = true;
            this.gridColumn48.VisibleIndex = 47;
            this.gridColumn48.Width = 145;
            // 
            // gridColumn49
            // 
            this.gridColumn49.Caption = "下行初传BLER";
            this.gridColumn49.FieldName = "PDSCH_Init_BLER";
            this.gridColumn49.Name = "gridColumn49";
            this.gridColumn49.Visible = true;
            this.gridColumn49.VisibleIndex = 48;
            this.gridColumn49.Width = 100;
            // 
            // gridColumn53
            // 
            this.gridColumn53.Caption = "下行初传BLERCode0";
            this.gridColumn53.FieldName = "PDSCH_Init_BLERCode0";
            this.gridColumn53.Name = "gridColumn53";
            this.gridColumn53.Visible = true;
            this.gridColumn53.VisibleIndex = 49;
            this.gridColumn53.Width = 139;
            // 
            // gridColumn54
            // 
            this.gridColumn54.Caption = "下行初传BLERCode1";
            this.gridColumn54.FieldName = "PDSCH_Init_BLERCode1";
            this.gridColumn54.Name = "gridColumn54";
            this.gridColumn54.Visible = true;
            this.gridColumn54.VisibleIndex = 50;
            this.gridColumn54.Width = 138;
            // 
            // gridControlRTP
            // 
            this.gridControlRTP.ContextMenuStrip = this.ctxMenuRTP;
            this.gridControlRTP.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode1.LevelTemplate = this.gridViewMessageInfo;
            gridLevelNode1.RelationName = "MessageInfos";
            this.gridControlRTP.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControlRTP.Location = new System.Drawing.Point(0, 0);
            this.gridControlRTP.MainView = this.gridViewRTP;
            this.gridControlRTP.Name = "gridControlRTP";
            this.gridControlRTP.Size = new System.Drawing.Size(997, 481);
            this.gridControlRTP.TabIndex = 6;
            this.gridControlRTP.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewRTP,
            this.gridViewMessageInfo});
            // 
            // ctxMenuRTP
            // 
            this.ctxMenuRTP.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportRTPExcel,
            this.miShowAllRTPNode,
            this.miHideAllRTPNode});
            this.ctxMenuRTP.Name = "ctxMenu";
            this.ctxMenuRTP.Size = new System.Drawing.Size(149, 70);
            // 
            // miExportRTPExcel
            // 
            this.miExportRTPExcel.Name = "miExportRTPExcel";
            this.miExportRTPExcel.Size = new System.Drawing.Size(148, 22);
            this.miExportRTPExcel.Text = "导出Excel";
            this.miExportRTPExcel.Click += new System.EventHandler(this.miExportRTPExcel_Click);
            // 
            // miShowAllRTPNode
            // 
            this.miShowAllRTPNode.Name = "miShowAllRTPNode";
            this.miShowAllRTPNode.Size = new System.Drawing.Size(148, 22);
            this.miShowAllRTPNode.Text = "展开所有节点";
            this.miShowAllRTPNode.Click += new System.EventHandler(this.miShowAllRTPNode_Click);
            // 
            // miHideAllRTPNode
            // 
            this.miHideAllRTPNode.Name = "miHideAllRTPNode";
            this.miHideAllRTPNode.Size = new System.Drawing.Size(148, 22);
            this.miHideAllRTPNode.Text = "收起所有节点";
            this.miHideAllRTPNode.Click += new System.EventHandler(this.miHideAllRTPNode_Click);
            // 
            // gridViewRTP
            // 
            this.gridViewRTP.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1});
            this.gridViewRTP.GridControl = this.gridControlRTP;
            this.gridViewRTP.Name = "gridViewRTP";
            this.gridViewRTP.OptionsBehavior.Editable = false;
            this.gridViewRTP.OptionsDetail.ShowDetailTabs = false;
            this.gridViewRTP.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "LOG名称";
            this.gridColumn1.FieldName = "FileName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // ZTRtpPacketsLostShowForm_NR
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(997, 481);
            this.Controls.Add(this.gridControlRTP);
            this.Name = "ZTRtpPacketsLostShowForm_NR";
            this.Text = "单通问题点统计";
            ((System.ComponentModel.ISupportInitialize)(this.gridViewMessageInfo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRTP)).EndInit();
            this.ctxMenuRTP.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRTP)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControlRTP;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewRTP;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn55;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewMessageInfo;
        private System.Windows.Forms.ContextMenuStrip ctxMenuRTP;
        private System.Windows.Forms.ToolStripMenuItem miExportRTPExcel;
        private System.Windows.Forms.ToolStripMenuItem miShowAllRTPNode;
        private System.Windows.Forms.ToolStripMenuItem miHideAllRTPNode;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn51;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn52;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn53;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn54;
    }
}