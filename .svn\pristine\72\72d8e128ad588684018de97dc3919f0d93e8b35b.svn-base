﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LTEAntennaMultiCoverageSetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControlBand = new DevExpress.XtraEditors.LabelControl();
            this.numRxLevDValue = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.label4 = new System.Windows.Forms.Label();
            this.numRxLevThreshold = new DevExpress.XtraEditors.SpinEdit();
            this.simpleButton1 = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButton2 = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditInvalidThresold = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.chkSaveSample = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.cmbStatTopN = new DevExpress.XtraEditors.ComboBoxEdit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevThreshold.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditInvalidThresold.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSaveSample.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbStatTopN.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControlBand
            // 
            this.labelControlBand.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControlBand.Appearance.Options.UseFont = true;
            this.labelControlBand.Location = new System.Drawing.Point(28, 17);
            this.labelControlBand.Name = "labelControlBand";
            this.labelControlBand.Size = new System.Drawing.Size(96, 12);
            this.labelControlBand.TabIndex = 3;
            this.labelControlBand.Text = "与最强信号差异 <";
            // 
            // numRxLevDValue
            // 
            this.numRxLevDValue.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numRxLevDValue.Location = new System.Drawing.Point(130, 14);
            this.numRxLevDValue.Name = "numRxLevDValue";
            this.numRxLevDValue.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRxLevDValue.Properties.Appearance.Options.UseFont = true;
            this.numRxLevDValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevDValue.Properties.IsFloatValue = false;
            this.numRxLevDValue.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.numRxLevDValue.Properties.Mask.EditMask = "N00";
            this.numRxLevDValue.Size = new System.Drawing.Size(82, 20);
            this.numRxLevDValue.TabIndex = 4;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(218, 17);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(12, 12);
            this.labelControl1.TabIndex = 5;
            this.labelControl1.Text = "dB";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(59, 51);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 8;
            this.label4.Text = "信号强度 >";
            // 
            // numRxLevThreshold
            // 
            this.numRxLevThreshold.EditValue = new decimal(new int[] {
            80,
            0,
            0,
            -2147483648});
            this.numRxLevThreshold.Location = new System.Drawing.Point(130, 48);
            this.numRxLevThreshold.Name = "numRxLevThreshold";
            this.numRxLevThreshold.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRxLevThreshold.Properties.Appearance.Options.UseFont = true;
            this.numRxLevThreshold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevThreshold.Properties.IsFloatValue = false;
            this.numRxLevThreshold.Properties.Mask.EditMask = "N00";
            this.numRxLevThreshold.Size = new System.Drawing.Size(82, 20);
            this.numRxLevThreshold.TabIndex = 9;
            // 
            // simpleButton1
            // 
            this.simpleButton1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButton1.Appearance.Options.UseFont = true;
            this.simpleButton1.Location = new System.Drawing.Point(82, 176);
            this.simpleButton1.Name = "simpleButton1";
            this.simpleButton1.Size = new System.Drawing.Size(75, 23);
            this.simpleButton1.TabIndex = 10;
            this.simpleButton1.Text = "确定";
            this.simpleButton1.Click += new System.EventHandler(this.simpleButton1_Click);
            // 
            // simpleButton2
            // 
            this.simpleButton2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButton2.Appearance.Options.UseFont = true;
            this.simpleButton2.Location = new System.Drawing.Point(170, 176);
            this.simpleButton2.Name = "simpleButton2";
            this.simpleButton2.Size = new System.Drawing.Size(75, 23);
            this.simpleButton2.TabIndex = 11;
            this.simpleButton2.Text = "取消";
            this.simpleButton2.Click += new System.EventHandler(this.simpleButton2_Click);
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(218, 51);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(18, 12);
            this.labelControl2.TabIndex = 12;
            this.labelControl2.Text = "dBm";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(218, 86);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(18, 12);
            this.labelControl5.TabIndex = 17;
            this.labelControl5.Text = "dBm";
            // 
            // spinEditInvalidThresold
            // 
            this.spinEditInvalidThresold.EditValue = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            this.spinEditInvalidThresold.Location = new System.Drawing.Point(130, 82);
            this.spinEditInvalidThresold.Name = "spinEditInvalidThresold";
            this.spinEditInvalidThresold.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditInvalidThresold.Properties.Appearance.Options.UseFont = true;
            this.spinEditInvalidThresold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditInvalidThresold.Properties.IsFloatValue = false;
            this.spinEditInvalidThresold.Properties.Mask.EditMask = "N00";
            this.spinEditInvalidThresold.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.spinEditInvalidThresold.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.spinEditInvalidThresold.Size = new System.Drawing.Size(82, 20);
            this.spinEditInvalidThresold.TabIndex = 15;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(52, 86);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(72, 12);
            this.labelControl4.TabIndex = 16;
            this.labelControl4.Text = "无效点场强 <";
            // 
            // chkSaveSample
            // 
            this.chkSaveSample.Enabled = false;
            this.chkSaveSample.Location = new System.Drawing.Point(50, 151);
            this.chkSaveSample.Name = "chkSaveSample";
            this.chkSaveSample.Properties.Caption = "保留测试点";
            this.chkSaveSample.Size = new System.Drawing.Size(89, 19);
            this.chkSaveSample.TabIndex = 18;
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(52, 119);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(72, 12);
            this.labelControl3.TabIndex = 19;
            this.labelControl3.Text = "统计分析方式";
            // 
            // cmbStatTopN
            // 
            this.cmbStatTopN.Location = new System.Drawing.Point(130, 116);
            this.cmbStatTopN.Name = "cmbStatTopN";
            this.cmbStatTopN.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cmbStatTopN.Properties.Appearance.Options.UseFont = true;
            this.cmbStatTopN.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbStatTopN.Properties.Items.AddRange(new object[] {
            "BCCH&TCH",
            "BCCH Only",
            "TCH Only"});
            this.cmbStatTopN.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cmbStatTopN.Size = new System.Drawing.Size(82, 20);
            this.cmbStatTopN.TabIndex = 21;
            // 
            // LTEAntennaMultiCoverageSetForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(274, 226);
            this.Controls.Add(this.cmbStatTopN);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.chkSaveSample);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.spinEditInvalidThresold);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.simpleButton2);
            this.Controls.Add(this.simpleButton1);
            this.Controls.Add(this.numRxLevThreshold);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.numRxLevDValue);
            this.Controls.Add(this.labelControlBand);
            this.Name = "LTEAntennaMultiCoverageSetForm";
            this.Text = "小区重叠覆盖度分析条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevThreshold.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditInvalidThresold.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSaveSample.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbStatTopN.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControlBand;
        private DevExpress.XtraEditors.SpinEdit numRxLevDValue;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.Label label4;
        private DevExpress.XtraEditors.SpinEdit numRxLevThreshold;
        private DevExpress.XtraEditors.SimpleButton simpleButton1;
        private DevExpress.XtraEditors.SimpleButton simpleButton2;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit spinEditInvalidThresold;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.CheckEdit chkSaveSample;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.ComboBoxEdit cmbStatTopN;
    }
}