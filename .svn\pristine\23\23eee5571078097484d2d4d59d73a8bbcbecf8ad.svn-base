﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRHandoverTooMuchDlg : BaseDialog
    {
        public NRHandoverTooMuchDlg()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        public void SetCondition(NRHandoverTooMuchCondition condition)
        {
            numTimeLimit.Value = (decimal)condition.TimeLimit;
            numDistanceLimit.Value = (decimal)condition.DistanceLimit;
            numHandoverCount.Value = (decimal)condition.HandoverCount;
            numDistanceLimitMin.Value = (decimal)condition.DistanceLimitMin;
            chkAnaLTE.Checked = condition.IsAnaLTE;
        }

        public void GetCondition(NRHandoverTooMuchCondition condition)
        {
            condition.TimeLimit = (int)numTimeLimit.Value;
            condition.DistanceLimit = (int)numDistanceLimit.Value;
            condition.HandoverCount = (int)numHandoverCount.Value;
            condition.DistanceLimitMin = (int)numDistanceLimitMin.Value;
            condition.IsAnaLTE = chkAnaLTE.Checked;
        }
    }

    public class NRHandoverTooMuchCondition
    {
        public int TimeLimit { get; set; } = 15;
        public int DistanceLimitMin { get; set; } = 0;
        public int DistanceLimit { get; set; } = 150;
        public int HandoverCount { get; set; } = 3;
        public bool IsAnaLTE { get; set; }
    }
}
