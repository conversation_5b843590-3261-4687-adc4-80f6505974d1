﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRLowSpeedPointDetail : IComparable<NRLowSpeedPointDetail>
    {
        private readonly NRTpManagerBase nRCond;
        public NRLowSpeedPointDetail(TestPoint pnt, NRLowSpeedCauseBase reason, NRTpManagerBase nRCond)
        {
            this.TestPoint = pnt;
            this.Reason = reason;
            this.nRCond = nRCond;
        }

        public TestPoint TestPoint
        {
            get;
            private set;
        }

        public string SCellName
        {
            get
            {
                if (SCell == null)
                {
                    return string.Empty;
                }
                return SCell.Name;
            }
        }

        public ICell SCell
        {
            get
            {
                if (TestPoint != null)
                {
                    return TestPoint.GetMainCell_NR();
                }
                return null;
            }
        }

        public NRLowSpeedCauseBase Reason
        {
            get;
            set;
        }

        public double Speed
        {
            get
            {
                double? speed = NRTpHelper.NrTpManager.GetAppSpeedMb(TestPoint);
                if (speed != null)
                {
                    return (double)speed;
                }
                return 0;
            }
        }

        public string CauseType
        {
            get { return Reason.Ancestor.Name; }
        }

        public string CauseScene
        {
            get
            {
                string ret = string.Empty;
                if (Reason.Parent != null)
                {
                    if (Reason.Parent == Reason.Ancestor)
                    {
                        ret = Reason.Name;
                    }
                    else
                    {
                        ret = Reason.Parent.Name;
                    }
                }
                return ret;
            }
        }

        public string CauseDetailName
        {
            get
            {
                return Reason.Name;
            }
        }

        public float? RSRP
        {
            get
            {
                return nRCond.GetSCellRsrp(TestPoint);
            }
        }

        public float? SINR
        {
            get
            {
                return nRCond.GetSCellSinr(TestPoint);
            }
        }

        public DateTime Time
        {
            get { return TestPoint.DateTime; }
        }

        public double Longitude
        {
            get { return TestPoint.Longitude; }
        }
        public double Latitude
        {
            get { return TestPoint.Latitude; }
        }

        #region IComparable<LowSpeedPointDetail> 成员

        public int CompareTo(NRLowSpeedPointDetail other)
        {
            return this.TestPoint.SN.CompareTo(other.TestPoint.SN);
        }

        #endregion
    }
}
