<?xml version="1.0"?>
<Configs>
  <Config name="Main">
    <Item name="WorkSpace" typeName="WorkSpace">
      <Item name="WorkSheets" typeName="IList">
        <Item typeName="WorkSheet">
          <Item name="Text" typeName="String">地图</Item>
          <Item name="ChildFormConfigs" typeName="IList">
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.MapForm</Item>
              <Item name="ImageFilePath" typeName="String">images\maplogo.gif</Item>
              <Item name="Text" typeName="String">MAP地图</Item>
              <Item name="WindowState" typeName="Int32">2</Item>
              <Item name="LocationX" typeName="Int32">0</Item>
              <Item name="LocationY" typeName="Int32">0</Item>
              <Item name="SizeWidth" typeName="Int32">1084</Item>
              <Item name="SizeHeight" typeName="Int32">552</Item>
              <Item name="IsTopFront" typeName="Boolean">False</Item>
              <Item name="BeforeLocationX" typeName="Int32">47</Item>
              <Item name="BeforeLocationY" typeName="Int32">9</Item>
              <Item name="Param" typeName="IDictionary" />
            </Item>
          </Item>
          <Item name="ActiveChildFormIndex" typeName="Int32">0</Item>
        </Item>
        <Item typeName="WorkSheet">
          <Item name="Text" typeName="String">事件/信令</Item>
          <Item name="ChildFormConfigs" typeName="IList">
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.BriefDataGridFormEvent</Item>
              <Item name="ImageFilePath" typeName="String">images\frame_info.gif</Item>
              <Item name="Text" typeName="String">事件列表</Item>
              <Item name="WindowState" typeName="Int32">0</Item>
              <Item name="LocationX" typeName="Int32">1</Item>
              <Item name="LocationY" typeName="Int32">-6</Item>
              <Item name="SizeWidth" typeName="Int32">1052</Item>
              <Item name="SizeHeight" typeName="Int32">493</Item>
              <Item name="IsTopFront" typeName="Boolean">False</Item>
              <Item name="BeforeLocationX" typeName="Int32">0</Item>
              <Item name="BeforeLocationY" typeName="Int32">0</Item>
              <Item name="Param" typeName="IDictionary">
                <Item typeName="Boolean" key="EquipmentFilterEnabled">False</Item>
                <Item typeName="IList" key="NeedLoadEquipmentFlags">
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                  <Item typeName="Boolean">False</Item>
                </Item>
                <Item typeName="Boolean" key="MessageFilterEnabled">False</Item>
                <Item typeName="IList" key="NeedLoadMessageIDs" />
                <Item typeName="Boolean" key="EventFilterEnabled">True</Item>
                <Item typeName="IList" key="NeedLoadEventIDs" />
                <Item typeName="Boolean" key="isShortTime">True</Item>
                <Item typeName="IList" key="ColumnHeaderTexts">
                  <Item typeName="String">MS</Item>
                  <Item typeName="String" />
                  <Item typeName="String">Name</Item>
                  <Item typeName="String">Time</Item>
                  <Item typeName="String">File Name</Item>
                  <Item typeName="String">LAC</Item>
                  <Item typeName="String">RAC</Item>
                  <Item typeName="String">CI</Item>
                  <Item typeName="String">Target LAC</Item>
                  <Item typeName="String">Target RAC</Item>
                  <Item typeName="String">Target CI</Item>
                  <Item typeName="String">Longitude</Item>
                  <Item typeName="String">Latitude</Item>
                  <Item typeName="String">Index</Item>
                  <Item typeName="String">Info</Item>
                  <Item typeName="String">StartTime</Item>
                  <Item typeName="String">StreetName</Item>
                  <Item typeName="String">AreaName</Item>
                  <Item typeName="String">Value1</Item>
                  <Item typeName="String">Value2</Item>
                  <Item typeName="String">Value3</Item>
                  <Item typeName="String">Value4</Item>
                  <Item typeName="String">Value5</Item>
                  <Item typeName="String">Value6</Item>
                  <Item typeName="String">Value7</Item>
                  <Item typeName="String">Value8</Item>
                  <Item typeName="String">Value9</Item>
                  <Item typeName="String">Value10</Item>
                  <Item typeName="String">CellName</Item>
                  <Item typeName="String">PCI</Item>
                  <Item typeName="String">ID</Item>
                  <Item typeName="String">ID(Hex)</Item>
                  <Item typeName="String">GridName</Item>
                  <Item typeName="String">AgentArea</Item>
                </Item>
                <Item typeName="IList" key="ColumnWidths">
                  <Item typeName="Int32">40</Item>
                  <Item typeName="Int32">20</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">120</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">30</Item>
                  <Item typeName="Int32">30</Item>
                  <Item typeName="Int32">30</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">80</Item>
                  <Item typeName="Int32">80</Item>
                  <Item typeName="Int32">50</Item>
                  <Item typeName="Int32">50</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">5</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                </Item>
                <Item typeName="IList" key="VisibleColumnIndexs" />
                <Item typeName="Boolean" key="IsCompareForm">False</Item>
                <Item typeName="Boolean" key="IsMTRForm">False</Item>
              </Item>
            </Item>
          </Item>
          <Item name="ActiveChildFormIndex" typeName="Int32">0</Item>
        </Item>
        <Item typeName="WorkSheet">
          <Item name="Text" typeName="String">文件列表</Item>
          <Item name="ChildFormConfigs" typeName="IList">
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.FileInfoForm</Item>
              <Item name="ImageFilePath" typeName="String" />
              <Item name="Text" typeName="String">文件列表</Item>
              <Item name="WindowState" typeName="Int32">2</Item>
              <Item name="LocationX" typeName="Int32">-8</Item>
              <Item name="LocationY" typeName="Int32">-30</Item>
              <Item name="SizeWidth" typeName="Int32">1084</Item>
              <Item name="SizeHeight" typeName="Int32">542</Item>
              <Item name="IsTopFront" typeName="Boolean">False</Item>
              <Item name="BeforeLocationX" typeName="Int32">-1</Item>
              <Item name="BeforeLocationY" typeName="Int32">-1</Item>
              <Item name="Param" typeName="IDictionary" />
            </Item>
          </Item>
          <Item name="ActiveChildFormIndex" typeName="Int32">0</Item>
        </Item>
        <Item typeName="WorkSheet">
          <Item name="Text" typeName="String">KPI统计</Item>
        </Item>
        <Item typeName="WorkSheet">
          <Item name="Text" typeName="String">TD数据</Item>
          <Item name="ChildFormConfigs" typeName="IList">
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.LineChartForm</Item>
              <Item name="ImageFilePath" typeName="String">images\frame_timeline.gif</Item>
              <Item name="Text" typeName="String">TD数据时序图</Item>
              <Item name="WindowState" typeName="Int32">0</Item>
              <Item name="LocationX" typeName="Int32">0</Item>
              <Item name="LocationY" typeName="Int32">0</Item>
              <Item name="SizeWidth" typeName="Int32">543</Item>
              <Item name="SizeHeight" typeName="Int32">238</Item>
              <Item name="IsTopFront" typeName="Boolean">False</Item>
              <Item name="BeforeLocationX" typeName="Int32">0</Item>
              <Item name="BeforeLocationY" typeName="Int32">0</Item>
              <Item name="Param" typeName="IDictionary">
                <Item typeName="Boolean" key="DisplayInfo">False</Item>
                <Item typeName="Boolean" key="DisplayLegend">True</Item>
                <Item typeName="Int32" key="GraphWidth">440</Item>
                <Item typeName="Int32" key="GraphHeight">143</Item>
                <Item typeName="Int32" key="DisplayChartCount">2</Item>
                <Item typeName="IList" key="ChartHeights">
                  <Item typeName="Int32">92</Item>
                  <Item typeName="Int32">74</Item>
                  <Item typeName="Int32">50</Item>
                </Item>
                <Item typeName="IList" key="ChartInfos">
                  <Item typeName="IDictionary">
                    <Item typeName="Boolean" key="DisplayHorizontalLine">True</Item>
                    <Item typeName="IList" key="SerialInfos">
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="String" key="SystemName">TDSCDMA</Item>
                        <Item typeName="String" key="ParamName">PCCPCH_RSCP</Item>
                        <Item typeName="Int32" key="ArrayIndex">0</Item>
                        <Item typeName="Single" key="Min">-140</Item>
                        <Item typeName="Single" key="Max">-10</Item>
                        <Item typeName="Int32" key="DisplayType">1</Item>
                        <Item typeName="Int32" key="ColorR">0</Item>
                        <Item typeName="Int32" key="ColorG">255</Item>
                        <Item typeName="Int32" key="ColorB">64</Item>
                        <Item typeName="Boolean" key="DisplayAlarmLine">True</Item>
                        <Item typeName="Single" key="AlarmThreshold">-90</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="String" key="SystemName">TDSCDMA</Item>
                        <Item typeName="String" key="ParamName">GSM_RxlevSub</Item>
                        <Item typeName="Int32" key="ArrayIndex">0</Item>
                        <Item typeName="Single" key="Min">-120</Item>
                        <Item typeName="Single" key="Max">-10</Item>
                        <Item typeName="Int32" key="DisplayType">1</Item>
                        <Item typeName="Int32" key="ColorR">128</Item>
                        <Item typeName="Int32" key="ColorG">255</Item>
                        <Item typeName="Int32" key="ColorB">255</Item>
                        <Item typeName="Boolean" key="DisplayAlarmLine">False</Item>
                      </Item>
                    </Item>
                    <Item typeName="IList" key="EventInfos">
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">6</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">7</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">8</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">9</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">10</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">11</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">18</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">41</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">43</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">82</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">83</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">84</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">85</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">87</Item>
                      </Item>
                    </Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Boolean" key="DisplayHorizontalLine">True</Item>
                    <Item typeName="IList" key="SerialInfos">
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="String" key="SystemName">TDSCDMA</Item>
                        <Item typeName="String" key="ParamName">AppThroughputDL_kb</Item>
                        <Item typeName="Int32" key="ArrayIndex">0</Item>
                        <Item typeName="Single" key="Min">0</Item>
                        <Item typeName="Single" key="Max">4000</Item>
                        <Item typeName="Int32" key="DisplayType">0</Item>
                        <Item typeName="Int32" key="ColorR">255</Item>
                        <Item typeName="Int32" key="ColorG">0</Item>
                        <Item typeName="Int32" key="ColorB">0</Item>
                        <Item typeName="Int32" key="LineWidth">1</Item>
                        <Item typeName="Boolean" key="DisplayAlarmLine">False</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="String" key="SystemName">TDSCDMA</Item>
                        <Item typeName="String" key="ParamName">BLER</Item>
                        <Item typeName="Int32" key="ArrayIndex">0</Item>
                        <Item typeName="Single" key="Min">0</Item>
                        <Item typeName="Single" key="Max">100</Item>
                        <Item typeName="Int32" key="DisplayType">0</Item>
                        <Item typeName="Int32" key="ColorR">0</Item>
                        <Item typeName="Int32" key="ColorG">128</Item>
                        <Item typeName="Int32" key="ColorB">0</Item>
                        <Item typeName="Int32" key="LineWidth">1</Item>
                        <Item typeName="Boolean" key="DisplayAlarmLine">False</Item>
                      </Item>
                    </Item>
                    <Item typeName="IList" key="EventInfos">
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">1</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">2</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">3</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">4</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">5</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">6</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">7</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">8</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">9</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">10</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">11</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">12</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">13</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">14</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">15</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">17</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">18</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">40</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">41</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">43</Item>
                      </Item>
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="Int32" key="EventID">73</Item>
                      </Item>
                    </Item>
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Boolean" key="DisplayHorizontalLine">True</Item>
                    <Item typeName="IList" key="SerialInfos">
                      <Item typeName="IDictionary">
                        <Item typeName="Int32" key="MS">0</Item>
                        <Item typeName="String" key="SystemName">GSM</Item>
                        <Item typeName="String" key="ParamName">MsTxPower</Item>
                        <Item typeName="Int32" key="ArrayIndex">0</Item>
                        <Item typeName="Single" key="Min">0</Item>
                        <Item typeName="Single" key="Max">39</Item>
                        <Item typeName="Int32" key="DisplayType">0</Item>
                        <Item typeName="Int32" key="ColorR">255</Item>
                        <Item typeName="Int32" key="ColorG">0</Item>
                        <Item typeName="Int32" key="ColorB">0</Item>
                        <Item typeName="Int32" key="LineWidth">1</Item>
                        <Item typeName="Boolean" key="DisplayAlarmLine">False</Item>
                      </Item>
                    </Item>
                    <Item typeName="IList" key="EventInfos" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="Boolean" key="DisplayHorizontalLine">True</Item>
                    <Item typeName="IList" key="SerialInfos" />
                    <Item typeName="IList" key="EventInfos" />
                  </Item>
                </Item>
                <Item typeName="Boolean" key="IsCompareForm">False</Item>
                <Item typeName="Boolean" key="IsMTRForm">False</Item>
                <Item typeName="IList" key="selServiceTypes">
                  <Item typeName="Int32">5</Item>
                  <Item typeName="Int32">18</Item>
                  <Item typeName="Int32">27</Item>
                </Item>
              </Item>
            </Item>
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.RTPointsDataGridForm</Item>
              <Item name="ImageFilePath" typeName="String">images\frame_info.gif</Item>
              <Item name="Text" typeName="String">TD Serving/Neighbor</Item>
              <Item name="WindowState" typeName="Int32">0</Item>
              <Item name="LocationX" typeName="Int32">494</Item>
              <Item name="LocationY" typeName="Int32">240</Item>
              <Item name="SizeWidth" typeName="Int32">533</Item>
              <Item name="SizeHeight" typeName="Int32">168</Item>
              <Item name="IsTopFront" typeName="Boolean">False</Item>
              <Item name="BeforeLocationX" typeName="Int32">0</Item>
              <Item name="BeforeLocationY" typeName="Int32">0</Item>
              <Item name="Param" typeName="IDictionary">
                <Item typeName="Int32" key="RowCount">15</Item>
                <Item typeName="Int32" key="ColumnCount">10</Item>
                <Item typeName="IList" key="ColumnWidths">
                  <Item typeName="Int32">41</Item>
                  <Item typeName="Int32">62</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">62</Item>
                  <Item typeName="Int32">67</Item>
                  <Item typeName="Int32">77</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">48</Item>
                  <Item typeName="Int32">100</Item>
                  <Item typeName="Int32">100</Item>
                </Item>
                <Item typeName="IList" key="ColumnHeaderTexts">
                  <Item typeName="String" />
                  <Item typeName="String">CellID</Item>
                  <Item typeName="String">CellName</Item>
                  <Item typeName="String">CPI</Item>
                  <Item typeName="String">UARFCN</Item>
                  <Item typeName="String">RSCP(dBm)</Item>
                  <Item typeName="String">PathLoss(dB)</Item>
                  <Item typeName="String">Rs/Rn</Item>
                  <Item typeName="String">CarrierRSSI(dBm)</Item>
                  <Item typeName="String">Distance(米)</Item>
                </Item>
                <Item typeName="IList" key="CellDefines">
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">SC</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_CI</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_Name</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_CPI</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_UARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">PCCPCH_RSCP</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">PCCPCH_Pathloss</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_Rs</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">CarrierRSSI</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">NC1</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_CI</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Name</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_CPI</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_UARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_PCCPCH_RSCP</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Pathloss</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Rn</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_RSSI</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">NC2</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_CI</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Name</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_CPI</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_UARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_PCCPCH_RSCP</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Pathloss</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Rn</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_RSSI</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">NC3</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_CI</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Name</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_CPI</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_UARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_PCCPCH_RSCP</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Pathloss</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Rn</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_RSSI</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">NC4</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_CI</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Name</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_CPI</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_UARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_PCCPCH_RSCP</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Pathloss</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Rn</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_RSSI</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">NC5</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_CI</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Name</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_CPI</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_UARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_PCCPCH_RSCP</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Pathloss</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Rn</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_RSSI</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">NC6</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_CI</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Name</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_CPI</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_UARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_PCCPCH_RSCP</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Pathloss</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Rn</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_RSSI</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">NCell_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                </Item>
                <Item typeName="Boolean" key="IsCompareForm">False</Item>
              </Item>
            </Item>
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.RTPointsDataGridForm</Item>
              <Item name="ImageFilePath" typeName="String">images\frame_info.gif</Item>
              <Item name="Text" typeName="String">TD-GSM Serving/Neighbor</Item>
              <Item name="WindowState" typeName="Int32">0</Item>
              <Item name="LocationX" typeName="Int32">493</Item>
              <Item name="LocationY" typeName="Int32">410</Item>
              <Item name="SizeWidth" typeName="Int32">536</Item>
              <Item name="SizeHeight" typeName="Int32">171</Item>
              <Item name="IsTopFront" typeName="Boolean">False</Item>
              <Item name="BeforeLocationX" typeName="Int32">0</Item>
              <Item name="BeforeLocationY" typeName="Int32">0</Item>
              <Item name="Param" typeName="IDictionary">
                <Item typeName="Int32" key="RowCount">7</Item>
                <Item typeName="Int32" key="ColumnCount">8</Item>
                <Item typeName="IList" key="ColumnWidths">
                  <Item typeName="Int32">66</Item>
                  <Item typeName="Int32">110</Item>
                  <Item typeName="Int32">77</Item>
                  <Item typeName="Int32">79</Item>
                  <Item typeName="Int32">96</Item>
                  <Item typeName="Int32">71</Item>
                  <Item typeName="Int32">78</Item>
                  <Item typeName="Int32">100</Item>
                </Item>
                <Item typeName="IList" key="ColumnHeaderTexts">
                  <Item typeName="String" />
                  <Item typeName="String">CellName</Item>
                  <Item typeName="String">LAC</Item>
                  <Item typeName="String">CellID</Item>
                  <Item typeName="String">ARFCN</Item>
                  <Item typeName="String">BSIC</Item>
                  <Item typeName="String">Rxlev</Item>
                  <Item typeName="String">Distance(米)</Item>
                </Item>
                <Item typeName="IList" key="CellDefines">
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">SC</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_SCell_Name</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_SCell_LAC</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_SCell_CI</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_SCell_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_SCell_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_RxlevSub</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_SCell_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">NC1</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Name</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_LAC</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_CI</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">NC2</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Name</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_LAC</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_CI</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">1</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">NC3</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Name</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_LAC</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_CI</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">2</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">NC4</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Name</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_LAC</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_CI</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">3</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">NC5</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Name</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_LAC</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_CI</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">4</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">NC6</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Name</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_LAC</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_CI</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Rxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_NCell_Distance</Item>
                      <Item typeName="Int32" key="ArrayIndex">5</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                </Item>
                <Item typeName="Boolean" key="IsCompareForm">False</Item>
              </Item>
            </Item>
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.RTPointsDataGridForm</Item>
              <Item name="ImageFilePath" typeName="String">images\frame_info.gif</Item>
              <Item name="Text" typeName="String">TD System Parameters</Item>
              <Item name="WindowState" typeName="Int32">0</Item>
              <Item name="LocationX" typeName="Int32">207</Item>
              <Item name="LocationY" typeName="Int32">238</Item>
              <Item name="SizeWidth" typeName="Int32">290</Item>
              <Item name="SizeHeight" typeName="Int32">345</Item>
              <Item name="IsTopFront" typeName="Boolean">False</Item>
              <Item name="BeforeLocationX" typeName="Int32">0</Item>
              <Item name="BeforeLocationY" typeName="Int32">0</Item>
              <Item name="Param" typeName="IDictionary">
                <Item typeName="Int32" key="RowCount">25</Item>
                <Item typeName="Int32" key="ColumnCount">4</Item>
                <Item typeName="IList" key="ColumnWidths">
                  <Item typeName="Int32">65</Item>
                  <Item typeName="Int32">42</Item>
                  <Item typeName="Int32">77</Item>
                  <Item typeName="Int32">46</Item>
                </Item>
                <Item typeName="IList" key="ColumnHeaderTexts">
                  <Item typeName="String">指标项</Item>
                  <Item typeName="String">值</Item>
                  <Item typeName="String">指标项</Item>
                  <Item typeName="String">值</Item>
                </Item>
                <Item typeName="IList" key="CellDefines">
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">Time</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">Common Param</Item>
                      <Item typeName="String" key="ParamName">Time</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">IntraSearch</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_IntraSearch</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">CellName</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_Name</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">InterSearch</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_InterSearch</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">LAC</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_LAC</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">Tresel</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_TreSelection</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">CI</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_CI</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">MaxTxPower</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">TxPower</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">UARFCN</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_UARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">Qhyst</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_Qhysts</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">CPI</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_CPI</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">Srxlev</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_Srxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">RAC</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_RAC</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">Rs</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_Rs</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">CellIdentity</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_CellIdentity</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">HCS PRIO</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_HCS</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">RNC ID</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">SCell_RNC</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">Work Frequency</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">DPCH_UARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">URAList</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">ChipWindow</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">Chip_Window</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">ATT</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">T3212</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">NMO</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">TxPower</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                </Item>
                <Item typeName="Boolean" key="IsCompareForm">False</Item>
              </Item>
            </Item>
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.RTPointsDataGridForm</Item>
              <Item name="ImageFilePath" typeName="String">images\frame_info.gif</Item>
              <Item name="Text" typeName="String">TD无线参数</Item>
              <Item name="WindowState" typeName="Int32">0</Item>
              <Item name="LocationX" typeName="Int32">549</Item>
              <Item name="LocationY" typeName="Int32">0</Item>
              <Item name="SizeWidth" typeName="Int32">477</Item>
              <Item name="SizeHeight" typeName="Int32">236</Item>
              <Item name="IsTopFront" typeName="Boolean">False</Item>
              <Item name="BeforeLocationX" typeName="Int32">0</Item>
              <Item name="BeforeLocationY" typeName="Int32">0</Item>
              <Item name="Param" typeName="IDictionary">
                <Item typeName="Int32" key="RowCount">13</Item>
                <Item typeName="Int32" key="ColumnCount">4</Item>
                <Item typeName="IList" key="ColumnWidths">
                  <Item typeName="Int32">132</Item>
                  <Item typeName="Int32">89</Item>
                  <Item typeName="Int32">112</Item>
                  <Item typeName="Int32">118</Item>
                </Item>
                <Item typeName="IList" key="ColumnHeaderTexts">
                  <Item typeName="String">TD参数项</Item>
                  <Item typeName="String">值</Item>
                  <Item typeName="String">GSM参数项</Item>
                  <Item typeName="String">值</Item>
                </Item>
                <Item typeName="IList" key="CellDefines">
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">PCCPCH_RSCP</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">PCCPCH_RSCP</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">CellName</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_SCell_Name</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">DPCH_RSCP</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">DPCH_RSCP</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">RxlevSub</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_RxlevSub</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">PCCPCH_ISCP</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">PCCPCH_ISCP</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">RxlevFull</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_RxlevFull</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">DPCH_ISCP</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">DPCH_ISCP</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">BCCHRxlev</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_BCCHRxlev</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">CCTRCHSIR</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">CCTRCH_SIR_Extra</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">RxQualSub</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_RxqualSub</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">DownlinkTargetSIR</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">DLTarget_SIR</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">RxQualFull</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_RxqualSub</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">CarrierRSSI</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">CarrierRSSI</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">BCCH</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_SCell_ARFCN</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">PCCPCH_C/I</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">PCCPCH_C2I</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">BSIC</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">GSM_SCell_BSIC</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">True</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">DPCH_C/I</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">DPCH_C2I</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">TxPower</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">TxPower</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">TA(chip/8)</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">TA(chips)</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                </Item>
                <Item typeName="Boolean" key="IsCompareForm">False</Item>
              </Item>
            </Item>
            <Item typeName="ChildFormConfig">
              <Item name="AssemblyName" typeName="String">RAMS.exe</Item>
              <Item name="TypeName" typeName="String">MasterCom.RAMS.Func.RTPointsDataGridForm</Item>
              <Item name="ImageFilePath" typeName="String">images\frame_info.gif</Item>
              <Item name="Text" typeName="String">参数显示模板</Item>
              <Item name="WindowState" typeName="Int32">0</Item>
              <Item name="LocationX" typeName="Int32">-1</Item>
              <Item name="LocationY" typeName="Int32">237</Item>
              <Item name="SizeWidth" typeName="Int32">211</Item>
              <Item name="SizeHeight" typeName="Int32">344</Item>
              <Item name="IsTopFront" typeName="Boolean">False</Item>
              <Item name="BeforeLocationX" typeName="Int32">0</Item>
              <Item name="BeforeLocationY" typeName="Int32">0</Item>
              <Item name="Param" typeName="IDictionary">
                <Item typeName="Int32" key="RowCount">5</Item>
                <Item typeName="Int32" key="ColumnCount">3</Item>
                <Item typeName="IList" key="ColumnWidths">
                  <Item typeName="Int32">77</Item>
                  <Item typeName="Int32">63</Item>
                  <Item typeName="Int32">61</Item>
                </Item>
                <Item typeName="IList" key="ColumnHeaderTexts">
                  <Item typeName="String">参数</Item>
                  <Item typeName="String">上行</Item>
                  <Item typeName="String">下行</Item>
                </Item>
                <Item typeName="IList" key="CellDefines">
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">App速率(kbps)</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">AppThroughputUL_kb</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                      <Item typeName="Int32" key="MS">0</Item>
                      <Item typeName="String" key="SystemName">TDSCDMA</Item>
                      <Item typeName="String" key="ParamName">AppThroughputDL_kb</Item>
                      <Item typeName="Int32" key="ArrayIndex">0</Item>
                      <Item typeName="Boolean" key="DisplayBar">True</Item>
                      <Item typeName="Boolean" key="BarConstantLength">False</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Text</Item>
                      <Item typeName="String" key="Text">码道</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                    </Item>
                    <Item typeName="IDictionary">
                      <Item typeName="String" key="Type">Param</Item>
                    </Item>
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                  <Item typeName="IList">
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                    <Item typeName="IDictionary" />
                  </Item>
                </Item>
                <Item typeName="Boolean" key="IsCompareForm">False</Item>
              </Item>
            </Item>
          </Item>
          <Item name="ActiveChildFormIndex" typeName="Int32">4</Item>
        </Item>
      </Item>
      <Item name="SelectedIndex" typeName="Int32">2</Item>
    </Item>
  </Config>
</Configs>