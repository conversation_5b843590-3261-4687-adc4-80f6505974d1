﻿using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NbIotMgrsWeakSinrSetting : LteMgrsConditionControlBase
    {
        public NbIotMgrsWeakSinrSetting()
        {
            InitializeComponent();
            initValues();
        }

        public override string Title
        {
            get { return "连续质差"; }
        }

        public override object GetCondition(out string invalidReason)
        {
            invalidReason = null;
            return new object[] {
                (int)numGridCount.Value,
                (double)numMinSINR.Value,
            };
        }

        /// <summary>
        /// 保存条件信息
        /// </summary>
        /// <param name="xcfg"></param>
        public override void SaveCondititon(XmlConfigFile xcfg)
        {
            XmlElement configWeakRsrp = xcfg.AddConfig("WeakSinr");
            xcfg.AddItem(configWeakRsrp, "GridCount", (int)numGridCount.Value);
            xcfg.AddItem(configWeakRsrp, "MinRinr", (double)numMinSINR.Value);
        }

        /// <summary>
        /// 从配置文件读取参数值
        /// </summary>
        private void initValues()
        {
            XmlConfigFile configFile = new MyXmlConfigFile(NbIotMgrsBaseSettingManager.Instance.ConfigPath);
            if (configFile.Load())
            {
                XmlElement configCoverage = configFile.GetConfig("WeakSinr");
                object obj = configFile.GetItemValue(configCoverage, "GridCount");
                if (obj != null)
                {
                    numGridCount.Value = (int)obj;
                }
                obj = configFile.GetItemValue(configCoverage, "MinRinr");
                if (obj != null)
                {
                    numMinSINR.Value = (decimal)(double)obj;
                }
            }
        }
    }
}
