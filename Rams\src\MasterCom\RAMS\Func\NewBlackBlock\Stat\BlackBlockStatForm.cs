﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Imaging;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraCharts;
using DevExpress.XtraGrid.Views.Grid;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Util;
using MasterCom.Util;

namespace MasterCom.RAMS.NewBlackBlock
{
    public partial class BlackBlockStatForm : MinCloseForm
    {
        public BlackBlockStatForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
        }
        List<BlackBlockItem> blocks;
        public void FillData(List<BlackBlockItem> blocks)
        {
            this.blocks = blocks;
            initDataTable();
            fillRegionStatTab();
            fillCauseTabView();
        }

        private void fillCauseTabView()
        {
            gridCtrlCause.DataSource = causeListTable;
            gridCtrlCause.RefreshDataSource();

            Series series = chartPieMainCause.GetSeriesByName("series");
            fillSeries(series, mainCauseTable);

            series = chartPieSubCause.GetSeriesByName("series");
            fillSeries(series, subCauseTable);

            series = chartPieDetailCause.GetSeriesByName("series");
            fillSeries(series, detailCauseTable);
        }
        private readonly string argName = "ArgName";
        private readonly string valueName = "ValueName";

        DataTable regionCreateTb;
        Dictionary<string, DataTable> regionGridCreateDic;
        DataTable regionCloseTb;
        Dictionary<string, DataTable> regionGridCloseDic;


        private void initDataTable()
        {
            int createCnt = 0;
            int closeCnt = 0;
            Dictionary<string, List<BlackBlockItem>> regionCreateBlkDic = new Dictionary<string, List<BlackBlockItem>>();
            Dictionary<string, List<BlackBlockItem>> regionCloseBlkDic = new Dictionary<string, List<BlackBlockItem>>();
            Dictionary<string, int> mainCauseCntDic = new Dictionary<string, int>();
            Dictionary<string, int> subCauseCntDic = new Dictionary<string, int>();
            Dictionary<string, int> detailCauseCntDic = new Dictionary<string, int>();
            Dictionary<string, Dictionary<string, int>> mainCauseRegCntDic = new Dictionary<string, Dictionary<string, int>>();
            Dictionary<string, Dictionary<string, int>> subCauseRegCntDic = new Dictionary<string, Dictionary<string, int>>();
            Dictionary<string, Dictionary<string, int>> detailCauseRegCntDic = new Dictionary<string, Dictionary<string, int>>();
            causeListTable = CreateCauseTable();
            foreach (BlackBlockItem bb in blocks)
            {
                //====tab cause===
                fillCauseTable(causeListTable, bb);
                fillCauseCntDic(mainCauseCntDic, subCauseCntDic, detailCauseCntDic, bb);
                fillCauseRegionCntDic(mainCauseRegCntDic, subCauseRegCntDic, detailCauseRegCntDic, bb);
                //====end====

                if (bb.status == 2 || bb.status == 3)
                {
                    createCnt++;
                    List<BlackBlockItem> items = null;
                    if (!regionCreateBlkDic.TryGetValue(bb.Region, out items))
                    {
                        items = new List<BlackBlockItem>();
                        regionCreateBlkDic.Add(bb.Region, items);
                    }
                    items.Add(bb);
                }
                else if (bb.status == 4)
                {
                    closeCnt++;
                    List<BlackBlockItem> items = null;
                    if (!regionCloseBlkDic.TryGetValue(bb.Region, out items))
                    {
                        items = new List<BlackBlockItem>();
                        regionCloseBlkDic.Add(bb.Region, items);
                    }
                    items.Add(bb);
                }
            }

            regionCreateTb = CreateDataTable();
            regionCloseTb = CreateDataTable();
            regionGridCreateDic = new Dictionary<string, DataTable>();
            regionGridCloseDic = new Dictionary<string, DataTable>();
            dealRegionGridCloseDic(createCnt, regionCreateBlkDic, regionCreateTb, regionGridCreateDic);

            dealRegionGridCloseDic(closeCnt, regionCloseBlkDic, regionCloseTb, regionGridCloseDic);

            //====cause datatable====
            mainCauseTable = CreateDataTable();
            subCauseTable = CreateDataTable();
            detailCauseTable = CreateDataTable();
            fillCausePieDataTable(mainCauseTable, mainCauseCntDic, blocks.Count);
            fillCausePieDataTable(subCauseTable, subCauseCntDic, blocks.Count);
            fillCausePieDataTable(detailCauseTable, detailCauseCntDic, blocks.Count);
            chartBarMainCause.Series.Clear();
            chartBarSubCause.Series.Clear();
            chartBarDetailCause.Series.Clear();

            Dictionary<string, int> regTotalCauseCntDic = getRegTotalCauseCntDic(mainCauseRegCntDic);
            dealChartBar(mainCauseRegCntDic, regTotalCauseCntDic, chartBarMainCause);

            regTotalCauseCntDic = getRegTotalCauseCntDic(mainCauseRegCntDic);
            dealChartBar(subCauseRegCntDic, regTotalCauseCntDic, chartBarSubCause);

            regTotalCauseCntDic = getRegTotalCauseCntDic(mainCauseRegCntDic);
            dealChartBar(detailCauseRegCntDic, regTotalCauseCntDic, chartBarDetailCause);
            //====end
        }

        private void dealRegionGridCloseDic(int cnt, Dictionary<string, List<BlackBlockItem>> regionBlkDic, DataTable regionTb, Dictionary<string, DataTable> regionGridDic)
        {
            foreach (string regName in regionBlkDic.Keys)
            {
                List<BlackBlockItem> bbItems = regionBlkDic[regName];
                DataRow row = regionTb.NewRow();
                row[argName] = regName;
                row[valueName] = bbItems.Count * 1.0 / cnt;
                regionTb.Rows.Add(row);

                Dictionary<string, int> gridCntDic = new Dictionary<string, int>();
                foreach (BlackBlockItem item in bbItems)
                {
                    if (gridCntDic.ContainsKey(item.GridGroup))
                    {
                        gridCntDic[item.GridGroup]++;
                    }
                    else
                    {
                        gridCntDic[item.GridGroup] = 1;
                    }
                }
                DataTable gridTb = CreateDataTable();
                foreach (string gridName in gridCntDic.Keys)
                {
                    DataRow gRow = gridTb.NewRow();
                    gRow[argName] = gridName;
                    gRow[valueName] = gridCntDic[gridName] * 1.0 / bbItems.Count;
                    gridTb.Rows.Add(gRow);
                }
                regionGridDic.Add(regName, gridTb);
            }
        }

        private Dictionary<string, int> getRegTotalCauseCntDic(Dictionary<string, Dictionary<string, int>> causeRegCntDic)
        {
            Dictionary<string, int> regTotalCauseCntDic = new Dictionary<string, int>();
            foreach (KeyValuePair<string, Dictionary<string, int>> kvp in causeRegCntDic)
            {
                foreach (string reg in kvp.Value.Keys)
                {
                    if (regTotalCauseCntDic.ContainsKey(reg))
                    {
                        regTotalCauseCntDic[reg] += kvp.Value[reg];
                    }
                    else
                    {
                        regTotalCauseCntDic[reg] = kvp.Value[reg];
                    }
                }
            }

            return regTotalCauseCntDic;
        }

        private void dealChartBar(Dictionary<string, Dictionary<string, int>> causeRegCntDic, Dictionary<string, int> regTotalCauseCntDic, ChartControl chartBar)
        {
            foreach (string causeName in causeRegCntDic.Keys)
            {
                Series series = new Series(causeName, ViewType.FullStackedBar);
                ((FullStackedBarPointOptions)series.PointOptions).ValueNumericOptions.Format = NumericFormat.Percent;
                ((FullStackedBarSeriesView)series.View).FillStyle.FillMode = FillMode.Solid;
                chartBar.Series.Add(series);
                foreach (string regName in causeRegCntDic[causeName].Keys)
                {
                    double per = causeRegCntDic[causeName][regName] * 1.0 / regTotalCauseCntDic[regName];
                    series.Points.Add(new SeriesPoint(regName, per));
                }
            }
        }

        private void fillCausePieDataTable(DataTable table, Dictionary<string, int> causeCntDic, int totalCnt)
        {
            foreach (string cause in causeCntDic.Keys)
            {
                double per = causeCntDic[cause] * 1.0 / totalCnt;
                DataRow row = table.NewRow();
                row[argName] = cause;
                row[valueName] = per;
                table.Rows.Add(row);
            }
        }

        DataTable mainCauseTable;
        DataTable subCauseTable;
        DataTable detailCauseTable;
        DataTable causeListTable;
        private DataTable CreateCauseTable()
        {
            DataTable tb = new DataTable();
            tb.Columns.Add("主因", typeof(string));
            tb.Columns.Add("原因", typeof(string));
            tb.Columns.Add("原因分析", typeof(string));
            tb.Columns.Add("区域", typeof(string));
            tb.Columns.Add("ID", typeof(int));
            tb.Columns.Add("状态", typeof(string));
            tb.Columns.Add("创建日期", typeof(string));
            tb.Columns.Add("关闭日期", typeof(string));
            return tb;
        }

        private void fillCauseTable(DataTable tb, BlackBlockItem bb)
        {
            DataRow row = tb.NewRow();
            row["主因"] = bb.Extended.CauseMain;
            row["原因"] = bb.Extended.CauseSub;
            row["原因分析"] = bb.Extended.CauseDetail;
            row["区域"] = bb.Region;
            row["ID"] = bb.blockId;
            row["状态"] = bb.StatusDes;
            row["创建日期"] = bb.CreateDateString;
            row["关闭日期"] = bb.ClosedDateString;
            tb.Rows.Add(row);
        }

        private DataTable CreateDataTable()
        {
            DataTable tb = new DataTable();
            tb.Columns.Add(argName, typeof(string));
            tb.Columns.Add(valueName, typeof(double));
            return tb;
        }

        /// <summary>
        /// 填充 [原因-次数]
        /// </summary>
        /// <param name="mainCauseCntDic"></param>
        /// <param name="subCauseCntDic"></param>
        /// <param name="detailCauseCntDic"></param>
        /// <param name="bb"></param>
        private void fillCauseCntDic(Dictionary<string, int> mainCauseCntDic, Dictionary<string, int> subCauseCntDic, Dictionary<string, int> detailCauseCntDic, BlackBlockItem bb)
        {
            if (mainCauseCntDic.ContainsKey(bb.Extended.CauseMain))
            {
                mainCauseCntDic[bb.Extended.CauseMain]++;
            }
            else
            {
                mainCauseCntDic[bb.Extended.CauseMain] = 1;
            }

            if (subCauseCntDic.ContainsKey(bb.Extended.CauseSub))
            {
                subCauseCntDic[bb.Extended.CauseSub]++;
            }
            else
            {
                subCauseCntDic[bb.Extended.CauseSub] = 1;
            }

            if (detailCauseCntDic.ContainsKey(bb.Extended.CauseDetail))
            {
                detailCauseCntDic[bb.Extended.CauseDetail]++;
            }
            else
            {
                detailCauseCntDic[bb.Extended.CauseDetail] = 1;
            }
        }

        /// <summary>
        /// 填充 [原因-[区域-次数]]
        /// </summary>
        /// <param name="mainCauseRegCntDic"></param>
        /// <param name="subCauseRegCntDic"></param>
        /// <param name="detailCauseRegCntDic"></param>
        /// <param name="bb"></param>
        private void fillCauseRegionCntDic(Dictionary<string, Dictionary<string, int>> mainCauseRegCntDic
            ,Dictionary<string, Dictionary<string, int>> subCauseRegCntDic
            , Dictionary<string, Dictionary<string, int>> detailCauseRegCntDic
            ,BlackBlockItem bb)
        {
            Dictionary<string, int> regCntDic = null;
            if (!mainCauseRegCntDic.TryGetValue(bb.Extended.CauseMain, out regCntDic))
            {
                regCntDic = new Dictionary<string, int>();
                mainCauseRegCntDic.Add(bb.Extended.CauseMain, regCntDic);
            }
            if (regCntDic.ContainsKey(bb.Region))
            {
                regCntDic[bb.Region]++;
            }
            else
            {
                regCntDic[bb.Region] = 1;
            }

            if (!subCauseRegCntDic.TryGetValue(bb.Extended.CauseSub, out regCntDic))
            {
                regCntDic = new Dictionary<string, int>();
                subCauseRegCntDic.Add(bb.Extended.CauseSub, regCntDic);
            }
            if (regCntDic.ContainsKey(bb.Region))
            {
                regCntDic[bb.Region]++;
            }
            else
            {
                regCntDic[bb.Region] = 1;
            }
            if (!detailCauseRegCntDic.TryGetValue(bb.Extended.CauseDetail, out regCntDic))
            {
                regCntDic = new Dictionary<string, int>();
                detailCauseRegCntDic.Add(bb.Extended.CauseDetail, regCntDic);
            }
            if (regCntDic.ContainsKey(bb.Region))
            {
                regCntDic[bb.Region]++;
            }
            else
            {
                regCntDic[bb.Region] = 1;
            }
        }


        private void fillRegionStatTab()
        {
            gridCtrlRegion.DataSource = blocks;
            gridCtrlRegion.RefreshDataSource();
            Series series = chartRegionCreate.GetSeriesByName("mainRegion");
            fillSeries(series,regionCreateTb);

            series = chartRegionClose.GetSeriesByName("mainRegion");
            fillSeries(series, regionCloseTb);

            series = chartGridCreate.GetSeriesByName("seriesGridCreate");
            series.Visible = false;

            series = chartGridClose.GetSeriesByName("seriesGridClose");
            series.Visible = false;
        }

        private void fillSeries(Series series, DataTable tb)
        {
            series.DataSource = tb;
            series.ArgumentDataMember = argName;
            series.ValueDataMembers.Clear();
            series.ValueDataMembers.AddRange(new string[] { valueName });
        }

        private void chartRegionCreate_ObjectSelected(object sender, HotTrackEventArgs e)
        {
            if (e.Object is Series && e.AdditionalObject is SeriesPoint)
            {
                SeriesPoint point = e.AdditionalObject as SeriesPoint;
                Series series = chartGridCreate.GetSeriesByName("seriesGridCreate");
                fillSeries(series, regionGridCreateDic[point.Argument]);
                series.Visible = true;
            }
        }

        private void chartRegionClose_ObjectSelected(object sender, HotTrackEventArgs e)
        {
            if (e.Object is Series && e.AdditionalObject is SeriesPoint)
            {
                SeriesPoint point = e.AdditionalObject as SeriesPoint;
                Series series = chartGridClose.GetSeriesByName("seriesGridClose");
                fillSeries(series, regionGridCloseDic[point.Argument]);
                series.Visible = true;
            }
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveFileDialog = new SaveFileDialog();
            saveFileDialog.Filter = FilterHelper.Excel;
            saveFileDialog.RestoreDirectory = true;

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                System.IO.DirectoryInfo dir = System.IO.Directory.CreateDirectory(System.IO.Path.Combine(Application.StartupPath, "BlockStatImg"));
                try
                {
                    ExcelControl excel = new ExcelControl();

                    int curTab = tabControl.SelectedIndex;//记住当前tabPage


                    for (int i = 0; i < tabCtrlBarCause.TabCount; i++)
                    {
                        this.tabCtrlBarCause.SelectedIndex = i;
                    }
                    for (int i = 0; i < tabCtrlCausePie.TabCount; i++)
                    {
                        this.tabCtrlCausePie.SelectedIndex = i;
                    }
                    List<ChartControl> charts = new List<ChartControl>();
                    charts.Add(chartPieMainCause);
                    charts.Add(chartPieSubCause);
                    charts.Add(chartPieDetailCause);
                    charts.Add(chartBarMainCause);
                    charts.Add(chartBarSubCause);
                    charts.Add(chartBarDetailCause);
                    exportProfile2Excel(excel, "原因统计", this.gridViewCaues, charts, dir);
                    charts.Clear();
                    charts.Add(chartRegionCreate);
                    charts.Add(chartGridCreate);
                    charts.Add(chartRegionClose);
                    charts.Add(chartGridClose);
                    exportProfile2Excel(excel, "区域黑点状态统计", this.gridViewRegion, charts, dir);

                    excel.SaveFile(saveFileDialog.FileName);
                    excel.CloseExcel();

                    tabControl.SelectedIndex = curTab;     //回到最初设定的tabPage

                    MessageBox.Show("导出完毕！");
                }
                catch (Exception xx)
                {
                    MessageBox.Show(xx.ToString());
                }
                finally
                {
                    System.IO.Directory.Delete(dir.FullName, true);
                }
            }
        }

        private void exportProfile2Excel(ExcelControl excel, string sheetName, GridView gridView, List<ChartControl> charts, System.IO.DirectoryInfo dir)
        {
            Microsoft.Office.Interop.Excel.Worksheet sheet = excel.AddNewSheet(sheetName);
            if (gridView.RowCount > 0)
            {
                excel.ExportExcel(sheet, gridView);
            }
            else
            {
                return;
            }
            int imgRowIdx = gridView.RowCount + 2;
            for (int i = 0; i < charts.Count; i++)
            {
                if (i > 0)
                {
                    imgRowIdx += 25;
                }
                string fileName = dir.FullName + System.IO.Path.DirectorySeparatorChar + sheetName + i.ToString() + ".bmp";
                ChartControl chart = charts[i];
                chart.ExportToImage(fileName, System.Drawing.Imaging.ImageFormat.Bmp);
                float imgWidth = chart.Width / 96f * 72f;
                float imgHeigh = chart.Height / 96f * 72f;
                excel.InsertPicture(sheet, imgRowIdx, fileName, imgWidth, imgHeigh);
            }
        }

        //private bool hasChart(ChartControl chartControl)
        //{
        //    if (chartControl == null)
        //    {
        //        return false;
        //    }
        //    for (int i = 0; i < chartControl.Series.Count; i++)
        //    {
        //        for (int j = 0; j < chartControl.Series[i].Points.Count; j++)
        //        {
        //            for (int k = 0; k < chartControl.Series[i].Points[j].Length; k++)
        //            {
        //                if (chartControl.Series[i].Points[j][k] > 0)
        //                {
        //                    return true;
        //                }
        //            }
        //        }
        //    }
        //    return false;
        //}


    }
}
