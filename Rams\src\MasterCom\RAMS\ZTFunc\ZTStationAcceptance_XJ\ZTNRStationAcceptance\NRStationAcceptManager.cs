﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class NRStationAcceptManager : StationAcceptManagerBase
    {
        protected override string templateFileName { get { return "NR新站验收模板.xlsx"; } }
        protected override string reportFileName { get { return "NR新站验收"; } }

        protected string picPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"userdata\NROptimizationcCusters\Pictures");

        public override void SetAcceptCond(StationAcceptConditionBase cond)
        {
            Singleton<NRStationAcceptConfigHelper>.Instance.LoadConfig();
            //workDir = Singleton<NRStationAcceptConfigHelper>.Instance.ModelPath;
            workDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/NRStationAcceptance");
            string picPath = Path.Combine(workDir, "Pictures");
            acceptCond = cond;

            acceptorList = new List<StationAcceptBase>()
            {
                new NRAcpAccRate(),
                new NRAcpGoodPointFtpDownload(),
                new NRAcpMiddlePointFtpDownload(),
                new NRAcpBadPointFtpDownload(),
                new NRAcpGoodPointFtpUpload(),
                new NRAcpMiddlePointFtpUpload(),
                new NRAcpBadPointFtpUpload(),
                new NRAcpEPSFBRate(),
                new NRAcpEPSFBDelay(),
                new NRAcpPingDelay(),
                new NRVONRRate(),
                new NRAcpHandoverRate(),
                new NRAcpCoverPic(picPath),
                new NRAcpHandoverPic(picPath),
                new NRAcpBtsOutdoorScenePic(),
                new NRAcpBtsAlarm(),
                new NRAcpBtsBaseConfig()
            };

            NRStationAcceptAlarmHelper.DealAlarmInfo();
        }

        protected override string getPath()
        {
            return Singleton<NRStationAcceptConfigHelper>.Instance.GetCurSavePath();
        }

        public override void AnalyzeFile(Model.FileInfo fileInfo, DTFileDataManager fileManager)
        {
            try
            {
                string errMsg = "";
                bool isHandoverFile = fileManager.FileName.Contains("切换");
                NRCell targetCell = getTargetCell(fileManager, isHandoverFile, out errMsg);
                if (!string.IsNullOrEmpty(errMsg))
                {
                    errMsg = $"文件{fileInfo.Name}未找到目标小区;{errMsg}";
                    log.Info(errMsg);
                    ErrMsg.AppendLine(errMsg);
                    return;
                }

                string fileBtsName = NRStationAcceptFileNameHelper.GetFileBtsName(targetCell, fileManager.FileName, isHandoverFile, out errMsg);
                if (!string.IsNullOrEmpty(errMsg))
                {
                    errMsg = $"文件{fileInfo.Name}未找到目标基站;{errMsg}";
                    log.Info(errMsg);
                    ErrMsg.AppendLine(errMsg);
                    return;
                }

                analyzeFile(fileInfo, fileManager, targetCell, fileBtsName, isHandoverFile);
            }
            catch (Exception e)
            {
                string errMsg = $"[{fileInfo.Name}]文件分析时产生异常;";
                log.Error(string.Format("{0} : {1}", errMsg, e.StackTrace));
                ErrMsg.AppendLine(errMsg);
            }
        }

        protected void analyzeFile(Model.FileInfo fileInfo, DTFileDataManager fileManager, NRCell targetCell, string fileBtsName, bool isHandoverFile)
        {
            NRStationAcceptCondition curCond = acceptCond as NRStationAcceptCondition;
            //curCond.NRServiceType = NRDTDataHelper.getServiceNameFromTypeID(fileInfo.ServiceType);
            curCond.NRServiceType = NRStationAcceptFileNameHelper.GetServiceName(fileInfo.Name);
            if (curCond.NRServiceType == NRServiceName.NULL)
            {
                string errMsg = $"[{fileInfo.Name}]不为SA或NSA文件";
                log.Debug(errMsg);
                ErrMsg.AppendLine(errMsg);
                return;
            }
            string btsName = targetCell.BelongBTS.Name;
            if (!BtsInfoDic.TryGetValue(btsName, out BtsInfoBase bts))
            {
                bts = initBtsInfo(targetCell, fileBtsName);
                BtsInfoDic.Add(btsName, bts);
            }
            NRBtsInfo nrBtsInfo = bts as NRBtsInfo;
            nrBtsInfo.Init(curCond.NRServiceType);
            nrBtsInfo.CurFileBtsName = fileBtsName;

            if (!bts.CellInfoDic.TryGetValue(targetCell.Name, out CellInfoBase cell))
            {
                cell = initCellInfo(targetCell);
                if (!isHandoverFile)
                {
                    //切换文件不增加小区结果数
                    bts.CellInfoDic.Add(targetCell.Name, cell);
                }
            }
            NRCellInfo nrCellInfo = cell as NRCellInfo;
            nrCellInfo.Init(targetCell, curCond.NRServiceType);

            foreach (StationAcceptBase acp in acceptorList)
            {
                acp.AnalyzeFile(fileInfo, fileManager, bts, cell, curCond);
            }
        }

        protected virtual BtsInfoBase initBtsInfo(NRCell targetCell, string fileBtsName)
        {
            return new NRBtsInfo(targetCell.BelongBTS);
        }

        protected virtual CellInfoBase initCellInfo(NRCell targetCell)
        {
            return new NRCellInfo(targetCell);
        }

        protected NRCell getTargetCell(DTFileDataManager fileManager, bool isHandoverFile, out string errMsg)
        {
            NRCell targeCell = null;
            errMsg = "TAC,ECI或ARFCN,PCI无法关联到宏站小区";
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                NRCell cell = StationAcceptCellHelper_XJ.Instance.GetNRCell(tp);
                if (cell != null && cell.BelongBTS.Type == NRBTSType.Outdoor)
                {
                    string keyStr = cell.Name;
                    if (isHandoverFile)
                    {
                        keyStr = NRStationAcceptFileNameHelper.GetFileBtsName(cell, fileManager.FileName, isHandoverFile, out errMsg);
                    }
                    if (!string.IsNullOrEmpty(keyStr) && fileManager.FileName.Contains(keyStr.Trim()))
                    {
                        targeCell = cell;
                        errMsg = "";
                        break;
                    }
                    errMsg = "文件名不包含工参小区名";
                }
            }
            return targeCell;
        }

        protected override void verifyResult()
        {
            //foreach (var btsInfo in BtsInfoDic.Values)
            //{
            //    NRBtsInfo nrBtsInfo = btsInfo as NRBtsInfo;
            //    foreach (var cellInfo in nrBtsInfo.CellInfoList)
            //    {
            //        //
            //    }
            //}
        }

        protected override void getExportedFiles(StringBuilder exportedFiles, BtsInfoBase bts)
        {
            NRBtsInfo nrBtsInfo = bts as NRBtsInfo;
            exportedFiles.Append(nrBtsInfo.BtsName);
            exportedFiles.Append(",");
        }

        protected override void fillResult(BtsInfoBase bts, Excel.Workbook eBook)
        {
            fillHomePage(eBook, bts);                     //Sheets[2]
            fillNetOptimizationTestPageSA(eBook, bts);    //Sheets[3]
            fillCoverPicPageSA(eBook, bts);               //Sheets[4]
            fillNetOptimizationTestPageNSA(eBook, bts);   //Sheets[5]
            fillCoverPicPageNSA(eBook, bts);
            fillCoverPic(eBook, bts);
            fillAlarmInfo(eBook, bts);
        }

        //根据是否合格填充单元格,不合格填充为红色
        protected void setCellValue<T>(Excel.Range range, int rowIndex, int colIndex, T data, bool isValid)
        {
            range[rowIndex, colIndex] = data;
            if (!isValid)
            {
                Excel.Range r = range[rowIndex, colIndex] as Excel.Range;
                r.Font.ColorIndex = 3;
            }
        }

        protected void setCellValue(Excel.Range range, int rowIndex, int colIndex, bool isValid)
        {
            if (isValid)
            {
                range[rowIndex, colIndex] = "是";
            }
            else
            {
                range[rowIndex, colIndex] = "否";
                Excel.Range r = range[rowIndex, colIndex] as Excel.Range;
                r.Font.ColorIndex = 3;
            }
        }

        #region fillHomePage
        private void fillHomePage(Excel.Workbook eBook, BtsInfoBase btsInfo)
        {
            Excel.Worksheet sheet = (Excel.Worksheet)eBook.Sheets[2];
            sheet.get_Range("C5").set_Value(Type.Missing, btsInfo.BtsName);

            NRBtsInfo nrBtsInfo = btsInfo as NRBtsInfo;
            if (nrBtsInfo.BtsBaseInfo != null)
            {
                NRBtsParameters nrBaseInfo = nrBtsInfo.BtsBaseInfo as NRBtsParameters;

                sheet.get_Range("T5").set_Value(Type.Missing, nrBaseInfo.ENodeBID);
                sheet.get_Range("C7").set_Value(Type.Missing, nrBaseInfo.Address);
                sheet.get_Range("T7").set_Value(Type.Missing, nrBaseInfo.Country);

                setCellValue(sheet.Cells, 11, 6, nrBaseInfo.BtsLongutide.Planing, nrBaseInfo.BtsLongutide.IsValid);
                setCellValue(sheet.Cells, 11, 9, nrBaseInfo.BtsLatitude.Planing, nrBaseInfo.BtsLatitude.IsValid);
                setCellValue(sheet.Cells, 11, 12, nrBaseInfo.BtsLongutide.Real, nrBaseInfo.BtsLongutide.IsValid);
                setCellValue(sheet.Cells, 11, 16, nrBaseInfo.BtsLatitude.Real, nrBaseInfo.BtsLatitude.IsValid);
                setCellValue(sheet.Cells, 11, 19, nrBaseInfo.BtsLongutide.IsValid && nrBaseInfo.BtsLatitude.IsValid);
                setCellValue(sheet.Cells, 12, 6, nrBaseInfo.CellCount.Planing, nrBaseInfo.CellCount.IsValid);
                setCellValue(sheet.Cells, 12, 12, nrBaseInfo.CellCount.Real, nrBaseInfo.CellCount.IsValid);
                setCellValue(sheet.Cells, 12, 19, nrBaseInfo.CellCount.IsValid);
                setCellValue(sheet.Cells, 13, 6, nrBaseInfo.TAC.Planing, nrBaseInfo.TAC.IsValid);
                setCellValue(sheet.Cells, 13, 12, nrBaseInfo.TAC.Real, nrBaseInfo.TAC.IsValid);
                setCellValue(sheet.Cells, 13, 19, nrBaseInfo.TAC.IsValid);

                ExcelCell antennaInfo = new ExcelCell(16, 6, 10, 5);
                setCellResInfo(sheet, antennaInfo, new List<NRCellParameters>(nrBaseInfo.CellDic.Values), setCellAntennaInfo);

                ExcelCell projectInfo = new ExcelCell(48, 6, 12, 5);
                setCellResInfo(sheet, projectInfo, new List<NRCellParameters>(nrBaseInfo.CellDic.Values), setCellProjectInfo);
            }

            ExcelCell fileAnaInfo = new ExcelCell(96, 6, 6, 4);
            setCellResInfo(sheet, fileAnaInfo, btsInfo.CellInfoList, setFileAnaResult);
        }

        delegate void SetResFunc<in T>(Excel.Worksheet sheet, int rowIdx, int colIdx, T nrCellInfo);
        private void setCellResInfo<T>(Excel.Worksheet sheet, ExcelCell info, List<T> dataList, SetResFunc<T> func)
        {
            int cellIndex = 0;
            int rowIdx = info.FstRowIdx;
            int colIdx;
            foreach (var cellInfo in dataList)
            {
                if (cellIndex % 3 == 0)
                {
                    rowIdx += info.RowInterval * (cellIndex / 3);
                    colIdx = info.FstColIdx;
                }
                else
                {
                    colIdx = info.FstColIdx + ((cellIndex % 3) * info.ColInterval);
                }

                func(sheet, rowIdx, colIdx, cellInfo);
                cellIndex++;
            }
        }

        private void setCellAntennaInfo(Excel.Worksheet sheet, int rowIdx, int colIdx, NRCellParameters cellInfo)
        {
            setCellValue(sheet.Cells, rowIdx, colIdx, cellInfo.CellName, true);
            setCellBaseInfo(sheet, rowIdx + 2, colIdx, cellInfo.Longitude);
            setCellBaseInfo(sheet, rowIdx + 3, colIdx, cellInfo.Latitude);
            setCellBaseInfo(sheet, rowIdx + 4, colIdx, cellInfo.Altitude);
            setCellBaseInfo(sheet, rowIdx + 5, colIdx, cellInfo.Direction);
            setCellBaseInfo(sheet, rowIdx + 6, colIdx, cellInfo.Downtilt);
            setCellBaseInfo(sheet, rowIdx + 7, colIdx, cellInfo.MechanicalTilt);
            setCellBaseInfo(sheet, rowIdx + 8, colIdx, cellInfo.Downward);
            setCellBaseInfo(sheet, rowIdx + 9, colIdx, cellInfo.Channels);
        }

        private void setCellProjectInfo(Excel.Worksheet sheet, int rowIdx, int colIdx, NRCellParameters cellInfo)
        {
            setCellValue(sheet.Cells, rowIdx, colIdx, cellInfo.CellName, true);
            setCellBaseInfo(sheet, rowIdx + 2, colIdx, cellInfo.CellID);
            setCellBaseInfo(sheet, rowIdx + 3, colIdx, cellInfo.PCI);
            setCellBaseInfo(sheet, rowIdx + 4, colIdx, cellInfo.FreqBand);
            setCellBaseInfo(sheet, rowIdx + 5, colIdx, cellInfo.Freq);
            setCellBaseInfo(sheet, rowIdx + 6, colIdx, cellInfo.SSBFreq);
            setCellBaseInfo(sheet, rowIdx + 7, colIdx, cellInfo.Bandwidth);
            setCellBaseInfo(sheet, rowIdx + 8, colIdx, cellInfo.PRACH);
            setCellBaseInfo(sheet, rowIdx + 9, colIdx, cellInfo.SubFrameRatio);
            setCellBaseInfo(sheet, rowIdx + 10, colIdx, cellInfo.AAUCount);
            setCellBaseInfo(sheet, rowIdx + 11, colIdx, cellInfo.CoreMode);
        }

        private void setCellBaseInfo<T>(Excel.Worksheet sheet, int rowIdx, int colIdx, ParamInfo<T> info)
        {
            setCellValue(sheet.Cells, rowIdx, colIdx, info.Planing, info.IsValid);
            setCellValue(sheet.Cells, rowIdx, colIdx + 2, info.Real, info.IsValid);
            setCellValue(sheet.Cells, rowIdx, colIdx + 4, info.IsValid);
        }

        private void setFileAnaResult(Excel.Worksheet sheet, int rowIdx, int colIdx, CellInfoBase cellInfo)
        {
            NRCellInfo nrCellInfo = cellInfo as NRCellInfo;
            if (nrCellInfo.SAInfo != null)
            {
                setCellValue(sheet.Cells, rowIdx, colIdx, nrCellInfo.SAInfo.AccessInfo.IsValid);
                setCellValue(sheet.Cells, rowIdx + 1, colIdx, nrCellInfo.SAInfo.SmallPackageDelay.IsValid
                    && nrCellInfo.SAInfo.BigPackageDelay.IsValid);
                setCellValue(sheet.Cells, rowIdx + 2, colIdx, nrCellInfo.SAInfo.GoodSampleDL.IsValid && nrCellInfo.SAInfo.MiddleSampleDL.IsValid && nrCellInfo.SAInfo.BadSampleDL.IsValid);
                setCellValue(sheet.Cells, rowIdx + 3, colIdx, nrCellInfo.SAInfo.GoodSampleUL.IsValid && nrCellInfo.SAInfo.MiddleSampleUL.IsValid && nrCellInfo.SAInfo.BadSampleUL.IsValid);
                setCellValue(sheet.Cells, rowIdx + 4, colIdx, nrCellInfo.SAInfo.EPSFBInfo.IsValid
                    && nrCellInfo.SAInfo.EPSFBDelay.IsValid);
            }

            colIdx += 2;
            if (nrCellInfo.NSAInfo != null)
            {
                setCellValue(sheet.Cells, rowIdx, colIdx, nrCellInfo.NSAInfo.AccessInfo.IsValid);
                setCellValue(sheet.Cells, rowIdx + 1, colIdx, nrCellInfo.NSAInfo.SmallPackageDelay.IsValid
                    && nrCellInfo.NSAInfo.BigPackageDelay.IsValid);
                setCellValue(sheet.Cells, rowIdx + 2, colIdx, nrCellInfo.NSAInfo.GoodSampleDL.IsValid && nrCellInfo.NSAInfo.MiddleSampleDL.IsValid && nrCellInfo.NSAInfo.BadSampleDL.IsValid);
                setCellValue(sheet.Cells, rowIdx + 3, colIdx, nrCellInfo.NSAInfo.GoodSampleUL.IsValid && nrCellInfo.NSAInfo.MiddleSampleUL.IsValid && nrCellInfo.NSAInfo.BadSampleUL.IsValid);
                setCellValue(sheet.Cells, rowIdx + 4, colIdx, nrCellInfo.NSAInfo.EPSFBInfo.IsValid
                    && nrCellInfo.NSAInfo.EPSFBDelay.IsValid);
            }
        }
        #endregion

        #region fillNetOptimizationTestPage
        private void fillNetOptimizationTestPageSA(Excel.Workbook eBook, BtsInfoBase btsInfo)
        {
            Excel.Worksheet sheet = (Excel.Worksheet)eBook.Sheets[3];
            sheet.get_Range("A1").set_Value(Type.Missing, btsInfo.BtsName + "-NR单站验证测试表格");
            sheet.get_Range("C2").set_Value(Type.Missing, btsInfo.BtsName);
            sheet.get_Range("S2").set_Value(Type.Missing, btsInfo.BtsID);
            sheet.get_Range("AC2").set_Value(Type.Missing, DateTime.Now.ToShortDateString());

            int cellIndex = 0;
            foreach (var cellInfo in btsInfo.CellInfoList)
            {
                NRCellInfo nrCellInfo = cellInfo as NRCellInfo;
                if (nrCellInfo.SAInfo == null)
                {
                    continue;
                }

                int rowIdx = 7 + (cellIndex * 15);

                setCellValue(sheet.Cells, rowIdx, 1, nrCellInfo.SAInfo.Cell.Name, true);

                //Access Success Rate
                rowIdx = rowIdx + 1;
                setSuccessRateKpiInfo(sheet, rowIdx, nrCellInfo.SAInfo.AccessInfo);

                //Ping
                rowIdx = rowIdx + 2;
                setCellValue(sheet.Cells, rowIdx, 16, nrCellInfo.SAInfo.SmallPackageDelay.Data, nrCellInfo.SAInfo.SmallPackageDelay.IsValid);
                setCellValue(sheet.Cells, rowIdx, 29, nrCellInfo.SAInfo.BigPackageDelay.Data, nrCellInfo.SAInfo.BigPackageDelay.IsValid);

                rowIdx = rowIdx + 2;
                //好点
                setFtpPointInfo(sheet, rowIdx, 16, nrCellInfo.SAInfo.GoodSampleDL);
                setFtpPointInfo(sheet, rowIdx + 3, 16, nrCellInfo.SAInfo.GoodSampleUL);
                //中点
                setFtpPointInfo(sheet, rowIdx, 25, nrCellInfo.SAInfo.MiddleSampleDL);
                setFtpPointInfo(sheet, rowIdx + 3, 25, nrCellInfo.SAInfo.MiddleSampleUL);
                //差点
                setFtpPointInfo(sheet, rowIdx, 33, nrCellInfo.SAInfo.BadSampleDL);
                setFtpPointInfo(sheet, rowIdx + 3, 33, nrCellInfo.SAInfo.BadSampleUL);

                //端到端呼叫成功率
                rowIdx = rowIdx + 7;
                setSuccessRateKpiInfo(sheet, rowIdx, nrCellInfo.SAInfo.EPSFBInfo);

                //呼叫时延（5G-5G）
                rowIdx = rowIdx + 1;
                setCellValue(sheet.Cells, rowIdx, 16, nrCellInfo.SAInfo.EPSFBDelay.Data, nrCellInfo.SAInfo.EPSFBDelay.IsValid);

                //接通率(VONR-VONR)
                rowIdx = rowIdx + 1;
                setSuccessRateKpiInfo(sheet, rowIdx, nrCellInfo.SAInfo.VONRInfo);

                cellIndex++;
            }

            //切换
            NRBtsInfo nrBtsInfo = btsInfo as NRBtsInfo;
            if (nrBtsInfo.SABtsInfo == null)
            {
                return;
            }

            int btsRowIdx = 142;
            foreach (var info in nrBtsInfo.SABtsInfo.FileBtsHandOverInfoList)
            {
                setSuccessRateKpiInfo(sheet, btsRowIdx, info.HandoverRate);
                btsRowIdx++;
            }
        }

        private void fillNetOptimizationTestPageNSA(Excel.Workbook eBook, BtsInfoBase btsInfo)
        {
            Excel.Worksheet sheet = (Excel.Worksheet)eBook.Sheets[5];
            sheet.get_Range("A1").set_Value(Type.Missing, btsInfo.BtsName + "-NR单站验证测试表格");
            sheet.get_Range("C2").set_Value(Type.Missing, btsInfo.BtsName);
            sheet.get_Range("S2").set_Value(Type.Missing, btsInfo.BtsID);
            sheet.get_Range("AC2").set_Value(Type.Missing, DateTime.Now.ToShortDateString());

            int cellIndex = 0;
            foreach (var cellInfo in btsInfo.CellInfoList)
            {
                NRCellInfo nrCellInfo = cellInfo as NRCellInfo;
                if (nrCellInfo.NSAInfo == null)
                {
                    continue;
                }

                int rowIdx = 7 + (cellIndex * 11);

                setCellValue(sheet.Cells, rowIdx, 1, nrCellInfo.NSAInfo.Cell.Name, true);

                //Access Success Rate
                rowIdx = rowIdx + 1;
                setSuccessRateKpiInfo(sheet, rowIdx, nrCellInfo.NSAInfo.AccessInfo);

                //Ping
                rowIdx = rowIdx + 2;
                setCellValue(sheet.Cells, rowIdx, 16, nrCellInfo.NSAInfo.SmallPackageDelay.Data, nrCellInfo.NSAInfo.SmallPackageDelay.IsValid);
                setCellValue(sheet.Cells, rowIdx, 29, nrCellInfo.NSAInfo.BigPackageDelay.Data, nrCellInfo.NSAInfo.BigPackageDelay.IsValid);

                rowIdx = rowIdx + 2;
                //好点
                setFtpPointInfo(sheet, rowIdx, 16, nrCellInfo.NSAInfo.GoodSampleDL);
                setFtpPointInfo(sheet, rowIdx + 3, 16, nrCellInfo.NSAInfo.GoodSampleUL);
                //中点
                setFtpPointInfo(sheet, rowIdx, 25, nrCellInfo.NSAInfo.MiddleSampleDL);
                setFtpPointInfo(sheet, rowIdx + 3, 25, nrCellInfo.NSAInfo.MiddleSampleUL);
                //差点
                setFtpPointInfo(sheet, rowIdx, 33, nrCellInfo.NSAInfo.BadSampleDL);
                setFtpPointInfo(sheet, rowIdx + 3, 33, nrCellInfo.NSAInfo.BadSampleUL);

                cellIndex++;
            }

            //切换
            NRBtsInfo nrBtsInfo = btsInfo as NRBtsInfo;
            if (nrBtsInfo.NSABtsInfo == null)
            {
                return;
            }

            int btsRowIdx = 106;
            foreach (var info in nrBtsInfo.NSABtsInfo.FileBtsHandOverInfoList)
            {
                setCellValue(sheet.Cells, btsRowIdx, 5, info.FileBtsName, true);
                setSuccessRateKpiInfo(sheet, btsRowIdx, info.HandoverRate);
                btsRowIdx++;
            }
        }

        private void setFtpPointInfo(Excel.Worksheet sheet, int rowIdx, int colIdx, FtpPointInfo info)
        {
            setCellValue(sheet.Cells, rowIdx, colIdx, info.Rsrp.Data, info.Rsrp.IsValid);
            setCellValue(sheet.Cells, rowIdx + 1, colIdx, info.Sinr.Data, info.Sinr.IsValid);
            setCellValue(sheet.Cells, rowIdx + 2, colIdx, info.Throughput.Data, info.Throughput.IsValid);
        }

        private void setSuccessRateKpiInfo(Excel.Worksheet sheet, int rowIdx, SuccessRateKpiInfo rate)
        {
            setCellValue(sheet.Cells, rowIdx, 16, rate.RequestCnt, rate.IsValid);
            setCellValue(sheet.Cells, rowIdx, 23, rate.SucceedCnt, rate.IsValid);
            setCellValue(sheet.Cells, rowIdx, 29, rate.FailedCnt, rate.IsValid);
            setCellValue(sheet.Cells, rowIdx, 35, rate.SuccessRate, rate.IsValid);
        }

        #endregion

        private void fillCoverPicPageSA(Excel.Workbook eBook, BtsInfoBase btsInfo)
        {
            Excel.Worksheet picPageSheet = (Excel.Worksheet)eBook.Sheets[4];

            double width = 300;
            double height = 190;

            int cellIndex = 0;
            foreach (var cellInfo in btsInfo.CellInfoList)
            {
                NRCellInfo nrCellInfo = cellInfo as NRCellInfo;
                if (nrCellInfo.SAInfo == null)
                {
                    continue;
                }
                int paraColIndex = 1 + (cellIndex * 2);

                //SS-RSRP
                setCellValue(picPageSheet.Cells, 6, paraColIndex, nrCellInfo.SAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.SAInfo.NRRsrpPic.PicPath, 7, paraColIndex, width, height);
                //SS-SINR
                setCellValue(picPageSheet.Cells, 10, paraColIndex, nrCellInfo.SAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.SAInfo.NRSinrPic.PicPath, 11, paraColIndex, width, height);
                //小区覆盖RSRP：SSB
                setCellValue(picPageSheet.Cells, 15, paraColIndex, nrCellInfo.SAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.SAInfo.NRRsrpPic.PicPath, 16, paraColIndex, width, height);
                //小区覆盖SINR：SSB
                setCellValue(picPageSheet.Cells, 19, paraColIndex, nrCellInfo.SAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.SAInfo.NRSinrPic.PicPath, 20, paraColIndex, width, height);
                //小区上传下载速率验证
                setCellValue(picPageSheet.Cells, 23, paraColIndex, nrCellInfo.SAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.SAInfo.NRULPic.PicPath, 24, paraColIndex, width, height);
                setCellValue(picPageSheet.Cells, 25, paraColIndex, nrCellInfo.SAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.SAInfo.NRDLPic.PicPath, 26, paraColIndex, width, height);
                cellIndex++;
            }

            NRBtsInfo nrBtsInfo = btsInfo as NRBtsInfo;
            if (nrBtsInfo.SABtsInfo == null)
            {
                return;
            }

            int btsColIdx = 1;
            foreach (var info in nrBtsInfo.SABtsInfo.FileBtsHandOverInfoList)
            {
                setCellValue(picPageSheet.Cells, 27, btsColIdx, info.FileBtsName, true);
                insertExcelPicture(picPageSheet, info.PCIPicInfo.PicPath, 28, btsColIdx, 920, 290);
                btsColIdx += 6;
            }
        }

        private void fillCoverPicPageNSA(Excel.Workbook eBook, BtsInfoBase btsInfo)
        {
            Excel.Worksheet picPageSheet = (Excel.Worksheet)eBook.Sheets[6];

            double width = 300;
            double height = 190;

            int cellIndex = 0;
            foreach (var cellInfo in btsInfo.CellInfoList)
            {
                NRCellInfo nrCellInfo = cellInfo as NRCellInfo;
                if (nrCellInfo.NSAInfo == null)
                {
                    continue;
                }
                int paraColIndex = 1 + (cellIndex * 2);

                //SS-RSRP
                setCellValue(picPageSheet.Cells, 8, paraColIndex, nrCellInfo.NSAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.NSAInfo.NRRsrpPic.PicPath, 9, paraColIndex, width, height);
                //SS-SINR
                setCellValue(picPageSheet.Cells, 12, paraColIndex, nrCellInfo.NSAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.NSAInfo.NRSinrPic.PicPath, 13, paraColIndex, width, height);
                //小区覆盖RSRP：SSB+锚点
                setCellValue(picPageSheet.Cells, 15, paraColIndex, nrCellInfo.NSAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.NSAInfo.LteRsrpPic.PicPath, 16, paraColIndex, width, height);
                insertExcelPicture(picPageSheet, nrCellInfo.NSAInfo.NRRsrpPic.PicPath, 17, paraColIndex, width, height);
                //小区覆盖SINR：SSB+锚点
                setCellValue(picPageSheet.Cells, 20, paraColIndex, nrCellInfo.NSAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.NSAInfo.LteSinrPic.PicPath, 21, paraColIndex, width, height);
                insertExcelPicture(picPageSheet, nrCellInfo.NSAInfo.NRSinrPic.PicPath, 22, paraColIndex, width, height);
                //小区上传下载速率验证
                setCellValue(picPageSheet.Cells, 25, paraColIndex, nrCellInfo.NSAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.NSAInfo.NRULPic.PicPath, 26, paraColIndex, width, height);
                setCellValue(picPageSheet.Cells, 27, paraColIndex, nrCellInfo.NSAInfo.Cell.Name, true);
                insertExcelPicture(picPageSheet, nrCellInfo.NSAInfo.NRDLPic.PicPath, 28, paraColIndex, width, height);
                cellIndex++;
            }

            NRBtsInfo nrBtsInfo = btsInfo as NRBtsInfo;
            if (nrBtsInfo.NSABtsInfo == null)
            {
                return;
            }
            int btsColIdx = 1;
            foreach (var info in nrBtsInfo.NSABtsInfo.FileBtsHandOverInfoList)
            {
                setCellValue(picPageSheet.Cells, 29, btsColIdx, info.FileBtsName, true);
                insertExcelPicture(picPageSheet, info.PCIPicInfo.PicPath, 30, btsColIdx, 920, 290);
                btsColIdx += 6;
            }
        }

        private void fillCoverPic(Excel.Workbook eBook, BtsInfoBase btsInfo)
        {
            NRBtsInfo nrBtsInfo = btsInfo as NRBtsInfo;
            Excel.Worksheet sheet = (Excel.Worksheet)eBook.Sheets[7];

            double width = eBook.Application.CentimetersToPoints(8.68);
            double height = eBook.Application.CentimetersToPoints(7.01);

            int btsIdx = 0;
            foreach (var info in nrBtsInfo.OutdoorScenePicInfoList)
            {
                int paraColIndex = btsIdx * 6;

                insertExcelPicture(sheet, info.PanoramicPicInfo.PicPath, 14, 1 + paraColIndex, width, height);
                insertExcelPicture(sheet, info.EntrancePicInfo.PicPath, 14, 3 + paraColIndex, width, height);
                insertExcelPicture(sheet, info.RoofPicInfo.PicPath, 14, 5 + paraColIndex, width, height);

                insertExcelPicture(sheet, info.Cell1PicInfo.PicPath, 18, 1 + paraColIndex, width, height);
                insertExcelPicture(sheet, info.Cell2PicInfo.PicPath, 18, 3 + paraColIndex, width, height);
                insertExcelPicture(sheet, info.Cell3PicInfo.PicPath, 18, 5 + paraColIndex, width, height);
                //insertExcelPicture(sheet, info.Cell4PicInfo.PicPath, 18, 7, width, height);
                //insertExcelPicture(sheet, info.Cell5PicInfo.PicPath, 18, 9, width, height);
                //insertExcelPicture(sheet, info.Cell6PicInfo.PicPath, 18, 11, width, height);
                //insertExcelPicture(sheet, info.Cell7PicInfo.PicPath, 18, 13, width, height);
                //insertExcelPicture(sheet, info.Cell8PicInfo.PicPath, 18, 15, width, height);
                //insertExcelPicture(sheet, info.Cell9PicInfo.PicPath, 18, 17, width, height);

                insertExcelPicture(sheet, info.Dir0PicInfo.PicPath, 22, 1 + paraColIndex, width, height);
                insertExcelPicture(sheet, info.Dir45PicInfo.PicPath, 22, 3 + paraColIndex, width, height);
                insertExcelPicture(sheet, info.Dir90PicInfo.PicPath, 22, 5 + paraColIndex, width, height);
                insertExcelPicture(sheet, info.Dir135PicInfo.PicPath, 24, 1 + paraColIndex, width, height);
                insertExcelPicture(sheet, info.Dir180PicInfo.PicPath, 24, 3 + paraColIndex, width, height);
                insertExcelPicture(sheet, info.Dir225PicInfo.PicPath, 24, 5 + paraColIndex, width, height);
                insertExcelPicture(sheet, info.Dir270PicInfo.PicPath, 26, 1 + paraColIndex, width, height);
                insertExcelPicture(sheet, info.Dir315PicInfo.PicPath, 26, 3 + paraColIndex, width, height);

                insertExcelPicture(sheet, info.Other1PicInfo.PicPath, 30, 1 + paraColIndex, width, height);
                insertExcelPicture(sheet, info.Other2PicInfo.PicPath, 30, 3 + paraColIndex, width, height);
                btsIdx++;
            }
        }

        private void fillAlarmInfo(Excel.Workbook eBook, BtsInfoBase btsInfo)
        {
            Excel.Worksheet sheet = (Excel.Worksheet)eBook.Sheets[8];

            NRBtsInfo nrBtsInfo = btsInfo as NRBtsInfo;
            string date = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            if (nrBtsInfo.BtsAlarmList == null || nrBtsInfo.BtsAlarmList.Count == 0)
            {
                setCellValue(sheet.Cells, 3, 1, 1, true);
                setCellValue(sheet.Cells, 3, 2, date, true);
                setCellValue(sheet.Cells, 3, 3, nrBtsInfo.BtsName, true);
                setCellValue(sheet.Cells, 3, 4, "无告警信息", true);
                return;
            }

            for (int i = 0; i < nrBtsInfo.BtsAlarmList.Count; i++)
            {
                int idx = 3 + i;
                setCellValue(sheet.Cells, idx, 1, i, true);
                setCellValue(sheet.Cells, idx, 2, date, true);
                setCellValue(sheet.Cells, idx, 3, nrBtsInfo.BtsName, true);
                setCellValue(sheet.Cells, idx, 4, nrBtsInfo.BtsAlarmList[i], true);
            }
        }

        protected override void clear()
        {
            foreach (var acp in acceptorList)
            {
                acp.Clear();
            }
        }
    }
}
