﻿namespace MasterCom.RAMS.Stat
{
    partial class HiLightDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(HiLightDlg));
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.tbxMinV = new System.Windows.Forms.TextBox();
            this.tbxMaxV = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.cbxForeColor = new System.Windows.Forms.CheckBox();
            this.cbxBgColor = new System.Windows.Forms.CheckBox();
            this.lbForeColor = new System.Windows.Forms.Label();
            this.lbBgColor = new System.Windows.Forms.Label();
            this.errProv = new System.Windows.Forms.ErrorProvider(this.components);
            ((System.ComponentModel.ISupportInitialize)(this.errProv)).BeginInit();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(58, 158);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(151, 158);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // tbxMinV
            // 
            this.tbxMinV.Location = new System.Drawing.Point(25, 23);
            this.tbxMinV.Name = "tbxMinV";
            this.tbxMinV.Size = new System.Drawing.Size(71, 21);
            this.tbxMinV.TabIndex = 2;
            // 
            // tbxMaxV
            // 
            this.tbxMaxV.Location = new System.Drawing.Point(155, 23);
            this.tbxMaxV.Name = "tbxMaxV";
            this.tbxMaxV.Size = new System.Drawing.Size(71, 21);
            this.tbxMaxV.TabIndex = 2;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(102, 26);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(47, 12);
            this.label1.TabIndex = 3;
            this.label1.Text = "≤ X ≤";
            // 
            // cbxForeColor
            // 
            this.cbxForeColor.AutoSize = true;
            this.cbxForeColor.Location = new System.Drawing.Point(24, 65);
            this.cbxForeColor.Name = "cbxForeColor";
            this.cbxForeColor.Size = new System.Drawing.Size(72, 16);
            this.cbxForeColor.TabIndex = 4;
            this.cbxForeColor.Text = "文字颜色";
            this.cbxForeColor.UseVisualStyleBackColor = true;
            // 
            // cbxBgColor
            // 
            this.cbxBgColor.AutoSize = true;
            this.cbxBgColor.Location = new System.Drawing.Point(25, 106);
            this.cbxBgColor.Name = "cbxBgColor";
            this.cbxBgColor.Size = new System.Drawing.Size(72, 16);
            this.cbxBgColor.TabIndex = 4;
            this.cbxBgColor.Text = "背景颜色";
            this.cbxBgColor.UseVisualStyleBackColor = true;
            // 
            // lbForeColor
            // 
            this.lbForeColor.AccessibleRole = System.Windows.Forms.AccessibleRole.None;
            this.lbForeColor.BackColor = System.Drawing.Color.Black;
            this.lbForeColor.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.lbForeColor.Location = new System.Drawing.Point(108, 56);
            this.lbForeColor.Name = "lbForeColor";
            this.lbForeColor.Size = new System.Drawing.Size(41, 25);
            this.lbForeColor.TabIndex = 27;
            this.lbForeColor.Click += new System.EventHandler(this.lbForeColor_Click);
            // 
            // lbBgColor
            // 
            this.lbBgColor.AccessibleRole = System.Windows.Forms.AccessibleRole.None;
            this.lbBgColor.BackColor = System.Drawing.Color.White;
            this.lbBgColor.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.lbBgColor.Location = new System.Drawing.Point(108, 97);
            this.lbBgColor.Name = "lbBgColor";
            this.lbBgColor.Size = new System.Drawing.Size(41, 25);
            this.lbBgColor.TabIndex = 27;
            this.lbBgColor.Click += new System.EventHandler(this.lbBgColor_Click);
            // 
            // errProv
            // 
            this.errProv.ContainerControl = this;
            // 
            // HiLightDlg
            // 
            this.Appearance.BackColor = System.Drawing.SystemColors.Control;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("HiLightDlg.Appearance.Image")));
            this.Appearance.Options.UseBackColor = true;
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(255, 193);
            this.Controls.Add(this.lbBgColor);
            this.Controls.Add(this.lbForeColor);
            this.Controls.Add(this.cbxBgColor);
            this.Controls.Add(this.cbxForeColor);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.tbxMaxV);
            this.Controls.Add(this.tbxMinV);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "HiLightDlg";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "高亮显示设置";
            ((System.ComponentModel.ISupportInitialize)(this.errProv)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.TextBox tbxMinV;
        private System.Windows.Forms.TextBox tbxMaxV;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.CheckBox cbxForeColor;
        private System.Windows.Forms.CheckBox cbxBgColor;
        private System.Windows.Forms.Label lbForeColor;
        private System.Windows.Forms.Label lbBgColor;
        private System.Windows.Forms.ErrorProvider errProv;
    }
}