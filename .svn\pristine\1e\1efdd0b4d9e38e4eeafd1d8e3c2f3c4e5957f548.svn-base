﻿namespace MasterCom.RAMS.Func
{
    partial class CellFusionDataForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CellFusionDataForm));
            this.tabCtrl = new System.Windows.Forms.TabControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.TsMenuItemKpiSet = new System.Windows.Forms.ToolStripMenuItem();
            this.TsMenuItemTopFront = new System.Windows.Forms.ToolStripMenuItem();
            this.TsMenuItemExportAll = new System.Windows.Forms.ToolStripMenuItem();
            this.pageAlarm = new System.Windows.Forms.TabPage();
            this.gridCtrlAlarm = new DevExpress.XtraGrid.GridControl();
            this.gvAlarm = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.pagePerf = new System.Windows.Forms.TabPage();
            this.gridCtrlPerf = new DevExpress.XtraGrid.GridControl();
            this.gvPerf = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.pageArg = new System.Windows.Forms.TabPage();
            this.gridCtrlArg = new DevExpress.XtraGrid.GridControl();
            this.gvArg = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.TsMenuItemReQuery = new System.Windows.Forms.ToolStripMenuItem();
            this.tabCtrl.SuspendLayout();
            this.contextMenuStrip1.SuspendLayout();
            this.pageAlarm.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlAlarm)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvAlarm)).BeginInit();
            this.pagePerf.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlPerf)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvPerf)).BeginInit();
            this.pageArg.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlArg)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvArg)).BeginInit();
            this.SuspendLayout();
            // 
            // tabCtrl
            // 
            this.tabCtrl.ContextMenuStrip = this.contextMenuStrip1;
            this.tabCtrl.Controls.Add(this.pageAlarm);
            this.tabCtrl.Controls.Add(this.pagePerf);
            this.tabCtrl.Controls.Add(this.pageArg);
            this.tabCtrl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabCtrl.Location = new System.Drawing.Point(0, 0);
            this.tabCtrl.Name = "tabCtrl";
            this.tabCtrl.SelectedIndex = 0;
            this.tabCtrl.Size = new System.Drawing.Size(584, 430);
            this.tabCtrl.TabIndex = 0;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.TsMenuItemKpiSet,
            this.TsMenuItemTopFront,
            this.TsMenuItemExportAll,
            this.TsMenuItemReQuery});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(173, 114);
            // 
            // TsMenuItemKpiSet
            // 
            this.TsMenuItemKpiSet.Name = "TsMenuItemKpiSet";
            this.TsMenuItemKpiSet.Size = new System.Drawing.Size(172, 22);
            this.TsMenuItemKpiSet.Text = "显示列设置";
            this.TsMenuItemKpiSet.Click += new System.EventHandler(this.TsMenuItemKpiSet_Click);
            // 
            // TsMenuItemTopFront
            // 
            this.TsMenuItemTopFront.Name = "TsMenuItemTopFront";
            this.TsMenuItemTopFront.Size = new System.Drawing.Size(172, 22);
            this.TsMenuItemTopFront.Text = "窗口弹出到最前";
            this.TsMenuItemTopFront.Click += new System.EventHandler(this.TsMenuItemTopFront_Click);
            // 
            // TsMenuItemExportAll
            // 
            this.TsMenuItemExportAll.Name = "TsMenuItemExportAll";
            this.TsMenuItemExportAll.Size = new System.Drawing.Size(172, 22);
            this.TsMenuItemExportAll.Text = "导出到Excel";
            this.TsMenuItemExportAll.Click += new System.EventHandler(this.TsMenuItemExportAll_Click);
            // 
            // pageAlarm
            // 
            this.pageAlarm.Controls.Add(this.gridCtrlAlarm);
            this.pageAlarm.Location = new System.Drawing.Point(4, 23);
            this.pageAlarm.Name = "pageAlarm";
            this.pageAlarm.Padding = new System.Windows.Forms.Padding(3);
            this.pageAlarm.Size = new System.Drawing.Size(576, 403);
            this.pageAlarm.TabIndex = 0;
            this.pageAlarm.Text = "告警";
            this.pageAlarm.UseVisualStyleBackColor = true;
            // 
            // gridCtrlAlarm
            // 
            this.gridCtrlAlarm.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrlAlarm.Location = new System.Drawing.Point(3, 3);
            this.gridCtrlAlarm.MainView = this.gvAlarm;
            this.gridCtrlAlarm.Name = "gridCtrlAlarm";
            this.gridCtrlAlarm.ShowOnlyPredefinedDetails = true;
            this.gridCtrlAlarm.Size = new System.Drawing.Size(570, 397);
            this.gridCtrlAlarm.TabIndex = 0;
            this.gridCtrlAlarm.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvAlarm});
            // 
            // gvAlarm
            // 
            this.gvAlarm.Appearance.FocusedCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvAlarm.Appearance.FocusedCell.ForeColor = System.Drawing.Color.White;
            this.gvAlarm.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gvAlarm.Appearance.FocusedCell.Options.UseForeColor = true;
            this.gvAlarm.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvAlarm.Appearance.FocusedRow.ForeColor = System.Drawing.Color.White;
            this.gvAlarm.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gvAlarm.Appearance.FocusedRow.Options.UseForeColor = true;
            this.gvAlarm.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvAlarm.Appearance.SelectedRow.ForeColor = System.Drawing.Color.White;
            this.gvAlarm.Appearance.SelectedRow.Options.UseBackColor = true;
            this.gvAlarm.Appearance.SelectedRow.Options.UseForeColor = true;
            this.gvAlarm.GridControl = this.gridCtrlAlarm;
            this.gvAlarm.Name = "gvAlarm";
            this.gvAlarm.OptionsBehavior.Editable = false;
            this.gvAlarm.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gvAlarm.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gvAlarm.OptionsSelection.EnableAppearanceHideSelection = false;
            this.gvAlarm.OptionsView.ShowGroupPanel = false;
            this.gvAlarm.RowClick += new DevExpress.XtraGrid.Views.Grid.RowClickEventHandler(this.gvFusion_RowClick);
            this.gvAlarm.CustomDrawEmptyForeground += new DevExpress.XtraGrid.Views.Base.CustomDrawEventHandler(this.gvAlarm_CustomDrawEmptyForeground);
            this.gvAlarm.DoubleClick += new System.EventHandler(this.gvFusion_DoubleClick);
            // 
            // pagePerf
            // 
            this.pagePerf.Controls.Add(this.gridCtrlPerf);
            this.pagePerf.Location = new System.Drawing.Point(4, 23);
            this.pagePerf.Name = "pagePerf";
            this.pagePerf.Padding = new System.Windows.Forms.Padding(3);
            this.pagePerf.Size = new System.Drawing.Size(576, 403);
            this.pagePerf.TabIndex = 1;
            this.pagePerf.Text = "性能";
            this.pagePerf.UseVisualStyleBackColor = true;
            // 
            // gridCtrlPerf
            // 
            this.gridCtrlPerf.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrlPerf.Location = new System.Drawing.Point(3, 3);
            this.gridCtrlPerf.MainView = this.gvPerf;
            this.gridCtrlPerf.Name = "gridCtrlPerf";
            this.gridCtrlPerf.ShowOnlyPredefinedDetails = true;
            this.gridCtrlPerf.Size = new System.Drawing.Size(570, 397);
            this.gridCtrlPerf.TabIndex = 1;
            this.gridCtrlPerf.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvPerf});
            // 
            // gvPerf
            // 
            this.gvPerf.Appearance.FocusedCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvPerf.Appearance.FocusedCell.ForeColor = System.Drawing.Color.White;
            this.gvPerf.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gvPerf.Appearance.FocusedCell.Options.UseForeColor = true;
            this.gvPerf.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvPerf.Appearance.FocusedRow.ForeColor = System.Drawing.Color.White;
            this.gvPerf.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gvPerf.Appearance.FocusedRow.Options.UseForeColor = true;
            this.gvPerf.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvPerf.Appearance.SelectedRow.ForeColor = System.Drawing.Color.White;
            this.gvPerf.Appearance.SelectedRow.Options.UseBackColor = true;
            this.gvPerf.Appearance.SelectedRow.Options.UseForeColor = true;
            this.gvPerf.GridControl = this.gridCtrlPerf;
            this.gvPerf.Name = "gvPerf";
            this.gvPerf.OptionsBehavior.Editable = false;
            this.gvPerf.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gvPerf.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gvPerf.OptionsSelection.EnableAppearanceHideSelection = false;
            this.gvPerf.OptionsView.ShowGroupPanel = false;
            this.gvPerf.RowClick += new DevExpress.XtraGrid.Views.Grid.RowClickEventHandler(this.gvFusion_RowClick);
            this.gvPerf.CustomDrawEmptyForeground += new DevExpress.XtraGrid.Views.Base.CustomDrawEventHandler(this.gvPerf_CustomDrawEmptyForeground);
            this.gvPerf.DoubleClick += new System.EventHandler(this.gvFusion_DoubleClick);
            // 
            // pageArg
            // 
            this.pageArg.Controls.Add(this.gridCtrlArg);
            this.pageArg.Location = new System.Drawing.Point(4, 23);
            this.pageArg.Name = "pageArg";
            this.pageArg.Padding = new System.Windows.Forms.Padding(3);
            this.pageArg.Size = new System.Drawing.Size(576, 403);
            this.pageArg.TabIndex = 2;
            this.pageArg.Text = "参数";
            this.pageArg.UseVisualStyleBackColor = true;
            // 
            // gridCtrlArg
            // 
            this.gridCtrlArg.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrlArg.Location = new System.Drawing.Point(3, 3);
            this.gridCtrlArg.MainView = this.gvArg;
            this.gridCtrlArg.Name = "gridCtrlArg";
            this.gridCtrlArg.ShowOnlyPredefinedDetails = true;
            this.gridCtrlArg.Size = new System.Drawing.Size(570, 397);
            this.gridCtrlArg.TabIndex = 2;
            this.gridCtrlArg.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvArg});
            // 
            // gvArg
            // 
            this.gvArg.Appearance.FocusedCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvArg.Appearance.FocusedCell.ForeColor = System.Drawing.Color.White;
            this.gvArg.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gvArg.Appearance.FocusedCell.Options.UseForeColor = true;
            this.gvArg.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvArg.Appearance.FocusedRow.ForeColor = System.Drawing.Color.White;
            this.gvArg.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gvArg.Appearance.FocusedRow.Options.UseForeColor = true;
            this.gvArg.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvArg.Appearance.SelectedRow.ForeColor = System.Drawing.Color.White;
            this.gvArg.Appearance.SelectedRow.Options.UseBackColor = true;
            this.gvArg.Appearance.SelectedRow.Options.UseForeColor = true;
            this.gvArg.GridControl = this.gridCtrlArg;
            this.gvArg.Name = "gvArg";
            this.gvArg.OptionsBehavior.Editable = false;
            this.gvArg.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gvArg.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gvArg.OptionsSelection.EnableAppearanceHideSelection = false;
            this.gvArg.OptionsView.ShowGroupPanel = false;
            this.gvArg.RowClick += new DevExpress.XtraGrid.Views.Grid.RowClickEventHandler(this.gvFusion_RowClick);
            this.gvArg.CustomDrawEmptyForeground += new DevExpress.XtraGrid.Views.Base.CustomDrawEventHandler(this.gvArg_CustomDrawEmptyForeground);
            this.gvArg.DoubleClick += new System.EventHandler(this.gvFusion_DoubleClick);
            // 
            // TsMenuItemReQuery
            // 
            this.TsMenuItemReQuery.Name = "TsMenuItemReQuery";
            this.TsMenuItemReQuery.Size = new System.Drawing.Size(172, 22);
            this.TsMenuItemReQuery.Text = "重新关联多维数据";
            this.TsMenuItemReQuery.Click += new System.EventHandler(this.TsMenuItemReQuery_Click);
            // 
            // CellFusionDataForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(584, 430);
            this.Controls.Add(this.tabCtrl);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "CellFusionDataForm";
            this.Text = "小区关联数据";
            this.tabCtrl.ResumeLayout(false);
            this.contextMenuStrip1.ResumeLayout(false);
            this.pageAlarm.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlAlarm)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvAlarm)).EndInit();
            this.pagePerf.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlPerf)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvPerf)).EndInit();
            this.pageArg.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlArg)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvArg)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabCtrl;
        private System.Windows.Forms.TabPage pageAlarm;
        private DevExpress.XtraGrid.GridControl gridCtrlAlarm;
        private DevExpress.XtraGrid.Views.Grid.GridView gvAlarm;
        private System.Windows.Forms.TabPage pagePerf;
        private DevExpress.XtraGrid.GridControl gridCtrlPerf;
        private DevExpress.XtraGrid.Views.Grid.GridView gvPerf;
        private System.Windows.Forms.TabPage pageArg;
        private DevExpress.XtraGrid.GridControl gridCtrlArg;
        private DevExpress.XtraGrid.Views.Grid.GridView gvArg;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem TsMenuItemTopFront;
        private System.Windows.Forms.ToolStripMenuItem TsMenuItemKpiSet;
        private System.Windows.Forms.ToolStripMenuItem TsMenuItemExportAll;
        private System.Windows.Forms.ToolStripMenuItem TsMenuItemReQuery;

    }
}