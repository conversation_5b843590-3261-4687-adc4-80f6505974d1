﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTCsfbCellJudgeListForm : MinCloseForm
    {
        public ZTCsfbCellJudgeListForm()
            : base()
        {
            InitializeComponent();
            mapForm = MainModel.MainForm.GetMapForm();
            init();
            DisposeWhenClose = true;
        }

        private MapForm mapForm = null;
        private List<ZTCsfbCellJudge> resultList = new List<ZTCsfbCellJudge>();

        public void FillData(List<ZTCsfbCellJudge> resultList)
        {
            this.resultList = new List<ZTCsfbCellJudge>();
            this.resultList = resultList;

            ListViewCsfbCellJudge.RebuildColumns();
            ListViewCsfbCellJudge.ClearObjects();
            ListViewCsfbCellJudge.SetObjects(resultList);

            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        private void init()
        {
            CellManager cm = CellManager.GetInstance();
            #region 界面绑定数据

            this.olvColumnStatSN.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.SN;
                }
                return "";
            };

            this.olvColumnMoFileName.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.MoFileName;
                }
                return "";
            };

            this.olvColumnMtFileName.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    StringBuilder str = new StringBuilder();
                    foreach (string name in judge.MtFileName)
                    {
                        str.Append(name + ",");
                        str.Append("\r\n");
                    }
                    return str.ToString();
                }
                return "";
            };

            this.olvColumnCellName.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.CellName;
                }
                return "";
            };

            this.olvColumnCellID.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.CellID;
                }
                return "";
            };

            this.olvColumnLAC.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.LAC;
                }
                return "";
            };

            this.olvColumnTime.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.BeginTime;
                }
                return "";
            };

            this.olvColumnLongitude.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.Longitude;
                }
                return "";
            };

            this.olvColumnLatitude.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.Latitude;
                }
                return "";
            };

            this.olvColumnGSMLongitude.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.FallLongitude;
                }
                return "";
            };

            this.olvColumnGSMLatitude.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.FallLatitude;
                }
                return "";
            };

            this.olvColumnGSMDistance.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.FallDistance;
                }
                return "";
            };

            this.olvColumnGSMRxLev.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    if (judge.FallRxLev == 0)
                    {
                        return "";
                    }
                    return judge.FallRxLev;
                }
                return "";
            };

            this.olvColumnLTECellName.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.LteCellName;
                }
                return "";
            };

            this.olvColumnLTEECI.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.LteECI;
                }
                return "";
            };

            this.olvColumnENBID.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.ENBID;
                }
                return "";
            };

            this.olvColumnLTETAC.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.LteTAC;
                }
                return "";
            };

            this.olvColumnLTELongitude.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.LteLongitude;
                }
                return "";
            };

            this.olvColumnLTELatitude.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.LteLatitude;
                }
                return "";
            };

            this.olvColumnLTEDistance.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.LTEDistance;
                }
                return "";
            };

            this.olvColumnMaxGSMDistance.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.MaxFallDistance;
                }
                return "";
            };

            this.olvColumnMaxGSMRxLev.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    return judge.MaxFallRxLev;
                }
                return "";
            };

            this.olvColumnIsInList.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    if (judge.IsInList)
                    {
                        return "是";
                    }
                    else
                    {
                        return "否";
                    }
                }
                return "";
            };

            this.olvColumnCell1.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 0)
                    {
                        return nameList[0];
                    }
                }
                return "";
            };

            this.olvColumnCellType1.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 0)
                    {
                        Cell cell = cm.GetCellByName(nameList[0]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID1.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 0)
                    {
                        Cell cell = cm.GetCellByName(nameList[0]);
                        if (cell != null)
                        {
                            return cell.CI;
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI1.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 0)
                    {
                        Cell cell = cm.GetCellByName(nameList[0]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC1.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 0)
                    {
                        Cell cell = cm.GetCellByName(nameList[0]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell2.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 1)
                    {
                        return nameList[1];
                    }
                }
                return "";
            };

            this.olvColumnCellType2.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 1)
                    {
                        Cell cell = cm.GetCellByName(nameList[1]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID2.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 1)
                    {
                        Cell cell = cm.GetCellByName(nameList[1]);
                        if (cell != null)
                        {
                            return cell.CI;
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI2.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 1)
                    {
                        Cell cell = cm.GetCellByName(nameList[1]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC2.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 1)
                    {
                        Cell cell = cm.GetCellByName(nameList[1]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell3.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 2)
                    {
                        return nameList[2];
                    }
                }
                return "";
            };

            this.olvColumnCellType3.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 2)
                    {
                        Cell cell = cm.GetCellByName(nameList[2]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID3.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 2)
                    {
                        Cell cell = cm.GetCellByName(nameList[2]);
                        if (cell != null)
                        {
                            return cell.CI;
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI3.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 2)
                    {
                        Cell cell = cm.GetCellByName(nameList[2]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC3.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 2)
                    {
                        Cell cell = cm.GetCellByName(nameList[2]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell4.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 3)
                    {
                        return nameList[3];
                    }
                }
                return "";
            };

            this.olvColumnCellType4.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 3)
                    {
                        Cell cell = cm.GetCellByName(nameList[3]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID4.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 3)
                    {
                        Cell cell = cm.GetCellByName(nameList[3]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI4.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 3)
                    {
                        Cell cell = cm.GetCellByName(nameList[3]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC4.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 3)
                    {
                        Cell cell = cm.GetCellByName(nameList[3]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell5.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string>nameList=new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 4)
                    {
                        return nameList[4];
                    }
                }
                return "";
            };

            this.olvColumnCellType5.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 4)
                    {
                        Cell cell = cm.GetCellByName(nameList[4]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID5.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 4)
                    {
                        Cell cell = cm.GetCellByName(nameList[4]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI5.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 4)
                    {
                        Cell cell = cm.GetCellByName(nameList[4]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC5.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 4)
                    {
                        Cell cell = cm.GetCellByName(nameList[4]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell6.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 5)
                    {
                        return nameList[5];
                    }
                }
                return "";
            };

            this.olvColumnCellType6.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 5)
                    {
                        Cell cell = cm.GetCellByName(nameList[5]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID6.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 5)
                    {
                        Cell cell = cm.GetCellByName(nameList[5]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI6.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 5)
                    {
                        Cell cell = cm.GetCellByName(nameList[5]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC6.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 5)
                    {
                        Cell cell = cm.GetCellByName(nameList[5]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell7.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 6)
                    {
                        return nameList[6];
                    }
                }
                return "";
            };

            this.olvColumnCellType7.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 6)
                    {
                        Cell cell = cm.GetCellByName(nameList[6]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID7.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 6)
                    {
                        Cell cell = cm.GetCellByName(nameList[6]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI7.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 6)
                    {
                        Cell cell = cm.GetCellByName(nameList[6]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC7.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 6)
                    {
                        Cell cell = cm.GetCellByName(nameList[6]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell8.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 7)
                    {
                        return nameList[7];
                    }
                }
                return "";
            };

            this.olvColumnCellType8.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 7)
                    {
                        Cell cell = cm.GetCellByName(nameList[7]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID8.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 7)
                    {
                        Cell cell = cm.GetCellByName(nameList[7]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI8.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 7)
                    {
                        Cell cell = cm.GetCellByName(nameList[7]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC8.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 7)
                    {
                        Cell cell = cm.GetCellByName(nameList[7]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell9.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 8)
                    {
                        return nameList[8];
                    }
                }
                return "";
            };

            this.olvColumnCellType9.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 8)
                    {
                        Cell cell = cm.GetCellByName(nameList[8]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID9.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 8)
                    {
                        Cell cell = cm.GetCellByName(nameList[8]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI9.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 8)
                    {
                        Cell cell = cm.GetCellByName(nameList[8]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC9.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 8)
                    {
                        Cell cell = cm.GetCellByName(nameList[8]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell10.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 9)
                    {
                        return nameList[9];
                    }
                }
                return "";
            };

            this.olvColumnCellType10.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 9)
                    {
                        Cell cell = cm.GetCellByName(nameList[9]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID10.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 9)
                    {
                        Cell cell = cm.GetCellByName(nameList[9]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI10.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 9)
                    {
                        Cell cell = cm.GetCellByName(nameList[9]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC10.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 9)
                    {
                        Cell cell = cm.GetCellByName(nameList[9]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell11.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 10)
                    {
                        return nameList[10];
                    }
                }
                return "";
            };

            this.olvColumnCellType11.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 10)
                    {
                        Cell cell = cm.GetCellByName(nameList[10]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID11.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 10)
                    {
                        Cell cell = cm.GetCellByName(nameList[10]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI11.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 10)
                    {
                        Cell cell = cm.GetCellByName(nameList[10]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC11.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 10)
                    {
                        Cell cell = cm.GetCellByName(nameList[10]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell12.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 11)
                    {
                        return nameList[11];
                    }
                }
                return "";
            };

            this.olvColumnCellType12.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 11)
                    {
                        Cell cell = cm.GetCellByName(nameList[11]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID12.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 11)
                    {
                        Cell cell = cm.GetCellByName(nameList[11]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI12.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 11)
                    {
                        Cell cell = cm.GetCellByName(nameList[11]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC12.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 11)
                    {
                        Cell cell = cm.GetCellByName(nameList[11]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell13.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 12)
                    {
                        return nameList[12];
                    }
                }
                return "";
            };

            this.olvColumnCellType13.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 12)
                    {
                        Cell cell = cm.GetCellByName(nameList[12]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID13.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 12)
                    {
                        Cell cell = cm.GetCellByName(nameList[12]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI13.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 12)
                    {
                        Cell cell = cm.GetCellByName(nameList[12]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC13.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 12)
                    {
                        Cell cell = cm.GetCellByName(nameList[12]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell14.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 13)
                    {
                        return nameList[13];
                    }
                }
                return "";
            };

            this.olvColumnCellType14.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 13)
                    {
                        Cell cell = cm.GetCellByName(nameList[13]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID14.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 13)
                    {
                        Cell cell = cm.GetCellByName(nameList[13]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI14.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 13)
                    {
                        Cell cell = cm.GetCellByName(nameList[13]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC14.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 13)
                    {
                        Cell cell = cm.GetCellByName(nameList[13]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell15.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 14)
                    {
                        return nameList[14];
                    }
                }
                return "";
            };

            this.olvColumnCellType15.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 14)
                    {
                        Cell cell = cm.GetCellByName(nameList[14]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID15.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 14)
                    {
                        Cell cell = cm.GetCellByName(nameList[14]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI15.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 14)
                    {
                        Cell cell = cm.GetCellByName(nameList[14]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC15.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 14)
                    {
                        Cell cell = cm.GetCellByName(nameList[14]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell16.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 15)
                    {
                        return nameList[15];
                    }
                }
                return "";
            };

            this.olvColumnCellType16.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 15)
                    {
                        Cell cell = cm.GetCellByName(nameList[15]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID16.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 15)
                    {
                        Cell cell = cm.GetCellByName(nameList[15]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI16.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 15)
                    {
                        Cell cell = cm.GetCellByName(nameList[15]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC16.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 15)
                    {
                        Cell cell = cm.GetCellByName(nameList[15]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell17.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 16)
                    {
                        return nameList[16];
                    }
                }
                return "";
            };

            this.olvColumnCellType17.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 16)
                    {
                        Cell cell = cm.GetCellByName(nameList[16]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID17.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 16)
                    {
                        Cell cell = cm.GetCellByName(nameList[16]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI17.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 16)
                    {
                        Cell cell = cm.GetCellByName(nameList[16]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC17.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 16)
                    {
                        Cell cell = cm.GetCellByName(nameList[16]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell18.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 17)
                    {
                        return nameList[17];
                    }
                }
                return "";
            };

            this.olvColumnCellType18.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 17)
                    {
                        Cell cell = cm.GetCellByName(nameList[17]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID18.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 17)
                    {
                        Cell cell = cm.GetCellByName(nameList[17]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI18.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 17)
                    {
                        Cell cell = cm.GetCellByName(nameList[17]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC18.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 17)
                    {
                        Cell cell = cm.GetCellByName(nameList[17]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell19.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 18)
                    {
                        return nameList[18];
                    }
                }
                return "";
            };

            this.olvColumnCellType19.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 18)
                    {
                        Cell cell = cm.GetCellByName(nameList[18]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID19.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 18)
                    {
                        Cell cell = cm.GetCellByName(nameList[18]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI19.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 18)
                    {
                        Cell cell = cm.GetCellByName(nameList[18]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC19.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 18)
                    {
                        Cell cell = cm.GetCellByName(nameList[18]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell20.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 19)
                    {
                        return nameList[19];
                    }
                }
                return "";
            };

            this.olvColumnCellType20.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 19)
                    {
                        Cell cell = cm.GetCellByName(nameList[19]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID20.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 19)
                    {
                        Cell cell = cm.GetCellByName(nameList[19]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI20.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 19)
                    {
                        Cell cell = cm.GetCellByName(nameList[19]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC20.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 19)
                    {
                        Cell cell = cm.GetCellByName(nameList[19]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell21.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 20)
                    {
                        return nameList[20];
                    }
                }
                return "";
            };

            this.olvColumnCellType21.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 20)
                    {
                        Cell cell = cm.GetCellByName(nameList[20]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID21.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 20)
                    {
                        Cell cell = cm.GetCellByName(nameList[20]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI21.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 20)
                    {
                        Cell cell = cm.GetCellByName(nameList[20]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC21.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 20)
                    {
                        Cell cell = cm.GetCellByName(nameList[20]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell22.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 21)
                    {
                        return nameList[21];
                    }
                }
                return "";
            };

            this.olvColumnCellType22.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 21)
                    {
                        Cell cell = cm.GetCellByName(nameList[21]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID22.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 21)
                    {
                        Cell cell = cm.GetCellByName(nameList[21]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI22.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 21)
                    {
                        Cell cell = cm.GetCellByName(nameList[21]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC22.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 21)
                    {
                        Cell cell = cm.GetCellByName(nameList[21]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell23.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 22)
                    {
                        return nameList[22];
                    }
                }
                return "";
            };

            this.olvColumnCellType23.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 22)
                    {
                        Cell cell = cm.GetCellByName(nameList[22]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID23.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 22)
                    {
                        Cell cell = cm.GetCellByName(nameList[22]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI23.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 22)
                    {
                        Cell cell = cm.GetCellByName(nameList[22]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC23.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 22)
                    {
                        Cell cell = cm.GetCellByName(nameList[22]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell24.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 23)
                    {
                        return nameList[23];
                    }
                }
                return "";
            };

            this.olvColumnCellType24.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 23)
                    {
                        Cell cell = cm.GetCellByName(nameList[23]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID24.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 23)
                    {
                        Cell cell = cm.GetCellByName(nameList[23]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI24.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 23)
                    {
                        Cell cell = cm.GetCellByName(nameList[23]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC24.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 23)
                    {
                        Cell cell = cm.GetCellByName(nameList[23]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell25.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 24)
                    {
                        return nameList[24];
                    }
                }
                return "";
            };

            this.olvColumnCellType25.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 24)
                    {
                        Cell cell = cm.GetCellByName(nameList[24]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID25.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 24)
                    {
                        Cell cell = cm.GetCellByName(nameList[24]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI25.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 24)
                    {
                        Cell cell = cm.GetCellByName(nameList[24]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC25.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 24)
                    {
                        Cell cell = cm.GetCellByName(nameList[24]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell26.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 25)
                    {
                        return nameList[25];
                    }
                }
                return "";
            };

            this.olvColumnCellType26.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 25)
                    {
                        Cell cell = cm.GetCellByName(nameList[25]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID26.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 25)
                    {
                        Cell cell = cm.GetCellByName(nameList[25]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI26.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 25)
                    {
                        Cell cell = cm.GetCellByName(nameList[25]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC26.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 25)
                    {
                        Cell cell = cm.GetCellByName(nameList[25]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell27.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 26)
                    {
                        return nameList[26];
                    }
                }
                return "";
            };

            this.olvColumnCellType27.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 26)
                    {
                        Cell cell = cm.GetCellByName(nameList[26]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID27.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 26)
                    {
                        Cell cell = cm.GetCellByName(nameList[26]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI27.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 26)
                    {
                        Cell cell = cm.GetCellByName(nameList[26]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC27.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 26)
                    {
                        Cell cell = cm.GetCellByName(nameList[26]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell28.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 27)
                    {
                        return nameList[27];
                    }
                }
                return "";
            };

            this.olvColumnCellType28.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 27)
                    {
                        Cell cell = cm.GetCellByName(nameList[27]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID28.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 27)
                    {
                        Cell cell = cm.GetCellByName(nameList[27]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI28.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 27)
                    {
                        Cell cell = cm.GetCellByName(nameList[27]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC28.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 27)
                    {
                        Cell cell = cm.GetCellByName(nameList[27]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell29.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 28)
                    {
                        return nameList[28];
                    }
                }
                return "";
            };

            this.olvColumnCellType29.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 28)
                    {
                        Cell cell = cm.GetCellByName(nameList[28]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID29.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 28)
                    {
                        Cell cell = cm.GetCellByName(nameList[28]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI29.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 28)
                    {
                        Cell cell = cm.GetCellByName(nameList[28]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC29.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 28)
                    {
                        Cell cell = cm.GetCellByName(nameList[28]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell30.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 29)
                    {
                        return nameList[29];
                    }
                }
                return "";
            };

            this.olvColumnCellType30.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 29)
                    {
                        Cell cell = cm.GetCellByName(nameList[29]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID30.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 29)
                    {
                        Cell cell = cm.GetCellByName(nameList[29]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI30.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 29)
                    {
                        Cell cell = cm.GetCellByName(nameList[29]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC30.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 29)
                    {
                        Cell cell = cm.GetCellByName(nameList[29]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell31.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 30)
                    {
                        return nameList[31];
                    }
                }
                return "";
            };

            this.olvColumnCellType31.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 30)
                    {
                        Cell cell = cm.GetCellByName(nameList[30]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID31.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 30)
                    {
                        Cell cell = cm.GetCellByName(nameList[30]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI31.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 30)
                    {
                        Cell cell = cm.GetCellByName(nameList[30]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC31.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 30)
                    {
                        Cell cell = cm.GetCellByName(nameList[30]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnCell32.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 31)
                    {
                        return nameList[31];
                    }
                }
                return "";
            };

            this.olvColumnCellType32.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 31)
                    {
                        Cell cell = cm.GetCellByName(nameList[31]);
                        if (cell != null)
                        {
                            if (cell.BCCH >= 1 && cell.BCCH <= 124)
                            {
                                return "GSM小区";
                            }
                            else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                            {
                                return "DCS小区";
                            }
                        }
                    }
                }
                return "";
            };

            this.olvColumnCellID32.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 31)
                    {
                        Cell cell = cm.GetCellByName(nameList[31]);
                        if (cell != null)
                        {
                            return cell.CI.ToString();
                        }
                    }
                }
                return "";
            };

            this.olvColumnPCI32.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 32)
                    {
                        Cell cell = cm.GetCellByName(nameList[32]);
                        if (cell != null)
                        {
                            return cell.BCCH;
                        }
                    }
                }
                return "";
            };

            this.olvColumnBSIC32.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    List<string> nameList = new List<string>(judge.CellRxLevList.Keys);
                    if (nameList.Count > 31)
                    {
                        Cell cell = cm.GetCellByName(nameList[31]);
                        if (cell != null)
                        {
                            return cell.BSIC;
                        }
                    }
                }
                return "";
            };

            this.olvColumnRxLev1.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 0)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev2.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 1)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev3.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 2)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev4.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 3)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev5.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 4)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev6.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 5)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev7.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 6)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev8.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 7)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev9.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 8)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev10.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 9)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev11.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 10)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev12.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 11)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev13.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 12)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev14.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 13)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev15.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 14)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev16.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 15)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev17.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 16)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev18.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 17)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev19.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 18)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev20.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 19)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev21.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 20)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev22.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 21)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev23.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 22)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev24.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 23)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev25.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 24)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev26.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 25)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev27.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 26)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev28.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 27)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev29.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 28)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev30.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 29)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev31.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 30)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            this.olvColumnRxLev32.AspectGetter = delegate(object row)
            {
                if (row is ZTCsfbCellJudge)
                {
                    int i = 0;
                    ZTCsfbCellJudge judge = row as ZTCsfbCellJudge;
                    foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                    {
                        if (i == 31)
                        {
                            return pair.Value;
                        }
                        i++;
                    }
                }
                return "";
            };

            #endregion
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(ListViewCsfbCellJudge);
        }

        private void ListViewCsfbCellJudge_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            OlvListViewHitTestInfo info = this.ListViewCsfbCellJudge.OlvHitTest(e.X, e.Y);
            if (info.RowObject is ZTCsfbCellJudge)
            {
                MainModel.ClearDTData();
                ZTCsfbCellJudge judge = info.RowObject as ZTCsfbCellJudge;
                addDTData(judge);
                foreach (ZTCsfbCellJudge cj in judge.AllList)
                {
                    addDTData(cj);
                }
                this.MainModel.IsFileReplayByCompareMode = false;
                this.MainModel.FireDTDataChanged(this);
                this.MainModel.FireSetDefaultMapSerialTheme("GSM", "RxLevSub");
            }
        }

        private void addDTData(ZTCsfbCellJudge cj)
        {
            foreach (TestPoint tp in cj.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            foreach (Event evt in cj.Events)
            {
                MainModel.DTDataManager.Add(evt);
            }
            foreach (Model.Message msg in cj.Messages)
            {
                MainModel.DTDataManager.Add(msg);
            }
        }
    }
}
