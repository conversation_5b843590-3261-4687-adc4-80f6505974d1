﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class XtraSetCellMultiForm : BaseDialog
    {
        public XtraSetCellMultiForm(string type)
        {
            InitializeComponent();
            cbxCoFreqType.Visible = true;
            chkCoFreq.Visible = true;
            cbxCoFreqType.Properties.Items.Clear();
            if (type.Equals("GSM"))
            {
                cbxCoFreqType.Properties.Items.Add("BCCH & TCH");
                cbxCoFreqType.Properties.Items.Add("BCCH Only");
                cbxCoFreqType.Properties.Items.Add("TCH Only");
                numRxLevDValue.Value = 12;
                numRxLevThreshold.Value = -80;
            }
            else if (type.Equals("TD"))
            {
                cbxCoFreqType.Properties.Items.Add("ARFCN & ARFCNList");
                cbxCoFreqType.Properties.Items.Add("ARFCN Only");
                cbxCoFreqType.Properties.Items.Add("ARFCNList Only");
                numRxLevDValue.Value = 6;
                numRxLevThreshold.Value = -80;
                chkSaveSample.Enabled = true;
            }
            else if (type.Equals("LTE"))
            {
                cbxCoFreqType.Visible = false;
                chkCoFreq.Visible = false;
                chkSaveSample.Enabled = true;
                cbxFreqBand.Visible = true;
                cbxFreqBand.Location = new Point(69, 137);
                chkFreqBand.Visible = true;
                chkFreqBand.Location = new Point(14, 137);
            }
            else if (type.Equals("LTEScan"))
            {
                cbxCoFreqType.Visible = false;
                chkCoFreq.Visible = false;
                chkSaveSample.Visible = false;
                numRxLevDValue.Value = 6;
                numRxLevThreshold.Value = -80;
                spinEditInvalidThresold.Value = -110;
            }
            else if (type.Equals("WCDMA"))
            {
                cbxCoFreqType.Properties.Items.Add("UARFCN 或 UARFCNListOnly");
                cbxCoFreqType.Properties.Items.Add("UARFCN Only");
                cbxCoFreqType.Properties.Items.Add("UARFCNList Only");
                numRxLevDValue.Value = 6;
                numRxLevThreshold.Value = -80;
                chkSaveSample.Enabled = true;
            }

            if (cbxCoFreqType.Properties.Items.Count > 1)
            {
                cbxCoFreqType.SelectedIndex = 1;
            }
            chkCoFreq_CheckedChanged(null, null);
        }

        public CellMultiCoverageCondition GetSettingFilterRet()
        {
            CellMultiCoverageCondition condition = new CellMultiCoverageCondition();
            condition.SetRxlevDiff = (int)numRxLevDValue.Value;
            condition.SetRxlev = (int)numRxLevThreshold.Value;
            condition.CoFreq = chkCoFreq.Checked;
            condition.InterferenceType = (MapForm.DisplayInterferenceType)cbxCoFreqType.SelectedIndex;
            condition.InvalidPointRxLev = (int)spinEditInvalidThresold.Value;
            condition.IsSaveSample = chkSaveSample.Checked;
            condition.CheckFreqBandOnly = chkFreqBand.Checked;
            Model.LTEBandType band = (Model.LTEBandType)Enum.Parse(typeof(Model.LTEBandType), cbxFreqBand.SelectedItem.ToString());
            condition.BandType = band;
            return condition;
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButton2_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void chkCoFreq_CheckedChanged(object sender, EventArgs e)
        {
            cbxCoFreqType.Enabled = chkCoFreq.Checked;
        }

        private void chkFreqBand_CheckedChanged(object sender, EventArgs e)
        {
            cbxFreqBand.Enabled = chkFreqBand.Checked;
        }
    }

    public class CellMultiCoverageCondition
    {
        public int SetRxlev { get; set; }
        public int SetRxlevDiff { get; set; }
        public bool CoFreq { get; set; }
        public MapForm.DisplayInterferenceType InterferenceType { get; set; }
        public string StrStatTopN { get; set; }
        public int InvalidPointRxLev { get; set; }
        public bool IsSaveSample { get; set; }
        
        public bool CheckFreqBandOnly { get; set; } = false;
        public Model.LTEBandType BandType { get; set; }


        /// <summary>
        ///运营商 0：移动 1：电信 2：联通
        /// </summary>
        public int CarrierId { get; set; }

        /// <summary>
        /// 频点集合
        /// </summary>
        public List<FreqPoint> ListFreqPoint { get; set; }
    }
}