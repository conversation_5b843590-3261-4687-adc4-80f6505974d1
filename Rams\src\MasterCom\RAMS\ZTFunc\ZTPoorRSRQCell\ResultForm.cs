﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTPoorRSRQCell
{
    public partial class ResultForm : MinCloseForm
    {
        public ResultForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = false;
        }

        bool isNRForm = false;

        internal void FillData(List<PoorRsrqCell> poorCellList)
        {
            foreach (TestPoint tp in poorCellList[0].PoorPoints)
            {
                if (tp is TestPoint_NR)
                {
                    gridColumn21.Caption = "NCI";
                    gridColumn21.FieldName = "Nci";
                    isNRForm = true;
                    break;
                }
            }
            gridControl.Refresh();

            gridControl.DataSource = poorCellList;
            gridControl.RefreshDataSource();
            gv.BestFitColumns();
            MainModel.ClearDTData();
            MainModel.FireDTDataChanged(this);
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gv);
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            PoorRsrqCell poorCell = gv.GetFocusedRow() as PoorRsrqCell;
            if (poorCell != null)
            {
                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in poorCell.GoodPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                foreach (TestPoint tp in poorCell.PoorPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                if (isNRForm)
                {
                    MainModel.SetSelectedNRCell(((PoorRsrqCell_NR)poorCell).Cell);
                }
                else
                {
                    MainModel.SelectedLTECell = poorCell.Cell;
                }
                MainModel.FireDTDataChanged(this);
            }
        }


    }
}
