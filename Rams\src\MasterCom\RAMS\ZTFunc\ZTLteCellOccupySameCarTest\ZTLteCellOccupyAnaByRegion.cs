﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteCellOccupyAnaByRegion : ZTLteCellOccupyAnaByFile
    {
        public ZTLteCellOccupyAnaByRegion(MainModel mModel)
            : base(mModel)
        {
        }
        public override string Name
        {
            get { return "同车小区占用(按区域)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return Model.MainModel.NeedSearchType.Region;
        }

        protected override void queryFileToAnalyse()
        {
            DIYQueryFileInfoByRegion queryFileByReg = new DIYQueryFileInfoByRegion(MainModel);
            queryFileByReg.IsShowFileInfoForm = false;
            queryFileByReg.SetQueryCondition(condition);
            queryFileByReg.Query();

            condition.FileInfos = MainModel.FileInfos;
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }
    }

    public class ZTLteCellOccupyAnaByRegion_FDD : ZTLteCellOccupyAnaByFile_FDD
    {
        public ZTLteCellOccupyAnaByRegion_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get { return "同车小区占用LTE_FDD(按区域)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return Model.MainModel.NeedSearchType.Region;
        }

        protected override void queryFileToAnalyse()
        {
            DIYQueryFileInfoByRegion queryFileByReg = new DIYQueryFileInfoByRegion(MainModel);
            queryFileByReg.IsShowFileInfoForm = false;
            queryFileByReg.SetQueryCondition(condition);
            queryFileByReg.Query();

            condition.FileInfos = MainModel.FileInfos;
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }
    }
}
