﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTCsfbCallStat
{
    public partial class CallConditionDlg : BaseDialog
    {
        public CallConditionDlg()
            : base()
        {
            InitializeComponent();
        }

        private void chkDelay_CheckedChanged(object sender, EventArgs e)
        {
            numMaxDelaySec.Enabled = chkDelay.Checked;
        }

        public void GetCondition(out bool checkDelay,out int sec)
        {
            checkDelay = chkDelay.Checked;
            sec = (int)numMaxDelaySec.Value;
        }

        public void SetCondition(bool checkDelay,int sec)
        {
            chkDelay.Checked = checkDelay;
            numMaxDelaySec.Value = (decimal)sec;
        }

    }
}
