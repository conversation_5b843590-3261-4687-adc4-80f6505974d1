﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using System.Collections;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class HighSpeedRailPrivateNetDlg : BaseDialog
    {
        public HighSpeedRailPrivateNetDlg(HighSpeedRailPrivateNetCondition condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        private void setCondition(HighSpeedRailPrivateNetCondition condition)
        {
            if (condition == null)
            {
                return;
            }
            numRSRP.Value = condition.WeakCoverRSRP;
            numCoverDuration.Value = condition.WeakCoverDuration;
            numCoverReasonDistance.Value = condition.CoverReasonDistance;

            numSINR.Value = condition.WeakSINR;
            numSINRDuration.Value = condition.WeakSINRDuration;
            numSINRReasonRSRP.Value = condition.SINRReasonRSRP;
            numSINRReasonDistance.Value = condition.SINRReasonDistance;

            numPrivateNetRSRP.Value = condition.PrivateNetWeakCoverRSRP;
            numPrivateNetCoverDuration.Value = condition.PrivateNetWeakCoverDuration;
            numPrivateNetSINR.Value = condition.PrivateNetWeakSINR;
            numPrivateNetSINRDuration.Value = condition.PrivateNetWeakSINRDuration;
            privateNetCell = condition.PrivateNetCell;

            labelFileName.Text = condition.FileName;
            labelPrivateCellCount.Text = privateNetCell.Count.ToString();
        }

        public HighSpeedRailPrivateNetCondition GetConditon()
        {
            HighSpeedRailPrivateNetCondition condition = new HighSpeedRailPrivateNetCondition();
            condition.WeakCoverRSRP = (int)numRSRP.Value;
            condition.WeakCoverDuration = (int)numCoverDuration.Value;
            condition.CoverReasonDistance = (int)numCoverReasonDistance.Value;

            condition.WeakSINR = (int)numSINR.Value;
            condition.WeakSINRDuration = (int)numSINRDuration.Value;
            condition.SINRReasonRSRP = (int)numSINRReasonRSRP.Value;
            condition.SINRReasonDistance = (int)numSINRReasonDistance.Value;

            condition.PrivateNetWeakCoverRSRP = (int)numPrivateNetRSRP.Value;
            condition.PrivateNetWeakCoverDuration = (int)numPrivateNetCoverDuration.Value;
            condition.PrivateNetWeakSINR = (int)numPrivateNetSINR.Value;
            condition.PrivateNetWeakSINRDuration = (int)numPrivateNetSINRDuration.Value;
            condition.PrivateNetCell = privateNetCell;

            condition.FileName = labelFileName.Text;

            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (privateNetCell.Count <= 0)
            {
                MessageBox.Show("专网小区不能为空,请先导入专网小区工参");
            }
            else
            {
                this.DialogResult = DialogResult.OK;
            }
        }

        List<LTECell> privateNetCell = new List<LTECell>();
        private void simpleButton1_Click(object sender, EventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.InitialDirectory = "";
            openFileDialog.Filter = "Excel files |*.xls;*.xlsx|all files (*.*)|*.*";
            openFileDialog.RestoreDirectory = true;
            openFileDialog.FilterIndex = 1;
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                WaitBox.Show("正在导入工参", importPrivateNetCell, new object[] { openFileDialog.SafeFileName, openFileDialog.FileName });
            }
        }

        private void importPrivateNetCell(object args)
        {
            object[] theArg = args as object[];
            privateNetCell.Clear();

            string xlsFilePath = theArg[1].ToString();
            try
            {
                DataSet ds = ExcelNPOIManager.ImportFromExcel(xlsFilePath);
                DataTable datatable = ds.Tables[0];

                if (datatable.Rows.Count > 0)
                {
                    if (datatable.Columns.Count < 6)
                    {
                        MessageBox.Show("缺少字段");
                    }
                    else if (datatable.Columns.Count > 6)
                    {
                        MessageBox.Show("过多字段");
                    }
                }
                addPrivateNetCell(datatable);

                string fileName = theArg[0].ToString();
                if (fileName.Length > 50)
                {
                    labelFileName.Text = fileName.Substring(0, 50) + "...";
                }
                else
                {
                    labelFileName.Text = fileName;
                }

                labelPrivateCellCount.Text = privateNetCell.Count.ToString();
            }
            catch (Exception ex)
            {
                labelFileName.Text = "";
                privateNetCell.Clear();
                MessageBox.Show("导入存在异常," + ex.Message);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void addPrivateNetCell(DataTable datatable)
        {
            if (datatable.Rows.Count > 0)
            {
                for (int i = 0; i < datatable.Rows.Count; i++)
                {
                    DataRow dr = datatable.Rows[i];
                    LTECell lteCell = getLTECell(dr);

                    privateNetCell.Add(lteCell);
                }
            }
        }

        private static LTECell getLTECell(DataRow dr)
        {
            LTECell lteCell = new LTECell();
            //第一列是小区名
            lteCell.Name = dr[0].ToString();
            //第二列是ECI
            if (dr[1] != DBNull.Value && dr[1].ToString() != "")
            {
                lteCell.ECI = Convert.ToInt32(dr[1]);
            }
            //第三列是TAC
            if (dr[2] != DBNull.Value && dr[2].ToString() != "")
            {
                lteCell.TAC = Convert.ToInt32(dr[2]);
            }
            //第四列是PCI
            if (dr[3] != DBNull.Value && dr[3].ToString() != "")
            {
                lteCell.PCI = Convert.ToInt32(dr[3]);
            }
            LTEBTS bts = new LTEBTS();
            //第五列是经度
            if (dr[4] != DBNull.Value && dr[4].ToString() != "")
            {
                bts.Longitude = Convert.ToDouble(dr[4]);
            }
            //第六列是纬度
            if (dr[5] != DBNull.Value && dr[5].ToString() != "")
            {
                bts.Latitude = Convert.ToDouble(dr[5]);
            }
            lteCell.BelongBTS = bts;
            return lteCell;
        }
    }

    public class HighSpeedRailPrivateNetCondition
    {
        /// <summary>
        /// 专网工参小区
        /// </summary>
        public List<LTECell> PrivateNetCell { get; set; } = new List<LTECell>();

        public string FileName { get; set; } = "";

        #region 弱覆盖条件设置
        /// <summary>
        /// 弱覆盖RSRP
        /// </summary>
        public int WeakCoverRSRP { get; set; } = -105;
        
        /// <summary>
        /// 弱覆盖持续时间
        /// </summary>
        public int WeakCoverDuration { get; set; } = 10;
        
        /// <summary>
        /// 弱覆盖原因-缺站距离
        /// </summary>
        public int CoverReasonDistance { get; set; } = 800;
        #endregion

        #region 弱质差条件设置
        /// <summary>
        /// 质差SINR
        /// </summary>
        public int WeakSINR { get; set; } = 0;
        
        /// <summary>
        /// 质差持续时间
        /// </summary>
        public int WeakSINRDuration { get; set; } = 10;
        
        /// <summary>
        /// 质差原因-弱覆盖RSRP
        /// </summary>
        public int SINRReasonRSRP { get; set; } = -105;
        
        /// <summary>
        /// 质差原因-过覆盖距离
        /// </summary>
        public int SINRReasonDistance { get; set; } = 800;
        #endregion

        #region 出专网条件设置
        public int PrivateNetWeakCoverRSRP { get; set; } = -105;
        
        public int PrivateNetWeakCoverDuration { get; set; } = 10;
        
        public int PrivateNetWeakSINR { get; set; } = 0;
        
        public int PrivateNetWeakSINRDuration { get; set; } = 10;
        #endregion
    }
}
