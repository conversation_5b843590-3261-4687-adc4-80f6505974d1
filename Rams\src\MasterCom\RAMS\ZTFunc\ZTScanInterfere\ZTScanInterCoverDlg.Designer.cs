﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTScanInterCoverDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.rbtnShowBoth = new System.Windows.Forms.RadioButton();
            this.rbtnShowOrg = new System.Windows.Forms.RadioButton();
            this.rbtnShowJam = new System.Windows.Forms.RadioButton();
            this.rbtnShowSame = new System.Windows.Forms.RadioButton();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.rbtnShowSame);
            this.groupBox1.Controls.Add(this.rbtnShowJam);
            this.groupBox1.Controls.Add(this.rbtnShowOrg);
            this.groupBox1.Controls.Add(this.rbtnShowBoth);
            this.groupBox1.Location = new System.Drawing.Point(13, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(236, 205);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "类型";
            // 
            // rbtnShowBoth
            // 
            this.rbtnShowBoth.AutoSize = true;
            this.rbtnShowBoth.Checked = true;
            this.rbtnShowBoth.Location = new System.Drawing.Point(34, 33);
            this.rbtnShowBoth.Name = "rbtnShowBoth";
            this.rbtnShowBoth.Size = new System.Drawing.Size(167, 16);
            this.rbtnShowBoth.TabIndex = 0;
            this.rbtnShowBoth.TabStop = true;
            this.rbtnShowBoth.Text = "显示原小区和干扰小区覆盖";
            this.rbtnShowBoth.UseVisualStyleBackColor = true;
            this.rbtnShowBoth.CheckedChanged += new System.EventHandler(this.rbtnShowBoth_CheckedChanged);
            // 
            // rbtnShowOrg
            // 
            this.rbtnShowOrg.AutoSize = true;
            this.rbtnShowOrg.Location = new System.Drawing.Point(34, 76);
            this.rbtnShowOrg.Name = "rbtnShowOrg";
            this.rbtnShowOrg.Size = new System.Drawing.Size(107, 16);
            this.rbtnShowOrg.TabIndex = 1;
            this.rbtnShowOrg.Text = "显示原小区覆盖";
            this.rbtnShowOrg.UseVisualStyleBackColor = true;
            this.rbtnShowOrg.CheckedChanged += new System.EventHandler(this.rbtnShowOrg_CheckedChanged);
            // 
            // rbtnShowJam
            // 
            this.rbtnShowJam.AutoSize = true;
            this.rbtnShowJam.Location = new System.Drawing.Point(34, 119);
            this.rbtnShowJam.Name = "rbtnShowJam";
            this.rbtnShowJam.Size = new System.Drawing.Size(119, 16);
            this.rbtnShowJam.TabIndex = 2;
            this.rbtnShowJam.Text = "显示干扰小区覆盖";
            this.rbtnShowJam.UseVisualStyleBackColor = true;
            this.rbtnShowJam.CheckedChanged += new System.EventHandler(this.rbtnShowJam_CheckedChanged);
            // 
            // rbtnShowSame
            // 
            this.rbtnShowSame.AutoSize = true;
            this.rbtnShowSame.Location = new System.Drawing.Point(34, 162);
            this.rbtnShowSame.Name = "rbtnShowSame";
            this.rbtnShowSame.Size = new System.Drawing.Size(119, 16);
            this.rbtnShowSame.TabIndex = 3;
            this.rbtnShowSame.Text = "显示公共部分覆盖";
            this.rbtnShowSame.UseVisualStyleBackColor = true;
            this.rbtnShowSame.CheckedChanged += new System.EventHandler(this.rbtnShowSame_CheckedChanged);
            // 
            // ZTScanInterCoverDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(261, 229);
            this.Controls.Add(this.groupBox1);
            this.Name = "ZTScanInterCoverDlg";
            this.Text = "覆盖类型选择";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.RadioButton rbtnShowSame;
        private System.Windows.Forms.RadioButton rbtnShowJam;
        private System.Windows.Forms.RadioButton rbtnShowOrg;
        private System.Windows.Forms.RadioButton rbtnShowBoth;
    }
}