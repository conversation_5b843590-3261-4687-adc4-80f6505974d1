﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.IO;
using System.Windows.Forms;
using System.Net;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.Net
{
    public abstract class DIYEventQuery : BackgroundQueryBase
    {
        /// <summary>
        /// 是否显示查询事件选择窗口
        /// </summary>
        public bool showEventChooser { get; set; }
        /// <summary>
        /// 是否显示事件结果窗口
        /// </summary>
        public bool showForm { get; set; }
        /// <summary>
        /// 当isAddEventToDTDataManager==false，记录事件列表
        /// </summary>
        public List<Event> Events { get; set; }
        /// <summary>
        /// 是否按文件ID建立事件集合
        /// </summary>
        protected bool saveAsFileEventsDic = false;
        public Dictionary<int, List<Event>> fileEventsDic { get; set; }
        /// <summary>
        /// 是否查询所有事件
        /// </summary>
        public bool IsQueryAllEvents { get; set; }
        /// <summary>
        /// 是否按900,1800过滤： 1 所有； 2 900； 3 1800
        /// </summary>
        protected int frequencyRangeFilter = 1;
        /// <summary>
        /// 是否把事件信息放到DTDataManager，默认为放到DTDataManager
        /// </summary>
        protected bool isAddEventToDTDataManager = true;
        protected DIYEventQuery(MainModel mainModel)
            : base(mainModel)
        {
            showEventChooser = true;
            showForm = false;
            Events = new List<Event>();
            fileEventsDic = new Dictionary<int, List<Event>>();
            IsQueryAllEvents = true;
        }

        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.None; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.其他; }
        }

        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.None; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected virtual void doWithDTData(Event evt)
        {
            if (saveAsFileEventsDic)
            {
                if (fileEventsDic.ContainsKey(evt.FileID))
                {
                    if (!fileEventsDic[evt.FileID].Contains(evt))
                    {
                        fileEventsDic[evt.FileID].Add(evt);
                    }
                }
                else
                {
                    List<Event> evtList = new List<Event>();
                    evtList.Add(evt);
                    fileEventsDic[evt.FileID] = evtList;
                }
            }
            else
            {
                Events.Add(evt);
            }
        }
        public void SetIsAddEventToDTDataManager(bool isAdd)
        {
            this.isAddEventToDTDataManager = isAdd;
        }
        public void SetSaveAsFileEventsDic(bool isAdd)
        {
            this.saveAsFileEventsDic = isAdd;
        }
        public void ClearEventList()//清除事件列表
        {
            Events.Clear();
            fileEventsDic.Clear();
        }

        protected void fillContentNeeded_Event(Package package)
        {
            StringBuilder sbuilder = new StringBuilder();
            sbuilder.Append("0,1,43,");
            sbuilder.Append("0,2,43,");
            sbuilder.Append("0,3,43,");
            sbuilder.Append("0,4,43,");
            sbuilder.Append("0,5,43,");
            sbuilder.Append("0,6,43,");
            sbuilder.Append("0,7,43,");
            sbuilder.Append("0,8,43,");
            sbuilder.Append("0,9,43,");
            sbuilder.Append("0,10,43,");
            sbuilder.Append("0,11,43,");
            sbuilder.Append("0,12,43,");
            sbuilder.Append("0,13,43,");
            sbuilder.Append("0,14,43,");
            sbuilder.Append("0,15,43,");
            sbuilder.Append("0,16,43,");
            sbuilder.Append("0,17,43,");
            sbuilder.Append("0,18,43,");
            sbuilder.Append("0,19,43,");
            sbuilder.Append("0,20,43,");
            sbuilder.Append("0,21,43,");
            sbuilder.Append("0,22,43,");
            sbuilder.Append("0,23,43,");
            sbuilder.Append("0,24,43,");
            sbuilder.Append("0,25,43,");
            sbuilder.Append("0,26,43");
            package.Content.AddParam(sbuilder.ToString());
        }

        /**
        private List<ColumnDefItem> getNeededColumnDefList(DIYSampleGroup group)
        {
            InterfaceManager manager = InterfaceManager.GetInstance();
            List<ColumnDefItem> retList = new List<ColumnDefItem>();
            Dictionary<string,ColumnDefItem> triIdTofixColumnsDic = new Dictionary<string,ColumnDefItem>();
            Dictionary<string,ColumnDefItem> tbIdAndImgIdToNonFixColumnsDic = new Dictionary<string,ColumnDefItem>();
            foreach(DIYSampleParamDef paraDef in group.ColumnsDefSet)
            {
                if(paraDef.parameter!=null)
                {
                    List<ColumnDefItem> columnDefList = manager.GetColumnDefByShowName(paraDef.parameter.Info.Name);
                    if (columnDefList != null)
                    {
                        foreach(ColumnDefItem columnDef in columnDefList)
                        {
                            if (columnDef.fix)
                            {
                                triIdTofixColumnsDic[columnDef.GetTriIdStr()] = columnDef;
                            }
                            else
                            {
                                string colDicKey = columnDef.tableName + "," + columnDef.imgID;
                                tbIdAndImgIdToNonFixColumnsDic[colDicKey] = columnDef;
                            }
                        }
                    }
                }
            }
            foreach(ColumnDefItem col in triIdTofixColumnsDic.Values)
            {
                retList.Add(col);
            }
            foreach(ColumnDefItem col in tbIdAndImgIdToNonFixColumnsDic.Values)
            {
                retList.Add(col);
            }
            return retList;
        }
        */
        protected virtual bool prepareAskWhatEvent()
        {
            if (showEventChooser)
            {
                EventChooserForm eventChooser = EventChooserForm.GetInstance(MainModel);
                if (eventChooser.ShowDialog() == DialogResult.OK)
                {
                    showForm = eventChooser.ShowForm;
                    Condition.FilterOffValue9 = eventChooser.FilterOffValue9;
                    if (eventChooser.SelectedEventIDs.Count > 0)
                    {
                        Condition.EventIDs = eventChooser.SelectedEventIDs;
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                return false;
            }
            if (IsQueryAllEvents)
            {
                Condition.EventIDs = new List<int>();
                Condition.EventIDs.Add(-1);
            }
            return true;
        }
        protected override void query()
        {
            if (!getConditionBeforeQuery())
            {
                return;
            }
            if (!prepareAskWhatEvent())
            {
                return;
            }
            if (!MainModel.IsBackground && !MainModel.QueryFromBackground)
            {
                WaitBox.CanCancel = true;
                WaitBox.Text = "正在查询...";
            }
            ClientProxy clientProxy = null;

            MainModel.ClearDTData();
            MainModel.IsDrawEventResult = false;
            MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

            foreach (int DistrictID in condition.DistrictIDs)
            {
                clientProxy = new ClientProxy();
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                if (!MainModel.IsBackground && !MainModel.QueryFromBackground)
                {
                    WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                }
                else
                {
                    queryInThread(clientProxy);
                }
                clientProxy.Close();
            }
            MainModel.FireDTDataChanged(this);
            MainModel.RefreshLegend();
            doAfterQuery();
        }

        private void doAfterQuery()
        {
            if (showForm)
            {
                fireShowForm();
            }
            else
            {
                if (MainModel.QueryFromBackground)
                {
                    initBackgroundImageDesc();
                }
                else
                {
                    fireShowFormAfterQuery();
                }
            }
        }

        protected void fireShowForm()
        {
            EventInfoForm frm = MainModel.GetInstance().CreateResultForm(typeof(EventInfoForm)) as EventInfoForm;
            frm.Owner = MainModel.MainForm;
            frm.FillData();
            frm.Visible = true;
            frm.BringToFront();
        }

        protected virtual bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground)
            {
                return false;
            }
            return true;
        }

        protected virtual void fireShowFormAfterQuery()
        {

        }

        protected virtual void doSomethingBeforeQueryInThread()
        {

        }

        protected virtual void getResultAfterQuery()
        {

        }

        protected virtual void queryInThread(object o)
        {
            try
            {
                doSomethingBeforeQueryInThread();
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, null, true);
                }
                else
                {
                    queryByPeriods(clientProxy, package, periodCount, index);
                }
                getResultAfterQuery();
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
            finally
            {
                if (!MainModel.IsBackground && !MainModel.QueryFromBackground)
                {
                    WaitBox.Close();
                }
            }
        }

        private void queryByPeriods(ClientProxy clientProxy, Package package, int periodCount, int index)
        {
            foreach (TimePeriod period in Condition.Periods)
            {
                if (!MainModel.IsBackground && !MainModel.QueryFromBackground)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                    WaitBox.ProgressPercent = index / periodCount + 10;
                }
                else if (MainModel.BackgroundStopRequest)
                {
                    break;
                }
                queryPeriodInfo(clientProxy, package, period, false);
            }
        }

        protected virtual void queryPeriodInfo(ClientProxy clientProxy, Package package, TimePeriod period, bool byround)
        {
            MainModel.IsFusionInclude = false;
            prepareStatPackage_Event_FileFilter(package, period, byround);
            prepareStatPackage_Event_EventFilter(package, period);
            fillContentNeeded_Event(package);
            clientProxy.Send();
            recieveInfo_Event(clientProxy);
        }

        protected virtual void recieveInfo_Event(ClientProxy clientProxy)
        {
            DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<ColumnDefItem> curSampleColumnDef = new List<ColumnDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                bool isStop = stopQuery();
                if (isStop)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader fileInfo = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    if (fileInfo != null)
                    {
                        DTDataHeaderManager.GetInstance().AddDTDataHeader(fileInfo);
                    }
                }
                else if (package.Content.Type == ResponseType.COLUMN_DEFINE)
                {
                    curSampleColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurColumnDef(idpairs, curSampleColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_EVENT)
                {
                    NREventHelper.ReSetIntCI(curSampleColumnDef);
                    fillData(headerManager, curSampleColumnDef, package);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_EVENT_NR)
                {
                    NREventHelper.SetLongCI(curSampleColumnDef);
                    fillData(headerManager, curSampleColumnDef, package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                #endregion

                if (!MainModel.IsBackground && !MainModel.QueryFromBackground)
                {
                    setProgressPercent(ref index, ref progress);
                }
            }
        }

        private bool stopQuery()
        {
            if (!MainModel.IsBackground && !MainModel.QueryFromBackground)
            {
                if (WaitBox.CancelRequest)
                {
                    return true;
                }
            }
            else if (MainModel.BackgroundStopRequest)
            {
                return true;
            }
            return false;
        }

        private void fillData(DTDataHeaderManager headerManager, List<ColumnDefItem> curSampleColumnDef, Package package)
        {
            Event evt = Event.Create(package.Content, curSampleColumnDef);
            DTDataHeader header = headerManager.GetHeaderByFileID(evt.FileID);
            if (header == null)
            {
                return;
            }
            evt.ApplyHeader(header);
            bool isValid = judgeValidEvt(evt);
            if (!isValid)
            {
                return;
            }
            if (isValidPoint(evt.Longitude, evt.Latitude))
            {
                if (frequencyRangeFilter == 2 && getFrequency(evt) != 900)
                {
                    return;
                }
                if (frequencyRangeFilter == 3 && getFrequency(evt) != 1800)
                {
                    return;
                }
                if (isAddEventToDTDataManager)
                {
                    MainModel.DTDataManager.Add(evt);
                }
                else
                {
                    doWithDTData(evt);
                }
            }
        }

        private bool judgeValidEvt(Event evt)
        {
            if (Condition.FilterOffValue9)
            {
                object obj = evt["Value9"];
                if (obj == null)
                {
                    //
                }
                else if (obj is long)
                {
                    if ((long)obj == -1)
                    {
                        return false;
                    }
                }
                else if (obj is int)
                {
                    int value = (int)obj;
                    if (value == -1)
                    {
                        return false;
                    }
                }
            }
            return true;
        }

        private int getFrequency(Event e)
        {
            int frequencyRange = (int)(long)e["Value10"];
            if (frequencyRange >= 1 && frequencyRange <= 94)
            {
                return 900;
            }
            else if ((frequencyRange >= 512 && frequencyRange <= 636) || (frequencyRange >= 737 && frequencyRange <= 849))
            {
                return 1800;
            }
            return 0;
        }

        protected virtual void prepareStatPackage_Event_FileFilter(Package package, TimePeriod period, bool byRound)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
            package.Content.PrepareAddParam();
            if (byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            AddDIYRegion_Intersect(package);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);
            if (condition.DeviceTypes.Count != 0)
            {
                AddDIYDeviceType(package, condition.DeviceTypes);
            }
            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            AddDIYMomt(package, condition.Momt);
            //
            AddDIYEndOpFlag(package);
        }

        protected void prepareStatPackage_Event_EventFilter(Package package, TimePeriod period)
        {
            if (Condition.EventIDs.Count > 0 && Condition.EventIDs[0] != -1)
            {
                package.Content.AddParam((byte)OpOptionDef.InSelect);
                package.Content.AddParam("0,7,43");
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < Condition.EventIDs.Count; i++)
                {
                    int id = Condition.EventIDs[i];
                    sb.Append(id);
                    if (i < Condition.EventIDs.Count - 1)
                    {
                        sb.Append(",");
                    }
                }
                package.Content.AddParam(sb.ToString());
            }
            prepareOtherEventFilter(package);
            prepareTimeSpanIntersect(package, period);
            AddDIYEndOpFlag(package);
        }
        protected virtual void prepareOtherEventFilter(Package package)
        {

        }
        protected virtual void prepareTimeSpanIntersect(Package package, TimePeriod period)
        {

        }
        protected virtual bool isValidPoint(double ltX, double ltY, double brX, double brY)
        {
            return true;
        }
        protected virtual bool isValidPoint(double jd, double wd)
        {
            return true;
        }

    }
}
