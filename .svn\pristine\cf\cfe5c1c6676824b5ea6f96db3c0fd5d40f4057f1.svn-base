﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NoCellCoverSetDlg : BaseDialog
    {
        public NoCellCoverSetDlg()
        {
            InitializeComponent();
        }

        public void SetSettingFilterRet(int maxRxlev, int roadDistance)
        {
            numRxlevThreshold.Value = maxRxlev;
            numDistance.Value = roadDistance;
        }

        public void GetSettingFilterRet(out int maxRxlev, out int roadDistance)
        {
            maxRxlev = (int)numRxlevThreshold.Value;
            roadDistance = (int)numDistance.Value;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}