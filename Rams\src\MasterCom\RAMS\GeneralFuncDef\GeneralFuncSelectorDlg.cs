﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.GeneralFuncDef;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.Func.OwnSampleAnalyse
{
    public partial class GeneralFuncSelectorDlg : BaseFormStyle
    {
        List<GeneralRoutine> settings;
        public GeneralFuncSelectorDlg()
        {
            InitializeComponent();
            settings = loadOptionSettings();
            FreshSetting();
        }

        internal GeneralRoutine GetSelectedRoutine()
        {
            return cbxOwnFunc.SelectedItem as GeneralRoutine;
        }

        internal void FreshSetting()
        {
            cbxOwnFunc.Items.Clear();
            foreach (GeneralRoutine cmder in settings)
            {
                cbxOwnFunc.Items.Add(cmder);
            }
            if(cbxOwnFunc.Items.Count>0)
            {
                cbxOwnFunc.SelectedIndex = 0;
            }
        }

        private List<GeneralRoutine> loadOptionSettings()
        {
            List<GeneralRoutine> optionList = new List<GeneralRoutine>();
            try
            {
                XmlConfigFile configFile = new XmlConfigFile(string.Format(Application.StartupPath + "/config/generalroutine.xml"));
                List<Object> list = configFile.GetItemValue("GeneralRoutineOptions", "options") as List<Object>;
                if (list != null)
                {
                    foreach (object value in list)
                    {
                        GeneralRoutine option = new GeneralRoutine();
                        option.Param = value as Dictionary<string, object>;
                        optionList.Add(option);
                    }
                }
            }
            catch
            {
                //continue
            }
            return optionList;
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            GeneralFuncSettingForm dlg = new GeneralFuncSettingForm();
            dlg.FillCurrentSettings(settings,cbxOwnFunc.SelectedItem as GeneralRoutine);
            dlg.ShowDialog();
            FreshSetting();
            

        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if(cbxOwnFunc.SelectedItem == null)
            {
                XtraMessageBox.Show("请选择扩展操作！");
                return;
            }
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}