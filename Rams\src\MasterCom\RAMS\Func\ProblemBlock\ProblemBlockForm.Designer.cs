﻿namespace MasterCom.RAMS.Func.ProblemBlock
{
    partial class ProblemBlockForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ProblemBlockForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miViewDetail = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportBBReport = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportSelectedBBReport = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.filterObjectListView = new BrightIdeasSoftware.FilterObjectListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnReason = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnVIP = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAbDays = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNormalDays = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAbEvents = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatus = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCenterLong = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCenterLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPlaceDes = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCreateDate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCloseDate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFirstAbDate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLastAbDate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLastTestDate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDeliveryDate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOrderSeq = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBlockedCallNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDropCallNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherEventNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.filterObjectListViewSecond = new System.Windows.Forms.ListView();
            this.columnHeader21 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader22 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader55 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader23 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader47 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader48 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader49 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader50 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader51 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader52 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader37 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader38 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader39 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader44 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader45 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader46 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader24 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader25 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader26 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader27 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader28 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader9 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader10 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader11 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ExcelStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.StripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.problemSummarryListView = new System.Windows.Forms.ListView();
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader5 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader6 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader7 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader8 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.contextMenuStrip2 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.rabProblemLayer = new System.Windows.Forms.RadioButton();
            this.rabEventLayer = new System.Windows.Forms.RadioButton();
            this.cbxShowType = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.cheOrder = new DevExpress.XtraEditors.CheckEdit();
            this.columnHeader12 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader13 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.filterObjectListView)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            this.contextMenuStrip1.SuspendLayout();
            this.xtraTabPage3.SuspendLayout();
            this.contextMenuStrip2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cheOrder.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miViewDetail,
            this.miExportExcel,
            this.miExportBBReport,
            this.miExportSelectedBBReport,
            this.ToolMenuItem});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(192, 114);
            // 
            // miViewDetail
            // 
            this.miViewDetail.Name = "miViewDetail";
            this.miViewDetail.Size = new System.Drawing.Size(191, 22);
            this.miViewDetail.Text = "问题点信息详情...";
            this.miViewDetail.Click += new System.EventHandler(this.miViewDetail_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(191, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // miExportBBReport
            // 
            this.miExportBBReport.Name = "miExportBBReport";
            this.miExportBBReport.Size = new System.Drawing.Size(191, 22);
            this.miExportBBReport.Text = "导出全部问题点报告...";
            this.miExportBBReport.Click += new System.EventHandler(this.miExportBBReport_Click);
            // 
            // miExportSelectedBBReport
            // 
            this.miExportSelectedBBReport.Name = "miExportSelectedBBReport";
            this.miExportSelectedBBReport.Size = new System.Drawing.Size(191, 22);
            this.miExportSelectedBBReport.Text = "导出选中问题点报告...";
            this.miExportSelectedBBReport.Click += new System.EventHandler(this.miExportSelectedBBReport_Click);
            // 
            // ToolMenuItem
            // 
            this.ToolMenuItem.Name = "ToolMenuItem";
            this.ToolMenuItem.Size = new System.Drawing.Size(191, 22);
            this.ToolMenuItem.Text = "导出问题点图层";
            this.ToolMenuItem.Click += new System.EventHandler(this.ToolMenuItem_Click);
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 28);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(1094, 390);
            this.xtraTabControl1.TabIndex = 1;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.filterObjectListView);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(1087, 360);
            this.xtraTabPage1.Text = "问题点详细列表";
            // 
            // filterObjectListView
            // 
            this.filterObjectListView.AllColumns.Add(this.olvColumnSN);
            this.filterObjectListView.AllColumns.Add(this.olvColumnID);
            this.filterObjectListView.AllColumns.Add(this.olvColumnName);
            this.filterObjectListView.AllColumns.Add(this.olvColumnReason);
            this.filterObjectListView.AllColumns.Add(this.olvColumnVIP);
            this.filterObjectListView.AllColumns.Add(this.olvColumnAbDays);
            this.filterObjectListView.AllColumns.Add(this.olvColumnNormalDays);
            this.filterObjectListView.AllColumns.Add(this.olvColumnAbEvents);
            this.filterObjectListView.AllColumns.Add(this.olvColumnStatus);
            this.filterObjectListView.AllColumns.Add(this.olvColumnCenterLong);
            this.filterObjectListView.AllColumns.Add(this.olvColumnCenterLat);
            this.filterObjectListView.AllColumns.Add(this.olvColumnPlaceDes);
            this.filterObjectListView.AllColumns.Add(this.olvColumnCreateDate);
            this.filterObjectListView.AllColumns.Add(this.olvColumnCloseDate);
            this.filterObjectListView.AllColumns.Add(this.olvColumnFirstAbDate);
            this.filterObjectListView.AllColumns.Add(this.olvColumnLastAbDate);
            this.filterObjectListView.AllColumns.Add(this.olvColumnLastTestDate);
            this.filterObjectListView.AllColumns.Add(this.olvColumnDeliveryDate);
            this.filterObjectListView.AllColumns.Add(this.olvColumnOrderSeq);
            this.filterObjectListView.AllColumns.Add(this.olvColumnBlockedCallNum);
            this.filterObjectListView.AllColumns.Add(this.olvColumnDropCallNum);
            this.filterObjectListView.AllColumns.Add(this.olvColumnOtherEventNum);
            this.filterObjectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnID,
            this.olvColumnName,
            this.olvColumnReason,
            this.olvColumnVIP,
            this.olvColumnAbDays,
            this.olvColumnNormalDays,
            this.olvColumnAbEvents,
            this.olvColumnStatus,
            this.olvColumnCenterLong,
            this.olvColumnCenterLat,
            this.olvColumnPlaceDes,
            this.olvColumnCreateDate,
            this.olvColumnCloseDate,
            this.olvColumnFirstAbDate,
            this.olvColumnLastAbDate,
            this.olvColumnLastTestDate,
            this.olvColumnDeliveryDate,
            this.olvColumnOrderSeq,
            this.olvColumnBlockedCallNum,
            this.olvColumnDropCallNum,
            this.olvColumnOtherEventNum});
            this.filterObjectListView.ContextMenuStrip = this.ctxMenu;
            this.filterObjectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.filterObjectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.filterObjectListView.FullRowSelect = true;
            this.filterObjectListView.GridLines = true;
            this.filterObjectListView.HeaderWordWrap = true;
            this.filterObjectListView.IsNeedShowOverlay = false;
            this.filterObjectListView.Location = new System.Drawing.Point(0, 0);
            this.filterObjectListView.Name = "filterObjectListView";
            this.filterObjectListView.ShowGroups = false;
            this.filterObjectListView.Size = new System.Drawing.Size(1087, 360);
            this.filterObjectListView.TabIndex = 5;
            this.filterObjectListView.UseAlternatingBackColors = true;
            this.filterObjectListView.UseCompatibleStateImageBehavior = false;
            this.filterObjectListView.View = System.Windows.Forms.View.Details;
            this.filterObjectListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.filterObjectListView_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "Index";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "地市";
            this.olvColumnSN.Width = 66;
            // 
            // olvColumnID
            // 
            this.olvColumnID.AspectName = "blockId";
            this.olvColumnID.HeaderFont = null;
            this.olvColumnID.Text = "问题点ID";
            this.olvColumnID.Width = 77;
            // 
            // olvColumnName
            // 
            this.olvColumnName.AspectName = "name";
            this.olvColumnName.HeaderFont = null;
            this.olvColumnName.Text = "名称";
            this.olvColumnName.Width = 90;
            // 
            // olvColumnReason
            // 
            this.olvColumnReason.HeaderFont = null;
            this.olvColumnReason.Text = "来源";
            // 
            // olvColumnVIP
            // 
            this.olvColumnVIP.AspectName = "VIP";
            this.olvColumnVIP.HeaderFont = null;
            this.olvColumnVIP.Text = "权重值";
            this.olvColumnVIP.Width = 80;
            // 
            // olvColumnAbDays
            // 
            this.olvColumnAbDays.AspectName = "abnormal_days";
            this.olvColumnAbDays.HeaderFont = null;
            this.olvColumnAbDays.Text = "问题天数";
            this.olvColumnAbDays.Width = 70;
            // 
            // olvColumnNormalDays
            // 
            this.olvColumnNormalDays.AspectName = "normal_days";
            this.olvColumnNormalDays.HeaderFont = null;
            this.olvColumnNormalDays.Text = "正常天数";
            this.olvColumnNormalDays.Width = 68;
            // 
            // olvColumnAbEvents
            // 
            this.olvColumnAbEvents.AspectName = "abnormal_event_count";
            this.olvColumnAbEvents.HeaderFont = null;
            this.olvColumnAbEvents.Text = "异常事件个数";
            this.olvColumnAbEvents.Width = 93;
            // 
            // olvColumnStatus
            // 
            this.olvColumnStatus.AspectName = "StatusDes";
            this.olvColumnStatus.HeaderFont = null;
            this.olvColumnStatus.Text = "状态";
            this.olvColumnStatus.Width = 123;
            // 
            // olvColumnCenterLong
            // 
            this.olvColumnCenterLong.HeaderFont = null;
            this.olvColumnCenterLong.Text = "中心点经度";
            this.olvColumnCenterLong.Width = 122;
            // 
            // olvColumnCenterLat
            // 
            this.olvColumnCenterLat.HeaderFont = null;
            this.olvColumnCenterLat.Text = "中心点纬度";
            this.olvColumnCenterLat.Width = 133;
            // 
            // olvColumnPlaceDes
            // 
            this.olvColumnPlaceDes.AspectName = "placeDes";
            this.olvColumnPlaceDes.HeaderFont = null;
            this.olvColumnPlaceDes.Text = "位置描述";
            this.olvColumnPlaceDes.Width = 181;
            // 
            // olvColumnCreateDate
            // 
            this.olvColumnCreateDate.AspectName = "CreateDateString";
            this.olvColumnCreateDate.HeaderFont = null;
            this.olvColumnCreateDate.Text = "问题点创建时间";
            this.olvColumnCreateDate.Width = 140;
            // 
            // olvColumnCloseDate
            // 
            this.olvColumnCloseDate.AspectName = "ClosedDateString";
            this.olvColumnCloseDate.HeaderFont = null;
            this.olvColumnCloseDate.Text = "问题点关闭时间";
            this.olvColumnCloseDate.Width = 140;
            // 
            // olvColumnFirstAbDate
            // 
            this.olvColumnFirstAbDate.AspectName = "First_abnormal_dateString";
            this.olvColumnFirstAbDate.HeaderFont = null;
            this.olvColumnFirstAbDate.Text = "第一个异常时间";
            this.olvColumnFirstAbDate.Width = 140;
            // 
            // olvColumnLastAbDate
            // 
            this.olvColumnLastAbDate.AspectName = "Last_abnormal_dateString";
            this.olvColumnLastAbDate.HeaderFont = null;
            this.olvColumnLastAbDate.Text = "最后异常时间";
            this.olvColumnLastAbDate.Width = 140;
            // 
            // olvColumnLastTestDate
            // 
            this.olvColumnLastTestDate.AspectName = "Last_test_dateString";
            this.olvColumnLastTestDate.HeaderFont = null;
            this.olvColumnLastTestDate.Text = "最后测试时间";
            this.olvColumnLastTestDate.Width = 140;
            // 
            // olvColumnDeliveryDate
            // 
            this.olvColumnDeliveryDate.AspectName = "Delivery_DateString";
            this.olvColumnDeliveryDate.HeaderFont = null;
            this.olvColumnDeliveryDate.Text = "派单日期";
            this.olvColumnDeliveryDate.Width = 140;
            // 
            // olvColumnOrderSeq
            // 
            this.olvColumnOrderSeq.AspectName = "order_seq";
            this.olvColumnOrderSeq.HeaderFont = null;
            this.olvColumnOrderSeq.Text = "工单ID";
            this.olvColumnOrderSeq.Width = 150;
            // 
            // olvColumnBlockedCallNum
            // 
            this.olvColumnBlockedCallNum.HeaderFont = null;
            this.olvColumnBlockedCallNum.Text = "未接通事件个数";
            this.olvColumnBlockedCallNum.Width = 120;
            // 
            // olvColumnDropCallNum
            // 
            this.olvColumnDropCallNum.HeaderFont = null;
            this.olvColumnDropCallNum.Text = "掉话事件个数";
            this.olvColumnDropCallNum.Width = 120;
            // 
            // olvColumnOtherEventNum
            // 
            this.olvColumnOtherEventNum.HeaderFont = null;
            this.olvColumnOtherEventNum.Text = "其他事件个数";
            this.olvColumnOtherEventNum.Width = 200;
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.filterObjectListViewSecond);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(1087, 360);
            this.xtraTabPage2.Text = "显性问题点详细列表";
            // 
            // filterObjectListViewSecond
            // 
            this.filterObjectListViewSecond.Alignment = System.Windows.Forms.ListViewAlignment.Left;
            this.filterObjectListViewSecond.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader21,
            this.columnHeader22,
            this.columnHeader55,
            this.columnHeader23,
            this.columnHeader47,
            this.columnHeader48,
            this.columnHeader49,
            this.columnHeader50,
            this.columnHeader51,
            this.columnHeader52,
            this.columnHeader37,
            this.columnHeader38,
            this.columnHeader39,
            this.columnHeader44,
            this.columnHeader45,
            this.columnHeader46,
            this.columnHeader24,
            this.columnHeader25,
            this.columnHeader26,
            this.columnHeader27,
            this.columnHeader28,
            this.columnHeader1,
            this.columnHeader9,
            this.columnHeader10,
            this.columnHeader11,
            this.columnHeader12,
            this.columnHeader13});
            this.filterObjectListViewSecond.ContextMenuStrip = this.contextMenuStrip1;
            this.filterObjectListViewSecond.Dock = System.Windows.Forms.DockStyle.Fill;
            this.filterObjectListViewSecond.FullRowSelect = true;
            this.filterObjectListViewSecond.GridLines = true;
            this.filterObjectListViewSecond.Location = new System.Drawing.Point(0, 0);
            this.filterObjectListViewSecond.Name = "filterObjectListViewSecond";
            this.filterObjectListViewSecond.ShowGroups = false;
            this.filterObjectListViewSecond.ShowItemToolTips = true;
            this.filterObjectListViewSecond.Size = new System.Drawing.Size(1087, 360);
            this.filterObjectListViewSecond.TabIndex = 3;
            this.filterObjectListViewSecond.UseCompatibleStateImageBehavior = false;
            this.filterObjectListViewSecond.View = System.Windows.Forms.View.Details;
            this.filterObjectListViewSecond.DoubleClick += new System.EventHandler(this.filterObjectListViewSecond_DoubleClick);
            this.filterObjectListViewSecond.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.filterObjectListViewSecond_MouseDoubleClick);
            // 
            // columnHeader21
            // 
            this.columnHeader21.Text = "地市";
            this.columnHeader21.Width = 80;
            // 
            // columnHeader22
            // 
            this.columnHeader22.Text = "问题点ID";
            this.columnHeader22.Width = 80;
            // 
            // columnHeader55
            // 
            this.columnHeader55.Text = "县";
            // 
            // columnHeader23
            // 
            this.columnHeader23.Text = "镇";
            // 
            // columnHeader47
            // 
            this.columnHeader47.Text = "网格类型";
            // 
            // columnHeader48
            // 
            this.columnHeader48.Text = "网格";
            // 
            // columnHeader49
            // 
            this.columnHeader49.Text = "LAC";
            // 
            // columnHeader50
            // 
            this.columnHeader50.Text = "CI";
            // 
            // columnHeader51
            // 
            this.columnHeader51.Text = "项目类型";
            // 
            // columnHeader52
            // 
            this.columnHeader52.Text = "测试文件名";
            // 
            // columnHeader37
            // 
            this.columnHeader37.Text = "问题点出现时间";
            this.columnHeader37.Width = 100;
            // 
            // columnHeader38
            // 
            this.columnHeader38.Text = "道路类型";
            // 
            // columnHeader39
            // 
            this.columnHeader39.Text = "问题点路段";
            // 
            // columnHeader44
            // 
            this.columnHeader44.Text = "经度";
            // 
            // columnHeader45
            // 
            this.columnHeader45.Text = "纬度";
            // 
            // columnHeader46
            // 
            this.columnHeader46.Text = "是否重复问题点";
            // 
            // columnHeader24
            // 
            this.columnHeader24.Text = "问题点类型";
            this.columnHeader24.Width = 87;
            // 
            // columnHeader25
            // 
            this.columnHeader25.Text = "过去3个月共出现几次显性重复问题点";
            this.columnHeader25.Width = 86;
            // 
            // columnHeader26
            // 
            this.columnHeader26.Text = "过去6个月共出现几次显性重复问题点";
            this.columnHeader26.Width = 79;
            // 
            // columnHeader27
            // 
            this.columnHeader27.Text = "是否需要在下个月进行重点问题测试";
            this.columnHeader27.Width = 83;
            // 
            // columnHeader28
            // 
            this.columnHeader28.Text = "复测渗透次数";
            this.columnHeader28.Width = 78;
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "工单ID";
            this.columnHeader1.Width = 80;
            // 
            // columnHeader9
            // 
            this.columnHeader9.Text = "问题原因";
            this.columnHeader9.Width = 80;
            // 
            // columnHeader10
            // 
            this.columnHeader10.Text = "详细分析";
            this.columnHeader10.Width = 100;
            // 
            // columnHeader11
            // 
            this.columnHeader11.Text = "复测异常次数";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ExcelStripMenuItem,
            this.StripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(183, 48);
            // 
            // ExcelStripMenuItem
            // 
            this.ExcelStripMenuItem.Name = "ExcelStripMenuItem";
            this.ExcelStripMenuItem.Size = new System.Drawing.Size(182, 22);
            this.ExcelStripMenuItem.Text = "导出Excel";
            this.ExcelStripMenuItem.Click += new System.EventHandler(this.ExcelStripMenuItem_Click);
            // 
            // StripMenuItem
            // 
            this.StripMenuItem.Name = "StripMenuItem";
            this.StripMenuItem.Size = new System.Drawing.Size(182, 22);
            this.StripMenuItem.Text = "导出问题点事件图层";
            this.StripMenuItem.Click += new System.EventHandler(this.StripMenuItem_Click);
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.problemSummarryListView);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(1087, 360);
            this.xtraTabPage3.Text = "各地市汇总信息";
            // 
            // problemSummarryListView
            // 
            this.problemSummarryListView.Alignment = System.Windows.Forms.ListViewAlignment.Left;
            this.problemSummarryListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader2,
            this.columnHeader3,
            this.columnHeader4,
            this.columnHeader5,
            this.columnHeader6,
            this.columnHeader7,
            this.columnHeader8});
            this.problemSummarryListView.ContextMenuStrip = this.contextMenuStrip2;
            this.problemSummarryListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.problemSummarryListView.FullRowSelect = true;
            this.problemSummarryListView.GridLines = true;
            this.problemSummarryListView.Location = new System.Drawing.Point(0, 0);
            this.problemSummarryListView.Name = "problemSummarryListView";
            this.problemSummarryListView.ShowGroups = false;
            this.problemSummarryListView.ShowItemToolTips = true;
            this.problemSummarryListView.Size = new System.Drawing.Size(1087, 360);
            this.problemSummarryListView.TabIndex = 4;
            this.problemSummarryListView.UseCompatibleStateImageBehavior = false;
            this.problemSummarryListView.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "序号";
            this.columnHeader2.Width = 80;
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "地市";
            this.columnHeader3.Width = 90;
            // 
            // columnHeader4
            // 
            this.columnHeader4.Text = "派单总数";
            this.columnHeader4.Width = 90;
            // 
            // columnHeader5
            // 
            this.columnHeader5.Text = "事件总数";
            this.columnHeader5.Width = 90;
            // 
            // columnHeader6
            // 
            this.columnHeader6.Text = "派单事件数";
            this.columnHeader6.Width = 90;
            // 
            // columnHeader7
            // 
            this.columnHeader7.Text = "掉话事件数";
            this.columnHeader7.Width = 100;
            // 
            // columnHeader8
            // 
            this.columnHeader8.Text = "未接通事件数";
            this.columnHeader8.Width = 100;
            // 
            // contextMenuStrip2
            // 
            this.contextMenuStrip2.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItem});
            this.contextMenuStrip2.Name = "contextMenuStrip2";
            this.contextMenuStrip2.Size = new System.Drawing.Size(125, 26);
            // 
            // ToolStripMenuItem
            // 
            this.ToolStripMenuItem.Name = "ToolStripMenuItem";
            this.ToolStripMenuItem.Size = new System.Drawing.Size(124, 22);
            this.ToolStripMenuItem.Text = "导出Excel";
            this.ToolStripMenuItem.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // rabProblemLayer
            // 
            this.rabProblemLayer.AutoSize = true;
            this.rabProblemLayer.Checked = true;
            this.rabProblemLayer.Location = new System.Drawing.Point(306, 0);
            this.rabProblemLayer.Name = "rabProblemLayer";
            this.rabProblemLayer.Size = new System.Drawing.Size(109, 18);
            this.rabProblemLayer.TabIndex = 2;
            this.rabProblemLayer.TabStop = true;
            this.rabProblemLayer.Text = "显示问题点图层";
            this.rabProblemLayer.UseVisualStyleBackColor = true;
            this.rabProblemLayer.CheckedChanged += new System.EventHandler(this.rabProblemLayer_CheckedChanged);
            // 
            // rabEventLayer
            // 
            this.rabEventLayer.AutoSize = true;
            this.rabEventLayer.Location = new System.Drawing.Point(432, 0);
            this.rabEventLayer.Name = "rabEventLayer";
            this.rabEventLayer.Size = new System.Drawing.Size(109, 18);
            this.rabEventLayer.TabIndex = 3;
            this.rabEventLayer.Text = "显示事件点图层";
            this.rabEventLayer.UseVisualStyleBackColor = true;
            // 
            // cbxShowType
            // 
            this.cbxShowType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxShowType.FormattingEnabled = true;
            this.cbxShowType.Location = new System.Drawing.Point(850, -1);
            this.cbxShowType.Name = "cbxShowType";
            this.cbxShowType.Size = new System.Drawing.Size(85, 22);
            this.cbxShowType.TabIndex = 24;
            this.cbxShowType.SelectedIndexChanged += new System.EventHandler(this.cbxShowType_SelectedIndexChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(719, 4);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(115, 14);
            this.label1.TabIndex = 25;
            this.label1.Text = "按重复事件数显示：";
            // 
            // cheOrder
            // 
            this.cheOrder.Location = new System.Drawing.Point(562, 0);
            this.cheOrder.Name = "cheOrder";
            this.cheOrder.Properties.Caption = "勾上只画派单图层";
            this.cheOrder.Size = new System.Drawing.Size(151, 19);
            this.cheOrder.TabIndex = 26;
            this.cheOrder.CheckedChanged += new System.EventHandler(this.cheOrder_CheckedChanged);
            // 
            // columnHeader12
            // 
            this.columnHeader12.Text = "复测异常事件数";
            // 
            // columnHeader13
            // 
            this.columnHeader13.Text = "主被叫同时掉话数";
            // 
            // ProblemBlockForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1094, 418);
            this.Controls.Add(this.cheOrder);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.cbxShowType);
            this.Controls.Add(this.rabEventLayer);
            this.Controls.Add(this.rabProblemLayer);
            this.Controls.Add(this.xtraTabControl1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ProblemBlockForm";
            this.ShowInTaskbar = false;
            this.Text = "问题点列表";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.filterObjectListView)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            this.contextMenuStrip1.ResumeLayout(false);
            this.xtraTabPage3.ResumeLayout(false);
            this.contextMenuStrip2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.cheOrder.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miViewDetail;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miExportBBReport;
        private System.Windows.Forms.ToolStripMenuItem miExportSelectedBBReport;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private BrightIdeasSoftware.FilterObjectListView filterObjectListView;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnID;
        private BrightIdeasSoftware.OLVColumn olvColumnName;
        private BrightIdeasSoftware.OLVColumn olvColumnReason;
        private BrightIdeasSoftware.OLVColumn olvColumnVIP;
        private BrightIdeasSoftware.OLVColumn olvColumnAbDays;
        private BrightIdeasSoftware.OLVColumn olvColumnNormalDays;
        private BrightIdeasSoftware.OLVColumn olvColumnAbEvents;
        private BrightIdeasSoftware.OLVColumn olvColumnStatus;
        private BrightIdeasSoftware.OLVColumn olvColumnCenterLong;
        private BrightIdeasSoftware.OLVColumn olvColumnCenterLat;
        private BrightIdeasSoftware.OLVColumn olvColumnPlaceDes;
        private BrightIdeasSoftware.OLVColumn olvColumnCreateDate;
        private BrightIdeasSoftware.OLVColumn olvColumnCloseDate;
        private BrightIdeasSoftware.OLVColumn olvColumnFirstAbDate;
        private BrightIdeasSoftware.OLVColumn olvColumnLastAbDate;
        private BrightIdeasSoftware.OLVColumn olvColumnLastTestDate;
        private BrightIdeasSoftware.OLVColumn olvColumnDeliveryDate;
        private BrightIdeasSoftware.OLVColumn olvColumnOrderSeq;
        private BrightIdeasSoftware.OLVColumn olvColumnBlockedCallNum;
        private BrightIdeasSoftware.OLVColumn olvColumnDropCallNum;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherEventNum;
        private System.Windows.Forms.ListView filterObjectListViewSecond;
        private System.Windows.Forms.ColumnHeader columnHeader21;
        private System.Windows.Forms.ColumnHeader columnHeader22;
        private System.Windows.Forms.ColumnHeader columnHeader55;
        private System.Windows.Forms.ColumnHeader columnHeader23;
        private System.Windows.Forms.ColumnHeader columnHeader47;
        private System.Windows.Forms.ColumnHeader columnHeader48;
        private System.Windows.Forms.ColumnHeader columnHeader49;
        private System.Windows.Forms.ColumnHeader columnHeader50;
        private System.Windows.Forms.ColumnHeader columnHeader51;
        private System.Windows.Forms.ColumnHeader columnHeader52;
        private System.Windows.Forms.ColumnHeader columnHeader37;
        private System.Windows.Forms.ColumnHeader columnHeader38;
        private System.Windows.Forms.ColumnHeader columnHeader39;
        private System.Windows.Forms.ColumnHeader columnHeader44;
        private System.Windows.Forms.ColumnHeader columnHeader45;
        private System.Windows.Forms.ColumnHeader columnHeader46;
        private System.Windows.Forms.ColumnHeader columnHeader24;
        private System.Windows.Forms.ColumnHeader columnHeader25;
        private System.Windows.Forms.ColumnHeader columnHeader26;
        private System.Windows.Forms.ColumnHeader columnHeader27;
        private System.Windows.Forms.ColumnHeader columnHeader28;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ExcelStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ToolMenuItem;
        private System.Windows.Forms.ToolStripMenuItem StripMenuItem;
        private System.Windows.Forms.RadioButton rabProblemLayer;
        private System.Windows.Forms.RadioButton rabEventLayer;
        private System.Windows.Forms.ColumnHeader columnHeader1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private System.Windows.Forms.ListView problemSummarryListView;
        private System.Windows.Forms.ColumnHeader columnHeader2;
        private System.Windows.Forms.ColumnHeader columnHeader3;
        private System.Windows.Forms.ColumnHeader columnHeader4;
        private System.Windows.Forms.ColumnHeader columnHeader5;
        private System.Windows.Forms.ColumnHeader columnHeader6;
        private System.Windows.Forms.ComboBox cbxShowType;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ColumnHeader columnHeader7;
        private System.Windows.Forms.ColumnHeader columnHeader8;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip2;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItem;
        private System.Windows.Forms.ColumnHeader columnHeader9;
        private System.Windows.Forms.ColumnHeader columnHeader10;
        private System.Windows.Forms.ColumnHeader columnHeader11;
        private DevExpress.XtraEditors.CheckEdit cheOrder;
        private System.Windows.Forms.ColumnHeader columnHeader12;
        private System.Windows.Forms.ColumnHeader columnHeader13;
    }
}