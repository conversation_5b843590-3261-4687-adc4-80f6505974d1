﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.TestDepth
{
    public partial class GridPercentageSettingDlg : BaseForm
    {
        public GridPercentageSettingDlg(MasterCom.RAMS.ZTFunc.QueryTPGridTestDepth.GridCondition condition)
        {
            InitializeComponent();
            if (condition!=null)
            {
                numSize.Value = (decimal)condition.GridSize;
                dtHistoryBegin.Value = condition.HistoryPeriod.BeginTime.Date;
                dtHistoryEnd.Value = condition.HistoryPeriod.EndTime.Date;
                dtBegin.Value = condition.EstimatePeriod.BeginTime.Date;
                dtEnd.Value = condition.EstimatePeriod.EndTime.Date;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (dtHistoryBegin.Value > dtHistoryEnd.Value)
            {
                MessageBox.Show("基准数据时间段设置有误！开始时间不能大于结束时间！");
                return;
            }
            if (dtBegin.Value > dtEnd.Value)
            {
                MessageBox.Show("评估数据时间段设置有误！开始时间不能大于结束时间！");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        public MasterCom.RAMS.ZTFunc.QueryTPGridTestDepth.GridCondition GetCondition()
        {
            return new QueryTPGridTestDepth.GridCondition((int)numSize.Value
                , new TimePeriod(dtHistoryBegin.Value.Date, dtHistoryEnd.Value.Date.AddDays(1).AddMilliseconds(-1))
                , new TimePeriod(dtBegin.Value.Date, dtEnd.Value.Date.AddDays(1).AddMilliseconds(-1)));
        }

    }
}
