using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Text;

namespace MasterCom.Util
{
    public class DllInvoke
    {
        #region Win API
        [DllImport("kernel32", SetLastError = true, CharSet = CharSet.Auto)]
        private extern static IntPtr LoadLibrary(string path);

        //[DllImport("kernel32", SetLastError = true)]
        //private extern static IntPtr GetProcAddress(IntPtr lib, string funcName);

        [DllImport("kernel32", CharSet = CharSet.Ansi, ExactSpelling = true, SetLastError = true)]
        static extern IntPtr GetProcAddress(IntPtr hModule, string procName);


        [DllImport("kernel32", SetLastError = true, CharSet = CharSet.Auto)]
        private extern static bool FreeLibrary(IntPtr lib);

        [DllImport("kernel32.dll", CharSet = CharSet.Unicode, SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool SetDllDirectory(string pathName);
        #endregion

        private readonly IntPtr hLib;
        public DllInvoke(String dllPath)
        {
            string dir = System.IO.Path.GetDirectoryName(dllPath);
            SetDllDirectory(dir);
            hLib = LoadLibrary(dllPath);
            int x = Marshal.GetLastWin32Error();
            if (x != 0)
            {
                //System.Windows.Forms.MessageBox.Show("LoadLibrary error" + x);
            }
        }

        ~DllInvoke()
        {
            FreeLibrary(hLib);
        }

        //将要执行的函数转换为委托 回调
        public Delegate Invoke(string apiName, Type t)
        {
            IntPtr api = GetProcAddress(hLib, apiName);
            int x = Marshal.GetLastWin32Error();
            if (x != 0)
            {
                //System.Windows.Forms.MessageBox.Show("GetProcAddress error:" + x);
            }
            return Marshal.GetDelegateForFunctionPointer(api, t);
        }

    }
}
