﻿using BrightIdeasSoftware;
using DevExpress.XtraCharts;
using DevExpress.XtraGrid.Views.Grid;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class DelayReasonResultDlg : MinCloseForm
    {
        public DelayReasonResultDlg()
            : base()
        {
            InitializeComponent();
            mapForm = MainModel.MainForm.GetMapForm();
            init();
            DisposeWhenClose = true;
        }
        private MapForm mapForm = null;
        private List<ReasonInfo> resultListMo = new List<ReasonInfo>();
        private List<ReasonInfo> resultListMt = new List<ReasonInfo>();
        private Dictionary<string, NumCal> dicMo = null;
        private Dictionary<string, NumCal> dicMt = null;

        private void init()
        {
            #region MO时延分析界面
            this.listViewMo.CanExpandGetter = delegate (object x)
            {
                return x is ReasonInfo;
            };
            this.listViewMo.ChildrenGetter = delegate (object x)
            {
                ReasonInfo info = x as ReasonInfo;
                return info.DetailList;
            };
            this.olvColumnSN.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfo)
                {
                    ReasonInfo info = row as ReasonInfo;
                    return info.SN;
                }
                return "";
            };
            this.olvColumnMoFileName.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfo)
                {
                    ReasonInfo info = row as ReasonInfo;
                    return info.MoFileName;
                }
                return "";
            };
            this.olvColumnMtFileName.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfo)
                {
                    ReasonInfo info = row as ReasonInfo;
                    return info.MtFileName;
                }
                return "";
            };
            setMOReasonInfoDetail();
            #endregion

            #region MT时延分析界面
            this.listViewMt.CanExpandGetter = delegate (object row)
            {
                return row is ReasonInfo;
            };
            this.listViewMt.ChildrenGetter = delegate (object row)
            {
                ReasonInfo info = row as ReasonInfo;
                return info.DetailList;
            };
            this.olvColumnMtSN.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfo)
                {
                    ReasonInfo info = row as ReasonInfo;
                    return info.SN;
                }
                return "";
            };
            this.olvColumnMtMoFileName.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfo)
                {
                    ReasonInfo info = row as ReasonInfo;
                    return info.MoFileName;
                }
                return "";
            };
            this.olvColumnMtMtFileName.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfo)
                {
                    ReasonInfo info = row as ReasonInfo;
                    return info.MtFileName;
                }
                return "";
            };
            setMTReasonInfoDetail();
            #endregion
        }

        private void setMOReasonInfoDetail()
        {
            this.olvColumnDesc.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return detail.Desc;
                }
                return "";
            };
            this.olvColumnBeginTime.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return detail.BeginTime.ToString("HH:mm:ss.fff");
                }
                return "";
            };
            this.olvColumnEndTime.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return detail.EndTime.ToString("HH:mm:ss.fff");
                }
                return "";
            };
            this.olvColumnSpanTime.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return detail.SpanTime;
                }
                return "";
            };
            this.olvColumnConTime.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return detail.TimeCondition;
                }
                return "";
            };
            this.olvColumnNormal.RendererDelegate = delegate (EventArgs e
                , Graphics g, Rectangle r, Object row)
            {
                if (row is ReasonInfoDetail)
                {
                    return setNormal(g, r, row);
                }
                return false;
            };
            this.olvColumnNormal.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return setDesc(detail.IsNormal);
                }
                return "";
            };
            this.olvColumnHandover.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return setDesc(detail.IsHandover);
                }
                return "";
            };
            this.olvColumnTA.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return setDesc(detail.IsTaupdate);
                }
                return "";
            };
            this.olvColumnCover.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return setDesc(detail.IsCover);
                }
                return "";
            };
            this.olvColumnHeart.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return setDesc(detail.IsHeartNet);
                }
                return "";
            };
            this.olvColumnEnd.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return setDesc(detail.IsEnd);
                }
                return "";
            };
            this.olvColumnOverNet.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return setDesc(detail.IsOverNet);
                }
                return "";
            };
            this.olvColumnMtAnalyse.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return setDesc(detail.IsMtAnalyse);
                }
                return "";
            };
        }

        private void setMTReasonInfoDetail()
        {
            this.olvColumnMtDesc.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return detail.Desc;
                }
                return "";
            };
            this.olvColumnMtBeginTIme.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return detail.BeginTime.ToString("HH:mm:ss.fff");
                }
                return "";
            };
            this.olvColumnMtEndTime.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return detail.EndTime.ToString("HH:mm:ss.fff");
                }
                return "";
            };
            this.olvColumnMtSpanTime.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return detail.SpanTime;
                }
                return "";
            };
            this.olvColumnMtConTime.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return detail.TimeCondition;
                }
                return "";
            };
            this.olvColumnMtNormal.RendererDelegate = delegate (EventArgs e,
                Graphics g, Rectangle r, Object row)
            {
                if (row is ReasonInfoDetail)
                {
                    return setNormal(g, r, row);
                }
                return false;
            };
            this.olvColumnMtNormal.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return setDesc(detail.IsNormal);
                }
                return "";
            };
            this.olvColumnMtHandover.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return setDesc(detail.IsHandover);
                }
                return "";
            };
            this.olvColumnMtTA.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return setDesc(detail.IsTaupdate);
                }
                return "";
            };
            this.olvColumnMtCover.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return setDesc(detail.IsCover);
                }
                return "";
            };
            this.olvColumnMtHeart.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return setDesc(detail.IsHeartNet);
                }
                return "";
            };
            this.olvColumnMtEnd.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return setDesc(detail.IsEnd);
                }
                return "";
            };
            this.olvColumnMtMoAnalyse.AspectGetter = delegate (object row)
            {
                if (row is ReasonInfoDetail)
                {
                    ReasonInfoDetail detail = row as ReasonInfoDetail;
                    return setDesc(detail.IsMoAnalyse);
                }
                return "";
            };
        }

        private bool setNormal(Graphics g, Rectangle r, object row)
        {
            ReasonInfoDetail detail = row as ReasonInfoDetail;
            if (!detail.IsNormal)
            {
                Rectangle rect = r;
                rect.Inflate(-2, -2);
                g.DrawString("否", listViewMo.Font,
                    new SolidBrush(Color.Red), r.X, r.Y);
            }
            else
            {
                g.DrawString("是", listViewMo.Font,
                    new SolidBrush(listViewMo.ForeColor), r.X, r.Y);
            }
            return true;
        }

        private object setDesc(bool isTrue)
        {
            if (isTrue)
            {
                return "是";
            }
            return "否";
        }

        public void FillData(List<ReasonInfo> resultListMo,List<ReasonInfo>resultListMt)
        {
            this.resultListMo = resultListMo;
            this.resultListMt = resultListMt;

            listViewMo.RebuildColumns();
            listViewMo.ClearObjects();
            listViewMo.SetObjects(resultListMo);

            listViewMt.RebuildColumns();
            listViewMt.ClearObjects();
            listViewMt.SetObjects(resultListMt);


            refreshSummary(chartControlReason.Series[0], this.resultListMo, true);
            refreshSummary(chartControlMtReason.Series[0], this.resultListMt, false);
            updateSummary(chartControlMo, resultListMo, out dicMo);
            updateSummary(chartControlMt, resultListMt, out dicMt);

            bandData(dicMo, true);
            bandData(dicMt, false);

            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        /// <summary>
        /// 绑定饼状图数据
        /// </summary>
        /// <param name="reason"></param>
        /// <param name="result"></param>
        /// <param name="isMO"></param>
        private void refreshSummary(DevExpress.XtraCharts.Series reason, List<ReasonInfo> result, bool isMO)
        {
            Dictionary<string, int> modic = new Dictionary<string, int>();

            reason.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            reason.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            reason.Points.Clear();

            int moSum = 0;
            foreach (ReasonInfo info in result)
            {
                foreach (ReasonInfoDetail detail in info.DetailList)
                {
                    moSum = dealReasonInfo(isMO, modic, moSum, detail);
                }
            }
            addReason(reason, modic, moSum);
        }

        private int dealReasonInfo(bool isMO, Dictionary<string, int> modic, int moSum, ReasonInfoDetail detail)
        {
            Type t = typeof(ReasonInfoDetail);
            foreach (var item in t.GetProperties())
            {
                if (item.GetValue(detail, null) is bool)
                {
                    if (isMO && item.Name == "IsMoAnalyse")
                    {
                        continue;
                    }
                    if (!isMO && (item.Name == "IsMtAnalyse" || item.Name == "IsOverNet"))
                    {
                        continue;
                    }
                    moSum = addModic(modic, moSum, detail, item);
                }
            }

            return moSum;
        }

        private int addModic(Dictionary<string, int> modic, int moSum, ReasonInfoDetail detail, System.Reflection.PropertyInfo item)
        {
            if (!modic.ContainsKey(item.Name))
            {
                if ((bool)item.GetValue(detail, null))
                {
                    modic.Add(item.Name, 1);
                    moSum++;
                }
                else
                {
                    modic.Add(item.Name, 0);
                }
            }
            else
            {
                if ((bool)item.GetValue(detail, null))
                {
                    moSum++;
                    modic[item.Name]++;
                }
            }

            return moSum;
        }

        private static void addReason(Series reason, Dictionary<string, int> modic, int moSum)
        {
            foreach (KeyValuePair<string, int> pair in modic)
            {
                string name = "";
                switch (pair.Key)
                {
                    case "IsNormal":
                        name = "正常范围";
                        break;
                    case "IsHandover":
                        name = "切换";
                        break;
                    case "IsTaupdate":
                        name = "TA更新";
                        break;
                    case "IsCover":
                        name = "覆盖干扰";
                        break;
                    case "IsHeartNet":
                        name = "核心网核查";
                        break;
                    case "IsEnd":
                        name = "终端核查";
                        break;
                    case "IsOverNet":
                        name = "跨网消息转发";
                        break;
                    case "IsMtAnalyse":
                        name = "被叫测核查";
                        break;
                    case "IsMoAnalyse":
                        name = "主叫测核查";
                        break;
                    default:
                        break;
                }
                DevExpress.XtraCharts.SeriesPoint sp =
                        new DevExpress.XtraCharts.SeriesPoint(name, Math.Round((double)pair.Value / moSum, 2));
                reason.Points.Add(sp);
            }
        }

        /// <summary>
        /// 绑定柱状图数据
        /// </summary>
        /// <param name="charts"></param>
        /// <param name="result"></param>
        /// <param name="dic"></param>
        private void updateSummary(DevExpress.XtraCharts.ChartControl charts, List<ReasonInfo> result, out Dictionary<string,NumCal> dic)
        {
            for (int j = 0; j < charts.Series.Count; j++)
            {
                charts.Series[j].Points.Clear();
            }
            Series reason;
            dic = new Dictionary<string, NumCal>();
            foreach (ReasonInfo info in result)
            {
                foreach (ReasonInfoDetail detail in info.DetailList)
                {
                    NumCal data = new NumCal();
                    data.NumList.Add(detail.SpanTime);
                    if (!dic.ContainsKey(detail.Desc))
                    {
                        dic.Add(detail.Desc, data);
                    }
                    else
                    {
                        dic[detail.Desc].NumList.Add(detail.SpanTime);
                    }
                }
            }
            int index = 0;
            foreach (KeyValuePair<string, NumCal> pair in dic)
            {
                index++;
                string name = GetShortName(pair.Key, index);
                
                for (int i = 0; i < 3; i++)
                {
                    //charts.Series.Insert(index, new Series(name, new ViewType()));
                    reason = charts.Series[i];
                    reason.PointOptions.ValueNumericOptions.Format = NumericFormat.Number;
                    reason.PointOptions.ValueNumericOptions.Precision = 3;
                    switch (i)
                    {
                        case 0:
                            reason.Points.Add(new SeriesPoint(name, pair.Value.Max/1000));
                            break;
                        case 1:
                            reason.Points.Add(new SeriesPoint(name, pair.Value.Avg/1000));
                            break;
                        case 2:
                            reason.Points.Add(new SeriesPoint(name, pair.Value.Min/1000));
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        /// <summary>
        /// 绑定gridview的数据
        /// </summary>
        /// <param name="dic"></param>
        /// <param name="isMo"></param>
        private void bandData(Dictionary<string, NumCal> dic, bool isMo)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("Desc", typeof(string));
            dt.Columns.Add("ShortName", typeof(string));
            dt.Columns.Add("Max", typeof(string));
            dt.Columns.Add("Avg", typeof(string));
            dt.Columns.Add("Min", typeof(string));
            int index = 0;
            foreach (KeyValuePair<string, NumCal> pair in dic)
            {
                index++;
                dt.Rows.Add(pair.Key, GetShortName(pair.Key, index), pair.Value.Max, pair.Value.Avg, pair.Value.Min);
            }
            if (isMo)
            {
                gCMo.DataSource = dt;
            }
            else
            {
                gCMt.DataSource = dt;
            }
        }
        /// <summary>
        /// 获取信令切换的简称
        /// </summary>
        /// <param name="longName"></param>
        /// <returns></returns>
        private string GetShortName(string longName, int i)
        {
            string name = "";
            switch (longName)
            {
                case "Invite -> Try 100":
                    name = "第" + i.ToString() + "节点";
                    break;
                case "Try 100 -> 183 PROGRESS":
                    name = "第" + i.ToString() + "节点";
                    break;
                case "183 PROGRESS -> PRACK":
                    name = "第" + i.ToString() + "节点";
                    break;
                case "PRACK -> PRACK 200 OK":
                    name = "第" + i.ToString() + "节点";
                    break;
                case "PRACK 200 OK -> UPDATE":
                    name = "第" + i.ToString() + "节点";
                    break;
                case "UPDATE -> UPDATE 200 OK":
                    name = "第" + i.ToString() + "节点";
                    break;
                case "UPDATE 200 OK -> 180 RINGING":
                    name = "第" + i.ToString() + "节点";
                    break;
                case "Paging -> Invite":
                    name = "第" + i.ToString() + "节点";
                    break;
                default:
                    break;
            }
            return name;
        }


        private void gCMo_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo hi = gVMo.CalcHitInfo((sender as Control).PointToClient(Control.MousePosition));
            if ((hi != null && hi.RowHandle >= 0) && (hi.InRow || hi.InRowCell))
            {
                this.pictureBox1.Visible = true;
                gVMo.SelectRow(hi.RowHandle);
                for (int i = 0; i < 3; i++)
                {
                    Series reason = chartControlMo.Series[i];
                    chartControlMo.Series[i].Points.Clear();
                    reason.PointOptions.ValueNumericOptions.Format = NumericFormat.Number;
                    reason.PointOptions.ValueNumericOptions.Precision = 3;
                    switch (i)
                    {
                        case 0:
                            reason.Points.Add(new SeriesPoint(this.gVMo.GetRowCellValue(this.gVMo.FocusedRowHandle, "Desc"), Convert.ToDouble(this.gVMo.GetRowCellValue(this.gVMo.FocusedRowHandle, "Max").ToString()) / 1000));
                            break;
                        case 1:
                            reason.Points.Add(new SeriesPoint(this.gVMo.GetRowCellValue(this.gVMo.FocusedRowHandle, "Desc"), Convert.ToDouble(this.gVMo.GetRowCellValue(this.gVMo.FocusedRowHandle, "Avg").ToString()) / 1000));
                            break;
                        case 2:
                            reason.Points.Add(new SeriesPoint(this.gVMo.GetRowCellValue(this.gVMo.FocusedRowHandle, "Desc"), Convert.ToDouble(this.gVMo.GetRowCellValue(this.gVMo.FocusedRowHandle, "Min").ToString()) / 1000));
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        private void gCMt_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo hi = gVMt.CalcHitInfo((sender as Control).PointToClient(Control.MousePosition));
            if ((hi != null && hi.RowHandle >= 0) && (hi.InRow || hi.InRowCell))
            {
                this.pictureBox2.Visible = true;
                gVMt.SelectRow(hi.RowHandle);
                for (int i = 0; i < 3; i++)
                {
                    Series reason = chartControlMt.Series[i];
                    chartControlMt.Series[i].Points.Clear();
                    reason.PointOptions.ValueNumericOptions.Format = NumericFormat.Number;
                    reason.PointOptions.ValueNumericOptions.Precision = 3;
                    switch (i)
                    {
                        case 0:
                            reason.Points.Add(new SeriesPoint(this.gVMt.GetRowCellValue(this.gVMt.FocusedRowHandle, "Desc"), Convert.ToDouble(this.gVMt.GetRowCellValue(this.gVMt.FocusedRowHandle, "Max").ToString()) / 1000));
                            break;
                        case 1:
                            reason.Points.Add(new SeriesPoint(this.gVMt.GetRowCellValue(this.gVMt.FocusedRowHandle, "Desc"), Convert.ToDouble(this.gVMt.GetRowCellValue(this.gVMt.FocusedRowHandle, "Avg").ToString()) / 1000));
                            break;
                        case 2:
                            reason.Points.Add(new SeriesPoint(this.gVMt.GetRowCellValue(this.gVMt.FocusedRowHandle, "Desc"), Convert.ToDouble(this.gVMt.GetRowCellValue(this.gVMt.FocusedRowHandle, "Min").ToString()) / 1000));
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        private void ExportItem_Click(object sender, EventArgs e)
        {
            if (listViewMo.Focused)
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(listViewMo);
            }
            else if (listViewMt.Focused)
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(listViewMt);
            }
        }

        private void ReplayItem_Click(object sender, EventArgs e)
        {
            if (listViewMo.Focused)
            {
                replay(listViewMo);
            }
            else
            {
                replay(listViewMt);
            }
        }
        private void replay(TreeListView listView)
        {
            object rows = listView.GetSelectedObject();
            if (rows == null)
            {
                MessageBox.Show("请选择要回放的数据");
                return;
            }
            ReasonInfo info = listView.GetSelectedObject() as ReasonInfo;
            ReasonInfoDetail detail = listView.GetSelectedObject() as ReasonInfoDetail;
            if (info == null && detail == null)
            {
                return;
            }
            MainModel.MainForm.NeedChangeWorkSpace(false);
            
            try
            {
                if (info != null)
                {
                    if (!string.IsNullOrEmpty(info.MoFileName) && !string.IsNullOrEmpty(info.MtFileName))
                    {
                        FileReplayer.ReplayOnePartBothSides(info.Inviterequest);
                    }
                    else
                    {
                        FileReplayer.ReplayOnePart(info.Inviterequest);
                    }
                }
                else
                {
                    if (detail.MoFile != null && detail.MtFile != null)
                    {
                        FileReplayer.ReplayOnePartBothSides(detail.StartMsg);
                    }
                    else
                    {
                        FileReplayer.ReplayOnePart(detail.StartMsg);
                    }
                }
            }
            catch
            {
                MainModel.MainForm.CancelChange = true;
            }
            MainModel.MainForm.ChangeWorkSpace();
        }

        private void pictureBox1_Click(object sender, EventArgs e)
        {
            updateSummary(chartControlMo, resultListMo, out dicMo);
            this.pictureBox1.Visible = false;
        }

        private void pictureBox2_Click(object sender, EventArgs e)
        {
            updateSummary(chartControlMt, resultListMt, out dicMt);
            this.pictureBox2.Visible = false;
        }

        private void ExpandItem_Click(object sender, EventArgs e)
        {
            if (listViewMo.Focused)
            {
                listViewMo.ExpandAll();
            }
            else if (listViewMt.Focused)
            {
                listViewMt.ExpandAll();
            }
        }

    }
    internal class NumCal
    {
        public List<double> NumList = new List<double>();
        public double Avg
        {
            get
            {
                double sum = 0;
                foreach (double i in NumList)
                {
                    sum += i;
                }
                if (NumList.Count > 0)
                {
                    return Math.Round(sum / NumList.Count, 2);
                }
                return 0;
            }
        }
        public double Max
        {
            get
            {
                double num = double.MinValue;
                foreach (double i in NumList)
                {
                    if (num < i)
                    {
                        num = i;
                    }
                }
                return num;
            }
        }
        public double Min
        {
            get
            {
                double num = double.MaxValue;
                foreach (double i in NumList)
                {
                    if (num > i)
                    {
                        num = i;
                    }
                }
                return num;
            }
        }
    }
}
