﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTLteScanGoodRsrpPoorSinr
{
    public partial class ResultForm : MinCloseForm
    {
        public ResultForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void FillData(List<GoodRsrpPoorSinrCell> cells)
        {
            gridControl.DataSource = cells;
            gridControl.RefreshDataSource();
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gridView.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }

            GoodRsrpPoorSinrCell cell = gridView.GetRow(info.RowHandle) as GoodRsrpPoorSinrCell;
            if (cell==null)
            {
                return;
            }
            MainModel.SelectedLTECell = cell.Cell;
            MainModel.ClearDTData();
            foreach (TestPoint tp in cell.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
            MainModel.FireDTDataChanged(this);
        }

        private void miExp2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }
    }
}
