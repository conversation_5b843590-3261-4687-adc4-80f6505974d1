﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTAtuLogKPIGroup;

namespace MasterCom.RAMS.ZTFunc
{
    class AtuLogKPIGroupQuery : QueryKpiStatByFiles
    {
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18037, this.Name);
        }
        protected GroupTemplate template = null;

        protected override void fireShowResult()
        {
            ATUFileGroupForm dlg = MainModel.GetObjectFromBlackboard(typeof(ATUFileGroupForm)) as ATUFileGroupForm;
            if (dlg==null||dlg.IsDisposed)
            {
                dlg = new ATUFileGroupForm();
            }
            dlg.Owner = MainModel.MainForm;
            dlg.FillData(template, deviceSet);
            dlg.Visible = true;
            dlg.BringToFront();

            triadIDSet = null;
            KpiDataManager = null;
            deviceSet = null;
        }

        protected override bool getConditionBeforeQuery()
        {
            TemplateSelector dlg = new TemplateSelector(template);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            isQueryAllParams = false;
            template = dlg.Template;
            KpiDataManager = new KPIDataManager();
            return true;
        }

        private string triadIDSet = null;
        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            if (triadIDSet == null)
            {
                List<string> formulaSet = new List<string>();
                
                foreach (GroupIndicatorOption item in template.Options)
                {
                    addFormulaSet(formulaSet, item);
                }
                triadIDSet = getTriadIDIgnoreServiceType(formulaSet);
            }
            return triadIDSet;
        }

        private void addFormulaSet(List<string> formulaSet, GroupIndicatorOption item)
        {
            if (item.IsMultiFormula)
            {
                if (!formulaSet.Contains(item.Formula1))
                {
                    formulaSet.Add(item.Formula1);
                }
                if (!formulaSet.Contains(item.Formula2))
                {
                    formulaSet.Add(item.Formula2);
                }
            }
            else
            {
                if (!formulaSet.Contains(item.KPIFormula))
                {
                    formulaSet.Add(item.KPIFormula);
                }
            }
        }

        List<DeviceKPIInfo> deviceSet = null;
        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            List<KPIDataGroup> dataSet = KpiDataManager.GetReportGroupDataSet();
            Dictionary<string, DevFileKPIGroup> deviceDic = new Dictionary<string, DevFileKPIGroup>();
            foreach (KPIDataGroup item in dataSet)
            {
                if (item.GroupInfo is FileInfo)
                {
                    FileInfo fi = item.GroupInfo as FileInfo;
                    string deviceName = getDeviceNameByFileName(fi.Name);
                    if (string.IsNullOrEmpty(deviceName))
                    {
                        continue;
                    }
                    DevFileKPIGroup dGrp = null;
                    if (!deviceDic.TryGetValue(deviceName, out dGrp))
                    {
                        dGrp = new DevFileKPIGroup(deviceName);
                        deviceDic.Add(deviceName, dGrp);
                    }
                    dGrp.AddFileData(item);
                }
            }

            deviceSet = new List<DeviceKPIInfo>();
            foreach (DevFileKPIGroup file in deviceDic.Values)
            {
                deviceSet.Add(file.MakeSummary(template));
            }

        }

        private string getDeviceNameByFileName(string fileName)
        {
            string devName = string.Empty;
            for (int i = fileName.Length - 1; i >= 0; i--)
            {
                char c = fileName[i];
                if (Char.IsDigit(c))
                {
                    StringBuilder sb = new StringBuilder(devName);
                    sb.Append(fileName[i]);
                    devName = sb.ToString();
                    if (devName.Length == 14)
                    {//20150521101927 可能为时间标记
                        char[] arr = devName.ToCharArray();
                        Array.Reverse(arr);
                        devName = new string(arr);
                        DateTime dt;
                        if (DateTime.TryParse(
                            string.Format("{0}-{1}-{2} {3}:{4}:{5}",
                            devName.Substring(0, 4), devName.Substring(4, 2)
                            , devName.Substring(6, 2), devName.Substring(8, 2)
                            , devName.Substring(10, 2), devName.Substring(12)), out dt))
                        {
                            devName = string.Empty;
                        }
                    }
                }
                else if (devName.Length >= 8)
                {
                    char[] arr = devName.ToCharArray();
                    Array.Reverse(arr);
                    devName = new string(arr);
                    return devName;
                }
                else
                {
                    devName = string.Empty;
                }
            }

            if (devName.Length >= 8)
            {
                char[] arr = devName.ToCharArray();
                Array.Reverse(arr);
                devName = new string(arr);
                return devName;
            }
            return fileName;
        }
    }
}
