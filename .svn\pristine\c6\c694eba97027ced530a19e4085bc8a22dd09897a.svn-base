﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using System.Drawing;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTWeakCoverRoadLayer : CustomDrawLayer, IKMLExport
    {
        public ZTWeakCoverRoadLayer(MapOperation mp, string name)
            : base(mp, name)
        {
            VisibleScaleEnabled = true;
            VisibleScale = new VisibleScale(0, 80000);
        }
        
        public List<ZTWeakCoverGrid> Peroid1WeakGrids { get; set; } = new List<ZTWeakCoverGrid>();
        
        public List<ZTWeakCoverGrid> Peroid2RepeatWeakGrids { get; set; } = new List<ZTWeakCoverGrid>();
        
        public List<ZTWeakCoverGrid> Peroid2NewWeakGrids { get; set; } = new List<ZTWeakCoverGrid>();

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            updateRect.Inflate((int)(40 * 10000 / Map.Scale), (int)(40 * 10000 / Map.Scale));
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
            drawWeakGrid(dRect, graphics);
        }
        
        public bool ShowPeriod1Grid { get; set; } = true;
        SolidBrush period1Brush = new SolidBrush(Color.FromArgb(150, Color.Orange));
        public Color Period1GridColor
        {
            get { return period1Brush.Color; }
            set { period1Brush = new SolidBrush(Color.FromArgb(150, value)); }
        }
        
        public bool ShowPeriod2RepeatGrid { get; set; } = true;
        SolidBrush period2RepeatBrush = new SolidBrush(Color.FromArgb(150, Color.Red));
        public Color Period2RepeatGridColor
        {
            get { return period2RepeatBrush.Color; }
            set { period2RepeatBrush = new SolidBrush(Color.FromArgb(150, value)); }
        }
        
        public bool ShowPeriod2NewGrid { get; set; } = true;
        SolidBrush period2NewBrush = new SolidBrush(Color.FromArgb(150, Color.Blue));
        public Color Period2NewGridColor
        {
            get { return period2NewBrush.Color; }
            set { period2NewBrush = new SolidBrush(Color.FromArgb(150, value)); }
        }

        private void drawWeakGrid(DbRect curViewRect, Graphics g)
        {
            drawGrid(curViewRect, g, ShowPeriod1Grid, Peroid1WeakGrids, period1Brush);
            drawGrid(curViewRect, g, ShowPeriod2NewGrid, Peroid2NewWeakGrids, period2NewBrush);
            drawGrid(curViewRect, g, ShowPeriod2RepeatGrid, Peroid2RepeatWeakGrids, period2RepeatBrush);
        }

        private void drawGrid(DbRect curViewRect, Graphics g, bool isShow, List<ZTWeakCoverGrid> grids, SolidBrush brush)
        {
            if (isShow)
            {
                foreach (ZTWeakCoverGrid grid in grids)
                {
                    foreach (DbRect rect in grid.Grids)
                    {
                        fillGrid(curViewRect, rect, g, brush);
                    }
                }
            }
        }

        private void fillGrid(DbRect curViewRect,DbRect gridRect, Graphics g,Brush brush)
        {
            if (gridRect.x2 > curViewRect.x1 && gridRect.x1 < curViewRect.x2 && gridRect.y2 > curViewRect.y1 && gridRect.y1 < curViewRect.y2)
            {
                DbPoint ltPoint = new DbPoint(gridRect.x1, gridRect.y2);
                PointF pointLt;
                this.Map.ToDisplay(ltPoint, out pointLt);
                DbPoint brPoint = new DbPoint(gridRect.x2, gridRect.y1);
                PointF pointBr;
                this.Map.ToDisplay(brPoint, out pointBr);
                g.FillRectangle(brush, pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
            }
        }

        #region IKMLExport Members

        public void ExportKml(KMLExporter exporter, System.Xml.XmlElement parentElem)
        {
            throw new NotImplementedException();
        }

        #endregion

        #region IKMLExport Members

        void IKMLExport.ExportKml(KMLExporter exporter, System.Xml.XmlElement parentElem)
        {
            throw new NotImplementedException();
        }

        #endregion
    }
}
