﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTWirelessNetTest
{
    public class WirelessNetTestHelper
    {
        private static WirelessNetTestHelper instance = null;
        public static WirelessNetTestHelper Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new WirelessNetTestHelper();
                }
                return instance;
            }
        }

        public static string BasicPath { get; } = $@"{Application.StartupPath}\config\WirelessNetTest";

        /// <summary>
        /// 测试数据和测试级别进行对应
        /// 例 : 
        /// 城区数据用于地市级和场景级的统计
        /// </summary>
        public Dictionary<WirelessNetTestProjectType, List<WirelessNetTestStatType>> StatTypeDic { get; }
          = new Dictionary<WirelessNetTestProjectType, List<WirelessNetTestStatType>>()
        {
            { WirelessNetTestProjectType.城区
              , new List<WirelessNetTestStatType>()
                { WirelessNetTestStatType.地市级, WirelessNetTestStatType.场景级 }
            },
            { WirelessNetTestProjectType.区县
              , new List<WirelessNetTestStatType>()
                { WirelessNetTestStatType.区县级, WirelessNetTestStatType.场景级 }
            },
            { WirelessNetTestProjectType.高速
              , new List<WirelessNetTestStatType>()
                { WirelessNetTestStatType.场景级, WirelessNetTestStatType.子场景级 }
            },
            { WirelessNetTestProjectType.高铁
              , new List<WirelessNetTestStatType>()
                { WirelessNetTestStatType.场景级, WirelessNetTestStatType.子场景级 }
            },
            { WirelessNetTestProjectType.地铁
              , new List<WirelessNetTestStatType>()
                { WirelessNetTestStatType.场景级, WirelessNetTestStatType.子场景级 }
            },
            { WirelessNetTestProjectType.机场
              , new List<WirelessNetTestStatType>()
                { WirelessNetTestStatType.场景级, WirelessNetTestStatType.子场景级 }
            },
        };

        public T GetValidEnum<T>(string str, T defaultData)
        {
            try
            {
                T res = (T)Enum.Parse(typeof(T), str);
                return res;
            }
            catch
            {
                return defaultData;
            }
        }

        public CarrierStatType GetCarrierStatType(int carrier)
        {
            if (carrier == 1)
            {
                return CarrierStatType.移动;
            }
            return CarrierStatType.电信;
        }
    }
}
