﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTSQLReportEventDeviceQuery_BJ : DIYSQLBase
    {
        public ZTSQLReportEventDeviceQuery_BJ(MainModel mainModel)
            : base(mainModel)
        {
        }

        private readonly List<string> deviceList = new List<string>();

        public List<string> GetReportEventDeviceList()
        {
            return deviceList;
        }

        protected override string getSqlTextString()
        {
            return "select deviceName, deviceDesc from tb_beijing_report_event_device order by deviceName";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int index = 0;
            E_VType[] rType = new E_VType[2];
            rType[index++] = E_VType.E_String;
            rType[index] = E_VType.E_String;

            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    string deviceName = "";
                    string deviceDesc = "";
                    deviceName = package.Content.GetParamString();
                    deviceDesc = package.Content.GetParamString();
                    deviceList.Add(deviceName + "|" + deviceDesc);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        public override string Name
        {
            get { return "ZTSQLReportEventDeviceQuery_BJ"; }
        }
    }
}
