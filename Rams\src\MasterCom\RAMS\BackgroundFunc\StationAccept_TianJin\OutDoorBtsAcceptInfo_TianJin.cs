﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public abstract class OutDoorAcceptInfo_TianJin
    {
        protected List<string> hasGotKpiKeyList = new List<string>();//已经获取到的指标Key集合

        protected KpiFile_TJ curKpiFile = KpiFile_TJ.None;

        public virtual void AddAcceptKpiInfo(Dictionary<uint, object> kpiDic)
        {
            curKpiFile = KpiFile_TJ.None;

            statsKpiNewestValues(kpiDic);

            dealWithData();
        }

        protected virtual void dealWithData()
        {

        }

        /// <summary>
        /// 统计各指标的最新一个指标值
        /// </summary>
        /// <param name="kpiDic"></param>
        protected virtual void statsKpiNewestValues(Dictionary<uint, object> kpiDic)
        {
            foreach (uint key in kpiDic.Keys)
            {
                KpiKey_TJ kpiKey = (KpiKey_TJ)key;

                if (curKpiFile == KpiFile_TJ.None && KpiFileKey.GetInstance().KeyFileCorrespond.ContainsKey(kpiKey))
                {
                    curKpiFile = KpiFileKey.GetInstance().KeyFileCorrespond[kpiKey];
                }

                if (!hasGotKpiKeyList.Contains(kpiKey.ToString()))
                {
                    object objValue = kpiDic[key];
                    hasGotKpiKeyList.Add(kpiKey.ToString());

                    statsKpiNewestValue(kpiKey, objValue);
                }
            }
        }

        protected virtual void statsKpiNewestValue(KpiKey_TJ kpiKey, object objValue)
        {

        }

        protected string getNotAccordKpiInfo(KpiInfo_TianJin kpi, ref bool isAllKpiAccord)
        {
            return getNotAccordKpiInfo(kpi.IsAccord, kpi.Error, ref isAllKpiAccord);
        }

        protected string getNotAccordKpiInfo(bool isKpiAccord, string strKpiName, ref bool isAllKpiAccord)
        {
            if (!isKpiAccord)
            {
                isAllKpiAccord = false;
                return strKpiName;
            }
            return string.Empty;
        }
    }

    public class OutDoorBtsAcceptInfo_TianJin : OutDoorAcceptInfo_TianJin
    {
        public OutDoorBtsAcceptInfo_TianJin(LTEBTS bts)
        {
            Bts = bts;
            AccpetTimePeriod = new TimePeriod();
            CellsAcceptDic = new Dictionary<int, OutDoorCellAcceptInfo_TianJin>();
        }

        public int SN { get; set; }
        public string Time
        {
            get
            {
                AccpetTimePeriod.showDayFormat = true;
                return AccpetTimePeriod.ToString();
            }
        }
        
        public TimePeriod AccpetTimePeriod { get; set; }

        public BtsFusionInfoBase FusionInfo { get; set; }
        public LTEBTS Bts { get; set; }
        public string BtsName { get { return Bts.Name.Trim(); } }
        public int BtsId { get { return Bts.BTSID; } }
        public double Longitude { get { return Bts.Longitude; } }
        public double Latitude { get { return Bts.Latitude; } }
        public string NotAccordKpiDes { get; set; }
        public string IsAccordAcceptStr { get { return IsAccordAccept ? "是" : "否"; } }
        public bool IsAccordAccept { get; set; }

        //各小区验收信息key:cellid
        public Dictionary<int, OutDoorCellAcceptInfo_TianJin> CellsAcceptDic { get; set; }

        public BtsAcceptFileInfo_TianJin BtsAcceptRes { get; set; }

        /// <summary>
        /// 检验基站和各小区指标是否合格,获取完基站和各小区的指标信息后调用
        /// </summary>
        public virtual void CheckBtsIsAccordAccept()
        {
            StringBuilder strbNotAccordDes = new StringBuilder();
            bool allCellAccord = true;
            foreach (LTECell cell in Bts.Cells)
            {
                OutDoorCellAcceptInfo_TianJin cellInfo;
                if (this.CellsAcceptDic.TryGetValue(cell.CellID, out cellInfo))
                {
                    cellInfo.CheckCellIsAccordAccept();
                    if (!cellInfo.IsAccord)
                    {
                        allCellAccord = false;
                        strbNotAccordDes.AppendLine(cell.Name + "小区不达标的指标：" + cellInfo.NotAccordKpiDes);
                    }
                }
                else
                {
                    allCellAccord = false;
                    strbNotAccordDes.AppendLine(cell.Name + "小区未查询到指标信息;");
                }
            }

            CheckCellIsAccordAccept(ref allCellAccord);
            NotAccordKpiDes += strbNotAccordDes.ToString();
            IsAccordAccept = allCellAccord;
        }

        public void CheckCellIsAccordAccept(ref bool isAllKpiAccord)
        {
            DTHandoverInfo.CheckIsAccordData();
            InterOperationKpiInfo.CheckIsAccordData();
            
            StringBuilder strbNotAccordKpiName = new StringBuilder();
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.DTHandoverInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.InterOperationKpiInfo, ref isAllKpiAccord));
            if (strbNotAccordKpiName.Length > 0)
            {
                this.NotAccordKpiDes += "基站其他不达标的指标：" + strbNotAccordKpiName.ToString().TrimEnd(',') + ";";
            }
        }

        public HandoverBTSKpiInfo_TianJin DTHandoverInfo { get; private set; } = new HandoverBTSKpiInfo_TianJin("DT切换");

        public InterOperationKpiInfo_TianJin InterOperationKpiInfo { get; private set; } = new InterOperationKpiInfo_TianJin("互操作");

        protected override void dealWithData()
        {
            DTHandoverInfo.DealWithData();
            InterOperationKpiInfo.DealWithData();
        }

        protected override void statsKpiNewestValue(KpiKey_TJ kpiKey, object objValue)
        {
            getBTSHandoverData(kpiKey, objValue);
            getInterOperationKpiData(kpiKey, objValue);
        }

        protected virtual void getBTSHandoverData(KpiKey_TJ kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case KpiKey_TJ.DTRsrpPic:
                    this.DTHandoverInfo.CoverPicPath_Rsrp.StrValue = objValue.ToString();
                    break;
                case KpiKey_TJ.DTSinrPic:
                    this.DTHandoverInfo.CoverPicPath_Sinr.StrValue = objValue.ToString();
                    break;
                case KpiKey_TJ.DTHandOverPic:
                    this.DTHandoverInfo.CoverPicPath_Pci.StrValue = objValue.ToString();
                    break;
            }
        }

        protected void getInterOperationKpiData(KpiKey_TJ kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case KpiKey_TJ.InterOperationNum:
                    InterOperationKpiInfo.Mo4GMt3G_Num.StrValue = objValue.ToString();
                    break;
                case KpiKey_TJ.InterOperationCsfbRate:
                    InterOperationKpiInfo.Mo4GMt3G_CsfbRate.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.InterOperationCsfbDelay:
                    InterOperationKpiInfo.Mo4GMt3G_CsfbDelay.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.InterOperationFRDelay:
                    InterOperationKpiInfo.Mo4GMt3G_FRDelay.StrValue = objValue.ToString();
                    break;
            }
        }
    }

    public class OutDoorCellAcceptInfo_TianJin : OutDoorAcceptInfo_TianJin
    {
        public OutDoorCellAcceptInfo_TianJin(LTECell cell)
        {
            this.Cell = cell;
        }

        protected bool isAccord = false;
        public bool IsAccord { get { return isAccord; } }
        public string IsAccordDes { get { return isAccord ? "是" : "否"; } }
        public string NotAccordKpiDes { get; set; }
        public LTECell Cell { get; set; }

        public int CellId
        {
            get
            {
                if (Cell != null)
                {
                    return Cell.CellID;
                }
                return 0;
            }
        }

        public string CellName
        {
            get
            {
                if (Cell != null)
                {
                    return Cell.Name;
                }
                return "";
            }
        }

        public DLKpiInfo_TianJin DLThroughputInfo { get; private set; } = new DLKpiInfo_TianJin("下载");

        public ULKpiInfo_TianJin ULThroughputInfo { get; private set; } = new ULKpiInfo_TianJin("上传");

        public PingKpiInfo_TianJin PingInfo { get; private set; } = new PingKpiInfo_TianJin("Ping");

        public CsfbKpiInfo_TianJin CsfbInfo { get; private set; } = new CsfbKpiInfo_TianJin("Csfb");

        public HandoverKpiInfo_TianJin DTHandoverInfo { get; private set; } = new HandoverKpiInfo_TianJin("DT切换");

        protected override void dealWithData()
        {
            switch (curKpiFile)
            {
                case KpiFile_TJ.DL:
                    DLThroughputInfo.DealWithData();
                    break;
                case KpiFile_TJ.UL:
                    ULThroughputInfo.DealWithData();
                    break;
                case KpiFile_TJ.PING:
                    PingInfo.DealWithData();
                    break;
                case KpiFile_TJ.CSFB:
                    CsfbInfo.DealWithData();
                    break;
                case KpiFile_TJ.Handover:
                    DTHandoverInfo.DealWithData();
                    break;
            }
        }

        protected override void statsKpiNewestValue(KpiKey_TJ kpiKey, object objValue)
        {
            switch (curKpiFile)
            {
                case KpiFile_TJ.DL:
                    getDLData(kpiKey, objValue);
                    break;
                case KpiFile_TJ.UL:
                    getULData(kpiKey, objValue);
                    break;
                case KpiFile_TJ.PING:
                    getPingData(kpiKey, objValue);
                    break;
                case KpiFile_TJ.CSFB:
                    getCsfbData(kpiKey, objValue);
                    break;
                case KpiFile_TJ.Handover:
                    getCoverData(kpiKey, objValue);
                    break;
            }
        }

        protected void getPingData(KpiKey_TJ kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case KpiKey_TJ.PingRate:
                    PingInfo.PingRate.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.PingDelay:
                    PingInfo.PingDelay.DValue = (double)objValue;
                    break;
            }
        }

        protected void getCsfbData(KpiKey_TJ kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case KpiKey_TJ.CsfbRate:
                    CsfbInfo.CsfbRate.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.CsfbDelay:
                    CsfbInfo.CsfbDelay.DValue = (double)objValue;
                    CsfbInfo.CsfbDelayOther.DValue = (double)objValue;
                    break;
            }
        }

        protected void getULData(KpiKey_TJ kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case KpiKey_TJ.ULMaxThroughput:
                    ULThroughputInfo.ULMaxThroughput.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.ULAvgThroughput:
                    ULThroughputInfo.ULAvgThroughput.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.ULEdgeThroughput:
                    ULThroughputInfo.EdgeMacUL.DValue = (double)objValue;
                    break;
            }
        }

        protected void getDLData(KpiKey_TJ kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case KpiKey_TJ.DLAvgRsrp:
                    this.DLThroughputInfo.AvgRSRP.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DLAvgSinr:
                    this.DLThroughputInfo.AvgSINR.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DLMaxRsrp:
                    this.DLThroughputInfo.MaxRSRP.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DLMaxSinr:
                    this.DLThroughputInfo.MaxSINR.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DLMaxThroughput:
                    this.DLThroughputInfo.DLMaxThroughput.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DLAvgThroughput:
                    this.DLThroughputInfo.DLAvgThroughput.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DLEdgeRsrp:
                    this.DLThroughputInfo.EdgeRsrp.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DLEdgeSinr:
                    this.DLThroughputInfo.EdgeSinr.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DLEdgeThroughput:
                    this.DLThroughputInfo.EdgeMacDL.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DLPci:
                    this.DLThroughputInfo.Pci.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DLEarfcn:
                    this.DLThroughputInfo.Earfcn.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DLCover:
                    this.DLThroughputInfo.Cover.BValue = (bool)objValue;
                    break;
            }
        }

        protected void getCoverData(KpiKey_TJ kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case KpiKey_TJ.DTPci:
                    this.DTHandoverInfo.Pci.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DTEarfcn:
                    this.DTHandoverInfo.Earfcn.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DTAvgRsrp:
                    this.DTHandoverInfo.AvgRSRP.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DTAvgSinr:
                    this.DTHandoverInfo.AvgSINR.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DTMaxRsrp:
                    this.DTHandoverInfo.MaxRSRP.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DTMaxSinr:
                    this.DTHandoverInfo.MaxSINR.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DTEdgeRsrp:
                    this.DTHandoverInfo.EdgeRsrp.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DTEdgeSinr:
                    this.DTHandoverInfo.EdgeSinr.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DTRsrpPic:
                    this.DTHandoverInfo.CoverPicPath_Rsrp.StrValue = objValue.ToString();
                    break;
                case KpiKey_TJ.DTSinrPic:
                    this.DTHandoverInfo.CoverPicPath_Sinr.StrValue = objValue.ToString();
                    break;
                case KpiKey_TJ.DTHandOver:
                    this.DTHandoverInfo.Handover.BValue = (bool)objValue;
                    break;
                case KpiKey_TJ.DTCover:
                    this.DTHandoverInfo.Cover.BValue = (bool)objValue;
                    break;
                case KpiKey_TJ.DTOverLapRate:
                    this.DTHandoverInfo.OverlapRate.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DTRRCRate:
                    this.DTHandoverInfo.ConnectRate.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DTHandOverRate:
                    this.DTHandoverInfo.HandoverRate.DValue = (double)objValue;
                    break;
                case KpiKey_TJ.DTAntennaOpposite:
                    this.DTHandoverInfo.AntennaOpposite.BValue = (bool)objValue;
                    break;
            }
        }

        /// <summary>
        /// 检验小区指标是否合格,获取完小区各指标信息后调用
        /// </summary>
        /// <returns></returns>
        public void CheckCellIsAccordAccept()
        {
            DLThroughputInfo.CheckIsAccordData(Cell);
            ULThroughputInfo.CheckIsAccordData();
            PingInfo.CheckIsAccordData();
            CsfbInfo.CheckIsAccordData();
            DTHandoverInfo.CheckIsAccordData();

            bool isAllKpiAccord = true;
            StringBuilder strbNotAccordKpiName = new StringBuilder();
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.DLThroughputInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.ULThroughputInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.PingInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.CsfbInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.DTHandoverInfo, ref isAllKpiAccord));
            this.isAccord = isAllKpiAccord;
            this.NotAccordKpiDes = strbNotAccordKpiName.ToString().TrimEnd(',');
        }
    }

    public class KpiInfo_TianJin
    {
        public KpiInfo_TianJin(string strKpiName)
        {
            KpiName = strKpiName;
        }

        public string KpiName { get; set; }

        public virtual void DealWithData()
        {

        }

        public string Error { get; set; } = "";
        public bool IsAccord { get; protected set; }

        public virtual void CheckIsAccordData()
        {

        }
        
        public class KpiInfo
        {
            public double DValue { get; set; } = double.NaN;
            public string StrValue { get; set; }
            public bool BValue { get; set; }

            public string ValidRes { get; set; }
            public bool IsAccord { get; set; }
            public string IsAccordDes { get; set; }

            protected string invalidStr = "";

            public string NotAccordInfo { get; set; } = "";

            public void SetValidBoolDes()
            {
                if (BValue)
                {
                    ValidRes = "是";
                }
                else
                {
                    ValidRes = "否";
                }
            }

            public void SetVaildString()
            {
                if (string.IsNullOrEmpty(StrValue))
                {
                    ValidRes = invalidStr;
                }
                else
                {
                    ValidRes = StrValue;
                }
            }

            public void SetVaildData()
            {
                if (double.IsNaN(DValue))
                {
                    ValidRes = invalidStr;
                }
                else
                {
                    ValidRes = DValue.ToString();
                }
            }

            public void SetValidRate()
            {
                if (double.IsNaN(DValue))
                {
                    ValidRes = invalidStr;
                }
                else
                {
                    ValidRes = DValue.ToString() + "%";
                }
            }

            public void CheckIsAccordBool(string name, ref string error, ref bool isAllAccord)
            {
                if (BValue)
                {
                    setAccord(true);
                }
                else
                {
                    setAccord(false);
                    NotAccordInfo += name + ",";
                    error += NotAccordInfo;
                    isAllAccord = false;
                }
            }

            public void CheckIsAccordString(string name, ref string error, ref bool isAllAccord)
            {
                if (!string.IsNullOrEmpty(StrValue))
                {
                    setAccord(true);
                }
                else
                {
                    setAccord(false);
                    NotAccordInfo += name + ",";
                    error += NotAccordInfo;
                    isAllAccord = false;
                }
            }

            public void CheckIsAccordData(string name, double minValidData, double maxValidData, ref string error, ref bool isAllAccord)
            {
                if (!double.IsNaN(DValue) && DValue >= minValidData && DValue <= maxValidData)
                {
                    setAccord(true);
                }
                else
                {
                    setAccord(false);
                    NotAccordInfo += name + ",";
                    error += NotAccordInfo;
                    isAllAccord = false;
                }
            }

            protected void setAccord(bool accord)
            {
                IsAccord = accord;
                if (accord)
                {
                    IsAccordDes = "是";
                }
                else
                {
                    IsAccordDes = "否";
                }
            }

        }
    }

    public class DLKpiInfo_TianJin : KpiInfo_TianJin
    {
        public DLKpiInfo_TianJin(string strKpiName)
            : base(strKpiName)
        {

        }

        public KpiInfo AvgRSRP { get; set; } = new KpiInfo();
        public KpiInfo AvgSINR { get; set; } = new KpiInfo();
        public KpiInfo MaxRSRP { get; set; } = new KpiInfo();
        public KpiInfo MaxSINR { get; set; } = new KpiInfo();
        public KpiInfo DLMaxThroughput { get; set; } = new KpiInfo();
        public KpiInfo DLAvgThroughput { get; set; } = new KpiInfo();
        public KpiInfo EdgeRsrp { get; set; } = new KpiInfo();
        public KpiInfo EdgeSinr { get; set; } = new KpiInfo();
        public KpiInfo EdgeMacDL { get; set; } = new KpiInfo();

        public KpiInfo Earfcn { get; set; } = new KpiInfo();
        public KpiInfo Pci { get; set; } = new KpiInfo();
        public KpiInfo Cover { get; set; } = new KpiInfo();

        public override void DealWithData()
        {
            AvgRSRP.SetVaildData();
            AvgSINR.SetVaildData();
            MaxRSRP.SetVaildData();
            MaxSINR.SetVaildData();

            Earfcn.SetVaildData();
            Pci.SetVaildData();
            Cover.SetValidBoolDes();

            DLMaxThroughput.SetVaildData();
            DLAvgThroughput.SetVaildData();
            EdgeRsrp.SetVaildData();
            EdgeSinr.SetVaildData();
            EdgeMacDL.SetVaildData();
        }

        public void CheckIsAccordData(LTECell cell)
        {
            string error = "";
            bool isAccord = true;
            AvgRSRP.CheckIsAccordData("平均RSRP", -95, double.MaxValue, ref error, ref isAccord);
            AvgSINR.CheckIsAccordData("平均SINR", 5, double.MaxValue, ref error, ref isAccord);
            MaxRSRP.CheckIsAccordData("最大RSRP", -90, double.MaxValue, ref error, ref isAccord);
            MaxSINR.CheckIsAccordData("最大SINR", 5, double.MaxValue, ref error, ref isAccord);

            DLMaxThroughput.CheckIsAccordData("FTP下载吞吐量（峰值）", 85, double.MaxValue, ref error, ref isAccord);
            DLAvgThroughput.CheckIsAccordData("FTP下载吞吐量（均值）", 50, double.MaxValue, ref error, ref isAccord);

            EdgeRsrp.CheckIsAccordData("边缘RSRP", -90, double.MaxValue, ref error, ref isAccord);
            EdgeSinr.CheckIsAccordData("边缘SINR", 5, double.MaxValue, ref error, ref isAccord);
            EdgeMacDL.CheckIsAccordData("边缘速率(DL)", 4, double.MaxValue, ref error, ref isAccord);

            Cover.CheckIsAccordBool("小区覆盖测试", ref error, ref isAccord);
            //Earfcn.CheckIsAccordData("EARFCN", cell.EARFCN, cell.EARFCN, ref error, ref isAccord);
            //Pci.CheckIsAccordData("PCI", cell.PCI, cell.PCI, ref error, ref isAccord);
            Pci.BValue = cell.PCI == Pci.DValue;
            Pci.SetValidBoolDes();
            Pci.CheckIsAccordBool("PCI", ref error, ref isAccord);
            Error = error;
            IsAccord = isAccord;
        }
    }

    public class ULKpiInfo_TianJin : KpiInfo_TianJin
    {
        public ULKpiInfo_TianJin(string strKpiName)
            :base(strKpiName)
        {

        }

        public KpiInfo ULMaxThroughput { get; set; } = new KpiInfo();
        public KpiInfo ULAvgThroughput { get; set; } = new KpiInfo();
        public KpiInfo EdgeMacUL { get; set; } = new KpiInfo();

        public override void DealWithData()
        {
            ULMaxThroughput.SetVaildData();
            ULAvgThroughput.SetVaildData();
            EdgeMacUL.SetVaildData();
        }

        public override void CheckIsAccordData()
        {
            string error = "";
            bool isAccord = true;
            ULMaxThroughput.CheckIsAccordData("FTP上传吞吐量（峰值）", 45, double.MaxValue, ref error, ref isAccord);
            ULAvgThroughput.CheckIsAccordData("FTP上传吞吐量（均值）", 30, double.MaxValue, ref error, ref isAccord);
            EdgeMacUL.CheckIsAccordData("边缘速率(UL)", 1, double.MaxValue, ref error, ref isAccord);
            Error = error;
            IsAccord = isAccord;
        }
    }

    public class PingKpiInfo_TianJin : KpiInfo_TianJin
    {
        public PingKpiInfo_TianJin(string strKpiName)
            : base(strKpiName)
        {

        }

        public KpiInfo PingRate { get; set; } = new KpiInfo();
        public KpiInfo PingDelay { get; set; } = new KpiInfo();

        public override void DealWithData()
        {
            PingRate.SetVaildData();
            PingDelay.SetVaildData();
        }

        public override void CheckIsAccordData()
        {
            string error = "";
            bool isAccord = true;
            PingRate.CheckIsAccordData("PING成功率", 0.98, double.MaxValue, ref error, ref isAccord);
            PingDelay.CheckIsAccordData("PING时延", 0, 30, ref error, ref isAccord);
            Error = error;
            IsAccord = isAccord;
        }
    }

    public class InterOperationKpiInfo_TianJin : KpiInfo_TianJin
    {
        public InterOperationKpiInfo_TianJin(string strKpiName)
            : base(strKpiName)
        {

        }

        public KpiInfo Mo4GMt3G_Num { get; set; } = new KpiInfo();
        public KpiInfo Mo4GMt3G_CsfbRate { get; set; } = new KpiInfo();
        public KpiInfo Mo4GMt3G_CsfbDelay { get; set; } = new KpiInfo();
        public KpiInfo Mo4GMt3G_FRDelay { get; set; } = new KpiInfo();

        public KpiInfo Mo3GMt4G_Num { get; set; } = new KpiInfo();
        public KpiInfo Mo3GMt4G_CsfbRate { get; set; } = new KpiInfo();
        public KpiInfo Mo3GMt4G_CsfbDelay { get; set; } = new KpiInfo();
        public KpiInfo Mo3GMt4G_FRDelay { get; set; } = new KpiInfo();

        public KpiInfo Mo4GMt4G_MoNum { get; set; } = new KpiInfo();
        public KpiInfo Mo4GMt4G_MoCsfbRate { get; set; } = new KpiInfo();
        public KpiInfo Mo4GMt4G_MoCsfbDelay { get; set; } = new KpiInfo();
        public KpiInfo Mo4GMt4G_MoFRDelay { get; set; } = new KpiInfo();

        public KpiInfo Mo4GMt4G_MtNum { get; set; } = new KpiInfo();
        public KpiInfo Mo4GMt4G_MtCsfbRate { get; set; } = new KpiInfo();
        public KpiInfo Mo4GMt4G_MtCsfbDelay { get; set; } = new KpiInfo();
        public KpiInfo Mo4GMt4G_MtFRDelay { get; set; } = new KpiInfo();

        public override void DealWithData()
        {
            Mo4GMt3G_Num.SetVaildString();
            Mo4GMt3G_CsfbRate.SetVaildData();
            Mo4GMt3G_CsfbDelay.SetVaildData();
            Mo4GMt3G_FRDelay.SetVaildString();

            //Mo3GMt4G_Num.SetVaildString();
            //Mo3GMt4G_CsfbRate.SetVaildData();
            //Mo3GMt4G_CsfbDelay.SetVaildData();
            //Mo3GMt4G_FRDelay.SetVaildData();

            //Mo4GMt4G_MoNum.SetVaildString();
            //Mo4GMt4G_MoCsfbRate.SetVaildData();
            //Mo4GMt4G_MoCsfbDelay.SetVaildData();
            //Mo4GMt4G_MoFRDelay.SetVaildData();

            //Mo4GMt4G_MtNum.SetVaildString();
            //Mo4GMt4G_MtCsfbRate.SetVaildData();
            //Mo4GMt4G_MtCsfbDelay.SetVaildData();
            //Mo4GMt4G_MtFRDelay.SetVaildData();
        }

        public override void CheckIsAccordData()
        {
            string error = "";
            bool isAccord = true;
            Mo4GMt3G_Num.CheckIsAccordString("主叫4G被叫3G中主叫号码", ref error, ref isAccord);
            Mo4GMt3G_CsfbRate.CheckIsAccordData("主叫4G被叫3G中CSFB成功率", 0.98, double.MaxValue, ref error, ref isAccord);
            Mo4GMt3G_CsfbDelay.CheckIsAccordData("主叫4G被叫3G中CSFB时延", 0, 6.2, ref error, ref isAccord);
            Mo4GMt3G_FRDelay.CheckIsAccordString("主叫4G被叫3G中FRB时延", ref error, ref isAccord);

            //Mo3GMt4G_Num.CheckIsAccordString("主叫3G被叫4G中被叫号码", ref error, ref isAccord);
            //Mo3GMt4G_CsfbRate.CheckIsAccordData("主叫3G被叫4G中CSFB成功率", 0.98, double.MaxValue, ref error, ref isAccord);
            //Mo3GMt4G_CsfbDelay.CheckIsAccordData("主叫3G被叫4G中CSFB时延", 0, 6.2, ref error, ref isAccord);

            //Mo4GMt4G_MoNum.CheckIsAccordString("主叫4G被叫4G中主叫号码", ref error, ref isAccord);
            //Mo4GMt4G_MoCsfbRate.CheckIsAccordData("主叫4G被叫4G中CSFB成功率", 0.98, double.MaxValue, ref error, ref isAccord);
            //Mo4GMt4G_MoCsfbDelay.CheckIsAccordData("主叫4G被叫4G中CSFB时延", 0, 6.2, ref error, ref isAccord);

            //Mo4GMt4G_MtNum.CheckIsAccordString("主叫4G被叫4G中被叫号码", ref error, ref isAccord);
            //Mo4GMt4G_MtCsfbRate.CheckIsAccordData("主叫4G被叫4G中CSFB成功率", 0.98, double.MaxValue, ref error, ref isAccord);
            //Mo4GMt4G_MtCsfbDelay.CheckIsAccordData("主叫4G被叫4G中CSFB时延", 0, 6.2, ref error, ref isAccord);
            Error = error;
            IsAccord = isAccord;
        }
    }

    public class CsfbKpiInfo_TianJin : KpiInfo_TianJin
    {
        public CsfbKpiInfo_TianJin(string strKpiName)
            : base(strKpiName)
        {

        }
        
        public KpiInfo CsfbRate { get; set; } = new KpiInfo();
        public KpiInfo CsfbDelay { get; set; } = new KpiInfo();
        public KpiInfo CsfbDelayOther { get; set; } = new KpiInfo();

        public override void DealWithData()
        {
            CsfbRate.SetVaildData();
            CsfbDelay.SetVaildData();

            if (double.IsNaN(CsfbDelayOther.DValue))
            {
                CsfbDelayOther.ValidRes = "";
            }
            else
            {
                //保留一位小数不进位
                CsfbDelayOther.DValue = (int)(CsfbDelayOther.DValue * 10) / 10.0;
                CsfbDelayOther.ValidRes = CsfbDelayOther.DValue.ToString();
            }
        }

        public override void CheckIsAccordData()
        {
            string error = "";
            bool isAccord = true;
            CsfbRate.CheckIsAccordData("CSFB成功率", 0.98, double.MaxValue, ref error, ref isAccord);
            CsfbDelay.CheckIsAccordData("CSFB时延", 0, 6.2, ref error, ref isAccord);
            CsfbDelayOther.CheckIsAccordData("CSFB时延(不进位)", 0, 6.2, ref error, ref isAccord);
            Error = error;
            IsAccord = isAccord;
        }
    }

    public class HandoverBTSKpiInfo_TianJin : KpiInfo_TianJin
    {
        public HandoverBTSKpiInfo_TianJin(string strPicName)
            : base(strPicName)
        {
        }

        public KpiInfo CoverPicPath_Rsrp { get; set; } = new KpiInfo();
        public KpiInfo CoverPicPath_Sinr { get; set; } = new KpiInfo();
        public KpiInfo CoverPicPath_Pci { get; set; } = new KpiInfo();

        public override void DealWithData()
        {
            CoverPicPath_Rsrp.SetVaildString();
            CoverPicPath_Sinr.SetVaildString();
            CoverPicPath_Pci.SetVaildString();
        }

        public override void CheckIsAccordData()
        {
            string error = "";
            bool isAccord = true;
            CoverPicPath_Rsrp.CheckIsAccordString("Rsrp基站覆盖图", ref error, ref isAccord);
            CoverPicPath_Sinr.CheckIsAccordString("Sinr基站覆盖图", ref error, ref isAccord);
            CoverPicPath_Pci.CheckIsAccordString("PCI基站覆盖图", ref error, ref isAccord);
            Error = error;
            IsAccord = isAccord;
        }
    }

    public class HandoverKpiInfo_TianJin : DLKpiInfo_TianJin
    {
        public HandoverKpiInfo_TianJin(string strPicName)
            : base(strPicName)
        {
        }

        public KpiInfo CoverPicPath_Rsrp { get; set; } = new KpiInfo();
        public KpiInfo CoverPicPath_Sinr { get; set; } = new KpiInfo();

        public KpiInfo Handover { get; set; } = new KpiInfo();
        //public KpiInfo Cover { get; set; } = new KpiInfo();

        public KpiInfo OverlapRate { get; set; } = new KpiInfo();
        public KpiInfo ConnectRate { get; set; } = new KpiInfo();
        public KpiInfo HandoverRate { get; set; } = new KpiInfo();

        public KpiInfo AntennaOpposite { get; set; } = new KpiInfo();

        public override void DealWithData()
        {
            AvgRSRP.SetVaildData();
            AvgSINR.SetVaildData();
            MaxRSRP.SetVaildData();
            MaxSINR.SetVaildData();
            EdgeRsrp.SetVaildData();
            EdgeSinr.SetVaildData();

            CoverPicPath_Rsrp.SetVaildString();
            CoverPicPath_Sinr.SetVaildString();

            Handover.SetValidBoolDes();
            //Cover.SetValidBoolDes();

            OverlapRate.SetVaildData();
            ConnectRate.SetVaildData();
            HandoverRate.SetVaildData();

            AntennaOpposite.SetValidBoolDes();
        }

        public override void CheckIsAccordData()
        {
            string error = "";
            bool isAccord = true;
            AvgRSRP.CheckIsAccordData("平均RSRP", -95, double.MaxValue, ref error, ref isAccord);
            AvgSINR.CheckIsAccordData("平均SINR", 5, double.MaxValue, ref error, ref isAccord);
            MaxRSRP.CheckIsAccordData("最大RSRP", -90, double.MaxValue, ref error, ref isAccord);
            MaxSINR.CheckIsAccordData("最大SINR", 5, double.MaxValue, ref error, ref isAccord);

            EdgeRsrp.CheckIsAccordData("边缘RSRP", double.MinValue, double.MaxValue, ref error, ref isAccord);
            EdgeSinr.CheckIsAccordData("边缘SINR", double.MinValue, double.MaxValue, ref error, ref isAccord);

            //Earfcn.CheckIsAccordData("EARFCN", -90, double.MaxValue, ref error, ref isAccord);
            //Pci.CheckIsAccordData("PCI", -90, double.MaxValue, ref error, ref isAccord);

            OverlapRate.CheckIsAccordData("重叠覆盖率", 0, 0.2, ref error, ref isAccord);
            ConnectRate.CheckIsAccordData("连接成功率", 1, 1, ref error, ref isAccord);
            HandoverRate.CheckIsAccordData("切换成功率", 1, 1, ref error, ref isAccord);
            
            CoverPicPath_Rsrp.CheckIsAccordString("Rsrp覆盖图", ref error, ref isAccord);
            CoverPicPath_Sinr.CheckIsAccordString("Sinr覆盖图", ref error, ref isAccord);

            AntennaOpposite.CheckIsAccordBool("天线接反", ref error, ref isAccord);
            Handover.CheckIsAccordBool("切换情况", ref error, ref isAccord);
            //Cover.CheckIsAccordBool("小区覆盖测试", ref error, ref isAccord);

            Error = error;
            IsAccord = isAccord;
        }
    }
}
