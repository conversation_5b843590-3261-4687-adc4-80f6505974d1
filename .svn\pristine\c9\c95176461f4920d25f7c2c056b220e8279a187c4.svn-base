﻿namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    partial class ConditionDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.numFTPMax = new DevExpress.XtraEditors.SpinEdit();
            this.numHTTPMax = new DevExpress.XtraEditors.SpinEdit();
            this.chkHttp = new System.Windows.Forms.CheckBox();
            this.numEmailMax = new DevExpress.XtraEditors.SpinEdit();
            this.chkEmail = new System.Windows.Forms.CheckBox();
            this.label7 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.chkFTP = new System.Windows.Forms.CheckBox();
            this.label4 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.numDistance = new DevExpress.XtraEditors.SpinEdit();
            this.label6 = new System.Windows.Forms.Label();
            this.numSecond = new DevExpress.XtraEditors.SpinEdit();
            this.label9 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.numSampleCnt = new DevExpress.XtraEditors.SpinEdit();
            this.label3 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.tvReason = new System.Windows.Forms.TreeView();
            this.btnDown = new DevExpress.XtraEditors.SimpleButton();
            this.btnUp = new DevExpress.XtraEditors.SimpleButton();
            this.tabCtrlDetail = new DevExpress.XtraTab.XtraTabControl();
            this.tabCover = new DevExpress.XtraTab.XtraTabPage();
            this.coverPnl1 = new MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.CoverPnl();
            this.tabQual = new DevExpress.XtraTab.XtraTabPage();
            this.qualPnl = new MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.QualPnl();
            this.tabHo = new DevExpress.XtraTab.XtraTabPage();
            this.hoUpdatePnl = new MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause.HoUpdatePnl();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHTTPMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numEmailMax.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSecond.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCnt.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabCtrlDetail)).BeginInit();
            this.tabCtrlDetail.SuspendLayout();
            this.tabCover.SuspendLayout();
            this.tabQual.SuspendLayout();
            this.tabHo.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.groupBox2);
            this.groupControl1.Controls.Add(this.groupBox1);
            this.groupControl1.Controls.Add(this.label1);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1178, 93);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "低速率设条件";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.numFTPMax);
            this.groupBox2.Controls.Add(this.numHTTPMax);
            this.groupBox2.Controls.Add(this.chkHttp);
            this.groupBox2.Controls.Add(this.numEmailMax);
            this.groupBox2.Controls.Add(this.chkEmail);
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Controls.Add(this.label2);
            this.groupBox2.Controls.Add(this.chkFTP);
            this.groupBox2.Controls.Add(this.label4);
            this.groupBox2.Location = new System.Drawing.Point(12, 27);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(678, 61);
            this.groupBox2.TabIndex = 0;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "下载速率指标";
            // 
            // numFTPMax
            // 
            this.numFTPMax.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numFTPMax.Location = new System.Drawing.Point(127, 21);
            this.numFTPMax.Name = "numFTPMax";
            this.numFTPMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPMax.Size = new System.Drawing.Size(60, 21);
            this.numFTPMax.TabIndex = 0;
            // 
            // numHTTPMax
            // 
            this.numHTTPMax.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numHTTPMax.Location = new System.Drawing.Point(355, 20);
            this.numHTTPMax.Name = "numHTTPMax";
            this.numHTTPMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numHTTPMax.Size = new System.Drawing.Size(60, 21);
            this.numHTTPMax.TabIndex = 0;
            // 
            // chkHttp
            // 
            this.chkHttp.AutoSize = true;
            this.chkHttp.Location = new System.Drawing.Point(235, 24);
            this.chkHttp.Name = "chkHttp";
            this.chkHttp.Size = new System.Drawing.Size(114, 16);
            this.chkHttp.TabIndex = 2;
            this.chkHttp.Text = "HTTP Download≤";
            this.chkHttp.UseVisualStyleBackColor = true;
            // 
            // numEmailMax
            // 
            this.numEmailMax.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numEmailMax.Location = new System.Drawing.Point(570, 22);
            this.numEmailMax.Name = "numEmailMax";
            this.numEmailMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numEmailMax.Size = new System.Drawing.Size(60, 21);
            this.numEmailMax.TabIndex = 0;
            // 
            // chkEmail
            // 
            this.chkEmail.AutoSize = true;
            this.chkEmail.Location = new System.Drawing.Point(468, 26);
            this.chkEmail.Name = "chkEmail";
            this.chkEmail.Size = new System.Drawing.Size(96, 16);
            this.chkEmail.TabIndex = 2;
            this.chkEmail.Text = "EMail SMTP≤";
            this.chkEmail.UseVisualStyleBackColor = true;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(193, 26);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(29, 12);
            this.label7.TabIndex = 0;
            this.label7.Text = "Mbps";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(421, 25);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(29, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "Mbps";
            // 
            // chkFTP
            // 
            this.chkFTP.AutoSize = true;
            this.chkFTP.Checked = true;
            this.chkFTP.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkFTP.Location = new System.Drawing.Point(13, 25);
            this.chkFTP.Name = "chkFTP";
            this.chkFTP.Size = new System.Drawing.Size(108, 16);
            this.chkFTP.TabIndex = 2;
            this.chkFTP.Text = "FTP Download≤";
            this.chkFTP.UseVisualStyleBackColor = true;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(636, 27);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(29, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "Mbps";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.numDistance);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.numSecond);
            this.groupBox1.Controls.Add(this.label9);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.numSampleCnt);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Location = new System.Drawing.Point(696, 26);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(470, 62);
            this.groupBox1.TabIndex = 1;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "持续性（与关系）";
            // 
            // numDistance
            // 
            this.numDistance.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numDistance.Location = new System.Drawing.Point(359, 25);
            this.numDistance.Name = "numDistance";
            this.numDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDistance.Size = new System.Drawing.Size(60, 21);
            this.numDistance.TabIndex = 1;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(312, 30);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(41, 12);
            this.label6.TabIndex = 0;
            this.label6.Text = "距离≥";
            // 
            // numSecond
            // 
            this.numSecond.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numSecond.Location = new System.Drawing.Point(211, 25);
            this.numSecond.Name = "numSecond";
            this.numSecond.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSecond.Size = new System.Drawing.Size(60, 21);
            this.numSecond.TabIndex = 1;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(425, 31);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(17, 12);
            this.label9.TabIndex = 0;
            this.label9.Text = "米";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(277, 29);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(17, 12);
            this.label8.TabIndex = 0;
            this.label8.Text = "秒";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(164, 30);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(41, 12);
            this.label5.TabIndex = 0;
            this.label5.Text = "时间≥";
            // 
            // numSampleCnt
            // 
            this.numSampleCnt.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numSampleCnt.Location = new System.Drawing.Point(79, 25);
            this.numSampleCnt.Name = "numSampleCnt";
            this.numSampleCnt.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSampleCnt.Size = new System.Drawing.Size(60, 21);
            this.numSampleCnt.TabIndex = 1;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(32, 30);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(41, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "点数≥";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(292, 41);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(0, 12);
            this.label1.TabIndex = 0;
            // 
            // groupControl2
            // 
            this.groupControl2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupControl2.Controls.Add(this.splitContainerControl1);
            this.groupControl2.Location = new System.Drawing.Point(0, 99);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(1178, 532);
            this.groupControl2.TabIndex = 0;
            this.groupControl2.Text = "原因设置";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(2, 23);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.tvReason);
            this.splitContainerControl1.Panel1.Controls.Add(this.btnDown);
            this.splitContainerControl1.Panel1.Controls.Add(this.btnUp);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.tabCtrlDetail);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1174, 507);
            this.splitContainerControl1.SplitterPosition = 274;
            this.splitContainerControl1.TabIndex = 0;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // tvReason
            // 
            this.tvReason.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tvReason.HideSelection = false;
            this.tvReason.Location = new System.Drawing.Point(0, 0);
            this.tvReason.Name = "tvReason";
            this.tvReason.Size = new System.Drawing.Size(226, 507);
            this.tvReason.TabIndex = 0;
            this.tvReason.AfterSelect += new System.Windows.Forms.TreeViewEventHandler(this.tvReason_AfterSelect);
            // 
            // btnDown
            // 
            this.btnDown.Anchor = System.Windows.Forms.AnchorStyles.Right;
            this.btnDown.Appearance.Font = new System.Drawing.Font("宋体", 9F);
            this.btnDown.Appearance.Options.UseFont = true;
            this.btnDown.Location = new System.Drawing.Point(233, 255);
            this.btnDown.Name = "btnDown";
            this.btnDown.Size = new System.Drawing.Size(32, 23);
            this.btnDown.TabIndex = 2;
            this.btnDown.Text = "↓";
            this.btnDown.Click += new System.EventHandler(this.btnDown_Click);
            // 
            // btnUp
            // 
            this.btnUp.Anchor = System.Windows.Forms.AnchorStyles.Right;
            this.btnUp.Appearance.Font = new System.Drawing.Font("宋体", 9F);
            this.btnUp.Appearance.Options.UseFont = true;
            this.btnUp.Location = new System.Drawing.Point(233, 226);
            this.btnUp.Name = "btnUp";
            this.btnUp.Size = new System.Drawing.Size(32, 23);
            this.btnUp.TabIndex = 1;
            this.btnUp.Text = "↑";
            this.btnUp.Click += new System.EventHandler(this.btnUp_Click);
            // 
            // tabCtrlDetail
            // 
            this.tabCtrlDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabCtrlDetail.Location = new System.Drawing.Point(0, 0);
            this.tabCtrlDetail.Name = "tabCtrlDetail";
            this.tabCtrlDetail.SelectedTabPage = this.tabQual;
            this.tabCtrlDetail.Size = new System.Drawing.Size(894, 507);
            this.tabCtrlDetail.TabIndex = 0;
            this.tabCtrlDetail.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.tabQual,
            this.tabCover,
            this.tabHo});
            // 
            // tabCover
            // 
            this.tabCover.AutoScroll = true;
            this.tabCover.Controls.Add(this.coverPnl1);
            this.tabCover.Name = "tabCover";
            this.tabCover.Size = new System.Drawing.Size(887, 477);
            this.tabCover.Text = "覆盖";
            // 
            // coverPnl1
            // 
            this.coverPnl1.AutoScroll = true;
            this.coverPnl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.coverPnl1.Location = new System.Drawing.Point(0, 0);
            this.coverPnl1.Name = "coverPnl1";
            this.coverPnl1.Size = new System.Drawing.Size(887, 477);
            this.coverPnl1.TabIndex = 0;
            // 
            // tabQual
            // 
            this.tabQual.Controls.Add(this.qualPnl);
            this.tabQual.Name = "tabQual";
            this.tabQual.Size = new System.Drawing.Size(887, 477);
            this.tabQual.Text = "质量";
            // 
            // qualPnl
            // 
            this.qualPnl.AutoScroll = true;
            this.qualPnl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.qualPnl.Location = new System.Drawing.Point(0, 0);
            this.qualPnl.Name = "qualPnl";
            this.qualPnl.Size = new System.Drawing.Size(887, 477);
            this.qualPnl.TabIndex = 0;
            // 
            // tabHo
            // 
            this.tabHo.Controls.Add(this.hoUpdatePnl);
            this.tabHo.Name = "tabHo";
            this.tabHo.Size = new System.Drawing.Size(887, 477);
            this.tabHo.Text = "切换与更新";
            // 
            // hoUpdatePnl
            // 
            this.hoUpdatePnl.AutoScroll = true;
            this.hoUpdatePnl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.hoUpdatePnl.Location = new System.Drawing.Point(0, 0);
            this.hoUpdatePnl.Name = "hoUpdatePnl";
            this.hoUpdatePnl.Size = new System.Drawing.Size(887, 477);
            this.hoUpdatePnl.TabIndex = 0;
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(1010, 645);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 1;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(1091, 645);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 2;
            this.btnCancel.Text = "取消";
            // 
            // ConditionDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1178, 680);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.groupControl1);
            this.Name = "ConditionDlg";
            this.Text = "条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHTTPMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numEmailMax.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSecond.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCnt.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabCtrlDetail)).EndInit();
            this.tabCtrlDetail.ResumeLayout(false);
            this.tabCover.ResumeLayout(false);
            this.tabQual.ResumeLayout(false);
            this.tabHo.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit numFTPMax;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.SpinEdit numSampleCnt;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private System.Windows.Forms.TreeView tvReason;
        private DevExpress.XtraTab.XtraTabControl tabCtrlDetail;
        private DevExpress.XtraTab.XtraTabPage tabCover;
        private DevExpress.XtraTab.XtraTabPage tabQual;
        private CoverPnl coverPnl1;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.CheckBox chkHttp;
        private System.Windows.Forms.CheckBox chkEmail;
        private System.Windows.Forms.CheckBox chkFTP;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraEditors.SpinEdit numHTTPMax;
        private DevExpress.XtraEditors.SpinEdit numEmailMax;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.SpinEdit numDistance;
        private System.Windows.Forms.Label label6;
        private DevExpress.XtraEditors.SpinEdit numSecond;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label8;
        private QualPnl qualPnl;
        private DevExpress.XtraTab.XtraTabPage tabHo;
        private HoUpdatePnl hoUpdatePnl;
        private DevExpress.XtraEditors.SimpleButton btnDown;
        private DevExpress.XtraEditors.SimpleButton btnUp;
    }
}