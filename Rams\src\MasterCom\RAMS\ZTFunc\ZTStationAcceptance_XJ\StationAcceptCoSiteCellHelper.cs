﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    /// <summary>
    /// 单验共站小区工参Helper
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class StationAcceptCoSiteCellHelper
    {
        List<LTECell> oldLTECellList;
        //List<NRCell> oldNRCellList;

        /// <summary>
        /// 根据当前的单验类型处理工参
        /// 共站小区仅保留当前单验类型的小区
        /// 例 : 
        /// TDD,FDD,NB小区共站
        /// 在进行FDD单验时仅保留包含FDD后缀的小区
        /// </summary>
        /// <param name="type"></param>
        public void RemoveCoSiteCells<T>(StationAcceptType type, T curCell)
            where T : ICell
        {
            var cellManager = MainModel.GetInstance().CellManager;
            switch (type)
            {
                case StationAcceptType.TDD_OutDoor:
                case StationAcceptType.TDD_Indoor:
                case StationAcceptType.TDD_Small:
                case StationAcceptType.FDD_OutDoor:
                case StationAcceptType.FDD_Indoor:
                case StationAcceptType.FDD_Small:
                case StationAcceptType.NBIOT:
                    var lteCell = curCell as LTECell;
                    dealLteCells(type, lteCell, cellManager);
                    break;

                //case StationAcceptType.NR_OutDoor:
                //case StationAcceptType.NR_Indoor:
                //case StationAcceptType.NR_Small:
                //case StationAcceptType.NR_700M:
                //    dealNrCells(type, curCell, cellManager);
                //    break;
                default:
                    return;
            }
        }

        #region LTE TDD,FDD,NB共站处理
        private void dealLteCells(StationAcceptType type, LTECell lteCell, CellManager cellManager)
        {
            //获取当前单验类型以外的其他共站小区进行剔除
            List<LTECell> otherServiceCellList;
            switch (type)
            {
                case StationAcceptType.TDD_OutDoor:
                    otherServiceCellList = getLteOtherCells(LteServiceType.TDD, LTEBTSType.Outdoor, lteCell);
                    break;
                case StationAcceptType.TDD_Indoor:
                    otherServiceCellList = getLteOtherCells(LteServiceType.TDD, LTEBTSType.Indoor, lteCell);
                    break;
                case StationAcceptType.TDD_Small:
                    otherServiceCellList = getLteOtherCells(LteServiceType.TDD, LTEBTSType.Indoor, lteCell);
                    break;

                case StationAcceptType.FDD_OutDoor:
                    otherServiceCellList = getLteOtherCells(LteServiceType.FDD, LTEBTSType.Outdoor, lteCell);
                    break;
                case StationAcceptType.FDD_Indoor:
                    otherServiceCellList = getLteOtherCells(LteServiceType.FDD, LTEBTSType.Indoor, lteCell);
                    break;
                case StationAcceptType.FDD_Small:
                    otherServiceCellList = getLteOtherCells(LteServiceType.FDD, LTEBTSType.Indoor, lteCell);
                    break;

                case StationAcceptType.NBIOT:
                    otherServiceCellList = getLteOtherCells(LteServiceType.NBIOT, null, lteCell);
                    break;
                default:
                    return;
            }
            oldLTECellList = new List<LTECell>();

            //从工参中剔除对应小区
            removeLteCoSiteCells(otherServiceCellList, cellManager);

            //记录剔除的小区以供单验结束后恢复
            foreach (var otherServiceCell in otherServiceCellList)
            {
                oldLTECellList.Add(otherServiceCell);
            }
        }
       
        private List<LTECell> getLteOtherCells(LteServiceType type, LTEBTSType? btsType, LTECell lteCell)
        {
            var cellList = new List<LTECell>();
            var bts = lteCell.BelongBTS;
            if (btsType != null && bts.Type != btsType)
            {
                return cellList;
            }

            List<LTECell> tddCells = new List<LTECell>();
            List<LTECell> fddCells = new List<LTECell>();
            List<LTECell> nbCells = new List<LTECell>();
            foreach (var cell in bts.Cells)
            {
                if (cell.Name.Contains("-FDD"))
                {
                    fddCells.Add(cell);
                }
                else if (cell.Name.Contains("-NB"))
                {
                    nbCells.Add(cell);
                }
                else
                {
                    tddCells.Add(cell);
                }
            }

            switch (type)
            {
                case LteServiceType.TDD:
                    cellList.AddRange(fddCells);
                    cellList.AddRange(nbCells);
                    break;
                case LteServiceType.FDD:
                    cellList.AddRange(tddCells);
                    cellList.AddRange(nbCells);
                    break;
                case LteServiceType.NBIOT:
                    cellList.AddRange(tddCells);
                    cellList.AddRange(fddCells);
                    break;
            }
            return cellList;
        }

        private void removeLteCoSiteCells(List<LTECell> cells, CellManager cellManager)
        {
            if (cells == null)
            {
                return;
            }

            foreach (var cell in cells)
            {
                cellManager.Remove(cell);
                //foreach (LTEAntenna ant in cell.Antennas)
                //{
                //    cellManager.Remove(ant);
                //}
            }
        }

        public void RevocerLteCoSiteCells()
        {
            var cellManager = MainModel.GetInstance().CellManager;
            foreach (LTECell lteCell in oldLTECellList)
            {
                cellManager.Add(lteCell);
                //foreach (LTEAntenna antenna in lteCell.Antennas)
                //{
                //    cellManager.Add(antenna);
                //}
            }

            oldLTECellList = null;
        }

        private enum LteServiceType
        {
            TDD,
            FDD,
            NBIOT
        }
        #endregion

        #region NR暂无共站情况
        //private void dealNrCells(StationAcceptType type, T curCell, CellManager cellManager)
        //{ 
        
        //}
        #endregion
    }
}
