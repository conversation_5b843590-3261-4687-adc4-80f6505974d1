﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    class ReasonsIndoorOutCover : ReasonBase
    {
        public ReasonsIndoorOutCover()
        {
            this.Name = "室分泄露";
        }

        public override bool IsValid(Model.TestPoint tp, params object[] resvParams)
        {
            LTECell mainCell = null;
            if (tp is ScanTestPoint_NBIOT)
            {
                mainCell = tp.GetCell_LTEScan(0);
                if (mainCell != null && mainCell.Type == LTEBTSType.Indoor)
                {
                    return true;
                }
            }
            else
            {
                mainCell = tp.GetMainLTECell_TdOrFdd();

                if (mainCell != null && mainCell.Type == LTEBTSType.Indoor)
                {
                    return true;
                }
                int? earfcn = (int?)GetEARFCN(tp);
                if (earfcn != null && LTECell.GetBandTypeByEarfcn((int)earfcn) == LTEBandType.E)//E频点的就是室内
                {
                    return true;
                }
            }
            return false;
        }
        protected object GetEARFCN(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_EARFCN"];
            }
            else if (tp is ScanTestPoint_NBIOT)
            {
                return tp["LTESCAN_TopN_EARFCN"];
            }
            return tp["lte_EARFCN"];
        }
    }
}
