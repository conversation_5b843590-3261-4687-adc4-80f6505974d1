﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class AreasInfoForm : MinCloseForm
    {
        public AreasInfoForm(MainModel mainModel)
            :base(mainModel)
        {
            InitializeComponent();
        }

        public void FillData(Dictionary<string, double> regionDic)
        {
            StringBuilder sb = new StringBuilder(txbAreasInfo.Text);
            foreach (string regionName in regionDic.Keys)
            {
                sb.Append("\r\n");
                sb.Append(regionName + "：");
                sb.Append(regionDic[regionName].ToString("f3") + " 平方千米");
            }
            txbAreasInfo.Text = sb.ToString();
        }
    }

}
