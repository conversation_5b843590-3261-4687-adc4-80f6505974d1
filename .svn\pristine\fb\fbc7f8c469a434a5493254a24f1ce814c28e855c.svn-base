﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTLteDownloadIpGroup;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTQueryLteDlIpGroup : DIYAnalyseByFileBackgroundBase
    {
        protected ZTQueryLteDlIpGroup()
            : base(MainModel.GetInstance())
        {
            this.IncludeEvent = true;
            this.IncludeMessage = true;
            this.Columns = new List<string>();
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_APP_Speed");
            Columns.Add("lte_PDCCH_DL_Grant_Count");
            Columns.Add("lte_PDSCH_PRb_Num_s");
            Columns.Add("lte_Rank_Indicator");
            Columns.Add("lte_Times_QAM64_DLCode0");
            Columns.Add("lte_Times_QPSK_DLCode0");
            Columns.Add("lte_Times_QAM16_DLCode0");
            Columns.Add("lte_Times_QAM64_DLCode1");
            Columns.Add("lte_Times_QPSK_DLCode1");
            Columns.Add("lte_Times_QAM16_DLCode1");
            Columns.Add("lte_Count_DL_Code0_HARQ_ACK");
            Columns.Add("lte_Count_DL_Code0_HARQ_NACK");
            Columns.Add("lte_Count_DL_Code1_HARQ_ACK");
            Columns.Add("lte_Count_DL_Code1_HARQ_NACK");


            Columns.Add("lte_Wideband_CQI_for_CW0");
            Columns.Add("lte_Wideband_CQI_for_CW1");
            Columns.Add("lte_Ratio_DL_Code0_HARQ_ACK");
            Columns.Add("lte_Ratio_DL_Code0_HARQ_NACK");
            Columns.Add("lte_Ratio_DL_Code1_HARQ_ACK");
            Columns.Add("lte_Ratio_DL_Code1_HARQ_NACK");
            Columns.Add("lte_MCSCode0_DL");
            Columns.Add("lte_MCSCode1_DL");
            Columns.Add("lte_PDSCH_BLER");
            
        }

        protected readonly static object lockObj = new object();
        private static ZTQueryLteDlIpGroup intance = null;
        public static ZTQueryLteDlIpGroup GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTQueryLteDlIpGroup();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "按IP统计下载信息(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22048, this.Name);
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {

            if (Condition.FileInfos.Count > 0)
            {
                dlInfoSet = new List<DownloadInfo>();
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

        protected override void fireShowForm()
        {
            DownloadIpGroupForm frm = MainModel.GetObjectFromBlackboard(typeof(DownloadIpGroupForm)) as DownloadIpGroupForm;
            if (frm==null||frm.IsDisposed)
            {
                frm = new DownloadIpGroupForm();
            }
            frm.Owner = MainModel.MainForm;
            frm.FillData(dlInfoSet);
            frm.Visible = true;
            frm.BringToFront();
            dlInfoSet = null;
        }

        protected List<DownloadInfo> dlInfoSet = null;
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                DownloadInfo curInfo = null;
                bool isDling = false;
                List<TestPoint> dlingTpSet = new List<TestPoint>();//下载过程中的采样点
                foreach (DTData data in file.DTDatas)
                {
                    dealDataInfo(file, ref curInfo, ref isDling, dlingTpSet, data);
                }
                if (curInfo != null && curInfo.DownloadResultEvents.Count > 0 && !dlInfoSet.Contains(curInfo))
                {
                    curInfo.AddTestPoints(dlingTpSet);
                    curInfo.MakeSummary();
                    dlInfoSet.Add(curInfo);
                }
            }
        }

        private void dealDataInfo(DTFileDataManager file, ref DownloadInfo curInfo, ref bool isDling, List<TestPoint> dlingTpSet, DTData data)
        {
            if (data is MessageWithSource)
            {
                setMsgInfo(file, ref curInfo, ref isDling, dlingTpSet, data);
            }
            else if (curInfo != null)
            {
                if (data is Event)
                {
                    setEvtInfo(curInfo, ref isDling, dlingTpSet, data);
                }
                else if (isDling && data is TestPoint)
                {
                    dlingTpSet.Add(data as TestPoint);
                }
            }
        }

        private void setEvtInfo(DownloadInfo curInfo, ref bool isDling, List<TestPoint> dlingTpSet, DTData data)
        {
            Event evt = data as Event;
            if (evt.ID == 57)
            {//ftp begin
                isDling = true;
                if (curInfo.BeginTime == DateTime.MinValue)
                {
                    curInfo.BeginTime = evt.DateTime;
                }
            }
            else if ((evt.ID == 58 || evt.ID == 59)
                && curInfo.BeginTime != DateTime.MinValue)
            {//结束
                isDling = false;
                curInfo.AddTestPoints(dlingTpSet);
                dlingTpSet.Clear();
                curInfo.DownloadResultEvents.Add(evt);
            }
        }

        private void setMsgInfo(DTFileDataManager file, ref DownloadInfo curInfo, ref bool isDling, List<TestPoint> dlingTpSet, DTData data)
        {
            MessageWithSource msg = data as MessageWithSource;
            if (msg.ID == 1097533253 || msg.ID == 1097533249)//detach attach
            {
                isDling = false;
                if (curInfo != null && curInfo.DownloadResultEvents.Count > 0)
                {
                    curInfo.MakeSummary();
                    dlInfoSet.Add(curInfo);
                }
                dlingTpSet.Clear();
                curInfo = null;
            }
            else if (msg.ID == 0x416b02c1)
            {//Activate default EPS bearer context request
                isDling = false;
                if (curInfo != null && curInfo.DownloadResultEvents.Count > 0)
                {
                    curInfo.MakeSummary();
                    dlInfoSet.Add(curInfo);
                }
                dlingTpSet.Clear();
                curInfo = null;
                int hexIp = 0;
                MessageDecodeHelper.StartDissect(msg.Direction, msg.Source, msg.Length, msg.ID);
                MessageDecodeHelper.GetSingleSInt("nas_eps.esm.pdn_ipv4_int", ref hexIp);
                string strIp = hexIp.ToString("X8");
                strIp = string.Format("{0}.{1}.{2}.{3}"
                    , Convert.ToInt32(strIp.Substring(0, 2), 16)
                    , Convert.ToInt32(strIp.Substring(2, 2), 16)
                    , Convert.ToInt32(strIp.Substring(4, 2), 16)
                    , Convert.ToInt32(strIp.Substring(6, 2), 16));
                curInfo = new DownloadInfo(file.FileName, strIp);
            }
        }
    }

    public class ZTQueryLteDlIpGroup_FDD : ZTQueryLteDlIpGroup
    {
        private static ZTQueryLteDlIpGroup_FDD instance = null;
        public static new ZTQueryLteDlIpGroup_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTQueryLteDlIpGroup_FDD();
                    }
                }
            }
            return instance;
        }
        protected ZTQueryLteDlIpGroup_FDD()
            : base()
        {
            this.IncludeEvent = true;
            this.IncludeMessage = true;
            this.Columns = new List<string>();
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_SINR");
            Columns.Add("lte_fdd_APP_Speed");
            Columns.Add("lte_fdd_PDCCH_DL_Grant_Count");
            Columns.Add("lte_fdd_PDSCH_PRb_Num_s");
            Columns.Add("lte_fdd_Rank_Indicator");
            Columns.Add("lte_fdd_Times_QAM64_DLCode0");
            Columns.Add("lte_fdd_Times_QPSK_DLCode0");
            Columns.Add("lte_fdd_Times_QAM16_DLCode0");
            Columns.Add("lte_fdd_Times_QAM64_DLCode1");
            Columns.Add("lte_fdd_Times_QPSK_DLCode1");
            Columns.Add("lte_fdd_Times_QAM16_DLCode1");
            Columns.Add("lte_fdd_Count_DL_Code0_HARQ_ACK");
            Columns.Add("lte_fdd_Count_DL_Code0_HARQ_NACK");
            Columns.Add("lte_fdd_Count_DL_Code1_HARQ_ACK");
            Columns.Add("lte_fdd_Count_DL_Code1_HARQ_NACK");


            Columns.Add("lte_fdd_Wideband_CQI_for_CW0");
            Columns.Add("lte_fdd_Wideband_CQI_for_CW1");
            Columns.Add("lte_fdd_Ratio_DL_Code0_HARQ_ACK");
            Columns.Add("lte_fdd_Ratio_DL_Code0_HARQ_NACK");
            Columns.Add("lte_fdd_Ratio_DL_Code1_HARQ_ACK");
            Columns.Add("lte_fdd_Ratio_DL_Code1_HARQ_NACK");
            Columns.Add("lte_fdd_MCSCode0_DL");
            Columns.Add("lte_fdd_MCSCode1_DL");
            Columns.Add("lte_fdd_PDSCH_BLER");
        }
        public override string Name
        {
            get { return "按IP统计下载信息LTE_FDD(按文件)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26061, this.Name);
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                DownloadInfo curInfo = null;
                bool isDling = false;
                List<TestPoint> dlingTpSet = new List<TestPoint>();//下载过程中的采样点
                foreach (DTData data in file.DTDatas)
                {
                    dealDataInfo(file, ref curInfo, ref isDling, dlingTpSet, data);
                }
                if (curInfo != null && curInfo.DownloadResultEvents.Count > 0 && !dlInfoSet.Contains(curInfo))
                {
                    curInfo.AddTestPoints(dlingTpSet);
                    curInfo.MakeSummary();
                    dlInfoSet.Add(curInfo);
                }
            }
        }

        private void dealDataInfo(DTFileDataManager file, ref DownloadInfo curInfo, ref bool isDling, List<TestPoint> dlingTpSet, DTData data)
        {
            if (data is MessageWithSource)
            {
                setMsgInfo(file, ref curInfo, ref isDling, dlingTpSet, data);
            }
            else if (curInfo != null)
            {
                if (data is Event)
                {
                    setEvtInfo(curInfo, ref isDling, dlingTpSet, data);
                }
                else if (isDling && data is TestPoint)
                {
                    dlingTpSet.Add(data as TestPoint);
                }
            }
        }

        private void setEvtInfo(DownloadInfo curInfo, ref bool isDling, List<TestPoint> dlingTpSet, DTData data)
        {
            Event evt = data as Event;
            if (evt.ID == 3557)
            {//ftp begin
                isDling = true;
                if (curInfo.BeginTime == DateTime.MinValue)
                {
                    curInfo.BeginTime = evt.DateTime;
                }
            }
            else if ((evt.ID == 3558 || evt.ID == 3559)
                && curInfo.BeginTime != DateTime.MinValue)
            {//结束
                isDling = false;
                curInfo.AddTestPoints(dlingTpSet);
                dlingTpSet.Clear();
                curInfo.DownloadResultEvents.Add(evt);
            }
        }

        private void setMsgInfo(DTFileDataManager file, ref DownloadInfo curInfo, ref bool isDling, List<TestPoint> dlingTpSet, DTData data)
        {
            MessageWithSource msg = data as MessageWithSource;
            if (msg.ID == 1097533253 || msg.ID == 1097533249)//detach attach
            {
                isDling = false;
                if (curInfo != null && curInfo.DownloadResultEvents.Count > 0)
                {
                    curInfo.MakeSummary();
                    dlInfoSet.Add(curInfo);
                }
                dlingTpSet.Clear();
                curInfo = null;
            }
            else if (msg.ID == 0x416b02c1)
            {//Activate default EPS bearer context request
                isDling = false;
                if (curInfo != null && curInfo.DownloadResultEvents.Count > 0)
                {
                    curInfo.MakeSummary();
                    dlInfoSet.Add(curInfo);
                }
                dlingTpSet.Clear();
                curInfo = null;
                int hexIp = 0;
                MessageDecodeHelper.StartDissect(msg.Direction, msg.Source, msg.Length, msg.ID);
                MessageDecodeHelper.GetSingleSInt("nas_eps.esm.pdn_ipv4_int", ref hexIp);
                string strIp = hexIp.ToString("X8");
                strIp = string.Format("{0}.{1}.{2}.{3}"
                    , Convert.ToInt32(strIp.Substring(0, 2), 16)
                    , Convert.ToInt32(strIp.Substring(2, 2), 16)
                    , Convert.ToInt32(strIp.Substring(4, 2), 16)
                    , Convert.ToInt32(strIp.Substring(6, 2), 16));
                curInfo = new DownloadInfo(file.FileName, strIp);

            }
        }
    }
}
