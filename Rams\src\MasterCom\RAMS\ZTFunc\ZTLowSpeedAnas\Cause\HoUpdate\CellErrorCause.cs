﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    [Serializable]
    public class CellErrorCause : CauseBase
    {
        public override string Name
        {
            get { return "小区问题"; }
        }
        
        public int SecondBefore { get; set; } = 5;
        public int SecondAfter { get; set; } = 5;
        public override string Desc
        {
            get
            {
                return string.Format("在本小区期间速率异常，切入本小区前{0}秒(一般0-5s)速率正常，切出本小区后{1}秒（0-5s）速率恢复正常", 
                    SecondAfter, SecondBefore);
            }
        }

        public override string Suggestion
        {
            get
            {
                return null;
            }
        }

        /**
        public override void Judge(LowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP)
        {
            //1.计算所有小区各自的平均速率并筛选出满足速率异常的小区
            foreach (TestPoint testPoint in allTP)
            {
                
            }

            //2.1循环速率异常小区

            //2.2记录满足小区第一个采样点前n秒内所有采样点速率正常(前n秒内平均速率正常)
            //   ,最后一个采样点m秒后正常(后m秒内平均速率正常)


            //Dictionary<string, TestPoint> cellPointDic = new Dictionary<string, TestPoint>();
            //foreach (TestPoint pnt in segItem.TestPoints)
            //{
            //    LTECell cellLow = pnt.GetMainLTECell_TdOrFdd();
            //    if (cellLow != null && !cellPointDic.ContainsKey(cellLow.Name))
            //    {
            //        cellPointDic.Add(cellLow.Name, pnt);
            //    }
            //}
            //foreach (KeyValuePair<string, TestPoint> cellPoint in cellPointDic)
            //{

            //}

            //foreach (TestPoint pnt in segItem.TestPoints)
            //{
            //    if (!segItem.IsNeedJudge(pnt))
            //    {
            //        continue;
            //    }
            //    //查询低速率时的小区
            //    LTECell cellLow = pnt.GetMainLTECell_TdOrFdd();
            //    if (cellLow == null)
            //    {
            //        continue;
            //    }

            //    //根据小区名查询 切入该小区的采样点和切出该小区的采样点

            //    int bTime = pnt.Time - secondBefore;
            //    foreach (TestPoint testPoint in allTP)
            //    {
            //        //切入前n秒
            //        if (testPoint.Time < bTime)
            //        {
                    
            //        }

            //        //LTECell cell = testPoint.GetMainLTECell_TdOrFdd();
            //        //if (cellLow == null || cell.Name != cellLow.Name || testPoint.Time < bTime)
            //        //{
            //        //    continue;
            //        //}
            //    }
            //}
        }

        private void selectFirAndLastPoint(string cellName, List<TestPoint> allTP)
        { 
        
        }
        */

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;

                paramDic["secondBefore"] = this.SecondBefore;
                paramDic["secondAfter"] = this.SecondAfter;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.SecondBefore = (int)value["secondBefore"];
                this.SecondAfter = (int)value["secondAfter"];
            }
        }
    }
}
