﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.IO;

namespace MasterCom.RAMS.Func
{
    public partial class CellAnaResultForm : MasterCom.RAMS.Frame.ChildForm
    {
        public CellAnaResultForm()
        {
            InitializeComponent();
        }

        private String strGSM = string.Empty;
        private String strTD = string.Empty;
        private String strLTE = string.Empty;
        private StringBuilder sbGSM = null;
        private StringBuilder sbTD = null;
        private StringBuilder sbLTE = null;
        private readonly string strLoad = "小区综合分析（正在加载）";
        private readonly string strCompelet = "小区综合分析";

        public override void Init()
        {
            if (!LoadConfig())
                return;

            initWB();
            checkSelectedTp();
        }

        private void initWB()
        {
            MainModel.SelectedTestPointsChanged += selectedTestPointsChanged;
            MainModel.SelectedEventsChanged += selectedEventsChanged;
            mainModel.SelectedMessageChanged += selectedMessageChanged;
            wbGSM.IsWebBrowserContextMenuEnabled = false;
            wbTD.IsWebBrowserContextMenuEnabled = false;
            wbLTE.IsWebBrowserContextMenuEnabled = false;
        }

        private void WBNavigate(WebBrowser wb, StringBuilder sb)
        {
            try
            {
                if (wb != null)
                {
                    if (wb.Url != null && wb.Url.ToString() == sb.ToString())
                        return;
                    this.Text = strLoad;
                    wb.Navigate(sb.ToString());
                }
            }
            catch
            {
                //continue
            }
        }

        //读取URL配置文件，URL格式由网优之家提供
        private bool LoadConfig()
        {
            try
            {
                string xmlPath = Application.StartupPath + @"\config\cellAnaResultConfig.xml";
                if (!File.Exists(xmlPath))
                {
                    XmlConfigFile newConfig = new XmlConfigFile();
                    System.Xml.XmlElement cfg = newConfig.AddConfig("Configs");
                    newConfig.AddItem(cfg, "URLConfig", this.Param);
                    newConfig.Save(xmlPath);

                }

                XmlConfigFile config = new XmlConfigFile(xmlPath);
                Dictionary<string, object> dicConfig = config.GetItemValue("Configs", "URLConfig") as Dictionary<string, object>;
                strGSM = dicConfig["GSM"].ToString();
                strTD = dicConfig["TD"].ToString();
                strLTE = dicConfig["LTE"].ToString();

                return true;
            }
            catch (Exception)
            {
                MessageBox.Show("配置文件读取失败，请检查配置文件！");
                return false;
            }

        }

        public override Dictionary<string, object> Param
        {
            get
            {
                return new Dictionary<string, object>
                    { { "GSM", "http://192.168.1.131:8088/mtnoh/page/mtnoh.html?uname=5K2Pap63iyw=&pwd=Hz2R2QJJME4=&hidelogo=1&hideframe=1&app=2B208BEC-0302-4690-A4DF-1EFD2CEFDAA0&hide_bgWindow=1" },
                      { "TD", "http://192.168.1.131:8088/mtnoh/page/mtnoh.html?uname=5K2Pap63iyw=&pwd=Hz2R2QJJME4=&hidelogo=1&hideframe=1&app=F242627F-2E13-4594-9A2C-3144D8972C1B&hide_bgWindow=1" },
                      { "LTE", "http://192.168.1.131:8088/mtnoh/page/mtnoh.html?uname=5K2Pap63iyw=&pwd=Hz2R2QJJME4=&hidelogo=1&hideframe=1&app=AC6B6B31-4AEF-4F64-A614-CE9F3EC5BF42&hide_bgWindow=1" }};
            }
        }

        //获取URL
        private void getURL(TestPoint tp)
        {
            int? Lac = null;
            int? ci = null;

            if (tp != null)
            {
                Lac = tp.GetLAC();
                ci = tp.GetCI();

                //需要在回放参数中加mode才能使NetworkType有效
                if (tp.NetworkType == TestPoint.ECurrNetType.GSM)
                {
                    setUrlInfo(Lac, ci, tpGSM, ref sbGSM, strGSM, wbGSM);
                }
                else if (tp.NetworkType == TestPoint.ECurrNetType.TD)
                {
                    setUrlInfo(Lac, ci, tpTD, ref sbTD, strTD, wbTD);
                }
                else if (tp.NetworkType == TestPoint.ECurrNetType.LTE)
                {
                    setUrlInfo(Lac, ci, tpLTE, ref sbLTE, strLTE, wbLTE);
                }
            }
        }

        private void setUrlInfo(int? Lac, int? ci, TabPage page, ref StringBuilder sb, string str, WebBrowser wb)
        {
            tabCtrlMain.SelectedTab = page;
            sb = new StringBuilder(str);
            if (Lac != null && ci != null)
                sb.Append("&LAC=").Append(Lac).Append("&CI=").Append(ci);
            if (wb.Url == null || wb.Url.ToString() != sb.ToString())
                WBNavigate(wb, sb);
        }

        //检查已选择的采样点
        private void checkSelectedTp()
        {
            if (MainModel.SelectedTestPoints.Count > 0)
            {
                TestPoint tpSelected = MainModel.SelectedTestPoints[0];
                getURL(tpSelected);
            }
            else
            {
                this.Text = strCompelet;
                getURL(null);
            }
        }

        private void selectedTestPointsChanged(object sender, EventArgs e)
        {
            if (sender != this)
            {
                if (MainModel.SelectedTestPoints.Count == 0 && MainModel.SelectedEvents.Count == 0)
                {
                    //return
                }
                else if (MainModel.SelectedTestPoints.Count > 0)
                {
                    DTData data = MainModel.SelectedTestPoints[0];
                    DTDataManager dtDataManager = MainModel.DTDataManager;
                    bool isValid = getValidFileDTData(ref data, dtDataManager);
                    if(!isValid)
                    {
                        return;
                    }
                    setValidTimeDTData(data, dtDataManager);
                }
            }
        }

        private bool getValidFileDTData(ref DTData data, DTDataManager dtDataManager)
        {
            if (MainModel.IsFileReplayByCompareMode && !(IsCompareForm && dtDataManager.FileDataManagers.Count < 2))
            {
                int momtFlag = IsCompareForm ? (int)MoMtFile.MtFlag : (int)MoMtFile.MoFlag;
                DTFileDataManager fileMng = null;
                foreach (DTFileDataManager mng in dtDataManager.FileDataManagers)
                {
                    if (mng.MoMtFlag == momtFlag)
                    {
                        fileMng = mng;
                        break;
                    }
                }
                if (fileMng == null)
                {
                    return false;
                }
                if (data.FileID != fileMng.FileID)
                {
                    int time = data.Time;
                    data = fileMng.DTDatas.Find(delegate (DTData d) { return (d.Time == time) && !(d is Model.Message); });
                }
            }

            return true;
        }

        private void setValidTimeDTData(DTData data, DTDataManager dtDataManager)
        {
            if ((MainModel.IsFileReplayByMTRMode || MainModel.IsFileReplayByMTRToLogMode)
                && data.FileID != dtDataManager.FileDataManagers[IsMTRForm ? 1 : 0].FileID)
            {
                if (IsMTRForm)
                {
                    int time = data.Time;
                    data = dtDataManager.FileDataManagers[1].DTDatas.Find(
                        delegate (DTData d) { return ((int)((d.Time * 1000L + d.Millisecond) / 1000) == time) && !(d is Model.Message); });
                }
                else
                {
                    int time = (int)((data.Time * 1000L + data.Millisecond) / 1000);
                    data = dtDataManager.FileDataManagers[0].DTDatas.Find(
                        delegate (DTData d) { return (d.Time == time) && !(d is Model.Message); });
                }
            }
            if (!(data is TestPoint))
                getURL(getTestPoint(data));
            else
                getURL(data as TestPoint);
        }

        private void selectedEventsChanged(object sender, EventArgs e)
        {
            if (sender != this)
            {
                if (MainModel.SelectedTestPoints.Count == 0 && MainModel.SelectedEvents.Count == 0)
                {
                    //return
                }
                else if (MainModel.SelectedEvents.Count > 0)
                {
                    DTData data = MainModel.SelectedEvents[0];
                    DTDataManager dtDataManager = MainModel.DTDataManager;
                    bool isValid = getValidFileDTData(ref data, dtDataManager);
                    if (!isValid)
                    {
                        return;
                    }
                    setValidTimeDTData(data, dtDataManager);
                }
            }
        }

        private void selectedMessageChanged(object sender, EventArgs e)
        {
            if (sender != this && MainModel.SelectedMessage != null)
            {
                if (MainModel.SelectedTestPoints.Count == 0)
                {
                    MainModel.SelectTestPointByMessage();
                    if (MainModel.SelectedTestPoints.Count == 0) return;
                }
                TestPoint stp = MainModel.SelectedTestPoints[0];
                DTDataManager dtDataManager = MainModel.DTDataManager;
                bool isValid = getValidFileTP(ref stp, dtDataManager);
                if (!isValid)
                {
                    return;
                }
                stp = getValidTimeTP(stp, dtDataManager);
                getURL(stp);
            }
        }

        private bool getValidFileTP(ref TestPoint stp, DTDataManager dtDataManager)
        {
            if (MainModel.IsFileReplayByCompareMode && !(IsCompareForm && dtDataManager.FileDataManagers.Count < 2))
            {
                int momtFlag = IsCompareForm ? (int)MoMtFile.MtFlag : (int)MoMtFile.MoFlag;
                DTFileDataManager fileMng = null;
                foreach (DTFileDataManager mng in dtDataManager.FileDataManagers)
                {
                    if (mng.MoMtFlag == momtFlag)
                    {
                        fileMng = mng;
                        break;
                    }
                }
                if (fileMng == null)
                {
                    return false;
                }
                if (stp.FileID != fileMng.FileID)
                {
                    int time = stp.Time;
                    stp = fileMng.TestPoints.Find(delegate (TestPoint p) { return p.Time == time; });
                }
            }

            return true;
        }

        private TestPoint getValidTimeTP(TestPoint stp, DTDataManager dtDataManager)
        {
            if ((MainModel.IsFileReplayByMTRMode || MainModel.IsFileReplayByMTRToLogMode)
                && stp.FileID != dtDataManager.FileDataManagers[IsMTRForm ? 1 : 0].FileID)
            {
                if (IsMTRForm)
                {
                    int time = stp.Time;
                    stp = dtDataManager.FileDataManagers[1].TestPoints.Find(
                        delegate (TestPoint p) { return (int)((p.Time * 1000L + p.Millisecond) / 1000) == time; });
                }
                else
                {
                    int time = (int)((stp.Time * 1000L + stp.Millisecond) / 1000);
                    stp = dtDataManager.FileDataManagers[0].TestPoints.Find(
                        delegate (TestPoint p) { return p.Time == time; });
                }
            }

            return stp;
        }

        private TestPoint getTestPoint(DTData dtData)
        {
            foreach (DTFileDataManager fdm in mainModel.DTDataManager.FileDataManagers)
            {
                if (fdm.FileID == dtData.FileID)
                {
                    DTData data = null;
                    for (int idx = fdm.DTDatas.IndexOf(dtData); idx >= 0; idx--)
                    {
                        data = fdm.DTDatas[idx];
                        if (data is TestPoint)
                        {
                            return data as TestPoint;
                        }
                    }
                }
            }
            return null;
        }

        public override bool IsCompareForm { get; set; } = false;

        private void wbGSM_DocumentCompleted(object sender, WebBrowserDocumentCompletedEventArgs e)
        {
            if (this.Text == strLoad)
                this.Text = strCompelet;
        }

        private void wbTD_DocumentCompleted(object sender, WebBrowserDocumentCompletedEventArgs e)
        {
            if (this.Text == strLoad)
                this.Text = strCompelet;
        }

        private void wbLTE_DocumentCompleted(object sender, WebBrowserDocumentCompletedEventArgs e)
        {
            if (this.Text == strLoad)
                this.Text = strCompelet;
        }

        private void cmsTabCtrl_Opening(object sender, CancelEventArgs e)
        {
            smOpenCompareForm.Enabled = MainModel.IsFileReplayByCompareMode && !IsCompareForm && MainModel.DTDataManager.FileDataManagers.Count == 2;
            smOpenAllCompareForm.Enabled = smOpenCompareForm.Enabled;
        }

        private void smOpenCompareForm_Click(object sender, EventArgs e)
        {
            ChildFormConfig childFormConfig = null;
            foreach (WorkSheet ws in MainModel.WorkSpace.WorkSheets)
            {
                foreach (ChildFormConfig cfc in ws.ChildFormConfigs)
                {
                    if (cfc.ChildForm == this)
                    {
                        childFormConfig = cfc;
                    }
                }
            }
            if (childFormConfig == null)
            {
                return;
            }
            Dictionary<string, object> createParam = new Dictionary<string, object>();
            createParam.Add("AssemblyName", childFormConfig.AssemblyName);
            createParam.Add("TypeName", childFormConfig.TypeName);
            createParam.Add("Text", childFormConfig.Text);
            createParam.Add("ImageFilePath", childFormConfig.ImageFilePath);
            createParam.Add("WindowState", childFormConfig.WindowState);
            createParam.Add("SizeWidth", childFormConfig.Size.Width);
            createParam.Add("SizeHeight", childFormConfig.Size.Height);
            createParam.Add("LocationX", this.Location.X + 20);
            createParam.Add("LocationY", this.Location.Y + 20);
            createParam.Add("IsTopFront", childFormConfig.IsTopFront);
            createParam.Add("BeforeLocationX", childFormConfig.BeforeLocation.X + 20);
            createParam.Add("BeforeLocationY", childFormConfig.BeforeLocation.Y + 20);
            Dictionary<string, object> param = childFormConfig.Param;
            if (param != null)
            {
                param["IsCompareForm"] = true;
                createParam.Add("Param", param);
            }
            MainModel.MainForm.newChildForm(createParam);
        }

        private void smOpenAllCompareForm_Click(object sender, EventArgs e)
        {
            MainForm.OpenAllCompareForm(this);
        }

        //避免窗口关闭后触发事件
        private void CellAnaResultForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            MainModel.SelectedTestPointsChanged -= selectedTestPointsChanged;
            MainModel.SelectedEventsChanged -= selectedEventsChanged;
            mainModel.SelectedMessageChanged -= selectedMessageChanged;
        }

        private void tabCtrlMain_SelectedIndexChanged(object sender, EventArgs e)
        {
            switch (tabCtrlMain.SelectedIndex)
            {
                case 0:
                    if (wbGSM.Url == null)
                        WBNavigate(wbGSM, new StringBuilder(strGSM));
                    break;
                case 1:
                    if (wbTD.Url == null)
                        WBNavigate(wbTD, new StringBuilder(strTD));
                    break;
                case 2:
                    if (wbLTE.Url == null)
                        WBNavigate(wbLTE, new StringBuilder(strLTE));
                    break;
            }
        }
    }
}
