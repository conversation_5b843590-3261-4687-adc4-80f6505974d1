﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;
using DevExpress.XtraTreeList.Nodes;
using DevExpress.XtraTreeList;
using DevExpress.Utils.Drawing;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class SiteForm : BaseForm
    {
        private SiteQueryCond cond;

        private SiteDTAnaQuery query;

        private List<FileAreaStatInfo> retPartALst = new List<FileAreaStatInfo>();

        private List<FileAreaStatInfo> retPartBLst = new List<FileAreaStatInfo>();

        private List<string> dayLst = new List<string>();
        private List<string> aNoneDays = new List<string>();
        private List<string> bNoneDays = new List<string>();
        private List<string> bothNoneDays = new List<string>();
        private List<string> bothDoneDays = new List<string>();
        private Dictionary<string, Dictionary<ECarrier, List<CDTSiteFileInfo>>> timeFileDic = new Dictionary<string, Dictionary<ECarrier, List<CDTSiteFileInfo>>>();

        public SiteForm(SiteQueryCond cond)
        {
            InitializeComponent();
            this.areaListPnl.BackColor = Color.FromArgb(224, 234, 243);
            this.monthControl.SetLableEventHandle(lableClickEventHandle);
            this.cond = cond;
            init();
        }

        private void init()
        {
            this.dateTimePickerStart.Value = cond.StartTime;
            this.dateTimePickerEnd.Value = cond.EndTime;

            this.checkEditAgo.Checked = cond.BTimeAgo;
            this.spinEditAgo.Value = cond.ITimeAgo;

            this.radioGroupPartA.Properties.Items.Clear();
            this.radioGroupPartB.Properties.Items.Clear();
            foreach (Enum e in Enum.GetValues(typeof(ECarrier)))
            {
                this.radioGroupPartA.Properties.Items.Add(new DevExpress.XtraEditors.Controls.RadioGroupItem(e, e.ToString()));
                this.radioGroupPartB.Properties.Items.Add(new DevExpress.XtraEditors.Controls.RadioGroupItem(e, e.ToString()));
            }
            this.radioGroupPartA.SelectedIndex = (int)cond.CarrierPartA - 1;
            this.radioGroupPartB.SelectedIndex = (int)cond.CarrierPartB - 1;

            areaListPnl.FillData(ZTAreaManager.Instance);
            checkEditAgo_CheckedChanged(null, null);
        }

        private void checkEditAgo_CheckedChanged(object sender, EventArgs e)
        {
            bool enable = this.checkEditAgo.Checked;

            this.spinEditAgo.Enabled = enable;
            this.dateTimePickerStart.Enabled = !enable;
            this.dateTimePickerEnd.Enabled = !enable;
        }

        private void clearData()
        {
            retPartALst.Clear();
            retPartBLst.Clear();
            timeFileDic.Clear();
            listViewResult.Items.Clear();
            listViewResultCompete.Items.Clear();

            gridControlFile.DataSource = null;
        }

        private void simpleBtnSearch_Click(object sender, EventArgs e)
        {
            saveCond();
            clearData();
            xtraTabControlResult.SelectedTabPage = xtraTabPageTime;
            this.cond.RootLeafDic = areaListPnl.GetSelAreaDic();
            this.cond.GetSelAreaIDs();
            if (cond.SiteAreaIDDic.Count == 0)
            {
                MessageBox.Show("尚未选择区域...", "提示");
                return;
            }
            WaitBox.Show("开始查询数据...", searchData);
            refreshData();
        }

        private void searchData()
        {
            try
            {
                WaitBox.Text = "查询我方数据...";
                query = new SiteDTAnaQuery(MainModel.GetInstance());
                query.SiteCond = this.cond;
                query.SetQueryCondition(this.cond.QueryCond);
                query.DistrictId = this.cond.QueryCond.DistrictID;
                //我方
                this.cond.SetCarrier(new List<int>(new int[] { (int)this.cond.CarrierPartA }));
                query.Query();
                this.retPartALst = query.ResultStatList;
                //对方
                WaitBox.Text = "查询对方数据...";
                this.cond.SetCarrier(new List<int>(new int[] { (int)this.cond.CarrierPartB }));
                query.Query();
                this.retPartBLst = query.ResultStatList;
            }
            catch
            {
            	//continue
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void saveCond()
        {
            this.cond.StartTime = dateTimePickerStart.Value;
            this.cond.EndTime = dateTimePickerEnd.Value;
            this.cond.BTimeAgo = checkEditAgo.Checked;
            this.cond.ITimeAgo = (int)spinEditAgo.Value;
            this.cond.CarrierPartA = (ECarrier)radioGroupPartA.SelectedIndex + 1;
            this.cond.CarrierPartB = (ECarrier)radioGroupPartB.SelectedIndex + 1;

            dayLst.Clear();
            List<DateTime> dates = this.cond.GetTimePeriod().GetAllDates();
            foreach (DateTime dt in dates)
            {
                dayLst.Add(dt.ToShortDateString());
            }
        }

        private void refreshData()
        {
            List<string> aDoneDays = new List<string>();
            List<string> bDoneDays = new List<string>();
            Dictionary<string, FileAreaStatInfo> namePartADic = new Dictionary<string, FileAreaStatInfo>();
            Dictionary<string, FileAreaStatInfo> namePartBDic = new Dictionary<string, FileAreaStatInfo>();            
            addPartFileDic(aDoneDays, namePartADic, retPartALst, cond.CarrierPartA);
            addPartFileDic(bDoneDays, namePartBDic, retPartBLst, cond.CarrierPartB);

            foreach (string name in cond.NameLeafDic.Keys)
            {
                if (!namePartADic.ContainsKey(name))
                {
                    addDTLv(cond.NameLeafDic[name]);
                }
                else
                {
                    addNotDTLv(cond.NameLeafDic[name], namePartBDic.ContainsKey(name));
                }
            }
            refreshMonth(aDoneDays, bDoneDays);
        }

        private void addPartFileDic(List<string> doneDays, Dictionary<string, FileAreaStatInfo> namePartDic, 
            List<FileAreaStatInfo> retPartLst, ECarrier carrierPart)
        {
            foreach (FileAreaStatInfo info in retPartLst)
            {
                namePartDic[string.Format("{0}_{1}", info.areaType, info.areaId)] = info;

                DateTime dt = JavaDate.GetDateTimeFromMilliseconds(info.fileHeader.BeginTime * 1000L);
                string strDt = dt.ToShortDateString();
                if (!doneDays.Contains(strDt))
                {
                    doneDays.Add(strDt);
                }
                List<CDTSiteFileInfo> files;
                Dictionary<ECarrier, List<CDTSiteFileInfo>> partFileDic;
                if (!timeFileDic.TryGetValue(strDt, out partFileDic))
                {
                    partFileDic = new Dictionary<ECarrier, List<CDTSiteFileInfo>>();
                    timeFileDic[strDt] = partFileDic;
                }
                if (!partFileDic.TryGetValue(carrierPart, out files))
                {
                    files = new List<CDTSiteFileInfo>();
                    partFileDic[carrierPart] = files;
                }
                CDTSiteFileInfo file = new CDTSiteFileInfo(info);
                files.Add(file);
            }
        }

        private void refreshMonth(List<string> aDoneDays, List<string> bDoneDays)
        {
            aNoneDays.Clear();
            bNoneDays.Clear();
            bothNoneDays.Clear();
            bothDoneDays.Clear();

            foreach (string day in dayLst)
            {
                bool aContail = aDoneDays.Contains(day);
                bool bContain = bDoneDays.Contains(day);

                if (aContail && bContain)
                {
                    bothDoneDays.Add(day);
                }
                else if (aContail)
                {
                    bNoneDays.Add(day);
                }
                else if (bContain)
                {
                    aNoneDays.Add(day);
                }
                else
                {
                    bothNoneDays.Add(day);
                }
            }
            this.textBoxBothNone.Text = Convert.ToString(bothNoneDays.Count);
            this.textBoxANone.Text = Convert.ToString(aNoneDays.Count);
            this.textBoxBNone.Text = Convert.ToString(bNoneDays.Count);
            this.textBoxBothDone.Text = Convert.ToString(bothDoneDays.Count);

            monthControl.FillData(aNoneDays, bNoneDays, bothNoneDays, bothDoneDays);
            monthControl.RefreshData();
        }

        private void addDTLv(AreaBase leaf)
        {
            ListViewItem lvi = new ListViewItem();
            lvi.Tag = leaf;
            lvi.Text = leaf.Name;

            listViewResult.Items.Add(lvi);
        }

        private void addNotDTLv(AreaBase leaf, bool bBDT)
        {
            ListViewItem lvi = new ListViewItem();
            lvi.Tag = leaf;
            lvi.Text = leaf.Name;
            lvi.SubItems.Add(bBDT ? "已测" : "未测");

            listViewResultCompete.Items.Add(lvi);
        }

        private int pBothNone = -1;
        private void labelBothNone_Click(object sender, EventArgs e)
        {
            if (bothNoneDays.Count > 0)
            {
                moveNext(ref pBothNone, bothNoneDays.Count);
                monthControl.GoToday(bothNoneDays[pBothNone]);
            }
        }

        private int pANone = -1;
        private void labelANone_Click(object sender, EventArgs e)
        {
            if (aNoneDays.Count > 0)
            {
                moveNext(ref pANone, aNoneDays.Count);
                monthControl.GoToday(aNoneDays[pANone]);
            }
        }

        private int pBNone = -1;
        private void labelBNone_Click(object sender, EventArgs e)
        {
            if (bNoneDays.Count > 0)
            {
                moveNext(ref pBNone, bNoneDays.Count);
                monthControl.GoToday(bNoneDays[pBNone]);
            }
        }

        private int pBothDone = -1;
        private void labelBothDone_Click(object sender, EventArgs e)
        {
            if (bothDoneDays.Count > 0)
            {
                moveNext(ref pBothDone, bothDoneDays.Count);
                monthControl.GoToday(bothDoneDays[pBothDone]);
            }
        }

        private void moveNext(ref int pi, int maxCnt)
        {
            pi++;
            if (pi >= maxCnt)
            {
                pi = 0;
            }
        }

        private void lableClickEventHandle(object sender, EventArgs e)
        {
            textBoxAllCnt.Text = "0";
            textBoxACnt.Text = "0";
            textBoxBCnt.Text = "0";
            gridControlFile.DataSource = null;
            xtraTabControlAreaFile.SelectedTabPage = xtraTabPageFile;

            Label lb = sender as Label;
            if (lb == null) return;

            string strDate = lb.Tag as string;
            if (strDate == null) return;

            Dictionary<ECarrier, List<CDTSiteFileInfo>> partFileDic;
            if (timeFileDic.TryGetValue(strDate, out partFileDic))
            {
                List<CDTSiteFileInfo> files = new List<CDTSiteFileInfo>();
                if (partFileDic.ContainsKey(this.cond.CarrierPartA))
                {
                    files.AddRange(partFileDic[this.cond.CarrierPartA]);
                    textBoxACnt.Text = Convert.ToString(partFileDic[this.cond.CarrierPartA].Count);
                }
                if (partFileDic.ContainsKey(this.cond.CarrierPartB))
                {
                    files.AddRange(partFileDic[this.cond.CarrierPartB]);
                    textBoxBCnt.Text = Convert.ToString(partFileDic[this.cond.CarrierPartB].Count);
                }
                textBoxAllCnt.Text = Convert.ToString(files.Count);
                gridControlFile.DataSource = files;
            }
        }
    }

    public class SiteQueryCond
    {
        public bool BTimeAgo { get; set; }

        public int ITimeAgo { get; set; }

        public DateTime StartTime { get; set; }

        public DateTime EndTime { get; set; }

        public ECarrier CarrierPartA { get; set; }

        public ECarrier CarrierPartB { get; set; }

        public QueryCondition QueryCond { get; set; }

        public Dictionary<AreaBase, List<AreaBase>> RootLeafDic { get; set; }

        public Dictionary<int, List<int>> SiteAreaIDDic { get; set; }
        public Dictionary<string, AreaBase> NameLeafDic { get; set; }

        public int CurType { get; set; }

        public List<int> CurIdLst { get; set; }
        
        public List<int> CarrierLst { get; set; }

        public SiteQueryCond(QueryCondition cond)
        {
            this.BTimeAgo = false;
            this.ITimeAgo = 5;

            DateTime dtNow = DateTime.Now.Date;
            this.StartTime = dtNow;
            this.EndTime = dtNow.AddDays(-1);
            if (cond.Periods.Count > 0)
            {
                this.StartTime = cond.Periods[0].BeginTime;
                this.EndTime = cond.Periods[0].EndTime;
            }

            this.CarrierPartA = ECarrier.移动;
            this.CarrierPartB = ECarrier.联通;

            this.QueryCond = cond;
            this.RootLeafDic = new Dictionary<AreaBase, List<AreaBase>>();
            this.SiteAreaIDDic = new Dictionary<int, List<int>>();
            this.CarrierLst = new List<int>();
            this.NameLeafDic = new Dictionary<string, AreaBase>();
            this.CurIdLst = new List<int>();
        }

        public void SetCarrier(List<int> carriers)
        {
            this.CarrierLst = carriers;
        }

        public void GetSelAreaIDs()
        {
            SiteAreaIDDic.Clear();
            foreach (AreaBase root in RootLeafDic.Keys)
            {
                List<int> leafs = new List<int>();
                SiteAreaIDDic[root.AreaTypeID] = leafs;

                foreach (AreaBase leaf in RootLeafDic[root])
                {
                    leafs.Add(leaf.AreaID);

                    NameLeafDic[string.Format("{0}_{1}", leaf.AreaTypeID, leaf.AreaID)] = leaf;
                }
            }
        }

        public void SetQueryCond(QueryCondition cond)
        {
            this.QueryCond = cond;
        }

        public TimePeriod GetTimePeriod()
        {
            if (BTimeAgo)
            {
                DateTime dtNow = DateTime.Now;
                return new TimePeriod(dtNow.AddDays(0 - ITimeAgo), dtNow);
            }
            return new TimePeriod(StartTime, EndTime);
        }
    }

    public enum ECarrier 
    { 
        移动 = 1, 
        联通 = 2, 
        电信 = 3 
    }

    public class CDTSiteFileInfo
    {
        public string StrFileName { get; set; }
        public string StrProj { get; set; }
        public string StrService { get; set; }
        public string StrCarrier { get; set; }

        public CDTSiteFileInfo(FileAreaStatInfo file)
        {
            this.StrFileName = file.fileHeader.Name;
            this.StrProj = file.fileHeader.ProjectDescription;
            this.StrService = file.fileHeader.ServiceTypeDescription;
            this.StrCarrier = file.fileHeader.CarrierTypeDescription;
        }
    }
}
