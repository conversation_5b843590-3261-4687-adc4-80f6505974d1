﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTWeakCoverCDForm : MinCloseForm
    {
        public ZTWeakCoverCDForm()
            : base()
        {
            InitializeComponent();
            mapForm = MainModel.MainForm.GetMapForm();
        }

        public void FillData()
        {
            ListViewRoad.ClearObjects();
            ListViewRoad.SetObjects(MainModel.WeakCoverRoadCDList);
            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        private void ListViewRoad_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (ListViewRoad.SelectedObject != null && ListViewRoad.SelectedObject is WeakCoverInfo_CD)
            {
                WeakCoverInfo_CD info = ListViewRoad.SelectedObject as WeakCoverInfo_CD;

                mModel.DTDataManager.Clear(); 
                foreach (TestPoint tp in info.SampleList)
                {
                    mModel.DTDataManager.Add(tp);
                }
                mModel.FireDTDataChanged(this);
            }
        }

        //private void GoToView(List<CDMATestPointDetail> tpList)
        //{
        //    double ltLong = 100000;
        //    double ltLat = -100000;
        //    double brLong = -100000;
        //    double brLat = 100000;

        //    foreach (TestPoint tp in tpList)
        //    {
        //        if (tp.Longitude < ltLong)
        //        {
        //            ltLong = tp.Longitude;
        //        }
        //        if (tp.Longitude > brLong)
        //        {
        //            brLong = tp.Longitude;
        //        }
        //        if (tp.Latitude < brLat)
        //        {
        //            brLat = tp.Latitude;
        //        }
        //        if (tp.Latitude > ltLat)
        //        {
        //            ltLat = tp.Latitude;
        //        }
        //    }
        //    mapForm.GoToView(new DbRect(ltLong, ltLat, brLong, brLat));
        //}

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(ListViewRoad);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

        private MapForm mapForm;
    }
}
