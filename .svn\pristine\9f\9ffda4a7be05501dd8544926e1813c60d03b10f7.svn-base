﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LowSpeedInfoByFileForm_TD
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTransfereSize = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTransfereTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnSampleCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellID = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(895, 407);
            this.gridControl.TabIndex = 2;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            this.gridControl.Click += new System.EventHandler(this.gridControl_DoubleClick);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnFileName,
            this.gridColumnCellName,
            this.gridColumnLAC,
            this.gridColumnCI,
            this.gridColumnCellID,
            this.gridColumnTime,
            this.gridColumnTransfereSize,
            this.gridColumnTransfereTime,
            this.gridColumnSampleCount});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsSelection.MultiSelect = true;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnFileName
            // 
            this.gridColumnFileName.Caption = "文件名";
            this.gridColumnFileName.FieldName = "FileName";
            this.gridColumnFileName.Name = "gridColumnFileName";
            this.gridColumnFileName.Visible = true;
            this.gridColumnFileName.VisibleIndex = 0;
            this.gridColumnFileName.Width = 151;
            // 
            // gridColumnCellName
            // 
            this.gridColumnCellName.Caption = "小区名";
            this.gridColumnCellName.FieldName = "CellName";
            this.gridColumnCellName.Name = "gridColumnCellName";
            this.gridColumnCellName.Visible = true;
            this.gridColumnCellName.VisibleIndex = 1;
            // 
            // gridColumnLAC
            // 
            this.gridColumnLAC.Caption = "LAC";
            this.gridColumnLAC.FieldName = "LAC";
            this.gridColumnLAC.Name = "gridColumnLAC";
            this.gridColumnLAC.Visible = true;
            this.gridColumnLAC.VisibleIndex = 2;
            // 
            // gridColumnCI
            // 
            this.gridColumnCI.Caption = "CI";
            this.gridColumnCI.FieldName = "CI";
            this.gridColumnCI.Name = "gridColumnCI";
            this.gridColumnCI.Visible = true;
            this.gridColumnCI.VisibleIndex = 3;
            // 
            // gridColumnTime
            // 
            this.gridColumnTime.Caption = "时间";
            this.gridColumnTime.FieldName = "DateTime";
            this.gridColumnTime.Name = "gridColumnTime";
            this.gridColumnTime.Visible = true;
            this.gridColumnTime.VisibleIndex = 5;
            this.gridColumnTime.Width = 146;
            // 
            // gridColumnTransfereSize
            // 
            this.gridColumnTransfereSize.Caption = "下载数据量";
            this.gridColumnTransfereSize.FieldName = "TransferedSize";
            this.gridColumnTransfereSize.Name = "gridColumnTransfereSize";
            this.gridColumnTransfereSize.Visible = true;
            this.gridColumnTransfereSize.VisibleIndex = 6;
            // 
            // gridColumnTransfereTime
            // 
            this.gridColumnTransfereTime.Caption = "下载时长";
            this.gridColumnTransfereTime.FieldName = "TransferedTime";
            this.gridColumnTransfereTime.Name = "gridColumnTransfereTime";
            this.gridColumnTransfereTime.Visible = true;
            this.gridColumnTransfereTime.VisibleIndex = 7;
            // 
            // gridColumnSampleCount
            // 
            this.gridColumnSampleCount.Caption = "统计样本数";
            this.gridColumnSampleCount.FieldName = "SampleCount";
            this.gridColumnSampleCount.Name = "gridColumnSampleCount";
            this.gridColumnSampleCount.Visible = true;
            this.gridColumnSampleCount.VisibleIndex = 8;
            // 
            // gridColumnCellID
            // 
            this.gridColumnCellID.Caption = "CellID";
            this.gridColumnCellID.FieldName = "CellID";
            this.gridColumnCellID.Name = "gridColumnCellID";
            // 
            // LowSpeedInfoByFileForm_TD
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(895, 407);
            this.Controls.Add(this.gridControl);
            this.Name = "LowSpeedInfoByFileForm_TD";
            this.Text = "低速率里程分析";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSampleCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLAC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTransfereSize;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTransfereTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellID;
    }
}