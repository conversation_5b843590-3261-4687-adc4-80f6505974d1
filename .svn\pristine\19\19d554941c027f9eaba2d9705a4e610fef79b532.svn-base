﻿using System;
using System.Collections.Generic;
using System.Text;
using MapWinGIS;
using AxMapWinGIS;

namespace MasterCom.MapSpaceManager.MapSpaceManager
{
    public class MapHelper
    {
        public static void GetMapBounds(AxMap mapControl, out double xMin, out double yMin, out double xMax, out double yMax)
        {
            double zMin = 0, zMax = 0;
            Extents extents = mapControl.Extents as Extents;
            extents.GetBounds(out xMin, out yMin, out zMin, out xMax, out yMax, out zMax);
        }
    }
}
