﻿using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.UserMng;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryCellGridByRegion : QueryCellKPIByFiles
    {
        public QueryCellGridByRegion()
            : base() { }

        public override string Name
        {
            get
            {
                return "按区域内数据所属小区统计(可过滤小区)";
            }
        }

        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.cell_grid;
        }

        public override bool CanEnabled(Model.SearchGeometrys searchGeometrys)//可用
        {
            if (searchGeometrys.Region == null)//选择网格区域
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override LogInfoItem getRecLogItem()
        {
            return new LogInfoItem(2, 11000, 11060, this.Name);
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        protected override void getFilesCondition()
        {
            //
        }

        public override MainModel.NeedSearchType needSearchType()//查询类型
        {
            return MainModel.NeedSearchType.Region;//按区域查
        }

        //接收和处理特定的统计数据
        protected override void recieveAndHandleSpecificStatData(Net.Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            #region
            int lac = package.Content.GetParamInt();
            long ci = NRDTDataHelper.AnalyseNCI(package.Content);
            //int ci = package.Content.GetParamInt();
            double lng = package.Content.GetParamDouble();//经度
            double lat = package.Content.GetParamDouble();//纬度
            GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            if (!condition.Geometorys.GeoOp.Contains(grid.CenterLng, grid.CenterLat))
            {
                return;
            }
            filterAddStatData(package, curImgColumnDef, singleStatData, lac, ci);
            #endregion
        }


        protected override void handleStatEvent(Event evt)//处理统计事件
        {
            object Lac = evt["LAC"];
            object Ci = evt["CI"];
            int lac = Convert.ToInt32(Lac);
            long ci = Convert.ToInt64(Ci);
            if (!condition.Geometorys.GeoOp.Contains(evt.Longitude, evt.Latitude))
            {
                return;
            }
            eventFilterAddStatData(evt, Lac, Ci, lac, ci);
        }

        protected override void preparePackageCommand(Package package)
        {
            if (isQueringEvent)
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
                package.Content.PrepareAddParam();
            }
            else
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.KPI_CELL_GRID;
                package.Content.PrepareAddParam();
            }
        }

        /// <summary>
        /// 添加地理过滤器
        /// </summary>
        /// <param name="package"></param>
        protected override void AddGeographicFilter(Package package)
        {
            if (isQueringEvent)
            {
                AddDIYEndOpFlag(package);
                this.AddDIYRegion_Sample(package);
            }
            else
            {
                this.AddDIYRegion_Intersect(package);
            }
        }
    }
}
