﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public class AreaKpiBaseQuery : QueryKPIStatAll
    {
        public bool IsStatLatestOnly { get; set; } = false;
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        public Dictionary<AreaBase, AreaKPIDataGroup<AreaBase>> AreaKpiMap { get; set; } = new Dictionary<AreaBase, AreaKPIDataGroup<AreaBase>>();

        protected Dictionary<int, Dictionary<int, AreaBase>> typePairMap = new Dictionary<int, Dictionary<int, AreaBase>>();

        protected string strType = string.Empty;

        private List<string> formulaVec;

        protected MasterCom.MTGis.DbRect rect = null;

        public AreaKpiBaseQuery()
        {
            IsShowResultForm = false;
            formulaVec = new List<string>();
        }

        public void SetQueryAllParams(bool bQueryAll)
        {
            this.isQueryAllParams = bQueryAll;
        }

        public void SetFormula(List<string> formulas)
        {
            this.formulaVec = formulas;
        }

        private MasterCom.MTGis.DbRect getRect()
        {
            if (typePairMap.Count == 0)
                return null;

            MasterCom.MTGis.DbRect dbRect = new MasterCom.MTGis.DbRect();
            dbRect.x1 = dbRect.y1 = 900;
            dbRect.x2 = dbRect.y2 = 0;

            foreach (Dictionary<int, AreaBase> areaDic in typePairMap.Values)
            {
                foreach (AreaBase area in areaDic.Values)
                {
                    dbRect.x1 = Math.Min(area.Bounds.x1, dbRect.x1);
                    dbRect.x2 = Math.Max(area.Bounds.x2, dbRect.x2);
                    dbRect.y1 = Math.Min(area.Bounds.y1, dbRect.y1);
                    dbRect.y2 = Math.Max(area.Bounds.y2, dbRect.y2);
                }
            }
            return dbRect;
        }

        public void SetTypes(Dictionary<int, Dictionary<int, AreaBase>> typeIdMap)
        {
            typePairMap = typeIdMap;
            rect = getRect();

            StringBuilder sb = new StringBuilder();
            foreach (int type in typeIdMap.Keys)
            {
                sb.Append(type);
                sb.Append(',');
            }
            strType = sb.ToString().TrimEnd(',');
        }

        protected AreaBase getArea(int areaType, int areaID)
        {
            AreaBase rtArea;
            Dictionary<int, AreaBase> idArea;
            if (!typePairMap.TryGetValue(areaType, out idArea))
            {
                return null;
            }
            if (!idArea.TryGetValue(areaID, out rtArea))
            {
                return null;
            }
            return rtArea;
        }

        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.area;
        }

        protected override bool getConditionBeforeQuery()
        {
            return typePairMap != null && typePairMap.Count > 0;
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            string triadIDSet = "1,1,1089";
            if (isQueryAllParams)
            {
                triadIDSet = "-1,-1,-1";
            }
            else if (formulaVec.Count > 0)
            {
                triadIDSet = getTriadIDIgnoreServiceType(formulaVec);
            }
            return triadIDSet;
        }

        protected override void preparePackageCommand(Net.Package package)
        {
            if (isQueringEvent)
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREASTAT_EVNET;
                package.Content.PrepareAddParam();
            }
            else
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREASTAT_KPI;
                package.Content.PrepareAddParam();
            }
        }

        protected override void AddGeographicFilter(Package package)
        {
            //
        }

        protected override void preparePackageNeededInfo_Event(Package package)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("0,1,47,");
            sb.Append("0,2,47,");
            sb.Append("0,3,47,");
            sb.Append("0,4,47,");
            sb.Append("0,5,47,");
            sb.Append("0,6,47,");
            sb.Append("0,7,47,");
            sb.Append("0,8,47,");
            sb.Append("0,9,47,");
            sb.Append("0,10,47,");
            sb.Append("0,11,47,");
            sb.Append("0,12,47,");
            sb.Append("0,13,47,");
            sb.Append("0,14,47,");
            sb.Append("0,15,47,");
            sb.Append("0,16,47");
            package.Content.AddParam(sb.ToString());
        }

        protected void addRegion(Package package)
        {
            if (rect == null)
            {
                return;
            }
            package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
            package.Content.AddParam(rect.x1);
            package.Content.AddParam(rect.y2);
            package.Content.AddParam(rect.x2);
            package.Content.AddParam(rect.y1);
        }

        protected override void AddExtraCondition(Package package, params object[] reservedParams)
        {
            addRegion(package);
            AddDIYEndOpFlag(package);
            package.Content.AddParam((byte)OpOptionDef.InSelect);
            package.Content.AddParam("0,25,1");
            package.Content.AddParam(strType);


            Dictionary<int, bool> areaIdDic = new Dictionary<int, bool>();
            foreach (Dictionary<int, AreaBase> pairSet in typePairMap.Values)
            {
                foreach (int id in pairSet.Keys)
                {
                    areaIdDic[id] = true;
                }
            }

            StringBuilder sb = new StringBuilder();
            foreach (int id in areaIdDic.Keys)
            {
                sb.Append(id.ToString() + ",");
            }
            if (sb.Length > 0 && sb.Length < 5000)
            {
                package.Content.AddParam((byte)OpOptionDef.InSelect);
                package.Content.AddParam("0,26,1");
                package.Content.AddParam(sb.Remove(sb.Length - 1, 1).ToString());
            }
        }

        protected override bool isImgColDefContent(Package package, List<StatImgDefItem> imgColDefSet)
        {
            if (package.Content.Type == ResponseType.COLUMN_DEFINE)
            {
                imgColDefSet.Clear();
                parseToCurImgColumnDef(package.Content.GetParamString(), imgColDefSet);
                return true;
            }
            else
            {
                return false;
            }
        }

        protected override bool isColDefContent(Package package, List<ColumnDefItem> imgColDefSet)
        {
            if (package.Content.Type == ResponseType.AREASTAT_KPI_EVENT_LTE ||
                package.Content.Type == ResponseType.AREASTAT_KPI_EVENT_LTE_FDD)
            {
                return false;
            }
            return base.isColDefContent(package, imgColDefSet);
        }

        protected override bool isKPIDataContent(Package package, out KPIStatDataBase statData)
        {
            statData = null;

            if (package.Content.Type == ResponseType.AREASTAT_KPI_LTE)
            {
                statData = new StatDataLTE();
            }
            else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREASTAT_KPI_LTE_FDD_AMR)
            {
                statData = new StatDataLTE_FDD();
            }

            return statData != null || base.isKPIDataContent(package, out statData);
        }

        protected override void recieveAndHandleSpecificStatData(Package package, List<StatImgDefItem> curImgColumnDef
           , KPIStatDataBase singleStatData)
        {
            int areaTypeID = package.Content.GetParamInt();
            int areaSubID = package.Content.GetParamInt();
            AreaBase area = getArea(areaTypeID, areaSubID);
            if (area == null)
            {
                return;
            }

            fillStatData(package, curImgColumnDef, singleStatData);
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(singleStatData.FileID);

            AreaKPIDataGroup<AreaBase> group;
            if (!AreaKpiMap.TryGetValue(area, out group))
            {
                group = new AreaKPIDataGroup<AreaBase>(area, IsStatLatestOnly);
                AreaKpiMap[area] = group;
            }
            group.AddStatData(fi, singleStatData);
        }

        protected override void handleStatEvent(Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);

            AreaBase area = getArea(evt.AreaTypeID, evt.AreaID);
            if (area == null)
            {
                return;
            }

            AreaKPIDataGroup<AreaBase> group;
            if (!AreaKpiMap.TryGetValue(area, out group))
            {
                group = new AreaKPIDataGroup<AreaBase>(area, IsStatLatestOnly);
                AreaKpiMap[area] = group;
            }
            group.AddStatData(fi, eventData);
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            foreach (AreaKPIDataGroup<AreaBase> group in AreaKpiMap.Values)
            {
                group.FinalMtMoGroup();
            }

            //补填
            foreach (int areaType in typePairMap.Keys)
            {
                foreach (AreaBase area in typePairMap[areaType].Values)
                {
                    AreaKPIDataGroup<AreaBase> group;
                    if (!AreaKpiMap.TryGetValue(area, out group))
                    {
                        group = new AreaKPIDataGroup<AreaBase>(area, IsStatLatestOnly);
                        AreaKpiMap[area] = group;
                    }
                }
            }
        }
    }

}
