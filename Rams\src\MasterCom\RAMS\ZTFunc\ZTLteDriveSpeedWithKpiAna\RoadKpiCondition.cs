﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class RoadKpiCondition
    {
        public RoadKpiCondition()
        {
            GoodSinrGate = BadSinrGate = 0;
            GoodRsrpGate = BadRsrpGate = -105;
            GoodDownLoadSpeedGate = BadDownLoadSpeedGate = 10;

            IsGoodKpiAllAnd = true;
            IsCheckGoodDlSpeed = true;
            IsCheckBadDlSpeed = true;
        }
        /// <summary>
        /// 指标是否为和关系
        /// </summary>
        public bool IsGoodKpiAllAnd { get; set; }
        public bool IsBadKpiAllAnd { get; set; }
        public float StopSpeedMaxGate { get; set; } = 10;
        public float SlowSpeedMaxGate { get; set; } = 30;
        public float SlowSpeedMinGate { get; set; } = 10;
        public float FastSpeedMinGate { get; set; } = 30;
        public double MinCoverRoadDistance { get; set; } = 5;//最小持续距离
        public double Max2TPDistance { get; set; } = 50;
        public double MinDuration { get; set; } = 2;//最小持续时长
        public bool CheckMinDistance { get; set; } = true;
        public bool MatchMinWeakCoverDistance(double distance)
        {
            if (CheckMinDistance)
            {
                return distance >= MinCoverRoadDistance;
            }
            else
            {
                return true;
            }
        }
        
        public bool CheckMinDuration { get; set; } = false;
        public bool MatchMinWeakCoverDuration(double duration)
        {
            if (CheckMinDuration)
            {
                return duration >= MinDuration;
            }
            else
            {
                return true;
            }
        }
        
        public double CommonTpPercentGate { get; set; } = 90;
        public float GoodSinrGate { get; set; }
        public float GoodRsrpGate { get; set; }
        public bool IsCheckGoodDlSpeed { get; set; }
        public float GoodDownLoadSpeedGate { get; set; }

        public float BadSinrGate { get; set; }
        public float BadRsrpGate { get; set; }
        public bool IsCheckBadDlSpeed { get; set; }
        public float BadDownLoadSpeedGate { get; set; }

        public bool IsGoodKpiTestPoint(float? sinr, float? rsrp, double? speed)
        {
            bool goodSinr = isGoodSinr(sinr);
            bool goodRsrp = isGoodRsrp(rsrp);
            bool goodSpeed= isGoodSpeed(speed);
            if (IsGoodKpiAllAnd)
            {
                if (goodSinr && rsrp != null && goodRsrp && (!IsCheckGoodDlSpeed || goodSpeed))
                {
                    return true;
                }
            }
            else
            {
                if (goodSinr || goodRsrp || goodSpeed)
                {
                    return true;
                }
            }
            return false;
        }

        private bool isGoodSinr(float? sinr)
        {
            if (sinr != null && sinr >= GoodSinrGate)
            {
                return true;
            }
            return false;
        }

        private bool isGoodRsrp(float? rsrp)
        {
            if (rsrp != null && rsrp >= GoodRsrpGate)
            {
                return true;
            }
            return false;
        }

        private bool isGoodSpeed(double? speed)
        {
            if (speed != null && IsCheckGoodDlSpeed && speed >= GoodDownLoadSpeedGate)
            {
                return true;
            }
            return false;
        }

        public bool IsBadKpiTestPoint(float? sinr, float? rsrp, double? speed)
        {
            bool badSinr = isBadSinr(sinr);
            bool badRsrp = isBadRsrp(rsrp);
            bool badSpeed = isBadSpeed(speed);
            if (IsBadKpiAllAnd)
            {
                if (badSinr && badRsrp && (!IsCheckBadDlSpeed || badSpeed))
                {
                    return true;
                }
            }
            else
            {
                if (badSinr || badRsrp || badSpeed)
                {
                    return true;
                }
            }
            return false;
        }

        private bool isBadSinr(float? sinr)
        {
            if (sinr != null && sinr < BadSinrGate)
            {
                return true;
            }
            return false;
        }

        private bool isBadRsrp(float? rsrp)
        {
            if (rsrp != null && rsrp < BadRsrpGate)
            {
                return true;
            }
            return false;
        }

        private bool isBadSpeed(double? speed)
        {
            if (speed != null && IsCheckBadDlSpeed && speed < BadDownLoadSpeedGate)
            {
                return true;
            }
            return false;
        }


        public string GetDriveSpeedType(double speed)
        {
            if (speed >= 0 && speed < StopSpeedMaxGate)
            {
                return "停车";
            }
            else if (speed >= SlowSpeedMinGate && speed < SlowSpeedMaxGate)
            {
                return "车速慢";
            }
            else if (speed >= FastSpeedMinGate)
            {
                return "车速快";
            }
            return "Undefined";
        }
    }
}
