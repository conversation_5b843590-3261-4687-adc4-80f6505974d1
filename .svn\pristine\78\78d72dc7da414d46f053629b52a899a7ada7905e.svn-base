﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class TPBlockDisplayColumn : TestPointDisplayColumn
    {
        public TPBlockDisplayColumn(DTDisplayParameterInfo param, int paramArrayIndex, ESummaryValueType valueType)
            : base("未命名", param, paramArrayIndex, valueType)
        {
        }
        public override string ToString()
        {
            if (DisplayParam == null)
            {
                return "";
            }
            string idxStr = ParamArrayIndex == -1 ? " " : "[" + ParamArrayIndex.ToString() + "] ";
            return DisplayParam.Name + idxStr + ValueType.ToString();
        }
        public TPBlockDisplayColumn()
        {
        }
        internal System.Xml.XmlElement SaveConfig(MasterCom.Util.XmlConfigFile configFile, System.Xml.XmlElement config, string name, object value)
        {
            if (value is TPBlockDisplayColumn)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "SysName", this.DisplayParam.System.Name);
                configFile.AddItem(item, "ParamName", this.DisplayParam.Name);
                configFile.AddItem(item, "ParamArrayIndex", this.ParamArrayIndex);
                configFile.AddItem(item, "SummaryValueType", (int)this.ValueType);
                return item;
            }
            return null;
        }

        internal object LoadConfig(MasterCom.Util.XmlConfigFile configFile, XmlElement item, string itemName)
        {
            if (itemName == typeof(TPBlockDisplayColumn).Name)
            {
                string sysName = configFile.GetItemValue(item, "SysName") as string;
                string paramName = configFile.GetItemValue(item, "ParamName") as string;
                this.DisplayParam = DTDisplayParameterManager.GetInstance()[sysName][paramName];
                this.ParamArrayIndex = (int)configFile.GetItemValue(item, "ParamArrayIndex");
                this.ValueType = (ESummaryValueType)configFile.GetItemValue(item, "SummaryValueType");
                return this;
            }
            return null;
        }
    }

    public enum ESummaryValueType
    {
        Average = 0,
        Min,
        Max
    }
}
