﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.GridOrderCommon;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryGridOrder : DIYSQLBase
    {
#if Shandong
        public static readonly string DBCatName = "KPIMNG_DB_SHANDONG";
#elif <PERSON><PERSON>
        public static readonly string DBCatName = "KPIGRID_DB_JILIN";
#else
        public static readonly string DBCatName = "KPIMNG_DB_SHANDONG";
#endif

        public QueryGridOrder()
            : base()
        {
            MainDB = true;
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18044, this.Name);
        }
        public override string Name 
        { 
            get { return "栅格工单"; } 
        }
        private enum TbName
        {
            Order,
            OrderGrid,
            GridCell,
            GridKPI,
            CellKPI,
        }

        readonly TaskIDFilterDlg dlg = new TaskIDFilterDlg();
        private List<GridOrderToken> token2Qry = new List<GridOrderToken>();
        protected override bool isValidCondition()
        {
            cityIDs = string.Empty;
            orderIDFilter = string.Empty;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            StringBuilder sb = new StringBuilder();
            foreach (int id in dlg.CityIDSet)
            {
                sb.Append(id + ",");
            }
            cityIDs = sb.ToString().TrimEnd(',');
            orderIDFilter = dlg.OrderIDs;
            token2Qry = dlg.TokenSet;
            orderMonths = dlg.orderMonths;
            return true;
        }

        private string orderIDFilter = string.Empty;
        private string cityIDs = string.Empty;
        protected override void query()
        {
            OrderTokenMng mng = OrderTokenMng.Instance;
            Console.Write(mng);
            OrderKPICfgMng cfgMng = OrderKPICfgMng.Instance;
            Console.Write(cfgMng);
            orderDic = new Dictionary<string, GridOrder>();
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port
                    , userName, password, dbid > 0 ? dbid : MainModel.DistrictID)
                    != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitTextBox.Show("正在查询工单...", queryInThread, clientProxy);
                fireShowResultForm();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void fireShowResultForm()
        {
            GridOrderListForm frm = MainModel.CreateResultForm(typeof(GridOrderListForm)) as GridOrderListForm;
            frm.FillData(new List<GridOrder>(this.orderDic.Values));
            frm.Visible = true;
            frm.BringToFront();
            orderDic = null;
        }

        GridOrderToken curToken = null;
        TbName curTbName;
        protected override void queryInThread(object o)
        {
            try
            {
                foreach (GridOrderToken token in OrderTokenMng.Instance.TokenSet)
                {
                    if (!token2Qry.Contains(token))
                    {
                        continue;
                    }
                    curToken = token;
                    queryFromDB((ClientProxy)o, TbName.Order);
                    if (listSetID.Count <= 0)
                        continue;

                    setIDs = string.Empty;
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < listSetID.Count; i++)
                    {
                        sb.Append(listSetID[i].ToString() + ",");
                        //为防止参数过长，每五百条工单查询一次
                        if ((i + 1) % 500 == 0)
                        {
                            setIDs = sb.ToString().TrimEnd(',');
                            queryFromDB((ClientProxy)o, TbName.OrderGrid);
                            queryFromDB((ClientProxy)o, TbName.GridCell);
                            queryFromDB((ClientProxy)o, TbName.GridKPI);
                            queryFromDB((ClientProxy)o, TbName.CellKPI);
                            sb = new StringBuilder();
                            setIDs = string.Empty;
                        }
                    }
                    setIDs = sb.ToString();
                    if (setIDs != string.Empty)
                    {
                        setIDs = setIDs.TrimEnd(',');
                        queryFromDB((ClientProxy)o, TbName.OrderGrid);
                        queryFromDB((ClientProxy)o, TbName.GridCell);
                        queryFromDB((ClientProxy)o, TbName.GridKPI);
                        queryFromDB((ClientProxy)o, TbName.CellKPI);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                WaitTextBox.Close();
            }
        }

        private void queryFromDB(ClientProxy proxy, TbName table)
        {
            Package package = proxy.Package;
            curTbName = table;
            string strsql = getSqlTextString();
            E_VType[] retArrDef = getSqlRetTypeArr();
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            if (MainDB)
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
            }
            else
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
            }
            package.Content.PrepareAddParam();
            package.Content.AddParam(strsql);
            StringBuilder sb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    sb.Append((int)retArrDef[i]);
                    sb.Append(",");
                }
            }
            package.Content.AddParam(sb.ToString().TrimEnd(','));
            proxy.Send();
            if (curTbName == TbName.Order)
                listSetID = new List<int>();
            receiveRetData(proxy);
        }

        private string orderMonths = string.Empty;
        private string setIDs = string.Empty;
        List<int> listSetID = null;
        protected override string getSqlTextString()
        {
            switch (curTbName)
            {
#if Shandong || DEBUG
                case TbName.Order:
                    //return string.Format("select cityID,setTypeID,setID,itemCount,areaNames,roadNames,taskID,orderStatus from [{1}].[dbo].{0} where cityID in({2})", curToken.OrderTbName, DBCatName, cityIDs)
                    return string.Format("EXEC sp_grid_set_shandong_get '{1}','{0}','{2}','{3}',''", curToken.OrderTbName, DBCatName, cityIDs, orderMonths);
                case TbName.OrderGrid:
                    //return string.Format("select setID,cityID,itemID,setTypeID,gridSN,ltLongitude,ltLatitude from [{1}].[dbo].{0} where cityID in({2})", curToken.OrderGridTbName, DBCatName, cityIDs)                 
                    return string.Format("EXEC sp_grid_set_shandong_get '{1}','{0}','','','{2}'", curToken.OrderGridTbName, DBCatName, setIDs);
                case TbName.GridCell:
                    //return string.Format("select setID,cityID,itemID,cellID,setTypeID,gridSN,projectID,serviceID,fileID,logTablName,LAC,CI,cellName,primarytype,specifictype,detail,suggest,status,filename from [{1}].[dbo].{0} where cityID in({2})", curToken.OrderGridCellTbName, DBCatName, cityIDs)
                    return string.Format("EXEC sp_grid_set_shandong_get '{1}','{0}','','','{2}'", curToken.OrderGridCellTbName, DBCatName, setIDs);
                case TbName.GridKPI:
                    //return string.Format("select [setID],[cityID],[itemID],[setTypeID],[kpiID],[kpiValue] from [{1}].[dbo].{0} where cityID in({2})", curToken.OrderGridKPITbName, DBCatName, cityIDs)
                    return string.Format("EXEC sp_grid_set_shandong_get '{1}','{0}','','','{2}'", curToken.OrderGridKPITbName, DBCatName, setIDs);
                case TbName.CellKPI:
                    //return string.Format("select [setID],[cityID],[itemID],[cellID],[setTypeID],[kpiID],[kpiValue] from [{1}].[dbo].{0} where cityID in({2})", curToken.OrderGridCellKPITbName, DBCatName, cityIDs)
                    return string.Format("EXEC sp_grid_set_shandong_get '{1}','{0}','','','{2}'", curToken.OrderGridCellKPITbName, DBCatName, setIDs);
#else 
                    case TbName.Order:
                    //return string.Format("select cityID,setTypeID,setID,itemCount,areaNames,roadNames,taskID,orderStatus from [{1}].[dbo].{0} where cityID in({2})", curToken.OrderTbName, DBCatName, cityIDs)
                    return string.Format("EXEC sp_grid_set_get '{1}','{0}','{2}','{3}',''", curToken.OrderTbName, DBCatName, cityIDs, orderMonths);
                case TbName.OrderGrid:
                    //return string.Format("select setID,cityID,itemID,setTypeID,gridSN,ltLongitude,ltLatitude from [{1}].[dbo].{0} where cityID in({2})", curToken.OrderGridTbName, DBCatName, cityIDs)                 
                    return string.Format("EXEC sp_grid_set_get '{1}','{0}','','','{2}'", curToken.OrderGridTbName, DBCatName, setIDs);
                case TbName.GridCell:
                    //return string.Format("select setID,cityID,itemID,cellID,setTypeID,gridSN,projectID,serviceID,fileID,logTablName,LAC,CI,cellName,primarytype,specifictype,detail,suggest,status,filename from [{1}].[dbo].{0} where cityID in({2})", curToken.OrderGridCellTbName, DBCatName, cityIDs)
                    return string.Format("EXEC sp_grid_set_get '{1}','{0}','','','{2}'", curToken.OrderGridCellTbName, DBCatName, setIDs);
                case TbName.GridKPI:
                    //return string.Format("select [setID],[cityID],[itemID],[setTypeID],[kpiID],[kpiValue] from [{1}].[dbo].{0} where cityID in({2})", curToken.OrderGridKPITbName, DBCatName, cityIDs)
                    return string.Format("EXEC sp_grid_set_get '{1}','{0}','','','{2}'", curToken.OrderGridKPITbName, DBCatName, setIDs);
                case TbName.CellKPI:
                    //return string.Format("select [setID],[cityID],[itemID],[cellID],[setTypeID],[kpiID],[kpiValue] from [{1}].[dbo].{0} where cityID in({2})", curToken.OrderGridCellKPITbName, DBCatName, cityIDs)
                    return string.Format("EXEC sp_grid_set_get '{1}','{0}','','','{2}'", curToken.OrderGridCellKPITbName, DBCatName, setIDs);
#endif
                default:
                    break;
            }
            return null;
        }

        protected override Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            if (TbName.Order == curTbName)
            {
                E_VType[] arr = new E_VType[8];
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i] = E_VType.E_Int;
                return arr;
            }
            else if (TbName.OrderGrid == curTbName)
            {
                E_VType[] arr = new E_VType[7];
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_Int;
                arr[i] = E_VType.E_Int;
                return arr;
            }
            else if (curTbName == TbName.GridCell)
            {
                E_VType[] arr = new E_VType[19];
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_String;
                arr[i++] = E_VType.E_Int;
                arr[i] = E_VType.E_String;
                return arr;
            }
            else if (TbName.GridKPI == curTbName)
            {
                E_VType[] arr = new E_VType[6];
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i] = E_VType.E_Float;
                return arr;
            }
            else if (TbName.CellKPI == curTbName)
            {
                E_VType[] arr = new E_VType[7];
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i++] = E_VType.E_Int;
                arr[i] = E_VType.E_Float;
                return arr;
            }
            return new E_VType[0];
        }

        private Dictionary<string, GridOrder> orderDic = null;
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            if (curTbName == TbName.Order)
            {
                getOrder(package);
            }
            else if (curTbName == TbName.OrderGrid)
            {
                getOrderGrid(package);
            }
            else if (curTbName == TbName.GridCell)
            {
                getGridCell(package);
            }
            else if (curTbName == TbName.GridKPI)
            {
                getGridKPI(package);
            }
            else if (curTbName == TbName.CellKPI)
            {
                getCellKPI(package);
            }
        }

        private void getOrder(Package package)
        {
            GridOrder order = new GridOrder();
            order.CityID = package.Content.GetParamInt();
            order.SetTokenID = package.Content.GetParamInt();
            order.SetID = package.Content.GetParamInt();
            order.GridCount = package.Content.GetParamInt();
            order.AreaNames = package.Content.GetParamString();
            order.RoadNames = package.Content.GetParamString();
            order.TokenName = OrderTokenMng.Instance.GetToken(order.SetTokenID).Name;
            order.TaskID = package.Content.GetParamString();
            order.OrderStatus = package.Content.GetParamInt();
            listSetID.Add(order.SetID);
            if (this.orderIDFilter.Length == 0)
            {
                this.orderDic[order.Key] = order;
            }
            else if (this.orderIDFilter.Contains(order.TaskID))
            {
                this.orderDic[order.Key] = order;
            }
        }

        private void getOrderGrid(Package package)
        {
            OrderGridItem item = new OrderGridItem();
            item.SetID = package.Content.GetParamInt();
            item.CityID = package.Content.GetParamInt();
            item.ItemID = package.Content.GetParamInt();
            item.SetTokenID = package.Content.GetParamInt();
            item.GridSN = package.Content.GetParamString();
            item.LTLng = package.Content.GetParamInt() / 10000000.0;
            item.LTLat = package.Content.GetParamInt() / 10000000.0;

            GridOrder order = null;
            if (this.orderDic.TryGetValue(item.OrderKey, out order))
            {
                order.AddGrid(item);
            }
        }

        private void getGridCell(Package package)
        {
            OrderCellItem item = new OrderCellItem();
            item.SetID = package.Content.GetParamInt();
            item.CityID = package.Content.GetParamInt();
            item.ItemID = package.Content.GetParamInt();
            item.CellID = package.Content.GetParamInt();
            item.SetTokenID = package.Content.GetParamInt();
            item.GridSN = package.Content.GetParamString();
            item.ProjectID = package.Content.GetParamInt();
            item.ServiceID = package.Content.GetParamInt();
            item.FileID = package.Content.GetParamInt();
            item.LogTbName = package.Content.GetParamString();
            item.Lac = package.Content.GetParamInt();
            item.Ci = package.Content.GetParamInt();
            item.CellName = package.Content.GetParamString();
            item.PrimaryCause = package.Content.GetParamString();
            item.SpecifictCause = package.Content.GetParamString();
            item.Detail = package.Content.GetParamString();
            item.Suggest = package.Content.GetParamString();
            item.Status = package.Content.GetParamInt();
            item.FileName = package.Content.GetParamString();

            GridOrder order = null;
            if (this.orderDic.TryGetValue(item.OrderKey, out order))
            {
                order.AddCell(item);
            }
        }

        private void getGridKPI(Package package)
        {
            int setID = package.Content.GetParamInt();
            int cityID = package.Content.GetParamInt();
            int itemID = package.Content.GetParamInt();
            int setTypeID = package.Content.GetParamInt();
            int kpiID = package.Content.GetParamInt();
            double kpiValue = Math.Round(package.Content.GetParamFloat(), 2);
            GridOrderKPICfg cfg = OrderKPICfgMng.Instance.GetKPICfgItem(setTypeID, kpiID);
            if (cfg != null)
            {
                string orderKey = string.Format("{0}-{1}-{2}", cityID, setTypeID, setID);
                GridOrder order = null;
                if (this.orderDic.TryGetValue(orderKey, out order))
                {
                    order.AddGridKPI(itemID, cfg.Name, kpiValue);
                }
            }
        }

        private void getCellKPI(Package package)
        {
            int setID = package.Content.GetParamInt();
            int cityID = package.Content.GetParamInt();
            int itemID = package.Content.GetParamInt();
            int cellID = package.Content.GetParamInt();
            int setTypeID = package.Content.GetParamInt();
            int kpiID = package.Content.GetParamInt();
            double kpiValue = Math.Round(package.Content.GetParamFloat(), 2);
            GridOrderKPICfg cfg = OrderKPICfgMng.Instance.GetKPICfgItem(setTypeID, kpiID);
            string orderKey = string.Format("{0}-{1}-{2}", cityID, setTypeID, setID);
            GridOrder order = null;
            if (this.orderDic.TryGetValue(orderKey, out order))
            {
                order.AddGridCellKPI(itemID, cellID, cfg.Name, kpiValue);
            }
        }
    }
}
