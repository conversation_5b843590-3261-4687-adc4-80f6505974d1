﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class CreateWlanChannelsChartForm : CreateChildForm
    {
        public CreateWlanChannelsChartForm(MainModel mm)
            : base(mm)
        { 
        }
        public override string Description
        {
            get
            {
                return "创建信道图窗口 WlanChannelsChartForm ";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20023, this.Name);
        }
        public override string Name
        {
            get
            {
                return "信道图";
            }
        }

        protected override void initAction()
        {
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = MainModel.MainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = "MasterCom.RAMS.Func.WlanChannelsChartForm";
            actionParam["Text"] = "信道图";
            actionParam["ImageFilePath"] = @"images\GSM频率核查\同邻频.png";
            action = new ActionCreateChildFrame();
            action.Param = actionParam;
        }
    }
}
