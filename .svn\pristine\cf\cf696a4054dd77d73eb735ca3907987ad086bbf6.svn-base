﻿using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRWeakCoverAnaByRegion : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static NRWeakCoverAnaByRegion instance = null;
        NRWeakCoverAnaCondtion weakCoverCondtion = new NRWeakCoverAnaCondtion();
        Dictionary<string, NRRsrpRegionInfo> regionInfoDic = null;   //区域维度
        Dictionary<NRCell4Rsrp, NRRsrpCellInfo> cellInfoDic = null;  //小区维度
        protected Dictionary<string, MapOperation2> regionMapDic = null;
        List<NRRsrpPendingPointInfo> tpList_Pending = null;   //用于存放未找到原因的采样点，用于后续再次判断

        public static NRWeakCoverAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRWeakCoverAnaByRegion();
                    }
                }
            }
            return instance;
        }

        protected NRWeakCoverAnaByRegion()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = false;
            Columns = NRTpHelper.InitBaseReplayParamBackground(true, true);

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        public override string Name
        {
            get
            {
                return "NR弱覆盖分析(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35026, this.Name);
        }
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            NRWeakCoverAnaSetForm dlg = new NRWeakCoverAnaSetForm();
            dlg.SetCondition(weakCoverCondtion);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            weakCoverCondtion = dlg.GetCondition();
            return true;
        }
        protected override void clearDataBeforeAnalyseFiles()
        {
            regionInfoDic = new Dictionary<string, NRRsrpRegionInfo>();
            cellInfoDic = new Dictionary<NRCell4Rsrp, NRRsrpCellInfo>();
            regionMapDic = new Dictionary<string, MapOperation2>();
            tpList_Pending = new List<NRRsrpPendingPointInfo>();
            InitRegionMop2();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<Event> evtList = new List<Event>();
                foreach (Event evt in fileMng.Events)
                {
                    bool isValid = NREventHelper.HandoverHelper.JudgeHandoverSuccess(evt.ID, weakCoverCondtion.IsAnaLte);
                    if (isValid)
                    {
                        evtList.Add(evt);
                    }
                }
                int index = -1;
                foreach (TestPoint tp in fileMng.TestPoints)
                {
                    addValidWeakRsrpTp(fileMng, evtList, ref index, tp, NRTpHelper.NrTpManager);
                    if (weakCoverCondtion.IsAnaLte)
                    {
                        addValidWeakRsrpTp(fileMng, evtList, ref index, tp, NRTpHelper.NrLteTpManager);
                    }
                }
            }
        }

        private void addValidWeakRsrpTp(DTFileDataManager fileMng, List<Event> evtList, ref int index, TestPoint tp, NRTpManagerBase nRCond)
        {
            try
            {
                index++;
                NRRsrpRegionInfo curRegion = getCurRegion(tp);
                if (curRegion == null)
                {
                    return;
                }
                NRRsrpCellInfo curCell = getCurCell(tp, nRCond);

                if (isWeakRsrpTp(tp, nRCond))
                {
                    string reason = getResult(index, fileMng.TestPoints, evtList, nRCond);
                    if (reason == "其它")
                    {
                        string regionTag = isContainPoint(new DbPoint(tp.Longitude, tp.Latitude));
                        NRCell4Rsrp cellTag = getCellTag(tp, curCell.Cell, nRCond);
                        NRRsrpPendingPointInfo pendingInfo = new NRRsrpPendingPointInfo(tp, cellTag, regionTag);
                        tpList_Pending.Add(pendingInfo);
                        return;
                    }
                    addToRegion(reason, tp, ref curRegion);
                    addToCell(reason, tp, ref curCell);
                }
            }
            catch
            {
                //continue
            }
        }

        protected void dealPendingList()
        {
            foreach (NRRsrpPendingPointInfo tpPending in tpList_Pending)
            {
                NRRsrpRegionInfo regionInfo = regionInfoDic[tpPending.RegionTag];
                NRRsrpCellInfo cellInfo = cellInfoDic[tpPending.CellTag];

                //在同一个文件中找
                string reason = "";
                reason = getReasonFromNearestPoint(cellInfo, tpPending);
                addToRegion(reason, tpPending.Tp, ref regionInfo);
                addToCell(reason, tpPending.Tp, ref cellInfo);
            }
        }
        protected void fillCellOtherInfo()
        {
            Cursor.Current = Cursors.WaitCursor;

            foreach (NRCell4Rsrp cell in cellInfoDic.Keys)
            {
                if (cell.Name == "未知小区")
                {
                    cellInfoDic[cell].AreaName = "";
                    cellInfoDic[cell].RoadName = "";
                }
                else
                {
                    cellInfoDic[cell].AreaName = GISManager.GetInstance().GetGridDesc(cell.Longitude, cell.Latitude);
                    cellInfoDic[cell].RoadName = GISManager.GetInstance().GetRoadPlaceDesc(cell.Longitude, cell.Latitude);
                }
            }

            Cursor.Current = Cursors.Default;
        }

        private void addToRegion(string strReason, TestPoint tp, ref NRRsrpRegionInfo curRegion)
        {
            curRegion.RsrpTotal++;

            if (curRegion.ReasonDic.ContainsKey(strReason))
            {
                curRegion.ReasonDic[strReason].RsrpTotal++;
                curRegion.ReasonDic[strReason].TpList.Add(new NRRsrpReasonColorTestPoint(strReason, tp));
            }
        }
        public void addToCell(string strReason, TestPoint tp, ref NRRsrpCellInfo curCell)
        {
            curCell.RsrpTotal++;

            if (curCell.ReasonDic.ContainsKey(strReason))
            {
                curCell.ReasonDic[strReason].RsrpTotal++;
                curCell.ReasonDic[strReason].TpList.Add(new NRRsrpReasonColorTestPoint(strReason, tp));
            }

            if (curCell.FileDic.ContainsKey(tp.FileName))
            {
                int SN = curCell.FileDic[tp.FileName].Count + 1;
                curCell.FileDic[tp.FileName].Add(new NRRsrpPointInfo(SN, strReason, tp, curCell.Cell));
            }
            else
            {
                NRRsrpPointInfo fileInfo = new NRRsrpPointInfo(1, strReason, tp, curCell.Cell);
                List<NRRsrpPointInfo> tpList = new List<NRRsrpPointInfo>();
                tpList.Add(fileInfo);
                curCell.FileDic.Add(tp.FileName, tpList);
            }
        }

        protected string getResult(int curTpIndex, List<TestPoint> pointsList, List<Event> evtList, NRTpManagerBase nRCond)
        {
            TestPoint tp = pointsList[curTpIndex];
            bool hasResult = false;
            string reason = "";
            foreach (var keyValue in weakCoverCondtion.InventoryDic)
            {
                if (hasResult)
                {
                    break;
                }
                if (!keyValue.Value)
                {
                    continue;
                }
                reason = keyValue.Key;
                hasResult = judgeHasResult(curTpIndex, pointsList, tp, hasResult, reason, nRCond);
            }

            if (hasResult)
            {
                return reason;
            }
            else
            {
                return "其它";
            }
        }

        private bool judgeHasResult(int curTpIndex, List<TestPoint> pointsList, TestPoint tp, bool hasResult, string reason, NRTpManagerBase nRCond)
        {
            switch (reason)
            {
                case "缺少规划站":
                    if (isPoorBts(tp, nRCond))
                    {
                        hasResult = true;
                    }
                    break;
                case "切换不合理":
                    if (isHandoverProblem(pointsList, curTpIndex, nRCond))
                    {
                        hasResult = true;
                    }
                    break;
                case "覆盖不稳定":
                    if (isUnstabitilyCover(pointsList, curTpIndex, nRCond))
                    {
                        hasResult = true;
                    }
                    break;
                case "过覆盖":
                    if (isOverCover(pointsList, tp, nRCond))
                    {
                        hasResult = true;
                    }
                    break;
                default:
                    break;
            }

            return hasResult;
        }

        private bool isPoorBts(TestPoint tp, NRTpManagerBase nRCond)
        {
            if (nRCond is NRTpManager)
            {
                List<NRBTS> btsList = CellManager.GetInstance().GetNRBTSs(MapNRCellLayer.CurShowSnapshotTime);
                foreach (NRBTS bts in btsList)
                {
                    double dis = MathFuncs.GetDistance(bts.Longitude, bts.Latitude, tp.Longitude, tp.Latitude);
                    if (dis <= weakCoverCondtion.BtsDisGate)
                    {
                        return false;
                    }
                }
            }
            else if (nRCond is NRLTETpManager)
            {
                List<LTEBTS> btsList = CellManager.GetInstance().GetLTEBTSs(MapLTECellLayer.CurShowSnapshotTime);
                foreach (LTEBTS bts in btsList)
                {
                    double dis = MathFuncs.GetDistance(bts.Longitude, bts.Latitude, tp.Longitude, tp.Latitude);
                    if (dis <= weakCoverCondtion.BtsDisGate)
                    {
                        return false;
                    }
                }
            }
            return true;
        }

        private bool isHandoverProblem(List<TestPoint> pointsList, int curTpIndex, NRTpManagerBase nRCond)//是否切换不合理
        {
            TestPoint curTp = pointsList[curTpIndex];

            for (int i = curTpIndex - 1; i >=0; i--)
            {
                TestPoint tp = pointsList[i];
                if ((curTp.DateTime - tp.DateTime).TotalSeconds > weakCoverCondtion.MinSecondsMainLowerNear)
                {
                    break;
                }
                float? rsrp = nRCond.GetSCellRsrp(tp);
                float? maxNRsrp = nRCond.GetNCellRsrp(tp, 0);
                if (rsrp != null && maxNRsrp != null && maxNRsrp - rsrp >= weakCoverCondtion.RsrpMainLowerNear)
                {
                    return true;
                }
            }
            return false;
        }

        private bool isUnstabitilyCover(List<TestPoint> pointsList, int curIndex, NRTpManagerBase nRCond)//是否覆盖不稳定
        {
            TestPoint curTp = pointsList[curIndex];
            for (int i = curIndex - 1; i >= 0; i--)
            {
                TestPoint tp = pointsList[i];
                if ((curTp.DateTime - tp.DateTime).TotalSeconds > weakCoverCondtion.LastSecondsBeforeWeak)
                {
                    break;
                }
                float? rsrp = nRCond.GetSCellRsrp(tp);
                if (rsrp != null && rsrp < weakCoverCondtion.RsrpWeakGate)
                {
                    return false;
                }
            }
            return true;
        }

        private bool isOverCover(List<TestPoint> pointsList, TestPoint curTp, NRTpManagerBase nRCond)
        {
            if (nRCond is NRTpManager)
            {
                return judgeNrOverCover(pointsList, curTp);
            }
            else if (nRCond is NRLTETpManager)
            {
                return judgeLteOverCover(pointsList, curTp);
            }
            return false;
        }

        private bool judgeNrOverCover(List<TestPoint> pointsList, TestPoint curTp)
        {
            NRCell curCell = curTp.GetMainCell_NR();
            if (curCell == null)
            {
                return false;
            }
            if (curCell.Type == NRBTSType.Indoor || curCell.Antennas == null || curCell.Antennas.Count == 0)
            {
                return false;
            }
            double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(curCell, weakCoverCondtion.IdealCoverBtsCount);
            double rationalDistance = radiusOfCell * weakCoverCondtion.IdealCoverRadFactor;

            double distanceToCell = MathFuncs.GetDistance(curTp.Longitude, curTp.Latitude, curCell.Longitude, curCell.Latitude);
            if (distanceToCell > rationalDistance)
            {
                return true;
            }

            for (int i = 0; i < pointsList.Count; i++)
            {
                TestPoint tp = pointsList[i];
                NRCell cell = tp.GetMainCell_NR();
                if (cell != null && cell.ID == curCell.ID)
                {
                    distanceToCell = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, curCell.Longitude, curCell.Latitude);
                    if (distanceToCell > rationalDistance)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        private bool judgeLteOverCover(List<TestPoint> pointsList, TestPoint curTp)
        {
            LTECell curCell = curTp.GetMainLTECell_TdOrFdd();
            if (curCell != null)
            {
                if (curCell.Type == LTEBTSType.Indoor)
                {
                    return false;
                }
                double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(curCell, weakCoverCondtion.IdealCoverBtsCount);
                double rationalDistance = radiusOfCell * weakCoverCondtion.IdealCoverRadFactor;

                double distanceToCell = MathFuncs.GetDistance(curTp.Longitude, curTp.Latitude, curCell.Longitude, curCell.Latitude);
                if (distanceToCell > rationalDistance)
                {
                    return true;
                }

                for (int i = 0; i < pointsList.Count; i++)
                {
                    TestPoint tp = pointsList[i];
                    LTECell cell = tp.GetMainLTECell_TdOrFdd();
                    if (cell != null && cell.ID == curCell.ID)
                    {
                        distanceToCell = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, curCell.Longitude, curCell.Latitude);
                        if (distanceToCell > rationalDistance)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        private bool isWeakRsrpTp(TestPoint tp, NRTpManagerBase nRCond)
        {
            float? rsrp = nRCond.GetSCellRsrp(tp);
            if (rsrp == null || rsrp > weakCoverCondtion.RsrpWeakGate)
            {
                return false;
            }
            return true;
        }

        protected virtual void InitRegionMop2()
        {
            List<ResvRegion> resvRegions = MainModel.SearchGeometrys.SelectedResvRegions;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;

            if (resvRegions != null && resvRegions.Count > 0)  //预存区域
            {
                foreach (ResvRegion region in resvRegions)
                {
                    if (!regionMapDic.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMapDic.Add(region.RegionName, mapOp2);
                    }
                }
            }
            else if (gmt != null)//单个区域
            {
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMapDic.Add("当前区域", mapOp2);
            }
        }

        protected NRRsrpRegionInfo getCurRegion(TestPoint tp)
        {
            //获取区域名称
            string strRegionName = isContainPoint(new DbPoint(tp.Longitude, tp.Latitude));
            if (strRegionName == null)
            {
                return null;
            }

            NRRsrpRegionInfo curRegion;
            if (regionInfoDic.ContainsKey(strRegionName))
            {
                curRegion = regionInfoDic[strRegionName];
                curRegion.TpTotal++;
            }
            else
            {
                curRegion = new NRRsrpRegionInfo(strRegionName,weakCoverCondtion);
                regionInfoDic[strRegionName] = curRegion;
            }

            return curRegion;
        }
        protected NRRsrpCellInfo getCurCell(TestPoint tp, NRTpManagerBase nRCond)
        {
            NRRsrpCellInfo curCell = null;
            if (nRCond is NRTpManager)
            {
                NRCell cell = tp.GetMainCell_NR();
                NRCell4Rsrp cellTag = getCellTag(tp, cell, nRCond);
                if (cellInfoDic.ContainsKey(cellTag))
                {
                    curCell = cellInfoDic[cellTag];
                    curCell.TpTotal++;
                }
                else
                {
                    curCell = new NRRsrpCellInfo(cell, weakCoverCondtion);
                    cellInfoDic[cellTag] = curCell;
                }
            }
            else if (nRCond is NRLTETpManager)
            {
                LTECell cell = tp.GetMainLTECell_TdOrFdd();
                NRCell4Rsrp cellTag = getCellTag(tp, cell, nRCond);
                if (cellInfoDic.ContainsKey(cellTag))
                {
                    curCell = cellInfoDic[cellTag];
                    curCell.TpTotal++;
                }
                else
                {
                    curCell = new NRRsrpCellInfo(cell, weakCoverCondtion);
                    cellInfoDic[cellTag] = curCell;
                }
            }
            return curCell;
        }
        protected NRCell4Rsrp getCellTag(TestPoint tp, ICell cell, NRTpManagerBase nRCond)
        {
            NRCell4Rsrp cellTag = new NRCell4Rsrp();
            if (cell != null)
            {
                if (nRCond is NRTpManager)
                {
                    NRCell curCell = cell as NRCell;
                    cellTag.Name = curCell.Name;
                    cellTag.EARFCN = curCell.SSBARFCN;
                    cellTag.PCI = curCell.PCI;
                    cellTag.TAC = curCell.TAC;
                    cellTag.NCI = curCell.NCI;
                    cellTag.Longitude = curCell.Longitude;
                    cellTag.Latitude = curCell.Latitude;
                }
                else if(nRCond is NRLTETpManager)
                {
                    LTECell curCell = cell as LTECell;
                    cellTag.Name = curCell.Name;
                    cellTag.EARFCN = curCell.EARFCN;
                    cellTag.PCI = curCell.PCI;
                    cellTag.TAC = curCell.TAC;
                    cellTag.NCI = curCell.ECI;
                    cellTag.Longitude = curCell.Longitude;
                    cellTag.Latitude = curCell.Latitude;
                }
            }
            else
            {
                cellTag.Name = "未知小区";
                object earfcn = nRCond.GetEARFCN(tp);
                if (earfcn != null)
                {
                    cellTag.EARFCN = (int)earfcn;
                }
                object pci = nRCond.GetPCI(tp);
                if (pci != null)
                {
                    cellTag.PCI = (int)pci;
                }
                object tac = nRCond.GetTAC(tp);
                if (tac != null)
                {
                    cellTag.EARFCN = (int)tac;
                }
                object nci = nRCond.GetNCI(tp);
                if (nci != null)
                {
                    cellTag.NCI = (long)nci;
                }
                cellTag.Longitude = 0;
                cellTag.Latitude = 0;
            }
            return cellTag;
        }

        protected virtual string isContainPoint(DbPoint dPoint)
        {
            foreach (string strKey in regionMapDic.Keys)
            {
                if (regionMapDic[strKey].CheckPointInRegion(dPoint.x, dPoint.y))
                {
                    return strKey;
                }
            }
            return null;
        }
        protected string getReasonFromNearestPoint(NRRsrpCellInfo cellInfo, NRRsrpPendingPointInfo tpPending)
        {
            string strFileName = tpPending.Tp.FileName;

            if (cellInfo.FileDic.ContainsKey(strFileName))  //取出文件对应的所有点
            {
                List<NRRsrpPointInfo> fileTpList = cellInfo.FileDic[strFileName];

                NRRsrpPointInfo afterTp, beforeTp;
                getBeforeAndAfterTP(tpPending, fileTpList, out afterTp, out beforeTp);

                if (afterTp != null)
                {
                    return afterTp.Reason;
                }
                else if (beforeTp != null)
                {
                    return beforeTp.Reason;
                }
                else
                {
                    return "其它";
                }
            }
            else
            {
                return "其它";
            }
        }

        private static void getBeforeAndAfterTP(NRRsrpPendingPointInfo tpPending, List<NRRsrpPointInfo> fileTpList, 
            out NRRsrpPointInfo afterTp, out NRRsrpPointInfo beforeTp)
        {
            afterTp = null;
            beforeTp = null;
            foreach (NRRsrpPointInfo tpFile in fileTpList)
            {
                if ((tpPending.Tp.Time - tpFile.ColorTp.Tp.Time) <= 0 && (tpPending.Tp.Time - tpFile.ColorTp.Tp.Time) >= -5)   //向后看5秒内
                {
                    if (afterTp == null)
                    {
                        afterTp = tpFile;
                    }
                    else if (afterTp.ColorTp.Tp.Time > tpFile.ColorTp.Tp.Time)  //更近
                    {
                        afterTp = tpFile;
                    }
                }
                else if ((tpPending.Tp.Time - tpFile.ColorTp.Tp.Time) > 0 && (tpPending.Tp.Time - tpFile.ColorTp.Tp.Time) <= 5)  //向前看5秒内
                {
                    if (beforeTp == null)
                    {
                        beforeTp = tpFile;
                    }
                    else if (beforeTp.ColorTp.Tp.Time < tpFile.ColorTp.Tp.Time)  //更近
                    {
                        beforeTp = tpFile;
                    }
                }
            }
        }

        protected override void getResultsAfterQuery()
        {
            dealPendingList();
            fillCellOtherInfo();
        }

        protected override void fireShowForm()
        {
            if (regionInfoDic.Count == 0 && cellInfoDic.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            NRWeakCoverAnaListForm frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(NRWeakCoverAnaListForm)) as NRWeakCoverAnaListForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new NRWeakCoverAnaListForm(MainModel);
            }
            frm.FillData(regionInfoDic, cellInfoDic);
            frm.Show(MainModel.MainForm);
            frm.BringToFront();
        }

        private Comparer comparer { get; set; } = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }
    }

    public class NRRsrpRegionInfo
    {
        public string RegionName { get; set; }
        public int TpTotal { get; set; }
        public int RsrpTotal { get; set; }
        public double WeakRsrpPer
        {
            get
            {
                if (TpTotal != 0)
                {
                    return Math.Round((double)100 * RsrpTotal / TpTotal, 2);
                }
                return 0;
            }
        }
        public Dictionary<string, NRRsrpReasonInfo> ReasonDic { get; set; }

        public NRRsrpRegionInfo(string regionName, NRWeakCoverAnaCondtion weakCondtion)
        {
            this.RegionName = regionName;
            this.TpTotal = 1;
            this.RsrpTotal = 0;
            this.ReasonDic = NRRsrpReasonInfo.InitReasonDic(weakCondtion);
        }
    }

    public class NRRsrpReasonInfo
    {
        public string Reason { get; set; }
        public int RsrpTotal { get; set; }
        public List<NRRsrpReasonColorTestPoint> TpList { get; set; }

        public NRRsrpReasonInfo(string reason)
        {
            this.Reason = reason;
            this.RsrpTotal = 0;
            this.TpList = new List<NRRsrpReasonColorTestPoint>();
        }

        public static Dictionary<string, NRRsrpReasonInfo> InitReasonDic(NRWeakCoverAnaCondtion weakCoverCondtion)
        {
            Dictionary<string, NRRsrpReasonInfo> reasonDic = new Dictionary<string, NRRsrpReasonInfo>();
            if (weakCoverCondtion != null)
            {
                foreach (var keyValue in weakCoverCondtion.InventoryDic)
                {
                    if (keyValue.Value)
                    {
                        NRRsrpReasonInfo info = new NRRsrpReasonInfo(keyValue.Key);
                        reasonDic.Add(keyValue.Key, info);
                    }
                }
            }
            NRRsrpReasonInfo info2 = new NRRsrpReasonInfo("其它");
            reasonDic.Add("其它", info2);
            return reasonDic;
        }
    }

    public class NRRsrpReasonColorTestPoint
    {
        public string Reason { get; set; }
        public System.Drawing.Color Color { get; set; }
        public TestPoint Tp { get; set; }

        public NRRsrpReasonColorTestPoint(string reason, TestPoint tp)
        {
            this.Reason = reason;
            this.Tp = tp;
            setPointColor(reason);
        }

        private void setPointColor(string reason)
        {
            switch (reason)
            {
                case "弱覆盖":
                    Color = System.Drawing.Color.Olive;
                    break;
                case "切换不合理":
                    Color = System.Drawing.Color.Purple;
                    break;
                case "其它":
                    Color = System.Drawing.Color.Crimson;
                    break;
                default:
                    break;
            }
        }
    }

    public class NRRsrpCellInfo
    {
        public ICell Cell{ get; set; }
        public int TpTotal{ get; set; }
        public int RsrpTotal{ get; set; }

        public string AreaName{ get; set; }
        public string RoadName{ get; set; }
        public Dictionary<string, NRRsrpReasonInfo> ReasonDic{ get; set; }
        public Dictionary<string, List<NRRsrpPointInfo>> FileDic{ get; set; }

        public NRRsrpCellInfo(LTECell cell, NRWeakCoverAnaCondtion weakCondtion)
        {
            this.Cell = cell;
            this.TpTotal = 1;
            this.RsrpTotal = 0;
            this.ReasonDic = NRRsrpReasonInfo.InitReasonDic(weakCondtion);
            this.FileDic = new Dictionary<string, List<NRRsrpPointInfo>>();
        }

        public NRRsrpCellInfo(NRCell cell, NRWeakCoverAnaCondtion weakCondtion)
        {
            this.Cell = cell;
            this.TpTotal = 1;
            this.RsrpTotal = 0;
            this.ReasonDic = NRRsrpReasonInfo.InitReasonDic(weakCondtion);
            this.FileDic = new Dictionary<string, List<NRRsrpPointInfo>>();
        }
    }

    public class NRRsrpPointInfo
    {
        public int SN { get; set; }
        public string Reason { get; set; }
        public string TpTime { get; set; }
        public float Rsrp { get; set; }
        public float Sinr { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public NRRsrpReasonColorTestPoint ColorTp { get; set; }

        public NRRsrpPointInfo(int SN, string reason, TestPoint tp, ICell cell)
        {
            this.SN = SN;
            this.Reason = reason;
            this.TpTime = tp.DateTimeStringWithMillisecond;
            Longitude = tp.Longitude;
            Latitude = tp.Latitude;

            if (cell is NRCell)
            {
                object rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
                if (rsrp != null)
                {
                    Rsrp = (float)rsrp;
                }

                object sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);
                if (sinr != null)
                {
                    Sinr = (float)sinr;
                }
            }
            else if (cell is LTECell)
            {
                object rsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(tp);
                if (rsrp != null)
                {
                    Rsrp = (float)rsrp;
                }

                object sinr = NRTpHelper.NrLteTpManager.GetSCellSinr(tp);
                if (sinr != null)
                {
                    Sinr = (float)sinr;
                }
            }

            ColorTp = new NRRsrpReasonColorTestPoint(reason, tp);
        }
    }

    public class NRCell4Rsrp
    {
        public string Name { get; set; } = "";
        public int TAC { get; set; } = 0;
        public long NCI { get; set; } = 0;
        public int EARFCN { get; set; } = 0;
        public int PCI { get; set; } = 0;
        public double Longitude { get; set; } = 0;
        public double Latitude { get; set; } = 0;

        public override bool Equals(object obj)
        {
            NRCell4Rsrp other = obj as NRCell4Rsrp;

            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.Name.Equals(other.Name)
                    && this.EARFCN.Equals(other.EARFCN)
                    && this.PCI.Equals(other.PCI));
        }

        public override int GetHashCode()
        {
            return (this.EARFCN.ToString() + this.PCI.ToString()).GetHashCode();
        }
    }

    public class NRRsrpPendingPointInfo
    {
        public TestPoint Tp{ get; set; }
        public NRCell4Rsrp CellTag{ get; set; }
        public string RegionTag{ get; set; }

        public NRRsrpPendingPointInfo(TestPoint tp, NRCell4Rsrp cellTag, string regionTag)
        {
            this.Tp = tp;
            this.CellTag = cellTag;
            this.RegionTag = regionTag;
        }
    }
}
