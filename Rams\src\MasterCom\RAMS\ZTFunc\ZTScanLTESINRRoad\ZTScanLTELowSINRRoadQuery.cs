﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTScanLTELowSINRRoadQuery : DIYAnalyseFilesOneByOneByRegion
    {
        private int sinrThreshold = -3;
        private int distanceThreshold = 20;
        private List<LowSINRRoad> roadLst;

        public ZTScanLTELowSINRRoadQuery(MainModel mainModel)
            : base(mainModel)
        {
            FilterSampleByRegion = false;
            roadLst = new List<LowSINRRoad>();
        }

        public override string Name
        {
            get { return "低SINR路段分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23017, this.Name);
        }

        protected override bool getCondition()
        {
            ZTScanLTELowSINRRoadSetForm dlg = new ZTScanLTELowSINRRoadSetForm();
            dlg.ResetDlg(sinrThreshold, distanceThreshold);
            if (DialogResult.OK != dlg.ShowDialog())
            {
                return false;
            }
            sinrThreshold = dlg.SINRMax;
            distanceThreshold = dlg.DistanceMin;
            return true;
        }

        protected override void getReadyBeforeQuery()
        {
            roadLst = new List<LowSINRRoad>();
        }

        private bool isValid(TestPoint tp)
        {
            float sinrMax = float.MinValue;
            for (int i = 0; i < 50; i++ )
            {
                float? sinr = (float?)tp["LTESCAN_TopN_CELL_Specific_RSSINR", i];
                float s = sinr == null || sinr > 50 || sinr < -50? float.MinValue : (float)sinr;
                sinrMax = s > sinrMax ? s : sinrMax;
            }
            return sinrMax != float.MinValue && sinrMax < sinrThreshold && base.isValidTestPoint(tp);
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                List<TestPoint> testPointList = fileDataManager.TestPoints;
                List<TestPoint> tmpLst = new List<TestPoint>();
                foreach (TestPoint tp in testPointList)
                {
                    if (isValid(tp))
                    {
                        tmpLst.Add(tp);
                    }
                    else
                    {
                        dealRoad(tmpLst, fileDataManager.FileName);
                        tmpLst.Clear();
                    }
                }
                dealRoad(tmpLst, fileDataManager.FileName);
            }
        }

        private void dealRoad(List<TestPoint> tPntLst, string fileName)
        {
            double distance = 0;
            double duration = 0;
            TestPoint tpPrev = null;
            foreach (TestPoint tp in tPntLst)
            {
                if (tpPrev != null)
                {
                    distance += MathFuncs.GetDistance(tp.Longitude, tp.Latitude, tpPrev.Longitude, tpPrev.Latitude);
                    duration += tp.Time - tpPrev.Time;
                }
                tpPrev = tp;
            }
            if (distance >= distanceThreshold)
            {
                LowSINRRoad road = new LowSINRRoad(fileName, distance, duration);
                road.AddRange(tPntLst);
                roadLst.Add(road);
            }
        }

        protected override void getResultsAfterQuery()
        {
            foreach (LowSINRRoad road in roadLst)
            {
                foreach (TestPoint tp in road.TestPntLst)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
        }

        protected override void fireShowForm()
        {
            MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSSINR");

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTScanLTELowSINRRoadListForm).FullName);
            ZTScanLTELowSINRRoadListForm listForm = obj == null ? null : obj as ZTScanLTELowSINRRoadListForm;
            if (listForm == null || listForm.IsDisposed)
            {
                listForm = new ZTScanLTELowSINRRoadListForm(MainModel);
                listForm.Owner = MainModel.MainForm;
            }
            listForm.FillData(roadLst);
            listForm.Visible = true;
            listForm.BringToFront();
            roadLst = null;
        }
    }

    public class LowSINRRoad
    {
        private readonly string fileName;
        public string FileName
        {
            get { return fileName; }
        }

        private readonly double distance;
        public double Distance
        {
            get { return Math.Round(distance, 2); }
        }

        private readonly double duration;
        public double Duration
        {
            get { return duration; }
        }

        public int TestPointCnt
        {
            get { return testPointLst.Count; }
        }

        public string LongMid
        {
            get
            {
                if (TestPointCnt > 0)
                {
                    return testPointLst[TestPointCnt / 2].Longitude.ToString();
                }
                return "-";
            }
        }

        public string LatMid
        {
            get
            {
                if (TestPointCnt > 0)
                {
                    return testPointLst[TestPointCnt / 2].Latitude.ToString();
                }
                return "-";
            }
        }

        string roadName = null;
        public string RoadName
        {
            get {
                if (roadName==null)
                {
                    double lng;
                    double lat;
                    if (double.TryParse(LongMid, out lng)
                        && double.TryParse(LatMid, out lat))
                    {
                        roadName = GISManager.GetInstance().GetRoadPlaceDesc(lng, lat);
                    }
                }
                return roadName;
            }
        }

        string gridName = null;
        public string GridName
        {
            get
            {
                if (gridName == null)
                {
                    double lng;
                    double lat;
                    if (double.TryParse(LongMid, out lng)
                        && double.TryParse(LatMid, out lat))
                    {
                        gridName = GISManager.GetInstance().GetGridDesc(lng, lat);
                    }
                }
                return gridName;
            }
        }

        private readonly List<TestPoint> testPointLst;
        public List<TestPoint> TestPntLst
        {
            get { return testPointLst; }
        }

        public LowSINRRoad(string fileName, double distance, double duration)
        {
            testPointLst = new List<TestPoint>();
            this.fileName = fileName;
            this.distance = distance;
            this.duration = duration;
        }

        public void AddRange(List<TestPoint> tPntLst)
        {
            testPointLst.AddRange(tPntLst);
        }
    }
}
