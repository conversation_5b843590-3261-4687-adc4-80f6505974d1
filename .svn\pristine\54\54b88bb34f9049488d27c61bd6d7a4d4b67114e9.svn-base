﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class HandoverTooMuchConditionDialog : BaseDialog
    {
        public HandoverTooMuchConditionDialog()
        {
            InitializeComponent();
            
        }

        public void SetCondition(int timeLimit, int distanceLimitMin, int distanceLimit
                                  , int handoverCount, bool isBusiness)
        {
            numTimeLimit.Value = (decimal)timeLimit;
            numDistanceLimit.Value = (decimal)distanceLimit;
            numHandoverCount.Value = (decimal)handoverCount;
            numDistanceLimitMin.Value = (decimal)distanceLimitMin;
            chkBusiness.Checked = isBusiness;
        }

        public void GetCondition(ref int timeLimit, ref int distanceLimitMin, ref int distanceLimit
                                  , ref int handoverCount, ref bool isBusiness)
        {
            timeLimit = (int)numTimeLimit.Value;
            distanceLimit = (int)numDistanceLimit.Value;
            handoverCount = (int)numHandoverCount.Value;
            distanceLimitMin = (int)numDistanceLimitMin.Value;
            isBusiness = chkBusiness.Checked;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
