﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTMainCoverCellChangeTable;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Model.Interface
{
    /// <summary>
    /// Written by WuJunHong 2012.7.28
    /// </summary>
    public class DIYMainCovercellChangeTableQueryByRegion : DIYQueryFileInfoByRegion
    {
        protected int serviceType = 19;
        List<MainCoverInfo> mainCoverInfoList = null;
        private int discrepancyRxlev = 4;

        public DIYMainCovercellChangeTableQueryByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "主服小区变更分析"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 16000, 16005, this.Name);
        }

        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }
            fileItemLst.Clear();
            MainModel.ClearDTData();
            MainModel.MainCoverInfoList.Clear();
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            bool drawServer = MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer;
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = false;
            base.query();

            mainCoverInfoList = new List<MainCoverInfo>();

            WaitBox.CanCancel = true;
            WaitBox.Show("开始分析文件...", analyseFiles);
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = drawServer;

            fireShowForm();

            MainModel.FireSetDefaultMapSerialTheme("TDSCAN_PCCPCH_RSCP");
            MainModel.FireDTDataChanged(this);
        }

        private bool getCondition()
        {
            SetDiscrepancyRxlevThresholdForm setDiscrepancyRxlevThresholdForm = new SetDiscrepancyRxlevThresholdForm();
            if (setDiscrepancyRxlevThresholdForm.ShowDialog() == DialogResult.OK)
            {
                discrepancyRxlev = setDiscrepancyRxlevThresholdForm.DiscrepancyRxlevThreshold;
                return true;
            }
            return false;
        }

        private void analyseFiles()
        {
            try
            {
                List<FileInfo> files = new List<FileInfo>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (fileInfo.ServiceType != serviceType)
                    {
                        continue;
                    }
                    files.Add(fileInfo);  //只选用Scan_TD类型的文件
                }
                Condition.FileInfos.Clear();
                Condition.FileInfos.AddRange(files);
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                replay();
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void replay()
        {
            QueryCondition condition = new QueryCondition();
            condition.QueryType = Condition.QueryType;
            condition.FileInfos.AddRange(Condition.FileInfos);
            condition.Geometorys = Condition.Geometorys;
            DIYReplayFileWithNoWaitBox query = new DIYReplayFileWithNoWaitBox(MainModel);
            query.FilterSampleByRegion = true;
            query.IncludeEvent = false;
            query.SetQueryCondition(condition);
            query.Query();
            try
            {
                doStat();
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
        }

        private readonly List<TDScanMainCellChangeFileItem> fileItemLst = new List<TDScanMainCellChangeFileItem>();
        private void doStat()
        {
            try
            {
                MainCoverInfo mainCoverInfo = null;
                TDScanMainCellChangeCellItem subItem = null;
                int fileIndex = 0;
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    TDScanMainCellChangeFileItem fileItem = new TDScanMainCellChangeFileItem();
                    fileItem.Sn = fileItemLst.Count + 1;
                    fileItem.FileName = fileDataManager.FileName;
                    fileItemLst.Add(fileItem);
                    List<TDScanMainCellChangeCellItem> subItemLst = new List<TDScanMainCellChangeCellItem>();
                    fileItem.CellItems = subItemLst;
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    fileIndex++;
                    TDCell mainCell = null;
                    short cellFreq = 0;
                    byte cellCpi = 0;
                    
                    int firstTpWithMaincellIndex = 0;
                    float mainCellRxlev = 0;

                    for (int f = 0; f < testPointList.Count; f++)
                    {
                        TestPoint testPoint = testPointList[f];
                        if (isValidTestPoint(testPoint))
                        {
                            firstTpWithMaincellIndex = f;
                            if ((float?)testPoint["TDS_PCCPCH_RSCP", 0] != null) //选取采样点集合的头一个有有效主覆盖小区的采样点
                            {
                                break;
                            }
                        }
                        if (f == testPointList.Count - 1)
                        {
                            return;
                        }
                    }

                    bool isValidPt = false;
                    for (int i = firstTpWithMaincellIndex; i < testPointList.Count; i++)
                    {
                        isValidPt = false;
                        TestPoint testPoint = testPointList[i];
                        if (isValidTestPoint(testPoint))
                        {
                            if (i == firstTpWithMaincellIndex)
                            {
                                mainCellRxlev = (float)testPoint["TDS_PCCPCH_RSCP", 0];
                                
                                short uarfcn = (short)(int)testPoint["TDS_PCCPCH_Channel", 0];
                                byte cpi = (byte)(int)testPoint["TDS_PCCPCH_CPI", 0];
                                cellFreq = uarfcn;
                                cellCpi = cpi;
                                mainCell = CellManager.GetInstance().GetNearestTDCell(testPoint.DateTime, uarfcn, (short)cpi, testPoint.Longitude, testPoint.Latitude);
                                mainCoverInfo = new MainCoverInfo();
                                mainCoverInfo.cellId = mainCell.CI;
                                mainCoverInfo.cellname = mainCell.Name;
                                mainCoverInfo.startLocationLon = testPoint.Longitude;
                                mainCoverInfo.startLocationLati = testPoint.Latitude;
                                mainCoverInfo.tpCount = 1;
                                mainCoverInfo.uarfcn = uarfcn;
                                mainCoverInfo.cpi = cpi;

                                subItem = new TDScanMainCellChangeCellItem();
                                subItem.MainCell = mainCell;
                                subItem.preRxLev = float.MaxValue;
                                subItem.nextRxLev = float.MaxValue;
                                subItem.Sn = subItemLst.Count + 1;
                                subItem.cellId = mainCell.CI; 
                                subItem.cellname = mainCell.Name;
                                subItem.lastLongi = subItem.startLocationLon = testPoint.Longitude;
                                subItem.lastLati = subItem.startLocationLati = testPoint.Latitude;
                                subItem.uarfcn = uarfcn;
                                subItem.cpi = cpi;
                                subItem.TestPoints.Add(testPoint);
                                subItem.tpCount++;

                                mainCoverInfoList.Add(mainCoverInfo);
                            }
                            else
                            {
                                int strongestRxlevIndex = 0;
                                for (int index = 0; index < 50; index++)//扫频数据，入库时已按rxlev从大到小排序，无需再排序。这里选最强信号与前一个采样点的最强信号对比
                                {
                                    if ((float?)testPoint["TDS_PCCPCH_RSCP", index] != null)
                                    {
                                        strongestRxlevIndex = index;
                                        isValidPt = true;
                                        break;
                                    }
                                }
                                if (!isValidPt)
                                {
                                    continue;
                                }
                                float rxlevNextTp = (float)testPoint["TDS_PCCPCH_RSCP", strongestRxlevIndex];
                                short uarfcnNextTp = (short)(int)testPoint["TDS_PCCPCH_Channel", strongestRxlevIndex];
                                byte cpiNextTp = (byte)(int)testPoint["TDS_PCCPCH_CPI", strongestRxlevIndex];

                                //通过下一个采样点的频点和扰码，找出现在的主服务小区
                                for (int findmc = 0; findmc < 50; findmc++)
                                {
                                    short? mcUarfcn = (short?)(int?)testPoint["TDS_PCCPCH_Channel", findmc];
                                    byte? mcCpi = (byte?)(int?)testPoint["TDS_PCCPCH_CPI", findmc];

                                    if (cellFreq == mcUarfcn && cellCpi == mcCpi)
                                    {
                                        mainCellRxlev = (float)testPoint["TDS_PCCPCH_RSCP", findmc];
                                        break;
                                    }
                                }

                                float disRxlev = rxlevNextTp - mainCellRxlev;
                                if (disRxlev > discrepancyRxlev)  //若下一个采样点的信号强度比之前的信号强度大4dB(此门限可设置大小)，则需要转换小区
                                {
                                    mainCell = CellManager.GetInstance().GetNearestTDCell(testPoint.DateTime, uarfcnNextTp, (short)cpiNextTp, testPoint.Longitude, testPoint.Latitude);
                                    cellFreq = uarfcnNextTp;
                                    cellCpi = cpiNextTp;

                                    mainCoverInfo = new MainCoverInfo();
                                    mainCoverInfo.cellId = mainCell == null ? int.MaxValue : mainCell.CI;
                                    mainCoverInfo.cellname = mainCell == null ? uarfcnNextTp + "_" + cpiNextTp : mainCell.Name;
                                    mainCoverInfo.startLocationLon = testPoint.Longitude;
                                    mainCoverInfo.startLocationLati = testPoint.Latitude;
                                    mainCoverInfo.tpCount++;
                                    mainCoverInfo.uarfcn = uarfcnNextTp;
                                    mainCoverInfo.cpi = cpiNextTp;

                                    subItem.cvrLength += getDistance(subItem.lastLongi, subItem.lastLati, testPoint.Longitude, testPoint.Latitude);
                                    subItem.preRxLev = mainCellRxlev;
                                    subItem.nextRxLev = rxlevNextTp;
                                    subItemLst.Add(subItem);

                                    subItem = new TDScanMainCellChangeCellItem();
                                    subItem.MainCell = mainCell;
                                    subItem.Sn = subItemLst.Count + 1;
                                    subItem.cellId = mainCell == null ? int.MaxValue : mainCell.CI;
                                    subItem.cellname = mainCell == null ? uarfcnNextTp + "_" + cpiNextTp : mainCell.Name;
                                    subItem.lastLongi = subItem.startLocationLon = testPoint.Longitude;
                                    subItem.lastLati = subItem.startLocationLati = testPoint.Latitude;
                                    subItem.tpCount++;
                                    subItem.TestPoints.Add(testPoint);
                                    subItem.uarfcn = uarfcnNextTp;
                                    subItem.cpi = cpiNextTp;

                                    mainCoverInfoList.Add(mainCoverInfo);
                                    if (i == testPointList.Count - 1)  //所有文件的最后一个采样点的小主服务区自动入库
                                    {
                                        subItemLst.Add(subItem);
                                    }
                                    // subItemLst.Add(subItem);
                                }
                                else //若不需要转换小区，则累计道路长度，采样点数目
                                {
                                    mainCoverInfo.roadLenth += getDistance(testPoint.Longitude, testPoint.Latitude, testPointList[i - 1].Longitude, testPointList[i - 1].Latitude);
                                    mainCoverInfo.tpCount++;

                                    subItem.cvrLength += getDistance(testPoint.Longitude, testPoint.Latitude, subItem.lastLongi, subItem.lastLati);
                                    subItem.tpCount++;
                                    subItem.TestPoints.Add(testPoint);
                                    subItem.lastLongi = testPoint.Longitude;
                                    subItem.lastLati = testPoint.Latitude;

                                    if (i == testPointList.Count - 1)  //所有文件的最后一个采样点的小主服务区自动入库
                                    {
                                        mainCoverInfoList.Add(mainCoverInfo);
                                        subItemLst.Add(subItem);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
        }

        private double getDistance(double x, double y, double x2, double y2)
        {
            double x2Distance = (Math.Sin((90 - y2) * 2 * Math.PI / 360) + Math.Sin((90 - y) * 2 * Math.PI / 360)) / 2 * (x2 - x) / 360 * 40075360;
            double y2Distance = (y2 - y) / 360 * 39940670;
            return Math.Round(Math.Sqrt(x2Distance * x2Distance + y2Distance * y2Distance), 2);
        }

        protected virtual bool isValidTestPoint(TestPoint testPoint)
        {
            if (Condition.Geometorys != null && Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
            {
                return true;
            }
            return false;
        }

        private void fireShowForm()
        {
            MainModel.MainCoverInfoList = mainCoverInfoList;
            MainModel.TDScanMCChangeFileItemLst = this.fileItemLst;
            MainScanCoverCellChangeTableForm frm = MainModel.GetInstance().CreateResultForm(typeof(MainScanCoverCellChangeTableForm)) as MainScanCoverCellChangeTableForm;
            frm.FillData();
            frm.Visible = true;
            frm.BringToFront();
        }

        /// <summary>
        /// 同路段的信息
        /// </summary>
        public class MainCoverInfo
        {
            public int roadSn { get; set; } //路段序列号
            public int cellId { get; set; }    //小区ID
            public string cellname { get; set; }  //小区名
            public short uarfcn { get; set; }  //频点
            public byte cpi { get; set; } //扰码
            public double startLocationLon { get; set; } //变更路段起始位置经度
            public double startLocationLati { get; set; } //变更路段起始位置纬度
            public int tpCount { get; set; } //采样点数
            public double roadLenth { get; set; } //连续覆盖路段长度（m）
        }
    }

    public class TDScanMainCellChangeFileItem
    {
        public TDScanMainCellChangeFileItem()
        {
            FileName = "";
        }

        public int Sn { get; set; }
        public string FileName { get; set; }
        public List<TDScanMainCellChangeCellItem> CellItems { get; set; }
    }

    public class TDScanMainCellChangeCellItem
    {
        public TDScanMainCellChangeCellItem()
        {
            preRxLev = float.MaxValue;
            nextRxLev = float.MaxValue;
            TestPoints = new List<TestPoint>();
        }

        public int Sn { get; set; } //路段序列号
        public int cellId { get; set; }    //小区ID
        public string cellname { get; set; }  //小区名
        public short uarfcn { get; set; }  //频点
        public byte cpi { get; set; } //扰码
        public double startLocationLon { get; set; } //变更路段起始位置经度
        public double startLocationLati { get; set; } //变更路段起始位置纬度
        public double lastLongi { get; set; }
        public double lastLati { get; set; }
        public int tpCount { get; set; } //采样点数
        public double cvrLength { get; set; } //连续覆盖路段长度（m）
        public float preRxLev { get; set; }
        public float nextRxLev { get; set; }
        public TDCell MainCell { get; set; }
        public List<TestPoint> TestPoints { get; set; }
    }

    public class DIYMainCovercellChangeTableQueryByRegion_W : DIYQueryFileInfoByRegion
    {
        protected int serviceType = 21;
        List<MainCoverInfo> mainCoverInfoList = null;
        private int discrepancyRxlev = 4;

        public DIYMainCovercellChangeTableQueryByRegion_W(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "WCDMA主服小区变更分析"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        //protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        //{
        //    return new MasterCom.RAMS.UserMng.LogInfoItem(2, 16000, 16005, this.Name);
        //}

        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }
            fileItemLst.Clear();
            MainModel.ClearDTData();
            MainModel.MainCoverInfoList.Clear();
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            bool drawServer = MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer;
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = false;
            base.query();

            mainCoverInfoList = new List<MainCoverInfo>();

            WaitBox.CanCancel = true;
            WaitBox.Show("开始分析文件...", analyseFiles);
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = drawServer;

            fireShowForm();

            MainModel.FireSetDefaultMapSerialTheme("WS_CPICHTotalRSCP");
            MainModel.FireDTDataChanged(this);
        }

        private bool getCondition()
        {
            SetDiscrepancyRxlevThresholdForm setDiscrepancyRxlevThresholdForm = new SetDiscrepancyRxlevThresholdForm();
            if (setDiscrepancyRxlevThresholdForm.ShowDialog() == DialogResult.OK)
            {
                discrepancyRxlev = setDiscrepancyRxlevThresholdForm.DiscrepancyRxlevThreshold;
                return true;
            }
            return false;
        }

        private void analyseFiles()
        {
            try
            {
                List<FileInfo> files = new List<FileInfo>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (fileInfo.ServiceType != serviceType)
                    {
                        continue;
                    }
                    files.Add(fileInfo);  //只选用Scan_TD类型的文件
                }
                if (files.Count > 0)
                {
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.AddRange(files);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    replay();
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void replay()
        {
            QueryCondition condition = new QueryCondition();
            condition.QueryType = Condition.QueryType;
            condition.FileInfos.AddRange(Condition.FileInfos);
            condition.Geometorys = Condition.Geometorys;
            DIYReplayFileWithNoWaitBox query = new DIYReplayFileWithNoWaitBox(MainModel);
            query.FilterSampleByRegion = true;
            query.IncludeEvent = false;
            query.SetQueryCondition(condition);
            query.Query();
            try
            {
                doStat();
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
        }

        private readonly List<WScanMainCellChangeFileItem> fileItemLst = new List<WScanMainCellChangeFileItem>();
        private void doStat()
        {
            try
            {
                MainCoverInfo mainCoverInfo = null;
                WScanMainCellChangeCellItem subItem = null;
                int fileIndex = 0;
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    WScanMainCellChangeFileItem fileItem = new WScanMainCellChangeFileItem();
                    fileItem.Sn = fileItemLst.Count + 1;
                    fileItem.FileName = fileDataManager.FileName;
                    fileItemLst.Add(fileItem);
                    List<WScanMainCellChangeCellItem> subItemLst = new List<WScanMainCellChangeCellItem>();
                    fileItem.CellItems = subItemLst;
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    fileIndex++;
                    WCell mainCell = null;
                    short cellFreq = 0;
                    short cellCpi = 0;
                    
                    int firstTpWithMaincellIndex = 0;
                    float mainCellRxlev = 0;

                    for (int f = 0; f < testPointList.Count; f++)
                    {
                        TestPoint testPoint = testPointList[f];
                        if (isValidTestPoint(testPoint))
                        {
                            firstTpWithMaincellIndex = f;
                            if ((float?)testPoint["WS_CPICHTotalRSCP", 0] != null) //选取采样点集合的头一个有有效主覆盖小区的采样点
                            {
                                break;
                            }
                        }
                        if (f == testPointList.Count - 1)
                        {
                            return;
                        }
                    }

                    bool isValidPt = false;
                    for (int i = firstTpWithMaincellIndex; i < testPointList.Count; i++)
                    {
                        isValidPt = false;
                        TestPoint testPoint = testPointList[i];
                        if (isValidTestPoint(testPoint))
                        {
                            if (i == firstTpWithMaincellIndex)
                            {
                                mainCellRxlev = (float)testPoint["WS_CPICHTotalRSCP", 0];
                                
                                short? uarfcn = testPoint["WS_CPICHChannel", 0] as short?;
                                short? cpi = testPoint["WS_CPICHPilot", 0] as short?;
                                cellFreq = (short)uarfcn;
                                cellCpi = (short)cpi;
                                mainCell = CellManager.GetInstance().GetNearestWCell(testPoint.DateTime, (short)uarfcn, (short)cpi, testPoint.Longitude, testPoint.Latitude);
                                mainCoverInfo = new MainCoverInfo();
                                mainCoverInfo.cellId = mainCell.CI;
                                mainCoverInfo.cellname = mainCell.Name;
                                mainCoverInfo.startLocationLon = testPoint.Longitude;
                                mainCoverInfo.startLocationLati = testPoint.Latitude;
                                mainCoverInfo.tpCount = 1;
                                mainCoverInfo.uarfcn = (short)uarfcn;
                                mainCoverInfo.cpi = (short)cpi;

                                subItem = new WScanMainCellChangeCellItem();
                                subItem.MainCell = mainCell;
                                subItem.preRxLev = float.MaxValue;
                                subItem.nextRxLev = float.MaxValue;
                                subItem.Sn = subItemLst.Count + 1;
                                subItem.cellId = mainCell.CI;
                                subItem.cellname = mainCell.Name;
                                subItem.lastLongi = subItem.startLocationLon = testPoint.Longitude;
                                subItem.lastLati = subItem.startLocationLati = testPoint.Latitude;
                                subItem.uarfcn = (short)uarfcn;
                                subItem.cpi = (short)cpi;
                                subItem.TestPoints.Add(testPoint);
                                subItem.tpCount++;

                                mainCoverInfoList.Add(mainCoverInfo);
                            }
                            else
                            {
                                int strongestRxlevIndex = 0;
                                for (int index = 0; index < 50; index++)//扫频数据，入库时已按rxlev从大到小排序，无需再排序。这里选最强信号与前一个采样点的最强信号对比
                                {
                                    if ((float?)testPoint["TDS_PCCPCH_RSCP", index] != null)
                                    {
                                        strongestRxlevIndex = index;
                                        isValidPt = true;
                                        break;
                                    }
                                }
                                if (!isValidPt)
                                {
                                    continue;
                                }
                                float rxlevNextTp = (float)testPoint["WS_CPICHTotalRSCP", strongestRxlevIndex];
                                short? uarfcnNextTp = testPoint["WS_CPICHChannel", strongestRxlevIndex] as short?;
                                short? cpiNextTp = testPoint["WS_CPICHPilot", strongestRxlevIndex] as short?;

                                //通过下一个采样点的频点和扰码，找出现在的主服务小区
                                for (int findmc = 0; findmc < 50; findmc++)
                                {
                                    short? mcUarfcn = testPoint["WS_CPICHChannel", findmc] as short?;
                                    short? mcCpi = testPoint["WS_CPICHPilot", findmc] as short?;

                                    if (cellFreq == mcUarfcn && cellCpi == mcCpi)
                                    {
                                        mainCellRxlev = (float)testPoint["WS_CPICHTotalRSCP", findmc];
                                        break;
                                    }
                                }

                                float disRxlev = rxlevNextTp - mainCellRxlev;
                                if (disRxlev > discrepancyRxlev)  //若下一个采样点的信号强度比之前的信号强度大4dB(此门限可设置大小)，则需要转换小区
                                {
                                    mainCell = CellManager.GetInstance().GetNearestWCell(testPoint.DateTime, (short)uarfcnNextTp, (short)cpiNextTp, testPoint.Longitude, testPoint.Latitude);
                                    cellFreq = (short)uarfcnNextTp;
                                    cellCpi = (byte)cpiNextTp;

                                    mainCoverInfo = new MainCoverInfo();
                                    mainCoverInfo.cellId = mainCell == null ? int.MaxValue : mainCell.CI;
                                    mainCoverInfo.cellname = mainCell == null ? uarfcnNextTp + "_" + cpiNextTp : mainCell.Name;
                                    mainCoverInfo.startLocationLon = testPoint.Longitude;
                                    mainCoverInfo.startLocationLati = testPoint.Latitude;
                                    mainCoverInfo.tpCount++;
                                    mainCoverInfo.uarfcn = (short)uarfcnNextTp;
                                    mainCoverInfo.cpi = (short)cpiNextTp;

                                    subItem.cvrLength += getDistance(subItem.lastLongi, subItem.lastLati, testPoint.Longitude, testPoint.Latitude);
                                    subItem.preRxLev = mainCellRxlev;
                                    subItem.nextRxLev = rxlevNextTp;
                                    subItemLst.Add(subItem);

                                    subItem = new WScanMainCellChangeCellItem();
                                    subItem.MainCell = mainCell;
                                    subItem.Sn = subItemLst.Count + 1;
                                    subItem.cellId = mainCell == null ? int.MaxValue : mainCell.CI;
                                    subItem.cellname = mainCell == null ? uarfcnNextTp + "_" + cpiNextTp : mainCell.Name;
                                    subItem.lastLongi = subItem.startLocationLon = testPoint.Longitude;
                                    subItem.lastLati = subItem.startLocationLati = testPoint.Latitude;
                                    subItem.tpCount++;
                                    subItem.TestPoints.Add(testPoint);
                                    subItem.uarfcn = (short)uarfcnNextTp;
                                    subItem.cpi = (short)cpiNextTp;

                                    mainCoverInfoList.Add(mainCoverInfo);
                                    if (i == testPointList.Count - 1)  //所有文件的最后一个采样点的小主服务区自动入库
                                    {
                                        subItemLst.Add(subItem);
                                    }
                                    // subItemLst.Add(subItem);
                                }
                                else //若不需要转换小区，则累计道路长度，采样点数目
                                {
                                    mainCoverInfo.roadLenth += getDistance(testPoint.Longitude, testPoint.Latitude, testPointList[i - 1].Longitude, testPointList[i - 1].Latitude);
                                    mainCoverInfo.tpCount++;

                                    subItem.cvrLength += getDistance(testPoint.Longitude, testPoint.Latitude, subItem.lastLongi, subItem.lastLati);
                                    subItem.tpCount++;
                                    subItem.TestPoints.Add(testPoint);
                                    subItem.lastLongi = testPoint.Longitude;
                                    subItem.lastLati = testPoint.Latitude;

                                    if (i == testPointList.Count - 1)  //所有文件的最后一个采样点的小主服务区自动入库
                                    {
                                        mainCoverInfoList.Add(mainCoverInfo);
                                        subItemLst.Add(subItem);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
        }

        private double getDistance(double x, double y, double x2, double y2)
        {
            double x2Distance = (Math.Sin((90 - y2) * 2 * Math.PI / 360) + Math.Sin((90 - y) * 2 * Math.PI / 360)) / 2 * (x2 - x) / 360 * 40075360;
            double y2Distance = (y2 - y) / 360 * 39940670;
            return Math.Round(Math.Sqrt(x2Distance * x2Distance + y2Distance * y2Distance), 2);
        }

        protected virtual bool isValidTestPoint(TestPoint testPoint)
        {
            if (Condition.Geometorys != null && Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
            {
                return true;
            }
            return false;
        }

        private void fireShowForm()
        {
            MainModel.WScanMCChangeFileItemLst = this.fileItemLst;
            MainScanCoverCellChangeTableForm_W frm = MainModel.GetInstance().CreateResultForm(typeof(MainScanCoverCellChangeTableForm_W)) as MainScanCoverCellChangeTableForm_W;
            frm.FillData();
            frm.Visible = true;
            frm.BringToFront();
        }

        /// <summary>
        /// 同路段的信息
        /// </summary>
        public class MainCoverInfo
        {
            public int roadSn{ get; set; } //路段序列号
            public int cellId{ get; set; }    //小区ID
            public string cellname{ get; set; }  //小区名
            public short uarfcn{ get; set; }  //频点
            public short cpi{ get; set; } //扰码
            public double startLocationLon{ get; set; } //变更路段起始位置经度
            public double startLocationLati{ get; set; } //变更路段起始位置纬度
            public int tpCount { get; set; } //采样点数
            public double roadLenth { get; set; } //连续覆盖路段长度（m）
        }
    }

    public class WScanMainCellChangeFileItem
    {
        public WScanMainCellChangeFileItem()
        {
            FileName = "";
        }

        public int Sn { get; set; }
        public string FileName { get; set; }
        public List<WScanMainCellChangeCellItem> CellItems { get; set; }
    }

    public class WScanMainCellChangeCellItem
    {
        public WScanMainCellChangeCellItem()
        {
            preRxLev = float.MaxValue;
            nextRxLev = float.MaxValue;
            TestPoints = new List<TestPoint>();
        }

        public int Sn { get; set; } //路段序列号
        public int cellId { get; set; }    //小区ID
        public string cellname { get; set; }  //小区名
        public short uarfcn { get; set; }  //频点
        public short cpi { get; set; } //扰码
        public double startLocationLon { get; set; } //变更路段起始位置经度
        public double startLocationLati { get; set; } //变更路段起始位置纬度
        public double lastLongi { get; set; }
        public double lastLati { get; set; }
        public int tpCount { get; set; } //采样点数
        public double cvrLength { get; set; }  //连续覆盖路段长度（m）
        public float preRxLev { get; set; }
        public float nextRxLev { get; set; }
        public WCell MainCell { get; set; }
        public List<TestPoint> TestPoints { get; set; }
    }

}
