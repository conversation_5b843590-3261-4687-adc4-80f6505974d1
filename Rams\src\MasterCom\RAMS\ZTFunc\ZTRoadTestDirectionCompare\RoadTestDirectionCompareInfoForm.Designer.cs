﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class RoadTestDirectionCompareInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportDetailExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportSumExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemMapLayer = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemShowAllLayer = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemExportShp = new System.Windows.Forms.ToolStripMenuItem();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.colorUncovered = new DevExpress.XtraEditors.ColorEdit();
            this.colorCovered = new DevExpress.XtraEditors.ColorEdit();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.ListViewRoadCompare = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnGridName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadLeval = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadId = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTestDirection = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnIsSameDirection = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ListViewTestRandomInfo = new BrightIdeasSoftware.TreeListView();
            this.olvColumnRandomInfoSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRandomInfoDes = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRandomInfoPer = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorUncovered.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCovered.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewRoadCompare)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewTestRandomInfo)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExpandAll,
            this.ToolStripMenuItemCallapsAll,
            this.miExportDetailExcel,
            this.miExportSumExcel,
            this.ToolStripMenuItemMapLayer,
            this.ToolStripMenuItemExportShp});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(226, 136);
            // 
            // ToolStripMenuItemExpandAll
            // 
            this.ToolStripMenuItemExpandAll.Name = "ToolStripMenuItemExpandAll";
            this.ToolStripMenuItemExpandAll.Size = new System.Drawing.Size(225, 22);
            this.ToolStripMenuItemExpandAll.Text = "全部展开";
            this.ToolStripMenuItemExpandAll.Click += new System.EventHandler(this.ToolStripMenuItemExpandAll_Click);
            // 
            // ToolStripMenuItemCallapsAll
            // 
            this.ToolStripMenuItemCallapsAll.Name = "ToolStripMenuItemCallapsAll";
            this.ToolStripMenuItemCallapsAll.Size = new System.Drawing.Size(225, 22);
            this.ToolStripMenuItemCallapsAll.Text = "全部合并";
            this.ToolStripMenuItemCallapsAll.Click += new System.EventHandler(this.ToolStripMenuItemCallapsAll_Click);
            // 
            // miExportDetailExcel
            // 
            this.miExportDetailExcel.Name = "miExportDetailExcel";
            this.miExportDetailExcel.Size = new System.Drawing.Size(225, 22);
            this.miExportDetailExcel.Text = "导出详情到Excel";
            this.miExportDetailExcel.Click += new System.EventHandler(this.miExportDetailExcel_Click);
            // 
            // miExportSumExcel
            // 
            this.miExportSumExcel.Name = "miExportSumExcel";
            this.miExportSumExcel.Size = new System.Drawing.Size(225, 22);
            this.miExportSumExcel.Text = "导出测试随机性信息到Excel";
            this.miExportSumExcel.Click += new System.EventHandler(this.miExportSumExcel_Click);
            // 
            // ToolStripMenuItemMapLayer
            // 
            this.ToolStripMenuItemMapLayer.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemShowAllLayer});
            this.ToolStripMenuItemMapLayer.Name = "ToolStripMenuItemMapLayer";
            this.ToolStripMenuItemMapLayer.Size = new System.Drawing.Size(225, 22);
            this.ToolStripMenuItemMapLayer.Text = "图层显示";
            // 
            // ToolStripMenuItemShowAllLayer
            // 
            this.ToolStripMenuItemShowAllLayer.Name = "ToolStripMenuItemShowAllLayer";
            this.ToolStripMenuItemShowAllLayer.Size = new System.Drawing.Size(172, 22);
            this.ToolStripMenuItemShowAllLayer.Text = "显示全部时段图层";
            // 
            // ToolStripMenuItemExportShp
            // 
            this.ToolStripMenuItemExportShp.Name = "ToolStripMenuItemExportShp";
            this.ToolStripMenuItemExportShp.Size = new System.Drawing.Size(225, 22);
            this.ToolStripMenuItemExportShp.Text = "导出当前图层";
            this.ToolStripMenuItemExportShp.Click += new System.EventHandler(this.ToolStripMenuItemExportShp_Click);
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003;
            this.splitContainerControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.colorUncovered);
            this.splitContainerControl1.Panel1.Controls.Add(this.colorCovered);
            this.splitContainerControl1.Panel1.Controls.Add(this.label3);
            this.splitContainerControl1.Panel1.Controls.Add(this.label2);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.splitContainerControl2);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1008, 482);
            this.splitContainerControl1.SplitterPosition = 39;
            this.splitContainerControl1.TabIndex = 8;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // colorUncovered
            // 
            this.colorUncovered.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.colorUncovered.EditValue = System.Drawing.Color.Red;
            this.colorUncovered.Location = new System.Drawing.Point(903, 11);
            this.colorUncovered.Name = "colorUncovered";
            this.colorUncovered.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorUncovered.Size = new System.Drawing.Size(47, 21);
            this.colorUncovered.TabIndex = 8;
            this.colorUncovered.EditValueChanged += new System.EventHandler(this.colorUncovered_EditValueChanged);
            // 
            // colorCovered
            // 
            this.colorCovered.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.colorCovered.EditValue = System.Drawing.Color.Blue;
            this.colorCovered.Location = new System.Drawing.Point(704, 11);
            this.colorCovered.Name = "colorCovered";
            this.colorCovered.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorCovered.Size = new System.Drawing.Size(47, 21);
            this.colorCovered.TabIndex = 9;
            this.colorCovered.EditValueChanged += new System.EventHandler(this.colorCovered_EditValueChanged);
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(770, 14);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(127, 14);
            this.label3.TabIndex = 6;
            this.label3.Text = "方向不一致道路颜色：";
            // 
            // label2
            // 
            this.label2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(583, 14);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(115, 14);
            this.label2.TabIndex = 7;
            this.label2.Text = "方向一致道路颜色：";
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.Horizontal = false;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.ListViewRoadCompare);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.ListViewTestRandomInfo);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.splitContainerControl2.Size = new System.Drawing.Size(1008, 439);
            this.splitContainerControl2.SplitterPosition = 353;
            this.splitContainerControl2.TabIndex = 9;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // ListViewRoadCompare
            // 
            this.ListViewRoadCompare.AllColumns.Add(this.olvColumnSN);
            this.ListViewRoadCompare.AllColumns.Add(this.olvColumnGridName);
            this.ListViewRoadCompare.AllColumns.Add(this.olvColumnRoadLeval);
            this.ListViewRoadCompare.AllColumns.Add(this.olvColumnRoadName);
            this.ListViewRoadCompare.AllColumns.Add(this.olvColumnRoadId);
            this.ListViewRoadCompare.AllColumns.Add(this.olvColumnRoadDistance);
            this.ListViewRoadCompare.AllColumns.Add(this.olvColumnTime);
            this.ListViewRoadCompare.AllColumns.Add(this.olvColumnTestDirection);
            this.ListViewRoadCompare.AllColumns.Add(this.olvColumnIsSameDirection);
            this.ListViewRoadCompare.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnGridName,
            this.olvColumnRoadLeval,
            this.olvColumnRoadName,
            this.olvColumnRoadId,
            this.olvColumnRoadDistance,
            this.olvColumnTime,
            this.olvColumnTestDirection,
            this.olvColumnIsSameDirection});
            this.ListViewRoadCompare.ContextMenuStrip = this.contextMenuStrip;
            this.ListViewRoadCompare.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewRoadCompare.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewRoadCompare.FullRowSelect = true;
            this.ListViewRoadCompare.GridLines = true;
            this.ListViewRoadCompare.HeaderWordWrap = true;
            this.ListViewRoadCompare.IsNeedShowOverlay = false;
            this.ListViewRoadCompare.Location = new System.Drawing.Point(0, 0);
            this.ListViewRoadCompare.Name = "ListViewRoadCompare";
            this.ListViewRoadCompare.OwnerDraw = true;
            this.ListViewRoadCompare.ShowGroups = false;
            this.ListViewRoadCompare.Size = new System.Drawing.Size(1008, 353);
            this.ListViewRoadCompare.TabIndex = 8;
            this.ListViewRoadCompare.UseCompatibleStateImageBehavior = false;
            this.ListViewRoadCompare.View = System.Windows.Forms.View.Details;
            this.ListViewRoadCompare.VirtualMode = true;
            this.ListViewRoadCompare.DoubleClick += new System.EventHandler(this.ListViewRoadCompare_DoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnGridName
            // 
            this.olvColumnGridName.HeaderFont = null;
            this.olvColumnGridName.Text = "道路网格";
            this.olvColumnGridName.Width = 80;
            // 
            // olvColumnRoadLeval
            // 
            this.olvColumnRoadLeval.HeaderFont = null;
            this.olvColumnRoadLeval.Text = "道路级别";
            this.olvColumnRoadLeval.Width = 80;
            // 
            // olvColumnRoadName
            // 
            this.olvColumnRoadName.HeaderFont = null;
            this.olvColumnRoadName.Text = "道路名称";
            this.olvColumnRoadName.Width = 120;
            // 
            // olvColumnRoadId
            // 
            this.olvColumnRoadId.HeaderFont = null;
            this.olvColumnRoadId.Text = "道路ID";
            // 
            // olvColumnRoadDistance
            // 
            this.olvColumnRoadDistance.HeaderFont = null;
            this.olvColumnRoadDistance.Text = "道路总距离（米）";
            this.olvColumnRoadDistance.Width = 120;
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "测试时段";
            this.olvColumnTime.Width = 100;
            // 
            // olvColumnTestDirection
            // 
            this.olvColumnTestDirection.HeaderFont = null;
            this.olvColumnTestDirection.Text = "测试主方向";
            this.olvColumnTestDirection.Width = 120;
            // 
            // olvColumnIsSameDirection
            // 
            this.olvColumnIsSameDirection.HeaderFont = null;
            this.olvColumnIsSameDirection.Text = "方向是否一致";
            this.olvColumnIsSameDirection.Width = 200;
            // 
            // ListViewTestRandomInfo
            // 
            this.ListViewTestRandomInfo.AllColumns.Add(this.olvColumnRandomInfoSN);
            this.ListViewTestRandomInfo.AllColumns.Add(this.olvColumnRandomInfoDes);
            this.ListViewTestRandomInfo.AllColumns.Add(this.olvColumnRandomInfoPer);
            this.ListViewTestRandomInfo.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnRandomInfoSN,
            this.olvColumnRandomInfoDes,
            this.olvColumnRandomInfoPer});
            this.ListViewTestRandomInfo.ContextMenuStrip = this.contextMenuStrip;
            this.ListViewTestRandomInfo.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewTestRandomInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewTestRandomInfo.FullRowSelect = true;
            this.ListViewTestRandomInfo.GridLines = true;
            this.ListViewTestRandomInfo.HeaderWordWrap = true;
            this.ListViewTestRandomInfo.IsNeedShowOverlay = false;
            this.ListViewTestRandomInfo.Location = new System.Drawing.Point(0, 0);
            this.ListViewTestRandomInfo.Name = "ListViewTestRandomInfo";
            this.ListViewTestRandomInfo.OwnerDraw = true;
            this.ListViewTestRandomInfo.ShowGroups = false;
            this.ListViewTestRandomInfo.Size = new System.Drawing.Size(1008, 80);
            this.ListViewTestRandomInfo.TabIndex = 9;
            this.ListViewTestRandomInfo.UseCompatibleStateImageBehavior = false;
            this.ListViewTestRandomInfo.View = System.Windows.Forms.View.Details;
            this.ListViewTestRandomInfo.VirtualMode = true;
            // 
            // olvColumnRandomInfoSN
            // 
            this.olvColumnRandomInfoSN.AspectName = "";
            this.olvColumnRandomInfoSN.HeaderFont = null;
            this.olvColumnRandomInfoSN.Text = "序号";
            // 
            // olvColumnRandomInfoDes
            // 
            this.olvColumnRandomInfoDes.HeaderFont = null;
            this.olvColumnRandomInfoDes.Text = "对比时段";
            this.olvColumnRandomInfoDes.Width = 200;
            // 
            // olvColumnRandomInfoPer
            // 
            this.olvColumnRandomInfoPer.HeaderFont = null;
            this.olvColumnRandomInfoPer.Text = "测试随机性(%)";
            this.olvColumnRandomInfoPer.Width = 120;
            // 
            // RoadTestDirectionCompareInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.ClientSize = new System.Drawing.Size(1008, 482);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "RoadTestDirectionCompareInfoForm";
            this.Text = "多时段道路测试方向对比信息";
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.colorUncovered.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCovered.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewRoadCompare)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewTestRandomInfo)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportDetailExcel;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private BrightIdeasSoftware.TreeListView ListViewRoadCompare;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnGridName;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadLeval;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadId;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadName;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnTestDirection;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private BrightIdeasSoftware.OLVColumn olvColumnIsSameDirection;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExpandAll;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemCallapsAll;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemMapLayer;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemShowAllLayer;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExportShp;
        private DevExpress.XtraEditors.ColorEdit colorUncovered;
        private DevExpress.XtraEditors.ColorEdit colorCovered;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private BrightIdeasSoftware.TreeListView ListViewTestRandomInfo;
        private BrightIdeasSoftware.OLVColumn olvColumnRandomInfoSN;
        private BrightIdeasSoftware.OLVColumn olvColumnRandomInfoDes;
        private BrightIdeasSoftware.OLVColumn olvColumnRandomInfoPer;
        private System.Windows.Forms.ToolStripMenuItem miExportSumExcel;




    }
}