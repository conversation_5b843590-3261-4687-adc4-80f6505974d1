﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRHandOverArfcnBase : NRHandOverAnaBase
    {
        List<HandOverDirectionInfo> handDirectionInfoList = new List<HandOverDirectionInfo>();
        public NRHandOverArfcnBase(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = true;
            this.IncludeMessage = false;
        }

        protected void init(bool isVoNR)
        {
            valuedEvtId = new List<int> { 9296, 9297, 9299, 9300 };
            ServiceTypes.Clear();
            if (isVoNR)
            {
                ServiceTypes.Add(ServiceType.NR_SA_TDD_VONR);
            }
            else
            {
                ServiceTypes.Add(ServiceType.NR_SA_TDD_IDLE);
                ServiceTypes.Add(ServiceType.NR_SA_TDD_DATA);
                ServiceTypes.Add(ServiceType.NR_SA_TDD_VOLTE);
                ServiceTypes.Add(ServiceType.SER_NR_SA_TDD_EPSFB);
                ServiceTypes.Add(ServiceType.SER_NR_SA_TDD_MULTI);
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22069, this.Name);
        }

        protected override void getResultsAfterQuery()
        {
            var dicInfo = new Dictionary<string, HandOverDirectionInfo>();
            foreach (NRHandOverAnaItem item in resultList)
            {
                if (!dicInfo.ContainsKey(item.HnadOverDirection))
                {
                    dicInfo[item.HnadOverDirection] = new HandOverDirectionInfo(item.HnadOverDirection);
                }

                if (item.HandOverResult == "成功")
                {
                    dicInfo[item.HnadOverDirection].HnadOverSuccessTimes++;
                    dicInfo[item.HnadOverDirection].HnadOverTotalTimes++;
                }
                else if (item.HandOverResult == "失败")
                {
                    dicInfo[item.HnadOverDirection].HnadOverFailTimes++;
                    dicInfo[item.HnadOverDirection].HnadOverTotalTimes++;
                }
            }
            handDirectionInfoList = new List<HandOverDirectionInfo>(dicInfo.Values);
        }

        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            NRHandOverArfcnListForm frm = MainModel.CreateResultForm(typeof(NRHandOverArfcnListForm)) as NRHandOverArfcnListForm;
            frm.FillData(handDirectionInfoList, resultList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }
}
