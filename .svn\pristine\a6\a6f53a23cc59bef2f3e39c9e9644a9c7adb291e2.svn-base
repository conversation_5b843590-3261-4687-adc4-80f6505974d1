<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="labelBSIC.GenerateMember" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="labelBCCH.GenerateMember" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAIAICAAAAEAIACoEAAAJgAAABAQAAABACAAaAQAAM4QAAAoAAAAIAAAAEAAAAABACAAAAAAAIAQ
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA
        AAgAAAAVAAAAIgAAACsAAAAvAAAAMAAAADAAAAAvAAAAKwAAACIAAAAVAAAACAAAAAEAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAA
        AA0AAAAjAAAAPQAAAFoAAAB0AAAAhgAAAI4AAACPAAAAjwAAAI4AAACGAAAAdAAAAFoAAAA9AAAAIwAA
        AA0AAAACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA
        AAgAAAAfAAAARScAAYNcAQLBgwEC6LxiY/qrISP/nwED/58BA/+fAQP/lwED+4ABAu5UAQLTHQABsAAA
        AJEAAABxAAAARQAAAB4AAAAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAEAAAANAAAAMDsAAYGFAQPknwED/58BA/+fAQP/57Wg//PAgP+lHBL/nwED/58BA/+fAQP/nwED/58B
        A/+fAQP/gAEC7igAAb4AAACSAAAAYgAAAC0AAAALAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAABAAAADSUAAUR1AQLDnwED/58BA/+fAQP/nwED/58BA/+3OCr//9OA///hqP/bmpH/qyEj/58B
        A/+fAQP/nwED/58BA/+fAQP/nwED/2UBAuEAAACkAAAAdAAAADYAAAANAAAAAQAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAQAAAA0lAAFEjwED5p8BA/+fAQP/nwED/58BA/+fAQP/nwED/58BA//hklz//9WG///V
        hP//5LH/57KY/6shI/+fAQP/nwED/58BA/+fAQP/nwED/4gBA/MOAACwAAAAeQAAADcAAAANAAAAAQAA
        AAAAAAAAAAAAAAAAAAAAAAAIKQABPZABA+WfAQP/nwED/58BA/+fAQP/nwED/58BA/+fAQP/nwED/8ls
        Zf//36P//96g///cmP//14z/+c+S/6snGv+fAQP/nwED/58BA/+fAQP/nwED/4gBA/MOAACwAAAAdgAA
        ADAAAAAIAAAAAAAAAAAAAAAAAAAAAjkAAS2SAQPinwED/58BA/+fAQP/nwED/58BA/+fAQP/nwED/58B
        A/+xMTL/+evk///pvv//6Lv//+Wz///hp///25b/7bZ0/58MAv+fAQP/nwED/58BA/+fAQP/nwED/4gB
        A/MPAACqAAAAZAAAAB8AAAACAAAAAAAAAAAAAAANgwECrp8BA/+fAQP/nwED/58BA/+fAQP/nwED/58B
        A/+fAQP/sTEy//nr5P//8db///DU///v0f//7cn//+i9///irP//25f/z3pD/58DA/+fAQP/nwED/58B
        A/+fAQP/nwED/2UBAuEAAACSAAAARQAAAA0AAAAAAAAAAWQBAkyfAQP/nwED/6ACBP+hBQb/oggH/6MK
        Cf+kCgn/owoJ/6IIB//hsLH///Xh///25P//9ub///Xj///z2///78///+m+///hqP/5zYb/sTgh/58B
        A/+fAQP/nwED/58BA/+fAQP/nwED/ygAAb4AAABxAAAAIwAAAAEAAAAIkwIEz6IGBv+lDgv/qRUQ/6sb
        E/+uIBb/ryMX/68jGP+vIxj/5ri0///68P//+Ov///rx///78v//+u////fo///z3P//7cv//+a2///d
        nP/5z4v/sTcq/58BA/+fAQP/nwED/58BA/+fAQP/gAEC7gAAAJEAAAA9AAAACGkKCDKmEA3/rBwT/7Am
        Gf+0Lh7/tzQi/7k5Jf+6PCb/uz0n/7o8J//lsZ3///fp///78////fn///77///8+P//+vD///bk///w
        1P//6b7//+Ck///Wh//np2D/nw4C/58BA/+fAQP/nwED/58BA/+fAQP/HQABsAAAAFoAAAAVkxoSg7En
        Gv+2MyH/uz0n/79GLP/CTTD/xVMz/8ZWNf/HVzb/xlY2/9B0Wv/87+P///z3///+/f////7///78///7
        9P//9+j///LY///qwv/nsn//w1g2/7c2Iv+fBQP/nwED/58BA/+fAQP/vVBS/+GwsP9qOjrTAAAAdAAA
        ACKrLh7Kuz0n/8FKLv/GVTX/yl47/85mP//QbEL/0m9E/9JwRf/Sb0T/4aGI//bg1f///Pf///78////
        /v///vv///v0///36P//8df/57mS/58OAv+fAQP/nwED/58BA/+fAQP/nwED/7dBQv///v3///37/8Cq
        p+4AAACGAAAAK7xDKvLFUzT/y2A8/9BsQ//Vd0n/2X9N/9uFUf/diVP/3opU/+y7mP/y07r/5K+Q//ns
        4P///Pf///35///89v//+e//47Wf/7Y/K/+nEQ3/oAME/58BA/+fAQP/nwED/58BA/+fAQP/z4CA///9
        +f///fj/7eDa+wAAAI4AAAAvx1c2/85nQP/Vdkn/24NQ/+COV//kl1z/555g/+miYv/rqWz/9ta3//PZ
        wf/00qr/455s//feyf/ksZb/6LSZ/895W/+8RCj/tzUi/68iF/+mDwz/nwID/58BA/+fAQP/nwED/58B
        A//DYGD///z1///79P//+vH/AAAAjwAAADDPakH/13tM/96LVf/lmV3/6qZk/++vav/zv4D/+uHD///6
        8P//9uX/67+Z/+qoZP/utoH/9tWz/+Sqe//JZDj/0GxC/8haOP+/Ry3/tjMh/60eFf+jCQj/nwED/58B
        A/+fAQP/nwED/7dBQv//+vD///nv///57P8AAACPAAAAMNh8TP/gjlf/6J9h/++vav/1vHL/+s2I///6
        8v//+/X/7cWd//nOiv/9z3//+9ur//jgwv//6sL/8smY/9iCTf/Yfk3/0GtC/8ZWNv+9Qir/sywd/6kW
        EP+gAwX/nwED/58BA/+fAQP/t0FC///46v//9+n///bm/wAAAI4AAAAv341W/+igYf/ws2z/+MR2//7W
        j///+e7///77/+e+ov//4KP//9+h///ty///9eL/57qW//nFd//xtG3/6aJi/+CPV//Xekv/zWU//8NP
        Mf+5OST/riIX/6QLCv+fAQP/nwED/58BA/+fAQP///fo///14v//9N//AAAAhgAAACvkm17x8LFr//jF
        d///1IX//+rD/////v////7/4bSd///pvv//6Lz///nt///14//ntov//9WH//nGeP/wsmz/555g/92I
        U//Tckb/yVs5/75EK/+zLR3/qRUP/6ACBP+fAQP/nwED/7dBQv//9uT///LZ/+fLs/oAAAB0AAAAIuen
        ZcT2v3P//tKD///cmv//+e7///////////////3/58W2/+3Ntv//+vD/+enW/+Goev//3Z3//tSF//fB
        df/tq2j/45Va/9h9Tf/NZj//wk4x/7c2I/+sHRT/ogYH/58BA/+3QUL///Tf///x1///79D/vpaA6AAA
        AFoAAAAV6rNsdPvLe///2pP///Pc///++/////7////+///+/P///fn///v0///57P//9eP/7cuw/+2/
        i///25X//M19//K3bv/nn2D/3IZS/9FuRP/GVjX/uz0n/68kGP+kCwr/nwED/6URE///7s//t0sz/58O
        Av9cBgHBAAAAPQAAAAjvvHIh/tOE///eoP//9+n///35///++////vv///36///89v//+vH///jq///0
        4P//8dX/57iP///fo//+1Ib/9r9z/+unZf/fjlb/1HVI/8lcOf+9Qyr/sikb/6YQDP+fAQP/z359///s
        yP+lDw7/nwED/ycAAYMAAAAjAAAAAQAAAAD71IzD/+Go///46v//+/T///z2///89////PX///rx///5
        7P//9uX///Pb///v0P/53rf/25xt//nMhP/5xXf/7axo/+KSWf/WeUr/y2A7/79GLP+zLR3/uEA8/9ue
        m///7Mf//+Sv/71KOf+FAQPkAAAARQAAAA0AAAAAAAAAAPbTkjL/463///bm///57f//+vD///rw///5
        7v//+Ov///bm///03v//8NX//+3J///ovP//46z/4aJm/+ipYv/urmn/45Ra/9qEV//fmHf/672i//bX
        u///6cD/7buC/+28hP//36L/tzkr/zsAAYEAAAAfAAAAAgAAAAAAAAAAAAAAAPnerZP/7Mj///jq///3
        5///9+f///bm///14v//893///HV///tzP//6sD//+Wz///gpP//2pL/46Bd/+6uaf/jlFr/13tL/8po
        O//HYjn/wVYz/7AxGv+fCgP/nwMD/7dBKP91BwHDAAAAMAAAAAgAAAAAAAAAAAAAAAAAAAAA9NusEfzn
        wNL/8tn///Xh///z3f//89v///HX///w0v//7cr/89Sw///sx///5rX/+dCQ//nWof/3y4z/4JVW/+GR
        Wf/WeEr/yl87/79FLP+zLB3/pxMO/58BA/+fAQP/jwED5iUAAUQAAAANAAAAAQAAAAAAAAAAAAAAAAAA
        AAAAAAAA9OC7Efzry9L/9OD//+/Q///uzv//7cv//+vF///pvv/zz6X//+Sx/+28hP/nsHr//+e5///T
        gP/aik7/34xV/9NzR//IWjj/vUEp/7EoG/+mDwz/nwED/5ABA+UlAAFEAAAADQAAAAEAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA9OPAEfzszdL/6sH//+nA///ovP//5rf//+Ov///gpf//3Jr/+c6J//PM
        m///04D/5KBb/+acX//bhFH/0GxD/8VUNP+6Oyb/ryMX/6QKCf+SAQPiKQABPQAAAA0AAAABAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA9OK/EfngspP/46///+Kr///gpv//3p7//9qV//PA
        eP/zxIX//9aI//nIeP/jm1v/4ZJZ/9d7S//MYz3/wkwv/7c0If+rGxP/hgYGrjkAAS0AAAAIAAAAAQAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPbapTL72ZrD/9qT///X
        i///1IT//9OA///TgP/yu2//56Jf/+WaXv/chVH/0m9E/8dZN/+9QSr/piobz2wSDUwAAAANAAAAAgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAD1zX8h9ct7dPjNfcT8zn3x+cZ4//Cxa//nnmD/3otU/9J2SPLBXjvKp0Urg3gpGjIAAAAIAAAAAQAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP+AAf/+AAB/+AAAP/AAAA/gAAAHwAAAA8AA
        AAOAAAABgAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAgAAAAYAAAAHAAAADwAAAA+AAAAfwAAAP+AAAH/4AAH//gAH/KAAAABAAAAAgAAAAAQAgAAAA
        AABABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAOAAAAHwAAACwAAAAwAAAAMAAA
        ACwAAAAfAAAADgAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcAAAAfAAAARwAAAG4AAACIAAAAjwAA
        AI8AAACIAAAAbgAAAEcAAAAfAAAABwAAAAAAAAAAAAAAAAAAAAcAAAAnSwABiH4BAt2xKyL/z3RZ/58B
        A/+fAQP/dwEC6jQAAcMAAACTAAAAYAAAACcAAAAHAAAAAAAAAAMAAAAfewECup8BA/+fAQP/pRET//nI
        fv/zxpb/vU1C/58BA/+fAQP/ZQEC4QAAAJ4AAABgAAAAHwAAAAMAAAAOgwECrp8BA/+fAQP/nwED/7Ex
        Mv/54cX//+a3///dnf+9TCz/nwED/58BA/9lAQLhAAAAkwAAAEcAAAAOdAECV6ADBP+jCQj/pg8L/6sd
        Gv/z2tD///bm///14f//7Mf/88aI/7EuKv+fAQP/nwED/zQAAcMAAABuAAAAH50UD72xKBv/uTol/71D
        Kv/LZk7///jr///9+v///PX///Pc///jrP/bjVL/nwED/6URE/+LNDbqAAAAiAAAACy7Pif/yFo4/9Fu
        RP/WeUr/5q6R///57v///fr///z1//TZw/+yOCf/nwED/58BA//hr67/+e3p/wAAAI8AAAAwz2lB/96K
        VP/pomL/8saa//zq0f/su43/7Met/9qOav+9RSr/rB0U/6ACBP+fAQP/256a///68P8AAACPAAAAMOGQ
        WP/yt27//ebD///46v//5LH//+/U//bMlP/ejlX/z2pB/7o7Jv+lDQv/nwED/898dv//9eP/AAAAiAAA
        ACzvsGv//tqY/////v/z4tr/+enW//nn0v/+2ZP/8rVu/9yFUv/FUzT/rR8V/58BA//z1cL/+eDE/wAA
        AG4AAAAf9sh7tP/uzf///fr///35///68P//9N7/7cGR//rLfv/kmFz/zGM9/7MtHf+yMS//57KR/34D
        At0AAABHAAAADvbSjEL/7s3///nt///57f//9uP//+/R///mtv/stHL/6J9h/+ahcP/yxp3/56+E/+Gb
        b/9KAAGIAAAAHwAAAAMAAAAA++S4ov/03///8df//+7O///pwP//4Kb/8sKC/9eBSv/NZT7/tC4e/6AE
        Bf96AQK7AAAAKAAAAAcAAAAAAAAAAAAAAAD66cmS/+e4///jr///3Zz/+c2F//C3bP/Zgk7/x1c2/68j
        F/+JAgO6AAAAIQAAAAcAAAAAAAAAAAAAAAAAAAAAAAAAAPbVmEL504q0/9SB//vKe//djVP/0nFF/7FC
        Kr1/FxBXAAAADgAAAAMAAAAAAAAAAAAAAADgBwAAwAMAAIABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAgAEAAMADAADgBwAA
</value>
  </data>
</root>