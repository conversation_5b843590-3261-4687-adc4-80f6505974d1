﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public class GsmStationAcceptQuery : LteStationAcceptQuery
    {
        public GsmStationAcceptQuery(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get
            {
                return "GSM宏站验收";
            }
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.DefaultSerialThemeName = null;
           
            List<ColumnDefItem> items = null;
            foreach (string col in queryColumns)
            {
                items = InterfaceManager.GetInstance().GetColumnDefByShowName(col);
                items = items.FindAll(x =>
                {
                    if (x.tableName.Contains("tb_dtgsm"))
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                });
                option.SampleColumns.AddRange(items);
            }

            option.EventInclude = true;
            option.MessageInclude = true;

            return option;
        }

        protected override bool isValidCondition()
        {
            string path = string.Empty;
            System.Windows.Forms.FolderBrowserDialog fbd = new System.Windows.Forms.FolderBrowserDialog();
            if (fbd.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                path = fbd.SelectedPath;
            }
            else
            {
                return false;
            }

            manager = new GsmStationAcceptManager();
            manager.SetAcceptCond(path);
            return true;
        }

        protected override void analyzeFile(FileInfo fileInfo)
        {
            if (MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }
            try
            {
                manager.AnalyzeFile(fileInfo, MainModel.DTDataManager.FileDataManagers[0]);
            }
            catch (Exception ex)
            {
                this.errEx = ex;
                throw;
            }
        }

        protected override void afterAnalyzeInThread()
        {
            try
            {
                if (this.errEx == null)
                {
                    manager.DoWorkAfterAnalyze();
                    manager = null;
                    this.errEx = null;
                }
            }
            catch (Exception ex)
            {
                this.errEx = ex;
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        protected new GsmStationAcceptManager manager;

        protected Exception errEx;

        #region queryColumns
        protected override List<string> queryColumns
        {
            get
            {
                return new List<string>()
                        {
                            "isampleid",
                            "itime",
                            "ilongitude",
                            "ilatitude",
                            "LAC",
                            "CI",
                            "BCCH",
                            "BSIC",
                            "RxLevSub",
                            "RxQualSub",
                            "AppThroughputDL",
                        };
            }
        }
        #endregion
    }
}
