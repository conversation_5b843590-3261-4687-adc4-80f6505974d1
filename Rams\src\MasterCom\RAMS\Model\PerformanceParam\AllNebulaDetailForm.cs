﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.PerformanceParam;
using MasterCom.RAMS.Model.PerformanceParam.TD切换分析;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.PerformanceParam
{
    public partial class AllNebulaDetailForm : DevExpress.XtraEditors.XtraForm
    {
        private MainModel mainModel;
        //显示的表
        private DataTable tableMain;
        private DataTable tableChild;
        private DataTable tableSave;
        private Dictionary<string, int> allLevelInfs;
        private string keyInfFirst;

        private Dictionary<string, List<JoinHandoverAnalysis>> allDetailInfs;
        //所有数据
        private Dictionary<int, List<JoinHandoverAnalysis>> handoverAnalysisDic;
        //所有数据
        public Dictionary<int, List<JoinHandoverAnalysis>> HandoverAnalysisDic
        {
            get { return handoverAnalysisDic; }
            set
            {
                handoverAnalysisDic = value;
                keyInfFirst = "";
                allLevelInfs = new Dictionary<string, int>();
                allDetailInfs = new Dictionary<string, List<JoinHandoverAnalysis>>();
                foreach (int iKey in value.Keys)
                {
                    IniOneTypeOfShow(value[iKey]);
                }
                IniDetailGridControlShow(allDetailInfs[keyInfFirst]);
                IniMainTableShow();
            }
        }
        private bool IsTDAnalysis = false;
        public AllNebulaDetailForm(MainModel mainModel,bool IsTDAnalysis)
        {
            InitializeComponent();
            this.mainModel = mainModel;
            this.IsTDAnalysis = IsTDAnalysis;
            //初始化表结构
            IniTableOfResult();
        }

        public void SetMainGridviewFocueCellLocation(string lac_ci)
        {
            for (int i = 0; i < gvMain.RowCount; i++)
            {
                DataRow dr = gvMain.GetDataRow(i);
                if (dr["LAC_CI"].ToString() == lac_ci)
                {
                    gvMain.MoveFirst();
                    gvMain.MoveBy(i);
                    return;
                }
            }
        }

        private void IniMainTableShow()
        {
            foreach (string keyInf in allDetailInfs.Keys)
            {
                List<object> rowInf = new List<object>();
                int MDTChangeCount = 0;
                int changeSuccesscount = 0;
                int changeSumCount = 0;
                foreach (JoinHandoverAnalysis jhAnalysis in allDetailInfs[keyInf])
                {
                    if (rowInf.Count == 0)
                    {
                        rowInf.Add(jhAnalysis.IMon);
                        rowInf.Add(jhAnalysis.ILac);
                        rowInf.Add(jhAnalysis.ICi);
                        rowInf.Add(jhAnalysis.LAC_CI);
                    }
                    MDTChangeCount += jhAnalysis.IHoNum;
                    changeSuccesscount += jhAnalysis.ISucc;
                    changeSumCount += jhAnalysis.IReq;
                }
                rowInf.Add(MDTChangeCount);
                rowInf.Add(changeSuccesscount);
                rowInf.Add(changeSumCount);
                rowInf.Add(((((double)(changeSuccesscount)) / ((double)(changeSumCount)))*100).ToString("0.0")+"%");
                tableMain.Rows.Add(rowInf.ToArray());
            }
        } 
   
        private void IniOneTypeOfShow(List<JoinHandoverAnalysis> joinHandoverData)
        {
            foreach (JoinHandoverAnalysis jhAnalysisData in joinHandoverData)
            {
                string Lac_Ci = jhAnalysisData.LAC_CI;
                if (keyInfFirst == "")
                {
                    keyInfFirst = Lac_Ci;
                }
                if (!allDetailInfs.ContainsKey(Lac_Ci))
                {
                    List<JoinHandoverAnalysis> detailInf = new List<JoinHandoverAnalysis>();
                    detailInf.Add(jhAnalysisData);
                    allDetailInfs.Add(Lac_Ci, detailInf);
                }
                else
                {
                    allDetailInfs[Lac_Ci].Add(jhAnalysisData);
                }
                string ILac_Ci = jhAnalysisData.ILac + "_" + jhAnalysisData.ICi;
                if (!allLevelInfs.ContainsKey(ILac_Ci))
                {
                    allLevelInfs.Add(ILac_Ci, jhAnalysisData.ILevel);
                }
            }
        }
        //初始化表数据
        List<JoinHandoverAnalysis> detailInfSave;
        private void IniDetailGridControlShow(List<JoinHandoverAnalysis> detailInf)
        {
            List<object> rowInf;
            tableChild.Rows.Clear();
            foreach (JoinHandoverAnalysis jhAnalysis in detailInf)
            {
                rowInf = new List<object>();
                rowInf.Add(jhAnalysis.ITargetLac);
                rowInf.Add(jhAnalysis.ITargetCi);

                if (IsTDAnalysis)
                {
                    if (((JoinHandoverAnalysisTD)jhAnalysis).INet == 1)
                    {
                        rowInf.Add("TD网内切换");
                    }
                    else
                    {
                        rowInf.Add("TD网间切换");
                    }
                }

                rowInf.Add(jhAnalysis.IHoNum);
                rowInf.Add((jhAnalysis.FoutRate*100).ToString("0.0")+"%");
                rowInf.Add((jhAnalysis.FintRate*100).ToString("0.0")+"%");
                rowInf.Add(jhAnalysis.IReq);
                rowInf.Add(jhAnalysis.ISucc);
                rowInf.Add((jhAnalysis.FSuccRate*100).ToString("0.0")+"%");
                switch (jhAnalysis.ILevel)
                {
                    case 1:
                        rowInf.Add("正常小区");
                        break;
                    case 2:
                        rowInf.Add("冗余小区");
                        break;
                    case 3:
                        rowInf.Add("低质小区");
                        break;
                    case 4:
                        rowInf.Add("规避小区");
                        break;
                    default:
                        break;
                }
                rowInf.Add(jhAnalysis.Strcomment);
                rowInf.Add(jhAnalysis.ILevel);
                tableChild.Rows.Add(rowInf.ToArray());
                string lac_ci = jhAnalysis.ILac + "_" + jhAnalysis.ICi;
                if (!allLevelInfs.ContainsKey(lac_ci))
                {
                    allLevelInfs.Add(lac_ci, jhAnalysis.ILevel);
                }
            }
            detailInfSave = detailInf;
        }      

        //初始化表结构
        private void IniTableOfResult()
        {
            tableMain = new DataTable();
            tableChild = new DataTable();

            tableMain.Columns.Add("IMon");
            tableMain.Columns.Add("ILAC");
            tableMain.Columns.Add("ICI");
            tableMain.Columns.Add("LAC_CI");
            tableMain.Columns.Add("MDTChangeCount");
            tableMain.Columns.Add("ChangeSuccessCount");
            tableMain.Columns.Add("ChangeCount");
            tableMain.Columns.Add("ChangeSuccessRadio");

            tableChild.Columns.Add("targetLAC");
            tableChild.Columns.Add("targetCI");

            if (IsTDAnalysis)
            {
                DevExpress.XtraGrid.Columns.GridColumn addedColumn = new DevExpress.XtraGrid.Columns.GridColumn();
                addedColumn.Caption = "网络切换类型";
                addedColumn.FieldName = "INet";
                addedColumn.Width = 70;
                addedColumn.VisibleIndex = 3;
                gridViewDetail.Columns.Insert(3, addedColumn);
                tableChild.Columns.Add("INet");
            }

            tableChild.Columns.Add("MDTChangeCount");
            tableChild.Columns.Add("outRadio");

            tableChild.Columns.Add("targetOutRadio");
            tableChild.Columns.Add("requestCout");

            tableChild.Columns.Add("successCount");
            tableChild.Columns.Add("successRadio");

            tableChild.Columns.Add("questionIndex");
            tableChild.Columns.Add("suggestInf");
            tableChild.Columns.Add("ILevel");

            gridControlRes.DataSource = tableMain;
            gridControlRes.RefreshDataSource();
            gridControlDetail.DataSource = tableChild;
            gridControlDetail.RefreshDataSource();
        }
        private bool IsFirstFocused = true;
        private void gvMain_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            if (IsFirstFocused)
            {
                IsFirstFocused = false;
                return;
            }
            Cell cell = null;
            TDCell tdCell = null;
            string lac_ci = gvMain.GetFocusedRowCellDisplayText(gcLAC_CI);//
            if (!allDetailInfs.ContainsKey(lac_ci))
            {
                return;
            }
            List<JoinHandoverAnalysis> witchingList = allDetailInfs[lac_ci];

            if (witchingList != null)
            {
                mainModel.HandoverAnalysisList.Clear();
                foreach (JoinHandoverAnalysis handoverAnalysis in witchingList)
                {
                    mainModel.HandoverAnalysisList.Add(handoverAnalysis);
                    string date = handoverAnalysis.IMon.ToString().Substring(0, 4) +
                        "-" + handoverAnalysis.IMon.ToString().Substring(4, 2);
                    DateTime dateTime = Convert.ToDateTime(date);
                    cell = mainModel.CellManager.GetCell(dateTime, (ushort)handoverAnalysis.ILac, (ushort)handoverAnalysis.ICi);
                    tdCell = mainModel.CellManager.GetTDCell(dateTime, handoverAnalysis.ILac, handoverAnalysis.ICi);
                    if (cell != null)
                    {
                        mainModel.MainForm.GetMapForm().GoToView(cell.EndPointLongitude, cell.EndPointLatitude);
                    }
                    else if(tdCell!=null)
                    {
                        mainModel.MainForm.GetMapForm().GoToView(tdCell.EndPointLongitude, tdCell.EndPointLatitude);                        
                    }
                }
                IniDetailGridControlShow(witchingList);
            }
        }
        

        private void gridViewDetail_RowStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowStyleEventArgs e)
        {
            string sTargetLevel = gridViewDetail.GetRowCellDisplayText(e.RowHandle, gcILevel);
            if (sTargetLevel == "1")
            {
                e.Appearance.BackColor = Color.FromArgb(100, 0, 255, 0);
            }
            else if (sTargetLevel == "2")
            {
                e.Appearance.BackColor = Color.FromArgb(100, 0, 0, 255);
            }
            else if (sTargetLevel == "3")
            {
                e.Appearance.BackColor = Color.DarkOrange;
            }
            else if (sTargetLevel == "4")
            {
                e.Appearance.BackColor = Color.FromArgb(100, 255, 0, 0);
            }
        }


        private void gvMain_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            gvMain_FocusedRowChanged(null, null);
        }

        private void gridViewDetail_Click(object sender, EventArgs e)
        {
            Cell cell = null;
            TDCell tdCell = null;
            string sLACDate = gridViewDetail.GetFocusedRowCellDisplayText(gcTargetLAC);
            string sCIDate = gridViewDetail.GetFocusedRowCellDisplayText(gcTargetCI);
            if (sLACDate == "" || sCIDate == "")
            {
                return;
            }
            if (detailInfSave != null)
            {
                foreach (JoinHandoverAnalysis jhAnalysis in detailInfSave)
                {
                    if (jhAnalysis.ITargetLac == int.Parse(sLACDate) &&
                        jhAnalysis.ITargetCi == int.Parse(sCIDate.ToString()))
                    {
                        mainModel.HandoverAnalysisList.Clear();
                        mainModel.HandoverAnalysisList.Add(jhAnalysis);
                        string date = jhAnalysis.IMon.ToString().Substring(0, 4) +
                            "-" + jhAnalysis.IMon.ToString().Substring(4, 2);
                        DateTime dateTime = Convert.ToDateTime(date);
                        cell = mainModel.CellManager.GetCell(dateTime, (ushort)jhAnalysis.ILac, (ushort)jhAnalysis.ICi);
                        tdCell = mainModel.CellManager.GetTDCell(dateTime, jhAnalysis.ILac, jhAnalysis.ICi);
                        if (cell != null)
                        {
                            mainModel.MainForm.GetMapForm().GoToView(cell.EndPointLongitude, cell.EndPointLatitude);
                        }
                        else if (tdCell != null)
                        {
                            mainModel.MainForm.GetMapForm().GoToView(tdCell.EndPointLongitude, tdCell.EndPointLatitude);
                        }
                        System.Threading.Thread.Sleep(0);
                    }
                }
            }
        }

        private void 导出数据ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //设置需要保存的表结构
            IniTableSaveStructure();

            //设置保存表的数据
            WaitBox.Show(IniTableSaveData);
            ExcelNPOIManager.ExportToExcel(tableSave);
        }

        private void IniTableSaveStructure()
        {
            tableSave = new DataTable();
            tableSave.Columns.Add("轮次");
            tableSave.Columns.Add("源LAC");
            tableSave.Columns.Add("源CI");

            tableSave.Columns.Add("路测切换总次数");
            tableSave.Columns.Add("网络侧切换成功次数");
            tableSave.Columns.Add("网络侧切换总次数");
            tableSave.Columns.Add("网络侧切换成功率");


            tableSave.Columns.Add("目标LAC");
            tableSave.Columns.Add("目标CI");
            tableSave.Columns.Add("路测切换次数");
            if (IsTDAnalysis)
            {
                tableSave.Columns.Add("网络切换类型"); 
            }
            tableSave.Columns.Add("源小区切出占比");
            tableSave.Columns.Add("目标小区切出占比");
            tableSave.Columns.Add("性能侧切换请求次数");
            tableSave.Columns.Add("性能侧切换成功次数");
            tableSave.Columns.Add("性能侧切换成功率");
            tableSave.Columns.Add("问题级别");
            tableSave.Columns.Add("建议方案");
        }

        private void IniTableSaveData()
        {
            WaitBox.ProgressPercent = 25;
            if (tableSave != null && tableSave.Columns.Count != 0)
            {
                foreach (string keyInf in allDetailInfs.Keys)
                {
                    List<object> rowInf = new List<object>();
                    int MDTChangeCount = 0;
                    int changeSuccesscount = 0;
                    int changeSumCount = 0;
                    foreach (JoinHandoverAnalysis jhAnalysis in allDetailInfs[keyInf])
                    {
                        if (rowInf.Count == 0)
                        {
                            rowInf.Add(jhAnalysis.IMon);
                            rowInf.Add(jhAnalysis.ILac);
                            rowInf.Add(jhAnalysis.ICi);
                        }
                        MDTChangeCount += jhAnalysis.IHoNum;
                        changeSuccesscount += jhAnalysis.ISucc;
                        changeSumCount += jhAnalysis.IReq;
                    }
                    rowInf.Add(MDTChangeCount);
                    rowInf.Add(changeSuccesscount);
                    rowInf.Add(changeSumCount);
                    rowInf.Add((((double)(changeSuccesscount)) / ((double)(changeSumCount))).ToString("0.000"));
                    foreach (JoinHandoverAnalysis jhAnalysis in allDetailInfs[keyInf])
                    {
                        addRowInfSave(rowInf, jhAnalysis);
                    }
                }
            }
            WaitBox.ProgressPercent = 100;
            WaitBox.Close();
        }

        private void addRowInfSave(List<object> rowInf, JoinHandoverAnalysis jhAnalysis)
        {
            List<object> rowInfSave = new List<object>(rowInf);
            rowInfSave.Add(jhAnalysis.ITargetLac);
            rowInfSave.Add(jhAnalysis.ITargetCi);

            if (IsTDAnalysis)
            {
                if (((JoinHandoverAnalysisTD)jhAnalysis).INet == 1)
                {
                    rowInfSave.Add("TD网内切换");
                }
                else
                {
                    rowInfSave.Add("TD网间切换");
                }
            }

            rowInfSave.Add(jhAnalysis.IHoNum);
            rowInfSave.Add((jhAnalysis.FoutRate * 100).ToString("0.0") + "%");
            rowInfSave.Add((jhAnalysis.FintRate * 100).ToString("0.0") + "%");
            rowInfSave.Add(jhAnalysis.IReq);
            rowInfSave.Add(jhAnalysis.ISucc);
            rowInfSave.Add(jhAnalysis.FSuccRate.ToString("0.000"));
            switch (jhAnalysis.ILevel)
            {
                case 1:
                    rowInfSave.Add("正常小区");
                    break;
                case 2:
                    rowInfSave.Add("冗余小区");
                    break;
                case 3:
                    rowInfSave.Add("低质小区");
                    break;
                case 4:
                    rowInfSave.Add("规避小区");
                    break;
                default:
                    break;
            }
            rowInfSave.Add(jhAnalysis.Strcomment);
            tableSave.Rows.Add(rowInfSave.ToArray());
        }

        private void toolStripMenuItem1_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridViewDetail);
        }

    }
}