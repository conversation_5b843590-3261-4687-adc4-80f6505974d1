﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTLTESanHighCoverateRoadListForm : MinCloseForm
    {
        private MapForm mapForm = null;
        private List<LTEScanHighCoverageRoadInfo> relRoadCoverageList = null;
        private Dictionary<LTECell, LTEScanHighCoverageCellType> relProblemCellDic;

        private List<LTEScanHighCoverageRoadInfo> absRoadCoverageList = null;
        private Dictionary<LTECell, LTEScanHighCoverageCellType> absProblemCellDic;

        public ZTLTESanHighCoverateRoadListForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            mapForm = MainModel.GetInstance().MainForm.GetMapForm();
            init();
            RelListViewRoad.MouseDoubleClick += RelListViewRoad_MouseDoubleClick;
            AbsListViewRoad.MouseDoubleClick += AbsListViewRoad_MouseDoubleClick;
            DisposeWhenClose = true;
        }

        protected override void MinCloseForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            TempLayer.Instance.Clear();
            lnglatLineDic.Clear();
            mModel.MainForm.GetMapForm().updateMap();
            base.MinCloseForm_FormClosing(sender, e);
        }

        private void init()
        {
            this.olvColumnSN.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.SN;
                }
                else if (row is LTEScanHighCoveragePointInfo)
                {
                    LTEScanHighCoveragePointInfo item = row as LTEScanHighCoveragePointInfo;
                    return item.SN;
                }
                else if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.SN;
                }
                return null;
            };

            this.olvColumnCellType.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.CellTypeDesc;
                }
                return null;
            };

            this.olvColumnRoadName.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.RoadName;
                }
                return "";
            };

            this.olvColumnDitance.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.Distance;
                }
                return "";
            };

            this.olvColumnPercent.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.Percent;
                }
                return "";
            };

            this.olvColumnSample.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.SampleLst.Count;
                }
                return "";
            };

            this.olvColumnCellName.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.CellName;
                }
                return "";
            };

            this.olvColumnLAC.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.TAC;
                }
                return "";
            };

            this.olvColumnCI.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.ECI;
                }
                return "";
            };

            this.olvColumnARFCN.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.EARFCN;
                }
                return "";
            };

            this.olvColumnCPI.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.PCI;
                }
                return "";
            };

            this.olvColumnRscp.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.Rssi;
                }
                return "";
            };

            this.olvColumnCellDistance.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.Distance;
                }
                return "";
            };

            this.olvColumnMaxRscp.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    if (item.RssiSample > 0)
                    {
                        return item.RssiMax;
                    }
                }
                return "";
            };

            this.olvColumnMinRscp.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    if (item.RssiSample > 0)
                    {
                        return item.RssiMin;
                    }
                }
                return "";
            };

            this.olvColumnAvgRscp.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    if (item.RssiSample > 0)
                    {
                        return item.RssiAvg;
                    }
                }
                return "";
            };

            this.olvColumnLongitudeMid.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.LongitudeMid;
                }
                else if (row is LTEScanHighCoveragePointInfo)
                {
                    LTEScanHighCoveragePointInfo item = row as LTEScanHighCoveragePointInfo;
                    return item.Tp.Longitude;
                }
                else if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.Longitude;
                }

                return "";
            };

            this.olvColumnLatitudeMid.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.LatitudeMid;
                }
                else if (row is LTEScanHighCoveragePointInfo)
                {
                    LTEScanHighCoveragePointInfo item = row as LTEScanHighCoveragePointInfo;
                    return item.Tp.Latitude;
                }
                else if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.Latitude;
                }
                return "";
            };

            this.olvColumnFileName.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.FileName;
                }
                return "";
            };

            this.olvColumnFirstTime.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.GetFirstTime();
                }
                return "";
            };

            this.olvColumnLastTime.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.GetLasttime();
                }
                return "";
            };

            this.RelListViewRoad.CanExpandGetter = delegate(object x)
            {
                return x is LTEScanHighCoverageRoadInfo || x is LTEScanHighCoveragePointInfo;
            };
            this.RelListViewRoad.ChildrenGetter = delegate(object x)
            {
                if (x is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo roadInfo = x as LTEScanHighCoverageRoadInfo;
                    return roadInfo.SampleLst;
                }
                else if (x is LTEScanHighCoveragePointInfo)
                {
                    LTEScanHighCoveragePointInfo pointInfo = x as LTEScanHighCoveragePointInfo;
                    return pointInfo.CellList;
                }
                else
                {
                    return "";
                }
            };


            this.absSN.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.SN;
                }
                else if (row is LTEScanHighCoveragePointInfo)
                {
                    LTEScanHighCoveragePointInfo item = row as LTEScanHighCoveragePointInfo;
                    return item.SN;
                }
                else if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.SN;
                }
                return null;
            };

            this.absCellType.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.CellTypeDesc;
                }
                return null;
            };

            this.absRoadName.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.RoadName;
                }
                return "";
            };

            this.absDistance.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.Distance;
                }
                return "";
            };

            this.absPercent.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.Percent;
                }
                return "";
            };

            this.absSample.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.SampleLst.Count;
                }
                return "";
            };

            this.absCellName.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.CellName;
                }
                return "";
            };

            this.absLAC.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.TAC;
                }
                return "";
            };

            this.absCI.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.ECI;
                }
                return "";
            };

            this.absARFCN.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.EARFCN;
                }
                return "";
            };

            this.absCPI.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.PCI;
                }
                return "";
            };

            this.absRscp.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.Rssi;
                }
                return "";
            };

            this.absCellDistance.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.Distance;
                }
                return "";
            };

            this.absMaxRscp.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    if (item.RssiSample > 0)
                    {
                        return item.RssiMax;
                    }
                }
                return "";
            };

            this.absMinRscp.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    if (item.RssiSample > 0)
                    {
                        return item.RssiMin;
                    }
                }
                return "";
            };

            this.absAvgRscp.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    if (item.RssiSample > 0)
                    {
                        return item.RssiAvg;
                    }
                }
                return "";
            };

            this.absLongitudeMid.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.LongitudeMid;
                }
                else if (row is LTEScanHighCoveragePointInfo)
                {
                    LTEScanHighCoveragePointInfo item = row as LTEScanHighCoveragePointInfo;
                    return item.Tp.Longitude;
                }
                else if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.Longitude;
                }

                return "";
            };

            this.absLatitudeMid.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.LatitudeMid;
                }
                else if (row is LTEScanHighCoveragePointInfo)
                {
                    LTEScanHighCoveragePointInfo item = row as LTEScanHighCoveragePointInfo;
                    return item.Tp.Latitude;
                }
                else if (row is LTEScanHighCoverageCellInfo)
                {
                    LTEScanHighCoverageCellInfo item = row as LTEScanHighCoverageCellInfo;
                    return item.Latitude;
                }
                return "";
            };

            this.absFileName.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.FileName;
                }
                return "";
            };

            this.absFirstTime.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.GetFirstTime();
                }
                return "";
            };

            this.absLastTime.AspectGetter = delegate(object row)
            {
                if (row is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo item = row as LTEScanHighCoverageRoadInfo;
                    return item.GetLasttime();
                }
                return "";
            };

            this.AbsListViewRoad.CanExpandGetter = delegate(object x)
            {
                return x is LTEScanHighCoverageRoadInfo || x is LTEScanHighCoveragePointInfo;
            };
            this.AbsListViewRoad.ChildrenGetter = delegate(object x)
            {
                if (x is LTEScanHighCoverageRoadInfo)
                {
                    LTEScanHighCoverageRoadInfo roadInfo = x as LTEScanHighCoverageRoadInfo;
                    return roadInfo.SampleLst;
                }
                else if (x is LTEScanHighCoveragePointInfo)
                {
                    LTEScanHighCoveragePointInfo pointInfo = x as LTEScanHighCoveragePointInfo;
                    return pointInfo.CellList;
                }
                else
                {
                    return "";
                }
            };
        }

        public void FillData(List<LTEScanHighCoverageRoadInfo> relRoadCoverageList, Dictionary<LTECell, LTEScanHighCoverageCellType> relProblemCellDic,
                                List<LTEScanHighCoverageRoadInfo> absRoadCoverageList, Dictionary<LTECell, LTEScanHighCoverageCellType> absProblemCellDic, LTEScanHighCoverageRoadCondition cond)
        {
            if (!cond.IsRelativeCheck)
            {
                splitContainer1.Panel1Collapsed = true;
            }

            if (!cond.IsAbsCheck)
            {
                splitContainer1.Panel2Collapsed = true;
            }

            RelListViewRoad.RebuildColumns();
            RelListViewRoad.ClearObjects();
            RelListViewRoad.SetObjects(relRoadCoverageList);
            this.relRoadCoverageList = relRoadCoverageList;
            this.relProblemCellDic = relProblemCellDic;

            AbsListViewRoad.RebuildColumns();
            AbsListViewRoad.ClearObjects();
            AbsListViewRoad.SetObjects(absRoadCoverageList);
            this.absRoadCoverageList = absRoadCoverageList;
            this.absProblemCellDic = absProblemCellDic;

            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        private void miExportRel_Click(object sender, EventArgs e)
        {
            exportToExcel(relRoadCoverageList);
        }

        private void miExportAbs_Click(object sender, EventArgs e)
        {
            exportToExcel(absRoadCoverageList);
        }

        private void exportToExcel(List<LTEScanHighCoverageRoadInfo> roadCoverageList)
        {
            List<List<object>> content = new List<List<object>>();
            List<object> title = new List<object>();
            title.Add("序号");
            title.Add("道路名称");
            title.Add("距离(米)");
            title.Add("高重叠覆盖点占比(%)");
            title.Add("采样点数");
            title.Add("第一强最大值");
            title.Add("第一强最小值");
            title.Add("第一强平均值");
            title.Add("路段中心经度");
            title.Add("路段中心纬度");
            title.Add("文件名");
            title.Add("开始时间");
            title.Add("结束时间");

            title.Add("采样点编号");
            title.Add("采样点经度");
            title.Add("采样点纬度");

            title.Add("小区名称");
            title.Add("小区状态");
            title.Add("TAC");
            title.Add("ECI");
            title.Add("EARFCN");
            title.Add("PCI");
            title.Add("RSRP");
            title.Add("与采样点距离（米）");
            title.Add("小区经度");
            title.Add("小区纬度");
            content.Add(title);

            foreach (LTEScanHighCoverageRoadInfo roadInfo in roadCoverageList)
            {
                List<object> roadPart = new List<object>();
                roadPart.Add(roadInfo.SN);
                roadPart.Add(roadInfo.RoadName);
                roadPart.Add(roadInfo.Distance);
                roadPart.Add(roadInfo.Percent);
                roadPart.Add(roadInfo.SampleLst.Count);
                roadPart.Add(roadInfo.RssiMax);
                roadPart.Add(roadInfo.RssiMin);
                roadPart.Add(roadInfo.RssiAvg);
                roadPart.Add(roadInfo.LongitudeMid);
                roadPart.Add(roadInfo.LatitudeMid);
                roadPart.Add(roadInfo.FileName);
                roadPart.Add(roadInfo.GetFirstTime());
                roadPart.Add(roadInfo.GetLasttime());
                foreach (LTEScanHighCoveragePointInfo ptInfo in roadInfo.SampleLst)
                {
                    List<object> pointPart = new List<object>(roadPart);
                    pointPart.Add(ptInfo.SN);
                    pointPart.Add(ptInfo.Tp.Longitude);
                    pointPart.Add(ptInfo.Tp.Latitude);
                    foreach (LTEScanHighCoverageCellInfo cellInfo in ptInfo.CellList)
                    {
                        List<object> cellPart = new List<object>(pointPart);
                        cellPart.Add(cellInfo.CellName);
                        cellPart.Add(cellInfo.CellTypeDesc);
                        cellPart.Add(cellInfo.TAC);
                        cellPart.Add(cellInfo.ECI);
                        cellPart.Add(cellInfo.EARFCN);
                        cellPart.Add(cellInfo.PCI);
                        cellPart.Add(cellInfo.Rssi);
                        cellPart.Add(cellInfo.Distance);
                        cellPart.Add(cellInfo.Longitude);
                        cellPart.Add(cellInfo.Latitude);
                        content.Add(cellPart);
                    }
                }
            }
            ExcelNPOIManager.ExportToExcel(content);
        }

        private void miExportSummaryRel_Click(object sender, EventArgs e)
        {
            exportSummaryToExcel(relRoadCoverageList);
        }

        private void miExportSummaryAbs_Click(object sender, EventArgs e)
        {
            exportSummaryToExcel(absRoadCoverageList);
        }

        private void exportSummaryToExcel(List<LTEScanHighCoverageRoadInfo> roadCoverageList)
        {
            List<List<object>> content = new List<List<object>>();
            List<object> title = new List<object>();
            title.Add("序号");
            title.Add("道路名称");
            title.Add("距离(米)");
            title.Add("高重叠覆盖点占比(%)");
            title.Add("采样点数");
            title.Add("第一强最大值");
            title.Add("第一强最小值");
            title.Add("第一强平均值");
            title.Add("经度");
            title.Add("纬度");
            title.Add("文件名");
            title.Add("开始时间");
            title.Add("结束时间");
            content.Add(title);

            foreach (LTEScanHighCoverageRoadInfo roadInfo in roadCoverageList)
            {
                List<object> roadPart = new List<object>();
                roadPart.Add(roadInfo.SN);
                roadPart.Add(roadInfo.RoadName);
                roadPart.Add(roadInfo.Distance);
                roadPart.Add(roadInfo.Percent);
                roadPart.Add(roadInfo.SampleLst.Count);
                roadPart.Add(roadInfo.RssiMax);
                roadPart.Add(roadInfo.RssiMin);
                roadPart.Add(roadInfo.RssiAvg);
                roadPart.Add(roadInfo.LongitudeMid);
                roadPart.Add(roadInfo.LatitudeMid);
                roadPart.Add(roadInfo.FileName);
                roadPart.Add(roadInfo.GetFirstTime());
                roadPart.Add(roadInfo.GetLasttime());
                content.Add(roadPart);
            }
            ExcelNPOIManager.ExportToExcel(content);
        }        
        
        private void miExportCellAbs_Click(object sender, EventArgs e)
        {
            exportCellToExcel(absProblemCellDic);
        }

        private void miExportCellRel_Click(object sender, EventArgs e)
        {
            exportCellToExcel(relProblemCellDic);
        }

        private void exportCellToExcel(Dictionary<LTECell, LTEScanHighCoverageCellType> problemCellDic)
        {
            List<List<object>> content = new List<List<object>>();
            List<object> title = new List<object>();
            title.Add("小区名");
            title.Add("TAC");
            title.Add("ECI");
            title.Add("问题描述");
            content.Add(title);

            foreach (LTECell lteCell in problemCellDic.Keys)
            {
                List<object> row = new List<object>();
                row.Add(lteCell.Name);
                row.Add(lteCell.TAC);
                row.Add(lteCell.ECI);
                row.Add(LTEScanHighCoverageCellChecker.GetCellTypeDesc(problemCellDic[lteCell]));
                content.Add(row);
            }

            ExcelNPOIManager.ExportToExcel(content);
        }

        private void RelListViewRoad_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (RelListViewRoad.SelectedObject is LTEScanHighCoverageRoadInfo)
            {
                LTEScanHighCoverageRoadInfo info = RelListViewRoad.SelectedObject as LTEScanHighCoverageRoadInfo;
                showRoadInfo(info);
            }
            else if (RelListViewRoad.SelectedObject is LTEScanHighCoveragePointInfo)
            {
                LTEScanHighCoveragePointInfo info = RelListViewRoad.SelectedObject as LTEScanHighCoveragePointInfo;
                showPointInfo(info);
            }
        }

        private void AbsListViewRoad_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (AbsListViewRoad.SelectedObject is LTEScanHighCoverageRoadInfo)
            {
                LTEScanHighCoverageRoadInfo info = AbsListViewRoad.SelectedObject as LTEScanHighCoverageRoadInfo;
                showRoadInfo(info);
            }
            else if (AbsListViewRoad.SelectedObject is LTEScanHighCoveragePointInfo)
            {
                LTEScanHighCoveragePointInfo info = AbsListViewRoad.SelectedObject as LTEScanHighCoveragePointInfo;
                showPointInfo(info);
            }
        }

        private void showRoadInfo(LTEScanHighCoverageRoadInfo info)
        {
            mModel.DTDataManager.Clear();
            lnglatLineDic = new Dictionary<string, Line>();
            DbPoint goViewPoint = null;

            foreach (LTEScanHighCoveragePointInfo pointInfo in info.SampleLst)
            {
                mModel.DTDataManager.Add(pointInfo.Tp);
                this.AddLineInPoint(pointInfo);
            }

            goViewPoint = new DbPoint(info.LongitudeMid, info.LatitudeMid);

            mModel.FireDTDataChanged(this);
            TempLayer.Instance.Draw(this.DrawLine);
            mModel.MainForm.GetMapForm().GoToView(goViewPoint.x, goViewPoint.y);
        }

        private void showPointInfo(LTEScanHighCoveragePointInfo info)
        {
            mModel.DTDataManager.Clear();
            lnglatLineDic = new Dictionary<string, Line>();
            DbPoint goViewPoint = null;

            mModel.DTDataManager.Add(info.Tp);
            this.AddLineInPoint(info);
            goViewPoint = new DbPoint(info.Tp.Longitude, info.Tp.Latitude);

            mModel.FireDTDataChanged(this);
            TempLayer.Instance.Draw(this.DrawLine);
            mModel.MainForm.GetMapForm().GoToView(goViewPoint.x, goViewPoint.y);
        }

        //private void GoToView(List<LTEScanHighCoverageCellInfo> cellList)
        //{
        //    double ltLong = 100000;
        //    double ltLat = -100000;
        //    double brLong = -100000;
        //    double brLat = 100000;

        //    foreach (LTEScanHighCoverageCellInfo cellInfo in cellList)
        //    {
        //        if (cellInfo.LteCell == null)
        //        {
        //            continue;
        //        }
        //        if (cellInfo.LteCell.Longitude < ltLong)
        //        {
        //            ltLong = cellInfo.LteCell.Longitude;
        //        }
        //        if (cellInfo.LteCell.Longitude > brLong)
        //        {
        //            brLong = cellInfo.LteCell.Longitude;
        //        }
        //        if (cellInfo.LteCell.Latitude < brLat)
        //        {
        //            brLat = cellInfo.LteCell.Latitude;
        //        }
        //        if (cellInfo.LteCell.Latitude > ltLat)
        //        {
        //            ltLat = cellInfo.LteCell.Latitude;
        //        }
        //    }
        //    MainModel.MainForm.GetMapForm().GoToView((ltLong + brLong) / 2, (ltLat + brLat) / 2, 8000);
        //}

        private void AddLineInPoint(LTEScanHighCoveragePointInfo ptInfo)
        {
            double ptLng = ptInfo.Tp.Longitude;
            double ptLat = ptInfo.Tp.Latitude;
            foreach (LTEScanHighCoverageCellInfo cellInfo in ptInfo.CellList)
            {
                if (cellInfo.LteCell == null)
                {
                    continue;
                }
                double cellLng = cellInfo.LteCell.EndPointLongitude;
                double cellLat = cellInfo.LteCell.EndPointLatitude;

                string key = string.Format("{0}_{1}_{2}_{3}", ptLng, ptLat, cellLng, cellLat);                
                if (lnglatLineDic.ContainsKey(key))
                {
                    continue;
                }

                Line line = new Line();
                line.P1 = new DbPoint(ptLng, ptLat);
                line.P2 = new DbPoint(cellLng, cellLat);
                if (cellInfo.CellType == LTEScanHighCoverageCellType.FarCover)
                {
                    line.Color = farCoverColor;
                }
                else if (cellInfo.CellType == LTEScanHighCoverageCellType.LeakOut)
                {
                    line.Color = Color.Chocolate;
                }
                else
                {
                    line.Color = Color.Blue;
                }
                lnglatLineDic.Add(key, line);
            }
        }

        private void DrawLine(Rectangle clientRect, Rectangle updateRect, Graphics graphics, MapOperation mop)
        {
            if (lnglatLineDic.Count == 0)
            {
                return;
            }

            foreach (Line line in lnglatLineDic.Values)
            {
                PointF p1, p2;
                mop.ToDisplay(line.P1, out p1);
                mop.ToDisplay(line.P2, out p2);
                Pen pen = new Pen(line.Color, 2);
                pen.DashStyle = System.Drawing.Drawing2D.DashStyle.Dash;

                graphics.DrawLine(pen, p1, p2);
            }
        }

        private Color farCoverColor = Color.Red;
        //private Color leakOutColor = Color.Blue;
        //private Color normalColor = Color.Lime;
        private Dictionary<string, Line> lnglatLineDic = new Dictionary<string, Line>();

        private class Line
        {
            public DbPoint P1;
            public DbPoint P2;
            public Color Color;
        }

        private void miExpandRel_Click(object sender, EventArgs e)
        {
            RelListViewRoad.ExpandAll();
        }

        private void miExpandAbs_Click(object sender, EventArgs e)
        {
            AbsListViewRoad.ExpandAll();
        }

        private void miCollapseRel_Click(object sender, EventArgs e)
        {
            RelListViewRoad.CollapseAll();
        }

        private void miCollapseAbs_Click(object sender, EventArgs e)
        {
            AbsListViewRoad.CollapseAll();
        }
    }
}
