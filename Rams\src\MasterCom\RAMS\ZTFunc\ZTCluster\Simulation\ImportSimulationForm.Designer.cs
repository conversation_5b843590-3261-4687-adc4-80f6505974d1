﻿namespace MasterCom.RAMS.ZTFunc.ZTCluster.Simulation
{
    partial class ImportSimulationForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.btnImportXls = new System.Windows.Forms.Button();
            this.btnBrowse = new System.Windows.Forms.Button();
            this.tbxImportPath = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // btnImportXls
            // 
            this.btnImportXls.Location = new System.Drawing.Point(524, 26);
            this.btnImportXls.Name = "btnImportXls";
            this.btnImportXls.Size = new System.Drawing.Size(75, 23);
            this.btnImportXls.TabIndex = 7;
            this.btnImportXls.Text = "导入";
            this.btnImportXls.UseVisualStyleBackColor = true;
            this.btnImportXls.Click += new System.EventHandler(this.btnImportXls_Click);
            // 
            // btnBrowse
            // 
            this.btnBrowse.Location = new System.Drawing.Point(443, 26);
            this.btnBrowse.Name = "btnBrowse";
            this.btnBrowse.Size = new System.Drawing.Size(75, 23);
            this.btnBrowse.TabIndex = 6;
            this.btnBrowse.Text = "浏览...";
            this.btnBrowse.UseVisualStyleBackColor = true;
            this.btnBrowse.Click += new System.EventHandler(this.btnBrowse_Click);
            // 
            // tbxImportPath
            // 
            this.tbxImportPath.BackColor = System.Drawing.SystemColors.Window;
            this.tbxImportPath.Location = new System.Drawing.Point(113, 28);
            this.tbxImportPath.Name = "tbxImportPath";
            this.tbxImportPath.ReadOnly = true;
            this.tbxImportPath.Size = new System.Drawing.Size(324, 21);
            this.tbxImportPath.TabIndex = 5;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(17, 31);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(89, 12);
            this.label5.TabIndex = 4;
            this.label5.Text = "导入文件路径：";
            // 
            // ImportSimulationForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(617, 83);
            this.Controls.Add(this.btnImportXls);
            this.Controls.Add(this.btnBrowse);
            this.Controls.Add(this.tbxImportPath);
            this.Controls.Add(this.label5);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ImportSimulationForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "导入小区信息";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnImportXls;
        private System.Windows.Forms.Button btnBrowse;
        private System.Windows.Forms.TextBox tbxImportPath;
        private System.Windows.Forms.Label label5;

    }
}