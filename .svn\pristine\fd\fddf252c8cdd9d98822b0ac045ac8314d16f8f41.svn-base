﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using NPOI.OpenXmlFormats.Dml;

namespace MasterCom.RAMS.ZTFunc.ZTCluster
{
    public partial class XtraClusterForm : DevExpress.XtraEditors.XtraForm
    {
        MainModel mainModel;
        public XtraClusterForm()
        {
            InitializeComponent();
            mainModel = MainModel.GetInstance();
            InitializeComponent();
            comboBox1.SelectedIndex = 0;
        }

        List<ClusterManager> clustermanagerList;
        
        public List<ClusterAna> ClusteranaList { get; set; } = new List<ClusterAna>();
        public List<ClusterInfoMR> ClusterinfoList { get; set; } = new List<ClusterInfoMR>();
        public List<ClusterShow> ClustershowList { get; set; } = new List<ClusterShow>();

        public void setData(List<ClusterShow> clustershow,List<ClusterInfoMR> clusterinfo,List<ClusterAna> clusterana,List<ClusterInterference> clusterinter)
        {
            

            clustermanagerList = new List<ClusterManager>(mainModel.ClusterManagerList);
            gridControlShow.DataSource = clustershow;

            gridControlInter.DataSource = clusterinter;

            ClustershowList = clustershow;
            ClusteranaList = clusterana;
            ClusterinfoList = clusterinfo;
        }

        //隐藏至右下角
        private void XtraClusterForm_Deactivate(object sender, EventArgs e)
        {
            if (this.WindowState == FormWindowState.Minimized)
            {
                this.Visible = false;
                mainModel.AddQuickWindowItem(this.GetType().Name, this.Text, "images\\cellquery.gif");
            }
        }


        int iclusterid = -1;
        //点击显示簇信息
        private void gridControlShow_Click(object sender, EventArgs e)
        {
            try
            {
                iclusterid = Convert.ToInt32(gridViewShow.GetFocusedRowCellValue("Iclusterid").ToString());

                List<ClusterInfoMR> list = new List<ClusterInfoMR>();
                foreach (ClusterInfoMR item in ClusterinfoList)
                {
                    if (item.Iclusterid == iclusterid)
                    {
                        list.Add(item);
                    }

                }
                gridControlInfo.DataSource = list;
            }
            catch
            {
                 //continue
            }
        }

        //单元格颜色
        private void gridViewInfo_RowCellStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
        {
            List<ClusterInfoMR> cinfoMR = new List<ClusterInfoMR>();

            //处理高频小区
            ClusterManager cmtemp = clustermanagerList.Find(delegate (ClusterManager p) { return p.Iclusterid == iclusterid; });
            if (cmtemp != null)
            {
                cinfoMR = cmtemp.MaxCell;

            }

            if (e.Column.FieldName == "Strcellname" && cinfoMR.Count == 3)
            {
                string aa = gridViewInfo.GetRowCellDisplayText(e.RowHandle, gridViewInfo.Columns["Strcellname"]);

                if (aa == cinfoMR[0].Strcellname)  //top1
                {
                    e.Appearance.BackColor = Color.FromArgb(255, 26, 26);
                    e.Appearance.BackColor2 = Color.LightCyan;
                }
                if (aa == cinfoMR[1].Strcellname) //top2
                {
                    e.Appearance.BackColor = Color.FromArgb(255, 61, 255);
                    e.Appearance.BackColor2 = Color.LightCyan;
                }
                if (aa == cinfoMR[2].Strcellname) //tio3
                {
                    e.Appearance.BackColor = Color.FromArgb(255, 177, 61);
                    e.Appearance.BackColor2 = Color.LightCyan;
                }
            }
        }

        //所选簇信息变动 显示小区集信息
        private void gridControlInfo_DataSourceChanged(object sender, EventArgs e)
        {
            List<ClusterInfoMR> list = gridViewInfo.DataSource as List<ClusterInfoMR>;

            List<ClusterAna> list2=new List<ClusterAna>();

            foreach (ClusterAna ana in ClusteranaList)
            {
                foreach (ClusterInfoMR mr in list)
                {
                    if (mr.Strcellname==ana.Strcellname)
                    {
                        list2.Add(ana);
                    }
                }
            }

            gridControlAna.DataSource = list2;
        }

       

        //双击切换到所选簇信息
        private void gridControlShow_DoubleClick(object sender, EventArgs e)
        {
            int clusterid = -1;
            try
            {
                clusterid = Convert.ToInt32(gridViewShow.GetFocusedRowCellValue("Iclusterid").ToString());
                if (clusterid < 1)
                    return;
            }
            catch
            {
                return;
            }

            List<ClusterInfoMR> list = new List<ClusterInfoMR>();
            foreach (ClusterInfoMR item in ClusterinfoList)
            {
                if (item.Iclusterid == clusterid)
                {
                    list.Add(item);
                }

            }
            gridControlInfo.DataSource = list;
            xtraTabControl1.SelectedTabPageIndex = 1;
        }

        //频率筛选变化
        private void CheckBoxChanged(object sender, EventArgs e)
        {
            List<ClusterManager> list = new List<ClusterManager>();
            if (ckb900.Checked)
            {
                foreach (ClusterManager item in clustermanagerList)
                {
                    if (item.Strbandtype == "900")
                    {
                        list.Add(item);
                    }
                }
            }
            if (ckb1800.Checked)
            {
                foreach (ClusterManager item in clustermanagerList)
                {
                    if (item.Strbandtype == "1800")
                    {
                        list.Add(item);
                    }
                }
            }
            mainModel.ClusterManagerList = list;
        }

        //分组簇
        Dictionary<int, List<ClusterManager>> clusterManagerDic = new Dictionary<int, List<ClusterManager>>();
        int j = 0;

        List<string> clearCell = new List<string>();  //排除小区列表

        List<string> shieldCell = new List<string>();// 屏蔽小区列表

        //屏蔽小区
        private void ToolStripMenuItemShield_Click(object sender, EventArgs e)
        {
            clusterManagerDic.Clear();
            j = 0;
            clearCell.Clear();

            string SPcellname = "";
            
            SPcellname = gridViewInfo.GetFocusedRowCellValue("Strcellname").ToString();
            if (SPcellname == "")
                return;
            shieldCell.Add(SPcellname);

            ClusterinfoList.Clear();
            //处理现有小区集合
            foreach (ClusterManager item in clustermanagerList)
            {
                ClusterinfoList.AddRange(item.ClusterinfoMR);                
            }

            //重置界面分组数据
            foreach (ClusterShow item in ClustershowList)
            {
                item.GroupId = 0;
            }

            comboBox1.Items.Clear();
            //屏蔽小区
            foreach (string item in shieldCell)
            {
                removeSameCellnameCluster(item);
                comboBox1.Items.Add(item);
            }

            ClusterinfoList.Sort(delegate(ClusterInfoMR a, ClusterInfoMR b) { return b.Iclusterid.CompareTo(a.Iclusterid); });

            List<ClusterManager> newCMList = batData();

            //开始对簇进行分组
            Dictionary<int, List<ClusterManager>> cluM = batData2(newCMList);

            //清除区域外的簇信息
            ClustershowList.RemoveAll(delegate(ClusterShow p) { return p.GroupId == 0; });


            mainModel.ClusterManagerList.Clear();
            //处理绘制区域
            foreach (int clukey in cluM.Keys)
            {
                foreach (ClusterManager item in cluM[clukey])
                {
                    mainModel.ClusterManagerList.Add(item);
                }
            }
            gridControlShow.DataSource = ClustershowList;
            gridControlShow.RefreshDataSource();
            mainModel.MainForm.GetMapForm().FireDoShowClusterCellGridMR();
            xtraTabControl1.SelectedTabPageIndex = 0;
        }

        private void removeSameCellnameCluster(string item)
        {
            ClusterinfoList.RemoveAll(delegate (ClusterInfoMR p) { return p.Strcellname == item; });
        }

        public List<ClusterManager> batData()
        {
            List<ClusterManager> managerList = new List<ClusterManager>();
            ClusterManager manager = new ClusterManager();
            int tmpInt = 0;

            Dictionary<string, int> cellnum = new Dictionary<string, int>();

            foreach (ClusterInfoMR info in ClusterinfoList)
            {

                if (!cellnum.ContainsKey(info.Strcellname))
                {
                    cellnum.Add(info.Strcellname, 1);
                    info.Num = 1;
                }
                else
                {
                    cellnum[info.Strcellname] += 1;
                    info.Num = cellnum[info.Strcellname];
                }


                if (tmpInt == 0)
                {
                    manager.Iclusterid = info.Iclusterid;
                    manager.Strbandtype = info.Strbandtype;
                    ClusterManager temp = mainModel.ClusterManagerList.Find(delegate(ClusterManager p) { return p.Iclusterid == manager.Iclusterid; });
                    manager.Color =temp==null? getRandomColor():temp.Color;
                    
                    manager.ClusterinfoMR.Add(info);
                    tmpInt = info.Iclusterid;
                }
                else if (tmpInt == info.Iclusterid)
                {
                    manager.ClusterinfoMR.Add(info);
                }
                else if (tmpInt != info.Iclusterid)
                {
                    managerList.Add(manager);
                    manager = new ClusterManager();
                    manager.Iclusterid = info.Iclusterid;
                    manager.Strbandtype = info.Strbandtype;
                    ClusterManager temp = mainModel.ClusterManagerList.Find(delegate(ClusterManager p) { return p.Iclusterid == manager.Iclusterid; });
                    manager.Color = temp == null ? getRandomColor() : temp.Color;
                    manager.ClusterinfoMR.Add(info);
                    tmpInt = info.Iclusterid;
                }

            }
            if (manager != null)
                managerList.Add(manager);
            return managerList;
        }

        private Color getRandomColor()
        {
            Random random = new Random();
            Color color = Color.FromArgb(random.Next(255), random.Next(255), random.Next(255));
            return color;
        }


        int Dstutas = -1;
        //对簇进行分组
        public Dictionary<int, List<ClusterManager>> batData2(List<ClusterManager> cmList)
        {
            //开始对簇进行分组
            List<ClusterInfoMR> cimrNameList = new List<ClusterInfoMR>();//单次重复小区最高频率集合

            //按重复最高小区组合簇组
            ClusterInfoMR maxciMR = getMaxCellToList(cmList, cimrNameList);
            cimrNameList.Add(maxciMR);

            List<ClusterManager> testCMList1 = getMaxCell(cmList, cimrNameList);
            //检索本簇组最大重复小区
            ClusterInfoMR maxciMR1 = getMaxCellToList(testCMList1, cimrNameList);
            //2次按重复最高小区组合簇组
            cimrNameList.Clear();
            cimrNameList.Add(maxciMR);
            cimrNameList.Add(maxciMR1);
            List<ClusterManager> testCMList2 = getMaxCell(testCMList1, cimrNameList);
            //2次检索本簇组最大重复小区
            ClusterInfoMR maxciMR2 = getMaxCellToList(testCMList2, cimrNameList);
            //3次按重复最高小区组合簇组
            cimrNameList.Clear();
            cimrNameList.Add(maxciMR);
            cimrNameList.Add(maxciMR1);
            cimrNameList.Add(maxciMR2);
            List<ClusterManager> testCMList3 = getMaxCell(testCMList2, cimrNameList);

            //结束对簇进行分组

            foreach (ClusterInfoMR item1 in cimrNameList)
            {
                clearCell.Add(item1.Strcellname);//添加
            }

            clusterManagerDic.Add(++j, testCMList3);

            //对界面数据进行处理
            foreach (ClusterManager cmUI in testCMList3)
            {
                setClusterShowGroupId(cmUI);

                setClusterManagerMaxCell(cimrNameList, cmUI);
            }

            cmList.RemoveAll(delegate(ClusterManager p)
            {
                return p.ClusterinfoMR.Exists(delegate(ClusterInfoMR o)
                {
                    return clearCell.Contains(o.Strcellname);
                });
            });

            if (cmList.Count != 0)
            {
                if (Dstutas != cmList.Count)
                {
                    Dstutas = cmList.Count;
                    batData2(cmList);// 递归
                }
                else
                {
                    //所有簇分组完成
                    foreach (ClusterManager itemcm in cmList)
                    {
                        List<ClusterManager> tempCM = new List<ClusterManager>();
                        tempCM.Add(itemcm);
                        clusterManagerDic.Add(++j, tempCM);
                    }
                    return clusterManagerDic;
                }

            }

            return clusterManagerDic;
        }

        private void setClusterManagerMaxCell(List<ClusterInfoMR> cimrNameList, ClusterManager cmUI)
        {
            ClusterManager cmtemp = clustermanagerList.Find(delegate (ClusterManager p) { return p.Iclusterid == cmUI.Iclusterid; });
            if (cmtemp != null)
            {
                cmtemp.MaxCell = cimrNameList;
            }
        }

        private void setClusterShowGroupId(ClusterManager cmUI)
        {
            ClusterShow tempcs = ClustershowList.Find(delegate (ClusterShow p) { return p.Iclusterid == cmUI.Iclusterid; });
            if (tempcs != null)
            {
                tempcs.GroupId = j;
            }
        }

        /// <summary>
        /// 检索,找到包含出现频率最高小区的簇集合
        /// </summary>
        /// <param name="clusterManagerList"></param>
        /// <param name="no1"></param>
        /// <returns></returns>
        public List<ClusterManager> getMaxCell(List<ClusterManager> clusterManagerList, List<ClusterInfoMR> no1)
        {
            List<ClusterManager> List1 = clusterManagerList.FindAll(delegate(ClusterManager p)
            {
                return ExistsCell(p, no1);
            });
            return List1;
        }

        public bool ExistsCell(ClusterManager cm, List<ClusterInfoMR> cellnames)
        {
            if (cellnames.Count > 3)
            {
                return false;
            }

            bool isAllExists = true;
            for (int i = 0; i < cellnames.Count; i++)
            {
                bool isExists = false;
                foreach (var item in cm.ClusterinfoMR)
                {
                    if (item.Strcellname == cellnames[i].Strcellname)
                    {
                        isExists = true;
                        break;
                    }
                }
                if (!isExists)
                {
                    isAllExists = false;
                    break;
                }
            }
            return isAllExists;
        }

        /// <summary>
        /// 对以检索过的簇集合计算小区频率
        /// </summary>
        /// <returns></returns>
        public ClusterInfoMR getMaxCellToList(List<ClusterManager> clusterManagerList, List<ClusterInfoMR> cellnames)
        {
            List<ClusterInfoMR> cellnum = new List<ClusterInfoMR>();
            foreach (ClusterManager item in clusterManagerList)
            {
                foreach (ClusterInfoMR mr in item.ClusterinfoMR)
                {
                    if (cellnum.Exists(delegate(ClusterInfoMR p) { return (p.Strcellname == mr.Strcellname); }))
                    {
                        cellnum.Find(delegate(ClusterInfoMR p) { return (p.Strcellname == mr.Strcellname); }).Num += 1;
                    }
                    else
                    {
                        ClusterInfoMR cimr = new ClusterInfoMR();
                        cimr.Strcellname = mr.Strcellname;
                        cimr.Num = 1;
                        cellnum.Add(cimr);
                    }
                }
            }
            cellnum.Sort(delegate(ClusterInfoMR a, ClusterInfoMR b) { return b.Num.CompareTo(a.Num); });
            for (int i = 1; i < cellnum.Count; i++)
            {

                if (!cellnames.Exists(delegate(ClusterInfoMR p) { return p.Strcellname == cellnum[i].Strcellname; }))
                {
                    if (cellnames.Count == 0)
                        return cellnum[0];
                    return cellnum[i];
                }
            }

            return cellnum[1];
        }
        
    }
}