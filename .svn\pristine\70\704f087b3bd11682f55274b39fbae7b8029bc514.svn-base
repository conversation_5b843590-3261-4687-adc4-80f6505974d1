﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using System.Runtime.InteropServices;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class MonthControl : UserControl
    {
        private bool boolyear = false;
        private bool boolmonth = false;
        private string dtLight = DateTime.Now.ToShortDateString();
        
        public List<string> PartANoneLst { get; set; }
        public List<string> PartBNoneLst { get; set; }
        public List<string> PartBothNoneLst { get; set; }
        public List<string> PartBothDoneLst { get; set; }

        [DllImport("user32")]
        private static extern bool AnimateWindow(IntPtr hwnd, int dwTime, int dwFlags);

        private const int AW_SLIDE = 0x40000;
        private const int AW_HOR_NEGATIVE = 0x0002;
        private const int AW_VER_NEGATIVE = 0x0008;

        private EventHandler lableClickEventHandle = null;

        public MonthControl()
        {
            this.PartANoneLst = new List<string>();
            this.PartBNoneLst = new List<string>();
            this.PartBothNoneLst = new List<string>();
            this.PartBothDoneLst = new List<string>();

            this.SetStyle(ControlStyles.AllPaintingInWmPaint, true);
            this.SetStyle(ControlStyles.DoubleBuffer, true);
            this.SetStyle(ControlStyles.UserPaint, true);
            this.SetStyle(ControlStyles.ResizeRedraw, true);

            InitializeComponent();

            blind();
            CreatCalendar(Convert.ToInt32(cbb_year.SelectedValue), Convert.ToInt32(cbb_month.SelectedValue));
            boolyear = true;
            boolmonth = true;
        }

        public MonthControl(EventHandler lableClickEventHandle)
            : this()
        {
            this.lableClickEventHandle = lableClickEventHandle;
        }

        public void SetLableEventHandle(EventHandler lableClickEventHandle)
        {
            this.lableClickEventHandle = lableClickEventHandle;
        }

        public void FillData(List<string> aNoLst, List<string> bNoLst, List<string> bothNoLst, List<string> bothDoneLst)
        {
            this.PartANoneLst = aNoLst;
            this.PartBNoneLst = bNoLst;
            this.PartBothNoneLst = bothNoLst;
            this.PartBothDoneLst = bothDoneLst;
        }

        public void RefreshData()
        {
            this.cbb_month_TextChanged(null, null);
        }


        public void GoToday(string dayString)
        {
            DateTime dt;
            if (!DateTime.TryParse(dayString, out dt))
                return;

            cbb_year.SelectedItem = dt.Year;
            cbb_month.SelectedItem = dt.Month;
            this.panel_calendara.Visible = false;
            panel_calendara.Controls.Clear();
            dtLight = dt.ToShortDateString();
            CreatCalendar(Convert.ToInt32(dt.Year), Convert.ToInt32(dt.Month));
            System.Threading.Thread.Sleep(500);
            this.panel_calendara.Visible = true;
        }

        //绑定年月的下拉框
        private void blind()
        {
            Data data = new Data();
            cbb_year.DropDownStyle = ComboBoxStyle.DropDownList;
            cbb_year.DataSource = data.Year();
            cbb_year.Text = DateTime.Now.Year.ToString();
            
            cbb_month.DropDownStyle = ComboBoxStyle.DropDownList;
            cbb_month.DataSource = data.Month();
            cbb_month.Text = DateTime.Now.Month.ToString();
        }

        //动态生成当年月的日历
        private void CreatCalendar(int year, int month)
        {
            lbl_year.Text = year.ToString();
            lbl_month.Text = month.ToString();
            
            Data data = new Data();
            int totaldayofmonth = data.GetMonthCount(year, month);
            //公历日
            Label[] lbl_array_gl = new Label[totaldayofmonth];
            for (int k = 0; k < totaldayofmonth; k++)
            {
                lbl_array_gl[k] = new Label();
                lbl_array_gl[k].Text = (k + 1).ToString();
                lbl_array_gl[k].Name = "lbl_gl" + (k + 1).ToString();
            }
            //农历日
            Label[] lbl_array_nl = new Label[totaldayofmonth];
            for (int q = 0; q < totaldayofmonth; q++)
            {
                lbl_array_nl[q] = new Label();
                lbl_array_nl[q].Name = "lbl_nl" + (q + 1).ToString();
            }
            int firstdayofweekofmonth = data.FirstDayOfWeekOfMonth(year, month);
            int index = 0;
            int intervalX = lbl_yi.Location.X - lbl_ri.Location.X;
            for (int i = 0; i < 6; i++)
            {
                for (int j = 0; j < 7; j++)
                {
                    if ((i != 0 || j >= firstdayofweekofmonth) && index < totaldayofmonth)
                    {
                        string strDtLbl = new DateTime(year, month, Convert.ToInt32(lbl_array_gl[index].Text)).ToShortDateString();
                        this.panel_calendara.Controls.Add(lbl_array_gl[index]);
                        this.panel_calendara.Controls.Add(lbl_array_nl[index]);
                        lbl_array_gl[index].AutoSize = true;

                        lbl_array_gl[index].Location = new Point(intervalX * j + 15, i * 36 + 10);
                        lbl_array_gl[index].BackColor = Color.Transparent;
                        lbl_array_gl[index].Click += lableClickEventHandle;
                        lbl_array_gl[index].Tag = strDtLbl;

                        lbl_array_nl[index].AutoSize = true;
                        lbl_array_nl[index].Text = data.GetNLDay(year, month, Convert.ToInt32(lbl_array_gl[index].Text));
                        lbl_array_nl[index].Location = new Point(intervalX * j + 5, i * 36 + 25);
                        lbl_array_nl[index].BackColor = Color.Transparent;

                        setLableColor(lbl_array_gl, index, strDtLbl);
                        index++;
                    }
                }
            }
        }

        private void setLableColor(Label[] lbl_array_gl, int index, string strDtLbl)
        {
            if (PartBothDoneLst.Contains(strDtLbl))
            {
                lbl_array_gl[index].BackColor = Color.Green;
            }
            else if (PartANoneLst.Contains(strDtLbl))
            {
                lbl_array_gl[index].BackColor = Color.Yellow;
            }
            else if (PartBNoneLst.Contains(strDtLbl))
            {
                lbl_array_gl[index].BackColor = Color.BlueViolet;
            }
            else if (PartBothNoneLst.Contains(strDtLbl))
            {
                lbl_array_gl[index].BackColor = Color.OrangeRed;
            }

            if (strDtLbl == dtLight)
            {
                lbl_array_gl[index].ForeColor = Color.Aqua;
            }
        }

        private void btn_pre_Click(object sender, EventArgs e)
        {
            if (cbb_year.SelectedIndex >= 0)
            {
                if (cbb_month.SelectedIndex != 0)
                {
                    cbb_month.SelectedIndex--;
                }
                else
                {
                    if (cbb_year.SelectedIndex != 0)
                    {
                        cbb_month.SelectedIndex = cbb_month.Items.Count - 1;
                        cbb_year.SelectedIndex--;
                    }
                }
            }
        }

        private void btn_back_Click(object sender, EventArgs e)
        {
            if (cbb_year.SelectedIndex <= cbb_year.Items.Count - 1)
            {
                if (cbb_month.SelectedIndex != cbb_month.Items.Count - 1)
                {
                    cbb_month.SelectedIndex++;
                }
                else
                {
                    if (cbb_year.SelectedIndex != cbb_year.Items.Count - 1)
                    {
                        cbb_month.SelectedIndex = 0;
                        cbb_year.SelectedIndex++;
                    }
                }
            }
        }

        private void cbb_year_TextChanged(object sender, EventArgs e)
        {
            if (boolyear)
            {
                this.panel_calendara.Visible = false;
                panel_calendara.Controls.Clear();
                dtLight = DateTime.Now.ToShortDateString();
                CreatCalendar(Convert.ToInt32(cbb_year.SelectedValue), Convert.ToInt32(cbb_month.SelectedValue));
                System.Threading.Thread.Sleep(500);
                this.panel_calendara.Visible = true;
            }
        }

        private void cbb_month_TextChanged(object sender, EventArgs e)
        {
            if (boolmonth)
            {
                this.panel_calendara.Visible = false;
                panel_calendara.Controls.Clear();
                dtLight = DateTime.Now.ToShortDateString();
                CreatCalendar(Convert.ToInt32(cbb_year.SelectedValue), Convert.ToInt32(cbb_month.SelectedValue));
                System.Threading.Thread.Sleep(500);
                this.panel_calendara.Visible = true;
                AnimateWindow(this.panel_calendara.Handle, 1000, AW_SLIDE | AW_HOR_NEGATIVE | AW_VER_NEGATIVE);
            }
        }
    }


}
