﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTFileCompare;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYTestPlanQuery:DIYSQLBase
    {
        readonly DateTime sTime;
        readonly DateTime eTime;
        public ZTDIYTestPlanQuery(DateTime sTime, DateTime eTime)
            : base(MainModel.GetInstance())
        {
            MainDB = true;
            this.sTime = sTime;
            this.eTime = eTime;
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 18000, 18027, this.Name);
        }
        protected override string getSqlTextString()
        {
            string selSql = "select [gridname],[cityname],[voicedevice],[datadevice],[stime],[etime],[project],[comment] from tb_testplan_info where ";
            string cond = " stime>={0} and etime<={1}";
            return string.Format(selSql + cond, (int)(JavaDate.GetMilliseconds(sTime) / 1000), (int)(JavaDate.GetMilliseconds(eTime) / 1000));
        }

        protected override Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            E_VType[] arr = new E_VType[8];
            int idx = 0;
            arr[idx++] = E_VType.E_String;
            arr[idx++] = E_VType.E_String;
            arr[idx++] = E_VType.E_String;
            arr[idx++] = E_VType.E_String;
            arr[idx++] = E_VType.E_Int;
            arr[idx++] = E_VType.E_Int;
            arr[idx++] = E_VType.E_String;
            arr[idx] = E_VType.E_String;
            return arr;
        }

        public List<TestPlan_Beijing> Plans { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Plans = new List<TestPlan_Beijing>();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    Plans.Add(new TestPlan_Beijing(package.Content));
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

    }
}
