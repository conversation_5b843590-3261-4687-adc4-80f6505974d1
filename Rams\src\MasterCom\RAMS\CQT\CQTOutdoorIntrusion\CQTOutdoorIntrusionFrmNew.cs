﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using CQTLibrary.PublicItem;
using MasterCom.RAMS.Model;
using Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTOutdoorIntrusionFrmNew : Form
    {
        public CQTOutdoorIntrusionFrmNew(MainModel Mainmodel,string netWoker)
        {
            InitializeComponent();
            mainmodel = Mainmodel;
            this.Text = netWoker + this.Text;
        }
        MainModel mainmodel = null;
        
        List<CQTInvadeCoverItem> touinvadeCoverList = new List<CQTInvadeCoverItem>();
        List<CQTCellSetSubInfo> weicellSetList = new List<CQTCellSetSubInfo>();

        public List<CQTInvadeCoverItem> InvadeCoverList { get; set; } = new List<CQTInvadeCoverItem>();

        public new void Refresh()
        {
            touinvadeCoverList = InvadeCoverList;
            this.gridControl1.DataSource = InvadeCoverList;
            this.gridControl1.RefreshDataSource();
        }

        private void gridView1_SelectionChanged(object sender, DevExpress.Data.SelectionChangedEventArgs e)
        {
            if (gridView1.SelectedRowsCount > 0)
            {
                List<CQTCellSetSubInfo> cellSetList = new List<CQTCellSetSubInfo>();
                int[] rows = this.gridView1.GetSelectedRows();
                foreach (int i in rows)
                {
                    CQTInvadeCoverItem coverItem = this.gridView1.GetRow(i) as CQTInvadeCoverItem;
                    cellSetList.AddRange(coverItem.cellSetList);
                }
                for (int i = 0; i < cellSetList.Count; i++)
                {
                    Cell cell = null;
                    cell = mainmodel.CellManager.GetCurrentCell(cellSetList[i].Ilac, cellSetList[i].Ici);
                    if (cell != null)
                        cellSetList[i].Strcellname = cell.Name;
                }
                this.gridControl2.DataSource = cellSetList;
                this.gridControl2.RefreshDataSource();
            }
        }

        private int tounum(int id)
        {
            List<CQTCellSetSubInfo> cellSetList = new List<CQTCellSetSubInfo>();
            CQTInvadeCoverItem coverItem = this.gridView1.GetRow(id) as CQTInvadeCoverItem;
            cellSetList.AddRange(coverItem.cellSetList);
            return cellSetList.Count;
        }

        private void ExportExcel()
        {
            Microsoft.Office.Interop.Excel.Application app = null;
            try
            {
                app = new Microsoft.Office.Interop.Excel.Application();
                if (app == null)
                {
                    throw (new Exception("ERROR: EXCEL couldn't be started!"));
                }
                app.Visible = false;
                app.UserControl = false;
                Workbooks workbooks = app.Workbooks;
                _Workbook workbook = workbooks.Add(XlWBATemplate.xlWBATWorksheet);
                Sheets sheets = workbook.Worksheets;
                _Worksheet worksheet = (_Worksheet)sheets.get_Item(1);
                if (worksheet == null)
                {
                    throw (new Exception("ERROR: worksheet == null"));
                }
                worksheet.Name = "GSM覆盖入侵表";
                //==== 列标题
                int idx = 1;
                makeTitle(worksheet, 1, idx++, "序号", 6, false);
                makeTitle(worksheet, 1, idx++, "CQT地点名称", 12, false);
                makeTitle(worksheet, 1, idx++, "入侵类型", 10, false);
                makeTitle(worksheet, 1, idx++, "室内小区数", 10, false);
                makeTitle(worksheet, 1, idx++, "室外小区数", 10, false);
                makeTitle(worksheet, 1, idx++, "小区名称", 10, false);
                makeTitle(worksheet, 1, idx++, "LAC", 6, false);
                makeTitle(worksheet, 1, idx++, "CI", 6, false);
                makeTitle(worksheet, 1, idx++, "站点类型", 8, false);
                makeTitle(worksheet, 1, idx++, "覆盖采样点数", 12, false);
                makeTitle(worksheet, 1, idx++, "主服采样点数", 12, false);
                makeTitle(worksheet, 1, idx++, "主服采样点占比", 14, false);
                makeTitle(worksheet, 1, idx++, "主服平均电平(dBm)", 12, false);
                makeTitle(worksheet, 1, idx++, "电平最大值(dBm)", 16, false);
                makeTitle(worksheet, 1, idx++, "电平最小值(dBm)", 16, false);
                makeTitle(worksheet, 1, idx, "电平平均值(dBm)", 16, false);

                int rowAt = 2; int idxx = 0;
                foreach (CQTInvadeCoverItem ts in touinvadeCoverList)
                {
                    int Id = tounum(idxx);
                    for (int i = 0; i < Id; i++)
                    {
                        int xx = 1;
                        makeItemRow(worksheet, rowAt, xx++, ts.StrNo);
                        makeItemRow(worksheet, rowAt, xx++, ts.StrCqtName);
                        makeItemRow(worksheet, rowAt, xx++, ts.StrProType);
                        makeItemRow(worksheet, rowAt, xx++, ts.IIndoorCellNum.ToString());
                        makeItemRow(worksheet, rowAt, xx, ts.IOutdoorCellNum.ToString());
                        rowAt++;
                    }
                    idxx++;

                }
                rowAt = 2;
                foreach (CQTCellSetSubInfo ws in weicellSetList)
                {
                    int xx = 6;
                    makeItemRow(worksheet, rowAt, xx++, ws.Strcellname);
                    makeItemRow(worksheet, rowAt, xx++, ws.Ilac.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ws.Ici.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ws.Strtype);
                    makeItemRow(worksheet, rowAt, xx++, ws.Isamplenum.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ws.IServerSampleNum.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ws.StrMainPointProportion);
                    makeItemRow(worksheet, rowAt, xx++, ws.StrMainPointAvgRxlev.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ws.IMaxRxlev.ToString());
                    makeItemRow(worksheet, rowAt, xx++, ws.IMinRxlev.ToString());
                    makeItemRow(worksheet, rowAt, xx, ws.IAvgRxlev.ToString());
                    
                    rowAt++;
                }
                app.Visible = true;
                app.UserControl = true;
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(this, "导出 Excel 出错：" + ex.Message);
            }
        }

        public void makeTitle(_Worksheet worksheet, int row, int col, string title, int width, bool wraptext)
        {
            Range range = worksheet.Cells[row, col] as Range;
            range.Value2 = title;
            //  range.Font.Bold = true;
            range.ColumnWidth = width;
            range.WrapText = wraptext;
        }

        public void makeItemRow(_Worksheet worksheet, int row, int column, string str)
        {
            Range range = worksheet.Cells[row, column] as Range;
            range.Value2 = str;
        }

        private void EXCELToolStripMenuItem_Click(object sender, EventArgs e)
        {
            weicellSetList.Clear();
            for (int i = 0; i < touinvadeCoverList.Count; i++)
            {
                //object o = gridView1.GetRow(i);
                CQTInvadeCoverItem coverItem = this.gridView1.GetRow(i) as CQTInvadeCoverItem;
                weicellSetList.AddRange(coverItem.cellSetList);
                //for (int j = 0; j < weicellSetList.Count; j++)
                //{
                //    Cell cell = null;
                //    cell = mainmodel.CellManager.GetCurrentCell(weicellSetList[j].Ilac, weicellSetList[j].Ici);
                //    if (cell != null)
                //        weicellSetList[i].Strcellname = cell.Name;
                //}
            }
            ExportExcel();
        }

    }
}
