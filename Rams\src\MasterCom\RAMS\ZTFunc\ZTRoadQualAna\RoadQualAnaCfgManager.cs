﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using System.Drawing;
using MasterCom.RAMS.Net;
using MasterCom.MControls;

namespace MasterCom.RAMS.ZTFunc
{
    public class RoadQualAnaCfgManager
    {
        private static RoadQualAnaCfgManager instance = null;
        private RoadQualAnaCfgManager()
        {
            cfgFileName = System.Windows.Forms.Application.StartupPath + "/config/RoadQualAna.xml";
            LoadCfg();
            setColorMode();
        }
        private void setColorMode()
        {
            if (RoadQualAnaSetting.GridColorModeList.Count > 0)
            {
                return;
            }
            List<RoadQualAnaColorModeItem> gridColorModeList = new List<RoadQualAnaColorModeItem>();
            RoadQualAnaSetting.GridColorModeList = gridColorModeList;

            RoadQualAnaColorModeItem rsrpColor = new RoadQualAnaColorModeItem("RSRP", this.Formula_LteRsrp, -141, 25);
            rsrpColor.ColorRanges.Add(new ColorRange(-141, -100, Color.Red));
            rsrpColor.ColorRanges.Add(new ColorRange(-100, -90, Color.Yellow));
            rsrpColor.ColorRanges.Add(new ColorRange(-90, 25, Color.Green));
            gridColorModeList.Add(rsrpColor);

            RoadQualAnaColorModeItem sinrColor = new RoadQualAnaColorModeItem("SINR", this.Formula_LteSinr, -50, 50);
            sinrColor.ColorRanges.Add(new ColorRange(-50, -10, Color.Red));
            sinrColor.ColorRanges.Add(new ColorRange(-10, 10, Color.Yellow));
            sinrColor.ColorRanges.Add(new ColorRange(10, 50, Color.Green));
            gridColorModeList.Add(sinrColor);

            RoadQualAnaColorModeItem lteCoverRateColor = new RoadQualAnaColorModeItem("LTE综合覆盖率", this.Formula_LteCoverRate, 0, 100);
            lteCoverRateColor.ColorRanges.Add(new ColorRange(0, 10, Color.Red));
            lteCoverRateColor.ColorRanges.Add(new ColorRange(10, 30, Color.Yellow));
            lteCoverRateColor.ColorRanges.Add(new ColorRange(30, 100, Color.Green));
            gridColorModeList.Add(lteCoverRateColor);

            RoadQualAnaColorModeItem rxlevColor = new RoadQualAnaColorModeItem("RxLev", this.Formula_GsmRxlev, -120, -10);
            rxlevColor.ColorRanges.Add(new ColorRange(-120, -100, Color.Red));
            rxlevColor.ColorRanges.Add(new ColorRange(-100, -90, Color.Yellow));
            rxlevColor.ColorRanges.Add(new ColorRange(-90, -10, Color.Green));
            gridColorModeList.Add(rxlevColor);

            RoadQualAnaColorModeItem abnormalPerColor = new RoadQualAnaColorModeItem("异常概率", this.Formula_AbnomalPer, 0, 100);
            abnormalPerColor.ColorRanges.Add(new ColorRange(0, 10, Color.Green));
            abnormalPerColor.ColorRanges.Add(new ColorRange(10, 30, Color.Yellow));
            abnormalPerColor.ColorRanges.Add(new ColorRange(30, 100, Color.Red));
            gridColorModeList.Add(abnormalPerColor);
        }
        public static RoadQualAnaCfgManager GetInstance()
        {
            if (instance==null)
            {
                instance = new RoadQualAnaCfgManager();
            }
            return instance;
        }
        
        public RoadQualAnaCond RoadQualAnaSetting { get; set; } = new RoadQualAnaCond();
        public string RoadAnaBaseDBName { get; set; } = "ROADANA_GUANGZHOU";

        public string Formula_AbnomalPer { get; set; } = "Formula_AbnomalPer";
        public string Formula_GsmRxlev { get; set; } = "Formula_GsmRxlev";

        public string Formula_LteRsrp { get; set; } = "Formula_LteRsrp";
        public string Formula_LteSinr { get; set; } = "Formula_LteSinr";
        public string Formula_LteCoverRate { get; set; } = "Formula_LteCoverRate";

        private Dictionary<string, object> cfgParam
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["RoadAnaBaseDBName"] = RoadAnaBaseDBName;
                param["RoadQualAnaSetting"] = RoadQualAnaSetting.Param;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                RoadAnaBaseDBName = param["RoadAnaBaseDBName"] as string;
                if (param.ContainsKey("RoadQualAnaSetting"))
                {
                    RoadQualAnaSetting.Param = param["RoadQualAnaSetting"] as Dictionary<string, object>;
                }
            }
        }

        private readonly string cfgFileName;
        public void LoadCfg()
        {
            if (File.Exists(cfgFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(cfgFileName);
                cfgParam = configFile.GetItemValue("RoadQualAna", "Setting") as Dictionary<string, object>;
            }
        }
        public void Save()
        {
            MasterCom.Util.XmlConfigFile xmlFile = new MasterCom.Util.XmlConfigFile();
            XmlElement cfgE = xmlFile.AddConfig("RoadQualAna");
            xmlFile.AddItem(cfgE, "Setting", this.cfgParam);
            xmlFile.Save(cfgFileName);
        }
    }
}
