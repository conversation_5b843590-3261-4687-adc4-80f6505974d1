﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Text.RegularExpressions;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCsfbCellJudgeBaseNew : DIYAnalyseByFileBackgroundBase
    {
        protected int curLteServiceType = (int)ServiceType.LTE_TDD_VOICE;
        protected int curFallServiceType = (int)ServiceType.GSM_VOICE;
        protected int EvtIdMtCsfbLteRelease = 886;
        protected int EvtIdMoCsfbLteRelease = 878;
        protected int EvtIdLte_CellReselection_L2G = 1300;
        protected bool isFDD = false;

        public ZTCsfbCellJudgeSetRadiusCondition hoCondition { get; set; }
        public ZTCsfbCellJudgeNew clJudge { get; set; }

        public Dictionary<string, int> levDc { get; set; }  //统计各采样点小区和各自的电平和
        public Dictionary<string, int> cellCountlist { get; set; }  //统计每个小区影响的采样点个数
        public List<ZTCsfbCellJudgeNew> resultList { get; set; }//保存结果

        protected ICell iCell = null;

        public ZTCsfbCellJudgeBaseNew(MainModel mainModel)
            : base(mainModel)
        {
            IncludeMessage = true;
            hoCondition = new ZTCsfbCellJudgeSetRadiusCondition();
            clJudge = new ZTCsfbCellJudgeNew();
        }

        /// <summary>
        /// 对同一网格的csfb文件和gsm文件进行关联
        /// </summary>
        protected override void analyseFiles()
        {
            Dictionary<FileInfo, List<FileInfo>> moMtPair = new Dictionary<FileInfo, List<FileInfo>>();
            foreach (FileInfo fileInfo in MainModel.FileInfos)
            {
                if (fileInfo.ServiceType == curLteServiceType)   //csfb文件关联到gsm文件
                {
                    List<FileInfo> mtFileList = getMTFileList(fileInfo);
                    if (mtFileList != null && mtFileList.Count != 0)
                    {
                        moMtPair[fileInfo] = mtFileList;
                    }
                }
            }
            try
            {
                AnalyseMoMtPair(moMtPair);
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        private void AnalyseMoMtPair(Dictionary<FileInfo, List<FileInfo>> moMtPair)
        {
            clearDataBeforeAnalyseFiles();
            int iloop = 0;
            foreach (KeyValuePair<FileInfo, List<FileInfo>> pair in moMtPair)
            {
                WaitBox.Text = "正在分析(" + (++iloop) + "/" + moMtPair.Count + ")...";
                WaitBox.ProgressPercent = (int)(iloop * 100.0 / moMtPair.Count);

                condition.FileInfos.Clear();
                if (pair.Key != null)
                {
                    condition.FileInfos.Add(pair.Key);
                }
                if (pair.Value.Count != 0 || pair.Value != null)
                {
                    foreach (FileInfo file in pair.Value)
                    {
                        condition.FileInfos.Add(file);
                    }
                }
                replay();
                condition.FileInfos.Clear();
                if (WaitBox.CancelRequest)
                {
                    break;
                }
            }
            MainModel.ClearDTData();
            doSomethingAfterAnalyseFiles();
        }

        private List<FileInfo> getMTFileList(FileInfo fileInfo)
        {
            List<FileInfo> mtFileList = MainModel.FileInfos.FindAll(delegate (FileInfo x)
            {
                #region  汉字网格
                List<string> netList = new List<string>{"昌平大学城","昌平区1号","城关","大兴区1号","房山区1号","怀柔区1号","怀柔区2号","回龙观","马驹桥",
                                                "门头沟区1号","密云县1号","密云县2号","平谷区1号","平谷区2号","平谷区3号","沙峪别墅区","沙峪工业区",
                                                "顺义区1号","顺义区2号","天通苑","天竺","通州区1号","通州区2号","通州区3号","延庆县1号","延庆县2号",
                                                "燕山石化","亦庄核心区1","亦庄核心区2","亦庄核心区3","亦庄路东区","长阳"};
                #endregion
                string netWorkX = Regex.Match(x.Name, @"网格\d+", RegexOptions.None).Value;
                string netWorkFile = Regex.Match(fileInfo.Name, @"网格\d+", RegexOptions.None).Value;
                if (string.IsNullOrEmpty(netWorkFile) && string.IsNullOrEmpty(netWorkX))
                {
                    foreach (string net in netList)
                    {
                        if (fileInfo.Name.Contains(net) && x.Name.Contains(net))
                        {
                            netWorkX = net;
                            netWorkFile = net;
                        }
                    }
                }
                if (string.IsNullOrEmpty(netWorkX) || string.IsNullOrEmpty(netWorkFile))
                {
                    return false;
                }
                if (fileInfo.ServiceType != x.ServiceType && netWorkX == netWorkFile)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            });
            return mtFileList;
        }

        ZTCsfbCellJudgeSetRadiusForm setForm = null;
        protected override bool getCondition()
        {
            initColumns();
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTCsfbCellJudgeSetRadiusForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                resultList = new List<ZTCsfbCellJudgeNew>();
                hoCondition = setForm.GetCondition();
                return true;
            }
            return false;
        }

        protected virtual void initColumns()
        {
            this.Columns = new List<string>();
            #region GSM
            Columns.Add("LAC");
            Columns.Add("CI");
            Columns.Add("BCCH");
            Columns.Add("BSIC");
            Columns.Add("RxLevSub");
            Columns.Add("N_BCCH");
            Columns.Add("N_BSIC");
            Columns.Add("N_RxLev");
            #endregion

            #region LTE
            Columns.Add("lte_gsm_SC_LAC");
            Columns.Add("lte_gsm_SC_CI");
            Columns.Add("lte_gsm_SC_BCCH");
            Columns.Add("lte_gsm_SC_BSIC");
            Columns.Add("lte_gsm_DM_RxLevSub");
            Columns.Add("lte_gsm_NC_BCCH");
            Columns.Add("lte_gsm_NC_BSIC");
            Columns.Add("mode");
            #endregion
        }

        protected override void doStatWithQuery()
        {
            DTFileDataManager moFile = null;
            List<DTFileDataManager> fileList = new List<DTFileDataManager>();

            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file.ServiceType == curLteServiceType)
                {
                    moFile = file;
                }
                else if (file.ServiceType == curFallServiceType)
                {
                    fileList.Add(file);
                }
            }

            List<ZTCsfbCellJudgeNew> moJudges = new List<ZTCsfbCellJudgeNew>();
            judgeBeforeCSFBFile(moFile, fileList, ref moJudges);
            judgeAfterCSFBFiles(fileList, moJudges);
        }

        private void judgeBeforeCSFBFile(DTFileDataManager moFile, List<DTFileDataManager> fileList, ref List<ZTCsfbCellJudgeNew> moJudges)
        {
            if (moFile != null)
            {
                try
                {
                    ZTCsfbCellJudgeNew judge = null;
                    int lastTimeTestPoint = 0;
                    //循环主叫文件所有事件信令采样点数据
                    foreach (DTData data in moFile.DTDatas)
                    {
                        //获取LTE CSFB事件请求,记为回落位置
                        bool isGetJudge = getJudge(moFile, ref moJudges, ref judge, data);
                        if (!isGetJudge && judge != null)
                        {
                            addJudgeInfo(moFile, fileList, ref judge, ref lastTimeTestPoint, data);
                        }
                    }
                }
                catch (Exception e)
                {
                    log.Debug("........", e);
                    throw;
                }

                addDTDatas(moFile, moJudges);
            }
        }

        private bool getJudge(DTFileDataManager moFile, ref List<ZTCsfbCellJudgeNew> moJudges, ref ZTCsfbCellJudgeNew judge, DTData data)
        {
            if (data is Event)
            {
                Event evt = data as Event;
                //MT CSFB LTE Release(被叫CSFB重定向)MO CSFB LTE Release(主叫CSFB重定向)
                if (evt.ID == EvtIdMtCsfbLteRelease || evt.ID == EvtIdMoCsfbLteRelease)
                {
                    addMoJudge(evt, moFile, ref judge, ref moJudges);
                    return true;
                }
            }
            return false;
        }

        private void addJudgeInfo(DTFileDataManager moFile, List<DTFileDataManager> fileList, ref ZTCsfbCellJudgeNew judge, ref int lastTimeTestPoint, DTData data)
        {
            //存在CSFB事件请求则添加对应事件,信令,采样点信息
            if (data is TestPoint)
            {
                judge.AddTestPoint(data as TestPoint);
            }
            else if (data is Model.Message)
            {
                judge.AddMessage(data as Model.Message);
            }
            else if (data is Event)
            {
                Event evt = data as Event;
                if (evt.ID == EvtIdLte_CellReselection_L2G)     //LTE_CellReselection_L2G(LTE回落)
                {
                    cellCountlist = new Dictionary<string, int>();
                    levDc = new Dictionary<string, int>();
                    judge.EndTime = evt.DateTime;
                    //获取回落后的小区
                    iCell = getSrcICellByEvt(evt);
                    if (iCell != null)
                    {
                        //回落前后小区的距离
                        judge.FallDistance = Math.Round(MathFuncs.GetDistance(judge.Longitude, judge.Latitude, iCell.Longitude, iCell.Latitude), 2);
                        List<TestPoint> tpListFall = moFile.TestPoints;
                        lastTimeTestPoint = getFallRxLev(judge, lastTimeTestPoint, evt, tpListFall);
                        judge.AddEvent(evt);
                        GetTable(fileList, judge);
                        judge = null;
                    }
                    else
                    {
                        judge.AddEvent(evt);
                    }
                }
            }
        }

        private int getFallRxLev(ZTCsfbCellJudgeNew judge, int lastTimeTestPoint, Event evt, List<TestPoint> tpListFall)
        {
            //获取回落小区电平
            for (; lastTimeTestPoint < tpListFall.Count - 1; lastTimeTestPoint++)
            {
                TestPoint curTp = tpListFall[lastTimeTestPoint];

                if (curTp.DateTime >= evt.DateTime)
                {
                    bool isOver = getFallRxLevByTP(judge, lastTimeTestPoint, evt, tpListFall, curTp);
                    if (isOver)
                    {
                        break;
                    }
                }
            }

            return lastTimeTestPoint;
        }

        private bool getFallRxLevByTP(ZTCsfbCellJudgeNew judge, int lastTimeTestPoint, Event evt, List<TestPoint> tpListFall, TestPoint curTp)
        {
            int? curTpLac = curTp.GetLAC();
            int? curTpCi = curTp.GetCI();

            if (evt["TargetLAC"] != null && curTpLac != null && evt["TargetCI"] != null && curTpCi != null
                && (int)evt["TargetLAC"] == curTpLac && (int)evt["TargetCI"] == curTpCi)
            {
                TestPoint nextTp = tpListFall[lastTimeTestPoint + 1];
                int? nextTpLac = nextTp.GetLAC();
                int? nextTpCi = nextTp.GetCI();
                if (nextTpLac != null && nextTpCi != null)
                {
                    int? curTpRxlev = (int?)curTp.GetRxlev();
                    if (curTpRxlev == null && curTpLac == nextTpLac && curTpCi == nextTpCi)
                    {
                        //没有获取到电平，但是下个点的LACCI没变继续获取电平
                    }
                    else if (curTpRxlev == null)
                    {
                        //没有获取到电平
                        return true;
                    }
                    else
                    {
                        //获取到回落后电平break
                        judge.FallRxLev = (int)curTpRxlev;
                        return true;
                    }
                }
            }
            return false;
        }

        private void addDTDatas(DTFileDataManager moFile, List<ZTCsfbCellJudgeNew> moJudges)
        {
            foreach (ZTCsfbCellJudgeNew cj in moJudges)
            {
                foreach (DTData dt in moFile.DTDatas)
                {
                    addValidDTData(cj, dt);
                }
            }
        }

        private void addValidDTData(ZTCsfbCellJudgeNew cj, DTData dt)
        {
            if ((cj.BeginTime - dt.DateTime).TotalSeconds <= 3 && (dt.DateTime - cj.EndTime).TotalSeconds <= 3)
            {
                if (dt is Event)
                {
                    Event evt = dt as Event;
                    if (!cj.Events.Contains(evt))
                    {
                        cj.Events.Add(evt);
                    }
                }
                else if (dt is TestPoint)
                {
                    TestPoint tp = dt as TestPoint;
                    if (!cj.TestPoints.Contains(tp))
                    {
                        cj.TestPoints.Add(tp);
                    }
                }
                else if (dt is Model.Message)
                {
                    Model.Message msg = dt as Model.Message;
                    if (!cj.Messages.Contains(msg))
                    {
                        cj.Messages.Add(msg);
                    }
                }
            }
        }

        /// <summary>
        /// 添加回落前相关数据
        /// </summary>
        /// <param name="evt"></param>
        /// <param name="moFile"></param>
        /// <param name="judge"></param>
        /// <param name="moJudges"></param>
        /// <returns></returns>
        private void addMoJudge(Event evt, DTFileDataManager moFile, ref ZTCsfbCellJudgeNew judge, ref List<ZTCsfbCellJudgeNew> moJudges)
        {
            LTECell cell = evt.GetSrcCell() as LTECell;
            if (cell == null)
            {
                return;
            }
            judge = new ZTCsfbCellJudgeNew();
            judge.Longitude = evt.Longitude;
            judge.Latitude = evt.Latitude;
            judge.BeginTime = evt.DateTime;
            judge.AddEvent(evt);
            judge.MoFileName = moFile.FileName;
            judge.LteCellName = cell.Name;
            judge.LteECI = cell.ECI;
            judge.ENBID = (cell.ECI / 256).ToString() + "-" + (cell.ECI % 256).ToString();
            judge.LteTAC = cell.TAC;
            judge.LteLongitude = cell.Longitude;
            judge.LteLatitude = cell.Latitude;
            judge.LTEDistance = Math.Round(cell.GetDistance(evt.Longitude, evt.Latitude), 2);
            moJudges.Add(judge);
        }

        /// <summary>
        /// 给回落结果添加回落后文件数据
        /// </summary>
        /// <param name="fileList"></param>
        /// <param name="moJudges"></param>
        private void judgeAfterCSFBFiles(List<DTFileDataManager> fileList, List<ZTCsfbCellJudgeNew> moJudges)
        {
            ZTCsfbCellJudgeNew judge = null;
            if (fileList.Count != 0)
            {
                foreach (ZTCsfbCellJudgeNew jg in moJudges)
                {
                    foreach (DTFileDataManager mtFile in fileList)
                    {
                        judge = addValidJudgeInfo(judge, jg, mtFile);
                        jg.AddFileName(mtFile.FileName);
                    }
                }
            }
        }

        private ZTCsfbCellJudgeNew addValidJudgeInfo(ZTCsfbCellJudgeNew judge, ZTCsfbCellJudgeNew jg, DTFileDataManager mtFile)
        {
            foreach (DTData data in mtFile.DTDatas)
            {
                if (data is TestPoint)
                {
                    TestPoint tp = data as TestPoint;
                    double distance = MathFuncs.GetDistance(jg.Longitude, jg.Latitude, tp.Longitude, tp.Latitude);
                    if (distance <= (double)hoCondition.Radius)
                    {
                        judge = new ZTCsfbCellJudgeNew();
                        judge.AddTestPoint(tp);
                        jg.AddList(judge);
                    }
                    else
                    {
                        judge = null;
                    }
                }
                else if (data is Event && judge != null)
                {
                    judge.AddEvent(data as Event);
                }
                else if (data is Model.Message && judge != null)
                {
                    judge.AddMessage(data as Model.Message);
                }
            }

            return judge;
        }

        protected virtual ICell getSrcICellByEvt(Event evt)
        {
            return CellManager.GetInstance().GetNearestCell(evt.DateTime, (ushort?)(int?)evt["TargetLAC"]
                , (ushort?)(int?)evt["TargetCI"], evt.Longitude, evt.Latitude);
        }

        private void GetTable(List<DTFileDataManager> fileList, ZTCsfbCellJudgeNew judge)
        {
            try
            {
                //在所有的同车测试文件中查询在回落位置点设置范围以内的小区进行添加(也就是获取文件中回落位置 设置范围以内的所有小区)
                foreach (DTFileDataManager mtFile in fileList)
                {
                    List<TestPoint> tpList = mtFile.TestPoints;

                    dealTestPoint(judge, tpList);
                }

                foreach (KeyValuePair<string, int> pair in cellCountlist)
                {
                    if (levDc.ContainsKey(pair.Key))
                    {
                        levDc[pair.Key] = (int)Math.Round((decimal)levDc[pair.Key] / cellCountlist[pair.Key], 2);

                        judge.AddCellRxLev(pair.Key, levDc[pair.Key]);
                    }
                }
                //将小区中与最强小区电平差大于6DB的去掉
                int maxRxLev = GetMaxValues(levDc);
                double maxLongitude = 0;
                double maxLatitude = 0;
                List<string> lCell = new List<string>();
                foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
                {
                    if (maxRxLev - pair.Value > hoCondition.RxLev)
                    {
                        lCell.Add(pair.Key);
                    }
                }
                foreach (string cl in lCell)
                {
                    judge.RemoveCellRxLev(cl);
                }

                getMaxData(judge, maxRxLev, ref maxLongitude, ref maxLatitude);
                judge.AddSubInfos(isFDD);
                addInfoToResultList(judge, maxRxLev, maxLongitude, maxLatitude);
            }
            catch (Exception e)
            {
                log.Debug("getTable()方法中异常：", e);
                throw;
            }
        }

        private void dealTestPoint(ZTCsfbCellJudgeNew judge, List<TestPoint> tpList)
        {
            for (int i = 0; i < tpList.Count; i++)
            {
                TestPoint curTp = tpList[i];
                int? rxlev = (int?)curTp.GetRxlev();
                if (rxlev != null)
                {
                    ICell mCell = getCell(curTp);
                    addValiddCell(judge, curTp, rxlev, mCell);
                }
            }
        }

        private void addValiddCell(ZTCsfbCellJudgeNew judge, TestPoint curTp, int? rxlev, ICell mCell)
        {
            if (mCell != null)
            {
                //计算与回落位置的距离
                double distance = MathFuncs.GetDistance(judge.Longitude, judge.Latitude, curTp.Longitude, curTp.Latitude);
                //对在覆盖范围内的小区进行添加
                if (distance <= (double)hoCondition.Radius)
                {
                    if (isContainsKey(levDc, mCell))
                    {
                        levDc[mCell.Name] += (int)rxlev;
                        cellCountlist[mCell.Name]++;
                    }
                    else
                    {
                        levDc.Add(mCell.Name, (int)rxlev);
                        cellCountlist.Add(mCell.Name, 1);
                    }

                    if (!judge.Contains(mCell))
                    {
                        judge.AddCell(mCell);
                    }
                    getNBCellInfo(judge, curTp);
                }
            }
        }

        protected virtual ICell getCell(TestPoint curTp)
        {
            return curTp.GetMainCell_GSM();
        }

        protected virtual void getMaxData(ZTCsfbCellJudgeNew judge, int maxRxLev, ref double maxLongitude, ref double maxLatitude)
        {
            foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
            {
                CellManager cm = CellManager.GetInstance();
                Cell cell = cm.GetCellByName(pair.Key);
                if (maxRxLev == pair.Value && cell != null)
                {
                    maxLongitude = cell.Longitude;
                    maxLatitude = cell.Latitude;
                    break;
                }
            }
        }

        protected virtual void getNBCellInfo(ZTCsfbCellJudgeNew judge, TestPoint curTp)
        {
            int gsmCount = 0;
            int dscCount = 0;
            for (int j = 0; j < 32; j++)
            {
                ICell nbCell = curTp.GetNBCell(j);

                int? nRxlev = (int?)curTp.GetNbRxlev(j);
                if (nbCell != null && nRxlev != null)
                {
                    Cell gCell = CellManager.GetInstance().GetCellByName(nbCell.Name);
                    if (gCell != null)
                    {
                        addNBCellInfo(judge, ref gsmCount, ref dscCount, nbCell, nRxlev, gCell);
                    }
                }
                else
                {
                    break;
                }
            }
        }

        private void addNBCellInfo(ZTCsfbCellJudgeNew judge, ref int gsmCount, ref int dscCount, ICell nbCell, int? nRxlev, Cell gCell)
        {
            if (gCell.BCCH >= 1 && gCell.BCCH <= 124)
            {
                gsmCount++;
                if (gsmCount > 16)
                {
                    return;
                }
            }
            else if (gCell.BCCH >= 512 && gCell.BCCH <= 1024)
            {
                dscCount++;
                if (dscCount > 16)
                {
                    return;
                }
            }

            if (isContainsKey(levDc, nbCell))
            {
                levDc[nbCell.Name] += (int)nRxlev;
                cellCountlist[nbCell.Name]++;
            }
            else
            {
                levDc.Add(nbCell.Name, (int)nRxlev);
                cellCountlist.Add(nbCell.Name, 1);
            }

            if (!judge.Contains(nbCell))
            {
                judge.AddCell(nbCell);
            }
        }

        protected virtual void addInfoToResultList(ZTCsfbCellJudgeNew judge, int maxRxLev, double maxLongitude, double maxLatitude)
        {
            Cell cell = iCell as Cell;
            if (cell == null)
            {
                return;
            }
            judge.Sort();
            if (judge.Contains(cell))
            {
                if (isInSix(judge.CellRxLevList, cell))
                {
                    judge.IsInList = true;
                }
                else
                {
                    judge.IsInList = false;
                }
                judge.SN = resultList.Count + 1;
                judge.CellName = cell.Name;
                judge.CellID = cell.CI;
                judge.LAC = cell.LAC;
                judge.FallLongitude = cell.Longitude;
                judge.FallLatitude = cell.Latitude;
                judge.MaxFallDistance = Math.Round(MathFuncs.GetDistance(judge.Longitude, judge.Latitude, maxLongitude, maxLatitude), 2);
                judge.MaxFallRxLev = maxRxLev;
                resultList.Add(judge);
            }
        }

        protected bool isContainsKey(Dictionary<string, int> levDc, ICell cl)
        {
            foreach (KeyValuePair<string, int> pair in levDc)
            {
                if (pair.Key == cl.Name)
                {
                    return true;
                }
            }
            return false;
        }

        protected virtual bool isInSix(Dictionary<string, int> levDc, ICell cl)
        {
            int i = 0;
            foreach (KeyValuePair<string, int> pair in levDc)
            {
                i++;
                if (i <= 6)
                {
                    Cell cell = CellManager.GetInstance().GetCellByName(pair.Key);
                    if (cell != null && cell.CI.ToString() == cl.Code)
                    {
                        return true;
                    }
                }
                else
                {
                    break;
                }
            }
            return false;
        }

        /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            ZTCsfbCellJudgeResultForm frm = MainModel.GetInstance().CreateResultForm(typeof(ZTCsfbCellJudgeResultForm)) as ZTCsfbCellJudgeResultForm;
            frm.FillData(resultList, isFDD);
            frm.Visible = true;
            frm.BringToFront();
            resultList = null;
        }
        protected int GetMaxValues(Dictionary<string, int> dc)
        {
            int max = int.MinValue;
            foreach (int key in dc.Values)
            {
                if (max <= key)
                {
                    max = key;
                }
            }
            return max;
        }
    }

    public class ZTCsfbCellJudgeNew
    {
        private readonly List<ZTCsfbCellJudgeNew> allList = new List<ZTCsfbCellJudgeNew>();
        public List<ZTCsfbCellJudgeNew> AllList
        {
            get { return allList; }
        }
        public void AddList(ZTCsfbCellJudgeNew cj)
        {
            allList.Add(cj);
        }
        public int SN { get; set; }     //序号
        public string MoFileName { get; set; }     //csfb文件名

        private readonly List<string> mtFileNames = new List<string>();    //回落后文件名
        public List<string> MtFileNames
        {
            get { return mtFileNames; }
        }

        public string MtFileName
        {
            get
            {
                StringBuilder sb = new StringBuilder();
                foreach (string name in mtFileNames)
                {
                    sb.Append(string.Format("{0},\r\n", name));
                }
                return sb.ToString();
            }
        }

        public void AddFileName(string fileName)
        {
            mtFileNames.Add(fileName);
        }
        public string CellName { get; set; }      //回落的小区名
        public int LAC { get; set; }
        public int CellID { get; set; }
        public double Latitude { get; set; }       //回落位置的纬度
        public double Longitude { get; set; }      //回落位置的经度
        public DateTime BeginTime { get; set; }    //回落开始的时间
        public DateTime EndTime { get; set; }      //回落结束时间
        public string LteCellName { get; set; }    //回落前4G小区名称
        public int LteECI { get; set; }            //回落前4G小区ECI
        public string ENBID { get; set; }          //ECI/256-LocalCellID
        public int LteTAC { get; set; }            //回落前4G小区TAC
        public double LteLongitude { get; set; }   //回落前4G小区经度
        public double LteLatitude { get; set; }    //回落前4G小区纬度
        public bool IsInList { get; set; }         //是否在前六强小区中

        public string IsInListDisplay               //是否在前六强小区中结果显示
        {
            get
            {
                if (IsInList)
                {
                    return "是";
                }
                else
                {
                    return "否";
                }
            }
        }

        private List<SubZTCsfbCellJudgeNew> subInfos = new List<SubZTCsfbCellJudgeNew>();
        public List<SubZTCsfbCellJudgeNew> SubInfos
        {
            get { return subInfos; }
        }

        public void AddSubInfos(bool isFDD)
        {
            CellManager cm = CellManager.GetInstance();
            List<string> nameList = new List<string>(CellRxLevList.Keys);
            subInfos = new List<SubZTCsfbCellJudgeNew>(nameList.Count);
            SubZTCsfbCellJudgeNew subInfo = null;
            foreach (string name in nameList)
            {
                subInfo = new SubZTCsfbCellJudgeNew();

                if (isFDD)
                {
                    addWcdmaCell(cm, subInfo, name);
                }
                else
                {
                    addGsmDcsCell(cm, subInfo, name);
                }
            }
        }

        private void addWcdmaCell(CellManager cm, SubZTCsfbCellJudgeNew subInfo, string name)
        {
            WCell cell = cm.GetWCellByName(name);
            if (cell != null)
            {
                subInfo.CellName = name;
                subInfo.CellType = "WCDMA小区";
                subInfo.CellID = cell.CI;
                subInfo.PCI = cell.UARFCN;
                subInfo.BSIC = cell.PSC;
                subInfo.RxLev = CellRxLevList[name];

                subInfos.Add(subInfo);
            }
        }

        private void addGsmDcsCell(CellManager cm, SubZTCsfbCellJudgeNew subInfo, string name)
        {
            Cell cell = cm.GetCellByName(name);
            if (cell != null)
            {
                subInfo.CellName = name;
                if (cell.BCCH >= 1 && cell.BCCH <= 124)
                {
                    subInfo.CellType = "GSM小区";
                }
                else if (cell.BCCH >= 512 && cell.BCCH <= 1024)
                {
                    subInfo.CellType = "DCS小区";
                }

                subInfo.CellID = cell.CI;
                subInfo.PCI = cell.BCCH;
                subInfo.BSIC = cell.BSIC;
                subInfo.RxLev = CellRxLevList[name];

                subInfos.Add(subInfo);
            }
        }

        private readonly List<Cell> GSMCell = new List<Cell>();    //所有覆盖回落位置的小区集合
        private readonly List<WCell> WCellList = new List<WCell>();    //所有覆盖回落位置的小区集合
        public void AddCell(ICell cl)
        {
            if (cl is Cell)
            {
                GSMCell.Add(cl as Cell);
            }
            else if (cl is WCell)
            {
                WCellList.Add(cl as WCell);
            }
        }

        public bool Contains(ICell cl)
        {
            if (cl is Cell)
            {
                foreach (Cell gcl in GSMCell)
                {
                    if (gcl.Name == cl.Name)
                    {
                        return true;
                    }
                }
            }
            else if (cl is WCell)
            {
                foreach (WCell wcl in WCellList)
                {
                    if (wcl.Name == cl.Name)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        private readonly Dictionary<string, int> cellReLevList = new Dictionary<string, int>();   //小区及对应的电平
        public void AddCellRxLev(string cl, int cValue)
        {
            cellReLevList.Add(cl, cValue);
        }
        public void RemoveCellRxLev(string cl)
        {
            cellReLevList.Remove(cl);
        }
        public Dictionary<string, int> CellRxLevList
        {
            get { return cellReLevList; }
        }

        public Dictionary<string, int> Sort()
        {
            if (cellReLevList.Count > 0)
            {
                List<KeyValuePair<string, int>> lst = new List<KeyValuePair<string, int>>(cellReLevList);
                lst.Sort(delegate(KeyValuePair<string, int> k1, KeyValuePair<string, int> k2)
                {
                    return k2.Value.CompareTo(k1.Value);
                });
                cellReLevList.Clear();
                foreach (KeyValuePair<string, int> pair in lst)
                {
                    cellReLevList.Add(pair.Key, pair.Value);
                }
                return cellReLevList;
            }
            return cellReLevList;
        }
        public double LTEDistance { get; set; }        //回落前与LTE小区的距离
        public double FallLatitude { get; set; }        //回落后小区的经度
        public double FallLongitude { get; set; }       //回落后小区的纬度
        public double FallRxLev { get; set; }           //回落后小区的电平
        public double FallDistance { get; set; }        //回落后与小区的距离
        public double MaxFallRxLev { get; set; }        //最强小区的电平
        public double MaxFallDistance { get; set; }     //回落位置与最强小区的距离

        private readonly List<Event> evtList = new List<Event>();
        public void AddEvent(Event evt)
        {
            evtList.Add(evt);
        }

        private readonly List<TestPoint> tpList = new List<TestPoint>();
        public void AddTestPoint(TestPoint tp)
        {
            tpList.Add(tp);
        }

        private readonly List<Model.Message> msgList = new List<Model.Message>();
        public void AddMessage(Model.Message msg)
        {
            msgList.Add(msg);
        }
        public List<Event> Events
        {
            get { return evtList; }
        }
        public List<TestPoint> TestPoints
        {
            get { return tpList; }
        }
        public List<Model.Message> Messages
        {
            get { return msgList; }
        }
        public string MoMtDesc { get; set; }
    }

    public class SubZTCsfbCellJudgeNew
    {
        public string CellName { get; set; }
        public string CellType { get; set; }
        public int CellID { get; set; }
        public int RxLev { get; set; }
        public short PCI { get; set; }
        public short BSIC { get; set; }
    }

}
