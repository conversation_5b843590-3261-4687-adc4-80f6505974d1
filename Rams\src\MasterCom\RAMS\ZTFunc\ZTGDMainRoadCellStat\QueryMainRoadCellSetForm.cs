﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class QueryMainRoadCellSetForm : MinCloseForm
    {
        public QueryMainRoadCellSetForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView7;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView8;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView9;
        private DevExpress.XtraTab.XtraTabControl xtraTabCoverMuch;
        private DevExpress.XtraTab.XtraTabPage xtraTabDetail;
        private DevExpress.XtraGrid.GridControl gcDetail;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraTab.XtraTabPage xtraTabSum;
        private DevExpress.XtraGrid.GridControl gcSum;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraTab.XtraTabPage xtraTabTestMuchDetail;
        private DevExpress.XtraGrid.GridControl gcTestDeatail;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraTab.XtraTabPage xtraTabTestMuchSum;
        private DevExpress.XtraGrid.GridControl gcTestSum;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn51;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn52;
        private Label label2;
        private NumericUpDown numTestCover;
        private Label label1;
       
        List<MainRoadCellInfo> mainRoadCellInfoDetailList = null;
        List<MainRoadCellInfo> mainRoadCellInfoSumList = null;
        List<MainRoadCellInfo> mainRoadTestCoverDetailList = null;
        List<MainRoadCellInfo> mainRoadTestCoverSumList = null;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn53;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn54;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn55;
        ResultData resultData = null;

        public void FillData(ResultData resultData)
        {
            BindingSource source = new BindingSource();
            source.DataSource = resultData.MainRoadCellInfoDetailList;
            gcDetail.DataSource = source;
            gcDetail.RefreshDataSource();

            mainRoadCellInfoDetailList = new List<MainRoadCellInfo>();
            mainRoadCellInfoDetailList.AddRange(resultData.MainRoadCellInfoDetailList);

            mainRoadCellInfoSumList = new List<MainRoadCellInfo>();

            #region 按要求调整汇总的排序
            foreach (CityMainRoadCellKey cellKey in resultData.MainRoadCellInfoSumDic.Keys)
            {
                resultData.MainRoadCellInfoSumDic[cellKey].CellNameList.Clear();
                resultData.MainRoadCellInfoSumDic[cellKey].BtsLogicList.Clear();
                resultData.MainRoadCellInfoSumDic[cellKey].BtsPhysiList.Clear();
            }

            addMainRoadCellInfoSumList(new Func(judgeNet)); 
            addMainRoadCellInfoSumList(new Func(judgeTestDir)); 
            addMainRoadCellInfoSumList(new Func(judgeTestMonth));
            addMainRoadCellInfoSumList(new Func(judgeMainRoadAndCity));
            addMainRoadCellInfoSumList(new Func(judgeCity));
            addMainRoadCellInfoSumList(new Func(judgeMainRoad));
            #endregion

            source = new BindingSource();
            source.DataSource = mainRoadCellInfoSumList;
            gcSum.DataSource = source;
            gcSum.RefreshDataSource();

            this.resultData = resultData;
            refreshData(3);
        }

        private bool judgeNet(CityMainRoadCellKey cellKey)
        {
            if (cellKey.StrNet != "汇总")
            {
                return true;
            }
            return false;
        }
        private bool judgeTestDir(CityMainRoadCellKey cellKey)
        {
            if (cellKey.StrTestDir != "汇总" && cellKey.StrNet == "汇总")
            {
                return true;
            }
            return false;
        }
        private bool judgeTestMonth(CityMainRoadCellKey cellKey)
        {
            if (cellKey.StrTestMonth != "汇总" && cellKey.StrTestDir == "汇总" && cellKey.StrNet == "汇总")
            {
                return true;
            }
            return false;
        }
        private bool judgeMainRoadAndCity(CityMainRoadCellKey cellKey)
        {
            if (cellKey.StrMainRoad != "汇总" && cellKey.StrCity != "汇总" && cellKey.StrTestMonth == "汇总"
                    && cellKey.StrTestDir == "汇总" && cellKey.StrNet == "汇总")
            {
                return true;
            }
            return false;
        }
        private bool judgeCity(CityMainRoadCellKey cellKey)
        {
            if (cellKey.StrCity == "汇总" && cellKey.StrTestMonth == "汇总"
                    && cellKey.StrTestDir == "汇总" && cellKey.StrNet == "汇总")
            {
                return true;
            }
            return false;
        }
        private bool judgeMainRoad(CityMainRoadCellKey cellKey)
        {
            if (cellKey.StrMainRoad == "汇总")
            {
                return true;
            }
            return false;
        }

        private void addMainRoadCellInfoSumList(Func func)
        {
            foreach (CityMainRoadCellKey cellKey in resultData.MainRoadCellInfoSumDic.Keys)
            {
                if (func(cellKey))
                {
                    resultData.MainRoadCellInfoSumDic[cellKey].ISN = mainRoadCellInfoSumList.Count + 1;
                    mainRoadCellInfoSumList.Add(resultData.MainRoadCellInfoSumDic[cellKey]);
                }
            }
        }

        private delegate bool Func(CityMainRoadCellKey cellKey);

        private void refreshData(int iTestCoverNum)
        {
            mainRoadTestCoverDetailList = new List<MainRoadCellInfo>();
            resultData.MainRoadMuchCoverSumDic.Clear();
            mainRoadTestCoverSumList = new List<MainRoadCellInfo>();

            foreach (MainRoadCellInfo mainRoadCell in resultData.MainRoadMuchCoverDetailList)
            {
                if (mainRoadCell.ICoverNum >= iTestCoverNum)
                {
                    mainRoadTestCoverDetailList.Add(mainRoadCell);
                    List<CityMainRoadCellKey> cellAllKeyList = new List<CityMainRoadCellKey>();
                    cellAllKeyList.Add(new CityMainRoadCellKey(mainRoadCell.StrMainRoadName, mainRoadCell.StrCity, "", "", mainRoadCell.StrNet));
                    cellAllKeyList.Add(new CityMainRoadCellKey(mainRoadCell.StrMainRoadName, mainRoadCell.StrCity, "", "", "汇总"));
                    cellAllKeyList.Add(new CityMainRoadCellKey(mainRoadCell.StrMainRoadName, "汇总", "", "", "汇总"));
                    cellAllKeyList.Add(new CityMainRoadCellKey("汇总", mainRoadCell.StrCity, "", "", "汇总"));

                    string strLac_Ci = mainRoadCell.ILAC + "_" + mainRoadCell.ICI;
                    foreach (CityMainRoadCellKey cellAllKey in cellAllKeyList)
                    {
                        addMainRoadMuchCoverSumDic(mainRoadCell, strLac_Ci, cellAllKey);
                    }
                }
            }

            BindingSource source = new BindingSource();
            source.DataSource = mainRoadTestCoverDetailList;
            gcTestDeatail.DataSource = source;
            gcTestDeatail.RefreshDataSource();

            setMainRoadMuchCoverSumDic();
            source = new BindingSource();
            source.DataSource = mainRoadTestCoverSumList;
            gcTestSum.DataSource = source;
            gcTestSum.RefreshDataSource();
        }

        private void addMainRoadMuchCoverSumDic(MainRoadCellInfo mainRoadCell, string strLac_Ci, CityMainRoadCellKey cellAllKey)
        {
            if (!resultData.MainRoadMuchCoverSumDic.ContainsKey(cellAllKey))
            {
                MainRoadCellInfo cellSumInfo = new MainRoadCellInfo();
                cellSumInfo.StrMainRoadName = cellAllKey.StrMainRoad;
                cellSumInfo.StrCity = cellAllKey.StrCity;
                cellSumInfo.StrTestMonth = cellAllKey.StrTestMonth;
                cellSumInfo.StrTestDir = cellAllKey.StrTestDir;
                cellSumInfo.StrNet = cellAllKey.StrNet;
                cellSumInfo.CellNameList.Add(strLac_Ci);
                cellSumInfo.ICellNum = 1;
                if (mainRoadCell.StrBTSLogicName != "")
                {
                    cellSumInfo.BtsLogicList.Add(mainRoadCell.StrBTSLogicName);
                    cellSumInfo.IBTSLogicNum = 1;
                }
                if (mainRoadCell.StrBTSPhysiName != "")
                {
                    cellSumInfo.BtsPhysiList.Add(mainRoadCell.StrBTSPhysiName);
                    cellSumInfo.IBTSPhysiNum = 1;
                }
                resultData.MainRoadMuchCoverSumDic[cellAllKey] = cellSumInfo;
            }
            else
            {
                if (!resultData.MainRoadMuchCoverSumDic[cellAllKey].CellNameList.Contains(strLac_Ci))
                {
                    resultData.MainRoadMuchCoverSumDic[cellAllKey].CellNameList.Add(strLac_Ci);
                    resultData.MainRoadMuchCoverSumDic[cellAllKey].ICellNum += 1;
                }
                if (!resultData.MainRoadMuchCoverSumDic[cellAllKey].BtsLogicList.Contains(mainRoadCell.StrBTSLogicName)
                    && mainRoadCell.StrBTSLogicName != "")
                {
                    resultData.MainRoadMuchCoverSumDic[cellAllKey].BtsLogicList.Add(mainRoadCell.StrBTSLogicName);
                    resultData.MainRoadMuchCoverSumDic[cellAllKey].IBTSLogicNum += 1;
                }
                if (!resultData.MainRoadMuchCoverSumDic[cellAllKey].BtsPhysiList.Contains(mainRoadCell.StrBTSPhysiName)
                    && mainRoadCell.StrBTSPhysiName != "")
                {
                    resultData.MainRoadMuchCoverSumDic[cellAllKey].BtsPhysiList.Add(mainRoadCell.StrBTSPhysiName);
                    resultData.MainRoadMuchCoverSumDic[cellAllKey].IBTSPhysiNum += 1;
                }
            }
        }

        private void setMainRoadMuchCoverSumDic()
        {
            #region 按要求调整汇总的排序
            foreach (CityMainRoadCellKey cellKey in resultData.MainRoadMuchCoverSumDic.Keys)
            {
                resultData.MainRoadMuchCoverSumDic[cellKey].CellNameList.Clear();
                resultData.MainRoadMuchCoverSumDic[cellKey].BtsLogicList.Clear();
                resultData.MainRoadMuchCoverSumDic[cellKey].BtsPhysiList.Clear();
            }

            foreach (CityMainRoadCellKey cellKey in resultData.MainRoadMuchCoverSumDic.Keys)
            {
                if (cellKey.StrNet != "汇总")
                {
                    resultData.MainRoadMuchCoverSumDic[cellKey].ISN = mainRoadTestCoverSumList.Count + 1;
                    mainRoadTestCoverSumList.Add(resultData.MainRoadMuchCoverSumDic[cellKey]);
                }
            }
            foreach (CityMainRoadCellKey cellKey in resultData.MainRoadMuchCoverSumDic.Keys)
            {
                if (cellKey.StrMainRoad != "汇总" && cellKey.StrCity != "汇总" && cellKey.StrNet == "汇总")
                {
                    resultData.MainRoadMuchCoverSumDic[cellKey].ISN = mainRoadTestCoverSumList.Count + 1;
                    mainRoadTestCoverSumList.Add(resultData.MainRoadMuchCoverSumDic[cellKey]);
                }
            }
            foreach (CityMainRoadCellKey cellKey in resultData.MainRoadMuchCoverSumDic.Keys)
            {
                if (cellKey.StrCity == "汇总" && cellKey.StrNet == "汇总")
                {
                    resultData.MainRoadMuchCoverSumDic[cellKey].ISN = mainRoadTestCoverSumList.Count + 1;
                    mainRoadTestCoverSumList.Add(resultData.MainRoadMuchCoverSumDic[cellKey]);
                }
            }
            foreach (CityMainRoadCellKey cellKey in resultData.MainRoadMuchCoverSumDic.Keys)
            {
                if (cellKey.StrMainRoad == "汇总")
                {
                    resultData.MainRoadMuchCoverSumDic[cellKey].ISN = mainRoadTestCoverSumList.Count + 1;
                    mainRoadTestCoverSumList.Add(resultData.MainRoadMuchCoverSumDic[cellKey]);
                }
            }
            #endregion
        }

        private void outPutExcel_Click(object sender, EventArgs e)
        {
            exportToExcel();
        }

        private void exportToExcel()
        {
            List<NPOIRow> dataDetails = new List<NPOIRow>();
            List<NPOIRow> dataSum = new List<NPOIRow>();

            List<NPOIRow> dataTestDetails = new List<NPOIRow>();
            List<NPOIRow> dataTestSum = new List<NPOIRow>();

            #region 表头字段
            NPOIRow nrTitle = new NPOIRow();
            List<object> rowTitle = new List<object>();
            rowTitle.Add("序号");
            rowTitle.Add("高速");
            rowTitle.Add("地市");
            rowTitle.Add("测试月份");
            rowTitle.Add("测试方向");
            rowTitle.Add("网络");
            rowTitle.Add("CGI");
            rowTitle.Add("小区中文名");
            rowTitle.Add("小区英文名");
            rowTitle.Add("TAC/LAC");
            rowTitle.Add("ECI/CI");
            rowTitle.Add("方向角");
            rowTitle.Add("下倾角");
            rowTitle.Add("经度");
            rowTitle.Add("纬度");
            rowTitle.Add("逻辑基站站点名");
            rowTitle.Add("物理基站站点名");
            rowTitle.Add("占用采样点数");
            rowTitle.Add("占用里程(米)");
            rowTitle.Add("垂直距离");
            rowTitle.Add("垂直点经度");
            rowTitle.Add("垂直点纬度");
            nrTitle.cellValues = rowTitle;
            dataDetails.Add(nrTitle);

            nrTitle = new NPOIRow();
            rowTitle = new List<object>();
            rowTitle.Add("序号");
            rowTitle.Add("高速");
            rowTitle.Add("地市");
            rowTitle.Add("测试月份");
            rowTitle.Add("测试方向");
            rowTitle.Add("网络");
            rowTitle.Add("小区数");
            rowTitle.Add("逻辑基站数");
            rowTitle.Add("物理基站数");
            nrTitle.cellValues = rowTitle;
            dataSum.Add(nrTitle);

            nrTitle = new NPOIRow();
            rowTitle = new List<object>();
            rowTitle.Add("序号");
            rowTitle.Add("高速");
            rowTitle.Add("地市");
            rowTitle.Add("测试次数");
            rowTitle.Add("占用次数");
            rowTitle.Add("网络");
            rowTitle.Add("CGI");
            rowTitle.Add("小区中文名");
            rowTitle.Add("小区英文名");
            rowTitle.Add("TAC/LAC");
            rowTitle.Add("ECI/CI");
            rowTitle.Add("方向角");
            rowTitle.Add("下倾角");
            rowTitle.Add("经度");
            rowTitle.Add("纬度");
            rowTitle.Add("逻辑基站站点名");
            rowTitle.Add("物理基站站点名");
            nrTitle.cellValues = rowTitle;
            dataTestDetails.Add(nrTitle);

            nrTitle = new NPOIRow();
            rowTitle = new List<object>();
            rowTitle.Add("序号");
            rowTitle.Add("高速");
            rowTitle.Add("地市");
            rowTitle.Add("网络");
            rowTitle.Add("小区数");
            rowTitle.Add("逻辑基站数");
            rowTitle.Add("物理基站数");
            nrTitle.cellValues = rowTitle;
            dataTestSum.Add(nrTitle);
            #endregion

            foreach (MainRoadCellInfo cellInfo in this.mainRoadCellInfoDetailList)
            {
                NPOIRow nrData = new NPOIRow();
                List<object> row = new List<object>();
                row.Add(cellInfo.ISN);
                row.Add(cellInfo.StrMainRoadName);
                row.Add(cellInfo.StrCity);
                row.Add(cellInfo.StrTestMonth);
                row.Add(cellInfo.StrTestDir);
                row.Add(cellInfo.StrNet);
                row.Add(cellInfo.StrCGI);
                row.Add(cellInfo.StrCellName);
                row.Add(cellInfo.StrCellEngName);
                row.Add(cellInfo.ILAC);
                row.Add(cellInfo.ICI);
                row.Add(cellInfo.StrDirection);
                row.Add(cellInfo.StrDownward);
                row.Add(cellInfo.StrLongitude);
                row.Add(cellInfo.StrLatitude);
                row.Add(cellInfo.StrBTSLogicName);
                row.Add(cellInfo.StrBTSPhysiName);
                row.Add(cellInfo.ISampleNum);
                row.Add(cellInfo.DDistance);
                row.Add(cellInfo.StrTestRoadDistance);
                row.Add(cellInfo.StrRoadPointLng);
                row.Add(cellInfo.StrRoadPointLat);
                nrData.cellValues = row;
                dataDetails.Add(nrData);
            }

            foreach (MainRoadCellInfo cellInfo in this.mainRoadCellInfoSumList)
            {
                NPOIRow nrData = new NPOIRow();
                List<object> row = new List<object>();
                row.Add(cellInfo.ISN);
                row.Add(cellInfo.StrMainRoadName);
                row.Add(cellInfo.StrCity);
                row.Add(cellInfo.StrTestMonth);
                row.Add(cellInfo.StrTestDir);
                row.Add(cellInfo.StrNet);
                row.Add(cellInfo.ICellNum);
                row.Add(cellInfo.IBTSLogicNum);
                row.Add(cellInfo.IBTSPhysiNum);
                nrData.cellValues = row;
                dataSum.Add(nrData);
            }

            foreach (MainRoadCellInfo cellInfo in this.mainRoadTestCoverDetailList)
            {
                NPOIRow nrData = new NPOIRow();
                List<object> row = new List<object>();
                row.Add(cellInfo.ISN);
                row.Add(cellInfo.StrMainRoadName);
                row.Add(cellInfo.StrCity);
                row.Add(cellInfo.ITestNum);
                row.Add(cellInfo.ICoverNum);
                row.Add(cellInfo.StrNet);
                row.Add(cellInfo.StrCGI);
                row.Add(cellInfo.StrCellName);
                row.Add(cellInfo.StrCellEngName);
                row.Add(cellInfo.ILAC);
                row.Add(cellInfo.ICI);
                row.Add(cellInfo.StrDirection);
                row.Add(cellInfo.StrDownward);
                row.Add(cellInfo.StrLongitude);
                row.Add(cellInfo.StrLatitude);
                row.Add(cellInfo.StrBTSLogicName);
                row.Add(cellInfo.StrBTSPhysiName);
                nrData.cellValues = row;
                dataTestDetails.Add(nrData);
            }

            foreach (MainRoadCellInfo cellInfo in this.mainRoadTestCoverSumList)
            {
                NPOIRow nrData = new NPOIRow();
                List<object> row = new List<object>();
                row.Add(cellInfo.ISN);
                row.Add(cellInfo.StrMainRoadName);
                row.Add(cellInfo.StrCity);
                row.Add(cellInfo.StrNet);
                row.Add(cellInfo.ICellNum);
                row.Add(cellInfo.IBTSLogicNum);
                row.Add(cellInfo.IBTSPhysiNum);
                nrData.cellValues = row;
                dataTestSum.Add(nrData);
            }

            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            List<string> sheetNames = new List<string>();
            nrDatasList.Add(dataDetails);
            nrDatasList.Add(dataSum);
            nrDatasList.Add(dataTestDetails);
            nrDatasList.Add(dataTestSum);
            sheetNames.Add("高速主覆盖小区列表");
            sheetNames.Add("高速主覆盖小区统计");
            sheetNames.Add("高速多次覆盖小区列表");
            sheetNames.Add("高速多次覆盖小区统计");
            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }

        private void numTestCover_ValueChanged(object sender, EventArgs e)
        {
            refreshData(Convert.ToInt32(numTestCover.Value));
        }

        private System.Windows.Forms.ContextMenuStrip conOutPutDate;
        private System.ComponentModel.IContainer components;
        private System.Windows.Forms.ToolStripMenuItem outPutExcel;
    
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.conOutPutDate = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.outPutExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView7 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView8 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView9 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabCoverMuch = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabDetail = new DevExpress.XtraTab.XtraTabPage();
            this.gcDetail = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn55 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn53 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn54 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabSum = new DevExpress.XtraTab.XtraTabPage();
            this.gcSum = new DevExpress.XtraGrid.GridControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabTestMuchDetail = new DevExpress.XtraTab.XtraTabPage();
            this.gcTestDeatail = new DevExpress.XtraGrid.GridControl();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabTestMuchSum = new DevExpress.XtraTab.XtraTabPage();
            this.gcTestSum = new DevExpress.XtraGrid.GridControl();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn51 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn52 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.label2 = new System.Windows.Forms.Label();
            this.numTestCover = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.conOutPutDate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabCoverMuch)).BeginInit();
            this.xtraTabCoverMuch.SuspendLayout();
            this.xtraTabDetail.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            this.xtraTabSum.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcSum)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            this.xtraTabTestMuchDetail.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcTestDeatail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            this.xtraTabTestMuchSum.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcTestSum)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTestCover)).BeginInit();
            this.SuspendLayout();
            // 
            // conOutPutDate
            // 
            this.conOutPutDate.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.outPutExcel});
            this.conOutPutDate.Name = "conOutPutDate";
            this.conOutPutDate.Size = new System.Drawing.Size(130, 26);
            // 
            // outPutExcel
            // 
            this.outPutExcel.Name = "outPutExcel";
            this.outPutExcel.Size = new System.Drawing.Size(129, 22);
            this.outPutExcel.Text = "导出Excel";
            this.outPutExcel.Click += new System.EventHandler(this.outPutExcel_Click);
            // 
            // gridView5
            // 
            this.gridView5.Name = "gridView5";
            // 
            // gridView7
            // 
            this.gridView7.Name = "gridView7";
            // 
            // gridView8
            // 
            this.gridView8.Name = "gridView8";
            // 
            // gridView9
            // 
            this.gridView9.Name = "gridView9";
            // 
            // xtraTabCoverMuch
            // 
            this.xtraTabCoverMuch.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabCoverMuch.Location = new System.Drawing.Point(0, 0);
            this.xtraTabCoverMuch.Name = "xtraTabCoverMuch";
            this.xtraTabCoverMuch.SelectedTabPage = this.xtraTabDetail;
            this.xtraTabCoverMuch.Size = new System.Drawing.Size(1127, 411);
            this.xtraTabCoverMuch.TabIndex = 2;
            this.xtraTabCoverMuch.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabDetail,
            this.xtraTabSum,
            this.xtraTabTestMuchDetail,
            this.xtraTabTestMuchSum});
            // 
            // xtraTabDetail
            // 
            this.xtraTabDetail.Controls.Add(this.gcDetail);
            this.xtraTabDetail.Name = "xtraTabDetail";
            this.xtraTabDetail.Size = new System.Drawing.Size(1120, 381);
            this.xtraTabDetail.Text = "高速主覆盖小区列表";
            // 
            // gcDetail
            // 
            this.gcDetail.ContextMenuStrip = this.conOutPutDate;
            this.gcDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcDetail.Location = new System.Drawing.Point(0, 0);
            this.gcDetail.MainView = this.gridView1;
            this.gcDetail.Name = "gcDetail";
            this.gcDetail.Size = new System.Drawing.Size(1120, 381);
            this.gcDetail.TabIndex = 0;
            this.gcDetail.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1,
            this.gridView6});
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn26,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn47,
            this.gridColumn55,
            this.gridColumn48,
            this.gridColumn53,
            this.gridColumn54});
            this.gridView1.GridControl = this.gcDetail;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "序号";
            this.gridColumn1.FieldName = "ISN";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "高速";
            this.gridColumn2.FieldName = "StrMainRoadName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "地市";
            this.gridColumn3.FieldName = "StrCity";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "测试月份";
            this.gridColumn4.FieldName = "StrTestMonth";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "测试方向";
            this.gridColumn5.FieldName = "StrTestDir";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "网络";
            this.gridColumn6.FieldName = "StrNet";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "CGI";
            this.gridColumn7.FieldName = "StrCGI";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "小区中文名";
            this.gridColumn8.FieldName = "StrCellName";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 7;
            this.gridColumn8.Width = 90;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "小区英文名";
            this.gridColumn9.FieldName = "StrCellEngName";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 8;
            this.gridColumn9.Width = 90;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "TAC/LAC";
            this.gridColumn10.FieldName = "ILAC";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 9;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "ECI/CI";
            this.gridColumn26.FieldName = "ICI";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 10;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "方向角";
            this.gridColumn11.FieldName = "StrDirection";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 11;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "下倾角";
            this.gridColumn12.FieldName = "StrDownward";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 12;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "经度";
            this.gridColumn13.FieldName = "StrLongitude";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 13;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "纬度";
            this.gridColumn14.FieldName = "StrLatitude";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 14;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "逻辑基站站点名";
            this.gridColumn15.FieldName = "StrBTSLogicName";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 15;
            this.gridColumn15.Width = 120;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "物理基站站点名";
            this.gridColumn16.FieldName = "StrBTSPhysiName";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 16;
            this.gridColumn16.Width = 120;
            // 
            // gridColumn47
            // 
            this.gridColumn47.Caption = "占用采样点数";
            this.gridColumn47.FieldName = "ISampleNum";
            this.gridColumn47.Name = "gridColumn47";
            this.gridColumn47.Visible = true;
            this.gridColumn47.VisibleIndex = 17;
            this.gridColumn47.Width = 100;
            // 
            // gridColumn55
            // 
            this.gridColumn55.Caption = "占用里程(米)";
            this.gridColumn55.FieldName = "DDistance";
            this.gridColumn55.Name = "gridColumn55";
            this.gridColumn55.Visible = true;
            this.gridColumn55.VisibleIndex = 18;
            this.gridColumn55.Width = 100;
            // 
            // gridColumn48
            // 
            this.gridColumn48.Caption = "垂直距离";
            this.gridColumn48.FieldName = "StrTestRoadDistance";
            this.gridColumn48.Name = "gridColumn48";
            this.gridColumn48.Visible = true;
            this.gridColumn48.VisibleIndex = 19;
            this.gridColumn48.Width = 90;
            // 
            // gridColumn53
            // 
            this.gridColumn53.Caption = "垂直点经度";
            this.gridColumn53.FieldName = "StrRoadPointLng";
            this.gridColumn53.Name = "gridColumn53";
            this.gridColumn53.Visible = true;
            this.gridColumn53.VisibleIndex = 20;
            this.gridColumn53.Width = 90;
            // 
            // gridColumn54
            // 
            this.gridColumn54.Caption = "垂直点纬度";
            this.gridColumn54.FieldName = "StrRoadPointLat";
            this.gridColumn54.Name = "gridColumn54";
            this.gridColumn54.Visible = true;
            this.gridColumn54.VisibleIndex = 21;
            this.gridColumn54.Width = 90;
            // 
            // gridView6
            // 
            this.gridView6.GridControl = this.gcDetail;
            this.gridView6.Name = "gridView6";
            // 
            // xtraTabSum
            // 
            this.xtraTabSum.Controls.Add(this.gcSum);
            this.xtraTabSum.Name = "xtraTabSum";
            this.xtraTabSum.Size = new System.Drawing.Size(1120, 381);
            this.xtraTabSum.Text = "高速主覆盖小区统计";
            // 
            // gcSum
            // 
            this.gcSum.ContextMenuStrip = this.conOutPutDate;
            this.gcSum.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcSum.Location = new System.Drawing.Point(0, 0);
            this.gcSum.MainView = this.gridView2;
            this.gcSum.Name = "gcSum";
            this.gcSum.Size = new System.Drawing.Size(1120, 381);
            this.gcSum.TabIndex = 1;
            this.gcSum.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            // 
            // gridView2
            // 
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25});
            this.gridView2.GridControl = this.gcSum;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsView.ColumnAutoWidth = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "序号";
            this.gridColumn17.FieldName = "ISN";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 0;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "高速";
            this.gridColumn18.FieldName = "StrMainRoadName";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 1;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "地市";
            this.gridColumn19.FieldName = "StrCity";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 2;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "测试月份";
            this.gridColumn20.FieldName = "StrTestMonth";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 3;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "测试方向";
            this.gridColumn21.FieldName = "StrTestDir";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 4;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "网络";
            this.gridColumn22.FieldName = "StrNet";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 5;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "小区数";
            this.gridColumn23.FieldName = "ICellNum";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 6;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "逻辑基站数";
            this.gridColumn24.FieldName = "IBTSLogicNum";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 7;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "物理基站数";
            this.gridColumn25.FieldName = "IBTSPhysiNum";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 8;
            // 
            // xtraTabTestMuchDetail
            // 
            this.xtraTabTestMuchDetail.Controls.Add(this.gcTestDeatail);
            this.xtraTabTestMuchDetail.Name = "xtraTabTestMuchDetail";
            this.xtraTabTestMuchDetail.Size = new System.Drawing.Size(1120, 381);
            this.xtraTabTestMuchDetail.Text = "高速多次覆盖小区列表";
            // 
            // gcTestDeatail
            // 
            this.gcTestDeatail.ContextMenuStrip = this.conOutPutDate;
            this.gcTestDeatail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcTestDeatail.Location = new System.Drawing.Point(0, 0);
            this.gcTestDeatail.MainView = this.gridView3;
            this.gcTestDeatail.Name = "gcTestDeatail";
            this.gcTestDeatail.Size = new System.Drawing.Size(1120, 381);
            this.gcTestDeatail.TabIndex = 1;
            this.gcTestDeatail.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView3});
            // 
            // gridView3
            // 
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn32,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn38,
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43});
            this.gridView3.GridControl = this.gcTestDeatail;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsView.ColumnAutoWidth = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "序号";
            this.gridColumn27.FieldName = "ISN";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 0;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "高速";
            this.gridColumn28.FieldName = "StrMainRoadName";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 1;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "地市";
            this.gridColumn29.FieldName = "StrCity";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 2;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "网络";
            this.gridColumn32.FieldName = "StrNet";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 3;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "测试次数";
            this.gridColumn30.FieldName = "ITestNum";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 4;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "占用次数";
            this.gridColumn31.FieldName = "ICoverNum";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 5;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "CGI";
            this.gridColumn33.FieldName = "StrCGI";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 6;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "小区中文名";
            this.gridColumn34.FieldName = "StrCellName";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 7;
            this.gridColumn34.Width = 90;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "小区英文名";
            this.gridColumn35.FieldName = "StrCellEngName";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 8;
            this.gridColumn35.Width = 90;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "TAC/LAC";
            this.gridColumn36.FieldName = "ILAC";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 9;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "ECI/CI";
            this.gridColumn37.FieldName = "ICI";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 10;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "方向角";
            this.gridColumn38.FieldName = "StrDirection";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 11;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "下倾角";
            this.gridColumn39.FieldName = "StrDownward";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 12;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "经度";
            this.gridColumn40.FieldName = "StrLongitude";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 13;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "纬度";
            this.gridColumn41.FieldName = "StrLatitude";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 14;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "逻辑基站站点名";
            this.gridColumn42.FieldName = "StrBTSLogicName";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 15;
            this.gridColumn42.Width = 120;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "物理基站站点名";
            this.gridColumn43.FieldName = "StrBTSPhysiName";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 16;
            this.gridColumn43.Width = 120;
            // 
            // xtraTabTestMuchSum
            // 
            this.xtraTabTestMuchSum.Controls.Add(this.gcTestSum);
            this.xtraTabTestMuchSum.Name = "xtraTabTestMuchSum";
            this.xtraTabTestMuchSum.Size = new System.Drawing.Size(1120, 381);
            this.xtraTabTestMuchSum.Text = "高速多次覆盖小区列表";
            // 
            // gcTestSum
            // 
            this.gcTestSum.ContextMenuStrip = this.conOutPutDate;
            this.gcTestSum.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcTestSum.Location = new System.Drawing.Point(0, 0);
            this.gcTestSum.MainView = this.gridView4;
            this.gcTestSum.Name = "gcTestSum";
            this.gcTestSum.Size = new System.Drawing.Size(1120, 381);
            this.gcTestSum.TabIndex = 2;
            this.gcTestSum.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView4});
            // 
            // gridView4
            // 
            this.gridView4.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn44,
            this.gridColumn45,
            this.gridColumn46,
            this.gridColumn49,
            this.gridColumn50,
            this.gridColumn51,
            this.gridColumn52});
            this.gridView4.GridControl = this.gcTestSum;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsBehavior.Editable = false;
            this.gridView4.OptionsView.ColumnAutoWidth = false;
            this.gridView4.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "序号";
            this.gridColumn44.FieldName = "ISN";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 0;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "高速";
            this.gridColumn45.FieldName = "StrMainRoadName";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 1;
            // 
            // gridColumn46
            // 
            this.gridColumn46.Caption = "地市";
            this.gridColumn46.FieldName = "StrCity";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 2;
            // 
            // gridColumn49
            // 
            this.gridColumn49.Caption = "网络";
            this.gridColumn49.FieldName = "StrNet";
            this.gridColumn49.Name = "gridColumn49";
            this.gridColumn49.Visible = true;
            this.gridColumn49.VisibleIndex = 3;
            // 
            // gridColumn50
            // 
            this.gridColumn50.Caption = "小区数";
            this.gridColumn50.FieldName = "ICellNum";
            this.gridColumn50.Name = "gridColumn50";
            this.gridColumn50.Visible = true;
            this.gridColumn50.VisibleIndex = 4;
            // 
            // gridColumn51
            // 
            this.gridColumn51.Caption = "逻辑基站数";
            this.gridColumn51.FieldName = "IBTSLogicNum";
            this.gridColumn51.Name = "gridColumn51";
            this.gridColumn51.Visible = true;
            this.gridColumn51.VisibleIndex = 5;
            // 
            // gridColumn52
            // 
            this.gridColumn52.Caption = "物理基站数";
            this.gridColumn52.FieldName = "IBTSPhysiNum";
            this.gridColumn52.Name = "gridColumn52";
            this.gridColumn52.Visible = true;
            this.gridColumn52.VisibleIndex = 6;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(773, 6);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(19, 14);
            this.label2.TabIndex = 8;
            this.label2.Text = "次";
            // 
            // numTestCover
            // 
            this.numTestCover.Location = new System.Drawing.Point(697, 0);
            this.numTestCover.Name = "numTestCover";
            this.numTestCover.Size = new System.Drawing.Size(66, 22);
            this.numTestCover.TabIndex = 7;
            this.numTestCover.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numTestCover.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numTestCover.ValueChanged += new System.EventHandler(this.numTestCover_ValueChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(541, 5);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(149, 14);
            this.label1.TabIndex = 6;
            this.label1.Text = "多次覆盖小区,占用次数>=";
            // 
            // QueryMainRoadCellSetForm
            // 
            this.ClientSize = new System.Drawing.Size(1127, 411);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.numTestCover);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.xtraTabCoverMuch);
            this.Name = "QueryMainRoadCellSetForm";
            this.Text = "高速小区集结果";
            this.conOutPutDate.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabCoverMuch)).EndInit();
            this.xtraTabCoverMuch.ResumeLayout(false);
            this.xtraTabDetail.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            this.xtraTabSum.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcSum)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            this.xtraTabTestMuchDetail.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcTestDeatail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            this.xtraTabTestMuchSum.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcTestSum)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTestCover)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

    }
}
