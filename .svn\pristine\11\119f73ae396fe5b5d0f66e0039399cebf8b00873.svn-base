﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class VolteWeakMosReasonManager : WeakMosReasonManagerBase
    {
        public VolteWeakMosReasonManager(VolteWeakMosReasonHelper helper)
        {
            this.helper = helper;
        }

        //Volte解析器
        readonly VolteWeakMosReasonHelper helper;

        protected override float? getRsrp(TestPoint tp)
        {
            return (float?)tp["lte_RSRP"];
        }

        protected override float? getSinr(TestPoint tp)
        {
            return (float?)tp["lte_SINR"];
        }

        protected override int? getRtpPacketsLostNum(TestPoint tp)
        {
            return helper.GetRtpPacketsLostNum(tp);
        }

        protected override bool judgeCsfb(int evtID)
        {
            if (evtID != 1374//VoLTE Video MO Call Established
              && evtID != 1375//VoLTE Video MT Call Established
              && evtID != 1074//VoLTE Audio MO Call Established
              && evtID != 1075)//VoLTE Audio MT Call Established
            {
                return true;
            }
            return false;
        }
    }
}
