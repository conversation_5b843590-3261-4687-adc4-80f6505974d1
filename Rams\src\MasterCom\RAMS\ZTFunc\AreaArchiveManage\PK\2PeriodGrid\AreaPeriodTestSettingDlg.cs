﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.PK
{
    public partial class AreaPeriodTestSettingDlg : BaseDialog
    {
        public AreaPeriodTestSettingDlg()
        {
            InitializeComponent();
            this.gridCtrlOptionalArea.DataSource = optionalAeas;
            this.gridCtrlSelected.DataSource = selAreas;
            cmbFilter.Items.Clear();
            cmbFilter.Items.Add(NoneFilter);
            foreach (AreaBase area in ZTAreaManager.Instance.GetArea(ZTAreaManager.Instance.Ranks[1]))
            {
                cmbFilter.Items.Add(area);
            }
            if (cmbFilter.Items.Count > 1)
            {
                cmbFilter.SelectedIndex = 1;
            }
        }

        private readonly string NoneFilter = "不限";

        public AreaPeriodTestSettingDlg(TemplateMngr templateMngr)
            : this()
        {
            this.templateMngr = templateMngr;
            fillTemplate(null);
        }

        private List<AreaBase> optionalAeas = new List<AreaBase>();
        private List<AreaBase> selAreas = new List<AreaBase>();
        public List<AreaBase> SelectedAreas
        {
            get
            {
                return selAreas;
            }
            set
            {
                selAreas.Clear();
                if (value == null)
                {
                    return;
                }
                selAreas.AddRange(value);
                gridCtrlSelected.RefreshDataSource();
            }
        }

        public TimePeriod Period1
        {
            get
            {
                TimePeriod tp = new TimePeriod(this.dtBegin1.Value.Date
                    , this.dtEnd1.Value.Date.AddDays(1).AddMilliseconds(-1));
                return tp;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.dtBegin1.Value = value.BeginTime;
                this.dtEnd1.Value = value.EndTime;
            }
        }

        public TimePeriod Period2
        {
            get
            {
                TimePeriod tp = new TimePeriod(this.dtBegin2.Value.Date
                    , this.dtEnd2.Value.Date.AddDays(1).AddMilliseconds(-1));
                return tp;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.dtBegin2.Value = value.BeginTime;
                this.dtEnd2.Value = value.EndTime;
            }
        }

        TemplateMngr templateMngr;
        public AreaReportTemplate SelTemplate
        {
            get
            {
                return cbxTemplate.SelectedItem as AreaReportTemplate;
            }
        }

        private void fillTemplate(AreaReportTemplate selTemplate)
        {
            cbxTemplate.Properties.Items.Clear();
            foreach (AreaReportTemplate rpt in templateMngr.ReportTemplates)
            {
                cbxTemplate.Properties.Items.Add(rpt);
            }
            if (selTemplate != null)
            {
                cbxTemplate.SelectedItem = selTemplate;
            }
            else if (cbxTemplate.Properties.Items.Count > 0)
            {
                cbxTemplate.SelectedIndex = 0;
            }
        }

        private void btnTemplate_Click(object sender, EventArgs e)
        {
            AreaRptTemplateOptionDlg dlg = new AreaRptTemplateOptionDlg();
            dlg.SetTemplate(cbxTemplate.SelectedItem as AreaReportTemplate);
            dlg.ShowDialog();
            fillTemplate(dlg.CurTemplate);
        }

        private void btnFind_Click(object sender, EventArgs e)
        {
            filterArea();
        }

        private void txtBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                filterArea();
            }
        }

        private void filterArea()
        {
            string name = txtName.Text.Trim().ToUpper();
            string sn = txtSn.Text.Trim().ToUpper();
            AreaBase areaFilter = cmbFilter.SelectedItem as AreaBase;
            if (name.Length == 0 && sn.Length == 0 && areaFilter == null)
            {
                return;
            }
            optionalAeas.Clear();
            foreach (AreaBase area in ZTAreaManager.Instance.GetLowestAreas())
            {
                bool isValid = judgeValid(name, sn, areaFilter, area);
                if (isValid)
                {
                    optionalAeas.Add(area);
                }
            }
            gridCtrlOptionalArea.RefreshDataSource();
        }

        private bool judgeValid(string name, string sn, AreaBase areaFilter, AreaBase area)
        {
            if (areaFilter == null)
            {
                if ((name.Length != 0 && area.Name.ToUpper().IndexOf(name) == -1)
                    || (sn.Length != 0 && area.SN.ToUpper().IndexOf(sn) == -1))
                {
                    return false;
                }
            }
            else
            {
                if (area.ParentArea.ParentArea != areaFilter
                    || (name.Length != 0 && area.Name.ToUpper().IndexOf(name) == -1)
                    || (sn.Length != 0 && area.SN.ToUpper().IndexOf(sn) == -1))
                {
                    return false;
                }
            }
            return true;
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (this.optionalAeas == null)
            {
                return;
            }
            foreach (AreaBase cell in optionalAeas)
            {
                if (!selAreas.Contains(cell))
                {
                    selAreas.Add(cell);
                }
            }
            optionalAeas.Clear();
            gridCtrlOptionalArea.RefreshDataSource();
            gridCtrlSelected.RefreshDataSource();
        }

        private void btnRemove_Click(object sender, EventArgs e)
        {
            foreach (AreaBase area in selAreas)
            {
                if (!optionalAeas.Contains(area))
                {
                    optionalAeas.Add(area);
                }
            }
            selAreas.Clear();
            gridCtrlOptionalArea.RefreshDataSource();
            gridCtrlSelected.RefreshDataSource();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (SelTemplate == null)
            {
                MessageBox.Show("报表模板不能为空！");
                return;
            }
            if (selAreas.Count == 0)
            {
                MessageBox.Show("请至少选择一个村庄！");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void gvOptionalArea_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gvOptionalArea.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            int idx = gvOptionalArea.GetDataSourceRowIndex(info.RowHandle);
            if (idx >= 0)
            {
                selAreas.Add(optionalAeas[idx]);
                optionalAeas.RemoveAt(idx);
                gridCtrlOptionalArea.RefreshDataSource();
                gridCtrlSelected.RefreshDataSource();
            }
        }

        private void gvSelected_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gvSelected.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            int idx = gvSelected.GetDataSourceRowIndex(info.RowHandle);
            if (idx >= 0)
            {
                optionalAeas.Add(this.selAreas[idx]);
                selAreas.RemoveAt(idx);
                gridCtrlOptionalArea.RefreshDataSource();
                gridCtrlSelected.RefreshDataSource();
            }
        }

        private void btnClearSel_Click(object sender, EventArgs e)
        {
            this.selAreas.Clear();
            gridCtrlSelected.RefreshDataSource();
        }

    }
}
