﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TestDetailShowForCellCoverge : DevExpress.XtraEditors.XtraForm
    {
        public TestDetailShowForCellCoverge()
        {
            InitializeComponent();
        }
        
        public List<TestDetailInf> AllShowInfs { get; set; }

        private DataTable dtRes;
        private void TestDetailShowForCellCoverge_Load(object sender, EventArgs e)
        {
            if (AllShowInfs != null && AllShowInfs.Count != 0)
            {
                if (dtRes == null)
                {
                    dtRes = new DataTable();
                    dtRes.Columns.Add("FileName");
                    dtRes.Columns.Add("Tmdat");
                    dtRes.Columns.Add("LAC");
                    dtRes.Columns.Add("CI");
                    dtRes.Columns.Add("Proble");
                }
                dtRes.Rows.Clear();
                for (int i = 0; i < AllShowInfs.Count; i++)
                {
                    dtRes.Rows.Add(new object[] { AllShowInfs[i].FileName,AllShowInfs[i].Tmdat,
                        AllShowInfs[i].LAC,AllShowInfs[i].CI,AllShowInfs[i].ProbleName});
                }
                this.gridControlRes.DataSource = dtRes;
            }
        } 
    }
}