﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTCellCoverageRangeAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTCellCoverageRangeAnaListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miFlyLine = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExportShp = new System.Windows.Forms.ToolStripMenuItem();
            this.miExpShpOne = new System.Windows.Forms.ToolStripMenuItem();
            this.miExpShpAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewCellCoverAna = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnServSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnServRxlevMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnServRxlevMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnServRxlevAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNbSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNbRxlevMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNbRxlevMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNbRxlevAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCellCoverAna)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miFlyLine,
            this.toolStripMenuItem1,
            this.miExportShp,
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(153, 98);
            this.ctxMenu.Opening += new System.ComponentModel.CancelEventHandler(this.ctxMenu_Opening);
            // 
            // miFlyLine
            // 
            this.miFlyLine.Checked = true;
            this.miFlyLine.CheckState = System.Windows.Forms.CheckState.Checked;
            this.miFlyLine.Name = "miFlyLine";
            this.miFlyLine.ShowShortcutKeys = false;
            this.miFlyLine.Size = new System.Drawing.Size(152, 22);
            this.miFlyLine.Text = "飞线";
            this.miFlyLine.Click += new System.EventHandler(this.miFlyLine_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(149, 6);
            // 
            // miExportShp
            // 
            this.miExportShp.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpShpOne,
            this.miExpShpAll});
            this.miExportShp.Name = "miExportShp";
            this.miExportShp.Size = new System.Drawing.Size(152, 22);
            this.miExportShp.Text = "导出Shp";
            // 
            // miExpShpOne
            // 
            this.miExpShpOne.Name = "miExpShpOne";
            this.miExpShpOne.Size = new System.Drawing.Size(152, 22);
            this.miExpShpOne.Text = "选中";
            this.miExpShpOne.Click += new System.EventHandler(this.miExpShpOne_Click);
            // 
            // miExpShpAll
            // 
            this.miExpShpAll.Name = "miExpShpAll";
            this.miExpShpAll.Size = new System.Drawing.Size(152, 22);
            this.miExpShpAll.Text = "全部";
            this.miExpShpAll.Click += new System.EventHandler(this.miExpShpAll_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(152, 22);
            this.miExportExcel.Text = "导出Excel...";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // ListViewCellCoverAna
            // 
            this.ListViewCellCoverAna.AllColumns.Add(this.olvColumnSN);
            this.ListViewCellCoverAna.AllColumns.Add(this.olvColumnCellName);
            this.ListViewCellCoverAna.AllColumns.Add(this.olvColumnCellType);
            this.ListViewCellCoverAna.AllColumns.Add(this.olvColumnLAC);
            this.ListViewCellCoverAna.AllColumns.Add(this.olvColumnCI);
            this.ListViewCellCoverAna.AllColumns.Add(this.olvColumnServSampleCount);
            this.ListViewCellCoverAna.AllColumns.Add(this.olvColumnServRxlevMax);
            this.ListViewCellCoverAna.AllColumns.Add(this.olvColumnServRxlevMin);
            this.ListViewCellCoverAna.AllColumns.Add(this.olvColumnServRxlevAvg);
            this.ListViewCellCoverAna.AllColumns.Add(this.olvColumnNbSampleCount);
            this.ListViewCellCoverAna.AllColumns.Add(this.olvColumnNbRxlevMax);
            this.ListViewCellCoverAna.AllColumns.Add(this.olvColumnNbRxlevMin);
            this.ListViewCellCoverAna.AllColumns.Add(this.olvColumnNbRxlevAvg);
            this.ListViewCellCoverAna.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.ListViewCellCoverAna.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnCellName,
            this.olvColumnCellType,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnServSampleCount,
            this.olvColumnServRxlevMax,
            this.olvColumnServRxlevMin,
            this.olvColumnServRxlevAvg,
            this.olvColumnNbSampleCount,
            this.olvColumnNbRxlevMax,
            this.olvColumnNbRxlevMin,
            this.olvColumnNbRxlevAvg});
            this.ListViewCellCoverAna.ContextMenuStrip = this.ctxMenu;
            this.ListViewCellCoverAna.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewCellCoverAna.FullRowSelect = true;
            this.ListViewCellCoverAna.GridLines = true;
            this.ListViewCellCoverAna.HeaderWordWrap = true;
            this.ListViewCellCoverAna.IsNeedShowOverlay = false;
            this.ListViewCellCoverAna.Location = new System.Drawing.Point(1, 1);
            this.ListViewCellCoverAna.Name = "ListViewCellCoverAna";
            this.ListViewCellCoverAna.OwnerDraw = true;
            this.ListViewCellCoverAna.ShowGroups = false;
            this.ListViewCellCoverAna.Size = new System.Drawing.Size(1120, 319);
            this.ListViewCellCoverAna.TabIndex = 7;
            this.ListViewCellCoverAna.UseCompatibleStateImageBehavior = false;
            this.ListViewCellCoverAna.View = System.Windows.Forms.View.Details;
            this.ListViewCellCoverAna.VirtualMode = true;
            this.ListViewCellCoverAna.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 40;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 120;
            // 
            // olvColumnCellType
            // 
            this.olvColumnCellType.HeaderFont = null;
            this.olvColumnCellType.Text = "小区类别";
            this.olvColumnCellType.Width = 80;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC/TAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI/ECI";
            this.olvColumnCI.Width = 90;
            // 
            // olvColumnServSampleCount
            // 
            this.olvColumnServSampleCount.HeaderFont = null;
            this.olvColumnServSampleCount.Text = "主服采样点数";
            this.olvColumnServSampleCount.Width = 90;
            // 
            // olvColumnServRxlevMax
            // 
            this.olvColumnServRxlevMax.HeaderFont = null;
            this.olvColumnServRxlevMax.Text = "主服信号最大值";
            this.olvColumnServRxlevMax.Width = 90;
            // 
            // olvColumnServRxlevMin
            // 
            this.olvColumnServRxlevMin.HeaderFont = null;
            this.olvColumnServRxlevMin.Text = "主服信号最小值";
            this.olvColumnServRxlevMin.Width = 90;
            // 
            // olvColumnServRxlevAvg
            // 
            this.olvColumnServRxlevAvg.HeaderFont = null;
            this.olvColumnServRxlevAvg.Text = "主服信号平均值";
            this.olvColumnServRxlevAvg.Width = 90;
            // 
            // olvColumnNbSampleCount
            // 
            this.olvColumnNbSampleCount.HeaderFont = null;
            this.olvColumnNbSampleCount.Text = "邻区采样点数";
            this.olvColumnNbSampleCount.Width = 90;
            // 
            // olvColumnNbRxlevMax
            // 
            this.olvColumnNbRxlevMax.HeaderFont = null;
            this.olvColumnNbRxlevMax.Text = "邻区信号最大值";
            this.olvColumnNbRxlevMax.Width = 90;
            // 
            // olvColumnNbRxlevMin
            // 
            this.olvColumnNbRxlevMin.HeaderFont = null;
            this.olvColumnNbRxlevMin.Text = "邻区信号最小值";
            this.olvColumnNbRxlevMin.Width = 90;
            // 
            // olvColumnNbRxlevAvg
            // 
            this.olvColumnNbRxlevAvg.HeaderFont = null;
            this.olvColumnNbRxlevAvg.Text = "邻区信号平均值";
            this.olvColumnNbRxlevAvg.Width = 90;
            // 
            // ZTCellCoverageRangeAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1122, 320);
            this.Controls.Add(this.ListViewCellCoverAna);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTCellCoverageRangeAnaListForm";
            this.Text = "小区覆盖带分析结果";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCellCoverAna)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miFlyLine;
        private BrightIdeasSoftware.TreeListView ListViewCellCoverAna;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnServSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnServRxlevMax;
        private BrightIdeasSoftware.OLVColumn olvColumnServRxlevMin;
        private BrightIdeasSoftware.OLVColumn olvColumnServRxlevAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnNbSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnNbRxlevMax;
        private BrightIdeasSoftware.OLVColumn olvColumnNbRxlevMin;
        private BrightIdeasSoftware.OLVColumn olvColumnNbRxlevAvg;
        private System.Windows.Forms.ToolStripMenuItem miExportShp;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpShpOne;
        private System.Windows.Forms.ToolStripMenuItem miExpShpAll;

    }
}