﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Net
{
    /// <summary>
    /// 切换频繁 - 界面,结果集修改版
    /// </summary>
    public class ZTDIYQueryHandoverTooMuchNew : DIYAnalyseByFileBackgroundBase
    {
        public int timeLimit { get; set; } = 15;
        public int distanceLimitMin { get; set; } = 0;
        public int distanceLimit { get; set; } = 150;
        public int handoverCount { get; set; } = 3;
        //是否只分析数据且做业务态的切换
        public bool isBusiness { get; set; } = false;

        public List<int> statEventIDs { get; set; } = new List<int>();
        public List<HandoverFileDataManagerNew> handoverFileList { get; set; } = new List<HandoverFileDataManagerNew>();
        public List<Event> handoverEvents { get; set; } = new List<Event>();

        private static ZTDIYQueryHandoverTooMuchNew intance = null;
        protected static readonly object lockObj = new object();
        public static ZTDIYQueryHandoverTooMuchNew GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYQueryHandoverTooMuchNew();
                    }
                }
            }
            return intance;
        }

        protected ZTDIYQueryHandoverTooMuchNew()
            : base(MainModel.GetInstance())
        {
            /*为了计算小区信息指标需要回放采样点，但是不按区域过滤采样点
            防止切换点的前三秒或后三秒的采样点不在区域内*/
            FilterSampleByRegion = false;
            IncludeTestPoint = true;
            FilterEventByRegion = true;

            MainModel.NeedType = true;
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
        }

        public override string Name
        {
            get
            {
                return "切换过频繁分析(LTE新版)";
            }
        }

        HandoverTooMuchConditionDialog conditionDlg = null;
        List<int> businessBeginEventIDs = null;
        List<int> businessEndEventIDs = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (conditionDlg == null)
            {
                conditionDlg = new HandoverTooMuchConditionDialog();
                conditionDlg.SetCondition(timeLimit, distanceLimitMin, distanceLimit, handoverCount, isBusiness);
            }
            if (conditionDlg.ShowDialog() == DialogResult.OK)
            {
                int tmpTimeLimit = 0, tmpDistanceLimitMin = 0, tmpDistanceLimit = 0, tmpHandoverCount = 0;
                bool tmpIsBusiness = true;
                conditionDlg.GetCondition(ref tmpTimeLimit, ref tmpDistanceLimitMin, ref tmpDistanceLimit, ref tmpHandoverCount, ref tmpIsBusiness);
                timeLimit = tmpTimeLimit;
                distanceLimitMin = tmpDistanceLimitMin;
                distanceLimit = tmpDistanceLimit;
                handoverCount = tmpHandoverCount;
                isBusiness = tmpIsBusiness;

                if (isBusiness)
                {
                    businessBeginEventIDs = new List<int>() { 57, 60, 1211, 1216 };
                    businessEndEventIDs = new List<int>() { 58, 59, 61, 62, 1214, 1213
                                                       , 1215, 1269, 1217, 1218, 1270 };
                }
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            handoverFileList.Clear();
            handoverEvents.Clear();

            initStatEventIDs();
        }

        protected virtual void initStatEventIDs()
        {
            statEventIDs.Clear();
            statEventIDs.Add(851);
            statEventIDs.Add(899);
            statEventIDs.Add(1039);
        }

        #region 数据处理
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                fileDataManager.Events = filterEvents(fileDataManager.Events);
                HandoverFileDataManagerNew curHandoverFile = HandoverAndReselectionManagerNew.GetHandoverTooMuchResult(fileDataManager,
                timeLimit, distanceLimit, handoverCount);
                if (curHandoverFile.HandoverTimes > 0)
                {
                    curHandoverFile.fmnger.TestPoints = filterTestPointsByEvents(curHandoverFile.Events, fileDataManager.TestPoints);
                    HandoverAndReselectionManagerNew.GetHandoverToMuchDetails(curHandoverFile, curHandoverFile.fmnger);
                    curHandoverFile.Index = handoverFileList.Count + 1;
                    handoverFileList.Add(curHandoverFile);
                    handoverEvents.AddRange(curHandoverFile.Events);
                }
            }
        }

        /// <summary>
        /// 过滤statEventIDs中的事件
        /// </summary>
        /// <param name="events">源文件事件合集</param>
        /// <returns>过滤后只包含statEventIDs事件ID的事件集合</returns>
        protected List<Event> filterEvents(List<Event> events)
        {
            List<Event> refList = new List<Event>();
            for (int eLoop = 0; eLoop < events.Count; ++eLoop)
            {
                Event e = events[eLoop];
                if (statEventIDs.Contains(e.ID))
                {
                    if (isBusiness && !IsBusinessHandOver(events, eLoop))
                    {
                        continue;
                    }
                    refList.Add(e);
                }
            }
            return refList;
        }

        /// <summary>
        /// 分析数据且做业务态的切换(条件窗口勾选后才会进入)
        /// </summary>
        /// <param name="eventList"></param>
        /// <param name="eLoop"></param>
        /// <returns></returns>
        private bool IsBusinessHandOver(List<Event> eventList, int eLoop)
        {
            bool tmpIsBusiness = false;
            for (int i = eLoop - 1; i >= 0; i--)
            {
                int iEventID = eventList[i].ID;
                if (businessEndEventIDs.Contains(iEventID))
                    break;
                if (businessBeginEventIDs.Contains(iEventID))
                {
                    tmpIsBusiness = true;
                    break;
                }
            }
            if (!tmpIsBusiness)
            {
                for (int i = eLoop + 1; i < eventList.Count; i++)
                {
                    int iEventID = eventList[i].ID;
                    if (businessBeginEventIDs.Contains(iEventID))
                        break;
                    if (businessEndEventIDs.Contains(iEventID))
                    {
                        tmpIsBusiness = true;
                        break;
                    }
                }
            }
            return tmpIsBusiness;
        }

        /// <summary>
        /// 按频繁切换事件过滤在事件发生时间内的采样点
        /// </summary>
        /// <param name="listEvt">频繁切换事件合集</param>
        /// <param name="listTp">采样点合集</param>
        /// <returns></returns>
        protected List<TestPoint> filterTestPointsByEvents(List<Event> listEvt, List<TestPoint> listTp)
        {
            if (listTp != null && listTp.Count > 0 && listEvt != null && listEvt.Count > 1)
            {
                List<TestPoint> list = getListTP(listEvt, listTp);
                return list;
            }
            return listTp;
        }

        private List<TestPoint> getListTP(List<Event> listEvt, List<TestPoint> listTp)
        {
            List<TestPoint> list = new List<TestPoint>();
            DateTime dtStart = listEvt[0].DateTime.AddSeconds(-10);
            DateTime dtEnd = listEvt[listEvt.Count - 1].DateTime.AddSeconds(5);

            foreach (TestPoint tp in listTp)
            {
                if (tp.DateTime > dtEnd)
                {
                    break;
                }
                if (tp.DateTime >= dtStart)
                {
                    list.Add(tp);
                }
            }

            return list;
        }
        #endregion

        protected override void fireShowForm()
        {
            ZTHandoverToolMuchBaseForm frm = null;
            frm = MainModel.CreateResultForm(typeof(ZTHandoverToolMuchBaseForm)) as ZTHandoverToolMuchBaseForm;
            frm.FillData(handoverFileList, handoverEvents);
            frm.Visible = true;
            frm.BringToFront();
        }

    }
}
