using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Grid;
using MasterCom.MControls;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TDPoorBlerRoadListForm : MinCloseForm
    {
        public TDPoorBlerRoadListForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            mapForm = mainModel.MainForm.GetMapForm();
            init();
            DisposeWhenClose = true;
        }
        private MapForm mapForm = null;

        private void init()
        {
            this.olvColumnSN.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return item.SN;
                }
                return null;
            };

            this.olvColumnRoadName.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return item.roadName;
                }
                return "";
            };

            this.olvColumnDitance.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return item.Distance_Show;
                }
                return "";
            };


            this.olvColumnSample.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return item.sampleLst.Count;
                }
                return "";
            };

            this.olvColumnCellNames.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return item.CellNames;
                }
                return "";
            };

            this.olvColumnCells.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return item.CellsStr;
                }
                return "";
            };

            this.olvColumnMaxBler.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return item.blerMax;
                }
                return "";
            };

            this.olvColumnMinBler.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return item.blerMin;
                }
                return "";
            };

            this.olvColumnAvgBler.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return item.blerAvg;
                }
                return "";
            };

            initData();

            this.olvColumnLongitudeMid.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return item.LongitudeMid;
                }
                return "";
            };

            this.olvColumnLatitudeMid.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return item.LatitudeMid;
                }
                return "";
            };

            this.olvColumnFileName.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return item.fileName;
                }
                return "";
            };

            this.olvColumnFirstTime.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return item.getFirstTime();
                }
                return "";
            };

            this.olvColumnLastTime.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return item.getLasttime();
                }
                return "";
            };
        }

        private object getValidData(float count, float data)
        {
            if (count > 0)
            {
                return data;
            }
            else
            {
                return "";
            }
        }

        private void initData()
        {
            this.olvColumnMaxRscp.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return getValidData(item.pccpchRscpSample, item.pccpchRscpMax);
                }
                return "";
            };

            this.olvColumnMinRscp.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return getValidData(item.pccpchRscpSample, item.pccpchRscpMin);
                }
                return "";
            };

            this.olvColumnAvgRscp.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return getValidData(item.pccpchRscpSample, item.pccpchRscpAvg);
                }
                return "";
            };

            this.olvColumnMaxPCCPCHC2I.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return getValidData(item.pccpchC2ISample, item.pccpchC2IMax);
                }
                return "";
            };

            this.olvColumnMinPCCPCHC2I.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return getValidData(item.pccpchC2ISample, item.pccpchC2IMin);
                }
                return "";
            };

            this.olvColumnAvgPCCPCHC2I.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return getValidData(item.pccpchC2ISample, item.pccpchC2IAvg);
                }
                return "";
            };

            this.olvColumnMaxDPCHC2I.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return getValidData(item.dpchC2ISample, item.dpchC2IMax);
                }
                return "";
            };

            this.olvColumnMinDPCHC2I.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return getValidData(item.dpchC2ISample, item.dpchC2IMin);
                }
                return "";
            };

            this.olvColumnAvgDPCHC2I.AspectGetter = delegate (object row)
            {
                if (row is TDPoorBlerRoadInfo)
                {
                    TDPoorBlerRoadInfo item = row as TDPoorBlerRoadInfo;
                    return getValidData(item.dpchC2ISample, item.dpchC2IAvg);
                }
                return "";
            };
        }

        public void FillData(List<TDPoorBlerRoadInfo> roadBlerList)
        {
            ListViewRoad.RebuildColumns();
            ListViewRoad.ClearObjects();
            ListViewRoad.SetObjects(roadBlerList);//(MainModel.TdPoorBlerRoadCovLst);
            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        private void listViewTotal_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (ListViewRoad.SelectedObject is TDPoorBlerRoadInfo)
            {
                TDPoorBlerRoadInfo info = ListViewRoad.SelectedObject as TDPoorBlerRoadInfo;
                mModel.SelectedCells = info.Cells;
                mModel.SelectedTDCells = info.TDCells;
                mModel.DTDataManager.Clear();
                foreach (TestPoint tp in info.sampleLst)
                {
                    mModel.DTDataManager.Add(tp);
                }
                mModel.FireDTDataChanged(this);
            }
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            ListViewRoad.ExpandAll();
        }

        private void miCallapsAll_Click(object sender, EventArgs e)
        {
            ListViewRoad.CollapseAll();
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(ListViewRoad);
        }
    }
}