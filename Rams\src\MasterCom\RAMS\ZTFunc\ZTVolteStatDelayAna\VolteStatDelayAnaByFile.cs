﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class VolteStatDelayAnaByFile : VolteStatDelayAnaByRegion
    {
        protected VolteStatDelayAnaByFile()
            : base()
        {
        }

        private static VolteStatDelayAnaByFile intance = null;
        public static new VolteStatDelayAnaByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new VolteStatDelayAnaByFile();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "VOLTE时延分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }

    public class VolteStatDelayAnaByFile_FDD : VolteStatDelayAnaByFile
    {
        private static VolteStatDelayAnaByFile_FDD instance = null;
        public static new VolteStatDelayAnaByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VolteStatDelayAnaByFile_FDD();
                    }
                }
            }
            return instance;
        }
        protected VolteStatDelayAnaByFile_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get { return "VOLTE_FDD时延分析(按文件)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30020, this.Name);
        }
    }
}
