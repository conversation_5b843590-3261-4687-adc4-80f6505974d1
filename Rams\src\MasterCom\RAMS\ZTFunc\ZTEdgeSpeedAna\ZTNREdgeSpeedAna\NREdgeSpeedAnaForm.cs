﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NREdgeSpeedAnaForm : MinCloseForm
    {
        public NREdgeSpeedAnaForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
        }

        public void Init(string strColText)
        {
            gridColumn4.Caption = strColText;
        }

        public void FillData(List<NREdgeSpeedInfo> edgeSpeedInfoList)
        {
            dataGrid.DataSource = edgeSpeedInfoList;
            dataGrid.RefreshDataSource();
        }

        private void outPutData_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.gridView1);
        }
    }
}
