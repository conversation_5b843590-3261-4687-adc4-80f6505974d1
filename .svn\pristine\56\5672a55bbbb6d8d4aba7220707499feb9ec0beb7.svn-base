﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ImportCellArgumentResultForm : BaseDialog
    {
        public ImportCellArgumentResultForm(string title, List<string> errorRows)
        {
            InitializeComponent();
            this.btnOK.Click += BtnOK_Click;

            this.label1.Text = title;
            StringBuilder sb = new StringBuilder();
            foreach (string s in errorRows)
            {
                sb.AppendLine(s);
            }
            this.textBox1.Text = sb.ToString();
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }
    }
}
