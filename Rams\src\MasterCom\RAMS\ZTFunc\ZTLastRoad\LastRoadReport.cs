﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LastRoadReport
    {
        public override string ToString()
        {
            return Name;
        }
        public LastRoadReport() { }
        public LastRoadReport(string name)
        {
            Name = name;
        }
        public string Name
        {
            get;
            set;
        }
        public string GISDisplaySerialName
        {
            get;
            set;
        }
        private readonly LastRoadCondition condition = new LastRoadCondition();
        public LastRoadCondition Condition
        {
            get { return condition; }
        }
        public List<TestPointDisplayColumn> DisplayColumns { get; set; } = new List<TestPointDisplayColumn>();

        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["Name"] = this.Name;
                paramDic["GISDisplaySerialName"] = this.GISDisplaySerialName;
                paramDic["Condition"] = condition.CfgParam;
                List<object> displayParams = new List<object>();
                foreach (TestPointDisplayColumn col in DisplayColumns)
                {
                    displayParams.Add(col.CfgParam);
                }
                paramDic["DisplayColumns"] = displayParams;
                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.Name = value["Name"].ToString();
                this.GISDisplaySerialName = value["GISDisplaySerialName"] as string;
                condition.CfgParam = value["Condition"] as Dictionary<string, object>;
                DisplayColumns.Clear();
                List<object> subParams = value["DisplayColumns"] as List<object>;
                foreach (object objParam in subParams)
                {
                    TestPointDisplayColumn col = new TestPointDisplayColumn();
                    col.CfgParam = objParam as Dictionary<string, object>;
                    DisplayColumns.Add(col);
                }
            }
        }

        public string ConditionDescription
        {
            get
            {
                return "持续条件：" + Environment.NewLine + condition.Description;
            }
        }

        internal bool AddDisplayColumn(TestPointDisplayColumn col)
        {
            TestPointDisplayColumn existCol = DisplayColumns.Find(
            delegate(TestPointDisplayColumn c) { return c.Equals(col); });
            if (existCol!=null)
            {
                return false;
            }
            else
            {
                DisplayColumns.Add(col);
                return true;
            }
        }

        public List<string> GetNeedParamNames()
        {
            Dictionary<string, bool> paraDic = new Dictionary<string, bool>();
            DTParameter p = null;
            List<string> sysNames = new List<string>();
            foreach (TestPointValueCondition item in condition.DetailItems)
            {
                p = DTDisplayParameterManager.GetInstance()[item.SysName, item.ParamName, item.ParamArrayIndex].Parameter;
                paraDic[p.Info.Name] = true;
                if (!sysNames.Contains(item.SysName))
                {
                    sysNames.Add(item.SysName);
                }
            }
            foreach (TestPointDisplayColumn col in DisplayColumns)
            {
                paraDic[col.DisplayParam.ParamInfo.Name] = true;
                if (!sysNames.Contains(col.DisplayParam.System.Name))
                {
                    sysNames.Add(col.DisplayParam.System.Name);
                }
            }

            setParaForCell(paraDic, sysNames);
            return new List<string>(paraDic.Keys);
        }

        private void setParaForCell(Dictionary<string, bool> paraDic, List<string> sysNames)
        {
            foreach (string sys in sysNames)
            {
                if (sys.Contains("GSM"))
                {
                    setGsmParam(paraDic, sys);
                }
                else if (sys.Contains("SCDMA"))
                {
                    setScdmaParam(paraDic, sys);
                }
                else if (sys.Contains("WCDMA"))
                {
                    setWcdmaParam(paraDic, sys);
                }
                else if (sys.Contains("CDMA"))
                {
                    /*Need2BePerfect_Qiujianwei
                     * 待优化
                     */
                }
                else if (sys.Contains("LTE"))
                {
                    setLteParam(paraDic, sys);
                }
            }
        }

        private void setGsmParam(Dictionary<string, bool> paraDic, string sys)
        {
            if (sys.Contains("SCAN"))
            {
                paraDic["GSCAN_BCCH"] = true;
                paraDic["GSCAN_BSIC"] = true;
            }
            else
            {
                paraDic["LAC"] = true;
                paraDic["CI"] = true;
                paraDic["BCCH"] = true;
                paraDic["BSIC"] = true;
            }
        }

        private void setScdmaParam(Dictionary<string, bool> paraDic, string sys)
        {
            if (sys.Contains("SCAN"))
            {
                paraDic["TDS_PCCPCH_Channel"] = true;
                paraDic["TDS_PCCPCH_CPI"] = true;
            }
            else
            {
                paraDic[MainModel.TD_SCell_LAC] = true;
                paraDic[MainModel.TD_SCell_CI] = true;
                paraDic[MainModel.TD_SCell_UARFCN] = true;
                paraDic[MainModel.TD_SCell_CPI] = true;
                paraDic["TD_SCell_RNC"] = true;
                paraDic["TD_SCell_CI"] = true;
                paraDic["TD_GSM_SCell_LAC"] = true;
                paraDic["TD_GSM_SCell_CI"] = true;
                paraDic["TD_GSM_SCell_ARFCN"] = true;
                paraDic["TD_GSM_SCell_BSIC"] = true;
            }
        }

        private void setWcdmaParam(Dictionary<string, bool> paraDic, string sys)
        {
            if (sys.Contains("SCAN"))
            {
                paraDic["WS_CPICHChannel"] = true;
                paraDic["WS_CPICHPilot"] = true;
            }
            else
            {
                paraDic["W_SysLAI"] = true;
                paraDic["W_SysCellID"] = true;
                paraDic["W_frequency"] = true;
                paraDic["W_Reference_PSC"] = true;
            }
        }

        private static void setLteParam(Dictionary<string, bool> paraDic, string sys)
        {
            if (sys.Contains("SCAN") || sys.Contains("UEP"))
            {
                paraDic["LTESCAN_TopN_EARFCN"] = true;
                paraDic["LTESCAN_TopN_PCI"] = true;
            }
            else
            {
                paraDic["lte_TAC"] = true;
                paraDic["lte_ECI"] = true;
                paraDic["lte_EARFCN"] = true;
                paraDic["lte_PCI"] = true;
            }
        }
    }
}
