using System;
using System.Collections.Generic;
using System.Text;
using System.Data;

namespace MasterCom.RAMS.CQT
{
    public class XlsPoint
    {
        public int PointID { get; set; }
        public string Name { get; set; }
        public string AddrAt { get; set; }
        public string Desc { get; set; }
        public string LTLongitude { get; set; }
        public string LTLatitude { get; set; }
        public string BRLongitude { get; set; }
        public string BRLatitude { get; set; }
        public string Altitude { get; set; }
        public string AliasName { get; set; }
        public string PointType { get; set; }
        public string DensityType { get; set; }
        public string SpaceType { get; set; }
        public string CoverType { get; set; }
        public string NetworkType { get; set; }
        public string BelongArea { get; set; }
        public string BelongArea2 { get; set; }
        public int OtherType1 { get; set; }
        public int OtherType2 { get; set; }
        public int OtherType3 { get; set; }
        public int OtherType4 { get; set; }
        public List<XlsPointCell> xlsCellList { get; set; } = new List<XlsPointCell>();

        public string NetType_Cell1
        {
            get
            {
                if (xlsCellList.Count > 0)
                {
                    return xlsCellList[0].NetWorkType;
                }
                return "";
            }
        }

        public string LAC_Cell1
        {
            get
            {
                if (xlsCellList.Count > 0)
                {
                    return xlsCellList[0].LAC;
                }
                return "";
            }
        }

        public string CI_Cell1
        {
            get
            {
                if (xlsCellList.Count > 0)
                {
                    return xlsCellList[0].CI;
                }
                return "";
            }
        }

        public string CellName_Cell1
        {
            get
            {
                if (xlsCellList.Count > 0)
                {
                    return xlsCellList[0].CellName;
                }
                return "";
            }
        }

        public string BSCName_Cell1
        {
            get
            {
                if (xlsCellList.Count > 0)
                {
                    return xlsCellList[0].BSCName;
                }
                return "";
            }
        }

        public string NetType_Cell2
        {
            get
            {
                if (xlsCellList.Count > 1)
                {
                    return xlsCellList[1].NetWorkType;
                }
                return "";
            }
        }

        public string LAC_Cell2
        {
            get
            {
                if (xlsCellList.Count > 1)
                {
                    return xlsCellList[1].LAC;
                }
                return "";
            }
        }

        public string CI_Cell2
        {
            get
            {
                if (xlsCellList.Count > 1)
                {
                    return xlsCellList[1].CI;
                }
                return "";
            }
        }

        public string CellName_Cell2
        {
            get
            {
                if (xlsCellList.Count > 1)
                {
                    return xlsCellList[1].CellName;
                }
                return "";
            }
        }

        public string BSCName_Cell2
        {
            get
            {
                if (xlsCellList.Count > 1)
                {
                    return xlsCellList[1].BSCName;
                }
                return "";
            }
        }

        public string NetType_Cell3
        {
            get
            {
                if (xlsCellList.Count > 2)
                {
                    return xlsCellList[2].NetWorkType;
                }
                return "";
            }
        }

        public string LAC_Cell3
        {
            get
            {
                if (xlsCellList.Count > 2)
                {
                    return xlsCellList[2].LAC;
                }
                return "";
            }
        }

        public string CI_Cell3
        {
            get
            {
                if (xlsCellList.Count > 2)
                {
                    return xlsCellList[2].CI;
                }
                return "";
            }
        }

        public string CellName_Cell3
        {
            get
            {
                if (xlsCellList.Count > 2)
                {
                    return xlsCellList[2].CellName;
                }
                return "";
            }
        }

        public string BSCName_Cell3
        {
            get
            {
                if (xlsCellList.Count > 2)
                {
                    return xlsCellList[2].BSCName;
                }
                return "";
            }
        }

        public string NetType_Cell4
        {
            get
            {
                if (xlsCellList.Count > 3)
                {
                    return xlsCellList[3].NetWorkType;
                }
                return "";
            }
        }

        public string LAC_Cell4
        {
            get
            {
                if (xlsCellList.Count > 3)
                {
                    return xlsCellList[3].LAC;
                }
                return "";
            }
        }

        public string CI_Cell4
        {
            get
            {
                if (xlsCellList.Count > 3)
                {
                    return xlsCellList[3].CI;
                }
                return "";
            }
        }

        public string CellName_Cell4
        {
            get
            {
                if (xlsCellList.Count > 3)
                {
                    return xlsCellList[3].CellName;
                }
                return "";
            }
        }

        public string BSCName_Cell4
        {
            get
            {
                if (xlsCellList.Count > 3)
                {
                    return xlsCellList[3].BSCName;
                }
                return "";
            }
        }

        public string NetType_Cell5
        {
            get
            {
                if (xlsCellList.Count > 4)
                {
                    return xlsCellList[4].NetWorkType;
                }
                return "";
            }
        }

        public string LAC_Cell5
        {
            get
            {
                if (xlsCellList.Count > 4)
                {
                    return xlsCellList[4].LAC;
                }
                return "";
            }
        }

        public string CI_Cell5
        {
            get
            {
                if (xlsCellList.Count > 4)
                {
                    return xlsCellList[4].CI;
                }
                return "";
            }
        }

        public string CellName_Cell5
        {
            get
            {
                if (xlsCellList.Count > 4)
                {
                    return xlsCellList[4].CellName;
                }
                return "";
            }
        }

        public string BSCName_Cell5
        {
            get
            {
                if (xlsCellList.Count > 4)
                {
                    return xlsCellList[4].BSCName;
                }
                return "";
            }
        }

        public string NetType_Cell6
        {
            get
            {
                if (xlsCellList.Count > 5)
                {
                    return xlsCellList[5].NetWorkType;
                }
                return "";
            }
        }

        public string LAC_Cell6
        {
            get
            {
                if (xlsCellList.Count > 5)
                {
                    return xlsCellList[5].LAC;
                }
                return "";
            }
        }

        public string CI_Cell6
        {
            get
            {
                if (xlsCellList.Count > 5)
                {
                    return xlsCellList[5].CI;
                }
                return "";
            }
        }

        public string CellName_Cell6
        {
            get
            {
                if (xlsCellList.Count > 5)
                {
                    return xlsCellList[5].CellName;
                }
                return "";
            }
        }

        public string BSCName_Cell6
        {
            get
            {
                if (xlsCellList.Count > 5)
                {
                    return xlsCellList[5].BSCName;
                }
                return "";
            }
        }

        public List<string> lightCellList { get; set; } = new List<string>();

        public XlsPoint()
        {
        }

        public XlsPoint(DataRow row)
        {
            int index = 0;

            this.Name = row[index++].ToString();
            this.AddrAt = row[index++].ToString();
            this.Desc = row[index++].ToString();
            this.LTLongitude = row[index++].ToString();
            this.LTLatitude = row[index++].ToString();
            this.BRLongitude = row[index++].ToString();
            this.BRLatitude = row[index++].ToString();
            this.Altitude = row[index++].ToString();
            this.AliasName = row[index++].ToString();
            this.PointType = row[index++].ToString();
            this.DensityType = row[index++].ToString();
            this.SpaceType = row[index++].ToString();
            this.CoverType = row[index++].ToString();
            this.NetworkType = row[index++].ToString();
            this.BelongArea = row[index++].ToString();
            this.BelongArea2 = row[index++].ToString();

            for(int i = 0; i < 6; i++)
            {
                XlsPointCell pointCell = new XlsPointCell(row, ref index);
                if (pointCell.LAC.Equals("") || pointCell.CI.Equals(""))
                    continue;
                xlsCellList.Add(pointCell);
                pointCell.Priority = xlsCellList.Count;
            }
        }

        public CQTPoint ConvertToCQTPoint()
        {
            CQTPoint cqtPoint = new CQTPoint();
            cqtPoint.ID = this.PointID;
            cqtPoint.AddrAt = this.AddrAt;
            cqtPoint.AliasName = this.AliasName;
            cqtPoint.Altitude = int.Parse(this.Altitude);
            cqtPoint.BelongArea = this.BelongArea;
            cqtPoint.BelongArea2 = this.BelongArea2;
            cqtPoint.BRLatitude = double.Parse(this.BRLatitude);
            cqtPoint.BRLongitude = double.Parse(this.BRLongitude);
            cqtPoint.CoverType = CQTCfgManager.GetInstance().GetCQTCoverType(this.CoverType);
            cqtPoint.DenstityType = CQTCfgManager.GetInstance().GetCQTDensityType(this.DensityType);
            cqtPoint.Desc = this.Desc;
            cqtPoint.LTLatitude = double.Parse(this.LTLatitude);
            cqtPoint.LTLongitude = double.Parse(this.LTLongitude);
            cqtPoint.Name = this.Name;
            cqtPoint.NetworkType = new List<CQTNetworkType>();
            foreach (string netType in this.NetworkType.Split(';'))
            {
                cqtPoint.NetworkType.Add(CQTCfgManager.GetInstance().GetCQTNetworkType(netType));
            }
            //cqtPoint.NetworkType
            cqtPoint.PointType = CQTCfgManager.GetInstance().GetCQTPointType(this.PointType);
            cqtPoint.SpaceType = CQTCfgManager.GetInstance().GetCQTSpaceType(this.SpaceType);
            return cqtPoint;
        }

        public void SetPointID(int pointID)
        {
            this.PointID = pointID;
            foreach (XlsPointCell pointCell in xlsCellList)
            {
                pointCell.PointID = pointID;
            }
        }

        public bool check()
        {
            return checkNull() || checkDigit() || checkPointType() || checkDensity() || checkSpaceType() || checkCoverType() || checkNetType();
        }

        private bool checkNull()
        {
            return checkNull(this.Name) || checkNull(this.AddrAt) || checkNull(this.LTLongitude) || checkNull(this.LTLatitude) ||
                checkNull(this.BRLongitude) || checkNull(this.BRLatitude) || checkNull(this.PointType) || checkNull(this.DensityType) ||
                checkNull(this.SpaceType) || checkNull(this.CoverType) || checkNull(this.NetworkType);
        }

        private bool checkDigit()
        {
            return checkDouble(this.LTLongitude) || checkDouble(this.LTLatitude) || checkDouble(this.BRLongitude) || checkDouble(this.BRLatitude) || checkInt(this.Altitude);
        }

        private bool checkPointType()
        {
            return CQTPointType.checkIllegal(this.PointType);
        }

        private bool checkDensity()
        {
            return CQTDensityType.checkIllegal(this.DensityType);
        }

        private bool checkSpaceType()
        {
            return CQTSpaceType.checkIllegal(this.SpaceType);
        }

        private bool checkCoverType()
        {
            return CQTCoverType.checkIllegal(this.CoverType);
        }

        private bool checkNetType()
        {
            return CQTNetworkType.checkIllegal(this.NetworkType, ';');
        }

        private bool checkNull(string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                return true;
            }
            return false;
        }

        private bool checkDouble(string digit)
        {
            double result;
            if (!double.TryParse(digit, out result) || result < 0 || result > 360)
            {
                return true;
            }
            return false;
        }

        private bool checkInt(string digit)
        {
            int result;
            if (!int.TryParse(digit, out result))
            {
                return true;
            }
            return false;
        }
    }

    public class XlsPointCell
    {
        public int PointID { get; set; } 
        public string NetWorkType { get; set; } 
        public string LAC { get; set; } 
        public string CI { get; set; } 
        public string CellName { get; set; } 
        public string BSCName { get; set; } 
        public int Priority { get; set; } 

        public XlsPointCell()
        {
        }

        public XlsPointCell(DataRow row, ref int index)
        {
            this.NetWorkType = row[index++].ToString();
            this.LAC = row[index++].ToString();
            this.CI = row[index++].ToString();
            this.CellName = row[index++].ToString();
            this.BSCName = row[index++].ToString();
        }

        public bool checkNull()
        {
            return checkNull(this.NetWorkType) || checkNull(this.LAC) || checkNull(this.CI) || checkNetType();
        }

        private bool checkNull(string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                return true;
            }
            return false;
        }

        private bool checkNetType()
        {
            if (this.NetWorkType.IndexOf(';') >= 0)
            {
                return true;
            }
            return CQTNetworkType.checkIllegal(this.NetWorkType, ';');
        }
    }

    public class XlsAtuPoint
    {
        public int PointID { get; set; } = 1;
        public int Altitude { get; set; } = 100;
        public string TESTPOINT_ID { get; set; }
        public string TESTPOINT_NAME { get; set; }
        public string TESTPOINT_DES { get; set; }
        public string LT_LONGITUDE { get; set; }
        public string LT_LATITUDE { get; set; }
        public string GRID_ID { get; set; }
        public string GROUP_ID { get; set; }
        public string RB_LONGITUDE { get; set; }
        public string RB_LATITUDE { get; set; }
        public string CIVILIAN_NAME { get; set; }
        public string OFFICIAL_ADDRESS { get; set; }
        public string CIVILIAN_ADDRESS { get; set; }
        public string FUNCTION_CLASS { get; set; }
        public string DENSITY_CLASS { get; set; }
        public string INFLUENCE_CLASS { get; set; }
        public string AREA_CLASS { get; set; }
        public string SPATIAL_CLASS { get; set; }
        public string COVER_CLASS { get; set; }
        public string NETWORK_CLASS { get; set; }
        public string SERVICE_SELL { get; set; }
        public string TESTPOINT_IDENTIFIER { get; set; }
        public string BASE_ALTITUDE { get; set; }
        
        public int OtherType1 { get; set; } = 1;
        public int OtherType2 { get; set; }
        public int OtherType3 { get; set; }
        public int OtherType4 { get; set; }

        public XlsAtuPoint()
        {
        }

        public XlsAtuPoint(DataRow row)
        {
            int index = 0;

            this.TESTPOINT_ID = row[index++].ToString();
            this.TESTPOINT_NAME = row[index++].ToString();
            this.TESTPOINT_DES = row[index++].ToString();
            this.LT_LONGITUDE = row[index++].ToString();
            this.LT_LATITUDE = row[index++].ToString();
            this.GRID_ID = row[index++].ToString();
            this.GROUP_ID = row[index++].ToString();
            this.RB_LONGITUDE = row[index++].ToString();
            this.RB_LATITUDE = row[index++].ToString();
            this.CIVILIAN_NAME = row[index++].ToString();
            this.OFFICIAL_ADDRESS = row[index++].ToString();
            this.CIVILIAN_ADDRESS = row[index++].ToString();
            this.FUNCTION_CLASS = row[index++].ToString();
            this.DENSITY_CLASS = row[index++].ToString();
            this.INFLUENCE_CLASS = row[index++].ToString();
            this.AREA_CLASS = row[index++].ToString();
            this.SPATIAL_CLASS = row[index++].ToString();
            this.COVER_CLASS = row[index++].ToString();
            this.NETWORK_CLASS = row[index++].ToString();
            this.SERVICE_SELL = row[index++].ToString();
            this.TESTPOINT_IDENTIFIER = row[index++].ToString();
            this.BASE_ALTITUDE = row[index].ToString();
        }

        public bool check()
        {
            return checkDigit() || checkPointType() || checkDensity() || checkSpaceType() || checkCoverType() || checkNetType();
        }

        private bool checkDigit()
        {
            return checkDouble(this.LT_LONGITUDE) || checkDouble(this.LT_LATITUDE) || checkDouble(this.RB_LONGITUDE) || checkDouble(this.RB_LATITUDE);
        }

        private bool checkPointType()
        {
            return CQTPointType.checkIllegal(this.FUNCTION_CLASS);
        }

        private bool checkDensity()
        {
            return CQTDensityType.checkIllegal(this.DENSITY_CLASS);
        }

        private bool checkSpaceType()
        {
            return CQTSpaceType.checkIllegal(this.SPATIAL_CLASS);
        }

        private bool checkCoverType()
        {
            return CQTCoverType.checkIllegal(this.COVER_CLASS);
        }

        private bool checkNetType()
        {
            return CQTNetworkType.checkIllegal(this.NETWORK_CLASS, '/');
        }

        private bool checkDouble(string digit)
        {
            double result;
            if (!double.TryParse(digit, out result) || result < 0 || result > 360)
            {
                return true;
            }
            return false;
        }

        public CQTPoint ConvertToCQTPoint()
        {
            CQTPoint cqtPoint = new CQTPoint();
            cqtPoint.ID = this.PointID;
            cqtPoint.AddrAt = this.OFFICIAL_ADDRESS;
            cqtPoint.AliasName = this.CIVILIAN_NAME;
            cqtPoint.Altitude = this.Altitude;
            cqtPoint.BelongArea = this.GRID_ID;
            cqtPoint.BelongArea2 = this.GROUP_ID;
            cqtPoint.BRLatitude = double.Parse(this.RB_LATITUDE);
            cqtPoint.BRLongitude = double.Parse(this.RB_LONGITUDE);
            cqtPoint.CoverType = CQTCfgManager.GetInstance().GetCQTCoverType(this.COVER_CLASS);
            cqtPoint.DenstityType = CQTCfgManager.GetInstance().GetCQTDensityType(this.DENSITY_CLASS);
            cqtPoint.Desc = this.CIVILIAN_ADDRESS;
            cqtPoint.LTLatitude = double.Parse(this.LT_LATITUDE);
            cqtPoint.LTLongitude = double.Parse(this.LT_LONGITUDE);
            cqtPoint.Name = this.TESTPOINT_DES;
            cqtPoint.NetworkType = new List<CQTNetworkType>();
            foreach (string netType in this.NETWORK_CLASS.Split(';'))
            {
                cqtPoint.NetworkType.Add(CQTCfgManager.GetInstance().GetCQTNetworkType(netType));
            }
            //cqtPoint.NetworkType
            cqtPoint.PointType = CQTCfgManager.GetInstance().GetCQTPointType(this.FUNCTION_CLASS);
            cqtPoint.SpaceType = CQTCfgManager.GetInstance().GetCQTSpaceType(this.SPATIAL_CLASS);
            return cqtPoint;
        }
    }
}
