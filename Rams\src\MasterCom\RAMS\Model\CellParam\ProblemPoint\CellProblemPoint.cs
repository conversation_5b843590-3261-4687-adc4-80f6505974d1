﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model.CellParam
{
    public class CellProblemPoint
    {
        public override string ToString()
        {
            if (Cell == null)
            {
                return string.Empty;
            }
            return Cell.ToString();
        }
        public object Cell { get; set; }
        public string CellName { get; set; } = string.Empty;
        public int ID { get; set; } = -1;
        public DateTime DateTime { get; set; }
        public short StateID { get; set; } = -1;
        public string Description { get; set; } = string.Empty;
        public int RuleID { get; set; } = -1;
        public ProblemPointRuleDetail Rule { get; set; }
        public int CellSignID { get; set; } = -1;

        public string RuleName
        {
            get;
            set;
        }
        public string RuleDescription
        {
            get;
            set;
        }
        public string CheckValue
        {
            get
            {
                if (Rule == null)
                {
                    return null;
                }
                return Rule.CheckValue;
            }
        }
        public string CheckDescription
        {
            get
            {
                if (Rule == null)
                {
                    return null;
                }
                return Rule.CheckDescription;
            }
        }
        public string DefaultValue
        {
            get
            {
                if (Rule == null)
                {
                    return null;
                }
                return Rule.DefaultValue;
            }
        }
        public string Range
        {
            get
            {
                if (Rule == null)
                {
                    return null;
                }
                return Rule.Range;
            }
        }
        public string ParamName
        {
            get
            {
                if (Rule == null)
                {
                    return null;
                }
                return Rule.ParamName;
            }
        }

    }

}
