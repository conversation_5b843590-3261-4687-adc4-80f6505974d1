﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.CellCoverage
{
    public class CellCoverAreaItem
    {
        public CellCoverAreaItem(CellCoverMainItem cellItem, AreaBase area)
        {
            this.CellCoverItem = cellItem;
            this.Area = area;
            DataGroup = new KPIDataGroup(area);
        }
        public AreaBase Area
        {
            get;
            private set;
        }
        public CellCoverMainItem CellCoverItem
        {
            get;
            private set;
        }

        public KPIDataGroup DataGroup { get; set; }
        private readonly Dictionary<string, double> expValueDic = new Dictionary<string, double>();
        public double this[string exp]
        {
            get
            {
                double ret;
                if (!expValueDic.TryGetValue(exp, out ret))
                {
                    ret = double.NaN;
                }
                return ret;
            }
            set
            {
                expValueDic[exp] = value;
            }
        }

        public List<ISite> NearestSite
        {
            get;
            private set;
        }
        public string NearestSiteNames
        {
            get;
            private set;
        }
        public double NearestSiteDistance
        {
            get;
            private set;
        }
        public double CoverCellDistance
        {
            get;
            private set;
        }
        public string IsNearestCellDesc
        {
            get;
            private set;
        }

        internal void MakeSumamry()
        {
            NearestSiteDistance = double.MaxValue;
            NearestSite = new List<ISite>();
            MasterCom.MTGis.DbRect exBounds = new MTGis.DbRect(Area.Bounds.x1 - 0.5, Area.Bounds.y1 - 0.5
                , Area.Bounds.x2 + 0.5, Area.Bounds.y2 + 0.5);
            MasterCom.MTGis.DbPoint centerPt = new MTGis.DbPoint(Area.Shape.Centroid.x, Area.Shape.Centroid.y);

            CoverCellDistance = Math.Round(
                MasterCom.Util.MathFuncs.GetDistance(CellCoverItem.Cell.Longitude, CellCoverItem.Cell.Latitude
                , centerPt.x, centerPt.y), 2);

            if (CellCoverItem.Cell is Cell)
            {
                foreach (ISite site in CellManager.GetInstance().GetCurrentBTSs())
                {
                    addNearestSite(exBounds, centerPt, site);
                }
            }
            else if (CellCoverItem.Cell is TDCell)
            {
                foreach (ISite site in CellManager.GetInstance().GetCurrentTDBTSs())
                {
                    addNearestSite(exBounds, centerPt, site);
                }
            }
            else if (CellCoverItem.Cell is LTECell)
            {
                foreach (ISite site in CellManager.GetInstance().GetCurrentLTEBTSs())
                {
                    addNearestSite(exBounds, centerPt, site);
                }
            }
            setNearestCellInfo();
        }

        private void addNearestSite(MTGis.DbRect exBounds, MTGis.DbPoint centerPt, ISite site)
        {
            if (exBounds.IsPointInThisRect(site.Longitude, site.Latitude))
            {
                double dis = Math.Round(MasterCom.Util.MathFuncs.GetDistance(site.Longitude, site.Latitude, centerPt.x, centerPt.y)
                    , 2);
                if (dis == NearestSiteDistance)
                {
                    NearestSite.Add(site);
                }
                else if (dis < NearestSiteDistance)
                {
                    NearestSite.Clear();
                    NearestSite.Add(site);
                    NearestSiteDistance = dis;
                }
            }
        }

        private void setNearestCellInfo()
        {
            IsNearestCellDesc = "否";
            StringBuilder sb = new StringBuilder();
            foreach (ISite site in NearestSite)
            {
                sb.Append(site.Name + ";");
                if (CellCoverItem.Cell is Cell)
                {
                    if (((BTS)site).Cells.Contains(CellCoverItem.Cell as Cell))
                    {
                        IsNearestCellDesc = "是";
                    }
                }
                else if (CellCoverItem.Cell is TDCell)
                {
                    if (((TDNodeB)site).Cells.Contains(CellCoverItem.Cell as TDCell))
                    {
                        IsNearestCellDesc = "是";
                    }
                }
                else if (CellCoverItem.Cell is LTECell)
                {
                    if (((LTEBTS)site).Cells.Contains(CellCoverItem.Cell as LTECell))
                    {
                        IsNearestCellDesc = "是";
                    }
                }
                else
                {
                    //
                }
            }
            NearestSiteNames = sb.ToString();
            NearestSiteNames = NearestSiteNames.TrimEnd(';');
        }
    }
}
