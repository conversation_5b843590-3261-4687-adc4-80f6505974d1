using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Model
{
    public class VisibleOffsetManager
    {
        public VisibleOffsetManager(MainModel mainModel)
        {
            mainModel.DTDataChanged += delegate(object sender, EventArgs e)
            {
                visibleOffsetFileMap.Clear();
                foreach (DTFileDataManager dtFileDataManager in mainModel.DTDataManager.FileDataManagers)
                {
                    visibleOffsetFileMap[dtFileDataManager.FileID] = new VisibleOffset();
                }
            };
            FavoriteSerialConfig.Instance.FavoriteSerialsChanged += delegate()
            {
                foreach (MapSerialInfo msi in FavoriteSerialConfig.Instance.FavoriteSerialInfos)
                {
                    if (!visibleOffsetCommonKPIMap.ContainsKey(msi))
                    {
                        visibleOffsetCommonKPIMap.Add(msi, new VisibleOffset());
                    }
                }
            };
        }

        public void Init()
        {
            visibleOffsetProjectMap.Clear();
            if (CategoryManager.GetInstance()["Project"]!=null)
            {
                foreach (CategoryEnumItem item in ((CategoryEnum)CategoryManager.GetInstance()["Project"]).Items)
                {
                    visibleOffsetProjectMap[item.ID] = new VisibleOffset();
                }
            }
            
            visibleOffsetServiceMap.Clear();
            if (CategoryManager.GetInstance()["ServiceType"]!=null)
            {
                foreach (CategoryEnumItem item in ((CategoryEnum)CategoryManager.GetInstance()["ServiceType"]).Items)
                {
                    visibleOffsetServiceMap[item.ID] = new VisibleOffset();
                }
            }
            
        }

        public void SetFileVisible(int fileID, bool visible)
        {
            if (visibleOffsetFileMap.ContainsKey(fileID) && visibleOffsetFileMap[fileID].Visible != visible)
            {
                visibleOffsetFileMap[fileID].Visible = visible;
                fireVisibleOffsetChanged();
            }
        }

        public void SetProjectVisible(int projectID, bool visible)
        {
            if (visibleOffsetProjectMap.ContainsKey(projectID) && visibleOffsetProjectMap[projectID].Visible != visible)
            {
                visibleOffsetProjectMap[projectID].Visible = visible;
                fireVisibleOffsetChanged();
            }
        }

        public void SetServiceVisible(int serviceType, bool visible)
        {
            if (visibleOffsetServiceMap.ContainsKey(serviceType) && visibleOffsetServiceMap[serviceType].Visible != visible)
            {
                visibleOffsetServiceMap[serviceType].Visible = visible;
                fireVisibleOffsetChanged();
            }
        }

        public void SetCommonKPIVisible(MapSerialInfo serialInfo, bool visible)
        {
            if (visibleOffsetCommonKPIMap.ContainsKey(serialInfo) && visibleOffsetCommonKPIMap[serialInfo].Visible != visible)
            {
                visibleOffsetCommonKPIMap[serialInfo].Visible = visible;
                serialInfo.Visible = visible;
                fireVisibleOffsetChanged();
            }
        }

        public void SetFileOffset(int fileID, int offsetX, int offsetY)
        {
            if (visibleOffsetFileMap.ContainsKey(fileID)
                && (visibleOffsetFileMap[fileID].OffsetX != offsetX
                || visibleOffsetFileMap[fileID].OffsetY != offsetY))
            {
                visibleOffsetFileMap[fileID].OffsetX = offsetX;
                visibleOffsetFileMap[fileID].OffsetY = offsetY;
                fireVisibleOffsetChanged();
            }
        }

        public void SetProjectOffset(int projectID, int offsetX, int offsetY)
        {
            if (visibleOffsetProjectMap.ContainsKey(projectID)
                && (visibleOffsetProjectMap[projectID].OffsetX != offsetX
                || visibleOffsetProjectMap[projectID].OffsetY != offsetY))
            {
                visibleOffsetProjectMap[projectID].OffsetX = offsetX;
                visibleOffsetProjectMap[projectID].OffsetY = offsetY;
                fireVisibleOffsetChanged();
            }
        }

        public void SetServiceOffset(int serviceType, int offsetX, int offsetY)
        {
            if (visibleOffsetServiceMap.ContainsKey(serviceType)
                && (visibleOffsetServiceMap[serviceType].OffsetX != offsetX
                || visibleOffsetServiceMap[serviceType].OffsetY != offsetY))
            {
                visibleOffsetServiceMap[serviceType].OffsetX = offsetX;
                visibleOffsetServiceMap[serviceType].OffsetY = offsetY;
                fireVisibleOffsetChanged();
            }
        }

        public void SetCommonKPIOffset(MapSerialInfo serialInfo, int offsetX, int offsetY)
        {
            if (visibleOffsetCommonKPIMap.ContainsKey(serialInfo) &&
                (visibleOffsetCommonKPIMap[serialInfo].OffsetX != offsetX ||
                visibleOffsetCommonKPIMap[serialInfo].OffsetY != offsetY))
            {
                visibleOffsetCommonKPIMap[serialInfo].OffsetX = offsetX;
                visibleOffsetCommonKPIMap[serialInfo].OffsetY = offsetY;
                fireVisibleOffsetChanged();
            }
        }

        public bool IsFileVisble(int fileID)
        {
            if (visibleOffsetFileMap.ContainsKey(fileID))
            {
                return visibleOffsetFileMap[fileID].Visible;
            }
            return false;
        }

        public bool IsProjectVisble(int projectID)
        {
            if (projectID == 0)
            {
                return true;
            }
            if (visibleOffsetProjectMap.Count==0)
            {
                return true;
            }
            if (visibleOffsetProjectMap.ContainsKey(projectID))
            {
                return visibleOffsetProjectMap[projectID].Visible;
            }
            return false;
        }

        public bool IsServiceVisble(int serviceType)
        {
            if (serviceType == 0)
            {
                return true;
            }
            if (visibleOffsetServiceMap.Count==0)
            {
                return true;
            }
            if (visibleOffsetServiceMap.ContainsKey(serviceType))
            {
                return visibleOffsetServiceMap[serviceType].Visible;
            }
            return false;
        }

        public bool IsCommonKPIVisible(MapSerialInfo serialInfo)
        {
            if (!visibleOffsetCommonKPIMap.ContainsKey(serialInfo))
            {
                return true;
            }
            return visibleOffsetCommonKPIMap[serialInfo].Visible;
        }

        public void GetFileOffset(ref int offsetX, ref int offsetY, int fileID)
        {
            if (visibleOffsetFileMap.ContainsKey(fileID))
            {
                offsetX += visibleOffsetFileMap[fileID].OffsetX;
                offsetY += visibleOffsetFileMap[fileID].OffsetY;
            }
        }

        public void GetProjectOffset(ref int offsetX, ref int offsetY, int projectID)
        {
            if (visibleOffsetProjectMap.ContainsKey(projectID))
            {
                offsetX += visibleOffsetProjectMap[projectID].OffsetX;
                offsetY += visibleOffsetProjectMap[projectID].OffsetY;
            }
        }

        public void GetServiceOffset(ref int offsetX, ref int offsetY, int serviceType)
        {
            if (visibleOffsetServiceMap.ContainsKey(serviceType))
            {
                offsetX += visibleOffsetServiceMap[serviceType].OffsetX;
                offsetY += visibleOffsetServiceMap[serviceType].OffsetY;
            }
        }

        public void GetCommonKPIOffset(ref int offsetX, ref int offsetY, MapSerialInfo serialInfo)
        {
            if (visibleOffsetCommonKPIMap.ContainsKey(serialInfo))
            {
                offsetX += visibleOffsetCommonKPIMap[serialInfo].OffsetX;
                offsetY += visibleOffsetCommonKPIMap[serialInfo].OffsetY;
            }
        }

        public void GetOffset(ref int offsetX, ref int offsetY, int fileID, int projectID, int serviceType)
        {
            GetFileOffset(ref offsetX, ref offsetY, fileID);
            GetProjectOffset(ref offsetX, ref offsetY, projectID);
            GetServiceOffset(ref offsetX, ref offsetY, serviceType);
        }

        private void fireVisibleOffsetChanged()
        {
            if (VisibleOffsetChanged != null)
            {
                VisibleOffsetChanged(this, EventArgs.Empty);
            }
        }

        public event EventHandler VisibleOffsetChanged;

        private readonly Dictionary<int, VisibleOffset> visibleOffsetFileMap = new Dictionary<int, VisibleOffset>();

        private readonly Dictionary<int, VisibleOffset> visibleOffsetProjectMap = new Dictionary<int, VisibleOffset>();

        private readonly Dictionary<int, VisibleOffset> visibleOffsetServiceMap = new Dictionary<int, VisibleOffset>();
        
        private Dictionary<MapSerialInfo, VisibleOffset> visibleOffsetCommonKPIMap { get; set; } = new Dictionary<MapSerialInfo, VisibleOffset>();

        private class VisibleOffset
        {
            public VisibleOffset()
            {
                Visible = true;
            }

            public bool Visible { get; set; }

            public int OffsetX { get; set; }

            public int OffsetY { get; set; }
        }
    }
}
