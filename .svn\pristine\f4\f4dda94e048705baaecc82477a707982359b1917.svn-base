﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTScanLTELowSINRRoadSetForm : Form
    {
        public ZTScanLTELowSINRRoadSetForm()
        {
            InitializeComponent();
        }

        private void simpleBtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void simpleBtnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        public void ResetDlg(int sinr, int distance)
        {
            this.spinEditSINR.Value = sinr;
            this.spinEditDistance.Value = distance;
        }

        public int SINRMax
        {
            get { return (int)spinEditSINR.Value; }
        }

        public int DistanceMin
        {
            get { return (int)spinEditDistance.Value; }
        }
    }
}
