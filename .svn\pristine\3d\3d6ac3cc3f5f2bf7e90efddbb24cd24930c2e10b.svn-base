﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    public class KPITemplateManager
    {
        public static readonly string CfgFileName = string.Format(System.Windows.Forms.Application.StartupPath + "/config/KPITemplateManager.xml");
        private static KPITemplateManager instance = null;
        private KPITemplateManager()
        {
            LoadCfg();
        }
        public static KPITemplateManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new KPITemplateManager();
                }
                return instance;
            }
        }
        
        public List<KPITemplate> Templates { get; set; } = new List<KPITemplate>();
        private List<object> cfgParam
        {
            get
            {
                List<object> rpts = new List<object>();
                foreach (KPITemplate rpt in Templates)
                {
                    rpts.Add(rpt.CfgParam);
                }
                return rpts;
            }
            set {
                if (value==null)
                {
                    return;
                }
                Templates.Clear();
                foreach (object obj in value)
                {
                    KPITemplate rpt = new KPITemplate();
                    rpt.CfgParam = obj as Dictionary<string, object>;
                    Templates.Add(rpt);
                }
            }
        }

        public bool LoadCfg()
        {
            if (File.Exists(CfgFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(CfgFileName);
                cfgParam = configFile.GetItemValue("KPIColumnCfg", "Templates") as List<object>;
                return true;
            }

            return false;
        }

        public void Save()
        {
            MasterCom.Util.XmlConfigFile xmlFile = new MasterCom.Util.XmlConfigFile();
            XmlElement cfgE = xmlFile.AddConfig("KPIColumnCfg");
            xmlFile.AddItem(cfgE, "Templates", this.cfgParam);
            xmlFile.Save(CfgFileName);
        }



    }
}
