﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    public partial class CellErrorPnl : UserControl
    {
        public CellErrorPnl()
        {
            InitializeComponent();
        }

        CellErrorCause mainReason = null;

        public void LinkCondition(CellErrorCause reason)
        {
            this.mainReason = reason;

            numSecondBefore.Value = (decimal)reason.SecondBefore;
            numSecondBefore.ValueChanged += numSecondBefore_ValueChanged;

            numSecondAfter.Value = (decimal)reason.SecondAfter;
            numSecondAfter.ValueChanged += numSecondAfter_ValueChanged;
        }


        void numSecondBefore_ValueChanged(object sender, EventArgs e)
        {
            mainReason.SecondBefore = (int)numSecondBefore.Value;
        }

        void numSecondAfter_ValueChanged(object sender, EventArgs e)
        {
            mainReason.SecondAfter = (int)numSecondAfter.Value;
        }
    }
}
