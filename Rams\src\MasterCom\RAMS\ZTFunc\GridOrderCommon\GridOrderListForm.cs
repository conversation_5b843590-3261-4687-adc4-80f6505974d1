﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.GridOrderCommon
{
    public partial class GridOrderListForm : MinCloseForm
    {
        public GridOrderListForm()
        {
            InitializeComponent();
            init();
        }

        private void init()
        {
            this.tabCtrl.Dock = DockStyle.Fill;
            this.lv.MouseDoubleClick += lv_MouseDoubleClick;

            this.colAreaNames.AspectGetter += delegate (object row)
            {
                if (row is GridOrder)
                {
                    GridOrder item = row as GridOrder;
                    return item.AreaNames;
                }
                return null;
            };

            this.colDistrictName.AspectGetter += delegate (object row)
            {
                if (row is GridOrder)
                {
                    GridOrder item = row as GridOrder;
                    return item.CityName;
                }
                return null;
            };

            this.colItemCount.AspectGetter += delegate (object row)
            {
                if (row is GridOrder)
                {
                    GridOrder item = row as GridOrder;
                    return item.GridCount;
                }
                return null;
            };

            this.colGridSN.AspectGetter += delegate (object row)
            {
                if (row is OrderCellItem)
                {
                    OrderCellItem item = row as OrderCellItem;
                    return item.GridSN;
                }
                else if (row is OrderGridItem)
                {
                    OrderGridItem item = row as OrderGridItem;
                    return item.GridSN;
                }
                return null;
            };

            setOrderCellItem();

            this.colLat.AspectGetter += delegate (object row)
            {
                if (row is OrderGridItem)
                {
                    OrderGridItem item = row as OrderGridItem;
                    return item.LTLat;
                }
                return null;
            };

            this.colLng.AspectGetter += delegate (object row)
            {
                if (row is OrderGridItem)
                {
                    OrderGridItem item = row as OrderGridItem;
                    return item.LTLng;
                }
                return null;
            };

            this.colOrderTypeName.AspectGetter += delegate (object row)
            {
                if (row is GridOrder)
                {
                    GridOrder item = row as GridOrder;
                    return item.TokenName;
                }
                return null;
            };

            this.colRoadNames.AspectGetter += delegate (object row)
            {
                if (row is GridOrder)
                {
                    GridOrder item = row as GridOrder;
                    return item.RoadNames;
                }
                return null;
            };

            this.colTaskID.AspectGetter += delegate (object row)
            {
                if (row is GridOrder)
                {
                    GridOrder item = row as GridOrder;
                    return item.TaskID;
                }
                return null;
            };

            this.colStatusDesc.AspectGetter += delegate (object row)
            {
                if (row is GridOrder)
                {
                    GridOrder item = row as GridOrder;
                    return item.StatusDesc;
                }
                return null;
            };

            this.lv.CanExpandGetter += delegate (object row)
            {
                return row is GridOrder || row is OrderGridItem;
            };

            this.lv.ChildrenGetter += delegate (object row)
            {
                if (row is GridOrder)
                {
                    GridOrder order = row as GridOrder;
                    return order.Grids;
                }
                else if (row is OrderGridItem)
                {
                    OrderGridItem item = row as OrderGridItem;
                    return item.Cells;
                }
                return null;
            };

        }

        private void setOrderCellItem()
        {
            this.colCellName.AspectGetter += delegate (object row)
            {
                if (row is OrderCellItem)
                {
                    OrderCellItem item = row as OrderCellItem;
                    return item.CellName;
                }
                return null;
            };

            this.colCi.AspectGetter += delegate (object row)
            {
                if (row is OrderCellItem)
                {
                    OrderCellItem item = row as OrderCellItem;
                    return item.Ci;
                }
                return null;
            };

            this.colDetail.AspectGetter += delegate (object row)
            {
                if (row is OrderCellItem)
                {
                    OrderCellItem item = row as OrderCellItem;
                    return item.Detail;
                }
                return null;
            };

            this.colIsProbCell.AspectGetter += delegate (object row)
            {
                if (row is OrderCellItem)
                {
                    OrderCellItem item = row as OrderCellItem;
                    return item.IsProbCell;
                }
                return null;
            };

            this.colFileName.AspectGetter += delegate (object row)
            {
                if (row is OrderCellItem)
                {
                    OrderCellItem item = row as OrderCellItem;
                    return item.FileName;
                }
                return null;
            };

            this.colLac.AspectGetter += delegate (object row)
            {
                if (row is OrderCellItem)
                {
                    OrderCellItem item = row as OrderCellItem;
                    return item.Lac;
                }
                return null;
            };

            this.colPrimaryCause.AspectGetter += delegate (object row)
            {
                if (row is OrderCellItem)
                {
                    OrderCellItem item = row as OrderCellItem;
                    return item.PrimaryCause;
                }
                return null;
            };

            this.colSpecificCuase.AspectGetter += delegate (object row)
            {
                if (row is OrderCellItem)
                {
                    OrderCellItem item = row as OrderCellItem;
                    return item.SpecifictCause;
                }
                return null;
            };

            this.colSuggest.AspectGetter += delegate (object row)
            {
                if (row is OrderCellItem)
                {
                    OrderCellItem item = row as OrderCellItem;
                    return item.Suggest;
                }
                return null;
            };
        }

        void lv_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            TreeListView listView = sender as TreeListView;
            OlvListViewHitTestInfo info = listView.OlvHitTest(e.X, e.Y);
            GridOrder order = null;
            List<GridOrder> orderList = new List<GridOrder>();
            if (info.RowObject is GridOrder)
            {
                order = info.RowObject as GridOrder;
            }
            else if (info.RowObject is OrderGridItem)
            {
                OrderGridItem g = info.RowObject as OrderGridItem;
                order = g.Order;
            }
            else if (info.RowObject is OrderCellItem)
            {
                OrderCellItem c = info.RowObject as OrderCellItem;
                order = c.Grid.Order;
            }
            else
            {
                return;
            }
            orderList.Add(order);
            makeSureLayerVisible();

            double minLng = double.MaxValue;
            double maxLng = double.MinValue;
            double minLat = double.MaxValue;
            double maxLat = double.MinValue;
            foreach (OrderGridItem g in order.Grids)
            {
                minLng = Math.Min(minLng, g.CenterLng);
                maxLng = Math.Max(maxLng, g.CenterLng);
                minLat = Math.Min(minLat, g.CenterLat);
                maxLat = Math.Max(maxLat, g.CenterLat);
            }

            layer.Orders = orderList;
            MasterCom.MTGis.DbRect rect = new MTGis.DbRect(minLng - 0.002, minLat - 0.002, maxLng + 0.002, maxLat + 0.002);
            MainModel.MainForm.GetMapForm().GoToView(rect);
        }

        GridOrderLayer layer;
        private void makeSureLayerVisible()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            layer = mf.GetLayerBase(typeof(GridOrderLayer)) as GridOrderLayer;
        }

        private List<GridOrder> orders = null;
        public void FillData(List<GridOrder> orderSet)
        {
            tabCtrl.TabPages.Clear();
            foreach (GridOrderToken token in OrderTokenMng.Instance.TokenSet)
            {
                TabPage page = new TabPage(token.Name);
                tabCtrl.TabPages.Add(page);
                createView(page, token, orderSet);
            }
            makeSureLayerVisible();
            layer.Orders = orderSet;
            this.orders = orderSet;
            MainModel.MainForm.SetConditionTime(DateTime.Now.AddMonths(-6), DateTime.Now);
        }

        private void createView(TabPage page, GridOrderToken token, IEnumerable<GridOrder> orders)
        {
            TreeListView view = createView();
            view.CanExpandGetter += lv.CanExpandGetter;
            view.ChildrenGetter += lv.ChildrenGetter;
            view.MouseDoubleClick += this.lv_MouseDoubleClick;
            view.ContextMenuStrip = this.ctxMenu;
            page.Controls.Add(view);
            view.Dock = DockStyle.Fill;
            foreach (OLVColumn oldCol in lv.Columns)
            {
                OLVColumn col = new OLVColumn();
                col.Text = oldCol.Text;
                view.AllColumns.Add(col);
                view.Columns.AddRange(new ColumnHeader[] { col });
                col.AspectGetter = oldCol.AspectGetter;
            }

            List<GridOrder> orderSet = new List<GridOrder>();
            foreach (GridOrder go in orders)
            {
                if (go.SetTokenID == token.ID)
                {
                    orderSet.Add(go);
                }
            }
            if (orderSet.Count == 0)
            {
                view.EmptyListMsg = "无数据！";
                return;
            }
            view.SetObjects(orderSet);

            List<GridOrderKPICfg> cfgSet = OrderKPICfgMng.Instance.GetKPICfgSetByTypeID(token.ID);
            foreach (GridOrderKPICfg cfgItem in cfgSet)
            {
                OLVColumn colx = new OLVColumn();
                colx.Text = cfgItem.Name;
                colx.Tag = cfgItem.Name;
                view.AllColumns.Add(colx);
                view.Columns.AddRange(new ColumnHeader[] { colx });
                setColumnAspectGetter(colx);
            }
            view.RebuildColumns();
        }

        private void setColumnAspectGetter(OLVColumn colx)
        {
            colx.AspectGetter = delegate (object row)
            {
                if (row is OrderGridItem)
                {
                    OrderGridItem g = row as OrderGridItem;
                    double retVal;
                    if (!g.KPIDic.TryGetValue(colx.Tag.ToString(), out retVal))
                    {
                        return null;
                    }
                    return retVal;
                }
                else if (row is OrderCellItem)
                {
                    OrderCellItem g = row as OrderCellItem;
                    double retVal;
                    if (!g.KPIDic.TryGetValue(colx.Tag.ToString(), out retVal))
                    {
                        return null;
                    }
                    return retVal;
                }
                return null;
            };
        }

        private TreeListView createView()
        {
            TreeListView view = new TreeListView();
            view.Cursor = System.Windows.Forms.Cursors.Default;
            view.Dock = System.Windows.Forms.DockStyle.Fill;
            view.FullRowSelect = true;
            view.GridLines = true;
            view.HeaderWordWrap = true;
            view.IsNeedShowOverlay = false;
            view.Location = new System.Drawing.Point(0, 0);
            view.OwnerDraw = true;
            view.ShowGroups = false;
            view.ShowItemToolTips = true;
            view.UseCompatibleStateImageBehavior = false;
            view.View = System.Windows.Forms.View.Details;
            view.VirtualMode = true;
            return view;
        }

        private void miShowAll_Click(object sender, EventArgs e)
        {
            makeSureLayerVisible();
            double minLng = double.MaxValue;
            double maxLng = double.MinValue;
            double minLat = double.MaxValue;
            double maxLat = double.MinValue;
            TreeListView tv = getCurView();
            List<GridOrder> list = new List<GridOrder>();
            foreach (GridOrder order in tv.Roots)
            {
                list.Add(order);
                foreach (OrderGridItem g in order.Grids)
                {
                    minLng = Math.Min(minLng, g.CenterLng);
                    maxLng = Math.Max(maxLng, g.CenterLng);
                    minLat = Math.Min(minLat, g.CenterLat);
                    maxLat = Math.Max(maxLat, g.CenterLat);
                }
            }

            layer.Orders = this.orders;
            if (list.Count > 0)
            {
                MasterCom.MTGis.DbRect rect = new MTGis.DbRect(minLng - 0.002, minLat - 0.002, maxLng + 0.002, maxLat + 0.002);
                MainModel.MainForm.GetMapForm().GoToView(rect);
            }
        }

        private TreeListView getCurView()
        {
            TreeListView view = tabCtrl.SelectedTab.Controls[0] as TreeListView;
            return view;
        }

        private void miExportXls_Click(object sender, EventArgs e)
        {
            TreeListView listView = getCurView();
            listView.ExpandAll();
            ExcelNPOIManager.ExportToExcel(listView);
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            TreeListView listView = getCurView();
            listView.ExpandAll();
        }

        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            TreeListView listView = getCurView();
            listView.CollapseAll();
        }

        private void miReplayFile_Click(object sender, EventArgs e)
        {
            TreeListView listView = getCurView();
            OrderCellItem cellItem = listView.SelectedObject as OrderCellItem;
            if (cellItem==null)
            {
                return;
            }

            QueryCondition cond = new QueryCondition();
            cond.FileName = cellItem.FileID.ToString();
            cond.FileNameOrNum = 1;
            cond.NameFilterType = FileFilterType.ByMark_ID;
            DateTime bTime;
            DateTime eTime;
            MainModel.MainForm.GetConditionTime(out bTime, out eTime);
            TimePeriod period = new TimePeriod(bTime, eTime);
            cond.Periods.Add(period);
            cond.DistrictIDs.Add(cellItem.CityID);
            MasterCom.RAMS.Net.DIYQueryFileInfo queryCheckFile = new MasterCom.RAMS.Net.DIYQueryFileInfo(MainModel);
            queryCheckFile.IsShowFileInfoForm = false;
            queryCheckFile.SetQueryCondition(cond);
            queryCheckFile.Query();

            if (MainModel.FileInfos.Count > 0)
            {
                cond.FileInfos.AddRange(MainModel.FileInfos);
            }
            else
            {
                MessageBox.Show("未找到文件！");
                return;
            }

            MasterCom.RAMS.Net.DIYReplayFileQuery query = new MasterCom.RAMS.Net.DIYReplayFileQuery(MainModel);
            query.SetQueryCondition(cond);
            query.Query(); 
        }

        private void ctxMenu_Opening(object sender, CancelEventArgs e)
        {
            TreeListView listView = getCurView();
            miReplayFile.Enabled = listView.SelectedObject is OrderCellItem;
        }

    }
}
