﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.Globalization;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 性能数据钻取
    /// </summary>
    class TestDetailInfQuery : DIYSQLBase
    {
        private readonly bool IsTDIn = false;
       
        
        private DateTime timeStart;
        public DateTime TimeStart
        {
            get
            {
                return timeStart;
            }
            set
            {
                timeStart = new DateTime(value.Year, value.Month, value.Day);
            }
        }
        public DateTime TimeEnd { get; set; }
        public TestDetailInfQuery(MainModel mainModel, bool IsTDIn)
            : base(mainModel)
        {
            this.IsTDIn = IsTDIn;
            timeStart = DateTime.MaxValue;
            TimeEnd = DateTime.MaxValue;
        }

        protected override string getSqlTextString()
        {
            //this.SetQueryCondition(
            if (timeStart == DateTime.MaxValue && TimeEnd == DateTime.MaxValue)
            {
                TimePeriod timePeriod = condition.Periods[0];
                timeStart = timePeriod.BeginTime;
                TimeEnd = timePeriod.EndTime;
            }
            string statement = "";
            if (IsTDIn)
            {
                statement = "select strfilename as '文件名',tmdat as '时间',ilac as 'LAC',ici as 'CI'," +
                    "strtype as '问题类型' from tb_para_utrancell_event_info";
            }
            else
            {
                statement = "select strfilename as '文件名',tmdat as '时间',ilac as 'LAC',ici as 'CI'," +
                    "strtype as '问题类型' from tb_para_cell_event_info";
            }
            statement += "\r\nwhere tmdat >= '" + timeStart.ToString("yyyy-MM-dd 00:00:00.000") + "' and tmdat < '" +
                TimeEnd.AddDays(1).ToString("yyyy-MM-dd 00:00:00.000") + "'";
            return statement;
        }

        
        public List<TestDetailInf> DetailInf { get; set; } = new List<TestDetailInf>();

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[5];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_String;
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        private void fillData(Package package)
        {
            try
            {
                TestDetailInf detInf = new TestDetailInf();
                detInf.FileName = package.Content.GetParamString();
                detInf.Tmdat = package.Content.GetParamString();
                detInf.LAC = package.Content.GetParamInt();
                detInf.CI = package.Content.GetParamInt();
                detInf.ProbleName = package.Content.GetParamString();
                DetailInf.Add(detInf);
            }
            catch
            {
                //continue
            }
        }

        public override string Name
        {
            get { return "PerDetailInfQuery"; }
        }
    }

    public class TestDetailInf
    {
        public string FileName { get; set; }
        public string Tmdat { get; set; }
        public int LAC { get; set; }
        public int CI { get; set; }
        public string ProbleName { get; set; }
        public TestDetailInf()
        {
             FileName = "";
             Tmdat = "";
             LAC = 0;
             CI = 0;
             ProbleName = "";
        }
    }
}
