﻿namespace MasterCom.RAMS.Stat
{
    partial class WorkCommonChartForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(WorkCommonChartForm));
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.vbarBtn = new System.Windows.Forms.ToolStripButton();
            this.lineBtn = new System.Windows.Forms.ToolStripButton();
            this.pieBtn = new System.Windows.Forms.ToolStripButton();
            this.tChartWork = new Steema.TeeChart.TChart();
            this.pie = new Steema.TeeChart.Styles.Pie();
            this.clbTitle = new System.Windows.Forms.CheckedListBox();
            this.label1 = new System.Windows.Forms.Label();
            this.lbTitle = new System.Windows.Forms.ListBox();
            this.lbTips = new System.Windows.Forms.Label();
            this.toolStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // toolStrip1
            // 
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.vbarBtn,
            this.lineBtn,
            this.pieBtn});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.RenderMode = System.Windows.Forms.ToolStripRenderMode.System;
            this.toolStrip1.Size = new System.Drawing.Size(873, 25);
            this.toolStrip1.TabIndex = 0;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // vbarBtn
            // 
            this.vbarBtn.Checked = true;
            this.vbarBtn.CheckState = System.Windows.Forms.CheckState.Checked;
            this.vbarBtn.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.vbarBtn.Image = ((System.Drawing.Image)(resources.GetObject("vbarBtn.Image")));
            this.vbarBtn.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.vbarBtn.Name = "vbarBtn";
            this.vbarBtn.Size = new System.Drawing.Size(23, 22);
            this.vbarBtn.Text = "柱状图";
            this.vbarBtn.Click += new System.EventHandler(this.vbarBtn_Click);
            // 
            // lineBtn
            // 
            this.lineBtn.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.lineBtn.Image = ((System.Drawing.Image)(resources.GetObject("lineBtn.Image")));
            this.lineBtn.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.lineBtn.Name = "lineBtn";
            this.lineBtn.Size = new System.Drawing.Size(23, 22);
            this.lineBtn.Text = "折线图";
            this.lineBtn.Click += new System.EventHandler(this.lineBtn_Click);
            // 
            // pieBtn
            // 
            this.pieBtn.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.pieBtn.Image = ((System.Drawing.Image)(resources.GetObject("pieBtn.Image")));
            this.pieBtn.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.pieBtn.Name = "pieBtn";
            this.pieBtn.Size = new System.Drawing.Size(23, 22);
            this.pieBtn.Text = "饼图";
            this.pieBtn.Click += new System.EventHandler(this.pieBtn_Click);
            // 
            // tChartWork
            // 
            this.tChartWork.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            // 
            // 
            // 
            this.tChartWork.Aspect.Elevation = 315;
            this.tChartWork.Aspect.ElevationFloat = 315;
            this.tChartWork.Aspect.Orthogonal = false;
            this.tChartWork.Aspect.Perspective = 0;
            this.tChartWork.Aspect.Rotation = 360;
            this.tChartWork.Aspect.RotationFloat = 360;
            this.tChartWork.Aspect.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
            this.tChartWork.Aspect.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Axes.Bottom.Automatic = true;
            // 
            // 
            // 
            this.tChartWork.Axes.Bottom.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartWork.Axes.Bottom.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Axes.Bottom.Labels.Font.Shadow.Visible = false;
            this.tChartWork.Axes.Bottom.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartWork.Axes.Bottom.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Axes.Bottom.Title.Font.Shadow.Visible = false;
            this.tChartWork.Axes.Bottom.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartWork.Axes.Bottom.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartWork.Axes.Depth.Automatic = true;
            // 
            // 
            // 
            this.tChartWork.Axes.Depth.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartWork.Axes.Depth.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Axes.Depth.Labels.Font.Shadow.Visible = false;
            this.tChartWork.Axes.Depth.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartWork.Axes.Depth.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Axes.Depth.Title.Font.Shadow.Visible = false;
            this.tChartWork.Axes.Depth.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartWork.Axes.Depth.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartWork.Axes.DepthTop.Automatic = true;
            // 
            // 
            // 
            this.tChartWork.Axes.DepthTop.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartWork.Axes.DepthTop.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Axes.DepthTop.Labels.Font.Shadow.Visible = false;
            this.tChartWork.Axes.DepthTop.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartWork.Axes.DepthTop.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Axes.DepthTop.Title.Font.Shadow.Visible = false;
            this.tChartWork.Axes.DepthTop.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartWork.Axes.DepthTop.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartWork.Axes.Left.Automatic = true;
            // 
            // 
            // 
            this.tChartWork.Axes.Left.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartWork.Axes.Left.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Axes.Left.Labels.Font.Shadow.Visible = false;
            this.tChartWork.Axes.Left.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartWork.Axes.Left.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Axes.Left.Title.Font.Shadow.Visible = false;
            this.tChartWork.Axes.Left.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartWork.Axes.Left.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartWork.Axes.Right.Automatic = true;
            // 
            // 
            // 
            this.tChartWork.Axes.Right.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartWork.Axes.Right.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Axes.Right.Labels.Font.Shadow.Visible = false;
            this.tChartWork.Axes.Right.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartWork.Axes.Right.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Axes.Right.Title.Font.Shadow.Visible = false;
            this.tChartWork.Axes.Right.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartWork.Axes.Right.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartWork.Axes.Top.Automatic = true;
            // 
            // 
            // 
            this.tChartWork.Axes.Top.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartWork.Axes.Top.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Axes.Top.Labels.Font.Shadow.Visible = false;
            this.tChartWork.Axes.Top.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartWork.Axes.Top.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Axes.Top.Title.Font.Shadow.Visible = false;
            this.tChartWork.Axes.Top.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartWork.Axes.Top.Title.Shadow.Visible = false;
            this.tChartWork.Cursor = System.Windows.Forms.Cursors.Default;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Footer.Font.Shadow.Visible = false;
            this.tChartWork.Footer.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartWork.Footer.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Header.Font.Shadow.Visible = false;
            this.tChartWork.Header.Font.Unit = System.Drawing.GraphicsUnit.World;
            this.tChartWork.Header.Lines = new string[] {
        "MasterCom"};
            // 
            // 
            // 
            this.tChartWork.Header.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Legend.Font.Shadow.Visible = false;
            this.tChartWork.Legend.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Legend.Title.Font.Bold = true;
            // 
            // 
            // 
            this.tChartWork.Legend.Title.Font.Shadow.Visible = false;
            this.tChartWork.Legend.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartWork.Legend.Title.Pen.Visible = false;
            // 
            // 
            // 
            this.tChartWork.Legend.Title.Shadow.Visible = false;
            this.tChartWork.Location = new System.Drawing.Point(0, 28);
            this.tChartWork.Name = "tChartWork";
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Panel.Shadow.Visible = false;
            this.tChartWork.Series.Add(this.pie);
            this.tChartWork.Size = new System.Drawing.Size(692, 397);
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.SubFooter.Font.Shadow.Visible = false;
            this.tChartWork.SubFooter.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartWork.SubFooter.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.SubHeader.Font.Shadow.Visible = false;
            this.tChartWork.SubHeader.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartWork.SubHeader.Shadow.Visible = false;
            this.tChartWork.TabIndex = 1;
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartWork.Walls.Back.AutoHide = false;
            // 
            // 
            // 
            this.tChartWork.Walls.Back.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartWork.Walls.Bottom.AutoHide = false;
            // 
            // 
            // 
            this.tChartWork.Walls.Bottom.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartWork.Walls.Left.AutoHide = false;
            // 
            // 
            // 
            this.tChartWork.Walls.Left.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartWork.Walls.Right.AutoHide = false;
            // 
            // 
            // 
            this.tChartWork.Walls.Right.Shadow.Visible = false;
            // 
            // pie
            // 
            // 
            // 
            // 
            this.pie.Brush.Color = System.Drawing.Color.Green;
            this.pie.Circled = true;
            this.pie.LabelMember = "Labels";
            this.pie.Labels = ((Steema.TeeChart.Styles.StringList)(resources.GetObject("pie.Labels")));
            // 
            // 
            // 
            // 
            // 
            // 
            this.pie.Marks.Callout.ArrowHead = Steema.TeeChart.Styles.ArrowHeadStyles.None;
            this.pie.Marks.Callout.ArrowHeadSize = 8;
            // 
            // 
            // 
            this.pie.Marks.Callout.Brush.Color = System.Drawing.Color.Black;
            this.pie.Marks.Callout.Distance = 0;
            this.pie.Marks.Callout.Draw3D = false;
            this.pie.Marks.Callout.Length = 8;
            this.pie.Marks.Callout.Style = Steema.TeeChart.Styles.PointerStyles.Rectangle;
            // 
            // 
            // 
            // 
            // 
            // 
            this.pie.Marks.Font.Shadow.Visible = false;
            this.pie.Marks.Font.Unit = System.Drawing.GraphicsUnit.World;
            this.pie.Marks.Style = Steema.TeeChart.Styles.MarksStyles.Percent;
            // 
            // 
            // 
            this.pie.Shadow.Height = 20;
            this.pie.Shadow.Width = 20;
            this.pie.Title = "pie";
            this.pie.Visible = false;
            // 
            // 
            // 
            this.pie.XValues.DataMember = "Angle";
            this.pie.XValues.Order = Steema.TeeChart.Styles.ValueListOrder.Ascending;
            // 
            // 
            // 
            this.pie.YValues.DataMember = "Pie";
            // 
            // clbTitle
            // 
            this.clbTitle.CheckOnClick = true;
            this.clbTitle.Dock = System.Windows.Forms.DockStyle.Right;
            this.clbTitle.FormattingEnabled = true;
            this.clbTitle.Location = new System.Drawing.Point(698, 25);
            this.clbTitle.Name = "clbTitle";
            this.clbTitle.Size = new System.Drawing.Size(175, 388);
            this.clbTitle.TabIndex = 2;
            this.clbTitle.SelectedIndexChanged += new System.EventHandler(this.clbTitle_SelectedIndexChanged);
            this.clbTitle.ItemCheck += new System.Windows.Forms.ItemCheckEventHandler(this.clbTitle_ItemCheck);
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(696, 9);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(137, 12);
            this.label1.TabIndex = 3;
            this.label1.Text = "请选择要查看的属性列：";
            // 
            // lbTitle
            // 
            this.lbTitle.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbTitle.FormattingEnabled = true;
            this.lbTitle.ItemHeight = 12;
            this.lbTitle.Location = new System.Drawing.Point(698, 25);
            this.lbTitle.Name = "lbTitle";
            this.lbTitle.Size = new System.Drawing.Size(175, 400);
            this.lbTitle.TabIndex = 4;
            this.lbTitle.Visible = false;
            this.lbTitle.Click += new System.EventHandler(this.lbTitle_Click);
            // 
            // lbTips
            // 
            this.lbTips.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.lbTips.AutoSize = true;
            this.lbTips.Location = new System.Drawing.Point(187, 103);
            this.lbTips.Name = "lbTips";
            this.lbTips.Size = new System.Drawing.Size(0, 12);
            this.lbTips.TabIndex = 5;
            // 
            // WorkCommonChartForm
            // 
            this.Appearance.BackColor = System.Drawing.SystemColors.Control;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("WorkCommonChartForm.Appearance.Image")));
            this.Appearance.Options.UseBackColor = true;
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(873, 425);
            this.Controls.Add(this.lbTips);
            this.Controls.Add(this.lbTitle);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.clbTitle);
            this.Controls.Add(this.tChartWork);
            this.Controls.Add(this.toolStrip1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.SizableToolWindow;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "WorkCommonChartForm";
            this.ShowInTaskbar = false;
            this.Text = "图表";
            this.TopMost = true;
            this.VisibleChanged += new System.EventHandler(this.WorkCommonChartForm_VisibleChanged);
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.WorkCommonChartForm_FormClosing);
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ToolStrip toolStrip1;
        private System.Windows.Forms.ToolStripButton vbarBtn;
        private System.Windows.Forms.ToolStripButton lineBtn;
        private System.Windows.Forms.ToolStripButton pieBtn;
        private Steema.TeeChart.TChart tChartWork;
        private Steema.TeeChart.Styles.Pie pie;
        private System.Windows.Forms.CheckedListBox clbTitle;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ListBox lbTitle;
        private System.Windows.Forms.Label lbTips;
    }
}