﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.MTGis
{
    public partial class CommonShpLayerSetProperties : MTLayerPropUserControl
    {
        public event EventHandler LayerPropertyChanged;
        public VecLayerItem m_layer { get; set; }
        private SfLayerInfo layerInfo;
        AxMapWinGIS.AxMap mapControl = null;
        private Dictionary<string, LinePatternItem> patternDic = new Dictionary<string, LinePatternItem>();
        public CommonShpLayerSetProperties()
        {
            InitializeComponent();
            //===
            List<LinePatternItem> linePatternList = LinePatternItem.InitCustomLineType();
            cbxLineType.Items.Clear();
            for (int i = 0; i < linePatternList.Count; i++)
            {
                LinePatternItem pattern = linePatternList[i];
                patternDic[pattern.Name] = pattern;
                cbxLineType.Items.Add(pattern);
            }
        }
        internal void SetMapControl(AxMapWinGIS.AxMap axMap)
        {
            this.mapControl = axMap;
        }
        public override void Setup(Object obj)
        {
            if (obj == null)
            {
                return;
            }
            Text = "基础设置";
            layerInfo = (SfLayerInfo)obj;
            m_layer = layerInfo.vecItem;
            updateControls();
        }

        private void updateControls()
        {
            unboundEvent();
            if (m_layer != null && layerInfo != null)
            {
                this.PointMarkHook();
                cbxBgFill.Checked = m_layer.style_bg_fill == 1;
                cbxBgColorCol.Enabled = pnBgColor.Enabled = tbBg.Enabled = cbxBgFill.Checked;
                cbxIsRoad.Checked = m_layer.is_road_layer == 1;
                cbxRange.Checked = m_layer.visible_range_enable == 1;
                bool check = cbxRange.Checked;
                m_layer.visible_range_enable = check ? 1 : 0;
                txtRangMax.Enabled = check;
                txtRangMin.Enabled = check;
                btnScaleCurMax.Enabled = check;
                btnScaleCurMin.Enabled = check;

                cbxVisible.Checked = m_layer.visible == 1;

                txtRangMax.Text = m_layer.visible_range_max.ToString();
                txtRangMin.Text = m_layer.visible_range_min.ToString();
                pnBgColor.BackColor = m_layer.style_bg_color;
                pnLineColor.BackColor = m_layer.style_line_color;
                nudLineWidth.Value = (decimal)m_layer.style_line_width;
                //=====line type start
                setLineType();
                //====line type end
                tbBg.Value = (int)(m_layer.style_bg_opaque * 255);
                cbxShowLabel.Checked = m_layer.label_show == 1;
                cbxLabelField.Visible = cbxShowLabel.Checked;
                numLabelFontSize.Enabled = cbxShowLabel.Checked;
                lbLabelFontColor.Enabled = cbxShowLabel.Checked;
                List<string> nameColumnNames = new List<string>();
                int numFields = layerInfo.sf.NumFields;
                for (int x = 0; x < numFields; x++)
                {
                    MapWinGIS.Field field = layerInfo.sf.get_Field(x);
                    nameColumnNames.Add(field.Name);
                }
                setColorAndField(nameColumnNames, numFields);
                numLabelFontSize.Value = m_layer.label_font_size;
                lbLabelFontColor.BackColor = m_layer.label_font_color;
            }
            boundEvent();
        }

        private void setColorAndField(List<string> nameColumnNames, int numFields)
        {
            cbxLabelField.Items.Clear();
            cbxBgColorCol.Items.Clear();
            cbxBgColorCol.Items.Add("[不使用]");
            for (int x = 0; x < numFields; x++)
            {
                cbxLabelField.Items.Add(nameColumnNames[x]);
                cbxBgColorCol.Items.Add(nameColumnNames[x]);
            }
            if (m_layer.label_field != null && m_layer.label_field != "")
            {
                cbxLabelField.SelectedItem = m_layer.label_field;
            }
            if (cbxLabelField.SelectedItem == null && cbxLabelField.Items.Count > 0)
            {
                cbxLabelField.SelectedIndex = 0;
            }
            string selField = cbxLabelField.SelectedItem as String;
            if (selField != null)
            {
                m_layer.label_field = selField;
            }
            if (m_layer.style_bg_color_column != null && m_layer.style_bg_color_column != "")
            {
                cbxBgColorCol.SelectedItem = m_layer.style_bg_color_column;
            }
            if (cbxBgColorCol.SelectedItem == null && cbxBgColorCol.Items.Count > 0)
            {
                cbxBgColorCol.SelectedIndex = 0;
            }
        }

        private void setLineType()
        {
            if (m_layer.style_line_patten != null)
            {
                LinePatternItem pattern = null;
                if (patternDic.TryGetValue(m_layer.style_line_patten, out pattern))
                {
                    cbxLineType.SelectedItem = pattern;
                }
                else
                {
                    cbxLineType.SelectedIndex = 0;
                }
            }
            else
            {
                cbxLineType.SelectedIndex = 0;
            }
        }

        private void unboundEvent()
        {
            cbxVisible.CheckedChanged -= cbxVisible_CheckedChanged;
            cbxIsRoad.CheckedChanged -= cbxIsRoad_CheckedChanged;
            cbxRange.CheckedChanged -= cbxRange_CheckedChanged;
            cbxBgFill.CheckedChanged -= cbxBgFill_CheckedChanged;
            nudLineWidth.ValueChanged -= nudLineWidth_ValueChanged;
            cbxShowLabel.CheckedChanged -= cbxShowLabel_CheckedChanged;
            cbxLabelField.SelectedIndexChanged -= cbxLabelField_SelectedIndexChanged;
            numLabelFontSize.ValueChanged -= numLabelFontSize_ValueChanged;
            txtRangMax.TextChanged -= txtRangMax_TextChanged;
            txtRangMin.TextChanged -= txtRangMin_TextChanged;
            cbxLineType.SelectedIndexChanged -= cbxLineType_SelectedIndexChanged;
            cbxBgColorCol.SelectedIndexChanged -= cbxBgColorCol_SelectedIndexChanged;
        }

        private void boundEvent()
        {
            cbxVisible.CheckedChanged += cbxVisible_CheckedChanged;
            cbxIsRoad.CheckedChanged += cbxIsRoad_CheckedChanged;
            cbxRange.CheckedChanged += cbxRange_CheckedChanged;
            cbxBgFill.CheckedChanged += cbxBgFill_CheckedChanged;
            nudLineWidth.ValueChanged += nudLineWidth_ValueChanged;
            cbxShowLabel.CheckedChanged += cbxShowLabel_CheckedChanged;
            cbxLabelField.SelectedIndexChanged += cbxLabelField_SelectedIndexChanged;
            numLabelFontSize.ValueChanged += numLabelFontSize_ValueChanged;
            txtRangMax.TextChanged += txtRangMax_TextChanged;
            txtRangMin.TextChanged += txtRangMin_TextChanged;
            cbxLineType.SelectedIndexChanged += cbxLineType_SelectedIndexChanged;
            cbxBgColorCol.SelectedIndexChanged += cbxBgColorCol_SelectedIndexChanged;
        }

        private void cbxVisible_CheckedChanged(object sender, EventArgs e)
        {
            m_layer.visible = cbxVisible.Checked ? 1 : 0;
            VecEventArgs arg = new VecEventArgs();
            arg.sfInfo = layerInfo;
            LayerPropertyChanged(this, arg);
        }

        private void cbxIsRoad_CheckedChanged(object sender, EventArgs e)
        {
            m_layer.is_road_layer = cbxIsRoad.Checked ? 1 : 0;
            VecEventArgs arg = new VecEventArgs();
            arg.sfInfo = layerInfo;
            LayerPropertyChanged(this, arg);
        }

        private void cbxRange_CheckedChanged(object sender, EventArgs e)
        {
            bool rangeEnabled = cbxRange.Checked;
            m_layer.visible_range_enable = rangeEnabled ? 1 : 0;
            txtRangMax.Enabled = rangeEnabled;
            txtRangMin.Enabled = rangeEnabled;
            btnScaleCurMax.Enabled = rangeEnabled;
            btnScaleCurMin.Enabled = rangeEnabled;
            VecEventArgs arg = new VecEventArgs();
            arg.sfInfo = layerInfo;
            LayerPropertyChanged(this, arg);
        }

        private void btnScaleCurMax_Click(object sender, EventArgs e)
        {
            txtRangMax.Text = "" + (int)mapControl.CurrentScale; 
        }

        private void btnScaleCurMin_Click(object sender, EventArgs e)
        {
            txtRangMin.Text = "" + (int)mapControl.CurrentScale;
        }

        private void cbxBgFill_CheckedChanged(object sender, EventArgs e)
        {
            m_layer.style_bg_fill = cbxBgFill.Checked ? 1 : 0;
            cbxBgColorCol.Enabled = pnBgColor.Enabled = tbBg.Enabled = cbxBgFill.Checked;
            VecEventArgs arg = new VecEventArgs();
            arg.sfInfo = layerInfo;
            LayerPropertyChanged(this, arg);
        }

        private void pnBgColor_Click(object sender, EventArgs e)
        {
            selectColor(pnBgColor);
            m_layer.style_bg_color = pnBgColor.BackColor;
            VecEventArgs arg = new VecEventArgs();
            arg.sfInfo = layerInfo;
            LayerPropertyChanged(this, arg);
        }

        private void tbBg_Scroll(object sender, EventArgs e)
        {
            m_layer.style_bg_opaque = tbBg.Value / 255f;
            VecEventArgs arg = new VecEventArgs();
            arg.sfInfo = layerInfo;
            LayerPropertyChanged(this, arg);
        }

        private void nudLineWidth_ValueChanged(object sender, EventArgs e)
        {
            m_layer.style_line_width = (int)nudLineWidth.Value;
            VecEventArgs arg = new VecEventArgs();
            arg.sfInfo = layerInfo;
            LayerPropertyChanged(this, arg);
        }

        private void pnLineColor_Click(object sender, EventArgs e)
        {
            selectColor(pnLineColor);
            m_layer.style_line_color = pnLineColor.BackColor;
            VecEventArgs arg = new VecEventArgs();
            arg.sfInfo = layerInfo;
            LayerPropertyChanged(this, arg);
        }

        private void cbxShowLabel_CheckedChanged(object sender, EventArgs e)
        {
            m_layer.label_show = cbxShowLabel.Checked ? 1 : 0;
            cbxLabelField.Visible = cbxShowLabel.Checked;
            numLabelFontSize.Enabled = cbxShowLabel.Checked;
            lbLabelFontColor.Enabled = cbxShowLabel.Checked;
            VecEventArgs arg = new VecEventArgs();
            arg.sfInfo = layerInfo;
            LayerPropertyChanged(this, arg);
        }

        private void cbxLabelField_SelectedIndexChanged(object sender, EventArgs e)
        {
            string selField = cbxLabelField.SelectedItem as String;
            if (selField != null)
            {
                m_layer.label_field = selField;
                m_layer.label_font_color = lbLabelFontColor.BackColor;
                m_layer.label_font_size = (int)numLabelFontSize.Value;
                VecEventArgs arg = new VecEventArgs();
                arg.sfInfo = layerInfo;
                LayerPropertyChanged(this, arg);
            }
        }

        private void numLabelFontSize_ValueChanged(object sender, EventArgs e)
        {
            m_layer.label_font_size = (int)numLabelFontSize.Value;
            VecEventArgs arg = new VecEventArgs();
            arg.sfInfo = layerInfo;
            LayerPropertyChanged(this, arg);
        }

        private void lbLabelFontColor_Click(object sender, EventArgs e)
        {
            selectColor(lbLabelFontColor);
            m_layer.label_font_color = lbLabelFontColor.BackColor;
            VecEventArgs arg = new VecEventArgs();
            arg.sfInfo = layerInfo;
            LayerPropertyChanged(this, arg);
        }

        private void txtRangMax_TextChanged(object sender, EventArgs e)
        {
            int value;
            int.TryParse(txtRangMax.Text, out value);
            m_layer.visible_range_max = value;
            VecEventArgs arg = new VecEventArgs();
            arg.sfInfo = layerInfo;
            LayerPropertyChanged(this, arg);
        }

        private void txtRangMin_TextChanged(object sender, EventArgs e)
        {
            int value;
            int.TryParse(txtRangMin.Text, out value);
            m_layer.visible_range_min = value;
            VecEventArgs arg = new VecEventArgs();
            arg.sfInfo = layerInfo;
            LayerPropertyChanged(this, arg);
        }
        private void selectColor(Panel pnColor)
        {
            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                pnColor.BackColor = colorDialog.Color;
            }
        }

        private void cbxLineType_SelectedIndexChanged(object sender, EventArgs e)
        {
            LinePatternItem item = cbxLineType.SelectedItem as LinePatternItem;
            if (item != null)
            {
                m_layer.style_line_patten = item.Name;
                VecEventArgs arg = new VecEventArgs();
                arg.sfInfo = layerInfo;
                LayerPropertyChanged(this, arg);
            }
        }

        private void cbxBgColorCol_SelectedIndexChanged(object sender, EventArgs e)
        {
            string selField = cbxBgColorCol.SelectedItem as String;
            if (selField == null || selField == "[不使用]")
            {
                selField = "";
            }
            m_layer.style_bg_color_column = selField;
            VecEventArgs arg = new VecEventArgs();
            arg.sfInfo = layerInfo;
            LayerPropertyChanged(this, arg);
        }

        /// <summary>
        /// 点标签屏蔽一些选项
        /// </summary>
        private void PointMarkHook()
        {
            if (layerInfo.name.StartsWith("点_") && layerInfo.sf.ShapefileType == MapWinGIS.ShpfileType.SHP_POINT)
            {
                this.cbxLineType.Enabled = false;
                this.nudLineWidth.Enabled = false;
                this.pnLineColor.Enabled = false;
                this.cbxBgColorCol.Enabled = false;
            }
        }
        
    }
    public class VecEventArgs : EventArgs
    {
        public SfLayerInfo sfInfo { get; set; }
    }
}
