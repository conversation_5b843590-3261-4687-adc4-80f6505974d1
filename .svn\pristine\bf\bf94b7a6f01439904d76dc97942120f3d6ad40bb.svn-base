﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteAntennaForm : MinCloseForm
    {
        public LteAntennaForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            initAngleArgs();
            this.dataGridViewAngle.Columns[0].Frozen = true;
            this.dataGridViewAngle.Columns[1].Frozen = true;
        }     

        public List<List<NPOIRow>> nrDatasList { get; set; }
        public List<string> sheetNames { get; set; }
        private int iPointIndex = 0;
        int iDataNum = 0;
        private Dictionary<string, ZTLteAntenna.CellAngleData> dicUtranCellAngelData;

        /// <summary>
        /// 填充新方法，使用nrDatasList对数据进行填充，无需再次编写填充代码
        /// </summary>
        public void FillData(Dictionary<string, ZTLteAntenna.CellAngleData> dicUtranCellAngelData)
        {
            dataGridView.Columns.Clear();
            dataGridView.Rows.Clear();
            dataGridViewCell.Columns.Clear();
            dataGridViewCell.Rows.Clear();
            this.dicUtranCellAngelData = new Dictionary<string, ZTLteAntenna.CellAngleData>();
            foreach (string strCell in dicUtranCellAngelData.Keys)
            {
                this.dicUtranCellAngelData[strCell] = dicUtranCellAngelData[strCell];
            }
            iDataNum = dicUtranCellAngelData.Count;
            labNum.Text = iDataNum.ToString();
            int iPage = iDataNum % 200 > 0 ? iDataNum / 200 + 1 : iDataNum / 200;
            labPage.Text = iPage.ToString();

            for (int i = 0; i < nrDatasList.Count; i++)
            {
                int idx = 0;
                foreach (NPOIRow row in nrDatasList[i])
                {
                    if (idx == 0)
                    {
                        intDataViewColumn(row.cellValues, i);
                        idx++;
                    }
                    else 
                    {
                        if (i == 0)
                        {
                            initDataRow(dataGridView, row, row.cellValues[0].ToString());
                        }
                        else if (i == 2)
                        {
                            initDataRow(dataGridViewCell, row, row.cellValues[5].ToString());
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 按小区模糊查找，前200个小区
        /// </summary>
        private void FillData(string strCellName)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int rowCellAt = 0;
            foreach (NPOIRow rowData in nrDatasList[2])
            {
                if (nrDatasList[2][0].cellValues[0].ToString() == rowData.cellValues[0].ToString())
                    continue;

                if (strCellName != "" && rowData.cellValues[3].ToString().IndexOf(strCellName) < 0)
                    continue;

                if (rowCellAt >= 200)
                    break;
                initDataRow(dataGridViewCell, rowData, rowData.cellValues[2].ToString());
                rowCellAt++;
            }
            FillRegionAndAngleData();
        }

        /// <summary>
        /// 按页数查找
        /// </summary>
        private void FillData(int iPage)
        {
            dataGridViewCell.Rows.Clear();//小区级
            int iCount = -1;
            int rowCellAt = 0;
            foreach (NPOIRow rowData in nrDatasList[2])
            {
                if (nrDatasList[2][0].cellValues[0].ToString() == rowData.cellValues[0].ToString())
                    continue;

                iCount++;
                if (iCount / 200 != iPage)
                    continue;
                initDataRow(dataGridViewCell, rowData, rowData.cellValues[2].ToString());
                rowCellAt++;
            }
            FillRegionAndAngleData();
        }

        /// <summary>
        /// 区间级数据赋值
        /// </summary>
        private void FillRegionAndAngleData()
        {
            dataGridView.Rows.Clear();

            foreach (DataGridViewRow dgCell in dataGridViewCell.Rows)
            {
                if (dgCell == null || dgCell.Tag == null)
                    continue;

                foreach (NPOIRow rowData in nrDatasList[0])
                {
                    if (rowData.cellValues[0].ToString() == dgCell.Tag.ToString())
                    {
                        initDataRow(dataGridView, rowData, rowData.cellValues[0].ToString());
                    }
                }
            }
        }

        /// <summary>
        /// 初始化列头赋值
        /// </summary>
        private void intDataViewColumn(List<object> objList,int index)
        {
            int idx = 1;
            foreach (object obj in objList)
            {
                switch (index)
                {
                    case 0:
                        dataGridView.Columns.Add(idx++.ToString(), obj.ToString());
                        break;
                    case 2:
                        dataGridViewCell.Columns.Add(idx++.ToString(), obj.ToString());
                        break;
                    default:
                        break;
                }
            }
        }

        /// <summary>
        /// 初始化数据赋值
        /// </summary>
        private void initDataRow(DataGridView datatGridView, NPOIRow nop, string strCellName)
        {
            DataGridViewRow row = new DataGridViewRow();
            row.Tag = strCellName;//小区名称
            foreach (object obj in nop.cellValues)
            {
                DataGridViewTextBoxCell boxcell = new DataGridViewTextBoxCell();
                boxcell.Value = obj.ToString();
                row.Cells.Add(boxcell);
            }
            datatGridView.Rows.Add(row);
        }

        ZTLteAntenna.CellAngleData data;
        ///<summary>
        ///生成0~360度的角度列
        ///</summary>
        public void initAngleArgs()
        {
            for (int i = -179; i < 181; i++ )
            {
                DataGridViewTextBoxColumn angelCol = new DataGridViewTextBoxColumn();
                angelCol.HeaderText = i + "°";
                angelCol.Width = 63;
                dataGridViewAngle.Columns.Add(angelCol);
            }

            cbbxSeries1.Items.Add("RSRP");
            cbbxSeries1.Items.Add("平滑RSRP");
            cbbxSeries1.Items.Add("RSRP0");
            cbbxSeries1.Items.Add("RSRP1");
            cbbxSeries1.Items.Add("Pathloss");
            cbbxSeries1.Items.Add("RSSI");
            cbbxSeries1.Items.Add("RSRQ");
            cbbxSeries1.Items.Add("过覆盖指数");
            cbbxSeries1.Items.Add("通信距离");
            cbbxSeries1.Items.Add("SINR");
            cbbxSeries1.SelectedIndex = 0;

            cbbxSeries2.Items.Add("RSRP");
            cbbxSeries2.Items.Add("平滑RSRP");
            cbbxSeries2.Items.Add("RSRP0");
            cbbxSeries2.Items.Add("RSRP1");
            cbbxSeries2.Items.Add("Pathloss");
            cbbxSeries2.Items.Add("RSSI");
            cbbxSeries2.Items.Add("RSRQ");
            cbbxSeries2.Items.Add("过覆盖指数");
            cbbxSeries2.Items.Add("通信距离");
            cbbxSeries2.Items.Add("SINR");
            cbbxSeries2.SelectedIndex = 4;

            Series series = chartControl1.Series[0];
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical; //设置x轴有固定间距
            SeriesPoint pt;
            pt = new SeriesPoint(-150, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-90, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-60, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-30, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(-15, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(0, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(15, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(30, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(60, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(90, 50);
            series.Points.Add(pt);
            pt = new SeriesPoint(150, 50);
            series.Points.Add(pt);

            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;
        }
        /// <summary>
        /// "天线辐射波形重建"中显示表格信息
        /// </summary>
        public void setAngelTable(ZTLteAntenna.CellAngleData data)
        {
            dataGridViewAngle.Rows.Clear();
            int rowAt = 0;
            dataGridViewAngle.Rows.Add(1);
            int colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "RSRP";
            for (int i = 181; i < 360; i++) //181~359度（按正负算-179~-1）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.sampNumArray[i] == 0 ? 0 : data.ciItem.rsrpArray[i] / data.ciItem.sampNumArray[i]).ToString();
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.sampNumArray[i] == 0 ? 0 : data.ciItem.rsrpArray[i] / data.ciItem.sampNumArray[i]).ToString();
            }
            dataGridViewAngle.Rows.Add(1);

            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = "平滑RSRP";
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = Math.Round(data.ciItem.newRsrpArray[i], 2);
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = Math.Round(data.ciItem.newRsrpArray[i], 2);
            }

            addRowData(data, ref rowAt, "RSRP0", data.ciItem.rsrp0Array);
            addRowData(data, ref rowAt, "RSRP1", data.ciItem.rsrp1Array);
            addRowData(data, ref rowAt, "Pathloss", data.ciItem.pathlossArray);
            addRowData(data, ref rowAt, "RSSI", data.ciItem.rssiArray);
            addRowData(data, ref rowAt, "RSRQ", data.ciItem.rsrqArray);
            addRowData(data, ref rowAt, "过覆盖指数", data.ciItem.coverArray);
            addRowData(data, ref rowAt, "通信距离", data.ciItem.sampArray);
            addRowData(data, ref rowAt, "SINR", data.ciItem.sinrArray);
        }

        private void addRowData(ZTLteAntenna.CellAngleData data, ref int rowAt, string desc, double[] array)
        {
            dataGridViewAngle.Rows.Add(1);
            int colAt;
            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = desc;
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.sampNumArray[i] == 0 ? 0 : array[i] / data.ciItem.sampNumArray[i]).ToString();
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.sampNumArray[i] == 0 ? 0 : array[i] / data.ciItem.sampNumArray[i]).ToString();
            }
        }

        private void addRowData(ZTLteAntenna.CellAngleData data, ref int rowAt, string desc, int[] array)
        {
            dataGridViewAngle.Rows.Add(1);
            int colAt;
            rowAt++;
            colAt = 0;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = data.cellname;
            dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = desc;
            for (int i = 181; i < 360; i++)
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.sampNumArray[i] == 0 ? 0 : array[i] / data.ciItem.sampNumArray[i]).ToString();
            }
            for (int i = 0; i < 181; i++) //0~180度（按正负算0~180）
            {
                dataGridViewAngle.Rows[rowAt].Cells[colAt++].Value = (data.ciItem.sampNumArray[i] == 0 ? 0 : array[i] / data.ciItem.sampNumArray[i]).ToString();
            }
        }

        /// <summary>
        /// 调整Y轴，按绝对值大小描写刻度
        /// </summary>
        private void checkReverse()
        {
            if ((double)(((XYDiagram)chartControl1.Diagram).AxisY.Range.MaxValue) > 0)
            {
                ((XYDiagram)chartControl1.Diagram).AxisY.Reverse = false;
            }
            else
            {
                ((XYDiagram)chartControl1.Diagram).AxisY.Reverse = true;
            }

            if ((double)(((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Range.MaxValue) > 0)
            {
                ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Reverse = false;
            }
            else
            {
                ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0].Reverse = true;
            }
        }
        /// <summary>
        /// 按参数值1画图表
        /// </summary>
        /// <param name="seriesValue"></param>
        private void DrawTable1Series(int[] seriesValues)
        {
            int count = 0;
            SeriesPoint pt;
            if (chartControl1.Series.Count > 1)
            {
                chartControl1.Series.RemoveAt(1);
            }
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.2;
            sideBySideBarSeriesView.Color = Color.Orange;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 181; i < 360; i++)
            {
                string arg = (-179 + count).ToString();
                double value = Math.Round(data.ciItem.sampNumArray[i] == 0 ? 0 : seriesValues[i] * 1.0 / data.ciItem.sampNumArray[i], 2);
                pt = new SeriesPoint(arg, value);
                series.Points.Add(pt);
                count++;
            }
            for (int i = 1; i < 181; i++)
            {
                string arg = i.ToString();
                double value = Math.Round(data.ciItem.sampNumArray[i] == 0 ? 0 : seriesValues[i] * 1.0 / data.ciItem.sampNumArray[i], 2);
                pt = new SeriesPoint(arg, value);
                series.Points.Add(pt);
            }

            chartControl1.Series.Insert(1, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;

            ((XYDiagram)chartControl1.Diagram).AxisY.Range.Auto = true;
            chartControl1.Focus();
        }
        /// <summary>
        /// 按参数值1画图表
        /// </summary>
        /// <param name="seriesValues"></param>
        private void DrawTable1Series(double[] seriesValues)
        {
            if (chartControl1.Series.Count > 0)
            {
                chartControl1.Series.RemoveAt(1);
            }

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.2;
            sideBySideBarSeriesView.Color = Color.Orange;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            int count = 0;
            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(data.ciItem.sampNumArray[i] == 0 ? 0 : seriesValues[i] / data.ciItem.sampNumArray[i], 2)));
            }
            for (int i = 1; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(data.ciItem.sampNumArray[i] == 0 ? 0 : seriesValues[i] / data.ciItem.sampNumArray[i], 2)));
            }

            chartControl1.Series.Insert(1, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;


            ((XYDiagram)chartControl1.Diagram).AxisY.Range.Auto = true;
            chartControl1.Focus();
        }
        /// <summary>
        /// 按参数值1画图表
        /// </summary>
        private void DrawTable1SeriesNotCalc(double[] seriesValues)
        {
            if (chartControl1.Series.Count > 0)
            {
                chartControl1.Series.RemoveAt(1);
            }

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.2;
            sideBySideBarSeriesView.Color = Color.Orange;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            int count = 0;
            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(seriesValues[i], 2)));
            }
            for (int i = 1; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(seriesValues[i], 2)));
            }

            chartControl1.Series.Insert(1, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;


            ((XYDiagram)chartControl1.Diagram).AxisY.Range.Auto = true;
            chartControl1.Focus();
        }
        /// <summary>
        /// 按参数值2画图表
        /// </summary>
        /// <param name="seriesValues"></param>
        private void drawTable2Series(int[] seriesValues)
        {
            int count = 0;
            if (chartControl1.Series.Count > 2)
            {
                chartControl1.Series.RemoveAt(2);
            }

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            LineSeriesView lineSeriesView = new LineSeriesView();
            lineSeriesView.Color = System.Drawing.Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            ((LineSeriesView)series.View).AxisY = ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0];

            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(data.ciItem.sampNumArray[i] == 0 ? 0 : seriesValues[i] * 1.0 / data.ciItem.sampNumArray[i], 2)));
            }
            for (int i = 0; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(data.ciItem.sampNumArray[i] == 0 ? 0 : seriesValues[i] * 1.0 / data.ciItem.sampNumArray[i], 2)));
            }

            chartControl1.Series.Insert(2, series);
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;

            ((LineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl1.Focus();
        }
        /// <summary>
        /// 按参数值2画图表
        /// </summary>
        /// <param name="seriesValues"></param>
        private void drawTable2Series(double[] seriesValues)
        {
            int count = 0;

            if (chartControl1.Series.Count > 2)
            {
                chartControl1.Series.RemoveAt(2);
            }

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            LineSeriesView lineSeriesView = new LineSeriesView();
            lineSeriesView.Color = System.Drawing.Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            ((LineSeriesView)series.View).AxisY = ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0];

            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(data.ciItem.sampNumArray[i] == 0 ? 0 : seriesValues[i] / data.ciItem.sampNumArray[i], 2)));
            }
            for (int i = 0; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(data.ciItem.sampNumArray[i] == 0 ? 0 : seriesValues[i] / data.ciItem.sampNumArray[i], 2)));
            }

            chartControl1.Series.Insert(2, series);

            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;
            ((LineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl1.Focus();
        }
        /// <summary>
        /// 按参数值2画图表
        /// </summary>
        private void drawTable2SeriesNotCalc(double[] seriesValues)
        {
            int count = 0;

            if (chartControl1.Series.Count > 2)
            {
                chartControl1.Series.RemoveAt(2);
            }

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;
            LineSeriesView lineSeriesView = new LineSeriesView();
            lineSeriesView.Color = System.Drawing.Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            ((LineSeriesView)series.View).AxisY = ((XYDiagram)chartControl1.Diagram).SecondaryAxesY[0];

            for (int i = 181; i < 360; i++)
            {
                count++;
                series.Points.Add(new SeriesPoint((-179 + count).ToString(), Math.Round(seriesValues[i], 2)));
            }
            for (int i = 0; i < 181; i++)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(seriesValues[i], 2)));
            }

            chartControl1.Series.Insert(2, series);

            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MinValue = -179;
            ((XYDiagram)chartControl1.Diagram).AxisX.Range.MaxValue = 180;
            ((LineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl1.Focus();
        }

        /// <summary>
        /// 按平滑线绘雷达图
        /// </summary>
        private void drawAntRadarSeries(double[] seriesValues, double[] modelSeriesValues)
        {
            chartControl2.Series.Clear();

            #region 实际测试数据
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
            lineSeriesView.Color = Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;

            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 359; i >= 0; i--)
            {
                series.Points.Add(new SeriesPoint(i.ToString(), Math.Round(seriesValues[i], 2)));
            }
            chartControl2.Series.Insert(0, series);
            #endregion

            #region 权值模型数据

            Series series2 = new Series();
            series2.ShowInLegend = false;
            series2.PointOptions.PointView = PointView.Values;

            RadarLineSeriesView lineSeriesView2 = new RadarLineSeriesView();
            lineSeriesView2.Color = Color.Orange;
            lineSeriesView2.LineMarkerOptions.Size = 2;

            series2.View = lineSeriesView2;
            series2.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series2.Label.Visible = false;

            for (int i = 0; i < 360; i++)
            {
                double tmpValue = -120;
                if (i >= 270)
                    tmpValue = modelSeriesValues[i - 270];
                else if (i < 90)
                    tmpValue = modelSeriesValues[i + 90];

                series2.Points.Add(new SeriesPoint(i.ToString(), Math.Round(tmpValue, 2)));
            }
            chartControl2.Series.Insert(1, series2);
            #endregion

            int iMaxValue = -50;
            int iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(modelSeriesValues, ref iMaxValue, ref iMinValue);
            iMinValue = 50;//最小值不取模型值
            ZTAntFuncHelper.getMaxAndMinValue(seriesValues, ref iMaxValue, ref iMinValue);

            ((RadarDiagram)chartControl2.Diagram).AxisY.Range.MinValue = iMinValue;
            ((RadarDiagram)chartControl2.Diagram).AxisY.Range.MaxValue = iMaxValue;

            ((RadarDiagram)chartControl2.Diagram).AxisX.GridSpacing = 20;

            ((RadarDiagram)chartControl2.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
            ((RadarDiagram)chartControl2.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;

            chartControl2.Focus();
        }

        /// <summary>
        /// 按权值理想覆盖图
        /// </summary>
        private void DrawWeightTableSeries(double[] modelSeries)
        {
            chartControl5.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 180; i++)
            {
                series.Points.Add(new SeriesPoint((-90 + i).ToString(), Math.Round(modelSeries[i], 2)));
            }
            iPointIndex = 0;
            chartControl5.Series.Insert(0, series);

            ((XYDiagram)chartControl5.Diagram).AxisX.Range.MinValue = -90;
            ((XYDiagram)chartControl5.Diagram).AxisX.Range.MaxValue = 90;

            chartControl5.Focus();
        }

        /// <summary>
        /// 按距离等间线绘图
        /// </summary>
        private void drawVertSplineSeries(int[] seriesValues)
        {
            double[] tmpAarry = new double[200];
            chartControl3.Series.Clear();
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SplineSeriesView lineSeriesView = new SplineSeriesView();
            lineSeriesView.Color = Color.DarkCyan;
            lineSeriesView.LineMarkerOptions.Size = 2;
            series.View = lineSeriesView;

            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            for (int i = 0; i < 200; i++)
            {
                double tmpValue = Math.Round(data.ciItem.sampNumVertArray[i] == 0 ? -140 : seriesValues[i] * 1.0 / data.ciItem.sampNumVertArray[i], 2);
                series.Points.Add(new SeriesPoint(i.ToString(), tmpValue));
                tmpAarry[i] = tmpValue;
            }
            chartControl3.Series.Insert(0, series);

            int iMaxValue = -50;
            int iMinValue = 50;
            ZTAntFuncHelper.getMaxAndMinValue(tmpAarry, ref iMaxValue, ref iMinValue);
            ((XYDiagram)chartControl3.Diagram).AxisY.Range.MinValue = iMinValue;
            ((XYDiagram)chartControl3.Diagram).AxisY.Range.MaxValue = iMaxValue;
            ((SplineSeriesView)series.View).AxisY.Range.Auto = true;

            chartControl3.Focus();
        }

        private void cbbxSeries1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (data == null)
            {
                return;
            }

            Cursor.Current = Cursors.WaitCursor;
            if (this.cbbxSeries1.Text == this.cbbxSeries2.Text)
            {
                if (chartControl1.Series.Count == 1)
                {
                    return;
                }
                Series series = chartControl1.Series[1];
                series.Points.Clear();
                checkReverse();
                return; //选同一个参数，不作比较画图
            }
            switch (this.cbbxSeries1.Text)
            {
                case "RSRP":
                    {
                        DrawTable1Series(data.ciItem.rsrpArray);
                    }
                    break;
                case "平滑RSRP":
                    {
                        DrawTable1SeriesNotCalc(data.ciItem.newRsrpArray);
                    }
                    break;
                case "RSRP0":
                    {
                        DrawTable1Series(data.ciItem.rsrp0Array);
                    }
                    break;
                case "RSRP1":
                    {
                        DrawTable1Series(data.ciItem.rsrp1Array);
                    }
                    break;
                case "Pathloss":
                    {
                        DrawTable1Series(data.ciItem.pathlossArray);
                    }
                    break;
                case "RSSI":
                    {
                        DrawTable1Series(data.ciItem.rssiArray);
                    }
                    break;
                case "RSRQ":
                    {
                        DrawTable1Series(data.ciItem.rsrqArray);
                    }
                    break;
                case "过覆盖指数":
                    {
                        DrawTable1Series(data.ciItem.coverArray);
                    }
                    break;
                case "通信距离":
                    {
                        DrawTable1Series(data.ciItem.sampArray);
                    }
                    break;
                case "SINR":
                    {
                        DrawTable1Series(data.ciItem.sinrArray);
                    }
                    break;
                default:
                    break;
            }
            checkReverse();
            Cursor.Current = Cursors.Default;
        }

        private void cbbxSeries2_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (data == null)
            {
                return;
            }

            Cursor.Current = Cursors.WaitCursor;
            if (this.cbbxSeries1.Text == this.cbbxSeries2.Text)
            {
                if (chartControl1.Series.Count == 2)
                {
                    return;
                }
                Series series = chartControl1.Series[2];
                series.Points.Clear();
                checkReverse();
                return; //选同一个参数，不作比较画图
            }

            //图例2内容
            switch (this.cbbxSeries2.Text)
            {
                case "RSRP":
                    {
                        drawTable2Series(data.ciItem.rsrpArray);
                    }
                    break;
                case "平滑RSRP":
                    {
                        drawTable2SeriesNotCalc(data.ciItem.newRsrpArray);
                    }
                    break;
                case "RSRP0":
                    {
                        drawTable2Series(data.ciItem.rsrp0Array);
                    }
                    break;
                case "RSRP1":
                    {
                        drawTable2Series(data.ciItem.rsrp1Array);
                    }
                    break;
                case "Pathloss":
                    {
                        drawTable2Series(data.ciItem.pathlossArray);
                    }
                    break;
                case "RSSI":
                    {
                        drawTable2Series(data.ciItem.rssiArray);
                    }
                    break;
                case "RSRQ":
                    {
                        drawTable2Series(data.ciItem.rsrqArray);
                    }
                    break;
                case "过覆盖指数":
                    {
                        drawTable2Series(data.ciItem.coverArray);
                    }
                    break;
                case "通信距离":
                    {
                        drawTable2Series(data.ciItem.sampArray);
                    }
                    break;
                case "SINR":
                    {
                        drawTable2Series(data.ciItem.sinrArray);
                    }
                    break;
                default:
                    break;
            }
            checkReverse();
            Cursor.Current = Cursors.Default;
        }

        private void miShwoChart_Click(object sender, EventArgs e)
        {
            string strCellName = "";
            if (this.xtraTabControl1.SelectedTabPageIndex == 0)
            {
                strCellName = dataGridViewCell.SelectedRows[0].Tag as string;
            }
            else if (this.xtraTabControl1.SelectedTabPageIndex == 1)
            {
                strCellName = dataGridView.SelectedRows[0].Tag as string;
            }
            if (strCellName != null && strCellName != "" && dicUtranCellAngelData.ContainsKey(strCellName))
            {
                data = dicUtranCellAngelData[strCellName];
                if (data == null)
                    return;
                groupControl3.Text = string.Format("天线全向分析({0})", data.cellname);
                setAngelTable(data);

                if (chartControl1.Series.Count == 3)
                {
                    chartControl1.Series.RemoveAt(1);
                    chartControl1.Series.RemoveAt(1);
                }
                else if (chartControl1.Series.Count == 2)
                {
                    chartControl1.Series.RemoveAt(1);
                }

                this.xtraTabControl1.SelectedTabPageIndex = 5;
                cbbxSeries1_SelectedIndexChanged(null, null);
                cbbxSeries2_SelectedIndexChanged(null, null);

                drawAntRadarSeries(data.ciItem.newRsrpArray, data.ciItem.modelMaxArray);//俯视面雷达图
                DrawWeightTableSeries(data.ciItem.modelMaxArray);//权值理想覆盖图
                drawVertSplineSeries(data.ciItem.rsrpVertArray);//垂直面覆盖图
            }
        }

        private void miShowGis_Click(object sender, EventArgs e)
        {
            MainModel.ClearDTData();
            string strCellName = "";
            if (this.xtraTabControl1.SelectedTabPageIndex == 0)
            {
                strCellName = dataGridViewCell.SelectedRows[0].Tag as string;
            }
            else if (this.xtraTabControl1.SelectedTabPageIndex == 1)
            {
                strCellName = dataGridView.SelectedRows[0].Tag as string;
            }

            if (strCellName != null && strCellName != "" && dicUtranCellAngelData.ContainsKey(strCellName))
            {
                data = dicUtranCellAngelData[strCellName];
                if (data == null)
                    return;
                foreach (TestPoint tp in data.tpList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.CellAntSampleIndexDic = data.tpIndexDic;
                MainModel.SelectedLTECell = CellManager.GetInstance().GetLTECellLatest(data.cellname);
                MainModel.FireDTDataChanged(this);
                MainModel.FireSetDefaultMapSerialTheme("LTE_TDD","RSRP");
            }
        }

        private void miShowSimulation_Click(object sender, EventArgs e)
        {
            string strCellName = "";
            if (this.xtraTabControl1.SelectedTabPageIndex == 0)
            {
                strCellName = dataGridViewCell.SelectedRows[0].Tag as string;
            }
            else if (this.xtraTabControl1.SelectedTabPageIndex == 1)
            {
                strCellName = dataGridView.SelectedRows[0].Tag as string;
            }

            if (strCellName != null && strCellName != "" && dicUtranCellAngelData.ContainsKey(strCellName))
            {
                data = dicUtranCellAngelData[strCellName];
                if (data == null)
                    return;
                ZTAntennaBase.SimulationPoints simulationPoints = new ZTAntennaBase.SimulationPoints();
                simulationPoints.cellLongitude = data.cellLongitude;
                simulationPoints.cellLatitude = data.cellLatitude;
                simulationPoints.strNet = "LTE";
                simulationPoints.longLatTestList.AddRange(data.longLatTestList);
                simulationPoints.longLatModelList.AddRange(data.longLatModelList);

                MainModel.SimulationPoints = simulationPoints;
                MainModel.SelectedLTECell = CellManager.GetInstance().GetLTECellLatest(data.cellname);
                MainModel.MainForm.GetMapForm().GoToView(data.cellLongitude, data.cellLatitude);
                MainModel.FireCellDrawInfoChanged(this);
            }
        }

        private void miExportWholeExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }

        private void 拆分导出CSVToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ZTAntFuncHelper.OutputCsvFile(nrDatasList, sheetNames);
        }

        private void xtraTabControl1_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            if (xtraTabControl1.SelectedTabPageIndex == 0 || xtraTabControl1.SelectedTabPageIndex == 1)
            {
                miShwoChart.Visible = true;
                miShowGis.Visible = true;
                miShowSimulation.Visible = true;
            }
            else if (xtraTabControl1.SelectedTabPageIndex == 2 || xtraTabControl1.SelectedTabPageIndex == 3)
            {
                miShwoChart.Visible = false;
                miShowGis.Visible = false;
                miShowSimulation.Visible = false;
            }
        }

        private void chartControl5_CustomDrawSeriesPoint(object sender, CustomDrawSeriesPointEventArgs e)
        {
            double dRsrp = e.SeriesPoint.Values[0];
            iPointIndex = iPointIndex % 180;
            if (Math.Round(data.ciItem.model1Array[iPointIndex], 2) >= dRsrp)
                e.SeriesDrawOptions.Color = Color.Blue;
            else
                e.SeriesDrawOptions.Color = Color.Red;

            iPointIndex++;
        }

        private void chartControl2_SizeChanged(object sender, EventArgs e)
        {
            ((RadarDiagram)chartControl2.Diagram).AxisX.GridSpacing = 20;
        }

        private void btnGo_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = iDataNum;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;
            if (iPage < 0)
                iPage = 0;
            else if (iPage > iCount - 1)
                iPage = iCount - 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnPrevpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            iPage = iPage - 1 >= 0 ? iPage - 1 : iPage;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnNextpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = iDataNum;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;

            iPage = iPage + 1 >= iCount ? iPage : iPage + 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string strCellName = txtCellName.Text;
            FillData(strCellName);
        }
    }
}
