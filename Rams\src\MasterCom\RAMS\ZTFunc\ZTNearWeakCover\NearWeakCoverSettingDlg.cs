﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc.ZTNearWeakCover;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NearWeakCoverSettingDlg : BaseDialog
    {
        public NearWeakCoverSettingDlg()
        {
            InitializeComponent();
        }

        public FuncCondition Condition
        {
            get
            {
                FuncCondition cond = new FuncCondition();
                cond.Distance = (double)numDistance.Value;
                cond.Rsrp = (float)numRsrp.Value;
                return cond;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                numDistance.Value = (decimal)value.Distance;
                numRsrp.Value = (decimal)value.Rsrp;
            }
        }

    }
}
