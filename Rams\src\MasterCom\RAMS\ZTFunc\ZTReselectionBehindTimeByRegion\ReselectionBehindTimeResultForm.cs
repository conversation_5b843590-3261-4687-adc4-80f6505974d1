﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ReselectionBehindTimeResultForm : MinCloseForm
    {
        private List<BehindTimeSCellInfo> sInfoList;
        private MapForm mapForm;
        public ReselectionBehindTimeResultForm(MainModel mainModel) : base(mainModel)
        {
            InitializeComponent();
            mapForm = mainModel.MainForm.GetMapForm();
            sInfoList = new List<BehindTimeSCellInfo>();
            DisposeWhenClose = true;
            Init();
        }

        public void Init()
        {
            this.olvDuration.AspectGetter = delegate(object row)
            {
                if (row.GetType() == typeof(BehindTimeSCellInfo))
                {
                    BehindTimeSCellInfo sInfo = row as BehindTimeSCellInfo;
                    return sInfo.Duration;
                }
                return "";
            };

            this.olvNCellCount.AspectGetter = delegate(object row)
            {
                if (row.GetType() == typeof(BehindTimeSCellInfo))
                {
                    BehindTimeSCellInfo sInfo = row as BehindTimeSCellInfo;
                    return sInfo.NCellInfoList.Count;
                }
                return "";
            };

            this.olvMeanRxLevDiff.AspectGetter = delegate(object row)
            {
                if (row.GetType() == typeof(BehindTimeNCellInfo))
                {
                    BehindTimeNCellInfo nInfo = row as BehindTimeNCellInfo;
                    return nInfo.MeanRxLevDiff;
                }
                return "";
            };

            this.listView.CanExpandGetter = delegate(object x)
            {
                return x.GetType() == typeof(BehindTimeSCellInfo);
            };

            this.listView.ChildrenGetter = delegate(object x)
            {
                BehindTimeSCellInfo sInfo = x as BehindTimeSCellInfo;
                List<BehindTimeNCellInfo> nInfoList = new List<BehindTimeNCellInfo>();
                nInfoList.AddRange(sInfo.NCellInfoList);
                return nInfoList;
            };
        }

        public void FillData(List<BehindTimeSCellInfo> sInfoList)
        {
            this.sInfoList = sInfoList;
            listView.ClearObjects();
            listView.SetObjects(this.sInfoList);
        }

        /// <summary>
        /// 按listview格式导出excel
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                listView.ExpandAll();
                ExcelNPOIManager.ExportToExcel(listView);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
            finally
            {
                listView.CollapseAll();
            }
        }

        /// <summary>
        /// SA - NA1
        /// SA - NA2
        /// SA - NA3
        /// 格式导出excel
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void miExportExcelPair_Click(object sender, EventArgs e)
        {
            List<List<object>> datas = ColloctCellPair();
            ExcelNPOIManager.ExportToExcel(datas);
        }

        private void ToolStripMenuItemExpand_Click(object sender, EventArgs e)
        {
            try
            {
                listView.ExpandAll();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show("展开所有节点失败!" + ex.Message);
            }
        }

        private void ToolStripMenuItemFold_Click(object sender, EventArgs e)
        {
            try
            {
                listView.CollapseAll();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show("折叠所有节点失败!" + ex.Message);
            }
        }

        /// <summary>
        /// txt导出控制
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void miExportToTxt_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = "Excel file (*.txt)|*.txt";
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            WaitBox.Show("正在导出TXT...", ExportToTxtPair, dlg.FileName);
        }

        /// <summary>
        /// SA - NA1
        /// SA - NA2
        /// SA - NA3
        /// 格式导出TXT
        /// </summary>
        /// <param name="fName"></param>
        private void ExportToTxtPair(object fName)
        {
            FileStream fs = new FileStream(fName as string, FileMode.OpenOrCreate);
            StreamWriter sw = new StreamWriter(fs, Encoding.Unicode);
            List<List<object>> datas = ColloctCellPair();

            int loop = 0;
            foreach (List<object> row in datas)
            {
                WaitBox.ProgressPercent = ++loop * 100 / datas.Count;
                foreach (object obj in row)
                {
                    sw.Write(obj.ToString() + "\t");
                }
                sw.WriteLine();
            }

            sw.Close();
            fs.Close();
            MessageBox.Show("导出txt成功！");
            WaitBox.Close();
        }

        /// <summary>
        /// 小区对格式收集
        /// </summary>
        /// <returns></returns>
        private List<List<object>> ColloctCellPair()
        {
            List<List<object>> datas = new List<List<object>>();
            List<object> header = new List<object>();
            header.Add("主服小区名");
            addCellInfo(header);
            header.Add("持续时间");
            header.Add("邻区个数");
            header.Add("邻区序号");
            header.Add("邻区名");
            addCellInfo(header);
            header.Add("与主服小区场强差值");
            datas.Add(header);

            foreach (BehindTimeSCellInfo sInfo in sInfoList)
            {
                List<object> srow = new List<object>();
                srow.Add(sInfo.CellName);
                srow.Add(sInfo.Lac);
                srow.Add(sInfo.Ci);
                srow.Add(sInfo.TestPointCount);
                srow.Add(sInfo.MeanRxLev);
                srow.Add(sInfo.MaxRxLev);
                srow.Add(sInfo.MinRxLev);
                srow.Add(sInfo.Duration);
                srow.Add(sInfo.NCellInfoList.Count);

                foreach (BehindTimeNCellInfo nInfo in sInfo.NCellInfoList)
                {
                    List<object> nrow = new List<object>();
                    nrow.AddRange(srow);
                    nrow.Add(nInfo.ID);
                    nrow.Add(nInfo.CellName);
                    nrow.Add(nInfo.Lac);
                    nrow.Add(nInfo.Ci);
                    nrow.Add(nInfo.TestPointCount);
                    nrow.Add(nInfo.MeanRxLev);
                    nrow.Add(nInfo.MaxRxLev);
                    nrow.Add(nInfo.MinRxLev);
                    nrow.Add(nInfo.MeanRxLevDiff);

                    datas.Add(nrow);
                }
            }

            return datas;
        }

        private static void addCellInfo(List<object> header)
        {
            header.Add("LAC");
            header.Add("CI");
            header.Add("采样点数");
            header.Add("平均场强");
            header.Add("最大场强");
            header.Add("最小场强");
        }
    }
}
