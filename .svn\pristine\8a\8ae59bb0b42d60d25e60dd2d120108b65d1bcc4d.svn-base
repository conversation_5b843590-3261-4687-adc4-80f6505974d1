﻿using System;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NbIotMgrsOverlapCoverageSetting : LteMgrsConditionControlBase
    {
        public NbIotMgrsOverlapCoverageSetting()
        {
            InitializeComponent();
            chkAllRSRP.CheckedChanged += chkAllRSRP_CheckedChanged;
            initValues();
        }

        public override string Title
        {
            get { return "重叠覆盖度分布"; }
        }

        public override object GetCondition(out string invalidReason)
        {
            invalidReason = null;
            return new object[] {
                (double)numMinRSRP.Value,
                chkAllRSRP.Checked,
                (double)numAllMinRSRP.Value,
                (double)numRSRPDis.Value,
            };
        }

        /// <summary>
        /// 保存条件信息
        /// </summary>
        /// <param name="xcfg"></param>
        public override void SaveCondititon(XmlConfigFile xcfg)
        {
            XmlElement configWeakRsrp = xcfg.AddConfig("OverlapCoverage");
            xcfg.AddItem(configWeakRsrp, "MinRsrp", (double)numMinRSRP.Value);
            xcfg.AddItem(configWeakRsrp, "CheckAllRSRP", chkAllRSRP.Checked.ToString());
            xcfg.AddItem(configWeakRsrp, "AllMinRsrp", (double)numAllMinRSRP.Value);
            xcfg.AddItem(configWeakRsrp, "RsrpDis", (double)numRSRPDis.Value);
        }

        /// <summary>
        /// 从配置文件读取参数值
        /// </summary>
        private void initValues()
        {
            XmlConfigFile configFile = new MyXmlConfigFile(NbIotMgrsBaseSettingManager.Instance.ConfigPath);
            if (configFile.Load())
            {
                XmlElement configCoverage = configFile.GetConfig("OverlapCoverage");
                object obj = configFile.GetItemValue(configCoverage, "MinRsrp");
                if (obj != null)
                {
                    numMinRSRP.Value = (decimal)(double)obj;
                }
                obj = configFile.GetItemValue(configCoverage, "CheckAllRSRP");
                if (obj != null)
                {
                    chkAllRSRP.Checked = obj.ToString() == "True";
                }
                obj = configFile.GetItemValue(configCoverage, "AllMinRsrp");
                if (obj != null)
                {
                    numAllMinRSRP.Value = (decimal)(double)obj;
                }
                obj = configFile.GetItemValue(configCoverage, "RsrpDis");
                if (obj != null)
                {
                    numRSRPDis.Value = (decimal)(double)obj;
                }
            }
        }

        private void chkAllRSRP_CheckedChanged(object sender, EventArgs e)
        {
            numAllMinRSRP.Enabled = chkAllRSRP.Checked;
        }
    }
}
