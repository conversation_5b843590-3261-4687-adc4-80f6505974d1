﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.MTGis
{
    public class DbRect
    {
        public DbRect Clone()
        {
            DbRect rect = new DbRect(x1, y1, x2, y2);
            return rect;
        }

        public double x1 { get; set; }
        public double x2 { get; set; }
        public double y1 { get; set; }
        public double y2 { get; set; }

        public DbRect()
        {
        }
        /// <summary>
        /// pnt1 左下角  pnt2 右上角
        /// </summary>
        /// <param name="pnt1"></param>
        /// <param name="pnt2"></param>
        public DbRect(DbPoint pnt1, DbPoint pnt2)
        {
            this.x1 = Math.Min(pnt1.x, pnt2.x);
            this.x2 = Math.Max(pnt1.x, pnt2.x);
            this.y1 = Math.Min(pnt1.y, pnt2.y);
            this.y2 = Math.Max(pnt1.y, pnt2.y);
        }

        public DbRect(double x1, double y1, double x2, double y2)
        {
            this.x1 = Math.Min(x1, x2);
            this.x2 = Math.Max(x1, x2);
            this.y1 = Math.Min(y1, y2);
            this.y2 = Math.Max(y1, y2);
        }
        public DbPoint Center()
        {
            return new DbPoint((x1 + x2) / 2, (y1 + y2) / 2);
        }
        public bool IsEmpty
        {
            get {
                return x1 == y1 && y1 == x2;
            }
        }
        public double Width()
        {
            return Math.Abs(x2 - x1);
        }
        public double Height()
        {
            return Math.Abs(y2 - y1);
        }

        public void MergeRects(DbRect rct2Merger)
        {
            this.x1 = Math.Min(x1, rct2Merger.x1);
            this.x2 = Math.Max(x2, rct2Merger.x2);
            this.y1 = Math.Min(y1, rct2Merger.y1);
            this.y2 = Math.Max(y2, rct2Merger.y2);
        }

        /// <summary>
        /// 某点是否在该矩形内，包括边界
        /// </summary>
        /// <param name="longitue">纬度</param>
        /// <param name="latitue">经度</param>
        /// <returns>return true when in</returns>
        public bool IsPointInThisRect(double longitue, double latitue)
        {
            return longitue >= x1 && longitue <= x2 && latitue >= y1 && latitue <= y2;
        }

        /// <summary>
        /// 某点是否在该矩形内，不包括边界
        /// </summary>
        /// <param name="pnt"></param>
        /// <returns></returns>
        public bool IsPointInThisRect(DbPoint pnt)
        {
            return IsPointInThisRect(pnt.x, pnt.y);
        }

        public bool Within(DbRect rect)
        {
            if (x2 < rect.x1 || x1 > rect.x2 || y2 < rect.y1 || y1 > rect.y2)
            {
                return false;
            }
            return true;
        }


        internal DbRect Expend(double factor)
        {
            DbPoint center = this.Center();
            double width = this.Width() * factor;
            double height = this.Height() * factor;
            return new DbRect(center.x - 0.5 * width, center.y - 0.5 * height, center.x + 0.5 * width, center.y + 0.5 * height);
        }
    }
}
