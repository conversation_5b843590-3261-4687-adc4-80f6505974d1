﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Func.Voronoi;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    #region 室外站

    public class OutDoorBtsAcceptInfo_QH : BtsAcceptInfoBase
    {
        public OutDoorBtsAcceptInfo_QH(LTEBTS lteBts)
            : base(lteBts)
        {
        }

        //各小区验收信息key:cellid
        public Dictionary<int, OutDoorCellAcceptInfo_QH> CellsAcceptDic { get; set; } = new Dictionary<int, OutDoorCellAcceptInfo_QH>();

        private readonly RateKpiInfo handOverInfo = new RateKpiInfo("系统内切换");
        public RateKpiInfo HandOverInfo { get { return handOverInfo; } }

        private readonly UltraSiteKpiInfo ultraSiteInfo = new UltraSiteKpiInfo("四超检查");
        public UltraSiteKpiInfo UltraSiteInfo { get { return ultraSiteInfo; } }
        
        public string Is34G_ReselectAccordDes { get; set; }
        public string Is24G_ReselectAccordDes { get; set; }
        public string IsCsfbAccordDes { get; set; }
        public string IsSrvccAccordDes { get; set; }
        public string IsVolteAudioAccordDes { get; set; }
        public string IsVolteVideoAccordDes { get; set; }
        public string CoverPicPath_Handover { get; set; }//站点PCI覆盖截图
        public OutBtsPhoneTestInfo PhoneTestInfo { get; set; }
        public void AddHandOverKpiInfo(Dictionary<uint, object> kpiDic)
        {
            //系统内切换 只取最新一个文件的指标
            if (handOverInfo.TotalCount == null || this.handOverInfo.ValidCount == null)
            {
                foreach (uint key in kpiDic.Keys)
                {
                    KpiKey kpiKey = (KpiKey)key;
                    object objValue = kpiDic[key];

                    switch (kpiKey)
                    {
                        case KpiKey.InHandoverRequestCnt:
                            this.handOverInfo.TotalCount = (int)objValue;
                            break;
                        case KpiKey.InHandoverSucceedCnt:
                            this.handOverInfo.ValidCount = (int)objValue;
                            break;
                    }
                }
            }
        }

        /// <summary>
        /// 检验基站和各小区指标是否合格,获取完基站和各小区的指标信息后调用
        /// </summary>
        public override void CheckBtsIsAccordAccept()
        {
            StringBuilder strbNotAccordDes = new StringBuilder();
            bool allCellAccord = true;
            foreach (LTECell cell in LteBts.Cells)
            {
                OutDoorCellAcceptInfo_QH cellInfo;
                if (this.CellsAcceptDic.TryGetValue(cell.CellID, out cellInfo))
                {
                    cellInfo.CheckCellIsAccordAccept();
                    setBtsKpiInfo(cellInfo);
                    if (!cellInfo.IsAccord)
                    {
                        allCellAccord = false;
                        strbNotAccordDes.AppendLine(cell.Name + "小区不达标的指标：" + cellInfo.NotAccordKpiDes);
                    }
                }
                else
                {
                    allCellAccord = false;
                    strbNotAccordDes.AppendLine(cell.Name + "小区未查询到指标信息;");
                }
            }

            ultraSiteInfo.CheckIsAccord(LteBts);
            if (!ultraSiteInfo.IsAccord)
            {
                allCellAccord = false;
                strbNotAccordDes.AppendLine(ultraSiteInfo.KpiName + "指标不达标;");
            }

            this.handOverInfo.CheckIsAccord(10, 100);
            if (!handOverInfo.IsAccord)
            {
                allCellAccord = false;
                strbNotAccordDes.AppendLine("整站的" + handOverInfo.KpiName + "指标不达标;");
            }
            this.NotAccordKpiDes = strbNotAccordDes.ToString();
            this.IsAccordAccept = allCellAccord && this.handOverInfo.IsAccord;
        }
        protected void setBtsKpiInfo(OutDoorCellAcceptInfo_QH cellInfo)
        {
            this.Is34G_ReselectAccordDes = isKpiAccord(this.Is34G_ReselectAccordDes, cellInfo.Reselect34Info.IsAccord);
            this.Is24G_ReselectAccordDes = isKpiAccord(this.Is24G_ReselectAccordDes, cellInfo.Reselect24Info.IsAccord);
            this.IsCsfbAccordDes = isKpiAccord(this.IsCsfbAccordDes, cellInfo.IsCsfbAccord);
            this.IsSrvccAccordDes = isKpiAccord(this.IsSrvccAccordDes, cellInfo.SrvccInfo.IsAccord);
            this.IsVolteAudioAccordDes = isKpiAccord(this.IsVolteAudioAccordDes, cellInfo.VolteAudioInfo.IsAccord);
            this.IsVolteVideoAccordDes = isKpiAccord(this.IsVolteVideoAccordDes, cellInfo.VolteVideoInfo.IsAccord);
        }
        private string isKpiAccord(string btsKpiDes, bool cellKpi)
        {
            if (btsKpiDes == null)
            {
                return cellKpi ? "是" : "否";
            }

            if (cellKpi && (btsKpiDes == "是"))
            {
                return "是";
            }
            return "否";
        }
    }
    public class OutDoorCellAcceptInfo_QH : OutDoorCellAcceptInfo, IComparable<OutDoorCellAcceptInfo>
    {
        public OutDoorCellAcceptInfo_QH(LTECell cell)
            : base(cell)
        {
        }
        private readonly FtpKpiInfo ftpDlInfo_Bad = new FtpKpiInfo("FTP差点下行指标");
        public FtpKpiInfo FtpDlInfo_Bad { get { return ftpDlInfo_Bad; } }

        private readonly FtpKpiInfo ftpUlInfo_Bad = new FtpKpiInfo("FTP差点上行指标");
        public FtpKpiInfo FtpUlInfo_Bad { get { return ftpUlInfo_Bad; } }

        public double? ReturnToLteTimeDelayAvg { get; set; }
        public bool IsCsfbAccord { get; private set; }

        private readonly RateKpiInfo csfbReturnToLteInfo = new RateKpiInfo("CSFB手机返回成功率");
        public RateKpiInfo CsfbReturnToLteInfo { get { return csfbReturnToLteInfo; } }

        private readonly RateKpiInfo srvccInfo = new RateKpiInfo("SRVCC切换成功率");
        public RateKpiInfo SrvccInfo { get { return srvccInfo; } }

        private readonly RateKpiInfo volteAudioInfo = new RateKpiInfo("VOLTE语音呼叫成功率");
        public RateKpiInfo VolteAudioInfo { get { return volteAudioInfo; } }

        private readonly RateKpiInfo volteVideoInfo = new RateKpiInfo("VOLTE视频呼叫成功率指标");
        public RateKpiInfo VolteVideoInfo { get { return volteVideoInfo; } }

        private readonly UltraSiteKpiInfo ultraSiteInfo = new UltraSiteKpiInfo("四超检查指标");
        public UltraSiteKpiInfo UltraSiteInfo { get { return ultraSiteInfo; } }

        public string CoverPicPath_Rsrp { get; set; }
        public string CoverPicPath_Sinr { get; set; }
        public string CoverPicPath_PdcpDL { get; set; }
        public string CoverPicPath_PdcpUL { get; set; }
        public OutCellPhoneTestInfo PhoneTestInfo { get; set; }

        protected override void statsKpiNewestValue(KpiKey kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case KpiKey.FtpDlRsrpAvg_Bad:
                    this.ftpDlInfo_Bad.RsrpAvg = (double)objValue;
                    break;
                case KpiKey.FtpDlSinrAvg_Bad:
                    this.ftpDlInfo_Bad.SinrAvg = (double)objValue;
                    break;
                case KpiKey.FtpDlSpeedAvg_Bad:
                    this.ftpDlInfo_Bad.SpeedAvg = (double)objValue;
                    break;
                case KpiKey.FtpUlRsrpAvg_Bad:
                    this.ftpUlInfo_Bad.RsrpAvg = (double)objValue;
                    break;
                case KpiKey.FtpUlSinrAvg_Bad:
                    this.ftpUlInfo_Bad.SinrAvg = (double)objValue;
                    break;
                case KpiKey.FtpUlSpeedAvg_Bad:
                    this.ftpUlInfo_Bad.SpeedAvg = (double)objValue;
                    break;
                case KpiKey.CsfbReturnToLteRequestCnt:
                    this.CsfbReturnToLteInfo.TotalCount = (int)objValue;
                    break;
                case KpiKey.CsfbReturnToLteCompleteCnt:
                    this.CsfbReturnToLteInfo.ValidCount = (int)objValue;
                    break;
                case KpiKey.CsfbReturnToLteTimeDelay:
                    this.ReturnToLteTimeDelayAvg = (double)objValue;
                    break;
                case KpiKey.SrvccCallRequestCnt:
                    this.SrvccInfo.TotalCount = (int)objValue;
                    break;
                case KpiKey.SrvccCallSucceedCnt:
                    this.SrvccInfo.ValidCount = (int)objValue;
                    break;
                case KpiKey.VolteAudioCallRequestCnt:
                    this.VolteAudioInfo.TotalCount = (int)objValue;
                    break;
                case KpiKey.VolteAudioCallSucceedCnt:
                    this.VolteAudioInfo.ValidCount = (int)objValue;
                    break;
                case KpiKey.VolteVideoCallRequestCnt:
                    this.VolteVideoInfo.TotalCount = (int)objValue;
                    break;
                case KpiKey.VolteVideoCallSucceedCnt:
                    this.VolteVideoInfo.ValidCount = (int)objValue;
                    break;
                default:
                    base.statsKpiNewestValue(kpiKey, objValue);
                    break;
            }
        }

        /// <summary>
        /// 检验小区指标是否合格,获取完小区各指标信息后调用
        /// </summary>
        /// <returns></returns>
        public override void CheckCellIsAccordAccept()
        {
            this.RrcInfo.CheckIsAccord(5, 100);
            this.ErabInfo.CheckIsAccord(5, 100);
            this.AccessInfo.CheckIsAccord(5, 100);
            this.Reselect34Info.CheckIsAccord(5, 100);
            this.Reselect24Info.CheckIsAccord(5, 100);
            this.CsfbInfo.CheckIsAccord(5, 100);
            this.CsfbReturnToLteInfo.CheckIsAccord(1, 100);
            this.SrvccInfo.CheckIsAccord(5, 100);
            this.VolteAudioInfo.CheckIsAccord(5, 100);
            this.VolteVideoInfo.CheckIsAccord(5, 100);
            this.FtpDlInfo.CheckIsAccord(-85, 25, 45);
            this.FtpUlInfo.CheckIsAccord(-85, 25, 6);

            this.IsCsfbAccord = this.CsfbInfo.IsAccord && csfbReturnToLteInfo.IsAccord && ReturnToLteTimeDelayAvg < 1.5;

            bool isAllKpiAccord = true;
            StringBuilder strbNotAccordKpiName = new StringBuilder();
            if (!(this.Reselect34Info.IsAccord || this.Reselect24Info.IsAccord))
            {
                isAllKpiAccord = false;
                strbNotAccordKpiName.Append(this.Reselect34Info.KpiName + "或" + this.Reselect24Info.KpiName + ";");
            }
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.RrcInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.ErabInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.AccessInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.IsCsfbAccord, "CSFB功能测试", ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.SrvccInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.VolteAudioInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.VolteVideoInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.FtpDlInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.FtpUlInfo, ref isAllKpiAccord));

            this.isAccord = isAllKpiAccord;
            this.NotAccordKpiDes = strbNotAccordKpiName.ToString();
        }
        public new int CompareTo(OutDoorCellAcceptInfo other)
        {
            return this.CellId.CompareTo(other.CellId);
        }
    }

    #endregion

    #region 室内站
    public class InDoorBtsAcceptInfo_QH : BtsAcceptInfoBase
    {
        public InDoorBtsAcceptInfo_QH(LTEBTS lteBts)
            : base(lteBts)
        {
        }
        public string CoveredFloors { get; set; }
        public string CoveredFloorsDes_Lack { get; set; }

        /// <summary>
        /// 单双路
        /// </summary>
        public string RoadTypeDes { get; set; }

        public List<InDoorCellAcceptInfo_QH> CellsAcceptList
        {
            get
            {
                List<InDoorCellAcceptInfo_QH> cellsAcceptList = new List<InDoorCellAcceptInfo_QH>(CellsAcceptDic.Values);
                cellsAcceptList.Sort();
                return cellsAcceptList;
            }
        }

        //key:CellID
        public Dictionary<int, InDoorCellAcceptInfo_QH> CellsAcceptDic { get; set; } = new Dictionary<int, InDoorCellAcceptInfo_QH>();
        public string IsHandoverAccordDes { get; set; }
        public InBtsPhoneTestInfo PhoneTestInfo { get; set; }

        public Dictionary<string, InBtsFloorCoverPicInfo> FloorCoverPicInfoDic { get; set; } = new Dictionary<string, InBtsFloorCoverPicInfo>();

        /// <summary>
        /// 检验基站和各小区指标是否合格,获取完基站和各小区的指标信息后调用
        /// </summary>
        public override void CheckBtsIsAccordAccept()
        {
            getCoveredFloors();

            this.IsHandoverAccordDes = "是";

            bool allCellAccord = true;
            StringBuilder strbNotAccordDes = new StringBuilder();
            foreach (LTECell cell in LteBts.Cells)
            {
                InDoorCellAcceptInfo_QH cellInfo;
                if (this.CellsAcceptDic.TryGetValue(cell.CellID, out cellInfo))
                {
                    if (string.IsNullOrEmpty(this.RoadTypeDes))
                    {
                        this.RoadTypeDes = cellInfo.RoadTypeDes;
                    }

                    cellInfo.CheckCellIsAccordAccept();
                    if (!cellInfo.IsAccord)
                    {
                        allCellAccord = false;
                        strbNotAccordDes.AppendLine(cell.Name + "小区不达标的指标：" + cellInfo.NotAccordKpiDes);
                    }
                    if (!cellInfo.HandOverInfo.IsAccord)
                    {
                        this.IsHandoverAccordDes = "否";
                    }
                }
                else
                {
                    allCellAccord = false;
                    this.IsHandoverAccordDes = "否";
                    strbNotAccordDes.AppendLine(cell.Name + "小区未查询到指标信息;");
                }
            }
            this.NotAccordKpiDes = strbNotAccordDes.ToString();
            this.IsAccordAccept = allCellAccord;
        }

        private void getCoveredFloors()
        {
            List<int> floorList = new List<int>();
            foreach (InDoorCellAcceptInfo_QH cellInfo in this.CellsAcceptDic.Values)
            {
                floorList.AddRange(cellInfo.CoveredFloorList);
            }
            if (floorList.Count > 0)
            {
                setCoveredFloorsDes(floorList);
            }

        }

        private void setCoveredFloorsDes(List<int> floorList)
        {
            floorList.Sort();
            int minFloor = floorList[0];
            int maxFloor = floorList[floorList.Count - 1];

            if (minFloor == maxFloor)
            {
                this.CoveredFloors = (minFloor + "F").Replace("-", "B");
            }
            else
            {
                this.CoveredFloors = string.Format("{0}F—{1}F", minFloor, maxFloor).Replace("-", "B");

                StringBuilder lackFloorDesStrb = new StringBuilder();
                for (int i = minFloor; i <= maxFloor; i++)
                {
                    if (i != 0 && !floorList.Contains(i))
                    {
                        lackFloorDesStrb.Append(i + "F" + "、");
                    }
                }
                if (lackFloorDesStrb.Length > 0)
                {
                    this.CoveredFloorsDes_Lack = "缺少"
                        + lackFloorDesStrb.Remove(lackFloorDesStrb.Length - 1, 1).ToString() + "文件...";
                }
            }
        }
    }
    public class InDoorCellAcceptInfo_QH : InDoorCellAcceptInfo
    {
        public InDoorCellAcceptInfo_QH(LTECell cell)
            : base(cell)
        {
        }

        public double? ReturnToLteTimeDelayAvg { get; set; }
        public bool IsCsfbAccord { get; private set; }

        private readonly RateKpiInfo csfbReturnToLteInfo = new RateKpiInfo("CSFB手机返回成功率");
        public RateKpiInfo CsfbReturnToLteInfo { get { return csfbReturnToLteInfo; } }

        private readonly RateKpiInfo srvccInfo = new RateKpiInfo("SRVCC切换成功率");
        public RateKpiInfo SrvccInfo { get { return srvccInfo; } }

        private readonly RateKpiInfo volteAudioInfo = new RateKpiInfo("VOLTE语音呼叫成功率");
        public RateKpiInfo VolteAudioInfo { get { return volteAudioInfo; } }

        private readonly RateKpiInfo volteAudioInOutHandInfo = new RateKpiInfo("VOLTE语音室内外切换成功率");
        public RateKpiInfo VolteAudioInOutHandInfo { get { return volteAudioInOutHandInfo; } }

        private readonly RateKpiInfo volteAudioInHandInfo = new RateKpiInfo("VOLTE语音室内小区间切换成功率");
        public RateKpiInfo VolteAudioInHandInfo { get { return volteAudioInHandInfo; } }

        private readonly RateKpiInfo volteVideoInfo = new RateKpiInfo("VOLTE视频呼叫成功率指标");
        public RateKpiInfo VolteVideoInfo { get { return volteVideoInfo; } }
        public InCellPhoneTestInfo PhoneTestInfo { get; set; }
        protected override void statsFtpUlInfo(LevelTestKpiInfo levelInfo)
        {
            base.statsFtpUlInfo(levelInfo);

            setRoadType(levelInfo.FileName);
            dlCoverRate.AddInfo(levelInfo.PointCount_RsrpB105sinr6, levelInfo.PointCount_RsrpAndSinr);
        }
        protected override void statsKpiNewestValue(KpiKey kpiKey, object objValue)
        {
            switch (kpiKey)
            {
                case KpiKey.CsfbReturnToLteRequestCnt:
                    this.CsfbReturnToLteInfo.TotalCount = (int)objValue;
                    break;
                case KpiKey.CsfbReturnToLteCompleteCnt:
                    this.CsfbReturnToLteInfo.ValidCount = (int)objValue;
                    break;
                case KpiKey.CsfbReturnToLteTimeDelay:
                    this.ReturnToLteTimeDelayAvg = (double)objValue;
                    break;
                case KpiKey.SrvccCallRequestCnt:
                    this.SrvccInfo.TotalCount = (int)objValue;
                    break;
                case KpiKey.SrvccCallSucceedCnt:
                    this.SrvccInfo.ValidCount = (int)objValue;
                    break;
                case KpiKey.VolteAudioCallRequestCnt:
                    this.VolteAudioInfo.TotalCount = (int)objValue;
                    break;
                case KpiKey.VolteAudioCallSucceedCnt:
                    this.VolteAudioInfo.ValidCount = (int)objValue;
                    break;
                case KpiKey.VolteAudioInOutHandTotalCnt:
                    this.VolteAudioInOutHandInfo.TotalCount = (int)objValue;
                    break;
                case KpiKey.VolteAudioInOutHandSucceedCnt:
                    this.VolteAudioInOutHandInfo.ValidCount = (int)objValue;
                    break;
                case KpiKey.VolteAudioInHandTotalCnt:
                    this.VolteAudioInHandInfo.TotalCount = (int)objValue;
                    break;
                case KpiKey.VolteAudioInHandSucceedCnt:
                    this.VolteAudioInHandInfo.ValidCount = (int)objValue;
                    break;
                case KpiKey.VolteVideoCallRequestCnt:
                    this.VolteVideoInfo.TotalCount = (int)objValue;
                    break;
                case KpiKey.VolteVideoCallSucceedCnt:
                    this.VolteVideoInfo.ValidCount = (int)objValue;
                    break;
                default:
                    base.statsKpiNewestValue(kpiKey, objValue);
                    break;
            }
        }

        /// <summary>
        /// 检验小区指标是否合格,获取完小区各指标信息后调用
        /// </summary>
        /// <returns></returns>
        public override void CheckCellIsAccordAccept()
        {
            this.RrcInfo.CheckIsAccord(5, 100);
            this.ErabInfo.CheckIsAccord(5, 100);
            this.AccessInfo.CheckIsAccord(5, 100);
            this.CsfbInfo.CheckIsAccord(5, 100);
            this.CsfbReturnToLteInfo.CheckIsAccord(1, 100);
            this.SrvccInfo.CheckIsAccord(5, 100);
            this.VolteAudioInfo.CheckIsAccord(5, 100);
            this.VolteVideoInfo.CheckIsAccord(5, 100);
            this.HandOverInfo.CheckIsAccord(5, 98);
            this.DlCoverRate.CheckIsAccord(1, 95);

            this.IsCsfbAccord = this.CsfbInfo.IsAccord && csfbReturnToLteInfo.IsAccord && ReturnToLteTimeDelayAvg < 1.5;
            if (this.RoadTypeDes == "单路")
            {
                this.IsFtpDlAccord = FtpDlSpeedInfo.KpiAvgValue >= 45;
                this.IsFtpUlAccord = FtpUlSpeedInfo.KpiAvgValue >= 6;
            }
            else if (this.RoadTypeDes == "双路")
            {
                this.IsFtpDlAccord = FtpDlSpeedInfo.KpiAvgValue >= 60;
                this.IsFtpUlAccord = FtpUlSpeedInfo.KpiAvgValue >= 10;
            }
            else if (this.RoadTypeDes == "移频双路")
            {
                this.IsFtpDlAccord = FtpDlSpeedInfo.KpiAvgValue >= 55;
            }
            bool isRsrpIsAccord = FtpRsrpInfo.KpiAvgValue > -85;
            bool isSinrIsAccord = FtpSinrInfo.KpiAvgValue >= 25;

            bool isAllKpiAccord = true;
            StringBuilder strbNotAccordKpiName = new StringBuilder();
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.RrcInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.ErabInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.AccessInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.IsCsfbAccord, "CSFB功能测试", ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.SrvccInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.VolteAudioInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.VolteVideoInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.HandOverInfo, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(this.DlCoverRate, ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(isRsrpIsAccord, "RSRP", ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(isSinrIsAccord, "SINR", ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(IsFtpDlAccord, "下行吞吐率", ref isAllKpiAccord));
            strbNotAccordKpiName.Append(getNotAccordKpiInfo(IsFtpUlAccord, "上行吞吐率", ref isAllKpiAccord));

            this.isAccord = isAllKpiAccord;
            this.NotAccordKpiDes = strbNotAccordKpiName.ToString();
        }
    }
    #endregion

    public class UltraSiteKpiInfo
    {
        public UltraSiteKpiInfo(string strKpiName)
        {
            this.KpiName = strKpiName;
        }
        public string KpiName { get; set; }

        public bool IsAccord { get; private set; }
        public string IsAccordDes { get { return IsAccord ? "是" : "否"; } }

        public void CheckIsAccord(LTEBTS lteBts)
        {
            UltraSiteStationAccept judgeUltraSite = new UltraSiteStationAccept(lteBts);
            bool isAccord = judgeUltraSite.JudgeSites();
            if (isAccord)
            {
                this.IsAccord = true;
            }
            else
            {
                this.IsAccord = false;
            }
        }

        public override string ToString()
        {
            return IsAccordDes;
        }
    }

    public class UltraSiteStationAccept
    {
        readonly LTEBTS lteBts;
        Dictionary<ICell, List<UltraSiteCell>> cellDic;
        UltraSiteCondition ultraSiteCondition;
        int regionRadius;
        //判断某个站是否为三超站点
        public UltraSiteStationAccept(LTEBTS lteBts)
        {
            this.lteBts = lteBts;
        }

        public void Init()
        {
            cellDic = new Dictionary<ICell, List<UltraSiteCell>>();
            ultraSiteCondition = new UltraSiteCondition();
            //区域半径
            regionRadius = 3000;
            //超远
            ultraSiteCondition.AvgSiteDistanceMax = 700;
            ultraSiteCondition.AvgSitesDistanceAngle = 120;
            //超高
            ultraSiteCondition.AltitudeMax = 50;
            //超近
            ultraSiteCondition.NearDistanceMin = 100;
            //超近异频站距离
            ultraSiteCondition.DiffBandDistanceMin = 50;
        }
        
        public virtual bool JudgeSites()
        {
            Init();
            List<LTEBTS> tempSites = getCurrentLTEBTSs();
            List<ISite> allSites = new List<ISite>(tempSites.Count);
            foreach (LTEBTS item in tempSites)
            {
                if (item.Type == LTEBTSType.Outdoor)
                {
                    allSites.Add(item);
                }
            }

            if (allSites.Count < 3)
            {
                //"室外站数量小于3个，无法计算站间距！"
                return true;
            }

            //需检测的站
            ISite inRegionSite = lteBts;
            //范围内的其他站
            List<ISite> outRegionSites = new List<ISite>(allSites);
            outRegionSites.Remove(lteBts);

            Dictionary<ISite, ZTFunc.SiteDelaunayTri> delaunayDic = getSiteDelaunayTri(allSites, outRegionSites);
            //先计算站间距，后面超高，超近小区会有站间距信息。
            Dictionary<ICell, UltraFarSite> cellDisDic = new Dictionary<ICell, UltraFarSite>();
            dealUltraFarSites(allSites, inRegionSite, delaunayDic, cellDisDic);

            dealRegionSites(inRegionSite, outRegionSites, cellDisDic);

            if (cellDic.Count > 0)
            {
                return false;
            }

            return true;
        }

        protected virtual List<LTEBTS> getCurrentLTEBTSs()
        {
            //获取当前基站指定范围内的所有基站
            List<LTEBTS> validBts = new List<LTEBTS>();
            if (lteBts != null)
            {
                validBts = CellManager.GetInstance().GetCurrentLTEBTSs();
                for (int i = 0; i < validBts.Count; i++)
                {
                    ISite site = validBts[i];
                    double distance = MathFuncs.GetDistance(lteBts.Longitude, lteBts.Latitude, site.Longitude, site.Latitude);
                    if (distance > regionRadius)
                    {
                        validBts.RemoveAt(i);
                        i--;
                    }
                }
            }
            return validBts;
        }

        #region 构建delaunay三角网
        private Dictionary<ISite, SiteDelaunayTri> getSiteDelaunayTri(List<ISite> allSites
            , List<ISite> filterSites)
        {
            Dictionary<int, List<ISite>> posSiteDic = new Dictionary<int, List<ISite>>();
            List<Vertex> pnts = new List<Vertex>();
            foreach (ISite site in allSites)
            {
                Vertex pnt = new Vertex(site.Longitude, site.Latitude);
                if (!pnts.Contains(pnt))
                {//过滤掉相同经纬度的点
                    pnts.Add(pnt);
                }
                int posKey = site.Longitude.GetHashCode() ^ site.Latitude.GetHashCode();
                List<ISite> samePosSites = null;
                if (!posSiteDic.TryGetValue(posKey, out samePosSites))
                {
                    samePosSites = new List<ISite>();
                    posSiteDic.Add(posKey, samePosSites);
                }
                samePosSites.Add(site);
            }

            CTriangle cTriangle = new CTriangle();
            List<Vertex> cTriangleResult = null;
            try
            {
                cTriangleResult = cTriangle.Triangulate(pnts);
            }
            catch
            {
                return new Dictionary<ISite, SiteDelaunayTri>();
            }
            //System.Diagnostics.Debug.Assert(cTriangleResult.Count % 3 == 0, "构建delaunay三角网失败")
            Dictionary<ISite, SiteDelaunayTri> delaunayDic = new Dictionary<ISite, SiteDelaunayTri>();
            //每3个点，为一个delaunay三角
            for (int i = 0; i + 2 < cTriangleResult.Count; i++)
            {
                Vertex pt1 = cTriangleResult[i];
                Vertex pt2 = cTriangleResult[++i];
                Vertex pt3 = cTriangleResult[++i];
                List<ISite> site1 = posSiteDic[pt1.GetHashCode()];
                List<ISite> site2 = posSiteDic[pt2.GetHashCode()];
                List<ISite> site3 = posSiteDic[pt3.GetHashCode()];

                addOtherSite(filterSites, delaunayDic, site1, site2, site3);
                addOtherSite(filterSites, delaunayDic, site2, site1, site3);
                addOtherSite(filterSites, delaunayDic, site3, site1, site2);
            }
            return delaunayDic;
        }

        private void addOtherSite(List<ISite> filterSites, Dictionary<ISite, SiteDelaunayTri> delaunayDic,
            List<ISite> siteA, List<ISite> siteB, List<ISite> siteC)
        {
            foreach (ISite site in siteA)
            {
                if (!filterSites.Contains((LTEBTS)site))
                {
                    SiteDelaunayTri dTri = null;
                    if (!delaunayDic.TryGetValue(site, out dTri))
                    {
                        dTri = new SiteDelaunayTri(site);
                        delaunayDic.Add(site, dTri);
                    }
                    foreach (ISite item in siteB)
                    {
                        dTri.AddOtherSite(item);
                    }
                    foreach (ISite item in siteC)
                    {
                        dTri.AddOtherSite(item);
                    }
                }
            }
        }
        #endregion

        private void dealUltraFarSites(List<ISite> allSites, ISite inRegionSite,
            Dictionary<ISite, SiteDelaunayTri> delaunayDic, Dictionary<ICell, UltraFarSite> cellDisDic)
        {
            if (!delaunayDic.ContainsKey(inRegionSite))
            {
                return;
            }
            SiteDelaunayTri siteDTri = delaunayDic[inRegionSite];
            //超远 （方向角站间距）
            List<UltraFarSite> tempFarSites = getCellAvgDistanceByDir(inRegionSite, allSites);
            foreach (UltraFarSite farSite in tempFarSites)
            {
                farSite.SetDelaunayInfo(siteDTri);
                cellDisDic[farSite.Cell] = farSite;
                //判断是否是超远站
                if (ultraSiteCondition.IsTooFar(farSite.Distance))
                {
                    addUltraFarSite(farSite);
                }
            }
        }

        private void addUltraFarSite(UltraFarSite farSite)
        {
            List<UltraSiteCell> ultraSet = null;
            if (!cellDic.TryGetValue(farSite.Cell, out ultraSet))
            {
                ultraSet = new List<UltraSiteCell>();
                cellDic.Add(farSite.Cell, ultraSet);
            }
            ultraSet.Add(farSite);
        }

        private List<UltraFarSite> getCellAvgDistanceByDir(ISite site, List<ISite> otherSites)
        {
            //获取基站覆盖方向上最近的3个站
            LTEBTS bts = site as LTEBTS;
            List<UltraFarSite> farCells = new List<UltraFarSite>();
            foreach (LTECell cell in bts.LatestCells)
            {
                List<ISite> twoKMSites = new List<ISite>();
                List<ISite> tenKMSites = new List<ISite>();
                List<ISite> twtyKMSites = new List<ISite>();
                addSitesByDistance(otherSites, bts, twoKMSites, tenKMSites, twtyKMSites);
                List<SiteDistance> nearestCells = new List<SiteDistance>();
                addInCoverDirectionSites(nearestCells, cell, twoKMSites);
                if (nearestCells.Count < 3)
                {
                    addInCoverDirectionSites(nearestCells, cell, tenKMSites);
                }
                if (nearestCells.Count < 3)
                {
                    addInCoverDirectionSites(nearestCells, cell, twtyKMSites);
                }
                nearestCells.Sort();
                double disTotal = 0;
                List<SiteDistance> nSites = new List<SiteDistance>();
                for (int i = 0; i < 3 && i < nearestCells.Count; i++)
                {
                    nSites.Add(nearestCells[i]);
                    disTotal += nearestCells[i].Distance;
                }
                double avgDis = Math.Round(disTotal / nSites.Count, 2);
                farCells.Add(new UltraFarSite(cell, avgDis, nSites));
            }
            return farCells;
        }

        private void addSitesByDistance(List<ISite> otherSites, LTEBTS bts, List<ISite> twoKMSites, List<ISite> tenKMSites, List<ISite> twtyKMSites)
        {
            foreach (ISite other in otherSites)
            {
                if (other == bts)
                {
                    continue;
                }
                double lngDis = Math.Abs(bts.Longitude - other.Longitude);
                double latDis = Math.Abs(bts.Latitude - other.Latitude);
                if (lngDis > 0.2 || latDis > 0.2)
                {//约20KM
                }
                else if (lngDis > 0.1 || latDis > 0.1)
                {//约10KM
                    twtyKMSites.Add(other);
                }
                else if (lngDis > 0.02 || latDis > 0.02)
                {//约2KM
                    tenKMSites.Add(other);
                }
                else
                {
                    twoKMSites.Add(other);
                }
            }
        }

        private void addInCoverDirectionSites(List<SiteDistance> sites, ICell cell, List<ISite> siteSet)
        {
            foreach (ISite site in siteSet)
            {
                if (MathFuncs.JudgePoint(cell.Longitude, cell.Latitude, site.Longitude, site.Latitude, (int)cell.Direction
                        , ultraSiteCondition.AvgSitesDistanceAngle / 2))
                {//在扇区范围内
                    double distance = MathFuncs.GetDistance(cell.Longitude, cell.Latitude
                    , site.Longitude, site.Latitude);
                    sites.Add(new SiteDistance(site, distance));
                }
            }
        }

        private void dealRegionSites(ISite inRegionSite, List<ISite> outRegionSites, Dictionary<ICell, UltraFarSite> cellDisDic)
        {
            LTEBTS bts = inRegionSite as LTEBTS;

            //超高
            dealUltraHighSites(cellDisDic, bts);

            //超近(单验只有1个站需要判断,只判断本站和其他站是否超近)
            dealUltraNearSites(outRegionSites, cellDisDic, bts);
        }

        #region 超高
        private void dealUltraHighSites(Dictionary<ICell, UltraFarSite> cellDisDic, LTEBTS bts)
        {
            foreach (LTECell cell in bts.LatestCells)
            {
                if (ultraSiteCondition.IsTooHighSite(cell))
                {
                    addUltraHighSite(cellDisDic, cell);
                }
            }
        }

        private void addUltraHighSite(Dictionary<ICell, UltraFarSite> cellDisDic, LTECell cell)
        {
            UltraHighSite hSite = new UltraHighSite(cell);
            hSite.DistanceByDelaunay = cellDisDic[cell].DistanceByDelaunay;
            hSite.DistanceByDir = cellDisDic[cell].DistanceByDir;
            List<UltraSiteCell> ultraSet = null;
            if (!cellDic.TryGetValue(cell, out ultraSet))
            {
                ultraSet = new List<UltraSiteCell>();
                cellDic.Add(cell, ultraSet);
            }
            ultraSet.Add(hSite);
        }
        #endregion

        #region 超近
        private void dealUltraNearSites(List<ISite> outRegionSites, Dictionary<ICell, UltraFarSite> cellDisDic, LTEBTS bts)
        {
            foreach (ISite other in outRegionSites)
            {
                LTEBTS otherBts = other as LTEBTS;
                double dis = 0;
                if (ultraSiteCondition.IsTooNearSite(bts, otherBts, out dis))
                {
                    foreach (LTECell cell in bts.LatestCells)
                    {
                        addValidBts(cellDisDic, otherBts, bts, dis);
                    }
                }
            }
        }

        private void addValidBts(Dictionary<ICell, UltraFarSite> cellDisDic, LTEBTS bts, LTEBTS judgedBts, double dis)
        {
            foreach (LTECell cell in judgedBts.LatestCells)
            {
                bool diffBand = false;
                foreach (LTECell tmp in bts.LatestCells)
                {
                    if (tmp.BandType != cell.BandType)
                    {
                        diffBand = true;
                        break;
                    }
                }
                if (diffBand && dis <= ultraSiteCondition.DiffBandDistanceMin)
                {//异频，且距离很近有可能是共站情况，过滤。
                    continue;
                }

                addUltraNearSite(cellDisDic, bts, dis, cell);
            }
        }

        private void addUltraNearSite(Dictionary<ICell, UltraFarSite> cellDisDic, ISite site, double dis, LTECell cell)
        {
            UltraNearSite nSite = new UltraNearSite(cell, site, dis);
            nSite.DistanceByDelaunay = cellDisDic[cell].DistanceByDelaunay;
            nSite.DistanceByDir = cellDisDic[cell].DistanceByDir;
            List<UltraSiteCell> ultraSet = null;
            if (!cellDic.TryGetValue(cell, out ultraSet))
            {
                ultraSet = new List<UltraSiteCell>();
                cellDic.Add(cell, ultraSet);
            }
            ultraSet.Add(nSite);
        }
        #endregion



    }
}
