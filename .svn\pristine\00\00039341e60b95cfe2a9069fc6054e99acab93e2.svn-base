﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTCheckCellOccupyByRegion_NB : ZTCheckCellOccupyByRegion
    {
        public ZTCheckCellOccupyByRegion_NB(ServiceName serviceName)
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        public override string Name
        {
            get { return "占用核查(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34018, this.Name);
        }
    }

    class ZTCheckCellOccupyByFile_NB : ZTCheckCellOccupyByFile
    {
        public ZTCheckCellOccupyByFile_NB(ServiceName serviceName)
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        public override string Name
        {
            get { return "占用核查(按文件)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34019, this.Name);
        }
    }
}
