﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.RAMS.Stat
{
    public class NoGisBatExpItem : IComparable<NoGisBatExpItem>
    {
        public override string ToString()
        {
            return Name;
        }
        public string Name { get; set; }
        public bool IsCheck { get; set; } = false;
        public List<string> ReportNames { get; set; } = new List<string>();
        public List<int> DistrictIDs { get; set; } = new List<int>();
        public Dictionary<int, List<int>> AreaTypeIDDic { get; set; } = new Dictionary<int, List<int>>();
        public List<int> ProjectIDs { get; set; } = new List<int>();
        public List<int> ServiceIDs { get; set; } = new List<int>();
        public List<int> AgentIDs { get; set; } = new List<int>();
        public bool CheckFileName { get; set; } = false;
        public string FileNameFilter { get; set; }
        public bool IsMergeData { get; set; } = true;
        public int QueryFunc { get; set; } = 0;
        public string FileName { get; set; } = string.Empty;

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param.Add("Name", Name);
                param.Add("IsCheck", IsCheck);
                param.Add("ReportNames", ReportNames);
                param.Add("DistrictIDs", DistrictIDs);
                param.Add("ProjectIDs", ProjectIDs);
                param.Add("ServiceIDs", ServiceIDs);
                param.Add("AgentIDs", AgentIDs);
                param.Add("AreaTypeIDDic", AreaTypeIDDic);
                param.Add("CheckFileName", CheckFileName);
                param.Add("FileNameFilter", FileNameFilter);
                param.Add("IsMergeData", IsMergeData);
                param.Add("QueryFunc", QueryFunc);
                param.Add("FileName", FileName);
                return param;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                setValue(value);
            }
        }

        private void setValue(Dictionary<string, object> value)
        {
            if (value.ContainsKey("CheckFileName"))
            {
                CheckFileName = (bool)value["CheckFileName"];
            }
            if (value.ContainsKey("FileNameFilter"))
            {
                FileNameFilter = value["FileNameFilter"] as string;
            }

            if (value.ContainsKey("Name"))
            {
                Name = value["Name"] as string;
            }
            if (value.ContainsKey("IsCheck"))
            {
                IsCheck = (bool)value["IsCheck"];
            }
            if (value.ContainsKey("IsMergeData"))
            {
                IsMergeData = (bool)value["IsMergeData"];
            }
            if (value.ContainsKey("QueryFunc"))
            {
                QueryFunc = (int)value["QueryFunc"];
            }
            if (value.ContainsKey("FileName"))
            {
                string str = (string)value["FileName"];
                if (str == null || str.IndexOf("(区域名)") < 0 || str.IndexOf("(报表名)") < 0)
                    FileName = "(区域名)_(报表名)";
                else
                    FileName = str;
            }

            ReportNames = getStringInfo(value, "ReportNames", ReportNames);
            DistrictIDs = getIDsInfo(value, "DistrictIDs", DistrictIDs);
            ProjectIDs = getIDsInfo(value, "ProjectIDs", ProjectIDs);
            ServiceIDs = getIDsInfo(value, "ServiceIDs", ServiceIDs);
            AgentIDs = getIDsInfo(value, "AgentIDs", AgentIDs);
            setAreaTypeIDDic(value);
        }

        private List<string> getStringInfo(Dictionary<string, object> value, string name, List<string> ids)
        {
            if (value.ContainsKey(name))
            {
                if (value[name] is List<string>)
                {
                    ids = value[name] as List<string>;
                }
                else
                {
                    List<object> list = value[name] as List<object>;
                    foreach (object obj in list)
                    {
                        if (obj != null)
                        {
                            ids.Add(obj.ToString());
                        }
                    }
                }
            }
            return ids;
        }

        private List<int> getIDsInfo(Dictionary<string, object> value, string name, List<int> ids)
        {
            if (value.ContainsKey(name))
            {
                if (value[name] is List<int>)
                {
                    ids = value[name] as List<int>;
                }
                else
                {
                    List<object> list = value[name] as List<object>;
                    foreach (object obj in list)
                    {
                        if (obj != null)
                        {
                            ids.Add(int.Parse(obj.ToString()));
                        }
                    }
                }
            }
            return ids;
        }

        private void setAreaTypeIDDic(Dictionary<string, object> value)
        {
            if (value.ContainsKey("AreaTypeIDDic"))
            {
                if (value["AreaTypeIDDic"] is Dictionary<int, List<int>>)
                {
                    this.AreaTypeIDDic = value["AreaTypeIDDic"] as Dictionary<int, List<int>>;
                }
                else
                {
                    addAreaTypeIDDic(value);
                }
            }
        }

        private void addAreaTypeIDDic(Dictionary<string, object> value)
        {
            Dictionary<string, object> dic = value["AreaTypeIDDic"] as Dictionary<string, object>;
            foreach (string areaType in dic.Keys)
            {
                List<int> ids = new List<int>();
                this.AreaTypeIDDic[int.Parse(areaType)] = ids;
                foreach (object id in (dic[areaType] as List<object>))
                {
                    if (id != null)
                    {
                        ids.Add(int.Parse(id.ToString()));
                    }
                }
            }
        }

        #region IComparable<NoGisOneKeyExpItem> 成员

        public int CompareTo(NoGisBatExpItem other)
        {
            return this.Name.CompareTo(other.Name);
        }

        #endregion
    }

    public class NoGisBatExpCfgMngr
    {
        private readonly string cfgFileName = Application.StartupPath + "\\config\\templates\\BatExpNoGisOptions.xml";
        public string ExportFolderPath { get; set; } = Environment.CurrentDirectory;
        public List<NoGisBatExpItem> Items { get; set; }
        private static NoGisBatExpCfgMngr instance = null;
        public static NoGisBatExpCfgMngr Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new NoGisBatExpCfgMngr();
                }
                return instance;
            }
        }
        private NoGisBatExpCfgMngr()
        {
            Load();
        }

        public void Add(NoGisBatExpItem item)
        {
            Items.Add(item);
            Save();
        }

        public void Load()
        {
            Items = new List<NoGisBatExpItem>();
            if (!File.Exists(cfgFileName))
            {
                return;
            }

            XmlConfigFile configFile = new XmlConfigFile(cfgFileName);
            object obj = configFile.GetItemValue("Config", "SavePath");
            if (obj != null)
            {
                ExportFolderPath = obj.ToString();
            }
            List<object> fsLst = configFile.GetItemValue("Config", "Items") as List<object>;
            foreach (object o in fsLst)
            {
                NoGisBatExpItem fs = new NoGisBatExpItem();
                fs.Param = o as Dictionary<string, object>;
                Items.Add(fs);
            }
            Items.Sort();
        }

        public void Save()
        {
            List<object> objs = new List<object>();
            foreach (NoGisBatExpItem fs in Items)
            {
                objs.Add(fs.Param);
            }
            XmlConfigFile configFile = new XmlConfigFile();
            XmlElement config = configFile.AddConfig("Config");
            configFile.AddItem(config, "SavePath", ExportFolderPath);
            configFile.AddItem(config, "Items", objs);
            configFile.Save(cfgFileName);
        }

    }

}
