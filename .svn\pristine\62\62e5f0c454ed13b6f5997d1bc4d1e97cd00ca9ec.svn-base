﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LtePlanningExcelWarningForm : BaseDialog
    {
        public LtePlanningExcelWarningForm(string title, List<string> warnings)
        {
            InitializeComponent();
            btnContinue.Click += BtnContinue_Click;
            btnAbort.Click += BtnAbort_Click;

            labelTitle.Text = title;
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < warnings.Count; ++i)
            {
                sb.Append(warnings[i]);
                if (i < warnings.Count - 1)
                {
                    sb.Append(Environment.NewLine);
                }
            }
            txtWarning.Text = sb.ToString();
        }

        private void BtnContinue_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void BtnAbort_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Abort;
        }
    }
}
