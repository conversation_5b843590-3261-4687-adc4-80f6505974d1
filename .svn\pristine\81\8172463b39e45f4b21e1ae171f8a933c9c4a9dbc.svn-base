﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Text;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.Func
{
    public class PercentRoadItem
    {
        public List<TestPoint> TestPoints
        {
            get;
            set;
        }
        public List<bool> ValidStatus
        {
            get;
            set;
        }
        public double Duration
        {
            get;
            set;
        }
        public double Length
        {
            get;
            set;
        }
        public int ValidCount
        {
            get;
            set;
        }
        public int InvalidCount
        {
            get;
            set;
        }
        public int Count
        {
            get { return ValidCount + InvalidCount; }
        }
        public double ValidPercent
        {
            get { return Count == 0 ? 0 : 1d * ValidCount / Count; }
        }
        public double InvalidPercent
        {
            get { return Count == 0 ? 0 : 1d * InvalidCount / Count; }
        }

        public PercentRoadItem()
        {
            TestPoints = new List<TestPoint>();
            ValidStatus = new List<bool>();
        }

        public string NetType { set; get; }
    }

    // 道路被生成的时候使用该回调函数通知调用方
    public delegate void PercentRoadCompleteHandler(object sender, PercentRoadItem roadItem);

    // 生成道路条件
    public class PercentRoadCondition
    {
        public bool IsCheckDistanceGap { get; set; } 
        public bool IsCheckTimeGap { get; set; } 
        public bool IsCheckMinLength { get; set; } 
        public bool IsCheckMaxLength { get; set; } = false;
        public bool IsCheckDuration { get; set; } 

        public double MaxTimeGap { get; set; } 
        public double MaxDistanceGap { get; set; } 
        public double MinLength { get; set; } 
        public double MaxLength { get; set; } 
        public double MinDuration { get; set; } 

        public double MinValidPercent { get; set; } 
        public PercentRoadCompleteHandler Completer { get; set; } 

        public PercentRoadCondition(double minValidPercent, PercentRoadCompleteHandler completer)
        {
            this.MinValidPercent = minValidPercent;
            this.Completer = completer;

            this.IsCheckTimeGap = false;
            this.IsCheckDistanceGap = true;
            this.IsCheckDuration = true;
            this.IsCheckMinLength = false;

            this.MaxTimeGap = 50;
            this.MaxDistanceGap = 20;
            this.MinLength = 50;
            this.MinDuration = 10;
        }
    }

    // 道路构建器
    public class PercentRoadBuilder
    {
        public PercentRoadBuilder(PercentRoadCondition roadCond)
        {
            this.RoadCond = roadCond;
            this.roadWindow = new PercentRoadWindow(roadCond);
        }

        public void AddPoint(TestPoint tp, bool isValid)
        {
            PercentRoadPoint curPoint = new PercentRoadPoint(tp, isValid);
            while (!roadWindow.TryMove(curPoint))
            {
                ReportRoad();
            }
        }

        public void AddPoint(TestPoint tp, bool isValid, string netType)
        {
            PercentRoadPoint curPoint = new PercentRoadPoint(tp, isValid);
            while (!roadWindow.TryMove(curPoint))
            {
                ReportRoad(netType);
            }
        }

        // 文件的末尾，没有更多采样点的时候调用
        public void StopRoading()
        {
            roadWindow.StopMove();
            ReportRoad();
            roadWindow.ResetWindow();
        }


        public void StopRoading(string netType)
        {
            roadWindow.StopMove();
            ReportRoad(netType);
            roadWindow.ResetWindow();
        }

        private void ReportRoad()
        {
            if (roadWindow.RoadPoints.Count == 0)
            {
                return;
            }

            PercentRoadItem roadItem = new PercentRoadItem();
            foreach (PercentRoadPoint rpt in roadWindow.RoadPoints)
            {
                roadItem.TestPoints.Add(rpt.TestPoint);
                roadItem.ValidStatus.Add(rpt.IsValid);
                roadItem.ValidCount += rpt.IsValid ? 1 : 0;
                roadItem.InvalidCount += rpt.IsValid ? 0 : 1;
            }
            roadItem.Length = roadWindow.RoadLength;
            roadItem.ValidCount = roadWindow.RoadValidCount;
            roadItem.Duration = roadWindow.RoadDuration;
            RoadCond.Completer(this, roadItem);

            roadWindow.ResetRoad();
        }

        private void ReportRoad(string netType)
        {
            if (roadWindow.RoadPoints.Count == 0)
            {
                return;
            }

            PercentRoadItem roadItem = new PercentRoadItem();
            foreach (PercentRoadPoint rpt in roadWindow.RoadPoints)
            {
                roadItem.TestPoints.Add(rpt.TestPoint);
                roadItem.ValidStatus.Add(rpt.IsValid);
                roadItem.ValidCount += rpt.IsValid ? 1 : 0;
                roadItem.InvalidCount += rpt.IsValid ? 0 : 1;
            }
            roadItem.Length = roadWindow.RoadLength;
            roadItem.ValidCount = roadWindow.RoadValidCount;
            roadItem.Duration = roadWindow.RoadDuration;
            roadItem.NetType = netType;
            RoadCond.Completer(this, roadItem);

            roadWindow.ResetRoad();
        }

        private readonly PercentRoadWindow roadWindow;
        public PercentRoadCondition RoadCond { get; set; }

        #region 私有嵌套类
        private class PercentRoadPoint
        {
            public TestPoint TestPoint
            {
                get { return tp; }
            }

            public double Longitude
            {
                get { return tp.Longitude; }
            }

            public double Latitude
            {
                get { return tp.Latitude; }
            }

            public bool IsValid
            {
                get { return isValid; }
            }

            public PercentRoadPoint(TestPoint tp, bool isValid)
            {
                this.tp = tp;
                this.isValid = isValid;
            }

            private readonly TestPoint tp;
            private readonly bool isValid;
        }

        // 总处于不满足持续条件(时间或长度)状态的移动窗口
        private class PercentRoadWindow
        {
            // 窗口在移动过程中生成的道路
            // 在TryMove失败或者Close之后需立即进行处理
            public ReadOnlyCollection<PercentRoadPoint> RoadPoints
            {
                get { return roadPoints.AsReadOnly(); }
            }

            // 道路持续时间
            public double RoadDuration
            {
                get;
                private set;
            }

            // 道路持续长度
            public double RoadLength
            {
                get;
                private set;
            }

            // 道路持续有效点个数
            public int RoadValidCount
            {
                get;
                private set;
            }

            public PercentRoadWindow(PercentRoadCondition roadCond)
            {
                this.roadCond = roadCond;
                this.roadPoints = new List<PercentRoadPoint>();
            }

            // 窗口尝试前移一个点
            public bool TryMove(PercentRoadPoint rpt)
            {
                double timeGap, distanceGap;

                // 不满足间隔条件，结束道路生成
                if (!IsValidGap(rpt, out timeGap, out distanceGap))
                {
                    if (roadPoints.Count > 0) // 窗口内的点属于已生成的道路
                    {
                        DequeueValidWindow();
                    }
                    else
                    {
                        DequeueInvalidWindow(); // 未曾有道路生成，窗口内的点都可丢弃
                    }
                    return false;
                }

                // 如果前移该点导致窗口不满足百分比条件，结束道路生成
                if (!IsValidPercent(rpt, timeGap, distanceGap))
                {
                    if (roadPoints.Count > 0) // 窗口内的点属于已生成的道路
                    {
                        DequeueValidWindow();
                    }
                    else
                    {
                        DequeueInvalidPoint(); // 未曾有道路生成，将窗口后方前移一个点
                    }
                    return false;
                }

                // 窗口前方前移一个点，后方前移直到窗口再次不满足持续条件
                EnqueuePoint(rpt, timeGap, distanceGap);
                while (IsWindowFull())
                {
                    DequeueValidPoint(); // 后方前移的点加入到道路中
                }

                return true;
            }

            // 在没有更多的点需要前移窗口时调用
            public void StopMove()
            {
                if (roadPoints.Count > 0)
                {
                    DequeueValidWindow();
                }
                else
                {
                    DequeueInvalidWindow();
                }
            }

            // 清理窗口
            public void ResetWindow()
            {
                DequeueInvalidWindow();
            }

            // 重置窗口移动过程中生成的道路
            public void ResetRoad()
            {
                roadPoints.Clear();
                RoadDuration = RoadLength = 0;
                RoadValidCount = 0;
            }

            // 判断窗口是否满足持续条件
            private bool IsWindowFull()
            {
                if (pointLst.Count == 0)
                {
                    return false;
                }
                if (roadCond.IsCheckDuration && winDuration < roadCond.MinDuration)
                {
                    return false;
                }
                if (roadCond.IsCheckMinLength && winLength < roadCond.MinLength)
                {
                    return false;
                }
                return true;
            }

            // 判断一个尝试前移的点与窗口当前最后一个点的间隔是否满足
            private bool IsValidGap(PercentRoadPoint curPoint, out double timeGap, out double distanceGap)
            {
                if (pointLst.Count == 0)
                {
                    timeGap = distanceGap = 0;
                    return true;
                }

                PercentRoadPoint lastPoint = pointLst.Last.Value;
                timeGap = Math.Abs(1.0 * (lastPoint.TestPoint.lTimeWithMillsecond - curPoint.TestPoint.lTimeWithMillsecond) / 1000);
                distanceGap = MathFuncs.GetDistance(curPoint.Longitude, curPoint.Latitude, lastPoint.Longitude, lastPoint.Latitude);
                if (roadCond.IsCheckDistanceGap && distanceGap > roadCond.MaxDistanceGap)
                {
                    return false;
                }
                if (roadCond.IsCheckTimeGap && timeGap > roadCond.MaxTimeGap)
                {
                    return false;
                }
                return true;
            }

            // 判断前移改点后窗口是否满足百分比
            private bool IsValidPercent(PercentRoadPoint tryPoint, double timeGap, double distanceGap)
            {
                if (roadCond.IsCheckDuration && RoadDuration + winDuration + timeGap < roadCond.MinDuration)
                {
                    return true;
                }
                if (roadCond.IsCheckMinLength && RoadLength + winLength + distanceGap < roadCond.MinLength)
                {
                    return true;
                }
                if (roadCond.IsCheckMaxLength && RoadLength + winLength + distanceGap > roadCond.MaxLength)
                {
                    return false;
                }

                int tryValidCount = winValidCount + (tryPoint.IsValid ? 1 : 0);
                double tryValidPercent = 1d * tryValidCount / (pointLst.Count + 1);
                double validPercent = 1d * (RoadValidCount + tryValidCount) / (roadPoints.Count + pointLst.Count + 1);

                return validPercent >= roadCond.MinValidPercent && tryValidPercent >= roadCond.MinValidPercent;
            }

            // 将窗口前移到指定点，即将指定点加入窗口前方（移动方向）
            private void EnqueuePoint(PercentRoadPoint curPoint, double timeGap, double distanceGap)
            {
                winDuration += timeGap;
                winLength += distanceGap;
                winValidCount += curPoint.IsValid ? 1 : 0;
                pointLst.AddLast(curPoint);
                if (pointLst.Count > 1)
                {
                    timeGapLst.AddLast(timeGap);
                    distanceGapLst.AddLast(distanceGap);
                }
            }

            // 窗口后方前移一个点，被移出的点不需要加入到道路中
            private void DequeueInvalidPoint()
            {
                PercentRoadPoint firstPoint = pointLst.First.Value;
                winValidCount -= firstPoint.IsValid ? 1 : 0;
                pointLst.RemoveFirst();

                if (timeGapLst.Count > 0)
                {
                    winDuration -= timeGapLst.First.Value;
                    timeGapLst.RemoveFirst();
                }
                if (distanceGapLst.Count > 0)
                {
                    winLength -= distanceGapLst.First.Value;
                    distanceGapLst.RemoveFirst();
                }
            }

            // 窗口后方前移一个点，被移出的点需要加入到道路中
            private void DequeueValidPoint()
            {
                if (pointLst.Count == 0)
                {
                    return;
                }

                PercentRoadPoint firstPoint = pointLst.First.Value;
                winValidCount -= firstPoint.IsValid ? 1 : 0;
                RoadValidCount += firstPoint.IsValid ? 1 : 0;
                roadPoints.Add(firstPoint);
                pointLst.RemoveFirst();

                if (timeGapLst.Count > 0)
                {
                    double firstTimeGap = timeGapLst.First.Value;
                    winDuration -= firstTimeGap;
                    RoadDuration += firstTimeGap;
                    timeGapLst.RemoveFirst();
                }
                if (distanceGapLst.Count > 0)
                {
                    double firstDistanceGap = distanceGapLst.First.Value;
                    winLength -= firstDistanceGap;
                    RoadLength += firstDistanceGap;
                    distanceGapLst.RemoveFirst();
                }
            }

            // 将窗口后方缩小至0，被移出的点不需要加入到道路中
            private void DequeueInvalidWindow()
            {
                winDuration = winLength = 0;
                winValidCount = 0;
                pointLst.Clear();
                timeGapLst.Clear();
                distanceGapLst.Clear();
            }

            // 将窗口缩小缩小至0，被移出的点需要加入到道路中
            private void DequeueValidWindow()
            {
                roadPoints.AddRange(pointLst);
                RoadLength += winLength;
                RoadDuration += winDuration;
                RoadValidCount += winValidCount;

                winDuration = winLength = 0;
                winValidCount = 0;
                pointLst.Clear();
                timeGapLst.Clear();
                distanceGapLst.Clear();
            }

            private double winDuration;
            private double winLength;
            private int winValidCount;
            private readonly LinkedList<PercentRoadPoint> pointLst = new LinkedList<PercentRoadPoint>();
            private readonly LinkedList<double> timeGapLst = new LinkedList<double>();
            private readonly LinkedList<double> distanceGapLst = new LinkedList<double>();
            private readonly PercentRoadCondition roadCond;
            private readonly List<PercentRoadPoint> roadPoints;
        }
        #endregion
    }
}
