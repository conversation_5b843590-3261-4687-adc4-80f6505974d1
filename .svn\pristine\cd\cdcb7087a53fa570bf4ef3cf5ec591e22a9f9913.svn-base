﻿using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Chris.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLTECaculateEarfcnCoverage : DIYAnalyseByFileBackgroundBase
    {
        private static ZTLTECaculateEarfcnCoverage instance = null;
        private static readonly object lockObj = new object();
        public static ZTLTECaculateEarfcnCoverage GetInstance()
        {
            lock (lockObj)
            {
                if (instance == null)
                {
                    instance = new ZTLTECaculateEarfcnCoverage();
                }
                return instance;
            }
        }
 
        protected ZTLTECaculateEarfcnCoverage()
            : base(MainModel.GetInstance())
        {
            this.FilterSampleByRegion = true;
            this.IncludeEvent = false;
        }

        public override string Name
        {
            get { return "LTE频段覆盖占比"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22126, this.Name);
        }

        private LTEEarfcnCoverageSettingBox settingDlg = null;

        protected override bool getCondition()
        {
            if(settingDlg==null)
            {
                settingDlg = new LTEEarfcnCoverageSettingBox();
            }
            if(settingDlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            return true;
        }

        private EarfcnCoverageResult earfcnCoverageResult = null;

        protected override void getReadyBeforeQuery()
        {
            if (condition.Geometorys.SelectedResvRegions != null && condition.Geometorys.SelectedResvRegions.Count > 0)
            {
                earfcnCoverageResult = new EarfcnCoverageResult(condition.Geometorys.SelectedResvRegions, settingDlg.RsrpRangeValues,settingDlg.EarfcnRangeValue);
            }
            else
            {
                ResvRegion region = new ResvRegion();
                region.RegionName = "当前选择区域";
                List<ResvRegion> regionList = new List<ResvRegion>();
                regionList.Add(region);
                earfcnCoverageResult = new EarfcnCoverageResult(regionList, settingDlg.RsrpRangeValues, settingDlg.EarfcnRangeValue);
            }
        }

        protected override void doStatWithQuery()
        {
            foreach(DTFileDataManager fileDataManager in MainModel.GetInstance().DTDataManager.FileDataManagers)
            {
                earfcnCoverageResult.DistributionPointByRegion(fileDataManager.TestPoints);
            }
        }

        protected override void getResultsAfterQuery()
        {
            earfcnCoverageResult.CaculatePercent();
        }

        protected override void fireShowForm()
        {
            if (earfcnCoverageResult.RegionInfoDic.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            RsrpEarfcnForm frm = MainModel.GetInstance().CreateResultForm(typeof(RsrpEarfcnForm)) as RsrpEarfcnForm;
            frm.Owner = MainModel.MainForm;
            frm.FillData(earfcnCoverageResult);
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override void releaseSource()
        {
            earfcnCoverageResult = null;
        }
    }

    public class EarfcnCoverageResult
    {
        public EarfcnCoverageRegionInfo AllRegionSummary { get; set; }
        private readonly EarfcnRangeValue earfcnRangeValue;
        public Dictionary<string, EarfcnCoverageRegionInfo> RegionInfoDic { get; } = new Dictionary<string, EarfcnCoverageRegionInfo>();

        public EarfcnCoverageResult(List<ResvRegion> resvRegions, IList<Range> rsrpRangeValues, EarfcnRangeValue earfcnRangeValue)
        {
            this.resvRegions = resvRegions;
            this.rsrpRangeValues = rsrpRangeValues;
            this.earfcnRangeValue = earfcnRangeValue;
            init();
            if(this.resvRegions.Count > 1)
            {
                ResvRegion region = new ResvRegion();
                region.RegionName = "汇总";
                AllRegionSummary = new EarfcnCoverageRegionInfo(region, rsrpRangeValues, earfcnRangeValue);
            }
        }

        private void init()
        {
            foreach(ResvRegion region in resvRegions)
            {
                EarfcnCoverageRegionInfo regionInfo = new EarfcnCoverageRegionInfo(region, this.rsrpRangeValues, earfcnRangeValue);
                RegionInfoDic.Add(region.RegionName, regionInfo);
            }
        }

        
        public void DistributionPointByRegion(List<TestPoint> testPoints)
        {
            if(resvRegions.Count <= 1)
            {
                foreach(TestPoint point in testPoints)
                {
                    RegionInfoDic[resvRegions[0].RegionName].DistributionByRsrpRange(point);
                }
            }
            else
            {
                foreach(TestPoint point in testPoints)
                {
                    foreach(ResvRegion region in resvRegions)
                    {
                        if(region.GeoOp.CheckPointInRegion(point.Longitude, point.Latitude))
                        {
                            RegionInfoDic[region.RegionName].DistributionByRsrpRange(point);
                        }
                    }
                }
            }
        }

        public void CaculatePercent()
        {
            foreach(EarfcnCoverageRegionInfo regionInfo in RegionInfoDic.Values)
            {
                regionInfo.CaculatePercent();
            }
            CaculateRegionSummary();
        }

        public void CaculateRegionSummary()
        {
            if(resvRegions.Count <= 1)
            {
                return;
            }
            foreach (Range range in rsrpRangeValues)
            {
                string rangeDescribe = range.ToString();
                foreach (var regionInfo in RegionInfoDic.Values)
                {
                    AllRegionSummary.RangeItemInfo[rangeDescribe].EarfcnDCount += regionInfo.RangeItemInfo[rangeDescribe].EarfcnDCount;
                    AllRegionSummary.RangeItemInfo[rangeDescribe].EarfcnECount += regionInfo.RangeItemInfo[rangeDescribe].EarfcnECount;
                    AllRegionSummary.RangeItemInfo[rangeDescribe].EarfcnFCount += regionInfo.RangeItemInfo[rangeDescribe].EarfcnFCount;
                    AllRegionSummary.RangeItemInfo[rangeDescribe].EarfcnFDD_900Count += regionInfo.RangeItemInfo[rangeDescribe].EarfcnFDD_900Count;
                    AllRegionSummary.RangeItemInfo[rangeDescribe].EarfcnFDD_1800Count += regionInfo.RangeItemInfo[rangeDescribe].EarfcnFDD_1800Count;
                    AllRegionSummary.RangeItemInfo[rangeDescribe].TddPointCount += regionInfo.RangeItemInfo[rangeDescribe].TddPointCount;
                    AllRegionSummary.RangeItemInfo[rangeDescribe].FddPointCount += regionInfo.RangeItemInfo[rangeDescribe].FddPointCount;
                }
            }
            AllRegionSummary.CaculatePercent();
        }

        private readonly IList<Range> rsrpRangeValues;
        private readonly List<ResvRegion> resvRegions;
    }

    public class EarfcnCoverageRegionInfo
    {
        public Dictionary<string, EarfcnCoverageRangeItem> RangeItemInfo { set; get; } = new Dictionary<string, EarfcnCoverageRangeItem>();
        private readonly EarfcnRangeValue earfcnRangeValue;

        public EarfcnCoverageRegionInfo(ResvRegion region, IList<Range> rangeValues, EarfcnRangeValue earfcnRangeValue)
        {
            this.rsrpRangeValues = rangeValues;
            this.earfcnRangeValue = earfcnRangeValue;
            this.region = region;
            init();
        }

        private void init()
        {
            foreach(Range range in this.rsrpRangeValues)
            {
                EarfcnCoverageRangeItem rangeInfo = new EarfcnCoverageRangeItem(range, region.RegionName, earfcnRangeValue);
                RangeItemInfo.Add(range.ToString(), rangeInfo);
            }         
        }

        public void DistributionByRsrpRange(TestPoint point)
        {
            object obj = getRSRP(point);
            if (obj != null)
            {
                float rsrp = (float)obj;
                foreach (Range range in rsrpRangeValues)
                {
                    if(range.Contains(rsrp))
                    {
                        RangeItemInfo[range.ToString()].ADDEarfcnNumByRsrpRange(point);
                    }
                }
            }
        }

        public void CaculatePercent()
        {
            foreach(EarfcnCoverageRangeItem rangeInfo in RangeItemInfo.Values)
            {
                rangeInfo.CaculatePercent();
            }
        }

        private object getRSRP(TestPoint point)
        {
            if(point is LTETestPointDetail)
            {
                return point["lte_RSRP"];
            }
            else if(point is LTEFddTestPoint)
            {
                return point["lte_fdd_RSRP"];
            }
            return null;
        }

        private readonly ResvRegion region;
        private readonly IList<Range> rsrpRangeValues;
    }

    public class EarfcnCoverageRangeItem
    {
        private readonly EarfcnRangeValue earfcnRangeValue;

        public EarfcnCoverageRangeItem(Range rsrpRangeValue, string regionName, EarfcnRangeValue earfcnRangeValue)
        {
            this.rsrpRangeValue = rsrpRangeValue;
            this.earfcnRangeValue = earfcnRangeValue;
            this.RsrpRangeName = rsrpRangeValue.ToString();
            this.RegionName = regionName;
        }

        private Range rsrpRangeValue { set; get; }

        public void CaculatePercent()
        {
            if (TddPointCount != 0)
            {
                PercentEarfcnD = Math.Round((double)EarfcnDCount / TddPointCount, 4);
                PercentEarfcnE = Math.Round((double)EarfcnECount / TddPointCount, 4);
                PercentEarfcnF = Math.Round((double)EarfcnFCount / TddPointCount, 4);
            }
            if(FddPointCount != 0)
            {
                PercentEarfcnFDD_900 = Math.Round((double)EarfcnFDD_900Count / FddPointCount, 4);
                PercentEarfcnFDD_1800 = Math.Round((double)EarfcnFDD_1800Count / FddPointCount, 4);
            }
        }

        public void ADDEarfcnNumByRsrpRange(TestPoint point)
        {
            object obj = getEarfcn(point);
            if(obj != null)
            {
                int earfcn = (int)obj;
                if(earfcnRangeValue.EarfcnD.Contains(earfcn))
                {
                    EarfcnDCount++;
                    TddPointCount++;
                }
                else if(earfcnRangeValue.EarfcnE.Contains(earfcn))
                {
                    EarfcnECount++;
                    TddPointCount++;
                }
                else if (earfcnRangeValue.EarfcnF.Contains(earfcn))
                {
                    EarfcnFCount++;
                    TddPointCount++;
                }
                else if (earfcnRangeValue.EarfcnFDD_900.Contains(earfcn))
                {
                    EarfcnFDD_900Count++;
                    FddPointCount++;
                }
                else if (earfcnRangeValue.EarfcnFDD_1800.Contains(earfcn))
                {
                    EarfcnFDD_1800Count++;
                    FddPointCount++;
                }
            }
        }

        public int EarfcnDCount { get; set; }
        public int EarfcnECount { get;  set; }
        public int EarfcnFCount { get;  set; }
        public int EarfcnFDD_900Count {  set; get; }
        public int EarfcnFDD_1800Count {  set; get; }
        public int TddPointCount {  set; get; }
        public int FddPointCount {  set; get; }
        public int PointCount
        {
            get
            {
                return FddPointCount + TddPointCount;
            }
        }
        public double PercentEarfcnD { get; set; }
        public double PercentEarfcnE { get; set; }
        public double PercentEarfcnF { get; set; }
        public double PercentEarfcnFDD_900 { set; get; }
        public double PercentEarfcnFDD_1800 { set; get; }

        private object getEarfcn(TestPoint point)
        {
            if(point is LTETestPointDetail)
            {
                return point["lte_EARFCN"];
            }
            else if(point is LTEFddTestPoint)
            {
                return point["lte_fdd_EARFCN"];
            }
            return null;
        }

        public string RsrpRangeName { get; set; }
        public string RegionName { get; set; }
    }
}
