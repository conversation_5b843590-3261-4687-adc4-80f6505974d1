﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class DiyQueryZTRoadGridArchiveBase : DIYSQLBase
    {
        protected DiyQueryZTRoadGridArchiveBase()
            : base(MainModel.GetInstance())
        {

        }

        protected ZTRoadGridArchiveCondition settingCondition;

        public virtual void SetCondition(ZTRoadGridArchiveCondition settingCondition)
        {
            this.settingCondition = settingCondition;
        }

        public override string IconName
        {
            get { return string.Empty; }
        }

        #region 查询流程
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                queryInThread(clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                string strSql = getSqlTextString();
                getDataBySql(clientProxy, package, strSql);
            }
            catch (Exception ex)
            {
                log.Error(ex.ToString());
            }
        }

        protected virtual void getDataBySql(ClientProxy clientProxy, Package package, string strSql)
        {
            package.Command = Command.DIYSearch;//枚举类型：DIY接口
            package.SubCommand = SubCommand.Request;//枚举类型：请求
            if (MainDB)
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
            }
            else
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
            }

            E_VType[] retArrDef = getSqlRetTypeArr();
            string[] strArr = strSql.Split(new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
            int curIdx = 0;
            while (curIdx < strArr.Length)//分批次发送包
            {
                curIdx = addPackageContent(package, retArrDef, strArr, curIdx);
                clientProxy.Send();
                System.Threading.Thread.Sleep(300);
                receiveRetData(clientProxy);
            }
        }

        protected int addPackageContent(Package package, E_VType[] retArrDef, string[] strArr, int curIdx)
        {
            StringBuilder strb = new StringBuilder();
            for (; curIdx < strArr.Length; curIdx++)
            {
                string curStr = strArr[curIdx];
                int bufferLength = Encoding.Default.GetBytes(strb + curStr).Length;
                if (bufferLength > 7000)
                {
                    break;
                }
                strb.Append(curStr + ";");
            }
            package.Content.PrepareAddParam();
            package.Content.AddParam(strb.ToString());

            strb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    strb.Append((int)retArrDef[i]);
                    if (i < retArrDef.Length - 1)
                    {
                        strb.Append(",");
                    }
                }
            }
            package.Content.AddParam(strb.ToString());
            return curIdx;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;

            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        protected virtual void fillData(Package package)
        {
            //
        }
        #endregion
    }

    #region 查询采样点小区数据
    public class DiyQueryStatCellRoadRound : DiyQueryZTRoadGridArchiveBase
    {
        public List<StatCellRoadInfo> CurStatCellRoadResList { get; set; } = new List<StatCellRoadInfo>();
        public List<StatCellRoadInfo> HisStatCellRoadResList { get; set; } = new List<StatCellRoadInfo>();

        protected virtual string tableModelName { get; set; } = "tb_stat_cell_roadana_round_";

        protected List<string> curTableNameList = new List<string>();
        protected List<string> hisTableNameList = new List<string>();
        protected bool isQueryCur;

        public override void SetCondition(ZTRoadGridArchiveCondition settingCondition)
        {
            base.SetCondition(settingCondition);

            //传入轮次,根据轮次查询数据
            foreach (string round in settingCondition.CurRoundListStr)
            {
                string curRoundTableName = tableModelName + round;
                curTableNameList.Add(curRoundTableName);
            }

            foreach (string round in settingCondition.HisRoundListStr)
            {
                string hisRoundTableName = tableModelName + round;
                hisTableNameList.Add(hisRoundTableName);
            }
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                //第一次查询当前轮次或时段
                isQueryCur = true;
                string strSql = getSqlTextString();
                getDataBySql(clientProxy, package, strSql);

                //然后查询历史轮次或对比时段
                isQueryCur = false;
                strSql = getSqlTextString();
                getDataBySql(clientProxy, package, strSql);
            }
            catch (Exception ex)
            {
                log.Error(ex.ToString());
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder strSQL = new StringBuilder();
            if (isQueryCur)
            {
                foreach (string curTable in curTableNameList)
                {
                    strSQL.AppendFormat(@"select lac,ci,roadID,areaID,sampleNum from {0} where RoadID in ({1});", curTable, settingCondition.RoadListStr);
                }
            }
            else
            {
                foreach (string hisTable in hisTableNameList)
                {
                    strSQL.AppendFormat(@"select lac,ci,roadID,areaID,sampleNum from {0} where RoadID in ({1});", hisTable, settingCondition.RoadListStr);
                }
            }

            return strSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[5];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Int;
            return rType;
        }

        protected override void fillData(Package package)
        {
            StatCellRoadInfo info = new StatCellRoadInfo();
            info.LAC = package.Content.GetParamInt();
            info.CI = package.Content.GetParamInt();
            info.RoadID = package.Content.GetParamInt();
            info.AreaID = package.Content.GetParamInt();
            info.Times = package.Content.GetParamInt();

            setCellName(info);

            if (isQueryCur)
            {
                CurStatCellRoadResList.Add(info);
            }
            else
            {
                HisStatCellRoadResList.Add(info);
            }
        }

        private void setCellName(StatCellRoadInfo info)
        {
            ICell cell = MainModel.CellManager.GetCurrentLTECell(info.LAC, info.CI);
            if (cell != null)
            {
                info.CellName = cell.Name;
                return;
            }
            cell = MainModel.CellManager.GetCurrentCell(info.LAC, info.CI);
            if (cell != null)
            {
                info.CellName = cell.Name;
                return;
            }
            cell = MainModel.CellManager.GetCurrentTDCell(info.LAC, info.CI);
            if (cell != null)
            {
                info.CellName = cell.Name;
                return;
            }
            info.CellName = info.LAC + "_" + info.CI;
        }
    }

    public class DiyQueryStatCellRoadDD : DiyQueryStatCellRoadRound
    {
        protected override string tableModelName { get; set; } = "tb_stat_cell_roadana_dd_";

        public override void SetCondition(ZTRoadGridArchiveCondition settingCondition)
        {
            this.settingCondition = settingCondition;
            foreach (string dayStr in settingCondition.TimeByDayStrList)
            {
                string curTableName = tableModelName + dayStr;
                curTableNameList.Add(curTableName);
            }
            foreach (string dayStr in settingCondition.CompTimeByDayStrList)
            {
                string hisTableName = tableModelName + dayStr;
                hisTableNameList.Add(hisTableName);
            }
        }
    }

    public class DiyQueryStatCellRoadMM : DiyQueryStatCellRoadRound
    {
        protected override string tableModelName { get; set; } = "tb_stat_cell_roadana_mm_";

        public override void SetCondition(ZTRoadGridArchiveCondition settingCondition)
        {
            this.settingCondition = settingCondition;
            foreach (string monthStr in settingCondition.TimeByMonthStrList)
            {
                string curTableName = tableModelName + monthStr;
                curTableNameList.Add(curTableName);
            }
            foreach (string monthStr in settingCondition.CompTimeByMonthStrList)
            {
                string hisTableName = tableModelName + monthStr;
                hisTableNameList.Add(hisTableName);
            }
        }
    }
    #endregion

    #region 查询事件数据
    public class DiyQueryEventRoadRound : DiyQueryZTRoadGridArchiveBase
    {
        public List<EventRoadInfo> CurEventRoadResList { get; set; } = new List<EventRoadInfo>();

        protected virtual string tableModelName { get; set; } = "tb_event_roadana_round_";
        protected List<string> curTableNameList = new List<string>();

        public override void SetCondition(ZTRoadGridArchiveCondition settingCondition)
        {
            this.settingCondition = settingCondition;
            foreach (string round in settingCondition.CurRoundListStr)
            {
                string curRoundTableName = tableModelName + round;
                curTableNameList.Add(curRoundTableName);
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder strSQL = new StringBuilder();
            foreach (string curTable in curTableNameList)
            {
                strSQL.AppendFormat(@"select roadID,areaID,eventType,eventNum from {0} where RoadID in ({1});", curTable, settingCondition.RoadListStr);
            }
            return strSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[4];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_Int;
            return rType;
        }

        protected override void fillData(Package package)
        {
            EventRoadInfo info = new EventRoadInfo();
            info.RoadID = package.Content.GetParamInt();
            info.AreaID = package.Content.GetParamInt();
            info.EventType = package.Content.GetParamString();
            info.EventNum = package.Content.GetParamInt();
            CurEventRoadResList.Add(info);
        }
    }

    public class DiyQueryEventRoadDD : DiyQueryEventRoadRound
    {
        protected override string tableModelName { get; set; } = "tb_event_roadana_dd_";

        public override void SetCondition(ZTRoadGridArchiveCondition settingCondition)
        {
            this.settingCondition = settingCondition;
            foreach (string dayStr in settingCondition.TimeByDayStrList)
            {
                string curTableName = tableModelName + dayStr;
                curTableNameList.Add(curTableName);
            }
        }
    }

    public class DiyQueryEventRoadMM : DiyQueryEventRoadRound
    {
        protected override string tableModelName { get; set; } = "tb_event_roadana_mm_";

        public override void SetCondition(ZTRoadGridArchiveCondition settingCondition)
        {
            this.settingCondition = settingCondition;
            foreach (string monthStr in settingCondition.TimeByMonthStrList)
            {
                string curTableName = tableModelName + monthStr;
                curTableNameList.Add(curTableName);
            }
        }
    }
    #endregion

    #region 查询网络原因
    public class DiyQueryReasonRoadRound : DiyQueryEventRoadRound
    {
        protected override string tableModelName { get; set; } = "tb_netreason_roadana_round_";
        public List<ReasonRoadInfo> CurRoadReasonResList { get; set; } = new List<ReasonRoadInfo>();

        protected override string getSqlTextString()
        {
            StringBuilder strSQL = new StringBuilder();
            foreach (string curTable in curTableNameList)
            {
                strSQL.AppendFormat(@"if exists (select name from sysobjects where name like'{0}')select roadID,areaID,netreason,netreasonNum from {0} where RoadID in ({1});", curTable, settingCondition.RoadListStr);
            }
            return strSQL.ToString();
        }

        protected override void fillData(Package package)
        {
            ReasonRoadInfo info = new ReasonRoadInfo();
            info.RoadID = package.Content.GetParamInt();
            info.AreaID = package.Content.GetParamInt();
            info.Netreason = package.Content.GetParamString();
            info.NetreasonNum = package.Content.GetParamInt();
            CurRoadReasonResList.Add(info);
        }
    }

    public class DiyQueryReasonRoadDD : DiyQueryReasonRoadRound
    {
        protected override string tableModelName { get; set; } = "tb_netreason_roadana_dd_";

        public override void SetCondition(ZTRoadGridArchiveCondition settingCondition)
        {
            this.settingCondition = settingCondition;
            foreach (string dayStr in settingCondition.TimeByDayStrList)
            {
                string curTableName = tableModelName + dayStr;
                curTableNameList.Add(curTableName);
            }
        }
    }

    public class DiyQueryReasonRoadMM : DiyQueryReasonRoadRound
    {
        protected override string tableModelName { get; set; } = "tb_netreason_roadana_mm_";

        public override void SetCondition(ZTRoadGridArchiveCondition settingCondition)
        {
            this.settingCondition = settingCondition;
            foreach (string monthStr in settingCondition.TimeByMonthStrList)
            {
                string curTableName = tableModelName + monthStr;
                curTableNameList.Add(curTableName);
            }
        }
    }
    #endregion
}
