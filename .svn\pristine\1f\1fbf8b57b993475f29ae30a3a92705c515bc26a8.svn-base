﻿namespace MasterCom.RAMS.Func
{
    partial class ZTLeakOutCellTDScanSetForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.contextMenuStripListView = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBCCH = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBSIC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colLng = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colLat = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.contextMenuStripListView.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStripListView
            // 
            this.contextMenuStripListView.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExport});
            this.contextMenuStripListView.Name = "contextMenuStripListView";
            this.contextMenuStripListView.Size = new System.Drawing.Size(139, 26);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(138, 22);
            this.ToolStripMenuItemExport.Text = "导出Excel...";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.contextMenuStripListView;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(1105, 476);
            this.gridControl.TabIndex = 3;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.colLAC,
            this.colCI,
            this.colBCCH,
            this.colBSIC,
            this.colLng,
            this.colLat,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7});
            this.gridView.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.DoubleClick += new System.EventHandler(this.gridView_DoubleClick);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "文件名";
            this.gridColumn1.FieldName = "FileName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "CellID";
            this.gridColumn2.FieldName = "CellID";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "小区";
            this.gridColumn3.FieldName = "CellName";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            // 
            // colLAC
            // 
            this.colLAC.Caption = "LAC";
            this.colLAC.FieldName = "LAC";
            this.colLAC.Name = "colLAC";
            this.colLAC.Visible = true;
            this.colLAC.VisibleIndex = 3;
            // 
            // colCI
            // 
            this.colCI.Caption = "CI";
            this.colCI.FieldName = "CI";
            this.colCI.Name = "colCI";
            this.colCI.Visible = true;
            this.colCI.VisibleIndex = 4;
            // 
            // colBCCH
            // 
            this.colBCCH.Caption = "频点";
            this.colBCCH.FieldName = "BCCH";
            this.colBCCH.Name = "colBCCH";
            this.colBCCH.Visible = true;
            this.colBCCH.VisibleIndex = 5;
            // 
            // colBSIC
            // 
            this.colBSIC.Caption = "扰码";
            this.colBSIC.FieldName = "BSIC";
            this.colBSIC.Name = "colBSIC";
            this.colBSIC.Visible = true;
            this.colBSIC.VisibleIndex = 6;
            // 
            // colLng
            // 
            this.colLng.Caption = "中间点经度";
            this.colLng.FieldName = "LongtitudeMid";
            this.colLng.Name = "colLng";
            this.colLng.Visible = true;
            this.colLng.VisibleIndex = 7;
            // 
            // colLat
            // 
            this.colLat.Caption = "中间点纬度";
            this.colLat.FieldName = "LatitudeMid";
            this.colLat.Name = "colLat";
            this.colLat.Visible = true;
            this.colLat.VisibleIndex = 8;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "持续距离(m)";
            this.gridColumn4.FieldName = "Distance";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 9;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "平均场强";
            this.gridColumn5.FieldName = "RxLevMean";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 10;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "最小场强";
            this.gridColumn6.FieldName = "RxLevMin";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 11;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "最大场强";
            this.gridColumn7.FieldName = "RxLevMax";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 12;
            // 
            // ZTLeakOutCellTDScanSetForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1105, 476);
            this.Controls.Add(this.gridControl);
            this.Name = "ZTLeakOutCellTDScanSetForm";
            this.Text = "室分外泄小区分析";
            this.contextMenuStripListView.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStripListView;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn colLAC;
        private DevExpress.XtraGrid.Columns.GridColumn colCI;
        private DevExpress.XtraGrid.Columns.GridColumn colBCCH;
        private DevExpress.XtraGrid.Columns.GridColumn colBSIC;
        private DevExpress.XtraGrid.Columns.GridColumn colLng;
        private DevExpress.XtraGrid.Columns.GridColumn colLat;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;


    }
}