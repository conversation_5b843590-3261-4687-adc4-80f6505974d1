﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteMgrsDualFreqStater : LteMgrsStaterBase
    {
        private List<LteMgrsDualFreqView> resultViewList = null;
        private double rsrpMin;

        public override void DoStat(LteMgrsFuncItem curFuncItem)
        {
            resultViewList = new List<LteMgrsDualFreqView>();
            this.rsrpMin = (double)curFuncItem.FuncCondtion;

            LteMgrsCity city = curFuncItem.CurQueryCitys[curFuncItem.SelectedCityIndex];
            foreach (LteMgrsRegion region in city.RegionDic.Values)
            {
                LteMgrsDualFreqView view = new LteMgrsDualFreqView(city.CityName, region.RegionName);
                foreach (LteMgrsGrid grid in region.GridDic.Values)
                {
                    LteMgrsDualFreqType freqType = GetGridFreqType(grid);
                    view.AddGrid(freqType);
                }
                resultViewList.Add(view);
            }

            LteMgrsDualFreqView summary = new LteMgrsDualFreqView(city.CityName, "汇总(网格内)");
            foreach (LteMgrsDualFreqView view in resultViewList)
            {
                if (view.RegionName == LteMgrsCity.SOutsideRegion)
                {
                    continue;
                }
                summary.AddGrid(LteMgrsDualFreqType.SingleD, view.SingleD);
                summary.AddGrid(LteMgrsDualFreqType.SingleF, view.SingleF);
                summary.AddGrid(LteMgrsDualFreqType.Dual, view.Dual);
                summary.AddGrid(LteMgrsDualFreqType.None, view.None);
            }
            resultViewList.Add(summary);
        }

        public override List<LteMgrsResultControlBase> GetResult()
        {
            LteMgrsDualFreqResult resultControl = new LteMgrsDualFreqResult();
            resultControl.FillData(new List<LteMgrsDualFreqView>(resultViewList));
            return new List<LteMgrsResultControlBase>() { resultControl };
        }

        public override void Clear()
        {
            if (resultViewList != null)
            {
                resultViewList.Clear();
                resultViewList = null;
            }
        }

        protected LteMgrsDualFreqType GetGridFreqType(LteMgrsGrid grid)
        {
            LteMgrsDualFreqType retType = LteMgrsDualFreqType.None;
            List<LteMgrsFreq> freqList = grid.FreqList;
            foreach (LteMgrsFreq freq in freqList)
            {
                if (freq.RsrpAvg <= rsrpMin || retType == LteMgrsDualFreqType.Dual)
                {
                    break;
                }

                if (LTECell.GetBandTypeByEarfcn(freq.Earfcn) == LTEBandType.D) // D
                {
                    if (retType == LteMgrsDualFreqType.SingleF)
                    {
                        retType = LteMgrsDualFreqType.Dual;
                    }
                    else
                    {
                        retType = LteMgrsDualFreqType.SingleD;
                    }
                }
                else if (LTECell.GetBandTypeByEarfcn(freq.Earfcn) == LTEBandType.F) // F
                {
                    if (retType == LteMgrsDualFreqType.SingleD)
                    {
                        retType = LteMgrsDualFreqType.Dual;
                    }
                    else
                    {
                        retType = LteMgrsDualFreqType.SingleF;
                    }
                }
            }
            return retType;
        }
    }

    public class LteMgrsDualFreqView
    {
        public LteMgrsDualFreqView(string cityName, string regionName)
        {
            CityName = cityName;
            RegionName = regionName;
        }

        public void AddGrid(LteMgrsDualFreqType freqType)
        {
            AddGrid(freqType, 1);
        }

        public void AddGrid(LteMgrsDualFreqType freqType, int times)
        {
            switch (freqType)
            {
                case LteMgrsDualFreqType.SingleD:
                    SingleD += times;
                    break;
                case LteMgrsDualFreqType.SingleF:
                    SingleF += times;
                    break;
                case LteMgrsDualFreqType.Dual:
                    Dual += times;
                    break;
                case LteMgrsDualFreqType.None:
                    None += times;
                    break;
            }
            GridCount += times;
        }

        public string CityName
        {
            get;
            private set;
        }

        public string RegionName
        {
            get;
            private set;
        }

        public int GridCount
        {
            get;
            private set;
        }

        public int SingleD
        {
            get;
            private set;
        }

        public int SingleF
        {
            get;
            private set;
        }

        public int Dual
        {
            get;
            private set;
        }

        public int None
        {
            get;
            private set;
        }
    }

    public enum LteMgrsDualFreqType
    {
        SingleD,
        SingleF,
        Dual,
        None,
    }
}
