﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTVoLteESRVCCAna;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class VoLteESRVCCAnaByRegion : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static VoLteESRVCCAnaByRegion instance = null;

        readonly List<int> msgInfoTypeList = new List<int> { 1560, 1561, 1562, 1563, 1564, 1565, 1566, 1567, 1538, 1539, 1540, 1541, 1542, 1543, 1536 };//语音包信令

        private List<ESRVCCAnaItem> eSRVCCInfoList = new List<ESRVCCAnaItem>();
        public static VoLteESRVCCAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoLteESRVCCAnaByRegion();
                    }
                }
            }
            return instance;
        }

        protected VoLteESRVCCAnaByRegion()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_VOLTE);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
            this.Columns = new List<string>();
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_gsm_SC_LAC");
            Columns.Add("lte_gsm_SC_CI");
            Columns.Add("lte_td_SC_LAC");
            Columns.Add("lte_td_SC_CellID");
            Columns.Add("lte_gsm_DM_RxLevBCCH");
            Columns.Add("lte_gsm_DM_RxLevSub");
            Columns.Add("C_I");
            Columns.Add("lte_td_DM_PCCPCH_RSCP");
        }


        public override string Name
        {
            get
            {
                return "eSRVCC无线性能分析(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27003, this.Name);
        }

        protected override void fireShowForm()
        {
            if (eSRVCCInfoList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            ESRVCCAnaListForm frm = MainModel.CreateResultForm(typeof(ESRVCCAnaListForm)) as ESRVCCAnaListForm;
            frm.FillData(eSRVCCInfoList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            eSRVCCInfoList = new List<ESRVCCAnaItem>();
        }
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<DTData> dtDataList = getDTDataList(fileMng);

                TestPoint lastTp = null;
                ESRVCCAnaItem eSRVCCAnaItem = new ESRVCCAnaItem(fileMng.FileName);

                for (int i = 0; i < dtDataList.Count; i++)
                {
                    if (dtDataList[i] is TestPoint)
                    {
                        lastTp = dtDataList[i] as TestPoint;//最近的采样点
                        eSRVCCAnaItem.TestPoints.Add(lastTp);
                    }
                    else if (dtDataList[i] is Event)
                    {
                        eSRVCCAnaItem = dealEvt(fileMng, dtDataList, lastTp, eSRVCCAnaItem, i);
                    }
                    else if (dtDataList[i] is MasterCom.RAMS.Model.Message)
                    {
                        eSRVCCAnaItem = dealMsg(dtDataList, eSRVCCAnaItem, i);
                    }
                }
                addToResultList(eSRVCCAnaItem); //最后一个
            }
        }

        private ESRVCCAnaItem dealEvt(DTFileDataManager fileMng, List<DTData> dtDataList, TestPoint lastTp, ESRVCCAnaItem eSRVCCAnaItem, int i)
        {
            if ((dtDataList[i] as Event).ID == (int)EnumESRVCCEvent.P2CHandoverRequest
                && eSRVCCAnaItem.IsGotBegin)//前一个事件
            {
                addToResultList(eSRVCCAnaItem);//保存前一个eSRVCCAnaItem

                eSRVCCAnaItem = dealEvtMsg(fileMng, dtDataList, lastTp, i);
                if (eSRVCCAnaItem.MsgHandoverCommandFirst != null)
                {
                    FindValuableMsg(i, dtDataList, eSRVCCAnaItem.MsgHandoverCommandFirst.ID, ref eSRVCCAnaItem);
                    return eSRVCCAnaItem;
                }
            }
            doWithEvent(dtDataList, i, lastTp, ref eSRVCCAnaItem);
            return eSRVCCAnaItem;
        }

        private ESRVCCAnaItem dealEvtMsg(DTFileDataManager fileMng, List<DTData> dtDataList, TestPoint lastTp, int i)
        {
            ESRVCCAnaItem eSRVCCAnaItem;
            eSRVCCAnaItem = new ESRVCCAnaItem(fileMng.FileName);//开启一个新的eSRVCCAnaItem

            for (int j = i - 1; j >= 0; j--)//查询dtDataList中在该事件之前是否有与其相同SN的HandoverCommand信令
            {
                if (dtDataList[j].SN != dtDataList[i].SN)
                {
                    break;
                }
                if (dtDataList[j] is MasterCom.RAMS.Model.Message)
                {
                    Message msg2 = (Message)dtDataList[j];
                    if (msg2.ID == (int)EnumESRVCCMsg.HandoverCommand)
                    {
                        eSRVCCAnaItem.MsgHandoverCommandFirst = msg2;
                        doWithEvent(dtDataList, i, lastTp, ref eSRVCCAnaItem);
                        break;
                    }
                }
            }

            return eSRVCCAnaItem;
        }

        private ESRVCCAnaItem dealMsg(List<DTData> dtDataList, ESRVCCAnaItem eSRVCCAnaItem, int i)
        {
            if (eSRVCCAnaItem.IsGotBegin && eSRVCCAnaItem.EvtEnd == null)//已经开始，还未结束
            {
                doWithMsg(dtDataList, i, ref eSRVCCAnaItem);
            }

            return eSRVCCAnaItem;
        }

        private List<DTData> getDTDataList(DTFileDataManager fileMng)
        {
            List<DTData> dtDataList = new List<DTData>();

            foreach (TestPoint tp in fileMng.TestPoints)
            {
                dtDataList.Add((DTData)tp);
            }
            foreach (Event evt in fileMng.Events)
            {
                if (evt.ID == (int)EnumESRVCCEvent.P2CHandoverRequest || evt.ID == (int)EnumESRVCCEvent.P2CHandoverSuccess
                    || evt.ID == (int)EnumESRVCCEvent.P2CHandoverFailure || evt.ID == (int)EnumESRVCCEvent.TrackAreaUpdateAttempt
                    || evt.ID == (int)EnumESRVCCEvent.TrackAreaUpdateSuccess || evt.ID == (int)EnumESRVCCEvent.TrackAreaUpdateFail
                    || evt.ID == (int)EnumESRVCCEvent.TrackAreaUpdateAttemptFDD || evt.ID == (int)EnumESRVCCEvent.TrackAreaUpdateSuccessFDD
                    || evt.ID == (int)EnumESRVCCEvent.TrackAreaUpdateFailFDD)
                {
                    dtDataList.Add((DTData)evt);
                }
                //dtDataList.Add((DTData)evt);
            }

            foreach (MasterCom.RAMS.Model.Message msg in fileMng.Messages)
            {
                if (msg.ID == (int)EnumESRVCCMsg.MeasurementReport || msg.ID == (int)EnumESRVCCMsg.MeasurementReportGsm
                    || msg.ID == (int)EnumESRVCCMsg.HandoverCommand
                    || msg.ID == (int)EnumESRVCCMsg.HandoverComplete || msg.ID == (int)EnumESRVCCMsg.ChannelRelease
                    || msg.ID == (int)EnumESRVCCMsg.TAUAccept || msg.ID == (int)EnumESRVCCMsg.RRCConnectionRelease
                    || msg.ID == (int)EnumESRVCCMsg.RAUAccept || msgInfoTypeList.Contains(msg.ID)
                    || (msg.ID >= 0x42000000 && msg.ID < 0x43000000))
                {
                    dtDataList.Add((DTData)msg);
                }
            }

            dtDataList.Sort(comparer);
            return dtDataList;
        }

        private void doWithEvent(List<DTData> dtDataList, int preIdx, TestPoint lastTp, ref ESRVCCAnaItem curItem)
        {
            Event evt = (Event)dtDataList[preIdx];

            if (evt.ID == (int)EnumESRVCCEvent.P2CHandoverRequest) //开始
            {
                curItem.Events.Add(evt);
                curItem.AddBeginEvtInfo(lastTp, evt);
            }
            else if (curItem.IsGotBegin && curItem.EvtEnd == null)
            {
                curItem.Events.Add(evt);
                if (evt.ID == (int)EnumESRVCCEvent.TrackAreaUpdateSuccess || evt.ID == (int)EnumESRVCCEvent.TrackAreaUpdateFail
                    || evt.ID == (int)EnumESRVCCEvent.TrackAreaUpdateSuccessFDD || evt.ID == (int)EnumESRVCCEvent.TrackAreaUpdateFailFDD) //结束
                {
                    if (curItem.EvtMid != null)
                    {
                        curItem.AddEndEvtInfo(evt);
                    }
                }
                else if (evt.ID == (int)EnumESRVCCEvent.P2CHandoverFailure)
                {
                    curItem.AddEndEvtInfo(evt);
                }
                else if (evt.ID == (int)EnumESRVCCEvent.P2CHandoverSuccess)//中间事件
                {
                    curItem.AddMidEvtInfo(evt);
                    setESRVCCAnaItemTestPoint(dtDataList, preIdx, curItem, evt);
                }
            }
        }

        private void setESRVCCAnaItemTestPoint(List<DTData> dtDataList, int preIdx, ESRVCCAnaItem curItem, Event evt)
        {
            for (int i = preIdx + 1; i < dtDataList.Count; i++)
            {
                if (dtDataList[i] is TestPoint)
                {
                    TestPoint tp = dtDataList[i] as TestPoint;
                    int? rexLevel = GetRxLev(tp);
                    int? ci = (int?)tp["C_I"];

                    if ((tp.DateTime - evt.DateTime).TotalSeconds > 60)
                    {
                        break;
                    }
                    if (rexLevel != null || ci != null)
                    {
                        curItem.TpMid = tp;
                        break;
                    }
                }
            }
        }

        private void doWithMsg(List<DTData> dtDataList, int preIdx, ref ESRVCCAnaItem curItem)
        {
            Message msg = (Message)dtDataList[preIdx];
            curItem.Messages.Add(msg);
            #region 切换用户面下上行信令
            if (msgInfoTypeList.Contains(msg.ID) && curItem.MsgGsmPacketDown == null)
            {
                curItem.MsgGsmPacketDown = msg;//终端在2G收到的第一个下行语音包

                FindValuableMsg(preIdx, dtDataList, msg.ID, ref curItem);//查询终端在4G收到的最后一个下行RTP包
            }
            else if (msg.ID == (int)EnumESRVCCMsg.MeasurementReportGsm && curItem.MsgGsmPacketUp == null)
            {
                curItem.MsgGsmPacketUp = msg;//终端在2G发出的第一个上行语音包

                FindValuableMsg(preIdx, dtDataList, msg.ID, ref curItem);//查询终端在4G发出的最后一个上行RTP包
            }

            # endregion
            else
            {
                curItem = setESRVCCAnaItem(dtDataList, preIdx, curItem, msg);
            }
        }

        private ESRVCCAnaItem setESRVCCAnaItem(List<DTData> dtDataList, int preIdx, ESRVCCAnaItem curItem, Message msg)
        {
            switch (msg.ID)
            {
                case 1579:
                    if (curItem.MsgHandoverCommandFirst == null)
                    {
                        curItem.MsgHandoverCommandFirst = msg;

                        FindValuableMsg(preIdx, dtDataList, msg.ID, ref curItem);//查询切换前第一条B2测量报告（包含目标2G小区）
                    }
                    break;
                case 1580:
                    if (curItem.MsgHandoverComplete == null)
                    {
                        curItem.MsgHandoverComplete = msg;

                        FindValuableMsg(preIdx, dtDataList, msg.ID, ref curItem);//查询本次Handover Complete之前的第一条Handover Command
                    }
                    break;
                case 1097533257:
                    if (curItem.MsgTAUAccept == null)
                    {
                        curItem.MsgTAUAccept = msg;
                        FindValuableMsg(preIdx, dtDataList, msg.ID, ref curItem);//查询本次TAU Accept之前的第一条2G channel Release
                    }
                    break;
                case 1093625861:
                    if (curItem.MsgRRCConnectionRelease == null)
                    {
                        curItem.MsgRRCConnectionRelease = msg;
                    }
                    break;
                case 2057:
                    if (curItem.MsgRAUAccept == null && curItem.MsgRRCConnectionRelease != null)
                    {
                        curItem.MsgRAUAccept = msg;
                    }
                    break;
            }

            return curItem;
        }

        private void FindValuableMsg(int preIdx, List<DTData> dtDataList,int id, ref ESRVCCAnaItem curItem)
        {
            for (int i = preIdx - 1; i >= 0; i--)
            {
                if (dtDataList[i] is MasterCom.RAMS.Model.Message)
                {
                    Message msg = (Message)dtDataList[i];

                    if ((curItem.EvtBegin.DateTime - msg.DateTime).TotalSeconds >= 60)
                    {
                        break;
                    }

                    bool hasSet = setESRVCCAnaItemMsgInfo(id, curItem, msg);
                    if (hasSet)
                    {
                        break;
                    }
                }
            }
        }

        private bool setESRVCCAnaItemMsgInfo(int id, ESRVCCAnaItem curItem, Message msg)
        {
            if (id == (int)EnumESRVCCMsg.HandoverCommand && msg.ID == (int)EnumESRVCCMsg.MeasurementReport)
            {
                curItem.MsgMeasurementReportLastFirst = msg;
                return true;
            }
            else if (id == (int)EnumESRVCCMsg.HandoverComplete && msg.ID == (int)EnumESRVCCMsg.HandoverCommand)
            {
                curItem.MsgHandoverCommand = msg;
                return true;
            }
            else if (id == (int)EnumESRVCCMsg.TAUAccept && msg.ID == (int)EnumESRVCCMsg.ChannelRelease)
            {
                curItem.MsgChannelRelease = msg;
                return true;
            }
            else if (msg.ID >= 0x42000000 && msg.ID < 0x43000000)
            {
                if (id == (int)EnumESRVCCMsg.MeasurementReportGsm && msg.Direction == 2)
                {
                    curItem.MsgLtePacketUp = msg;//终端在4G收到的上行RTP包
                    return true;
                }
                else if (msgInfoTypeList.Contains(id) && msg.Direction == 1)
                {
                    curItem.MsgLtePacketDown = msg;//终端在4G收到的下行RTP包
                    return true;
                }
            }
            return false;
        }

        private void addToResultList(ESRVCCAnaItem eSRVCCAnaItem)
        {
            //没有eSRVCC开始，不处理
            if (!eSRVCCAnaItem.IsGotBegin)
            {
                return;
            }
            Event evtHand = eSRVCCAnaItem.EvtBegin;

            eSRVCCAnaItem.LteRSRP = GetRSRP(eSRVCCAnaItem.TpBegin);
            eSRVCCAnaItem.LteSINR = GetSINR(eSRVCCAnaItem.TpBegin);

            if (eSRVCCAnaItem.EvtEnd == null || eSRVCCAnaItem.EvtEnd.ID == (int)EnumESRVCCEvent.P2CHandoverFailure)
            {
                eSRVCCAnaItem.FRIsBack = "-";
            }
            else if (eSRVCCAnaItem.EvtEnd.ID == (int)EnumESRVCCEvent.TrackAreaUpdateSuccess || eSRVCCAnaItem.EvtEnd.ID == (int)EnumESRVCCEvent.TrackAreaUpdateSuccessFDD)
            {
                eSRVCCAnaItem.FRIsBack = "是";
            }
            else
            {
                eSRVCCAnaItem.FRIsBack = "否";
            }

            eSRVCCAnaItem.HandoverPrepTimes = getTime(eSRVCCAnaItem.MsgHandoverCommandFirst, eSRVCCAnaItem.MsgMeasurementReportLastFirst);

            if (eSRVCCAnaItem.EvtEnd != null && eSRVCCAnaItem.EvtEnd.ID == (int)EnumESRVCCEvent.P2CHandoverFailure)//eSRVCC切换失败
            {
                eSRVCCAnaItem.ESRVCCResult = "否";
                evtHand = eSRVCCAnaItem.EvtEnd;
            }
            else if (eSRVCCAnaItem.EvtMid != null)//eSRVCC切换成功
            {
                //eSRVCC成功
                eSRVCCAnaItem.ESRVCCResult = "是";

                evtHand = eSRVCCAnaItem.EvtMid;

                //切换成功后的信息
                if (eSRVCCAnaItem.TpMid != null)
                {
                    eSRVCCAnaItem.RexLevel = (float?)GetRxLev(eSRVCCAnaItem.TpMid);
                    eSRVCCAnaItem.C2I = (float?)eSRVCCAnaItem.TpMid["C_I"];//暂取该值
                }

                #region 计算时延
                eSRVCCAnaItem.HandoverCtrlSuspendTimes = getTime(eSRVCCAnaItem.MsgHandoverComplete, eSRVCCAnaItem.MsgHandoverCommand);

                eSRVCCAnaItem.HandoverDownDelayTimes = getTime(eSRVCCAnaItem.MsgGsmPacketDown, eSRVCCAnaItem.MsgLtePacketDown);
                eSRVCCAnaItem.HandoverUpDelayTimes = getTime(eSRVCCAnaItem.MsgGsmPacketUp, eSRVCCAnaItem.MsgLtePacketUp);
                eSRVCCAnaItem.HandoverDown2UpDelayTimes = getTime(eSRVCCAnaItem.MsgGsmPacketUp, eSRVCCAnaItem.MsgLtePacketDown);

                eSRVCCAnaItem.BackCtrlDelayTimes = getTime(eSRVCCAnaItem.MsgTAUAccept, eSRVCCAnaItem.MsgChannelRelease);
                eSRVCCAnaItem.CtrlDelayTimes2To3 = getTime(eSRVCCAnaItem.MsgRAUAccept, eSRVCCAnaItem.MsgRRCConnectionRelease);
                eSRVCCAnaItem.CtrlDelayTimes3To4 = getTime(eSRVCCAnaItem.MsgTAUAccept, eSRVCCAnaItem.MsgRAUAccept);
                #endregion
            }
            else//eSRVCC切换无结果
            {
                eSRVCCAnaItem.ESRVCCResult = "-";
            }

            //填充eSRVCC前信息
            if (evtHand != null)
            {
                eSRVCCAnaItem.LteEARFCN = Convert.ToInt32(evtHand["Value1"]);
                eSRVCCAnaItem.LtePCI = Convert.ToInt32(evtHand["Value2"]);
                eSRVCCAnaItem.GsmEARFCN = Convert.ToInt32(evtHand["Value3"]);
                eSRVCCAnaItem.BSIC = Convert.ToInt32(evtHand["Value4"]);
            }

            eSRVCCAnaItem.SN = eSRVCCInfoList.Count + 1;
            eSRVCCInfoList.Add(eSRVCCAnaItem);
        }

        private string getTime(Message msg1,Message msg2)
        {
            string strTimes = "";
            if (msg1 != null && msg2 != null)
            {
                double seconds = (msg1.HandsetTime - msg2.HandsetTime).TotalSeconds;

                if (seconds > 0)
                {
                    strTimes = seconds.ToString();
                }
            }
            else
            {
                strTimes = "0.000";
            }
            return strTimes;
        }

        private readonly Comparer comparer = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }

        protected short? GetRxLev(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (short?)tp["lte_fdd_gsm_DM_RxLevSub"];
            }
            return (short?)tp["lte_gsm_DM_RxLevSub"];
        }
        protected float? GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_RSRP"];
            }
            return (float?)tp["lte_RSRP"];
        }
        protected float? GetSINR(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_SINR"];
            }
            return (float?)tp["lte_SINR"];
        }
    }

    public class VoLteESRVCCAnaByRegion_FDD : VoLteESRVCCAnaByRegion
    {
        private static VoLteESRVCCAnaByRegion_FDD instance = null;
        public static new VoLteESRVCCAnaByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoLteESRVCCAnaByRegion_FDD();
                    }
                }
            }
            return instance;
        }
        protected VoLteESRVCCAnaByRegion_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
            this.Columns = new List<string>();
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_SINR");
            Columns.Add("lte_fdd_TAC");
            Columns.Add("lte_fdd_ECI");
            Columns.Add("lte_fdd_EARFCN");
            Columns.Add("lte_fdd_PCI");
            Columns.Add("lte_fdd_gsm_SC_LAC");
            Columns.Add("lte_fdd_gsm_SC_CI");
            Columns.Add("lte_td_SC_LAC");
            Columns.Add("lte_td_SC_CellID");
            Columns.Add("lte_fdd_gsm_DM_RxLevBCCH");
            Columns.Add("lte_fdd_gsm_DM_RxLevSub");
            Columns.Add("C_I");
            Columns.Add("lte_td_DM_PCCPCH_RSCP");
        }
        public override string Name
        {
            get
            {
                return "VOLTE_FDD eSRVCC无线性能分析(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30022, this.Name);
        }
    }

    enum EnumESRVCCEvent
    {

        P2CHandoverRequest = 1145,
        P2CHandoverSuccess = 1146,
        P2CHandoverFailure = 1147,

        TrackAreaUpdateAttempt = 852,
        TrackAreaUpdateSuccess = 853,
        TrackAreaUpdateFail = 854,

        TrackAreaUpdateAttemptFDD = 3171,
        TrackAreaUpdateSuccessFDD=3172,
        TrackAreaUpdateFailFDD = 3173,
    }

    enum EnumESRVCCMsg
    {
        MeasurementReportGsm = 1557,
        MeasurementReport = 1093626369,
        HandoverCommand=1579,
        HandoverComplete=1580,
        ChannelRelease=1549,
        TAUAccept=1097533257,
        RRCConnectionRelease = 1093625861,
        RAUAccept=2057,
    }
}
