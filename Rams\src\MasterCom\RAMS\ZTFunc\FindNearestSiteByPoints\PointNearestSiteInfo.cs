﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.FindNearestSiteByPoints
{
    public class PointNearestSiteInfo
    {
       public PointInfo Point { get; set; }
        public List<NearestSiteInfo> NearestSites { get; set; } = new List<NearestSiteInfo>();

       MTGis.DbRect bounds = null;
       public MTGis.DbRect Bounds
       {
           get
           {
               if (bounds == null)
               {
                   double lngMax = Point.Longitude;
                   double lngMin = Point.Longitude;
                   double latMax = Point.Latitude;
                   double latMin = Point.Latitude;

                   foreach (NearestSiteInfo item in NearestSites)
                   {
                       lngMax = Math.Max(item.Site.Longitude, lngMax);
                       lngMin = Math.Min(item.Site.Longitude, lngMin);
                       latMax = Math.Max(item.Site.Latitude, latMax);
                       latMin = Math.Min(item.Site.Latitude, latMin);
                   }
                   bounds = new MTGis.DbRect(lngMin - 0.002, latMin - 0.002, lngMax + 0.002, latMax + 0.002);
               }
               return bounds;
           }
       }
       
    }

    public class NearestSiteInfo : IComparable
    {
        public ISite Site
        {
            get;
            private set;
        }
        public double Distance
        {
            get;
            private set;
        }
        public PointNearestSiteInfo Point
        {
            get;
            private set;
        }
        public NearestSiteInfo(PointNearestSiteInfo pi, ISite site, double distance)
        {
            this.Point = pi;
            this.Site = site;
            this.Distance = Math.Round(distance, 2);
        }
        public int CompareTo(object obj)
        {
              try
              {
                  NearestSiteInfo info = obj as NearestSiteInfo;
                  return this.Distance.CompareTo(info.Distance);
             }
             catch (Exception ex) { throw (new Exception(ex.Message)); }
        }

    }

}
