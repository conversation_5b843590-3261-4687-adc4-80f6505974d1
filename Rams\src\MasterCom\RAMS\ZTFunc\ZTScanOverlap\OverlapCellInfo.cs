﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class ScanOverlapInfo
    {
        protected ScanOverlapInfo(Cell cell)
        {
            this.cell = cell;
        }
        protected Cell cell;
        public Cell Cell
        {
            get { return cell; }
        }
        public int CI
        {
            get { return cell.CI; }
        }
        public string CellName
        {
            get
            {
                return cell.Name;
            }
        }
        protected List<TestPoint> tps = new List<TestPoint>();
        public List<TestPoint> TestPoints
        {
            get { return tps; }
        }
        public int TestPointCount
        {
            get { return tps.Count; }
        }

        protected float rxLevSum = 0;
        protected float rxLevMax = float.MinValue;
        protected float rxLevMin = float.MaxValue;
        public float RxLevAvg
        {
            get
            {
                float ret = float.NaN;
                if (tps.Count > 0)
                {
                    ret = (float)Math.Round(rxLevSum / tps.Count, 2);
                }
                return ret;
            }
        }

        protected double distanceSum = 0;
        protected double distanceMax = double.MinValue;
        protected double distanceMin = double.MaxValue;
        public double DistanceAvg
        {
            get
            {
                double ret = double.NaN;
                if (tps.Count > 0)
                {
                    ret = Math.Round(distanceSum / tps.Count, 2);
                }
                return ret;
            }
        }

        protected float rxLevDiffSum = 0;
        protected float rxLevDiffMax = float.MinValue;
        protected float rxLevDiffMin = float.MaxValue;
        public float RxLevDiffAvg
        {
            get
            {
                float ret = float.NaN;
                if (tps.Count > 0)
                {
                    ret = (float)Math.Round(rxLevDiffSum / tps.Count, 2);
                }
                return ret;
            }
        }

        protected double distanceDiffSum = 0;
        protected double distanceDiffMax = float.MinValue;
        protected double distanceDiffMin = float.MaxValue;
        public double DistanceDiffAvg
        {
            get
            {
                double ret = double.NaN;
                if (tps.Count > 0)
                {
                    ret = Math.Round(distanceDiffSum / tps.Count, 2);
                }
                return ret;
            }
        }

        protected void addInfo(TestPoint tp,float rxlev, float rxlevDiff, double distance, double distanceDiff)
        {
            tps.Add(tp);
            rxLevSum += rxlev;
            rxLevMax = Math.Max(rxLevMax, rxlev);
            rxLevMin = Math.Min(rxLevMin, rxlev);
            rxLevDiffSum += rxlevDiff;
            rxLevDiffMax = Math.Max(rxLevDiffMax, rxlevDiff);
            rxLevDiffMin = Math.Min(rxLevDiffMin, rxlevDiff);
            distanceSum += distance;
            distanceMax = Math.Max(distanceMax, distance);
            distanceMin = Math.Min(distanceMin, distance);
            distanceDiffSum += distanceDiff;
            distanceDiffMax = Math.Max(distanceDiffMax, distanceDiff);
            distanceDiffMin = Math.Min(distanceDiffMin, distanceDiff);
        }
    }

    public class OverlapCellInfo : ScanOverlapInfo
    {
        public OverlapCellInfo(Cell cell) : base(cell) { }
        private readonly Dictionary<Cell, OverlapSubCellInfo> cellSubOverlapDic = new Dictionary<Cell, OverlapSubCellInfo>();
        public List<OverlapSubCellInfo> SubCells
        {
            get { return new List<OverlapSubCellInfo>(cellSubOverlapDic.Values); }
        }
        /**
        /// <summary>
        /// 增加过覆盖采样点
        /// </summary>
        /// <param name="tp">采样点</param>
        /// <param name="rxlev">过覆盖小区的场强</param>
        /// <param name="distance2Cell">采样点与过覆盖小区的距离</param>
        /// <param name="subCell">被过覆盖小区</param>
        /// <param name="subCellrxlev">被过覆盖小区场强</param>
        /// <param name="distance2SubCell">采样点与被过覆盖小区的距离</param>
        /// <param name="distanceDiff">两距离差</param>
        public void AddTestPoint(TestPoint tp, float rxlev, double distance2Cell, Cell subCell, float subCellrxlev, double distance2SubCell, float rxlevDiff, double distanceDiff)
        {
            addInfo(tp, rxlev, rxlevDiff, distance2Cell, distanceDiff);
            if (cellSubOverlapDic.ContainsKey(subCell))
            {
                OverlapSubCellInfo subCellInfo = cellSubOverlapDic[subCell];
                subCellInfo.AddTestPoint(tp, rxlev, distance2Cell, subCellrxlev, distance2SubCell, rxlevDiff, distanceDiff);
            }
            else
            {
                OverlapSubCellInfo subCellInfo = new OverlapSubCellInfo(subCell, cell);
                subCellInfo.AddTestPoint(tp, rxlev, distance2Cell, subCellrxlev, distance2SubCell, rxlevDiff, distanceDiff);
                cellSubOverlapDic.Add(subCell, subCellInfo);
            }
        }
        */
    }

    public class OverlapSubCellInfo : ScanOverlapInfo
    {
        public OverlapSubCellInfo(Cell cell, Cell overlapCell)
            : base(cell)
        {
            this.overlapCell = overlapCell;
        }
        private readonly Cell overlapCell;
        public string OverlapCellName
        {
            get { return overlapCell.Name; }
        }

        public Cell OverlapCell
        {
            get { return overlapCell; }
        }

        private float ovlCellRxLevSum = 0;
        private float ovlCellRxLevMax = float.MinValue;
        private float ovlCellRxLevMin = float.MaxValue;
        public float OverlapCellRxLevAvg
        {
            get
            {
                float ret = float.NaN;
                if (tps.Count > 0)
                {
                    ret = (float)Math.Round(ovlCellRxLevSum / tps.Count, 2);
                }
                return ret;
            }
        }
        public int MaxCellCI
        {
            get
            {
                return overlapCell.CI;
            }
        }

        private double overlapCellDistanceSum = 0;
        private double overlapCellDistanceMax = double.MinValue;
        private double overlapCellDistanceMin = double.MaxValue;
        public double OverlapCellDistanceAvg
        {
            get
            {
                double ret = double.NaN;
                if (tps.Count > 0)
                {
                    ret = Math.Round(overlapCellDistanceSum / tps.Count, 2);
                }
                return ret;
            }
        }

        public void AddTestPoint(TestPoint tp, float ovrerlapCellRxLev, double distance2OvrerlapCell, float nearestCellRxLev, double distance2NearestCell, float rxlevDiff, double distanceDiff)
        {
            addInfo(tp, nearestCellRxLev, rxlevDiff, distance2NearestCell, distanceDiff);
            //过覆盖小区信息
            ovlCellRxLevSum += ovrerlapCellRxLev;
            ovlCellRxLevMax = Math.Max(ovlCellRxLevMax, ovrerlapCellRxLev);
            ovlCellRxLevMin = Math.Min(ovlCellRxLevMin, ovrerlapCellRxLev);
            overlapCellDistanceSum += distance2OvrerlapCell;
            overlapCellDistanceMax = Math.Max(overlapCellDistanceMax, distance2OvrerlapCell);
            overlapCellDistanceMin = Math.Min(overlapCellDistanceMin, distance2OvrerlapCell);
        }
    }
}
