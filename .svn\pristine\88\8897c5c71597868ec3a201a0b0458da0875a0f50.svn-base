﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class Area2PeriodTestResultForm : MinCloseForm
    {
        public Area2PeriodTestResultForm()
            : base()
        {
            InitializeComponent();
        }

        private List<AreaBase> areaSet = null;
        private AreaReportTemplate template;
        public void FillData(AreaReportTemplate template, DataTable table, List<AreaBase> areas)
        {
            this.template = template;
            this.areaSet = areas;
            gridCtrl.BeginUpdate();
            gv.Columns.Clear();
            gridCtrl.DataSource = table;
            gv.PopulateColumns(table);
            foreach (DevExpress.XtraGrid.Columns.GridColumn col in gv.Columns)
            {
                col.Caption = col.FieldName;
                col.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
                if (col.Caption == "村庄")
                {
                    col.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
                }
                else if (col.Caption == "Tag")
                {
                    col.Visible = false;
                }
            }
            gv.BestFitColumns();
            gridCtrl.EndUpdate();

            cbxCol.SelectedIndexChanged -= cbxCol_SelectedIndexChanged;
            cbxCol.Items.Clear();
            foreach (TemplateColumn col in template.Columns)
            {
                if (col.IsDynamicBKColor && col.DynamicBKColorRanges.Count > 0)
                {
                    cbxCol.Items.Add(col);
                }
            }
            if (cbxCol.Items.Count > 0)
            {
                cbxCol.SelectedIndex = 0;
            }
            refreshLegend();
            cbxCol.SelectedIndexChanged += cbxCol_SelectedIndexChanged;
        }

        ZTAreaArchiveLayer layer = null;
        Area2PeriodGridLayer gridLayer = null;
        void initLayer()
        {
            layer = MainModel.MainForm.GetMapForm().GetLayerBase(typeof(ZTAreaArchiveLayer)) as ZTAreaArchiveLayer;
            layer.Areas = this.areaSet;

            gridLayer = MainModel.MainForm.GetMapForm().GetLayerBase(typeof(Area2PeriodGridLayer)) as Area2PeriodGridLayer;
        }

        void cbxCol_SelectedIndexChanged(object sender, EventArgs e)
        {
            refreshLegend();
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            GridView gridView = sender as GridView;
            if (gridView == null)
            {
                return;
            }
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gridView.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            DataRowView rowView = gridView.GetRow(info.RowHandle) as DataRowView;
            foreach (object cell in rowView.Row.ItemArray)
            {
                if (cell is Area2PeriodGrid)
                {
                    prepareGridColor(cell as Area2PeriodGrid, cbxCol.SelectedItem as TemplateColumn);
                    break;
                }
            }
        }

        private void gv_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            //
        }

        private void refreshLegend()
        {
            lbxLegend.Items.Clear();
            TemplateColumn tCol = cbxCol.SelectedItem as TemplateColumn;
            if (tCol != null)
            {
                lbxLegend.Items.Add("");
                foreach (DTParameterRangeColor rng in tCol.DynamicBKColorRanges)
                {
                    lbxLegend.Items.Add(rng);
                }
                lbxLegend.Items.Add("");

                DataRowView rowView = gv.GetFocusedRow() as DataRowView;
                if (rowView != null)
                {

                    foreach (object cell in rowView.Row.ItemArray)
                    {
                        if (cell is Area2PeriodGrid)
                        {
                            prepareGridColor(cell as Area2PeriodGrid, tCol);
                            break;
                        }
                    }
                }
            }

            lbxLegend.Invalidate();
        }

        private void prepareGridColor(Area2PeriodGrid areaGrid,TemplateColumn tCol)
        {
            initLayer();
            Dictionary<AreaKPIDataGroup<GridUnitBase>, Color> dic = new Dictionary<AreaKPIDataGroup<GridUnitBase>, Color>();
            List<Area2PeriodGrid> list = new List<Area2PeriodGrid>();
            list.Add(areaGrid);
            gridLayer.AreaGrids = list;
            gridLayer.GridColorDic = dic;
            if (tCol != null)
            {
                setGridColor(areaGrid.Grids1, tCol, dic);
                setGridColor(areaGrid.Grids2, tCol, dic);
            }
            MainModel.MainForm.GetMapForm().GoToView(areaGrid.Area.Bounds);
        }

        private void setGridColor(List<AreaKPIDataGroup<GridUnitBase>> grids, TemplateColumn tCol, Dictionary<AreaKPIDataGroup<GridUnitBase>, Color> dic)
        {
            if (grids != null)
            {
                foreach (AreaKPIDataGroup<GridUnitBase> grid in grids)
                {
                    double value = grid.CalcFormula((CarrierType)tCol.CarrierID, -1, tCol.Expression);
                    Color color = tCol.GetBKColorByValue(value);
                    if (!color.IsEmpty)
                    {
                        dic[grid] = color;
                    }
                }
            }
        }

        private void lbxLegend_DrawItem(object sender, DrawItemEventArgs e)
        {
            System.Windows.Forms.ListBox listBoxLegend = sender as System.Windows.Forms.ListBox;
            if (e.Index < 0)
            {
                return;
            }
            object item = listBoxLegend.Items[e.Index];
            string text = "";
            if (item is DTParameterRangeColor)
            {
                e.Graphics.FillRectangle(new SolidBrush((item as DTParameterRangeColor).Value), e.Bounds.X, e.Bounds.Y, 16, 16);
                text = ((DTParameterRange)item).RangeDescription + "  " + ((DTParameterRange)item).DesInfo;
            }
            else if (item is string)
            {
                text = item.ToString();
            }
            e.Graphics.DrawString(text, listBoxLegend.Font, Brushes.Black, e.Bounds.X + 20, e.Bounds.Y);
        }

        private void num_ValueChanged(object sender, EventArgs e)
        {
            refreashLayer();
        }

        private void color_EditValueChanged(object sender, EventArgs e)
        {
            refreashLayer();
        }

        private void refreashLayer()
        {
            initLayer();
            gridLayer.XOffset1 = (int)numX1.Value;
            gridLayer.XOffset2 = (int)numX2.Value;
            gridLayer.YOffset1 = (int)numY1.Value;
            gridLayer.YOffset2 = (int)numY2.Value;
            gridLayer.Color1 = color1.Color;
            gridLayer.Color2 = color2.Color;
            gridLayer.Invalidate();
        }

        private void miExportXls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridCtrl.MainView as GridView);
        }

    }
}
