﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NREdgeSpeedAna : DIYAnalyseByFileBackgroundBase
    {
        public Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic { get; set; }
        public Dictionary<string, Dictionary<string, List<float>>> cityGridTypeNameEdgeSpeedDic { get; set; }

        public List<NREdgeSpeedInfo> nrEdgeSpeedInfoList { get; set; }
        protected NREdgeSpeedAnaHelper helper = new NREdgeSpeedAnaHelper();
        protected NREdgeSpeedCondtion curCondition = new NREdgeSpeedCondtion();
        protected string strCityName = "";
        protected NREdgeSpeedAnaDlg dlg;

        protected static readonly object lockObj = new object();
        protected NREdgeSpeedAna()
            : base(MainModel.GetInstance())
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(false, false);
            Columns.Add("NR_APP_type");
            Columns.Add("NR_APP_Status");
            Columns.Add("NR_APP_ThroughputDL_Mb");

            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.NR_SA_TDD_DATA);
            ServiceTypes.Add(ServiceType.NR_NSA_TDD_DATA);
            ServiceTypes.Add(ServiceType.NR_DM_TDD_DATA);
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 35000, 35041, this.Name);
        }

        protected override void QueryMultiDistrictFile()
        {
            ClientProxy clientProxy = null;
            foreach (int DistrictID in condition.DistrictIDs)
            {
                strCityName = DistrictManager.GetInstance().getDistrictName(DistrictID);
                clientProxy = new ClientProxy();
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    continue;
                }
                Package package = clientProxy.Package;
                if (condition.IsByRound)
                {
                    queryTimePeriodInfo(clientProxy, package, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        queryTimePeriodInfo(clientProxy, package, period, false);
                    }
                }
                clientProxy.Close();
            }
        }

        protected override bool getCondition()
        {
            if (dlg == null)
            {
                dlg = new NREdgeSpeedAnaDlg();
            }
            dlg.SetCondition(curCondition);
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                curCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void getReadyBeforeQuery()
        {
            mutRegionMopDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
            mutRegionMopDic = EdgeSpeedAnaHelper.InitRegionMop2();
            cityGridTypeNameEdgeSpeedDic = new Dictionary<string, Dictionary<string, List<float>>>();
            nrEdgeSpeedInfoList = new List<NREdgeSpeedInfo>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (var tp in file.TestPoints)
                {
                    if (isValidTestPoint(tp))
                    {
                        dealTestPoint(tp);
                    }
                }
            }
        }

        private void dealTestPoint(TestPoint tp)
        {
            double? dValue = NRTpHelper.NrTpManager.GetAppThroughputDLMb(tp);
            short? type = NRTpHelper.NrTpManager.GetAppType(tp);

            if (type != null && type == 2 && dValue != null)
            {
                float dEdgeSpeed = float.Parse(dValue.ToString());
                helper.AddValidData(tp.Longitude, tp.Latitude, dEdgeSpeed, strCityName, mutRegionMopDic, cityGridTypeNameEdgeSpeedDic);
            }
        }

        protected override void getResultsAfterQuery()
        {
            List<EdgeSpeedInfo> infoList = helper.GetResultAfterAllQuery(cityGridTypeNameEdgeSpeedDic, curCondition.EdgeSpeedThreshold);
            foreach (var info in infoList)
            {
                nrEdgeSpeedInfoList.Add(info as NREdgeSpeedInfo);
            }
        }

        protected override void fireShowForm()
        {
            NREdgeSpeedAnaForm frm = MainModel.CreateResultForm(typeof(NREdgeSpeedAnaForm)) as NREdgeSpeedAnaForm;
            frm.Init("有效采样点数");
            frm.FillData(nrEdgeSpeedInfoList);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class NREdgeSpeedAnaByFile : NREdgeSpeedAna
    {
        private NREdgeSpeedAnaByFile()
            : base()
        {
        }

        private static NREdgeSpeedAnaByFile instance = null;
        public static NREdgeSpeedAnaByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NREdgeSpeedAnaByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "边缘速率分析按采样点(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }

    public class NREdgeSpeedAnaByRegion : NREdgeSpeedAna
    {
        protected NREdgeSpeedAnaByRegion()
            : base()
        {
        }

        private static NREdgeSpeedAnaByRegion instance = null;
        public static NREdgeSpeedAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NREdgeSpeedAnaByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "边缘速率分析按采样点(按区域)"; }
        }
    }
}
