﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model.Interface
{
    public class DIYReplayContentOption
    {
        public DIYReplayContentOption()
        {
            SampleColumns = new List<ColumnDefItem>();
            MessageInclude = false;
            MessageIncludeAllRtp = false;
            MessageL3HexCode = false;
            EventInclude = false;
            FusionInclude = false;
        }

        public string Name { get; set; }
        public string Desc { get; set; }
        public string DefaultSerialThemeName { get; set; }
        public int SampleServiceType { get; set; }
        public List<ColumnDefItem> SampleColumns { get; set; }
        public bool MessageInclude { get; set; }
        public bool MessageIncludeAllRtp { get; set; }
        public bool MessageL3HexCode { get; set; }
        public bool EventInclude { get; set; }
        public bool FusionInclude { get; set; }

        public override string ToString()
        {
            return Name;
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Name"] = Name;
                param["Desc"] = Desc;
                param["SampleServiceType"] = SampleServiceType;
                param["MessageInclude"] = MessageInclude;
                param["MessageIncludeAllRtp"] = MessageIncludeAllRtp;
                param["MessageL3HexCode"] = MessageL3HexCode;
                param["EventInclude"] = EventInclude;
                param["FusionInclude"] = FusionInclude;
                List<object> columnsParams = new List<object>();
                param["columnsDef"] = columnsParams;
                param["DefaultSerialThemeName"] = DefaultSerialThemeName;
                foreach (ColumnDefItem cr in SampleColumns)
                {
                    if(cr!=null)
                    {
                        columnsParams.Add(cr.Param);
                    }
                    
                }
                return param;
            }
            set
            {
                Name = (String)value["Name"];
                Desc = (String)value["Desc"];
                SampleServiceType = (int)value["SampleServiceType"];
                MessageInclude = (bool)value["MessageInclude"];
                if (value.ContainsKey("MessageIncludeAllRtp"))
                {
                    MessageIncludeAllRtp = (bool)value["MessageIncludeAllRtp"];
                }
                MessageL3HexCode = (bool)value["MessageL3HexCode"];
                EventInclude = (bool)value["EventInclude"];
                if (value.ContainsKey("FusionInclude"))
                {
                    FusionInclude = (bool)value["FusionInclude"];
                }
                if (value.ContainsKey("DefaultSerialThemeName"))
                {
                    DefaultSerialThemeName = (string)value["DefaultSerialThemeName"];
                }
                SampleColumns.Clear();
                List<object> columnsParams = (List<object>)value["columnsDef"];
                foreach (object o in columnsParams)
                {
                    Dictionary<string, object> rptParam = (Dictionary<string, object>)o;
                    int imgID = (int)rptParam["ImgID"];
                    int paraID = (int)rptParam["ParaID"];
                    int tableID = (int)rptParam["TableID"];
                    ColumnDefItem cdf = InterfaceManager.GetInstance().GetColumnDef(imgID, paraID, tableID);
                    if(cdf!=null)
                    {
                        SampleColumns.Add(cdf);
                    }
                }
            }
        }
    }
}
