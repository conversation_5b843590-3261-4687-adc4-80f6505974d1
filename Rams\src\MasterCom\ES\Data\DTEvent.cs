﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.ES.Data
{
    public class DTEvent
    {
        public string eventName { get; set; }
        public int eventId { get; set; }
        public override string ToString()
        {
            return eventId + " : " +eventName;
        }

        public static XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is DTEvent)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "Event", (value as DTEvent).Param);
                return item;
            }
            return null;
        }
        public static object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals(typeof(DTEvent).Name))
            {
                Dictionary<string, object> param = configFile.GetItemValue(item, "Event") as Dictionary<string, object>;
                DTEvent p = new DTEvent();
                p.Param = param;
                return p;
            }
            return null;
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["eventName"] = eventName;
                param["eventId"] = eventId;
                return param;
            }
            set
            {
                if (value == null || value.Count == 0)
                {
                    return;
                }
                this.eventName = (string)value["eventName"];
                this.eventId = (int)value["eventId"];
            }
        }
    }
}
