﻿using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Grid;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRScanModRoadResultForm : MinCloseForm
    {
        public NRScanModRoadResultForm()
            : base()
        {
            InitializeComponent();
            this.DisposeWhenClose = true;
            btnFind.Click += BtnFind_Click;
            gridView1.DoubleClick += GridView_DoubleClick;
            gridView2.DoubleClick += GridView_DoubleClick;
            gridView3.DoubleClick += GridView_DoubleClick;
            miExportExcel.Click += MiExportExcel_Click;
        }

        private void BtnFind_Click(object sender, EventArgs e)
        {
            FillData();
        }

        private void GridView_DoubleClick(object sender, EventArgs e)
        {
            GridView gv = sender as GridView;
            object row = gv.GetRow(gv.GetSelectedRows()[0]);
            if (row is ScanNRModCellInfo)
            {
                ScanNRModCellInfo cellInfo = row as ScanNRModCellInfo;
                NRCell cell = cellInfo.NRCell;
                MainModel.SelectedNRCells = null;
                MainModel.SetSelectedNRCell(cell);
                MainModel.FireSelectedCellChanged(MainModel.MainForm);
            }
            else if (row is NRScanModRoadQueryInfo)
            {
                NRScanModRoadQueryInfo roadInfo = row as NRScanModRoadQueryInfo;
                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in roadInfo.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(MainModel.MainForm);

                if (roadInfo.TestPoints.Count == 0)
                {
                    return;
                }
                TestPoint midTp = roadInfo.TestPoints[roadInfo.TestPoints.Count / 2];
                MainModel.MainForm.GetMapForm().GoToView(midTp.Longitude, midTp.Latitude);

                OutlineOfRoad outRoad = new OutlineOfRoad();
                outRoad.SetPoints(roadInfo.TestPoints);
                TempLayer.Instance.Draw(outRoad.Drawer);
            }
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            if (isNeedHideCellKeyInfo)
            {
                List<List<object>> exportList = MasterCom.Util.GridViewTransfer.Transfer(gridControl1);
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(exportList);
                return;
            }

            List<NPOIRow> contents = new List<NPOIRow>();
            NPOIRow titleRow = new NPOIRow();
            foreach (GridColumn col in gridView1.VisibleColumns)
            {
                titleRow.AddCellValue(col.Caption);
            }
            foreach (GridColumn col in gridView2.VisibleColumns)
            {
                titleRow.AddCellValue(col.Caption);
            }
            foreach (GridColumn col in gridView3.VisibleColumns)
            {
                titleRow.AddCellValue(col.Caption);
            }
            contents.Add(titleRow);

            List<NRScanModRoadQueryInfo> roadList = gridControl1.DataSource as List<NRScanModRoadQueryInfo>;
            foreach (NRScanModRoadQueryInfo road in roadList)
            {
                NPOIRow roadRow = new NPOIRow();
                roadRow.AddCellValue(road.SN);
                roadRow.AddCellValue(road.RoadDesc);
                roadRow.AddCellValue(road.Length);
                roadRow.AddCellValue(road.RelLevel);
                roadRow.AddCellValue(road.TotalSampleCount);
                roadRow.AddCellValue(road.InvalidSampleCount);
                roadRow.AddCellValue(road.CellsCount);
                roadRow.AddCellValue(road.ModCellsCount);
                roadRow.AddCellValue(road.FileName);

                foreach (ScanNRModCellInfo tarCell in road.ModCells)
                {
                    NPOIRow tarRow = new NPOIRow();
                    tarRow.cellValues.AddRange(roadRow.cellValues);
                    tarRow.AddCellValue(tarCell.CellName);
                    tarRow.AddCellValue(tarCell.CellID);
                    tarRow.AddCellValue(tarCell.EARFCN);
                    tarRow.AddCellValue(tarCell.PCI);
                    tarRow.AddCellValue(tarCell.SID);
                    tarRow.AddCellValue(tarCell.SrcCellsCount);
                    tarRow.AddCellValue(tarCell.Longitude);
                    tarRow.AddCellValue(tarCell.Latitude);
                    tarRow.AddCellValue(tarCell.Direction);

                    foreach (ScanNRModCellInfo srcCell in tarCell.SrcCells)
                    {
                        NPOIRow srcRow = new NPOIRow();
                        srcRow.cellValues.AddRange(tarRow.cellValues);
                        srcRow.AddCellValue(srcCell.CellName);
                        srcRow.AddCellValue(srcCell.CellID);
                        srcRow.AddCellValue(srcCell.TarInterfereCount);
                        srcRow.AddCellValue(srcCell.EARFCN);
                        srcRow.AddCellValue(srcCell.PCI);
                        srcRow.AddCellValue(srcCell.SID);
                        srcRow.AddCellValue(srcCell.Longitude);
                        srcRow.AddCellValue(srcCell.Latitude);
                        srcRow.AddCellValue(srcCell.Direction);
                        srcRow.AddCellValue(srcCell.TarDistance);

                        contents.Add(srcRow);
                    }
                }
            }
            ExcelNPOIManager.ExportToExcel(contents);
        }

        public void FillData(NRScanModRoadQueryStater stater, NRScanModRoadCondition cond)
        {
            this.stater = stater;
            cmbModType.SelectedItem = cond.FilterCond.ModX.ToString();

            FillData();
        }

        private void FillData()
        {
            NRModInterfereCondition cond = GetFilter();
            object result = stater.GetStatResult(cond);
            gridControl1.DataSource = result;
            gridControl1.RefreshDataSource();
        }

        public override void ReleaseResources()
        {
            if (this.stater != null)
            {
                stater.Clear();
            }
            TempLayer.Instance.Clear();
            MainModel.DTDataManager.Clear();
            MainModel.FireDTDataChanged(MainModel.MainForm);
        }

        private NRModInterfereCondition GetFilter()
        {
            NRModInterfereCondition cond = new NRModInterfereCondition();
            cond.Angle = (double)numAngle.Value;
            cond.Distance = (double)numDistance.Value;
            cond.ModX = cond.GetModeType(cmbModType.SelectedItem.ToString());
            if (chkValue0.Checked) cond.Sids.Add(0);
            if (chkValue1.Checked) cond.Sids.Add(1);
            if (chkValue2.Checked) cond.Sids.Add(2);
            if (chkValue3.Checked && cond.ModX >= 4) cond.Sids.Add(3);
            if (chkValue4.Checked && cond.ModX == 6) cond.Sids.Add(4);
            if (chkValue5.Checked && cond.ModX == 6) cond.Sids.Add(5);
            return cond;
        }

        private void cmbModType_SelectedIndexChanged(object sender, EventArgs e)
        {
            string modType = cmbModType.SelectedItem.ToString();
            if (modType == "3")
            {
                chkValue3.Enabled = false;
                chkValue3.Checked = false;
                chkValue4.Enabled = false;
                chkValue4.Checked = false;
                chkValue5.Enabled = false;
                chkValue5.Checked = false;
            }
            else if (modType == "4")
            {
                chkValue3.Enabled = true;
                chkValue4.Enabled = false;
                chkValue4.Checked = false;
                chkValue5.Enabled = false;
                chkValue5.Checked = false;
            }
            else if (modType == "6")
            {
                chkValue3.Enabled = true;
                chkValue4.Enabled = true;
                chkValue5.Enabled = true;
            }
        }

        private NRScanModRoadQueryStater stater;
    }
}
