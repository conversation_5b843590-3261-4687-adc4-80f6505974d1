﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.BackgroundFunc
{
    public partial class BackgroundInfoForm : ChildForm
    {
        StringBuilder strbInfo = new StringBuilder();
        BackgroundFuncManager bgManager = BackgroundFuncManager.GetInstance();
        public BackgroundInfoForm()
        {
            InitializeComponent();
            bgManager.BackgroundInfoChanged += backgroundInfoChanged;
            Disposed += disposed;
        }

        private void miClearInfo_Click(object sender, EventArgs e)
        {
            strbInfo = new StringBuilder();
            edtInfo.Text = "";
        }

        private void backgroundInfoChanged(object sender, EventArgs e)
        {
            if (this.Visible)//该窗体可见时，将运行信息直接AppendText到TextBox控件中
            {
                backgroundInfoChangedShow();
            }
            else//该窗体不可见时，将运行信息记录到StringBuilder中（避免切换窗体时出现卡顿问题）
            {
                backgroundInfoChangedUnshow();
            }
        }
        private void backgroundInfoChangedShow()
        {
            if (edtInfo.Text.Length > 20480)
            {
                edtInfo.Text = "";
            }
            edtInfo.AppendText(bgManager.BackgroundInfo + "\r\n");
        }
        private void backgroundInfoChangedUnshow()
        {
            if (strbInfo.Length > 20480)
            {
                strbInfo = new StringBuilder();
            }
            strbInfo.AppendLine(bgManager.BackgroundInfo);
        }
        private void disposed(object sender, EventArgs e)
        {
            bgManager.BackgroundInfoChanged -= backgroundInfoChanged;
        }

        private void BackgroundInfoForm_VisibleChanged(object sender, EventArgs e)
        {
            if (this.Visible)
            {
                edtInfo.Text = strbInfo.ToString();
            }
            else
            {
                strbInfo = new StringBuilder();
                strbInfo.Append(edtInfo.Text);
            }
        }
    }
}
