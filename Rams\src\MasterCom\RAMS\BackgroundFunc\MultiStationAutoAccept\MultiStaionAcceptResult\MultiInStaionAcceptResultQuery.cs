﻿using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    class MultiInStaionAcceptResultQuery : MultiOutStaionAcceptResultQuery
    {
        Dictionary<int, InDoorBtsAcceptInfo> inDoorBtsAcceptInfoDic = new Dictionary<int, InDoorBtsAcceptInfo>();

        private static MultiInStaionAcceptResultQuery intance = null;
        public new static MultiInStaionAcceptResultQuery GetIntance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new MultiInStaionAcceptResultQuery();
                    }
                }
            }
            return intance;
        }
        protected MultiInStaionAcceptResultQuery()
            : base()
        {
        }
        public override string Name
        {
            get { return "LTE室分新站总体验收结果查询"; }
        }
        protected override void getReadyBeforeQuery()
        {
            inDoorBtsAcceptInfoDic.Clear();
        }
        protected override void initBackgroundImageDesc()
        {
            AddBackgroundInfoToResults(BackgroundResultList, ref inDoorBtsAcceptInfoDic);
        }
        public static void AddBackgroundInfoToResults(List<BackgroundResult> backgroundResultList
            , ref Dictionary<int, InDoorBtsAcceptInfo> btsAcceptInfoDic)
        {
            backgroundResultList.Sort(BackgroundResult.ComparerByISTimeDesc);

            foreach (BackgroundResult bgResult in backgroundResultList)
            {
                if (bgResult.StrDesc == "室外")
                {
                    continue;
                }

                DateTime bgResultTime = JavaDate.GetDateTimeFromMilliseconds(bgResult.ISTime * 1000L);
                LTECell lteCell = CellManager.GetInstance().GetLTECell(bgResultTime, bgResult.LAC, bgResult.CI);
                if (lteCell == null)
                {
                    reportBackgroundInfo(string.Format("预处理文件{0}未关联到目标小区。", bgResult.FileName));
                    continue;
                }

                try
                {
                    byte[] bytes = bgResult.GetImageValueBytes();
                    Dictionary<uint, object> kpiDic = MasterCom.RAMS.NewBlackBlock.KeyValueImageParser.FromImage(bytes);

                    InDoorCellAcceptInfo cellAcceptInfo = null;
                    cellAcceptInfo = getCurInDoorCellAcceptInfo(bgResultTime, lteCell, ref btsAcceptInfoDic);
                    cellAcceptInfo.AddAcceptKpiInfo(bgResult.FileName, kpiDic);
                }
                catch (Exception ex)
                {
                    reportBackgroundError(ex);
                }
            }

            foreach (InDoorBtsAcceptInfo btsInfo in btsAcceptInfoDic.Values)
            {
                btsInfo.CheckBtsIsAccordAccept();
            }

            upLoadCellCheckInfo(btsAcceptInfoDic);
        }
        private static void upLoadCellCheckInfo(Dictionary<int, InDoorBtsAcceptInfo> inDoorBtsAcceptInfoDic)
        {
            try
            {
                if (inDoorBtsAcceptInfoDic == null || inDoorBtsAcceptInfoDic.Count <= 0)
                {
                    return;
                }
                BackgroundFuncConfigManager bgCfgManager = BackgroundFuncConfigManager.GetInstance();
                UpLoadIndoorBtsKpiInfo_XJ ulQuery = new UpLoadIndoorBtsKpiInfo_XJ(bgCfgManager.StartTime, bgCfgManager.EndTime,
                    new List<InDoorBtsAcceptInfo>(inDoorBtsAcceptInfoDic.Values));
                ulQuery.Query();
            }
            catch (Exception ex)
            {
                reportBackgroundError(ex);
            }
        }
        private static InDoorCellAcceptInfo getCurInDoorCellAcceptInfo(DateTime bgTime, LTECell lteCell
            , ref Dictionary<int, InDoorBtsAcceptInfo> btsAcceptInfoDic)
        {
            InDoorBtsAcceptInfo btsAcceptInfo;
            if (!btsAcceptInfoDic.TryGetValue(lteCell.BelongBTS.BTSID, out btsAcceptInfo))
            {
                btsAcceptInfo = new InDoorBtsAcceptInfo(lteCell.BelongBTS);
                btsAcceptInfo.SN = btsAcceptInfoDic.Count + 1;
                btsAcceptInfo.AccpetTimePeriod.SetBeginTime(bgTime);
                btsAcceptInfoDic.Add(lteCell.BelongBTS.BTSID, btsAcceptInfo);
            }
            btsAcceptInfo.AccpetTimePeriod.SetEndTime(bgTime);

            InDoorCellAcceptInfo cellAcceptInfo;
            if (!btsAcceptInfo.CellsAcceptDic.TryGetValue(lteCell.CellID, out cellAcceptInfo))
            {
                cellAcceptInfo = new InDoorCellAcceptInfo(lteCell);
                btsAcceptInfo.CellsAcceptDic.Add(lteCell.CellID, cellAcceptInfo);
            }
            return cellAcceptInfo;
        }

        protected override void fireShowForm()
        {
            if (inDoorBtsAcceptInfoDic.Count == 0)
            {
                XtraMessageBox.Show("未查询到结果！");
                return;
            }
            MultiInStaionAcceptResultForm frm = MainModel.CreateResultForm(typeof(MultiInStaionAcceptResultForm)) as MultiInStaionAcceptResultForm;
            frm.FillData(new List<InDoorBtsAcceptInfo>(inDoorBtsAcceptInfoDic.Values));
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }
}
