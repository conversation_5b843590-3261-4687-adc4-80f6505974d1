﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Text;
using System.Threading;
using GMap.NET;
using MasterCom.MTGis;
using MasterCom.RAMS.ExMap;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;

namespace MasterCom.RAMS.ExMap
{
    class ExMapCellEmulateCovLayer : ExMapDrawBaseLayer
    {
        readonly MainModel mModel = null;
        private static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public ExMapCellEmulateCovLayer(MTExGMap exMap)
            : base(exMap)
        {
            mModel = MainModel.GetInstance();
        }

        public override string Alias
        {
            get { return "小区仿真覆盖"; }
        }

        public override void Draw(System.Drawing.Graphics g, PointLatLng ltPt, PointLatLng brPt)
        {
            try
            {
                if (mModel.cellEmulateShowItem==null)
                {
                    return;
                }
                NeedFreshFullImg = mModel.cellEmulateShowItem.exMapCellEmulateNeedFreshImg;
                doPrepareColorMatrixThread();

                doDrawCoverImg(g);
            }
            catch
            {
                //continue
            }
        }

        private readonly bool useWaitBox = false;
        private DbRect bounds;
        public bool NeedFreshFullImg = false;//是否数据发生了变化需要更新整个栅格
        private void doPrepareColorMatrixThread()
        {
            try
            {
                if (!NeedFreshFullImg)
                {
                    return;
                }
                bounds = new DbRect();
                bool first = true;
      
                RxlevGridLongLat rxlevGridLongLat = mModel.RxlevGridLongLat;
                foreach (GridLongLat g in rxlevGridLongLat.Rxlev70GllList)
                {
                    if (first)
                    {
                        bounds.x1 = g.fltlongitude;
                        bounds.x2 = g.fbrlongitude;
                        bounds.y1 = g.fbrlatitude;
                        bounds.y2 = g.fltlatitude;
                        first = false;
                    }
                    else
                    {
                        if (bounds.x1 > g.fltlongitude)
                        {
                            bounds.x1 = g.fltlongitude;
                        }
                        if (bounds.x2 < g.fbrlongitude)
                        {
                            bounds.x2 = g.fbrlongitude;
                        }
                        if (bounds.y1 > g.fbrlatitude)
                        {
                            bounds.y1 = g.fbrlatitude;
                        }
                        if (bounds.y2 < g.fltlatitude)
                        {
                            bounds.y2 = g.fltlatitude;
                        }
                    }
                }

                foreach (GridLongLat g in rxlevGridLongLat.Rxlev80GllList)
                {
                    if (first)
                    {
                        bounds.x1 = g.fltlongitude;
                        bounds.x2 = g.fbrlongitude;
                        bounds.y1 = g.fbrlatitude;
                        bounds.y2 = g.fltlatitude;
                        first = false;
                    }
                    else
                    {
                        if (bounds.x1 > g.fltlongitude)
                        {
                            bounds.x1 = g.fltlongitude;
                        }
                        if (bounds.x2 < g.fbrlongitude)
                        {
                            bounds.x2 = g.fbrlongitude;
                        }
                        if (bounds.y1 > g.fbrlatitude)
                        {
                            bounds.y1 = g.fbrlatitude;
                        }
                        if (bounds.y2 < g.fltlatitude)
                        {
                            bounds.y2 = g.fltlatitude;
                        }
                    }
                }

                foreach (GridLongLat g in rxlevGridLongLat.Rxlev85GllList)
                {
                    if (first)
                    {
                        bounds.x1 = g.fltlongitude;
                        bounds.x2 = g.fbrlongitude;
                        bounds.y1 = g.fbrlatitude;
                        bounds.y2 = g.fltlatitude;
                        first = false;
                    }
                    else
                    {
                        if (bounds.x1 > g.fltlongitude)
                        {
                            bounds.x1 = g.fltlongitude;
                        }
                        if (bounds.x2 < g.fbrlongitude)
                        {
                            bounds.x2 = g.fbrlongitude;
                        }
                        if (bounds.y1 > g.fbrlatitude)
                        {
                            bounds.y1 = g.fbrlatitude;
                        }
                        if (bounds.y2 < g.fltlatitude)
                        {
                            bounds.y2 = g.fltlatitude;
                        }
                    }
                }

                foreach (GridLongLat g in rxlevGridLongLat.Rxlev90GllList)
                {
                    if (first)
                    {
                        bounds.x1 = g.fltlongitude;
                        bounds.x2 = g.fbrlongitude;
                        bounds.y1 = g.fbrlatitude;
                        bounds.y2 = g.fltlatitude;
                        first = false;
                    }
                    else
                    {
                        if (bounds.x1 > g.fltlongitude)
                        {
                            bounds.x1 = g.fltlongitude;
                        }
                        if (bounds.x2 < g.fbrlongitude)
                        {
                            bounds.x2 = g.fbrlongitude;
                        }
                        if (bounds.y1 > g.fbrlatitude)
                        {
                            bounds.y1 = g.fbrlatitude;
                        }
                        if (bounds.y2 < g.fltlatitude)
                        {
                            bounds.y2 = g.fltlatitude;
                        }
                    }
                }

                foreach (GridLongLat g in rxlevGridLongLat.Rxlev94GllList)
                {
                    if (first)
                    {
                        bounds.x1 = g.fltlongitude;
                        bounds.x2 = g.fbrlongitude;
                        bounds.y1 = g.fbrlatitude;
                        bounds.y2 = g.fltlatitude;
                        first = false;
                    }
                    else
                    {
                        if (bounds.x1 > g.fltlongitude)
                        {
                            bounds.x1 = g.fltlongitude;
                        }
                        if (bounds.x2 < g.fbrlongitude)
                        {
                            bounds.x2 = g.fbrlongitude;
                        }
                        if (bounds.y1 > g.fbrlatitude)
                        {
                            bounds.y1 = g.fbrlatitude;
                        }
                        if (bounds.y2 < g.fltlatitude)
                        {
                            bounds.y2 = g.fltlatitude;
                        }
                    }
                }

                foreach (GridLongLat g in rxlevGridLongLat.Rxlev95GllList)
                {
                    if (first)
                    {
                        bounds.x1 = g.fltlongitude;
                        bounds.x2 = g.fbrlongitude;
                        bounds.y1 = g.fbrlatitude;
                        bounds.y2 = g.fltlatitude;
                        first = false;
                    }
                    else
                    {
                        if (bounds.x1 > g.fltlongitude)
                        {
                            bounds.x1 = g.fltlongitude;
                        }
                        if (bounds.x2 < g.fbrlongitude)
                        {
                            bounds.x2 = g.fbrlongitude;
                        }
                        if (bounds.y1 > g.fbrlatitude)
                        {
                            bounds.y1 = g.fbrlatitude;
                        }
                        if (bounds.y2 < g.fltlatitude)
                        {
                            bounds.y2 = g.fltlatitude;
                        }
                    }
                }

                foreach (GridLongLat g in rxlevGridLongLat.RxlevNonCoverGllList)
                {
                    if (first)
                    {
                        bounds.x1 = g.fltlongitude;
                        bounds.x2 = g.fbrlongitude;
                        bounds.y1 = g.fbrlatitude;
                        bounds.y2 = g.fltlatitude;
                        first = false;
                    }
                    else
                    {
                        if (bounds.x1 > g.fltlongitude)
                        {
                            bounds.x1 = g.fltlongitude;
                        }
                        if (bounds.x2 < g.fbrlongitude)
                        {
                            bounds.x2 = g.fbrlongitude;
                        }
                        if (bounds.y1 > g.fbrlatitude)
                        {
                            bounds.y1 = g.fbrlatitude;
                        }
                        if (bounds.y2 < g.fltlatitude)
                        {
                            bounds.y2 = g.fltlatitude;
                        }
                    }
                }

                yGapPixel = xGapPixel = 1;
                prepareImage();
                mModel.cellEmulateShowItem.exMapCellEmulateNeedFreshImg = false;
            }
            catch
            {
                mModel.cellEmulateShowItem.exMapCellEmulateNeedFreshImg = false;
            }
            finally
            {
                try
                {
                    if (useWaitBox)
                    {
                        Thread.Sleep(2000);
                        WaitBox.Close();
                    }

                }
                catch
                {
                    //continue
                }
            }
        }

        private int xGapPixel = 3;
        private int yGapPixel = 3;
        public double GRID_SPAN_LONG = CD.ATOM_SPAN_LONG * 2 * 3;//当前运算中使用的值
        public double GRID_SPAN_LAT = CD.ATOM_SPAN_LAT * 2 * 3;//当前运算中使用的值

        #region 生成图像
        Bitmap img = null;
        public void prepareImage()
        {
            try
            {
                RxlevGridLongLat rxlevGridLongLat = mModel.RxlevGridLongLat;
                GRID_SPAN_LONG = mModel.cellEmulateShowItem.gridSpanLon;
                GRID_SPAN_LAT = mModel.cellEmulateShowItem.gridSpanLat;
                int columnCount = (int)(bounds.Width() / GRID_SPAN_LONG + 0.5);
                int rowCount = (int)(bounds.Height() / GRID_SPAN_LAT + 0.5);
                if (rowCount < 1 || columnCount < 1)
                {
                    return;
                }
                if (img != null)
                {
                    img.Dispose();
                }
                img = new Bitmap(columnCount * xGapPixel, yGapPixel * rowCount, System.Drawing.Imaging.PixelFormat.Format32bppArgb);
                Graphics graphics = Graphics.FromImage(img);

                drawGridColorUnit(graphics, mModel.cellEmulateShowItem.rxlev70Color, rxlevGridLongLat.Rxlev70GllList);

                if (mModel.cellEmulateShowItem.visibleRxlevTopN > 1)
                {
                    drawGridColorUnit(graphics, mModel.cellEmulateShowItem.rxlev80Color, rxlevGridLongLat.Rxlev80GllList);
                }

                if (mModel.cellEmulateShowItem.visibleRxlevTopN > 2)
                {
                    drawGridColorUnit(graphics, mModel.cellEmulateShowItem.rxlev85Color, rxlevGridLongLat.Rxlev85GllList);
                }

                if (mModel.cellEmulateShowItem.visibleRxlevTopN > 3)
                {
                    drawGridColorUnit(graphics, mModel.cellEmulateShowItem.rxlev90Color, rxlevGridLongLat.Rxlev90GllList);
                }

                if (mModel.cellEmulateShowItem.visibleRxlevTopN > 4)
                {
                    drawGridColorUnit(graphics, mModel.cellEmulateShowItem.rxlev94Color, rxlevGridLongLat.Rxlev94GllList);
                }

                if (mModel.cellEmulateShowItem.visibleRxlevTopN > 5)
                {
                    drawGridColorUnit(graphics, mModel.cellEmulateShowItem.rxlev95Color, rxlevGridLongLat.Rxlev95GllList);
                }

                drawGridColorUnit(graphics, mModel.cellEmulateShowItem.rxlevNonCovColor, rxlevGridLongLat.RxlevNonCoverGllList);

                graphics.Save();
                graphics.Dispose();
            }
            catch (Exception e)
            {
                log.Error("Prepare Image Error :" + e.Message);
            }
        }

        private void drawGridColorUnit(Graphics graphics, Color color, List<GridLongLat> coverGllList)
        {
            foreach (GridLongLat ll in coverGllList)
            {
                int rAt, cAt;
                getInPlace(ll, bounds, out rAt, out cAt);
                drawGridColorUnitToImg(color, graphics, cAt * xGapPixel, rAt * yGapPixel, xGapPixel, yGapPixel);
            }
        }
        #endregion


        /// <summary>
        /// 获取所在矩阵中的位置 【rAt，cAt】 
        /// </summary>
        /// <param name="gll"></param>
        /// <param name="bounds"></param>
        /// <param name="xAt"></param>
        /// <param name="yAt"></param>
        private void getInPlace(GridLongLat gll, DbRect bounds, out int rAt, out int cAt)
        {
            double xDis = gll.fltlongitude - bounds.x1;
            cAt = (int)(Math.Round(xDis / this.GRID_SPAN_LONG));
            double yDis = bounds.y2 - gll.fltlatitude;
            rAt = (int)(Math.Round(yDis / this.GRID_SPAN_LAT));
        }


        private void drawGridColorUnitToImg(Color gridColor, Graphics graphics, int left, int top, int width, int height)
        {
            Color color = Color.FromArgb(135, gridColor);
            graphics.FillRectangle(new SolidBrush(color), left, top, width, height);
        }

        private void doDrawCoverImg(Graphics graphics)
        {
            try
            {
                if (mModel.RxlevGridLongLat.Rxlev70GllList.Count == 0 && mModel.RxlevGridLongLat.Rxlev80GllList.Count == 0 && mModel.RxlevGridLongLat.Rxlev85GllList.Count == 0
                    && mModel.RxlevGridLongLat.Rxlev90GllList.Count == 0 && mModel.RxlevGridLongLat.Rxlev94GllList.Count == 0 && mModel.RxlevGridLongLat.Rxlev95GllList.Count == 0
                    && mModel.RxlevGridLongLat.RxlevNonCoverGllList.Count == 0)
                {
                    return;
                }
                if (img != null)
                {
                    int columnCount = (int)(bounds.Width() / GRID_SPAN_LONG + 0.5);
                    int rowCount = (int)(bounds.Height() / GRID_SPAN_LAT + 0.5);
                    if (rowCount < 1 || columnCount < 1)
                    {
                        return;
                    }
                    PointLatLng ulPt = new PointLatLng(bounds.y2, bounds.x1);
                    PointLatLng urPt = new PointLatLng(bounds.y2, bounds.x2);
                    PointLatLng llPt = new PointLatLng(bounds.y1, bounds.x1);

                    GPoint ulGPoint = exMap.FromLatLngToLocalAdaptered(ulPt);
                    GPoint urGpoint = exMap.FromLatLngToLocalAdaptered(urPt);
                    GPoint llGpoint = exMap.FromLatLngToLocalAdaptered(llPt);

                    PointF ulCorner = new PointF(ulGPoint.X, ulGPoint.Y);
                    PointF urCorner = new PointF(urGpoint.X, urGpoint.Y);
                    PointF llCorner = new PointF(llGpoint.X, llGpoint.Y);
                    PointF[] destPara = { ulCorner, urCorner, llCorner };

                    int imgXSize = columnCount * xGapPixel;
                    int imgYSize = rowCount * yGapPixel;

                    float x0 = 0;
                    float y0 = 0;
                    float w0 = imgXSize;
                    float h0 = imgYSize;

                    // Create rectangle for source image.
                    RectangleF srcRect = new RectangleF(x0, y0, w0, h0);
                    GraphicsUnit units = GraphicsUnit.Pixel;

                    float opqF = (float)(MapFormCompareHisShowLayer.OpaqueValue) / 255;
                    float[][] ptsArray ={ 
                        new float[] {1, 0, 0, 0, 0},
                        new float[] {0, 1, 0, 0, 0},
                        new float[] {0, 0, 1, 0, 0},
                    new float[] {0, 0, 0, opqF, 0}, //注意：此处为0.5f，图像为半透明
                    new float[] {0, 0, 0, 0, 1}};
                    ColorMatrix clrMatrix = new ColorMatrix(ptsArray);
                    ImageAttributes imgAttributes = new ImageAttributes();
                    //设置图像的颜色属性
                    imgAttributes.SetColorMatrix(clrMatrix, ColorMatrixFlag.Default,
                    ColorAdjustType.Bitmap);

                    graphics.DrawImage(img, destPara, srcRect, units, imgAttributes);

                    return;
                }
            }
            catch
            {
                //continue
            }
        }

    }
}
