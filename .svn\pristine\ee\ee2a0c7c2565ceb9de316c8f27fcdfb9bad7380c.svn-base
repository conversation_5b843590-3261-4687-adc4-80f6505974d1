﻿using DevExpress.XtraEditors;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.NewBlackBlock;
using MasterCom.Util;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.BackgroundFunc
{
    public static class BackgroundResultToWord
    {
        private static bool isAutoExport = false;
        public static void ExportToWord(List<BackgroundResult> bgResults, string saveFileName = "")
        {
            try
            {
                if (MainModel.GetInstance().MainForm.GetMapForm() != null)
                {
                    if (!string.IsNullOrEmpty(saveFileName))
                    {
                        isAutoExport = true;
                        exportWordParam(bgResults, saveFileName, 10000);
                    }
                    else
                    {
                        isAutoExport = false;
                        NumberInputBox box = new NumberInputBox("选择比例", "请选择比例", 10000, 1000000, 10, 100);
                        if (box.ShowDialog() == DialogResult.OK)
                        {
                            SaveFileDialog saveFileDlg = new SaveFileDialog();
                            saveFileDlg.Title = "选择要保存文档的路径";
                            saveFileDlg.RestoreDirectory = true;
                            saveFileDlg.Filter = FilterHelper.Doc;
                            if (saveFileDlg.ShowDialog() == DialogResult.OK)
                            {
                                exportWordParam(bgResults, saveFileDlg.FileName, (double)box.Value);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (isAutoExport)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
                }
                else
                {
                    XtraMessageBox.Show("导出报告出错！\r\n原因：" + ex.Message, "提示");
                }
            }
        }
        private static void exportWordParam(List<BackgroundResult> bgResults, string saveFileName, double mapScale)
        {
            if (bgResults != null && bgResults.Count > 0)
            {
                MainModel mainModel = MainModel.GetInstance();
                mainModel.CurBackgroundResultList = bgResults;
                mainModel.MainForm.GetMapForm().FireAddBackgroundResultLayer();

                string dirPath = Path.Combine(Application.StartupPath, "BackgroundPict");
                if (!Directory.Exists(dirPath))
                {
                    Directory.CreateDirectory(dirPath);
                }
                foreach (string filePath in Directory.GetFiles(dirPath))
                {
                    try
                    {
                        File.Delete(filePath);
                    }
                    catch
                    {
                        //continue
                    }
                }

                BlockExportWordParam wordParam = new BlockExportWordParam();
                wordParam.MapForm = mainModel.MainForm.GetMapForm();
                wordParam.BlockItems = bgResults;
                wordParam.SaveDocFilePath = saveFileName;
                wordParam.BlockPicDirPath = dirPath;
                wordParam.MapScale = mapScale;
                if (isAutoExport)
                {
                    printBlackBlocks2BMP(wordParam);
                }
                else
                {
                    WaitBox.Show(printBlackBlocks2BMP, wordParam);
                }
            }
        }
        private static void printBlackBlocks2BMP(object param)
        {
            System.Threading.Thread.Sleep(20);
            BlockExportWordParam wordParam = (BlockExportWordParam)param;
            printBlackBlocks2BMP(wordParam.MapForm, wordParam.BlockItems, wordParam.SaveDocFilePath, wordParam.BlockPicDirPath, wordParam.MapScale);
        }
        private static void printBlackBlocks2BMP(MapForm mf, IList bbItems, string saveFileName, string picPath, double mapScale)
        {
            try
            {
                if (isAutoExport)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("正在导出问题点图层报告...");
                }
                else
                {
                    WaitBox.Text = "正在开始导出问题点报告...";
                }
                WordControl wordcontrol = getWordcontrol();

                if (bbItems.Count > 0 && bbItems[0] is BackgroundResult)
                {
                    BackgroundResult itemFirst = bbItems[0] as BackgroundResult;
                    wordcontrol.InsertText(itemFirst.SubFuncName, "标题 1");
                }

                dealBackgroundResult(mf, bbItems, picPath, mapScale, wordcontrol);
                wordcontrol.SavedAsWord(saveFileName);

                if (!isAutoExport
                    && XtraMessageBox.Show("导出报告成功！现在打开报告文档吗?", "提示", MessageBoxButtons.YesNo) == DialogResult.Yes)
                {
                    System.Diagnostics.Process.Start(saveFileName);
                }
            }
            catch (Exception ex)
            {
                if (isAutoExport)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
                }
                else
                {
                    XtraMessageBox.Show("导出报告出错！\r\n原因：" + ex.Message, "提示");
                }
            }
            finally
            {
                if (!isAutoExport)
                {
                    WaitBox.Close();
                }
            }
        }

        private static void dealBackgroundResult(MapForm mf, IList bbItems, string picPath, double mapScale, WordControl wordcontrol)
        {
            int iLoop = 0;
            foreach (BackgroundResult item in bbItems)
            {
                if (MainModel.GetInstance().BackgroundStopRequest)
                {
                    break;
                }
                ++iLoop;
                if (!isAutoExport)
                {
                    WaitBox.Text = "正在导出" + item.SubFuncName + "_" + item.SN + "的问题点...";
                    WaitBox.ProgressPercent = (int)(iLoop * 100.0 / bbItems.Count);
                }
                item.SN = iLoop;
                printBlackBlock2BMP(mf, item, wordcontrol, picPath, mapScale);
            }
        }

        private static WordControl getWordcontrol()
        {
            WordControl wordcontrol = null;
            if (System.IO.File.Exists(Application.StartupPath + "\\模板文档.doc"))
            {
                wordcontrol = new WordControl(Application.StartupPath + "\\模板文档.doc", false);
            }
            else
            {
                wordcontrol = new WordControl(false);
            }

            return wordcontrol;
        }

        private static void printBlackBlock2BMP(MapForm mf, BackgroundResult bgResult, WordControl wordcontrol, string picPath, double mapScale)
        {
            wordcontrol.NewLine();
            wordcontrol.InsertText(bgResult.SN.ToString(), "标题 2");

            if (bgResult.LongitudeMid > 0 && bgResult.LatitudeMid > 0)
            {
                MainModel.GetInstance().CurBackgroundResult = bgResult;
                DbPoint center = new DbPoint(bgResult.LongitudeMid, bgResult.LatitudeMid);
                mf.GoToView(center.x, center.y, (float)mapScale);
                System.Threading.Thread.Sleep(100);

                wordcontrol.NewLine();  //先执行这行处理正确，否则GoToView还没出发完就截图了。

                Bitmap bitMap = mf.DrawToBitmapDIY();

                string title = bgResult.SubFuncName + "_" + bgResult.SN;

                PictureOperator.CutForCustom(bitMap, string.Format(picPath + "\\" + title + ".jpg"),
                    100, 60, bitMap.Width - 200, bitMap.Height - 120, 0);

                System.Threading.Thread.Sleep(20);

                wordcontrol.InsertPicture(string.Format(picPath + "\\" + title + ".jpg"));
            }
            wordcontrol.NewLine();
            wordcontrol.InsertText("问题点描述：", "正文");
            wordcontrol.NewLine();
            wordcontrol.InsertText(bgResult.Desc, "正文");

            wordcontrol.NewLine();
            wordcontrol.NewLine();
            wordcontrol.NewLine();
        }

    }
}
