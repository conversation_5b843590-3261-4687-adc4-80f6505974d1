﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.Net
{
    public class DIYFileInfoData : DIYQueryFileInfoBase
    {
        public DIYFileInfoData(MainModel mainModel)
            : base(mainModel)
        {
            
            IsShowFileInfoForm = false;
        }
        public List<FileInfo> FlieInfoData { get; set; } = new List<FileInfo>();
        public override string Name
        {
            get { return "文件查询(全部)"; }
        }

        public override string IconName
        {
            get { return "Images/fileq.gif"; }
        }
        readonly bool findByName = false;
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, findByName ? 11003 : 11004, this.Name);
        }
        protected override void query()
        {
            FlieInfoData.Clear();
            base.query();
            FlieInfoData.AddRange(MainModel.FileInfos);
        }
    }
}
