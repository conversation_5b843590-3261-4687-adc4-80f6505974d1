using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CellWeakCoverByCellDirConditionDlg : BaseDialog
    {
        public CellWeakCoverByCellDirConditionDlg()
        {
            InitializeComponent();
            numJudgeScale.Enabled = chkJudgeByScale.Checked;
        }

        public void GetCondition(ZTDIYCellWeakCoverByCellDir_GScan.ZTDiyCellWeakCoverByCellDirCondition condition)
        {
            condition.rxLevMax = (int)numRxLevMax.Value;
            condition.judgeByScale = chkJudgeByScale.Checked;
            condition.judgeScale = (int)numJudgeScale.Value;
            condition.cellCoverDistance = (int)numCellCoverDistance.Value;
            condition.weakGridCount = (int)numWeakGridCount.Value;
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void chkJudgeByScale_CheckedChanged(object sender, EventArgs e)
        {
            numJudgeScale.Enabled = chkJudgeByScale.Checked;
        }
    }
}