<?xml version="1.0"?>
<Configs>
  <Config name="MultiCompareCfg">
    <Item name="Set" typeName="IDictionary">
      <Item typeName="IList" key="Periods">
        <Item typeName="IDictionary">
          <Item typeName="String" key="BeginTime">635262912000000000</Item>
          <Item typeName="String" key="EndTime">635265503999990000</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="BeginTime">635289696000000000</Item>
          <Item typeName="String" key="EndTime">635316479999990000</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="BeginTime">635812416000000000</Item>
          <Item typeName="String" key="EndTime">635899679999990000</Item>
        </Item>
      </Item>
      <Item typeName="String" key="ReprortSavePath">C:\Users\<USER>\Desktop\营销区域网格图层</Item>
      <Item typeName="Boolean" key="IsFilterFile">False</Item>
      <Item typeName="String" key="StrFileNameFilter" />
      <Item typeName="Double" key="DistanceEvt">51</Item>
      <Item typeName="Boolean" key="IsAnaMessageCellInfo">True</Item>
      <Item typeName="IList" key="SelectEvtIDSet">
        <Item typeName="Int32">1008</Item>
        <Item typeName="Int32">1009</Item>
        <Item typeName="Int32">1275</Item>
        <Item typeName="Int32">897</Item>
        <Item typeName="Int32">1242</Item>
        <Item typeName="Int32">1250</Item>
        <Item typeName="Int32">1134</Item>
        <Item typeName="Int32">871</Item>
        <Item typeName="Int32">872</Item>
        <Item typeName="Int32">1169</Item>
        <Item typeName="Int32">1172</Item>
        <Item typeName="Int32">1245</Item>
        <Item typeName="Int32">1107</Item>
        <Item typeName="Int32">1319</Item>
        <Item typeName="Int32">1109</Item>
        <Item typeName="Int32">1132</Item>
        <Item typeName="Int32">1274</Item>
        <Item typeName="Int32">1272</Item>
        <Item typeName="Int32">1078</Item>
        <Item typeName="Int32">1079</Item>
        <Item typeName="Int32">1092</Item>
        <Item typeName="Int32">1093</Item>
      </Item>
      <Item typeName="IList" key="DistrictIDSet" />
      <Item typeName="IList" key="ProjectIDSet">
        <Item typeName="Int32">5</Item>
        <Item typeName="Int32">4</Item>
        <Item typeName="Int32">3</Item>
        <Item typeName="Int32">7</Item>
        <Item typeName="Int32">9</Item>
        <Item typeName="Int32">2</Item>
        <Item typeName="Int32">1</Item>
        <Item typeName="Int32">8</Item>
        <Item typeName="Int32">6</Item>
        <Item typeName="Int32">10</Item>
      </Item>
      <Item typeName="IList" key="ServiceIDSet">
        <Item typeName="Int32">34</Item>
        <Item typeName="Int32">33</Item>
      </Item>
    </Item>
  </Config>
</Configs>