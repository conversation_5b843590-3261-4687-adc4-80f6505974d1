﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTCsfbCellJudgeSetRadiusForm : BaseDialog
    {
        public ZTCsfbCellJudgeSetRadiusForm()
        {
            InitializeComponent();
        }

        public ZTCsfbCellJudgeSetRadiusCondition GetCondition()
        {
            ZTCsfbCellJudgeSetRadiusCondition condition = new ZTCsfbCellJudgeSetRadiusCondition();
            condition.Radius = (int)numSiteRadius.Value;
            condition.RxLev = (int)numSiteRxLev.Value;
            return condition;
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
