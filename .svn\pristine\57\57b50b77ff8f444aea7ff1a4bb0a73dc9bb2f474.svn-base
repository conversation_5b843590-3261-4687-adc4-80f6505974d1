﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class DIYFastFadingByRegion_GSCAN : DIYAnalyseFilesOneByOneByRegion
    {
        private GSMFreqBandType bandType = GSMFreqBandType.All;
        protected int rxLevDValue = 8;
        protected int secondLast = 10;
        protected int secondFading = 5;
        protected int rxLevDValueFading = 15;
        public DIYFastFadingByRegion_GSCAN(MainModel mainModel)
            : base(mainModel)
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.GSM_SCAN);
        }

        public override string Name
        {
            get { return "场强快衰"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 15000, 15026, this.Name);//////
        }

        protected override void fireShowForm()
        {
            if (MainModel.FastFadingList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的数据。");
                return;
            }
            StrongCellDeficiencyForm frm = MainModel.GetInstance().CreateResultForm(typeof(StrongCellDeficiencyForm)) as StrongCellDeficiencyForm;
            frm.FillData();
            frm.Visible = true;
            frm.BringToFront();
        }

        readonly FastFadingDlg_GSCAN conditionDlg = new FastFadingDlg_GSCAN();
        protected override bool getCondition()
        {
            if (conditionDlg.ShowDialog() == DialogResult.OK)
            {
                int band = 0;
                conditionDlg.GetFilterCondition(out band, out rxLevDValue, out secondLast, out secondFading, out rxLevDValueFading);
                bandType = (GSMFreqBandType)band;
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            MainModel.ClearFastFading();
        }

        protected override void analyseFiles()
        {
            try
            {
                List<FileInfo> files = new List<FileInfo>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (ServiceTypes.Count > 0 && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                    {
                        continue;
                    }
                    files.Add(fileInfo);
                }
                clearDataBeforeAnalyseFiles();

                foreach (FileInfo fileInfo in files)
                {
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    replay();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }
        /// <summary>
        /// 小区持续列表（小区名，小区持续对象）
        /// </summary>
        protected Dictionary<string, CellLast> cellLastDic = new Dictionary<string, CellLast>();
        /// <summary>
        /// 满足持续条件的小区列表（采样点序号，（小区名，小区持续对象））
        /// </summary>
        protected Dictionary<int, Dictionary<string, CellLast>> tpCellValidDic = new Dictionary<int, Dictionary<string, CellLast>>();

        protected void clearIndermediateVariable()
        {
            cellLastDic.Clear();
            tpCellValidDic.Clear();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    clearIndermediateVariable();
                    dealTPInfo(testPointList);
                }
            }
            catch
            {
                //continue
            }
        }

        private void dealTPInfo(List<TestPoint> testPointList)
        {
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];
                if (isValidTestPoint(testPoint))
                {
                    List<int> indexLst = testPoint.GetGSMScanIdxSpecify(bandType);
                    if (indexLst.Count > 0)
                    {
                        dealIndexLst(testPointList, i, testPoint, indexLst);
                    }
                }
                else
                {
                    clearIndermediateVariable();
                }
            }
        }

        private void dealIndexLst(List<TestPoint> testPointList, int i, TestPoint testPoint, List<int> indexLst)
        {
            Dictionary<string, float> cellRxLevDic = new Dictionary<string, float>();
            float? rxLevMax = (float?)testPoint["GSCAN_RxLev", indexLst[0]];
            foreach (int j in indexLst)
            {
                float? rxLev = (float?)testPoint["GSCAN_RxLev", j];
                if (rxLev == null || rxLev > -10 || rxLev < -120)
                {
                    break;
                }
                int? bcch = (int?)testPoint["GSCAN_BCCH", j];
                int? bsic = (int?)testPoint["GSCAN_BSIC", j];
                Cell cell = CellManager.GetInstance().GetNearestCell(testPoint.DateTime, (short)bcch, (byte)bsic, testPoint.Longitude, testPoint.Latitude);
                if (cell != null)
                {
                    cellRxLevDic[cell.Name] = (float)rxLev;
                    try
                    {
                        judgeCell(rxLevMax, rxLev, cell.Name, testPointList, i);
                    }
                    catch
                    {
                        //continue
                    }
                }
            }

            judgeTestPoint(testPointList, i, cellRxLevDic);
        }

        /// <summary>
        /// 4件事：
        /// 1.与一强相差8db的放入小区队列；
        /// 2.与一强相差8db持续满10秒的小区放入对象小区队列；
        /// 3.与一强相差8db并且前一个采样点中有该小区，延续到当前采样点；
        /// 4.如果与一强相差大于8db，小区队列中移除该小区。
        /// </summary>
        /// <param name="rxLevMax">一强场强</param>
        /// <param name="rxLev">当前小区场强</param>
        /// <param name="cell">当前小区名</param>
        /// <param name="testPointList">采样点列表</param>
        /// <param name="curIndex">当前采样点序号</param>
        protected void judgeCell(float? rxLevMax, float? rxLev, string cell, List<TestPoint> testPointList, int curIndex)
        {
            TestPoint testPoint = testPointList[curIndex];
            if (rxLevMax - rxLev <= rxLevDValue)    //与一强相差8db内
            {
                if (!cellLastDic.ContainsKey(cell))
                {
                    CellLast cellLast = new CellLast();
                    cellLastDic[cell] = cellLast;
                }
                cellLastDic[cell].Add(testPoint, (float)rxLev);
                if (cellLastDic[cell].SecondLast >= secondLast) //持续10秒，满足前提条件
                {
                    if (!tpCellValidDic.ContainsKey(curIndex))
                    {
                        Dictionary<string, CellLast> cellValidDic = new Dictionary<string, CellLast>();
                        tpCellValidDic[curIndex] = cellValidDic;
                    }
                    tpCellValidDic[curIndex][cell] = cellLastDic[cell];
                    cellLastDic.Remove(cell);
                }

                //更新已经满10秒，未形成快衰的点，延续到下个点判断
                List<int> tpList = new List<int>(tpCellValidDic.Keys);
                foreach (int index in tpList)
                {
                    if (index == curIndex - 1)//当前点前一个采样点
                    {
                        dealSameTPData(rxLev, cell, curIndex, testPoint, index);
                    }
                }
                return;
            }

            //未满10秒移除
            if (cellLastDic.ContainsKey(cell))
            {
                cellLastDic.Remove(cell);
            }
        }

        private void dealSameTPData(float? rxLev, string cell, int curIndex, TestPoint testPoint, int index)
        {
            List<string> tmpCellList = new List<string>(tpCellValidDic[index].Keys);
            foreach (string cellTP in tmpCellList)
            {
                if (cell == cellTP)
                {
                    //前一个采样点中小区延续到当前采样点
                    if (!tpCellValidDic.ContainsKey(curIndex))
                    {
                        Dictionary<string, CellLast> cellValidDic = new Dictionary<string, CellLast>();
                        tpCellValidDic[curIndex] = cellValidDic;
                    }
                    if (!tpCellValidDic[curIndex].ContainsKey(cell))
                    {
                        tpCellValidDic[curIndex][cell] = tpCellValidDic[index][cell];
                    }
                    tpCellValidDic[curIndex][cell].Add(testPoint, (float)rxLev);

                    //前一个采样点中移除该小区，如果移除完采样点没有其他小区，移除采样点
                    tpCellValidDic[index].Remove(cell);
                    if (tpCellValidDic[index].Count == 0)
                    {
                        tpCellValidDic.Remove(index);
                    }
                }
            }
        }

        /// <summary>
        /// 3件事：
        /// 1.不连续的小区从小区持续队列移除；
        /// 2.中断或者5秒内未达到快衰条件的从对象小区队列中移除；
        /// 3.判断对象小区是否形成快衰，形成的移除；
        /// </summary>
        /// <param name="testPointList">采样点列表</param>
        /// <param name="curIndex">当前采样点序号</param>
        /// <param name="cellRxLevDic">当前采样点小区列表</param>
        protected void judgeTestPoint(List<TestPoint> testPointList, int curIndex, Dictionary<string, float> cellRxLevDic)
        {
            try
            {
                TestPoint testPoint = testPointList[curIndex];
                //不连续的小区移除
                List<string> removeCellList = new List<string>(cellLastDic.Keys);
                foreach (string cell in removeCellList)
                {
                    if (!cellRxLevDic.ContainsKey(cell))
                    {
                        cellLastDic.Remove(cell);
                    }
                }
                
                //满足前提条件小区，超过5秒未衰减达-15db的移除
                List<int> removeTPList = new List<int>(tpCellValidDic.Keys);
                foreach (int i in removeTPList)
                {
                    if (testPoint.Time - testPointList[i].Time > secondFading)
                    {
                        tpCellValidDic.Remove(i);
                        continue;
                    }
                    filterTpCell(cellRxLevDic, i);
                }

                //判断是否形成快衰,形成的保存并从列表移除
                removeTPList = new List<int>(tpCellValidDic.Keys);
                foreach (int index in removeTPList)
                {
                    if (index == curIndex)//刚形成的对象小区，下个采样点开始判断
                    {
                        continue;
                    }
                    Dictionary<string, CellLast> cellValidDic = tpCellValidDic[index];
                    FastFading fastFading = getFastFading(cellRxLevDic, testPoint, cellValidDic);
                    if (fastFading != null)
                    {
                        tpCellValidDic.Remove(index);
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void filterTpCell(Dictionary<string, float> cellRxLevDic, int i)
        {
            List<string> cellList = new List<string>(tpCellValidDic[i].Keys);
            foreach (string cell in cellList)
            {
                //中断或者超过10db
                if (!cellRxLevDic.ContainsKey(cell))
                {
                    tpCellValidDic[i].Remove(cell);
                }
            }
            if (tpCellValidDic[i].Count == 0)
            {
                tpCellValidDic.Remove(i);
            }
        }

        private FastFading getFastFading(Dictionary<string, float> cellRxLevDic, TestPoint testPoint, Dictionary<string, CellLast> cellValidDic)
        {
            FastFading fastFading = null;
            foreach (string cell in cellRxLevDic.Keys)
            {
                if (cellValidDic.ContainsKey(cell))
                {
                    CellLast cellLast = cellValidDic[cell];
                    if (cellLast.RxLevLastAvg - cellRxLevDic[cell] >= rxLevDValueFading)
                    {
                        if (fastFading == null)
                        {
                            fastFading = new FastFading(testPoint);
                            MainModel.FastFadingList.Add(fastFading);
                        }
                        fastFading.AddCell(cell);
                    }
                }
            }

            return fastFading;
        }

        protected override void getResultsAfterQuery()
        {
            // Method intentionally left empty.
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (Condition.Geometorys != null && Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
                {
                    return true;
                }
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        protected class CellLast
        {
            public CellLast()
            {
            }
            private readonly List<TestPoint> testPointList = new List<TestPoint>();
            private readonly List<float> rxLevList = new List<float>();
            /// <summary>
            /// 持续时间
            /// </summary>
            public int SecondLast
            {
                get
                {
                    if (testPointList.Count > 1)
                    {
                        return testPointList[testPointList.Count - 1].Time - testPointList[0].Time;
                    }
                    else
                    {
                        return 0;
                    }
                }
            }

            /// <summary>
            /// 持续满10秒后的平均场强
            /// </summary>
            public float RxLevLastAvg
            {
                get
                {
                    float sum = 0;
                    foreach (float rxLev in rxLevList)
                    {
                        sum += rxLev;
                    }
                    return sum / rxLevList.Count;
                }
            }
            /// <summary>
            /// 持续满10秒时的时间
            /// </summary>
            public float TimeLast
            {
                get
                {
                    if (testPointList.Count > 0)
                    {
                        return testPointList[testPointList.Count - 1].Time;
                    }
                    return 0;
                }
            }

            public string FileName
            {
                get { return testPointList[0].FileName; }
            }

            public double LongitudeMid
            {
                get { return testPointList[(testPointList.Count / 2)].Longitude; }
            }

            public double LatitudeMid
            {
                get { return testPointList[(testPointList.Count / 2)].Latitude; }
            }

            public void Add(TestPoint tp, float rxLev)
            {
                testPointList.Add(tp);
                rxLevList.Add(rxLev);
            }
        }
    }

    /// <summary>
    /// 场强快衰
    /// </summary>
    public class FastFading
    {
        public FastFading(TestPoint tp)
        {
            this.TestPoint = tp;
        }

        public TestPoint TestPoint { get; set; }

        public string FileName
        {
            get { return TestPoint.FileName; }
        }

        public int Time
        {
            get { return TestPoint.Time; }
        }

        public short Millisecond
        {
            get { return TestPoint.Millisecond; }
        }

        public DateTime DateTime
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(Time * 1000L); }
        }

        public string DateTimeStringWithMillisecond
        {
            get
            {
                return String.Format("{0}.{1:d3}", DateTime.ToString("yy-MM-dd HH:mm:ss"), Millisecond);
            }
        }

        public double Longitude
        {
            get { return TestPoint.Longitude; }
        }

        public double Latitude
        {
            get { return TestPoint.Latitude; }
        }

        public List<string> cellList { get; set; } = new List<string>();

        public string Cells
        {
            get
            {
                StringBuilder cellString = new StringBuilder();
                foreach (string cell in cellList)
                {
                    if (cellString.Length == 0)
                    {
                        cellString.Append(" | ");
                    }
                    cellString.Append(cell);
                }
                return cellString.ToString();
            }
        }

        public int CellCount
        {
            get { return cellList.Count; }
        }

        public void AddCell(string cell)
        {
            if (!cellList.Contains(cell))
            {
                cellList.Add(cell);
            }
        }
        
        public string RoadDesc { get; set; } = "";

        public bool WithIn(double x1, double y1, double x2, double y2)
        {
            if (Longitude < x1 || Longitude > x2 || Latitude < y1 || Latitude > y2)
            {
                return false;
            }
            return true;
        }
    }

    public class GridFastFading : GridBase
    {
        public GridFastFading(double ltLongitude, double ltLatitude)
            : base(ltLongitude, ltLatitude)
        {
        }

        public void AddFastFading(FastFading ff)
        {
            for (int i = 0; i < ff.CellCount; i++)
            {
                string cell = ff.cellList[i];
                if (!cellList.Contains(cell))
                {
                    cellList.Add(cell);
                }
            }
        }

        public List<string> cellList { get; set; } = new List<string>();
        public string Cells
        {
            get
            {
                StringBuilder cellString = new StringBuilder();
                foreach (string cell in cellList)
                {
                    if (cellString.Length == 0)
                    {
                        cellString.Append(" | ");
                    }
                    cellString.Append(cell);
                }
                return cellString.ToString();
            }
        }
    }
}
