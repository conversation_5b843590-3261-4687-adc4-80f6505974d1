﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat;
using MasterCom.Util;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.KPI_Statistics
{
    public partial class ReportAndWhiteListSetDlg : BaseDialog
    {
        ReportAndWhiteListCondition cond = new ReportAndWhiteListCondition();
        List<TestRound> testRounds = new List<TestRound>();
        public ReportAndWhiteListSetDlg()
        {
            InitializeComponent();

#if PermissionControl_Func || DEBUG
            if (MainModel.User.HasFunctionRight(TestRoundListForm.FuncId))
            {
                btnTestRoundSet.Visible = true;
            }
#endif
            reportFilter.initReportPicker();
        }

        private void lnkReloadRpt_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            KPIReportManager.Instance.Init();
            reportFilter.initReportPicker();
        }

        public void SetCondition(ReportAndWhiteListCondition condition, List<TestRound> testRoundList)
        {
            this.cond = condition;
            if (cond != null)
            {
                chkLte.Checked = cond.GridStatSetting_LTE.IsChecked;
                chkOther.Checked = cond.GridStatSetting_Other.IsChecked;
                //if (cond.Report != null)
                //{
                //    reportFilter.listBoxCtrData.SelectedItem = cond.Report;
                //}
                chkAllParam.Checked = cond.IsQueryAllParams;
                chkShowFileInfoForm.Checked = cond.IsShowFileInfoForm;
                drawLableTipText(true);
            }

            fillTestRound(testRoundList);
            fillServiceView();
        }
        private void fillServiceView()
        {
            CategoryEnumItem[] svcItems = ((CategoryEnum)CategoryManager.GetInstance()["ServiceType"]).Items;
            fillCategoryView(lvServiceLte, cond != null ? cond.GridStatSetting_LTE.ServiceList : null, svcItems);
            fillCategoryView(lvServiceOther, cond != null ? cond.GridStatSetting_Other.ServiceList : null, svcItems);
            lblSvcCntLte.Text = "[" + lvServiceLte.Items.Count.ToString() + "]";
            lblSvcCntOther.Text = "[" + lvServiceOther.Items.Count.ToString() + "]";

            MapFormItemSelection itemSelection = mainModel.MainForm.GetMapForm().ItemSelection;
            ItemSelectionPanel servPanelLte = new ItemSelectionPanel(dropDownSvcLte, lvServiceLte, lblSvcCntLte, itemSelection, "ServiceType", true);
            ItemSelectionPanel servPanelOther = new ItemSelectionPanel(dropDownSvcOther, lvServiceOther, lblSvcCntOther, itemSelection, "ServiceType", true);
            servPanelLte.FreshItems();
            servPanelOther.FreshItems();
            dropDownSvcLte.Items.Add(new ToolStripControlHost(servPanelLte));
            dropDownSvcOther.Items.Add(new ToolStripControlHost(servPanelOther));
        }

        private void fillTestRound(List<TestRound> testRoundList)
        {
            this.testRounds = testRoundList;
            chkCbxUnWhiteListTime.Properties.Items.Clear();
            chkCbxWhiteListTime.Properties.Items.Clear();
            foreach (TestRound round in testRoundList)
            {
                if (cond != null)
                {
                    chkCbxUnWhiteListTime.Properties.Items.Add(round, isPeroidChecked(round, cond.TestRoundsUnWhite));
                    chkCbxWhiteListTime.Properties.Items.Add(round, isPeroidChecked(round, cond.TestRoundsWhite));
                }
                else
                {
                    chkCbxUnWhiteListTime.Properties.Items.Add(round);
                    chkCbxWhiteListTime.Properties.Items.Add(round);
                }
            }
        }
        private bool isPeroidChecked(TestRound round, List<TestRound> TestRounds)
        {
            if (TestRounds != null && TestRounds.Count > 0)
            {
                foreach (TestRound period in TestRounds)
                {
                    if (period.ToString() == round.ToString())
                    {
                        return true;
                    }
                }
                return false;
            }
            return false;
        }
        public ReportAndWhiteListCondition GetConditon()
        {
            cond.GridStatSetting_LTE.IsChecked = chkLte.Checked;
            cond.GridStatSetting_Other.IsChecked = chkOther.Checked;
            cond.Report = reportFilter.SelectedReport; 
            cond.IsQueryAllParams = chkAllParam.Checked;
            cond.IsShowFileInfoForm = chkShowFileInfoForm.Checked;

            cond.TestRoundsUnWhite = new List<TestRound>();
            foreach (DevExpress.XtraEditors.Controls.CheckedListBoxItem item in chkCbxUnWhiteListTime.Properties.Items)
            {
                if (item.CheckState == CheckState.Checked)
                {
                    cond.TestRoundsUnWhite.Add((TestRound)item.Value);
                }
            }

            cond.TestRoundsWhite = new List<TestRound>();
            foreach (DevExpress.XtraEditors.Controls.CheckedListBoxItem item in chkCbxWhiteListTime.Properties.Items)
            {
                if (item.CheckState == CheckState.Checked)
                {
                    cond.TestRoundsWhite.Add((TestRound)item.Value);
                }
            }
            return this.cond;
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(chkCbxUnWhiteListTime.Properties.GetCheckedItems().ToString())
                && string.IsNullOrEmpty(chkCbxWhiteListTime.Properties.GetCheckedItems().ToString()))
            {
                XtraMessageBox.Show("请至少选择一个测试时段");
                return;
            }
            ReportStyle rpt = reportFilter.SelectedReport;
            if (rpt == null)
            {
                XtraMessageBox.Show("请选择报表！");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void btnGridSelect_Click(object sender, EventArgs e)
        {
            OpenFileDialog dialog = new OpenFileDialog();
            dialog.Filter = FilterHelper.Excel;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                getGridSettingFromExcel(dialog.FileName);
                drawLableTipText(false);
            }
        }
        private void drawLableTipText(bool isFirstLoad)
        {
            txtBoxWhiteListGrid.Text = getGridNameDes(isFirstLoad, cond.GridStatSetting_LTE.WhiteGridNameList
                , cond.GridStatSetting_Other.WhiteGridNameList);

            txtBoxUnWhiteListGrid.Text = getGridNameDes(isFirstLoad, cond.GridStatSetting_LTE.UnWhiteGridNameList
                , cond.GridStatSetting_Other.UnWhiteGridNameList);
        }
        private string getGridNameDes(bool isFirstLoad,List<string> lteGridNameList, List<string> otherGridNameList)
        {
            StringBuilder strb = new StringBuilder();
            if (lteGridNameList != null && lteGridNameList.Count > 0)
            {
                strb.Append("LTE:");
                foreach (string gridName in lteGridNameList)
                {
                    strb.Append(gridName + ",");
                }
            }
            if (otherGridNameList != null && otherGridNameList.Count > 0)
            {
                strb.Append(" Other:");
                foreach (string gridName in otherGridNameList)
                {
                    strb.Append(gridName + ",");
                }
            }
            if (strb.Length > 0)
            {
                return strb.ToString().Remove(strb.Length - 1, 1);
            }
            else
            {
                return isFirstLoad ? "请选择网格" : "未读取到有效的网格";
            }
        }
        private void getGridSettingFromExcel(string fileName)
        {
            DataSet dataSet;
            try
            {
                dataSet = ExcelNPOIManager.ImportFromExcel(fileName);
            }
            catch (Exception ee)
            {
                XtraMessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
                return;
            }

            bool hasGetLteGridSetting = getGridSettingFromDataSet(dataSet, "LTE", cond.GridStatSetting_LTE);
            bool hasGetOtherGridSetting = getGridSettingFromDataSet(dataSet, "Other", cond.GridStatSetting_Other);

            if (!hasGetLteGridSetting && !hasGetOtherGridSetting)
            {
                XtraMessageBox.Show("未读取到网格信息！");
            }
        }
        private bool getGridSettingFromDataSet(DataSet dataSet, string sheetName, GridStatSetting gridSetting)
        {
            gridSetting.WhiteGridNameList = new List<string>();
            gridSetting.UnWhiteGridNameList = new List<string>();
            gridSetting.FileNameSuffixesList = new List<string>();

            if (dataSet == null || dataSet.Tables == null || !dataSet.Tables.Contains(sheetName))
            {
                return false;
            }
            bool hasFoundGridName = judgeFoundGridName(dataSet, sheetName, gridSetting);

            if (hasFoundGridName)
            {
                foreach (string strWhite in gridSetting.WhiteGridNameList)
                {
                    foreach (string strUnWhite in gridSetting.UnWhiteGridNameList)
                    {
                        if (strWhite == strUnWhite)
                        {
                            MessageBox.Show(sheetName + "表具有相同的网格名：" + strWhite);
                        }
                    }
                }
            }
            return hasFoundGridName;
        }

        private static bool judgeFoundGridName(DataSet dataSet, string sheetName, GridStatSetting gridSetting)
        {
            bool hasFoundGridName = false;
            System.Data.DataTable tb = dataSet.Tables[sheetName];
            if (tb != null && tb.Rows.Count >= 0)
            {
                int index = 0;
                foreach (DataRow row in tb.Rows)
                {
                    index++;
                    try
                    {
                        hasFoundGridName = addGridName(gridSetting, hasFoundGridName, row);
                    }
                    catch (Exception ex)
                    {
                        XtraMessageBox.Show(ex.ToString());
                    }
                }
            }

            return hasFoundGridName;
        }

        private static bool addGridName(GridStatSetting gridSetting, bool hasFoundGridName, DataRow row)
        {
            string gridWhiteName = row["白名单网格名"].ToString();
            if (!string.IsNullOrEmpty(gridWhiteName))
            {
                hasFoundGridName = true;
                gridSetting.WhiteGridNameList.Add(gridWhiteName);
            }

            string gridUnWhiteName = row["非白名单网格名"].ToString();
            if (!string.IsNullOrEmpty(gridUnWhiteName))
            {
                hasFoundGridName = true;
                gridSetting.UnWhiteGridNameList.Add(gridUnWhiteName);
            }

            string strFileNameSuffixes = row["文件后缀名"].ToString();
            if (!string.IsNullOrEmpty(strFileNameSuffixes))
            {
                gridSetting.FileNameSuffixesList.Add(strFileNameSuffixes);
            }

            return hasFoundGridName;
        }

        private void btnTestRoundSet_Click(object sender, EventArgs e)
        {
            TestRoundListForm form = new TestRoundListForm(testRounds);
            form.ShowDialog();
            fillTestRound(testRounds);
        }
        private void fillCategoryView(ListView lv, List<int> projIDs, CategoryEnumItem[] projItems)
        {
            lv.Items.Clear();
            if (projIDs == null)
            {
                foreach (CategoryEnumItem item in projItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.Name;
                    lvi.Tag = item.ID;
                    lv.Items.Add(lvi);
                }
            }
            else
            {
                foreach (int id in projIDs)
                {
                    foreach (CategoryEnumItem item in projItems)
                    {
                        if (id == item.ID)
                        {
                            ListViewItem lvi = new ListViewItem();
                            lvi.Text = item.Description;
                            lvi.Tag = id;
                            lv.Items.Add(lvi);
                        }
                    }
                }
            }
        }

        private void btnSelSvcLte_Click(object sender, EventArgs e)
        {
            dropDownSvcLte.Closed -= dropDownSvcLte_Closed;
            System.Drawing.Point pt = new System.Drawing.Point(btnSelSvcLte.Width, btnSelSvcLte.Height);
            dropDownSvcLte.Show(btnSelSvcLte, pt, ToolStripDropDownDirection.BelowLeft);
            dropDownSvcLte.Closed += dropDownSvcLte_Closed;
        }
        void dropDownSvcLte_Closed(object sender, ToolStripDropDownClosedEventArgs e)
        {
            if (cond != null)
            {
                cond.GridStatSetting_LTE.ServiceList.Clear();
                foreach (ListViewItem item in lvServiceLte.Items)
                {
                    cond.GridStatSetting_LTE.ServiceList.Add((int)item.Tag);
                }
            }
        }
        private void btnSelSvcOther_Click(object sender, EventArgs e)
        {
            dropDownSvcOther.Closed -= dropDownSvcOther_Closed;
            System.Drawing.Point pt = new System.Drawing.Point(btnSelSvcOther.Width, btnSelSvcOther.Height);
            dropDownSvcOther.Show(btnSelSvcOther, pt, ToolStripDropDownDirection.BelowLeft);
            dropDownSvcOther.Closed += dropDownSvcOther_Closed;
        }

        void dropDownSvcOther_Closed(object sender, ToolStripDropDownClosedEventArgs e)
        {
            if (cond != null)
            {
                cond.GridStatSetting_Other.ServiceList.Clear();
                foreach (ListViewItem item in lvServiceOther.Items)
                {
                    cond.GridStatSetting_Other.ServiceList.Add((int)item.Tag);
                }
            }
        }
    }

    public class ReportAndWhiteListCondition
    {
        public ReportAndWhiteListCondition()
        {
            GridStatSetting_LTE = new GridStatSetting();
            GridStatSetting_LTE.IsChecked = true;
            GridStatSetting_Other = new GridStatSetting();
        }
        public bool IsShowFileInfoForm { get; set; }

        public GridStatSetting GridStatSetting_LTE { get; set; }
        public GridStatSetting GridStatSetting_Other { get; set; }
        public bool IsQueryAllParams { get; set; }
        public ReportStyle Report { get; set; }
        public List<TestRound> TestRoundsUnWhite { get; set; }
        public List<TestRound> TestRoundsWhite { get; set; }
    }
    public class GridStatSetting
    {
        public GridStatSetting()
        {
            WhiteGridNameList = new List<string>();
            UnWhiteGridNameList = new List<string>();
            FileNameSuffixesList = new List<string>();
            ServiceList = new List<int>();
        }
        public bool IsChecked { get; set; }
        public List<string> WhiteGridNameList { get; set; }
        public List<string> UnWhiteGridNameList { get; set; }
        public List<string> FileNameSuffixesList { get; set; }
        public List<int> ServiceList { get; set; }
    }
}
