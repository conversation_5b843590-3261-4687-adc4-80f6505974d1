﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTNRReSelectListForm : MinCloseForm
    {
        public ZTNRReSelectListForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            mapForm = mainModel.MainForm.GetMapForm();
            init();
            DisposeWhenClose = true;
        }

        private MapForm mapForm = null;
        private List<ZTNRReSelectItem> resultList = new List<ZTNRReSelectItem>();

        private void init()
        {
            #region SatList
            this.olvColumnStatSN.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.SN;
                }
                return "";
            };

            this.olvColumnFileName.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.FileName;
                }
                return "";
            };

            this.olvColumnGridName.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.GridName;
                }
                return "";
            };

            this.olvColumnTime.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.HOEvt.DateTime;
                }
                return "";
            };

            this.olvColumnLongitude.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.HOEvt.Longitude;
                }
                return "";
            };

            this.olvColumnLatitude.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.HOEvt.Latitude;
                }
                return "";
            };

            setBeforeHandoverData();
            setAfterHandoverData();

            this.olvColumnSameSite.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.IsSameSite;
                }
                return "";
            };

            #endregion
        }

        private void setAfterHandoverData()
        {
            this.olvColumnDestCellName.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.DestCellItem.CellName;
                }
                return "";
            };

            this.olvColumnDestCellID.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.DestCellItem.CellID;
                }
                return "";
            };

            this.olvColumnAfterArfcn.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.AfterArfcn;
                }
                return "";
            };

            this.olvColumnAfterPCI.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.AfterPCI;
                }
                return "";
            };

            this.olvColumnDestTAC.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.DestCellItem.TAC;
                }
                return "";
            };

            this.olvColumnDestCI.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.DestCellItem.NCI;
                }
                return "";
            };

            this.olvColumnDestDistance.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.DestCellItem.Distance;
                }
                return "";
            };

            this.olvColumnAfterRsrp.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.AfterRsrpAvg;
                }
                return "";
            };

            this.olvColumnAfterSinr.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.AfterSinrAvg;
                }
                return "";
            };

            this.olvColumnAfterAppSpeed.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.AfterAppSpeedAvg;
                }
                return "";
            };

            this.olvColumnAfterPdcpSpeed.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.AfterPdcpSpeedAvg;
                }
                return "";
            };
            this.olvColumnAfterRsrq.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.AfterRsrqAvg;
                }
                return "";
            };
            this.olvColumnAfterRssi.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.AfterRssiAvg;
                }
                return "";
            };
        }

        private void setBeforeHandoverData()
        {
            this.olvColumnCellName.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.SrcCellItem.CellName;
                }
                return "";
            };

            this.olvColumnCellID.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.SrcCellItem.CellID;
                }
                return "";
            };

            this.olvColumnBeforeArfcn.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.BeforeArfcn;
                }
                return "";
            };

            this.olvColumnBeforePCI.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.BeforePCI;
                }
                return "";
            };

            this.olvColumnCellTAC.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.SrcCellItem.TAC;
                }
                return "";
            };

            this.olvColumnCellCI.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.SrcCellItem.NCI;
                }
                return "";
            };

            this.olvColumnCellDistance.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.SrcCellItem.Distance;
                }
                return "";
            };

            this.olvColumnBeforeRsrp.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.BeforeRsrpAvg;
                }
                return "";
            };

            this.olvColumnBeforeSinr.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.BeforeSinrAvg;
                }
                return "";
            };

            this.olvColumnBeforeAppSpeed.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.BeforeAppSpeedAvg;
                }
                return "";
            };

            this.olvColumnBeforePdcpSpeed.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.BeforePdcpSpeedAvg;
                }
                return "";
            };
            this.olvColumnBeforeRsrq.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.BeforeRsrqAvg;
                }
                return "";
            };
            this.olvColumnBeforeRssi.AspectGetter = delegate (object row)
            {
                if (row is ZTNRReSelectItem)
                {
                    ZTNRReSelectItem item = row as ZTNRReSelectItem;
                    return item.BeforeRssiAvg;
                }
                return "";
            };
        }

        public void FillData(List<ZTNRReSelectItem> resultList)
        {
            this.resultList = new List<ZTNRReSelectItem>();
            this.resultList = resultList;

            ListViewHandOverAna.RebuildColumns();
            ListViewHandOverAna.ClearObjects();
            ListViewHandOverAna.SetObjects(resultList);

            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            exportStatListToXls();
        }

        private void exportStatListToXls()
        {
            if (!exportListToExcel())
            {
                exportStatListToTxt();
            }
        }

        #region ExportStatList
        private bool exportListToExcel()
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(ListViewHandOverAna);
            }
            catch
            {
                return false;
            }
            return true;
        }

        private void exportStatListToTxt()
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = "Text file (*.txt)|*.txt";
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    WaitBox.Show("正在导出到Txt...", doExportStatListToTxt, dlg.FileName);
                    MessageBox.Show("Txt导出完成！");
                }
                catch (System.Exception e)
                {
                    MessageBox.Show("导出到Txt出错：" + e.Message);
                }
            }
        }

        private void doExportStatListToTxt(object nameObj)
        {
            string fileName = nameObj.ToString();
            System.IO.FileStream fileStream = new System.IO.FileStream(fileName, System.IO.FileMode.Create, System.IO.FileAccess.Write, System.IO.FileShare.Read);
            System.IO.StreamWriter streamWriter = new System.IO.StreamWriter(fileStream, Encoding.Default);
            try
            {
                writeStatListContent(streamWriter);
            }
            finally
            {
                streamWriter.Close();
                fileStream.Close();
                WaitBox.Close();
            }
        }

        private void writeStatListContent(System.IO.StreamWriter streamWriter)
        {
            StringBuilder sb = new StringBuilder();

            sb.Append("序号\t");
            sb.Append("文件名称\t");
            sb.Append("网格名称\t");
            sb.Append("事件时间\t");
            sb.Append("事件经度\t");
            sb.Append("事件纬度\t");

            sb.Append("源小区名称\t");
            sb.Append("源小区TAC\t");
            sb.Append("源小区CI\t");
            sb.Append("源小区CellID\t");
            sb.Append("源频点\t");
            sb.Append("源PCI\t");
            sb.Append("源小区与事件距离\t");

            sb.Append("切换前RSRP\t");
            sb.Append("切换前SINR/I\t");
            sb.Append("切换前速率\t");
            sb.Append("切换前RSRQ\t");
            sb.Append("切换前RSSI\t");

            sb.Append("目的小区名称\t");
            sb.Append("目的小区TAC\t");
            sb.Append("目的小区CI\t");
            sb.Append("目的小区CellID\t");
            sb.Append("目的频点\t");
            sb.Append("目的PCI\t");
            sb.Append("目的小区与事件距离\t");

            sb.Append("切换后RSRP\t");
            sb.Append("切换后SINR/I\t");
            sb.Append("切换后速率\t");
            sb.Append("切换后RSRQ\t");
            sb.Append("切换后RSSI\t");
            sb.Append("同站/I\t");

            streamWriter.WriteLine(sb.ToString());
            sb.Remove(0, sb.Length);
            int iLoop = 1;

            foreach (ZTNRReSelectItem item in resultList)
            {
                sb.Append(item.SN + "\t");
                sb.Append(item.FileName + "\t");
                sb.Append(item.GridName + "\t");
                sb.Append(item.HOEvt.DateTime + "\t");
                sb.Append(item.HOEvt.Longitude + "\t");
                sb.Append(item.HOEvt.Latitude + "\t");

                sb.Append(item.SrcCellItem.CellName + "\t");
                sb.Append(item.SrcCellItem.TAC + "\t");
                sb.Append(item.SrcCellItem.NCI + "\t");
                sb.Append(item.SrcCellItem.CellID + "\t");
                sb.Append(item.BeforeArfcn + "\t");
                sb.Append(item.BeforePCI + "\t");
                sb.Append(item.SrcCellItem.Distance + "\t");

                sb.Append(item.BeforeRsrpAvg + "\t");
                sb.Append(item.BeforeSinrAvg + "\t");
                sb.Append(item.BeforeAppSpeedAvg + "\t");
                sb.Append(item.BeforeRsrqAvg + "\t");
                sb.Append(item.BeforeRssiAvg + "\t");

                sb.Append(item.DestCellItem.CellName + "\t");
                sb.Append(item.DestCellItem.TAC + "\t");
                sb.Append(item.DestCellItem.NCI + "\t");
                sb.Append(item.DestCellItem.CellID + "\t");
                sb.Append(item.AfterArfcn + "\t");
                sb.Append(item.AfterPCI + "\t");
                sb.Append(item.DestCellItem.Distance + "\t");

                sb.Append(item.AfterRsrpAvg + "\t");
                sb.Append(item.AfterSinrAvg + "\t");
                sb.Append(item.AfterAppSpeedAvg + "\t");
                sb.Append(item.AfterRsrqAvg + "\t");
                sb.Append(item.AfterRssiAvg + "\t");
                sb.Append(item.IsSameSite + "\t");

                streamWriter.WriteLine(sb.ToString() + "\t");
                sb.Remove(0, sb.Length);

                WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / resultList.Count);
            }
        }
        #endregion

        private void ctxMenu_Opening(object sender, CancelEventArgs e)
        {
            //
        }

        private void miReplay_Click(object sender, EventArgs e)
        {
            if (ListViewHandOverAna.SelectedObject is ZTNRReSelectItem)
            {
                ZTNRReSelectItem item = ListViewHandOverAna.SelectedObject as ZTNRReSelectItem;
                MasterCom.RAMS.Model.Interface.FileReplayer.ReplayOnePart(item.HOEvt);
            }
        }

        private void ListViewHandOverAna_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (ListViewHandOverAna.SelectedObject is ZTNRReSelectItem)
            {
                ZTNRReSelectItem hoItem = ListViewHandOverAna.SelectedObject as ZTNRReSelectItem;
                this.MainModel.ClearDTData();
                this.MainModel.DTDataManager.Add(hoItem.HOEvt);
                hoItem.HOEvt.Selected = true;
                this.MainModel.FireDTDataChanged(this);
            }
        }
    }
}
