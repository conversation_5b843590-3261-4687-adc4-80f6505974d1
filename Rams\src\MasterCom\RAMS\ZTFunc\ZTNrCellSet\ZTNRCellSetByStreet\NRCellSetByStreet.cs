﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRCellSetByStreet : DIYAnalyseByPeriodBackgroundBase_Sample
    {
        public NRCellSetByStreet(MainModel mainModel)
           : base(mainModel)
        {
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        List<string> selectedRegionNames = new List<string>();

        List<CellSetRegionDataBase> serviceDataList = new List<CellSetRegionDataBase>();
        List<CellSetResultBase> resultList = new List<CellSetResultBase>();

        public override string Name
        {
            get { return "道路小区集分析"; }
        }

        public override string IconName
        {
            get { return "Images/streetq.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35034, this.Name);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.SelectedStreets.Count <= 0)
            {
                return false;
            }
            return true;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.Street;
        }

        protected override void prepareStatPackage_Sample_SampleFilter(Package package, TimePeriod period)
        {
            AddDIYStreets_Sample(package);
            AddDIYEndOpFlag(package);
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            bool isValid = Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
            return isValid;
        }

        protected override bool getConditionBeforeQuery()
        {
            return true;
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = NRTpHelper.InitBaseReplayParamSample(false, true);
            NRTpHelper.AddParam(columnsDef, "NR_SS_RSRQ", 0);
            NRTpHelper.AddParam(columnsDef, "NR_NCell_RSRQ", 0);
            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("columnsDef", (object)columnsDef);
            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            initDataMap();

            foreach (var street in Condition.Geometorys.SelectedStreetFeatures)
            {
                selectedRegionNames.Add(street.name);
            }
            if (selectedRegionNames.Count == 0)
            {
                selectedRegionNames.Add("当前所选道路");
            }
        }

        protected virtual void initDataMap()
        {
            serviceDataList = new List<CellSetRegionDataBase>();
            NRCellSetRegionData nrRegionData = new NRCellSetRegionData();
            serviceDataList.Add(nrRegionData);
            resultList = new List<CellSetResultBase>();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                foreach (var regionData in serviceDataList)
                {
                    regionData.DoWithData(tp);
                }
            }
            catch (Exception ex)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.ToString());
            }
        }

        CellSetResultDetail res;
        protected override void getResultAfterQuery()
        {
            foreach (var serviceData in serviceDataList)
            {
                serviceData.Stat(selectedRegionNames, Condition);
               
                resultList.Add(serviceData.Result);
            }

            res = new CellSetResultDetail();
            foreach (var service in resultList)
            {
                foreach (var item in service.ResultDic.Values)
                {
                    res.EarfcnCellResultList.AddRange(item.EarfcnCellResultList);
                    res.CellSetOfRegionResultList.AddRange(item.CellSetOfRegionResultList);
                    res.UnusedCellSetOfRegionResultList.AddRange(item.UnusedCellSetOfRegionResultList);
                    res.BtsSetOfRegionResultList.AddRange(item.BtsSetOfRegionResultList);
                    res.NBCellSetOfRegionResultList.AddRange(item.NBCellSetOfRegionResultList);
                    res.SCellAndNCellResultList.AddRange(item.SCellAndNCellResultList);
                }
            }
        }

        protected override void FireShowFormAfterQuery()
        {
            NRCellSetByStreetForm frm = MainModel.CreateResultForm(typeof(NRCellSetByStreetForm)) as NRCellSetByStreetForm;
            frm.FillData(res);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            releaseData();
        }

        private void releaseData()
        {
            selectedRegionNames = new List<string>();
            serviceDataList = new List<CellSetRegionDataBase>();
        }
    }
}
