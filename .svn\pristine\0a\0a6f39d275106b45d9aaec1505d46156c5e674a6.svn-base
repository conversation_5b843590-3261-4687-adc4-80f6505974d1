﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage._45GHighReverseFlowCoverage
{
    /// <summary>
    /// 查询小区信息,目前4G,5G小区表由现场手工整理入库
    /// </summary>
    public abstract class DiyQueryCellInfo : DIYSQLBase
    {
        public Dictionary<int, List<CellParamInfo>> CityCellInfosDic { get; protected set; }

        protected abstract string tableName { get; }

        public int TotalCount { get; protected set; }
        public int ErrTotalCount { get; protected set; }

        protected DiyQueryCellInfo()
            : base()
        {
            MainDB = true;
        }

        protected override string getSqlTextString()
        {
            string sql = $"SELECT [province],[provinceid],[city],[cityid],[region],[grid],[enodebid]," +
                $"[cellname],[type],[tac],[ci],[celllongitude],[celllatitude],[btslongitude],[btslatitude] " +
                $"FROM {tableName} where haserr = 0";

            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[15];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int64;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx] = E_VType.E_Int;
            return rType;
        }

        protected override bool isValidCondition()
        {
            CityCellInfosDic = new Dictionary<int, List<CellParamInfo>>();
            return true;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    dealReceiveData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        protected virtual void dealReceiveData(Package package)
        {
            var cellInfo = Init();
            cellInfo.FillDataBySQL(package);
            if (cellInfo.CityID == 0 || cellInfo.Bts.ILongitude == 0 || cellInfo.Bts.ILatitude == 0)
            {
                ErrTotalCount++;
                return;
            }

            if (!CityCellInfosDic.TryGetValue(cellInfo.CityID, out var cellInfoList))
            {
                cellInfoList = new List<CellParamInfo>();
                CityCellInfosDic.Add(cellInfo.CityID, cellInfoList);
            }
            cellInfoList.Add(cellInfo);
            TotalCount++;
        }

        protected abstract CellParamInfo Init();
    }

    public class DiyQueryNRCellInfo : DiyQueryCellInfo
    {
        public static string TableName { get; } = "tb_high_reverse_flow_coverage_nr_cell";
        protected override string tableName { get { return TableName; } }

        public override string Name { get { return "查询5G工参表"; } }

        protected override CellParamInfo Init()
        {
            return new NRCellInfo();
        }

        protected override string getSqlTextString()
        {
            string sql = $"SELECT [province],[provinceid],[city],[cityid],[region],[grid],[enodebid]," +
                $"[cellname],[type],[tac],[ci],[celllongitude],[celllatitude],[btslongitude],[btslatitude]," +
                $"[freqband] " +
                $"FROM {tableName} where freqband = '{NRCellInfo.FreqBand._2600M.ToString()}' and haserr = 0";

            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[16];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int64;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx] = E_VType.E_String;
            return rType;
        }
    }

    public class DiyQueryLTECellInfo : DiyQueryCellInfo
    {
        public static string TableName { get; } = "tb_high_reverse_flow_coverage_lte_cell";
        protected override string tableName { get { return TableName; } }

        public override string Name { get { return "查询4G工参表"; } }

        protected override CellParamInfo Init()
        {
            return new LTECellInfo();
        }
    }
}
