﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class ArchiveBaseSettingForm : BaseForm
    {
        public ArchiveBaseSettingForm()
        {
            InitializeComponent();
        }

        public void SetCondition(ArchiveCondition cond)
        {
            if (cond == null)
                cond = new ArchiveCondition();

            basePanel.SetCondition(cond.BaseCondition);
            areaPanel.FillData(ZTAreaManager.Instance);
            areaPanel.SetCondition(cond.VillageCondition);
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            if (!basePanel.CheckCondition() ||
                !areaPanel.CheckCondition())
            {
                return;
            }

            this.DialogResult = DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        public ArchiveCondition GetCondition()
        {
            ArchiveCondition condition = new ArchiveCondition();
            condition.BaseCondition = basePanel.GetCondition();
            condition.VillageCondition = areaPanel.GetCondition();

            return condition;
        }
    }
}
