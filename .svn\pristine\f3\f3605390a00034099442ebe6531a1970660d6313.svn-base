﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRLastWeakMosAnaByTPBase : DIYAnalyseByFileBackgroundBase
    {
        private NRLastWeakMosByTPCondition curCondtion = new NRLastWeakMosByTPCondition();
        private List<NRLastWeakMosByTPResult> listResult = null;
        private NRLastWeakMosAnaByTPForm resultForm = null;
        protected static readonly object lockObj = new object();
        private string curMosParamName = "";

        public NRLastWeakMosAnaByTPBase()
            : base(MainModel.GetInstance())
        {
            IncludeMessage = true;
            Columns = NRTpHelper.InitBaseReplayParamBackground(true, false);
            Columns.Add("NR_PESQScore");
            Columns.Add("NR_PESQLQ");
            Columns.Add("NR_PESQMos");
            Columns.Add("NR_POLQA_Score_SWB");

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NRVoice);
        }

        public override string Name
        {
            get { return "持续弱MOS按采样点分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35042, this.Name);
        }

        protected override bool getCondition()
        {
            NRLastWeakMosAnaByTPDlg setForm = new NRLastWeakMosAnaByTPDlg();
            setForm.SetValue(curCondtion);
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            curCondtion = setForm.GetResult();
            return true;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            this.listResult = new List<NRLastWeakMosByTPResult>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                curMosParamName = "";
                List<TimePeriod> timePeriodList = getCallTimePeriod(file);
               
                int idx = 0;
                foreach (var timePeriod in timePeriodList)
                {
                    idx = dealTPs(file, idx, timePeriod);
                }
            }
        }

        private List<TimePeriod> getCallTimePeriod(DTFileDataManager file)
        {
            List<TimePeriod> timePeriodList = new List<TimePeriod>();
            List<int> startMsgList = new List<int>() { 1107361792, 769 };
            List<int> endMsgList = new List<int>() { 1107443912, 805 };

            //根据信令统计通话时段
            if (curCondtion.IsACall)
            {
                Message startMsg = null;
                foreach (var msg in file.Messages)
                {
                    if (startMsgList.Contains(msg.ID) && startMsg == null)
                    {
                        startMsg = msg;
                    }
                    else if (endMsgList.Contains(msg.ID) && startMsg != null)
                    {
                        TimePeriod timePeriod = new TimePeriod(startMsg.DateTime, msg.DateTime);
                        timePeriodList.Add(timePeriod);
                        startMsg = null;
                    }
                }
            }
            else
            {
                //整个文件按一个时段处理
                DTData startData = file.DTDatas[0];
                DTData endData = file.DTDatas[file.DTDatas.Count - 1];
                TimePeriod timePeriod = new TimePeriod(startData.DateTime, endData.DateTime);
                timePeriodList.Add(timePeriod);
            }

            return timePeriodList;
        }

        private int dealTPs(DTFileDataManager file, int idx, TimePeriod timePeriod)
        {
            NRLastWeakMosByTPInfo info = new NRLastWeakMosByTPInfo();
            info.CallTimePeriod = timePeriod;
            info.FileName = file.FileName;
            for (; idx < file.TestPoints.Count; idx++)
            {
                TestPoint tp = file.TestPoints[idx];
                if (tp.DateTime < timePeriod.BeginTime)
                {
                    continue;
                }
                else if (tp.DateTime > timePeriod.EndTime)
                {
                    break;
                }

                info.MosTimePeriodTPs.Add(tp);
                float? mos = NRTpHelper.NrTpManager.GetMosInfo(tp, ref curMosParamName);
                if (mos != null)
                {
                    if (mos <= curCondtion.MOS)
                    {
                        if (info.StartTP == null)
                        {
                            info.StartTP = tp;
                        }
                        info.WeakMosTPs.Add(tp, Math.Round((double)mos, 2));
                    }
                    else
                    {
                        info.MosTimePeriodTPs.Remove(tp);
                        addValidRes(info);
                        info = new NRLastWeakMosByTPInfo();
                        info.CallTimePeriod = timePeriod;
                        info.FileName = file.FileName;
                    }
                }
            }

            addValidRes(info);
            return idx;
        }

        private void addValidRes(NRLastWeakMosByTPInfo info)
        {
            info.Calculate();
            if (info.Distance > curCondtion.Distance && info.WeakMosTPs.Count >= curCondtion.TpNum)
            {
                NRLastWeakMosByTPResult res = dealRes(info);
                listResult.Add(res);
            }
        }

        private NRLastWeakMosByTPResult dealRes(NRLastWeakMosByTPInfo info)
        {
            NRLastWeakMosByTPResult res = new NRLastWeakMosByTPResult(info);
            res.FileName = info.FileName;
            foreach (var tp in info.MosTimePeriodTPs)
            {
                res.NRInfo.RsrpInfo.Add(NRTpHelper.NrTpManager.GetSCellRsrp(tp));
                res.NRInfo.SinrInfo.Add(NRTpHelper.NrTpManager.GetSCellSinr(tp));

                res.LTEInfo.RsrpInfo.Add(NRTpHelper.NrLteTpManager.GetSCellRsrp(tp));
                res.LTEInfo.SinrInfo.Add(NRTpHelper.NrLteTpManager.GetSCellSinr(tp));
            }

            foreach (var item in info.WeakMosTPs)
            {
                NRLastWeakMosByTPResult.NRLastWeakMosByTPMosInfo mos = new NRLastWeakMosByTPResult.NRLastWeakMosByTPMosInfo();
                mos.WeakMOSTP = item.Key;
                mos.MOS = item.Value;
                res.MOSInfo.Add(item.Value);

                mos.NRInfo.Earfcn = NRTpHelper.NrTpManager.GetEARFCN(item.Key)?.ToString();
                mos.NRInfo.Pci = NRTpHelper.NrTpManager.GetPCI(item.Key)?.ToString();
                mos.NRInfo.Rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(item.Key)?.ToString();
                mos.NRInfo.Sinr = NRTpHelper.NrTpManager.GetSCellSinr(item.Key)?.ToString();

                mos.LTEInfo.Earfcn = NRTpHelper.NrLteTpManager.GetEARFCN(item.Key)?.ToString();
                mos.LTEInfo.Pci = NRTpHelper.NrLteTpManager.GetPCI(item.Key)?.ToString();
                mos.LTEInfo.Rsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(item.Key)?.ToString();
                mos.LTEInfo.Sinr = NRTpHelper.NrLteTpManager.GetSCellSinr(item.Key)?.ToString();
                res.WeakMosTPs.Add(mos);
            }
            res.Calculate();
            return res;
        }

        protected override void fireShowForm()
        {
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new NRLastWeakMosAnaByTPForm();
            }
            resultForm.FillData(listResult, curCondtion);
            resultForm.Owner = MainModel.MainForm;
            resultForm.Visible = false;
            resultForm.Show();
            resultForm.Focus();
        }
    }

    public class NRLastWeakMosAnaByTPByFile : NRLastWeakMosAnaByTPBase
    {
        private NRLastWeakMosAnaByTPByFile()
            : base()
        {
        }

        private static NRLastWeakMosAnaByTPByFile instance = null;
        public static NRLastWeakMosAnaByTPByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRLastWeakMosAnaByTPByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "持续弱MOS按采样点分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }

    public class NRLastWeakMosAnaByTPByRegion : NRLastWeakMosAnaByTPBase
    {
        protected NRLastWeakMosAnaByTPByRegion()
            : base()
        {
        }

        private static NRLastWeakMosAnaByTPByRegion instance = null;
        public static NRLastWeakMosAnaByTPByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRLastWeakMosAnaByTPByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "持续弱MOS按采样点分析(按区域)"; }
        }
    }
}
