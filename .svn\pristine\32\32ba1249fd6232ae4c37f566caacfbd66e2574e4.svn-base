﻿using System.Collections.Generic;
using MasterCom.MTGis;
using System.Drawing;
using MasterCom.RAMS.Func.CoverageCheck;
using MapWinGIS;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class MissGridUnitLayer : CustomDrawLayer
    {
        public MissGridUnitLayer(MapOperation mp, string name)
            : base(mp, name)
        {
        }
        GridMatrix<ColorUnit> compareResult = null;
        public void FillData(GridMatrix<ColorUnit> compareResult)
        {
            this.compareResult = compareResult;
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, System.Drawing.Graphics graphics)
        {
            if (compareResult == null)
            {
                return;
            }
            DbRect viewRect;
            Map.FromDisplay(updateRect, out viewRect);
            foreach (ColorUnit colorUnit in compareResult)
            {
                if (colorUnit.Within(viewRect))
                {
                    DbPoint ltPoint = new DbPoint(colorUnit.LTLng, colorUnit.LTLat);
                    PointF pointLt;
                    this.Map.ToDisplay(ltPoint, out pointLt);
                    DbPoint brPoint = new DbPoint(colorUnit.BRLng, colorUnit.BRLat);
                    PointF pointBr;
                    this.Map.ToDisplay(brPoint, out pointBr);
                    graphics.FillRectangle(new SolidBrush(Color.FromArgb(199, Color.Green)), pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
                }
            }
        }
    }
}
