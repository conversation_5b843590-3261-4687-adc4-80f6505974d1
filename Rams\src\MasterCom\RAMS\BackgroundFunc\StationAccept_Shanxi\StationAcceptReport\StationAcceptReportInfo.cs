﻿using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    public class StationAcceptReportInfo
    {
        public string DistrictName { get; set; }
        public string BtsName { get; set; }
        public int ENodeBID { get; set; }
        public string CoverTypeDesc { get; set; }
        public int CellID { get; set; }
        public string CellName { get; set; }
        public bool IsCellPassAccept { get; set; } = false;
        public string CellAcceptErrorInfo { get; set; }
        public bool IsBtsPassAccept { get; set; } = false;
        public string BtsAcceptErrorInfo { get; set; }
        public string ExcelPath { get; set; }
        public DateTime UpdateTime { get; set; }

        public void FillDataByDB(Package package)
        {
            DistrictName = package.Content.GetParamString();
            BtsName = package.Content.GetParamString();
            ENodeBID = package.Content.GetParamInt();
            CoverTypeDesc = package.Content.GetParamString();
            CellName = package.Content.GetParamString();
            CellID = package.Content.GetParamInt();
            IsCellPassAccept = Convert.ToBoolean(package.Content.GetParamInt());
            CellAcceptErrorInfo = package.Content.GetParamString();
            IsBtsPassAccept = Convert.ToBoolean(package.Content.GetParamInt());
            BtsAcceptErrorInfo = package.Content.GetParamString();
            ExcelPath = package.Content.GetParamString();
            UpdateTime = Convert.ToDateTime(package.Content.GetParamString());
        }
    }
}
