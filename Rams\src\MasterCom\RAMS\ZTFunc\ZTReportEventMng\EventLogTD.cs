﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTReportEventMng
{
    public class EventLogTD : EventLog
    {
        [Description("手机型号")]
        public string PhoneModelNumber { get; set; } = string.Empty;

        public override bool CheckData(out string errorInfo)
        {
            bool correct = base.CheckData(out errorInfo);
            if (string.IsNullOrEmpty(PhoneModelNumber))
            {
                errorInfo += "请填写手机型号";
                return false;
            }
            return correct;
        }

        public override bool CheckField(string fieldName, ref string errorInfo)
        {
            if (!base.CheckField(fieldName, ref errorInfo))
            {
                return false;
            }
            if (fieldName == "PhoneModelNumber" && string.IsNullOrEmpty(PhoneModelNumber))
            {
                errorInfo += "请填写手机型号";
                return false;
            }
            return true;
        }

    }
}
