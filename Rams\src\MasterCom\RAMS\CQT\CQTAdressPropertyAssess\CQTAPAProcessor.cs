﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS
{
    class CQTAPAProcessor: QueryBase   
    {
        public CQTAPAProcessor(MainModel mainModel)
            : base(mainModel)
        { }
        public int Roundid { get; set; }

        /// <summary>
        /// 标题名称
        /// </summary>
        public override string Name
        {
            get { return "CQT地点性能测试"; }
        }

        /// <summary>
        /// 图标名称
        /// </summary>
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }

        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }

        /// <summary>
        /// 判断是否选择区域
        /// </summary>
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        protected override void query()
        {
            CQTAdressPropertyAssessProcessor CQTAdrPrprtAssPro = new CQTAdressPropertyAssessProcessor(MainModel, condition);
            CQTAdrPrprtAssPro.Run();
        }
    }
}
