﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class PlanBtsDbHelper
    {
        /// <summary>
        /// 获取指定城市驻留率情况
        /// </summary>
        public class DiySqlResidentRate : DIYSQLBase
        {
            public Dictionary<LaiKey, ResidentItem> residentDic { get; set; }
            readonly string strCity;

            public DiySqlResidentRate(MainModel mainModel, string city)
                : base(mainModel)
            {
                strCity = city;
            }

            public override string Name
            {
                get { return "DiySqlResidentRate"; }
            }

            protected override string getSqlTextString()
            {
                string strSql = "select max(strcellname),ilac,ici,max(flongitude),max(flatitude),max(fresident),max(strEdgeBack) " +
                "from DTASYSTEM.DBO.tb_planbts_para_resident where strcity = '" + strCity + "' and fresident != -255 group by ilac,ici";
                return strSql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[7];
                rType[0] = E_VType.E_String;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_Int;
                rType[3] = E_VType.E_Float;
                rType[4] = E_VType.E_Float;
                rType[5] = E_VType.E_Float;
                rType[6] = E_VType.E_Float;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                residentDic = new Dictionary<LaiKey, ResidentItem>();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        fillData(package);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }

            private void fillData(Package package)
            {
                ResidentItem resItem = new ResidentItem();
                resItem.StrCellName = package.Content.GetParamString();
                resItem.ILac = package.Content.GetParamInt();
                resItem.ICi = package.Content.GetParamInt();
                resItem.Flongitude = package.Content.GetParamFloat();
                resItem.Flatitude = package.Content.GetParamFloat();
                resItem.Fresident = package.Content.GetParamFloat();
                resItem.FEdgeBackFlow = package.Content.GetParamFloat();
                LaiKey lKey = new LaiKey();
                lKey.ILac = resItem.ILac;
                lKey.ICi = resItem.ICi;
                if (!residentDic.ContainsKey(lKey))
                {
                    residentDic.Add(lKey, resItem);
                }
            }
        }

        /// <summary>
        /// 获取指定城市倒流量情况
        /// </summary>
        public class DiySqlBackFlow : DIYSQLBase
        {
            public Dictionary<LaiKey, ResidentItem> backFlowDic { get; set; }
            readonly string strCity;

            public DiySqlBackFlow(MainModel mainModel, string city)
                : base(mainModel)
            {
                strCity = city;
            }

            public override string Name
            {
                get { return "DiySqlBackFlow"; }
            }

            protected override string getSqlTextString()
            {
                string strSql = "select ilac,ici,sum(fflow)/1024 as fflow from DTASYSTEM.DBO.tb_planbts_para_backflow where strcity = '" + strCity + "' group by ilac,ici";
                return strSql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[3];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_Float;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                backFlowDic = new Dictionary<LaiKey, ResidentItem>();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        fillData(package);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }

            private void fillData(Package package)
            {
                ResidentItem resItem = new ResidentItem();
                resItem.ILac = package.Content.GetParamInt();
                resItem.ICi = package.Content.GetParamInt();
                resItem.FEdgeBackFlow = package.Content.GetParamFloat();
                LaiKey lKey = new LaiKey();
                lKey.ILac = resItem.ILac;
                lKey.ICi = resItem.ICi;
                if (!backFlowDic.ContainsKey(lKey))
                {
                    backFlowDic.Add(lKey, resItem);
                }
            }
        }

        public class DiySqlComplainNum : DIYSQLBase
        {
            public List<ComplainItem> compList { get; set; }
            readonly string strCity;
            /// <summary>
            ///  获取指定城市用户投诉量
            /// </summary>
            public DiySqlComplainNum(MainModel mainModel, string city)
                : base(mainModel)
            {
                strCity = city;
            }

            public override string Name
            {
                get { return "DiySqlComplainNum"; }
            }

            protected override string getSqlTextString()
            {
                string strSql = "select strCompId,fLongitude,fLatitude,iNum from DTASYSTEM.DBO.tb_planbts_para_complain " +
                                "where strcity = '" + strCity + "' " + "order by convert(datetime,dstarttime)";
                return strSql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[4];
                rType[0] = E_VType.E_String;
                rType[1] = E_VType.E_Float;
                rType[2] = E_VType.E_Float;
                rType[3] = E_VType.E_Int;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                compList = new List<ComplainItem>();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        ComplainItem compItem = new ComplainItem();
                        compItem.StrCompId = package.Content.GetParamString();
                        compItem.FLongitude = package.Content.GetParamFloat();
                        compItem.FLatitude = package.Content.GetParamFloat();
                        compItem.INum = package.Content.GetParamInt();
                        compList.Add(compItem);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 获取指定城市MR数据
        /// </summary>
        public class DiySqlMrData : DIYSQLBase
        {
            public Dictionary<LaiKey, MrItem> mrDataDic { get; set; }
            public string strCity { get; set; }

            public DiySqlMrData(MainModel mainModel, string city)
                : base(mainModel)
            {
                strCity = city;
            }

            public override string Name
            {
                get { return "DiySqlMrData"; }
            }

            protected override string getSqlTextString()
            {
                string strSql = "select ilac,ici,strcellname,sum(isnull(iTotalNum,0)),max(isnull(fWeakCoverRate,0)),min(isnull(fBetterCoverRate,0)) " +
                                "from DTASYSTEM.DBO.tb_planbts_para_mrdata where strcity = '" + strCity + "' and ilac is not null group by strcellname,ilac,ici";
                return strSql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[6];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_String;
                rType[3] = E_VType.E_Int;
                rType[4] = E_VType.E_Float;
                rType[5] = E_VType.E_Float;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                mrDataDic = new Dictionary<LaiKey, MrItem>();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        fillData(package);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }

            private void fillData(Package package)
            {
                MrItem mrItem = new MrItem();
                mrItem.ILac = package.Content.GetParamInt();
                mrItem.ICi = package.Content.GetParamInt();
                mrItem.StrCellName = package.Content.GetParamString();
                mrItem.IRscpTotal = package.Content.GetParamInt();
                mrItem.FWeakCoverRate = package.Content.GetParamFloat();
                mrItem.FBetterCoverRate = package.Content.GetParamFloat();

                LaiKey lKey = new LaiKey();
                lKey.ILac = mrItem.ILac;
                lKey.ICi = mrItem.ICi;
                if (!mrDataDic.ContainsKey(lKey))
                {
                    mrDataDic.Add(lKey, mrItem);
                }
            }
        }

        /// <summary>
        /// 获取高流量小区
        /// </summary>
        public class DiySqlHighFlow : DIYSQLBase
        {
            public Dictionary<LaiKey, HighFlow> highFlowDic { get; set; }
            readonly string strCity;

            public DiySqlHighFlow(MainModel mainModel, string city)
                : base(mainModel)
            {
                strCity = city;
            }

            public override string Name
            {
                get { return "DiySqlHighFlow"; }
            }

            protected override string getSqlTextString()
            {
                string strSql = "select strcellname,ilac,ici,fLongitude,fLatitude,fFlow " +
                                "from DTASYSTEM.DBO.tb_planbts_para_highedge where strcity = '" + strCity + "'";
                return strSql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[6];
                rType[0] = E_VType.E_String;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_Int;
                rType[3] = E_VType.E_Float;
                rType[4] = E_VType.E_Float;
                rType[5] = E_VType.E_Float;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                highFlowDic = new Dictionary<LaiKey, HighFlow>();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        fillData(package);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }

            private void fillData(Package package)
            {
                HighFlow highFlow = new HighFlow();
                highFlow.StrCellName = package.Content.GetParamString();
                highFlow.ILac = package.Content.GetParamInt();
                highFlow.ICi = package.Content.GetParamInt();
                highFlow.FLongitude = package.Content.GetParamFloat();
                highFlow.FLatitude = package.Content.GetParamFloat();
                highFlow.FFlow = package.Content.GetParamFloat();

                LaiKey lKey = new LaiKey();
                lKey.ILac = highFlow.ILac;
                lKey.ICi = highFlow.ICi;
                if (!highFlowDic.ContainsKey(lKey))
                {
                    highFlowDic.Add(lKey, highFlow);
                }
            }
        }

        /// <summary>
        /// 获取栅格竞争对比
        /// </summary>
        public class DiySqlCompGrid : DIYSQLBase
        {
            public List<CompGridItem> compGridList { get; set; }
            readonly string strCity;

            public DiySqlCompGrid(MainModel mainModel, string city)
                : base(mainModel)
            {
                strCity = city;
            }

            public override string Name
            {
                get { return "DiySqlCompGrid"; }
            }

            protected override string getSqlTextString()
            {
                string strSql = "select flongitude,flatitude,fPccpchRscp,fvalue1 from DTASYSTEM.DBO.tb_planbts_para_competition2" +
                                " where strcity = '" + strCity + "' and fPccpchRscp < -85";
                return strSql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[4];
                rType[0] = E_VType.E_Float;
                rType[1] = E_VType.E_Float;
                rType[2] = E_VType.E_Float;
                rType[3] = E_VType.E_Float;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                compGridList = new List<CompGridItem>();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        CompGridItem cgItem = new CompGridItem();
                        cgItem.FLongitude = package.Content.GetParamFloat();
                        cgItem.FLatitude = package.Content.GetParamFloat();
                        cgItem.FAbsolutePccpchRscp = package.Content.GetParamFloat();
                        cgItem.FRelativeRscp = package.Content.GetParamFloat();
                        compGridList.Add(cgItem);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 获取持续差道路
        /// </summary>
        public class DiySqlTdLastWeakRoad : DIYSQLBase
        {
            public List<WeakRoadItem> wrList { get; set; }
            readonly string strCity;

            public DiySqlTdLastWeakRoad(MainModel mainModel, string city)
                : base(mainModel)
            {
                strCity = city;
            }

            public override string Name
            {
                get { return "DiySqlTdLastWeakRoad"; }
            }

            protected override string getSqlTextString()
            {
                string strSql = "select Convert(float,imlongitude)/10000000 as fLongitude,Convert(float,imlatitude)/10000000 as fLatitude,idistance/1000 as fDistance,Convert(int,rxlmean) " +
                                "from DTASYSTEM.DBO.tb_planbts_para_tdweak_lastroad where strcity = '" + strCity + "' and imlongitude >0 ";
                return strSql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[4];
                rType[0] = E_VType.E_Float;
                rType[1] = E_VType.E_Float;
                rType[2] = E_VType.E_Float;
                rType[3] = E_VType.E_Int;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                wrList = new List<WeakRoadItem>();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        WeakRoadItem wrItem = new WeakRoadItem();
                        wrItem.FLongitude = package.Content.GetParamFloat();
                        wrItem.FLatitude = package.Content.GetParamFloat();
                        wrItem.FDistance = package.Content.GetParamFloat();
                        wrItem.IRxlmean = package.Content.GetParamInt();
                        wrList.Add(wrItem);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }
        }

        public class DiySqlPlanningBtsScore : DIYSQLBase
        {
            public List<PlanningBtsScore> btsScoreList { get; set; }
            readonly string strCity;
            /// <summary>
            ///  获取规划站评估
            /// </summary>
            public DiySqlPlanningBtsScore(MainModel mainModel, string city)
                : base(mainModel)
            {
                strCity = city;
            }

            public override string Name
            {
                get { return "DiySqlPlanningBtsScore"; }
            }

            protected override string getSqlTextString()
            {
                string strSql = "select strbtsname,flongitude,flatitude,fscore from DTASYSTEM.DBO.tb_planbts_para_info where strcity = '" + strCity + "'";
                return strSql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[4];
                rType[0] = E_VType.E_String;
                rType[1] = E_VType.E_Float;
                rType[2] = E_VType.E_Float;
                rType[3] = E_VType.E_Float;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                btsScoreList = new List<PlanningBtsScore>();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        PlanningBtsScore compItem = new PlanningBtsScore();
                        compItem.Strbtsname = package.Content.GetParamString();
                        compItem.FLongitude = package.Content.GetParamFloat();
                        compItem.FLatitude = package.Content.GetParamFloat();
                        compItem.FScore = package.Content.GetParamFloat();
                        btsScoreList.Add(compItem);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }
        }

        public class ResidentItem
        {
            /// <summary>
            /// 小区名称
            /// </summary>
            public string StrCellName { get; set; }
            /// <summary>
            /// LAC
            /// </summary>
            public int ILac { get; set; }
            /// <summary>
            /// CI
            /// </summary>
            public int ICi { get; set; }
            /// <summary>
            /// 经度
            /// </summary>
            public float Flongitude { get; set; }
            /// <summary>
            /// 纬度
            /// </summary>
            public float Flatitude { get; set; }
            /// <summary>
            /// 驻留率value
            /// </summary>
            public float Fresident { get; set; }
            /// <summary>
            /// 2G倒流率value
            /// </summary>
            public float FEdgeBackFlow { get; set; }
        }

        public class ComplainItem
        {
            /// <summary>
            /// 工单ID
            /// </summary>
            public string StrCompId { get; set; }
            /// <summary>
            /// 经度
            /// </summary>
            public float FLongitude { get; set; }
            /// <summary>
            /// 纬度
            /// </summary>
            public float FLatitude { get; set; }
            /// <summary>
            /// 网络覆盖投诉量value
            /// </summary>
            public int INum { get; set; }
        }

        public class MrItem
        {
            //lac,ci,cell_name,pccpchrscp_total,pccpchrscp_ge_70,pccpchrscp_ge_80,pccpchrscp_ge_85,pccpchrscp_ge_90,pccpchrscp_ge_95
            /// <summary>
            /// LAC
            /// </summary>
            public int ILac { get; set; }
            /// <summary>
            /// CI
            /// </summary>
            public int ICi { get; set; }
            /// <summary>
            /// 小区名称
            /// </summary>
            public string StrCellName { get; set; }
            /// <summary>
            /// 全部采样点
            /// </summary>
            public int IRscpTotal { get; set; }
            /// <summary>
            /// 70覆盖采样点
            /// </summary>
            public int IRscp70 { get; set; }
            /// <summary>
            /// 80覆盖采样点
            /// </summary>
            public int IRscp80 { get; set; }
            /// <summary>
            /// 85覆盖采样点
            /// </summary>
            public int IRscp85 { get; set; }
            /// <summary>
            /// 90覆盖采样点
            /// </summary>
            public int IRscp90 { get; set; }
            /// <summary>
            /// 95覆盖采样点
            /// </summary>
            public int IRscp95 { get; set; }
            /// <summary>
            /// TD良好覆盖率value
            /// </summary>
            public float FBetterCoverRate { get; set; }
            /// <summary>
            /// TD弱覆盖率value
            /// </summary>
            public float FWeakCoverRate { get; set; }
        }

        public class HighFlow
        {
            public string StrCellName { get; set; }
            public int ILac { get; set; }
            public int ICi { get; set; }
            public float FLongitude { get; set; }
            public float FLatitude { get; set; }
            /// <summary>
            /// GSM高流量value
            /// </summary>
            public float FFlow { get; set; }
        }

        public class CompGridItem
        {
            public float FLongitude { get; set; }
            public float FLatitude { get; set; }
            /// <summary>
            /// 移动电平绝对值
            /// </summary>
            public float FAbsolutePccpchRscp { get; set; }
            /// <summary>
            /// 移动与联通的电平相对值
            /// </summary>
            public float FRelativeRscp { get; set; }
        }

        public class WeakRoadItem
        {
            /// <summary>
            /// 经度
            /// </summary>
            public float FLongitude { get; set; }
            /// <summary>
            /// 纬度
            /// </summary>
            public float FLatitude { get; set; }
            /// <summary>
            /// 持续距离
            /// </summary>
            public float FDistance { get; set; }
            /// <summary>
            /// 电平值value
            /// </summary>
            public int IRxlmean { get; set; }
        }

        public class PlanningBtsScore
        {
            /// <summary>
            /// 站名
            /// </summary>
            public string Strbtsname { get; set; }
            /// <summary>
            /// 经度
            /// </summary>
            public float FLongitude { get; set; }
            /// <summary>
            /// 纬度
            /// </summary>
            public float FLatitude { get; set; }
            /// <summary>
            /// 评估分value
            /// </summary>
            public float FScore { get; set; }
        }

        public class LaiKey
        {
            /// <summary>
            /// LAC
            /// </summary>
            public int ILac { get; set; }
            /// <summary>
            /// CI
            /// </summary>
            public int ICi { get; set; }

            public override bool Equals(object obj)
            {
                LaiKey other = obj as LaiKey;
                if (other == null)
                    return false;

                if (!base.GetType().Equals(obj.GetType()))
                    return false;

                return (this.ILac.Equals(other.ILac) &&
                        this.ICi.Equals(other.ICi));
            }

            public override int GetHashCode()
            {
                return this.ICi.GetHashCode();
            }
        }
    }
}
