﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Model.CellParam.QueryByRegion
{
    public partial class CellSignParamConditionDlg : BaseForm
    {
        public CellSignParamConditionDlg(DateTime dtBegin,DateTime dtEnd)
        {
            InitializeComponent();
            this.dtBegin.Value = dtBegin;
            this.dtEnd.Value = dtEnd;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (dtBegin.Value > dtEnd.Value)
            {
                MessageBox.Show("开始时间不能大于结束时间！");
                return;
            }
            DialogResult = DialogResult.OK;
            //if (chk2G.Checked || chkTD.Checked)
            //{

            //}
            //else
            //{
            //    MessageBox.Show("至少选择一种小区查询类型!");
            //    return;
            //}

        }

        public DateTime BeginTime
        {
            get { return dtBegin.Value.Date; }
        }
        public DateTime EndTime
        {
            get { return dtEnd.Value.Date.AddDays(1).AddMilliseconds(-1); }
        }

    }
}
