﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRScanModRoadSettingDlg : BaseDialog
    {
        public NRScanModRoadSettingDlg()
        {
            InitializeComponent();
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
        }

        public void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        public void BtnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        public NRScanModRoadCondition GetCondition()
        {
            NRScanModRoadCondition cond = new NRScanModRoadCondition();
            cond.MaxRxlev = (double)numMaxRxlev.Value;
            cond.SampleInterval = (double)numSampleInterval.Value;
            cond.RoadLength = (double)numRoadLength.Value;
            cond.DiffRxlev = (double)numDiffRxlev.Value;
            string type = cmbModType.SelectedItem.ToString();
            cond.FilterCond.ModX = cond.FilterCond.GetModeType(type);
            return cond;
        }
    }
}
