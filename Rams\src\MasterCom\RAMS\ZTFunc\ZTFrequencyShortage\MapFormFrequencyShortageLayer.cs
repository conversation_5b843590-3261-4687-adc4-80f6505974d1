using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Runtime.Serialization;
using MasterCom.RAMS.Model;
using System.Drawing;
using MasterCom.MControls;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    [Serializable()]
    public class MapFormFrequencyShortageLayer : CustomDrawLayer
    {
        public enum ShowBandType
        {
            GSM900,
            DSC1800,
            All
        }

        static MapFormFrequencyShortageLayer()
        {
        }
        public MapFormFrequencyShortageLayer(MapOperation mp, string name)
            : base(mp, name)
        {
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            float ratio = CustomDrawLayer.GetTestPointDisplayRatio(Map.Scale);
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            if (MainModel.GridFrequencyShortageList.Count > 0)
            {
                foreach (GridFrequencyShortage grid in MainModel.GridFrequencyShortageList)
                {
                    drawGrid(grid, dRect, graphics);
                }
            }
            else if (MainModel.FrequencyShortageList.Count > 0)
            {
                foreach (FrequencyShortage freqShortage in MainModel.FrequencyShortageList)
                {
                    draw(freqShortage, dRect, graphics, ratio);
                }
            }
        }

        private void draw(FrequencyShortage freqShortage, DbRect dRect, Graphics graphics, float ratio)
        {
            if (freqShortage.WithIn(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
            {
                Color color = Color.Red;
                if (color != Color.Empty)
                {
                    float radius = ratio * 20;
                    Brush brush = new SolidBrush(color);
                    DbPoint dPoint = new DbPoint(freqShortage.Longitude, freqShortage.Latitude);
                    PointF point;
                    this.Map.ToDisplay(dPoint, out point);
                    graphics.TranslateTransform(point.X, point.Y);
                    graphics.ScaleTransform(radius, radius);
                    GraphicsPath gp = SymbolManager.GetInstance().Paths[0];
                    graphics.FillPath(brush, gp);
                    graphics.ResetTransform();
                }
            }
        }

        private void drawGrid(GridFrequencyShortage grid, DbRect dRect, Graphics graphics)
        {
            if (grid.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
            {
                Color color = Color.Red;
                if (color != Color.Empty)
                {
                    DbPoint ltPoint = new DbPoint(grid.ltLong, grid.ltLat);
                    PointF pointLt;
                    this.Map.ToDisplay(ltPoint, out pointLt);
                    DbPoint brPoint = new DbPoint(grid.brLong, grid.brLat);
                    PointF pointBr;
                    this.Map.ToDisplay(brPoint, out pointBr);
                    Brush brush = new SolidBrush(color);

                    graphics.FillRectangle(brush, pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
                }
            }
        }
    }
}
