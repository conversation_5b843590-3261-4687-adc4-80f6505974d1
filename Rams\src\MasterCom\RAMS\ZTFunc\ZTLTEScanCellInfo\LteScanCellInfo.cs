﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteScanCellInfo
    {
        private readonly bool hasAntennas = false;
        private readonly LTECell Cell;
        public LteScanCellInfo(LTECell LTECell)
        {
            Cell = LTECell;
            if (Cell.Antennas != null && Cell.Antennas.Count > 0)
            {
                hasAntennas = true;
            }
        }

        public string TPTime { get; set; }
        public string FileName { get; set; }
        public double TPLongitude { get; set; }
        public double TPLatitude { get; set; }
        public string Rsrp { get; set; } = "-";
        public string Sinr { get; set; } = "-";

        public string CellName
        {
            get
            {
                return Cell.Name;
            }
        }
        public int NodeBID
        {
            get
            {
                return Cell.BelongBTS.BTSID;
            }
        }
        public double CellLongitude
        {
            get
            {
                return Cell.Longitude;
            }
        }
        public double CellLatitude
        {
            get
            {
                return Cell.Latitude;
            }
        }
        public int EARFCN
        {
            get
            {
                return Cell.EARFCN;
            }
        }
        public int PCI
        {
            get
            {
                return Cell.PCI;
            }
        }
        public int TAC
        {
            get
            {
                return Cell.TAC;
            }
        }
        public int ECI
        {
            get
            {
                return Cell.ECI;
            }
        }
        public string CGI
        {
            get
            {
                string cgi = string.Format("460-00-{0}-{1}", NodeBID, SectorID);
                return cgi;
            }
        }
        public int CellID
        {
            get
            {
                return Cell.CellID;
            }
        }
        public int SectorID
        {
            get
            {
                return Cell.SectorID;
            }
        }
        public string TypeDesc
        {
            get
            {
                return Cell.Type == LTEBTSType.Outdoor ? "室外" : "室内";
            }
        }
        public string AntennasLongitude
        {
            get
            {
                if (hasAntennas)
                {
                    return Cell.Antennas[0].Longitude.ToString();
                }
                return "-";
            }
        }
        public string AntennasLatitude
        {
            get
            {
                if (hasAntennas)
                {
                    return Cell.Antennas[0].Latitude.ToString();
                }
                return "-";
            }
        }

        public string Direction
        {
            get
            {
                if (hasAntennas)
                {
                    return Cell.Antennas[0].Direction.ToString();
                }
                return "-";
            }
        }

        public string Downward
        {
            get
            {
                if (hasAntennas)
                {
                    return Cell.Antennas[0].Downward.ToString();
                }
                return "-";
            }
        }

        public string Altitude
        {
            get
            {
                if (hasAntennas)
                {
                    return Cell.Antennas[0].Altitude.ToString();
                }
                return "-";
            }
        }
    }

    public class LteScanCellInfoStater
    {
        private readonly List<LteScanCellInfo> tpCellInfoList;

        public LteScanCellInfoStater()
        {
            tpCellInfoList = new List<LteScanCellInfo>();
        }

        public void StatTestPoint(TestPoint tp)
        {
            for (int i = 0; i < 50; ++i)
            {
                LTECell cell = tp.GetCell_LTEScan(i);
                if (cell != null)
                {
                    float? rsrp = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", i];
                    float? sinr = (float?)tp["LTESCAN_TopN_CELL_Specific_RSSINR", i];

                    LteScanCellInfo cellInfo = new LteScanCellInfo(cell);
                    cellInfo.TPTime = tp.DateTimeStringWithMillisecond;
                    cellInfo.FileName = tp.FileName;
                    cellInfo.TPLongitude = tp.Longitude;
                    cellInfo.TPLatitude = tp.Latitude;
                    if (rsrp != null)
                    {
                        cellInfo.Rsrp = rsrp.ToString();
                    }
                    if (sinr != null)
                    {
                        cellInfo.Sinr = sinr.ToString();
                    }
                    tpCellInfoList.Add(cellInfo);
                }
            }
        }

        public List<LteScanCellInfo> GetStatResult()
        {
            return tpCellInfoList;
        }

        public void Clear()
        {
            tpCellInfoList.Clear();
        }
    }
}
