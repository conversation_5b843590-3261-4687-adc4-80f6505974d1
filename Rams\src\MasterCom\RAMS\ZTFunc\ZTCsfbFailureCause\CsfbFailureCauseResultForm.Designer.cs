﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CsfbFailureCauseResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel1 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PiePointOptions piePointOptions1 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PiePointOptions piePointOptions2 = new DevExpress.XtraCharts.PiePointOptions();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView1 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel2 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView2 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.ChartTitle chartTitle1 = new DevExpress.XtraCharts.ChartTitle();
            this.tabCtrl = new DevExpress.XtraTab.XtraTabControl();
            this.pageSummary = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridSummary = new DevExpress.XtraGrid.GridControl();
            this.ctx = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miShowFlowChart = new System.Windows.Forms.ToolStripMenuItem();
            this.viewSummary = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.chartMain = new DevExpress.XtraCharts.ChartControl();
            this.pageDetail = new DevExpress.XtraTab.XtraTabPage();
            this.gridDetail = new DevExpress.XtraGrid.GridControl();
            this.viewDetails = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand9 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn17 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand12 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand10 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand11 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand4 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn23 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand5 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn26 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand7 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn32 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand8 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn33 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand13 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn35 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand6 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn34 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.viewDetail = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.tabCtrl)).BeginInit();
            this.tabCtrl.SuspendLayout();
            this.pageSummary.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridSummary)).BeginInit();
            this.ctx.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.viewSummary)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartMain)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView2)).BeginInit();
            this.pageDetail.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewDetails)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewDetail)).BeginInit();
            this.SuspendLayout();
            // 
            // tabCtrl
            // 
            this.tabCtrl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabCtrl.Location = new System.Drawing.Point(0, 0);
            this.tabCtrl.Name = "tabCtrl";
            this.tabCtrl.SelectedTabPage = this.pageSummary;
            this.tabCtrl.Size = new System.Drawing.Size(980, 500);
            this.tabCtrl.TabIndex = 2;
            this.tabCtrl.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.pageSummary,
            this.pageDetail});
            // 
            // pageSummary
            // 
            this.pageSummary.Controls.Add(this.splitContainerControl1);
            this.pageSummary.Name = "pageSummary";
            this.pageSummary.Size = new System.Drawing.Size(972, 470);
            this.pageSummary.Text = "汇总信息";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.gridSummary);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.chartMain);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(972, 470);
            this.splitContainerControl1.SplitterPosition = 335;
            this.splitContainerControl1.TabIndex = 1;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // gridSummary
            // 
            this.gridSummary.ContextMenuStrip = this.ctx;
            this.gridSummary.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridSummary.Location = new System.Drawing.Point(0, 0);
            this.gridSummary.MainView = this.viewSummary;
            this.gridSummary.Name = "gridSummary";
            this.gridSummary.Size = new System.Drawing.Size(335, 470);
            this.gridSummary.TabIndex = 0;
            this.gridSummary.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.viewSummary});
            // 
            // ctx
            // 
            this.ctx.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport2Xls,
            this.toolStripMenuItem1,
            this.miShowFlowChart});
            this.ctx.Name = "ctx";
            this.ctx.Size = new System.Drawing.Size(159, 54);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(158, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(155, 6);
            // 
            // miShowFlowChart
            // 
            this.miShowFlowChart.Name = "miShowFlowChart";
            this.miShowFlowChart.Size = new System.Drawing.Size(158, 22);
            this.miShowFlowChart.Text = "显示分析流程图";
            this.miShowFlowChart.Click += new System.EventHandler(this.miShowFlowChart_Click);
            // 
            // viewSummary
            // 
            this.viewSummary.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.viewSummary.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.viewSummary.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.viewSummary.ColumnPanelRowHeight = 30;
            this.viewSummary.GridControl = this.gridSummary;
            this.viewSummary.Name = "viewSummary";
            this.viewSummary.OptionsBehavior.Editable = false;
            this.viewSummary.OptionsView.EnableAppearanceEvenRow = true;
            this.viewSummary.OptionsView.ShowGroupPanel = false;
            // 
            // chartMain
            // 
            this.chartMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartMain.EmptyChartText.Text = "无数据";
            this.chartMain.Location = new System.Drawing.Point(0, 0);
            this.chartMain.Name = "chartMain";
            this.chartMain.PaletteName = "Metro";
            this.chartMain.RuntimeSelection = true;
            this.chartMain.RuntimeSeriesSelectionMode = DevExpress.XtraCharts.SeriesSelectionMode.Point;
            pieSeriesLabel1.Border.Visible = false;
            pieSeriesLabel1.LineVisible = true;
            series1.Label = pieSeriesLabel1;
            piePointOptions1.PercentOptions.PercentageAccuracy = 4;
            piePointOptions1.PointView = DevExpress.XtraCharts.PointView.Argument;
            piePointOptions1.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            piePointOptions1.ValueNumericOptions.Precision = 4;
            series1.LegendPointOptions = piePointOptions1;
            series1.Name = "Series 1";
            piePointOptions2.PercentOptions.PercentageAccuracy = 4;
            piePointOptions2.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            series1.PointOptions = piePointOptions2;
            series1.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            series1.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            series1.SynchronizePointOptions = false;
            pieSeriesView1.Border.Visible = false;
            pieSeriesView1.ExplodedDistancePercentage = 2D;
            pieSeriesView1.FillStyle.FillMode = DevExpress.XtraCharts.FillMode.Solid;
            pieSeriesView1.Rotation = 1;
            pieSeriesView1.RuntimeExploding = false;
            series1.View = pieSeriesView1;
            this.chartMain.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1};
            pieSeriesLabel2.LineVisible = true;
            this.chartMain.SeriesTemplate.Label = pieSeriesLabel2;
            pieSeriesView2.RuntimeExploding = false;
            this.chartMain.SeriesTemplate.View = pieSeriesView2;
            this.chartMain.Size = new System.Drawing.Size(631, 470);
            this.chartMain.SmallChartText.Text = "请拉伸图表大小以便显示";
            this.chartMain.TabIndex = 1;
            chartTitle1.Alignment = System.Drawing.StringAlignment.Near;
            chartTitle1.Font = new System.Drawing.Font("宋体", 16F);
            chartTitle1.Text = "原因占比";
            this.chartMain.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle1});
            // 
            // pageDetail
            // 
            this.pageDetail.Controls.Add(this.gridDetail);
            this.pageDetail.Name = "pageDetail";
            this.pageDetail.Size = new System.Drawing.Size(972, 470);
            this.pageDetail.Text = "详细信息";
            // 
            // gridDetail
            // 
            this.gridDetail.ContextMenuStrip = this.ctx;
            this.gridDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridDetail.Location = new System.Drawing.Point(0, 0);
            this.gridDetail.MainView = this.viewDetails;
            this.gridDetail.Name = "gridDetail";
            this.gridDetail.ShowOnlyPredefinedDetails = true;
            this.gridDetail.Size = new System.Drawing.Size(972, 470);
            this.gridDetail.TabIndex = 0;
            this.gridDetail.UseEmbeddedNavigator = true;
            this.gridDetail.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.viewDetails,
            this.viewDetail});
            // 
            // viewDetails
            // 
            this.viewDetails.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand1,
            this.gridBand9,
            this.gridBand2,
            this.gridBand12,
            this.gridBand10,
            this.gridBand11,
            this.gridBand3,
            this.gridBand4,
            this.gridBand5,
            this.gridBand7,
            this.gridBand8,
            this.gridBand13,
            this.gridBand6});
            this.viewDetails.ColumnPanelRowHeight = 50;
            this.viewDetails.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.gridColumn1,
            this.gridColumn17,
            this.gridColumn4,
            this.gridColumn13,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn23,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn35,
            this.gridColumn34,
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.bandedGridColumn3,
            this.bandedGridColumn4});
            this.viewDetails.GridControl = this.gridDetail;
            this.viewDetails.Name = "viewDetails";
            this.viewDetails.OptionsBehavior.AutoPopulateColumns = false;
            this.viewDetails.OptionsBehavior.Editable = false;
            this.viewDetails.OptionsDetail.ShowDetailTabs = false;
            this.viewDetails.OptionsPrint.ExpandAllDetails = true;
            this.viewDetails.OptionsPrint.PrintDetails = true;
            this.viewDetails.OptionsView.ColumnAutoWidth = false;
            this.viewDetails.OptionsView.EnableAppearanceEvenRow = true;
            this.viewDetails.DoubleClick += new System.EventHandler(this.viewDetails_DoubleClick);
            // 
            // gridBand1
            // 
            this.gridBand1.Caption = "道路";
            this.gridBand1.Columns.Add(this.gridColumn1);
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.OptionsBand.FixedWidth = true;
            this.gridBand1.Width = 154;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "道路";
            this.gridColumn1.FieldName = "RoadDesc";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.Width = 154;
            // 
            // gridBand9
            // 
            this.gridBand9.Caption = "主/被叫";
            this.gridBand9.Columns.Add(this.bandedGridColumn1);
            this.gridBand9.Name = "gridBand9";
            this.gridBand9.Width = 75;
            // 
            // bandedGridColumn1
            // 
            this.bandedGridColumn1.Caption = "主/被叫";
            this.bandedGridColumn1.FieldName = "MoMtDesc";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.Visible = true;
            // 
            // gridBand2
            // 
            this.gridBand2.Caption = "原因";
            this.gridBand2.Columns.Add(this.gridColumn17);
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.OptionsBand.FixedWidth = true;
            this.gridBand2.Width = 128;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "原因";
            this.gridColumn17.FieldName = "Cause";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.Width = 128;
            // 
            // gridBand12
            // 
            this.gridBand12.Caption = "对端情况";
            this.gridBand12.Columns.Add(this.bandedGridColumn4);
            this.gridBand12.Name = "gridBand12";
            this.gridBand12.Width = 75;
            // 
            // bandedGridColumn4
            // 
            this.bandedGridColumn4.AppearanceHeader.Options.UseTextOptions = true;
            this.bandedGridColumn4.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.bandedGridColumn4.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridColumn4.Caption = "对端情况";
            this.bandedGridColumn4.FieldName = "OtherSideCause";
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            this.bandedGridColumn4.Visible = true;
            // 
            // gridBand10
            // 
            this.gridBand10.Caption = "FailureTime";
            this.gridBand10.Columns.Add(this.bandedGridColumn3);
            this.gridBand10.Name = "gridBand10";
            this.gridBand10.Width = 75;
            // 
            // bandedGridColumn3
            // 
            this.bandedGridColumn3.AppearanceHeader.Options.UseTextOptions = true;
            this.bandedGridColumn3.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.bandedGridColumn3.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridColumn3.Caption = "CSFB Failure时间";
            this.bandedGridColumn3.DisplayFormat.FormatString = "yyyy/MM/dd HH:mm:ss.fff";
            this.bandedGridColumn3.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Custom;
            this.bandedGridColumn3.FieldName = "FailureTime";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.Visible = true;
            // 
            // gridBand11
            // 
            this.gridBand11.Caption = "RequstTime";
            this.gridBand11.Columns.Add(this.bandedGridColumn2);
            this.gridBand11.Name = "gridBand11";
            this.gridBand11.Width = 75;
            // 
            // bandedGridColumn2
            // 
            this.bandedGridColumn2.AppearanceHeader.Options.UseTextOptions = true;
            this.bandedGridColumn2.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.bandedGridColumn2.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridColumn2.Caption = "CSFB Request时间";
            this.bandedGridColumn2.DisplayFormat.FormatString = "yyyy/MM/dd HH:mm:ss.fff";
            this.bandedGridColumn2.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Custom;
            this.bandedGridColumn2.FieldName = "RequestTime";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.Visible = true;
            // 
            // gridBand3
            // 
            this.gridBand3.Caption = "LTE";
            this.gridBand3.Columns.Add(this.gridColumn4);
            this.gridBand3.Columns.Add(this.gridColumn13);
            this.gridBand3.Columns.Add(this.gridColumn15);
            this.gridBand3.Columns.Add(this.gridColumn16);
            this.gridBand3.Columns.Add(this.gridColumn18);
            this.gridBand3.Columns.Add(this.gridColumn19);
            this.gridBand3.Name = "gridBand3";
            this.gridBand3.Width = 360;
            // 
            // gridColumn4
            // 
            this.gridColumn4.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn4.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn4.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn4.Caption = "平均RSRP";
            this.gridColumn4.FieldName = "RsrpAvg";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.Width = 60;
            // 
            // gridColumn13
            // 
            this.gridColumn13.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn13.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn13.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn13.Caption = "最大RSRP";
            this.gridColumn13.FieldName = "RsrpMax";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.Width = 60;
            // 
            // gridColumn15
            // 
            this.gridColumn15.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn15.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn15.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn15.Caption = "最小RSRP";
            this.gridColumn15.FieldName = "RsrpMin";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.Width = 60;
            // 
            // gridColumn16
            // 
            this.gridColumn16.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn16.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn16.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn16.Caption = "平均SINR";
            this.gridColumn16.FieldName = "SinrAvg";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.Width = 60;
            // 
            // gridColumn18
            // 
            this.gridColumn18.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn18.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn18.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn18.Caption = "最大SINR";
            this.gridColumn18.FieldName = "SinrMax";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.Width = 60;
            // 
            // gridColumn19
            // 
            this.gridColumn19.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn19.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn19.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn19.Caption = "最小SINR";
            this.gridColumn19.FieldName = "SinrMin";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.Width = 60;
            // 
            // gridBand4
            // 
            this.gridBand4.Caption = "GSM";
            this.gridBand4.Columns.Add(this.gridColumn23);
            this.gridBand4.Columns.Add(this.gridColumn20);
            this.gridBand4.Columns.Add(this.gridColumn21);
            this.gridBand4.Columns.Add(this.gridColumn22);
            this.gridBand4.Columns.Add(this.gridColumn24);
            this.gridBand4.Columns.Add(this.gridColumn25);
            this.gridBand4.Name = "gridBand4";
            this.gridBand4.Width = 426;
            // 
            // gridColumn23
            // 
            this.gridColumn23.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn23.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn23.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn23.Caption = "平均RxLevSub";
            this.gridColumn23.FieldName = "RxLevSubAvg";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.Width = 70;
            // 
            // gridColumn20
            // 
            this.gridColumn20.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn20.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn20.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn20.Caption = "最大RxLevSub";
            this.gridColumn20.FieldName = "RxLevSubMax";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.Width = 70;
            // 
            // gridColumn21
            // 
            this.gridColumn21.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn21.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn21.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn21.Caption = "最小RxLevSub";
            this.gridColumn21.FieldName = "RxLevSubMin";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.Width = 70;
            // 
            // gridColumn22
            // 
            this.gridColumn22.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn22.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn22.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn22.Caption = "平均RxQualSub";
            this.gridColumn22.FieldName = "RxQualAvg";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.Width = 72;
            // 
            // gridColumn24
            // 
            this.gridColumn24.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn24.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn24.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn24.Caption = "最大RxQualSub";
            this.gridColumn24.FieldName = "RxQualMax";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.Width = 72;
            // 
            // gridColumn25
            // 
            this.gridColumn25.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn25.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn25.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn25.Caption = "最小RxQualSub";
            this.gridColumn25.FieldName = "RxQualMin";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.Width = 72;
            // 
            // gridBand5
            // 
            this.gridBand5.Caption = "TD";
            this.gridBand5.Columns.Add(this.gridColumn26);
            this.gridBand5.Columns.Add(this.gridColumn27);
            this.gridBand5.Columns.Add(this.gridColumn28);
            this.gridBand5.Columns.Add(this.gridColumn29);
            this.gridBand5.Columns.Add(this.gridColumn30);
            this.gridBand5.Columns.Add(this.gridColumn31);
            this.gridBand5.Name = "gridBand5";
            this.gridBand5.Width = 240;
            // 
            // gridColumn26
            // 
            this.gridColumn26.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn26.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn26.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn26.Caption = "平均RSCP";
            this.gridColumn26.FieldName = "RscpAvg";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.Width = 40;
            // 
            // gridColumn27
            // 
            this.gridColumn27.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn27.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn27.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn27.Caption = "最大RSCP";
            this.gridColumn27.FieldName = "RscpMax";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.Width = 40;
            // 
            // gridColumn28
            // 
            this.gridColumn28.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn28.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn28.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn28.Caption = "最小RSCP";
            this.gridColumn28.FieldName = "RscpMin";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.Width = 40;
            // 
            // gridColumn29
            // 
            this.gridColumn29.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn29.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn29.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn29.Caption = "平均BLER";
            this.gridColumn29.FieldName = "BlerAvg";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.Width = 40;
            // 
            // gridColumn30
            // 
            this.gridColumn30.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn30.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn30.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn30.Caption = "最大BLER";
            this.gridColumn30.FieldName = "BlerMax";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.Width = 40;
            // 
            // gridColumn31
            // 
            this.gridColumn31.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn31.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridColumn31.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn31.Caption = "最小BLER";
            this.gridColumn31.FieldName = "BlerMin";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.Width = 40;
            // 
            // gridBand7
            // 
            this.gridBand7.Caption = "中心经度";
            this.gridBand7.Columns.Add(this.gridColumn32);
            this.gridBand7.Name = "gridBand7";
            this.gridBand7.Width = 75;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "中心经度";
            this.gridColumn32.FieldName = "LngMid";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            // 
            // gridBand8
            // 
            this.gridBand8.Caption = "中心纬度";
            this.gridBand8.Columns.Add(this.gridColumn33);
            this.gridBand8.Name = "gridBand8";
            this.gridBand8.Width = 75;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "中心纬度";
            this.gridColumn33.FieldName = "LatMid";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            // 
            // gridBand13
            // 
            this.gridBand13.Caption = "异常信令";
            this.gridBand13.Columns.Add(this.gridColumn35);
            this.gridBand13.Name = "gridBand13";
            this.gridBand13.Width = 75;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "异常信令";
            this.gridColumn35.FieldName = "AbnormalMsg";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            // 
            // gridBand6
            // 
            this.gridBand6.Caption = "文件";
            this.gridBand6.Columns.Add(this.gridColumn34);
            this.gridBand6.Name = "gridBand6";
            this.gridBand6.Width = 75;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "文件";
            this.gridColumn34.FieldName = "FileName";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            // 
            // viewDetail
            // 
            this.viewDetail.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn5,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn7,
            this.gridColumn6,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn12});
            this.viewDetail.GridControl = this.gridDetail;
            this.viewDetail.Name = "viewDetail";
            this.viewDetail.OptionsBehavior.AutoPopulateColumns = false;
            this.viewDetail.OptionsBehavior.Editable = false;
            this.viewDetail.OptionsDetail.ShowDetailTabs = false;
            this.viewDetail.OptionsView.EnableAppearanceEvenRow = true;
            this.viewDetail.OptionsView.ShowDetailButtons = false;
            this.viewDetail.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "主服小区";
            this.gridColumn5.FieldName = "SCellName";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 0;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "原因类别";
            this.gridColumn10.FieldName = "CauseType";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 1;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "原因场景";
            this.gridColumn11.FieldName = "CauseScene";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 2;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "详细原因";
            this.gridColumn7.FieldName = "CauseDetailName";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 3;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "下载速率(Mbps)";
            this.gridColumn6.FieldName = "Speed";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 4;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "RSRP";
            this.gridColumn8.FieldName = "RSRP";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 5;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "SINR";
            this.gridColumn9.FieldName = "SINR";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 6;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "时间";
            this.gridColumn12.DisplayFormat.FormatString = "d";
            this.gridColumn12.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn12.FieldName = "Time";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 7;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "持续时间(秒)";
            this.gridColumn14.FieldName = "Second";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 1;
            this.gridColumn14.Width = 88;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "采样点个数";
            this.gridColumn3.FieldName = "SampleCount";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 1;
            this.gridColumn3.Width = 76;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "平均速率(Mbps)";
            this.gridColumn2.FieldName = "AvgSpeed";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 101;
            // 
            // CsfbFailureCauseResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(980, 500);
            this.Controls.Add(this.tabCtrl);
            this.Name = "CsfbFailureCauseResultForm";
            this.Text = "CSFB回落失败原因";
            ((System.ComponentModel.ISupportInitialize)(this.tabCtrl)).EndInit();
            this.tabCtrl.ResumeLayout(false);
            this.pageSummary.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridSummary)).EndInit();
            this.ctx.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.viewSummary)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartMain)).EndInit();
            this.pageDetail.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewDetails)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.viewDetail)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl tabCtrl;
        private DevExpress.XtraTab.XtraTabPage pageSummary;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraGrid.GridControl gridSummary;
        private DevExpress.XtraGrid.Views.Grid.GridView viewSummary;
        private DevExpress.XtraTab.XtraTabPage pageDetail;
        private DevExpress.XtraGrid.GridControl gridDetail;
        private DevExpress.XtraCharts.ChartControl chartMain;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Views.Grid.GridView viewDetail;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView viewDetails;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn17;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn13;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn15;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn16;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn23;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn21;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn22;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn24;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn25;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn26;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn27;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn28;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn29;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn30;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn31;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn32;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn33;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn34;
        private System.Windows.Forms.ContextMenuStrip ctx;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miShowFlowChart;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn35;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand9;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand12;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand10;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand11;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand4;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand5;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand7;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand8;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand13;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand6;
    }
}