﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class GridProblemForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.btnSetting = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.cbxStatus = new DevExpress.XtraEditors.ComboBoxEdit();
            this.btnQuery = new DevExpress.XtraEditors.SimpleButton();
            this.cbxAreaAgent = new DevExpress.XtraEditors.ComboBoxEdit();
            this.cbxAreaOpt = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.edtAreaATUGridID = new DevExpress.XtraEditors.TextEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.edtNetGridID = new DevExpress.XtraEditors.TextEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlGrid = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewGrid = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNetGridID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnMidLong = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnMidLat = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnStatus = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnWeight = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCreatedMonth = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLastAbnormalMonth = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnClosedMonth = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGoodDaysCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnValidateStatus = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGridRepeatCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAreaATUGridID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAreaOpt = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAreaAgent = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControlGridKPI = new DevExpress.XtraGrid.GridControl();
            this.gridViewGridKPI = new DevExpress.XtraGrid.Views.Grid.GridView();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxStatus.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxAreaAgent.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxAreaOpt.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtAreaATUGridID.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtNetGridID.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGrid)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGrid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGridKPI)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGridKPI)).BeginInit();
            this.SuspendLayout();
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.btnSetting);
            this.panelControl1.Controls.Add(this.labelControl6);
            this.panelControl1.Controls.Add(this.cbxStatus);
            this.panelControl1.Controls.Add(this.btnQuery);
            this.panelControl1.Controls.Add(this.cbxAreaAgent);
            this.panelControl1.Controls.Add(this.cbxAreaOpt);
            this.panelControl1.Controls.Add(this.labelControl5);
            this.panelControl1.Controls.Add(this.labelControl4);
            this.panelControl1.Controls.Add(this.edtAreaATUGridID);
            this.panelControl1.Controls.Add(this.labelControl3);
            this.panelControl1.Controls.Add(this.edtNetGridID);
            this.panelControl1.Controls.Add(this.labelControl2);
            this.panelControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControl1.Location = new System.Drawing.Point(0, 0);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(1007, 46);
            this.panelControl1.TabIndex = 0;
            // 
            // btnSetting
            // 
            this.btnSetting.Location = new System.Drawing.Point(942, 11);
            this.btnSetting.Name = "btnSetting";
            this.btnSetting.Size = new System.Drawing.Size(53, 23);
            this.btnSetting.TabIndex = 13;
            this.btnSetting.Text = "设置";
            this.btnSetting.Click += new System.EventHandler(this.btnSetting_Click);
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(21, 15);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(36, 14);
            this.labelControl6.TabIndex = 12;
            this.labelControl6.Text = "状态：";
            // 
            // cbxStatus
            // 
            this.cbxStatus.Location = new System.Drawing.Point(63, 12);
            this.cbxStatus.Name = "cbxStatus";
            this.cbxStatus.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxStatus.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxStatus.Size = new System.Drawing.Size(76, 21);
            this.cbxStatus.TabIndex = 1;
            // 
            // btnQuery
            // 
            this.btnQuery.Location = new System.Drawing.Point(870, 11);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.Size = new System.Drawing.Size(53, 23);
            this.btnQuery.TabIndex = 6;
            this.btnQuery.Text = "查询";
            this.btnQuery.Click += new System.EventHandler(this.btnQuery_Click);
            // 
            // cbxAreaAgent
            // 
            this.cbxAreaAgent.Location = new System.Drawing.Point(648, 12);
            this.cbxAreaAgent.Name = "cbxAreaAgent";
            this.cbxAreaAgent.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxAreaAgent.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxAreaAgent.Size = new System.Drawing.Size(79, 21);
            this.cbxAreaAgent.TabIndex = 5;
            // 
            // cbxAreaOpt
            // 
            this.cbxAreaOpt.Location = new System.Drawing.Point(480, 12);
            this.cbxAreaOpt.Name = "cbxAreaOpt";
            this.cbxAreaOpt.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxAreaOpt.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxAreaOpt.Size = new System.Drawing.Size(79, 21);
            this.cbxAreaOpt.TabIndex = 4;
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(582, 15);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(60, 14);
            this.labelControl5.TabIndex = 10;
            this.labelControl5.Text = "代维分区：";
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(438, 15);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(36, 14);
            this.labelControl4.TabIndex = 8;
            this.labelControl4.Text = "片区：";
            // 
            // edtAreaATUGridID
            // 
            this.edtAreaATUGridID.Location = new System.Drawing.Point(353, 12);
            this.edtAreaATUGridID.Name = "edtAreaATUGridID";
            this.edtAreaATUGridID.Size = new System.Drawing.Size(62, 21);
            this.edtAreaATUGridID.TabIndex = 3;
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(311, 15);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(36, 14);
            this.labelControl3.TabIndex = 6;
            this.labelControl3.Text = "网格：";
            // 
            // edtNetGridID
            // 
            this.edtNetGridID.Location = new System.Drawing.Point(226, 12);
            this.edtNetGridID.Name = "edtNetGridID";
            this.edtNetGridID.Size = new System.Drawing.Size(62, 21);
            this.edtNetGridID.TabIndex = 2;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(160, 15);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(60, 14);
            this.labelControl2.TabIndex = 4;
            this.labelControl2.Text = "栅格编号：";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.Panel2;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 46);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.gridControlGrid);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.gridControlGridKPI);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1007, 438);
            this.splitContainerControl1.SplitterPosition = 160;
            this.splitContainerControl1.TabIndex = 1;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // gridControlGrid
            // 
            this.gridControlGrid.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlGrid.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlGrid.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlGrid.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlGrid.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlGrid.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlGrid.Location = new System.Drawing.Point(0, 0);
            this.gridControlGrid.MainView = this.gridViewGrid;
            this.gridControlGrid.Name = "gridControlGrid";
            this.gridControlGrid.Size = new System.Drawing.Size(1007, 272);
            this.gridControlGrid.TabIndex = 3;
            this.gridControlGrid.UseEmbeddedNavigator = true;
            this.gridControlGrid.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewGrid});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(153, 48);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(152, 22);
            this.miExportToExcel.Text = "导出Excel...";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // gridViewGrid
            // 
            this.gridViewGrid.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnID,
            this.gridColumnNetGridID,
            this.gridColumnMidLong,
            this.gridColumnMidLat,
            this.gridColumnStatus,
            this.gridColumnWeight,
            this.gridColumnCreatedMonth,
            this.gridColumnLastAbnormalMonth,
            this.gridColumnClosedMonth,
            this.gridColumnGoodDaysCount,
            this.gridColumnValidateStatus,
            this.gridColumnGridRepeatCount,
            this.gridColumnAreaATUGridID,
            this.gridColumnAreaOpt,
            this.gridColumnAreaAgent});
            this.gridViewGrid.GridControl = this.gridControlGrid;
            this.gridViewGrid.Name = "gridViewGrid";
            this.gridViewGrid.OptionsBehavior.Editable = false;
            this.gridViewGrid.OptionsView.ColumnAutoWidth = false;
            this.gridViewGrid.OptionsView.ShowDetailButtons = false;
            this.gridViewGrid.OptionsView.ShowGroupPanel = false;
            this.gridViewGrid.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gridViewGrid_FocusedRowChanged);
            this.gridViewGrid.DoubleClick += new System.EventHandler(this.gridViewGrid_DoubleClick);
            // 
            // gridColumnID
            // 
            this.gridColumnID.Caption = "问题点编号";
            this.gridColumnID.FieldName = "ID";
            this.gridColumnID.Name = "gridColumnID";
            this.gridColumnID.Visible = true;
            this.gridColumnID.VisibleIndex = 0;
            // 
            // gridColumnNetGridID
            // 
            this.gridColumnNetGridID.Caption = "栅格编号";
            this.gridColumnNetGridID.FieldName = "NetGridID";
            this.gridColumnNetGridID.Name = "gridColumnNetGridID";
            this.gridColumnNetGridID.Visible = true;
            this.gridColumnNetGridID.VisibleIndex = 1;
            // 
            // gridColumnMidLong
            // 
            this.gridColumnMidLong.Caption = "经度";
            this.gridColumnMidLong.FieldName = "MidLong";
            this.gridColumnMidLong.Name = "gridColumnMidLong";
            this.gridColumnMidLong.Visible = true;
            this.gridColumnMidLong.VisibleIndex = 2;
            // 
            // gridColumnMidLat
            // 
            this.gridColumnMidLat.Caption = "纬度";
            this.gridColumnMidLat.FieldName = "MidLat";
            this.gridColumnMidLat.Name = "gridColumnMidLat";
            this.gridColumnMidLat.Visible = true;
            this.gridColumnMidLat.VisibleIndex = 3;
            // 
            // gridColumnStatus
            // 
            this.gridColumnStatus.Caption = "状态";
            this.gridColumnStatus.FieldName = "StatusString";
            this.gridColumnStatus.Name = "gridColumnStatus";
            this.gridColumnStatus.Visible = true;
            this.gridColumnStatus.VisibleIndex = 4;
            // 
            // gridColumnWeight
            // 
            this.gridColumnWeight.Caption = "权重";
            this.gridColumnWeight.FieldName = "Weight";
            this.gridColumnWeight.Name = "gridColumnWeight";
            this.gridColumnWeight.Visible = true;
            this.gridColumnWeight.VisibleIndex = 5;
            // 
            // gridColumnCreatedMonth
            // 
            this.gridColumnCreatedMonth.Caption = "创建月份";
            this.gridColumnCreatedMonth.FieldName = "CreatedMonth";
            this.gridColumnCreatedMonth.Name = "gridColumnCreatedMonth";
            this.gridColumnCreatedMonth.Visible = true;
            this.gridColumnCreatedMonth.VisibleIndex = 6;
            // 
            // gridColumnLastAbnormalMonth
            // 
            this.gridColumnLastAbnormalMonth.Caption = "最后异常月份";
            this.gridColumnLastAbnormalMonth.FieldName = "LastAbnormalMonth";
            this.gridColumnLastAbnormalMonth.Name = "gridColumnLastAbnormalMonth";
            this.gridColumnLastAbnormalMonth.Visible = true;
            this.gridColumnLastAbnormalMonth.VisibleIndex = 7;
            this.gridColumnLastAbnormalMonth.Width = 105;
            // 
            // gridColumnClosedMonth
            // 
            this.gridColumnClosedMonth.Caption = "关闭月份";
            this.gridColumnClosedMonth.FieldName = "ClosedMonth";
            this.gridColumnClosedMonth.Name = "gridColumnClosedMonth";
            this.gridColumnClosedMonth.Visible = true;
            this.gridColumnClosedMonth.VisibleIndex = 8;
            // 
            // gridColumnGoodDaysCount
            // 
            this.gridColumnGoodDaysCount.Caption = "验证正常次数";
            this.gridColumnGoodDaysCount.FieldName = "GoodDaysCount";
            this.gridColumnGoodDaysCount.Name = "gridColumnGoodDaysCount";
            this.gridColumnGoodDaysCount.Visible = true;
            this.gridColumnGoodDaysCount.VisibleIndex = 9;
            this.gridColumnGoodDaysCount.Width = 99;
            // 
            // gridColumnValidateStatus
            // 
            this.gridColumnValidateStatus.Caption = "验证测试状态";
            this.gridColumnValidateStatus.FieldName = "ValidateStatus";
            this.gridColumnValidateStatus.Name = "gridColumnValidateStatus";
            this.gridColumnValidateStatus.Visible = true;
            this.gridColumnValidateStatus.VisibleIndex = 10;
            this.gridColumnValidateStatus.Width = 99;
            // 
            // gridColumnGridRepeatCount
            // 
            this.gridColumnGridRepeatCount.Caption = "栅格问题次数";
            this.gridColumnGridRepeatCount.FieldName = "GridRepeatCount";
            this.gridColumnGridRepeatCount.Name = "gridColumnGridRepeatCount";
            this.gridColumnGridRepeatCount.Visible = true;
            this.gridColumnGridRepeatCount.VisibleIndex = 11;
            this.gridColumnGridRepeatCount.Width = 103;
            // 
            // gridColumnAreaATUGridID
            // 
            this.gridColumnAreaATUGridID.Caption = "网格";
            this.gridColumnAreaATUGridID.FieldName = "AreaATUGridID";
            this.gridColumnAreaATUGridID.Name = "gridColumnAreaATUGridID";
            this.gridColumnAreaATUGridID.Visible = true;
            this.gridColumnAreaATUGridID.VisibleIndex = 12;
            // 
            // gridColumnAreaOpt
            // 
            this.gridColumnAreaOpt.Caption = "片区";
            this.gridColumnAreaOpt.FieldName = "AreaOpt";
            this.gridColumnAreaOpt.Name = "gridColumnAreaOpt";
            this.gridColumnAreaOpt.Visible = true;
            this.gridColumnAreaOpt.VisibleIndex = 13;
            // 
            // gridColumnAreaAgent
            // 
            this.gridColumnAreaAgent.Caption = "代维分区";
            this.gridColumnAreaAgent.FieldName = "AreaAgent";
            this.gridColumnAreaAgent.Name = "gridColumnAreaAgent";
            this.gridColumnAreaAgent.Visible = true;
            this.gridColumnAreaAgent.VisibleIndex = 14;
            this.gridColumnAreaAgent.Width = 78;
            // 
            // gridControlGridKPI
            // 
            this.gridControlGridKPI.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlGridKPI.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlGridKPI.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlGridKPI.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlGridKPI.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlGridKPI.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlGridKPI.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlGridKPI.Location = new System.Drawing.Point(0, 0);
            this.gridControlGridKPI.MainView = this.gridViewGridKPI;
            this.gridControlGridKPI.Name = "gridControlGridKPI";
            this.gridControlGridKPI.Size = new System.Drawing.Size(1007, 160);
            this.gridControlGridKPI.TabIndex = 4;
            this.gridControlGridKPI.UseEmbeddedNavigator = true;
            this.gridControlGridKPI.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewGridKPI});
            // 
            // gridViewGridKPI
            // 
            this.gridViewGridKPI.GridControl = this.gridControlGridKPI;
            this.gridViewGridKPI.Name = "gridViewGridKPI";
            this.gridViewGridKPI.OptionsBehavior.Editable = false;
            this.gridViewGridKPI.OptionsView.ColumnAutoWidth = false;
            this.gridViewGridKPI.OptionsView.ShowDetailButtons = false;
            this.gridViewGridKPI.OptionsView.ShowGroupPanel = false;
            // 
            // GridProblemForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1007, 484);
            this.Controls.Add(this.splitContainerControl1);
            this.Controls.Add(this.panelControl1);
            this.Name = "GridProblemForm";
            this.Text = "栅格问题点";
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxStatus.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxAreaAgent.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxAreaOpt.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtAreaATUGridID.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtNetGridID.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGrid)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGrid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlGridKPI)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewGridKPI)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraGrid.GridControl gridControlGrid;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewGrid;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNetGridID;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnStatus;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnWeight;
        private DevExpress.XtraGrid.GridControl gridControlGridKPI;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewGridKPI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAreaATUGridID;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAreaOpt;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAreaAgent;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.TextEdit edtAreaATUGridID;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.TextEdit edtNetGridID;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SimpleButton btnQuery;
        private DevExpress.XtraEditors.ComboBoxEdit cbxAreaAgent;
        private DevExpress.XtraEditors.ComboBoxEdit cbxAreaOpt;
        private DevExpress.XtraEditors.ComboBoxEdit cbxStatus;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private DevExpress.XtraEditors.SimpleButton btnSetting;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnMidLong;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnMidLat;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGoodDaysCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnValidateStatus;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGridRepeatCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCreatedMonth;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLastAbnormalMonth;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnClosedMonth;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnID;
    }
}