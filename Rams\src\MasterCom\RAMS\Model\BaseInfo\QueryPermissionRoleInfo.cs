﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.UserMng;

namespace MasterCom.RAMS.Model.BaseInfo
{
    /// <summary>
    /// 查询权限角色组
    /// </summary>
    public class QueryPermissionRoleInfo: QueryBase
    {
        public QueryPermissionRoleInfo(MainModel mainModel, bool isQueryAllDistrict)
            : base(mainModel)
        {
            this.isQueryAllDistrict = isQueryAllDistrict;
        }

        readonly bool isQueryAllDistrict = false;

        public override string Name
        {
            get { return "获取基础权限信息"; }
        }

        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            WaitTextBox.Show("正在获取基础信息...", queryData);
        }

        private void queryData()
        {/*查询填充权限信息次序由权限关系的最低层开始，顺序为：
          * 1、function
          * 2、role-func
          * 3、role-dataSrc
          */
            try
            {
                if (!MainModel.PermissionManager.HasQueriedFuncRoleInfo)
                {
                    queryAndFillFunctionSetting();
                    queryAndFillRoleSetting();
                    MainModel.PermissionManager.HasQueriedFuncRoleInfo = true;
                }

                if (!MainModel.PermissionManager.HasQueriedRoleInfo)
                {
                    queryAndFillDataSrcSetting();
                }
                if (isQueryAllDistrict)
                {
                    QueryAllDistrictCategory query = new QueryAllDistrictCategory();
                    query.Query();
                }
                MainModel.PermissionManager.HasQueriedRoleInfo = isQueryAllDistrict;
            }
            catch (Exception ex)
            {
                MessageBox.Show("查询权限基础信息异常！" + Environment.NewLine + ex.ToString());
            }
            finally
            {
                System.Threading.Thread.Sleep(200);
                WaitTextBox.Close();
            }
        }

        private void queryAndFillDataSrcSetting()
        {
            List<Stat.IDNamePair> districtSet = null;
            if (isQueryAllDistrict)
            {
                districtSet = DistrictManager.GetInstance().GetAvailableDistrict();
            }
            else
            {
                districtSet = new List<Stat.IDNamePair>();
                districtSet.Add(new Stat.IDNamePair(MainModel.DistrictID, ""));
            }
            List<DataSourceRole> roles = new List<DataSourceRole>();
            foreach (Stat.IDNamePair districtPair in districtSet)
            {
                QueryPermissionDataSrc queryAll = new QueryPermissionDataSrc(districtPair.id);
                queryAll.Query();
                roles.AddRange(queryAll.Roles);
            }
            MainModel.PermissionManager.DataSourceRoles = roles;
        }

        private void queryAndFillFunctionSetting()
        {
            //main-function 
            DIYQueryFunc queryFunc = new DIYQueryFunc(MainModel);
            queryFunc.Query();
            //sub-function
            DIYQuerySubFunc querySubFunc = new DIYQuerySubFunc(MainModel);
            querySubFunc.Query();

#if PermissionControl_DataExport
            DIYQueryUserExportFunc queryExportFunc = new DIYQueryUserExportFunc(MainModel);
            queryExportFunc.Query();
#endif
            //fill subfunc to mainfunc,then add to manager
            MainModel.PermissionManager.FuncList.Clear();
            for (int i = 0; i < queryFunc.FuncList.Count; i++)
            {
                MainFunction func = queryFunc.FuncList[i];
                func.SubFuncList.AddRange(querySubFunc.SubFuncDic[func.ID]);
                MainModel.PermissionManager.FuncList.Add(func);
            }
        }

        private void queryAndFillRoleSetting()
        {
            //role
            DIYQueryRole queryRole = new DIYQueryRole(MainModel);
            queryRole.Query();
            List<int> roleIDList = queryRole.RoleIDList;
            Dictionary<int, FunctionRole> roleDic = queryRole.RoleDic;
            //role function
            DIYQueryRoleOfFuncs queryRoleOfFuncs = new DIYQueryRoleOfFuncs(MainModel);
            queryRoleOfFuncs.Query();
            Dictionary<int, List<int>> roleOfFuncs = queryRoleOfFuncs.RoleFuncs;

#if PermissionControl_DataExport
            DIYQueryRoleOfExportPermit queryRoleOfExportPermit = new DIYQueryRoleOfExportPermit(MainModel);
            queryRoleOfExportPermit.Query();
            Dictionary<int, List<FuncExportPermit>> roleExportPermit = queryRoleOfExportPermit.RoleExportPermit;
#endif
            //fill function to role
            foreach (int roleID in roleIDList)
            {
                FunctionRole role = roleDic[roleID];
                if (roleOfFuncs.ContainsKey(roleID))
                {
                    role.Permissions.AddRange(roleOfFuncs[roleID]);
                }

#if PermissionControl_DataExport
                List<FuncExportPermit> eFuncList;
                if (roleExportPermit.TryGetValue(roleID, out eFuncList))
                {
                    foreach (FuncExportPermit eFunc in eFuncList)
                    {
                        FuncExportPermit eFuncSrc;
                        if (role.FuncExportPermitDic.TryGetValue(eFunc.SubFuncID, out eFuncSrc))
                        {
                            eFuncSrc.Merge(eFunc);
                        }
                        else
                        {
                            role.FuncExportPermitDic.Add(eFunc.SubFuncID, eFunc.Clone());
                        }
                    }
                }
#endif
            }
            MainModel.PermissionManager.FunctionRoles.Clear();
            MainModel.PermissionManager.FunctionRoles.AddRange(roleDic.Values);
        }

    }
}
