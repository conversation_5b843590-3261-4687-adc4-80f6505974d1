﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Drawing;
using System.Drawing.Drawing2D;
using MasterCom.Util;
using System.Windows.Forms;
using System.Runtime.Serialization;
using System.Collections;
using MasterCom.MControls;
using MasterCom.MTGis;
using MapWinGIS;
namespace MasterCom.RAMS.CQT
{
    [Serializable()]
    public class CQTProblemTrackLayer : CustomDrawLayer
    {
        public Pen selPen { get; set; } = new Pen(Color.Cyan, 3);
        public CQTProblemTrackLayer(MapOperation mp, string name)
            : base(mp, name)
        {
        }
        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            updateRect.Inflate((int)(30 * 10000 / Map.Scale), (int)(30 * 10000 / Map.Scale));
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

            double temp_long = Map.GetCenter().x;
            double temp_lati = Map.GetCenter().y;
            DbPoint ptDSel = new DbPoint(temp_long, temp_lati);
            PointF scrPointSel;
            Map.ToDisplay(ptDSel, out scrPointSel);
            //底层20米的精度跨度大小 0.0001951
            double llGap = (0.000195 / 20) * 100;
            DbPoint ptDSel2 = new DbPoint(temp_long + llGap, temp_lati);
            PointF scrPointSel2;
            Map.ToDisplay(ptDSel2, out scrPointSel2);
            int rGap = (int)((scrPointSel2.X - scrPointSel.X) / 2) + 1;

            DrawItem(MainModel.TabProblemInfoList, dRect, rGap, graphics);
        }

        private void DrawItem(List<TabProblemInfo> tabProblemInfo, DbRect dRect, int rGap, Graphics graphics)
        {
            SolidBrush brush;
            foreach (TabProblemInfo tbPro in tabProblemInfo)
            {
                if (tbPro.DLongitude >= dRect.x1 && tbPro.DLongitude <= dRect.x2
                    && tbPro.DLatitude >= dRect.y1 && tbPro.DLatitude <= dRect.y2)
                {
                    bool selected = false;
                    if (mainModel.MainForm.GetMapForm().CurSelProblemTrackID == tbPro.ITurn)
                    {
                        selected = true;
                    }
                    Region blockReg = setRegion(rGap, tbPro);
                    brush = new SolidBrush(Color.FromArgb(230, tbPro.ColorClose));
                    graphics.FillRegion(brush, blockReg);

                    if (selected)
                    {
                        RectangleF rctf = blockReg.GetBounds(graphics);
                        graphics.DrawRectangle(selPen, (int)rctf.Left, (int)rctf.Top, (int)rctf.Width, (int)rctf.Height);
                    }
                }
            }
        }

        private Region setRegion(int rGap, TabProblemInfo tbPro)
        {
            Region blockReg = null;
            DbPoint dPoint = new DbPoint(tbPro.DLongitude, tbPro.DLatitude);
            PointF point;
            this.Map.ToDisplay(dPoint, out point);
            float radius = rGap > 8 ? rGap : 8;
            RectangleF rect = new RectangleF(point.X - radius, point.Y - radius, radius * 2, radius * 2);
            GraphicsPath gp = new GraphicsPath();
            gp.AddEllipse(rect);
            blockReg = new Region(gp);
            return blockReg;
        }

        public static int OutputShapeFile(string filename)
        {
            return 1;
            /**
            //try
            //{
            //    if (MainModel.GetInstance().CurProblemBlockEventList.Count > 0)
            //    {
            //        Shapefile shpFile = new Shapefile();
            //        int idIdx = 0;
            //        int fBlockId = idIdx++;
            //        int strCity = idIdx++;
            //        int strCounty = idIdx++;
            //        int strTown = idIdx++;
            //        int strGridType = idIdx++;
            //        int strGridName = idIdx++;
            //        int iLac = idIdx++;
            //        int iCi = idIdx++;
            //        int strProject = idIdx++;
            //        int strFileName = idIdx++;
            //        int dTime = idIdx++;
            //        int strRoadType = idIdx++;
            //        int strRoadName = idIdx++;
            //        int fLongId = idIdx++;
            //        int fLatId = idIdx++;
            //        int StrIsRepeat = idIdx++;
            //        int strEventType = idIdx++;
            //        int iLast3MonthEventNum = idIdx++;
            //        int iLast6MonthEventNum = idIdx++;
            //        int StrIsTestNextMonth = idIdx++;
            //        int iAccessTime = idIdx++;

            //        bool result = shpFile.CreateNew("", MapWinGIS.ShpfileType.SHP_POLYGON);
            //        if (result == false)
            //        {
            //            DevExpress.XtraEditors.XtraMessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
            //            return -1;
            //        }

            //        ShapeHelper.InsertNewField(shpFile, "BlockId", FieldType.INTEGER_FIELD, 7, 0, ref fBlockId);
            //        ShapeHelper.InsertNewField(shpFile, "strCity", FieldType.STRING_FIELD, 7, 0, ref strCity);
            //        ShapeHelper.InsertNewField(shpFile, "strCounty", FieldType.STRING_FIELD, 7, 0, ref strCounty);
            //        ShapeHelper.InsertNewField(shpFile, "strTown", FieldType.STRING_FIELD, 7, 0, ref strTown);
            //        ShapeHelper.InsertNewField(shpFile, "strGridType", FieldType.STRING_FIELD, 7, 0, ref strGridType);
            //        ShapeHelper.InsertNewField(shpFile, "strGridName", FieldType.STRING_FIELD, 7, 0, ref strGridName);
            //        ShapeHelper.InsertNewField(shpFile, "iLac", FieldType.INTEGER_FIELD, 7, 0, ref iLac);
            //        ShapeHelper.InsertNewField(shpFile, "iCi", FieldType.INTEGER_FIELD, 7, 0, ref iCi);
            //        ShapeHelper.InsertNewField(shpFile, "strProject", FieldType.STRING_FIELD, 7, 0, ref strProject);
            //        ShapeHelper.InsertNewField(shpFile, "strFileName", FieldType.STRING_FIELD, 7, 0, ref strFileName);
            //        ShapeHelper.InsertNewField(shpFile, "dTime", FieldType.STRING_FIELD, 7, 0, ref dTime);
            //        ShapeHelper.InsertNewField(shpFile, "strRoadType", FieldType.STRING_FIELD, 7, 0, ref strRoadType);
            //        ShapeHelper.InsertNewField(shpFile, "strRoadName", FieldType.STRING_FIELD, 7, 0, ref strRoadName);
            //        ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 7, 0, ref fLongId);
            //        ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 7, 0, ref fLatId);
            //        ShapeHelper.InsertNewField(shpFile, "StrIlsRepeat", FieldType.STRING_FIELD, 7, 0, ref StrIsRepeat);
            //        ShapeHelper.InsertNewField(shpFile, "strEventType", FieldType.STRING_FIELD, 7, 0, ref strEventType);
            //        ShapeHelper.InsertNewField(shpFile, "iLast3MonthEventNum", FieldType.INTEGER_FIELD, 7, 0, ref iLast3MonthEventNum);
            //        ShapeHelper.InsertNewField(shpFile, "iLast6MonthEventNum", FieldType.INTEGER_FIELD, 7, 0, ref iLast6MonthEventNum);
            //        ShapeHelper.InsertNewField(shpFile, "StrIsTestNextMonth", FieldType.STRING_FIELD, 7, 0, ref StrIsTestNextMonth);
            //        ShapeHelper.InsertNewField(shpFile, "iAccessTime", FieldType.INTEGER_FIELD, 7, 0, ref iAccessTime);

            //        double radius = 0.00048775;//大约50米
            //        int shpIdx = 0;
            //        MainModel.GetInstance().MainForm.Cursor = Cursors.WaitCursor;
            //        foreach (TabProblemInfo tabItem in MainModel.GetInstance().TabProblemInfoList)
            //        {
            //            MapWinGIS.Shape spBase = null;
            //            if (tabItem.fLongitude < 90 || tabItem.fLatitude > 45)
            //                continue;
            //            MapWinGIS.Shape evtShp = ShapeHelper.CreateCircleShape(evtItem.fLongitude, evtItem.fLatitude, radius);
            //            if (spBase != null)
            //            {
            //                spBase = spBase.Clip(evtShp, MapWinGIS.tkClipOperation.clUnion);
            //            }
            //            else
            //            {
            //                spBase = evtShp;
            //            }
            //            shpFile.EditInsertShape(spBase, ref shpIdx);
            //            shpFile.EditCellValue(fBlockId, shpIdx, evtItem.iid);
            //            shpFile.EditCellValue(strCity, shpIdx, evtItem.strCity);
            //            shpFile.EditCellValue(strCounty, shpIdx, evtItem.strCounty);
            //            shpFile.EditCellValue(strTown, shpIdx, evtItem.strTown);
            //            shpFile.EditCellValue(strGridType, shpIdx, evtItem.strGridType);
            //            shpFile.EditCellValue(strGridName, shpIdx, evtItem.strGridName);
            //            shpFile.EditCellValue(iLac, shpIdx, evtItem.iLac);
            //            shpFile.EditCellValue(iCi, shpIdx, evtItem.iCi);
            //            shpFile.EditCellValue(strProject, shpIdx, evtItem.strProject);
            //            shpFile.EditCellValue(strFileName, shpIdx, evtItem.strFileName);
            //            shpFile.EditCellValue(dTime, shpIdx, evtItem.dTime.ToString());
            //            shpFile.EditCellValue(strRoadType, shpIdx, evtItem.strRoadType);
            //            shpFile.EditCellValue(strRoadName, shpIdx, evtItem.strRoadName);
            //            shpFile.EditCellValue(fLongId, shpIdx, evtItem.fLongitude);
            //            shpFile.EditCellValue(fLatId, shpIdx, evtItem.fLatitude);
            //            shpFile.EditCellValue(StrIsRepeat, shpIdx, evtItem.StrIsRepeat);
            //            shpFile.EditCellValue(strEventType, shpIdx, evtItem.strEventType);
            //            shpFile.EditCellValue(iLast3MonthEventNum, shpIdx, evtItem.iLast3MonthEventNum);
            //            shpFile.EditCellValue(iLast6MonthEventNum, shpIdx, evtItem.iLast6MonthEventNum);
            //            shpFile.EditCellValue(StrIsTestNextMonth, shpIdx, evtItem.StrIsTestNextMonth);
            //            shpFile.EditCellValue(iAccessTime, shpIdx, evtItem.iAccessTime);
            //            shpIdx++;
            //        }
            //        shpFile.SaveAs(filename, null);
            //        return 1;
            //    }
            //    else
            //    {
            //        return 0;
            //    }
            //}
            //catch
            //{
            //    return -1;
            //}
            //finally
            //{
            //    MainModel.GetInstance().MainForm.Cursor = Cursors.Default;
            //}
            */
        }
    }
}
