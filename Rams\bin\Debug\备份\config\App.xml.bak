<?xml version="1.0"?>
<Configs>
  <Config name="Server">
    <Item name="IP" typeName="String">************</Item>
    <Item name="Port" typeName="Int32">23456</Item>
    <Item name="TestPageUrl" typeName="String">http://************/testpage.html</Item>
    <Item name="Name" typeName="String">61</Item>
    <Item name="ServerType" typeName="String">内网</Item>
    <Item name="IP2" typeName="String">************</Item>
    <Item name="Port2" typeName="Int32">23456</Item>
    <Item name="TestPageUrl2" typeName="String">http://*************:8080/testpage.html</Item>
    <Item name="Name2" typeName="String">81</Item>
    <Item name="ServerType2" typeName="String">内网</Item>
    <Item name="IP3" typeName="String">************</Item>
    <Item name="Port3" typeName="Int32">23456</Item>
    <Item name="TestPageUrl3" typeName="String">http://************/testpage.html</Item>
    <Item name="Name3" typeName="String">测试</Item>
    <Item name="ServerType3" typeName="String">内网</Item>
    <Item name="IP4" typeName="String">127.0.0.1</Item>
    <Item name="Port4" typeName="Int32">23456</Item>
    <Item name="TestPageUrl4" typeName="String">未定义</Item>
    <Item name="Name4" typeName="String">me</Item>
    <Item name="ServerType4" typeName="String">内网</Item>
    <Item name="IP5" typeName="String">************</Item>
    <Item name="Port5" typeName="Int32">23456</Item>
    <Item name="TestPageUrl5" typeName="String">未定义</Item>
    <Item name="Name5" typeName="String">2.12</Item>
    <Item name="ServerType5" typeName="String">内网</Item>
    <Item name="LastServerIP" typeName="String">************</Item>
    <Item name="AutoLoginServerIP" typeName="String">************</Item>
    <Item name="AutoLoginServerDelay" typeName="Int32">3</Item>
  </Config>
  <Config name="Map">
    <Item name="MapURLs" typeName="IList" />
  </Config>
  <Config name="User">
    <Item name="LastUser" typeName="String">gmcc</Item>
    <Item name="IsRememberPW" typeName="Boolean">True</Item>
    <Item name="IsAutoLogin" typeName="Boolean">True</Item>
    <Item name="AutoLoginUserDelay" typeName="Int32">3</Item>
    <Item name="EncodedPW" typeName="String">XGXcSkQD+ug=</Item>
    <Item name="UsedUsers" typeName="IList">
      <Item typeName="String">开发区</Item>
      <Item typeName="String">西区</Item>
      <Item typeName="String">hehui</Item>
      <Item typeName="String">user</Item>
      <Item typeName="String">lianwenguang</Item>
      <Item typeName="String">dwliangwenbao</Item>
      <Item typeName="String">yuanzhiqiang</Item>
      <Item typeName="String">suweixiang</Item>
      <Item typeName="String">daizhenxin</Item>
      <Item typeName="String">youzhicheng</Item>
      <Item typeName="String">yzch</Item>
      <Item typeName="String">guoyubin</Item>
      <Item typeName="String">ydguoyubin</Item>
      <Item typeName="String">ydliminying</Item>
      <Item typeName="String">liangjieliang</Item>
      <Item typeName="String">wengaoli</Item>
      <Item typeName="String">nopuser</Item>
      <Item typeName="String">ydliuzhibin</Item>
      <Item typeName="String">maoweixu</Item>
      <Item typeName="String">dwliujianxin</Item>
      <Item typeName="String">dwsufeng</Item>
      <Item typeName="String">lifu</Item>
      <Item typeName="String">test</Item>
      <Item typeName="String">xiaogan</Item>
      <Item typeName="String">mastercom</Item>
      <Item typeName="String">陕西</Item>
      <Item typeName="String">bjtest</Item>
      <Item typeName="String">chihaibin</Item>
      <Item typeName="String">qiubo</Item>
      <Item typeName="String">T1</Item>
      <Item typeName="String">西安</Item>
      <Item typeName="String">chenbing</Item>
      <Item typeName="String">sangcong</Item>
      <Item typeName="String">1</Item>
      <Item typeName="String">sc1</Item>
      <Item typeName="String">sc</Item>
      <Item typeName="String">admin</Item>
      <Item typeName="String">gmcc</Item>
    </Item>
  </Config>
  <Config name="WorkSpace">
    <Item name="LastWorkSpace" typeName="String">Default</Item>
    <Item name="VerLoadedFlag1" typeName="Int32">0</Item>
  </Config>
</Configs>