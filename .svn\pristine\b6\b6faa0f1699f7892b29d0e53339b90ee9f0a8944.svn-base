﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using Microsoft.Office.Interop.Excel;
using MasterCom.RAMS.Func.SystemSetting;
using System.Drawing;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYNonconformity : DIYAnalyseByCellBackgroundBaseByPeriod
    {
        public bool checkCellRank { get; set; }
        public int cellRank { get; set; }
        public bool checkPccpchRscpThreshold { get; set; }
        public double pccpchRscpThreshold { get; set; }          //-85dBm
        public double directionGapDegreeThreshold { get; set; }  //50%以上
        public double distanceRangeThreshold { get; set; }      //0.6km以上
        int tpNum = 0;
        bool isDefineCell = false; //是否选用自导入的小区工参
        Dictionary<TDCell,CellData> cellDataDic = null;
        Dictionary<DefineCell, CellData> dcellDataDic = null;
        Dictionary<string, List<DefineCell>> freqCpi_defineCellsDic = null;
        readonly List<CellData> nonconformityCellData = new List<CellData>();
        bool showForm = false;

        private static ZTDIYNonconformity intance = null;
        protected static readonly object lockObj = new object();
        public static ZTDIYNonconformity GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYNonconformity();
                    }
                }
            }
            return intance;
        }

        protected ZTDIYNonconformity()
            : base(MainModel.GetInstance())
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.TD_SCAN);
            carrierID = CarrierType.ChinaMobile;

            checkCellRank = true;
            cellRank = 5;    //5级
            checkPccpchRscpThreshold = true;
            pccpchRscpThreshold = -85;
            directionGapDegreeThreshold = 50;
            distanceRangeThreshold = 0.6;
        }

        public override string Name
        {
            get { return "覆盖不符_TD扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 16000, 16012, this.Name);
        }

        NonconformityConditionDlg conditionDlg = null;
        protected override bool  getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                isDefineCell = false;
                return true;
            }
            if (conditionDlg == null)
            {
                conditionDlg = new NonconformityConditionDlg();
            }
            if (conditionDlg.ShowDialog() == DialogResult.OK)
            {
                bool res = getConditionData();
                return res;
            }
            return false;
        }

        private bool getConditionData()
        {
            int cellRankTmp;
            double pccpchRscpThresholdTmp;
            double directionGapDegreeThresholdTmp;
            double distanceRangeThresholdTmp;
            conditionDlg.getCondition(out cellRankTmp, out pccpchRscpThresholdTmp, out directionGapDegreeThresholdTmp, out distanceRangeThresholdTmp, out tpNum, out isDefineCell);
            cellRank = cellRankTmp;
            pccpchRscpThreshold = pccpchRscpThresholdTmp;
            directionGapDegreeThreshold = directionGapDegreeThresholdTmp;
            distanceRangeThreshold = distanceRangeThresholdTmp;
            checkCellRank = conditionDlg.EnableCellRank;
            checkPccpchRscpThreshold = conditionDlg.EnablePccpch_Rscp;

            if (isDefineCell)
            {
                freqCpi_defineCellsDic = new Dictionary<string, List<DefineCell>>();
                if (conditionDlg.DefineCellList == null)
                {
                    return false;
                }
                foreach (DefineCell cell in conditionDlg.DefineCellList)
                {
                    string fckey = cell.Freq + "_" + cell.Cpi;
                    if (!freqCpi_defineCellsDic.ContainsKey(fckey))
                    {
                        List<DefineCell> cells = new List<DefineCell>();
                        cells.Add(cell);
                        freqCpi_defineCellsDic.Add(fckey, cells);
                    }
                    else
                    {
                        freqCpi_defineCellsDic[fckey].Add(cell);
                    }
                }
            }
            return true;
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup leakGroup = new DIYSampleGroup();
            leakGroup.ThemeName = "---";
            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("TDS_PCCPCH_RSSI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TDS_PCCPCH_RSCP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TDS_PCCPCH_ISCP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TDS_PCCPCH_C_I");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TDS_PCCPCH_Ec_Io");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TDS_PCCPCH_SIR");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TDS_DwPTS_RSSI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TDS_DwPTS_RSCP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TDS_DwPTS_ISCP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TDS_DwPTS_C_I");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TDS_DwPTS_Ec_Io");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TDS_DwPTS_SIR");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TDS_PCCPCH_Channel");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("TDS_PCCPCH_CPI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            
            return leakGroup;
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            cellDataDic = new Dictionary<TDCell, CellData>();
            dcellDataDic = new Dictionary<DefineCell, CellData>();
            nonconformityCellData.Clear();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            for (int i = 0; i < 50; i++)
            {
                if (i > cellRank && checkCellRank)
                    break;

                if (checkPccpchRscpThreshold)
                {
                    float? rscp = (float?)tp["TDS_PCCPCH_RSCP", i];

                    if (rscp != null && rscp > pccpchRscpThreshold)
                    {
                        short? freq = (short?)(int?)tp["TDS_PCCPCH_Channel", i];
                        byte? cpi = (byte?)(int?)tp["TDS_PCCPCH_CPI", i];

                        if (!isDefineCell)
                        {
                            TDCell cell = CellManager.GetInstance().GetNearestTDCell(tp.DateTime, (short)freq, (byte)cpi, tp.Longitude, tp.Latitude);
                            if (cell != null)
                            {
                                if (MainModel.SystemConfigInfo.distLimit
                                    && cell.GetDistance(tp.Longitude, tp.Latitude) >= CD.MAX_COV_DISTANCE_TD)//距离限制设置
                                {
                                    continue;
                                }

                                double distance = Math.Round(cell.GetDistance(tp.Longitude, tp.Latitude), 2);
                                double angleDiff;
                                if (!isValidAngle(cell, tp.Longitude, tp.Latitude, out angleDiff) && distance > distanceRangeThreshold)
                                {
                                    if (cellDataDic.ContainsKey(cell))
                                    {
                                        cellDataDic[cell].nonconformityTpList.Add(tp);
                                        cellDataDic[cell].countTpAll++;

                                        ///统计采样点到小区的距离
                                        cellDataDic[cell].sumDistance += distance; //小区距离采样点总距离
                                        if (cellDataDic[cell].closestDistance > distance)
                                        {
                                            cellDataDic[cell].closestDistance = distance; //小区离采样点最近距离
                                        }
                                        if (cellDataDic[cell].farestDistance < distance)
                                        {
                                            cellDataDic[cell].farestDistance = distance; //小区离采样点最远距离
                                        }
                                    }
                                    else
                                    {
                                        cellDataDic[cell] = new CellData();
                                        cellDataDic[cell].nonconformityTpList = new List<TestPoint>();
                                        cellDataDic[cell].nonconformityTpList.Add(tp);
                                        cellDataDic[cell].tdcell = cell;
                                        cellDataDic[cell].countTpAll++;

                                        ///统计采样点到小区的距离
                                        cellDataDic[cell].sumDistance = distance;
                                        cellDataDic[cell].closestDistance = distance;
                                        cellDataDic[cell].farestDistance = distance;
                                    }
                                }
                                else if (cellDataDic.ContainsKey(cell))
                                {
                                    cellDataDic[cell].countTpAll++;
                                }
                            }
                        }
                        else
                        {
                            DefineCell cell = getCellFromTable((short)freq, (byte)cpi, tp.Longitude, tp.Latitude);
                            if (cell != null)
                            {
                                if (MainModel.SystemConfigInfo.distLimit
                                    && cell.GetDistance(tp.Longitude, tp.Latitude) >= CD.MAX_COV_DISTANCE_TD)//距离限制设置
                                {
                                    continue;
                                }

                                double distance = Math.Round(cell.GetDistance(tp.Longitude, tp.Latitude), 2);
                                double angleDiff;
                                if (!isValidAngle(cell, tp.Longitude, tp.Latitude, out angleDiff) && distance > distanceRangeThreshold)
                                {
                                    if (dcellDataDic.ContainsKey(cell))
                                    {
                                        dcellDataDic[cell].nonconformityTpList.Add(tp);
                                        dcellDataDic[cell].countTpAll++;

                                        ///统计采样点到小区的距离
                                        dcellDataDic[cell].sumDistance += distance; //小区距离采样点总距离
                                        if (dcellDataDic[cell].closestDistance > distance)
                                        {
                                            dcellDataDic[cell].closestDistance = distance; //小区离采样点最近距离
                                        }
                                        if (dcellDataDic[cell].farestDistance < distance)
                                        {
                                            dcellDataDic[cell].farestDistance = distance; //小区离采样点最远距离
                                        }
                                    }
                                    else
                                    {
                                        dcellDataDic[cell] = new CellData();
                                        dcellDataDic[cell].nonconformityTpList = new List<TestPoint>();
                                        dcellDataDic[cell].nonconformityTpList.Add(tp);
                                        dcellDataDic[cell].dcell = cell;
                                        dcellDataDic[cell].countTpAll++;

                                        ///统计采样点到小区的距离
                                        dcellDataDic[cell].sumDistance = distance;
                                        dcellDataDic[cell].closestDistance = distance;
                                        dcellDataDic[cell].farestDistance = distance;
                                    }
                                }
                                else if (dcellDataDic.ContainsKey(cell))
                                {
                                    dcellDataDic[cell].countTpAll++;
                                }
                            }
                        }
                    }
                }
                else
                {
                    short? freq = (short?)(int?)tp["TDS_PCCPCH_Channel", i];
                    byte? cpi = (byte?)(int?)tp["TDS_PCCPCH_CPI", i];
                    if (!isDefineCell)
                    {
                        TDCell cell = CellManager.GetInstance().GetNearestTDCell(tp.DateTime, (short)freq, (byte)cpi, tp.Longitude, tp.Latitude);
                        if (cell != null)
                        {
                            double distance = cell.GetDistance(tp.Longitude, tp.Latitude);
                            double angleDiff;
                            if (!isValidAngle(cell, tp.Longitude, tp.Latitude, out angleDiff) && distance > distanceRangeThreshold)
                            {
                                if (cellDataDic.ContainsKey(cell))
                                {
                                    cellDataDic[cell].nonconformityTpList.Add(tp);
                                    cellDataDic[cell].countTpAll++;

                                    ///统计采样点到小区的距离
                                    cellDataDic[cell].sumDistance += distance; //小区距离采样点总距离
                                    if (cellDataDic[cell].closestDistance > distance)
                                    {
                                        cellDataDic[cell].closestDistance = distance; //小区离采样点最近距离
                                    }
                                    if (cellDataDic[cell].farestDistance < distance)
                                    {
                                        cellDataDic[cell].farestDistance = distance; //小区离采样点最远距离
                                    }
                                }
                                else
                                {
                                    cellDataDic[cell] = new CellData();
                                    cellDataDic[cell].nonconformityTpList = new List<TestPoint>();
                                    cellDataDic[cell].nonconformityTpList.Add(tp);
                                    cellDataDic[cell].tdcell = cell;
                                    cellDataDic[cell].countTpAll++;

                                    ///统计采样点到小区的距离
                                    cellDataDic[cell].sumDistance = distance;
                                    cellDataDic[cell].closestDistance = distance;
                                    cellDataDic[cell].farestDistance = distance;
                                }
                            }
                            else if (cellDataDic.ContainsKey(cell))
                            {
                                cellDataDic[cell].countTpAll++;
                            }
                        }
                    }
                    else
                    {
                        DefineCell cell = getCellFromTable((short)freq, (byte)cpi, tp.Longitude, tp.Latitude);
                        if (cell != null)
                        {
                            double distance = cell.GetDistance(tp.Longitude, tp.Latitude);
                            double angleDiff;
                            if (!isValidAngle(cell, tp.Longitude, tp.Latitude, out angleDiff) && distance > distanceRangeThreshold)
                            {
                                if (dcellDataDic.ContainsKey(cell))
                                {
                                    dcellDataDic[cell].nonconformityTpList.Add(tp);
                                    dcellDataDic[cell].countTpAll++;

                                    ///统计采样点到小区的距离
                                    dcellDataDic[cell].sumDistance += distance; //小区距离采样点总距离
                                    if (dcellDataDic[cell].closestDistance > distance)
                                    {
                                        dcellDataDic[cell].closestDistance = distance; //小区离采样点最近距离
                                    }
                                    if (dcellDataDic[cell].farestDistance < distance)
                                    {
                                        dcellDataDic[cell].farestDistance = distance; //小区离采样点最远距离
                                    }
                                }
                                else
                                {
                                    dcellDataDic[cell] = new CellData();
                                    dcellDataDic[cell].nonconformityTpList = new List<TestPoint>();
                                    dcellDataDic[cell].nonconformityTpList.Add(tp);
                                    dcellDataDic[cell].dcell = cell;
                                    dcellDataDic[cell].countTpAll++;

                                    ///统计采样点到小区的距离
                                    dcellDataDic[cell].sumDistance = distance;
                                    dcellDataDic[cell].closestDistance = distance;
                                    dcellDataDic[cell].farestDistance = distance;
                                }
                            }
                            else if (dcellDataDic.ContainsKey(cell))
                            {
                                dcellDataDic[cell].countTpAll++;
                            }
                        }
                    }
                }
            }
        }

        protected override void getResultAfterQuery()
        {
            if (MainModel.IsBackground)
            {
                getResult();
                return;
            }
            getResult();
            showForm = true;
        }

        private void getResult()
        {
            if (!isDefineCell)
            {
                foreach (CellData cd in cellDataDic.Values)
                {
                    if (cd.nonconformityTpList.Count > tpNum)
                    {
                        nonconformityCellData.Add(cd);
                    }
                }
            }
            else
            {
                foreach (CellData cd in dcellDataDic.Values)
                {
                    if (cd.nonconformityTpList.Count > tpNum)
                    {
                        nonconformityCellData.Add(cd);
                    }
                }
            }
            cellDataDic = null;
            dcellDataDic = null;
        }

        protected override void FireShowFormAfterQuery()
        {
            if (showForm)
            {
                TDScanNonconformityForm tDScanNonconformityForm =
                    MainModel.GetInstance().CreateResultForm(typeof(TDScanNonconformityForm)) as TDScanNonconformityForm;
                tDScanNonconformityForm.FillData(nonconformityCellData,"否");
                tDScanNonconformityForm.Visible = true;
                tDScanNonconformityForm.BringToFront();
            }
        }

        /// <summary>
        /// 采样点到小区的夹角是否属于正常角度
        /// </summary>
        /// <param name="cell"></param>
        /// <param name="longitude"></param>
        /// <param name="latitude"></param>
        /// <returns></returns>
        private bool isValidAngle(TDCell cell, double longitude, double latitude, out double angleDiff)
        {
            angleDiff = 0;
            double distance = cell.GetDistance(longitude, latitude);
            if (distance > distanceRangeThreshold * 1000)
            {
                ///所有角度按正北方向算起始，顺时针算夹角，正北为0度
                double angle;
                double ygap = cell.GetDistance(cell.Longitude, latitude);
                double angleV = Math.Acos(ygap / distance);
                if (longitude >= cell.Longitude && latitude >= cell.Latitude)//1象限
                {
                    angle = angleV * 180 / Math.PI;
                }
                else if (longitude <= cell.Longitude && latitude >= cell.Latitude)//2象限
                {
                    angle = 360 - angleV * 180 / Math.PI;
                }
                else if (longitude <= cell.Longitude && latitude <= cell.Latitude)//3象限
                {
                    angle = 180 + angleV * 180 / Math.PI;
                }
                else//4象限
                {
                    angle = 180 - angleV * 180 / Math.PI;
                }

                angleDiff = Math.Abs(angle - cell.Direction);
                if (angleDiff > 180)
                {
                    angleDiff = 360 - angleDiff;
                }
                if (angleDiff > 65 * (0.5 + directionGapDegreeThreshold * 0.01))
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 采样点到小区的夹角是否属于正常角度
        /// </summary>
        /// <param name="cell"></param>
        /// <param name="longitude"></param>
        /// <param name="latitude"></param>
        /// <returns></returns>
        private bool isValidAngle(DefineCell cell, double longitude, double latitude, out double angleDiff)
        {
            angleDiff = 0;
            if (cell.Direction == -1 || longitude == 0 || latitude == 0)
            {
                return true;
            }
            double distance = cell.GetDistance(longitude, latitude);
            if (distance > distanceRangeThreshold * 1000)
            {
                ///所有角度按正北方向算起始，顺时针算夹角，正北为0度
                double angle;
                double ygap = cell.GetDistance(cell.Longitude, latitude);
                double angleV = Math.Acos(ygap / distance);
                if (longitude >= cell.Longitude && latitude >= cell.Latitude)//1象限
                {
                    angle = angleV * 180 / Math.PI;
                }
                else if (longitude <= cell.Longitude && latitude >= cell.Latitude)//2象限
                {
                    angle = 360 - angleV * 180 / Math.PI;
                }
                else if (longitude <= cell.Longitude && latitude <= cell.Latitude)//3象限
                {
                    angle = 180 + angleV * 180 / Math.PI;
                }
                else//4象限
                {
                    angle = 180 - angleV * 180 / Math.PI;
                }

                angleDiff = Math.Abs(angle - cell.Direction);
                if (angleDiff > 180)
                {
                    angleDiff = 360 - angleDiff;
                }
                if (angleDiff > 65 * (0.5 + directionGapDegreeThreshold * 0.01))
                {
                    return false;
                }
            }
            return true;
        }

        private DefineCell getCellFromTable(short freq, byte cpi, double longitude, double latitude)
        {     
            DefineCell tarCell = null;
            string fckey = freq + "_" + cpi;
            if (freqCpi_defineCellsDic.ContainsKey(fckey))
            {
                tarCell = freqCpi_defineCellsDic[fckey][0];
                double minDistance = tarCell.GetDistance(longitude, latitude);
                for (int i = 1; i < freqCpi_defineCellsDic[fckey].Count; i++)
                {
                    double distance=freqCpi_defineCellsDic[fckey][i].GetDistance(longitude, latitude);
                    if (minDistance > distance)
                    {
                        minDistance = distance;
                        tarCell=freqCpi_defineCellsDic[fckey][i];
                    }
                }   
                return tarCell;
            }
            return null;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.TD扫频; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["CheckCellRank"] = checkCellRank;
                param["CellRank"] = cellRank;
                param["CheckPccpchRscpThreshold"] = checkPccpchRscpThreshold;
                param["PccpchRscpThreshold"] = pccpchRscpThreshold;
                param["DirectionGapDegreeThreshold"] = directionGapDegreeThreshold;
                param["DistanceRangeThreshold"] = distanceRangeThreshold;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("CheckCellRank"))
                {
                    checkCellRank = (bool)param["CheckCellRank"];
                }
                if (param.ContainsKey("CellRank"))
                {
                    cellRank = int.Parse(param["CellRank"].ToString());
                }
                if (param.ContainsKey("CheckPccpchRscpThreshold"))
                {
                    checkPccpchRscpThreshold = (bool)param["CheckPccpchRscpThreshold"];
                }
                if (param.ContainsKey("PccpchRscpThreshold"))
                {
                    pccpchRscpThreshold = double.Parse(param["PccpchRscpThreshold"].ToString());
                }
                if (param.ContainsKey("DirectionGapDegreeThreshold"))
                {
                    directionGapDegreeThreshold = double.Parse(param["DirectionGapDegreeThreshold"].ToString());
                }
                if (param.ContainsKey("DistanceRangeThreshold"))
                {
                    distanceRangeThreshold = double.Parse(param["DistanceRangeThreshold"].ToString());
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new NonconformityProperties_TDSCAN(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (CellData block in nonconformityCellData)
            {
                BackgroundResult result = block.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), Condition.Periods[0].IBeginTime,
                Condition.Periods[0].IEndTime, bgResultList);
            nonconformityCellData.Clear();
        }

        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                int abnormalCount = bgResult.GetImageValueInt();
                int normalCount = bgResult.GetImageValueInt();
                float abnormalRate = bgResult.GetImageValueFloat();
                float distanceAvg = bgResult.GetImageValueFloat();
                float distanceClosest = bgResult.GetImageValueFloat();
                float distanceFarest = bgResult.GetImageValueFloat();
                StringBuilder sb = new StringBuilder();
                sb.Append("覆盖不符采样点数：");
                sb.Append(abnormalCount);
                sb.Append("\r\n");
                sb.Append("正常采样点数：");
                sb.Append(normalCount);
                sb.Append("\r\n");
                sb.Append("异常采样点比例(%)：");
                sb.Append(abnormalRate);
                sb.Append("\r\n");
                sb.Append("平均覆盖距离：");
                sb.Append(distanceAvg);
                sb.Append("\r\n");
                sb.Append("最近覆盖距离：");
                sb.Append(distanceClosest);
                sb.Append("\r\n");
                sb.Append("最远覆盖距离：");
                sb.Append(distanceFarest);
                bgResult.ImageDesc = sb.ToString();
            }
        }
        #endregion

        public class CellData
        {
            public TDCell tdcell { get; set; }
            public DefineCell dcell { get; set; }
            public List<TestPoint> nonconformityTpList { get; set; }
            public short DirectionCfg
            {
                get { return tdcell.Direction; }
            }
            public int WrongDirMean
            { get; private set; }

            public int WrongDirMax
            { get; private set; }

            public int WrongDirMin
            { get; private set; }

            public int DirDiff
            {
                get;
                private set;
            }

            public void CalcWrongDir()
            {
                WrongDirMax = -1;
                WrongDirMin = 361;
                foreach (TestPoint tp in nonconformityTpList)
                {
                    int dir = MathFuncs.getAngleFromPointToPoint(tdcell.Longitude, tdcell.Latitude
                  , tp.Longitude, tp.Latitude);
                    WrongDirMin = Math.Min(dir, WrongDirMin);
                    WrongDirMax = Math.Max(dir, WrongDirMax);
                }
                int diffDir = WrongDirMax - WrongDirMin;
                if (diffDir > 180)
                {
                    double meanDir = (360 - diffDir) / 2.0;
                    if (meanDir > WrongDirMin)
                    {
                        WrongDirMean = (int)(360 - (meanDir - WrongDirMin));
                    }
                    else
                    {
                        WrongDirMean = (int)(WrongDirMin - meanDir);
                    }
                }
                else
                {
                    double halfDir = diffDir / 2.0;
                    WrongDirMean = (short)(WrongDirMin + halfDir);
                }

                DirDiff = Math.Abs(WrongDirMean - tdcell.Direction);
                DirDiff = DirDiff > 180 ? 360 - DirDiff : DirDiff;
            }
            public int countTpAll { get; set; }
            public double sumDistance { get; set; }
            public double closestDistance { get; set; }
            public double farestDistance { get; set; }
            public double AvgDistance
            {
                get { return Math.Round(sumDistance / nonconformityTpList.Count, 2); }
            }

            public BackgroundResult ConvertToBackgroundResult()
            {
                BackgroundResult bgResult = new BackgroundResult();
                bgResult.CellType = BackgroundCellType.TD;
                bgResult.LAC = tdcell.LAC;
                bgResult.CI = tdcell.CI;
                bgResult.BCCH = tdcell.FREQ;
                bgResult.BSIC = tdcell.CPI;
                TestPoint tpFirst = nonconformityTpList[0];
                bgResult.LongitudeMid = tpFirst.Longitude;
                bgResult.LatitudeMid = tpFirst.Latitude;
                bgResult.ISTime = tpFirst.Time;
                bgResult.IETime = tpFirst.Time;
                bgResult.AddImageValue(nonconformityTpList.Count);
                bgResult.AddImageValue(countTpAll - nonconformityTpList.Count);
                bgResult.AddImageValue((float)Math.Round(100 * ((double)nonconformityTpList.Count / (double)countTpAll), 2));
                bgResult.AddImageValue((float)AvgDistance);
                bgResult.AddImageValue((float)closestDistance);
                bgResult.AddImageValue((float)farestDistance);
                return bgResult;
            }
        }

        public class DefineCell
        {
            public string Cellname{ get; set; }
            public short Freq{ get; set; }
            public byte Cpi{ get; set; }
            public double Longitude{ get; set; }
            public double Latitude{ get; set; }
            public int Lac{ get; set; }
            public int Ci{ get; set; }
            public int Direction{ get; set; }
            public string Indoor{ get; set; }

            public double GetDistance(double x, double y)
            {
                double longitudeDistance = (Math.Sin((90 - Latitude) * 2 * Math.PI / 360) + Math.Sin((90 - y) * 2 * Math.PI / 360)) / 2 * (Longitude - x) / 360 * 40075360;
                double latitudeDistance = (Latitude - y) / 360 * 39940670;
                return Math.Sqrt(longitudeDistance * longitudeDistance + latitudeDistance * latitudeDistance);
            }
        }

        public class NonconformityCoverData
        {
            //采样点
            public double longitude{ get; set; }
            public double latitude{ get; set; }
            public DateTime time{ get; set; }
            public float TDS_PCCPCH_RSSI{ get; set; } //int(float*1000)
            public float TDS_PCCPCH_RSCP{ get; set; }
            public float TDS_PCCPCH_ISCP{ get; set; }//int(float*1000)
            public float TDS_PCCPCH_C_I{ get; set; }//int(float*1000)
            public float TDS_PCCPCH_Ec_Io{ get; set; }//int(float*1000)
            public float TDS_PCCPCH_SIR{ get; set; }//int(float*1000)
            public float TDS_DwPTS_RSSI{ get; set; }//int(float*1000)
            public float TDS_DwPTS_RSCP{ get; set; }//int(float*1000)
            public float TDS_DwPTS_ISCP{ get; set; }//int(float*1000)
            public float TDS_DwPTS_C_I{ get; set; }//int(float*1000)
            public float TDS_DwPTS_Ec_Io{ get; set; }//int(float*1000)
            public float TDS_DwPTS_SIR{ get; set; }//int(float*1000)
            public double distance{ get; set; }
            public double relativeAngle{ get; set; } //采样点与小区主瓣角的夹角
            public string protocol{ get; set; }  //网络类型
            //

            //小区
            public string cellname{ get; set; }
            public int UARFCN{ get; set; }
            public int CPI{ get; set; }
            //
        }
    }

    public class ZTDIYWCDMANonconformity : DIYAnalyseByCellBackgroundBaseByPeriod
    {
        public bool checkCellRank { get; set; }
        public int cellRank { get; set; }
        public bool checkPccpchRscpThreshold { get; set; }
        public double pccpchRscpThreshold { get; set; }         //-85dBm
        public double directionGapDegreeThreshold { get; set; }  //50%以上
        public double distanceRangeThreshold { get; set; }          //0.6km以上
        int tpNum = 0;
        bool isDefineCell = false; //是否选用自导入的小区工参
        Dictionary<WCell, CellData> cellDataDic = null;
        Dictionary<DefineCell, CellData> dcellDataDic = null;
        Dictionary<string, List<DefineCell>> freqCpi_defineCellsDic = null;
        readonly List<CellData> nonconformityCellData = new List<CellData>();
        bool showForm = false;

        private static ZTDIYWCDMANonconformity intance = null;
        protected static readonly object lockObj = new object();
        public static ZTDIYWCDMANonconformity GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYWCDMANonconformity();
                    }
                }
            }
            return intance;
        }

        protected ZTDIYWCDMANonconformity()
            : base(MainModel.GetInstance())
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.TD_SCAN);
            carrierID = CarrierType.ChinaMobile;

            checkCellRank = true;
            cellRank = 5;    //5级
            checkPccpchRscpThreshold = true;
            pccpchRscpThreshold = -85;
            directionGapDegreeThreshold = 50;
            distanceRangeThreshold = 0.6;
        }

        public override string Name
        {
            get { return "覆盖不符_WCDMA扫频"; }
        }

        NonconformityWCDMAConditionDlg conditionDlg = null;
        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                isDefineCell = false;
                return true;
            }
            if (conditionDlg == null)
            {
                conditionDlg = new NonconformityWCDMAConditionDlg();
            }
            if (conditionDlg.ShowDialog() == DialogResult.OK)
            {
                bool res = getConditionData();
                return res;
            }
            return false;
        }

        private bool getConditionData()
        {
            int cellRankTmp;
            double pccpchRscpThresholdTmp;
            double directionGapDegreeThresholdTmp;
            double distanceRangeThresholdTmp;
            conditionDlg.getCondition(out cellRankTmp, out pccpchRscpThresholdTmp, out directionGapDegreeThresholdTmp, out distanceRangeThresholdTmp, out tpNum, out isDefineCell);
            cellRank = cellRankTmp;
            pccpchRscpThreshold = pccpchRscpThresholdTmp;
            directionGapDegreeThreshold = directionGapDegreeThresholdTmp;
            distanceRangeThreshold = distanceRangeThresholdTmp;
            checkCellRank = conditionDlg.EnableCellRank;
            checkPccpchRscpThreshold = conditionDlg.EnablePccpch_Rscp;

            if (isDefineCell)
            {
                freqCpi_defineCellsDic = new Dictionary<string, List<DefineCell>>();
                if (conditionDlg.defineCellList == null)
                    return false;
                foreach (DefineCell cell in conditionDlg.defineCellList)
                {
                    string fckey = cell.Freq + "_" + cell.Cpi;
                    if (!freqCpi_defineCellsDic.ContainsKey(fckey))
                    {
                        List<DefineCell> cells = new List<DefineCell>();
                        cells.Add(cell);
                        freqCpi_defineCellsDic.Add(fckey, cells);
                    }
                    else
                    {
                        freqCpi_defineCellsDic[fckey].Add(cell);
                    }
                }
            }
            return true;
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup leakGroup = new DIYSampleGroup();
            leakGroup.ThemeName = "---";
            DTParameter parameter= DTParameterManager.GetInstance().GetParameter("WS_CPICHTotalRSCP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("WS_CPICHTotalEcIo");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("WS_CPICHSIR");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("WS_CPICHChannel");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("WS_CPICHPilot");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }

            return leakGroup;
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            cellDataDic = new Dictionary<WCell, CellData>();
            dcellDataDic = new Dictionary<DefineCell, CellData>();
            nonconformityCellData.Clear();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            for (int i = 0; i < 50; i++)
            {
                if (checkCellRank && i > cellRank)
                {
                    break;
                }

                bool isValid = dealTestPoints(tp, i);
                if (!isValid)
                {
                    break;
                }
            }
        }

        private bool dealTestPoints(TestPoint tp, int i)
        {
            if (checkPccpchRscpThreshold)
            {
                float? rscp = (float?)tp["WS_CPICHTotalRSCP", i];
                if (rscp != null && rscp > pccpchRscpThreshold)
                {
                    short? freq = tp["WS_CPICHChannel", i] as short?;
                    short? cpi = tp["WS_CPICHPilot", i] as short?;
                    if (freq == null || cpi == null)
                    {
                        return false;
                    }

                    addDataByCellType(tp, freq, cpi, MainModel.SystemConfigInfo.distLimit);
                }
            }
            else
            {
                short? freq = tp["WS_CPICHChannel", i] as short?;
                short? cpi = tp["WS_CPICHPilot", i] as short?;
                if (freq == null || cpi == null)
                {
                    return false;
                }

                addDataByCellType(tp, freq, cpi, false);
            }
            return true;
        }

        private void addDataByCellType(TestPoint tp, short? freq, short? cpi, bool distLimit)
        {
            if (!isDefineCell)
            {
                WCell cell = CellManager.GetInstance().GetNearestWCell(tp.DateTime, (short)freq, (short)cpi, tp.Longitude, tp.Latitude);
                if (cell != null)
                {
                    if (distLimit && cell.GetDistance(tp.Longitude, tp.Latitude) >= CD.MAX_COV_DISTANCE_TD)//距离限制设置
                    {
                        return;
                    }
                    addCellDataDic(tp, cell);
                }
            }
            else
            {
                DefineCell cell = getCellFromTable((short)freq, (byte)cpi, tp.Longitude, tp.Latitude);
                if (cell != null)
                {
                    if (distLimit && cell.GetDistance(tp.Longitude, tp.Latitude) >= CD.MAX_COV_DISTANCE_TD)//距离限制设置
                    {
                        return;
                    }
                    addCellDataDic(tp, cell);
                }
            }
        }

        private void addCellDataDic(TestPoint tp, DefineCell cell)
        {
            double distance = cell.GetDistance(tp.Longitude, tp.Latitude);
            double angleDiff;
            if (!isValidAngle(cell, tp.Longitude, tp.Latitude, out angleDiff) && distance > distanceRangeThreshold)
            {
                addCellData(tp, cell, distance);
            }
            else if (dcellDataDic.ContainsKey(cell))
            {
                dcellDataDic[cell].countTpAll++;
            }
        }

        private void addCellDataDic(TestPoint tp, WCell cell)
        {
            double distance = cell.GetDistance(tp.Longitude, tp.Latitude);
            double angleDiff;
            if (!isValidAngle(cell, tp.Longitude, tp.Latitude, out angleDiff) && distance > distanceRangeThreshold)
            {
                addCellData(tp, cell, distance);
            }
            else if (cellDataDic.ContainsKey(cell))
            {
                cellDataDic[cell].countTpAll++;
            }
        }

        private void addCellData(TestPoint tp, DefineCell cell, double distance)
        {
            if (dcellDataDic.ContainsKey(cell))
            {
                dcellDataDic[cell].nonconformityTpList.Add(tp);
                dcellDataDic[cell].countTpAll++;

                ///统计采样点到小区的距离
                dcellDataDic[cell].sumDistance += distance; //小区距离采样点总距离
                if (dcellDataDic[cell].closestDistance > distance)
                {
                    dcellDataDic[cell].closestDistance = distance; //小区离采样点最近距离
                }
                if (dcellDataDic[cell].farestDistance < distance)
                {
                    dcellDataDic[cell].farestDistance = distance; //小区离采样点最远距离
                }
            }
            else
            {
                dcellDataDic[cell] = new CellData();
                dcellDataDic[cell].nonconformityTpList = new List<TestPoint>();
                dcellDataDic[cell].nonconformityTpList.Add(tp);
                dcellDataDic[cell].dcell = cell;
                dcellDataDic[cell].countTpAll++;

                ///统计采样点到小区的距离
                dcellDataDic[cell].sumDistance = distance;
                dcellDataDic[cell].closestDistance = distance;
                dcellDataDic[cell].farestDistance = distance;
            }
        }

        private void addCellData(TestPoint tp, WCell cell, double distance)
        {
            if (cellDataDic.ContainsKey(cell))
            {
                cellDataDic[cell].nonconformityTpList.Add(tp);
                cellDataDic[cell].countTpAll++;

                ///统计采样点到小区的距离
                cellDataDic[cell].sumDistance += distance; //小区距离采样点总距离
                if (cellDataDic[cell].closestDistance > distance)
                {
                    cellDataDic[cell].closestDistance = distance; //小区离采样点最近距离
                }
                if (cellDataDic[cell].farestDistance < distance)
                {
                    cellDataDic[cell].farestDistance = distance; //小区离采样点最远距离
                }
            }
            else
            {
                cellDataDic[cell] = new CellData();
                cellDataDic[cell].nonconformityTpList = new List<TestPoint>();
                cellDataDic[cell].nonconformityTpList.Add(tp);
                cellDataDic[cell].wCell = cell;
                cellDataDic[cell].countTpAll++;

                ///统计采样点到小区的距离
                cellDataDic[cell].sumDistance = distance;
                cellDataDic[cell].closestDistance = distance;
                cellDataDic[cell].farestDistance = distance;
            }
        }

        protected override void getResultAfterQuery()
        {
            if (MainModel.IsBackground)
            {
                getResult();
                return;
            }
            getResult();
            showForm = true;
        }

        private void getResult()
        {
            if (!isDefineCell)
            {
                foreach (CellData cd in cellDataDic.Values)
                {
                    if (cd.nonconformityTpList.Count > tpNum)
                    {
                        nonconformityCellData.Add(cd);
                    }
                }
            }
            else
            {
                foreach (CellData cd in dcellDataDic.Values)
                {
                    if (cd.nonconformityTpList.Count > tpNum)
                    {
                        nonconformityCellData.Add(cd);
                    }
                }
            }
            cellDataDic = null;
            dcellDataDic = null;
        }

        protected override void FireShowFormAfterQuery()
        {
            if (showForm)
            {
                WCDMAScanNonconformityForm wScanNonconformityForm =
                    MainModel.GetInstance().CreateResultForm(typeof(WCDMAScanNonconformityForm)) as WCDMAScanNonconformityForm;
                wScanNonconformityForm.FillData(nonconformityCellData, "否");
                wScanNonconformityForm.Visible = true;
                wScanNonconformityForm.BringToFront();
            }
        }

        /// <summary>
        /// 采样点到小区的夹角是否属于正常角度
        /// </summary>
        /// <param name="cell"></param>
        /// <param name="longitude"></param>
        /// <param name="latitude"></param>
        /// <returns></returns>
        private bool isValidAngle(WCell cell, double longitude, double latitude, out double angleDiff)
        {
            angleDiff = 0;
            double distance = cell.GetDistance(longitude, latitude);
            if (distance > distanceRangeThreshold * 1000)
            {
                ///所有角度按正北方向算起始，顺时针算夹角，正北为0度
                double angle;
                double ygap = cell.GetDistance(cell.Longitude, latitude);
                double angleV = Math.Acos(ygap / distance);
                if (longitude >= cell.Longitude && latitude >= cell.Latitude)//1象限
                {
                    angle = angleV * 180 / Math.PI;
                }
                else if (longitude <= cell.Longitude && latitude >= cell.Latitude)//2象限
                {
                    angle = 360 - angleV * 180 / Math.PI;
                }
                else if (longitude <= cell.Longitude && latitude <= cell.Latitude)//3象限
                {
                    angle = 180 + angleV * 180 / Math.PI;
                }
                else//4象限
                {
                    angle = 180 - angleV * 180 / Math.PI;
                }

                angleDiff = Math.Abs(angle - cell.Direction);
                if (angleDiff > 180)
                {
                    angleDiff = 360 - angleDiff;
                }
                if (angleDiff > 65 * (0.5 + directionGapDegreeThreshold * 0.01))
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 采样点到小区的夹角是否属于正常角度
        /// </summary>
        /// <param name="cell"></param>
        /// <param name="longitude"></param>
        /// <param name="latitude"></param>
        /// <returns></returns>
        private bool isValidAngle(DefineCell cell, double longitude, double latitude, out double angleDiff)
        {
            angleDiff = 0;
            if (cell.Direction == -1 || longitude == 0 || latitude == 0)
            {
                return true;
            }
            double distance = cell.GetDistance(longitude, latitude);
            if (distance > distanceRangeThreshold * 1000)
            {
                ///所有角度按正北方向算起始，顺时针算夹角，正北为0度
                double angle;
                double ygap = cell.GetDistance(cell.Longitude, latitude);
                double angleV = Math.Acos(ygap / distance);
                if (longitude >= cell.Longitude && latitude >= cell.Latitude)//1象限
                {
                    angle = angleV * 180 / Math.PI;
                }
                else if (longitude <= cell.Longitude && latitude >= cell.Latitude)//2象限
                {
                    angle = 360 - angleV * 180 / Math.PI;
                }
                else if (longitude <= cell.Longitude && latitude <= cell.Latitude)//3象限
                {
                    angle = 180 + angleV * 180 / Math.PI;
                }
                else//4象限
                {
                    angle = 180 - angleV * 180 / Math.PI;
                }

                angleDiff = Math.Abs(angle - cell.Direction);
                if (angleDiff > 180)
                {
                    angleDiff = 360 - angleDiff;
                }
                if (angleDiff > 65 * (0.5 + directionGapDegreeThreshold * 0.01))
                {
                    return false;
                }
            }
            return true;
        }

        private DefineCell getCellFromTable(short freq, byte cpi, double longitude, double latitude)
        {
            DefineCell tarCell = null;
            string fckey = freq + "_" + cpi;
            if (freqCpi_defineCellsDic.ContainsKey(fckey))
            {
                tarCell = freqCpi_defineCellsDic[fckey][0];
                double minDistance = tarCell.GetDistance(longitude, latitude);
                for (int i = 1; i < freqCpi_defineCellsDic[fckey].Count; i++)
                {
                    double distance = freqCpi_defineCellsDic[fckey][i].GetDistance(longitude, latitude);
                    if (minDistance > distance)
                    {
                        minDistance = distance;
                        tarCell = freqCpi_defineCellsDic[fckey][i];
                    }
                }
                return tarCell;
            }
            return null;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.WCDMA扫频; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (CellData block in nonconformityCellData)
            {
                BackgroundResult result = block.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), Condition.Periods[0].IBeginTime,
                Condition.Periods[0].IEndTime, bgResultList);
            nonconformityCellData.Clear();
        }

        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                int abnormalCount = bgResult.GetImageValueInt();
                int normalCount = bgResult.GetImageValueInt();
                float abnormalRate = bgResult.GetImageValueFloat();
                float distanceAvg = bgResult.GetImageValueFloat();
                float distanceClosest = bgResult.GetImageValueFloat();
                float distanceFarest = bgResult.GetImageValueFloat();
                StringBuilder sb = new StringBuilder();
                sb.Append("覆盖不符采样点数：");
                sb.Append(abnormalCount);
                sb.Append("\r\n");
                sb.Append("正常采样点数：");
                sb.Append(normalCount);
                sb.Append("\r\n");
                sb.Append("异常采样点比例(%)：");
                sb.Append(abnormalRate);
                sb.Append("\r\n");
                sb.Append("平均覆盖距离：");
                sb.Append(distanceAvg);
                sb.Append("\r\n");
                sb.Append("最近覆盖距离：");
                sb.Append(distanceClosest);
                sb.Append("\r\n");
                sb.Append("最远覆盖距离：");
                sb.Append(distanceFarest);
                bgResult.ImageDesc = sb.ToString();
            }
        }
        #endregion

        public class CellData
        {
            public WCell wCell { get; set; }
            public DefineCell dcell { get; set; }
            public List<TestPoint> nonconformityTpList { get; set; }
            public short DirectionCfg
            {
                get { return wCell.Direction; }
            }
            public int WrongDirMean
            { get; private set; }

            public int WrongDirMax
            { get; private set; }

            public int WrongDirMin
            { get; private set; }

            public int DirDiff
            {
                get;
                private set;
            }

            public void CalcWrongDir()
            {
                WrongDirMax = -1;
                WrongDirMin = 361;
                foreach (TestPoint tp in nonconformityTpList)
                {
                    int dir = MathFuncs.getAngleFromPointToPoint(wCell.Longitude, wCell.Latitude
                  , tp.Longitude, tp.Latitude);
                    WrongDirMin = Math.Min(dir, WrongDirMin);
                    WrongDirMax = Math.Max(dir, WrongDirMax);
                }
                int diffDir = WrongDirMax - WrongDirMin;
                if (diffDir > 180)
                {
                    double meanDir = (360 - diffDir) / 2.0;
                    if (meanDir > WrongDirMin)
                    {
                        WrongDirMean = (int)(360 - (meanDir - WrongDirMin));
                    }
                    else
                    {
                        WrongDirMean = (int)(WrongDirMin - meanDir);
                    }
                }
                else
                {
                    double halfDir = diffDir / 2.0;
                    WrongDirMean = (short)(WrongDirMin + halfDir);
                }

                DirDiff = Math.Abs(WrongDirMean - wCell.Direction);
                DirDiff = DirDiff > 180 ? 360 - DirDiff : DirDiff;
            }
            public int countTpAll { get; set; }
            public double sumDistance { get; set; }
            public double closestDistance { get; set; }
            public double farestDistance { get; set; }
            public double AvgDistance
            {
                get { return Math.Round(sumDistance / nonconformityTpList.Count, 2); }
            }

            public BackgroundResult ConvertToBackgroundResult()
            {
                BackgroundResult bgResult = new BackgroundResult();
                bgResult.CellType = BackgroundCellType.TD;
                bgResult.LAC = wCell.LAC;
                bgResult.CI = wCell.CI;
                bgResult.BCCH = wCell.UARFCN;
                bgResult.BSIC = wCell.PSC;
                TestPoint tpFirst = nonconformityTpList[0];
                bgResult.LongitudeMid = tpFirst.Longitude;
                bgResult.LatitudeMid = tpFirst.Latitude;
                bgResult.ISTime = tpFirst.Time;
                bgResult.IETime = tpFirst.Time;
                bgResult.AddImageValue(nonconformityTpList.Count);
                bgResult.AddImageValue(countTpAll - nonconformityTpList.Count);
                bgResult.AddImageValue((float)Math.Round(100 * ((double)nonconformityTpList.Count / (double)countTpAll), 2));
                bgResult.AddImageValue((float)AvgDistance);
                bgResult.AddImageValue((float)closestDistance);
                bgResult.AddImageValue((float)farestDistance);
                return bgResult;
            }
        }

        public class DefineCell
        {
            public string Cellname{ get; set; }
            public short Freq{ get; set; }
            public byte Cpi{ get; set; }
            public double Longitude{ get; set; }
            public double Latitude{ get; set; }
            public int Lac{ get; set; }
            public int Ci{ get; set; }
            public int Direction{ get; set; }
            public string Indoor{ get; set; }

            public double GetDistance(double x, double y)
            {
                double longitudeDistance = (Math.Sin((90 - Latitude) * 2 * Math.PI / 360) + Math.Sin((90 - y) * 2 * Math.PI / 360)) / 2 * (Longitude - x) / 360 * 40075360;
                double latitudeDistance = (Latitude - y) / 360 * 39940670;
                return Math.Sqrt(longitudeDistance * longitudeDistance + latitudeDistance * latitudeDistance);
            }
        }

        public class NonconformityCoverData
        {
            //采样点
            public double longitude{ get; set; }
            public double latitude{ get; set; }
            public DateTime time{ get; set; }
            public float TDS_PCCPCH_RSSI{ get; set; } //int(float*1000)
            public float TDS_PCCPCH_RSCP{ get; set; }
            public float TDS_PCCPCH_ISCP{ get; set; }//int(float*1000)
            public float TDS_PCCPCH_C_I{ get; set; }//int(float*1000)
            public float TDS_PCCPCH_Ec_Io{ get; set; }//int(float*1000)
            public float TDS_PCCPCH_SIR{ get; set; }//int(float*1000)
            public float TDS_DwPTS_RSSI{ get; set; }//int(float*1000)
            public float TDS_DwPTS_RSCP{ get; set; }//int(float*1000)
            public float TDS_DwPTS_ISCP{ get; set; }//int(float*1000)
            public float TDS_DwPTS_C_I{ get; set; }//int(float*1000)
            public float TDS_DwPTS_Ec_Io{ get; set; }//int(float*1000)
            public float TDS_DwPTS_SIR{ get; set; }//int(float*1000)
            public double distance{ get; set; }
            public double relativeAngle{ get; set; } //采样点与小区主瓣角的夹角
            public string protocol{ get; set; }  //网络类型
            //

            //小区
            public string cellname{ get; set; }
            public int UARFCN{ get; set; }
            public int CPI{ get; set; }
            //
        }
    }
}
