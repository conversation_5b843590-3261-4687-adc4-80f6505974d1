﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LtePCIOptimizeNewSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.panelMain = new System.Windows.Forms.Panel();
            this.panelOne = new System.Windows.Forms.Panel();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditThreadNum = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditVarRate = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditCrossRate = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditPopularSize = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditIteration = new DevExpress.XtraEditors.SpinEdit();
            this.panelTwo = new System.Windows.Forms.Panel();
            this.comboBoxEditDB = new DevExpress.XtraEditors.ComboBoxEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.labelAdd = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.textBoxCellName = new System.Windows.Forms.TextBox();
            this.checkBoxIncludeIndoor = new System.Windows.Forms.CheckBox();
            this.checkBoxAll = new System.Windows.Forms.CheckBox();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.listViewCell = new System.Windows.Forms.ListView();
            this.panelThree = new System.Windows.Forms.Panel();
            this.groupControl6 = new DevExpress.XtraEditors.GroupControl();
            this.listBoxTime = new System.Windows.Forms.ListBox();
            this.groupControl5 = new DevExpress.XtraEditors.GroupControl();
            this.radioButtonBg = new System.Windows.Forms.RadioButton();
            this.radioButtonCmd = new System.Windows.Forms.RadioButton();
            this.panelFour = new System.Windows.Forms.Panel();
            this.groupControl4 = new DevExpress.XtraEditors.GroupControl();
            this.wyzProgressSSS = new MasterCom.RAMS.ZTFunc.WyzProgress();
            this.wyzProgressPSS = new MasterCom.RAMS.ZTFunc.WyzProgress();
            this.labelDesc = new System.Windows.Forms.Label();
            this.simpleButtonUp = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonDown = new DevExpress.XtraEditors.SimpleButton();
            this.bgWorkerOptimize = new System.ComponentModel.BackgroundWorker();
            this.panelMain.SuspendLayout();
            this.panelOne.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditThreadNum.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditVarRate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCrossRate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPopularSize.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditIteration.Properties)).BeginInit();
            this.panelTwo.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEditDB.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            this.panelThree.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl6)).BeginInit();
            this.groupControl6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl5)).BeginInit();
            this.groupControl5.SuspendLayout();
            this.panelFour.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).BeginInit();
            this.groupControl4.SuspendLayout();
            this.SuspendLayout();
            // 
            // panelMain
            // 
            this.panelMain.Controls.Add(this.panelOne);
            this.panelMain.Controls.Add(this.panelTwo);
            this.panelMain.Controls.Add(this.panelThree);
            this.panelMain.Controls.Add(this.panelFour);
            this.panelMain.Location = new System.Drawing.Point(3, 0);
            this.panelMain.Name = "panelMain";
            this.panelMain.Size = new System.Drawing.Size(668, 322);
            this.panelMain.TabIndex = 0;
            // 
            // panelOne
            // 
            this.panelOne.Controls.Add(this.groupControl2);
            this.panelOne.Controls.Add(this.groupControl1);
            this.panelOne.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelOne.Location = new System.Drawing.Point(0, 0);
            this.panelOne.Name = "panelOne";
            this.panelOne.Size = new System.Drawing.Size(668, 322);
            this.panelOne.TabIndex = 0;
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.labelControl15);
            this.groupControl2.Controls.Add(this.labelControl14);
            this.groupControl2.Controls.Add(this.labelControl13);
            this.groupControl2.Controls.Add(this.labelControl12);
            this.groupControl2.Controls.Add(this.labelControl11);
            this.groupControl2.Location = new System.Drawing.Point(3, 182);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(662, 137);
            this.groupControl2.TabIndex = 3;
            this.groupControl2.Text = "参数说明";
            // 
            // labelControl15
            // 
            this.labelControl15.Location = new System.Drawing.Point(7, 106);
            this.labelControl15.Name = "labelControl15";
            this.labelControl15.Size = new System.Drawing.Size(640, 14);
            this.labelControl15.TabIndex = 0;
            this.labelControl15.Text = "工作线程数：遗传算法的并行线程数，线程数越多，计算越快。但是该值不能大于服务器的CPU个数，建议取值范围为4-8";
            // 
            // labelControl14
            // 
            this.labelControl14.Location = new System.Drawing.Point(7, 86);
            this.labelControl14.Name = "labelControl14";
            this.labelControl14.Size = new System.Drawing.Size(576, 14);
            this.labelControl14.TabIndex = 0;
            this.labelControl14.Text = "变异率：变异是遗传算法中一个个体的一部分基因突变束产生新个体的一种方法，变异率一般取默认值即可。";
            // 
            // labelControl13
            // 
            this.labelControl13.Location = new System.Drawing.Point(7, 66);
            this.labelControl13.Name = "labelControl13";
            this.labelControl13.Size = new System.Drawing.Size(552, 14);
            this.labelControl13.TabIndex = 0;
            this.labelControl13.Text = "交叉率：交叉式遗传算法中两个个体通过交换基因束产生新个体的一种方法，交叉率一般取默认值即可。";
            // 
            // labelControl12
            // 
            this.labelControl12.Location = new System.Drawing.Point(7, 46);
            this.labelControl12.Name = "labelControl12";
            this.labelControl12.Size = new System.Drawing.Size(634, 14);
            this.labelControl12.TabIndex = 0;
            this.labelControl12.Text = "种群大小：指遗传算法中的一次迭代需要计算的个体(即方案)个数。种群越大，单次迭代的结果就越优化，但耗时越长。";
            // 
            // labelControl11
            // 
            this.labelControl11.Location = new System.Drawing.Point(7, 26);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(504, 14);
            this.labelControl11.TabIndex = 0;
            this.labelControl11.Text = "最大迭代次数：指遗传算法的迭代计算次数，迭代次数越多，结果就越优化，但是耗时就越长。";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.labelControl10);
            this.groupControl1.Controls.Add(this.labelControl8);
            this.groupControl1.Controls.Add(this.labelControl6);
            this.groupControl1.Controls.Add(this.labelControl4);
            this.groupControl1.Controls.Add(this.labelControl2);
            this.groupControl1.Controls.Add(this.labelControl9);
            this.groupControl1.Controls.Add(this.labelControl7);
            this.groupControl1.Controls.Add(this.labelControl5);
            this.groupControl1.Controls.Add(this.labelControl3);
            this.groupControl1.Controls.Add(this.labelControl1);
            this.groupControl1.Controls.Add(this.spinEditThreadNum);
            this.groupControl1.Controls.Add(this.spinEditVarRate);
            this.groupControl1.Controls.Add(this.spinEditCrossRate);
            this.groupControl1.Controls.Add(this.spinEditPopularSize);
            this.groupControl1.Controls.Add(this.spinEditIteration);
            this.groupControl1.Location = new System.Drawing.Point(3, 3);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(662, 173);
            this.groupControl1.TabIndex = 2;
            this.groupControl1.Text = "模式设置";
            // 
            // labelControl10
            // 
            this.labelControl10.Appearance.ForeColor = System.Drawing.Color.LightCoral;
            this.labelControl10.Appearance.Options.UseForeColor = true;
            this.labelControl10.Location = new System.Drawing.Point(262, 146);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(133, 14);
            this.labelControl10.TabIndex = 0;
            this.labelControl10.Text = "范围：1~64      默认：1";
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.ForeColor = System.Drawing.Color.LightCoral;
            this.labelControl8.Appearance.Options.UseForeColor = true;
            this.labelControl8.Location = new System.Drawing.Point(262, 119);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(140, 14);
            this.labelControl8.TabIndex = 0;
            this.labelControl8.Text = "范围：1~99      默认：10";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.ForeColor = System.Drawing.Color.LightCoral;
            this.labelControl6.Appearance.Options.UseForeColor = true;
            this.labelControl6.Location = new System.Drawing.Point(262, 92);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(140, 14);
            this.labelControl6.TabIndex = 0;
            this.labelControl6.Text = "范围：1~99      默认：20";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.ForeColor = System.Drawing.Color.LightCoral;
            this.labelControl4.Appearance.Options.UseForeColor = true;
            this.labelControl4.Location = new System.Drawing.Point(262, 65);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(175, 14);
            this.labelControl4.TabIndex = 0;
            this.labelControl4.Text = "范围：10~10000      默认：100";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.ForeColor = System.Drawing.Color.LightCoral;
            this.labelControl2.Appearance.Options.UseForeColor = true;
            this.labelControl2.Location = new System.Drawing.Point(262, 38);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(182, 14);
            this.labelControl2.TabIndex = 0;
            this.labelControl2.Text = "范围：10~100000      默认：200";
            // 
            // labelControl9
            // 
            this.labelControl9.Location = new System.Drawing.Point(22, 146);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(72, 14);
            this.labelControl9.TabIndex = 0;
            this.labelControl9.Text = "工作线程数：";
            // 
            // labelControl7
            // 
            this.labelControl7.Location = new System.Drawing.Point(46, 119);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(48, 14);
            this.labelControl7.TabIndex = 0;
            this.labelControl7.Text = "变异率：";
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(46, 92);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(48, 14);
            this.labelControl5.TabIndex = 0;
            this.labelControl5.Text = "交叉率：";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(34, 65);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(60, 14);
            this.labelControl3.TabIndex = 0;
            this.labelControl3.Text = "种群大小：";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(10, 38);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(84, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "最大迭代次数：";
            // 
            // spinEditThreadNum
            // 
            this.spinEditThreadNum.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.spinEditThreadNum.Location = new System.Drawing.Point(121, 143);
            this.spinEditThreadNum.Name = "spinEditThreadNum";
            this.spinEditThreadNum.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditThreadNum.Properties.Mask.EditMask = "f0";
            this.spinEditThreadNum.Size = new System.Drawing.Size(100, 21);
            this.spinEditThreadNum.TabIndex = 1;
            // 
            // spinEditVarRate
            // 
            this.spinEditVarRate.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditVarRate.Location = new System.Drawing.Point(121, 116);
            this.spinEditVarRate.Name = "spinEditVarRate";
            this.spinEditVarRate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditVarRate.Properties.Mask.EditMask = "f0";
            this.spinEditVarRate.Size = new System.Drawing.Size(100, 21);
            this.spinEditVarRate.TabIndex = 1;
            // 
            // spinEditCrossRate
            // 
            this.spinEditCrossRate.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.spinEditCrossRate.Location = new System.Drawing.Point(121, 89);
            this.spinEditCrossRate.Name = "spinEditCrossRate";
            this.spinEditCrossRate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditCrossRate.Properties.Mask.EditMask = "f0";
            this.spinEditCrossRate.Size = new System.Drawing.Size(100, 21);
            this.spinEditCrossRate.TabIndex = 1;
            // 
            // spinEditPopularSize
            // 
            this.spinEditPopularSize.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditPopularSize.Location = new System.Drawing.Point(121, 62);
            this.spinEditPopularSize.Name = "spinEditPopularSize";
            this.spinEditPopularSize.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditPopularSize.Properties.Mask.EditMask = "f0";
            this.spinEditPopularSize.Size = new System.Drawing.Size(100, 21);
            this.spinEditPopularSize.TabIndex = 1;
            // 
            // spinEditIteration
            // 
            this.spinEditIteration.EditValue = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.spinEditIteration.Location = new System.Drawing.Point(121, 35);
            this.spinEditIteration.Name = "spinEditIteration";
            this.spinEditIteration.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditIteration.Properties.Mask.EditMask = "f0";
            this.spinEditIteration.Size = new System.Drawing.Size(100, 21);
            this.spinEditIteration.TabIndex = 1;
            // 
            // panelTwo
            // 
            this.panelTwo.Controls.Add(this.comboBoxEditDB);
            this.panelTwo.Controls.Add(this.label2);
            this.panelTwo.Controls.Add(this.labelAdd);
            this.panelTwo.Controls.Add(this.label1);
            this.panelTwo.Controls.Add(this.textBoxCellName);
            this.panelTwo.Controls.Add(this.checkBoxIncludeIndoor);
            this.panelTwo.Controls.Add(this.checkBoxAll);
            this.panelTwo.Controls.Add(this.groupControl3);
            this.panelTwo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelTwo.Location = new System.Drawing.Point(0, 0);
            this.panelTwo.Name = "panelTwo";
            this.panelTwo.Size = new System.Drawing.Size(668, 322);
            this.panelTwo.TabIndex = 4;
            // 
            // comboBoxEditDB
            // 
            this.comboBoxEditDB.Location = new System.Drawing.Point(59, 290);
            this.comboBoxEditDB.Name = "comboBoxEditDB";
            this.comboBoxEditDB.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxEditDB.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.comboBoxEditDB.Size = new System.Drawing.Size(165, 21);
            this.comboBoxEditDB.TabIndex = 6;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(10, 293);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(43, 14);
            this.label2.TabIndex = 5;
            this.label2.Text = "库名：";
            // 
            // labelAdd
            // 
            this.labelAdd.AutoSize = true;
            this.labelAdd.Location = new System.Drawing.Point(230, 293);
            this.labelAdd.Name = "labelAdd";
            this.labelAdd.Size = new System.Drawing.Size(79, 14);
            this.labelAdd.TabIndex = 6;
            this.labelAdd.Text = "点击添加小区";
            this.labelAdd.Click += new System.EventHandler(this.labelAdd_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(454, 293);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(43, 14);
            this.label1.TabIndex = 6;
            this.label1.Text = "搜索：";
            // 
            // textBoxCellName
            // 
            this.textBoxCellName.Location = new System.Drawing.Point(501, 289);
            this.textBoxCellName.Name = "textBoxCellName";
            this.textBoxCellName.Size = new System.Drawing.Size(162, 22);
            this.textBoxCellName.TabIndex = 5;
            this.textBoxCellName.TextChanged += new System.EventHandler(this.textBoxCellName_TextChanged);
            // 
            // checkBoxIncludeIndoor
            // 
            this.checkBoxIncludeIndoor.AutoSize = true;
            this.checkBoxIncludeIndoor.Location = new System.Drawing.Point(319, 291);
            this.checkBoxIncludeIndoor.Name = "checkBoxIncludeIndoor";
            this.checkBoxIncludeIndoor.Size = new System.Drawing.Size(74, 18);
            this.checkBoxIncludeIndoor.TabIndex = 4;
            this.checkBoxIncludeIndoor.Text = "包含室分";
            this.checkBoxIncludeIndoor.UseVisualStyleBackColor = true;
            this.checkBoxIncludeIndoor.CheckedChanged += new System.EventHandler(this.checkBoxIncludeIndoor_CheckedChanged);
            // 
            // checkBoxAll
            // 
            this.checkBoxAll.AutoSize = true;
            this.checkBoxAll.Location = new System.Drawing.Point(398, 291);
            this.checkBoxAll.Name = "checkBoxAll";
            this.checkBoxAll.Size = new System.Drawing.Size(50, 18);
            this.checkBoxAll.TabIndex = 4;
            this.checkBoxAll.Text = "全选";
            this.checkBoxAll.UseVisualStyleBackColor = true;
            this.checkBoxAll.CheckedChanged += new System.EventHandler(this.checkBoxAll_CheckedChanged);
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.listViewCell);
            this.groupControl3.Location = new System.Drawing.Point(3, 3);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(662, 279);
            this.groupControl3.TabIndex = 0;
            this.groupControl3.Text = "区域内小区";
            // 
            // listViewCell
            // 
            this.listViewCell.CheckBoxes = true;
            this.listViewCell.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewCell.Location = new System.Drawing.Point(2, 23);
            this.listViewCell.Name = "listViewCell";
            this.listViewCell.Size = new System.Drawing.Size(658, 254);
            this.listViewCell.TabIndex = 0;
            this.listViewCell.UseCompatibleStateImageBehavior = false;
            this.listViewCell.View = System.Windows.Forms.View.List;
            this.listViewCell.ItemChecked += new System.Windows.Forms.ItemCheckedEventHandler(this.listViewCell_ItemChecked);
            // 
            // panelThree
            // 
            this.panelThree.Controls.Add(this.groupControl6);
            this.panelThree.Controls.Add(this.groupControl5);
            this.panelThree.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelThree.Location = new System.Drawing.Point(0, 0);
            this.panelThree.Name = "panelThree";
            this.panelThree.Size = new System.Drawing.Size(668, 322);
            this.panelThree.TabIndex = 0;
            // 
            // groupControl6
            // 
            this.groupControl6.Controls.Add(this.listBoxTime);
            this.groupControl6.Location = new System.Drawing.Point(3, 76);
            this.groupControl6.Name = "groupControl6";
            this.groupControl6.Size = new System.Drawing.Size(659, 243);
            this.groupControl6.TabIndex = 4;
            this.groupControl6.Text = "优化时间";
            // 
            // listBoxTime
            // 
            this.listBoxTime.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxTime.FormattingEnabled = true;
            this.listBoxTime.ItemHeight = 14;
            this.listBoxTime.Location = new System.Drawing.Point(2, 23);
            this.listBoxTime.Name = "listBoxTime";
            this.listBoxTime.Size = new System.Drawing.Size(655, 214);
            this.listBoxTime.TabIndex = 27;
            // 
            // groupControl5
            // 
            this.groupControl5.Controls.Add(this.radioButtonBg);
            this.groupControl5.Controls.Add(this.radioButtonCmd);
            this.groupControl5.Location = new System.Drawing.Point(3, 3);
            this.groupControl5.Name = "groupControl5";
            this.groupControl5.Size = new System.Drawing.Size(659, 67);
            this.groupControl5.TabIndex = 3;
            this.groupControl5.Text = "优化方式";
            // 
            // radioButtonBg
            // 
            this.radioButtonBg.AutoSize = true;
            this.radioButtonBg.Location = new System.Drawing.Point(383, 32);
            this.radioButtonBg.Name = "radioButtonBg";
            this.radioButtonBg.Size = new System.Drawing.Size(169, 18);
            this.radioButtonBg.TabIndex = 0;
            this.radioButtonBg.TabStop = true;
            this.radioButtonBg.Text = "后台优化（直取优化结果）";
            this.radioButtonBg.UseVisualStyleBackColor = true;
            this.radioButtonBg.CheckedChanged += new System.EventHandler(this.radioButtonBg_CheckedChanged);
            // 
            // radioButtonCmd
            // 
            this.radioButtonCmd.AutoSize = true;
            this.radioButtonCmd.Location = new System.Drawing.Point(60, 32);
            this.radioButtonCmd.Name = "radioButtonCmd";
            this.radioButtonCmd.Size = new System.Drawing.Size(199, 18);
            this.radioButtonCmd.TabIndex = 0;
            this.radioButtonCmd.TabStop = true;
            this.radioButtonCmd.Text = "即时优化（发命令启动PCI优化）";
            this.radioButtonCmd.UseVisualStyleBackColor = true;
            // 
            // panelFour
            // 
            this.panelFour.Controls.Add(this.groupControl4);
            this.panelFour.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelFour.Location = new System.Drawing.Point(0, 0);
            this.panelFour.Name = "panelFour";
            this.panelFour.Size = new System.Drawing.Size(668, 322);
            this.panelFour.TabIndex = 7;
            // 
            // groupControl4
            // 
            this.groupControl4.Controls.Add(this.wyzProgressSSS);
            this.groupControl4.Controls.Add(this.wyzProgressPSS);
            this.groupControl4.Controls.Add(this.labelDesc);
            this.groupControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl4.Location = new System.Drawing.Point(0, 0);
            this.groupControl4.Name = "groupControl4";
            this.groupControl4.Size = new System.Drawing.Size(668, 322);
            this.groupControl4.TabIndex = 0;
            this.groupControl4.Text = "进度...";
            // 
            // wyzProgressSSS
            // 
            this.wyzProgressSSS.BackColor = System.Drawing.Color.White;
            this.wyzProgressSSS.Location = new System.Drawing.Point(5, 184);
            this.wyzProgressSSS.Name = "wyzProgressSSS";
            this.wyzProgressSSS.ProgressMaximum = 100;
            this.wyzProgressSSS.ProgressMinimum = 0;
            this.wyzProgressSSS.RegionColor = System.Drawing.Color.Green;
            this.wyzProgressSSS.Size = new System.Drawing.Size(660, 133);
            this.wyzProgressSSS.TabIndex = 2;
            this.wyzProgressSSS.Text = "wyzProgress1";
            // 
            // wyzProgressPSS
            // 
            this.wyzProgressPSS.BackColor = System.Drawing.Color.White;
            this.wyzProgressPSS.Location = new System.Drawing.Point(5, 48);
            this.wyzProgressPSS.Name = "wyzProgressPSS";
            this.wyzProgressPSS.ProgressMaximum = 100;
            this.wyzProgressPSS.ProgressMinimum = 0;
            this.wyzProgressPSS.RegionColor = System.Drawing.Color.Green;
            this.wyzProgressPSS.Size = new System.Drawing.Size(660, 133);
            this.wyzProgressPSS.TabIndex = 2;
            this.wyzProgressPSS.Text = "wyzProgress1";
            // 
            // labelDesc
            // 
            this.labelDesc.AutoSize = true;
            this.labelDesc.Location = new System.Drawing.Point(8, 30);
            this.labelDesc.Name = "labelDesc";
            this.labelDesc.Size = new System.Drawing.Size(67, 14);
            this.labelDesc.TabIndex = 1;
            this.labelDesc.Text = "当前进度：";
            // 
            // simpleButtonUp
            // 
            this.simpleButtonUp.Location = new System.Drawing.Point(504, 330);
            this.simpleButtonUp.Name = "simpleButtonUp";
            this.simpleButtonUp.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonUp.TabIndex = 1;
            this.simpleButtonUp.Text = "上一步";
            this.simpleButtonUp.Click += new System.EventHandler(this.simpleButtonUp_Click);
            // 
            // simpleButtonDown
            // 
            this.simpleButtonDown.Location = new System.Drawing.Point(585, 330);
            this.simpleButtonDown.Name = "simpleButtonDown";
            this.simpleButtonDown.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonDown.TabIndex = 1;
            this.simpleButtonDown.Text = "下一步";
            this.simpleButtonDown.Click += new System.EventHandler(this.simpleButtonDown_Click);
            // 
            // bgWorkerOptimize
            // 
            this.bgWorkerOptimize.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgWorkerOptimize_DoWork);
            this.bgWorkerOptimize.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bgWorkerOptimize_RunWorkerCompleted);
            this.bgWorkerOptimize.ProgressChanged += new System.ComponentModel.ProgressChangedEventHandler(this.bgWorkerOptimize_ProgressChanged);
            // 
            // LtePCIOptimizeNewSettingDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(672, 362);
            this.Controls.Add(this.simpleButtonDown);
            this.Controls.Add(this.simpleButtonUp);
            this.Controls.Add(this.panelMain);
            this.MaximizeBox = false;
            this.MaximumSize = new System.Drawing.Size(688, 401);
            this.MinimumSize = new System.Drawing.Size(688, 401);
            this.Name = "LtePCIOptimizeNewSettingDlg";
            this.Text = "PCI优化设置";
            this.panelMain.ResumeLayout(false);
            this.panelOne.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditThreadNum.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditVarRate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCrossRate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPopularSize.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditIteration.Properties)).EndInit();
            this.panelTwo.ResumeLayout(false);
            this.panelTwo.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEditDB.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.panelThree.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl6)).EndInit();
            this.groupControl6.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl5)).EndInit();
            this.groupControl5.ResumeLayout(false);
            this.groupControl5.PerformLayout();
            this.panelFour.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).EndInit();
            this.groupControl4.ResumeLayout(false);
            this.groupControl4.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panelMain;
        private DevExpress.XtraEditors.SimpleButton simpleButtonUp;
        private DevExpress.XtraEditors.SimpleButton simpleButtonDown;
        private System.Windows.Forms.Panel panelOne;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit spinEditThreadNum;
        private DevExpress.XtraEditors.SpinEdit spinEditVarRate;
        private DevExpress.XtraEditors.SpinEdit spinEditCrossRate;
        private DevExpress.XtraEditors.SpinEdit spinEditPopularSize;
        private DevExpress.XtraEditors.SpinEdit spinEditIteration;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private System.Windows.Forms.Panel panelTwo;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private System.Windows.Forms.ListView listViewCell;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox textBoxCellName;
        private System.Windows.Forms.CheckBox checkBoxAll;
        private System.Windows.Forms.Panel panelFour;
        private DevExpress.XtraEditors.GroupControl groupControl4;
        private System.Windows.Forms.Label labelDesc;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxEditDB;
        private System.Windows.Forms.Label label2;
        private System.ComponentModel.BackgroundWorker bgWorkerOptimize;
        private WyzProgress wyzProgressPSS;
        private System.Windows.Forms.Label labelAdd;
        private System.Windows.Forms.CheckBox checkBoxIncludeIndoor;
        private WyzProgress wyzProgressSSS;
        private System.Windows.Forms.Panel panelThree;
        private DevExpress.XtraEditors.GroupControl groupControl6;
        private DevExpress.XtraEditors.GroupControl groupControl5;
        private System.Windows.Forms.RadioButton radioButtonBg;
        private System.Windows.Forms.RadioButton radioButtonCmd;
        private System.Windows.Forms.ListBox listBoxTime;





    }
}