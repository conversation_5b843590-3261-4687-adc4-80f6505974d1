﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using DevExpress.XtraCharts;
using System.IO;
using MasterCom.RAMS.Util;
using Excel = Microsoft.Office.Interop.Excel;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using System.Collections;

namespace MasterCom.RAMS.Func.PopShow
{
    public partial class KPIInfoPanel_ng_total : UserControl, PopShowPanelInterface
    {
        //第一命令字：config
        public const byte REQTYPE_CONFIG_KPI_TABLE = 0x2b;  //REQUEST
        public const byte RESTYPE_CONFIG_KPI_TABLE = 0x2b;

        //第一命令字：查询    启动预读
        public const byte REQTYPE_TABLE_DATA_INFO = 0x83; //REQUEST 
        public const byte RESTYPE_TABLE_DATA_INFO = 0x83; 
        private MainModel MainModel;
        public Dictionary<string, Object> colorRangeDic { get; set; }
        public Dictionary<string, AlarmCfgItem> alarmCfgDic { get; set; }

        private Stack<PopKPIQueryCondition> backStacks = new Stack<PopKPIQueryCondition>(); //后退栈
        private Stack<PopKPIQueryCondition> forwardStacks = new Stack<PopKPIQueryCondition>();//前进栈
        
        string curTableType;//当前选择的表业务类型
        Dictionary<string, List<KPIResultInfo>> tableTypeAllKPIResultInfoList = new Dictionary<string, List<KPIResultInfo>>();//相同业务类型表的所有原始记录

        public static KPIInfoPanel_ng_total theKPIInfoPanel { get; set; }

        private ToolStripDropDown toolStripDropDownCity = new ToolStripDropDown();
        private List<int> selectedProjects = new List<int>();//已选择项目

        private string curShowType = "";//当前时间方式
        private List<string> selectedDateStrs = new List<string>();//按天、按周、按月选择的日期列表

        private CitySelectionPanel cityPanel = null;

        AxisRange defaultRangeX;
        AxisRange defaultRangeY;
        public KPIInfoPanel_ng_total()
        {
            InitializeComponent();
#if PopShow_KPI_Color
            btnColor.Visible = true;
#endif
            initShowInfo();
            theKPIInfoPanel = this;
            if (chartControl.Diagram != null)
            {
                defaultRangeX = (AxisRange)((XYDiagram)chartControl.Diagram).AxisX.Range.Clone();
                defaultRangeY = (AxisRange)((XYDiagram)chartControl.Diagram).AxisY.Range.Clone();
            }
            toolStripDropDownCity.Closed += new ToolStripDropDownClosedEventHandler(toolStripDropDownCity_Closed);
            initCityOrder();

            curRetDataDic = new Dictionary<string, KPIPopTable>();
        }

        private void toolStripDropDownCity_Closed(object sender, ToolStripDropDownClosedEventArgs e)
        {
            projectChanged();
        }

        private void projectChanged()
        {
            this.lbStatusDesc.Text = getStatusDesc();
            updateToolTip();
        }

        private string getStatusDesc()
        {
            StringBuilder desc = new StringBuilder();
            desc.Append("已选项目:" + cbxProject.Text + ";\r\n");
            desc.Append("已选地市:");
            foreach (string selectedCityName in (IList)this.toolStripDropDownCity.Tag)
            {
                desc.Append(selectedCityName + ",");
            }
            desc.Append(";");
            return desc.ToString();
        }

        BlackBlockInfoPanel_ng blackBlockPanel_ng = null;
        public void setBlackBlockInfoPanel(BlackBlockInfoPanel_ng blackBlockPanel_ng)
        {
            this.blackBlockPanel_ng = blackBlockPanel_ng;
        }

        int minTime = 0x7fffffff;  //查询结果中的时间最小值
        int maxTime = 0;           //查询结果中的时间最大值

        private void setTime(int time)
        {
            if (time > maxTime)
            {
                maxTime = time;
            }

            if (time < minTime)
            {
                minTime = time;
            }
        }

        private void resetTime()
        {
            minTime = 0x7fffffff;
            maxTime = 0;
        }

        private void initShowInfo()
        {
            curTableType = "gsmvoice";

            cbxShowType.Items.Add("前一周");
            cbxShowType.Items.Add("前一月");
            cbxShowType.Items.Add("前一天");

            cbxShowType.Items.Add("本周");
            cbxShowType.Items.Add("本月");

            cbxShowType.Items.Add("按周");
            cbxShowType.Items.Add("按月");
            cbxShowType.Items.Add("按天");
            cbxShowType.SelectedIndex = 0;

            cbxTableType.Items.Add("GSM语音");
            cbxTableType.Items.Add("GSM数据");
            cbxTableType.Items.Add("TD语音");
            cbxTableType.Items.Add("TD数据");
            cbxTableType.Items.Add("TD空闲");
            cbxTableType.SelectedIndex = 0;  //选择的业务相关表，则报表只统计此类业务

            btnBack.Enabled = false;
            btnForward.Enabled = false;
            splitMain.Panel2Collapsed = true;
        }

        #region PopShowPanelInterface 成员

        public void RunQuery(BackgroundWorker worker, TaskInfo task)
        {
            Dictionary<string, KPIPopTable> entryHeaderDic = queryPopEntry(worker); //从popkpi获取每个表信息，key:表名 value.entryList 对应表的列信息
            tableTypeAllKPIResultInfoList = new Dictionary<string, List<KPIResultInfo>>();
            tableTypeAllKPIResultInfoList.Add("gsmvoice",new List<KPIResultInfo>());
             tableTypeAllKPIResultInfoList.Add("gsmdata",new List<KPIResultInfo>());
             tableTypeAllKPIResultInfoList.Add("tdvoice",new List<KPIResultInfo>());
             tableTypeAllKPIResultInfoList.Add("tddata",new List<KPIResultInfo>());
             tableTypeAllKPIResultInfoList.Add("tdidle",new List<KPIResultInfo>());
            foreach (string tbkey in entryHeaderDic.Keys)
            {
                KPIPopTable hdUnit = entryHeaderDic[tbkey];
                hdUnit.Sort();
                hdUnit.initFinderDic();
                List<KPIResultInfo> resultList = queryResultFromHeader(worker, hdUnit, MainModel.User.DBID);
                if (tbkey.Contains("gsmvoice"))
                {
                    tableTypeAllKPIResultInfoList["gsmvoice"].AddRange(resultList);
                }
                else if (tbkey.Contains("gsmdata"))
                {
                    tableTypeAllKPIResultInfoList["gsmdata"].AddRange(resultList);
                }
                else if (tbkey.Contains("tdvoice"))
                {
                    tableTypeAllKPIResultInfoList["tdvoice"].AddRange(resultList);
                }
                else if (tbkey.Contains("tddata"))
                {
                    tableTypeAllKPIResultInfoList["tddata"].AddRange(resultList);
                }
                else if (tbkey.Contains("tdidle"))
                {
                    tableTypeAllKPIResultInfoList["tdidle"].AddRange(resultList);
                }
                hdUnit.cityDataResult = buildCityStruct(resultList);
            }
            task.retResultInfo = entryHeaderDic;
        }
        
        private Dictionary<string, List<KPIResultInfo>> buildCityStruct(List<KPIResultInfo> resultList)
        {
            Dictionary<string, List<KPIResultInfo>> cityDic = new Dictionary<string, List<KPIResultInfo>>();
            foreach(KPIResultInfo info in resultList)
            {
                string strcity = DistrictManager.GetInstance().getDistrictName(info.dbid);
                List<KPIResultInfo> list = null;
                if(!cityDic.TryGetValue(strcity,out list))
                {
                    list = new List<KPIResultInfo>();
                    cityDic[strcity] = list;
                }
                list.Add(info);
            }
            return cityDic;
        }

        private List<KPIResultInfo> queryResultFromHeader(BackgroundWorker worker, KPIPopTable hdUnit,int dbid)
        {
            List<KPIResultInfo> retList = new List<KPIResultInfo>();
            ClientProxy clientProxy = new ClientProxy();

            string username = MainModel.User.LoginName;
            string password = MainModel.User.Password;
            int districtID = MainModel.DistrictID;
#if Guangdong
            //if (districtID == 14 || districtID == 15 || districtID == 22)
            {
                username = MainModel.MainDbUser.LoginName;
                password = MainModel.MainDbUser.Password;
                districtID = 2;
            }
#endif
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, username, password, districtID) != ConnectResult.Success)
            {
                worker.ReportProgress(99, "连接服务器端出错！");
                return retList;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.InfoQuery;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_TABLE_DATA_INFO;
                package.Content.PrepareAddParam();
                package.Content.AddParam(dbid);

                package.Content.AddParam(hdUnit.tablename);
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_TABLE_DATA_INFO)
                    {
                        package.Content.PrepareGetParam();
                        KPIResultInfo retItem = hdUnit.ReadResultItemFrom(package.Content);
                        retList.Add(retItem);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                }
                return retList;
            }

            finally
            {
                clientProxy.Close();
            }

        }
        private Dictionary<string, KPIPopTable> queryPopEntry(BackgroundWorker worker)
        {
#if PopShow_KPI_Color
            DiySqlPopKpiColor kpiColorQeruyTask = new DiySqlPopKpiColor(MainModel);
            kpiColorQeruyTask.Query();
            colorRangeDic = kpiColorQeruyTask.colorRangeDic;

            DiySqlPopKpiAlarmCfg kpiAlarmCfgrQeruyTask = new DiySqlPopKpiAlarmCfg(MainModel);
            kpiAlarmCfgrQeruyTask.Query();
            alarmCfgDic = kpiAlarmCfgrQeruyTask.alarmCfgDic;
#endif           

            Dictionary<string, KPIPopTable> entryDicList = new Dictionary<string, KPIPopTable>();
            ClientProxy clientProxy = new ClientProxy();
            string username = MainModel.User.LoginName;
            string password = MainModel.User.Password;
            int dbid = MainModel.DistrictID;
#if Guangdong
            //if (dbid == 14 || dbid == 15 || dbid == 22)
            {
                username = MainModel.MainDbUser.LoginName;
                password = MainModel.MainDbUser.Password;
                dbid = 2;
            }
#endif

            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, username, password, dbid) != ConnectResult.Success)
            {
                worker.ReportProgress(99,"连接服务器端出错！");
                return entryDicList;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.CellConfigManage;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = REQTYPE_CONFIG_KPI_TABLE;
                package.Content.PrepareAddParam();
                clientProxy.Send();
                while (true)
                {
                    clientProxy.Recieve();
                    if (package.Content.Type == RESTYPE_CONFIG_KPI_TABLE)
                    {
                        package.Content.PrepareGetParam();
                        PopKPIEntryItem entry = new PopKPIEntryItem();
                        entry.Fill(package.Content);
                        addEntryList(entryDicList, entry);
                    }
                    else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                    {
                        break;
                    }
                }
                return entryDicList;
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void addEntryList(Dictionary<string, KPIPopTable> entryDicList, PopKPIEntryItem entry)
        {
            if (entry.strtablename == "tb_popblackblock"
                || entry.strtablename == "tb_pop_esinsight"
                || entry.strtablename == "tb_pop_compbench_status"
                || entry.strtablename == "tb_pop_compbench_unit"
                || entry.strtablename.Contains("tb_popkpi_wxwljc") /*|| entry.strtablename.IndexOf("_agent_")!=-1*/)//特殊的供其它程序使用的，不在KPI显示处显示
            {
                //
            }
            else
            {
                KPIPopTable headerUnit = null;
                if (!entryDicList.TryGetValue(entry.strtablename, out headerUnit))
                {
                    headerUnit = new KPIPopTable();
                    headerUnit.tablename = entry.strtablename;
                    entryDicList[headerUnit.tablename] = headerUnit;
                }
#if PopShow_KPI_Color
                string key = entry.strtablename + entry.strcolname;
                if (kpiColorQeruyTask.colorRangeDic.ContainsKey(key))
                {
                    entry.colorLst = kpiColorQeruyTask.colorRangeDic[key] as List<DTParameterRangeColor>;
                }
#endif
                headerUnit.entryList.Add(entry);
            }
        }
        #endregion

        #region PopShowPanelInterface 成员

        public Dictionary<string, KPIPopTable> curRetDataDic { get; set; }
        public void FireFreshShowData(TaskInfo task)
        {
            if (!(task.retResultInfo is Dictionary<string, KPIPopTable>))
            {
                curRetDataDic.Clear();
            }
            else
            {
                curRetDataDic = task.retResultInfo as Dictionary<string, KPIPopTable>;
            }

            fillProject();
            refreshShowReport(true);
            string showType = cbxShowType.SelectedItem.ToString();

            selectedDateStrs = new List<string>();
            foreach (CheckedListBoxItem item in checkedCbxDate.Properties.Items)
            { 
                if(item.CheckState==CheckState.Checked)
                {
                    selectedDateStrs.Add(item.Value.ToString());
                }
            }

            this.blackBlockPanel_ng.setInitCondition(minTime, maxTime, selectedProjects, this.toolStripDropDownCity.Tag as List<string>, showType, selectedDateStrs);
            resetTime();
            KPIInfoPanelHelper.FreshShowChart_ng_total(2, new FreshShowChartControl(chartControl, dataGridView, cbxShowType, null), defaultRangeX, defaultRangeY);
        }

        /// <summary>
        /// 按当前选择的表类型加载相应的项目类型
        /// </summary>
        private void fillProject()
        {
            if (cbxProject.Properties.Items.Count > 0)
                cbxProject.Properties.Items.Clear();
            foreach (KPIPopTable popTable in curRetDataDic.Values)
            {
                if (popTable.tablename.Contains("_agent_") || popTable.ToString().Contains("汇总"))
                {
                    continue;
                }
                if (popTable.tablename.Contains(curTableType))
                {
                    cbxProject.Properties.Items.Add(popTable);
                }
            }
            if (cbxProject.Properties.Items.Count > 0)
            {
                cbxProject.Text = cbxProject.Properties.Items[0].Value.ToString();
                foreach (CheckedListBoxItem item in cbxProject.Properties.Items)
                {
                    if (item.Value.ToString().Contains("巡检") || item.Value.ToString().Contains("ATU"))
                    {
                        item.CheckState = CheckState.Checked;
                    }
                    else
                    {
                        item.CheckState = CheckState.Unchecked;
                    }
                }
            }
        }

        #endregion

        #region PopShowPanelInterface 成员


        public void SetMainModal(MasterCom.RAMS.Model.MainModel mm, WelcomForm welcomform)
        {
            this.MainModel = mm;
        }

        #endregion

        private void updateToolTip()////
        {
            StringBuilder projectTipDesc = new StringBuilder();
            foreach (CheckedListBoxItem item in cbxProject.Properties.Items)
            {
                if (item.CheckState == CheckState.Checked)
                {
                    projectTipDesc.Append(item.Value.ToString() + ";");
                }
            }
            cbxProject.ToolTip = projectTipDesc.ToString();

            if (this.toolStripDropDownCity.Tag != null)
            {
                setBtnShowCityNames();
            }
            checkedCbxDate.ToolTip = checkedCbxDate.Text;
        }

        private void setBtnShowCityNames()
        {
            if (MainModel.User.DBID == -1) //省用户显示
            {
                if (((IList)this.toolStripDropDownCity.Tag).Count > 0)
                {
                    if (((IList)this.toolStripDropDownCity.Tag).Count == 1)
                    {
                        btnShowCityNames.Text = "请选择地市↓(已选择 " + ((List<string>)this.toolStripDropDownCity.Tag)[0] + " )";
                    }
                    else
                    {
                        btnShowCityNames.Text = "请选择地市↓(已选择" + ((List<string>)this.toolStripDropDownCity.Tag).Count.ToString() + "个地市)";
                    }
                }
                else
                {
                    btnShowCityNames.Text = "请选择地市↓";
                }
            }
            else//地市用户，只能显示自己地市的，不能选择
            {
                string cityName = DistrictManager.GetInstance().getDistrictName(MainModel.User.DBID);
                btnShowCityNames.Text = "当前地市:" + cityName;
                btnShowCityNames.Enabled = false;
            }
        }

        private void refreshShowReport(bool isClickFresh)
        {
            dataGridView.Columns.Clear();
            List<string> kpiPopTableNames = new List<string>(); //记录已选报表列表
            foreach (CheckedListBoxItem item in cbxProject.Properties.Items)
            {
                if (item.CheckState == CheckState.Checked)
                {
                    kpiPopTableNames.Add(item.ToString());
                }
            }
            string selShowType = cbxShowType.SelectedItem as string;  //记录已选方式
            bool isByProject = cbxByProject.Checked; //按报表
            bool isByCity = cbxByCity.Checked;  //按地市
            List<string> selectedCityNames = this.toolStripDropDownCity.Tag as List<string>;

            if (isClickFresh)
            {
                PopKPIQueryCondition queryCondition = new PopKPIQueryCondition();
                foreach (string selectedCityName in selectedCityNames)
                {
                    queryCondition.AddCheckedCityName(selectedCityName);
                }
                foreach (CheckedListBoxItem item in cbxProject.Properties.Items)
                {
                    if (item.CheckState == CheckState.Checked)
                    {
                        queryCondition.AddCheckedProject(item.Value.ToString());
                    }
                }
                foreach (CheckedListBoxItem item in checkedCbxDate.Properties.Items)
                {
                    queryCondition.AddDate(item.Value.ToString(), item.CheckState == CheckState.Checked);
                }
                queryCondition.SetIsByCity(isByCity);
                queryCondition.SetIsByProject(isByProject);
                queryCondition.SetShowType(selShowType);

                backStacks.Push(queryCondition);
                btnBack.Enabled = backStacks.Count > 1;
                btnForward.Enabled = forwardStacks.Count > 0;
            }
            FinalShowResult showRet = parseShowFromTable(selShowType, isByProject, isByCity, selectedCityNames);
            if (showRet != null)
            {
                showInGrid(showRet);
            }
        }

        private FinalShowResult parseShowFromTable(string selShowType, bool isByProject, bool isByCity,List<string> selectedCityNames)
        {
            FinalShowResult sRet = new FinalShowResult();
            if (selShowType == "按天")
            {
                sRet = prepareShowByDay(isByProject, isByCity,selectedCityNames);
            }
            else if (selShowType == "按月")
            {
                sRet = prepareShowByMonth(isByProject, isByCity, selectedCityNames);
            }
            else if (selShowType == "按周")
            {
                sRet = prepareShowByWeek(isByProject, isByCity, selectedCityNames);
            }
            else if (selShowType == "本月")
            {
                sRet = prepareShowByThisMonth(isByProject, isByCity, selectedCityNames);
            }
            else if (selShowType == "本周")
            {
                sRet = prepareShowByThisWeek(isByProject, isByCity, selectedCityNames);
            }
            else if (selShowType == "前一周")
            {
                sRet = prepareShowByBackOneWeek(isByProject, isByCity, selectedCityNames);
            }
            else if (selShowType == "前一月")
            {
                sRet = prepareShowByOneBackMonth(isByProject, isByCity, selectedCityNames);
            }
            else if (selShowType == "前一天")
            {
                sRet = prepareShowByYesterday(isByProject, isByCity, selectedCityNames);
            }
            return sRet;
        }

        private void initCityOrder()
        {
            if (cityPanel == null)
            {
                cityPanel = new CitySelectionPanel(this.toolStripDropDownCity);
            }
            toolStripDropDownCity.Items.Clear();
            toolStripDropDownCity.Items.Add(new ToolStripControlHost(cityPanel));
        }

        #region prepareShow
        class PrepareShowInfo
        {
            public bool IsByProject { get; set; }
            public bool IsByCity { get; set; }
            public List<string> SelectedCityNames { get; set; }
            public List<string> KpiPopTableNames { get; set; }
            public KPIPopTable KpiPopTable { get; set; }

            public PrepareShowInfo(bool isByProject, bool isByCity, List<string> selectedCityNames, List<string> kpiPopTableNames, KPIPopTable kpiPopTable)
            {
                IsByProject = isByProject;
                IsByCity = isByCity;
                SelectedCityNames = selectedCityNames;
                KpiPopTableNames = kpiPopTableNames;
                KpiPopTable = kpiPopTable;
            }
        }

        delegate string FuncDateKey(KPIResultInfo retInfo);
        delegate void FuncAddDate(KPIResultInfo ginfo, List<object> objList);
        delegate Dictionary<string, KPIResultInfo> FuncRetDic(PrepareShowInfo info, FuncDateKey funcKey);

        private FinalShowResult prepareShow(PrepareShowInfo info, FuncDateKey funcKey, FuncAddDate funcAdd, FuncRetDic funcRet)
        {
            FinalShowResult sRet = new FinalShowResult();
            sRet.columnNames.Add("时间");
            sRet.columnNames.Add("名称");
            List<string> kpiPopTableNames = new List<string>();

            foreach (CheckedListBoxItem item in cbxProject.Properties.Items)
            {
                if (item.CheckState == CheckState.Checked && item.Value is KPIPopTable)
                {
                    kpiPopTableNames.Add(((KPIPopTable)item.Value).tablename);
                }
            }
            if (kpiPopTableNames.Count == 0)
            {
                return null;
            }
            KPIPopTable kpiPopTable = curRetDataDic[kpiPopTableNames[0]];
            for (int i = 4; i < kpiPopTable.entryList.Count; i++)
            {
                PopKPIEntryItem entry = kpiPopTable.entryList[i];
                if (entry.strcolname.IndexOf("_base") == -1)
                {
                    sRet.columnNames.Add(entry.strcoldesc);
                }
            }

            info.KpiPopTableNames = kpiPopTableNames;
            info.KpiPopTable = kpiPopTable;

            Dictionary<string, KPIResultInfo> retDic = funcRet(info, funcKey);

            dealRetDic(info.SelectedCityNames, funcAdd, sRet, info.KpiPopTableNames, retDic);
            return sRet;
        }

        private Dictionary<string, KPIResultInfo> getRetDic(PrepareShowInfo info, FuncDateKey funcKey)
        {
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            foreach (KPIPopTable tableData in curRetDataDic.Values)
            {
                if (!info.KpiPopTableNames.Contains(tableData.tablename))
                {
                    continue;
                }
                tableData.reloadDataResultByLevel((MainModel.User.DBID == -1), "", "");
                foreach (KPIResultInfo retInfo in tableData.dataResult)
                {
                    string dateKey = funcKey(retInfo);
                    setResultInfo(info, retDic, tableData, retInfo, dateKey);
                }
            }

            return retDic;
        }

        private void setResultInfo(PrepareShowInfo info, Dictionary<string, KPIResultInfo> retDic, KPIPopTable tableData, KPIResultInfo retInfo, string dateKey)
        {
            dateKey += ":";
            if (info.IsByProject)
            {
                dateKey += tableData.ToString();
            }
            dateKey += ":";
            if (info.IsByCity)
            {
                dateKey += CityOrder.getInstance().getCityPriorityID(retInfo.dbid).ToString("D2");//地市优先级值
            }
            string cityName = DistrictManager.GetInstance().getDistrictName(retInfo.dbid);
            if (!info.SelectedCityNames.Contains(cityName))
            {
                return;
            }
            KPIResultInfo ginfo = null;
            if (!retDic.TryGetValue(dateKey, out ginfo))
            {
                retDic[dateKey] = retInfo.copyInstance();
                retDic[dateKey].strname = dateKey;//时间，项目,地市优先级
            }
            else
            {
                ginfo.Gather(retInfo, info.KpiPopTable);
            }
        }

        private void dealRetDic(List<string> selectedCityNames, FuncAddDate funcAdd, FinalShowResult sRet, List<string> kpiPopTableNames, Dictionary<string, KPIResultInfo> retDic)
        {
            List<string> keys = new List<string>();
            foreach (string key in retDic.Keys)
            {
                keys.Add(key);
            }
            keys.Sort();
            for (int k = keys.Count - 1; k >= 0; k--)
            {
                KPIResultInfo ginfo = retDic[keys[k]];
                List<object> objList = new List<object>();
                funcAdd(ginfo, objList);

                string[] strs = ginfo.strname.Split(':');
                string strShowName = getStrShowName(selectedCityNames, kpiPopTableNames, strs);
                objList.Add(strShowName);
                for (int i = 0; i < ginfo.valueList.Count && i < sRet.columnNames.Count - 2; i++)
                {
                    object v = ginfo.valueList[i];
                    objList.Add(v);
                }
                sRet.dataRows.Add(objList);
            }
        }

        private string getStrShowName(List<string> selectedCityNames, List<string> kpiPopTableNames, string[] strs)
        {
            string strShowName = "";
            if (strs.Length != 3)
            {
                return strShowName;
            }
            if (strs[1].Length > 0)
            {
                strShowName += strs[1];
            }
            int priorityID;
            int.TryParse(strs[2], out priorityID);
            if (priorityID != -999)
            {
                int dbid = CityOrder.getInstance().getCityidByPriority(priorityID);
                if (dbid != -999)
                {
                    string cityName = DistrictManager.GetInstance().getDistrictName(dbid);
                    if (strShowName != "")
                    {
                        strShowName += "_";
                    }
                    strShowName += cityName;
                }
            }

            if (strShowName == "")
            {
                if (kpiPopTableNames.Count == 1)
                {
                    strShowName += curRetDataDic[kpiPopTableNames[0]].ToString();
                }
                if (selectedCityNames.Count == 1)
                {
                    strShowName += selectedCityNames[0];
                }
                if (strShowName == "")
                {
                    strShowName = "项目汇总";
                }
            }
            return strShowName;
        }
        #endregion

        #region prepareShowByDay
        private FinalShowResult prepareShowByDay(bool isByProject, bool isByCity, List<string> selectedCityNames)
        {
            return prepareShow(new PrepareShowInfo(isByProject, isByCity, selectedCityNames, null, null), getDateKeyByDay, addDateByDay, getRetDic);
        }

        private string getDateKeyByDay(KPIResultInfo retInfo)
        {
            setTime(retInfo.stime);
            DateTime stimeDate = JavaDate.GetDateTimeFromMilliseconds(retInfo.stime * 1000L);
            string dateKey = stimeDate.ToString("yyyy.MM.dd");
            return dateKey;
        }

        private void addDateByDay(KPIResultInfo ginfo, List<object> objList)
        {
            DateTime stimeDate = JavaDate.GetDateTimeFromMilliseconds(ginfo.stime * 1000L);
            objList.Add(stimeDate.ToString("yyyy.MM.dd"));
        }
        #endregion

        #region prepareShowByWeek
        private FinalShowResult prepareShowByWeek(bool isByProject, bool isByCity, List<string> selectedCityNames)
        {
            return prepareShow(new PrepareShowInfo(isByProject, isByCity, selectedCityNames, null, null), getDateKeyByWeek, addDateByWeek, getRetDic);
        }

        private string getDateKeyByWeek(KPIResultInfo retInfo)
        {
            setTime(getWeekBeginTime(retInfo.stime));
            setTime(getWeekEndTime(retInfo.stime));
            string dateKey = getWeekStr(retInfo.stime);
            return dateKey;
        }

        private void addDateByWeek(KPIResultInfo ginfo, List<object> objList)
        {
            objList.Add(getWeekStr(ginfo.stime));
        }
        #endregion

        #region prepareShowByMonth
        private FinalShowResult prepareShowByMonth(bool isByProject, bool isByCity, List<string> selectedCityNames)
        {
            return prepareShow(new PrepareShowInfo(isByProject, isByCity, selectedCityNames, null, null), getDateKeyByMonth, addDateByMonth, getRetDic);
        }

        private string getDateKeyByMonth(KPIResultInfo retInfo)
        {
            setTime(getMonthBeginTime(retInfo.stime));
            setTime(getMonthEndTime(retInfo.stime));
            string dateKey = getMonthStr(retInfo.stime);
            return dateKey;
        }

        private void addDateByMonth(KPIResultInfo ginfo, List<object> objList)
        {
            objList.Add(getMonthStr(ginfo.stime));
        }
        #endregion

        #region prepareShowByThisWeek
        private FinalShowResult prepareShowByThisWeek(bool isByProject, bool isByCity, List<string> selectedCityNames)
        {
            return prepareShow(new PrepareShowInfo(isByProject, isByCity, selectedCityNames, null, null), getDateKeyByThisWeek, addDateByThisWeek, getRetDicByThisWeek);
        }

        private Dictionary<string, KPIResultInfo> getRetDicByThisWeek(PrepareShowInfo info, FuncDateKey funcKey)
        {
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            DateTime dtNow = DateTime.Now;
            int nowTime = (int)(JavaDate.GetMilliseconds(dtNow) / 1000L);
            int thisWeekTime = getWeekBeginTime(nowTime);

            setTime(thisWeekTime);
            setTime(getWeekEndTime(thisWeekTime));

            foreach (KPIPopTable tableData in curRetDataDic.Values)
            {
                if (!info.KpiPopTableNames.Contains(tableData.tablename))
                {
                    continue;
                }
                tableData.reloadDataResultByLevel((MainModel.User.DBID == -1), "", "");
                foreach (KPIResultInfo retInfo in tableData.dataResult)
                {
                    if (thisWeekTime == getWeekBeginTime(retInfo.stime))
                    {
                        string dateKey = funcKey(retInfo);
                        setResultInfo(info, retDic, tableData, retInfo, dateKey);
                    }
                }
            }

            minTime = thisWeekTime;
            maxTime = thisWeekTime + 6 * 24 * 3600;

            return retDic;
        }

        private string getDateKeyByThisWeek(KPIResultInfo retInfo)
        {
            string dateKey = getWeekStr(retInfo.stime);
            return dateKey;
        }

        private void addDateByThisWeek(KPIResultInfo ginfo, List<object> objList)
        {
            objList.Add(getWeekStr(ginfo.stime));
        }
        #endregion

        #region prepareShowByBackOneWeek
        //前一周
        private FinalShowResult prepareShowByBackOneWeek(bool isByProject, bool isByCity, List<string> selectedCityNames)
        {
            return prepareShow(new PrepareShowInfo(isByProject, isByCity, selectedCityNames, null, null), getDateKeyByThisWeek, addDateByThisWeek, getRetDicByOneWeek);
        }

        private Dictionary<string, KPIResultInfo> getRetDicByOneWeek(PrepareShowInfo info, FuncDateKey funcKey)
        {
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            DateTime dtNow = DateTime.Now;
            DateTime dtBackOneWeek = dtNow.AddDays(-7);
            int backOneWeekTime = (int)(JavaDate.GetMilliseconds(dtBackOneWeek) / 1000L);
            int backOneWeekBeginTime = getWeekBeginTime(backOneWeekTime);

            setTime(backOneWeekBeginTime);
            setTime(getWeekEndTime(backOneWeekBeginTime));
            bool isProvUser = (MainModel.User.DBID == -1);

            foreach (KPIPopTable tableData in curRetDataDic.Values)
            {
                if (!info.KpiPopTableNames.Contains(tableData.tablename))
                {
                    continue;
                }
                tableData.reloadDataResultByLevel(isProvUser, "", "");
                foreach (KPIResultInfo retInfo in tableData.dataResult)
                {
                    if (backOneWeekBeginTime == getWeekBeginTime(retInfo.stime))
                    {
                        string dateKey = funcKey(retInfo);
                        setResultInfo(info, retDic, tableData, retInfo, dateKey);
                    }
                }
            }

            return retDic;
        }
        #endregion

        #region prepareShowByThisMonth
        private FinalShowResult prepareShowByThisMonth(bool isByProject, bool isByCity, List<string> selectedCityNames)
        {
            return prepareShow(new PrepareShowInfo(isByProject, isByCity, selectedCityNames, null, null), getDateKeyByThisMonth, addDateByThisMonth, getRetDicByThisMonth);
        }

        private Dictionary<string, KPIResultInfo> getRetDicByThisMonth(PrepareShowInfo info, FuncDateKey funcKey)
        {
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            DateTime dtNow = DateTime.Now;
            DateTime dtThisMonthBeginTime = new DateTime(dtNow.Year, dtNow.Month, 1).ToLocalTime();
            int thisMonthTime = (int)(JavaDate.GetMilliseconds(dtThisMonthBeginTime) / 1000L);

            setTime(thisMonthTime);
            setTime(getMonthEndTime(thisMonthTime));

            foreach (KPIPopTable tableData in curRetDataDic.Values)
            {
                if (!info.KpiPopTableNames.Contains(tableData.tablename))
                {
                    continue;
                }
                tableData.reloadDataResultByLevel((MainModel.User.DBID == -1), "", "");
                foreach (KPIResultInfo retInfo in tableData.dataResult)
                {
                    if (thisMonthTime == getMonthBeginTime(retInfo.stime))
                    {
                        string dateKey = funcKey(retInfo);
                        setResultInfo(info, retDic, tableData, retInfo, dateKey);
                    }
                }
            }

            return retDic;
        }

        private string getDateKeyByThisMonth(KPIResultInfo retInfo)
        {
            string dateKey = getMonthStr(retInfo.stime);
            return dateKey;
        }

        private void addDateByThisMonth(KPIResultInfo ginfo, List<object> objList)
        {
            objList.Add(getMonthStr(ginfo.stime));
        }
        #endregion

        #region prepareShowByOneBackMonth
        //前一月
        private FinalShowResult prepareShowByOneBackMonth(bool isByProject, bool isByCity, List<string> selectedCityNames)
        {
            return prepareShow(new PrepareShowInfo(isByProject, isByCity, selectedCityNames, null, null), getDateKeyByThisMonth, addDateByThisMonth, getRetDicByOneBackMonth);
        }

        private Dictionary<string, KPIResultInfo> getRetDicByOneBackMonth(PrepareShowInfo info, FuncDateKey funcKey)
        {
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            DateTime dtBackOneMonthTime = DateTime.Now.AddMonths(-1);
            DateTime dtBackOneMonthBeginTime = new DateTime(dtBackOneMonthTime.Year, dtBackOneMonthTime.Month, 1).ToLocalTime();
            int backOneMonthTime = (int)(JavaDate.GetMilliseconds(dtBackOneMonthBeginTime) / 1000L);

            setTime(backOneMonthTime);
            setTime(getMonthEndTime(backOneMonthTime));

            foreach (KPIPopTable tableData in curRetDataDic.Values)
            {
                if (!info.KpiPopTableNames.Contains(tableData.tablename))
                {
                    continue;
                }
                tableData.reloadDataResultByLevel((MainModel.User.DBID == -1), "", "");
                foreach (KPIResultInfo retInfo in tableData.dataResult)
                {
                    if (backOneMonthTime == getMonthBeginTime(retInfo.stime))
                    {
                        string dateKey = funcKey(retInfo);
                        setResultInfo(info, retDic, tableData, retInfo, dateKey);
                    }
                }
            }

            return retDic;
        }
        #endregion

        #region prepareShowByYesterday
        //前一天
        private FinalShowResult prepareShowByYesterday(bool isByProject, bool isByCity, List<string> selectedCityNames)
        {
            return prepareShow(new PrepareShowInfo(isByProject, isByCity, selectedCityNames, null, null), getDateKeyByYesterday, addDateByDay, getRetDicByYesterday);
        }

        private Dictionary<string, KPIResultInfo> getRetDicByYesterday(PrepareShowInfo info, FuncDateKey funcKey)
        {
            Dictionary<string, KPIResultInfo> retDic = new Dictionary<string, KPIResultInfo>();
            DateTime dtYesterDay = DateTime.Now.AddDays(-1);
            DateTime dtYesterDayBeginTime = new DateTime(dtYesterDay.Year, dtYesterDay.Month, dtYesterDay.Day, 0, 0, 0);
            int yesterdayBeginTime = (int)(JavaDate.GetMilliseconds(dtYesterDayBeginTime) / 1000L);

            foreach (KPIPopTable tableData in curRetDataDic.Values)
            {
                if (!info.KpiPopTableNames.Contains(tableData.tablename))
                {
                    continue;
                }
                tableData.reloadDataResultByLevel((MainModel.User.DBID == -1), "", "");
                foreach (KPIResultInfo retInfo in tableData.dataResult)
                {
                    if (yesterdayBeginTime == retInfo.stime)
                    {
                        string dateKey = funcKey(retInfo);
                        setResultInfo(info, retDic, tableData, retInfo, dateKey);
                    }
                }
            }
            setTime(yesterdayBeginTime);

            return retDic;
        }

        private string getDateKeyByYesterday(KPIResultInfo retInfo)
        {
            DateTime stimeDate = JavaDate.GetDateTimeFromMilliseconds(retInfo.stime * 1000L);
            string dateKey = stimeDate.ToString("yyyy.MM.dd");
            return dateKey;
        }
        #endregion

        #region getTime
        private int getWeekBeginTime(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DayOfWeek dweek = dt.DayOfWeek;
            int dayToMondy = 0;
            switch (dweek)
            {
                case DayOfWeek.Monday:
                    dayToMondy = 0;
                    break;
                case DayOfWeek.Tuesday:
                    dayToMondy = 1;
                    break;
                case DayOfWeek.Wednesday:
                    dayToMondy = 2;
                    break;
                case DayOfWeek.Thursday:
                    dayToMondy = 3;
                    break;
                case DayOfWeek.Friday:
                    dayToMondy = 4;
                    break;
                case DayOfWeek.Saturday:
                    dayToMondy = 5;
                    break;
                case DayOfWeek.Sunday:
                    dayToMondy = 6;
                    break;

            }
            DateTime dtbegin = dt.AddDays(-dayToMondy);
            dtbegin = (new DateTime(dtbegin.Year, dtbegin.Month, dtbegin.Day)).ToLocalTime();
            long seconds = JavaDate.GetMilliseconds(dtbegin)/1000;
            return (int)seconds;
        }

        private int getWeekEndTime(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DayOfWeek dweek = dt.DayOfWeek;
            int dayToMondy = 0;
            switch (dweek)
            {
                case DayOfWeek.Monday:
                    dayToMondy = 0;
                    break;
                case DayOfWeek.Tuesday:
                    dayToMondy = 1;
                    break;
                case DayOfWeek.Wednesday:
                    dayToMondy = 2;
                    break;
                case DayOfWeek.Thursday:
                    dayToMondy = 3;
                    break;
                case DayOfWeek.Friday:
                    dayToMondy = 4;
                    break;
                case DayOfWeek.Saturday:
                    dayToMondy = 5;
                    break;
                case DayOfWeek.Sunday:
                    dayToMondy = 6;
                    break;

            }
            DateTime dtbegin = dt.AddDays(-dayToMondy);
            dtbegin = (new DateTime(dtbegin.Year, dtbegin.Month, dtbegin.Day)).ToLocalTime();
            DateTime dtEnd = dtbegin.AddDays(6);
            long seconds = JavaDate.GetMilliseconds(dtEnd) / 1000;
            return (int)seconds;
        }

        private string getWeekStr(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DayOfWeek dweek = dt.DayOfWeek;
            int dayToMondy = 0;
            switch(dweek)
            {
                case DayOfWeek.Monday:
                    dayToMondy =0;
                    break;
                case DayOfWeek.Tuesday:
                    dayToMondy = 1;
                    break;
                case DayOfWeek.Wednesday:
                    dayToMondy = 2;
                    break;
                case DayOfWeek.Thursday:
                    dayToMondy = 3;
                    break;
                case DayOfWeek.Friday:
                    dayToMondy = 4;
                    break;
                case DayOfWeek.Saturday:
                    dayToMondy = 5;
                    break;
                case  DayOfWeek.Sunday:
                    dayToMondy = 6;
                    break;

            }
            return dt.AddDays(-dayToMondy).ToString("yyyy.MM.dd_") + dt.AddDays(6 - dayToMondy).ToString("MM.dd");
        }

        private int getMonthBeginTime(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DateTime dtbegin = new DateTime(dt.Year, dt.Month, 1).ToLocalTime();
            return (int)(JavaDate.GetMilliseconds(dtbegin)/1000);
        }

        private int getMonthEndTime(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DateTime dtbegin = new DateTime(dt.Year, dt.Month, 1).ToLocalTime();
            DateTime dtEnd = dtbegin.AddMonths(1).AddDays(-1);
            return (int)(JavaDate.GetMilliseconds(dtEnd) / 1000);
        }
        /**
        private int getDayBeginTime(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            DateTime dtbegin = new DateTime(dt.Year, dt.Month, dt.Day, 0, 0, 0);
            return (int)(JavaDate.GetMilliseconds(dtbegin) / 1000);
        }
        */
        private string getMonthStr(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            return dt.ToString("yyyy-MM");
        }

        private string getDayStr(int time)
        {
            DateTime dt = JavaDate.GetDateTimeFromMilliseconds(time * 1000L);
            return dt.ToString("yyyy.MM.dd");
        }
        #endregion

        List<int> highlightColumnIdxs = new List<int>();
        private void showInGrid(FinalShowResult showRet)
        {
            dataGridView.Rows.Clear();
            dataGridView.Columns.Clear();
            highlightColumnIdxs.Clear();
            addHighlightColumnIdxs(showRet);
            dataGridView.Columns[0].SortMode = DataGridViewColumnSortMode.Automatic;
            if (showRet.dataRows.Count > 0)
            {
                int indexRowAt = 0;
                for (int r = 0; r < showRet.dataRows.Count; r++)
                {
                    List<object> dataRow = showRet.dataRows[r];
                    indexRowAt = AddRowToGrid(indexRowAt, dataRow, showRet);
                }
            }
            dataGridView.Sort(dataGridView.Columns[0], ListSortDirection.Descending);
            dataGridView.Columns[0].Frozen = true;
            dataGridView.Columns[1].Frozen = true;

            DateTime startDateTime = JavaDate.GetDateTimeFromMilliseconds(minTime * 1000L);
            DateTime endDateTime = JavaDate.GetDateTimeFromMilliseconds(maxTime * 1000L);

            addSelectedProjects();
            curShowType = cbxShowType.SelectedItem.ToString();
            selectedDateStrs = new List<string>();
            foreach (CheckedListBoxItem item in checkedCbxDate.Properties.Items)
            {
                if (item.CheckState == CheckState.Checked)
                {
                    selectedDateStrs.Add(item.Value.ToString());
                }
            }

            List<string> selectedCityNames = this.toolStripDropDownCity.Tag as List<string>;
            if (cbxRelevantES.Checked)//刷新显示异常事件情况
            {
                this.blackBlockPanel_ng.freshDateAndProject(startDateTime, endDateTime, selectedProjects, selectedCityNames, curShowType, selectedDateStrs);
            }
        }

        private void addHighlightColumnIdxs(FinalShowResult showRet)
        {
            for (int i = 0; i < showRet.columnNames.Count; i++)
            {
                string columnname = showRet.columnNames[i];
                dataGridView.Columns.Add("Column" + i, columnname);
                if (curTableType == "gsmvoice" && columnname.Contains("GSM语音质量(%)（RxQuality质量 0-4级占比）")
                    || curTableType == "gsmdata" && columnname.Contains("FTP应用层下载速率(kb/s)(含掉线)")
                    || curTableType == "tdvoice" && columnname.Contains("TD语音质量（%）")
                    || curTableType == "tddata" && columnname.Contains("TD下载速率（%）（TD手机用户下载速率大于500kbps的采样点占比）")
                    || curTableType == "tdidle" && columnname.Contains("TD室外连续覆盖率(%)"))
                {
                    highlightColumnIdxs.Add(i);
                }
            }
        }

        private void addSelectedProjects()
        {
            selectedProjects = new List<int>();
            foreach (CheckedListBoxItem item in cbxProject.Properties.Items)
            {
                if (item.CheckState == CheckState.Checked)
                {
                    string rptName = item.Value.ToString();
                    if (rptName.Contains("地市测"))
                    {
                        selectedProjects.Add(5);
                    }
                    else if (rptName.Contains("省公司测"))
                    {
                        selectedProjects.Add(12);
                    }
                    else if (rptName.Contains("集团测"))
                    {
                        selectedProjects.Add(7);
                    }
                    else if (rptName.Contains("日常测试"))
                    {
                        selectedProjects.Add(1);
                    }
                    else if (rptName.Contains("省公司巡检"))
                    {
                        selectedProjects.Add(3);
                    }
                }
            }
        }

        private int AddRowToGrid(int indexRowAt, List<object> dataRow, FinalShowResult showRet)
        {
            if (checkedCbxDate.Enabled && !checkedCbxDate.Text.Contains((string)dataRow[0]))
            {
                return indexRowAt;
            }
            dataGridView.Rows.Add(1);
            for (int c = 0; c < dataRow.Count; c++)
            {

                object dv = dataRow[c];
                if (dv is DateTime)
                {
                    dataGridView.Rows[indexRowAt].Cells[c].Value = ((DateTime)dv).ToString("yyyy-MM-dd");
                }
                else
                {
                    dataGridView.Rows[indexRowAt].Cells[c].Value = dataRow[c];
#if PopShow_KPI_Color
                        string selShowType = cbxShowType.SelectedItem as string;
                        if (selShowType.Contains("周"))
                        {
                            List<string> keys = new List<string>();
                            foreach (CheckedListBoxItem item in cbxProject.Properties.Items)
                            {
                                if (item.CheckState == CheckState.Checked)
                                {
                                    KPIPopTable kpiPopTable = item.Value as KPIPopTable;
                                    if (kpiPopTable != null)
                                    {
                                        string key = kpiPopTable.tablename;
                                        foreach (PopKPIEntryItem entryItem in kpiPopTable.entryList)
                                        {
                                            if (entryItem.strcoldesc == showRet.columnNames[c])
                                            {
                                                key += entryItem.strcolname;
                                                break;
                                            }
                                        }
                                        keys.Add(key);
                                    }
                                }
                            }

                            foreach (string key in keys)
                            {
                                if (colorRangeDic != null && colorRangeDic.ContainsKey(key))
                                {
                                    List<DTParameterRangeColor> colorRangeLst = colorRangeDic[key] as List<DTParameterRangeColor>;
                                    foreach (DTParameterRangeColor colorRange in colorRangeLst)
                                    {
                                        if (colorRange.Within((float)dataRow[c]))
                                        {
                                            dataGridView.Rows[indexRowAt].Cells[c].Style.BackColor = colorRange.Value;
                                            dataGridView.Rows[indexRowAt].Cells[c].ToolTipText = colorRange.DesInfo;

                                            break;
                                        }
                                    }
                                }
                            }
                        }
#else
                    Console.Write(showRet.ToString());
#endif
                }

                if (highlightColumnIdxs.Contains(c))
                    dataGridView.Rows[indexRowAt].Cells[c].Style.BackColor = Color.Yellow;
                else
                    dataGridView.Rows[indexRowAt].Cells[c].Style.BackColor = Color.White;
               
            }
            indexRowAt++;
            return indexRowAt;
        }

        private void dataGridView_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex >= 2 && e.ColumnIndex < dataGridView.Columns.Count)
            {
                KPIInfoPanelHelper.FreshShowChart_ng_total(e.ColumnIndex, new FreshShowChartControl(chartControl, dataGridView, cbxShowType, null), defaultRangeX, defaultRangeY);
            }
        }
        /**
        private void extraDrawMaxMinValue(double[] doubles,out double minDrawValue,out double maxDrawValue) //求画图显示用的最大最小值
        {
            if (doubles.Length > 0)
            {
                minDrawValue = doubles[0];
                maxDrawValue = doubles[0];
                for (int i = 0; i < doubles.Length; i++)
                {
                    if (doubles[i] > maxDrawValue)
                    {
                        maxDrawValue = doubles[i];
                    }
                    if (doubles[i] < minDrawValue)
                    {
                        minDrawValue = doubles[i];
                    }
                }
            }
            else
            {
                minDrawValue = -999;
                maxDrawValue=999;
            }
        }
        */

        internal void ReturnTChartControl(Steema.TeeChart.TChart tchart)
        {
            tchart.Parent = null;
            splitMain.Panel2.Controls.Add(tchart);
            splitMain.Panel2Collapsed = false;
            tchart.Dock = DockStyle.Fill;
        }

        private void btnColor_Click(object sender, EventArgs e)
        {
#if PopShow_KPI_Color
            kpiColorCfgForm form = new kpiColorCfgForm(this);
            
            form.ShowDialog();
#else
            //do nothing
#endif
        }
        
        private void miExp2Word_Click(object sender, EventArgs e)
        {
            if (dataGridView.RowCount<=0)
            {
                MessageBox.Show("当前无数据可导出！");
                return;
            }
            SelectSavePath();
        }
        private void SelectSavePath()
        {
            SaveFileDialog saveFileDlg = new SaveFileDialog();
            saveFileDlg.Title = "选择要保存文档的路径";
            saveFileDlg.RestoreDirectory = true;
            saveFileDlg.Filter = FilterHelper.Excel;
            if (saveFileDlg.ShowDialog() == DialogResult.OK)
            {
                ExcelControl excel = new ExcelControl();
                try
                {
                    ExportInfo(excel);
                    excel.SaveFile(saveFileDlg.FileName);
                }
                catch
                {
                    MessageBox.Show("导出数据出错！");
                    return;
                }
                finally 
                {
                    excel.CloseExcel();
                }
                if (DialogResult.Yes == MessageBox.Show(this, "Excel文件保存完毕，是否现在打开?", "打开文件", MessageBoxButtons.YesNo))
                {
                    try
                    {
                        System.Diagnostics.Process.Start(saveFileDlg.FileName);
                    }
                    catch
                    {
                        MessageBox.Show("打开失败!\r\n文件名:" + saveFileDlg.FileName);
                    }
                }
            }
        }

        public void ExportInfo(ExcelControl excel)
        {
            string dirPath = Path.Combine(Application.StartupPath, "KPIPictTemp");
            Directory.CreateDirectory(dirPath);
            DirectoryInfo dirInfo = new DirectoryInfo(dirPath);
            foreach (System.IO.FileInfo fileInfo in dirInfo.GetFiles())
            {
                File.Delete(fileInfo.FullName);
            }

            MasterCom.RAMS.Func.PopShow.KPIInfoPanel_ng.KPIExport2XlsParam excelParam = new MasterCom.RAMS.Func.PopShow.KPIInfoPanel_ng.KPIExport2XlsParam();
            excelParam.excelApp = excel;
            excelParam.dgv = dataGridView;
            excelParam.pictPath = dirPath;
            string title = "";
            title += cbxShowType.SelectedItem.ToString();
            excelParam.title = title;
            WaitBox.Show(this, printPict, excelParam);
        }

        #region
#endregion

        private void printPict(object param)
        {
            WaitBox.Text = "正在生成图片...";
            MasterCom.RAMS.Func.PopShow.KPIInfoPanel_ng.KPIExport2XlsParam wordParam = param as MasterCom.RAMS.Func.PopShow.KPIInfoPanel_ng.KPIExport2XlsParam;
            for (int i = 2; i < dataGridView.ColumnCount; i++)
                {
                    if (KPIInfoPanelHelper.FreshShowChart_ng_total(i, new FreshShowChartControl(chartControl, dataGridView, cbxShowType, null), defaultRangeX, defaultRangeY))
                    {
                        string pictPath = wordParam.pictPath + Path.DirectorySeparatorChar + i.ToString() + ".jpg";
                        chartControl.ExportToImage(pictPath, System.Drawing.Imaging.ImageFormat.Jpeg);
                        WaitBox.ProgressPercent = (int)(i * 100.0 / dataGridView.ColumnCount);
                    }
                }
                fireExp2Xls(wordParam);
        }

        private void fireExp2Xls(object param)
        {
            MasterCom.RAMS.Func.PopShow.KPIInfoPanel_ng.KPIExport2XlsParam excelParam = param as MasterCom.RAMS.Func.PopShow.KPIInfoPanel_ng.KPIExport2XlsParam;
            excelParam.excelApp.Sheet.Name = "KPI报表_" + excelParam.title;
            excelParam.excelApp.ExportExcel(excelParam.excelApp.Sheet, excelParam.dgv);
            object cellHeightPt = ((Microsoft.Office.Interop.Excel.Range)(excelParam.excelApp.Sheet.Cells[1, 1])).Height;
            float fCellHeightPt = float.Parse(cellHeightPt.ToString());//Excel单元格的高度（磅）
            float picWidth = chartControl.Width / 96f * 72;//图片的宽度（磅）
            float picHeight = chartControl.Height / 96f * 72f;//图片的高度（磅）
            int h = (int)(picHeight / fCellHeightPt + 1);
            for (int index = 2; index < excelParam.dgv.ColumnCount; index++)//插入图片
            {
                int rowIndex=excelParam.dgv.RowCount+2+h*(index -2);
                string pictPath = excelParam.pictPath + Path.DirectorySeparatorChar + index.ToString() + ".jpg";
                if (File.Exists(pictPath))
                {
                    excelParam.excelApp.InsertPicture(rowIndex, pictPath, picWidth, picHeight);
                }
            }
            WaitBox.Close();
        }

        private bool isChartFocused = false;
        public bool IsChartFocused
        {
            get { return isChartFocused; }
        }
        private void chartControl_MouseEnter(object sender, EventArgs e)
        {
            chartControl.Focus();
            isChartFocused = true;
        }
        
        private void chartControl_MouseLeave(object sender, EventArgs e)
        {
            isChartFocused = false;
            this.Focus();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            WelcomForm welFrm = this.Parent.Parent.Parent as WelcomForm;
           if (welFrm!=null)
           {
               welFrm.ExpAll2Xls();
           }
        }

        private void exp2Xls_Click(object sender, EventArgs e)
        {
            SelectSavePath();
        }

        private void checkedCbxMonth_EditValueChanged(object sender, EventArgs e)
        {
            if (checkMonthState)
            {
                needFreshGrid = true;
            }
            else
            {
                needFreshGrid = false;
            }
        }
        private bool needFreshGrid = false;
        private bool checkMonthState = false;

        public class CityOrder
        {
            private readonly Dictionary<int, int> dbIDCityPriorityID = new Dictionary<int, int>();//数据库地市ID,地市优先级ID

            private static CityOrder instance = null;
            public static CityOrder getInstance()
            {
                if (instance == null)
                {
                    instance = new CityOrder();
                }
                return instance;
            }
            public int getCityPriorityID(int cityID)
            { 
                int priorityID;
                dbIDCityPriorityID.TryGetValue(cityID, out priorityID);
                return priorityID;
            }

            public int getCityidByPriority(int priorityID)
            {
                foreach (int dbid in dbIDCityPriorityID.Keys)
                {
                    if (dbIDCityPriorityID[dbid] == priorityID)
                    {
                        return dbid;
                    }
                }
                return -999;
            }
            public CityOrder()
            {
                dbIDCityPriorityID.Clear();
                dbIDCityPriorityID.Add(2, 21);
                dbIDCityPriorityID.Add(3, 20);
                dbIDCityPriorityID.Add(4, 16);
                dbIDCityPriorityID.Add(5, 17);
                dbIDCityPriorityID.Add(6, 18);
                dbIDCityPriorityID.Add(7, 7);
                dbIDCityPriorityID.Add(8, 3);
                dbIDCityPriorityID.Add(9, 4);
                dbIDCityPriorityID.Add(10, 14);
                dbIDCityPriorityID.Add(11, 2);
                dbIDCityPriorityID.Add(12, 19);
                dbIDCityPriorityID.Add(13, 15);
                dbIDCityPriorityID.Add(14, 13);
                dbIDCityPriorityID.Add(15, 5);
                dbIDCityPriorityID.Add(16, 12);
                dbIDCityPriorityID.Add(17, 11);
                dbIDCityPriorityID.Add(18, 9);
                dbIDCityPriorityID.Add(19, 10);
                dbIDCityPriorityID.Add(20, 6);
                dbIDCityPriorityID.Add(21, 8);
                dbIDCityPriorityID.Add(22, 1);
            }
        }

        public class PopKPIQueryCondition //欢迎界面KPI查询条件
        {
            public PopKPIQueryCondition()
            {
                CheckedProjects = new List<string>();
                showType = "";
                CheckedCityNames = new List<string>();
                DateMap = new Dictionary<string, bool>();
                isByProject = false;
                isByCity = false;
            }

            public List<string> CheckedProjects { get; set; } //已选的项目
            public string showType { get; set; }//已选的周期时间
            public List<string> CheckedCityNames { get; set; }//已选的城市名
            public Dictionary<string,bool> DateMap { get; set; }//已选的时间段
            public bool isByProject { get; set; }
            public bool isByCity { get; set; }

            public void AddCheckedProject(string checkedProject)
            {
                this.CheckedProjects.Add(checkedProject);
            }

            public void SetShowType(string showType)
            {
                this.showType = showType;
            }

            public void AddCheckedCityName(string cityName)
            {
                this.CheckedCityNames.Add(cityName);
            }

            public void AddDate(string dateDesc, bool isCheckedDate)
            {
                this.DateMap[dateDesc] = isCheckedDate;
            }

            public void SetIsByProject(bool isByProject)
            {
                this.isByProject = isByProject;
            }

            public void SetIsByCity(bool isByCity)
            {
                this.isByCity = isByCity;
            }
        }

        private void checkedComboBoxEdit1_Properties_Closed(object sender, DevExpress.XtraEditors.Controls.ClosedEventArgs e)
        {
            if (needFreshGrid)
            {
               FinalShowResult rSet= checkedCbxDate.Properties.Tag as FinalShowResult;
               if (rSet!=null)
               {
                   showInGrid(rSet);
               }
            }
        }

        private void checkedCbxMonth_MouseDown(object sender, MouseEventArgs e)
        {
            checkedCbxDate.Properties.DropDownRows = checkedCbxDate.Properties.Items.Count + 1;
        }

        private void btnFresh_Click(object sender, EventArgs e)
        {
            refreshShowReport(true);
            resetTime();
            KPIInfoPanelHelper.FreshShowChart_ng_total(2, new FreshShowChartControl(chartControl, dataGridView, cbxShowType, null), defaultRangeX, defaultRangeY);
        }

        private void cbxProject_EditValueChanged(object sender, EventArgs e)
        {
            projectChanged();
        }

        private void cbxShowType_SelectedIndexChanged_1(object sender, EventArgs e)
        {
            if (cbxShowType.Text == "按月" || cbxShowType.Text == "按周" || cbxShowType.Text == "按天")
            {
                labelDate.Enabled = true;
                checkedCbxDate.Enabled = true;
            }
            else
            {
                labelDate.Enabled = false;
                checkedCbxDate.Enabled = false;
            }

            checkedCbxDate.Properties.Items.Clear();
            List<int> timeList = new List<int>();
            //foreach (KPIResultInfo retInfo in allKPIResultInfoList)
            //{
            //    timeList.Add(retInfo.stime);
            //}
            if (tableTypeAllKPIResultInfoList.ContainsKey(curTableType))
            {
                List<KPIResultInfo> curAllKPIResultInfoList = tableTypeAllKPIResultInfoList[curTableType];
                foreach (KPIResultInfo retInfo in curAllKPIResultInfoList)
                {
                    timeList.Add(retInfo.stime);
                }
                timeList.Sort();
            }

            List<string> showTypeStrs=new List<string>();
            if (cbxShowType.Text == "前一周" || cbxShowType.Text == "本周" || cbxShowType.Text == "按周")
            {
                addShowTypes(showTypeStrs, timeList, new Func(getWeekStr));
            }
            else if (cbxShowType.Text == "前一月" || cbxShowType.Text == "本月" || cbxShowType.Text == "按月")
            {
                addShowTypes(showTypeStrs, timeList, new Func(getMonthStr));
            }
            else if (cbxShowType.Text == "前一天" || cbxShowType.Text == "按天")
            {
                addShowTypes(showTypeStrs, timeList, new Func(getDayStr));
            }

            checkedCbxDate.Properties.Items.Clear();
            //foreach (string showTypeStr in showTypeStrs)
            //{
            //    checkedCbxDate.Properties.Items.Add(showTypeStr, true);
            //}
            for (int i = showTypeStrs.Count - 1; i >= 0; i--)
            {
                checkedCbxDate.Properties.Items.Add(showTypeStrs[i], true);
            }
        }

        private void addShowTypes(List<string> showTypeStrs, List<int> timeList, Func func)
        {
            foreach (int time in timeList)
            {
                string typeStr = func(time);
                if (!showTypeStrs.Contains(typeStr))
                {
                    showTypeStrs.Add(typeStr);
                }
            }
        }

        delegate string Func(int time);

        private void btnShowCityNames_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(btnShowCityNames.Width, btnShowCityNames.Height);
            toolStripDropDownCity.Show(this.btnShowCityNames, pt,ToolStripDropDownDirection.BelowLeft);
        }

        private void btnBack_Click(object sender, EventArgs e)
        {
            PopKPIQueryCondition queryCondition = backStacks.Pop();
            forwardStacks.Push(queryCondition);
            queryCondition = backStacks.Peek();
            updatePanel(queryCondition);
        }

        private void btnForward_Click(object sender, EventArgs e)
        {
            PopKPIQueryCondition queryCondition = forwardStacks.Pop();
            backStacks.Push(queryCondition);
            updatePanel(queryCondition);
        }

        private void updatePanel(PopKPIQueryCondition queryCondition)
        {
            btnBack.Enabled = backStacks.Count > 1;
            btnForward.Enabled = forwardStacks.Count > 0;
            foreach (CheckedListBoxItem item in cbxProject.Properties.Items)
            {
                item.CheckState = CheckState.Unchecked;
                foreach (string proj in queryCondition.CheckedProjects)
                {
                    if (item.Value.ToString() == proj)
                    {
                        item.CheckState = CheckState.Checked;
                    }
                }
            }

            foreach (object item in cbxShowType.Items)
            {
                if (item.ToString() == queryCondition.showType)
                {
                    cbxShowType.SelectedItem = item;
                }
            }

            checkedCbxDate.Properties.Items.Clear();
            foreach (string key in queryCondition.DateMap.Keys)
            {
                checkedCbxDate.Properties.Items.Add(key, queryCondition.DateMap[key]);
            }

            this.toolStripDropDownCity.Tag = queryCondition.CheckedCityNames;

            this.cbxByCity.Checked = queryCondition.isByCity;
            this.cbxByProject.Checked = queryCondition.isByProject;

            updateToolTip();

            refreshShowReport(false);
            resetTime();
            KPIInfoPanelHelper.FreshShowChart_ng_total(2, new FreshShowChartControl(chartControl, dataGridView, cbxShowType, null), defaultRangeX, defaultRangeY);
        }

        private void btnShowPanel2_Click(object sender, EventArgs e)
        {
            splitMain.Panel2Collapsed = !splitMain.Panel2Collapsed;
        }

        private void checkedCbxDate_EditValueChanged(object sender, EventArgs e)
        {
            updateToolTip();
        }

        private void cbxTableType_SelectedIndexChanged(object sender, EventArgs e)
        {
            switch (cbxTableType.Text)
            {
                case "GSM语音":
                    curTableType = "gsmvoice";
                    break;
                case "GSM数据":
                    curTableType = "gsmdata";
                    break;
                case "TD语音":
                    curTableType = "tdvoice";
                    break;
                case "TD数据":
                    curTableType="tddata";
                    break;
                case "TD空闲":
                    curTableType="tdidle";
                    break;
            }

            fillProject();
        }
    }
}
