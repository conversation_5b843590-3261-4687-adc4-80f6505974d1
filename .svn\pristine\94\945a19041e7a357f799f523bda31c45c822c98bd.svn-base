﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
using MapWinGIS;
using MasterCom.MTGis;
using AxMapWinGIS;

namespace MasterCom.RAMS.Func
{
    public class PointMarkManager
    {
        private readonly List<SfLayerInfo> sfLayerList; //已经打开的shpFile图层
        private readonly string dirPath = string.Format(System.Windows.Forms.Application.StartupPath + @"/userdata/");
        private readonly int fieldIndex = 0;
        private readonly string fieldName = "FieldName";

        public PointMarkManager(List<SfLayerInfo> sfLayerList)
        {
            this.sfLayerList = sfLayerList;
        }

        /// <summary>
        /// 从目录中获取存在的点标记文件名
        /// </summary>
        /// <returns></returns>
        public List<string> GetExistedFiles()
        {
            List<string> retList = new List<string>();
            if (!System.IO.Directory.Exists(this.dirPath))
            {
                System.IO.Directory.CreateDirectory(this.dirPath);
            }
            FileInfo[] files = new DirectoryInfo(this.dirPath).GetFiles();
            foreach (FileInfo f in files)
            {
                if (f.Name.EndsWith(@".shp") && f.Name.StartsWith(@"点_"))
                {
                    retList.Add(f.Name);
                }
            }
            return retList;
        }

        /// <summary>
        /// 从指定文件名中获取存在的字段值
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public List<string> GetExistedFields(string fileName)
        {
            List<string> retList = new List<string>();
            Shapefile shpFile = this.GetOpenedShapefile(fileName);
            bool isOpened = true;
            if (shpFile == null)
            {
                isOpened = false;
                shpFile = new Shapefile();
                if (!shpFile.Open(this.dirPath + fileName, null))
                {
                    return retList;
                }
            }

            for (int i = 0; i < shpFile.NumShapes; i++)
            {
                string value = shpFile.get_CellValue(this.fieldIndex, i) as string;
                if (value != null)
                {
                    retList.Add(value);
                }
            }

            if (!isOpened)
            {
                shpFile.Close();
            }
            return retList;
        }

        /// <summary>
        /// 获取在地图上已经显示的点标记
        /// </summary>
        /// <returns></returns>
        public List<ExPointMark> GetShownPointDic()
        {
            List<ExPointMark> retList = new List<ExPointMark>();
            foreach (SfLayerInfo sfInfo in this.sfLayerList)
            {
                if (!sfInfo.name.StartsWith(@"点_")
                    || sfInfo.vecItem.visible != 1
                    || sfInfo.sf.ShapefileType != ShpfileType.SHP_POINT)
                {
                    continue;
                }

                // 已知所有点图层都是未关闭的
                for (int i = 0; i < sfInfo.sf.NumShapes; ++i)
                {
                    Shape shape = sfInfo.sf.get_Shape(i);
                    double x = 0, y = 0;
                    shape.get_XY(0, ref x, ref y);
                    string fieldValue = sfInfo.sf.get_CellValue(this.fieldIndex, i) as string;
                    string fileName = sfInfo.name + ".shp";
                    ExPointMark pm = new ExPointMark(x, y, fieldValue);
                    pm.FileName = fileName;
                    retList.Add(pm);
                }
            }
            return retList;
        }

        /// <summary>
        /// 获取在地图上已经选中的点标记
        /// </summary>
        /// <param name="map"></param>
        /// <param name="pixelX"></param>
        /// <param name="pixelY"></param>
        /// <param name="shownPointList"></param>
        /// <returns></returns>
        public List<ExPointMark> GetSelectedPointDic(AxMap map, int pixelX, int pixelY,
            List<ExPointMark> shownPointList)
        {
            List<ExPointMark> retList = new List<ExPointMark>();
            foreach (ExPointMark point in shownPointList)
            {
                double px = 0, py = 0;
                map.ProjToPixel(point.Longitude, point.Latitude, ref px, ref py);
                double distance = Math.Sqrt((px - pixelX) * (px - pixelX) + (py - pixelY) * (py - pixelY));
                if (distance < 10)
                {
                    retList.Add(point);
                }
            }
            return retList;
        }

        /// <summary>
        /// 获取文件名对应的已经打开的shpFile对象
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public Shapefile GetOpenedShapefile(string fileName)
        {
            foreach (SfLayerInfo sfInfo in this.sfLayerList)
            {
                if (fileName.Equals(sfInfo.name + ".shp"))
                {
                    return sfInfo.sf;
                }
            }
            return null;
        }

        /// <summary>
        /// 打开shp文件返回shpFile对象
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public Shapefile GetNewShapefile(string fileName)
        {
            MapWinGIS.Shapefile newFile = new MapWinGIS.Shapefile();

            List<string> existedFile = this.GetExistedFiles();
            if (existedFile.Contains(fileName))
            {
                return newFile.Open(this.dirPath + fileName, null) ? newFile : null;
            }

            newFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POINT);
            MapWinGIS.Field field = new MapWinGIS.Field();
            field.Type = MapWinGIS.FieldType.STRING_FIELD;
            field.Name = this.fieldName;
            field.Width = 10;
            int fIndex = 0;
            newFile.EditInsertField(field, ref fIndex, null);
            newFile.SaveAs(this.dirPath + fileName, null);
            return newFile;
        }
        
        /// <summary>
        /// 在地图上刷新shpFile对象中包含的点
        /// </summary>
        /// <param name="shpFile"></param>
        /// <param name="map"></param>
        /// <param name="mapForm"></param>
        public void RefreshLayer(Shapefile shpFile, AxMap map, MapForm mapForm)
        {
            SfLayerInfo refreshSF = null;
            foreach (SfLayerInfo sfInfo in this.sfLayerList)
            {
                if (sfInfo.sf == shpFile)
                {
                    refreshSF = sfInfo;
                    break;
                }
            }

            if (refreshSF == null)
            {
                VecLayerItem vLayer = new VecLayerItem();
                vLayer.file_path_name = shpFile.Filename;
                vLayer.name = Path.GetFileNameWithoutExtension(shpFile.Filename);
                vLayer.style_bg_opaque = 1f;
                vLayer.style_bg_fill = 1;
                vLayer.label_show = 1;
                vLayer.style_bg_color = System.Drawing.Color.Red;

                int hnd = map.AddLayer(shpFile, true);
                refreshSF = new SfLayerInfo();
                refreshSF.hnd = hnd;
                refreshSF.name = vLayer.name;
                refreshSF.sf = shpFile;
                refreshSF.vecItem = vLayer;
                this.sfLayerList.Add(refreshSF);
            }

            LayerControlDlg dlg = new LayerControlDlg();
            dlg.FillCurrentLayers(mapForm, map, new List<SfLayerInfo>(), new List<CustomDrawLayer>(),new List<LayerBase>());
            dlg.ApplyLayerShowStyle(refreshSF);
            dlg.Dispose();
        }

        /// <summary>
        /// 判断字段值是否在指定的文件中
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public bool IsFieldExisted(string fileName, string value)
        {
            List<string> fields = this.GetExistedFields(fileName);
            return fields.Contains(value);
        }

        /// <summary>
        /// 将图层文件名转换成显示图层名
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public string ToShowName(string name)
        {
            int s = 0, e = name.Length;

            if (name.StartsWith("点_"))
            {
                s = name.IndexOf("_") + 1;
            }
            if (name.EndsWith(".shp"))
            {
                e = name.LastIndexOf(".");
            }

            return name.Substring(s, e - s);
        }

        /// <summary>
        /// 将显示图层名转换为图层文件名
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public string ToFileName(string name)
        {
            if (!name.StartsWith("点_"))
            {
                name = "点_" + name;
            }
            if (!name.EndsWith(".shp"))
            {
                name += ".shp";
            }
            return name;
        }

    } // end class
} // end namespace
