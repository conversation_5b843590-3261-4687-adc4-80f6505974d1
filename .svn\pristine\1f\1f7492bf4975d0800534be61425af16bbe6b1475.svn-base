﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using System.IO;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.RAMS.Frame
{
    public partial class CommonUseFileNamePanel : UserControl
    {
        public CommonUseFileNamePanel()
        {
            InitializeComponent();
            btnOK.Click += BtnOK_Click;
            chkAll.CheckedChanged += ChkAll_CheckedChanged;
            listView.ItemChecked += ListView_ItemChecked;
        }

        public System.Windows.Forms.TextBox TextBox
        {
            get;
            set;
        }

        public System.Windows.Forms.ToolStripDropDown ToolStripFileNameApplier
        {
            get;
            set;
        }

        public void LoadFileNames()
        {
            if (!File.Exists(cfgFileName))
            {
                return;
            }

            List<object> infoList;
            try
            {
                XmlConfigFile configFile = new XmlConfigFile(cfgFileName);
                infoList = configFile.GetItemValue("CommonUseFileConfig", "CommonUseFileInfos", GetItemValue) as List<object>;
            }
            catch
            {
                return;
            }
            
            foreach (object obj in infoList)
            {
                CommonUseFileInfo info = obj as CommonUseFileInfo;
                ListViewItem item = new ListViewItem();
                item.Tag = info;
                item.Text = string.Format("[{0}]{1}", info.Comment, info.FileName);
                listView.Items.Add(item);
            }
        }

        private object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            CommonUseFileInfo info = new CommonUseFileInfo();
            info.FileName = configFile.GetItemValue(item, "FileName") as string;
            info.Comment = configFile.GetItemValue(item, "Comment") as string;
            return info;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            bool first = true;
            StringBuilder sb = new StringBuilder("");
            foreach (ListViewItem item in listView.Items)
            {
                if (!item.Checked)
                {
                    continue;
                }
                if (!first)
                {
                    sb.Append(" or ");
                }
                sb.Append((item.Tag as CommonUseFileInfo).FileName);
                first = false;
            }
            this.TextBox.Text = sb.ToString();
            ToolStripFileNameApplier.Close();
        }

        private void ChkAll_CheckedChanged(object sender, EventArgs e)
        {
            listView.ItemChecked -= ListView_ItemChecked;
            foreach (ListViewItem item in listView.Items)
            {
                item.Checked = chkAll.Checked;
            }
            listView.ItemChecked += ListView_ItemChecked;
        }

        private void ListView_ItemChecked(object sender, ItemCheckedEventArgs e)
        {
            chkAll.CheckedChanged -= ChkAll_CheckedChanged;
            chkAll.Checked = true;
            foreach (ListViewItem item in listView.Items)
            {
                if (!item.Checked)
                {
                    chkAll.Checked = false;
                    break;
                }
            }
            chkAll.CheckedChanged += ChkAll_CheckedChanged;
        }

        private readonly string cfgFileName = Application.StartupPath + @"\userdata\CommonUseFileNames.xml";
    }

    public class CommonUseFileInfo
    {
        public string FileName { get; set; }
        public string Comment { get; set; }
    }
}
