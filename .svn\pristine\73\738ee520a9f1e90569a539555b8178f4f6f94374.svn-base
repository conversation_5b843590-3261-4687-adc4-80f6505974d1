﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Compare_Foreground
{
    public partial class GridCellsForm : MinCloseForm
    {
        public GridCellsForm()
        {
            InitializeComponent();
        }

        public void FillData(GridMatrix<GridCellsUnit> gridCellUnits, CompareParam cpParam, bool bHost)
        {
            List<GridCellInfo> gridCells = new List<GridCellInfo>();

            foreach (GridCellsUnit unit in gridCellUnits)
            {
                foreach (ICell cell in unit.CellDataHubDic.Keys)
                {
                    GridCellInfo gridCell = new GridCellInfo(unit.LTLng, unit.LTLat, cell, unit.CellDataHubDic[cell]);
                    gridCell.GetKpi(cpParam, bHost);

                    if (gridCell.IsValid(cpParam, bHost))
                    {
                        gridCells.Add(gridCell);
                    }
                }
            }

            gridControlCells.DataSource = gridCells;
            gridControlCells.RefreshDataSource();
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            try
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(gridViewCell);
            }
            catch 
            {
                MessageBox.Show("导出到Excel失败...");
            }
        }
    }

    public class GridCellInfo
    {
        public double LtLng { get; set; }

        public double LtLat { get; set; }

        public ICell Cell { get; set; }

        public StatDataHubBase DataHub { get; set; }

        public string CellName { get { return Cell.Name; } }

        public int LAC
        {
            get
            {
                if (Cell is Cell)
                    return (Cell as Cell).LAC;
                else if (Cell is TDCell)
                    return (Cell as TDCell).LAC;
                else if (Cell is LTECell)
                    return (Cell as LTECell).TAC;
                else if (Cell is WCell)
                    return (Cell as WCell).LAC;
                else if (Cell is CDCell)
                    return (Cell as CDCell).LAC;
                else if (Cell is NRCell)
                    return (Cell as NRCell).TAC;
                else if (Cell is UnknowCell)
                    return (Cell as UnknowCell).LAC;
                else
                    return 0;
            }
        }

        public long CI
        {
            get
            {
                if (Cell is Cell)
                    return (Cell as Cell).CI;
                else if (Cell is TDCell)
                    return (Cell as TDCell).CI;
                else if (Cell is LTECell)
                    return (Cell as LTECell).ECI;
                else if (Cell is WCell)
                    return (Cell as WCell).CI;
                else if (Cell is CDCell)
                    return (Cell as CDCell).CI;
                else if (Cell is NRCell)
                    return (Cell as NRCell).NCI;
                else if (Cell is UnknowCell)
                    return (Cell as UnknowCell).CI;
                else
                    return 0;
            }
        }

        private double kpiValue;
        public double KpiValue
        {
            get
            {
                return kpiValue;
            }
        }

        public GridCellInfo(double ltlng, double ltlat, ICell cell, StatDataHubBase hub)
        {
            this.LtLng = ltlng;
            this.LtLat = ltlat;
            this.Cell = cell;
            this.DataHub = hub;
        }

        public void GetKpi(CompareParam cpParam, bool bHost)
        {
            if (DataHub == null) return;

            string formula = bHost ? cpParam.formula_A : cpParam.formula_B;
            kpiValue = DataHub.CalcValueByFormula(formula);
        }

        public bool IsValid(CompareParam cpParam, bool bHost)
        {
            return cpParam.IsValid(kpiValue, bHost);
        }
    }
}
