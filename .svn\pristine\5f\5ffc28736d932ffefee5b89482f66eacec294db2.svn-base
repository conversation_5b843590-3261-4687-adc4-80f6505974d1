﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ;
using MasterCom.Util.UiEx;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NROptimizationcCusters : NROptimizationcCustersBase
    {
        public NROptimizationcCusters(MainModel mainModel)
        {
        }



        public override string Name
        {
            get
            {
                return "5G簇优化统计";
            }
        }

        protected override bool isValidCondition()
        {
            List<FileInfo> nrFiles = new List<FileInfo>();
            foreach (var file in Condition.FileInfos)
            {
                ServiceName name = ServiceTypeManager.getServiceNameFromTypeID(file.ServiceType);
                if (name == ServiceName.NR)
                {
                    nrFiles.Add(file);
                }
            }
            if (nrFiles.Count == 0)
            {
                MessageBox.Show("请选择5G文件进行簇优化统计");
                return false;
            }
            Condition.FileInfos = nrFiles;

            NRStationAcceptCondition cond = new NRStationAcceptCondition();
            cond.Init();
            FolderBrowserDialog dlg = new FolderBrowserDialog();
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                cond.SaveFolder = dlg.SelectedPath;
                this.SaveFolder = dlg.SelectedPath;
                NRPicture nRPicture = new NRPicture(mainModel, condition);
                nRPicture.CreatePicture(cond);
                this.btsInfoDic = NROptimizationcCustersBaseInfo.btsDic;
                return true;
            }
            return false;
        }
    }

    public class NRPicture : NROptimizationcCustersBaseInfo
    {
        NRStationAcceptCondition nRStationAcceptCondition = new NRStationAcceptCondition();
        public NRPicture(MainModel mainModel)
   : base(mainModel)
        {

        }

        public NRPicture(MainModel mainModel, QueryCondition queryCondition)
: base(mainModel)
        {
            condition = queryCondition;
        }

        protected override bool isValidCondition()
        {
            initManager(nRStationAcceptCondition);
            query();
            return true;
        }

        public void CreatePicture(NRStationAcceptCondition cond)
        {
            nRStationAcceptCondition = cond;
            isValidCondition();
        }

        protected override void initManager(StationAcceptConditionBase cond)
        {
            manager = new NRStationAcceptManager();
            manager.SetAcceptCond(cond, "簇优化");
        }

        protected override void query()
        {
            MainModel.FireDTDataChanged(MainModel.MainForm);
            errMsg = null;
            base.query();
        }

        protected override List<string> queryColumns
        {
            get
            {
                return new List<string>()
                {
                     "isampleid",
                     "itime",
                     "ilongitude",
                     "ilatitude",
                     "NR_SSB_ARFCN",
                     "NR_PCI",
                     "NR_SS_RSRP",
                     "NR_SS_SINR",
                     "NR_Throughput_MAC_DL",
                     "NR_Throughput_MAC_UL",
                     "NR_APP_type",
                     "NR_APP_Status",

                     "NR_lte_TAC",
                     "NR_lte_ECI",
                     "NR_lte_EARFCN",
                     "NR_lte_PCI",
                     "NR_lte_RSRP",
                     "NR_lte_RSRQ",
                     "NR_lte_RSSI",
                     "NR_lte_SINR",
                };
            }
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.DefaultSerialThemeName = null;

            foreach (string col in queryColumns)
            {
                List<ColumnDefItem> items = InterfaceManager.GetInstance().GetColumnDefByShowName(col);
                option.SampleColumns.AddRange(items);
            }

            option.EventInclude = true;
            option.MessageInclude = true;

            return option;
        }

        protected override void queryReplayInfo(ClientProxy clientProxy, Package package, FileInfo fileInfo)
        {
            base.queryReplayInfo(clientProxy, package, fileInfo);
            if (judgeValidFile(fileInfo))
            {
                analyzeFile(fileInfo);
                MainModel.DTDataManager.Clear();
            }
        }
    }

    public class NROptimizationcCustersBaseInfo : DIYReplayFileQuery
    {
        public NROptimizationcCustersBaseInfo(MainModel mainModel)
 : base(mainModel)
        {
        }

        protected StationAcceptManagerBase manager;
        protected string errMsg;
        public static Dictionary<string, BtsInfoBase> btsDic { get; set; }

        /// <summary>
        /// 子类必须重写
        /// </summary>
        protected virtual void initManager(StationAcceptConditionBase cond)
        {
            manager.SetAcceptCond(cond);
        }

        /// <summary>
        /// 子类必须重写
        /// </summary>
        protected virtual List<string> queryColumns
        {
            get
            {
                return new List<string>()
                {
                     "isampleid",
                     "itime",
                     "ilongitude",
                     "ilatitude"
                };
            }
        }

        protected virtual bool judgeValidFile(FileInfo fileInfo)
        {
            if (MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return false;
            }
            return true;
        }

        protected virtual void analyzeFile(FileInfo fileInfo)
        {
            manager.AnalyzeFile(fileInfo, MainModel.DTDataManager.FileDataManagers[0]);
        }

        protected override void doPostReplayAction()
        {
            afterAnalyzeInThread();
        }

        protected virtual void afterAnalyzeInThread()
        {
            btsDic = manager.getBtsInfo();
        }
    }
}
