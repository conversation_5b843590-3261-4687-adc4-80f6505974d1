﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTPilotFreqPolluteByRegion_NBScan : ZTPilotFreqPolluteByRegion_LTEScan
    {
        public ZTPilotFreqPolluteByRegion_NBScan(ServiceName serviceName)
            : base(serviceName)
        {
        }

        public override string Name
        {
            get { return "导频污染分析_NBIOT扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33009, this.Name);
        }
    }
}
