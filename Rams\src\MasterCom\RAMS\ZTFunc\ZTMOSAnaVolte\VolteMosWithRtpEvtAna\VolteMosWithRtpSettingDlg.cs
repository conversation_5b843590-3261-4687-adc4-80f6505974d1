﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class VolteMosWithRtpSettingDlg : BaseForm
    {
        public VolteMosWithRtpSettingDlg(float mosMax, float mosMin)
        {
            InitializeComponent();
            setCondition(mosMax, mosMin);
        }

        private void setCondition(float mosMax, float mosMin)
        {
            numMaxValue.Value = (decimal)mosMax;
            numMinValue.Value = (decimal)mosMin;
        }

        public void GetCondition(out float mosMax, out float mosMin)
        {
            mosMax = (float)numMaxValue.Value;
            mosMin = (float)numMinValue.Value;
        }

    }
}
