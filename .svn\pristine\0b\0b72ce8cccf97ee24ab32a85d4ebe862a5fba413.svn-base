﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class BaseResultControl : UserControl
    {
        public MasterCom.RAMS.UserMng.LogInfoItem LogItemSrc { get; set; }//该窗体所对应的专题功能信息,构造控件时需为该变量赋值
        protected MainModel mainModel;
        public BaseResultControl()
        {
            mainModel = MainModel.GetInstance();
            addFormEventArgs();
        }
        private void addFormEventArgs()
        {
#if PermissionControl_DataExport
            this.MouseLeave += this.clearCurFuncLogItem;
#else
            //
#endif
        }

        protected override void WndProc(ref System.Windows.Forms.Message m)
        {
#if PermissionControl_DataExport
            //const int WM_MOUSEACTIVATE = 0x0021
            const int WM_PARENTNOTIFY = 0x0210;
            const int WM_NCACTIVATE = 0x0086;

            if (m.Msg == WM_PARENTNOTIFY || m.Msg == WM_NCACTIVATE)
            {
                setCurFuncLogItem(null, null);
            }
#endif
            base.WndProc(ref m);
        }
        protected virtual void setCurFuncLogItem(object sender, EventArgs e)
        {
            MasterCom.RAMS.UserMng.ExportFuncResultManager.GetInstance().SetCurLogItem(LogItemSrc);
        }
        protected virtual void clearCurFuncLogItem(object sender, EventArgs e)
        {
            MasterCom.RAMS.UserMng.ExportFuncResultManager.GetInstance().SetCurLogItem(null);
        }
    }
}
