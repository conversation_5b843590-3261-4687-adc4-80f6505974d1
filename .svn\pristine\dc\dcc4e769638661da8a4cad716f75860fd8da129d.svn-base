﻿namespace MasterCom.RAMS.Func
{
    partial class MarkFilesSelectPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MarkFilesSelectPanel));
            this.treeListMarkFiles = new DevExpress.XtraTreeList.TreeList();
            this.colKey = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.btnOK = new System.Windows.Forms.Button();
            this.buttonRemove = new System.Windows.Forms.Button();
            ((System.ComponentModel.ISupportInitialize)(this.treeListMarkFiles)).BeginInit();
            this.SuspendLayout();
            // 
            // treeListMarkFiles
            // 
            this.treeListMarkFiles.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.treeListMarkFiles.Appearance.Empty.BackColor = System.Drawing.Color.Ivory;
            this.treeListMarkFiles.Appearance.Empty.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.treeListMarkFiles.Appearance.Empty.Options.UseBackColor = true;
            this.treeListMarkFiles.Appearance.Empty.Options.UseForeColor = true;
            this.treeListMarkFiles.Appearance.EvenRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(253)))), ((int)(((byte)(245)))), ((int)(((byte)(230)))));
            this.treeListMarkFiles.Appearance.EvenRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListMarkFiles.Appearance.EvenRow.Options.UseBackColor = true;
            this.treeListMarkFiles.Appearance.EvenRow.Options.UseForeColor = true;
            this.treeListMarkFiles.Appearance.FooterPanel.BackColor = System.Drawing.Color.NavajoWhite;
            this.treeListMarkFiles.Appearance.FooterPanel.BorderColor = System.Drawing.Color.NavajoWhite;
            this.treeListMarkFiles.Appearance.FooterPanel.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.treeListMarkFiles.Appearance.FooterPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListMarkFiles.Appearance.FooterPanel.Options.UseBackColor = true;
            this.treeListMarkFiles.Appearance.FooterPanel.Options.UseBorderColor = true;
            this.treeListMarkFiles.Appearance.FooterPanel.Options.UseFont = true;
            this.treeListMarkFiles.Appearance.FooterPanel.Options.UseForeColor = true;
            this.treeListMarkFiles.Appearance.GroupButton.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(224)))), ((int)(((byte)(192)))));
            this.treeListMarkFiles.Appearance.GroupButton.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(224)))), ((int)(((byte)(192)))));
            this.treeListMarkFiles.Appearance.GroupButton.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListMarkFiles.Appearance.GroupButton.Options.UseBackColor = true;
            this.treeListMarkFiles.Appearance.GroupButton.Options.UseBorderColor = true;
            this.treeListMarkFiles.Appearance.GroupButton.Options.UseForeColor = true;
            this.treeListMarkFiles.Appearance.GroupFooter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(232)))), ((int)(((byte)(194)))), ((int)(((byte)(145)))));
            this.treeListMarkFiles.Appearance.GroupFooter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(232)))), ((int)(((byte)(194)))), ((int)(((byte)(145)))));
            this.treeListMarkFiles.Appearance.GroupFooter.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListMarkFiles.Appearance.GroupFooter.Options.UseBackColor = true;
            this.treeListMarkFiles.Appearance.GroupFooter.Options.UseBorderColor = true;
            this.treeListMarkFiles.Appearance.GroupFooter.Options.UseForeColor = true;
            this.treeListMarkFiles.Appearance.HeaderPanel.BackColor = System.Drawing.Color.BurlyWood;
            this.treeListMarkFiles.Appearance.HeaderPanel.BorderColor = System.Drawing.Color.BurlyWood;
            this.treeListMarkFiles.Appearance.HeaderPanel.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.treeListMarkFiles.Appearance.HeaderPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListMarkFiles.Appearance.HeaderPanel.Options.UseBackColor = true;
            this.treeListMarkFiles.Appearance.HeaderPanel.Options.UseBorderColor = true;
            this.treeListMarkFiles.Appearance.HeaderPanel.Options.UseFont = true;
            this.treeListMarkFiles.Appearance.HeaderPanel.Options.UseForeColor = true;
            this.treeListMarkFiles.Appearance.HideSelectionRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.treeListMarkFiles.Appearance.HideSelectionRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(212)))), ((int)(((byte)(208)))), ((int)(((byte)(200)))));
            this.treeListMarkFiles.Appearance.HideSelectionRow.Options.UseBackColor = true;
            this.treeListMarkFiles.Appearance.HideSelectionRow.Options.UseForeColor = true;
            this.treeListMarkFiles.Appearance.HorzLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListMarkFiles.Appearance.HorzLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListMarkFiles.Appearance.HorzLine.Options.UseBackColor = true;
            this.treeListMarkFiles.Appearance.HorzLine.Options.UseForeColor = true;
            this.treeListMarkFiles.Appearance.OddRow.BackColor = System.Drawing.Color.Bisque;
            this.treeListMarkFiles.Appearance.OddRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.treeListMarkFiles.Appearance.OddRow.Options.UseBackColor = true;
            this.treeListMarkFiles.Appearance.OddRow.Options.UseForeColor = true;
            this.treeListMarkFiles.Appearance.Preview.BackColor = System.Drawing.Color.Cornsilk;
            this.treeListMarkFiles.Appearance.Preview.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(255)))));
            this.treeListMarkFiles.Appearance.Preview.Options.UseBackColor = true;
            this.treeListMarkFiles.Appearance.Preview.Options.UseForeColor = true;
            this.treeListMarkFiles.Appearance.Preview.Options.UseTextOptions = true;
            this.treeListMarkFiles.Appearance.Preview.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top;
            this.treeListMarkFiles.Appearance.Row.BackColor = System.Drawing.Color.Ivory;
            this.treeListMarkFiles.Appearance.Row.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(25)))), ((int)(((byte)(25)))), ((int)(((byte)(112)))));
            this.treeListMarkFiles.Appearance.Row.Options.UseBackColor = true;
            this.treeListMarkFiles.Appearance.Row.Options.UseForeColor = true;
            this.treeListMarkFiles.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(138)))));
            this.treeListMarkFiles.Appearance.SelectedRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.treeListMarkFiles.Appearance.SelectedRow.Options.UseBackColor = true;
            this.treeListMarkFiles.Appearance.SelectedRow.Options.UseForeColor = true;
            this.treeListMarkFiles.Appearance.TreeLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.treeListMarkFiles.Appearance.TreeLine.Options.UseForeColor = true;
            this.treeListMarkFiles.Appearance.VertLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListMarkFiles.Appearance.VertLine.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.treeListMarkFiles.Appearance.VertLine.Options.UseBackColor = true;
            this.treeListMarkFiles.Appearance.VertLine.Options.UseForeColor = true;
            this.treeListMarkFiles.Appearance.VertLine.Options.UseTextOptions = true;
            this.treeListMarkFiles.Appearance.VertLine.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.treeListMarkFiles.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("treeListMarkFiles.BackgroundImage")));
            this.treeListMarkFiles.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.colKey});
            this.treeListMarkFiles.Location = new System.Drawing.Point(0, 0);
            this.treeListMarkFiles.Name = "treeListMarkFiles";
            this.treeListMarkFiles.OptionsBehavior.AllowIndeterminateCheckState = true;
            this.treeListMarkFiles.OptionsBehavior.AutoChangeParent = false;
            this.treeListMarkFiles.OptionsBehavior.AutoNodeHeight = false;
            this.treeListMarkFiles.OptionsBehavior.AutoSelectAllInEditor = false;
            this.treeListMarkFiles.OptionsBehavior.CloseEditorOnLostFocus = false;
            this.treeListMarkFiles.OptionsBehavior.Editable = false;
            this.treeListMarkFiles.OptionsBehavior.KeepSelectedOnClick = false;
            this.treeListMarkFiles.OptionsBehavior.ResizeNodes = false;
            this.treeListMarkFiles.OptionsBehavior.SmartMouseHover = false;
            this.treeListMarkFiles.OptionsMenu.EnableFooterMenu = false;
            this.treeListMarkFiles.OptionsPrint.PrintHorzLines = false;
            this.treeListMarkFiles.OptionsPrint.PrintVertLines = false;
            this.treeListMarkFiles.OptionsPrint.UsePrintStyles = true;
            this.treeListMarkFiles.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.treeListMarkFiles.OptionsView.ShowCheckBoxes = true;
            this.treeListMarkFiles.OptionsView.ShowColumns = false;
            this.treeListMarkFiles.OptionsView.ShowFocusedFrame = false;
            this.treeListMarkFiles.OptionsView.ShowHorzLines = false;
            this.treeListMarkFiles.OptionsView.ShowIndicator = false;
            this.treeListMarkFiles.OptionsView.ShowVertLines = false;
            this.treeListMarkFiles.Size = new System.Drawing.Size(222, 267);
            this.treeListMarkFiles.TabIndex = 18;
            this.treeListMarkFiles.BeforeCheckNode += new DevExpress.XtraTreeList.CheckNodeEventHandler(this.treeListMarkFiles_BeforeCheckNode);
            this.treeListMarkFiles.AfterCheckNode += new DevExpress.XtraTreeList.NodeEventHandler(this.treeListMarkFiles_AfterCheckNode);
            // 
            // colKey
            // 
            this.colKey.AllNodesSummary = true;
            this.colKey.Caption = "Registry Keys";
            this.colKey.FieldName = "Key";
            this.colKey.MinWidth = 36;
            this.colKey.Name = "colKey";
            this.colKey.SummaryFooter = DevExpress.XtraTreeList.SummaryItemType.Count;
            this.colKey.SummaryFooterStrFormat = "Count keys = {0}";
            this.colKey.Visible = true;
            this.colKey.VisibleIndex = 0;
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(162, 273);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(57, 23);
            this.btnOK.TabIndex = 19;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // buttonRemove
            // 
            this.buttonRemove.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.buttonRemove.Location = new System.Drawing.Point(3, 273);
            this.buttonRemove.Name = "buttonRemove";
            this.buttonRemove.Size = new System.Drawing.Size(57, 23);
            this.buttonRemove.TabIndex = 19;
            this.buttonRemove.Text = "删除";
            this.buttonRemove.UseVisualStyleBackColor = true;
            this.buttonRemove.Visible = false;
            this.buttonRemove.Click += new System.EventHandler(this.buttonRemove_Click);
            // 
            // MarkFilesSelectPanel
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.BackColor = System.Drawing.SystemColors.Window;
            this.Controls.Add(this.buttonRemove);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.treeListMarkFiles);
            this.Name = "MarkFilesSelectPanel";
            this.Size = new System.Drawing.Size(222, 299);
            ((System.ComponentModel.ISupportInitialize)(this.treeListMarkFiles)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTreeList.TreeList treeListMarkFiles;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colKey;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button buttonRemove;
    }
}
