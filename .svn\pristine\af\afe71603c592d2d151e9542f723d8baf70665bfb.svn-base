﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRPingPangSettingForm : BaseDialog
    {
        public NRPingPangSettingForm()
        {
            InitializeComponent();
        }

        public NRPingPangCondition SetCondition()
        {
            NRPingPangCondition ppCond = new NRPingPangCondition();
            ppCond.TimeLimit = (int)numTimeLimit.Value;
            ppCond.IsLimitSpeed = chkSpeedLimit.Checked;
            ppCond.SpeedLimitMin = (int)numSpeedLimitMin.Value;
            ppCond.SpeedLimitMax = (int)numSpeedLimitMax.Value;
            chkAnaLTE.Checked = ppCond.IsAnaLte;
            return ppCond;
        }

        public NRPingPangCondition GetCondition()
        {
            NRPingPangCondition ppCond = new NRPingPangCondition();
            ppCond.TimeLimit = (int)numTimeLimit.Value;
            ppCond.IsLimitSpeed = chkSpeedLimit.Checked;
            ppCond.SpeedLimitMin = (int)numSpeedLimitMin.Value;
            ppCond.SpeedLimitMax = (int)numSpeedLimitMax.Value;
            ppCond.IsAnaLte = chkAnaLTE.Checked;
            return ppCond;
        }

        private void chkSpeedLimit_CheckedChanged(object sender, EventArgs e)
        {
            numSpeedLimitMin.Enabled = chkSpeedLimit.Checked;
            numSpeedLimitMax.Enabled = chkSpeedLimit.Checked;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (chkSpeedLimit.Checked && (int)numSpeedLimitMin.Value > (int)numSpeedLimitMax.Value)
            {
                MessageBox.Show("时速最小值不能超过最大值，请重新设置。");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }

    public class NRPingPangCondition
    {
        public int TimeLimit { get; set; }
        public bool IsLimitSpeed { get; set; }
        public int SpeedLimitMax { get; set; }
        public int SpeedLimitMin { get; set; }
        public bool IsAnaLte { get; set; }

        public NRPingPangCondition()
        {
            this.TimeLimit = 10;
            this.IsLimitSpeed = false;
            this.SpeedLimitMax = 20;
            this.SpeedLimitMin = 5;
            IsAnaLte = false;
        }
    }
}
