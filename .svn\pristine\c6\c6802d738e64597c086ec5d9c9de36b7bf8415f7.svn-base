﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason;
using MasterCom.Util;
using MasterCom.RAMS.Func.SystemSetting;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRWeakSINRReason : DIYAnalyseByCellBackgroundBaseByFile
    {
        public static List<Event> condEvents { get; set; } = new List<Event>();
        public static List<Event> handOverTooMuchEvents { get; set; } = new List<Event>();
        protected static readonly object lockObj = new object();
        private static NRWeakSINRReason intance = null;
        public static NRWeakSINRReason GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new NRWeakSINRReason();
                    }
                }
            }
            return intance;
        }

        protected NRWeakSINRReason()
            : base(MainModel.GetInstance())
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(true, false);

            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        public override string Name
        {
            get { return "NR质差原因分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35043, this.Name);
        }

        protected override void fireShowForm()
        {
            if (groupSet.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            NRRusultListForm frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(NRRusultListForm)) as NRRusultListForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new NRRusultListForm();
            }
            frm.FillData(groupSet);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }

        protected NRFuncCondition funcCond = new NRFuncCondition();
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            return showFuncCondSetDlg();
        }

        protected bool showFuncCondSetDlg()
        {
            NRReasonOptionDlg dlg = new NRReasonOptionDlg(funcCond);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            groupSet = new List<NRWeakSINRPointGroup>();
            groupDic = new Dictionary<object, NRWeakSINRPointGroup>();
        }

        protected virtual void saveTestPointInfo(FileInfo fi, TestPoint tp, NRWeakSINRPoint wp, params object[] resvParam)
        {
            List<ResvRegion> resvRegions = MainModel.SearchGeometrys.SelectedResvRegions;
            if (resvRegions != null && resvRegions.Count > 0)  //预存区域
            {
                foreach (ResvRegion reg in resvRegions)
                {
                    addGroupInfo(reg, wp, reg);
                }
            }
            else if (condition.Geometorys?.RegionInfo != null)
            {
                addGroupInfo(condition.Geometorys.RegionInfo, wp, condition.Geometorys.RegionInfo);
            }
            else
            {
                addGroupInfo(fi, wp, "预存区域");
            }
        }

        private void addGroupInfo(object item, NRWeakSINRPoint wp, object grpItem)
        {
            NRWeakSINRPointGroup grp;
            if (!groupDic.TryGetValue(item, out grp))
            {
                grp = new NRWeakSINRPointGroup(grpItem, funcCond.Reasons);
                groupDic.Add(item, grp);
            }
            grp.AddWeakSINRPoint(wp);
        }

        protected virtual bool filter(TestPoint tp)
        {
            if (condition.Geometorys.Region != null)
            {
                return !condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
            }
            return false;
        }

        /// <summary>
        /// key可为FileInfo，或者是RegionInfo
        /// </summary>
        private Dictionary<object, NRWeakSINRPointGroup> groupDic;
        private List<NRWeakSINRPointGroup> groupSet;
        //用于判断是否为切换不及时
        public static bool isHandOverUnTimely { get; set; } = false;

        //用于判断是否为切换不合理
        public static bool isHandOverProblem { get; set; } = false;

        //用于判断是否为突然质差
        public static bool isSuddenWeak { get; set; } = false;

        //切换过频繁条件
        public static int timeLimit { get; set; } = 15;
        public static int distanceLimit { get; set; } = 250;
        public static int handoverCount { get; set; } = 2;
        protected override void doStatWithQuery()
        {
            if (MainModel.DTDataManager.FileDataManagers.Count <= 0)
            {
                return;
            }

            foreach (Event evt in MainModel.DTDataManager.FileDataManagers[0].Events)
            {
                bool isValid = NREventHelper.HandoverHelper.JudgeHandoverSuccess(evt.ID, funcCond.IsAnaLte);
                if (isValid)
                {
                    condEvents.Add(evt);
                }
            }
            NREventHelper.HandoverHelper.FilterHandoverEvents(condEvents);

            try
            {
                //切换过频繁
                MainModel.DTDataManager.FileDataManagers[0].Events = condEvents;
                NRHandoverFileDataManager curHandoverFile = NRHandoverAndReselectionManager.GetHandoverTooMuchResult(MainModel.DTDataManager.FileDataManagers[0],
                timeLimit, distanceLimit, handoverCount);
                handOverTooMuchEvents = curHandoverFile.Events;

                dealTestPointList();
                condEvents.Clear();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString() + Environment.NewLine + ex.StackTrace);
            }
        }

        private void dealTestPointList()
        {
            List<TestPoint> testPointList = MainModel.DTDataManager.FileDataManagers[0].TestPoints;
            FileInfo fi = MainModel.DTDataManager.FileDataManagers[0].GetFileInfo();
            LTEHandoverBehindTime info = null;
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint tp = testPointList[i];
                if (!filter(tp))
                {
                    NRWeakSINRPoint pnt = null;
                    if (funcCond.IsValid(tp, funcCond.IsAnaLte))
                    {
                        isHandOverProblem = isHandOverProblemFun(testPointList, i);
                        isHandOverUnTimely = isHandOverUnTimelyFun(testPointList, i, ref info);
                        isSuddenWeak = isSuddenWeakFun(testPointList, i);

                        NRReasonBase reason = funcCond.JudgeReason(tp, funcCond.IsAnaLte);
                        pnt = new NRWeakSINRPoint(fi, tp, reason, funcCond.IsAnaLte);
                        bool isValid = pnt.Calculate();
                        if (!isValid)
                        {
                            continue;
                        }
                    }
                    saveTestPointInfo(fi, tp, pnt);
                }
            }
        }

        public static int timeBeforeWeakSinr { get; set; } = 6;
        public static int timePersist { get; set; } = 3;
        //切换后2秒的电平均值比前2秒均值差的
        private static int timeAround = 2;
        //用于判断是否为切换不合理
        protected bool isHandOverProblemFun(List<TestPoint> testPointList, int index)
        {
            if (condEvents.Count == 0)
            {
                return false;
            }
            TestPoint tp = testPointList[index];
            List<Event> tempEvent = new List<Event>();
            foreach (Event evt in condEvents)
            {
                if ((tp.Time - evt.Time <= timeBeforeWeakSinr)
                    && (tp.Time - evt.Time >= 0))
                {
                    tempEvent.Add(evt);
                }
            }

            if (tempEvent.Count == 0)
            {
                return false;
            }

            return (handOverProblemFun1(testPointList, index, tempEvent)
                    || handOverProblemFun2(testPointList, index, tempEvent));
        }

        //判断切换不合理的方法一:切换后2秒的电平均值比前2秒均值差的
        private bool handOverProblemFun1(List<TestPoint> testPointList, int index, List<Event> tempEvent)
        {
            foreach (Event evt in tempEvent)
            {
                List<TestPoint> before2second = new List<TestPoint>();
                List<TestPoint> after2second = new List<TestPoint>();
                int tempBefore = index;
                int tempAfter = index;

                //取得前2秒的采样点
                getBeforeTps(testPointList, evt, before2second, tempBefore);

                //取得后2秒的采样点
                getAfterTps(testPointList, evt, after2second, tempAfter);

                if ((before2second.Count == 0)
                    || (after2second.Count == 0))
                {
                    continue;
                }

                float beforeSumRsrp = 0;
                float afterSumRsrp = 0;
                NRHandOverType type = NREventHelper.HandoverHelper.GetHandoverType(evt.ID, false);
                if (type == NRHandOverType.LTE)
                {
                    getSumRsrp(before2second, after2second, ref beforeSumRsrp, ref afterSumRsrp, NRTpHelper.NrLteTpManager);
                }
                else if (type == NRHandOverType.NSA || type == NRHandOverType.SA)
                {
                    getSumRsrp(before2second, after2second, ref beforeSumRsrp, ref afterSumRsrp, NRTpHelper.NrTpManager);
                }

                //NRCellType type = NREventHelper.JudgeHandoverEvtType(evt);
                //if (type == NRCellType.LTE)
                //{
                //    getSumRsrp(before2second, after2second, ref beforeSumRsrp, ref afterSumRsrp, NRTpHelper.NrLteTpManager);
                //}
                //else if (type == NRCellType.NR)
                //{
                //    getSumRsrp(before2second, after2second, ref beforeSumRsrp, ref afterSumRsrp, NRTpHelper.NrTpManager);
                //}

                if ((beforeSumRsrp / before2second.Count) - (afterSumRsrp / after2second.Count) > 0)
                {
                    return true;
                }
            }
            return false;
        }

        private void getBeforeTps(List<TestPoint> testPointList, Event evt, List<TestPoint> before2second, int tempBefore)
        {
            while (true)
            {
                --tempBefore;
                if (tempBefore < 0)
                {
                    break;
                }
                if ((evt.Time - testPointList[tempBefore].Time > 0)
                    && (evt.Time - testPointList[tempBefore].Time <= timeAround))
                {
                    before2second.Add(testPointList[tempBefore]);
                }
                if (evt.Time - testPointList[tempBefore].Time > timeAround)
                {
                    break;
                }
            }
        }

        private void getAfterTps(List<TestPoint> testPointList, Event evt, List<TestPoint> after2second, int tempAfter)
        {
            while (true)
            {
                if ((testPointList[tempAfter].Time - evt.Time > 0)
                    && (testPointList[tempAfter].Time - evt.Time <= timeAround))
                {
                    after2second.Add(testPointList[tempAfter]);
                }
                if (testPointList[tempAfter].Time - evt.Time > timeAround)
                {
                    break;
                }
                tempAfter++;
                if (tempAfter == testPointList.Count)
                {
                    break;
                }
            }
        }

        private void getSumRsrp(List<TestPoint> before2second, List<TestPoint> after2second, ref float beforeSumRsrp, ref float afterSumRsrp, NRTpManagerBase nRCond)
        {
            foreach (TestPoint btp in before2second)
            {
                object value = nRCond.GetSCellRsrp(btp);
                if (value != null)
                {
                    beforeSumRsrp += (float)value;
                }
            }

            foreach (TestPoint atp in after2second)
            {
                object value = nRCond.GetSCellRsrp(atp);
                if (value != null)
                {
                    afterSumRsrp += (float)value;
                }
            }
        }

        //判断切换不合理的方法二:切换后主服小区电 平小于邻小区电平+3dBm持续 N1 秒
        private bool handOverProblemFun2(List<TestPoint> testPointList, int index, List<Event> tempEvent)
        {
            foreach (Event evt in tempEvent)
            {
                List<TestPoint> tempTPs = getTempTPs(testPointList, index, evt);

                if (tempTPs.Count == 0)
                {
                    continue;
                }

                bool isEnd = false;
                NRHandOverType type = NREventHelper.HandoverHelper.GetHandoverType(evt.ID, false);
                if (type == NRHandOverType.LTE)
                {
                    isEnd = judgeProblemTP(tempTPs, NRTpHelper.NrLteTpManager);
                }
                else if (type == NRHandOverType.NSA || type == NRHandOverType.SA)
                {
                    isEnd = judgeProblemTP(tempTPs, NRTpHelper.NrTpManager);
                }

                //NRCellType type = NREventHelper.JudgeHandoverEvtType(evt);
                //bool isEnd = false;
                //if (type == NRCellType.LTE)
                //{
                //    isEnd = judgeProblemTP(tempTPs, NRTpHelper.NrLteTpManager);
                //}
                //else if (type == NRCellType.NR)
                //{
                //    isEnd = judgeProblemTP(tempTPs, NRTpHelper.NrTpManager);
                //}

                if (isEnd)
                {
                    return true;
                }
            }
            return false;
        }

        private bool judgeProblemTP(List<TestPoint> tempTPs, NRTpManagerBase nRCond)
        {
            foreach (TestPoint tp in tempTPs)
            {
                object mRsrp = nRCond.GetSCellRsrp(tp);
                if (mRsrp == null)
                {
                    continue;
                }

                float maxNRsrp = getMaxNRsrp(tp, nRCond);
                if (maxNRsrp == -1000000)
                {
                    continue;
                }

                if ((float)mRsrp >= maxNRsrp + 3)
                {
                    break;
                }

                if (tp == tempTPs[tempTPs.Count - 1])
                {
                    return true;
                }
            }
            return false;
        }

        private List<TestPoint> getTempTPs(List<TestPoint> testPointList, int index, Event evt)
        {
            int beforeIndex = index;
            int afterIndex = index + 1;
            List<TestPoint> tempTPs = new List<TestPoint>();
            while ((beforeIndex >= 0)
                    && (testPointList[beforeIndex].Time - evt.Time >= 0))
            {
                if (testPointList[beforeIndex].Time - evt.Time <= timePersist)
                {
                    tempTPs.Add(testPointList[beforeIndex]);
                }
                beforeIndex--;
            }

            while ((afterIndex < testPointList.Count)
                    && (testPointList[afterIndex].Time - evt.Time <= timePersist))
            {
                tempTPs.Add(testPointList[afterIndex]);
                afterIndex++;
            }

            return tempTPs;
        }

        private float getMaxNRsrp(TestPoint tp, NRTpManagerBase nRCond)
        {
            float maxNRsrp = -1000000;
            for (int i = 0; i < 16; i++)
            {
                bool isNCell = judgeNCell(tp, nRCond, i);
                if (isNCell)
                {
                    float? value = nRCond.GetNCellRsrp(tp, i);
                    if (value == null)
                    {
                        continue;
                    }
                    if (maxNRsrp < value)
                    {
                        maxNRsrp = (float)value;
                    }
                }
            }

            return maxNRsrp;
        }

        private bool judgeNCell(TestPoint tp, NRTpManagerBase nRCond, int i)
        {
            if (nRCond is NRTpManager)
            {
                bool isNCell = NRTpHelper.NrTpManager.JudgeIsNCell(tp, i);
                if (!isNCell)
                {
                    return false;
                }
            }
            return true;
        }

        //几秒前
        public static int beforeTime { get; set; } = 5;
        //主小区与邻小区的差值限制
        public static float rsrpDiffer { get; set; } = 1;
        //持续时间
        public static int stayTime { get; set; } = 3;

        protected bool isHandOverUnTimelyFun(List<TestPoint> testPointList, int index, ref LTEHandoverBehindTime info)
        {
            bool isHandOverUnTime = isHandOverUnTimelyFun(testPointList, index, ref info, NRTpHelper.NrTpManager);
            if (funcCond.IsAnaLte && !isHandOverUnTime)
            {
                isHandOverUnTime = isHandOverUnTimelyFun(testPointList, index, ref info, NRTpHelper.NrLteTpManager);
            }

            return isHandOverUnTime;
        }

        //用于判断是否为切换不及时
        protected bool isHandOverUnTimelyFun(List<TestPoint> testPointList, int index, ref LTEHandoverBehindTime info, NRTpManagerBase nRCond)
        {
            bool isUnTimely = false;
            TestPoint weakTp = testPointList[index];
            for (; index >= 0; index--)
            {
                TestPoint tp = testPointList[index];
                double seconds = (weakTp.DateTime - tp.DateTime).TotalSeconds;
                if (seconds > beforeTime)
                {
                    break;
                }

                if (!isValidTestPoint(tp))//非区域内点
                {
                    isUnTimely = saveAndResetOneResult(ref info);
                }
                else
                {
                    bool isEnd = judgeTimely(testPointList, ref info, ref isUnTimely, tp, nRCond);
                    if (isEnd)
                    {
                        break;
                    }
                }

                if (isUnTimely)
                {
                    return true;
                }
            }
            return isUnTimely;
        }

        private bool judgeTimely(List<TestPoint> testPointList, ref LTEHandoverBehindTime info, ref bool isUnTimely, TestPoint tp, NRTpManagerBase nRCond)
        {
            float? pccpchValue = nRCond.GetSCellRsrp(tp);
            float maxNCellPccpch = float.MinValue;
            for (int i = 0; i < 10; i++)
            {
                float? nCellPccpch = nRCond.GetNCellRsrp(tp, i);
                if (nCellPccpch != null)
                {
                    maxNCellPccpch = Math.Max((float)nCellPccpch, maxNCellPccpch);
                }
            }
            if ((maxNCellPccpch - pccpchValue) >= rsrpDiffer)
            {
                if (info == null)
                {
                    info = new LTEHandoverBehindTime();
                }
                info.AddTestPoint(tp, (float)pccpchValue);
                if (tp.Equals(testPointList[0]))
                {//文件最后一点，需要把前面的信息保存起来
                    isUnTimely = saveAndResetOneResult(ref info);
                    return true;
                }
            }
            else
            {
                isUnTimely = saveAndResetOneResult(ref info);
            }
            return false;
        }

        //质量毛刺质量平均值
        public static float suddenWeakAvg { get; set; } = 8;
        //质量毛刺时间限制
        public static int suddenWeakTime { get; set; } = 2;

        protected bool isSuddenWeakFun(List<TestPoint> testPointList, int index)
        {
            bool isSuddenWeakTmp = isSuddenWeakFun(testPointList, index, NRTpHelper.NrTpManager);
            if (funcCond.IsAnaLte && !isSuddenWeakTmp)
            {
                isSuddenWeakTmp = isSuddenWeakFun(testPointList, index, NRTpHelper.NrLteTpManager);
            }

            return isSuddenWeakTmp;
        }

        protected bool isSuddenWeakFun(List<TestPoint> testPointList, int index, NRTpManagerBase nRCond)
        {
            TestPoint weakTp = testPointList[index];
            int tempBefore = index - 1;
            int tempAfter = index + 1;
            int coutSum = 0;
            float sinrSunm = 0;

            for (; tempBefore >= 0; tempBefore--)//前几秒
            {
                TestPoint tp = testPointList[tempBefore];
                double seconds = (weakTp.DateTime - tp.DateTime).TotalSeconds;
                if (seconds > suddenWeakTime)
                {
                    break;
                }
                float? sinr = nRCond.GetSCellSinr(tp);
                if (sinr != null)
                {
                    coutSum++;
                    sinrSunm += (float)sinr;
                }
            }

            for (; tempAfter < testPointList.Count; tempAfter++)//后几秒
            {
                TestPoint tp = testPointList[tempAfter];
                double seconds = (tp.DateTime - weakTp.DateTime).TotalSeconds;
                if (seconds > suddenWeakTime)
                {
                    break;
                }
                float? sinr = nRCond.GetSCellSinr(tp);
                if (sinr != null)
                {
                    coutSum++;
                    sinrSunm += (float)sinr;
                }
            }

            if (coutSum > 0)
            {
                return sinrSunm / coutSum >= suddenWeakAvg;
            }
            return false;
        }

        private bool saveAndResetOneResult(ref LTEHandoverBehindTime info)
        {
            if (info == null)
            {
                return false;
            }
            if (Math.Abs(info.StaySeconds) >= stayTime)//持续时间判断
            {
                info = null;//重置
                return true;
            }
            info = null;//重置
            return false;
        }

        protected override void getResultsAfterQuery()
        {
            if (groupDic.Count > 0)
            {
                NRWeakSINRPointGroup summaryGrp = new NRWeakSINRPointGroup("汇总", funcCond.Reasons);
                foreach (KeyValuePair<object, NRWeakSINRPointGroup> kvp in groupDic)
                {
                    groupSet.Add(kvp.Value);
                    if (groupDic.Count > 1)
                    {//大于一组，需要进行汇总处理
                        summaryGrp.Gather(kvp.Value);
                    }
                }
                if (groupDic.Count > 1)
                {
                    groupSet.Add(summaryGrp);
                }
            }
        }
    }

    public class NRWeakSINRReasonByFile : NRWeakSINRReason
    {
        private static NRWeakSINRReasonByFile instance = null;
        public new static NRWeakSINRReasonByFile GetInstance()
        {
            if (instance == null)
            {
                instance = new NRWeakSINRReasonByFile();
            }
            return instance;
        }

        public override string Name
        {
            get { return "NR质差原因分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool filter(TestPoint tp)
        {
            return false;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
    }
}
