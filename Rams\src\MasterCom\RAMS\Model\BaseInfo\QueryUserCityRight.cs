﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.BaseInfo
{
    public class QueryUserCityRight : DIYSQLBase
    {
        private readonly List<User> users = null;
        public QueryUserCityRight(List<User> users)
            : base(Model.MainModel.GetInstance())
        {
            MainDB = true;
            dbid = 1;
            this.users = users;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] ret = new E_VType[2];
            ret[0] = E_VType.E_Int;
            ret[1] = E_VType.E_Int;
            return ret;
        }

        protected override string getSqlTextString()
        {
            StringBuilder userIDs = new StringBuilder();
            foreach(User user in users)
            {
                userIDs.Append(user.ID + ",");
            }
            string sql =
                string.Format(
                    "select user_id,city_id from tb_cfg_static_user_city_id where user_id in ({0}) order by user_id,city_id;"
                    , userIDs.ToString().TrimEnd(','));
            return sql;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while(true)
            {
                if(WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if(package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    int userID = package.Content.GetParamInt();
                    int cityID = package.Content.GetParamInt();
                    User user = users.Find(delegate(User x)
                    {
                        return x.ID == userID;
                    });
                    user.UpdateCityRight(cityID , true);
                }
                else if(package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if(package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if(package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }


        public override string Name
        {
            get
            {
                return "查询当前用户配置的地市ID";
            }
        }
    }
}
