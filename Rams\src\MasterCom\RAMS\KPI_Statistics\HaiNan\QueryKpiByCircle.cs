﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.KPI_Statistics.HaiNan;
using System.Windows.Forms;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryKpiByCircle : QueryKPIStatBase
    {
        private ConditionSet condSet = null;
        private List<StationData> listStationData = null;
        private double minLon = double.MaxValue, minLat = double.MaxValue;
        private double maxLon = double.MinValue, maxLat = double.MinValue;
        public QueryKpiByCircle() : base()
        {
            this.condSet = new ConditionSet();
            this.condSet.fileName = "";
            this.condSet.radius = 500;
        }
        protected override StatTbToken getTableNameToken()
        {
            return StatTbToken.grid;
        }

        public override string Name
        {
            get
            {
                return "三网指标对比评估";
            }
        }
        protected override void query()
        {
            ConditionSettingDlg conDlg = new ConditionSettingDlg();
            conDlg.SetCondition(this.condSet);
            if (conDlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return;
            }
            this.condSet = conDlg.GetCondition();
            if (!this.readFile(this.condSet.fileName))
            {
                return;
            }

            double meterPerLon = MathFuncs.GetDistance(109, 19, 110, 19);
            double meterPerLat = MathFuncs.GetDistance(109, 19, 109, 20);
            this.minLon -= this.condSet.radius / meterPerLon;
            this.minLat -= this.condSet.radius / meterPerLat;
            this.maxLon += this.condSet.radius / meterPerLon;
            this.maxLat += this.condSet.radius / meterPerLat;

            this.hasInitAll = false;
            base.query();
        }
        private bool readFile(string fileName)
        {
            ExcelNPOIReader reader = new ExcelNPOIReader(fileName);
            ExcelNPOITable table = reader.GetTable();
            try
            {
                this.listStationData = new List<StationData>();
                double lon,lat;
                string fn;
                int index_stationName = 0;
                int index_lon=0;
                int index_lat=0;
                int fieldCount = 0;
                List<string> listColumn = reader.GetColumns();
                for (int i = 0; i < listColumn.Count; i++)
                {
                    string val = listColumn[i];
                    if (val.IndexOf("站点名称") >= 0)
                    {
                        index_stationName = i;
                        fieldCount++;
                    }
                    else if (val.IndexOf("经度") >= 0)
                    {
                        index_lon = i;
                        fieldCount++;
                    }
                    else if (val.IndexOf("纬度") >= 0)
                    {
                        index_lat = i;
                        fieldCount++;
                    }
                }
                if (fieldCount != 3 )
                {
                    MessageBox.Show("基站经纬度文件格式错误，必须包含 站点名称、经度、纬度 三个字段。");
                    return false;
                }
                int invalidCount = 0;
                for (int i = 0; i < table.CellValues.Count;i++ )
                {
                    object[] cellValue = table.CellValues[i];
                    string strLon = cellValue[index_lon].ToString();
                    string strLat = cellValue[index_lat].ToString();
                    strLon = strLon.Replace('。', '.');
                    strLat = strLat.Replace('。', '.');
                    fn = cellValue[index_stationName] as string;
                    bool isValid = judgeValid(out lon, out lat, fn, ref invalidCount, strLon, strLat);
                    if (isValid)
                    {
                        StationData sd = new StationData();
                        sd.stationName = fn;
                        sd.lon = lon;
                        sd.lat = lat;
                        this.listStationData.Add(sd);

                        setLonLat(lon, lat);
                    }
                }
                if (invalidCount > 0)
                { 
                    int total = table.CellValues.Count;
                    DialogResult re = MessageBox.Show(
                                                string.Format("共有数据 {0} 行\n有效数据 {1} 行\n无效数据 {2} 行\n继续统计点击 ‘是’， 终止点击 ‘否’", total, total - invalidCount, invalidCount),
                                                "文件中出现无效的数据行",
                                                MessageBoxButtons.YesNo);
                    if (re != DialogResult.Yes)
                    {
                        return false;
                    }
                }
                return true;
            }
            catch(Exception ex)
            {
                MessageBox.Show(string.Format("读取文件: {0} 失败。\n信息：{1}", fileName, ex.Message));
                return false;
            } 
        }

        private bool judgeValid(out double lon, out double lat, string fn, ref int invalidCount, string strLon, string strLat)
        {
            lon = 0;
            lat = 0;
            if (string.IsNullOrEmpty(fn))
            {
                invalidCount++;
                return false;
            }
            if (!double.TryParse(strLon, out lon) || lon < -180 || lon > 180)
            {
                invalidCount++;
                return false;
            }
            if (!double.TryParse(strLat, out lat) || lat > 90)
            {
                invalidCount++;
                return false;
            }

            return true;
        }

        private void setLonLat(double lon, double lat)
        {
            this.minLon = this.minLon < lon ? this.minLon : lon;
            this.minLat = this.minLat < lat ? this.minLat : lat;
            this.maxLon = this.maxLon > lon ? this.maxLon : lon;
            this.maxLat = this.maxLat > lat ? this.maxLat : lat;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11019, this.Name);
        }

        public override bool CanEnabled(Model.SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override void AddGeographicFilter(Package package)
        {
            if (isQueringEvent)
            {
                AddDIYEndOpFlag(package);
                this.AddDIYRegion_Sample(package);
            }
            else
            {
                this.AddDIYRegion_Intersect(package);
            }
        }
        private bool isOutBound(double lon, double lat)
        {
            if (lon < this.minLon) return true;
            if (lon > this.maxLon) return true;
            if (lat < this.minLat) return true;
            if (lat > this.maxLat) return true;
            return false;
        }
        private bool hasInitAll = false;
        private void initAllArea()
        {
            foreach (StationData sd in this.listStationData)
            {
                KpiDataManager.AddStatData(sd.stationName, sd, null, null, this.curReportStyle != null && this.curReportStyle.HasGridPerCell);
            }
        }
        protected override void recieveAndHandleSpecificStatData(Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            if (!this.hasInitAll)
            {
                this.initAllArea();
                this.hasInitAll = true;
            }
            double lon = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            if (this.isOutBound(lon, lat))
            {
                return;
            }

            float radius = this.condSet.radius;
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lon, lat);
            if (!condition.Geometorys.GeoOp.Contains(grid.CenterLng, grid.CenterLat))
            {
                return;
            } 
            bool filled = false;
            foreach(StationData sd in this.listStationData)
            {
                if (MathFuncs.GetDistance(sd.lon, sd.lat, lon, lat) > radius)
                {
                    continue;
                }
                if (!filled)
                {
                    fillStatData(package, curImgColumnDef, singleStatData);
                    filled = true;
                }
                //List<ResvRegion> regs = getStatImgIntersectRegions(lng, lat);
                int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
                MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);
                KpiDataManager.AddStatData(sd.stationName, sd, fi, singleStatData, this.curReportStyle != null && this.curReportStyle.HasGridPerCell);
            }
        }

        protected override void handleStatEvent(Event evt)
        {
            if (!condition.Geometorys.GeoOp.Contains(evt.Longitude, evt.Latitude))
            {
                return;
            }
            float radius = this.condSet.radius;
            foreach (StationData sd in this.listStationData)
            {
                if (MathFuncs.GetDistance(sd.lon, sd.lat, evt.Longitude, evt.Latitude) > radius)
                {
                    continue;
                }
                StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
                //List<ResvRegion> regs = getEventInRegions(evt);
                FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
                KpiDataManager.AddStatData(sd.stationName, sd, fi, eventData, this.curReportStyle != null && this.curReportStyle.HasGridPerCell);
            }
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            if (KpiDataManager != null && MainModel.MultiGeometrys
               && Condition.Geometorys.SelectedResvRegions != null
               && Condition.Geometorys.SelectedResvRegions.Count > 0)
            {
                foreach (ResvRegion resvRegion in Condition.Geometorys.SelectedResvRegions)
                {
                    KpiDataManager.AddStatData(resvRegion.RegionName, resvRegion, null, null, false);
                }
            }
            base.afterRecieveAllData(reservedParams);
        }

        protected List<ResvRegion> getStatImgIntersectRegions(double ltLng, double ltLat)
        {
            List<ResvRegion> regs = new List<ResvRegion>();
            GridUnitBase grid = new GridUnitBase(ltLng, ltLat);
            if (MainModel.MultiGeometrys
                && MainModel.SearchGeometrys.SelectedResvRegions != null
                && MainModel.SearchGeometrys.SelectedResvRegions.Count > 0)
            {
                foreach (ResvRegion resvRegion in MainModel.SearchGeometrys.SelectedResvRegions)
                {

                    if (resvRegion.GeoOp.CheckPointInRegion(grid.CenterLng, grid.CenterLat))
                    {
                        regs.Add(resvRegion);
                        break;
                    }
                }
            }
            else
            {
                if (MainModel.SearchGeometrys.GeoOp.Contains(grid.CenterLng, grid.CenterLat))
                {
                    regs.Add(MainModel.SearchGeometrys.RegionInfo);
                }
            }
            return regs;
        }

        protected List<ResvRegion> getEventInRegions(Model.Event evt)
        {
            List<ResvRegion> regs = new List<ResvRegion>();
            if (MainModel.MultiGeometrys
                && MainModel.SearchGeometrys.SelectedResvRegions != null
                && MainModel.SearchGeometrys.SelectedResvRegions.Count > 0)
            {
                foreach (ResvRegion resvRegion in MainModel.SearchGeometrys.SelectedResvRegions)
                {
                    if (resvRegion.GeoOp.CheckPointInRegion(evt.Longitude,evt.Latitude))
                    {
                        regs.Add(resvRegion);
                    }
                }
            }
            else
            {
                if (MainModel.SearchGeometrys.GeoOp.Contains(evt.Longitude, evt.Latitude))
                {
                    regs.Add(MainModel.SearchGeometrys.RegionInfo);
                }
            }
            return regs;
        }

    }

    class StationData
    {
        public string stationName;
        public double lon;
        public double lat;
        public override string ToString()
        {
            return this.stationName;
        }
    }
}
