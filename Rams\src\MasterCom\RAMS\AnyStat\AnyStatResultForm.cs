﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using System.Collections;
using MasterCom.RAMS.Util;
using MasterCom.Util;

namespace MasterCom.RAMS.AnyStat
{
    public partial class AnyStatResultForm : BaseFormStyle
    {
        ListViewManager listViewManager;
        public AnyStatResultForm()
        {
            InitializeComponent();
            listViewManager = new ListViewManager(this.treeListView);
        }
        public void FreshShowResult(AnyStatUnit statUnit)
        {
            treeListView.Columns.Clear();
            OLVColumn columnTreeKey = new OLVColumn("分类项","");
            columnTreeKey.AspectGetter = delegate(object x)
            {
                KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                return kpair.Key;
            };
            treeListView.Columns.Add(columnTreeKey);
#region 定义值列
            int columnVToAdd = statUnit.columnsDef.Count - statUnit.keyColumnCount;
            #region 值列1定义
            if (columnVToAdd>=1)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(0);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            #endregion
            #region 值列2到10
            if (columnVToAdd >= 2)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount+1];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(1);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 3)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 2];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(2);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 4)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 3];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(3);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 5)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 4];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(4);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 6)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 5];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(5);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 7)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 6];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(6);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 8)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 7];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(7);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 9)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 8];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(8);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 10)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 9];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(9);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            #endregion
#region 值列11到20
            if (columnVToAdd >= 11)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 10];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(10);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 12)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 11];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(11);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 13)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 12];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(12);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 14)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 13];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(13);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 15)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 14];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(14);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 16)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 15];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(15);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 17)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 16];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(16);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 18)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 17];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(17);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 19)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 18];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(18);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 20)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 19];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(19);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
#endregion
#region 值列21到30
            if (columnVToAdd >= 21)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 20];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(20);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 22)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 21];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(21);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 23)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 22];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(22);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 24)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 23];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(23);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 25)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 24];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(24);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 26)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 25];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(25);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 27)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 26];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(26);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 28)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 27];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(27);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 29)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 28];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(28);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 30)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 29];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(29);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
#endregion
#region 列31到40
            if (columnVToAdd >= 31)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 30];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(30);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 32)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 31];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(31);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 33)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 32];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(32);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 34)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 33];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(33);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            } 
            if (columnVToAdd >= 35)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 34];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(34);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            } 
            if (columnVToAdd >= 36)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 35];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(35);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            } 
            if (columnVToAdd >= 37)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 36];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(36);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            } 
            if (columnVToAdd >= 38)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 37];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(37);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            } 
            if (columnVToAdd >= 39)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 38];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(38);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            } 
            if (columnVToAdd >= 40)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 39];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(39);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
#endregion
#region 值列41到50
            if (columnVToAdd >= 41)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 40];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(40);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 42)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 41];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(41);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 43)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 42];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(42);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 44)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 43];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(43);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 45)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 44];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(44);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 46)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 45];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(45);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 47)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 46];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(46);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 48)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 47];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(47);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 49)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 48];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(48);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
            if (columnVToAdd >= 50)
            {
                AnyColumnDef columnDef = statUnit.columnsDef[statUnit.keyColumnCount + 49];
                OLVColumn columnValue = new OLVColumn(columnDef.name, "");
                columnValue.AspectGetter = delegate(object x)
                {
                    KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                    AnyResultKeyNode vnode = kpair.Value;
                    if (vnode != null)
                    {
                        return vnode.GetValueAt(49);
                    }
                    return 0;
                };
                columnValue.AspectToStringConverter = delegate(object value)
                {
                    return formatValueForColumn(value);
                };
                treeListView.Columns.Add(columnValue);
            }
#endregion
#endregion

            this.treeListView.CanExpandGetter = delegate(object x)
            {
                KeyValuePair<string,AnyResultKeyNode> kpair = (KeyValuePair<string,AnyResultKeyNode>)x;
                return kpair.Value.anyResultNodes.Count > 0;
            };
            this.treeListView.ChildrenGetter = delegate(object x)
            {
                KeyValuePair<string, AnyResultKeyNode> kpair = (KeyValuePair<string, AnyResultKeyNode>)x;
                return kpair.Value.anyResultNodes;
            };
            //start fill it
            this.treeListView.IsNeedShowOverlay = false;
            this.treeListView.SetObjects(statUnit.AnyResultNode);
        }

        private static string formatValueForColumn(object value)
        {
            if (value is DateTime)
                return ((DateTime)value).ToString("yyyy-MM-dd HH-mm-ss");
            else if (value == null)
                return "";
            else
                return value.ToString();
        }

        private void excep_tsmi_Click(object sender, EventArgs e)
        {
            MasterCom.Util.UiEx.WaitTextBox.Show(this, "正在导出表格数据...", DoExcel);
        }
        private void DoExcel()
        {
            try
            {
                
                listViewManager.DoExcel();
            }
            finally
            {
                MasterCom.Util.UiEx.WaitTextBox.Close();
            }
        }

        private void expand_tsmi_Click(object sender, EventArgs e)
        {
            treeListView.ExpandAll();
        }

        private void collapse_tsmi_Click(object sender, EventArgs e)
        {
            treeListView.CollapseAll();
        }
    }

}