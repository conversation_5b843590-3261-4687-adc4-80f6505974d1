﻿
namespace MasterCom.RAMS.ZTFunc
{
    public partial class NbIotMgrsWeakSinrResult : NbIotMgrsWeakRsrpResult
    {
        public NbIotMgrsWeakSinrResult()
        {
            InitializeComponent();
        }

        public override string Desc
        {
            get { return "连续质差"; }
        }

        public override void DrawOnLayer()
        {
            mf = MainModel.MainForm.GetMapForm();
            MTGis.LayerBase clayer = mf.GetTempLayerBase(typeof(ZTScanGridLayer));
            layer = clayer as ZTScanGridLayer;
            layer.GridInfos = scanGridInfoList;
            layer.SelectedGrid = null;
            layer.InitColor(ZTScanGridLayer.RenderingIndex.R0_CINR);
            layer.CurType = typeof(NbIotMgrsWeakSinrResult);
            MainModel.FireSetDefaultMapSerialTheme(layer.SerialInfoName);
            mf.updateMap();
            if (carrierDataList[0].AreaGridViews.Count > 0 && carrierDataList[0].AreaGridViews[0].SerialGridViews.Count > 0)
            {
                GotoSelectedViewGV(carrierDataList[0].AreaGridViews[0].SerialGridViews[0], 0);
            }
        }

        protected override void GetData()
        {
            NbIotMgrsWeakSinrStater stater = funcItem.Stater as NbIotMgrsWeakSinrStater;
            carrierDataList = stater.GetViews();
        }
    }
}
