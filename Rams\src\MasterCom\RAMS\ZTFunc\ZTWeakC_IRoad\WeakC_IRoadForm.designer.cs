﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class WeakC_IRoadForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(WeakC_IRoadForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.groupControl = new DevExpress.XtraEditors.GroupControl();
            this.btnFilter = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.edtTimeMax = new DevExpress.XtraEditors.SpinEdit();
            this.edtTimeMin = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.edtDistanceMax = new DevExpress.XtraEditors.SpinEdit();
            this.edtDistanceMin = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRoadDesc = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDistance = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnSecond = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTestPointCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRxLevMean = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRxLevMin = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRxLevMax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLong = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLat = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).BeginInit();
            this.groupControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edtTimeMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtTimeMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtDistanceMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtDistanceMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // groupControl
            // 
            this.groupControl.Controls.Add(this.btnFilter);
            this.groupControl.Controls.Add(this.labelControl4);
            this.groupControl.Controls.Add(this.edtTimeMax);
            this.groupControl.Controls.Add(this.edtTimeMin);
            this.groupControl.Controls.Add(this.labelControl6);
            this.groupControl.Controls.Add(this.labelControl3);
            this.groupControl.Controls.Add(this.edtDistanceMax);
            this.groupControl.Controls.Add(this.edtDistanceMin);
            this.groupControl.Controls.Add(this.labelControl1);
            this.groupControl.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl.Location = new System.Drawing.Point(0, 0);
            this.groupControl.Name = "groupControl";
            this.groupControl.Size = new System.Drawing.Size(1045, 60);
            this.groupControl.TabIndex = 3;
            this.groupControl.Text = "条件过滤";
            // 
            // btnFilter
            // 
            this.btnFilter.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnFilter.Location = new System.Drawing.Point(940, 29);
            this.btnFilter.Name = "btnFilter";
            this.btnFilter.Size = new System.Drawing.Size(75, 23);
            this.btnFilter.TabIndex = 10;
            this.btnFilter.Text = "过滤";
            this.btnFilter.Click += new System.EventHandler(this.btnFilter_Click);
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(512, 33);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(12, 14);
            this.labelControl4.TabIndex = 9;
            this.labelControl4.Text = "秒";
            // 
            // edtTimeMax
            // 
            this.edtTimeMax.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.edtTimeMax.Location = new System.Drawing.Point(444, 30);
            this.edtTimeMax.Name = "edtTimeMax";
            this.edtTimeMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtTimeMax.Properties.IsFloatValue = false;
            this.edtTimeMax.Properties.Mask.EditMask = "N00";
            this.edtTimeMax.Size = new System.Drawing.Size(62, 21);
            this.edtTimeMax.TabIndex = 7;
            // 
            // edtTimeMin
            // 
            this.edtTimeMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.edtTimeMin.Location = new System.Drawing.Point(296, 30);
            this.edtTimeMin.Name = "edtTimeMin";
            this.edtTimeMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtTimeMin.Properties.IsFloatValue = false;
            this.edtTimeMin.Properties.Mask.EditMask = "N00";
            this.edtTimeMin.Size = new System.Drawing.Size(62, 21);
            this.edtTimeMin.TabIndex = 6;
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(364, 33);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(74, 14);
            this.labelControl6.TabIndex = 5;
            this.labelControl6.Text = "< 持续时间 <";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(247, 33);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(12, 14);
            this.labelControl3.TabIndex = 4;
            this.labelControl3.Text = "米";
            // 
            // edtDistanceMax
            // 
            this.edtDistanceMax.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.edtDistanceMax.Location = new System.Drawing.Point(179, 30);
            this.edtDistanceMax.Name = "edtDistanceMax";
            this.edtDistanceMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtDistanceMax.Properties.IsFloatValue = false;
            this.edtDistanceMax.Properties.Mask.EditMask = "N00";
            this.edtDistanceMax.Size = new System.Drawing.Size(62, 21);
            this.edtDistanceMax.TabIndex = 2;
            // 
            // edtDistanceMin
            // 
            this.edtDistanceMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.edtDistanceMin.Location = new System.Drawing.Point(31, 30);
            this.edtDistanceMin.Name = "edtDistanceMin";
            this.edtDistanceMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtDistanceMin.Properties.IsFloatValue = false;
            this.edtDistanceMin.Properties.Mask.EditMask = "N00";
            this.edtDistanceMin.Size = new System.Drawing.Size(62, 21);
            this.edtDistanceMin.TabIndex = 1;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(99, 33);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(74, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "< 持续距离 <";
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.ctxMenu;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl.Location = new System.Drawing.Point(0, 60);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(1045, 442);
            this.gridControl.TabIndex = 4;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            this.gridControl.DoubleClick += new System.EventHandler(this.gridControl_DoubleClick);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnFileName,
            this.gridColumnRoadDesc,
            this.gridColumnDistance,
            this.gridColumnSecond,
            this.gridColumnTestPointCount,
            this.gridColumnRxLevMean,
            this.gridColumnRxLevMin,
            this.gridColumnRxLevMax,
            this.gridColumnLong,
            this.gridColumnLat});
            this.gridView.CustomizationFormBounds = new System.Drawing.Rectangle(888, 491, 216, 187);
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnFileName
            // 
            this.gridColumnFileName.Caption = "文件名";
            this.gridColumnFileName.FieldName = "FileName";
            this.gridColumnFileName.Name = "gridColumnFileName";
            this.gridColumnFileName.Visible = true;
            this.gridColumnFileName.VisibleIndex = 0;
            this.gridColumnFileName.Width = 198;
            // 
            // gridColumnRoadDesc
            // 
            this.gridColumnRoadDesc.Caption = "道路名称";
            this.gridColumnRoadDesc.FieldName = "RoadDesc";
            this.gridColumnRoadDesc.Name = "gridColumnRoadDesc";
            this.gridColumnRoadDesc.Visible = true;
            this.gridColumnRoadDesc.VisibleIndex = 1;
            this.gridColumnRoadDesc.Width = 138;
            // 
            // gridColumnDistance
            // 
            this.gridColumnDistance.Caption = "持续距离(米)";
            this.gridColumnDistance.FieldName = "Distance";
            this.gridColumnDistance.Name = "gridColumnDistance";
            this.gridColumnDistance.Visible = true;
            this.gridColumnDistance.VisibleIndex = 2;
            this.gridColumnDistance.Width = 107;
            // 
            // gridColumnSecond
            // 
            this.gridColumnSecond.Caption = "持续时间(秒)";
            this.gridColumnSecond.FieldName = "Second";
            this.gridColumnSecond.Name = "gridColumnSecond";
            this.gridColumnSecond.Visible = true;
            this.gridColumnSecond.VisibleIndex = 3;
            this.gridColumnSecond.Width = 102;
            // 
            // gridColumnTestPointCount
            // 
            this.gridColumnTestPointCount.Caption = "采样点数";
            this.gridColumnTestPointCount.FieldName = "TestPointCount";
            this.gridColumnTestPointCount.Name = "gridColumnTestPointCount";
            this.gridColumnTestPointCount.Visible = true;
            this.gridColumnTestPointCount.VisibleIndex = 4;
            // 
            // gridColumnRxLevMean
            // 
            this.gridColumnRxLevMean.Caption = "平均C/I";
            this.gridColumnRxLevMean.FieldName = "C_IMean";
            this.gridColumnRxLevMean.Name = "gridColumnRxLevMean";
            this.gridColumnRxLevMean.Visible = true;
            this.gridColumnRxLevMean.VisibleIndex = 5;
            // 
            // gridColumnRxLevMin
            // 
            this.gridColumnRxLevMin.Caption = "最小C/I";
            this.gridColumnRxLevMin.FieldName = "C_IMin";
            this.gridColumnRxLevMin.Name = "gridColumnRxLevMin";
            this.gridColumnRxLevMin.Visible = true;
            this.gridColumnRxLevMin.VisibleIndex = 6;
            // 
            // gridColumnRxLevMax
            // 
            this.gridColumnRxLevMax.Caption = "最大C/I";
            this.gridColumnRxLevMax.FieldName = "C_IMax";
            this.gridColumnRxLevMax.Name = "gridColumnRxLevMax";
            this.gridColumnRxLevMax.Visible = true;
            this.gridColumnRxLevMax.VisibleIndex = 7;
            // 
            // gridColumnLong
            // 
            this.gridColumnLong.Caption = "经度";
            this.gridColumnLong.FieldName = "LongitudeMid";
            this.gridColumnLong.Name = "gridColumnLong";
            this.gridColumnLong.Visible = true;
            this.gridColumnLong.VisibleIndex = 8;
            this.gridColumnLong.Width = 88;
            // 
            // gridColumnLat
            // 
            this.gridColumnLat.Caption = "纬度";
            this.gridColumnLat.FieldName = "LatitudeMid";
            this.gridColumnLat.Name = "gridColumnLat";
            this.gridColumnLat.Visible = true;
            this.gridColumnLat.VisibleIndex = 9;
            this.gridColumnLat.Width = 90;
            // 
            // WeakC_IRoadForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1045, 502);
            this.Controls.Add(this.gridControl);
            this.Controls.Add(this.groupControl);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "WeakC_IRoadForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "弱C/I路段";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).EndInit();
            this.groupControl.ResumeLayout(false);
            this.groupControl.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edtTimeMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtTimeMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtDistanceMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtDistanceMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private DevExpress.XtraEditors.GroupControl groupControl;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRoadDesc;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDistance;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSecond;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTestPointCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRxLevMean;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRxLevMin;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRxLevMax;
        private DevExpress.XtraEditors.SimpleButton btnFilter;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SpinEdit edtTimeMax;
        private DevExpress.XtraEditors.SpinEdit edtTimeMin;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit edtDistanceMax;
        private DevExpress.XtraEditors.SpinEdit edtDistanceMin;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLong;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLat;

    }
}