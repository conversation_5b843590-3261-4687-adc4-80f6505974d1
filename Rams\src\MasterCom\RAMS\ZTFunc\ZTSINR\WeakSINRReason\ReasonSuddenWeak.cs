﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public class ReasonSuddenWeak : ReasonBase
    {
        public ReasonSuddenWeak()
        {
            this.Name = "质量毛刺";
        }

        public override bool IsValid(Model.TestPoint tp, params object[] resvParams)
        {
            if (ZTWeakSINRReason.isSuddenWeak)
            {
                return true;
            }
            return false;
        }
    }
}
