using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func.PopShow
{
    public partial class CompetitionColorSettingBox : Form
    {
        public List<DTParameterRangeColor> colorRanges { get; set; }
        public CompetitionColorSettingBox()
        {
            InitializeComponent();
            colorRanges = new List<DTParameterRangeColor>();
        }

        public void FillData(List<DTParameterRangeColor> colorRanges)
        {
            this.colorRanges = colorRanges;
            rowCountChanged();
        }

        private void rowCountChanged()
        {
            dataGridView.RowCount = colorRanges.Count;
            foreach (DataGridViewRow row in dataGridView.Rows)
            {
                row.Height = 18;
            }
            dataGridView.Invalidate();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void dataGridView_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.ColumnIndex == 1)
            {
                e.CellStyle.BackColor = colorRanges[e.RowIndex].Value;
                e.CellStyle.SelectionBackColor = colorRanges[e.RowIndex].Value;
            }
        }

        private void dataGridView_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            ColorDialog dialog = new ColorDialog();
            dialog.Color = dataGridView.SelectedRows[0].Cells[1].Style.BackColor;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                colorRanges[dataGridView.SelectedRows[0].Index].Value = dialog.Color;
                dataGridView.Invalidate();
            }
        }

        private void dataGridView_CellValueNeeded(object sender, DataGridViewCellValueEventArgs e)
        {
            if (e.RowIndex > colorRanges.Count)
            {
                return;
            }
            if (e.ColumnIndex == 0)
            {
                e.Value = colorRanges[e.RowIndex].RangeDescriptionForPopShowGIS;
            }
        }
    }
}