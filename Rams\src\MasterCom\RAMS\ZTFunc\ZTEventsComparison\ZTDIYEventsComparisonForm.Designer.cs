﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTDIYEventsComparisonForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTDIYEventsComparisonForm));
            this.treeListView = new BrightIdeasSoftware.TreeListView();
            this.olvColumnReduplicateEventsCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCity = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFileName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFIleId = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnProj = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRoadName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnEventName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnEventId = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnOrd_id = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnReduplicateReason = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnReduplicateFileName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnReduplicateFileId = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnReduplicateProj = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnReduplicateRoadName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnReduplicateDate = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnReduplicateLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnReduplicateLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnReduplicateLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnReduplicateCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnReduplicateEventName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnReduplicateEventId = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miAllEventsNowExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.label1 = new System.Windows.Forms.Label();
            this.textBoxEventsNowCount = new System.Windows.Forms.TextBox();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.textBoxEventNowHasProblemPct = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.textBoxEventNowAllCount = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.treeListViewAllEventsNow = new BrightIdeasSoftware.TreeListView();
            this.olvCityName = new BrightIdeasSoftware.OLVColumn();
            this.olvFileName = new BrightIdeasSoftware.OLVColumn();
            this.olvRoadName = new BrightIdeasSoftware.OLVColumn();
            this.olvTime = new BrightIdeasSoftware.OLVColumn();
            this.olvLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvLac = new BrightIdeasSoftware.OLVColumn();
            this.olvCi = new BrightIdeasSoftware.OLVColumn();
            this.olvEventName = new BrightIdeasSoftware.OLVColumn();
            this.pageSetupDialog1 = new System.Windows.Forms.PageSetupDialog();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewAllEventsNow)).BeginInit();
            this.SuspendLayout();
            // 
            // treeListView
            // 
            this.treeListView.AllColumns.Add(this.olvColumnReduplicateEventsCount);
            this.treeListView.AllColumns.Add(this.olvColumnCity);
            this.treeListView.AllColumns.Add(this.olvColumnFileName);
            this.treeListView.AllColumns.Add(this.olvColumnFIleId);
            this.treeListView.AllColumns.Add(this.olvColumnProj);
            this.treeListView.AllColumns.Add(this.olvColumnRoadName);
            this.treeListView.AllColumns.Add(this.olvColumnDate);
            this.treeListView.AllColumns.Add(this.olvColumnLongitude);
            this.treeListView.AllColumns.Add(this.olvColumnLatitude);
            this.treeListView.AllColumns.Add(this.olvColumnLAC);
            this.treeListView.AllColumns.Add(this.olvColumnCI);
            this.treeListView.AllColumns.Add(this.olvColumnEventName);
            this.treeListView.AllColumns.Add(this.olvColumnEventId);
            this.treeListView.AllColumns.Add(this.olvColumnOrd_id);
            this.treeListView.AllColumns.Add(this.olvColumnReduplicateReason);
            this.treeListView.AllColumns.Add(this.olvColumnReduplicateFileName);
            this.treeListView.AllColumns.Add(this.olvColumnReduplicateFileId);
            this.treeListView.AllColumns.Add(this.olvColumnReduplicateProj);
            this.treeListView.AllColumns.Add(this.olvColumnReduplicateRoadName);
            this.treeListView.AllColumns.Add(this.olvColumnReduplicateDate);
            this.treeListView.AllColumns.Add(this.olvColumnReduplicateLongitude);
            this.treeListView.AllColumns.Add(this.olvColumnReduplicateLatitude);
            this.treeListView.AllColumns.Add(this.olvColumnReduplicateLAC);
            this.treeListView.AllColumns.Add(this.olvColumnReduplicateCI);
            this.treeListView.AllColumns.Add(this.olvColumnReduplicateEventName);
            this.treeListView.AllColumns.Add(this.olvColumnReduplicateEventId);
            this.treeListView.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.treeListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnReduplicateEventsCount,
            this.olvColumnCity,
            this.olvColumnFileName,
            this.olvColumnFIleId,
            this.olvColumnProj,
            this.olvColumnRoadName,
            this.olvColumnDate,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnEventName,
            this.olvColumnEventId,
            this.olvColumnOrd_id,
            this.olvColumnReduplicateReason,
            this.olvColumnReduplicateFileName,
            this.olvColumnReduplicateFileId,
            this.olvColumnReduplicateProj,
            this.olvColumnReduplicateRoadName,
            this.olvColumnReduplicateDate,
            this.olvColumnReduplicateLongitude,
            this.olvColumnReduplicateLatitude,
            this.olvColumnReduplicateLAC,
            this.olvColumnReduplicateCI,
            this.olvColumnReduplicateEventName,
            this.olvColumnReduplicateEventId});
            this.treeListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListView.FullRowSelect = true;
            this.treeListView.GridLines = true;
            this.treeListView.Location = new System.Drawing.Point(3, 30);
            this.treeListView.Name = "treeListView";
            this.treeListView.OwnerDraw = true;
            this.treeListView.ShowGroups = false;
            this.treeListView.ShowImagesOnSubItems = true;
            this.treeListView.Size = new System.Drawing.Size(988, 338);
            this.treeListView.TabIndex = 0;
            this.treeListView.UseCompatibleStateImageBehavior = false;
            this.treeListView.View = System.Windows.Forms.View.Details;
            this.treeListView.VirtualMode = true;
            // 
            // olvColumnReduplicateEventsCount
            // 
            this.olvColumnReduplicateEventsCount.HeaderFont = null;
            this.olvColumnReduplicateEventsCount.Text = "重复问题点个数";
            this.olvColumnReduplicateEventsCount.Width = 108;
            // 
            // olvColumnCity
            // 
            this.olvColumnCity.HeaderFont = null;
            this.olvColumnCity.Text = "地市";
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名";
            this.olvColumnFileName.Width = 100;
            // 
            // olvColumnFIleId
            // 
            this.olvColumnFIleId.HeaderFont = null;
            this.olvColumnFIleId.Text = "文件ID";
            this.olvColumnFIleId.Width = 80;
            // 
            // olvColumnProj
            // 
            this.olvColumnProj.HeaderFont = null;
            this.olvColumnProj.Text = "项目类型";
            // 
            // olvColumnRoadName
            // 
            this.olvColumnRoadName.HeaderFont = null;
            this.olvColumnRoadName.Text = "道路名称";
            // 
            // olvColumnDate
            // 
            this.olvColumnDate.HeaderFont = null;
            this.olvColumnDate.Text = "时间";
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // olvColumnEventName
            // 
            this.olvColumnEventName.HeaderFont = null;
            this.olvColumnEventName.Text = "事件类型";
            // 
            // olvColumnEventId
            // 
            this.olvColumnEventId.HeaderFont = null;
            this.olvColumnEventId.Text = "事件ID";
            // 
            // olvColumnOrd_id
            // 
            this.olvColumnOrd_id.HeaderFont = null;
            this.olvColumnOrd_id.Text = "工单号";
            this.olvColumnOrd_id.Width = 150;
            // 
            // olvColumnReduplicateReason
            // 
            this.olvColumnReduplicateReason.HeaderFont = null;
            this.olvColumnReduplicateReason.Text = "重复问题点原因";
            this.olvColumnReduplicateReason.Width = 100;
            // 
            // olvColumnReduplicateFileName
            // 
            this.olvColumnReduplicateFileName.HeaderFont = null;
            this.olvColumnReduplicateFileName.Text = "重复文件名";
            this.olvColumnReduplicateFileName.Width = 100;
            // 
            // olvColumnReduplicateFileId
            // 
            this.olvColumnReduplicateFileId.HeaderFont = null;
            this.olvColumnReduplicateFileId.Text = "重复文件ID";
            this.olvColumnReduplicateFileId.Width = 80;
            // 
            // olvColumnReduplicateProj
            // 
            this.olvColumnReduplicateProj.HeaderFont = null;
            this.olvColumnReduplicateProj.Text = "重复项目类型";
            // 
            // olvColumnReduplicateRoadName
            // 
            this.olvColumnReduplicateRoadName.HeaderFont = null;
            this.olvColumnReduplicateRoadName.Text = "重复道路名称";
            // 
            // olvColumnReduplicateDate
            // 
            this.olvColumnReduplicateDate.HeaderFont = null;
            this.olvColumnReduplicateDate.Text = "重复时间";
            // 
            // olvColumnReduplicateLongitude
            // 
            this.olvColumnReduplicateLongitude.HeaderFont = null;
            this.olvColumnReduplicateLongitude.Text = "重复经度";
            // 
            // olvColumnReduplicateLatitude
            // 
            this.olvColumnReduplicateLatitude.HeaderFont = null;
            this.olvColumnReduplicateLatitude.Text = "重复纬度";
            // 
            // olvColumnReduplicateLAC
            // 
            this.olvColumnReduplicateLAC.HeaderFont = null;
            this.olvColumnReduplicateLAC.Text = "重复LAC";
            // 
            // olvColumnReduplicateCI
            // 
            this.olvColumnReduplicateCI.HeaderFont = null;
            this.olvColumnReduplicateCI.Text = "重复CI";
            // 
            // olvColumnReduplicateEventName
            // 
            this.olvColumnReduplicateEventName.HeaderFont = null;
            this.olvColumnReduplicateEventName.Text = "重复事件类型";
            this.olvColumnReduplicateEventName.Width = 100;
            // 
            // olvColumnReduplicateEventId
            // 
            this.olvColumnReduplicateEventId.HeaderFont = null;
            this.olvColumnReduplicateEventId.Text = "重复事件ID";
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripSeparator1,
            this.miExpandAll,
            this.miCollapseAll,
            this.miExportExcel,
            this.miAllEventsNowExportExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(226, 98);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(222, 6);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(225, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(225, 22);
            this.miCollapseAll.Text = "全部叠合";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(225, 22);
            this.miExportExcel.Text = "重复问题点导出表到Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // miAllEventsNowExportExcel
            // 
            this.miAllEventsNowExportExcel.Name = "miAllEventsNowExportExcel";
            this.miAllEventsNowExportExcel.Size = new System.Drawing.Size(225, 22);
            this.miAllEventsNowExportExcel.Text = "所有当前事件导出表到Excel";
            this.miAllEventsNowExportExcel.Click += new System.EventHandler(this.miAllEventsNowExportExcel_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label1.Location = new System.Drawing.Point(728, 6);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(133, 14);
            this.label1.TabIndex = 1;
            this.label1.Text = "当前重复问题点个数";
            // 
            // textBoxEventsNowCount
            // 
            this.textBoxEventsNowCount.Location = new System.Drawing.Point(863, 3);
            this.textBoxEventsNowCount.Name = "textBoxEventsNowCount";
            this.textBoxEventsNowCount.ReadOnly = true;
            this.textBoxEventsNowCount.Size = new System.Drawing.Size(125, 21);
            this.textBoxEventsNowCount.TabIndex = 2;
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(1002, 397);
            this.tabControl1.TabIndex = 3;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.textBoxEventNowHasProblemPct);
            this.tabPage1.Controls.Add(this.label2);
            this.tabPage1.Controls.Add(this.treeListView);
            this.tabPage1.Controls.Add(this.textBoxEventsNowCount);
            this.tabPage1.Controls.Add(this.label1);
            this.tabPage1.Location = new System.Drawing.Point(4, 22);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(994, 371);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "重复问题点";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // textBoxEventNowHasProblemPct
            // 
            this.textBoxEventNowHasProblemPct.Location = new System.Drawing.Point(590, 3);
            this.textBoxEventNowHasProblemPct.Name = "textBoxEventNowHasProblemPct";
            this.textBoxEventNowHasProblemPct.ReadOnly = true;
            this.textBoxEventNowHasProblemPct.Size = new System.Drawing.Size(100, 21);
            this.textBoxEventNowHasProblemPct.TabIndex = 4;
            this.textBoxEventNowHasProblemPct.Text = "0";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label2.Location = new System.Drawing.Point(401, 6);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(189, 14);
            this.label2.TabIndex = 3;
            this.label2.Text = "当前事件存在重复问题点比例";
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.textBoxEventNowAllCount);
            this.tabPage2.Controls.Add(this.label3);
            this.tabPage2.Controls.Add(this.treeListViewAllEventsNow);
            this.tabPage2.Location = new System.Drawing.Point(4, 22);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(994, 371);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "当前事件";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // textBoxEventNowAllCount
            // 
            this.textBoxEventNowAllCount.Location = new System.Drawing.Point(886, 4);
            this.textBoxEventNowAllCount.Name = "textBoxEventNowAllCount";
            this.textBoxEventNowAllCount.ReadOnly = true;
            this.textBoxEventNowAllCount.Size = new System.Drawing.Size(100, 21);
            this.textBoxEventNowAllCount.TabIndex = 2;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("宋体", 10.5F);
            this.label3.Location = new System.Drawing.Point(792, 7);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(91, 14);
            this.label3.TabIndex = 1;
            this.label3.Text = "当前事件个数";
            // 
            // treeListViewAllEventsNow
            // 
            this.treeListViewAllEventsNow.AllColumns.Add(this.olvCityName);
            this.treeListViewAllEventsNow.AllColumns.Add(this.olvFileName);
            this.treeListViewAllEventsNow.AllColumns.Add(this.olvRoadName);
            this.treeListViewAllEventsNow.AllColumns.Add(this.olvTime);
            this.treeListViewAllEventsNow.AllColumns.Add(this.olvLongitude);
            this.treeListViewAllEventsNow.AllColumns.Add(this.olvLatitude);
            this.treeListViewAllEventsNow.AllColumns.Add(this.olvLac);
            this.treeListViewAllEventsNow.AllColumns.Add(this.olvCi);
            this.treeListViewAllEventsNow.AllColumns.Add(this.olvEventName);
            this.treeListViewAllEventsNow.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.treeListViewAllEventsNow.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvCityName,
            this.olvFileName,
            this.olvRoadName,
            this.olvTime,
            this.olvLongitude,
            this.olvLatitude,
            this.olvLac,
            this.olvCi,
            this.olvEventName});
            this.treeListViewAllEventsNow.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListViewAllEventsNow.FullRowSelect = true;
            this.treeListViewAllEventsNow.GridLines = true;
            this.treeListViewAllEventsNow.Location = new System.Drawing.Point(3, 30);
            this.treeListViewAllEventsNow.Name = "treeListViewAllEventsNow";
            this.treeListViewAllEventsNow.OwnerDraw = true;
            this.treeListViewAllEventsNow.ShowGroups = false;
            this.treeListViewAllEventsNow.Size = new System.Drawing.Size(988, 365);
            this.treeListViewAllEventsNow.TabIndex = 0;
            this.treeListViewAllEventsNow.UseCompatibleStateImageBehavior = false;
            this.treeListViewAllEventsNow.View = System.Windows.Forms.View.Details;
            this.treeListViewAllEventsNow.VirtualMode = true;
            // 
            // olvCityName
            // 
            this.olvCityName.HeaderFont = null;
            this.olvCityName.Text = "地市";
            // 
            // olvFileName
            // 
            this.olvFileName.HeaderFont = null;
            this.olvFileName.Text = "文件名";
            this.olvFileName.Width = 150;
            // 
            // olvRoadName
            // 
            this.olvRoadName.HeaderFont = null;
            this.olvRoadName.Text = "道路名称";
            this.olvRoadName.Width = 150;
            // 
            // olvTime
            // 
            this.olvTime.HeaderFont = null;
            this.olvTime.Text = "时间";
            this.olvTime.Width = 100;
            // 
            // olvLongitude
            // 
            this.olvLongitude.HeaderFont = null;
            this.olvLongitude.Text = "经度";
            this.olvLongitude.Width = 100;
            // 
            // olvLatitude
            // 
            this.olvLatitude.HeaderFont = null;
            this.olvLatitude.Text = "纬度";
            this.olvLatitude.Width = 100;
            // 
            // olvLac
            // 
            this.olvLac.HeaderFont = null;
            this.olvLac.Text = "LAC";
            // 
            // olvCi
            // 
            this.olvCi.HeaderFont = null;
            this.olvCi.Text = "CI";
            // 
            // olvEventName
            // 
            this.olvEventName.HeaderFont = null;
            this.olvEventName.Text = "事件类型";
            this.olvEventName.Width = 150;
            // 
            // ZTDIYEventsComparisonForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.SystemColors.ButtonHighlight;
            this.ClientSize = new System.Drawing.Size(1002, 397);
            this.ContextMenuStrip = this.contextMenuStrip;
            this.Controls.Add(this.tabControl1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTDIYEventsComparisonForm";
            this.Text = "重复问题点结果";
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.tabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tabPage1.PerformLayout();
            this.tabPage2.ResumeLayout(false);
            this.tabPage2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewAllEventsNow)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeListView;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnDate;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnEventName;
        private BrightIdeasSoftware.OLVColumn olvColumnReduplicateReason;
        private BrightIdeasSoftware.OLVColumn olvColumnReduplicateFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnReduplicateDate;
        private BrightIdeasSoftware.OLVColumn olvColumnReduplicateLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnReduplicateLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnReduplicateLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnReduplicateCI;
        private BrightIdeasSoftware.OLVColumn olvColumnReduplicateEventName;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private BrightIdeasSoftware.OLVColumn olvColumnReduplicateEventsCount;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox textBoxEventsNowCount;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadName;
        private BrightIdeasSoftware.OLVColumn olvColumnReduplicateRoadName;
        private BrightIdeasSoftware.OLVColumn olvColumnCity;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private BrightIdeasSoftware.TreeListView treeListViewAllEventsNow;
        private BrightIdeasSoftware.OLVColumn olvCityName;
        private BrightIdeasSoftware.OLVColumn olvFileName;
        private BrightIdeasSoftware.OLVColumn olvLongitude;
        private BrightIdeasSoftware.OLVColumn olvLatitude;
        private BrightIdeasSoftware.OLVColumn olvLac;
        private BrightIdeasSoftware.OLVColumn olvCi;
        private BrightIdeasSoftware.OLVColumn olvEventName;
        private BrightIdeasSoftware.OLVColumn olvRoadName;
        private BrightIdeasSoftware.OLVColumn olvTime;
        private System.Windows.Forms.ToolStripMenuItem miAllEventsNowExportExcel;
        private System.Windows.Forms.TextBox textBoxEventNowHasProblemPct;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox textBoxEventNowAllCount;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.PageSetupDialog pageSetupDialog1;
        private BrightIdeasSoftware.OLVColumn olvColumnOrd_id;
        private BrightIdeasSoftware.OLVColumn olvColumnFIleId;
        private BrightIdeasSoftware.OLVColumn olvColumnEventId;
        private BrightIdeasSoftware.OLVColumn olvColumnReduplicateFileId;
        private BrightIdeasSoftware.OLVColumn olvColumnReduplicateEventId;
        private BrightIdeasSoftware.OLVColumn olvColumnProj;
        private BrightIdeasSoftware.OLVColumn olvColumnReduplicateProj;
    }
}