﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MapWinGIS;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryCellInfoOneKey : QueryBase
    {
        public QueryCellInfoOneKey(MainModel mainModel)
            : base(mainModel)
        { 
        }

        public override string Name
        {
            get { return "一键更新工参"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20046, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            int iCurCity = mainModel.DistrictID;
            foreach (int iDistrictID in condition.DistrictIDs)
            {
                mainModel.DistrictID = iDistrictID;
                mainModel.ModelConfig.Init(true);
            }
            mainModel.DistrictID = iCurCity;
        }
    }
}
