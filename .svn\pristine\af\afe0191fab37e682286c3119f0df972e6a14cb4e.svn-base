﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.NOP.Stat
{
    public class ColumnRender
    {
        public ColumnRender()
        {
            RangeColorSet = new List<DTParameterRangeColor>();
            DTParameterRangeColor rng = new DTParameterRangeColor(0, 10, Color.FromArgb(92,184,92));
            RangeColorSet.Add(rng);
            rng = new DTParameterRangeColor(10, 20, Color.LawnGreen);
            RangeColorSet.Add(rng);
            rng = new DTParameterRangeColor(20, 30, Color.Yellow);
            RangeColorSet.Add(rng);
            rng = new DTParameterRangeColor(30, 50, Color.Orange);
            RangeColorSet.Add(rng);
            rng = new DTParameterRangeColor(50, 9999, Color.Red);
            rng.MaxIncluded = true;
            RangeColorSet.Add(rng);
        }

        public ColumnRender(string caption)
            : this()
        {
            this.Caption = caption;
        }

        public override string ToString()
        {
            return Caption;
        }

        public string Caption
        {
            get;
            set;
        }

        public string Expression
        {
            get;
            set;
        }

        public List<DTParameterRangeColor> RangeColorSet
        {
            get;
            set;
        }

        public float ValueMin
        {
            get;
            set;
        }

        public float ValueMax
        {
            get;
            set;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic["Caption"] = Caption;
                dic["Expression"] = Expression;
                dic["ValueMin"] = ValueMin;
                dic["ValueMax"] = ValueMax;
                List<object> colorParam = new List<object>();
                foreach (DTParameterRangeColor item in RangeColorSet)
                {
                    colorParam.Add(item.Params);
                }
                dic["ColorParam"] = colorParam;
                return dic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                Caption = value["Caption"] as string;
                Expression = value["Expression"] as string;
                ValueMin = (float)value["ValueMin"];
                ValueMax = (float)value["ValueMax"];
                List<object> colorParam = value["ColorParam"] as List<object>;
                RangeColorSet = new List<DTParameterRangeColor>();
                foreach (Dictionary<string,object> param in colorParam)
                {
                    DTParameterRangeColor rangeColor = new DTParameterRangeColor();
                    rangeColor.Params = param;
                    RangeColorSet.Add(rangeColor);
                }

            }
        }

    }
}
