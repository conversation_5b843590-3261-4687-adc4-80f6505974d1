﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.Windows.Forms;
using System.Drawing;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteMgrsCoverAllDetail : ZTLteMgrsCoveDetail
    {
        public ZTLteMgrsCoverAllDetail(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "LTE军事栅格分析(路测及扫频)"; }
        }
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_SINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_NCell_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_NCell_SINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_NCell_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_NCell_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LTE_MGRS_GRID");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }
    }
    public class ZTLteMgrsCoveDetail : DIYSampleByRegion
    {
        public ZTLteMgrsCoveDetail(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        private readonly ServiceName serviceName = ServiceName.LTE;
        public ZTLteMgrsCoveDetail(ServiceName serviceName, MainModel mainModel)
            : this(mainModel)
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
            this.serviceName = serviceName;
        }

        public override string Name
        {
            get { return "LTE军事栅格扫频分析"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23022, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LTE_MGRS_GRID");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override bool isValidCondition()
        {

            if (condForm == null)
            {
                condForm = new ZTLteMgrsCoveSetDlg(serviceName);
            }
            condForm.SetCondition(LTEMgrsCondition);
            return condForm.ShowDialog() == DialogResult.OK;
        }

        ZTAntennaBase antBase = null;
        private ZTLteMgrsCoveSetDlg condForm = null;
        public LTEMgrsCondition LTEMgrsCondition { get; set; }
        private static TwoEarfcnGetter getter { get; set; }
        public Dictionary<string, LTEMgrsGridInfo> dicResult { get; set; }
        protected override void query()
        {
            LteMgrsGrid.FileType = "";
            //地图匹配处理
            antBase = new ZTAntennaBase(MainModel);
            antBase.InitRegionMop2();

            LTEMgrsCondition = condForm.Condition;
            dicResult = new Dictionary<string, LTEMgrsGridInfo>();
            getter = new TwoEarfcnGetter();
            LTEMgrsCondition.cityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            base.query();
            WaitBox.Show("正在统计栅格小区信息...", doWithMgrsGrid);
            fireShowForm();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            string mgrsString = MgrsGridConverter.GetMgrsString(tp.Longitude, tp.Latitude, LteMgrsGrid.SGridSize);
            string strGrid = "";
            if (antBase != null)
            {
                antBase.isContainPoint(tp.Longitude, tp.Latitude, ref strGrid);
            }
            if (!dicResult.ContainsKey(mgrsString))
            {
                LTEMgrsGridInfo grid = new LTEMgrsGridInfo(mgrsString);
                grid.strGrid = strGrid;
                if (grid.CentLng > 180 || grid.CentLat > 90) // 转换库有问题
                {
                    return;
                }
                dicResult.Add(mgrsString, grid);
            }
            dicResult[mgrsString].DoWithTestPoint(tp);
        }

        private void doWithMgrsGrid()
        {
            int iSum = dicResult.Count;
            int idx = 0;
            double iProgress = 0;
            double iLastProgress = 0;
            foreach (string key in dicResult.Keys)
            {
                idx++;
                iProgress = Math.Round(idx * 1.0 / iSum * 100, 0);
                if (iProgress % 2 == 0 && iProgress != 0 && iLastProgress != iProgress)
                {
                    iLastProgress = iProgress;
                    WaitBox.ProgressPercent = (int)iProgress;
                    WaitBox.Text = "正在统计栅格小区信息..." + iProgress + "%";
                }
                dicResult[key].iCoverage = LteMgrsFunc.doWithAllCell(dicResult[key], LTEMgrsCondition, getter);
                dicResult[key].clearData();
            }
            WaitBox.Close();
        }
        private void fireShowForm()
        {
            ZTLteMgrsCoveForm showForm = new ZTLteMgrsCoveForm(MainModel);
            showForm.iniData(dicResult, LTEMgrsCondition);
            showForm.Show();
        }
    }
    /*
     * 军事栅格号 编号规则：  MgrsGridConverter
     * 
     * 多层网判断算法 ：TwoEarfcnGetter 
     * 
     *  判断频点：LTECell.GetBandTypeByEarfcn(Earfcn)
     *            LTECell.GetBandTypeByEarfcn_BJ(Earfcn)
     *  频点枚举：LTEBandType
     */
    public class LTEMgrsGridInfo : LteMgrsGrid
    {
        public Color GridColor
        {
            get;
            set;
        }

        private string StrGrid;
        public string strGrid
        {
            get
            {
                if (StrGrid == "")
                    return "未匹配到网格";
                return StrGrid;
            }
            set
            {
                this.StrGrid = value;
            }
        }
        /// <summary>
        /// 重叠度
        /// </summary>
        public int iCoverage { get; set; }

        /// <summary>
        /// 重叠小区
        /// </summary>
        public string strCoverageCell
        {
            get
            {
                StringBuilder strCell = new StringBuilder();
                if (this.CoverageCellList == null)
                    return strCell.ToString();
                if (this.CoverageCellList.Count == 0)
                    return "无效栅格";
                for (int i = 0; i < CoverageCellList.Count; i++)
                {
                    strCell.Append(i > 0 ? ";" + CoverageCellList[i].CellName : CoverageCellList[i].CellName);
                }
                return strCell.ToString();
            }
        }

        /// <summary>
        /// 指定频段剔除、按频点排序后的小区集
        /// </summary>
        public List<LteMgrsCell> newCellList
        {
            get;
            set;
        }

        /// <summary>
        /// 重叠小区集
        /// </summary>
        public List<LteMgrsCell> CoverageCellList { get; set; }

        /// <summary>
        /// 栅格平均RSRP
        /// </summary>
        public float? gridAvgRSRP
        {
            get
            {
                if (newCellList.Count == 0)
                    return null;
                double sumRSRP = 0;
                int sumSampleCount = 0;
                foreach (LteMgrsCell cell in newCellList)
                {
                    sumRSRP += (double)cell.AvgRsrp * cell.SampleCount;
                    sumSampleCount += cell.SampleCount;
                }
                if (sumSampleCount == 0)
                    return null;
                return (float)(Math.Round(sumRSRP / sumSampleCount, 2));
            }
        }

        public float? gridAvgSINR
        {
            get
            {
                if (newCellList.Count == 0)
                    return null;
                double sumSINR = 0;
                int sumSampleCount = 0;
                foreach (LteMgrsCell cell in newCellList)
                {
                    sumSINR += (double)cell.AvgSinr * cell.SinrSampleCount;
                    sumSampleCount += cell.SinrSampleCount;
                }

                if (sumSampleCount == 0)
                    return null;
                return (float)(Math.Round(sumSINR / sumSampleCount, 2));
            }
        }

        public LTEMgrsGridInfo(string mgrsString)
            : base(mgrsString)
        {
        }

        public new void DoWithTestPoint(TestPoint tp)
        {
            SampleList.Add(new LteMgrsTestPoint(tp, MgrsString));
        }
        public void clearData()
        {
            this.FreqList.Clear();
            this.FreqDic.Clear();
            this.CellList.Clear();
            this.SampleList.Clear();
        }
    }

    public class LteMgrsFunc
    {
        protected LteMgrsFunc()
        {

        }

        private static LTEMgrsCondition cond = null;
        private static int iCoverage = 0;
        private static TwoEarfcnGetter getter = null;
        public static int doWithAllCell(LTEMgrsGridInfo grid, LTEMgrsCondition Condition, TwoEarfcnGetter twoEarfcnGetter)
        {
            iCoverage = 0;
            cond = Condition;
            getter = twoEarfcnGetter;
            try
            {
                removeMoreCellbyCondition(grid);
            }
            catch
            {
                return 0;
            }
            return iCoverage;
        }

        /// <summary>
        /// 多层网剔除
        /// </summary>
        private static void removeMoreCellbyCondition(LTEMgrsGridInfo grid)
        {
            List<LteMgrsCell> CellList = FilterCellByBand(cond, grid.CellList);
            grid.newCellList = CellList;
            List<LteMgrsCell> coverageCell = new List<LteMgrsCell>();
            if (!cond.CheckTwoEarfcn)//
            {
                grid.CoverageCellList = removeCellbyCondition(CellList);
                return;
            }
            LteMgrsCell cellMax = null;
            if (CellList.Count == 0 || CellList[0].AvgRsrp < (decimal)cond.MinRsrp)
                return;
            else
                cellMax = CellList[0];

            foreach (LteMgrsCell cell in CellList)
            {
                addValidCoverageCell(coverageCell, ref cellMax, cell);
            }
            grid.CoverageCellList = coverageCell;
        }

        private static void addValidCoverageCell(List<LteMgrsCell> coverageCell, ref LteMgrsCell cellMax, LteMgrsCell cell)
        {
            bool isEqual = cell.BandJT == LTEBandTypeJT.A
                || cell.BandJT == LTEBandTypeJT.E
                || cell.BandJT == LTEBandTypeJT.Undefined;
            if (!isEqual)
            {
                if (cellMax == null)
                {
                    cellMax = cell;
                }
                if (!getter.NameCellLst.ContainsKey(cell.CellName) || cell.Earfcn == cellMax.Earfcn)
                {
                    decimal cmpValue = cond.EnableOptional
                        ? Math.Max(cellMax.AvgRsrp - (decimal)cond.DiffRsrp, (decimal)cond.OptionalRsrp)
                        : cellMax.AvgRsrp - (decimal)cond.DiffRsrp;
                    if (cell.AvgRsrp >= cmpValue)
                    {
                        ++iCoverage;
                        coverageCell.Add(cell);
                    }
                }
            }
        }

        /// <summary>
        /// 非多层网剔除
        /// </summary>
        private static List<LteMgrsCell> removeCellbyCondition(List<LteMgrsCell> cellList)
        {
            List<LteMgrsCell> coverageCell = new List<LteMgrsCell>();
            for (int i = 0; i < cellList.Count; ++i)
            {
                LteMgrsCell cell = cellList[i];

                if (i == 0 && cell.AvgRsrp < (decimal)cond.MinRsrp)
                {
                    break;
                }
                decimal cmpValue = cond.EnableOptional
                    ? Math.Max(cellList[0].AvgRsrp - (decimal)cond.DiffRsrp, (decimal)cond.OptionalRsrp)
                    : cellList[0].AvgRsrp - (decimal)cond.DiffRsrp;
                if (cell.AvgRsrp >= cmpValue)
                {
                    ++iCoverage;
                    coverageCell.Add(cell);
                }
            }
            return coverageCell;
        }

        /// <summary>
        /// 剔除指定频段
        /// </summary>
        private static List<LteMgrsCell> FilterCellByBand(LTEMgrsCondition cond, List<LteMgrsCell> CellList)
        {
            if (cond.IsNotBand)//不分段
            {
                return new List<LteMgrsCell>(CellList);
            }

            List<LteMgrsCell> retList = new List<LteMgrsCell>();
            for (int i = 0; i < CellList.Count; ++i)
            {
                addRetList(cond, CellList, retList, i);
            }
            return retList;
        }

        private static void addRetList(LTEMgrsCondition cond, List<LteMgrsCell> CellList, List<LteMgrsCell> retList, int i)
        {
            LteMgrsCell freq = CellList[i];
            int earfcn = freq.Earfcn;
            LTEBandType freqStrongType = LTECell.GetBandTypeByEarfcn(CellList[0].Earfcn);//最强归属段
            LTEBandType freqBandType = LTECell.GetBandTypeByEarfcn(earfcn);//得到小区所在的频段

            if (cond.IsStrongRange)//且最强归属段
            {
                if ((freqBandType == freqStrongType) && (freqBandType == LTEBandType.D || freqBandType == LTEBandType.F))
                {
                    retList.Add(freq);
                }
            }//不是最强归属段
            else if (FreqPoint.ValidCellBandType(cond.ListFreqPoint, earfcn))
            {
                retList.Add(freq);
            }
        }
    }


    /// <summary>
    /// 查询条件
    /// </summary>
    public class LTEMgrsCondition : LteMgrsCoverageCondition
    {
        //原有频段
        public LteMgrsCoverageFullBandType LteBandType { get; set; }

        /// <summary>
        ///运营商 0：移动 1：电信 2：联通
        /// </summary>
        public int CarrierId { get; set; }

        /// <summary>
        /// 频点集合
        /// </summary>
        public List<FreqPoint> ListFreqPoint { get; set; }

        /// <summary>
        /// 不分段
        /// </summary>
        public bool IsNotBand { get; set; }

        /// <summary>
        /// 最强归属段
        /// </summary>
        public bool IsStrongRange { get; set; }

        /// <summary>
        /// 按频点分段
        /// </summary>
        public bool IsFreqBand { get; set; }
        public string cityName { get; set; }
    }

    /// <summary>
    /// 频段枚举
    /// </summary>
    public enum LteMgrsCoverageFullBandType
    {
        [EnumDescriptionAttribute("D频段")]
        SingleD,

        [EnumDescriptionAttribute("D_37900频段")]
        SingleD_37900,

        [EnumDescriptionAttribute("D_38098频段")]
        SingleD_38098,

        [EnumDescriptionAttribute("D_38100频段")]
        SingleD_38100,

        [EnumDescriptionAttribute("F频段")]
        SingleF,

        [EnumDescriptionAttribute("最强归属段")]
        Top,

        [EnumDescriptionAttribute("不分段")]
        All,

        [EnumDescriptionAttribute("TDD")]
        TDD,

        [EnumDescriptionAttribute("FDD")]
        FDD,
    }
}
