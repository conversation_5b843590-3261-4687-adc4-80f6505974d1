using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LeakOutAsNCellDlg : BaseDialog
    {
        public LeakOutAsNCellDlg(bool isNeedGSM)
        {
            InitializeComponent();
            numRxLevDValue.Enabled = numRxLev.Enabled = chkNBCell.Checked;
            if (!isNeedGSM)
            {
                chkGSMCell.Visible = false;
                chkGetRoadDesc.Location = new Point(30, 130);
                label5.Location = new Point(147, 132);
            }

        }

        public int RxLevThreshold
        {
            get { return (int)numRxLev.Value; }
        }

        public int RxLevDValue
        {
            get { return (int)numRxLevDValue.Value; }
        }

        public bool LeakOutAsMainCell
        {
            get { return chkMainCell.Checked; }
        }

        public bool LeakOutAsNBCell
        {
            get { return chkNBCell.Checked; }
        }

        public bool getRoadDesc
        {
            get { return chkGetRoadDesc.Checked; }
        }

        public bool IsGetGSMCell
        {
            get { return chkGSMCell.Checked; }
        }

        private void chkNBCell_CheckedChanged(object sender, EventArgs e)
        {
            numRxLevDValue.Enabled = numRxLev.Enabled = chkNBCell.Checked;
        }
    }
}