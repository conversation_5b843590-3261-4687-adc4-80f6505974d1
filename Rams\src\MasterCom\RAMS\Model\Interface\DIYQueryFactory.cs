﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.Net
{
    public enum EAnaType { AnaByFile, AnaByRegion, FDDAnaByFile, FDDAnaByRegion }

    public static class DIYQueryFactory
    {
        public static QueryBase SwitchQuery(EAnaType eType)
        {
            QueryBase query = null;
            switch (eType)
            {
                case EAnaType.AnaByFile:
                    query = new DIYReplayFileQueryByCustom(MainModel.GetInstance());
                    break;
                case EAnaType.AnaByRegion:
                    query = new DIYSampleByRegion2(MainModel.GetInstance());
                    break;
                case EAnaType.FDDAnaByFile:
                    query = new DIYReplayFileQueryByCustom(MainModel.GetInstance());
                    break;
                case EAnaType.FDDAnaByRegion:
                    query = new DIYSampleByRegionFDD(MainModel.GetInstance());
                    break;
                default:
                    break;
            }
            return query;
        }
    }

    public class DIYSampleByRegion2 : DIYSampleByRegion
    {
        public DIYSampleByRegion2(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        protected override MasterCom.RAMS.Model.Interface.DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup sampleGroup = new DIYSampleGroup();
            sampleGroup.ThemeName = "---";
            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("lte_EARFCN");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_PCI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_RSRP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_TAC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_ECI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_NCell_RSRP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_NCell_EARFCN");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_NCell_PCI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGroup.ColumnsDefSet.Add(pDef);
            }

            return sampleGroup;
        }
    }

    public class DIYSampleByRegionFDD : DIYSampleByRegion
    {
        public DIYSampleByRegionFDD(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            for (int i = 0; i < 6; i++)
            {
                param = new Dictionary<string, object>();
                param["param_name"] = "lte_fdd_NCell_RSRP";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "lte_fdd_NCell_EARFCN";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "lte_fdd_NCell_PCI";
                param["param_arg"] = i;
                columnsDef.Add((object)param);
            }

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"lte_fdd_RSRP");
            tmpDic.Add("themeName", (object)"lte_fdd_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);
            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }
    }

    public class DIYQueryFileOrRegion : QueryBase
    {
        private readonly QueryBase queryBase = null;

        public DIYQueryFileOrRegion(MainModel mainModel, EAnaType eType)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            queryBase = DIYQueryFactory.SwitchQuery(eType);
            queryBase.DoWithDTDataEvent += DoWithDTData;
        }

        public override string Name
        {
            get { return ""; }
        }

        public override string IconName
        {
            get { return ""; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return queryBase.needSearchType();
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return queryBase.CanEnabled(searchGeometrys);
        }

        protected override void query()
        {
            DoSomethingBeforeQuery();
            queryBase.SetQueryCondition(this.Condition);
            setReplayContent();
            queryBase.Query();
            DealResultAfterQuery();
            FireShowForm();
        }

        private void setReplayContent()
        {
            if (queryBase != null && queryBase is DIYReplayFileQueryByCustom)
            {
                List<string> contentList = new List<string>();
                contentList.Add("itime");
                contentList.Add("wtimems");
                contentList.Add("ilongitude");
                contentList.Add("ilatitude");
                contentList.Add("lte_EARFCN");
                contentList.Add("lte_PCI");
                contentList.Add("lte_TAC");
                contentList.Add("lte_ECI");
                contentList.Add("lte_RSRP");
                contentList.Add("lte_NCell_RSRP");
                contentList.Add("lte_NCell_EARFCN");
                contentList.Add("lte_NCell_PCI");
                ((DIYReplayFileQueryByCustom)queryBase).SetReplayContent(contentList, false, false);
            }
        }

        protected virtual void DoSomethingBeforeQuery()
        {
        }

        protected virtual void DoWithDTData(TestPoint tp)
        {
        }

        protected virtual void DealResultAfterQuery()
        {
        }

        protected virtual void FireShowForm()
        {
        }
    }
}
