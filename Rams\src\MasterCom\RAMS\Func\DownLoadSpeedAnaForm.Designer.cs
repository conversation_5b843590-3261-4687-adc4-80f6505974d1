﻿namespace MasterCom.RAMS.Func
{
    partial class DownLoadSpeedAnaForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.XYDiagram xyDiagram100 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series145 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel235 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel236 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle64 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.Series series146 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel19 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView19 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.PieSeriesLabel pieSeriesLabel20 = new DevExpress.XtraCharts.PieSeriesLabel();
            DevExpress.XtraCharts.PieSeriesView pieSeriesView20 = new DevExpress.XtraCharts.PieSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram101 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series147 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel237 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel238 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle65 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram102 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series148 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel239 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel240 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle66 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram103 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series149 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel241 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.Series series150 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel242 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel243 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram104 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series151 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel244 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel245 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle67 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram105 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series152 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel246 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.Series series153 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel247 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel248 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram106 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series154 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel249 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel250 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle68 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram107 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series155 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel251 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.Series series156 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel252 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel253 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram108 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series157 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel254 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel255 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle69 = new DevExpress.XtraCharts.ChartTitle();
            DevExpress.XtraCharts.XYDiagram xyDiagram109 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series158 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel256 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.Series series159 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel257 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel258 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram110 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series160 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel259 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel260 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle70 = new DevExpress.XtraCharts.ChartTitle();
            this.contextMenuStripC_I = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tsmiSettingRange = new System.Windows.Forms.ToolStripMenuItem();
            this.chartControlTSDetails = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPageC_I = new DevExpress.XtraTab.XtraTabPage();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlC_IDetails = new DevExpress.XtraGrid.GridControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.splitterControl2 = new DevExpress.XtraEditors.SplitterControl();
            this.gridControlC_IPercentage = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.chartControlC_IAvg = new DevExpress.XtraCharts.ChartControl();
            this.splitterControl14 = new DevExpress.XtraEditors.SplitterControl();
            this.chartControlC_IPercentage = new DevExpress.XtraCharts.ChartControl();
            this.xtraTabPageCoding = new DevExpress.XtraTab.XtraTabPage();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.splitContainerControl3 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlCodingDetails = new DevExpress.XtraGrid.GridControl();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.splitterControl7 = new DevExpress.XtraEditors.SplitterControl();
            this.gridControlCodingPercentage = new DevExpress.XtraGrid.GridControl();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.chartControlCodingDetails = new DevExpress.XtraCharts.ChartControl();
            this.splitterControl5 = new DevExpress.XtraEditors.SplitterControl();
            this.chartControlCodingPercentage = new DevExpress.XtraCharts.ChartControl();
            this.xtraTabPageReselection = new DevExpress.XtraTab.XtraTabPage();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.splitContainerControl4 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlReselDetails = new DevExpress.XtraGrid.GridControl();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.splitterControl8 = new DevExpress.XtraEditors.SplitterControl();
            this.gridControlReselPercentage = new DevExpress.XtraGrid.GridControl();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.chartControlReselDetails = new DevExpress.XtraCharts.ChartControl();
            this.splitterControl3 = new DevExpress.XtraEditors.SplitterControl();
            this.chartControlReselPercentage = new DevExpress.XtraCharts.ChartControl();
            this.xtraTabPageCV = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl5 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlCVSpeed = new DevExpress.XtraGrid.GridControl();
            this.gridView9 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.splitterControl9 = new DevExpress.XtraEditors.SplitterControl();
            this.gridControlCVPercentage = new DevExpress.XtraGrid.GridControl();
            this.gridView10 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.chartControlCVSpeed = new DevExpress.XtraCharts.ChartControl();
            this.splitterControl10 = new DevExpress.XtraEditors.SplitterControl();
            this.chartControlCVPercentage = new DevExpress.XtraCharts.ChartControl();
            this.xtraTabPageMEAN_BEP = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl6 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlMEANSpeed = new DevExpress.XtraGrid.GridControl();
            this.gridView11 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.splitterControl11 = new DevExpress.XtraEditors.SplitterControl();
            this.gridControlMEANPercentage = new DevExpress.XtraGrid.GridControl();
            this.gridView12 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.chartControlMEANSpeed = new DevExpress.XtraCharts.ChartControl();
            this.splitterControl12 = new DevExpress.XtraEditors.SplitterControl();
            this.chartControlMEANPercentage = new DevExpress.XtraCharts.ChartControl();
            this.xtraTabPageTS = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl7 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlTSSpeed = new DevExpress.XtraGrid.GridControl();
            this.gridView7 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.splitterControl1 = new DevExpress.XtraEditors.SplitterControl();
            this.gridControlTSPercentage = new DevExpress.XtraGrid.GridControl();
            this.gridView8 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.chartControlTSSpeed = new DevExpress.XtraCharts.ChartControl();
            this.splitterControl13 = new DevExpress.XtraEditors.SplitterControl();
            this.chartControlTSPercentage = new DevExpress.XtraCharts.ChartControl();
            this.btnExport2Xls = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.splitterControl4 = new DevExpress.XtraEditors.SplitterControl();
            this.splitterControl6 = new DevExpress.XtraEditors.SplitterControl();
            this.contextMenuStripC_I.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlTSDetails)).BeginInit();
            this.chartControlTSDetails.SuspendLayout();
            this.xtraTabPageC_I.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlC_IDetails)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlC_IPercentage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlC_IAvg)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram100)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series145)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel235)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel236)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlC_IPercentage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series146)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView20)).BeginInit();
            this.xtraTabPageCoding.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).BeginInit();
            this.splitContainerControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCodingDetails)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCodingPercentage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlCodingDetails)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram101)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series147)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel237)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel238)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlCodingPercentage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram102)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series148)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel239)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel240)).BeginInit();
            this.xtraTabPageReselection.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl4)).BeginInit();
            this.splitContainerControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlReselDetails)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlReselPercentage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlReselDetails)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram103)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series149)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel241)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series150)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel242)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel243)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlReselPercentage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram104)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series151)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel244)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel245)).BeginInit();
            this.xtraTabPageCV.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl5)).BeginInit();
            this.splitContainerControl5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCVSpeed)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCVPercentage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlCVSpeed)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram105)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series152)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel246)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series153)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel247)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel248)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlCVPercentage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram106)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series154)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel249)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel250)).BeginInit();
            this.xtraTabPageMEAN_BEP.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl6)).BeginInit();
            this.splitContainerControl6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlMEANSpeed)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlMEANPercentage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlMEANSpeed)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram107)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series155)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel251)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series156)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel252)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel253)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlMEANPercentage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram108)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series157)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel254)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel255)).BeginInit();
            this.xtraTabPageTS.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl7)).BeginInit();
            this.splitContainerControl7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTSSpeed)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTSPercentage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlTSSpeed)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram109)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series158)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel256)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series159)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel257)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel258)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlTSPercentage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram110)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series160)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel259)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel260)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            this.SuspendLayout();
            // 
            // contextMenuStripC_I
            // 
            this.contextMenuStripC_I.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiSettingRange});
            this.contextMenuStripC_I.Name = "contextMenuStripC_I";
            this.contextMenuStripC_I.Size = new System.Drawing.Size(142, 26);
            // 
            // tsmiSettingRange
            // 
            this.tsmiSettingRange.Name = "tsmiSettingRange";
            this.tsmiSettingRange.Size = new System.Drawing.Size(141, 22);
            this.tsmiSettingRange.Text = "C_I范围设置";
            this.tsmiSettingRange.Click += new System.EventHandler(this.tsmiSettingRange_Click);
            // 
            // chartControlTSDetails
            // 
            this.chartControlTSDetails.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlTSDetails.Location = new System.Drawing.Point(0, 0);
            this.chartControlTSDetails.Name = "chartControlTSDetails";
            this.chartControlTSDetails.SelectedTabPage = this.xtraTabPageC_I;
            this.chartControlTSDetails.Size = new System.Drawing.Size(1206, 569);
            this.chartControlTSDetails.TabIndex = 21;
            this.chartControlTSDetails.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPageC_I,
            this.xtraTabPageCoding,
            this.xtraTabPageReselection,
            this.xtraTabPageCV,
            this.xtraTabPageMEAN_BEP,
            this.xtraTabPageTS});
            // 
            // xtraTabPageC_I
            // 
            this.xtraTabPageC_I.ContextMenuStrip = this.contextMenuStripC_I;
            this.xtraTabPageC_I.Controls.Add(this.labelControl1);
            this.xtraTabPageC_I.Controls.Add(this.splitContainerControl2);
            this.xtraTabPageC_I.Name = "xtraTabPageC_I";
            this.xtraTabPageC_I.Size = new System.Drawing.Size(1199, 539);
            this.xtraTabPageC_I.Text = "C/I ";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(6, 10);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(0, 14);
            this.labelControl1.TabIndex = 17;
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.Horizontal = false;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.gridControlC_IDetails);
            this.splitContainerControl2.Panel1.Controls.Add(this.splitterControl2);
            this.splitContainerControl2.Panel1.Controls.Add(this.gridControlC_IPercentage);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.chartControlC_IAvg);
            this.splitContainerControl2.Panel2.Controls.Add(this.splitterControl14);
            this.splitContainerControl2.Panel2.Controls.Add(this.chartControlC_IPercentage);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(1199, 539);
            this.splitContainerControl2.SplitterPosition = 222;
            this.splitContainerControl2.TabIndex = 18;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // gridControlC_IDetails
            // 
            this.gridControlC_IDetails.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlC_IDetails.Location = new System.Drawing.Point(600, 0);
            this.gridControlC_IDetails.MainView = this.gridView2;
            this.gridControlC_IDetails.Name = "gridControlC_IDetails";
            this.gridControlC_IDetails.Size = new System.Drawing.Size(599, 222);
            this.gridControlC_IDetails.TabIndex = 3;
            this.gridControlC_IDetails.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            // 
            // gridView2
            // 
            this.gridView2.GridControl = this.gridControlC_IDetails;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // splitterControl2
            // 
            this.splitterControl2.Location = new System.Drawing.Point(594, 0);
            this.splitterControl2.Name = "splitterControl2";
            this.splitterControl2.Size = new System.Drawing.Size(6, 222);
            this.splitterControl2.TabIndex = 2;
            this.splitterControl2.TabStop = false;
            // 
            // gridControlC_IPercentage
            // 
            this.gridControlC_IPercentage.Dock = System.Windows.Forms.DockStyle.Left;
            this.gridControlC_IPercentage.Location = new System.Drawing.Point(0, 0);
            this.gridControlC_IPercentage.MainView = this.gridView1;
            this.gridControlC_IPercentage.Name = "gridControlC_IPercentage";
            this.gridControlC_IPercentage.Size = new System.Drawing.Size(594, 222);
            this.gridControlC_IPercentage.TabIndex = 0;
            this.gridControlC_IPercentage.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.GridControl = this.gridControlC_IPercentage;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // chartControlC_IAvg
            // 
            xyDiagram100.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram100.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram100.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram100.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram100.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram100.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlC_IAvg.Diagram = xyDiagram100;
            this.chartControlC_IAvg.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlC_IAvg.EmptyChartText.Text = "没有数据！";
            this.chartControlC_IAvg.Legend.Visible = false;
            this.chartControlC_IAvg.Location = new System.Drawing.Point(600, 0);
            this.chartControlC_IAvg.Name = "chartControlC_IAvg";
            sideBySideBarSeriesLabel235.LineVisible = true;
            series145.Label = sideBySideBarSeriesLabel235;
            series145.Name = "Series 1";
            this.chartControlC_IAvg.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series145};
            sideBySideBarSeriesLabel236.LineVisible = true;
            this.chartControlC_IAvg.SeriesTemplate.Label = sideBySideBarSeriesLabel236;
            this.chartControlC_IAvg.Size = new System.Drawing.Size(599, 311);
            this.chartControlC_IAvg.TabIndex = 33;
            chartTitle64.Text = "下载速率均值";
            this.chartControlC_IAvg.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle64});
            // 
            // splitterControl14
            // 
            this.splitterControl14.Location = new System.Drawing.Point(594, 0);
            this.splitterControl14.Name = "splitterControl14";
            this.splitterControl14.Size = new System.Drawing.Size(6, 311);
            this.splitterControl14.TabIndex = 3;
            this.splitterControl14.TabStop = false;
            // 
            // chartControlC_IPercentage
            // 
            this.chartControlC_IPercentage.Dock = System.Windows.Forms.DockStyle.Left;
            this.chartControlC_IPercentage.Location = new System.Drawing.Point(0, 0);
            this.chartControlC_IPercentage.Name = "chartControlC_IPercentage";
            pieSeriesLabel19.LineVisible = true;
            series146.Label = pieSeriesLabel19;
            series146.Name = "Series 1";
            pieSeriesView19.RuntimeExploding = false;
            series146.View = pieSeriesView19;
            this.chartControlC_IPercentage.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series146};
            pieSeriesLabel20.LineVisible = true;
            this.chartControlC_IPercentage.SeriesTemplate.Label = pieSeriesLabel20;
            pieSeriesView20.RuntimeExploding = false;
            this.chartControlC_IPercentage.SeriesTemplate.View = pieSeriesView20;
            this.chartControlC_IPercentage.Size = new System.Drawing.Size(594, 311);
            this.chartControlC_IPercentage.TabIndex = 0;
            // 
            // xtraTabPageCoding
            // 
            this.xtraTabPageCoding.Controls.Add(this.labelControl2);
            this.xtraTabPageCoding.Controls.Add(this.splitContainerControl3);
            this.xtraTabPageCoding.Name = "xtraTabPageCoding";
            this.xtraTabPageCoding.Size = new System.Drawing.Size(1199, 539);
            this.xtraTabPageCoding.Text = "编码方式";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(6, 10);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(0, 14);
            this.labelControl2.TabIndex = 22;
            // 
            // splitContainerControl3
            // 
            this.splitContainerControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl3.Horizontal = false;
            this.splitContainerControl3.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl3.Name = "splitContainerControl3";
            this.splitContainerControl3.Panel1.Controls.Add(this.gridControlCodingDetails);
            this.splitContainerControl3.Panel1.Controls.Add(this.splitterControl7);
            this.splitContainerControl3.Panel1.Controls.Add(this.gridControlCodingPercentage);
            this.splitContainerControl3.Panel1.Text = "Panel1";
            this.splitContainerControl3.Panel2.Controls.Add(this.chartControlCodingDetails);
            this.splitContainerControl3.Panel2.Controls.Add(this.splitterControl5);
            this.splitContainerControl3.Panel2.Controls.Add(this.chartControlCodingPercentage);
            this.splitContainerControl3.Panel2.Text = "Panel2";
            this.splitContainerControl3.Size = new System.Drawing.Size(1199, 539);
            this.splitContainerControl3.SplitterPosition = 254;
            this.splitContainerControl3.TabIndex = 23;
            this.splitContainerControl3.Text = "splitContainerControl3";
            // 
            // gridControlCodingDetails
            // 
            this.gridControlCodingDetails.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlCodingDetails.Location = new System.Drawing.Point(607, 0);
            this.gridControlCodingDetails.MainView = this.gridView3;
            this.gridControlCodingDetails.Name = "gridControlCodingDetails";
            this.gridControlCodingDetails.Size = new System.Drawing.Size(592, 254);
            this.gridControlCodingDetails.TabIndex = 6;
            this.gridControlCodingDetails.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView3});
            // 
            // gridView3
            // 
            this.gridView3.GridControl = this.gridControlCodingDetails;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // splitterControl7
            // 
            this.splitterControl7.Location = new System.Drawing.Point(601, 0);
            this.splitterControl7.Name = "splitterControl7";
            this.splitterControl7.Size = new System.Drawing.Size(6, 254);
            this.splitterControl7.TabIndex = 5;
            this.splitterControl7.TabStop = false;
            // 
            // gridControlCodingPercentage
            // 
            this.gridControlCodingPercentage.Dock = System.Windows.Forms.DockStyle.Left;
            this.gridControlCodingPercentage.Location = new System.Drawing.Point(0, 0);
            this.gridControlCodingPercentage.MainView = this.gridView4;
            this.gridControlCodingPercentage.Name = "gridControlCodingPercentage";
            this.gridControlCodingPercentage.Size = new System.Drawing.Size(601, 254);
            this.gridControlCodingPercentage.TabIndex = 4;
            this.gridControlCodingPercentage.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView4});
            // 
            // gridView4
            // 
            this.gridView4.GridControl = this.gridControlCodingPercentage;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsView.ShowGroupPanel = false;
            // 
            // chartControlCodingDetails
            // 
            xyDiagram101.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram101.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram101.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram101.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram101.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram101.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlCodingDetails.Diagram = xyDiagram101;
            this.chartControlCodingDetails.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlCodingDetails.EmptyChartText.Text = "没有数据！";
            this.chartControlCodingDetails.Legend.Visible = false;
            this.chartControlCodingDetails.Location = new System.Drawing.Point(607, 0);
            this.chartControlCodingDetails.Name = "chartControlCodingDetails";
            sideBySideBarSeriesLabel237.LineVisible = true;
            series147.Label = sideBySideBarSeriesLabel237;
            series147.Name = "Series 1";
            this.chartControlCodingDetails.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series147};
            sideBySideBarSeriesLabel238.LineVisible = true;
            this.chartControlCodingDetails.SeriesTemplate.Label = sideBySideBarSeriesLabel238;
            this.chartControlCodingDetails.Size = new System.Drawing.Size(592, 279);
            this.chartControlCodingDetails.TabIndex = 26;
            chartTitle65.Text = "下载速率均值";
            this.chartControlCodingDetails.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle65});
            // 
            // splitterControl5
            // 
            this.splitterControl5.Location = new System.Drawing.Point(601, 0);
            this.splitterControl5.Name = "splitterControl5";
            this.splitterControl5.Size = new System.Drawing.Size(6, 279);
            this.splitterControl5.TabIndex = 23;
            this.splitterControl5.TabStop = false;
            // 
            // chartControlCodingPercentage
            // 
            xyDiagram102.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram102.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram102.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram102.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram102.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram102.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram102.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlCodingPercentage.Diagram = xyDiagram102;
            this.chartControlCodingPercentage.Dock = System.Windows.Forms.DockStyle.Left;
            this.chartControlCodingPercentage.EmptyChartText.Text = "没有数据！";
            this.chartControlCodingPercentage.Legend.Visible = false;
            this.chartControlCodingPercentage.Location = new System.Drawing.Point(0, 0);
            this.chartControlCodingPercentage.Name = "chartControlCodingPercentage";
            sideBySideBarSeriesLabel239.LineVisible = true;
            series148.Label = sideBySideBarSeriesLabel239;
            series148.Name = "Series 1";
            this.chartControlCodingPercentage.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series148};
            sideBySideBarSeriesLabel240.LineVisible = true;
            this.chartControlCodingPercentage.SeriesTemplate.Label = sideBySideBarSeriesLabel240;
            this.chartControlCodingPercentage.Size = new System.Drawing.Size(601, 279);
            this.chartControlCodingPercentage.TabIndex = 21;
            chartTitle66.Text = "比例";
            this.chartControlCodingPercentage.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle66});
            // 
            // xtraTabPageReselection
            // 
            this.xtraTabPageReselection.Controls.Add(this.labelControl3);
            this.xtraTabPageReselection.Controls.Add(this.splitContainerControl4);
            this.xtraTabPageReselection.Name = "xtraTabPageReselection";
            this.xtraTabPageReselection.Size = new System.Drawing.Size(1199, 539);
            this.xtraTabPageReselection.Text = "重选次数";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(6, 10);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(0, 14);
            this.labelControl3.TabIndex = 22;
            // 
            // splitContainerControl4
            // 
            this.splitContainerControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl4.Horizontal = false;
            this.splitContainerControl4.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl4.Name = "splitContainerControl4";
            this.splitContainerControl4.Panel1.Controls.Add(this.gridControlReselDetails);
            this.splitContainerControl4.Panel1.Controls.Add(this.splitterControl8);
            this.splitContainerControl4.Panel1.Controls.Add(this.gridControlReselPercentage);
            this.splitContainerControl4.Panel1.Text = "Panel1";
            this.splitContainerControl4.Panel2.Controls.Add(this.chartControlReselDetails);
            this.splitContainerControl4.Panel2.Controls.Add(this.splitterControl3);
            this.splitContainerControl4.Panel2.Controls.Add(this.chartControlReselPercentage);
            this.splitContainerControl4.Panel2.Text = "Panel2";
            this.splitContainerControl4.Size = new System.Drawing.Size(1199, 539);
            this.splitContainerControl4.SplitterPosition = 212;
            this.splitContainerControl4.TabIndex = 24;
            this.splitContainerControl4.Text = "splitContainerControl4";
            // 
            // gridControlReselDetails
            // 
            this.gridControlReselDetails.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlReselDetails.Location = new System.Drawing.Point(613, 0);
            this.gridControlReselDetails.MainView = this.gridView5;
            this.gridControlReselDetails.Name = "gridControlReselDetails";
            this.gridControlReselDetails.Size = new System.Drawing.Size(586, 212);
            this.gridControlReselDetails.TabIndex = 6;
            this.gridControlReselDetails.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView5});
            // 
            // gridView5
            // 
            this.gridView5.GridControl = this.gridControlReselDetails;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsView.ShowGroupPanel = false;
            // 
            // splitterControl8
            // 
            this.splitterControl8.Location = new System.Drawing.Point(607, 0);
            this.splitterControl8.Name = "splitterControl8";
            this.splitterControl8.Size = new System.Drawing.Size(6, 212);
            this.splitterControl8.TabIndex = 5;
            this.splitterControl8.TabStop = false;
            // 
            // gridControlReselPercentage
            // 
            this.gridControlReselPercentage.Dock = System.Windows.Forms.DockStyle.Left;
            this.gridControlReselPercentage.Location = new System.Drawing.Point(0, 0);
            this.gridControlReselPercentage.MainView = this.gridView6;
            this.gridControlReselPercentage.Name = "gridControlReselPercentage";
            this.gridControlReselPercentage.Size = new System.Drawing.Size(607, 212);
            this.gridControlReselPercentage.TabIndex = 4;
            this.gridControlReselPercentage.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView6});
            // 
            // gridView6
            // 
            this.gridView6.GridControl = this.gridControlReselPercentage;
            this.gridView6.Name = "gridView6";
            this.gridView6.OptionsView.ShowGroupPanel = false;
            // 
            // chartControlReselDetails
            // 
            xyDiagram103.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram103.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram103.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram103.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram103.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram103.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlReselDetails.Diagram = xyDiagram103;
            this.chartControlReselDetails.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlReselDetails.Location = new System.Drawing.Point(613, 0);
            this.chartControlReselDetails.Name = "chartControlReselDetails";
            sideBySideBarSeriesLabel241.LineVisible = true;
            series149.Label = sideBySideBarSeriesLabel241;
            series149.Name = "Series 1";
            sideBySideBarSeriesLabel242.LineVisible = true;
            series150.Label = sideBySideBarSeriesLabel242;
            series150.Name = "Series 2";
            this.chartControlReselDetails.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series149,
        series150};
            sideBySideBarSeriesLabel243.LineVisible = true;
            this.chartControlReselDetails.SeriesTemplate.Label = sideBySideBarSeriesLabel243;
            this.chartControlReselDetails.Size = new System.Drawing.Size(586, 321);
            this.chartControlReselDetails.TabIndex = 26;
            // 
            // splitterControl3
            // 
            this.splitterControl3.Location = new System.Drawing.Point(607, 0);
            this.splitterControl3.Name = "splitterControl3";
            this.splitterControl3.Size = new System.Drawing.Size(6, 321);
            this.splitterControl3.TabIndex = 22;
            this.splitterControl3.TabStop = false;
            // 
            // chartControlReselPercentage
            // 
            xyDiagram104.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram104.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram104.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram104.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram104.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram104.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram104.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlReselPercentage.Diagram = xyDiagram104;
            this.chartControlReselPercentage.Dock = System.Windows.Forms.DockStyle.Left;
            this.chartControlReselPercentage.EmptyChartText.Text = "没有数据！";
            this.chartControlReselPercentage.Legend.Visible = false;
            this.chartControlReselPercentage.Location = new System.Drawing.Point(0, 0);
            this.chartControlReselPercentage.Name = "chartControlReselPercentage";
            sideBySideBarSeriesLabel244.LineVisible = true;
            series151.Label = sideBySideBarSeriesLabel244;
            series151.Name = "Series 1";
            this.chartControlReselPercentage.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series151};
            sideBySideBarSeriesLabel245.LineVisible = true;
            this.chartControlReselPercentage.SeriesTemplate.Label = sideBySideBarSeriesLabel245;
            this.chartControlReselPercentage.Size = new System.Drawing.Size(607, 321);
            this.chartControlReselPercentage.TabIndex = 21;
            chartTitle67.Text = "比例";
            this.chartControlReselPercentage.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle67});
            // 
            // xtraTabPageCV
            // 
            this.xtraTabPageCV.Controls.Add(this.splitContainerControl5);
            this.xtraTabPageCV.Name = "xtraTabPageCV";
            this.xtraTabPageCV.Size = new System.Drawing.Size(1199, 539);
            this.xtraTabPageCV.Text = "CV_BEP";
            // 
            // splitContainerControl5
            // 
            this.splitContainerControl5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl5.Horizontal = false;
            this.splitContainerControl5.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl5.Name = "splitContainerControl5";
            this.splitContainerControl5.Panel1.Controls.Add(this.gridControlCVSpeed);
            this.splitContainerControl5.Panel1.Controls.Add(this.splitterControl9);
            this.splitContainerControl5.Panel1.Controls.Add(this.gridControlCVPercentage);
            this.splitContainerControl5.Panel1.Text = "Panel1";
            this.splitContainerControl5.Panel2.Controls.Add(this.chartControlCVSpeed);
            this.splitContainerControl5.Panel2.Controls.Add(this.splitterControl10);
            this.splitContainerControl5.Panel2.Controls.Add(this.chartControlCVPercentage);
            this.splitContainerControl5.Panel2.Text = "Panel2";
            this.splitContainerControl5.Size = new System.Drawing.Size(1199, 539);
            this.splitContainerControl5.SplitterPosition = 212;
            this.splitContainerControl5.TabIndex = 25;
            this.splitContainerControl5.Text = "splitContainerControl5";
            // 
            // gridControlCVSpeed
            // 
            this.gridControlCVSpeed.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlCVSpeed.Location = new System.Drawing.Point(613, 0);
            this.gridControlCVSpeed.MainView = this.gridView9;
            this.gridControlCVSpeed.Name = "gridControlCVSpeed";
            this.gridControlCVSpeed.Size = new System.Drawing.Size(586, 212);
            this.gridControlCVSpeed.TabIndex = 6;
            this.gridControlCVSpeed.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView9});
            // 
            // gridView9
            // 
            this.gridView9.GridControl = this.gridControlCVSpeed;
            this.gridView9.Name = "gridView9";
            this.gridView9.OptionsView.ShowGroupPanel = false;
            // 
            // splitterControl9
            // 
            this.splitterControl9.Location = new System.Drawing.Point(607, 0);
            this.splitterControl9.Name = "splitterControl9";
            this.splitterControl9.Size = new System.Drawing.Size(6, 212);
            this.splitterControl9.TabIndex = 5;
            this.splitterControl9.TabStop = false;
            // 
            // gridControlCVPercentage
            // 
            this.gridControlCVPercentage.Dock = System.Windows.Forms.DockStyle.Left;
            this.gridControlCVPercentage.Location = new System.Drawing.Point(0, 0);
            this.gridControlCVPercentage.MainView = this.gridView10;
            this.gridControlCVPercentage.Name = "gridControlCVPercentage";
            this.gridControlCVPercentage.Size = new System.Drawing.Size(607, 212);
            this.gridControlCVPercentage.TabIndex = 4;
            this.gridControlCVPercentage.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView10});
            // 
            // gridView10
            // 
            this.gridView10.GridControl = this.gridControlCVPercentage;
            this.gridView10.Name = "gridView10";
            this.gridView10.OptionsView.ShowGroupPanel = false;
            // 
            // chartControlCVSpeed
            // 
            xyDiagram105.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram105.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram105.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram105.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram105.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram105.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlCVSpeed.Diagram = xyDiagram105;
            this.chartControlCVSpeed.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlCVSpeed.Location = new System.Drawing.Point(613, 0);
            this.chartControlCVSpeed.Name = "chartControlCVSpeed";
            sideBySideBarSeriesLabel246.LineVisible = true;
            series152.Label = sideBySideBarSeriesLabel246;
            series152.Name = "Series 1";
            sideBySideBarSeriesLabel247.LineVisible = true;
            series153.Label = sideBySideBarSeriesLabel247;
            series153.Name = "Series 2";
            this.chartControlCVSpeed.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series152,
        series153};
            sideBySideBarSeriesLabel248.LineVisible = true;
            this.chartControlCVSpeed.SeriesTemplate.Label = sideBySideBarSeriesLabel248;
            this.chartControlCVSpeed.Size = new System.Drawing.Size(586, 321);
            this.chartControlCVSpeed.TabIndex = 26;
            // 
            // splitterControl10
            // 
            this.splitterControl10.Location = new System.Drawing.Point(607, 0);
            this.splitterControl10.Name = "splitterControl10";
            this.splitterControl10.Size = new System.Drawing.Size(6, 321);
            this.splitterControl10.TabIndex = 22;
            this.splitterControl10.TabStop = false;
            // 
            // chartControlCVPercentage
            // 
            xyDiagram106.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram106.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram106.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram106.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram106.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram106.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram106.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlCVPercentage.Diagram = xyDiagram106;
            this.chartControlCVPercentage.Dock = System.Windows.Forms.DockStyle.Left;
            this.chartControlCVPercentage.EmptyChartText.Text = "没有数据！";
            this.chartControlCVPercentage.Legend.Visible = false;
            this.chartControlCVPercentage.Location = new System.Drawing.Point(0, 0);
            this.chartControlCVPercentage.Name = "chartControlCVPercentage";
            sideBySideBarSeriesLabel249.LineVisible = true;
            series154.Label = sideBySideBarSeriesLabel249;
            series154.Name = "Series 1";
            this.chartControlCVPercentage.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series154};
            sideBySideBarSeriesLabel250.LineVisible = true;
            this.chartControlCVPercentage.SeriesTemplate.Label = sideBySideBarSeriesLabel250;
            this.chartControlCVPercentage.Size = new System.Drawing.Size(607, 321);
            this.chartControlCVPercentage.TabIndex = 21;
            chartTitle68.Text = "比例";
            this.chartControlCVPercentage.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle68});
            // 
            // xtraTabPageMEAN_BEP
            // 
            this.xtraTabPageMEAN_BEP.Controls.Add(this.splitContainerControl6);
            this.xtraTabPageMEAN_BEP.Name = "xtraTabPageMEAN_BEP";
            this.xtraTabPageMEAN_BEP.Size = new System.Drawing.Size(1199, 539);
            this.xtraTabPageMEAN_BEP.Text = "MEAN_BEP";
            // 
            // splitContainerControl6
            // 
            this.splitContainerControl6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl6.Horizontal = false;
            this.splitContainerControl6.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl6.Name = "splitContainerControl6";
            this.splitContainerControl6.Panel1.Controls.Add(this.gridControlMEANSpeed);
            this.splitContainerControl6.Panel1.Controls.Add(this.splitterControl11);
            this.splitContainerControl6.Panel1.Controls.Add(this.gridControlMEANPercentage);
            this.splitContainerControl6.Panel1.Text = "Panel1";
            this.splitContainerControl6.Panel2.Controls.Add(this.chartControlMEANSpeed);
            this.splitContainerControl6.Panel2.Controls.Add(this.splitterControl12);
            this.splitContainerControl6.Panel2.Controls.Add(this.chartControlMEANPercentage);
            this.splitContainerControl6.Panel2.Text = "Panel2";
            this.splitContainerControl6.Size = new System.Drawing.Size(1199, 539);
            this.splitContainerControl6.SplitterPosition = 212;
            this.splitContainerControl6.TabIndex = 26;
            this.splitContainerControl6.Text = "splitContainerControl6";
            // 
            // gridControlMEANSpeed
            // 
            this.gridControlMEANSpeed.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlMEANSpeed.Location = new System.Drawing.Point(613, 0);
            this.gridControlMEANSpeed.MainView = this.gridView11;
            this.gridControlMEANSpeed.Name = "gridControlMEANSpeed";
            this.gridControlMEANSpeed.Size = new System.Drawing.Size(586, 212);
            this.gridControlMEANSpeed.TabIndex = 6;
            this.gridControlMEANSpeed.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView11});
            // 
            // gridView11
            // 
            this.gridView11.GridControl = this.gridControlMEANSpeed;
            this.gridView11.Name = "gridView11";
            this.gridView11.OptionsView.ShowGroupPanel = false;
            // 
            // splitterControl11
            // 
            this.splitterControl11.Location = new System.Drawing.Point(607, 0);
            this.splitterControl11.Name = "splitterControl11";
            this.splitterControl11.Size = new System.Drawing.Size(6, 212);
            this.splitterControl11.TabIndex = 5;
            this.splitterControl11.TabStop = false;
            // 
            // gridControlMEANPercentage
            // 
            this.gridControlMEANPercentage.Dock = System.Windows.Forms.DockStyle.Left;
            this.gridControlMEANPercentage.Location = new System.Drawing.Point(0, 0);
            this.gridControlMEANPercentage.MainView = this.gridView12;
            this.gridControlMEANPercentage.Name = "gridControlMEANPercentage";
            this.gridControlMEANPercentage.Size = new System.Drawing.Size(607, 212);
            this.gridControlMEANPercentage.TabIndex = 4;
            this.gridControlMEANPercentage.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView12});
            // 
            // gridView12
            // 
            this.gridView12.GridControl = this.gridControlMEANPercentage;
            this.gridView12.Name = "gridView12";
            this.gridView12.OptionsView.ShowGroupPanel = false;
            // 
            // chartControlMEANSpeed
            // 
            xyDiagram107.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram107.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram107.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram107.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram107.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram107.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlMEANSpeed.Diagram = xyDiagram107;
            this.chartControlMEANSpeed.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlMEANSpeed.Location = new System.Drawing.Point(613, 0);
            this.chartControlMEANSpeed.Name = "chartControlMEANSpeed";
            sideBySideBarSeriesLabel251.LineVisible = true;
            series155.Label = sideBySideBarSeriesLabel251;
            series155.Name = "Series 1";
            sideBySideBarSeriesLabel252.LineVisible = true;
            series156.Label = sideBySideBarSeriesLabel252;
            series156.Name = "Series 2";
            this.chartControlMEANSpeed.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series155,
        series156};
            sideBySideBarSeriesLabel253.LineVisible = true;
            this.chartControlMEANSpeed.SeriesTemplate.Label = sideBySideBarSeriesLabel253;
            this.chartControlMEANSpeed.Size = new System.Drawing.Size(586, 321);
            this.chartControlMEANSpeed.TabIndex = 26;
            // 
            // splitterControl12
            // 
            this.splitterControl12.Location = new System.Drawing.Point(607, 0);
            this.splitterControl12.Name = "splitterControl12";
            this.splitterControl12.Size = new System.Drawing.Size(6, 321);
            this.splitterControl12.TabIndex = 22;
            this.splitterControl12.TabStop = false;
            // 
            // chartControlMEANPercentage
            // 
            xyDiagram108.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram108.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram108.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram108.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram108.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram108.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram108.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlMEANPercentage.Diagram = xyDiagram108;
            this.chartControlMEANPercentage.Dock = System.Windows.Forms.DockStyle.Left;
            this.chartControlMEANPercentage.EmptyChartText.Text = "没有数据！";
            this.chartControlMEANPercentage.Legend.Visible = false;
            this.chartControlMEANPercentage.Location = new System.Drawing.Point(0, 0);
            this.chartControlMEANPercentage.Name = "chartControlMEANPercentage";
            sideBySideBarSeriesLabel254.LineVisible = true;
            series157.Label = sideBySideBarSeriesLabel254;
            series157.Name = "Series 1";
            this.chartControlMEANPercentage.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series157};
            sideBySideBarSeriesLabel255.LineVisible = true;
            this.chartControlMEANPercentage.SeriesTemplate.Label = sideBySideBarSeriesLabel255;
            this.chartControlMEANPercentage.Size = new System.Drawing.Size(607, 321);
            this.chartControlMEANPercentage.TabIndex = 21;
            chartTitle69.Text = "比例";
            this.chartControlMEANPercentage.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle69});
            // 
            // xtraTabPageTS
            // 
            this.xtraTabPageTS.Controls.Add(this.splitContainerControl7);
            this.xtraTabPageTS.Name = "xtraTabPageTS";
            this.xtraTabPageTS.Size = new System.Drawing.Size(1199, 539);
            this.xtraTabPageTS.Text = "TimeSlot_DL";
            // 
            // splitContainerControl7
            // 
            this.splitContainerControl7.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl7.Horizontal = false;
            this.splitContainerControl7.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl7.Name = "splitContainerControl7";
            this.splitContainerControl7.Panel1.Controls.Add(this.gridControlTSSpeed);
            this.splitContainerControl7.Panel1.Controls.Add(this.splitterControl1);
            this.splitContainerControl7.Panel1.Controls.Add(this.gridControlTSPercentage);
            this.splitContainerControl7.Panel1.Text = "Panel1";
            this.splitContainerControl7.Panel2.Controls.Add(this.chartControlTSSpeed);
            this.splitContainerControl7.Panel2.Controls.Add(this.splitterControl13);
            this.splitContainerControl7.Panel2.Controls.Add(this.chartControlTSPercentage);
            this.splitContainerControl7.Panel2.Text = "Panel2";
            this.splitContainerControl7.Size = new System.Drawing.Size(1199, 539);
            this.splitContainerControl7.SplitterPosition = 212;
            this.splitContainerControl7.TabIndex = 26;
            this.splitContainerControl7.Text = "splitContainerControl7";
            // 
            // gridControlTSSpeed
            // 
            this.gridControlTSSpeed.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlTSSpeed.Location = new System.Drawing.Point(613, 0);
            this.gridControlTSSpeed.MainView = this.gridView7;
            this.gridControlTSSpeed.Name = "gridControlTSSpeed";
            this.gridControlTSSpeed.Size = new System.Drawing.Size(586, 212);
            this.gridControlTSSpeed.TabIndex = 6;
            this.gridControlTSSpeed.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView7});
            // 
            // gridView7
            // 
            this.gridView7.GridControl = this.gridControlTSSpeed;
            this.gridView7.Name = "gridView7";
            this.gridView7.OptionsView.ShowGroupPanel = false;
            // 
            // splitterControl1
            // 
            this.splitterControl1.Location = new System.Drawing.Point(607, 0);
            this.splitterControl1.Name = "splitterControl1";
            this.splitterControl1.Size = new System.Drawing.Size(6, 212);
            this.splitterControl1.TabIndex = 5;
            this.splitterControl1.TabStop = false;
            // 
            // gridControlTSPercentage
            // 
            this.gridControlTSPercentage.Dock = System.Windows.Forms.DockStyle.Left;
            this.gridControlTSPercentage.Location = new System.Drawing.Point(0, 0);
            this.gridControlTSPercentage.MainView = this.gridView8;
            this.gridControlTSPercentage.Name = "gridControlTSPercentage";
            this.gridControlTSPercentage.Size = new System.Drawing.Size(607, 212);
            this.gridControlTSPercentage.TabIndex = 4;
            this.gridControlTSPercentage.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView8});
            // 
            // gridView8
            // 
            this.gridView8.GridControl = this.gridControlTSPercentage;
            this.gridView8.Name = "gridView8";
            this.gridView8.OptionsView.ShowGroupPanel = false;
            // 
            // chartControlTSSpeed
            // 
            xyDiagram109.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram109.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram109.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram109.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram109.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram109.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlTSSpeed.Diagram = xyDiagram109;
            this.chartControlTSSpeed.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlTSSpeed.Location = new System.Drawing.Point(613, 0);
            this.chartControlTSSpeed.Name = "chartControlTSSpeed";
            sideBySideBarSeriesLabel256.LineVisible = true;
            series158.Label = sideBySideBarSeriesLabel256;
            series158.Name = "Series 1";
            sideBySideBarSeriesLabel257.LineVisible = true;
            series159.Label = sideBySideBarSeriesLabel257;
            series159.Name = "Series 2";
            this.chartControlTSSpeed.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series158,
        series159};
            sideBySideBarSeriesLabel258.LineVisible = true;
            this.chartControlTSSpeed.SeriesTemplate.Label = sideBySideBarSeriesLabel258;
            this.chartControlTSSpeed.Size = new System.Drawing.Size(586, 321);
            this.chartControlTSSpeed.TabIndex = 26;
            // 
            // splitterControl13
            // 
            this.splitterControl13.Location = new System.Drawing.Point(607, 0);
            this.splitterControl13.Name = "splitterControl13";
            this.splitterControl13.Size = new System.Drawing.Size(6, 321);
            this.splitterControl13.TabIndex = 22;
            this.splitterControl13.TabStop = false;
            // 
            // chartControlTSPercentage
            // 
            xyDiagram110.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram110.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram110.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram110.AxisY.NumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            xyDiagram110.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram110.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram110.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlTSPercentage.Diagram = xyDiagram110;
            this.chartControlTSPercentage.Dock = System.Windows.Forms.DockStyle.Left;
            this.chartControlTSPercentage.EmptyChartText.Text = "没有数据！";
            this.chartControlTSPercentage.Legend.Visible = false;
            this.chartControlTSPercentage.Location = new System.Drawing.Point(0, 0);
            this.chartControlTSPercentage.Name = "chartControlTSPercentage";
            sideBySideBarSeriesLabel259.LineVisible = true;
            series160.Label = sideBySideBarSeriesLabel259;
            series160.Name = "Series 1";
            this.chartControlTSPercentage.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series160};
            sideBySideBarSeriesLabel260.LineVisible = true;
            this.chartControlTSPercentage.SeriesTemplate.Label = sideBySideBarSeriesLabel260;
            this.chartControlTSPercentage.Size = new System.Drawing.Size(607, 321);
            this.chartControlTSPercentage.TabIndex = 21;
            chartTitle70.Text = "比例";
            this.chartControlTSPercentage.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle70});
            // 
            // btnExport2Xls
            // 
            this.btnExport2Xls.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnExport2Xls.Location = new System.Drawing.Point(1087, 0);
            this.btnExport2Xls.Name = "btnExport2Xls";
            this.btnExport2Xls.Size = new System.Drawing.Size(115, 27);
            this.btnExport2Xls.TabIndex = 4;
            this.btnExport2Xls.Text = "导出数据到Excel";
            this.btnExport2Xls.Click += new System.EventHandler(this.btnExport2Xls_Click);
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(5, 9);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(0, 14);
            this.labelControl4.TabIndex = 22;
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.splitterControl4);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.splitterControl6);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1027, 458);
            this.splitContainerControl1.SplitterPosition = 212;
            this.splitContainerControl1.TabIndex = 24;
            this.splitContainerControl1.Text = "splitContainerControl4";
            // 
            // splitterControl4
            // 
            this.splitterControl4.Location = new System.Drawing.Point(0, 0);
            this.splitterControl4.Name = "splitterControl4";
            this.splitterControl4.Size = new System.Drawing.Size(6, 212);
            this.splitterControl4.TabIndex = 5;
            this.splitterControl4.TabStop = false;
            // 
            // splitterControl6
            // 
            this.splitterControl6.Location = new System.Drawing.Point(0, 0);
            this.splitterControl6.Name = "splitterControl6";
            this.splitterControl6.Size = new System.Drawing.Size(6, 240);
            this.splitterControl6.TabIndex = 22;
            this.splitterControl6.TabStop = false;
            // 
            // DownLoadSpeedAnaForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1206, 569);
            this.Controls.Add(this.btnExport2Xls);
            this.Controls.Add(this.chartControlTSDetails);
            this.Name = "DownLoadSpeedAnaForm";
            this.Text = "下载速率评估分析";
            this.contextMenuStripC_I.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chartControlTSDetails)).EndInit();
            this.chartControlTSDetails.ResumeLayout(false);
            this.xtraTabPageC_I.ResumeLayout(false);
            this.xtraTabPageC_I.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlC_IDetails)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlC_IPercentage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram100)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel235)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series145)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel236)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlC_IAvg)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series146)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesLabel20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlC_IPercentage)).EndInit();
            this.xtraTabPageCoding.ResumeLayout(false);
            this.xtraTabPageCoding.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).EndInit();
            this.splitContainerControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCodingDetails)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCodingPercentage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram101)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel237)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series147)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel238)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlCodingDetails)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram102)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel239)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series148)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel240)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlCodingPercentage)).EndInit();
            this.xtraTabPageReselection.ResumeLayout(false);
            this.xtraTabPageReselection.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl4)).EndInit();
            this.splitContainerControl4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlReselDetails)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlReselPercentage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram103)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel241)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series149)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel242)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series150)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel243)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlReselDetails)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram104)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel244)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series151)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel245)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlReselPercentage)).EndInit();
            this.xtraTabPageCV.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl5)).EndInit();
            this.splitContainerControl5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCVSpeed)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCVPercentage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram105)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel246)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series152)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel247)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series153)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel248)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlCVSpeed)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram106)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel249)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series154)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel250)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlCVPercentage)).EndInit();
            this.xtraTabPageMEAN_BEP.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl6)).EndInit();
            this.splitContainerControl6.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlMEANSpeed)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlMEANPercentage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram107)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel251)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series155)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel252)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series156)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel253)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlMEANSpeed)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram108)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel254)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series157)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel255)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlMEANPercentage)).EndInit();
            this.xtraTabPageTS.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl7)).EndInit();
            this.splitContainerControl7.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTSSpeed)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTSPercentage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram109)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel256)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series158)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel257)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series159)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel258)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlTSSpeed)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram110)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel259)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series160)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel260)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlTSPercentage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStripC_I;
        private System.Windows.Forms.ToolStripMenuItem tsmiSettingRange;
        private DevExpress.XtraTab.XtraTabControl chartControlTSDetails;
        private DevExpress.XtraTab.XtraTabPage xtraTabPageC_I;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraTab.XtraTabPage xtraTabPageCoding;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl3;
        private DevExpress.XtraTab.XtraTabPage xtraTabPageReselection;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl4;
        private DevExpress.XtraCharts.ChartControl chartControlReselPercentage;
        private DevExpress.XtraCharts.ChartControl chartControlCodingDetails;
        private DevExpress.XtraEditors.SplitterControl splitterControl5;
        private DevExpress.XtraEditors.SplitterControl splitterControl3;
        private DevExpress.XtraGrid.GridControl gridControlC_IDetails;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.GridControl gridControlC_IPercentage;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.GridControl gridControlCodingDetails;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraEditors.SplitterControl splitterControl7;
        private DevExpress.XtraGrid.GridControl gridControlCodingPercentage;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.GridControl gridControlReselDetails;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraEditors.SplitterControl splitterControl8;
        private DevExpress.XtraGrid.GridControl gridControlReselPercentage;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraCharts.ChartControl chartControlCodingPercentage;
        private DevExpress.XtraEditors.SimpleButton btnExport2Xls;
        private DevExpress.XtraCharts.ChartControl chartControlReselDetails;
        private DevExpress.XtraTab.XtraTabPage xtraTabPageCV;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl5;
        private DevExpress.XtraEditors.SplitterControl splitterControl9;
        private DevExpress.XtraCharts.ChartControl chartControlCVSpeed;
        private DevExpress.XtraEditors.SplitterControl splitterControl10;
        private DevExpress.XtraCharts.ChartControl chartControlCVPercentage;
        private DevExpress.XtraTab.XtraTabPage xtraTabPageMEAN_BEP;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl6;
        private DevExpress.XtraGrid.GridControl gridControlMEANSpeed;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView11;
        private DevExpress.XtraEditors.SplitterControl splitterControl11;
        private DevExpress.XtraGrid.GridControl gridControlMEANPercentage;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView12;
        private DevExpress.XtraCharts.ChartControl chartControlMEANSpeed;
        private DevExpress.XtraEditors.SplitterControl splitterControl12;
        private DevExpress.XtraCharts.ChartControl chartControlMEANPercentage;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.SplitterControl splitterControl4;
        private DevExpress.XtraEditors.SplitterControl splitterControl6;
        private DevExpress.XtraGrid.GridControl gridControlCVSpeed;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView9;
        private DevExpress.XtraGrid.GridControl gridControlCVPercentage;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView10;
        private DevExpress.XtraTab.XtraTabPage xtraTabPageTS;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl7;
        private DevExpress.XtraGrid.GridControl gridControlTSSpeed;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView7;
        private DevExpress.XtraEditors.SplitterControl splitterControl1;
        private DevExpress.XtraGrid.GridControl gridControlTSPercentage;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView8;
        private DevExpress.XtraCharts.ChartControl chartControlTSSpeed;
        private DevExpress.XtraEditors.SplitterControl splitterControl13;
        private DevExpress.XtraCharts.ChartControl chartControlTSPercentage;
        private DevExpress.XtraEditors.SplitterControl splitterControl2;
        private DevExpress.XtraCharts.ChartControl chartControlC_IAvg;
        private DevExpress.XtraEditors.SplitterControl splitterControl14;
        private DevExpress.XtraCharts.ChartControl chartControlC_IPercentage;
    }
}