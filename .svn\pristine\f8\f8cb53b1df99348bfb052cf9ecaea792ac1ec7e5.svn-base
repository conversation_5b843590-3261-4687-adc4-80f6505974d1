using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.Model
{
    [Serializable]
    public class WorkSheet
    {
        public WorkSheet Clone()
        {
            System.IO.MemoryStream stream = new System.IO.MemoryStream();
            System.Runtime.Serialization.Formatters.Binary.BinaryFormatter formatter = new System.Runtime.Serialization.Formatters.Binary.BinaryFormatter();
            formatter.Serialize(stream, this);
            stream.Position = 0;
            return (WorkSheet)formatter.Deserialize(stream);
        }

        public bool CanSave { get; set; } = true;

        public override string ToString()
        {
            return Text;
        }

        public string Text { get; set; }

        public List<ChildFormConfig> ChildFormConfigs { get; set; } = new List<ChildFormConfig>();

        public int ActiveChildFormIndex
        {
            get
            {
                if (ChildFormConfigs.Count > 0)
                {
                    int index = 0;
                    foreach (ChildFormConfig childFormConfig in ChildFormConfigs)
                    {
                        if (childFormConfig.ChildForm != null && !childFormConfig.ChildForm.Visible)
                        {
                            break;
                        }
                        else if (childFormConfig.ChildForm != null && childFormConfig.ChildForm.MdiParent !=null && childFormConfig.ChildForm.MdiParent.ActiveMdiChild == childFormConfig.ChildForm)
                        {
                            activeChildFormIndex = index;
                            break;
                        }
                        index++;
                    }
                }
                else
                {
                    activeChildFormIndex = -1;
                }
                return activeChildFormIndex;
            }
            set { activeChildFormIndex = value; }
        }

        public object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals(typeof(WorkSheet).Name))
            {
                ChildFormConfigs.Clear();
                Text = configFile.GetItemValue(item, "Text") as string;
                List<object> list = configFile.GetItemValue(item, "ChildFormConfigs", GetItemValue) as List<object>;
                if (list != null)
                {
                    foreach (object value in list)
                    {
                        if (value != null && value is ChildFormConfig)
                        {
                            ChildFormConfigs.Add((ChildFormConfig)value);
                        }
                    }
                }
                object o = null;
                o = configFile.GetItemValue(item, "ActiveChildFormIndex");
                if (o != null)
                {
                    activeChildFormIndex = (int)o;
                }
                else
                {
                    activeChildFormIndex = -1;
                }
                return this;
            }
            else if (typeName.Equals(typeof(ChildFormConfig).Name))
            {
                ChildFormConfig childFormConfig = new ChildFormConfig();
                childFormConfig.GetItemValue(configFile, item, typeName);
                return childFormConfig;
            }
            return null;
        }

        public XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is WorkSheet)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "Text", Text);
                if (!Text.Contains("KPI"))
                {
                    configFile.AddItem(item, "ChildFormConfigs", ChildFormConfigs, AddItem);
                    configFile.AddItem(item, "ActiveChildFormIndex", ActiveChildFormIndex);
                }
                return item;
            }
            else if (value is ChildFormConfig)
            {
                return (value as ChildFormConfig).AddItem(configFile, config, null, value);
            }
            return null;
        }

        private int activeChildFormIndex = -1;
    }
}
