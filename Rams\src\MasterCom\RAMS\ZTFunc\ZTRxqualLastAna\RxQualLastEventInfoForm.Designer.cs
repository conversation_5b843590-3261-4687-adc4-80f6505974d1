﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class RxQualLastEventInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.listView = new System.Windows.Forms.ListView();
            this.columnHeaderSN = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderEventName = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderFileName = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderLAC = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCI = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderLongitude = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderLatitude = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderLastDuration = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderLastDist = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRoadName = new System.Windows.Forms.ColumnHeader();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItemExport2xls = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemRelatedAna = new System.Windows.Forms.ToolStripMenuItem();
            this.columnHeaderStartTime = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderEndTime = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderSampleNum = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRxlevMax = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRxlevMin = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRxlevMean = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderC_I_Max = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderC_I_Min = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderC_I_Mean = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRxquality_Max = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRxquality_Min = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRxquality_Mean = new System.Windows.Forms.ColumnHeader();
            this.ctxMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // listView
            // 
            this.listView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderSN,
            this.columnHeaderEventName,
            this.columnHeaderFileName,
            this.columnHeaderLAC,
            this.columnHeaderCI,
            this.columnHeaderLongitude,
            this.columnHeaderLatitude,
            this.columnHeaderLastDuration,
            this.columnHeaderLastDist,
            this.columnHeaderRoadName,
            this.columnHeaderStartTime,
            this.columnHeaderEndTime,
            this.columnHeaderSampleNum,
            this.columnHeaderRxlevMax,
            this.columnHeaderRxlevMin,
            this.columnHeaderRxlevMean,
            this.columnHeaderC_I_Max,
            this.columnHeaderC_I_Min,
            this.columnHeaderC_I_Mean,
            this.columnHeaderRxquality_Max,
            this.columnHeaderRxquality_Min,
            this.columnHeaderRxquality_Mean});
            this.listView.ContextMenuStrip = this.ctxMenu;
            this.listView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listView.FullRowSelect = true;
            this.listView.GridLines = true;
            this.listView.Location = new System.Drawing.Point(0, 0);
            this.listView.MultiSelect = false;
            this.listView.Name = "listView";
            this.listView.ShowGroups = false;
            this.listView.Size = new System.Drawing.Size(925, 479);
            this.listView.TabIndex = 0;
            this.listView.UseCompatibleStateImageBehavior = false;
            this.listView.View = System.Windows.Forms.View.Details;
            this.listView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listView_MouseDoubleClick);
            // 
            // columnHeaderSN
            // 
            this.columnHeaderSN.Text = "序号";
            // 
            // columnHeaderEventName
            // 
            this.columnHeaderEventName.Text = "事件名";
            this.columnHeaderEventName.Width = 154;
            // 
            // columnHeaderFileName
            // 
            this.columnHeaderFileName.Text = "所属文件";
            this.columnHeaderFileName.Width = 134;
            // 
            // columnHeaderLAC
            // 
            this.columnHeaderLAC.Text = "LAC";
            this.columnHeaderLAC.Width = 72;
            // 
            // columnHeaderCI
            // 
            this.columnHeaderCI.Text = "CI";
            this.columnHeaderCI.Width = 70;
            // 
            // columnHeaderLongitude
            // 
            this.columnHeaderLongitude.Text = "经度";
            // 
            // columnHeaderLatitude
            // 
            this.columnHeaderLatitude.Text = "纬度";
            // 
            // columnHeaderLastDuration
            // 
            this.columnHeaderLastDuration.Text = "持续时长(秒)";
            this.columnHeaderLastDuration.Width = 141;
            // 
            // columnHeaderLastDist
            // 
            this.columnHeaderLastDist.Text = "质差距离(米)";
            this.columnHeaderLastDist.Width = 148;
            // 
            // columnHeaderRoadName
            // 
            this.columnHeaderRoadName.Text = "道路名称";
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItemExport2xls,
            this.ToolStripMenuItemRelatedAna});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(155, 48);
            // 
            // toolStripMenuItemExport2xls
            // 
            this.toolStripMenuItemExport2xls.Name = "toolStripMenuItemExport2xls";
            this.toolStripMenuItemExport2xls.Size = new System.Drawing.Size(154, 22);
            this.toolStripMenuItemExport2xls.Text = "导出到Excel...";
            this.toolStripMenuItemExport2xls.Click += new System.EventHandler(this.toolStripMenuItemExport2xls_Click);
            // 
            // ToolStripMenuItemRelatedAna
            // 
            this.ToolStripMenuItemRelatedAna.Name = "ToolStripMenuItemRelatedAna";
            this.ToolStripMenuItemRelatedAna.Size = new System.Drawing.Size(154, 22);
            this.ToolStripMenuItemRelatedAna.Text = "扫频关联分析";
            this.ToolStripMenuItemRelatedAna.Click += new System.EventHandler(this.ToolStripMenuItemRelatedAna_Click);
            // 
            // columnHeaderStartTime
            // 
            this.columnHeaderStartTime.Text = "质差起始时间";
            // 
            // columnHeaderEndTime
            // 
            this.columnHeaderEndTime.Text = "质差结束时间";
            // 
            // columnHeaderSampleNum
            // 
            this.columnHeaderSampleNum.Text = "采样点数";
            // 
            // columnHeaderRxlevMax
            // 
            this.columnHeaderRxlevMax.Text = "最大场强";
            // 
            // columnHeaderRxlevMin
            // 
            this.columnHeaderRxlevMin.Text = "最小场强";
            // 
            // columnHeaderRxlevMean
            // 
            this.columnHeaderRxlevMean.Text = "平均场强";
            // 
            // columnHeaderC_I_Max
            // 
            this.columnHeaderC_I_Max.Text = "最大C/I";
            // 
            // columnHeaderC_I_Min
            // 
            this.columnHeaderC_I_Min.Text = "最小C/I";
            // 
            // columnHeaderC_I_Mean
            // 
            this.columnHeaderC_I_Mean.Text = "平均C/I";
            // 
            // columnHeaderRxquality_Max
            // 
            this.columnHeaderRxquality_Max.Text = "最大Rxquality";
            // 
            // columnHeaderRxquality_Min
            // 
            this.columnHeaderRxquality_Min.Text = "最小Rxquality";
            // 
            // columnHeaderRxquality_Mean
            // 
            this.columnHeaderRxquality_Mean.Text = "平均Rxquality";
            // 
            // RxQualLastEventInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(925, 479);
            this.Controls.Add(this.listView);
            this.Name = "RxQualLastEventInfoForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "持续质差详细信息";
            this.ctxMenu.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ListView listView;
        private System.Windows.Forms.ColumnHeader columnHeaderSN;
        private System.Windows.Forms.ColumnHeader columnHeaderEventName;
        private System.Windows.Forms.ColumnHeader columnHeaderFileName;
        private System.Windows.Forms.ColumnHeader columnHeaderLAC;
        private System.Windows.Forms.ColumnHeader columnHeaderCI;
        private System.Windows.Forms.ColumnHeader columnHeaderLastDuration;
        private System.Windows.Forms.ColumnHeader columnHeaderLastDist;
        private System.Windows.Forms.ColumnHeader columnHeaderRoadName;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemExport2xls;
        private System.Windows.Forms.ColumnHeader columnHeaderLongitude;
        private System.Windows.Forms.ColumnHeader columnHeaderLatitude;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemRelatedAna;
        private System.Windows.Forms.ColumnHeader columnHeaderStartTime;
        private System.Windows.Forms.ColumnHeader columnHeaderEndTime;
        private System.Windows.Forms.ColumnHeader columnHeaderSampleNum;
        private System.Windows.Forms.ColumnHeader columnHeaderRxlevMax;
        private System.Windows.Forms.ColumnHeader columnHeaderRxlevMin;
        private System.Windows.Forms.ColumnHeader columnHeaderRxlevMean;
        private System.Windows.Forms.ColumnHeader columnHeaderC_I_Max;
        private System.Windows.Forms.ColumnHeader columnHeaderC_I_Min;
        private System.Windows.Forms.ColumnHeader columnHeaderC_I_Mean;
        private System.Windows.Forms.ColumnHeader columnHeaderRxquality_Max;
        private System.Windows.Forms.ColumnHeader columnHeaderRxquality_Min;
        private System.Windows.Forms.ColumnHeader columnHeaderRxquality_Mean;
    }
}