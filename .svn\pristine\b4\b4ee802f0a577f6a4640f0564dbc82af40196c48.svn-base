﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using MasterCom.RAMS.Net;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTAddProblemCfgForm : BaseDialog
    {
        public ZTAddProblemCfgForm()
        {
            InitializeComponent();
            initData();
            cmbJudge.Text = "追加";
        }

        public int iFunc { get; set; } = 1;
        private Dictionary<string, AddIndexItem>  IndexDic = new Dictionary<string, AddIndexItem>();
        public StatIndexProblem statIndexProblem { get; set; } = new StatIndexProblem();
        private void btnOK_Click(object sender, EventArgs e)
        {
            if (tbProName.Text == "")
                return;
            statIndexProblem.pName = tbProName.Text;
            statIndexProblem.ptype = cmbJudge.Text;
            statIndexProblem.conditionList = addConditon();
            SaveCfgXML();
            this.DialogResult = DialogResult.OK;
        }

        private List<StatIndexCondition> addConditon()
        {
            List<StatIndexCondition> list = new List<StatIndexCondition>();
            foreach (ListViewItem item in listView.Items)
            {
                StatIndexCondition stat = item.Tag as StatIndexCondition;
                list.Add(stat);
            }
            return list;
        }

        
        private void btnCancel_Click(object sender, EventArgs e)
        {
            SaveCfgXML();
            this.DialogResult = DialogResult.Cancel;
        }

        private void initData()
        {
            ReadCfgXML();
            reflashListView();
        }
        private void reflashListView()
        {
            listViewdt.Items.Clear();
            listViewscan.Items.Clear();
            listViewtest.Items.Clear();
            listViewstat.Items.Clear();
            foreach (AddIndexItem add in IndexDic.Values)
            {
                switch (add.indextype)
                {
                    case "路测":
                        updateListView(listViewdt, add);
                        break;
                    case "扫频":
                        updateListView(listViewscan, add);
                        break;
                    case "测量":
                        updateListView(listViewtest, add);
                        break;
                    case "统计":
                        updateListView(listViewstat, add);
                        break;
                    default:
                        break;
                }
            }
        }

        private void updateListView(ListView listView, AddIndexItem add)
        {
            listView.BeginUpdate();
            ListViewItem listitem = new ListViewItem();
            listitem.Tag = add;
            listitem.SubItems.Add(add.name);
            listitem.SubItems.Add(add.des);
            listView.Items.Add(listitem);
            listView.EndUpdate();
        }
        
        private void ReadCfgXML()
        {
            IndexDic = new Dictionary<string, AddIndexItem>();
            string path = Application.StartupPath + "//userData//AntParaCommonCfg.xml";
            try
            {
                XmlDocument xd1 = new XmlDocument();
                xd1.Load(path);
                XmlNodeList xnl = xd1.SelectNodes("/AntParaCommon");
                foreach (XmlNode xl1 in xnl)
                {
                    XmlNodeList xnl1 = xl1.SelectNodes("IndexType");
                    foreach (XmlNode xl in xnl1)
                    {
                        AddIndexItem addItem = new AddIndexItem();
                        addItem.indextype = xl.Attributes["type"].Value;
                        XmlNode XL = xl.SelectSingleNode("Name");
                        addItem.name = XL.InnerText;
                        XmlNode XL2 = xl.SelectSingleNode("Des");
                        addItem.des = XL2.InnerText;
                        if (!IndexDic.ContainsKey(addItem.name + addItem.indextype))
                            IndexDic[addItem.name + addItem.indextype] = addItem;
                    }
                }
            }
            catch
            {
                MessageBox.Show("文件读取失败！");
            }
        }

        private void SaveCfgXML()
        {
            try
            {
                string path = Application.StartupPath + "//userData//AntParaCommonCfg.xml";
                XmlDocument xd = new XmlDocument();
                xd.CreateXmlDeclaration("1.0", "utf-8", "yes");
                XmlNode first = xd.CreateElement("AntParaCommon");
                foreach (AddIndexItem addItem in IndexDic.Values)
                {
                    XmlNode root = xd.CreateElement("IndexType");
                    XmlAttribute xa = xd.CreateAttribute("type");
                    xa.Value = addItem.indextype;
                    root.Attributes.Append(xa);
                    XmlNode xn1 = xd.CreateElement("Name");
                    xn1.InnerText = addItem.name;
                    root.AppendChild(xn1);
                    XmlNode xn3 = xd.CreateElement("Des");
                    xn3.InnerText = addItem.des;
                    root.AppendChild(xn3);
                    first.AppendChild(root);
                }
                xd.AppendChild(first);
                xd.Save(path);
            }
            catch
            {
                MessageBox.Show("文件写入失败！");
            }
        }

        private void 添加指标ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ZTAddIndexCfgForm cfg = new ZTAddIndexCfgForm();
            if (cfg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                AddIndexItem addIndex = cfg.addIndexItem;
                if (!IndexDic.ContainsKey(addIndex.name + addIndex.indextype))
                    IndexDic[addIndex.name + addIndex.indextype] = addIndex;
                reflashListView();
            }
        }

        private void 删除指标ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            DialogResult dr = MessageBox.Show("确定要删除本条内容？", "删除记录", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (dr != DialogResult.OK)
                return;
            AddIndexItem addItem = new AddIndexItem();
            int idx = tabIndex.SelectedIndex;
            if (idx == 0)
            {
                if (listViewdt.SelectedItems.Count == 0)
                    return;
                addItem = listViewdt.SelectedItems[0].Tag as AddIndexItem;
                listViewdt.Items.RemoveAt(listViewdt.SelectedItems[0].Index);
            }
            else if (idx == 1)
            {
                if (listViewscan.SelectedItems.Count == 0)
                    return;
                addItem = listViewscan.SelectedItems[0].Tag as AddIndexItem;
                listViewscan.Items.RemoveAt(listViewscan.SelectedItems[0].Index);
            }
            else if (idx == 2)
            {
                addItem = listViewtest.SelectedItems[0].Tag as AddIndexItem;
                listViewtest.Items.RemoveAt(listViewtest.SelectedItems[0].Index);
            }
            else if (idx == 3)
            {
                addItem = listViewstat.SelectedItems[0].Tag as AddIndexItem;
                listViewtest.Items.RemoveAt(listViewtest.SelectedItems[0].Index);
            }
            IndexDic.Remove(addItem.name + addItem.indextype);
        }

        private void btnadd_Click(object sender, EventArgs e)
        {
            StatIndexCondition cond = new StatIndexCondition();
            cond.tip = tbconTips.Text;
        }

        internal void Apply(StatIndexProblem statP)
        {
            tbProName.Enabled = false;
            btnOK.Text = "修改";
            statIndexProblem = statP;
            tbProName.Text = statP.pName;
            cmbJudge.Text = statP.ptype;
            initData();
            refreshFormula();
        }

        private void listView_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            AddIndexItem addItem = new AddIndexItem();
            int idx = tabIndex.SelectedIndex;
            if (idx == 0)
            {
                if (listViewdt.SelectedItems.Count == 0)
                    return;
                addItem = listViewdt.SelectedItems[0].Tag as AddIndexItem;
            }
            else if (idx == 1)
            {
                if (listViewscan.SelectedItems.Count == 0)
                    return;
                addItem = listViewscan.SelectedItems[0].Tag as AddIndexItem;
            }
            else if (idx == 2)
            {
                if (listViewtest.SelectedItems.Count == 0)
                    return;
                addItem = listViewtest.SelectedItems[0].Tag as AddIndexItem;
            }
            else if (idx == 3)
            {
                if (listViewstat.SelectedItems.Count == 0)
                    return;
                addItem = listViewstat.SelectedItems[0].Tag as AddIndexItem;
            }
            tbFormula.Text += "[" + addItem.indextype + "_" + addItem.name + "]";
        }

        private void btnCheckFormula_Click(object sender, EventArgs e)
        {
            if (tbFormula.Text == "")
            {
                lbinfo.Text = "公式不能为空";
                return;
            }
            if (!CheckFormula(tbFormula.Text))
            {
                lbinfo.Text = "公式错误！";
                return;
            }
            lbinfo.Text = "公式通过！";
        }
        /// <summary>
        /// 添加公式
        /// </summary>
        private void btnAddFormula_Click(object sender, EventArgs e)
        {
            if (!CheckFormula(tbFormula.Text) || tbFormula.Text == "")
            {
                lbinfo.Text = "公式不正确！";
                return;
            }
            if (tbconTips.Text == "")
            {
                lbinfo.Text = "提示语不能为空！";
                return;
            }
            StatIndexCondition con = new StatIndexCondition();
            con.tip = tbconTips.Text;
            con.Formula = tbFormula.Text;
            if (!statIndexProblem.conditionList.Contains(con))
                statIndexProblem.conditionList.Add(con);
            refreshFormula();
        }

        private void refreshFormula()
        {
            if (statIndexProblem == null)
                return;
            listView.Items.Clear();
            foreach (StatIndexCondition con in statIndexProblem.conditionList)
            {
                listView.BeginUpdate();
                ListViewItem item = new ListViewItem();
                item.Tag = con;
                item.SubItems.Add(con.Formula);
                item.SubItems.Add(con.tip);
                listView.Items.Add(item);
                listView.EndUpdate();
            }
        }

        private bool CheckFormula(string p)
        {
            return ProblemAanalysis.GetInstance().CheckFormula(p);
        }
        /// <summary>
        /// 删除公式
        /// </summary>
        private void btnDelFormula_Click(object sender, EventArgs e)
        {
            if (listView.SelectedItems.Count == 0)
                return;
            DialogResult dr = MessageBox.Show("确定要删除本条公式？", "删除公式", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (dr == DialogResult.OK)
            {
                StatIndexCondition con = listView.SelectedItems[0].Tag as StatIndexCondition;
                statIndexProblem.conditionList.Remove(con);
                refreshFormula();
            }
        }

        #region 按键事件

        private void btnFormula_Click(object sender, EventArgs e)
        {
            tbFormula.AppendText(((Button)sender).Text);
        }

        private void btnOr_Click(object sender, EventArgs e)
        {
            tbFormula.AppendText(" or ");
        }
        private void btnAnd_Click(object sender, EventArgs e)
        {
            tbFormula.AppendText(" and ");
        }
        private void btnNotEQ_Click(object sender, EventArgs e)
        {
            tbFormula.AppendText(" <> ");
        }
        private void btnBackSpace_Click(object sender, EventArgs e)
        {
            if (tbFormula.Text != string.Empty)
            {
                tbFormula.Text = tbFormula.Text.Remove(tbFormula.Text.Length - 1, 1);
            }
        }
        private void btnYesOrNo_Click(object sender, EventArgs e)
        {
            tbFormula.AppendText(" '" + ((Button)sender).Text + "' ");
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            tbFormula.Text = "";
        }

        private void listView_MouseClick(object sender, MouseEventArgs e)
        {
            if (listView.SelectedItems.Count == 0)
                return;
            StatIndexCondition con = listView.SelectedItems[0].Tag as StatIndexCondition;
            tbFormula.Text = con.Formula;
        }

        private void btnup_Click(object sender, EventArgs e)
        {
            if (listView.SelectedItems.Count == 0)
                return;
            listView.BeginUpdate();
            foreach (ListViewItem var in listView.SelectedItems)
            {
                int indexSelectedItem = var.Index;
                if (indexSelectedItem == 0)
                    break;
                listView.Items.RemoveAt(indexSelectedItem);
                listView.Items.Insert(indexSelectedItem - 1, var);
            }
            listView.EndUpdate();
        }

        private void btndown_Click(object sender, EventArgs e)
        {
            if (listView.SelectedItems.Count == 0)
                return;
            listView.BeginUpdate();
            foreach (ListViewItem var in listView.SelectedItems)
            {
                int indexSelectedItem = var.Index;
                if (indexSelectedItem == listView.Items.Count - 1)
                    break;
                listView.Items.RemoveAt(indexSelectedItem);
                listView.Items.Insert(indexSelectedItem + 1, var);
            }
            listView.EndUpdate();
        }
        #endregion

    }
}
