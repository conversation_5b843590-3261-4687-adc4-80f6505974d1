﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.MTGis;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class DIYNoCoverRoadByRegion_TDLTEScan : DIYNoCoverRoadByRegion_GScan
    {
        private static DIYNoCoverRoadByRegion_TDLTEScan intance = null;
        public new static DIYNoCoverRoadByRegion_TDLTEScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new DIYNoCoverRoadByRegion_TDLTEScan();
                    }
                }
            }
            return intance;
        }

        protected DIYNoCoverRoadByRegion_TDLTEScan()
            : base()
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_SCAN_TOPN);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "弱覆盖路段分析_TDLTE扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22001, this.Name);//////
        }

        protected override void getReadyBeforeQuery()
        {
            Columns = new List<string>();
            Columns.Add("LTESCAN_TopN_CELL_Specific_RSRP");
        }

        protected override void fireSetDefaultMapSerialTheme()
        {
            MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
        }

        protected override bool filterFile(FileInfo fileInfo)
        {
            return false;
        }

        private LTEScanNoCoverRoadSetDlg setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new LTEScanNoCoverRoadSetDlg();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                int rxLev;
                int distance;
                setForm.GetFilterCondition(out rxLev, out distance);
                rxLevThreshold = rxLev;
                distanceLast = distance;
                return true;
            }
            return false;
        }

        protected override bool validTestPoint(TestPoint tp)
        {
            if (tp is ScanTestPoint_LTE)
            {
                float? rxLev = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", 0];
                if (rxLev != null && rxLev <= rxLevThreshold)
                {
                    return true;
                }
            }
            return false;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.None; }
        }
        #endregion
    }
}
