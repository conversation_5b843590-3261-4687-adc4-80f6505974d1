﻿using System;
using System.Collections.Generic;
using System.Text;
using CsGL.Util;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class CsfbFailureEntity
    {
        public CsfbFailureEntity()
        {
            TestPoints = new List<TestPoint>();
            Events = new List<Event>();
            Messages = new List<Message>();
        }

        public string OtherSideCause { get; set; }

        protected bool hasReleaseEvt = false;
        protected bool hasCallAttemptEvt = false;
        protected bool cmServiceAbort = false;
        protected bool hasCMServiceRequestMsg = false;
        protected bool hasPagingResponseMsg = false;
        protected MessageWithSource releaseMsg = null;
        protected MessageWithSource alertingMsg = null;
        protected MessageWithSource disconnnectMsg = null;
        protected int MoMtFlag = -1;
        public string MoMtDesc
        {
            get
            {
                string desc = "未知";
                if (MoMtFlag == (int)MoMtFile.MoFlag)
                {
                    desc = "主叫";
                }
                else if (MoMtFlag == (int)MoMtFile.MtFlag)
                {
                    desc = "被叫";
                }
                return desc;
            }
        }

        public DateTime RequestTime { get; protected set; }
        public DateTime FailureTime { get; protected set; }

        public CsfbFailureEntity(DTFileDataManager fi, int fromEvtIdx, int toEvtIdx, int forwardSec)
            : this()
        {
            MoMtFlag = fi.MoMtFlag;
            Event requestEvent = fi.Events[fromEvtIdx];
            RequestTime = requestEvent.DateTime;
            Event failureEvent = fi.Events[toEvtIdx];
            FailureTime = failureEvent.DateTime;
            int endIdx = fi.DTDatas.IndexOf(failureEvent);
            //从failure事件往前取数据，直到超出request事件forwardSec秒
            for (int i = endIdx; i >= 0; i--)
            {
                DTData data = fi.DTDatas[i];
                if (requestEvent.Time - data.Time > forwardSec)
                { //超出前x秒
                    break;
                }
                if (data is Event)
                {
                    Event evt = data as Event;
                    Events.Add(evt);
                    if (evt.ID == 886 || evt.ID == 878)
                    {
                        hasReleaseEvt = true;
                    }
                    else if (evt.ID == 1001 || evt.ID == 1002)
                    {
                        hasCallAttemptEvt = true;
                    }
                }
                else if (data is TestPoint)
                {
                    TestPoints.Add(data as TestPoint);
                }
                else if (data is Message)
                {
                    dealMsg(requestEvent, data);
                }
            }
        }

        private void dealMsg(Event requestEvent, DTData data)
        {
            Message msg = data as Message;
            Messages.Add(msg);
            if (msg is MessageWithSource && msg.SN > requestEvent.SN)
            { //取request后面的Msg
                if (msg.ID == (int)CSFBMsg.RRCConnectionRelease)
                {
                    releaseMsg = msg as MessageWithSource;
                }
                else if (msg.ID == (int)CSFBMsg.Alterting)
                {
                    alertingMsg = msg as MessageWithSource;
                }
                else if (msg.ID == (int)CSFBMsg.Disconnect)
                {
                    disconnnectMsg = msg as MessageWithSource;
                }
                else if (msg.ID == (int)CSFBMsg.CmServiceAbort)
                {
                    cmServiceAbort = true;
                }
                else
                {
                    if (MoMtFlag == (int)MoMtFile.MoFlag && msg.ID == (int)CSFBMsg.CmServiceRequest)
                    {
                        hasCMServiceRequestMsg = true;
                    }
                    else if (MoMtFlag == (int)MoMtFile.MtFlag && msg.ID == (int)CSFBMsg.PagingReponse)
                    {
                        hasPagingResponseMsg = true;
                    }
                }
            }
        }

        public List<Event> Events { get; set; }
        public List<TestPoint> TestPoints { get; set; }
        public List<Message> Messages { get; set; }

        public double LngMid
        {
            get
            {
                return Events[Events.Count / 2].Longitude;
            }
        }
        public double LatMid
        {
            get
            {
                return Events[Events.Count / 2].Latitude;
            }
        }

        public string FileName
        {
            get
            {
                return Events[Events.Count / 2].FileName;
            }
        }

        public float? RsrpMin { get; protected set; }
        public float? RsrpMax { get; protected set; }
        public float? RsrpAvg { get; protected set; }
        public float? SinrMin { get; protected set; }
        public float? SinrMax { get; protected set; }
        public float? SinrAvg { get; protected set; }

        public int? RxLevSubMin { get; protected set; }
        public int? RxLevSubMax { get; protected set; }
        public int? RxLevSubAvg { get; protected set; }
        public int? RxQualMin { get; protected set; }
        public int? RxQualMax { get; protected set; }
        public int? RxQualAvg { get; protected set; }

        public float? RscpMin { get; protected set; }
        public float? RscpMax { get; protected set; }
        public float? RscpAvg { get; protected set; }
        public float? BlerMin { get; protected set; }
        public float? BlerMax { get; protected set; }
        public float? BlerAvg { get; protected set; }

        public string RoadDesc { get; protected set; }
        public string AbnormalMsg { get; protected set; }

        public virtual void Summary(CsfbFailureCondition cond)
        {
            float rsrpTotal = 0;
            int rsrpNum = 0;
            float sinrTotal = 0;
            int sinrNum = 0;
            int rxLevTotal = 0;
            int rxLevNum = 0;
            int qualTotal = 0;
            int qualNum = 0;
            int rscpTotal = 0;
            int rscpNum = 0;
            int blerTotal = 0;
            int blerNum = 0;
            foreach (TestPoint tp in TestPoints)
            {
                object value = tp["lte_RSRP"];
                if (value != null)
                {
                    float rsrp = float.Parse(value.ToString());
                    if (rsrp >= -141)
                    {
                        RsrpMax = RsrpMax == null ? rsrp : Math.Max(rsrp, (float)RsrpMax);
                        RsrpMin = RsrpMin == null ? rsrp : Math.Min(rsrp, (float)RsrpMin);
                        rsrpTotal += rsrp;
                        rsrpNum++;
                    }
                }

                value = tp["lte_SINR"];
                if (value != null)
                {
                    float sinr = float.Parse(value.ToString());
                    if (sinr >= -50 && sinr <= 50)
                    {
                        SinrMax = SinrMax == null ? sinr : Math.Max(sinr, (float)SinrMax);
                        SinrMin = SinrMin == null ? sinr : Math.Min(sinr, (float)SinrMin);
                        sinrTotal += sinr;
                        sinrNum++;
                    }
                }

                value = tp["lte_gsm_DM_RxLevSub"];
                if (value != null)
                {
                    int rxLev = int.Parse(value.ToString());
                    if (-120 <= rxLev && rxLev <= -10)
                    {
                        RxLevSubMax = RxLevSubMax == null ? rxLev : Math.Max(rxLev, (int)RxLevSubMax);
                        RxLevSubMin = RxLevSubMin == null ? rxLev : Math.Min(rxLev, (int)RxLevSubMin);
                        rxLevTotal += rxLev;
                        rxLevNum++;
                    }
                }

                value = tp["lte_gsm_DM_RxQualSub"];
                if (value != null)
                {
                    int qual = int.Parse(value.ToString());
                    if (0 <= qual && qual <= 8)
                    {
                        RxQualMax = RxQualMax == null ? qual : Math.Max(qual, (int)RxQualMax);
                        RxQualMin = RxQualMin == null ? qual : Math.Min(qual, (int)RxQualMin);
                        qualTotal += qual;
                        qualNum++;
                    }
                }

                value = tp["lte_td_DM_PCCPCH_RSCP"];
                if (value != null)
                {
                    int rscp = int.Parse(value.ToString());
                    if (-140 <= rscp && rscp <= -10)
                    {
                        RscpMax = RscpMax == null ? rscp : Math.Max(rscp, (int)RscpMax);
                        RscpMin = RscpMin == null ? rscp : Math.Min(rscp, (int)RscpMin);
                        rscpTotal += rscp;
                        rscpNum++;
                    }
                }

                value = tp["lte_td_DM_BLER"];
                if (value != null)
                {
                    int bler = int.Parse(value.ToString());
                    if (0 <= bler && bler <= 10)
                    {
                        BlerMax = BlerMax == null ? bler : Math.Max(bler, (int)BlerMax);
                        BlerMin = BlerMin == null ? bler : Math.Min(bler, (int)BlerMin);
                        blerTotal += bler;
                        blerNum++;
                    }
                }
            }
            if (rsrpNum != 0)
            {
                RsrpAvg = (float)Math.Round(rsrpTotal / rsrpNum, 2);
            }
            if (sinrNum != 0)
            {
                SinrAvg = (float)Math.Round(sinrTotal / sinrNum, 2);
            }
            if (rxLevNum != 0)
            {
                RxLevSubAvg = rxLevTotal / rxLevNum;
            }
            if (qualNum != 0)
            {
                RxQualAvg = qualTotal / qualNum;
            }
            if (rscpNum != 0)
            {
                RscpAvg = rscpTotal / rscpNum;
            }
            if (blerNum != 0)
            {
                BlerAvg = blerTotal / blerNum;
            }

            evaluate(cond);
            getRoadDesc();
        }

        protected void getRoadDesc()
        {
            if (TestPoints.Count == 0)
            {
                return;
            }
            List<double> lngs = new List<double>();
            List<double> lats = new List<double>();
            lngs.Add(TestPoints[0].Longitude);
            lats.Add(TestPoints[0].Latitude);
            lngs.Add(TestPoints[TestPoints.Count / 2].Longitude);
            lats.Add(TestPoints[TestPoints.Count / 2].Latitude);
            lngs.Add(TestPoints[TestPoints.Count - 1].Longitude);
            lats.Add(TestPoints[TestPoints.Count - 1].Latitude);
            RoadDesc = GISManager.GetInstance().GetRoadPlaceDesc(lngs, lats);
        }

        protected virtual void evaluate(CsfbFailureCondition cond)
        {
            if (!hasReleaseEvt)
            { //无release事件
                AbnormalMsg = "无rrc connect release";
                if (RsrpAvg < cond.Rsrp)
                {
                    Cause = CsfbFailureCause.无RRCConnRelease_LTE网络弱覆盖;
                }
                else if (SinrAvg < cond.Sinr)
                {
                    Cause = CsfbFailureCause.无RRCConnRelease_LTE网络质差;
                }
                else
                {
                    Cause = CsfbFailureCause.无RRCConnRelease_未知原因;
                }
            }
            else
            {
                if (MoMtFlag == (int)MoMtFile.MoFlag && !hasCMServiceRequestMsg)
                {
                    AbnormalMsg = "无cm service request";
                }
                else if (MoMtFlag == (int)MoMtFile.MtFlag && !hasPagingResponseMsg)
                {
                    AbnormalMsg = "无paging reponse";
                }
                dealReleaseMsg(cond);
            }

        }

        protected virtual void dealReleaseMsg(CsfbFailureCondition cond)
        {
            if (releaseMsg != null)
            {
                uint carrier = 0, group = 0;
                MessageWithSource msgWithSoure = releaseMsg;
                MessageDecodeHelper.StartDissect(msgWithSoure.Direction, msgWithSoure.Source, msgWithSoure.Length, msgWithSoure.ID);
                MessageDecodeHelper.GetSingleUInt("lte-rrc.redirectedCarrierInfo", ref carrier);
                bool isValid = dealNeighbour(carrier, ref group);
                if (isValid)
                {
                    return;
                }

                //网络覆盖、质量问题
                isValid = dealGsmNetWork(cond);
                if (isValid)
                {
                    return;
                }

                isValid = dealNetWork(cond);
                if (isValid)
                {
                    return;
                }

                if (!hasCallAttemptEvt)
                { //无呼叫
                    Cause = MoMtFlag == (int)MoMtFile.MoFlag ? CsfbFailureCause.未发起呼叫_MO : CsfbFailureCause.未收到寻呼_MT;
                    return;
                }
                if (cmServiceAbort)
                {
                    Cause = CsfbFailureCause.主动终止服务;
                    return;
                }
                dealDisconnnectMsg();
            }
        }

        protected virtual bool dealNeighbour(uint carrier, ref uint group)
        {
            MessageDecodeHelper.GetSingleUInt("lte-rrc.explicitListOfARFCNs", ref group);
            if (carrier != (int)ECarrierInfo.geran || group <= 0)
            {
                Cause = CsfbFailureCause.无G_TD邻区配置;
                return true;
            }
            return false;
        }

        protected virtual bool dealGsmNetWork(CsfbFailureCondition cond)
        {
            if (RxLevSubAvg != null && RxLevSubAvg < cond.RxLev)
            {
                Cause = CsfbFailureCause.GSM网络弱覆盖;
                return true;
            }
            if (RxQualAvg != null && RxQualAvg >= cond.Qual)
            {
                Cause = CsfbFailureCause.GSM网络质差;
                return true;
            }
            return false;
        }

        protected virtual bool dealNetWork(CsfbFailureCondition cond)
        {
            if (RscpAvg != null && RscpAvg <= cond.Pccpch_Rscp)
            {
                Cause = CsfbFailureCause.TD网络弱覆盖;
                return true;
            }
            if (BlerAvg != null && BlerAvg >= cond.Bler)
            {
                Cause = CsfbFailureCause.TD网络高BLER;
                return true;
            }
            return false;
        }

        protected virtual void dealDisconnnectMsg()
        {
            if (disconnnectMsg != null)
            {
                uint cause = 0;
                MessageDecodeHelper.StartDissect(disconnnectMsg.Direction, disconnnectMsg.Source, disconnnectMsg.Length, disconnnectMsg.ID);
                MessageDecodeHelper.GetSingleUInt("gsm_a_dtap.cause", ref cause);
                if (cause == 16 || cause == 31)
                {
                    if (alertingMsg != null)
                    {
                        this.Cause = CsfbFailureCause.振铃未接通挂断;
                    }
                    else
                    {
                        this.Cause = CsfbFailureCause.无振铃挂断;
                    }
                    return;
                }
                this.Cause = CsfbFailureCause.异常原因释放;
            }
        }

        public CsfbFailureCause Cause { get; protected set; }

    }

    public enum CsfbFailureCause
    {
        未知原因,
        无RRCConnRelease_LTE网络弱覆盖,
        无RRCConnRelease_LTE网络质差,
        无RRCConnRelease_未知原因,
        未发起呼叫_MO,
        未收到寻呼_MT,
        主动终止服务,
        振铃未接通挂断,
        无振铃挂断,
        异常原因释放,
        GSM网络弱覆盖,
        GSM网络质差,
        无G_TD邻区配置,
        TD网络弱覆盖,
        TD网络高BLER,
        无G_W邻区配置,
        W网络弱覆盖,
        W网络高质差,
    }

}
