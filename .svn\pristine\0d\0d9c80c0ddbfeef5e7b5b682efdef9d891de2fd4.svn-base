﻿namespace MasterCom.RAMS.Model
{
    partial class CellParamSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.tabControlShow = new System.Windows.Forms.TabControl();
            this.pageCustom = new System.Windows.Forms.TabPage();
            this.treeListCustom = new DevExpress.XtraTreeList.TreeList();
            this.colRoleName = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colRoleDesc = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.pageSel = new System.Windows.Forms.TabPage();
            this.lvColSelected = new System.Windows.Forms.ListView();
            this.buttonColumnDown = new System.Windows.Forms.Button();
            this.buttonColumnUp = new System.Windows.Forms.Button();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.treeListParams = new DevExpress.XtraTreeList.TreeList();
            this.treeListColumn2 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumn1 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListColumn3 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.cmsTable = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miAddTable = new System.Windows.Forms.ToolStripMenuItem();
            this.miRemoveTables = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miRemoveCols = new System.Windows.Forms.ToolStripMenuItem();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.cmsCustom = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miAddGrp = new System.Windows.Forms.ToolStripMenuItem();
            this.miRemoveGrp = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.tabControlShow.SuspendLayout();
            this.pageCustom.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListCustom)).BeginInit();
            this.pageSel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListParams)).BeginInit();
            this.cmsTable.SuspendLayout();
            this.cmsCustom.SuspendLayout();
            this.SuspendLayout();
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.splitContainerControl2.Location = new System.Drawing.Point(1, 2);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.groupControl1);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.groupControl2);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(953, 446);
            this.splitContainerControl2.SplitterPosition = 323;
            this.splitContainerControl2.TabIndex = 9;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.tabControlShow);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(323, 446);
            this.groupControl1.TabIndex = 4;
            this.groupControl1.Text = "已选参数";
            // 
            // tabControlShow
            // 
            this.tabControlShow.Controls.Add(this.pageCustom);
            this.tabControlShow.Controls.Add(this.pageSel);
            this.tabControlShow.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlShow.Location = new System.Drawing.Point(2, 23);
            this.tabControlShow.Name = "tabControlShow";
            this.tabControlShow.SelectedIndex = 0;
            this.tabControlShow.Size = new System.Drawing.Size(319, 421);
            this.tabControlShow.TabIndex = 4;
            // 
            // pageCustom
            // 
            this.pageCustom.Controls.Add(this.treeListCustom);
            this.pageCustom.Location = new System.Drawing.Point(4, 22);
            this.pageCustom.Name = "pageCustom";
            this.pageCustom.Padding = new System.Windows.Forms.Padding(3);
            this.pageCustom.Size = new System.Drawing.Size(311, 395);
            this.pageCustom.TabIndex = 0;
            this.pageCustom.Text = "自定义参数组";
            this.pageCustom.UseVisualStyleBackColor = true;
            // 
            // treeListCustom
            // 
            this.treeListCustom.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.treeListCustom.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.Lime;
            this.treeListCustom.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.colRoleName,
            this.colRoleDesc});
            this.treeListCustom.ContextMenuStrip = this.cmsCustom;
            this.treeListCustom.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListCustom.Location = new System.Drawing.Point(3, 3);
            this.treeListCustom.LookAndFeel.SkinName = "Office 2007 Blue";
            this.treeListCustom.LookAndFeel.UseDefaultLookAndFeel = false;
            this.treeListCustom.Name = "treeListCustom";
            this.treeListCustom.BeginUnboundLoad();
            this.treeListCustom.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListCustom.AppendNode(new object[] {
            null,
            null}, 0);
            this.treeListCustom.AppendNode(new object[] {
            null,
            null}, 0);
            this.treeListCustom.AppendNode(new object[] {
            null,
            null}, 0);
            this.treeListCustom.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListCustom.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListCustom.AppendNode(new object[] {
            null,
            null}, -1);
            this.treeListCustom.AppendNode(new object[] {
            null,
            null}, 6);
            this.treeListCustom.AppendNode(new object[] {
            null,
            null}, 6);
            this.treeListCustom.EndUnboundLoad();
            this.treeListCustom.OptionsBehavior.AllowIndeterminateCheckState = true;
            this.treeListCustom.OptionsView.ShowCheckBoxes = true;
            this.treeListCustom.Size = new System.Drawing.Size(305, 389);
            this.treeListCustom.TabIndex = 3;
            // 
            // colRoleName
            // 
            this.colRoleName.Caption = "名称";
            this.colRoleName.FieldName = "Name";
            this.colRoleName.MinWidth = 51;
            this.colRoleName.Name = "colRoleName";
            this.colRoleName.OptionsColumn.AllowEdit = false;
            this.colRoleName.OptionsColumn.AllowSort = false;
            this.colRoleName.Visible = true;
            this.colRoleName.VisibleIndex = 0;
            this.colRoleName.Width = 140;
            // 
            // colRoleDesc
            // 
            this.colRoleDesc.Caption = "描述";
            this.colRoleDesc.FieldName = "Description";
            this.colRoleDesc.Name = "colRoleDesc";
            this.colRoleDesc.OptionsColumn.AllowEdit = false;
            this.colRoleDesc.OptionsColumn.AllowSort = false;
            this.colRoleDesc.Visible = true;
            this.colRoleDesc.VisibleIndex = 1;
            this.colRoleDesc.Width = 141;
            // 
            // pageSel
            // 
            this.pageSel.Controls.Add(this.lvColSelected);
            this.pageSel.Controls.Add(this.buttonColumnDown);
            this.pageSel.Controls.Add(this.buttonColumnUp);
            this.pageSel.Location = new System.Drawing.Point(4, 22);
            this.pageSel.Name = "pageSel";
            this.pageSel.Padding = new System.Windows.Forms.Padding(3);
            this.pageSel.Size = new System.Drawing.Size(311, 395);
            this.pageSel.TabIndex = 1;
            this.pageSel.Text = "已选择参数";
            this.pageSel.UseVisualStyleBackColor = true;
            // 
            // lvColSelected
            // 
            this.lvColSelected.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lvColSelected.FullRowSelect = true;
            this.lvColSelected.HideSelection = false;
            this.lvColSelected.LabelWrap = false;
            this.lvColSelected.Location = new System.Drawing.Point(3, 3);
            this.lvColSelected.Name = "lvColSelected";
            this.lvColSelected.Size = new System.Drawing.Size(235, 389);
            this.lvColSelected.TabIndex = 32;
            this.lvColSelected.UseCompatibleStateImageBehavior = false;
            this.lvColSelected.View = System.Windows.Forms.View.List;
            this.lvColSelected.Click += new System.EventHandler(this.lvColSelected_Click);
            // 
            // buttonColumnDown
            // 
            this.buttonColumnDown.Anchor = System.Windows.Forms.AnchorStyles.Right;
            this.buttonColumnDown.Location = new System.Drawing.Point(244, 200);
            this.buttonColumnDown.Name = "buttonColumnDown";
            this.buttonColumnDown.Size = new System.Drawing.Size(64, 23);
            this.buttonColumnDown.TabIndex = 31;
            this.buttonColumnDown.Text = "向下";
            this.buttonColumnDown.UseVisualStyleBackColor = true;
            this.buttonColumnDown.Click += new System.EventHandler(this.buttonColumnDown_Click);
            // 
            // buttonColumnUp
            // 
            this.buttonColumnUp.Anchor = System.Windows.Forms.AnchorStyles.Right;
            this.buttonColumnUp.Location = new System.Drawing.Point(244, 171);
            this.buttonColumnUp.Name = "buttonColumnUp";
            this.buttonColumnUp.Size = new System.Drawing.Size(64, 23);
            this.buttonColumnUp.TabIndex = 30;
            this.buttonColumnUp.Text = "向上";
            this.buttonColumnUp.UseVisualStyleBackColor = true;
            this.buttonColumnUp.Click += new System.EventHandler(this.buttonColumnUp_Click);
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.treeListParams);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl2.Location = new System.Drawing.Point(0, 0);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(624, 446);
            this.groupControl2.TabIndex = 4;
            this.groupControl2.Text = "可选参数";
            // 
            // treeListParams
            // 
            this.treeListParams.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.treeListColumn2,
            this.treeListColumn1,
            this.treeListColumn3});
            this.treeListParams.ContextMenuStrip = this.cmsTable;
            this.treeListParams.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListParams.Location = new System.Drawing.Point(2, 23);
            this.treeListParams.LookAndFeel.SkinName = "Office 2007 Blue";
            this.treeListParams.LookAndFeel.UseDefaultLookAndFeel = false;
            this.treeListParams.Name = "treeListParams";
            this.treeListParams.BeginUnboundLoad();
            this.treeListParams.AppendNode(new object[] {
            null,
            null,
            null}, -1);
            this.treeListParams.AppendNode(new object[] {
            null,
            null,
            null}, 0);
            this.treeListParams.AppendNode(new object[] {
            null,
            null,
            null}, 0);
            this.treeListParams.AppendNode(new object[] {
            null,
            null,
            null}, 0);
            this.treeListParams.AppendNode(new object[] {
            null,
            null,
            null}, -1);
            this.treeListParams.AppendNode(new object[] {
            null,
            null,
            null}, -1);
            this.treeListParams.AppendNode(new object[] {
            null,
            null,
            null}, -1);
            this.treeListParams.AppendNode(new object[] {
            null,
            null,
            null}, 6);
            this.treeListParams.AppendNode(new object[] {
            null,
            null,
            null}, 6);
            this.treeListParams.EndUnboundLoad();
            this.treeListParams.OptionsBehavior.AllowIndeterminateCheckState = true;
            this.treeListParams.OptionsView.ShowCheckBoxes = true;
            this.treeListParams.Size = new System.Drawing.Size(620, 421);
            this.treeListParams.TabIndex = 2;
            this.treeListParams.CellValueChanged += new DevExpress.XtraTreeList.CellValueChangedEventHandler(this.treeListParams_CellValueChanged);
            // 
            // treeListColumn2
            // 
            this.treeListColumn2.Caption = "名称";
            this.treeListColumn2.FieldName = "Name";
            this.treeListColumn2.MinWidth = 51;
            this.treeListColumn2.Name = "treeListColumn2";
            this.treeListColumn2.OptionsColumn.AllowEdit = false;
            this.treeListColumn2.OptionsColumn.AllowSort = false;
            this.treeListColumn2.Visible = true;
            this.treeListColumn2.VisibleIndex = 0;
            this.treeListColumn2.Width = 140;
            // 
            // treeListColumn1
            // 
            this.treeListColumn1.Caption = "别名";
            this.treeListColumn1.FieldName = "Alias";
            this.treeListColumn1.Name = "treeListColumn1";
            this.treeListColumn1.Visible = true;
            this.treeListColumn1.VisibleIndex = 1;
            // 
            // treeListColumn3
            // 
            this.treeListColumn3.Caption = "描述";
            this.treeListColumn3.FieldName = "Description";
            this.treeListColumn3.Name = "treeListColumn3";
            this.treeListColumn3.Visible = true;
            this.treeListColumn3.VisibleIndex = 2;
            this.treeListColumn3.Width = 141;
            // 
            // cmsTable
            // 
            this.cmsTable.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miAddTable,
            this.miRemoveTables,
            this.toolStripMenuItem1,
            this.miRemoveCols});
            this.cmsTable.Name = "cmsTable";
            this.cmsTable.Size = new System.Drawing.Size(147, 76);
            // 
            // miAddTable
            // 
            this.miAddTable.Name = "miAddTable";
            this.miAddTable.Size = new System.Drawing.Size(152, 22);
            this.miAddTable.Text = "添加表...";
            this.miAddTable.Click += new System.EventHandler(this.miAddTable_Click);
            // 
            // miRemoveTables
            // 
            this.miRemoveTables.Name = "miRemoveTables";
            this.miRemoveTables.Size = new System.Drawing.Size(152, 22);
            this.miRemoveTables.Text = "移除勾选表";
            this.miRemoveTables.Click += new System.EventHandler(this.miRemoveTables_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(149, 6);
            // 
            // miRemoveCols
            // 
            this.miRemoveCols.Name = "miRemoveCols";
            this.miRemoveCols.Size = new System.Drawing.Size(152, 22);
            this.miRemoveCols.Text = "移除勾选参数";
            this.miRemoveCols.Click += new System.EventHandler(this.miRemoveCols_Click);
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(780, 464);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 10;
            this.btnOK.Text = "确定";
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(861, 464);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 11;
            this.btnCancel.Text = "取消";
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnSave.Location = new System.Drawing.Point(12, 464);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(75, 23);
            this.btnSave.TabIndex = 10;
            this.btnSave.Text = "保存配置";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // cmsCustom
            // 
            this.cmsCustom.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miAddGrp,
            this.miRemoveGrp});
            this.cmsCustom.Name = "cmsCustom";
            this.cmsCustom.Size = new System.Drawing.Size(180, 48);
            this.cmsCustom.Opening += new System.ComponentModel.CancelEventHandler(this.cmsCustom_Opening);
            // 
            // miAddGrp
            // 
            this.miAddGrp.Name = "miAddGrp";
            this.miAddGrp.Size = new System.Drawing.Size(179, 22);
            this.miAddGrp.Text = "将勾选的指标分组...";
            this.miAddGrp.Click += new System.EventHandler(this.miAddGrp_Click);
            // 
            // miRemoveGrp
            // 
            this.miRemoveGrp.Name = "miRemoveGrp";
            this.miRemoveGrp.Size = new System.Drawing.Size(179, 22);
            this.miRemoveGrp.Text = "移除自定义指标组";
            this.miRemoveGrp.Click += new System.EventHandler(this.miRemoveGrp_Click);
            // 
            // CellParamSettingDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(957, 499);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.splitContainerControl2);
            this.MinimumSize = new System.Drawing.Size(800, 472);
            this.Name = "CellParamSettingDlg";
            this.Text = "参数显示设置";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.tabControlShow.ResumeLayout(false);
            this.pageCustom.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeListCustom)).EndInit();
            this.pageSel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeListParams)).EndInit();
            this.cmsTable.ResumeLayout(false);
            this.cmsCustom.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraTreeList.TreeList treeListCustom;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colRoleName;
        private DevExpress.XtraTreeList.Columns.TreeListColumn colRoleDesc;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraTreeList.TreeList treeListParams;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn2;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn3;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.ContextMenuStrip cmsTable;
        private System.Windows.Forms.ToolStripMenuItem miAddTable;
        private System.Windows.Forms.ToolStripMenuItem miRemoveTables;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miRemoveCols;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn1;
        private System.Windows.Forms.TabControl tabControlShow;
        private System.Windows.Forms.TabPage pageCustom;
        private System.Windows.Forms.TabPage pageSel;
        private System.Windows.Forms.ListView lvColSelected;
        private System.Windows.Forms.Button buttonColumnDown;
        private System.Windows.Forms.Button buttonColumnUp;
        private DevExpress.XtraEditors.SimpleButton btnSave;
        private System.Windows.Forms.ContextMenuStrip cmsCustom;
        private System.Windows.Forms.ToolStripMenuItem miAddGrp;
        private System.Windows.Forms.ToolStripMenuItem miRemoveGrp;

    }
}