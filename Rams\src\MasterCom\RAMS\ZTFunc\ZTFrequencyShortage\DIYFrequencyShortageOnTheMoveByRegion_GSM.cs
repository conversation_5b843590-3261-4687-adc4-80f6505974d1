﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class DIYFrequencyShortageOnTheMoveByRegion_GSM : DIYFrequencyShortageOnTheMoveByRegion_GSCAN
    {
        public DIYFrequencyShortageOnTheMoveByRegion_GSM(MainModel mainModel)
            : base(mainModel)
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.GSM_VOICE);
            ServiceTypes.Add(ServiceType.GPRS_DATA);
            ServiceTypes.Add(ServiceType.EDGE_DATA);
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12058, this.Name);//////
        }

        readonly FrequencyShortageOnTheMoveDlg_GSM conditionDlg = new FrequencyShortageOnTheMoveDlg_GSM();
        protected override bool getCondition()
        {
            if (conditionDlg.ShowDialog() == DialogResult.OK)
            {
                conditionDlg.GetFilterCondition(out rxLevDValue, out secondLast, out rxLevDValueOther, out freqCountRateThreshold,
                    out secondFading, out rxLevDValueFading);
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    clearIntermediateVariable();
                    for (int i = 0; i < testPointList.Count; i++)
                    {
                        TestPoint testPoint = testPointList[i];
                        if (isValidTestPoint(testPoint))
                        {
                            Dictionary<Cell, float> cellRxLevDic = getCellRxLevDic(testPointList, i, testPoint);

                            judgeTestPoint(testPointList, i, cellRxLevDic);
                        }
                        else
                        {
                            clearIntermediateVariable();
                        }
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private Dictionary<Cell, float> getCellRxLevDic(List<TestPoint> testPointList, int i, TestPoint testPoint)
        {
            Dictionary<Cell, float> cellRxLevDic = new Dictionary<Cell, float>();
            float? rxLevMain = (float?)(short?)testPoint["RxLevSub"];
            Cell mainCell = CellManager.GetInstance().GetNearestCell(testPoint.DateTime, (ushort?)(int?)testPoint["LAC"], (ushort?)(int?)testPoint["CI"], (short?)testPoint["BCCH"], (byte?)testPoint["BSIC"], testPoint.Longitude, testPoint.Latitude);
            if (mainCell != null)
            {
                cellRxLevDic[mainCell] = (float)rxLevMain;
                judgeCell(rxLevMain, rxLevMain, mainCell, testPointList, i);
            }
            for (int j = 0; j < 50; j++)
            {
                float? rxLev = (float?)(short?)testPoint["N_RxLev", j];
                if (rxLev == null || rxLev > -10 || rxLev < -120)
                {
                    break;
                }
                short? bcch = (short?)testPoint["N_BCCH", j];
                byte? bsic = (byte?)testPoint["N_BSIC", j];
                Cell cell = CellManager.GetInstance().GetNearestCell(testPoint.DateTime, (short)bcch, (byte)bsic, testPoint.Longitude, testPoint.Latitude);
                if (cell != null)
                {
                    cellRxLevDic[cell] = (float)rxLev;
                    judgeCell(rxLevMain, rxLev, cell, testPointList, i);
                }
            }

            return cellRxLevDic;
        }
    }
}
