﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FrequencyShortageDlg_GSCAN : BaseDialog
    {
        public FrequencyShortageDlg_GSCAN()
        {
            InitializeComponent();
            cbxBandType.SelectedIndex = 0;
        }

        public void GetFilterCondition(out int bandType, out int rxLevDValue, out int secondLast, out int rxLevDValueOther, out int freqCountRateThreshold)
        {
            bandType = cbxBandType.SelectedIndex;
            rxLevDValue = (int)numRxLevDValue.Value;
            secondLast = (int)numSecondLast.Value;
            rxLevDValueOther = (int)numRxLevDValueOther.Value;
            freqCountRateThreshold = (int)numFreqCountRateThreshold.Value;
        }
    }
}
