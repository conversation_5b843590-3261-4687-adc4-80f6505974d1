﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteSignalToInterenceDataForm : MinCloseForm
    {
        MapForm mapForm;
        public LteSignalToInterenceDataForm() : base(MainModel.GetInstance())
        {
            InitializeComponent();
            this.mapForm = MainModel.MainForm.GetMapForm();
            this.mModel = mapForm.MainModel;
        }

        int iRsrpDiff = 6;
        int iSignalCellCount = 1;
        public static double dGridRate { get; set; } = 0.3;
        public static List<LteSignalToInterferenceItem> result { get; set; } = null;

        public void GetLteSignalToInterenceSet()
        {
            double rate;
            if (txtRate.Text == null || txtRate.Text == ""
                || !double.TryParse(txtRate.Text.ToString(), out rate))
            {
                MessageBox.Show("输入干扰指数门限值有误，请检查！");
                return;
            }
            dGridRate = rate;
            iRsrpDiff = int.Parse(nuRSRPDIFF.Value.ToString());
            iSignalCellCount = int.Parse(numSignalCellCount.Value.ToString());
        }
   
        public void FillAndCalData()
        {
            GetLteSignalToInterenceSet();
            result = new List<LteSignalToInterferenceItem>();
            foreach (LTECell lteCell in DIYQueryLteSignalToInterenceByRegion.lteCellGridUnit.Keys)
            {
                LteSignalToInterferenceItem lteSignalItemInfo = new LteSignalToInterferenceItem();
                lteSignalItemInfo.ISN = result.Count + 1;
                lteSignalItemInfo.LteCell = lteCell;
                lteSignalItemInfo.StrCellName = lteCell.Name;
                lteSignalItemInfo.ITac = lteCell.TAC;
                lteSignalItemInfo.IEci = lteCell.ECI;
                int iSignalCellGridCount = 0;
                double dSignalRate = 0;
                lteSignalItemInfo.ICellGridCount = DIYQueryLteSignalToInterenceByRegion.lteCellGridUnit[lteCell].Count;
                CalSignalToInterference(lteCell,DIYQueryLteSignalToInterenceByRegion.lteCellGridUnit[lteCell]
                    , ref iSignalCellGridCount, ref dSignalRate);
                lteSignalItemInfo.ISignalCellGridCount = iSignalCellGridCount;
                lteSignalItemInfo.DSignalRate = dSignalRate;
                if (dSignalRate < dGridRate)
                {
                    continue;
                }

                result.Add(lteSignalItemInfo);
            }
            this.dataGrid.DataSource = result;
            this.dataGrid.RefreshDataSource();
        }

        private void CalSignalToInterference(LTECell lteCell ,List<string> strGridlist, ref int SignalToInterferenceCount, ref double dGridRate)
        {
            foreach (string strGrid in strGridlist)
            {
                List<GridCellInfos> gridList = DIYQueryLteSignalToInterenceByRegion.lteColorUnitGridCellInfos[strGrid];
                for(int i = 0; i < gridList.Count;i++)
                {
                    if (gridList[i].LteCell == lteCell)
                    {
                        SignalToInterferenceCount = gridList[i].iGridLteCell(gridList[i].FRsrp - iRsrpDiff);
                        break;
                    }
                }
            }
            if (SignalToInterferenceCount < iSignalCellCount)
            {
                return;
            }
            dGridRate = 1.0 * SignalToInterferenceCount / strGridlist.Count;
        }

        private void btnCalAgin_Click(object sender, EventArgs e)
        {
            FillAndCalData();
            MainModel.MainForm.FireLteSignalToInterenceQueried();
        }

        private void outPutExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.gridView1);
        }

        private void dataGrid_Click(object sender, EventArgs e)
        {
            int[] row = gridView1.GetSelectedRows();
            if (row.Length == 0)
                return;
            object o = gridView1.GetRow(row[0]);
            LteSignalToInterferenceItem lteSiganalItem = o as LteSignalToInterferenceItem;
            mapForm.CurSelLteSignalToInterenceID = lteSiganalItem.ISN;
            DbPoint center = new DbPoint(lteSiganalItem.LteCell.Longitude, lteSiganalItem.LteCell.Latitude);
            mapForm.GoToView(center.x, center.y, 3200);
        }
    }
}
