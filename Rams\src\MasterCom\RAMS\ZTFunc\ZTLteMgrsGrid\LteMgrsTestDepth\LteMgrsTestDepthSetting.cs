﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsTestDepthSetting : LteMgrsConditionControlBase
    {
        public LteMgrsTestDepthSetting()
        {
            InitializeComponent();
        }

        public override string Title
        {
            get { return "测试深度"; }
        }

        public override object GetCondition(out string invalidReason)
        {
            if (dtStart.Value.Date > dtEnd.Value.Date)
            {
                invalidReason = "开始时间不能大于结束时间";
                return null;
            }

            invalidReason = null;
            return new object[] { 
                chkEnable.Checked,
                new TimePeriod(this.dtStart.Value.Date, this.dtEnd.Value.Date.AddDays(1).AddSeconds(-1))
            };
        }
    }
}
