﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc 
{
    public partial class GridCompareCountForm : MinCloseForm
    {
        public GridCompareCountForm(MainModel mainModel, bool isMonthForward)
            : base(mainModel)
        {
            InitializeComponent();
            xtraTabPage2.PageVisible = false;
        }

        List<GridMutCarriersCountInfo> gridMutCarriersCountInfoList = null;
        List<GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfoList = null;
        bool GetShowGridDataInfo = false;
        public void FillData(List<GridMutCarriersCountInfo> gridMutCarriersCountInfoList
            , List<GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfoList, bool GetShowGridDataInfo)
        {
            BindingSource source = new BindingSource();
            source.DataSource = gridMutCarriersCountInfoList;
            dataGrid.DataSource = source;
            dataGrid.RefreshDataSource();

            if (GetShowGridDataInfo)
            {
                xtraTabPage2.PageVisible = true;
                BindingSource sourceIfno = new BindingSource();
                sourceIfno.DataSource = gridDownLoadTimeSpeedInfoList;
                dataGridInfo.DataSource = sourceIfno;
                dataGridInfo.RefreshDataSource();
            }
            this.gridMutCarriersCountInfoList = new List<GridMutCarriersCountInfo>();
            this.gridDownLoadTimeSpeedInfoList = new List<GridDownLoadTimeSpeedInfo>();
            this.gridMutCarriersCountInfoList = gridMutCarriersCountInfoList;
            this.gridDownLoadTimeSpeedInfoList = gridDownLoadTimeSpeedInfoList;
            this.GetShowGridDataInfo = GetShowGridDataInfo;
        }

        private void conOutExcel_Click(object sender, EventArgs e)
        {
             //ExcelNPOIManager.ExportToExcel(this.gridView1);
             List<NPOIRow> dataTotal = new List<NPOIRow>();
             List<NPOIRow> datasDetails = new List<NPOIRow>();
             List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
             List<string> sheetNames = new List<string>();

             #region 导出汇总
             NPOIRow nrTotal = new NPOIRow();
             List<object> colsTotal = new List<object>();
             colsTotal.Add("序号");
             colsTotal.Add("地市");
             colsTotal.Add("网格类型");
             colsTotal.Add("网格号");
             colsTotal.Add("含历史主优栅格总数（低于25M或关系）");
             colsTotal.Add("含历史主劣栅格总数（低于25M或关系）");
             colsTotal.Add("含历史栅格优胜率（低于25M或关系）");
             colsTotal.Add("本月主优栅格总数（低于25M或关系）");
             colsTotal.Add("本月主劣栅格总数（低于25M或关系）");
             colsTotal.Add("本月栅格优胜率（低于25M或关系）");
             colsTotal.Add("本月主优栅格总数（低于25M或关系）--不含脱网");
             colsTotal.Add("本月主劣栅格总数（低于25M或关系）--不含脱网");
             colsTotal.Add("本月栅格优胜率（低于25M或关系）--不含脱网");
             colsTotal.Add("主队原始数据");
             colsTotal.Add("客队原总栅格数");
             colsTotal.Add("总栅格数(并集)");
             colsTotal.Add("主队本月有下载栅格数");
             colsTotal.Add("主队本月有下载栅格数占总栅格数比例");
             colsTotal.Add("客队本月有下载栅格数");
             colsTotal.Add("客队本月有下载栅格数占总栅格比例");
             colsTotal.Add("本月不含脱网对比总栅格数");
             colsTotal.Add("本月含脱网对比总栅格数");
             colsTotal.Add("本月含脱网对比栅格数占总栅格比例");
             colsTotal.Add("主队含历史有下载栅格数");
             colsTotal.Add("主队含历史有下载栅格数占总栅格比例");
             colsTotal.Add("客队含历史有下载栅格数");
             colsTotal.Add("客队含历史有下载栅格数占总栅格比例");
             colsTotal.Add("含历史不含脱网对比总栅格数");
             colsTotal.Add("含历史对比总栅格数");
             colsTotal.Add("含历史对比栅格数占总栅格比例");
             colsTotal.Add("主队历史栅格数");
             colsTotal.Add("主队历史栅格占含历史对比总栅格比例");
             colsTotal.Add("主队历史栅格数列表");
             colsTotal.Add("主队历史栅格比例列表");
             colsTotal.Add("客队历史栅格数");
             colsTotal.Add("客队历史栅格数占含历史对比总栅格比例");
             colsTotal.Add("客队历史栅格数列表");
             colsTotal.Add("客队历史栅格比例列表");
             colsTotal.Add("主队脱网栅格数");
             colsTotal.Add("主队脱网(脱网标识)");
             colsTotal.Add("主队脱网(占它网时长)");
             colsTotal.Add("主队脱网(无RSRP采样点)");
             colsTotal.Add("客队脱网栅格数");
             colsTotal.Add("客队脱网栅格占总栅格比例");
             colsTotal.Add("客队脱网栅格占含历史对比栅格比例");
             colsTotal.Add("客队脱网(脱网标记)");
             colsTotal.Add("客队脱网(占它网时长)");
             colsTotal.Add("客队脱网(无RSRP采样点)");
             colsTotal.Add("同高优栅格数");
             colsTotal.Add("同高劣栅格数");
             colsTotal.Add("同高优胜率");
             colsTotal.Add("同低优栅格数");
             colsTotal.Add("同低劣栅格数");
             colsTotal.Add("同低优胜率");
             colsTotal.Add("主高客低栅格数");
             colsTotal.Add("主低客高优栅格数");
             colsTotal.Add("主低客高劣栅格数");

             nrTotal.cellValues = colsTotal;
             dataTotal.Add(nrTotal);

             foreach (GridMutCarriersCountInfo gridCountInfo in gridMutCarriersCountInfoList)
             {
                 NPOIRow nr = new NPOIRow();
                 List<object> objs = new List<object>();

                 objs.Add(gridCountInfo.ISN.ToString());
                 objs.Add(gridCountInfo.StrCity.ToString());
                 objs.Add(gridCountInfo.StrGridType.ToString());
                 objs.Add(gridCountInfo.StrGridName.ToString());
                 objs.Add(gridCountInfo.IHostGoodGrid.ToString());
                 objs.Add(gridCountInfo.IHostWeakGrid.ToString());
                 objs.Add(gridCountInfo.StrHostGoodRate.ToString());
                 objs.Add(gridCountInfo.IHostGoodGridCurMonth.ToString());
                 objs.Add(gridCountInfo.IHostWeakGridCurMonth.ToString());
                 objs.Add(gridCountInfo.StrHostGoodRateCurMonth.ToString());
                 objs.Add(gridCountInfo.I本月主优栅格总数_不含脱网.ToString());
                 objs.Add(gridCountInfo.I本月主劣栅格总数_不含脱网.ToString());
                 objs.Add(gridCountInfo.Str本月栅格优胜率_不含脱网.ToString());
                 objs.Add(gridCountInfo.IHostGrid.ToString());
                 objs.Add(gridCountInfo.IGuestGrid.ToString());
                 objs.Add(gridCountInfo.IAllGrid.ToString());
                 objs.Add(gridCountInfo.I主队本月下载栅格数.ToString());
                 objs.Add(gridCountInfo.StrHostDwonloadGridPercent.ToString());
                 objs.Add(gridCountInfo.I客队本月下载栅格数.ToString());
                 objs.Add(gridCountInfo.StrGuestDwonloadGridPercent.ToString());
                 objs.Add(gridCountInfo.I本月不含脱网对比总栅格数.ToString());
                 objs.Add(gridCountInfo.I本月对比总栅格数.ToString());
                 objs.Add(gridCountInfo.Str本月含脱网对比栅格数占总栅格比例.ToString());
                 objs.Add(gridCountInfo.I含历史主队下载栅格数.ToString());
                 objs.Add(gridCountInfo.StrHostHistoryDwonloadGridPercent.ToString());
                 objs.Add(gridCountInfo.I含历史客队下载栅格数.ToString());
                 objs.Add(gridCountInfo.StrGuestHistoryDwonloadGridPercent.ToString());
                 objs.Add(gridCountInfo.I含历史不含脱网对比总栅格数.ToString());
                 objs.Add(gridCountInfo.ICompareGrid.ToString());
                 objs.Add(gridCountInfo.Str含历史对比栅格数占总栅格比例.ToString());
                 objs.Add(gridCountInfo.IHostHistoryGrid.ToString());
                 objs.Add(gridCountInfo.str主队占含历史对比总数比例.ToString());
                 objs.Add(gridCountInfo.StrHostHistoryGrid.ToString());
                 objs.Add(gridCountInfo.StrHostHistoryGridPercent.ToString());
                 objs.Add(gridCountInfo.IGuestHistoryGrid.ToString());
                 objs.Add(gridCountInfo.str客队占含历史对比总数比例.ToString());
                 objs.Add(gridCountInfo.StrGuestHistoryGrid.ToString());
                 objs.Add(gridCountInfo.StrGuestHistoryGridPercent.ToString());
                 objs.Add(gridCountInfo.I主队脱网栅格数.ToString());
                 objs.Add(gridCountInfo.I主队脱网栅格数_脱网标记.ToString());
                 objs.Add(gridCountInfo.I主队脱网栅格数_占它网时长.ToString());
                 objs.Add(gridCountInfo.I主队脱网栅格数_无RSRP采样点.ToString());
                 objs.Add(gridCountInfo.I客队脱网栅格数.ToString());
                 objs.Add(gridCountInfo.str客队脱网占总栅格数比例.ToString());
                 objs.Add(gridCountInfo.str客队脱网占含历史总栅格数比例.ToString());
                 objs.Add(gridCountInfo.I客队脱网栅格数_脱网标记.ToString());
                 objs.Add(gridCountInfo.I客队脱网栅格数_占它网时长.ToString());
                 objs.Add(gridCountInfo.I客队脱网栅格数_无RSRP采样点.ToString());
                 objs.Add(gridCountInfo.IMoreGoodGrid.ToString());
                 objs.Add(gridCountInfo.IMoreWeakGrid.ToString());
                 objs.Add(gridCountInfo.StrMoreGoodRate.ToString());
                 objs.Add(gridCountInfo.ILessGoodGrid.ToString());
                 objs.Add(gridCountInfo.ILessWeakGrid.ToString());
                 objs.Add(gridCountInfo.StrLessGoodRate.ToString());
                 objs.Add(gridCountInfo.IHostMoreGuestLess.ToString());
                 objs.Add(gridCountInfo.IHostLessGuestMore.ToString());
                 objs.Add(gridCountInfo.IHostLessGuestMoreWeak.ToString());

                 nr.cellValues = objs;
                 dataTotal.Add(nr);
             }
             nrDatasList.Add(dataTotal);
             sheetNames.Add("优胜率对比汇总");
             #endregion

             #region 导出详情
             NPOIRow nrDetails = new NPOIRow();
             List<object> colsDetails = new List<object>();
             colsDetails.Add("地市");
             colsDetails.Add("网格类型");
             colsDetails.Add("网格号");
             colsDetails.Add("栅格中心经纬度");
             colsDetails.Add("左上经度");
             colsDetails.Add("左上纬度");
             colsDetails.Add("中心经度");
             colsDetails.Add("中心纬度");
             colsDetails.Add("TDD_RSRP采样点数");
             colsDetails.Add("TDD_下载采样点数");
             colsDetails.Add("TDD下载时长");
             colsDetails.Add("TDD下载量");
             colsDetails.Add("TDD下载速率");
             colsDetails.Add("主队脱网标记");
             colsDetails.Add("主队占它网时长");
             colsDetails.Add("主队栅格状态");
             colsDetails.Add("主队数据源");
             colsDetails.Add("FDD_RSRP采样点数");
             colsDetails.Add("FDD_下载采样点数");
             colsDetails.Add("FDD下载时长");
             colsDetails.Add("FDD下载量");
             colsDetails.Add("FDD下载速率");
             colsDetails.Add("客队脱网标记");
             colsDetails.Add("客队占它网时长");
             colsDetails.Add("客队栅格状态");
             colsDetails.Add("客队数据源");

             nrDetails.cellValues = colsDetails;
             datasDetails.Add(nrDetails);
             if (GetShowGridDataInfo)
             {
                 foreach (GridDownLoadTimeSpeedInfo gridInfo in gridDownLoadTimeSpeedInfoList)
                 {
                     NPOIRow nr = new NPOIRow();
                     List<object> objs = new List<object>();

                     objs.Add(gridInfo.StrCityInfo.ToString());
                     objs.Add(gridInfo.StrGridType.ToString());
                     objs.Add(gridInfo.StrGridName.ToString());
                     objs.Add(gridInfo.StrGirdCenterInfo.ToString());
                     objs.Add(gridInfo.Itllng.ToString());
                     objs.Add(gridInfo.Itllat.ToString());
                     objs.Add(gridInfo.Icentlng.ToString());
                     objs.Add(gridInfo.Icentlat.ToString());
                     objs.Add(gridInfo.D主队RSRP采样点数.ToString());
                     objs.Add(gridInfo.DTDDSampleNum.ToString());
                     objs.Add(gridInfo.DTDDDownTime.ToString());
                     objs.Add(gridInfo.DTDDDownSize.ToString());
                     objs.Add(gridInfo.DTDDDownSpeed.ToString());
                     objs.Add(gridInfo.D主队脱网标记.ToString());
                     objs.Add(gridInfo.D主队占它网时长.ToString());
                     objs.Add(gridInfo.StrGridTDDStatstatus.ToString());
                     objs.Add(gridInfo.Str主队数据源.ToString());
                     objs.Add(gridInfo.D客队RSRP采样点数.ToString());
                     objs.Add(gridInfo.DFDDSampleNum.ToString());
                     objs.Add(gridInfo.DFDDDownTime.ToString());
                     objs.Add(gridInfo.DFDDDownSize.ToString());
                     objs.Add(gridInfo.DFDDDownSpeed.ToString());
                     objs.Add(gridInfo.D客队脱网标记.ToString());
                     objs.Add(gridInfo.D客队占它网时长.ToString());
                     objs.Add(gridInfo.StrGridFDDStatstatus.ToString());
                     objs.Add(gridInfo.Str客队数据源.ToString());

                     nr.cellValues = objs;
                     datasDetails.Add(nr);
                 }
                 nrDatasList.Add(datasDetails);
                 sheetNames.Add("优胜率对比明细");
             }
             #endregion

             ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }
    }
}
