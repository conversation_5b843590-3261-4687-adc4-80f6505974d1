﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class NRSmallStationAcceptManager : NRStationAcceptManager
    {
        protected override string templateFileName { get { return "NR小站验收模板.xlsx"; } }
        protected override string reportFileName { get { return "NR小站验收"; } }

        public override void SetAcceptCond(StationAcceptConditionBase cond)
        {
            //Singleton<NRSmallStationAcceptConfigHelper>.Instance.LoadConfig();
            //acceptCond = cond;

            //acceptorList = new List<StationAcceptBase>()
            //{
            //    new NRSmallAcpCellAddRate(),
            //    new NRSmallAcpAccRate(),
            //    new NRSmallAcpGoodPointFtpDownload(),
            //    new NRSmallAcpGoodPointFtpUpload(),
            //    new NRSmallAcpBadPointFtpDownload(),
            //    new NRSmallAcpBadPointFtpUpload(),
            //    new NRSmallAcpEPSFBRate(),
            //    new NRSmallAcpEPSFBDelay(),
            //    new NRSmallAcpPingDelay(),
            //    new NRSmallAcpLeakOutLock(),
            //    new NRSmallAcpHandoverRate(),
            //    new NRSmallAcpCoverPic(),
            //};
        }

        protected virtual NRCell GetTargetCell(DTFileDataManager fileManager, string btsName, string pciStr)
        {
            NRCell targeCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                int? arfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
                int? pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
                if (arfcn == null || pci == null)
                {
                    continue;
                }
                List<NRCell> cellList = CellManager.GetInstance().GetNRCellListByARFCNPCI(tp.DateTime, arfcn, pci);
                if (cellList == null)
                {
                    continue;
                }
                targeCell = getValidCell(btsName, pciStr, targeCell, cellList);
                if (targeCell != null)
                {
                    break;
                }
            }
            return targeCell;
        }

        private NRCell getValidCell(string btsName, string pci, NRCell targeCell, List<NRCell> cellList)
        {
            foreach (var cell in cellList)
            {
                if (cell.Name.Contains(btsName) && cell.PCI.ToString() == pci
                    && cell.BelongBTS.Type == NRBTSType.Indoor)
                {
                    targeCell = cell;
                    break;
                }
            }

            return targeCell;
        }

        public override void AnalyzeFile(Model.FileInfo fileInfo, DTFileDataManager fileManager)
        {
            try
            {
                //string errMsg = SmallHelper.JudgeValidFileName(fileInfo.Name);
                //if (!string.IsNullOrEmpty(errMsg))
                //{
                //    log.Info(errMsg);
                //    ErrMsg.AppendLine(errMsg);
                //    return;
                //}

                //NRCell targetCell = GetTargetCell(fileManager, SmallHelper.BtsNameByFileName, SmallHelper.PCIByFileName);
                //if (targetCell == null)
                //{
                //    errMsg = string.Format("文件{0}未找到目标小区;", fileInfo.Name);
                //    log.Info(errMsg);
                //    ErrMsg.AppendLine(errMsg);
                //    return;
                //}

                //analyzeFile(fileInfo, fileManager, targetCell, SmallHelper.BtsNameByFileName);
            }
            catch (Exception e)
            {
                string errMsg = $"[{fileInfo.Name}]文件分析时产生异常;";
                log.Error(string.Format("{0} : {1}", errMsg, e.StackTrace));
                ErrMsg.AppendLine(errMsg);
            }
        }

        protected void analyzeFile(Model.FileInfo fileInfo, DTFileDataManager fileManager, NRCell targetCell, string btsName)
        {
            NRSmallStationAcceptCondition curCond = acceptCond as NRSmallStationAcceptCondition;
            curCond.NRServiceType = NRStationAcceptFileNameHelper.GetServiceName(fileInfo.Name);
            if (curCond.NRServiceType == NRServiceName.NULL)
            {
                string errMsg = $"[{fileInfo.Name}]不为SA或NSA文件";
                log.Debug(errMsg);
                ErrMsg.AppendLine(errMsg);
                return;
            }
            if (!BtsInfoDic.TryGetValue(btsName, out BtsInfoBase bts))
            {
                bts = initBtsInfo(targetCell, btsName);
                BtsInfoDic.Add(btsName, bts);
            }
            //NRSmallBtsInfo nrBtsInfo = bts as NRSmallBtsInfo;
            //nrBtsInfo.Init(curCond.NRServiceType);
            if (!bts.CellInfoDic.TryGetValue(targetCell.Name, out CellInfoBase cell))
            {
                cell = initCellInfo(targetCell);
                bts.CellInfoDic.Add(targetCell.Name, cell);
            }
            NRSmallCellInfo nrCellInfo = cell as NRSmallCellInfo;
            nrCellInfo.Init(targetCell, curCond.NRServiceType);

            foreach (StationAcceptBase acp in acceptorList)
            {
                acp.AnalyzeFile(fileInfo, fileManager, bts, cell, curCond);
            }
        }

        protected override BtsInfoBase initBtsInfo(NRCell targetCell, string fileBtsName)
        {
            return new NRSmallBtsInfo(targetCell.BelongBTS, fileBtsName);
        }

        protected override CellInfoBase initCellInfo(NRCell targetCell)
        {
            return new NRSmallCellInfo(targetCell);
        }

        protected override void verifyResult()
        {
            foreach (var btsInfo in BtsInfoDic.Values)
            {
                //NRSmallBtsInfo nrBtsInfo = btsInfo as NRSmallBtsInfo;
           
            }
        }

        protected override void getExportedFiles(StringBuilder exportedFiles, BtsInfoBase bts)
        {
            NRSmallBtsInfo nrBtsInfo = bts as NRSmallBtsInfo;
            exportedFiles.Append(nrBtsInfo.FileBtsName);
            exportedFiles.Append(",");
        }

        protected override void fillResult(BtsInfoBase bts, Excel.Workbook eBook)
        {
            //
        }
    }
}
