﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.Net
{
    public class DIYAnalyseByPeriodBackgroundBase_Sample : DIYSampleQuery
    {
        /// <summary>
        /// 汇聚半径
        /// </summary>
        public int GatherRadius { get; set; } = 200;
        public DIYAnalyseByPeriodBackgroundBase_Sample(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Region; }
        }

        public override string Name
        {
            get { return "按区域专题分析基类"; }
        }
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.None; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11011, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override void prepareStatPackage_Sample_FileFilter(Package package, TimePeriod period, bool byRound)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            if (!isPreSetted)
            {
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_COVER_SAMPLE;
            }
            else
            {
                package.Content.Type = this.preSettedCommandType;
            }
            package.Content.PrepareAddParam();
            if (byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            AddDIYRegion_Intersect(package);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);

            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            //
            AddDIYEndOpFlag(package);

        }
        protected override void prepareStatPackage_Sample_SampleFilter(Package package, TimePeriod period)
        {
            AddDIYRegion_Sample(package);
            AddDIYEndOpFlag(package);
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                return Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
            }
            catch
            {
                try
                {
                    return Condition.Geometorys.GeoOp2.CheckPointInRegion(tp.Longitude, tp.Latitude);//网络体检后台
                }
                catch (Exception ex)
                {
                    log.Error(ex.StackTrace);
                }
                return false;
            }
        }

        protected override void query()
        {
            if (MainModel.IsBackground && !MainModel.BackgroundStarted)
            {
                return;
            }
            if (!getConditionBeforeQuery())
            {
                return;
            }
            curSelDIYSampleGroup = getCurSelDIYSampleGroupPrepare();
            if (curSelDIYSampleGroup == null)
            {
                return;
            }
            WaitBox.CanCancel = true;
            if (!MainModel.IsBackground && !MainModel.QueryFromBackground)
            {
                WaitBox.Text = "正在查询...";
            }
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                MainModel.ClearDTData();
                doQuery(clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void doQuery(ClientProxy clientProxy)
        {
            if (!MainModel.IsBackground)
            {
                if (!MainModel.QueryFromBackground)
                {
                    WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                    if (curSelDIYSampleGroup.ThemeName != null && curSelDIYSampleGroup.ThemeName != "")
                    {
                        MainModel.FireSetDefaultMapSerialTheme(curSelDIYSampleGroup.ThemeName);
                    }
                    FireShowFormAfterQuery();
                    MainModel.FireDTDataChanged(this);
                }
                else
                {
                    getBackgroundData();
                    initBackgroundImageDesc();
                }
            }
            else
            {
                doBackgroundStatByPeriod(clientProxy);
            }
        }

        protected override List<TimePeriod> GetStatedTimePeriod()
        {
            return BackgroundFuncQueryManager.GetInstance().GetStatedTimePeriod_Region(GetSubFuncID(), BackgroundFuncConfigManager.GetInstance().ProjectType);
        }

        protected override void statData(ClientProxy clientProxy)
        {
            queryInThread(clientProxy);
        }

        protected override void getBackgroundData()
        {
            BackgroundResultList = BackgroundFuncQueryManager.GetInstance().GetResult_Region(Condition.Periods[0].IBeginTime,
                            Condition.Periods[0].IEndTime, GetSubFuncID(), Name, StatType, BackgroundFuncConfigManager.GetInstance().ProjectType);
        }

        protected virtual bool isValidBackgroundResult(double longitude, double latitude)
        {
            try
            {
                if (Condition.Geometorys == null)
                {
                    return true;
                }
                return Condition.Geometorys.GeoOp.Contains(longitude, latitude);
            }
            catch
            {
                return false;
            }
        }
    }
}
