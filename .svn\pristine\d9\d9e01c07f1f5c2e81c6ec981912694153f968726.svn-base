﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTMainCellLastOccupyResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.treeViewCellOccupy = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDuration = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgRSRP = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgSINR = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRPNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSINRNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTestPointNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            this.olvColumnTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            ((System.ComponentModel.ISupportInitialize)(this.treeViewCellOccupy)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeViewCellOccupy
            // 
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnStatSN);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnFileName);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnStatCellName);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnECI);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnTAC);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnDuration);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnLongitude);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnLatitude);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnTime);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnAvgRSRP);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnAvgSINR);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnRSRPNum);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnSINRNum);
            this.treeViewCellOccupy.AllColumns.Add(this.olvColumnTestPointNum);
            this.treeViewCellOccupy.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnFileName,
            this.olvColumnStatCellName,
            this.olvColumnECI,
            this.olvColumnTAC,
            this.olvColumnDuration,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnTime,
            this.olvColumnAvgRSRP,
            this.olvColumnAvgSINR,
            this.olvColumnRSRPNum,
            this.olvColumnSINRNum,
            this.olvColumnTestPointNum});
            this.treeViewCellOccupy.ContextMenuStrip = this.contextMenuStrip1;
            this.treeViewCellOccupy.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeViewCellOccupy.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeViewCellOccupy.FullRowSelect = true;
            this.treeViewCellOccupy.GridLines = true;
            this.treeViewCellOccupy.HeaderWordWrap = true;
            this.treeViewCellOccupy.IsNeedShowOverlay = false;
            this.treeViewCellOccupy.Location = new System.Drawing.Point(0, 0);
            this.treeViewCellOccupy.Name = "treeViewCellOccupy";
            this.treeViewCellOccupy.OwnerDraw = true;
            this.treeViewCellOccupy.ShowGroups = false;
            this.treeViewCellOccupy.Size = new System.Drawing.Size(935, 455);
            this.treeViewCellOccupy.TabIndex = 9;
            this.treeViewCellOccupy.UseCompatibleStateImageBehavior = false;
            this.treeViewCellOccupy.View = System.Windows.Forms.View.Details;
            this.treeViewCellOccupy.VirtualMode = true;
            this.treeViewCellOccupy.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeViewCellOccupy_MouseDoubleClick);
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            this.olvColumnStatSN.Width = 80;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 150;
            // 
            // olvColumnStatCellName
            // 
            this.olvColumnStatCellName.HeaderFont = null;
            this.olvColumnStatCellName.Text = "小区名称";
            this.olvColumnStatCellName.Width = 120;
            // 
            // olvColumnECI
            // 
            this.olvColumnECI.HeaderFont = null;
            this.olvColumnECI.Text = "CI/ECI";
            // 
            // olvColumnDuration
            // 
            this.olvColumnDuration.HeaderFont = null;
            this.olvColumnDuration.Text = "占用时长(秒)";
            this.olvColumnDuration.Width = 90;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 90;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 90;
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "时间";
            this.olvColumnTime.Width = 120;
            // 
            // olvColumnAvgRSRP
            // 
            this.olvColumnAvgRSRP.HeaderFont = null;
            this.olvColumnAvgRSRP.Text = "平均电平";
            // 
            // olvColumnAvgSINR
            // 
            this.olvColumnAvgSINR.HeaderFont = null;
            this.olvColumnAvgSINR.Text = "平均质量";
            // 
            // olvColumnRSRPNum
            // 
            this.olvColumnRSRPNum.HeaderFont = null;
            this.olvColumnRSRPNum.Text = "低电平采样点数量";
            // 
            // olvColumnSINRNum
            // 
            this.olvColumnSINRNum.HeaderFont = null;
            this.olvColumnSINRNum.Text = "质差采样点数量";
            // 
            // olvColumnTestPointNum
            // 
            this.olvColumnTestPointNum.HeaderFont = null;
            this.olvColumnTestPointNum.Text = "总采样点个数";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExport});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(137, 26);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(136, 22);
            this.ToolStripMenuItemExport.Text = "导出到xls...";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // olvColumnTAC
            // 
            this.olvColumnTAC.HeaderFont = null;
            this.olvColumnTAC.Text = "LAC/TAC";
            // 
            // ZTMainCellLastOccupyResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(935, 455);
            this.Controls.Add(this.treeViewCellOccupy);
            this.Name = "ZTMainCellLastOccupyResultForm";
            this.Text = "主服小区持续占用";
            ((System.ComponentModel.ISupportInitialize)(this.treeViewCellOccupy)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeViewCellOccupy;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnDuration;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRSRP;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgSINR;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRPNum;
        private BrightIdeasSoftware.OLVColumn olvColumnSINRNum;
        private BrightIdeasSoftware.OLVColumn olvColumnTestPointNum;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private BrightIdeasSoftware.OLVColumn olvColumnECI;
        private BrightIdeasSoftware.OLVColumn olvColumnTAC;
    }
}