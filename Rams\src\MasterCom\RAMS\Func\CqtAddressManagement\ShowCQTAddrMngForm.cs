﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func.CqtAddressManagement;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class ShowCQTAddrMngForm : ShowFuncForm
    {
        public ShowCQTAddrMngForm(MainModel mm)
            : base(mm)
        { }
        CqtAddressManagementForm form = null;
        protected override void showForm()
        {
            if (form == null || form.IsDisposed)
            {
                form = new CqtAddressManagementForm(MainModel, "", -1);    //查询cqt测试点，查询全体的地点
            }
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }

        public override string Name
        {
            get { return "CQT地点管理窗口"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 21000, 21001, this.Name);
        }
    }
}
