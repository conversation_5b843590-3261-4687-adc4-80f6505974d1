﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class PhoneInfoItem
    {
        public PhoneInfoItem(string strFileName, string strPairFileName)
        {
            this.FileName = strFileName;
            this.PairFileName = strPairFileName;
        }

        public List<TestPoint> TestPoints { get; set; } = new List<TestPoint>();
        public List<Event> Events { get; set; } = new List<Event>();
        public List<Message> Messages { get; set; } = new List<Message>();

        public int SN
        {
            get;
            set;
        }

        public string FileName
        {
            get;
            set;
        }
        public string PairFileName
        {
            get;
            set;
        }
        public string PairPhoneNumber
        {
            get;
            set;
        }
        public string PairIMSI
        {
            get;
            set;
        }
        public string PairTMSI
        {
            get;
            set;
        }
    }
}
