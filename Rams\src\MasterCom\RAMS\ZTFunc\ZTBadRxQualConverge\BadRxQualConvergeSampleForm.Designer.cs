﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class BadRxQualConvergeSampleForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.gridViewCell = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStripGridControl = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExportGC = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.listViewTotal = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSampleCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxQual = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxLev = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTime = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBand = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDistance = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStripBadRxQualConverge = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemExpand = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemFold = new System.Windows.Forms.ToolStripMenuItem();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCell)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            this.contextMenuStripGridControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).BeginInit();
            this.contextMenuStripBadRxQualConverge.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            this.xtraTabPage2.SuspendLayout();
            this.SuspendLayout();
            // 
            // gridViewCell
            // 
            this.gridViewCell.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23});
            this.gridViewCell.GridControl = this.gridControl1;
            this.gridViewCell.Name = "gridViewCell";
            this.gridViewCell.OptionsBehavior.Editable = false;
            this.gridViewCell.OptionsDetail.ShowDetailTabs = false;
            this.gridViewCell.OptionsView.ColumnAutoWidth = false;
            this.gridViewCell.OptionsView.ShowGroupPanel = false;
            this.gridViewCell.DoubleClick += new System.EventHandler(this.gridViewCell_DoubleClick);
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "小区名称";
            this.gridColumn20.FieldName = "CellName";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 0;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "LAC";
            this.gridColumn21.FieldName = "LAC";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 1;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "CI";
            this.gridColumn22.FieldName = "CI";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 2;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "小区质差采样点数";
            this.gridColumn23.FieldName = "TestPointCount";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 3;
            this.gridColumn23.Width = 112;
            // 
            // gridControl1
            // 
            this.gridControl1.ContextMenuStrip = this.contextMenuStripGridControl;
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode1.LevelTemplate = this.gridViewCell;
            gridLevelNode1.RelationName = "CellList";
            this.gridControl1.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(874, 322);
            this.gridControl1.TabIndex = 1;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1,
            this.gridViewCell});
            // 
            // contextMenuStripGridControl
            // 
            this.contextMenuStripGridControl.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExportGC});
            this.contextMenuStripGridControl.Name = "contextMenuStripGridControl";
            this.contextMenuStripGridControl.Size = new System.Drawing.Size(153, 48);
            // 
            // ToolStripMenuItemExportGC
            // 
            this.ToolStripMenuItemExportGC.Name = "ToolStripMenuItemExportGC";
            this.ToolStripMenuItemExportGC.Size = new System.Drawing.Size(152, 22);
            this.ToolStripMenuItemExportGC.Text = "导出到Excel";
            this.ToolStripMenuItemExportGC.Click += new System.EventHandler(this.ToolStripMenuItemExportGC_Click);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsDetail.ShowDetailTabs = false;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.DoubleClick += new System.EventHandler(this.gridView1_DoubleClick);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "区域编号";
            this.gridColumn1.FieldName = "StrArea";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "质差采样点数";
            this.gridColumn2.FieldName = "TestPointCount";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 87;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "RxQual";
            this.gridColumn3.FieldName = "RxQualAvg";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "RxLev";
            this.gridColumn4.FieldName = "RxLevAvg";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "经度";
            this.gridColumn5.FieldName = "LongitudeMid";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "纬度";
            this.gridColumn6.FieldName = "LatitudeMid";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "路段";
            this.gridColumn7.FieldName = "RoadName";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "室分泄露";
            this.gridColumn8.FieldName = "Indoor";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 7;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "占用不合理";
            this.gridColumn9.FieldName = "CoverLap";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 8;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "弱覆盖";
            this.gridColumn10.FieldName = "WeakCover";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 9;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "覆盖杂乱";
            this.gridColumn11.FieldName = "NoMainCell";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 10;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "背向覆盖";
            this.gridColumn12.FieldName = "BackCover";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 11;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "重选问题";
            this.gridColumn13.FieldName = "ReselectProblem";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 12;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "切换不合理";
            this.gridColumn14.FieldName = "HandoverProblem";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 13;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "切换不及时";
            this.gridColumn15.FieldName = "HandoverNotInTime";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 14;
            this.gridColumn15.Width = 86;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "质量毛刺";
            this.gridColumn16.FieldName = "QualBurr";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 15;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "频率干扰或故障";
            this.gridColumn17.FieldName = "Interfere_C_I";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 16;
            this.gridColumn17.Width = 109;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "其它";
            this.gridColumn18.FieldName = "Others";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 17;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "解决建议";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 18;
            // 
            // listViewTotal
            // 
            this.listViewTotal.AllColumns.Add(this.olvColumnSN);
            this.listViewTotal.AllColumns.Add(this.olvColumnSampleCount);
            this.listViewTotal.AllColumns.Add(this.olvColumnCellName);
            this.listViewTotal.AllColumns.Add(this.olvColumnLAC);
            this.listViewTotal.AllColumns.Add(this.olvColumnCI);
            this.listViewTotal.AllColumns.Add(this.olvColumnRxQual);
            this.listViewTotal.AllColumns.Add(this.olvColumnRxLev);
            this.listViewTotal.AllColumns.Add(this.olvColumnLongitude);
            this.listViewTotal.AllColumns.Add(this.olvColumnLatitude);
            this.listViewTotal.AllColumns.Add(this.olvColumnTime);
            this.listViewTotal.AllColumns.Add(this.olvColumnBand);
            this.listViewTotal.AllColumns.Add(this.olvColumnDistance);
            this.listViewTotal.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnSampleCount,
            this.olvColumnCellName,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnRxQual,
            this.olvColumnRxLev,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnTime,
            this.olvColumnBand,
            this.olvColumnDistance});
            this.listViewTotal.ContextMenuStrip = this.contextMenuStripBadRxQualConverge;
            this.listViewTotal.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewTotal.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewTotal.FullRowSelect = true;
            this.listViewTotal.GridLines = true;
            this.listViewTotal.HeaderWordWrap = true;
            this.listViewTotal.IsNeedShowOverlay = false;
            this.listViewTotal.Location = new System.Drawing.Point(0, 0);
            this.listViewTotal.Name = "listViewTotal";
            this.listViewTotal.OwnerDraw = true;
            this.listViewTotal.ShowGroups = false;
            this.listViewTotal.Size = new System.Drawing.Size(874, 322);
            this.listViewTotal.TabIndex = 4;
            this.listViewTotal.UseCompatibleStateImageBehavior = false;
            this.listViewTotal.View = System.Windows.Forms.View.Details;
            this.listViewTotal.VirtualMode = true;
            this.listViewTotal.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "Index";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnSampleCount
            // 
            this.olvColumnSampleCount.HeaderFont = null;
            this.olvColumnSampleCount.Text = "采样点数";
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名";
            this.olvColumnCellName.Width = 105;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            this.olvColumnLAC.Width = 81;
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            this.olvColumnCI.Width = 69;
            // 
            // olvColumnRxQual
            // 
            this.olvColumnRxQual.HeaderFont = null;
            this.olvColumnRxQual.Text = "RxQual";
            this.olvColumnRxQual.Width = 68;
            // 
            // olvColumnRxLev
            // 
            this.olvColumnRxLev.HeaderFont = null;
            this.olvColumnRxLev.Text = "RxLev";
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 85;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 82;
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "时间";
            this.olvColumnTime.Width = 135;
            // 
            // olvColumnBand
            // 
            this.olvColumnBand.HeaderFont = null;
            this.olvColumnBand.Text = "占用频段";
            this.olvColumnBand.Width = 100;
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "与小区距离";
            // 
            // contextMenuStripBadRxQualConverge
            // 
            this.contextMenuStripBadRxQualConverge.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExport,
            this.ToolStripMenuItemExpand,
            this.ToolStripMenuItemFold});
            this.contextMenuStripBadRxQualConverge.Name = "contextMenuStripBadRxQualConverge";
            this.contextMenuStripBadRxQualConverge.Size = new System.Drawing.Size(149, 70);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemExport.Text = "导出到Excel";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // ToolStripMenuItemExpand
            // 
            this.ToolStripMenuItemExpand.Name = "ToolStripMenuItemExpand";
            this.ToolStripMenuItemExpand.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemExpand.Text = "展开所有节点";
            this.ToolStripMenuItemExpand.Click += new System.EventHandler(this.ToolStripMenuItemExpand_Click);
            // 
            // ToolStripMenuItemFold
            // 
            this.ToolStripMenuItemFold.Name = "ToolStripMenuItemFold";
            this.ToolStripMenuItemFold.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemFold.Text = "折叠所有节点";
            this.ToolStripMenuItemFold.Click += new System.EventHandler(this.ToolStripMenuItemFold_Click);
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(881, 352);
            this.xtraTabControl1.TabIndex = 5;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.listViewTotal);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(874, 322);
            this.xtraTabPage1.Text = "汇聚列表";
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.gridControl1);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(874, 322);
            this.xtraTabPage2.Text = "汇聚&&原因分析";
            // 
            // BadRxQualConvergeSampleForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(881, 352);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "BadRxQualConvergeSampleForm";
            this.Text = "质差点汇聚列表";
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCell)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            this.contextMenuStripGridControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).EndInit();
            this.contextMenuStripBadRxQualConverge.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.xtraTabPage2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView listViewTotal;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnRxQual;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripBadRxQualConverge;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExpand;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemFold;
        private BrightIdeasSoftware.OLVColumn olvColumnBand;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewCell;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripGridControl;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExportGC;
    }
}