﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTAtuLogKPIGroup
{
    public class GroupIndicatorOption
    {
        public GroupIndicatorOption()
        {
            Logic = "=";
            KPIName = string.Empty;
            KPIFormula = string.Empty;
        }
        public string KPIFormula
        {
            get;
            set;
        }
        public string KPIName
        {
            get;
            set;
        }
        public byte CarrierID { get; set; } = 1;//默认运营商是中国移动
        public string Logic
        {
            get;
            set;
        }
        public double ReferentValue
        {
            get;
            set;
        }

        public bool AsSatisfiedWhenNoValue
        {
            get;
            set;
        }

        public bool StatPercent
        {
            get;
            set;
        }

        public bool IsMultiFormula
        {
            get;
            set;
        }

        public string Formula1
        {
            get;
            set;
        }

        public string Formula2
        {
            get;
            set;
        }

        public int MoMt1
        {
            get;
            set;
        }

        public int MoMt2
        {
            get;
            set;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic["KPIName"] = KPIName;
                dic["KPIFormula"] = KPIFormula;
                dic["Logic"] = Logic;
                dic["ReferentValue"] = ReferentValue;
                dic["AsSatisfiedWhenNoValue"] = AsSatisfiedWhenNoValue;
                dic["IsMultiFormula"] = IsMultiFormula;
                dic["Formula1"] = Formula1;
                dic["Formula2"] = Formula2;
                dic["MoMt1"] = MoMt1;
                dic["MoMt2"] = MoMt2;
                dic["CarrierID"] = (int)CarrierID;
                return dic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                KPIName = value["KPIName"].ToString();
                KPIFormula = value["KPIFormula"] == null ? string.Empty : value["KPIFormula"].ToString();
                Logic = value["Logic"].ToString();
                ReferentValue = double.Parse(value["ReferentValue"].ToString());
                AsSatisfiedWhenNoValue = (bool)value["AsSatisfiedWhenNoValue"];

                if (value.ContainsKey("IsMultiFormula"))
                {
                    IsMultiFormula = (bool)value["IsMultiFormula"];
                }
                if (value.ContainsKey("Formula1"))
                {
                    Formula1 = value["Formula1"] as string;
                }

                if (value.ContainsKey("Formula2"))
                {
                    Formula2 = value["Formula2"] as string;
                }

                if (value.ContainsKey("MoMt1"))
                {
                    MoMt1 = (int)value["MoMt1"];
                }

                if (value.ContainsKey("MoMt2"))
                {
                    MoMt2 = (int)value["MoMt2"];
                }

                if (value.ContainsKey("CarrierID"))
                {
                    CarrierID = (byte)(int)value["CarrierID"];
                }
            }
        }

        public bool IsValid
        {
            get
            {
                return !string.IsNullOrEmpty(KPIFormula) && !string.IsNullOrEmpty(KPIName);
            }
        }

        public string Desc
        {
            get
            {
                return string.Format("{0}{1}{2}", KPIName == null ? "" : KPIName
                    , Logic, ReferentValue);
            }
        }

        public string FileNumToken
        {
            get
            {
                return Desc + "(文件个数)";
            }
        }

        public string FilePerToken
        {
            get { return Desc + "占比(%)"; }
        }

        public bool IsSatisfiedValue(double value)
        {
            if (double.IsNaN(value))
            {
                return AsSatisfiedWhenNoValue;
            }

            bool ret = false;
            if (Logic == ">")
            {
                ret = value > ReferentValue;
            }
            else if (Logic == "=")
            {
                ret = value == ReferentValue;
            }
            else if (Logic == "<")
            {
                ret = value < ReferentValue;
            }
            else if (Logic == "≥")
            {
                ret = value >= ReferentValue;

            }
            else if (Logic == "≤")
            {
                ret = value <= ReferentValue;
            }
            else if (Logic == "!=")
            {
                ret = value != ReferentValue;
            }
            return ret;
        }

    }
}
