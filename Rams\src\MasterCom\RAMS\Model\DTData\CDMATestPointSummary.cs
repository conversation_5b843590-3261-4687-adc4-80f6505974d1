using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
//using EventDefineEngine;

namespace MasterCom.RAMS.Model
{
    public class CDMATestPointSummary : TestPoint
    {
        public override DateTime DateTime
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(Time * 1000L + Millisecond); }
        }
        public new short Millisecond { get; set; }
        public override void Fill(MasterCom.RAMS.Net.Content content)
        {
            //TestType = content.GetParamInt();
            //DeviceType = content.GetParamInt();
            //FileType = content.GetParamInt();
            //ServiceType = content.GetParamInt();
            //CarrierType = content.GetParamInt();
            //ProjectType = content.GetParamInt();
            //Batch = content.GetParamInt();
            FileID = content.GetParamInt();
            //FileName = content.GetParamString();
            SN = content.GetParamInt();
            Time = content.GetParamInt();
            Millisecond = content.GetParamShort();
            MS = content.GetParamByte();
            Longitude = content.GetParamDouble();
            Latitude = content.GetParamDouble();
            this["CD_RX_Power"] = content.GetParamFloat();
            this["CD_TX_Power"] = content.GetParamFloat();
            this["CD_TotalEcIo"] = content.GetParamFloat();
            this["CD_ReferenceEcIo"] = content.GetParamFloat();
            this["CD_TotalEc"] = content.GetParamFloat();
            this["CD_ReferenceEc"] = content.GetParamFloat();
            this["CD_FFER"] = content.GetParamFloat();
        }
    }
}
