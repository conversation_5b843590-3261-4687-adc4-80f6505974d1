<?xml version="1.0"?>
<Configs>
  <Config name="ReportSetting">
    <Item name="styles" typeName="IDictionary">
      <Item typeName="String" key="Name">山东联通测试_GSM语音业务事件（新）</Item>
      <Item typeName="IList" key="Cells">
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">切换请求次数</Item>
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">位置更新失败次数</Item>
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[15]}</Item>
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[20]}</Item>
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">切换</Item>
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">切换成功次数</Item>
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">切换失败次数</Item>
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">切换成功率</Item>
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[16]}</Item>
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[17]}</Item>
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*evtIdCount[16]/(evtIdCount[16]+evtIdCount[17]) }%</Item>
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">位置更新成功率</Item>
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*evtIdCount[19]/evtIdCount[18] }%</Item>
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">指标分类</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">指标名称</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">值</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">平均切换时间间隔</Item>
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">平均切换距离间隔</Item>
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">单位里程切换请求次数</Item>
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">单位里程切换成功次数</Item>
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Mx_0805/1000)/evtIdCount[15]}秒</Item>
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Mx_0806/1000)/evtIdCount[15]}公里</Item>
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[15]/((Mx_640119+Mx_640116+Mx_640115+Mx_640114)/1000) }次/公里</Item>
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[16]/((Mx_640119+Mx_640116+Mx_640115+Mx_640114)/1000) }次/公里</Item>
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">位置更新</Item>
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">位置更新请求次数</Item>
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[18]}</Item>
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">位置更新成功次数</Item>
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[19]}</Item>
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">呼叫</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">主叫试呼次数</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">主叫接通次数</Item>
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">主叫掉话次数</Item>
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">掉话次数（主叫+被叫）</Item>
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">掉话率</Item>
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[0]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[0]-(evtIdCount[7]+evtIdCount[9]+evtIdCount[81]) }</Item>
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[5]}</Item>
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[5]+evtIdCount[6] }</Item>
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100.0*(evtIdCount[5]+evtIdCount[6])/(evtIdCount[3]+evtIdCount[4]) }%</Item>
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">接通率</Item>
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(evtIdCount[0]-(evtIdCount[7]+evtIdCount[9]+evtIdCount[81]))/evtIdCount[0] }</Item>
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">平均呼叫建立时延</Item>
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value1[13]/(1000.0*evtIdCount[13]) }秒</Item>
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[0]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
      </Item>
      <Item typeName="IList" key="Graphs" />
      <Item typeName="IList" key="ColWidth">
        <Item typeName="Int32">90</Item>
        <Item typeName="Int32">318</Item>
        <Item typeName="Int32">136</Item>
        <Item typeName="Int32">122</Item>
        <Item typeName="Int32">114</Item>
        <Item typeName="Int32">108</Item>
        <Item typeName="Int32">104</Item>
        <Item typeName="Int32">102</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
      </Item>
    </Item>
  </Config>
</Configs>