﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.Func.SystemSetting
{
    public partial class StationAcceptProperties_TianJin : PropertiesControl
    {
        StationAccept_TianJin queryFunc;
        public StationAcceptProperties_TianJin(StationAccept_TianJin queryFunc)
        {
            InitializeComponent();
            this.queryFunc = queryFunc;
        }

        public override string ParentName
        {
            get { return queryFunc.FuncType.ToString(); }
        }

        public override string ParentSubName
        {
            get { return queryFunc.SubFuncType.ToString(); }
        }

        public override string SelfName
        {
            get { return queryFunc.Name; }
        }

        public override string TabPageName
        {
            get { return queryFunc.Name; }
        }

        public override bool IsValid()
        {
            return true;
        }

        public override void Apply()
        {
            queryFunc.BackgroundStat = chkBackgroundStat.Checked;
            queryFunc.StationCondition.ExcelPath = btnEditCellParamPath.Text;
            queryFunc.StationCondition.FilePath = btnEditResultSavePath.Text;
            queryFunc.StationCondition.AcceptType = (StationAcceptCondition_TJ.StationAcceptType)comboBoxFileNameType.SelectedIndex;
        }

        public override void Flush()
        {
            chkBackgroundStat.Checked = queryFunc.BackgroundStat;
            btnEditCellParamPath.Text = queryFunc.StationCondition.ExcelPath;
            btnEditResultSavePath.Text = queryFunc.StationCondition.FilePath;
            comboBoxFileNameType.SelectedIndex = (int)queryFunc.StationCondition.AcceptType;
        }

        private void btnEditSelectFolder(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            DevExpress.XtraEditors.ButtonEdit btnEdit = sender as DevExpress.XtraEditors.ButtonEdit;

            if (btnEdit != null)
            {
                FolderBrowserDialog folderDlg = new FolderBrowserDialog();
                if (folderDlg.ShowDialog() == DialogResult.OK)
                {
                    btnEdit.Text = folderDlg.SelectedPath;
                }
            }
        }
    }
}
