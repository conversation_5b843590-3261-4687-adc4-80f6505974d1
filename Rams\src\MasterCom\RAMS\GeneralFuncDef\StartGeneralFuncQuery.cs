﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func.OwnSampleAnalyse;
using System.Windows.Forms;
using MasterCom.RAMS.GeneralFuncDef.OwnSampleAnalyse;

namespace MasterCom.RAMS.GeneralFuncDef
{
    public class StartGeneralFuncQuery : QueryBase
    {
        public StartGeneralFuncQuery(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "自定义扩展分析"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 1100, 1120, this.Name);
        }
        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        protected override void query()
        {
            GeneralFuncSelectorDlg dlg = new GeneralFuncSelectorDlg();
            if(dlg.ShowDialog() == DialogResult.OK)
            {
                QueryCondition qcond = GetQueryCondition();
                GeneralRoutine routine = dlg.GetSelectedRoutine();
                if(routine!=null)
                {
                    for(int i=0;i<routine.analysersList.Count;i++)
                    {
                        BaseSubAnalyser analyser = routine.analysersList[i];
                        if(analyser is SampleAnalyserDef)
                        {
                            CommonSampleAnalyserDIYByRegion sampleQuery = new CommonSampleAnalyserDIYByRegion(MainModel.GetInstance());
                            sampleQuery.SetQueryCondition(qcond);
                            sampleQuery.Query();
                        }

                    }
                }
            }
        }
        protected override bool isValidCondition()
        {
            return true;
        }
    }
}
