using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Chris.Util;

namespace MasterCom.RAMS.Func
{
    public partial class VoNRMosSettingDlg : BaseForm
    {
        NRMosAnaCondition MosAnaCondition;
        public VoNRMosSettingDlg(NRMosAnaCondition condition)
        {
            MosAnaCondition = condition;
            InitializeComponent();
            rstSinr.RangeAll = new Range(-50, true, 50, true);
            rstSinr.DecimalPlaces = 2;
            rstRsrp.RangeAll = new Range(-141, true, 25, true);
            rstRsrp.DecimalPlaces = 2;
            rstQamPer.RangeAll = new Range(0, true, 100, true);
            rstQamPer.DecimalPlaces = 2;
            rstDLQam16Per.RangeAll = new Range(0, true, 100, true);
            rstDLQam16Per.DecimalPlaces = 2;
            rstDLQam64Per.RangeAll = new Range(0, true, 100, true);
            rstDLQam64Per.DecimalPlaces = 2;
            rstDLQpskPer.RangeAll = new Range(0, true, 100, true);
            rstDLQpskPer.DecimalPlaces = 2;

            if (MosAnaCondition == null)
            {
                MosAnaCondition = new NRMosAnaCondition();
                MosAnaCondition.SinrRangeSet = getAutoSinrRangeSet();
                MosAnaCondition.RsrpRangeSet = getAutoRsrpRangeSet();
                MosAnaCondition.QamPerRangeSet = getAutoQamPerRangeSet();
                MosAnaCondition.DlQam16PerRangeSet = getAutoDlQam16PerRangeSet();
                MosAnaCondition.DlQam64PerRangeSet = getAutoDlQam64PerRangeSet();
                MosAnaCondition.DlQpskPerRangeSet = getAutoDlQpskPerRangeSet();
            }
            rstSinr.RangeSet = MosAnaCondition.SinrRangeSet;
            rstSinr.AutoRangeSet = getAutoSinrRangeSet();
            rstRsrp.RangeSet = MosAnaCondition.RsrpRangeSet;
            rstRsrp.AutoRangeSet = getAutoRsrpRangeSet();
            rstQamPer.RangeSet = MosAnaCondition.QamPerRangeSet;
            rstQamPer.AutoRangeSet = getAutoQamPerRangeSet();
            rstDLQam16Per.RangeSet = MosAnaCondition.DlQam16PerRangeSet;
            rstDLQam16Per.AutoRangeSet = getAutoDlQam16PerRangeSet();
            rstDLQam64Per.RangeSet = MosAnaCondition.DlQam64PerRangeSet;
            rstDLQam64Per.AutoRangeSet = getAutoDlQam64PerRangeSet();
            rstDLQpskPer.RangeSet = MosAnaCondition.DlQpskPerRangeSet;
            rstDLQpskPer.AutoRangeSet = getAutoDlQpskPerRangeSet();
        }

        public NRMosAnaCondition GetCondition()
        {
            MosAnaCondition = new NRMosAnaCondition();
            MosAnaCondition.SinrRangeSet = rstSinr.RangeSet;
            MosAnaCondition.RsrpRangeSet = rstRsrp.RangeSet;
            MosAnaCondition.QamPerRangeSet = rstQamPer.RangeSet;
            MosAnaCondition.DlQam16PerRangeSet = rstDLQam16Per.RangeSet;
            MosAnaCondition.DlQam64PerRangeSet = rstDLQam64Per.RangeSet;
            MosAnaCondition.DlQpskPerRangeSet = rstDLQpskPer.RangeSet;

            return MosAnaCondition;
        }

        private RangeSet getAutoSinrRangeSet()
        {
            RangeSet set = new RangeSet();
            set.Add(new Range(-50, true, -3, false));
            set.Add(new Range(-3, true, 3, false));
            set.Add(new Range(3, true, 10, false));
            set.Add(new Range(10, true, 20, false));
            set.Add(new Range(20, true, 50, true));
            return set;
        }
        private RangeSet getAutoRsrpRangeSet()
        {
            RangeSet set = new RangeSet();
            set.Add(new Range(-141, true, -100, false));
            set.Add(new Range(-100, true, -90, false));
            set.Add(new Range(-90, true, -80, false));
            set.Add(new Range(-80, true, 25, true));
            return set;
        }

        private RangeSet getAutoQamPerRangeSet()
        {
            RangeSet set = new RangeSet();
            set.Add(new Range(0, true, 30, false));
            set.Add(new Range(30, true, 50, false));
            set.Add(new Range(50, true, 80, false));
            set.Add(new Range(80, true, 90, false));
            set.Add(new Range(90, true, 100, true));
            return set;
        }

        private RangeSet getAutoDlQam16PerRangeSet()
        {
#if DEBUG
            Console.Write("Qam16");
#endif
            RangeSet set = new RangeSet();
            set.Add(new Range(0, true, 30, false));
            set.Add(new Range(30, true, 50, false));
            set.Add(new Range(50, true, 80, false));
            set.Add(new Range(80, true, 90, false));
            set.Add(new Range(90, true, 100, true));
            return set;
        }

        private RangeSet getAutoDlQam64PerRangeSet()
        {
#if DEBUG
            Console.Write("Qam64");
#endif
            RangeSet set = new RangeSet();
            set.Add(new Range(0, true, 30, false));
            set.Add(new Range(30, true, 50, false));
            set.Add(new Range(50, true, 80, false));
            set.Add(new Range(80, true, 90, false));
            set.Add(new Range(90, true, 100, true));
            return set;
        }

        private RangeSet getAutoDlQpskPerRangeSet()
        {
#if DEBUG
            Console.Write("Qpsk");
#endif
            RangeSet set = new RangeSet();
            set.Add(new Range(0, true, 30, false));
            set.Add(new Range(30, true, 50, false));
            set.Add(new Range(50, true, 80, false));
            set.Add(new Range(80, true, 90, false));
            set.Add(new Range(90, true, 100, true));
            return set;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.Dispose();
        }
    }
}