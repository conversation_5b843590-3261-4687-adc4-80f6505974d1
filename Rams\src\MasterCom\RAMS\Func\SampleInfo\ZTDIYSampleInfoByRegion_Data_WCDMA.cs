﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYSampleInfoByRegion_Data_WCDMA : ZTDIYSampleInfoByRegion_TD
    {
        public ZTDIYSampleInfoByRegion_Data_WCDMA(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "WCDMA数据采样点信息导出(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11065, this.Name);
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                if (tp is WCDMATestPointDetail)
                {
                    bool inRegion = Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
                    if (inRegion)//进行过覆盖算法运算
                    {
                        return true;
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        protected override void doWithDTData(TestPoint tp)
        {
            rows.Add(writeExcel(tp));
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "W_CellName";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysLAI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysCellID";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_frequency";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_Reference_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_Reference_Ec_Io";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_Reference_PSC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            for (int i = 0; i < 5; i++)
            {
                param = new Dictionary<string, object>();
                param["param_name"] = "W_N_CellName";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "W_SNeiFreq";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "W_SNeiRSCP";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "W_SNeiEcIo";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "W_SNeiPSC";
                param["param_arg"] = i;
                columnsDef.Add((object)param);
            }

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"PilotFrequencyPollute");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override NPOIRow getExcelHeader()
        {
            NPOIRow row = new NPOIRow();
            row.cellValues.Add("文件名");
            row.cellValues.Add("时间");
            row.cellValues.Add("经度");
            row.cellValues.Add("纬度");
            row.cellValues.Add("主服小区");
            row.cellValues.Add("LAC");
            row.cellValues.Add("CELLID");
            row.cellValues.Add("BCCH");
            row.cellValues.Add("BSIC");
            row.cellValues.Add("Ec_Io");
            row.cellValues.Add("Rxlev");

            for (int i = 0; i < 5; i++)
            {
                row.cellValues.Add("邻区" + (i + 1));
                row.cellValues.Add("N_BCCH" + (i + 1));
                row.cellValues.Add("N_BSIC" + (i + 1));
                row.cellValues.Add("N_Ec_Io" + (i + 1));
                row.cellValues.Add("N_RxLev" + (i + 1));
            }
            return row;
        }

        protected override NPOIRow writeExcel(TestPoint tp)
        {
            NPOIRow row = new NPOIRow();
            row.cellValues.Add(tp.FileName);
            row.cellValues.Add(tp.DateTimeStringWithMillisecond);
            row.cellValues.Add(tp.Longitude.ToString());
            row.cellValues.Add(tp.Latitude.ToString());

            object cellName = tp["W_CellName"];
            if (cellName != null)
            {
                row.cellValues.Add(cellName.ToString());
            }
            else
            {
                row.cellValues.Add("");
            }

            object objVal = tp["W_SysLAI"];
            row.cellValues.Add(objVal == null ? "" : objVal.ToString());
            objVal = tp["W_SysCellID"];
            row.cellValues.Add(objVal == null ? "" : objVal.ToString());
            objVal = tp["W_frequency"];
            row.cellValues.Add(objVal == null ? "" : objVal.ToString());
            objVal = tp["W_Reference_RSCP"];
            row.cellValues.Add(objVal == null ? "" : objVal.ToString());
            objVal = tp["W_Reference_Ec_Io"];
            row.cellValues.Add(objVal == null ? "" : objVal.ToString());
            objVal = tp["W_Reference_PSC"];
            row.cellValues.Add(objVal == null ? "" : objVal.ToString());

            addNCellInfo(tp, row);
            return row;
        }

        private void addNCellInfo(TestPoint tp, NPOIRow row)
        {
            for (int i = 0; i < 5; i++)
            {
                addValidData(tp, row, i, "W_N_CellName");
                addValidData(tp, row, i, "W_SNeiFreq");
                addValidData(tp, row, i, "W_SNeiRSCP");
                addValidData(tp, row, i, "W_SNeiEcIo");
                addValidData(tp, row, i, "W_SNeiPSC");
            }
        }

        private void addValidData(TestPoint tp, NPOIRow row, int i, string name)
        {
            if (tp[name, i] != null)
            {
                row.cellValues.Add(tp[name, i].ToString());
            }
            else
            {
                row.cellValues.Add("");
            }
        }
    }
}
