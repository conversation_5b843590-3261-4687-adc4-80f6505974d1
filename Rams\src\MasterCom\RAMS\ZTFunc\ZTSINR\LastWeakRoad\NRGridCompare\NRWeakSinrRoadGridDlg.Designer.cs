﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRWeakSinrRoadGridDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.endTime1 = new System.Windows.Forms.DateTimePicker();
            this.endTime2 = new System.Windows.Forms.DateTimePicker();
            this.beginTime2 = new System.Windows.Forms.DateTimePicker();
            this.beginTime1 = new System.Windows.Forms.DateTimePicker();
            this.label10 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.label1 = new System.Windows.Forms.Label();
            this.numGridSpan = new System.Windows.Forms.NumericUpDown();
            this.numMaxTPDistance = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.numMaxValue = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.numMinDistance = new System.Windows.Forms.NumericUpDown();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numGridSpan)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxTPDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxValue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDistance)).BeginInit();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(233, 273);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 75;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(143, 273);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 74;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.endTime1);
            this.groupControl1.Controls.Add(this.endTime2);
            this.groupControl1.Controls.Add(this.beginTime2);
            this.groupControl1.Controls.Add(this.beginTime1);
            this.groupControl1.Controls.Add(this.label10);
            this.groupControl1.Controls.Add(this.label14);
            this.groupControl1.Controls.Add(this.label13);
            this.groupControl1.Controls.Add(this.label9);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(466, 93);
            this.groupControl1.TabIndex = 77;
            this.groupControl1.Text = "对比时间段设置";
            // 
            // endTime1
            // 
            this.endTime1.Location = new System.Drawing.Point(293, 33);
            this.endTime1.Name = "endTime1";
            this.endTime1.Size = new System.Drawing.Size(153, 21);
            this.endTime1.TabIndex = 1;
            // 
            // endTime2
            // 
            this.endTime2.Location = new System.Drawing.Point(293, 60);
            this.endTime2.Name = "endTime2";
            this.endTime2.Size = new System.Drawing.Size(153, 21);
            this.endTime2.TabIndex = 3;
            // 
            // beginTime2
            // 
            this.beginTime2.Location = new System.Drawing.Point(106, 60);
            this.beginTime2.Name = "beginTime2";
            this.beginTime2.Size = new System.Drawing.Size(152, 21);
            this.beginTime2.TabIndex = 2;
            // 
            // beginTime1
            // 
            this.beginTime1.Location = new System.Drawing.Point(105, 33);
            this.beginTime1.Name = "beginTime1";
            this.beginTime1.Size = new System.Drawing.Size(153, 21);
            this.beginTime1.TabIndex = 0;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(22, 64);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(71, 12);
            this.label10.TabIndex = 27;
            this.label10.Text = "时间段2：从";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(268, 64);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(17, 12);
            this.label14.TabIndex = 27;
            this.label14.Text = "到";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(268, 37);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(17, 12);
            this.label13.TabIndex = 27;
            this.label13.Text = "到";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(22, 37);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(71, 12);
            this.label9.TabIndex = 27;
            this.label9.Text = "时间段1：从";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.label1);
            this.groupControl2.Controls.Add(this.numGridSpan);
            this.groupControl2.Controls.Add(this.numMaxTPDistance);
            this.groupControl2.Controls.Add(this.label4);
            this.groupControl2.Controls.Add(this.numMaxValue);
            this.groupControl2.Controls.Add(this.label5);
            this.groupControl2.Controls.Add(this.label6);
            this.groupControl2.Controls.Add(this.label3);
            this.groupControl2.Controls.Add(this.label2);
            this.groupControl2.Controls.Add(this.label8);
            this.groupControl2.Controls.Add(this.label7);
            this.groupControl2.Controls.Add(this.numMinDistance);
            this.groupControl2.Location = new System.Drawing.Point(0, 93);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(466, 160);
            this.groupControl2.TabIndex = 76;
            this.groupControl2.Text = "质差路段";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(165, 33);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 61;
            this.label1.Text = "SINR≤";
            // 
            // numGridSpan
            // 
            this.numGridSpan.Location = new System.Drawing.Point(211, 124);
            this.numGridSpan.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numGridSpan.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numGridSpan.Name = "numGridSpan";
            this.numGridSpan.Size = new System.Drawing.Size(80, 21);
            this.numGridSpan.TabIndex = 3;
            this.numGridSpan.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numGridSpan.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // numMaxTPDistance
            // 
            this.numMaxTPDistance.Location = new System.Drawing.Point(211, 92);
            this.numMaxTPDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMaxTPDistance.Name = "numMaxTPDistance";
            this.numMaxTPDistance.Size = new System.Drawing.Size(80, 21);
            this.numMaxTPDistance.TabIndex = 2;
            this.numMaxTPDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMaxTPDistance.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(296, 70);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 64;
            this.label4.Text = "米";
            // 
            // numMaxValue
            // 
            this.numMaxValue.Location = new System.Drawing.Point(211, 28);
            this.numMaxValue.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numMaxValue.Minimum = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numMaxValue.Name = "numMaxValue";
            this.numMaxValue.Size = new System.Drawing.Size(80, 21);
            this.numMaxValue.TabIndex = 0;
            this.numMaxValue.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(129, 129);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(77, 12);
            this.label5.TabIndex = 65;
            this.label5.Text = "栅格化大小：";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(105, 96);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(101, 12);
            this.label6.TabIndex = 65;
            this.label6.Text = "相邻采样点距离≤";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(296, 33);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(17, 12);
            this.label3.TabIndex = 63;
            this.label3.Text = "dB";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(141, 65);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 62;
            this.label2.Text = "持续距离≥";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(296, 129);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(17, 12);
            this.label8.TabIndex = 66;
            this.label8.Text = "米";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(296, 104);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 66;
            this.label7.Text = "米";
            // 
            // numMinDistance
            // 
            this.numMinDistance.Location = new System.Drawing.Point(211, 60);
            this.numMinDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMinDistance.Name = "numMinDistance";
            this.numMinDistance.Size = new System.Drawing.Size(80, 21);
            this.numMinDistance.TabIndex = 1;
            this.numMinDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinDistance.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // NRWeakSinrRoadGridDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(466, 311);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "NRWeakSinrRoadGridDlg";
            this.Text = "NRWeakSinrRoadGridDlg";
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numGridSpan)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxTPDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxValue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDistance)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.DateTimePicker endTime1;
        private System.Windows.Forms.DateTimePicker endTime2;
        private System.Windows.Forms.DateTimePicker beginTime2;
        private System.Windows.Forms.DateTimePicker beginTime1;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label9;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numGridSpan;
        private System.Windows.Forms.NumericUpDown numMaxTPDistance;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numMaxValue;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown numMinDistance;
    }
}