﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTNearWeakCover
{
    public partial class ResultForm : MinCloseForm
    {
        public ResultForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void FillData(List<NearWeakCoverLteCell> cells)
        {
            gridControl.DataSource = cells;
            gridControl.RefreshDataSource();
            gridView.BestFitColumns();
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gridView.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }

            NearWeakCoverLteCell cell = gridView.GetRow(info.RowHandle) as NearWeakCoverLteCell;
            if (cell == null)
            {
                return;
            }
            MainModel.SelectedLTECell = cell.Cell;
            MainModel.ClearDTData();
            foreach (TestPoint tp in cell.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
            MainModel.FireDTDataChanged(this);
        }

        private void miExp2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }

    }
}
