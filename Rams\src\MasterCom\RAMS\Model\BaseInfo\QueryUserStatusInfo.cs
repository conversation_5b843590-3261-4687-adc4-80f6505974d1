﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model.BaseInfo
{
    public class QueryUserStatusInfo : DIYSQLBase
    {
        private readonly List<UserStatusInfo> userStatusList = new List<UserStatusInfo>();
        public List<UserStatusInfo> UserStatusList
        {
            get { return userStatusList; }
        }

        public QueryUserStatusInfo(MainModel mainModel)
            : base(mainModel)
        {
            MainDB = true;
        }

        protected override string getSqlTextString()
        {
            string sqlStr = @"select iid,strcomment from tb_cfg_static_user_state";
            return sqlStr;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[2];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    UserStatusInfo userStatus = new UserStatusInfo();
                    userStatus.UserStatus = package.Content.GetParamInt();
                    userStatus.UserStatusDes = package.Content.GetParamString();
                    userStatusList.Add(userStatus);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }
        public override string Name
        {
            get { return "查询所有状态信息。"; }
        }
    }

    public class UserStatusInfo
    {
        public int UserStatus { get; set; } = -100;
        public string UserStatusDes { get; set; } = "";
    }
}
