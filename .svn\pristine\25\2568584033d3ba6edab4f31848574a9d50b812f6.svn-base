﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.Globalization;

namespace MasterCom.RAMS.ZTFunc
{
    public class TDScoreQuery : DIYSQLBase
    {
        public TDScoreQuery(MainModel mainModel)
            : base(mainModel)
        {
        }
        protected override string getSqlTextString()
        {
            return "select id,strtype,strmode,strproject,fthreshold,foutindicators,f0indicators,itotalscore,ithresholdscore from tb_auto_zt_score where strnetType='TD'";
        }

        public List<TDScore> TdScoreList { get; set; } = new List<TDScore>();

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[9];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_Float;
            rType[5] = E_VType.E_Float;
            rType[6] = E_VType.E_Float;
            rType[7] = E_VType.E_Int;
            rType[8] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    TDScore tdScore = new TDScore();
                    tdScore.Id = package.Content.GetParamInt();
                    tdScore.Strtype = package.Content.GetParamString();
                    tdScore.Strmode = package.Content.GetParamString();
                    tdScore.Strproject = package.Content.GetParamString();
                    tdScore.Fthreshold = decimal.Parse(package.Content.GetParamFloat().ToString());
                    tdScore.Foutindicators = decimal.Parse(package.Content.GetParamFloat().ToString());
                    tdScore.F0indicators = decimal.Parse(package.Content.GetParamFloat().ToString());
                    tdScore.Itotalscore = package.Content.GetParamInt();
                    tdScore.Ithresholdscore = package.Content.GetParamInt();
                    TdScoreList.Add(tdScore);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        public override string Name
        {
            get { return "TDScoreQuery"; }
        }
    }
    public class TDScoreQueryIndicators : DIYSQLBase
    {
        public TDScoreQueryIndicators(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override string getSqlTextString()
        {
            return "exec zt_score";
        }

        
        public List<TDScore> TDScoreList { get; set; } = new List<TDScore>();

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[2];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_Float;
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        private void fillData(Package package)
        {
            try
            {
                TDScore tdScore = new TDScore();
                tdScore.Strproject = package.Content.GetParamString();
                tdScore.Ftrueindicators = decimal.Parse(package.Content.GetParamFloat().ToString());
                TDScoreList.Add(tdScore);
            }
            catch
            {
                //continue
            }
        }

        public override string Name
        {
            get { return "TDScoreQuery"; }
        }
    }
}
