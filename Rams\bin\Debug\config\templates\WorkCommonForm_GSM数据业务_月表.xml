<?xml version="1.0"?>
<Configs>
  <Config name="StatReports">
    <Item name="reports" typeName="IList">
      <Item typeName="ReporterTemplate">
        <Item name="Param" typeName="IDictionary">
          <Item typeName="String" key="Name">GSM数据业务_月表</Item>
          <Item typeName="Int32" key="KeyCount">3</Item>
          <Item typeName="Int32" key="TimeShowType">1</Item>
          <Item typeName="IList" key="Columns">
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">代维公司</Item>
              <Item typeName="String" key="Exp">{kDwId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">代维公司</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">所属项目</Item>
              <Item typeName="String" key="Exp">{kProjId}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">所属项目</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">日期</Item>
              <Item typeName="String" key="Exp">{kTimeValue}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">时间范围 </Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试里程(公里)</Item>
              <Item typeName="String" key="Exp">{Mx_0806/1000}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">测试里程(公里)</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">测试时长(分钟)</Item>
              <Item typeName="String" key="Exp">{Mx_0805/60000}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">测试时长(分钟)</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均时速(公里/小时)</Item>
              <Item typeName="String" key="Exp">{Mx_0806*3600/Mx_0805 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">平均时速(公里/小时)</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">90覆盖率(%)</Item>
              <Item typeName="String" key="Exp">{100*(Mx_5A020217+Mx_5A02020A+Mx_5A020209+Mx_5A020208) /Mx_5A020201}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">90覆盖率(%)</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">94覆盖率(%)</Item>
              <Item typeName="String" key="Exp">{100*(Mx_5A020217+Mx_5A02020A+Mx_5A020209+Mx_5A020208+Mx_5A020207) /Mx_5A020201}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">94覆盖率(%)</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均RLC吞吐量(Kbps)</Item>
              <Item typeName="String" key="Exp">{(Mx_03020402+Mx_03020802)/(1024*(Mx_03020401+Mx_03020801) )}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">平均RLC吞吐量(Kbps)</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均RLC_BLER(%)</Item>
              <Item typeName="String" key="Exp">{1000*(Mx_04020402+Mx_04020802)/(Mx_04020401+Mx_04020801) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">平均RLC_BLER(%)</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">FTP下载成功次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[57]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">FTP下载成功次数</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">FTP下载失败次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[58]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">FTP下载失败次数</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">FTP下载成功率(%)</Item>
              <Item typeName="String" key="Exp">{100*evtIdCount[57]/(evtIdCount[57]+evtIdCount[58])}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">FTP下载成功率(%)</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">FTP下载速率(Kbps)</Item>
              <Item typeName="String" key="Exp">{value1[57]*8/(1.0*value2[57]) }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">FTP下载速率(Kbps)</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">RAU成功次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[27]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">RAU成功次数</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">RAU失败次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[28]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">RAU失败次数</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均RAU间隔时长(分钟/次)</Item>
              <Item typeName="String" key="Exp">{Mx_0805/evtIdCount[27]*60000 }</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">平均RAU间隔时长(分钟/次)</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">平均RAU间隔距离(公里/次)</Item>
              <Item typeName="String" key="Exp">{Mx_0806/evtIdCount[27]*1000}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">2</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">平均RAU间隔距离(公里/次)</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="Title">小区重选次数</Item>
              <Item typeName="String" key="Exp">{evtIdCount[39]}</Item>
              <Item typeName="Boolean" key="UseHi">False</Item>
              <Item typeName="IList" key="HiList" />
              <Item typeName="Boolean" key="AutoGraph">False</Item>
              <Item typeName="Int32" key="DecPlace">0</Item>
              <Item typeName="Boolean" key="AllowDrill">False</Item>
              <Item typeName="Int32" key="CarrierId">1</Item>
              <Item typeName="String" key="Bak">小区重选次数</Item>
              <Item typeName="Int32" key="Momt">0</Item>
              <Item typeName="IList" key="ServiceIDSet" />
              <Item key="FileNameFilter" />
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>