﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using System.Collections;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Columns;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTPointImportForm : XtraForm
    {
        bool bHasName = false;
        Dictionary<string, int> cqtNameIdDic;
        List<int> cqtIDList;
        List<XlsPoint> xlsPointList;
        List<XlsPoint> wrongXlsPointList;
        List<XlsPoint> correctXlsPointList;
        Dictionary<string, XlsPoint> insertXlsPointDic;
        public CQTPointImportForm()
        {
            cqtNameIdDic = new Dictionary<string, int>();
            cqtIDList = new List<int>();
            xlsPointList = new List<XlsPoint>();
            wrongXlsPointList = new List<XlsPoint>();
            correctXlsPointList = new List<XlsPoint>();
            insertXlsPointDic = new Dictionary<string, XlsPoint>();
            InitializeComponent();
            labelFormat.Text = "";
            labelFormat_Atu.Text = "";
            buttonImport.Enabled = false;
            buttonImport_Atu.Enabled = false;
        }

        private void init()
        {
            foreach (CQTPoint point in CQTPointManager.GetInstance().MTPntList)
            {
                cqtNameIdDic.Add(point.Name, point.ID);
                cqtIDList.Add(point.ID);
            }
            cqtIDList.Sort();
        }

        private void clearData()
        {
            cqtNameIdDic.Clear();
            cqtIDList.Clear();
            xlsPointList.Clear();
            wrongXlsPointList.Clear();
            correctXlsPointList.Clear();
            insertXlsPointDic.Clear();
        }

        private void btnFileOpen_Click(object sender, EventArgs e)
        {
            bHasName = false;
            clearData();
            init();
            gridControl1.DataSource = wrongXlsPointList;
            gridControl1.RefreshDataSource();
            buttonImport.Enabled = false;
            string resultfile = "";
            OpenFileDialog openfiledialog1 = new OpenFileDialog();
            openfiledialog1.InitialDirectory = "";
            openfiledialog1.Filter = "Excel files |*.xls;*.xlsx";
            openfiledialog1.FilterIndex = 2;
            openfiledialog1.RestoreDirectory = true;
            if (openfiledialog1.ShowDialog() == DialogResult.OK)
            {
                resultfile = openfiledialog1.FileName;
            }
            else
            {
                return;
            }
            textBoxFilePath.Text = resultfile;

            try
            {
                DataTable datatable = getDataTable();
                if (datatable.Rows.Count > 0)
                {
                    fill(datatable);
                    checkXlsPointCell();

                    if (wrongXlsPointList.Count > 0)
                    {
                        labelFormat.Text = bHasName ? "库里存在该CQT名" : "格式错误";
                        gridControl1.DataSource = wrongXlsPointList;
                        gridControl1.RefreshDataSource();
                    }
                    else if (insertXlsPointDic.Count < correctXlsPointList.Count)
                    {
                        labelFormat.Text = "存在同名CQT";
                        addWrongXlsPointList();
                        gridControl1.DataSource = wrongXlsPointList;
                        gridControl1.RefreshDataSource();
                    }
                    else
                    {
                        labelFormat.Text = "格式正确";
                        buttonImport.Enabled = true;
                    }
                }
                else
                {
                    labelFormat.Text = "无地点信息";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private DataTable getDataTable()
        {
            ArrayList sheets;
            DataTable datatable = new DataTable();
            sheets = ExcelTools.GetSheetNameList(textBoxFilePath.Text.Trim());

            foreach (string item in sheets)
            {
                DataTable table = ExcelTools.ExcelToDataTable(textBoxFilePath.Text, item);
                if (table.Rows.Count > 0)
                {
                    if (table.Columns.Count < gridView1.Columns.Count)
                    {
                        MessageBox.Show("sheet  " + item + "缺少字段");
                    }
                    else if (table.Columns.Count > gridView1.Columns.Count)
                    {
                        MessageBox.Show("sheet  " + item + "过多字段");
                    }
                    else
                    {
                        datatable.Merge(table);
                    }
                }
            }

            return datatable;
        }

        private void addWrongXlsPointList()
        {
            List<XlsPoint> insertList = new List<XlsPoint>(insertXlsPointDic.Values);
            foreach (XlsPoint point in correctXlsPointList)
            {
                if (!insertList.Contains(point))
                {
                    if (!wrongXlsPointList.Contains(point))
                        wrongXlsPointList.Add(point);
                    if (!wrongXlsPointList.Contains(insertXlsPointDic[point.Name]))
                        wrongXlsPointList.Add(insertXlsPointDic[point.Name]);
                }
            }
        }

        private void fill(DataTable datatable)
        {
            for (int i = 0; i < datatable.Rows.Count; i++ )
            {
                XlsPoint point = new XlsPoint(datatable.Rows[i]);
                xlsPointList.Add(point);
            }
        }

        private void checkXlsPointCell()
        {
            foreach (XlsPoint xlsPoint in xlsPointList)
            {
                if (xlsPoint.check())
                {
                    wrongXlsPointList.Add(xlsPoint);
                }
                else if (cqtNameIdDic.ContainsKey(xlsPoint.Name))
                {
                    bHasName = true;
                    wrongXlsPointList.Add(xlsPoint);
                }
                else
                {
                    int id = searchPointID();

                    xlsPoint.SetPointID(id);
                    correctXlsPointList.Add(xlsPoint);
                    if (!insertXlsPointDic.ContainsKey(xlsPoint.Name))
                    {
                        insertXlsPointDic.Add(xlsPoint.Name, xlsPoint);
                        cqtNameIdDic.Add(xlsPoint.Name, xlsPoint.PointID);
                        cqtIDList.Add(xlsPoint.PointID);
                    }
                }
            }
        }

        private int searchPointID()
        {
            if (cqtIDList.Count <= 0)
            {
                return 1;
            }
            else if (cqtIDList.Count < cqtIDList[cqtIDList.Count - 1])
            {
                for (int i = 1; i < cqtIDList[cqtIDList.Count - 1]; i++)
                {
                    if (!cqtIDList.Contains(i))
                    {
                        return i;
                    }
                }
            }
            return cqtIDList[cqtIDList.Count - 1] + 1;
        }

        private void buttonImport_Click(object sender, EventArgs e)
        {
            List<XlsPoint> insertXlsPointList = new List<XlsPoint>(insertXlsPointDic.Values);
            if (insertXlsPointList.Count > 0)
            {
                WaitBox.Show("正在插入CQT地点信息...", backGroundInsert, insertXlsPointList);
            }
            MessageBox.Show("导入完成");
        }

        public void backGroundInsert(object o)
        {
            try
            {
                int counter = 0;
                int curPercent = 11;

                string sql;
                List<XlsPoint> insertXlsPointList = o as List<XlsPoint>;
                MainModel mainModel = MainModel.GetInstance();
                DiySqlNonQuery queryInsert;
                foreach (XlsPoint xlsPoint in insertXlsPointList)
                {
                    DiySqlOneIntValueOnly idQuery;
                    sql = "select isnull(max(pointID),0)+1 from tb_cqt_point";
                    idQuery = new DiySqlOneIntValueOnly(mainModel, sql);
                    idQuery.Query();
                    xlsPoint.PointID = idQuery.IntValue;

                    sql = "insert into tb_cqt_point values(" + xlsPoint.PointID + ",'" + xlsPoint.Name + "','" + xlsPoint.AddrAt + "','" + xlsPoint.Desc + "'," + double.Parse(xlsPoint.LTLongitude) * 10000000 + "," + double.Parse(xlsPoint.LTLatitude) * 10000000 + "," + double.Parse(xlsPoint.BRLongitude) * 10000000 + ","
                    + double.Parse(xlsPoint.BRLatitude) * 10000000 + "," + xlsPoint.Altitude + ",'" + xlsPoint.AliasName + "','" + CQTCfgManager.GetInstance().GetCQTPointType(xlsPoint.PointType).ID + "','" + CQTCfgManager.GetInstance().GetCQTDensityType(xlsPoint.DensityType).ID + "','"
                    + CQTCfgManager.GetInstance().GetCQTSpaceType(xlsPoint.SpaceType).ID + "','" + CQTCfgManager.GetInstance().GetCQTCoverType(xlsPoint.CoverType).ID + "','" + getNetworkIDs(xlsPoint.NetworkType, ';') + "',"
                    + xlsPoint.OtherType1 + "," + xlsPoint.OtherType2 + "," + xlsPoint.OtherType3 + "," + xlsPoint.OtherType4 + ",'" + xlsPoint.BelongArea + "','" + xlsPoint.BelongArea2 + "')";
                    queryInsert = new DiySqlNonQuery(mainModel, sql);
                    queryInsert.Query();
                    CQTPoint cqtPoint = xlsPoint.ConvertToCQTPoint();
                    CQTPointManager.GetInstance().AddPoint(cqtPoint);

                    foreach (XlsPointCell xlsCell in xlsPoint.xlsCellList)
                    {
                        sql = "insert into tb_cqt_point_cells values(" + xlsPoint.PointID + "," + CQTCfgManager.GetInstance().GetCQTNetworkType(xlsCell.NetWorkType).ID + "," + xlsCell.LAC + "," + xlsCell.CI + ",'" + xlsCell.CellName + "','" + xlsCell.BSCName + "'," + xlsCell.Priority + ")";
                        queryInsert = new DiySqlNonQuery(mainModel, sql);
                        queryInsert.Query();
                    }

                    counter = counter + 40;
                    int tmp = (int)(Math.Log(counter) * (100000 / 10000));
                    if (tmp < 95 && tmp > 0 && curPercent != tmp)
                    {
                        WaitBox.ProgressPercent = tmp;
                        curPercent = tmp;
                    }
                    else if (tmp > 95)
                    {
                        curPercent = 5;
                        counter = 0;
                    }
                }
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private string getNetworkIDs(string netType, char cSplit)
        {
            StringBuilder ids = new StringBuilder();
            string[] netArr = netType.Split(cSplit);
            foreach (string net in netArr)
            {
                ids.Append(CQTCfgManager.GetInstance().GetCQTNetworkType(net).ID + ";");
            }
            return ids.ToString().TrimEnd(';');
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveFileDialog = new SaveFileDialog();
            saveFileDialog.Title = "导出Excel";
            saveFileDialog.Filter = "Excel文件(*.xls)|*.xls";
            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                gridControl1.ExportToXls(saveFileDialog.FileName);
                DevExpress.XtraEditors.XtraMessageBox.Show("保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void gridView1_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            DevExpress.Utils.AppearanceDefault appRed = new DevExpress.Utils.AppearanceDefault(
                Color.Black, Color.Red, Color.Empty, Color.SeaShell, System.Drawing.Drawing2D.LinearGradientMode.Horizontal);

            switch (e.Column.FieldName)
            {
                case "Name":
                    setName(e, appRed, gridView1, cqtNameIdDic);
                    break;
                case "AddrAt":
                    setAddrAt(e, appRed, gridView1);
                    break;
                case "PointType":
                    setPointType(e, appRed, "PointType", gridView1);
                    break;
                case "DensityType":
                    setDensityType(e, appRed, "DensityType", gridView1);
                    break;
                case "SpaceType":
                    setSpaceType(e, appRed, "SpaceType", gridView1);
                    break;
                case "CoverType":
                    setCoverType(e, appRed, "CoverType", gridView1);
                    break;
                case "NetworkType":
                    setNetworkType(e, appRed, "NetworkType", gridView1, ';');
                    break;
                case "Altitude":
                    setAltitude(e, appRed);
                    break;
                case "LTLongitude":
                case "LTLatitude":
                case "BRLongitude":
                case "BRLatitude":
                    setLongitudeLatitude(e, appRed, gridView1);
                    break;
                case "NetType_Cell1":
                case "NetType_Cell2":
                case "NetType_Cell3":
                case "NetType_Cell4":
                case "NetType_Cell5":
                case "NetType_Cell6":
                    setNetTypeCell(e, appRed);
                    break;
                default:
                    break;
            }
            
        }

        private void setName(DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e, DevExpress.Utils.AppearanceDefault appRed, DevExpress.XtraGrid.Views.Grid.GridView gv, Dictionary<string, int> nameIdDic)
        {
            if (nameIdDic.ContainsKey(gv.GetRowCellValue(e.RowHandle, e.Column.FieldName).ToString()) 
                || gv.GetRowCellValue(e.RowHandle, e.Column.FieldName).ToString().Trim().Equals(""))
            {
                DevExpress.Utils.AppearanceHelper.Apply(e.Appearance, appRed);
            }
        }

        private void setAddrAt(DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e, DevExpress.Utils.AppearanceDefault appRed, DevExpress.XtraGrid.Views.Grid.GridView gv)
        {
            if (gv.GetRowCellValue(e.RowHandle, e.Column.FieldName).ToString().Trim().Equals(""))
            {
                DevExpress.Utils.AppearanceHelper.Apply(e.Appearance, appRed);
            }
        }

        private void setPointType(DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e, DevExpress.Utils.AppearanceDefault appRed, string type, DevExpress.XtraGrid.Views.Grid.GridView gv)
        {
            if (CQTPointType.checkIllegal(gv.GetRowCellValue(e.RowHandle, type).ToString()))
            {
                DevExpress.Utils.AppearanceHelper.Apply(e.Appearance, appRed);
            }
        }

        private void setDensityType(DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e, DevExpress.Utils.AppearanceDefault appRed, string type, DevExpress.XtraGrid.Views.Grid.GridView gv)
        {
            if (CQTDensityType.checkIllegal(gv.GetRowCellValue(e.RowHandle, type).ToString()))
            {
                DevExpress.Utils.AppearanceHelper.Apply(e.Appearance, appRed);
            }
        }

        private void setSpaceType(DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e, DevExpress.Utils.AppearanceDefault appRed, string type, DevExpress.XtraGrid.Views.Grid.GridView gv)
        {
            if (CQTSpaceType.checkIllegal(gv.GetRowCellValue(e.RowHandle, type).ToString()))
            {
                DevExpress.Utils.AppearanceHelper.Apply(e.Appearance, appRed);
            }
        }

        private void setCoverType(DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e, DevExpress.Utils.AppearanceDefault appRed, string type, DevExpress.XtraGrid.Views.Grid.GridView gv)
        {
            if (CQTCoverType.checkIllegal(gv.GetRowCellValue(e.RowHandle, type).ToString()))
            {
                DevExpress.Utils.AppearanceHelper.Apply(e.Appearance, appRed);
            }
        }

        private void setNetworkType(DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e, DevExpress.Utils.AppearanceDefault appRed, string type, DevExpress.XtraGrid.Views.Grid.GridView gv, char splitChar)
        {
            if (CQTNetworkType.checkIllegal(gv.GetRowCellValue(e.RowHandle, type).ToString(), splitChar))
            {
                DevExpress.Utils.AppearanceHelper.Apply(e.Appearance, appRed);
            }
        }

        private void setAltitude(DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e, DevExpress.Utils.AppearanceDefault appRed)
        {
            int iResult;
            if (!int.TryParse(gridView1.GetRowCellValue(e.RowHandle, "Altitude").ToString(), out iResult))
            {
                DevExpress.Utils.AppearanceHelper.Apply(e.Appearance, appRed);
            }
        }

        private void setLongitudeLatitude(DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e, DevExpress.Utils.AppearanceDefault appRed, DevExpress.XtraGrid.Views.Grid.GridView gv)
        {
            double dResult;
            if (!double.TryParse(gv.GetRowCellValue(e.RowHandle, e.Column.FieldName).ToString(), out dResult)
                || dResult < 0 || dResult > 360)
            {
                DevExpress.Utils.AppearanceHelper.Apply(e.Appearance, appRed);
            }
        }

        private void setNetTypeCell(DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e, DevExpress.Utils.AppearanceDefault appRed)
        {
            if (gridView1.GetRowCellValue(e.RowHandle, e.Column.FieldName).ToString().IndexOf(';') >= 0)
            {
                DevExpress.Utils.AppearanceHelper.Apply(e.Appearance, appRed);
            }
        }

        private void clearAtuData()
        {
            atuCqtNameIdDic.Clear();
            atuCqtIDList.Clear();
            xlsAtuPointList.Clear();
            wrongXlsAtuPointList.Clear();
            correctXlsAtuPointList.Clear();
            insertXlsAtuPointDic.Clear();
            buttonImport_Atu.Enabled = false;
        }

        private void initAtu()
        {
            foreach (CQTPoint pnt in CQTPointManager.GetInstance().AtuPntList)
            {
                atuCqtNameIdDic.Add(pnt.Name, pnt.ID);
                atuCqtIDList.Add(pnt.ID);
            }
            atuCqtIDList.Sort();
        }

        private void btnFileOpen_Atu_Click(object sender, EventArgs e)
        {
            clearAtuData();
            initAtu();
            string resultfile = "";
            gridControl_Atu.DataSource = wrongXlsAtuPointList;
            gridControl_Atu.RefreshDataSource();
            OpenFileDialog openfiledialog1 = new OpenFileDialog();
            openfiledialog1.InitialDirectory = "";
            openfiledialog1.Filter = "Excel files |*.xls;*.xlsx";
            openfiledialog1.FilterIndex = 2;
            openfiledialog1.RestoreDirectory = true;
            if (openfiledialog1.ShowDialog() == DialogResult.OK)
            {
                resultfile = openfiledialog1.FileName;
            }
            else
            {
                return;
            }
            textBoxFilePath_Atu.Text = resultfile;
            try
            {
                DataTable datatable = getDatatTable();
                if (datatable.Rows.Count > 0)
                {
                    fillAtu(datatable);
                    checkXlsAtuPoint();

                    if (wrongXlsAtuPointList.Count > 0)
                    {
                        labelFormat_Atu.Text = bHasName ? "库里存在该CQT名" : "格式错误";
                        gridControl_Atu.DataSource = wrongXlsAtuPointList;
                        gridControl_Atu.RefreshDataSource();
                    }
                    else if (insertXlsAtuPointDic.Count < correctXlsAtuPointList.Count)
                    {
                        labelFormat_Atu.Text = "存在同名CQT";
                        addWrongXlsAtuPointList();
                        gridControl_Atu.DataSource = wrongXlsAtuPointList;
                        gridControl_Atu.RefreshDataSource();
                    }
                    else
                    {
                        labelFormat_Atu.Text = "格式正确";
                        buttonImport_Atu.Enabled = true;
                    }
                }
                else
                {
                    labelFormat_Atu.Text = "无地点信息";
                }
            }
            catch
            {
            	//continue
            }
        }

        private DataTable getDatatTable()
        {
            ArrayList sheets;
            DataTable datatable = new DataTable();
            sheets = ExcelTools.GetSheetNameList(textBoxFilePath_Atu.Text.Trim());

            foreach (string item in sheets)
            {
                if (!item.Equals("测试点信息")) continue;
                DataTable table = ExcelTools.ExcelToDataTable(textBoxFilePath_Atu.Text, item);
                if (table.Rows.Count > 0)
                {
                    if (table.Columns.Count < gridViewAtu.Columns.Count)
                    {
                        MessageBox.Show("sheet  " + item + "缺少字段");
                    }
                    else if (table.Columns.Count > gridViewAtu.Columns.Count)
                    {
                        MessageBox.Show("sheet  " + item + "过多字段");
                    }
                    else
                    {
                        datatable.Merge(table);
                    }
                }
            }

            return datatable;
        }
        
        private void addWrongXlsAtuPointList()
        {
            List<XlsAtuPoint> insertList = new List<XlsAtuPoint>(insertXlsAtuPointDic.Values);
            foreach (XlsAtuPoint point in correctXlsAtuPointList)
            {
                if (!insertList.Contains(point))
                {
                    if (!wrongXlsAtuPointList.Contains(point))
                        wrongXlsAtuPointList.Add(point);
                    if (!wrongXlsAtuPointList.Contains(insertXlsAtuPointDic[point.TESTPOINT_DES]))
                        wrongXlsAtuPointList.Add(insertXlsAtuPointDic[point.TESTPOINT_DES]);
                }
            }
        }

        Dictionary<string, int> atuCqtNameIdDic = new Dictionary<string, int>();
        List<int> atuCqtIDList = new List<int>();
        List<XlsAtuPoint> xlsAtuPointList = new List<XlsAtuPoint>();
        List<XlsAtuPoint> wrongXlsAtuPointList = new List<XlsAtuPoint>();
        List<XlsAtuPoint> correctXlsAtuPointList = new List<XlsAtuPoint>();
        Dictionary<string, XlsAtuPoint> insertXlsAtuPointDic = new Dictionary<string, XlsAtuPoint>();
        private void fillAtu(DataTable datatable)
        {
            for (int i = 0; i < datatable.Rows.Count; i++)
            {
                XlsAtuPoint atuPoint = new XlsAtuPoint(datatable.Rows[i]);
                xlsAtuPointList.Add(atuPoint);
            }
        }

        private void checkXlsAtuPoint()
        {
            foreach (XlsAtuPoint xlsAtuPoint in xlsAtuPointList)
            {
                if (xlsAtuPoint.check())
                {
                    wrongXlsAtuPointList.Add(xlsAtuPoint);
                }
                else if (atuCqtNameIdDic.ContainsKey(xlsAtuPoint.TESTPOINT_DES))
                {
                    bHasName = true;
                    wrongXlsAtuPointList.Add(xlsAtuPoint);
                }
                else
                {
                    int id = searchPointID();

                    xlsAtuPoint.PointID = id;
                    correctXlsAtuPointList.Add(xlsAtuPoint);
                    if (!insertXlsAtuPointDic.ContainsKey(xlsAtuPoint.TESTPOINT_DES))
                    {
                        insertXlsAtuPointDic.Add(xlsAtuPoint.TESTPOINT_DES, xlsAtuPoint);
                        atuCqtNameIdDic.Add(xlsAtuPoint.TESTPOINT_DES, xlsAtuPoint.PointID);
                        atuCqtIDList.Add(xlsAtuPoint.PointID);
                    }
                }
            }
        }

        private void buttonImport_Atu_Click(object sender, EventArgs e)
        {
            List<XlsAtuPoint> insertXlsAtuPointList = new List<XlsAtuPoint>(insertXlsAtuPointDic.Values);
            if (insertXlsAtuPointList.Count > 0)
            {
                WaitBox.Show("正在插入CQT地点信息...", backGroundInsert_Atu, insertXlsAtuPointList);
            }
            MessageBox.Show("导入完成");
        }

        public void backGroundInsert_Atu(object o)
        {
            try
            {
                int counter = 0;
                int curPercent = 11;

                string sql;
                List<XlsAtuPoint> insertXlsAtuPointList = o as List<XlsAtuPoint>;
                MainModel mainModel = MainModel.GetInstance();
                DiySqlNonQuery queryInsert;
                foreach (XlsAtuPoint xlsPoint in insertXlsAtuPointList)
                {
                    DiySqlOneIntValueOnly idQuery;
                    sql = "select isnull(max(pointID),0)+1 from tb_cqt_point";
                    idQuery = new DiySqlOneIntValueOnly(mainModel, sql);
                    idQuery.Query();
                    xlsPoint.PointID = idQuery.IntValue;

                    sql = "insert into tb_cqt_point values(" + xlsPoint.PointID + ",'" + xlsPoint.TESTPOINT_DES + "','" + xlsPoint.OFFICIAL_ADDRESS + "','" + xlsPoint.CIVILIAN_ADDRESS + "'," + double.Parse(xlsPoint.LT_LONGITUDE) * 10000000 + "," + double.Parse(xlsPoint.LT_LATITUDE) * 10000000 + "," + double.Parse(xlsPoint.RB_LONGITUDE) * 10000000 + ","
                    + double.Parse(xlsPoint.RB_LATITUDE) * 10000000 + "," + xlsPoint.Altitude + ",'" + xlsPoint.CIVILIAN_NAME + "','" + CQTCfgManager.GetInstance().GetCQTPointType(xlsPoint.FUNCTION_CLASS).ID + "','" + CQTCfgManager.GetInstance().GetCQTDensityType(xlsPoint.DENSITY_CLASS).ID + "','"
                    + CQTCfgManager.GetInstance().GetCQTSpaceType(xlsPoint.SPATIAL_CLASS).ID + "','" + CQTCfgManager.GetInstance().GetCQTCoverType(xlsPoint.COVER_CLASS).ID + "','" + getNetworkIDs(xlsPoint.NETWORK_CLASS, '/') + "',"
                    + xlsPoint.OtherType1 + "," + xlsPoint.OtherType2 + "," + xlsPoint.OtherType3 + "," + xlsPoint.OtherType4 + ",'" + xlsPoint.GRID_ID + "','" + xlsPoint.GROUP_ID + "')";
                    queryInsert = new DiySqlNonQuery(mainModel, sql);
                    queryInsert.Query();
                    CQTPoint cqtPoint = xlsPoint.ConvertToCQTPoint();
                    CQTPointManager.GetInstance().AddPoint(cqtPoint);

                    counter += 40;
                    int tmp = (int)(Math.Log(counter) * (100000 / 10000));
                    if (tmp < 95 && tmp > 0 && curPercent != tmp)
                    {
                        WaitBox.ProgressPercent = tmp;
                        curPercent = tmp;
                    }
                    else if (tmp > 95)
                    {
                        curPercent = 5;
                        counter = 0;
                    }
                }
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void gridViewAtu_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            DevExpress.Utils.AppearanceDefault appRed = new DevExpress.Utils.AppearanceDefault(
                Color.Black, Color.Red, Color.Empty, Color.SeaShell, System.Drawing.Drawing2D.LinearGradientMode.Horizontal);
            switch (e.Column.FieldName)
            {
                case "TESTPOINT_DES":
                    setName(e, appRed, gridViewAtu, atuCqtNameIdDic);
                    break;
                case "OFFICIAL_ADDRESS":
                    setAddrAt(e, appRed, gridViewAtu);
                    break;
                case "FUNCTION_CLASS":
                    setPointType(e, appRed, "FUNCTION_CLASS", gridViewAtu);
                    break;
                case "DENSITY_CLASS":
                    setDensityType(e, appRed, "DENSITY_CLASS", gridViewAtu);
                    break;
                case "SPATIAL_CLASS":
                    setSpaceType(e, appRed, "SPATIAL_CLASS", gridViewAtu);
                    break;
                case "COVER_CLASS":
                    setCoverType(e, appRed, "COVER_CLASS", gridViewAtu);
                    break;
                case "NETWORK_CLASS":
                    setNetworkType(e, appRed, "NETWORK_CLASS", gridViewAtu, '/');
                    break;
                case "LT_LONGITUDE":
                case "LT_LATITUDE":
                case "RB_LONGITUDE":
                case "RB_LATITUDE":
                    setLongitudeLatitude(e, appRed, gridViewAtu);
                    break;
                default:
                    break;
            }

        }
    }
}