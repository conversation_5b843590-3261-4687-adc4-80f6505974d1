﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakSinrSampleRoadQuery_NR : ZTWeakSINRRoadNRQuery
    {
        private static WeakSinrSampleRoadQuery_NR instance;
        public static WeakSinrSampleRoadQuery_NR Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new WeakSinrSampleRoadQuery_NR();
                }
                return instance;
            }
        }

        public override string Name
        {
            get { return "质差采样点统计_NR"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35009, this.Name);
        }

        protected override void fireShowForm()
        {
            List<WeakSinrSampleRoadNR> roadList = new List<WeakSinrSampleRoadNR>();
            foreach (WeakSINRRoad road in weakCoverList)
            {
                if (road is WeakSINRRoadNR)
                {
                    WeakSINRRoadNR nrRoad = road as WeakSINRRoadNR;
                    WeakSinrSampleRoadNR sampleRoad = new WeakSinrSampleRoadNR(nrRoad);
                    sampleRoad.GetResult();
                    roadList.Add(sampleRoad);
                }
            }

            WeakSinrSampleRoadNRForm frm = MainModel.CreateResultForm(typeof(WeakSinrSampleRoadNRForm)) as WeakSinrSampleRoadNRForm;
            frm.FillData(roadList);
            frm.Visible = true;
            frm.BringToFront();
            weakCoverList = null;
        }
    }

    public class WeakSinrSampleRoadNR : WeakSINRRoadNR
    {
        public WeakSinrSampleRoadNR(WeakSINRRoadNR baseRoad) : base(baseRoad)
        {
        }

        public List<WeakSinrSampleInfoNR> SampleInfos { get; protected set; }

        public void GetResult()
        {
            SampleInfos = new List<WeakSinrSampleInfoNR>();
            int sn = 0;
            foreach (TestPoint tp in TestPoints)
            {
                WeakSinrSampleInfoNR sample = new WeakSinrSampleInfoNR(tp);
                sample.SN = ++sn;
                addValidData(sample.LteRsrp, ref lteRsrpCount, ref lteRsrpSum);
                addValidData(sample.LteSinr, ref lteSinrCount, ref lteSinrSum);
                SampleInfos.Add(sample);
            }
            AvgLteRSRP = caculateAvg(lteRsrpCount, lteRsrpSum);
            AvgLteSINR = caculateAvg(lteSinrCount, lteSinrSum);
        }

        private void addValidData(float? data, ref int count, ref float sum)
        {
            if (data != null)
            {
                count++;
                sum += (float)data;
            }
        }
    }

    public class WeakSinrSampleInfoNR : WeakSinrSampleInfo
    {
        public long? Nci { get; protected set; }
        public float? LteRsrp { get; protected set; }
        public float? LteSinr { get; protected set; }

        public WeakSinrSampleInfoNR(TestPoint tp)
        {
            TestPoint = tp;
            TimeString = tp.DateTimeStringWithMillisecond;
            Longitude = tp.Longitude;
            Latitude = tp.Latitude;

            Earfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
            Pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);

            NRCell nrCell = CellManager.GetInstance().GetNearestNRCell(tp.DateTime, Tac, Eci, Earfcn, Pci, Longitude, Latitude);
            if (nrCell == null)
            {
                Tac = (int?)NRTpHelper.NrTpManager.GetTAC(tp);
                Nci = (long?)NRTpHelper.NrTpManager.GetNCI(tp);
                CellID = CellName = "";
            }
            else
            {
                Tac = nrCell.TAC;
                Nci = nrCell.NCI;
                CellID = nrCell.ID.ToString();
                CellName = nrCell.Name;
            }

            Rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp, true);
            Sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp, true);
            Rsrq = NRTpHelper.NrTpManager.GetSCellRsrq(tp, true);

            LteRsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(tp, true);
            LteSinr = NRTpHelper.NrLteTpManager.GetSCellSinr(tp, true);
        }
    }

    public class NRWeakSinrSampleRoadQueryByFile : WeakSinrSampleRoadQuery_NR
    {
        private static NRWeakSinrSampleRoadQueryByFile instance = null;
        public new static NRWeakSinrSampleRoadQueryByFile GetInstance()
        {
            if (instance == null)
            {
                instance = new NRWeakSinrSampleRoadQueryByFile();
            }
            return instance;
        }

        public override string Name
        {
            get { return "质差采样点统计_NR(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isTPInRegion(TestPoint tp)
        {
            return true;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
    }
}
