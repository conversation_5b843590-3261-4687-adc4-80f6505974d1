﻿namespace MasterCom.RAMS.ZTFunc.ZTLastWeakRoadAna
{
    partial class TdLastWeakRoadForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.listView = new System.Windows.Forms.ListView();
            this.columnHeaderSN = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderType = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderDistance = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderDuration = new System.Windows.Forms.ColumnHeader();

            this.columnHeaderRelmean = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRxl75 = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRxl76_80 = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRxl81_85 = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRxl86_90 = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRxl91_94 = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderRxl94 = new System.Windows.Forms.ColumnHeader();

            this.columnHeaderDc2imean = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderDc2iF10 = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderDc2iF10TF3 = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderDc2iF3T15 = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderDc2i15 = new System.Windows.Forms.ColumnHeader();

            this.columnHeaderPesqmean = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderPesq28 = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderPesq28_30 = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderPesq30 = new System.Windows.Forms.ColumnHeader();

            this.columnHeaderTxpowermean = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderTxpowerF20 = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderTxpowerF20T0 = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderTxpower0T15 = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderTxpower15 = new System.Windows.Forms.ColumnHeader();

            this.columnHeaderCell1 = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCell2 = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderCell3 = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderStrgrid = new System.Windows.Forms.ColumnHeader();
            this.columnHeaderStrroad = new System.Windows.Forms.ColumnHeader();

            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItemExport2xls = new System.Windows.Forms.ToolStripMenuItem();

            this.ctxMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // listView
            // 
            this.listView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderSN,
            this.columnHeaderType,
            this.columnHeaderDistance,
            this.columnHeaderDuration,

            this.columnHeaderRelmean,
            this.columnHeaderRxl75,
            this.columnHeaderRxl76_80,
            this.columnHeaderRxl81_85,
            this.columnHeaderRxl86_90,
            this.columnHeaderRxl91_94,
            this.columnHeaderRxl94,

            this.columnHeaderDc2imean,
            this.columnHeaderDc2iF10,
            this.columnHeaderDc2iF10TF3,
            this.columnHeaderDc2iF3T15,
            this.columnHeaderDc2i15,

            this.columnHeaderPesqmean,
            this.columnHeaderPesq28,
            this.columnHeaderPesq28_30,
            this.columnHeaderPesq30,

            this.columnHeaderTxpowermean,
            this.columnHeaderTxpowerF20,
            this.columnHeaderTxpowerF20T0,
            this.columnHeaderTxpower0T15,
            this.columnHeaderTxpower15,

            this.columnHeaderCell1,
            this.columnHeaderCell2,
            this.columnHeaderCell3,
            this.columnHeaderStrgrid,
            this.columnHeaderStrroad});

            this.listView.ContextMenuStrip = this.ctxMenu;
            this.listView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listView.FullRowSelect = true;
            this.listView.GridLines = true;
            this.listView.Location = new System.Drawing.Point(0, 0);
            this.listView.MultiSelect = false;
            this.listView.Name = "listView";
            this.listView.ShowGroups = false;
            this.listView.Size = new System.Drawing.Size(793, 411);
            this.listView.TabIndex = 0;
            this.listView.UseCompatibleStateImageBehavior = false;
            this.listView.View = System.Windows.Forms.View.Details;
            this.listView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listView_MouseDoubleClick);

            this.columnHeaderSN.Text = "序号" ;
            this.columnHeaderType.Text = "问题类型" ;
            this.columnHeaderDistance.Text = "距离(米)";
            this.columnHeaderDuration.Text = "时长(秒)" ;

            this.columnHeaderRelmean.Text = "电平均值";
            this.columnHeaderRxl75.Text = "电平[-75,-10]" ;
            this.columnHeaderRxl75.Width = 100;
            this.columnHeaderRxl76_80.Text = "电平[-80,-75)";
            this.columnHeaderRxl76_80.Width = 100;
            this.columnHeaderRxl81_85.Text = "电平[-85,-80)";
            this.columnHeaderRxl81_85.Width = 100;
            this.columnHeaderRxl86_90.Text = "电平[-90,-85)";
            this.columnHeaderRxl86_90.Width = 100;
            this.columnHeaderRxl91_94.Text = "电平[-94,-90)";
            this.columnHeaderRxl91_94.Width = 100;
            this.columnHeaderRxl94.Text = "电平[-140,-94)";
            this.columnHeaderRxl94.Width = 100;

            this.columnHeaderDc2imean.Text = "DPCH C/I均值" ;
            this.columnHeaderDc2imean.Width = 70;
            this.columnHeaderDc2iF10.Text = "C/I[-20,-10)";
            this.columnHeaderDc2iF10.Width = 70;
            this.columnHeaderDc2iF10TF3.Text = "C/I[-10,-3)";
            this.columnHeaderDc2iF10TF3.Width = 70;
            this.columnHeaderDc2iF3T15.Text = "C/I[-3,15)";
            this.columnHeaderDc2iF3T15.Width = 70;
            this.columnHeaderDc2i15.Text = "C/I[15,25]";
            this.columnHeaderDc2i15.Width = 70;

            this.columnHeaderPesqmean.Text = "MOS均值" ;
            this.columnHeaderPesq28.Text = "MOS[0,2.8)";
            this.columnHeaderPesq28.Width = 100;
            this.columnHeaderPesq28_30.Text = "MOS[2.8,3)";
            this.columnHeaderPesq28_30.Width = 100;
            this.columnHeaderPesq30.Text = "MOS[3,5]";
            this.columnHeaderPesq30.Width = 70;

            this.columnHeaderTxpowermean.Text = "TxPower均值" ;
            this.columnHeaderTxpowermean.Width = 70;
            this.columnHeaderTxpowerF20.Text = "TxPower[-50,-20)";
            this.columnHeaderTxpowerF20.Width = 70;
            this.columnHeaderTxpowerF20T0.Text = "TxPower[-20,0)";
            this.columnHeaderTxpowerF20T0.Width = 70;
            this.columnHeaderTxpower0T15.Text = "TxPower[0,15)";
            this.columnHeaderTxpower0T15.Width = 70;
            this.columnHeaderTxpower15.Text = "TxPower[15,34]";
            this.columnHeaderTxpower15.Width = 70;

            this.columnHeaderCell1.Text = "小区1" ;
            this.columnHeaderCell1.Width = 100;
            this.columnHeaderCell2.Text = "小区2" ;
            this.columnHeaderCell2.Width = 100;
            this.columnHeaderCell3.Text = "小区3" ;
            this.columnHeaderCell3.Width = 100;
            this.columnHeaderStrgrid.Text = "所属网格" ;
            this.columnHeaderStrgrid.Width = 70;
            this.columnHeaderStrroad.Text = "所属道路";
            this.columnHeaderStrroad.Width = 100;
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItemExport2xls});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(151, 26);
            // 
            // toolStripMenuItemExport2xls
            // 
            this.toolStripMenuItemExport2xls.Name = "toolStripMenuItemExport2xls";
            this.toolStripMenuItemExport2xls.Size = new System.Drawing.Size(150, 22);
            this.toolStripMenuItemExport2xls.Text = "导出到Excel...";
            this.toolStripMenuItemExport2xls.Click += new System.EventHandler(this.toolStripMenuItemExport2xls_Click);
            // 
            // RxQualLastEventInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(793, 411);
            this.Controls.Add(this.listView);
            this.Name = "TdLastWeakRoadForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "TD持续差道路详细信息";
            this.ctxMenu.ResumeLayout(false);
            this.ResumeLayout(false);
        }

        #endregion

        private System.Windows.Forms.ListView listView;
        private System.Windows.Forms.ColumnHeader columnHeaderSN;
        private System.Windows.Forms.ColumnHeader columnHeaderType;
        private System.Windows.Forms.ColumnHeader columnHeaderDistance;
        private System.Windows.Forms.ColumnHeader columnHeaderDuration;

        private System.Windows.Forms.ColumnHeader columnHeaderRelmean;
        private System.Windows.Forms.ColumnHeader columnHeaderRxl75;
        private System.Windows.Forms.ColumnHeader columnHeaderRxl76_80;
        private System.Windows.Forms.ColumnHeader columnHeaderRxl81_85;
        private System.Windows.Forms.ColumnHeader columnHeaderRxl86_90;
        private System.Windows.Forms.ColumnHeader columnHeaderRxl91_94;
        private System.Windows.Forms.ColumnHeader columnHeaderRxl94;

        private System.Windows.Forms.ColumnHeader columnHeaderDc2imean;
        private System.Windows.Forms.ColumnHeader columnHeaderDc2iF10;
        private System.Windows.Forms.ColumnHeader columnHeaderDc2iF10TF3;
        private System.Windows.Forms.ColumnHeader columnHeaderDc2iF3T15;
        private System.Windows.Forms.ColumnHeader columnHeaderDc2i15;

        private System.Windows.Forms.ColumnHeader columnHeaderPesqmean;
        private System.Windows.Forms.ColumnHeader columnHeaderPesq28;
        private System.Windows.Forms.ColumnHeader columnHeaderPesq28_30;
        private System.Windows.Forms.ColumnHeader columnHeaderPesq30;

        private System.Windows.Forms.ColumnHeader columnHeaderTxpowermean;
        private System.Windows.Forms.ColumnHeader columnHeaderTxpowerF20;
        private System.Windows.Forms.ColumnHeader columnHeaderTxpowerF20T0;
        private System.Windows.Forms.ColumnHeader columnHeaderTxpower0T15;
        private System.Windows.Forms.ColumnHeader columnHeaderTxpower15;

        private System.Windows.Forms.ColumnHeader columnHeaderCell1;
        private System.Windows.Forms.ColumnHeader columnHeaderCell2;
        private System.Windows.Forms.ColumnHeader columnHeaderCell3;
        private System.Windows.Forms.ColumnHeader columnHeaderStrgrid;
        private System.Windows.Forms.ColumnHeader columnHeaderStrroad;

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemExport2xls;

    }
}