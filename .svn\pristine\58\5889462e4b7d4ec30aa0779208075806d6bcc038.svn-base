﻿using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Stat;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.src.MasterCom.Util
{
    public partial class KPIFormulaItemEditor : BaseForm
    {
        public KPIFormulaItemEditor()
        {
            InitializeComponent();
            initParams();
        }

        private ConfigSetSwitch css = new ConfigSetSwitch();
        private ParamCfgItem curParamCfgItem = null;    //当前节点（当前选中项）
        private ListView curLv = null;      //当前的Listview

        private static int maxSubCount = 6;    //最大子目录数量
        private ParamCfgItem[] paramParents = new ParamCfgItem[maxSubCount + 1];//最大子目录有6个,加上根目录共7个。这里分别用来存储子目录对应的父ParamCfgParam

        //初始化根目录
        public void initParams()
        {
            if(css.paramNodesItems != null)
            {
                css.paramNodesItems.Clear();    //清空上次保存的数据
            }
            System.IO.DirectoryInfo directory = new System.IO.DirectoryInfo(string.Format(Application.StartupPath + "/config"));
            foreach (System.IO.FileInfo file in directory.GetFiles("statinitargs_kpi_*.xml"))
            {
                css.addData(ParamCfgItem.loadParamCfgFromFile(file.FullName));
            }
            lvRoot.Items.Clear();
            if (css.paramNodesItems == null) return;
            foreach (ParamCfgItem cfgItem in css.paramNodesItems)
            {
                ListViewItem item = new ListViewItem();
                item.Text = cfgItem.nodeName;
                item.ToolTipText = cfgItem.nodeDesc;
                item.Tag = cfgItem;
                lvRoot.Items.Add(item);
            }
        }

        private void setBtnFalse()
        {
            txtParamName.Enabled = false;
            btnUp.Enabled = false;
            btnDown.Enabled = false;
            txtFName.Enabled = false;
            txtFTag.Enabled = false;
            btnSaveChange.Enabled = false;
            listMenu.Items[2].Enabled = false;
            btnAddParamItem.Enabled = false;
        }
        
        private void checkTag()
        {
            if (curParamCfgItem.tag != null)
            {
                btnSaveChange.Enabled = true;
                txtFName.Text = curParamCfgItem.tag.field;
                txtFTag.Text = curParamCfgItem.tag.arg.ToString();
            }
            if (curParamCfgItem.nodeDesc != null)
            {
                txtDesc.Text = curParamCfgItem.nodeDesc;
            }
        }

        private void txtClear()
        {
            txtParamName.Text = "";
            txtFName.Text = "";
            txtFTag.Text = "";
            txtDesc.Text = "";
        }

        private GroupControl curFocusGc;//当前所在的GroupControl
        private int num = 0;    //子目录标记
        private ListViewItem curListItem = null;//当前的ListItem，用于当点击ListView的空白处时，选中项的高亮不会消失
        private int selectedItemsCount = 0;//被选中的item数量

        private ListView curRootSubLv = null;//当前根ListView的子ListView
        private ListView curSubSubLv = null;//当前选中ListView的子ListView

        //获取当前SplitContainerControl、GroupControl和ListView 的 Size，使得动态生成的控件大小相同
        private Size curSccSize;
        private Size curGcSize;
        private Size curLvSize;
        private void lvRoot_MouseUp(object sender, MouseEventArgs e)
        {
            curLv = sender as ListView;
            listMenu.Items[0].Enabled = true;
            listMenu.Items[1].Enabled = false;//只能添加目录

            txtClear();
            setBtnFalse();

            //获取当前控件大小（在设计视图上已经存在的）
            curSccSize = splitContainerControl1.Panel2.Size;
            curGcSize = groupControl1.Size;
            curLvSize = lvRoot.Size;

            //如果点击时在当前的ListView中有选中的项
            if (curLv.SelectedItems.Count > 0)
            {
                selectedItemsCount = curLv.SelectedItems.Count;//获取当前ListView选中项数量

                curListItem = curLv.SelectedItems[0];//curListItem用于实现选中项一直高亮
                int controlsCount = splitContainerControl1.Panel2.Controls.Count;//splitContainerControl1中的控件数量

                curParamCfgItem = curLv.SelectedItems[0].Tag as ParamCfgItem;//绑定数据
                paramParents[0] = curParamCfgItem;//绑定根节点，单个文件

                txtParamName.Enabled = true;
                btnSaveChange.Enabled = true;
                txtParamName.Text = curListItem.Text;
                txtDesc.Text = curParamCfgItem.nodeDesc;

                //如果splitContainerControl1里面有控件，说明已经有显示（有刷新数据）
                refreshControlsData(controlsCount);

                //如果splitContainerControl1里面没有控件，说明是第一次点击,生成子控件
                createControls(controlsCount);
            }
            else
            {
                if (curLv.Items.Count > 0 && selectedItemsCount > 0 && curListItem != null && curLv.FocusedItem != null)
                {
                    curLv.FocusedItem = curListItem;
                    curLv.FocusedItem.Selected = true;
                }
            }
        }

        private void refreshControlsData(int controlsCount)
        {
            if (controlsCount > 0)
            {
                curRootSubLv.Items.Clear();
                foreach (ParamCfgItem cfgItem in curParamCfgItem.children)
                {
                    ListViewItem item = new ListViewItem();
                    item.Text = cfgItem.nodeName;
                    item.ToolTipText = cfgItem.nodeDesc;
                    item.Tag = cfgItem;
                    curRootSubLv.Items.Add(item);
                }

                SplitContainerControl curSub = splitContainerControl1.Panel2.Controls[0] as SplitContainerControl;//获取splitContainerControl1中的子控件
                                                                                                                  //如果子控件splitContainerControl1中还有子控件，则将它们清空
                if (curSub.Panel2.Controls.Count > 0)
                {
                    curSub.Panel2.Controls.Clear();
                }
            }
        }

        private void createControls(int controlsCount)
        {
            if (controlsCount == 0)
            {
                SplitContainerControl nextScc = new SplitContainerControl();
                GroupControl nextGc = new GroupControl();
                nextGc.Text = "1" + " 级子目录";
                ListView nextLv = new ListView();

                nextScc.Dock = DockStyle.Fill;
                nextGc.Dock = DockStyle.Fill;
                nextLv.Dock = DockStyle.Fill;

                nextGc.ShowCaption = true;
                nextScc.Size = curSccSize;
                nextScc.Panel1.Size = curSccSize;
                nextGc.Size = curGcSize;
                nextScc.SplitterPosition = nextGc.Width;
                nextGc.Location = groupControl1.Location;
                nextLv.Location = lvRoot.Location;
                nextLv.Size = curLvSize;
                nextLv.HideSelection = false;
                nextLv.View = View.List;

                splitContainerControl1.Panel2.Controls.Add(nextScc);
                nextScc.Panel1.Controls.Add(nextGc);
                nextGc.Controls.Add(nextLv);

                foreach (ParamCfgItem cfgItem in curParamCfgItem.children)
                {
                    ListViewItem item = new ListViewItem();
                    item.Text = cfgItem.nodeName;
                    item.ToolTipText = cfgItem.nodeDesc;
                    item.Tag = cfgItem;
                    nextLv.Items.Add(item);
                }

                nextScc.Show();
                nextGc.Show();
                nextLv.Show();

                curRootSubLv = nextLv;//绑定当前ListView的子控件

                nextLv.MouseUp += nextLv_MouseUp;
                nextLv.ContextMenuStrip = listMenu;
            }
        }

        private void nextLv_MouseUp(object sender, MouseEventArgs e)
        {
            curLv = sender as ListView;//当前ListView
            listMenu.Items[1].Enabled = true;

            txtClear();
            setBtnFalse();

            GroupControl curGc = curLv.Parent as GroupControl;//当前GroupControl
            SplitGroupPanel curSgPanel = curGc.Parent as SplitGroupPanel;//当前SplitContainerControl的Panel1
            SplitContainerControl curScc = curSgPanel.Parent as SplitContainerControl;//当前SplitContainerControl
            
            curFocusGc = curGc;//当前获得焦点的GroupControl

            //获取当前控件大小（动态生成的）
            curSccSize = curScc.Panel2.Size;
            curGcSize = curGc.Size;
            curLvSize = curLv.Size;

            //获取GroupControl的标题中的数字，用于标记
            string s = curGc.Text.Substring(0, 2).Trim();
            num = int.Parse(s);

            if (curLv.SelectedItems.Count > 0)
            {
                selectedItemsCount = curLv.SelectedItems.Count;

                listMenu.Items[2].Enabled = true;//有选中项则可以删除，根目录除外

                //获取当前选中项的绑定数据
                curParamCfgItem = curLv.SelectedItems[0].Tag as ParamCfgItem;
                paramParents[num] = curParamCfgItem;
                txtDesc.Text = curParamCfgItem.nodeDesc;

                //选中参数的参数名显示
                curListItem = curLv.SelectedItems[0];
                txtParamName.Text = curListItem.Text.ToString();
                txtParamName.Enabled = true;//有选中项时可以编辑参数名
                btnSaveChange.Enabled = true;//有选中项时，修改后保存

                btnStateCheck(curLv);//更新上下按钮及TextEdit的状态
                int controlsCount = curScc.Panel2.Controls.Count;//当前SplitContainerControl的子控件数量

                setControl(curScc, controlsCount);
                checkTag();
            }
            else
            {
                num++;//用于判断是否是最后一个ListView
                if (curLv.Items.Count > 0 && selectedItemsCount > 0 && curListItem != null && curLv.FocusedItem != null)
                {
                    curLv.FocusedItem = curListItem;
                    curLv.FocusedItem.Selected = true;
                }
            }
            listMenu.Items[0].Enabled = true;//无论是否有选中项，都可以添加参数目录，第6级除外
            listMenu.Items[1].Enabled = true;

            //如果是最后一个ListView，则将添加参数目录的按钮置为不可用
            if(num > maxSubCount)
            {
                listMenu.Items[0].Enabled = false;
            }
            else
            {
                listMenu.Items[0].Enabled = true;
            }
        }

        private void setControl(SplitContainerControl curScc, int controlsCount)
        {
            if (num <= maxSubCount)
            {
                if (curParamCfgItem.tag != null)
                {
                    curScc.Panel2.Controls.Clear();
                    num++;
                }
                else
                {
                    clearControl(curScc, controlsCount);
                    num++;//标记当前的父目录，累加

                    if (num <= maxSubCount && controlsCount == 0)
                    {
                        SplitContainerControl nextScc = new SplitContainerControl();
                        GroupControl nextGc = new GroupControl();
                        nextGc.Text = num + " 级子目录";
                        ListView nextLv = new ListView();
                        curSubSubLv = nextLv;

                        nextScc.Dock = DockStyle.Fill;
                        nextGc.Dock = DockStyle.Fill;
                        nextLv.Dock = DockStyle.Fill;

                        nextGc.ShowCaption = true;
                        nextScc.Size = curSccSize;
                        nextScc.Panel1.Size = curSccSize;

                        //最后一级子目录会填满剩余空白区域
                        setMenuEnable(nextScc, nextGc, nextLv);

                        nextScc.SplitterPosition = nextGc.Width;
                        nextGc.Location = groupControl1.Location;
                        nextLv.Location = lvRoot.Location;
                        nextLv.HideSelection = false;
                        nextLv.View = View.List;

                        curScc.Panel2.Controls.Add(nextScc);
                        nextScc.Panel1.Controls.Add(nextGc);
                        nextGc.Controls.Add(nextLv);

                        //如果当前项含有子节点
                        addSubNode(nextLv);
                        /* 结束 */

                        nextScc.Show();
                        nextGc.Show();
                        nextLv.Show();

                        nextLv.MouseUp += nextLv_MouseUp;
                        nextLv.ContextMenuStrip = listMenu;
                    }
                }
            }
        }

        private void clearControl(SplitContainerControl curScc, int controlsCount)
        {
            if (controlsCount > 0)//判断当前SplitContainerControl的Panel2中是否有控件
            {
                //获取当前ListView的子ListView
                SplitContainerControl curSubSubScc = curScc.Panel2.Controls[0] as SplitContainerControl;
                GroupControl curSubSubGc = curSubSubScc.Panel1.Controls[0] as GroupControl;
                curSubSubLv = curSubSubGc.Controls[0] as ListView;
                curSubSubLv.Items.Clear();//清空子Listview中保存的数据，重新赋值

                foreach (ParamCfgItem cfgItem in curParamCfgItem.children)
                {
                    ListViewItem item = new ListViewItem();
                    item.Text = cfgItem.nodeName;
                    item.ToolTipText = cfgItem.nodeDesc;
                    item.Tag = cfgItem;
                    curSubSubLv.Items.Add(item);
                }

                //获取当前Panel2 中的子控件（SplitContainerControl），清空该子控件scc的Panel2中存在的其它子控件
                SplitContainerControl curSub = curScc.Panel2.Controls[0] as SplitContainerControl;
                if (curSub.Panel2.Controls.Count > 0)
                {
                    curSub.Panel2.Controls.Clear();
                }
            }
        }

        private void setMenuEnable(SplitContainerControl nextScc, GroupControl nextGc, ListView nextLv)
        {
            if (num == maxSubCount)
            {
                nextGc.Text = nextGc.Text + "（最多" + maxSubCount + "级）";
                nextGc.Size = new Size(curSccSize.Width - 10, curGcSize.Height);
                nextLv.Size = new Size(curSccSize.Width - 14, curLvSize.Height);
                listMenu.Items[0].Enabled = false;//设置菜单不可用
            }
            else
            {
                if (nextScc.Width < 150)
                {
                    nextGc.Size = new Size(curSccSize.Width - 10, curGcSize.Height);
                    nextLv.Size = new Size(curSccSize.Width - 14, curLvSize.Height);
                }
                else
                {
                    nextGc.Size = curGcSize;
                    nextLv.Size = curLvSize;
                }
                listMenu.Items[0].Enabled = true;//设置菜单可用
            }
        }

        private void addSubNode(ListView nextLv)
        {
            if (curParamCfgItem.children.Count > 0)
            {
                txtFName.Enabled = false;//改变控件编辑状态
                txtFTag.Enabled = false;
                //将子节点的内容绑定到当前项的子ListView中
                foreach (ParamCfgItem cfgItem in curParamCfgItem.children)
                {
                    ListViewItem item = new ListViewItem();
                    item.Text = cfgItem.nodeName;
                    item.ToolTipText = cfgItem.nodeDesc;
                    item.Tag = cfgItem;
                    nextLv.Items.Add(item);
                }
            }
            else
            {
                checkTag();//如果不含子节点，检查各属性的值是否为空，并根据情况显示到编辑框中
            }
        }

        /**
         * 判断选中项位置，改变按钮状态（可用和不可用）
         * */
        private void btnStateCheck(ListView curLv)
        {
            if (curLv.SelectedItems[0].Index == 0)
            {
                btnUp.Enabled = false;
            }
            else
            {
                btnUp.Enabled = true;
            }

            if (curLv.SelectedItems[0].Index == (curLv.Items.Count - 1))
            {
                btnDown.Enabled = false;
            }
            else
            {
                btnDown.Enabled = true;
            }
        }


        //上移选中项
        private void btnUp_Click(object sender, EventArgs e)
        {
            int index = curLv.SelectedItems[0].Index;

            if(index >= 0 && index < curLv.Items.Count)
            {
                ListViewItem listItem = curLv.SelectedItems[0];
                curLv.SelectedItems[0].Remove();
                curLv.Items.Insert(index - 1, listItem);

                string s = curFocusGc.Text.Substring(0, 2).Trim();
                int paramNum = int.Parse(s);

                paramParents[paramNum - 1].children.Remove(curParamCfgItem);
                paramParents[paramNum - 1].children.Insert(index - 1, curParamCfgItem);
                
                btnStateCheck(curLv);
                ParamCfgItem.saveChangeToFile(paramParents[0].fileName, paramParents[0]);
            }
            
        }

        //下移选中项
        private void btnDown_Click(object sender, EventArgs e)
        {
            int index = curLv.SelectedItems[0].Index;

            if(index >= 0 && index < curLv.Items.Count)
            {
                ListViewItem listItem = curLv.SelectedItems[0];
                curLv.SelectedItems[0].Remove();
                curLv.Items.Insert(index + 1, listItem);

                string s = curFocusGc.Text.Substring(0, 2).Trim();
                int paramNum = int.Parse(s);

                paramParents[paramNum - 1].children.Remove(curParamCfgItem);
                paramParents[paramNum - 1].children.Insert(index + 1, curParamCfgItem);

                btnStateCheck(curLv);
                ParamCfgItem.saveChangeToFile(paramParents[0].fileName, paramParents[0]);
            }
        }

        /**
         * 保存修改，修改名称和移动位置每次操作完就保存，不需要点击保存
         * */
        private void btnSaveChange_Click(object sender, EventArgs e)
        {
            if(curParamCfgItem.children.Count > 0)
            {
                curParamCfgItem.nodeName = txtParamName.Text.Trim();
                curParamCfgItem.nodeDesc = txtDesc.Text.Trim();
                curLv.SelectedItems[0].Text = txtParamName.Text.Trim();
                ParamCfgItem.saveChangeToFile(paramParents[0].fileName, paramParents[0]);
            }
            else
            {
                curParamCfgItem.nodeName = txtParamName.Text.Trim();
                curLv.SelectedItems[0].Text = txtParamName.Text.Trim();
                if (curParamCfgItem.tag != null && txtFName.Text.Trim() != "" && txtFTag.Text.Trim() != "")
                {
                    curParamCfgItem.tag.field = txtFName.Text.Trim();
                    int arg = 0;
                    if (int.TryParse(txtFTag.Text.Trim(), out arg))
                    {
                        curParamCfgItem.tag.arg = arg;
                    }
                    else
                    {
                        MessageBox.Show("公式值必须是整数");
                        return;
                    }
                    curParamCfgItem.nodeDesc = txtDesc.Text.Trim();
                    ParamCfgItem.saveChangeToFile(paramParents[0].fileName, paramParents[0]);
                }
                else
                {
                    MessageBox.Show("公式名和公式值不能为空");
                }
            }
            
        }

        private void btnAddParamItem_Click(object sender, EventArgs e)
        {
            if(txtParamName.Text.Trim() != "" && txtFName.Text.Trim() != "" && txtFTag.Text.Trim() != "")
            {
                ParamCfgItem newParamItem = new ParamCfgItem();
                newParamItem.nodeName = txtParamName.Text.Trim();
                newParamItem.tag = new FieldTag(null, 0);
                newParamItem.tag.field = txtFName.Text.Trim();

                int arg = 0;
                if (int.TryParse(txtFTag.Text.Trim(), out arg))
                {
                    newParamItem.tag.arg = arg;
                }
                else
                {
                    MessageBox.Show("公式值必须是整数");
                    return;
                }
                newParamItem.nodeDesc = txtDesc.Text.Trim();

                ListViewItem newItem = new ListViewItem();
                newItem.Text = txtParamName.Text.Trim();
                newItem.Tag = newParamItem;
                curLv.Items.Add(newItem);

                string s = curFocusGc.Text.Substring(0, 2);
                int paramNum = int.Parse(s);
                paramParents[paramNum - 1].children.Add(newParamItem);
                ParamCfgItem.saveChangeToFile(paramParents[0].fileName, paramParents[0]);

                setBtnFalse();
            }
            else
            {
                MessageBox.Show("参数名及公式不能为空");
            }
        }

        private void menuAddParamDir_Click(object sender, EventArgs e)
        {
            GroupControl gc = curLv.Parent as GroupControl;
            if (gc.Text == "根目录")
            {
                TextInputBox nameEditBox = new TextInputBox("新建根目录", "请输入根目录名称：", null);
                nameEditBox.ShowDialog();

                if (nameEditBox.DialogResult == DialogResult.OK)
                {
                    TextInputBox fileNameEditBox = new TextInputBox("保存文件", "目录将生成新的文件，请填写文件名(无需前后缀)", null);
                    fileNameEditBox.ShowDialog();

                    if(fileNameEditBox.DialogResult == DialogResult.OK)
                    {
                        ParamCfgItem rootParamDir = new ParamCfgItem();
                        rootParamDir.nodeName = nameEditBox.TextInput.Trim();
                        ListViewItem newItem = new ListViewItem();
                        newItem.Text = nameEditBox.TextInput.Trim();
                        newItem.Tag = rootParamDir;
                        curLv.Items.Add(newItem);

                        //文件的保存路径
                        System.IO.DirectoryInfo directory = new System.IO.DirectoryInfo(string.Format(Application.StartupPath + "/config/"));

                        //格式化文件名
                        string fileFullName = directory + "statinitargs_kpi_" + fileNameEditBox.TextInput.Trim() + ".xml";
                        ParamCfgItem.saveChangeToFile(fileFullName, rootParamDir);

                        splitContainerControl1.Panel2.Controls.Clear();
                        initParams();//刷新根目录
                    }
                }
            }
            else
            {
                string s = curFocusGc.Text.Substring(0, 2).Trim();
                int paramNum = int.Parse(s);
                TextInputBox nameEditBox = new TextInputBox("新建参数目录", "请输入目录名称：", null);
                nameEditBox.ShowDialog();

                if (nameEditBox.DialogResult == DialogResult.OK)
                {
                    ParamCfgItem newParamDir = new ParamCfgItem();
                    newParamDir.nodeName = nameEditBox.TextInput;
                    ListViewItem newItem = new ListViewItem();
                    newItem.Text = nameEditBox.TextInput;
                    newItem.Tag = newParamDir;
                    curLv.Items.Add(newItem);

                    paramParents[paramNum - 1].children.Add(newParamDir);
                    ParamCfgItem.saveChangeToFile(paramParents[0].fileName, paramParents[0]);
                }
            }
            
        }

        private void menuAddParam_Click(object sender, EventArgs e)
        {
            txtClear();
            btnAddParamItem.Enabled = true;
            txtParamName.Enabled = true;
            txtFName.Enabled = true;
            txtFTag.Enabled = true;
            txtParamName.Focus();
        }

        private void menuDeleteParam_Click(object sender, EventArgs e)
        {
            
            DialogResult dr = MessageBox.Show("确定要删除选中参数吗？", "删除参数", MessageBoxButtons.YesNo);
            if (dr == DialogResult.Yes)
            {
                curParamCfgItem = curLv.SelectedItems[0].Tag as ParamCfgItem;
                curLv.Items.Remove(curLv.SelectedItems[0]);

                //获取当前所在的GroupControl的序号
                string s = curFocusGc.Text.Substring(0, 2).Trim();
                int paramNum = int.Parse(s);

                //通过序号找到对应ListView绑定的对象进行删除
                paramParents[paramNum - 1].children.Remove(curParamCfgItem);
                ParamCfgItem.saveChangeToFile(paramParents[0].fileName, paramParents[0]);

                txtClear();

                GroupControl curGc = curLv.Parent as GroupControl;//当前GroupControl
                SplitGroupPanel curSgPanel = curGc.Parent as SplitGroupPanel;//当前SplitContainerControl的Panel1
                SplitContainerControl curScc = curSgPanel.Parent as SplitContainerControl;//当前SplitContainerControl

                curScc.Panel2.Controls.Clear();
            }
        }

        private void txtParamSearch_TextChanged(object sender, EventArgs e)
        {
            SplitContainerControl publicScc = splitContainerControl1;
            GroupControl publicGc = null;
            ListView publicLv = null;
            List<ListView> lvList = new List<ListView>();

            //遍历到当前显示的最后一级子目录
            while(publicScc.Panel2.Controls.Count > 0)
            {
                publicScc = publicScc.Panel2.Controls[0] as SplitContainerControl;
                publicGc = publicScc.Panel1.Controls[0] as GroupControl;
                publicLv = publicGc.Controls[0] as ListView;
                lvList.Add(publicLv);
            }

            foreach (ListView lv in lvList)
            {
                foreach (ListViewItem item in lv.Items)
                {
                    if (txtParamSearch.Text.Trim().Equals(""))
                    {
                        item.BackColor = Color.White;
                        continue;
                    }
                    string inStr = txtParamSearch.Text.Trim().ToUpper();
                    if (item.Text.ToUpper().IndexOf(inStr) != -1)
                    {
                        item.BackColor = Color.LightBlue;
                    }
                    else
                    {
                        item.BackColor = Color.White;
                    }
                }
            }
        }

    }
}
