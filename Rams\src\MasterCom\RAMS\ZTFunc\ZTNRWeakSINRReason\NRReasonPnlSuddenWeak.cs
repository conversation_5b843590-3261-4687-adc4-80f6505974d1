﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRReasonPnlSuddenWeak : NRReasonPanelBase
    {
        public NRReasonPnlSuddenWeak()
        {
            InitializeComponent();
        }

        public override void AttachReason(NRReasonBase reason)
        {
            base.AttachReason(reason);
            numTimeLimit.ValueChanged -= timeLimit_ValueChanged;
            numTimeLimit.Value = (decimal)NRWeakSINRReason.suddenWeakTime;
            numTimeLimit.ValueChanged += timeLimit_ValueChanged;
            numSinrAvg.ValueChanged -= numSinrAvg_ValueChanged;
            numSinrAvg.Value = (decimal)NRWeakSINRReason.suddenWeakAvg;
            numSinrAvg.ValueChanged += numSinrAvg_ValueChanged;
        }

        void timeLimit_ValueChanged(object sender, EventArgs e)
        {
            NRWeakSINRReason.suddenWeakTime = (int)numTimeLimit.Value;
        }
        void numSinrAvg_ValueChanged(object sender, EventArgs e)
        {
            NRWeakSINRReason.suddenWeakAvg = (float)numSinrAvg.Value;
        }
    }
}
