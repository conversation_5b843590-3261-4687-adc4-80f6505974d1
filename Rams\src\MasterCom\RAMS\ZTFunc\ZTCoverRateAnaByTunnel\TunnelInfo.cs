﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class TunnelInfo : TunnelBaseInfo
    {
        public TunnelInfo()
        { 
        }
        public TunnelInfo(TunnelBaseInfo baseInfo)
        {
            if (baseInfo == null)
            {
                return;
            }
            this.RoadName = baseInfo.RoadName;
            this.Direction = baseInfo.Direction;
            this.Date = baseInfo.Date;
            this.DateDesc = baseInfo.DateDesc;
            this.TunnelName = baseInfo.TunnelName;
            this.TestTime = baseInfo.TestTime;
            this.LogName = baseInfo.LogName;
            this.BeginTime = baseInfo.BeginTime;
            this.EndTime = baseInfo.EndTime;
        }
        public int SN { get; set; }

        public List<string> FileNameList { get; set; } = new List<string>();
        public string FileNames
        {
            get
            {
                if (FileNameList != null && FileNameList.Count > 0)
                {
                    StringBuilder strb = new StringBuilder();
                    foreach (string str in FileNameList)
                    {
                        strb.Append(str + ",");
                    }
                    if (strb.Length > 1)
                    {
                        return strb.Remove(strb.Length - 1, 1).ToString();
                    }
                }
                return "";
            }
        }
        public int GsmTpCountSum { get; set; }
        public int GsmTpCountValid { get; set; }
        public double? GsmCoverRate
        {
            get
            {
                if (GsmTpCountSum > 0)
                {
                    return Math.Round((double)(100 * GsmTpCountValid) / GsmTpCountSum, 2);
                }
                return null;
            }
        }
        public int LteTpCountSum { get; set; }
        public int LteTpCountValid { get; set; }
        public double? LteCoverRate
        {
            get
            {
                if (LteTpCountSum > 0)
                {
                    return Math.Round((double)(100 * LteTpCountValid) / LteTpCountSum, 2);
                }
                return null;
            }
        }

    }
    public class TunnelBaseInfo : IComparable<TunnelBaseInfo>
    {
        public string RoadName { get; set; }
        public string Direction { get; set; }
        public string Date { get; set; }
        public string DateDesc { get; set; }
        public string TunnelName { get; set; }

        public string TestTime { get; set; }
        public string LogName { get; set; }

        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }

        public bool SetTestTimeToDateTime()
        {
            if (string.IsNullOrEmpty(this.Date) || string.IsNullOrEmpty(this.TestTime))
            {
                return false;
            }
            this.Date = this.Date.Replace("号","日").Trim();
            string[] strArray = this.TestTime.Split('-');
            if (strArray.Length != 2)
            {
                return false;
            }

            string strBegin = Date + " " + strArray[0];
            string strEnd = Date + " " + strArray[1];
            DateTime beginTime;
            DateTime endTime;
            if (DateTime.TryParse(strBegin, out beginTime) && DateTime.TryParse(strEnd, out endTime))
            {
                this.BeginTime = beginTime;
                this.EndTime = endTime;
                this.DateDesc = beginTime.ToString("yyyy-MM-dd"); 
                return true;
            }
            return false;
        }


        #region IComparable<TunnelBaseInfo> 成员
        public int CompareTo(TunnelBaseInfo other)
        {
            return this.BeginTime.CompareTo(other.BeginTime);
        }

        #endregion
    }
}
