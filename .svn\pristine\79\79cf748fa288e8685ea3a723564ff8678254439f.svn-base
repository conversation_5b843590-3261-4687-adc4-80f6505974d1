﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FartherCoverSettingDlg : Form
    {
        private FartherCoverCondition cond = null;

        public FartherCoverSettingDlg(FartherCoverCondition condition)
        {
            InitializeComponent();
            this.cond = condition;
            if (this.cond != null)
            {
                fillCondition();
            }
        }

        private void fillCondition()
        {
            spinEditRSRP.Value = cond.RsrpThreshold;
            spinEditSampleNum.Value = cond.SampleNum;
            spinEditDistanceMin.Value = cond.DistanceMin;
            spinEditDistanceMax.Value = cond.DistanceMax;
            checkBoxNBCell.Checked = cond.NBCellChecked;
        }

        public FartherCoverCondition GetCondition()
        {
            if (cond == null)
            {
                cond = new FartherCoverCondition();
            }
            cond.ResetValue((int)spinEditRSRP.Value, (int)spinEditSampleNum.Value, (int)spinEditDistanceMin.Value,
                (int)spinEditDistanceMax.Value, checkBoxNBCell.Checked);
            return cond;
        }

        private void simpleBtnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleBtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }

    public class FartherCoverCondition
    {
        public int RsrpThreshold { get; set; } = -110;
        public int SampleNum { get; set; } = 15;
        public int DistanceMin { get; set; } = 1500;
        public int DistanceMax { get; set; } = 6000;
        public bool NBCellChecked { get; set; } = true;

        public void ResetValue(int rsrp, int sampleNum, int distanceMin, int distanceMax, bool nbcellChecked)
        {
            this.RsrpThreshold = rsrp;
            this.SampleNum = sampleNum;
            this.DistanceMin = distanceMin;
            this.DistanceMax = distanceMax;
            this.NBCellChecked = nbcellChecked;
        }
    }
}
