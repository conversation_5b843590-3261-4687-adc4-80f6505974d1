﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public delegate bool CallBackValidGrid(ScanGridAnaGridInfo grid);
    public class ScanGridAnaGridBlock
    {
        private readonly List<ScanGridAnaGridInfo> gridList;
        private readonly CallBackValidGrid isValidGrid;
        private readonly Dictionary<int, List<ScanGridAnaGridInfo>> idGridDic;
        private int[] parent;

        public ScanGridAnaGridBlock(List<ScanGridAnaGridInfo> gridList, CallBackValidGrid isValidGrid)
        {
            this.gridList = new List<ScanGridAnaGridInfo>(gridList);
            this.isValidGrid = isValidGrid;
            this.idGridDic = new Dictionary<int, List<ScanGridAnaGridInfo>>();
            Do();
        }

        public List<ScanGridAnaGridInfo[]> GetResult(int validBlockCount)
        {
            List<ScanGridAnaGridInfo[]> retList = new List<ScanGridAnaGridInfo[]>();
            foreach (List<ScanGridAnaGridInfo> lst in idGridDic.Values)
            {
                if (lst.Count >= validBlockCount)
                {
                    retList.Add(lst.ToArray());
                }
            }
            return retList;
        }

        // 没有考虑zone
        private bool IsAdjacent(ScanGridAnaGridInfo x, ScanGridAnaGridInfo y)
        {
            double dis = MathFuncs.GetDistance(x.CentLng, x.CentLat, y.CentLng, y.CentLat);
            return dis < x.GridSize * 1.5;
        }

        private void Do()
        {
            List<ScanGridAnaGridInfo> filterList = new List<ScanGridAnaGridInfo>();
            foreach (ScanGridAnaGridInfo grid in gridList)
            {
                if (isValidGrid(grid))
                {
                    filterList.Add(grid);
                }
            }

            Make(filterList);
            for (int i = 0; i < filterList.Count - 1; ++i) // Find relation O(N^2)......
            {
                for (int j = i + 1; j < filterList.Count; ++j)
                {
                    if (IsAdjacent(filterList[i], filterList[j]))
                    {
                        Union(i, j);
                    }
                }
            }

            // result
            for (int i = 0; i < parent.Length; ++i)
            {
                int par = Find(i);
                if (!idGridDic.ContainsKey(par))
                {
                    idGridDic.Add(par, new List<ScanGridAnaGridInfo>());
                }
                idGridDic[par].Add(filterList[i]);
            }
        }

        private void Make(List<ScanGridAnaGridInfo> filterList)
        {
            parent = new int[filterList.Count];
            for (int i = 0; i < parent.Length; ++i)
            {
                parent[i] = i;
            }
        }

        private int Find(int x)
        {
            if (x == parent[x])
            {
                return x;
            }
            return parent[x] = Find(parent[x]);
        }

        private void Union(int x, int y)
        {
            int px = Find(x);
            int py = Find(y);
            parent[px] = py;
        }
    }

    public class ScanGridAnaGridBlockStater
    {
        private readonly Dictionary<ScanGridAnaGridType, List<ScanGridAnaGridInfo>> netGridList;
        private readonly Dictionary<ScanGridAnaGridType, ScanGridAnaGridBlock> netGridBlockRxlev;
        private readonly Dictionary<ScanGridAnaGridType, ScanGridAnaGridBlock> netGridBlockCoverage;
        private readonly ScanGridAnaResult result;
        private int consecutiveGridCount = 3;

        public ScanGridAnaGridBlockStater(ScanGridAnaResult result)
        {
            this.result = result;
            netGridList = new Dictionary<ScanGridAnaGridType, List<ScanGridAnaGridInfo>>();
            netGridBlockRxlev = new Dictionary<ScanGridAnaGridType, ScanGridAnaGridBlock>();
            netGridBlockCoverage = new Dictionary<ScanGridAnaGridType, ScanGridAnaGridBlock>();
        }

        public void Stat()
        {
            foreach (ScanGridAnaGridInfo grid in result.GridList)
            {
                if (!netGridList.ContainsKey(grid.GridType))
                {
                    netGridList.Add(grid.GridType, new List<ScanGridAnaGridInfo>());
                }
                netGridList[grid.GridType].Add(grid);
            }

            foreach (ScanGridAnaGridType gridType in netGridList.Keys)
            {
                netGridBlockRxlev.Add(gridType, new ScanGridAnaGridBlock(netGridList[gridType], IsWeakRelev));
                netGridBlockCoverage.Add(gridType, new ScanGridAnaGridBlock(netGridList[gridType], IsHighCoverage));
            }

            if (result.GridList.Count > 0)
            {
                consecutiveGridCount = (int)result.GridList[0].ConsecutiveGridCount;
            }
        }

        public List<ScanGridAnaGridInfo[]> GetResult(ScanGridAnaGridType netType, ScanGridAnaRangeType rangeType)
        {
            switch (rangeType)
            {
                case ScanGridAnaRangeType.HighCoverage:
                    if (netGridBlockCoverage.ContainsKey(netType))
                    {
                        return netGridBlockCoverage[netType].GetResult(consecutiveGridCount);
                    }
                    break;
                case ScanGridAnaRangeType.WeakRxlev:
                    if (netGridBlockRxlev.ContainsKey(netType))
                    {
                        return netGridBlockRxlev[netType].GetResult(consecutiveGridCount);
                    }
                    break;
            }
            return new List<ScanGridAnaGridInfo[]>();
        }

        public List<ScanGridAnaGridBlockLevelOne> GetShowList(ScanGridAnaGridType netType, ScanGridAnaRangeType rangeType)
        {
            List<ScanGridAnaGridInfo[]> gridList = GetResult(netType, rangeType);
            ScanGridAnaGridBlockLevelShower shower = new ScanGridAnaGridBlockLevelShower(gridList);
            return shower.GetShowList();
        }

        private bool IsWeakRelev(ScanGridAnaGridInfo grid)
        {
            return grid.IsWeakRxlev;
        }

        private bool IsHighCoverage(ScanGridAnaGridInfo grid)
        {
            return grid.IsHighCoverage;
        }
    }

    public class ScanGridAnaGridBlockLevelShower
    {
        public ScanGridAnaGridBlockLevelShower(List<ScanGridAnaGridInfo[]> _gridList)
        {
            gridList = _gridList;
        }

        public List<ScanGridAnaGridBlockLevelOne> GetShowList()
        {
            List<ScanGridAnaGridBlockLevelOne> levelOneList = new List<ScanGridAnaGridBlockLevelOne>();
            int index = 0;
            foreach (ScanGridAnaGridInfo[] grids in gridList)
            {
                ScanGridAnaGridBlockLevelOne levelOne = new ScanGridAnaGridBlockLevelOne(grids);
                levelOne.ID = ++index;
                levelOneList.Add(levelOne);
            }
            return levelOneList;
        }

        private readonly List<ScanGridAnaGridInfo[]> gridList;
    }

    public class ScanGridAnaGridBlockLevelOne
    {
        public int ID
        {
            get;
            set;
        }

        public int GridCount
        {
            get;
            private set;
        }

        public double CentLng
        {
            get;
            private set;
        }

        public double CentLat
        {
            get;
            private set;
        }

        private string roadName = null;
        public string RoadName
        {
            get
            {
                if (roadName != null)
                {
                    return roadName;
                }

                Dictionary<string, int> roadDic = new Dictionary<string, int>();
                StringBuilder sb = new StringBuilder();
                foreach (ScanGridAnaGridBlockLevelTwo grid in LevelTwoList)
                {
                    string oneName = GISManager.GetInstance().GetRoadPlaceDesc(grid.CentLng, grid.CentLat);
                    if (string.IsNullOrEmpty(oneName))
                    {
                        continue;
                    }

                    if (!roadDic.ContainsKey(oneName))
                    {
                        roadDic[oneName] = 0;
                        if (sb.Length > 0)
                        {
                            sb.Append("|");
                        }
                        sb.Append(oneName);
                    }
                    ++roadDic[oneName];
                }
                roadName = sb.ToString();
                return roadName;
            }
        }

        public List<ScanGridAnaGridBlockLevelTwo> LevelTwoList
        {
            get;
            private set;
        }

        public ScanGridAnaGridBlockLevelOne(ScanGridAnaGridInfo[] _gridList)
        {
            gridList = new List<ScanGridAnaGridInfo>(_gridList);
            gridList.Sort();

            GridCount = gridList.Count;
            LevelTwoList = new List<ScanGridAnaGridBlockLevelTwo>();
            foreach (ScanGridAnaGridInfo grid in gridList)
            {
                ScanGridAnaGridBlockLevelTwo levelTwo = new ScanGridAnaGridBlockLevelTwo(grid);
                LevelTwoList.Add(levelTwo);
            }

            if (gridList.Count > 0)
            {
                int idx = gridList.Count / 2;
                CentLng = gridList[idx].CentLng;
                CentLat = gridList[idx].CentLat;
            }
        }

        private List<ScanGridAnaGridInfo> gridList { get; set; }
    }

    public class ScanGridAnaGridBlockLevelTwo
    {
        public ScanGridAnaGridInfo Grid
        {
            get { return grid; }
        }

        public string MgrsString
        {
            get { return grid.MGRSGridString; }
        }

        public int CellCount
        {
            get { return grid.CellInfoDic.Count; }
        }

        public int MaxBcch
        {
            get { return grid.MaxBcch; }
        }

        public int MaxBsic
        {
            get { return grid.MaxBsic; }
        }

        public int RelLevel
        {
            get { return grid.RelLevel; }
        }

        public int AbsLevel
        {
            get { return grid.AbsLevel; }
        }

        public int RelAndAbsLevel
        {
            get { return grid.RelAndAbsLevel; }
        }

        public double MaxRxlev
        {
            get { return grid.MaxRxlev; }
        }

        public int TestPointCount
        {
            get { return grid.TestPointCount; }
        }

        public double CentLng
        {
            get { return grid.CentLng; }
        }

        public double CentLat
        {
            get { return grid.CentLat; }
        }

        public List<ScanGridAnaGridBlockLevelThree> LevelThreeList
        {
            get;
            private set;
        }

        public ScanGridAnaGridBlockLevelTwo(ScanGridAnaGridInfo _grid)
        {
            grid = _grid;
            LevelThreeList = new List<ScanGridAnaGridBlockLevelThree>();
            foreach (ScanGridAnaCellInfo cellInfo in grid.CellInfoDic.Values)
            {
                ScanGridAnaGridBlockLevelThree levelThree = new ScanGridAnaGridBlockLevelThree(cellInfo);
                LevelThreeList.Add(levelThree);
            }
        }

        private readonly ScanGridAnaGridInfo grid;
    }

    public class ScanGridAnaGridBlockLevelThree
    {
        public string CellName
        {
            get { return cellInfo.CellName; }
        }

        public int Bcch
        {
            get { return cellInfo.Bcch; }
        }

        public int Bsic
        {
            get { return cellInfo.Bsic; }
        }

        public double Longitude
        {
            get { return cellInfo.Cell.Longitude; }
        }

        public double Latitude
        {
            get { return cellInfo.Cell.Latitude; }
        }

        public int TestPointCount
        {
            get { return cellInfo.TestPointCount; }
        }

        public ScanGridAnaGridBlockLevelThree(ScanGridAnaCellInfo cellInfo)
        {
            this.cellInfo = cellInfo;
        }
        private readonly ScanGridAnaCellInfo cellInfo;
    }
}
