﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NoCoverRoadSettingDlg_NR : BaseDialog
    {
        public NoCoverRoadSettingDlg_NR()
        {
            InitializeComponent();
        }

        public void SetCondition(NoCoverRoadCondition_NR condition)
        {
            if (condition == null)
            {
                return;
            }
            numRSRP.Value = (decimal)condition.MaxRsrp;
            numMinNoCoverPercent.Value = (decimal)condition.MinNoCoverPercent;
            numMaxTPDistance.Value = (decimal)condition.MaxTpDistance;
            chkMinDistance.Checked = condition.CheckMinDistance;
            numMinDistance.Value = (decimal)condition.MinRoadDistance;
            chkMinDuration.Checked = condition.CheckMinDuration;
            numMinDuration.Value = (decimal)condition.MinRoadDuration;
            chkFilterMultiVoice.Checked = condition.CheckFilterMultiVoice;
        }

        public NoCoverRoadCondition_NR GetCondition()
        {
            NoCoverRoadCondition_NR condition = new NoCoverRoadCondition_NR();
            condition.MaxRsrp = (float)numRSRP.Value;
            condition.MinNoCoverPercent = (double)numMinNoCoverPercent.Value;
            condition.MaxTpDistance = (double)numMaxTPDistance.Value;
            condition.CheckMinDistance = chkMinDistance.Checked;
            condition.MinRoadDistance = (double)numMinDistance.Value;
            condition.CheckMinDuration = chkMinDuration.Checked;
            condition.MinRoadDuration = (double)numMinDuration.Value;
            condition.CheckFilterMultiVoice = chkFilterMultiVoice.Checked;

            return condition;
        }

        private void chkMinDistance_CheckedChanged(object sender, EventArgs e)
        {
            numMinDistance.Enabled = chkMinDistance.Checked;
        }

        private void chkMinDuration_CheckedChanged(object sender, EventArgs e)
        {
            numMinDuration.Enabled = chkMinDuration.Checked;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (!chkMinDistance.Checked && !chkMinDuration.Checked)
            {
                MessageBox.Show(this, "请至少选择“持续距离”和“持续时长”中的一项！");
            }
            else
            {
                this.DialogResult = DialogResult.OK;
            }
        }
    }
}
