﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class SZKPIManager
    {
        private static SZKPIManager manager = null;
        public static SZKPIManager GetInstance()
        {
            if (manager == null)
            {
                manager = new SZKPIManager();
            }
            return manager;
        }
        private SZKPIManager()
        {

        }

        readonly Dictionary<int, Dictionary<int, Dictionary<int, List<SZKPISetting>>>> szKPISettingDic = new Dictionary<int, Dictionary<int, Dictionary<int, List<SZKPISetting>>>>();
        public void InitKPISetting(List<SZKPISetting> kpiSettingList)
        {
            szKPISettingDic.Clear();
            foreach (SZKPISetting kpiSetting in kpiSettingList)
            {
                if (!szKPISettingDic.ContainsKey(kpiSetting.projectType))
                {
                    Dictionary<int, Dictionary<int, List<SZKPISetting>>> carrierKPIDicTmp = new Dictionary<int,Dictionary<int,List<SZKPISetting>>>();
                    szKPISettingDic[kpiSetting.projectType] = carrierKPIDicTmp;
                }
                Dictionary<int, Dictionary<int, List<SZKPISetting>>> carrierKPIDic = szKPISettingDic[kpiSetting.projectType];
                if (!carrierKPIDic.ContainsKey(kpiSetting.carrierID))
                {
                    Dictionary<int, List<SZKPISetting>> serviceKPIDicTmp = new Dictionary<int, List<SZKPISetting>>();
                    carrierKPIDic[kpiSetting.carrierID] = serviceKPIDicTmp;
                }
                Dictionary<int, List<SZKPISetting>> serviceKPIDic = carrierKPIDic[kpiSetting.carrierID];
                if (!serviceKPIDic.ContainsKey(kpiSetting.serviceType))
                {
                    List<SZKPISetting> kpiListTmp = new List<SZKPISetting>();
                    serviceKPIDic[kpiSetting.serviceType] = kpiListTmp;
                }
                List<SZKPISetting> kpiList = serviceKPIDic[kpiSetting.serviceType];
                if (!kpiList.Contains(kpiSetting))
                {
                    kpiList.Add(kpiSetting);
                }
            }
            foreach (int projectType in szKPISettingDic.Keys)
            {
                foreach (int carrierID in szKPISettingDic[projectType].Keys)
                {
                    foreach (int serviceType in szKPISettingDic[projectType][carrierID].Keys)
                    {
                        szKPISettingDic[projectType][carrierID][serviceType].Sort(SZKPISetting.GetSortBySN());
                    }
                }
            }
        }

        public static string GetBatchString(int year, int batch)
        {
            int month = batch / 2;
            if (batch % 2 == 1)
            {
                month++;
            }
            string sMonth = month.ToString();
            if (sMonth.Length == 1)
            {
                sMonth = "0" + sMonth;
            }
            int curBatch = batch % 2 + 1;
            return year + "年" + sMonth + "月" + curBatch + "轮";
        }
    }

    public class SZKPI
    {
        protected virtual string TimeString_Day
        {
            get { return ""; }
        }
        protected virtual string TimeString_Month
        {
            get { return ""; }
        }
        protected virtual string BatchString
        {
            get { return ""; }
        }
        protected int projectType;
        protected int areaType;
        protected int areaID;
        protected string areaName;
        public string AreaName
        {
            get { return areaName; }
        }
        public List<float> kpiList { get; set; } = new List<float>();
    }

    public class SZKPIDay : SZKPI
    {
        protected int time;
        public int Time
        {
            get { return time; }
        }
        protected override string TimeString_Day
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(time * 1000L).ToString("yyyy年MM月dd日"); }
        }

        public void FillFrom(Content con)
        {
            areaName = con.GetParamString();
            time = con.GetParamInt();
            projectType = con.GetParamInt();
            areaType = con.GetParamInt();
            areaID = con.GetParamInt();
            for (int i = 0; i < 50; i++)
            {
                kpiList[i] = con.GetParamFloat();
            }
        }
    }

    public class SZKPIMonth : SZKPIDay
    {
        protected override string TimeString_Month
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(time * 1000L).ToString("yyyy年MM月"); }
        }
    }

    public class SZKPIBatch : SZKPI
    {
        private int year;
        public int Year
        {
            get { return year; }
        }
        private int batch;
        public int Batch
        {
            get { return batch; }
        }
        protected override string BatchString
        {
            get 
            {
                return SZKPIManager.GetBatchString(year, batch); 
            }
        }

        public void FillFrom(Content con)
        {
            areaName = con.GetParamString();
            year = con.GetParamInt();
            batch = con.GetParamInt();
            projectType = con.GetParamInt();
            areaType = con.GetParamInt();
            areaID = con.GetParamInt();
            for (int i = 0; i < 50; i++)
            {
                kpiList[i] = con.GetParamFloat();
            }
        }
    }

    public enum SZKPIMergeType
    {
        SUM,
        AVERAGE,
        MIN,
        MAX
    }

    public class SZKPISetting
    {
        public int sn { get; set; }
        public int projectType { get; set; }
        public int carrierID { get; set; }
        public int serviceType { get; set; }
        public string targetName { get; set; } = "";
        public string formula { get; set; } = "";
        public SZKPIMergeType mergeType { get; set; } = SZKPIMergeType.SUM;
        public string MergeTypeString
        {
            get
            {
                switch (mergeType)
                {
                    case SZKPIMergeType.SUM:
                        {
                            return "求和";
                        }
                    case SZKPIMergeType.AVERAGE:
                        {
                            return "平均值";
                        }
                    case SZKPIMergeType.MAX:
                        {
                            return "最大值";
                        }
                    case SZKPIMergeType.MIN:
                        {
                            return "最小值";
                        }
                    default:
                        {
                            return "求和";
                        }
                }
            }
        }
        public string checkMethod { get; set; } = "";
        public float checkValue { get; set; } = 0;

        public static SZKPISetting FillFrom(Content con)
        {
            SZKPISetting kpiSetting = new SZKPISetting();
            kpiSetting.sn = con.GetParamInt();
            kpiSetting.projectType = con.GetParamInt();
            kpiSetting.carrierID = con.GetParamInt();
            kpiSetting.serviceType = con.GetParamInt();
            kpiSetting.targetName = con.GetParamString();
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < 8; i++)
            {
                sb.Append(con.GetParamString());
            }
            kpiSetting.formula = sb.ToString();
            kpiSetting.checkMethod = con.GetParamString();
            kpiSetting.checkValue = con.GetParamFloat();
            kpiSetting.mergeType = (SZKPIMergeType)con.GetParamInt();
            return kpiSetting;
        }

        /// <summary>
        /// 是否未达标
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public bool BelowStandard(double value)
        {
            switch (checkMethod)
            {
                case "":
                    return false;
                case "大于":
                    if (value > checkValue)
                    {
                        return false;
                    }
                    return true;
                case "小于":
                    if (value < checkValue)
                    {
                        return false;
                    }
                    return true;
                case "等于":
                    if (value == checkValue)
                    {
                        return false;
                    }
                    return true;
                case "大于等于":
                    if (value >= checkValue)
                    {
                        return false;
                    }
                    return true;
                case "小于等于":
                    if (value <= checkValue)
                    {
                        return false;
                    }
                    return true;
                default:
                    return true;
            }
        }

        public string BelowStandardFormula
        {
            get
            {
                if (checkMethod == "")
                {
                    return "";
                }
                else if (checkMethod == "大于")
                {
                    return "X<=" + checkValue;
                }
                else if (checkMethod == "小于")
                {
                    return "X>=" + checkValue;
                }
                else if (checkMethod == "等于")
                {
                    return "X!=" + checkValue;
                }
                else if (checkMethod == "大于等于")
                {
                    return "X<" + checkValue;
                }
                else if (checkMethod == "小于等于")
                {
                    return "X>" + checkValue;
                }
                return "";
            }
        }

        public bool IsSettinged()
        {
            if (!string.IsNullOrEmpty(targetName) && !string.IsNullOrEmpty(formula) && mergeType >= 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public static IComparer<SZKPISetting> GetSortBySN()
        {
            if (sortBySN == null)
            {
                sortBySN = new SortBySN();
            }
            return sortBySN;
        }

        private static IComparer<SZKPISetting> sortBySN;

        private class SortBySN : IComparer<SZKPISetting>
        {
            public int Compare(SZKPISetting x, SZKPISetting y)
            {
                return x.sn - y.sn;
            }
        }
    }
}
