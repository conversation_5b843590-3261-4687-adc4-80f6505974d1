﻿namespace MasterCom.RAMS.Func
{
    partial class CellCloudPictureCommonSetting
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.btnColorZone = new System.Windows.Forms.Button();
            this.numMinWeight = new System.Windows.Forms.NumericUpDown();
            this.numMaxWeight = new System.Windows.Forms.NumericUpDown();
            this.numMajorRate = new System.Windows.Forms.NumericUpDown();
            this.numMinorRate = new System.Windows.Forms.NumericUpDown();
            this.numOffsetRate = new System.Windows.Forms.NumericUpDown();
            this.label6 = new System.Windows.Forms.Label();
            this.numGradientRate = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.numAlpha = new System.Windows.Forms.NumericUpDown();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            ((System.ComponentModel.ISupportInitialize)(this.numMinWeight)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxWeight)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMajorRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinorRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOffsetRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numGradientRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAlpha)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(15, 12);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(89, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "指标最小值门限";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(219, 12);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(89, 12);
            this.label2.TabIndex = 1;
            this.label2.Text = "指标最大值门限";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(37, 59);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 2;
            this.label3.Text = "短半轴占比";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(37, 90);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 3;
            this.label4.Text = "长半轴占比";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(231, 59);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(77, 12);
            this.label5.TabIndex = 4;
            this.label5.Text = "小区中心后移";
            // 
            // btnColorZone
            // 
            this.btnColorZone.Location = new System.Drawing.Point(322, 169);
            this.btnColorZone.Name = "btnColorZone";
            this.btnColorZone.Size = new System.Drawing.Size(75, 23);
            this.btnColorZone.TabIndex = 5;
            this.btnColorZone.Text = "色带设置";
            this.btnColorZone.UseVisualStyleBackColor = true;
            // 
            // numMinWeight
            // 
            this.numMinWeight.DecimalPlaces = 2;
            this.numMinWeight.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numMinWeight.Location = new System.Drawing.Point(108, 9);
            this.numMinWeight.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numMinWeight.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numMinWeight.Name = "numMinWeight";
            this.numMinWeight.Size = new System.Drawing.Size(83, 21);
            this.numMinWeight.TabIndex = 6;
            this.numMinWeight.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // numMaxWeight
            // 
            this.numMaxWeight.DecimalPlaces = 2;
            this.numMaxWeight.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numMaxWeight.Location = new System.Drawing.Point(314, 9);
            this.numMaxWeight.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numMaxWeight.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numMaxWeight.Name = "numMaxWeight";
            this.numMaxWeight.Size = new System.Drawing.Size(83, 21);
            this.numMaxWeight.TabIndex = 7;
            this.numMaxWeight.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMaxWeight.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // numMajorRate
            // 
            this.numMajorRate.DecimalPlaces = 2;
            this.numMajorRate.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numMajorRate.Location = new System.Drawing.Point(108, 87);
            this.numMajorRate.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numMajorRate.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            131072});
            this.numMajorRate.Name = "numMajorRate";
            this.numMajorRate.Size = new System.Drawing.Size(83, 21);
            this.numMajorRate.TabIndex = 8;
            this.numMajorRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMajorRate.Value = new decimal(new int[] {
            7,
            0,
            0,
            65536});
            // 
            // numMinorRate
            // 
            this.numMinorRate.DecimalPlaces = 2;
            this.numMinorRate.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numMinorRate.Location = new System.Drawing.Point(108, 54);
            this.numMinorRate.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numMinorRate.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            131072});
            this.numMinorRate.Name = "numMinorRate";
            this.numMinorRate.Size = new System.Drawing.Size(83, 21);
            this.numMinorRate.TabIndex = 9;
            this.numMinorRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinorRate.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // numOffsetRate
            // 
            this.numOffsetRate.DecimalPlaces = 2;
            this.numOffsetRate.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numOffsetRate.Location = new System.Drawing.Point(314, 54);
            this.numOffsetRate.Minimum = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            this.numOffsetRate.Name = "numOffsetRate";
            this.numOffsetRate.Size = new System.Drawing.Size(83, 21);
            this.numOffsetRate.TabIndex = 10;
            this.numOffsetRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numOffsetRate.Value = new decimal(new int[] {
            25,
            0,
            0,
            131072});
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(231, 90);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(77, 12);
            this.label6.TabIndex = 12;
            this.label6.Text = "渐变中心前移";
            // 
            // numGradientRate
            // 
            this.numGradientRate.DecimalPlaces = 2;
            this.numGradientRate.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numGradientRate.Location = new System.Drawing.Point(314, 87);
            this.numGradientRate.Minimum = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            this.numGradientRate.Name = "numGradientRate";
            this.numGradientRate.Size = new System.Drawing.Size(83, 21);
            this.numGradientRate.TabIndex = 13;
            this.numGradientRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numGradientRate.Value = new decimal(new int[] {
            25,
            0,
            0,
            131072});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(37, 142);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(65, 12);
            this.label7.TabIndex = 14;
            this.label7.Text = "色带透明值";
            // 
            // numAlpha
            // 
            this.numAlpha.Location = new System.Drawing.Point(108, 138);
            this.numAlpha.Maximum = new decimal(new int[] {
            255,
            0,
            0,
            0});
            this.numAlpha.Name = "numAlpha";
            this.numAlpha.Size = new System.Drawing.Size(83, 21);
            this.numAlpha.TabIndex = 15;
            this.numAlpha.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numAlpha.Value = new decimal(new int[] {
            200,
            0,
            0,
            0});
            // 
            // pictureBox1
            // 
            this.pictureBox1.Location = new System.Drawing.Point(39, 169);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(277, 26);
            this.pictureBox1.TabIndex = 16;
            this.pictureBox1.TabStop = false;
            // 
            // CellCloudPictureCommonSetting
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.pictureBox1);
            this.Controls.Add(this.numAlpha);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.numGradientRate);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.numOffsetRate);
            this.Controls.Add(this.numMinorRate);
            this.Controls.Add(this.numMajorRate);
            this.Controls.Add(this.numMaxWeight);
            this.Controls.Add(this.numMinWeight);
            this.Controls.Add(this.btnColorZone);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.Name = "CellCloudPictureCommonSetting";
            this.Size = new System.Drawing.Size(428, 222);
            ((System.ComponentModel.ISupportInitialize)(this.numMinWeight)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxWeight)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMajorRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinorRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOffsetRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numGradientRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAlpha)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Button btnColorZone;
        private System.Windows.Forms.NumericUpDown numMinWeight;
        private System.Windows.Forms.NumericUpDown numMaxWeight;
        private System.Windows.Forms.NumericUpDown numMajorRate;
        private System.Windows.Forms.NumericUpDown numMinorRate;
        private System.Windows.Forms.NumericUpDown numOffsetRate;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown numGradientRate;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown numAlpha;
        private System.Windows.Forms.PictureBox pictureBox1;
    }
}
