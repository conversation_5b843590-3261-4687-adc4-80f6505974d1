﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class DIYBackgroundResultQuery : QueryBase
    {
        public DIYBackgroundResultQuery(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "网络体检结果查询"; }
        }

        public override string IconName
        {
            get { return ""; }
        }
        protected override bool isValidCondition()
        {
            return true;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22001, this.Name);
        }

        protected override void query()
        {
            if (MainModel.BackgroundStarted)
            {
                return;
            }
            if (MainModel.IsBackground)
            {
                MainModel.SystemConfigInfo.isBackground = false;
                MainModel.SystemConfigInfo.Save();
            }
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询网络体检结果...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                MainModel.ClearDTData();
                WaitBox.Show("正在查询网络体检结果...", queryInThread);
                fireShowForm();
                MainModel.FireDTDataChanged(this);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void queryInThread()
        {
            try
            {
                MainModel.QueryFromBackground = true;
                backgroundResultDic.Clear();
                BackgroundFuncConfigManager.GetInstance();
                BackgroundFuncManager funcManager = BackgroundFuncManager.GetInstance();
                int iLoop = 0;
                int totalCount = funcManager.BackgroundQueryList.Count;
                foreach (BackgroundQueryBase queryFunc in funcManager.BackgroundQueryList)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    WaitBox.Text = "正在查询网络体检结果(" + (++iLoop) + "/" + totalCount + ")...";
                    if (!queryFunc.BackgroundStat)
                    {
                        continue;
                    }
                    queryFunc.SetQueryCondition(Condition);
                    queryFunc.Query();
                    if (!backgroundResultDic.ContainsKey(queryFunc.FuncType))
                    {
                        Dictionary<BackgroundSubFuncType, Dictionary<string, List<BackgroundResult>>> resultDic =
                            new Dictionary<BackgroundSubFuncType, Dictionary<string, List<BackgroundResult>>>();
                        backgroundResultDic[queryFunc.FuncType] = resultDic;
                    }
                    if (!backgroundResultDic[queryFunc.FuncType].ContainsKey(queryFunc.SubFuncType))
                    {
                        Dictionary<string, List<BackgroundResult>> resultDic = new Dictionary<string, List<BackgroundResult>>();
                        backgroundResultDic[queryFunc.FuncType][queryFunc.SubFuncType] = resultDic;
                    }
                    if (!backgroundResultDic[queryFunc.FuncType][queryFunc.SubFuncType].ContainsKey(queryFunc.Name))
                    {
                        List<BackgroundResult> resultList = new List<BackgroundResult>();
                        backgroundResultDic[queryFunc.FuncType][queryFunc.SubFuncType][queryFunc.Name] = resultList;
                    }
                    foreach (BackgroundResult bgResult in queryFunc.BackgroundResultList)
                    {
                        bgResult.SN = backgroundResultDic[queryFunc.FuncType][queryFunc.SubFuncType][queryFunc.Name].Count + 1;
                        backgroundResultDic[queryFunc.FuncType][queryFunc.SubFuncType][queryFunc.Name].Add(bgResult);
                    }
                    WaitBox.ProgressPercent = (int)(100.0 * iLoop / totalCount);
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                MainModel.QueryFromBackground = false;
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        private void fireShowForm()
        {
            if (backgroundResultDic.Count == 0)
            {
                XtraMessageBox.Show("没有符合条件的信息！");
                return;
            }
            object obj = MainModel.GetObjectFromBlackboard(typeof(BackgroundResultForm).FullName);
            BackgroundResultForm bgResultForm = obj == null ? null : obj as BackgroundResultForm;
            if (bgResultForm == null || bgResultForm.IsDisposed)
            {
                bgResultForm = new BackgroundResultForm(MainModel);
            }
            bgResultForm.FillData(backgroundResultDic);
            if (!bgResultForm.Visible)
            {
                bgResultForm.Show(MainModel.MainForm);
            }
            MainModel.MainForm.GetMapForm().FireAddBackgroundResultLayer();
        }

        private readonly Dictionary<BackgroundFuncType, Dictionary<BackgroundSubFuncType, Dictionary<string, List<BackgroundResult>>>> backgroundResultDic =
            new Dictionary<BackgroundFuncType, Dictionary<BackgroundSubFuncType, Dictionary<string, List<BackgroundResult>>>>();
    }
}
