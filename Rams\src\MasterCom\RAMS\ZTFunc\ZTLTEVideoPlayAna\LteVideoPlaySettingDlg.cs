﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteVideoPlaySettingDlg : BaseDialog
    {
        public LteVideoPlaySettingDlg(VideoPlayCondion_LTE condition)
        {
            InitializeComponent();
            setCondition(condition);
        }
        private void setCondition(VideoPlayCondion_LTE condition)
        {
            numPreLoad.Value = condition.PreLoadTime;
            numPreRebuffer.Value = condition.PreRebufferTime;
        }
        public VideoPlayCondion_LTE GetCondition()
        {
            VideoPlayCondion_LTE condition = new VideoPlayCondion_LTE();
            condition.PreRebufferTime = (int)numPreRebuffer.Value;
            condition.PreLoadTime = (int)numPreLoad.Value;
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }
    }
}
