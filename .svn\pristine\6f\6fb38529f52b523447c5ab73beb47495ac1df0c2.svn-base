﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTRoadGridArchiveDlg : BaseDialog
    {
        ZTRoadGridArchiveCondition settingCondition;
        //道路名,路段信息
        Dictionary<string, int> roadInfoDic;
        //粒度,数据标识合集
        Dictionary<string, List<string>> dataVerifyDic;

        public ZTRoadGridArchiveDlg()
        {
            InitializeComponent();
        }

        public void Init(ZTRoadGridArchiveCondition settingCondition, Dictionary<string, int> roadInfoDic, Dictionary<string, List<string>> dataVerifyDic)
        {
            this.roadInfoDic = roadInfoDic;
            this.dataVerifyDic = dataVerifyDic;
            this.settingCondition = settingCondition;

            InitRoad(settingCondition, roadInfoDic);

            InitRound(settingCondition, dataVerifyDic);

            if (this.settingCondition.TypeContiodion == ZTRoadGridArchiveCondition.Type.ByRound)
            {
                radioRound.Checked = true;
            }
            else
            {
                radioPeriod.Checked = true;
                if (this.settingCondition.PeriodTypeContiodion == ZTRoadGridArchiveCondition.PeriodType.ByDay)
                {
                    radioPeriodType.SelectedIndex = 0;
                }
                else
                {
                    radioPeriodType.SelectedIndex = 1;
                }
            }

            dPSTime.Value = this.settingCondition.StartTimeByDay;
            dPETime.Value = this.settingCondition.EndTimeByDay;
            dPSCompTime.Value = this.settingCondition.StartCompTimeByDay;
            dPECompTime.Value = this.settingCondition.EndCompTimeByDay;

            dPSMonth.Value = this.settingCondition.StartTimeByMonth;
            dPEMonth.Value = this.settingCondition.EndTimeByMonth;
            dPSCompMonth.Value = this.settingCondition.StartCompTimeByMonth;
            dPECompMonth.Value = this.settingCondition.EndCompTimeByMonth;
        }

        private void InitRoad(ZTRoadGridArchiveCondition settingCondition, Dictionary<string, int> roadInfoDic)
        {
            //添加道路
            chkCmbRoad.Properties.Items.Clear();
            StringBuilder roadStr = new StringBuilder();
            foreach (string roadName in roadInfoDic.Keys)
            {
                chkCmbRoad.Properties.Items.Add(roadName);
                if (settingCondition.RoadConditionStr.Contains(roadName))
                {
                    roadStr.Append(roadName + ",");
                }
            }
            chkCmbRoad.Text = roadStr.ToString().TrimEnd(',');
        }

        private void InitRound(ZTRoadGridArchiveCondition settingCondition, Dictionary<string, List<string>> dataVerifyDic)
        {
            //添加轮次
            chkCmbCurRound.Properties.Items.Clear();
            chkCmbHisRound.Properties.Items.Clear();
            StringBuilder curRoundStr = new StringBuilder();
            StringBuilder hisRoundStr = new StringBuilder();
            if (dataVerifyDic.ContainsKey("round"))
            {
                List<string> roundList = dataVerifyDic["round"];
                foreach (var round in roundList)
                {
                    chkCmbCurRound.Properties.Items.Add(round);
                    chkCmbHisRound.Properties.Items.Add(round);
                    if (settingCondition.CurRoundConditionStr.Contains(round))
                    {
                        curRoundStr.Append(round + ",");
                    }
                    if (settingCondition.HisRoundConditionStr.Contains(round))
                    {
                        hisRoundStr.Append(round + ",");
                    }
                }
            }
            chkCmbCurRound.Text = curRoundStr.ToString().TrimEnd(',');
            chkCmbHisRound.Text = hisRoundStr.ToString().TrimEnd(',');
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (!judgeCondition())
            {
                return;
            }

            settingCondition.StartTimeByDay = dPSTime.Value;
            settingCondition.EndTimeByDay = dPETime.Value;
            settingCondition.StartCompTimeByDay = dPSCompTime.Value;
            settingCondition.EndCompTimeByDay = dPECompTime.Value;
            
            settingCondition.StartTimeByMonth = dPSMonth.Value;
            settingCondition.EndTimeByMonth = dPEMonth.Value;
            settingCondition.StartCompTimeByMonth = dPSCompMonth.Value;
            settingCondition.EndCompTimeByMonth = dPECompMonth.Value;

            settingCondition.RoadConditionStr = chkCmbRoad.Text;
            settingCondition.CurRoundConditionStr = chkCmbCurRound.Text;
            settingCondition.HisRoundConditionStr = chkCmbHisRound.Text;
           
            settingCondition.SetFinalCondition(roadInfoDic, dataVerifyDic);

            DialogResult = DialogResult.OK;
        }

        private bool judgeCondition()
        {
            if (string.IsNullOrEmpty(chkCmbRoad.Text.Trim()))
            {
                MessageBox.Show("至少选择一条道路进行分析");
                return false;
            }
            if (settingCondition.TypeContiodion == ZTRoadGridArchiveCondition.Type.ByRound)
            {
                return judgeRound();
            }
            else
            {
                return judagePeriod();
            }
        }

        private bool judgeRound()
        {
            if (string.IsNullOrEmpty(chkCmbCurRound.Text.Trim()))
            {
                MessageBox.Show("至少选择一个当前轮次行进行分析");
                return false;
            }
            if (string.IsNullOrEmpty(chkCmbHisRound.Text.Trim()))
            {
                MessageBox.Show("至少选择一个历史轮次进行分析");
                return false;
            }
            return true;
        }

        private bool judagePeriod()
        {
            if (settingCondition.PeriodTypeContiodion == ZTRoadGridArchiveCondition.PeriodType.ByDay)
            {
                if (dPSTime.Value.Date > dPETime.Value.Date || dPSCompTime.Value.Date > dPECompTime.Value.Date)
                {
                    MessageBox.Show("起始时间不能大于结束时间");
                    return false;
                }
            }
            else
            {
                if (dPSMonth.Value.Date > dPEMonth.Value.Date || dPSCompMonth.Value.Date > dPECompMonth.Value.Date)
                {
                    MessageBox.Show("起始时间不能大于结束时间");
                    return false;
                }
            }
            return true;
        }

        private void radioPeriodType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (radioPeriodType.SelectedIndex == 0)
            {
                settingCondition.PeriodTypeContiodion = ZTRoadGridArchiveCondition.PeriodType.ByDay;
                panelDate.Enabled = true;
                panelMonth.Enabled = false;
            }
            else
            {
                settingCondition.PeriodTypeContiodion = ZTRoadGridArchiveCondition.PeriodType.ByMonth;
                panelDate.Enabled = false;
                panelMonth.Enabled = true;
            }
        }

        private void radioRound_CheckedChanged(object sender, EventArgs e)
        {
            if (radioRound.Checked)
            {
                settingCondition.TypeContiodion = ZTRoadGridArchiveCondition.Type.ByRound;
                panelRound.Enabled = true;
                panelPeriod.Enabled = false;
                radioPeriod.Checked = false;
            }
        }

        private void radioPeriod_CheckedChanged(object sender, EventArgs e)
        {
            if (radioPeriod.Checked)
            {
                settingCondition.TypeContiodion = ZTRoadGridArchiveCondition.Type.ByPeriod;
                panelRound.Enabled = false;
                panelPeriod.Enabled = true;
                radioRound.Checked = false;
            }
        }
    }

    public class ZTRoadGridArchiveCondition
    {
        public DateTime StartTimeByDay { get; set; } = DateTime.Now;
        public DateTime EndTimeByDay { get; set; } = DateTime.Now;
        public DateTime StartCompTimeByDay { get; set; } = DateTime.Now;
        public DateTime EndCompTimeByDay { get; set; } = DateTime.Now;

        public DateTime StartTimeByMonth { get; set; } = DateTime.Now;
        public DateTime EndTimeByMonth { get; set; } = DateTime.Now;
        public DateTime StartCompTimeByMonth { get; set; } = DateTime.Now;
        public DateTime EndCompTimeByMonth { get; set; } = DateTime.Now;

        public string RoadConditionStr { get; set; } = "";
        //当前轮次 用于汇总
        public string CurRoundConditionStr { get; set; } = "";
        public string HisRoundConditionStr { get; set; } = "";

        //查询类型
        public Type TypeContiodion { get; set; }
        //时段类型
        public PeriodType PeriodTypeContiodion { get; set; }

        //所选道路ID合集
        public string RoadListStr { get; set; } = "";
        //当前轮次数 用于汇总
        public int CurRoundCount { get; set; }
        //当前轮次
        public List<string> CurRoundListStr { get; set; } = new List<string>();
        //历史轮次
        public List<string> HisRoundListStr { get; set; } = new List<string>();

        //有效的当前日期时段
        public List<string> TimeByDayStrList { get; private set; } = new List<string>();
        public List<string> CompTimeByDayStrList { get; private set; } = new List<string>();
        public List<string> TimeByMonthStrList { get; private set; } = new List<string>();
        public List<string> CompTimeByMonthStrList { get; private set; } = new List<string>();

        public void SetFinalCondition(Dictionary<string, int> roadInfoDic, Dictionary<string, List<string>> dataVerifyDic)
        {
            clearData();

            addRoadList(roadInfoDic);

            addRoundList();

            addValidPeriod(dataVerifyDic);
        }

        private void clearData()
        {
            RoadListStr = "";
            CurRoundCount = 0;
            CurRoundListStr.Clear();
            HisRoundListStr.Clear();
            TimeByDayStrList.Clear();
            CompTimeByDayStrList.Clear();
            TimeByMonthStrList.Clear();
            CompTimeByMonthStrList.Clear();
        }

        private void addRoadList(Dictionary<string, int> roadInfoDic)
        {
            //设置道路信息
            StringBuilder roadStr = new StringBuilder();
            string[] road = RoadConditionStr.Split(',');
            foreach (var name in road)
            {
                roadStr.Append(roadInfoDic[name.Trim()] + ",");
            }
            RoadListStr = roadStr.ToString().TrimEnd(',');
        }

        private void addRoundList()
        {
            //设置当前轮次信息
            string[] curRound = CurRoundConditionStr.Split(',');
            CurRoundCount = curRound.Length;
            foreach (var round in curRound)
            {
                CurRoundListStr.Add(round.Trim());
            }

            //设置历史轮次信息
            string[] hisRound = HisRoundConditionStr.Split(',');
            foreach (var round in hisRound)
            {
                HisRoundListStr.Add(round.Trim());
            }
        }

        private void addValidPeriod(Dictionary<string, List<string>> dataVerifyDic)
        {
            //根据所选时段匹配已有天表或月表,添加时段中包含对应表的标识
            if (TypeContiodion == Type.ByPeriod)
            {
                if (PeriodTypeContiodion == PeriodType.ByDay)
                {
                    TimeByDayStrList.Clear();
                    CompTimeByDayStrList.Clear();

                    if (dataVerifyDic.ContainsKey("dd"))
                    {
                        List<string> ddList = dataVerifyDic["dd"];
                        ddList.Sort();
                        addValidDay(ddList, StartTimeByDay, EndTimeByDay, TimeByDayStrList);
                        addValidDay(ddList, StartCompTimeByDay, EndCompTimeByDay, CompTimeByDayStrList);
                    }
                }
                else
                {
                    TimeByMonthStrList.Clear();
                    CompTimeByMonthStrList.Clear();

                    if (dataVerifyDic.ContainsKey("mm"))
                    {
                        List<string> mmList = dataVerifyDic["mm"];
                        mmList.Sort();
                        addValidMonth(mmList, StartTimeByMonth, EndTimeByMonth, TimeByMonthStrList);
                        addValidMonth(mmList, StartCompTimeByMonth, EndCompTimeByMonth, CompTimeByMonthStrList);
                    }
                }
            }
        }

        private void addValidDay(List<string> ddList, DateTime start, DateTime end, List<string> dayStrList)
        {
            int index = 0;
            for (; start <= end; start = start.AddDays(1))
            {
                //格式要与tb_数据核查_数据日期表的一致
                string curName = start.ToString("yyMMdd");
                addValidTime(ddList, dayStrList, ref index, curName);
                if (index >= ddList.Count)
                {
                    break;
                }
            }
        }

        private void addValidMonth(List<string> mmList, DateTime start, DateTime end, List<string> monthStrList)
        {
            int index = 0;
            for (; start <= end; start = start.AddMonths(1))
            {
                string curName = start.ToString("yyMM");
                addValidTime(mmList, monthStrList, ref index, curName);
                if (index >= mmList.Count)
                {
                    break;
                }
            }
        }

        private void addValidTime(List<string> timeList, List<string> timeStrList, ref int index, string curName)
        {
            for (; index < timeList.Count; index++)
            {
                int comp = curName.CompareTo(timeList[index]);
                if (comp == 0)
                {
                    timeStrList.Add(curName);
                    break;
                }
                else if (comp == -1)
                {
                    break;
                }
            }
        }

        public enum PeriodType
        {
            ByDay,
            ByMonth
        }

        public enum Type
        {
            ByRound,
            ByPeriod
        }
    }
}
