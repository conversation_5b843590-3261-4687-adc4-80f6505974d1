﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTPrivateNetCellQuery : DIYSQLBase
    {
        public List<PrivateNetCell> privateNetCellList { get; set; } = new List<PrivateNetCell>();

        public ZTPrivateNetCellQuery()
        {
            MainDB = true;
        }

        protected override string getSqlTextString()
        {
            return "SELECT [strareaname],[strroadname],[strnettype],[ilac],[ici],[ilongitude],[ilatitude],[strcomment] FROM [tb_cfg_railway_cell]";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[8];
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i] = E_VType.E_String;
            return arr;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            privateNetCellList.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    PrivateNetCell cellInfo = new PrivateNetCell();
                    cellInfo.AreaName = package.Content.GetParamString();
                    cellInfo.RoadName = package.Content.GetParamString();
                    cellInfo.NetType = package.Content.GetParamString();
                    cellInfo.TAC = package.Content.GetParamInt();
                    cellInfo.ECI = package.Content.GetParamInt();
                    int iLongitude = package.Content.GetParamInt();
                    int iLatitude = package.Content.GetParamInt();
                    cellInfo.Longitude = (double)iLongitude / 10000000;
                    cellInfo.Latitude = (double)iLatitude / 10000000;
                    cellInfo.CellName = package.Content.GetParamString();
                    privateNetCellList.Add(cellInfo);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }

  
}
