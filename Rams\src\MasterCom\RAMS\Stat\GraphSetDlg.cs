﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;

namespace MasterCom.RAMS.Stat
{
    public partial class GraphSetDlg : BaseFormStyle
    {
        public GraphSetDlg()
        {
            InitializeComponent();
        }

        internal void FillSelectedCells(List<DataGridViewCell> labelCells, List<DataGridViewCell> valueCells)
        {
            dataGrid.Rows.Clear();
            graphEntity.labels.Clear();
            graphEntity.values.Clear();
            for (int i = 0; i < labelCells.Count; i++)
            {
                DataGridViewCell labCell = labelCells[i];
                DataGridViewCell vCell = valueCells[i];
                if ( i >= dataGrid.Rows.Count )
                {
                    dataGrid.Rows.Add(1);
                }
                dataGrid.Rows[i].Cells[0].Value = labCell.Value.ToString();
                dataGrid.Rows[i].Cells[1].Value = string.Format("(第{0}行,第{1}列)",labCell.RowIndex+1,labCell.ColumnIndex+1);
                dataGrid.Rows[i].Cells[2].Value = vCell.Value.ToString();
                dataGrid.Rows[i].Cells[3].Value = string.Format("(第{0}行,第{1}列)", vCell.RowIndex + 1, vCell.ColumnIndex + 1);
                graphEntity.labels.Add(new CellPosition(labCell.RowIndex,labCell.ColumnIndex));
                graphEntity.values.Add(new CellPosition(vCell.RowIndex, vCell.ColumnIndex));
            }
        }
        public GraphEntity graphEntity { get; set; } = new GraphEntity();
        private void btnOK_Click(object sender, EventArgs e)
        {
            graphEntity.graphTitle = tbxTitle.Text;
            graphEntity.graphXString = tbxXLabel.Text;
            graphEntity.graphYString = tbxVLabel.Text;
            this.DialogResult = DialogResult.OK;
        }

        private void tbxTitle_TextChanged(object sender, EventArgs e)
        {
            fireBtnChange();
            label4.Visible = string.IsNullOrEmpty(this.tbxTitle.Text);
        }

        private void tbxXLabel_TextChanged(object sender, EventArgs e)
        {
            fireBtnChange();
            label5.Visible = string.IsNullOrEmpty(this.tbxXLabel.Text);
        }

        private void tbxVLabel_TextChanged(object sender, EventArgs e)
        {
            fireBtnChange();
            label5.Visible = string.IsNullOrEmpty(this.tbxVLabel.Text);
        }

        private void fireBtnChange()
        {
            btnOK.Enabled = !string.IsNullOrEmpty(this.tbxTitle.Text) && !string.IsNullOrEmpty(this.tbxVLabel.Text)
            && !string.IsNullOrEmpty(this.tbxXLabel.Text);
        }
    }
}
