﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.CQT
{
    public class DIYSQLInsertCQTTestTask : DIYSQLBase
    {
        public DIYSQLInsertCQTTestTask(MainModel mm)
            : base(mm)
        { 
        }
        public DIYSQLInsertCQTTestTask(MainModel mm, List<CQTTestTask> task2Insert):base(mm)
        {
            testTaskList = task2Insert;
        }
        protected List<CQTTestTask> testTaskList = new List<CQTTestTask>();
        string sqlText = "";
        protected override void queryInThread(object o)
        {
            if (testTaskList == null)
            {
                return;
            }
            WaitTextBox.Text = "正在更新操作...";
            WaitTextBox.Show(operaWithWaitBox, o);
        }

        protected void operaWithWaitBox(object o)
        {
            try
            {
                foreach (CQTTestTask task in testTaskList)
                {
                    if (task==null)
                    {
                        continue;
                    }
                    sqlText = makeSqlTextByCQTTestTask(task);
                    base.queryInThread(o);
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitTextBox.Close();
            }
        }

        protected override string getSqlTextString()
        {
            return sqlText;
        }

        protected virtual string makeSqlTextByCQTTestTask(CQTTestTask task)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("INSERT INTO tb_cqt_testtask (taskID,taskName,sTime,eTime,creatorID,serviceID,agentID,careerID) VALUES (");
            sb.Append("'" + task.ID.ToString() + "','" + task.Name + "'," + (int)(JavaDate.GetMilliseconds(task.BeginTime) / 1000) + "," + (int)(JavaDate.GetMilliseconds(task.EndTime) / 1000));
            sb.Append("," + task.CreatorID.ToString() + "," + task.ServiceID.ToString() + "," + task.AgentID + "," + task.CareerID + ")");
            sb.Append(";INSERT INTO tb_cqt_testtask_detail (taskID,pointID,planWorkLoad,comment) VALUES (");
            sb.Append("'" + task.ID.ToString() + "'," + task.CQTPoint.ID + "," + ((int)(task.Target.TotalSeconds)).ToString() + ",'" + task.Comment + "')");
            return sb.ToString();
        }

        public void InsertTestTask(CQTTestTask testTask)
        {
            testTaskList.Add(testTask);
        }

        protected override MasterCom.RAMS.Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            MasterCom.RAMS.Model.Interface.E_VType[] rType = new MasterCom.RAMS.Model.Interface.E_VType[1];
            rType[0] = MasterCom.RAMS.Model.Interface.E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public override string Name
        {
            get { return "插入测试计划"; }
        }
    }
}
