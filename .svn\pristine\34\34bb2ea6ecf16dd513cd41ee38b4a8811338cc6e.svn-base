using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public partial class BTSsLineSettingForm : Form
    {
        private Color lineColor;

        public Color LineColor
        {
            get { return lineColor; }
        }
        private int lineWidth;

        public int LineWidth
        {
            get { return lineWidth; }
        }
        private double distance;

        public double Distance
        {
            get { return distance; }
        }
        private bool drawLine;

        public bool DrawLine
        {
            get { return drawLine; }
        }
        private Color labelColor;
        public Color LabelColor
        {
            get { return labelColor; }
        }
        private double angle;
        public double Angle 
        {
            get { return angle; }
        }
        
        public BTSsLineSettingForm()
        {
            InitializeComponent();
        }

        private void labelLineColor_Click(object sender, EventArgs e)
        {
            ColorDialog dialog = new ColorDialog();
            dialog.Color = labelLineColor.BackColor;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                labelLineColor.BackColor = dialog.Color;
            }
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            lineColor = labelLineColor.BackColor;
            lineWidth = (int)numericUpDownLineWidth.Value;
            distance = (double)numericUpDownDistance.Value;
            drawLine = checkBoxVisible.Checked;
            labelColor = lblColor.BackColor;
            angle = (double)numericUpDownAngle.Value;
            this.DialogResult = DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void labelColor_Click(object sender, EventArgs e)
        {
            ColorDialog dialog = new ColorDialog();
            dialog.Color = lblColor.BackColor;
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                lblColor.BackColor = dialog.Color;
            }
        }

    }
}