using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using BrightIdeasSoftware;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Grid;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTNBCellMissForm : MinCloseForm
    {
        public ZTNBCellMissForm()
            : base()
        {
            InitializeComponent();
            init();

            cmbNBCellStatus.Items.Add("全部");
            cmbNBCellStatus.Items.Add("相同");
            cmbNBCellStatus.Items.Add("数据中有，配置中未找到");
            cmbNBCellStatus.Items.Add("配置有，数据中未找到");
            cmbNBCellStatus.SelectedIndex = 0;

            for (int i = 0; i <= 6; i++)
            {
                comboBoxAngleMin.Items.Add((i * 30).ToString());
            }
            comboBoxAngleMin.SelectedIndex = 0;
            for (int i = 0; i <= 6; i++)
            {
                comboBoxAngleMax.Items.Add((i * 30).ToString());
            }
            comboBoxAngleMax.SelectedIndex = 6;

            cbxInOrgAngle.SelectedIndex = 1;
            cbxInDoorCell.SelectedIndex = 2;
            cbxBandType.SelectedIndex = 0;
            rdbNBigerThenMain.Checked = true;
            rdbNBigerThenMain_CheckedChanged(null, null);
        }

        private List<MissNbCellSubResult> nbList = new List<MissNbCellSubResult>();
        private Dictionary<object, MissNbCellMainItem> nbCellMissInfoDic = new Dictionary<object, MissNbCellMainItem>();
        private Dictionary<MasterCom.RAMS.Net.GridItem, Dictionary<string, Dictionary<int, GSMCellRxLev>>> cellGridDic = new Dictionary<MasterCom.RAMS.Net.GridItem, Dictionary<string, Dictionary<int, GSMCellRxLev>>>();
        int cellRxLevMin = 0;
        int cellRxLevMax = 0;
        int nbCellRxLevMin = 0;
        int nbCellRxLevMax = 0;
        InspectType inspectType = InspectType.GSM;
        int topN = 0;
        int dValueWithTop1 = 0;
        int oldTopN = 0;
        int oldDValueWithTop1 = 0;
        CellBandTypeFilter bandTypeFilter = CellBandTypeFilter.All;
        CellBandTypeFilter oldBandTypeFilter = CellBandTypeFilter.All;
        Color color = Color.LightGreen;

        internal void FillDatas(Dictionary<MasterCom.RAMS.Net.GridItem, Dictionary<string, Dictionary<int, GSMCellRxLev>>> cellGridDicTemp, InspectType inspectType)
        {
            if (nbList != null)
            {
                nbList.Clear();
            }
            this.cellGridDic = cellGridDicTemp;
            if (this.inspectType != inspectType)
            {
                olvColumnCellLac.Text = "LAC";
                olvColumnCellCI.Text = "CI";
                cbxBandType.Items.Clear();
                cbxBandType.Items.Add("全部");
                if (inspectType == InspectType.GSM)
                {
                    cbxBandType.Items.Add("900-900");
                    cbxBandType.Items.Add("900-1800");
                    cbxBandType.Items.Add("1800-1800");
                    cbxBandType.Items.Add("1800-900");
                    cbxBandType.SelectedIndex = 0;

                    olvColumnCellBCCH.Text = "BCCH";
                    olvColumnCellBSIC.Text = "BSIC";
                }
                else if (inspectType == InspectType.T2T)
                {
                    cbxBandType.Items.Add("TD-TD");
                    cbxBandType.SelectedIndex = 0;

                    olvColumnCellBCCH.Text = "FREQ";
                    olvColumnCellBSIC.Text = "CPI";
                }
                else if (inspectType == InspectType.T2G)
                {
                    cbxBandType.Items.Add("TD-2G");
                    cbxBandType.SelectedIndex = 0;

                    olvColumnCellBCCH.Text = "FREQ/BCCH";
                    olvColumnCellBSIC.Text = "CPI/BSIC";
                }
                else if (inspectType == InspectType.W2W)
                {
                    cbxBandType.Items.Add("W-W");
                    cbxBandType.SelectedIndex = 0;

                    olvColumnCellBCCH.Text = "UARFCN";
                    olvColumnCellBSIC.Text = "PSC";
                }
                else if (inspectType == InspectType.LTE2GSM)
                {
                    cbxBandType.Items.Add("LTE-GSM");
                    cbxBandType.SelectedIndex = 0;

                    olvColumnCellLac.Text = "TAC";
                    olvColumnCellCI.Text = "ECI";
                    olvColumnCellBCCH.Text = "EARFCN/BCCH";
                    olvColumnCellBSIC.Text = "PCI/BSIC";
                }
                else if (inspectType == InspectType.LTE2LTE)
                {
                    cbxBandType.Items.Add("LTE-LTE");
                    cbxBandType.SelectedIndex = 0;

                    olvColumnCellLac.Text = "TAC";
                    olvColumnCellCI.Text = "ECI";
                    olvColumnCellBCCH.Text = "EARFCN";
                    olvColumnCellBSIC.Text = "PCI";
                }
                else if (inspectType == InspectType.NR2LTE)
                {
                    cbxBandType.Items.Add("NR-LTE");
                    cbxBandType.SelectedIndex = 0;

                    olvColumnCellLac.Text = "TAC";
                    olvColumnCellCI.Text = "NCI/ECI";
                    olvColumnCellBCCH.Text = "SSBARFCN/EARFCN";
                    olvColumnCellBSIC.Text = "PCI";
                }
                listViewTotal.RebuildColumns();
            }
            this.inspectType = inspectType;
            refreshData();
        }

        private void refreshData()
        {
            oldTopN = topN;
            oldDValueWithTop1 = dValueWithTop1;
            oldBandTypeFilter = bandTypeFilter;
            cellRxLevMin = (int)numCellRxLevMin.Value;
            cellRxLevMax = (int)numCellRxLevMax.Value;
            nbCellRxLevMin = (int)numNBCellRxLevMin.Value;
            nbCellRxLevMax = (int)numNBCellRxLevMax.Value;
            nbList = ScanGridManager.GetNBResult(cellGridDic, cellRxLevMin, cellRxLevMax, nbCellRxLevMin, nbCellRxLevMax, inspectType, topN, dValueWithTop1, bandTypeFilter);
            freshShowListView();
        }

        private void freshShowListView()
        {
            nbCellMissInfoDic.Clear();
            foreach (MissNbCellSubResult nbResult in nbList)
            {
                MissNbCellMainItem info = null;
                if (!nbCellMissInfoDic.TryGetValue(nbResult.MainCell, out info))
                {
                    info = new MissNbCellMainItem(nbResult.MainCell);
                }

                if (true == nbResult.CanShow(cmbNBCellStatus.SelectedItem.ToString(), (int)numGridCountMin.Value, this.numNBDistanceMin.Value.ToString(),
                                             this.numNBDistanceMax.Value.ToString(), comboBoxAngleMin.SelectedItem.ToString(), comboBoxAngleMax.SelectedItem.ToString(),
                                             cbxInOrgAngle.SelectedIndex, cbxInDoorCell.SelectedIndex, (int)numSampleCount.Value))
                {
                    info.MissCellsInfo.Add(nbResult);
                    if (nbResult.NBCellRxLevAvg > info.maxNBRxLevAvg)
                    {
                        info.maxNBRxLevAvg = nbResult.NBCellRxLevAvg;
                    }
                }
                if (info.MissCellsInfo.Count > 0)
                {
                    nbCellMissInfoDic[info.Cell] = info;
                }
            }
            int rxLevDValue = (int)numRxLevDValue.Value;
            foreach (MissNbCellMainItem info in nbCellMissInfoDic.Values)
            {
                double maxRxLev = info.maxNBRxLevAvg;
                for (int i = 0; i < info.MissCellsInfo.Count; i++)
                {
                    if (maxRxLev - info.MissCellsInfo[i].NBCellRxLevAvg > rxLevDValue)
                    {
                        info.MissCellsInfo.RemoveAt(i--);
                    }
                }
            }

            if (listViewTotal.Items.Count > 0)
            {
                listViewTotal.ClearObjects();
            }

            listViewTotal.SetObjects(nbCellMissInfoDic.Values);
        }

        private void init()
        {
            this.olvColumnOrgCell.AspectGetter = delegate(object row)
            {
                if (row is MissNbCellMainItem)
                {
                    MissNbCellMainItem info = row as MissNbCellMainItem;
                    return info.Cell;
                }
                else if (row is MissNbCellSubResult)
                {
                    MissNbCellSubResult nbResult = row as MissNbCellSubResult;
                    return nbResult.MainCell.Name;
                }
                return string.Empty;
            };
            this.olvColumnNBCell.AspectGetter = delegate(object row)
            {
                if (row is MissNbCellMainItem)
                {
                    return ((MissNbCellMainItem)row).MissCellsInfo.Count;
                }
                else if (row is MissNbCellSubResult)
                {
                    MissNbCellSubResult nbResult = row as MissNbCellSubResult;
                    return nbResult.NbCell.Name;
                }
                return string.Empty;
            };
            this.olvColumnNBCellDes.AspectGetter = delegate(object row)
            {
                if (row is MissNbCellSubResult)
                {
                    MissNbCellSubResult nbResult = row as MissNbCellSubResult;
                    return nbResult.result;
                }
                return string.Empty;
            };
            this.olvColumnGridCount.AspectGetter = delegate(object row)
            {
                if (row is MissNbCellSubResult)
                {
                    MissNbCellSubResult nbResult = row as MissNbCellSubResult;
                    return nbResult.gridnum;
                }
                return string.Empty;
            };
            this.olvColumnSampleCount.AspectGetter = delegate(object row)
            {
                if (row is MissNbCellSubResult)
                {
                    MissNbCellSubResult nbResult = row as MissNbCellSubResult;
                    return nbResult.sampleNum;
                }
                return string.Empty;
            };
            this.olvColumnCellDistance.AspectGetter = delegate(object row)
            {
                if (row is MissNbCellSubResult)
                {
                    MissNbCellSubResult nbResult = row as MissNbCellSubResult;
                    return Math.Round(nbResult.distance, 2);
                }
                return string.Empty;
            };
            this.olvColumnAngle.AspectGetter = delegate(object row)
            {
                if (row is MissNbCellSubResult)
                {
                    MissNbCellSubResult nbResult = row as MissNbCellSubResult;
                    return nbResult.Angle;
                }
                return string.Empty;
            };
            this.olvColumnIsOrgAngle.AspectGetter = delegate(object row)
            {
                if (row is MissNbCellSubResult)
                {
                    MissNbCellSubResult nbResult = row as MissNbCellSubResult;
                    return nbResult.isInAngle ? "是" : "否";
                }
                return string.Empty;
            };
            this.olvColumnCellLac.AspectGetter = delegate(object row)
            {
                if (row is MissNbCellMainItem)
                {
                    MissNbCellMainItem info = row as MissNbCellMainItem;
                    if (info.Cell is Cell)
                    {
                        return (info.Cell as Cell).LAC;
                    }
                    else if (info.Cell is TDCell)
                    {
                        return (info.Cell as TDCell).LAC;
                    }
                    else if (info.Cell is WCell)
                    {
                        return (info.Cell as WCell).LAC;
                    }
                    else if (info.Cell is LTECell)
                    {
                        return (info.Cell as LTECell).TAC;
                    }
                    else if (info.Cell is NRCell)
                    {
                        return (info.Cell as NRCell).TAC;
                    }
                    return "-";
                }
                else if (row is MissNbCellSubResult)
                {
                    MissNbCellSubResult nbResult = row as MissNbCellSubResult;
                    if (nbResult.NbCell is Cell)
                    {
                        return (nbResult.NbCell as Cell).LAC;
                    }
                    else if (nbResult.NbCell is TDCell)
                    {
                        return (nbResult.NbCell as TDCell).LAC;
                    }
                    else if (nbResult.NbCell is WCell)
                    {
                        return (nbResult.NbCell as WCell).LAC;
                    }
                    else if (nbResult.NbCell is LTECell)
                    {
                        return (nbResult.NbCell as LTECell).TAC;
                    }
                    else if (nbResult.NbCell is NRCell)
                    {
                        return (nbResult.NbCell as NRCell).TAC;
                    }
                    return "-";
                }
                return string.Empty;
            };
            this.olvColumnCellCI.AspectGetter = delegate(object row)
            {
                if (row is MissNbCellMainItem)
                {
                    MissNbCellMainItem info = row as MissNbCellMainItem;
                    if (info.Cell is Cell)
                    {
                        return (info.Cell as Cell).CI;
                    }
                    else if (info.Cell is TDCell)
                    {
                        return (info.Cell as TDCell).CI;
                    }
                    else if (info.Cell is WCell)
                    {
                        return (info.Cell as WCell).CI;
                    }
                    else if (info.Cell is LTECell)
                    {
                        return (info.Cell as LTECell).ECI;
                    }
                    else if (info.Cell is NRCell)
                    {
                        return (info.Cell as NRCell).NCI;
                    }
                    return "-";
                }
                else if (row is MissNbCellSubResult)
                {
                    MissNbCellSubResult nbResult = row as MissNbCellSubResult;
                    if (nbResult.NbCell is Cell)
                    {
                        return (nbResult.NbCell as Cell).CI;
                    }
                    else if (nbResult.NbCell is TDCell)
                    {
                        return (nbResult.NbCell as TDCell).CI;
                    }
                    else if (nbResult.NbCell is WCell)
                    {
                        return (nbResult.NbCell as WCell).CI;
                    }
                    else if (nbResult.NbCell is LTECell)
                    {
                        return (nbResult.NbCell as LTECell).ECI;
                    }
                    else if (nbResult.NbCell is NRCell)
                    {
                        return (nbResult.NbCell as NRCell).NCI;
                    }
                    return "-";
                }
                return string.Empty;
            };
            this.olvColumnCellBCCH.AspectGetter = delegate(object row)
            {
                if (row is MissNbCellMainItem)
                {
                    MissNbCellMainItem info = row as MissNbCellMainItem;
                    if (info.Cell is Cell)
                    {
                        return (info.Cell as Cell).BCCH;
                    }
                    else if (info.Cell is TDCell)
                    {
                        return (info.Cell as TDCell).FREQ;
                    }
                    else if (info.Cell is WCell)
                    {
                        return (info.Cell as WCell).UARFCN;
                    }
                    else if (info.Cell is LTECell)
                    {
                        return (info.Cell as LTECell).EARFCN;
                    }
                    else if (info.Cell is NRCell)
                    {
                        return (info.Cell as NRCell).SSBARFCN;
                    }
                    return "-";
                }
                else if (row is MissNbCellSubResult)
                {
                    MissNbCellSubResult nbResult = row as MissNbCellSubResult;
                    if (nbResult.NbCell is Cell)
                    {
                        return (nbResult.NbCell as Cell).BCCH;
                    }
                    else if (nbResult.NbCell is TDCell)
                    {
                        return (nbResult.NbCell as TDCell).FREQ;
                    }
                    else if (nbResult.NbCell is WCell)
                    {
                        return (nbResult.NbCell as WCell).UARFCN;
                    }
                    else if (nbResult.NbCell is LTECell)
                    {
                        return (nbResult.NbCell as LTECell).EARFCN;
                    }
                    else if (nbResult.NbCell is NRCell)
                    {
                        return (nbResult.NbCell as NRCell).SSBARFCN;
                    }
                    return "-";
                }
                return string.Empty;
            };
            this.olvColumnCellBSIC.AspectGetter = delegate(object row)
            {
                if (row is MissNbCellMainItem)
                {
                    MissNbCellMainItem info = row as MissNbCellMainItem;
                    if (info.Cell is Cell)
                    {
                        return (info.Cell as Cell).BSIC;
                    }
                    else if (info.Cell is TDCell)
                    {
                        return (info.Cell as TDCell).CPI;
                    }
                    else if (info.Cell is WCell)
                    {
                        return (info.Cell as WCell).PSC;
                    }
                    else if (info.Cell is LTECell)
                    {
                        return (info.Cell as LTECell).PCI;
                    }
                    else if (info.Cell is NRCell)
                    {
                        return (info.Cell as NRCell).PCI;
                    }
                    return "-";
                }
                else if (row is MissNbCellSubResult)
                {
                    MissNbCellSubResult nbResult = row as MissNbCellSubResult;
                    if (nbResult.NbCell is Cell)
                    {
                        return (nbResult.NbCell as Cell).BSIC;
                    }
                    else if (nbResult.NbCell is TDCell)
                    {
                        return (nbResult.NbCell as TDCell).CPI;
                    }
                    else if (nbResult.NbCell is WCell)
                    {
                        return (nbResult.NbCell as WCell).PSC;
                    }
                    else if (nbResult.NbCell is LTECell)
                    {
                        return (nbResult.NbCell as LTECell).PCI;
                    }
                    else if (nbResult.NbCell is NRCell)
                    {
                        return (nbResult.NbCell as NRCell).PCI;
                    }
                    return "-";
                }
                return string.Empty;
            };
            //this.olvColumnCellBSIC.RendererDelegate = delegate(EventArgs e, Graphics g, Rectangle r, object row)
            //{
            //    if (row is MissNbCellMainItem)
            //    {
            //        MissNbCellMainItem info = row as MissNbCellMainItem;
            //        if (info.Cell is Cell)
            //        {
            //            g.DrawString((info.Cell as Cell).BSIC.ToString(), this.listViewTotal.Font, new SolidBrush(Color.Black), r.X, r.Y);
            //        }
            //        else if (info.Cell is TDCell)
            //        {
            //            g.DrawString((info.Cell as TDCell).CPI.ToString(), this.listViewTotal.Font, new SolidBrush(Color.Black), r.X, r.Y);
            //        }
            //        else if (info.Cell is WCell)
            //        {
            //            g.DrawString((info.Cell as WCell).PSC.ToString(), this.listViewTotal.Font, new SolidBrush(Color.Black), r.X, r.Y);
            //        }
            //        else if (info.Cell is LTECell)
            //        {
            //            g.DrawString((info.Cell as LTECell).PCI.ToString(), this.listViewTotal.Font, new SolidBrush(Color.Black), r.X, r.Y);
            //        }
            //    }
            //    else if (row is MissNbCellSubResult)
            //    {
            //        MissNbCellSubResult nbResult = row as MissNbCellSubResult;
            //        if (nbResult.NbCell is Cell)
            //        {
            //            g.DrawString((nbResult.NbCell as Cell).BSIC.ToString(), this.listViewTotal.Font, new SolidBrush(Color.Black), r.X, r.Y);
            //        }
            //        else if (nbResult.NbCell is TDCell)
            //        {
            //            g.DrawString((nbResult.NbCell as TDCell).CPI.ToString(), this.listViewTotal.Font, new SolidBrush(Color.Black), r.X, r.Y);
            //        }
            //        else if (nbResult.NbCell is WCell)
            //        {
            //            g.DrawString((nbResult.NbCell as WCell).PSC.ToString(), this.listViewTotal.Font, new SolidBrush(Color.Black), r.X, r.Y);
            //        }
            //        else if (nbResult.NbCell is LTECell)
            //        {
            //            if (nbResult.IsMod3NBCell)
            //            {
            //                g.FillRectangle(new SolidBrush(color), r);
            //            }
            //            g.DrawString((nbResult.NbCell as LTECell).PCI.ToString(), this.listViewTotal.Font, new SolidBrush(Color.Black), r.X, r.Y);
            //        }
            //    }
            //    return true;
            //};
            this.colMainCellRxLevMean.AspectGetter += delegate(object row)
            {
                if (row is MissNbCellSubResult)
                {
                    MissNbCellSubResult nbResult = row as MissNbCellSubResult;
                    if (double.IsNaN(nbResult.MainCellRxLevAvg))
                    {
                        return "-";
                    }
                    return nbResult.MainCellRxLevAvg;
                }
                return null;
            };
            this.olvColumnNBCellRxLevMean.AspectGetter = delegate(object row)
            {
                if (row is MissNbCellSubResult)
                {
                    MissNbCellSubResult nbResult = row as MissNbCellSubResult;
                    if (double.IsNaN(nbResult.NBCellRxLevAvg))
                    {
                        return "-";
                    }
                    return nbResult.NBCellRxLevAvg;
                }
                return null;
            };
            this.colOneGridPos.AspectGetter += delegate(object row)
            {
                if (row is MissNbCellSubResult)
                {
                    MissNbCellSubResult nbResult = row as MissNbCellSubResult;
                    return nbResult.OneGridPos;
                }
                return null;
            };
            this.olvColumnRoadDesc.AspectGetter = delegate(object row)
            {
                if (row is MissNbCellSubResult)
                {
                    MissNbCellSubResult nbResult = row as MissNbCellSubResult;
                    return nbResult.RoadDesc;
                }
                return string.Empty;
            };
            this.listViewTotal.CanExpandGetter = delegate(object x)
            {
                return x is MissNbCellMainItem;
            };
            this.listViewTotal.ChildrenGetter = delegate(object x)
            {
                MissNbCellMainItem nbCellMissInfo = (MissNbCellMainItem)x;
                return nbCellMissInfo.MissCellsInfo;
            };
        }

        private void hilightCells(ICell mainCell, IEnumerable<ICell> nbCells)
        {
            if (mainCell is Cell)
            {
                mModel.SelectedCell = mainCell as Cell;
            }
            else if (mainCell is TDCell)
            {
                mModel.SelectedTDCell = mainCell as TDCell;
            }
            else if (mainCell is LTECell)
            {
                mModel.SelectedLTECell = mainCell as LTECell;

            }
            else if (mainCell is WCell)
            {
                mModel.SelectedWCell = mainCell as WCell;
            }
            else if (mainCell is NRCell)
            {
                mModel.SetSelectedNRCell(mainCell as NRCell);
            }

            foreach (ICell cell in nbCells)
            {
                if (cell is Cell)
                {
                    mModel.SelectedCells.Add(cell as Cell);
                }
                else if (cell is TDCell)
                {
                    mModel.SelectedTDCells.Add(cell as TDCell);
                }
                else if (cell is LTECell)
                {
                    mModel.SelectedLTECells.Add(cell as LTECell);
                }
                else if (cell is WCell)
                {
                    mModel.SelectedWCells.Add(cell as WCell);
                }
                else if (cell is NRCell)
                {
                    mModel.SelectedNRCells.Add(cell as NRCell);
                }
            }
            List<ICell> temp = new List<ICell>();
            temp.Add(mainCell);
            temp.AddRange(nbCells);
            DbRect bounds = GetBounds(temp);
            if (MainModel.CurGridColorUnitMatrix != null)
            {
                DbRect b = MainModel.CurGridColorUnitMatrix.GetBounds();
                if (b != null)
                {
                    bounds.MergeRects(b);
                }
            }
            mModel.MainForm.GetMapForm().GoToViewCellsBounds(bounds);
        }

        //private void hilightNBCell(int orgCellID, IEnumerable<int> nbCells)
        //{
        //    try
        //    {
        //        DbRect gridBounds = new DbRect();
        //        if (MainModel.CurGridColorUnitMatrix != null)
        //        {
        //            gridBounds = MainModel.CurGridColorUnitMatrix.GetBounds();
        //        }
        //        if (inspectType == InspectType.GSM)
        //        {
        //            Cell cell1 = mModel.CellManager.GetCurrentCell(orgCellID);
        //            if (cell1 != null)
        //            {
        //                mModel.SelectedCell = cell1;
        //                mModel.SelectedCells.Clear();
        //                mModel.SelectedCells.Add(cell1);
        //                mModel.SelectedCells.AddRange(GetCellListByName(mModel, nbCells));
        //                DbRect bounds = GetBounds(mModel.SelectedCells.ToArray());
        //                bounds.MergeRects(gridBounds);
        //                mModel.MainForm.GetMapForm().GoToViewCellsBounds(bounds);

        //                //mModel.ScanInterSet.JamCellOnGIS = cell1;
        //                //mModel.ScanInterSet.OrgCellsOnGIS = GetCellListByName(mModel, nbCells);

        //                //List<Cell> tempList = new List<Cell>(mModel.ScanInterSet.OrgCellsOnGIS);
        //                //tempList.Add(cell1);

        //                //mModel.ClusterAnalysisSet.OrgCellsOnGIS = mModel.ScanInterSet.OrgCellsOnGIS;
        //                //mModel.ClusterAnalysisSet.JamCellOnGIS = mModel.ScanInterSet.JamCellOnGIS;
        //                //DbRect bounds = GetBounds(tempList);
        //                //bounds.MergeRects(gridBounds);
        //                //mModel.FireCellDrawInfoChanged(this);
        //                //mModel.MainForm.GetMapForm().GoToViewCellsBounds(bounds);
        //            }
        //        }
        //        else if (inspectType == InspectType.T2T || inspectType == InspectType.T2G)
        //        {
        //            TDCell tdCell = mModel.CellManager.GetCurrentTDCell(orgCellID);
        //            if (tdCell != null)
        //            {
        //                mModel.ScanInterSet.JamTDCellOnGIS = tdCell;
        //                if (inspectType == InspectType.T2T)
        //                {
        //                    mModel.ScanInterSet.OrgTDCellsOnGIS = GetTDCellListByName(mModel, nbCells);

        //                    List<TDCell> tempList = new List<TDCell>(mModel.ScanInterSet.OrgTDCellsOnGIS);
        //                    tempList.Add(tdCell);

        //                    mModel.ClusterAnalysisSet.OrgTDCellsOnGIS = mModel.ScanInterSet.OrgTDCellsOnGIS;
        //                    mModel.ClusterAnalysisSet.JamTDCellOnGIS = mModel.ScanInterSet.JamTDCellOnGIS;
        //                    DbRect bounds = GetBounds(tempList);
        //                    bounds.MergeRects(gridBounds);
        //                    mModel.FireCellDrawInfoChanged(this);
        //                    mModel.MainForm.GetMapForm().GoToViewCellsBounds(bounds);
        //                }
        //                else
        //                {
        //                    mModel.ScanInterSet.OrgCellsOnGIS = GetCellListByName(mModel, nbCells);

        //                    List<Cell> tempList = new List<Cell>(mModel.ScanInterSet.OrgCellsOnGIS);
        //                    //tempList.Add(tdCell);

        //                    mModel.ClusterAnalysisSet.OrgCellsOnGIS = mModel.ScanInterSet.OrgCellsOnGIS;
        //                    mModel.ClusterAnalysisSet.JamTDCellOnGIS = mModel.ScanInterSet.JamTDCellOnGIS;
        //                    DbRect bounds = GetBounds(tempList);
        //                    List<TDCell> tdCellList = new List<TDCell>();
        //                    tdCellList.Add(tdCell);
        //                    bounds.MergeRects(GetBounds(tdCellList));
        //                    bounds.MergeRects(gridBounds);
        //                    mModel.FireCellDrawInfoChanged(this);
        //                    mModel.MainForm.GetMapForm().GoToViewCellsBounds(bounds);
        //                }
        //            }
        //        }
        //        else if (inspectType == InspectType.W2W)
        //        {
        //            WCell wCell = mModel.CellManager.GetCurrentWCell(orgCellID);
        //            if (wCell != null)
        //            {
        //                mModel.ScanInterSet.JamWCellOnGIS = wCell;
        //                mModel.ScanInterSet.OrgWCellsOnGIS = GetWCellListByName(mModel, nbCells);

        //                List<WCell> tempList = new List<WCell>(mModel.ScanInterSet.OrgWCellsOnGIS);
        //                tempList.Add(wCell);

        //                mModel.ClusterAnalysisSet.OrgWCellsOnGIS = mModel.ScanInterSet.OrgWCellsOnGIS;
        //                mModel.ClusterAnalysisSet.JamWCellOnGIS = mModel.ScanInterSet.JamWCellOnGIS;
        //                DbRect bounds = GetBounds(tempList);
        //                bounds.MergeRects(gridBounds);
        //                mModel.FireCellDrawInfoChanged(this);
        //                mModel.MainForm.GetMapForm().GoToViewCellsBounds(bounds);
        //            }
        //        }
        //        else if (inspectType == InspectType.LTE2GSM || inspectType == InspectType.LTE2LTE || inspectType == InspectType.LTE2TD)
        //        {
        //            MainModel.SelectedLTECell = mModel.CellManager.GetCurrentLTECell(orgCellID);
        //            MainModel.SelectedLTECell.CellType = MainOrNBCell.MainCell;
        //            DbRect bounds = null;
        //            if (inspectType == InspectType.LTE2GSM)
        //            {
        //                MainModel.SelectedCells = GetCellListByName(mModel, nbCells);
        //                foreach (Cell cell in MainModel.SelectedCells)
        //                {
        //                    cell.CellType = MainOrNBCell.NBCell;
        //                }
        //                bounds = GetBounds(MainModel.SelectedCells);
        //            }
        //            else if (inspectType == InspectType.LTE2TD)
        //            {
        //                MainModel.SelectedTDCells = GetTDCellListByName(mModel, nbCells);
        //                foreach (TDCell tdCell in MainModel.SelectedTDCells)
        //                {
        //                    tdCell.CellType = MainOrNBCell.NBCell;
        //                }
        //                bounds = GetBounds(MainModel.SelectedTDCells);
        //            }
        //            else
        //            {
        //                MainModel.SelectedLTECells = GetLTECellListByName(mModel, nbCells);
        //                foreach (LTECell lteCell in MainModel.SelectedLTECells)
        //                {
        //                    lteCell.CellType = MainOrNBCell.NBCell;
        //                }
        //                bounds = GetBounds(MainModel.SelectedLTECells);
        //            }
        //            List<LTECell> lteCells = new List<LTECell>();
        //            lteCells.Add(MainModel.SelectedLTECell);
        //            bounds.MergeRects(GetBounds(lteCells));
        //            bounds.MergeRects(gridBounds);
        //            mModel.FireCellDrawInfoChanged(this);
        //            mModel.MainForm.GetMapForm().GoToViewCellsBounds(bounds);
        //        }
        //    }
        //    catch
        //    { }
        //}

        private DbRect GetBounds(IEnumerable<ICell> cells)
        {
            double minjd = double.MaxValue;
            double maxjd = double.MinValue;
            double minwd = double.MaxValue;
            double maxwd = double.MinValue;
            foreach (ICell cell in cells)
            {
                minjd = Math.Min(minjd, cell.Longitude);
                maxjd = Math.Max(maxjd, cell.Longitude);
                minwd = Math.Min(minwd, cell.Latitude);
                maxwd = Math.Max(maxwd, cell.Latitude);
            }
            DbRect drect = new DbRect(minjd, minwd, maxjd, maxwd);
            if (drect.x1 == drect.x2 && drect.y1 == drect.y2)
            {
                drect.x1 -= 0.001;
                drect.x2 += 0.001;
                drect.y1 -= 0.001;
                drect.y2 += 0.001;
            }
            return drect;
        }

        private void listViewTotal_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (listViewTotal.SelectedObject != null)
                {
                    mModel.SelectedTDCell = null;
                    mModel.SelectedWCell = null;
                    mModel.SelectedCDCell = null;
                    mModel.SelectedLTECell = null;
                    mModel.SelectedCell = null;
                    mModel.SetSelectedNRCell(null);

                    mModel.SelectedCells.Clear();
                    mModel.SelectedTDCells.Clear();
                    mModel.SelectedLTECells.Clear();
                    mModel.SelectedWCells.Clear();

                    mModel.ScanInterSet.OrgCellsOnGIS.Clear();
                    mModel.ScanInterSet.OrgTDCellsOnGIS.Clear();
                    mModel.ScanInterSet.OrgCellsOnGIS.Clear();
                    mModel.ScanInterSet.OrgWCellsOnGIS.Clear();
                    if (listViewTotal.SelectedObject is MissNbCellMainItem)
                    {
                        MissNbCellMainItem mainItem = listViewTotal.SelectedObject as MissNbCellMainItem;
                        if (nbCellMissInfoDic.ContainsKey(mainItem.Cell))
                        {
                            List<ICell> nbCells = new List<ICell>();
                            MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                            foreach (MissNbCellSubResult dbResult in nbCellMissInfoDic[mainItem.Cell].MissCellsInfo)
                            {
                                nbCells.Add(dbResult.NbCell);
                                dbResult.GetNBCellGridMatrix(inspectType, MainModel.CurGridColorUnitMatrix);
                            }
                            fillLayer(MainModel.CurGridColorUnitMatrix);
                            hilightCells(mainItem.Cell, nbCells);
                        }
                    }
                    else
                    {
                        if (listViewTotal.SelectedObject is MissNbCellSubResult)
                        {
                            MissNbCellSubResult nbResult = listViewTotal.SelectedObject as MissNbCellSubResult;

                            MainModel.CurGridColorUnitMatrix = nbResult.GetNBCellGridMatrix(inspectType);
                            fillLayer(MainModel.CurGridColorUnitMatrix);
                            MapGridLayer.NeedFreshFullImg = true;
                            hilightCells(nbResult.MainCell, new ICell[] { nbResult.NbCell });
                            if (MainModel.MainForm.GetMapForm().GetGridShowLayer().CurModeIndex < 0)
                            {
                                MainModel.MainForm.GetMapForm().GetGridShowLayer().CurModeIndex = 0;
                                MainModel.RefreshLegend();
                            }
                            MainModel.FireGridCoverQueried(this);
                        }
                    }
                }
            }
            catch
            {
            }
        }

        private void fillLayer(GridMatrix<ColorUnit> compareResult)
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                MasterCom.MTGis.CustomDrawLayer cLayer = mf.GetCustomLayer(typeof(MissGridUnitLayer));
                if (cLayer == null)
                {
                    layer = new MissGridUnitLayer(mf.GetMapOperation(), "邻区配置核查");
                    mf.AddTempCustomLayer(layer);
                }
                else
                {
                    layer = cLayer as MissGridUnitLayer;
                }
            }
            layer.Invalidate();
            layer.FillData(compareResult);
        }
        MissGridUnitLayer layer = null;

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (Convert.ToInt32(numNBDistanceMin.Value) > Convert.ToInt32(numNBDistanceMax.Value))
            {
                MessageBox.Show(this, "邻区间距选择错误，请检查", "筛选失败");
                return;
            }
            pnlCondition.Visible = false;
            if (cellRxLevMin != (int)numCellRxLevMin.Value || cellRxLevMax != (int)numCellRxLevMax.Value || nbCellRxLevMin != (int)numNBCellRxLevMin.Value ||
                nbCellRxLevMax != (int)numNBCellRxLevMax.Value || oldTopN != topN || oldDValueWithTop1 != dValueWithTop1 || oldBandTypeFilter != bandTypeFilter)
            {
                refreshData();
            }
            else
            {
                freshShowListView();
            }
        }

        private void ToolStripMenuItemPuthOut_Click(object sender, EventArgs e)
        {
            listViewTotal.ExpandAll();
            MasterCom.Util.UiEx.WaitTextBox.Show(this, "正在加载表格数据...", DoExcel_listViewTotal);
        }

        private void ToolStripMenuItemExpendAll_Click(object sender, EventArgs e)
        {
            listViewTotal.ExpandAll();
        }

        private void ToolStripMenuItemUnOpenAll_Click(object sender, EventArgs e)
        {
            listViewTotal.CollapseAll();
        }

        private void DoExcel_listViewTotal()
        {
            try
            {
                olvColumnRoadDesc.IsVisible = false;
                MasterCom.RAMS.Util.ExcelManager.ExportExcel((ObjectListView)listViewTotal, false);
            }
            finally
            {
                olvColumnRoadDesc.IsVisible = true;
                MasterCom.Util.UiEx.WaitTextBox.Close();
            }
        }

        private NBCellGridInfoForm nbCellGridInfoForm = null;
        private void ToolStripMenuItemPushOutGridInfo_Click(object sender, EventArgs e)
        {
            //if (nbCellGridInfoForm == null)
            //{
            //    nbCellGridInfoForm = new NBCellGridInfoForm();
            //}
            //nbCellGridInfoForm.FillForm(cellGridDic);
            //nbCellGridInfoForm.Owner = this;
            //nbCellGridInfoForm.Visible = true;           
        }

        private void lblConditionVisible_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            pnlCondition.Visible = true;
        }

        private void lblConditionDisappear_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            pnlCondition.Visible = false;
        }

        private void rdbTopN_CheckedChanged(object sender, EventArgs e)
        {
            numTopN.Enabled = rdbTopN.Checked;
            numDValueWithTop1.Enabled = rdbDValueWithTop1.Checked;
            topN = (int)numTopN.Value;
            dValueWithTop1 = 0;
        }

        private void rdbDValueWithTop1_CheckedChanged(object sender, EventArgs e)
        {
            numTopN.Enabled = rdbTopN.Checked;
            numDValueWithTop1.Enabled = rdbDValueWithTop1.Checked;
            dValueWithTop1 = (int)numDValueWithTop1.Value;
            topN = 0;
        }

        private void numTopN_ValueChanged(object sender, EventArgs e)
        {
            topN = (int)numTopN.Value;
            dValueWithTop1 = 0;
        }

        private void numDValueWithTop1_ValueChanged(object sender, EventArgs e)
        {
            dValueWithTop1 = (int)numDValueWithTop1.Value;
            topN = 0;
        }

        private void rdbNBigerThenMain_CheckedChanged(object sender, EventArgs e)
        {
            numTopN.Enabled = rdbTopN.Checked;
            numDValueWithTop1.Enabled = rdbDValueWithTop1.Checked;
            if (rdbNBigerThenMain.Checked)
            {
                dValueWithTop1 = 0;
                topN = 0;
            }
        }

        private void cbxBandType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (inspectType == InspectType.GSM)
            {
                bandTypeFilter = (CellBandTypeFilter)cbxBandType.SelectedIndex;
            }
            else if (inspectType == InspectType.T2T)
            {
                bandTypeFilter = CellBandTypeFilter.TD2TD;
            }
            else if (inspectType == InspectType.T2G)
            {
                bandTypeFilter = CellBandTypeFilter.TD2GSM;
            }
            else if (inspectType == InspectType.W2W)
            {
                bandTypeFilter = CellBandTypeFilter.W2W;
            }
            else if (inspectType == InspectType.LTE2GSM)
            {
                bandTypeFilter = CellBandTypeFilter.LTE2GSM;
            }
        }

        private void miExportToExcelOneByOne_Click(object sender, EventArgs e)
        {
            exportToExcelOneByOne();
        }

        private void exportToExcelOneByOne()
        {
            try
            {
                List<List<object>> datas = new List<List<object>>();
                List<object> rowTitle = new List<object>();
                rowTitle.Add("参考小区");
                rowTitle.Add("参考小区code");
                string lacCaption = "参考小区LAC";
                string ciCaption = "参考小区CI";
                if (inspectType == InspectType.LTE2GSM
                     || inspectType == InspectType.LTE2LTE
                     || inspectType == InspectType.LTE2TD)
                {
                    lacCaption = "参考小区TAC";
                    ciCaption = "参考小区ECI";
                }
                rowTitle.Add(lacCaption);
                rowTitle.Add(ciCaption);

                rowTitle.Add("参考小区平均场强");

                rowTitle.Add("邻区");
                rowTitle.Add("邻区code");

                string ncLACCaption = "邻区LAC";
                string ncCICaption = "邻区CI";
                if (inspectType == InspectType.LTE2LTE)
                {
                    ncLACCaption = "邻区TAC";
                    ncCICaption = "邻区ECI";
                }
                rowTitle.Add(ncLACCaption);
                rowTitle.Add(ncCICaption);
                rowTitle.Add("邻区平均场强");
                rowTitle.Add("邻区状况");
                rowTitle.Add("栅格数");
                rowTitle.Add("其一栅格中心经纬度");
                rowTitle.Add("采样点数");
                rowTitle.Add("小区间距(米)");
                rowTitle.Add("天线夹角");
                rowTitle.Add("是否在主瓣方向");
                //rowTitle.Add("涉及道路");
                datas.Add(rowTitle);
                foreach (MissNbCellMainItem info in nbCellMissInfoDic.Values)
                {
                    foreach (MissNbCellSubResult nbResult in info.MissCellsInfo)
                    {
                        List<object> row = new List<object>();
                        string cellName = info.Cell.Name;
                        string lac = "";
                        string ci = "";
                        string code = "";
                        if (info.Cell is Cell)
                        {
                            lac = (info.Cell as Cell).LAC.ToString();
                            ci = (info.Cell as Cell).CI.ToString();
                            code = (info.Cell as Cell).Code;
                        }
                        else if (info.Cell is TDCell)
                        {
                            lac = (info.Cell as TDCell).LAC.ToString();
                            ci = (info.Cell as TDCell).CI.ToString();
                            code = (info.Cell as TDCell).Code;
                        }
                        else if (info.Cell is WCell)
                        {
                            lac = (info.Cell as WCell).LAC.ToString();
                            ci = (info.Cell as WCell).CI.ToString();
                            code = (info.Cell as WCell).Code;
                        }
                        else if (info.Cell is LTECell)
                        {
                            lac = (info.Cell as LTECell).TAC.ToString();
                            ci = (info.Cell as LTECell).ECI.ToString();
                            code = (info.Cell as LTECell).SCellID.ToString();
                        }
                        row.Add(cellName);
                        row.Add(code);
                        row.Add(lac);
                        row.Add(ci);
                        row.Add(nbResult.MainCellRxLevAvg);
                        cellName = "";
                        lac = "";
                        ci = "";
                        code = "";
                        if (inspectType == InspectType.GSM
                            || inspectType == InspectType.T2G
                            || inspectType == InspectType.LTE2GSM)
                        {
                            Cell cell = nbResult.NbCell as Cell;
                            if (cell != null)
                            {
                                cellName = cell.Name;
                                lac = cell.LAC.ToString();
                                ci = cell.CI.ToString();
                                code = cell.Code;
                            }
                        }
                        else if (inspectType == InspectType.T2T
                            || inspectType == InspectType.LTE2TD)
                        {
                            TDCell tdCell = nbResult.NbCell as TDCell;
                            if (tdCell != null)
                            {
                                cellName = tdCell.Name;
                                lac = tdCell.LAC.ToString();
                                ci = tdCell.CI.ToString();
                                code = tdCell.Code;
                            }
                        }
                        else if (inspectType == InspectType.W2W)
                        {
                            WCell wCell = nbResult.NbCell as WCell;
                            if (wCell != null)
                            {
                                cellName = wCell.Name;
                                lac = wCell.LAC.ToString();
                                ci = wCell.CI.ToString();
                                code = wCell.Code;
                            }
                        }
                        else if (inspectType == InspectType.LTE2LTE)
                        {
                            LTECell cell = nbResult.NbCell as LTECell;
                            if (cell != null)
                            {
                                cellName = cell.Name;
                                lac = cell.TAC.ToString();
                                ci = cell.ECI.ToString();
                                code = cell.SCellID.ToString();
                            }
                        }
                        row.Add(cellName);
                        row.Add(code);
                        row.Add(lac);
                        row.Add(ci);
                        row.Add(nbResult.NBCellRxLevAvg.ToString());
                        row.Add(nbResult.result);
                        row.Add(nbResult.gridnum);
                        row.Add(nbResult.OneGridPos);
                        row.Add(nbResult.sampleNum);
                        row.Add(Math.Round(nbResult.distance, 2));
                        row.Add(nbResult.Angle);
                        row.Add(nbResult.InAngle);
                        //row.Add(nbResult.GetRoadPlaceDesc(Session.Current.Catalog.OpenTable(mModel.StreetTablePath, mModel.StreetTableName), mModel.StreetTableName, mModel.StreetTableNameCol, mModel.StreetTableObjCol));
                        datas.Add(row);
                    }
                }
                if (datas != null)
                {
                    ExcelNPOIManager.ExportToExcel(datas);
                }
            }
            catch
            {
            }
        }

        private void listViewTotal_DrawItem(object sender, DrawListViewItemEventArgs e)
        {
            //init();
            //if (sender is MissNbCellSubResult)
            //{
            //    listViewTotal.GetItem(e.ItemIndex).BackColor = this.color;
            //}
        }
    }

    internal class MissNbCellMainItem
    {
        private ICell cell;
        public ICell Cell
        {
            get { return cell; }
        }
        internal int SampleCount = 0;
        internal double RxLevMean = 0;
        internal List<MissNbCellSubResult> MissCellsInfo = new List<MissNbCellSubResult>();
        internal double maxNBRxLevAvg = -999;
        private object p;

        public MissNbCellMainItem(ICell cell)
        {
            this.cell = cell;
        }
    }
}