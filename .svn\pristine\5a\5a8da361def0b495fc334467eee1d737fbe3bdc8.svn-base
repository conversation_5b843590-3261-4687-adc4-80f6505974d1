﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.MControls;

namespace MasterCom.RAMS.Func.EventBlock
{
    public partial class EventBlockOptionDlg : BaseForm
    {
        public EventBlockOptionDlg()
        {
            InitializeComponent();
        }
        public int GetSettingRadius()
        {
            return (int)numRadius.Value;
        }
        public int GetSettingMinEvtCount()
        {
            return (int)numMinEventCount.Value;
        }
        public bool GetSettingLacCiCond()
        {
            return checkBoxLACCI.Checked;
        }
        private void btnQuery_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
    public class EventBlockLegendOption //事件汇聚组设置（按异常事件个数着色）
    {
        public EventBlockLegendOption()
        {
            initialize();
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                List<object> colorParams = new List<object>();
                param["excelBlockAbEventColorRanges"] = colorParams;
                foreach (ColorRange cr in Ranges)
                {
                    colorParams.Add(cr.Param);
                }
                return param;
            }
            set
            {
                Ranges.Clear();
                List<object> colorParams = (List<object>)value["excelBlockAbEventColorRanges"];
                foreach (object o in colorParams)
                {
                    Dictionary<string, object> colorParam = (Dictionary<string, object>)o;
                    ColorRange cr = new ColorRange();
                    cr.Param = colorParam;
                    Ranges.Add(cr);
                }
            }
        }

        private void initialize()
        {
            Ranges.Clear();
            Ranges.Add(new ColorRange(1, 3, Color.FromArgb(255, 255, 0)));
            Ranges.Add(new ColorRange(3, 5, Color.FromArgb(255, 150, 0)));
            Ranges.Add(new ColorRange(5, 9, Color.FromArgb(255, 0, 0)));
            Ranges.Add(new ColorRange(9, 13, Color.FromArgb(150, 0, 0)));
            Ranges.Add(new ColorRange(13, 50, Color.Black));
        }

        public List<ColorRange> Ranges { get; set; } = new List<ColorRange>();
    }
}