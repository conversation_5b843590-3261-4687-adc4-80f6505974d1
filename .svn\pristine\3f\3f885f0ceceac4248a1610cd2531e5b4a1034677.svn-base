﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Func.SystemSetting;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYCellWrongDir_WCDMA : ZTDIYCellWrongDirQueryBase
    {
        private static ZTDIYCellWrongDir_WCDMA intance = null;
        public static ZTDIYCellWrongDir_WCDMA GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYCellWrongDir_WCDMA();
                    }
                }
            }
            return intance;
        }
        private ZTDIYCellWrongDir_WCDMA()
            : base(MainModel.GetInstance())
        {
            init();
            cellWrongCond.SetThreShold(-120, 100, 50, 50);
        }

        private void init()
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.WCDMA_VOICE);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "覆盖方向异常_WCDMA"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 14000, 14021, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "W_TotalRSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysLAI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysCellID";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_frequency";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_Reference_PSC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"W_TotalRSCP");
            tmpDic.Add("themeName", (object)"W_TotalRSCP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void FireShowFormAfterQuery()
        {
            if (cellDic.Count == 0)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("没有符合条件的数据！");
                return;
            }
            CellWrongDirForm_TD frm =MainModel.CreateResultForm(typeof(CellWrongDirForm_TD)) as CellWrongDirForm_TD;
            frm.FillData(new List<TDCellWrongDir>(cellDic.Values), cellWrongCond.IsTwiceBatch);
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            float? rscp = (float?)tp["W_TotalRSCP"];
            if (rscp != null && rscp >= -120 && rscp <= -10 && rscp >= cellWrongCond.RxLevMin)
            {
                //int? lac = (int?)tp[MainModel.TD_SCell_LAC];
                //int? ci = (int?)tp[MainModel.TD_SCell_CI];
                WCell cell = tp.GetMainCell_W();
                if (cell == null)
                    return;
                double dis = cell.GetDistance(tp.Longitude, tp.Latitude);
                if (dis >= cellWrongCond.DistanceMin)
                {
                    TDCellWrongDir cellWrong;
                    if (!cellDic.TryGetValue(cell, out cellWrong))
                    {
                        cellWrong = new WCDMACellWrongDir(cell);
                        cellDic.Add(cell, cellWrong);
                    }
                    cellWrong.AddTestPoint(tp, cellWrongCond.AngleMin, cellWrongBatch);
                }
            }
        }

        protected override void getResultAfterQuery()
        {
            List<object> cells = new List<object>();
            foreach (ICell cell in cellDic.Keys)
            {
                if (cellDic[cell].WrongPercentage < cellWrongCond.WrongRateMin)
                {
                    cells.Add(cell);
                }
            }
            foreach (ICell cell in cells)
            {
                cellDic.Remove(cell);
            }
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.WCDMA业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["RSCPMin"] = cellWrongCond.RxLevMin;
                param["DisMin"] = cellWrongCond.DistanceMin;
                param["AngleMin"] = cellWrongCond.AngleMin;
                param["WrongPerMin"] = cellWrongCond.WrongRateMin;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("RSCPMin"))
                {
                    cellWrongCond.RxLevMin = float.Parse(param["RSCPMin"].ToString());
                }
                if (param.ContainsKey("DisMin"))
                {
                    cellWrongCond.DistanceMin = double.Parse(param["DisMin"].ToString());
                }
                if (param.ContainsKey("AngleMin"))
                {
                    cellWrongCond.AngleMin = int.Parse(param["AngleMin"].ToString());
                }
                if (param.ContainsKey("WrongPerMin"))
                {
                    cellWrongCond.WrongRateMin = double.Parse(param["WrongPerMin"].ToString());
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return null;
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (TDCellWrongDir block in cellDic.Values)
            {
                BackgroundResult result = block.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), Condition.Periods[0].IBeginTime,
                Condition.Periods[0].IEndTime, bgResultList);
            cellDic.Clear();
        }

        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                int goodSampleCount = bgResult.GetImageValueInt();
                float wrongPercent = bgResult.GetImageValueFloat();
                StringBuilder sb = new StringBuilder();
                sb.Append("正常采样点数：");
                sb.Append(goodSampleCount);
                sb.Append("\r\n");
                sb.Append("异常采样点占比：");
                sb.Append(wrongPercent);
                sb.Append("%");
                bgResult.ImageDesc = sb.ToString();
            }
        }
        #endregion
    }
}
