﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Collections;

namespace MasterCom.Util
{
    public enum Parameters
    {
        A, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T, U, V, W, X, Y, Z
    }
    public class MathParser
    {
        private readonly List<String> OperationOrder = new List<string>();

        public Dictionary<Parameters, decimal> Parameters { get; set; } = new Dictionary<Parameters, decimal>();

        public MathParser()
        {
            OperationOrder.Add("/");
            OperationOrder.Add("*");
            OperationOrder.Add("-");
            OperationOrder.Add("+");
            OperationOrder.Add("&");
            OperationOrder.Add("|");
            OperationOrder.Add("=");
            OperationOrder.Add("#");
            OperationOrder.Add(">");
            OperationOrder.Add("<");
            OperationOrder.Add("@");
        }
        public decimal Calculate(string Formula)
        {
            try
            {
                /*
                string[] arr = Formula.Split("/+-*()&|=><".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
                foreach (KeyValuePair<Parameters, decimal> de in _Parameters)
                {
                    foreach (string s in arr)
                    {
                        if (s != de.Key.ToString() && s.EndsWith(de.Key.ToString()))
                        {
                            Formula = Formula.Replace(s, (Convert.ToDecimal(s.Replace(de.Key.ToString(), "")) * de.Value).ToString());
                        }
                    }
                    Formula = Formula.Replace(de.Key.ToString(), de.Value.ToString());
                }*/
                while (Formula.LastIndexOf("(") > -1)
                {
                    int lastOpenPhrantesisIndex = Formula.LastIndexOf("(");
                    int firstClosePhrantesisIndexAfterLastOpened = Formula.IndexOf(")", lastOpenPhrantesisIndex);
                    decimal result = ProcessOperation(Formula.Substring(lastOpenPhrantesisIndex + 1, firstClosePhrantesisIndexAfterLastOpened - lastOpenPhrantesisIndex - 1));
                    bool AppendAsterix = false;
                    if (lastOpenPhrantesisIndex > 0)
                    {
                        string formulaChar = Formula.Substring(lastOpenPhrantesisIndex - 1, 1);
                        if (formulaChar != "(" && !OperationOrder.Contains(formulaChar))
                        {
                            AppendAsterix = true;
                        }
                    }

                    Formula = Formula.Substring(0, lastOpenPhrantesisIndex) + (AppendAsterix ? "*" : "") + result.ToString() + Formula.Substring(firstClosePhrantesisIndexAfterLastOpened + 1);

                }
                return ProcessOperation(Formula);
            }
            catch (Exception ex)
            {
                throw (new Exception("Error Occured While Calculating. Check Syntax", ex));
            }
        }

        private decimal ProcessOperation(string operation)
        {
            operation = operation.Replace(" ", "");
            ArrayList arr = getArr(operation);

            removeUnusedChar(arr);

            foreach (string op in OperationOrder)
            {
                while (arr.IndexOf(op) > -1)
                {
                    if (op == "@")//加入的特殊处理符号
                    {
                        return dealSpecialOperator(arr, op);
                    }
                    else
                    {
                        setArrByOperator(arr, op);
                    }
                }
            }
            return Convert.ToDecimal(arr[0]);
        }

        private ArrayList getArr(string operation)
        {
            ArrayList arr = new ArrayList();
            StringBuilder s = new StringBuilder();
            for (int i = 0; i < operation.Length; i++)
            {
                string currentCharacter = operation.Substring(i, 1);
                if (OperationOrder.IndexOf(currentCharacter) > -1)
                {
                    if (s.Length > 0)
                    {
                        arr.Add(s.ToString());
                    }
                    arr.Add(currentCharacter);
                    s = new StringBuilder();
                }
                else
                {
                    s.Append(currentCharacter);
                }
            }
            arr.Add(s.ToString());
            return arr;
        }

        private static void removeUnusedChar(ArrayList arr)
        {
            //处理掉"-"
            int pos = 0;
            while ((pos = arr.IndexOf("-", pos)) != -1)
            {
                if (pos == 0)
                {
                    arr.RemoveAt(0);
                    decimal op = Convert.ToDecimal(arr[0].ToString()) * -1;
                    arr[0] = op.ToString();
                }
                else
                {
                    string formerStr = arr[pos - 1].ToString();
                    decimal dec;
                    if (decimal.TryParse(formerStr, out dec))
                    {
                        pos++;
                    }
                    else
                    {
                        decimal op = Convert.ToDecimal(arr[pos + 1].ToString()) * -1;
                        arr.RemoveAt(pos);
                        arr[pos] = op.ToString();
                    }
                }
            }
        }

        private static decimal dealSpecialOperator(ArrayList arr, string op)
        {
            int operatorIndex = arr.IndexOf(op);
            decimal digitBeforeOperator = Convert.ToDecimal(arr[operatorIndex - 1]);

            string rightCompString = (string)arr[operatorIndex + 1];
            string[] rightValues = rightCompString.Split(',');
            foreach (string rv in rightValues)
            {
                decimal rvDecimal = Convert.ToDecimal(rv);
                if (digitBeforeOperator == rvDecimal)
                {
                    return 1;
                }
            }
            return 0;
        }

        private void setArrByOperator(ArrayList arr, string op)
        {
            int operatorIndex = arr.IndexOf(op);
            decimal digitBeforeOperator = Convert.ToDecimal(arr[operatorIndex - 1]);
            decimal digitAfterOperator = 0;
            if (arr[operatorIndex + 1].ToString() == "-")
            {
                arr.RemoveAt(operatorIndex + 1);
                digitAfterOperator = Convert.ToDecimal(arr[operatorIndex + 1]) * -1;
            }
            else
            {
                digitAfterOperator = Convert.ToDecimal(arr[operatorIndex + 1]);
            }
            arr[operatorIndex] = CalculateByOperator(digitBeforeOperator, digitAfterOperator, op);
            arr.RemoveAt(operatorIndex - 1);
            arr.RemoveAt(operatorIndex);
        }

        private decimal CalculateByOperator(decimal number1, decimal number2, string op)
        {
            if (op == "/")
            {
                return number1 / number2;
            }
            else if (op == "*")
            {
                return number1 * number2;
            }
            else if (op == "-")
            {
                return number1 - number2;
            }
            else if (op == "+")
            {
                return number1 + number2;
            }
            else if (op == "&")
            {
                return getValidData(number1 > 0 && number2 > 0);
            }
            else if (op == "|")
            {
                return getValidData(number1 > 0 || number2 > 0);
            }
            else if (op == "=")
            {
                return getValidData(number1 == number2);
            }
            else if (op == "#")
            {
                return getValidData(number1 != number2);
            }
            else if (op == ">")
            {
                return getValidData(number1 > number2);
            }
            else if (op == "<")
            {
                return getValidData(number1 < number2);
            }
            else
            {
                return 0;
            }
        }

        private int getValidData(bool value)
        {
            if (value)
            {
                return 1;
            }
            else
            {
                return 0;
            }
        }
    }
}
