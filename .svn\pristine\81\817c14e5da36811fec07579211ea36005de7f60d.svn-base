﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class GDDataPushDetailsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.conOutPutExcel = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.outPutExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridData = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.序号 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.数据编号 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.数据名称 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.数据FTP路径 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.数据监测平台路径 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.数据大小 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.数据完整到达平台时间 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.设备ID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.设备类型 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.设备名称 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.开始推送时间 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.结束推送时间 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.推送次数 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.推送状态 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.数据入记录表时间 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.失败错误信息 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.数据来源服务器 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.下载状态 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.conOutPutExcel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridData)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // conOutPutExcel
            // 
            this.conOutPutExcel.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.outPutExcel});
            this.conOutPutExcel.Name = "conOutPutExcel";
            this.conOutPutExcel.Size = new System.Drawing.Size(129, 26);
            // 
            // outPutExcel
            // 
            this.outPutExcel.Name = "outPutExcel";
            this.outPutExcel.Size = new System.Drawing.Size(128, 22);
            this.outPutExcel.Text = "导出Excel";
            this.outPutExcel.Click += new System.EventHandler(this.outPutExcel_Click);
            // 
            // gridData
            // 
            this.gridData.ContextMenuStrip = this.conOutPutExcel;
            this.gridData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridData.Location = new System.Drawing.Point(0, 0);
            this.gridData.MainView = this.gridView1;
            this.gridData.Name = "gridData";
            this.gridData.Size = new System.Drawing.Size(1070, 479);
            this.gridData.TabIndex = 1;
            this.gridData.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.序号,
            this.数据编号,
            this.数据名称,
            this.数据FTP路径,
            this.数据监测平台路径,
            this.数据大小,
            this.数据完整到达平台时间,
            this.设备ID,
            this.设备类型,
            this.设备名称,
            this.开始推送时间,
            this.结束推送时间,
            this.推送次数,
            this.推送状态,
            this.数据入记录表时间,
            this.失败错误信息,
            this.数据来源服务器,
            this.下载状态});
            this.gridView1.GridControl = this.gridData;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // 序号
            // 
            this.序号.Caption = "序号";
            this.序号.FieldName = "序号";
            this.序号.Name = "序号";
            this.序号.Visible = true;
            this.序号.VisibleIndex = 0;
            // 
            // 数据编号
            // 
            this.数据编号.Caption = "数据编号";
            this.数据编号.FieldName = "数据编号";
            this.数据编号.Name = "数据编号";
            this.数据编号.Visible = true;
            this.数据编号.VisibleIndex = 1;
            // 
            // 数据名称
            // 
            this.数据名称.Caption = "数据名称";
            this.数据名称.FieldName = "数据名称";
            this.数据名称.Name = "数据名称";
            this.数据名称.Visible = true;
            this.数据名称.VisibleIndex = 2;
            // 
            // 数据FTP路径
            // 
            this.数据FTP路径.Caption = "数据FTP路径";
            this.数据FTP路径.FieldName = "数据FTP路径";
            this.数据FTP路径.Name = "数据FTP路径";
            this.数据FTP路径.Visible = true;
            this.数据FTP路径.VisibleIndex = 3;
            // 
            // 数据监测平台路径
            // 
            this.数据监测平台路径.Caption = "数据监测平台路径";
            this.数据监测平台路径.FieldName = "数据监测平台路径";
            this.数据监测平台路径.Name = "数据监测平台路径";
            this.数据监测平台路径.Visible = true;
            this.数据监测平台路径.VisibleIndex = 4;
            // 
            // 数据大小
            // 
            this.数据大小.Caption = "数据大小";
            this.数据大小.FieldName = "数据大小";
            this.数据大小.Name = "数据大小";
            this.数据大小.Visible = true;
            this.数据大小.VisibleIndex = 5;
            // 
            // 数据完整到达平台时间
            // 
            this.数据完整到达平台时间.Caption = "数据完整到达平台时间";
            this.数据完整到达平台时间.FieldName = "数据完整到达平台时间";
            this.数据完整到达平台时间.Name = "数据完整到达平台时间";
            this.数据完整到达平台时间.Visible = true;
            this.数据完整到达平台时间.VisibleIndex = 6;
            // 
            // 设备ID
            // 
            this.设备ID.Caption = "设备ID";
            this.设备ID.FieldName = "设备ID";
            this.设备ID.Name = "设备ID";
            this.设备ID.Visible = true;
            this.设备ID.VisibleIndex = 7;
            // 
            // 设备类型
            // 
            this.设备类型.Caption = "设备类型";
            this.设备类型.FieldName = "设备类型";
            this.设备类型.Name = "设备类型";
            this.设备类型.Visible = true;
            this.设备类型.VisibleIndex = 8;
            // 
            // 设备名称
            // 
            this.设备名称.Caption = "设备名称";
            this.设备名称.FieldName = "设备名称";
            this.设备名称.Name = "设备名称";
            this.设备名称.Visible = true;
            this.设备名称.VisibleIndex = 9;
            // 
            // 开始推送时间
            // 
            this.开始推送时间.Caption = "开始推送时间";
            this.开始推送时间.FieldName = "开始推送时间";
            this.开始推送时间.Name = "开始推送时间";
            this.开始推送时间.Visible = true;
            this.开始推送时间.VisibleIndex = 10;
            // 
            // 结束推送时间
            // 
            this.结束推送时间.Caption = "结束推送时间";
            this.结束推送时间.FieldName = "结束推送时间";
            this.结束推送时间.Name = "结束推送时间";
            this.结束推送时间.Visible = true;
            this.结束推送时间.VisibleIndex = 11;
            // 
            // 推送次数
            // 
            this.推送次数.Caption = "推送次数";
            this.推送次数.FieldName = "推送次数";
            this.推送次数.Name = "推送次数";
            this.推送次数.Visible = true;
            this.推送次数.VisibleIndex = 12;
            // 
            // 推送状态
            // 
            this.推送状态.Caption = "推送状态";
            this.推送状态.FieldName = "推送状态";
            this.推送状态.Name = "推送状态";
            this.推送状态.Visible = true;
            this.推送状态.VisibleIndex = 13;
            // 
            // 数据入记录表时间
            // 
            this.数据入记录表时间.Caption = "数据入记录表时间";
            this.数据入记录表时间.FieldName = "数据入记录表时间";
            this.数据入记录表时间.Name = "数据入记录表时间";
            this.数据入记录表时间.Visible = true;
            this.数据入记录表时间.VisibleIndex = 14;
            // 
            // 失败错误信息
            // 
            this.失败错误信息.Caption = "失败错误信息";
            this.失败错误信息.FieldName = "失败错误信息";
            this.失败错误信息.Name = "失败错误信息";
            this.失败错误信息.Visible = true;
            this.失败错误信息.VisibleIndex = 15;
            // 
            // 数据来源服务器
            // 
            this.数据来源服务器.Caption = "数据来源服务器";
            this.数据来源服务器.FieldName = "数据来源服务器";
            this.数据来源服务器.Name = "数据来源服务器";
            this.数据来源服务器.Visible = true;
            this.数据来源服务器.VisibleIndex = 16;
            // 
            // 下载状态
            // 
            this.下载状态.Caption = "下载状态";
            this.下载状态.FieldName = "下载状态";
            this.下载状态.Name = "下载状态";
            this.下载状态.Visible = true;
            this.下载状态.VisibleIndex = 17;
            // 
            // GDDataPushDetailsForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1070, 479);
            this.Controls.Add(this.gridData);
            this.Name = "GDDataPushDetailsForm";
            this.Text = "数据推送信息跟踪列表";
            this.conOutPutExcel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridData)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip conOutPutExcel;
        private System.Windows.Forms.ToolStripMenuItem outPutExcel;
        private DevExpress.XtraGrid.GridControl gridData;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn 序号;
        private DevExpress.XtraGrid.Columns.GridColumn 数据编号;
        private DevExpress.XtraGrid.Columns.GridColumn 数据名称;
        private DevExpress.XtraGrid.Columns.GridColumn 数据FTP路径;
        private DevExpress.XtraGrid.Columns.GridColumn 数据监测平台路径;
        private DevExpress.XtraGrid.Columns.GridColumn 数据大小;
        private DevExpress.XtraGrid.Columns.GridColumn 数据完整到达平台时间;
        private DevExpress.XtraGrid.Columns.GridColumn 设备ID;
        private DevExpress.XtraGrid.Columns.GridColumn 设备类型;
        private DevExpress.XtraGrid.Columns.GridColumn 设备名称;
        private DevExpress.XtraGrid.Columns.GridColumn 开始推送时间;
        private DevExpress.XtraGrid.Columns.GridColumn 结束推送时间;
        private DevExpress.XtraGrid.Columns.GridColumn 推送次数;
        private DevExpress.XtraGrid.Columns.GridColumn 推送状态;
        private DevExpress.XtraGrid.Columns.GridColumn 数据入记录表时间;
        private DevExpress.XtraGrid.Columns.GridColumn 失败错误信息;
        private DevExpress.XtraGrid.Columns.GridColumn 数据来源服务器;
        private DevExpress.XtraGrid.Columns.GridColumn 下载状态;
    }
}