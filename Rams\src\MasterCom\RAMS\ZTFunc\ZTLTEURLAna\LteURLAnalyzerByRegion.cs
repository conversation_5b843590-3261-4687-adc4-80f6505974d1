﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    class LteURLAnalyzerByRegion : LteURLAnalyzer
    {
        protected List<LteURLRegion> resultsRegion = null;
        Dictionary<string, List<ResvRegion>> resvRegionsDic = null;
        protected Dictionary<string, List<Event>> dicBroRegion = null;
        protected Dictionary<string, List<Event>> dicDownRegion = null;
        protected Dictionary<string, List<Event>> dicVideoRegion = null;

        protected string GetRegion(string regionAndgrid)
        {
            if (regionAndgrid == "")
            {
                return "";
            }
            int index = regionAndgrid.IndexOf("@^&");
            string s = regionAndgrid;
            return s.Remove(index);
        }

        protected string GetGrid(string regionAndgrid)
        {
            if (regionAndgrid == "")
            {
                return "";
            }
            int indexStart = regionAndgrid.IndexOf("@^&") + 3;
            int indexEnd = regionAndgrid.IndexOf("&^@");
            int length = indexEnd - indexStart;
            string s = regionAndgrid.Substring(indexStart, length);
            
            return s;
        }

        protected string GetURL(string regionAndgrid)
        {
            if (regionAndgrid == "")
            {
                return "";
            }
            int index = regionAndgrid.IndexOf("&^@") + 3;
            string s = regionAndgrid.Substring(index);
            return s;
        }

        public void Analyze(List<DTFileDataManager> fileManagers)
        {
            resultsRegion = new List<LteURLRegion>();
            dicBroRegion = new Dictionary<string, List<Event>>();
            dicDownRegion = new Dictionary<string, List<Event>>();
            dicVideoRegion = new Dictionary<string, List<Event>>();
            foreach (DTFileDataManager dtFile in fileManagers)
            {
                Analyze(dtFile);
            }

            foreach (KeyValuePair<string, List<ResvRegion>> kvp in resvRegionsDic)
            {
                foreach (ResvRegion res in kvp.Value)
                {
                    LteURLRegion dtlRegion = new LteURLRegion(kvp.Key, res.RegionName);
                    Dictionary<string, List<Event>> dicBroTemp = new Dictionary<string, List<Event>>();
                    Dictionary<string, List<Event>> dicDownTemp = new Dictionary<string, List<Event>>();
                    Dictionary<string, List<Event>> dicVideoTemp = new Dictionary<string, List<Event>>();
                    dealBroRegion(kvp, res, dicBroTemp);
                    dealDownRegion(kvp, res, dicDownTemp);
                    dealVideoRegion(kvp, res, dicVideoTemp);
                    List<LteURLBroURL> dtlBros = HttpAnalyze(dicBroTemp);
                    dtlRegion.Bros = dtlBros;

                    List<LteURLDowURL> dtlDowns = DownAnalyze(dicDownTemp);
                    dtlRegion.Downs = dtlDowns;

                    List<LteURLVideoURL> dtlVideos = VideoAnalyze(dicVideoTemp);
                    dtlRegion.Videos = dtlVideos;
                    resultsRegion.Add(dtlRegion);
                }
            }
            SetSN();
        }

        private void dealBroRegion(KeyValuePair<string, List<ResvRegion>> kvp, ResvRegion res, Dictionary<string, List<Event>> dicBroTemp)
        {
            foreach (string key in dicBroRegion.Keys)
            {
                string regionName = GetRegion(key);
                string gridName = GetGrid(key);
                string URL = GetURL(key);
                if (kvp.Key.Equals(regionName) && res.RegionName.Equals(gridName))
                {
                    if (dicBroTemp.ContainsKey(URL))
                    {
                        dicBroTemp[URL].AddRange(dicBroRegion[key]);
                    }
                    else
                    {
                        dicBroTemp.Add(URL, dicBroRegion[key]);
                    }
                }
            }
        }

        private void dealDownRegion(KeyValuePair<string, List<ResvRegion>> kvp, ResvRegion res, Dictionary<string, List<Event>> dicDownTemp)
        {
            foreach (string key in dicDownRegion.Keys)
            {
                string regionName = GetRegion(key);
                string gridName = GetGrid(key);
                string URL = GetURL(key);
                if (kvp.Key.Equals(regionName) && res.RegionName.Equals(gridName))
                {
                    if (dicDownTemp.ContainsKey(URL))
                    {
                        dicDownTemp[URL].AddRange(dicDownRegion[key]);
                    }
                    else
                    {
                        dicDownTemp.Add(URL, dicDownRegion[key]);
                    }
                }
            }
        }

        private void dealVideoRegion(KeyValuePair<string, List<ResvRegion>> kvp, ResvRegion res, Dictionary<string, List<Event>> dicVideoTemp)
        {
            foreach (string key in dicVideoRegion.Keys)
            {
                string regionName = GetRegion(key);
                string gridName = GetGrid(key);
                string URL = GetURL(key);
                if (kvp.Key.Equals(regionName) && res.RegionName.Equals(gridName))
                {
                    if (dicVideoTemp.ContainsKey(URL))
                    {
                        dicVideoTemp[URL].AddRange(dicVideoRegion[key]);
                    }
                    else
                    {
                        dicVideoTemp.Add(URL, dicVideoRegion[key]);
                    }
                }
            }
        }

        protected void Analyze(DTFileDataManager dtFile)
        {
            DTFileDataManager f = dtFile;
            AnaToDic(f);
        }

        protected void AnaToDic(DTFileDataManager dtFile)
        {
            string broURL = "";
            string downURL = "";
            string videoURL = "";
            string resvRegionsBro = "";
            string resvRegionsDown = "";
            string resvRegionsVideo = "";
            Event evtBeforRequestBro = null;
            Event evtBeforRequestDown = null;
            Event evtBeforRequestVideo = null;
            List<Event> evtBroTemps = new List<Event>();
            List<Event> evtDownTemps = new List<Event>();
            List<Event> evtVideoTemps = new List<Event>();
            foreach (Event evt in dtFile.Events)
            {
                if (IsInHttpID(evt.ID))
                {
                    if (evt.ID == (int)LteURLCheckMsg.HttpRequestEventID 
                        || evt.ID == (int)LteURLCheckMsg.HttpRequestEventID_FDD)
                    {
                        if (evtBeforRequestBro != null)
                        {
                            resvRegionsBro = CheckEvtInRegion(evtBeforRequestBro);
                            if (resvRegionsBro != null)
                            {
                                StringBuilder sb = new StringBuilder(resvRegionsBro);
                                sb.Append("&^@" + broURL);
                                resvRegionsBro = sb.ToString();
                                if (evtBroTemps[0].ID == (int)LteURLCheckMsg.HttpRequestEventID 
                                    || evtBroTemps[0].ID == (int)LteURLCheckMsg.HttpRequestEventID_FDD)
                                {
                                    if (dicBroRegion.ContainsKey(resvRegionsBro))
                                    {
                                        dicBroRegion[resvRegionsBro].AddRange(evtBroTemps);
                                    }
                                    else
                                    {
                                        dicBroRegion.Add(resvRegionsBro, evtBroTemps);
                                    }
                                }
                            }
                        }
                        evtBroTemps = new List<Event>();
                        broURL = GetURL(evt.SN, dtFile.Messages);

                        evtBroTemps.Add(evt);
                        continue;
                    }
                    evtBroTemps.Add(evt);
                    evtBeforRequestBro = evt;
                }
                else if (IsInDownloadID(evt.ID))
                {
                    if (evt.ID == (int)LteURLCheckMsg.DownRequestEventID || evt.ID == (int)LteURLCheckMsg.DownRequestEventID_FDD)
                    {
                        if (evtBeforRequestDown != null)
                        {

                            resvRegionsDown = CheckEvtInRegion(evtBeforRequestDown);
                            if (resvRegionsDown != null)
                            {
                                StringBuilder sb = new StringBuilder(resvRegionsDown);
                                sb.Append("&^@" + downURL);
                                resvRegionsDown = sb.ToString();
                                if (evtDownTemps[0].ID == (int)LteURLCheckMsg.DownRequestEventID || evtDownTemps[0].ID == (int)LteURLCheckMsg.DownRequestEventID_FDD)
                                {
                                    if (dicDownRegion.ContainsKey(resvRegionsDown))
                                    {
                                        dicDownRegion[resvRegionsDown].AddRange(evtDownTemps);
                                    }
                                    else
                                    {
                                        dicDownRegion.Add(resvRegionsDown, evtDownTemps);
                                    }
                                }
                            }
                        }
                        evtDownTemps = new List<Event>();
                        downURL = GetURL(evt.SN, dtFile.Messages);
                        evtDownTemps.Add(evt);
                        continue;
                    }
                    evtDownTemps.Add(evt);
                    evtBeforRequestDown = evt;
                }
                else if (IsInVideoID(evt.ID))
                {
                    if (evt.ID == (int)LteURLCheckMsg.VideoRequestEventID || evt.ID == (int)LteURLCheckMsg.VideoRequestEventID_FDD)
                    {
                        if (evtBeforRequestVideo != null)
                        {
                            resvRegionsVideo = CheckEvtInRegion(evtBeforRequestVideo);

                            if (resvRegionsVideo != null)
                            {
                                StringBuilder sb = new StringBuilder(resvRegionsVideo);
                                sb.Append("&^@" + videoURL);
                                resvRegionsVideo = sb.ToString();
                                if (evtVideoTemps[0].ID == (int)LteURLCheckMsg.VideoRequestEventID || evtVideoTemps[0].ID == (int)LteURLCheckMsg.VideoRequestEventID_FDD)
                                {
                                    if (dicVideoRegion.ContainsKey(resvRegionsVideo))
                                    {
                                        dicVideoRegion[resvRegionsVideo].AddRange(evtVideoTemps);
                                    }
                                    else
                                    {
                                        dicVideoRegion.Add(resvRegionsVideo, evtVideoTemps);
                                    }
                                }
                            }
                        }
                        evtVideoTemps = new List<Event>();
                        videoURL = GetURL(evt.SN, dtFile.Messages);
                        evtVideoTemps.Add(evt);
                        continue;
                    }
                    evtVideoTemps.Add(evt);
                    evtBeforRequestVideo = evt;
                }
            }
            if (evtBeforRequestBro != null)
            {
                resvRegionsBro = CheckEvtInRegion(evtBeforRequestBro);
                if (resvRegionsBro != null)
                {
                    resvRegionsBro = resvRegionsBro + "&^@" + broURL;
                    if (evtBroTemps[0].ID == (int)LteURLCheckMsg.HttpRequestEventID 
                        || evtBroTemps[0].ID == (int)LteURLCheckMsg.HttpRequestEventID_FDD)
                    {
                        if (dicBroRegion.ContainsKey(resvRegionsBro))
                        {
                            dicBroRegion[resvRegionsBro].AddRange(evtBroTemps);
                        }
                        else
                        {
                            dicBroRegion.Add(resvRegionsBro, evtBroTemps);
                        }
                    }
                }
            }
            if (evtBeforRequestDown != null)
            {
                resvRegionsDown = CheckEvtInRegion(evtBeforRequestDown);
                if (resvRegionsDown != null)
                {
                    resvRegionsDown = resvRegionsDown + "&^@" + downURL;
                    if (evtDownTemps[0].ID == (int)LteURLCheckMsg.DownRequestEventID 
                        || evtDownTemps[0].ID == (int)LteURLCheckMsg.DownRequestEventID_FDD)
                    {
                        if (dicDownRegion.ContainsKey(resvRegionsDown))
                        {
                            dicDownRegion[resvRegionsDown].AddRange(evtDownTemps);
                        }
                        else
                        {
                            dicDownRegion.Add(resvRegionsDown, evtDownTemps);
                        }
                    }
                }
            }
            if (evtBeforRequestVideo != null)
            {
                resvRegionsVideo = CheckEvtInRegion(evtBeforRequestVideo);
                if (resvRegionsVideo != null)
                {
                    resvRegionsVideo = resvRegionsVideo + "&^@" + videoURL;
                    if (evtVideoTemps[0].ID == (int)LteURLCheckMsg.VideoRequestEventID 
                        || evtVideoTemps[0].ID == (int)LteURLCheckMsg.VideoRequestEventID_FDD)
                    {
                        if (dicVideoRegion.ContainsKey(resvRegionsVideo))
                        {
                            dicVideoRegion[resvRegionsVideo].AddRange(evtVideoTemps);
                        }
                        else
                        {
                            dicVideoRegion.Add(resvRegionsVideo, evtVideoTemps);
                        }
                    }
                }
            }
        }

        protected string CheckEvtInRegion(Event evt)
        {
            double x = evt.Longitude;
            double y = evt.Latitude;
            foreach (KeyValuePair<string, List<ResvRegion>> kvp in resvRegionsDic)
            {
                foreach (ResvRegion res in kvp.Value)
                {
                    if (res.GeoOp.CheckPointInRegion(x, y))
                    {
                        string sRegionAndGrid = kvp.Key + "@^&" + res.RegionName;
                        return sRegionAndGrid;
                    }
                }
            }
            return null;
        }

        protected void SetSN()
        {
            int fileSN = 0;
            foreach (LteURLRegion logFile in resultsRegion)
            {
                logFile.SN = ++fileSN;

                int BroSN = 0;
                foreach (LteURLBroURL logBro in logFile.Bros)
                {
                    logBro.SN = ++BroSN;
                    setEvtSN(logBro.Events);
                }

                int DowSN = 0;
                foreach (LteURLDowURL logDow in logFile.Downs)
                {
                    logDow.SN = ++DowSN;
                    setEvtSN(logDow.Events);
                }

                int VideoSN = 0;
                foreach (LteURLVideoURL logVideo in logFile.Videos)
                {
                    logVideo.SN = ++VideoSN;
                    setEvtSN(logVideo.Events);
                }
            }
        }

        private void setEvtSN(List<LteURLEvent> logEvts)
        {
            int evtSN = 0;
            foreach (LteURLEvent logEvt in logEvts)
            {
                logEvt.SN = ++evtSN;
            }
        }

        public void SetResvRegion(Dictionary<string, List<ResvRegion>> resv)
        {
            this.resvRegionsDic = new Dictionary<string, List<ResvRegion>>();
            this.resvRegionsDic = resv;
        }

        public object GetResult()
        {
            object ret = resultsRegion;
            resultsRegion = null;
            return ret;
        }
    }
}
