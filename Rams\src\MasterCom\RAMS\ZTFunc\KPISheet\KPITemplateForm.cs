﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Func.ExportTestPoint;

namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    public partial class KPITemplateForm : BaseForm
    {
        Dictionary<string, Dictionary<string, TemplateInfo>> testTypeDic;
        KPITemplate curNetTypeTemplate = null;
        KPISubTemplate curSubTemplate = null;
        KPIColumnOptions curColumns = null;

        public KPITemplateForm(Dictionary<string, Dictionary<string, TemplateInfo>> testTypeDic)
            : base()
        {
            InitializeComponent();
            this.testTypeDic = testTypeDic;
            init();
        }

        /// <summary>
        /// 初始化，存在xml配置文件则读取，不存在则创建，默认有三个模版 
        /// </summary>
        private void init()
        {
            gridCtrlNetType.DataSource = null;
            gridCtrlNetType.DataSource = KPITemplateManager.Instance.Templates;
            gridCtrlNetType.RefreshDataSource();

            Dictionary<string, TemplateInfo> templateDic;
            
            
            KPITemplate template = null;

            //如果读取不到对应的xml文件，默认三个网络各保存一份包含所有列的模版，并保存文件
            if (KPITemplateManager.Instance.Templates.Count == 0)
            {
                foreach (string type in testTypeDic.Keys)
                {
                    template = new KPITemplate(type);
                    KPITemplateManager.Instance.Templates.Add(template);
                    testTypeDic.TryGetValue(type, out templateDic);//没判断是否不为空

                    dealTemplateDic(templateDic, template);
                }

                KPITemplateManager.Instance.Save();
            }
            else
            {
                //KPITemplateManager.Instance.Save();
            }

        }

        private static void dealTemplateDic(Dictionary<string, TemplateInfo> templateDic, KPITemplate template)
        {
            foreach (string tempName in templateDic.Keys)
            {
                KPISubTemplate subTemplate = new KPISubTemplate(tempName);
                TemplateInfo templateInfo;
                templateDic.TryGetValue(tempName, out templateInfo);//没判断是否为空

                foreach (string subTempName in templateInfo.subTemplate.Keys)//子模板名
                {
                    KPIColumnOptions column = new KPIColumnOptions();
                    List<string> list;
                    templateInfo.subTemplate.TryGetValue(subTempName, out list);
                    column.subTemplateName = subTempName;
                    foreach (string item in list)
                    {
                        column.ColumnsList.Add(item);
                    }
                    subTemplate.SubTemplateList.Add(column);
                }
                template.AddTemplate(subTemplate);//添加子模板
            }
        }


        /// <summary>
        /// 模版列表赋值
        /// </summary>
        /// <param name="kpiTemplate"></param>
        /// <param name="kpiColumn"></param>
        private void fillTemplateListBox()
        {
            listBox_template.Items.Clear();
            if(curNetTypeTemplate != null)
            {
                foreach (KPISubTemplate subTemp in curNetTypeTemplate.TemplateList)
                {
                    listBox_template.Items.Add(subTemp);
                }
            }
            
            if(listBox_template.Items.Count > 0)
            {
                listBox_template.SelectedIndex = 0;
            }

        }

        /// <summary>
        /// 子模板列表赋值
        /// </summary>
        private void fillSubTemplateListBox()
        {
            listBox_subTemplate.Items.Clear();
            if(curSubTemplate != null)
            {
                foreach (KPIColumnOptions col in curSubTemplate.SubTemplateList)
                {
                    listBox_subTemplate.Items.Add(col);
                }
            }
            
            if (listBox_subTemplate.Items.Count > 0)
            {
                listBox_subTemplate.SelectedIndex = 0;
            }
        }

        /// <summary>
        /// checkListBox列表赋值（模版所有列名）
        /// </summary>
        private void fillCheckListBox()
        {
            chkList_columnItems.Items.Clear();
            if(curColumns != null)
            {
                foreach (string item in curColumns.ColumnsList)
                {
                    chkList_columnItems.Items.Add(item);
                }
            }
            
            chkList_columnItems.CheckAll();
        }

        /// <summary>
        /// 保存修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSave_Click(object sender, EventArgs e)
        {
            #region
            if (curColumns.subTemplateName == txtTemplateName.Text)
            {
                repalceSubTemplateName();
            }
            else
            {
                foreach (KPIColumnOptions item in curSubTemplate.SubTemplateList)
                {
                    if (txtTemplateName.Text == item.subTemplateName)
                    {
                        repalceSubTemplate(item);
                        return;
                    }
                }
                curColumns.subTemplateName = txtTemplateName.Text;
                curColumns.ColumnsList.Clear();
                for (int i = 0; i < chkList_columnItems.Items.Count; i++)
                {
                    if (chkList_columnItems.GetItemCheckState(i) == CheckState.Checked)
                    {
                        curColumns.ColumnsList.Add(chkList_columnItems.GetItem(i).ToString());
                    }
                }
                gridCtrlNetType.RefreshDataSource();
                KPITemplateManager.Instance.Save();
                visualizeCurTemplate();
            }
            #endregion
        }

        private void repalceSubTemplateName()
        {
            if (MessageBox.Show(this, "模版名 " + curColumns.subTemplateName + " 与原模板名相同，确定要覆盖吗？", "确认", MessageBoxButtons.YesNo) == DialogResult.Yes)
            {
                curColumns.subTemplateName = txtTemplateName.Text;
                curColumns.ColumnsList.Clear();
                //获取勾选的列名
                for (int i = 0; i < chkList_columnItems.Items.Count; i++)
                {
                    if (chkList_columnItems.GetItemCheckState(i) == CheckState.Checked)
                    {
                        curColumns.ColumnsList.Add(chkList_columnItems.GetItem(i).ToString());
                    }
                }
                gridCtrlNetType.RefreshDataSource();
                KPITemplateManager.Instance.Save();
                visualizeCurTemplate();
            }
        }

        private void repalceSubTemplate(KPIColumnOptions item)
        {
            if (MessageBox.Show(this, "模版名 " + txtTemplateName.Text + " 已存在，是否覆盖？", "确认", MessageBoxButtons.YesNo) == DialogResult.Yes)
            {
                item.ColumnsList.Clear();
                for (int i = 0; i < chkList_columnItems.Items.Count; i++)
                {
                    if (chkList_columnItems.GetItemCheckState(i) == CheckState.Checked)
                    {
                        item.ColumnsList.Add(chkList_columnItems.GetItem(i).ToString());
                    }
                }
                curSubTemplate.SubTemplateList.Remove(curColumns);//删除原来的模板
                gridCtrlNetType.RefreshDataSource();
                KPITemplateManager.Instance.Save();
                visualizeCurTemplate();
            }
        }

        /// <summary>
        /// 新增模版
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnNewReport_Click(object sender, EventArgs e)
        {
            KPIColumnOptions newTemplate = new KPIColumnOptions(txtTemplateName.Text);
            foreach (KPIColumnOptions item in curSubTemplate.SubTemplateList)
            {
                if (item.subTemplateName == txtTemplateName.Text)
                {
                    MessageBox.Show("该模版名已经存在，请重新修改");
                    return;
                }
            }

            //获取勾选的列名
            for (int i = 0; i < chkList_columnItems.Items.Count; i++)
            {
                if (chkList_columnItems.GetItemCheckState(i) == CheckState.Checked)
                {
                    newTemplate.ColumnsList.Add(chkList_columnItems.GetItem(i).ToString());
                }
            }

            curSubTemplate.SubTemplateList.Add(newTemplate);
            gridCtrlNetType.RefreshDataSource();
            KPITemplateManager.Instance.Save();
            visualizeCurTemplate();
        }

        /// <summary>
        /// 删除模板
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRemoveTemplate_Click(object sender, EventArgs e)
        {
            KPIColumnOptions removeTemplate = listBox_subTemplate.SelectedItem as KPIColumnOptions;

            if (MessageBox.Show(this, "确定要删除 " + removeTemplate.subTemplateName + " 模板？", "确认", MessageBoxButtons.YesNo) == DialogResult.Yes)
            {
                curSubTemplate.SubTemplateList.Remove(removeTemplate);
                gridCtrlNetType.RefreshDataSource();
                KPITemplateManager.Instance.Save();
                visualizeCurTemplate();
            }
        }

        private void gvTmpl_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            curNetTypeTemplate = gvTmpl.GetRow(e.FocusedRowHandle) as KPITemplate;
            visualizeCurTemplate();
        }

        private void visualizeCurTemplate()
        {
            if (curNetTypeTemplate != null)
            {
                fillTemplateListBox();
            }
        }

        /// <summary>
        /// 模版列表
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void listBox_template_SelectedIndexChanged(object sender, EventArgs e)
        {
            curSubTemplate = listBox_template.SelectedItem as KPISubTemplate;
            fillSubTemplateListBox();
        }


        /// <summary>
        /// 子模板列表
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void listBox_subTemplate_SelectedIndexChanged(object sender, EventArgs e)
        {
            curColumns = listBox_subTemplate.SelectedItem as KPIColumnOptions;
            btnStateCheck(listBox_subTemplate);
            if (curColumns != null)
            {
                txtTemplateName.Text = curColumns.subTemplateName;

                //包含所有列的模版不能被修改或者删除（有固定的模版名）,只能从中添加出新的模版，默认模板名格式：XXX默认模板
                if (curColumns.subTemplateName == curSubTemplate.templateName + NetTypeName.getInstance().defaultTemplateName)
                {
                    btn_RemoveTemplate.Enabled = false;
                    btn_Save.Enabled = false;
                }
                else
                {
                    btn_RemoveTemplate.Enabled = true;
                    btn_Save.Enabled = true;
                }
                fillCheckListBox();
            }
        }

        /// <summary>
        /// 列名
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void chkBox_selectAll_CheckedChanged(object sender, EventArgs e)
        {
            if(chkBox_selectAll.CheckState == CheckState.Checked)
            {
                chkList_columnItems.CheckAll();
            }
            else
            {
                chkList_columnItems.UnCheckAll();
            }
        }

        /**
         * 判断选中项位置，改变按钮状态（可用和不可用）
         * */
        private void btnStateCheck(ListBox listBox)
        {
            if (listBox.SelectedIndex == 0)
            {
                btn_Up.Enabled = false;
            }
            else
            {
                btn_Up.Enabled = true;
            }

            if (listBox.SelectedIndex == (listBox.Items.Count - 1))
            {
                btn_Down.Enabled = false;
            }
            else
            {
                btn_Down.Enabled = true;
            }
        }

        private void btn_Up_Click(object sender, EventArgs e)
        {
            int index = listBox_subTemplate.Items.IndexOf(curColumns);
            if(index >= 0 && index < listBox_subTemplate.Items.Count)
            {
                KPIColumnOptions colTemp = listBox_subTemplate.SelectedItem as KPIColumnOptions;
                listBox_subTemplate.Items.Remove(colTemp);
                listBox_subTemplate.Items.Insert(index - 1, colTemp);

                curSubTemplate.SubTemplateList.Remove(colTemp);
                curSubTemplate.SubTemplateList.Insert(index - 1, colTemp);
                listBox_subTemplate.SelectedItem = colTemp;

                btnStateCheck(listBox_subTemplate);
                KPITemplateManager.Instance.Save();
            }
        }

        private void btn_Down_Click(object sender, EventArgs e)
        {
            int index = listBox_subTemplate.Items.IndexOf(curColumns);
            if (index >= 0 && index < listBox_subTemplate.Items.Count)
            {
                KPIColumnOptions colTemp = listBox_subTemplate.SelectedItem as KPIColumnOptions;
                listBox_subTemplate.Items.Remove(colTemp);
                listBox_subTemplate.Items.Insert(index + 1, colTemp);

                curSubTemplate.SubTemplateList.Remove(colTemp);
                curSubTemplate.SubTemplateList.Insert(index + 1, colTemp);
                listBox_subTemplate.SelectedItem = colTemp;

                btnStateCheck(listBox_subTemplate);
                KPITemplateManager.Instance.Save();
            }
        }


    }
}
