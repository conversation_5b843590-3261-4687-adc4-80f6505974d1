﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class MOSFactorSettingForm : BaseForm
    {
        private float mosValue;

        public MOSFactorSettingForm()
        {
            InitializeComponent();
            this.btnOK.Click += delegate(object sender, EventArgs e)
            {
                mosValue = (float)numMos.Value;
                DialogResult = DialogResult.OK;
            };
            this.btnCancel.Click += delegate(object sender, EventArgs e)
            {
                DialogResult = DialogResult.Cancel;
            };
        }

        public float MosValue
        {
            get { return mosValue; }
        }
    }
}
