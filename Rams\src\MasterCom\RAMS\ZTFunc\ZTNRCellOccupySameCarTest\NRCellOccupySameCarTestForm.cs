﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRCellOccupySameCarTestForm : MinCloseForm
    {
        public NRCellOccupySameCarTestForm()
        {
            InitializeComponent();
        }

        private QueryCondition condition;
        private List<FileInfo> lstHost;
        private List<FileInfo> lstGuest;
        private List<NRSameCarTestResult> lstOccupyResults;
        //bool isCheckDlAndUl = true;
        string hostKeyStr = "下载";
        string guestKeyStr = "上传";
        NRCellOccupySameCarTestType type;

        private NRSameCarTestResSummary summaryResult { get; set; }

        public void FillData(QueryCondition cond, List<FileInfo> lstHost, List<FileInfo> lstGuest
            , List<NRSameCarTestResult> lstOccupyResults, NRCellOccupySameCarTestType type)
        {
            this.condition = cond;
            this.lstHost = lstHost;
            this.lstGuest = lstGuest;
            this.lstOccupyResults = lstOccupyResults;
            this.type = type;

            if (type == NRCellOccupySameCarTestType.DlAndUl)
            {
                hostKeyStr = "下载";
                guestKeyStr = "上传";
            }
            else
            {
                hostKeyStr = "VoLTE语音";
                guestKeyStr = "下载";
            }
            foreach (DevExpress.XtraGrid.Columns.GridColumn column in this.gvFileResult.Columns)
            {
                column.Caption = column.Caption.Replace("主端", hostKeyStr).Replace("对端", guestKeyStr);
            }
          
            refreshFiles();
            refreshResults();
        }

        private void refreshFiles()
        {
            List<FileInfo> files = new List<FileInfo>();
            files.AddRange(lstHost);
            files.AddRange(lstGuest);

            gridControlFile.DataSource = files;
            gridControlFile.RefreshDataSource();
        }

        private void refreshResults()
        {
            gridControlResult.DataSource = null;
            List<NRSameCarTestResult> lstResultShow = new List<NRSameCarTestResult>();
            lstResultShow.AddRange(lstOccupyResults);
            setSummaryRes();
            lstResultShow.Add(summaryResult);
            gridControlResult.DataSource = lstResultShow;
            gridControlResult.RefreshDataSource();
        }

        private void setSummaryRes()
        {
            summaryResult = new NRSameCarTestResSummary(type);

            foreach (NRSameCarTestResult result in lstOccupyResults)
            {
                summaryResult.Merge(result);
            }
            summaryResult.Calculate();
        }

        private void simBtnCombine_Click(object sender, EventArgs e)
        {
            int[] hRows = gvFile.GetSelectedRows();
            if (type == NRCellOccupySameCarTestType.DlAndUl)
            {
                if (hRows.Length != 2)
                {
                    MessageBox.Show("请选择一个下载端文件和一个上传端文件...");
                    return;
                }
            }
            else
            {
                if (hRows.Length < 2)
                {
                    MessageBox.Show("请选择一个VoLte语音文件和至少一个下载端文件...");
                    return;
                }
            }

            List<FileInfo> fileSlectList = getFileSlectList(hRows);

            List<FileInfo> fileListHost = ZTLteCellOccupyAnaByFile.GetHasKeyStrFiles(fileSlectList, hostKeyStr);
            List<FileInfo> fileListGuest = ZTLteCellOccupyAnaByFile.GetHasKeyStrFiles(fileSlectList, guestKeyStr);

            if (fileListHost == null || fileListHost.Count <= 0 || fileListGuest == null || fileListGuest.Count <= 0)
            {
                if (type == NRCellOccupySameCarTestType.DlAndUl)
                {
                    MessageBox.Show("请选择一个下载端文件和一个上传端文件...");
                }
                else
                {
                    MessageBox.Show("请选择一个VoLte语音文件和至少一个下载端文件...");
                }
                return;
            }

            if (!isSameCarGroup(fileListHost[0], fileListGuest[0])
                && MessageBox.Show("该文件组不是同车测试，是否继续？", "提示", MessageBoxButtons.OKCancel) != System.Windows.Forms.DialogResult.OK)
            {
                return;
            }

            WaitBox.Show("开始联合分析文件...", analyse, new object[] { fileListHost[0], fileListGuest });

            refreshFiles();
            refreshResults();
        }

        private List<FileInfo> getFileSlectList(int[] hRows)
        {
            List<FileInfo> fileSlectList = new List<FileInfo>();
            foreach (int index in hRows)
            {
                FileInfo fi = gvFile.GetRow(index) as FileInfo;
                if (fi != null)
                {
                    fileSlectList.Add(fi);
                }
            }

            return fileSlectList;
        }

        private bool isSameCarGroup(FileInfo fHost, FileInfo fGuest)
        {
            bool isServiceTypePair;
            if (type == NRCellOccupySameCarTestType.DlAndUl)
            {
                isServiceTypePair = fHost.ServiceType == (int)ServiceType.LTE_TDD_DATA &&
                    fGuest.ServiceType == (int)ServiceType.LTE_TDD_DATA;
            }
            else
            {
                isServiceTypePair = fHost.ServiceType == (int)ServiceType.LTE_TDD_VOLTE &&
                     fGuest.ServiceType == (int)ServiceType.LTE_TDD_DATA;
            }

            return isServiceTypePair && fHost.ProjectID == fGuest.ProjectID &&
                   fHost.CarrierType == (int)CarrierType.ChinaMobile &&
                   fGuest.CarrierType == (int)CarrierType.ChinaMobile &&
                   Math.Abs(fHost.BeginTime - fGuest.BeginTime) < 150 &&
                   Math.Abs(fHost.EndTime - fGuest.EndTime) < 150 &&
                   Math.Abs(fHost.TopLeftLongitude - fGuest.TopLeftLongitude) < 10000 &&
                   Math.Abs(fHost.BottomRightLongitude - fGuest.BottomRightLongitude) < 10000 &&
                   Math.Abs(fHost.TopLeftLatitude - fGuest.TopLeftLatitude) < 9000 &&
                   Math.Abs(fHost.BottomRightLatitude - fGuest.BottomRightLatitude) < 9000 &&
                   fHost.BeginTime < fGuest.EndTime && fHost.EndTime > fGuest.BeginTime;
        }

        private void analyse(object obj)
        {
            try
            {
                object[] objArr = obj as object[];
                FileInfo fileHost = objArr[0] as FileInfo;
                List<FileInfo> fileListGuest = objArr[1] as List<FileInfo>;

                NRSameCarTestHelper occupyAna = new NRSameCarTestHelper(fileHost, fileListGuest, type);
                occupyAna.SetCondition(this.condition);
                occupyAna.Analyse();

                lstOccupyResults.Add(occupyAna.OccupyResult);
                lstHost.Remove(fileHost);
                foreach (FileInfo fi in fileListGuest)
                {
                    lstGuest.Remove(fi);
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void simBtnDeleteFile_Click(object sender, EventArgs e)
        {
            int[] hRows = gvFile.GetSelectedRows();
            if (hRows.Length == 0)
                return;

            foreach (int hRow in hRows)
            {
                FileInfo file = gvFile.GetRow(hRow) as FileInfo;
                if (file == null)
                    return;

                lstHost.Remove(file);
                lstGuest.Remove(file);
            }
            refreshFiles();
        }

        private void simBtnDeleteGroup_Click(object sender, EventArgs e)
        {
            int[] hRows = gvResult.GetSelectedRows();
            if (hRows.Length == 0)
                return;

            foreach (int hRow in hRows)
            {
                NRSameCarTestResult result = gvResult.GetRow(hRows[0]) as NRSameCarTestResult;
                if (result == null || result is NRSameCarTestResSummary)
                    continue;

                lstOccupyResults.Remove(result);
                lstHost.AddRange(result.ResultHost.FileList);
                lstGuest.AddRange(result.ResultGuest.FileList);
            }

            refreshFiles();
            refreshResults();
        }

        private void gvFile_RowStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowStyleEventArgs e)
        {
            if (e.RowHandle % 2 == 0)
            {
                e.Appearance.BackColor = Color.FromArgb(226, 239, 218);
            }
            else
            {
                e.Appearance.BackColor = Color.FromArgb(255, 242, 204);
            }
        }

        private void gvResult_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            if (e.Column == gvResult.Columns[0])
            {
                e.DisplayText = string.Format("{0}", e.RowHandle + 1);
            }
        }



        private List<NPOIRow> getResultRows()
        {
            List<NPOIRow> lstRows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("组号");
            row.AddCellValue("文件ID");
            row.AddCellValue("文件名");
            row.AddCellValue("均空闲且占同一小区(%)");
            row.AddCellValue("均空闲且占不同小区(%)");
            row.AddCellValue(string.Format("{0}空闲、{1}业务态，且占同一小区(%)", hostKeyStr, guestKeyStr));
            row.AddCellValue(string.Format("{0}空闲、{1}业务态，且占不同小区(%)", hostKeyStr, guestKeyStr));
            row.AddCellValue(string.Format("{0}业务态、{1}空闲，且占同一小区(%)", hostKeyStr, guestKeyStr));
            row.AddCellValue(string.Format("{0}业务态、{1}空闲，且占不同小区(%)", hostKeyStr, guestKeyStr));
            row.AddCellValue("均处业务态，且占同一小区(%)");
            row.AddCellValue("均处业务态，且占异频不同小区(%)");
            row.AddCellValue("均处业务态，且占同频不同小区(%)");
            row.AddCellValue("测试时长(s)");
            lstRows.Add(row);

            for (int idx = 0; idx < lstOccupyResults.Count; idx++)
            {
                NRSameCarTestResult result = lstOccupyResults[idx];

                row = new NPOIRow();
                row.AddCellValue(idx + 1);
                row.AddCellValue("");
                row.AddCellValue(result.Name);
                lstRows.Add(row);

                foreach (NRSameCarTestCellResult ocResult in result.OccupyCellResults)
                {
                    row = new NPOIRow();
                    row.AddCellValue("");
                    row.AddCellValue(ocResult.ID);
                    row.AddCellValue(ocResult.Name);
                    row.AddCellValue(ocResult.BothIdle_SameCell_Percent);
                    row.AddCellValue(ocResult.BothIdle_DiffCell_Percent);
                    row.AddCellValue(ocResult.HostIdleGuestOccupy_SameCell_Percent);
                    row.AddCellValue(ocResult.HostIdleGuestOccupy_DiffCell_Percent);
                    row.AddCellValue(ocResult.HostOccupyGuestIdle_SameCell_Percent);
                    row.AddCellValue(ocResult.HostOccupyGuestIdle_DiffCell_Percent);
                    row.AddCellValue(ocResult.SameCellOccupyPercent);
                    row.AddCellValue(ocResult.DiffCellDiFreqOccupyPercent);
                    row.AddCellValue(ocResult.DiffCellCoFreqOccupyPercent);
                    row.AddCellValue(ocResult.TestDuration);

                    lstRows.Add(row);
                }
            }

            row = new NPOIRow();
            row.AddCellValue(lstOccupyResults.Count + 1);
            row.AddCellValue("");
            row.AddCellValue(summaryResult.Name);
            lstRows.Add(row);

            foreach (NRSameCarTestCellResult ocResult in summaryResult.OccupyCellResults)
            {
                row = new NPOIRow();
                row.AddCellValue("");
                row.AddCellValue(ocResult.ID);
                row.AddCellValue(ocResult.Name);
                row.AddCellValue(ocResult.BothIdle_SameCell_Percent);
                row.AddCellValue(ocResult.BothIdle_DiffCell_Percent);
                row.AddCellValue(ocResult.HostIdleGuestOccupy_SameCell_Percent);
                row.AddCellValue(ocResult.HostIdleGuestOccupy_DiffCell_Percent);
                row.AddCellValue(ocResult.HostOccupyGuestIdle_SameCell_Percent);
                row.AddCellValue(ocResult.HostOccupyGuestIdle_DiffCell_Percent);
                row.AddCellValue(ocResult.SameCellOccupyPercent);
                row.AddCellValue(ocResult.DiffCellDiFreqOccupyPercent);
                row.AddCellValue(ocResult.DiffCellCoFreqOccupyPercent);
                row.AddCellValue(ocResult.TestDuration);
                lstRows.Add(row);
            }

            return lstRows;
        }

        #region 鼠标拖选多条记录
        private bool isMouseDown = false;
        private int nStartRow = -1;
        private int nCurRow = -1;
        private void gvFile_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                isMouseDown = true;
            }
        }

        private void gvFile_MouseUp(object sender, MouseEventArgs e)
        {
            isMouseDown = false;
            nStartRow = -1;
            nCurRow = -1;
        }

        private void gvFile_MouseMove(object sender, MouseEventArgs e)
        {
            if (isMouseDown)
            {
                DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo hitInfo = gvFile.CalcHitInfo(e.X, e.Y);
                if (hitInfo.InRow)
                {
                    if (nStartRow == -1)
                    {
                        nStartRow = hitInfo.RowHandle;
                    }
                    else if (hitInfo.RowHandle != nCurRow)
                    {
                        nCurRow = hitInfo.RowHandle;
                        selectRows(nStartRow, nCurRow);
                    }
                }
            }
        }

        private void selectRows(int nStartRow, int nCurRow)
        {
            if (nStartRow > -1 && nCurRow > -1)
            {
                gvFile.BeginSelection();
                gvFile.ClearSelection();
                gvFile.SelectRange(nStartRow, nCurRow);
                gvFile.EndSelection();
            }
        }
        #endregion

        private void tsMenuItemCollapse_Click(object sender, EventArgs e)
        {
            gvResult.CollapseAllDetails();
        }

        private void tsMenuItemExpand_Click(object sender, EventArgs e)
        {
            for (int row = 0; row < gvResult.RowCount; row++)
            {
                gvResult.SetMasterRowExpanded(row, true);
            }
        }

        private void tsMenuItemExportResult_Click(object sender, EventArgs e)
        {
            try
            {
                List<NPOIRow> lstRows = getResultRows();
                ExcelNPOIManager.ExportToExcel(lstRows);
            }
            catch
            {
                MessageBox.Show("导出导Excel...失败！");
            }
        }

        private void tsMenuItemExportFiles_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(gvFile);
            }
            catch
            {
                MessageBox.Show("导出导Excel...失败！");
            }
        }
    }
}
