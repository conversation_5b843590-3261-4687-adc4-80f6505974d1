﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Util;
using MasterCom.RAMS.Compare;
using MasterCom.Util;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    public partial class CPReportFormNew : MinCloseForm
    {
        private MainForm mainForm = null;
        private MapFormCompareHisShowLayer hisLayer;

        private CompareConfig compareConfig = new CompareConfig();
        public CompareConfig CompareConfig
        {
            get { return compareConfig; }
        }
        private Dictionary<int, CPDataType> cpDataTypeDic = null;
        public CPReportFormNew(MainForm mf,MainModel mModel,MapFormCompareHisShowLayer layer)
            :base(mModel)
        {
            this.mainForm = mf;
            this.hisLayer = layer;
            InitializeComponent();
            listView.ListViewItemSorter = new ListViewSorter(listView);
            listViewBetter.ListViewItemSorter = new ListViewSorter(listViewBetter);
            listViewWorse.ListViewItemSorter = new ListViewSorter(listViewWorse);
            initOption();

            compareConfig.loadConfig(Application.StartupPath + @"\config\compareconfig.xml");
            freshCbxCompareMode();
        }

        private void CPReportForm_Load(object sender, EventArgs e)
        {
            //
        }

        public void autoCalData()
        {
            btnFreshVSType_Click(null, null);
        }

        private void tsbSave_Click(object sender, EventArgs e)
        {
            if (compareConfig.saveConfig(Application.StartupPath +　@"\config\compareconfig.xml"))
            {
                MessageBox.Show("保存配置成功", "提示");
                freshCbxCompareMode();
            }
            else { MessageBox.Show("保存配置失败", "提示"); }
      
        }

        private void freshCbxCompareMode()
        {
            cbxCompareMode.Items.Clear();
            List<CompareParamConfig> compareParamConfigList = compareConfig.CompareParamConfigList;
            foreach (CompareParamConfig compareParamConfig in compareParamConfigList)
            {
                cbxCompareMode.Items.Add(compareParamConfig.name);
            }
            if (cbxCompareMode.Items.Count > 0)
            {
                cbxCompareMode.SelectedIndex = 0;
            }
        }

        private void initOption()
        {
            if(hisLayer.ByMethod == 2)
            {
                rdTime.Checked = true;
            }
            else
            {
                rdSum.Checked = true;
            }

            for(int w =1;w<20;w++)
            {
                int span = 80*(w*2+1);
                cbxWindowSize.Items.Add(new IDNamePair(w,span+"×"+span));
            }
            cbxWindowSize.SelectedIndex = 1;//default 2
            for(int a = 10;a<400;a++)
            {
                int area = 6400 * a;
                cbxAreaSize.Items.Add(new IDNamePair(a, "" + area));
            }
            cbxAreaSize.SelectedIndex = 50;
        }
        public void FillDatas(CPGridReportUnit rptUnit)
        {
            this.listView.Items.Clear();
            listView.Items.Add(makeLVI("优势点", rptUnit.betterGrid, rptUnit.totleGrid));
            listView.Items.Add(makeLVI("劣势点", rptUnit.worseGrid, rptUnit.totleGrid));
            listView.Items.Add(makeLVI("相当点", rptUnit.equalGrid, rptUnit.totleGrid));
            listView.Items.Add(makeLVI("同优点", rptUnit.allGoodGrid, rptUnit.totleGrid));
            listView.Items.Add(makeLVI("同差点", rptUnit.allBadGrid, rptUnit.totleGrid));

            int unknown = rptUnit.totleGrid - rptUnit.betterGrid - rptUnit.worseGrid
                          - rptUnit.equalGrid - rptUnit.allGoodGrid - rptUnit.allBadGrid;

            listView.Items.Add(makeLVI("未知点", unknown, rptUnit.totleGrid));
        }
        public void FillDatas(CPPeriodStatUnit rptUnit)
        {
            this.listView.Items.Clear();
            listView.Items.Add(makeLVI("很优点(近期3天以上优势)", rptUnit.veryBetter, rptUnit.totle));
            listView.Items.Add(makeLVI("较优点(最近测试为优势)", rptUnit.better, rptUnit.totle));
            listView.Items.Add(makeLVI("很劣点(近期3天以上劣势)", rptUnit.veryWorse, rptUnit.totle));
            listView.Items.Add(makeLVI("较劣点(最近测试为劣势)", rptUnit.worse, rptUnit.totle));
            listView.Items.Add(makeLVI("其它", rptUnit.other, rptUnit.totle));
        }
        public void FillDataType(Dictionary<int, CPDataType> cpDataTypeDic)
        {
            this.cpDataTypeDic = cpDataTypeDic;
        }

        public IComparer<List<CompUnit>> GetCompareComp()
        {
            if (theComparer == null)
            {
                theComparer = new CompListCompare();
            }
            return theComparer;
        }

        private IComparer<List<CompUnit>> theComparer;

        public class CompListCompare : IComparer<List<CompUnit>>
        {
            public int Compare(List<CompUnit> x, List<CompUnit> y)
            {
                if(x.Count>0 && y.Count>0)
                {
                    int xstatus = x[0].GoodBadStatus;
                    int ystatus = y[0].GoodBadStatus;
                    if(xstatus==ystatus)
                    {
                        return y.Count - x.Count;
                    }
                    else
                    {
                        return xstatus - ystatus;
                    }
                }
                else
                {
                    return 0;
                }
               
            }
        }
        internal void FillComRegions(List<List<CompUnit>> retcompList)
        {
            retcompList.Sort(GetCompareComp());
            this.listViewBetter.Items.Clear();
            this.listViewWorse.Items.Clear();
            foreach(List<CompUnit> comList in retcompList)
            {
                if(comList.Count>0)
                {
                    if(comList[0].GoodBadStatus==1)
                    {
                        this.listViewBetter.Items.Add(makeRegLVI(this.listViewBetter.Items.Count + 1, comList));
                    }
                    else if(comList[0].GoodBadStatus==2)
                    {
                        this.listViewWorse.Items.Add(makeRegLVI(this.listViewWorse.Items.Count + 1, comList));
                    }
                }
            }
        }

        private ListViewItem makeRegLVI(int idx, List<CompUnit> comList)
        {
            ListViewItem lvi = new ListViewItem();
            lvi.Text = ""+idx;
            lvi.Tag = comList;
            lvi.SubItems[0].Tag = idx;
            lvi.SubItems.Add("" + comList.Count*6400);
            lvi.SubItems[1].Tag = comList.Count;

            //添加街道显示
            List<double> lonList = new List<double>();
            List<double> latList = new List<double>();
            foreach (CompUnit cu in comList)
            {
                lonList.Add(cu.MidLongitude);
                latList.Add(cu.MidLatitude);
            }
            string roadItem = GISManager.GetInstance().GetRoadPlaceDesc(lonList, latList);
            lvi.SubItems.Add(roadItem);
            lvi.SubItems[2].Tag = roadItem;

            return lvi;
        }

        private ListViewItem makeLVI(string title,int num,int totle)
        {
            ListViewItem lvi = new ListViewItem();
            lvi.Text = title;
            lvi.SubItems.Add("" + num);
            lvi.SubItems[1].Tag = num;
            lvi.SubItems.Add(getPercentDesc(num, totle));
            lvi.SubItems[2].Tag = getPercentFloat(num, totle);
            return lvi;
        }

        private string getPercentDesc(int sub, int totle)
        {
            if(totle==0)
            {
                return "-";
            }
            else
            {
                return string.Format("{0:F2}%", 100.0*sub / totle);
            }
        }
        private float getPercentFloat(int sub,int totle)
        {
            if(totle==0)
            {
                return 0;
            }
            else
            {
                return (float)sub / totle;
            }
        }

        protected override string ShowImage
        {
            get
            {
                return "images\\cellquery.gif";
            }
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(this.listView);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

        private void listViewBetter_SelectedIndexChanged(object sender, EventArgs e)
        {
            //
        }

        private void fireGotoSelectionRegion(List<CompUnit> compList)
        {
            DbRect gotoRect = new DbRect();
            //数据范围
            bool first = true;
            foreach (CompUnit u in compList)
            {
                if (first)
                {
                    gotoRect.x1 = u.ltlongitude;
                    gotoRect.x2 = u.brlongitude;
                    gotoRect.y1 = u.brlatitude;
                    gotoRect.y2 = u.ltlatitude;
                    first = false;
                }
                else
                {
                    setOtherRect(gotoRect, u);
                }
            }
            MapForm mf = mainForm.GetMapForm();
            if (mf != null)
            {
                DbPoint center = gotoRect.Center();
                mf.GoToView(center.x,center.y,20000);
            }
        }

        private void setOtherRect(DbRect gotoRect, CompUnit u)
        {
            if (gotoRect.x1 > u.ltlongitude)
            {
                gotoRect.x1 = u.ltlongitude;
            }
            if (gotoRect.x2 < u.brlongitude)
            {
                gotoRect.x2 = u.brlongitude;
            }
            if (gotoRect.y1 > u.brlatitude)
            {
                gotoRect.y1 = u.brlatitude;
            }
            if (gotoRect.y2 < u.ltlatitude)
            {
                gotoRect.y2 = u.ltlatitude;
            }
        }

        public void clearList()
        {
            listView.Items.Clear();
            listViewBetter.Items.Clear();
            listViewWorse.Items.Clear();
        }

        private void listViewBetter_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (listViewBetter.SelectedItems.Count > 0)
            {
                List<CompUnit> compList = listViewBetter.SelectedItems[0].Tag as List<CompUnit>;
                if (compList != null)
                {
                    fireGotoSelectionRegion(compList);
                }
            }
        }

        private void listViewWorse_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (listViewWorse.SelectedItems.Count > 0)
            {
                List<CompUnit> compList = listViewWorse.SelectedItems[0].Tag as List<CompUnit>;
                if (compList != null)
                {
                    fireGotoSelectionRegion(compList);
                }
            }
        }

        private void toolStripMenuItem1_Click(object sender, EventArgs e)
        {
            try
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(this.listViewBetter);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

        private void toolStripMenuItem2_Click(object sender, EventArgs e)
        {
            try
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(this.listViewWorse);
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

        private void btnReCombine_Click(object sender, EventArgs e)
        {
            IDNamePair windsz = cbxWindowSize.SelectedItem as IDNamePair;
            IDNamePair areasz = cbxAreaSize.SelectedItem as IDNamePair;
            if(windsz!=null && areasz!=null)
            {
                this.hisLayer.NeedFreshFullImg = true;
                this.hisLayer.SetAutoCombinParam(windsz.id, areasz.id);
                this.hisLayer.doFireApplyAction();
            }
            
        }

        private void btnFreshVSType_Click(object sender, EventArgs e)
        {
            if (cbxCompareMode.SelectedItem != null)
            {
                string paramName = cbxCompareMode.SelectedItem.ToString();
                CompareParamConfig compareParamConfig = null;
                if (compareConfig.compareParamConfigDic.TryGetValue(paramName, out compareParamConfig))
                {
                    this.hisLayer.CarrierPair = getCarrierPair(compareParamConfig);
                    this.hisLayer.ServPair = getServicePair(compareParamConfig);

                    if (rdTime.Checked)
                    {
                        this.hisLayer.ByMethod = 2;
                    }
                    else
                    {
                        this.hisLayer.ByMethod = 1;
                    }
                }
                this.hisLayer.KpiPos = 0;

                MainModel.CompUnitHisData = calData();
                this.hisLayer.NeedFreshFullImg = true;
                this.hisLayer.doFireApplyAction();
            }
            else { MessageBox.Show("请选择竞对模式", "提示"); }
        }

        private void btnColorMng_Click(object sender, EventArgs e)
        {
            CPColorMngDlg dlg = new CPColorMngDlg();
            dlg.FillColorModeList(hisLayer.ColorModes,hisLayer.ByMethod);
            if (dlg.ShowDialog()==DialogResult.OK)
            {
                btnFreshVSType_Click(sender, e);
            } 
        }

        private void tsbEdit_Click(object sender, EventArgs e)
        {
            try
            {
                CompareConfig compareConfigTemp = new CompareConfig();
                compareConfigTemp.Param = compareConfig.Param;

                CompareModeEditForm compareModeEditForm = new CompareModeEditForm();
                compareModeEditForm.FillData(compareConfigTemp, cpDataTypeDic);
                if (DialogResult.OK == compareModeEditForm.ShowDialog())
                {
                    compareConfig = compareModeEditForm.CompareConfig;
                    freshCbxCompareMode();
                }
            }
            catch
            {
            	//continue
            }

        }

        private string getCarrierPair(CompareParamConfig compareParamConfig)
        {
            string carrierA = getCarrierNameByID(compareParamConfig.carrierID_A);
            string carrierB = getCarrierNameByID(compareParamConfig.carrierID_B);
            return carrierA + "2" + carrierB;
        }

        private string getServicePair(CompareParamConfig compareParamConfig)
        {
            string serviceA = getServiceNameByID(compareParamConfig.serviceID_A);
            string serviceB = getServiceNameByID(compareParamConfig.serviceID_B);
            return serviceA + "2" + serviceB;
        }

        private string getCarrierNameByID(int id)
        {
            string carrier = "";
            switch (id)
            {
                case (int)CompareModeEditForm.E_Carrier.移动:
                    {
                        carrier = "Y";
                    }
                    break;
                case (int)CompareModeEditForm.E_Carrier.联通:
                    {
                        carrier = "L";
                    }
                    break;
                case (int)CompareModeEditForm.E_Carrier.电信:
                    {
                        carrier = "D";
                    }
                    break;
                default:
                    break;
            }
            return carrier;
        }

        private string getServiceNameByID(int id)
        {
            string service = "";
            switch (id)
            {
                case (int)CompareModeEditForm.E_Service.GSM:
                    {
                        service = "G";
                    }
                    break;
                case (int)CompareModeEditForm.E_Service.CDMA:
                    {
                        service = "C";
                    }
                    break;
                case (int)CompareModeEditForm.E_Service.TDSCDMA:
                    {
                        service = "T";
                    }
                    break;
                case (int)CompareModeEditForm.E_Service.WCDMA:
                    {
                        service = "W";
                    }
                    break;
                default:
                    break;
            }
            return service;
        }

        /// <summary>
        /// 从新的竞对结果中组合成竞对图层运算所需要的数据
        /// </summary>
        private List<CompUnit> calData()
        {
            List<CompUnit> compUnitList = new List<CompUnit>();
            CompareParamConfig compareParamConfig = null;
            if (cbxCompareMode != null && cbxCompareMode.SelectedItem != null)
            {
                string paramName = cbxCompareMode.SelectedItem.ToString();
                if (!compareConfig.compareParamConfigDic.TryGetValue(paramName, out compareParamConfig))
                {
                    return compUnitList;
                }
            }
            else { return compUnitList; }

            Dictionary<string, CompUnit> compUnitDic = new Dictionary<string, CompUnit>();
            
            int unitID = 1;
            foreach (CPDataInfo cpDataInfo in MainModel.CurCPDataInfoList)
            {
                CompUnit v = null;
                if (!compUnitDic.TryGetValue(cpDataInfo.ltlongitude + "," + cpDataInfo.ltlatitude, out v))
                {
                    v = new CompUnit();
                    v.id = unitID;
                    v.status = 0;
                    v.ltlongitude = cpDataInfo.ltlongitude;
                    v.ltlatitude = cpDataInfo.ltlatitude;
                    v.brlongitude = cpDataInfo.brlongitude;
                    v.brlatitude = cpDataInfo.brlatitude;
                    v.betterdays = 0;
                    v.worsedays = 0;

                    compUnitDic[cpDataInfo.ltlongitude + "," + cpDataInfo.ltlatitude] = v;
                    unitID++;
                }
                DateResult dateResult = new DateResult();
                dateResult.unit_id = v.id;
                dateResult.dateValue = cpDataInfo.date;

                string carrierPair = getCarrierPair(compareParamConfig);
                string servicePair = getServicePair(compareParamConfig);
                dateResult.combineResult = carrierPair + "_" + servicePair + "_" + (int)cpDataInfo.calResult(compareParamConfig);
                v.testDates.Add(dateResult);
            }
            foreach (CompUnit compUnit in compUnitDic.Values)
            {
                compUnitList.Add(compUnit);
            }
            return compUnitList;
        }





    }
   
}