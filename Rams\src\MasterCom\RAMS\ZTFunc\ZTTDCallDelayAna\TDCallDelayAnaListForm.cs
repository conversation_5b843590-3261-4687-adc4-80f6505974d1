using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Grid;
using MasterCom.MControls;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TDCallDelayAnaListForm : MinCloseForm
    {
        public TDCallDelayAnaListForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            mapForm = mainModel.MainForm.GetMapForm();
            init();
        }
        private MapForm mapForm = null;

        private void init()
        {
            this.olvColumnSN.AspectGetter = delegate (object row)
            {
                if (row is FileCallInfo)
                {
                    FileCallInfo item = row as FileCallInfo;
                    return item.SN;
                }
                else if (row is AlertingEvent)
                {
                    AlertingEvent item = row as AlertingEvent;
                    return item.SN;
                }
                return null;
            };

            this.olvColumnMO.AspectGetter = delegate (object row)
            {
                if (row is FileCallInfo)
                {
                    FileCallInfo item = row as FileCallInfo;
                    return item.moFileName;
                }
                else if (row is AlertingEvent)
                {
                    AlertingEvent item = row as AlertingEvent;
                    return item.moNetType;
                }
                return "";
            };

            this.olvColumnMT.AspectGetter = delegate (object row)
            {
                if (row is FileCallInfo)
                {
                    FileCallInfo item = row as FileCallInfo;
                    return item.mtFileName;
                }
                else if (row is AlertingEvent)
                {
                    AlertingEvent item = row as AlertingEvent;
                    return item.mtNetType;
                }
                return "";
            };

            setAlertingEvent();

            this.olvColumnSignalName.AspectGetter = delegate (object row)
            {
                if (row is msgKey)
                {
                    msgKey item = row as msgKey;
                    return item.msgName;
                }
                return "";
            };

            this.olvColumnSignalTime.AspectGetter = delegate (object row)
            {
                if (row is msgKey)
                {
                    msgKey item = row as msgKey;
                    return item.msgTime;
                }
                return "";
            };

            this.ListViewCallDelay.CanExpandGetter = delegate (object x)
            {
                return x is FileCallInfo || x is AlertingEvent;
            };
            this.ListViewCallDelay.ChildrenGetter = delegate (object x)
            {
                if (x is FileCallInfo)
                {
                    FileCallInfo fileCallInfo = x as FileCallInfo;
                    return fileCallInfo.eventList;
                }
                else if (x is AlertingEvent)
                {
                    AlertingEvent eventInfo = x as AlertingEvent;
                    return eventInfo.msgList;
                }
                else
                {
                    return "";
                }
            };
        }

        private void setAlertingEvent()
        {
            this.olvColumnCallAttempt.AspectGetter = delegate (object row)
            {
                if (row is AlertingEvent)
                {
                    AlertingEvent item = row as AlertingEvent;
                    string milliSecond = item.connRequestTime.ToString();
                    milliSecond = milliSecond.Substring(10, milliSecond.Length - 10);
                    return JavaDate.GetDateTimeFromMilliseconds(item.connRequestTime).ToString("yyyy-MM-dd HH:mm:ss") + " " + milliSecond;
                }
                return "";
            };

            this.olvColumnAlerting.AspectGetter = delegate (object row)
            {
                if (row is AlertingEvent)
                {
                    AlertingEvent item = row as AlertingEvent;
                    string milliSecond = item.alertingTime.ToString();
                    milliSecond = milliSecond.Substring(10, milliSecond.Length - 10);
                    return JavaDate.GetDateTimeFromMilliseconds(item.alertingTime).ToString("yyyy-MM-dd HH:mm:ss") + " " + milliSecond;
                }
                return "";
            };

            this.olvColumnSignalName.AspectGetter = delegate (object row)
            {
                if (row is AlertingEvent)
                {
                    AlertingEvent item = row as AlertingEvent;
                    return item.moNetType;
                }
                return "";
            };

            this.olvColumnSignalTime.AspectGetter = delegate (object row)
            {
                if (row is AlertingEvent)
                {
                    AlertingEvent item = row as AlertingEvent;
                    return item.mtNetType;
                }
                return "";
            };

            this.olvColumnDelay.AspectGetter = delegate (object row)
            {
                if (row is AlertingEvent)
                {
                    AlertingEvent item = row as AlertingEvent;
                    return item.alertingTime - item.connRequestTime;
                }
                return "";
            };

            this.olvColumnLongitude.AspectGetter = delegate (object row)
            {
                if (row is AlertingEvent)
                {
                    AlertingEvent item = row as AlertingEvent;
                    return item.alertEvent.Longitude;
                }
                return "";
            };

            this.olvColumnLatitude.AspectGetter = delegate (object row)
            {
                if (row is AlertingEvent)
                {
                    AlertingEvent item = row as AlertingEvent;
                    return item.alertEvent.Latitude;
                }
                return "";
            };
        }

        public void FillData(List<FileCallInfo> resultList)
        {
            ListViewCallDelay.RebuildColumns();
            ListViewCallDelay.ClearObjects();
            ListViewCallDelay.SetObjects(resultList);//(MainModel.TdPoorBlerRoadCovLst);
            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        private void listViewTotal_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            //
        }

        //private void GoToView(List<TestPoint> tpList)
        //{
        //    double ltLong = 100000;
        //    double ltLat = -100000;
        //    double brLong = -100000;
        //    double brLat = 100000;

        //    foreach (TestPoint tp in tpList)
        //    {
        //        if (tp.Longitude < ltLong)
        //        {
        //            ltLong = tp.Longitude;
        //        }
        //        if (tp.Longitude > brLong)
        //        {
        //            brLong = tp.Longitude;
        //        }
        //        if (tp.Latitude < brLat)
        //        {
        //            brLat = tp.Latitude;
        //        }
        //        if (tp.Latitude > ltLat)
        //        {
        //            ltLat = tp.Latitude;
        //        }
        //    }
        //    mapForm.GoToView(new DbRect(ltLong, ltLat, brLong, brLat));
        //}

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            ListViewCallDelay.ExpandAll();
        }

        private void miCallapsAll_Click(object sender, EventArgs e)
        {
            ListViewCallDelay.CollapseAll();
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(ListViewCallDelay);
        }

        private void TDCallDelayAnaListForm_Load(object sender, EventArgs e)
        {
            //
        }
    }
}