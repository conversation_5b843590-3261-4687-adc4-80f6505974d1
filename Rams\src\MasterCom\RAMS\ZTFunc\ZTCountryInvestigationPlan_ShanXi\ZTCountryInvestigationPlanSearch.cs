﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System.Data;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCountryInvestigationPlanSearch : QueryCoverageByRegion
    {
        private readonly Dictionary<string, Dictionary<string, List<CountryInvestigation>>> idTestTypeCountryDic;
        private readonly Dictionary<int, Dictionary<string, Dictionary<string, List<CountryInvestigation>>>> distriIDCountryDic;
        private readonly Dictionary<int, Dictionary<string, List<FileInfo>>> districtIDBoxIDFilesDic;
        private readonly Dictionary<int, DistrictCountryInvestigation> distriCIDic;

        public ZTCountryInvestigationPlanSearch(MainModel mainModel)
            : base(mainModel)
        {
            ColumnCounts = 14;
            ShowRadius = false;
            idTestTypeCountryDic = new Dictionary<string, Dictionary<string, List<CountryInvestigation>>>();
            distriIDCountryDic = new Dictionary<int, Dictionary<string, Dictionary<string, List<CountryInvestigation>>>>();
            districtIDBoxIDFilesDic = new Dictionary<int, Dictionary<string, List<FileInfo>>>();
            distriCIDic = new Dictionary<int, DistrictCountryInvestigation>();
        }

        public override string Name
        {
            get { return "行政村普查"; }
        }

        public override string IconName
        {
            get { return ""; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18018, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void clearData()
        {
            idTestTypeCountryDic.Clear();
            distriIDCountryDic.Clear();
            districtIDBoxIDFilesDic.Clear();
            distriCIDic.Clear();
        }

        protected override void getData(DataTable datatable)
        {
            foreach (DataRow dr in datatable.Rows)
            {
                CountryInvestigation ci = new CountryInvestigation(dr);
                Dictionary<string, List<CountryInvestigation>> testTypeCIDic;
                if (!idTestTypeCountryDic.TryGetValue(ci.BoxNum, out testTypeCIDic))
                {
                    testTypeCIDic = new Dictionary<string, List<CountryInvestigation>>();
                    idTestTypeCountryDic.Add(ci.BoxNum, testTypeCIDic);
                }
                List<CountryInvestigation> ciList;
                if (!testTypeCIDic.TryGetValue(ci.TestType, out ciList))
                {
                    ciList = new List<CountryInvestigation>();
                    testTypeCIDic.Add(ci.TestType, ciList);
                }
                ciList.Add(ci);
            }
        }

        private void initDistrict()
        {
            foreach (int districtID in this.Condition.DistrictIDs)
            {
                distriIDCountryDic[districtID] = new Dictionary<string, Dictionary<string, List<CountryInvestigation>>>(idTestTypeCountryDic);
            }
        }

        protected override void search()
        {
            if (idTestTypeCountryDic.Count <= 0) return;
            initDistrict();
            DIYQueryFileInfo queryFiles = new DIYQueryFileInfo(MainModel);
            queryFiles.SetQueryCondition(this.Condition);
            queryFiles.IsShowFileInfoForm = false;
            queryFiles.Query();

            makeFileGroup();
            analyse();
            getDistrictStructrue();
        }

        private void makeFileGroup()
        {
            foreach (FileInfo fi in MainModel.FileInfos)
            {
                foreach (string boxID in idTestTypeCountryDic.Keys)
                {
                    if (fi.Name.StartsWith(boxID))
                    {
                        Dictionary<string, List<FileInfo>> boxIDFilesDic;
                        if (!districtIDBoxIDFilesDic.TryGetValue(fi.DistrictID, out boxIDFilesDic))
                        {
                            boxIDFilesDic = new Dictionary<string, List<FileInfo>>();
                            districtIDBoxIDFilesDic.Add(fi.DistrictID, boxIDFilesDic);
                        }
                        List<FileInfo> fileList;
                        if (!boxIDFilesDic.TryGetValue(boxID, out fileList))
                        {
                            fileList = new List<FileInfo>();
                            boxIDFilesDic.Add(boxID, fileList);
                        }
                        fileList.Add(fi);
                        break;
                    }
                }
            }
            MainModel.FileInfos.Clear();
        }

        private void analyse()
        {
            int previousDistrictID = this.Condition.DistrictID;
            try
            {
                foreach (int districtID in districtIDBoxIDFilesDic.Keys)
                {
                    this.Condition.DistrictID = districtID;
                    foreach (string boxID in districtIDBoxIDFilesDic[districtID].Keys)
                    {
                        this.Condition.FileInfos.Clear();
                        MainModel.FileInfos.Clear();
                        this.Condition.FileInfos.AddRange(districtIDBoxIDFilesDic[districtID][boxID]);
                        MainModel.FileInfos.AddRange(districtIDBoxIDFilesDic[districtID][boxID]);
                        ZTDIYReplyCountryInvestigation reply = new ZTDIYReplyCountryInvestigation(MainModel,
                            distriIDCountryDic[districtID][boxID]);
                        reply.SetQueryCondition(this.Condition);
                        reply.Query();
                        foreach (List<CountryInvestigation> ciList in distriIDCountryDic[districtID][boxID].Values)
                        {
                            foreach (CountryInvestigation ci in ciList)
                            {
                                ci.GetKABCells();
                            }
                        }
                    }
                }
            }
            finally
            {
                this.Condition.DistrictID = previousDistrictID;
            }
        }

        private void getDistrictStructrue()
        {
            foreach (int district in distriIDCountryDic.Keys)
            {
                DistrictCountryInvestigation disCI;
                if (!distriCIDic.TryGetValue(district, out disCI))
                {
                    disCI = new DistrictCountryInvestigation(district, DistrictManager.GetInstance().getDistrictName(district));
                    distriCIDic.Add(district, disCI);
                }
                foreach (string boxID in distriIDCountryDic[district].Keys)
                {
                    foreach (List<CountryInvestigation> ciList in distriIDCountryDic[district][boxID].Values)
                    {
                        disCI.DistrictCIList.AddRange(ciList);
                    }
                }
            }
        }

        protected override void fireShowForm()
        {
            object obj = MainModel.GetObjectFromBlackboard(typeof(CountryInvestigationPlanInfoForm).FullName);
            CountryInvestigationPlanInfoForm form = obj == null ? 
                new CountryInvestigationPlanInfoForm(MainModel)
                :
                obj as CountryInvestigationPlanInfoForm;
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
            form.FillData(new List<DistrictCountryInvestigation>(distriCIDic.Values));
        }
    }

    public class DistrictCountryInvestigation : IComparable
    {
        public int DistrictID { get; set; }
        public string DistrictName { get; set; }
        public List<CountryInvestigation> DistrictCIList { get; set; }

        public DistrictCountryInvestigation(int districtID, string districtName)
        {
            this.DistrictID = districtID;
            this.DistrictName = districtName;
            DistrictCIList = new List<CountryInvestigation>();
        }

        public int CompareTo(object obj)
        {
            DistrictCountryInvestigation dCI = obj as DistrictCountryInvestigation;
            return this.DistrictID.CompareTo(dCI.DistrictID);
        }
    }

    public class CountryInvestigation : IComparable
    {
        public int SN { get; set; }
        public string CoverNum { get; set; }
        public string Province { get; set; }
        public string City { get; set; }
        public string Country { get; set; }
        public string Town { get; set; }
        public string Village { get; set; }
        public string SceneType { get; set; }
        public string CoverPopulation { get { return ""; } }
        public string BoxNum { get; set; }
        public string TestDate { get; set; }
        public string TestTimeSpan { get; set; }
        public string TestType { get; set; }
        public string TestName { get; set; }
        public string Validated { get; set; }
        public string Desc { get; set; }
        public List<FileInfo> FileInfoList { get; set; } = new List<FileInfo>();

        public DateTime DTStart { get; set; }
        public DateTime DTEnd { get; set; }

        private double minLong = double.MaxValue;
        private double maxLong = double.MinValue;
        private double minLat = double.MaxValue;
        private double maxLat = double.MinValue;
        public string MidLong
        {
            get
            {
                if (gsmYDSampleNum + tdSampleNum + gsmLTSampleNum + wSampleNum + cdmaSampleNum + evdoSampleNum > 0)
                {
                    return ((minLong + maxLong) / 2).ToString();
                }
                return "";
            }
        }
        public string MidLat
        {
            get
            {
                if (gsmYDSampleNum + tdSampleNum + gsmLTSampleNum + wSampleNum + cdmaSampleNum + evdoSampleNum > 0)
                {
                    return ((minLat + maxLat) / 2).ToString();
                }
                return "";
            }
        }

        //移动指标
        private int gsmYDSampleNum = 0;
        private int gsmYD_rxlevMax = int.MinValue;
        private int gsmYD_rxlevMin = int.MaxValue;
        private double gsmYD_rxlevMean = 0;
        private int gsmYD_85 = 0;
        private int gsmYD_90 = 0;
        public string GSMYD_RxlevMax { get { return GetValue(gsmYDSampleNum, gsmYD_rxlevMax, "", false); } }
        public string GSMYD_RxlevMin { get { return GetValue(gsmYDSampleNum, gsmYD_rxlevMin, "", false); } }
        public string GSMYD_RxlevMean { get { return gsmYDSampleNum > 0 ? Math.Round(gsmYD_rxlevMean / gsmYDSampleNum, 2).ToString() : ""; } }
        public string GSMYD_85Coverage { get { return GetValue(gsmYDSampleNum, gsmYD_85, "", true); } }
        public string GSMYD_90Coverage { get { return GetValue(gsmYDSampleNum, gsmYD_90, "", true); } }

        private int tdSampleNum = 0;
        private float td_rscpMax = float.MinValue;
        private float td_rscpMin = float.MaxValue;
        private double td_rscpMean = 0;
        private int td_85 = 0;
        private int td_90 = 0;
        public string TD_RscpMax { get { return GetValue(tdSampleNum, td_rscpMax, "", false); } }
        public string TD_RscpMin { get { return GetValue(tdSampleNum, td_rscpMin, "", false); } }
        public string TD_RScpMean { get { return tdSampleNum > 0 ? Math.Round(td_rscpMean / tdSampleNum, 2).ToString() : ""; } }
        public string TD_85Coverage { get { return GetValue(tdSampleNum, td_85, "", true); } }
        public string TD_90Coverage { get { return GetValue(tdSampleNum, td_90, "", true); } }
        public string TDCoverRage { get { return gsmYDSampleNum + tdSampleNum > 0 ? (tdSampleNum * 1.0 / (gsmYDSampleNum + tdSampleNum)).ToString("p2") : ""; } }

        private readonly Dictionary<string, int> ydCellCoverDic = new Dictionary<string, int>();
        private readonly Dictionary<string, int> ydTDCellCoverDic = new Dictionary<string, int>();

        private string gsmYD_Cell_CGI_1 = null;
        private string gsmYD_Cell_CGI_2 = null;
        private string gsmYD_Cell_CGI_3 = null;
        public string GSMYD_CGI_1 { get { return gsmYD_Cell_CGI_1 == null ? "" : gsmYD_Cell_CGI_1; } }
        public string GSMYD_CGI_1_Rage { get { return gsmYD_Cell_CGI_1 == null ? "" : (ydCellCoverDic[gsmYD_Cell_CGI_1] * 1.0 / gsmYDSampleNum).ToString("p2"); } }
        public string GSMYD_CGI_2 { get { return gsmYD_Cell_CGI_2 == null ? "" : gsmYD_Cell_CGI_2; } }
        public string GSMYD_CGI_2_Rage { get { return gsmYD_Cell_CGI_2 == null ? "" : (ydCellCoverDic[gsmYD_Cell_CGI_2] * 1.0 / gsmYDSampleNum).ToString("p2"); } }
        public string GSMYD_CGI_3 { get { return gsmYD_Cell_CGI_3 == null ? "" : gsmYD_Cell_CGI_3; } }
        public string GSMYD_CGI_3_Rage { get { return gsmYD_Cell_CGI_3 == null ? "" : (ydCellCoverDic[gsmYD_Cell_CGI_3] * 1.0 / gsmYDSampleNum).ToString("p2"); } }

        private string TDYD_TDCell_CGI_1 = null;
        private string TDYD_TDCell_CGI_2 = null;
        private string TDYD_TDCell_CGI_3 = null;
        public string TDYD_CGI_1 { get { return TDYD_TDCell_CGI_1 == null ? "" : TDYD_TDCell_CGI_1; } }
        public string TDYD_CGI_1_Rage { get { return TDYD_TDCell_CGI_1 == null ? "" : (ydTDCellCoverDic[TDYD_TDCell_CGI_1] * 1.0 / tdSampleNum).ToString("p2"); } }
        public string TDYD_CGI_2 { get { return TDYD_TDCell_CGI_2 == null ? "" : TDYD_TDCell_CGI_2; } }
        public string TDYD_CGI_2_Rage { get { return TDYD_TDCell_CGI_2 == null ? "" : (ydTDCellCoverDic[TDYD_TDCell_CGI_2] * 1.0 / tdSampleNum).ToString("p2"); } }
        public string TDYD_CGI_3 { get { return TDYD_TDCell_CGI_3 == null ? "" : TDYD_TDCell_CGI_3; } }
        public string TDYD_CGI_3_Rage { get { return TDYD_TDCell_CGI_3 == null ? "" : (ydTDCellCoverDic[TDYD_TDCell_CGI_3] * 1.0 / tdSampleNum).ToString("p2"); } }

        //联通指标
        private int gsmLTSampleNum = 0;
        private int gsmLT_rxlevMax = int.MinValue;
        private int gsmLT_rxlevMin = int.MaxValue;
        private double gsmLT_rxlevMean = 0;
        private int gsmLT_85 = 0;
        private int gsmLT_90 = 0;
        public string GSMLT_RxlevMax { get { return GetValue(gsmLTSampleNum, gsmLT_rxlevMax, "", false); } }
        public string GSMLT_RxlevMin { get { return GetValue(gsmLTSampleNum, gsmLT_rxlevMin, "", false); } }
        public string GSMLT_RxlevMean { get { return gsmLTSampleNum > 0 ? Math.Round(gsmLT_rxlevMean / gsmLTSampleNum, 2).ToString() : ""; } }
        public string GSMLT_85Coverage { get { return GetValue(gsmLTSampleNum, gsmLT_85, "", true); } }
        public string GSMLT_90Coverage { get { return GetValue(gsmLTSampleNum, gsmLT_90, "", true); } }

        private int wSampleNum = 0;
        private float w_rscpMax = int.MinValue;
        private float w_rscpMin = int.MaxValue;
        private double w_rscpMean = 0;
        private int w_85 = 0;
        private int w_90 = 0;
        public string W_RscpMax { get { return GetValue(wSampleNum, w_rscpMax, "", false); } }
        public string W_RscpMin { get { return GetValue(wSampleNum, w_rscpMin, "", false); } }
        public string W_RscpMean { get { return wSampleNum > 0 ? Math.Round(w_rscpMean / wSampleNum, 2).ToString() : ""; } }
        public string W_85Coverage { get { return GetValue(wSampleNum, w_85, "", true); } }
        public string W_90Coverage { get { return GetValue(wSampleNum, w_90, "", true); } }
        public string WCoverRage { get { return gsmLTSampleNum + wSampleNum > 0 ? (wSampleNum * 1.0 / (gsmLTSampleNum + wSampleNum)).ToString("p2") : ""; } }

        private readonly Dictionary<string, int> ltCellCoverDic = new Dictionary<string, int>();
        private readonly Dictionary<string, int> ltWCellCoverDic = new Dictionary<string, int>();

        private string gsmLT_Cell_CGI_1 = null;
        private string gsmLT_Cell_CGI_2 = null;
        private string gsmLT_Cell_CGI_3 = null;
        public string GSMLT_CGI_1 { get { return gsmLT_Cell_CGI_1 == null ? "" : gsmLT_Cell_CGI_1; } }
        public string GSMLT_CGI_1_Rage { get { return gsmLT_Cell_CGI_1 == null ? "" : (ltCellCoverDic[gsmLT_Cell_CGI_1] * 1.0 / gsmLTSampleNum).ToString("p2"); } }
        public string GSMLT_CGI_2 { get { return gsmLT_Cell_CGI_2 == null ? "" : gsmLT_Cell_CGI_2; } }
        public string GSMLT_CGI_2_Rage { get { return gsmLT_Cell_CGI_2 == null ? "" : (ltCellCoverDic[gsmLT_Cell_CGI_2] * 1.0 / gsmLTSampleNum).ToString("p2"); } }
        public string GSMLT_CGI_3 { get { return gsmLT_Cell_CGI_3 == null ? "" : gsmLT_Cell_CGI_3; } }
        public string GSMLT_CGI_3_Rage { get { return gsmLT_Cell_CGI_3 == null ? "" : (ltCellCoverDic[gsmLT_Cell_CGI_3] * 1.0 / gsmLTSampleNum).ToString("p2"); } }

        private string wLT_WCell_CGI_1 = null;
        private string wLT_WCell_CGI_2 = null;
        private string wLT_WCell_CGI_3 = null;
        public string WLT_CGI_1 { get { return wLT_WCell_CGI_1 == null ? "" : wLT_WCell_CGI_1; } }
        public string WLT_CGI_1_Rage { get { return wLT_WCell_CGI_1 == null ? "" : (ltWCellCoverDic[wLT_WCell_CGI_1] * 1.0 / wSampleNum).ToString("p2"); } }
        public string WLT_CGI_2 { get { return wLT_WCell_CGI_2 == null ? "" : wLT_WCell_CGI_2; } }
        public string WLT_CGI_2_Rage { get { return wLT_WCell_CGI_2 == null ? "" : (ltWCellCoverDic[wLT_WCell_CGI_2] * 1.0 / wSampleNum).ToString("p2"); } }
        public string WLT_CGI_3 { get { return wLT_WCell_CGI_3 == null ? "" : wLT_WCell_CGI_3; } }
        public string WLT_CGI_3_Rage { get { return wLT_WCell_CGI_3 == null ? "" : (ltWCellCoverDic[wLT_WCell_CGI_3] * 1.0 / wSampleNum).ToString("p2"); } }

        //电信指标
        private int cdmaSampleNum = 0;
        private float cdma_rxagcMax = int.MinValue;
        private float cdma_rxagcMin = int.MaxValue;
        private double cdma_rxagcMean = 0;
        private int cdma_85 = 0;
        private int cdma_90 = 0;
        public string CDMA_RxAgc0Max { get { return GetValue(cdmaSampleNum, cdma_rxagcMax, "", false); } }
        public string CDMA_RxAgc0Min { get { return GetValue(cdmaSampleNum, cdma_rxagcMin, "", false); } }
        public string CDMA_RxAgc0Mean { get { return cdmaSampleNum > 0 ? Math.Round(cdma_rxagcMean / cdmaSampleNum, 2).ToString() : ""; } }
        public string CDMA_85Coverage { get { return GetValue(cdmaSampleNum, cdma_85, "", true); } }
        public string CDMA_90Coverage { get { return GetValue(cdmaSampleNum, cdma_90, "", true); } }

        private readonly int evdoSampleNum = 0;
        private readonly float evdo_rxagcMax = int.MinValue;
        private readonly float evdo_rxagcMin = int.MaxValue;
        private readonly double evdo_rxagcMean = 0;
        private readonly int evdo_85 = 0;
        private readonly int evdo_90 = 0;
        public string EVDO_RxAgc0Max { get { return GetValue(evdoSampleNum, evdo_rxagcMax, "", false); } }
        public string EVDO_RxAgc0Min { get { return GetValue(evdoSampleNum, evdo_rxagcMin, "", false); } }
        public string EVDO_RxAgc0Mean { get { return evdoSampleNum > 0 ? Math.Round(evdo_rxagcMean / evdoSampleNum, 2).ToString() : ""; } }
        public string EVDO_85Coverage { get { return GetValue(evdoSampleNum, evdo_85, "", true); } }
        public string EVDO_90Coverage { get { return GetValue(evdoSampleNum, evdo_90, "", true); } }
        public string EVDOCoverRage { get { return cdmaSampleNum + evdoSampleNum > 0 ? (evdoSampleNum * 1.0 / (cdmaSampleNum + evdoSampleNum)).ToString("p2") : ""; } }

        private readonly Dictionary<string, int> dxCellCoverDic = new Dictionary<string, int>();

        private string cdmaDX_CDCell_CGI_1 = null;
        private string cdmaDX_CDCell_CGI_2 = null;
        private string cdmaDX_CDCell_CGI_3 = null;
        public string CDMADX_CGI_1 { get { return cdmaDX_CDCell_CGI_1 == null ? "" : cdmaDX_CDCell_CGI_1; } }
        public string CDMADX_CGI_1_Rage { get { return cdmaDX_CDCell_CGI_1 == null ? "" : (dxCellCoverDic[cdmaDX_CDCell_CGI_1] * 1.0 / cdmaSampleNum).ToString("p2"); } }
        public string CDMADX_CGI_2 { get { return cdmaDX_CDCell_CGI_2 == null ? "" : cdmaDX_CDCell_CGI_2; } }
        public string CDMADX_CGI_2_Rage { get { return cdmaDX_CDCell_CGI_2 == null ? "" : (dxCellCoverDic[cdmaDX_CDCell_CGI_2] * 1.0 / cdmaSampleNum).ToString("p2"); } }
        public string CDMADX_CGI_3 { get { return cdmaDX_CDCell_CGI_3 == null ? "" : cdmaDX_CDCell_CGI_3; } }
        public string CDMADX_CGI_3_Rage { get { return cdmaDX_CDCell_CGI_3 == null ? "" : (dxCellCoverDic[cdmaDX_CDCell_CGI_3] * 1.0 / cdmaSampleNum).ToString("p2"); } }

        private void sortCell(Dictionary<string, int> cellCoverDic, ref string cgi_1, ref string cgi_2, ref string cgi_3)
        {
            if (cellCoverDic.Count <= 0) return;
            string[] cellArray = new string[cellCoverDic.Count];
            int[] numArray = new int[cellCoverDic.Count];
            cellCoverDic.Keys.CopyTo(cellArray, 0);
            cellCoverDic.Values.CopyTo(numArray, 0);
            Array.Sort(numArray, cellArray);
            Array.Reverse(numArray);
            Array.Reverse(cellArray);
            for (int idx = 0; idx < cellCoverDic.Count; idx++)
            {
                switch (idx)
                {
                    case 0:
                        cgi_1 = cellArray[idx];
                        break;
                    case 1:
                        cgi_2 = cellArray[idx];
                        break;
                    case 2:
                        cgi_3 = cellArray[idx];
                        break;
                    default:
                        return;
                }
            }
        }

        private void sortTDCell(Dictionary<string, int> tdCellCoverDic, ref string cgi_1, ref string cgi_2, ref string cgi_3)
        {
            if (tdCellCoverDic.Count <= 0) return;
            string[] cellArray = new string[tdCellCoverDic.Count];
            int[] numArray = new int[tdCellCoverDic.Count];
            tdCellCoverDic.Keys.CopyTo(cellArray, 0);
            tdCellCoverDic.Values.CopyTo(numArray, 0);
            Array.Sort(numArray, cellArray);
            Array.Reverse(numArray);
            Array.Reverse(cellArray);
            for (int idx = 0; idx < tdCellCoverDic.Count; idx++)
            {
                switch (idx)
                {
                    case 0:
                        cgi_1 = cellArray[idx];
                        break;
                    case 1:
                        cgi_2 = cellArray[idx];
                        break;
                    case 2:
                        cgi_3 = cellArray[idx];
                        break;
                    default:
                        return;
                }
            }
        }

        private void sortWCell(Dictionary<string, int> wCellCoverDic, ref string cgi_1, ref string cgi_2, ref string cgi_3)
        {
            if (wCellCoverDic.Count <= 0) return;
            string[] cellArray = new string[wCellCoverDic.Count];
            int[] numArray = new int[wCellCoverDic.Count];
            wCellCoverDic.Keys.CopyTo(cellArray, 0);
            wCellCoverDic.Values.CopyTo(numArray, 0);
            Array.Sort(numArray, cellArray);
            Array.Reverse(numArray);
            Array.Reverse(cellArray);
            for (int idx = 0; idx < wCellCoverDic.Count; idx++)
            {
                switch (idx)
                {
                    case 0:
                        cgi_1 = cellArray[idx];
                        break;
                    case 1:
                        cgi_2 = cellArray[idx];
                        break;
                    case 2:
                        cgi_3 = cellArray[idx];
                        break;
                    default:
                        return;
                }
            }
        }

        private void sortCDCell(Dictionary<string, int> cdCellCoverDic, ref string cgi_1, ref string cgi_2, ref string cgi_3)
        {
            if (cdCellCoverDic.Count <= 0) return;
            string[] cellArray = new string[cdCellCoverDic.Count];
            int[] numArray = new int[cdCellCoverDic.Count];
            cdCellCoverDic.Keys.CopyTo(cellArray, 0);
            cdCellCoverDic.Values.CopyTo(numArray, 0);
            Array.Sort(numArray, cellArray);
            Array.Reverse(numArray);
            Array.Reverse(cellArray);
            for (int idx = 0; idx < cdCellCoverDic.Count; idx++)
            {
                switch (idx)
                {
                    case 0:
                        cgi_1 = cellArray[idx];
                        break;
                    case 1:
                        cgi_2 = cellArray[idx];
                        break;
                    case 2:
                        cgi_3 = cellArray[idx];
                        break;
                    default:
                        return;
                }
            }
        }

        public void GetKABCells()
        {
            sortCell(ydCellCoverDic, ref gsmYD_Cell_CGI_1, ref gsmYD_Cell_CGI_2, ref gsmYD_Cell_CGI_3);
            sortTDCell(ydTDCellCoverDic, ref TDYD_TDCell_CGI_1, ref TDYD_TDCell_CGI_2, ref TDYD_TDCell_CGI_3);

            sortCell(ltCellCoverDic, ref gsmLT_Cell_CGI_1, ref gsmLT_Cell_CGI_2, ref gsmLT_Cell_CGI_3);
            sortWCell(ltWCellCoverDic, ref wLT_WCell_CGI_1, ref wLT_WCell_CGI_2, ref wLT_WCell_CGI_3);

            sortCDCell(dxCellCoverDic, ref cdmaDX_CDCell_CGI_1, ref cdmaDX_CDCell_CGI_2, ref cdmaDX_CDCell_CGI_3);
        }

        public CountryInvestigation(DataRow dr)
        {
            int idx = 0;
            SN = Convert.ToInt32(dr[idx++]);
            CoverNum = Convert.ToString(dr[idx++]);
            Province = Convert.ToString(dr[idx++]);
            City = Convert.ToString(dr[idx++]);
            Country = Convert.ToString(dr[idx++]);
            Town = Convert.ToString(dr[idx++]);
            Village = "";
            SceneType = Convert.ToString(dr[idx++]);
            BoxNum = Convert.ToString(dr[idx++]);
            TestDate = Convert.ToString(dr[idx++]);
            TestTimeSpan = Convert.ToString(dr[idx++]);
            TestType = Convert.ToString(dr[idx++]);
            TestName = Convert.ToString(dr[idx++]);
            Validated = Convert.ToString(dr[idx++]);
            Desc = Convert.ToString(dr[idx]);

            getDate();
            TestType = TestType == "" ? "未知" : TestType;
        }

        private void getDate()
        {
            string[] times = TestTimeSpan.Split('-');
            if(times.Length != 2) return;
            int idx = 0;
            DTStart = Convert.ToDateTime(System.DateTime.Now.Year + "." + TestDate + " " + times[idx++]);
            DTEnd = Convert.ToDateTime(System.DateTime.Now.Year + "." + TestDate + " " + times[idx]);

            int subIdx = Town.IndexOf('-');
            if (subIdx >= 0)
            {
                Village = Town.Substring(subIdx + 1);
                Town = Town.Substring(0, subIdx);
            }
        }

        public string TransDigit(string strValue)
        {
            double digit = 0;
            strValue = strValue.EndsWith("%") ? strValue.Remove(strValue.Length - 1) : strValue;
            if (strValue == "" ||
                !double.TryParse(strValue, out digit))
            {
                return strValue;
            }
            else
            {
                return (digit / 100).ToString();
            }
        }

        private string GetValue(int sampleNum, double value, string strValue, bool bDivide)
        {
            if (sampleNum > 0)
            {
                return bDivide ? Math.Round(value * 100.0 / sampleNum, 2).ToString() : Math.Round(value, 2).ToString();
            }
            return strValue;
        }

        private void statLongLat(TestPoint tp)
        {
            maxLong = tp.Longitude > maxLong ? tp.Longitude : maxLong;
            minLong = tp.Longitude < minLong ? tp.Longitude : minLong;

            maxLat = tp.Latitude > maxLat ? tp.Latitude : maxLat;
            minLat = tp.Latitude < minLat ? tp.Latitude : minLat;
        }

        public void DealTestPoint(TestPoint tp)
        {
            if (tp is TestPointDetail)
            {
                dealTestPointDetail(tp);
            }
            else if (tp is TDTestPointDetail)
            {
                dealTDTestPointDetail(tp);
            }
            else if (tp is WCDMATestPointDetail)
            {
                dealWCDMATestPointDetail(tp);
            }
            else if (tp is CDMATestPointDetail)
            {
                dealCDMATestPointDetail(tp);
            }
        }

        private void dealTestPointDetail(TestPoint tp)
        {
            short? rxlev = (short?)tp["RxLevSub"];
            int? lac = (int?)tp["LAC"];
            int? ci = (int?)tp["CI"];
            if (rxlev == null || lac == null || ci == null ||
                rxlev < -140 || rxlev > -10 ||
                lac <= 0 || ci <= 0)
                return;
            switch (tp.CarrierType)
            {
                case 1://YD
                    setYDData(tp, rxlev, lac, ci);
                    break;
                case 2://LT
                    setLTData(tp, rxlev, lac, ci);
                    break;
                default:
                    break;
            }
        }

        private void setYDData(TestPoint tp, short? rxlev, int? lac, int? ci)
        {
            gsmYDSampleNum++;
            statLongLat(tp);
            gsmYD_rxlevMean += (short)rxlev;
            gsmYD_rxlevMax = rxlev > gsmYD_rxlevMax ? (short)rxlev : gsmYD_rxlevMax;
            gsmYD_rxlevMin = rxlev < gsmYD_rxlevMin ? (short)rxlev : gsmYD_rxlevMin;
            gsmYD_85 = (short)rxlev >= -85 ? gsmYD_85 + 1 : gsmYD_85;
            gsmYD_90 = (short)rxlev >= -85 ? gsmYD_90 + 1 : gsmYD_90;
            statYDCell(lac + "_" + ci);
        }

        private void setLTData(TestPoint tp, short? rxlev, int? lac, int? ci)
        {
            gsmLTSampleNum++;
            statLongLat(tp);
            gsmLT_rxlevMean += (short)rxlev;
            gsmLT_rxlevMax = rxlev > gsmLT_rxlevMax ? (short)rxlev : gsmLT_rxlevMax;
            gsmLT_rxlevMin = rxlev < gsmLT_rxlevMin ? (short)rxlev : gsmLT_rxlevMin;
            gsmLT_85 = (short)rxlev >= -85 ? gsmLT_85 + 1 : gsmLT_85;
            gsmLT_90 = (short)rxlev >= -85 ? gsmLT_90 + 1 : gsmLT_90;
            statLTCell(lac + "_" + ci);
        }

        private void dealTDTestPointDetail(TestPoint tp)
        {
            float? rscp = (float?)tp["TD_PCCPCH_RSCP"];
            int? lac = (int?)tp["TD_SCell_LAC"];
            int? ci = (int?)tp["TD_SCell_CI"];
            if (rscp == null || lac == null || ci == null || rscp < -140 || rscp > -10) return;
            tdSampleNum++;
            statLongLat(tp);
            td_rscpMean += (float)rscp;
            td_rscpMax = rscp > td_rscpMax ? (float)rscp : td_rscpMax;
            td_rscpMin = rscp < td_rscpMin ? (float)rscp : td_rscpMin;
            td_85 = (float)rscp > -85 ? td_85 + 1 : td_85;
            td_90 = (float)rscp > -90 ? td_90 + 1 : td_90;
            statYDTDCell(lac + "_" + ci);
        }

        private void dealWCDMATestPointDetail(TestPoint tp)
        {
            float? rscp = (float?)tp["W_Reference_RSCP"];
            int? lac = (int?)tp["W_SysLAI"];
            int? ci = (int?)tp["W_SysCellID"];
            if (rscp == null || lac == null || ci == null ||
                rscp < -140 || rscp > -10 ||
                lac <= 0 || ci <= 0)
                return;
            wSampleNum++;
            statLongLat(tp);
            w_rscpMean += (float)rscp;
            w_rscpMax = rscp > w_rscpMax ? (float)rscp : w_rscpMax;
            w_rscpMin = rscp < w_rscpMin ? (float)rscp : w_rscpMin;
            w_85 = (float)rscp > -85 ? w_85 + 1 : w_85;
            w_90 = (float)rscp > -90 ? w_90 + 1 : w_90;
            statLTWCell(lac + "_" + ci);
        }

        private void dealCDMATestPointDetail(TestPoint tp)
        {
            float? rxagc = (float?)tp["CD_RxAGC"];
            int? lac = (int?)tp["CD_SID"];
            int? ci = (int?)tp["CD_BID"];
            if (rxagc == null || lac == null || ci == null ||
                rxagc < -140 || rxagc > -10 ||
                lac <= 0 || ci <= 0)
                return;
            cdmaSampleNum++;
            statLongLat(tp);
            cdma_rxagcMean += (float)rxagc;
            cdma_rxagcMax = rxagc > cdma_rxagcMax ? (float)rxagc : cdma_rxagcMax;
            cdma_rxagcMin = rxagc < cdma_rxagcMin ? (float)rxagc : cdma_rxagcMin;
            cdma_85 = (float)rxagc > -85 ? cdma_85 + 1 : cdma_85;
            cdma_90 = (float)rxagc > -90 ? cdma_90 + 1 : cdma_90;
            statDXCDCell(lac + "_" + ci);
        }

        private void statYDCell(string cell)
        {
            if (!ydCellCoverDic.ContainsKey(cell))
            {
                ydCellCoverDic.Add(cell, 0);
            }
            ydCellCoverDic[cell]++;
        }

        private void statYDTDCell(string tdCell)
        {
            if (!ydTDCellCoverDic.ContainsKey(tdCell))
            {
                ydTDCellCoverDic.Add(tdCell, 0);
            }
            ydTDCellCoverDic[tdCell]++;
        }

        private void statLTCell(string cell)
        {
            if (!ltCellCoverDic.ContainsKey(cell))
            {
                ltCellCoverDic.Add(cell, 0);
            }
            ltCellCoverDic[cell]++;
        }

        private void statLTWCell(string wCell)
        {
            if (!ltWCellCoverDic.ContainsKey(wCell))
            {
                ltWCellCoverDic.Add(wCell, 0);
            }
            ltWCellCoverDic[wCell]++;
        }

        private void statDXCDCell(string cdCell)
        {
            if (!dxCellCoverDic.ContainsKey(cdCell))
            {
                dxCellCoverDic.Add(cdCell, 0);
            }
            dxCellCoverDic[cdCell]++;
        }

        public int CompareTo(object obj)
        {
            CountryInvestigation ci = obj as CountryInvestigation;
            return this.SN.CompareTo(ci.SN);
        }
    }
}
