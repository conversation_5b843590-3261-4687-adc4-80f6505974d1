﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.NOP
{
    /// <summary>
    /// 节点
    /// </summary>
    public class NodeEntry
    {
        public override string ToString()
        {
            return this.ExpString;
        }
        //
        public int XPos { get; set; } 
        public int YPos { get; set; } 
        public int Width { get; set; } 
        public int Height { get; set; } 
        //
        public int _xOffset { get; set; } 
        public int _yOffset { get; set; } 
        public bool _breakPoint { get; set; } = false;
        public bool _rootNode { get; set; } = false;
        //
        /// <summary>
        /// 节点描述内容
        /// </summary>
        private string expString = string.Empty;
        public string ExpString
        {
            get { return expString; }
            set
            {
                if (value == null)
                {
                    expString = string.Empty;
                }
                else
                {
                    expString = value;
                }
            }
        }
        public NodeType Type { get; set; } = NodeType.Condition;

        public NodeConnection YesConnection { get; set; }
        public NodeEntry YesNode
        {
            get
            {
                if (YesConnection == null)
                {
                    return null;
                }
                return YesConnection.NextNode;
            }
            set
            {
                if (value == null)
                {
                    this.YesConnection = null;
                }
                else
                {
                    this.YesConnection = new NodeConnection(this, value);
                }
            }
        }
        public NodeConnection NoConnection { get; set; }
        public NodeEntry NoNode
        {
            get
            {
                if (NoConnection == null)
                {
                    return null;
                }
                return NoConnection.NextNode;
            }
            set
            {
                if (value == null)
                {
                    NoConnection = null;
                }
                else
                {
                    this.NoConnection = new NodeConnection(this, value);
                }
            }
        }
        public int _Idx { get; set; }

        private string decideExp = string.Empty;//执行操作，返回结果true or false
        public string DecideExp
        {
            get
            {
                if (decideExp == null)
                {
                    return string.Empty;
                }
                else
                {
                    return decideExp;
                }

            }
            set
            {
                if (value == null)
                {
                    decideExp = string.Empty;
                }
                else
                {
                    decideExp = value;
                }
            }
        }

        public List<NodeEntry> ParentNodes { get; set; } = new List<NodeEntry>();

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["XPos"] = XPos;
                param["YPos"] = YPos;
                param["Width"] = Width;
                param["Height"] = Height;
                param["Type"] = (int)Type;
                param["ExpString"] = expString;
                param["DecideExp"] = decideExp;
                param["_Idx"] = _Idx;
                if (YesNode != null)
                {
                    param["YesNode"] = YesNode.Param;
                    param["YesConnection"] = YesConnection.Param;
                }
                if (NoNode != null)
                {
                    param["NoNode"] = NoNode.Param;
                    param["NoConnection"] = NoConnection.Param;
                }
                return param;
            }
            set
            {
                if (value == null || value.Count == 0)
                {
                    return;
                }
                this.XPos = (int)value["XPos"];
                this.YPos = (int)value["YPos"];
                this.Width = (int)value["Width"];
                this.Height = (int)value["Height"];
                this.Type = (NodeType)((int)value["Type"]);
                this.expString = (string)value["ExpString"];
                this.DecideExp = (string)value["DecideExp"];
                object objIdx;
                if (value.TryGetValue("_Idx", out objIdx))
                {
                    this._Idx = (int)objIdx;
                }
                setYesNode(value);
                setNoNode(value);
            }
        }

        private void setYesNode(Dictionary<string, object> value)
        {
            if (value.ContainsKey("YesNode"))
            {
                NodeEntry wNode = new NodeEntry();
                wNode.Param = (Dictionary<string, object>)value["YesNode"];
                if (wNode._Idx != 0)
                {
                    NodeEntry nex = null;
                    if (ProcRoutine.TempKeyInRoutineDic.TryGetValue(wNode._Idx, out nex))//已经创建过了该节点
                    {
                        wNode = nex;
                    }
                    else
                    {
                        ProcRoutine.TempKeyInRoutineDic[wNode._Idx] = wNode;
                    }

                    wNode.ParentNodes.Add(this);
                    this.YesNode = wNode;
                    if (this.YesConnection != null && value.ContainsKey("YesConnection"))
                    {
                        this.YesConnection.Param = (Dictionary<string, object>)value["YesConnection"];
                    }
                }
            }
        }

        private void setNoNode(Dictionary<string, object> value)
        {
            if (value.ContainsKey("NoNode"))
            {
                NodeEntry wNode = new NodeEntry();
                wNode.Param = (Dictionary<string, object>)value["NoNode"];
                if (wNode._Idx != 0)
                {
                    NodeEntry nex = null;
                    if (ProcRoutine.TempKeyInRoutineDic.TryGetValue(wNode._Idx, out nex))//已经创建过了该节点
                    {
                        wNode = nex;
                    }
                    else
                    {
                        ProcRoutine.TempKeyInRoutineDic[wNode._Idx] = wNode;
                    }
                    wNode.ParentNodes.Add(this);
                    this.NoNode = wNode;
                    if (this.NoConnection != null && value.ContainsKey("NoConnection"))
                    {
                        this.NoConnection.Param = (Dictionary<string, object>)value["NoConnection"];
                    }
                }
            }
        }

        internal Size GetMaxSize()
        {
            Size sz = new Size(XPos + 200, YPos + 200);
            if (YesNode != null)
            {
                Size ySize = YesNode.GetMaxSize();
                if (ySize.Width > sz.Width)
                {
                    sz.Width = ySize.Width;
                }
                if (ySize.Height > sz.Height)
                {
                    sz.Height = ySize.Height;
                }
            }
            if (NoNode != null)
            {
                Size nSize = NoNode.GetMaxSize();
                if (nSize.Width > sz.Width)
                {
                    sz.Width = nSize.Width;
                }
                if (nSize.Height > sz.Height)
                {
                    sz.Height = nSize.Height;
                }
            }
            return sz;
        }
    }
}
