﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Net
{
    public class DIYReplayFileWithinPeriodQueryWithOneIMSI : DIYReplayFileQuery
    {
        public DIYReplayFileWithinPeriodQueryWithOneIMSI(MainModel mainModel)
            : base(mainModel)
        {
            NeedAskDlg = true;
            this.DoWithDTDataEvent += DoWithDTData;
            this.DoWithDTEventEvent += DoWithDTEvent;
        }

        public bool NeedAskDlg
        {
            get;
            set;
        }

        DIYReplayOptionDlg replayOptionDlg = null;
        protected override DIYReplayContentOption getDIYReplayContent()
        {
            if (replayOptionDlg == null)
            {
                replayOptionDlg = new DIYReplayOptionDlg();
                replayOptionDlg.InitLoadInfo("", "隐藏");
            }
            if (Condition.FileInfos.Count > 0)
            {
                return getReplayOption();
            }
            else
            {
                MessageBox.Show("没有可用的配置读取！");
                return null;
            }
        }

        private DIYReplayContentOption getReplayOption()
        {
            int svtype = Condition.FileInfos[0].ServiceType;
            replayOptionDlg.FillCurrentServiceType(svtype);

            if (NeedAskDlg)
            {
                if (replayOptionDlg.ShowDialog() == DialogResult.OK)
                {
                    return replayOptionDlg.GetSelectedReplayOption();
                }
                return null;
            }
            else
            {
                DIYReplayContentOption option = replayOptionDlg.GetCurDeepestOption();
                if (option == null)
                {
                    MessageBox.Show("没有可用的回放配置，请设置回放内容!");
                    if (replayOptionDlg.ShowDialog() == DialogResult.OK)
                    {
                        return replayOptionDlg.GetSelectedReplayOption();
                    }
                    return null;
                }
                else
                {
                    option.EventInclude = true;
                    option.MessageInclude = true;
                    option.MessageL3HexCode = true;
                    return option;
                }
            }
        }

        public override string Name
        {
            get { return "指定时间段文件回放"; }
        }

        public override string IconName
        {
            get { return "Images/replay.gif"; }
        }
        protected override void prepareStatPackage_Sample_SampleFilter(Package package)
        {
            prepareStatPackage(package);
        }
        protected override void prepareStatPackage_Event_EventFilter(Package package)
        {
            prepareStatPackage(package);
        }
        protected override void prepareStatPackage_Message_MessageFilter(Package package)
        {
            prepareStatPackage(package);
        }

        private void prepareStatPackage(Package package)
        {
            package.Content.AddParam((byte)OpOptionDef.iTimeSelect);
            QueryCondition cond = GetQueryCondition();
            TimePeriod tp = cond.Periods[0];
            package.Content.AddParam((int)(JavaDate.GetMilliseconds(tp.BeginTime) / 1000));
            package.Content.AddParam((int)(JavaDate.GetMilliseconds(tp.EndTime) / 1000));
            AddDIYEndOpFlag(package);
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            object imsi = tp["signal_IMSI"];
            return imsi != null && IsContainImsi(imsi.ToString());
        }

        protected override bool isValidEvent(Event e)
        {
            object imsi = e["Value3"];
            return imsi != null && IsContainImsi(imsi.ToString());
        }
        private string curImsiSel = "";
        internal void SetCurrentIMSI(long imsi)
        {
            curImsiSel = imsi.ToString();
        }
        private bool IsContainImsi(string imsi)
        {
            return curImsiSel == imsi;
        }
        private SearchGeometrys curSearchGeo = null;
        internal void setWithinGeometry(SearchGeometrys searchGeometrys)
        {
            this.curSearchGeo = searchGeometrys;
        }
        private void DoWithDTEvent(Event evt)
        {
            if (curSearchGeo != null)
            {
                DbRect rect = curSearchGeo.RegionBounds;
                if (rect == null)
                {
                    return;
                }
                bool ok = false;
                double x = evt.Longitude;
                double y = evt.Latitude;
                ok = curSearchGeo.GeoOp.Contains(x, y);
                int retryCount = 0;
                while (!ok && retryCount < 8)
                {
                    getLongLat(evt.Longitude, evt.Latitude, ref x, ref y, retryCount);
                    ok = curSearchGeo.GeoOp.Contains(x, y);
                    retryCount++;
                }
                if (ok)
                {
                    evt.Longitude = x;
                    evt.Latitude = y;
                }
                else
                {
                    evt.Longitude = 0.5 * (rect.x1 + rect.x2);
                    evt.Latitude = 0.5 * (rect.y1 + rect.y2);
                }
                evt.Tag = curSearchGeo;

                //TestPoints.Add(tp);
            }
        }
        private void DoWithDTData(TestPoint tp)
        {
            if (curSearchGeo != null)
            {
                DbRect rect = curSearchGeo.RegionBounds;
                if(rect==null)
                {
                    return;
                }
                bool ok = false;
                double x = tp.Longitude;
                double y = tp.Latitude;
                ok = curSearchGeo.GeoOp.Contains(x, y);
                int retryCount = 0;
                while (!ok && retryCount < 8)
                {
                    getLongLat(tp.Longitude, tp.Latitude, ref x, ref y, retryCount);
                    ok = curSearchGeo.GeoOp.Contains(x, y);
                    retryCount++;
                }
                if (ok)
                {
                    tp.Longitude = x;
                    tp.Latitude = y;
                }
                else
                {
                    tp.Longitude = 0.5 * (rect.x1 + rect.x2);
                    tp.Latitude = 0.5 * (rect.y1 + rect.y2);
                }
                if(tp is SignalTestPoint)
                {
                    (tp as SignalTestPoint).queryGeometry = curSearchGeo;
                }
                
                //TestPoints.Add(tp);
            }
        }

        private void getLongLat(double longitude, double latitude, ref double x, ref double y, int retryCount)
        {
            if (retryCount == 0)
            {
                x = longitude - CD.ATOM_SPAN_LONG;
                y = latitude - CD.ATOM_SPAN_LAT;
            }
            else if (retryCount == 1)
            {
                x = longitude - CD.ATOM_SPAN_LONG;
                y = latitude + CD.ATOM_SPAN_LAT;
            }
            else if (retryCount == 2)
            {
                x = longitude + CD.ATOM_SPAN_LONG;
                y = latitude - CD.ATOM_SPAN_LAT;
            }
            else if (retryCount == 3)
            {
                x = longitude + CD.ATOM_SPAN_LONG;
                y = latitude + CD.ATOM_SPAN_LAT;
            }
            else if (retryCount == 4)
            {
                x = longitude - 0.5 * CD.ATOM_SPAN_LONG;
                y = latitude - 0.5 * CD.ATOM_SPAN_LAT;
            }
            else if (retryCount == 5)
            {
                x = longitude - 0.5 * CD.ATOM_SPAN_LONG;
                y = latitude + 0.5 * CD.ATOM_SPAN_LAT;
            }
            else if (retryCount == 6)
            {
                x = longitude + 0.5 * CD.ATOM_SPAN_LONG;
                y = latitude - 0.5 * CD.ATOM_SPAN_LAT;
            }
            else if (retryCount == 7)
            {
                x = longitude + 0.5 * CD.ATOM_SPAN_LONG;
                y = latitude + 0.5 * CD.ATOM_SPAN_LAT;
            }
        }
    }
}

