﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class QueryPoorRsrqCell_NB : QueryPoorRsrqCell
    {
        public QueryPoorRsrqCell_NB(ServiceName serviceName)
            : base(serviceName)
        {
        }

        public override string Name
        {
            get { return "低RSRQ路段_NB"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34023, this.Name);
        }
    }
}
