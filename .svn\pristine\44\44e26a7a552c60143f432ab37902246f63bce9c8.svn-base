﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    //DIYAnalyseByFileBackgroundBase
    public class NRScanRoadMultiCoverageByRegion : DIYSampleByRegion
    {
        public Dictionary<string, List<NRScanRoadMultiCoverageInfo>> RegionRoadMultiCoveragePoints { get; set; }
        public NRScanRoadMultiCoverageLayer Layer { get; set; }

        private NRScanRoadMultiCoverageByRegion()
          : base(MainModel.GetInstance())
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NRScan);
        }

        protected static readonly object lockObj = new object();
        private static NRScanRoadMultiCoverageByRegion intance = null;
        public static NRScanRoadMultiCoverageByRegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new NRScanRoadMultiCoverageByRegion();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "道路重叠覆盖度分析_NR扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 36000, 36004, this.Name);//////
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            return NRTpHelper.InitNrScanParamSample(NRTpHelper.NrScanTpManager.RsrpThemeName);
        }

        private ScanMultiCoverageCondition funcCond = null;
        protected override bool getConditionBeforeQuery()
        {
            NRScanRoadMultiCoverageDlg settingDlg = new NRScanRoadMultiCoverageDlg();
            settingDlg.SetCondition(funcCond);
            if (settingDlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            funcCond = settingDlg.GetCondition();
            return true;
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            //MainModel.ClearRoadMultiCoverage_TD(getRegionNames());

            RegionRoadMultiCoveragePoints = new Dictionary<string, List<NRScanRoadMultiCoverageInfo>>();
            var regionList = getRegionNames();
            foreach (string region in regionList)
            {
                RegionRoadMultiCoveragePoints.Add(region, new List<NRScanRoadMultiCoverageInfo>());
            }
            RegionRoadMultiCoveragePoints.Add("全部汇总", new List<NRScanRoadMultiCoverageInfo>());
        }

        private List<string> getRegionNames()
        {
            List<string> names = new List<string>();
            List<ResvRegion> resvRegions = condition.Geometorys.SelectedResvRegions;
            if (resvRegions != null)
            {
                foreach (ResvRegion region in resvRegions)
                {
                    names.Add(region.RegionName);
                }
            }
            else
            {
                names.Add("当前区域");
            }
            return names;
        }

        #region 处理数据
        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                string regionName = getRegionName(tp.Longitude, tp.Latitude);
                if (regionName == null)
                    return;

                //string mainCellName = null;
                float maxValue = float.MinValue;//主强rxlev
                int relativeLevel = 0;
                List<string> relCovCellsName = new List<string>();

                int absoluteLevel = 0;
                List<string> absCovCellsName = new List<string>();

                int relANDabsLevel = 0;
                List<string> relANDabsCovCellsName = new List<string>();

                List<NRCellRsrpInfo> cellrsrps;
                bool invalid = prevDealBandType(tp, out cellrsrps) || cellrsrps.Count <= 0;

                List<NRCellRsrpInfo> lstAbsoluteCell = new List<NRCellRsrpInfo>();
                List<NRCellRsrpInfo> lstRelatedCell = new List<NRCellRsrpInfo>();
                List<NRCellRsrpInfo> lstComprehensiveCell = new List<NRCellRsrpInfo>();
                foreach (NRCellRsrpInfo cell in cellrsrps)
                {
                    if (maxValue == float.MinValue)
                    {
                        maxValue = cell.Rsrp;
                        //mainCellName = cell.StrName;
                    }

                    bool isAbs = judgeIsAbs(ref absoluteLevel, absCovCellsName, lstAbsoluteCell, cell);
                    bool isRel = judgeIsRel(maxValue, ref relativeLevel, relCovCellsName, lstRelatedCell, cell);

                    addResult(ref relANDabsLevel, relANDabsCovCellsName, lstComprehensiveCell, cell, isAbs, isRel);
                }

                //RoadMultiCoverageInfo_TD info1 = new RoadMultiCoverageInfo_TD(tp, maxValue, mainCellName, relativeLevel, relCovCellsName, absoluteLevel, absCovCellsName,
                //    relANDabsLevel, relANDabsCovCellsName, funcCond.SaveTestPoint, invalid);

                NRScanRoadMultiCoverageInfo info = new NRScanRoadMultiCoverageInfo();
                if (funcCond.SaveTestPoint)
                {
                    info.TestPoint = tp;
                }
                info.InvalidatePoint = invalid;
                if (invalid)
                {
                    info.RelativeLevel = -1;
                    info.AbsoluteLevel = -1;
                    info.RelANDAbsLevel = -1;
                }
                else
                {
                    info.RelativeLevel = relativeLevel;
                    info.AbsoluteLevel = absoluteLevel;
                    info.RelANDAbsLevel = relANDabsLevel;
                }
                info.FileName = tp.FileName;
                info.DateTime = tp.DateTime;
                info.Longitude = tp.Longitude;
                info.Latitude = tp.Latitude;
                info.RsrpMax = maxValue;
                //info.MainCellName = mainCellName;
                info.AbsCoverCellsName = absCovCellsName;
                info.RelCoverCellsName = relCovCellsName;
                info.RelANDAbsCoverCellsName = relANDabsCovCellsName;
                info.LstRelatedCell.AddRange(lstAbsoluteCell);
                info.LstAbsoluteCell.AddRange(lstRelatedCell);
                info.LstComprehensive.AddRange(lstComprehensiveCell);
                info.StrRegionName = regionName;

                info.Calculate();
                RegionRoadMultiCoveragePoints[regionName].Add(info);
                RegionRoadMultiCoveragePoints["全部汇总"].Add(info);
                //MainModel.RegionRoadMultiCoveragePoints_TD[regionName].Add(info);
                //MainModel.RegionRoadMultiCoveragePoints_TD["全部汇总"].Add(info);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + Environment.NewLine + ex.Source);
            }
        }

        private string getRegionName(double lng, double lat)
        {
            List<ResvRegion> resvRegions = condition.Geometorys.SelectedResvRegions;
            if (resvRegions != null)
            {
                foreach (ResvRegion region in resvRegions)
                {
                    if (region.GeoOp.CheckPointInRegion(lng, lat))
                    {
                        return region.RegionName;
                    }
                }
            }
            else if (condition.Geometorys.GeoOp.Contains(lng, lat))
            {
                return "当前区域";
            }
            return null;
        }

        private bool prevDealBandType(TestPoint testPoint, out List<NRCellRsrpInfo> cellRsrps)
        {
            cellRsrps = new List<NRCellRsrpInfo>();
            bool invalid = false;
            NRCellRsrpInfo maxCell = null;

            Dictionary<int, int> groupDic = NRTpHelper.NrScanTpManager.GetCellMaxBeam(testPoint);
            foreach (var index in groupDic.Values)
            {
                NRCellRsrpInfo cellRsrp = buildCell(testPoint, index);
                if (cellRsrp != null)
                {
                    bool isValid = addCellRsrps(cellRsrps, ref invalid, ref maxCell, cellRsrp);
                    if (!isValid)
                    {
                        break;
                    }
                }
            }

            //Dictionary<int, bool> groupDic = new Dictionary<int, bool>();
            //for (int index = 0; index < 50; index++) //扫频数据，入库时已按rxlev从大到小排序，无需再排序
            //{
            //    int? group = (int?)testPoint["NRSCAN_GroupNum", index];
            //    if (group == null)
            //    {
            //        break;
            //    }
            //    if (groupDic.ContainsKey((int)group))
            //    {
            //        continue;
            //    }
            //    groupDic.Add(group, true);

            //    NRCellRsrpInfo cellRsrp = buildCell(testPoint, index);
            //    if (cellRsrp != null)
            //    {
            //        bool isValid = addCellRsrps(cellRsrps, ref invalid, ref maxCell, cellRsrp);
            //        if (!isValid)
            //        {
            //            break;
            //        }
            //    }
            //}
            return invalid;
        }

        private NRCellRsrpInfo buildCell(TestPoint testPoint, int index)
        {
            NRCellRsrpInfo cellRsrp = null;
            float? rsrp = NRTpHelper.NrScanTpManager.GetCellRsrp(testPoint, index);
            int? earfcn = (int?)NRTpHelper.NrScanTpManager.GetEARFCN(testPoint, index);
            int? pci = (int?)NRTpHelper.NrScanTpManager.GetPCI(testPoint, index);

            if (rsrp != null && earfcn != null && pci != null)
            {
                NRCell curCell = testPoint.GetCell_NRScan(index);
                cellRsrp = new NRCellRsrpInfo((float)rsrp, (int)earfcn, (int)pci, curCell);
            }
            return cellRsrp;
        }

        private bool addCellRsrps(List<NRCellRsrpInfo> cellRsrps, ref bool invalid, ref NRCellRsrpInfo maxCell, NRCellRsrpInfo cellRsrp)
        {
            if (maxCell == null)
            {
                maxCell = cellRsrp;
                invalid = maxCell.Rsrp < funcCond.ValidValue;
                if (invalid)
                {
                    return false;
                }
            }
            cellRsrps.Add(cellRsrp);
            return true;
        }

        private bool judgeIsAbs(ref int absoluteLevel, List<string> absCovCellsName
            , List<NRCellRsrpInfo> lstAbsoluteCell, NRCellRsrpInfo cellRsrp)
        {
            bool isAbs = false;
            if (cellRsrp.Rsrp > funcCond.AbsoluteValue) //绝对覆盖度
            {
                absoluteLevel++;
                absCovCellsName.Add(cellRsrp.StrName);
                isAbs = true;

                lstAbsoluteCell.Add(cellRsrp);
            }

            return isAbs;
        }

        private bool judgeIsRel(float maxValue, ref int relativeLevel, List<string> relCovCellsName
            , List<NRCellRsrpInfo> lstRelatedCell, NRCellRsrpInfo cellRsrp)
        {
            bool isRel = false;
            if ((decimal)maxValue - (decimal)cellRsrp.Rsrp <= funcCond.CoverBandDiff) //相对覆盖度
            {
                relativeLevel++;
                relCovCellsName.Add(cellRsrp.StrName);
                isRel = true;

                lstRelatedCell.Add(cellRsrp);
            }

            return isRel;
        }

        private void addResult(ref int relANDabsLevel, List<string> relANDabsCovCellsName
            , List<NRCellRsrpInfo> lstComprehensiveCell, NRCellRsrpInfo cellRsrp, bool isAbs, bool isRel)
        {
            if (isAbs && isRel)
            {
                relANDabsLevel++;
                relANDabsCovCellsName.Add(cellRsrp.StrName);
                lstComprehensiveCell.Add(cellRsrp);
            }
        }
        #endregion

        protected override void FireShowFormAfterQuery()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            Layer = mf.GetCustomLayerBase(typeof(NRScanRoadMultiCoverageLayer))
              as NRScanRoadMultiCoverageLayer;
            Layer.Clear();
            mf.MakeSureCustomLayerVisible(Layer, true);

            var fmr = MainModel.CreateResultForm(typeof(NRScanRoadMultiCoverageForm))
                as NRScanRoadMultiCoverageForm;
            fmr.FillData(funcCond, RegionRoadMultiCoveragePoints);
            fmr.Visible = true;
            fmr.BringToFront();
        }
    }
}
