﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ReselectionBehindTimeResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.listView = new BrightIdeasSoftware.TreeListView();
            this.olvID = new BrightIdeasSoftware.OLVColumn();
            this.olvCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvLac = new BrightIdeasSoftware.OLVColumn();
            this.olvCi = new BrightIdeasSoftware.OLVColumn();
            this.olvTestPointCount = new BrightIdeasSoftware.OLVColumn();
            this.olvMeanRxLev = new BrightIdeasSoftware.OLVColumn();
            this.olvMeanRxLevDiff = new BrightIdeasSoftware.OLVColumn();
            this.olvMaxRxLev = new BrightIdeasSoftware.OLVColumn();
            this.olvMinRxLev = new BrightIdeasSoftware.OLVColumn();
            this.olvDuration = new BrightIdeasSoftware.OLVColumn();
            this.olvNCellCount = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.导出ExcelToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.导出TxtToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.展开所有ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.折叠所有节点ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.listView)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // listView
            // 
            this.listView.AllColumns.Add(this.olvID);
            this.listView.AllColumns.Add(this.olvCellName);
            this.listView.AllColumns.Add(this.olvLac);
            this.listView.AllColumns.Add(this.olvCi);
            this.listView.AllColumns.Add(this.olvTestPointCount);
            this.listView.AllColumns.Add(this.olvMeanRxLev);
            this.listView.AllColumns.Add(this.olvMeanRxLevDiff);
            this.listView.AllColumns.Add(this.olvMaxRxLev);
            this.listView.AllColumns.Add(this.olvMinRxLev);
            this.listView.AllColumns.Add(this.olvDuration);
            this.listView.AllColumns.Add(this.olvNCellCount);
            this.listView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvID,
            this.olvCellName,
            this.olvLac,
            this.olvCi,
            this.olvTestPointCount,
            this.olvMeanRxLev,
            this.olvMeanRxLevDiff,
            this.olvMaxRxLev,
            this.olvMinRxLev,
            this.olvDuration,
            this.olvNCellCount});
            this.listView.ContextMenuStrip = this.contextMenuStrip1;
            this.listView.Cursor = System.Windows.Forms.Cursors.Default;
            this.listView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listView.FullRowSelect = true;
            this.listView.GridLines = true;
            this.listView.HeaderWordWrap = true;
            this.listView.IsNeedShowOverlay = false;
            this.listView.Location = new System.Drawing.Point(0, 0);
            this.listView.Name = "listView";
            this.listView.OwnerDraw = true;
            this.listView.ShowGroups = false;
            this.listView.Size = new System.Drawing.Size(1057, 486);
            this.listView.TabIndex = 5;
            this.listView.UseCompatibleStateImageBehavior = false;
            this.listView.View = System.Windows.Forms.View.Details;
            this.listView.VirtualMode = true;
            // 
            // olvID
            // 
            this.olvID.AspectName = "ID";
            this.olvID.HeaderFont = null;
            this.olvID.Text = "序号";
            // 
            // olvCellName
            // 
            this.olvCellName.AspectName = "CellName";
            this.olvCellName.HeaderFont = null;
            this.olvCellName.Text = "小区名";
            this.olvCellName.Width = 100;
            // 
            // olvLac
            // 
            this.olvLac.AspectName = "Lac";
            this.olvLac.HeaderFont = null;
            this.olvLac.Text = "LAC";
            // 
            // olvCi
            // 
            this.olvCi.AspectName = "Ci";
            this.olvCi.HeaderFont = null;
            this.olvCi.Text = "CI";
            // 
            // olvTestPointCount
            // 
            this.olvTestPointCount.AspectName = "TestPointCount";
            this.olvTestPointCount.HeaderFont = null;
            this.olvTestPointCount.Text = "采样点数";
            // 
            // olvMeanRxLev
            // 
            this.olvMeanRxLev.AspectName = "MeanRxLev";
            this.olvMeanRxLev.HeaderFont = null;
            this.olvMeanRxLev.Text = "平均场强";
            this.olvMeanRxLev.Width = 80;
            // 
            // olvMeanRxLevDiff
            // 
            this.olvMeanRxLevDiff.HeaderFont = null;
            this.olvMeanRxLevDiff.Text = "与主服平均场强差";
            this.olvMeanRxLevDiff.Width = 120;
            // 
            // olvMaxRxLev
            // 
            this.olvMaxRxLev.AspectName = "MaxRxLev";
            this.olvMaxRxLev.HeaderFont = null;
            this.olvMaxRxLev.Text = "最大场强";
            this.olvMaxRxLev.Width = 80;
            // 
            // olvMinRxLev
            // 
            this.olvMinRxLev.AspectName = "MinRxLev";
            this.olvMinRxLev.HeaderFont = null;
            this.olvMinRxLev.Text = "最小场强";
            this.olvMinRxLev.Width = 80;
            // 
            // olvDuration
            // 
            this.olvDuration.HeaderFont = null;
            this.olvDuration.Text = "持续时间(秒)";
            this.olvDuration.Width = 80;
            // 
            // olvNCellCount
            // 
            this.olvNCellCount.HeaderFont = null;
            this.olvNCellCount.Text = "邻区个数";
            this.olvNCellCount.Width = 80;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.导出ExcelToolStripMenuItem,
            this.导出TxtToolStripMenuItem,
            this.展开所有ToolStripMenuItem,
            this.折叠所有节点ToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(149, 92);
            // 
            // 导出ExcelToolStripMenuItem
            // 
            this.导出ExcelToolStripMenuItem.Name = "导出ExcelToolStripMenuItem";
            this.导出ExcelToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.导出ExcelToolStripMenuItem.Text = "导出Excel...";
            this.导出ExcelToolStripMenuItem.Click += new System.EventHandler(this.miExportExcelPair_Click);
            // 
            // 导出TxtToolStripMenuItem
            // 
            this.导出TxtToolStripMenuItem.Name = "导出TxtToolStripMenuItem";
            this.导出TxtToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.导出TxtToolStripMenuItem.Text = "导出Txt...";
            this.导出TxtToolStripMenuItem.Click += new System.EventHandler(this.miExportToTxt_Click);
            // 
            // 展开所有ToolStripMenuItem
            // 
            this.展开所有ToolStripMenuItem.Name = "展开所有ToolStripMenuItem";
            this.展开所有ToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.展开所有ToolStripMenuItem.Text = "展开所有节点";
            this.展开所有ToolStripMenuItem.Click += new System.EventHandler(this.ToolStripMenuItemExpand_Click);
            // 
            // 折叠所有节点ToolStripMenuItem
            // 
            this.折叠所有节点ToolStripMenuItem.Name = "折叠所有节点ToolStripMenuItem";
            this.折叠所有节点ToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.折叠所有节点ToolStripMenuItem.Text = "折叠所有节点";
            this.折叠所有节点ToolStripMenuItem.Click += new System.EventHandler(this.ToolStripMenuItemFold_Click);
            // 
            // ReselectionBehindTimeResultForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1057, 486);
            this.Controls.Add(this.listView);
            this.Name = "ReselectionBehindTimeResultForm";
            this.Text = "重选不及时";
            ((System.ComponentModel.ISupportInitialize)(this.listView)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView listView;
        private BrightIdeasSoftware.OLVColumn olvID;
        private BrightIdeasSoftware.OLVColumn olvCellName;
        private BrightIdeasSoftware.OLVColumn olvLac;
        private BrightIdeasSoftware.OLVColumn olvCi;
        private BrightIdeasSoftware.OLVColumn olvTestPointCount;
        private BrightIdeasSoftware.OLVColumn olvMeanRxLev;
        private BrightIdeasSoftware.OLVColumn olvMaxRxLev;
        private BrightIdeasSoftware.OLVColumn olvMinRxLev;
        private BrightIdeasSoftware.OLVColumn olvDuration;
        private BrightIdeasSoftware.OLVColumn olvNCellCount;
        private BrightIdeasSoftware.OLVColumn olvMeanRxLevDiff;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem 导出ExcelToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 导出TxtToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 展开所有ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 折叠所有节点ToolStripMenuItem;

    }
}