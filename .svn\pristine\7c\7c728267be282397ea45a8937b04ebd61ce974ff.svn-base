﻿namespace MasterCom.RAMS
{
    partial class CPGridCellShowForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.cMS_listView = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportLvExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.cMS_LvCells = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShowAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportCellsExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportCellsTxt = new System.Windows.Forms.ToolStripMenuItem();
            this.cMS_lvDatail = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportTxt = new System.Windows.Forms.ToolStripMenuItem();
            this.listItem1 = new MasterCom.Util.UiEx.ListItem();
            this.splitContainerControl3 = new DevExpress.XtraEditors.SplitContainerControl();
            this.panel4 = new System.Windows.Forms.Panel();
            this.panel3 = new System.Windows.Forms.Panel();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.cbxEditRegion = new DevExpress.XtraEditors.ComboBoxEdit();
            this.listView = new System.Windows.Forms.ListView();
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.panel2 = new System.Windows.Forms.Panel();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.lvCells = new BrightIdeasSoftware.TreeListView();
            this.grpPointInfo = new DevExpress.XtraEditors.GroupControl();
            this.lvDetails = new BrightIdeasSoftware.TreeListView();
            this.olvItemName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvGrid = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvCell = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvCellLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvCellLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvCellQualityHost = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvCellQualityGuest = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColHostGuestDiff = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.panel1 = new System.Windows.Forms.Panel();
            this.cbxDisplayMode = new System.Windows.Forms.ComboBox();
            this.label2 = new System.Windows.Forms.Label();
            this.cMS_listView.SuspendLayout();
            this.cMS_LvCells.SuspendLayout();
            this.cMS_lvDatail.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).BeginInit();
            this.splitContainerControl3.SuspendLayout();
            this.panel4.SuspendLayout();
            this.panel3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxEditRegion.Properties)).BeginInit();
            this.panel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvCells)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpPointInfo)).BeginInit();
            this.grpPointInfo.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvDetails)).BeginInit();
            this.panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // cMS_listView
            // 
            this.cMS_listView.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.cMS_listView.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportLvExcel});
            this.cMS_listView.Name = "cMS_listView";
            this.cMS_listView.Size = new System.Drawing.Size(130, 26);
            // 
            // miExportLvExcel
            // 
            this.miExportLvExcel.Name = "miExportLvExcel";
            this.miExportLvExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportLvExcel.Text = "导出Excel";
            this.miExportLvExcel.Click += new System.EventHandler(this.miExportLvExcel_Click);
            // 
            // cMS_LvCells
            // 
            this.cMS_LvCells.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.cMS_LvCells.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShowAll,
            this.miExportCellsExcel,
            this.miExportCellsTxt});
            this.cMS_LvCells.Name = "cMS_LvCells";
            this.cMS_LvCells.Size = new System.Drawing.Size(149, 70);
            // 
            // miShowAll
            // 
            this.miShowAll.Name = "miShowAll";
            this.miShowAll.Size = new System.Drawing.Size(148, 22);
            this.miShowAll.Text = "显示所有小区";
            this.miShowAll.Click += new System.EventHandler(this.miShowAll_Click);
            // 
            // miExportCellsExcel
            // 
            this.miExportCellsExcel.Name = "miExportCellsExcel";
            this.miExportCellsExcel.Size = new System.Drawing.Size(148, 22);
            this.miExportCellsExcel.Text = "导出Excel";
            this.miExportCellsExcel.Click += new System.EventHandler(this.miExportCellsExcel_Click);
            // 
            // miExportCellsTxt
            // 
            this.miExportCellsTxt.Name = "miExportCellsTxt";
            this.miExportCellsTxt.Size = new System.Drawing.Size(148, 22);
            this.miExportCellsTxt.Text = "导出Txt";
            this.miExportCellsTxt.Click += new System.EventHandler(this.miExportCellsTxt_Click);
            // 
            // cMS_lvDatail
            // 
            this.cMS_lvDatail.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.cMS_lvDatail.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.toolStripSeparator1,
            this.miExportExcel,
            this.miExportTxt});
            this.cMS_lvDatail.Name = "contextMenuStrip";
            this.cMS_lvDatail.Size = new System.Drawing.Size(149, 98);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(148, 22);
            this.miExpandAll.Text = "展开所有节点";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(148, 22);
            this.miCollapseAll.Text = "收缩所有节点";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(145, 6);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(148, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // miExportTxt
            // 
            this.miExportTxt.Name = "miExportTxt";
            this.miExportTxt.Size = new System.Drawing.Size(148, 22);
            this.miExportTxt.Text = "导出TXT";
            this.miExportTxt.Click += new System.EventHandler(this.miExportTxt_Click);
            // 
            // listItem1
            // 
            this.listItem1.BackColor = System.Drawing.SystemColors.Window;
            this.listItem1.Font = new System.Drawing.Font("Tahoma", 9F);
            this.listItem1.ForeColor = System.Drawing.SystemColors.WindowText;
            this.listItem1.Text = "listItem1";
            // 
            // splitContainerControl3
            // 
            this.splitContainerControl3.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl3.Horizontal = false;
            this.splitContainerControl3.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl3.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl3.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl3.Name = "splitContainerControl3";
            this.splitContainerControl3.Panel1.Controls.Add(this.panel4);
            this.splitContainerControl3.Panel1.Text = "Panel1";
            this.splitContainerControl3.Panel2.Controls.Add(this.grpPointInfo);
            this.splitContainerControl3.Panel2.Text = "Panel2";
            this.splitContainerControl3.Size = new System.Drawing.Size(1008, 570);
            this.splitContainerControl3.SplitterPosition = 262;
            this.splitContainerControl3.TabIndex = 3;
            this.splitContainerControl3.Text = "splitContainerControl3";
            // 
            // panel4
            // 
            this.panel4.Controls.Add(this.panel3);
            this.panel4.Controls.Add(this.panel2);
            this.panel4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel4.Location = new System.Drawing.Point(0, 0);
            this.panel4.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.panel4.Name = "panel4";
            this.panel4.Size = new System.Drawing.Size(1008, 262);
            this.panel4.TabIndex = 10;
            // 
            // panel3
            // 
            this.panel3.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panel3.Controls.Add(this.groupControl3);
            this.panel3.Location = new System.Drawing.Point(578, 2);
            this.panel3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.panel3.Name = "panel3";
            this.panel3.Size = new System.Drawing.Size(427, 258);
            this.panel3.TabIndex = 9;
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.labelControl1);
            this.groupControl3.Controls.Add(this.cbxEditRegion);
            this.groupControl3.Controls.Add(this.listView);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl3.Location = new System.Drawing.Point(0, 0);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(427, 258);
            this.groupControl3.TabIndex = 0;
            this.groupControl3.Text = "栅格小区整体分析情况";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(409, 3);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(52, 14);
            this.labelControl1.TabIndex = 3;
            this.labelControl1.Text = "当前区域:";
            this.labelControl1.Visible = false;
            // 
            // cbxEditRegion
            // 
            this.cbxEditRegion.Enabled = false;
            this.cbxEditRegion.Location = new System.Drawing.Point(471, 1);
            this.cbxEditRegion.Name = "cbxEditRegion";
            this.cbxEditRegion.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxEditRegion.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxEditRegion.Size = new System.Drawing.Size(39, 21);
            this.cbxEditRegion.TabIndex = 2;
            this.cbxEditRegion.Visible = false;
            // 
            // listView
            // 
            this.listView.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader1,
            this.columnHeader2,
            this.columnHeader3});
            this.listView.ContextMenuStrip = this.cMS_listView;
            this.listView.FullRowSelect = true;
            this.listView.Location = new System.Drawing.Point(2, 24);
            this.listView.Name = "listView";
            this.listView.Size = new System.Drawing.Size(426, 229);
            this.listView.TabIndex = 1;
            this.listView.UseCompatibleStateImageBehavior = false;
            this.listView.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "项";
            this.columnHeader1.Width = 97;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "数量";
            this.columnHeader2.Width = 109;
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "占比";
            this.columnHeader3.Width = 103;
            // 
            // panel2
            // 
            this.panel2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.panel2.Controls.Add(this.groupControl1);
            this.panel2.Location = new System.Drawing.Point(3, 2);
            this.panel2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(571, 258);
            this.panel2.TabIndex = 8;
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.lvCells);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(571, 258);
            this.groupControl1.TabIndex = 1;
            this.groupControl1.Text = "小区分析情况";
            // 
            // lvCells
            // 
            this.lvCells.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lvCells.AutoArrange = false;
            this.lvCells.ContextMenuStrip = this.cMS_LvCells;
            this.lvCells.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvCells.FullRowSelect = true;
            this.lvCells.GridLines = true;
            this.lvCells.HeaderWordWrap = true;
            this.lvCells.IsNeedShowOverlay = false;
            this.lvCells.Location = new System.Drawing.Point(2, 24);
            this.lvCells.Name = "lvCells";
            this.lvCells.OwnerDraw = true;
            this.lvCells.ShowGroups = false;
            this.lvCells.Size = new System.Drawing.Size(568, 229);
            this.lvCells.TabIndex = 7;
            this.lvCells.UseCompatibleStateImageBehavior = false;
            this.lvCells.View = System.Windows.Forms.View.Details;
            this.lvCells.VirtualMode = true;
            // 
            // grpPointInfo
            // 
            this.grpPointInfo.Controls.Add(this.lvDetails);
            this.grpPointInfo.Controls.Add(this.panel1);
            this.grpPointInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.grpPointInfo.Location = new System.Drawing.Point(0, 0);
            this.grpPointInfo.Name = "grpPointInfo";
            this.grpPointInfo.Size = new System.Drawing.Size(1008, 304);
            this.grpPointInfo.TabIndex = 1;
            this.grpPointInfo.Text = "栅格小区详细信息";
            // 
            // lvDetails
            // 
            this.lvDetails.AllColumns.Add(this.olvItemName);
            this.lvDetails.AllColumns.Add(this.olvGrid);
            this.lvDetails.AllColumns.Add(this.olvCell);
            this.lvDetails.AllColumns.Add(this.olvCellLongitude);
            this.lvDetails.AllColumns.Add(this.olvCellLatitude);
            this.lvDetails.AllColumns.Add(this.olvCellQualityHost);
            this.lvDetails.AllColumns.Add(this.olvCellQualityGuest);
            this.lvDetails.AllColumns.Add(this.olvColHostGuestDiff);
            this.lvDetails.AutoArrange = false;
            this.lvDetails.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvItemName,
            this.olvGrid,
            this.olvCell,
            this.olvCellLongitude,
            this.olvCellLatitude,
            this.olvCellQualityHost,
            this.olvCellQualityGuest,
            this.olvColHostGuestDiff});
            this.lvDetails.ContextMenuStrip = this.cMS_lvDatail;
            this.lvDetails.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvDetails.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lvDetails.FullRowSelect = true;
            this.lvDetails.GridLines = true;
            this.lvDetails.HeaderWordWrap = true;
            this.lvDetails.IsNeedShowOverlay = false;
            this.lvDetails.Location = new System.Drawing.Point(2, 61);
            this.lvDetails.Name = "lvDetails";
            this.lvDetails.OwnerDraw = true;
            this.lvDetails.ShowGroups = false;
            this.lvDetails.Size = new System.Drawing.Size(1004, 241);
            this.lvDetails.TabIndex = 6;
            this.lvDetails.UseCompatibleStateImageBehavior = false;
            this.lvDetails.View = System.Windows.Forms.View.Details;
            this.lvDetails.VirtualMode = true;
            // 
            // olvItemName
            // 
            this.olvItemName.HeaderFont = null;
            this.olvItemName.Text = "项";
            this.olvItemName.Width = 84;
            // 
            // olvGrid
            // 
            this.olvGrid.HeaderFont = null;
            this.olvGrid.Text = "小区所在栅格左上角经纬度";
            this.olvGrid.Width = 190;
            // 
            // olvCell
            // 
            this.olvCell.HeaderFont = null;
            this.olvCell.Text = "小区名";
            this.olvCell.Width = 100;
            // 
            // olvCellLongitude
            // 
            this.olvCellLongitude.HeaderFont = null;
            this.olvCellLongitude.Text = "小区经度";
            this.olvCellLongitude.Width = 100;
            // 
            // olvCellLatitude
            // 
            this.olvCellLatitude.HeaderFont = null;
            this.olvCellLatitude.Text = "小区纬度";
            this.olvCellLatitude.Width = 120;
            // 
            // olvCellQualityHost
            // 
            this.olvCellQualityHost.HeaderFont = null;
            this.olvCellQualityHost.Text = "主队信号强度和质量";
            this.olvCellQualityHost.Width = 100;
            // 
            // olvCellQualityGuest
            // 
            this.olvCellQualityGuest.HeaderFont = null;
            this.olvCellQualityGuest.Text = "客队信号强度和质量";
            this.olvCellQualityGuest.Width = 100;
            // 
            // olvColHostGuestDiff
            // 
            this.olvColHostGuestDiff.HeaderFont = null;
            this.olvColHostGuestDiff.Text = "主客差值";
            this.olvColHostGuestDiff.Width = 100;
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.cbxDisplayMode);
            this.panel1.Controls.Add(this.label2);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(2, 23);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1004, 38);
            this.panel1.TabIndex = 17;
            // 
            // cbxDisplayMode
            // 
            this.cbxDisplayMode.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxDisplayMode.FormattingEnabled = true;
            this.cbxDisplayMode.Location = new System.Drawing.Point(74, 9);
            this.cbxDisplayMode.Name = "cbxDisplayMode";
            this.cbxDisplayMode.Size = new System.Drawing.Size(175, 22);
            this.cbxDisplayMode.TabIndex = 15;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(17, 12);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(55, 14);
            this.label2.TabIndex = 14;
            this.label2.Text = "显示项：";
            // 
            // CPGridCellShowForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1008, 570);
            this.Controls.Add(this.splitContainerControl3);
            this.Name = "CPGridCellShowForm";
            this.Text = "竞争对比分析汇总";
            this.cMS_listView.ResumeLayout(false);
            this.cMS_LvCells.ResumeLayout(false);
            this.cMS_lvDatail.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).EndInit();
            this.splitContainerControl3.ResumeLayout(false);
            this.panel4.ResumeLayout(false);
            this.panel3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.groupControl3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxEditRegion.Properties)).EndInit();
            this.panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lvCells)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpPointInfo)).EndInit();
            this.grpPointInfo.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lvDetails)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private MasterCom.Util.UiEx.ListItem listItem1;
        private System.Windows.Forms.ContextMenuStrip cMS_lvDatail;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miExportTxt;
        private System.Windows.Forms.ContextMenuStrip cMS_LvCells;
        private System.Windows.Forms.ToolStripMenuItem miShowAll;
        private System.Windows.Forms.ToolStripMenuItem miExportCellsExcel;
        private System.Windows.Forms.ToolStripMenuItem miExportCellsTxt;
        private System.Windows.Forms.ContextMenuStrip cMS_listView;
        private System.Windows.Forms.ToolStripMenuItem miExportLvExcel;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl3;
        private System.Windows.Forms.Panel panel4;
        private System.Windows.Forms.Panel panel3;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.ComboBoxEdit cbxEditRegion;
        private System.Windows.Forms.ListView listView;
        private System.Windows.Forms.ColumnHeader columnHeader1;
        private System.Windows.Forms.ColumnHeader columnHeader2;
        private System.Windows.Forms.ColumnHeader columnHeader3;
        private System.Windows.Forms.Panel panel2;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private BrightIdeasSoftware.TreeListView lvCells;
        private DevExpress.XtraEditors.GroupControl grpPointInfo;
        private BrightIdeasSoftware.TreeListView lvDetails;
        private BrightIdeasSoftware.OLVColumn olvItemName;
        private BrightIdeasSoftware.OLVColumn olvGrid;
        private BrightIdeasSoftware.OLVColumn olvCell;
        private BrightIdeasSoftware.OLVColumn olvCellLongitude;
        private BrightIdeasSoftware.OLVColumn olvCellLatitude;
        private BrightIdeasSoftware.OLVColumn olvCellQualityHost;
        private BrightIdeasSoftware.OLVColumn olvCellQualityGuest;
        private BrightIdeasSoftware.OLVColumn olvColHostGuestDiff;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.ComboBox cbxDisplayMode;
        private System.Windows.Forms.Label label2;
    }
}