﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage
{
    public class CoverageCondition : ConfigDataInfo
    {
        public int Distance { get; set; } = 50;
        public DataBaseCondition DBCond { get; set; } = new DataBaseCondition();

        public void SetDefalut(CoverageCondition cond)
        {
            Distance = cond.Distance;
        }
    }
}
