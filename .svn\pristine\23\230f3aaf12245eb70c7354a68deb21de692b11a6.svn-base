﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using Microsoft.Office.Interop.Excel;
using System.Reflection;

namespace MasterCom.RAMS.Func
{
    public partial class CellSetBriefForm : MinCloseForm
    {
        public CellSetBriefForm(MainModel mainModel)
            :base(mainModel)
        {
            InitializeComponent();

            DisposeWhenClose = true;

            listViewGenaral.ListViewItemSorter = new ListViewSorter(listViewGenaral);
            listViewCellDetail.ListViewItemSorter = new ListViewSorter(listViewCellDetail);
        }


        Dictionary<string, CellSetGenaral> cellSetGenaralDic;
        List<CellBriefStater> cellBriefStaterList;
        public void fillData(Dictionary<string, CellSetGenaral> cellSetGenaralDic, List<CellBriefStater> cellBriefStaterList)
        {
            this.cellSetGenaralDic = cellSetGenaralDic;
            this.cellBriefStaterList = cellBriefStaterList;

            listViewGenaral.Items.Clear();
            listViewCellDetail.Items.Clear();

            foreach (CellSetGenaral csg in cellSetGenaralDic.Values)
            {
                ListViewItem listViewItem = new System.Windows.Forms.ListViewItem();
                listViewItem.Text = (csg.district).ToString();
                listViewItem.SubItems.Add(csg.carrier);
                listViewItem.SubItems.Add(csg.cellType);
                listViewItem.SubItems.Add(csg.grid);
                listViewItem.SubItems.Add(csg.btsCount.ToString());
                listViewItem.SubItems.Add(csg.cellCount.ToString());
                listViewItem.SubItems.Add(csg.indoorCellCount.ToString());

                listViewGenaral.Items.Add(listViewItem);
            }

            int snD = 1;
            foreach (CellBriefStater cbs in cellBriefStaterList)
            {
                ListViewItem listViewItem = new System.Windows.Forms.ListViewItem();
                listViewItem.Text = (snD++).ToString();
                listViewItem.SubItems.Add(DistrictManager.GetInstance().getDistrictName(cbs.district));
                listViewItem.SubItems.Add(cbs.carrier);
                listViewItem.SubItems.Add(cbs.cellType);
                listViewItem.SubItems.Add(cbs.grid);
                listViewItem.SubItems.Add(cbs.cellName);
                listViewItem.SubItems.Add(cbs.lac.ToString());
                listViewItem.SubItems.Add(cbs.ci.ToString());

                listViewCellDetail.Items.Add(listViewItem);
            }
        }

        private void export(object obj)
        {
            string fileName = (string)obj;
            WaitBox.Text = "正在导出小区集信息…";
            Microsoft.Office.Interop.Excel.Application app = null;
            try
            {
                app = new Microsoft.Office.Interop.Excel.Application();
                app.Visible = false;

                app.UserControl = false;
                Workbooks workbooks = app.Workbooks;
                _Workbook workbook = workbooks.Add(XlWBATemplate.xlWBATWorksheet);
                workbook.Title = "小区集合精简信息";
                app.Worksheets.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value);

                Sheets sheets = workbook.Worksheets;

                _Worksheet worksheet = (_Worksheet)sheets.get_Item(1);
                if (worksheet == null)
                {
                    throw (new Exception("ERROR: worksheet == null"));
                }
                worksheet.Name = " 小区集概况";
                //==== 列标题
                int idx = 1;
                makeTitle(worksheet, 1, idx++, "城市", 10);
                makeTitle(worksheet, 1, idx++, "运营商", 10);
                makeTitle(worksheet, 1, idx++, "小区类型", 10);
                makeTitle(worksheet, 1, idx++, "所属网格", 10);
                makeTitle(worksheet, 1, idx++, "基站数目", 10);
                makeTitle(worksheet, 1, idx++, "小区数目", 10);
                makeTitle(worksheet, 1, idx, "室内小区数目", 10);
                
                int rowAt = 2;
                foreach (CellSetGenaral csg in cellSetGenaralDic.Values)
                {
                    int xx = 1;
                    makeItemRow(worksheet, rowAt, xx++, csg.district);
                    makeItemRow(worksheet, rowAt, xx++, csg.carrier);
                    makeItemRow(worksheet, rowAt, xx++, csg.cellType);
                    makeItemRow(worksheet, rowAt, xx++, csg.grid);
                    makeItemRow(worksheet, rowAt, xx++, csg.btsCount.ToString());
                    makeItemRow(worksheet, rowAt, xx++, csg.cellCount.ToString());
                    makeItemRow(worksheet, rowAt, xx, csg.indoorCellCount.ToString());

                    rowAt++;
                }

                _Worksheet worksheet2 = (_Worksheet)sheets.get_Item(2);
                if (worksheet2 == null)
                {
                    throw (new Exception("ERROR: worksheet == null"));
                }
                worksheet2.Name = " 小区信息";
                //==== 列标题
                int idx_s2 = 1;
                makeTitle(worksheet2, 1, idx_s2++, "序号", 10);
                makeTitle(worksheet2, 1, idx_s2++, "城市", 10);
                makeTitle(worksheet2, 1, idx_s2++, "运营商", 10);
                makeTitle(worksheet2, 1, idx_s2++, "小区类型", 10);
                makeTitle(worksheet2, 1, idx_s2++, "所属网格", 10);
                makeTitle(worksheet2, 1, idx_s2++, "小区名称", 30);
                makeTitle(worksheet2, 1, idx_s2++, "LAC", 10);
                makeTitle(worksheet2, 1, idx_s2, "CI", 10);
                
                int rowAt_s2 = 2;
                int sn = 1;
                foreach(CellBriefStater cbf in cellBriefStaterList)
                {
                    int xx = 1;
                    WaitBox.ProgressPercent = (int)(100.0 * rowAt_s2 / cellBriefStaterList.Count);
                    makeItemRow(worksheet2, rowAt_s2, xx++, (sn++).ToString());
                    makeItemRow(worksheet2, rowAt_s2, xx++, DistrictManager.GetInstance().getDistrictName(cbf.district));
                    makeItemRow(worksheet2, rowAt_s2, xx++, cbf.carrier);
                    makeItemRow(worksheet2, rowAt_s2, xx++, cbf.cellType);
                    makeItemRow(worksheet2, rowAt_s2, xx++, cbf.grid);
                    makeItemRow(worksheet2, rowAt_s2, xx++, cbf.cellName);
                    makeItemRow(worksheet2, rowAt_s2, xx++, cbf.lac.ToString());
                    makeItemRow(worksheet2, rowAt_s2, xx, cbf.ci.ToString());

                    rowAt_s2++;
                }
                workbook.SaveAs(fileName);
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(this, "导出 Excel 出错：" + ex.Message);
            }
            finally
            {
                if (app != null)
                {
                    app.Quit();
                }
                WaitBox.Close();
            }
        }

        private void miExportToExcel_Click(object sender, EventArgs e)
        {
            try
            {
                ExportResultSecurityHelper.ExportToExcel(export, ExportResultSecurityHelper.ObjFileName, true);
            }
            catch
            {
                //continue
            }
        }

        private void makeTitle(_Worksheet worksheet, int row, int col, string title, int width)
        {
            Range range = worksheet.Cells[row, col] as Range;
            range.Value2 = title;
            range.Font.Bold = true;
            range.ColumnWidth = width;
        }
        private void makeItemRow(_Worksheet worksheet, int row, int column, string str)
        {
            Range range = worksheet.Cells[row, column] as Range;
            range.Value2 = str;
        }

    }
}
