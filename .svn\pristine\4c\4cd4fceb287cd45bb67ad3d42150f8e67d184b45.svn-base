﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.VoLTEDropCallCause
{
    public class DropCallCondition
    {
        public int WeakRsrpSec { get; set; }
        public int PoorSinrSec { get; set; }

        public float WeakRsrp { get; set; }

        public int PoorSinr { get; set; }

        public int HoNum { get; set; }

        public int HoSec { get; set; }

        public int MultiSec { get; set; }

        public int MultiBand { get; set; }

        public float MultiPer { get; set; }

        public int MultiValue { get; set; }

        public DropCallCondition()
        {
            CauseSet = new List<DropCallCauseBase>();
            CauseSet.Add(new VoiceHangupCause());
            CauseSet.Add(new TwoBYECause());
            CauseSet.Add(new SIPBYERequestTerminated());
            CauseSet.Add(new ReleasEPSBeforeOKCause());
        }
        public List<DropCallCauseBase> CauseSet
        {
            get;
            set;
        }
    }


}
