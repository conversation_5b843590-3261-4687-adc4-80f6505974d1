﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.CheckCellOccupation;
using MasterCom.RAMS.ZTFunc.ZTNRCheckCellOccupation;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRCheckCellOccupationForm : MinCloseForm
    {
        public NRCheckCellOccupationForm()
        {
            InitializeComponent();

            ToolStripMenuItem item = new ToolStripMenuItem("导出到Excel");
            item.Click += item_Click;
            contextMenuStrip1.Items.Add(item);
            this.gridControlResult.ContextMenuStrip = contextMenuStrip1;
            this.gridControlResult.MouseDoubleClick += gridControlResult_MouseDoubleClick;
        }

        void gridControlResult_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (this.gv.SelectedRowsCount <= 0)
            {
                return;
            }
            NRCheckCellOccupationResult item = this.gv.GetRow(this.gv.GetSelectedRows()[0]) as NRCheckCellOccupationResult;
            if (item == null)
            {
                return;
            }
            MainModel.GetInstance().DTDataManager.Clear();
            foreach (TestPoint tp in item.cellData.listValidTp)
            {
                MainModel.GetInstance().DTDataManager.Add(tp);
            }
            //画平均经纬度点和相关连线
            MapForm mf = MainModel.GetInstance().MainForm.GetMapForm();
            //获取图层，是的该类型的图层实例被生成并存储在系统的图层队列中，重绘时即会生效
            CheckCellOccupationLayer layer = mf.GetLayerBase(typeof(CheckCellOccupationLayer)) as CheckCellOccupationLayer;
            layer.SetData(item.cellData);
            MainModel.SetSelectedNRCell(item.cellData.mainCell);
            MainModel.SelectedNRCells.Clear();
            MainModel.SelectedNRCells.Add(item.cellData.mainCell);
            MainModel.GetInstance().FireDTDataChanged(this);
            MainModel.FireSetDefaultMapSerialTheme("NR_SS_RSRP");
        }

        void item_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(gv);
        }

        public void FillData(List<NRCheckCellOccupationResult> listResult)
        {
            this.gridControlResult.DataSource = listResult;
            this.gridControlResult.Refresh();
        }
    }
}
