﻿namespace MasterCom.RAMS.ZTFunc.ZTNrNBCellCheckAna
{
    partial class NBCellCheckAnaForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTLteNBCellCheckBothAnaListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewNBCheckStat = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatNBCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellCellID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnEnodeBID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSectorID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellRSRP = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatScore = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatOrder = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatSource = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatStatusByMsg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatStatusByParam = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatAdvice = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewNBCheckStat)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 76);
            this.ctxMenu.Opening += new System.ComponentModel.CancelEventHandler(this.ctxMenu_Opening);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(126, 6);
            this.toolStripMenuItem1.Visible = false;
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(129, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // ListViewNBCheckStat
            // 
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatSN);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatCellName);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatNBCount);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatCellTAC);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatCellCellID);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnNCI);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnEnodeBID);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnSectorID);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatCellARFCN);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatCellPCI);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatSampleCount);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatCellRSRP);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatScore);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatOrder);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatSource);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatStatusByMsg);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatStatusByParam);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatAdvice);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatDistance);
            this.ListViewNBCheckStat.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.ListViewNBCheckStat.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnStatCellName,
            this.olvColumnStatNBCount,
            this.olvColumnStatCellTAC,
            this.olvColumnStatCellCellID,
            this.olvColumnNCI,
            this.olvColumnEnodeBID,
            this.olvColumnSectorID,
            this.olvColumnStatCellARFCN,
            this.olvColumnStatCellPCI,
            this.olvColumnStatSampleCount,
            this.olvColumnStatCellRSRP,
            this.olvColumnStatScore,
            this.olvColumnStatOrder,
            this.olvColumnStatSource,
            this.olvColumnStatStatusByMsg,
            this.olvColumnStatStatusByParam,
            this.olvColumnStatAdvice,
            this.olvColumnStatDistance});
            this.ListViewNBCheckStat.ContextMenuStrip = this.ctxMenu;
            this.ListViewNBCheckStat.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewNBCheckStat.FullRowSelect = true;
            this.ListViewNBCheckStat.GridLines = true;
            this.ListViewNBCheckStat.HeaderWordWrap = true;
            this.ListViewNBCheckStat.IsNeedShowOverlay = false;
            this.ListViewNBCheckStat.Location = new System.Drawing.Point(1, 1);
            this.ListViewNBCheckStat.Name = "ListViewNBCheckStat";
            this.ListViewNBCheckStat.OwnerDraw = true;
            this.ListViewNBCheckStat.ShowGroups = false;
            this.ListViewNBCheckStat.Size = new System.Drawing.Size(1248, 560);
            this.ListViewNBCheckStat.TabIndex = 7;
            this.ListViewNBCheckStat.UseCompatibleStateImageBehavior = false;
            this.ListViewNBCheckStat.View = System.Windows.Forms.View.Details;
            this.ListViewNBCheckStat.VirtualMode = true;
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            // 
            // olvColumnStatCellName
            // 
            this.olvColumnStatCellName.HeaderFont = null;
            this.olvColumnStatCellName.Text = "小区名称";
            this.olvColumnStatCellName.Width = 160;
            // 
            // olvColumnStatNBCount
            // 
            this.olvColumnStatNBCount.HeaderFont = null;
            this.olvColumnStatNBCount.Text = "配置邻区数量";
            this.olvColumnStatNBCount.Width = 80;
            // 
            // olvColumnStatCellTAC
            // 
            this.olvColumnStatCellTAC.HeaderFont = null;
            this.olvColumnStatCellTAC.Text = "TAC";
            this.olvColumnStatCellTAC.Width = 80;
            // 
            // olvColumnStatCellCellID
            // 
            this.olvColumnStatCellCellID.HeaderFont = null;
            this.olvColumnStatCellCellID.Text = "CellID";
            this.olvColumnStatCellCellID.Width = 80;
            // 
            // olvColumnNCI
            // 
            this.olvColumnNCI.HeaderFont = null;
            this.olvColumnNCI.Text = "NCI";
            this.olvColumnNCI.Width = 80;
            // 
            // olvColumnEnodeBID
            // 
            this.olvColumnEnodeBID.HeaderFont = null;
            this.olvColumnEnodeBID.Text = "EnodeBID";
            // 
            // olvColumnSectorID
            // 
            this.olvColumnSectorID.HeaderFont = null;
            this.olvColumnSectorID.Text = "SectorID";
            // 
            // olvColumnStatCellARFCN
            // 
            this.olvColumnStatCellARFCN.HeaderFont = null;
            this.olvColumnStatCellARFCN.Text = "ARFCN";
            this.olvColumnStatCellARFCN.Width = 80;
            // 
            // olvColumnStatCellPCI
            // 
            this.olvColumnStatCellPCI.HeaderFont = null;
            this.olvColumnStatCellPCI.Text = "PCI";
            // 
            // olvColumnStatSampleCount
            // 
            this.olvColumnStatSampleCount.HeaderFont = null;
            this.olvColumnStatSampleCount.Text = "采样点数";
            // 
            // olvColumnStatCellRSRP
            // 
            this.olvColumnStatCellRSRP.HeaderFont = null;
            this.olvColumnStatCellRSRP.Text = "平均RSRP";
            // 
            // olvColumnStatScore
            // 
            this.olvColumnStatScore.HeaderFont = null;
            this.olvColumnStatScore.Text = "总得分";
            // 
            // olvColumnStatOrder
            // 
            this.olvColumnStatOrder.HeaderFont = null;
            this.olvColumnStatOrder.Text = "排名";
            // 
            // olvColumnStatSource
            // 
            this.olvColumnStatSource.HeaderFont = null;
            this.olvColumnStatSource.Text = "数据来源";
            this.olvColumnStatSource.Width = 80;
            // 
            // olvColumnStatStatusByMsg
            // 
            this.olvColumnStatStatusByMsg.HeaderFont = null;
            this.olvColumnStatStatusByMsg.Text = "检测结果(与信令比对)";
            this.olvColumnStatStatusByMsg.Width = 80;
            // 
            // olvColumnStatStatusByParam
            // 
            this.olvColumnStatStatusByParam.HeaderFont = null;
            this.olvColumnStatStatusByParam.Text = "检测结果(与工参比对)";
            this.olvColumnStatStatusByParam.Width = 80;
            // 
            // olvColumnStatAdvice
            // 
            this.olvColumnStatAdvice.HeaderFont = null;
            this.olvColumnStatAdvice.Text = "建议";
            this.olvColumnStatAdvice.Width = 80;
            // 
            // olvColumnStatDistance
            // 
            this.olvColumnStatDistance.HeaderFont = null;
            this.olvColumnStatDistance.Text = "距离(米)";
            this.olvColumnStatDistance.Width = 80;
            // 
            // ZTLteNBCellCheckBothAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1250, 561);
            this.Controls.Add(this.ListViewNBCheckStat);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "NBCellCheckAnaForm";
            this.Text = "NR邻区检测分析结果";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewNBCheckStat)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private BrightIdeasSoftware.TreeListView ListViewNBCheckStat;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellCellID;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellPCI;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellRSRP;
        private BrightIdeasSoftware.OLVColumn olvColumnStatScore;
        private BrightIdeasSoftware.OLVColumn olvColumnStatDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnStatStatusByMsg;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSource;
        private BrightIdeasSoftware.OLVColumn olvColumnStatNBCount;
        private BrightIdeasSoftware.OLVColumn olvColumnStatOrder;
        private BrightIdeasSoftware.OLVColumn olvColumnStatStatusByParam;
        private BrightIdeasSoftware.OLVColumn olvColumnStatAdvice;
        private BrightIdeasSoftware.OLVColumn olvColumnNCI;
        private BrightIdeasSoftware.OLVColumn olvColumnEnodeBID;
        private BrightIdeasSoftware.OLVColumn olvColumnSectorID;
    }
}