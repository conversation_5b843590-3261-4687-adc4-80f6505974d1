﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    public partial class WeakCoverPnl : UserControl
    {
        public WeakCoverPnl()
        {
            InitializeComponent();
        }

        WeakCoverCause mainReason = null;
        WeakCoverNoSite noSiteR = null;
        WeakCoverLackNbCell lncR = null;
        WeakCoverSCell scR = null;
        WeakCoverNCell ncR = null;
        public void LinkCondition(WeakCoverCause reason)
        {
            this.mainReason = reason;
            foreach (CauseBase item in reason.SubCauses)
            {
                if (item is WeakCoverNoSite)
                {
                    noSiteR = item as WeakCoverNoSite;
                }
                else if (item is WeakCoverLackNbCell)
                {
                    lncR = item as WeakCoverLackNbCell;
                }
                else if (item is WeakCoverSCell)
                {
                    scR = item as WeakCoverSCell;
                }
                else if (item is WeakCoverNCell)
                {
                    ncR = item as WeakCoverNCell;
                }
            }

            numRSRPMax.Value = (decimal)mainReason.RSRPMax;
            numRSRPMax.ValueChanged += numRSRPMax_ValueChanged;

            if (noSiteR != null)
            {
                numDisMin.Value = (decimal)noSiteR.RadiusMin;
                numDisMin.ValueChanged += numDisMin_ValueChanged;
                lblNoSite.Text = string.Format("周围{0}米内，没有其它LTE基站", noSiteR.RadiusMin);
            }

            if (lncR != null)
            {
                numDisMin2.Value = (decimal)lncR.RadiusMin;
                numDisMin2.ValueChanged += numDisMin2_ValueChanged;
                lblLNC.Text = string.Format("周围{0}米内，有其它LTE基站", lncR.RadiusMin);
            }

            if (scR != null)
            {
                numDisMax.Value = (decimal)scR.RadiusMin;
                numDisMax.ValueChanged += numDisMax_ValueChanged;
            }

            if (ncR != null)
            {
                numDisMax2.Value = (decimal)ncR.RadiusMin;
                numDisMax2.ValueChanged += numDisMax2_ValueChanged;
            }

        }

        void numDisMax2_ValueChanged(object sender, EventArgs e)
        {
            ncR.RadiusMin = (float)numDisMax2.Value;
            numDisMax.Value = numDisMax2.Value;
        }

        void numDisMax_ValueChanged(object sender, EventArgs e)
        {
            scR.RadiusMin = (float)numDisMax.Value;
            numDisMax2.Value = numDisMax.Value;
        }

        void numDisMin2_ValueChanged(object sender, EventArgs e)
        {
            lncR.RadiusMin = (float)numDisMin2.Value;
            lblLNC.Text = string.Format("周围{0}米内，有其它LTE基站", lncR.RadiusMin);
            numDisMin.Value = numDisMin2.Value;
        }

        void numDisMin_ValueChanged(object sender, EventArgs e)
        {
            noSiteR.RadiusMin = (float)numDisMin.Value;
            lblNoSite.Text = string.Format("周围{0}米内，没有其它LTE基站", noSiteR.RadiusMin);
            numDisMin2.Value = numDisMin.Value;
        }


        void numRSRPMax_ValueChanged(object sender, EventArgs e)
        {
            mainReason.RSRPMax = (float)numRSRPMax.Value;
        }

    }
}
