﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class NR700MCellInfo : CellInfoBase
    {
        public NR700MCellServiceInfo SAInfo { get; private set; }

        public NR700MCellInfo(ICell cell)
            : base(cell)
        {

        }

        public void Init(ICell cell)
        {
            if (SAInfo == null)
            {
                SAInfo = new NR700MCellServiceInfo(cell as NRCell);
            }
        }
    }

    public class NR700MCellServiceInfo
    {
        public NR700MCellServiceInfo(ICell cell)
        {
            Cell = cell;
        }
        public ICell Cell { get; set; }
        public SuccessRateKpiInfo AccessInfo { get; set; } = new SuccessRateKpiInfo();

        public class EvtInfo
        {
            public DataKpiInfo Delay { get; set; } = new DataKpiInfo();
            public DataKpiInfo Shake { get; set; } = new DataKpiInfo();
            public SuccessRateKpiInfo RateInfo { get; set; } = new SuccessRateKpiInfo();
        }

        #region PING
        public EvtInfo BigPackagePing { get; set; } = new EvtInfo();
        public EvtInfo SmallPackagePing { get; set; } = new EvtInfo();

        //public DataKpiInfo BigPackageDelay { get; set; } = new DataKpiInfo();
        //public DataKpiInfo SmallPackageDelay { get; set; } = new DataKpiInfo();

        //public DataKpiInfo BigPackageShake { get; set; } = new DataKpiInfo();
        //public DataKpiInfo SmallPackageShake { get; set; } = new DataKpiInfo();

        //public SuccessRateKpiInfo BigPackageInfo { get; set; } = new SuccessRateKpiInfo();
        //public SuccessRateKpiInfo SmallPackageInfo { get; set; } = new SuccessRateKpiInfo();
        #endregion

        #region EPSFB
        public EvtInfo EpsfbNrCallNr { get; set; } = new EvtInfo();
        public EvtInfo EpsfbNrCallLte{ get; set; } = new EvtInfo();
        public EvtInfo EpsfbNrCallNrFR { get; set; } = new EvtInfo();
        public EvtInfo EpsfbNrCallLteFR { get; set; } = new EvtInfo();

        //public DataKpiInfo NrCallNrEPSFBDelay { get; set; } = new DataKpiInfo();
        //public DataKpiInfo NrCallLteEPSFBDelay { get; set; } = new DataKpiInfo();
        //public DataKpiInfo NrCallNrFREPSFBDelay { get; set; } = new DataKpiInfo();
        //public DataKpiInfo NrCallLteFREPSFBDelay { get; set; } = new DataKpiInfo();

        //public DataKpiInfo NrCallNrEPSFBShake { get; set; } = new DataKpiInfo();
        //public DataKpiInfo NrCallLteEPSFBShake { get; set; } = new DataKpiInfo();
        //public DataKpiInfo NrCallNrFREPSFBShake { get; set; } = new DataKpiInfo();
        //public DataKpiInfo NrCallLteFREPSFBShake { get; set; } = new DataKpiInfo();

        //public SuccessRateKpiInfo NrCallNrEPSFBInfo { get; set; } = new SuccessRateKpiInfo();
        //public SuccessRateKpiInfo NrCallLteEPSFBInfo { get; set; } = new SuccessRateKpiInfo();
        //public SuccessRateKpiInfo NrCallNrFREPSFBInfo { get; set; } = new SuccessRateKpiInfo();
        //public SuccessRateKpiInfo NrCallLteFREPSFBInfo { get; set; } = new SuccessRateKpiInfo();
        #endregion

        public FtpPointInfo GoodSampleDL { get; set; } = new FtpPointInfo();
        public FtpPointInfo BadSampleDL { get; set; } = new FtpPointInfo();
        public FtpPointInfo GoodSampleUL { get; set; } = new FtpPointInfo();
        public FtpPointInfo BadSampleUL { get; set; } = new FtpPointInfo();

        #region Pic
        public PicKpiInfo NRRsrpPic { get; set; } = new PicKpiInfo();
        public PicKpiInfo NRSinrPic { get; set; } = new PicKpiInfo();
        public PicKpiInfo NRDLPic { get; set; } = new PicKpiInfo();
        public PicKpiInfo NRULPic { get; set; } = new PicKpiInfo();

        public PicKpiInfo NRDLLineChartPic { get; set; } = new PicKpiInfo();
        public PicKpiInfo NRULLineChartPic { get; set; } = new PicKpiInfo();
        #endregion
    }

    public class NR700MCellParameters : CellParameters
    {
        #region 小区天线参数验证
        public string CellName { get; set; }

        /// <summary>
        /// 经度
        /// </summary>
        public ParamInfo<double> Longitude { get; set; } = new ParamInfo<double>();
        /// <summary>
        /// 纬度
        /// </summary>
        public ParamInfo<double> Latitude { get; set; } = new ParamInfo<double>();
        public ParamInfo<int> CellID { get; set; } = new ParamInfo<int>();
        public ParamInfo<int> PCI { get; set; } = new ParamInfo<int>();
        /// <summary>
        /// 频段
        /// </summary>
        public ParamInfo<string> FreqBand { get; set; } = new ParamInfo<string>();
        /// <summary>
        /// 频点
        /// </summary>
        public ParamInfo<string> Freq { get; set; } = new ParamInfo<string>();
        /// <summary>
        /// 小区带宽（兆）
        /// </summary>
        public ParamInfo<string> Bandwidth { get; set; } = new ParamInfo<string>();
        /// <summary>
        /// 根序列(PRACH)
        /// </summary>
        public ParamInfo<string> PRACH { get; set; } = new ParamInfo<string>();

        /// <summary>
        /// 天线挂高
        /// </summary>
        public ParamInfo<double?> Altitude { get; set; } = new ParamInfo<double?>();
        /// <summary>
        /// 方位角
        /// </summary>
        public ParamInfo<double?> Direction { get; set; } = new ParamInfo<double?>();
        /// <summary>
        /// 内置倾角
        /// </summary>
        public ParamInfo<double?> BuiltInAngle { get; set; } = new ParamInfo<double?>();
        /// <summary>
        /// 电气下倾角
        /// </summary>
        public ParamInfo<double?> Downtilt { get; set; } = new ParamInfo<double?>();
        /// <summary>
        /// 机械下倾角
        /// </summary>
        public ParamInfo<double?> MechanicalTilt { get; set; } = new ParamInfo<double?>();
        /// <summary>
        /// 总下倾角
        /// </summary>
        public ParamInfo<double?> Downward { get; set; } = new ParamInfo<double?>();
        #endregion

        public void Caluculate()
        {
            Longitude.JudgeValidLongitude(50);
            Latitude.JudgeValidLatitude(50);
            CellID.JudgeValid();
            PCI.JudgeValid();
            FreqBand.JudgeValid();
            Freq.JudgeValid();
            Bandwidth.JudgeValid();
            PRACH.JudgeValid();

            Altitude.JudgeValid();
            Direction.JudgeValid();
            BuiltInAngle.JudgeValid();
            Downtilt.JudgeValid();
            MechanicalTilt.JudgeValid();
            Downward.JudgeValid();
        }
    }
}
