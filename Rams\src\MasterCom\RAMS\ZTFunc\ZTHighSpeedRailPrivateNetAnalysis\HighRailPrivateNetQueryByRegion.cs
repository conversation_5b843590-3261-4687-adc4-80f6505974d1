﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class HighRailPrivateNetQueryByRegion : HighSpeedRailPrivateNetQuery
    {
        private HighRailPrivateNetQueryByRegion()
            : base()
        {
            FilterSampleByRegion = true;
        }

        private static HighRailPrivateNetQueryByRegion instance = null;
        public static new HighRailPrivateNetQueryByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new HighRailPrivateNetQueryByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "高铁专项(按区域)"; }
        }
    }
}
