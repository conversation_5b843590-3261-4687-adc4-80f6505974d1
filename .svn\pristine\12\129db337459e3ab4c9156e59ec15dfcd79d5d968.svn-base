﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MasterCom.MapSpaceManager.Util;
using System.Xml;
using MapWinGIS;
using System.IO;

namespace MasterCom.MapSpaceManager
{
    /// <summary>
    /// 地图显示设置，一个图层设置
    /// </summary>
    public class VecLayerItem
    {
        public VecLayerItem()
        {

        }
        /// <summary>
        /// 0 不显示  1 显示
        /// </summary>
        public int visible = 1;
        /// <summary>
        /// 0 显示范围控制不生效 1生效
        /// </summary>
        public int visible_range_enable = 0;
        public int visible_range_max = 123456789;
        public int visible_range_min = 0;

        public String file_path_name;
        public String name;
        /// <summary>
        /// 0 背景不填充  1 背景填充
        /// </summary>
        public int style_bg_fill = 0;
        /// <summary>
        /// 背景不透明度 0全透明   1 不透明
        /// </summary>
        public float style_bg_opaque = 0.5f;
        public Color style_bg_color = Color.Cyan;
        public String style_bg_color_column = "";
        public float style_line_width = 2.0f;
        public Color style_line_color = Color.Gray;
        public String style_line_patten = "";
        /// <summary>
        /// 0 非道路图层 1 道路图层
        /// </summary>
        public int is_road_layer = 0;
        /// <summary>
        /// 是否显示标签
        /// </summary>
        public int label_show = 0;
        public string label_field = "";
        public int label_font_size = 10;
        public Color label_font_color = Color.Blue;
        
        //============非保存的程序运行时用
        public int _handle = -1;
        public Shapefile _sfile = null;
        //====================
        public object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals(typeof(VecLayerItem).Name))
            {
                object o = configFile.GetItemValue(item, "visible");
                visible = int.Parse(configFile.GetItemValue(item, "visible").ToString());
                visible_range_enable = int.Parse(configFile.GetItemValue(item, "visible_range_enable").ToString());
                visible_range_max = int.Parse(configFile.GetItemValue(item, "visible_range_max").ToString());
                visible_range_min = int.Parse(configFile.GetItemValue(item, "visible_range_min").ToString());
                file_path_name = configFile.GetItemValue(item, "file_path_name").ToString();
                name = (string)configFile.GetItemValue(item, "name");

                style_bg_fill = int.Parse(configFile.GetItemValue(item, "style_bg_fill").ToString());
                style_bg_opaque = float.Parse(configFile.GetItemValue(item, "style_bg_opaque").ToString());

                string rgb = configFile.GetItemValue(item, "style_bg_color").ToString();
                string[] colorArr = rgb.Split(',');
                style_bg_color = Color.FromArgb(int.Parse(colorArr[0]), int.Parse(colorArr[1]), int.Parse(colorArr[2]));

                rgb = configFile.GetItemValue(item, "style_line_color").ToString();
                colorArr = rgb.Split(',');
                style_line_color = Color.FromArgb(int.Parse(colorArr[0]), int.Parse(colorArr[1]), int.Parse(colorArr[2]));

                style_line_width = float.Parse(configFile.GetItemValue(item, "style_line_width").ToString());
                is_road_layer = int.Parse(configFile.GetItemValue(item, "is_road_layer").ToString());
                label_show = int.Parse(configFile.GetItemValue(item, "label_show").ToString());
                label_field = (String)configFile.GetItemValue(item, "label_field");
                if (label_field == null)
                {
                    label_field = "";
                }
                //====
                object sizeObj = configFile.GetItemValue(item, "label_font_size");
                if (sizeObj is int)
                {
                    label_font_size = (int)sizeObj;
                }
                object lbColorObj = configFile.GetItemValue(item, "label_font_color");
                if (lbColorObj is String)
                {
                    string[] colorArrX = ((String)lbColorObj).Split(',');
                    if (colorArrX.Length == 3)
                    {
                        label_font_color = Color.FromArgb(int.Parse(colorArrX[0]), int.Parse(colorArrX[1]), int.Parse(colorArrX[2]));
                    }
                }
                //
                style_line_patten = (String)configFile.GetItemValue(item, "style_line_patten");
                if (style_line_patten == null)
                {
                    style_line_patten = "基本";
                }
                style_bg_color_column = (String)configFile.GetItemValue(item, "style_bg_color_column");
                if (style_bg_color_column == null)
                {
                    style_bg_color_column = "";
                }
                //======
                return this;
            }
            return null;
        }

        public XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is VecLayerItem)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "visible", visible);
                configFile.AddItem(item, "visible_range_enable", visible_range_enable);
                configFile.AddItem(item, "visible_range_max", visible_range_max);
                configFile.AddItem(item, "visible_range_min", visible_range_min);
                configFile.AddItem(item, "file_path_name", file_path_name);
                configFile.AddItem(item, "name", this.name);
                configFile.AddItem(item, "style_bg_fill", style_bg_fill);
                configFile.AddItem(item, "style_bg_opaque", style_bg_opaque);

                string strClr = style_bg_color.R + "," + style_bg_color.G + "," + style_bg_color.B;
                configFile.AddItem(item, "style_bg_color", strClr);

                strClr = style_line_color.R + "," + style_line_color.G + "," + style_line_color.B;
                configFile.AddItem(item, "style_line_color", strClr);

                configFile.AddItem(item, "style_line_width", style_line_width);
                configFile.AddItem(item, "is_road_layer", is_road_layer);
                configFile.AddItem(item, "label_show", label_show);
                configFile.AddItem(item, "label_field", this.label_field);
                //===
                configFile.AddItem(item, "label_font_size", this.label_font_size);
                string strClrFont = label_font_color.R + "," + label_font_color.G + "," + label_font_color.B;
                configFile.AddItem(item, "label_font_color", strClrFont);
                configFile.AddItem(item, "style_line_patten", style_line_patten);
                configFile.AddItem(item, "style_bg_color_column", style_bg_color_column);
                return item;
            }
            return null;
        }

    }

}
