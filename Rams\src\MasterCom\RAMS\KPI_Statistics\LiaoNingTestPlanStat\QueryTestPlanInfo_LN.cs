﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryTestPlanInfo_LN : DIYSQLBase
    {
        public Dictionary<string, List<string>> Log_CityGrid_Dic { get; set; } = new Dictionary<string, List<string>>();
        public Dictionary<string, List<string>> City_Log_Dic { get; set; } = new Dictionary<string, List<string>>();
        public QueryTestPlanInfo_LN(MainModel mainModel)
            : base(mainModel)
        { 
        }

        public override string Name
        {
            get { return "QueryTestPlanInfo_LN"; }
        }
        public void clearDatas()
        {
            Log_CityGrid_Dic.Clear();
            City_Log_Dic.Clear();
        }

        /// <summary>
        /// 获得根据时间范围年月组成的SQL语句
        /// </summary>
        /// <returns></returns>
        protected override string getSqlTextString()
        {
            string sql = string.Empty;
            if (condition != null && condition.Periods != null)
            {
                StringBuilder sqlStrb = new StringBuilder();
                foreach (TimePeriod period in condition.Periods)
                {
                    DateTime startDate = period.BeginTime;
                    DateTime endDate = period.EndTime;
                    while (endDate >= startDate)
                    {
                        string tableName = "tb_testplan_LiaoNing_";
                        tableName = tableName + startDate.ToString("yyyyMM");
                        sqlStrb.AppendFormat(" if exists (select name from sysobjects where name='{0}' and type='U') ", tableName);
                        sqlStrb.AppendFormat(@"select cityname, gridName,testPlanName,logName, logTime from {0} where 
 logTime between '{1}' and '{2}';", tableName, startDate, endDate);
                        startDate = startDate.AddMonths(1);
                    }
                }
                sql = sqlStrb.ToString();
            }
            return sql;
        }
        protected override E_VType[] getSqlRetTypeArr()/*获取SQL返回类型*/
        {
            E_VType[] rType = new E_VType[5];
            rType[0] = E_VType.E_String;/*城市*/
            rType[1] = E_VType.E_String;/*区域*/
            rType[2] = E_VType.E_String;/*测试计划名称*/
            rType[3] = E_VType.E_String;/*log*/
            rType[4] = E_VType.E_String;/*日期*/
            return rType;
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                if (MainDB)
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                }
                else
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                }

                E_VType[] retArrDef = getSqlRetTypeArr();
                string strsql = getSqlTextString();
                string[] strArr = strsql.Split(';');
                int curIdx = 0;
                while (curIdx < strArr.Length)
                {
                    StringBuilder txt = new StringBuilder();
                    for (; curIdx < strArr.Length; curIdx++)
                    {
                        txt.Append(strArr[curIdx] + ";");
                        if (txt.Length > 6000)
                        {
                            break;
                        }
                    }
                    package.Content.PrepareAddParam();
                    package.Content.AddParam(txt.ToString());
                    StringBuilder sb = getRetArrDef(retArrDef);
                    package.Content.AddParam(sb.ToString());
                    clientProxy.Send();
                    System.Threading.Thread.Sleep(100);
                }
                receiveRetData(clientProxy);
            }
            catch
            {
                //continue
            }
        }

        private static StringBuilder getRetArrDef(E_VType[] retArrDef)
        {
            StringBuilder sb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    sb.Append((int)retArrDef[i]);
                    if (i < retArrDef.Length - 1)
                    {
                        sb.Append(",");
                    }
                }
            }

            return sb;
        }

        /// <summary>
        /// 接收返回数据
        /// </summary>
        /// <param name="clientProxy"></param>
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            clearDatas();
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();

                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        private void fillData(Package package)
        {
            string city = package.Content.GetParamString();
            string grid = package.Content.GetParamString();
            package.Content.GetParamString();//testPlan
            string log = package.Content.GetParamString();
            package.Content.GetParamString();//dataTime
            if (!string.IsNullOrEmpty(city) && !string.IsNullOrEmpty(log))
            {
                addToCity_Log_Dic(city, log);
                addToLog_CityGrid_Dic(log, city, grid);
            }
        }

        /// <summary>
        /// 获取city-log 根据测试计划查询log
        /// </summary>
        /// <param name="city">城市</param>
        /// <param name="log">log名称</param>
        private void addToCity_Log_Dic(string city, string log)
        {
            List<string> logList;
            if (!City_Log_Dic.TryGetValue(city, out logList))
            {
                logList = new List<string>();
                City_Log_Dic.Add(city, logList);
            }
            if (!logList.Contains(log))
            {
                logList.Add(log);
            }
        }

        /// <summary>
        /// 获取log-city/cityarea 的集合进行kpi统计
        /// </summary>
        /// <param name="log">log名称</param>
        /// <param name="city">城市</param>
        /// <param name="area">网格</param>
        private void addToLog_CityGrid_Dic(string log, string city, string grid)
        {
            List<string> cityGridList;
            if (!Log_CityGrid_Dic.TryGetValue(log, out cityGridList))
            {
                cityGridList = new List<string>();
                Log_CityGrid_Dic.Add(log, cityGridList);
            }

            string citygrid = city + "-" + grid;
            if (!cityGridList.Contains(citygrid))
            {
                cityGridList.Add(citygrid);
            }
            if (!cityGridList.Contains(city))
            {
                cityGridList.Add(city);
            }
        }
    }
}
