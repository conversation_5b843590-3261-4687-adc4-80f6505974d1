﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYReplyCountryInvestigation : DIYAnalyseFilesOneByOneByRegion
    {
        private readonly Dictionary<string, List<CountryInvestigation>> testTypeCountryDic;
        private readonly CategoryEnum servItem;

        public ZTDIYReplyCountryInvestigation(MainModel mainModel, Dictionary<string, List<CountryInvestigation>> testTypeCountryDic)
            : base(mainModel)
        {
            IncludeEvent = false;
            FilterSampleByRegion = false;
            this.testTypeCountryDic = testTypeCountryDic;

            if (mainModel.CategoryManager["ServiceType"] != null)
            {
                servItem = (CategoryEnum)CategoryManager.GetInstance()["ServiceType"];
            }
        }

        protected override void queryFileToAnalyse()
        {
            //
        }

        private bool isValidServiceType(int fileServ, string serviceType)
        {
            switch (serviceType)
            {
                case "语音":
                    return servItem[fileServ].Description.Contains(serviceType);
                case "数据":
                    return servItem[fileServ].Description.Contains(serviceType) ||
                        servItem[fileServ].Description.Contains("HSDPA") ||
                        servItem[fileServ].Description.Contains("HSUPA");
                default:
                    return false;
            }
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileManager in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (string testType in testTypeCountryDic.Keys)
                {
                    if (!isValidServiceType(fileManager.ServiceType, testType)) continue;
                    foreach (TestPoint tp in fileManager.TestPoints)
                    {
                        dealTestPoint(testTypeCountryDic[testType], tp);
                    }
                }
            }
        }

        private void dealTestPoint(List<CountryInvestigation> countryList, TestPoint tp)
        {
            foreach (CountryInvestigation ci in countryList)
            {
                if (tp.DateTime >= ci.DTStart && tp.DateTime <= ci.DTEnd)
                {
                    ci.DealTestPoint(tp);
                }
            }
        }
    }
}
