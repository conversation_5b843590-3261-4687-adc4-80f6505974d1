﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ESRVCCAnaListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.bandedGridView = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn52 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn50 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn14 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn15 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn16 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn17 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand4 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand5 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn21 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn22 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand7 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.ctxMenu;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.bandedGridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(972, 447);
            this.gridControl.TabIndex = 0;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView});
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay,
            this.toolStripSeparator1,
            this.miExport2Xls});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(139, 54);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(138, 22);
            this.miReplay.Text = "回放文件...";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(135, 6);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(138, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // bandedGridView
            // 
            this.bandedGridView.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.bandedGridView.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.bandedGridView.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView.BandPanelRowHeight = 4;
            this.bandedGridView.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand1,
            this.gridBand2,
            this.gridBand3,
            this.gridBand4,
            this.gridBand5,
            this.gridBand7});
            this.bandedGridView.ColumnPanelRowHeight = 50;
            this.bandedGridView.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.bandedGridColumn3,
            this.bandedGridColumn4,
            this.bandedGridColumn6,
            this.bandedGridColumn7,
            this.bandedGridColumn8,
            this.bandedGridColumn52,
            this.bandedGridColumn50,
            this.bandedGridColumn9,
            this.bandedGridColumn10,
            this.bandedGridColumn11,
            this.bandedGridColumn14,
            this.bandedGridColumn15,
            this.bandedGridColumn16,
            this.bandedGridColumn17,
            this.bandedGridColumn18,
            this.bandedGridColumn19,
            this.bandedGridColumn20,
            this.bandedGridColumn21,
            this.bandedGridColumn22,
            this.bandedGridColumn5});
            this.bandedGridView.GridControl = this.gridControl;
            this.bandedGridView.Name = "bandedGridView";
            this.bandedGridView.OptionsBehavior.Editable = false;
            this.bandedGridView.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView.OptionsView.ShowGroupPanel = false;
            this.bandedGridView.DoubleClick += new System.EventHandler(this.bandedGridView_DoubleClick);
            // 
            // gridBand1
            // 
            this.gridBand1.Caption = "基本信息";
            this.gridBand1.Columns.Add(this.bandedGridColumn1);
            this.gridBand1.Columns.Add(this.bandedGridColumn2);
            this.gridBand1.Columns.Add(this.bandedGridColumn3);
            this.gridBand1.Columns.Add(this.bandedGridColumn4);
            this.gridBand1.Columns.Add(this.bandedGridColumn6);
            this.gridBand1.Columns.Add(this.bandedGridColumn7);
            this.gridBand1.Columns.Add(this.bandedGridColumn8);
            this.gridBand1.Columns.Add(this.bandedGridColumn52);
            this.gridBand1.Columns.Add(this.bandedGridColumn50);
            this.gridBand1.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridBand1.MinWidth = 20;
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.Width = 764;
            // 
            // bandedGridColumn1
            // 
            this.bandedGridColumn1.Caption = "序号";
            this.bandedGridColumn1.FieldName = "SN";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.RowCount = 4;
            this.bandedGridColumn1.Visible = true;
            this.bandedGridColumn1.Width = 61;
            // 
            // bandedGridColumn2
            // 
            this.bandedGridColumn2.Caption = "eSRVCC是否成功";
            this.bandedGridColumn2.FieldName = "ESRVCCResult";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.RowCount = 4;
            this.bandedGridColumn2.Visible = true;
            this.bandedGridColumn2.Width = 70;
            // 
            // bandedGridColumn3
            // 
            this.bandedGridColumn3.Caption = "失败现象及原因";
            this.bandedGridColumn3.FieldName = "CallFailReason";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.RowCount = 4;
            this.bandedGridColumn3.Visible = true;
            this.bandedGridColumn3.Width = 116;
            // 
            // bandedGridColumn4
            // 
            this.bandedGridColumn4.Caption = "挂机后自主FR是否成功返回";
            this.bandedGridColumn4.FieldName = "FRIsBack";
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            this.bandedGridColumn4.RowCount = 4;
            this.bandedGridColumn4.Visible = true;
            this.bandedGridColumn4.Width = 92;
            // 
            // bandedGridColumn6
            // 
            this.bandedGridColumn6.Caption = "LTE测控消息中包含的2G频点";
            this.bandedGridColumn6.FieldName = "LteContGsmEARFCN";
            this.bandedGridColumn6.Name = "bandedGridColumn6";
            this.bandedGridColumn6.RowCount = 4;
            this.bandedGridColumn6.Visible = true;
            this.bandedGridColumn6.Width = 100;
            // 
            // bandedGridColumn7
            // 
            this.bandedGridColumn7.Caption = "LTE频点";
            this.bandedGridColumn7.FieldName = "LteEARFCN";
            this.bandedGridColumn7.Name = "bandedGridColumn7";
            this.bandedGridColumn7.RowCount = 4;
            this.bandedGridColumn7.Visible = true;
            // 
            // bandedGridColumn8
            // 
            this.bandedGridColumn8.Caption = "LTE PCI";
            this.bandedGridColumn8.FieldName = "LtePCI";
            this.bandedGridColumn8.Name = "bandedGridColumn8";
            this.bandedGridColumn8.RowCount = 4;
            this.bandedGridColumn8.Visible = true;
            // 
            // bandedGridColumn52
            // 
            this.bandedGridColumn52.Caption = "目标GSM频点";
            this.bandedGridColumn52.FieldName = "GsmEARFCN";
            this.bandedGridColumn52.Name = "bandedGridColumn52";
            this.bandedGridColumn52.RowCount = 4;
            this.bandedGridColumn52.Visible = true;
            this.bandedGridColumn52.Width = 95;
            // 
            // bandedGridColumn50
            // 
            this.bandedGridColumn50.Caption = "GSM BSIC";
            this.bandedGridColumn50.FieldName = "BSIC";
            this.bandedGridColumn50.Name = "bandedGridColumn50";
            this.bandedGridColumn50.RowCount = 4;
            this.bandedGridColumn50.Visible = true;
            this.bandedGridColumn50.Width = 80;
            // 
            // gridBand2
            // 
            this.gridBand2.Caption = "切换点信息";
            this.gridBand2.Columns.Add(this.bandedGridColumn9);
            this.gridBand2.Columns.Add(this.bandedGridColumn10);
            this.gridBand2.Columns.Add(this.bandedGridColumn11);
            this.gridBand2.Columns.Add(this.bandedGridColumn14);
            this.gridBand2.MinWidth = 20;
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 440;
            // 
            // bandedGridColumn9
            // 
            this.bandedGridColumn9.Caption = "切换前LTE RSRP";
            this.bandedGridColumn9.DisplayFormat.FormatString = "F2";
            this.bandedGridColumn9.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn9.FieldName = "LteRSRP";
            this.bandedGridColumn9.Name = "bandedGridColumn9";
            this.bandedGridColumn9.RowCount = 4;
            this.bandedGridColumn9.Visible = true;
            this.bandedGridColumn9.Width = 108;
            // 
            // bandedGridColumn10
            // 
            this.bandedGridColumn10.Caption = "切换前LTE SINR";
            this.bandedGridColumn10.DisplayFormat.FormatString = "F2";
            this.bandedGridColumn10.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn10.FieldName = "LteSINR";
            this.bandedGridColumn10.Name = "bandedGridColumn10";
            this.bandedGridColumn10.RowCount = 4;
            this.bandedGridColumn10.Visible = true;
            this.bandedGridColumn10.Width = 108;
            // 
            // bandedGridColumn11
            // 
            this.bandedGridColumn11.Caption = "切换后2G RxLev";
            this.bandedGridColumn11.DisplayFormat.FormatString = "F2";
            this.bandedGridColumn11.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn11.FieldName = "RexLevel";
            this.bandedGridColumn11.Name = "bandedGridColumn11";
            this.bandedGridColumn11.RowCount = 4;
            this.bandedGridColumn11.Visible = true;
            this.bandedGridColumn11.Width = 124;
            // 
            // bandedGridColumn14
            // 
            this.bandedGridColumn14.Caption = "切换后2G C/I";
            this.bandedGridColumn14.DisplayFormat.FormatString = "F2";
            this.bandedGridColumn14.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn14.FieldName = "C2I";
            this.bandedGridColumn14.Name = "bandedGridColumn14";
            this.bandedGridColumn14.RowCount = 4;
            this.bandedGridColumn14.Visible = true;
            this.bandedGridColumn14.Width = 100;
            // 
            // gridBand3
            // 
            this.gridBand3.Caption = "切换时延(s)";
            this.gridBand3.Columns.Add(this.bandedGridColumn15);
            this.gridBand3.Columns.Add(this.bandedGridColumn16);
            this.gridBand3.Columns.Add(this.bandedGridColumn17);
            this.gridBand3.Columns.Add(this.bandedGridColumn18);
            this.gridBand3.Columns.Add(this.bandedGridColumn19);
            this.gridBand3.MinWidth = 20;
            this.gridBand3.Name = "gridBand3";
            this.gridBand3.Width = 469;
            // 
            // bandedGridColumn15
            // 
            this.bandedGridColumn15.Caption = "切换准备时延";
            this.bandedGridColumn15.FieldName = "HandoverPrepTimes";
            this.bandedGridColumn15.Name = "bandedGridColumn15";
            this.bandedGridColumn15.RowCount = 4;
            this.bandedGridColumn15.Visible = true;
            this.bandedGridColumn15.Width = 97;
            // 
            // bandedGridColumn16
            // 
            this.bandedGridColumn16.Caption = "切换控制面中断时延";
            this.bandedGridColumn16.FieldName = "HandoverCtrlSuspendTimes";
            this.bandedGridColumn16.Name = "bandedGridColumn16";
            this.bandedGridColumn16.RowCount = 4;
            this.bandedGridColumn16.Visible = true;
            this.bandedGridColumn16.Width = 90;
            // 
            // bandedGridColumn17
            // 
            this.bandedGridColumn17.Caption = "切换用户面下行中断时延";
            this.bandedGridColumn17.FieldName = "HandoverDownDelayTimes";
            this.bandedGridColumn17.Name = "bandedGridColumn17";
            this.bandedGridColumn17.RowCount = 4;
            this.bandedGridColumn17.Visible = true;
            this.bandedGridColumn17.Width = 90;
            // 
            // bandedGridColumn18
            // 
            this.bandedGridColumn18.Caption = "切换用户面上行中断时延";
            this.bandedGridColumn18.FieldName = "HandoverUpDelayTimes";
            this.bandedGridColumn18.Name = "bandedGridColumn18";
            this.bandedGridColumn18.RowCount = 4;
            this.bandedGridColumn18.Visible = true;
            this.bandedGridColumn18.Width = 89;
            // 
            // bandedGridColumn19
            // 
            this.bandedGridColumn19.Caption = "切换用户面下上行中断时延";
            this.bandedGridColumn19.FieldName = "HandoverDown2UpDelayTimes";
            this.bandedGridColumn19.Name = "bandedGridColumn19";
            this.bandedGridColumn19.RowCount = 4;
            this.bandedGridColumn19.Visible = true;
            this.bandedGridColumn19.Width = 103;
            // 
            // gridBand4
            // 
            this.gridBand4.Caption = "返回控制面时延(s)  2G channel Release—> TAU Accept";
            this.gridBand4.Columns.Add(this.bandedGridColumn20);
            this.gridBand4.MinWidth = 20;
            this.gridBand4.Name = "gridBand4";
            this.gridBand4.Width = 124;
            // 
            // bandedGridColumn20
            // 
            this.bandedGridColumn20.Caption = "返回控制面时延";
            this.bandedGridColumn20.FieldName = "BackCtrlDelayTimes";
            this.bandedGridColumn20.Name = "bandedGridColumn20";
            this.bandedGridColumn20.RowCount = 4;
            this.bandedGridColumn20.Visible = true;
            this.bandedGridColumn20.Width = 124;
            // 
            // gridBand5
            // 
            this.gridBand5.Caption = "返回控制面时延(s)  2G ->3G->4G";
            this.gridBand5.Columns.Add(this.bandedGridColumn21);
            this.gridBand5.Columns.Add(this.bandedGridColumn22);
            this.gridBand5.MinWidth = 20;
            this.gridBand5.Name = "gridBand5";
            this.gridBand5.Width = 238;
            // 
            // bandedGridColumn21
            // 
            this.bandedGridColumn21.Caption = "2->3控制面总时延";
            this.bandedGridColumn21.FieldName = "CtrlDelayTimes2To3";
            this.bandedGridColumn21.Name = "bandedGridColumn21";
            this.bandedGridColumn21.RowCount = 4;
            this.bandedGridColumn21.Visible = true;
            this.bandedGridColumn21.Width = 118;
            // 
            // bandedGridColumn22
            // 
            this.bandedGridColumn22.Caption = "3->4控制面总时延";
            this.bandedGridColumn22.FieldName = "CtrlDelayTimes3To4";
            this.bandedGridColumn22.Name = "bandedGridColumn22";
            this.bandedGridColumn22.RowCount = 4;
            this.bandedGridColumn22.Visible = true;
            this.bandedGridColumn22.Width = 120;
            // 
            // gridBand7
            // 
            this.gridBand7.Caption = "其他";
            this.gridBand7.Columns.Add(this.bandedGridColumn5);
            this.gridBand7.MinWidth = 20;
            this.gridBand7.Name = "gridBand7";
            this.gridBand7.Width = 500;
            // 
            // bandedGridColumn5
            // 
            this.bandedGridColumn5.Caption = "文件名";
            this.bandedGridColumn5.FieldName = "FileName";
            this.bandedGridColumn5.Name = "bandedGridColumn5";
            this.bandedGridColumn5.Visible = true;
            this.bandedGridColumn5.Width = 500;
            // 
            // ESRVCCAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(972, 447);
            this.Controls.Add(this.gridControl);
            this.Name = "ESRVCCAnaListForm";
            this.Text = "eSRVCC无线性能分析";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn7;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn9;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn11;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn14;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn15;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn16;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn17;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn21;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn22;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn50;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn52;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn5;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand4;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand5;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand7;
    }
}