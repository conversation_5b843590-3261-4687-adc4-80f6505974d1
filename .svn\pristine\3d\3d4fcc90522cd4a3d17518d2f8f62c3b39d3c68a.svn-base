﻿using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteNBCellCheckDiffFreqAnaByBegion : ZTLteNBCellCheckDiffFreqAnaBase
    {
        public ZTLteNBCellCheckDiffFreqAnaByBegion(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected static readonly object lockObj = new object();
        private static ZTLteNBCellCheckDiffFreqAnaByBegion instance = null;
        public static ZTLteNBCellCheckDiffFreqAnaByBegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTLteNBCellCheckDiffFreqAnaByBegion(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "LTE邻区核查(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22044, this.Name);//////
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        /// <summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is LTETestPointDetail)
                {
                    return Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
    public class LteFddNBCellCheckDiffFreqAnaByBegion : ZTLteNBCellCheckDiffFreqAnaByBegion
    {
        public LteFddNBCellCheckDiffFreqAnaByBegion(MainModel mainModel)
            : base(mainModel)
        {
        }
        private static LteFddNBCellCheckDiffFreqAnaByBegion instance = null;
        public new static LteFddNBCellCheckDiffFreqAnaByBegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteFddNBCellCheckDiffFreqAnaByBegion(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26018, this.Name);
        }
    }
}