﻿using MastercomDecode;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Model
{
    public static class MessageDecodeHelper
    {
#if DecodeDll2019
        #region 直接调用新版动态库
        public static void CloseServer()
        {
        }
        public static bool DissectToTree(byte[] byteArray, int dwRead, int msgId, TreeView treeNode)
        {
            return CDecode.DissectToTree(byteArray, dwRead, msgId, treeNode);
        }
        public static int GetFieldsNum(string strFieldName)
        {
            return CDecode.GetFieldsNum(getNewFieldName(strFieldName));
        }
        public static bool GetMultiSInt(string strFieldName, ref int[] valAry, int nMaxSize)
        {
            uint uMaxSize = (uint)nMaxSize;
            string strNewFieldName = getNewFieldName(strFieldName);
            uint[] uValueArray = new uint[0];
            if (CDecode.GetMultiUInt(strNewFieldName, ref uValueArray, uMaxSize))
            {
                valAry = new int[uValueArray.Length];
                for (int i = 0; i< uValueArray.Length; i++)
                {
                    valAry[i] = (int)uValueArray[i];
                }
                return true;
            }
            else
            {
                return CDecode.GetMultiSInt(strNewFieldName, ref valAry, uMaxSize);
            }
        }
        public static bool GetMultiString(string strFieldName, ref string[] valAry, int nMaxSize)
        {
            return CDecode.GetMultiString(getNewFieldName(strFieldName), ref valAry, (uint)nMaxSize);
        }
        public static bool GetMultiUInt(string strFieldName, ref uint[] valAry, int nMaxSize)
        {
            return CDecode.GetMultiUInt(getNewFieldName(strFieldName), ref valAry, (uint)nMaxSize);
        }
        public static bool GetMultiULong(string strFieldName, ref ulong[] valAry, int nMaxSize)
        {
            return CDecode.GetMultiULong(getNewFieldName(strFieldName), ref valAry, (uint)nMaxSize);
        }
        public static bool GetSingleBytes(string strFieldName, ref byte[] valAry, int nMaxSize)
        {
            return CDecode.GetSingleBytes(getNewFieldName(strFieldName), ref valAry, (uint)nMaxSize);
        }
        public static bool GetSingleSInt(string strFieldName, ref int val)
        {
            string strNewFieldName = getNewFieldName(strFieldName);
            uint uValue = 0;
            if (CDecode.GetSingleUInt(strNewFieldName, ref uValue))
            {
                val = (int)uValue;
                return true;
            }
            else
            {
                return CDecode.GetSingleSInt(strNewFieldName, ref val);
            }
        }
        public static bool GetSingleString(string strFieldName, ref string val)
        {
            return CDecode.GetSingleString(getNewFieldName(strFieldName), ref val);
        }
        public static bool GetSingleUInt(string strFieldName, ref uint val)
        {
            return CDecode.GetSingleUInt(getNewFieldName(strFieldName), ref val);
        }
        public static bool GetSingleULong(string strFieldName, ref ulong val)
        {
            return CDecode.GetSingleULong(getNewFieldName(strFieldName), ref val);
        }
        public static void SetTextBoxHighLight(TreeNode treeNode, Frame.SrcTextBox srcTextBox)
        {
            ItemData item = (ItemData)treeNode.Tag;
            srcTextBox.SetHighLight((int)item.Start, (int)item.Length);
        }
        public static bool StartDissect(int dir, byte[] byteArray, int dwRead, int msgId)
        {
            return StartDissect(byteArray, dwRead, msgId);
        }
        public static bool StartDissect(byte[] byteArray, int dwRead, int msgId)
        {
            return CDecode.StartDissect(byteArray, dwRead, msgId);
        }

        #region 旧解码-新解码键值对应关系
        private static readonly Dictionary<string, string> old_New_FieldNameDic = new Dictionary<string, string>
        {
            { "gsm_a.imsi", "e212.imsi" },
            { "gsm_a.cld_party_bcd_num", "gsm_a.dtap.cld_party_bcd_num" },
            { "gsm_a.clg_party_bcd_num", "gsm_a.dtap.clg_party_bcd_num" },
            { "nas_eps.emm.qci", "nas_eps.esm.qci" },
            { "nas_eps.emm.mbr_ul", "nas_eps.esm.mbr_ul" },
            { "nas_eps.emm.mbr_dl", "nas_eps.esm.mbr_dl" },
            { "nas_eps.emm.gbr_ul", "nas_eps.esm.gbr_ul" },
            { "nas_eps.emm.gbr_dl", "nas_eps.esm.gbr_dl" },
            { "nas_eps.emm.embr_ul", "nas_eps.esm.embr_ul" },
            { "nas_eps.emm.embr_dl", "nas_eps.esm.embr_dl" },
            { "nas_eps.emm.egbr_ul", "nas_eps.esm.egbr_ul" },
            { "nas_eps.emm.egbr_dl", "nas_eps.esm.egbr_dl" },
            { "nas_eps.emm.eit", "nas_eps.esm.eit" },
            { "gsm_a.dtap_service_type", "gsm_a.dtap.service_type" },
            { "gsm_a.rr.des_ch_tn", "gsm_a.rr.timeslot" },
            { "gsm_a.lai_lac", "gsm_a.lac" },
            { "gsm_a.cell_ci", "gsm_a.bssmap.cell_ci" },
            { "gsm_a.gm.link_dir", "gsm_a.gm.sm.link_dir" },
            { "nas_eps.esm.pdn_ipv4_int", "nas_eps.esm.pdn_ipv4_uint" },
            { "gsm_a.rr.List_of_ARFCNs", "gsm_a.rr.arfcn_list" },
            { "rrc.DL_DCCH_Message", "rrc.DL_DCCH_Message_element" },
            { "rrc.UL_DCCH_Message", "rrc.UL_DCCH_Message_element" },
            { "rrc.DL_CCCH_Message", "rrc.DL_CCCH_Message_element" },
            { "rrc.UL_CCCH_Message", "rrc.UL_CCCH_Message_element" },
            { "rrc.PCCH_Message", "rrc.PCCH_Message_element" },
            { "rrc.DL_SHCCH_Message", "rrc.DL_SHCCH_Message_element" },
            { "rrc.UL_SHCCH_Message", "rrc.UL_SHCCH_Message_element" },
            { "rrc.BCCH_FACH_Message", "rrc.BCCH_FACH_Message_element" },
            { "rrc.BCCH_BCH_Message", "rrc.BCCH_BCH_Message_element" },
            { "rrc.MCCH_Message", "rrc.MCCH_Message_element" },
            { "rrc.MSCH_Message", "rrc.MSCH_Message_element" },
            { "rrc.InterRATHandoverInfo", "rrc.InterRATHandoverInfo_element" },
            { "rrc.SystemInformation_BCH", "rrc.SystemInformation_BCH_element" },
            { "rrc.System_Information_Container", "rrc.System_Information_Container_element" },
            { "rrc.UE_RadioAccessCapabilityInfo", "rrc.UE_RadioAccessCapabilityInfo_element" },
            { "rrc.MasterInformationBlock", "rrc.MasterInformationBlock_element" },
            { "rrc.SysInfoType1", "rrc.SysInfoType1_element" },
            { "rrc.SysInfoType2", "rrc.SysInfoType2_element" },
            { "rrc.SysInfoType3", "rrc.SysInfoType3_element" },
            { "rrc.SysInfoType4", "rrc.SysInfoType4_element" },
            { "rrc.SysInfoTypeSB2", "rrc.SysInfoTypeSB3_element" },
            { "lte-rrc.BCCH_BCH_Message", "lte-rrc.BCCH_BCH_Message_element" },
            { "lte-rrc.BCCH_DL_SCH_Message", "lte-rrc.BCCH_DL_SCH_Message_element" },
            { "lte-rrc.MCCH_Message", "lte-rrc.MCCH_Message_element" },
            { "lte-rrc.PCCH_Message", "lte-rrc.PCCH_Message_element" },
            { "lte-rrc.DL_CCCH_Message", "lte-rrc.DL_CCCH_Message_element" },
            { "lte-rrc.DL_DCCH_Message", "lte-rrc.DL_DCCH_Message_element" },
            { "lte-rrc.UL_CCCH_Message", "lte-rrc.UL_CCCH_Message_element" },
            { "lte-rrc.UL_DCCH_Message", "lte-rrc.UL_DCCH_Message_element" },
            { "lte-rrc.UECapabilityInformation", "lte-rrc.UECapabilityInformation_element" },
            { "lte-rrc.UE_EUTRA_Capability", "lte-rrc.UE_EUTRA_Capability_element" },
            { "lte-rrc.HandoverCommand", "lte-rrc.HandoverCommand_element" },
            { "lte-rrc.message", "lte-rrc.message_element" },
            { "lte-rrc.systemInformation", "lte-rrc.systemInformation_element" },
            { "lte-rrc.systemInformationBlockType1", "lte-rrc.systemInformationBlockType1_element" },
            { "lte-rrc.messageClassExtension", "lte-rrc.messageClassExtension_element" },
            { "lte-rrc.rrcConnectionSetup", "lte-rrc.rrcConnectionSetup_element" },
            { "lte-rrc.rrcConnectionRelease", "lte-rrc.rrcConnectionRelease_element" },
            { "lte-rrc.rrcConnectionRequest", "lte-rrc.rrcConnectionRequest_element" },
            { "wlan_mgt.ssid", "wlan.ssid" },
            { "wlan_mgt.ds.current_channel", "wlan.ds.current_channel" },
            { "wlan_mgt.fixed.beacon", "wlan.fixed.beacon" },
            { "wlan_mgt.supported_rates_str", "wlan.supported_rates" },
            { "wlan_mgt.extented_supported_rates_str", "wlan.extended_supported_rates" },
            { "gsm_a.qos.max_bitrate_downl", "gsm_a.gm.sm.qos.max_bitrate_downl" },
            { "gsm_a.qos.max_bit_rate_down", "gsm_a.gm.sm.qos.max_bitrate_downl_ext" },
            { "gsm_a.qos.max_bitrate_upl", "gsm_a.gm.sm.qos.max_bitrate_upl" },
            { "gsm_a.qos.max_bit_rate_up", "gsm_a.gm.sm.qos.max_bitrate_upl_ext" },
            { "gsm_a.itc", "gsm_a.dtap.itc" },
            { "gsm_a.dtap_msg_gmm_type", "gsm_a.dtap.msg_gmm_type" },
            { "gsm_a.dtap_msg_sm_type", "gsm_a.dtap.msg_sm_type" },
            { "gsm_a_gm.elem_id", "gsm_a.gm.elem_id" },
            { "gsm_a.qos.delay_cls", "gsm_a.gm.sm.qos.delay_cls" },
            { "gsm_a.dtap_msg_cc_type", "gsm_a.dtap.msg_cc_type" },
            { "gsm_a.dtap_msg_mm_type", "gsm_a.dtap.msg_mm_type" },
            { "gsm_a.dtap_msg_rr_type", "gsm_a.dtap.msg_rr_type" },
            { "gsm_a.dtap_msg_sms_type", "gsm_a.dtap.msg_sms_type" },
            { "gsm_a.dtap_msg_ss_type", "gsm_a.dtap.msg_ss_type" },
            { "gsm_a.dtap_msg_tp_type", "gsm_a.dtap.msg_tp_type" },
            { "gsm_a.rr.arfcn", "gsm_a.rr.single_channel_arfcn" },
            { "gsm_a_dtap.cause", "gsm_a.dtap.cause" },
            { "gsm_a.bcch_arfcn", "gsm_a.rr.bcch_arfcn" },
            { "gsm_a.ncc", "gsm_a.rr.ncc" },
            { "gsm_a.bcc", "gsm_a.rr.bcc" }
        };
        #endregion

        private static string getNewFieldName(string strFieldName)
        {
            string strNewFieldName;
            if (!old_New_FieldNameDic.TryGetValue(strFieldName, out strNewFieldName))
            {
                strNewFieldName = strFieldName;
            }
            return strNewFieldName;
        }
        #endregion
#else
        #region 直接调用旧版动态库
        public static void CloseServer()
        {
            DecodeClient.CloseServer();
        }
        public static bool DissectToTree(byte[] byteArray, int dwRead, int msgId, TreeView treeNode)
        {
            return DecodeClient.DissectToTree(byteArray, dwRead, msgId, treeNode);
        }
        public static int GetFieldsNum(string strFieldName)
        {
            return DecodeClient.GetFieldsNum(strFieldName);
        }
        public static bool GetMultiSInt(string strFieldName, ref int[] valAry, int nMaxSize)
        {
            return DecodeClient.GetMultiSInt(strFieldName, ref valAry, nMaxSize);
        }
        public static bool GetMultiString(string strFieldName, ref string[] valAry, int nMaxSize)
        {
            return DecodeClient.GetMultiString(strFieldName, ref valAry, nMaxSize);
        }
        public static bool GetMultiUInt(string strFieldName, ref uint[] valAry, int nMaxSize)
        {
            return DecodeClient.GetMultiUInt(strFieldName, ref valAry, nMaxSize);
        }
        public static bool GetSingleBytes(string strFieldName, ref byte[] valAry, int nMaxSize)
        {
            return DecodeClient.GetSingleBytes(strFieldName, ref valAry, nMaxSize);
        }
        public static bool GetSingleSInt(string strFieldName, ref int val)
        {
            return DecodeClient.GetSingleSInt(strFieldName, ref val);
        }
        public static bool GetSingleString(string strFieldName, ref string val)
        {
            return DecodeClient.GetSingleString(strFieldName, ref val);
        }
        public static bool GetSingleUInt(string strFieldName, ref uint val)
        {
            return DecodeClient.GetSingleUInt(strFieldName, ref val);
        }
        public static void SetTextBoxHighLight(TreeNode treeNode, Frame.SrcTextBox srcTextBox)
        {
            ItemData item = (ItemData)treeNode.Tag;
            srcTextBox.SetHighLight((int)item.start, (int)item.length);
        }
        public static bool StartDissect(int dir, byte[] byteArray, int dwRead, int msgId)
        {
            return DecodeClient.StartDissect(dir, byteArray, dwRead, msgId);
        }
        #endregion
#endif

        public static byte[] GetMsgSingleBytes(Message msg, string paramName, int byteCount)
        {
            MessageWithSource msgSource = msg as MessageWithSource;
            byte[] valAry = new byte[byteCount];
            StartDissect(msg.Direction, msgSource.Source, msgSource.Length, msgSource.ID);
            GetSingleBytes(paramName, ref valAry, byteCount);
            return valAry;
        }
        public static int GetMsgSingleInt(Message msg, string paramName)
        {
            MessageWithSource msgSource = msg as MessageWithSource;
            int val = int.MaxValue;
            StartDissect(msg.Direction, msgSource.Source, msgSource.Length, msgSource.ID);
            GetSingleSInt(paramName, ref val);
            return val;
        }
        public static string GetMsgSingleString(Message msg, string paramName)
        {
            MessageWithSource msgSource = msg as MessageWithSource;
            string val = "";
            StartDissect(msg.Direction, msgSource.Source, msgSource.Length, msgSource.ID);
            GetSingleString(paramName, ref val);
            return val;
        }
        public static uint GetMsgSingleUInt(Message msg, string paramName)
        {
            MessageWithSource msgSource = msg as MessageWithSource;
            uint val = uint.MaxValue;
            StartDissect(msg.Direction, msgSource.Source, msgSource.Length, msgSource.ID);
            GetSingleUInt(paramName, ref val);
            return val;
        }
        public static ulong GetMsgSingleULong(Message msg, string paramName)
        {
            MessageWithSource msgSource = msg as MessageWithSource;
            ulong val = ulong.MaxValue;

            StartDissect(msg.Direction, msgSource.Source, msgSource.Length, msgSource.ID);
            GetSingleULong(paramName, ref val);

            return val;
        }
        public static uint[] GetMsgMultiUInt(Message msg, string itemCountParamName, string paramName)
        {
            MessageWithSource msgSource = msg as MessageWithSource;
            int count = 0;
            StartDissect(msg.Direction, msgSource.Source, msgSource.Length, msgSource.ID);
            GetSingleSInt(itemCountParamName, ref count);

            uint[] list = new uint[0];
            if (count > 0 && count < 20)
            {
                list = new uint[count];
                StartDissect(msg.Direction, msgSource.Source, msgSource.Length, msgSource.ID);
                GetMultiUInt(paramName, ref list, 20);
            }
            return list;
        }
        public static ulong[] GetMsgMultiULong(Message msg, string itemCountParamName, string paramName)
        {
            MessageWithSource msgSource = msg as MessageWithSource;
            int count = 0;
            StartDissect(msg.Direction, msgSource.Source, msgSource.Length, msgSource.ID);
            GetSingleSInt(itemCountParamName, ref count);

            ulong[] list = new ulong[0];
            if (count > 0 && count < 20)
            {
                list = new ulong[count];
                StartDissect(msg.Direction, msgSource.Source, msgSource.Length, msgSource.ID);
                GetMultiULong(paramName, ref list, 20);
            }
            return list;
        }
    }

}
