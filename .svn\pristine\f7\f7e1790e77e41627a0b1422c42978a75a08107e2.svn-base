﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDiyNoCellCoverQueryByRegion_NR : ZTDiyNoCellCoverQueryByRegion
    {
        private static ZTDiyNoCellCoverQueryByRegion_NR intance = null;
        public new static ZTDiyNoCellCoverQueryByRegion_NR GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDiyNoCellCoverQueryByRegion_NR();
                    }
                }
            }
            return intance;
        }

        protected ZTDiyNoCellCoverQueryByRegion_NR()
            : base()
        {
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NRScan);
        }

        public override string Name
        {
            get { return "无归属信号_NR扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 36000, 36010, this.Name);//////
        }

        #region deal
        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    dealTPs(fileDataManager);
                }
            }
            catch
            {
                //continue
            }
        }

        private void dealTPs(DTFileDataManager fileDataManager)
        {
            List<TestPoint> testPointList = fileDataManager.TestPoints;
            Dictionary<string, NoCellCover> noCellCoverDic = new Dictionary<string, NoCellCover>();
            Dictionary<string, bool> cellrefreshDic = new Dictionary<string, bool>();
            List<string> removeCell = new List<string>();
            List<string> retainCell = new List<string>();
            for (int i = 0; i < testPointList.Count; i++)
            {
                foreach (string cellName in retainCell)
                {
                    cellrefreshDic[cellName] = false;
                }
                TestPoint testPoint = testPointList[i];
                if (isValidTestPoint(testPoint))
                {
                    dealScanTP(noCellCoverDic, cellrefreshDic, testPoint);
                }
                dealCells(noCellCoverDic, cellrefreshDic, removeCell, retainCell);
            }
        }

        private void dealScanTP(Dictionary<string, NoCellCover> noCellCoverDic, Dictionary<string, bool> cellrefreshDic, TestPoint testPoint)
        {
            Dictionary<int, int> groupDic = NRTpHelper.NrScanTpManager.GetCellMaxBeam(testPoint);
            foreach (var index in groupDic.Values)
            {
                int? uarfcn = null;
                short? cpi = null;
                int iResult = validSignal(testPoint, index, ref uarfcn, ref cpi);
                if (iResult != 1)
                {
                    if (iResult == 0)
                    {
                        continue;
                    }
                    break;
                }
                string key = getKey((int)uarfcn, (short)cpi);
                if (noCellCoverDic.ContainsKey(key))
                {
                    noCellCoverDic[key].Add(testPoint, index);
                    cellrefreshDic[key] = true;
                }
                else
                {
                    //ICell curCell = getCell(testPoint, index);
                    noCellCoverDic[key] = new NoCellCover(testPoint, index);
                    cellrefreshDic[key] = true;
                }
            }
        }

        protected override int validSignal(TestPoint testPoint, int index, ref int? uarfcn, ref short? cpi)
        {
            var rsrp = NRTpHelper.NrScanTpManager.GetCellRsrp(testPoint, index);
            uarfcn = (int?)NRTpHelper.NrScanTpManager.GetEARFCN(testPoint, index);
            var pci = (int?)NRTpHelper.NrScanTpManager.GetPCI(testPoint, index);
            if (rsrp == null || uarfcn == null || pci == null || rsrp < rxLevThreshold)
            {
                return -1;
            }
            NRCell cell = testPoint.GetCell_NRScan(index);
            if (cell != null)
            {
                return 0;
            }
            cpi = (short?)pci;
            return 1;
        }

        private void dealCells(Dictionary<string, NoCellCover> noCellCoverDic, Dictionary<string, bool> cellrefreshDic, List<string> removeCell, List<string> retainCell)
        {
            if (cellrefreshDic.Count > 0)
            {
                removeCell.Clear();
                retainCell.Clear();
                foreach (string cellName in cellrefreshDic.Keys)
                {
                    if (!cellrefreshDic[cellName])
                    {
                        NoCellCover item = noCellCoverDic[cellName];
                        if (item.Distance >= distanceThreshold)
                        {
                            noCellCoverList.Add(item);
                        }
                        removeCell.Add(cellName);
                    }
                    else
                    {
                        retainCell.Add(cellName);
                    }
                }
                foreach (string cellName in removeCell)
                {
                    noCellCoverDic.Remove(cellName);
                    cellrefreshDic.Remove(cellName);
                }
            }
        }
        #endregion

        protected override void getResultsAfterQuery()
        {
            for (int i = 0; i < noCellCoverList.Count; i++)
            {
                noCellCoverList[i].GetResult();
            }
            MainModel.FireSetDefaultMapSerialTheme(NRTpHelper.NrScanTpManager.RsrpFullThemeName);
        }
    }
}
