﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Windows.Forms;
using System.Linq;
using MasterCom.RAMS.BackgroundFunc;
using EvtEngineLib;
using MasterCom.MTGis;
namespace MasterCom.RAMS.ZTFunc
{
    public class ZTRtpPacketsLostNewBase : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        public override string Name
        {
            get
            {
                return "单通问题点统计(新)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22111, this.Name);
        }


        //protected override void queryFileToAnalyse()
        //{
        //    MainModel.FileInfos = Condition.FileInfos;
        //}

        protected override bool isValidTestPoint(TestPoint tp)
        {
            return true;
        }

        protected ZTRtpPacketsLostMessageConditon hoCondition = new ZTRtpPacketsLostMessageConditon();

        public ZTRtpPacketsLostNewBase(MainModel mainModel)
            : base(mainModel)
        {
            this.Columns = new List<string>();
            this.IncludeMessage = true;
            this.Columns.Add("lte_volte_Source_SSRC");
            this.Columns.Add("lte_TAC");
            this.Columns.Add("lte_ECI");
            this.Columns.Add("lte_PCI");
            this.Columns.Add("lte_RSRP");
            this.Columns.Add("lte_SINR");

            this.Columns.Add("lte_PUSCH_Power");
            this.Columns.Add("lte_gsm_SC_BCCH");
            this.Columns.Add("lte_gsm_SC_LAC");
            this.Columns.Add("lte_gsm_SC_CI");
            this.Columns.Add("isampleid");
            this.Columns.Add("itime");
            this.Columns.Add("ilongitude");
            this.Columns.Add("ilatitude");

            this.Columns.Add("lte_Pathloss");
            this.Columns.Add("lte_PDSCH_BLER");
            this.Columns.Add("lte_PUSCH_BLER");
            this.Columns.Add("lte_PDSCH_RB_Number");
            this.Columns.Add("lte_volte_RTP_Sequence_Number");

            this.Columns.Add("lte_volte_UL_Source_SSRC");
            this.Columns.Add("lte_PESQMos");
            this.Columns.Add("lte_POLQA_Score_SWB");

        }
        private readonly int signDL = 1;
        private readonly int signUL = 2;
        private readonly string signRTPNumber = "eSam_VOLTE_RTP_Sequence_Number";

        protected override void analyseFiles()
        {
            try
            {
               
                List<FileInfo> files = new List<FileInfo>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0 && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                    {
                        continue;
                    }
                    files.Add(fileInfo);
                }
                clearDataBeforeAnalyseFiles();
                int iloop = 0;
                if (MainModel.IsBackground)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("共读取待分析文件" + files.Count + "个...");
                }
                foreach (FileInfo fileInfo in files)
                {
                    if (MainModel.IsBackground)
                    {
                        if (MainModel.BackgroundStopRequest)
                        {
                            break;
                        }
                        BackgroundFuncManager.GetInstance().ReportBackgroundInfo("正在分析 " + FuncType.ToString() +
                            SubFuncType.ToString() + " 类 " + Name + "，当前文件 " + (++iloop) + "/" + files.Count +
                            "个...文件名：" + fileInfo.Name);
                    }
                    else
                    {
                        WaitBox.Text = "正在分析文件( " + (++iloop) + "/" + files.Count + " )...";
                        WaitBox.ProgressPercent = (int)(iloop * 100.0 / files.Count);
                    }
                    if (filterFile(fileInfo))
                    {
                        continue;
                    }
                    curAnaFileInfo = fileInfo;
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                    //Replay all rtp messages.
                    this.IncludeMessage = true;
                    this.IncludeAllRtpMessage = true;
                    replay();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            catch
            {
                throw;
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }

        }

        ZTRtpPacketsLostMessageSetConditionForm setForm = null;

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTRtpPacketsLostMessageSetConditionForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                setForm.GetCondition(out hoCondition);
                listFileInfo = new List<ZTRtpPacketsLostFileInfo>();
                return true;
            }
            return false;
        }

        Dictionary<string, MsgTime> dicCallingTime = null;
        List<DateTime> listMsgLostTime = null;
        List<ZTRtpPacketsLostMessageInfo> listDLMessageInfo = null;
        List<ZTRtpPacketsLostMessageInfo> listULMessageInfo = null;
        List<ZTRtpPacketsLostFileInfo> listFileInfo = null;
        List<MosInfo> listMosInfo = null;

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file == null)
                    continue;

                dicCallingTime = new Dictionary<string, MsgTime>();
                listDLMessageInfo = new List<ZTRtpPacketsLostMessageInfo>();
                listULMessageInfo = new List<ZTRtpPacketsLostMessageInfo>();

                #region Analysis PacketLoss by messages

                //Get Calling time by messages and events
                fillDicCallingTime(file.Messages, file.Events);

                //DownLoad
                getLossByMessages(file.Messages, signDL);

                //UpLoad
                getLossByMessages(file.Messages, signUL);

                #endregion

                #region Analysis PacketLoss by TestPoint

                getAllMosByTestPoint(file.TestPoints);

                //DownLoad
                getLossTpByLossMessage(signDL, file);

                //UpLoad
                getLossTpByLossMessage(signUL, file);

                //FillInfos
                if (listDLMessageInfo.Count > 0 || listULMessageInfo.Count > 0)
                    listFileInfo.Add(new ZTRtpPacketsLostFileInfo(listDLMessageInfo, listULMessageInfo, file.FileName));

                #endregion
            }

        }

        /// <summary>
        /// Beacuse pockets are different in different callings and with different direction,           
        /// so it is necessary initially to classify pockets into certain types.
        /// </summary>
        private void fillDicCallingTime(List<Model.Message> msgs, List<Event> evts)
        {
            if (msgs == null)
                return;

            int ack = 1107361792;
            int byeOk = 1107443912;
            int mobility = 1093625859;
            int invite = 1107706056;
            int msgLost = 89;
            Dictionary<string, MsgTime> dicTime = new Dictionary<string, MsgTime>();
            listMsgLostTime = new List<DateTime>();
            //保存所有的IMS_SIP_INVITE信令时间
            List<DateTime> listInvite = new List<DateTime>();

            #region Find ACK->Bye and ACK->Mobility

            for (int i = 0; i < msgs.Count; i++)
            {
                if (msgs[i].ID == invite)
                {
                    listInvite.Add(msgs[i].HandsetTime);
                }
                else if (msgs[i].ID == ack)
                {
                    if (i + 1 < msgs.Count)
                    {
                        for (int j = i + 1; j < msgs.Count; j++)
                        {
                            if (msgs[j].ID == ack)
                            {
                                i = j;
                                continue;
                            };

                            if (msgs[j].ID == byeOk)
                            {
                                //ACK -> BYEOK 正常通话
                                if (!dicTime.ContainsKey("Bye_" + msgs[i].Time))
                                {
                                    dicTime.Add("Bye_" + msgs[i].Time,
                                        new MsgTime(new TimePeriod(msgs[i].HandsetTime, msgs[j].HandsetTime),
                                            new TimePeriod(msgs[i].DateTime, msgs[j].DateTime)));
                                }
                                i = j;
                                break;
                            }
                            else if (msgs[j].ID == mobility)
                            {
                                //ACK -> Mobility 发生eSRVCC切换通话
                                if (!dicTime.ContainsKey("Mobility_" + msgs[i].Time))
                                {
                                    dicTime.Add("Mobility_" + msgs[i].Time,
                                        new MsgTime(new TimePeriod(msgs[i].HandsetTime, msgs[j].HandsetTime),
                                            new TimePeriod(msgs[i].DateTime, msgs[j].DateTime)));
                                }
                                i = j;
                                break;
                            }
                            else if (msgs[j].ID == invite)
                            {
                                listInvite.Add(msgs[j].HandsetTime);
                            }
                        }
                    }
                }
            }
            #endregion

            #region Find time of message lost
            if (evts != null)
            {
                evts.ForEach(evt =>
                {
                    if (evt.ID == msgLost)
                    {
                        listMsgLostTime.Add(evt.DateTime);
                    }
                });
            }

            #endregion

            #region Check calling time

            List<string> listKey = new List<string>(dicTime.Keys);
            for (int i = 0; i < listKey.Count; i++)
            {
                if (listKey[i].Contains("Bye_"))
                {
                    //添加通话时段
                    addNewCallingTime(listKey[i], dicTime[listKey[i]]);
                }
                else
                {
                    if (i + 1 < listKey.Count && listKey[i + 1].Contains("Bye_"))
                    {
                        //ACK -> Mobility信令时间段
                        TimePeriod tpStart = dicTime[listKey[i]].HandsetsTime;
                        //ACK -> ByeOK信令时间段
                        TimePeriod tpEnd = dicTime[listKey[i + 1]].HandsetsTime;
                        //Mobility -> ACK信令时间段
                        TimePeriod tpCheck = new TimePeriod(tpStart.EndTime, tpEnd.BeginTime);
                        //切换eSRVCC是否成功,true为成功
                        bool isConnect = false;
                        //判断 Mobility -> ACK 期间有无Invite信令,且Invite信令在Mobility信令发生的10秒内
                        foreach (DateTime dt in listInvite)
                        {
                            if (tpCheck.Contains(dt) && (dt - tpCheck.BeginTime).TotalSeconds <= 10)
                            {
                                isConnect = true;
                                break;
                            }
                        }

                        if (isConnect)
                        {
                            addNewCallingTime("Invite_" + tpStart.BeginTime.ToShortTimeString(), new MsgTime(
                                new TimePeriod(tpStart.BeginTime, tpEnd.EndTime), new TimePeriod(
                                    dicTime[listKey[i]].CompleteTime.BeginTime, dicTime[listKey[i + 1]].CompleteTime.EndTime)));
                            i++;
                        }
                        else
                        {
                            addNewCallingTime(listKey[i], dicTime[listKey[i]]);
                            addNewCallingTime(listKey[i + 1], dicTime[listKey[i + 1]]);
                            i++;
                        }
                    }
                    else
                    {
                        addNewCallingTime(listKey[i], dicTime[listKey[i]]);
                    }
                }
            }

            #endregion
        }

        private void addNewCallingTime(string key, MsgTime val)
        {
            if (!isMsgLost(val.HandsetsTime) && !dicCallingTime.ContainsKey(key))
                dicCallingTime.Add(key, val);
        }

        private bool isMsgLost(TimePeriod timePeriod)
        {
            bool result = false;
            listMsgLostTime.ForEach(t =>
            {
                if (timePeriod.Contains(t))
                    result = true;
            });
            return result;
        }

        /// <summary>
        /// To get LossMessages through time interval between two rtp messages.
        /// </summary>
        /// <param name="Messages">The messages in a file</param>
        /// <param name="direction">The sign of direction</param>
        private void getLossByMessages(List<Model.Message> Messages, int direction)
        {
            int indexTp = 0;
            Model.Message msgTol = null;
            Model.Message msgNext = null;
            List<ZTRtpPacketsLostMessageInfo> listLoss = direction == signDL ? listDLMessageInfo : listULMessageInfo;
            float lossTime = hoCondition.LossTime * 1000;
            //循环所有通话时段
            foreach (MsgTime msgTime in dicCallingTime.Values)
            {
                //满足在本次通话时段内的IMS_RTP_SN_And_Payload信令合集
                List<Model.Message> listMessage = new List<Model.Message>();

                for (int i = indexTp; i < Messages.Count; i++)
                {
                    msgTol = Messages[i];
                    //msg 为 IMS_RTP_SN_And_Payload信令且上下行方向相同
                    if (msgTol != null && msgTol.ID == 2147426825 && msgTol.Direction == direction)
                    {
                        //IMS_RTP_SN_And_Payload信令在本次循环的通话时段内
                        if (msgTol.HandsetTime >= msgTime.HandsetsTime.BeginTime &&
                            msgTol.HandsetTime <= msgTime.HandsetsTime.EndTime)
                        {
                            listMessage.Add(msgTol);
                        }
                        else if (msgTol.HandsetTime > msgTime.HandsetsTime.EndTime)
                        {
                            indexTp = i;
                            break;
                        }
                    }
                }

                for (int i = 0; i < listMessage.Count - 1; i++)
                {
                    msgTol = listMessage[i];
                    msgNext = listMessage[i + 1];
                    //前后2个IMS_RTP_SN_And_Payload信令时间大于设定条件则为丢包
                    if ((msgNext.HandsetTime - msgTol.HandsetTime).TotalMilliseconds > lossTime)
                    {
                        ZTRtpPacketsLostMessageInfo msgInfo = new ZTRtpPacketsLostMessageInfo(
                            msgTol, msgNext);
                        msgInfo.SCallTime = msgTime.HandsetsTime.BeginTime;
                        msgInfo.SCallCompleteTime = msgTime.CompleteTime.BeginTime;
                        listLoss.Add(msgInfo);
                    }
                }
            }

        }

        private long GetLossNum(Model.Message msgStart, Model.Message msgEnd)
        {
            long result = 0;
            long lossNumStart = 0;
            long lossNumEnd = 0;
            MessageWithSource msgStartWithSource = msgStart as MessageWithSource;
            MessageWithSource msgEndWithSource = msgEnd as MessageWithSource;

            OwnMsgDecode.StartDissect(msgStartWithSource.Source, msgStartWithSource.ID);
            OwnMsgDecode.GetIntValue(signRTPNumber, ref lossNumStart);
            OwnMsgDecode.StartDissect(msgEndWithSource.Source, msgEndWithSource.ID);
            OwnMsgDecode.GetIntValue(signRTPNumber, ref lossNumEnd);

            result = lossNumEnd - lossNumStart;
            return result;
        }

        /// <summary>
        /// According to Loss Messages, to find Loss Testpoints.
        /// </summary>
        /// <param name="direction">The messages in a file</param>
        /// <param name="file">The information of file</param>
        private void getLossTpByLossMessage(int direction, DTFileDataManager file)
        {
            List<ZTRtpPacketsLostMessageInfo> listLoss = direction == signDL ? listDLMessageInfo : listULMessageInfo;
            string ssrc = direction == signDL ? "lte_volte_Source_SSRC" : "lte_volte_UL_Source_SSRC";
            List<TestPoint> tps = file.TestPoints;
            FileInfo fileInfo = file.GetFileInfo();
            string AreaString = string.Empty;
            //string AreaTypeString = string.Empty;

            if (fileInfo != null)
            {
                AreaString = fileInfo.AreaString;
                //AreaTypeString = fileInfo.AreaTypeString;
            }

            if (listLoss != null && tps != null
                && listLoss.Count > 0)
            {
                int index = 0;

                listLoss.ForEach(msg =>
                {
                    for (int i = index; i < tps.Count; i++)
                    {
                        if (tps[i] != null && tps[i][ssrc] != null
                            && tps[i].DateTime >= msg.StartMsg.DateTime)
                        {
                            index = i + 1;
                            TestPoint tpTol = tps[i];
                            msg.TestPoints.Add(tpTol);
                            msg.Area = AreaString;
                            msg.Grid = getGrid(tpTol.Longitude, tpTol.Latitude);//更改为 预存区域
                            msg.FileName = file.FileName;
                            msg.Direction = direction == signDL ? "下行" : "上行";
                            msg.SLossTime = msg.StartMsg.HandsetTime;
                            msg.SLossCompleteTime = msg.StartMsg.DateTime;
                            //msg.Longitude = tpTol.Longitude;
                            //msg.Latitude = tpTol.Latitude;
                            //msg.LossTime = msg.EndMsg.HandsetTime - msg.StartMsg.HandsetTime;
                            //msg.LossNumber = GetLossNum(msg.StartMsg, msg.EndMsg);
                            //msg.LossCell = tpTol.GetMainCell_LTE();

                            msg.FillData(tpTol);
                            //msg.RSRP = Convert.ToSingle(tpTol["lte_RSRP"]);
                            //msg.SINR = Convert.ToSingle(tpTol["lte_SINR"]);
                            //msg.PathLoss = Convert.ToSingle(tpTol["lte_Pathloss"]);
                            //msg.Pdsch_bler = Convert.ToSingle(tpTol["lte_PDSCH_BLER"]);
                            //msg.Pusch_bler = Convert.ToSingle(tpTol["lte_PUSCH_BLER"]);
                            //msg.Uetxpower = Convert.ToInt32(tpTol["lte_PUSCH_Power"]);
                            //msg.RBCount = Convert.ToInt32(tpTol["lte_PDSCH_RB_Number"]);

                            getMosInfo(msg);
                            break;
                        }
                    }
                });
            }
        }
        private string getGrid(double longitude, double latitude)
        {
            foreach (string strKey in regionMopDic.Keys)
            {
                if (regionMopDic[strKey].CheckPointInRegion(longitude, latitude))
                {
                    return strKey;
                }
            }
            return null;
        }
        protected override void getReadyBeforeQuery()
        {
            //实例化区域，并初始化选择区域
            regionMopDic = new Dictionary<string, MapOperation2>();
            InitRegionMop2();
        }

        Dictionary<string, MapOperation2> regionMopDic = null;
        private void InitRegionMop2()
        {
            List<ResvRegion> resvRegions = MainModel.SearchGeometrys.SelectedResvRegions;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;

            if (resvRegions != null && resvRegions.Count > 0)  //预存区域
            {
                foreach (ResvRegion region in resvRegions)
                {
                    if (!regionMopDic.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon((MapWinGIS.Shape)region.Shape);
                        regionMopDic.Add(region.RegionName, mapOp2);
                    }
                }
            }
            else if (gmt != null)//单个区域
            {
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon((MapWinGIS.Shape)gmt);
                regionMopDic.Add("当前区域", mapOp2);
            }
        }

        private void getAllMosByTestPoint(List<TestPoint> listTp)
        {
            string MOSParamName = string.Empty;
            int mosGate = 5;
            listMosInfo = new List<MosInfo>();

            foreach (TestPoint tp in listTp)//通过采样点mos值，确定回放的时间段
            {
                if (MOSParamName == string.Empty)
                {
                    float? pesq = (float?)tp["lte_PESQMos"];
                    float? polqa = (float?)tp["lte_POLQA_Score_SWB"];
                    if (pesq != null && pesq > 0 && pesq <= 5)
                    {
                        MOSParamName = "lte_PESQMos";
                    }
                    else if (polqa != null && polqa > 0 && polqa <= 5)
                    {
                        MOSParamName = "lte_POLQA_Score_SWB";
                    }
                    else
                    {
                        continue;
                    }
                }
                float? mos = (float?)tp[MOSParamName];
                if (mos != null && mos > 0 && mos < mosGate)
                {
                    listMosInfo.Add(new MosInfo(
                        new TimePeriod(tp.DateTime.AddSeconds(-10), tp.DateTime.AddSeconds(-2)),
                        tp.DateTime, (float)mos));
                }
            }
        }

        private void getMosInfo(ZTRtpPacketsLostMessageInfo lostInfo)
        {
            if (listMosInfo.Count == 0)
                return;
            //记录在RTP丢包时间段内存在的MOS周期
            List<MosInfo> listMosInPeriod = new List<MosInfo>();
            string timeFormat = "yy-MM-dd HH:mm:ss.fff";

            foreach (var mosInfo in listMosInfo)
            {
                //Mos值是从采样点中获取,采样点只有DateTime,因此要和对应的RTP丢包时间段的DateTime去比较
                if (mosInfo.MosPeriod.BeginTime > lostInfo.EndMsg.DateTime)
                {
                    break;
                }
                if (mosInfo.MosPeriod.EndTime < lostInfo.StartMsg.DateTime)
                {
                    continue;
                }

                listMosInPeriod.Add(mosInfo);
            }

            if (listMosInPeriod.Count == 0)
            {
                lostInfo.MosTime = string.Empty;
                lostInfo.MosVal = null;
                lostInfo.MosAveVal = null;
            }
            else if (listMosInPeriod.Count == 1)
            {
                lostInfo.MosTime = listMosInPeriod[0].PointTime.ToString(timeFormat);
                lostInfo.MosVal = listMosInPeriod[0].MosVal;
                lostInfo.MosAveVal = listMosInPeriod[0].MosVal;
            }
            else
            {
                float val = 0;
                listMosInPeriod.ForEach(m =>
                {
                    val += m.MosVal;
                    lostInfo.MosTime += m.PointTime.ToString(timeFormat) + ",";
                });
                lostInfo.MosTime.TrimEnd(',');
                lostInfo.MosVal = listMosInPeriod[0].MosVal;
                lostInfo.MosAveVal = val / listMosInPeriod.Count;
            }
        }


        protected override void fireShowForm()
        {
            if (listFileInfo.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }

            ZTRtpPacketsLostShowForm frm = MainModel.GetInstance().CreateResultForm(typeof(ZTRtpPacketsLostShowForm)) as ZTRtpPacketsLostShowForm;
            frm.FillData(listFileInfo);
            frm.Visible = true;
            frm.BringToFront();

        }



    }
    public class ZTRtpPacketsLostNewByRegion : ZTRtpPacketsLostNewBase
    {
        private static ZTRtpPacketsLostNewByRegion instance = null;
        public static new ZTRtpPacketsLostNewByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTRtpPacketsLostNewByRegion(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        public ZTRtpPacketsLostNewByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }
    
        public override string Name
        {
            get
            {
                return "单通问题点统计(按区域)";
            }
        }
    }
    public class ZTRtpPacketsLostNewByFile : ZTRtpPacketsLostNewBase
    {
        private static ZTRtpPacketsLostNewByFile instance = null;
        public static new ZTRtpPacketsLostNewByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTRtpPacketsLostNewByFile(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        public ZTRtpPacketsLostNewByFile(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get
            {
                return "单通问题点统计(按文件)";
            }
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            return true;
        }
    }
}
