﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLTECSFBDelayAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTLTECSFBDelayAnaListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewCSFBDelayAna = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCSFBResult = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCSFBType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMsgNameBegin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMsgTimeBegin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMsgNameEnd = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMsgTimeEnd = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCSFBDelayTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellNameBegin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellNameEnd = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellEndBcch = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellEndRxlev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellEndRxqual = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellEndRxlevMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellEndBcchBest = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnIsBackOnBestBcch = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnHasSetBestBcch = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnIsTAUpdate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnReDirectCarrierInfo = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStartingARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBandIndicator = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFollowingARFCNs = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnARFCNTotal = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCSFBDelayAna)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay,
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 98);
            this.ctxMenu.Opening += new System.ComponentModel.CancelEventHandler(this.ctxMenu_Opening);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(129, 22);
            this.miReplay.Text = "回放";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(126, 6);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(129, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // ListViewCSFBDelayAna
            // 
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnStatSN);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnFileName);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnFileType);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnCSFBResult);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnCSFBType);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnMsgNameBegin);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnMsgTimeBegin);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnMsgNameEnd);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnMsgTimeEnd);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnCSFBDelayTime);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnLongitude);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnLatitude);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnCellNameBegin);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnCellNameEnd);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnCellEndBcch);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnCellEndRxlev);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnCellEndRxqual);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnCellEndRxlevMax);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnCellEndBcchBest);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnIsBackOnBestBcch);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnHasSetBestBcch);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnIsTAUpdate);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnReDirectCarrierInfo);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnStartingARFCN);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnBandIndicator);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnFollowingARFCNs);
            this.ListViewCSFBDelayAna.AllColumns.Add(this.olvColumnARFCNTotal);
            this.ListViewCSFBDelayAna.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.ListViewCSFBDelayAna.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnFileName,
            this.olvColumnFileType,
            this.olvColumnCSFBResult,
            this.olvColumnCSFBType,
            this.olvColumnMsgNameBegin,
            this.olvColumnMsgTimeBegin,
            this.olvColumnMsgNameEnd,
            this.olvColumnMsgTimeEnd,
            this.olvColumnCSFBDelayTime,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnCellNameBegin,
            this.olvColumnCellNameEnd,
            this.olvColumnCellEndBcch,
            this.olvColumnCellEndRxlev,
            this.olvColumnCellEndRxqual,
            this.olvColumnCellEndRxlevMax,
            this.olvColumnCellEndBcchBest,
            this.olvColumnIsBackOnBestBcch,
            this.olvColumnHasSetBestBcch,
            this.olvColumnIsTAUpdate,
            this.olvColumnReDirectCarrierInfo,
            this.olvColumnStartingARFCN,
            this.olvColumnBandIndicator,
            this.olvColumnFollowingARFCNs,
            this.olvColumnARFCNTotal});
            this.ListViewCSFBDelayAna.ContextMenuStrip = this.ctxMenu;
            this.ListViewCSFBDelayAna.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewCSFBDelayAna.FullRowSelect = true;
            this.ListViewCSFBDelayAna.GridLines = true;
            this.ListViewCSFBDelayAna.HeaderWordWrap = true;
            this.ListViewCSFBDelayAna.IsNeedShowOverlay = false;
            this.ListViewCSFBDelayAna.Location = new System.Drawing.Point(1, 1);
            this.ListViewCSFBDelayAna.Name = "ListViewCSFBDelayAna";
            this.ListViewCSFBDelayAna.OwnerDraw = true;
            this.ListViewCSFBDelayAna.ShowGroups = false;
            this.ListViewCSFBDelayAna.Size = new System.Drawing.Size(1368, 501);
            this.ListViewCSFBDelayAna.TabIndex = 7;
            this.ListViewCSFBDelayAna.UseCompatibleStateImageBehavior = false;
            this.ListViewCSFBDelayAna.View = System.Windows.Forms.View.Details;
            this.ListViewCSFBDelayAna.VirtualMode = true;
            this.ListViewCSFBDelayAna.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.ListViewCSFBDelayAna_MouseDoubleClick);
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            this.olvColumnStatSN.Width = 80;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 120;
            // 
            // olvColumnFileType
            // 
            this.olvColumnFileType.HeaderFont = null;
            this.olvColumnFileType.Text = "文件类型";
            // 
            // olvColumnCSFBResult
            // 
            this.olvColumnCSFBResult.HeaderFont = null;
            this.olvColumnCSFBResult.Text = "回落结果";
            // 
            // olvColumnCSFBType
            // 
            this.olvColumnCSFBType.HeaderFont = null;
            this.olvColumnCSFBType.Text = "回落制式";
            // 
            // olvColumnMsgNameBegin
            // 
            this.olvColumnMsgNameBegin.HeaderFont = null;
            this.olvColumnMsgNameBegin.Text = "前一信令";
            this.olvColumnMsgNameBegin.Width = 150;
            // 
            // olvColumnMsgTimeBegin
            // 
            this.olvColumnMsgTimeBegin.HeaderFont = null;
            this.olvColumnMsgTimeBegin.Text = "前一信令时间";
            this.olvColumnMsgTimeBegin.Width = 100;
            // 
            // olvColumnMsgNameEnd
            // 
            this.olvColumnMsgNameEnd.HeaderFont = null;
            this.olvColumnMsgNameEnd.Text = "后一信令";
            this.olvColumnMsgNameEnd.Width = 150;
            // 
            // olvColumnMsgTimeEnd
            // 
            this.olvColumnMsgTimeEnd.HeaderFont = null;
            this.olvColumnMsgTimeEnd.Text = "后一信令时间";
            this.olvColumnMsgTimeEnd.Width = 100;
            // 
            // olvColumnCSFBDelayTime
            // 
            this.olvColumnCSFBDelayTime.HeaderFont = null;
            this.olvColumnCSFBDelayTime.Text = "时延(毫秒)";
            this.olvColumnCSFBDelayTime.Width = 70;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 90;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 90;
            // 
            // olvColumnCellNameBegin
            // 
            this.olvColumnCellNameBegin.HeaderFont = null;
            this.olvColumnCellNameBegin.Text = "回落前小区";
            this.olvColumnCellNameBegin.Width = 100;
            // 
            // olvColumnCellNameEnd
            // 
            this.olvColumnCellNameEnd.HeaderFont = null;
            this.olvColumnCellNameEnd.Text = "回落后小区";
            this.olvColumnCellNameEnd.Width = 100;
            // 
            // olvColumnCellEndBcch
            // 
            this.olvColumnCellEndBcch.HeaderFont = null;
            this.olvColumnCellEndBcch.Text = "回落后小区频点";
            this.olvColumnCellEndBcch.Width = 80;
            // 
            // olvColumnCellEndRxlev
            // 
            this.olvColumnCellEndRxlev.HeaderFont = null;
            this.olvColumnCellEndRxlev.Text = "回落后小区RxLevSub(dBm)";
            this.olvColumnCellEndRxlev.Width = 100;
            // 
            // olvColumnCellEndRxqual
            // 
            this.olvColumnCellEndRxqual.HeaderFont = null;
            this.olvColumnCellEndRxqual.Text = "回落后小区RxQualSub";
            this.olvColumnCellEndRxqual.Width = 100;
            // 
            // olvColumnCellEndRxlevMax
            // 
            this.olvColumnCellEndRxlevMax.HeaderFont = null;
            this.olvColumnCellEndRxlevMax.Text = "回落后最强RxLevSub";
            this.olvColumnCellEndRxlevMax.Width = 80;
            // 
            // olvColumnCellEndBcchBest
            // 
            this.olvColumnCellEndBcchBest.HeaderFont = null;
            this.olvColumnCellEndBcchBest.Text = "回落后最强频点";
            // 
            // olvColumnIsBackOnBestBcch
            // 
            this.olvColumnIsBackOnBestBcch.HeaderFont = null;
            this.olvColumnIsBackOnBestBcch.Text = "是否回落最强频点";
            // 
            // olvColumnHasSetBestBcch
            // 
            this.olvColumnHasSetBestBcch.HeaderFont = null;
            this.olvColumnHasSetBestBcch.Text = "最强频点是否已配置";
            this.olvColumnHasSetBestBcch.Width = 70;
            // 
            // olvColumnIsTAUpdate
            // 
            this.olvColumnIsTAUpdate.HeaderFont = null;
            this.olvColumnIsTAUpdate.Text = "是否有TA/LAC更新";
            this.olvColumnIsTAUpdate.Width = 70;
            // 
            // olvColumnReDirectCarrierInfo
            // 
            this.olvColumnReDirectCarrierInfo.HeaderFont = null;
            this.olvColumnReDirectCarrierInfo.Text = "ReDirectCarrierInfo";
            // 
            // olvColumnStartingARFCN
            // 
            this.olvColumnStartingARFCN.HeaderFont = null;
            this.olvColumnStartingARFCN.Text = "StartingARFCN";
            // 
            // olvColumnBandIndicator
            // 
            this.olvColumnBandIndicator.HeaderFont = null;
            this.olvColumnBandIndicator.Text = "BandIndicator";
            // 
            // olvColumnFollowingARFCNs
            // 
            this.olvColumnFollowingARFCNs.HeaderFont = null;
            this.olvColumnFollowingARFCNs.Text = "FollowingARFCNs";
            // 
            // olvColumnARFCNTotal
            // 
            this.olvColumnARFCNTotal.HeaderFont = null;
            this.olvColumnARFCNTotal.Text = "ARFCNs";
            // 
            // ZTLTECSFBDelayAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1370, 502);
            this.Controls.Add(this.ListViewCSFBDelayAna);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTLTECSFBDelayAnaListForm";
            this.Text = "CSFB回落时延分析";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCSFBDelayAna)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private BrightIdeasSoftware.TreeListView ListViewCSFBDelayAna;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnFileType;
        private BrightIdeasSoftware.OLVColumn olvColumnCSFBResult;
        private BrightIdeasSoftware.OLVColumn olvColumnCSFBType;
        private BrightIdeasSoftware.OLVColumn olvColumnMsgNameBegin;
        private BrightIdeasSoftware.OLVColumn olvColumnCellNameBegin;
        private BrightIdeasSoftware.OLVColumn olvColumnMsgTimeBegin;
        private BrightIdeasSoftware.OLVColumn olvColumnMsgNameEnd;
        private BrightIdeasSoftware.OLVColumn olvColumnMsgTimeEnd;
        private BrightIdeasSoftware.OLVColumn olvColumnCSFBDelayTime;
        private BrightIdeasSoftware.OLVColumn olvColumnCellNameEnd;
        private BrightIdeasSoftware.OLVColumn olvColumnIsTAUpdate;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private BrightIdeasSoftware.OLVColumn olvColumnReDirectCarrierInfo;
        private BrightIdeasSoftware.OLVColumn olvColumnStartingARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnBandIndicator;
        private BrightIdeasSoftware.OLVColumn olvColumnFollowingARFCNs;
        private BrightIdeasSoftware.OLVColumn olvColumnARFCNTotal;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnCellEndBcch;
        private BrightIdeasSoftware.OLVColumn olvColumnCellEndRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnCellEndRxqual;
        private BrightIdeasSoftware.OLVColumn olvColumnIsBackOnBestBcch;
        private BrightIdeasSoftware.OLVColumn olvColumnHasSetBestBcch;
        private BrightIdeasSoftware.OLVColumn olvColumnCellEndRxlevMax;
        private BrightIdeasSoftware.OLVColumn olvColumnCellEndBcchBest;

    }
}