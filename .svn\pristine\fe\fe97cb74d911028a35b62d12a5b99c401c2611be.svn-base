using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class ProjectManager
    {
        public static ProjectManager GetInstance()
        {
            return instance;
        }

        private static ProjectManager instance = new ProjectManager();

        private ProjectManager()
        {
        }

        public void Clear()
        {
            areaTypeMap.Clear();
            batchMap.Clear();
        }

        public CategoryEnum Projects
        {
            get { return (CategoryEnum)CategoryManager.GetInstance()["Project"]; }
        }

        public List<CategoryEnumItem> GetAreaTypes(int projectID)
        {
            return areaTypeMap.ContainsKey(projectID) ? areaTypeMap[projectID] : null;
        }

        public List<CategoryEnumItem> GetProjectsByAreaType(int areaTypeID)
        {
            CategoryEnumItem areaType = ((CategoryEnum)CategoryManager.GetInstance()["AreaType"])[areaTypeID];
            List<CategoryEnumItem> projects = new List<CategoryEnumItem>();
            if (areaType != null)
            {
                foreach (int projectID in areaTypeMap.Keys)
                {
                    if (areaTypeMap[projectID].Contains(areaType))
                    {
                        CategoryEnumItem item = ((CategoryEnum)CategoryManager.GetInstance()["Project"])[projectID];
                        if (item != null)
                        {
                            projects.Add(item);
                        }
                    }
                }
            }
            return projects;
        }

        public bool IsBatch(int projectID)
        {
            if (batchMap.ContainsKey(projectID))
            {
                return batchMap[projectID];
            }
            return false;
        }

        public void AddArea(int projectID, CategoryEnumItem areaType)
        {
            if (areaType != null)
            {
                if (!areaTypeMap.ContainsKey(projectID))
                {
                    areaTypeMap[projectID] = new List<CategoryEnumItem>();
                }
                if (!areaTypeMap[projectID].Contains(areaType))
                {
                    areaTypeMap[projectID].Add(areaType);
                }
            }
        }

        public void SetBatch(int projectID, bool batch)
        {
            batchMap[projectID] = batch;
        }

        private readonly Dictionary<int, List<CategoryEnumItem>> areaTypeMap = new Dictionary<int, List<CategoryEnumItem>>();

        private readonly Dictionary<int, bool> batchMap = new Dictionary<int, bool>();

        public string GetStrProjetName(int iProjectId)
        {
            string strProName = "";
            CategoryEnumItem[] projItems = ((CategoryEnum)CategoryManager.GetInstance()["Project"]).Items;
            foreach (CategoryEnumItem cei in projItems)
            {
                if (cei.ID == iProjectId)
                {
                    strProName = cei.Name;
                    break;
                }
            }
            return strProName;
        }
    }
}
