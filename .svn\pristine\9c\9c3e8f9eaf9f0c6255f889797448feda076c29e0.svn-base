﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryAreaKPI : AreaKpiQueryBase
    {
        AreaReportTemplate curTemplate = null;

        Dictionary<AreaBase, CKpiValue> KpiValueDic;

        List<string> formulas;

        List<TemplateColumn> cols;

        public override string Name
        {
            get { return "报表统计"; }
        }

        public QueryAreaKPI()
            : base(MainModel.GetInstance())
        {
            this.isStatLatestOnly = true;
            KpiValueDic = new Dictionary<AreaBase, CKpiValue>();
            formulas = new List<string>();
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 31000, 31003, this.Name);
        }

        protected override bool setConditionDlg()
        {
            ReportConditionDlg dlg = new ReportConditionDlg(TemplateMngr.Instance);
            dlg.CheckStatLatestOnly = this.isStatLatestOnly;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            curTemplate = dlg.SelTemplate;
            this.isStatLatestOnly = dlg.CheckStatLatestOnly;
            return true;
        }

        protected override List<string> getFormulas()
        {
            List<string> formulasTmp = new List<string>();
            if (curTemplate == null)
                formulasTmp.Add("-1,-1,-1");
            else
            {
                foreach (TemplateColumn col in curTemplate.Columns)
                {
                    formulasTmp.Add(col.Expression);
                }
            }
            return formulasTmp;
        }

        protected override void searchData()
        {
            KpiValueDic.Clear();
            formulas = getFormulas();
            cols = getColumns();
            
            AreaKpiQueryDirectly query = new AreaKpiQueryDirectly();
            query.IsStatLatestOnly = this.isStatLatestOnly;
            QueryCondition searchCond = archiveCondition.GetBaseConditionBackUp();
            query.SetQueryCondition(searchCond);
            query.SetFormula(formulas);
            query.SetTypes(getTypeIds());
            query.SetColumns(cols);
            query.Query();

            KpiValueDic = query.KpiValueDic;
        }

        private new Dictionary<int, Dictionary<int, AreaBase>> getTypeIds()
        {
            Dictionary<int, Dictionary<int, AreaBase>> typeIds = new Dictionary<int, Dictionary<int, AreaBase>>();

            AreaRank countryRank = ZTAreaManager.Instance.GetRank(1);
            foreach (AreaBase root in rootLeafDic.Keys)
            {            
                Dictionary<int, AreaBase> cityIds = new Dictionary<int, AreaBase>();
                cityIds[root.AreaID] = root;
                typeIds[root.AreaTypeID] = cityIds;
                foreach (AreaBase vil in rootLeafDic[root])
                {
                    AreaBase country = vil.GetUpperSectionArea(countryRank);

                    Dictionary<int, AreaBase> ids;
                    if (!typeIds.TryGetValue(country.AreaTypeID, out ids))
                    {
                        ids = new Dictionary<int, AreaBase>();
                        typeIds[country.AreaTypeID] = ids;
                    }
                    ids[country.AreaID] = country;
                }
            }
            return typeIds;
        }

        private List<TemplateColumn> getColumns()
        {
            List<TemplateColumn> colsTmp = new List<TemplateColumn>();
            foreach (TemplateColumn col in curTemplate.Columns)
            {
                colsTmp.Add(col);
            }
            return colsTmp;
        }

        protected override void fireShowForm()
        {
            AreaKPIReportForm form = MainModel.CreateResultForm(typeof(AreaKPIReportForm)) as AreaKPIReportForm;
            form.FillData(curTemplate, KpiValueDic, isStatLatestOnly, formulas, archiveCondition, cols);
            form.Visible = true;
            form.BringToFront();
        }
    }

    public class CKpiValue
    {
        public AreaBase Area
        {
            get;
            set;
        }

        public Dictionary<int, FileInfo> IdFileDic
        {
            get;
            set;
        }

        public Dictionary<TemplateColumn, double> KpiValueDic
        {
            get;
            set;
        }

        public CKpiValue(AreaBase area)
        {
            Area = area;
            IdFileDic = new Dictionary<int, FileInfo>();
            KpiValueDic = new Dictionary<TemplateColumn, double>();
        }

        public void CalcValue(AreaKPIDataGroup<AreaBase> group, List<TemplateColumn> columns)
        {
            foreach (int id in group.FileIDDic.Keys)
            {
                IdFileDic[id] = group.FileIDDic[id];
            }

            foreach (TemplateColumn col in columns)
            {
                double dValue = group.CalcFormula((CarrierType)col.CarrierID, (int)col.MoMtFlag, col.Expression);
                KpiValueDic[col] = dValue;
            }
        }

        public object[] getDetail()
        {
            object[] row = new object[4 + KpiValueDic.Count];
            int i = 0;
            row[i++] = Area;
            row[i++] = Area.Rank;
            row[i++] = Area.Centroid == null ? "-" : Area.Centroid.x.ToString();
            row[i++] = Area.Centroid == null ? "-" : Area.Centroid.y.ToString();
            foreach (TemplateColumn col in KpiValueDic.Keys)
            {
                double val = KpiValueDic[col];
                if (double.IsNaN(val))
                {
                    row[i++] = "-";
                }
                else
                {
                    row[i++] = val;
                }
            }
            return row;
        }
    }

    public class AreaKPIDataGroup<T> : IComparable<AreaKPIDataGroup<T>>
    {
        private bool isStatLatestOnly { get; set; } = false;
        public AreaKPIDataGroup(T area)
        {
            this.Area = area;
        }
        public AreaKPIDataGroup(T area, bool isStatLatestOnly)
            : this(area)
        {
            this.isStatLatestOnly = isStatLatestOnly;
        }

        public override string ToString()
        {
            string info = string.Empty;
            if (Area != null)
            {
                info = Area.ToString();
            }
            return info;
        }

        public T Area
        {
            get;
            private set;
        }
        CarrierStatDataHub cmDataHub = null;
        CarrierStatDataHub cuDataHub = null;
        CarrierStatDataHub ctDataHub = null;

        public void Merge(AreaKPIDataGroup<T> other)
        {
            foreach (KeyValuePair<int, FileInfo> item in other.FileIDDic)
            {
                FileIDDic[item.Key] = item.Value;
            }
            if (other.cmDataHub != null)
            {
                if (this.cmDataHub == null)
                {
                    this.cmDataHub = other.cmDataHub.Clone();
                }
                else
                {
                    this.cmDataHub.Merge(other.cmDataHub);
                }
            }
            if (other.ctDataHub != null)
            {
                if (this.ctDataHub == null)
                {
                    this.ctDataHub = other.ctDataHub.Clone();
                }
                else
                {
                    this.ctDataHub.Merge(other.ctDataHub);
                }
            }
            if (other.cuDataHub != null)
            {
                if (this.cuDataHub == null)
                {
                    this.cuDataHub = other.cuDataHub.Clone();
                }
                else
                {
                    this.cuDataHub.Merge(other.cuDataHub);
                }
            }
        }

        public AreaKPIDataGroup<T> Clone()
        {
            AreaKPIDataGroup<T> grp = new AreaKPIDataGroup<T>(this.Area,this.isStatLatestOnly);
            if (this.cmDataHub != null)
            {
                grp.cmDataHub = this.cmDataHub.Clone();
            }
            if (this.ctDataHub != null)
            {
                grp.ctDataHub = this.ctDataHub.Clone();
            }
            if (this.cuDataHub != null)
            {
                grp.cuDataHub = this.cuDataHub.Clone();
            }
            return grp;
        }

        private class ServiceLatestDataHub
        {
            public int ServiceID
            {
                get;
                private set;
            }
            public DateTime TestDate
            {
                get;
                private set;
            }
            public Dictionary<int, FileInfo> FileDic { get; set; }
            public CarrierStatDataHub DataHub
            {
                get;
                private set;
            }
            public ServiceLatestDataHub(FileInfo fileInfo)
            {
                FileDic = new Dictionary<int, FileInfo>();
                DataHub = new CarrierStatDataHub((CarrierType)fileInfo.CarrierType);
                this.ServiceID = fileInfo.ServiceType;
                this.TestDate = DateTime.Parse(fileInfo.BeginTimeString).Date;
            }

            public void AddStatData(FileInfo fi, KPIStatDataBase data)
            {
                DateTime date = DateTime.Parse(fi.BeginTimeString).Date;
                if (TestDate < date)
                {//原测试数据相对当前传入测试数据日期更早，需清空
                    TestDate = date;
                    DataHub = new CarrierStatDataHub((CarrierType)fi.CarrierType);
                    FileDic.Clear();
                    DataHub.AddToTotal(fi, data, false);
                    FileDic[fi.ID] = fi;
                }
                else if (TestDate == date)
                {//同一天测试，汇聚
                    DataHub.AddToTotal(fi, data, false);
                    FileDic[fi.ID] = fi;
                }
            }
        }

        public Dictionary<int, FileInfo> FileIDDic { get; set; } = new Dictionary<int, FileInfo>();
        private Dictionary<int, ServiceLatestDataHub> cmLatestServDataDic = null;
        private Dictionary<int, ServiceLatestDataHub> ctLatestServDataDic = null;
        private Dictionary<int, ServiceLatestDataHub> cuLatestServDataDic = null;
        private void addLatestStatDataOnly(FileInfo fileInfo, KPIStatDataBase data)
        {
            Dictionary<int, ServiceLatestDataHub> carrierDataDic = null;
            carrierDataDic = getCarrierLatestData(fileInfo.CarrierType);
            if (carrierDataDic == null || carrierDataDic.Count == 0)
            {
                return;
            }
            
            ServiceLatestDataHub servDataHub = null;
            if (!carrierDataDic.TryGetValue(fileInfo.ServiceType, out servDataHub))
            {
                servDataHub = new ServiceLatestDataHub(fileInfo);
                carrierDataDic[fileInfo.ServiceType] = servDataHub;
            }
            servDataHub.AddStatData(fileInfo, data);
        }

        private Dictionary<int, ServiceLatestDataHub> getCarrierLatestData(int carrierID)
        {
            if (carrierID==1)
            {
                if (cmLatestServDataDic==null)
                {
                    cmLatestServDataDic = new Dictionary<int, ServiceLatestDataHub>();
                }
                return cmLatestServDataDic;
            }
            else if (carrierID==2)
            {
                if (cuLatestServDataDic == null)
                {
                    cuLatestServDataDic = new Dictionary<int, ServiceLatestDataHub>();
                }
                return cuLatestServDataDic;
            }
            else if (carrierID==3)
            {
                if (ctLatestServDataDic == null)
                {
                    ctLatestServDataDic = new Dictionary<int, ServiceLatestDataHub>();
                }
                return ctLatestServDataDic;
            }
            return new Dictionary<int, ServiceLatestDataHub>();
        }

        public virtual void AddStatData(FileInfo fileInfo, KPIStatDataBase data)
        {
            if (fileInfo == null || data == null)
            {
                return;
            }
            if (isStatLatestOnly)
            {
                addLatestStatDataOnly(fileInfo, data);
            }
            else
            {
                FileIDDic[fileInfo.ID] = fileInfo;
                if (fileInfo.CarrierType == (int)CarrierType.ChinaMobile)
                {
                    addDataHub(ref cmDataHub, CarrierType.ChinaMobile, fileInfo, data);
                }
                else if (fileInfo.CarrierType == (int)CarrierType.ChinaUnicom)
                {
                    addDataHub(ref cuDataHub, CarrierType.ChinaUnicom, fileInfo, data);
                }
                else if (fileInfo.CarrierType == (int)CarrierType.ChinaTelecom)
                {
                    addDataHub(ref ctDataHub, CarrierType.ChinaTelecom, fileInfo, data);
                }
            }
        }

        private void addDataHub(ref CarrierStatDataHub dataHub, CarrierType type, FileInfo fileInfo, KPIStatDataBase data)
        {
            if (dataHub == null)
            {
                dataHub = new CarrierStatDataHub(type);
            }
            dataHub.AddToTotal(fileInfo, data, false);
        }

        private void finalMergeLatest(CarrierStatDataHub dataHub
            , IEnumerable<ServiceLatestDataHub> servDataHubSet)
        {
            foreach (ServiceLatestDataHub servDataHub in servDataHubSet)
            {
                foreach (FileInfo fi in servDataHub.FileDic.Values)
                {
                    this.FileIDDic[fi.ID] = fi;
                }
                dataHub.Merge(servDataHub.DataHub);
            }
        }

        public void FinalMtMoGroup()
        {
            if (this.isStatLatestOnly)
            {
                this.FileIDDic = new Dictionary<int, FileInfo>();
                if (this.cmLatestServDataDic != null)
                {
                    cmDataHub = new CarrierStatDataHub(CarrierType.ChinaMobile);
                    finalMergeLatest(cmDataHub, cmLatestServDataDic.Values);
                }
                if (this.cuLatestServDataDic != null)
                {
                    cuDataHub = new CarrierStatDataHub(CarrierType.ChinaUnicom);
                    finalMergeLatest(cuDataHub, cuLatestServDataDic.Values);
                }
                if (this.ctLatestServDataDic != null)
                {
                    ctDataHub = new CarrierStatDataHub(CarrierType.ChinaTelecom);
                    finalMergeLatest(ctDataHub, ctLatestServDataDic.Values);
                }
            }
        }

        public double CalcFormula(CarrierType carrierType, int momtFlag, string formula,params object[] extraParams)
        {
            double v = double.NaN;
            CarrierStatDataHub hub = getDataHub(carrierType);
            if (hub == null)
            {
                return v;
            }
            v = hub.CalcFormula(momtFlag, formula, extraParams);
            return v;
        }

        private CarrierStatDataHub getDataHub(CarrierType carrierType)
        {
            CarrierStatDataHub hub = null;
            switch (carrierType)
            {
                case CarrierType.ChinaMobile:
                    hub = cmDataHub;
                    break;
                case CarrierType.ChinaUnicom:
                    hub = cuDataHub;
                    break;
                case CarrierType.ChinaTelecom:
                    hub = ctDataHub;
                    break;
                default:
                    break;
            }
            return hub;
        }

        #region IComparable<AreaKPIDataGroup<T>> 成员

        public int CompareTo(AreaKPIDataGroup<T> other)
        {
            throw new NotImplementedException();
        }

        #endregion
    }

}
