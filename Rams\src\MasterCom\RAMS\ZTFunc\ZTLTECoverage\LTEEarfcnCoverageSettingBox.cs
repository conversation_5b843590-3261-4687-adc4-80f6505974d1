﻿using MasterCom.RAMS.Chris.Util;
using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEEarfcnCoverageSettingBox : BaseDialog
    {
        public LTEEarfcnCoverageSettingBox()
        {
            InitializeComponent();
            initRsrpRangeSetting();
            initEarfcnSetting();
        }

        private RangeSet initRangeSelect()
        {
            RangeSet rangeSet = new RangeSet();
            rangeSet.Add(new Range(-141, true, -110, false));
            rangeSet.Add(new Range(-110, true, -105, false));
            rangeSet.Add(new Range(-105, true, -100, false));
            rangeSet.Add(new Range(-100, true, -95, false));
            rangeSet.Add(new Range(-95, true, -80, false));
            rangeSet.Add(new Range(-80, true, -75, false));
            rangeSet.Add(new Range(-75, true, -40, false));
            rangeSet.Add(new Range(-40, true, 25, true));
            return rangeSet;
        }
        
        private void initRsrpRangeSetting()
        {
            this.rangeSetSetting.RangeAll = new Range(-141, true, 25, true);
            this.rangeSetSetting.RangeSet = initRangeSelect();
            this.rangeSetSetting.AutoRangeSet = initRangeSelect();
        }

        private EarfcnRangeValue earfcnRangeValue = new EarfcnRangeValue();
        private IList<Range> rangeValues;

        public EarfcnRangeValue EarfcnRangeValue
        {
            get
            {
                return earfcnRangeValue;
            }
        }

        private void initEarfcnSetting()
        {
            txtEarfcnD.Text = "37900,38098,40936";
            txtEarfcnE.Text = "38950,39148,39292";
            txtEarfcnF.Text = "38400,38544";
            txtEarfcn900.Text = "3683";
            txtEarfcn1800.Text = "1250,1300";
        }

        private void makeEarfcnSetting()
        {
            earfcnRangeValue.ClearSetting();
            if (chkEarfcnD.Checked)
            {
                earfcnRangeValue.EarfcnD.AddRange(splitStr(txtEarfcnD.Text));
            }
            if (chkBoxEarfcnE.Checked)
            {
                earfcnRangeValue.EarfcnE.AddRange(splitStr(txtEarfcnE.Text));
            }
            if (chkBoxEarfcnF.Checked)
            {
                earfcnRangeValue.EarfcnF.AddRange(splitStr(txtEarfcnF.Text));
            }
            if (chkBoxFDD900.Checked)
            {
                earfcnRangeValue.EarfcnFDD_900.AddRange(splitStr(txtEarfcn900.Text));
            }
            if (chkBoxFDD1800.Checked)
            {
                earfcnRangeValue.EarfcnFDD_1800.AddRange(splitStr(txtEarfcn1800.Text));
            }
        }

        private List<int> splitStr(string txtText)
        {
            List<int> list = new List<int>();
            string[] strArray = txtText.Split(',');
            foreach (string str in strArray)
            {
                int result;
                if(int.TryParse(str, out result))
                {
                    list.Add(result);
                }
            }
            return list;
        }

        public IList<Range> RsrpRangeValues
        {
            get
            {
                return rangeValues;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            makeEarfcnSetting();
            this.rangeValues = rangeSetSetting.RangeSet.Values as IList<Range>;
            DialogResult = DialogResult.OK;
        }
    }

    public class EarfcnRangeValue
    {
        public List<int> EarfcnD { set; get; } = new List<int>();
        public List<int> EarfcnE { set; get; } = new List<int>();
        public List<int> EarfcnF { set; get; } = new List<int>();
        public List<int> EarfcnFDD_900 { set; get; } = new List<int>();
        public List<int> EarfcnFDD_1800 { set; get; } = new List<int>();

        public void ClearSetting()
        {
            EarfcnD.Clear();
            EarfcnE.Clear();
            EarfcnF.Clear();
            EarfcnFDD_900.Clear();
            EarfcnFDD_1800.Clear();
        }
    }
}
