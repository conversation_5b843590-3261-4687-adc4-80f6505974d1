﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using System.Text;

namespace MasterCom.RAMS.NewBlackBlock
{
    public class BlackBlockEditSubmit : DIYSQLBase
    {
        readonly BlackBlockItem blackBlock;
        readonly bool isUpdateBaseInfo = false;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="blackBlock"></param>
        /// <param name="isUpdateBaseInfo">是否需要更新基础信息</param>
        public BlackBlockEditSubmit(BlackBlockItem blackBlock,bool isUpdateBaseInfo)
            : base(MainModel.GetInstance())
        {
            this.blackBlock = blackBlock;
            this.isUpdateBaseInfo = isUpdateBaseInfo;
        }

        protected override string getSqlTextString()
        {
            StringBuilder sb = new StringBuilder();
            if (isUpdateBaseInfo)
            {
                sb.Append(string.Format("update tb_black_{0}block set [cell_names]='{1}',[road_names]='{2}',[griddesc]='{3}' where id={4};"
                    , blackBlock.Extended.Token, blackBlock.cell_names, blackBlock.road_names, blackBlock.grid_names, blackBlock.blockId));
            }

            sb.Append(string.Format("delete tb_black_block_extend where iBlockID={0} and strToken='{1}';"
                , blackBlock.blockId, blackBlock.Extended.Token));

            StringBuilder insertSb = new StringBuilder();
            insertSb.Append("insert into tb_black_block_extend ([iBlockID],[strCauseMain],[strCauseSub],[strCauseDetail]");
            insertSb.Append(",[strProblem],[strSolution],[strPerson],[dateLastTime],[dateCloseTime],[strToken],[strStatusByFiles],[iHandlePercent])");
            insertSb.Append("values({0},'{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}','{10}',{11});");
            sb.Append(string.Format(insertSb.ToString(), blackBlock.blockId
                 , blackBlock.Extended.CauseMain, blackBlock.Extended.CauseSub, blackBlock.Extended.CauseDetail
                 , blackBlock.Extended.Problem
                 , blackBlock.Extended.Solution, blackBlock.Extended.Person, blackBlock.Extended.LastTime
                 , blackBlock.Extended.Plan2CloseTime.ToString(), blackBlock.Extended.Token, blackBlock.Extended.StatusByFile, blackBlock.Extended.HandleProgress));
            return sb.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int index = 0;
            E_VType[] rType = new E_VType[1];
            rType[index] = E_VType.E_Int;
            return rType;
        }
        private int result = 0;
        public int Result
        {
            get { return result; }
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                result = 1;
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    result = 1;
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public override string Name
        {
            get { return "更新黑点处理信息"; }
        }
    }
}
