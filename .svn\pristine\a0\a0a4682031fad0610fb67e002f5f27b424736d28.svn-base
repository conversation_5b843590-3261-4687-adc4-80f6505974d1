﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTHightRSRPLowSINRQuery_Uep : ZTHightRSRPLowSINRBaseQuery<ZTHightRSRPLowSINRQuery_Uep>
    {
        protected override string themeName { get { return "LTE_UEP:SINR"; } }
        protected override string rsrpName { get { return "lte_uep_SINR"; } }
        protected override string sinrName { get { return "lte_uep_RSRP"; } }

        public ZTHightRSRPLowSINRQuery_Uep()
            : base()
        {
            init();
        }

        protected void init()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_UEP);
#if LT
            carrierID = CarrierType.ChinaUnicom;
#else
            carrierID = CarrierType.ChinaMobile;
#endif
        }

        public override string Name
        {
            get { return "强信号弱质量_LTE_UEP"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 24000, 24005, this.Name);//////
        }
        
        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE感知; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.质量; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                //param["BackgroundStat"] = backgroundStat;
                //param["MaxRxLev"] = weakCovRoadCond.maxRxlev;
                //param["SampleDistance"] = weakCovRoadCond.sampleDistance;
                return param;
            }
            set
            {
#if DEBUG
                Dictionary<string, object> param = value;
                Console.Write(param);
#endif
                //if (value == null || value.Count <= 0)
                //{
                //    return;
                //}
                //Dictionary<string, object> param = value;
                //if (param.ContainsKey("BackgroundStat"))
                //{
                //    backgroundStat = (bool)param["BackgroundStat"];
                //}
                //if (param.ContainsKey("MaxRxLev"))
                //{
                //    weakCovRoadCond.maxRxlev = int.Parse(param["MaxRxLev"].ToString());
                //}
                //if (param.ContainsKey("SampleDistance"))
                //{
                //    weakCovRoadCond.sampleDistance = int.Parse(param["SampleDistance"].ToString());
                //}
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return null;
                //return new WeakCovRoadProperties_GSM(this);
            }
        }

        protected override void saveBackgroundData()
        {
            //List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            //foreach (ZTWeakCovRoadInfo item in ZTWeakCovRoadInfoList)
            //{
            //    BackgroundResult result = item.ConvertToBackgroundResult();
            //    result.SubFuncID = GetSubFuncID();
            //    bgResultList.Add(result);
            //}
            //BackgroundFuncQueryManager.GetInstance().SaveResult_Road(GetSubFuncID(), curAnaFileInfo, bgResultList);
            //ZTWeakCovRoadInfoList.Clear();
        }
        #endregion
    }
}
