﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.BackgroundFunc
{
    class ExportBtsReportHelper_TianJin : ExportOutdoorBtsReportBase
    {
        public static readonly string WorkDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/BTS");
        protected static string getTargetFile(string btsName, int cellCount, string saveFolder)
        {
            if (cellCount > 6)
            {
                throw (new Exception(string.Format("基站{0}小区数超过6个，不支持报告导出", btsName)));
            }

            string templateFile = "天津联通OSS需求申请单模板-报告模板.xlsx";
            templateFile = Path.Combine(WorkDir, templateFile);

            string targetFile = string.Format("天津联通新站验收_{0}.xlsx", btsName);
            targetFile = Path.Combine(saveFolder, targetFile);
            if (File.Exists(targetFile))
            {
                File.Delete(targetFile);
            }
            File.Copy(templateFile, targetFile);
            return targetFile;
        }

        public static bool ExportReports(OutDoorBtsAcceptInfo_TianJin btsInfo, BtsWorkParam_TianJin btsWorkParamInfo, out string savePath)
        {
            savePath = "";
            if (MainModel.GetInstance().BackgroundStopRequest)
            {
                return false;
            }

            Excel.Application xlApp = null;
            try
            {
                StationAcceptCondition_TJ funcSet = StationAccept_TianJin.GetInstance().StationCondition;
                string folderPath = Path.Combine(funcSet.FilePath, btsWorkParamInfo.DateDes);
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }
                string targetFile = getTargetFile(btsInfo.BtsName, btsInfo.Bts.Cells.Count, folderPath);
                savePath = targetFile;
                reportInfo(string.Format("开始导出 {0} 站点的单验报告", btsInfo.BtsName));

                xlApp = new Excel.Application();
                xlApp.Visible = false;
                Excel.Workbook eBook = xlApp.Workbooks.Open(targetFile);

                fillHomePage(eBook, btsWorkParamInfo, btsInfo);
                fillDTPicPage(eBook, btsInfo);
                fillBtsPerformanceAcceptPage(eBook, btsWorkParamInfo, btsInfo);
                fillCellAcceptPage(eBook, btsWorkParamInfo, btsInfo);
                fillInterOperationKpiPage(eBook, btsInfo);

                eBook.Save();
                eBook.Close(Type.Missing, Type.Missing, Type.Missing);

                NbIotStationAcceptAna.GetInstance().ReportFilePaths.Add(targetFile);
                reportInfo(string.Format("成功导出 {0} 站点的单验报告。", btsInfo.BtsName));
                return true;
            }
            catch (Exception ex)
            {
                reportError(ex);
                return false;
            }
            finally
            {
                if (xlApp != null)
                {
                    xlApp.Quit();
                }
            }
        }

        //填充报告首页-宏站验收记录单
        protected static void fillHomePage(Excel.Workbook eBook, BtsWorkParam_TianJin btsWorkParamInfo
            , OutDoorBtsAcceptInfo_TianJin btsInfo)
        {
            LTEBTS srcLteBts = btsInfo.Bts;
            Excel.Worksheet homePageSheet = (Excel.Worksheet)eBook.Sheets[1];

            #region 工参信息
            homePageSheet.get_Range("c3").set_Value(Type.Missing, srcLteBts.Name);
            homePageSheet.get_Range("c4").set_Value(Type.Missing, btsWorkParamInfo.Address);
            homePageSheet.get_Range("e3").set_Value(Type.Missing, btsWorkParamInfo.CompanyName);
            homePageSheet.get_Range("e4").set_Value(Type.Missing, btsWorkParamInfo.ENodeBID);

            int cellParamColIndex = 5;
            for (int i = 0; i < btsWorkParamInfo.CellWorkParams.Count; i++)
            {
                CellWorkParam_TianJin cellInfo = btsWorkParamInfo.CellWorkParams[i] as CellWorkParam_TianJin;
                homePageSheet.Cells[7, cellParamColIndex] = cellInfo.Altitude;
                homePageSheet.Cells[8, cellParamColIndex] = cellInfo.Longitude;
                homePageSheet.Cells[9, cellParamColIndex] = cellInfo.Latitude;
                homePageSheet.Cells[10, cellParamColIndex] = cellInfo.Direction;
                homePageSheet.Cells[11, cellParamColIndex] = cellInfo.MechanicalTilt;
                homePageSheet.Cells[12, cellParamColIndex] = cellInfo.AntennaType;
                homePageSheet.Cells[13, cellParamColIndex] = cellInfo.Downtilt;
                homePageSheet.Cells[14, cellParamColIndex] = cellInfo.AntennaSharedDesc;

                homePageSheet.Cells[19, cellParamColIndex] = cellInfo.Pci;
                homePageSheet.Cells[20, cellParamColIndex] = cellInfo.Earfcn;
                homePageSheet.Cells[21, cellParamColIndex] = cellInfo.Eci;
                homePageSheet.Cells[22, cellParamColIndex] = cellInfo.ENodeBID;
                homePageSheet.Cells[23, cellParamColIndex] = cellInfo.Tac;
                homePageSheet.Cells[25, cellParamColIndex] = cellInfo.DuplexType;

                cellParamColIndex++;
            }
            #endregion

            cellParamColIndex = 5;
            //按工参信息填充分析结果,防止中间某个小区无结果导致后续结果错乱
            for (int i = 0; i < btsWorkParamInfo.CellWorkParams.Count; i++)
            {
                CellWorkParam_TianJin cellInfo = btsWorkParamInfo.CellWorkParams[i] as CellWorkParam_TianJin;
                if (btsInfo.CellsAcceptDic.ContainsKey(cellInfo.CellID))
                {
                    OutDoorCellAcceptInfo_TianJin cellRes = btsInfo.CellsAcceptDic[cellInfo.CellID];
                    setCellValue(homePageSheet.Cells, 32, cellParamColIndex, cellRes.DTHandoverInfo.AntennaOpposite);
                    setCellValue(homePageSheet.Cells, 44, cellParamColIndex, cellRes.DLThroughputInfo.AvgRSRP);
                    setCellValue(homePageSheet.Cells, 45, cellParamColIndex, cellRes.DLThroughputInfo.AvgSINR);
                    setCellValue(homePageSheet.Cells, 46, cellParamColIndex, cellRes.DLThroughputInfo.DLMaxThroughput);
                    setCellValue(homePageSheet.Cells, 47, cellParamColIndex, cellRes.ULThroughputInfo.ULMaxThroughput);

                    setCellValue(homePageSheet.Cells, 48, cellParamColIndex, cellRes.PingInfo.PingRate);
                    homePageSheet.Cells[49, cellParamColIndex] = "";//1500Byte
                    setCellValue(homePageSheet.Cells, 50, cellParamColIndex, cellRes.PingInfo.PingDelay);
                    homePageSheet.Cells[51, cellParamColIndex] = "";//1500Byte

                    homePageSheet.Cells[52, cellParamColIndex] = "";//WCDMA
                    homePageSheet.Cells[53, cellParamColIndex] = "";//WCDMA
                    setCellValue(homePageSheet.Cells, 54, cellParamColIndex, cellRes.CsfbInfo.CsfbRate);
                    setCellValue(homePageSheet.Cells, 55, cellParamColIndex, cellRes.CsfbInfo.CsfbDelayOther);

                    setCellValue(homePageSheet.Cells, 56, cellParamColIndex, cellRes.DLThroughputInfo.DLAvgThroughput);
                    setCellValue(homePageSheet.Cells, 57, cellParamColIndex, cellRes.ULThroughputInfo.ULAvgThroughput);
                    setCellValue(homePageSheet.Cells, 58, cellParamColIndex, cellRes.DLThroughputInfo.AvgRSRP);
                    setCellValue(homePageSheet.Cells, 59, cellParamColIndex, cellRes.DLThroughputInfo.AvgSINR);               
                }
                cellParamColIndex++;
            }
        }

        private static void setCellValue(Excel.Range range, int rowIndex, int colIndex, KpiInfo_TianJin.KpiInfo kpi)
        {
            range[rowIndex, colIndex] = kpi.ValidRes;
            if (!kpi.IsAccord)
            {
                Excel.Range r = range[rowIndex, colIndex] as Excel.Range;
                r.Font.ColorIndex = 3;
            }
        }

        //填充报告第二页-路测分布图
        protected static void fillDTPicPage(Excel.Workbook eBook, OutDoorBtsAcceptInfo_TianJin btsInfo)
        {
            //Excel.Worksheet coverPicPageSheet = (Excel.Worksheet)eBook.Sheets[1];
            LTEBTS srcLteBts = btsInfo.Bts;

            //小区图
            List<string> colList = new List<string>() { "AM", "C", "AM"};
            int rowIndex = 3;
            //模板为3个小区,目前模板无法自动增长
            int max = 3;//Math.Max(srcLteBts.Cells.Count, 3);
            for (int i = 0; i < max; i++)
            {
                LTECell cell = srcLteBts.Cells[i];
                OutDoorCellAcceptInfo_TianJin cellInfo;
                if (btsInfo.CellsAcceptDic.TryGetValue(cell.CellID, out cellInfo))
                {
                    insertCoverPic(eBook, cellInfo.DTHandoverInfo.CoverPicPath_Rsrp.ValidRes, colList[i] + rowIndex.ToString());
                    rowIndex += 36;
                    insertCoverPic(eBook, cellInfo.DTHandoverInfo.CoverPicPath_Sinr.ValidRes, colList[i] + rowIndex.ToString());
                }
                rowIndex = 21;
            }

            //基站图
            insertCoverPic(eBook, btsInfo.DTHandoverInfo.CoverPicPath_Rsrp.ValidRes, "C3");
            insertCoverPic(eBook, btsInfo.DTHandoverInfo.CoverPicPath_Sinr.ValidRes, "C39");
            insertCoverPic(eBook, btsInfo.DTHandoverInfo.CoverPicPath_Pci.ValidRes, "C75");
        }

        protected static void insertCoverPic(Excel.Workbook eBook, string coverPicPath, string colIndex)
        {
            if (File.Exists(coverPicPath))
            {
                AcpAutoCellCoverPic_TianJin.InsertExcelPicture(eBook, colIndex, coverPicPath, 2, 15.5, 11.1);
            }
        }

        //填充报告第三页-站点性能验收表
        protected static void fillBtsPerformanceAcceptPage(Excel.Workbook eBook, BtsWorkParam_TianJin btsWorkParamInfo, OutDoorBtsAcceptInfo_TianJin btsInfo)
        {
            Excel.Worksheet acceptPageSheet = (Excel.Worksheet)eBook.Sheets[3];

            int cellParamColIndex = 8;
            for (int i = 0; i < btsWorkParamInfo.CellWorkParams.Count; i++)
            {
                CellWorkParam_TianJin cellInfo = btsWorkParamInfo.CellWorkParams[i] as CellWorkParam_TianJin;
                if (btsInfo.CellsAcceptDic.ContainsKey(cellInfo.CellID))
                {
                    OutDoorCellAcceptInfo_TianJin cellRes = btsInfo.CellsAcceptDic[cellInfo.CellID];
                    setCellValue(acceptPageSheet.Cells, 3, cellParamColIndex, cellRes.PingInfo.PingDelay);
                    setCellValue(acceptPageSheet.Cells, 4, cellParamColIndex, cellRes.DLThroughputInfo.DLMaxThroughput);
                    setCellValue(acceptPageSheet.Cells, 5, cellParamColIndex, cellRes.ULThroughputInfo.ULMaxThroughput);
                    setCellValue(acceptPageSheet.Cells, 6, cellParamColIndex, cellRes.DLThroughputInfo.DLAvgThroughput);
                    setCellValue(acceptPageSheet.Cells, 7, cellParamColIndex, cellRes.ULThroughputInfo.ULAvgThroughput);
                    setCellValue(acceptPageSheet.Cells, 8, cellParamColIndex, cellRes.CsfbInfo.CsfbRate);
                    setCellValue(acceptPageSheet.Cells, 9, cellParamColIndex, cellRes.CsfbInfo.CsfbDelayOther);
                    setCellValue(acceptPageSheet.Cells, 10, cellParamColIndex, cellRes.DLThroughputInfo.Pci);
                    setCellValue(acceptPageSheet.Cells, 11, cellParamColIndex, cellRes.DTHandoverInfo.Handover);//切换
                    setCellValue(acceptPageSheet.Cells, 12, cellParamColIndex, cellRes.DLThroughputInfo.Cover);//覆盖
                }
                cellParamColIndex++;
            }
        }

        //填充报告第四页-小区验证情况
        protected static void fillCellAcceptPage(Excel.Workbook eBook, BtsWorkParam_TianJin btsWorkParamInfo, OutDoorBtsAcceptInfo_TianJin btsInfo)
        {
            Excel.Worksheet cellAcceptPageSheet = (Excel.Worksheet)eBook.Sheets[4];

            int cellParamRowIndex = 2;
            for (int i = 0; i < btsWorkParamInfo.CellWorkParams.Count; i++)
            {
                CellWorkParam_TianJin cellInfo = btsWorkParamInfo.CellWorkParams[i] as CellWorkParam_TianJin;
                if (btsInfo.CellsAcceptDic.ContainsKey(cellInfo.CellID))
                {
                    OutDoorCellAcceptInfo_TianJin cellRes = btsInfo.CellsAcceptDic[cellInfo.CellID];
                    cellAcceptPageSheet.Cells[cellParamRowIndex, 1] = cellInfo.CellNameFull;
                    cellAcceptPageSheet.Cells[cellParamRowIndex, 2] = cellInfo.CellID + "扇区";

                    setCellValue(cellAcceptPageSheet.Cells, cellParamRowIndex, 3, cellRes.DLThroughputInfo.AvgRSRP);
                    setCellValue(cellAcceptPageSheet.Cells, cellParamRowIndex, 4, cellRes.DLThroughputInfo.EdgeRsrp);
                    setCellValue(cellAcceptPageSheet.Cells, cellParamRowIndex, 5, cellRes.DLThroughputInfo.AvgSINR);
                    setCellValue(cellAcceptPageSheet.Cells, cellParamRowIndex, 6, cellRes.DLThroughputInfo.EdgeSinr);
                    setCellValue(cellAcceptPageSheet.Cells, cellParamRowIndex, 7, cellRes.DTHandoverInfo.OverlapRate);//重叠覆盖
                    setCellValue(cellAcceptPageSheet.Cells, cellParamRowIndex, 8, cellRes.DLThroughputInfo.DLAvgThroughput);
                    setCellValue(cellAcceptPageSheet.Cells, cellParamRowIndex, 9, cellRes.ULThroughputInfo.ULAvgThroughput);
                    setCellValue(cellAcceptPageSheet.Cells, cellParamRowIndex, 10, cellRes.DLThroughputInfo.EdgeMacDL);
                    setCellValue(cellAcceptPageSheet.Cells, cellParamRowIndex, 11, cellRes.ULThroughputInfo.EdgeMacUL);
                    setCellValue(cellAcceptPageSheet.Cells, cellParamRowIndex, 12, cellRes.DTHandoverInfo.ConnectRate);//连接成功率
                    setCellValue(cellAcceptPageSheet.Cells, cellParamRowIndex, 13, cellRes.DTHandoverInfo.HandoverRate);//切换成功率
                    setCellValue(cellAcceptPageSheet.Cells, cellParamRowIndex, 14, cellRes.CsfbInfo.CsfbRate);
                    setCellValue(cellAcceptPageSheet.Cells, cellParamRowIndex, 15, cellRes.CsfbInfo.CsfbDelayOther);
                    cellParamRowIndex++;
                }
            }
        }

        //填充报告第五页-LTE互操作
        protected static void fillInterOperationKpiPage(Excel.Workbook eBook, OutDoorBtsAcceptInfo_TianJin btsInfo)
        {
            Excel.Worksheet bgKpiPageSheet = (Excel.Worksheet)eBook.Sheets[5];

            setCellValue(bgKpiPageSheet.Cells, 2, 8, btsInfo.InterOperationKpiInfo.Mo4GMt3G_Num);
            setCellValue(bgKpiPageSheet.Cells, 2, 10, btsInfo.InterOperationKpiInfo.Mo4GMt3G_CsfbRate);
            setCellValue(bgKpiPageSheet.Cells, 2, 11, btsInfo.InterOperationKpiInfo.Mo4GMt3G_CsfbDelay);
            setCellValue(bgKpiPageSheet.Cells, 3, 12, btsInfo.InterOperationKpiInfo.Mo4GMt3G_FRDelay);

            setCellValue(bgKpiPageSheet.Cells, 6, 8, btsInfo.InterOperationKpiInfo.Mo4GMt3G_Num);
            setCellValue(bgKpiPageSheet.Cells, 6, 10, btsInfo.InterOperationKpiInfo.Mo4GMt3G_CsfbRate);
            setCellValue(bgKpiPageSheet.Cells, 6, 11, btsInfo.InterOperationKpiInfo.Mo4GMt3G_CsfbDelay);
            setCellValue(bgKpiPageSheet.Cells, 8, 12, btsInfo.InterOperationKpiInfo.Mo4GMt3G_FRDelay);
        }
    }
}
