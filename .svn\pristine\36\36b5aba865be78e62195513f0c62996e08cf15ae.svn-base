﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.Windows.Forms;
using System.Net;

namespace MasterCom.RAMS.CQT
{
    public class DIYQureyCQTGSMNoMainCelldData : DIYSampleQuery
    {
        public DIYQureyCQTGSMNoMainCelldData(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            isAddSampleToDTDataManager = false;
        }
        /// <summary>
        /// 标题名称
        /// </summary>
        public override string Name
        {
            get { return ""; }
        }
        /// <summary>
        /// 图标名称
        /// </summary>
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }
        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 21000, 21017, this.Name);//临时
        }
        

        #region 涉及的全局变量
        private readonly List<string> fileValueNameList = new List<string>();
        readonly Dictionary<string, List<FileInfo>> fileValueList = new Dictionary<string, List<FileInfo>>();
        readonly Dictionary<string, List<TestPoint>> cqtNameTestPointList = new Dictionary<string, List<TestPoint>>();
        readonly Dictionary<string, Dictionary<Cell, Dictionary<TestPoint, List<NbSampleInfoGSM>>>> cqtMainCellTestPointNbDic
            = new Dictionary<string, Dictionary<Cell, Dictionary<TestPoint, List<NbSampleInfoGSM>>>>();
        readonly Dictionary<Cell, Dictionary<TestPoint, List<NbSampleInfoGSM>>> mainCellTestPointNbDic
            = new Dictionary<Cell, Dictionary<TestPoint, List<NbSampleInfoGSM>>>();
        readonly Dictionary<Cell, int> cellNoDominTestPointDic = new Dictionary<Cell, int>();
        readonly List<CQTGsmNoMainCell> resultData = new List<CQTGsmNoMainCell>();

        public int RxLevMin { get; set; } = -85;
        public int RxLevMax { get; set; } = -10;
        public int sampleCountLimit { get; set; } = 1;
        public int cellCountLimit { get; set; } = 4;
        public int rxLevDValue { get; set; } = 6;
        #endregion

        /// <summary>
        /// 准备查询数据
        /// </summary>
        protected override void query()
        {
            fileValueNameList.Clear();
            fileValueList.Clear();           
            resultData.Clear();
            NoMainCellSetForm noMainCellSetForm = new NoMainCellSetForm("GSM");
            if (noMainCellSetForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            NoMainCellCondition settingCondition = noMainCellSetForm.GetSettingFilterRet();
            RxLevMin = settingCondition.RSCPMin;
            RxLevMax = settingCondition.RSCPMax;
            sampleCountLimit = settingCondition.sampleCountLimit;
            cellCountLimit = settingCondition.cellCountLimit;
            rxLevDValue = settingCondition.rxLevDValue;
            // 查找全区域文件
            DIYFileInfoData diyFileInfoData = new DIYFileInfoData(mainModel);
            diyFileInfoData.SetQueryCondition(this.Condition);
            diyFileInfoData.Query();         
            //找出所有文件中包含的地点名称
            foreach (FileInfo fileIn in diyFileInfoData.FlieInfoData)
            {
                string[] name = fileIn.Name.Split('_');
                if (name.Length < 3)
                    continue;
                if (!fileValueNameList.Contains(name[2]))
                {
                    fileValueNameList.Add(name[2]);
                }
            }
            //每个地点所涉及的文件
            foreach (string nameL in fileValueNameList)
            {
                List<FileInfo> subFileList = new List<FileInfo>();
                foreach (FileInfo fileIn in diyFileInfoData.FlieInfoData)
                {
                    string[] name = fileIn.Name.Split('_');
                    if (name.Length < 3)
                        continue;
                    if (nameL.Equals(name[2]))
                    {
                        subFileList.Add(fileIn);
                    }
                }
                fileValueList.Add(nameL, subFileList);
            }
            ClientProxy clientProxy = new ClientProxy();
            WaitBox.Show("开始分析数据...", queryInThread, clientProxy);
            //显示结果窗体
            CQTGSMNoDominantCellNew xtraformstatus = new CQTGSMNoDominantCellNew(MainModel);
            xtraformstatus.setData(resultData);
            xtraformstatus.Show();
            clientProxy.Close();
        }
        /// <summary>
        /// 开始分析采样点
        /// </summary>
        private new void queryInThread(object o)
        {
            WaitBox.CanCancel = true;
            int idx = 1;
            foreach (string name in fileValueNameList)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                cqtNameTestPointList.Clear();
                cellNoDominTestPointDic.Clear();
                cqtMainCellTestPointNbDic.Clear();

                //获取测试地点的采样点
                anyFileAnalysTestPoint(name, idx++);

                //对每个测试地点涉及的采样点进行电平值的统计分析
                addCqtMainCellTestPointNbDic();
                WaitBox.Text = "分析该测试地点各小区的无主导采样点个数...";
                foreach (string cqtname in cqtMainCellTestPointNbDic.Keys)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    addCellNoDominTestPointDic(cqtname);
                }
                dealWithDataDic();
            }
            WaitBox.Close();
        }

        private void addCqtMainCellTestPointNbDic()
        {
            foreach (string cqtName in cqtNameTestPointList.Keys)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                mainCellTestPointNbDic.Clear();
                dealTPList(cqtName);
                cqtMainCellTestPointNbDic.Add(cqtName, mainCellTestPointNbDic);
            }
        }

        private void dealTPList(string cqtName)
        {
            foreach (TestPoint tp in cqtNameTestPointList[cqtName])
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                if (tp["CI"] == null || tp["RxLevSub"] == null || tp["RxQualSub"] == null)
                    continue;
                int? iCi = (int?)tp["CI"];
                int? iRxlevSub = (short?)tp["RxLevSub"];
                if (iCi == null || iRxlevSub == null || iCi <= 0 || iRxlevSub > RxLevMax || iRxlevSub < RxLevMin
                    || int.Parse(tp["RxQualSub"].ToString()) > 7 || int.Parse(tp["RxQualSub"].ToString()) < 0)
                    continue;
                GetCellByTestPoint(tp);
            }
        }

        private void addCellNoDominTestPointDic(string cqtname)
        {
            foreach (Cell cell in cqtMainCellTestPointNbDic[cqtname].Keys)
            {
                int noDominCout = 0;
                foreach (TestPoint tp in cqtMainCellTestPointNbDic[cqtname][cell].Keys)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    if (IsNoDominTestPoint(tp))
                    {
                        noDominCout++;
                    }
                }
                cellNoDominTestPointDic.Add(cell, noDominCout);
            }
        }

        /// <summary>
        /// 实际获取采样点的过程
        /// </summary>
        private void anyFileAnalysTestPoint(string name,int idx)
        {
            List<TestPoint> testlist = new List<TestPoint>();           
            WaitBox.Text = "分析(" + idx.ToString() + "/" + fileValueNameList.Count.ToString() + ")( " + name + " )的采样点...";
            WaitBox.ProgressPercent = 30;
            QueryCondition condition = new QueryCondition();
            condition.FileInfos.AddRange(fileValueList[name]);
            try
            {
                ReplayFileCQT query = new ReplayFileCQT(MainModel);
                query.SetQueryCondition(condition);
                query.Query();
                testlist.AddRange(query.testPointList);
                WaitBox.ProgressPercent = 95;
            }
            catch
            {
                //continue
            }
            cqtNameTestPointList.Add(name,testlist);
            WaitBox.ProgressPercent = 90;
        }
        /// <summary>
        /// 通过采样点获取小区
        /// </summary>
        private void GetCellByTestPoint(TestPoint tp)
        {
            Dictionary<TestPoint, List<NbSampleInfoGSM>> testPointNbSample = new Dictionary<TestPoint, List<NbSampleInfoGSM>>();
            List<NbSampleInfoGSM> nbsampleList = new List<NbSampleInfoGSM>();           
            for (int i = 0; i < 6; i++)
            {
                NbSampleInfoGSM nbsampleinfo = new NbSampleInfoGSM();
                if (tp["N_BCCH", i] == null || tp["N_BSIC", i] == null || tp["N_RxLev", i] == null)
                    continue; 
                nbsampleinfo.iNbBcch = (int)(short?)tp["N_BCCH", i];
                nbsampleinfo.iNbBsic = (int)(byte?)tp["N_BSIC", i];
                nbsampleinfo.iNbRxlevSub = (int)(short?)tp["N_RxLev", i];
                if (nbsampleinfo.iNbBcch <= 0 || nbsampleinfo.iNbBsic <= 0 || nbsampleinfo.iNbRxlevSub > RxLevMax || nbsampleinfo.iNbRxlevSub < RxLevMin)
                    continue;
                nbsampleList.Add(nbsampleinfo);
            }
            Cell mainCell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (ushort?)(int?)tp["LAC"], (ushort?)(int?)tp["CI"],
                (short?)tp["BCCH"], (byte?)tp["BSIC"], tp.Longitude, tp.Latitude);
            if (mainCell != null)
            {
                List<Cell> cellTem = new List<Cell>();
                cellTem.AddRange(mainCellTestPointNbDic.Keys);
                if (!cellTem.Contains(mainCell))
                {
                    testPointNbSample.Add(tp, nbsampleList);
                    mainCellTestPointNbDic.Add(mainCell, testPointNbSample);                   
                }
                else
                {
                    mainCellTestPointNbDic[mainCell].Add(tp, nbsampleList);
                }                
            }           
        }
        /// <summary>
        /// 判断是否是无主导的采样点
        /// </summary>
        private bool IsNoDominTestPoint(TestPoint tp)
        {
            List<int> relevList = new List<int>();
            int? rxlevSub = (int?)(short?)tp["RxLevSub"];
            if (rxlevSub == null || rxlevSub < RxLevMin)
                return false;
            else
                relevList.Add((int)(rxlevSub));
            addRelevList(tp, relevList);
            int icount = 0;
            relevList.Sort();
            if (relevList.Count != 0)
            {
                int maxRelev = relevList[relevList.Count - 1];
                icount++;
                for (int i = relevList.Count - 2; i >= 0; i--)
                {
                    if ((maxRelev - relevList[i]) <= rxLevDValue)
                        icount++;
                    else
                        break;
                }
            }
            else
                return false;
            if (icount >= cellCountLimit)
                return true;
            else
                return false;
        }

        private void addRelevList(TestPoint tp, List<int> relevList)
        {
            for (int i = 0; i < 6; i++)
            {
                int? rxlevSubN = (int?)(short?)tp["N_RxLev", i];
                if (rxlevSubN != null)
                {
                    if (rxlevSubN >= RxLevMin)
                        relevList.Add((int)(rxlevSubN));
                }
                else
                    break;
            }
        }

        /// <summary>
        /// 将处理的数据转化为CQTGsmNoMainCell数据，用于输出
        /// </summary>
        private void dealWithDataDic()
        {           
            foreach (string cqtName in cqtMainCellTestPointNbDic.Keys)
            {              
                foreach (Cell cell in cqtMainCellTestPointNbDic[cqtName].Keys)
                {
                    CQTGsmNoMainCell cqtGsmNoMainCell = new CQTGsmNoMainCell();
                    cqtGsmNoMainCell.Strcqtname = cqtName;
                    cqtGsmNoMainCell.Strcellname = cell.Name;
                    cqtGsmNoMainCell.Ilac = cell.LAC;
                    cqtGsmNoMainCell.Ici = cell.CI;
                    cqtGsmNoMainCell.Ibcch = cell.BCCH;
                    cqtGsmNoMainCell.Ibsic = cell.BSIC;
                    List<TestPoint> testPoint = new List<TestPoint>();
                    testPoint.AddRange(cqtMainCellTestPointNbDic[cqtName][cell].Keys);
                    string strRxlevSub = GetMaxMinAvgRxLevSub(testPoint, cqtMainCellTestPointNbDic[cqtName][cell]);
                    cqtGsmNoMainCell.Isamplenum = testPoint.Count;
                    cqtGsmNoMainCell.Inomaincellname = cellNoDominTestPointDic[cell];
                    cqtGsmNoMainCell.Strnomaincellrate = (100.0 * cqtGsmNoMainCell.Inomaincellname / cqtGsmNoMainCell.Isamplenum).ToString("0.00") + "%";                   
                    cqtGsmNoMainCell.Imaxrxlev = int.Parse(strRxlevSub.Split('|')[0].Split(',')[0]);
                    cqtGsmNoMainCell.Iminrxlev = int.Parse(strRxlevSub.Split('|')[0].Split(',')[1]);
                    cqtGsmNoMainCell.Iavgrxlev = int.Parse(strRxlevSub.Split('|')[0].Split(',')[2]);
                    cqtGsmNoMainCell.Iavgqual = int.Parse(strRxlevSub.Split('|')[2]);
                    cqtGsmNoMainCell.Imaxnbrxlev = int.Parse(strRxlevSub.Split('|')[1].Split(',')[0]);
                    cqtGsmNoMainCell.Iminnbrxlev = int.Parse(strRxlevSub.Split('|')[1].Split(',')[1]);
                    cqtGsmNoMainCell.Iavgnbrxlev = int.Parse(strRxlevSub.Split('|')[1].Split(',')[2]);
                    if (cqtGsmNoMainCell.Isamplenum == 0 || cqtGsmNoMainCell.Inomaincellname < sampleCountLimit || cqtGsmNoMainCell.Isamplenum == 0)
                        continue;
                    resultData.Add(cqtGsmNoMainCell);
                }
            }
        }
        /// <summary>
        /// 对于每个小区采样点列表求最大最小以及平均电平值
        /// </summary>
        private string GetMaxMinAvgRxLevSub(List<TestPoint> testPoint, Dictionary<TestPoint, List<NbSampleInfoGSM>> testPointNbSample)
        {
            int MaxRxlevSub = RxLevMin;
            int MinRxlevSub = RxLevMax;
            int AvgRxlevSub = 0;
            int SumRxlevSub = 0;
            int AvgQuality = 0;
            int SumAvgQuality = 0;
            int nbMaxRxlevSub = RxLevMin;
            int nbMinRxlevSub = RxLevMax;
            int nbAvgRxlevSub = 0;
            int nbSumRxlevSub = 0;
            int nbSumSampleCount = 0;
            foreach (TestPoint tp in testPoint)
            {
                SumRxlevSub += (int)(short?)tp["RxLevSub"];
                if ((int)(short?)tp["RxLevSub"] > MaxRxlevSub)
                {
                    MaxRxlevSub = (int)(short?)tp["RxLevSub"];
                }
                if ((int)(short?)tp["RxLevSub"] < MinRxlevSub)
                {
                    MinRxlevSub = (int)(short?)tp["RxLevSub"];
                }
                SumAvgQuality += int.Parse(tp["RxQualSub"].ToString());
                getNBTPInfo(testPointNbSample, ref nbMaxRxlevSub, ref nbMinRxlevSub, ref nbSumRxlevSub, ref nbSumSampleCount, tp);
            }
            if (testPoint.Count != 0)
            {
                AvgRxlevSub = SumRxlevSub / testPoint.Count;
                AvgQuality = SumAvgQuality / testPoint.Count;
            }
            else
            {
                AvgRxlevSub = 0;
                AvgQuality = 0;
            }
            if (nbSumSampleCount != 0)
                nbAvgRxlevSub = nbSumRxlevSub / nbSumSampleCount;
            else
                nbAvgRxlevSub = 0;
            return MaxRxlevSub.ToString() + "," + MinRxlevSub.ToString() + "," + AvgRxlevSub.ToString() + "|"
                + nbMaxRxlevSub.ToString() + "," + nbMinRxlevSub.ToString() + "," + nbAvgRxlevSub.ToString() + "|"
                 + AvgQuality.ToString();
        }

        private void getNBTPInfo(Dictionary<TestPoint, List<NbSampleInfoGSM>> testPointNbSample, ref int nbMaxRxlevSub, ref int nbMinRxlevSub, ref int nbSumRxlevSub, ref int nbSumSampleCount, TestPoint tp)
        {
            foreach (NbSampleInfoGSM nbsampleinfo in testPointNbSample[tp])
            {
                nbSumRxlevSub += nbsampleinfo.iNbRxlevSub;
                if (nbsampleinfo.iNbRxlevSub > nbMaxRxlevSub)
                {
                    nbMaxRxlevSub = nbsampleinfo.iNbRxlevSub;
                }
                if (nbsampleinfo.iNbRxlevSub < nbMinRxlevSub)
                {
                    nbMinRxlevSub = nbsampleinfo.iNbRxlevSub;
                }
            }
            nbSumSampleCount += testPointNbSample[tp].Count;
        }
    }
    public class ReplayFileCQT : DIYReplayFileWithNoWaitBox
    {
        public List<TestPoint> testPointList { get; set; }

        public ReplayFileCQT(MainModel mainModel)
            : base(mainModel)
        {
            IncludeEvent = false;
            IsAddSampleToDTDataManager = false;
        }
        public ReplayFileCQT(MainModel mainModel, bool isIncludeEvent)
            : base(mainModel)
        {
            IncludeEvent = isIncludeEvent;
            IsAddSampleToDTDataManager = false;
        }
        protected override void query()
        {
            testPointList = new List<TestPoint>();
            base.query();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            testPointList.Add(tp);
        }
    }
    class NbSampleInfoGSM
    {
        public int iNbBcch;
        public int iNbBsic;
        public int iNbRxlevSub;

        public NbSampleInfoGSM()
        {
            iNbBcch = 0;
            iNbBsic = 0;
            iNbRxlevSub = 0;
        }
    }
    public class CQTGsmNoMainCell
    {
        public float Fnomaincellrate { get; set; }
        public float Ftotalnbrxlev { get; set; }
        public float Ftotalrxlev { get; set; }
        public float Ftotalrxqual { get; set; }
        public int Iareaid { get; set; }
        public int Iareatype { get; set; }
        public int Iavgnbrxlev { get; set; }
        public int Iavgqual { get; set; }
        public int Iavgrxlev { get; set; }
        public int Ibcch { get; set; }
        public int Ibsic { get; set; }
        public int Ici { get; set; }
        public int Idx { get; set; }
        public int Ilac { get; set; }
        public int Imaxnbrxlev { get; set; }
        public int Imaxrxlev { get; set; }
        public int Iminnbrxlev { get; set; }
        public int Iminrxlev { get; set; }
        public int Inomaincellname { get; set; }
        public int Inumnbrxlev { get; set; }
        public int Inumrxqual { get; set; }
        public int Isamplenum { get; set; }
        public string Strcellname { get; set; }
        public string Strcqtname { get; set; }
        public string Strnomaincellrate { get; set; }
    }
}
