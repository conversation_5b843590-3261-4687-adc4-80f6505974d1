using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Drawing;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
namespace MasterCom.RAMS.Func
{
    [System.ComponentModel.ToolboxItem(false)]
    public class MapWCellLayerCellProperties : MTLayerPropUserControl
    {
        public MapWCellLayerCellProperties()
        {
            InitializeComponent();
        }

        public override void Setup(Object obj)
        {
            if (obj == null)
            {
                return;
            }
            Text = "WCDMA Cell";
            layer = (MapWCellLayer)obj;
            checkBoxDisplay.Checked = layer.DrawCell;
            labelColorCell.BackColor = layer.ColorCell;
            TrackBarOpacity.Value = labelColorCell.BackColor.A;

            cbxDrawCellLabel.Checked = layer.DrawCellLabel;
            btnFont.Enabled = cbxDrawCellLabel.Checked;
            cbxCellName.Checked = layer.DrawCellName;
            cbxCellCode.Checked = layer.DrawCellCode;
            cbxCellLAC.Checked = layer.DrawCellLAC;
            cbxCellCI.Checked = layer.DrawCellCI;
            cbxCellUARFCN.Checked = layer.DrawCellUARFCN;
            cbxCellPSC.Checked = layer.DrawCellPSC;
            cbxCellDes.Checked = layer.DrawCellDes;
        }

        private void checkBoxDisplay_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCell = checkBoxDisplay.Checked;
        }

        private void labelColorCell_Click(object sender, EventArgs e)
        {
            colorDialog.Color = labelColorCell.BackColor;
            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                labelColorCell.BackColor = Color.FromArgb(TrackBarOpacity.Value, colorDialog.Color);
                layer.ColorCell = labelColorCell.BackColor;
                layer.RefreshBrushes();
            }
        }

        private void TrackBarOpacity_Scroll(object sender, EventArgs e)
        {
            labelColorCell.BackColor = Color.FromArgb(TrackBarOpacity.Value, labelColorCell.BackColor);
            layer.ColorCell = labelColorCell.BackColor;
            layer.RefreshBrushes();
        }

        private void cbxDrawCellLabel_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellLabel = cbxDrawCellLabel.Checked;
            btnFont.Enabled = cbxDrawCellLabel.Checked;
            groupBox1.Enabled = cbxDrawCellLabel.Checked;
        }

        private void btnFont_Click(object sender, EventArgs e)
        {
            fontDialog.Font = layer.FontCellLabel;
            if (fontDialog.ShowDialog(this) == DialogResult.OK)
            {
                layer.FontCellLabel = fontDialog.Font;
            }
        }

        private void cbxCellName_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellName = cbxCellName.Checked;
        }

        private void cbxCellCode_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellCode = cbxCellCode.Checked;
        }

        private void cbxCellLAC_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellLAC = cbxCellLAC.Checked;
        }

        private void cbxCellCI_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellCI = cbxCellCI.Checked;
        }

        private void cbxCellUARFCN_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellUARFCN = cbxCellUARFCN.Checked;
        }

        private void cbxCellPSC_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellPSC = cbxCellPSC.Checked;
        }

        private void cbxCellDes_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellDes = cbxCellDes.Checked;
        }

        private void InitializeComponent()
        {
            System.Windows.Forms.Label LabelOpacity;
            System.Windows.Forms.Label labelColor;
            System.Windows.Forms.Label labelColorLabel;
            System.Windows.Forms.Label label0;
            System.Windows.Forms.Label label100;
            this.TrackBarOpacity = new System.Windows.Forms.TrackBar();
            this.labelColorCell = new System.Windows.Forms.Label();
            this.checkBoxDisplay = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbxCellDes = new System.Windows.Forms.CheckBox();
            this.cbxCellPSC = new System.Windows.Forms.CheckBox();
            this.cbxCellUARFCN = new System.Windows.Forms.CheckBox();
            this.cbxCellCI = new System.Windows.Forms.CheckBox();
            this.cbxCellLAC = new System.Windows.Forms.CheckBox();
            this.cbxCellCode = new System.Windows.Forms.CheckBox();
            this.cbxCellName = new System.Windows.Forms.CheckBox();
            this.btnFont = new System.Windows.Forms.Button();
            this.cbxDrawCellLabel = new System.Windows.Forms.CheckBox();
            LabelOpacity = new System.Windows.Forms.Label();
            labelColor = new System.Windows.Forms.Label();
            labelColorLabel = new System.Windows.Forms.Label();
            label0 = new System.Windows.Forms.Label();
            label100 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // LabelOpacity
            // 
            LabelOpacity.Location = new System.Drawing.Point(3, 166);
            LabelOpacity.Name = "LabelOpacity";
            LabelOpacity.Size = new System.Drawing.Size(56, 16);
            LabelOpacity.TabIndex = 8;
            LabelOpacity.Text = "Opacity: ";
            // 
            // labelColor
            // 
            labelColor.Location = new System.Drawing.Point(3, 26);
            labelColor.Name = "labelColor";
            labelColor.Size = new System.Drawing.Size(37, 20);
            labelColor.TabIndex = 5;
            labelColor.Text = "Color:";
            labelColor.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // labelColorLabel
            // 
            labelColorLabel.AutoSize = true;
            labelColorLabel.Location = new System.Drawing.Point(3, 52);
            labelColorLabel.Name = "labelColorLabel";
            labelColorLabel.Size = new System.Drawing.Size(29, 12);
            labelColorLabel.TabIndex = 36;
            labelColorLabel.Text = "Cell";
            labelColorLabel.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label0
            // 
            label0.AutoSize = true;
            label0.Location = new System.Drawing.Point(54, 187);
            label0.Name = "label0";
            label0.Size = new System.Drawing.Size(17, 12);
            label0.TabIndex = 40;
            label0.Text = "0%";
            label0.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label100
            // 
            label100.AutoSize = true;
            label100.Location = new System.Drawing.Point(138, 187);
            label100.Name = "label100";
            label100.Size = new System.Drawing.Size(29, 12);
            label100.TabIndex = 41;
            label100.Text = "100%";
            label100.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // TrackBarOpacity
            // 
            this.TrackBarOpacity.LargeChange = 32;
            this.TrackBarOpacity.Location = new System.Drawing.Point(49, 155);
            this.TrackBarOpacity.Maximum = 255;
            this.TrackBarOpacity.Name = "TrackBarOpacity";
            this.TrackBarOpacity.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.TrackBarOpacity.Size = new System.Drawing.Size(115, 45);
            this.TrackBarOpacity.TabIndex = 9;
            this.TrackBarOpacity.TickFrequency = 32;
            this.TrackBarOpacity.Value = 255;
            this.TrackBarOpacity.Scroll += new System.EventHandler(this.TrackBarOpacity_Scroll);
            // 
            // labelColorCell
            // 
            this.labelColorCell.BackColor = System.Drawing.Color.Red;
            this.labelColorCell.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.labelColorCell.Location = new System.Drawing.Point(58, 47);
            this.labelColorCell.Name = "labelColorCell";
            this.labelColorCell.Size = new System.Drawing.Size(25, 25);
            this.labelColorCell.TabIndex = 35;
            this.labelColorCell.Click += new System.EventHandler(this.labelColorCell_Click);
            // 
            // checkBoxDisplay
            // 
            this.checkBoxDisplay.AutoSize = true;
            this.checkBoxDisplay.Location = new System.Drawing.Point(6, 7);
            this.checkBoxDisplay.Name = "checkBoxDisplay";
            this.checkBoxDisplay.Size = new System.Drawing.Size(66, 16);
            this.checkBoxDisplay.TabIndex = 42;
            this.checkBoxDisplay.Text = "Display";
            this.checkBoxDisplay.UseVisualStyleBackColor = true;
            this.checkBoxDisplay.CheckedChanged += new System.EventHandler(this.checkBoxDisplay_CheckedChanged);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.cbxCellDes);
            this.groupBox1.Controls.Add(this.cbxCellPSC);
            this.groupBox1.Controls.Add(this.cbxCellUARFCN);
            this.groupBox1.Controls.Add(this.cbxCellCI);
            this.groupBox1.Controls.Add(this.cbxCellLAC);
            this.groupBox1.Controls.Add(this.cbxCellCode);
            this.groupBox1.Controls.Add(this.cbxCellName);
            this.groupBox1.Enabled = false;
            this.groupBox1.Location = new System.Drawing.Point(179, 6);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(130, 193);
            this.groupBox1.TabIndex = 47;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "Display Index";
            // 
            // cbxCellDes
            // 
            this.cbxCellDes.AutoSize = true;
            this.cbxCellDes.Location = new System.Drawing.Point(25, 162);
            this.cbxCellDes.Name = "cbxCellDes";
            this.cbxCellDes.Size = new System.Drawing.Size(90, 16);
            this.cbxCellDes.TabIndex = 7;
            this.cbxCellDes.Text = "Description";
            this.cbxCellDes.UseVisualStyleBackColor = true;
            this.cbxCellDes.CheckedChanged += new System.EventHandler(this.cbxCellDes_CheckedChanged);
            // 
            // cbxCellPSC
            // 
            this.cbxCellPSC.AutoSize = true;
            this.cbxCellPSC.Location = new System.Drawing.Point(25, 139);
            this.cbxCellPSC.Name = "cbxCellPSC";
            this.cbxCellPSC.Size = new System.Drawing.Size(42, 16);
            this.cbxCellPSC.TabIndex = 6;
            this.cbxCellPSC.Text = "PSC";
            this.cbxCellPSC.UseVisualStyleBackColor = true;
            this.cbxCellPSC.CheckedChanged += new System.EventHandler(this.cbxCellPSC_CheckedChanged);
            // 
            // cbxCellUARFCN
            // 
            this.cbxCellUARFCN.AutoSize = true;
            this.cbxCellUARFCN.Location = new System.Drawing.Point(25, 116);
            this.cbxCellUARFCN.Name = "cbxCellUARFCN";
            this.cbxCellUARFCN.Size = new System.Drawing.Size(60, 16);
            this.cbxCellUARFCN.TabIndex = 4;
            this.cbxCellUARFCN.Text = "UARFCN";
            this.cbxCellUARFCN.UseVisualStyleBackColor = true;
            this.cbxCellUARFCN.CheckedChanged += new System.EventHandler(this.cbxCellUARFCN_CheckedChanged);
            // 
            // cbxCellCI
            // 
            this.cbxCellCI.AutoSize = true;
            this.cbxCellCI.Location = new System.Drawing.Point(25, 93);
            this.cbxCellCI.Name = "cbxCellCI";
            this.cbxCellCI.Size = new System.Drawing.Size(36, 16);
            this.cbxCellCI.TabIndex = 3;
            this.cbxCellCI.Text = "CI";
            this.cbxCellCI.UseVisualStyleBackColor = true;
            this.cbxCellCI.CheckedChanged += new System.EventHandler(this.cbxCellCI_CheckedChanged);
            // 
            // cbxCellLAC
            // 
            this.cbxCellLAC.AutoSize = true;
            this.cbxCellLAC.Location = new System.Drawing.Point(25, 70);
            this.cbxCellLAC.Name = "cbxCellLAC";
            this.cbxCellLAC.Size = new System.Drawing.Size(42, 16);
            this.cbxCellLAC.TabIndex = 2;
            this.cbxCellLAC.Text = "LAC";
            this.cbxCellLAC.UseVisualStyleBackColor = true;
            this.cbxCellLAC.CheckedChanged += new System.EventHandler(this.cbxCellLAC_CheckedChanged);
            // 
            // cbxCellCode
            // 
            this.cbxCellCode.AutoSize = true;
            this.cbxCellCode.Location = new System.Drawing.Point(25, 47);
            this.cbxCellCode.Name = "cbxCellCode";
            this.cbxCellCode.Size = new System.Drawing.Size(48, 16);
            this.cbxCellCode.TabIndex = 1;
            this.cbxCellCode.Text = "Code";
            this.cbxCellCode.UseVisualStyleBackColor = true;
            this.cbxCellCode.CheckedChanged += new System.EventHandler(this.cbxCellCode_CheckedChanged);
            // 
            // cbxCellName
            // 
            this.cbxCellName.AutoSize = true;
            this.cbxCellName.Checked = true;
            this.cbxCellName.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbxCellName.Location = new System.Drawing.Point(25, 24);
            this.cbxCellName.Name = "cbxCellName";
            this.cbxCellName.Size = new System.Drawing.Size(48, 16);
            this.cbxCellName.TabIndex = 0;
            this.cbxCellName.Text = "Name";
            this.cbxCellName.UseVisualStyleBackColor = true;
            this.cbxCellName.CheckedChanged += new System.EventHandler(this.cbxCellName_CheckedChanged);
            // 
            // btnFont
            // 
            this.btnFont.Location = new System.Drawing.Point(6, 124);
            this.btnFont.Name = "btnFont";
            this.btnFont.Size = new System.Drawing.Size(53, 23);
            this.btnFont.TabIndex = 70;
            this.btnFont.Text = "Font...";
            this.btnFont.UseVisualStyleBackColor = true;
            this.btnFont.Click += new System.EventHandler(this.btnFont_Click);
            // 
            // cbxDrawCellLabel
            // 
            this.cbxDrawCellLabel.AutoSize = true;
            this.cbxDrawCellLabel.Location = new System.Drawing.Point(6, 101);
            this.cbxDrawCellLabel.Name = "cbxDrawCellLabel";
            this.cbxDrawCellLabel.Size = new System.Drawing.Size(102, 16);
            this.cbxDrawCellLabel.TabIndex = 69;
            this.cbxDrawCellLabel.Text = "Display Label";
            this.cbxDrawCellLabel.UseVisualStyleBackColor = true;
            this.cbxDrawCellLabel.CheckedChanged += new System.EventHandler(this.cbxDrawCellLabel_CheckedChanged);
            // 
            // MapWCellLayerCellProperties
            // 
            this.Controls.Add(this.btnFont);
            this.Controls.Add(this.cbxDrawCellLabel);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.checkBoxDisplay);
            this.Controls.Add(label100);
            this.Controls.Add(label0);
            this.Controls.Add(labelColorLabel);
            this.Controls.Add(this.labelColorCell);
            this.Controls.Add(this.TrackBarOpacity);
            this.Controls.Add(LabelOpacity);
            this.Controls.Add(labelColor);
            this.Name = "MapWCellLayerCellProperties";
            this.Size = new System.Drawing.Size(315, 205);
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private MapWCellLayer layer;

        private CheckBox checkBoxDisplay;

        private Label labelColorCell;

        private TrackBar TrackBarOpacity;
        private GroupBox groupBox1;
        private CheckBox cbxCellDes;
        private CheckBox cbxCellPSC;
        private CheckBox cbxCellUARFCN;
        private CheckBox cbxCellCI;
        private CheckBox cbxCellLAC;
        private CheckBox cbxCellCode;
        private CheckBox cbxCellName;
        private Button btnFont;
        private CheckBox cbxDrawCellLabel;

        private readonly ColorDialog colorDialog = new ColorDialog();

        private readonly FontDialog fontDialog = new FontDialog();

    }
}
