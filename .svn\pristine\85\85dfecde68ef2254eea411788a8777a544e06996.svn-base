﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage.CellCoverage;

namespace MasterCom.RAMS.ZTFunc
{
    public class CellCoverAreaQuery : QueryKPIStatByRegion
    {
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        public override string Name
        {
            get { return "小区覆盖"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 31000, 31012, this.Name);
        }

        protected override MasterCom.RAMS.Model.Interface.StatTbToken getTableNameToken()
        {
            return MasterCom.RAMS.Model.Interface.StatTbToken.cell_grid;
        }

        protected override void preparePackageCommand(Package package)
        {
            if (isQueringEvent)
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
                package.Content.PrepareAddParam();
            }
            else
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.KPI_CELL_GRID;
                package.Content.PrepareAddParam();
            }
        }

        protected override void AddExtraCondition(Package package, params object[] reservedParams)
        {
            if (!isQueringEvent)
            {
                package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
                package.Content.AddParam(cellGridBounds.x1);
                package.Content.AddParam(cellGridBounds.y2);
                package.Content.AddParam(cellGridBounds.x2);
                package.Content.AddParam(cellGridBounds.y1);
                AddDIYEndOpFlag(package);
            }
        }

        protected override void AddGeographicFilter(Package package)
        {
            if (isQueringEvent)
            {
                AddDIYEndOpFlag(package);
                package.Content.AddParam((byte)OpOptionDef.AreaSelectSample);
                package.Content.AddParam(cellGridBounds.x1);
                package.Content.AddParam(cellGridBounds.y2);
                package.Content.AddParam(cellGridBounds.x2);
                package.Content.AddParam(cellGridBounds.y1);
            }
            else
            {
                package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
                package.Content.AddParam(cellGridBounds.x1);
                package.Content.AddParam(cellGridBounds.y2);
                package.Content.AddParam(cellGridBounds.x2);
                package.Content.AddParam(cellGridBounds.y1);
            }
        }


        private Dictionary<string, ICell> cellTokenDic = null;
        private CellCoverRptTemplate template = null;
        private DbRect cellGridBounds = null;
        protected override bool getConditionBeforeQuery()
        {
            ArchiveCondition areaCond = ArchiveSettingManager.GetInstance().Condition;
            this.condition = areaCond.GetBaseConditionBackUp();

            List<ICell> selectedCells = new List<ICell>();
            foreach (Cell cell in MainModel.SelectedCells)
            {
                selectedCells.Add(cell);
            }
            foreach (TDCell cell in MainModel.SelectedTDCells)
            {
                selectedCells.Add(cell);
            }
            foreach (LTECell cell in MainModel.SelectedLTECells)
            {
                selectedCells.Add(cell);
            }
            if (selectedCells.Count == 0 && MainModel.SearchGeometrys != null
                && MainModel.SearchGeometrys.IsSelectRegion())
            {
                foreach (Cell cell in CellManager.GetInstance().GetCurrentCells())
                {
                    if (MainModel.SearchGeometrys.GeoOp.Contains(cell.Longitude, cell.Latitude))
                    {
                        selectedCells.Add(cell);
                    }
                }
            }
            ConditionDlg dlg = new ConditionDlg();
            dlg.SelectedCells = selectedCells;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            template = dlg.CurTemplate;
            cellTokenDic = new Dictionary<string, ICell>();
            double maxLng = double.MinValue, minLng = double.MaxValue, maxLat = double.MinValue, minLat = double.MaxValue;
            foreach (ICell cell in dlg.SelectedCells)
            {
                cellTokenDic[cell.Token] = cell;
                maxLng = Math.Max(maxLng, cell.Longitude);
                minLng = Math.Min(minLng, cell.Longitude);
                maxLat = Math.Max(maxLat, cell.Latitude);
                minLat = Math.Min(minLat, cell.Latitude);
            }
            cellGridBounds = new DbRect(minLng - 0.05, minLat - 0.05, maxLng + 0.05, maxLat + 0.05);
            cellCoverDic = new Dictionary<ICell, CellCoverMainItem>();
            return true;
        }

        protected override void fireShowResult()
        {
            if (cellCoverDic.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("所选小区没有栅格数据！");
                return;
            }
            CellCoverResultForm frm = MainModel.GetObjectFromBlackboard(typeof(CellCoverResultForm)) as CellCoverResultForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new CellCoverResultForm();
                frm.Owner = MainModel.MainForm;
            }
            frm.FillData(template, new List<CellCoverMainItem>(cellCoverDic.Values));
            frm.Visible = true;
            frm.BringToFront();
            cellTokenDic = null;
            cellCoverDic = null;
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> formulaSet = new List<string>();
            foreach (MasterCom.RAMS.ZTFunc.AreaArchiveManage.CellCoverage.TemplateColumn col in template.Columns)
            {
                formulaSet.Add(col.Expression);
            }
            return getTriadIDIgnoreServiceType(formulaSet);
        }

        private Dictionary<ICell, CellCoverMainItem> cellCoverDic = null;
        protected override void recieveAndHandleSpecificStatData(Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            int lac = package.Content.GetParamInt();
            int ci = package.Content.GetParamInt();
            string cellToken = string.Format("{0}_{1}", lac, ci);
            ICell cell;
            if (!cellTokenDic.TryGetValue(cellToken, out cell))
            {
                return;
            }
            package.Content.GetParamDouble();//lng
            package.Content.GetParamDouble();//lat
            fillStatData(package, curImgColumnDef, singleStatData);
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(singleStatData.FileID);
            CellCoverMainItem cellItem;
            if (!cellCoverDic.TryGetValue(cell,out cellItem))
            {
                cellItem = new CellCoverMainItem(cell);
                cellCoverDic[cell] = cellItem;
            }
            cellItem.AddStatData(fi, singleStatData, false);
        }

        protected override void handleStatEvent(Event evt)
        {
            object lac = evt["LAC"];
            object ci = evt["CI"];
            if (lac == null || ci == null)
            {
                return;
            }
            string cellToken = string.Format("{0}_{1}", lac, ci);
            ICell cell;
            if (!cellTokenDic.TryGetValue(cellToken, out cell))
            {
                return;
            }
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);

            CellCoverMainItem cellItem;
            if (!cellCoverDic.TryGetValue(cell, out cellItem))
            {
                cellItem = new CellCoverMainItem(cell);
                cellCoverDic[cell] = cellItem;
            }
            cellItem.AddStatData(fi, eventData, false);
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            if (cellCoverDic == null || cellCoverDic.Count == 0)
            {
                return;
            }
            MasterCom.Util.UiEx.WaitTextBox.Show("正在关联区域信息...", makeSummary);
        }

        private void makeSummary()
        {
            try
            {
                List<AreaBase> areas = ZTAreaManager.Instance.GetArea(ZTAreaManager.Instance.Ranks[0]);
                foreach (CellCoverMainItem cellItem in cellCoverDic.Values)
                {
                    cellItem.MakeSummary(template, areas);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("小区关联村庄异常" + Environment.NewLine + ex.Message);
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                MasterCom.Util.UiEx.WaitTextBox.Close();
            }
        }

    }
}
