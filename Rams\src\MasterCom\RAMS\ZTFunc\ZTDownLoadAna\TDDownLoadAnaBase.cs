﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using System.Threading;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    enum TDEventID
    {
        Download_Began = 57,
        Download_Success = 58,
        Download_Fail = 59,

        TD_RAU_Request = 131,
        TD_GSM_RAU_Request = 134,

        TD_RAU_Success = 132,
        TD_GSM_RAU_Success = 135,

        TD_HandoverRequest_T2G = 141,
        TD_HandoverRequest_IntraT = 144,
        TD_HandoverRequest_Baton = 147,
        TD_HandvoerRequest_IntraG = 150,
        TD_HandoverRequest_RBReConfigT = 231,

        TD_HandoverSuccess_T2G = 142,
        TD_HandoverSuccess_IntraT = 145,
        TD_HandoverSuccess_Baton = 148,
        TD_HandvoerSuccess_IntraG = 151,
        TD_HandoverSuccess_RBReConfigT = 232,

        TD_CellReselection_T2G = 137,
        TD_CellReselection_G2T = 139,
        TD_CellReselection_T2T = 179,
        TD_CellReselection_G2G = 181,

        TD_CellUpdate = 177,
        TD_CellUpdateConfirm = 178,

    }

    public abstract class TDDownLoadQueryAnaBase : QueryBase
    {
        private List<FileInfo> file2Stat { get; set; }
        private List<TDFtpFileInfo> fileInfoList { get; set; }

        private int span = 10;  //样本间隔，10秒

        protected TDDownLoadQueryAnaBase(MainModel mm)
            : base(mm)
        {
        }
        public override string Name
        {
            get { return "下载速率分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13030, this.Name);
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected List<string> prepareSampleQryColumnNames()
        {
            List<string> colNames;
            colNames = new List<string>();
            colNames.Add("itime");
            colNames.Add("ilongitude");
            colNames.Add("ilatitude");
            colNames.Add("ifileid");
            colNames.Add("isampleid");
            colNames.Add("TD_SCell_LAC");
            colNames.Add("TD_SCell_CI");
            colNames.Add("TD_SCell_UARFCN");
            colNames.Add("TD_SCell_CPI");
            colNames.Add("TD_NCell_PCCPCH_RSCP");
            colNames.Add("TD_NCell_UARFCN");
            colNames.Add("TD_NCell_CPI");
            colNames.Add("TD_APP_DataStatus_DL");
            colNames.Add("TD_APP_ThroughputDL");
            colNames.Add("TD_PCCPCH_RSCP");
            colNames.Add("TD_PCCPCH_C2I");
            colNames.Add("TD_DPCH_RSCP");
            colNames.Add("TD_DPCH_C2I");
            colNames.Add("TD_HSDPA_HS_SCCH_CI");
            colNames.Add("TD_HSDPA_HS_PDSCH_CI");
            colNames.Add("TD_BLER");
            colNames.Add("TD_HSDPA_HS_PDSCH_TotalBLER");
            colNames.Add("TD_HSDPA_CQI_Max");
            colNames.Add("TD_HSDPA_CQI_Min");
            colNames.Add("TD_HSDPA_CQI_Mean");
            colNames.Add("TD_HSDPA_HS_ScchScheduled_Count");
            colNames.Add("TD_HSDPA_HS_ScchScheduled_Rate");
            colNames.Add("TD_HSDPA_HS_PDSCH_TimeSlotUsed");
            colNames.Add("TD_HSDPA_HS_16QAM_Rate");
            colNames.Add("TD_HSDPA_HS_QPSK_Rate");
            colNames.Add("TD_TxPower");
            return colNames;
        }

        public bool NeedJudgeTestPointByRegion { get; set; } = false;

        protected virtual void queryDownLoadFiles()
        {
            DIYReplayFileQueryByCustom repFile = new DIYReplayFileQueryByCustom(MainModel);
            if (!NeedJudgeTestPointByRegion)
            {
                condition.Geometorys = null;
            }
            repFile.SetQueryCondition(condition);
            repFile.SetReplayContent(prepareSampleQryColumnNames(), true, false);
            repFile.Query();
            MainModel.DTDataManager.Sort();
        }

        TDDownLoadSetForm tdDownLoadSetForm = null;
        private bool setSpan()
        {
            if (tdDownLoadSetForm == null)
            {
                tdDownLoadSetForm = new TDDownLoadSetForm();
            }

            if (tdDownLoadSetForm.ShowDialog() == DialogResult.OK)
            {
                tdDownLoadSetForm.GetSettingFilterRet(out span);
                return true;
            }
            else
            {
                return false;
            }
        }

        protected override void query()
        {
            if (!setSpan())
            {
                return;
            }

            file2Stat = new List<FileInfo>(condition.FileInfos);
            fileInfoList = new List<TDFtpFileInfo>();

            foreach (FileInfo fi in file2Stat)
            {
                try
                {
                    condition.FileInfos.Clear();
                    condition.FileInfos.Add(fi);
                    MainModel.ClearDTData();
                    if (fi.ServiceType == (int)Model.ServiceType.TDSCDMA_DATA || fi.ServiceType == (int)Model.ServiceType.TDSCDMA_HSDPA
                        || fi.ServiceType == (int)Model.ServiceType.TDSCDMA_HSUPA)
                    {
                        queryDownLoadFiles();
                        WaitBox.Show("开始分析..." + fi.Name, anaEachFile, MainModel.DTDataManager.FileDataManagers);
                    }
                }
                catch
                {
                   //continue
                }
            }

            if (fileInfoList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(TDDownLoadAnaListForm).FullName);
            TDDownLoadAnaListForm mosListForm = obj == null ? null : obj as TDDownLoadAnaListForm;
            if (mosListForm == null || mosListForm.IsDisposed)
            {
                mosListForm = new TDDownLoadAnaListForm(MainModel);
            }

            mosListForm.showAnalyseInfo(fileInfoList);
            if (!mosListForm.Visible)
            {
                mosListForm.Show(MainModel.MainForm);
            }
        }

        protected virtual void anaEachFile(object o)
        {
            try
            {
                List<DTFileDataManager> fileMngrs = o as List<DTFileDataManager>;
                foreach (DTFileDataManager fileMngr in fileMngrs)
                {
                    dealFileData(fileMngr);
                }
            }
            finally
            {
                Thread.Sleep(1000);
                WaitBox.Close();
            }
        }

        private void dealFileData(DTFileDataManager fileMngr)
        {
            TDFtpFileInfo fileInfo = new TDFtpFileInfo();
            fileInfo.fileName = fileMngr.FileName;
            fileInfoList.Add(fileInfo);

            int startIndex = 0;
            int counter = 0;
            int curPercent = 11;
            string startTime = null;

            List<Event> eventList = new List<Event>();
            foreach (Event e in fileMngr.Events)
            {
                if (startTime == null && e.ID == (int)TDEventID.Download_Began)
                {
                    startTime = e.DateTime.ToString();
                    eventList.Add(e);
                    continue;
                }
                if (startTime != null)
                {
                    eventList.Add(e);
                    if (e.ID == (int)TDEventID.Download_Success || e.ID == (int)TDEventID.Download_Fail)
                    {
                        List<TestPoint> tpList = findTestPoints(fileMngr.TestPoints, DateTime.Parse(startTime), e.DateTime, ref startIndex);
                        processEachFTPEvent(eventList, tpList, DateTime.Parse(startTime), e.DateTime, e.ID, (int)e["TargetCI"], ref fileInfo);
                        startTime = null;
                        eventList.Clear();
                    }
                }

                int tmp = (int)(Math.Log(counter++) * (100000 / 10000));
                if (tmp < 95 && tmp > 0 && curPercent != tmp)
                {
                    WaitBox.ProgressPercent = tmp;
                    curPercent = tmp;
                }
                else if (tmp > 95)
                {
                    curPercent = 5;
                    counter = 0;
                }
            }
        }

        private List<TestPoint> findTestPoints(List<TestPoint> testpointList, DateTime startTime, DateTime endTime, ref int startIndex)
        {
            List<TestPoint> tpList = new List<TestPoint>();
            for (int i = startIndex; i < testpointList.Count; i++)
            {
                if (testpointList[i].DateTime >= startTime)
                {
                    if (startIndex <= 0)
                    {
                        startIndex = i;
                    }
                    if (testpointList[i].DateTime <= endTime)
                    {
                        tpList.Add(testpointList[i]);
                    }
                    else
                    {
                        startIndex = i;
                        break;
                    }
                }
            }
            return tpList;
        }

        private void processEachFTPEvent(List<Event> eventList, List<TestPoint> tpList, DateTime startTime, DateTime endTime, int eventID, int ipAddr, ref TDFtpFileInfo fileInfo)
        {
            if (tpList.Count <= 0)
            {
                return;
            }

            TDFtpEventInfo eventInfo = new TDFtpEventInfo();
            TDFtpSampleInfo sampleInfo = new TDFtpSampleInfo();

            DateTime bTime = startTime;
            DateTime eTime = bTime.AddSeconds(span);

            foreach (TestPoint tp in tpList)
            {
                if (tp.DateTime.CompareTo(bTime) >= 0 && tp.DateTime.CompareTo(eTime) <= 0) //在10秒内的采样点
                {
                    sampleInfo.MergePoint(tp);  //10秒内的数据记录在一个样本中
                }
                else
                {
                    sampleInfo.sn = eventInfo.sampleInfoList.Count + 1;
                    sampleInfo.startTime = bTime;
                    sampleInfo.endTime = eTime;
                    sampleInfo.SetMidCoordinate();
                    getEventCount(eventList,ref sampleInfo);    //查找本时间段的切换和路由更新
                    eventInfo.sampleInfoList.Add(sampleInfo);

                    bTime = eTime;
                    eTime = bTime.AddSeconds(span);
                    sampleInfo = new TDFtpSampleInfo();
                }
            }

            //最后一个样本，直接加入到EventInfo中
            sampleInfo.sn = eventInfo.sampleInfoList.Count + 1;
            sampleInfo.startTime = bTime;
            sampleInfo.endTime = endTime;   //真正的结束时间
            sampleInfo.SetMidCoordinate();
            getEventCount(eventList, ref sampleInfo);    //查找本时间段的切换和路由更新
            eventInfo.sampleInfoList.Add(sampleInfo);

            //对EventInfo进行处理
            eventInfo.mergeData();
            eventInfo.startTime = startTime;
            eventInfo.endTime = endTime;
            eventInfo.setMidCoordinate();
            if (eventID == (int)TDEventID.Download_Fail)
            {
                eventInfo.isFail = true;
            }
            eventInfo.sn = fileInfo.eventInfoList.Count + 1;
            eventInfo.ipAddr = TDFtpEventInfo.getIPAddrFromInt(ipAddr);

            //放入文件列表中
            fileInfo.eventInfoList.Add(eventInfo);
        }

        private void getEventCount(List<Event> eventList, ref TDFtpSampleInfo sampleInfo)
        {
            foreach (Event e in eventList)
            {
                if (e.DateTime.CompareTo(sampleInfo.startTime) >= 0 && e.DateTime.CompareTo(sampleInfo.endTime) <= 0)
                {
                    addEvtCount(sampleInfo, e);
                }
            }
        }

        private static void addEvtCount(TDFtpSampleInfo sampleInfo, Event e)
        {
            if (e.ID == (int)TDEventID.TD_HandoverRequest_T2G || e.ID == (int)TDEventID.TD_HandoverRequest_IntraT
                || e.ID == (int)TDEventID.TD_HandoverRequest_Baton || e.ID == (int)TDEventID.TD_HandoverSuccess_RBReConfigT //针对RBReconfig，成功和请求用同一个ID
                || e.ID == (int)TDEventID.TD_HandvoerRequest_IntraG)
            {
                sampleInfo.baseInfo.HORequestCount++;
            }
            else if (e.ID == (int)TDEventID.TD_HandoverSuccess_T2G || e.ID == (int)TDEventID.TD_HandoverSuccess_IntraT
                || e.ID == (int)TDEventID.TD_HandoverSuccess_Baton || e.ID == (int)TDEventID.TD_HandoverSuccess_RBReConfigT
                || e.ID == (int)TDEventID.TD_HandvoerSuccess_IntraG)
            {
                sampleInfo.baseInfo.HOSuccessCount++;
            }
            else if (e.ID == (int)TDEventID.TD_RAU_Request || e.ID == (int)TDEventID.TD_GSM_RAU_Request)
            {
                sampleInfo.baseInfo.RAURequestCount++;
            }
            else if (e.ID == (int)TDEventID.TD_RAU_Success || e.ID == (int)TDEventID.TD_GSM_RAU_Success)
            {
                sampleInfo.baseInfo.RAUSuccessCount++;
            }
            else if (e.ID == (int)TDEventID.TD_CellUpdate)
            {
                sampleInfo.baseInfo.TDCellUpdate++;
            }
            else if (e.ID == (int)TDEventID.TD_CellUpdateConfirm)
            {
                sampleInfo.baseInfo.TDCellUpdateConfirm++;
            }
        }
    }

    public class TDFtpFileInfo
    {
        public string fileName { get; set; } = "";
        public List<TDFtpEventInfo> eventInfoList { get; set; } = new List<TDFtpEventInfo>();
    }

    public class TDFtpEventInfo
    {
        public int sn { get; set; }
        public int sampleNum { get; set; }
        public DateTime startTime { get; set; }
        public DateTime endTime { get; set; }
        public bool isFail { get; set; } = false; //是否为下载失败事件
        public TDFtpBaseInfo baseInfo { get; set; } = new TDFtpBaseInfo();
        public List<TDFtpSampleInfo> sampleInfoList { get; set; } = new List<TDFtpSampleInfo>();
        public double midLong { get; set; }
        public double midLat { get; set; }
        public string ipAddr { get; set; } = "";

        public static string getIPAddrFromInt(int ipAddr)
        {
            return ((ipAddr & 0xFF000000) / Math.Pow(16, 6)).ToString() + '.' + ((ipAddr & 0x00FF0000) / Math.Pow(16, 4)).ToString() + '.'
                 + ((ipAddr & 0x0000FF00) / Math.Pow(16, 2)).ToString() + '.' + (ipAddr & 0x000000FF).ToString();

        }
        public void mergeData()
        {
            for (int i = 0; i < sampleInfoList.Count; i++)
            {
                sampleNum += sampleInfoList[i].sampleNum;
                baseInfo.speedTotal += sampleInfoList[i].baseInfo.speedTotal;
                baseInfo.speed500KSample += sampleInfoList[i].baseInfo.speed500KSample;
                baseInfo.speed0KSample += sampleInfoList[i].baseInfo.speed0KSample;
                baseInfo.speedSample += sampleInfoList[i].baseInfo.speedSample;
                baseInfo.pccpchRscpTotal += sampleInfoList[i].baseInfo.pccpchRscpTotal;
                baseInfo.pccpchRscpF85Sample += sampleInfoList[i].baseInfo.pccpchRscpF85Sample;
                baseInfo.pccpchRscpSample += sampleInfoList[i].baseInfo.pccpchRscpSample;
                baseInfo.pccpchC2iTotal += sampleInfoList[i].baseInfo.pccpchC2iTotal;
                baseInfo.pccpchC2iF3Sample += sampleInfoList[i].baseInfo.pccpchC2iF3Sample;
                baseInfo.pccpchC2iSample += sampleInfoList[i].baseInfo.pccpchC2iSample;

                baseInfo.dpchRscpTotal += sampleInfoList[i].baseInfo.dpchRscpTotal;
                baseInfo.dpchRscpF85Sample += sampleInfoList[i].baseInfo.dpchRscpF85Sample;
                baseInfo.dpchRscpSample += sampleInfoList[i].baseInfo.dpchRscpSample;
                baseInfo.dpchC2iTotal += sampleInfoList[i].baseInfo.dpchC2iTotal;
                baseInfo.dpchC2iF3Sample += sampleInfoList[i].baseInfo.dpchC2iF3Sample;
                baseInfo.dpchC2iSample += sampleInfoList[i].baseInfo.dpchC2iSample;

                baseInfo.hsscchC2iTotal += sampleInfoList[i].baseInfo.hsscchC2iTotal;
                baseInfo.hsscchC2iF3Sample += sampleInfoList[i].baseInfo.hsscchC2iF3Sample;
                baseInfo.hsscchC2i5Sample += sampleInfoList[i].baseInfo.hsscchC2i5Sample;
                baseInfo.hsscchC2iSample += sampleInfoList[i].baseInfo.hsscchC2iSample;
                baseInfo.hspdschC2iTotal += sampleInfoList[i].baseInfo.hspdschC2iTotal;
                baseInfo.hspdschC2iF3Sample += sampleInfoList[i].baseInfo.hspdschC2iF3Sample;
                baseInfo.hspdschC2i5Sample += sampleInfoList[i].baseInfo.hspdschC2i5Sample;
                baseInfo.hspdschC2iSample += sampleInfoList[i].baseInfo.hspdschC2iSample;

                baseInfo.blerTotal += sampleInfoList[i].baseInfo.blerTotal;
                baseInfo.bler5Sample += sampleInfoList[i].baseInfo.bler5Sample;
                baseInfo.blerSample += sampleInfoList[i].baseInfo.blerSample;

                baseInfo.hspdcshBlerTotal += sampleInfoList[i].baseInfo.hspdcshBlerTotal;
                baseInfo.hspdcshBler5Sample += sampleInfoList[i].baseInfo.hspdcshBler5Sample;
                baseInfo.hspdcshBlerSample += sampleInfoList[i].baseInfo.hspdcshBlerSample;

                baseInfo.hsCqiMaxTotal += sampleInfoList[i].baseInfo.hsCqiMaxTotal;
                baseInfo.hsCqiMinTotal += sampleInfoList[i].baseInfo.hsCqiMinTotal;
                baseInfo.hsCqiAvgTotal += sampleInfoList[i].baseInfo.hsCqiAvgTotal;
                baseInfo.hsCqiSample += sampleInfoList[i].baseInfo.hsCqiSample;

                baseInfo.hs16QamRateTotal += sampleInfoList[i].baseInfo.hs16QamRateTotal;
                baseInfo.hsQpskRateTotal += sampleInfoList[i].baseInfo.hsQpskRateTotal;
                baseInfo.hsCodecSample += sampleInfoList[i].baseInfo.hsCodecSample;

                baseInfo.hspdschTimeSlotUsedTotal += sampleInfoList[i].baseInfo.hspdschTimeSlotUsedTotal;
                baseInfo.hspdschTimeSlotUsedSample += sampleInfoList[i].baseInfo.hspdschTimeSlotUsedSample;

                baseInfo.txPowerTotal += sampleInfoList[i].baseInfo.txPowerTotal;
                baseInfo.txPowerSample += sampleInfoList[i].baseInfo.txPowerSample;
                baseInfo.txPower24Sample += sampleInfoList[i].baseInfo.txPower24Sample;

                baseInfo.HORequestCount += sampleInfoList[i].baseInfo.HORequestCount;
                baseInfo.RAURequestCount += sampleInfoList[i].baseInfo.RAURequestCount;
                baseInfo.HOSuccessCount += sampleInfoList[i].baseInfo.HOSuccessCount;
                baseInfo.RAUSuccessCount += sampleInfoList[i].baseInfo.RAUSuccessCount;
                baseInfo.TDCellUpdate += sampleInfoList[i].baseInfo.TDCellUpdate;
                baseInfo.TDCellUpdateConfirm += sampleInfoList[i].baseInfo.TDCellUpdateConfirm;
                baseInfo.pollutSample += sampleInfoList[i].baseInfo.pollutSample;
                baseInfo.GSMSample += sampleInfoList[i].baseInfo.GSMSample;
                baseInfo.R4Sample += sampleInfoList[i].baseInfo.R4Sample;
                baseInfo.HSPASample += sampleInfoList[i].baseInfo.HSPASample;
            }            
        }

        public void setMidCoordinate()
        {
            if (sampleInfoList.Count > 0)
            {
                midLong = sampleInfoList[(sampleInfoList.Count - 1) / 2].midLong;
                midLat = sampleInfoList[(sampleInfoList.Count - 1) / 2].midLat;
            }
        }
     }

    public class TDFtpSampleInfo
    {
        public int sn { get; set; }
        public int sampleNum { get; set; }
        public DateTime startTime { get; set; }
        public DateTime endTime { get; set; }
        public TDFtpBaseInfo baseInfo { get; set; } = new TDFtpBaseInfo();
        public List<TestPoint> tpList { get; set; } = new List<TestPoint>();
        public double midLong { get; set; }
        public double midLat { get; set; }

        public void MergePoint(TestPoint tp)
        {
            this.sampleNum++;
            this.tpList.Add(tp);
            baseInfo.MergeTestPoint(tp);
        }

        public void SetMidCoordinate()
        {
            if (tpList.Count > 0)
            {
                midLong = tpList[(tpList.Count - 1) / 2].Longitude;
                midLat = tpList[(tpList.Count - 1) / 2].Latitude;
            }
        }
    }

    public class TDFtpBaseInfo
    {
        public float speedTotal { get; set; }
        public float speed500KSample { get; set; }
        public float speed0KSample { get; set; }
        public float speedSample { get; set; }
        public string Speed500KRateString
        {
            get { return speedSample == 0 ? "-" : Math.Round(100 * speed500KSample / speedSample, 2).ToString(); }
        }
        public string Speed0KRateString
        {
            get { return speedSample == 0 ? "-" : Math.Round(100 * speed0KSample / speedSample, 2).ToString(); }
        }
        public string SpeedAvgString
        {
            get { return speedSample == 0 ? "-" : Math.Round(speedTotal / (1024 * speedSample), 2).ToString(); }
        }

        public float pccpchRscpTotal { get; set; }
        public float pccpchRscpF85Sample { get; set; }
        public int pccpchRscpSample { get; set; }
        public string PccpchRscpF85RateString
        {
            get { return pccpchRscpSample == 0 ? "-" : Math.Round(100 * pccpchRscpF85Sample / pccpchRscpSample, 2).ToString(); }
        }
        public string PccpchRscpAvgString
        {
            get { return pccpchRscpSample == 0 ? "-" : Math.Round(pccpchRscpTotal / pccpchRscpSample, 2).ToString(); }
        }

        public float pccpchC2iTotal { get; set; }
        public float pccpchC2iF3Sample { get; set; }
        public float pccpchC2iSample { get; set; }
        public string PccpchCIF3RateString
        {
            get { return pccpchC2iSample == 0 ? "-" : Math.Round(100 * pccpchC2iF3Sample / pccpchC2iSample, 2).ToString(); }
        }
        public string PccpchCIAvgString
        {
            get { return pccpchC2iSample == 0 ? "-" : Math.Round(pccpchC2iTotal / pccpchC2iSample, 2).ToString(); }
        }

        public float dpchRscpTotal { get; set; }
        public float dpchRscpF85Sample { get; set; }
        public int dpchRscpSample { get; set; }
        public string DpchRscpF85RateString
        {
            get { return dpchRscpSample == 0 ? "-" : Math.Round(100 * dpchRscpF85Sample / dpchRscpSample, 2).ToString(); }
        }
        public string DpchRscpAvgString
        {
            get { return dpchRscpSample == 0 ? "-" : Math.Round(dpchRscpTotal / dpchRscpSample, 2).ToString(); }
        }

        public float dpchC2iTotal { get; set; }
        public float dpchC2iF3Sample { get; set; }
        public float dpchC2iSample { get; set; }
        public string DpchCIF3RateString
        {
            get { return dpchC2iSample == 0 ? "-" : Math.Round(100 * dpchC2iF3Sample / dpchC2iSample, 2).ToString(); }
        }
        public string DpchCIAvgString
        {
            get { return dpchC2iSample == 0 ? "-" : Math.Round(dpchC2iTotal / dpchC2iSample, 2).ToString(); }
        }

        public float hsscchRscpTotal { get; set; }
        public float hsscchRscpSample { get; set; }
        public string HSScchRSCPAvgString
        {
            get { return hsscchRscpSample == 0 ? "-" : Math.Round(hsscchRscpTotal / hsscchRscpSample, 2).ToString(); }
        }

        public float hsscchIscpTotal { get; set; }
        public float hsscchIscpSample { get; set; }
        public string HSScchISCPAvgString
        {
            get { return hsscchIscpSample == 0 ? "-" : Math.Round(hsscchIscpTotal / hsscchIscpSample, 2).ToString(); }
        }

        public float hsscchC2iTotal { get; set; }
        public float hsscchC2iF3Sample { get; set; }
        public float hsscchC2i5Sample { get; set; }
        public float hsscchC2iSample { get; set; }
        public string HSScchCIF3RateString
        {
            get { return hsscchC2iSample == 0 ? "-" : Math.Round(100 * hsscchC2iF3Sample / hsscchC2iSample, 2).ToString(); }
        }
        public string HSScchCI5RateString
        {
            get { return hsscchC2iSample == 0 ? "-" : Math.Round(100 * hsscchC2i5Sample / hsscchC2iSample, 2).ToString(); }
        }
        public string HSScchCIAvgString
        {
            get { return hsscchC2iSample == 0 ? "-" : Math.Round(hsscchC2iTotal / hsscchC2iSample, 2).ToString(); }
        }

        public float hspdschRscpTotal { get; set; }
        public float hspdschRscpSample { get; set; }
        public string HSPdschRSCPAvgString
        {
            get { return hspdschRscpSample == 0 ? "-" : Math.Round(hspdschRscpTotal / hspdschRscpSample, 2).ToString(); }
        }

        public float hspdschC2iTotal { get; set; }
        public float hspdschC2iF3Sample { get; set; }
        public float hspdschC2i5Sample { get; set; }
        public float hspdschC2iSample { get; set; }
        public string HSPdschCIF3RateString
        {
            get { return hspdschC2iSample == 0 ? "-" : Math.Round(100 * hspdschC2iF3Sample / hspdschC2iSample, 2).ToString(); }
        }
        public string HSPdschCI5RateString
        {
            get { return hspdschC2iSample == 0 ? "-" : Math.Round(100 * hspdschC2i5Sample / hspdschC2iSample, 2).ToString(); }
        }
        public string HSPdschCIAvgString
        {
            get { return hspdschC2iSample == 0 ? "-" : Math.Round(hspdschC2iTotal / hspdschC2iSample, 2).ToString(); }
        }

        public float hsCqiMaxTotal { get; set; }
        public float hsCqiMinTotal { get; set; }
        public float hsCqiAvgTotal { get; set; }
        public float hsCqiSample { get; set; }
        public string HSCqiMaxAvgString
        {
            get { return hsCqiSample == 0 ? "-" : Math.Round(hsCqiMaxTotal / hsCqiSample, 2).ToString(); }
        }
        public string HSCqiMinAvgString
        {
            get { return hsCqiSample == 0 ? "-" : Math.Round(hsCqiMinTotal / hsCqiSample, 2).ToString(); }
        }
        public string HSCqiAvgAvgString
        {
            get { return hsCqiSample == 0 ? "-" : Math.Round(hsCqiAvgTotal / hsCqiSample, 2).ToString(); }
        }

        public float hs16QamRateTotal { get; set; }
        public float hsQpskRateTotal { get; set; }
        public float hsCodecSample { get; set; }
        public string HS16QAMRateAvgString
        {
            get { return hsCodecSample == 0 ? "-" : Math.Round(hs16QamRateTotal / hsCodecSample, 2).ToString(); }
        }
        public string HSQPSKRateAvgString
        {
            get { return hsCodecSample == 0 ? "-" : Math.Round(hsQpskRateTotal / hsCodecSample, 2).ToString(); }
        }

        public float blerTotal { get; set; }
        public float bler5Sample { get; set; }
        public float blerSample { get; set; }
        public string Bler5RateString
        {
            get { return blerSample == 0 ? "-" : Math.Round(100 * bler5Sample / blerSample, 2).ToString(); }
        }
        public string BlerAvgString
        {
            get { return blerSample == 0 ? "-" : Math.Round(blerTotal / blerSample, 2).ToString(); }
        }

        public float hspdcshBlerTotal { get; set; }
        public float hspdcshBler5Sample { get; set; }
        public float hspdcshBlerSample { get; set; }
        public string HSPdschBler5RateString
        {
            get { return hspdcshBlerSample == 0 ? "-" : Math.Round(100 * hspdcshBler5Sample / hspdcshBlerSample, 2).ToString(); }
        }
        public string HSPdschBlerAvgString
        {
            get { return hspdcshBlerSample == 0 ? "-" : Math.Round(hspdcshBlerTotal / hspdcshBlerSample, 2).ToString(); }
        }

        public float hsscchScheduledCountTotal { get; set; }
        public float hsscchScheduledRateTotal { get; set; }
        public float hsscchScheduledRateSample { get; set; }

        public float hspdschTimeSlotUsedTotal { get; set; }
        public float hspdschTimeSlotUsedSample { get; set; }
        public string TimeSlotUsedAvgString
        {
            get { return hspdschTimeSlotUsedSample == 0 ? "-" : Math.Round(hspdschTimeSlotUsedTotal / hspdschTimeSlotUsedSample, 2).ToString(); }
        }

        public float txPowerTotal { get; set; }
        public float txPower24Sample { get; set; }
        public float txPowerSample { get; set; }
        public string TxPower24RateString
        {
            get { return txPowerSample == 0 ? "-" : Math.Round(100 * txPower24Sample / txPowerSample, 2).ToString(); }
        }
        public string TxPowerAvgString
        {
            get { return txPowerSample == 0 ? "-" : Math.Round(txPowerTotal / txPowerSample, 2).ToString(); }
        }

        public int HORequestCount { get; set; }
        public int RAURequestCount { get; set; }

        public int HOSuccessCount { get; set; }
        public int RAUSuccessCount { get; set; }

        /// <summary>
        /// 小区更新
        /// </summary>
        public int TDCellUpdate { get; set; }
        public int TDCellUpdateConfirm { get; set; }

        public int CellReselectionCount { get; set; }

        public int downLoadFailCount { get; set; }

        public int pollutSample { get; set; }
        public int GSMSample { get; set; }
        public int R4Sample { get; set; }
        public int HSPASample { get; set; }

        public void MergeTestPoint(TestPoint tp)
        {
            this.mergeSpeed(tp);
            this.mergeStatus(tp);
            this.mergePccpchRscp(tp);
            this.mergePccpchC2i(tp);
            this.mergeDpchRscp(tp);
            this.mergeDpchC2i(tp);
            this.mergeHsscchRscp(tp);
            this.mergeHsscchIscp(tp);
            this.mergeHsscchC2i(tp);
            this.mergeHspdschRscp(tp);
            this.mergeHspdschC2i(tp);
            this.mergeHspdschBler(tp);
            this.mergeBler(tp);
            this.mergeCqi(tp);
            this.mergeCodec(tp);
            this.mergeTimeSlot(tp);
            this.mergeSchedule(tp);
            this.mergePollute(tp);
            this.mergeTxPower(tp);
        }

        private void mergeSpeed(TestPoint tp)
        {
            int? speed = (int?)tp["TD_APP_ThroughputDL"];

            if (speed != null && speed >= 0)
            {
                speedTotal += (float)speed;
                speedSample++;
                if (speed >= (500 * 1000))
                {
                    speed500KSample++;
                }
                if (speed == 0)
                {
                    speed0KSample++;
                }
            }
        }

        private void mergeStatus(TestPoint tp)
        {
            int? dlStatus = (int?)tp["TD_APP_DataStatus_DL"];

            if (dlStatus != null && dlStatus >= 0)
            {
                if (dlStatus == 1 || dlStatus == 2 || dlStatus == 3)
                {
                    R4Sample++;
                }
                else if (dlStatus == 4 || dlStatus == 8)
                {
                    GSMSample++;
                }
                else if (dlStatus == 5)
                {
                    HSPASample++;
                }
            }
        }

        private void mergePccpchRscp(TestPoint tp)
        {
            float? pccpchRscp = (float?)tp["TD_PCCPCH_RSCP"];

            if (pccpchRscp != null && pccpchRscp >= -140 && pccpchRscp <= -10)
            {
                pccpchRscpTotal += (float)pccpchRscp;
                pccpchRscpSample++;
                if (pccpchRscp >= -85)
                {
                    pccpchRscpF85Sample++;
                }
            }
        }

        private void mergePccpchC2i(TestPoint tp)
        {
            int? pccpchC2i = (int?)tp["TD_PCCPCH_C2I"];

            if (pccpchC2i != null && pccpchC2i >= -30 && pccpchC2i <= 40)
            {
                pccpchC2iTotal += (float)pccpchC2i;
                pccpchC2iSample++;
                if (pccpchC2i >= -3)
                {
                    pccpchC2iF3Sample++;
                }
            }
        }

        private void mergeDpchRscp(TestPoint tp)
        {
            float? dpchRscp = (float?)tp["TD_DPCH_RSCP"];

            if (dpchRscp != null && dpchRscp >= -140 && dpchRscp <= -10)
            {
                dpchRscpTotal += (float)dpchRscp;
                dpchRscpSample++;
                if (dpchRscp >= -85)
                {
                    dpchRscpF85Sample++;
                }
            }
        }

        private void mergeDpchC2i(TestPoint tp)
        {
            int? dpchC2i = (int?)tp["TD_DPCH_C2I"];

            if (dpchC2i != null && dpchC2i >= -30 && dpchC2i <= 40)
            {
                dpchC2iTotal += (float)dpchC2i;
                dpchC2iSample++;
                if (dpchC2i >= -3)
                {
                    dpchC2iF3Sample++;
                }
            }
        }

        private void mergeHsscchRscp(TestPoint tp)
        {
            int? hsscchRscp = (int?)tp["TD_HSDPA_HS_SCCH_RSCP"];

            if (hsscchRscp != null && hsscchRscp >= -140 && hsscchRscp <= -10)
            {
                hsscchRscpTotal += (float)hsscchRscp;
                hsscchRscpSample++;
            }
        }

        private void mergeHsscchIscp(TestPoint tp)
        {
            int? hsscchIscp = (int?)tp["TD_HSDPA_HS_SCCH_ISCP"];

            if (hsscchIscp != null && hsscchIscp >= -140 && hsscchIscp <= -10)
            {
                hsscchIscpTotal += (float)hsscchIscp;
                hsscchIscpSample++;
            }
        }

        private void mergeHsscchC2i(TestPoint tp)
        {
            int? hsscchC2i = (int?)tp["TD_HSDPA_HS_SCCH_CI"];

            if (hsscchC2i != null && hsscchC2i >= -30 && hsscchC2i <= 40)
            {
                hsscchC2iTotal += (float)hsscchC2i;
                hsscchC2iSample++;
                if (hsscchC2i >= -3)
                {
                    hsscchC2iF3Sample++;
                }
                if (hsscchC2i >= 5)
                {
                    hsscchC2i5Sample++;
                }
            }
        }

        private void mergeHspdschRscp(TestPoint tp)
        {
            int? hspdschRscp = (int?)tp["TD_HSDPA_HS_PDSCH_RSCP"];

            if (hspdschRscp != null && hspdschRscp >= -140 && hspdschRscp <= -10)
            {
                hspdschRscpTotal += (float)hspdschRscp;
                hspdschRscpSample++;
            }
        }

        private void mergeHspdschC2i(TestPoint tp)
        {
            int? hspdschC2i = (int?)tp["TD_HSDPA_HS_PDSCH_CI"];

            if (hspdschC2i != null && hspdschC2i >= -30 && hspdschC2i <= 40)
            {
                hspdschC2iTotal += (float)hspdschC2i;
                hspdschC2iSample++;
                if (hspdschC2i >= -3)
                {
                    hspdschC2iF3Sample++;
                }
                if (hspdschC2i >= 5)
                {
                    hspdschC2i5Sample++;
                }
            }
        }

        private void mergeBler(TestPoint tp)
        {
            int? bler = (int?)tp["TD_BLER"];

            if (bler != null && bler >= 0 && bler <= 100)
            {
                blerTotal += (float)bler;
                blerSample++;
                if (bler >= 5)
                {
                    bler5Sample++;
                }
            }
        }

        private void mergeHspdschBler(TestPoint tp)
        {
            int? pdschBler = (int?)tp["TD_HSDPA_HS_PDSCH_TotalBLER"];

            if (pdschBler != null && pdschBler >= 0 && pdschBler <= 100)
            {
                hspdcshBlerTotal += (float)pdschBler;
                hspdcshBlerSample++;
                if (pdschBler >= 5)
                {
                    hspdcshBler5Sample++;
                }
            }
        }

        private void mergeCqi(TestPoint tp)
        {
            int? cqiMax = (int?)tp["TD_HSDPA_CQI_Max"];
            int? cqiMin = (int?)tp["TD_HSDPA_CQI_Min"];
            int? cqiAvg = (int?)tp["TD_HSDPA_CQI_Mean"];

            if (cqiMax != null && cqiMin != null && cqiAvg != null && cqiMax >= 0 && cqiMax <= 127 && cqiMin >= 0 && cqiMin <= 127 && cqiAvg >= 0 && cqiAvg <= 127)
            {
                hsCqiMaxTotal += (float)cqiMax;
                hsCqiMinTotal += (float)cqiMin;
                hsCqiAvgTotal += (float)cqiAvg;
                hsCqiSample++;
            }
        }

        private void mergeSchedule(TestPoint tp)
        {
            int? scheCount = (int?)tp["TD_HSDPA_HS_ScchScheduled_Count"];
            int? scheRate = (int?)tp["TD_HSDPA_HS_ScchScheduled_Rate"];

            if (scheCount != null && scheRate != null && scheCount >= 0 && scheRate >= 0)
            {
                hsscchScheduledCountTotal += (float)scheCount;
                hsscchScheduledRateTotal += (float)scheRate;
                hsscchScheduledRateSample++;
            }
        }

        private void mergeTimeSlot(TestPoint tp)
        {
            int? timeSlot = (int?)tp["TD_HSDPA_HS_PDSCH_TimeSlotUsed"];

            if (timeSlot != null && timeSlot > 0)
            {
                hspdschTimeSlotUsedTotal += (float)timeSlot;
                hspdschTimeSlotUsedSample++;
            }
        }

        private void mergeCodec(TestPoint tp)
        {
            float? qamRate = (float?)tp["TD_HSDPA_HS_16QAM_Rate"];
            float? qpskRate = (float?)tp["TD_HSDPA_HS_QPSK_Rate"];

            if (qamRate != null && qpskRate != null && qamRate >= 0 && qpskRate >= 0)
            {
                hs16QamRateTotal += (float)qamRate;
                hsQpskRateTotal += (float)qpskRate;
                hsCodecSample++;
            }
        }

        private void mergeTxPower(TestPoint tp)
        {
            int? txpower = (int?)tp["TD_TxPower"];

            if (txpower != null && txpower >= -50 && txpower <= 33)
            {
                txPowerTotal += (float)txpower;
                txPowerSample++;

                if (txpower >= 24)
                {
                    txPower24Sample++;
                }
            }
        }

        private void mergePollute(TestPoint tp)
        {
            if (checkPollution(tp))
            {
                pollutSample++;
            }
        }

        private bool checkPollution(TestPoint tp)
        {
            int count = 0;
            List<float> rscpList = new List<float>();

            //主服场强
            float? rscp = (float?)tp["TD_PCCPCH_RSCP"];
            if (rscp != null)
            {
                rscpList.Add((float)rscp);
            }
            else
            {
                return false;
            }

            //邻区场强
            try
            {
                for (int i = 0; i < 6; i++)
                {
                    int? nRscp = (int?)tp["TD_NCell_PCCPCH_RSCP", i];
                    if (nRscp == null)
                    {
                        break;
                    }
                    else
                    {
                        rscpList.Add((float)nRscp);
                    }
                }

                //排序后统计与最强信号6dB内的小区数量
                if (rscpList.Count > 0)
                {
                    rscpList.Sort();

                    int index = rscpList.Count;
                    float maxRscp = rscpList[index - 1];

                    for (int i = 0; i < index; i++)
                    {
                        if ((maxRscp - rscpList[i]) < 6)
                        {
                            count++;
                        }
                    }
                }
            }
            catch
            {
                //continue
            }
            return count >= 4;
        }
    }
}
