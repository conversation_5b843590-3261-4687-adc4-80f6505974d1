using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    public partial class Decide2ShowCQTImgForm : XtraForm
    {
        private MainModel mainModel;
        private List<CQTAddressItem> cqtItems;
        private Dictionary<int, string> cqtTypeIDDesc;
        public Decide2ShowCQTImgForm(MainModel mm, List<CQTAddressItem> cqtItems)
        {
            InitializeComponent();
            mainModel = mm;
            this.cqtItems = cqtItems;
            cqtTypeIDDesc = new Dictionary<int, string>();
            foreach (CQTTypeInfoItem typeInfo in mainModel.CQTTypeInfoList)
            {
                cqtTypeIDDesc.Add(typeInfo.areaTypeId, typeInfo.areaTypeName);
            }
            initData();
        }

        private void initData()
        {
            this.cmbxCqtItems.Items.Clear();
            foreach (CQTAddressItem cqt in cqtItems)
            {
                cmbxCqtItems.Items.Add(cqt);
            }
            if (cmbxCqtItems.Items.Count > 0)
            {
                cmbxCqtItems.SelectedIndex = 0;
            }
        }

        private CQTAddressItem curSelCqtItem = null;

        private void showCqtInfo()
        {
            if (curSelCqtItem == null)
                return;

            tbxDesc.Text = curSelCqtItem.desc;
            tbxCqtType.Text = cqtTypeIDDesc[curSelCqtItem.cqtTypeId];
            nudJD.Value = (decimal)curSelCqtItem.jd;
            nudWD.Value = (decimal)curSelCqtItem.wd;
            tbxImgCount.Text = curSelCqtItem.ImageCount.ToString();

            listViewImg.BeginUpdate();
            listViewImg.Items.Clear();
            imageList.Images.Clear();
            foreach (CQTAddrItemImage img in curSelCqtItem.Images)
            {
                imageList.Images.Add(img.Name, img.Img);
                ListViewItem item = new ListViewItem(img.Name, img.Name);
                listViewImg.Items.Add(item);
            }
            if (listViewImg.Items.Count > 0)
            {
                listViewImg.Items[0].Selected = true;
            }
            else
            {
                listViewImg_SelectedIndexChanged(null, null);
            }
            listViewImg.EndUpdate();
        }


        private void cmbxCqtItems_SelectedIndexChanged(object sender, EventArgs e)
        {
            curSelCqtItem = cmbxCqtItems.SelectedItem as CQTAddressItem;
            if (curSelCqtItem==null)
            {
                curSelImg = null;
            }
            showCqtInfo();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (curSelImg!=null)
            {
                double scale = curSelImg.Ratio = (double)nudRatio.Value;
                DbPoint ltPoint = new DbPoint(curSelCqtItem.jd, curSelCqtItem.wd);
                PointF displayLTPt;
                mainModel.MainForm.GetMapForm().GetMapOperation().ToDisplay(ltPoint, out displayLTPt);
                double ratio = scale / mainModel.MainForm.GetMapForm().GetMapOperation().Scale;
                int brX = (int)(displayLTPt.X + (ratio * curSelImg.Img.Width));
                int brY = (int)(displayLTPt.Y + (ratio * curSelImg.Img.Height));
                PointF realDisplayBRpt = new PointF(brX, brY);
                DbPoint brPt;
                mainModel.MainForm.GetMapForm().GetMapOperation().FromDisplay(realDisplayBRpt, out brPt);
                //double brLongtide = ltPoint.x + curSelImg.Img.Width * ratio;
                //double brLatitude = ltPoint.y - curSelImg.Img.Height * ratio;
                mainModel.CQTPlanIMGScale = scale;
                mainModel.CQTPlanImgLTPos = ltPoint;
                mainModel.CQTPlanImgBRPos = brPt;
                mainModel.CQTPlanImg = curSelImg.Img.Clone() as Image;
                mainModel.MainForm.GetMapForm().DtLayerChange();
            }

            if (checkEditBackFillTp.Checked)
            {
                arrangeCQTLongLat();
            }
            mainModel.MainForm.GetMapForm().GetDTLayer().Invalidate();
            this.DialogResult = DialogResult.OK;
        }

        private void arrangeCQTLongLat()
        {
            double CenterLong = curSelCqtItem.jd;
            double CenterLati = curSelCqtItem.wd;
            if (mainModel.DTDataManager.FileDataManagers.Count > 0)
            {
                DTFileDataManager fileMngr = mainModel.DTDataManager.FileDataManagers[0];
                int MaxLongCount = 100;
                int MaxLatiCount = 10;
                double step_long = 0.0002d;
                double step_lati = 0.0002d;
                CQTData cqt = new CQTData(CenterLong, CenterLati);
                fileMngr.Sort();
                List<DTData> dtList = fileMngr.DTDatas;
                foreach (DTData data in dtList)
                {
                    if (data is TestPoint)
                    {
                        dealTP(MaxLongCount, MaxLatiCount, step_long, step_lati, data, cqt);
                    }
                    else if (data is Event)
                    {
                        dealEvt(MaxLongCount, MaxLatiCount, step_long, step_lati, data, cqt);
                    }
                }
            }
        }

        class CQTData
        {
            public CQTData(double curLong, double curLati)
            {
                CurLong = curLong;
                CurLati = curLati;
            }

            public double CurLong { get;set;}
            public double CurLati { get; set; }
            public double DisCounter { get; set; } = 0;
            public double NextStepDir { get; set; } = 1;
        }

        private void dealTP(int MaxLongCount, int MaxLatiCount, double step_long, double step_lati, DTData data, CQTData cqt)
        {
            TestPoint tp = data as TestPoint;
            if (cqt.NextStepDir == 1)
            {
                tp.Longitude = cqt.CurLong;
                tp.Latitude = cqt.CurLati;
                cqt.CurLong += step_long;
                cqt.DisCounter++;
                if (cqt.DisCounter > MaxLongCount)
                {
                    cqt.DisCounter = 0;
                    cqt.NextStepDir = 2;
                }
            }
            else if (cqt.NextStepDir == 2)
            {
                tp.Longitude = cqt.CurLong;
                tp.Latitude = cqt.CurLati;
                cqt.CurLati -= step_lati;
                cqt.DisCounter++;
                if (cqt.DisCounter > MaxLatiCount)
                {
                    cqt.DisCounter = 0;
                    cqt.NextStepDir = 3;
                }
            }
            else if (cqt.NextStepDir == 3)
            {
                tp.Longitude = cqt.CurLong;
                tp.Latitude = cqt.CurLati;
                cqt.CurLong -= step_long;
                cqt.DisCounter++;
                if (cqt.DisCounter > MaxLongCount)
                {
                    cqt.DisCounter = 0;
                    cqt.NextStepDir = 4;
                }
            }
            else if (cqt.NextStepDir == 4)
            {
                tp.Longitude = cqt.CurLong;
                tp.Latitude = cqt.CurLati;
                cqt.CurLati -= step_lati;
                cqt.DisCounter++;
                if (cqt.DisCounter > MaxLatiCount)
                {
                    cqt.DisCounter = 0;
                    cqt.NextStepDir = 1;
                }
            }
        }

        private void dealEvt(int MaxLongCount, int MaxLatiCount, double step_long, double step_lati, DTData data, CQTData cqt)
        {
            Event evt = data as Event;
            if (cqt.NextStepDir == 1)
            {
                evt.Longitude = cqt.CurLong;
                evt.Latitude = cqt.CurLati;
                cqt.CurLong += step_long;
                cqt.DisCounter++;
                if (cqt.DisCounter > MaxLongCount)
                {
                    cqt.DisCounter = 0;
                    cqt.NextStepDir = 2;
                }
            }
            else if (cqt.NextStepDir == 2)
            {
                evt.Longitude = cqt.CurLong;
                evt.Latitude = cqt.CurLati;
                cqt.CurLati -= step_lati;
                cqt.DisCounter++;
                if (cqt.DisCounter > MaxLatiCount)
                {
                    cqt.DisCounter = 0;
                    cqt.NextStepDir = 3;
                }
            }
            else if (cqt.NextStepDir == 3)
            {
                evt.Longitude = cqt.CurLong;
                evt.Latitude = cqt.CurLati;
                cqt.CurLong -= step_long;
                cqt.DisCounter++;
                if (cqt.DisCounter > MaxLongCount)
                {
                    cqt.DisCounter = 0;
                    cqt.NextStepDir = 4;
                }
            }
            else if (cqt.NextStepDir == 4)
            {
                evt.Longitude = cqt.CurLong;
                evt.Latitude = cqt.CurLati;
                cqt.CurLati -= step_lati;
                cqt.DisCounter++;
                if (cqt.DisCounter > MaxLatiCount)
                {
                    cqt.DisCounter = 0;
                    cqt.NextStepDir = 1;
                }
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private CQTAddrItemImage curSelImg = null;
        private void listViewImg_SelectedIndexChanged(object sender, EventArgs e)
        {
            bool isSel = listViewImg.SelectedItems.Count > 0;
            if (isSel)
            {
                curSelImg = curSelCqtItem.Images[listViewImg.SelectedIndices[0]];
            }
            else
            {
                curSelImg = null;
            }
            //btnOK.Enabled = isSel;
            showImgInfo();
        }


        private void showImgInfo()
        {
            if (curSelImg != null)
            {
                tbxImgName.Text = curSelImg.Name;
                tbxImgDesc.Text = curSelImg.Desc;
                nudRatio.Value = (decimal)curSelImg.Ratio;
            }
        }


    }
}