﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 高重叠覆盖道路
    /// </summary>
    public class ZTLTEScanHighCoverateRoadQueryByRegion : DIYAnalyseByFileBackgroundBase
    {
        private List<LTEScanHighCoverageRoadInfo> relRoadCoverageList = null;
        private List<LTEScanHighCoverageRoadInfo> absRoadCoverageList = null;
        private LTEScanHighCoverageRoadCondition condHighCover = new LTEScanHighCoverageRoadCondition();
        private LTEScanHighCoverageCellChecker cellChecker = null;
        private readonly Dictionary<LTECell, LTEScanHighCoverageCellType> relProblemCellDic = new Dictionary<LTECell, LTEScanHighCoverageCellType>();
        private readonly Dictionary<LTECell, LTEScanHighCoverageCellType> absProblemCellDic = new Dictionary<LTECell, LTEScanHighCoverageCellType>();
        private ZTLTEHighCoverageRoadSetForm setForm;

        private static ZTLTEScanHighCoverateRoadQueryByRegion intance = null;
        protected static readonly object LockObj = new object();
        public static ZTLTEScanHighCoverateRoadQueryByRegion GetInstance()
        {
            if (intance == null)
            {
                lock (LockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTLTEScanHighCoverateRoadQueryByRegion();
                    }
                }
            }
            return intance;
        }

        protected ZTLTEScanHighCoverateRoadQueryByRegion()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_SCAN_TOPN);
            carrierID = CarrierType.ChinaMobile;
        }

        private readonly ServiceName serviceName = ServiceName.LTE;
        public ZTLTEScanHighCoverateRoadQueryByRegion(ServiceName serviceName)
            : this()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
            carrierID = CarrierType.ChinaMobile;
            this.serviceName = serviceName;
        }

        public override string Name
        {
            get { return "高重叠覆盖路段_LTE"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 23000, 23024, this.Name);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                initData();
                return true;
            }
            if (showFuncCondSetDlg())
            {
                initData();
                return true;
            }
            return false;
        }
        protected void initData()
        {
            relRoadCoverageList = new List<LTEScanHighCoverageRoadInfo>();
            absRoadCoverageList = new List<LTEScanHighCoverageRoadInfo>();
            cellChecker = new LTEScanHighCoverageCellChecker(condHighCover);
            relProblemCellDic.Clear();
            absProblemCellDic.Clear();
            setRoadCond();
        }
        protected bool showFuncCondSetDlg()
        {
            if (setForm == null || setForm.IsDisposed)
            {
                setForm = new ZTLTEHighCoverageRoadSetForm(serviceName);
            }
            setForm.SetCondition(condHighCover);
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                condHighCover = setForm.GetCondition();
                return true;
            }
            return false;
        }

        protected PercentRoadBuilder roadBuilder_Rel;
        protected PercentRoadBuilder roadBuilder_Abs;

        public void setRoadCond()
        {
            PercentRoadCondition roadCond = new PercentRoadCondition(condHighCover.RoadMinPercent / 100, OnOneRoadComplete);
            roadCond.IsCheckDuration = false;
            roadCond.IsCheckMinLength = true;
            roadCond.MinLength = condHighCover.RoadDistance;
            roadCond.IsCheckDistanceGap = true;
            roadCond.MaxDistanceGap = condHighCover.SampleDistance;

            this.roadBuilder_Rel = new PercentRoadBuilder(roadCond);
            this.roadBuilder_Abs = new PercentRoadBuilder(roadCond);
        }

        private double curPercent = 0;
        public List<TestPoint> tps { get; set; } = new List<TestPoint>();

        protected void OnOneRoadComplete(object sender, PercentRoadItem roadItem)
        {
            curPercent = Math.Round(roadItem.ValidPercent * 100, 2);
            if (curPercent < this.condHighCover.RoadMinPercent)
            {
                return;
            }
            tps = roadItem.TestPoints;
            if (roadItem.NetType == TypeString.Abs)
            {
                addToReportInfo(tps, true);
            }
            else
            {
                addToReportInfo(tps, false);
            }
        }
        public struct TypeString
        {
            public static string Rel { get; set; } = "Rel";
            public static string Abs { get; set; } = "Abs";
        }

        public virtual void addToReportInfo(List<TestPoint> testPointList, bool isAbs)
        {
            if (testPointList.Count == 0)
            {
                return;
            }
            double tmpLongitude = testPointList[0].Longitude;
            double tmpLatitude = testPointList[0].Latitude;

            LTEScanHighCoverageRoadInfo relInfo = new LTEScanHighCoverageRoadInfo(TypeString.Rel);
            LTEScanHighCoverageRoadInfo absInfo = new LTEScanHighCoverageRoadInfo(TypeString.Abs);

            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];

                double distance = MathFuncs.GetDistance(testPoint.Longitude, testPoint.Latitude, tmpLongitude, tmpLatitude);

                doWithDTData(ref relInfo, ref absInfo, testPoint, distance, isAbs);

                tmpLongitude = testPointList[i].Longitude;
                tmpLatitude = testPointList[i].Latitude;
            }

            if (isAbs)
            {
                saveResult(absRoadCoverageList, absProblemCellDic, absInfo);
            }
            else
            {
                saveResult(relRoadCoverageList, relProblemCellDic, relInfo);
            }
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    tps = new List<TestPoint>();

                    foreach (TestPoint tp in fileDataManager.TestPoints)
                    {
                        if (condHighCover.IsRelativeCheck)
                        {
                            roadBuilder_Rel.AddPoint(tp, isValidTestPoint(tp, false), TypeString.Rel);
                        }
                        if (condHighCover.IsAbsCheck)
                        {
                            roadBuilder_Abs.AddPoint(tp, isValidTestPoint(tp, true), TypeString.Abs);
                        }
                    }
                    this.roadBuilder_Rel.StopRoading();
                    this.roadBuilder_Abs.StopRoading();
                }
            }
            catch (Exception ee)
            {
                showException(ee);
            }
        }

        protected bool isValidTestPoint(TestPoint tp, bool isAbs)
        {
            bool ret = false;
            try
            {
                bool ignore = isIgnoreTestPoint(tp, isAbs);
                if (!ignore)
                {
                    ret = condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
                }
            }
            catch
            {
                //continue
            }
            return ret;
        }

        public bool isIgnoreTestPoint(TestPoint testPoint, bool isAbs)
        {
            float? maxRscp = null;

            List<LTEScanHighCoverageCellInfo> relCellList = new List<LTEScanHighCoverageCellInfo>();
            List<LTEScanHighCoverageCellInfo> absCellList = new List<LTEScanHighCoverageCellInfo>();

            for (int index = 0; index <= 50; index++)//扫频数据，入库时已按rxlev从大到小排序，无需再排序
            {
                bool needDeal = judgeNeedDeal(testPoint, index);

                if (needDeal)
                {
                    float? rscp = null;
                    bool isValidMaxRscp = setRsrp(testPoint, ref rscp, ref maxRscp, index);
                    if (!isValidMaxRscp)
                    {
                        return true;
                    }

                    bool isValid = dealTestPoint(testPoint, isAbs, maxRscp, relCellList, absCellList, index, rscp);
                    if (!isValid)
                    {
                        break;
                    }
                }
            }

            if (!isAbs && relCellList.Count < condHighCover.RelCoverate)  //不符合相对覆盖带要求
            {
                return true;
            }
            if (isAbs && absCellList.Count < condHighCover.AbsCoverate)   //不符合绝对覆盖带要求
            {
                return true;
            }
            return false;
        }

        private bool dealTestPoint(TestPoint testPoint, bool isAbs, float? maxRscp, 
            List<LTEScanHighCoverageCellInfo> relCellList, List<LTEScanHighCoverageCellInfo> absCellList, int index, float? rscp)
        {
            LTEScanHighCoverageCellInfo cellInfo = new LTEScanHighCoverageCellInfo(testPoint, index);
            //检查模拟闭站
            bool closeSimu = cellChecker.JudgeCloseSimu(cellInfo.LteCell);
            if (!closeSimu)
            {
                bool isValid = addValidCellInfo(testPoint, isAbs, maxRscp, relCellList, absCellList, rscp, cellInfo);
                if (!isValid)
                {
                    return false;
                }
            }
            return true;
        }

        private bool judgeNeedDeal(TestPoint testPoint, int index)
        {
            bool needDeal = true;
            if (condHighCover.IsFreqBand)//只分析某一频段
            {
                int? earfcnTP = testPoint["LTESCAN_TopN_EARFCN", index] as int?;
                if (earfcnTP != null)
                {
                    int earfcn = (int)earfcnTP;
                    needDeal = FreqPoint.ValidCellBandType(condHighCover.ListFreqPoint,earfcn);
                }
            }
            return needDeal;
        }

        private bool setRsrp(TestPoint testPoint, ref float? rscp, ref float? maxRscp, int index)
        {
            rscp = (float?)testPoint["LTESCAN_TopN_CELL_Specific_RSRP", index];
            if (maxRscp == null)
            {
                maxRscp = rscp;
                if (maxRscp == null || maxRscp <= condHighCover.RxLevMin)
                {
                    return false;
                }
            }

            return true;
        }

        private bool addValidCellInfo(TestPoint testPoint, bool isAbs, float? maxRscp, List<LTEScanHighCoverageCellInfo> relCellList, List<LTEScanHighCoverageCellInfo> absCellList, float? rscp, LTEScanHighCoverageCellInfo cellInfo)
        {
            if (condHighCover.IsShieldProblem)
            {
                cellInfo.CellType = cellChecker.JudgeCellType(testPoint, cellInfo.LteCell);
                if (cellInfo.CellType != LTEScanHighCoverageCellType.FarCover && cellInfo.CellType != LTEScanHighCoverageCellType.LeakOut)
                {
                    bool isAdded = addCellList(isAbs, maxRscp, relCellList, absCellList, rscp, cellInfo);
                    if (!isAdded)
                    {
                        return false;
                    }
                }
            }
            else
            {
                bool isAdded = addCellList(isAbs, maxRscp, relCellList, absCellList, rscp, cellInfo);
                if (!isAdded)
                {
                    return false;
                }
            }
            return true;
        }

        private bool addCellList(bool isAbs, float? maxRscp, List<LTEScanHighCoverageCellInfo> relCellList, 
            List<LTEScanHighCoverageCellInfo> absCellList, float? rscp, LTEScanHighCoverageCellInfo cellInfo)
        {
            if (rscp == null)    //无效值
            {
                return false;
            }

            if (!isAbs && condHighCover.IsRelativeCheck && (maxRscp - rscp) < condHighCover.RxLevMaxDiff)//相对覆盖带
            {
                relCellList.Add(cellInfo);
            }

            if (isAbs && condHighCover.IsAbsCheck && rscp > condHighCover.AbsValue)//绝对覆盖带
            {
                absCellList.Add(cellInfo);
            }
            return true;
        }

        private void prevDealTwoEarfcn(TestPoint testPoint, out List<LTEScanHighCoverageCellInfo> lstHightCoverCells)
        {
            lstHightCoverCells = new List<LTEScanHighCoverageCellInfo>();
            bool invalid = false;
            LTEScanHighCoverageCellInfo maxCell = null;
            for (int index = 0; index < 50; index++) //扫频数据，入库时已按rxlev从大到小排序，无需再排序
            {
                LTEScanHighCoverageCellInfo hcCell = new LTEScanHighCoverageCellInfo(testPoint, index);

                if (!hcCell.IsValid) continue;

                if (maxCell == null)
                {
                    maxCell = hcCell;
                    invalid = maxCell.Rssi < condHighCover.RxLevMin;
                    if (invalid)
                    {
                        break;
                    }
                }

                addLstHightCoverCells(testPoint, lstHightCoverCells, maxCell, hcCell);
            }
        }

        private void addLstHightCoverCells(TestPoint testPoint, List<LTEScanHighCoverageCellInfo> lstHightCoverCells, LTEScanHighCoverageCellInfo maxCell, LTEScanHighCoverageCellInfo hcCell)
        {
            DualBand dualBand = DualBandGetter.GetDualBand(hcCell.LteCell, testPoint.DateTime);
            if (dualBand == null || dualBand.EDualBandType == EDualBand.Normal)
            {
                lstHightCoverCells.Add(hcCell);
            }
            else if (dualBand.EDualBandType == EDualBand.DualBand)
            {
                bool equal = hcCell.EARFCN == maxCell.EARFCN;
                if (equal)
                {
                    lstHightCoverCells.Add(hcCell);
                }
            }
            else if (dualBand.EDualBandType == EDualBand.MultiBand)
            {
                bool equal = LTECell.GetBandTypeByEarfcn(hcCell.EARFCN) == LTECell.GetBandTypeByEarfcn(maxCell.EARFCN);
                if (equal)
                {
                    lstHightCoverCells.Add(hcCell);
                }
            }
        }

        private void prevDealBandType(TestPoint testPoint, out List<LTEScanHighCoverageCellInfo> lstHightCoverCells)
        {
            lstHightCoverCells = new List<LTEScanHighCoverageCellInfo>();
            bool invalid = false;
            LTEScanHighCoverageCellInfo maxCell = null;
            for (int index = 0; index < 50; index++) //扫频数据，入库时已按rxlev从大到小排序，无需再排序
            {
                LTEScanHighCoverageCellInfo hcCell = new LTEScanHighCoverageCellInfo(testPoint, index);

                if (!hcCell.IsValid) continue;

                if (maxCell == null)
                {
                    maxCell = hcCell;
                    invalid = maxCell.Rssi < condHighCover.RxLevMin;
                    if (invalid)
                    {
                        break;
                    }
                }
                int earfcn = hcCell.EARFCN;
                if (condHighCover.IsFreqBand)
                {
                    if (FreqPoint.ValidCellBandType(condHighCover.ListFreqPoint,earfcn))
                    {
                        lstHightCoverCells.Add(hcCell);
                    }
                }
                else
                {
                    lstHightCoverCells.Add(hcCell);
                }
            }
        }
        private void doWithDTData(ref LTEScanHighCoverageRoadInfo relInfo, ref LTEScanHighCoverageRoadInfo absInfo
            , TestPoint testPoint, double distance, bool isAbs)
        {
            float? maxRscp = null;

            List<LTEScanHighCoverageCellInfo> relCellList = new List<LTEScanHighCoverageCellInfo>();
            List<LTEScanHighCoverageCellInfo> absCellList = new List<LTEScanHighCoverageCellInfo>();

            List<LTEScanHighCoverageCellInfo> lstHightCoverCells;
            if (condHighCover.IsTwoEarfcn)
            {
                prevDealTwoEarfcn(testPoint, out lstHightCoverCells);
            }
            else
            {
                prevDealBandType(testPoint, out lstHightCoverCells);
            }

            getCellList(testPoint, isAbs, ref maxRscp, relCellList, absCellList, lstHightCoverCells);

            if (maxRscp == null)
                return;

            if (!isAbs && relCellList.Count < condHighCover.RelCoverate)  //不符合相对覆盖带要求
            {
                return;
            }
            if (isAbs && absCellList.Count < condHighCover.AbsCoverate)   //不符合绝对覆盖带要求
            {
                return;
            }

            if (isAbs)
            {
                absInfo.Distance += distance;
                absInfo.SetRssi((float)maxRscp);
                LTEScanHighCoveragePointInfo absPointInfo = new LTEScanHighCoveragePointInfo(absInfo.SampleLst.Count + 1, testPoint, absCellList);
                absInfo.SampleLst.Add(absPointInfo);
            }
            else
            {
                relInfo.Distance += distance;
                relInfo.SetRssi((float)maxRscp);
                LTEScanHighCoveragePointInfo relPointInfo = new LTEScanHighCoveragePointInfo(relInfo.SampleLst.Count + 1, testPoint, relCellList);
                relInfo.SampleLst.Add(relPointInfo);
            }
        }

        private void getCellList(TestPoint testPoint, bool isAbs, ref float? maxRscp, List<LTEScanHighCoverageCellInfo> relCellList, List<LTEScanHighCoverageCellInfo> absCellList, List<LTEScanHighCoverageCellInfo> lstHightCoverCells)
        {
            foreach (LTEScanHighCoverageCellInfo cellInfo in lstHightCoverCells)
            {
                if (maxRscp == null)
                    maxRscp = cellInfo.Rssi;

                if (!cellChecker.JudgeCloseSimu(cellInfo.LteCell))
                {
                    dealCellInfo(testPoint, isAbs, maxRscp, relCellList, absCellList, cellInfo);
                }
            }
        }

        private void dealCellInfo(TestPoint testPoint, bool isAbs, float? maxRscp, List<LTEScanHighCoverageCellInfo> relCellList, List<LTEScanHighCoverageCellInfo> absCellList, LTEScanHighCoverageCellInfo cellInfo)
        {
            if (condHighCover.IsShieldProblem)
            {
                cellInfo.CellType = cellChecker.JudgeCellType(testPoint, cellInfo.LteCell);
                cellInfo.CellTypeDesc = LTEScanHighCoverageCellChecker.GetCellTypeDesc(cellInfo.CellType);
                if (cellInfo.CellType == LTEScanHighCoverageCellType.FarCover || cellInfo.CellType == LTEScanHighCoverageCellType.LeakOut)
                {
                    if (!relProblemCellDic.ContainsKey(cellInfo.LteCell))
                    {
                        relProblemCellDic.Add(cellInfo.LteCell, cellInfo.CellType);
                    }
                }
                else
                {
                    addCellList(isAbs, maxRscp, relCellList, absCellList, cellInfo);
                }
            }
            else
            {
                addCellList(isAbs, maxRscp, relCellList, absCellList, cellInfo);
            }
        }

        private void addCellList(bool isAbs, float? maxRscp, List<LTEScanHighCoverageCellInfo> relCellList, List<LTEScanHighCoverageCellInfo> absCellList, LTEScanHighCoverageCellInfo cellInfo)
        {
            if (!isAbs && ((maxRscp - cellInfo.Rssi) < condHighCover.RxLevMaxDiff)) //超出覆盖带
            {
                relCellList.Add(cellInfo);
                cellInfo.SN = relCellList.Count;
            }

            if (isAbs && cellInfo.Rssi > condHighCover.AbsValue)
            {
                absCellList.Add(cellInfo);
                cellInfo.SN = absCellList.Count;
            }
        }

        private void saveResult(List<LTEScanHighCoverageRoadInfo> roadCoverageList
            , Dictionary<LTECell, LTEScanHighCoverageCellType> problemCellDic, LTEScanHighCoverageRoadInfo info)
        {
            if (info.Distance >= condHighCover.RoadDistance)  //超过距离门限
            {
                if (info.SampleLst.Count > 1)
                {
                    info.Percent = curPercent;
                    info.SN = roadCoverageList.Count + 1;
                    roadCoverageList.Add(info);
                }

                if (!condHighCover.IsShieldProblem)
                {
                    addProblemCellDic(problemCellDic, info);
                }
            }
        }

        private void addProblemCellDic(Dictionary<LTECell, LTEScanHighCoverageCellType> problemCellDic, LTEScanHighCoverageRoadInfo info)
        {
            foreach (LTEScanHighCoveragePointInfo ptInfo in info.SampleLst)
            {
                foreach (LTEScanHighCoverageCellInfo cellInfo in ptInfo.CellList)
                {
                    cellInfo.CellType = cellChecker.JudgeCellType(ptInfo.Tp, cellInfo.LteCell);
                    cellInfo.CellTypeDesc = LTEScanHighCoverageCellChecker.GetCellTypeDesc(cellInfo.CellType);
                    if (cellInfo.LteCell != null && !problemCellDic.ContainsKey(cellInfo.LteCell))
                    {
                        problemCellDic.Add(cellInfo.LteCell, cellInfo.CellType);
                    }
                }
            }
        }

        protected override void getResultsAfterQuery()
        {
            if (condHighCover.IsRelativeCheck)
            {
                foreach (LTEScanHighCoverageRoadInfo info in relRoadCoverageList)
                {
                    info.GetResult();
                    foreach (LTEScanHighCoveragePointInfo pointInfo in info.SampleLst)
                    {
                        MainModel.DTDataManager.Add(pointInfo.Tp);
                    }
                }
            }

            if (condHighCover.IsAbsCheck)
            {
                foreach (LTEScanHighCoverageRoadInfo info in absRoadCoverageList)
                {
                    info.GetResult();
                    foreach (LTEScanHighCoveragePointInfo pointInfo in info.SampleLst)
                    {
                        MainModel.DTDataManager.Add(pointInfo.Tp);
                    }
                }
            }
            MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
        }

        protected override void fireShowForm()
        {
            ZTLTESanHighCoverateRoadListForm form = MainModel.CreateResultForm(typeof(ZTLTESanHighCoverateRoadListForm)) as ZTLTESanHighCoverateRoadListForm;
            form.FillData(new List<LTEScanHighCoverageRoadInfo>(relRoadCoverageList), new Dictionary<LTECell, LTEScanHighCoverageCellType>(relProblemCellDic),
                            new List<LTEScanHighCoverageRoadInfo>(absRoadCoverageList), new Dictionary<LTECell, LTEScanHighCoverageCellType>(absProblemCellDic), condHighCover);
            form.Owner = MainModel.MainForm;
            form.Visible = true;
            form.BringToFront();

            relRoadCoverageList = null;
            absRoadCoverageList = null;
            cellChecker = null;
            relProblemCellDic.Clear();
            absProblemCellDic.Clear();
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE扫频专题; }
        }
        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                ignoreParamKeys.Add("FuncCond");

                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["FuncCond"] = condHighCover.Param;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("FuncCond"))
                {
                    if (condHighCover == null)
                    {
                        condHighCover = new LTEScanHighCoverageRoadCondition();
                    }
                    condHighCover.Param = param["FuncCond"] as Dictionary<string, object>;
                }
            }
        }
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Road; }
        }
        public override PropertiesControl Properties
        {
            get
            {
                return new CommonSimpleProperties(this, showFuncCondSetDlg);
            }
        }
        protected override void saveBackgroundData()
        {
            int subFuncId = this.GetSubFuncID();
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            if (condHighCover.IsRelativeCheck)
            {
                bgResultList.AddRange(getBgResultsByRoadInfo(relRoadCoverageList, subFuncId));
            }

            if (condHighCover.IsAbsCheck)
            {
                bgResultList.AddRange(getBgResultsByRoadInfo(absRoadCoverageList, subFuncId));
            }

            BackgroundFuncQueryManager.GetInstance().SaveResult_Road(GetSubFuncID(), curAnaFileInfo, bgResultList);
            initData();
        }
        private List<BackgroundResult> getBgResultsByRoadInfo(List<LTEScanHighCoverageRoadInfo> roadCoverageList
            , int subFuncId)
        {
            List<BackgroundResult> resultList = new List<BackgroundResult>();
            foreach (LTEScanHighCoverageRoadInfo roadInfo in roadCoverageList)
            {
                roadInfo.GetResult();
                resultList.Add(roadInfo.ConverToBackgroundResult(subFuncId));
            }
            return resultList;
        }

        protected override void initBackgroundImageDesc()
        {
            BackgroundResultList.Sort(BackgroundResult.ComparerByFileIdAndISTimeAsc);
            List<NPOIRow> resultList_Rel = new List<NPOIRow>();
            List<NPOIRow> resultList_Abs = new List<NPOIRow>();

            int lastRoadSn_Rel = 0;
            int lastRoadSn_Abs = 0;
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                if (condHighCover.IsRelativeCheck && bgResult.StrDesc == TypeString.Rel)
                {
                    resultList_Rel.Add(getNPOIRowByBgResult(bgResult, ref lastRoadSn_Rel));
                }
                if (condHighCover.IsAbsCheck && bgResult.StrDesc == TypeString.Abs)
                {
                    resultList_Abs.Add(getNPOIRowByBgResult(bgResult, ref lastRoadSn_Abs));
                }
            }

            this.BackgroundNPOIRowResultDic.Clear();
            NPOIRow title = new NPOIRow();
            title.AddCellValue("序号");
            title.AddCellValue("道路名称");
            title.AddCellValue("距离(米)");
            title.AddCellValue("高重叠覆盖点占比(%)");
            title.AddCellValue("采样点数");
            title.AddCellValue("第一强最大值");
            title.AddCellValue("第一强最小值");
            title.AddCellValue("第一强平均值");
            title.AddCellValue("经度");
            title.AddCellValue("纬度");
            title.AddCellValue("文件名");
            title.AddCellValue("开始时间");
            title.AddCellValue("结束时间");
            if (condHighCover.IsRelativeCheck)
            {
                resultList_Rel.Insert(0, title);
                this.BackgroundNPOIRowResultDic["相对覆盖带"] = resultList_Rel;
            }
            if (condHighCover.IsAbsCheck)
            {
                resultList_Abs.Insert(0, title);
                this.BackgroundNPOIRowResultDic["绝对覆盖带"] = resultList_Abs;
            }
        }
        private NPOIRow getNPOIRowByBgResult(BackgroundResult bgResult, ref int lastRoadSn)
        {
            float coverRate = bgResult.GetImageValueFloat();
            string beginTimeStr = bgResult.GetImageValueString();
            string endTimeStr = bgResult.GetImageValueString();

            NPOIRow row = new NPOIRow();
            row.AddCellValue(++lastRoadSn);
            row.AddCellValue(bgResult.RoadDesc);
            row.AddCellValue(bgResult.DistanceLast);
            row.AddCellValue(coverRate);
            row.AddCellValue(bgResult.SampleCount);
            row.AddCellValue(bgResult.RxLevMax);
            row.AddCellValue(bgResult.RxLevMin);
            row.AddCellValue(bgResult.RxLevMean);
            row.AddCellValue(bgResult.LongitudeMid);
            row.AddCellValue(bgResult.LatitudeMid);
            row.AddCellValue(bgResult.FileName);
            row.AddCellValue(beginTimeStr);
            row.AddCellValue(endTimeStr);
            return row;
        }
        #endregion
    }
}
