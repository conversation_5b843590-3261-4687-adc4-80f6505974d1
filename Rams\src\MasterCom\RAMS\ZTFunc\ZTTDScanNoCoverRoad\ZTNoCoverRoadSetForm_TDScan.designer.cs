﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTNoCoverRoadSetForm_TDScan
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.numPccpchRscpThreshold = new System.Windows.Forms.NumericUpDown();
            this.numDistance = new System.Windows.Forms.NumericUpDown();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.checkBoxMerge = new System.Windows.Forms.CheckBox();
            this.numMergeDistance = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.numMaxDistance = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numPccpchRscpThreshold)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMergeDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxDistance)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(51, 16);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(59, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "最强信号<";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(51, 45);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(59, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "持续距离>";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(203, 16);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(23, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "dBm";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(205, 45);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "米";
            // 
            // numPccpchRscpThreshold
            // 
            this.numPccpchRscpThreshold.Location = new System.Drawing.Point(113, 12);
            this.numPccpchRscpThreshold.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numPccpchRscpThreshold.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numPccpchRscpThreshold.Name = "numPccpchRscpThreshold";
            this.numPccpchRscpThreshold.Size = new System.Drawing.Size(82, 21);
            this.numPccpchRscpThreshold.TabIndex = 2;
            this.numPccpchRscpThreshold.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numPccpchRscpThreshold.Value = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            // 
            // numDistance
            // 
            this.numDistance.Location = new System.Drawing.Point(114, 41);
            this.numDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numDistance.Name = "numDistance";
            this.numDistance.Size = new System.Drawing.Size(81, 21);
            this.numDistance.TabIndex = 2;
            this.numDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDistance.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(88, 141);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(179, 141);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 3;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // checkBoxMerge
            // 
            this.checkBoxMerge.AutoSize = true;
            this.checkBoxMerge.Checked = true;
            this.checkBoxMerge.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBoxMerge.Location = new System.Drawing.Point(39, 103);
            this.checkBoxMerge.Name = "checkBoxMerge";
            this.checkBoxMerge.Size = new System.Drawing.Size(72, 16);
            this.checkBoxMerge.TabIndex = 4;
            this.checkBoxMerge.Text = "是否合并";
            this.checkBoxMerge.UseVisualStyleBackColor = true;
            this.checkBoxMerge.CheckStateChanged += new System.EventHandler(this.checkBoxMerge_CheckStateChanged);
            // 
            // numMergeDistance
            // 
            this.numMergeDistance.Location = new System.Drawing.Point(114, 99);
            this.numMergeDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMergeDistance.Name = "numMergeDistance";
            this.numMergeDistance.Size = new System.Drawing.Size(81, 21);
            this.numMergeDistance.TabIndex = 2;
            this.numMergeDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMergeDistance.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(201, 103);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(53, 12);
            this.label5.TabIndex = 5;
            this.label5.Text = "米内合并";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(9, 75);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(101, 12);
            this.label6.TabIndex = 6;
            this.label6.Text = "相邻采样点距离≤";
            // 
            // numMaxDistance
            // 
            this.numMaxDistance.Location = new System.Drawing.Point(114, 70);
            this.numMaxDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMaxDistance.Name = "numMaxDistance";
            this.numMaxDistance.Size = new System.Drawing.Size(81, 21);
            this.numMaxDistance.TabIndex = 2;
            this.numMaxDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMaxDistance.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(203, 75);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 7;
            this.label7.Text = "米";
            // 
            // ScanTDPccpchRscpNoneCoverConditionDlg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(279, 185);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.checkBoxMerge);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.numMaxDistance);
            this.Controls.Add(this.numMergeDistance);
            this.Controls.Add(this.numDistance);
            this.Controls.Add(this.numPccpchRscpThreshold);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ScanTDPccpchRscpNoneCoverConditionDlg";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "弱/无覆盖路段分析条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numPccpchRscpThreshold)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMergeDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxDistance)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numPccpchRscpThreshold;
        private System.Windows.Forms.NumericUpDown numDistance;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.CheckBox checkBoxMerge;
        private System.Windows.Forms.NumericUpDown numMergeDistance;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown numMaxDistance;
        private System.Windows.Forms.Label label7;
    }
}